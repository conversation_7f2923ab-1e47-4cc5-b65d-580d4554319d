#!/bin/bash

export PATH=/home/<USER>/php7/bin:$PATH

# Halt on errors
set -e

# Be verbose
set -x

function record() {
        wget https://git.xiaojukeji.com/gulfstream/boss-hook/raw/master/biz-recorder.tar.gz &&
        tar zxvf biz-recorder.tar.gz &&
        rm -rf biz-recorder.tar.gz
}

function compose() {
    (
        composer clearcache && \
 	    composer install -o --no-dev && echo -e "===== composer install ok =====" && \
 	    composer dump-autoload -o && echo -e "===== composer dump-autoload ok ====="
 	) || ( echo -e "===== composer install failure =====" &&  exit 2)
}

function make_output() {
    #创建临时目录
    local output="/tmp/pre-sale/output"
    rm -rf $output &>/dev/null
    mkdir -p $output &>/dev/null
    #填充output目录, output的内容即为待部署内容
    (
        rm -rf "./output" &&
        cp -rf ./* $output &&
        find $output -name ".git" | xargs rm -rf &&
        mv $output ./ && echo -e "make output ok"
    ) || { echo -e "make output error"; exit 2; }
}


# 安装性能分析组件 only centos7
function install_profiling() {
    wget https://git.xiaojukeji.com/nuwa/php/nuwa-profiling-collector/-/archive/master/nuwa-profiling-collector-master.tar.gz &&
    tar zxvf nuwa-profiling-collector-master.tar.gz &&
    rm -rf nuwa-profiling-collector-master.tar.gz &&
    mv nuwa-profiling-collector-master ./output/nuwa-profiling-collector
}

# 接入代码覆盖率分析
function install_coverage(){
    <NAME_EMAIL>:possible_engine_qa/code_coverage.git
    echo "install_coverage"
    if [ $? == 0 ]; then
        mv ./code_coverage ./output/
        ls ./output
    else
        return
    fi
}

# 安装日志脱敏 php 扩展
function install_log_masking() {
    local tempDir="/tmp/nuwa-php-masking"
    rm -rf $tempDir &>/dev/null
    mkdir -p $tempDir &>/dev/null
    local pkg_addr="http://artifactory-new.intra.xiaojukeji.com/artifactory/product-dev-local/nuwa/php/php-masking"
    local pkg_name="/20250612103522_f83ffccd/nuwa_php_php-masking_parameter_centos7.2_20250612103709.tar.gz"
    wget "$pkg_addr$pkg_name" -O nuwa-php-masking.tar.gz &&
    tar zxvf nuwa-php-masking.tar.gz -C $tempDir &&
    rm -rf nuwa-php-masking.tar.gz &&
    mv "$tempDir/output" ./output/nuwa-php-masking
}

record
compose
make_output
if [[ "${COV}" == "yes" ]]; then
    # $COV is OE pipeline's global variable
    cover_tool_url="https://artifactory.intra.xiaojukeji.com/artifactory/nuwa-local/cover/cover-tools-php.tar.gz"
    curl -sSL ${cover_tool_url} | tar -xz
    source ./cover-tools-php/scripts/build.sh
    cover_build
    # install_coverage
fi

install_profiling
install_log_masking

echo -e "build done"
exit 0
