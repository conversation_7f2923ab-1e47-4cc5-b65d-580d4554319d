#!/bin/bash

workspace=$(cd $(dirname $0) && pwd -P)
cd ${workspace}

CLUSTER=`cat .deploy/service.cluster.txt`
BR_CLUSTER=br01
LD_PRELOAD="/home/<USER>/biz-recorder/boss-recorder-loader.so /usr/lib64/libcurl.so.4"
KOALA_SO="/home/<USER>/biz-recorder/boss-recorder.so"
KOALA_BR_SO="/home/<USER>/biz-recorder/boss-recorder-br.so"

function start() {
    if [[ $CLUSTER == $BR_CLUSTER* ]]; then
        rm -rf $KOALA_SO && mv $KOALA_BR_SO $KOALA_SO
    fi

    # start agent
    nohup /home/<USER>/biz-recorder/bin/agent >> /home/<USER>/biz-recorder/agent.log 2>&1 &
    pid=$!
    echo $pid > /home/<USER>/biz-recorder/agent.pid
    echo "start agent success, pid=${pid}"

    # reload php-fpm
    if [ -f "/home/<USER>/php7/control.sh" ]; then
        source ./recorder_env.sh
        bash /home/<USER>/php7/control.sh reload
    	echo "reload php-fpm success"
    fi
    exit 0
}

function stop() {
    if [ -f "/home/<USER>/biz-recorder/agent.pid" ]; then 
        pid=`cat /home/<USER>/biz-recorder/agent.pid`

        if [ ! -z "$pid" ]; then
            kill $pid &>/dev/null
            sleep 2
        fi
    fi

    echo "stop success"
    exit 0
}

case $1 in
    "start" )
        start
        ;;
    "stop" )
        stop
        ;;
    * )
        echo "unknow command"
        exit 1
        ;;
esac
