pid = var/run/nuwa.pid

[http]
# Listen ip:port, default ":8999"
addr = :9003
# Handler timeout(ms), default 5000
handlerTimeout = 1000
# Request header timeout(ms), default 2000
readHeaderTimeout = 1000
# Recieve http request timeout(ms), including the body, default 5000
readTimeout = 1000
# Recieve http body and response timeout(ms), default 10000
writeTimeout = 1000
# Keep-alive timeout(ms), default 60000
idleTimeout = 5000

[log]
type = file
prefix = didi

file.enable = true
file.dir = ./log
file.rotate_by_hour = true
file.level = TRACE
file.format = [%L][%Z][%S]%M
file.seprated = true
file.auto_clear = true
file.clear_hours = 168
file.call_depth = 6

[zaplog]
filename = log/record.log.%Y%m%d%H
linkname = log/record.log
maxHourAge = 4
maxHourRotate = 1
