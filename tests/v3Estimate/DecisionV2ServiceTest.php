<?php

namespace Test\v3Estimate;

use BizLib\Database\Result;
use BizLib\Utils\Language;
use PreSale\Logics\v3Estimate\AthenaRecommend;
use PreSale\Logics\v3Estimate\DecisionV2Service;
use PHPUnit\Framework\TestCase;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutBuilder;
use PreSale\Test\utils\BaseTestCase;
use PreSale\Test\utils\TestToggleResult;
use Xiaoju\Apollo\Apollo;
use Xiaoju\Apollo\NamespaceConfResult;

/**
 * @runTestsInSeparateProcesses
 */
class DecisionV2ServiceTest extends BaseTestCase
{

    /**
     * 测试出企使用实验
     *
     * @return void
     */
    public function testGetAggConfByGroupId() {
        $oMockApollo = \Mockery::mock('overload:' . Apollo::class);
        $oMockApollo->shouldReceive('getInstance')
            ->andReturn($oMockApollo);
        $oMockApollo->shouldReceive('featureToggle')->andReturn(new TestToggleResult(true, []));

        $oMockLanguage = \Mockery::mock('overload:' . Language::class);
        $oMockLanguage->shouldReceive('getDecodedTextFromDcmp')
            ->andReturn(
                ['manufacturer_in' => 'wycll_bubble_form_mogou',]
            );

        // mock apollo car_aggregation conf
        $oMockNameSpaceConfig = \Mockery::mock(NamespaceConfResult::class);
        $oMockNameSpaceConfig->shouldReceive('getAllConfigData')->andReturn(
            [
                true,
                [[
                    'sub_group_id' => LayoutBuilder::SUB_GROUP_ID_SHORT_DISTANCE,
                    'product_list' => [
                        '280',
                        '282',
                        '285',
                    ],
                ],
                ],
            ]
        );
        $oMockApollo->shouldReceive('getConfigsByNamespaceAndConditions')
            ->with('car_aggregation', ['sub_group_id' => LayoutBuilder::SUB_GROUP_ID_SHORT_DISTANCE])
            ->andReturn($oMockNameSpaceConfig);

        $oMockApollo->shouldReceive('getHitExperimentInLayer')
            ->withSomeOfArgs('wycll_bubble_form_mogou')
            ->andReturn(new TestToggleResult(true, ['manufacturer_in' => '287']));

        list($aPcIdToGroupId, $aGroupIdToPcId)
            = DecisionV2Service::getAggConfByGroupId([LayoutBuilder::SUB_GROUP_ID_SHORT_DISTANCE], ['pid' => 123]);
        self::assertEquals([1 => ['280', '282', '285', '287']], $aGroupIdToPcId);
        self::assertEquals(['280' => 1, '282' => 1, '285' => 1, '287' => 1], $aPcIdToGroupId);
    }


    /**
     * 测试根据Athena变更组
     *
     * @return void
     */
    public function testMoveSubGroupByAthena() {
        $decisionService = DecisionV2Service::getInstance();

        $pcIdToGroupIdProp = new \ReflectionProperty(DecisionV2Service::class, '_newPcIDToSubGroupID');
        $pcIdToGroupIdProp->setAccessible(true);
        $pcIdToGroupIdProp->setValue(
            $decisionService,
            [
                '280' => 1,
                '282' => 1,
                '285' => 1,
            ]
        );

        $groupIdToPcId = new \ReflectionProperty(DecisionV2Service::class, '_newSubGroupIDToProducts');
        $groupIdToPcId->setAccessible(true);
        $groupIdToPcId->setValue(
            $decisionService,
            [
                1 => ['280', '282', '285'],
            ]
        );

        $oAthenaMock = \Mockery::mock('alias:'.AthenaRecommend::class);
        $oAthenaMock->shouldReceive('getInstance')
            ->andReturn(
                new MockAthenaInstance(
                    [
                        '280' => LayoutBuilder::SUB_GROUP_ID_DISCOUNT_ALLIANCE,
                        '282' => LayoutBuilder::SUB_GROUP_ID_SHORT_DISTANCE,
                    ]
                )
            );

        $decisionService->moveSubGroupByAthena();

        self::assertEquals(
            LayoutBuilder::SUB_GROUP_ID_DISCOUNT_ALLIANCE,
            DecisionV2Service::getInstance()->getSubGroupIDByPcID('280')
        );
        self::assertEquals(
            LayoutBuilder::SUB_GROUP_ID_SHORT_DISTANCE,
            DecisionV2Service::getInstance()->getSubGroupIDByPcID('282')
        );
        self::assertEquals(
            LayoutBuilder::SUB_GROUP_ID_SHORT_DISTANCE,
            DecisionV2Service::getInstance()->getSubGroupIDByPcID('285')
        );
    }
}

/**
 * Mock出来的Athena推荐结果返回
 */
class MockAthenaInstance
{
    private $_dict;

    /**
     * @param array $dict mock出的Pcid到推荐结果的字典
     */
    public function __construct($dict) {
        $this->_dict = $dict;
    }

    /**
     * @return array|mixed 推荐结果
     */
    public function getRecPcToSubGroup() {
        return $this->_dict ?? [];
    }
}
