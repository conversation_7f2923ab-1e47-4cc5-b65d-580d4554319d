<?php

namespace PreSale\Test\v3Estimate;

use BizLib\Utils\Language;
use PreSale\Logics\v3Estimate\CategoryInfoLogic;
use PHPUnit\Framework\TestCase;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;
use PreSale\Logics\v3Estimate\multiResponse\Component\Selection;
use PreSale\Test\utils\TestConfResult;
use PreSale\Test\utils\TestToggleResult;
use Xiaoju\Apollo\Apollo;
use Xiaoju\Apollo\MockApollo;

/**
 */
class CategoryInfoLogicTest extends TestCase
{
    function testInitCategoryInfo() {
        $dcmpMock = \Mockery::mock('overload:'.Language::class);
        $dcmpMock->shouldReceive('getDecodedTextFromDcmp')
            ->with('config_text-classify_tab_exp_config')
            ->andReturn(['exp_name' => 'test']);
        $apolloMock = \Mockery::mock('overload:'.Apollo::class);
        $apolloMock->shouldReceive('getInstance')->andReturn($apolloMock);
        $apolloMock->shouldReceive('featureToggle')
            ->withSomeOfArgs('test')
            ->andReturn(new TestToggleResult(true, ['category_config_name', '']));

        $ret = json_decode('[{"category_id":1,"rank":1,"title":"特价","sub_title":"出发","section_title":"特价出发","icon":"https://img-hxy0idistatic.com/static/starimg/img/mhrYeDSBek1689391765653.png","bg_gradients":["#FFECDB","#FFFFFF","#E5FFC4"],"sub_group_id":[],"product_list":["1"]},{"category_id":2,"rank":2,"title":"特价","sub_title":"出发","section_title":"特价出发","icon":"https://img-hxy0idistatic.com/static/starimg/img/mhrYeDSBek1689391765653.png","bg_gradients":["#FFECDB","#FFFFFF","#E5FFC4"],"sub_group_id":[],"product_list":["2"]},{"category_id":10,"rank":3,"title":"特价","sub_title":"出发","section_title":"特价出发","icon":"https://img-hxy0idistatic.com/static/starimg/img/mhrYeDSBek1689391765653.png","bg_gradients":["#FFECDB","#FFFFFF","#E5FFC4"],"sub_group_id":[],"product_list":["3"]},{"category_id":99,"rank":-1,"title":"特价","sub_title":"出发","section_title":"特价出发","icon":"https://img-hxy0idistatic.com/static/starimg/img/mhrYeDSBek1689391765653.png","bg_gradients":["#FFECDB","#FFFFFF","#E5FFC4"],"sub_group_id":[],"product_list":[]}]',true);
        $apolloMock->shouldReceive('getConfigsByNamespace')
            ->with('classify_category_info_config')
            ->andReturn(new TestConfResult(true, $ret));
        $mockSelection = Selection::getInstance();
        $mockSelection->aSelectionMap = [1 => false, 2 => true, 3 => false, 4 =>false];

        $mock = \Mockery::mock(BizCommonInfo::class)
        ->shouldIgnoreMissing();
        $p = CategoryInfoLogic::getInstance($mock, null);
        self::assertEquals(1, $p->getCategoryInfo()[0]['is_selected']);
        self::assertEquals(1, $p->getCategoryInfo()[0]['category_id']);
    }

    function testInitCategoryInfo2() {
        $dcmpMock = \Mockery::mock('overload:'.Language::class);
        $dcmpMock->shouldReceive('getDecodedTextFromDcmp')
            ->with('config_text-classify_tab_exp_config')
            ->andReturn(['exp_name' => 'test']);
        $apolloMock = \Mockery::mock('overload:'.Apollo::class);
        $apolloMock->shouldReceive('getInstance')->andReturn($apolloMock);
        $apolloMock->shouldReceive('featureToggle')
            ->withSomeOfArgs('test')
            ->andReturn(new TestToggleResult(true, ['category_config_name', '']));

        $ret = json_decode('[{"category_id":1,"rank":1,"title":"特价","sub_title":"出发","section_title":"特价出发","icon":"https://img-hxy0idistatic.com/static/starimg/img/mhrYeDSBek1689391765653.png","bg_gradients":["#FFECDB","#FFFFFF","#E5FFC4"],"sub_group_id":[],"product_list":["1"]},{"category_id":2,"rank":2,"title":"特价","sub_title":"出发","section_title":"特价出发","icon":"https://img-hxy0idistatic.com/static/starimg/img/mhrYeDSBek1689391765653.png","bg_gradients":["#FFECDB","#FFFFFF","#E5FFC4"],"sub_group_id":[],"product_list":["2"]},{"category_id":10,"rank":3,"title":"特价","sub_title":"出发","section_title":"特价出发","icon":"https://img-hxy0idistatic.com/static/starimg/img/mhrYeDSBek1689391765653.png","bg_gradients":["#FFECDB","#FFFFFF","#E5FFC4"],"sub_group_id":[],"product_list":["3"]},{"category_id":99,"rank":-1,"title":"特价","sub_title":"出发","section_title":"特价出发","icon":"https://img-hxy0idistatic.com/static/starimg/img/mhrYeDSBek1689391765653.png","bg_gradients":["#FFECDB","#FFFFFF","#E5FFC4"],"sub_group_id":[],"product_list":[]}]',true);
        $apolloMock->shouldReceive('getConfigsByNamespace')
            ->with('classify_category_info_config')
            ->andReturn(new TestConfResult(true, $ret));
        $mockSelection = Selection::getInstance();
        $mockSelection->aSelectionMap = [1 => false, 2 => false, 3 => true, 4 =>false];

        $mock = \Mockery::mock(BizCommonInfo::class)
            ->shouldIgnoreMissing();
        $p = CategoryInfoLogic::getInstance($mock, null);
        self::assertEquals(10, $p->getCategoryInfo()[3]['category_id']);
        self::assertEquals(1, $p->getCategoryInfo()[3]['is_selected']);
    }

}
