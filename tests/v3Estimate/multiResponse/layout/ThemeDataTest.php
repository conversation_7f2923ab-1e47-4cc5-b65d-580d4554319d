<?php

namespace PreSale\Test\v3Estimate\multiResponse\layout;

use BizLib\Utils\Language;
use Dirpc\SDK\Dcmp\DcmpClient;
use Disf\SPL\Trace;
use Disf\SPL\TraceConfig;
use PreSale\Logics\v3Estimate\AthenaRecommend;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;
use PreSale\Logics\v3Estimate\multiResponse\layout\ThemeData;
use PHPUnit\Framework\TestCase;
use PreSale\Test\utils\MockDcmp;
use Xiaoju\Apollo\Apollo;

/**
 * @runTestsInSeparateProcesses
 * @preserveGlobalState disabled
 */
class ThemeDataTest extends TestCase
{
    private $expectedOldBargain = array(
        'theme_type'         => 5,
        'outer_bg_gradients' => array(
            0 => '#FC5801',
            1 => '#FE934E',
        ),
        'title'              => '打车可讲价，讲个好价出发！',
        'icon'               => 'https://dpubstatic.udache.com/static/dpubimg/jfi4wBq7vFNKUR-Q4eIuK.png',
        'right_icon'         => 'https://img-hxy021.didistatic.com/static/starimg/img/xKyq1VzNex1680249410046.png',
    );

    private $expectedNewBargain = array(
        'theme_type'          => 8,
        'text_corlor'         => '#FFFFFF',
        'border_corlor'       => '#FFBEAA',
        'outer_bg_gradients'  => [
            '#FFC715',
            '#FF6435',
        ],
        'disable_selected_bg' => 1,
        'title'               => '打车可讲价，讲个好价出发！',
        'icon'                => 'https://dpubstatic.udache.com/static/dpubimg/jfi4wBq7vFNKUR-Q4eIuK.png',
    );

    private $expectedNewCommon = array(
        'theme_type'          => 8,
        'text_corlor'         => '#5A413A',
        'border_corlor'       => '#EBC7BC',
        'outer_bg_gradients'  => array(
            0 => '#FBF9F0',
            1 => '#FFDAC0',
        ),
        'disable_selected_bg' => 1,
        'title'               => '测试测试测试测试',
        'icon'                => 'https://dpubstatic.udache.com/static/dpubimg/jfi4wBq7vFNKUR-Q4eIuK.png',
    );

    /**
     * @return void
     */
    public function testBuildThemeBargainOld() {
        MockDcmp::init();

        $mockBizCommonInfo = \Mockery::mock(BizCommonInfo::class)->makePartial();
        $mockBizCommonInfo->shouldReceive('getApolloParams')->andReturn([]);

        $mockThemeData = \Mockery::mock(ThemeData::class)->makePartial();

        $mockAthena = \Mockery::mock('alias:' . AthenaRecommend::class);
        $mockAthena->shouldReceive('getInstance')->andReturn($mockAthena);
        $mockAthena->shouldReceive('getTopRecommendStyle')->andReturn(
            [
                'style'      => 2,
                'extra_info' => ['bargain_scene_recommend' => '1',],
                'text'       => '打车可讲价，讲个好价出发！',
            ]
        );

        $mockThemeData->Init($mockBizCommonInfo);
        $k = $mockThemeData->getThemeBargain();
        self::assertEquals($this->expectedOldBargain, $k);
    }

    public function testBuildThemeBargainNew() {
        MockDcmp::init();

        $mockBizCommonInfo = \Mockery::mock(BizCommonInfo::class)->makePartial();
        $mockBizCommonInfo->shouldReceive('getApolloParams')->andReturn([]);

        $mockThemeData = \Mockery::mock(ThemeData::class)->makePartial();
        $mockThemeData->shouldAllowMockingProtectedMethods();
        $mockThemeData->shouldReceive('isEnableNormativeTheme')->andReturn(true);

        $mockAthena = \Mockery::mock('alias:' . AthenaRecommend::class);
        $mockAthena->shouldReceive('getInstance')->andReturn($mockAthena);
        $mockAthena->shouldReceive('getTopRecommendStyle')->andReturn(
            [
                'style'      => 2,
                'extra_info' => ['bargain_scene_recommend' => '1',],
                'text'       => '打车可讲价，讲个好价出发！',
            ]
        );

        $mockThemeData->Init($mockBizCommonInfo);
        $k = $mockThemeData->getThemeBargain();
        unset($k['hint']);
        self::assertEquals($this->expectedNewBargain, $k);
    }

    function testBuildCommonThemeOld() {
        MockDcmp::init();

        $mockBizCommonInfo = \Mockery::mock(BizCommonInfo::class)->makePartial();
        $mockBizCommonInfo->shouldReceive('getApolloParams')->andReturn([]);

        $mockThemeData = \Mockery::mock(ThemeData::class)->makePartial();
        $mockThemeData->shouldAllowMockingProtectedMethods();
        $mockThemeData->shouldReceive('isEnableNormativeTheme')->andReturn(true);

        $mockAthena = \Mockery::mock('alias:' . AthenaRecommend::class);
        $mockAthena->shouldReceive('getInstance')->andReturn($mockAthena);
        $mockAthena->shouldReceive('getTopRecommendStyle')->andReturn(
            [
                'style'    => 1,
                'text'     => '测试测试测试测试',
                'sub_text' => '1测试测试测试测试',
                'icon'     => 'https://dpubstatic.udache.com/static/dpubimg/jfi4wBq7vFNKUR-Q4eIuK.png',
                'sub_icon' => '1https://dpubstatic.udache.com/static/dpubimg/jfi4wBq7vFNKUR-Q4eIuK.png',
            ]
        );
        $mockAthena->shouldReceive('getTopSinglePcIds')->andReturn([68]);

        $mockApollo = \Mockery::mock('overload:'.Apollo::class);
        $mockApollo->shouldReceive('getInstance')->andReturn($mockApollo);
        $mockApollo->shouldReceive('getConfigsByNamespace')->andReturn(new MockApolloConf());

        $mockThemeData->Init($mockBizCommonInfo);
        $k = $mockThemeData->getThemeDataSingleCar(68);
        unset($k['hint']);
        self::assertEquals($this->expectedNewCommon, $k);
        self::assertEquals(null,  $mockThemeData->getThemeDataSingleCar(69));
    }
}

class MockApolloConf
{
    public function getAllConfigData() {
        $temp = '{"theme_key":"new_group","title":"叫车高峰·极速接单","sub_title":"超时赔付","title_icon":"","right_image":"","texture_image":"","theme_color":"#576b95","selected_bg_gradients":{"start_color":"#00FCFCFC","end_color":"#5CCFDCFC"},"out_bg_gradients":{"start_color":"#FF8443","mid_color":"#9AA1FE","end_color":"#FF6524"}}';
        $temp = json_decode($temp, true);
        return [
            true,
            ['new_group_box' => $temp,],
        ];
    }
}
