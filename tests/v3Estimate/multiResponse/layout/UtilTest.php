<?php

namespace v3Estimate\multiResponse\layout;

use PreSale\Logics\v3Estimate\multiResponse\layout\Util;
use PHPUnit\Framework\TestCase;
use <PERSON>ju\Apollo\Apollo;
use <PERSON><PERSON>\Apollo\NamespaceConfResult;
use <PERSON><PERSON>\Apollo\ToggleResult;

/**
 * UtilTest
 * @runTestsInSeparateProcesses
 */
class UtilTest extends TestCase
{

    /**
     * @return void
     */
    public function testGetTaxiGroupIcon() {
        $mockApollo = \Mockery::mock('overload:' . Apollo::class);
        $mockApollo->shouldReceive('getInstance')->andReturn($mockApollo);

        $mockToggle = \Mockery::mock(NamespaceConfResult::class);
        $mockToggle->shouldReceive('getAllConfigData')
            ->andReturn([true, [['car_icon' => 'test_icon']]]);
        $mockApollo->shouldReceive('getConfigsByNamespaceAndConditions')
            ->andReturn($mockToggle);

        self::assertEquals('test_icon', Util::getTaxiGroupIcon('', 1));
    }
}
