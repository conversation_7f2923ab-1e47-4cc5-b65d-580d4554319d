<?php

namespace PreSale\Test\v3Estimate\multiResponse\Component\basicFeeMsg;

use BizLib\Utils\Language;
use Dirpc\SDK\Dcmp\DcmpClient;
use Dirpc\SDK\PreSale\NewFormEstimateData;
use Dirpc\SDK\PriceApi\EstimateNewFormData;
use Dirpc\SDK\PriceApi\EstimateNewFormExtend;
use Dirpc\SDK\PriceApi\OrderInfo;
use PreSale\Logics\sideEstimate\component\formData\feeDescList\AplusFeeDesc;
use PreSale\Logics\v3Estimate\BizProduct;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;
use PreSale\Logics\v3Estimate\multiRequest\Product;
use PreSale\Logics\v3Estimate\multiResponse\Component\basicFeeMsg\APlusFeeMsg;
use PreSale\Logics\v3Estimate\multiResponse\Component\basicFeeMsg\NormalBasicFeeMsg;
use PHPUnit\Framework\TestCase;
use PreSale\Logics\v3Estimate\multiResponse\Component\basicFeeMsg\Util;

class fakeOrder
{
    public $iRequireLevel;
    public $iComboType;
    public $iLevelType;

    /**
     * @param $iRequireLevel
     * @param $iComboType
     * @param $iLevelType
     */
    public function __construct($iRequireLevel, $iComboType, $iLevelType) {
        $this->iRequireLevel = $iRequireLevel;
        $this->iComboType    = $iComboType;
        $this->iLevelType    = $iLevelType;
    }
}

/**
 * @runTestsInSeparateProcesses
 */
class NormalBasicFeeMsgTest extends TestCase
{
    /**
     * @param $lowerBoundFee
     * @param $estimateFee
     * @param $bizTemplate
     * @param $personalTemplate
     * @param $isRangeTemplateEnabled
     * @param $mixedDeductPrice
     * @param $expectedFeeMsg
     * @param $expectedMinFee
     * @param $expectedFeeRangeTemplate
     * @return void
     * @dataProvider aPlusDescDataProvider
     */
    public function testGetAPlusCarDescBiz(
        $lowerBoundFee, $estimateFee, $bizTemplate, $personalTemplate, $isRangeTemplateEnabled, $mixedDeductPrice,
        $expectedFeeMsg, $expectedMinFee, $expectedFeeRangeTemplate) {
        $mockBizProduct = \Mockery::mock(BizProduct::class)->makePartial();
        $mockBizProduct->shouldReceive('getProductCategory')->andReturn(68);
        $mockBizProduct->shouldReceive('getProductID')->andReturn(3);

        $mockCommon = \Mockery::mock(BizCommonInfo::class);
        $mockCommon->shouldReceive('getApolloParams')->andReturn([]);

        $extend    = new EstimateNewFormExtend();
        $extend->setEstimateFee($lowerBoundFee);
        $extend->setSceneMark([
            'is_a_plus_range' => '1'
        ]);

        $mockPrice = new EstimateNewFormData();
        $mockPrice->setExtendList([$extend]);
        $mockPrice->setEstimateFee($estimateFee);
        $mockBizProduct->setPriceInfo($mockPrice);

        $mockBizLib = \Mockery::mock('alias:'.\BizLib\Utils\Product::class)->makePartial();
        $mockBizLib->shouldReceive('isDefault')->andReturn(false);

        \Mockery::getConfiguration()->setConstantsMap(
            [
                Language::class => ['PT_BR' => 'pt-BR',],
            ]
        );
        $mockUtil = \Mockery::mock('overload:'.DcmpClient::class);
        $mockUtil = $mockUtil->shouldReceive('getInstance')->andReturn($mockUtil);
        $mockUtil->shouldReceive('getContent')
            ->withSomeOfArgs('estimate_form_v3-estimate_price_msg')
            ->andReturn(json_encode(['mix_personal' => $bizTemplate]));

        $basicFeeMsg = \Mockery::mock(APlusFeeMsg::class, [$mockBizProduct, $mockCommon])->makePartial();
        $basicFeeMsg->shouldAllowMockingProtectedMethods();
        $basicFeeMsg->shouldReceive('getAPlusCarDesc')->andReturn([$personalTemplate, Util::FEE_TYPE_CAPPRICE]);
        $basicFeeMsg->shouldReceive('getProductID')->andReturn(3);
        $basicFeeMsg->shouldReceive('getOrderInfo')->andReturn(new fakeOrder(600, 0, 1));
        $basicFeeMsg->shouldReceive('getAppLanguage')->andReturn('zh-CN');
        $basicFeeMsg->shouldReceive('getProductCategory')->andReturn(68);
        $basicFeeMsg->shouldReceive('isRangeTemplateEnabled')->andReturn($isRangeTemplateEnabled);
        $basicFeeMsg->shouldReceive('getMixedDeductPrice')->andReturn($mixedDeductPrice);

        list($feeTemplate, $e) = $basicFeeMsg->feeMsgAndTemplate();
        list($b, $c, $e)  = $basicFeeMsg->buildAplusRange($e);
        list($a, $b, $c, $e)  = $basicFeeMsg->renderFeeMsg($e, $b, $c, $feeTemplate, '');
        self::assertEquals($expectedFeeMsg, $a);
        self::assertEquals($expectedMinFee, $b);
        self::assertEquals($expectedFeeRangeTemplate, $c);
        assert(true);
    }
    /**
     * @param $lowerBoundFee
     * @param $estimateFee
     * @param $bizTemplate
     * @param $personalTemplate
     * @param $isRangeTemplateEnabled
     * @param $mixedDeductPrice
     * @param $expectedFeeMsg
     * @param $expectedMinFee
     * @param $expectedFeeRangeTemplate
     */

    public function aPlusDescDataProvider() {
        return [
            [12.23, 15.42, '个人付约{{{num}}}元', '车费{{{num}}}元', true, 2.35, '个人付约{9.88-13.07}元', '9.88', '个人付约 %s 元'],
            [12.23, 15.42, '个人付约{{{num}}}元', '车费{{{num}}}元', true, 13.23, '个人付约{0-2.19}元', '0', '个人付约 %s 元'],
            [12.23, 15.42, '个人付约{{{num}}}元', '车费{{{num}}}元', false, 13.23, '个人付约{0-2.19}元', '', ''],
            [12.23, 15.42, '个人付约{{{num}}}元', '车费{{{num}}}元', true, 20, '个人付约{0}元', '', ''],
            [12.23, 15.42, '个人付约{{{num}}}元', '车费{{{num}}}元', true, 0, '车费{12.23-15.42}元', '12.23', '车费 %s 元'],
            [16.32, 15.42, '个人付约{{{num}}}元', '车费{{{num}}}元', true, 0, '车费{15.42}元', '', ''],
        ];
    }
}
