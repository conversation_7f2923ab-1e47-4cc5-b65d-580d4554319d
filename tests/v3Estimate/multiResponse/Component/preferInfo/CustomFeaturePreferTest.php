<?php

namespace PreSale\Test\v3Estimate\multiResponse\Component\preferInfo;

use BizLib\Utils\Language;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;
use PreSale\Logics\v3Estimate\multiResponse\Component\preferInfo\CustomFeaturePrefer;
use PHPUnit\Framework\TestCase;

/**
 * @runTestsInSeparateProcesses
 */
class CustomFeaturePreferTest extends TestCase
{
    private $preferData = array(
        102 =>
            array (
                'id' => 102,
                'title' => '机场助理引导',
                'icon' => 'https://img-hxy021.didistatic.com/static/starimg/img/SrTCQxmaLV1670832540966.png',
                'light_icon' => 'https://pt-starimg.didistatic.com/static/starimg/img/eYAIQZ39Pp1585567001672.png',
                'max' => 1,
            ),
        112 =>
            array (
                'id' => 112,
                'title' => '司机举牌接机',
                'icon' => 'https://img-hxy021.didistatic.com/static/starimg/img/0CaqdJtmgk1689586231458.png',
                'light_icon' => 'https://img-hxy021.didistatic.com/static/starimg/img/TAQ1jmRDlj1689586231673.png',
                'max' => 1,
            ),
        108 => array(
            'id'         => 108,
            'title'      => '专车臻选',
            'icon'       => 'https://img-hxy021.didistatic.com/static/starimg/img/CmVuQxkM941675913907610.png',
            'light_icon' => '',
            'max'        => 1,
        ),
        106 => array(
            'id'         => 106,
            'title'      => '宠物专车',
            'icon'       => 'https://img-hxy021.didistatic.com/static/starimg/img/CmVuQxkM941675913907610.png',
            'light_icon' => '',
            'max'        => 1,
        ),
    );

    /**
     * @param array  $availableService $availableService
     * @param string $expected         $expected
     * @return void
     * @dataProvider preferContentDataProvider
     */
    public function testGetPreferContent($availableService, $expected) {
        $mock = \Mockery::mock('alias:' . Language::class);
        $mock->shouldReceive('getDecodedTextFromDcmp')
            ->withSomeOfArgs('prefer_info-priority_feature')
            ->andReturn(json_decode('{"priority_service_id":[108,106,102,112],"conjunction":"｜"}', true));

        $mockBizCommonInfo = \Mockery::mock(BizCommonInfo::class);
        $mockBizCommonInfo->shouldReceive('getAppLanguage')
            ->andReturn('zh-CN');

        $aAllAvailableCustomService = [];
        foreach ($availableService as $service_id) {
            $aAllAvailableCustomService[$service_id] = $this->preferData[$service_id];
        }

        $sPreferOptionConfig = array(
            'title'            => '设置本次乘车服务偏好',
            'set_option_title' => '乘车偏好已设置',
            'icon'             => 'https://pt-starimg.didistatic.com/static/starimg/img/9YAFdLFAxw1587353055757.png',
            'service_list'     => array(
                101 => '电话沟通',
                107 => '帮开车门',
                108 => '送进小区',
                102 => '行程勿扰',
                103 => '精简服务语',
                104 => '帮拿行李',
                105 => '饮用水',
                106 => '调大后排空间',
            ),
        );

        $featurePrefer = new CustomFeaturePrefer(null, $mockBizCommonInfo);

        $tag = $featurePrefer->tryGetPreferContent($aAllAvailableCustomService, array(), array(), $sPreferOptionConfig);
        self::assertEquals($expected, $tag[0]->getContent());
    }

    /**
     * @return array[]
     */
    public function preferContentDataProvider() {
        return [
            [[106, 108], '宠物专车｜设置本次乘车服务偏好'],
            [[106, 108, 102], '机场助理引导｜设置本次乘车服务偏好'],
            [[106, 108, 102, 112], '司机举牌接机｜设置本次乘车服务偏好'],
            [[106], '宠物专车｜设置本次乘车服务偏好'],
            [[108], '专车臻选｜设置本次乘车服务偏好'],
            [[108, 112], '司机举牌接机｜设置本次乘车服务偏好'],
            [[112, 108], '司机举牌接机｜设置本次乘车服务偏好'],
            [[], '设置本次乘车服务偏好'],
        ];
    }
}
