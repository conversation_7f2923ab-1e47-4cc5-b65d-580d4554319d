<?php

namespace v3Estimate\multiResponse\filterInfo;

use PreSale\Logics\v3Estimate\AthenaRecommend;
use PHPUnit\Framework\TestCase;
use PreSale\Logics\v3Estimate\multiResponse\filterInfo\NormalFilter;
use ReflectionClass;
use ReflectionException;

/**
 * 测试
 */
class NormalFilterTest extends TestCase
{
    /**
     * @param mixed  $obj  $obj
     * @param string $name $name
     * @param mixed  $val  $val
     * @return void
     * @throws ReflectionException 测试
     */
    private function _setPrivateField($obj, $name, $val) {
        $reflection         = new ReflectionClass($obj);
        $reflectionProperty = $reflection->getProperty($name);
        $reflectionProperty->setAccessible(true);
        $reflectionProperty->setValue($obj, $val);
    }

    /**
     * @param ReflectionClass $class $class
     * @param mixed           $obj   $obj
     * @param string          $name  $name
     * @param mixed           $val   $val
     * @return void
     * @throws ReflectionException 测试
     */
    private function _setPrivateFieldWithReflectionClass($class, $obj, $name, $val) {
        $reflectionProperty = $class->getProperty($name);
        $reflectionProperty->setAccessible(true);
        $reflectionProperty->setValue($obj, $val);
    }

    /**
     * @param array $filter 设置dcmp文案
     * @return void
     * @throws ReflectionException 测试
     */
    private function _setNormalFilterDcmp($filter) {
        $reflection = new ReflectionClass(NormalFilter::class);
        $oDcmpJson  = json_decode(
            '{
  "answer_rate_black": "预估应答率{color=#000000 font=5 size=20 text=%d%%},",
  "answer_rate_green": "预估应答率{color=#227B70 font=5 size=20 text=%d%%},",
  "short_supply_right_text": "多选车型更快出发",
  "normal_supply_right_text": "周围运力充足",
  "no_select_text": "请至少勾选一种车型",
  "old_recommend_exceed_limit": "已选%s种车型，多勾更快出发",
  "old_selected_exceed_limit": "已选%s种车型，叫车高峰推荐智能勾选"
}',
            true
        );
        $this->_setPrivateFieldWithReflectionClass(
            $reflection,
            $filter,
            '_oAnswerRateBlack',
            $oDcmpJson['answer_rate_black']
        );
        $this->_setPrivateFieldWithReflectionClass(
            $reflection,
            $filter,
            '_oAnswerRateGreen',
            $oDcmpJson['answer_rate_green']
        );
        $this->_setPrivateFieldWithReflectionClass(
            $reflection,
            $filter,
            '_oShortSupplyRightText',
            $oDcmpJson['short_supply_right_text']
        );
        $this->_setPrivateFieldWithReflectionClass(
            $reflection,
            $filter,
            '_oNormalSupplyRightText',
            $oDcmpJson['normal_supply_right_text']
        );
        $this->_setPrivateFieldWithReflectionClass(
            $reflection,
            $filter,
            '_oNoSelectText',
            $oDcmpJson['no_select_text']
        );
        $this->_setPrivateFieldWithReflectionClass(
            $reflection,
            $filter,
            '_aOldRecommendExceedLimit',
            $oDcmpJson['old_recommend_exceed_limit']
        );
        $this->_setPrivateFieldWithReflectionClass(
            $reflection,
            $filter,
            '_aOldSelectedExceedLimit',
            $oDcmpJson['old_selected_exceed_limit']
        );
    }

    /**
     * @param int    $aFilterRefreshType $aFilterRefreshType
     * @param bool   $isNewTitleEnabled  $isNewTitleEnabled
     * @param int    $filterId           $filterId
     * @param int    $answerRate         $answerRate
     * @param string $etInfo             $etInfo
     * @param string $title              $title
     * @param string $expected           $expected
     * @return void
     * @throws ReflectionException ReflectionException
     * @dataProvider dataProvider
     */
    public function testOne($aFilterRefreshType, $isNewTitleEnabled, $filterId, $answerRate,
        $etInfo, $title, $expected
    ) {
        $athenaInstance = AthenaRecommend::getInstance();
        $this->_setPrivateField($athenaInstance, '_aFilterRefreshType', $aFilterRefreshType);

        $normalFilter = \Mockery::mock(
            NormalFilter::class,
            [
                null,
                null,
                [
                    'filter_list' => [['is_default' => 1, 'filter_id' => $filterId, 'etp' => $etInfo]],
                    'answer_rate' => $answerRate,
                    'title'       => $title,
                ],
                null,
            ]
        )
            ->makePartial();
        $normalFilter->shouldAllowMockingProtectedMethods();
        $normalFilter->shouldReceive('_isNewTitleEnabled')->once()->andReturn($isNewTitleEnabled);

        $this->_setNormalFilterDcmp($normalFilter);
        $filter = $normalFilter->buildFilterInfo();

        $this->assertEquals(
            $expected,
            $filter['filter_normal']['filter_title']
        );
    }

    /**
     * @return array[]
     */
    public function dataProvider(): array {
        // $aFilterRefreshType, $isNewTitleEnabled, $filterId, $answerRate, $shortSupplyType, $etp, $title, $expected
        return [
            [1, true, 1, 80, '', '', '预估应答率{color=#227B70 font=5 size=20 text=80%},周围运力充足'],
            [1, true, 1, 20, '', '', '预估应答率{color=#000000 font=5 size=20 text=30%},多选车型更快出发'],
            [1, true, 1, 80, '', '', '预估应答率{color=#227B70 font=5 size=20 text=80%},周围运力充足'],
            [1, true, 1, 20, '', '', '预估应答率{color=#000000 font=5 size=20 text=30%},多选车型更快出发'],
            [1, true, 1, 0, '', '', '多选车型更快出发'],
            [1, true, 1, 45, '', '', '预估应答率{color=#000000 font=5 size=20 text=45%},多选车型更快出发'],
            [1, true, 1, null, '', '', '多选车型更快出发'],
            [1, true, 1, null, '', '', '多选车型更快出发'],
            [1, false, 1, 45, '', 'test_title', 'test_title'],
            [1, false, 1, 45, '', null, ''],
            [1, false, 1, 45, '', 13, '13'],
            [0, false, 1, 45, '', '', ''],
            [0, false, 1, 45, 11, '', '已选%s种车型，叫车高峰推荐智能勾选'],
            [0, false, null, 2, 11, '', ''],
            [0, false, 3, 45, 11, '', ''],
            [0, false, 1, 45, '11', '', '已选%s种车型，叫车高峰推荐智能勾选'],
            [0, false, 1, 45, 9, '', ''],
            [0, false, 1, 45, 2147483647, '', '已选%s种车型，叫车高峰推荐智能勾选'],
            [0, false, 1, 45, '烫烫烫', '', ''],
            [0, false, 1, 45, null, '', '已选%s种车型，叫车高峰推荐智能勾选'],
            [0, false, 2, 45, 11, '', ''],
            [0, false, 2, 45, 16, '', '已选%s种车型，多勾更快出发'],
        ];
    }
}
