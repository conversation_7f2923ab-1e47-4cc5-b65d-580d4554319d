<?php

namespace PreSale\Test\utils;

use BizLib\Utils\Language;
use Dirpc\SDK\Dcmp\DcmpClient;
use Disf\SPL\Trace;
use PHPUnit\Framework\TestCase;

class MockDcmp extends TestCase
{
    public static function init() {
        $dcmpContent = file_get_contents('tests/data/dcmp.json');
        $dcmpClient  = \Mockery::mock('overload:' . DcmpClient::class);
        $dcmpClient->shouldReceive('getInstance')->andReturn($dcmpClient);
        $mockTrace = \Mockery::mock('overload:' . Trace::class)->makePartial();
        $mockTrace->shouldReceive('hintContent')->andReturn('{"lang": "zh-CN"}');
        $testData = json_decode($dcmpContent, true);
        foreach ($testData as $key => $value) {
            $dcmpClient->shouldReceive('getContent')->withSomeOfArgs($key)->andReturn($value);
        }
    }

    public function testInit() {
        self::init();
        $content = Language::getDecodedTextFromDcmp('test-dcmp_key');
        self::assertEquals('1919810', $content['114514']);
    }
}
