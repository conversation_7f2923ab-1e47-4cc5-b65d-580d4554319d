<?php

namespace PreSale\Test\utils;

/**
 * 用来Mock的apollo开关
 */
class TestToggleResult
{
    private $_allow;

    private $_userParams;

    /**
     * Mock Apollo开关
     *
     * @param bool  $allow      是否允许通过
     * @param array $userParams 用户自定参数
     */
    public function __construct($allow, $userParams) {
        $this->_allow      = $allow;
        $this->_userParams = $userParams;
    }

    /**
     * @return bool 是否命中
     */
    public function allow() {
        return $this->_allow;
    }

    /**
     * @param string $key     参数键
     * @param mixed  $default 默认值
     * @return mixed
     */
    public function getParameter($key, $default) {
        return $this->_userParams[$key] ?? $default;
    }
}
