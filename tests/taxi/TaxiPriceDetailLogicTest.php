<?php

namespace PreSale\Test\taxi;

use BizLib\Client\SpsClient;
use PreSale\Logics\taxi\TaxiPriceDetailLogic;
use PHPUnit\Framework\TestCase;
use PreSale\Test\utils\MockDcmp;

class TaxiPriceDetailLogicTest extends TestCase
{
    private $sTestSpsRet = <<<TestR
{
    "data": {
        "red_packet": 0,
        "config": "",
        "rule": {
            "start_date": "",
            "end_date": "2025-01-01 23:59:59",
            "is_special_day": "",
            "period_conf": [
                {
                    "title": "节假日（2024.01.01-2025.01.01）",
                    "conf_list": [
                        {
                            "day_time": "00:00~23:00",
                            "price": "0.5",
                            "passenger_discount": "0.2",
                            "passenger_discount_price": "0.3",
                            "driver_price": "1.3",
                            "start_time": "00:00:00",
                            "end_time": "23:00:00",
                            "day_period": [
                                "1",
                                "2",
                                "3",
                                "4",
                                "5",
                                "6",
                                "7"
                            ],
                            "is_special_day": "1",
                            "tr": "0",
                            "driver_reward": "0.8"
                        }
                    ],
                    "titleType": 3,
                    "start_time": "2024.01.01",
                    "end_time": "2025.01.01"
                }
            ]
        }
    },
    "errmsg": "success",
    "errno": 0
}
TestR;


    public function testGetPeakFeeConfig() {
        MockDcmp::init();

        $oLogic = new TaxiPriceDetailLogic([]);
        $mock = \Mockery::mock('overload:' . SpsClient::class);
        $mock->shouldReceive('getFeeConfig')->andReturn(json_decode($this->sTestSpsRet, true));
        $sFeeConfig = $oLogic->getPeakFeeConfig(TaxiPriceDetailLogic::PEAK_PERIOD);
        echo json_encode($sFeeConfig, JSON_UNESCAPED_UNICODE);
    }
}
