{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "f40afa26d48127b7506191ab3cdd6593", "packages": [{"name": "apache/log4php", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/apache/logging-log4php.git", "reference": "cac428b6f67d2035af39784da1d1a299ef42fcf2"}, "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-github/vcs-dists/zip/apache/logging-log4php/cac428b6f67d2035af39784da1d1a299ef42fcf2", "reference": "cac428b6f67d2035af39784da1d1a299ef42fcf2", "shasum": ""}, "require": {"php": ">=5.2.7"}, "type": "library", "autoload": {"classmap": ["src/main/php/"]}, "license": ["Apache-2.0"], "description": "A versatile logging framework for PHP", "homepage": "http://logging.apache.org/log4php/", "keywords": ["log", "logging", "php"], "support": {"email": "<EMAIL>", "issues": "https://issues.apache.org/jira/browse/LOG4PHP", "source": "https://svn.apache.org/repos/asf/logging/log4php"}, "abandoned": true, "time": "2012-10-26T09:13:25+00:00"}, {"name": "apollo/apollo-sdk-php", "version": "2.9.2", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/apollo/apollo-sdk-php/apollo-apollo-sdk-php-2.9.2.zip"}, "require-dev": {"ext-json": "*", "phpunit/phpunit": "6.5.9"}, "suggest": {"ext-apcu": "*", "ext-bcmath": "*", "ext-json": "*"}, "type": "library", "autoload": {"psr-4": {"Xiaoju\\Apollo\\": "src/"}}, "license": ["proprietary"], "description": "Apollo php sdk.", "homepage": "http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=29728000", "keywords": ["apollo", "sdk"]}, {"name": "biz/biz-common", "version": "1.13.250", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/biz/biz-common/biz-biz-common-1.13.250.zip"}, "require": {"biz/biz-lib": "3.*", "dirpc/carpool": "2.*", "dirpc/crius": "2.*", "dirpc/driver-bill": "1.*", "dirpc/harmonia": "1.*", "dirpc/hertz": "1.*", "dirpc/hundun": "1.*", "dirpc/intercity-foras": "1.*", "dirpc/robin": "1.*", "dirpc/soraka": ">=1.0.19", "dirpc/trip-cloud-agent": "1.*", "dirpc/trip-cloud-passenger": "1.*", "dirpc/v-o-i-p-s-e-r-v-e-r": "0.*", "dukang/feature-php": "0.*", "dukang/property-const-php-sdk": ">=1.4.86", "ext-bcmath": "*", "ext-curl": "*", "ext-json": "*", "ext-openssl": "*", "gulfstream/bronze-door-sdk-php": "0.*", "gulfstream/hxz-php-quotation-sdk": "0.*", "gulfstream/tripcloud-common": "2.*", "lego/dirpc": "1.*", "nuwa/apollo": "1.*", "s3e/iexp-php": "1.*", "s3e/pts": "1.*", "swaggest/json-diff": "3.8.1"}, "type": "library", "autoload": {"psr-4": {"BizCommon\\": ["src"]}}, "license": ["None"], "keywords": ["biz", "biz-common"]}, {"name": "biz/biz-fpi", "version": "0.3.57", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/biz/biz-fpi/biz-fpi-0.3.57.tar.gz"}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"psr-4": {"Fpi\\": ["src"]}, "classmap": ["src"]}, "license": ["None"], "description": "library for biz", "keywords": ["biz-fpi", "fpi"]}, {"name": "biz/biz-lib", "version": "3.9.177", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/biz/biz-lib/biz-biz-lib-3.9.177.zip"}, "require": {"apollo/apollo-sdk-php": "2.*", "biz/biz-fpi": "0.*", "dirpc/action-gateway": "1.*", "dirpc/active-brick": "1.*", "dirpc/activity-v3": "1.1.0", "dirpc/adonis": "0.*", "dirpc/adx": "1.*", "dirpc/athena-api": "1.*", "dirpc/athena-api-kflower": "1.*", "dirpc/athena-apiv3": "2.*", "dirpc/athena-expect": "1.*", "dirpc/automarket-qrcode": "1.*", "dirpc/axb-v2": "4.*", "dirpc/baichuan": "1.*", "dirpc/baichuan-course": "1.*", "dirpc/bayze": "1.*", "dirpc/beatles-order": "1.*", "dirpc/bifrost": "1.*", "dirpc/bill-data-svr": "1.*", "dirpc/biz-ticket": "1.*", "dirpc/biz-transaction": "1.*", "dirpc/blanka-pixie": "1.*", "dirpc/bonfire": "1.*", "dirpc/bosp-api": "1.*", "dirpc/bounty-task": "1.*", "dirpc/brick": "1.*", "dirpc/c-api": "1.*", "dirpc/calcite-api": "1.*", "dirpc/carpool": "2.*", "dirpc/carpool-api": "1.*", "dirpc/carpool-open-api": "1.*", "dirpc/carpool-rec-service": "1.*", "dirpc/cash-back": "1.*", "dirpc/cashier-api": "1.*", "dirpc/cashier-tradecenter-api": "1.*", "dirpc/cedar-s-f-c-service": "^1.0", "dirpc/ceres-api": "2.*", "dirpc/clearing-api": "1.*", "dirpc/combined-travel": "1.*", "dirpc/common-api": "0.*", "dirpc/common-plat-coupon": "^1.0", "dirpc/commonplat-cashier": "2.*", "dirpc/commonplat-sc-invoice": "1.*", "dirpc/commonplatchunxiao": "1.*", "dirpc/compensation": "1.*", "dirpc/consec": "1.*", "dirpc/consec-service": "1.*", "dirpc/coupon": "1.*", "dirpc/crius": "2.*", "dirpc/csi": "1.*", "dirpc/customcar": "1.*", "dirpc/cyberset": "^1.0", "dirpc/d-master-d-server": "1.*", "dirpc/d-member": "10.*", "dirpc/dai-jia-kop": "1.*", "dirpc/dape-driver-price": "0.0.51", "dirpc/dape-dynamic-price": "0.*", "dirpc/dape-gateway": "2.*", "dirpc/dcmp-sdk": "2.*", "dirpc/dcoin": "1.*", "dirpc/ddpay": "1.*", "dirpc/defender": "1.*", "dirpc/defendernew": "1.*", "dirpc/defensor": "2.*", "dirpc/dfs": "0.*", "dirpc/discount-cal": "1.*", "dirpc/dispatch-judge": "1.*", "dirpc/ditag-service": "1.*", "dirpc/dlock": "2.*", "dirpc/dmaster-push": "1.*", "dirpc/dmasterapi-sdk": "1.*", "dirpc/dmasterapiv2": "1.*", "dirpc/dmc": "2.*", "dirpc/doraemon": "1.*", "dirpc/dorami": "1.*", "dirpc/dos": "1.*", "dirpc/dp-relation": "1.*", "dirpc/dpub-service": "1.*", "dirpc/driver": "1.*", "dirpc/driver-api": "2.*", "dirpc/driver-auditor-go": "1.*", "dirpc/driver-bill": "1.*", "dirpc/driver-card": "1.*", "dirpc/driver-center-go": "1.*", "dirpc/driver-certify": "1.*", "dirpc/driver-device": "^1.0", "dirpc/driver-status": "1.*", "dirpc/driver-task": "1.*", "dirpc/driver-trips": "1.*", "dirpc/driver-trips-go": "1.*", "dirpc/driver-workbench-go": "1.*", "dirpc/dufe-api": "1.*", "dirpc/dufe-data-contribution": "1.*", "dirpc/dufe-storage": "1.*", "dirpc/dupsuds": "1.*", "dirpc/duse-api": "2.*", "dirpc/duse-vrp": "1.*", "dirpc/endowment-auth-service": "1.*", "dirpc/energy-p-o-i-station-api": "1.*", "dirpc/engine-martini-proxy": "1.*", "dirpc/epowerservice": "1.*", "dirpc/es-open-a-p-i": "1.*", "dirpc/estimate-decision": "1.*", "dirpc/ether-access-service": "1.*", "dirpc/euler": "1.*", "dirpc/event-render-service": "1.*", "dirpc/f-p-s-producer": "1.*", "dirpc/feature-service": "1.*", "dirpc/feeds": "1.*", "dirpc/ferrari": "1.*", "dirpc/finance-ddpay-usercenter": "1.*", "dirpc/finance-vision": "1.*", "dirpc/full-dispatch": "1.*", "dirpc/gandalf": "1.*", "dirpc/gauss": "1.*", "dirpc/gcs-sdk": "1.*", "dirpc/geo-fence-http": "1.*", "dirpc/geo-fence-map": "1.*", "dirpc/geofence-sdk": "1.*", "dirpc/getfeatureservicev2": "1.*", "dirpc/go-member": "10.*", "dirpc/gondor": "1.*", "dirpc/governance-op": "^1.0", "dirpc/grade": "1.*", "dirpc/grade-go": "3.*", "dirpc/grandet": "1.*", "dirpc/gs-beatles-route": "1.*", "dirpc/hades": "1.*", "dirpc/harmonia": "1.*", "dirpc/hermes-api-go": "1.*", "dirpc/hermesapi": "1.*", "dirpc/hertz": "1.*", "dirpc/hestia-charge": "1.*", "dirpc/hilda": "1.*", "dirpc/honeycomb": "1.*", "dirpc/horae": "1.*", "dirpc/hotspot": "1.*", "dirpc/hundun": "1.*", "dirpc/iapetos": "2.*", "dirpc/id-gen": "1.*", "dirpc/ihap-acceptor": "1.*", "dirpc/ihap-avatar": "1.*", "dirpc/ihap-dg": "0.*", "dirpc/ihap-evidence-center": "1.*", "dirpc/ihap-nego": "1.*", "dirpc/ihap-rdc-std-dispose": "1.*", "dirpc/im-open-api": "1.*", "dirpc/inner-broker": "2.*", "dirpc/inner-gateway-osim-online": "1.*", "dirpc/insurance": "2.*", "dirpc/inter-city-carpool": "1.*", "dirpc/invoice-mgr": "1.*", "dirpc/its-service": "1.*", "dirpc/ivy-auth-service": "1.*", "dirpc/jackpot": "1.*", "dirpc/janus": "1.*", "dirpc/jetfire": "1.*", "dirpc/jury": "0.*", "dirpc/k-f-compensation": "1.*", "dirpc/k-f-dmaster-dserver": "1.*", "dirpc/k-f-duse-api": "1.*", "dirpc/k-f-duse-pk": "1.*", "dirpc/k-f-safety-feature-service": "2.*", "dirpc/k-flower-bonus": "2.*", "dirpc/kcard": "3.*", "dirpc/kflower-d-master-api": "1.*", "dirpc/kfopenapi": "1.*", "dirpc/kingflower-passenger-center-go": "1.0.9", "dirpc/kingflower-performance-go": "1.*", "dirpc/koenigsegg": "1.*", "dirpc/kp-rosen-bridge": "1.*", "dirpc/kronos": "1.*", "dirpc/layer-control": "1.*", "dirpc/lbspp-proxy-api": "1.*", "dirpc/league-api": "1.*", "dirpc/loc-svr-thrift": "1.*", "dirpc/locserver-thrift": "3.*", "dirpc/locsvr": "1.*", "dirpc/long-dispatch-service": "1.*", "dirpc/mamba": "1.*", "dirpc/manhattan": "1.*", "dirpc/manhattanmarket": "1.*", "dirpc/map-ar-navigation": "1.*", "dirpc/map-di-thrift": "1.*", "dirpc/map-pickup": "1.*", "dirpc/map-pickup-engine": "1.*", "dirpc/map-point-sys-a-p-i": "1.*", "dirpc/map-point-sys-travel-assistant": "1.*", "dirpc/map-post-order-rec": "1.*", "dirpc/map-rewrite-service": "1.*", "dirpc/mapapi": "1.*", "dirpc/market-app-workshop": "1.*", "dirpc/marketing": "^1.0", "dirpc/martini-proxy": "1.*", "dirpc/member": "10.*", "dirpc/merlin": "1.*", "dirpc/micius": "1.*", "dirpc/minos": "1.*", "dirpc/minos-async": "1.*", "dirpc/misfence": "1.*", "dirpc/moon": "1.*", "dirpc/mpb-share-info": "1.*", "dirpc/msg-center": "1.*", "dirpc/msg-ctrl": "1.*", "dirpc/nacc": "1.*", "dirpc/nemesis-api": "1.*", "dirpc/nereus": "1.*", "dirpc/nereus-sdk": "0.*", "dirpc/new-taxi-internal-api": "^1.0", "dirpc/new-taxi-service": "1.*", "dirpc/newtaxi-experience-score-service": "1.*", "dirpc/newton": "1.*", "dirpc/newton-api": "1.*", "dirpc/newton-common": "1.*", "dirpc/newton-hack": "1.*", "dirpc/newton-zkc": "1.*", "dirpc/nexus": "1.*", "dirpc/night-watch5": "^1.0", "dirpc/nx4": "1.*", "dirpc/nx7": "1.*", "dirpc/oak-auth-service": "1.*", "dirpc/one-conf": "1.*", "dirpc/one-conf-api": "1.*", "dirpc/open-api-gateway": "1.*", "dirpc/openapi": "1.*", "dirpc/openapi-developer": "1.*", "dirpc/openapi-inner": "1.*", "dirpc/order-route-api": "1.*", "dirpc/order-route-feature": "1.*", "dirpc/organised-center": "1.*", "dirpc/pandora": "1.*", "dirpc/paris": "1.*", "dirpc/parkth": "1.*", "dirpc/passenger-center-go": "1.*", "dirpc/passengercenter": "0.*", "dirpc/passport": "2.*", "dirpc/pay-center-api": "1.*", "dirpc/pay-member": "10.*", "dirpc/pbd-baidu-map": "1.*", "dirpc/pbd-common": "1.*", "dirpc/pbd-meituan": "1.*", "dirpc/pbd-passenger": "1.*", "dirpc/pbd-station-bus-common": "1.*", "dirpc/pbd-station-bus-ctrip": "1.*", "dirpc/pbd-tc-im5": "1.*", "dirpc/pbd-tencent-map": "1.*", "dirpc/pbd-tencent-traffic-code": "1.*", "dirpc/pbd-tongcheng": "1.*", "dirpc/pbd-tripcloud-p-b-d": "1.*", "dirpc/performance": "1.*", "dirpc/performance-go": "^1.0", "dirpc/permier-kop": "0.*", "dirpc/pfs": "0.*", "dirpc/pharos": "1.*", "dirpc/phoenix": "1.*", "dirpc/picasso": "1.*", "dirpc/picasso-judge": "3.*", "dirpc/plutus": ">=2.3.76", "dirpc/plutus-degrade": "1.*", "dirpc/pn": "1.*", "dirpc/poi-info": "1.*", "dirpc/polaris": "1.*", "dirpc/pope-action-proxy": "1.*", "dirpc/pope-dunning": "1.*", "dirpc/pope-engine": "1.*", "dirpc/pope-p-a-p-service": "1.*", "dirpc/pope-rosen-bridge": "1.*", "dirpc/pope2-ap": "1.*", "dirpc/pope2-engine": "1.*", "dirpc/popefs": "0.*", "dirpc/porsche": "1.*", "dirpc/pre-sale": "1.*", "dirpc/pre-sale-go": "1.*", "dirpc/preorder-judge-thrift": "1.*", "dirpc/prepay": "0.*", "dirpc/prfs": "1.*", "dirpc/price-api": "1.*", "dirpc/prs": "1.*", "dirpc/quartz": "2.*", "dirpc/query-pig-bubble-discount": "1.*", "dirpc/rainbow-appeal": "1.*", "dirpc/rainbow-driver-vacation": "2.*", "dirpc/raventree": "0.*", "dirpc/res-api": "3.*", "dirpc/reverse-geo": "1.*", "dirpc/right": "0.*", "dirpc/robin": "1.*", "dirpc/robot": "0.*", "dirpc/rohan": "0.*", "dirpc/route-broker-service": "1.*", "dirpc/routebroker": "1.*", "dirpc/routebrokerservice": "0.*", "dirpc/rt-sdk": "0.*", "dirpc/rule-engine": "1.*", "dirpc/safe-cmpl": "1.*", "dirpc/scene-insurance": "1.*", "dirpc/seal": "^1.0", "dirpc/sec-guard": "2.*", "dirpc/service-link-service": "1.*", "dirpc/sfc-trip-cloud-ha-luo": "1.*", "dirpc/sill": "1.*", "dirpc/skyeyeengine": "1.*", "dirpc/skyline": "1.*", "dirpc/soraka": "1.*", "dirpc/soter": "1.*", "dirpc/speech-synthesis": "1.*", "dirpc/spruce": "1.*", "dirpc/sps": ">=1.0.72", "dirpc/sspnereus": "2.0.*", "dirpc/ssse": "^1.2.0", "dirpc/station-push": "1.*", "dirpc/student-xpanel": "1.*", "dirpc/tag-service-http": "1.*", "dirpc/talos": "1.*", "dirpc/targaryen": "1.*", "dirpc/taxi-judge": "1.*", "dirpc/themis": "2.*", "dirpc/tianji-income-weekly": "1.*", "dirpc/ticket-api": "1.*", "dirpc/ticket-price": "1.*", "dirpc/toggle": "1.*", "dirpc/traffic": "1.*", "dirpc/traffic-report": "1.*", "dirpc/transaction-go": "1.*", "dirpc/transport-score-server": "1.*", "dirpc/trip-cloud-aa": "1.*", "dirpc/trip-cloud-agent": "1.*", "dirpc/trip-cloud-aiyouwei": "1.*", "dirpc/trip-cloud-common": "1.*", "dirpc/trip-cloud-data-service": "1.*", "dirpc/trip-cloud-dongfeng": "2.*", "dirpc/trip-cloud-driver": "1.*", "dirpc/trip-cloud-guangqi": "0.*", "dirpc/trip-cloud-hermes": "1.*", "dirpc/trip-cloud-passenger": "1.*", "dirpc/trip-cloud-shouqi": "1.*", "dirpc/trip-cloud-tongcheng": "2.*", "dirpc/trip-cloud-tonggang": "1.*", "dirpc/trip-cloud-xiangdao": "0.*", "dirpc/trip-cloud-yangguang": "2.*", "dirpc/trip-cloud-yiqi": "2.*", "dirpc/tripcloud-agent-go": "1.*", "dirpc/tripcloud-d-s-hz-health": "1.*", "dirpc/tripcloud-driver-go": "1.*", "dirpc/tripcloud-hermes-go": "1.*", "dirpc/tripcloud-passenger-go": "1.*", "dirpc/tudi": "1.*", "dirpc/u-f-s": "0.*", "dirpc/unicorn": "2.*", "dirpc/uranus": "1.*", "dirpc/usce-api": "1.*", "dirpc/user-center": "3.*", "dirpc/valet-trip-cloud-e": "1.*", "dirpc/vcard": "1.*", "dirpc/veyron": "1.*", "dirpc/vision5": "1.*", "dirpc/watson": "1.*", "dirpc/weather-service": "1.*", "dirpc/webapp-gate": "1.*", "dirpc/webappapi": "2.*", "dirpc/webappv3": "1.*", "dirpc/x-z-y-f": "1.*", "dirpc/xiaoju-oil": "1.*", "dirpc/xplorer": "1.*", "disf/bill": "0.*", "disf/carpool": "0.*", "disf/carpoolrecservice": "0.*", "disf/cashier": "0.*", "disf/charon": "0.*", "disf/counter": "0.*", "disf/credit": "0.*", "disf/defensor": "0.*", "disf/dmc": "^0.9.3", "disf/dos": "0.*", "disf/driversystem": "0.*", "disf/dsig": "0.*", "disf/fenceservice": "0.*", "disf/geofence": "0.*", "disf/heatmap": "0.*", "disf/horae": "1.*", "disf/horizons": "0.*", "disf/hotspot": "0.*", "disf/iapetos": "0.*", "disf/idl": "0.*", "disf/lbspp": "0.*", "disf/mappostorderrec": "0.*", "disf/member": "0.*", "disf/msggate": "0.*", "disf/ofs": "0.*", "disf/openapi": "0.*", "disf/ordersystem": "0.*", "disf/pfs": "0.*", "disf/preorderjudge": "0.*", "disf/priceapi": "0.*", "disf/routematch": "0.*", "disf/spl": "0.*", "disf/uranus": "0.*", "disf/userinfo": "0.*", "disf/winterfell": "0.*", "disf/withdrawplatform": "0.*", "dukang/product-definition": "0.*", "dukang/property": "0.*", "elvish/elvish-lib-php": "1.*", "expert-data-assets/datasender": "2.*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "gulfstream/minos-sdk": "0.*", "lego/dirpc": "1.*", "nuwa/apollo": "1.*", "nuwa/config": "1.*", "nuwa/errhandler": "1.*", "nuwa/locker": "1.*", "nuwa/log": "1.*", "nuwa/phystrix": "1.*", "nuwa/redis": "^1.0.10", "nuwa/util": "1.*", "platform-ha/diconf": "0.*", "platform-ha/onekey-degrade": "0.*", "qiankunbag/kms": "0.*", "webapp/didi-wechat-php-sdk": "^0.3.4"}, "require-dev": {"nuwa/unit": "^1.0"}, "type": "library", "autoload": {"psr-4": {"BizLib\\": ["src"], "DrSlump\\": ["src/Utils/protobuf-php/DrSlump"]}, "classmap": ["src/Utils/pb-php", "src/Utils/Sms", "src/Utils/pb-allegro"]}, "license": ["None"], "description": "library for biz", "keywords": ["biz", "biz-lib"]}, {"name": "dirpc/action-gateway", "version": "1.0.7", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/action-gateway/action-gateway-1.0.7.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/active-brick", "version": "1.0.8", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/active-brick/dirpc-active-brick-1.0.8.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\ActiveBrick\\": ["src/", "src/Dirpc/SDK/ActiveBrick/"]}}}, {"name": "dirpc/activity-v3", "version": "1.1.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/activity-v3/activity-v3-1.1.0.tar.gz"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\ActivityV3\\": ["src/", "src/Dirpc/SDK/ActivityV3/"]}}}, {"name": "dirpc/adonis", "version": "0.1.3", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/adonis/adonis-0.1.3.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["Proprietary"], "authors": [{"name": "", "email": "", "role": "Developer"}], "description": "adonis service php client sdk", "keywords": ["DiRPC", "adonis", "sdk"], "time": "2018-07-03T00:00:00+00:00"}, {"name": "dirpc/adx", "version": "1.1.70", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/adx/dirpc-adx-1.1.70.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/athena-api", "version": "1.0.36", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/athena-api/athena-api-1.0.36.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/athena-api-kflower", "version": "1.2.71", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/athena-api-kflower/dirpc-athena-api-kflower-1.2.71.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/athena-apiv3", "version": "2.1.44", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/athena-apiv3/dirpc-athena-apiv3-2.1.44.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/athena-expect", "version": "1.1.25", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/athena-expect/dirpc-athena-expect-1.1.25.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/automarket-qrcode", "version": "1.0.5", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/automarket-qrcode/automarket-qrcode-1.0.5.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/axb-v2", "version": "4.1.7", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/axb-v2/dirpc-axb-v2-4.1.7.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\AxbV2\\": ["src/", "src/Dirpc/SDK/AxbV2/"]}}}, {"name": "dirpc/baichuan", "version": "1.9.21", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/baichuan/dirpc-baichuan-1.9.21.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/baichuan-course", "version": "1.0.11", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/baichuan-course/baichuan-course-1.0.11.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/bayze", "version": "1.0.13", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/bayze/bayze-1.0.13.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/beatles-order", "version": "1.0.3", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/beatles-order/dirpc-beatles-order-1.0.3.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\BeatlesOrder\\": ["src/", "src/Dirpc/SDK/BeatlesOrder/"]}}}, {"name": "dirpc/bifrost", "version": "1.0.7", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/bifrost/dirpc-bifrost-1.0.7.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Bifrost\\": ["src/", "src/Dirpc/SDK/Bifrost/"]}}}, {"name": "dirpc/bill-data-svr", "version": "1.1.9", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/bill-data-svr/dirpc-bill-data-svr-1.1.9.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/biz-ticket", "version": "1.0.7", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/biz-ticket/biz-ticket-1.0.7.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/biz-transaction", "version": "1.0.41", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/biz-transaction/dirpc-biz-transaction-1.0.41.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/blanka-pixie", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/blanka-pixie/blanka-pixie-1.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["Proprietary"], "authors": [{"name": "", "email": "", "role": "Developer"}], "description": "blanka-pixie service php client sdk", "keywords": ["DiRPC", "blanka-pixie", "sdk"], "time": "2018-06-01T00:00:00+00:00"}, {"name": "dirpc/bonfire", "version": "1.1.5", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/bonfire/dirpc-bonfire-1.1.5.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\bonfire\\": ["src/", "src/Dirpc/SDK/bonfire/"]}}}, {"name": "dirpc/bosp-api", "version": "1.0.7", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/bosp-api/bosp-api-1.0.7.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/bounty-task", "version": "1.2.4", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/bounty-task/bounty-task-1.2.4.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/brick", "version": "1.0.42", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/brick/dirpc-brick-1.0.42.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Brick\\": ["src/", "src/Dirpc/SDK/Brick/"]}}}, {"name": "dirpc/c-api", "version": "1.0.7", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/c-api/c-api-1.0.7.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/calcite-api", "version": "1.0.16", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/calcite-api/dirpc-calcite-api-1.0.16.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/car-partner-center-sdk", "version": "1.0.3", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/car-partner-center-sdk/dirpc-car-partner-center-sdk-1.0.3.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\CarPartnerCenterSdk\\": ["src/", "src/Dirpc/SDK/CarPartnerCenterSdk/"]}}}, {"name": "dirpc/carpool", "version": "2.1.55", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/carpool/dirpc-carpool-2.1.55.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/carpool-api", "version": "1.9.44", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/carpool-api/dirpc-carpool-api-1.9.44.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/carpool-open-api", "version": "1.1.40", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/carpool-open-api/dirpc-carpool-open-api-1.1.40.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/carpool-rec-service", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/carpool-rec-service/carpool-rec-service-1.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/cash-back", "version": "1.0.7", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/cash-back/dirpc-cash-back-1.0.7.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.6", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\CashBack\\": ["src/", "src/Dirpc/SDK/CashBack/"]}}}, {"name": "dirpc/cashier-api", "version": "1.2.18", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/cashier-api/cashier-api-1.2.18.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/cashier-tradecenter-api", "version": "1.1.0", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/cashier-tradecenter-api/dirpc-cashier-tradecenter-api-1.1.0.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\CashierTradecenterApi\\": ["src/", "src/Dirpc/SDK/CashierTradecenterApi/"]}}}, {"name": "dirpc/cedar-s-f-c-service", "version": "1.0.3", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/cedar-s-f-c-service/dirpc-cedar-s-f-c-service-1.0.3.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\CedarSFCService\\": ["src/", "src/Dirpc/SDK/CedarSFCService/"]}}}, {"name": "dirpc/ceres-api", "version": "2.0.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/ceres-api/ceres-api-2.0.1.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/clearing-api", "version": "1.0.3", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/clearing-api/dirpc-clearing-api-1.0.3.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\ClearingApi\\": ["src/", "src/Dirpc/SDK/ClearingApi/"]}}}, {"name": "dirpc/combined-travel", "version": "1.1.4", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/combined-travel/dirpc-combined-travel-1.1.4.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\CombinedTravel\\": ["src/", "src/Dirpc/SDK/CombinedTravel/"]}}}, {"name": "dirpc/common-api", "version": "0.3.11", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/common-api/common-api-0.3.11.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/common-plat-coupon", "version": "1.0.52", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/common-plat-coupon/dirpc-common-plat-coupon-1.0.52.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/commonplat-cashier", "version": "2.7.9", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/commonplat-cashier/dirpc-commonplat-cashier-2.7.9.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/commonplat-sc-invoice", "version": "1.0.9", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/commonplat-sc-invoice/dirpc-commonplat-sc-invoice-1.0.9.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\CommonplatScInvoice\\": ["src/", "src/Dirpc/SDK/CommonplatScInvoice/"]}}}, {"name": "dirpc/commonplatchunxiao", "version": "1.0.13", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/commonplatchunxiao/commonplatchunxiao-1.0.13.tar.gz"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\CommonPlatChunXiao\\": ["src/", "src/Dirpc/SDK/CommonPlatChunXiao/"]}}}, {"name": "dirpc/compensation", "version": "1.0.53", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/compensation/dirpc-compensation-1.0.53.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/consec", "version": "1.0.03", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/consec/consec-1.0.03.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/consec-service", "version": "1.0.12", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/consec-service/dirpc-consec-service-1.0.12.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/coupon", "version": "1.1.27", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/coupon/dirpc-coupon-1.1.27.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/crius", "version": "2.1.4", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/crius/dirpc-crius-2.1.4.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/csi", "version": "1.0.5", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/csi/csi-1.0.5.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/customcar", "version": "1.1.1", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/customcar/dirpc-customcar-1.1.1.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.6", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\CustomCar\\": ["src/", "src/Dirpc/SDK/CustomCar/"]}}}, {"name": "dirpc/cyberset", "version": "1.2.2", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/cyberset/dirpc-cyberset-1.2.2.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Cyberset\\": ["src/", "src/Dirpc/SDK/Cyberset/"]}}}, {"name": "dirpc/d-master-d-server", "version": "1.2.73", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/d-master-d-server/dirpc-d-master-d-server-1.2.73.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/d-member", "version": "10.0.1", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/d-member/dirpc-d-member-10.0.1.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\DMember\\": ["src/", "src/Dirpc/SDK/DMember/"]}}}, {"name": "dirpc/dai-jia-kop", "version": "1.0.7", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dai-jia-kop/dai-jia-kop-1.0.7.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/dape-driver-price", "version": "0.0.51", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dape-driver-price/dirpc-dape-driver-price-0.0.51.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/dape-dynamic-price", "version": "0.1.40", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dape-dynamic-price/dirpc-dape-dynamic-price-0.1.40.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/dape-gateway", "version": "2.4.14", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dape-gateway/dirpc-dape-gateway-2.4.14.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/dcmp", "version": "0.1.0", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dcmp/dirpc-dcmp-0.1.0.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\dcmp\\": ["src/", "src/Dirpc/SDK/dcmp/"]}}}, {"name": "dirpc/dcmp-sdk", "version": "2.5.0", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dcmp-sdk/dirpc-dcmp-sdk-2.5.0.zip"}, "require": {"dirpc/dcmp": ">=0.1.0", "lego/dirpc": ">=0.0.20", "nuwa/apollo": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["Proprietary"], "authors": [{"name": "guf<PERSON><PERSON>e", "email": "<EMAIL>", "role": "Developer"}], "description": "dcmp php client sdk", "keywords": ["DIRPC", "dcmp", "sdk"], "time": "2023-10-25T00:00:00+00:00"}, {"name": "dirpc/dcoin", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dcoin/dcoin-1.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/ddpay", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/ddpay/ddpay-1.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/defender", "version": "1.3.5", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/defender/defender-1.3.5.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/defendernew", "version": "1.0.12", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/defendernew/dirpc-defendernew-1.0.12.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/defensor", "version": "2.0.24", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/defensor/dirpc-defensor-2.0.24.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/dfs", "version": "0.1.6", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dfs/dfs-0.1.6.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "dfs", "email": "<EMAIL>"}, {"name": "dfs", "email": "<EMAIL>"}, {"name": "dfs", "email": "<EMAIL>"}, {"name": "dfs", "email": "<EMAIL>"}], "description": "dfs service php client sdk", "keywords": ["DiRPC", "dfs"]}, {"name": "dirpc/discount-cal", "version": "1.0.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/discount-cal/discount-cal-1.0.2.tar.gz"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\DiscountCal\\": ["src/", "src/Dirpc/SDK/DiscountCal/"]}}}, {"name": "dirpc/dispatch-judge", "version": "1.0.124", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dispatch-judge/dirpc-dispatch-judge-1.0.124.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/ditag-service", "version": "1.0.0", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/ditag-service/dirpc-ditag-service-1.0.0.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\DitagService\\": ["src/", "src/Dirpc/SDK/DitagService/"]}}}, {"name": "dirpc/dlock", "version": "2.0.4", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dlock/dirpc-dlock-2.0.4.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Dlock\\": ["src/", "src/Dirpc/SDK/Dlock/"]}}}, {"name": "dirpc/dmaster-push", "version": "1.0.9", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dmaster-push/dirpc-dmaster-push-1.0.9.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\DmasterPush\\": ["src/", "src/Dirpc/SDK/DmasterPush/"]}}}, {"name": "dirpc/dmasterapi-sdk", "version": "1.0.5", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dmasterapi-sdk/dmasterapi-sdk-1.0.5.tar.gz"}, "require": {"lego/dirpc": ">=0.0.11"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "dongzerun", "email": "<EMAIL>"}], "description": "dirpc dmasterapi service php client", "keywords": ["dirpc-dmasterapi", "d<PERSON><PERSON><PERSON>"]}, {"name": "dirpc/dmasterapiv2", "version": "1.0.57", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dmasterapiv2/dirpc-dmasterapiv2-1.0.57.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\DmasterApiv2\\": ["src/", "src/Dirpc/SDK/DmasterApiv2/"]}}}, {"name": "dirpc/dmc", "version": "2.2.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dmc/dmc-2.2.2.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/doraemon", "version": "1.0.19", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/doraemon/dirpc-doraemon-1.0.19.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Doraemon\\": ["src/", "src/Dirpc/SDK/Doraemon/"]}}}, {"name": "dirpc/dorami", "version": "1.1.2", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dorami/dirpc-dorami-1.1.2.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Dorami\\": ["src/", "src/Dirpc/SDK/Dorami/"]}}}, {"name": "dirpc/dos", "version": "1.0.226", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dos/dirpc-dos-1.0.226.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/dp-relation", "version": "1.0.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dp-relation/dp-relation-1.0.1.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/dpub-service", "version": "1.0.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dpub-service/dpub-service-1.0.1.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/driver", "version": "1.0.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/driver/driver-1.0.2.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/driver-api", "version": "2.1.67", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/driver-api/dirpc-driver-api-2.1.67.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\DriverApi\\": ["src/", "src/Dirpc/SDK/DriverApi/"]}}}, {"name": "dirpc/driver-auditor-go", "version": "1.0.10", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/driver-auditor-go/dirpc-driver-auditor-go-1.0.10.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\DriverAuditorGo\\": ["src/", "src/Dirpc/SDK/DriverAuditorGo/"]}}}, {"name": "dirpc/driver-bill", "version": "1.0.41", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/driver-bill/dirpc-driver-bill-1.0.41.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\DriverBill\\": ["src/", "src/Dirpc/SDK/DriverBill/"]}}}, {"name": "dirpc/driver-card", "version": "1.1.26", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/driver-card/dirpc-driver-card-1.1.26.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\DriverCard\\": ["src/", "src/Dirpc/SDK/DriverCard/"]}}}, {"name": "dirpc/driver-center-go", "version": "1.0.19", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/driver-center-go/dirpc-driver-center-go-1.0.19.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\DriverCenterGo\\": ["src/", "src/Dirpc/SDK/DriverCenterGo/"]}}}, {"name": "dirpc/driver-certify", "version": "1.0.4", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/driver-certify/driver-certify-1.0.4.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/driver-device", "version": "1.0.17", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/driver-device/dirpc-driver-device-1.0.17.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\DriverDevice\\": ["src/", "src/Dirpc/SDK/DriverDevice/"]}}}, {"name": "dirpc/driver-status", "version": "1.0.1", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/driver-status/dirpc-driver-status-1.0.1.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\DriverStatus\\": ["src/", "src/Dirpc/SDK/DriverStatus/"]}}}, {"name": "dirpc/driver-task", "version": "1.0.16", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/driver-task/driver-task-1.0.16.tar.gz"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\DriverTask\\": ["src/", "src/Dirpc/SDK/DriverTask/"]}}}, {"name": "dirpc/driver-trips", "version": "1.0.12", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/driver-trips/dirpc-driver-trips-1.0.12.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\DriverTrips\\": ["src/", "src/Dirpc/SDK/DriverTrips/"]}}}, {"name": "dirpc/driver-trips-go", "version": "1.0.27", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/driver-trips-go/dirpc-driver-trips-go-1.0.27.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\DriverTripsGo\\": ["src/", "src/Dirpc/SDK/DriverTripsGo/"]}}}, {"name": "dirpc/driver-workbench-go", "version": "1.0.19", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/driver-workbench-go/dirpc-driver-workbench-go-1.0.19.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\DriverWorkbenchGo\\": ["src/", "src/Dirpc/SDK/DriverWorkbenchGo/"]}}}, {"name": "dirpc/dufe-api", "version": "1.0.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dufe-api/dufe-api-1.0.1.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/dufe-data-contribution", "version": "1.0.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dufe-data-contribution/dufe-data-contribution-1.0.1.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/dufe-storage", "version": "1.0.7", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dufe-storage/dirpc-dufe-storage-1.0.7.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/dupsuds", "version": "1.0.43", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/dupsuds/dirpc-dupsuds-1.0.43.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/duse-api", "version": "2.3.35", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/duse-api/dirpc-duse-api-2.3.35.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/duse-vrp", "version": "1.1.10", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/duse-vrp/dirpc-duse-vrp-1.1.10.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/endowment-auth-service", "version": "1.1.6", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/endowment-auth-service/dirpc-endowment-auth-service-1.1.6.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\EndowmentAuthService\\": ["src/", "src/Dirpc/SDK/EndowmentAuthService/"]}}}, {"name": "dirpc/energy-p-o-i-station-api", "version": "1.0.8", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/energy-p-o-i-station-api/dirpc-energy-p-o-i-station-api-1.0.8.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\EnergyPOIStationApi\\": ["src/", "src/Dirpc/SDK/EnergyPOIStationApi/"]}}}, {"name": "dirpc/engine-martini-proxy", "version": "1.0.6", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/engine-martini-proxy/engine-martini-proxy-1.0.6.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/epowerservice", "version": "1.1.4", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/epowerservice/epowerservice-1.1.4.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/es-open-a-p-i", "version": "1.0.2", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/es-open-a-p-i/dirpc-es-open-a-p-i-1.0.2.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\EsOpenAPI\\": ["src/", "src/Dirpc/SDK/EsOpenAPI/"]}}}, {"name": "dirpc/estimate-decision", "version": "1.1.41", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/estimate-decision/dirpc-estimate-decision-1.1.41.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/ether-access-service", "version": "1.1.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/ether-access-service/ether-access-service-1.1.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/euler", "version": "1.0.76", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/euler/dirpc-euler-1.0.76.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/event-render-service", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/event-render-service/event-render-service-1.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/f-p-s-producer", "version": "1.0.4", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/f-p-s-producer/dirpc-f-p-s-producer-1.0.4.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/feature-service", "version": "1.0.14", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/feature-service/feature-service-1.0.14.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/feeds", "version": "1.0.3", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/feeds/dirpc-feeds-1.0.3.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Feeds\\": ["src/", "src/Dirpc/SDK/Feeds/"]}}}, {"name": "dirpc/ferrari", "version": "1.0.17", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/ferrari/dirpc-ferrari-1.0.17.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Ferrari\\": ["src/", "src/Dirpc/SDK/Ferrari/"]}}}, {"name": "dirpc/finance-ddpay-usercenter", "version": "1.0.11", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/finance-ddpay-usercenter/dirpc-finance-ddpay-usercenter-1.0.11.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/finance-vision", "version": "1.0.3", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/finance-vision/finance-vision-1.0.3.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/full-dispatch", "version": "1.3.0", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/full-dispatch/dirpc-full-dispatch-1.3.0.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\FullDispatch\\": ["src/", "src/Dirpc/SDK/FullDispatch/"]}}}, {"name": "dirpc/gandalf", "version": "1.0.6", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/gandalf/gandalf-1.0.6.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/gauss", "version": "1.0.49", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/gauss/dirpc-gauss-1.0.49.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/gcs-sdk", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/gcs-sdk/gcs-sdk-1.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["Proprietary"], "authors": [{"name": "", "email": "", "role": "Developer"}], "description": "g-c-s service php client sdk", "keywords": ["DiRPC", "gcs", "sdk"], "time": "2018-07-15T00:00:00+00:00"}, {"name": "dirpc/geo-fence-http", "version": "1.0.3", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/geo-fence-http/geo-fence-http-1.0.3.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/geo-fence-map", "version": "1.0.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/geo-fence-map/geo-fence-map-1.0.2.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/geofence-sdk", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/geofence-sdk/geofence-sdk-1.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "geofence", "email": "<EMAIL>"}], "description": "geofence service php client sdk", "keywords": ["DiRPC", "geofence"]}, {"name": "dirpc/getfeatureservicev2", "version": "1.0.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/getfeatureservicev2/getfeatureservicev2-1.0.2.tar.gz"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\GetFeatureServiceV2\\": ["src/", "src/Dirpc/SDK/GetFeatureServiceV2/"]}}}, {"name": "dirpc/go-member", "version": "10.0.1", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/go-member/dirpc-go-member-10.0.1.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/gondor", "version": "1.0.3", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/gondor/dirpc-gondor-1.0.3.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/governance-op", "version": "1.0.33", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/governance-op/dirpc-governance-op-1.0.33.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\GovernanceOp\\": ["src/", "src/Dirpc/SDK/GovernanceOp/"]}}}, {"name": "dirpc/grade", "version": "1.0.4", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/grade/dirpc-grade-1.0.4.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.6", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Grade\\": ["src/", "src/Dirpc/SDK/Grade/"]}}}, {"name": "dirpc/grade-go", "version": "3.1.7", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/grade-go/dirpc-grade-go-3.1.7.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\GradeGo\\": ["src/", "src/Dirpc/SDK/GradeGo/"]}}}, {"name": "dirpc/grandet", "version": "1.0.13", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/grandet/dirpc-grandet-1.0.13.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Grandet\\": ["src/", "src/Dirpc/SDK/Grandet/"]}}}, {"name": "dirpc/gs-beatles-route", "version": "1.1.7", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/gs-beatles-route/dirpc-gs-beatles-route-1.1.7.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\GsBeatlesRoute\\": ["src/", "src/Dirpc/SDK/GsBeatlesRoute/"]}}}, {"name": "dirpc/hades", "version": "1.0.4", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/hades/dirpc-hades-1.0.4.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Hades\\": ["src/", "src/Dirpc/SDK/Hades/"]}}}, {"name": "dirpc/harmonia", "version": "1.0.1", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/harmonia/dirpc-harmonia-1.0.1.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Harmonia\\": ["src/", "src/Dirpc/SDK/Harmonia/"]}}}, {"name": "dirpc/hermes-api-go", "version": "1.0.17", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/hermes-api-go/dirpc-hermes-api-go-1.0.17.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\HermesAPIGo\\": ["src/", "src/Dirpc/SDK/HermesAPIGo/"]}}}, {"name": "dirpc/hermesapi", "version": "1.0.51", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/hermesapi/dirpc-hermesapi-1.0.51.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\HermesApi\\": ["src/", "src/Dirpc/SDK/HermesApi/"]}}}, {"name": "dirpc/hertz", "version": "1.0.77", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/hertz/dirpc-hertz-1.0.77.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Hertz\\": ["src/", "src/Dirpc/SDK/Hertz/"]}}}, {"name": "dirpc/hestia-charge", "version": "1.1.14", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/hestia-charge/dirpc-hestia-charge-1.1.14.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/hilda", "version": "1.0.69", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/hilda/dirpc-hilda-1.0.69.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Hilda\\": ["src/", "src/Dirpc/SDK/Hilda/"]}}}, {"name": "dirpc/honeycomb", "version": "1.0.6", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/honeycomb/honeycomb-1.0.6.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/horae", "version": "1.0.22", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/horae/dirpc-horae-1.0.22.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/hotspot", "version": "1.2.3", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/hotspot/dirpc-hotspot-1.2.3.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/hubble", "version": "2.0.26", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/hubble/dirpc-hubble-2.0.26.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Hubble\\": ["src/", "src/Dirpc/SDK/Hubble/"]}}}, {"name": "dirpc/hundun", "version": "1.0.33", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/hundun/dirpc-hundun-1.0.33.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Hundun\\": ["src/", "src/Dirpc/SDK/Hundun/"]}}}, {"name": "dirpc/iapetos", "version": "2.0.34", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/iapetos/dirpc-iapetos-2.0.34.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/id-gen", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/id-gen/id-gen-1.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/ihap-acceptor", "version": "1.1.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/ihap-acceptor/ihap-acceptor-1.1.1.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/ihap-avatar", "version": "1.10.20", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/ihap-avatar/ihap-avatar-1.10.20.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/ihap-dg", "version": "0.0.60", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/ihap-dg/dirpc-ihap-dg-0.0.60.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/ihap-evidence-center", "version": "1.0.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/ihap-evidence-center/ihap-evidence-center-1.0.1.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/ihap-nego", "version": "1.0.23", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/ihap-nego/dirpc-ihap-nego-1.0.23.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\IhapNego\\": ["src/", "src/Dirpc/SDK/IhapNego/"]}}}, {"name": "dirpc/ihap-rdc-std-dispose", "version": "1.0.26", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/ihap-rdc-std-dispose/ihap-rdc-std-dispose-1.0.26.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/im-open-api", "version": "1.0.8", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/im-open-api/dirpc-im-open-api-1.0.8.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\ImOpenApi\\": ["src/", "src/Dirpc/SDK/ImOpenApi/"]}}}, {"name": "dirpc/inner-broker", "version": "2.5.0", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/inner-broker/dirpc-inner-broker-2.5.0.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\InnerBroker\\": ["src/", "src/Dirpc/SDK/InnerBroker/"]}}}, {"name": "dirpc/inner-gateway-osim-online", "version": "1.0.19", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/inner-gateway-osim-online/dirpc-inner-gateway-osim-online-1.0.19.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\InnerGatewayOsimOnline\\": ["src/", "src/Dirpc/SDK/InnerGatewayOsimOnline/"]}}}, {"name": "dirpc/insurance", "version": "2.0.30", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/insurance/dirpc-insurance-2.0.30.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Insurance\\": ["src/", "src/Dirpc/SDK/Insurance/"]}}}, {"name": "dirpc/inter-city-carpool", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/inter-city-carpool/inter-city-carpool-1.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/intercity-foras", "version": "1.0.26", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/intercity-foras/dirpc-intercity-foras-1.0.26.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\IntercityForas\\": ["src/", "src/Dirpc/SDK/IntercityForas/"]}}}, {"name": "dirpc/invoice-mgr", "version": "1.0.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/invoice-mgr/invoice-mgr-1.0.2.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/its-service", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/its-service/its-service-1.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/ivy-auth-service", "version": "1.1.4", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/ivy-auth-service/dirpc-ivy-auth-service-1.1.4.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\IvyAuthService\\": ["src/", "src/Dirpc/SDK/IvyAuthService/"]}}}, {"name": "dirpc/jackpot", "version": "1.0.96", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/jackpot/dirpc-jackpot-1.0.96.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Jackpot\\": ["src/", "src/Dirpc/SDK/Jackpot/"]}}}, {"name": "dirpc/janus", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/janus/janus-1.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/jetfire", "version": "1.0.5", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/jetfire/jetfire-1.0.5.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/jury", "version": "0.1.5", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/jury/jury-0.1.5.tar.gz"}, "require": {"lego/dirpc": ">=0.0.11"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "l<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "dirpc jury service php client", "keywords": ["dirpc-jury", "jury"]}, {"name": "dirpc/k-f-compensation", "version": "1.0.3", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/k-f-compensation/dirpc-k-f-compensation-1.0.3.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\KFCompensation\\": ["src/", "src/Dirpc/SDK/KFCompensation/"]}}}, {"name": "dirpc/k-f-dmaster-dserver", "version": "1.0.0", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/k-f-dmaster-dserver/dirpc-k-f-dmaster-dserver-1.0.0.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/k-f-duse-api", "version": "1.0.54", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/k-f-duse-api/dirpc-k-f-duse-api-1.0.54.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/k-f-duse-pk", "version": "1.0.49", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/k-f-duse-pk/dirpc-k-f-duse-pk-1.0.49.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/k-f-safety-feature-service", "version": "2.0.6", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/k-f-safety-feature-service/dirpc-k-f-safety-feature-service-2.0.6.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\KFSafetyFeatureService\\": ["src/", "src/Dirpc/SDK/KFSafetyFeatureService/"]}}}, {"name": "dirpc/k-flower-bonus", "version": "2.0.5", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/k-flower-bonus/dirpc-k-flower-bonus-2.0.5.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\KFlowerBonus\\": ["src/", "src/Dirpc/SDK/KFlowerBonus/"]}}}, {"name": "dirpc/kcard", "version": "3.3.80", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/kcard/dirpc-kcard-3.3.80.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Kcard\\": ["src/", "src/Dirpc/SDK/Kcard/"]}}}, {"name": "dirpc/kflower-d-master-api", "version": "1.1.6", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/kflower-d-master-api/dirpc-kflower-d-master-api-1.1.6.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/kfopenapi", "version": "1.0.16", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/kfopenapi/dirpc-kfopenapi-1.0.16.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\KfOpenapi\\": ["src/", "src/Dirpc/SDK/KfOpenapi/"]}}}, {"name": "dirpc/kingflower-passenger-center-go", "version": "1.0.9", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/kingflower-passenger-center-go/dirpc-kingflower-passenger-center-go-1.0.9.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\KingflowerPassengerCenterGo\\": ["src/", "src/Dirpc/SDK/KingflowerPassengerCenterGo/"]}}}, {"name": "dirpc/kingflower-performance-go", "version": "1.0.4", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/kingflower-performance-go/dirpc-kingflower-performance-go-1.0.4.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\KingflowerPerformanceGo\\": ["src/", "src/Dirpc/SDK/KingflowerPerformanceGo/"]}}}, {"name": "dirpc/kms", "version": "0.0.6", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/kms/kms-0.0.6.tar.gz"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Kms\\": ["src/", "src/Dirpc/SDK/Kms/"]}}}, {"name": "dirpc/koenigsegg", "version": "1.0.5", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/koenigsegg/koenigsegg-1.0.5.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/kp-rosen-bridge", "version": "1.1.4", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/kp-rosen-bridge/dirpc-kp-rosen-bridge-1.1.4.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/kronos", "version": "1.0.4", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/kronos/kronos-1.0.4.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["Proprietary"], "authors": [{"name": "", "email": "", "role": "Developer"}], "description": "kronos service php client sdk", "keywords": ["DiRPC", "kronos", "sdk"], "time": "2018-05-28T00:00:00+00:00"}, {"name": "dirpc/layer-control", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/layer-control/layer-control-1.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["Proprietary"], "authors": [{"name": "", "email": "", "role": "Developer"}], "description": "layer-control service php client sdk", "keywords": ["DiRPC", "layer-control", "sdk"], "time": "2018-05-29T00:00:00+00:00"}, {"name": "dirpc/lbspp-proxy-api", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/lbspp-proxy-api/lbspp-proxy-api-1.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/league-api", "version": "1.3.7", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/league-api/dirpc-league-api-1.3.7.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\LeagueApi\\": ["src/", "src/Dirpc/SDK/LeagueApi/"]}}}, {"name": "dirpc/loc-svr-thrift", "version": "1.0.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/loc-svr-thrift/loc-svr-thrift-1.0.1.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/locserver-thrift", "version": "3.0.0", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/locserver-thrift/dirpc-locserver-thrift-3.0.0.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/locsvr", "version": "1.0.7", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/locsvr/locsvr-1.0.7.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/long-dispatch-service", "version": "1.0.14", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/long-dispatch-service/long-dispatch-service-1.0.14.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/mamba", "version": "1.0.157", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/mamba/dirpc-mamba-1.0.157.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Mamba\\": ["src/", "src/Dirpc/SDK/Mamba/"]}}}, {"name": "dirpc/manhattan", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/manhattan/manhattan-1.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["Proprietary"], "authors": [{"name": "", "email": "", "role": "Developer"}], "description": "manhattan service php client sdk", "keywords": ["DiRPC", "manhattan", "sdk"], "time": "2018-05-15T00:00:00+00:00"}, {"name": "dirpc/manhattanmarket", "version": "1.1.8", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/manhattanmarket/manhattanmarket-1.1.8.tar.gz"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\ManhattanMarket\\": ["src/", "src/Dirpc/SDK/ManhattanMarket/"]}}}, {"name": "dirpc/map-ar-navigation", "version": "1.0.13", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/map-ar-navigation/map-ar-navigation-1.0.13.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/map-di-thrift", "version": "1.0.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/map-di-thrift/map-di-thrift-1.0.2.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/map-pickup", "version": "1.0.7", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/map-pickup/map-pickup-1.0.7.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/map-pickup-engine", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/map-pickup-engine/map-pickup-engine-1.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/map-point-sys-a-p-i", "version": "1.1.36", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/map-point-sys-a-p-i/dirpc-map-point-sys-a-p-i-1.1.36.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\MapPointSysAPI\\": ["src/", "src/Dirpc/SDK/MapPointSysAPI/"]}}}, {"name": "dirpc/map-point-sys-travel-assistant", "version": "1.0.61", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/map-point-sys-travel-assistant/map-point-sys-travel-assistant-1.0.61.tar.gz"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\MapPointSysTravelAssistant\\": ["src/", "src/Dirpc/SDK/MapPointSysTravelAssistant/"]}}}, {"name": "dirpc/map-post-order-rec", "version": "1.0.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/map-post-order-rec/map-post-order-rec-1.0.2.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/map-rewrite-service", "version": "1.0.4", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/map-rewrite-service/dirpc-map-rewrite-service-1.0.4.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\MapRewriteService\\": ["src/", "src/Dirpc/SDK/MapRewriteService/"]}}}, {"name": "dirpc/mapapi", "version": "1.0.23", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/mapapi/mapapi-1.0.23.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/market-app-workshop", "version": "1.1.80", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/market-app-workshop/dirpc-market-app-workshop-1.1.80.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\MarketAppWorkshop\\": ["src/", "src/Dirpc/SDK/MarketAppWorkshop/"]}}}, {"name": "dirpc/marketing", "version": "1.0.9", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/marketing/dirpc-marketing-1.0.9.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Marketing\\": ["src/", "src/Dirpc/SDK/Marketing/"]}}}, {"name": "dirpc/martini-proxy", "version": "1.3.19", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/martini-proxy/dirpc-martini-proxy-1.3.19.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/member", "version": "10.0.1", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/member/dirpc-member-10.0.1.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/merlin", "version": "1.0.35", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/merlin/dirpc-merlin-1.0.35.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Merlin\\": ["src/", "src/Dirpc/SDK/Merlin/"]}}}, {"name": "dirpc/micius", "version": "1.0.15", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/micius/dirpc-micius-1.0.15.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.6", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Micius\\": ["src/", "src/Dirpc/SDK/Micius/"]}}}, {"name": "dirpc/minos", "version": "1.0.9", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/minos/minos-1.0.9.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/minos-async", "version": "1.0.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/minos-async/minos-async-1.0.2.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/misfence", "version": "1.0.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/misfence/misfence-1.0.2.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/moon", "version": "1.0.3", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/moon/dirpc-moon-1.0.3.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Moon\\": ["src/", "src/Dirpc/SDK/Moon/"]}}}, {"name": "dirpc/mpb-share-info", "version": "1.3.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/mpb-share-info/mpb-share-info-1.3.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/msg-center", "version": "1.0.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/msg-center/msg-center-1.0.1.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/msg-ctrl", "version": "1.1.8", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/msg-ctrl/msg-ctrl-1.1.8.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/muses", "version": "1.0.14", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/muses/dirpc-muses-1.0.14.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Muses\\": ["src/", "src/Dirpc/SDK/Muses/"]}}}, {"name": "dirpc/nacc", "version": "1.1.3", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/nacc/dirpc-nacc-1.1.3.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/nemesis-api", "version": "1.0.3", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/nemesis-api/nemesis-api-1.0.3.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/nereus", "version": "1.1.2", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/nereus/dirpc-nereus-1.1.2.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\nereus\\": ["src/", "src/Dirpc/SDK/nereus/"]}}}, {"name": "dirpc/nereus-sdk", "version": "0.2.10", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/nereus-sdk/nereus-sdk-0.2.10.tar.gz"}, "require": {"lego/dirpc": ">=0.0.20"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["Proprietary"], "authors": [{"name": "wang<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "nereus service php client sdk", "keywords": ["DiRPC", "nereus", "sdk"], "time": "2017-12-25T00:00:00+00:00"}, {"name": "dirpc/new-taxi-internal-api", "version": "1.0.6", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/new-taxi-internal-api/dirpc-new-taxi-internal-api-1.0.6.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\NewTaxiInternalApi\\": ["src/", "src/Dirpc/SDK/NewTaxiInternalApi/"]}}}, {"name": "dirpc/new-taxi-service", "version": "1.1.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/new-taxi-service/new-taxi-service-1.1.2.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/newtaxi-experience-score-service", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/newtaxi-experience-score-service/newtaxi-experience-score-service-1.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/newton", "version": "1.0.6", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/newton/newton-1.0.6.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/newton-api", "version": "1.0.10", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/newton-api/newton-api-1.0.10.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/newton-common", "version": "1.1.84", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/newton-common/dirpc-newton-common-1.1.84.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/newton-hack", "version": "1.0.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/newton-hack/newton-hack-1.0.2.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/newton-zkc", "version": "1.0.7", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/newton-zkc/newton-zkc-1.0.7.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/nexus", "version": "1.0.1", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/nexus/dirpc-nexus-1.0.1.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/night-watch5", "version": "1.0.99", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/night-watch5/dirpc-night-watch5-1.0.99.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\NightWatch5\\": ["src/", "src/Dirpc/SDK/NightWatch5/"]}}}, {"name": "dirpc/nx4", "version": "1.0.39", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/nx4/dirpc-nx4-1.0.39.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/nx7", "version": "1.0.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/nx7/nx7-1.0.2.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/oak-auth-service", "version": "1.0.6", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/oak-auth-service/dirpc-oak-auth-service-1.0.6.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\OakAuthService\\": ["src/", "src/Dirpc/SDK/OakAuthService/"]}}}, {"name": "dirpc/one-conf", "version": "1.0.4", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/one-conf/dirpc-one-conf-1.0.4.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\OneConf\\": ["src/", "src/Dirpc/SDK/OneConf/"]}}}, {"name": "dirpc/one-conf-api", "version": "1.0.3", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/one-conf-api/one-conf-api-1.0.3.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/open-api-gateway", "version": "1.0.9", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/open-api-gateway/dirpc-open-api-gateway-1.0.9.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/openapi", "version": "1.0.10", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/openapi/dirpc-openapi-1.0.10.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Openapi\\": ["src/", "src/Dirpc/SDK/Openapi/"]}}}, {"name": "dirpc/openapi-developer", "version": "1.0.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/openapi-developer/openapi-developer-1.0.1.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/openapi-inner", "version": "1.1.1", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/openapi-inner/dirpc-openapi-inner-1.1.1.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/order-route-api", "version": "1.1.33", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/order-route-api/dirpc-order-route-api-1.1.33.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\OrderRouteApi\\": ["src/", "src/Dirpc/SDK/OrderRouteApi/"]}}}, {"name": "dirpc/order-route-feature", "version": "1.1.32", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/order-route-feature/dirpc-order-route-feature-1.1.32.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/organised-center", "version": "1.2.1", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/organised-center/dirpc-organised-center-1.2.1.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\OrganisedCenter\\": ["src/", "src/Dirpc/SDK/OrganisedCenter/"]}}}, {"name": "dirpc/pandora", "version": "1.0.72", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pandora/dirpc-pandora-1.0.72.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/paris", "version": "1.0.8", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/paris/dirpc-paris-1.0.8.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/parkth", "version": "1.0.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/parkth/parkth-1.0.2.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/passenger-center-go", "version": "1.0.65", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/passenger-center-go/dirpc-passenger-center-go-1.0.65.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PassengerCenterGo\\": ["src/", "src/Dirpc/SDK/PassengerCenterGo/"]}}}, {"name": "dirpc/passengercenter", "version": "0.0.51", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/passengercenter/dirpc-passengercenter-0.0.51.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PassengerCenter\\": ["src/", "src/Dirpc/SDK/PassengerCenter/"]}}}, {"name": "dirpc/passport", "version": "2.0.55", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/passport/dirpc-passport-2.0.55.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/pay-center-api", "version": "1.0.110", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pay-center-api/dirpc-pay-center-api-1.0.110.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PayCenterApi\\": ["src/", "src/Dirpc/SDK/PayCenterApi/"]}}}, {"name": "dirpc/pay-member", "version": "10.0.0", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pay-member/dirpc-pay-member-10.0.0.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PayMember\\": ["src/", "src/Dirpc/SDK/PayMember/"]}}}, {"name": "dirpc/pbd-baidu-map", "version": "1.3.1", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pbd-baidu-map/dirpc-pbd-baidu-map-1.3.1.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/pbd-common", "version": "1.0.7", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pbd-common/dirpc-pbd-common-1.0.7.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PbdCommon\\": ["src/", "src/Dirpc/SDK/PbdCommon/"]}}}, {"name": "dirpc/pbd-meituan", "version": "1.1.6", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pbd-meituan/dirpc-pbd-meituan-1.1.6.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PbdMeituan\\": ["src/", "src/Dirpc/SDK/PbdMeituan/"]}}}, {"name": "dirpc/pbd-passenger", "version": "1.0.7", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pbd-passenger/dirpc-pbd-passenger-1.0.7.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PbdPassenger\\": ["src/", "src/Dirpc/SDK/PbdPassenger/"]}}}, {"name": "dirpc/pbd-station-bus-common", "version": "1.0.1", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pbd-station-bus-common/dirpc-pbd-station-bus-common-1.0.1.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PbdStationBusCommon\\": ["src/", "src/Dirpc/SDK/PbdStationBusCommon/"]}}}, {"name": "dirpc/pbd-station-bus-ctrip", "version": "1.0.4", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pbd-station-bus-ctrip/dirpc-pbd-station-bus-ctrip-1.0.4.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PbdStationBusCtrip\\": ["src/", "src/Dirpc/SDK/PbdStationBusCtrip/"]}}}, {"name": "dirpc/pbd-tc-im5", "version": "1.0.1", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pbd-tc-im5/dirpc-pbd-tc-im5-1.0.1.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PbdTcIm5\\": ["src/", "src/Dirpc/SDK/PbdTcIm5/"]}}}, {"name": "dirpc/pbd-tencent-map", "version": "1.0.14", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pbd-tencent-map/dirpc-pbd-tencent-map-1.0.14.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PbdTencentMap\\": ["src/", "src/Dirpc/SDK/PbdTencentMap/"]}}}, {"name": "dirpc/pbd-tencent-traffic-code", "version": "1.0.3", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pbd-tencent-traffic-code/dirpc-pbd-tencent-traffic-code-1.0.3.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PbdTencentTrafficCode\\": ["src/", "src/Dirpc/SDK/PbdTencentTrafficCode/"]}}}, {"name": "dirpc/pbd-tongcheng", "version": "1.0.2", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pbd-tongcheng/dirpc-pbd-tongcheng-1.0.2.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PbdTongcheng\\": ["src/", "src/Dirpc/SDK/PbdTongcheng/"]}}}, {"name": "dirpc/pbd-tripcloud-p-b-d", "version": "1.0.13", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pbd-tripcloud-p-b-d/dirpc-pbd-tripcloud-p-b-d-1.0.13.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PbdTripcloudPBD\\": ["src/", "src/Dirpc/SDK/PbdTripcloudPBD/"]}}}, {"name": "dirpc/performance", "version": "1.0.24", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/performance/dirpc-performance-1.0.24.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/performance-go", "version": "1.0.3", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/performance-go/dirpc-performance-go-1.0.3.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PerformanceGo\\": ["src/", "src/Dirpc/SDK/PerformanceGo/"]}}}, {"name": "dirpc/permier-kop", "version": "0.1.5", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/permier-kop/permier-kop-0.1.5.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/pfs", "version": "0.2.0", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pfs/dirpc-pfs-0.2.0.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/pharos", "version": "1.0.40", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pharos/dirpc-pharos-1.0.40.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Pharos\\": ["src/", "src/Dirpc/SDK/Pharos/"]}}}, {"name": "dirpc/phoenix", "version": "1.1.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/phoenix/phoenix-1.1.2.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/picasso", "version": "1.0.6", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/picasso/picasso-1.0.6.tar.gz"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Picasso\\": ["src/", "src/Dirpc/SDK/Picasso/"]}}}, {"name": "dirpc/picasso-judge", "version": "3.0.5", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/picasso-judge/dirpc-picasso-judge-3.0.5.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PicassoJudge\\": ["src/", "src/Dirpc/SDK/PicassoJudge/"]}}}, {"name": "dirpc/plutus", "version": "2.3.117", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/plutus/dirpc-plutus-2.3.117.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/plutus-degrade", "version": "1.0.0", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/plutus-degrade/dirpc-plutus-degrade-1.0.0.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PlutusDegrade\\": ["src/", "src/Dirpc/SDK/PlutusDegrade/"]}}}, {"name": "dirpc/pn", "version": "1.0.6", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pn/dirpc-pn-1.0.6.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/poi-info", "version": "1.1.8", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/poi-info/dirpc-poi-info-1.1.8.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PoiInfo\\": ["src/", "src/Dirpc/SDK/PoiInfo/"]}}}, {"name": "dirpc/polaris", "version": "1.1.9", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/polaris/dirpc-polaris-1.1.9.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/pope-action-proxy", "version": "1.2.8", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pope-action-proxy/pope-action-proxy-1.2.8.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/pope-dunning", "version": "1.0.4", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pope-dunning/dirpc-pope-dunning-1.0.4.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PopeDunning\\": ["src/", "src/Dirpc/SDK/PopeDunning/"]}}}, {"name": "dirpc/pope-engine", "version": "1.0.81", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pope-engine/dirpc-pope-engine-1.0.81.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/pope-p-a-p-service", "version": "1.0.43", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pope-p-a-p-service/dirpc-pope-p-a-p-service-1.0.43.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/pope-rosen-bridge", "version": "1.1.76", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pope-rosen-bridge/dirpc-pope-rosen-bridge-1.1.76.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PopeRosenBridge\\": ["src/", "src/Dirpc/SDK/PopeRosenBridge/"]}}}, {"name": "dirpc/pope2-ap", "version": "1.0.7", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pope2-ap/dirpc-pope2-ap-1.0.7.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Pope2Ap\\": ["src/", "src/Dirpc/SDK/Pope2Ap/"]}}}, {"name": "dirpc/pope2-engine", "version": "1.1.33", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pope2-engine/dirpc-pope2-engine-1.1.33.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/popefs", "version": "0.1.3", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/popefs/popefs-0.1.3.tar.gz"}, "require": {"lego/dirpc": ">=0.0.13"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "popefs service php client sdk", "keywords": ["DiRPC", "<PERSON><PERSON>"]}, {"name": "dirpc/porsche", "version": "1.0.22", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/porsche/dirpc-porsche-1.0.22.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Porsche\\": ["src/", "src/Dirpc/SDK/Porsche/"]}}}, {"name": "dirpc/pre-sale", "version": "1.0.29", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pre-sale/dirpc-pre-sale-1.0.29.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/pre-sale-go", "version": "1.0.13", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/pre-sale-go/dirpc-pre-sale-go-1.0.13.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\PreSaleGo\\": ["src/", "src/Dirpc/SDK/PreSaleGo/"]}}}, {"name": "dirpc/preorder-judge-thrift", "version": "1.0.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/preorder-judge-thrift/preorder-judge-thrift-1.0.2.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/prepay", "version": "0.1.0", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/prepay/dirpc-prepay-0.1.0.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Prepay\\": ["src/", "src/Dirpc/SDK/Prepay/"]}}}, {"name": "dirpc/prfs", "version": "1.0.90", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/prfs/dirpc-prfs-1.0.90.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/price-api", "version": "1.0.213", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/price-api/dirpc-price-api-1.0.213.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/prs", "version": "1.0.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/prs/prs-1.0.1.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/quartz", "version": "2.1.8", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/quartz/dirpc-quartz-2.1.8.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/query-pig-bubble-discount", "version": "1.0.5", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/query-pig-bubble-discount/query-pig-bubble-discount-1.0.5.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/rainbow-appeal", "version": "1.0.5", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/rainbow-appeal/rainbow-appeal-1.0.5.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/rainbow-driver-vacation", "version": "2.0.3", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/rainbow-driver-vacation/rainbow-driver-vacation-2.0.3.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/raventree", "version": "0.1.4", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/raventree/raventree-0.1.4.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["Proprietary"], "authors": [{"name": "", "email": "", "role": "Developer"}], "description": "raventree service php client sdk", "keywords": ["DiRPC", "raventree", "sdk"], "time": "2018-07-12T00:00:00+00:00"}, {"name": "dirpc/res-api", "version": "3.0.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/res-api/res-api-3.0.1.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/reverse-geo", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/reverse-geo/reverse-geo-1.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/right", "version": "0.1.4", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/right/right-0.1.4.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/robin", "version": "1.0.2", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/robin/dirpc-robin-1.0.2.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Robin\\": ["src/", "src/Dirpc/SDK/Robin/"]}}}, {"name": "dirpc/robot", "version": "0.0.4", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/robot/dirpc-robot-0.0.4.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Robot\\": ["src/", "src/Dirpc/SDK/Robot/"]}}}, {"name": "dirpc/rohan", "version": "0.0.28", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/rohan/dirpc-rohan-0.0.28.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/route-broker-service", "version": "1.0.6", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/route-broker-service/dirpc-route-broker-service-1.0.6.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/routebroker", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/routebroker/routebroker-1.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/routebrokerservice", "version": "0.1.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/routebrokerservice/routebrokerservice-0.1.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["Proprietary"], "authors": [{"name": "", "email": "", "role": "Developer"}], "description": "routebrokerservice service php client sdk", "keywords": ["DiRPC", "routebrokerservice", "sdk"], "time": "2018-07-27T00:00:00+00:00"}, {"name": "dirpc/rt-sdk", "version": "0.1.3", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/rt-sdk/rt-sdk-0.1.3.tar.gz"}, "require": {"lego/dirpc": ">=0.0.18"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "zhangcaneric", "email": "<EMAIL>"}], "description": "dirpc rockstable service php client", "keywords": ["rockstable", "rt"]}, {"name": "dirpc/rule-engine", "version": "1.7.4", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/rule-engine/dirpc-rule-engine-1.7.4.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/safe-cmpl", "version": "1.0.1", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/safe-cmpl/dirpc-safe-cmpl-1.0.1.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\SafeCmpl\\": ["src/", "src/Dirpc/SDK/SafeCmpl/"]}}}, {"name": "dirpc/scene-insurance", "version": "1.0.6", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/scene-insurance/scene-insurance-1.0.6.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/seal", "version": "1.0.26", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/seal/dirpc-seal-1.0.26.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Seal\\": ["src/", "src/Dirpc/SDK/Seal/"]}}}, {"name": "dirpc/sec-guard", "version": "2.0.43", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/sec-guard/dirpc-sec-guard-2.0.43.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\SecGuard\\": ["src/", "src/Dirpc/SDK/SecGuard/"]}}}, {"name": "dirpc/service-link-service", "version": "1.0.5", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/service-link-service/dirpc-service-link-service-1.0.5.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\ServiceLinkService\\": ["src/", "src/Dirpc/SDK/ServiceLinkService/"]}}}, {"name": "dirpc/sfc-trip-cloud-ha-luo", "version": "1.0.6", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/sfc-trip-cloud-ha-luo/dirpc-sfc-trip-cloud-ha-luo-1.0.6.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\SfcTripCloudHaLuo\\": ["src/", "src/Dirpc/SDK/SfcTripCloudHaLuo/"]}}}, {"name": "dirpc/sill", "version": "1.7.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/sill/sill-1.7.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/skyeyeengine", "version": "1.0.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/skyeyeengine/skyeyeengine-1.0.1.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/skyline", "version": "1.0.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/skyline/skyline-1.0.1.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/soraka", "version": "1.0.49", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/soraka/dirpc-soraka-1.0.49.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Soraka\\": ["src/", "src/Dirpc/SDK/Soraka/"]}}}, {"name": "dirpc/soter", "version": "1.0.60", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/soter/dirpc-soter-1.0.60.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/speech-synthesis", "version": "1.1.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/speech-synthesis/speech-synthesis-1.1.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/spruce", "version": "1.2.18", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/spruce/dirpc-spruce-1.2.18.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Spruce\\": ["src/", "src/Dirpc/SDK/Spruce/"]}}}, {"name": "dirpc/sps", "version": "1.0.91", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/sps/dirpc-sps-1.0.91.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Sps\\": ["src/", "src/Dirpc/SDK/Sps/"]}}}, {"name": "dirpc/sspnereus", "version": "2.0.3", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/sspnereus/dirpc-sspnereus-2.0.3.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\ssp_nereus\\": ["src/", "src/Dirpc/SDK/ssp_nereus/"]}}}, {"name": "dirpc/ssse", "version": "1.3.12", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/ssse/dirpc-ssse-1.3.12.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/station-push", "version": "1.0.1", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/station-push/dirpc-station-push-1.0.1.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/student-xpanel", "version": "1.0.6", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/student-xpanel/student-xpanel-1.0.6.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/tag-service-http", "version": "1.0.6", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/tag-service-http/dirpc-tag-service-http-1.0.6.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/talos", "version": "1.1.23", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/talos/dirpc-talos-1.1.23.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Talos\\": ["src/", "src/Dirpc/SDK/Talos/"]}}}, {"name": "dirpc/targaryen", "version": "1.1.4", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/targaryen/targaryen-1.1.4.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/taxi-judge", "version": "1.0.5", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/taxi-judge/taxi-judge-1.0.5.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/themis", "version": "2.0.29", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/themis/dirpc-themis-2.0.29.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/tianji-income-weekly", "version": "1.0.7", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/tianji-income-weekly/tianji-income-weekly-1.0.7.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/ticket-api", "version": "1.0.41", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/ticket-api/dirpc-ticket-api-1.0.41.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\TicketApi\\": ["src/", "src/Dirpc/SDK/TicketApi/"]}}}, {"name": "dirpc/ticket-price", "version": "1.0.7", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/ticket-price/dirpc-ticket-price-1.0.7.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\TicketPrice\\": ["src/", "src/Dirpc/SDK/TicketPrice/"]}}}, {"name": "dirpc/toggle", "version": "1.0.7", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/toggle/dirpc-toggle-1.0.7.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.6", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Toggle\\": ["src/", "src/Dirpc/SDK/Toggle/"]}}}, {"name": "dirpc/traffic", "version": "1.0.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/traffic/traffic-1.0.2.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/traffic-report", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/traffic-report/traffic-report-1.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/transaction-go", "version": "1.1.39", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/transaction-go/dirpc-transaction-go-1.1.39.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\TransactionGo\\": ["src/", "src/Dirpc/SDK/TransactionGo/"]}}}, {"name": "dirpc/transport-score-server", "version": "1.0.8", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/transport-score-server/transport-score-server-1.0.8.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/trip-cloud-aa", "version": "1.0.7", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/trip-cloud-aa/trip-cloud-aa-1.0.7.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/trip-cloud-agent", "version": "1.0.34", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/trip-cloud-agent/dirpc-trip-cloud-agent-1.0.34.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\TripCloudAgent\\": ["src/", "src/Dirpc/SDK/TripCloudAgent/"]}}}, {"name": "dirpc/trip-cloud-aiyouwei", "version": "1.0.6", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/trip-cloud-aiyouwei/trip-cloud-aiyouwei-1.0.6.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/trip-cloud-common", "version": "1.2.116", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/trip-cloud-common/dirpc-trip-cloud-common-1.2.116.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\TripCloudCommon\\": ["src/", "src/Dirpc/SDK/TripCloudCommon/"]}}}, {"name": "dirpc/trip-cloud-data-service", "version": "1.0.13", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/trip-cloud-data-service/dirpc-trip-cloud-data-service-1.0.13.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/trip-cloud-dongfeng", "version": "2.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/trip-cloud-dongfeng/trip-cloud-dongfeng-2.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/trip-cloud-driver", "version": "1.0.14", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/trip-cloud-driver/dirpc-trip-cloud-driver-1.0.14.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/trip-cloud-guangqi", "version": "0.3.17", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/trip-cloud-guangqi/dirpc-trip-cloud-guangqi-0.3.17.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/trip-cloud-hermes", "version": "1.2.17", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/trip-cloud-hermes/dirpc-trip-cloud-hermes-1.2.17.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\TripCloudHermes\\": ["src/", "src/Dirpc/SDK/TripCloudHermes/"]}}}, {"name": "dirpc/trip-cloud-passenger", "version": "1.4.66", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/trip-cloud-passenger/dirpc-trip-cloud-passenger-1.4.66.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/trip-cloud-shouqi", "version": "1.3.25", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/trip-cloud-shouqi/dirpc-trip-cloud-shouqi-1.3.25.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/trip-cloud-tongcheng", "version": "2.0.3", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/trip-cloud-tongcheng/trip-cloud-tongcheng-2.0.3.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/trip-cloud-tonggang", "version": "1.0.8", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/trip-cloud-tonggang/trip-cloud-tonggang-1.0.8.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/trip-cloud-xiangdao", "version": "0.0.23", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/trip-cloud-xiangdao/dirpc-trip-cloud-xiangdao-0.0.23.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/trip-cloud-yangguang", "version": "2.0.8", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/trip-cloud-yangguang/dirpc-trip-cloud-yangguang-2.0.8.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/trip-cloud-yiqi", "version": "2.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/trip-cloud-yiqi/trip-cloud-yiqi-2.0.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/tripcloud-agent-go", "version": "1.0.7", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/tripcloud-agent-go/dirpc-tripcloud-agent-go-1.0.7.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\TripcloudAgentGo\\": ["src/", "src/Dirpc/SDK/TripcloudAgentGo/"]}}}, {"name": "dirpc/tripcloud-d-s-hz-health", "version": "1.0.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/tripcloud-d-s-hz-health/tripcloud-d-s-hz-health-1.0.2.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/tripcloud-driver-go", "version": "1.0.8", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/tripcloud-driver-go/dirpc-tripcloud-driver-go-1.0.8.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\TripcloudDriverGo\\": ["src/", "src/Dirpc/SDK/TripcloudDriverGo/"]}}}, {"name": "dirpc/tripcloud-hermes-go", "version": "1.0.2", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/tripcloud-hermes-go/dirpc-tripcloud-hermes-go-1.0.2.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\TripcloudHermesGo\\": ["src/", "src/Dirpc/SDK/TripcloudHermesGo/"]}}}, {"name": "dirpc/tripcloud-passenger-go", "version": "1.1.39", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/tripcloud-passenger-go/dirpc-tripcloud-passenger-go-1.1.39.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\TripcloudPassengerGo\\": ["src/", "src/Dirpc/SDK/TripcloudPassengerGo/"]}}}, {"name": "dirpc/tudi", "version": "1.0.2", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/tudi/dirpc-tudi-1.0.2.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Tudi\\": ["src/", "src/Dirpc/SDK/Tudi/"]}}}, {"name": "dirpc/u-f-s", "version": "0.1.7", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/u-f-s/dirpc-u-f-s-0.1.7.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/unicorn", "version": "2.0.8", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/unicorn/dirpc-unicorn-2.0.8.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Unicorn\\": ["src/", "src/Dirpc/SDK/Unicorn/"]}}}, {"name": "dirpc/uranus", "version": "1.3.17", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/uranus/dirpc-uranus-1.3.17.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/usce-api", "version": "1.1.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/usce-api/usce-api-1.1.0.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/user-center", "version": "3.0.43", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/user-center/dirpc-user-center-3.0.43.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/v-o-i-p-s-e-r-v-e-r", "version": "0.0.2", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/v-o-i-p-s-e-r-v-e-r/dirpc-v-o-i-p-s-e-r-v-e-r-0.0.2.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\VOIPSERVER\\": ["src/", "src/Dirpc/SDK/VOIPSERVER/"]}}}, {"name": "dirpc/valet-trip-cloud-e", "version": "1.0.2", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/valet-trip-cloud-e/dirpc-valet-trip-cloud-e-1.0.2.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\ValetTripCloudE\\": ["src/", "src/Dirpc/SDK/ValetTripCloudE/"]}}}, {"name": "dirpc/vcard", "version": "1.1.8", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/vcard/dirpc-vcard-1.1.8.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Vcard\\": ["src/", "src/Dirpc/SDK/Vcard/"]}}}, {"name": "dirpc/veyron", "version": "1.0.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/veyron/veyron-1.0.1.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["Proprietary"], "authors": [{"name": "", "email": "", "role": "Developer"}], "description": "veyron service php client sdk", "keywords": ["DiRPC", "sdk", "veyron"], "time": "2018-07-02T00:00:00+00:00"}, {"name": "dirpc/vision5", "version": "1.2.30", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/vision5/dirpc-vision5-1.2.30.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Vision5\\": ["src/", "src/Dirpc/SDK/Vision5/"]}}}, {"name": "dirpc/watson", "version": "1.0.25", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/watson/dirpc-watson-1.0.25.zip"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/weather-service", "version": "1.0.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/weather-service/weather-service-1.0.2.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/webapp-gate", "version": "1.1.10", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/webapp-gate/webapp-gate-1.1.10.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/webappapi", "version": "2.28.6", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/webappapi/dirpc-webappapi-2.28.6.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\WebappApi\\": ["src/", "src/Dirpc/SDK/WebappApi/"]}}}, {"name": "dirpc/webappv3", "version": "1.0.8", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/webappv3/webappv3-1.0.8.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/x-z-y-f", "version": "1.0.12", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/x-z-y-f/dirpc-x-z-y-f-1.0.12.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\XZYF\\": ["src/", "src/Dirpc/SDK/XZYF/"]}}}, {"name": "dirpc/xiaoju-oil", "version": "1.0.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/xiaoju-oil/xiaoju-oil-1.0.1.tar.gz"}, "require": {"lego/dirpc": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}}, {"name": "dirpc/xplorer", "version": "1.0.7", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dirpc/xplorer/dirpc-xplorer-1.0.7.zip"}, "require": {"grpc/grpc": "^v1.7.0", "lego/dirpc": "1.*", "nuwa/php-rpc": "^0.1.7", "nuwa/protobuf": "4.*"}, "type": "library", "autoload": {"psr-4": {"Dirpc\\SDK\\Xplorer\\": ["src/", "src/Dirpc/SDK/Xplorer/"]}}}, {"name": "disf/bill", "version": "0.3.53", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/bill/bill-0.3.53.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "bill team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "bill service php client", "keywords": ["bill", "disf-bill"]}, {"name": "disf/carpool", "version": "0.2.24", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/carpool/carpool-0.2.24.tar.gz"}, "require": {"disf/ordersystem": ">=0.2.0", "disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "carpool team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "carpool service php client", "keywords": ["carpool", "disf-cashier"]}, {"name": "disf/carpoolrecservice", "version": "0.1.5", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/carpoolrecservice/carpoolrecservice-0.1.5.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "api", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "carpoolrecservice service php client", "keywords": [" disf!engine-public-carpoolrecsvr", "carpoolrecservice"]}, {"name": "disf/cashier", "version": "0.7.6", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/cashier/disf-cashier-0.7.6.zip"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "cashier team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "cashier service php client ", "keywords": ["cashier", "disf-cashier"]}, {"name": "disf/charon", "version": "0.3.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/charon/charon-0.3.1.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "charon team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "charon service php client", "keywords": ["charon", "disf-charon"]}, {"name": "disf/counter", "version": "0.2.8", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/counter/counter-0.2.8.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "counter team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "counter service php client", "keywords": ["counter", "disf-counter"]}, {"name": "disf/credit", "version": "0.3.5", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/credit/credit-0.3.5.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "credit team", "email": "z<PERSON><PERSON><PERSON>@didichuxing.com"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "credit service php client", "keywords": ["credit", "disf-credit"]}, {"name": "disf/defensor", "version": "0.5.3", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/defensor/defensor-0.5.3.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "defensor team", "email": "<EMAIL>"}, {"name": "defensor team", "email": "<EMAIL>"}], "description": "defensor service php client", "keywords": ["defensor", "disf-defensor"]}, {"name": "disf/dmc", "version": "0.9.3", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/dmc/dmc-0.9.3.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "dmc team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "dmc service php client", "keywords": ["disf-dmc", "dmc"]}, {"name": "disf/dos", "version": "0.166.0", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/dos/disf-dos-0.166.0.zip"}, "require": {"lego/dirpc": ">=0.0.24"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "dos team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "dos service php client", "keywords": ["disf-dos", "dos"]}, {"name": "disf/driversystem", "version": "0.3.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/driversystem/driversystem-0.3.1.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "driversystem team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "driversystem service php client", "keywords": ["disf-driversystem", "driversystem"]}, {"name": "disf/dsig", "version": "0.2.5", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/dsig/dsig-0.2.5.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "liulei", "email": "<EMAIL>"}, {"name": "wang<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "SDK for dsig"}, {"name": "disf/fenceservice", "version": "0.1.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/fenceservice/fenceservice-0.1.1.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "geofence team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "geofence http service php client", "keywords": ["disf-geofence", "disf-geofenceservice", "geofence", "geofenceservice"]}, {"name": "disf/geofence", "version": "0.0.3", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/geofence/geofence-0.0.3.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "geofence client team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "geofence php client", "keywords": ["disf-geofence", "geofence"]}, {"name": "disf/heatmap", "version": "0.1.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/heatmap/heatmap-0.1.0.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "heatmap team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "heatmap service php client", "keywords": ["disf-heatmap", "disf-heatmapthriftservice", "heatmap", "heatmapthriftservice"]}, {"name": "disf/horae", "version": "1.0.3", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/horae/horae-1.0.3.tar.gz"}, "require": {"disf/idl": ">=0.0.1", "disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "horae team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "horae service php client", "keywords": ["disf-horae", "horae"]}, {"name": "disf/horizons", "version": "0.1.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/horizons/horizons-0.1.2.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "horae team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "horizons service php client", "keywords": ["disf-horae", "horizons"]}, {"name": "disf/hotspot", "version": "0.0.5", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/hotspot/hotspot-0.0.5.tar.gz"}, "require": {"disf/spl": ">=0.4.40"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "hotspot client team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "hotspot php client", "keywords": ["disf-hotspot", "hotspot"]}, {"name": "disf/iapetos", "version": "0.2.12", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/iapetos/iapetos-0.2.12.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "iapetos team", "email": "<EMAIL>"}, {"name": "iapetos group", "email": "<EMAIL>"}], "description": "iapetos service php client", "keywords": ["disf-iapetos", "iapetos"]}, {"name": "disf/id", "version": "0.3.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/id/id-0.3.1.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "id team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "id service php client", "keywords": ["disf-cashier", "id"]}, {"name": "disf/idl", "version": "0.4.237", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/idl/disf-idl-0.4.237.zip"}, "type": "library", "autoload": {"classmap": ["common/php", "gen-code", "fpi"]}, "license": ["None"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "all idl files and the generated code", "keywords": ["disf", "disf-idl"]}, {"name": "disf/lbspp", "version": "0.1.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/lbspp/lbspp-0.1.0.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "lbspp team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "lbspp service php client", "keywords": ["disf-lbspp", "disf-lbsppthriftservice", "lbspp", "lbsppthriftservice"]}, {"name": "disf/mappostorderrec", "version": "0.6.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/MapPostOrderRec/MapPostOrderRec-0.6.0.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "MapPostOrderRec team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "MapPostOrderRec service php client", "keywords": ["MapPostOrderRec", "disf-MapPostOrderRec"]}, {"name": "disf/member", "version": "0.1.14", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/member/disf-member-0.1.14.zip"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "volcano group", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "member service php client", "keywords": ["disf-member", "member"]}, {"name": "disf/msggate", "version": "0.1.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/msggate/msggate-0.1.0.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "msggate team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "msggate service php client", "keywords": ["disf-msggate", "msggate"]}, {"name": "disf/ofs", "version": "0.4.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/ofs/ofs-0.4.0.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "ofs team", "email": "z<PERSON><PERSON><PERSON><PERSON>@didichuxing.com"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "ofs service php client", "keywords": ["disf-ofs", "ofs"]}, {"name": "disf/openapi", "version": "0.1.9", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/openapi/openapi-0.1.9.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "openapi team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "openapi service php client", "keywords": ["disf-openapi", "openapi"]}, {"name": "disf/ordersystem", "version": "0.4.7", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/ordersystem/ordersystem-0.4.7.tar.gz"}, "require": {"disf/dos": ">=0.2.0", "disf/id": ">=0.2.0", "disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "ordersystem team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "ordersystem service php client", "keywords": ["disf-cashier", "ordersystem"]}, {"name": "disf/pfs", "version": "0.5.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/pfs/pfs-0.5.2.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "pfs team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "pfs service php client", "keywords": ["disf-pfs", "pfs"]}, {"name": "disf/preorderjudge", "version": "0.6.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/preorderjudge/preorderjudge-0.6.0.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "strategy team", "email": "<EMAIL>"}, {"name": "strategy team", "email": "chenlin<PERSON><PERSON>@didichuxing.com"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "preorderjudge service php client", "keywords": ["disf-preorderjudge", "preorderjudge"]}, {"name": "disf/priceapi", "version": "0.2.10", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/priceapi/priceapi-0.2.10.tar.gz"}, "require": {"disf/idl": ">=0.2.0", "disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yang<PERSON>chen", "email": "<EMAIL>"}, {"name": "lishuaibing", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "price-api php client", "keywords": ["disf-priceapi", "dmc"]}, {"name": "disf/routematch", "version": "0.0.23", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/routematch/disf-routematch-0.0.23.zip"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "map team", "email": "<EMAIL>"}, {"name": "map team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "map service php client", "keywords": ["disf-routematch", "map"]}, {"name": "disf/spl", "version": "0.7.10", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/spl/disf-spl-0.7.10.zip"}, "require": {"lego/log": "0.* >=0.0.15", "ops-sec/disf_kms_sdk": "0.*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^9"}, "type": "library", "autoload": {"psr-4": {"Disf\\SPL\\": "src"}, "classmap": ["src"]}, "license": ["None"], "authors": [{"name": "disf group", "email": "<EMAIL>"}], "description": "disf standard php library", "keywords": ["disf-php", "disf-spl", "spl"]}, {"name": "disf/uranus", "version": "0.6.5", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/uranus/uranus-0.6.5.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "uranus team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "uranus service php client", "keywords": ["disf-uranus", "uranus"]}, {"name": "disf/userinfo", "version": "0.3.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/userinfo/userinfo-0.3.2.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "userinfo team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "userinfo service php client", "keywords": ["disf-userinfo", "userinfo"]}, {"name": "disf/winterfell", "version": "0.5.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/winterfell/winterfell-0.5.2.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "winterfell team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "winterfell service php client", "keywords": ["disf-winterfell", "winterfell"]}, {"name": "disf/withdrawplatform", "version": "0.1.1", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/disf/withdrawplatform/withdrawplatform-0.1.1.tar.gz"}, "require": {"disf/spl": ">=0.2.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "pfs team", "email": "<EMAIL>"}, {"name": "disf group", "email": "<EMAIL>"}], "description": "withdrawplatform service php client", "keywords": ["disf-withdrawplatform", "withdrawplatform"]}, {"name": "doctrine/inflector", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "e11d84c6e018beedd929cff5220969a3c6d1d462"}, "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-github/vcs-dists/zip/doctrine/inflector/e11d84c6e018beedd929cff5220969a3c6d1d462", "reference": "e11d84c6e018beedd929cff5220969a3c6d1d462", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Common String Manipulations with regard to casing and singular/plural rules.", "homepage": "http://www.doctrine-project.org", "keywords": ["inflection", "pluralize", "singularize", "string"], "support": {"source": "https://github.com/doctrine/inflector/tree/master"}, "time": "2017-07-22T12:18:28+00:00"}, {"name": "dukang/feature-php", "version": "0.0.9", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dukang/feature-php/dukang-feature-php-0.0.9.zip"}, "require": {"apollo/apollo-sdk-php": "^2.8", "s3e/x-engine-php": "*"}, "require-dev": {"phpunit/phpunit": "^6.5"}, "type": "library", "autoload": {"psr-4": {"Dukang\\Feature\\": ["src"], "Dukang\\Feature\\Examples\\": ["examples"]}}}, {"name": "dukang/product-definition", "version": "0.0.20", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dukang/product-definition/dukang-product-definition-0.0.20.zip"}, "require": {"disf/idl": "^0.4.190", "nuwa/apollo": "1.*"}, "require-dev": {"biz/biz-lib": "3.*", "johnkary/phpunit-speedtrap": "^4.0", "mockery/mockery": "^1.6", "nuwa/unit": "*.*", "phpunit/phpunit": "*.*"}, "type": "library", "autoload": {"psr-4": {"Dukang\\ProductDef\\": ["src"]}}, "authors": [{"name": "mahe", "email": "<EMAIL>"}]}, {"name": "dukang/property", "version": "0.0.3", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dukang/property/property-0.0.3.tar.gz"}, "require": {"nuwa/apollo": "1.*"}, "require-dev": {"biz/biz-lib": "3.*", "nuwa/unit": "*.*", "phpunit/phpunit": "*.*"}, "type": "library", "autoload": {"psr-4": {"Dukang\\Property\\": ["src"]}, "files": ["src/Property.php"]}, "authors": [{"name": "tan<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, {"name": "dukang/property-const-php-sdk", "version": "1.6.83", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/dukang/property-const-php-sdk/dukang-property-const-php-sdk-1.6.83.zip"}, "require": {"apollo/apollo-sdk-php": "2.*", "nuwa/php-rpc": "^0.1.9"}, "require-dev": {"phpunit/phpunit": "*.*"}, "type": "library", "autoload": {"psr-4": {"Dukang\\PropertyConst\\": ["src"]}}}, {"name": "elvish/elvish-lib-php", "version": "1.1.40", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/elvish/elvish-lib-php/elvish-lib-php-1.1.40.tar.gz"}, "require": {"apollo/apollo-sdk-php": ">=2.7.4", "disf/spl": ">=0.4.97"}, "require-dev": {"cpliakas/git-wrapper": "1.7.0", "phpunit/phpunit": "6.5.14"}, "type": "library", "autoload": {"psr-4": {"Elvish\\": ["src"]}}, "license": ["None"], "description": "library for elvish", "keywords": ["elvish", "global", "php"]}, {"name": "engine/gaia-sdk-php", "version": "1.0.22", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/engine/gaia-sdk-php/engine-gaia-sdk-php-1.0.22.zip"}, "require": {"apollo/apollo-sdk-php": "^2.8.1", "biz/biz-lib": "*", "ext-json": "*", "symfony/yaml": "~3.3"}, "require-dev": {"mockery/mockery": "1.*", "phpunit/phpunit": "6.*"}, "suggest": {"ext-apcu": "*"}, "type": "library", "autoload": {"psr-4": {"Engine\\GaiaSdkPhp\\": "src/"}, "files": ["config/consts.php"]}, "autoload-dev": {"psr-4": {"Engine\\GaiaSdkPhp\\Tests\\": "tests/"}}, "license": ["proprietary"], "description": "gaia sdk for php", "keywords": ["gaia", "php", "sdk"]}, {"name": "expert-data-assets/datasender", "version": "2.0.0", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/expert-data-assets/datasender/expert-data-assets-datasender-2.0.0.zip"}, "require": {"apache/log4php": "2.3.0"}, "type": "project", "autoload": {"psr-4": {"ExpertDataAssets\\": ["./"]}}, "license": ["MIT"], "description": "datasender api sdk for php", "homepage": "https://datasender.didichuxing.com"}, {"name": "fgrosse/phpasn1", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/fgrosse/PHPASN1.git", "reference": "6edcecb4df8b6881e79080d5e363dc8b90d4558c"}, "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-github/vcs-dists/zip/fgrosse/PHPASN1/6edcecb4df8b6881e79080d5e363dc8b90d4558c", "reference": "6edcecb4df8b6881e79080d5e363dc8b90d4558c", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"php-coveralls/php-coveralls": "~2.0", "phpunit/phpunit": "^6.3 | ^7.0"}, "suggest": {"ext-bcmath": "BCmath is the fallback extension for big integer calculations", "ext-curl": "For loading OID information from the web if they have not bee defined statically", "ext-gmp": "GMP is the preferred extension for big integer calculations", "phpseclib/bcmath_compat": "BCmath polyfill for servers where neither GMP nor BCmath is available"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"FG\\": "lib/"}}, "license": ["MIT"], "authors": [{"name": "Friedrich Große", "email": "<EMAIL>", "homepage": "https://github.com/FGrosse", "role": "Author"}, {"name": "All contributors", "homepage": "https://github.com/FGrosse/PHPASN1/contributors"}], "description": "A PHP Framework that allows you to encode and decode arbitrary ASN.1 structures using the ITU-T X.690 Encoding Rules.", "homepage": "https://github.com/FGrosse/PHPASN1", "keywords": ["DER", "asn.1", "asn1", "ber", "binary", "decoding", "encoding", "x.509", "x.690", "x509", "x690"], "support": {"issues": "https://github.com/fgrosse/PHPASN1/issues", "source": "https://github.com/fgrosse/PHPASN1/tree/v2.3.1"}, "abandoned": true, "time": "2021-12-09T20:59:31+00:00"}, {"name": "fpi/international-passenger", "version": "0.0.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/fpi/international-passenger/international-passenger-0.0.2.tar.gz"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "description": "plugin for global", "keywords": ["fpi", "global-plugin"]}, {"name": "fpi/nebula", "version": "0.2.11", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/fpi/nebula/fpi-nebula-0.2.11.zip"}, "require": {"biz/biz-common": "1.*", "biz/biz-lib": ">=1.4.14"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "car scene group", "email": "<EMAIL>"}], "description": "fps implement car scene", "keywords": ["fpi", "nebula"]}, {"name": "fpi/premier", "version": "0.0.6", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/fpi/premier/premier-0.0.6.tar.gz"}, "type": "library", "autoload": {"psr-4": {"Fpi\\Premier\\": ["src"]}}, "license": ["None"], "description": "fpi for premier", "keywords": ["fpi", "premier"]}, {"name": "grpc/grpc", "version": "1.74.0", "source": {"type": "git", "url": "https://github.com/grpc/grpc-php.git", "reference": "32bf4dba256d60d395582fb6e4e8d3936bcdb713"}, "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-github/vcs-dists/zip/grpc/grpc-php/32bf4dba256d60d395582fb6e4e8d3936bcdb713", "reference": "32bf4dba256d60d395582fb6e4e8d3936bcdb713", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"google/auth": "^v1.3.0"}, "suggest": {"ext-protobuf": "For better performance, install the protobuf C extension.", "google/protobuf": "To get started using grpc quickly, install the native protobuf library."}, "type": "library", "autoload": {"psr-4": {"Grpc\\": "src/lib/"}}, "license": ["Apache-2.0"], "description": "gRPC library for PHP", "homepage": "https://grpc.io", "keywords": ["rpc"], "support": {"source": "https://github.com/grpc/grpc-php/tree/v1.74.0"}, "time": "2025-07-24T20:02:16+00:00"}, {"name": "gulfstream/bronze-door-sdk-php", "version": "0.0.20", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/gulfstream/bronze-door-sdk-php/gulfstream-bronze-door-sdk-php-0.0.20.zip"}, "require": {"biz/biz-lib": "3.*", "ext-json": "*", "nuwa/apollo": "1.*", "s3e/x-engine-php": "0.*"}, "type": "library", "autoload": {"classmap": ["src/"]}, "description": "door php sdk"}, {"name": "gulfstream/car-partner-center-php-sdk", "version": "1.0.4", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/gulfstream/car-partner-center-php-sdk/gulfstream-car-partner-center-php-sdk-1.0.4.zip"}, "require": {"apollo/apollo-sdk-php": "2.*", "dirpc/car-partner-center-sdk": "1.*", "disf/spl": "0.*"}, "type": "library", "autoload": {"psr-4": {"GPBMetadata\\Src\\Proto\\": "src/GPBMetadata/Src/Proto/", "CarPartnerCenterSdk\\": ["src"]}}, "license": ["None"], "description": "pbd tripcloud partner center sdk", "keywords": ["car-partner-center-sdk", "pangu"]}, {"name": "gulfstream/driver-system-sdk", "version": "v1.0.9", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/gulfstream/driver-system-sdk/driver-system-sdk-v1.0.9.tar.gz"}, "type": "library", "autoload": {"psr-4": {"Sdk\\": "src/Sdk"}}, "authors": [{"name": "hua<PERSON>", "email": "<EMAIL>"}]}, {"name": "gulfstream/hxz-php-quotation-sdk", "version": "0.0.6", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/gulfstream/hxz-php-quotation-sdk/gulfstream-hxz-php-quotation-sdk-0.0.6.zip"}, "type": "library", "license": ["None"], "description": "None"}, {"name": "gulfstream/minos-sdk", "version": "0.1.3", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/gulfstream/minos-sdk/minos-sdk-0.1.3.tar.gz"}, "require": {"lego/dirpc": ">=0.0.4"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["Proprietary"], "authors": [{"name": "qiukan", "email": "<EMAIL>", "role": "Developer"}], "description": "minos service php client sdk", "keywords": ["DiRPC", "minos", "sdk"], "time": "2017-10-19T00:00:00+00:00"}, {"name": "gulfstream/tripcloud-common", "version": "2.0.442", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/gulfstream/tripcloud-common/gulfstream-tripcloud-common-2.0.442.zip"}, "require": {"biz/biz-common": "1.*", "biz/biz-lib": "3.*", "gulfstream/car-partner-center-php-sdk": "1.*", "qiankunbag/kms-agent": ">=0.0.1", "swaggest/json-diff": "3.8.1"}, "type": "library", "autoload": {"psr-4": {"TripcloudCommon\\": ["src"]}}, "license": ["None"], "description": "common code for tripcloud", "keywords": ["tripcloud", "tripcloud-common"]}, {"name": "illuminate/container", "version": "v5.5.44", "source": {"type": "git", "url": "https://github.com/illuminate/container.git", "reference": "7917f4c86ecf7f4d0efcfd83248ad3e301e08858"}, "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-github/vcs-dists/zip/illuminate/container/7917f4c86ecf7f4d0efcfd83248ad3e301e08858", "reference": "7917f4c86ecf7f4d0efcfd83248ad3e301e08858", "shasum": ""}, "require": {"illuminate/contracts": "5.5.*", "php": ">=7.0", "psr/container": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.5-dev"}}, "autoload": {"psr-4": {"Illuminate\\Container\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Container package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2018-01-19T17:58:33+00:00"}, {"name": "illuminate/contracts", "version": "v5.5.44", "source": {"type": "git", "url": "https://github.com/illuminate/contracts.git", "reference": "b2a62b4a85485fca9cf5fa61a933ad64006ff528"}, "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-github/vcs-dists/zip/illuminate/contracts/b2a62b4a85485fca9cf5fa61a933ad64006ff528", "reference": "b2a62b4a85485fca9cf5fa61a933ad64006ff528", "shasum": ""}, "require": {"php": ">=7.0", "psr/container": "~1.0", "psr/simple-cache": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.5-dev"}}, "autoload": {"psr-4": {"Illuminate\\Contracts\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Contracts package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2018-03-20T15:34:35+00:00"}, {"name": "illuminate/database", "version": "v5.5.0", "source": {"type": "git", "url": "https://github.com/illuminate/database.git", "reference": "0f8b6398baf4762fe147295dee156956f00a7743"}, "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-github/vcs-dists/zip/illuminate/database/0f8b6398baf4762fe147295dee156956f00a7743", "reference": "0f8b6398baf4762fe147295dee156956f00a7743", "shasum": ""}, "require": {"illuminate/container": "5.5.*", "illuminate/contracts": "5.5.*", "illuminate/support": "5.5.*", "php": ">=7.0"}, "suggest": {"doctrine/dbal": "Required to rename columns and drop SQLite columns (~2.5).", "fzaninotto/faker": "Required to use the eloquent factory builder (~1.4).", "illuminate/console": "Required to use the database commands (5.5.*).", "illuminate/events": "Required to use the observers with Eloquent (5.5.*).", "illuminate/filesystem": "Required to use the migrations (5.5.*).", "illuminate/pagination": "Required to paginate the result set (5.5.*)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.5-dev"}}, "autoload": {"psr-4": {"Illuminate\\Database\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Database package.", "homepage": "https://laravel.com", "keywords": ["database", "laravel", "orm", "sql"], "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2017-08-29T19:22:34+00:00"}, {"name": "illuminate/support", "version": "v5.5.44", "source": {"type": "git", "url": "https://github.com/illuminate/support.git", "reference": "5c405512d75dcaf5d37791badce02d86ed8e4bc4"}, "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-github/vcs-dists/zip/illuminate/support/5c405512d75dcaf5d37791badce02d86ed8e4bc4", "reference": "5c405512d75dcaf5d37791badce02d86ed8e4bc4", "shasum": ""}, "require": {"doctrine/inflector": "~1.1", "ext-mbstring": "*", "illuminate/contracts": "5.5.*", "nesbot/carbon": "^1.24.1", "php": ">=7.0"}, "replace": {"tightenco/collect": "<5.5.33"}, "suggest": {"illuminate/filesystem": "Required to use the composer class (5.5.*).", "symfony/process": "Required to use the composer class (~3.3).", "symfony/var-dumper": "Required to use the dd function (~3.3)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.5-dev"}}, "autoload": {"files": ["helpers.php"], "psr-4": {"Illuminate\\Support\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Support package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2018-08-10T19:40:01+00:00"}, {"name": "lego/dirpc", "version": "1.11.2", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/lego/dirpc/lego-dirpc-1.11.2.zip"}, "require": {"disf/spl": ">=0.7.9", "lego/log": ">=0.0.15", "nuwa/chaos-interceptor": "1.*", "nuwa/phystrix": "1.*", "pt-arch/warden-client": ">=0.0.2"}, "require-dev": {"phpunit/php-code-coverage": "^9.2", "phpunit/phpunit": "^9"}, "type": "library", "autoload": {"psr-4": {"Lego\\Dirpc\\": "src"}, "classmap": ["src"]}, "license": ["None"], "authors": [{"name": "lego-dev", "email": "<EMAIL>"}], "description": "Didi RPC framework for PHP", "keywords": ["DiRPC", "<PERSON><PERSON>", "RPC"]}, {"name": "lego/log", "version": "0.0.18", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/lego/log/lego-log-0.0.18.zip"}, "require": {"nuwa/log": ">=v1.3.6"}, "require-dev": {"phpunit/phpunit": "^9"}, "type": "library", "autoload": {"psr-4": {"Lego\\Log\\": "Log"}}, "license": ["BSD-style"], "authors": [{"name": "sky", "email": "<EMAIL>"}], "description": "log Common Components"}, {"name": "nesbot/carbon", "version": "1.37.1", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon.git", "reference": "5be4fdf97076a685b23efdedfc2b73ad0c5eab70"}, "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-github/vcs-dists/zip/CarbonPHP/carbon/5be4fdf97076a685b23efdedfc2b73ad0c5eab70", "reference": "5be4fdf97076a685b23efdedfc2b73ad0c5eab70", "shasum": ""}, "require": {"php": ">=5.3.9", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7"}, "suggest": {"friendsofphp/php-cs-fixer": "Needed for the `composer phpcs` command. Allow to automatically fix code style.", "phpstan/phpstan": "Needed for the `composer phpstan` command. Allow to detect potential errors."}, "type": "library", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}}, "autoload": {"psr-4": {"": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}], "description": "A simple API extension for DateTime.", "homepage": "http://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2019-04-19T10:27:42+00:00"}, {"name": "nuwa/apollo", "version": "1.1.2", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/nuwa/apollo/nuwa-apollo-1.1.2.zip"}, "require": {"apollo/apollo-sdk-php": "2.9.*"}, "type": "library", "autoload": {"psr-4": {"Nuwa\\ApolloSDK\\": ["src"]}}, "license": ["None"], "authors": [{"name": "Nuwa team", "email": "<EMAIL>"}, {"name": "Nuwa group", "email": "<EMAIL>"}], "description": "Nuwa PHP Apollo", "keywords": ["Apollo", "<PERSON><PERSON><PERSON>", "Nuwa-Apollo"]}, {"name": "nuwa/chaos-interceptor", "version": "1.0.12", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/nuwa/chaos-interceptor/nuwa-chaos-interceptor-1.0.12.zip"}, "require": {"disf/spl": "0.*"}, "type": "library", "autoload": {"psr-4": {"Nuwa\\Interceptor\\": "src/", "Nuwa\\Test\\": "tests/"}}, "description": "DiRPC Chaos Interceptor"}, {"name": "nuwa/config", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/nuwa/config/config-1.0.0.tar.gz"}, "type": "library", "autoload": {"psr-4": {"Nuwa\\Config\\": ["src"]}}, "license": ["None"], "authors": [{"name": "Nuwa team", "email": "<EMAIL>"}, {"name": "Nuwa group", "email": "<EMAIL>"}], "description": "Nuwa PHP Config", "keywords": ["Config", "<PERSON><PERSON><PERSON>", "Nuwa-Config"]}, {"name": "nuwa/cors", "version": "1.0.0", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/nuwa/cors/cors-1.0.0.tar.gz"}, "require-dev": {"phpunit/phpunit": "6", "symfony/var-dumper": "^4.0"}, "type": "library", "autoload": {"psr-4": {"Nuwa\\Cors\\": ["src"]}}, "description": "Nuwa CORS module.", "keywords": ["<PERSON><PERSON><PERSON>", "PHP", "cors"]}, {"name": "nuwa/database", "version": "1.0.5", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/nuwa/database/database-1.0.5.tar.gz"}, "require": {"disf/spl": "0.*", "illuminate/database": "5.5", "nesbot/carbon": "1.37.1"}, "type": "library", "autoload": {"psr-4": {"Nuwa\\Database\\": ["src"]}}, "license": ["None"], "authors": [{"name": "Nuwa team", "email": "<EMAIL>"}, {"name": "Nuwa group", "email": "<EMAIL>"}], "description": "Nuwa PHP Database", "keywords": ["Database", "<PERSON><PERSON><PERSON>", "Nuwa-Database"]}, {"name": "nuwa/errhandler", "version": "1.0.3", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/nuwa/errhandler/nuwa-errhandler-1.0.3.zip"}, "type": "library", "autoload": {"psr-4": {"Nuwa\\ErrHandler\\": ["src"]}}, "license": ["None"], "authors": [{"name": "Nuwa team", "email": "<EMAIL>"}, {"name": "Nuwa group", "email": "<EMAIL>"}], "description": "Nuwa PHP Error Handler", "keywords": ["Error", "Exception", "Handler", "<PERSON><PERSON><PERSON>"]}, {"name": "nuwa/framework", "version": "1.0.28", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/nuwa/framework/framework-1.0.28.tar.gz"}, "require": {"nuwa/config": "^1.0.0", "nuwa/cors": "^1.0.0", "nuwa/errhandler": "^1.0.1", "nuwa/log": "^1.1.0", "nuwa/util": "^1.0.4", "pimple/pimple": "^3.2", "psr/log": "^1.0"}, "type": "library", "autoload": {"psr-4": {"Nuwa\\": "src/Nuwa/"}, "files": ["src/Nuwa/Core/Bootstrap.php"]}, "description": "The Nuwa Framework.", "keywords": ["PHP", "framework", "nuwa"]}, {"name": "nuwa/framework-bridge", "version": "1.0.5", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/nuwa/framework-bridge/nuwa-framework-bridge-1.0.5.zip"}, "require": {"nuwa/framework": ">=0.0.1"}, "type": "library", "autoload": {"psr-4": {"Nuwa\\Bridge\\": "src/Nuwa/Bridge/"}}, "description": "The Nuwa Framework Bridge.", "keywords": ["PHP", "framework bridge", "nuwa"]}, {"name": "nuwa/locker", "version": "1.0.5", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/nuwa/locker/nuwa-locker-1.0.5.zip"}, "require": {"disf/spl": "0.*"}, "require-dev": {"phpunit/phpunit": "4.8.*"}, "type": "library", "autoload": {"psr-4": {"Nuwa\\Locker\\": ["src"]}}, "license": ["None"], "authors": [{"name": "Nuwa team", "email": "<EMAIL>"}, {"name": "Nuwa team", "email": "<EMAIL>"}, {"name": "Nuwa group", "email": "<EMAIL>"}], "description": "Nuwa PHP Locker", "keywords": ["<PERSON><PERSON><PERSON>", "Nuwa-<PERSON><PERSON>", "redis"]}, {"name": "nuwa/log", "version": "1.3.8", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/Nuwa/Log/Nuwa-Log-1.3.8.zip"}, "require": {"apollo/apollo-sdk-php": "2.*", "psr/log": "^1.0"}, "type": "library", "autoload": {"psr-4": {"Nuwa\\Log\\": ["src"]}}, "license": ["None"], "authors": [{"name": "Nuwa team", "email": "<EMAIL>"}, {"name": "Nuwa group", "email": "<EMAIL>"}], "description": "Nuwa PHP Log", "keywords": ["Log", "<PERSON><PERSON><PERSON>", "Nuwa-Log"]}, {"name": "nuwa/nuwa-online-coverage", "version": "0.0.9", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/nuwa/nuwa-online-coverage/nuwa-nuwa-online-coverage-0.0.9.zip"}, "require": {"nuwa/apollo": "1.*", "nuwa/log": "1.*"}, "type": "library", "autoload": {"psr-4": {"Nuwa\\CodeCoverage\\": ["src"]}}}, {"name": "nuwa/php-rpc", "version": "0.1.9", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/nuwa/php-rpc/nuwa-php-rpc-0.1.9.zip"}, "require": {"ext-json": "*"}, "type": "library", "autoload": {"psr-4": {"Nuwa\\PhpRpc\\": ["src/"]}}, "license": ["BSD-3-<PERSON><PERSON>"], "description": "new(2022) rpc library for PHP", "homepage": "https://git.xiaojukeji.com/nuwa/php-rpc", "keywords": ["rpc"]}, {"name": "nuwa/phystrix", "version": "1.0.10", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/nuwa/phystrix/phystrix-1.0.10.tar.gz"}, "require": {"disf/spl": "0.*", "nuwa/log": "1.*"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "type": "library", "autoload": {"psr-4": {"Nuwa\\Phystrix\\": ["src"], "Tests\\Nuwa\\Phystrix\\": ["tests/Tests"]}}, "license": ["Apache-2.0"], "authors": [{"name": "Nuwa team", "email": "<EMAIL>"}, {"name": "Nuwa group", "email": "<EMAIL>"}], "description": "Protocol agnostic library for latency and fault isolation", "keywords": ["nuwa", "phystrix"]}, {"name": "nuwa/protobuf", "version": "4.0.9", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/nuwa/protobuf/protobuf-4.0.9.tar.gz"}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "^6.5"}, "suggest": {"ext-bcmath": "Need to support JSON deserialization"}, "type": "library", "autoload": {"psr-4": {"Nuwa\\Protobuf\\": "src/Google/Protobuf", "GPBMetadata\\Nuwa\\Protobuf\\": "src/GPBMetadata/Google/Protobuf"}}, "autoload-dev": {"psr-4": {"Dirpc\\SDK\\ProtobufTest\\": "tests/idl/Dirpc/SDK/ProtobufTest/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "description": "proto library for PHP", "homepage": "https://git.xiaojukeji.com/nuwa/php/protobuf-php", "keywords": ["proto"]}, {"name": "nuwa/redis", "version": "1.0.20", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/nuwa/redis/nuwa-redis-1.0.20.zip"}, "require": {"disf/spl": "0.*", "nuwa/config": "1.*", "nuwa/log": "1.*"}, "require-dev": {"phpunit/phpunit": "^6.5"}, "suggest": {"ext-redis": "Need Redis extension"}, "type": "library", "autoload": {"psr-4": {"Nuwa\\Redis\\": ["src"]}}, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "an redis lib for nuwa"}, {"name": "nuwa/util", "version": "1.0.5", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/nuwa/util/nuwa-util-1.0.5.zip"}, "type": "library", "autoload": {"psr-4": {"Nuwa\\Util\\": ["src"]}}, "license": ["None"], "authors": [{"name": "Nuwa team", "email": "<EMAIL>"}, {"name": "Nuwa group", "email": "<EMAIL>"}], "description": "Nuwa PHP Util", "keywords": ["<PERSON><PERSON><PERSON>", "Nuwa-<PERSON><PERSON>", "<PERSON><PERSON>"]}, {"name": "ops-sec/disf_kms_sdk", "version": "0.1.4", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/ops-sec/disf_kms_sdk/disf_kms_sdk-0.1.4.tar.gz"}, "require": {"ext-json": "*", "ext-openssl": "*"}, "type": "library", "autoload": {"classmap": ["src/"]}, "authors": [{"name": "wutingxi", "email": "<EMAIL>"}], "description": "PHP SDK for DiSF-KMS"}, {"name": "phpseclib/phpseclib", "version": "2.0.48", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "eaa7be704b8b93a6913b69eb7f645a59d7731b61"}, "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-github/vcs-dists/zip/phpseclib/phpseclib/eaa7be704b8b93a6913b69eb7f645a59d7731b61", "reference": "eaa7be704b8b93a6913b69eb7f645a59d7731b61", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "^4.8.35|^5.7|^6.0|^9.4", "squizlabs/php_codesniffer": "~2.0"}, "suggest": {"ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations.", "ext-xml": "Install the XML extension to load XML formatted public keys."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib\\": "phpseclib/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.48"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2024-12-14T21:03:54+00:00"}, {"name": "pimple/pimple", "version": "v3.2.3", "source": {"type": "git", "url": "https://github.com/silexphp/Pimple.git", "reference": "9e403941ef9d65d20cba7d54e29fe906db42cf32"}, "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-github/vcs-dists/zip/silexphp/Pimple/9e403941ef9d65d20cba7d54e29fe906db42cf32", "reference": "9e403941ef9d65d20cba7d54e29fe906db42cf32", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/container": "^1.0"}, "require-dev": {"symfony/phpunit-bridge": "^3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2.x-dev"}}, "autoload": {"psr-0": {"Pimple": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "<PERSON><PERSON>, a simple Dependency Injection Container", "homepage": "http://pimple.sensiolabs.org", "keywords": ["container", "dependency injection"], "support": {"issues": "https://github.com/silexphp/Pimple/issues", "source": "https://github.com/silexphp/Pimple/tree/master"}, "time": "2018-01-21T07:42:36+00:00"}, {"name": "platform-ha/diconf", "version": "0.1.12", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/platform-ha/diconf/diconf-0.1.12.tar.gz"}, "require": {"disf/spl": ">=0.4.18"}, "type": "library", "autoload": {"psr-4": {"Xiaoju\\Diconf\\": "src"}, "classmap": ["src"]}, "license": ["None"], "authors": [{"name": "platform-ha", "email": "<EMAIL>"}], "description": "didi config service", "keywords": ["diconf"]}, {"name": "platform-ha/onekey-degrade", "version": "0.0.16", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/platform-ha/onekey-degrade/platform-ha-onekey-degrade-0.0.16.zip"}, "require": {"apollo/apollo-sdk-php": "2.*"}, "type": "library", "autoload": {"files": ["src/Interface.php"], "psr-4": {"PlatformHA\\Degrade\\": "src"}, "classmap": ["src"]}, "license": ["None"], "authors": [{"name": "op-hap", "email": "<EMAIL>"}], "description": "onekey-degrade", "keywords": ["onekey-degrade"]}, {"name": "psr/container", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f"}, "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-github/vcs-dists/zip/php-fig/container/b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/master"}, "time": "2017-02-14T16:28:37+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-github/vcs-dists/zip/php-fig/log/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-github/vcs-dists/zip/php-fig/simple-cache/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "pt-arch/warden-client", "version": "0.0.2", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/pt-arch/warden-client/warden-client-0.0.2.tar.gz"}, "require-dev": {"phpunit/phpunit": "^5"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "pt-arch", "email": "<EMAIL>"}], "description": "warden  client sdk for dirpc", "keywords": ["pt-arch", "warden", "warden-client"]}, {"name": "qiankunbag/kms", "version": "0.4.9", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/qiankunbag/kms/qiankunbag-kms-0.4.9.zip"}, "require": {"dirpc/kms": "^0.0.6", "ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "fgrosse/phpasn1": "^2.1", "nuwa/log": "1.*", "nuwa/redis": "^1.0.14", "phpseclib/phpseclib": "^2.0"}, "type": "library", "autoload": {"psr-4": {"QianKun\\Kms\\": ["src"]}}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}]}, {"name": "qiankunbag/kms-agent", "version": "0.0.6", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/qiankunbag/kms-agent/qiankunbag-kms-agent-0.0.6.zip"}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "fgrosse/phpasn1": "^2.1", "nuwa/log": "1.*", "phpseclib/phpseclib": "^2.0"}, "type": "library", "autoload": {"psr-4": {"QianKun\\KmsAgent\\": ["src"]}}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}]}, {"name": "s3e/iexp-php", "version": "1.0.2", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/s3e/iexp-php/s3e-iexp-php-1.0.2.zip"}, "require": {"biz/biz-lib": "^3.7", "nuwa/apollo": "1.*"}, "type": "library", "autoload": {"classmap": ["src"]}, "description": "i-exp for php"}, {"name": "s3e/pts", "version": "1.0.30", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/s3e/pts/s3e-pts-1.0.30.zip"}, "require": {"biz/biz-lib": "3.*", "disf/spl": "0.*", "php": ">=7.0"}, "type": "library", "autoload": {"classmap": ["src"]}, "license": ["None"], "authors": [{"name": "jing<PERSON>you", "email": "<EMAIL>"}], "keywords": ["pts", "s3e"]}, {"name": "s3e/x-engine-php", "version": "0.0.15", "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/s3e/x-engine-php/s3e-x-engine-php-0.0.15.zip"}, "require": {"biz/biz-lib": "3.9.*", "nuwa/apollo": "1.*"}, "require-dev": {"phpunit/phpunit": "^6.5"}, "type": "library", "autoload": {"classmap": ["src"]}, "description": "x-engine for php"}, {"name": "swaggest/json-diff", "version": "v3.8.1", "source": {"type": "git", "url": "https://github.com/swaggest/json-diff.git", "reference": "d2184358c5ef5ecaa1f6b4c2bce175fac2d25670"}, "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-github/vcs-dists/zip/swaggest/json-diff/d2184358c5ef5ecaa1f6b4c2bce175fac2d25670", "reference": "d2184358c5ef5ecaa1f6b4c2bce175fac2d25670", "shasum": ""}, "require": {"ext-json": "*"}, "require-dev": {"phpunit/phpunit": "^4.8.23"}, "type": "library", "autoload": {"psr-4": {"Swaggest\\JsonDiff\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "JSON diff/rearrange/patch/pointer library for PHP", "support": {"issues": "https://github.com/swaggest/json-diff/issues", "source": "https://github.com/swaggest/json-diff/tree/v3.8.1"}, "time": "2020-09-25T17:47:07+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.19.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "b5f7b932ee6fa802fc792eabd77c4c88084517ce"}, "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-github/vcs-dists/zip/symfony/polyfill-mbstring/b5f7b932ee6fa802fc792eabd77c4c88084517ce", "reference": "b5f7b932ee6fa802fc792eabd77c4c88084517ce", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.19-dev"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.19.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-23T09:01:57+00:00"}, {"name": "symfony/translation", "version": "v3.3.6", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "35dd5fb003c90e8bd4d8cabdf94bf9c96d06fdc3"}, "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-github/vcs-dists/zip/symfony/translation/35dd5fb003c90e8bd4d8cabdf94bf9c96d06fdc3", "reference": "35dd5fb003c90e8bd4d8cabdf94bf9c96d06fdc3", "shasum": ""}, "require": {"php": ">=5.5.9", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/config": "<2.8", "symfony/yaml": "<3.3"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~2.8|~3.0", "symfony/intl": "^2.8.18|^3.2.5", "symfony/yaml": "~3.3"}, "suggest": {"psr/log": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Translation Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/3.3"}, "time": "2017-06-24T16:45:30+00:00"}, {"name": "symfony/yaml", "version": "v3.3.6", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "ddc23324e6cfe066f3dd34a37ff494fa80b617ed"}, "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-github/vcs-dists/zip/symfony/yaml/ddc23324e6cfe066f3dd34a37ff494fa80b617ed", "reference": "ddc23324e6cfe066f3dd34a37ff494fa80b617ed", "shasum": ""}, "require": {"php": ">=5.5.9"}, "require-dev": {"symfony/console": "~2.8|~3.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/3.3"}, "time": "2017-07-23T12:43:26+00:00"}, {"name": "webapp/didi-wechat-php-sdk", "version": "0.3.7", "dist": {"type": "tar", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-local/direct-dists/webapp/didi-wechat-php-sdk/didi-wechat-php-sdk-0.3.7.tar.gz"}, "require": {"dirpc/webappapi": "^2.18"}, "type": "library", "autoload": {"psr-4": {"WebappSdk\\": ["src/"]}}}], "packages-dev": [{"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-github/vcs-dists/zip/hamcrest/hamcrest-php/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": ""}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.0.1"}, "time": "2020-07-09T08:09:16+00:00"}, {"name": "mockery/mockery", "version": "1.3.6", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "dc206df4fa314a50bbb81cf72239a305c5bbd5c0"}, "dist": {"type": "zip", "url": "http://artifactory.intra.xiaojukeji.com/artifactory/api/composer/composer-github/vcs-dists/zip/mockery/mockery/dc206df4fa314a50bbb81cf72239a305c5bbd5c0", "reference": "dc206df4fa314a50bbb81cf72239a305c5bbd5c0", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=5.6.0"}, "require-dev": {"phpunit/phpunit": "^5.7.10|^6.5|^7.5|^8.5|^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-0": {"Mockery": "library/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.astrumfutura.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://davedevelopment.co.uk"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.3.6"}, "time": "2022-09-07T15:05:49+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {"ext-json": "*", "ext-bcmath": "*"}, "platform-dev": {}, "platform-overrides": {"php": "7.0.6"}, "plugin-api-version": "2.6.0"}