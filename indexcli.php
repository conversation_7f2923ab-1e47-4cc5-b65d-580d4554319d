<?php
/**
 * Nuwa-PHP - A PHP Framework For Web.
 *
 * <AUTHOR>
 */
define('NUWA_START', microtime(true));
define('PROJ_PATH', __DIR__ . '/');
define('APP_PATH', PROJ_PATH . 'app/');

/**
 * require constants.php for biz
 */
require_once (PROJ_PATH.'config/constants.php');

/**
 * Register The Auto Loader.
 */
require(PROJ_PATH . 'vendor/autoload.php');

/**
 * Create The Application.
 *
 * read app/config/app.php and instance application.
 */
$app = Nuwa\Core\Application::create(realpath(PROJ_PATH));

/**
 * Bootstrap the application for HTTP requests.
 *
 * foreach(app/bootstrap/Bootstrap->bootstrappers as bootstrapper)
 *     bootstrapper->bootstrap()
 * end
 */
$app->bootstrap();

/**
 * Run The Application
 *
 * $plugin->routerStartup()
 * route($request)
 * $plugin->routerShutdown()
 *
 * $plugin->dispatchLoopStartup()
 *
 * initView
 * do {
 *     $plugin->preDispatch()
 *
 *     dispatcher($request, $response, $view)) // call controller->action
 *
 *     $plugin->postDispatch()
 * } while (--nesting > 0 && !request_is_dispatched())
 *
 * $plugin->dispatchLoopShutdown()
 */
// 是否使用路由
$useRoute = true;
\Nuwa\Core\Cli::run($useRoute);
