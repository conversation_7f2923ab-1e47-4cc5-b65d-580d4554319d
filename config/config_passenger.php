<?php

use Biz<PERSON>ib\Config as NuwaConfig;
use Disf\SPL\Scheduler\Svc;
use BizLib\Constants\OrderSystem;

$config['config_passenger'] = array(
    'service_phone'                     => '************,8', //客服电话
    'flier_normal_icon'                 => 'https://static.udache.com/gulfstream/api/passenger/fast_icon.png', //滴滴快车车标iconurl
    'flier_light_icon'                  => 'https://static.udache.com/gulfstream/api/passenger/fast_light_icon.png', //滴滴快车车标iconurl
    'flier_normal_md5'                  => 'febfbc5135667ec153d2a332435f9129', //滴滴快车车标iconurl
    'flier_light_md5'                   => '1af08bd2d929f45a6e6a249f36d420ef', // 滴滴快车车标iconurl
    'carpool_route_tips_h5_url'         => 'https://page.udache.com/passenger/apps/cross-city-carpool/index.html',  //跨城区域拼车开通线路地图
    'intercity_carpool_guide_url'       => 'https://page.udache.com/passenger/apps/cross-city-carpool/cross-city-newuser/index.html', // 跨城拼车新手引导
    'get_estimate_price_url'            => 'https://static.udache.com/passenger/apps/price/estimate/index.html', //预估价h5页面url
    'estimate_detail_url'               => 'https://static.udache.com/passenger/apps/price/estimate-v2/index.html', //新预估详情页面url
    'price_rule_url_v2'                 => 'https://page.udache.com/passenger/apps/price/price-rule-v3/index.html', //新版计价规则详情页url
    'station_guide_url'                 => 'https://static.udache.com/passenger/raw/station-tips/index.html',
    'default_station_guide_url'         => 'https://static.udache.com/passenger/raw/station-tips/tips.html',
    'timing_station_guide_url'          => 'https://static.udache.com/passenger/raw/station-tips/tips.html',   //定时定点上车引导页H5
    'timing_station_guide_times'        => 0,   //定时定点上车引导页展示次数限制
    'anycar_guide_url'                  => 'https://page.udache.com/passenger/apps/newuser-anycar/index.html', //anycar新手引导url
    'anycar_guide_times'                => 1,  // anycar新手引导展示次数
    'xiaoba_guide_url'                  => 'https://page.udache.com/passenger/apps/common-user-guide/index.html?menu_id=flash&sub_menu_id=xiaoba',
    'get_station_status_interval'       => 30, //定时定点上车：刷新预匹配时间接口轮训间隔
    'carpool_confirm_show_num'          => 0, //站点拼车新手教育弹窗控制
    'fast_car_invoice_switch'           => 0,
    'dynamic_price_tips'                => NuwaConfig::text('config_passenger', 'dynamic_price_tips'),
    'estimate_price_default_time'       => 120, //专车调用时间间隔
    'estimate_price_fast_car_time'      => 120, //快车调用时间间隔
    'estimate_price_threshold'          => 30, //专车和快车调用预估接口最多次数
    'comment_h5_url'                    => 'https://page.xiaojukeji.com/m/ddPage_0abZniRx.html',
    'driver_info_h5_url'                => 'https://page.udache.com/passenger/apps/driver-homepage-v2/index.html',
    'comment_haohua_h5_url'             => 'https://page.xiaojukeji.com/m/luxuryCarDriver.html', //司机头像
    'reward_h5'                         => 'https://static.udache.com/gulfstream/webapp/modules/carpool-discount/index.html',
    'reward_bubble'                     => NuwaConfig::text('config_passenger', 'reward_bubble'),
    'guide_to_airport_open'             => 1,   //快车订单导入到接送机入口开关 ,默认 1:打开
    'airport_enter_click_report'        => 1,  //接送机入口返卷接口请求开关，默认 1：打开
    'callcar_open'                      => 1,    //代叫车入口开关，默认 1：打开
    'pause_carpool_h5_url'              => 'https://page.udache.com/passenger/apps/modify-carpool/index.html', //乘客端停止拼车H5页面
    'pause_unione_carpool_h5_url'       => 'https://page.udache.com/passenger/apps/modify-carpool/index.html', //出租车乘客端停止拼车
    'special_car_guide_url'             => 'https://page.udache.com/passenger/apps/common-user-guide/index.html?menu_id=flash&sub_menu_id=special',  // 特价车教育h5链接
    'webapp_special_car_guide_url'      => 'https://static.udache.com/passenger/apps/common-user-guide/external/index.js?menu_id=flash&sub_menu_id=special&namespace=passenger&exportname=common-user-guide',
    //发票类型对应关系只允许添加不要修改
    'invoiceType'                       => array(
        NuwaConfig::text('config_passenger', 'fee_daijia'),
    ),
    //投诉类型对应关系只允许添加不要修改
//    'complaintType'    =>  array(
//        '司机迟到',
//        '司机爽约',
//        '计费不合理',
//        '服务态度恶劣',
//    ),
    //投诉类型修改后
    'complaintType'                     => NuwaConfig::text('config_passenger', 'complaintType'),
    //投诉订单场景：等待应答、等待接驾、行程中、待支付、已支付、订单取消
    'complaintOrderScene'               => array(
        1 => array(
            'scene_name'   => '等待应答',
            'order_status' => array(0),
        ),
        2 => array(
            'scene_name'   => '等待接驾',
            'order_status' => array(1, 2),
        ),
        3 => array(
            'scene_name'   => '行程中',
            'order_status' => array(4),
        ),
        4 => array(
            'scene_name'   => '待支付',
            'is_pay'       => 0,
            'order_status' => array(5),
        ),
        5 => array(
            'scene_name'   => '已支付',
            'is_pay'       => 1,
            'order_status' => array(5),
        ),
        6 => array(
            'scene_name'   => '订单取消',
            'order_status' => array(6, 7, 8, 9, 10, 11, 12),
        ),
    ),
    //type为数据库所存类型，index用来排序 控制展示位置
    'complaintTypeNew'                  => array(
        '100'  => array(
            'tag_name' => '服务态度恶劣',
            'type'     => 0,
        ),
        '200'  => array(
            'tag_name' => '未坐车产生费用',
            'type'     => 1,
        ),
        '300'  => array(
            'tag_name' => '停车费、高速费、路桥费等附加费用',
            'type'     => 2,
        ),
        '400'  => array(
            'tag_name' => '路不熟多产生费用',
            'type'     => 3,
        ),
        '500'  => array(
            'tag_name' => '提前计费',
            'type'     => 4,
        ),
        '600'  => array(
            'tag_name' => '未及时结束计费',
            'type'     => 5,
        ),
        '700'  => array(
            'tag_name' => '司机绕路',
            'type'     => 6,
        ),
        '800'  => array(
            'tag_name' => '司机迟到',
            'type'     => 7,
        ),
        '900'  => array(
            'tag_name' => '司机爽约或拒载',
            'type'     => 8,
        ),
        '1000' => array(
            'tag_name' => '司机原因导致行程被取消',
            'type'     => 9,
        ),
        '1100' => array(
            'tag_name' => '骚扰乘客',
            'type'     => 10,
        ),
        '1200' => array(
            'tag_name' => '危险驾驶',
            'type'     => 11,
        ),
        '110'  => array(
            'tag_name' => '不是订单显示车辆',
            'type'     => 12,
        ),
        '1400' => array(
            'tag_name' => '拼车实际价格超过一口价',
            'type'     => 13,
        ),
        '1500' => array(
            'tag_name' => '不认可取消费',
            'type'     => 14,
        ),
        '120'  => array(
            'tag_name' => '不是订单显示司机',
            'type'     => 15,
        ),
    ),
    //评论类型对应关系只允许添加不要修改
    'commentType'                       => NuwaConfig::text('config_passenger', 'commentType'),
    //评论类型对应关系只允许添加不要修改
    'commentTypeFastcar'                => NuwaConfig::text('config_passenger', 'commentTypeFastcar'),
    //取消行程原因
    'cancelType'                        => array(
        'tips'   => NuwaConfig::text('config_passenger', 'cancel_tips'),
        'select' => array(
            array(
                'icon0'  => 'https://static.udache.com/gulfstream/api/passenger/iOS/icon/<EMAIL>', //IOS图标
                'icon1'  => 'https://static.udache.com/gulfstream/api/passenger/android/icon/bus.png', //安卓图标
                'text'   => NuwaConfig::text('config_passenger', 'reason_for_choose_other_transport'),
                'remark' => '',
                'free'   => 1,
            ),
            array(
                'icon0'  => 'https://static.udache.com/gulfstream/api/passenger/iOS/icon/<EMAIL>',
                'icon1'  => 'https://static.udache.com/gulfstream/api/passenger/android/icon/change.png',
                'text'   => NuwaConfig::text('config_passenger', 'reason_for_no_need_for_car'),
                'remark' => '',
                'free'   => 1,
            ),
            array(
                'icon0'  => 'https://static.udache.com/gulfstream/api/passenger/iOS/icon/<EMAIL>',
                'icon1'  => 'https://static.udache.com/gulfstream/api/passenger/android/icon/wait.png',
                'text'   => NuwaConfig::text('config_passenger', 'reason_for_driver_late'),
                'remark' => NuwaConfig::text('config_passenger', 'reason_for_driver_late_remark'),
                'free'   => 1,
            ),
            array(
                'icon0'  => 'https://static.udache.com/gulfstream/api/passenger/iOS/icon/<EMAIL>',
                'icon1'  => 'https://static.udache.com/gulfstream/api/passenger/android/icon/cancel.png',
                'text'   => NuwaConfig::text('config_passenger', 'reason_for_driver_not_come'),
                'remark' => NuwaConfig::text('config_passenger', 'reason_for_driver_not_come_remark'),
                'free'   => 1,
            ),
        ),
    ),
    'p_pay_status_req'                  => '5', // pPayStatus轮询频率(单位秒)
    'p_order_get_req'                   => '8', // pGetOrderStatus轮询频率(单位秒)
    'p_order_detail_req'                => '50', // pGetOrderDetail轮询频率(单位秒)

    'p_get_order_status_spare'          => '10', // pGetOrderStatusSpare(单位秒)
    'p_get_order_status_spare_open'     => 1, // pGetOrderStatusSpare(0是关闭，是打开)

    'p_count_down_time'                 => '8', // 司机抢单倒计时
    'p_push_passenger_msg'              => NuwaConfig::text('config_passenger', 'p_push_passenger_msg'), // 返回给乘客端的文案
    'p_push_consult_msg'                => NuwaConfig::text('config_passenger', 'p_push_consult_msg'), // 协商时间消息推送

    'not_use_local_cache'               => 0, //1读取线上,0 或没有设置 读取缓存
    'p_multi_car_level'                 => 'https://static.udache.com/gulfstream/api/passenger/multi_car/ios_multi_car_icon.png',
    'p_multi_car_level_android'         => 'https://static.udache.com/gulfstream/api/passenger/multi_car/android_multi_car_icon.png',
    'carpool_guide'                     => NuwaConfig::text('config_passenger', 'carpool_guide'),
     //拼车引导新版优化配置(还有一处)
    'carpool_guide_optimize'            => NuwaConfig::text('config_passenger', 'carpool_guide_optimize'),
    'carpool_tips'                      => NuwaConfig::text('config_passenger', 'carpool_tips'),
    'carpool_law_h5_url'                => 'https://static.udache.com/gulfstream/webapp/pages/legalIntro/legalIntro.html',
    'cancel_rule_h5_url'                => 'https://page.udache.com/passenger/apps/cancel-trip/cancel-rule/index.html', //取消规则
    'passenger_cancel_trip_reason_page' => 'https://page.udache.com/passenger/apps/cancel-trip/cancel-reason/index.html', //乘客取消原因页地址
    'cancel_trip_url'                   => 'https://page.udache.com/passenger/apps/cancel-trip/cancel-trip-sec/index.html', //取消行程h5页
    'fee_doubt_h5'                      => 'https://acceptor.xiaojukeji.com/static/acceptor/?channel=10&tagL1=3&tagL2=1094&dictId=11498&kefuSceneId=1460&helpId=1&kefuSceneTitle=我不认可取消费&helpTitle=我不认可取消费&fromPage=cancel_pay_info&customType=1&showRule=true', //乘客取消费用疑问页面
    'fee_detail_h5'                     => 'https://static.udache.com/passenger/apps/price/view-details/index.html', //乘客查看明细h5
    // 因为需要区分国内国外，该配置已经移到biz-config/config/production/config_h5_url.php下
    'real_time_fee_detail_url'          => 'https://page.udache.com/passenger/apps/price/view-details-v2/index.html',    //行程中费用明细页url
    //'p_complaint_url'                   => 'https://static.udache.com/passenger/apps/complaint/index.html', //投诉h5
    'p_complaint_url'                   => 'https://help.xiaojukeji.com/static/psgComplaintFlow.html?source=app_complaint_xcz',
    //乘客迟到费说明页
    'p_late_fee_rule_url'               => 'https://page.udache.com/passenger/apps/late-price-intro/index.html', //乘客迟到费说明页

    //支付乘客标签状态
    'status_service'                    => array(
        'get_timeout' => 200, //ms
        'set_timeout' => 200, //ms
        //>>> 'url' => 'http://${ROUTER_COMMONSHARE_IP_PORT}/foundation/commonshare/v1/pTagService/',
        'url'         => 'https://************:8000/foundation/commonshare/v1/pTagService/',
        //<<<
    ),
    'coupon_bubble_tips'                => NuwaConfig::text('config_passenger', 'coupon_bubble_tips'), //拼车行程中价格优化文案
    'coupon_detail_tips'                => NuwaConfig::text('config_passenger', 'coupon_detail_tips'), //拼车行程中气泡点击进入页面的价格优化文案
    //是否显示接送机入口
    'b_show_airport'                    => 1,
   //4.2.1版入口图标
    'airport_normal_icon'               => 'https://static.udache.com/gulfstream/webapp/modules/airport-insurance/api-imgs/<EMAIL>',
    'airport_click_icon'                => 'https://static.udache.com/gulfstream/webapp/modules/airport-insurance/api-imgs/<EMAIL>',
   //4.2.0版入口图标
    'airport_normal_icon_android'       => 'https://static.udache.com/gulfstream/webapp/modules/airport-insurance/api-imgs/<EMAIL>',
    'airport_normal_icon_ios'           => 'https://static.udache.com/gulfstream/webapp/modules/airport-insurance/api-imgs/<EMAIL>',
    'airport_click_icon_android'        => 'https://static.udache.com/gulfstream/webapp/modules/airport-insurance/api-imgs/<EMAIL>',
    'airport_click_icon_ios'            => 'https://static.udache.com/gulfstream/webapp/modules/airport-insurance/api-imgs/<EMAIL>',
   //春节49元接送机
    'airport_festival_jsj_normal'       => 'https://static.udache.com/gulfstream/webapp/modules/airport-insurance/api-imgs/festival_jiesongji_01.png',
    'airport_festival_jsj_click'        => 'https://static.udache.com/gulfstream/webapp/modules/airport-insurance/api-imgs/festival_jiesongji_02.png',
    'airport_festival_jj_normal'        => 'https://static.udache.com/gulfstream/webapp/modules/airport-insurance/api-imgs/festival_jieji_01.png',
    'airport_festival_jj_click'         => 'https://static.udache.com/gulfstream/webapp/modules/airport-insurance/api-imgs/festival_jieji_02.png',

        //评价优化
    'car_evaluate_standard_url'         => '',
    'car_evaluate_redirect_url'         => '',
    'extraInfo'                         => array(
        array(
            'type'    => 0,
            'product' => 307,
            'data'    => array(
                '开双闪等我',
                '愿等15分钟',
                '不爱聊天',
            ),
        ),
        array(
            'type'    => 1,
            'product' => 307,
            'data'    => array(
                '时间可商量',
                '开双闪等我',
                '行李多',
                '打车往返程',
                '不爱聊天',
            ),
        ),
    ),

    //司乘取证录音配置信息
    'voice_evidence_conf'               => array(
        'upload_url'                => 'https://api.udache.com/gulfstream/csi/v1/service/voice/pSubmitVoiceEvidence',
        'order_max_num'             => 3, //单订单最大录音数
        'order_max_length'          => 60, //单订单最大录音长度 minutes
        'max_retry'                 => 10, //上传失败最大重试次数
        'max_save_day'              => 14, //单录音文件在端上最大保存时间
        'auto_split_length'         => 5, // 单录音文件自动分割长度 单位分钟
        'driver_day_max_upload_num' => 5, //单司机每天最多录音订单/行程（拼车）数
    ),
);
//service discovery
Svc::discoverHttpUrl(
    'disf!os-volcano-share_web',
    '/foundation/commonshare/v1/pTagService/',
    Svc::thenUpdate($config['config_passenger']['status_service']['url'])
);

//拼车引导新版优化配置（还有一处）
//动态调价发单前确认页信息 key 为动态调价类型
//接送机引导配置
$config['airport_guide_flag']     = 0;
$config['airport_guide_optimize'] = NuwaConfig::text('config_passenger', 'airport_guide_optimize');

//代叫车崩溃恢复白名单乘客手机号
//代叫车测试白名单
// 订单加密频率控制配置
$config['encrypt_oid_freq_block_time'] = 300; // 拉黑时间,单位:秒
$config['encrypt_oid_freq_plan']       = '2000/60';  // 频率控制,单位:次数/秒数,500/60表示60秒内,最多500次访问

$config['neworder_wait_msg_line_1'] = NuwaConfig::text('config_passenger', 'neworder_wait_msg_line_1');
$config['neworder_wait_msg_line_2'] = NuwaConfig::text('config_passenger', 'neworder_wait_msg_line_2');
$config['neworder_wait_msg_line_3'] = NuwaConfig::text('config_passenger', 'neworder_wait_msg_line_3');

//接送机航班号显示开关
$config['airport_number_switch'] = 0;
//webapp根据eta是否有司机，阻断发实时单功能，白名单城市列表
//按照不同产品线设立乘客端基础配置
$config['passenger_origin_config'] = array(
    OrderSystem::ORIGIN_ID_UBER => array(
        'cancel_trip_url'                   => 'https://page.udache.com/passenger/apps/cancel-trip-new/index.html',
        'passenger_cancel_trip_reason_page' => 'https://page.udache.com/passenger/apps/cancel-reason-new/index.html',
    ),
);

return $config;
