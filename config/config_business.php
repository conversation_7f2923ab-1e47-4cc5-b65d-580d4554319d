<?php

use Disf\SPL\Scheduler\Svc;

$config['api_info'] = array(
    'appId'     => 'zhuanche_app',
    'appKey'    => 'KLJdv4565%&++',
    //>>> 'baseUrl'   => 'http://${ROUTER_B2B_API_IP_PORT}/zeus/iapi/CompanyPay/v1',
    'baseUrl'   => 'http://*************:8000/zeus/iapi/CompanyPay/v1',
    //<<<
    //企业机房迁移,400ms临时改成1.5s
    'timeout'   => 1500, //ms
);
$config['timeout_info'] = array(
    'isVip'	                => 50,
    'getUserPayPermission'	 => 70,
    'newOrder'	             => 300,
    'reimbursement'	        => 200,
);
Svc::discoverHttpUrl(
    'disf!b2b-api',
    '/crius/iapi/CompanyPay/v1',
    Svc::thenUpdate($config['api_info']['baseUrl'])
);
// use_car_type = 2 专车, 1出租
return $config;
