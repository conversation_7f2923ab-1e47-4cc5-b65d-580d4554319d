<?php
/**
 *
 * Copyright (c) 2019 xiaojukeji.com, Inc. All Rights Reserved.
 * @Author: wa<PERSON><PERSON><PERSON><PERSON><PERSON>@didichuxing.com
 * @Date: 2019/5/12 下午4:33
 * @Desc:
 * @wiki:
 *
 */

$config['loss_remand_new_order_check'] = [
    'antispam'              => [
        'distance'                   => 1000, // 起终点距离阈值
        'same_driver_order_cnt'      => 1, // 乘客每月只能对同一个司机发单一次
        'passenger_finish_order_cnt' => 2, // 每个乘客每月最多只能发起2张遗失物品订单
        'driver_finish_order_cnt'    => 3, // 每个司机每月最多可接起3张遗失物品订单
    ],
    'concurrency_lock_time' => 70, // 由于支付超时为60s，这里考虑支付回调的延时，设置锁的时间为70s
];

return $config;
