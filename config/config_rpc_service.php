<?php
/***************************************************************************
 *
 * Copyright (c) 2016, Inc. All Rights Reserved
 *
 **************************************************************************/

use Disf\SPL\Scheduler\Svc;

/*
 * @file   config_rpc_service.php
 * <AUTHOR>
 * @date   2016/03/09
 * @desc   rpc远程调用相关配置
 *
 **/
$config['service_list'] = array(
    'passenger'               => array(
        //>>>    'remote_host' =>'${ROUTER_GS_ORDER_IP_PORT}',
        'remote_host'     => '*************:8000',
        //<<<
        'local_host'      => '127.0.0.1:8200',
        'prefix'          => '/gulfstream/passenger/v2/',
        //>>> 'timeout'    => '${ROUTER_GS_ORDER_TIMEOUT}',
        'timeout'         => 500,
        //<<<
        //>>> 'connect_timeout'    => '${ROUTER_GS_ORDER_CONNECT_TIMEOUT}',
        'connect_timeout' => 200,
        //<<<
        'retry'           => 1,
    ),
    'order-system'            => array(
        //>>> 'remote_host'    => '${ROUTER_GS_ORDER_SYSTEM_IP_PORT}',
        'remote_host'     => '**************:8000',
        //<<<
        'local_host'      => '127.0.0.1:8300',
        'prefix'          => '/gulfstream/order-system/v2/',
        //>>> 'timeout'    => '${ROUTER_GS_ORDER_SYSTEM_TIMEOUT}',
        'timeout'         => 500,
        //<<<
        //>>> 'connect_timeout'    => '${ROUTER_GS_ORDER_SYSTEM_CONNECT_TIMEOUT}',
        'connect_timeout' => 200,
        //<<<
        'retry'           => 1,
    ),
    'order'                   => array(
//>>>    'remote_host' =>'${ROUTER_GS_ORDER_IP_PORT}',
        'remote_host'     => '**************:8000',
//<<<
        'local_host'      => '127.0.0.1:8200',
        'prefix'          => '/gulfstream/order/v2/',
        //>>> 'timeout'    => '${ROUTER_GS_ORDER_TIMEOUT}',
        'timeout'         => 500,
        //<<<
        //>>> 'connect_timeout'    => '${ROUTER_GS_ORDER_CONNECT_TIMEOUT}',
        'connect_timeout' => 200,
        //<<<
        'retry'           => 1,
    ),
    'driver'                  => array(
//>>>    'remote_host' =>'${ROUTER_GS_DRIVER_IP_PORT}',
        'remote_host'     => '**************:8000',
//<<<
        'local_host'      => '127.0.0.1:8100',
        'prefix'          => '/gulfstream/driver/v2/',
        //>>> 'timeout'    => '${ROUTER_GS_DRIVER_TIMEOUT}',
        'timeout'         => 500,
        //<<<
        //>>> 'connect_timeout'    => '${ROUTER_GS_DRIVER_CONNECT_TIMEOUT}',
        'connect_timeout' => 200,
        //<<<
        'retry'           => 1,
    ),
    'cashier'                 => array(
//>>>    'remote_host' =>'${ROUTER_GS_PAY_IP_PORT}',
        'remote_host'     => '**************:8000',
//<<<
        'prefix'          => '/gulfstream/pay/v1/',
        'local_host'      => '127.0.0.1:8000',
        //>>> 'timeout'    => '${ROUTER_GS_PAY_TIMEOUT}',
        'timeout'         => 2000,
        //<<<
        //>>> 'connect_timeout'    => '${ROUTER_GS_PAY_CONNECT_TIMEOUT}',
        'connect_timeout' => 1000,
        //<<<
        'retry'           => 1,
    ),
    'cashier-checkPayAbility' => array(
//>>>    'remote_host' =>'${ROUTER_GS_PAY_IP_PORT}',
        'remote_host'     => '**************:8000',
//<<<
        'prefix'          => '/gulfstream/pay/v1/',
        'local_host'      => '127.0.0.1:8000',
        //>>> 'timeout'    => '${ROUTER_GS_PAY_TIMEOUT}',
        'timeout'         => 260,
        //<<<
        //>>> 'connect_timeout'    => '${ROUTER_GS_PAY_CONNECT_TIMEOUT}',
        'connect_timeout' => 100,
        //<<<
        'retry'           => 0,
    ),
    'get_pay_result'          => array(
//>>>    'remote_host' =>'${ROUTER_GS_PAY_IP_PORT}',
        'remote_host'     => '**************:8000',
//<<<
        'prefix'          => '/gulfstream/pay/v1/',
        'local_host'      => '127.0.0.1:8000',
        //>>> 'timeout'    => '${ROUTER_GS_PAY_TIMEOUT}',
        'timeout'         => 60,
        //<<<
        //>>> 'connect_timeout'    => '${ROUTER_GS_PAY_CONNECT_TIMEOUT}',
        'connect_timeout' => 100,
        //<<<
        'retry'           => 0,
    ),
    'write_order_resultInfo'  => array(
//>>>    'remote_host' =>'${ROUTER_GS_PAY_IP_PORT}',
        'remote_host'     => '**************:8000',
//<<<
        'prefix'          => '/gulfstream/pay/v1/',
        'local_host'      => '127.0.0.1:8000',
        //>>> 'timeout'    => '${ROUTER_GS_PAY_TIMEOUT}',
        'timeout'         => 300,
        //<<<
        //>>> 'connect_timeout'    => '${ROUTER_GS_PAY_CONNECT_TIMEOUT}',
        'connect_timeout' => 100,
        //<<<
        'retry'           => 0,
    ),

    'carpool'                 => array(
//>>>    'remote_host' =>'${ROUTER_GS_CARPOOL_IP_PORT}',
        'remote_host'     => '**************:8000',
//<<<
        'prefix'          => '/gulfstream/carpool/v1/',
        'local_host'      => '127.0.0.1:8000',
        //>>> 'timeout'    => '${ROUTER_GS_CARPOOL_TIMEOUT}',
        'timeout'         => 500,
        //<<<
        //>>> 'connect_timeout'    => '${ROUTER_GS_CARPOOL_CONNECT_TIMEOUT}',
        'connect_timeout' => 200,
        //<<<
        'retry'           => 1,
    ),
    'api'                     => array(
//>>>    'remote_host' =>'${ROUTER_GS_API_IP_PORT}',
        'remote_host'     => '**************:8000',
//<<<
        'local_host'      => '127.0.0.1:8000',
        'prefix'          => '/gulfstream/api/v1/',
        //>>> 'timeout'    => '${ROUTER_GS_API_TIMEOUT}',
        'timeout'         => 500,
        //<<<
        //>>> 'connect_timeout'    => '${ROUTER_GS_API_CONNECT_TIMEOUT}',
        'connect_timeout' => 200,
        //<<<
        'retry'           => 1,
    ),
    'themis'                  => array(
//>>>    'remote_host' =>'${ROUTER_GS_THEMIS_IP_PORT}',
        'remote_host'     => '**************:8000',
//<<<
        'local_host'      => '127.0.0.1:8000',
        'prefix'          => '/gulfstream/themis/v2/',
        //>>> 'timeout'    => '${ROUTER_GS_THEMIS_TIMEOUT}',
        'timeout'         => 300,
        //<<<
        //>>> 'connect_timeout'    => '${ROUTER_GS_THEMIS_CONNECT_TIMEOUT}',
        'connect_timeout' => 200,
        //<<<
        'retry'           => 1,
    ),

    'catalog'                 => array(
//>>>    'remote_host' =>'${ROUTER_GS_PLUTUS_IP_PORT}',
        'remote_host'     => '**************:8000',
//<<<
        'local_host'      => '127.0.0.1:8000',
        'prefix'          => '/gulfstream/plutus/catalog/',
         //>>> 'timeout'    => '${ROUTER_GS_PLUTUS_TIMEOUT}',
        'timeout'         => 200,
        //<<<
         //>>> 'connect_timeout'    => '${ROUTER_GS_PLUTUS_CONNECT_TIMEOUT}',
        'connect_timeout' => 100,
        //<<<
        'retry'           => 0,
    ),

    'minos'                   => array(
//>>>    'remote_host' =>'${ROUTER_GS_MINOS_IP_PORT}',
        'remote_host'     => '**************:8000',
//<<<
        'local_host'      => '127.0.0.1:8500',
        'prefix'          => '/gulfstream/minos/v1/',
         //>>> 'timeout'    => '${ROUTER_GS_MINOS_TIMEOUT}',
        'timeout'         => 200,
        //<<<
         //>>> 'connect_timeout'    => '${ROUTER_GS_MINOS_CONNECT_TIMEOUT}',
        'connect_timeout' => 50,
        //<<<
        'retry'           => 0,
    ),
    'koprpc'                  => array(
        'timeout_ms'            => 200,
        'connection_timeout_ms' => 100,
    ),
);

Svc::discoverEndpoint(
    'disf!biz-gs-biz_passenger',
    function ($endpoint) use (&$config) {
        $config['service_list']['passenger']['remote_host'] = $endpoint['ip'].':'.strval($endpoint['port']);
    }
);
Svc::discoverEndpoint(
    'disf!biz-gs-order_system',
    function ($endpoint) use (&$config) {
        $config['service_list']['order-system']['remote_host'] = $endpoint['ip'].':'.strval($endpoint['port']);
    }
);
Svc::discoverEndpoint(
    'disf!biz-gs-driver',
    function ($endpoint) use (&$config) {
        $config['service_list']['driver']['remote_host'] = $endpoint['ip'].':'.strval($endpoint['port']);
    }
);
Svc::discoverEndpoint(
    'disf!common-plat-gs-pay',
    function ($endpoint) use (&$config) {
        $config['service_list']['cashier']['remote_host'] = $endpoint['ip'].':'.strval($endpoint['port']);
        $config['service_list']['cashier-checkPayAbility']['remote_host'] = $endpoint['ip'].':'.strval($endpoint['port']);
        $config['service_list']['get_pay_result']['remote_host'] = $endpoint['ip'].':'.strval($endpoint['port']);
        $config['service_list']['write_order_resultInfo']['remote_host'] = $endpoint['ip'].':'.strval($endpoint['port']);
    }
);
Svc::discoverEndpoint(
    'disf!biz-gs-carpoolweb',
    function ($endpoint) use (&$config) {
        $config['service_list']['carpool']['remote_host'] = $endpoint['ip'].':'.strval($endpoint['port']);
    }
);
Svc::discoverEndpoint(
    'disf!biz-gs-api',
    function ($endpoint) use (&$config) {
        $config['service_list']['api']['remote_host'] = $endpoint['ip'].':'.strval($endpoint['port']);
    }
);
Svc::discoverEndpoint(
    'disf!biz-gs-themis',
    function ($endpoint) use (&$config) {
        $config['service_list']['themis']['remote_host'] = $endpoint['ip'].':'.strval($endpoint['port']);
    }
);
Svc::discoverEndpoint(
    'disf!commonplat-gs-plutus',
    function ($endpoint) use (&$config) {
        $config['service_list']['catalog']['remote_host'] = $endpoint['ip'].':'.strval($endpoint['port']);
    }
);
Svc::discoverEndpoint(
    'disf!biz-gs-minos',
    function ($endpoint) use (&$config) {
        $config['service_list']['minos']['remote_host'] = $endpoint['ip'].':'.strval($endpoint['port']);
    }
);

return $config;

/* vim: set et ts=4 sw=4 sts=4 tw=100: */
