<?php

use Disf\SPL\Scheduler\Svc;

/*
 * Constants for Biz.
 *
 * Biz initialize at app/bootstrap/InitializeBiz.php
 */
define('MODULE_NAME', 'pre-sale');
define('APPPATH', APP_PATH);
define('BIZ_LIB_CIPATH', PROJ_PATH.'vendor/biz/biz-lib/ci/');
define('BIZ_CONFIG', '/home/<USER>/webroot/gulfstream/application/biz-config/');
define('LOG_PATH', '/home/<USER>/webroot/gulfstream/log/pre-sale/');

//司机端
define('D_BASIC_INFO', 'd_basic_info');
//司机基础信息
define('D_EXT_INFO', 'd_ext_info');
//司机扩展信息
define('D_CONF_INFO', 'd_conf_info');
//司机配置信息
define('D_GETDRIVERIDBYPHONE', 'd_getdriveridbyphone');
//通过手机号获取driver_id
define('D_PUSH_TIME', 'd_push_time');
//根据司机ID记录推送时间
define('D_PUSH_ORDER_TIME', 'd_push_order_time');
//记录给司机推送订单的时间
define('D_JIASUQI_WHITE_LIST', 'd_jiasuqi_white_list');
//记录给司机推送订单的时间
define('D_IDCARD_INFO', 'd_idcard_info');
//通过司机ID获取身份证相关信息
define('D_IDCARD', 'd_idcard');
//通过司机ID获取身份证号
define('D_CONFIRM_CODE_INFO', 'd_confirm_code_info');
//验证码相关信息
define('D_SENDCODE', 'd_sendcode');
//记录验证码
define('D_COMPANY_BASIC_INFO', 'd_company_basic_info');
//记录司机所属公司基本信息
define('D_NATIONAL_COMPANY_BASIC_INFO', 'd_national_company_basic_info');
//记录国际化公司基本信息
define('D_LOCATION', 'd_location');
//记录经纬度
define('D_APP_VERSION', 'd_app_version');
//记录司机版本信息
define('D_CAR_BASIC_INFO', 'd_car_basic_info');
//记录司机所开车辆基本信息
define('D_CARID', 'd_carid');
//记录司机所开车辆id
define('D_PHOTO', 'd_photo');
//记录司机图像
define('D_PHOTO_V2', 'd_photo_v2');
//记录司机图像
define('D_PHOTO_V3', 'd_photo_v3');
//记录司机图像
define('D_PHOTO_V4', 'd_photo_v4');
//记录司机图像
define('D_RESENDORDER_FILTER', 'd_resendorder_filter');
//司机已改派订单，重新播报订单对该司机过滤
define('D_FIND_DRIVER_NUM', 'd_find_driver_num');
//获得司机数目
define('D_GETDRIVERIDBYIDCARDNO', 'd_getdriveridbyidcarno');
//通过身份证号获取driver_id
define('D_CHANGE_PWD_ERROR_TIMES', 'd_change_pwd_error_times');
//司机修改密码错误次数+phone
define('D_GET_BY_PASSPORT_UID', 'd_get_by_passort_uid');
//通过passport的uid获取司机信息+uid
define('D_GET_DAY_INFO', 'd_get_day_info');
// 获取当天在线时长
define('D_SET_DAY_INFO_TIMESTAMP', 'd_set_day_info_timestamp');
// 设置在线时长的起始时间戳
define('D_TOTALFEE', 'd_totalfee');
// 设置司机总金额
define('D_CURRENT_ORDER_NUM', 'd_current_order_num');
// 司机当天抢单数
define('D_FORBIDDEN_ONE_DAY', 'd_forbidden_one_day');
// 司机封禁一天
define('D_HONOR_LIST', 'd_honor_list');
//司机荣誉榜,根据平均星级计算排序
define('D_LONG_RENT_ORDER_NUM', 'd_long_rent_order_num');
// 长包车当天抢单数
define('D_GETCARIDBYPLATENO', 'd_getcaridbyplateno');
//通过车牌号获取车辆id
define('D_GETDRIVERLISTBYCARID', 'd_getdriverlistbycarid');
//通过车辆id获取司机id
define('D_RANK_ORDER_NUM_AREA', 'd_rank_order_num_area');
//司机排行榜
define('D_MONTH_ORDER_COST', 'd_month_order_cost');
//获取司机某月的订单费用
define('NEW_D_MONTH_ORDER_COST', 'new_d_month_order_cost');
//司乘计价分离后,获取司机某月的订单费用
define('D_MONTH_LEVEL', 'd_month_level');
//获取司机某月星级
define('D_REALTIMEPRICING_LAST_FEE_LOCK', 'd_realtimepricing_last_fee_lock');
// 实时计价上次费用加锁
define('D_FINISH_ORDER_LOCK', 'd_finish_order_lock');
// 完成计费加锁
define('D_RECHARGE_LOCK', 'd_recharge_lock');
// 司机代充值加锁
define('D_WITHDRAW_CNT', 'd_withdraw_cnt');
// 司机提现次数限制
define('D_WITHDRAW_MONEY', 'd_withdraw_money');
// 司机提现金额限制
define('D_WITHDRAW_LOCK', 'd_withdraw_lock');
// 司机提现加锁
define('D_CITY_PRICE', 'd_city_price');
// 城市计价配置
define('D_CITY_CAR_PRICE', 'd_city_car_price');
//城市车型计价配置
define('D_CITY_COMBO_TITLE', 'd_city_combo_title');
// 城市套餐
define('D_CITY_CAR_DEFAULT_PRICE', 'd_city_car_default_price');
//城市默认车型计价配置
define('D_PHONE_VERIFY', 'd_phone_verify');
//司机注册手机验证码
define('D_PHONE_VERIFY_LIMIT', 'd_phone_verify_limit');
//司机注册手机验证限制
define('D_SEND_CODE_LIMIT', 'd_send_code_limit');
//司机每天发送验证码次数限制
define('D_STRIVE_ORDER_DRIVER_CNT', 'd_strive_order_driver_cnt');
// 记录抢单司机数（供鱼鹰使用）
define('D_COUNTDOWN_TIME', 'd_countdown_time');
// 司机倒计时时间
define('D_CITY_DEFAULT_CAR_LEVEL', 'd_city_default_car_level');
// 获得城市对应的默认车型
define('D_LITTLE_DRIVER_UPDATE_WHITELIST', 'd_little_driver_update_whitelist');
// 司机小流量升级白名单
define('D_WITHDRAW_NOTIFY', 'd_withdraw_notify');
// 提现回调通知
define('D_WITHDRAW_INFO', 'd_withdraw_info');
// 司机提现加锁
define('D_SIGNED_DAY_IMEI', 'd_signed_day_imei');
//地网-识别此imei号今日是否签到过
define('D_SIGNED_DAY_FLAG', 'd_signed_day_flag');
//地网-今日是否签到
define('D_SIGNED_WEEK_COUNT', 'd_signed_week_count');
//地网-本周签到数
define('D_SIGNED_MONTH_COUNT', 'd_signed_month_count');
//地网-本月签到数
define('D_HONOR_CITY_LIST', 'd_honor_city_list');
//荣誉榜城市设置//订单
define('D_LONGRENT_COMMISSION_CFG', 'd_longrent_commission_cfg');
// 长包车任务配置
define('D_SEPARATE_CFG', 'd_separate_cfg');
// 分账配置
define('D_SEPARATE_QUERY_INFO', 'd_separate_query_info');
define('D_BEGIN_CHARGE_LOCK', 'd_begin_charge_lock');
// 开始计费加锁
define('D_WITHDRAW_TAX_CFG', 'd_withdraw_tax_cfg');
// 提现税率配置
define('D_CITY_PRICE_BY_STRATEGY', 'd_city_price_by_strategy');
// 策略计价
define('D_PANGU_DAY_ASSIGN', 'd_pangu_day_assign');
//司机盘古活动指派完成单数、完成率
define('D_PANGU_DAYS_ASSIGN', 'd_pangu_days_assign');
//司机盘古活动指派完成单数、完成率
define('D_DRIVER_STAR_FROM_CREDIT', 'd_driver_star_from_credit');
//司机从信用系统获取到的星级
define('M_PANGU_CONFIG', 'm_pangu_config');
//盘古活动
define('M_PANGU_RULE', 'm_pangu_rule');
//盘古活动下的配置
define('M_PANGU_RULE_DB_INFO', 'm_pangu_rule_db_info');
//盘古活动下的配置
define('M_PANGU_REWARD', 'm_pangu_reward');
//盘古活动下的奖励
define('D_MENUS_DEFAULT', 'd_menus_default');
//司机端默认菜单缓存
define('D_MENUS_BY_AREA', 'd_menus_by_area');
define('D_RECORD_LATEST_COMMENT_TAGS', 'd_record_latest_comment_tags');
//存储乘客评价司机的最近N条评论标签
define('D_HIGHEST_FREQUENCY_COMMENT_TAGS', 'd_highest_frequency_comment_tags');
//存储乘客评级司机的频次最高的N条评价标签以及相应评价次数
define('D_ORDER_DRIVER_COMMENT_PASSENGER_LOCK', 'd_order_driver_comment_passenger_lock');
//司机评价乘客
define('D_SEPARATE_SPLIT_CFG', 'd_separate_split_cfg');
// 分账配置
define('D_QRCODE_INFO', 'd_qrcode_info');
//司机二维码信息
define('D_WEEK_REASSIGN_ORDER_DUTY_NUM', 'd_week_reassign_order_duty_num');
//司机每周有责改派订单数目
define('D_REASSIGN_ORDER_DUTY_TOTAL_NUM', 'd_reassign_order_duty_total_num');
//司机总的有责改派次数
define('D_MEET_PASSENGER_EXEMPT_NUM', 'd_meet_passenger_exempt_num');
//司机遇到乘客免责取消次数
define('O_FINISH_COORDINATES', 'o_finish_coordinates');
//完成订单轨迹
define('D_DRIVER_ASSIGN_RATE', 'd_driver_assign_rate');
//司机本周指派完成率
define('D_NEW_DRIVER_PROGRESS_BAR_SHOW', 'd_new_driver_progress_bar_show');
//新司机是否已打开分层进度条
define('D_ORDER_NUM', 'd_order_num');
//荣誉榜城市设置//订单
define('D_DRIVER_PERWEEK_GET_QUESTION_NUM', 'd_driver_perweek_get_question_num');
//荣誉榜城市设置//订单
define('D_RESEND_ORDER_STATUS_STORED', 'd_resend_order_status_stored');
// 存储改派订单状态
define('D_RESEND_ORDER_STATUS_STORED_WEEK', 'd_resend_order_status_stored_week');
//存储改派订单状态1周，为企业使用
define('D_FREERIDE_INFO', 'd_freeride_info');
//顺风车信息
define('D_FREERIDE_REMIND_INFO', 'd_freeride_remind_info');
//顺风车提醒 +driver_id
define('D_WITHDRAW_DAY_REMIND', 'd_withdraw_day_remind');
//司机提现日提醒
define('D_COUPON_OUT_OF_TIME', 'd_coupon_out_of_time');
//司机券过期提醒
define('D_NEW_PERCENT_COMING', 'd_new_percent_coming');
//有新的分账到了提醒
define('D_DRIVER_LEVEL_GROWTH', 'd_driver_level_growth');
//司机等级与成长值之间的关系
define('D_BIND_COUPONS_LIST', 'd_bind_coupons_list');
//司机查看绑定券
define('D_LAST_SEND_SMS_TIME', 'd_last_send_sms_time');
//司机上次催款成功给乘客发送短信的时间
define('D_LAST_FINISH_ORDER_TIME', 'd_last_finish_order_time');
//司机最近一次完成订单时间信息
define('D_DRIVER_INSERVICE_STATE', 'd_driver_inservice_state');
//司机服务中的状态记录（统计成长值用）
define('D_LOWEST_DEDUCT_LEVEL_GROWTH', 'd_lowest_deduct_level_growth');
//记录司机等级中最低一个开始不扣分等级的分数值
// 司机端查询模块
define('D_EARNINGS_INQUIRY_WEEK', 'd_earnings_inquiry_week');
// 司机流水查询周数据
define('D_EARNINGS_INQUIRY_DAY', 'd_earnings_inquiry_day');
// 司机流水查询周数据
//raptor
define('D_RAPTOR_LOGIN', 'd_raptor_login');
// 存储司机是否首次登陆
define('D_RAPTOR_CONFIG', 'd_raptor_config');
// 存储城市配置信息
define('D_RAPTOR_TASK', 'd_raptor_task');
// 存储司机任务信息
define('D_RAPTOR_ORDER_TOTAL', 'd_raptor_order_total');
// 存储司机任务信息
define('D_RAPTOR_RANK', 'd_raptor_rank');
// 存储司机任务信息
define('D_RAPTOR_RANK_TOP', 'd_raptor_rank_top');
// 存储司机任务信息
define('D_RAPTOR_TRANSFER_LOCK', 'd_raptor_transfer_lock');
// 存储司机任务信息
define('D_RAPTOR_DISCONNECT', 'd_raptor_disconnect');
// 存储司机任务信息
define('D_RAPTOR_REMIND', 'd_raptor_remind');
// 存储司机任务信息
define('D_CITY_HONOUR_LIST', 'd_city_honour_list');
// 司机荣誉榜缓存
define('D_ANONY_COMMENT', 'd_anony_comment');
//匿名评价
define('D_DISTRICT_USING_ACTIVITY', 'd_district_using_activity');
//查询模块的  活动之历史活动
define('D_DISTRICT_HISTORY_ACTIVITY', 'd_district_history_activity');
//查询模块的  活动之正在进行的活动
define('D_DISTRICT_PANGU_USING_ACTIVITY', 'd_district_pangu_using_activity');
//盘古mis修改
define('D_DISTRICT_PANGU_HISTORY_ACTIVITY', 'd_district_pangu_history_activity');
//盘古mis修改 历史
define('D_ORDER_RESEND_CNT', 'd_order_resend_cnt');
//司机订单改派的次数
//ticket缓存
define('D_CHECK_TICKET_CACHE', 'd_check_ticket_cache');
//司机ticket增加缓存，降低passport请求量
define('D_RECHARGE_CACHE_LOCK', 'd_recharge_cache_lock');
//司机代充值锁
//司机异手机登录
define('D_DRIVER_LATEST_DEVICES', 'd_driver_latest_devices');
define('D_DRIVER_FREEZE', 'd_driver_freeze');
define('D_DRIVER_CAPTCHA_ERROR_NUM', 'd_driver_captcha_error_num');
define('D_DRIVER_CAPTCHA', 'd_driver_captcha');
define('D_DISTRICT_CAR_LEVELS', 'd_district_car_levels');
//城市开通车型
define('D_BRAND_NAME', 'd_brand_name');
//车辆品牌.车系名
define('D_HISTORY_ORDER_BY_DRIVER_ID', 'd_history_order_by_driver_id');
define('D_PAUSE_CARPOOL', 'd_pause_carpool');
define('P_GET_BARRENGE','p_get_barrenge');
//司机目的地订单
define('D_DEST_EDIT_NUM', 'd_dest_edit_num');
//每天司机修改的目的地次数
//司机请求交班时间
define('D_JIAOBAN_REQ_TIME', 'd_jiaoban_req_time');
//司机请求交班的时间控制定时队列数量
define('D_JIAOBAN_REQ_LOCK', 'd_jiaoban_req_lock');
//司机请求交班请求锁
define('D_NOT_ASSIGN_DEST_EDIT_NUM', 'd_not_assign_dest_edit_num');
//抢单模式下每天司机修改的目的地次数
define('D_FIRST_ON_CAR_EVERY_DAY', 'd_first_on_car_every_day');
//司机每天第一次出车
define('D_CARPOOL_DRIVER_ORDER', 'd_carpool_driver_order');
// 拼车司机订单
define('D_PREDICT_NEAR_HOT_TIME', 'd_predict_neat_hot_time');
// 设置司机播放预测热力图失效
define('D_PREDICT_NEAR_HOT_WHITELIST', 'd_predict_neat_hot_whitelist');
// 设置司机播放预测热力图白名单
define('D_FINISH_ORDER_BROAD_WHITELIST', 'd_finish_order_broad_whitelist');
// 司机结束计费是否可播放set
define('D_PREDICT_NEAR_HOT_DISTANCE', 'd_predict_neat_hot_distance');
// 设置司机预测热力图距离
define('O_INFO', 'o_info');
//订单信息
define('O_BASIC_INFO', 'o_basic_info');
//订单信息
define('O_EXTRA_INFO', 'o_extra_info');
//订单信息
define('O_DRIVER_PULL_FLAG', 'o_driver_pull_flag');
//司机拉单操作记录
define('O_PUSH_INFO', 'o_push_info');
//与策略耦合的, 给未抢单的司机推送'订单已被抢'的消息
define('O_ORDER_RESULT_INFO', 'o_order_result_info');
// 订单计价信息
define('O_ORDER_RESULT_DRIVER_INFO', 'o_order_result_driver_info');
// 订单计价信息
define('O_ORDER_CMP_INFO', 'o_order_cmp_info');
// 订单评价信息
define('O_ORDER_REALTIME_VALUATION_INFO', 'o_order_realtime_valuation_info');
define('O_ORDER_LAST_POINT', 'o_order_last_point');
// 上传坐标最后一个点
define('O_ORDER_PUNISH_FORWARD', 'o_order_punish_forwrd');
// 订单惩罚奖励+order_id
define('O_ORDER_RECALL_LIMIT', 'o_order_recall_limit');
//订单重叫时间限制
define('O_GETRESENDDRIVERIDBYORDERID', 'o_getresenddriveridbyorderid');
//根据订单获得改派司机ID
define('O_GETORDERID_BY_DISTANCE', 'o_getorderid_by_distance');
// 根据距离获得订单ID
define('O_LATE', 'o_late');
//预计司机迟到的订单id +district
define('O_PUSH_LOG', 'o_push_log');
//订单状态实时推 id-district
define('O_TIME_DISTANCE', 'o_time_distance');
//行驶时间,距离超时id +district
define('O_ORDER_ETA', 'o_order_eta');
// ETA时间
define('O_REALTIME_PRICING_NETWORK', 'o_realtime_pricing_network');
// network点缓存
define('O_REALTIME_PRICING_GPS', 'o_realtime_pricing_gps');
// gps点缓存
define('O_BEGIN_CHARGE_LOC', 'o_begin_charge_loc');
// gps点缓存
define('O_YUYING_IS_ORDER', 'o_yuying_is_order');
// 判断是否鱼鹰订单
define('O_KEY_LOCK', 'o_key_lock');
// 订单key 加锁
define('O_HIDE_ADDRESS_AREA', 'o_hide_address_area');
// 订单屏蔽坐标前缀
define('O_AREA_LIST', 'o_area_list');
//订单地区列表parent_id
define('O_FROM_TO_DISTANCE_INFO', 'o_from_to_distance_info');
//出发地和目的地之间路面距离和行驶时间
define('O_SAME_ADDRESS', 'o_same_address');
//相同出发地和目的地订单
define('O_STRATEGY_CONF_DISTRICT', 'o_strategy_conf_district');
//城市下套餐缓存
define('O_CLOSE_PRICE_SEPARATE_CITY_PRODUCT', 'o_close_price_separate_city_product');
//计价分离开关
define('O_CLOSE_AIRPORT_ACTIVITY_CITY_PRODUCT', 'o_close_airport_activity_city_product');
//机场活动开关
define('O_CLOSE_AIRPORT_GUIDE_CITY_PRODUCT', 'o_close_airport_guide_city_product');
//快车接送机导流开关
define('O_AIRPORT_GUIDE_CACHE_DATA', 'o_airport_guide_cache_data');
//快车接送机导流开关
define('O_ACTIVITY_FREQUENCY', 'o_activity_frequency');
//活动频次控制
define('O_MAPPING_COMBOID_AIRPORTID', 'o_mapping_comboid_airportid');
define('O_FLIGHT_NO', 'o_flight_no');
//订单对应航班号
define('O_AIRPORT_POI_CHECK', 'o_airport_poi_check');
//接送机订单坐标检查结果
define('O_COMMON_FLIGHT_DATA', 'o_common_flight_data');
//航班信息数据集
define('O_AIRPORT_ORDER_RESEND', 'o_airport_order_resend');
//改派机场订单
define('O_ORDER_FLIGHT_DATA', 'o_order_flight_data');
//订单航班数据集，field为订单id，val为航班的基本信息
define('O_ORDER_DEVICE_RELATION', 'o_order_device_relation');
////订单设备对应关系
define('O_BASIC_INSURE_INFO', 'o_basic_insure_info');
////订单保险信息
define('O_ORDERID_FREQUENCY_BLACKLIST', 'o_oid_freq_black');
// 订单加密orderid的黑名单前缀
define('O_ORDERID_FREQUENCY_COUNT', 'o_oid_freq_count');
// 订单加密orderid的频率计数
define('O_ORDER_POI_INFO', 'o_order_poi_info');
// 订单POI信息
//openApi相关
define('OPEN_B2B_FORE_OID_MAPPING', 'open_b2b_fore_oid_mapping');
//外部订单id和湾流订单id映射关系
define('OPEN_PINGAN_ACCESS_TOKEN', 'open_pingan_access_token');
//平安保险投保访问令牌
//网点端二维码series
define('D_SIGNED_SCANED_QRCODE', 'd_signed_scaned_qrcode');
define('O_GET_PWD_CNT', 'o_get_pwd_cnt');
define('O_ACCESS_QRCODE_CNT', 'o_access_qrcode_cnt');
define('O_STATION_LOGIN_FAIL', 'o_station_login_fail');
define('O_STATION_INFO', 'o_station_info');
define('O_CHECK_LONGRENT_NUM', 'o_check_longrent_num');
define('O_SEP_RECORD_CONF', 'o_sep_record_conf');
define('O_SEP_RECORD_DETAIL', 'o_sep_record_detail');
define('O_SEP_ACTUAL_PERCENT', 'o_sep_actual_percent');
define('O_SEP_DRIVER_BY_AREA_LEVEL_MODEL', 'o_sep_driver_by_area_level_model');
define('O_RELATION_TRAFFIC_INFO', 'o_relation_traffic_info');
//订单对应关系+order_id+district+orderData
define('O_MULTI_CAR_LEVEL', 'o_multi_car_level');
// 多车型
define('O_BONUS_CACHE', 'o_bonus_cache');
// 奖励缓存 +oid+district
define('O_STRATEGY_CONF', 'o_strategy_conf');
// 同步计价配置
define('O_STRATEGY_TOKEN', 'o_strategy_token');
// 计价token
define('O_MULTI_STRATEGY_CONF', 'o_multi_strategy_conf');
// 多计价token
define('O_OLD_DELAY_TIME_FLAG', 'o_old_delay_time_flag');
// 老的迟到计价标记
define('O_NEW_DELAY_TIME_FLAG', 'o_new_delay_time_flag');
// 新的迟到计价标记
define('O_NEW_BEGIN_CHARGE_FLAG', 'o_new_begin_charge_flag');
//新的开始计费标记
define('O_FREE_CLOSE_ORDER_DAY_COUNTER', 'o_free_close_order_day_counter');
//每天openapi关单的数量计数器
define('O_PASSENGER_FREE_CLOSE_ORDER_DAY_COUNTER', 'o_passenger_free_close_order_day_counter');
//每天某个乘客openapi关单的数量计数器
define('O_FAR_ORDER_FLAG', 'o_far_order_flag');
//远程判断标志
//司机分层体系
define('O_RANK_RECORD_CONF', 'o_rank_record_conf');
define('O_RANK_ACTUAL_PERCENT', 'o_rank_actual_percent');
define('O_RANK_RECORD_DETAIL', 'o_rank_record_detail');
define('O_RANK_RECORD_INFO', 'o_rank_record_info');
define('O_DRIVER_RANK_FOR_SORT', 'o_driver_rank_for_sort');
//利用zset对司机得分数据进行排序
define('O_DRIVER_RANK_PERCENT_DISPLAY', 'o_driver_rank_percent_display');
//乘客端
define('P_INFO_BY_TOKEN', 'p_info_by_token');
//passport乘客基础信息
define('P_INFO_BY_TOKEN_ORDER', 'p_info_by_token_order');
//passport乘客基础信息,发单专用
define('P_INFO_BY_PHONE', 'p_info_by_phone');
//passport乘客基础信息 +phone
define('P_INFO_CACHE_BY_TOKEN', 'p_info_cache_by_token');
//passport乘客基础信息
define('P_PASSENGER_BASIC_INFO', 'p_passenger_basic_info');
//乘客基础信息
define('P_ORDER_CACHE_LOCK', 'p_order_cache_lock');
//订单缓存锁
define('P_ORDER_COMMENT_LOCK', 'p_order_comment_lock');
//订单评论锁
define('P_ORDER_CACHE_LOCK_AVOID_CANCEL', 'p_order_cache_lock_avoid_cancel');
//订单入库前缓存锁
define('P_ORDER_CANCEL_TRIP_LOCK', 'p_order_cancel_trip_lock');
//取消行程锁
define('P_QUESTION_LOCK', 'p_question_lock');
//问卷调查锁
define('P_FEE_DISSENT_BASIC_INFO', 'p_fee_dissent_basic_info');
//费用异议
define('P_ADDRESS_BASIC_INFO', 'p_address_basic_info');
//邮寄地址
define('P_ADDRESS_MD5_INFO', 'p_address_md5_info');
//邮寄地址md5
define('P_HISTORY_ORDER_IDS', 'p_history_order_ids');
//乘客历史订单
define('P_NEW_HISTORY_ORDER_IDS', 'p_new_history_order_ids');
//乘客历史订单使用有序列表
define('P_LIMIT_HITED_USER', 'p_limit_hited_user');
//限流已匹配的目标用户
define('P_LOCATION', 'p_location');
//记录经纬度
define('P_LIMIT_DAY_COUNT', 'p_limit_day_count');
//限流已匹配的目标用户
define('P_DRAWIN_USER', 'p_drawin_user');
//引流用户列表
define('P_NEAR_DRIVERS', 'p_near_drivers');
//乘客当前坐标周边司机
define('P_NEAR_PRE_DRIVER_PHONE', 'p_near_pre_driver_phone');
//乘客当前坐标周边指定司机，通过电话号码指定
define('P_NEAR_PRE_DRIVER_ID', 'p_near_pre_driver_id');
//乘客当前坐标周边指定司机，通过司机id指定
define('P_OVERDRAFT', 'p_overdraft');
//乘客欠款订单
define('P_SEND_MSG', 'p_send_msg');
//记录是否发送消息
define('P_SEND_WAIT_DRIVER', 'p_send_wait_driver');
//等待司机接驾给乘客推送消息
define('P_DRIVER_DISTANCE_TIME', 'p_driver_distance_time');
//司机和乘客之间距离、时间
define('P_DACHE_OPENID', 'p_dache_openid');
//存储打车业务的openid
define('P_LATE_ORDER_ID', 'p_late_order_id');
//乘客最近订单号, 用于创建订单时去重
define('P_RECENT_ORDER_ID', 'p_recent_order_id');
//乘客最近订单号, 用于崩溃恢复
define('P_RECENT_ORDER_ID_BY_PHONE', 'p_recent_order_id_by_phone');
//乘客最近订单号按乘客手机号存储, 用于企业平台崩溃恢复
define('P_CALLCAR_RECENT_ORDER_ID', 'p_callcar_recent_order_id');
//乘客最近代叫车订单号
define('P_HISTORY_ORDER_LIST', 'p_history_order_list');
//乘客历史订单
define('P_LIMIT_CITY_RATIO', 'p_limit_city_ratio');
//限流围栏内用户看见专车比例
define('P_VIP_ADD_USER', 'p_vip_add_user');
//添加VIP用户缓存锁
define('P_GUIDE_WAITE_TIME', 'p_guide_waite_time');
//导流窗口出现时间
define('P_REDIS_CACHE_TAG', 'p_redis_cache_tag');
//号码保护提示
define('P_PLATFORM_INCREASE_LOCK', 'p_platform_increase_lock');
// 平台加价给司机转账加锁
define('P_PANGU_LOCK', 'p_pangu_lock');
// 盘古转账加锁
define('P_NEW_PANGU_LOCK', 'p_new_pangu_lock');
// 盘古转账加锁
define('P_QUESTION_DRIVERS', 'p_question_drivers');
//问卷需求司机池
define('P_WXAGENT_PUSH', 'p_wxagent_push');
//微信代扣签约push提醒消息次数 +pid
define('P_WXAGENT_BIND_STATUS', 'p_wxagent_bind_status');
//微信代扣签约状态 +pid
define('P_ORDER_DEFAULT_COUPON', 'p_order_default_coupon');
//微信代扣签约状态 +pid
define('P_REUNION_FESTIVAL_WHITELIST', 'p_reunion_festival_whitelist');
//回家白名单
define('P_REUNION_SEND_TIP', 'p_reunion_send_tip');
//回家白名单
define('P_GUIDE_FROM_TAXI_OR_GULFSTREAM', 'p_guide_from_taxi_or_gulfstream');
//#出租车导流到专车时标记此专车单来自出租车导流
define('P_UNFASTCAR_USER', 'p_unfastcar_user');
//快车目标用户
define('P_HAS_INVOICED_ORDER_LIST', 'p_has_invoiced_order_list');
//发票行程列表
define('P_AVAIL_INVOICED_ORDER_LIST', 'p_avail_invoiced_order_list');
//用户可开票行程列表
define('P_INVOICE_PASSENGER_HISTORY', 'p_invoice_passenger_history');
//用户历史开票行程列表
define('P_INVOICE_USERINFO', 'p_invoice_userinfo');
//乘客申请开票用户信息记录
define('P_INVOICE_DETAIL', 'p_invoice_detail');
//开票信息详情
define('P_USER_GUIDE_WAIT_TIME', 'p_user_guide_wait_time');
//导流高价值用户弹出时间
define('P_DIST_GUIDE_PARAM', 'p_dist_guide_param');
//导流高价值用户各区系数
define('P_GUIDANCE', 'p_guidance');
//快车版本导流策略存放 导流时间和导流方向
define('P_COUNT_INFO', 'p_count_info');
//乘客技术信息
define('P_VERSIONID', 'p_versionid');
//乘客版本ID
define('P_APP_VERSIONID', 'p_app_versionid');
//乘客版本ID
define('P_GET_LINE_SEND_MSG', 'p_get_line_send_msg');
//乘客推送拉取路线消息
define('P_ESTIMATEPRICE_DYNAMIC_PRICE_TEMP', 'p_estimateprice_dynamic_price_temp');
//预估气泡调用策略动态调价缓存
define('P_ESTIMATE_CONFIRM_INFO', 'p_estimate_confirm_info');
define('P_ESTIMATE_FEE_INFO', 'p_estimate_fee_info');
define('P_ESTIMATE_ID', 'p_estimate_id');
define('P_ESTIMATE_ID_JOIN_CARPOOL', 'p_estimate_id_join_carpool');

define('P_ESTIMATE_SELECTED_ID', 'p_estimate_selected_id');
define('P_ESTIMATE_CARPOOL_ID', 'p_estimate_carpool_id');
define('P_ESTIMATE_TRACE_ID', 'p_estimate_trace_id');
define('P_ESTIMATE_SHOW_PRODUCT_LIST', 'p_estimate_show_product_list');
define('P_ESTIMATE_DYNAMIC_PRICE_PAGE_DATA', 'p_estimate_dynamic_price_page_data');
define('P_ESTIMATEPRICE_DYNAMIC_PRICE', 'p_estimateprice_dynamic_price');
//预估气泡调用策略动态调价缓存
define('P_ESTIMATEPRICE_DYNAMIC_PRICE_MULTI', 'p_estimateprice_dynamic_price_multi');
//预估气泡调用策略动态调价缓存, anycar多车型
define('P_ANYCAR_ESTIMATE_PRICE', 'p_anycar_estimate_price');
//anycar预估缓存数据，数据来源于price-api
define('P_LIMIT_FEE_TIP_SHOW_NUM', 'p_limit_fee_tip_show_num');
//乘客最低消费tip展示次数
define('P_LIMIT_FEE', 'p_limit_fee');
define('P_SHOW_LMIT_TIP_TIME', 'p_show_limit_time');
define('P_NEW_LASTEST_TIME', 'p_new_lastest_time');
//ETA接驾时间
define('P_LASTEST_TIME', 'p_lastest_time');
//周边司机最近接驾时间
define('P_HISTORY_ORDER_BY_PASSENGER_ID', 'p_history_order_by_passenger_id');
define('P_MINUS_SUPPLY_NUM', 'p_minus_supply_num');
//立减活动补偿次数
define('P_TOPIC_INFO', 'p_topic_info');
//营销活动
define('P_BANNER_INFO', 'p_banner_info');
//专车banner
define('P_CARPOOL_ESTIMATE_PRICE', 'p_carpool_estimate_price');
//拼车一口价预估价缓存 数据来自计价系统
define('P_ESTIMATE_COUPON_INFO', 'p_estimate_coupon_info');
//预估券信息缓存
define('P_DISCARDED_ORDER_TAG', 'p_discarded_order_tag');
//丢弃订单标记
define('P_ESTIMATE_PRICE_CURRENCY', 'p_estimate_price_currency');
//预估计价币种
define('P_NONCARPOOL_ESTIMATE_PRICE', 'p_noncarpool_estimate_price');
//非拼车一口价南航接送机预估价缓存 数据来自计价系统
define('P_PREPAY_ORDER_CACHE', 'p_prepay_order_cache');
// 预付订单缓存
define('P_UPDATE_DEST_PREPAY_CACHE', 'p_update_dest_prepay');
//更新目的地预付缓存
define('P_DUSE_EXPIRE', 'p_duse_expire');
// duse播单超时缓存
define('P_FORMAT_PASSENGER_INFO', 'p_format_passenger_info');
//获取拼友头像昵称缓存
define('P_FORMAT_PASSENGER_LEVEL', 'p_format_passenger_level');
//获取乘客等级缓存
define('P_GET_REL_PASSENGER', 'p_get_rel_passenger');
//获取拼友行程规划缓存
define('P_CARPOOL_CONTROL_CONFIG', 'p_carpool_control_config');
//管控配置
define('P_UPDATE_DISPLAY_PRICE', 'p_update_display_price');
//乘客取消行程更新拼友订单对应司机展示价格
define('P_MUST_COMMENT_AB_TEST', 'p_must_comment_ab_test');
//必须评论ab test key
define('P_FASTCAR_MINI_NOTICE_SMS', 'p_fastcar_mini_notice_sms');
//mini滴订单短信通知缓存
define('P_ACT_FASTENSURE', 'p_act_fastensure');
//快车险缓存
define('P_CITY_FASTENSURE_CFG', 'p_city_fastensure_cfg');
//快车险开通城市缓存
define('P_CITIES_AIRPORT_SHUTTLE', 'p_cities_airport_shuttle');
//开通接送机服务的城市
define('P_CITY_AIRPORT_DATA_SET', 'p_city_airport_data_set');
//城市机场信息
define('P_GUIDE_UNIQ_DATA', 'p_guide_uniq_data');
//导流数据
define('P_INVOICE_CACHE_LOCK', 'p_invoice_cache_lock');
//乘客开票锁
define('P_CANCEL_FASTCAR_TRIP_NUM', 'p_cancel_fastcar_trip_num');
//乘客当天取消快车订单数
define('P_CARPOINT_START_CHOOSE', 'p_car_point_start_choose');
//乘客上车点起点，字符串类型
define('P_RED_PACKET_LAST_VALUE', 'p_red_packet_last_value');
//乘客红包所见红包价格
define('P_OPERATION_ACTIVITY_TYPE', 'p_operation_activity_type');
//预估价格加价司机感知,枚举值
define('P_ESTIMATE_CARPOOL_FLAT_RATE_ROUTE', 'p_estimate_carpool_flat_rate_route');
//拼车是否命中区域一口价路线缓存
define('P_CARPOOL_DECISION', 'p_carpool_decision');
//乘客拼车意愿，是否拼车
define('P_BUBBLE_TAG', 'p_bubble_tag');
// 定价调价，存储用户红点状态
define('P_ADJUST_PRICE_RED_DOT_TAG', 'p_adjust_price_red_dot_tag');
//乘客拼车意愿，是否拼车
define('P_CANCEL_ORDER_DUTY_TOTAL_NUM', 'p_cancel_order_duty_total_num');
//乘客总的有责取消次数
define('P_MEET_DRIVER_EXEMPT_NUM', 'p_meet_driver_exempt_num');
//乘客遇到司机免责改派次数
define('P_NEW_CONTROL_CONFIG', 'p_new_control_config');
define('P_CARPOOL_MIS_CFG', 'p_carpool_mis_cfg');
//拼车开城mis化
define('P_CARPOOL_MIS_CFG_NEW', 'p_carpool_mis_cfg_new');
//拼车开城mis化
define('P_BUBBLE_CACHE_BILL_PRODUCT_INFO', 'p_bubble_cache_bill_product_info');
//预估时缓存账单产品信息，供发单时使用
define('P_CARPOINT_START', 'p_car_point_start');
//乘客上车点起点
define('P_COMMENT_HISTORY', 'p_comment_history');
//乘客评价订单记录
define('P_FRIEND_COUPON', 'p_friend_coupon');
//webapp朋友券分享
define('P_AU_AIRPORT_GUIDE_ORDER', 'p_au_airport_guide_order');
//澳洲机场引导订单
//低价值订单强制加价的消费记录
define('P_LOW_ORDER_TIP', 'p_low_order_tip');
define('P_WEEK_CANCEL_ORDER_DUTY_NUM', 'p_week_cancel_order_duty_num');
define('P_CARPOOL_FIRST_TIPS', 'p_carpool_first_tips');
//标识拼车首次提示的push 防止重复发
define('P_CARPOOL_FIRST_PASSENGER_DETOUR_METRE', 'p_carpool_first_passenger_detour_metre');
//拼车第一位上车乘客拼成后绕路距离
define('P_CIP_PASSENGER_SET', 'p_cip_passenger_set');
//有cip资质的用户set
define('P_CIP_AIRPORT_SET', 'p_cip_airport_set');
//开通cip的机场
define('P_CIP_PASSENGER_NUM', 'p_cip_passenger_num');
//用户获得的cip券个数
define('AIRPORT_ONBOARD_TIP_SET', 'airport_onboard_tip_set');
//开通上车点的机场+航站楼
//openapi
define('P_CONTROL_CONFIG', 'p_control_config');
define('P_CARPOOL_FREQUENCY_CONTROL', 'p_carpool_frequency_control');
//拼车评论限制
define('P_CLIENT_MENU_CFG', 'p_client_menu_cfg');
//二级菜单配置
define('P_SPECIAL_CAR_ORDERS', 'p_special_car_orders');
//历史订单+pid
define('P_FESTIVAL_ALARM', 'p_festival_alarm');
//三站两场提醒
define('P_INFO_CACHE_BY_TOKEN_ORDER', 'p_info_cache_by_token_order');
//passport发单后乘客基础信息
define('P_CANCEL_TRIP_LOCK', 'p_cancel_trip_lock');
//乘客取消行程锁
define('P_CARPOOL_COMMUTE_TO_WORK', 'p_carpool_commute_to_work');
//乘客端命中拼车上班场景h5展示锁
define('P_CARPOOL_COMMUTE_FROM_WORK', 'p_carpool_commute_from_work');
//乘客端命中拼车下班班场景h5展示锁
define('P_CARPOOL_XIAOBA_GUIDE', 'p_carpool_xiaoba_guide');
//小巴新手引导页控制次数
define('P_CARPOOL_ACTIVITY_RELATION_PASSENGER_HEAD', 'p_carpool_activity_relation_passenger_head');
define('P_LOW_PRICE_CARPOOL_SHOW_BUBBLE', 'p_low_price_carpool_show_bubble');
define('P_SPECIAL_CAR_H5_GUIDE', 'p_special_car_h5_guide');
define('P_SHOW_WEBAPP_COUPON_INFO_REFUSE', 'p_show_webapp_coupon_info_refuse');
//本月回巢预估阻断不展示优惠券
define('P_SHOW_WEBAPP_COUPON_INFO', 'p_show_webapp_coupon_info');
//回巢预估阻断展示优惠券
define('P_CHECK_USER_EMERGENCY_CONTACT', 'p_check_user_emergency_contact');
//老版本检查乘客紧急联系人
define('P_SHOW_TAXI_CASH_PAY_CLOSE', 'p_show_taxi_cash_pay_close');
//出租车现金付下线
//openapi
define('API_SMS_DOWNLOAD', 'api_sms_download');
//下载链接 短信发送次数
define('API_WEIXIN_MP_ACCESS_TOKEN', 'api_weixin_mp_access_token');
//小桔科技公众号接口访问access_token
//车型
define('CAR_LEVEL_LIST', 'car_level_list');
//所有车型
define('CAR_LEVEL_LIST_RAW', 'car_level_list_raw');
//所有车型，g_car_level表原始数据
//anti spam
define('A_P_NEWORDER', 'a_p_neworder');
//乘客创建订单频率控制
define('A_P_CANCELORDER', 'a_p_cancelorder');
//乘客取消订单频率控制
define('A_P_CANCEL_REAL_ORDER', 'a_p_cancel_real_order');
//乘客取消订单频率控制
define('A_P_SAME_PASSENGER_DRIVER_DEALORDER', 'a_p_same_passenger_driver_dealorder');
//乘客与司机成交频率控制
define('A_P_PASSENGER_DEAL_ORDER', 'a_p_passenger_deal_order_frequency');
//乘客特定时间内成交限制
define('A_P_BINDIDBYPID', 'a_p_bindidbypid');
//Pid获取的真身Bindid
define('A_P_BINDID_ARRAY_BYPID', 'a_p_bindid_array_bypid');
//pid获取真身数组，包括阿里和微信、手q
define('A_P_WHITE_LIST', 'a_p_white_list');
//白名单
define('A_P_TEST_LIST', 'a_p_test_list');
//内测名单
define('A_P_WEBAPP_TEST_LIST', 'a_p_webapp_test_list');
//webapp内测名单
define('A_P_GUIDE_LIST', 'a_p_guide_list');
//引流防打扰
define('A_P_CANCEL_GUIDE_LIST', 'a_p_cancel_guide_list');
//取消打车引流防打扰
define('A_P_BLACK_LIST', 'a_p_black_list');
//黑名单用户
define('A_P_CANCEL_GUIDE_FOR_TAXI', 'a_p_cancel_guide_for_taxi');
//取消专车引流防打扰
define('A_P_WAIT_GUIDE_FOR_TAXI', 'a_p_wait_guide_for_taxi');
//等待专车引流防打扰
define('A_P_CANCEL_GUIDE_FAST', 'a_p_cancel_guide_fast');
//取消快车防打扰
define('A_P_WAIT_GUIDE_FAST', 'a_p_wait_guide_fast');
//等待快车防打扰
//商业变现滴滴播报广告频率控制
define('C_R_DIDIPUSH_AD_DAY', 'c_r_didipush_ad_day');
//商业变现滴滴播报广告司机推送一天频率控制
//mis kefu
define('K_COMPLAINT_COUNT', 'k_complaint_count');
//mis长包车
define('D_ONLINE_TIME', 'd_online_time');
//司机在线时长
define('D_ONLINE_LAST_DRIVERS', 'd_online_last_drivers');
//司机在线时长
define('D_MONITOR', 'd_monitor');
//地图展板
define('D_NEW_ONLINE_LAST_DRIVERS', 'd_new_online_last_drivers');
//工资结算
define('D_BASIC_WAGE_USED_CFG', 'd_basic_wage_used_cfg');
//计算基本工资使用的key
define('D_INSURANCE_USED_CFG', 'd_insurance_used_cfg');
//计算基本工资使用的key
define('D_BASIC_WAGE_WEEK_LIMIT_CFG', 'd_basic_wage_week_limit_cfg');
//基本工资周限制
define('D_BASIC_WAGE_MONTH_LIMIT_CFG', 'd_basic_wage_month_limit_cfg');
//基本工资月限制
define('D_PROTECTION_FEE_CFG', 'd_protection_fee_cfg');
//车辆租金配置
//mis预警订单取消
define('O_WARN_TIMES', 'o_warn_times');
//用户发送次数超限
define('O_WARN_ETA', 'o_warn_eta');
//eta预警
define('O_WARN_ETA_LATE', 'o_warn_eta_late');
//eta预警已经迟到
define('O_WARN_WAIT', 'o_warn_wait');
//等候超过10分钟
define('O_WARN_TRAVEL_TIME', 'o_warn_travel_time');
//行驶时间超过2小时
define('O_WARN_TRAVEL_DISTANCE', 'o_warn_travel_distance');
define('O_WARN_REASSIGN', 'o_warn_reassign');
//司机改派
define('O_WARN_NOT_PAY', 'o_warn_not_pay');
//已结束未支付1小时
define('O_WARN_TRAVEL_TWO_DISTANCE', 'o_warn_travel_two_distance');
//实际行驶距离超过预估距离1倍
define('O_WARN_TRAVEL_FEE', 'o_warn_travel_fee');
//行驶时间5分钟，价格还是起步价
define('O_WARN_TRAVEL_TWO_FEE', 'o_warn_travel_two_fee');
//实际价格超过预估价格1倍的
//mis司机类型每月统计
define('O_STATISTIC_DATE_TYPE', 'o_statistic_date_type');
define('M_DAILY_LEVEL_STATIS_BYAREA', 'm_daily_level_statis_byarea');
//分城市的每日星级评价统计
define('M_ACCOUNT_ROLE_INFO', 'm_account_role_info');
//mis账户拥有的权限
define('M_ACTIVITY_EFFECTIVE_AREA', 'm_activity_effective_area');
//MIS盘古活动区域
define('M_ACTIVITY_EFFECTIVE_CFG', 'm_activity_effective_cfg');
//MIS盘古活动配置
//司机服务问卷  优惠券已发放张数
define('M_QUESTION_COUPON_NUM', 'm_question_coupon_num');
//司机推荐用户数限制
define('M_DRIVER_RECOMMEND_CNT', 'm_driver_recommend_cnt');
//mis权限点
define('M_USER_PRIVILEGE', 'm_user_privilege');
//滴滴播报上传临时数据
define('M_DIDI_PUSH_UPLOAD', 'm_didi_push_upload');
//mis权限 城市管理
define('M_USER_PRIVILEGE_CITY', 'm_user_privilege_city');
define('M_AIRPORT_ALL', 'm_airport_all');
//全部机场缓存
define('M_DISTRICT_MAP_AREA', 'm_district_map_area');
//区号到区域号的映射
//第三方
define('TP_VARIFLIGHT_INFO', 'tp_variflight_info');
//非常准信息 +fnum（航班号）+flightDate（起飞日期）
define('TP_VARIFLIGHT_ADDPUSH', 'tp_variflight_addpush');
//定制航班信息推送 +fnum（航班号）+flightDate（起飞日期）+dep(起始地三字母)+arr(目的地三字母)
define('TOOL_TRAFFIC_INFO', 'tool_traffic_info');
//交通信息+traffic_num
define('TOOL_TRAFFIC_LIST', 'tool_traffic_list');
//交通信息列表+traffic_num 模糊查询
define('TOOL_CITY_AIRPORT', 'tool_city_airport');
//前缀+md5(城市名+区号)
define('D_REG_NUM_DRIVER', 'd_reg_num_driver');
//司机注册统计数量专用
define('D_TMP_NUM_DRIVER', 'd_tmp_num_driver');
//当前注册数增量
//通用问卷系统
define('Q_QUES_LIBRARY', 'q_ques_library');
//题库数据
define('Q_QUES_SURVEY', 'q_ques_survey');
//问卷数据
define('Q_QUES_EXTRACT', 'q_ques_extract');
//问卷题目配置
define('Q_QUES_TOPIC', 'q_ques_topic');
//答题
define('Q_QUES_TOPIC_IDS', 'q_ques_topic_ids');
//题目id集合
define('Q_COUNT_GROUP_DRIVER', 'q_count_group_driver');
//分组汇总司机总数
define('Q_QUES_SEND', 'q_ques_send');
//发放调研问卷
//司机在线考试
define('Q_EXAM_LOGIN', 'q_exam_login');
//考官登陆验证key
define('Q_EXAM_DRIVER', 'q_exam_driver');
//考试司机验证
define('M_SMS_REG', 'm_sms_reg');
//司机注册链接 短信发送次数
//司机model 统计司机在线专用
define('D_MODEL', 'd_model');
//统计司机在线专用，司机model
//司机服务参数
define('M_DRIVER_CANCEL', 'm_driver_cancel');
//司机订单取消率
define('M_DRIVER_COMPLAINT', 'm_driver_complaint');
//司机订单投诉率
define('M_DRIVER_AVGARRIVE', 'm_driver_avgarrive');
//司机订单平均接驾时间
define('M_DRIVER_AVGLEVEL', 'm_driver_avglevel');
//司机申请升级相关
define('MIS_CAR_BRAND', 'mis_car_brand');
//车辆品牌型号
define('D_CAR_BRAND', 'd_car_brand');
//车辆品牌型号
define('MIS_CAR_REL_MC', 'mis_car_rel_mc');
//申请状态缓存10分钟
define('MIS_CAR_REL_CAR_MC', 'mis_car_rel_car_mc');
//关联车信息缓存10分钟
define('MIS_DRIVER_UPGRADE_MC', 'mis_driver_upgrade_mc');
//历史状态缓存10分钟
define('MIS_ORDER_EXT_MC', 'mis_order_ext_mc');
//缓存订单信息缓存10分钟
//滴滴美食节
define('O_COORDINATE_BEGIN_FINISH', 'o_coordinate_begin_finish');
//订单开始计价、完成订单坐标
//订单fsource
define('O_FSOURCE', 'o_fsource');
//乘客发单source
define('O_POOL_SEAT_DISCOUNT', 'o_pool_seat_discount');
//拼座订单折扣
define('TAGS_BY_CATEGORY', 'tags_by_category');
//按栏目缓存标签
define('POSITIVETGS', 'positivetags');
//司机标签缓存
//记录司机当前行程的订单ID---拼车使用
define('D_CARPOOL_CURRENT_TRAVELORDER', 'd_carpool_current_travelorder');
//记录司机车上拼友---拼车使用
define('D_CARPOOL_ROUTE_ORDER', 'd_carpool_route_order');
//记录行程变更---拼车使用
define('D_CARPOOL_ROUTE_UPDATE', 'd_carpool_route_update');
//拼成奖励
define('P_CARPOOL_REWARD_INFO', 'p_carpool_reward_info');
define('P_CARPOOL_REWARD_SEND_MSG', 'p_carpool_reward_send_msg');
define('P_CARPOOL_SALES_COUPON', 'p_carpool_sales_coupon');
define('P_CARPOOL_COOPERATE_MSG', 'p_carpool_cooperate_msg');
define('P_ORDER_LIKE_WAIT_FLAG', 'p_order_like_wait_flag');
//订单是否是愿等订单
//拼车相关P_PASSENGER_ORDER_NUM_INFO
define('P_PASSENGER_ORDER_NUM_INFO', 'p_passenger_order_num_info');
//乘客次数统计key
//每天司机完成抢单数D_DRIVER_COMPET_ORDER_NUM
define('D_DRIVER_COMPET_ORDER_NUM', 'd_driver_compet_order_num');
define('O_FLIGHT_ABNORMAL_ORDER', 'o_flight_abnormal_order');
//异常航班的key
//司机tag信息
define('D_TAG_INFO', 'd_tag_info');
//tag集合下司机列表
define('D_TAG_REL', 'd_tag_rel');
//乘客tag
define('P_TAG_INFO', 'p_tag_info');
//tag集合下乘客列表
define('P_TAG_REL', 'p_tag_rel');
//订单盘古奖励结果缓存
define('O_PANGU_REWARD', 'o_pangu_reward');
//转账信息缓存
define('O_TRANSFER', 'o_transfer');
//push filter order 锁
define('O_PUSH_FILTER_LOCK', 'o_push_filter_lock');
define('D_ONLINE_TAGS', 'd_online_tags');
define('D_PUSH_FILTER_ORDER', 'd_push_filter_order');
//dPushFilter Order去重处理
define('D_FREERIDE_SWITCH_SET', 'd_freeride_switch_set');
//顺风车导流开关白名单集合
define('D_COMPET_ORDER_LIMIT_SET', 'd_compet_order_limit_set');
//抢单限制白名单集合
define('D_ASSIGN_REJECT_STAT', 'd_assign_reject_stat');
//指派拒接统计当日拒接数
define('D_ASSIGN_REJECT_STATUS', 'd_assign_reject_status');
//司机是否因指派拒接超过阀值而被封禁
define('D_GOOD_DRIVER', 'd_good_driver');
//好司机
define('D_GOOD_DRIVER_IMPORT', 'd_good_driver_import');
//好司机当天导入成功标志
define('P_FLAG_AGENT_REMIND', 'p_flag_agent_remind');
//是否需要提醒免密签约
define('D_LISTEN_ORDER_STATUS', 'd_listen_order_status');
//司机听单状态
define('D_LAST_DISPLAY_STATUS', 'd_last_display_status');
//上次向lbs推送是否显示车标的状态 #1:不显示 #2:显示
define('D_BANNER_SETS', 'd_banner_sets');
//司机首页banner列表id
define('D_BANNER_INFO', 'd_banner_info');
//司机首页banner详情
define('D_DIRECT_DRIVER_SET', 'd_direct_driver_set');
//直营车不能向下听单白名单
define('D_PRI_AIRPORT_DRIVER_SET', 'd_pri_airport_driver_set');
//倾斜听机场单白名单
define('O_TRACK_LOG_FREQUENCY_CONTROL', 'o_track_log_frequency_control');
//有针对性的采样订单记录track.log
define('P_LAST_IP', 'p_last_ip');
//乘客上次发单ip
define('P_LAST_DEVICE', 'p_last_device');
//企业是否播给顺路司机
define('P_LINEUP_BROADCAST_TIME', 'p_lineup_broadcast_time');
//排队订单拨单时间
define('P_LINEUP_INIT_RANK', 'p_lineup_init_rank');
//进入排队的初始位置
define('O_ORDERID_ESTIMATE_STATIONINFO', 'o_orderid_estimate_stationinfo');
//根据订单ID获取station info key+order_id
define('P_TRACEID_ESTIMATE_STATIONINFO', 'p_traceid_estimate_stationinfo');
// 存站点信息 key+trace_id
define('P_TRACEID_ESTIMATE_STATIONLIST', 'p_traceid_estimate_stationlist');
// 存站点列表信息 key+trace_id
define('P_MIS_HOLIDAY_AREA', 'p_mis_holiday_area');
//mis地区节假日配置，当天信息 key＋date＋area
define('P_MIS_HOLIDAY_COUNTRY', 'p_mis_holiday_country');
//mis全国节假日配置，当天信息 key＋date
define('P_TRACEID_ESTIMATE_COMBOTYPE', 'p_traceid_estimate_combotype');
// 命中机场单combotype key+trace_id
define('P_TRACEID_ESTIMATE_ROUTE_GROUP', 'p_traceid_estimate_route_group');
//跨城拼车父路线id
define('P_XIAOBA_NEW_ORDER_TIMES', 'p_xiaoba_new_order_times');
//发过小巴单次数
define('P_CARPOOL_ESTIMATE_ARRIVAL', 'p_carpool_estimate_arrival');
// 预估拼车ETD信息
define('P_SPECIAL_RATE_POOL_SEAT', 'p_special_rate_pool_seat');
//特价拼车实验
define('P_SCAN_PAY_BILLID', 'p_scan_pay_billid');
//扫码付
define('P_SCAN_PAY_DRIVER_MAP', 'p_scan_pay_driver_map');
//扫码付司机id映射
define('P_SCAN_PAY_CARD', 'p_scan_pay_card');
//扫码付空白二维码
define('C_CAR_MODEL_IMG', 'c_car_model_img');
//车型图片
define('P_CARPOOL_COMMUTE_CARD', 'p_carpool_commute_card');
define('P_CARPOOL_COMMUTE_CARD_REJECTION_RECORD_LIST', 'p_carpool_commute_card_rejection_record_list');
define('P_CARPOOL_COMMUTE_H5_REJECT_RECORD','p_carpool_commute_h5_reject_record');
//拼车周卡
define('P_COMMUTE_INFO', 'p_commute_info');
define('P_ESTIMATE_AIRPORT_STATION_CONTROL', 'p_estimate_airport_station_control');
//拼车是否命中区域一口价路线缓存
define('P_INSURANCE_INFO', 'p_insurance_info');
//保险人信息缓存 + estimate_id price-api写
define('P_ORDER_MATCH_EXPERIENCE_GROUP', 'p_order_match_experience_group');
//所命中的实验组
define('UNFINISH_REGISTER', 'unfinish_register');
define('GET_SMS_CODE', 'get_sms_code');
define('HAS_AUDITED', 'has_audited');
define('RESEND_ORDER_SMS', 'resend_order_sms');

define('P_CARPOOL_COMMUTE_FREE_CARD', 'p_carpool_commute_free_card');
/**物品遗失归还相关（不能放到prefix_redis.php中，启动时不会加载）*/
define('P_ESTIMATE_CACHE_LOSS_REMAND_PRODUCT_INFO', 'p_estimate_cache_loss_remand_product_info');
//预估时缓存物品遗失产品信息参数，供发单时使用
//预估气泡展示动调信息
define('P_ESTIMATE_BUBBLE_DYNAMIC_PRICE', 'p_estimate_bubble_dynamic_price');
// 推荐套餐 cache
define('P_REC_COMBO_CACHE_PREFIX', 'p_rec_combo_cache');
/*
 * @desc   : 定义全局存储服务错误码code（从505001~505100，6位错误码）
 *           定义全局rpc系统交互错误码code（从505101~505400，6位错误码）
 *           定义全局业务逻辑错错误码code（从505401开始，6位错误码）
 * @user : liuchuan<<EMAIL>>
 * @date   : 2016-04-26
 */
//成功返回0,错误返回505000
define('GLOBAL_SUCCESS', '0');
define('GLOBAL_PARAMS_ERROR', '505000');
define('GLOBAL_DSIG_ERROR', '520');
//乘客端
//
define('P_ERRNO_TOKEN_ERROR', '101');
// 兼容打车错误码
define('P_ERRNO_PARAMS_ERROR', '1010');
define('P_ERRNO_DYNAMIC_PRICE', '1102');

// 途经点和场景（如机场、火车站）冲突错误码
define('P_ERRNO_STOPOVER_POINTS_CONFLICTS_WITH_SCENES', '1035013');
// DDS通用的需要透传errMsg得到错误码
define('P_ERRNO_DDS_INTERCEPT_WITH_MSG', '1035014');

//动态调价客户端和服务端不一致
/*
 * @desc 存储服务错误码定义:redis,mysql
 * @desc 错误码分段:505001~505100
 *--------------------------------------
 */
//数据库
define('DB_ERRNO_LOAD_ERROR', '505001');
define('DB_ERRNO_TABLEFIELDS', '505002');
define('DB_ERRNO_INSERT', '505003');
define('DB_ERRNO_DELETE', '505004');
define('DB_ERRNO_SELECT', '505005');
define('DB_ERRNO_UPDATE', '505006');
define('DB_ERRNO_GET_STRATEGY_CONF', '505007');
define('DB_ERRNO_PROCESS_STRATEGY_CONF', '505008');
define('DB_SYNC_STRATEGY_CONF_ERROR', '505009');
define('DB_ERRNO_UPDATE_NEW_GUID_ERROR', '505010');
//缓存
define('CACHE_ERRNO_LOAD_ERROR', '505011');
define('CACHE_ERRNO_SET', '505012');
define('CACHE_ERRNO_GET', '505013');
define('CACHE_ERRNO_DELETE', '5050014');
define('CACHE_QUEUE_OUT_LIMIT', '505015');
define('CACHE_CKV_ERROR', '505016');
define('CACHE_CKV_LOCK_PROCESS', '505017');
define('CACHE_BINDIDS_REDIS_KEY_SETEX_FAILED', '505018');
/*
 * @desc rpc系统交互错误码定义:订单系统,司乘系统
 * @desc 错误码分段:505101~505400
 *---------------------------------------------------------
 */
//订单系统
define('O_ERRNO_ORDER_SYSTEM_NETWORK_EXCEPTION', '505101');
define('O_ERRNO_GET_ORDER_INFO_ERROR', '505102');
define('O_ERRNO_GET_MULTI_ORDER_INFO_ERROR', '505103');
define('O_ERRNO_GET_ORDER_EXTRA_INFO_ERROR', '505104');
define('O_ERRNO_UPDATE_ORDER_INFO_ERROR', '505105');
define('O_ERRNO_NEW_ORDER_INFO_ERROR', '505106');
define('O_ERRNO_ASSIGNED_ORDER_INFO_ERROR', '505107');
define('O_ERRNO_PERPAREED_ORDER_INFO_ERROR', '505108');
define('O_ERRNO_CANCELED_ORDER_INFO_ERROR', '505109');
define('O_ERRNO_BEGINED_ORDER_INFO_ERROR', '505110');
define('O_ERRNO_FINISHED_ORDER_INFO_ERROR', '505111');
define('O_ERRNO_COMPLETEED_ORDER_INFO_ERROR', '505112');
define('O_ERRNO_ADD_ORDER_FEATURE_ERROR', '505113');
define('O_ERRNO_UPDATE_ORDER_FEATURE_ERROR', '505114');
define('O_ERRNO_DELETE_ORDER_FEATURE_ERROR', '505115');
define('O_ERRNO_DISCARDED_ORDER', '505116');
define('O_ERRNO_UPDATE_ORDER_FEAURE_ERROR', '505117');
define('O_ERRNO_ENCRYPT_FREQUENCY_FAST', '505118');
// 加密订单id频率过快
define('O_ERRNO_ENCRYPT_ORDERID_FAIL', '505119');
// 加密订单id失败
define('O_ERRNO_DECRYPT_ORDERID_FAIL', '505120');
// 解密订单id失败
define('O_ERRNO_QUERY_LINEUP_FEATURE_ERROR', '505121');
//查询排队订单
define('O_ERRNO_CHECK_ORDER_STATUS_FAIL', '505122');
define('O_ERRNO_LINEUP_ORDER_UPDATE_FAIL', '505123');
define('O_ERRNO_TRIPCLOUD_ORDER_UPDATE_FAIL', '505124');
define('O_ERRNO_DYNAMIC_PRICE_EMPTY', '3034');
//动态调价信息为空
//司机系统rpc
define('DRIVER_RPC_GET_DRIVET_DATA_ERROR', '80009');
//司乘系统
define('D_ERRNO_GRAB_SYSTEM_NETWORK_EXCEPTION', '505151');
define('D_ERRNO_GRAB_SYSTEM_MAKEING_DECISIONS', '505152');
define('D_ERRNO_GRAB_SYSTEM_ASSIGNED_ORDER', '505153');
define('D_ERRNO_PARAMS_ERROR', '505154');
define('D_ERRNO_GET_COORD_ERROR', '505155');
define('D_ERRNO_GET_DRIVER_INFO_ERROR', '505156');
define('D_ERRNO_GET_DRIVER_FEATURE_ERROR', '505157');
define('D_ERRNO_UPDATE_DRIVER_INFO_ERROR', '505158');
define('D_ERRNO_UPDATE_DRIVER_FEATURE_ERROR', '505159');
define('D_ERRNO_PROCESS_DRIVER_ON_ORDEER_CHARAGE_ERROR', '505160');
define('D_ERRNO_DRIVER_SYSTEM_NETWORK_EXCEPTION', '505161');
define('D_ERRNO_SCANPAY_NO_ACCESSBILITY', '505162');
define('D_QRCODE_EXPIRED', '505163');
define('D_ERRNO_SCANPAY_NO_COUPON_ALLOWED', '505164');
define('D_ERRNO_SCANPAY_COUPON_USAGE_DENIED', '505165');
define('D_GET_COORD_ERROR', '2184');
//locsvr 获取司机坐标错误
//DUSE系统 505201开始
define('O_ERRNO_DUSE_CANCEL_ORDER', '505201');
/*
 * @desc 业务逻辑错误码定义
 * @desc 错误码分段:505401开始
 *---------------------------------------------------------
 */
define('P_ERRNO_ORDER_CANCEL_LOCKED', '505401');
define('P_ERRNO_ORDER_SYNC_KAFKA_FAIL', '505402');
//define('P_ERRNO_TOKEN_ERROR', '505403');
define('P_ERRNO_FREQUENCY_CONTROL_ERROR', '505404');
define('P_ERRNO_CHARGE_ORDER', '505405');
define('P_ERRNO_RECANCEL_ORDER', '505406');
define('P_ERRNO_SPAM', '505407');
define('P_ERRNO_INSERT_PUBNISHFORWARD', '505408');
define('P_ERRNO_ORDER_HAS_CLOSE_ERROR', '505409');
define('P_ERRNO_CURL_PASSPORT', '505410');
define('O_ERRNO_ORDER_TIMEOUT', '505411');
define('O_ERRNO_ORDER_NOT_UNSTRIVED', '505412');
define('O_ERRNO_INVALID_OID', '505413');
define('KAFKA_SEND_FAIL', '505414');
define('KAFKA_PARTITIONS_ERROR', '505415');
define('KAFKA_EXCEPTION', '505416');
define('QUEUE_EXECUTE_ERROR', '505417');
define('PUSH_SEND_COMMON_MSG_ERROR', '505418');
define('PUSH_SEND_BROADCAST_MSG_ERROR', '505419');
define('COMMON_PARAMS_ERROR', '505420');
define('COMMON_REQUEST_ERROR', '505421');
define('OP_BOOK_DRIVER_ORDER_LISTCKV_ERROR', '505422');
define('D_ERRNO_EXCEPTION_FOR_WAIT', '505423');
//老api迁移过来的错误号,后续改掉
//define('API_CHECK_AIRPORT_RAILWAY_FAIL', '505424');
define('AIRPORT_ERRNO_GETFLIAGHT_RPC', '505425');
define('AIRPORT_ERROR_ORDER_WAIT_COMPENSATE_DRIVER_RPC', '505426');
define('ORDER_CALL_THEMIS_FAILED', '505427');
define('O_ERRNO_CONTROL_TRANSFER_TODRIVER_FAILED', '505428');
define('O_ERRNO_BILLID_ERROR', '505429');
define('O_ERRNO_TRANSFER_ERROR', '505430');
define('O_ERRNO_CASHIER_WRITE_ORDERRESULT_INFO', '505431');
define('CARPOOL_DEL_DRIVER_TRAVELORDER_REDIS_ERROR', '505432');
define('CARPOOL_RPC_CALL_FAIL_ERROR', '505433');
define('CARPOOL_UPDATE_FAIL', '505434');
define('CARPOOL_DEL_COUPON_REWARD_ERROR', '505435');
define('CARPOOL_GET_REL_PASSENGER_ERROR', '505436');
define('INTER_CITY_CARPOOL_START_REGION_NOT_OPEN', '530001');
//'您的起点不在开通区域内',
define('INTER_CITY_CARPOOL_START_CITY_NOT_OPEN', '530002');
//'起点城市未开通',
define('INTER_CITY_CARPOOL_DEST_REGION_NOT_OPEN', '530003');
//'您的终点不在开通区域内',
define('INTER_CITY_CARPOOL_DEST_CITY_NOT_OPEN', '530004');
//'您的终点不在开通城市内',
define('INTER_CITY_CARPOOL_ROUTE_TIME_NOT_OPEN', '530005');
//'当前线路不在运营时间',
define('INTER_CITY_CARPOOL_OUT_OF_TIME', '530006');
//'当前线路不在运营时间',
define('WEIXIN_BINDID_REQUEST_ERROR', '505437');
define('WEIXIN_BINDID_PARAMS_ERROR', '505438');
define('CREATEORDER_BOOKING_ORDER_CARPOOL', '515439');
define('CREATEORDER_DEGRADE', '515440');
define('CREATEORDER_FREQUENCE_NEWORDER_CHECK', '515441');
define('CREATEORDER_FREQUENCE_CARPOOL_CHECK', '515442');
define('CREATEORDER_GEN_ORDERID_FAIL', '515443');
define('CREATEORDER_B2B_CHANNEL_ERROR', '515444');
define('CREATEORDER_FASTCAR_TEST', '515445');
define('CREATEORDER_GULFSTREAM_TEST', '515446');
define('CREATEORDER_AREA_NOT_OPEN_SERVICE', '515447');
define('CREATEORDER_AIRPORT_PARAMS_ERROR', '515448');
define('CREATEORDER_PAY_CHECK_FAIL', '515449');
define('CREATEORDER_WEBAPP_BUSIPAY_LIMIT', '515450');
define('CREATEORDER_BUSINESS_FREEZE_AMOUNT_FAIL', '515451');
define('CREATEORDER_BUSINESS_REIMBURSEMENT_AMOUNT_FAIL', '515452');
define('CREATEORDER_LOCK_AVOID_CANCEL_FAIL', '515453');
define('CREATEORDER_CREATE_ORDER_FAIL', '515454');
define('CREATEORDER_UNLOCK_AVOID_CANCEL_FAIL', '515455');
define('CREATEORDER_CHECK_CARLEVEL_FAIL', '515456');
define('CREATEORDER_CHECK_PARAMS_FAIL', '515457');
define('CREATEORDER_CHECK_CREATE_ABILITY_FAIL', '515458');
define('CREATEORDER_BUILD_ORDER_INFO_FAIL', '515459');
define('CREATEORDER_BUILD_RET_INFO_FAIL', '515460');
define('CREATEORDER_CREATE_PROCESS_FAIL', '515461');
define('CREATEORDER_POST_CREATE_PROCESS_FAIL', '515462');
define('CREATEORDER_CARPOOL_NOT_OPEN', '515463');
define('CREATEORDER_UPDATE_PASSENGER_INFO_FAIL', '515464');
define('CREATEORDER_HIDE_ADDRESS', '515465');
define('CREATEORDER_CALL_RECOMMENDATION_FAIL', '515466');
define('CREATEORDER_GET_RULEINFO_FAIL', '515467');
define('CREATEORDER_SET_RULEINFO_TO_CKV_FAIL', '515468');
define('CREATEORDER_SET_CARPOOL_DECISION_FAIL', '515469');
define('CREATEORDER_INSERT_ORDER_RELATION_FAIL', '515470');
define('CREATEORDER_GET_ORDER_PRICE_INFO_FAIL', '515471');
define('CREATEORDER_SET_FREQUENCY_CONTROL_FAIL', '515472');
define('CREATEORDER_PAY_CHECK_NETWORK_ERROR', '515473');
define('CREATEORDER_CHECK_CARLEVEL_NETWORK_ERROR', '515474');
define('CREATEORDER_COST_TOO_LONG_TIME', '515475');
define('P_ERRNO_CARPOOL_FREQUENCY_CONTROL_ERRNO', '515476');
define('P_ACTIVITY_MIS_ESTIMATE_REQ', '515477');
define('P_ACTIVITY_MIS_ESTIMATE_REQ_STATUS_ERROR', '515478');
define('P_ERRNO_TAG_RESULT_FAIL', '515479');
define('P_ERRNO_TAG_CALL_FAIL', '515480');
define('P_ERRNO_COMMON_GET_INFO_CALL_FAIL', '515481');
define('P_ERRNO_COMMON_GET_INFO_RESULT_FAIL', '515482');
define('O_ERRNO_ADD_TO_LBS', '515483');
define('O_ERRNO_DEL_TO_LBS', '515484');
define('AIR_ERRNO_INSURE_PINGAN_INPUT_ERROR', '515485');
define('AIR_ERRNO_INSURE_PINGAN_SERVICE_ERROR', '515486');
define('GEO_CODING_SYSTEM_REQUEST_ERROR', '515487');
define('GEO_CODING_SYSTEM_PARAMS_ERROR', '515488');
define('API_ADDRESS_REWRITE_ERROR', '515489');
//调用地址重写接口错误
define('API_CHECK_AIRPORT_RAILWAY_FAIL', '515490');
//校验机场或火车站失败
define('MULTI_BINDID_REQUEST_ERROR', '515491');
//多真身请求网络异常  (微信、手Q、支付宝)
define('MULTI_BINDID_PARAMS_ERROR', '515492');
//多真身请求参数错误  (微信、手Q、支付宝)
define('O_ERRNO_POI_CHECK_SERVICE_EXCEPTION', '515493');
define('O_ERRNO_WITHDRAW_ERROR', '515494');
define('O_ERRNO_AIRPORT_ERROR', '515495');
define('O_ERRNO_AIRPORT_INFO_ERROR', '515496');
define('P_ERRNO_SYSTEM_ERROR', '515497');
//老的model错误号,未重构,错误号定义范围过大
define('P_ERRNO_CHARGE_ILLEGAL', '515498');
define('P_ERRNO_TENCENT_MAP', '515499');
define('P_ERRNO_GPS_ERROR', '515500');
define('P_ERRNO_GUIDE_SYSTEM_CACHE_FAIL', '515501');
define('P_ERRNO_GUIDE_SYSTEM_PARAMS_FAIL', '515502');
define('P_ERRNO_GUIDE_SYSTEM_PROCESS_FAIL', '515503');
define('P_ERRNO_GUIDE_SYSTEM_CALL_FAIL', '515504');
define('P_ERRNO_INUPT_ERROR', '515505');
define('CREATEORDER_MINOS_CHECK_FAIL', '515506');
define('CREATEORDER_MINOS_CHECK_NETWORK_ERROR', '515507');
define('CREATEORDER_HIT_MINOS_CHECK', '515508');
define('CREATEORDER_CREATE_QUEUE_FAIL', '515509');
define('P_ERRNO_INSERT_COMMENT', '515510');
define('P_ERRNO_INSERT_ORDERCANCEL', '515511');
define('AREA_NOT_OPEN_SERVICE', '515512');
define('ERRNO_BILL_DEGRADE', '5011001');
define('CREATEORDER_PREPAY_CACHE_FAIL', '5011002');
define('CREATEORDER_CREATE_PREPAY_ORDER_ERROR', '5011003');
define('CREATEORDER_DUSE_ORDER_EXPIRE_CACHE_FAIL', '5011004');
define('O_ERRNO_OFS_QUERY_FAIL', '505141');
define('P_ERRNO_RED_PACKET_CACHE_FAIL', '505142');
define('P_ERRNO_RED_PACKET_FROM_TITAN_OVER_UPPER_BOUND', '505143');
define('P_ERRNO_RED_PACKET_FROM_TITAN_CONFIG_FAIL', '505144');
define('P_ERRNO_PAY_BUSY', '10621');
define('ERROR_CASHINER_BILL_CREATE', '505603');
define('ERROR_CASHINER_PAYMENT_STATUS', '505604');
define('ERROR_CASHINER_PAYMENT_TOOMANY_QUERY', '505605');
define('P_ANYCAR_ESTIMATE_FETCH_PARAM_ERROR', '505606');
define('ERRNO_CURL_REMOTE_DISPATCH', '5011010');
// themis（errno在51300~51399）
define('THEMIS_REQ_FAIL', '51300');
define('THEMIS_FEE_OBJECTION_PROCESS_ERROR', '51302');
define('ERRNO_CURL_MEMBER_PROFILE', 5011021);
define('ERRNO_CURL_MIS', 5011022);
define('P_ERRNO_PAY_HAS_OVERDRAFT', '1020');
//@todo 本地支付能力检查
define('P_ERRNO_BINDID_HAS_ORDER_NOY_PAY', '1040');
//@todo 本地支付能力检查
define('P_ERRNO_LOCK_FAIL', '1032');
//@todo 如果要下线，需要和端上沟通老版本处理问题
define('P_ERRNO_CAP_PRICE', '1123');
//@todo 拼车单一口价小于等于0
define('P_CAP_PRICE_EXPIRE', '1128');
//DI服务 :505601
define('O_ERRNO_ACCESS_POI_DI_ERROR', '505601');
define('O_ERRNO_BAD_POI_DI_ERROR', '505602');
// 队列
define('QUEUE_REDIS_SERVER_WENT_AWAY', '5502');
// 定制服务
define('HORAE_CUSTOM_ILLEGAL_CHOOSE', '1500');
//todo 迁移至biz-lib
//导流系统
define('ERRNO_CURL_BILL_ESTIMATE', 50102);
// 请求账单系统获取预估计错误
// curl请求的第三方的接口
define('BAPI_CONNECT_ERROR', '9710');
// 企业接口连接错误
define('BAPI_DATA_PARSE_ERROR', '9711');
// 企业接口数据格式错误
define('P_ERRNO_PAY_FREEZEAMOUNT', '10625');
//发单冻结企业用户余额失败
define('APP_VERSION_WHOLE', 'app_version_whole');
define('APP_VERSION_LITTLE_WHOLE', 'little_app_version_whole');
define('APP_VERSION_AREA_WHOLE', 'app_version_area_whole');

/*
 * 系统常量及参数定义
 */
/*
 * 队列执行方式常量定义
 */
define('QUEUE_TYPE_HTTP_POST', 'http_post');
define('QUEUE_TYPE_HTTP_GET', 'http_get');
define('QUEUE_TYPE_CLASS', 'class');
/*
 * 队列定义
 */
define('QUEUE_DEFAULT', 'default');
define('QUEUE_POSITION', 'position');
define('QUEUE_LOGS', 'logs');
define('QUEUE_PUSH', 'push');
define('QUEUE_SMS', 'sms');
define('QUEUE_ORDER', 'order');
define('QUEUE_PAY', 'pay');
define('QUEUE_DAYUER', 'dayuer');
/*---------2015-04-20 号 队列服务迁移配置--------*/
define('QUEUE_TYPE_DEFAULT', 1);
//普通队列
define('QUEUE_TYPE_TIMING', 2);
//定时队列
//>>> $queueRequestUrl = 'http://${ROUTER_GS_BRIDGE_QUEUE_IP_PORT}/gulfstream/bridgequeue/v1/'; //请求queue队列URL
$queueRequestUrl = 'http://**************:8000/gulfstream/bridgequeue/v1/';
//请求queue队列URL
//<<<
Svc::discoverHttpUrl(
    'disf!fd-bridgeq-bridgeq_web',
    '/gulfstream/bridgequeue/v1/',
    Svc::thenUpdate($queueRequestUrl)
);
define('QUEUE_REQUEST_URL', $queueRequestUrl);
define('QUEUE_MODE_REQUEST_GET', 1);
//GET请求
define('QUEUE_MODE_REQUEST_POST', 2);
//POST请求
define('QUEUE_REQUEST_NUM', 1);
//请求次数
define('QUEUE_REQUEST_TIMEOUT', 2000);
//回调请求超时时间
define('QUEUE_REQUEST_EXPIRE', 0);
//过期时间
define('QUEUE_REQUEST_TIMESTAMP', 5);
//延迟5S回调
/*普通队列定义*/
define('QUEUE_API_ORDER_DRIVER', 'queue_api_order_driver');
define('QUEUE_API_ORDER_PASSENGER', 'queue_api_order_passenger');
define('QUEUE_API_DEFAULT', 'queue_api_default');
define('QUEUE_API_B2B', 'queue_api_b2b');
define('QUEUE_API_WEBAPP', 'queue_api_webapp');
define('QUEUE_API_OPENAPI', 'queue_api_openapi');
define('QUEUE_API_NEW_ORDER', 'queue_api_new_order');
define('QUEUE_API_SPAM_REPORT', 'queue_api_spam_report');
//反作弊数据上报
define('QUEUE_API_DRIVER_ORDER', 'queue_api_driver_order');
define('QUEUE_API_ORDER_BILL', 'queue_api_order_bill');
define('QUEUE_API_CARPOOL_COUPON', 'queue_api_carpool_coupon');
//拼成大促
define('QUEUE_API_RECOVERY', 'queue_api_recovery');
define('QUEUE_CHUANLIU_ORDERCANCEL', 'queue_chuanliu_ordercancel');
/*定时队列定义*/
define('SCHEDULE_API_ORDER_DRIVER', 'schedule_api_order_driver');
define('SCHEDULE_API_ORDER_PASSENGER', 'schedule_api_order_passenger');
define('SCHEDULE_API_DEFAULT', 'schedule_api_default');
define('SCHEDULE_API_B2B', 'schedule_api_b2b');
define('SCHEDULE_API_WEBAPP', 'schedule_api_webapp');
define('SCHEDULE_API_OPENAPI', 'schedule_api_openapi');
define('SCHEDULE_API_DRIVER_RELIEVED', 'schedule_api_driver_relieved');
define('SCHEDULE_GS_API_RESEND_ORDER', 'schedule_gs_api_resend_order');
define('SCHEDULE_GS_API_CARPOOL_COUPON', 'schedule_gs_api_carpool_coupon');
define('SCHEDULE_GS_API_STRIVEORDER_CHECK', 'schedule_gs_api_striveorder_check');
/*
 * 实时通知通道常量定义
 */
define('CHANNEL_ORDER_NOTIFY', 'ORDER_NOTIFY');
define('CHANNEL_ORDER_STATUS_NOTIFY', 'ORDER_');
define('CHANNEL_DRIVER_POSITION', 'DRIVER_POSITION_');
define('CHANNEL_DRIVER_STATUS', 'DRIVER_STATUS_');
/*
 * node.js server定义
 */
define('SOCKET_IO_SERVER', 'http://10.10.10.106:49169');
/*
 * theone Avenger Push Topic
 */
define('PUSH_TOPIC_ONE_REALTIME_PRICE', 'One_Realtime_Price');
define('PUSH_TOPIC_ONE_DRIVER_ARRIVED', 'One_DriverArrived');
define('PUSH_TOPIC_ONE_STATUS_CHANGE', 'One_StatusChange');
define('ONE_PARTNER_STATUS_CHANGE', 'One_PartnerStatusChange');
define('ONE_CARPOOL_MESSAGE', 'One_CarpoolMessage');
/*
 * theone 拼车状态
*/
define('ONE_PARTNER_ADD', 10);
define('ONE_PARTNER_CANCEL', 20);
define('ONE_PARTNER_GET_ON', 30);
define('ONE_PARTNER_GET_OFF', 40);
/*
 * theone 专车订单状态
*/
define('ONE_STATUS_CREATE', 100);
define('ONE_STATUS_STRIVE', 200);
define('ONE_STATUS_ON', 300);
define('ONE_STATUS_OFF', 400);
define('ONE_STATUS_PAY', 500);
define('ONE_STATUS_CANCEL', 600);
/*
 * theone 业务线sid
*/
define('ONE_SID_ZHUANGCHE', 'premium');
define('ONE_SID_KUAICHE', 'flash');

define('LONG_TRIP_ROUTE_ID_1', 845);
define('LONG_TRIP_ROUTE_ID_2', 847);
define('LONG_TRIP_ROUTE_ID_3', 841);
define('LONG_TRIP_ROUTE_ID_4', 843);
define('FROM_DISTRICT_NAME', '禅桂区域');
define('TO_DISTRICT_NAME', '顺德区域');

define('DMC_NAMESPACE_FOR_GS_API', 'gs-api');

define('DMC_MODULE_NAME', 'disf!biz-gs-dmc');

define('PRODUCT_ID_DEFAULT', 1);
define('PRODUCT_ID_BUSINESS', 2);
define('PRODUCT_ID_FAST_CAR', 3);
define('PRODUCT_ID_BUSINESS_FAST_CAR', 4);
define('PRODUCT_ID_TRANSPORT', 111);

define('TIMING_STATION_REC_STATUS_DEFAULT', 0);    //0:默认值，
define('TIMING_STATION_REC_STATUS_NOP_NOC', 1);    //1:无车无人：“附近车辆较少，叫车时间会较长”，发单后跳转到“等待应答”页面
define('TIMING_STATION_REC_STATUS_P_NOC', 2);    //2:无车有人：“附近车辆较少，叫车时间会较长”，发单后跳转到“等待应答”页面
define('TIMING_STATION_REC_STATUS_C_NOWAIT', 3);    //3:有车且不需要愿等：“10:12在站点［＊＊］发车”，发单后跳转到“等待应答”页面

define('TIMING_STATION_TIME_TYPE_POINT', 0); //显示时间点
define('TIMING_STATION_TIME_TYPE_SECTION', 1); //显示时间区间
define('TIMING_STATION_TIME_TYPE_DEFAULT', 2); //显示站点

define('TIMING_STATION_REC_STATUS_C_WAIT', 4);    //4:有车且需要愿等：“10:12在站点［＊＊］发车”，发单后跳转到“前往站点”页面
define('TIMING_STATION_REC_STATUS_C_MATCH', 5);    //5:有车且需要愿等：“10:12在站点［＊＊］发车”，发单后跳转到“匹配中”页面
define('TIMING_STATION_REC_STATUS_C_COUPON', 6);    //5:有车且需要愿等：“10:12在站点［＊＊］发车”，发单后跳转到“愿等返券”页面
define('TIMING_STATION_REC_STATUS_C_NO_COUPON', 7); //7:有车且需要愿等：“10:12在站点［＊＊］发车”，发单后跳转到“愿等不返券”页面

//请将索引自增使用  text为显示文案  recall为是否做重新叫车提示
define('SPECIALCAR_CANCEL_REASON', 'specialcar_cancel_reason');
define('FASTCAR_CANCEL_REASON', 'fastcar_cancel_reason');
define('UNIONE_CANCEL_REASON', 'unione_cancel_reason');
define('BRAZIL_TAXI_CANCEL_REASON', 'brazil_taxi_cancel_reason');
define('MEXICO_FASTCAR_CANCEL_REASON', 'mexico_fastcar_cancel_reason');
define('CHARTERED_CAR_CANCEL_REASON', 'chartered_car_cancel_reason');

//送机险保险状态码
define('INSURE_STATUS_DEFAULT', 0); //初始值，滴滴未验证是否可投保
define('INSURE_STATUS_INSU_DIDI_CHECK_SUCCESS', '1'); //滴滴验证可投保
define('INSURE_STATUS_INSU_DIDI_CHECK_FAIL', '-1');    //滴滴验证不可投保
define('INSURE_STATUS_INSU_FINISH_CERT', '2'); //完成身份信息
define('INSURE_STATUS_INSU_NOT_FINISH_CERT', '-2'); //完成身份信息失败
define('INSURE_STATUS_INSU_SUCCESS', '3'); //投保成功
define('INSURE_STATUS_INSU_FAIL', '-3'); //投保失败
define('INSURE_STATUS_CLAIMS_DIDI_CHECK_SUCCESS', '4'); //滴滴验证可理赔
define('INSURE_STATUS_CLAIMS_DIDI_CHECK_FAIL', '-4'); //滴滴验证不可理赔
define('INSURE_STATUS_CLAIMS_REPORT_SUCCESS', '5'); //理赔报案成功
define('INSURE_STATUS_CLAIMS_REPORT_FAIL', '-5'); //理赔报案失败
define('INSURE_STATUS_CLAIMS_UPLOAD_SUCCESS', '6'); //材料上传成功
define('INSURE_STATUS_CLAIMS_UPLOAD_FAIL', '-6'); //材料上传失败
define('INSURE_STATUS_CLAIMS_FINISH_SUCCESS', '7'); //理赔成功
define('INSURE_STATUS_CLAIMS_FINISH_FAIL', '-7'); //理赔失败

//第三方回调状态
define('PINGAN_CALLBACK_STATUS_DEFAULT', 0); //已报案 未获取状态
define('PINGAN_CALLBACK_STATUS_REPORT_END', 1); //已报案（返回报案号）
define('PINGAN_CALLBACK_STATUS_PROCESSING', 2); //案件审核中
define('PINGAN_CALLBACK_STATUS_WAIT_PAY', 3); //己结案待付款(显示金额)
define('PINGAN_CALLBACK_STATUS_PAY_END', 4); //已付款
define('PINGAN_CALLBACK_STATUS_WAIT_UPLOAD', 5); //材料待补充
define('PINGAN_CALLBACK_STATUS_PAY_FAIL', 6); //支付失败
define('PINGAN_CALLBACK_STATUS_REFUSE_PAY', 7); //拒赔

//航空公司类型
define('FLIGHT_COMPANY_TYPE_DOME_CHUNQIU', 1);   //春秋
define('FLIGHT_COMPANY_TYPE_INTER', 2); //国际
define('FLIGHT_COMPANY_TYPE_DOME_NOT_CHUNQIU', 3); //国内非春秋

//国际国内航班  飞常准航班类型：0:国内-国内;1国内-国际;2国内-地区;3:地区-国际;4:国际-国际;5:未知
define('FLIGHT_TYPE_DOMESTIC_TO_DOMESTIC', 0);
define('FLIGHT_TYPE_DOMESTIC_TO_INTER', 1);
define('FLIGHT_TYPE_DOMESTIC_TO_AREA', 2);
define('FLIGHT_TYPE_AREA_TO_INTER', 3);
define('FLIGHT_TYPE_INTER_TO_INTER', 4);
define('FLIGHT_TYPE_UNKNOWN', 5);

//新业务新客引导冒泡缓存
define('P_NEW_USER_BUBBLE_GUIDE', 'p_new_user_bubble_guide');
define('P_NEW_USER_BUBBLE_GUIDE_REWARD', 'p_new_user_bubble_guide_reward');

define('P_60_GUIDE_BUBBLE_NUM_CACHE', 'p_60_scene_guide_bubble_num_cache');

/*-------------------------以下  预估response改造灰度阶段 rpc 缓存key ------------------------------------*/
//个性化服务
define('CUSTOM_SERVICE_REGISTRY_KEY', 'custom_service_info');
define('WALK_ORDER_COUNT', 'walk_order_count');

//乘客是否发过同程订单
define('TRIPCLOUD_IS_USED', 'tripcloud_is_used_');

//站点去尾弹窗次数
define('CARPOOL_REMOVE_END_BLOCK_COUNT', 'carpool_remove_end_count_');
define('CARPOOL_REMOVE_END_BLOCK_NEW_COUNT', 'carpool_remove_end_new_count_');
define('CARPOOL_STATION_INFO_CACHE', 'carpool_station_info_cache_');

//红包信息
define('GET_RED_PACKET_LAST_VALUE', 'get_red_packet_last_value');
define('SET_RED_PACKET_LAST_VALUE', 'set_red_packet_last_value');

//小巴新手教育标记
define('GET_XIAOBA_GUIDE_FLAG', 'get_xiaoba_guide_flag');
define('SET_XIAOBA_GUIDE_FLAG', 'set_xiaoba_guide_flag');
define('XIAOBA_GUIDE_PUBLIC_LOG', 'xiaoba_guide_public_log');

//特惠快车新手教育
define('GET_SPECIAL_RATE_GUIDE_FLAG', 'get_special_rate_guide_flag');
define('SET_SPECIAL_RATE_GUIDE_FLAG', 'set_special_rate_guide_flag');

//获取愿等&推荐站点request
define('LIKE_WAIT_REQUEST', 'get_like_wait_request');
define('LIKE_WAIT_RESPONSE', 'get_like_wait_response');

//etd信息写缓存
define('CARPOOL_ESTIMATE_ARRIVAL', 'carpool_estimate_arrival_');

//通勤卡写缓存
define('COMMUTE_CARD_REJECTION_RECORD_LIST', 'commute_card_rejection_record_list');
define('COMMUTE_H5_REJECT_RECORD', 'commute_H5_reject_record');
define('COMMUTE_CARD_CACHE', 'commute_card_cache');
define('FREE_COMMUTE_CARD_CACHE', 'free_commute_card_cache');
define('COMMUTE_INFO', 'commute_info');

//城际拼车时间片价格request
define('TIME_PAIRS_PRICE_REQUEST', 'get_time_pairs_price_request');
define('TIME_PAIRS_PRICE_RESPONSE', 'get_time_pairs_price_response');

//城际拼车时间片运力request
define('TIME_PAIRS_AVAILABLE_REQUEST', 'get_time_pairs_available_request');
define('TIME_PAIRS_AVAILABLE_RESPONSE', 'get_time_pairs_available_response');

//券
define('P_SHOW_WEBAPP_COUPON_INFO_REGISTRY_KEY', 'p_show_webapp_coupon_info_registry_key');

//carpool booking info
define('GET_BOOKING_INFO_REGISTRY_KEY', 'get_booking_info_registry_key');

//set estimate id
define('SET_ESTIMATE_ID_REGISTRY_KEY', 'set_estimate_id_registry_key');
define('SET_CARPOOL_ESTIMATE_ID_REGISTRY_KEY', 'set_carpool_estimate_id_registry_key');

//introMsg拼车apollo结果public日志
define('XIAOBA_BUTTON_TEST_KEY', 'xiaoba_button_text_toggle');
define('CARPOOL_BRANDNAME_TEST_KEY', 'carpool_brandname_test_key');

//anycar多车型偏好 public日志
define('ANYCAR_PREFERENCE_LIST', 'anycar_product_list_');

//athena 请求参数
define('ATHENA_REQUEST', 'athena_request');

//athena 返回结果
define('ATHENA_RESULT', 'athena_result');
define('ESTIMATE_PRICE_RESULT', 'estimate_price_result');

//城际司机扫码发单信息
define('D_INTER_DRIVER_QRCODE','d_inter_qrcode');

//预估eid关联缓存
define('P_ESTIMATE_ID_LINK_PREFIX', 'p_estimate_id_link_prefix');

//estimate_id关联命中业务围栏id缓存
define('P_ESTIMATE_ID_FENCE_ID_LINK_PREFIX', 'p_estimate_id_fence_id_link_prefix');

//价格沟通组件
define('P_SPECIAL_PRICE_COMPONENT', 'p_special_price_component');

//香港出租车小费
define('P_HK_TAXI_TIP_INFO', 'p_hk_taxi_tip_info');
//香港出租车捎话
define('P_HK_TAXI_COMMENT_INFO', 'p_hk_taxi_comment_info');
/*------------------------以上  预估response改造灰度阶段 rpc 缓存key ----------------------------------*/

//用traceID当索引记录一些预估的基础数据,报价单里没有的那些，给沟通组件渲染表单信息用
define('P_ESTIMATE_ORIGIN_DATA', 'p_estimate_origin_data');

// 拼车城际库存占座成功的缓存前缀
define('O_INTER_MATCH_OCC_SUC_STATUS', 'O_INTER_MATCH_OCC_SUC_STATUS_');

// 好友赠卡类型
define('FRIENDSHIP_CARD_TYPE', 3);

// 特价拼车场景来源标识（1：场景入口；2：导流入口；3：金刚位；4：短信跳转首页；5：短信跳转冒泡；6：其他；7：青菜导流城际）小程序只包含导流入口、金刚位、青菜导流城际
define('LOW_PRICE_DIAMON_SCENE_TYPE', 3); // 从金刚位进入

// 常用语校验失败errno
define('COMMON_EXPRESSION_VERIFY_FAILED_TYPE', '2');

// 备注校验失败errno
define('REMARK_CONTENT_VERIFY_FAILED_TYPE', '3');

// 昵称校验失败errno
define('NICKNAME_CONTENT_VERIFY_FAILED_TYPE', '4');

// 昵称无效errno
define('NICKNAME_CONTENT_INVALID_TYPE', '5');

// 昵称敏感词校验类型
define('NICKNAME_DETECT_TYPE', 0);

// 备注敏感词校验类型
define('REMARK_DETECT_TYPE', 1);

// 常用语敏感词校验类型
define('COMMON_EXPRESSION_DETECT_TYPE', 2);

// 价格文案前缀类型
define('PRICE_PREFIX_DEFAULT', 0); // 默认
define('PRICE_PREFIX_NULL', 1);    // null
define('PRICE_PREFIX_CAP', 2);     // 一口价
define('PRICE_PREFIX_FIXED', 3);   // 固定价
define('PRICE_PREFIX_PREMIUM', 4); // 专车价

// 城市ID
const CITY_ID_HONGKONG = 357;

const P_SIDE_ESTIMATE_EVENT_TOP    = 'top';
const P_SIDE_ESTIMATE_EVENT_BOTTOM = 'bottom';

// 报价单的KEY
const QUOTATION_KEY_TORB_TUNNEL_FEE       = 'ToRb_tunnel_fee';
const QUOTATION_KEY_TORB_BACKTRACKING_FEE = 'ToRb_backtracking_fee';
const QUOTATION_KEY_FEE_DETAIL_INFO       = 'fee_detail_info';
const QUOTATION_KEY_BILL2API_TUNNEL_FEE_DETAIL = 'BILL2API_tunnel_fee_detail';

const PRE_MATCH_TYPE_SUCCESS = 0; //预匹配成功
const PRE_MATCH_TYPE_NO_TP = 1; //预匹配候补
const MAP_CURVE_TYPE_EMPTY = 0; //曲线类型：0-不展示曲线
const MAP_CURVE_TYPE_BLUE_GRAY = 1; //曲线类型：1-展示蓝灰曲线（小巴快线）
const MAP_CURVE_TYPE_ONLY_BLUE = 2; //曲线类型：2-展示蓝色曲线（小巴普通&智能小巴）
