<?php

/*
 * @desc   : 定义全局存储服务错误码code（从505001~505100，6位错误码）
 *           定义全局rpc系统交互错误码code（从505101~505400，6位错误码）
 *           定义全局业务逻辑错错误码code（从505401开始，6位错误码）
 * @user : l<PERSON>chuan<<EMAIL>>
 * @date   : 2016-04-26
 */
//成功返回0,错误返回505000
define('GLOBAL_SUCCESS', '0');
define('GLOBAL_PARAMS_ERROR', '505000');
define('GLOBAL_DSIG_ERROR', '520');
//乘客端
define('P_ERRNO_TOKEN_ERROR', '101');
// 兼容打车错误码
define('P_ERRNO_PARAMS_ERROR', '1010');
define('P_ERRNO_DYNAMIC_PRICE', '1102');
//动态调价客户端和服务端不一致
/*
 * @desc 存储服务错误码定义:redis,mysql
 * @desc 错误码分段:505001~505100
 *--------------------------------------
 */
//数据库
define('DB_ERRNO_LOAD_ERROR', '505001');
define('DB_ERRNO_TABLEFIELDS', '505002');
define('DB_ERRNO_INSERT', '505003');
define('DB_ERRNO_DELETE', '505004');
define('DB_ERRNO_SELECT', '505005');
define('DB_ERRNO_UPDATE', '505006');
define('DB_ERRNO_GET_STRATEGY_CONF', '505007');
define('DB_ERRNO_PROCESS_STRATEGY_CONF', '505008');
define('DB_SYNC_STRATEGY_CONF_ERROR', '505009');
define('DB_ERRNO_UPDATE_NEW_GUID_ERROR', '505010');
//缓存
define('CACHE_ERRNO_LOAD_ERROR', '505011');
define('CACHE_ERRNO_SET', '505012');
define('CACHE_ERRNO_GET', '505013');
define('CACHE_ERRNO_DELETE', '5050014');
define('CACHE_QUEUE_OUT_LIMIT', '505015');
define('CACHE_CKV_ERROR', '505016');
define('CACHE_CKV_LOCK_PROCESS', '505017');
define('CACHE_BINDIDS_REDIS_KEY_SETEX_FAILED', '505018');
/*
 * @desc rpc系统交互错误码定义:订单系统,司乘系统
 * @desc 错误码分段:505101~505400
 *---------------------------------------------------------
 */
//订单系统
define('O_ERRNO_ORDER_SYSTEM_NETWORK_EXCEPTION', '505101');
define('O_ERRNO_GET_ORDER_INFO_ERROR', '505102');
define('O_ERRNO_GET_MULTI_ORDER_INFO_ERROR', '505103');
define('O_ERRNO_GET_ORDER_EXTRA_INFO_ERROR', '505104');
define('O_ERRNO_UPDATE_ORDER_INFO_ERROR', '505105');
define('O_ERRNO_NEW_ORDER_INFO_ERROR', '505106');
define('O_ERRNO_ASSIGNED_ORDER_INFO_ERROR', '505107');
define('O_ERRNO_PERPAREED_ORDER_INFO_ERROR', '505108');
define('O_ERRNO_CANCELED_ORDER_INFO_ERROR', '505109');
define('O_ERRNO_BEGINED_ORDER_INFO_ERROR', '505110');
define('O_ERRNO_FINISHED_ORDER_INFO_ERROR', '505111');
define('O_ERRNO_COMPLETEED_ORDER_INFO_ERROR', '505112');
define('O_ERRNO_ADD_ORDER_FEATURE_ERROR', '505113');
define('O_ERRNO_UPDATE_ORDER_FEATURE_ERROR', '505114');
define('O_ERRNO_DELETE_ORDER_FEATURE_ERROR', '505115');
define('O_ERRNO_DISCARDED_ORDER', '505116');
define('O_ERRNO_UPDATE_ORDER_FEAURE_ERROR', '505117');
define('O_ERRNO_ENCRYPT_FREQUENCY_FAST', '505118');
// 加密订单id频率过快
define('O_ERRNO_ENCRYPT_ORDERID_FAIL', '505119');
// 加密订单id失败
define('O_ERRNO_DECRYPT_ORDERID_FAIL', '505120');
// 解密订单id失败
define('O_ERRNO_QUERY_LINEUP_FEATURE_ERROR', '505121');
//查询排队订单
define('O_ERRNO_CHECK_ORDER_STATUS_FAIL', '505122');
define('O_ERRNO_LINEUP_ORDER_UPDATE_FAIL', '505123');
define('O_ERRNO_TRIPCLOUD_ORDER_UPDATE_FAIL', '505124');
define('O_ERRNO_DYNAMIC_PRICE_EMPTY', '3034');
//动态调价信息为空
//司机系统rpc
define('DRIVER_RPC_GET_DRIVET_DATA_ERROR', '80009');
//司乘系统
define('D_ERRNO_GRAB_SYSTEM_NETWORK_EXCEPTION', '505151');
define('D_ERRNO_GRAB_SYSTEM_MAKEING_DECISIONS', '505152');
define('D_ERRNO_GRAB_SYSTEM_ASSIGNED_ORDER', '505153');
define('D_ERRNO_PARAMS_ERROR', '505154');
define('D_ERRNO_GET_COORD_ERROR', '505155');
define('D_ERRNO_GET_DRIVER_INFO_ERROR', '505156');
define('D_ERRNO_GET_DRIVER_FEATURE_ERROR', '505157');
define('D_ERRNO_UPDATE_DRIVER_INFO_ERROR', '505158');
define('D_ERRNO_UPDATE_DRIVER_FEATURE_ERROR', '505159');
define('D_ERRNO_PROCESS_DRIVER_ON_ORDEER_CHARAGE_ERROR', '505160');
define('D_ERRNO_DRIVER_SYSTEM_NETWORK_EXCEPTION', '505161');
define('D_ERRNO_SCANPAY_NO_ACCESSBILITY', '505162');
define('D_QRCODE_EXPIRED', '505163');
define('D_ERRNO_SCANPAY_NO_COUPON_ALLOWED', '505164');
define('D_ERRNO_SCANPAY_COUPON_USAGE_DENIED', '505165');
define('D_GET_COORD_ERROR', '2184');
//locsvr 获取司机坐标错误
//DUSE系统 505201开始
define('O_ERRNO_DUSE_CANCEL_ORDER', '505201');
/*
 * @desc 业务逻辑错误码定义
 * @desc 错误码分段:505401开始
 *---------------------------------------------------------
 */
define('P_ERRNO_ORDER_CANCEL_LOCKED', '505401');
define('P_ERRNO_ORDER_SYNC_KAFKA_FAIL', '505402');
define('P_ERRNO_TOKEN_ERROR', '505403');
define('P_ERRNO_FREQUENCY_CONTROL_ERROR', '505404');
define('P_ERRNO_CHARGE_ORDER', '505405');
define('P_ERRNO_RECANCEL_ORDER', '505406');
define('P_ERRNO_SPAM', '505407');
define('P_ERRNO_INSERT_PUBNISHFORWARD', '505408');
define('P_ERRNO_ORDER_HAS_CLOSE_ERROR', '505409');
define('P_ERRNO_CURL_PASSPORT', '505410');
define('O_ERRNO_ORDER_TIMEOUT', '505411');
define('O_ERRNO_ORDER_NOT_UNSTRIVED', '505412');
define('O_ERRNO_INVALID_OID', '505413');
define('KAFKA_SEND_FAIL', '505414');
define('KAFKA_PARTITIONS_ERROR', '505415');
define('KAFKA_EXCEPTION', '505416');
define('QUEUE_EXECUTE_ERROR', '505417');
define('PUSH_SEND_COMMON_MSG_ERROR', '505418');
define('PUSH_SEND_BROADCAST_MSG_ERROR', '505419');
define('COMMON_PARAMS_ERROR', '505420');
define('COMMON_REQUEST_ERROR', '505421');
define('OP_BOOK_DRIVER_ORDER_LISTCKV_ERROR', '505422');
define('D_ERRNO_EXCEPTION_FOR_WAIT', '505423');
//老api迁移过来的错误号,后续改掉
define('API_CHECK_AIRPORT_RAILWAY_FAIL', '505424');
define('AIRPORT_ERRNO_GETFLIAGHT_RPC', '505425');
define('AIRPORT_ERROR_ORDER_WAIT_COMPENSATE_DRIVER_RPC', '505426');
define('ORDER_CALL_THEMIS_FAILED', '505427');
define('O_ERRNO_CONTROL_TRANSFER_TODRIVER_FAILED', '505428');
define('O_ERRNO_BILLID_ERROR', '505429');
define('O_ERRNO_TRANSFER_ERROR', '505430');
define('O_ERRNO_CASHIER_WRITE_ORDERRESULT_INFO', '505431');
define('CARPOOL_DEL_DRIVER_TRAVELORDER_REDIS_ERROR', '505432');
define('CARPOOL_RPC_CALL_FAIL_ERROR', '505433');
define('CARPOOL_UPDATE_FAIL', '505434');
define('CARPOOL_DEL_COUPON_REWARD_ERROR', '505435');
define('CARPOOL_GET_REL_PASSENGER_ERROR', '505436');
define('INTER_CITY_CARPOOL_START_REGION_NOT_OPEN', '530001');
//'您的起点不在开通区域内',
define('INTER_CITY_CARPOOL_START_CITY_NOT_OPEN', '530002');
//'起点城市未开通',
define('INTER_CITY_CARPOOL_DEST_REGION_NOT_OPEN', '530003');
//'您的终点不在开通区域内',
define('INTER_CITY_CARPOOL_DEST_CITY_NOT_OPEN', '530004');
//'您的终点不在开通城市内',
define('INTER_CITY_CARPOOL_ROUTE_TIME_NOT_OPEN', '530005');
//'当前线路不在运营时间',
define('INTER_CITY_CARPOOL_OUT_OF_TIME', '530006');
//'当前线路不在运营时间',
define('WEIXIN_BINDID_REQUEST_ERROR', '505437');
define('WEIXIN_BINDID_PARAMS_ERROR', '505438');
define('CREATEORDER_BOOKING_ORDER_CARPOOL', '515439');
define('CREATEORDER_DEGRADE', '515440');
define('CREATEORDER_FREQUENCE_NEWORDER_CHECK', '515441');
define('CREATEORDER_FREQUENCE_CARPOOL_CHECK', '515442');
define('CREATEORDER_GEN_ORDERID_FAIL', '515443');
define('CREATEORDER_B2B_CHANNEL_ERROR', '515444');
define('CREATEORDER_FASTCAR_TEST', '515445');
define('CREATEORDER_GULFSTREAM_TEST', '515446');
define('CREATEORDER_AREA_NOT_OPEN_SERVICE', '515447');
define('CREATEORDER_AIRPORT_PARAMS_ERROR', '515448');
define('CREATEORDER_PAY_CHECK_FAIL', '515449');
define('CREATEORDER_WEBAPP_BUSIPAY_LIMIT', '515450');
define('CREATEORDER_BUSINESS_FREEZE_AMOUNT_FAIL', '515451');
define('CREATEORDER_BUSINESS_REIMBURSEMENT_AMOUNT_FAIL', '515452');
define('CREATEORDER_LOCK_AVOID_CANCEL_FAIL', '515453');
define('CREATEORDER_CREATE_ORDER_FAIL', '515454');
define('CREATEORDER_UNLOCK_AVOID_CANCEL_FAIL', '515455');
define('CREATEORDER_CHECK_CARLEVEL_FAIL', '515456');
define('CREATEORDER_CHECK_PARAMS_FAIL', '515457');
define('CREATEORDER_CHECK_CREATE_ABILITY_FAIL', '515458');
define('CREATEORDER_BUILD_ORDER_INFO_FAIL', '515459');
define('CREATEORDER_BUILD_RET_INFO_FAIL', '515460');
define('CREATEORDER_CREATE_PROCESS_FAIL', '515461');
define('CREATEORDER_POST_CREATE_PROCESS_FAIL', '515462');
define('CREATEORDER_CARPOOL_NOT_OPEN', '515463');
define('CREATEORDER_UPDATE_PASSENGER_INFO_FAIL', '515464');
define('CREATEORDER_HIDE_ADDRESS', '515465');
define('CREATEORDER_CALL_RECOMMENDATION_FAIL', '515466');
define('CREATEORDER_GET_RULEINFO_FAIL', '515467');
define('CREATEORDER_SET_RULEINFO_TO_CKV_FAIL', '515468');
define('CREATEORDER_SET_CARPOOL_DECISION_FAIL', '515469');
define('CREATEORDER_INSERT_ORDER_RELATION_FAIL', '515470');
define('CREATEORDER_GET_ORDER_PRICE_INFO_FAIL', '515471');
define('CREATEORDER_SET_FREQUENCY_CONTROL_FAIL', '515472');
define('CREATEORDER_PAY_CHECK_NETWORK_ERROR', '515473');
define('CREATEORDER_CHECK_CARLEVEL_NETWORK_ERROR', '515474');
define('CREATEORDER_COST_TOO_LONG_TIME', '515475');
define('P_ERRNO_CARPOOL_FREQUENCY_CONTROL_ERRNO', '515476');
define('P_ACTIVITY_MIS_ESTIMATE_REQ', '515477');
define('P_ACTIVITY_MIS_ESTIMATE_REQ_STATUS_ERROR', '515478');
define('P_ERRNO_TAG_RESULT_FAIL', '515479');
define('P_ERRNO_TAG_CALL_FAIL', '515480');
define('P_ERRNO_COMMON_GET_INFO_CALL_FAIL', '515481');
define('P_ERRNO_COMMON_GET_INFO_RESULT_FAIL', '515482');
define('O_ERRNO_ADD_TO_LBS', '515483');
define('O_ERRNO_DEL_TO_LBS', '515484');
define('AIR_ERRNO_INSURE_PINGAN_INPUT_ERROR', '515485');
define('AIR_ERRNO_INSURE_PINGAN_SERVICE_ERROR', '515486');
define('GEO_CODING_SYSTEM_REQUEST_ERROR', '515487');
define('GEO_CODING_SYSTEM_PARAMS_ERROR', '515488');
define('API_ADDRESS_REWRITE_ERROR', '515489');
//调用地址重写接口错误
define('API_CHECK_AIRPORT_RAILWAY_FAIL', '515490');
//校验机场或火车站失败
define('MULTI_BINDID_REQUEST_ERROR', '515491');
//多真身请求网络异常  (微信、手Q、支付宝)
define('MULTI_BINDID_PARAMS_ERROR', '515492');
//多真身请求参数错误  (微信、手Q、支付宝)
define('O_ERRNO_POI_CHECK_SERVICE_EXCEPTION', '515493');
define('O_ERRNO_WITHDRAW_ERROR', '515494');
define('O_ERRNO_AIRPORT_ERROR', '515495');
define('O_ERRNO_AIRPORT_INFO_ERROR', '515496');
define('P_ERRNO_SYSTEM_ERROR', '515497');
//老的model错误号,未重构,错误号定义范围过大
define('P_ERRNO_CHARGE_ILLEGAL', '515498');
define('P_ERRNO_TENCENT_MAP', '515499');
define('P_ERRNO_GPS_ERROR', '515500');
define('P_ERRNO_GUIDE_SYSTEM_CACHE_FAIL', '515501');
define('P_ERRNO_GUIDE_SYSTEM_PARAMS_FAIL', '515502');
define('P_ERRNO_GUIDE_SYSTEM_PROCESS_FAIL', '515503');
define('P_ERRNO_GUIDE_SYSTEM_CALL_FAIL', '515504');
define('P_ERRNO_INUPT_ERROR', '515505');
define('CREATEORDER_MINOS_CHECK_FAIL', '515506');
define('CREATEORDER_MINOS_CHECK_NETWORK_ERROR', '515507');
define('CREATEORDER_HIT_MINOS_CHECK', '515508');
define('CREATEORDER_CREATE_QUEUE_FAIL', '515509');
define('P_ERRNO_INSERT_COMMENT', '515510');
define('P_ERRNO_INSERT_ORDERCANCEL', '515511');
define('AREA_NOT_OPEN_SERVICE', '515512');
define('ERRNO_BILL_DEGRADE', '5011001');
define('CREATEORDER_PREPAY_CACHE_FAIL', '5011002');
define('CREATEORDER_CREATE_PREPAY_ORDER_ERROR', '5011003');
define('CREATEORDER_DUSE_ORDER_EXPIRE_CACHE_FAIL', '5011004');
define('O_ERRNO_OFS_QUERY_FAIL', '505141');
define('P_ERRNO_RED_PACKET_CACHE_FAIL', '505142');
define('P_ERRNO_RED_PACKET_FROM_TITAN_OVER_UPPER_BOUND', '505143');
define('P_ERRNO_RED_PACKET_FROM_TITAN_CONFIG_FAIL', '505144');
define('P_ERRNO_PAY_BUSY', '10621');
define('ERROR_CASHINER_BILL_CREATE', '505603');
define('ERROR_CASHINER_PAYMENT_STATUS', '505604');
define('ERROR_CASHINER_PAYMENT_TOOMANY_QUERY', '505605');
define('ERRNO_CURL_REMOTE_DISPATCH', '5011010');
define('P_ANYCAR_ESTIMATE_FETCH_PARAM_ERROR', '505606');
// themis（errno在51300~51399）
define('THEMIS_REQ_FAIL', '51300');
define('THEMIS_FEE_OBJECTION_PROCESS_ERROR', '51302');
define('ERRNO_CURL_MEMBER_PROFILE', 5011021);
define('ERRNO_CURL_MIS', 5011022);
define('P_ERRNO_PAY_HAS_OVERDRAFT', '1020');
//@todo 本地支付能力检查
define('P_ERRNO_BINDID_HAS_ORDER_NOY_PAY', '1040');
//@todo 本地支付能力检查
define('P_ERRNO_LOCK_FAIL', '1032');
//@todo 如果要下线，需要和端上沟通老版本处理问题
define('P_ERRNO_CAP_PRICE', '1123');
//@todo 拼车单一口价小于等于0
define('P_CAP_PRICE_EXPIRE', '1128');
//DI服务 :505601
define('O_ERRNO_ACCESS_POI_DI_ERROR', '505601');
define('O_ERRNO_BAD_POI_DI_ERROR', '505602');
// 队列
define('QUEUE_REDIS_SERVER_WENT_AWAY', '5502');
// 定制服务
define('HORAE_CUSTOM_ILLEGAL_CHOOSE', '1500');
//todo 迁移至biz-lib
//导流系统
define('ERRNO_CURL_BILL_ESTIMATE', 50102);
// 请求账单系统获取预估计错误
// curl请求的第三方的接口
define('BAPI_CONNECT_ERROR', '9710');
// 企业接口连接错误
define('BAPI_DATA_PARSE_ERROR', '9711');
// 企业接口数据格式错误
define('P_ERRNO_PAY_FREEZEAMOUNT', '10625');
//发单冻结企业用户余额失败
// 途经点和场景（如机场、火车站）冲突错误码
define('P_ERRNO_STOPOVER_POINTS_CONFLICTS_WITH_SCENES', '1035013');
$config['errno'] = array(
    //成功或错误
    '0'               => 'SUCCESS',
    '505000'          => 'params is error',

    GLOBAL_DSIG_ERROR => '',

    //乘客端
    '101'             => '登录已过期，请重新登录~',
    '1010'            => '好像出错了~再试一次？',
    '1102'            => '当前订单价格已更新，请重新确认~',

    '1020'            => '有未支付订单，需先完成支付，才能继续呼叫接驾车辆。',  //@todo 本地支付能力检查
    '1040'            => '您有未支付订单', //@todo 本地支付能力检查
    '1032'            => '发单频率太快，请稍等一会儿再发',    //@todo 锁逻辑
    '1073'            => '显示对话框', //唤起乘客端对话框标准状态码
    '1123'            => '拼车一口价获取中',   //@todo 拼车一口价检查逻辑
    '1128'            => '一口价获取中',
    '10621'           => '当前收银台排队人数过多，请稍后再尝试付款',
    '1127'            => '需要进行facebook认证或者绑卡认证',
    '1128'            => '需要进行用户信用卡/借记卡验证',

    /*
     * @desc 存储服务错误码定义:redis,mysql
     * @desc 错误码分段:505001~505100
     *--------------------------------------
     */

    //数据库
    '505001'          => 'db init error',
    '505002'          => 'db table fields error',
    '505003'          => 'db insert error',
    '505004'          => 'db delete error',
    '505005'          => 'db select error',
    '505006'          => 'db update error',

    //缓存
    '505011'          => 'redis init error',
    '505012'          => 'redis set error',
    '505013'          => 'redis read error',
    '505014'          => 'redis delete error',

    /*
     * @desc rpc系统交互错误码定义:订单系统,司乘系统
     * @desc 错误码分段:505101~505400
     *---------------------------------------------------------
     */

    //订单系统
    '505101'          => 'order system network exception',
    '505102'          => 'failure to obtain order information from the order system',
    '505103'          => 'failure to obtain mutil order information from the order system',
    '505104'          => 'failure to obtain order extra information from the order system',
    '505105'          => 'failure to update order information from the order system',
    '505106'          => 'failure to new order information from the order system',
    '505107'          => 'failure to assign order information from the order system',
    '505108'          => 'failure to prepare order information from the order system',
    '505109'          => 'failure to cancle order information from the order system',
    '505110'          => 'failure to begin order information from the order system',
    '505111'          => 'failure to finish order information from the order system',
    '505112'          => 'failure to compete order information from the order system',
    '505113'          => 'failure to add order feature from the duse system',
    '505114'          => 'failure to update order feature from the duse system',
    '505115'          => 'failure to delete order feature from the duse system',
    '505116'          => 'order is discarded',
    '505117'          => 'update order feature fail',
    '505118'          => 'encrypt orderid frequency too fast',
    '505119'          => 'encrypt order id fail',
    '505120'          => 'decrypt order id fail',
    '505121'          => 'query lineup info fail',
    '505122'          => 'check order status fail',
    '505123'          => 'lineup order, update order system fail',

    //司乘系统
    '505151'          => 'grab system network exception',
    '505152'          => 'grab system is making decisions',
    '505153'          => 'grab system was assigned order',
    '505154'          => 'params error to request driver system',
    '505155'          => 'failure to obtain driver coordinate from this locsvr',
    '505156'          => 'failure to obtain driver info from this driver system',
    '505157'          => 'failure to obtain driver feature from this duse system',
    '505158'          => 'failure to update driver feature from this driver system',
    '505159'          => 'failure to update driver feature from this duse system',
    '505160'          => 'process driver on order status change fail',
    '505161'          => 'driver system network exception',

    '505201'          => 'duse check cancel order error',
    '505141'          => 'get ofs error',

    /*
     * @desc 业务逻辑错误码定义
     * @desc 错误码分段:505401开始
     *---------------------------------------------------------
     */
    '505401'          => 'forbid order cancellation has been locked',
    '505402'          => 'order sync kafka fail',
    '505403'          => 'token param error',
    '505404'          => 'updateNewOrderFreq set cache fail',
    '505405'          => 'the trip has begin and can not cancel',
    '505406'          => 'the driver has close the order',
    '505407'          => 'hit the antispam frequency control',
    '505408'          => 'insert the publish forward fail',
    '505409'          => 'the order has close',
    '505410'          => 'passport system network exception',
    '505411'          => 'order timeout',
    '505412'          => 'order status is not unstrived',
    '505413'          => 'invalid oid',
    '505414'          => 'order information sent to kafka failed ',
    '505415'          => 'kafka partitions error',
    '505416'          => 'kafka network exception',
    '505417'          => 'queue execure error',
    '505418'          => 'push common message fail',
    '505419'          => 'push broadcard message fail',
    '505420'          => 'params error to request common system',
    '505421'          => 'common system network exception',
    '505422'          => 'do book driver order list error',
    '505423'          => 'network exception',
    '505424'          => 'airport railway check fail',
    '505425'          => 'get flight info error',
    '505426'          => 'compensate driver airpot order fail',
    '505427'          => 'call themis fail',
    '505428'          => 'control transfer to driver fail',
    '505429'          => 'billid error',
    '505430'          => 'pay transfer error',
    '505431'          => 'call cashier writeOrderResultInfo fail',
    '505432'          => 'carpool del driver travel order redis fail',
    '505433'          => 'carpool rpc call fail',
    '505434'          => 'carpool update fail',
    '505435'          => 'carpool del coupon reward fail',
    '505436'          => 'carpool rpc call getRelPassenger fail',
    '505437'          => 'network exception to get weixin bindid',
    '505438'          => 'params error to get weixin bindid ',
    '515439'          => 'booking order not support car pool',
    '515440'          => 'degrade discard order',
    '515441'          => 'frequency control new order create check fail',
    '515442'          => 'frequency control carpool check fail',
    '515443'          => 'gen new order id fail',
    '515444'          => 'b2b channel error',
    '515445'          => 'fastcar test, only permit test channel create order',
    '515446'          => 'gulfstream test, only permit test channel create order',
    '515447'          => 'current area not open service',
    '515448'          => 'airport order params error',
    '515449'          => 'pay check fail',
    '515450'          => 'webapp business pay limit',
    '515451'          => 'business pay freeze fail',
    '515452'          => 'business pay reimbursement fail',
    '515453'          => 'set lock for avoid cancel order fail',
    '515454'          => 'call order system fail',
    '515455'          => 'set unlock for avoid cancel order fail',
    '515456'          => 'check car level fail',
    '515457'          => 'check create order params fail',
    '515458'          => 'check create ability fail',
    '515459'          => 'build create order message fail',
    '515460'          => 'build create order ret message fail',
    '515461'          => 'create order process fail',
    '515462'          => 'post create order process fail',
    '515463'          => 'carpool not open in this area',
    '515464'          => 'update passenger info fail',
    '515465'          => 'coordinate is in hide address range',
    '515466'          => 'call recommendation service fail',
    '515467'          => 'get order rule info fail',
    '515468'          => 'set order rule info to storage fail',
    '515469'          => 'save carpool decision fail',
    '515470'          => 'insert order relation fail',
    '515471'          => 'get order price info fail',
    '515472'          => 'set frequency control fail',
    '515473'          => 'check pay ability network error',
    '515474'          => 'check carlevel network error',
    '515475'          => 'create order cost too long time',
    '515476'          => 'the number of carpool to on-line limit',
    '515477'          => 'estimate request passenger activity error',
    '515478'          => 'estimate request passenger activity result error',
    '515479'          => 'failure to obtain info from this tag system',
    '515480'          => 'tag system network exception',
    '515481'          => 'common system network exception',
    '515482'          => 'failure to obtain user info from this common system',
    '515483'          => 'failure to add order from this lbs',
    '515484'          => 'failure to delete order form this lbs',
    '515485'          => 'airport ping an insurance input parameters error',
    '515486'          => 'airport ping an insurance network exception',
    '515487'          => 'address inverse solution network exception',
    '515488'          => 'address inverse solution param error',
    '515489'          => 'address rewrite error',
    '515490'          => 'check airport and railway fail',
    '515491'          => 'multi bindid request error',
    '515492'          => 'multi bindid request param error',
    '515493'          => 'check whether the coordinates of the airport service network exception',
    '515494'          => 'order cash withdrawal is exception',
    '515495'          => 'failure to match for airport info',
    '515496'          => 'failure to obtain airport info',
    '515497'          => 'network exception to request from other system',
    '515498'          => 'failed to freeze enterprise user balance when creating an order',
    '515499'          => 'failure to obstain info from this tencent map',
    '515500'          => 'GPS exception',
    '515501'          => 'guide system cache error',
    '515502'          => 'guide system request params error',
    '515503'          => 'guide system inner error',
    '515504'          => 'guide system network exception',
    '515505'          => 'params type error',
    '515506'          => 'create order minos check fail',
    '515507'          => 'call minos network error',
    '515508'          => 'hit minos strategy',
    '515509'          => 'create order queue fail',
    '515510'          => 'save comment or complaint fail',
    '515511'          => 'insert order cancel fail',
    '515512'          => 'current area not open service',
    '505601'          => 'access map di fail',
    '505602'          => 'bad poi id',
    '5011001'         => 'bill&pay degrade',
    '5011002'         => 'set the pre pay order cache fail',
    '5011003'         => 'request error from cashier\'s api createPrepayOrder',

    '505603'          => 'fail to create cashier bill order',
    '505604'          => 'fail to get cashier payment status',
    '505605'          => 'too many queries',

    '505606'          => '获取anycar预估参数失败',

    '5011010'         => 'guide request fail',
    '5011021'         => 'request member fail',

    //跨区域拼车，待翻译
    '530001'          => '您的起点不在开通区域内',
    '530002'          => '起点城市未开通',
    '530003'          => '您的终点不在开通区域内',
    '530004'          => '您的终点不在开通城市内',
    '530005'          => '当前线路不在运营时间',
    //队列
    '5502'            => 'QUEUE_REDIS_SERVER_WENT_AWAY',

    // 定制服务
    '1500'            => 'illegal choose custom service',

    //导流系统
    '50102'           => '请求账单系统获取预估计错误',

    // 调用企业接口
    '9710'            => '调用企业端接口出错',
    '9711'            => '企业接口返回信息格式错误',

    '10625'           => '企业支付请求失败',

    //扫码付
    '505162'          => '因司机账号审核未通过等原因，扫码付车费功能暂时无法使用',
    '505163'          => '当前二维码已失效',
    '505164'          => '领券失败',
    '505165'          => '当前用户不能使用该券',
);

return $config;
