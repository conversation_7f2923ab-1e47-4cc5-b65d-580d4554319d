<?php

use BizLib\Config as NuwaConfig;
use Disf\SPL\Scheduler\Svc;

$config['config_pay'] = array(
    'pay_req' => array(
        //>>> 'http://${ROUTER_GS_PAY_IP_PORT}/gulfstream/pay/v1',
        'http://*************:8000/gulfstream/pay/v1',
        //<<<
    ),

    'new_pay_req' => array(
        //>>> 'http://${ROUTER_GS_PAY_IP_PORT}/gulfstream/pay/v1/income/increase',
        'http://*************:8000/gulfstream/pay/v1/income/increase',
        //<<<
    ),

    'income_reduce' => array(
        //>>> 'http://${ROUTER_GS_PAY_IP_PORT}/gulfstream/pay/v1/income/reduce',
        'http://*************:8000/gulfstream/pay/v1/income/reduce',
        //<<<
    ),
    //支付系统那边的券的调用接口地址
    'coupon_url' => array(
        //>>> 'http://${ROUTER_COUPON_IP_PORT}/foundation/coupon/v1',
        'http://*************:8000/foundation/coupon/v1',
        //<<<
    ),
    //支付系统那边的券的调用接口地址(新)
    'new_coupon_url' => array(
        //>>> 'http://${ROUTER_NEW_COUPON_IP_PORT}/foundation/coupon/v1',
        'http://************:8000/foundation/coupon/v1',
        //<<<
    ),

    //反作弊接口
    'anticheat_url' => array(
            //>>> 'url' => 'http://${ROUTER_GS_ANTISPAM_IP_PORT}/anti/antispam_interface/payment_ver',
            'url' => 'http://*************:8000/anti/antispam_interface/payment_ver',
            //<<<
    ),

    'pay_params' => array(
        'appid'           => 'wx4d1a99cc3d09584b',
        'dache_appid'     => 'wx69b6673576ec5a65',
        'pay_key'         => 'didi',
        'pay_appkey'      => '1234567890!@#$%^&*',
        //>>> 'pay_notifyurl' => "http://${ROUTER_GS_API_IP_PORT}/gulfstream/api/v1/passenger/pAcceptPayNotify?encodeOid=%s",
        'pay_notifyurl' => 'http://*************:8000/gulfstream/api/v1/passenger/pAcceptPayNotify?encodeOid=%s',
        //<<<
        //'bind_card_url' => "http://pay.udache.com/gulfstream/pay/v1/wxpayinterface/wxBindCard?appid=%s&openid=%s",
        //>>> 'bind_card_url' => "http://${ROUTER_GS_PAY_IP_PORT}/gulfstream/pay/v1/wxpayinterface/wxBindCard?appid=%s&openid=%s",
        'bind_card_url' => 'http://**************:8000/gulfstream/pay/v1/wxpayinterface/wxBindCard?appid=%s&openid=%s',
        //<<<
        'voucher_url' => 'http://api.udache.com/gulfstream/api/v1/passenger/pCoupon?', //TODO
    ),

    'city_list' => array(
        '010' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'            => 0.12,
                'third_party_ratio'           => 0.03,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
        ),
        //上海
        '021' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'            => 0.12,
                'third_party_ratio'           => 0.03,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
        ),
        '020' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 50,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'      => 0.12,
                'third_party_ratio'     => 0.03,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
        ),

        //武汉
        '027' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'      => 0.12,
                'third_party_ratio'     => 0.03,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
        ),

        //深圳
        '0755' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'      => 0.12,
                'third_party_ratio'     => 0.03,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
        ),

        //杭州
        '0571' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'      => 0.12,
                'third_party_ratio'     => 0.03,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
        ),

        //南京
        '025' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'      => 0.12,
                'third_party_ratio'     => 0.03,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
        ),

        //济南
        '0531' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'      => 0.12,
                'third_party_ratio'     => 0.03,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
        ),

        //青岛
        '0532' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'      => 0.12,
                'third_party_ratio'     => 0.03,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
        ),

        //大连
        '0411' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'      => 0.12,
                'third_party_ratio'     => 0.03,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
        ),

        //厦门
        '0592' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'      => 0.12,
                'third_party_ratio'     => 0.03,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
        ),

        //沈阳
        '024' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'      => 0.12,
                'third_party_ratio'     => 0.03,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
        ),

        //天津
        '022' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'      => 0.12,
                'third_party_ratio'     => 0.03,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
        ),

        //长沙
        '0731' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'      => 0.12,
                'third_party_ratio'     => 0.03,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
        ),

        //成都
        '028' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'      => 0.12,
                'third_party_ratio'     => 0.03,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
        ),

        //合肥
        '0551' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'      => 0.12,
                'third_party_ratio'     => 0.03,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
        ),

        //宁波
        '0574' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'      => 0.12,
                'third_party_ratio'     => 0.03,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
        ),

        //佛山
        '0757' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'      => 0.12,
                'third_party_ratio'     => 0.03,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
        ),

        //重庆
        '023' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'      => 0.12,
                'third_party_ratio'     => 0.03,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
        ),
        //西安
        '029' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'      => 0.12,
                'third_party_ratio'     => 0.03,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
        ),
        '0351' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'            => 0.12,
                'third_party_ratio'           => 0.03,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
        ),
        '0871' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'            => 0.12,
                'third_party_ratio'           => 0.03,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
        ),
        '0771' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'            => 0.12,
                'third_party_ratio'           => 0.03,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
        ),
        '0371' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'            => 0.12,
                'third_party_ratio'           => 0.03,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
        ),
        '0379' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'            => 0.12,
                'third_party_ratio'           => 0.03,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
        ),
        '0951' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'            => 0.12,
                'third_party_ratio'           => 0.03,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
        ),
        '0591' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'            => 0.12,
                'third_party_ratio'           => 0.03,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
        ),
        '0769' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'            => 0.12,
                'third_party_ratio'           => 0.03,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
        ),
        '0760' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'            => 0.12,
                'third_party_ratio'           => 0.03,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
        ),
        '0451' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'            => 0.12,
                'third_party_ratio'           => 0.03,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
        ),
        '0431' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'            => 0.12,
                'third_party_ratio'           => 0.03,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
        ),
        '0311' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'            => 0.12,
                'third_party_ratio'           => 0.03,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
        ),
        '0510' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'            => 0.12,
                'third_party_ratio'           => 0.03,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
        ),
        '0519' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'            => 0.12,
                'third_party_ratio'           => 0.03,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
        ),
        '0533' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'            => 0.12,
                'third_party_ratio'           => 0.03,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'            => 0.1536,
                'third_party_ratio'           => 0.0464,
                'is_other_fee_separate'       => 1,
                'is_tip_fee_separate'         => 0,
                'stimulate_flag'              => 1, //是否启动激励措施
                'stimulate_didi_basic_ratio'  => 0,
                'stimulate_third_party_ratio' => 0,
                'stimulate_dividing_line_fee' => 100,
                'stimulate_down_line_ratio'   => 2,
                'stimulate_up_line_ratio'     => 1,
            ),
        ),

        '9999' => array(
            //普通加盟车
            '1' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //长包车
            '2' => array(
            ),
            //京B
            '3' => array(
                'didi_basic_ratio'      => 0.12,
                'third_party_ratio'     => 0.03,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
            //奥迪
            '4' => array(
                'didi_basic_ratio'      => 0.1536,
                'third_party_ratio'     => 0.0464,
                'is_other_fee_separate' => 1,
                'is_tip_fee_separate'   => 0,
            ),
        ),
    ),

    'webapp' => array(
        'appid'     => 'wx4d1a99cc3d09584b', //gs-online
        'appkey'    => '0ac4563de25bcef3af90ebaa43feb48e', //gs-online
        'signkey'   => 'ScJh0WiHuwI1udqwKW111maSoIXvZHwLhdApZ7QyOoNHmI6hVaWE9GvLQU0PYecQeLuYBXX36iszmteitOZCNya2P5TLEqXr4KcXbRLg9OcHIW9fuVLKs6BW4Yng23m4', //gs-online
        'oauth2_url'=> 'https://open.weixin.qq.com/connect/oauth2/authorize',
        //'access_token_url'=>"https://api.weixin.qq.com/sns/oauth2/access_token",
        //'access_token_url'=>"http://api.wx.com:12361/sns/oauth2/access_token",
        'access_token_url'=> 'http://************:12361/sns/oauth2/access_token',
        'redirect_url'    => 'http://api.udache.com/gulfstream/api/v1/webapp/weixinapi',
        'bindcard_url'    => 'http://**************:8000/gulfstream/pay/v1/wxpayinterface/wxBindCard',
    ),

    //长包车司机月底分账奖惩
    'mis_long_rent_forward' => array(
        '010' => array(
            //听单抢单率
            'strive_listen' => array(
                array('min' => 0.0, 'max' => 0.5, 'forward' => -200.00),
                array('min' => 0.51, 'max' => 1.0, 'forward' => 0.00),
            ),
            //迟到
            'late'  		=> array(
                array('min' => 0, 'max' => 0, 'forward' => 300.00),
                array('min' => 1, 'max' => 3, 'forward' => 100.00),
                array('min' => 4, 'max' => 10, 'forward' => 50.00),
                array('min' => 10, 'forward' => 0),
            ),
            //改派
            'dispatch'      => array(
                array('min' => 0, 'max' => 0, 'forward' => 200.00),
                array('min' => 1, 'max' => 3, 'forward' => 50.00),
                array('min' => 4, 'forward' => 0),
            ),
            //拒绝改派
            'reject_dispatch' => array(),
            //评价星级
            'evaluate'         => array(
                array('min' => 0.0, 'max' => 4.0, 'forward' => 0.00),
                array('min' => 4.1, 'max' => 4.1, 'forward' => 100.00),
                array('min' => 4.2, 'max' => 4.2, 'forward' => 200.00),
                array('min' => 4.3, 'max' => 4.3, 'forward' => 300.00),
                array('min' => 4.4, 'max' => 4.4, 'forward' => 400.00),
                array('min' => 4.5, 'max' => 4.5, 'forward' => 1000.00),
                array('min' => 4.6, 'max' => 4.6, 'forward' => 1200.00),
                array('min' => 4.7, 'max' => 4.7, 'forward' => 1400.00),
                array('min' => 4.8, 'max' => 5.0, 'forward' => 2000.00),
            ),
        ),
    ),

    'withdraw_pay' => array(
        'boss' => array(
            'bossid'   => '1219773001',
            'boss_type'=> '52',  //财付通提现=52
            'source'   => '82',     //湾流提现=82
        ),

        'partner' => array(
            'partner_id'  => '1226539201', //提现转账方财付通（万古）非北京城市，自营车待定
            'partner_type'=> '51',       //财付通转账=51
        ),

        'filter_channel' => array('1010000001'),
    ),

    //湾流后台操作提现
    'withdraw_admin_pay' => array(
        'boss' => array(
            'bossid'   => '1219773001',  //提现出款方财付通, 通达无限=1219773001
            'boss_type'=> '52',       //财付通提现=52
            'source'   => '81',          //湾流后台操作提现=81
        ),

        'partner' => array(
            'partner_id'  => '1226539201', //提现转账方财付通, 冠华英才=1221084401（加盟车，京B，奥迪），自营车待定
            'partner_type'=> '51',       //财付通转账=51
        ),

        'filter_channel' => array('1010000001'),  //提现过滤掉的渠道号
    ),
);

$tmpUrl = '';
\Disf\SPL\Scheduler\Svc::discoverHttpUrl(
    'disf!common-plat-gs-pay',
    '/gulfstream/pay/v1',
    \Disf\SPL\Scheduler\Svc::thenUpdate($config['config_pay']['pay_req'][0])
);
\Disf\SPL\Scheduler\Svc::discoverHttpUrl(
    'disf!common-plat-gs-pay',
    '/gulfstream/pay/v1/income/increase',
    \Disf\SPL\Scheduler\Svc::thenUpdate($config['config_pay']['new_pay_req'][0])
);
\Disf\SPL\Scheduler\Svc::discoverHttpUrl(
    'disf!common-plat-gs-pay',
    '/gulfstream/pay/v1/income/reduce',
    \Disf\SPL\Scheduler\Svc::thenUpdate($config['config_pay']['income_reduce'][0])
);
Svc::discoverHttpUrl(
    'disf!common-plat-public-ms-coupon',
    '/foundation/coupon/v1',
    Svc::thenUpdate($config['config_pay']['coupon_url'][0])
);
//Svc::discoverHttpUrl('disf!common-plat-public-ms-coupon', '/foundation/coupon/v1',
//    Svc::thenUpdate($config['config_pay']['new_coupon_url']));
Svc::discoverHttpUrl(
    'disf!biz-gs-biz_passenger',
    '/gulfstream/api/v1/passenger/pAcceptPayNotify?encodeOid=%s',
    Svc::thenUpdate($config['config_pay']['pay_params']['pay_notifyurl'])
);
\Disf\SPL\Scheduler\Svc::discoverHttpUrl(
    'disf!common-plat-gs-pay',
    '/gulfstream/pay/v1/wxpayinterface/wxBindCard?appid=%s&openid=%s',
    \Disf\SPL\Scheduler\Svc::thenUpdate($config['config_pay']['pay_params']['bind_card_url'])
);
\Disf\SPL\Scheduler\Svc::discoverHttpUrl(
    'disf!common-plat-gs-pay',
    '/gulfstream/pay/v1/wxpayinterface/wxBindCard',
    \Disf\SPL\Scheduler\Svc::thenUpdate($config['config_pay']['webapp']['bindcard_url'])
);

//微信代扣配置

$config['new_weixin_agent'] =NuwaConfig::text('config_pay', 'new_weixin_agent');

//>>> $config['show_guide_url'] = 'http://${ROUTER_WEB_WALLET_IP_PORT}/web_wallet/passenger/canShowGuide';
$config['show_guide_url'] = 'http://*************:8000/web_wallet/passenger/canShowGuide';
//<<<
Svc::discoverHttpUrl(
    'disf!common_plat-public-phoenix-cgi',
    '/web_wallet/passenger/canShowGuide',
    Svc::thenUpdate($config['show_guide_url'])
);

return $config;

//<<<
