<?php

use BizLib\Config as NuwaConfig;
use Disf\SPL\Scheduler\Svc;

$config['open']            = false; //开关
$config['white_list_open'] = false; //白名单开关
$config['product_id']      = array(
    3,
);
$config['city_list']    = array('010');   //开通城市
$config['disable_city'] = array();  //不参与活动城市

//AB test规则，手机号%2取模，余数是1参与活动
//次数控制配置
$config['day_num']  = 1;
$config['week_num'] = 2;

//时段控制配置
$config['permit_time'] = array(
        array(
            'start_time' => '07:00:00',
            'end_time'   => '24:00:00',
        ),
    );

//获取券时间误差(单位秒)配置
$config['tolerance_time'] = 10;

//预估及H5规则配置
$config['estimate_msg']      = NuwaConfig::text('config_fastensure', 'estimate_msg');
$config['estimate_h5_title'] = NuwaConfig::text('config_fastensure', 'estimate_h5_title');
$config['estimate_h5_msg']   = NuwaConfig::text('config_fastensure', 'estimate_h5_msg');
$config['estimate_color']    = '#fa8919';
$config['fastensure_url']    = 'http://static.udache.com/gulfstream/webapp/pages/payForSlow/index.html';    //快车险说明url

//发单下发配置
$config['countdown_time']  = '88';
$config['countdown_msg']   = '未接驾，您将获得「慢必赔」补偿券';
$config['ext_msg']         = '什么是「慢必赔」';

//取消弹窗配置
$config['cancel_pop'] = array(
    'title'         => '再等%s秒',
    'subject'       => '司机未接单即可获得「慢必赔」补偿券',
    'act_img'       => 'http://static.udache.com/bg_img.jpg',
    'desc_list'     => '「慢必赔」补偿券可抵扣车费|每日限补偿一次，每周限两次',
    'cancel_button' => '不等了',
    'keep_button'   => '再等一会儿',
);
//补偿提示配置
$config['coupon_info'] = array(
    'title'         => '慢必赔券',
    'msg'           => '滴滴送您%s「慢必赔」补偿券，可直接抵扣车费',
    'act_img'       => 'http://static.udache.com/bg_img.jpg',
    'cancel_button' => '不分享',
    'cancel_info'   => array(
        'amount' => 0,
        'msg'    => '您已获得%s「慢必赔」，可用来抵扣车费',
    ),
    'share_button' => '分享出去',
    'share_url'    => 'http://www.didichuxing.com',
);
//智能派券系统链接
//>>> $config['dispatch_coupon_url'] = 'http://${ROUTER_COMMONSHARE_IP_PORT}/foundation/commonshare/v1/noopsycheinterface/getCoupon';
$config['dispatch_coupon_url'] = 'http://************:8000/foundation/commonshare/v1/noopsycheinterface/getCoupon';
//<<<
Svc::discoverHttpUrl(
    'disf!os-volcano-share_web',
    '/foundation/commonshare/v1/noopsycheinterface/getCoupon',
    Svc::thenUpdate($config['dispatch_coupon_url'])
);

//固定金额券批次配置
$config['coupon_batch'] = array(
    'activityid'   => '101945',
    'batchid'      => '1009641',
    'coupon_money' => 2,
);

return $config;

//分享链接待定
