<?php
 use BizLib\Config as NuwaConfig;
use Disf\SPL\Scheduler\Svc;

$config['gulfstreamShare'] = array(
            '0'     => array(
            'weixinSharePic'        => 'http://static.udache.com/gulfstream/api/passenger/share.jpg',
            'weixinShareIcon'       => 'http://static.udache.com/gulfstream/api/passenger/logo.png',
            'weixinShareUrl'        => 'http://static.udache.com/gulfstream/webapp/pages/ordershare/order-share.html?sig=%s&oid=%s', //点击图片跳转静态页url
            'weixinShareTitle'      => NuwaConfig::text('config_share', 'weixinShareTitle'), //分享标题
            'weixinShareContent'    => NuwaConfig::text('config_share', 'weixinShareContent'), //分享内容
            'weiboShareTitle'       => NuwaConfig::text('config_share', 'weiboShareTitle'), //微博分享标题里面可能含有跳转静态页url
            'weiboSharePic'         => 'http://static.udache.com/gulfstream/api/passenger/share.jpg',
            'shareType'             => '1', //此参数值安卓使用IOS没有使用以后废弃掉
            'shareTypeNew'          => '3', //1.大图；3.小图加链接
            'qqSharePic'            => 'http://static.udache.com/gulfstream/api/passenger/share.jpg',
            'qqShareIcon'           => 'http://static.udache.com/gulfstream/api/passenger/logo.png',
            'qqShareUrl'            => 'http://static.udache.com/gulfstream/webapp/pages/ordershare/order-share.html?sig=%s&oid=%s', //点击图片跳转静态页url
            'qqShareTitle'          => NuwaConfig::text('config_share', 'qqShareTitle'), //分享标题
            'qqShareContent'        => NuwaConfig::text('config_share', 'qqShareContent'), //分享内容
            ),
);

//分享券
$config['coupon'] = array(
    'district'               => array('010', '020', '027', '0755', '0411', '0532', '0531', '025', '0571', '0592', '021', '022', '024', '0731', '028', '0551', '0574', '0757', '023', '0351', '0871', '0771', '0371', '0379', '0951', '0591', '0769', '0760', '0451', '0431', '0311', '0510', '0519', '0533', '0631', '0315', '0752', '0756', '0898', '0851', '0535', '0536', '0512', '0717', '0791', '0595', '0758', '0412', '0452', '0516', '0539', '0514', '0546', '0733', '0816', '0716'), //开通区域码
    'activity_id'            => 100158,
    'batch_id'               => 1000471,
    'share_title'            => NuwaConfig::text('config_share', 'coupon_share_title'),
    'share_text'             => NuwaConfig::text('config_share', 'coupon_share_text'),
    'share_button_send'      => NuwaConfig::text('config_share', 'coupon_share_button_send'),
    'share_button_later'     => NuwaConfig::text('config_share', 'share_button_later'),
    'pop_layer_time'         => 1,
    'layer_image_url'        => 'http://static.udache.com/gulfstream/api/passenger/share_coupon/bg_pic.png',

    //分享到朋友圈
    'wx_share_title'         => NuwaConfig::text('config_share', 'coupon_wx_share_title'),
    'wx_share_text'          => NuwaConfig::text('config_share', 'coupon_wx_share_text'),
    'wx_share_logo'          => 'http://static.udache.com/gulfstream/webapp/modules/voucher/logo_150430.png',

    //分享给微信好友
    'wx_friend_title'        => NuwaConfig::text('config_share', 'coupon_wx_friend_title'),
    'wx_friend_text'         => NuwaConfig::text('config_share', 'coupon_wx_friend_text'),
    'wx_friend_logo'         => 'http://static.udache.com/gulfstream/webapp/modules/voucher/logo_150430.png',

    'display_coupon_logo'    => 1,

    'share_secret_key'       => 'x1i2a3o4j5u12345!@#$%',

    //接口url
    'create_url'           => 'http://************:8000/veyron/market_entry/hbEntry/createHbZc',  //创建分享url
    'create_url_new'       => 'http://************:8000/veyron/market_entry/hbEntry/createHbZcEx',  //创建分享url
    'create_url_enterprice'=> 'http://*************:8000/veyron/market_entry/hbEntry/createHbEnterprice',
    'get_url'              => 'http://************:8000/veyron/market_entry/hbEntry/getShareZc',     //获取分享url
    //营销区域
    'activity_district'      => array('0379', '020', '0592', '0411', '0551', '0531', '0591', '0769', '0574', '0351', '0451', '0519', '0871', '0756', '0371'),
    'create_hb_api_services' => array('ip' => '************', 'port' => '8000'),
);
Svc::discoverHttpUrl(
    'disf!os-volcano-market-market_entry',
    '/veyron/market_entry/hbEntry/createHbZc',
    Svc::thenUpdate($config['coupon']['create_url'])
);
Svc::discoverHttpUrl(
    'disf!os-volcano-market-market_entry',
    '/veyron/market_entry/hbEntry/createHbZcEx',
    Svc::thenUpdate($config['coupon']['create_url_new'])
);
Svc::discoverHttpUrl(
    'disf!os-volcano-market-market_entry',
    '/veyron/market_entry/hbEntry/createHbEnterprice',
    Svc::thenUpdate($config['coupon']['create_url_enterprice'])
);
Svc::discoverHttpUrl(
    'disf!os-volcano-market-market_entry',
    '/veyron/market_entry/hbEntry/getShareZc',
    Svc::thenUpdate($config['coupon']['get_url'])
);
Svc::discoverEndpoint('disf!os-volcano-market-market_entry', Svc::thenUpdate($config['coupon']['create_hb_api_services']));

return $config;

//分享粉红包

//捐赠1元钱(桔丝带活动)
