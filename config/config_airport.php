<?php
/**
 * Created by PhpStorm.
 * User: z<PERSON><PERSON><PERSON><PERSON>&gaoerpeng
 * Date: 2015/9/1 11:57
 * File: config_airport_cities.php.
 */

//送机险保险状态码
define('INSURE_STATUS_DEFAULT', 0); //初始值，滴滴未验证是否可投保
define('INSURE_STATUS_INSU_DIDI_CHECK_SUCCESS', '1'); //滴滴验证可投保
define('INSURE_STATUS_INSU_DIDI_CHECK_FAIL', '-1');    //滴滴验证不可投保
define('INSURE_STATUS_INSU_FINISH_CERT', '2'); //完成身份信息
define('INSURE_STATUS_INSU_NOT_FINISH_CERT', '-2'); //完成身份信息失败
define('INSURE_STATUS_INSU_SUCCESS', '3'); //投保成功
define('INSURE_STATUS_INSU_FAIL', '-3'); //投保失败
define('INSURE_STATUS_CLAIMS_DIDI_CHECK_SUCCESS', '4'); //滴滴验证可理赔
define('INSURE_STATUS_CLAIMS_DIDI_CHECK_FAIL', '-4'); //滴滴验证不可理赔
define('INSURE_STATUS_CLAIMS_REPORT_SUCCESS', '5'); //理赔报案成功
define('INSURE_STATUS_CLAIMS_REPORT_FAIL', '-5'); //理赔报案失败
define('INSURE_STATUS_CLAIMS_UPLOAD_SUCCESS', '6'); //材料上传成功
define('INSURE_STATUS_CLAIMS_UPLOAD_FAIL', '-6'); //材料上传失败
define('INSURE_STATUS_CLAIMS_FINISH_SUCCESS', '7'); //理赔成功
define('INSURE_STATUS_CLAIMS_FINISH_FAIL', '-7'); //理赔失败

//第三方回调状态
define('PINGAN_CALLBACK_STATUS_DEFAULT', 0); //已报案 未获取状态
define('PINGAN_CALLBACK_STATUS_REPORT_END', 1); //已报案（返回报案号）
define('PINGAN_CALLBACK_STATUS_PROCESSING', 2); //案件审核中
define('PINGAN_CALLBACK_STATUS_WAIT_PAY', 3); //己结案待付款(显示金额)
define('PINGAN_CALLBACK_STATUS_PAY_END', 4); //已付款
define('PINGAN_CALLBACK_STATUS_WAIT_UPLOAD', 5); //材料待补充
define('PINGAN_CALLBACK_STATUS_PAY_FAIL', 6); //支付失败
define('PINGAN_CALLBACK_STATUS_REFUSE_PAY', 7); //拒赔

//航空公司类型
define('FLIGHT_COMPANY_TYPE_DOME_CHUNQIU', 1);   //春秋
define('FLIGHT_COMPANY_TYPE_INTER', 2); //国际
define('FLIGHT_COMPANY_TYPE_DOME_NOT_CHUNQIU', 3); //国内非春秋

//国际国内航班  飞常准航班类型：0:国内-国内;1国内-国际;2国内-地区;3:地区-国际;4:国际-国际;5:未知
define('FLIGHT_TYPE_DOMESTIC_TO_DOMESTIC', 0);
define('FLIGHT_TYPE_DOMESTIC_TO_INTER', 1);
define('FLIGHT_TYPE_DOMESTIC_TO_AREA', 2);
define('FLIGHT_TYPE_AREA_TO_INTER', 3);
define('FLIGHT_TYPE_INTER_TO_INTER', 4);
define('FLIGHT_TYPE_UNKNOWN', 5);

//开通cip服务的城市
//开通误机险的城市
//误机险相关配置
//热门城市区号
//提供接送机服务的城市，空则表示所有专车城市
//航班信息提供商
$config['flight_status'] = array(
    'waiting'  => 0, //航班未起飞
    'flying'   => 1, //航班飞行中
    'arrived'  => 2, //航班已到达
    'abnormal' => -1, //航班异常：取消，备降，返航
);

//接机改派相关配置
//车型对应最大乘客数目
//各个车型对应的图标
//cip码请求地址
//上车点开关
// 送机单导接机单表达配置
$config['to_airport_guidance'] = array('icon_url' => 'http://pt-starimg.didistatic.com/static/starimg/img/1512446440769FdxR9lBQw5UlUMUSXQx.png',);

//附加服务的icon
$config['attach_service_icon'] = array(
    'http://img-ys011.didistatic.com/static/baoche_banner_photo/do1_3rXxMhthsmF1NMkxeDG7',
    'http://img-ys011.didistatic.com/static/baoche_banner_photo/do1_HbXdPTUi67xn8C4zx2zD',
);


return $config;
