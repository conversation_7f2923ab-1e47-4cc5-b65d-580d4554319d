<?php

use Disf\SPL\Scheduler\Svc;

/*
 * 系统常量及参数定义
 */
/*
 * 队列执行方式常量定义
 */
define('QUEUE_TYPE_HTTP_POST', 'http_post');
define('QUEUE_TYPE_HTTP_GET', 'http_get');
define('QUEUE_TYPE_CLASS', 'class');
/*
 * 队列定义
 */
define('QUEUE_DEFAULT', 'default');
define('QUEUE_POSITION', 'position');
define('QUEUE_LOGS', 'logs');
define('QUEUE_PUSH', 'push');
define('QUEUE_SMS', 'sms');
define('QUEUE_ORDER', 'order');
define('QUEUE_PAY', 'pay');
define('QUEUE_DAYUER', 'dayuer');
/*---------2015-04-20 号 队列服务迁移配置--------*/
define('QUEUE_TYPE_DEFAULT', 1);
//普通队列
define('QUEUE_TYPE_TIMING', 2);
//定时队列
//>>> $queueRequestUrl = 'http://${ROUTER_GS_BRIDGE_QUEUE_IP_PORT}/gulfstream/bridgequeue/v1/'; //请求queue队列URL
$queueRequestUrl = 'http://**************:8000/gulfstream/bridgequeue/v1/';
//请求queue队列URL
//<<<
Svc::discoverHttpUrl(
    'disf!fd-bridgeq-bridgeq_web',
    '/gulfstream/bridgequeue/v1/',
    Svc::thenUpdate($queueRequestUrl)
);
define('QUEUE_REQUEST_URL', $queueRequestUrl);
define('QUEUE_MODE_REQUEST_GET', 1);
//GET请求
define('QUEUE_MODE_REQUEST_POST', 2);
//POST请求
define('QUEUE_REQUEST_NUM', 1);
//请求次数
define('QUEUE_REQUEST_TIMEOUT', 2000);
//回调请求超时时间
define('QUEUE_REQUEST_EXPIRE', 0);
//过期时间
define('QUEUE_REQUEST_TIMESTAMP', 5);
//延迟5S回调
/*普通队列定义*/
define('QUEUE_API_ORDER_DRIVER', 'queue_api_order_driver');
define('QUEUE_API_ORDER_PASSENGER', 'queue_api_order_passenger');
define('QUEUE_API_DEFAULT', 'queue_api_default');
define('QUEUE_API_B2B', 'queue_api_b2b');
define('QUEUE_API_WEBAPP', 'queue_api_webapp');
define('QUEUE_API_OPENAPI', 'queue_api_openapi');
define('QUEUE_API_NEW_ORDER', 'queue_api_new_order');
define('QUEUE_API_SPAM_REPORT', 'queue_api_spam_report');
//反作弊数据上报
define('QUEUE_API_DRIVER_ORDER', 'queue_api_driver_order');
define('QUEUE_API_ORDER_BILL', 'queue_api_order_bill');
define('QUEUE_API_CARPOOL_COUPON', 'queue_api_carpool_coupon');
//拼成大促
define('QUEUE_API_RECOVERY', 'queue_api_recovery');
define('QUEUE_CHUANLIU_ORDERCANCEL', 'queue_chuanliu_ordercancel');
/*定时队列定义*/
define('SCHEDULE_API_ORDER_DRIVER', 'schedule_api_order_driver');
define('SCHEDULE_API_ORDER_PASSENGER', 'schedule_api_order_passenger');
define('SCHEDULE_API_DEFAULT', 'schedule_api_default');
define('SCHEDULE_API_B2B', 'schedule_api_b2b');
define('SCHEDULE_API_WEBAPP', 'schedule_api_webapp');
define('SCHEDULE_API_OPENAPI', 'schedule_api_openapi');
define('SCHEDULE_API_DRIVER_RELIEVED', 'schedule_api_driver_relieved');
define('SCHEDULE_GS_API_RESEND_ORDER', 'schedule_gs_api_resend_order');
define('SCHEDULE_GS_API_CARPOOL_COUPON', 'schedule_gs_api_carpool_coupon');
define('SCHEDULE_GS_API_STRIVEORDER_CHECK', 'schedule_gs_api_striveorder_check');
/*
 * 实时通知通道常量定义
 */
define('CHANNEL_ORDER_NOTIFY', 'ORDER_NOTIFY');
define('CHANNEL_ORDER_STATUS_NOTIFY', 'ORDER_');
define('CHANNEL_DRIVER_POSITION', 'DRIVER_POSITION_');
define('CHANNEL_DRIVER_STATUS', 'DRIVER_STATUS_');
/*
 * node.js server定义
 */
define('SOCKET_IO_SERVER', 'http://10.10.10.106:49169');
/*
 * theone Avenger Push Topic
 */
define('PUSH_TOPIC_ONE_REALTIME_PRICE', 'One_Realtime_Price');
define('PUSH_TOPIC_ONE_DRIVER_ARRIVED', 'One_DriverArrived');
define('PUSH_TOPIC_ONE_STATUS_CHANGE', 'One_StatusChange');
define('ONE_PARTNER_STATUS_CHANGE', 'One_PartnerStatusChange');
define('ONE_CARPOOL_MESSAGE', 'One_CarpoolMessage');
/*
 * theone 拼车状态
*/
define('ONE_PARTNER_ADD', 10);
define('ONE_PARTNER_CANCEL', 20);
define('ONE_PARTNER_GET_ON', 30);
define('ONE_PARTNER_GET_OFF', 40);
/*
 * theone 专车订单状态
*/
define('ONE_STATUS_CREATE', 100);
define('ONE_STATUS_STRIVE', 200);
define('ONE_STATUS_ON', 300);
define('ONE_STATUS_OFF', 400);
define('ONE_STATUS_PAY', 500);
define('ONE_STATUS_CANCEL', 600);
/*
 * theone 业务线sid
*/
define('ONE_SID_ZHUANGCHE', 'premium');
define('ONE_SID_KUAICHE', 'flash');
$config['config_params'] = array();

return $config;
