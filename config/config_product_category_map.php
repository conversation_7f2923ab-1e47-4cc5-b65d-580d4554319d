<?php
if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

/* 为统一维护，添加前先更新此wiki: http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=233427648 */

define('PRODUCT_CATEGORY_DEFAULT', -1);                         //默认id
define('PRODUCT_CATEGORY_FAST', 1);                             //普通快车
define('PRODUCT_CATEGORY_CARPOOL_NORMAL', 2);                   //普通拼车
define('PRODUCT_CATEGORY_CARPOOL_STATION', 3);                  //站点拼车
define('PRODUCT_CATEGORY_CARPOOL_SPECIAL', 4);                  //特惠拼车
define('PRODUCT_CATEGORY_CARPOOL_INTE', 5);                     //跨城拼车
define('PRODUCT_CATEGORY_YOUXIANG', 6);                         //优享
define('PRODUCT_CATEGORY_UNIONE', 7);                           //unione出租车
define('PRODUCT_CATEGORY_PREMIUM_COMFORT', 8);                  //舒适型专车
define('PRODUCT_CATEGORY_PREMIUM_BUSINESS', 9);                 //商务型专车
define('PRODUCT_CATEGORY_PREMIUM_COMFORT_PICKUP', 10);          //舒适型专车接机
define('PRODUCT_CATEGORY_PREMIUM_BUSINESS_PICKUP', 11);         //商务型专车接机
define('PRODUCT_CATEGORY_PREMIUM_COMFORT_DROPOFF', 12);         //舒适型专车送机
define('PRODUCT_CATEGORY_PREMIUM_BUSINESS_DROPOFF', 13);        //商务型专车送机
define('PRODUCT_CATEGORY_LUXURY_PREMIUM_PICKUP', 14);           //豪华车接机（专车顶导）
define('PRODUCT_CATEGORY_LUXURY_PREMIUM_DROPOFF', 15);          //豪华车送机（专车顶导）
define('PRODUCT_CATEGORY_LUXURY_PREMIUM', 16);                  //豪华车（专车顶导）
define('PRODUCT_CATEGORY_LUXURY_ANY', 17);                      //任意豪华车
define('PRODUCT_CATEGORY_LUXURY_BENZ_E', 18);                   //奔驰E级
define('PRODUCT_CATEGORY_LUXURY_BMW_5', 19);                    //宝马5系
define('PRODUCT_CATEGORY_LUXURY_AUDI_A6L', 20);                 //奥迪A6L
define('PRODUCT_CATEGORY_LUXURY_ANY_PICKUP', 21);               //任意豪华车接机
define('PRODUCT_CATEGORY_LUXURY_BENZ_E_PICKUP', 22);            //奔驰E级接机
define('PRODUCT_CATEGORY_LUXURY_BMW_5_PICKUP', 23);             //宝马5系接机
define('PRODUCT_CATEGORY_LUXURY_AUDI_A6L_PICKUP', 24);          //奥迪A6L接机
define('PRODUCT_CATEGORY_LUXURY_ANY_DROPOFF', 25);              //任意豪华车送机
define('PRODUCT_CATEGORY_LUXURY_BENZ_E_DROPOFF', 26);           //奔驰E级送机
define('PRODUCT_CATEGORY_LUXURY_BMW_5_DROPOFF', 27);            //宝马5系送机
define('PRODUCT_CATEGORY_LUXURY_AUDI_A6L_DROPOFF', 28);         //奥迪A6L送机
define('PRODUCT_CATEGORY_RENT_SMALLFAMILY', 29);                //包车紧凑型
define('PRODUCT_CATEGORY_RENT_ROOMY', 30);                      //包车宽敞型
define('PRODUCT_CATEGORY_CARPOOL_FLAT_RATE', 31);               //拼车区域渗透
define('PRODUCT_CATEGORY_CARPOOL_PRICE_BY_SEAT', 32);           //拼车按座计价
define('PRODUCT_CATEGORY_PREMIUM_EXECUTIVE', 33);               //行政级专车
define('PRODUCT_CATEGORY_PREMIUM_EXECUTIVE_PICKUP', 34);        //行政级专车接机
define('PRODUCT_CATEGORY_PREMIUM_EXECUTIVE_DROPOFF', 35);       //行政级专车送机
define('PRODUCT_CATEGORY_PREMIUM_RECREATION', 36);              //休闲型专车
define('PRODUCT_CATEGORY_PREMIUM_RECREATION_PICKUP', 37);       //休闲型专车接机
define('PRODUCT_CATEGORY_PREMIUM_RECREATION_DROPOFF', 38);      //休闲型专车送机
define('PRODUCT_CATEGORY_UNIONE_SPECIAL_PRICE', 39);            //超值出租车
define('PRODUCT_CATEGORY_ANYCAR', 40);                          //同时呼叫anycar
define('PRODUCT_CATEGORY_TONGCHENG', 41);                       //开放运力平台同程
define('PRODUCT_CATEGORY_YIQI', 42);                            //开放运力平台一汽
define('PRODUCT_CATEGORY_GUANGQI', 43);                         //开放运力平台广汽
define('PRODUCT_CATEGORY_YOUXUAN_TAXI', 44);                    //优选出租车
define('PRODUCT_CATEGORY_DONGFENG', 45);                        //开放运力平台东风
define('PRODUCT_CATEGORY_YANGGUANG', 47);                       //开放运力平台阳光
define('PRODUCT_CATEGORY_SPECIAL_RATE', 61);                    //特惠快车
define('PRODUCT_CATEGORY_APLUS', 68);                           //A+订单
define('PRODUCT_CATEGORY_TAXI_MARKETISATION_PUTONG', 188);      //出租车一车两价（快的新出租）
define('PRODUCT_CATEGORY_TAXI_MARKETISATION_YOUXUAN', 189);     //出租车一车两价（优选出租车）
$config['map_key'] = array('business_id', 'require_level', 'combo_type', 'carpool_type', 'route_type', 'is_special_price','level_type');

$config['product_category'] = array(
    PRODUCT_CATEGORY_FAST                       => array(
        'business_id'   => 260,
        'require_level' => 600,
        'combo_type'    => 0,
    ),
    PRODUCT_CATEGORY_CARPOOL_NORMAL             => array(
        'business_id'   => 260,
        'require_level' => 600,
        'combo_type'    => 4,
        'carpool_type'  => 1,
    ),
    PRODUCT_CATEGORY_CARPOOL_STATION            => array(
        'business_id'   => 260,
        'require_level' => 600,
        'combo_type'    => 4,
        'carpool_type'  => 2,
    ),
    PRODUCT_CATEGORY_CARPOOL_SPECIAL            => array(
        'business_id'   => 260,
        'require_level' => 610,
        'combo_type'    => 4,
        'carpool_type'  => 2,
    ),
    PRODUCT_CATEGORY_CARPOOL_INTE               => array(
        'business_id'   => 260,
        'require_level' => 600,
        'combo_type'    => 302,
        'carpool_type'  => 3,
    ),
    PRODUCT_CATEGORY_YOUXIANG                   => array(
        'business_id'   => 260,
        'require_level' => 900,
        'combo_type'    => 0,
    ),
    PRODUCT_CATEGORY_PREMIUM_COMFORT            => array(
        'business_id'   => 258,
        'require_level' => 100,
        'combo_type'    => 0,
    ),
    PRODUCT_CATEGORY_PREMIUM_BUSINESS           => array(
        'business_id'   => 258,
        'require_level' => 400,
        'combo_type'    => 0,
    ),
    PRODUCT_CATEGORY_PREMIUM_COMFORT_PICKUP     => array(
        'business_id'   => 258,
        'require_level' => 100,
        'combo_type'    => 2,
    ),
    PRODUCT_CATEGORY_PREMIUM_BUSINESS_PICKUP    => array(
        'business_id'   => 258,
        'require_level' => 400,
        'combo_type'    => 2,
    ),
    PRODUCT_CATEGORY_PREMIUM_COMFORT_DROPOFF    => array(
        'business_id'   => 258,
        'require_level' => 100,
        'combo_type'    => 3,
    ),
    PRODUCT_CATEGORY_PREMIUM_BUSINESS_DROPOFF   => array(
        'business_id'   => 258,
        'require_level' => 400,
        'combo_type'    => 3,
    ),
    PRODUCT_CATEGORY_LUXURY_PREMIUM_PICKUP      => array(
        'business_id'   => 276,
        'require_level' => 1402,
        'combo_type'    => 2,
    ),
    PRODUCT_CATEGORY_LUXURY_PREMIUM_DROPOFF     => array(
        'business_id'   => 276,
        'require_level' => 1402,
        'combo_type'    => 3,
    ),
    PRODUCT_CATEGORY_LUXURY_PREMIUM             => array(
        'business_id'   => 276,
        'require_level' => 1402,
        'combo_type'    => 0,
    ),
    PRODUCT_CATEGORY_LUXURY_ANY                 => array(
        'business_id'   => 276,
        'require_level' => 1000,
        'combo_type'    => 0,
    ),
    PRODUCT_CATEGORY_LUXURY_BENZ_E              => array(
        'business_id'   => 276,
        'require_level' => 1500,
        'combo_type'    => 0,
    ),
    PRODUCT_CATEGORY_LUXURY_BMW_5               => array(
        'business_id'   => 276,
        'require_level' => 1400,
        'combo_type'    => 0,
    ),
    PRODUCT_CATEGORY_LUXURY_AUDI_A6L            => array(
        'business_id'   => 276,
        'require_level' => 1401,
        'combo_type'    => 0,
    ),
    PRODUCT_CATEGORY_LUXURY_ANY_PICKUP          => array(
        'business_id'   => 276,
        'require_level' => 1000,
        'combo_type'    => 2,
    ),
    PRODUCT_CATEGORY_LUXURY_BENZ_E_PICKUP       => array(
        'business_id'   => 276,
        'require_level' => 1500,
        'combo_type'    => 2,
    ),
    PRODUCT_CATEGORY_LUXURY_BMW_5_PICKUP        => array(
        'business_id'   => 276,
        'require_level' => 1400,
        'combo_type'    => 2,
    ),
    PRODUCT_CATEGORY_LUXURY_AUDI_A6L_PICKUP     => array(
        'business_id'   => 276,
        'require_level' => 1401,
        'combo_type'    => 2,
    ),
    PRODUCT_CATEGORY_LUXURY_ANY_DROPOFF         => array(
        'business_id'   => 276,
        'require_level' => 1000,
        'combo_type'    => 3,
    ),
    PRODUCT_CATEGORY_LUXURY_BENZ_E_DROPOFF      => array(
        'business_id'   => 276,
        'require_level' => 1500,
        'combo_type'    => 3,
    ),
    PRODUCT_CATEGORY_LUXURY_BMW_5_DROPOFF       => array(
        'business_id'   => 276,
        'require_level' => 1400,
        'combo_type'    => 3,
    ),
    PRODUCT_CATEGORY_LUXURY_AUDI_A6L_DROPOFF    => array(
        'business_id'   => 276,
        'require_level' => 1401,
        'combo_type'    => 3,
    ),
    PRODUCT_CATEGORY_RENT_SMALLFAMILY           => array(
        'business_id'   => 389,
        'require_level' => 3400,
        'combo_type'    => 302,
    ),
    PRODUCT_CATEGORY_RENT_ROOMY                 => array(
        'business_id'   => 389,
        'require_level' => 3500,
        'combo_type'    => 302,
    ),
    PRODUCT_CATEGORY_CARPOOL_FLAT_RATE          => array(
        'business_id'   => 260,
        'require_level' => 600,
        'combo_type'    => 4,
        'carpool_type'  => 2,
        'route_type'    => 1,
    ),
    PRODUCT_CATEGORY_CARPOOL_PRICE_BY_SEAT      => array(
        'business_id'   => 260,
        'require_level' => 600,
        'combo_type'    => 4,
        'carpool_type'  => 2,
        'route_type'    => 2,
    ),
    PRODUCT_CATEGORY_PREMIUM_EXECUTIVE          => array(
        'business_id'   => 258,
        'require_level' => 200,
        'combo_type'    => 0,
    ),
    PRODUCT_CATEGORY_PREMIUM_EXECUTIVE_PICKUP   => array(
        'business_id'   => 258,
        'require_level' => 200,
        'combo_type'    => 2,
    ),
    PRODUCT_CATEGORY_PREMIUM_EXECUTIVE_DROPOFF  => array(
        'business_id'   => 258,
        'require_level' => 200,
        'combo_type'    => 3,
    ),
    PRODUCT_CATEGORY_PREMIUM_RECREATION         => array(
        'business_id'   => 258,
        'require_level' => 3300,
        'combo_type'    => 0,
    ),
    PRODUCT_CATEGORY_PREMIUM_RECREATION_PICKUP  => array(
        'business_id'   => 258,
        'require_level' => 3300,
        'combo_type'    => 2,
    ),
    PRODUCT_CATEGORY_PREMIUM_RECREATION_DROPOFF => array(
        'business_id'   => 258,
        'require_level' => 3300,
        'combo_type'    => 3,
    ),
    PRODUCT_CATEGORY_ANYCAR                     => array(
        'business_id'   => 372,
        'require_level' => 2300,
        'combo_type'    => 0,
    ),
    PRODUCT_CATEGORY_UNIONE_SPECIAL_PRICE       => array(
        'business_id'      => 307,
        'require_level'    => 1100,
        'combo_type'       => 0,
        'is_special_price' => 1,
    ),
    PRODUCT_CATEGORY_UNIONE                     => array(
        'business_id'   => 307,
        'require_level' => 1100,
        'combo_type'    => 0,
    ),
    PRODUCT_CATEGORY_TONGCHENG                  => array(
        'business_id'   => 410,
        'require_level' => 4500,
        'combo_type'    => 0,
    ),
    PRODUCT_CATEGORY_YIQI                       => array(
        'business_id'   => 412,
        'require_level' => 4520,
        'combo_type'    => 0,
    ),
    PRODUCT_CATEGORY_GUANGQI                    => array(
        'business_id'   => 414,
        'require_level' => 4540,
        'combo_type'    => 0,
    ),
    PRODUCT_CATEGORY_YOUXUAN_TAXI               => array(
        'business_id'   => 307,
        'require_level' => 2000,
        'combo_type'    => 0,
    ),
    PRODUCT_CATEGORY_DONGFENG                   => array(
        'business_id'   => 413,
        'require_level' => 4530,
        'combo_type'    => 0,
    ),
    PRODUCT_CATEGORY_YANGGUANG                  => array(
        'business_id'   => 415,
        'require_level' => 4550,
        'combo_type'    => 0,
    ),
    PRODUCT_CATEGORY_APLUS                      => array(
        'business_id'   => 260,
        'require_level' => 600,
        'combo_type'    => 0,
        'level_type'    => 1,
    ),
);

return $config;
