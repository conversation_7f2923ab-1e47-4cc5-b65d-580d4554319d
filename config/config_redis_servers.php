<?php

use Disf\SPL\Scheduler\Svc;

/*
 * redis服务器配置
 */
$config['config_redis_servers'] = array(
    'common'  => array('use_local_cache' => true),

    'loss'    => array(
        'keys'    => array(
            'IS_REGION_CITY_', //是否新政城市
            'P_NEW_LASTEST_TIME_',
            'P_LASTEST_TIME_',
            'P_INFO_BY_TOKEN',
            'P_INFO_BY_TOKEN_ORDER',
            'P_INFO_BY_PHONE',
            'P_INFO_CACHE_BY_TOKEN',
            'P_PASSENGER_BASIC_INFO',
            'P_FEE_DISSENT_BASIC_INFO',
            'P_ADDRESS_BASIC_INFO',
            'P_ADDRESS_MD5_INFO',
            'P_NEAR_DRIVERS',
            'P_NEAR_PRE_DRIVER_PHONE',
            'P_NEAR_PRE_DRIVER_ID',
            'D_EARNINGS_INQUIRY_WEEK', // 司机流水查询周数据
            'D_EARNINGS_INQUIRY_DAY', // 司机流水查询周数据
            'P_SEND_MSG_',
            'O_AREA_LIST',
            'O_BASIC_INFO_',
            'O_EXTRA_INFO_',
            'O_ORDER_RESULT_INFO_',
            'O_ORDER_RESULT_DRIVER_INFO_',
            'O_ORDER_CMP_INFO_',
            'O_ORDER_PUNISH_FORWARD_',
            'O_ORDER_RECALL_LIMIT_',
            'O_GETORDERID_BY_DISTANCE_',
            'O_ORDER_ETA_',
            'O_LATE_',
            'O_PUSH_LOG_',
            'O_TIME__DISTANCE_',
            'O_REALTIME_PRICING_NETWORK_',
            'O_REALTIME_PRICING_GPS_',
            'O_SAME_ADDRESS_',
            'O_AIRPORT_GUIDE_CACHE_DATA_',
            'O_MAPPING_COMBOID_AIRPORTID_',
            'O_FROM_TO_DISTANCE_INFO',
            'P_LOCATION',
            'P_BANNER_INFO_',
            'P_ESTIMATE_PRICE_CURRENCY_',
            'P_CARPOOL_ESTIMATE_PRICE_',
            'P_LOSS_REMAND_ESTIMATE_PRICE_',
            'P_ESTIMATE_CACHE_LOSS_REMAND_PRODUCT_INFO_',
            'P_CONTROL_CONFIG_',
            'P_CARPOOL_FREQUENCY_CONTROL_',
            'API_SMS_DOWNLOAD_',
            'a_p_neworder_',
            'a_p_cancelorder_',
            'a_p_same_passenger_driver_dealorder_',
            'a_p_bindidbypid_',
            'A_P_BINDID_ARRAY_BYPID_',
            'a_p_passenger_deal_order_frequency_',
            'A_P_GUIDE_LIST',
            'A_P_CANCEL_GUIDE_LIST',
            'C_R_DIDIPUSH_AD_DAY_', //商业变现滴滴播报广告司机一天推送限制
            'P_VERSIONID_',
            'D_ONLINE_TIME_',
            'K_COMPLAINT_COUNT_',
            'O_WARN_TIMES_',
            'O_WARN_ETA_',
            'O_WARN_ETA_LATE_',
            'O_WARN_WAIT_',
            'O_WARN_TRAVEL_TIME_',
            'O_WARN_TRAVEL_DISTANCE',
            'O_WARN_REASSIGN_',
            'O_WARN_NOT_PAY',
            'O_STATISTIC_DATE_TYPE_',
            'O_WARN_TRAVEL_TWO_DISTANCE',
            'O_WARN_TRAVEL_FEE',
            'O_WARN_TRAVEL_TWO_FEE',
            'O_DACHE_ANYCAR_GUIDE_INFO',
            'M_DAILY_LEVEL_STATIS_BYAREA_',
            'D_PANGU_DAY_ASSIGN_',
            'M_PANGU_CONFIG_',
            'M_PANGU_RULE_',
            'M_PANGU_RULE_DB_INFO_',
            'M_PANGU_REWARD_',
            'D_BASIC_INFO_',
            'D_EXT_INFO_',
            'D_GETDRIVERIDBYPHONE_',
            'D_SEPARATE_QUERY_INFO_',
            'D_PUSH_TIME_',
            'D_IDCARD_INFO_',
            'D_IDCARD_',
            'D_CONFIRM_CODE_INFO_',
            'D_SENDCODE_',
            'D_COMPANY_BASIC_INFO_',
            'DRIVER_LOC_',
            'D_APP_VERSION_',
            'D_CAR_BASIC_INFO_',
            'D_CARID_',
            'D_PHOTO_',
            'D_PHOTO_V2_',
            'D_RESENDORDER_FILTER_',
            'D_FIND_DRIVER_NUM_',
            'D_GETDRIVERIDBYIDCARDNO_',
            'D_CHANGE_PWD_ERROR_TIMES_',
            'D_GET_BY_PASSPORT_UID_',
            'D_GET_DAY_INFO_',
            'D_SET_DAY_INFO_TIMESTAMP_',
            'D_TOTALFEE_',
            'D_GETCARIDBYPLATENO_',
            'D_GETDRIVERLISTBYCARID_',
            'D_MONTH_ORDER_COST_',
            'NEW_D_MONTH_ORDER_COST_',
            'D_MONTH_LEVEL_',
            'D_WITHDRAW_CNT_',
            'D_WITHDRAW_MONEY_',
            'D_CITY_PRICE_',
            'D_CITY_CAR_PRICE_',
            'D_CITY_CAR_DEFAULT_PRICE_',
            'D_PHONE_VERIFY_',
            'D_PHONE_VERIFY_LIMIT_',
            'D_CITY_DEFAULT_CAR_LEVEL_',
            'D_REASSIGN_ORDER_DUTY_TOTAL_NUM_',
            'D_MEET_PASSENGER_EXEMPT_NUM_',
            'P_PLATFORM_INCREASE_LOCK',
            'P_REDIS_CACHE_TAG',
            'CAR_LEVEL_LIST',
            'CAR_LEVEL_LIST_RAW',
            'O_YUYING_IS_ORDER_',
            'O_KEY_LOCK_',
            'D_MONITOR_',
            'M_ACCOUNT_ROLE_INFO_',
            'D_LONGRENT_COMMISSION_CFG_',
            'D_SEPARATE_CFG_',
            'D_BASIC_WAGE_USED_CFG_',
            'D_BEGIN_CHARGE_LOCK_',
            'D_JIAOBAN_REQ_LOCK_',
            'D_WITHDRAW_TAX_CFG_',
            'D_BASIC_WAGE_WEEK_LIMIT_CFG_',
            'D_INSURANCE_USED_CFG_',
            'D_BASIC_WAGE_MONTH_LIMIT_CFG',
            'D_SIGNED_SCANED_QRCODE', //二维码series、stationid
            'D_ORDER_NUM_',
            'D_WEEK_REASSIGN_ORDER_DUTY_NUM_',
            'P_WEEK_CANCEL_ORDER_DUTY_NUM_',
            'M_ACTIVITY_EFFECTIVE_AREA_',
            'M_ACTIVITY_EFFECTIVE_CFG_',
            'P_WXAGENT_BIND_STATUS_',
            'P_ORDER_DEFAULT_COUPON_',
            'D_RESEND_ORDER_STATUS_STORED_',
            'O_GET_PWD_CNT', //网点端获取二维码次数
            'O_ACCESS_QRCODE_CNT', //获取二维码次数
            'O_STATION_LOGIN_FAIL', //登陆失败次数
            'O_STATION_INFO', //审核过的站点信息
            'P_USER_GUIDE_WAIT_TIME_', //导流高价值用户
            'P_DIST_GUIDE_PARAM_', //导流高价值用户各区系数及专车导流到出租车的等待时间 hash
            'D_FREERIDE_INFO_', //司机顺风车信息
            'D_FREERIDE_REMIND_INFO_', //顺风车提醒key
            'D_CHECK_TICKET_CACHE_',   //校验ticket 缓存
            'TP_VARIFLIGHT_INFO_',      //非常准信息 +fnum（航班号）+flightDate（起飞日期）
            'TP_VARIFLIGHT_ADDPUSH_',   //定制航班信息推送 +fnum（航班号）+flightDate（起飞日期）+dep(起始地三字母)+arr(目的地三字母)
            'D_PROTECTION_FEE_CFG_',
            'O_SEP_RECORD_CONF_',
            'O_SEP_RECORD_DETAIL_',
            'O_SEP_ACTUAL_PERCENT_',
            'O_SEP_DRIVER_BY_AREA_LEVEL_MODEL_',
            'O_RELATION_TRAFFIC_INFO_',
            'TOOL_TRAFFIC_INFO_',
            'TOOL_TRAFFIC_LIST_',
            'D_DISTRICT_USING_ACTIVITY',
            'D_DISTRICT_HISTORY_ACTIVITY',
            'D_DISTRICT_PANGU_HISTORY_ACTIVITY', //盘古mis修改历史
            'D_DISTRICT_PANGU_USING_ACTIVITY', //盘古mis修改
            'O_MULTI_CAR_LEVEL_',
            'P_REUNION_SEND_TIP_', //回家提醒
            'P_GET_LINE_SEND_MSG_', //订单状态轮训拉取路线和拼友
            'P_ESTIMATEPRICE_DYNAMIC_PRICE_TEMP_', //动态调价临时缓存
            'P_ESTIMATEPRICE_DYNAMIC_PRICE_', //动态调价缓存发单使用
            'P_ESTIMATE_DYNAMIC_PRICE_PAGE_DATA', //动调页缓存数据
            'D_DRIVER_FREEZE_', //司机被冻结前缀
            'D_DRIVER_CAPTCHA_ERROR_NUM_', //司机连续输入验证码错误次数
            'D_DRIVER_CAPTCHA_',  //司机验证码
            'A_P_WAIT_GUIDE_FOR_TAXI', //标识专车导流到出租车等待时弹的次数
            'A_P_CANCEL_GUIDE_FOR_TAXI', //标识专车导流到出租车取消时弹的次数
            'P_GUIDE_FROM_TAXI_OR_GULFSTREAM', //标识10分钟内是否有导流
            'A_P_CANCEL_GUIDE_FAST', //快车防打扰
            'A_P_WAIT_GUIDE_FAST', //快车防打扰
            'P_GUIDANCE', //快车导流时间和方向
            'O_FINISH_COORDINATES_', //完成订单司机轨迹
            'D_ORDER_RESEND_CNT_',  //司机改派次数
            'P_CARPOOL_FIRST_PASSENGER_DETOUR_METRE_',
            'P_COUNT_INFO_',  //乘客计数信息
            'O_BONUS_CACHE_', //奖励缓存
            'P_LOW_ORDER_TIP',
            'P_CARPOOL_DECISION_',
            'P_BUBBLE_TAG_', // 冒泡特征
            'P_ESTIMATE_TRACE_ID_',
            'P_LIMIT_FEE_', //最低消费配置缓存',
            'P_CANCEL_ORDER_DUTY_TOTAL_NUM_',
            'P_MEET_DRIVER_EXEMPT_NUM_',

            'D_MODEL_',  //司机出车状态类型，统计在线司机专用
            'D_REG_NUM_DRIVER',
            'D_TMP_NUM_DRIVER', //当前10s内注册的司机数量
            'P_UNFASTCAR_USER_', //非目标用户
            'P_NEW_CONTROL_CONFIG_',
            'D_DISTRICT_CAR_LEVELS_', //城市开通车型
            'D_BRAND_NAME_', //#车辆品牌.车系名
            'P_MINUS_SUPPLY_NUM_', //立减活动补偿次数
            'O_COORDINATE_BEGIN_FINISH_',  //订单开始计费、结束计费坐标
            'D_DEST_EDIT_NUM_',  //实时目的地每天允许修改的最大次数
            'O_FSOURCE_',   //订单fsource
            'O_POOL_SEAT_DISCOUNT_', //拼座订单折扣
            'P_GUIDE_UNIQ_DATA_', //导流数据
            'P_PASSENGER_ORDER_NUM_INFO_', //乘客次数统计key
            'P_FORMAT_PASSENGER_INFO', //获取乘客头像昵称key
            'P_FORMAT_PASSENGER_LEVEL', //获取乘客等级key
            'P_UPDATE_DISPLAY_PRICE_', //乘客取消行程更新拼友订单对应司机展示价格
            'P_GET_REL_PASSENGER', //获取拼友行程规划key
            'P_CARPOOL_CONTROL_CONFIG_', //管控配置
            'D_JIAOBAN_REQ_TIME_',  //司机请求交班时间
            'D_NOT_ASSIGN_DEST_EDIT_NUM_',  //抢单模式下实时目的地每天允许修改的最大次数
            'P_DISCARDED_ORDER_TAG_', //token对应乘客丢单标记
            'P_FASTCAR_MINI_NOTICE_SMS_',
            'D_CARPOOL_DRIVER_ORDER_',
            'P_ACT_FASTENSURE_',
            'P_CITY_FASTENSURE_CFG',
            'D_FRIST_ON_CAR_EVERY_DAY_', //司机每天第一次出车
            'O_TRANSFER', //转账信息缓存
            'D_DRIVER_COMPET_ORDER_NUM_', //司机每天抢单的数量
            'O_PUSH_FILTER_LOCK_', //push filter order 锁
            'D_ONLINE_TAGS_',  //统计司机在线状态
            'O_ORDER_DEVICE_RELATION_', //订单设备对应关系
            'D_PUSH_FILTER_ORDER_', //dPushFilter Order去重处理
            'P_CARPOOL_FIRST_TIPS_', //拼车首次提示
            'D_ASSIGN_REJECT_STAT_',    //指派拒接统计当日拒接数
            'D_ASSIGN_REJECT_STATUS_', //司机是否因指派拒接超过阀值而被封禁
            'P_CARPOOL_REWARD_INFO_', //拼成奖励
            'D_GOOD_DRIVER_', //好司机
            'D_GOOD_DRIVER_IMPORT_', //好司机导入成功标志
            'P_FLAG_AGENT_REMIND_',  //是否需要提醒免密签约
            'D_LISTEN_ORDER_STATUS_',  //司机当前听单状态
            'D_LAST_DISPLAY_STATUS_',
            'P_CARPOOL_SALES_COUPON_', //拼成大促
            'P_CARPOOL_COOPERATE_MSG_', //拼成大促
            'P_ORDER_LIKE_WAIT_FLAG_', //标识订单是否是愿等订单
            'P_ESTIMATE_CONFIRM_INFO_', //预估confirm缓存数据
            'P_PREPAY_ORDER_CACHE_', // 预付订单缓存
            'P_CARPOOL_COMMUTE_TO_WORK_',
            'P_CARPOOL_COMMUTE_FROM_WORK_',
            'P_SHOW_WEBAPP_COUPON_INFO_REFUSE_',
            'P_SHOW_WEBAPP_COUPON_INFO_',
            'P_DUSE_EXPIRE_',
            'P_BROADCAST_CACHE', // 预付成功后发单不立即播单时写入
            'P_UPDATE_DEST_PREPAY_CACHE', //行程中修改目的地信息缓存
            'P_INSURANCE_INFO_', //缓存保险人信息，包车用
            'P_CHECK_USER_EMERGENCY_CONTACT_', //老版本检查乘客紧急联系人
            'P_INSER_ORDER_BROADCAST_QUEUE',
            'P_CHECK_OLD_VERSION_SMS_AUTH_RECORD_', // 老版本短信授权录音
            'P_CHECK_OLD_VERSION_SMS_AUTH_RECORD_CNT_', // 老版本短信授权短信发送次数
            'P_CARPOOL_WALK_STATION_CONFIRM_', //愿走订单是否确认下车点缓存
            'P_CHECK_YOURSELF_CONTACT_TRANSFER_', // 设置自己为紧急联系引导转化
            'P_LOSS_REMAND_DRIVER_FINISHED_ORDER_', // 司机已经完成的物品遗失订单
            'P_LOSS_REMAND_PASSENGER_FINISHED_ORDER_', // 乘客已经完成的物品遗失订单
            'P_LOSS_REMAND_SAME_DRIVER_FINISHED_ORDER_', // 针对同一个司机已经，乘客完成的物品遗失订单
            'P_LOSS_REMAND_DRIVER_CONCURRENCY_LOCK_', // 针对同一个司机的并发锁
            'P_SHOW_TAXI_CASH_PAY_CLOSE_', //出租车现金付下线
            'P_ESTIMATE_BUBBLE_DYNAMIC_PRICE_', //预估展示动调气泡
            'P_SPECIAL_PRICE_COMPONENT_', //价格沟通组件
            'P_CARPOOL_DAY_POPUP_DURATION',
            'P_ESTIMATE_BUBBLE_ACTIVITY_', //预估活动气泡
            'P_ESTIMATE_BUBBLE_CANCEL_INTERCEPT_', //预估拦截弹窗
            'P_HK_TAXI_TIP_INFO_',//香港出租车小费
            'P_HK_TAXI_COMMENT_INFO_',//香港出租车捎话
            'P_REC_COMBO_CACHE_PREFIX_', //套餐缓存
            'P_SPACIOUS_CAR_EID_', //车大联盟品类EID（用快车EID查询）
            'P_CARPOOL_PAGE_SUPPORT_ONE_SEAT_', //司乘一口价初次扶持拼车一座
            'P_ESTIMATE_ORIGIN_DATA_', //沟通组件需要使用的预估缓存
            'P_GUIDE_ANYCAR_ESTIMATE_PRICE_', // 同时呼叫anycar预估缓存
        ),
        'servers' => array(
            //>>> @foreach||service=GS_REDIS_LOSS
            //>>> 'LOSS_${FOREACH_INDEX}'=>array(
            //>>>    'host'=>'${GS_REDIS_LOSS_IP}',
            //>>>    'port'=>'${GS_REDIS_LOSS_PORT}'
            //>>> ),
            'LOSS_1'  => array(
                'host' => '**************',
                'port' => '3000',
            ),
            'LOSS_2'  => array(
                'host' => '**************',
                'port' => '3000',
            ),
            'LOSS_3'  => array(
                'host' => '**************',
                'port' => '3000',
            ),
            'LOSS_4'  => array(
                'host' => '**************',
                'port' => '3000',
            ),
            'LOSS_5'  => array(
                'host' => '*************',
                'port' => '3000',
            ),
            'LOSS_6'  => array(
                'host' => '**************',
                'port' => '3000',
            ),
            'LOSS_7'  => array(
                'host' => '**************',
                'port' => '3000',
            ),
            'LOSS_8'  => array(
                'host' => '**************',
                'port' => '3000',
            ),
            'LOSS_9'  => array(
                'host' => '**************',
                'port' => '3000',
            ),
            'LOSS_10' => array(
                'host' => '**************',
                'port' => '3000',
            ),
            'LOSS_11' => array(
                'host' => '**************',
                'port' => '3000',
            ),
            'LOSS_12' => array(
                'host' => '*************',
                'port' => '3000',
            ),
            'LOSS_13' => array(
                'host' => '*************',
                'port' => '3000',
            ),
            'LOSS_14' => array(
                'host' => '*************',
                'port' => '3000',
            ),
            'LOSS_15' => array(
                'host' => '*************',
                'port' => '3000',
            ),
            'LOSS_16' => array(
                'host' => '*************',
                'port' => '3000',
            ),
            'LOSS_17' => array(
                'host' => '*************',
                'port' => '3000',
            ),
            'LOSS_18' => array(
                'host' => '*************',
                'port' => '3000',
            ),
            'LOSS_19' => array(
                'host' => '**************',
                'port' => '3000',
            ),
                   //<<<
        ),
        'disf' => 'disf!codis-coproxy-gs-gscachegz',
    ),

    'forever' => array(
        'keys'    => array(),
        'servers' => array(
            //>>> @foreach||service=GS_REDIS_FOREVER
            //>>> 'gs.redis-${FOREACH_INDEX}'=>array(
            //>>>     'host'=>'${GS_REDIS_FOREVER_IP}',
            //>>>     'port'=>'${GS_REDIS_FOREVER_PORT}'
            //>>> ),
            'gs.redis-01' => array(
                'host' => '*************',
                'port' => '3000',
            ),
            'gs.redis-02' => array(
                'host' => '************',
                'port' => '3000',
            ),
            'gs.redis-03' => array(
                'host' => '**************',
                'port' => '3000',
            ),
            'gs.redis-04' => array(
                'host' => '**************',
                'port' => '3000',
            ),
            'gs.redis-05' => array(
                'host' => '**************',
                'port' => '3000',
            ),
            'gs.redis-06' => array(
                'host' => '*************',
                'port' => '3000',
            ),
                   //<<<
        ),
        'disf' => 'disf!codis-coproxy-gs-gsstore_coproxy',
    ),
);
Svc::discoverEndpoints(
    'disf!codis-coproxy-gs-gscachegz',
    function ($endpoints) use (&$config) {
        $config['config_redis_servers']['loss']['servers'] = array();
        foreach ($endpoints as $index => $endpoint) {
            $config['config_redis_servers']['loss']['servers']['LOSS_'.($index + 1)] = array(
                'host' => $endpoint['ip'],
                'port' => (string)($endpoint['port']),
            );
        }
    }
);
Svc::discoverEndpoints(
    'disf!codis-coproxy-gs-gsstore_coproxy',
    function ($endpoints) use (&$config) {
        $config['config_redis_servers']['forever']['servers'] = array();
        foreach ($endpoints as $index => $endpoint) {
            $config['config_redis_servers']['forever']['servers'][sprintf('gs.redis-%02d', $index + 1)] = array(
                'host' => $endpoint['ip'],
                'port' => (string)($endpoint['port']),
            );
        }
    }
);

return $config;
