<?php

/**
 * @desc   : 反作弊配置
 *
 * <AUTHOR> wkeke<<EMAIL>>
 * @date   : 2014-07-13
 */
//<<<
$config['carpool_antispam'] =array(
);
$config['passenger_antispam'] = array(
    //创建订单 控制策略
    'new_order_frequency' => array(
        array(
            'time' 		   => 300, //时间周期
            'threshold' => 8,   //阈值
            'action' 	  => 1,   //1,判定单子作弊, 2,禁用该账号，转人工处理, 3.规避
        ),
    ),

    //取消订单 控制策略
    'cancel_order_frequency' => array(
        array(
            'time' 		   => 14400, //四小时
            'threshold' => 6, //3,
            'action' 	  => 1,
        ),
        array(
            'time' 		   => 86400,
            'threshold' => 8, //3,
            'action' 	  => 1,
        ),
    ),

    //取消实时订单 控制策略
    'cancel_real_order_frequency' => array(
        array(
            'time' 		   => 14400, //四小时
            'threshold' => 6, //3,
            'action' 	  => 1,
        ),
        array(
            'time' 		   => 86400,
            'threshold' => 8, //3,
            'action' 	  => 1,
        ),
        array(
            'time' 		   => 3600,
            'threshold' => 3, //3,
            'action' 	  => 1,
        ),
    ),

    //同一乘客、司机 控制策略
    'same_passenger_driver_deal_order_frequency' => array(
        array(
            'time' 		   => 86400, //一天
            'threshold' => 10, //3,
            'action' 	  => 3,
        ),
        array(
            'time' 		   => 604800, //一周
            'threshold' => 20, //10,
            'action' 	  => 3,
        ),
    ),

    //同一乘客成交控制策略
    'passenger_deal_order_frequency' => array(
        array(
            'time'      => 14400, //四小时
            'threshold' => 5,
            'action'    => 3,
        ),
        array(
            'time' 		   => 86400, //一天
            'threshold' => 9, //3,
            'action' 	  => 3,
        ),
    ),
    //加速器控制策略

    'strive_order_control' => array(
        'min_time' 		=> 3, //最短抢单时间
        'log_time' 		=> 7, //短于这个时间，需要把信息记录下来，以便分析
    ),
);

return $config;
