<?php

define('UNFINISH_REGISTER', 'unfinish_register');
define('GET_SMS_CODE', 'get_sms_code');
define('HAS_AUDITED', 'has_audited');
define('RESEND_ORDER_SMS', 'resend_order_sms');
$config['message'] = array(
    'unfinish_register'    => '恭喜您注册成功。我们将在5个工作日内同您联系，届时将告知您加盟面试的流程和日期。优打车，欢迎您的加入！详询400-000-0666 ',
    'has_audited'          => '恭喜您通过考试！正式成为专车司机！立即上线接单，了解专车高额奖励，做司机中的高薪一族！点击链接了解司机晋升流程http://www.dwz.cn/H2bbu,详询400-0000-666 ',
    'has_audited_with_pwd' => '恭喜您通过考试，正式成为专车司机！立即上线接单，了解专车高额奖励，做司机中的高薪一族！点击链接了解司机晋升流程http://www.dwz.cn/H2bbu,您的登陆密码为%s.详询400-000-0666 ',
    'get_sms_code'         => '验证码：',

    'driver_bubble_info_waiting' => '%s分钟%s公里',

    'driver_bubble_info_arrived' => '司机已经到达',

    'driver_late_to_passenger'=> '抱歉司机未能在约定时间内到达，请您耐心稍候一会。如有疑问，您可拨打电话和司机沟通。',

    'driver_late_to_driver'=> '已到达约定乘客上车时间，如您还未到达约定地点，请与乘客联系。',

    'driver_late_b2b_booking_to_driver'=> '您有一个预约订单时间已到，乘客在等待您接驾，若有任何问题，请联系客服。',
    'driver_late_b2b_now_to_driver'    => '您已迟到，乘客等待您接驾，若有任何问题，请联系客服。',

    'driver_arrive_to_passenger'=> '您好，约定上车时间到了，%s后司机可以开始计费等您。',

    'driver_arrive_to_passenger_recover'=> '司机已到达，%s后开始计费等您，请您尽快上车。',

    'consult_driver_tips'   => '我承诺约%s分钟到达上车地点接您，您接受么？',

    'consult_system_tips'   => '30秒后司机有可能取消，请尽快确认',

    'waiting_driver_tips'   => '本次行程将由%s为您服务，他将准时来接您。',

    'waiting_driver_tips1'   => '本次行程将由更高端车型来接您，车型免费升级，还是一样的价格~', //更高端司机接单提示
    'waiting_driver_tips2'   => '本次行程按照%s收费。',
    'waiting_driver_tips3'   => array(
        'default' => '您的专车(%s)正前往 {%s} 接您。',
        'fastcar' => '您的司机正前往 {%s} 接您。',
    ),
    'waiting_driver_tips4' => '司机将准时来接您，请您稍候。', //以上几条目前都改为用这一条
//    'order_score_tips'      =>  array(
//                                    'default'   =>  '完成支付后将获得至少%d积分，实际积分数额以到账为准~',
//                                    'good'   =>  '完成支付后将获得约%d积分，其中%d分为优质订单奖励~',
//                                ),
    'order_score_tips'      => array(
        'default'   => '完成支付后将获得约%d积分，实际数额以到账为准~',
        'good'      => '完成支付后将获得约%d积分，车费越高积分越多哦~',
    ),
    'order_score_tips1' => '行程开始，完成支付后奖励约%s积分，车费越高积分越多哦~',

    'cancel_trip_pre_order_free'   => '是否取消？距预约出发时间超过1小时，您可免费取消。',

    'cancel_trip_pre_order_fee'   => array(
        'msg'   => '是否取消？如取消，为补偿司机空驶损失，您需支付%d元。',
        'val'   => 20, //出发前1小时内预约单，需乘客支付n元
    ),

    'cancel_trip_new_order_billing'   => '是否取消？如取消，为补偿司机等待损失，您需支付%d元。',

    'cancel_trip_new_order_waiting'   => array(
        'msg'   => '是否取消？如取消，为补偿司机空驶损失，您需支付%d元。',
        'val'   => 10, //实时订单，司机未计价
    ),

    'cancel_trip_driver_late'   => '是否取消？本订单司机已迟到，为补偿您的损失，您可免费取消。',

    'cancel_trip_free'          => '是否取消这次叫车？',

    'cancel_trip_tips'          => array(
        'new_order' => array(//路程百分比x = 1 - （乘客取消时司机距离乘客出发地的距离 / 接单时司机距离乘客出发地的距离）
            1   => '司机已经行驶{%s}公里', //乘客取消时司机已经行驶的距离大于等于4km
            2   => '司机距您还有{%s}公里', //第一个小车：x小于10%
            3   => '司机距您还有{%s}公里', //第二个小车：x大于等于10%，x小于40%
            4   => '司机距您还有{%s}公里', //第三个小车：x大于等于40%
        ),
        'pre_order' => array(
            1   => '感谢您预约滴滴专车！', //第一个小车：取消时间-预约时间大于1小时
            2   => '司机可能在赶来的路上了', //第三个小车：取消时间-预约时间小于等于1小时
        ),
    ),

    'cancel_trip_push_driver'   => array(
        'driver_late'                   => '前往%s的乘客因%s取消了订单。', //司机迟到，乘客取消行程
        'free_neworder_title'           => '前往%s的乘客因%s取消了订单。', //抢单后3分钟内实时订单免费取消
        'waiting_new_order_title'       => '本订单乘客已取消', //实时订单未计价标题
        'waiting_new_order_subtitle'    => '前往%s的乘客因%s取消了订单。', //实时订单未计价副标题
        'billing_new_order_title'       => '本订单乘客已取消', //实时订单已计价标题
        'billing_new_order_subtitle'    => '乘客将支付车费%s元', //实时订单已计价副标题
        'hour_later_preorder'           => '%s前往%s的乘客因%s取消了订单。', //预约一小时后订单取消
        'hour_within_preorder'          => '%s前往%s的乘客因%s取消了订单。', //预约一小时内订单取消
        'three_minute_within_neworder'  => '前往%s的乘客因%s取消了订单。',
    ),

    'cancel_trip_push_driver_b2b'   => array(
        'driver_late'                   => '前往%s的乘客因%s取消了订单。', //司机迟到，乘客取消行程
        'free_neworder_title'           => '本订单乘客已因%s取消', //抢单后3分',//抢单后3分钟内实时订单免费取消
        'waiting_new_order_title'       => '本订单乘客已取消', //实时订单未计价标题
        'waiting_new_order_subtitle'    => '乘客因%s取消订单，乘客将赔付您%s元', //实时订单未计价副标题
        'billing_new_order_title'       => '本订单乘客已取消', //实时订单已计价标题
        'billing_new_order_subtitle'    => '乘客将支付车费%s元', //实时订单已计价副标题
        'hour_later_preorder'           => '%s前往%s的乘客因%s取消了订单。', //预约一小时后订单取消
        'hour_within_preorder'          => '%s前往%s的乘客因%s取消了订单。将给您赔付%s元。', //预约一小时内订单取消
        'three_minute_within_neworder'  => '前往%s的乘客因%s取消了订单。',
    ),
    //拼车单抽屉状态和push消息
    'car_pool'  => array(
        'new_order' => array(
            'strive_passenger_order'    => '司机将于{%s前}赶到您的上车点，行程中持续为您寻找拼友',
            'strive_passenger_order1'   => '司机将于{%s前}赶到您的上车点。',
            'strive_friend_order'       => '司机正在去接拼友%s，将于{%s前}赶到您的上车点。',
        ),
        'pre_order' => array(
            'strive_passenger_order'    => '司机将在{出发时间前}赶到您的上车点，请保持手机畅通，遇到特殊情况请与司机及时联系。',
        ),
        'arrive_passenger'          => '司机已到您的上车点，请于{%s前}上车，迟到后司机可取消订单，您将需支付取消费。',
        'arrive_passenger1'         => '司机已到您的上车点，请您在{%s前}上车，迟到后司机可开始计费等您；若司机取消，您将需支付取消费。',
        'arrive_friend'             => '司机到达拼友%s上车点，拼友将于{%s前}上车，请耐心等候',
        'to_friend_starting_name'   => '司机正在去接拼友%s的路上，%s请耐心等待',
        'to_friend_dest_name'       => '司机正在去往拼友%s的目的地',
        'to_passenger_dest_name'    => '司机正在去往您的目的地',

        'with_detour'     => '您的行程预计会增加%s公里，',
        'without_detour'  => '比较顺路，预计不会增加您的行程距离，',
        //司机抢单，拼友加入，推送消息给乘客
        'add_passenger' => array(
            'text_strive'         => '司机抢单，同时为您找到一位顺路拼友',
            'text_reward_strive'  => '司机抢单，同时为您找到一位顺路拼友，赶快点击领取拼成红包',
            'text_content'        => '为您找到一位顺路拼友，点击查看行程详情',
            'text_reward_content' => '为您找到一位顺路拼友，赶快点击领取拼成红包',
            'temp_content'        => '拼友%s加入，路线重新调整。',
        ),
        //司机更改接送顺序，推送消息给乘客
        'update_line'  => array(
            'text_content'  => '司机更改接送顺序，路线重新规划，点击查看行程详情',
            'temp_content'  => '司机更改了行程的接送顺序，将先%s，请耐心等候',
            'temp_content2' => '司机更改了行程的接送顺序，路线重新规划，请耐心等待',
        ),
        //司机改派
        'driver_cancel' => array(
            'free_content'  => '司机迫不得已取消了%s日%s的%s订单，抱歉影响你的出行，详情返回滴滴客户端查看。', //司机责任，推送消息给乘客
            'pay_content'   => '司机因等你过久，取消了%s日%s的%s订单，请支付%s元取消费，详情返回滴滴客户端查看。', //乘客责任，推送消息给乘客
            'save_content'  => '您%s分钟内未上车支付%s元取消费', //取消行程页展示文案
        ),
        //乘客取消行程，司机改派订单，推送消息给拼友
        'passenger_cancel'  => array(
            'text_content'  => '拼友%s取消行程，您的行程继续，并持续为您寻找拼友',
            'temp_content'  => '拼友%s取消行程，您的行程继续，并持续为您寻找拼友',
        ),
    ),

    'cancel_trip_button'    => array(
        0   => array(
            '我知道了',
        ),
        1   => array(
            '投诉乘客', '我知道了',
        ),
    ),

    'estimate_price_fail'   => '车费暂无法预估',

    'estimate_price_basic'  => '{%s}元起步+{%s}元/公里',

    'estimate_price_txt'   => '车费预估约{%s}元',

    'estimate_price_discount_txt'   => '车费约{%s}元 优惠价{%s}元',

    'estimate_price_least_txt' => '约{¥%s}起',

    'estimate_price_description' => '车费可能因交通情况，天气等原因变更。整体费用不包含高速费，停车费等费用，打车券抵扣请以实际为准。',

    'estimate_price_discount_description' => '原价{%s}元 优惠价{%s}元，预计里程%s公里，如实际行驶超出预计里程10公里，您将无法享受优惠价。',

    'estimate_price_discount_description_new' => '本次预计里程为%s公里，如实际行驶超出预计里程10公里，您将无法享受优惠价。',

    'estimate_price_guid_txt'    => '本次行程车费预估约%s元',
    'estimate_price_fastcar_txt' => '预估费仅需%s元', //快车预估费

    'estimate_price_coupon_tips' => '当前%s元券可抵', // 给estimate_price_txt, estimate_price_guid_txt两者使用

    'estimate_price_car_level' => '本次行程按照{%s}收费', // 给estimate_price_txt联合使用

    'forbidden_driver_txt'        => '您好，由于%s，您的帐号将被系统冻结%s。冻结期间您不能听到新的订单，已接的预约订单将被改派。详询：400-0000-666 ',
    'prepare_forbidden_driver_txt'=> '您因%s将被短期封禁，封禁开始时间为%s，封禁时长为%s。',	//预约封禁
    'cancelforbidden_driver_txt'  => '您的帐号已经解冻。您现在可以正常接单。详询：400-0000-666 ',

    'passenger_late_to_driver' => '您好，司机已开始计费等您。等候时间按低速计价，支付时将与行程低速合并计费。',

    'passenger_late_fastcar'   => '您好，司机已开始计费等您。等候时间按服务时间计价，支付时将与行程时长费合并计费。',

    'driver_strive_order_sms' => '您的车辆%s %s将准时到达，司机%s %s（%s星）为您服务。',

    'driver_arrived_ontime'  => '司机已到达，请您上车。%s',
    'driver_arrived_ontime1' => '司机已到达，请您上车。',
    'driver_arrived_ontime2' => '司机已到达，请您在 {%s} 上车。',

    'driver_arrived_delaytime' => '我已到达上车位置等您，将免费等您%s分钟。',

    'driver_arrived_ontime_sms' => '您的滴滴座驾%s %s已到达约定地点。司机%s %s（%s星）将为您服务%s。',

    'driver_arrived_delaytime_sms' => '您的滴滴座驾%s %s已到达约定地点。司机%s %s（%s星）将为您服务%s。',

    'driver_begin_charge' => '行程开始，祝您一路好心情。',

    'driver_resend_order' => '司机%s因%s未能来接您。我们正在努力为您改派其他司机，请您稍候。',

    'resend_order_b2b_sms' => '司机%s因%s未能来接乘客，我们正在努力改派其他司机。为给您带来的不便深表歉意。',

    'resend_order_sms' => '司机%s因%s未能来接您，我们正在努力为您改派其他司机，请您稍候。为您带来的不便深表歉意。',

    'driver_resend_order_now_sms' => '您有一个实时订单被改派，%s，从%s到%s。',

    'driver_resend_order_now_voice_sms' => '您有一个实时订单被改派，%s，从%s出发。',

    'driver_resend_order_booking_sms' => '您有一个预约订单被改派，%s，从%s到%s。',

    'driver_resend_order_booking_voice_sms' => '您有一个预约订单被改派，%s，从%s出发。',

    'driver_resend_order_kefu_now_sms' => '您有一个实时订单被客服改派，%s，从%s到%s。',

    'driver_resend_order_kefu_now_voice_sms' => '您有一个实时订单被客服改派，%s，从%s出发。',

    'driver_resend_order_kefu_booking_sms' => '您有一个预约订单被客服改派，%s，从%s到%s。',

    'driver_resend_order_kefu_booking_voice_sms' => '您有一个预约订单被客服改派，%s，从%s出发。',

    'driver_cancel_order' => '司机取消订单。',

    'driver_resend_success' => '感谢您的等待，已为您改派司机%s为您服务。',

    'driver_resend_success_tail' => '他将在%s分钟内到达。',

    'driver_resend_success_now_sms' => '已为您改派司机%s为您服务，他将在%s分钟内到达，为给您带来的不便深表歉意。',

    'driver_resend_success_booking_sms' => '您前往%s的滴滴订单，已为您改派司机%s为您服务，为给您带来的不便深表歉意。',

    'driver_resend_b2b_success_now_sms' => '乘客手机号%s的专车订单已改派司机%s服务，他将在%s分钟内到达，为给您带来的不便深表歉意。',

    'driver_resend_b2b_success_booking_sms' => '乘客手机号%s，前往%s的专车订单，已改派司机%s服务，为给您带来的不便深表歉意。',

    'resend_order_failed_now' => '司机未能完成服务。',

    'resend_order_failed_booking' => '司机未能完成服务。',

    'resend_order_failed_now_sms' => '您的滴滴订单未能为您成功改派司机，为给您带来的不便深表歉意。',

    'resend_b2b_order_failed_now_sms' => '乘客手机号%s的专车订单未能成功改派司机，为给您带来的不便深表歉意，您可以重新发送订单。',

    'resend_order_failed_booking_sms' => '您前往%s的滴滴订单未能成功改派司机，为给您带来的不便深表歉意。',

    'resend_b2b_order_failed_booking_sms' => '乘客手机号%s，前往%s的专车订单未能成功改派司机，为给您带来的不便深表歉意，您可以重新发送订单。',

    'combine_order_to_passenger_input_now_sms' => '师傅，您好！您在%s成功抢到由%s到%s的（实时）组合订单，乘客电话：%s',

    'combine_order_to_passenger_input_booking_sms' => '师傅，您好！您在%s成功抢到由%s到%s的（预约）组合订单，乘客电话：%s',

    'driver_arrived_phone_protected_sms' => '（拨打该司机真实号码将无法对您进行号码保护）',

    'driver_close_order' => '司机未能完成服务，车费免单。',

    'close_order_sms' => '司机%s因%s无法完成服务，请您重新叫车。给您带来的不便深表歉意。',

    'reserve_order_past_one_hour' => '%s前往%s的订单因超时已取消。', // 滴滴播报

    'realtime_order_past_twelve_hour' => '%s前往%s的订单因超时已取消。', // 滴滴播报

    'fail_appointment' => '订单超时关闭。',

    'delay_time_reason' => '订单计价超时关闭。',

    'reward_push_to_driver' => '好消息，%s“满%d单奖励%s元”的奖励金额已经打入您的司机端余额中，请您点击查看。',

    'new_driver_reward_to_driver' => '好消息！新人奖奖励已经发至您的司机端余额中，请您点击查看。',

    'first_use_title'   => '第一次用车', //首次使用弹层标题

    'first_use_content' => '我们将竭力让您享受到优质、安全的用车体验#发票将邮寄给您，行程结束后需输入联系方式#如遇特殊情况请尽快取消行程，超时将收取一定取消费', //首次使用弹层内容

    'invoice_sms'=> '亲爱的乘客您好，您于%s提交的发票已经寄出,快递单号为[%s]%s，请注意查收！祝您生活愉快!',

    'recommended_driver_content'   => '%s邀请您一起加入滴滴专车司机，轻松月入万元。http://t.cn/RhQMKBt',  //被推荐司机短信内容 BY AndyCong
    'mis_reward_push_to_driver'    => '好消息，%s奖励金额已经打入您的司机端余额中，请您点击查看。',
    'order_bonus_sms_to_passenger' => '亲爱的乘客，您的订单因为较长时间无司机响应，已经由小滴补贴%s元给司机，以激励司机接单。本费用不抵扣车费。祝您今天有个好心情！',

    //提现短信和push内容
    'driver_withdraw_sms'  => '您%s发起转账,通过滴滴向%s,尾号%s的卡号转账%s元,预计将在24小时内到账.',   //提现申请短信
    'driver_withdraw_push' => '您%s发起转账,通过滴滴向%s,尾号%s的卡号转账%s元,预计将在24小时内到账.',  //提现push消息

    'driver_withdraw_fail_sms'     => '由于%s的错误,您通过滴滴向%s,尾号%s的卡号转账%s元失败,您的车费不会有任何扣除,请您放心', //提现失败短信
    'driver_withdraw_fail_push'    => '由于%s的错误,您通过滴滴向%s,尾号%s的卡号转账%s元失败,您的车费不会有任何扣除,请您放心', //提现失败消息
    'driver_withdraw_success_sms'  => '您通过滴滴向%s，尾号%s的卡号转账%s元成功，您可以到滴滴专车司机端“我-余额”查看详情，也可以到银行网点查看到账情况。推荐朋友做司机更有高额奖励！详情请查看：司机端-更多-推荐。',       //提现成功消息
    'driver_withdraw_success_push' => '您通过滴滴向%s,尾号%s的卡号转账%s元,目前已到账,请到“收到的车费”查看',
    //月服务奖励消息
    'reward_mis_servicelevel_push_to_driver'=> '好消息，%s服务星级奖励金额已经打入您的司机端余额中，请您点击查看。',
    'new_passenger_reward_coupon'           => '小滴终于等到您啦~专车的新朋友，请收下见面礼吧！%s元专车代金券已经放入账户，待会支付专车车费时即可使用。更多专车券请到“我的打车券”查看~',
    'near_driver_nums_guid'                 => '附近%s辆专车可为您服务',
    'near_driver_nums_fastcar'              => '附近有%s辆快车可为您服务',
    'coupon_num_txt_guid'                   => '您还有%s张专车券可用',
    'coupon_guide_text_new'                 => '您还有总价值%s元专车券可用',
    'guide_title_new'                       => '打不到车 试试专车',
    'guide_for_taxi'                        => '此时呼叫出租车成交率更高', //专车导向出租车第一版文案
    'guide_for_taxi_title'                  => '暂无应答，试试出租车？', //专车导向出租车第一版标题
    'wx_agent_success_sms'                  => '您于%s月%s日%s成功开通微信免密支付业务，下次支付车费即可使用，您可以在“滴滴会员－设置“中查看开通状态',
    'wx_agent_pay_sms'                      => '可能由于%s导致开通失败.本次支付的0.01元已在余额中,下次自动抵扣车费.',
    'wx_agent_fail_sms'                     => '很抱歉，您申请的微信免密支付业务开通失败，您可以在“滴滴会员－设置”中尝试重新开通。',
    'wx_agent_release_sms'                  => '您于%s月%s日%s成功解除微信免密支付业务，下次可以通过微信支付或代充值支付车费。',

    'wxagent_pay_success_msg'   => '您账户%s月%s日%s通过微信免密支付扣款%s元。免密全新升级，行程结束自动扣款。平台其他业务也可微信免密支付车费。',
    'wxagent_pay_fail_msg'      => '您的账户于%s月%s日%s微信免密支付扣款失败，请您到“滴滴会员—我的行程”中支付车费，给您带来的不便请见谅。',
    'wxagent_pay_fail_msg_new'  => '由于%s,您的账户于%s月%s日%s微信免密支付扣款失败，请您到“滴滴会员—我的行程”中支付车费，给您带来的不便请见谅。',

    'aliagent_pay_fail_msg'  => '由于%s,您的账户于%s月%s日%s支付宝免密支付扣款失败，请您到“滴滴会员—我的行程”中支付车费，给您带来的不便请见谅。',

    'raptor_super_task_notify_msg'            => '您有一个超级任务，抢单就奖励%d元，完成订单后在%s内再完成1单，还有更丰厚的奖励！活动时间为%s', // 滴滴播报
    'raptor_super_task_finish_order_msg'      => '完成超级任务，在%s内再完成一单奖励%d元！快快行动吧', // 滴滴播报
    'raptor_super_task_remind_msg'            => '您还有%s来完成一个订单，完成订单后领取%d元奖励，加油！', // 滴滴播报
    'raptor_super_task_award_msg'             => '您%s的订单奖励%d元已到账，请到“我的余额”中查看 ', // 滴滴播报
    'raptor_super_task_failed_msg'            => '任务衔接失败，您可以继续抢单完成任务，完成就奖励%d元，快快行动吧！', // 滴滴播报
    'raptor_super_task_end_msg'               => '恭喜您完成了所有的超级任务，奖励稍后会到账，感谢您对滴滴专车的支持', // 滴滴播报
    'raptor_super_task_expire_msg'            => '当前超级任务已经结束，感谢您对滴滴专车的支持', // 滴滴播报
    'timely_remind_wait_msg'                  => '愿等您%s分钟', //适时提醒中愿等给司机的捎话内容

    'coupon_close_desc'             => '暂时无法使用',  //券系统关闭提示
    'quick_reg_msg'                 => '请关注公众号“滴滴专车司机”或点击链接注册滴滴专车司机。%s',  //注册短信
    'exam_pass_msg'                 => '恭喜您考试通过，我们将在24小时内审核您的资料，请耐心等待', //通过考试短信

    'b2b_flight_delay_caller'       => '乘客%s的航班%s已延误%s，您的专车%s司机%s师傅%s(%s星)会在实际到达时刻为乘客服务。', //若无实际到达时间，则不展示“将于%mmddhh:mm%到达%机场名称%”
    'b2b_flight_delay_passenger'    => '您的航班%s已延误%s，您的专车%s司机%s师傅%s(%s星)会在实际到达时刻为您服务。',
    'b2b_flight_delay_driver'       => '乘客%s的航班%s已延误%s，您可在航班实际到达时到达机场。',
    'b2b_flight_delay_time'         => '，将于%s到达%s机场',
    'b2b_flight_cancel_caller'      => '乘客%s的航班%s已取消，建议您联系您的司机%s师傅%s(%s星)沟通是否继续用车。',
    'b2b_flight_cancel_passenger'   => '您的航班%s已取消，建议您联系您的司机%s师傅%s(%s星)沟通是否继续用车。',
    'b2b_flight_cancel_driver'      => '乘客%s的接机航班%s已取消，建议您联系叫车人%s沟通是否继续用车。客服4000000777',
    'b2b_flight_depart_driver'      => '乘客%s的航班%s将于%s到达%s机场%s，请准时到达。', //若约定时间 = 实际到达时间，则不展示“与您的约定时间为%mm-ddhh:mm%”
    'b2b_flight_bespeak_time'       => '，与您的约定时间为%s',
    'b2b_flight_arrive_driver'      => '司机师傅，乘客%s的航班%s已到达%s，请联系乘客',
    'b2b_flight_back_caller'        => '乘客%s的航班%s已返航，建议您联系您的司机%s师傅%s沟通是否继续用车。',
    'b2b_flight_back_passenger'     => '您的航班%s已返航，建议您联系您的司机%s师傅 %s(%s星)沟通是否继续用车。',
    'b2b_flight_back_driver'        => '乘客%s的接机航班%s已返航，建议您联系叫车人%s沟通是否继续用车。客服4000000777',
    'b2b_flight_readyArr_caller'    => '乘客%s的航班%s已备降，建议您联系您的司机%s师傅%s(%s星)沟通是否继续用车。',
    'b2b_flight_readyArr_passenger' => '您的航班%s已备降，建议您联系您的司机%s师傅%s(%s星)沟通是否继续用车。',
    'b2b_flight_readyArr_driver'    => '乘客%s的接机航班%s已备降，建议您联系叫车人%s沟通是否继续用车。客服4000000777',

    'b2b_five_minute_before_departure_time_to_driver' => '由%s出发的订单预约时间将到，请前往接驾',
    'b2b_booking_preday_remind_to_driver'             => '明天%s的预约单有：%s，从%s到%s',
    'b2b_booking_currentday_remind_to_driver'         => '今天%s的预约单有：%s，从%s到%s',

    'fastcar_near_time'      => '最快{%s分钟}接驾',
    'no_fastcar_near_time'   => '附近暂无车可用，请稍等',
    'no_defaultcar_near_time'=> '附近暂无车可用，请稍等',
    'start_dest_distance'    => '里程%s公里',
    'start_dest_time'        => '总时长%s分钟',

    'must_comment_suggest'            => '完成评价，获得滴滴礼包！',
    'comment_suggest_first_passenger' => '您的任何评价都会被严格匿名',
    'comment_suggest_old_passenger'   => '完成匿名评价，获得滴滴礼包',
    'card_pay_ext_msg'                => '出行卡不能抵扣高速费、过桥费、停车费等额外费用',
    'unfinish_orders_msg'             => '您当前有%s个未完成订单',

    'assign_reject_msg' => array(
        array('id'=>1, 'content'=>'乘客距离我太远'),
        array('id'=> 2, 'content'=>'路线太拥堵'),
        array('id'=> 3, 'content'=>'终点偏远或不熟悉'),
        array('id'=> 4, 'content'=>'没能及时接单'),
    ),
    'estimate_no_login'            => '登录后优惠可见',
    'bindId_has_order_not_pay_msg' => '您的%s账号有未支付的订单，请先完成支付',
    'trans_title'                  => array(
        '1' => '下次提现的开放日期为%s',
        '2' => '每周二开放提现功能哦',
    ),
    'withdraw_title' => array(
        '1' => '本周收入%s可提现，请留余额供72小时后代充值​',
        '2' => '本周收入下周提现，请留余额供72小时后代充值',
    ),
    'passenger_ace_finish_order_sms'=> '尊敬的乘客，非常感谢您乘坐滴滴出行豪华专车。为保证你的出行体验，若您对我们的服务有任何建议或反馈，请随时拨打滴滴专车贵宾专线：4000111800，我们将竭诚为您服务。期待您的下次出行。',

    'bindId_has_order_not_pay_msg' => '您的%s账号有未支付的订单，请先完成支付',

    'flight_abnormal_order_ready_delay' => array(
        'passenger'    => '尊敬的乘客，您乘坐的航班%s延误，预计%s起飞，%s到达 ，滴滴会免费等待，请和滴滴司机%s提前联系，确保准时接到您',
        'driver'       => '司机师傅，您%s的接机订单%s航班延误，预计%s起飞，%s到达，请和乘客%s保持联系，准时接驾',
        'broadcast'    => '司机师傅，您%s的接机订单%s航班延误，预计%s起飞，%s到达，请和乘客%s保持联系，准时接驾',
    ),
    'flight_abnormal_order_real_delay' => array(
        'driver'    => '司机师傅，您%s的接机订单，飞机将于%s起飞，预计%s达到机场，请准时到达接机，乘客手机%s',
        'passenger' => '尊敬的乘客，您乘坐的航班%s将于%s起飞，预计%s到达 ，您的滴滴司机%s将准时到达接驾。',
        'broadcast' => '司机师傅，您%s的接机订单，飞机将于%s起飞，预计%s达到机场，请准时到达接机，乘客手机%s',
    ),

    'flight_abnormal_order_flying_delay' => array(
        'driver'    => '司机师傅，您%s的接机订单，飞机已起飞，预计延误至%s达到机场，请准时到达接机，乘客手机%s',
        'passenger' => '',
        'broadcast' => '您%s的接机订单，飞机已起飞，预计延误至%s达到机场，请准时到达接机，乘客手机%s',
    ),
    'flight_abnormal_order_flying' => array(
        'driver'    => '司机师傅，您%s的接机订单，飞机已起飞，预计%s达到机场，请准时到达接机，乘客手机%s',
        'passenger' => '',
        'broadcast' => '您%s的接机订单，飞机已起飞，预计%s达到机场，请准时到达接机，乘客手机%s',
    ),

    'flight_abnormal_order_cancel' => array(
        'driver'    => '司机师傅，您%s的接机订单航班%s已取消，请及时和乘客沟通，乘客手机%s',
        'passenger' => '尊敬的乘客，您乘坐的航班%s已取消，请及时取消或更改订单，并和您的滴滴司机%s联系',
        'broadcast' => '您%s的接机订单航班%s已取消，请及时和乘客沟通，乘客手机%s',
    ),

    'flight_abnormal_order_change' => array(
        'driver'    => '司机师傅，您%s的接机订单航班%s已改降至%s，请及时和乘客沟通，乘客手机%s',
        'passenger' => '',
        'broadcast' => '司机师傅，您%s的接机订单航班%s已改降至%s，请及时和乘客沟通，乘客手机%s',
    ),

    'flight_abnormal_order_back' => array(
        'driver'    => '司机师傅，您%s的接机订单航班%s已返航至%s，请及时和乘客沟通，乘客手机%s',
        'passenger' => '',
        'broadcast' => '您%s的接机订单航班%s已返航至%s，请及时和乘客沟通，乘客手机%s',
    ),
    'carpool_salse_reward'=> array(
        'sales_coupon_msg' => '恭喜您成功拼到拼友，小滴赠您10元拼车券，可于本次行程使用',
        'cooperate_msg'    => '桔色星期一，0元拼快车。小滴赠送您价值38元的褚橙10只，点击 http://benlai.com/yeU3um 领取。回复TD退订',
        'success_reward'   => '有拼友加入行程啦！TA送你一个「拼成红包」，可抵扣本次车费，赶紧打开APP领取吧!',
    ),

    //司机端首页四个运营位配置文案
    'driver_index_menu_bussiness'  => array('在线:%0.1f小时', '接单:%d', '流水: %0.2f元', '成交率: %s'),
    'driver_to_passenger_other_fee'=> array('msg'=> '您的行程包含%s如不符，可到我的行程，投诉申请补偿',
        'title'                                  => '滴滴出行',
        'highway_fee'                            => '高速费%0.2f元，',
        'bridge_fee'                             => '路桥费%0.2f元，',
        'park_fee'                               => '停车费%0.2f元，',
        'other_fee'                              => '其他费用%0.2f元，',
    ),
    'pre_pay_remind_android' => '您的当前版本过低，请到应用商店下载最新版本后重试',
    'pre_pay_remind_ios'     => '您的当前版本过低，请到App Store下载最新版本后重试',

    /* 新政 */
    'region_info' => array(
        'title'     => '同时匹配顺路司机',
        'sub_title' => '(价格更低 不可开发票)',
    ),
    'region_info_uber' => array(
        'title'          => '同时匹配顺路司机',
        'sub_title'      => '价格更低',
        'title_negative' => '不发送给顺路司机',
        'title_question' => '是否发给顺路司机?',
    ),
    'airport_recommend_poi_title'    => '确认您的位置',
    'airport_recommend_poi_subtitle' => '将自动指定最优上车地点',
    'sms_caller_booking_success'     => array(
        'passenger' => '您好，用户（%s）已为您预订%s月%s日%s出行服务。从%s到%s，行程开始前%d分钟系统将准时为您发送司机信息，之后司机会给您致电确认行程。该订单由下单人支付，若有疑问，可联系下单人。',
    ),
    'sms_caller_booking_cancel' => array(
        'passenger' => '您好，用户（%s）取消了为您代叫的%s月%s日%s从%s到%s的出行服务。若有疑问，可联系下单人。',
    ),
);

return $config;
