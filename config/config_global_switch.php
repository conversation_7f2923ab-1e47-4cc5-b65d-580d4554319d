<?php

/***************************************************************************
 *
 * Copyright (c) 2014 xiaojukeji.com, Inc. All Rights Reserved
 * @package : application/controllers/passenger/
 * <AUTHOR> <EMAIL>
 * @desc    : api服务开关控制, 收敛所有服务的开关,
 *              包含所有降级预案的描述，
 *              OP使用脚本根据描述在线上生成新的配置文件
 * @date    : 2015/01/15
 *
 **************************************************************************/
$config['global_switch'] = array(
    // 支付开关
    'pay_switch'                                      => array(
        'open'  => 1, // 0：关闭 1：开启
        //降级预案
        'level' => array(
            'G-1' => array(
                'open' => 0,
            ),
        ),
    ),
    // 券系统开关
    'coupon_switch'                                   => array(
        'open'  => 1, // 0：关闭 1：开启
        //降级预案
        'level' => array(
            'G-2' => array(
                'open' => 0,
            ),
        ),
    ),

    // 发单前导流开关
    'guidance_switch'                                 => array(
        'open'  => 1, // 0：关闭 1：开启
        //降级预案
        'level' => array(
            'A-1' => array(
                'open' => 0, //执行A级预案时，把open开关置为 0
            ),
            'B-1' => array(
                'open' => 0, //执行B级预案时，把open开关置为 0
            ),
            'C-1' => array(
                'open' => 0, //执行C级预案时，把open开关置为 0
            ),
            'D-1' => array(
                'open' => 0, //执行D级预案时，把open开关置为 0
            ),
        ),
    ),

    // pGetWaitFlag接口开关
    // 出租车呼叫等待时是否导流到其他产品线
    'wait_switch'                                     => array(
        'open'  => 1, // 0：关闭 1：开启
        //降级预案
        'level' => array(
            'A-1' => array(
                'open' => 0, //执行A级预案时，把open开关置为 0
            ),
            'B-1' => array(
                'open' => 0, //执行B级预案时，把open开关置为 0
            ),
            'C-1' => array(
                'open' => 0, //执行C级预案时，把open开关置为 0
            ),
            'D-1' => array(
                'open' => 0, //执行D级预案时，把open开关置为 0
            ),
        ),
    ),

    // 调用公共服务系统获取乘客信息
    'common_switch'                                   => array(
        'open'  => 1, // 0：关闭 1：开启
        //降级预案
        'level' => array(
            'A-1' => array(
                'open' => 0, //执行A级预案时，把open开关置为 0
            ),
            'B-1' => array(
                'open' => 0, //执行B级预案时，把open开关置为 0
            ),
            'C-1' => array(
                'open' => 0, //执行C级预案时，把open开关置为 0
            ),
            'D-1' => array(
                'open' => 0, //执行D级预案时，把open开关置为 0
            ),
        ),
    ),

    // 调用实时标签系统获取高价值、活跃用户标签，从lizang提供的标签系统获取真是的标签信息，实时计算
    'tag_realtime_switch'                             => array(
        'open'  => 1, // 0：关闭 1：开启
        //降级预案
        'level' => array(
            'A-1' => array(
                'open' => 0, //执行A级预案时，把open开关置为 0
            ),
            'B-1' => array(
                'open' => 0, //执行B级预案时，把open开关置为 0
            ),
            'C-1' => array(
                'open' => 0, //执行C级预案时，把open开关置为 0
            ),
            'D-1' => array(
                'open' => 0, //执行D级预案时，把open开关置为 0
            ),
        ),
    ),

    // pGetCancelFlag接口开关
    // 出租车呼叫取消时是否导流到其他产品线
    'cancel_switch'                                   => array(
        'open'  => 1, // 0：关闭 1：开启
        //降级预案
        'level' => array(
            'A-1' => array(
                'open' => 0, //执行A级预案时，把open开关置为 0
            ),
            'B-1' => array(
                'open' => 0, //执行B级预案时，把open开关置为 0
            ),
            'C-1' => array(
                'open' => 0, //执行C级预案时，把open开关置为 0
            ),
            'D-1' => array(
                'open' => 0, //执行D级预案时，把open开关置为 0
            ),
        ),
    ),

    // pGetWaitFlagTaxi接口开关
    // 专车等待时是否导流到其他产品线
    'guide_for_taxi_wait_switch'                      => array(
        'open'  => 1, // 0：关闭 1：开启
        //降级预案
        'level' => array(
            'A-1' => array(
                'open' => 0, //执行A级预案时，把open开关置为 0
            ),
            'B-1' => array(
                'open' => 0, //执行B级预案时，把open开关置为 0
            ),
            'C-1' => array(
                'open' => 0, //执行C级预案时，把open开关置为 0
            ),
            'D-1' => array(
                'open' => 0, //执行D级预案时，把open开关置为 0
            ),
        ),
    ),

    //
    // 专车取消时是否导流到其他产品线
    'guide_for_taxi_cancel_switch'                    => array(
        'open' => 1, // 0：关闭 1：开启
    ),
    // 快车车取消时是否导流到其他产品线
    'guide_fast_car_cancel_switch'                    => array(
        'open' => 1, // 0：关闭 1：开启
    ),
    // 快车等待时是否导流到其他产品线
    'guide_fast_car_wait_switch'                      => array(
        'open'  => 1, // 0：关闭 1：开启
        //降级预案
        'level' => array(
            'A-1' => array(
                'open' => 0, //执行A级预案时，把open开关置为 0
            ),
            'B-1' => array(
                'open' => 0, //执行B级预案时，把open开关置为 0
            ),
            'C-1' => array(
                'open' => 0, //执行C级预案时，把open开关置为 0
            ),
            'D-1' => array(
                'open' => 0, //执行D级预案时，把open开关置为 0
            ),
        ),
    ),
    //等待是否允许其他产品线导流到快车开关
    'guide_to_fast_car_wait_switch'                   => array(
        'open'  => 1, // 0：不允许 1：允许
        //降级预案
        'level' => array(
            'A-1' => array(
                'open' => 0, //执行A级预案时，把open开关置为 0
            ),
            'B-1' => array(
                'open' => 0, //执行B级预案时，把open开关置为 0
            ),
            'C-1' => array(
                'open' => 0, //执行C级预案时，把open开关置为 0
            ),
            'D-1' => array(
                'open' => 0, //执行D级预案时，把open开关置为 0
            ),
        ),
    ),
    //取消是否允许其他产品线导流到快车开关
    'guide_to_fast_car_cancel_switch'                 => array(
        'open'  => 1, // 0：不允许 1：允许
        //降级预案
        'level' => array(
            'A-1' => array(
                'open' => 0,
            ),
        ),
    ),
    //乘客周边司机开关
    'near_drivers_switch'                             => array(
        'open'  => 1, //1正常返回；2返回20~30（目前50~80）位司机信息；3返回坐标数据；4返回空数据；
        //降级预案
        'level' => array(
            'B-1' => array(
                'open' => 3,
            ),
            'B-2' => array(
                'open' => 4,
            ),
            'B-3' => array(
                'open' => 4,
            ),
            'C-1' => array(
                'open' => 3,
            ),
            'D-1' => array(
                'open' => 3,
            ),
        ),
    ),

    //热力图开关
    'driver_hot_map_switch'                           => array(
        'open'  => 1,   // 0：关闭 1：开启
        //降级预案
        'level' => array(
            'B-1' => array(
                'open' => 0,
            ),
            'B-2' => array(
                'open' => 0,
            ),
            'B-3' => array(
                'open' => 0,
            ),
            'C-1' => array(
                'open' => 0,
            ),
            'D-1' => array(
                'open' => 0,
            ),
        ),
    ),

    //乘客passport校验开关
    'passport_switch'                                 => array(
        'open'  => 1, //0使用发单时写入redis缓存乘客数据；1使用passport返回乘客数据；
        //降级预案
        'level' => array(
            'E-1' => array(
                'open' => 0,
            ),
        ),
    ),

    // 预估价时soso地图限额情况调用开关
    'sosomap_getpositioninfo_switch'                  => array(
        'open'  => 1, // 0：关闭 1：开启
        //降级预案
        'level' => array(
            'F-2' => array(
                'open' => 0,
            ),
        ),
    ),

    // 动态调价开关
    'dynamic_price_switch'                            => array(
        'open'                    => 1, // 0：关闭 1：开启
        'area_list'               => array(//小流量，open=0时有效
            1  => array(1 => 1, 3 => 1), //北京专车获取动态调价、快车展示动态调价
            2  => array(1 => 0, 3 => 0), //深圳专车不获取动态调价、快车不展示动态调价
            4  => array(1 => 0, 3 => 0), //上海专车获取动态调价、快车不展示动态调价
            5  => array(1 => 0, 3 => 0), //杭州专车不获取动态调价、快车不展示动态调价
            7  => array(1 => 0, 3 => 0), //天津专车不获取动态调价、快车不展示动态调价
            11 => array(1 => 0, 3 => 0), //南京专车不获取动态调价、快车不展示动态调价
            32 => array(1 => 0, 3 => 0), //厦门专车不获取动态调价、快车不展示动态调价
            34 => array(1 => 0, 3 => 0), //福州专车不获取动态调价、快车不展示动态调价
        ),
        'open_dynamic_price'      => array(//全流量，open=1时有效，所有城市可以展示接驾时间
            1 => array(1 => 1, 3 => 1), //北京专车获取动态调价、快车展示动态调价
        ),
        'open_move_dynamic_price' => array(//动态调价迁移
            'flag'      => 1, //0:关闭 1:开启
            'area_list' => array(),
        ),
        //降级预案
        'level'                   => array(
            'F-1' => array(
                'open'      => 0,
                'area_list' => array(),
            ),
        ),
    ),

    // 目标用户开关
    'target_user_switch'                              => array(
        'open' => 1, // 0：关闭 1：开启
    ),

    // 号码保护开关
    'virtual_phone_switch'                            => array(
        'open' => 1, // 0：关闭 1：开启
    ),

    // 机场、火车站 其他费用之和 > 50 是否能使用券
    'other_fee_use_voucher_switch'                    => array(
        'open'      => 1, // 0 关闭, 1 开启
        'threshold' => 40, // other_fee + highway_fee + park_fee + bridge_fee > 50 不能使用券
    ),

    // 接口是否再次访问标示开关(1是可以，0是不可以)
    // 这个开关用户app端的访问判断，第一次必然能访问
    'get_order_status_spare_switch'                   => array(
        'open' => 1, // 0 关闭, 1 开启
    ),
    //给张翔提供的查询周边司机坐标的接口
    'driversCoordinate_switch'                        => array(
        'open' => 1, //；
    ),

    //企业用户支付开关
    'business_pay_switch'                             => array(
        'open'       => 1, // 0 关闭, 1 开启
        'white_list' => 0,
    ),

    //券反作弊开关
    'coupon_antispam_switch'                          => array(
        'open' => 1,  //0 关闭, 1开启
    ),

    //最低消费
    'limit_fee'                                       => array(
        'open'       => 1, // 0：关闭 1：开启
        'white_list' => 1,
    ),

    //低版本的用券开关(为了支持最低消费, 低于3.7的后续可能禁止用券)
    'disable_app_old_version_use_coupon_switch'       => array(
        'open' => 0,
    ),

    //反作弊频率控制开关
    'antispam_frequency_control_switch'               => array(
        'open' => 1,
    ),

    //反作弊真身黑名单与敏感词检查开关
    'antispam_bindId_and_word_blackList_check_switch' => array(
        'open' => 1,
    ),
    //司机地理位置开关(0 mongo、1 lbs)
    'driver_location_switch'                          => array(
        'open'  => 1,
        //降级预案
        'level' => array(
            'H-1' => array(
                'open' => 0,
            ),
        ),
    ),

    //eta服务地理位置信息开关(0 腾讯、1 eta)
    'eta_positioninfo_switch'                         => array(
        'open'  => 1,
        //降级预案
        'level' => array(
            'H-1' => array(
                'open' => 0,
            ),
        ),
    ),

    //司机实时计价切换配置
    'driver_pricing_model_switch'                     => array(
        'pricing_model' => array(
            'pricing_mode'   => 1,   //上传方式 1:push 2:http
            'location_rate'  => 1,   //定位频率 秒
            'upload_rate'    => 10,   //上传频率 秒
            'http_min_rate'  => 2,   //http最小上报频率 秒
            'push_min_rate'  => 1,   //push最小上报频率
            'default_points' => 10,  //默认上报点数(司机端不用)
            'max_points'     => 50,      //最大上报点数
            'threshold'      => '1000,500,100',  //阈值
            'keep_ratio'     => '2:1,5:3,5:4',   //留点比例
        ),
        'level'         => array(
            'J-1' => array(
                'pricing_model' => array(
                    'pricing_mode'   => 2,   //上传方式 1:push 2:http
                    'location_rate'  => 1,   //定位频率 秒
                    'upload_rate'    => 10,   //上传频率 秒
                    'http_min_rate'  => 2,   //http最小上报频率 秒
                    'push_min_rate'  => 1,   //push最小上报频率
                    'default_points' => 10,  //默认上报点数(司机端不用)
                    'max_points'     => 50,      //最大上报点数
                    'threshold'      => '1000,500,100',  //阈值
                    'keep_ratio'     => '2:1,5:3,5:4',   //留点比例
                ),
            ),
            'J-2' => array(
                'pricing_model' => array(
                    'pricing_mode'   => 1,   //上传方式 1:push 2:http
                    'location_rate'  => 1,   //定位频率 秒
                    'upload_rate'    => 10,   //上传频率 秒
                    'http_min_rate'  => 2,   //http最小上报频率 秒
                    'push_min_rate'  => 1,   //push最小上报频率
                    'default_points' => 10,  //默认上报点数(司机端不用)
                    'max_points'     => 100,      //最大上报点数
                    'threshold'      => '1000,500,100',  //阈值
                    'keep_ratio'     => '2:1,5:3,5:4',   //留点比例
                ),
            ),
            'J-3' => array(
                'pricing_model' => array(
                    'pricing_mode'   => 2,   //上传方式 1:push 2:http
                    'location_rate'  => 1,   //定位频率 秒
                    'upload_rate'    => 10,   //上传频率 秒
                    'http_min_rate'  => 2,   //http最小上报频率 秒
                    'push_min_rate'  => 1,   //push最小上报频率
                    'default_points' => 10,  //默认上报点数(司机端不用)
                    'max_points'     => 100,      //最大上报点数
                    'threshold'      => '1000,500,100',  //阈值
                    'keep_ratio'     => '2:1,5:3,5:4',   //留点比例
                ),
            ),
            'J-4' => array(
                'pricing_model' => array(
                    'pricing_mode'   => 1,   //上传方式 1:push 2:http
                    'location_rate'  => 1,   //定位频率 秒
                    'upload_rate'    => 15,   //上传频率 秒
                    'http_min_rate'  => 2,   //http最小上报频率 秒
                    'push_min_rate'  => 1,   //push最小上报频率
                    'default_points' => 10,  //默认上报点数(司机端不用)
                    'max_points'     => 50,      //最大上报点数
                    'threshold'      => '1000,500,100',  //阈值
                    'keep_ratio'     => '2:1,5:3,5:4',   //留点比例
                ),
            ),
            'J-5' => array(
                'pricing_model' => array(
                    'pricing_mode'   => 1,   //上传方式 1:push 2:http
                    'location_rate'  => 1,   //定位频率 秒
                    'upload_rate'    => 15,   //上传频率 秒
                    'http_min_rate'  => 2,   //http最小上报频率 秒
                    'push_min_rate'  => 1,   //push最小上报频率
                    'default_points' => 10,  //默认上报点数(司机端不用)
                    'max_points'     => 100,      //最大上报点数
                    'threshold'      => '1000,500,100',  //阈值
                    'keep_ratio'     => '2:1,5:3,5:4',   //留点比例
                ),
            ),
        ),
    ),

    //丢弃订单开关, 丢弃后不会播送订单，乘客无感知
    'give_up_order_switch'                            => array(
        'open'  => 0,
        'ratio' => 0,
        //降级预案
        'level' => array(
            'C-1'  => array(
                'open'  => 1, //降级开关打开
                'ratio' => 10, //丢弃10%的订单
            ),
            'C-2'  => array(
                'open'  => 1,
                'ratio' => 20,
            ),
            'C-3'  => array(
                'open'  => 1,
                'ratio' => 30,
            ),
            'C-4'  => array(
                'open'  => 1, //降级开关打开
                'ratio' => 40, //丢弃40%的订单
            ),
            'C-5'  => array(
                'open'  => 1, //降级开关打开
                'ratio' => 50, //丢弃50%的订单
            ),
            'C-6'  => array(
                'open'  => 1, //降级开关打开
                'ratio' => 60, //丢弃60%的订单
            ),
            'C-7'  => array(
                'open'  => 1, //降级开关打开
                'ratio' => 70, //丢弃70%的订单
            ),
            'C-8'  => array(
                'open'  => 1, //降级开关打开
                'ratio' => 80, //丢弃80%的订单
            ),
            'C-9'  => array(
                'open'  => 1, //降级开关打开
                'ratio' => 90, //丢弃90%的订单
            ),
            'C-10' => array(
                'open'  => 1, //降级开关打开
                'ratio' => 100, //丢弃100%的订单
            ),
        ),
    ),

    //丢弃订单开关, 丢弃后不会播送订单，乘客无感知
    'entrance_switch'                                 => array(
        'open'  => 0,
        //降级预案
        'level' => array(
            'D-1' => array(
                'open' => 1, //专车不能发单
            ),
            'D-2' => array(
                'open' => 2, //快车不能发单
            ),
            'D-3' => array(
                'open' => 3, //专车、快车不能发单
            ),
        ),
    ),
    //乘客端轮询接口 时间间隔控制
    'passenger_time_interval_switch'                  => array(
        'open'           => 0,
        'version_offset' => 0, //版本偏移量
        'time_interval'  => array(),
        //降级预案
        'level'          => array(
            'B-2' => array(
                'open'           => 1, //开启预案
                'version_offset' => 200,
                'time_interval'  => array(
                    'p_pay_status_req'              => '6', // pPayStatus轮询频率(单位秒)
                    'p_order_get_req'               => '10', // pGetOrderStatus轮询频率(单位秒)
                    'p_order_detail_req'            => '80', // pGetOrderDetail轮询频率(单位秒)
                    'p_get_order_status_spare'      => '20', // pGetOrderStatusSpare(单位秒)
                    'p_get_order_status_spare_open' => 0, // pGetOrderStatusSpare(0是关闭，是打开)
                ),
            ),
            'B-3' => array(
                'open'           => 1, //开启预案
                'version_offset' => 300,
                'time_interval'  => array(
                    'p_pay_status_req'              => '9', // pPayStatus轮询频率(单位秒)
                    'p_order_get_req'               => '15', // pGetOrderStatus轮询频率(单位秒)
                    'p_order_detail_req'            => '120', // pGetOrderDetail轮询频率(单位秒)
                    'p_get_order_status_spare'      => '30', // pGetOrderStatusSpare(单位秒)
                    'p_get_order_status_spare_open' => 0, // pGetOrderStatusSpare(0是关闭，是打开)
                ),
            ),
        ),
    ),

    //webapp pGetPollStatus 接口轮询间隔控制i
    'webapp_time_interval_switch'                     => array(
        'open'          => 0,
        'time_interval' => array(),
        //降级预案
        'level'         => array(
            'B-2' => array(
                'open'          => 1, //开启预案
                'time_interval' => array(
                    '1'     => 0, //待出发预约单
                    '2001'  => 0, //客服关闭 order_status=11
                    '2002'  => 0, //改派关闭 order_status=9
                    '2003'  => 0, //未能完成服务关闭 order_status=12
                    '3'     => 0, //已完成	正常订单 已完成order_status=5 and is_pay=1
                    '30001' => 6, //订单完成 未评价                    15
                    '4000'  => 6, //改派中
                    '4001'  => 20, //待接驾                         15
                    '4002'  => 6, //司机迟到
                    '4003'  => 6, //司机到达             10
                    '4004'  => 6, //乘客迟到
                    '4005'  => 6, //迟到计费
                    '4006'  => 6, ////服务中/计费中 10
                    '5001'  => 6, //正常订单待支付 order_status=5 and is_pay=0                          10
                    '5002'  => 6, //取消行程待支付 order_status=7 and pay_type=1 and is_pay=0
                    '6001'  => 0,  //取消订单无需支付 1、order_status=6 2、order_status=7 and pay_type=0
                    '6002'  => 0,  //取消订单已支付 order_status=7 and pay_type=1 and is_pay=1
                    '7'     => 14, //等待抢单            10
                    '7001'  => 6,
                    '7002'  => 6,
                    '8'     => 6, //协商状态
                ),
            ),
            'B-3' => array(
                'open'          => 1, //开启预案
                'time_interval' => array(
                    '1'     => 0, //待出发预约单
                    '2001'  => 0, //客服关闭 order_status=11
                    '2002'  => 0, //改派关闭 order_status=9
                    '2003'  => 0, //未能完成服务关闭 order_status=12
                    '3'     => 0, //已完成	正常订单 已完成order_status=5 and is_pay=1
                    '30001' => 9, //订单完成 未评价                    15
                    '4000'  => 9, //改派中
                    '4001'  => 30, //待接驾                         15
                    '4002'  => 9, //司机迟到
                    '4003'  => 9, //司机到达             10
                    '4004'  => 9, //乘客迟到
                    '4005'  => 9, //迟到计费
                    '4006'  => 9, ////服务中/计费中 10
                    '5001'  => 9, //正常订单待支付 order_status=5 and is_pay=0                          10
                    '5002'  => 9, //取消行程待支付 order_status=7 and pay_type=1 and is_pay=0
                    '6001'  => 0,  //取消订单无需支付 1、order_status=6 2、order_status=7 and pay_type=0
                    '6002'  => 0,  //取消订单已支付 order_status=7 and pay_type=1 and is_pay=1
                    '7'     => 14, //等待抢单            10
                    '7001'  => 9,
                    '7002'  => 9,
                    '8'     => 9, //协商状态
                ),
            ),
        ),
    ),

    //商业变现滴滴播报广告开关
    'commRealize_didiPush_ad'                         => array(
        'open' => 1,  //0 关闭, 1开启  //商业变现滴滴播报广告
    ),

    //券只抵扣业务费
    'coupon_deduct_business_fee'                      => array(
        'open' => 1,
    ),

    //zif小流量开关
    'observer_zif_event'                              => array(
        'open'       => 1,
        'city_list'  => array(
            '38' => '南昌',
            '40' => '唐山',
        ),
        'white_list' => array(),
        'rand'       => 0,
        'drand'      => 0,
    ),

    //创建订单时，passenger支付能力检查
    'check_pay_capacity_for_order'                    => array(
        //>>> 'open' => ${GS_PASSENGER_API_CHECK_PAY_CAPACITY_SWITCH},
        'open' => 1,
        //<<<
    ),

    //创建订单时，是否检查有running order
    'check_running_order_for_order'                   => array(
        //>>> 'open' => ${GS_PASSENGER_API_CHECK_RUNNING_ORDER_SWITCH},
        'open' => 1,
        //<<<
    ),
    //创建订单时，是否检查创建频率
    'check_frequency_for_order'                       => array(
        //>>> 'open' => ${GS_PASSENGER_API_CHECK_FREQUENCY_SWITCH},
        'open' => 1,
        //<<<
    ),

    //预估请求乘客运营和POPE降级开关
    'estimate_request_activitymis'                    => array(
        'open' => 1,
    ),

    //是否使用 carrera 发送 kafka 消息
    'kafka_proxy'                                     => array('open' => 0), //1 开启 0 关闭

    // 乘客端号码保护开关
    'passenger_virtual_phone_switch'                  => array(
        'open' => 0, // 0：关闭 1：开启
    ),

    // 乘客端预估 调用 athena 一键降级
    'passenger_estimate_athena_degrade_switch'        => array(
        //>>>  'open'    => 1,
        //>>>  'percent' => 1,
        'open'    => 0, // 0：关闭(默认0，降级关闭，则调用athena)  1：开启
        'percent' => 0, // open = 1 时，降级百分比
        //<<<
    ),
);

return $config;
