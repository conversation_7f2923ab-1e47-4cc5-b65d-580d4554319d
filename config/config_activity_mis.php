<?php

use Disf\SPL\Scheduler\Svc;

$config['timeout'] = 200; //ms
//>>> $config['estimate_url'] = 'http://${ROUTER_COMMONSHARE_IP_PORT}/foundation/commonshare/v1/passengerActivity/prePrice';
$config['estimate_url'] = 'http://************:8000/foundation/commonshare/v1/passengerActivity/prePrice';
//<<<
Svc::discoverHttpUrl(
    'disf!os-volcano-share_web',
    '/foundation/commonshare/v1/passengerActivity/prePrice',
    Svc::thenUpdate($config['estimate_url'])
);

//>>> $config['neworder_url'] = 'http://${ROUTER_COMMONSHARE_IP_PORT}/foundation/commonshare/v1/passengerActivity/preOrder';
$config['neworder_url'] = 'http://************:8000/foundation/commonshare/v1/passengerActivity/preOrder';
//<<<
Svc::discoverHttpUrl(
    'disf!os-volcano-share_web',
    '/foundation/commonshare/v1/passengerActivity/preOrder',
    Svc::thenUpdate($config['neworder_url'])
);

$config['seconds_not_response_url'] = 'http://************:8000/foundation/commonshare/v1/passengerActivity/nSecondNoResponse';
Svc::discoverHttpUrl(
    'disf!os-volcano-share_web',
    '/foundation/commonshare/v1/passengerActivity/nSecondNoResponse',
    Svc::thenUpdate($config['seconds_not_response_url'])
);

//<<<

//<<<

//<<<

//MIS活动
$config['ucmc_activity']    = 'http://ucmc.udache.com/ucmc/config/';
$config['activity_timeout'] = 200;

//POPE 配置
$config['pope_timeout'] = 200; //ms
//$config['pope_estimate_url'] = 'http://************:8000/gulfstream/popeproxy/syncapi';
$config['pope_estimate_url'] = '';
Svc::discoverHttpUrl(
    'disf!biz-gs-pope-engine',
    '/gulfstream/popeproxy/syncapi',
    Svc::thenUpdate($config['pope_estimate_url'])
);

return $config;
