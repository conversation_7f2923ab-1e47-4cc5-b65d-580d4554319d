<?php
/**
 * Core Config for Application and Component.
 *
 * Biz Config Use Other File
 */
use Nuwa\Log\Logger;
use Nuwa\Cors\CORS;

return [
    /*
     * Application 配置
     */
    'application'   => [
        'name'       => 'PreSale',

        'directory'  => APP_PATH,

        'modules'    => 'index,core,example,other,webapp',

        'dispatcher' => [
            'defaultModule'     => 'index',
            'defaultController' => 'welcome',
            'defaultAction'     => 'index',
        ],

        'baseUri'    => '',

        /*
         * 视图配置
         */
        'view'       => [
            'ext'             => 'phtml',

            /*
             * 是否使用视图、自动渲染视图
             * 根据 Controller、Action 自动寻找 view 文件，加载并渲染
             * auto_render = false, instantly_flush 和 return_response 配置不生效
             */
            'auto_render'     => false,

            /*
             * 视图 根路径，views 目录地址
             *
             * '' 使用默认值 resources/views
             */
            'path'            => '',

            /*
             * 在 autoRender = true 自动渲染后
             * instantly_flush 控制 是否直接输出渲染的数据，否则 将数据存入 Response 对象里
             */
            'instantly_flush' => false,

            /*
             * 在 auto_render = true 自动渲染后 & 关闭直接输出 instantly_flush = false，渲染的数据存储在 Response
             * return_response 决定 Response 对象是否返回给 PHP 层，true 表示返回 Response 给 PHP 层开发者，由开发者决定是否输出
             * false 表示不返回，Yaf 框架在请求结束后，自动调用 Response->response() 输出结果
             */
            'return_response' => false,
        ],
    ],

    /*
     * 下面是引导程序类名，实现 BootstrapInterface 接口，由框架自动加载、调用
     */
    'bootstrappers' => [
        \PreSale\Bootstrap\RegisterLogger::class,
        \PreSale\Bootstrap\RegisterConfig::class,
        \PreSale\Bootstrap\InitializeBiz::class,
        \PreSale\Bootstrap\HandleException::class,
        \PreSale\Bootstrap\RegisterPlugin::class,
        \PreSale\Bootstrap\RegisterRoute::class,
        \PreSale\Bootstrap\RegisterORM::class,
    ],

    /*
     * 下面是插件类名，实现 PluginInterface 接口，由框架自动注册、调用。
     */
    'plugins'       => [
        //\PreSale\Plugins\DebugPlugin::class,
        \PreSale\Plugins\CommonPlugin::class,
    ],

    // 详细配置信息参照: http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=*********
    'cors'          => [
        'servers' => [
            [
                'schemes'          => [CORS::SCHEME_HTTP, CORS::SCHEME_HTTPS],
                'hosts'            => [
                    '.udache.com',
                    '.xiaojukeji.com',
                    '.diditaxi.com.cn',
                    '.didichuxing.com',
                    '.99taxis.mobi',
                    '.kuaidadi.com',
                    '.didiglobal.com',
                    'didi.cn',
                    '.didi.cn',
                    '.hongyibo.com.cn',
                ],
                'ports'            => [CORS::PORT_HTTP, CORS::PORT_HTTPS],
                'locations'        => [
                    '/gulfstream/pre-sale/v1/core/pNewOrder',
                    '/gulfstream/pre-sale/v1/core/pEstimatePrice',
                    '/gulfstream/pre-sale/v1/core/pEstimatePriceCustom',
                    '/gulfstream/pre-sale/v1/core/pMultiEstimatePrice',
                    '/gulfstream/pre-sale/v1/core/pMultiEstimatePriceV3',
                    '/gulfstream/pre-sale/v1/other/pCheckBookingAbility',
                    '/gulfstream/pre-sale/v1/other/pGetComboInfo',
                    '/gulfstream/pre-sale/v1/other/pGetConfig',
                    '/gulfstream/pre-sale/v1/other/pGetFlag',
                    '/gulfstream/pre-sale/v1/other/pGetStationStatus',
                    '/gulfstream/pre-sale/v1/other/pMoreServices',
                    '/gulfstream/pre-sale/v1/other/pPrepayOrderAssignDispatch',
                    '/gulfstream/pre-sale/v1/other/pSelectItem',
                    '/gulfstream/pre-sale/v1/other/pGetWanliuInfo',
                    '/gulfstream/pre-sale/v1/other/pGetSceneProducts',
                    '/gulfstream/pre-sale/v1/other/pGetEstimateFeeDetail',
                    '/gulfstream/pre-sale/v1/other/pGetEstimateFeeDetailForAnycar',
                    '/gulfstream/pre-sale/v1/other/pGetDynamicPriceDetail',
                    '/gulfstream/pre-sale/v1/other/pGetDynamicPriceDetailV2',
                    '/gulfstream/pre-sale/v1/webapp/pGetFlag',
                    '/gulfstream/pre-sale/v1/webapp/pGetLite',
                    '/gulfstream/pre-sale/v1/other/pGetUnioneInfoFee',
                    '/gulfstream/pre-sale/v1/other/pGetActivityInfo',
                    '/gulfstream/pre-sale/v1/other/pPreCancelOrder',
                    '/gulfstream/pre-sale/v1/other/pGetDynamicConfig',
                    '/gulfstream/pre-sale/v1/other/pSideEstimate',
                    '/gulfstream/pre-sale/v1/other/pGetMultiSpecialRule',
                    '/gulfstream/pre-sale/v1/other/pGetTailorService',
                    '/gulfstream/pre-sale/v1/other/pGetTailorServiceV2',
                    '/gulfstream/pre-sale/v1/other/pSubmitTailorService',
                    '/gulfstream/pre-sale/v1/other/pCheckRiderInfo',
                    '/gulfstream/pre-sale/v1/other/pCheckInterScan',
                    '/gulfstream/pre-sale/v1/other/pConfirmInterScan',
                    '/gulfstream/pre-sale/v1/other/pGetBannerInfo',
                    '/gulfstream/pre-sale/v1/other/pReceiveCoupon',
                    '/gulfstream/pre-sale/v1/other/pGetPriceRule',
                    '/gulfstream/pre-sale/v1/other/pGetFullPageInfo',
                    '/gulfstream/pre-sale/v1/other/pGetPriceRuleListNew',
                    '/gulfstream/pre-sale/v1/other/pGetPriceRuleV2',
                    '/gulfstream/pre-sale/v1/other/pGetSpaciousCarPriceRule',
                    '/gulfstream/pre-sale/v1/other/pGetSpringRedPacketInfo',
                    '/gulfstream/pre-sale/v1/other/pGetFenceList',
                    '/gulfstream/pre-sale/v1/other/pGetInterceptCard',
                    '/gulfstream/pre-sale/v1/other/pSetConfirm',
                    '/gulfstream/pre-sale/v1/other/pGetTimeDiscountDetail',
                    '/gulfstream/pre-sale/v1/other/pGetEstimateCouponRule',
                    '/gulfstream/pre-sale/v1/other/pPageGuide',
                    '/gulfstream/pre-sale/v1/other/pGetTaxiPriceDetail',
                    '/gulfstream/pre-sale/v1/other/pGetCarCheckRulePage',
                    '/gulfstream/pre-sale/v1/other/pUpdateCarCheckList',
                    '/gulfstream/pre-sale/v1/other/pUpdateCarCheckRule',
                    '/gulfstream/pre-sale/v1/other/pCheckLuxuryPreferPageSensitiveWord',
                ],
                'response_headers' => [
                    CORS::ACAO => '',
                    CORS::ACAH => 'Cityid, Productid, didi-header-hint-content, didi-header-rid, content-type, xregionkeyname, xregionkeyvalue',
                ],
            ],
        ],
    ],

    'log'           => [
        [
            'enableMasking' => true,
            'file'          => LOG_PATH.'didi.log',
            'writeLevels'   => [Logger::NOTICE, Logger::INFO, Logger::DEBUG, ],
        ],
        [
            'enableMasking' => true,
            'file'          => LOG_PATH.'didi.log.wf',
            'writeLevels'   => [Logger::WARN, Logger::ERROR, ],
        ],
    ],

    'config'        => [
        'config' => [
            PROJ_PATH.'config',
            BIZ_CONFIG.'config',
            BIZ_LIB_CIPATH.'config',
        ],
        'text'   => [
            BIZ_CONFIG.'config/text',
        ],
    ],
];
