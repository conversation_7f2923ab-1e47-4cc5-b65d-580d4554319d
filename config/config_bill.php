<?php

use Disf\SPL\Scheduler\Svc;

//bill系统--预估价接口地址
//<<<

//bill系统--开始计价接口地址
//<<<

//>>> $config['bill_finish_order_url'] = 'http://${ROUTER_GS_PLUTUS_IP_PORT}/gulfstream/plutus/finishBill';
$config['bill_finish_order_url'] = 'http://**************:8000/gulfstream/plutus/finishBill';
//<<<
Svc::discoverHttpUrl(
    'disf!commonplat-gs-plutus',
    '/gulfstream/plutus/finishBill',
    Svc::thenUpdate($config['bill_finish_order_url'])
);

//<<<

//>>> $config['get_bill_strategies_url'] = 'http://${ROUTER_GS_PLUTUS_IP_PORT}/gulfstream/plutus/getStrategies';
$config['get_bill_strategies_url'] = 'http://**************:8000/gulfstream/plutus/getStrategies';
//<<<
Svc::discoverHttpUrl(
    'disf!commonplat-gs-plutus',
    '/gulfstream/plutus/getStrategies',
    Svc::thenUpdate($config['get_bill_strategies_url'])
);

//>>> $config['is_driver_passenger_separated'] = 'http://${ROUTER_GS_PLUTUS_IP_PORT}/gulfstream/plutus/catalog/isDriverPassengerSeparated';
$config['is_driver_passenger_separated'] = 'http://**************:8000/gulfstream/plutus/catalog/isDriverPassengerSeparated';
//<<<
Svc::discoverHttpUrl(
    'disf!commonplat-gs-plutus',
    '/gulfstream/plutus/catalog/isDriverPassengerSeparated',
    Svc::thenUpdate($config['is_driver_passenger_separated'])
);

$config['bill_finish_order_timeout'] = 2000; // 2000ms
$config['timeout']                   = 2000;

return $config; // 1000ms
