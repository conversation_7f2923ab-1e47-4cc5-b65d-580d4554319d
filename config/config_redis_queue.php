<?php

/**
 * redis queue服务器配置.
 */
$config['config_redis_queue']=array(
        'queue_default'=> array(
                //>>> 'host'=>'${GS_REDIS_QUEUE_DEFAULT_IP}',
                'host'=> '*************',
                //<<<
                //>>> 'port'=>'${GS_REDIS_QUEUE_DEFAULT_PORT}',
                'port'=> '6379',
                //<<<
                'limit'=> 0,
        ),
        'queue_position'=> array(
                //>>> 'host'=>'${GS_REDIS_QUEUE_POSITION_IP}',
                'host'=> '*************',
                //<<<
                //>>> 'port'=>'${GS_REDIS_QUEUE_POSITION_PORT}',
                'port'=> '6379',
                //<<<
                'limit'=> 50000,
        ),
        'queue_logs'=> array(
                //>>> 'host'=>'${GS_REDIS_QUEUE_LOGS_IP}',
                'host'=> '*************',
                //<<<
                //>>> 'port'=>'${GS_REDIS_QUEUE_LOGS_PORT}',
                'port'=> '6379',
                //<<<
                'limit'=> 0,
        ),
        'queue_push'=> array(
                //>>> 'host'=>'${GS_REDIS_QUEUE_PUSH_IP}',
                'host'=> '*************',
                //<<<
                //>>> 'port'=>'${GS_REDIS_QUEUE_PUSH_PORT}',
                'port'=> '6379',
                //<<<
                'limit'=> 0,
        ),
        'queue_sms'=> array(
                //>>> 'host'=>'${GS_REDIS_QUEUE_SMS_IP}',
                'host'=> '*************',
                //<<<
                //>>> 'port'=>'${GS_REDIS_QUEUE_SMS_PORT}',
                'port'=> '6379',
                //<<<
                'limit'=> 0,
        ),
        'queue_order'=> array(
                //>>> 'host'=>'${GS_REDIS_QUEUE_ORDER_IP}',
                'host'=> '*************',
                //<<<
                //>>> 'port'=>'${GS_REDIS_QUEUE_ORDER_PORT}',
                'port'=> '6379',
                //<<<
                'limit'=> 0,
        ),
        'queue_pay'=> array(
                //>>> 'host'=>'${GS_REDIS_QUEUE_PAY_IP}',
                'host'=> '*************',
                //<<<
                //>>> 'port'=>'${GS_REDIS_QUEUE_PAY_PORT}',
                'port'=> '6379',
                //<<<
                'limit'=> 0,
        ),
        /*
         * 鱼鹰专用
         */
        'queue_osprey'=> array(
                //>>> 'host'=>'${GS_REDIS_QUEUE_OSPREY_IP}',
                'host'=> '*************',
                //<<<
                //>>> 'port'=>'${GS_REDIS_QUEUE_OSPREY_PORT}',
                'port'=> '6379',
                //<<<
                'limit'=> 0,
        ),
);
/*
 * redis queue服务器配置，队列服务分配的队列id
 */
$config['config_redis_queue_id']=array(
          'queue_id' => array(
                'queue_api_default'          => '800700',
                'queue_api_order_passenger'  => '800500',
                'queue_api_order_driver'     => '800200',
                'queue_api_b2b'              => '801200',
                'queue_api_webapp'           => '801300',
                'queue_api_openapi'          => '801400',
                  'queue_api_new_order'      => '801500',
                  'queue_api_spam_report'    => '801600',
                  'queue_api_driver_order'   => '801800',
                'queue_gs_api_resend_order'  => '801900',
                'queue_api_order_bill'       => '802100',
                'queue_api_carpool_coupon'   => '802200',
                  'queue_api_recovery'       => '802300',
                'queue_chuanliu_ordercancel' => '802500',
          ),
);

return $config;
