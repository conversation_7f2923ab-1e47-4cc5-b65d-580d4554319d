<?php

use BizLib\Config as NuwaConfig;
use BizLib\Constants\OrderSystem;

//请将索引自增使用  text为显示文案  recall为是否做重新叫车提示
define('SPECIALCAR_CANCEL_REASON', 'specialcar_cancel_reason');
define('FIRSTCLASS_CANCEL_REASON', 'first_class_cancel_reason');
define('FASTCAR_CANCEL_REASON', 'fastcar_cancel_reason');
define('UNIONE_CANCEL_REASON', 'unione_cancel_reason');
define('BRAZIL_TAXI_CANCEL_REASON', 'brazil_taxi_cancel_reason');
define('MEXICO_FASTCAR_CANCEL_REASON', 'mexico_fastcar_cancel_reason');
define('CHARTERED_CAR_CANCEL_REASON', 'chartered_car_cancel_reason');
define('DUE_TO_DISPATCH', 'due_to_dispatch');
define('DUE_TO_DRIVER', 'due_to_driver');
define('DUE_TO_PASSENGER', 'due_to_passenger');
define('DUE_TO_OTHER', 'due_to_other');
$config['cancelReasonList'] = NuwaConfig::text('config_cancel_reason', 'cancelReasonList');

//前端按下列数组中排列顺序显示，若调整显示顺序，请调整下列数组，上面的列表请自增使用
$config['specialcar_cancel_reason'] = array(
    '101', '103', '104', '105', '108', '109', '110', '111', '112', '113',
);
$config['fastcar_normal_cancel_reason'] = array(
    '101', '102', '103', '104', '105', '108', '109', '110', '111', '112', '113',
);
$config['fastcar_carpool_cancel_reason'] = array(
    '101', '102', '119', '120', '121', '106', '122', '123', '124', '125', '126', '127', '128',
);
$config['fastcar_station_carpool_cancel_reason'] = array(
    '101', '102', '119', '120', '121', '114', '115', '129', '124', '123', '125', '127', '128',
);
$config['unione_cancel_reason'] = array(
    '101', '117', '103', '104', '105', '118', '109', '110', '111', '112', '113',
);
//unione新增对"车不符"的取消原因配置
$config['unione_cancel_reason_new'] = array(
    '101', '117', '103', '104', '161', '162', '105', '118', '109', '110', '111', '113',
);
$config['nirvana_cancel_reason'] = array(
    '101', '141', '103', '104', '105', '108', '109', '110', '111', '112', '113',
);
$config['brazil_normal_cancel_reason'] = array(
    '131', '132', '133', '135', '136', '137', '138', '139', '140',
);
$config['mexico_fastcar_cancel_reason'] = array(
    '150', '151', '152', '153', '154', '155', '156', '157', '158', '159', '160',
);
$config['chartered_car_cancel_reason'] = array(
    '150', '120', '121', '129', '124', '123', '125', '127', '128',
);
$config['specialcar_not_effect_driver_deal_rate_reason'] = array('101', '103');

//mvp项目改版取消原因
$config['specialcar_cancel_reason_mvp'] = array(
    DUE_TO_DISPATCH  => array('103'),
    DUE_TO_DRIVER    => array('104', '105', '108', '109', '111', '112'),
    DUE_TO_PASSENGER => array('101'),
    DUE_TO_OTHER     => array('128'),
);
$config['first_class_cancel_reason_mvp'] = array(
    DUE_TO_DISPATCH  => array('103'),
    DUE_TO_DRIVER    => array('104', '105', '108', '109', '111', '112'),
    DUE_TO_PASSENGER => array('101', '901', '902'),
    DUE_TO_OTHER     => array('128'),
);
$config['fastcar_normal_cancel_reason_mvp'] = array(
    DUE_TO_DISPATCH  => array('163', '164'),
    DUE_TO_DRIVER    => array('105', '108', '109',  '111', '112', '165', '166', '167', ),
    DUE_TO_PASSENGER => array('101', '102', '168', '169', '170'),
    DUE_TO_OTHER     => array('128'),
);
$config['fastcar_carpool_cancel_reason_mvp'] = array(
    DUE_TO_DISPATCH  => array('119', '122'),
    DUE_TO_DRIVER    => array('121', '123', '124', '125', '126', '127'),
    DUE_TO_PASSENGER => array('101', '102', '106', '168', '169'),
    DUE_TO_OTHER     => array('128'),
);
$config['fastcar_station_carpool_cancel_reason_mvp'] = array(
    DUE_TO_DISPATCH  => array('119', '114', '115'),
    DUE_TO_DRIVER    => array('120', '121', '129', '124', '123', '125', '127'),
    DUE_TO_PASSENGER => array('101', '102', '168', '169'),
    DUE_TO_OTHER     => array('128'),
);
$config['unione_cancel_reason_mvp'] = array(
    DUE_TO_DISPATCH  => array('103'),
    DUE_TO_DRIVER    => array('104', '105', '118', '109', '110', '111', '165', '166', '167', ),
    DUE_TO_PASSENGER => array('101', '117', '168', '169', '170'),
    DUE_TO_OTHER     => array('128'),
);

$config['nirvana_cancel_reason_mvp'] = array(
    DUE_TO_DISPATCH  => array('163', '164'),
    DUE_TO_DRIVER    => array('104', '105', '108', '109', '110', '111', '112', '165', '166', '167', ),
    DUE_TO_PASSENGER => array('101', '141', '168', '169', '170'),
    DUE_TO_OTHER     => array('128'),
);

$config['chartered_car_cancel_reason_mvp'] = array(
    DUE_TO_DRIVER    => array('120', '121', '129', '124', '123', '125', '127'),
    DUE_TO_PASSENGER => array('150'),
    DUE_TO_OTHER     => array('128'),
);

$config['cancel_type'] = array(
    OrderSystem::PRODUCT_ID_DEFAULT                  => SPECIALCAR_CANCEL_REASON,
    OrderSystem::PRODUCT_ID_FAST_CAR                 => FASTCAR_CANCEL_REASON,
    OrderSystem::PRODUCT_ID_UBER_DEFAULT             => SPECIALCAR_CANCEL_REASON,
    OrderSystem::PRODUCT_ID_UBER_FAST_CAR            => FASTCAR_CANCEL_REASON,
    OrderSystem::PRODUCT_ID_UBER_YOUXIANG_CAR        => FASTCAR_CANCEL_REASON,
    OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR          => FIRSTCLASS_CANCEL_REASON,
    OrderSystem::PRODUCT_ID_YCAR                     => SPECIALCAR_CANCEL_REASON,
    OrderSystem::PRODUCT_ID_DACHE                    => UNIONE_CANCEL_REASON,
    OrderSystem::PRODUCT_ID_UNITAXI                  => UNIONE_CANCEL_REASON,
    OrderSystem::PRODUCT_ID_BRA_FAST_CAR             => BRAZIL_TAXI_CANCEL_REASON,
    OrderSystem::PRODUCT_ID_REGULAR_TAXI_CAR         => BRAZIL_TAXI_CANCEL_REASON,
    OrderSystem::PRODUCT_ID_TOP_TAXI_CAR             => BRAZIL_TAXI_CANCEL_REASON,
    OrderSystem::PRODUCT_ID_TW_TAXI_CAR              => UNIONE_CANCEL_REASON,
    OrderSystem::PRODUCT_ID_HK_TAXI_CAR              => UNIONE_CANCEL_REASON,
    OrderSystem::PRODUCT_ID_MEXICO_FAST_CAR          => MEXICO_FASTCAR_CANCEL_REASON,
    OrderSystem::PRODUCT_ID_JAPAN_TAXI_CAR           => UNIONE_CANCEL_REASON,
    OrderSystem::PRODUCT_ID_BUSINESS                 => SPECIALCAR_CANCEL_REASON,
    OrderSystem::PRODUCT_ID_BUSINESS_FAST_CAR        => FASTCAR_CANCEL_REASON,
    OrderSystem::PRODUCT_ID_BUSINESS_FIRST_CLASS_CAR => SPECIALCAR_CANCEL_REASON,
    OrderSystem::PRODUCT_ID_CHARTERED_CAR            => CHARTERED_CAR_CANCEL_REASON,
);

return $config;
