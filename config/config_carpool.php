<?php

use BizLib\Config as NuwaConfig;
/*
 * <AUTHOR> liss
 * @date   : 2015-07-10
 * @desc   : 拼车项目配置文件
 */
use Disf\SPL\Scheduler\Svc;

define('PRODUCT_ID_DEFAULT', 1);
define('PRODUCT_ID_BUSINESS', 2);
define('PRODUCT_ID_FAST_CAR', 3);
define('PRODUCT_ID_BUSINESS_FAST_CAR', 4);

define('TIMING_STATION_REC_STATUS_DEFAULT', 0);    //0:默认值，
define('TIMING_STATION_REC_STATUS_NOP_NOC', 1);    //1:无车无人：“附近车辆较少，叫车时间会较长”，发单后跳转到“等待应答”页面
define('TIMING_STATION_REC_STATUS_P_NOC', 2);    //2:无车有人：“附近车辆较少，叫车时间会较长”，发单后跳转到“等待应答”页面
define('TIMING_STATION_REC_STATUS_C_NOWAIT', 3);    //3:有车且不需要愿等：“10:12在站点［＊＊］发车”，发单后跳转到“等待应答”页面

define('TIMING_STATION_TIME_TYPE_POINT', 0); //显示时间点
define('TIMING_STATION_TIME_TYPE_SECTION', 1); //显示时间区间
define('TIMING_STATION_TIME_TYPE_DEFAULT', 2); //显示站点

define('TIMING_STATION_REC_STATUS_C_WAIT', 4);    //4:有车且需要愿等：“10:12在站点［＊＊］发车”，发单后跳转到“前往站点”页面
define('TIMING_STATION_REC_STATUS_C_MATCH', 5);    //5:有车且需要愿等：“10:12在站点［＊＊］发车”，发单后跳转到“匹配中”页面
define('TIMING_STATION_REC_STATUS_C_COUPON', 6);    //5:有车且需要愿等：“10:12在站点［＊＊］发车”，发单后跳转到“愿等返券”页面
define('TIMING_STATION_REC_STATUS_C_NO_COUPON', 7); //7:有车且需要愿等：“10:12在站点［＊＊］发车”，发单后跳转到“愿等不返券”页面
$config['rec_status'] = [
    TIMING_STATION_REC_STATUS_DEFAULT,
    TIMING_STATION_REC_STATUS_NOP_NOC,
    TIMING_STATION_REC_STATUS_P_NOC,
    TIMING_STATION_REC_STATUS_C_NOWAIT,
    TIMING_STATION_REC_STATUS_C_WAIT,
    TIMING_STATION_REC_STATUS_C_MATCH,
    TIMING_STATION_REC_STATUS_C_COUPON,
    TIMING_STATION_REC_STATUS_C_NO_COUPON,
];

$config['reward_url'] = array(
    //>>> 'iOpen' => 'http://${ROUTER_COMMONSHARE_IP_PORT}/foundation/commonshare/v1/pinchenghongbao/checkUserCanJoined',
    'iOpen'         => 'http://************:8000/foundation/commonshare/v1/pinchenghongbao/checkUserCanJoined',
    //<<<
    //>>> 'sendReward' => 'http://${ROUTER_COMMONSHARE_IP_PORT}/foundation/commonshare/v1/pinchenghongbao/secondPassengerJoined',
    'sendReward'    => 'http://************:8000/foundation/commonshare/v1/pinchenghongbao/secondPassengerJoined',
    //<<<
    //>>> 'sendCoupon' => 'http://${ROUTER_COMMONSHARE_IP_PORT}/foundation/commonshare/v1/pinchenghongbao/receiveCarpoolGift',
    'sendCoupon'    => 'http://************:8000/foundation/commonshare/v1/pinchenghongbao/receiveCarpoolGift',
    //<<<
    //>>> 'sendRewardOne' => 'http://${ROUTER_COMMONSHARE_IP_PORT}/foundation/commonshare/v1/pinchenghongbao/getCarpoolGiftInfo',
    'sendRewardOne' => 'http://************:8000/foundation/commonshare/v1/pinchenghongbao/getCarpoolGiftInfo',
    //<<<
    //>>> 'delCoupon' => 'http://${ROUTER_COMMONSHARE_IP_PORT}/foundation/commonshare/v1/pinchenghongbao/cancelCarpoolGift',
    'delCoupon'     => 'http://************:8000/foundation/commonshare/v1/pinchenghongbao/cancelCarpoolGift',
    //<<<
);
Svc::discoverHttpUrl(
    'disf!os-volcano-share_web',
    '/foundation/commonshare/v1/pinchenghongbao/checkUserCanJoined',
    Svc::thenUpdate($config['reward_url']['iOpen'])
);
Svc::discoverHttpUrl(
    'disf!os-volcano-share_web',
    '/foundation/commonshare/v1/pinchenghongbao/secondPassengerJoined',
    Svc::thenUpdate($config['reward_url']['sendReward'])
);
Svc::discoverHttpUrl(
    'disf!os-volcano-share_web',
    '/foundation/commonshare/v1/pinchenghongbao/receiveCarpoolGift',
    Svc::thenUpdate($config['reward_url']['sendCoupon'])
);
Svc::discoverHttpUrl(
    'disf!os-volcano-share_web',
    '/foundation/commonshare/v1/pinchenghongbao/getCarpoolGiftInfo',
    Svc::thenUpdate($config['reward_url']['sendRewardOne'])
);
Svc::discoverHttpUrl(
    'disf!os-volcano-share_web',
    '/foundation/commonshare/v1/pinchenghongbao/cancelCarpoolGift',
    Svc::thenUpdate($config['reward_url']['delCoupon'])
);

//每修改该文件，一定要修改该版本号+1，用于端上与缓存信息比较
$config['station_match_degree_info']    = array(
    'title'      => '为减少绕路时间，正在筛选顺路拼友',
    'sub_title'  => '后为您优先派车',
    'tips'       => '正在优先派车',
    'degree_des' => '拼友顺路程度{%s%%}',
);
$config['station_match_coupon_info']    = array(
    'title'      => '为减少绕路时间，正在筛选顺路拼友',
    'title_v2'   => '倒计时结束后无应答将获得{3元}拼车券',
    'sub_title'  => '后无应答将获得3元拼车券',
    'tips'       => '附近车辆有点少，送您一张{3元}拼车券，{正在为您优先派车}',
    'degree_des' => '拼友顺路程度{%s%%}',
);
$config['station_match_no_coupon_info'] = array(
    'title_v2' => '90%%的订单会在%s秒内应答，请稍等',
    'title_v3' => '正在寻找司机，倒计时结束后无应答将为您优先派车',
    'tips'     => '正在为您优先派车',
);

//拼车开通城市配置
/*
    city_list =	array(
        '区号' => array(
            （product_id 3）产品线=>array(车型1，车型2),#车型在这里面表示此车型开通了拼车
        )
    }
    #是否全部开通
    'all_open' => array(
        （product_id 3）产品线=>array(车型1，车型2),#车型在这里面表示此车型开通了拼车
    )
    #是否开通白名单测试
    'white_open' => false,
    #白名单列表使用专车上线时的白名单 rdis key  > A_P_WHITE_LIST  集合类型
 */

//$config['illegal_time_carpool_order_reason'] = '23:00到7:00不可拼';
//拼车等待接驾页面新用户教育提示
//拼成大促发券批次信息
//拼成大促配置开关
//配置快车的哪些车型开通了拼车 不分城市配置
$config['carpool_wait_msg'] = NuwaConfig::text('config_carpool', 'carpool_wait_msg');

//拼座配置开关
$config['pool_seat_open'] = array(
    'default' => array(//默认
        1,
        2,
    ),
);

//跨城区域拼车拼座数配置开关
$config['inter_city_pool_seat_open'] = array(
    'default' => array(//默认
        1,
        2,
        3,
        4,
    ),
    '028'     => array(//成都
        1,
        2,
        3,
        4,
    ),
    '0838'    => array(//德阳
        1,
        2,
        3,
        4,
    ),
);

$config['company_carpool_seat_open'] = [
    1,
    2,
];

$config['inter_city_config'] = array(
    'last_departure_time' => 45,    //区域拼车，最晚出发时间
    'pre_new_order'       => NuwaConfig::text('config_carpool', 'pre_new_order'),
);

//无管控默认等待时间／分钟
$config['carpool_default_wait_minute'] = 5;

$config['passenger_config'] = array(
    PRODUCT_ID_DEFAULT  => array(),

    PRODUCT_ID_FAST_CAR => array(
        'passenger_count' => array(
            0 => array(
                'count_id'    => 0,
                'count_value' => '不拼车',
                'is_default'  => 0,
            ),
            1 => array(
                'count_id'    => 1,
                'count_value' => '拼车-我有{1}人',
                'is_default'  => 1,
            ),
            2 => array(
                'count_id'    => 2,
                'count_value' => '拼车-我有{2}人',
                'is_default'  => 0,
            ),
        ), //快车拼车  允许的乘车人数  (即一个乘客发出的订单允许的上车人数，非车上总人数)
    ),
);

//配置快车的哪些车型开通了拼车 不分城市配置
$config['fast_carpool_level'] = array(
    3 => array(600, ),
);

//拼车：定时定点拼车单运力预测结果
$config['timing_station_rec'] = array(
    TIMING_STATION_TIME_TYPE_DEFAULT => '请走到站点{「%s」}上车',
    TIMING_STATION_TIME_TYPE_POINT   => '{%s}在站点{「%s」}发车',
    TIMING_STATION_TIME_TYPE_SECTION => '{%s-%s分钟}在站点{「%s」}上车',
);
//拼车：定时定点拼车单运力预测结果(去掉站点ABTEST)
$config['timing_station_rec_abtest'] = array(
    TIMING_STATION_TIME_TYPE_DEFAULT => '请走到上车点{「%s」}上车',
    TIMING_STATION_TIME_TYPE_POINT   => '{%s}在上车点{「%s」}发车',
    TIMING_STATION_TIME_TYPE_SECTION => '{%s-%s分钟}在上车点{「%s」}上车',
);
//围栏服务token
$config['carpool_fence_token'] = '00FC0E72F857C7DBFC2869D68E21EEE4';

//拼车通勤卡，相关配置
$config['carpool_commute_card'] = [
    //拼车通勤卡，在连续拒绝多少次后，会静默气泡处理
    'rejection_record_num'  => 2,
    //拼车通勤卡，静默气泡的时间
    'rejection_bubble_time' => 30 * 24 * 60 * 60,
    'bubble_config'         => [
        'show_url'             => 'http://img-ys011.didistatic.com/static/baoche_banner_photo/do1_3rfKEdtbM7X8vRiT8cbL',
        'title'                => '%s元购买拼车卡 立享快车%s折价',
        'confirm_button_title' => '购买',
    ],
];

$config['carpool_commute_H5'] = [
    //拼车通勤卡H5，在连续拒绝多少次后，会静默气泡处理
    'rejection_record_num' => 2,
    //拼车通勤卡，静默H5的时间
    'rejection_H5_time'    => 30 * 24 * 60 * 60,
];

$config['carpool_commute_send_card'] = [
    //最近*天内 在某路线没有购买过卡
    'last_buy_card_interval' => 90 * 24 * 3600, //90天
    //赠送次数阈值
    'maximum_send_times'     => 3, //3次
    //同一条路线连续送卡*次，均未使用
    'continuous_send_times'  => 2,

    'dialog_config'          => [
        'title'             => '送你{拼车7天通勤卡}',
        'sub_title'         => '拼车用卡价=快车%s折价',
        'content'           => '限特定时段使用，可在钱包-通勤卡查看详情',
        'button_text'       => '开心收下',
        'center_img_url'    => 'https://pt-starimg.didistatic.com/static/starimg/img/yDAOsJoEmf1560489775783.png',
        'background_url'    => 'https://pt-starimg.didistatic.com/static/starimg/img/rCYIfb5uKd1560489725397.png',
        'discount_title'    => '{%s}折',
        'discount_subtitle' => '相当于快车%s折',
    ],
];
$config['confirm_top_tips']          = array(
    'near' => '请在{%s前}到达上车站点',
    'far'  => '司机和您将在以下站点碰面',
);
$config['confirm_top_tips_abtest']   = array(
    'near' => '请在{%s前}到达上车点',
    'far'  => '司机和您将在以下上车点碰面',
);

return $config;
