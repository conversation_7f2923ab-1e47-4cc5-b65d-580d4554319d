<?php

use Disf\SPL\Scheduler\Svc;

//<<<
//<<<
//<<<
//<<<
//<<<
//<<<
//<<<
//<<<
//>>> $config['weixin_bindid_url'] = 'http://${ROUTER_IDENTITY_IP_PORT}/taxi/identity/bindid/openid/%s';//微信openid-真身标识查询服务接口
$config['weixin_bindid_url'] = 'http://*************:8000/antispam/identity/v1/p_identity/wxbindid/openid/%s?productid=200';
//微信openid-真身标识查询服务接口
Svc::discoverHttpUrl(
    'disf!sec-anti-identity_sync',
    '/antispam/identity/v1/p_identity/wxbindid/openid/%s?productid=200',
    Svc::thenUpdate($config['weixin_bindid_url'])
);
//<<<
//>>> $config['alipay_bindid_url'] = 'http://${ROUTER_IDENTITY_IP_PORT}/taxi/identity/zmxy_bindid/openid/%s';//支付宝openid-真身标识查询服务接口
$config['alipay_bindid_url'] = 'http://*************:8000/antispam/identity/v1/p_identity/zmxybindid/openid/%s?productid=200';
//支付宝openid-真身标识查询服务接口
Svc::discoverHttpUrl(
    'disf!sec-anti-identity_sync',
    '/antispam/identity/v1/p_identity/zmxybindid/openid/%s?productid=200',
    Svc::thenUpdate($config['alipay_bindid_url'])
);
//<<<
//<<<
//>>> $config['address_to_coordinate'] = 'http://${ROUTER_ADDRESS_GEOCODING_IP_PORT}/taxi/api/v2/c_geocoding?area=%s&address=%s&maptype=soso';
$config['address_to_coordinate'] = 'http://*************:8000/taxi/api/v2/c_geocoding?area=%s&address=%s&maptype=soso';
//<<<
Svc::discoverHttpUrl(
    'disf!commonplat-public-commoncorelegacy',
    '/taxi/api/v2/c_geocoding?area=%s&address=%s&maptype=soso',
    Svc::thenUpdate($config['address_to_coordinate'])
);
//<<<
//<<<
//<<<
//<<<
//<<<
//<<<
//<<<
//<<<
//<<<
//<<<
//<<<
//>>> $config['b2bNotify_callback_url'] = 'http://${ROUTER_GS_API_IP_PORT}/gulfstream/api/v1/schedule/b2bScheduleNotify/b2bNotifyCallback'; // 企业版短信、tts发送回调接口
$config['b2bNotify_callback_url'] = 'http://**************:8000/gulfstream/api/v1/schedule/b2bScheduleNotify/b2bNotifyCallback';
// 企业版短信、tts发送回调接口
//<<<
Svc::discoverHttpUrl(
    'disf!biz-gs-api',
    '/gulfstream/api/v1/schedule/b2bScheduleNotify/b2bNotifyCallback',
    Svc::thenUpdate($config['b2bNotify_callback_url'])
);
//<<<
//<<<
//司机端上报log路径（普通log 和 路测log） BY AndyCong
//司机端上报log路径（普通log 和 路测log） BY AndyCong
//添加音频跳转地址
//鱼鹰调用接口
//平台加价支付回调接口
//>>> $config['after_pay_call_url'] = 'http://${ROUTER_GS_API_IP_PORT}/gulfstream/api/v1/schedule/afterPay/afterPayCall';
$config['after_pay_call_url'] = 'http://**************:8000/gulfstream/api/v1/schedule/afterPay/afterPayCall';
//<<<
Svc::discoverHttpUrl(
    'disf!biz-gs-api',
    '/gulfstream/api/v1/schedule/afterPay/afterPayCall',
    Svc::thenUpdate($config['after_pay_call_url'])
);
//push微信代扣提醒消息接口
//<<<
//<<<
//<<<
//push支付宝代扣提醒消息接口
//<<<
//券系统 新建订单之后  请求的接口
//<<<
//查询乘客等级、积分信息接口
//<<<
//积分策略：根据乘客等级、叫车车型等获取积分接口
//<<<
//反作弊数据上报接口
//<<<
//分页查询券信息接口
//raptor url
//专车等待接驾banner图片链接调用mis接口
//order对应关系url  供出租车和专车使用
//<<<
//回家二次提醒url
//<<<
//第三方【非常准】接口
$config['variflight_url'] = 'http://open-al.variflight.com/api';
//sug检索地址
//<<<
//适时提醒
//<<<
//<<<
//<<<
//<<<
//<<<
//<<<
//<<<
//webapp的微信服务号模板消息回调
//<<<
//<<<
//<<<
//获取微信服务号openId地址
//<<<
//$config['weixinMpTemplateUrl'] = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=%s'; //微信模板消息API地址
//<<<
//请求正华的超级任务地址
//<<<
//校验订单类型url
//<<<
//司机端签到记录h5地址
//轮询订单奖励
//<<<
//强制加价
//<<<
//<<<
//券反作弊url
//<<<
//动态调价策略url
//动态调价加价提醒url
//动态调价加价再次确认url
//券系统url
//openid变更双写公共服务数据库地址
//>>> $config['openid_to_public_service_DB_url'] = 'http://${ROUTER_USER_INFO_IP_PORT}/comm/userinfo/userinfoserv/updateinfo';
$config['openid_to_public_service_DB_url'] = 'http://*************:8000/comm/userinfo/userinfoserv/updateinfo';
//<<<
Svc::discoverHttpUrl(
    'disf!common-plat-public-comm_userinfo',
    '/comm/userinfo/userinfoserv/updateinfo',
    Svc::thenUpdate($config['openid_to_public_service_DB_url'])
);
//>>> $config['public_service_DB_url'] = 'http://${ROUTER_USER_INFO_IP_PORT}/comm/userinfo/userinfoserv';
$config['public_service_DB_url'] = 'http://*************:8000/comm/userinfo/userinfoserv';
//<<<
Svc::discoverHttpUrl(
    'disf!common-plat-public-comm_userinfo',
    '/comm/userinfo/userinfoserv',
    Svc::thenUpdate($config['public_service_DB_url'])
);
//<<<
//获取实时计算的标签
$config['passenger_realtime_tag_url'] = 'http://*************:8000/bigdata-tagservice/passenger';
Svc::discoverHttpUrl(
    'disf!dataplatform-intelligent-tagservice',
    '/bigdata-tagservice/passenger',
    Svc::thenUpdate($config['passenger_realtime_tag_url'])
);
//人群统一判定接口
$config['bigdata_tagservice_integrate'] = 'http://*************/tag-services/integrate';
Svc::discoverHttpUrl(
    'disf!dataplatform-intelligent-tagservice',
    //>>> '/tag-services/integrate',
    '/bigdata-tagservice/integrate',
    //<<<
    Svc::thenUpdate($config['bigdata_tagservice_integrate'])
);

//>>> $config['driverBookingOrderBackUrl'] = "http://${ROUTER_GS_API_IP_PORT}/gulfstream/api/v1/schedule/doDriverOrderList";
$config['driverBookingOrderBackUrl'] = 'http://**************:8000/gulfstream/api/v1/schedule/doDriverOrderList';
//<<<
Svc::discoverHttpUrl(
    'disf!biz-gs-api',
    '/gulfstream/api/v1/schedule/doDriverOrderList',
    Svc::thenUpdate($config['driverBookingOrderBackUrl'])
);
//向orderdaemon推送司机状态
//<<<
//<<<
//<<<
//<<<
//检查司机交班是否超时
//<<<
//基于真身的新用户发单控制回调链接
//<<<
//反作弊真身请求获取接口，返回数组，可能存在支付宝、微信、手q真身
//>>> $config['antispam_bindId_request_url'] = 'http://${ROUTER_IDENTITY_IP_PORT}/antispam/identity/v1/p_identity/all/uid/%s';
$config['antispam_bindId_request_url'] = 'http://*************:8000/antispam/identity/v1/p_identity/all/uid/%s';
//<<<
Svc::discoverHttpUrl(
    'disf!sec-anti-identity_sync',
    '/antispam/identity/v1/p_identity/all/uid/%s',
    Svc::thenUpdate($config['antispam_bindId_request_url'])
);
//乘客修改上车点上报
//<<<
//动态调价系统新老系统数据对比，临时支持，后续下线
//<<<
//<<<
//接送机单改派监听回调地址
//<<<
//账单异步处理地址 BY AndyCong
//<<<
//api 解锁司机url
//<<<
//<<<
//开放平台openid签名校验
//>>> $config['develop_openid_sign_url'] = 'http://${ROUTER_GS_DEVELOP_IP_PORT}/gulfstream/develop/v1/permit/pVerifySign';
$config['develop_openid_sign_url'] = 'http://************:8000/gulfstream/develop/v1/permit/pVerifySign';
//<<<
Svc::discoverHttpUrl(
    'disf!biz-gs-biz_developer',
    '/gulfstream/develop/v1/permit/pVerifySign',
    Svc::thenUpdate($config['develop_openid_sign_url'])
);
//写kafka通知lbs
//<<<
//拼成大促
//<<<
//结束订单故障恢复回调接口
//<<<
//订单状态同步
//接送机
//<<<
//<<<
//<<<
//计算抢单失败数
//<<<
//专车预约单提前提醒回调
//司机系统提供rpc调用
//<<<
//Hermes系统提供http调用
//<<<
//收银台
//<<<
//持续派单队列
//>>> $config['keep_order_dispatch_url'] = 'http://${ROUTER_GS_ORDER_IP_PORT}/gulfstream/internalapi/v1/commands/orderDispatch/loop';
$config['keep_order_dispatch_url'] = 'http://*************:8000/gulfstream/internalapi/v1/commands/orderDispatch/loop';
//<<<
Svc::discoverHttpUrl(
    'disf!biz-gs-internalapi',
    '/gulfstream/internalapi/v1/commands/orderDispatch/loop',
    Svc::thenUpdate($config['keep_order_dispatch_url'])
);
//driver system系统提供rpc调用
//<<<
//<<<
//<<<
//开放平台
//川流订单取消回调出租车
//新的围栏服务
$config['fenceservice_urls'] = [
    //>>> 'http://***********:8081/fenceservice/infence',
    'http://************:8000/fenceservice/infence',   // eb 机房
    'http://*************:8000/fenceservice/infence',  //qq 机房
    //<<<
];
Svc::discoverEndpoints(
    'disf!engine-gs-geofence',
    function ($endpoints) use (&$config) {
        $config['fenceservice_urls'] = array();
        foreach ($endpoints as $endpoint) {
            $config['fenceservice_urls'][] = 'http://'.$endpoint['ip'].':'.(string)($endpoint['port']).'/fenceservice/infence';
        }
    }
);
//>>> $config['fenceservice_token'] = "3CF4BA9BDC761BA0";
$config['fenceservice_token'] = 'D47FD63B4DC1502A50D983C0C606B65F';
//<<<
//>>> $config['fenceservice_timeout'] = 10000; // ms
$config['fenceservice_timeout'] = 100;
// ms
//<<<
//星级查询
$config['query_driver_star'] = array(
    'urls'    => array(
        //http://************:3219/gulfstream/credit/queryStar?driver_type=flash&driver_id=3&city_id=1&date=20160524
        'http://**************:8000/gulfstream/credit/queryStar',
    ),
    'timeout' => 100, //ms
);
Svc::discoverEndpoints(
    'disf!biz-gs-credit',
    function ($endpoints) use (&$config) {
        $config['query_driver_star']['urls'] = array();
        foreach ($endpoints as $endpoint) {
            $config['query_driver_star']['urls'][] = 'http://'.$endpoint['ip'].':'.(string)($endpoint['port']).'/gulfstream/credit/queryStar';
        }
    }
);
//乘客是否提交取消原因回调
$config['themis_check_passenger_submit_cancel_reason_url'] = 'http://**************:8000/gulfstream/themis/v2/serviceControl/checkPassengerCancelReason';
Svc::discoverHttpUrl(
    'disf!biz-gs-themis',
    '/gulfstream/themis/v2/serviceControl/checkPassengerCancelReason',
    Svc::thenUpdate($config['themis_check_passenger_submit_cancel_reason_url'])
);
//扫码付
$config['scan_pay_return_url'] = 'https://page.udache.com/pay/pages/qrcode_pay.html';
//导流系统
$config['remote_dispatch_guide_notify_url'] = 'http://common.diditaxi.com.cn/platformdiversion/remoteGuideAffirm';
//会员信息 online: http://*************:8000
$config['member_profile_url'] = 'http://************/member/premier/profile';
Svc::discoverHttpUrl(
    'disf!os-volcano-member',
    '/member/premier/profile',
    Svc::thenUpdate($config['member_profile_url'])
);
//iapetos相关
$config['iapetos_rpc_url'] = 'http://**************:8000';
\Disf\SPL\Scheduler\Svc::discoverHttpUrl('disf!biz-gs-iapetos', '', \Disf\SPL\Scheduler\Svc::thenUpdate($config['iapetos_rpc_url']));
//订单系统提供rpc调用
//<<<
$config['credit_rpc_url'] = 'http://**************:8000/gulfstream/credit';
Svc::discoverHttpUrl(
    'disf!biz-gs-credit',
    '/gulfstream/credit',
    Svc::thenUpdate($config['credit_rpc_url'])
);
//  地图重写服
\Disf\SPL\Scheduler\Svc::discoverHttpUrl(
    'disf!map-point_sys-rewrite_service',
    '/poiservice/rewrite?name=%s&addr=%s&area=%s&lng=%f&lat=%f&orderid=%d&maptype=%s&productid=%s&passengerid=%s&type=%d&poiid=%s',
    \Disf\SPL\Scheduler\Svc::thenUpdate($config['address_rewrite'][0])
);
//车型url
$config['car_model_img_url'] = 'http://gulfstream.didistatic.com/static/zhuancheimg/';
//号码保护提供rpc调用
//>>> $config['virtual_phone_rpc_url'] = 'http://${ROUTER_GS_VIRTUAL_PHONE_IP_PORT}/mp/axb/api/';
$config['virtual_phone_rpc_url'] = 'http://*************:8000/mp/axb/api/';
//<<<
Svc::discoverHttpUrl(
    'disf!common-plat-public-axb-api',
    '/mp/axb/api/',
    Svc::thenUpdate($config['virtual_phone_rpc_url'])
);
$config['virtual_phone_rpc_timeout'] = 200;
//预付单 回调接口
$config['prepay_order_assign_dispatch'] = 'https://api.udache.com/gulfstream/passenger/v2/other/pPrepayOrderAssignDispatch';
//增长系统
$config['dorado_growth'] = 'http://*************:8000/dorado/v1/open';
//反作弊真身校验
$config['honeycomb_risk']    = 'http://*************:8000/gateway';
$config['safety_bindid_url'] = 'http://************:8000/antispam/identity/v1/p_identity/verify/uid';
Svc::discoverHttpUrl(
    'disf!sec-anti-identity_sync',
    '/antispam/identity/v1/p_identity/verify/uid',
    Svc::thenUpdate($config['safety_bindid_url'])
);
//生成司机行程分享短链接 for iOS10RichNotification
//>>> $config['driver_share_info'] = 'http://*************:8000/rd/webapp/sharetrips/internal/contentForShare';
$config['driver_share_info'] = 'http://*************:8000/common/webapp/sharetrips/internal/contentForShare';
//<<<
//>>> Svc::discoverHttpUrl('disf!commonplat-public-commonweb', '/rd/webapp/sharetrips/internal/contentForShare',
Svc::discoverHttpUrl(
    'disf!commonplat-public-commonweb',
    '/common/webapp/sharetrips/internal/contentForShare',
    //<<<
    Svc::thenUpdate($config['driver_share_info'])
);
//查询乘客等级新接口
//>>> $config['user_level_new_url'] = 'http://${USER_LEVEL_IP_PORT}/member';
$config['user_level_new_url'] = 'http://************:8000/member';
//<<<
Svc::discoverHttpUrl(
    'disf!os-volcano-member',
    '/member',
    Svc::thenUpdate($config['user_level_new_url'])
);
$config['user_level_secret'] = 'DjixgwZwg*v$lg#65ApQSv4F^9JLJU7h';
$config['kop']         = 'https://gsscs.xiaojukeji.com/gateway';
$config['kop_app_key'] = '145458a464c049b28719e3907fc3f963';
$config['kop_app_sec'] = '5503e54cc39f460d91c8cfb1957380e5';

//乘客端司机主页，调用mis获取司机信息
$config['passenger_driver_mis_info']     = 'http://*************:8000/dop/grade/api/aDriverGrade/passenger';
$config['passenger_update_dst_callback'] = 'https://api.udache.com/gulfstream/passenger/v2/other/pUpdateDestPrepayCallBack';

return $config;
