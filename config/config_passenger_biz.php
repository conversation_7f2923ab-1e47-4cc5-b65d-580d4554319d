<?php

use BizLib\Config as NuwaConfig;

// 墨西哥快车
$config['config_passenger_biz']['375'] = array(
    'service_phone' => '018009888888', //客服电话
);

// 日本出租车
$config['config_passenger_biz']['378'] = array(
    'service_phone' => '************,8', //客服电话
);

// 澳洲快车
$config['config_passenger_biz']['383'] = array(
    'service_phone' => '611800861602', //客服电话
);

//专车
$config['config_passenger_biz']['258'] = array(
    'port_list'  => NuwaConfig::text('config_port_list', 'port_list_v2'), //口岸信息
    'trajectory' => [
        /*
         * 采集频率
         * DDMTrackSampleFrequencyNone = -1,                   //不需要采集,只需上报实时点
         * DDMTrackSampleFrequencyModeHigh = 1,                //高频率模式，采样间隔1秒
         * DDMTrackSampleFrequencyModeNormal = 3,              //普通模式，采样间隔3秒
         * DDMTrackSampleFrequencyModeLow = 9,                 //低频率模式，采样间隔9秒
         * DDMTrackSampleFrequencyModeBatterySave = 36         //省电模式，采样间隔36秒
         */
        'samplingFrequency' => -1,
        /*
         * 上报频率（上报频率大于采集频率）
         * DDMTrackUploadModeHigh = 3,                         //高频率模式，默认上报间隔3秒
         * DDMTrackUploadModeNormal = 9,                       //普通模式，默认上报间隔9秒
         * DDMTrackUploadModeLow = 36,                         //低频率模式，默认上报间隔36秒
         * DDMTrackUploadModeBatterySave = 72,                 //省电模式，默认上报间隔72秒
         */
        'sendFrequency' => 9,
        /*
         * 采集时间(单位：秒)
         */
        'samplingtime' => 1800,
        /*
         * 采集精度
         * kCLLocationAccuracyBestForNavigation = -2,           // 导航经度
         * kCLLocationAccuracyBest = -1,                        // kCLLocationAccuracyBest
         * kCLLocationAccuracyNearestTenMeters = 10,            // 10米
         * kCLLocationAccuracyHundredMeters = 100,              // 100米
         * kCLLocationAccuracyKilometer = 1000,                 // 1000米
         * kCLLocationAccuracyThreeKilometers = 3000,           // 3000米
         */
        'locationDesiredAccuracy' => -1,
        /*
         * 距离差
         * DISTANCE_0 = 5,
         * DISTANCE_0 = 10,
         * DISTANCE_0 = 50,
         * DISTANCE_0 = 100,
         */
        'distanceFilter' => 5,
    ],
);

//快车
$config['config_passenger_biz']['260'] = array(
    'trajectory' => [
        'samplingFrequency'       => -1,
        'sendFrequency'           => 9,
        'samplingtime'            => 1800,
        'locationDesiredAccuracy' => -1,
        'distanceFilter'          => 5,
    ],
);

//unione
$config['config_passenger_biz']['307'] = array(
    'trajectory' => [
        'samplingFrequency'       => -1,
        'sendFrequency'           => 9,
        'samplingtime'            => 1800,
        'locationDesiredAccuracy' => -1,
        'distanceFilter'          => 5,
    ],
);

//豪华车
$config['config_passenger_biz']['276'] = array(
    'trajectory' => [
        'samplingFrequency'       => -1,
        'sendFrequency'           => 9,
        'samplingtime'            => 1800,
        'locationDesiredAccuracy' => -1,
        'distanceFilter'          => 5,
    ],
);

//打车
$config['config_passenger_biz']['666'] = array(
    'port_list'  => NuwaConfig::text('config_port_list', 'port_list_v2'), //口岸信息
    'trajectory' => [
        'samplingFrequency'       => -1,
        'sendFrequency'           => 9,
        'samplingtime'            => 1800,
        'locationDesiredAccuracy' => -1,
        'distanceFilter'          => 5,
    ],
);

return $config;
