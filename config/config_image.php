<?php
/**
 * @desc 图片的配置
 *
 * <AUTHOR>
 *
 * @version 2014-05-23
 */
use Disf\SPL\Scheduler\Svc;

$config['static_source_config'] = array(
    //版本号
    'version'               => '75476.819855.82523.8584.83552.83804.84776.89085',
    'static_server_root'    => '/home/<USER>/webroot/gulfstream/static/mis',
    'upload_path'           => '/upload',
    'allow_file_types'      => array('jpg', 'jpeg', 'gif', 'png', 'bmp'),
    'max_upload_size'       => '2MB', //2M
    'private_key'           => 'gulfstream',
    //限制允许上传的图片后缀
    'file_type_exts'        => '*.gif; *.jpg; *.png; *.jpeg; *.bmp;',
    'uploader'              => '/gulfstream/api/v1/mis/mUpload/doPost',
    //静态服务器集群图片服务器地址
    'static_server_url'     => 'http://static.xiaojukeji.com/gulfstream',
    //静态服务器集群.静态文件地址
    'static_asset_url'      => 'http://static.xiaojukeji.com/gulfstream/mis/',
    //地图的开发密钥
    'map_key'           => 'Y5FBZ-JOGA4-ZCKU2-DVION-UT4PO-4GBHT',
    //uic远程登录链接
    'uic_login_url'         => 'http://mis.xiaojukeji.com/crm/index.php?r=site/remoteLogin',
    //mis crm yii链接
    'mis_crm_url'         => 'http://mis.xiaojukeji.com/crm/index.php',
    //uic验证地址
    'uic_check_url'       => 'http://mis.xiaojukeji.com/crm/index.php?r=misPrivilege/checkPrivilege',
    //大数据统计数据
    'bigdata_statis_url'    => 'http://mis03.qq:8000/mis/public_gs_bi_bam/get_datas',
    //当前项目路径
    'domain_base_url'       => '/gulfstream/api/v1/',
    //mis默认首页路由
    'default_page' => 'mis/mDriver/showDriverView/',
    //mis服务器地址变成VIP地址
    'mis_server_root'    => 'http://mis.xiaojukeji.com', //mis域名 正式环境
    'mis_server_vip_root'=> 'http://**************:8000', //mis域名 正式环境
    //node服务器地
    'node_host'         => 'http://nodejs.udache.com:8090/',

    //本地地址
    'local_host'   => '127.0.0.1:8000',
    //订单中心
    'list'              => array(
        //订单的类型
        'type'          => array(
            '0'             => '即时订单',
            '1'             => '预约订单',
            '2'             => '改派订单',
            '3'             => '即时订单－标准',
            '4'             => '即时订单－非标准',
            '5'             => '免单',
        ),
        //订单渠道
        'channel'       => array(
            '0'             => '不限',
            '1'             => '打车客户端',
            '2'             => '专车客户端',
            '3'             => 'PCWEB',
            '4'             => '合作方1',
        ),
    ),
    'mobile_menu' => array(//移动端菜单
    ),
    'user_mobile_menu' => array(//组与移动菜单配置关系
    ),
    //全局导航配置菜单
    'menu' => array(
        1=> array(
            'name'     => '基础信息',
            'url'      => '',
            'icon'     => 'fa-cubes',
            'children' => array(
                '2' => array(
                    'name'   => '司机列表',
                    'url'    => 'mis/mDriver/showDriverView/',
                    'icon'   => 'fa-user',
                    'default'=> 1,
                ),
                '3' => array(
                    'name' => '车辆列表',
                    'url'  => 'mis/mCar/showCarView/',
                    'icon' => 'fa-car',
                ),
                '4' => array(
                    'name' => '公司列表',
                    'url'  => 'mis/mCompany/showCompanyView/',
                    'icon' => 'fa-home',
                ),
                'passenger' => array(
                    'name'  => '乘客列表',
                    'url'   => 'mis/mPassenger/showPassengerList',
                    'icon'  => 'fa-user',
                ),
                'replenishStation' => array(
                    'name'  => '网点列表',
                    'url'   => 'mis/mReplenishStation/showReplenishStationView',
                    'icon'  => 'fa-user',
                ),
        '5' => array(
                    'name'  => '签到查询',
                    'url'   => 'mis/mScan/stationScanIndex',
                    'icon'  => 'fa-user',
                ),
            ),
        ),
        'data' => array(
            'name'     => '数据中心',
            'url'      => '',
            'icon'     => 'fa-cubes',
            'children' => array(
                'business_data' => array(
                    'name' => '业务数据展板',
                    'url'  => 'mis/mBusinessData/showBusinessData',
                    'icon' => 'fa-bar-chart-o',
                ),
                'monitor'       => array(
                    'name'  => '专车地图展板',
                    'url'   => 'mis/mMonitor/showMonitorBoard',
                    'icon'  => 'fa-dashboard',
                ),
                'driver_daily' => array(
                    'name' => '业务日报',
                    'url'  => 'mis/mDriverDaily/showDailyBoard',
                    'icon' => 'fa-car',
                ),
            ),
        ),
        5 => array(
            'name'     => '应用中心',
            'url'      => '',
            'icon'     => 'fa-cubes',
            'children' => array(
                '6' => array(
                    'name' => '订单中心',
                    'url'  => 'mis/mList/showListView',
                    'icon' => 'fa-file-text-o',
                ),
                '7' => array(
                    'name' => '客服中心',
                    'url'  => 'mis/mService/showRecordLists',
                    'icon' => 'fa-phone',
                ),
                '8' => array(
                    'name'  => '司机业绩管理',
                    'url'   => 'mis/mLongRentCar/showMonitorBoard',
                    'icon'  => 'fa-car',
                ),
                'invoice' => array(
                    'name' => '发票管理',
                    'url'  => 'mis/mInvoice/showInvoiceView',
                    'icon' => 'fa-file-text',
                ),
                'didipush' => array(
                    'name'  => '滴滴播报',
                    'url'   => 'mis/mDidiPush/showPushList',
                    'icon'  => 'fa-file-text',
                ),
                /*
                'statistic' => array(
                		'name'  => '订单统计',
                		'url'   => 'mis/mJoinModelStatistic/showDriverOrderStatistic',
                		'icon'  => 'fa-file-text',
                )*/
                'passenger-compensate' => array(
                        'name' => '乘客补偿管理',
                        'url'  => 'mis/mOrderCompensate/showCompensateList?type=1',
                        'icon' => 'fa-file-text',
                ),
                'driver-compensate' => array(
                        'name' => '司机补偿管理',
                        'url'  => 'mis/mOrderCompensate/showCompensateList?type=2',
                        'icon' => 'fa-file-text',
                ),
                'passenger-questionnaire ' => array(
                        'name' => '乘客问卷调查',
                        'url'  => 'mis/mQuestionnaire/index',
                        'icon' => 'fa-file-text',
                ),
            ),
        ),
        'finance_manage'=> array(
                'name'        => '财务管理',
                'url'         => '',
                'icon'        => 'fa-money',
                'children'    => array(
                        'salary'    => array(
                                'name'    => '账务查询',
                                'icon'    => 'fa-cny',
                                'url'     => 'mis/mFinance/showSalaryList',
                        ),
                ),
        ),
        'bd_kpi_manage'=> array(
            'name'        => 'BD专区',
            'url'         => '',
            'icon'        => 'fa-list-alt',
            'children'    => array(
                'driver_promotion' => array(
                    'name' => '线下注册渠道',
                    'url'  => 'mis/mDriverPromotion/index/',
                    'icon' => 'fa-thumbs-up',
                ),
                'driver_bind' => array(
                    'name' => 'BD绑定司机',
                    'url'  => 'mis/mDriverBind/index',
                    'icon' => 'fa-link',
                ),
                'bd_kpi' => array(
                    'name' => 'BD绩效查询',
                    'url'  => 'mis/mBdDriverKpi/index',
                    'icon' => 'fa-search',
                ),
            ),
        ),
        'sys_manage'    => array(
            'name'      => '系统管理',
            'url'       => '',
            'icon'      => 'fa-cogs',
            'children'  => array(
                'user_manage'   => array(
                    'name'      => '用户管理',
                    'url'       => 'mis/mSysManage/showUserManagePage',
                    'icon'      => 'fa-group',
                ),
                'invoice_company'=> array(
                        'name' => '发票公司管理',
                        'url'  => 'mis/mInvoiceCompany/sohwInvoiceCompany',
                        'icon' => 'fa-group',
                ),
            ),
        ),
    ),

    //司机相关配置
    'driver' => array(
        //司机类型配置信息
        'type' => array(
            array(
                'key'      => '1',
                'value'    => '加盟司机',
                'children' => array(
                    array('key'=>'11', 'value'=>'专职'),
                    array('key'=> '12', 'value'=>'兼职'),
                ),
            ),
            array(
                'key'     => '2',
                'value'   => '自营车司机',
                'children'=> array(
                    array(
                        'key'  => '21',
                        'value'=> '单班',
                    ),
                    array(
                        'key'     => '22',
                        'value'   => '双班',
                        'children'=> array(
                            array('key'=>'221', 'value'=>'早班'),
                            array('key'=> '222', 'value'=>'晚班'),
                        ),
                    ),
                ),
            ),
            array(
                'key'     => '3',
                'value'   => '营运司机',
                'children'=> array(
                    array('key'=>'31', 'value'=>'个人'),
                    array('key'=> '32', 'value'=>'公司'),
                ),
            ),
            array(
                'key'   => 5,
                'value' => '对公司机',
            ),
        ),
        //合作模式
        'join_model' => array('1'=>'普通加盟车', '2'=>'自营车', '3'=>'营运车', '4'=>'豪华加盟车', '5'=>'对公司机'),
        //户口类型
        'account_type' => array(1=>'农业户口', 2=>'非农户口'),
    ),
    //车辆相关配置
    'car'=> array(
        //使用性质
        'use_character' => array(
            '1' => '营运公司',
            '2' => '营运个人',
            '3' => '租赁',
            '4' => '非营运公司',
            '5' => '非营运个人',
        ),
        //业务模式
        'business_model' => array(
            '1' => '自营',
            '2' => '合作营运',
            '3' => '加盟',
            '4' => 'VIP定制',
        ),
        //车辆颜色
        'color' => '黑,灰,银,白,红,金,蓝,香槟,棕,咖啡,紫,米,褐,绿,橙,粉',
        //车辆渠道
        'channel' => array('0'=>'个人所有', '1'=>'公司所有', '2'=>'个人长包', '3'=>'公司长包'),
    ),
    //网点相关配置
    'station'=> array(
        //网点状态
        'status' => array(
            '1' => '待审核',
            '2' => '待开启',
            '3' => '运营中',
            '4' => '已过期',
            '5' => '审核未通过',
        ),
        'add_staff' => array(
            154        => '王雅娟',
            497        => '王恬',
            98         => '刘思成',
            523        => '李文惠',
            747        => '逯宇庭',
            525        => '贾广路',
            736        => '金莹',
            756        => '宋波',
            757        => '韩达',
            323        => '张良',
            395        => '朱玉银',
            318        => '刘洪元',
            300        => '冯力',
            336        => '王芳',
            317        => '田波',
            558        => '肖盛',
            861        => '薛娜',
            372        => '易杰',
            305        => '魏照伟',
            395        => '朱玉银',
            448        => '宋磊',
            631        => '何雪',
            309        => '陈伟',
            632        => '刘宏宇',
            419        => '张兰',
            316        => '陈小东',
            507        => '薛恩菊',
            505        => '冯晓飞',
            434        => '陈美玲',
            582        => '唐光金',
            377        => '刘健',
            433        => '徐彪',
            418        => '柳明',
            306        => '杨妍',
            301        => '季雷',
            422        => '何明鑫',
            376        => '李浩然',
            673        => '林泽浩',
            653        => '冯栎颖',
            508        => '孙楠楠',
            678        => '尹全',
            389        => '孔祥克',
            502        => '李梓豪',
            667        => '王永昌',
            498        => '王辉',
            425        => '庞锦宏',
            681        => '马瑞龙',
            654        => '乐中瑞',
            672        => '江泽芹',
        ),
        'edit_staff' => array(
            154        => '王雅娟',
            497        => '王恬',
            98         => '刘思成',
            523        => '李文惠',
            747        => '逯宇庭',
            525        => '贾广路',
            736        => '金莹',
            756        => '宋波',
            757        => '韩达',
            323        => '张良',
            395        => '朱玉银',
            318        => '刘洪元',
            300        => '冯力',
            336        => '王芳',
            317        => '田波',
            558        => '肖盛',
            861        => '薛娜',
            372        => '易杰',
            305        => '魏照伟',
            395        => '朱玉银',
            448        => '宋磊',
            631        => '何雪',
            309        => '陈伟',
            632        => '刘宏宇',
            419        => '张兰',
            316        => '陈小东',
            507        => '薛恩菊',
            505        => '冯晓飞',
            434        => '陈美玲',
            582        => '唐光金',
            377        => '刘健',
            433        => '徐彪',
            418        => '柳明',
            306        => '杨妍',
            301        => '季雷',
            422        => '何明鑫',
            376        => '李浩然',
            673        => '林泽浩',
            653        => '冯栎颖',
        ),
        'audit_staff' => array(
            154        => '王雅娟',
            497        => '王恬',
            98         => '刘思成',
            523        => '李文惠',
            747        => '逯宇庭',
            525        => '贾广路',
            736        => '金莹',
            240        => '杨晓亮',
            378        => '王思宁',
            91         => '李宁',
            757        => '韩达',
            323        => '张良',
            395        => '朱玉银',
            318        => '刘洪元',
            300        => '冯力',
            336        => '王芳',
            317        => '田波',
            558        => '肖盛',
            861        => '薛娜',
            372        => '易杰',
            305        => '魏照伟',
            395        => '朱玉银',
            448        => '宋磊',
            631        => '何雪',
            309        => '陈伟',
            632        => '刘宏宇',
            419        => '张兰',
            316        => '陈小东',
            507        => '薛恩菊',
            505        => '冯晓飞',
            434        => '陈美玲',
            582        => '唐光金',
            377        => '刘健',
            433        => '徐彪',
            418        => '柳明',
            306        => '杨妍',
            301        => '季雷',
            422        => '何明鑫',
            376        => '李浩然',
            673        => '林泽浩',
            653        => '冯栎颖',
        ),
    ),
    //司机问卷相关配置
    'questionnaire'=> array(
        //网点状态
        'url'              => 'http://xiaojukeji.qq.com/survey/index',
        'head'             => '小滴诚邀您填写问卷，成功提交后将在2月2日送您60元专车券！您是我们最重视的客人，这是您的专属链接',
        'foot'             => ' 。回复TD退订【滴滴打车】',
        'questionnaire_id' => 35,
        'send_staff'       => array(
            523        => '李文惠',
            747        => '逯宇庭',
            295        => '张良',
            569        => '冯鑫',
            176        => '李玉庆',
            369        => '吕梓轩',
            408        => '伍航佳',
            541        => '计宁茹',
            544        => '孟庆明',
            537        => '刘蔷',
            536        => '王群',
            542        => '周瑶',
            194        => '陈艳艳',
            567        => '光伟',
            154        => '王雅娟',
            298        => '霍志斌',
            174        => '苏丽瑶',
            905        => '谭宇',
        ),
    ),
    //乘客订单补偿管理
    'compensate'    => array(
            'preason'        => array(  //乘客补偿原因
                1        => '司机/车辆服务投诉',
                2        => 'APP/系统使用问题',
                3        => '收费策略不合理',
                4        => '运营活动相关',
                5        => '其他',
            ),
            'dreason'        => array( //司机补偿原因
                    6         => '空驶补偿',
                    7         => '系统问题',
                    8         => '订单异常',
                    9         => '活动奖励',
                    11        => '坏账补偿',
                    10        => '其他',
            ),
            'channel'       => array(
                1       => '余额',
                2       => '代金券',
                4       => '扣司机补乘客',
                5       => '扣乘客补司机',
            ),
            'audit_staff'    => array(
                    188        => '杜静',
                    243        => '王海英',
                    150        => '李桂芝',
                    158        => '张艳楠',
                    161        => '冯淑云',
                    171        => '刘海兰',
                    191        => '王爽',
                    360        => '罗欢',
                    390        => '王云',
                    510        => '石鹏',
                    169        => '许记红',
            ),
    ),
    //工资配置白名单
    'finance_deploy' => array(
            'white' => array(
                116 => '汪会清',
                94  => '唐彦也',
                261 => '胡哲明',
                262 => '王丹丹',
                356 => '徐小乐',
                224 => '梁小平',
            ),
    ),
    'white' => array(
        //导出
        'mis/mExport/',
        //上传图像
        'mis/mUpload/doPost',
        'mis/mUpload/cropImg',
        //远程调用打车接口
        'mis/soap/getRemoteData',
        //乘客档案
        'mis/mPassenger/showPassengerList',
        'mis/mPassenger/showPassengerDetail',
        'mis/mPassenger/getPassengerInfo',
        'mis/mPassenger/getHistoryOrders',
        'mis/mPassenger/getOrderDataItem',
        'mis//',
        'mis/mCompany/getAllCompany',
        'notify/OrderWarning',
        'notify/OrderStatusChangeStat',
        'notify/OrderPushLog',
        'notify/OrderOnService',
        'notify/OrderNotifyStat',
        'notify/OrderMatch',
        'notify/OrderLog',
        'notify/OrderExtraPlayCnt',
        'notify/OrderBoradCast',
        'notify/DriverStatusNotify',
        'notify/WatchRedis',
        'mis/mRes/getList',
        'mis/mService/complaintSummaryNotify',
    ),
    //pay相关配置信息
    'pay'=> array(
        //出款方
        'from_id'       => '999998',
        'from_type'     => '5',
        'appkey'        => '1234567890!@#$%^&*',
        'misKey'        => 'mis_key_20141211', //奖励、补款、扣款新接口使用
        'misSecret'     => '94e2be195d5f39e19abb1eb5d5f39', //奖励、补款、扣款新接口使用
        'pay_server_url'=> 'http://**************:8000/gulfstream/pay/v1', //支付 正式环境
    ),
    //作弊类型(type of spam_data)的含义,参考打车的type
    'cheat_type'=> array(
        '-1'=> '刷单策略中的从属订单',
        '0' => '默认值',
        '1' => '投诉',
        '2' => '刷单',
        '3' => '车上发单',
        '4' => '微信支付',
        '5' => '语音作弊',
        '6' => '未完成订单',
        '7' => '乘客对应多个微信id订单',
        '8' => '身份证对应多个乘客',
    ),
    //券相关配置
    'coupon'    => array(
        'server_url'    => 'http://10.231.158.59:8000/foundation/coupon/v1/',
        'appkey'        => 'Dd#4r8coupon?_7rfG@',
        'productid'     => 200,
        'compensate'    => array(
            'channel'       => 3, //滴滴红包
            'activityid'    => 100283,
            'batchid'       => array(
                    10  => 1000828,
                    20  => 1000829,
                    30  => 1000830,
                    40  => 1000831,
                    50  => 1000832,
                    60  => 1000833,
            ),
        ),
    ),
);

\Disf\SPL\Scheduler\Svc::discoverHttpUrl(
    'disf!common-plat-gs-pay',
    '/gulfstream/pay/v1',
    \Disf\SPL\Scheduler\Svc::thenUpdate($config['static_source_config']['pay']['pay_server_url'])
);
Svc::discoverHttpUrl(
    'disf!common-plat-public-ms-coupon',
    '/foundation/coupon/v1/',
    Svc::thenUpdate($config['static_source_config']['coupon']['server_url'])
);

return $config;
