<?php

/*
 * @desc   : redis缓存前缀配置 增加配置前一定要确认key和val都没有被定义过 每定义一个key都要声明一个常量对应
 * <AUTHOR> <PERSON><PERSON>ong<<EMAIL>>
 * @date   : 2014-05-14
 */
//司机端
define('D_BASIC_INFO', 'd_basic_info');
//司机基础信息
define('D_EXT_INFO', 'd_ext_info');
//司机扩展信息
define('D_CONF_INFO', 'd_conf_info');
//司机配置信息
define('D_GETDRIVERIDBYPHONE', 'd_getdriveridbyphone');
//通过手机号获取driver_id
define('D_PUSH_TIME', 'd_push_time');
//根据司机ID记录推送时间
define('D_PUSH_ORDER_TIME', 'd_push_order_time');
//记录给司机推送订单的时间
define('D_JIASUQI_WHITE_LIST', 'd_jiasuqi_white_list');
//记录给司机推送订单的时间
define('D_IDCARD_INFO', 'd_idcard_info');
//通过司机ID获取身份证相关信息
define('D_IDCARD', 'd_idcard');
//通过司机ID获取身份证号
define('D_CONFIRM_CODE_INFO', 'd_confirm_code_info');
//验证码相关信息
define('D_SENDCODE', 'd_sendcode');
//记录验证码
define('D_COMPANY_BASIC_INFO', 'd_company_basic_info');
//记录司机所属公司基本信息
define('D_NATIONAL_COMPANY_BASIC_INFO', 'd_national_company_basic_info');
//记录国际化公司基本信息
define('D_LOCATION', 'd_location');
//记录经纬度
define('D_APP_VERSION', 'd_app_version');
//记录司机版本信息
define('D_CAR_BASIC_INFO', 'd_car_basic_info');
//记录司机所开车辆基本信息
define('D_CARID', 'd_carid');
//记录司机所开车辆id
define('D_PHOTO', 'd_photo');
//记录司机图像
define('D_PHOTO_V2', 'd_photo_v2');
//记录司机图像
define('D_PHOTO_V3', 'd_photo_v3');
//记录司机图像
define('D_PHOTO_V4', 'd_photo_v4');
//记录司机图像
define('D_RESENDORDER_FILTER', 'd_resendorder_filter');
//司机已改派订单，重新播报订单对该司机过滤
define('D_FIND_DRIVER_NUM', 'd_find_driver_num');
//获得司机数目
define('D_GETDRIVERIDBYIDCARDNO', 'd_getdriveridbyidcarno');
//通过身份证号获取driver_id
define('D_CHANGE_PWD_ERROR_TIMES', 'd_change_pwd_error_times');
//司机修改密码错误次数+phone
define('D_GET_BY_PASSPORT_UID', 'd_get_by_passort_uid');
//通过passport的uid获取司机信息+uid
define('D_GET_DAY_INFO', 'd_get_day_info');
// 获取当天在线时长
define('D_SET_DAY_INFO_TIMESTAMP', 'd_set_day_info_timestamp');
// 设置在线时长的起始时间戳
define('D_TOTALFEE', 'd_totalfee');
// 设置司机总金额
define('D_CURRENT_ORDER_NUM', 'd_current_order_num');
// 司机当天抢单数
define('D_FORBIDDEN_ONE_DAY', 'd_forbidden_one_day');
// 司机封禁一天
define('D_HONOR_LIST', 'd_honor_list');
//司机荣誉榜,根据平均星级计算排序
define('D_LONG_RENT_ORDER_NUM', 'd_long_rent_order_num');
// 长包车当天抢单数
define('D_GETCARIDBYPLATENO', 'd_getcaridbyplateno');
//通过车牌号获取车辆id
define('D_GETDRIVERLISTBYCARID', 'd_getdriverlistbycarid');
//通过车辆id获取司机id
define('D_RANK_ORDER_NUM_AREA', 'd_rank_order_num_area');
//司机排行榜
define('D_MONTH_ORDER_COST', 'd_month_order_cost');
//获取司机某月的订单费用
define('NEW_D_MONTH_ORDER_COST', 'new_d_month_order_cost');
//司乘计价分离后,获取司机某月的订单费用
define('D_MONTH_LEVEL', 'd_month_level');
//获取司机某月星级
define('D_REALTIMEPRICING_LAST_FEE_LOCK', 'd_realtimepricing_last_fee_lock');
// 实时计价上次费用加锁
define('D_FINISH_ORDER_LOCK', 'd_finish_order_lock');
// 完成计费加锁
define('D_RECHARGE_LOCK', 'd_recharge_lock');
// 司机代充值加锁
define('D_WITHDRAW_CNT', 'd_withdraw_cnt');
// 司机提现次数限制
define('D_WITHDRAW_MONEY', 'd_withdraw_money');
// 司机提现金额限制
define('D_WITHDRAW_LOCK', 'd_withdraw_lock');
// 司机提现加锁
define('D_CITY_PRICE', 'd_city_price');
// 城市计价配置
define('D_CITY_CAR_PRICE', 'd_city_car_price');
//城市车型计价配置
define('D_CITY_COMBO_TITLE', 'd_city_combo_title');
// 城市套餐
define('D_CITY_CAR_DEFAULT_PRICE', 'd_city_car_default_price');
//城市默认车型计价配置
define('D_PHONE_VERIFY', 'd_phone_verify');
//司机注册手机验证码
define('D_PHONE_VERIFY_LIMIT', 'd_phone_verify_limit');
//司机注册手机验证限制
define('D_SEND_CODE_LIMIT', 'd_send_code_limit');
//司机每天发送验证码次数限制
define('D_STRIVE_ORDER_DRIVER_CNT', 'd_strive_order_driver_cnt');
// 记录抢单司机数（供鱼鹰使用）
define('D_COUNTDOWN_TIME', 'd_countdown_time');
// 司机倒计时时间
define('D_CITY_DEFAULT_CAR_LEVEL', 'd_city_default_car_level');
// 获得城市对应的默认车型
define('D_LITTLE_DRIVER_UPDATE_WHITELIST', 'd_little_driver_update_whitelist');
// 司机小流量升级白名单
define('D_WITHDRAW_NOTIFY', 'd_withdraw_notify');
// 提现回调通知
define('D_WITHDRAW_INFO', 'd_withdraw_info');
// 司机提现加锁
define('D_SIGNED_DAY_IMEI', 'd_signed_day_imei');
//地网-识别此imei号今日是否签到过
define('D_SIGNED_DAY_FLAG', 'd_signed_day_flag');
//地网-今日是否签到
define('D_SIGNED_WEEK_COUNT', 'd_signed_week_count');
//地网-本周签到数
define('D_SIGNED_MONTH_COUNT', 'd_signed_month_count');
//地网-本月签到数
define('D_HONOR_CITY_LIST', 'd_honor_city_list');
//荣誉榜城市设置//订单
define('D_LONGRENT_COMMISSION_CFG', 'd_longrent_commission_cfg');
// 长包车任务配置
define('D_SEPARATE_CFG', 'd_separate_cfg');
// 分账配置
define('D_SEPARATE_QUERY_INFO', 'd_separate_query_info');
define('D_BEGIN_CHARGE_LOCK', 'd_begin_charge_lock');
// 开始计费加锁
define('D_WITHDRAW_TAX_CFG', 'd_withdraw_tax_cfg');
// 提现税率配置
define('D_CITY_PRICE_BY_STRATEGY', 'd_city_price_by_strategy');
// 策略计价
define('D_PANGU_DAY_ASSIGN', 'd_pangu_day_assign');
//司机盘古活动指派完成单数、完成率
define('D_PANGU_DAYS_ASSIGN', 'd_pangu_days_assign');
//司机盘古活动指派完成单数、完成率
define('D_DRIVER_STAR_FROM_CREDIT', 'd_driver_star_from_credit');
//司机从信用系统获取到的星级
define('M_PANGU_CONFIG', 'm_pangu_config');
//盘古活动
define('M_PANGU_RULE', 'm_pangu_rule');
//盘古活动下的配置
define('M_PANGU_RULE_DB_INFO', 'm_pangu_rule_db_info');
//盘古活动下的配置
define('M_PANGU_REWARD', 'm_pangu_reward');
//盘古活动下的奖励
define('D_MENUS_DEFAULT', 'd_menus_default');
//司机端默认菜单缓存
define('D_MENUS_BY_AREA', 'd_menus_by_area');
define('D_RECORD_LATEST_COMMENT_TAGS', 'd_record_latest_comment_tags');
//存储乘客评价司机的最近N条评论标签
define('D_HIGHEST_FREQUENCY_COMMENT_TAGS', 'd_highest_frequency_comment_tags');
//存储乘客评级司机的频次最高的N条评价标签以及相应评价次数
define('D_ORDER_DRIVER_COMMENT_PASSENGER_LOCK', 'd_order_driver_comment_passenger_lock');
//司机评价乘客
define('D_SEPARATE_SPLIT_CFG', 'd_separate_split_cfg');
// 分账配置
define('D_QRCODE_INFO', 'd_qrcode_info');
//司机二维码信息
define('D_WEEK_REASSIGN_ORDER_DUTY_NUM', 'd_week_reassign_order_duty_num');
//司机每周有责改派订单数目
define('D_REASSIGN_ORDER_DUTY_TOTAL_NUM', 'd_reassign_order_duty_total_num');
//司机总的有责改派次数
define('D_MEET_PASSENGER_EXEMPT_NUM', 'd_meet_passenger_exempt_num');
//司机遇到乘客免责取消次数
define('O_FINISH_COORDINATES', 'o_finish_coordinates');
//完成订单轨迹
define('D_DRIVER_ASSIGN_RATE', 'd_driver_assign_rate');
//司机本周指派完成率
define('D_NEW_DRIVER_PROGRESS_BAR_SHOW', 'd_new_driver_progress_bar_show');
//新司机是否已打开分层进度条
define('D_ORDER_NUM', 'd_order_num');
//荣誉榜城市设置//订单
define('D_DRIVER_PERWEEK_GET_QUESTION_NUM', 'd_driver_perweek_get_question_num');
//荣誉榜城市设置//订单
define('D_RESEND_ORDER_STATUS_STORED', 'd_resend_order_status_stored');
// 存储改派订单状态
define('D_RESEND_ORDER_STATUS_STORED_WEEK', 'd_resend_order_status_stored_week');
//存储改派订单状态1周，为企业使用
define('D_FREERIDE_INFO', 'd_freeride_info');
//顺风车信息
define('D_FREERIDE_REMIND_INFO', 'd_freeride_remind_info');
//顺风车提醒 +driver_id
define('D_WITHDRAW_DAY_REMIND', 'd_withdraw_day_remind');
//司机提现日提醒
define('D_COUPON_OUT_OF_TIME', 'd_coupon_out_of_time');
//司机券过期提醒
define('D_NEW_PERCENT_COMING', 'd_new_percent_coming');
//有新的分账到了提醒
define('D_DRIVER_LEVEL_GROWTH', 'd_driver_level_growth');
//司机等级与成长值之间的关系
define('D_BIND_COUPONS_LIST', 'd_bind_coupons_list');
//司机查看绑定券
define('D_LAST_SEND_SMS_TIME', 'd_last_send_sms_time');
//司机上次催款成功给乘客发送短信的时间
define('D_LAST_FINISH_ORDER_TIME', 'd_last_finish_order_time');
//司机最近一次完成订单时间信息
define('D_DRIVER_INSERVICE_STATE', 'd_driver_inservice_state');
//司机服务中的状态记录（统计成长值用）
define('D_LOWEST_DEDUCT_LEVEL_GROWTH', 'd_lowest_deduct_level_growth');
//记录司机等级中最低一个开始不扣分等级的分数值
// 司机端查询模块
define('D_EARNINGS_INQUIRY_WEEK', 'd_earnings_inquiry_week');
// 司机流水查询周数据
define('D_EARNINGS_INQUIRY_DAY', 'd_earnings_inquiry_day');
// 司机流水查询周数据
//raptor
define('D_RAPTOR_LOGIN', 'd_raptor_login');
// 存储司机是否首次登陆
define('D_RAPTOR_CONFIG', 'd_raptor_config');
// 存储城市配置信息
define('D_RAPTOR_TASK', 'd_raptor_task');
// 存储司机任务信息
define('D_RAPTOR_ORDER_TOTAL', 'd_raptor_order_total');
// 存储司机任务信息
define('D_RAPTOR_RANK', 'd_raptor_rank');
// 存储司机任务信息
define('D_RAPTOR_RANK_TOP', 'd_raptor_rank_top');
// 存储司机任务信息
define('D_RAPTOR_TRANSFER_LOCK', 'd_raptor_transfer_lock');
// 存储司机任务信息
define('D_RAPTOR_DISCONNECT', 'd_raptor_disconnect');
// 存储司机任务信息
define('D_RAPTOR_REMIND', 'd_raptor_remind');
// 存储司机任务信息
define('D_CITY_HONOUR_LIST', 'd_city_honour_list');
// 司机荣誉榜缓存
define('D_ANONY_COMMENT', 'd_anony_comment');
//匿名评价
define('D_DISTRICT_USING_ACTIVITY', 'd_district_using_activity');
//查询模块的  活动之历史活动
define('D_DISTRICT_HISTORY_ACTIVITY', 'd_district_history_activity');
//查询模块的  活动之正在进行的活动
define('D_DISTRICT_PANGU_USING_ACTIVITY', 'd_district_pangu_using_activity');
//盘古mis修改
define('D_DISTRICT_PANGU_HISTORY_ACTIVITY', 'd_district_pangu_history_activity');
//盘古mis修改 历史
define('D_ORDER_RESEND_CNT', 'd_order_resend_cnt');
//司机订单改派的次数
//ticket缓存
define('D_CHECK_TICKET_CACHE', 'd_check_ticket_cache');
//司机ticket增加缓存，降低passport请求量
define('D_RECHARGE_CACHE_LOCK', 'd_recharge_cache_lock');
//司机代充值锁
//司机异手机登录
define('D_DRIVER_LATEST_DEVICES', 'd_driver_latest_devices');
define('D_DRIVER_FREEZE', 'd_driver_freeze');
define('D_DRIVER_CAPTCHA_ERROR_NUM', 'd_driver_captcha_error_num');
define('D_DRIVER_CAPTCHA', 'd_driver_captcha');
define('D_DISTRICT_CAR_LEVELS', 'd_district_car_levels');
//城市开通车型
define('D_BRAND_NAME', 'd_brand_name');
//车辆品牌.车系名
define('D_HISTORY_ORDER_BY_DRIVER_ID', 'd_history_order_by_driver_id');
define('D_PAUSE_CARPOOL', 'd_pause_carpool');
define('P_GET_BARRENGE','p_get_barrenge');//获取弹幕
//司机目的地订单
define('D_DEST_EDIT_NUM', 'd_dest_edit_num');
//每天司机修改的目的地次数
//司机请求交班时间
define('D_JIAOBAN_REQ_TIME', 'd_jiaoban_req_time');
//司机请求交班的时间控制定时队列数量
define('D_JIAOBAN_REQ_LOCK', 'd_jiaoban_req_lock');
//司机请求交班请求锁
define('D_NOT_ASSIGN_DEST_EDIT_NUM', 'd_not_assign_dest_edit_num');
//抢单模式下每天司机修改的目的地次数
define('D_FIRST_ON_CAR_EVERY_DAY', 'd_first_on_car_every_day');
//司机每天第一次出车
define('D_CARPOOL_DRIVER_ORDER', 'd_carpool_driver_order');
// 拼车司机订单
define('D_PREDICT_NEAR_HOT_TIME', 'd_predict_neat_hot_time');
// 设置司机播放预测热力图失效
define('D_PREDICT_NEAR_HOT_WHITELIST', 'd_predict_neat_hot_whitelist');
// 设置司机播放预测热力图白名单
define('D_FINISH_ORDER_BROAD_WHITELIST', 'd_finish_order_broad_whitelist');
// 司机结束计费是否可播放set
define('D_PREDICT_NEAR_HOT_DISTANCE', 'd_predict_neat_hot_distance');
// 设置司机预测热力图距离
define('O_INFO', 'o_info');
//订单信息
define('O_BASIC_INFO', 'o_basic_info');
//订单信息
define('O_EXTRA_INFO', 'o_extra_info');
//订单信息
define('O_DRIVER_PULL_FLAG', 'o_driver_pull_flag');
//司机拉单操作记录
define('O_PUSH_INFO', 'o_push_info');
//与策略耦合的, 给未抢单的司机推送'订单已被抢'的消息
define('O_ORDER_RESULT_INFO', 'o_order_result_info');
// 订单计价信息
define('O_ORDER_RESULT_DRIVER_INFO', 'o_order_result_driver_info');
// 订单计价信息
define('O_ORDER_CMP_INFO', 'o_order_cmp_info');
// 订单评价信息
define('O_ORDER_REALTIME_VALUATION_INFO', 'o_order_realtime_valuation_info');
define('O_ORDER_LAST_POINT', 'o_order_last_point');
// 上传坐标最后一个点
define('O_ORDER_PUNISH_FORWARD', 'o_order_punish_forwrd');
// 订单惩罚奖励+order_id
define('O_ORDER_RECALL_LIMIT', 'o_order_recall_limit');
//订单重叫时间限制
define('O_GETRESENDDRIVERIDBYORDERID', 'o_getresenddriveridbyorderid');
//根据订单获得改派司机ID
define('O_GETORDERID_BY_DISTANCE', 'o_getorderid_by_distance');
// 根据距离获得订单ID
define('O_LATE', 'o_late');
//预计司机迟到的订单id +district
define('O_PUSH_LOG', 'o_push_log');
//订单状态实时推 id-district
define('O_TIME_DISTANCE', 'o_time_distance');
//行驶时间,距离超时id +district
define('O_ORDER_ETA', 'o_order_eta');
// ETA时间
define('O_REALTIME_PRICING_NETWORK', 'o_realtime_pricing_network');
// network点缓存
define('O_REALTIME_PRICING_GPS', 'o_realtime_pricing_gps');
// gps点缓存
define('O_BEGIN_CHARGE_LOC', 'o_begin_charge_loc');
// gps点缓存
define('O_YUYING_IS_ORDER', 'o_yuying_is_order');
// 判断是否鱼鹰订单
define('O_KEY_LOCK', 'o_key_lock');
// 订单key 加锁
define('O_HIDE_ADDRESS_AREA', 'o_hide_address_area');
// 订单屏蔽坐标前缀
define('O_AREA_LIST', 'o_area_list');
//订单地区列表parent_id
define('O_FROM_TO_DISTANCE_INFO', 'o_from_to_distance_info');
//出发地和目的地之间路面距离和行驶时间
define('O_SAME_ADDRESS', 'o_same_address');
//相同出发地和目的地订单
define('O_STRATEGY_CONF_DISTRICT', 'o_strategy_conf_district');
//城市下套餐缓存
define('O_CLOSE_PRICE_SEPARATE_CITY_PRODUCT', 'o_close_price_separate_city_product');
//计价分离开关
define('O_CLOSE_AIRPORT_ACTIVITY_CITY_PRODUCT', 'o_close_airport_activity_city_product');
//机场活动开关
define('O_CLOSE_AIRPORT_GUIDE_CITY_PRODUCT', 'o_close_airport_guide_city_product');
//快车接送机导流开关
define('O_AIRPORT_GUIDE_CACHE_DATA', 'o_airport_guide_cache_data');
//快车接送机导流开关
define('O_ACTIVITY_FREQUENCY', 'o_activity_frequency');
//活动频次控制
define('O_MAPPING_COMBOID_AIRPORTID', 'o_mapping_comboid_airportid');
define('O_FLIGHT_NO', 'o_flight_no');
//订单对应航班号
define('O_AIRPORT_POI_CHECK', 'o_airport_poi_check');
//接送机订单坐标检查结果
define('O_COMMON_FLIGHT_DATA', 'o_common_flight_data');
//航班信息数据集
define('O_AIRPORT_ORDER_RESEND', 'o_airport_order_resend');
//改派机场订单
define('O_ORDER_FLIGHT_DATA', 'o_order_flight_data');
//订单航班数据集，field为订单id，val为航班的基本信息
define('O_ORDER_DEVICE_RELATION', 'o_order_device_relation');
////订单设备对应关系
define('O_BASIC_INSURE_INFO', 'o_basic_insure_info');
////订单保险信息
define('O_ORDERID_FREQUENCY_BLACKLIST', 'o_oid_freq_black');
// 订单加密orderid的黑名单前缀
define('O_ORDERID_FREQUENCY_COUNT', 'o_oid_freq_count');
// 订单加密orderid的频率计数
define('O_ORDER_POI_INFO', 'o_order_poi_info');
// 订单POI信息
//openApi相关
define('OPEN_B2B_FORE_OID_MAPPING', 'open_b2b_fore_oid_mapping');
//外部订单id和湾流订单id映射关系
define('OPEN_PINGAN_ACCESS_TOKEN', 'open_pingan_access_token');
//平安保险投保访问令牌
//网点端二维码series
define('D_SIGNED_SCANED_QRCODE', 'd_signed_scaned_qrcode');
define('O_GET_PWD_CNT', 'o_get_pwd_cnt');
define('O_ACCESS_QRCODE_CNT', 'o_access_qrcode_cnt');
define('O_STATION_LOGIN_FAIL', 'o_station_login_fail');
define('O_STATION_INFO', 'o_station_info');
define('O_CHECK_LONGRENT_NUM', 'o_check_longrent_num');
define('O_SEP_RECORD_CONF', 'o_sep_record_conf');
define('O_SEP_RECORD_DETAIL', 'o_sep_record_detail');
define('O_SEP_ACTUAL_PERCENT', 'o_sep_actual_percent');
define('O_SEP_DRIVER_BY_AREA_LEVEL_MODEL', 'o_sep_driver_by_area_level_model');
define('O_RELATION_TRAFFIC_INFO', 'o_relation_traffic_info');
//订单对应关系+order_id+district+orderData
define('O_MULTI_CAR_LEVEL', 'o_multi_car_level');
// 多车型
define('O_BONUS_CACHE', 'o_bonus_cache');
// 奖励缓存 +oid+district
define('O_STRATEGY_CONF', 'o_strategy_conf');
// 同步计价配置
define('O_STRATEGY_TOKEN', 'o_strategy_token');
// 计价token
define('O_MULTI_STRATEGY_CONF', 'o_multi_strategy_conf');
// 多计价token
define('O_OLD_DELAY_TIME_FLAG', 'o_old_delay_time_flag');
// 老的迟到计价标记
define('O_NEW_DELAY_TIME_FLAG', 'o_new_delay_time_flag');
// 新的迟到计价标记
define('O_NEW_BEGIN_CHARGE_FLAG', 'o_new_begin_charge_flag');
//新的开始计费标记
define('O_FREE_CLOSE_ORDER_DAY_COUNTER', 'o_free_close_order_day_counter');
//每天openapi关单的数量计数器
define('O_PASSENGER_FREE_CLOSE_ORDER_DAY_COUNTER', 'o_passenger_free_close_order_day_counter');
//每天某个乘客openapi关单的数量计数器
define('O_FAR_ORDER_FLAG', 'o_far_order_flag');
//远程判断标志
//司机分层体系
define('O_RANK_RECORD_CONF', 'o_rank_record_conf');
define('O_RANK_ACTUAL_PERCENT', 'o_rank_actual_percent');
define('O_RANK_RECORD_DETAIL', 'o_rank_record_detail');
define('O_RANK_RECORD_INFO', 'o_rank_record_info');
define('O_DRIVER_RANK_FOR_SORT', 'o_driver_rank_for_sort');
//利用zset对司机得分数据进行排序
define('O_DRIVER_RANK_PERCENT_DISPLAY', 'o_driver_rank_percent_display');
//乘客端
define('P_INFO_BY_TOKEN', 'p_info_by_token');
//passport乘客基础信息
define('P_INFO_BY_TOKEN_ORDER', 'p_info_by_token_order');
//passport乘客基础信息,发单专用
define('P_INFO_BY_PHONE', 'p_info_by_phone');
//passport乘客基础信息 +phone
define('P_INFO_CACHE_BY_TOKEN', 'p_info_cache_by_token');
//passport乘客基础信息
define('P_PASSENGER_BASIC_INFO', 'p_passenger_basic_info');
//乘客基础信息
define('P_ORDER_CACHE_LOCK', 'p_order_cache_lock');
//订单缓存锁
define('P_ORDER_COMMENT_LOCK', 'p_order_comment_lock');
//订单评论锁
define('P_ORDER_CACHE_LOCK_AVOID_CANCEL', 'p_order_cache_lock_avoid_cancel');
//订单入库前缓存锁
define('P_ORDER_CANCEL_TRIP_LOCK', 'p_order_cancel_trip_lock');
//取消行程锁
define('P_QUESTION_LOCK', 'p_question_lock');
//问卷调查锁
define('P_FEE_DISSENT_BASIC_INFO', 'p_fee_dissent_basic_info');
//费用异议
define('P_ADDRESS_BASIC_INFO', 'p_address_basic_info');
//邮寄地址
define('P_ADDRESS_MD5_INFO', 'p_address_md5_info');
//邮寄地址md5
define('P_HISTORY_ORDER_IDS', 'p_history_order_ids');
//乘客历史订单
define('P_NEW_HISTORY_ORDER_IDS', 'p_new_history_order_ids');
//乘客历史订单使用有序列表
define('P_LIMIT_HITED_USER', 'p_limit_hited_user');
//限流已匹配的目标用户
define('P_LOCATION', 'p_location');
//记录经纬度
define('P_LIMIT_DAY_COUNT', 'p_limit_day_count');
//限流已匹配的目标用户
define('P_DRAWIN_USER', 'p_drawin_user');
//引流用户列表
define('P_NEAR_DRIVERS', 'p_near_drivers');
//乘客当前坐标周边司机
define('P_NEAR_PRE_DRIVER_PHONE', 'p_near_pre_driver_phone');
//乘客当前坐标周边指定司机，通过电话号码指定
define('P_NEAR_PRE_DRIVER_ID', 'p_near_pre_driver_id');
//乘客当前坐标周边指定司机，通过司机id指定
define('P_OVERDRAFT', 'p_overdraft');
//乘客欠款订单
define('P_SEND_MSG', 'p_send_msg');
//记录是否发送消息
define('P_SEND_WAIT_DRIVER', 'p_send_wait_driver');
//等待司机接驾给乘客推送消息
define('P_DRIVER_DISTANCE_TIME', 'p_driver_distance_time');
//司机和乘客之间距离、时间
define('P_DACHE_OPENID', 'p_dache_openid');
//存储打车业务的openid
define('P_LATE_ORDER_ID', 'p_late_order_id');
//乘客最近订单号, 用于创建订单时去重
define('P_RECENT_ORDER_ID', 'p_recent_order_id');
//乘客最近订单号, 用于崩溃恢复
define('P_RECENT_ORDER_ID_BY_PHONE', 'p_recent_order_id_by_phone');
//乘客最近订单号按乘客手机号存储, 用于企业平台崩溃恢复
define('P_CALLCAR_RECENT_ORDER_ID', 'p_callcar_recent_order_id');
//乘客最近代叫车订单号
define('P_HISTORY_ORDER_LIST', 'p_history_order_list');
//乘客历史订单
define('P_LIMIT_CITY_RATIO', 'p_limit_city_ratio');
//限流围栏内用户看见专车比例
define('P_VIP_ADD_USER', 'p_vip_add_user');
//添加VIP用户缓存锁
define('P_GUIDE_WAITE_TIME', 'p_guide_waite_time');
//导流窗口出现时间
define('P_REDIS_CACHE_TAG', 'p_redis_cache_tag');
//号码保护提示
define('P_PLATFORM_INCREASE_LOCK', 'p_platform_increase_lock');
// 平台加价给司机转账加锁
define('P_PANGU_LOCK', 'p_pangu_lock');
// 盘古转账加锁
define('P_NEW_PANGU_LOCK', 'p_new_pangu_lock');
// 盘古转账加锁
define('P_QUESTION_DRIVERS', 'p_question_drivers');
//问卷需求司机池
define('P_WXAGENT_PUSH', 'p_wxagent_push');
//微信代扣签约push提醒消息次数 +pid
define('P_WXAGENT_BIND_STATUS', 'p_wxagent_bind_status');
//微信代扣签约状态 +pid
define('P_ORDER_DEFAULT_COUPON', 'p_order_default_coupon');
//微信代扣签约状态 +pid
define('P_REUNION_FESTIVAL_WHITELIST', 'p_reunion_festival_whitelist');
//回家白名单
define('P_REUNION_SEND_TIP', 'p_reunion_send_tip');
//回家白名单
define('P_GUIDE_FROM_TAXI_OR_GULFSTREAM', 'p_guide_from_taxi_or_gulfstream');
//#出租车导流到专车时标记此专车单来自出租车导流
define('P_UNFASTCAR_USER', 'p_unfastcar_user');
//快车目标用户
define('P_HAS_INVOICED_ORDER_LIST', 'p_has_invoiced_order_list');
//发票行程列表
define('P_AVAIL_INVOICED_ORDER_LIST', 'p_avail_invoiced_order_list');
//用户可开票行程列表
define('P_INVOICE_PASSENGER_HISTORY', 'p_invoice_passenger_history');
//用户历史开票行程列表
define('P_INVOICE_USERINFO', 'p_invoice_userinfo');
//乘客申请开票用户信息记录
define('P_INVOICE_DETAIL', 'p_invoice_detail');
//开票信息详情
define('P_USER_GUIDE_WAIT_TIME', 'p_user_guide_wait_time');
//导流高价值用户弹出时间
define('P_DIST_GUIDE_PARAM', 'p_dist_guide_param');
//导流高价值用户各区系数
define('P_GUIDANCE', 'p_guidance');
//快车版本导流策略存放 导流时间和导流方向
define('P_COUNT_INFO', 'p_count_info');
//乘客技术信息
define('P_VERSIONID', 'p_versionid');
//乘客版本ID
define('P_APP_VERSIONID', 'p_app_versionid');
//乘客版本ID
define('P_GET_LINE_SEND_MSG', 'p_get_line_send_msg');
//乘客推送拉取路线消息
define('P_ESTIMATEPRICE_DYNAMIC_PRICE_TEMP', 'p_estimateprice_dynamic_price_temp');
//预估气泡调用策略动态调价缓存
define('P_ESTIMATE_CONFIRM_INFO', 'p_estimate_confirm_info');
define('P_ESTIMATE_FEE_INFO', 'p_estimate_fee_info');
define('P_ESTIMATE_ID', 'p_estimate_id');
define('P_ESTIMATE_SELECTED_ID', 'p_estimate_selected_id');
define('P_ESTIMATE_CARPOOL_ID', 'p_estimate_carpool_id');
define('P_ESTIMATE_TRACE_ID', 'p_estimate_trace_id');
define('P_ESTIMATE_SHOW_PRODUCT_LIST', 'p_estimate_show_product_list');
define('P_ESTIMATE_ID_JOIN_CARPOOL', 'p_estimate_id_join_carpool');
//动调页缓存数据
define('P_ESTIMATE_DYNAMIC_PRICE_PAGE_DATA', 'p_estimate_dynamic_price_page_data');
define('P_ESTIMATEPRICE_DYNAMIC_PRICE', 'p_estimateprice_dynamic_price');
//预估气泡调用策略动态调价缓存
define('P_ESTIMATEPRICE_DYNAMIC_PRICE_MULTI', 'p_estimateprice_dynamic_price_multi');
//预估气泡调用策略动态调价缓存, anycar多车型
define('P_ESTIMATE_COUPON_INFO', 'p_estimate_coupon_info');
//预估券信息缓存
define('P_ANYCAR_ESTIMATE_PRICE', 'p_anycar_estimate_price');
//anycar预估缓存数据，数据来源于price-api
define('P_LIMIT_FEE_TIP_SHOW_NUM', 'p_limit_fee_tip_show_num');
//乘客最低消费tip展示次数
define('P_LIMIT_FEE', 'p_limit_fee');
define('P_SHOW_LMIT_TIP_TIME', 'p_show_limit_time');
define('P_NEW_LASTEST_TIME', 'p_new_lastest_time');
//ETA接驾时间
define('P_LASTEST_TIME', 'p_lastest_time');
//周边司机最近接驾时间
define('P_HISTORY_ORDER_BY_PASSENGER_ID', 'p_history_order_by_passenger_id');
define('P_MINUS_SUPPLY_NUM', 'p_minus_supply_num');
//立减活动补偿次数
define('P_TOPIC_INFO', 'p_topic_info');
//营销活动
define('P_BANNER_INFO', 'p_banner_info');
//专车banner
define('P_CARPOOL_ESTIMATE_PRICE', 'p_carpool_estimate_price');
//拼车一口价预估价缓存 数据来自计价系统
define('P_DISCARDED_ORDER_TAG', 'p_discarded_order_tag');
//丢弃订单标记
define('P_ESTIMATE_PRICE_CURRENCY', 'p_estimate_price_currency');
//预估计价币种
define('P_NONCARPOOL_ESTIMATE_PRICE', 'p_noncarpool_estimate_price');
//非拼车一口价南航接送机预估价缓存 数据来自计价系统
define('P_PREPAY_ORDER_CACHE', 'p_prepay_order_cache');
// 预付订单缓存
define('P_UPDATE_DEST_PREPAY_CACHE', 'p_update_dest_prepay');
//更新目的地预付缓存
define('P_DUSE_EXPIRE', 'p_duse_expire');
// duse播单超时缓存
define('P_BROADCAST_CACHE', 'p_broadcast_cache');
// 播单信息缓存
define('P_FORMAT_PASSENGER_INFO', 'p_format_passenger_info');
//获取拼友头像昵称缓存
define('P_FORMAT_PASSENGER_LEVEL', 'p_format_passenger_level');
//获取乘客等级缓存
define('P_GET_REL_PASSENGER', 'p_get_rel_passenger');
//获取拼友行程规划缓存
define('P_CARPOOL_CONTROL_CONFIG', 'p_carpool_control_config');
//管控配置
define('P_UPDATE_DISPLAY_PRICE', 'p_update_display_price');
//乘客取消行程更新拼友订单对应司机展示价格
define('P_MUST_COMMENT_AB_TEST', 'p_must_comment_ab_test');
//必须评论ab test key
define('P_FASTCAR_MINI_NOTICE_SMS', 'p_fastcar_mini_notice_sms');
//mini滴订单短信通知缓存
define('P_ACT_FASTENSURE', 'p_act_fastensure');
//快车险缓存
define('P_CITY_FASTENSURE_CFG', 'p_city_fastensure_cfg');
//快车险开通城市缓存
define('P_CITIES_AIRPORT_SHUTTLE', 'p_cities_airport_shuttle');
//开通接送机服务的城市
define('P_CITY_AIRPORT_DATA_SET', 'p_city_airport_data_set');
//城市机场信息
define('P_GUIDE_UNIQ_DATA', 'p_guide_uniq_data');
//导流数据
define('P_INVOICE_CACHE_LOCK', 'p_invoice_cache_lock');
//乘客开票锁
define('P_CANCEL_FASTCAR_TRIP_NUM', 'p_cancel_fastcar_trip_num');
//乘客当天取消快车订单数
define('P_CARPOINT_START_CHOOSE', 'p_car_point_start_choose');
//乘客上车点起点，字符串类型
define('P_RED_PACKET_LAST_VALUE', 'p_red_packet_last_value');
//乘客红包所见红包价格
define('P_OPERATION_ACTIVITY_TYPE', 'p_operation_activity_type');
//预估价格加价司机感知,枚举值
define('P_ESTIMATE_CARPOOL_FLAT_RATE_ROUTE', 'p_estimate_carpool_flat_rate_route');
//拼车是否命中区域一口价路线缓存
define('P_CARPOOL_WALK_STATION_CONFIRM', 'p_carpool_walk_station_confirm');
//拼车愿走下车点是否确定
define('P_CARPOOL_MATCH_SHOW_TYPE', 'p_carpool_match_show_type');
//拼车等待接驾时多个页面切换
define('P_CARPOOL_DECISION', 'p_carpool_decision');
//乘客拼车意愿，是否拼车
define('P_BUBBLE_TAG', 'p_bubble_tag');
// 定价调价，存储用户红点状态
define('P_ADJUST_PRICE_RED_DOT_TAG', 'p_adjust_price_red_dot_tag');
//乘客拼车意愿，是否拼车
define('P_CANCEL_ORDER_DUTY_TOTAL_NUM', 'p_cancel_order_duty_total_num');
//乘客总的有责取消次数
define('P_MEET_DRIVER_EXEMPT_NUM', 'p_meet_driver_exempt_num');
//乘客遇到司机免责改派次数
define('P_NEW_CONTROL_CONFIG', 'p_new_control_config');
define('P_CARPOOL_MIS_CFG', 'p_carpool_mis_cfg');
//拼车开城mis化
define('P_CARPOOL_MIS_CFG_NEW', 'p_carpool_mis_cfg_new');
//拼车开城mis化
define('P_BUBBLE_CACHE_BILL_PRODUCT_INFO', 'p_bubble_cache_bill_product_info');
//预估时缓存账单产品信息，供发单时使用
define('P_CARPOINT_START', 'p_car_point_start');
//乘客上车点起点
define('P_COMMENT_HISTORY', 'p_comment_history');
//乘客评价订单记录
define('P_FRIEND_COUPON', 'p_friend_coupon');
//webapp朋友券分享
define('P_AU_AIRPORT_GUIDE_ORDER', 'p_au_airport_guide_order');
//澳洲机场引导订单
//低价值订单强制加价的消费记录
define('P_LOW_ORDER_TIP', 'p_low_order_tip');
define('P_WEEK_CANCEL_ORDER_DUTY_NUM', 'p_week_cancel_order_duty_num');
define('P_CARPOOL_FIRST_TIPS', 'p_carpool_first_tips');
//标识拼车首次提示的push 防止重复发
define('P_CARPOOL_FIRST_PASSENGER_DETOUR_METRE', 'p_carpool_first_passenger_detour_metre');
//拼车第一位上车乘客拼成后绕路距离
define('P_CIP_PASSENGER_SET', 'p_cip_passenger_set');
//有cip资质的用户set
define('P_CIP_AIRPORT_SET', 'p_cip_airport_set');
//开通cip的机场
define('P_CIP_PASSENGER_NUM', 'p_cip_passenger_num');
//用户获得的cip券个数
define('AIRPORT_ONBOARD_TIP_SET', 'airport_onboard_tip_set');
//开通上车点的机场+航站楼
//openapi
define('P_CONTROL_CONFIG', 'p_control_config');
define('P_CARPOOL_FREQUENCY_CONTROL', 'p_carpool_frequency_control');
//拼车评论限制
define('P_CLIENT_MENU_CFG', 'p_client_menu_cfg');
//二级菜单配置
define('P_SPECIAL_CAR_ORDERS', 'p_special_car_orders');
//历史订单+pid
define('P_FESTIVAL_ALARM', 'p_festival_alarm');
//三站两场提醒
define('P_INFO_CACHE_BY_TOKEN_ORDER', 'p_info_cache_by_token_order');
//passport发单后乘客基础信息
define('P_CANCEL_TRIP_LOCK', 'p_cancel_trip_lock');
//乘客取消行程锁
define('P_CARPOOL_COMMUTE_TO_WORK', 'p_carpool_commute_to_work');
//乘客端命中拼车上班场景h5展示锁
define('P_CARPOOL_COMMUTE_FROM_WORK', 'p_carpool_commute_from_work');
//乘客端命中拼车下班班场景h5展示锁
define('P_CARPOOL_XIAOBA_GUIDE', 'p_carpool_xiaoba_guide');
//小巴新手引导页控制次数
define('P_CARPOOL_ACTIVITY_RELATION_PASSENGER_HEAD', 'p_carpool_activity_relation_passenger_head');
define('P_LOW_PRICE_CARPOOL_SHOW_BUBBLE', 'p_low_price_carpool_show_bubble');
define('P_SPECIAL_CAR_H5_GUIDE', 'p_special_car_h5_guide');
define('P_SHOW_WEBAPP_COUPON_INFO_REFUSE', 'p_show_webapp_coupon_info_refuse');
//本月回巢预估阻断不展示优惠券
define('P_SHOW_WEBAPP_COUPON_INFO', 'p_show_webapp_coupon_info');
//回巢预估阻断展示优惠券
//老版本检查乘客紧急联系人
define('P_CHECK_USER_EMERGENCY_CONTACT', 'p_check_user_emergency_contact');
// 设置自己为紧急联系引导转化
define('P_CHECK_YOURSELF_CONTACT_TRANSFER', 'p_check_yourself_contact_transfer');

//物品遗失
// 司机已经完成的物品遗失订单
define('P_LOSS_REMAND_DRIVER_FINISHED_ORDER', 'p_loss_remand_driver_finished_order');
// 乘客已经完成的物品遗失订单
define('P_LOSS_REMAND_PASSENGER_FINISHED_ORDER', 'p_loss_remand_passenger_finished_order');
// 针对同一个司机已经，乘客完成的物品遗失订单
define('P_LOSS_REMAND_SAME_DRIVER_FINISHED_ORDER', 'p_loss_remand_same_driver_finished_order');
// 针对同一个司机的并发锁
define('P_LOSS_REMAND_DRIVER_CONCURRENCY_LOCK', 'p_loss_remand_driver_concurrency_lock');

define('P_CHECK_OLD_VERSION_SMS_AUTH_RECORD', 'p_check_old_version_sms_auth_record');
// 老版本短信授权录音
define('P_CHECK_OLD_VERSION_SMS_AUTH_RECORD_CNT', 'p_check_old_version_sms_auth_record_cnt');
// 老版本短信授权短信发送次数
define('P_INSER_ORDER_BROADCAST_QUEUE', 'p_prepay_insert_order_broadcast_queue');
// 命中乘客语音授权订单
define('P_CHECK_PASSENGER_RECORD_HIT', 'p_check_passenger_record_hit');
//openapi
define('API_SMS_DOWNLOAD', 'api_sms_download');
//下载链接 短信发送次数
define('API_WEIXIN_MP_ACCESS_TOKEN', 'api_weixin_mp_access_token');
//小桔科技公众号接口访问access_token
//车型
define('CAR_LEVEL_LIST', 'car_level_list');
//所有车型
define('CAR_LEVEL_LIST_RAW', 'car_level_list_raw');
//所有车型，g_car_level表原始数据
//anti spam
define('A_P_NEWORDER', 'a_p_neworder');
//乘客创建订单频率控制
define('A_P_CANCELORDER', 'a_p_cancelorder');
//乘客取消订单频率控制
define('A_P_CANCEL_REAL_ORDER', 'a_p_cancel_real_order');
//乘客取消订单频率控制
define('A_P_SAME_PASSENGER_DRIVER_DEALORDER', 'a_p_same_passenger_driver_dealorder');
//乘客与司机成交频率控制
define('A_P_PASSENGER_DEAL_ORDER', 'a_p_passenger_deal_order_frequency');
//乘客特定时间内成交限制
define('A_P_BINDIDBYPID', 'a_p_bindidbypid');
//Pid获取的真身Bindid
define('A_P_BINDID_ARRAY_BYPID', 'a_p_bindid_array_bypid');
//pid获取真身数组，包括阿里和微信、手q
define('A_P_WHITE_LIST', 'a_p_white_list');
//白名单
define('A_P_TEST_LIST', 'a_p_test_list');
//内测名单
define('A_P_WEBAPP_TEST_LIST', 'a_p_webapp_test_list');
//webapp内测名单
define('A_P_GUIDE_LIST', 'a_p_guide_list');
//引流防打扰
define('A_P_CANCEL_GUIDE_LIST', 'a_p_cancel_guide_list');
//取消打车引流防打扰
define('A_P_BLACK_LIST', 'a_p_black_list');
//黑名单用户
define('A_P_CANCEL_GUIDE_FOR_TAXI', 'a_p_cancel_guide_for_taxi');
//取消专车引流防打扰
define('A_P_WAIT_GUIDE_FOR_TAXI', 'a_p_wait_guide_for_taxi');
//等待专车引流防打扰
define('A_P_CANCEL_GUIDE_FAST', 'a_p_cancel_guide_fast');
//取消快车防打扰
define('A_P_WAIT_GUIDE_FAST', 'a_p_wait_guide_fast');
//等待快车防打扰
//商业变现滴滴播报广告频率控制
define('C_R_DIDIPUSH_AD_DAY', 'c_r_didipush_ad_day');
//商业变现滴滴播报广告司机推送一天频率控制
//mis kefu
define('K_COMPLAINT_COUNT', 'k_complaint_count');
//mis长包车
define('D_ONLINE_TIME', 'd_online_time');
//司机在线时长
define('D_ONLINE_LAST_DRIVERS', 'd_online_last_drivers');
//司机在线时长
define('D_MONITOR', 'd_monitor');
//地图展板
define('D_NEW_ONLINE_LAST_DRIVERS', 'd_new_online_last_drivers');
//工资结算
define('D_BASIC_WAGE_USED_CFG', 'd_basic_wage_used_cfg');
//计算基本工资使用的key
define('D_INSURANCE_USED_CFG', 'd_insurance_used_cfg');
//计算基本工资使用的key
define('D_BASIC_WAGE_WEEK_LIMIT_CFG', 'd_basic_wage_week_limit_cfg');
//基本工资周限制
define('D_BASIC_WAGE_MONTH_LIMIT_CFG', 'd_basic_wage_month_limit_cfg');
//基本工资月限制
define('D_PROTECTION_FEE_CFG', 'd_protection_fee_cfg');
//车辆租金配置
//mis预警订单取消
define('O_WARN_TIMES', 'o_warn_times');
//用户发送次数超限
define('O_WARN_ETA', 'o_warn_eta');
//eta预警
define('O_WARN_ETA_LATE', 'o_warn_eta_late');
//eta预警已经迟到
define('O_WARN_WAIT', 'o_warn_wait');
//等候超过10分钟
define('O_WARN_TRAVEL_TIME', 'o_warn_travel_time');
//行驶时间超过2小时
define('O_WARN_TRAVEL_DISTANCE', 'o_warn_travel_distance');
define('O_WARN_REASSIGN', 'o_warn_reassign');
//司机改派
define('O_WARN_NOT_PAY', 'o_warn_not_pay');
//已结束未支付1小时
define('O_WARN_TRAVEL_TWO_DISTANCE', 'o_warn_travel_two_distance');
//实际行驶距离超过预估距离1倍
define('O_WARN_TRAVEL_FEE', 'o_warn_travel_fee');
//行驶时间5分钟，价格还是起步价
define('O_WARN_TRAVEL_TWO_FEE', 'o_warn_travel_two_fee');
//实际价格超过预估价格1倍的
//mis司机类型每月统计
define('O_STATISTIC_DATE_TYPE', 'o_statistic_date_type');
//邀约订单访问uv记录
define('O_INVITATION_ORDER_VISIT_UV', 'o_invitation_order_visit_uv');
define('M_DAILY_LEVEL_STATIS_BYAREA', 'm_daily_level_statis_byarea');
//分城市的每日星级评价统计
define('M_ACCOUNT_ROLE_INFO', 'm_account_role_info');
//mis账户拥有的权限
define('M_ACTIVITY_EFFECTIVE_AREA', 'm_activity_effective_area');
//MIS盘古活动区域
define('M_ACTIVITY_EFFECTIVE_CFG', 'm_activity_effective_cfg');
//MIS盘古活动配置
//司机服务问卷  优惠券已发放张数
define('M_QUESTION_COUPON_NUM', 'm_question_coupon_num');
//司机推荐用户数限制
define('M_DRIVER_RECOMMEND_CNT', 'm_driver_recommend_cnt');
//mis权限点
define('M_USER_PRIVILEGE', 'm_user_privilege');
//滴滴播报上传临时数据
define('M_DIDI_PUSH_UPLOAD', 'm_didi_push_upload');
//mis权限 城市管理
define('M_USER_PRIVILEGE_CITY', 'm_user_privilege_city');
define('M_AIRPORT_ALL', 'm_airport_all');
//全部机场缓存
define('M_DISTRICT_MAP_AREA', 'm_district_map_area');
//区号到区域号的映射
//第三方
define('TP_VARIFLIGHT_INFO', 'tp_variflight_info');
//非常准信息 +fnum（航班号）+flightDate（起飞日期）
define('TP_VARIFLIGHT_ADDPUSH', 'tp_variflight_addpush');
//定制航班信息推送 +fnum（航班号）+flightDate（起飞日期）+dep(起始地三字母)+arr(目的地三字母)
define('TOOL_TRAFFIC_INFO', 'tool_traffic_info');
//交通信息+traffic_num
define('TOOL_TRAFFIC_LIST', 'tool_traffic_list');
//交通信息列表+traffic_num 模糊查询
define('TOOL_CITY_AIRPORT', 'tool_city_airport');
//前缀+md5(城市名+区号)
define('D_REG_NUM_DRIVER', 'd_reg_num_driver');
//司机注册统计数量专用
define('D_TMP_NUM_DRIVER', 'd_tmp_num_driver');
//当前注册数增量
//通用问卷系统
define('Q_QUES_LIBRARY', 'q_ques_library');
//题库数据
define('Q_QUES_SURVEY', 'q_ques_survey');
//问卷数据
define('Q_QUES_EXTRACT', 'q_ques_extract');
//问卷题目配置
define('Q_QUES_TOPIC', 'q_ques_topic');
//答题
define('Q_QUES_TOPIC_IDS', 'q_ques_topic_ids');
//题目id集合
define('Q_COUNT_GROUP_DRIVER', 'q_count_group_driver');
//分组汇总司机总数
define('Q_QUES_SEND', 'q_ques_send');
//发放调研问卷
//司机在线考试
define('Q_EXAM_LOGIN', 'q_exam_login');
//考官登陆验证key
define('Q_EXAM_DRIVER', 'q_exam_driver');
//考试司机验证
define('M_SMS_REG', 'm_sms_reg');
//司机注册链接 短信发送次数
//司机model 统计司机在线专用
define('D_MODEL', 'd_model');
//统计司机在线专用，司机model
//司机服务参数
define('M_DRIVER_CANCEL', 'm_driver_cancel');
//司机订单取消率
define('M_DRIVER_COMPLAINT', 'm_driver_complaint');
//司机订单投诉率
define('M_DRIVER_AVGARRIVE', 'm_driver_avgarrive');
//司机订单平均接驾时间
define('M_DRIVER_AVGLEVEL', 'm_driver_avglevel');
//司机申请升级相关
define('MIS_CAR_BRAND', 'mis_car_brand');
//车辆品牌型号
define('D_CAR_BRAND', 'd_car_brand');
//车辆品牌型号
define('MIS_CAR_REL_MC', 'mis_car_rel_mc');
//申请状态缓存10分钟
define('MIS_CAR_REL_CAR_MC', 'mis_car_rel_car_mc');
//关联车信息缓存10分钟
define('MIS_DRIVER_UPGRADE_MC', 'mis_driver_upgrade_mc');
//历史状态缓存10分钟
define('MIS_ORDER_EXT_MC', 'mis_order_ext_mc');
//缓存订单信息缓存10分钟
//滴滴美食节
define('O_COORDINATE_BEGIN_FINISH', 'o_coordinate_begin_finish');
//订单开始计价、完成订单坐标
//订单fsource
define('O_FSOURCE', 'o_fsource');
//乘客发单source
define('O_POOL_SEAT_DISCOUNT', 'o_pool_seat_discount');
//拼座订单折扣
define('TAGS_BY_CATEGORY', 'tags_by_category');
//按栏目缓存标签
define('POSITIVETGS', 'positivetags');
//司机标签缓存
//记录司机当前行程的订单ID---拼车使用
define('D_CARPOOL_CURRENT_TRAVELORDER', 'd_carpool_current_travelorder');
//记录司机车上拼友---拼车使用
define('D_CARPOOL_ROUTE_ORDER', 'd_carpool_route_order');
//记录行程变更---拼车使用
define('D_CARPOOL_ROUTE_UPDATE', 'd_carpool_route_update');
//拼成奖励
define('P_CARPOOL_REWARD_INFO', 'p_carpool_reward_info');
define('P_CARPOOL_REWARD_SEND_MSG', 'p_carpool_reward_send_msg');
define('P_CARPOOL_SALES_COUPON', 'p_carpool_sales_coupon');
define('P_CARPOOL_COOPERATE_MSG', 'p_carpool_cooperate_msg');
define('P_ORDER_LIKE_WAIT_FLAG', 'p_order_like_wait_flag');
//订单是否是愿等订单
//拼车相关P_PASSENGER_ORDER_NUM_INFO
define('P_PASSENGER_ORDER_NUM_INFO', 'p_passenger_order_num_info');
//乘客次数统计key
//每天司机完成抢单数D_DRIVER_COMPET_ORDER_NUM
define('D_DRIVER_COMPET_ORDER_NUM', 'd_driver_compet_order_num');
define('O_FLIGHT_ABNORMAL_ORDER', 'o_flight_abnormal_order');
//异常航班的key
//司机tag信息
define('D_TAG_INFO', 'd_tag_info');
//tag集合下司机列表
define('D_TAG_REL', 'd_tag_rel');
//乘客tag
define('P_TAG_INFO', 'p_tag_info');
//tag集合下乘客列表
define('P_TAG_REL', 'p_tag_rel');
//订单盘古奖励结果缓存
define('O_PANGU_REWARD', 'o_pangu_reward');
//转账信息缓存
define('O_TRANSFER', 'o_transfer');
//push filter order 锁
define('O_PUSH_FILTER_LOCK', 'o_push_filter_lock');
define('D_ONLINE_TAGS', 'd_online_tags');
define('D_PUSH_FILTER_ORDER', 'd_push_filter_order');
//dPushFilter Order去重处理
define('D_FREERIDE_SWITCH_SET', 'd_freeride_switch_set');
//顺风车导流开关白名单集合
define('D_COMPET_ORDER_LIMIT_SET', 'd_compet_order_limit_set');
//抢单限制白名单集合
define('D_ASSIGN_REJECT_STAT', 'd_assign_reject_stat');
//指派拒接统计当日拒接数
define('D_ASSIGN_REJECT_STATUS', 'd_assign_reject_status');
//司机是否因指派拒接超过阀值而被封禁
define('D_GOOD_DRIVER', 'd_good_driver');
//好司机
define('D_GOOD_DRIVER_IMPORT', 'd_good_driver_import');
//好司机当天导入成功标志
define('P_FLAG_AGENT_REMIND', 'p_flag_agent_remind');
//是否需要提醒免密签约
define('D_LISTEN_ORDER_STATUS', 'd_listen_order_status');
//司机听单状态
define('D_LAST_DISPLAY_STATUS', 'd_last_display_status');
//上次向lbs推送是否显示车标的状态 #1:不显示 #2:显示
define('D_BANNER_SETS', 'd_banner_sets');
//司机首页banner列表id
define('D_BANNER_INFO', 'd_banner_info');
//司机首页banner详情
define('D_DIRECT_DRIVER_SET', 'd_direct_driver_set');
//直营车不能向下听单白名单
define('D_PRI_AIRPORT_DRIVER_SET', 'd_pri_airport_driver_set');
//倾斜听机场单白名单
define('O_TRACK_LOG_FREQUENCY_CONTROL', 'o_track_log_frequency_control');
//有针对性的采样订单记录track.log
define('P_LAST_IP', 'p_last_ip');
//乘客上次发单ip
define('P_LAST_DEVICE', 'p_last_device');
//企业是否播给顺路司机
define('P_LINEUP_BROADCAST_TIME', 'p_lineup_broadcast_time');
//排队订单拨单时间
define('P_LINEUP_INIT_RANK', 'p_lineup_init_rank');
//进入排队的初始位置
define('O_ORDERID_ESTIMATE_STATIONINFO', 'o_orderid_estimate_stationinfo');
//根据订单ID获取station info key+order_id
define('P_TRACEID_ESTIMATE_STATIONINFO', 'p_traceid_estimate_stationinfo');
// 存站点信息 key+trace_id
define('P_SCENE_FEATURE', 'p_scene_feature');
//场景自动打tag
define('P_TRACEID_ESTIMATE_STATIONLIST', 'p_traceid_estimate_stationlist');
// 存站点列表信息 key+trace_id
define('P_MIS_HOLIDAY_AREA', 'p_mis_holiday_area');
//mis地区节假日配置，当天信息 key＋date＋area
define('P_MIS_HOLIDAY_COUNTRY', 'p_mis_holiday_country');
//mis全国节假日配置，当天信息 key＋date
define('P_TRACEID_ESTIMATE_COMBOTYPE', 'p_traceid_estimate_combotype');
// 命中机场单combotype key+trace_id
define('P_TRACEID_ESTIMATE_ROUTE_GROUP', 'p_traceid_estimate_route_group');
//跨城拼车父路线id
define('P_XIAOBA_NEW_ORDER_TIMES', 'p_xiaoba_new_order_times');
//发过小巴单次数
define('P_CARPOOL_ESTIMATE_ARRIVAL', 'p_carpool_estimate_arrival');
// 预估拼车ETD信息
define('P_SPECIAL_RATE_POOL_SEAT', 'p_special_rate_pool_seat');
//特价拼车实验
define('P_SCAN_PAY_BILLID', 'p_scan_pay_billid');
// 拼车时间片
define('P_CARPOOL_TIME_RANGE','p_carpool_time_range');
//扫码付
define('P_SCAN_PAY_DRIVER_MAP', 'p_scan_pay_driver_map');
//扫码付司机id映射
define('P_SCAN_PAY_CARD', 'p_scan_pay_card');
//扫码付空白二维码
define('C_CAR_MODEL_IMG', 'c_car_model_img');
//车型图片
define('P_CARPOOL_COMMUTE_CARD', 'p_carpool_commute_card');

//拼车周卡拒绝记录（弹出气泡相关）
define('P_CARPOOL_COMMUTE_CARD_REJECTION_RECORD_LIST', 'p_carpool_commute_card_rejection_record_list');
define('P_CARPOOL_COMMUTE_H5_REJECT_RECORD','p_carpool_commute_h5_reject_record');

//拼车周卡
define('P_COMMUTE_INFO', 'p_commute_info');
define('P_ESTIMATE_AIRPORT_STATION_CONTROL', 'p_estimate_airport_station_control');
//拼车是否命中区域一口价路线缓存
define('P_INSURANCE_INFO', 'p_insurance_info');
//保险人信息缓存 + estimate_id price-api写
define('P_ORDER_MATCH_EXPERIENCE_GROUP', 'p_order_match_experience_group');
//所命中的实验组
define('P_CARPOOL_COMMUTE_FREE_CARD', 'p_carpool_commute_free_card');
// ui component cache
define('P_COMPONENT_CACHE_PREFIX', 'p_component_cache');
// activity type cache
define('P_ACTIVITY_TYPE_PREFIX', 'p_activity_type_cache');
//城际司机扫码发单信息
define('D_INTER_DRIVER_QRCODE','d_inter_qrcode');

//城际拼车单预估阶段快车价格
define('P_INTER_CITY_BUBBLE_FAST_CAR_ESTIMATE_FEE','p_inter_city_bubble_fast_car_estimate_fee');

//单个产品含券预估价格
define('P_PRODUCT_ACTIVITY_FEE','p_product_activity_fee');

//拼车EXT策略缓存
define('P_CARPOOL_EXT_INFO','p_carpool_ext_info');

//拼车ETS缓存
define('P_CARPOOL_ETS_INFO', 'p_carpool_ets_info');
//邀约同行匹配结果缓存
define('P_INVITATION_MATCH_INFO', 'p_invitation_match_info');
//邀约同行订单分享信息缓存
define('P_INVITATION_SHARE_ORDER_INFO', 'p_invitation_share_order_info');
//预估eid关联
define('P_ESTIMATE_ID_LINK_PREFIX', 'p_estimate_id_link_prefix');
//estimate_id关联命中业务围栏id缓存
define('P_ESTIMATE_ID_FENCE_ID_LINK_PREFIX', 'p_estimate_id_fence_id_link_prefix');

//打包拼友
define('P_CARPOOL_PACK_FRIENDS','p_carpool_pack_friends');

define('P_60_GUIDE_BUBBLE_CACHE', 'p_60_scene_guide_bubble_cache');
define('P_60_GUIDE_BUBBLE_NUM_CACHE', 'p_60_scene_guide_bubble_num_cache');

// 导流折扣类型
define('P_GUIDE_DISCOUNT_FORM', 'p_guide_discount_form');
//新业务新客引导冒泡缓存
define('P_NEW_USER_BUBBLE_GUIDE', 'p_new_user_bubble_guide');
define('P_NEW_USER_BUBBLE_GUIDE_REWARD', 'p_new_user_bubble_guide_reward');

define('P_LIKE_WAIT_BONUS_EXTRA_DATA', 'p_like_wait_bonus_extra_data');

// pGetOrderMatchInfo时请求queuePredict，Athena返回的订单当前信息（现只用名次字段，pPreCancelOrder确保展示尽量一致）
define('O_ATHENA_QUEUE_PREDICT', 'o_athena_queue_predict');
//价格沟通组件
define('P_SPECIAL_PRICE_COMPONENT', 'p_special_price_component');
// 8.0气泡数据
define('P_ONE_STOP_BUBBLE_DATA', 'p_one_stop_bubble_data');
// 默勾弹窗组件
define('P_DEFAULT_SELECT_EJECT_COMPONENT', 'p_default_select_eject_component');
// 默勾沟通组件
define('P_DEFAULT_SELECT_COMMUNICATE_COMPONENT', 'p_default_select_communicate_component');
//6.0导流数据缓存
define('O_DACHE_ANYCAR_GUIDE_INFO', 'o_dache_anycar_guide_info');
// 拼车日弹窗时间频控
define('P_CARPOOL_DAY_POPUP_DURATION', 'p_carpool_day_popup_duration');
//预估页活动气泡
define('P_ESTIMATE_BUBBLE_ACTIVITY', 'p_estimate_bubble_activity');
//预估页返回拦截弹窗
define('P_ESTIMATE_BUBBLE_CANCEL_INTERCEPT', 'p_estimate_bubble_cancel_intercept');
// 弹窗推荐可追加车型反作弊命中结果
define('O_MULTI_ADD_PRODUCT_INTERCEPT_RESULT', 'o_multi_add_product_intercept_result');

// 用户勾选追加车型的反作弊命中结果
define('O_MULTI_REQUIRE_PRODUCT_INTERCEPT_RESULT', 'o_multi_require_product_intercept_result');

//石家庄开服乘客体温确定
define('P_CONFIRM_TEMPERATURE', 'p_confirm_temperature');

//香港出租车小费
define('P_HK_TAXI_TIP_INFO', 'p_hk_taxi_tip_info');
//香港出租车捎话
define('P_HK_TAXI_COMMENT_INFO', 'p_hk_taxi_comment_info');
//车大联盟和快车的EID关联缓存(临时方案：发单拿快车的EID查询车大的EID)
define('P_SPACIOUS_CAR_EID', 'p_spacious_car_eid');
//司乘一口价初次进入命中扶持拼车一座显示的状态
define('P_CARPOOL_PAGE_SUPPORT_ONE_SEAT', 'p_carpool_page_support_one_seat');

//用traceID当索引记录一些预估的基础数据,报价单里没有的那些，给沟通组件渲染表单信息用
define('P_ESTIMATE_ORIGIN_DATA', 'p_estimate_origin_data');
//拼成乐导流沟通组件频次控制
define('P_CARPOOL_GUIDE_COMMUNICATION', 'p_carpool_guide_communication');
//节假日服务费宣教页沟通组件频次控制
define('P_RED_PACKET_TOP_RULE_COMMUNICATE', 'p_red_packet_top_rule_communicate');
// 香港一口价出租车强沟通价格
define('P_HK_CAP_TAXI_FORCE_COMMUNICATION', 'p_hk_cap_taxi_force_communication');
// 等待应答追加车型预估数据缓存(pOrderMatch接口写入)
define('P_GUIDE_ANYCAR_ESTIMATE_PRICE', 'p_guide_anycar_estimate_price');
//anycar预估缓存数据，数据来源于price-api,tpinfo
define('P_GUIDE_ANYCAR_ESTIMATE_PRICE_TP_INFO', 'p_guide_anycar_estimate_price_tp_info');
// 取消挽留追加拼车出口卡片缓存 -- 用于修改座位数时获取上一次的卡片数据
define('P_PRECANCEL_APPEND_CARPOOL_CARD', 'p_precancel_append_carpool_card');
// 司乘议价砍价按钮点击改价引导气泡频控缓存
define('P_BARGAIN_GUIDE_BAR_BARGAIN_BTN_BUBBLE', 'p_bargain_guide_bar_bargain_btn_bubble');
//内循环账户折扣沟通组件频次控制
define('P_REVOLVING_ACCOUNT_DISCOUNT_COMMUNICATION', 'p_revolving_account_discount_communication');
//内循环账户返利沟通组件频次控制
define('P_REVOLVING_ACCOUNT_REBATE_COMMUNICATION', 'p_revolving_account_rebate_communication');
//内循环账户N倍返返利沟通组件频次控制
define('P_REVOLVING_ACCOUNT_REBATE_N_FOLD_COMMUNICATION', 'p_revolving_account_rebate_n_fold_communication');
// 儿童票弹层组件
define('P_MAMBA_SEAT_SELECTION_INFO', 'p_mamba_seat_selection_info');
// 个性化弹窗弱引导
define('P_BUBBLE_WEAK_GUIDANCE_COMMUNICATION', 'p_bubble_weak_guidance_communication');
// 长单感知优化场景
define('O_AMOUNT_OF_NOTIFIED_DRIVERS', 'o_amount_of_notified_drivers');

/*
 * redis key常量定义请去constants.php文件定义
 *
 */
$config['prefix_redis'] = array(
    //司机端redis前缀
    'p_confirm_temperature'                           => 'P_CONFIRM_TEMPERATURE_',  //石家庄开服 预估id的设置
    'd_basic_info'                                    => 'D_BASIC_INFO_', //司机基础信息 +did
    'd_ext_info'                                      => 'D_EXT_INFO_', //司机扩展信息 +did
    'd_conf_info'                                     => 'D_CONF_INFO_', //司机配置信息 +did
    'd_getdriveridbyphone'                            => 'D_GETDRIVERIDBYPHONE_', //通过手机号获取driver_id +phone
    'd_push_time'                                     => 'D_PUSH_TIME_', //根据司机ID记录推送时间 +did
    'd_push_order_time'                               => 'D_PUSH_ORDER_TIME_', //记录给司机推送订单的时间 +did+oid
    'd_jiasuqi_white_list'                            => 'D_JIASUQI_WHITE_LIST_', //司机白名单（加速器）+ did
    'd_idcard_info'                                   => 'D_IDCARD_INFO_', // 通过司机ID获取身份证相关信息
    'd_idcard'                                        => 'D_IDCARD_', // 通过司机ID获取身份证号
    'd_confirm_code_info'                             => 'D_CONFIRM_CODE_INFO_', // 验证码相关信息
    'd_sendcode'                                      => 'D_SENDCODE_', // 记录验证码 + driver_phone
    'd_company_basic_info'                            => 'D_COMPANY_BASIC_INFO_', // 记录司机所属公司基本信息 + company_id
    'd_national_company_basic_info'                   => 'D_NATIONAL_COMPANY_BASIC_INFO_', // 记录司机所属国际化公司基本信息 + company_id
    'd_location'                                      => 'DRIVER_LOC_', // 记录经纬度 + did  ----  为了满足打车业务PUSH调用, 忽略格式问题
    'd_app_version'                                   => 'D_APP_VERSION_', // 记录司机版本信息 + did
    'd_car_basic_info'                                => 'D_CAR_BASIC_INFO_', // 记录司机所开车辆基本信息 + carid
    'd_carid'                                         => 'D_CARID_', //记录司机所开车辆id+did
    'd_photo'                                         => 'D_PHOTO_', //记录司机图像+did
    'd_photo_v2'                                      => 'D_PHOTO_V2_',            //记录司机图像+did
    'd_photo_v3'                                      => 'D_PHOTO_V3_',            //记录司机图像+did
    'd_photo_v4'                                      => 'D_PHOTO_V4_',            //记录司机图像+did
    'c_car_model_img'                                 => 'C_CAR_MODEL_IMG', //车辆型号图片
    'd_resendorder_filter'                            => 'D_RESENDORDER_FILTER_', //司机已改派订单，重新播报订单对该司机过滤
    'd_find_driver_num'                               => 'D_FIND_DRIVER_NUM_', //获得司机数目 + oid
    'd_getdriveridbyidcarno'                          => 'D_GETDRIVERIDBYIDCARDNO_', //通过身份证号获取driver_id
    'd_change_pwd_error_times'                        => 'D_CHANGE_PWD_ERROR_TIMES_', //司机修改密码错误次数+phone
    'd_get_by_passort_uid'                            => 'D_GET_BY_PASSPORT_UID_', //司机修改密码错误次数+phone
    'd_get_day_info'                                  => 'D_GET_DAY_INFO_', // 获取司机当天在线时长 + did
    'd_set_day_info_timestamp'                        => 'D_SET_DAY_INFO_TIMESTAMP_', // 设置在线时长的起始时间戳 + did
    'd_totalfee'                                      => 'D_TOTALFEE_', // 设置司机总金额 + did+district
    'd_current_order_num'                             => 'D_CURRENT_ORDER_NUM_', // 司机当天抢单数
    'd_forbidden_one_day'                             => 'D_FORBIDDEN_ONE_DAY_', // 司机封禁一天
    'd_honor_list'                                    => 'D_HONOR_LIST_',
    'd_long_rent_order_num'                           => 'D_LONG_RENT_ORDER_NUM_', // 长包车当天抢单数
    'd_getcaridbyplateno'                             => 'D_GETCARIDBYPLATENO_', // 通过车牌号获取车辆id
    'd_getdriverlistbycarid'                          => 'D_GETDRIVERLISTBYCARID_', //通过车辆id获取司机id
    'd_rank_order_num_area'                           => 'D_RANK_ORDER_NUM_AREA_', //通过车辆id获取司机id
    'd_driver_latest_devices'                         => 'D_DRIVER_LATEST_DEVICES_', //通过司机id获取司机最近登录的N个设备
    'd_driver_freeze'                                 => 'D_DRIVER_FREEZE_', //司机被冻结前缀
    'd_driver_captcha_error_num'                      => 'D_DRIVER_CAPTCHA_ERROR_NUM_', //司机连续输入验证码错误次数
    'd_month_order_cost'                              => 'D_MONTH_ORDER_COST_', //获取司机某月的订单费用
    'new_d_month_order_cost'                          => 'NEW_D_MONTH_ORDER_COST_', //司乘计价分离后，获取司机某月的订单费用
    'd_driver_captcha'                                => 'D_DRIVER_CAPTCHA_', //司机换设备验证码
    'd_month_level'                                   => 'D_MONTH_LEVEL_', //获取司机某月的订单费用
    'd_realtimepricing_last_fee_lock'                 => 'D_REALTIMEPRICING_LAST_FEE_LOCK_', // 实时计价上次费用加锁
    'd_finish_order_lock'                             => 'D_FINISH_ORDER_LOCK_', // 完成计费加锁
    'd_recharge_lock'                                 => 'D_RECHARGE_LOCK_', // 代充值加锁
    'd_withdraw_cnt'                                  => 'D_WITHDRAW_CNT_', // 司机提现次数限制
    'd_withdraw_money'                                => 'D_WITHDRAW_MONEY_', // 司机提现金额限制
    'd_withdraw_lock'                                 => 'D_WITHDRAW_LOCK_', // 司机提现加锁
    'd_honor_city_list'                               => 'D_HONOR_CITY_LIST', //荣誉榜城市设置
    'd_signed_day_imei'                               => 'D_SIGNED_DAY_IMEI_', //地网-此imei号今日签到的手机号码
    'd_signed_day_flag'                               => 'D_SIGNED_DAY_FLAG_', //地网-今日是否签到
    'd_signed_week_count'                             => 'D_SIGNED_WEEK_COUNT_', //地网-本周签到数
    'd_signed_month_count'                            => 'D_SIGNED_MONTH_COUNT_', //地网-本月签到数
    'd_district_car_levels'                           => 'D_DISTRICT_CAR_LEVELS_',
    'd_brand_name'                                    => 'D_BRAND_NAME_', //车辆品牌.车系名
    // 司机端查询模块
    'd_earnings_inquiry_week'                         => 'D_EARNINGS_INQUIRY_WEEK', // 司机流水查询周数据
    'd_earnings_inquiry_day'                          => 'D_EARNINGS_INQUIRY_DAY', // 司机流水查询周数据
    'd_driver_assign_rate'                            => 'D_DRIVER_ASSIGN_RATE_', //司机本周指派完成率
    'd_qrcode_info'                                   => 'D_QRCODE_INFO', //司机二维码信息
    'd_reassign_order_duty_total_num'                 => 'D_REASSIGN_ORDER_DUTY_TOTAL_NUM_',
    'd_meet_passenger_exempt_num'                     => 'D_MEET_PASSENGER_EXEMPT_NUM_',
    //乘客端redis前缀
    'p_format_passenger_info'                         => 'P_FORMAT_PASSENGER_INFO', //获取拼友头像昵称缓存
    'p_format_passenger_level'                        => 'P_FORMAT_PASSENGER_LEVEL', //获取乘客等级缓存
    'p_get_rel_passenger'                             => 'P_GET_REL_PASSENGER', //获取拼友行程规划缓存
    'p_carpool_control_config'                        => 'P_CARPOOL_CONTROL_CONFIG_', //管控配置
    'p_update_display_price'                          => 'P_UPDATE_DISPLAY_PRICE_', //乘客取消行程更新拼友订单对应司机展示价格
    'd_city_price'                                    => 'D_CITY_PRICE_', // 城市计价配置
    'd_city_car_price'                                => 'D_CITY_CAR_PRICE_', //城市车型计价配置
    'd_city_car_default_price'                        => 'D_CITY_CAR_DEFAULT_PRICE_', //城市默认车型计价配置
    'd_withdraw_lock'                                 => 'D_WITHDRAW_LOCK_', // 司机提现加锁
    'd_phone_verify'                                  => 'D_PHONE_VERIFY_', //司机注册手机号验证
    'd_phone_verify_limit'                            => 'D_PHONE_VERIFY_LIMIT_', //司机手机号验证次数限制
    'd_send_code_limit'                               => 'D_SEND_CODE_LIMIT_', //司机手机号验证次数限制
    'd_strive_order_driver_cnt'                       => 'D_STRIVE_ORDER_DRIVER_CNT_', // 记录抢单司机数（供鱼鹰使用）
    'd_countdown_time'                                => 'D_COUNTDOWN_TIME_', // 司机倒计时时间
    'd_city_default_car_level'                        => 'D_CITY_DEFAULT_CAR_LEVEL_', // 城市默认车型
    'd_longrent_commission_cfg'                       => 'D_LONGRENT_COMMISSION_CFG_', // 长包车任务配置
    'd_separate_cfg'                                  => 'D_SEPARATE_CFG_', // 分账配置
    'd_separate_query_info'                           => 'D_SEPARATE_QUERY_INFO_', // 查询司机的分账信息
    'd_begin_charge_lock'                             => 'D_BEGIN_CHARGE_LOCK_', // 计费开始加锁
    'd_withdraw_tax_cfg'                              => 'D_WITHDRAW_TAX_CFG_', // 提现税率配置
    'd_signed_scaned_qrcode'                          => 'D_SIGNED_SCANED_QRCODE_', //地网-签到二维码
    'd_city_price_by_strategy'                        => 'D_CITY_PRICE_BY_STRATEGY_', // 策略计价
    'd_recharge_cache_lock'                           => 'D_RECHARGE_CACHE_LOCK_', //司机代充值锁

    'd_history_order_by_driver_id'                    => 'D_HISTORY_ORDER_BY_DRIVER_ID_', // 司机历史订单
    'd_pause_carpool'                                 => 'D_PAUSE_CARPOOL_', // 司机历史订单
    'p_get_barrenge'                                  => 'P_GET_BARRENGE_',//获取弹幕
    'd_separate_split_cfg'                            => 'D_SEPARATE_SPLIT_CFG_', // 梧桐分账配置

    'o_finish_coordinates'                            => 'O_FINISH_COORDINATES_', //完成订单轨迹

    //司机分层体系
    'o_rank_record_conf'                              => 'O_RANK_RECORD_CONF',
    'o_rank_actual_percent'                           => 'O_RANK_ACTUAL_PERCENT',
    'o_rank_record_detail'                            => 'O_RANK_RECORD_DETAIL',
    'o_rank_record_info'                              => 'O_RANK_RECORD_INFO',
    'o_driver_rank_for_sort'                          => 'O_DRIVER_RANK_FOR_SORT', //利用zset对司机得分数据进行排序
    'o_driver_rank_percent_display'                   => 'O_DRIVER_RANK_PERCENT_DISPLAY',

    //乘客端redis前缀
    'd_countdown_time'                                => 'D_COUNTDOWN_TIME_', // 司机倒计时时间
    'd_little_driver_update_whitelist'                => 'D_LITTLE_DRIVER_UPDATE_WHITELIST_', // 司机小流量升级白名单
    'd_withdraw_notify'                               => 'D_WITHDRAW_NOTIFY_', // 司机提现回调通知key+didibillid
    'd_withdraw_info'                                 => 'D_WITHDRAW_INFO_', // 司机提现记录信息
    'd_order_num'                                     => 'D_ORDER_NUM_', // 当前订单数 +driver_id + 本周开始时间 + 本周结束时间
    'd_driver_perweek_get_question_num'               => 'D_DRIVER_PERWEEK_GET_QUESTION_NUM', //司机每周获取的问卷调查数
    'd_resend_order_status_stored'                    => 'D_RESEND_ORDER_STATUS_STORED_', // 司机改派订单状态存储
    'd_resend_order_status_stored_week'               => 'D_RESEND_ORDER_STATUS_STORED_WEEK_', // 司机改派订单状态存储1周，为企业使用
    //raptor
    'd_new_driver_progress_bar_show'                  => 'D_NEW_DRIVER_PROGRESS_BAR_SHOW_', //新司机是否已打开分层进度条
    'd_raptor_login'                                  => 'D_RAPTOR_LOGIN_',
    'd_raptor_config'                                 => 'D_RAPTOR_CONFIG_',
    'd_raptor_task'                                   => 'D_RAPTOR_TASK_',
    'd_raptor_order_total'                            => 'D_RAPTOR_ORDER_TOTAL_',
    'd_raptor_rank'                                   => 'D_RAPTOR_RANK_',
    'd_raptor_rank_top'                               => 'D_RAPTOR_RANK_TOP_',
    'd_raptor_transfer_lock'                          => 'D_RAPTOR_TRANSFER_LOCK_',
    'd_raptor_disconnect'                             => 'D_RAPTOR_DISCONNECT_',
    'd_raptor_remind'                                 => 'D_RAPTOR_REMIND_',

    'd_freeride_info'                                 => 'D_FREERIDE_INFO_', //司机顺风车信息 + driver_id
    'd_freeride_remind_info'                          => 'D_FREERIDE_REMIND_INFO_', //待提醒消息 + driver_id

    'd_withdraw_day_remind'                           => 'D_WITHDRAW_DAY_REMIND_', //司机提现日提醒
    'd_coupon_out_of_time'                            => 'D_COUPON_OUT_OF_TIME_', //司机券过期提醒
    'd_new_percent_coming'                            => 'D_NEW_PERCENT_COMING_', //司机有新的分账到达
    'd_driver_level_growth'                           => 'D_DRIVER_LEVEL_GROWTH_', //司机层级与成长值的对应
    'd_last_finish_order_time'                        => 'D_LAST_FINISH_ORDER_TIME_', //司机最近一次完成订单时间信息
    'd_driver_inservice_state'                        => 'D_DRIVER_INSERVICE_STATE_', //司机服务中的状态信息
    'd_lowest_deduct_level_growth'                    => 'D_LOWEST_DEDUCT_LEVEL_GROWTH_', //记录司机等级司机最低一层的分数
    'd_bind_coupons_list'                             => 'D_BIND_COUPONS_LIST_', //记录司机绑定的券信息 司机等级
    'd_last_send_sms_time'                            => 'D_LAST_SEND_SMS_TIME_', //司机上次催款成功给乘客发短信的时间
    'd_check_ticket_cache'                            => 'D_CHECK_TICKET_CACHE_', //司机ticket校验增加cache
    'd_city_honour_list'                              => 'D_CITY_HONOUR_LIST_', //司机荣誉榜配置
    'd_order_resend_cnt'                              => 'D_ORDER_RESEND_CNT_', //司机订单改派的次数
    'd_anony_comment'                                 => 'D_ANONY_COMMENT_', //匿名评价
    'd_district_using_activity'                       => 'D_DISTRICT_USING_ACTIVITY', //查询模块的  某区活动之正在进行的活动 +areaid
    'd_district_history_activity'                     => 'D_DISTRICT_HISTORY_ACTIVITY', //查询模块的 某区活动之正在进行的活动  +areaid
    'd_district_pangu_using_activity'                 => 'D_DISTRICT_PANGU_USING_ACTIVITY', //盘古mis配置
    'd_district_pangu_history_activity'               => 'D_DISTRICT_PANGU_HISTORY_ACTIVITY', //盘古mis配置历史
    'd_week_reassign_order_duty_num'                  => 'D_WEEK_REASSIGN_ORDER_DUTY_NUM_', //司机每周有责改派订单数目
    //乘客端redis前缀
    'p_info_by_token'                                 => 'P_INFO_BY_TOKEN', //passport乘客基础信息 +token的md5值
    'p_info_by_token_order'                           => 'P_INFO_BY_TOKEN_ORDER', //passport乘客基础信息 +token的md5值
    'p_info_by_phone'                                 => 'P_INFO_BY_PHONE', //passport乘客基础信息 +phone
    'p_info_cache_by_token'                           => 'P_INFO_CACHE_BY_TOKEN', //passport乘客基础信息+token的md5值
    'p_info_cache_by_token_order'                     => 'P_INFO_CACHE_BY_TOKEN_ORDER', //PASSPROT 发单乘客信息
    'p_passenger_basic_info'                          => 'P_PASSENGER_BASIC_INFO', //乘客基础信息 +pid
    'p_order_cache_lock'                              => 'P_ORDER_CACHE_LOCK', //获取缓存锁
    'p_order_comment_lock'                            => 'P_ORDER_COMMENT_LOCK', //订单评论锁 +oid
    'p_order_cache_lock_avoid_cancel'                 => 'P_ORDER_CACHE_LOCK_AVOID_CANCEL_', //订单入库前缓存锁
    'p_order_cancel_trip_lock'                        => 'P_ORDER_CANCEL_TRIP_LOCK_', //订单取消锁 +oid_did
    'p_question_lock'                                 => 'P_QUESTION_LOCK', //问卷调查锁 +oid
    'p_fee_dissent_basic_info'                        => 'P_FEE_DISSENT_BASIC_INFO', //费用异议 +oid
    'p_address_basic_info'                            => 'P_ADDRESS_BASIC_INFO', //最新邮寄地址 +pid
    'p_address_md5_info'                              => 'P_ADDRESS_MD5_INFO', //邮寄地址 + md5
    'p_history_order_ids'                             => 'P_HISTORY_ORDER_IDS', //乘客历史订单 + pid
    'p_new_history_order_ids'                         => 'P_NEW_HISTORY_ORDER_IDS', //乘客历史订单使用有序列表 + pid
    'p_limit_hited_user'                              => 'P_LIMIT_HITED_USER', //限流已匹配的目标用户 + city + phone
    'p_location'                                      => 'P_LOCATION', //乘客坐标 + pid
    'p_limit_day_count'                               => 'P_LIMIT_DAY_COUNT', //限流已匹配的目标用户 每日数量 + city + date
    'p_drawin_user'                                   => 'P_DRAWIN_USER', //引流用户信息 + city + phone
    'p_near_drivers'                                  => 'P_NEAR_DRIVERS', //乘客当前位置周边司机 + lat +lng
    'p_near_pre_driver_phone'                         => 'P_NEAR_PRE_DRIVER_PHONE', //乘客当前位置周边指定司机 + lat +lng（通过手机号指定司机）
    'p_near_pre_driver_id'                            => 'P_NEAR_PRE_DRIVER_ID', //乘客当前位置周边指定司机 + lat +lng（通过司机id指定司机）
    'p_overdraft'                                     => 'P_OVERDRAFT_', //乘客乘客欠款oid + pid
    'p_send_msg'                                      => 'P_SEND_MSG_', //是否已经发送消息 + pid/did
    'p_send_wait_driver'                              => 'P_SEND_WAIT_DRIVER', //等待司机接驾给乘客推送消息 + oid
    'p_driver_distance_time'                          => 'P_DRIVER_DISTANCE_TIME', //等待司机接驾距离和时间 + oid
    'p_dache_openid'                                  => 'P_DACHE_OPENID', //等待司机接驾距离和时间 + oid
    'p_late_order_id'                                 => 'P_LATE_ORDER_ID', //乘客最近创建的订单 +pid
    'p_recent_order_id'                               => 'P_RECENT_ORDER_ID', //乘客最近创建的订单 +pid
    'p_recent_order_id_by_phone'                      => 'P_RECENT_ORDER_ID_BY_PHONE', //乘客最近创建的订单 +passenger_phone
    'p_callcar_recent_order_id'                       => 'P_CALLCAR_RECENT_ORDER_ID', //乘客代叫车最近创建的订单 +pid
    'p_history_order_list'                            => 'P_HISTORY_ORDER_LIST', //乘客历史订单已完成订单
    'p_invoice_detail'                                => 'P_INVOICE_DETAIL', //某次开票详细信息
    'p_limit_city_ratio'                              => 'P_LIMIT_CITY_RATIO', //限流围栏内用户看见专车比例
    'p_invoice_passenger_history'                     => 'P_INVOICE_PASSENGER_HISTORY', //用户开票历史记录
    'p_vip_add_user'                                  => 'P_VIP_ADD_USER_', //添加VIP用户缓存锁 +pid
    'p_guide_waite_time'                              => 'P_GUIDE_WAITE_TIME', //导流窗口出现时间 +city
    'p_redis_cache_tag'                               => 'P_REDIS_CACHE_TAG', //号码保护 + pid
    'p_platform_increase_lock'                        => 'P_PLATFORM_INCREASE_LOCK', //平台加价给司机转账加锁 +did +oid
    'p_pangu_lock'                                    => 'P_PANGU_LOCK', //盘古转账加锁 +did +oid
    'p_new_pangu_lock'                                => 'P_NEW_PANGU_LOCK', //盘古转账加锁 +did +oid
    'p_question_drivers'                              => 'P_QUESTION_DRIVERS', //问卷需求司机池
    'p_has_invoiced_order_list'                       => 'P_HAS_INVOICED_ORDER_LIST', //发票行程列表
    'p_invoice_userinfo'                              => 'P_INVOICE_USERINFO',
    'p_avail_invoiced_order_list'                     => 'P_AVAIL_INVOICED_ORDER_LIST',
    'p_user_guide_wait_time'                          => 'P_USER_GUIDE_WAIT_TIME', //导流高价值用户
    'p_dist_guide_param'                              => 'P_DIST_GUIDE_PARAM', //导流高价值用户各区系数
    'p_guidance'                                      => 'P_GUIDANCE', //快车版本导流策略存放 导流时间和导流方向
    'p_carpool_commute_to_work'                       => 'P_CARPOOL_COMMUTE_TO_WORK_', //拼车上班场景教育h5页面展示锁
    'p_carpool_commute_from_work'                     => 'P_CARPOOL_COMMUTE_FROM_WORK_', //拼车下班场景教育h5页面展示锁
    'p_carpool_xiaoba_guide'                          => 'P_CARPOOL_XIAOBA_GUIDE_', //小巴新手引导
    'p_carpool_pack_friends'                          => 'P_CARPOOL_PACK_FRIENDS_',
    'p_loss_remand_driver_finished_order'             => 'P_LOSS_REMAND_DRIVER_FINISHED_ORDER_', // 司机已经完成的物品遗失订单
    'p_loss_remand_passenger_finished_order'          => 'P_LOSS_REMAND_PASSENGER_FINISHED_ORDER_', // 乘客已经完成的物品遗失订单
    'p_loss_remand_same_driver_finished_order'        => 'P_LOSS_REMAND_SAME_DRIVER_FINISHED_ORDER_', // 针对同一个司机已经，乘客完成的物品遗失订单
    'p_loss_remand_driver_concurrency_lock'           => 'P_LOSS_REMAND_DRIVER_CONCURRENCY_LOCK_', // 针对同一个司机的并发锁

        //openapi
    'p_wxagent_push'                                  => 'P_WXAGENT_PUSH_', //微信代扣签约push提醒消息次数
    'p_wxagent_bind_status'                           => 'P_WXAGENT_BIND_STATUS_', //微信代扣签约push提醒消息次数
    'p_order_default_coupon'                          => 'P_ORDER_DEFAULT_COUPON_', //默认选券标识
    'p_reunion_festival_whitelist'                    => 'P_REUNION_FESTIVAL_WHITELIST', //回家白名单
    'p_reunion_send_tip'                              => 'P_REUNION_SEND_TIP_', //回家提醒+phone+orderDate
    'p_guide_from_taxi_or_gulfstream'                 => 'P_GUIDE_FROM_TAXI_OR_GULFSTREAM', //导流标志 是出租车导流到专车或者导流到出租车标识
    'p_unfastcar_user'                                => 'P_UNFASTCAR_USER', //快车目标用户
    'p_count_info'                                    => 'P_COUNT_INFO_', //乘客技术信息
    'p_versionid'                                     => 'P_VERSIONID_', //乘客versionId, 原始的根据手机号缓存的,P_APP_VERSIONID_ 上线后删除此key
    'p_app_versionid'                                 => 'P_APP_VERSIONID_', //乘客versionId
    'p_get_line_send_msg'                             => 'P_GET_LINE_SEND_MSG_', //乘客拉取路线
    'p_estimateprice_dynamic_price_temp'              => 'P_ESTIMATEPRICE_DYNAMIC_PRICE_TEMP_', //预估气泡调用策略动态调价缓存+md5（imei+出发地坐标+目的地坐标）+产品线+区号+车型
    'p_estimate_confirm_info'                         => 'P_ESTIMATE_CONFIRM_INFO_',
    'p_estimate_fee_info'                             => 'P_ESTIMATE_FEE_INFO_',
    'p_estimate_id'                                   => 'P_ESTIMATE_ID_', //预估ID，源于账单
    'p_estimate_id_join_carpool'                      => 'P_ESTIMATE_ID_JOIN_CARPOOL_', //关联拼车预估id,用于拼友推荐
    'p_estimate_selected_id'                          => 'P_ESTIMATE_SELECTED_ID_', //最后一次冒泡ID
    'p_estimate_carpool_id'                           => 'P_ESTIMATE_CARPOOL_ID_', //预估拼车estimate_id
    'p_estimate_trace_id'                             => 'P_ESTIMATE_TRACE_ID_', //预估请求trace_id
    'p_estimate_dynamic_price_page_data'              => 'P_ESTIMATE_DYNAMIC_PRICE_PAGE_DATA_', //动调页缓存数据
    'p_estimateprice_dynamic_price'                   => 'P_ESTIMATEPRICE_DYNAMIC_PRICE_', //预估气泡调用策略动态调价缓存+md5（imei+出发地坐标+目的地坐标）+产品线+区号+车型
    'p_estimateprice_dynamic_price_multi'             => 'P_ESTIMATEPRICE_DYNAMIC_PRICE_MULTI_', //预估气泡调用策略动态调价缓存+traceid
    'p_estimate_show_product_list'                    => 'P_ESTIMATE_SHOW_PRODUCT_LIST_', //预估后展示的产品列表 + traceid
    'p_estimate_carpool_flat_rate_route'              => 'P_ESTIMATE_CARPOOL_FLAT_RATE_ROUTE_',
    'p_limit_fee_tip_show_num'                        => 'P_LIMIT_FEE_TIP_SHOW_NUM_', //乘客最低消费tip展示次数
    'p_limit_fee'                                     => 'P_LIMIT_FEE_',
    'p_show_limit_time'                               => 'P_SHOW_LMIT_TIP_TIME_',
    'p_lastest_time'                                  => 'P_LASTEST_TIME_', //周边司机最近接驾时间
    'p_new_lastest_time'                              => 'P_NEW_LASTEST_TIME_', //周边司机最近接驾时间
    'p_minus_supply_num'                              => 'P_MINUS_SUPPLY_NUM_', //立减活动补偿次数
    'p_topic_info'                                    => 'P_TOPIC_INFO_', //营销活动
    'p_banner_info'                                   => 'P_BANNER_INFO_', //专车banner P_BANNER_INFO_区号_1专车或2快车
    'p_estimate_price_currency'                       => 'P_ESTIMATE_PRICE_CURRENCY_', //预估价格币种
    'p_carpool_estimate_price'                        => 'P_CARPOOL_ESTIMATE_PRICE_', //拼车一口价预估价缓存 数据来自计价系统
    'p_loss_remand_estimate_price'                    => 'P_LOSS_REMAND_ESTIMATE_PRICE_', //物品遗失归还订单预估一口价缓存 数据来自计价系统
    'p_discarded_order_tag'                           => 'P_DISCARDED_ORDER_TAG_', //token对应乘客丢单标记
    'p_noncarpool_estimate_price'                     => 'P_NONCARPOOL_ESTIMATE_PRICE_', //费拼车一口价南航接送机预估价缓存 数据来自计价系统
    'p_prepay_order_cache'                            => 'P_PREPAY_ORDER_CACHE_', // 预付订单缓存
    'p_update_dest_prepay'                            => 'C_ORDER_UPDATE_DESTINATION_INFO_',
    'p_duse_expire'                                   => 'P_DUSE_EXPIRE_', // duse播单超时缓存
    'p_broadcast_cache'                               => 'P_BROADCAST_CACHE_', // 播单信息缓存，预付成功后不立即播单时写入
    'p_must_comment_ab_test'                          => 'P_MUST_COMMENT_AB_TEST_', //必须评论abtest
    'p_fastcar_mini_notice_sms'                       => 'P_FASTCAR_MINI_NOTICE_SMS_', //mini滴订单短信通知缓存
    'p_act_fastensure'                                => 'P_ACT_FASTENSURE_', //快车险缓存
    'p_city_fastensure_cfg'                           => 'P_CITY_FASTENSURE_CFG', //快车险开通城市缓存
    'p_comment_history'                               => 'P_COMMENT_HISTORY_', //乘客评价订单记录

    'p_cities_airport_shuttle'                        => 'P_CITIES_AIRPORT_SHUTTLE_', //开通接送机服务的城市
    'p_city_airport_data_set'                         => 'P_CITY_AIRPORT_DATA_SET_', //城市机场集
    'p_guide_uniq_data'                               => 'P_GUIDE_UNIQ_DATA_', //导流数据
    'p_history_order_by_passenger_id'                 => 'P_HISTORY_ORDER_BY_PASSENGER_ID_', // 乘客ID获取历史订单

    'p_invoice_cache_lock'                            => 'P_INVOICE_CACHE_LOCK_', //乘客开票锁
    'p_cancel_fastcar_trip_num'                       => 'P_CANCEL_FASTCAR_TRIP_NUM_', //乘客当天取消快车订单数
    'p_carpool_decision'                              => 'P_CARPOOL_DECISION_', //乘客拼车意愿，是否愿意拼车
    'p_bubble_tag'                                    => 'P_BUBBLE_TAG_', //冒泡特征
    'p_adjust_price_red_dot_tag'                      => 'P_ADJUST_PRICE_RED_DOT_TAG_', //调价红点状态

    'p_car_point_start'                               => 'P_CARPOINT_START_', //乘客上车点起点类型
    'p_car_point_start_choose'                        => 'P_CARPOINT_START_CHOOSE_', //乘客上车点起点类型,字符串类型
    'p_red_packet_last_value'                         => 'P_RED_PACKET_LAST_VALUE_', //乘客红包所见红包价格
    'p_operation_activity_type'                       => 'P_OPERATION_ACTIVITY_TYPE_', //运营活动类型
    'p_special_car_h5_guide'                          => 'P_SPECIAL_CAR_H5_GUIDE_',
    'p_friend_coupon'                                 => 'P_FRIEND_COUPON_', //webapp朋友券分享
    'p_au_airport_guide_order'                        => 'P_AU_AIRPORT_GUIDE_ORDER_',
    'p_low_order_tip'                                 => 'P_LOW_ORDER_TIP', //低价值订单强制加价的消费记录
    'p_week_cancel_order_duty_num'                    => 'P_WEEK_CANCEL_ORDER_DUTY_NUM_', //乘客每周取消次数
    'p_carpool_first_tips'                            => 'P_CARPOOL_FIRST_TIPS_', //标识拼车首次提示的push 防止重复发
    'p_carpool_first_passenger_detour_metre'          => 'P_CARPOOL_FIRST_PASSENGER_DETOUR_METRE_', //拼车第一位上车乘客拼成后绕路距离
    'p_cancel_order_duty_total_num'                   => 'P_CANCEL_ORDER_DUTY_TOTAL_NUM_',
    'p_meet_driver_exempt_num'                        => 'P_MEET_DRIVER_EXEMPT_NUM_',
    'p_new_control_config'                            => 'P_NEW_CONTROL_CONFIG_',
    'p_carpool_mis_cfg'                               => 'P_CARPOOL_MIS_CFG_',
    'p_carpool_mis_cfg_new'                           => 'P_CARPOOL_MIS_CFG_NEW_',
    'p_control_config'                                => 'P_CONTROL_CONFIG_',
    'p_bubble_cache_bill_product_info'                => 'P_BUBBLE_CACHE_BILL_PRODUCT_INFO_',
    'p_estimate_cache_loss_remand_product_info'       => 'P_ESTIMATE_CACHE_LOSS_REMAND_PRODUCT_INFO_',
    'p_carpool_frequency_control'                     => 'P_CARPOOL_FREQUENCY_CONTROL_', //拼车频率限制
    'p_cip_passenger_set'                             => 'P_CIP_PASSENGER_SET', //有cip资质的用户
    'p_cip_airport_set'                               => 'P_CIP_AIRPORT_SET', //开通cip的机场
    'p_cip_passenger_num'                             => 'P_CIP_PASSENGER_NUM_', //用户获得cip券个数
    'p_client_menu_cfg'                               => 'P_CLIENT_MENU_CFG', //二级菜单配置
    'p_special_car_orders'                            => 'P_SPECIAL_CAR_ORDERS_', //历史订单+pid
    'p_festival_alarm'                                => 'P_FESTIVAL_ALARM_', //三站两场提醒'
    'p_cancel_trip_lock'                              => 'P_CANCEL_TRIP_LOCK_',
    'p_show_webapp_coupon_info'                       => 'P_SHOW_WEBAPP_COUPON_INFO_', //回巢预估阻断展示优惠券
    'p_show_webapp_coupon_info_refuse'                => 'P_SHOW_WEBAPP_COUPON_INFO_REFUSE_', //本月回巢预估阻断不展示优惠券
    'airport_onboard_tip_set'                         => 'AIRPORT_ONBOARD_TIP_SET', //开通上车点的机场+航站楼
    'p_check_user_emergency_contact'                  => 'P_CHECK_USER_EMERGENCY_CONTACT_', //老版本检查乘客紧急联系人
    'p_prepay_insert_order_broadcast_queue'           => 'P_INSER_ORDER_BROADCAST_QUEUE_', //预付订单，多轮播单写播单池缓存
    'p_check_old_version_sms_auth_record'             => 'P_CHECK_OLD_VERSION_SMS_AUTH_RECORD_', // 老版本短信授权录音
    'p_check_old_version_sms_auth_record_cnt'         => 'P_CHECK_OLD_VERSION_SMS_AUTH_RECORD_CNT_', // 老版本短信授权短信发送次数
    'p_check_yourself_contact_transfer'               => 'P_CHECK_YOURSELF_CONTACT_TRANSFER_', // 设置自己为紧急联系人引导转化

    //openapi
    'api_sms_download'                                => 'API_SMS_DOWNLOAD_', //下载链接 短信发送次数 + phone
    'api_weixin_mp_access_token'                      => 'API_WEIXIN_MP_ACCESS_TOKEN',   //小桔科技公众号接口访问access_token

    //车型
    'car_level_list'                                  => 'CAR_LEVEL_LIST', //所有车型
    'car_level_list_raw'                              => 'CAR_LEVEL_LIST_RAW', //所有车型，g_car_level表原始数据
    //antispam
    'a_p_neworder'                                    => 'a_p_neworder_', //乘客创建订单频率 + city + pid+ time
    'a_p_cancelorder'                                 => 'a_p_cancelorder_', //乘客取消订单频率 + city + pid+ time
    'a_p_same_passenger_driver_dealorder'             => 'a_p_same_passenger_driver_dealorder_', //乘客与司机成交订单频率 + city + pid+did time
    'a_p_bindidbypid'                                 => 'a_p_bindidbypid_', //由pid获取的乘客真身id + pid
    'a_p_passenger_deal_order_frequency'              => 'a_p_passenger_deal_order_frequency_',
    'a_p_bindid_array_bypid'                          => 'A_P_BINDID_ARRAY_BYPID_', //真身数组缓存
    'a_p_white_list'                                  => 'A_P_WHITE_LIST', // 白名单key
    'a_p_test_list'                                   => 'A_P_TEST_LIST', // 内测人员名单key
    'a_p_webapp_test_list'                            => 'A_P_WEBAPP_TEST_LIST', // webapp内测人员名单key
    'a_p_guide_list'                                  => 'A_P_GUIDE_LIST', //引流防打扰 +pid
    'a_p_cancel_guide_list'                           => 'A_P_CANCEL_GUIDE_LIST', //取消打车引流防打扰 +pid
    'a_p_black_list'                                  => 'A_P_BLACK_LIST', //黑名单用户
    'a_p_cancel_guide_for_taxi'                       => 'A_P_CANCEL_GUIDE_FOR_TAXI', //取消专车引流防打扰
    'a_p_wait_guide_for_taxi'                         => 'A_P_WAIT_GUIDE_FOR_TAXI', //等待专车引流防打扰
    'a_p_cancel_guide_fast'                           => 'A_P_CANCEL_GUIDE_FAST', //取消快车防打扰
    'a_p_wait_guide_fast'                             => 'A_P_WAIT_GUIDE_FAST', //等待快车防打扰

    'c_r_didipush_ad_day'                             => 'C_R_DIDIPUSH_AD_DAY_', //商业变现滴滴播报广告司机推送一天频率控制
    //订单redis前缀
    'o_basic_info'                                    => 'O_BASIC_INFO_', //订单基础信息 + oid
    'o_extra_info'                                    => 'O_EXTRA_INFO_', //订单扩展信息 + oid
    'o_driver_pull_flag'                              => 'O_DRIVER_PULL_FLAG_', //司机拉单操作记录
    'o_push_info'                                     => 'OPUSH_', // 策略制定的前缀
    'o_order_result_info'                             => 'O_ORDER_RESULT_INFO_', // 订单计价信息 + oid
    'o_order_result_driver_info'                      => 'O_ORDER_RESULT_DRIVER_INFO_', // 订单乘客计价信息 + oid
    'o_order_cmp_info'                                => 'O_ORDER_CMP_INFO_', //订单评价信息 + oid
    'o_order_realtime_valuation_info'                 => 'O_ORDER_REALTIME_VALUATION_INFO_', // 订单实时计价 + oid
    'o_order_last_point'                              => 'O_ORDER_LAST_POINT_', // 上传坐标最后一个点 + oid
    'o_order_punish_forwrd'                           => 'O_ORDER_PUNISH_FORWARD_', // 订单惩罚奖励 + oid
    'o_order_recall_limit'                            => 'O_ORDER_RECALL_LIMIT_', //订单重叫限制 + oid
    'o_getresenddriveridbyorderid'                    => 'O_GETRESENDDRIVERIDBYORDERID_', // 根据订单ID获得改派司机ID
    'o_getorderid_by_distance'                        => 'O_GETORDERID_BY_DISTANCE_', // 根据距离获得订单ID(XXX-YYY公里)
    'o_late'                                          => 'O_LATE_', //预计司机迟到的订单id +district
    'o_push_log'                                      => 'O_PUSH_LOG_', ////订单状态实时推 id-district
    'o_time_distance'                                 => 'O_TIME__DISTANCE_', //行驶时间,距离超时id +district
    'o_order_eta'                                     => 'O_ORDER_ETA_', // ETA时间

    'o_realtime_pricing_network'                      => 'O_REALTIME_PRICING_NETWORK_', // network点缓存 +oid+district
    'o_realtime_pricing_gps'                          => 'O_REALTIME_PRICING_GPS_', // gps点缓存 +oid+district
    'o_begin_charge_loc'                              => 'O_BEGIN_CHARGE_LOC_', // gps点缓存 +oid+district
    'o_yuying_is_order'                               => 'O_YUYING_IS_ORDER_', // 判断是否鱼鹰订单
    'o_key_lock'                                      => 'O_KEY_LOCK_', //订单key 加锁
    'o_sep_record_conf'                               => 'O_SEP_RECORD_CONF_', // 分账比例配置
    'o_hide_address_area'                             => 'O_HIDE_ADDRESS_AREA_', //+城市区号，判断订单出发地和目的地是否在屏蔽坐标列表
    'o_sep_record_detail'                             => 'O_SEP_RECORD_DETAIL_', // 分账比例详情
    'o_sep_actual_percent'                            => 'O_SEP_ACTUAL_PERCENT_', // 分账实际比例
    'o_sep_driver_by_area_level_model'                => 'O_SEP_DRIVER_BY_AREA_LEVEL_MODEL_', // 通过area,car_level,join_model获取司机信息
    'o_relation_traffic_info'                         => 'O_RELATION_TRAFFIC_INFO_', //订单对应关系+order_id+district+orderData
    'o_same_address'                                  => 'O_SAME_ADDRESS_', //+pid+md5(出发地和目的地)
    'o_area_list'                                     => 'O_AREA_LIST_', //订单地区列表缓存
    'o_from_to_distance_info'                         => 'O_FROM_TO_DISTANCE_INFO', //出发地和目的地之间路面距离和行驶时间 + flng+flat+tlng+tlat
    'o_multi_car_level'                               => 'O_MULTI_CAR_LEVEL_', // 多车型
    'o_strategy_conf_district'                        => 'O_STRATEGY_CONF_DISTRICT_', ////城市下套餐缓存
    'o_close_price_separate_city_product'             => 'O_CLOSE_PRICE_SEPARATE_CITY_PRODUCT_', //司机和乘客计价分离开关
    'o_close_airport_activity_city_product'           => 'O_CLOSE_AIRPORT_ACTIVITY_CITY_PRODUCT_', //机场活动开关
    'o_close_airport_guide_city_product'              => 'O_CLOSE_AIRPORT_GUIDE_CITY_PRODUCT_', //快车接送机导流开关
    'o_airport_guide_cache_data'                      => 'O_AIRPORT_GUIDE_CACHE_DATA_', //快车接送机导流开关
    'o_activity_frequency'                            => 'O_ACTIVITY_FREQUENCY_', //活动频次控制
    'o_mapping_comboid_airportid'                     => 'O_MAPPING_COMBOID_AIRPORTID_',
    'o_strategy_conf'                                 => 'O_STRATEGY_CONF_', // 同步计价配置
    'o_strategy_token'                                => 'O_STRATEGY_TOKEN_', // 计价token
    'o_multi_strategy_conf'                           => 'O_MULTI_STRATEGY_CONF_', // 多计价token
    'o_old_delay_time_flag'                           => 'O_OLD_DELAY_TIME_FLAG_', // 老的迟到计价逻辑
    'o_new_delay_time_flag'                           => 'O_NEW_DELAY_TIME_FLAG_', // 新的迟到计价逻辑
    'o_new_begin_charge_flag'                         => 'O_NEW_BEGIN_CHARGE_FLAG_', // 新的开始计费逻辑

    'o_bonus_cache'                                   => 'O_BONUS_CACHE_', // +oid+district

    'o_flight_no'                                     => 'O_FLIGHT_NO_', //订单对应航班号
    'o_airport_poi_check'                             => 'O_AIRPORT_POI_CHECK_',
    'o_common_flight_data'                            => 'O_COMMON_FLIGHT_DATA_', //航班数据集
    'o_airport_order_resend'                          => 'O_AIRPORT_ORDER_RESEND_', //改派机场订单
    'o_order_flight_data'                             => 'O_ORDER_FLIGHT_DATA_', //订单航班数据集

    'o_flight_abnormal_order'                         => 'O_FLIGHT_ABNORMAL_ORDER_', //异常航班

    'o_order_device_relation'                         => 'O_ORDER_DEVICE_RELATION_', //订单设备对应关系
    'o_basic_insure_info'                             => 'O_BASIC_INSURE_INFO_', //订单保险信息+订单号
    'open_b2b_fore_oid_mapping'                       => 'OPEN_B2B_FORE_OID_MAPPING_', //外部订单id和湾流订单id映射关系
    'open_pingan_access_token'                        => 'OPEN_PINGAN_ACCESS_TOKEN', //平安保险投保访问令牌

    'o_free_close_order_day_counter'                  => 'O_FREE_CLOSE_ORDER_DAY_COUNTER_',
    'o_passenger_free_close_order_day_counter'        => 'O_PASSENGER_FREE_CLOSE_ORDER_DAY_COUNTER_',
    'o_far_order_flag'                                => 'O_FAR_ORDER_FLAG_', //远程判断标志
    //mis kefu
    'k_complaint_count'                               => 'K_COMPLAINT_COUNT_',
    //mis 长包车
    'd_online_time'                                   => 'D_ONLINE_TIME_',
    'd_online_last_drivers'                           => 'D_ONLINE_LAST_DRIVERS_',
    'd_monitor'                                       => 'D_MONITOR_',
    'd_new_online_last_drivers'                       => 'D_new_ONLINE_LAST_DRIVERS_',

    //工资结算
    'd_basic_wage_used_cfg'                           => 'D_BASIC_WAGE_USED_CFG_',
    'd_insurance_used_cfg'                            => 'D_INSURANCE_USED_CFG_',
    'd_basic_wage_week_limit_cfg'                     => 'D_BASIC_WAGE_WEEK_LIMIT_CFG_',
    'd_basic_wage_month_limit_cfg'                    => 'D_BASIC_WAGE_MONTH_LIMIT_CFG_',
    'd_protection_fee_cfg'                            => 'D_PROTECTION_FEE_CFG_', //车辆租金配置

    //司机目的地订单
    'd_dest_edit_num'                                 => 'D_DEST_EDIT_NUM_', //实时目的地每天修改次数
    'd_jiaoban_req_time'                              => 'D_JIAOBAN_REQ_TIME_', //司机请求交班时间
    'd_jiaoban_req_lock'                              => 'D_JIAOBAN_REQ_LOCK_', //司机请求交班锁
    'd_not_assign_dest_edit_num'                      => 'D_NOT_ASSIGN_DEST_EDIT_NUM_', //抢单模式下实时目的地每天修改次数
    'd_first_on_car_every_day'                        => 'D_FRIST_ON_CAR_EVERY_DAY_', //司机每天第一次出车
    //mis预警订单取消
    'o_warn_times'                                    => 'O_WARN_TIMES_', //发送次数超限
    'o_warn_eta'                                      => 'O_WARN_ETA_', //eta预警
    'o_warn_eta_late'                                 => 'O_WARN_ETA_LATE_', //eta已经迟到
    'o_warn_wait'                                     => 'O_WARN_WAIT_', //等待预警
    'o_warn_travel_time'                              => 'O_WARN_TRAVEL_TIME_', //行驶时间预警
    'o_warn_travel_distance'                          => 'O_WARN_TRAVEL_DISTANCE', //行驶里程预警
    'o_warn_reassign'                                 => 'O_WARN_REASSIGN_', //改派预警
    'o_warn_not_pay'                                  => 'O_WARN_NOT_PAY', //超过1小时未付钱
    'o_statistic_date_type'                           => 'O_STATISTIC_DATE_TYPE_', // mis司机类型每月统计
    'o_warn_travel_two_distance'                      => 'O_WARN_TRAVEL_TWO_DISTANCE', //实际行驶距离超过预估距离1倍
    'o_warn_travel_fee'                               => 'O_WARN_TRAVEL_FEE', //行驶时间超过5分钟，价格还是起步价
    'o_warn_travel_two_fee'                           => 'O_WARN_TRAVEL_TWO_FEE', //实际价格超过预估价格1倍的
    'o_invitation_order_visit_uv'                     => 'O_INVITATION_ORDER_VISIT_UV_',
    //mis分区域星级评价统计
    'm_daily_level_statis_byarea'                     => 'M_DAILY_LEVEL_STATIS_BYAREA_',
    'm_account_role_info'                             => 'M_ACCOUNT_ROLE_INFO_',
    'm_question_coupon_num'                           => 'M_QUESTION_COUPON_NUM',
    'm_activity_effective_area'                       => 'M_ACTIVITY_EFFECTIVE_AREA_',
    'm_activity_effective_cfg'                        => 'M_ACTIVITY_EFFECTIVE_CFG_',
    'o_get_pwd_cnt'                                   => 'O_GET_PWD_CNT_',
    'o_access_qrcode_cnt'                             => 'O_ACCESS_QRCODE_CNT_',
    'o_station_login_fail'                            => 'O_STATION_LOGIN_FAIL_',
    'o_station_info'                                  => 'O_STATION_INFO_',
    'o_check_longrent_num'                            => 'O_CHECK_LONGRENT_NUM_',
    'm_driver_recommend_cnt'                          => 'M_DRIVER_RECOMMEND_CNT_',

    //mis权限点
    'm_user_privilege'                                => 'M_USER_PRIVILEGE_',
    //滴滴播报上传临时数据
    'm_didi_push_upload'                              => 'M_DIDI_PUSH_UPLOAD_',

    //第三方 third_party
    'tp_variflight_info'                              => 'TP_VARIFLIGHT_INFO_', //非常准信息 +fnum（航班号）+flightDate（起飞日期）
    'tp_variflight_addpush'                           => 'TP_VARIFLIGHT_ADDPUSH_', //定制航班信息推送 +fnum（航班号）+flightDate（起飞日期）+dep(起始地三字母)+arr(目的地三字母)
    'tool_traffic_info'                               => 'TOOL_TRAFFIC_INFO_', //交通信息+traffic_num
    'tool_traffic_list'                               => 'TOOL_TRAFFIC_LIST_', //交通信息列表+traffic_num 模糊查询
    'tool_city_airport'                               => 'TOOL_CITY_AIRPORT_', //前缀+md5(城市名+区域号)

    //通用问卷系统
    'q_ques_library'                                  => 'Q_QUES_LIBRARY_', //题库数据
    'q_ques_survey'                                   => 'Q_QUES_SURVEY_', //问卷数据
    'q_ques_extract'                                  => 'Q_QUES_EXTRACT_', //抽取题目配置
    'q_ques_topic'                                    => 'Q_QUES_TOPIC_', //题目hash
    'q_ques_topic_ids'                                => 'Q_QUES_TOPIC_IDS_', //题目id集合
    'q_ques_drcount'                                  => 'Q_QUES_DRCOUNT_', //分组汇总司机总数
    'q_count_group_driver'                            => 'Q_COUNT_GROUP_DRIVER_', //分组汇总司机总数
    'q_ques_send'                                     => 'Q_QUES_SEND_', //发放调研问卷
    //司机在线考试
    'q_exam_login'                                    => 'Q_EXAM_LOGIN_', //考官登陆验证key
    'q_exam_driver'                                   => 'Q_EXAM_DRIVER_', //考试司机验证

    'm_user_privilege'                                => 'M_USER_PRIVILEGE_',
    //mis权限 城市管理
    'm_user_privilege_city'                           => 'M_USER_PRIVILEGE_CITY_',
    'm_sms_reg'                                       => 'M_SMS_REG_',
    'm_airport_all'                                   => 'M_AIRPORT_ALL', //全部机场
    'm_district_map_area'                             => 'M_DISTRICT_MAP_AREA_', //区号到区域号的映射
    //统计司机在线专用
    'd_model'                                         => 'D_MODEL_',
    //司机注册统计计数
    'd_reg_num_driver'                                => 'D_REG_NUM_DRIVER',
    'd_tmp_num_driver'                                => 'D_TMP_NUM_DRIVER',

    //司机订单服务
    'm_driver_cancel'                                 => 'M_DRIVER_CANCEL', //司机订单取消率
    'm_driver_complaint'                              => 'M_DRIVER_COMPLAINT', //司机订单投诉率
    'm_driver_avgarrive'                              => 'M_DRIVER_AVGARRIVE', //司机订单平均接驾时间
    'm_driver_avglevel'                               => 'M_DRIVER_AVGLEVEL', //司机平均星级

    //司机申请升级相关
    'mis_car_brand'                                   => 'MIS_CAR_BRAND_', //车辆品牌型号
    'd_car_brand'                                     => 'D_CAR_BRAND_', //车辆品牌型号
    'mis_car_rel_mc'                                  => 'MIS_CAR_REL_MC_', //申请状态缓存10
    'mis_car_rel_car_mc'                              => 'MIS_CAR_REL_CAR_MC_', //关联车信息缓存10分钟
    'mis_driver_upgrade_mc'                           => 'MIS_DRIVER_UPGRADE_MC_', //历史状态缓存10分钟
    'mis_order_ext_mc'                                => 'MIS_ORDER_EXT_MC_', //缓存订单信息缓存10分钟

    //MIS活动盘古配置
    'd_pangu_day_assign'                              => 'D_PANGU_DAY_ASSIGN_', //司机盘古活动指派完成单数、完成率
    'd_pangu_days_assign'                             => 'D_PANGU_DAYS_ASSIGN_', //司机盘古活动指派完成单数、完成率,指定时间段内
    'd_driver_star_from_credit'                       => 'D_DRIVER_STAR_FROM_CREDIT_', //司机从信用系统获取到的星级
    'm_pangu_config'                                  => 'M_PANGU_CONFIG_', //盘古活动
    'm_pangu_rule'                                    => 'M_PANGU_RULE_', //盘古活动下的规则
    'm_pangu_rule_db_info'                            => 'M_PANGU_RULE_DB_INFO_', //盘古活动下的规则
    'm_pangu_reward'                                  => 'M_PANGU_REWARD_', //盘古活动规则下的奖励

    'o_coordinate_begin_finish'                       => 'O_COORDINATE_BEGIN_FINISH_', //订单开始计价、完成订单坐标
    'o_fsource'                                       => 'O_FSOURCE_', //乘客订单fsource
    'o_pool_seat_discount'                            => 'O_POOL_SEAT_DISCOUNT_', //拼座订单折扣
    'tags_by_category'                                => 'TAGS_BY_CATEGORY_', //按栏目读取标签
    'positivetags'                                    => 'POSITIVETAGS_', //司机标签

    'd_carpool_current_travelorder'                   => 'D_CARPOOL_CURRENT_TRAVELORDER_', // 司机当前行程订单
    'd_carpool_route_order'                           => 'D_CARPOOL_ROUTE_ORDER_', // 司机行程拼友
    'd_carpool_route_update'                          => 'D_CARPOOL_ROUTE_UPDATE_', // 司机主动修改行程
    'p_carpool_reward_info'                           => 'P_CARPOOL_REWARD_INFO_', //拼成奖励
    'p_carpool_reward_send_msg'                       => 'P_CARPOOL_REWARD_SEND_MSG_', //是否发过拼成奖励的短信
    'p_carpool_sales_coupon'                          => 'P_CARPOOL_SALES_COUPON_',
    'p_carpool_cooperate_msg'                         => 'P_CARPOOL_COOPERATE_MSG_',
    'p_order_like_wait_flag'                          => 'P_ORDER_LIKE_WAIT_FLAG_',
    'p_carpool_activity_relation_passenger_head'      => 'P_CARPOOL_ACTIVITY_RELATION_PASSENGER_HEAD_', //拼车活动中替换拼友头像缓存
    'p_low_price_carpool_show_bubble'                 => 'P_LOW_PRICE_CARPOOL_SHOW_BUBBLE_',
    'p_carpool_walk_station_confirm'                  => 'P_CARPOOL_WALK_STATION_CONFIRM_', //拼车愿走下车点是否确定
    'p_carpool_match_show_type'                       => 'P_CARPOOL_MATCH_SHOW_TYPE_',

    //拼车相关
    'p_passenger_order_num_info'                      => 'P_PASSENGER_ORDER_NUM_INFO_', //乘客次数统计key
    'd_menus_default'                                 => 'D_MENUS_DEFAULT',
    'd_menus_by_area'                                 => 'D_MENUS_BY_AREA_',

    'd_record_latest_comment_tags'                    => 'D_RECORD_LATEST_COMMENT_TAGS_',
    'd_highest_frequency_comment_tags'                => 'D_HIGHEST_FREQUENCY_COMMENT_TAGS_',

    'd_carpool_driver_order'                          => 'D_CARPOOL_DRIVER_ORDER_',

    'd_predict_neat_hot_time'                         => 'D_PREDICT_NEAR_HOT_TIME_',
    'd_predict_neat_hot_whitelist'                    => 'D_PREDICT_NEAR_HOT_WHITELIST',
    'd_finish_order_broad_whitelist'                  => 'D_FINISH_ORDER_BROAD_WHITELIST',
    'd_predict_neat_hot_distance'                     => 'D_PREDICT_NEAR_HOT_DISTANCE_',

    'd_tag_info'                                      => 'D_TAG_INFO_',
    'd_tag_rel'                                       => 'D_TAG_REL_',
    'o_pangu_reward'                                  => 'O_PANGU_REWARD_',
    'o_transfer'                                      => 'O_TRANSFER_',
    //司机每天完成的抢单数量
    'd_driver_compet_order_num'                       => 'D_DRIVER_COMPET_ORDER_NUM_',
    'p_tag_info'                                      => 'P_TAG_INFO_',
    'p_tag_rel'                                       => 'P_TAG_REL_',
    //push filter order 锁
    'o_push_filter_lock'                              => 'O_PUSH_FILTER_LOCK_',
    'd_online_tags'                                   => 'D_ONLINE_TAGS_', //统计司机在线，分model和指派
    'd_push_filter_order'                             => 'D_PUSH_FILTER_ORDER_', //dPushFilter 去重key
    'd_freeride_switch_set'                           => 'D_FREERIDE_SWITCH_SET', //打开顺风车导流司机id集合
    'd_compet_order_limit_set'                        => 'D_COMPET_ORDER_LIMIT_SET', //打开限制抢单限制司机id集合
    'd_assign_reject_stat'                            => 'D_ASSIGN_REJECT_STAT_', //指派拒接统计当日拒接数
    'd_assign_reject_status'                          => 'D_ASSIGN_REJECT_STATUS_', //司机是否因指派拒接超过阀值而被封禁
    'd_good_driver'                                   => 'D_GOOD_DRIVER_', //好司机
    'd_good_driver_import'                            => 'D_GOOD_DRIVER_IMPORT_', //好司机
    'p_flag_agent_remind'                             => 'P_FLAG_AGENT_REMIND_', //是否需要提醒免密签约
    'd_last_display_status'                           => 'D_LAST_DISPLAY_STATUS_',

    'd_listen_order_status'                           => 'D_LISTEN_ORDER_STATUS_', //司机听单状态,是否可以听单
    //司机评价乘客
    'd_order_driver_comment_passenger_lock'           => 'D_ORDER_DRIVER_COMMENT_PASSENGER_LOCK',
    'd_banner_sets'                                   => 'D_BANNER_SETS_', //司机首页banner列表id
    'd_banner_info'                                   => 'D_BANNER_INFO_', //司机首页banner详情

    'd_direct_driver_set'                             => 'D_DIRECT_DRIVER_SET', //直营车不能向下听单白名单
    'd_pri_airport_driver_set'                        => 'D_PRI_AIRPORT_DRIVER_SET', //倾斜听机场单白名单

    'o_track_log_frequency_control'                   => 'O_TRACK_LOG_FREQUENCY_CONTROL', //有针对性的采样订单记录track.log
    'o_oid_freq_black'                                => 'O_OID_FREQ_BLACK', //订单加密orderid的黑名单前缀
    'o_oid_freq_count'                                => 'O_OID_FREQ_COUNT', //订单加密orderid的频率计数
    'p_last_ip'                                       => 'P_LAST_IP_',
    'p_last_device'                                   => 'P_LAST_DEVICE_',
    'o_order_poi_info'                                => 'O_ORDER_POI_INFO_', // 订单POI信息

    'p_lineup_broadcast_time'                         => 'P_LINEUP_BROADCAST_TIME_', //排队订单拨单时间 + orderId + district
    'p_lineup_init_rank'                              => 'P_LINEUP_INIT_RANK_', //排队初始位置，+ orderId
    'o_orderid_estimate_stationinfo'                  => 'O_ORDERID_ESTIMATE_STATIONINFO_', // 根据订单ID获取站点信息
    'p_traceid_estimate_stationinfo'                  => 'P_TRACEID_ESTIMATE_STATIONINFO_', // 根据trace id获取站点信息
    'p_scene_feature'                                 => 'P_SCENE_FEATURE_', //根据配置，自动给订单打特征
    'p_traceid_estimate_route_group'                  => 'P_TRACEID_ESTIMATE_ROUTE_GROUP_', //根据traceid缓存父路线id
    'p_xiaoba_new_order_times'                        => 'P_XIAOBA_NEW_ORDER_TIMES_', //发过小巴单次数
    'p_special_rate_pool_seat'                        => 'P_SPECIAL_RATE_POOL_SEAT_', //特价拼车实验
    'p_scan_pay_billid'                               => 'P_SCAN_PAY_BILLID', //乘客扫码付款
    'p_carpool_time_range'                            => 'P_CARPOOL_TIME_RANGE_',
    'p_scan_pay_driver_map'                           => 'P_SCAN_PAY_DRIVER_MAP', //扫码付司机映射
    'p_scan_pay_card'                                 => 'P_SCAN_PAY_CARD',
    'p_traceid_estimate_stationlist'                  => 'P_TRACEID_ESTIMATE_STATIONLIST_', // 根据trace id获取站点列表
    'p_mis_holiday_area'                              => 'P_MIS_HOLIDAY_AREA_', //mis地区节假日配置，当天信息 key＋date＋area
    'p_mis_holiday_country'                           => 'P_MIS_HOLIDAY_COUNTRY_', //mis全国节假日配置，当天信息 key＋date
    'd_city_combo_title'                              => 'D_CITY_COMBO_TITLE_',  //获取城市用车场景
    'p_traceid_estimate_combotype'                    => 'P_TRACEID_ESTIMATE_COMBOTYPE_',  //命中机场单combotype
    'p_carpool_commute_card'                          => 'P_CARPOOL_COMMUTE_CARD_',
    'p_carpool_commute_card_rejection_record_list'    => 'P_CARPOOL_COMMUTE_CARD_REJECTION_RECORD_LIST_', //拼车周卡拒绝记录（弹出气泡相关）
    'p_carpool_commute_h5_reject_record'              => 'P_CARPOOL_COMMUTE_H5_REJECT_RECORD_',//拼车通勤卡H5拒绝记录
    'p_commute_info'                                  => 'P_COMMUTE_INFO_',
    'p_estimate_airport_station_control'              => 'P_ESTIMATE_AIRPORT_STATION_CONTROL',
    'p_insurance_info'                                => 'P_INSURANCE_INFO_',
    'p_carpool_estimate_arrival'                      => 'P_CARPOOL_ESTIMATE_ARRIVAL',
    'p_order_match_experience_group'                  => 'P_ORDER_MATCH_EXPERIENCE_GROUP_', //命中的匹配阶段实验组，+ orderId
    'p_estimate_coupon_info'                          => 'P_ESTIMATE_COUPON_INFO_',
    'p_check_passenger_record_hit'                    => 'P_CHECK_PASSENGER_RECORD_HIT_',//命中乘客录音录像cache + orderId
    'p_carpool_commute_free_card'                     => 'P_CARPOOL_COMMUTE_FREE_CARD_',//免费赠送卡信息缓存
    'p_component_cache'                               => 'P_COMPONENT_CACHE_PREFIX_',// ui component cache
    'p_activity_type_cache'                           => 'P_ACTIVITY_TYPE_PREFIX_',//ACTIVITY TYPE CACHE
    'd_inter_qrcode'                                  => 'D_INTER_DRIVER_QRCODE_', //城际司机扫码发单信息
    'p_inter_city_bubble_fast_car_estimate_fee'       => 'P_INTER_CITY_BUBBLE_FAST_CAR_ESTIMATE_FEE_',  //城际三叉戟订单需要缓存快车价格做取消挽留文案
    'p_product_activity_fee'                          => 'P_PRODUCT_ACTIVITY_FEE_',//单个产品含券预估价格
    'p_show_taxi_cash_pay_close'                      => 'P_SHOW_TAXI_CASH_PAY_CLOSE_', //出租车现金付下线
    'p_estimate_bubble_dynamic_price'                 => 'P_ESTIMATE_BUBBLE_DYNAMIC_PRICE_', //预估气泡动调信息
    'p_carpool_ets_info'                              => 'P_CARPOOL_ETS_INFO_', //拼车ETS信息
    'p_invitation_match_info'                         => 'P_INVITATION_MATCH_INFO_', //邀约同行匹配结果
    'p_invitation_share_order_info'                   => 'P_INVITATION_SHARE_ORDER_INFO_',
    'p_carpool_ext_info'                              => 'P_CARPOOL_EXT_INFO_', //拼车EXT策略
    'p_estimate_id_link_prefix'                       => 'P_ESTIMATE_ID_LINK_PREFIX_',
    'p_60_guide_bubble_cache'                         => 'P_60_GUIDE_BUBBLE_CACHE_',
    'p_60_scene_guide_bubble_num_cache'               => 'P_60_GUIDE_BUBBLE_NUM_CACHE_',
    'p_estimate_id_fence_id_link_prefix'              => 'P_ESTIMATE_ID_FENCE_ID_LINK_PREFIX_',
    'p_60_scene_guide_bubble_cache'                   => 'P_60_SCENE_GUIDE_BUBBLE_CACHE_',
    'p_guide_discount_form'                           => 'P_GUIDE_DISCOUNT_FORM_', // 导流折扣类型
    'o_athena_queue_predict'                          => 'O_ATHENA_QUEUE_PREDICT_', // pGetOrderMatchInfo时请求queuePredict，Athena返回的订单当前信息（现只用名次字段，pPreCancelOrder确保展示尽量一致）
    'p_special_price_component'                       => 'P_SPECIAL_PRICE_COMPONENT_', //价格沟通组件
    'p_new_user_bubble_guide'                         => 'P_NEW_USER_BUBBLE_GUIDE_', //新业务新客引导冒泡缓存
    'p_new_user_bubble_guide_reward'                  => 'P_NEW_USER_BUBBLE_GUIDE_REWARD_', //新业务新客引导冒泡奖励缓存
    'o_dache_anycar_guide_info'                       => 'O_DACHE_ANYCAR_GUIDE_INFO_',
    'p_like_wait_bonus_extra_data'                    => 'P_LIKE_WAIT_BONUS_EXTRA_DATA_', // 愿等打车金额外数据存储
    'p_carpool_day_popup_duration'                    => 'P_CARPOOL_DAY_POPUP_DURATION',
    'p_estimate_bubble_activity'                      => 'P_ESTIMATE_BUBBLE_ACTIVITY_',
    'p_estimate_bubble_cancel_intercept'              => 'P_ESTIMATE_BUBBLE_CANCEL_INTERCEPT_',
    'o_multi_add_product_intercept_result'            => 'O_MULTI_ADD_PRODUCT_INTERCEPT_RESULT_',
    'p_hk_taxi_tip_info'                              => 'P_HK_TAXI_TIP_INFO_',
    'p_hk_taxi_comment_info'                          => 'P_HK_TAXI_COMMENT_INFO_',
    'p_rec_combo_cache'                               => 'P_REC_COMBO_CACHE_PREFIX_',
    'p_spacious_car_eid'                              => 'P_SPACIOUS_CAR_EID_',
    'p_carpool_page_support_one_seat'                 => 'P_CARPOOL_PAGE_SUPPORT_ONE_SEAT_',
    'p_estimate_origin_data'                          => 'P_ESTIMATE_ORIGIN_DATA_', //一部分预估的原始数据
    'p_carpool_guide_communication'                   => 'P_CARPOOL_GUIDE_COMMUNICATION',
    'p_hk_cap_taxi_force_communication'               => 'P_HK_CAP_TAXI_FORCE_COMMUNICATION_',
    'p_guide_anycar_estimate_price'                   => 'P_GUIDE_ANYCAR_ESTIMATE_PRICE_',
    'p_guide_anycar_estimate_price_tp_info'           => 'P_GUIDE_ANYCAR_ESTIMATE_PRICE_TP_INFO_',
    'p_precancel_append_carpool_card'                 => 'P_PRECANCEL_APPEND_CARPOOL_CARD_',
    'p_one_stop_bubble_data'                          => 'P_ONE_STOP_BUBBLE_DATA_',
    'p_default_select_eject_component'                => 'P_DEFAULT_SELECT_EJECT_COMPONENT', // 默勾弹窗组件
    'p_default_select_communicate_component'          => 'P_DEFAULT_SELECT_COMMUNICATE_COMPONENT', // 默勾沟通组件
    'p_revolving_account_discount_communication'      => 'P_REVOLVING_ACCOUNT_DISCOUNT_COMMUNICATION',
    'p_revolving_account_rebate_communication'        => 'P_REVOLVING_ACCOUNT_REBATE_COMMUNICATION',
    'p_revolving_account_rebate_n_fold_communication' => 'P_REVOLVING_ACCOUNT_REBATE_N_FOLD_COMMUNICATION',
    'p_bargain_guide_bar_bargain_btn_bubble'          => 'P_BARGAIN_GUIDE_BAR_BARGAIN_BTN_BUBBLE',
    'p_mamba_seat_selection_info'                     => 'P_MAMBA_SEAT_SELECTION_INFO',
    'p_bubble_weak_guidance_communication'            => 'P_BUBBLE_WEAK_GUIDANCE_COMMUNICATION',
    'p_carpool_minibus_map_token'                     => 'P_CARPOOL_MINIBUS_MAP_TOKEN_', //小巴地图token
    'p_carpool_minibus_info'                          => 'P_CARPOOL_MINIBUS_INFO_', //小巴信息，这个上完上面的废弃
    'o_multi_require_product_intercept_result'        => 'O_MULTI_REQUIRE_PRODUCT_INTERCEPT_RESULT_',
    'p_red_packet_top_rule_communicate'               => 'P_RED_PACKET_TOP_RULE_COMMUNICATE',
    'o_amount_of_notified_drivers'                    => 'O_AMOUNT_OF_NOTIFIED_DRIVERS_',
);

return $config;
