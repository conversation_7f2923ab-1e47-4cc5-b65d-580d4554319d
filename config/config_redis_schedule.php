<?php

/**
 * redis schedule服务器配置.
 */
$config['config_redis_schedule']=array(
        'schedule_default'=> array(
            //>>> 'host'=>'${GS_REDIS_SCHEDULE_IP}',
                'host'=> '**************',
            //<<<
            //>>> 'port'=>'${GS_REDIS_SCHEDULE_PORT}'
                'port'=> '6379',
            //<<<
        ),
);
/*
 * redis queue服务器配置，队列服务分配的队列id
 */
$config['config_redis_schedule_id']=array(
        'schedule_queue_id' => array(
                'schedule_api_default'              => '900900',
                'schedule_api_order_passenger'      => '900800',
                'schedule_api_order_driver'         => '900600',
                'schedule_api_b2b'                  => '901000',
                'schedule_api_webapp'               => '901100',
                'schedule_api_openapi'              => '901200',
                'schedule_api_driver_relieved'      => '901400',
                'schedule_gs_api_resend_order'      => '901700',
                'schedule_gs_api_carpool_coupon'    => '901900',
                'schedule_gs_api_striveorder_check' => '902200',
        ),
);

return $config;
