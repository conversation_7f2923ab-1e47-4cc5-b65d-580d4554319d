<?php
/**
 * Created by PhpStorm.
 * User: liuweitao
 * Date: 2017/5/3
 * Time: 17:51.
 */

/*
    二进制第i位为1表示该button被屏蔽
    SOrderActHiddenMask_CancelOrder = 1 << 0,    // 取消订单
    SOrderActHiddenMask_Share       = 1 << 1,    // 分享行程
    SOrderActHiddenMask_Help        = 1 << 2,    // 需要帮助
    SOrderActHiddenMask_Emergency   = 1 << 3,    // 紧急求助
    SOrderActHiddenMask_IM          = 1 << 4,    // IM控制
    SOrderActHiddenMask_Phone       = 1 << 5,    // 号码保护控制
 */
$config['scene_button_control'] = array(
    'ticket'    => 0x3F,
);

$config['product_button_control'] = array(
    '19'    => 0x08,
);

//二进制第i位为1表示该button被屏蔽
$config['button_control_index_mapping'] = array(
    'button_cancel_order'            => 1,   // 取消订单
    'button_share_trip'              => 2,   // 分享行程
    'button_need_help'               => 3,   // 需要帮助
    'button_emergency'               => 4,   // 紧急求助
    'button_im'                      => 5,   // IM控制
    'button_virtual_phone'           => 6,   // 号码保护控制
    'button_change_destination'      => 7,   // 修改目的地
    'button_pause_carpool'           => 8,   // 暂停拼车
    'button_cancel_trip_after_bill'  => 9,   // 司机开始计费后,乘客还没上车,取消行程
    'button_choose_route'            => 10,  // 多路线，路线选择
    'button_shield_voice_type'       => 11,  // 安全盾牌，开启录音
    'button_shield_video_type'       => 12,  // 安全盾牌，开启录像
);

return $config;
