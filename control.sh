#!/bin/bash

# Halt on errors
set -e

MODULE_NAME=$(cat .deploy/service.module.txt)
CLUSTER=$(cat .deploy/service.cluster.txt)
SERVICE_NAME=$(cat .deploy/service.service_name.txt)
NAMESPACE=${CLUSTER}.${SERVICE_NAME}
HND_CLUSTER=hnd-v
HNE_CLUSTER=hne-v
OSIM_CLUSTER=zjy-dev
CHN_CLUSTER="hna,hnb,offline_docker"
CLUSTER=${CLUSTER/-pre/}
CLUSTER=${CLUSTER/-small/}
PHP_INI_PATH=/home/<USER>/php7/etc/
PHP_EXT_PATH=/home/<USER>/php7/lib/php/extensions/no-debug-non-zts-20151012/
PHP74_EXT_PATH=/home/<USER>/php7/lib/php/extensions/no-debug-non-zts-20190902

# 接入代码覆盖率
function enable_xdebug(){
  if [[ -d code_coverage ]]; then
    if [[ $DIDIENV_ODIN_CLUSTER =~ .*-dev-* ]]; then
        # install xdebug
        if [[ -d /home/<USER>/code_coverage ]];then
            rm -rf /home/<USER>/code_coverage
        fi
        cp -rf ./code_coverage /home/<USER>/ && rm -rf ./code_coverage
        bash /home/<USER>/code_coverage/php/inject.sh
        bash /home/<USER>/code_coverage/php/install_xdebug.sh
        echo "enable_xdebug success"
    fi
  fi
}

function enable_online_coverage() {
  hostname=$(hostname)
  if [ $hostname == "pre-sale-hna-sf-8c4d8-3.docker.gz05" ] ||
    [ $hostname == "pre-sale-core-hna-sf-8c4d8-3.docker.gz05" ]; then
    echo "enable_online_coverage"
    local module_name="pre-sale"
    local scanner_dir="/home/<USER>/webroot/gulfstream/application/pre-sale/v1/app"
    local output_dir="/home/<USER>/webroot/gulfstream/log/pre-sale"
    bash $(pwd)/vendor/nuwa/nuwa-online-coverage/bin/control.sh ${module_name} ${scanner_dir} ${output_dir}
    /bin/bash /home/<USER>/php7/control.sh reload
  fi
}

function start() {
  if [ ${DIDIENV_DDCLOUD_TRAFFIC_RECORD} == "on" ] && [ -f ./biz-recorder/control.sh ]; then
    rm -rf /home/<USER>/biz-recorder
    mv ./biz-recorder /home/<USER>/
    bash /home/<USER>/biz-recorder/control.sh start
  fi
  enable_masking
  enable_profiling
  enable_online_coverage
   # 如果为测试环境就执行 enable_xdebug
  if [ ${DIDIENV_DDCLOUD_ENV_TYPE}x = "dev"x ]; then
    if [[ -f ./cover-tools-php/scripts/control.sh ]]; then
        source ./cover-tools-php/scripts/control.sh
        cover_control
        /bin/bash /home/<USER>/php7/control.sh reload
        echo "new enable_xdebug success"
    fi
#  			enable_xdebug
  fi
}

function stop() {
  echo "nothing to do"
}

function enable_masking() {
  if [ $(grep -c "masking.so" ${PHP_INI_PATH}"php.ini") -ne 0 ]; then
    echo 'masking.so has installed'
    return 0
  fi

  echo 'enable_masking'

  # 判断php版本使用对应拓展
  if [ $(/home/<USER>/php7/bin/php -v | grep -c 7.4.) -ne 0 ]; then
    rm -rf /home/<USER>/nuwa-php-masking
    mv ./nuwa-php-masking /home/<USER>
    echo -e "[masking]\nextension=/home/<USER>/nuwa-php-masking/masking.so\n" >>${PHP_INI_PATH}"php.ini"
    /bin/bash /home/<USER>/php7/control.sh reload
  fi
}

function enable_profiling() {
  hostname=$(hostname)
  if [ $hostname == "pre-sale-pre-sf-36d7a-1.docker.gz01" ] ||
    [ $hostname == "pre-sale-hna-sf-8c4d8-7.docker.gz05" ] ||
    [ $hostname == "pre-sale-core-hna-sf-8c4d8-7.docker.gz05" ]; then

    if [ $(grep -c "tideways.so" ${PHP_INI_PATH}"php.ini") -ne 0 ]; then
      echo 'tideways.so has installed'
      return 0
    fi

    echo 'enable_profiling'

    # 判断php版本使用对应拓展
    if [ $(/home/<USER>/php7/bin/php -v | grep -c 7.4.) -ne 0 ]; then
      echo "php74"
      mv -f nuwa-profiling-collector/so/php74/{tideways,mongodb}.so ${PHP74_EXT_PATH}
    else
      mv -f nuwa-profiling-collector/so/{tideways,mongodb}.so ${PHP_EXT_PATH}
    fi

    rm -rf /home/<USER>/nuwa/nuwa-profiling-collector &&
      mkdir -p /home/<USER>/nuwa &&
      mv nuwa-profiling-collector /home/<USER>/nuwa/nuwa-profiling-collector &&
      # 备份php.ini
      cp ${PHP_INI_PATH}"php.ini" ${PHP_INI_PATH}"php.ini.noprofiling" &&
      # 开启拓展
      sed -i -e '/extension="mongodb.so"/d' -e 's/^.*auto_prepend_file.*$/auto_prepend_file = "\/home\/<USER>\/nuwa\/nuwa-profiling-collector\/external\/header.php"/' ${PHP_INI_PATH}php.ini &&
      echo -e "[tideways]\nextension=tideways.so\n[mongodb]\nextension=mongodb.so" >>${PHP_INI_PATH}"php.ini"

      /bin/bash /home/<USER>/php7/control.sh reload
  fi
}

if [[ $CLUSTER == $HNE_CLUSTER* ]]; then
  cp -rp config/database/hne-v_database.php config/database.php
fi

if [[ $CLUSTER =~ ^$OSIM_CLUSTER.* ]]; then
     cp -rp config/zjy/* config/
fi

action=$1
case $action in
"start")
  # 启动服务
  start
  ;;
"stop")
  # 停止服务
  stop
  ;;
*)
  echo "unknown command"
  exit 1
  ;;
esac

