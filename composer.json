{"autoload": {"psr-4": {"PreSale\\Bootstrap\\": "app/bootstrap/", "PreSale\\Logics\\": "app/logics/", "PreSale\\Models\\": "app/models/", "PreSale\\Plugins\\": "app/plugins/", "PreSale\\Exception\\": "app/exception/", "PreSale\\DefaultImp\\": "app/defaultImp", "PreSale\\Core\\": "app/core/", "PreSale\\Dto\\": "app/Dto/", "PreSale\\Application\\": "app/Application/", "PreSale\\Domain\\": "app/Domain/", "PreSale\\Infrastructure\\": "app/Infrastructure/", "Dirpc\\SDK\\PreSale\\": "idl/Dirpc/SDK/PreSale"}, "exclude-from-classmap": ["/vendor/dirpc/pre-sale/"]}, "autoload-dev": {"psr-4": {"PreSale\\Test\\": "tests/"}}, "require": {"biz/biz-common": "^1.13", "disf/routematch": "0.*", "dirpc/carpool": "2.1.55", "dirpc/hotspot": "1.2.*", "dirpc/night-watch5": "1.*", "dirpc/hundun": "1.*", "biz/biz-lib": "3.*", "dirpc/estimate-decision": "1.*", "gulfstream/driver-system-sdk": "~1.0", "dirpc/mamba": "1.0.*", "dirpc/porsche": "1.*", "gulfstream/tripcloud-common": "2.0.*", "platform-ha/diconf": ">=0.1.1", "fpi/premier": ">=0.0.1", "fpi/international-passenger": ">=0.0.1", "biz/biz-fpi": "0.*", "nuwa/framework": "1.*", "nuwa/framework-bridge": "1.*", "dirpc/vcard": "1.*", "nuwa/database": "1.*", "fpi/nebula": ">=0.0.1", "nuwa/protobuf": "4.*", "dirpc/coupon": "^1.0", "dirpc/hestia-charge": "1.*", "dirpc/u-f-s": "0.*", "dirpc/pay-member": "10.*", "dukang/property-const-php-sdk": "1.*", "ext-json": "*", "dirpc/athena-apiv3": "2.*", "s3e/x-engine-php": "0.*", "s3e/iexp-php": "1.*", "gulfstream/bronze-door-sdk-php": "0.*", "dirpc/carpool-open-api": "1.1.*", "dirpc/watson": "1.0.25", "engine/gaia-sdk-php": "1.0.*", "dirpc/muses": "1.*", "dirpc/skyline": "1.0.1", "dirpc/pope-p-a-p-service": "1.0.43", "nuwa/nuwa-online-coverage": "0.0.*", "ext-bcmath": "*", "lego/log": "0.*", "dirpc/sps": "1.0.*", "dirpc/one-conf": "1.0.4", "dirpc/compensation": "1.0.*", "dirpc/governance-op": "^1.0", "dirpc/hubble": "2.0.*"}, "scripts": {"post-update-cmd": "find vendor/ -name .git | xargs rm -rf {}", "post-install-cmd": "find vendor/ -name .git | xargs rm -rf {}"}, "config": {"platform": {"php": "7.0.6"}}, "require-dev": {"mockery/mockery": "^1.3"}}