# Pre-Sale 售前模块

乘客侧业务相关模块之**pre-sale**(售前模块)，业务售前，需求表达模块,指的是订单生存周期中的发单前的部分，包括端启动、首页渲染、预估等部分。

## 一、前言
README文件是一个项目/模块的重要文件，能够帮助新加入的RD快速了解项目的基本情况（项目布局、注意事项、如何开发等等）。所以希望参与本模块开发的所有RD，一起来撰写本README，特别是在做了比较重要的更改后，能够在此及时更新文档。

## 二、模块结构

**备注**：模块结构可能会随着时间推移有变化，以下结构更新于2020年10月份，若有较大调整，请更新以下结构，并更新时间
```shell
.
├── README.md
├── app  // 本模块的核心代码都在app目录下
│   ├── Application
│   ├── Domain
│   ├── Dto
│   ├── Infrastructure
│   ├── bootstrap
│   ├── controllers
│   ├── core
│   ├── exception
│   ├── logics
│   ├── models
│   └── plugins
├── build.sh
├── composer.json
├── composer.lock
├── config  // 配置文件目录
├── control.sh
├── disf-meta.yaml
├── elevate
├── idl
├── index.php
├── indexcli.php
├── phpunit.xml
├── public
├── resources
├── storage
├── tests
└── vendor
```

本模块中所有接口在wiki上基本上都有对应文档，但是存在以下几个问题：
* wiki文档访问速度慢
* wiki文档太深
* 有的接口wiki文档很久没有更新，但是接口出入参数已发生变化
* 同一个接口有多份文档，太分散（有组织空间，也有个人空间在梳理文档）

所以，建议在此README文档中罗列接口对应的文档（最佳的一个），方便快速定位文档（接口按首字母拼音排序）

**app/controllers/Core**

（欢迎更新下表缺失字段）

接口|方法|接口wiki文档|备注
-----|-----|-----|-----
pEstimatePrice| | |单预估接口
pEstimatePriceCustom| | |
pLuxMultiEstimatePrice| | |豪华车预估接口
pMultiEstimatePrice| | |
pMultiEstimatePriceV2| | |6.0预估接口
pNewOrder| |http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=229521376|发单接口

**app/controllers/Other**

（欢迎更新下表缺失字段）

接口|方法|接口wiki文档|备注
-----|-----|-----|-----
getEstimatePrice| | |预估价格
pAnycarEstimate| | |
pCheckAndConfirmScanCode| | |
pCheckBookingAbility| | |
pCheckInterScan| | |
pCheckRiderInfo| | |
pConfirmInterScan| | |
pGetActivityInfo| | |
pGetBannerInfo| | |
pGetBarrage| | |
pGetBizConfig| | |
pGetBubbleInfo| | |
pGetComboInfo| | |
pGetCommunicateInfo| | |
pGetConfig| | |
pGetDynamicPriceDetail| | |
pGetEstimateFeeDetail| | |
pGetEstimateFeeDetailForAnycar| | |
pGetFenceList| | |
pGetFlag| | |
pGetIndexDiscountInfo| | |
pGetIndexInfo| | |
pGetInvitationInfo| | |
pGetMultiSpecialRule| | |
pGetPriceRule| | |
pGetPriceRuleList| | |
pGetPriceRuleListNew| | |
pGetPriceRuleV2| | |
pGetPriceScene| | |
pGetSceneList| | |
pGetSceneProducts| | |
pGetSpecialRule| | |
pGetStationStatus| | |
pGetTailorService| | |
pGetUnioneInfoFee| | |
pGetWanliuInfo| | |
pGetWebAppIndexInfo| | |
pMoreServices| | |
pMultiRouteEstimatePrice| | |
pOpenCity| | |
pOrderTimeout| | |
pPreCancelEstimate| | |
pPreCancelOrder| | |
pQueryAthena| | |
pReceiveCoupon| | |
pSelectItem| | |
pSubmitTailorService| | |

**app/controllers/Webapp**

（欢迎更新下表缺失字段）

接口|方法|接口wiki文档|备注
-----|-----|-----|-----
pGetConfig| | |
pGetFlag| | |
pGetLite| | |
pIndex| | |首页

## 三、如何开发
* 在本地机器上将本模块clone下来：<NAME_EMAIL>:gulfstream/pre-sale.git
* 利用composer安装模块依赖包：composer install
* 在OE上开新的分支，本地git checkout到新分支上
* 若是在Docker机上开发测试：
  * 配置PHPStorm与Docker机自动同步代码（当然也可以通过git push代码，然后在Docker机上用didi build zhuanche_xxx的方式部署，但是流程太慢，不建议这样操作）
  * 端上将环境切到docker机
  * 端上测试

* 若是在sim机器上开发测试：
  * git push最新修改的代码到gitlab代码仓库
  * 在OE上部署本模块
  * 端上测试

## 四、可参考文档
1. [核心出行服务架构图](http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=282677225)

2. [pre-sale 售前](http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=184833895)

## 五、本文档贡献者
（若对本文档有过贡献，哪怕改了一个错别字也欢迎添加自己的大名）
* 杨双龙 <<EMAIL>>