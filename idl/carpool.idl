namespace php Dirpc.SDK.PreSale
namespace go Dirpc.SDK.PreSale


struct GetInvitationInfoV2Request {
    1: required string invite_oid //邀约订单号
    2: required string token
    3: required string appversion
    4: optional i32 access_key_id
}

struct CarpoolerTravelInfo {
    1: optional string head_icon
    2: optional string departure_text
    3: optional string seat_info
    4: optional string start_name
    5: optional string dest_name
}

struct CarpoolerTravel {
    1: optional string title //卡片名称
    2: optional i16 zero_saturate //是否置灰卡片 1-置灰
    3: optional list<CarpoolerTravelInfo> infos //拼友行程
}


struct ShareInfoV2 {
    1: required string path //小程序页面path
    2: required string title//微信卡片title
    3: required string button_img_url//按钮背景图
    4: optional string bg_img//卡片背景图
    5: optional string button_text//按钮文案
    6: optional i16 style//样式
    7: optional string departure_text//出发时间文案
    8: optional string start_name //起点名称
    9: optional string dest_name  //终点名称
}

struct InvitationEstimateInfoV2 {
    1: optional string order_type //订单类型：0实时单；1预约单
    2: optional string departure_range//出发时间
    3: optional string menu_id//菜单id
}

struct InvitationExtraInfoV2 {
    1: optional ShareInfoV2 share_info //微信卡片
    2: optional i16 inviter_status //客态页面下发
}

struct StartAddressData {
    1: required string from_poi_id
    2: required string from_area
    3: required string from_lng
    4: required string from_lat
    5: required string from_name
    6: required string from_address
    7: required string from_area_name
}

struct EndAddressData {
    1: required string to_poi_id
    2: required string to_area
    3: required string to_lng
    4: required string to_lat
    5: required string to_name
    6: required string to_address
    7: required string to_area_name
}

struct AddressInfo {
    1: required StartAddressData start_address_info
    2: required EndAddressData end_address_info
}

struct InvitationButtonV2Param {
    1: optional AddressInfo address_info
}

struct InvitationButtonV2 {
    1: required i32 type
    2: required string img
    3: optional string jump_url
    4: optional InvitationButtonV2Param params
}

struct SeatOption {
    1: optional string label
    2: optional i16 value
    3: optional bool selected
    4: optional bool disable
}

struct InvitationCarpoolSeatModule {
    1: optional string title
    2: optional string seats_exceed_msg
    3: optional list<SeatOption> option_list
}

struct TravelInfo {
    1: optional string title //卡片标题
    2: optional i16 zero_saturate //是否置灰, 1-置灰
    3: optional string head_icon //头像
    4: optional string departure_text //出发时间文案
    5: optional string seat_info //座位数信息
    6: optional string start_name //起点名称
    7: optional string dest_name //终点名称
}

struct InvitationDataV2 {
    1: optional string bg_image //背景图片
    2: optional string intro_msg //浮标文案
    3: optional string intro_url //浮标url
    4: optional i16 status //当前用户落地页状态
    5: optional i16 role //角色
    6: optional TravelInfo travel_info//主卡位置行程卡片
    7: optional list<InvitationButtonV2> button //按钮
    8: optional InvitationEstimateInfoV2 estimate_info//预估信息
    9: optional InvitationExtraInfoV2 extra_info//额外信息
    10: optional string page_title //页面标题
    11: optional InvitationCarpoolSeatModule carpool_seat_module//座位数组件
    12: optional CarpoolerTravel carpooler_travel//主卡下方拼友行程卡片
    13: optional string estimate_loading//预估toast
    14: optional string jump_url//路径跳转
}

struct GetInvitationInfoV2Response {
    1: required i32 errno
    2: required string errmsg
    3: optional InvitationDataV2 data
}
