namespace php Dirpc.SDK.PreSale
namespace go Dirpc.SDK.PreSale

include "side_estimate.idl"
include "carpool.idl"

struct MultiEstimatePriceRequest {
    /** 客户端参数**/
    1: required string    token
    2: required string    app_version
    3: required i32       access_key_id
    4: required i32       channel
    5: required i32       client_type
    6: required string    lang
    7: required string    a3_token
    8: required string    pixels
    9: required string    maptype
    10: required string   imei
    11: required string   suuid
    12: required string   terminal_id
    13: required i32      origin_id
    14: required i32      platform_type
    15: required string   openid
    16: required i32      guide_type
    17: required string   from
    18: required string   preferred_route_id
    19: required string   dialog_id
    20: optional string   source_channel
    21: optional double   screen_scale
    22: optional i32      estimate_style_type // 预估表单样式，0:老样式，1:单行  2双排新表单 3多tab新表单 4:一站式出行
    23: optional i32      route_preference_type   // 路线偏好类型，例如：少附加费、大路优先
    24: optional i32      form_height
    25: optional i32      font_scale_type // 大字模式字段 0:正常 1:大字 2:超大字

    /**起终点相关信息**/
    30: required double     lat
    31: required double     lng
    32: required double     from_lat
    33: required double     from_lng
    34: required string     from_poi_id
    35: required string     from_poi_type
    36: required string     from_poi_code
    37: required string     from_address
    38: required string     from_name
    39: required double     to_lat
    40: required double     to_lng
    41: required string     to_poi_id
    42: required string     to_poi_type
    43: required string     to_address
    44: required string     to_name
    45: required string     dest_poi_code
    47: required string     dest_poi_tag
    48: required string     choose_f_searchid //用户选择起点请求ID
    49: required string     choose_t_searchid //用户选择终点请求ID

    /*订单属性等信息*/
    50: required string     menu_id
    51: required i32        page_type
    52: required i32        call_car_type
    53: required string     call_car_phone
    54: required i32        user_type // 用户类型 0表示普通用户，2表示企业用户
    55: required string     departure_time  //时间戳
    56: required i32        payments_type //支付类型
    57: required string     multi_require_product //用户勾选项
    58: required i32        has_scroll //客户端是否进行过上拉操作
    59: required i32        order_type //预约单
    60: required i32        origin_page_type // 原始入口页面（如预约跳转接送机）
    61: required string     stopover_points //途经点参数 json array 字符串
    62: optional string     tab_list //如果在有tab的场景下，需要告知tab的列表，供Athena使用，V3接口专用
    63: required string     additional_service // 整个预估的附加需求
    65: optional string     preference_filter //用户选择的过滤器id
    66: optional string     tab_id           // v3可能有normal/classify
    67: optional string     preference_filter_id //用户选择的过滤器id delete
    68: optional i32        carpool_seat_num  // 用户选择的拼车座位数
    69: optional string     category_info  //分框折叠态

    /*业务信息*/
    80: optional i32        shake_flag
    81: optional string     pre_trace_id
    82: optional string     departure_range //城际拼车订单出发时间

    /*接送机相关*/
    120: optional string    flight_dep_code // 航班出发地三字码,如CTU
    121: optional string    flight_dep_terminal // 航班出发航站楼，如T2
    122: optional string    traffic_dep_time // 航班起飞时间字符串
    123: optional string    flight_arr_code // 航班落地三字码,如CTU
    124: optional string    flight_arr_terminal //航班落地航站楼，如T2
    125: optional string    traffic_arr_time // 航班到达时间字符串
    126: optional string    traffic_number // 航班号，如CA1405
    127: optional i16       airport_type // 接送机类型 1-接机，2-送机，端上入口，无入口传0
    128: optional i32       airport_id // 接机时为航班落地航站楼id，送机时为出发航站楼id，如1573
    129: optional i32       shift_time // 用车偏移时间（接机时，单位：秒）

    /*活动相关*/
    150: optional i32       activity_id // 活动id，去掉原有x_activity_id

    /*B端车票*/
    155: optional string       biz_ticket // 车票ID

    /*专车相关*/
    160: optional i16       too_far_order_limit // 专车是否限制超远途订单

    /*特殊场景*/
    170: optional i32       special_scene_param // 极端天气场景（如暴雨等）
    171: required i32       from_type //用来标识是否是再来一单场景，3-再来一单
    172: required i32       is_female_driver_first //用来标识夜间场景的女司机优先场景
    173: optional string    guide_trace_id // 导流来源的冒泡trace

    180: optional string    luxury_select_carlevels
    181: optional string    luxury_select_driver
    190: optional string    agent_type

    /*webx公参*/
    191: optional string    xpsid
    192: optional string    xpsid_root

    193: optional string one_stop_version

    194: optional double adapter_scale
}

struct PreferTag {
    1: required string display_icon // 显示图标
    2: required list<string> display_names // 显示名称
    3: required i32 display_count // 选择服务的数量，如果是0则不展示数量
}
//专车，豪车定制服务设置
struct PreferData {
    1: required string prefer_desc // 标题：可选服务 | 已选服务
    2: required i32 prefer_select_count // 已选服务数量
    3: required i32 prefer_total_count // 可选服务总数
    4: required list<PreferTag> prefer_display_tags // 用户选择服务的标签和图标
    5: required i32 prefer_popup_type // 0-全页面；1-半弹屏
}
struct CommonTag {
    1: required string content
    2: required string left_icon
    3: optional i32 show_anim //是否需要转动
    4: optional string icon //纯图片标签
    5: optional list<string> bg_gradients //标签文字颜色
    6: optional string bg_fill_color //标签文字颜色
    7: optional string font_color //标签文字颜色
    8: optional string type //类型
    9: optional string amount //金额
}

struct AggregationPriceInfoTag {
    1: required string content
    2: required string left_icon
    3: optional i32 show_anim //是否需要转动
    4: required double amount
    5: required i32 type
}

//新出租独立小程序推荐文案信息
struct TaxiRecommendInfTag {
    1: required string content //文案内容
    2: required string tag_icon //tag图标
    3: required string left_icon //气泡下方图标
    4: required string bg_image //背景图片
    5: required string font_color //字体颜色
    6: required string conf_info //配置展示信息
    7: optional string other_info //保留字段
}

struct SubTitleInfo {
    1: required string content
    2: optional string font_color
    3: optional string background_color
    4: optional string border_color
    5: optional string icon_url
    6: optional string background_transparency
}
struct LinkProduct {
    1: optional string default_text //默认文案
    2: optional string select_text //选中文案
    3: optional i16 is_select //是否选中
    4: required i32 product_category //附着其他品类id
    5: optional string carpool_select_text //拼车选中文案
    6: optional string carpool_success_text //拼车价格文案
    7: optional i16 is_multi_select // 勾选了link的品类后 = 发单两个品类
    8: optional string info_url // 小 i 的跳转地址
    9: optional string icon_url // 左icon
}
struct SeatConfig {
    1: required i16 value
    2: required string label
    3: required string label_2
    4: required string label_explanation
    5: required string select_text
    6: required string price_desc
    7: required bool   disable
}
struct CarpoolSeatModule {
    1: required string title
    2: required string subtitle
    3: required string title_2
    4: required list<SeatConfig> seat_config
    5: required i16 select_value
    6: required i16 select_index
    7: required string button_desc
    8: required string seats_exceed_toast //座位数超选提示
}

struct NewCarpoolSeatModule {
    1: required string title
    2: required i16 max_count
    3: required i16 select_count
    4: required string seats_exceed_toast //座位数超选提示
}

struct PromoteSalesText {
    1: required list<i32> rule_type     //规则
}

struct SpecialPriceText {
    1: required list<i32> rule_type     //规则
    2: required string text             //文案
    3: optional i32 is_force_notice     //强弹标识
    4: optional i32 multi_line_style_type // 多行样式
}

struct IconList {
    1: optional string icon
}

struct TimeSpanRange {
    1: required string value
    2: required string tags
    3: required string tips
    4: required bool available
    5: required string available_tags
    6: required string base_price_desc
    7: required string price_desc
    8: required i32 order_type
    9: required string left_label
    10: required string right_label
    11: optional string title
    12: optional string sub_title
}

struct IntercityTimeSpanRange {
    1: required string value
    2: required string tags
    3: required string tips
    4: required bool available
    5: required string available_tags
    6: required string base_price_desc
    7: required string price_desc
    8: required i32 order_type
    9: required string left_label
    10: required string right_label
    11: optional string title
    12: optional string sub_title
    13: optional list<IconList> icon_list
    14: optional string sku_desc
    15: optional i16 is_select
}

struct ShowSkuTimeSpanRange {
       1: required string value
       2: required string tags
       3: required string tips
       4: required bool available
       5: required string available_tags
       6: required string base_price_desc
       7: required string price_desc
       8: required i32 order_type
       9: required string left_label
       10: required string right_label
       11: required string label
       12: optional string title
       13: optional string sub_title
       14: optional string icon
       15: optional string sku_desc
}

struct TimeSpan {
    1: required string title
    2: required string date
    3: required list<TimeSpanRange> range
}

struct IntercityTimeSpan {
    1: required string title
    2: required string date
    3: required list<IntercityTimeSpanRange> range
}

struct MatchRoutesData {
    1: required string time_title
    2: required string sub_title
    3: required string left_text
    4: required string right_text
    5: required string route_end_name
    6: required string price_tip
    7: required string none_range_pic
    8: required string none_range_tip
    9: required list<TimeSpan> time_span
}
struct Minute {
    1: optional i32 minute
    2: optional string value
    3: optional bool default_selected
    4: optional string value_now
    5: optional string checkbox_text
    6: optional string button_text
    7: optional string button_text_now
}
struct CarpoolBookingTime {
    1: optional string text
    2: optional i32 hour
    3: optional i32 gap
    4: optional string desc
    5: optional list<Minute> minute_list
}
struct CarpoolBookingTimeSpan {
    1: optional string title
    2: optional string date
    3: optional list<CarpoolBookingTime> time
}
struct StationCarpoolBooking {
    1: optional i32 is_interrupt
    2: optional string dialog_title
    3: optional string dialog_sub_title
    4: optional string title
    5: optional list<CarpoolBookingTimeSpan> time_span
}
struct ServicePopup {
    1: optional string button_detail
    2: optional string button_ok
    3: optional string detail_link
    4: optional string note_content
    5: optional i32 service_id
    6: required i32 product_id
}
struct SceneData {
    1: required i32 id // 个性化服务唯一id
    2: optional list<i32> mutex_ids // 互斥服务id
    3: required i32 max // 个性化服务数量限制
    4: required string title // title
    5: optional string desc // 摘要，如"限时0元"
    6: optional string price // 账单计价
    7: required string origin_price // 原价，固定配置，端上用于删除线展示
    8: required string unit // 单位，如："人"或"次"
    9: required string num // 已确认的人数或次数，默认为1人或1次，但不代表用户已勾选此服务
    10: required i32 selected_count // 已选择的人数/次数，默认为0
    11: optional string detail // 详情
    12: required i32 status // 状态，1-服务正常，2-服务不可选，点击出tips
    13: optional string tips // 提示
    14: optional string icon // 图标
    15: optional string num_selector_title // 选择数量的标题
    16: optional string num_selector_subtitle // 服务数量选择的副标题
    17: optional ServicePopup popup // 个性化服务弹窗，如新手引导弹窗
    18: optional string unit_price // 现价的单价
    19: optional string origin_unit_price // 原价的单价
    20: required string light_icon
    21: required string product_id
    22: required string service_id
}

struct PaymentInfoStyle {
    1: required string content // 支付方式名称
    2: optional string font_color // 字体颜色
    3: optional string background_color // 背景颜色
    4: optional string background_transparency // 背景透明度
    5: optional string border_color // 边框颜色
    6: optional string highlight_font_color // 高亮字体颜色
}
struct FormUserPayInfo {
    1: required string payment_id //支付方式id
    2: optional string business_const_set // 企业成本中心设置
    3: required PaymentInfoStyle payment_info // 支付信息
}

struct RedirectionInfo {
    1: optional string poi_id
    2: optional string displayname
    3: optional string address
    4: optional double lat
    5: optional double lng
    6: optional string coordinate_type
    7: optional i32 city_id
    8: optional string city_name
    9: optional string gap
}

struct AutoDriveRedirectionInfo {
    1: required RedirectionInfo start_info //无人车上车点
    2: required RedirectionInfo end_info //无人车下车点
}


struct OptionService {
    1: required i32 option_id
    2: required string title
    3: required string desc
    4: required i32 selected_count
}

struct OptionData {
    1: required string title
    2: required string icon
    3: required list<OptionService> service_list
}

struct ExtraTag {
    1: required string title
    2: required string icon
    3: required string type
    4: required string background_color
    5: required string text_color
}

struct MixedFeeMsg {
    1: optional string fee_msg
    2: optional string sub_fee_msg
}

struct RecommendGuide {
    1: optional i32 guide_scene
    2: optional i32 guide_type
    3: optional i32 count_down
    4: optional string recommend_text
    5: optional string recommend_toast
}

struct MultiPriceDesc {
    1: optional string fee_msg
    2: optional string estimate_fee
    3: optional string price_desc
    4: optional string left_icon
    5: optional i32 show_anim //是否需要转动
}

struct EstimateData {
    1: required string intro_msg //车型文案
    2: required string fee_msg //价格信息
    3: required i32 require_level //车型
    4: required i32 business_id //业务线
    5: required i32 product_id //产品线ID
    6: required i32 combo_type //combo_type
    7: required i32 product_category // 品类ID
    8: required list<CommonTag> price_info_desc //价格补充信息（券/动调/黑金会员等）
    9: required i16 category_id //分组ID
    10: optional CommonTag recommend_tag //车型右边的推荐语
    11: optional CommonTag selection_tag //勾选按钮上的气泡tag
    12: optional LinkProduct link_product //附着其他品类
    13: optional CarpoolSeatModule carpool_seat_module //拼车座位数组件
    14: required string estimate_id //预估ID
    15: optional list<SubTitleInfo> sub_title_list //车型下方的小字描述
    16: optional SpecialPriceText special_price_text //品类下的价格沟通规则、文案和强弹标识
    17: optional MatchRoutesData match_routes_data //城际拼车时间片组件
    18: optional i32 hidden //是否隐藏，两口价用
    19: optional StationCarpoolBooking station_carpool_booking //拼成乐时间片
    20: required string fee_amount //费用价格
    21: optional i32 combo_id //路线ID，发单参数
    22: optional list<SceneData> scene_data // 个性化服务数据
    23: optional string port_type //粤港车口岸信息
    24: optional list<string> route_id_list //路线ID
    25: optional string custom_service_popup_image // 个性化服务半弹窗顶部图片
    26: optional list<string> attach_service_icon // 附加服务的icon
    27: required FormUserPayInfo user_pay_info // 车型表单的支付方式
    28: optional i32 route_type // 路线类型
    29: optional list<ExtraTag> extra_tag // 产品tag描述(多是角标形式)
    30: optional string selected_background_url // 卡片背景图
    31: optional list<MultiPriceDesc> multi_price_desc //单品类多价格展示

    //6.0新增表单展示逻辑
    50: required i32 recommend_type //是否展示在外层推荐位 0-不展示；1-展示
    51: required i32 select_type //是否选中 （实时单有多项，预估单仅选中一项）0-不选中；1-选中
    52: required i32 form_show_type //表单展示类型（拼成乐/城际 展示在导流位）0-anycar列表中；1-导流位
    53: required string guide_show_type_tips //展示在导流位上的提示文案（「去预约」）
    54: required PreferData prefer_data //专车，豪车个性化设置展示
    55: optional i32 hit_dynamic_price //是否命中动调
    56: optional string depart_tag //导流位出发时间提示信息
    57: optional i32 is_tripcloud // 是否第三方产品线
    58: optional OptionData option_data // 专车个性化服务
    59: optional AutoDriveRedirectionInfo auto_driving_address_info //无人车地址
    60: optional i32 disabled //不可用状态
    61: optional UsageInfo usage_info //使用信息（车型下方的下拉文案）
    62: optional string disabled_text // 不可用文案
    63: optional i32 level_type // 场景 1-滴滴特快
    64: optional i32 count_price_type // 计价模式
    65: optional string background_url // 产品描述背景图
    66: optional string detail_url // 产品描述落地页
    67: optional string scene_data_head // 个性化服务文案
    68: optional string scene_data_head_link // 个性化服务说明
    69: optional PromoteSalesText promote_sales_text //营销类沟通组件
    70: optional list<AggregationEstimateData> sub_product // 6.0出租车业务多品类、短途特惠
    71: optional string sub_page_title // 二级冒泡页的标题
    72: optional string sub_page_sub_title // 二级冒泡页的子标题
    73: optional i32 sub_group_id // 标识 1标识是短途特惠 2标识出租车
    74: optional string sub_intro_icon  // 优选出租车icon图标
    75: optional MixedFeeMsg mixed_fee_msg // 混合支付的预估价信息
    76: optional string cutoff_fee_msg // 预估快车价信息
    77: optional string cutoff_fee_info // 预估快车价信息
    78: optional RecommendGuide recommend_guide //导流推荐信息
    79: optional bool show_sub_page_directly // 仅表示聚合子页面是否可以直接弹出，弹出时机端控制（可用于特惠联盟、出租车）
    80: optional string reference_fee_amount //特惠联盟参考价，用于计算最大可省
    81: optional i32 hit_show_h5_type //预估show h5命中类型
    82: optional i32 theme_type//预估品类主题标识
    83: optional bool sub_title_hidden_others // sub_title_list隐藏平滑移动信息
    84: optional BubbleThemeData theme_data//预估品类主题数据
    85: optional TaxiRecommendInfTag unitaix_recommend_tag //新出租独立小程序推荐文案
    86: optional ExtraEstimateData extra_estimate_data //载人车倒计时预估数据
    87: optional string sub_right_title //0.5盒子
    90: optional string box_id //0.5盒子
    91: optional string guide_path // 导流位跳转链接
}

// 价格轴
struct PriceAxle {
    1: optional string price_axle_title // 价格轴预期文案
    2: optional i32 type // 价格轴类型
    3: optional i32 left_boundary // 价格轴左边界
    4: optional i32 right_boundary // 价格轴右边界
    5: optional i32 division_method // 划分方法
    6: optional i32 division_price // 划分价格
    7: optional list<i32> ban_product_category // 禁止品类列表
    8: optional i32 slide_step // 步长
    9: optional list<AnchorPoint> anchor_point // 锚点

}

// 锚点
struct AnchorPoint {
    1: optional i32 anchor_fee // 锚点价格
    2: optional string anchor_text // 锚点文案
    3: optional string anchor_text_color // 锚点文案颜色
    4: optional string anchor_icon // 锚点icon
    5: optional string anchor_fee_color // 价格字体颜色
}

struct ExtraEstimateData {
    1: optional string fee_msg //价格信息
    2: optional list<MultiPriceDesc> multi_price_desc //单品类多价格展示
    3: optional BubbleThemeData theme_data//预估品类主题数据
    4: optional i32 count_down
    5: optional string left_down_icon_text
    6: optional AthenaExtraInfo extra_order_params
}

struct AthenaExtraInfo{
    1: optional string guide_scene
    2: optional string athena_id
}

struct BubbleThemeData {
    1: optional string car_image//车型图片
    2: required string theme_color//主题颜色
    3: optional string shadow_color//卡片阴影颜色
    5: required list<string> selected_bg_gradients//选中品类背景渐变色
    6: optional list<string> unselected_bg_gradients//未选中品类背景渐变色
    7: optional list<string> bottom_bg_gradients//品类底部背景渐变色
    8: optional list<string> bottom_label_list//品类底部标签列表
    9: optional list<string> outer_bg_gradients//品类外框背景渐变色
    11: optional string button_font_color//按钮字体颜色
    12: optional string inner_bg_color//内部(专豪二级服务，盒子已选品类)，背景色
    13: optional string title //顶部推荐标题
    14: optional string sub_title //顶部推荐副标题
    15: optional string title_icon //标题左侧Icon
    16: optional string right_image //标题右侧的小车等图标的图片
    17: optional string texture_image //底纹
    18: optional string right_title //右侧标题
    19: optional string right_info_url //0.5盒子 右侧跳转链接
}

struct AggregationEstimateData {
    1: required string intro_msg //车型文案
    2: required string fee_msg //价格信息
    3: required i32 require_level //车型
    4: required i32 business_id //业务线
    5: required i32 product_id //产品线ID
    6: required i32 combo_type //combo_type
    7: required i32 product_category // 品类ID
    8: required list<AggregationPriceInfoTag> price_info_desc //价格补充信息（券/动调/黑金会员等）
    9: required i16 category_id //分组ID
    10: optional CommonTag recommend_tag //车型右边的推荐语
    11: optional CommonTag selection_tag //勾选按钮上的气泡tag
    12: optional LinkProduct link_product //附着其他品类
    13: optional CarpoolSeatModule carpool_seat_module //拼车座位数组件
    14: required string estimate_id //预估ID
    15: optional list<SubTitleInfo> sub_title_list //车型下方的小字描述
    16: optional SpecialPriceText special_price_text //品类下的价格沟通规则、文案和强弹标识
    17: optional MatchRoutesData match_routes_data //城际拼车时间片组件
    18: optional i32 hidden //是否隐藏，两口价用
    19: optional StationCarpoolBooking station_carpool_booking //拼成乐时间片
    20: required string fee_amount //费用价格
    21: optional i32 combo_id //路线ID，发单参数
    22: optional list<SceneData> scene_data // 个性化服务数据
    23: optional string port_type //粤港车口岸信息
    24: optional list<string> route_id_list //路线ID
    25: optional string custom_service_popup_image // 个性化服务半弹窗顶部图片
    26: optional list<string> attach_service_icon // 附加服务的icon
    27: required FormUserPayInfo user_pay_info // 车型表单的支付方式
    28: optional i32 route_type // 路线类型
    29: optional list<ExtraTag> extra_tag // 产品tag描述(多是角标形式)
    30: optional string selected_background_url // 卡片背景图

    //6.0新增表单展示逻辑
    50: required i32 recommend_type //是否展示在外层推荐位 0-不展示；1-展示
    51: required i32 select_type //是否选中 （实时单有多项，预估单仅选中一项）0-不选中；1-选中
    52: required i32 form_show_type //表单展示类型（拼成乐/城际 展示在导流位）0-anycar列表中；1-导流位
    53: required string guide_show_type_tips //展示在导流位上的提示文案（「去预约」）
    54: required PreferData prefer_data //专车，豪车个性化设置展示
    55: optional i32 hit_dynamic_price //是否命中动调
    56: optional string depart_tag //导流位出发时间提示信息
    57: optional i32 is_tripcloud // 是否第三方产品线
    58: optional OptionData option_data // 专车个性化服务
    59: optional AutoDriveRedirectionInfo auto_driving_address_info //无人车地址
    60: optional i32 disabled //不可用状态
    61: optional UsageInfo usage_info //使用信息（车型下方的下拉文案）
    62: optional string disabled_text // 不可用文案
    63: optional i32 level_type // 场景 1-滴滴特快
    64: optional i32 count_price_type // 计价模式
    65: optional string background_url // 产品描述背景图
    66: optional string detail_url // 产品描述落地页
    67: optional string scene_data_head // 个性化服务文案
    68: optional string scene_data_head_link // 个性化服务说明
    69: optional PromoteSalesText promote_sales_text //营销类沟通组件
    70: optional string sub_page_title // 二级冒泡页的标题
    71: optional string sub_page_sub_title // 二级冒泡页的子标题
    72: optional i32 sub_group_id // 标识 1标识是短途特惠 2标识出租车
    73: optional string sub_intro_icon  // 优选出租车icon图标
    74: optional MixedFeeMsg mixed_fee_msg // 混合支付的预估价信息
    75: optional string cutoff_fee_msg // 预估快车价信息
    76: optional string cutoff_fee_info // 预估快车价信息
    77: optional string agg_discounts_desc // 聚合品类价格优惠描述
    78: required string highlight_type // 高亮类型
    79: required string brand_logo // 品牌Logo
    80: optional i32 close_checkbox //不展示勾选框 1是不展示
}

struct UsageInfo {
    // "alert": "滴滴特快今日使用次数已用完", // 弹窗
    // "left_text": "今日：还剩{2}次，可用3次", // 左侧文案
    // "right_text": "建议仅在急需用车时使用", // 右侧
    1: required string alert
    2: required string left_text
    3: required string right_text
}

struct CategoryInfo {
    1: required i16 category_id //与estimate_data中的category_id对应
    2: required string name //eg: 经济型/舒适型
    3: required string underline_color //分类下划线颜色
}

struct SpecialRule {
    1: required string multi_scene_text //多场景通用文案
    2: required map<string,string> single_scene_text //单场景多车型文案 key为RuleType
}

struct PromoteSalesRule {
    1: required string multi_scene_text //多场景通用文案
    2: required map<string,string> single_scene_text //单场景多车型文案 key为RuleType
    3: required string link_url
}


struct PluginPageInfo {
    1: required i32 type
    2: required string show_h5
    3: required string confirm_h5
}

struct UserPayInfoItem {
    1: required string tag //支付方式id
    2: required string msg // 支付方式描述
    3: required string business_const_set // 企业成本中心设置
    4: optional i16 disabled // 是否可用
    5: required i16 isSelected // 是否选中
    6: optional string car_support_desc // 车型支持描述信息
}

struct UserPayInfo {
    1: required string title // 标题
    2: required string sub_title // 副标题
    3: required list<UserPayInfoItem> payment_list // 支付列表
}

struct GuideInfo {
    1: required i32 show_type
    2: optional string title
    3: optional string background_url
    4: optional string link_url
    5: optional string link_text
    6: optional string dialog_id
    7: optional string dialog_close_type
    8: optional i32 select_product_category
    9: optional string left_button_text
    10: optional string right_button_text
}

struct EstimateResponse {
    1: required list<EstimateData> estimate_data
    2: required string estimate_trace_id
    3: required i32 is_support_multi_selection //是否支持多选，预约单时不支持 0-不支持；1-支持
    4: required list<CategoryInfo> category_info_list //分组配置
    5: required SpecialRule special_rule //价格沟通组件文案
    6: required string fee_detail_url //费用明细页地址
    7: optional PluginPageInfo plugin_page_info //动调、春节服务费等发单拦截页
    8: required UserPayInfo user_pay_info // 6.0支付方式并集
    9: optional string pay_type_disable_msg // 用户所选支付方式与勾选表单支付方式不匹配
    10: optional GuideInfo guide_info //弹窗信息
    11: optional PromoteSalesRule promote_sales_rule //营销类沟通文案
    12: optional BrandInfo brand_info //品牌信息
}

struct BrandInfo {
    1: optional string title
    2: optional string sub_title
    3: optional string bg_img
    4: optional string start_color
    5: optional string end_color
    6: optional string font_color
}


struct MultiEstimatePriceResponse {
    1: required string  errno
    2: required string  errmsg
    3: optional EstimateResponse data
}


struct LuxMultiEstimatePriceRequest {
    1: required string    token
    2: required string    appversion
    3: required i32       access_key_id //端来源标识 http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=118857095
    4: required i32       channel //客户端下载来源
    5: required i32       client_type //区分客户端：安卓乘客端 1； IOS乘客端 101； 所有webapp客户端 201； 企业级 301 ；openApi 401；导流系统 501；
    6: required string    lang //语言
    7: required string    a3_token //反作弊token
    8: required string    pixels //分辨率
    9: required string    maptype //地图类型
    10: required string   imei //设备号
    11: required string   suuid //设备识别id
    12: required string      terminal_id  //终端标识，区分不同端来源;
    13: required i32      origin_id //品牌ID:1滴滴； 2优步；3长平,默认滴滴
    14: required i32      platform_type //1 IOS 2 android 3 webapp 4 oepenapi 5 b2b 6 guide
    15: required string   openid //第三方平台id 微信openid 支付宝openid
    16: required string   from //暂不知道用途，建议透传主预估参数

    /**起终点相关信息**/
    30: required double     lat
    31: required double     lng
    32: required double     from_lat
    33: required double     from_lng
    34: required string     from_poi_id
    35: required string     from_poi_type
    36: required string     from_address
    37: required string     from_name
    38: required double     to_lat
    39: required double     to_lng
    40: required string     to_poi_id
    41: required string     to_poi_type
    42: required string     to_address
    43: required string     to_name
    44: required string     dest_poi_code
    45: required string     dest_poi_tag
    46: required string     use_dpa //是否使用溢价保护

    /*订单属性等信息*/
    50: required string     menu_id //顶导ID
    51: required i32        page_type //页面类型
    52: required i32        call_car_type //代叫类型
    53: required string     call_car_phone //代叫手机号
    54: required i32        user_type //用户类型：1普通用户；2企业用户
    55: required string     departure_time  //时间戳
    56: required i32        payments_type //支付类型
    57: required i32        order_type //订单类型
    58: required i32        origin_page_type // 原始入口页面（如预约跳转接送机）

    /*接送机相关*/
    120: optional string    flight_dep_code // 航班出发地三字码,如CTU
    121: optional string    flight_dep_terminal // 航班出发航站楼，如T2
    122: optional string    traffic_dep_time // 航班起飞时间字符串
    123: optional string    flight_arr_code // 航班落地三字码,如CTU
    124: optional string    flight_arr_terminal //航班落地航站楼，如T2
    125: optional string    traffic_arr_time // 航班到达时间字符串
    126: optional string    traffic_number // 航班号，如CA1405
    127: optional i16       airport_type // 接送机类型 1-接机，2-送机，端上入口，无入口传0
    128: optional i32       airport_id // 接机时为航班落地航站楼id，送机时为出发航站楼id，如1573
    129: optional i32       shift_time // 用车偏移时间（接机时，单位：秒）
    130: optional i16       railway_type
    131: optional i32       railway_id

    /*交互相关参数*/
    140: optional string    luxury_select_carlevels //选中车型, 示例: 1000 1500
    141: optional string    luxury_select_driver  //选中司机 示例: -1 580543123784568

    142: optional i32       business_id
    143: optional i32       require_level
    144: optional string    oid
    145: optional i16       tab_type //预估tab 0车型 1司机
    146: optional string    custom_feature //用户勾选的个性化增值服务
    147: optional i32       product_category //品类类型
    148: optional i16       is_close_prefer //是否关闭乘客推荐
    149: optional i16       is_multi_select //是否多勾
    150: optional i32       source_id
}

struct TipOption {
    1: optional i16 option_id
    2: optional i16 currency
    3: optional string text
    4: optional bool is_select
    5: optional string icon
}

struct DispatchFee {
    1: required string head
    2: required string sub_head
    3: required list<TipOption> tip_option
}

struct CommentOption {
    1: optional i16 option_id
    2: optional string text
    3: optional bool is_select
}

struct Comment {
    1: required string head
    2: required string sub_head
    3: required list<CommentOption> comment_option
}

struct LuxMultiEstimatePriceResponse {
    1: required i32 errno
    2: required string  errmsg
    3: optional LuxEstimateResponse data
}

struct LuxEstimateResponse {
    1: required list<CarEstimateData> estimate_car_level_data
    2: required list<DriverEstimateData> estimate_driver_level_data
    3: required PreferInfo prefer_info
    4: required string title
    5: required string sub_title
    6: required string head_img
    7: required string start_bg_color
    8: required string end_bg_color
    9: required string estimate_trace_id
    10: required i16 default_select_tab
    11: required i16 theme
    12: required i16 disable
    13: required i16 show_tab
    14: required string sub_title_link
    15: required DispatchFee dispatch_fee //红包
    16: required Comment comment //捎话
    17: optional list<CustomData> custom_info //个性化服务数据
    18: optional string custom_head //个性化服务文案
    19: optional string custom_head_link //个性化服务说明
}

struct CustomData {
    1: required i32 id // 个性化服务唯一id
    2: optional list<i32> mutex_ids // 互斥服务id
    3: required i32 max // 个性化服务数量限制
    4: required string title // title
    5: optional string desc // 摘要，如"限时0元"
    6: optional string price // 账单计价
    7: required string origin_price // 原价，固定配置，端上用于删除线展示
    8: required string unit // 单位，如："人"或"次"
    9: required string num // 已确认的人数或次数，默认为1人或1次，但不代表用户已勾选此服务
    10: required i32 selected_count // 已选择的人数/次数，默认为0
    11: optional string detail // 详情
    12: required i32 status // 状态，1-服务正常，2-服务不可选，点击出tips
    13: optional string tips // 提示
    14: optional string icon // 图标
    15: optional string num_selector_title // 选择数量的标题
    16: optional string num_selector_subtitle // 服务数量选择的副标题
    17: optional ServicePopup popup // 个性化服务弹窗，如新手引导弹窗
    18: optional string unit_price // 现价的单价
    19: optional string origin_unit_price // 原价的单价
    20: required string light_icon
    21: required string product_id
    22: required string service_id
}

struct PreferInfo {
    1: required string title
    2: required string remark
    3: required list<PreferOption> prefer_option
    4: optional string head
    5: optional string head_link
    6: optional i16 is_support_title
    7: optional i16 is_support_remark
    8: optional i16 is_im_direct_send
}

struct PreferOption {
    1: optional i16 id
    2: optional string text
    3: optional bool is_select
    4: optional string gray_icon
    5: optional string light_icon
    6: optional string toast
}

struct CarEstimateData {
    1: required string intro_msg
    2: required string intro_icon
    3: required string profile_link
    4: required string fee_detail_url
    5: optional list<FeeDescItem> fee_desc_info
    6: optional string fee_msg
    7: required string estimate_id
    8: required i32 require_level
    9: required i32 business_id
    10: optional i16 combo_type
    11: required i32 product_category
    12: optional i32 driver_id
    13: optional i16 is_real_driver
    14: required i16 is_default
    15: optional double estimate_fee //企业豪华车费用
    16: required string car_feature
}

struct DriverEstimateData {
    1: required string intro_msg
    2: required string intro_icon
    3: required string profile_link
    4: required string fee_detail_url
    5: optional list<FeeDescItem> fee_desc_info
    6: optional string fee_msg
    7: required string estimate_id
    8: required i32 require_level
    9: required i32 business_id
    10: optional i16 combo_type
    11: required i32 product_category
    12: optional string driver_id
    13: optional i16 is_real_driver
    14: required i16 is_default
    15: optional i16 is_online
    16: optional i16 down_broadcast
    17: optional double estimate_fee //企业豪华车费用
    18: required string driver_desc
    19: required string driver_desc_link
}

struct FeeDescItem {
    1: required string fee_desc
    2: optional string fee_desc_icon
}

struct GetInvitationInfoRequest {
    1: required string invite_oid //邀约订单号
    2: required string token
    3: required string app_version
    4: optional i32 access_key_id
}

struct InvitationButton {
    1: required i32 type
    2: required i32 theme_type
    3: required string color
    4: optional string end_color
    5: required string text
}

struct InvitationTag {
    1: required string text
    2: required string text_color
    3: required string tag_color
}

struct InvitationJoinInfo {
    1: required string image
    2: required string msg
}

struct AddrPageInfo {
    1: required string title
    2: required string sub_title
}

struct InvitationEstimateInfo {
    1: optional string order_type
    2: optional string departure_range
    3: optional string menu_id
    4: optional AddrPageInfo addr_page_info
}

struct ShareInfo {
    1: required string path
    2: required string title
    3: required string image_url
    4: optional string bg_img
    5: optional string button_text
    6: optional i16 style
    7: optional string price
    8: optional string origin_price
    9: optional string start_name
    10: optional string dest_name
}

struct InviteOrderInfo {
    1: optional string oid
    2: optional string product_id
    3: optional string area
}

struct InvitationExtraInfo {
    1: optional ShareInfo share_info
    2: optional InviteOrderInfo order_info
}

struct InvitationData {
    1: optional string bg_image //背景图片
    2: optional string detail_url//详情链接
    3: optional string title//标题
    4: optional string sub_title//副标题
    5: optional string departure_text//出发时间文案
    6: optional string start_name//出发地址
    7: optional string start_county//出发区县
    8: optional string dest_name//目的地址
    9: optional string dest_county//目的区县
    10: optional string price//价格
    11: optional string origin_price//划线价
    12: optional string high_light_color//高亮色值
    13: optional string status//状态
    14: optional string role//角色 1邀约人 2受邀人
    15: optional list<InvitationButton> button//按钮
    16: optional InvitationTag tag//标签
    17: optional InvitationJoinInfo joined_info//已加入行程文案
    18: optional InvitationEstimateInfo estimate_info//预估信息
    19: optional InvitationExtraInfo extra_info//额外信息
    20: optional string page_title//页面title
    21: optional i32 invitation_type//邀约类型 1 拼成乐 2 城际拼车
}

struct GetInvitationInfoResponse {
    1: required i32 errno
    2: required string errmsg
    3: optional InvitationData data
}

struct SubmitTailorServiceRequest {
    1: required string    token
    2: required string    source // 0 预估 1 IM
    3: required string    options
    4: optional i32       business_id
    5: optional i32       require_level
    6: optional string    oid
}

struct SubmitTailorServiceResponse {
    1: required string  errno
    2: required string  errmsg
}

struct SubmitCommonExpressionRequest {
     1: required string token
     2: required string common_expression
     3: required i32 business_id
     4: required i32 action_type
     5: required string lang
     6: required i64 uid
 }

 struct SubmitCommonExpressionResponse {
     1: required i32 errno
     2: required string errmsg
 }

 struct VerifyLuxuryPreferPageSensitiveWordRequest {
    1: required string to_be_verify_word
    2: required string lang
    3: required string token
    4: required i32    detection_type
 }

 struct VerifyLuxuryPreferPageSensitiveWordResponse {
     1: required i32 errno
     2: required string errmsg
  }

struct CapMultiEstimatePriceRequest {
    /** 用户相关 **/
    1: required string token
    2: required i32    user_type // 用户类型，0表示普通用户，2表示企业用户
    // 3: required string openid

    /** 端相关参数 **/
    4: required string    app_version
    5: required i32       access_key_id
    6: required i32       channel
    7: required i32       client_type
    8: required string    lang
    9: required string    maptype
    // 10: required string   imei
    // 11: required string   suuid
    12: required string   terminal_id
    13: required i32      origin_id
    14: required i32      platform_type
    15: required string   from // 作用？
    16: required i32      page_type

    /**起终点相关信息**/
    30: required double     lat
    31: required double     lng
    32: required double     from_lat
    33: required double     from_lng
    34: required string     from_poi_id
    35: required string     from_poi_type
    36: required string     from_address
    37: required string     from_name
    38: required double     to_lat
    39: required double     to_lng
    40: required string     to_poi_id
    41: required string     to_poi_type
    42: required string     to_address
    43: required string     to_name

    /*订单属性等信息*/
    50: required string     menu_id
    51: required string     departure_time  //时间戳
    52: required string     multi_require_product //用户勾选项
    53: required i32        payments_type // 用户选择的支付方式

    54: required string     tab_list //tab列表
    55: optional string     stopover_points //途经点参数 json array 字符串
}

//新出租入参
struct TaxiMultiEstimatePriceRequest {
    /** 客户端参数**/
    1: required string    token
    2: required string    app_version
    3: required i32       access_key_id
    4: required i32       channel
    5: required i32       client_type
    6: required string    lang
    7: required string    a3_token
    8: required string    pixels
    9: required string    maptype
    10: required string   imei
    11: required string   suuid
    12: required string   terminal_id
    13: required i32      origin_id
    14: required i32      platform_type
    15: required string   openid
    16: required i32      guide_type
    17: required string   from
    18: required string   preferred_route_id
    19: required string   dialog_id
    20: optional string   source_channel
    21: optional double   screen_scale
    22: optional i32 estimate_style_type // 预估表单样式，0:老样式，1:单行

    /**起终点相关信息**/
    30: required double     lat
    31: required double     lng
    32: required double     from_lat
    33: required double     from_lng
    34: required string     from_poi_id
    35: required string     from_poi_type
    36: required string     from_address
    37: required string     from_name
    38: required double     to_lat
    39: required double     to_lng
    40: required string     to_poi_id
    41: required string     to_poi_type
    42: required string     to_address
    43: required string     to_name
    44: required i32        dest_poi_code
    45: required string     dest_poi_tag

    /*订单属性等信息*/
    50: required string     menu_id
    51: required i32        page_type
    52: required i32        call_car_type
    53: required string     call_car_phone
    54: required i32        user_type // 用户类型 0表示普通用户，2表示企业用户
    55: required string     departure_time  //时间戳
    56: required i32        payments_type //支付类型
    57: required string     multi_require_product //用户勾选项
    58: required i32        has_scroll //客户端是否进行过上拉操作
    59: required i32        order_type //预约单
    60: required i32        origin_page_type // 原始入口页面（如预约跳转接送机）
    61: required string     stopover_points //途经点参数 json array 字符串

    /*业务信息*/
    80: optional i32        shake_flag
    81: optional string     pre_trace_id
    82: optional string     departure_range //城际拼车订单出发时间

    /*接送机相关*/
    120: optional string    flight_dep_code // 航班出发地三字码,如CTU
    121: optional string    flight_dep_terminal // 航班出发航站楼，如T2
    122: optional string    traffic_dep_time // 航班起飞时间字符串
    123: optional string    flight_arr_code // 航班落地三字码,如CTU
    124: optional string    flight_arr_terminal //航班落地航站楼，如T2
    125: optional string    traffic_arr_time // 航班到达时间字符串
    126: optional string    traffic_number // 航班号，如CA1405
    127: optional i16       airport_type // 接送机类型 1-接机，2-送机，端上入口，无入口传0
    128: optional i32       airport_id // 接机时为航班落地航站楼id，送机时为出发航站楼id，如1573
    129: optional i32       shift_time // 用车偏移时间（接机时，单位：秒）

    /*活动相关*/
    150: optional i32       activity_id // 活动id，去掉原有x_activity_id

    /*B端车票*/
    155: optional string       biz_ticket // 车票ID

    /*专车相关*/
    160: optional i16       too_far_order_limit // 专车是否限制超远途订单

    /*特殊场景*/
    170: optional i32       special_scene_param // 极端天气场景（如暴雨等）

    180: optional string    luxury_select_carlevels
    181: optional string    luxury_select_driver
    190: optional string    agent_type
}

//新出租响应结果
struct TaxiMultiEstimatePriceResponse {
    1: required string  errno
    2: required string  errmsg
    3: optional TaxiEstimateResponse data
}

//新出租预估结构
struct TaxiEstimateResponse {
    1: required list<EstimateData> estimate_data
    2: required string estimate_trace_id
    3: required i32 is_support_multi_selection //是否支持多选，预约单时不支持 0-不支持；1-支持
    4: required list<CategoryInfo> category_info_list //分组配置
    5: required SpecialRule special_rule //价格沟通组件文案
    6: required string fee_detail_url //费用明细页地址
    7: optional PluginPageInfo plugin_page_info //动调、春节服务费等发单拦截页
    8: required UserPayInfo user_pay_info // 6.0支付方式并集
    9: optional string pay_type_disable_msg // 用户所选支付方式与勾选表单支付方式不匹配
    10: optional GuideInfo guide_info //弹窗信息
    11: optional PromoteSalesRule promote_sales_rule //营销类沟通文案
    12: optional BrandInfo brand_info //品牌信息
}

struct AggregationCapEstimateData {
    1: required string intro_msg // 品牌文案
    2: required string fee_msg // 价格信息
    3: required i32 require_level //车型
    4: required i32 business_id //业务线
    5: required i32 product_id //产品线ID
    6: required i32 combo_type //combo_type
    8: required string estimate_id // 预估id
    12: required i32 category_id // 分组id
    13: required i16 select_type // 当前预估在表单是否被选中 0-未选中 1-被选中
    16: optional i32 seat_num //当前拼车的座位数
    17: required string fee_amount //价格
    18: optional list<string> route_id_list //路线ID
    20: optional list<NewFormFeeDesc>    fee_desc_list // 价格展示（复用新表单）
    35: required string extra_price_desc // 服务费
    // 发单参数
	21: required CapExtraMap extra_map
    34: optional NewFormUserPayInfo user_pay_info
    36: required i32    product_category
    22: required i32 count_price_type
    33: required i32 level_type

}

struct MultiAggregationCapEstimateData { // 临时
    1: required string intro_msg // 品牌文案
    2: required string fee_msg // 价格信息
    3: required i32 require_level //车型
    4: required i32 business_id //业务线
    5: required i32 product_id //产品线ID
    6: required i32 combo_type //combo_type
    8: required string estimate_id // 预估id
    12: required i32 category_id // 分组id
    13: required i16 select_type // 当前预估在表单是否被选中 0-未选中 1-被选中
    16: optional i32 seat_num //当前拼车的座位数
    17: required string fee_amount //价格
    18: optional list<string> route_id_list //路线ID
    20: optional list<NewFormFeeDesc>    fee_desc_list // 价格展示（复用新表单）
    35: required string extra_price_desc // 服务费
    // 发单参数
	21: required CapExtraMap extra_map
    34: optional NewFormUserPayInfo user_pay_info
    23: optional list<AggregationCapEstimateData> sub_product // 样式2 多一层嵌套 的 不拼车聚合
    36: required i32    product_category
    22: required i32 count_price_type
    33: required i32 level_type

}

struct CapExtraMap{
    1:required i32    product_id
    2:required i32    business_id
    3:required i32    combo_type
    4:required i32    require_level
    5:required i32    level_type
    6:required i32    count_price_type
    7:required i32    product_category
}

struct CapEstimateData {
    1: required string intro_msg // 品牌文案
    2: required string fee_msg // 价格信息
    3: required i32 require_level //车型
    4: required i32 business_id //业务线
    5: required i32 product_id //产品线ID
    6: required i32 combo_type //combo_type
    8: required string estimate_id // 预估id
    12: required i32 category_id // 分组id
    13: required i16 select_type // 当前预估在表单是否被选中 0-未选中 1-被选中
    16: optional i32 seat_num //当前拼车的座位数
    17: required string fee_amount //价格
    18: optional list<string> route_id_list //路线ID
    19: optional list<AggregationCapEstimateData> sub_product // 聚合产品
    20: optional string bubble_tip // 冒泡提示
    25: optional list<NewFormFeeDesc>    fee_desc_list // 价格展示（复用新表单）
    26: optional list<NewFormMultiPrice> multi_price_list // 单品类多价格展示 (复用新表单)
    31: required string extra_price_desc // 服务费
    // 发单参数
	28: required CapExtraMap extra_map
    27: optional NewFormUserPayInfo user_pay_info
    // 样式2的字段
    23: optional list<MultiAggregationCapEstimateData> multi_product // 样式2 多一层嵌套 的 不拼车聚合
    35: required i32    product_category
    22: required i32 count_price_type
    33: required i32 level_type
}

struct Category {
    1: required i32 category_id // 分组id
    2: required i16 seat_num // 座位信息
    3: required string seat_desc // 座位文案
    4: required string seat_prefix_desc
    5: required i16 enable_display // 是否渲染该品类 0/1
}


struct CapEstimateResponse {
    1: required list<CapEstimateData> estimate_data
    2: required string estimate_trace_id
    3: required string fee_detail_url
    4: required list<Category> category_info_list
    5: required i32 is_support_multi_selection //是否支持多选，预约单时不支持 0:不支持；1:支持
    9: optional string seat_prefix_desc // 座位前缀文案
    10: required UserPayInfo user_pay_info // 6.0支付方式并集
    12: required i32 form_style // 不拼座表单样式
}

struct CapMultiEstimatePriceResponse {
    1: required string  errno
    2: required string  errmsg
    3: optional CapEstimateResponse data
}


struct IntercityEstimatePriceRequest {
    /** 用户相关 **/
    1: required string token
    2: required i32    user_type // 用户类型，0表示普通用户，2表示企业用户
    3: required string openid

    /** 端相关参数 **/
    4: required string    app_version
    5: required i32       access_key_id
    6: required i32       channel
    7: required i32       client_type
    8: required string    lang
    9: required string    maptype
    10: required string   imei
    11: required string   suuid
    12: required string   terminal_id
    13: required i32      origin_id
    14: required i32      platform_type
    15: required string   from
    16: required string   pixels
    17: required i32      datatype

    /**起终点相关信息**/
    30: required double     lat
    31: required double     lng
    32: required double     from_lat
    33: required double     from_lng
    34: required string     from_poi_id
    35: required string     from_poi_type
    36: required string     from_address
    37: required string     from_name
    38: required double     to_lat
    39: required double     to_lng
    40: required string     to_poi_id
    41: required string     to_poi_type
    42: required string     to_address
    43: required string     to_name

    /*订单属性等信息*/
    50: required string     menu_id
    51: required string     multi_require_product //用户勾选项
    52: required i32        payments_type // 用户选择的支付方式
    53: required i32        order_type // 订单类型
    54: required i32        page_type  // 页面类型
    55: optional string     departure_range //城际拼车订单出发时间
    56: optional string     agent_type // 是否为客企扫二维码上车预估

    57: optional string     seat_detail_info
    58: optional i64 route_id

     /*webx公参*/
    191: optional string    xpsid
    192: optional string    xpsid_root
}

struct IntercityPriceInfoDesc {
    1: required string content // 文字支持高亮
    2: required string left_icon // 左侧icon
    3: required string font_color // // ex：抵{18}元；font_color表示 18的颜色
    4: required string bg_fill_color // 背景颜色
    5: required string border_color // 边框颜色
    6: required string font_text_color // ex：抵{18}元；font_text_color表示 抵和元的颜色
}


struct IntercitySeatConfig {
    1: required i16 value
    2: required string label_2
}

struct IntercityCarpoolSeatModule {
    1: required list<IntercitySeatConfig> seat_config
    2: required i16 select_value
    3: required i16 select_index
    4: required string seats_exceed_toast // 座位数超选提示
    5: required string button_desc
    6: required i16 select_count_child_ticket // 儿童票人数
}

struct IntercityMatchRoutesData {
    1: required string time_title
    2: required string sub_title
    3: required string left_text
    4: required string right_text
    5: required list<IntercityTimeSpan> time_span
    6: optional bool first_span_fit // 第一组的第一个时间片是否可以直接发单，不弹窗
}

struct IntercityMatchShowSkuData {
    1: required string bottom_text
    2: required string signal_text
    3: required list<ShowSkuTimeSpanRange> time_span
}

struct IntercitySpecialPriceText {
    1: required list<i32> rule_type     //规则
    2: required string text             //文案
}

struct IntercityFormUserPayInfo {
    1: required string payment_id //支付方式id
    2: optional string business_const_set // 企业成本中心设置
}

struct IntercityEstimateData {
    1: required string intro_image // 车型图片名称
    2: required string fee_msg // 价格信息
    3: required string bg_image // 背景图
    4: required IntercitySpecialPriceText special_price_text // 特殊价格沟通
    5: required string border_color // 边框色
    6: required string corner_image // 选中右上角图标
    7: required i32 route_type // 路线类型
    8: required i32 require_level // 车型
    9: required i32 business_id // 业务线
    10: required i32 product_id // 产品线ID
    11: required i32 combo_type // combo_type
    12: required i32 product_category // 品类ID
    13: required string estimate_id // 预估id
    14: required string feature_title // 特点标题
    15: required string feature_sub_title // 特点副标题
    16: required list<IntercityPriceInfoDesc> price_info_1 // 价格信息第一行
    17: required list<IntercityPriceInfoDesc> price_info_2 // 价格信息第二行
    18: required IntercityCarpoolSeatModule carpool_seat_module //拼车座位数组件
    19: required IntercityMatchRoutesData match_routes_data // 城际拼车时间片组件
    20: required i32 combo_id // 路线ID，发单参数
    21: required i32 select_type // 是否默认勾选

    23: required string fee_amount // 单纯价格，供端埋点用
    24: optional IntercityFormUserPayInfo user_pay_info // user_pay_info
    25: optional IntercityMatchShowSkuData match_show_sku_data // 城际拼车卡片扩展库存推荐
    26: optional bool is_unavailable // 城际拼车卡片是否可用，是否可以通过外漏的卡片直接发单
    27: optional i32 hit_show_h5_type //预估show h5命中类型
    28: optional i32 support_select_seat // 是否支持选座弹层
}


struct IntercityEstimateResult {
    1: required list<IntercityEstimateData> estimate_data
    2: optional UserPayInfo user_pay_info
    3: required string estimate_trace_id
    4: required string fee_detail_url
    5: required string background_url
    6: required string detail_url
    7: required string error_url
    8: required string force_notice_toast
    9: required IntercityCarpoolSeatModule carpool_seat_module //拼车座位数组件, 两个品类公用的
    10: optional PluginPageInfo plugin_page_info //春节服务费等发单拦截页
    11: optional string barrage_text
}

struct IntercityEstimatePriceResponse {
    1: required string  errno
    2: required string  errmsg
    3: optional IntercityEstimateResult data
}



struct ProductTag{
    1: optional string icon // 无下发, 使用默认图标
    2: required string content
}

struct GuideTarget {
    1: required string product_msg // 30KM以上推荐{xxx}, 含颜色
    2: required string product_desc
    3: required list<ProductTag> product_tag // 天天特价
    4: required string btn_content // 按钮文案
    5: required string menu_id // 导流到哪个顶导
    6: required int32  page_type // 导流到哪个页面
    7: required string menu_name // 导流到页面的名称
    8: required string to_link // 跳转链接
}

struct NoGuideMsg {
    1: required string title
    2: required string desc
}

struct PageGuideResponseData{
    1: optional string bg_img // 背景图(头部), 没有就用默认
    2: optional string page_title // 页面标题

    3: optional GuideTarget target // 目标品类(页面)相关数据, 有导流时下发
    4: optional NoGuideMsg  no_guide // 无导流品类时下发, 其他情况不下发
}

struct PageGuideResponse {
    1: required i32                   errno
    2: required string                errmsg
    3: required PageGuideResponseData data
}

struct PageGuideRequest {
    // 用户信息
    1: required string    token
    // 端信息
    2: required string app_version
    3: required i32    access_key_id
    4: required string menu_id
    5: required i32    page_type
    6: required string imei
    // 地理信息
    7: required double cur_lat
    8: required double cur_lng
    9: required double from_lat
   10: required double from_lng
   11: required double to_lat
   12: required double to_lng
   13: required string maptype
}

struct B2bMultiEstimateDataRequest {
    /** 客户端参数**/
    1: required string          token
    2: required string          app_version
    3: required i32             access_key_id
    4: required i32             channel
    5: required i32             client_type
    6: required string          lang
    7: required string          maptype
    8: required string          terminal_id
    9: required i32             origin_id
    10: required i32            platform_type
    11: required string         area
    12: required string         estimate_trace_id
    13: required string         estimate_id_list // encode过的预估id列表["eid1","e1d2"]
    14: required i16            type // 枚举 标识想要获取哪些数据 1:动调信息 2:特殊费用 3: 费用详情 请求费用详情时，estimate_id_list里请只放一个eid 返回只返回一个 4 专车预约单计价费用
}

struct B2bDynamicMemberInfo {
    1: required i16             level_id // 会员等级
    2: required string          level_name
    3: required i16             is_auto
}

struct B2bDynamicInfo {
    1: required double                 member_dynamic_capping // 会员动调保护封顶价格（-1不封顶，0免动调，>0 具体金额）
    2: required double                 dynamic_diff_price // 实际动调会花费的金额 如果命中会员溢价保护比如只需要付十块 那这个地方价格就是10
    3: required double                 dynamic_price_without_member_capping // 没有会员溢价保护时的动调增幅价格
    4: required bool                   is_hit_member_capping // 是否触发会员封顶
    5: required bool                   is_hit_dynamic_capping // 是否命中动调封顶
    6: required i32                    if_use_times // 等于1：当前动调为倍数增长 实际增长的倍数为dynamic_times的值 否则 为金额涨幅
    7: required double                 dynamic_times // 如：0.2 表示0.2倍
    8: required double                 dynamic_total_fee // 动调后账单价格

}

struct B2bSpecialFee {
    1: optional double         cross_city_fee // 跨城费
    2: optional double         highway_fee  // 高速费
    3: optional double         limit_fee    // 预约单限制基础费
    4: optional double         red_packet_fee   // 春节服务费
    5: optional i32            count_price_type // 价格类型是否是一口价
    6: optional double         designated_driver_fee // 选司务员费
    7: optional double         start_price // 起步价
    8: optional double         energy_consume_fee // 综合能耗费
    9: optional double         empty_distance // 空驶距离or超里程距离，公里
    10: optional double        normal_distance // 普通时段计费里程,公里
    11: optional double        normal_time // 普通时段计费时间,分钟
    12: optional double        total_fee // 预估总价格，单位：元
    13: optional double        limit_lowest_fee // 最低消费金额
    14: optional double        start_distance // 起步里程
    15: optional double        start_time // 起步时长
    16: optional string        fee_detail_info // 账单获取批量预估详情页的fee_detail_info，请自行解析 http://promise.intra.xiaojukeji.com/promise#/chibi/doc?id=getFeeDetailInfos_5ff45253ae794a560a7ac7c2&parent_id=5ff45253ae794a560a7ac7c2&type=file&key=getFeeDetailInfos
}

struct B2bDetailDataItem {
    1: required string                  estimate_id // 预估ID
    2: optional B2bDynamicInfo          dynamic_info // 动调相关信息
    3: optional B2bSpecialFee           special_fee // 特殊费用
    4: optional B2bDynamicMemberInfo    member_info // 会员相关信息(动调使用)
    5: optional string                  fee_detail // 费用详情 json 透传账单费用详情，请自行解析 http://promise.intra.xiaojukeji.com/promise#/chibi/doc?id=getFeeDetailInfos_5ff45253ae794a560a7ac7c2&parent_id=5ff45253ae794a560a7ac7c2&type=file&key=getFeeDetailInfos
    6: optional string                  price_token // 请求计价规则接口需要的token
    7: optional string                  booking_rules // 用于预约单专车计价规则获取
}

struct B2bMultiEstimateData {
    1: required map<string,B2bDetailDataItem> item // key为预估id
}

struct B2bMultiEstimateDataResponse {
    1: required i16 errno
    2: required string errmsg
    3: optional B2bMultiEstimateData data
}

struct NewFormRedirectionInfo {
    1: optional string poi_id
    2: optional string displayname
    3: optional string address
    4: optional double lat
    5: optional double lng
    6: optional string coordinate_type
    7: optional i32 city_id
    8: optional string city_name
    9: optional string gap
}

struct NewFormAutoDriveRedirectionInfo {
    1: required NewFormRedirectionInfo start_info //无人车上车点
    2: required NewFormRedirectionInfo end_info //无人车下车点
}

struct NewFormUserPayInfo{
	1: required string payment_id
	2: required string business_const_set // ??
	4: optional string fee_msg_prefix // 该支付方式前缀描述
}

struct NewFormFeeDesc {
	1: required string border_color // 边框RGB色值, 如#000000, 空串标识无边框
	2: required string content
	3: required string icon
    4: required double amount
    5: required i32    type
    6: required string text_color
    7: required string highlight_color
}

struct NewFormMultiPrice{
	1: required string fee_msg
    2: required double fee_amount
	3: optional NewFormFeeDesc fee_desc
	4: optional string fee_icon_url
	5: required i32 is_large_font
	6: required i32 font_size
    7: optional string  fee_msg_template
}

struct NewFormPreferData{
	1: required string desc
	2: required string fee_msg
	3: required string info_url
	4: required i32 id //增值服务id
	5: required i32    count
	6: required i32    is_selected //是否勾选
	7: required list<NewFormPreferDataTag> tag_list
    8: optional string jump_url
    9: optional string fee_amount
    10: optional i32   single_style //单钩样式
    11: optional list<NewFormFeeDesc> fee_desc_list //
}
struct NewFormPreferDataTag{
	1: required string content
	2: required string icon
}

struct NewFormCarpoolSeatOption{
	1: required string label
	2: required i32    value
	3: required bool   selected
}

struct NewFormExtraEstimateData {

	1: required string           fee_msg
	2: optional list<NewFormFeeDesc>    fee_desc_list

	3: required i32 count_down
	5: required map<string, string> extra_order_params

	6: required string left_down_icon_text
}

struct NewFormUnselectedData {
    1: optional list<NewFormFeeDesc>    fee_desc_list
    2: optional list<NewFormMultiPrice> multi_price_list
}

struct RadioOption {
    1: required string label
    2: required i32 value
}

struct RadioSetting {
    1: required string field //当前组件对应的字段
    2: required bool is_hold // 常驻 不依赖勾选
    3: required i32 selected_value
    4: required bool need_refresh  //切换勾选后 是否要重新预估
    5: required list<RadioOption> options
    6: required i32 assembly_style  //标识拼座组件在表单上的展示位置，0是左边，1是右边
}

struct ModeListOption {
    1: required string title
    2: required string selected_title
    3: required string content
    4: required string value
}

struct OrderOption {
    1: required string field
    2: required string selected_value
    3: required string default_value
    4: required string title
    5: required list<ModeListOption> mode_list
}

struct Tag {
    1: required string content
    2: required string icon
}

struct NoticeInfo {
    1: optional list<Tag> tag_list
    2: optional string right_text
    3: optional string jump_url
    4: optional list<string> background_gradients
    5: optional string border_color
    6: optional i32 action_type
    7: optional string bubble_arrow_top
    8: optional OmegaItem omega
}

struct OmegaItem {
    1: optional OmegaValue show
    2: optional OmegaValue click
}

struct OmegaValue {
    1: required string key
    2: required map<string, i32> params
}

struct DisabledInfo {
    1: required string content
}

struct MapInfo {
    1: required string mapinfo_cache_token
    2: required bool show_mini_bus_station
    3: required string mapinfo_start_cache_token
    4: required string mapinfo_dest_cache_token
    5: required i32 best_view_type
    6: required string voy_route_id // 自动驾驶的路线id
    7: required i32 minibus_type //小巴类型：1-智能小巴（给端上跳转地图组件使用）
}

struct MapCurveInfo {
    1: required int32 is_draw_curve
    2: required string bubble_text
    3: required string jump_url
    4: optional int32 curve_type //曲线类型：0-不展示曲线；1-展示蓝灰曲线（小巴快线）；2-展示蓝色曲线（小巴普通&智能小巴）【na: 7.0.6, wx: 6.10.25以后使用】
}

struct NewFormEstimateData{
	// 标识
	1: required string estimate_id
	2: required i32    product_category

	// 一些场景标识
	3: required i32  hit_dynamic_price
	4: required i32  hit_show_h5_type
	5: required bool is_tripcloud

	// 车型数据
	6: required string car_title
	7: required string car_icon

	// 价格信息
	8: required string           fee_amount
	9: required string           fee_msg
   10: optional list<NewFormFeeDesc>    fee_desc_list
   11: optional list<NewFormMultiPrice> multi_price_list
   42: optional string          fee_msg_template
   44: optional string                  fee_msg_prefix_icon //费用描述前置icon

   27: optional NewFormUnselectedData   unselected_data
   37: optional string min_fee_amount
   38: optional string fee_range_template

	// 支付信息
	12: required NewFormUserPayInfo user_pay_info

	// 附加
	13: optional NewFormPreferData              prefer_data
	14: optional list<NewFormCarpoolSeatOption> carpool_seat_list
    15: optional list<string>                   route_id_list

	// 发单参数
	16: required NewFormExtraMap extra_map

	// 其他额外
	17: optional NewFormExtraEstimateData        extra_estimate_data // 木得办法
	18: optional NewFormAutoDriveRedirectionInfo auto_driving_address_info
	19: required i32                             is_selected //勾选状态
    20: optional string                          depart_tag
    21: optional string                          sub_intro_icon // 出租车盒子选中车型前的icon
    22: required RadioSetting                    radio_setting // 拼车选座组件/拼车顺路组件
    23: optional OrderOption                     order_option //市内拼车的顺路标签（实验，后续验证无收益记得下掉）

    26: optional GroupSubTitle              sub_title         // 车型标签
    28: optional i32 is_hide_price //是否计算价格 0:默认,需要计算价格  1:不计算

    30: optional double           need_pay_fee_amount //券前价，目前只有司乘议价会用到
    31: optional NewCarpoolSeatModule carpool_seat_module //小巴座位数组件
    32: optional NoticeInfo notice_info // 品类下发横条+跳转组件
    33: optional i16 disabled //不可用状态
    35: optional DisabledInfo disabled_info // 不可用信息
    36: optional MapInfo map_info // 带给地图的数据
    39: optional string multi_route_tip_type // 提示文案
    40: optional BargainRangePopup bargain_range_popup //多勾惠选车弹窗数据
    41: optional MapCurveInfo map_curve_info // 地图画线和气泡信息
    43: optional i16 is_single_route // 是否只展示一条预估路线 0否 1是

    45: optional i16 car_icon_type // 三方车型icon类型 0（老的）, 1（单车型新的）
}

struct TabExtraData {
    1: required Classify classify

}

struct Classify {
    1: required string time_text
}

struct BargainRangePopup {
    1: required double price_limit_upper // 价格滑动条上界
    2: required double price_limit_lower // 价格滑动条下界
    3: required double recommend_price_upper // 推荐价上界
    4: required double recommend_price_lower // 推荐价下界
    5: optional double wait_reply_price_upper // 等应答最高价
    6: required double fast_car_estimate_fee // 快车预估实付价格
    7: required double sp_fast_car_estimate_fee // 特惠快车预估实付价格
    8: required string title // 弹窗标题
    9: required string sub_title //弹窗副标题
    10: required string price_limit_upper_text // 价格上界锚点文案
    11: required string low_price_bubble_text  // 价低文案提示
    12: required string high_price_bubble_text // 加价文案提示
    13: required string btn_text // 确认出价文案
    14: required string underline_color //价格下划线颜色
    15: required string background_img  //弹窗背景图
}

// 发单参数
struct NewFormExtraMap{
    1:required i32    product_id
    2:required i32    business_id
    3:required i32    combo_type
    4:required i32    require_level
    5:required i32    level_type
    6:required i32    combo_id
    7:required i32    route_type
    8:required i32    is_special_price
    9:required i32    count_price_type
   10:optional string port_type
   11:required i32    bargain_from_type
   12:required i32    is_default_auth
   13:required i32    etp
   14:optional string extra_custom_feature
   15: optional string     departure_range //城际拼车订单出发时间
   16: optional i32        carpool_seat_num  // 用户选择的拼车座位数
   17: optional string     is_intercity_surprise_alone //城际自营惊喜独享标识
   18: optional list<string> need_auth_list // 待授权的协议列表
}

struct RecTag {
    1: required string content
	2: required string font_color
	3: required string border_color
	4: required list<string> bg_gradients //标签文字颜色
	5: required string icon_url
	6: required string highlight_color
}

struct RecData {
    1: optional RecTag rec_tag
    2: optional RecTag rec_right_tag
    3: optional string rec_bg_color
}

struct NewFormGroup{
    1: optional i32 type //1单车型｜车大，2三方盒子，3出租车盒子，4高峰盒子，99导流, 101 司乘议价
    2: optional list<string> products
    4: optional i32 is_selected
    5: optional string car_icon
    6: optional string car_title
    7: optional string popup_title
    8: optional string popup_sub_title
    9: optional string recommend_product //当用户没有选择出租车的时候，出租车盒子需要外露的一个单独的车型
    11: optional NewFormLinkInfo link_info //车大
    12: optional string guide_path //导流地址
    13: optional string button_text //导流按钮文案
    14: optional i32 box_id
    15: optional string box_desc
    16: optional string fee_desc
    17: optional string fee_desc_icon
    18: optional int32 jump_type
    20: optional string group_id // group主键id 规则: type + boxid||product_category||subgroup_id
    21: optional GroupSubTitle sub_title // 盒子的标签
    22: optional ButtonStyle button_style  // 按钮样式，不设置默认原样式
    23: optional NewFormBargainMsg bargain_msg // 司乘议价砍价信息
    24: optional list<NewFormFeeDesc> fee_desc_list // 盒子外层费用描述
    25: optional string popup_toast

    26: optional i32    action_type
    27: optional i32    guide_style
    28: optional bargainPopup bargain_popup //司乘议价弹框信息
    29: optional string fee_msg // 盒子外层费用
    39: optional string    fee_msg_template
    30: optional string fee_detail_url
    31: optional i32    style_type //字体样式 0 正常 1 大字
    32: optional i32    disable_shadow // 1 关闭阴影
    33: optional map<string, string> button_params //button透传参数、端上调用后端接口使用
    34: optional string guide_params
    35: optional i32    is_compressed //  1 压缩间距
    36: optional map<string, string> omg_data // 埋点参数

    37: optional string unselect_popup_title // 增加未勾选的盒子标题
    38: optional RecData rec_data // 推荐表单推荐区车型标签
    40: optional i16 car_icon_type
    41: optional i16    is_sub_nodisplay_etp    // 1 不展示etp 0 展示etp
    42: optional i32 popup_style_type // 0 默认， 1 弹层内 没有子车型暴漏
}
struct bargainPopup {
    1: optional string title //弹框标题
    2: optional string sub_title_min //子标题-低于推荐价
    3: optional string sub_title_equal //子标题-等于推荐价
    4: optional string sub_title_max //子标题-高于推荐价
    5: optional list<int32> left_price_tag //左侧价格标签
    6: optional list<int32> right_price_tag //右侧价格标签
    7: optional string button_text // 按钮文案
    8: optional BargainFeeMargin fee_margin //上下限信息
    9: optional NewFormFeeDesc fee_desc_content_template //费用描述信息的模版
    10: optional string fee_msg_template //预估价模版
}
struct BargainFeeMargin {
    1: optional BargainFeeMarginNode ceil //上限
    2: optional BargainFeeMarginNode floor1 //下限
    3: optional BargainFeeMarginNode floor2 //下下限
}
struct BargainFeeMarginNode {
    1: optional string amount //金额
    2: optional string notice //提示
}
struct ButtonStyle {
    1: required string border_color
    2: required string font_color
    3: required list<string> bg_gradients
}

struct NewFormBargainMsg{
    1: required string text
    2: required string guide_path
    3: required string bubble_img_url
    4: required string underline_color
    5: optional bool show_plus_and_minus
    6: optional string    fee_msg_prefix
    7: optional string    fee_msg_amount
}

struct GroupSubTitle {
    1: required string          content
    2: required string          font_color
    3: required string          border_color
    4: required list<string>    bg_gradients
    5: required string          icon_url
    6: required string          link_url
}

struct GroupCarTag {
    1: required string          content               // 车型标签文案
    2: required string          font_color            // 字体未选中颜色
    3: required string          font_select_color     // 字体选中颜色
    4: required string          border_color          // 边框未选中颜色
    5: required string          border_select_color   // 边框选中颜色
}

struct NewFormLinkInfo{
    1: required string link_product
    2: required i32    is_selected
    3: required NewFormPreferData prefer_data
    4: required string fee_msg
    5: required list<NewFormFeeDesc> fee_desc_list
    6: required string info_url
    7: required i32 is_strength
    8: required i32 select_style // 1表示单勾
}

struct NewFormLayout{
    1: required list<NewFormGroup> groups
    2: required i32 form_show_type
    3: required NewFormThemeData theme_data //是否支持多选，预约单时不支持 0-不支持；1-支持
    4: optional double price
    5: optional i32 category_id   // 三方表单侧边栏分组id
    6: optional i32 fold_type  //折叠状态
    7: optional i32 sub_category_type // 子标题
}

struct NewFormThemeData{
        1: optional i32    theme_type
        2: optional string theme_color//主题颜色
        3: optional list<string> selected_bg_gradients//选中品类背景渐变色
        4: optional list<string> outer_bg_gradients//品类外框背景渐变色
        5: optional string title //顶部推荐标题
        6: optional string icon //标题左侧Icon
        7: optional string right_text //右侧标题
        8: optional string right_icon //右侧icon
        9: optional string right_info_url //0.5盒子 右侧跳转链接
        10: optional i64 expire_time // 倒计时(秒)  (当前时间-券过期时间)/1000
        11: optional i64 disable_selected_bg // 是否展示背景色
        12: optional string text_color // 字体颜色
        13: optional string border_color // 字体颜色
}

struct CategoryData {
    1: required i32          category_id
    2: required string       title
    3: required string       sub_title
    4: required string       icon
    5: required list<string> bg_gradients
    6: required i32          is_selected
    7: required string       section_title     //表单上的分组标题
    8: required string       fold_text  //  折叠文案
    9: required i32          is_fold  //是否折叠
    10:required i32          click_need_expand // 点击后是否展开
    11:optional string       sub_category_title // 子标题文案
}

struct OrderButtonInfo {
    1: required string expect_info_text
    2: optional string sendorder_button_title
    3: optional NavigationBar left_button
}

struct Animation {
    1: optional string icon // 动画图标
    2: optional string title // 动画文案
}

struct NavigationBar {
    1: required string key // 操作台组件唯一标识
    2: optional string title // 展示文案 (仅在某些组件有)
    3: optional string icon // 图标
    4: optional string link // 跳转链接
    5: optional Animation animation // 动画 可不传
    6: optional string highlight_color // 高亮颜色
    7: optional i32 is_highlight // 是否高亮
    8: optional NavigationBarParams params // 点击携带参数
    9: optional map<string,string> popup // 点击弹窗
}

struct NavigationBarParams {
    1: optional string tab_id
    2: optional i32 page_type
}

struct NewFormEstimateResponse {
    1:  required string estimate_trace_id
    2:  required map<string,NewFormEstimateData> estimate_data
    3:  required i32 is_support_multi_selection //是否支持多选，预约单时不支持 0-不支持；1-支持
    4:  required string fee_detail_url //费用明细页地址
    5:  optional PluginPageInfo plugin_page_info //动调、春节服务费等发单拦截页
    6:  required UserPayInfo user_pay_info // 6.0支付方式并集
    7:  required list<NewFormLayout> layout //布局
    8:  optional string toast_tip //预估完成后提示文案
    9:  required map<string,string> p_new_order_params //发单参数 预估级别
    10: optional AdditionalServiceData additional_service_data // 附加需求信息
    11: optional FilterInfo filter_info // 筛选器信息
    12: required map<string,string> travel_forecast // 行程预测
    13: required list<OperationItem> operation_list
    14: optional list<CategoryData> category_info   // 三方表单侧边栏信息
    15: optional OrderButtonInfo order_button_info  // 发单按钮信息
    16: optional i32 selection_style_type // 预约单是否出新样式：0不出，1 出
    17: optional i32 is_callcar_disabled // 是否屏蔽代叫按钮 1 屏蔽 0 不屏蔽
    18: optional map<string,string> multi_route_tips_data // 提示文案全集
    19: optional PriceAxle price_axle // 价格轴
    20: optional i32 show_category // 侧边栏展示策略
    21: optional list<NavigationBar> navigation_bar // 新版操作台
    22: optional map<string,i32> real_params // 端请求mamba透传参数
    23: optional map<string,i32> expect_params // 预期信息
    24: optional map<string,NewFormGroup> group_data //盒子信息
    25: optional MoreToastTipData more_toast_tip  //新版toast
    26: optional i32 rec_form      // 新推荐表单标识
    27: optional list<NewFormLayout>  rec_layout // 推荐layout
    28: optional i32 form_style_exp // 新style标识
    29: optional map<string,i32> side_params // 端请求mamba透传参数
    30: optional TabExtraData tab_extra_data
    31: optional string fee_msg_template //底部操作台 价格展示模版
    32: optional i32 phone_adaptation // 是否大屏适配
}
struct MoreToastTipData {
    1: required string text_color
    2: required string car_icon
    3: required string bg_color
    4: required string border_color
    5: required string text
    6: required string arrow_icon
}

struct ErrButtonResponse { //粤港车独立错误结构
    1: required string           errno
    2: required string           errmsg
    3: required ErrButtonData    data
}

struct ErrButtonData { //粤港车独立错误结构
    1: required ErrButton        button
}

struct ErrButton {
    1: required string text
    2: required string url
}


struct OperationItem {
    1: required string key
    2: required string title
    3: required string link
}

struct NewFormMultiEstimatePriceResponse {
    1: required string           errno
    2: required string           errmsg
    3: optional NewFormEstimateResponse data
}

struct AdditionalServiceData {
    1: required string page_title
    2: required string title
    3: required string tips_url
    4: required list<string> sub_title_list
    5: required list<AdditionalService> service_list

    6: required string guide_text
    7: required i32    guide_times
    8: required i32    version
}

struct FilterInfo {
    1: optional RecExplain rec_explain // 推荐沟通条
    2: optional FilterRecommend filter_recommend // 推荐筛选器
    3: optional FilterNormal filter_normal // 普通筛选器
}
struct RecExplain {
    1: optional string text
    2: optional string link_name
    3: optional string link_url
}

struct FilterRecommend {
    1: optional list<FilterRecommendData> filter_list
}

typedef list<i32> SubCategoryList

struct FilterRecommendData {
    1: optional string filter_id
    2: optional i32    is_selected  // 1:选中
    3: optional string filter_name
    4: optional i32    filter_count
    5: optional string expect_info
    6: optional list<string> fee_desc_list
    7: optional list<FilterData> filter_data
    }

struct FilterRecommendFeeDesc {
    1: optional string content
}

struct FilterData {
    1: optional string group_id //索引到具体车型group
    2: optional list<i32> selected_products  // 盒子有时存在. 具体盒子内哪个车型被勾选
}

struct FilterNormal {
    1: optional string filter_title
    2: optional string filter_sub_title
    3: optional list<FilterNormalData> filter_list
    4: optional i32 refresh_type
}
struct FilterNormalData {
    1: optional string filter_id
    2: optional i32    is_selected  // 1:选中
    3: optional string filter_name
    7: optional list<FilterData> filter_data
}

struct AdditionalService {
    1: required i32 id // 服务id
    2: required string text // 需要携带宠物
    3: required string icon
    4: required i32 select // 是否勾选 0未勾选 1:已勾选
    5: required string detail
}

struct TailorServiceResponse {
    1: required i32 errno
    2: required string  errmsg
    3: optional TailorServiceData data
}

struct TailorServiceData {
    1: required NewPreferInfo prefer_info //偏好选项
    2: required string head //头部文案
    3: required string head_img //整个头图
    4: required string title //主标题
    5: required string sub_title //副标题
    6: required string sub_title_link //副标题跳转链接
    7: required i16 disable //是否可以编辑
    8: optional list<ServiceData> warm_info //暖心服务数据
    9: optional string warm_head //暖心服务文案
    10: optional string upgrade_head //升级服务文案
    11: optional list<ServiceData> upgrade_info //升级服务数据
    12: required string tip //提示
    13: required string tip_link //提示链接
    14: required string estimate_trace_id //暂定
    15: optional string warm_desc //服务不可用提示
    16: required list<CarEstimateData> estimate_car_level_data
    17: required list<DriverEstimateData> estimate_driver_level_data
    18: required ExtraService extra_service
    19: required i32 theme // 定制服务页面
}

struct ExtraService {
     1: required string title
     2: required string default_text
     3: required list<CommonExpression> common_expressions
 }

 struct CommonExpression {
     1: required string text
     2: required bool is_default
 }

struct NewPreferInfo {
    1: required list<PreferOption> prefer_option
    2: required string head
    3: required string head_link
    4: required string remark
    5: required string appellation_ways
    6: required i32 is_remember_history_prefer
    7: required string title
    8: required string greet
    9: required string is_remember_history_prefer_title
}

struct ServiceData {
    1: required i32 id //个性化服务唯一id
    2: required string title //服务名称
    3: required string detail //详情页
    4: required string icon //图标
    5: required string unit // 单位，如："人"或"次"
    6: required i32 max //个性化服务数量限制
    7: required string price_msg //价格文案（无单位）
    8: optional string desc //价格摘要，如"抵{40}元"
    9: optional string tag //desc前的标签
    10: required i32 selected_count // 已选择的人数/次数，默认为0
    11: required double price //价格
    12: required string price_desc //价格文案，如 "{40}元/次"
    13: required i32 status // 状态，0-服务不可勾选，需置灰，1-服务正常
    14: optional string disable_icon // 服务不可勾选状态下icon
    15: optional list<ServiceItem> services // 多价格描述
    16: optional string service_desc // 服务描述
}

struct ServiceItem {
    1:  required i64    id //个性化服务唯一id
    2:  required i64    service_type //个性化服务子类型(eg:10601 携宠-带箱)
    3:  required string service_name //服务名称
    4:  required string service_desc //服务描述
    5:  required string icon // icon
    6:  required i64    selected_count //勾选次数
    7:  required string price_msg //价格文案，如 "{40}元"
    8:  required string price_desc //价格文案，如 "{40}元/次"
    9:  required double price //价格
    10: optional string desc           // text
    11: optional string tag            // url
}

service PreSaleService {
    NewFormMultiEstimatePriceResponse pMultiEstimateV3(1: MultiEstimatePriceRequest req)(
            path = "/gulfstream/pre-sale/v1/core/pMultiEstimatePriceV3"
            httpMethod = "get"
            contentType = "form"
            );

    MultiEstimatePriceResponse pMultiEstimateV2(1: MultiEstimatePriceRequest req)(
            path = "/gulfstream/pre-sale/v1/core/pMultiEstimatePriceV2"
            httpMethod = "get"
            contentType = "form"
            );
    LuxMultiEstimatePriceResponse pLuxMultiEstimate(1: LuxMultiEstimatePriceRequest req)(
            path = "/gulfstream/pre-sale/v1/core/pLuxMultiEstimatePrice"
            httpMethod = "get"
            contentType = "form"
            );
    CapMultiEstimatePriceResponse pCapMultiEstimate(1: CapMultiEstimatePriceRequest req)(
            path = "/gulfstream/pre-sale/v1/core/pCapMultiEstimatePrice"
            httpMethod = "get"
            contentType = "form"
            );
    IntercityEstimatePriceResponse pIntercityEstimate(1: IntercityEstimatePriceRequest req)(
            path = "/gulfstream/pre-sale/v1/core/pIntercityEstimatePrice"
            httpMethod = "get"
            contentType = "form"
            );
    TaxiMultiEstimatePriceResponse pTaxiMultiEstimate(1: TaxiMultiEstimatePriceRequest req)(
            path = "/gulfstream/pre-sale/v1/core/pTaxiMultiEstimatePrice"
            httpMethod = "get"
            contentType = "form"
            );
    GetInvitationInfoResponse pGetInvitationInfo(1: GetInvitationInfoRequest req)(
            path = "/gulfstream/pre-sale/v1/other/pGetInvitationInfo"
            httpMethod = "get"
            contentType = "form"
            );
    GetInvitationInfoV2Response pGetInvitationInfoV2(1: GetInvitationInfoV2Request req)(
                path = "/gulfstream/pre-sale/v1/other/pGetInvitationInfoV2"
                httpMethod = "get"
                contentType = "form"
                );

    LuxMultiEstimatePriceResponse pGetTailorService(1: LuxMultiEstimatePriceRequest req)(
            path = "/gulfstream/pre-sale/v1/other/pGetTailorService"
            httpMethod = "get"
            contentType = "form"
            );
    SubmitTailorServiceResponse pSubmitTailorService(1: SubmitTailorServiceRequest req)(
            path = "/gulfstream/pre-sale/v1/other/pSubmitTailorService"
            httpMethod = "get"
            contentType = "form"
            );
    SubmitCommonExpressionResponse pSubmitCommonExpression(1: SubmitCommonExpressionRequest req) (
            path = "/gulfstream/pre-sale/v1/other/pSubmitCommonExpression"
            httpMethod = "post"
            contentType = "json"
            );
    PageGuideResponse pPageGuide(1: PageGuideRequest req)(
        path = "/gulfstream/pre-sale/v1/other/pPageGuide"
        httpMethod = "get"
        contentType = "form"
            );
    /**
    * 企业接入预估6.0 通过eid列表批量获取费用明细、特殊费用、动调
    */
    B2bMultiEstimateDataResponse pGetMultiEstimateData(1: B2bMultiEstimateDataRequest req)(
        path = "/gulfstream/pre-sale/v1/other/pGetMultiEstimateData"
        httpMethod = "get"
        contentType = "form"
        connectTimeoutMsec="100"
        timeoutMsec="1000"
    );
    SideEstimateResponse pSideEstimate(1: SideEstimateRequest req)(
        path = "/gulfstream/pre-sale/v1/other/pSideEstimate"
        httpMethod = "get"
        contentType = "form"
    );
    TailorServiceResponse pGetTailorServiceV2(1: LuxMultiEstimatePriceRequest req)(
        path = "/gulfstream/pre-sale/v1/other/pGetTailorServiceV2"
        httpMethod = "get"
        contentType = "form"
    );
}(
        version="1.0.2"
        servName="disf!biz-gs-pre_sale"
        servType="http"

        /** 超时配置 */
        timeoutMsec="100"
        connectTimeoutMsec="2000"
)
