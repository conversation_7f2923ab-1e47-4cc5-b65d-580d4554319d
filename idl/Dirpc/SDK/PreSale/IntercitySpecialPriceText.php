<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.IntercitySpecialPriceText</code>
 */
class IntercitySpecialPriceText extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *规则
     *
     * Generated from protobuf field <code>repeated int32 rule_type = 1;</code>
     */
    private $rule_type;
    /**
     *文案
     *
     * Generated from protobuf field <code>string text = 2;</code>
     */
    protected $text = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int[]|\Nuwa\Protobuf\Internal\RepeatedField $rule_type
     *          规则
     *     @type string $text
     *          文案
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *规则
     *
     * Generated from protobuf field <code>repeated int32 rule_type = 1;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getRuleType()
    {
        return $this->rule_type;
    }

    /**
     *规则
     *
     * Generated from protobuf field <code>repeated int32 rule_type = 1;</code>
     * @param int[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setRuleType($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::INT32);
        $this->rule_type = $arr;

        return $this;
    }

    /**
     *文案
     *
     * Generated from protobuf field <code>string text = 2;</code>
     * @return string
     */
    public function getText()
    {
        return $this->text;
    }

    /**
     *文案
     *
     * Generated from protobuf field <code>string text = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setText($var)
    {
        GPBUtil::checkString($var, True);
        $this->text = $var;

        return $this;
    }

}

