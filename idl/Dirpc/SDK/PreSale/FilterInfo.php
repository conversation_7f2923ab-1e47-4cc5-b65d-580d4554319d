<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.FilterInfo</code>
 */
class FilterInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *推荐沟通条
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RecExplain rec_explain = 1;</code>
     */
    protected $rec_explain = null;
    /**
     *推荐筛选器
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.FilterRecommend filter_recommend = 2;</code>
     */
    protected $filter_recommend = null;
    /**
     *普通筛选器
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.FilterNormal filter_normal = 3;</code>
     */
    protected $filter_normal = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\RecExplain $rec_explain
     *          推荐沟通条
     *     @type \Dirpc\SDK\PreSale\FilterRecommend $filter_recommend
     *          推荐筛选器
     *     @type \Dirpc\SDK\PreSale\FilterNormal $filter_normal
     *          普通筛选器
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *推荐沟通条
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RecExplain rec_explain = 1;</code>
     * @return \Dirpc\SDK\PreSale\RecExplain
     */
    public function getRecExplain()
    {
        return isset($this->rec_explain) ? $this->rec_explain : null;
    }

    public function hasRecExplain()
    {
        return isset($this->rec_explain);
    }

    public function clearRecExplain()
    {
        unset($this->rec_explain);
    }

    /**
     *推荐沟通条
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RecExplain rec_explain = 1;</code>
     * @param \Dirpc\SDK\PreSale\RecExplain $var
     * @return $this
     */
    public function setRecExplain($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\RecExplain::class);
        $this->rec_explain = $var;

        return $this;
    }

    /**
     *推荐筛选器
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.FilterRecommend filter_recommend = 2;</code>
     * @return \Dirpc\SDK\PreSale\FilterRecommend
     */
    public function getFilterRecommend()
    {
        return isset($this->filter_recommend) ? $this->filter_recommend : null;
    }

    public function hasFilterRecommend()
    {
        return isset($this->filter_recommend);
    }

    public function clearFilterRecommend()
    {
        unset($this->filter_recommend);
    }

    /**
     *推荐筛选器
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.FilterRecommend filter_recommend = 2;</code>
     * @param \Dirpc\SDK\PreSale\FilterRecommend $var
     * @return $this
     */
    public function setFilterRecommend($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\FilterRecommend::class);
        $this->filter_recommend = $var;

        return $this;
    }

    /**
     *普通筛选器
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.FilterNormal filter_normal = 3;</code>
     * @return \Dirpc\SDK\PreSale\FilterNormal
     */
    public function getFilterNormal()
    {
        return isset($this->filter_normal) ? $this->filter_normal : null;
    }

    public function hasFilterNormal()
    {
        return isset($this->filter_normal);
    }

    public function clearFilterNormal()
    {
        unset($this->filter_normal);
    }

    /**
     *普通筛选器
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.FilterNormal filter_normal = 3;</code>
     * @param \Dirpc\SDK\PreSale\FilterNormal $var
     * @return $this
     */
    public function setFilterNormal($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\FilterNormal::class);
        $this->filter_normal = $var;

        return $this;
    }

}

