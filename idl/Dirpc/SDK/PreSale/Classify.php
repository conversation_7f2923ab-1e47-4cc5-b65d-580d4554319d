<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.Classify</code>
 */
class Classify extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string time_text = 1;</code>
     */
    protected $time_text = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $time_text
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string time_text = 1;</code>
     * @return string
     */
    public function getTimeText()
    {
        return $this->time_text;
    }

    /**
     * Generated from protobuf field <code>string time_text = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTimeText($var)
    {
        GPBUtil::checkString($var, True);
        $this->time_text = $var;

        return $this;
    }

}

