<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.MultiPriceDesc</code>
 */
class MultiPriceDesc extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string fee_msg = 1;</code>
     */
    protected $fee_msg = null;
    /**
     * Generated from protobuf field <code>string estimate_fee = 2;</code>
     */
    protected $estimate_fee = null;
    /**
     * Generated from protobuf field <code>string price_desc = 3;</code>
     */
    protected $price_desc = null;
    /**
     * Generated from protobuf field <code>string left_icon = 4;</code>
     */
    protected $left_icon = null;
    /**
     *是否需要转动
     *
     * Generated from protobuf field <code>int32 show_anim = 5;</code>
     */
    protected $show_anim = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $fee_msg
     *     @type string $estimate_fee
     *     @type string $price_desc
     *     @type string $left_icon
     *     @type int $show_anim
     *          是否需要转动
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string fee_msg = 1;</code>
     * @return string
     */
    public function getFeeMsg()
    {
        return isset($this->fee_msg) ? $this->fee_msg : '';
    }

    public function hasFeeMsg()
    {
        return isset($this->fee_msg);
    }

    public function clearFeeMsg()
    {
        unset($this->fee_msg);
    }

    /**
     * Generated from protobuf field <code>string fee_msg = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string estimate_fee = 2;</code>
     * @return string
     */
    public function getEstimateFee()
    {
        return isset($this->estimate_fee) ? $this->estimate_fee : '';
    }

    public function hasEstimateFee()
    {
        return isset($this->estimate_fee);
    }

    public function clearEstimateFee()
    {
        unset($this->estimate_fee);
    }

    /**
     * Generated from protobuf field <code>string estimate_fee = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateFee($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_fee = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string price_desc = 3;</code>
     * @return string
     */
    public function getPriceDesc()
    {
        return isset($this->price_desc) ? $this->price_desc : '';
    }

    public function hasPriceDesc()
    {
        return isset($this->price_desc);
    }

    public function clearPriceDesc()
    {
        unset($this->price_desc);
    }

    /**
     * Generated from protobuf field <code>string price_desc = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setPriceDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->price_desc = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string left_icon = 4;</code>
     * @return string
     */
    public function getLeftIcon()
    {
        return isset($this->left_icon) ? $this->left_icon : '';
    }

    public function hasLeftIcon()
    {
        return isset($this->left_icon);
    }

    public function clearLeftIcon()
    {
        unset($this->left_icon);
    }

    /**
     * Generated from protobuf field <code>string left_icon = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setLeftIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->left_icon = $var;

        return $this;
    }

    /**
     *是否需要转动
     *
     * Generated from protobuf field <code>int32 show_anim = 5;</code>
     * @return int
     */
    public function getShowAnim()
    {
        return isset($this->show_anim) ? $this->show_anim : 0;
    }

    public function hasShowAnim()
    {
        return isset($this->show_anim);
    }

    public function clearShowAnim()
    {
        unset($this->show_anim);
    }

    /**
     *是否需要转动
     *
     * Generated from protobuf field <code>int32 show_anim = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setShowAnim($var)
    {
        GPBUtil::checkInt32($var);
        $this->show_anim = $var;

        return $this;
    }

}

