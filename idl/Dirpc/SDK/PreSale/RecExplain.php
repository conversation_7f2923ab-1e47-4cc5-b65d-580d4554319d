<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.RecExplain</code>
 */
class RecExplain extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string text = 1;</code>
     */
    protected $text = null;
    /**
     * Generated from protobuf field <code>string link_name = 2;</code>
     */
    protected $link_name = null;
    /**
     * Generated from protobuf field <code>string link_url = 3;</code>
     */
    protected $link_url = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $text
     *     @type string $link_name
     *     @type string $link_url
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string text = 1;</code>
     * @return string
     */
    public function getText()
    {
        return isset($this->text) ? $this->text : '';
    }

    public function hasText()
    {
        return isset($this->text);
    }

    public function clearText()
    {
        unset($this->text);
    }

    /**
     * Generated from protobuf field <code>string text = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setText($var)
    {
        GPBUtil::checkString($var, True);
        $this->text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string link_name = 2;</code>
     * @return string
     */
    public function getLinkName()
    {
        return isset($this->link_name) ? $this->link_name : '';
    }

    public function hasLinkName()
    {
        return isset($this->link_name);
    }

    public function clearLinkName()
    {
        unset($this->link_name);
    }

    /**
     * Generated from protobuf field <code>string link_name = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setLinkName($var)
    {
        GPBUtil::checkString($var, True);
        $this->link_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string link_url = 3;</code>
     * @return string
     */
    public function getLinkUrl()
    {
        return isset($this->link_url) ? $this->link_url : '';
    }

    public function hasLinkUrl()
    {
        return isset($this->link_url);
    }

    public function clearLinkUrl()
    {
        unset($this->link_url);
    }

    /**
     * Generated from protobuf field <code>string link_url = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setLinkUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->link_url = $var;

        return $this;
    }

}

