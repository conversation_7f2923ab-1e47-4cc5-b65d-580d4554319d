<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.EjectButton</code>
 */
class EjectButton extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string text = 1;</code>
     */
    protected $text = '';
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectButtonParams params = 2;</code>
     */
    protected $params = null;
    /**
     * Generated from protobuf field <code>string bubble_text = 3;</code>
     */
    protected $bubble_text = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $text
     *     @type \Dirpc\SDK\PreSale\EjectButtonParams $params
     *     @type string $bubble_text
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string text = 1;</code>
     * @return string
     */
    public function getText()
    {
        return $this->text;
    }

    /**
     * Generated from protobuf field <code>string text = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setText($var)
    {
        GPBUtil::checkString($var, True);
        $this->text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectButtonParams params = 2;</code>
     * @return \Dirpc\SDK\PreSale\EjectButtonParams
     */
    public function getParams()
    {
        return isset($this->params) ? $this->params : null;
    }

    public function hasParams()
    {
        return isset($this->params);
    }

    public function clearParams()
    {
        unset($this->params);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectButtonParams params = 2;</code>
     * @param \Dirpc\SDK\PreSale\EjectButtonParams $var
     * @return $this
     */
    public function setParams($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\EjectButtonParams::class);
        $this->params = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string bubble_text = 3;</code>
     * @return string
     */
    public function getBubbleText()
    {
        return isset($this->bubble_text) ? $this->bubble_text : '';
    }

    public function hasBubbleText()
    {
        return isset($this->bubble_text);
    }

    public function clearBubbleText()
    {
        unset($this->bubble_text);
    }

    /**
     * Generated from protobuf field <code>string bubble_text = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setBubbleText($var)
    {
        GPBUtil::checkString($var, True);
        $this->bubble_text = $var;

        return $this;
    }

}

