<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.ServiceData</code>
 */
class ServiceData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *个性化服务唯一id
     *
     * Generated from protobuf field <code>int32 id = 1;</code>
     */
    protected $id = 0;
    /**
     *服务名称
     *
     * Generated from protobuf field <code>string title = 2;</code>
     */
    protected $title = '';
    /**
     *详情页
     *
     * Generated from protobuf field <code>string detail = 3;</code>
     */
    protected $detail = '';
    /**
     *图标
     *
     * Generated from protobuf field <code>string icon = 4;</code>
     */
    protected $icon = '';
    /**
     *单位，如："人"或"次"
     *
     * Generated from protobuf field <code>string unit = 5;</code>
     */
    protected $unit = '';
    /**
     *个性化服务数量限制
     *
     * Generated from protobuf field <code>int32 max = 6;</code>
     */
    protected $max = 0;
    /**
     *价格文案（无单位）
     *
     * Generated from protobuf field <code>string price_msg = 7;</code>
     */
    protected $price_msg = '';
    /**
     *价格摘要，如"抵{40}元"
     *
     * Generated from protobuf field <code>string desc = 8;</code>
     */
    protected $desc = null;
    /**
     *desc前的标签
     *
     * Generated from protobuf field <code>string tag = 9;</code>
     */
    protected $tag = null;
    /**
     *已选择的人数/次数，默认为0
     *
     * Generated from protobuf field <code>int32 selected_count = 10;</code>
     */
    protected $selected_count = 0;
    /**
     *价格
     *
     * Generated from protobuf field <code>double price = 11;</code>
     */
    protected $price = 0.0;
    /**
     *价格文案，如 "{40}元/次"
     *
     * Generated from protobuf field <code>string price_desc = 12;</code>
     */
    protected $price_desc = '';
    /**
     *状态，0-服务不可勾选，需置灰，1-服务正常
     *
     * Generated from protobuf field <code>int32 status = 13;</code>
     */
    protected $status = 0;
    /**
     *服务不可勾选状态下icon
     *
     * Generated from protobuf field <code>string disable_icon = 14;</code>
     */
    protected $disable_icon = null;
    /**
     *多价格描述
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ServiceItem services = 15;</code>
     */
    private $services;
    /**
     *服务描述
     *
     * Generated from protobuf field <code>string service_desc = 16;</code>
     */
    protected $service_desc = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $id
     *          个性化服务唯一id
     *     @type string $title
     *          服务名称
     *     @type string $detail
     *          详情页
     *     @type string $icon
     *          图标
     *     @type string $unit
     *          单位，如："人"或"次"
     *     @type int $max
     *          个性化服务数量限制
     *     @type string $price_msg
     *          价格文案（无单位）
     *     @type string $desc
     *          价格摘要，如"抵{40}元"
     *     @type string $tag
     *          desc前的标签
     *     @type int $selected_count
     *          已选择的人数/次数，默认为0
     *     @type float $price
     *          价格
     *     @type string $price_desc
     *          价格文案，如 "{40}元/次"
     *     @type int $status
     *          状态，0-服务不可勾选，需置灰，1-服务正常
     *     @type string $disable_icon
     *          服务不可勾选状态下icon
     *     @type \Dirpc\SDK\PreSale\ServiceItem[]|\Nuwa\Protobuf\Internal\RepeatedField $services
     *          多价格描述
     *     @type string $service_desc
     *          服务描述
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *个性化服务唯一id
     *
     * Generated from protobuf field <code>int32 id = 1;</code>
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     *个性化服务唯一id
     *
     * Generated from protobuf field <code>int32 id = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setId($var)
    {
        GPBUtil::checkInt32($var);
        $this->id = $var;

        return $this;
    }

    /**
     *服务名称
     *
     * Generated from protobuf field <code>string title = 2;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     *服务名称
     *
     * Generated from protobuf field <code>string title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     *详情页
     *
     * Generated from protobuf field <code>string detail = 3;</code>
     * @return string
     */
    public function getDetail()
    {
        return $this->detail;
    }

    /**
     *详情页
     *
     * Generated from protobuf field <code>string detail = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setDetail($var)
    {
        GPBUtil::checkString($var, True);
        $this->detail = $var;

        return $this;
    }

    /**
     *图标
     *
     * Generated from protobuf field <code>string icon = 4;</code>
     * @return string
     */
    public function getIcon()
    {
        return $this->icon;
    }

    /**
     *图标
     *
     * Generated from protobuf field <code>string icon = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->icon = $var;

        return $this;
    }

    /**
     *单位，如："人"或"次"
     *
     * Generated from protobuf field <code>string unit = 5;</code>
     * @return string
     */
    public function getUnit()
    {
        return $this->unit;
    }

    /**
     *单位，如："人"或"次"
     *
     * Generated from protobuf field <code>string unit = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setUnit($var)
    {
        GPBUtil::checkString($var, True);
        $this->unit = $var;

        return $this;
    }

    /**
     *个性化服务数量限制
     *
     * Generated from protobuf field <code>int32 max = 6;</code>
     * @return int
     */
    public function getMax()
    {
        return $this->max;
    }

    /**
     *个性化服务数量限制
     *
     * Generated from protobuf field <code>int32 max = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setMax($var)
    {
        GPBUtil::checkInt32($var);
        $this->max = $var;

        return $this;
    }

    /**
     *价格文案（无单位）
     *
     * Generated from protobuf field <code>string price_msg = 7;</code>
     * @return string
     */
    public function getPriceMsg()
    {
        return $this->price_msg;
    }

    /**
     *价格文案（无单位）
     *
     * Generated from protobuf field <code>string price_msg = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setPriceMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->price_msg = $var;

        return $this;
    }

    /**
     *价格摘要，如"抵{40}元"
     *
     * Generated from protobuf field <code>string desc = 8;</code>
     * @return string
     */
    public function getDesc()
    {
        return isset($this->desc) ? $this->desc : '';
    }

    public function hasDesc()
    {
        return isset($this->desc);
    }

    public function clearDesc()
    {
        unset($this->desc);
    }

    /**
     *价格摘要，如"抵{40}元"
     *
     * Generated from protobuf field <code>string desc = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->desc = $var;

        return $this;
    }

    /**
     *desc前的标签
     *
     * Generated from protobuf field <code>string tag = 9;</code>
     * @return string
     */
    public function getTag()
    {
        return isset($this->tag) ? $this->tag : '';
    }

    public function hasTag()
    {
        return isset($this->tag);
    }

    public function clearTag()
    {
        unset($this->tag);
    }

    /**
     *desc前的标签
     *
     * Generated from protobuf field <code>string tag = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setTag($var)
    {
        GPBUtil::checkString($var, True);
        $this->tag = $var;

        return $this;
    }

    /**
     *已选择的人数/次数，默认为0
     *
     * Generated from protobuf field <code>int32 selected_count = 10;</code>
     * @return int
     */
    public function getSelectedCount()
    {
        return $this->selected_count;
    }

    /**
     *已选择的人数/次数，默认为0
     *
     * Generated from protobuf field <code>int32 selected_count = 10;</code>
     * @param int $var
     * @return $this
     */
    public function setSelectedCount($var)
    {
        GPBUtil::checkInt32($var);
        $this->selected_count = $var;

        return $this;
    }

    /**
     *价格
     *
     * Generated from protobuf field <code>double price = 11;</code>
     * @return float
     */
    public function getPrice()
    {
        return $this->price;
    }

    /**
     *价格
     *
     * Generated from protobuf field <code>double price = 11;</code>
     * @param float $var
     * @return $this
     */
    public function setPrice($var)
    {
        GPBUtil::checkDouble($var);
        $this->price = $var;

        return $this;
    }

    /**
     *价格文案，如 "{40}元/次"
     *
     * Generated from protobuf field <code>string price_desc = 12;</code>
     * @return string
     */
    public function getPriceDesc()
    {
        return $this->price_desc;
    }

    /**
     *价格文案，如 "{40}元/次"
     *
     * Generated from protobuf field <code>string price_desc = 12;</code>
     * @param string $var
     * @return $this
     */
    public function setPriceDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->price_desc = $var;

        return $this;
    }

    /**
     *状态，0-服务不可勾选，需置灰，1-服务正常
     *
     * Generated from protobuf field <code>int32 status = 13;</code>
     * @return int
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     *状态，0-服务不可勾选，需置灰，1-服务正常
     *
     * Generated from protobuf field <code>int32 status = 13;</code>
     * @param int $var
     * @return $this
     */
    public function setStatus($var)
    {
        GPBUtil::checkInt32($var);
        $this->status = $var;

        return $this;
    }

    /**
     *服务不可勾选状态下icon
     *
     * Generated from protobuf field <code>string disable_icon = 14;</code>
     * @return string
     */
    public function getDisableIcon()
    {
        return isset($this->disable_icon) ? $this->disable_icon : '';
    }

    public function hasDisableIcon()
    {
        return isset($this->disable_icon);
    }

    public function clearDisableIcon()
    {
        unset($this->disable_icon);
    }

    /**
     *服务不可勾选状态下icon
     *
     * Generated from protobuf field <code>string disable_icon = 14;</code>
     * @param string $var
     * @return $this
     */
    public function setDisableIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->disable_icon = $var;

        return $this;
    }

    /**
     *多价格描述
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ServiceItem services = 15;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getServices()
    {
        return $this->services;
    }

    /**
     *多价格描述
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ServiceItem services = 15;</code>
     * @param \Dirpc\SDK\PreSale\ServiceItem[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setServices($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\ServiceItem::class);
        $this->services = $arr;

        return $this;
    }

    /**
     *服务描述
     *
     * Generated from protobuf field <code>string service_desc = 16;</code>
     * @return string
     */
    public function getServiceDesc()
    {
        return isset($this->service_desc) ? $this->service_desc : '';
    }

    public function hasServiceDesc()
    {
        return isset($this->service_desc);
    }

    public function clearServiceDesc()
    {
        unset($this->service_desc);
    }

    /**
     *服务描述
     *
     * Generated from protobuf field <code>string service_desc = 16;</code>
     * @param string $var
     * @return $this
     */
    public function setServiceDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->service_desc = $var;

        return $this;
    }

}

