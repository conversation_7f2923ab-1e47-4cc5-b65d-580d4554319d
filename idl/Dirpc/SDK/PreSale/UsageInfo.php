<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.UsageInfo</code>
 */
class UsageInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *"right_text": "建议仅在急需用车时使用", // 右侧
     *
     * Generated from protobuf field <code>string alert = 1;</code>
     */
    protected $alert = '';
    /**
     * Generated from protobuf field <code>string left_text = 2;</code>
     */
    protected $left_text = '';
    /**
     * Generated from protobuf field <code>string right_text = 3;</code>
     */
    protected $right_text = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $alert
     *          "right_text": "建议仅在急需用车时使用", // 右侧
     *     @type string $left_text
     *     @type string $right_text
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *"right_text": "建议仅在急需用车时使用", // 右侧
     *
     * Generated from protobuf field <code>string alert = 1;</code>
     * @return string
     */
    public function getAlert()
    {
        return $this->alert;
    }

    /**
     *"right_text": "建议仅在急需用车时使用", // 右侧
     *
     * Generated from protobuf field <code>string alert = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setAlert($var)
    {
        GPBUtil::checkString($var, True);
        $this->alert = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string left_text = 2;</code>
     * @return string
     */
    public function getLeftText()
    {
        return $this->left_text;
    }

    /**
     * Generated from protobuf field <code>string left_text = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setLeftText($var)
    {
        GPBUtil::checkString($var, True);
        $this->left_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string right_text = 3;</code>
     * @return string
     */
    public function getRightText()
    {
        return $this->right_text;
    }

    /**
     * Generated from protobuf field <code>string right_text = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setRightText($var)
    {
        GPBUtil::checkString($var, True);
        $this->right_text = $var;

        return $this;
    }

}

