<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideEstimateRequest</code>
 */
class SideEstimateRequest extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string token = 1;</code>
     */
    protected $token = '';
    /**
     * Generated from protobuf field <code>string app_version = 2;</code>
     */
    protected $app_version = '';
    /**
     * Generated from protobuf field <code>int32 access_key_id = 3;</code>
     */
    protected $access_key_id = 0;
    /**
     * Generated from protobuf field <code>int32 channel = 4;</code>
     */
    protected $channel = 0;
    /**
     * Generated from protobuf field <code>int32 client_type = 5;</code>
     */
    protected $client_type = 0;
    /**
     * Generated from protobuf field <code>string lang = 6;</code>
     */
    protected $lang = '';
    /**
     * Generated from protobuf field <code>string utc_offset = 7;</code>
     */
    protected $utc_offset = '';
    /**
     * Generated from protobuf field <code>string maptype = 9;</code>
     */
    protected $maptype = '';
    /**
     * Generated from protobuf field <code>string ddfp = 10;</code>
     */
    protected $ddfp = '';
    /**
     * Generated from protobuf field <code>string terminal_id = 12;</code>
     */
    protected $terminal_id = '';
    /**
     * Generated from protobuf field <code>int32 origin_id = 13;</code>
     */
    protected $origin_id = 0;
    /**
     * Generated from protobuf field <code>int32 platform_type = 14;</code>
     */
    protected $platform_type = 0;
    /**
     * Generated from protobuf field <code>string model = 15;</code>
     */
    protected $model = '';
    /**
     *冒泡tabID http://ab.intra.xiaojukeji.com/conf/1/11322/dict/1296
     *
     * Generated from protobuf field <code>string tab_id = 16;</code>
     */
    protected $tab_id = '';
    /**
     *冒泡tabID http://ab.intra.xiaojukeji.com/conf/1/11322/dict/1296
     *
     * Generated from protobuf field <code>int32 page_type = 17;</code>
     */
    protected $page_type = 0;
    /**
     * Generated from protobuf field <code>int32 user_type = 18;</code>
     */
    protected $user_type = 0;
    /**
     *前端当前的tab信息，包括tab_id 和 是否默认选中  (6.5预估表单+carpool预估表单 + 8.0表单tab)
     *
     * Generated from protobuf field <code>string tab_list = 19;</code>
     */
    protected $tab_list = '';
    /**
     *预估表单样式，0:老样式，1:单行  2双排新表单 3多tab新表单 4:一站式出行
     *
     * Generated from protobuf field <code>int32 estimate_style_type = 20;</code>
     */
    protected $estimate_style_type = null;
    /**
     *是否展示膨胀弹窗 0:不展示 1展示
     *
     * Generated from protobuf field <code>int32 expand_popup_status = 21;</code>
     */
    protected $expand_popup_status = null;
    /**
     *是否已自动刷新预估  0:否 1是
     *
     * Generated from protobuf field <code>int32 already_auto_refresh_estimate = 22;</code>
     */
    protected $already_auto_refresh_estimate = null;
    /**
     * Generated from protobuf field <code>int32 area = 30;</code>
     */
    protected $area = 0;
    /**
     * Generated from protobuf field <code>double from_lat = 33;</code>
     */
    protected $from_lat = 0.0;
    /**
     * Generated from protobuf field <code>double from_lng = 34;</code>
     */
    protected $from_lng = 0.0;
    /**
     *当前定位点
     *
     * Generated from protobuf field <code>double lat = 35;</code>
     */
    protected $lat = 0.0;
    /**
     *当前定位点
     *
     * Generated from protobuf field <code>double lng = 36;</code>
     */
    protected $lng = 0.0;
    /**
     *当前定位点
     *
     * Generated from protobuf field <code>double to_lat = 38;</code>
     */
    protected $to_lat = 0.0;
    /**
     * Generated from protobuf field <code>double to_lng = 39;</code>
     */
    protected $to_lng = 0.0;
    /**
     * Generated from protobuf field <code>string from_name = 40;</code>
     */
    protected $from_name = '';
    /**
     * Generated from protobuf field <code>string to_name = 41;</code>
     */
    protected $to_name = '';
    /**
     * Generated from protobuf field <code>string from_poi_id = 42;</code>
     */
    protected $from_poi_id = '';
    /**
     * Generated from protobuf field <code>string from_poi_type = 43;</code>
     */
    protected $from_poi_type = '';
    /**
     * Generated from protobuf field <code>string to_poi_id = 44;</code>
     */
    protected $to_poi_id = '';
    /**
     * Generated from protobuf field <code>string to_poi_type = 45;</code>
     */
    protected $to_poi_type = '';
    /**
     * Generated from protobuf field <code>string from_address = 46;</code>
     */
    protected $from_address = '';
    /**
     * Generated from protobuf field <code>string to_address = 47;</code>
     */
    protected $to_address = '';
    /**
     *用户当前路线id
     *
     * Generated from protobuf field <code>string route_id = 48;</code>
     */
    protected $route_id = '';
    /**
     *用户选择的支付方式
     *
     * Generated from protobuf field <code>int32 payments_type = 54;</code>
     */
    protected $payments_type = 0;
    /**
     *时间戳
     *
     * Generated from protobuf field <code>string departure_time = 55;</code>
     */
    protected $departure_time = '';
    /**
     *pGetDynamicConfig下发的按钮样式
     *
     * Generated from protobuf field <code>int32 button_style = 56;</code>
     */
    protected $button_style = 0;
    /**
     *推荐主题样式
     *
     * Generated from protobuf field <code>int32 rec_theme_type = 79;</code>
     */
    protected $rec_theme_type = 0;
    /**
     *{"estimate_id":"eid新快车","is_select_reccombo":0}
     *
     * Generated from protobuf field <code>string multi_product_category = 80;</code>
     */
    protected $multi_product_category = '';
    /**
     * Generated from protobuf field <code>string estimate_trace_id = 81;</code>
     */
    protected $estimate_trace_id = '';
    /**
     *途经点信息
     *
     * Generated from protobuf field <code>string stopover_points = 82;</code>
     */
    protected $stopover_points = '';
    /**
     *订单类型：0 是实时 1是预约
     *
     * Generated from protobuf field <code>int32 order_type = 83;</code>
     */
    protected $order_type = 0;
    /**
     *CallCarType 叫车类型：0为普通叫车；1为代叫车
     *
     * Generated from protobuf field <code>int32 call_car_type = 84;</code>
     */
    protected $call_car_type = 0;
    /**
     * Generated from protobuf field <code>string xpsid = 85;</code>
     */
    protected $xpsid = '';
    /**
     * Generated from protobuf field <code>string xpsid_root = 86;</code>
     */
    protected $xpsid_root = '';
    /**
     * Generated from protobuf field <code>string act_id = 87;</code>
     */
    protected $act_id = null;
    /**
     *冒泡来源
     *
     * Generated from protobuf field <code>int32 from_type = 88;</code>
     */
    protected $from_type = null;
    /**
     *是否需要展示事件 （通勤完单优惠大促：CommuteCouponFinishOrder）
     *
     * Generated from protobuf field <code>string is_need_hide_event = 89;</code>
     */
    protected $is_need_hide_event = null;
    /**
     *是否需要展示事件 （通勤完单优惠大促：CommuteCouponFinishOrder）
     *
     * Generated from protobuf field <code>int32 font_scale_type = 90;</code>
     */
    protected $font_scale_type = null;
    /**
     * Generated from protobuf field <code>int32 form_style_exp = 91;</code>
     */
    protected $form_style_exp = null;
    /**
     * Generated from protobuf field <code>string one_stop_version = 92;</code>
     */
    protected $one_stop_version = null;
    /**
     * Generated from protobuf field <code>int32 best_shift_distance = 93;</code>
     */
    protected $best_shift_distance = null;
    /**
     * Generated from protobuf field <code>int32 best_shift_exp_group = 94;</code>
     */
    protected $best_shift_exp_group = null;
    /**
     * Generated from protobuf field <code>int32 rec_form = 95;</code>
     */
    protected $rec_form = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $token
     *     @type string $app_version
     *     @type int $access_key_id
     *     @type int $channel
     *     @type int $client_type
     *     @type string $lang
     *     @type string $utc_offset
     *     @type string $maptype
     *     @type string $ddfp
     *     @type string $terminal_id
     *     @type int $origin_id
     *     @type int $platform_type
     *     @type string $model
     *     @type string $tab_id
     *          冒泡tabID http://ab.intra.xiaojukeji.com/conf/1/11322/dict/1296
     *     @type int $page_type
     *          冒泡tabID http://ab.intra.xiaojukeji.com/conf/1/11322/dict/1296
     *     @type int $user_type
     *     @type string $tab_list
     *          前端当前的tab信息，包括tab_id 和 是否默认选中  (6.5预估表单+carpool预估表单 + 8.0表单tab)
     *     @type int $estimate_style_type
     *          预估表单样式，0:老样式，1:单行  2双排新表单 3多tab新表单 4:一站式出行
     *     @type int $expand_popup_status
     *          是否展示膨胀弹窗 0:不展示 1展示
     *     @type int $already_auto_refresh_estimate
     *          是否已自动刷新预估  0:否 1是
     *     @type int $area
     *     @type float $from_lat
     *     @type float $from_lng
     *     @type float $lat
     *          当前定位点
     *     @type float $lng
     *          当前定位点
     *     @type float $to_lat
     *          当前定位点
     *     @type float $to_lng
     *     @type string $from_name
     *     @type string $to_name
     *     @type string $from_poi_id
     *     @type string $from_poi_type
     *     @type string $to_poi_id
     *     @type string $to_poi_type
     *     @type string $from_address
     *     @type string $to_address
     *     @type string $route_id
     *          用户当前路线id
     *     @type int $payments_type
     *          用户选择的支付方式
     *     @type string $departure_time
     *          时间戳
     *     @type int $button_style
     *          pGetDynamicConfig下发的按钮样式
     *     @type int $rec_theme_type
     *          推荐主题样式
     *     @type string $multi_product_category
     *          {"estimate_id":"eid新快车","is_select_reccombo":0}
     *     @type string $estimate_trace_id
     *     @type string $stopover_points
     *          途经点信息
     *     @type int $order_type
     *          订单类型：0 是实时 1是预约
     *     @type int $call_car_type
     *          CallCarType 叫车类型：0为普通叫车；1为代叫车
     *     @type string $xpsid
     *     @type string $xpsid_root
     *     @type string $act_id
     *     @type int $from_type
     *          冒泡来源
     *     @type string $is_need_hide_event
     *          是否需要展示事件 （通勤完单优惠大促：CommuteCouponFinishOrder）
     *     @type int $font_scale_type
     *          是否需要展示事件 （通勤完单优惠大促：CommuteCouponFinishOrder）
     *     @type int $form_style_exp
     *     @type string $one_stop_version
     *     @type int $best_shift_distance
     *     @type int $best_shift_exp_group
     *     @type int $rec_form
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string token = 1;</code>
     * @return string
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     * Generated from protobuf field <code>string token = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->token = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string app_version = 2;</code>
     * @return string
     */
    public function getAppVersion()
    {
        return $this->app_version;
    }

    /**
     * Generated from protobuf field <code>string app_version = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setAppVersion($var)
    {
        GPBUtil::checkString($var, True);
        $this->app_version = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 access_key_id = 3;</code>
     * @return int
     */
    public function getAccessKeyId()
    {
        return $this->access_key_id;
    }

    /**
     * Generated from protobuf field <code>int32 access_key_id = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setAccessKeyId($var)
    {
        GPBUtil::checkInt32($var);
        $this->access_key_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 channel = 4;</code>
     * @return int
     */
    public function getChannel()
    {
        return $this->channel;
    }

    /**
     * Generated from protobuf field <code>int32 channel = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setChannel($var)
    {
        GPBUtil::checkInt32($var);
        $this->channel = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 client_type = 5;</code>
     * @return int
     */
    public function getClientType()
    {
        return $this->client_type;
    }

    /**
     * Generated from protobuf field <code>int32 client_type = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setClientType($var)
    {
        GPBUtil::checkInt32($var);
        $this->client_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string lang = 6;</code>
     * @return string
     */
    public function getLang()
    {
        return $this->lang;
    }

    /**
     * Generated from protobuf field <code>string lang = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setLang($var)
    {
        GPBUtil::checkString($var, True);
        $this->lang = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string utc_offset = 7;</code>
     * @return string
     */
    public function getUtcOffset()
    {
        return $this->utc_offset;
    }

    /**
     * Generated from protobuf field <code>string utc_offset = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setUtcOffset($var)
    {
        GPBUtil::checkString($var, True);
        $this->utc_offset = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string maptype = 9;</code>
     * @return string
     */
    public function getMaptype()
    {
        return $this->maptype;
    }

    /**
     * Generated from protobuf field <code>string maptype = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setMaptype($var)
    {
        GPBUtil::checkString($var, True);
        $this->maptype = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string ddfp = 10;</code>
     * @return string
     */
    public function getDdfp()
    {
        return $this->ddfp;
    }

    /**
     * Generated from protobuf field <code>string ddfp = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setDdfp($var)
    {
        GPBUtil::checkString($var, True);
        $this->ddfp = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string terminal_id = 12;</code>
     * @return string
     */
    public function getTerminalId()
    {
        return $this->terminal_id;
    }

    /**
     * Generated from protobuf field <code>string terminal_id = 12;</code>
     * @param string $var
     * @return $this
     */
    public function setTerminalId($var)
    {
        GPBUtil::checkString($var, True);
        $this->terminal_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 origin_id = 13;</code>
     * @return int
     */
    public function getOriginId()
    {
        return $this->origin_id;
    }

    /**
     * Generated from protobuf field <code>int32 origin_id = 13;</code>
     * @param int $var
     * @return $this
     */
    public function setOriginId($var)
    {
        GPBUtil::checkInt32($var);
        $this->origin_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 platform_type = 14;</code>
     * @return int
     */
    public function getPlatformType()
    {
        return $this->platform_type;
    }

    /**
     * Generated from protobuf field <code>int32 platform_type = 14;</code>
     * @param int $var
     * @return $this
     */
    public function setPlatformType($var)
    {
        GPBUtil::checkInt32($var);
        $this->platform_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string model = 15;</code>
     * @return string
     */
    public function getModel()
    {
        return $this->model;
    }

    /**
     * Generated from protobuf field <code>string model = 15;</code>
     * @param string $var
     * @return $this
     */
    public function setModel($var)
    {
        GPBUtil::checkString($var, True);
        $this->model = $var;

        return $this;
    }

    /**
     *冒泡tabID http://ab.intra.xiaojukeji.com/conf/1/11322/dict/1296
     *
     * Generated from protobuf field <code>string tab_id = 16;</code>
     * @return string
     */
    public function getTabId()
    {
        return $this->tab_id;
    }

    /**
     *冒泡tabID http://ab.intra.xiaojukeji.com/conf/1/11322/dict/1296
     *
     * Generated from protobuf field <code>string tab_id = 16;</code>
     * @param string $var
     * @return $this
     */
    public function setTabId($var)
    {
        GPBUtil::checkString($var, True);
        $this->tab_id = $var;

        return $this;
    }

    /**
     *冒泡tabID http://ab.intra.xiaojukeji.com/conf/1/11322/dict/1296
     *
     * Generated from protobuf field <code>int32 page_type = 17;</code>
     * @return int
     */
    public function getPageType()
    {
        return $this->page_type;
    }

    /**
     *冒泡tabID http://ab.intra.xiaojukeji.com/conf/1/11322/dict/1296
     *
     * Generated from protobuf field <code>int32 page_type = 17;</code>
     * @param int $var
     * @return $this
     */
    public function setPageType($var)
    {
        GPBUtil::checkInt32($var);
        $this->page_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 user_type = 18;</code>
     * @return int
     */
    public function getUserType()
    {
        return $this->user_type;
    }

    /**
     * Generated from protobuf field <code>int32 user_type = 18;</code>
     * @param int $var
     * @return $this
     */
    public function setUserType($var)
    {
        GPBUtil::checkInt32($var);
        $this->user_type = $var;

        return $this;
    }

    /**
     *前端当前的tab信息，包括tab_id 和 是否默认选中  (6.5预估表单+carpool预估表单 + 8.0表单tab)
     *
     * Generated from protobuf field <code>string tab_list = 19;</code>
     * @return string
     */
    public function getTabList()
    {
        return $this->tab_list;
    }

    /**
     *前端当前的tab信息，包括tab_id 和 是否默认选中  (6.5预估表单+carpool预估表单 + 8.0表单tab)
     *
     * Generated from protobuf field <code>string tab_list = 19;</code>
     * @param string $var
     * @return $this
     */
    public function setTabList($var)
    {
        GPBUtil::checkString($var, True);
        $this->tab_list = $var;

        return $this;
    }

    /**
     *预估表单样式，0:老样式，1:单行  2双排新表单 3多tab新表单 4:一站式出行
     *
     * Generated from protobuf field <code>int32 estimate_style_type = 20;</code>
     * @return int
     */
    public function getEstimateStyleType()
    {
        return isset($this->estimate_style_type) ? $this->estimate_style_type : 0;
    }

    public function hasEstimateStyleType()
    {
        return isset($this->estimate_style_type);
    }

    public function clearEstimateStyleType()
    {
        unset($this->estimate_style_type);
    }

    /**
     *预估表单样式，0:老样式，1:单行  2双排新表单 3多tab新表单 4:一站式出行
     *
     * Generated from protobuf field <code>int32 estimate_style_type = 20;</code>
     * @param int $var
     * @return $this
     */
    public function setEstimateStyleType($var)
    {
        GPBUtil::checkInt32($var);
        $this->estimate_style_type = $var;

        return $this;
    }

    /**
     *是否展示膨胀弹窗 0:不展示 1展示
     *
     * Generated from protobuf field <code>int32 expand_popup_status = 21;</code>
     * @return int
     */
    public function getExpandPopupStatus()
    {
        return isset($this->expand_popup_status) ? $this->expand_popup_status : 0;
    }

    public function hasExpandPopupStatus()
    {
        return isset($this->expand_popup_status);
    }

    public function clearExpandPopupStatus()
    {
        unset($this->expand_popup_status);
    }

    /**
     *是否展示膨胀弹窗 0:不展示 1展示
     *
     * Generated from protobuf field <code>int32 expand_popup_status = 21;</code>
     * @param int $var
     * @return $this
     */
    public function setExpandPopupStatus($var)
    {
        GPBUtil::checkInt32($var);
        $this->expand_popup_status = $var;

        return $this;
    }

    /**
     *是否已自动刷新预估  0:否 1是
     *
     * Generated from protobuf field <code>int32 already_auto_refresh_estimate = 22;</code>
     * @return int
     */
    public function getAlreadyAutoRefreshEstimate()
    {
        return isset($this->already_auto_refresh_estimate) ? $this->already_auto_refresh_estimate : 0;
    }

    public function hasAlreadyAutoRefreshEstimate()
    {
        return isset($this->already_auto_refresh_estimate);
    }

    public function clearAlreadyAutoRefreshEstimate()
    {
        unset($this->already_auto_refresh_estimate);
    }

    /**
     *是否已自动刷新预估  0:否 1是
     *
     * Generated from protobuf field <code>int32 already_auto_refresh_estimate = 22;</code>
     * @param int $var
     * @return $this
     */
    public function setAlreadyAutoRefreshEstimate($var)
    {
        GPBUtil::checkInt32($var);
        $this->already_auto_refresh_estimate = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 area = 30;</code>
     * @return int
     */
    public function getArea()
    {
        return $this->area;
    }

    /**
     * Generated from protobuf field <code>int32 area = 30;</code>
     * @param int $var
     * @return $this
     */
    public function setArea($var)
    {
        GPBUtil::checkInt32($var);
        $this->area = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double from_lat = 33;</code>
     * @return float
     */
    public function getFromLat()
    {
        return $this->from_lat;
    }

    /**
     * Generated from protobuf field <code>double from_lat = 33;</code>
     * @param float $var
     * @return $this
     */
    public function setFromLat($var)
    {
        GPBUtil::checkDouble($var);
        $this->from_lat = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double from_lng = 34;</code>
     * @return float
     */
    public function getFromLng()
    {
        return $this->from_lng;
    }

    /**
     * Generated from protobuf field <code>double from_lng = 34;</code>
     * @param float $var
     * @return $this
     */
    public function setFromLng($var)
    {
        GPBUtil::checkDouble($var);
        $this->from_lng = $var;

        return $this;
    }

    /**
     *当前定位点
     *
     * Generated from protobuf field <code>double lat = 35;</code>
     * @return float
     */
    public function getLat()
    {
        return $this->lat;
    }

    /**
     *当前定位点
     *
     * Generated from protobuf field <code>double lat = 35;</code>
     * @param float $var
     * @return $this
     */
    public function setLat($var)
    {
        GPBUtil::checkDouble($var);
        $this->lat = $var;

        return $this;
    }

    /**
     *当前定位点
     *
     * Generated from protobuf field <code>double lng = 36;</code>
     * @return float
     */
    public function getLng()
    {
        return $this->lng;
    }

    /**
     *当前定位点
     *
     * Generated from protobuf field <code>double lng = 36;</code>
     * @param float $var
     * @return $this
     */
    public function setLng($var)
    {
        GPBUtil::checkDouble($var);
        $this->lng = $var;

        return $this;
    }

    /**
     *当前定位点
     *
     * Generated from protobuf field <code>double to_lat = 38;</code>
     * @return float
     */
    public function getToLat()
    {
        return $this->to_lat;
    }

    /**
     *当前定位点
     *
     * Generated from protobuf field <code>double to_lat = 38;</code>
     * @param float $var
     * @return $this
     */
    public function setToLat($var)
    {
        GPBUtil::checkDouble($var);
        $this->to_lat = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double to_lng = 39;</code>
     * @return float
     */
    public function getToLng()
    {
        return $this->to_lng;
    }

    /**
     * Generated from protobuf field <code>double to_lng = 39;</code>
     * @param float $var
     * @return $this
     */
    public function setToLng($var)
    {
        GPBUtil::checkDouble($var);
        $this->to_lng = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_name = 40;</code>
     * @return string
     */
    public function getFromName()
    {
        return $this->from_name;
    }

    /**
     * Generated from protobuf field <code>string from_name = 40;</code>
     * @param string $var
     * @return $this
     */
    public function setFromName($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_name = 41;</code>
     * @return string
     */
    public function getToName()
    {
        return $this->to_name;
    }

    /**
     * Generated from protobuf field <code>string to_name = 41;</code>
     * @param string $var
     * @return $this
     */
    public function setToName($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_poi_id = 42;</code>
     * @return string
     */
    public function getFromPoiId()
    {
        return $this->from_poi_id;
    }

    /**
     * Generated from protobuf field <code>string from_poi_id = 42;</code>
     * @param string $var
     * @return $this
     */
    public function setFromPoiId($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_poi_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_poi_type = 43;</code>
     * @return string
     */
    public function getFromPoiType()
    {
        return $this->from_poi_type;
    }

    /**
     * Generated from protobuf field <code>string from_poi_type = 43;</code>
     * @param string $var
     * @return $this
     */
    public function setFromPoiType($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_poi_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_poi_id = 44;</code>
     * @return string
     */
    public function getToPoiId()
    {
        return $this->to_poi_id;
    }

    /**
     * Generated from protobuf field <code>string to_poi_id = 44;</code>
     * @param string $var
     * @return $this
     */
    public function setToPoiId($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_poi_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_poi_type = 45;</code>
     * @return string
     */
    public function getToPoiType()
    {
        return $this->to_poi_type;
    }

    /**
     * Generated from protobuf field <code>string to_poi_type = 45;</code>
     * @param string $var
     * @return $this
     */
    public function setToPoiType($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_poi_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_address = 46;</code>
     * @return string
     */
    public function getFromAddress()
    {
        return $this->from_address;
    }

    /**
     * Generated from protobuf field <code>string from_address = 46;</code>
     * @param string $var
     * @return $this
     */
    public function setFromAddress($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_address = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_address = 47;</code>
     * @return string
     */
    public function getToAddress()
    {
        return $this->to_address;
    }

    /**
     * Generated from protobuf field <code>string to_address = 47;</code>
     * @param string $var
     * @return $this
     */
    public function setToAddress($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_address = $var;

        return $this;
    }

    /**
     *用户当前路线id
     *
     * Generated from protobuf field <code>string route_id = 48;</code>
     * @return string
     */
    public function getRouteId()
    {
        return $this->route_id;
    }

    /**
     *用户当前路线id
     *
     * Generated from protobuf field <code>string route_id = 48;</code>
     * @param string $var
     * @return $this
     */
    public function setRouteId($var)
    {
        GPBUtil::checkString($var, True);
        $this->route_id = $var;

        return $this;
    }

    /**
     *用户选择的支付方式
     *
     * Generated from protobuf field <code>int32 payments_type = 54;</code>
     * @return int
     */
    public function getPaymentsType()
    {
        return $this->payments_type;
    }

    /**
     *用户选择的支付方式
     *
     * Generated from protobuf field <code>int32 payments_type = 54;</code>
     * @param int $var
     * @return $this
     */
    public function setPaymentsType($var)
    {
        GPBUtil::checkInt32($var);
        $this->payments_type = $var;

        return $this;
    }

    /**
     *时间戳
     *
     * Generated from protobuf field <code>string departure_time = 55;</code>
     * @return string
     */
    public function getDepartureTime()
    {
        return $this->departure_time;
    }

    /**
     *时间戳
     *
     * Generated from protobuf field <code>string departure_time = 55;</code>
     * @param string $var
     * @return $this
     */
    public function setDepartureTime($var)
    {
        GPBUtil::checkString($var, True);
        $this->departure_time = $var;

        return $this;
    }

    /**
     *pGetDynamicConfig下发的按钮样式
     *
     * Generated from protobuf field <code>int32 button_style = 56;</code>
     * @return int
     */
    public function getButtonStyle()
    {
        return $this->button_style;
    }

    /**
     *pGetDynamicConfig下发的按钮样式
     *
     * Generated from protobuf field <code>int32 button_style = 56;</code>
     * @param int $var
     * @return $this
     */
    public function setButtonStyle($var)
    {
        GPBUtil::checkInt32($var);
        $this->button_style = $var;

        return $this;
    }

    /**
     *推荐主题样式
     *
     * Generated from protobuf field <code>int32 rec_theme_type = 79;</code>
     * @return int
     */
    public function getRecThemeType()
    {
        return $this->rec_theme_type;
    }

    /**
     *推荐主题样式
     *
     * Generated from protobuf field <code>int32 rec_theme_type = 79;</code>
     * @param int $var
     * @return $this
     */
    public function setRecThemeType($var)
    {
        GPBUtil::checkInt32($var);
        $this->rec_theme_type = $var;

        return $this;
    }

    /**
     *{"estimate_id":"eid新快车","is_select_reccombo":0}
     *
     * Generated from protobuf field <code>string multi_product_category = 80;</code>
     * @return string
     */
    public function getMultiProductCategory()
    {
        return $this->multi_product_category;
    }

    /**
     *{"estimate_id":"eid新快车","is_select_reccombo":0}
     *
     * Generated from protobuf field <code>string multi_product_category = 80;</code>
     * @param string $var
     * @return $this
     */
    public function setMultiProductCategory($var)
    {
        GPBUtil::checkString($var, True);
        $this->multi_product_category = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string estimate_trace_id = 81;</code>
     * @return string
     */
    public function getEstimateTraceId()
    {
        return $this->estimate_trace_id;
    }

    /**
     * Generated from protobuf field <code>string estimate_trace_id = 81;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateTraceId($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_trace_id = $var;

        return $this;
    }

    /**
     *途经点信息
     *
     * Generated from protobuf field <code>string stopover_points = 82;</code>
     * @return string
     */
    public function getStopoverPoints()
    {
        return $this->stopover_points;
    }

    /**
     *途经点信息
     *
     * Generated from protobuf field <code>string stopover_points = 82;</code>
     * @param string $var
     * @return $this
     */
    public function setStopoverPoints($var)
    {
        GPBUtil::checkString($var, True);
        $this->stopover_points = $var;

        return $this;
    }

    /**
     *订单类型：0 是实时 1是预约
     *
     * Generated from protobuf field <code>int32 order_type = 83;</code>
     * @return int
     */
    public function getOrderType()
    {
        return $this->order_type;
    }

    /**
     *订单类型：0 是实时 1是预约
     *
     * Generated from protobuf field <code>int32 order_type = 83;</code>
     * @param int $var
     * @return $this
     */
    public function setOrderType($var)
    {
        GPBUtil::checkInt32($var);
        $this->order_type = $var;

        return $this;
    }

    /**
     *CallCarType 叫车类型：0为普通叫车；1为代叫车
     *
     * Generated from protobuf field <code>int32 call_car_type = 84;</code>
     * @return int
     */
    public function getCallCarType()
    {
        return $this->call_car_type;
    }

    /**
     *CallCarType 叫车类型：0为普通叫车；1为代叫车
     *
     * Generated from protobuf field <code>int32 call_car_type = 84;</code>
     * @param int $var
     * @return $this
     */
    public function setCallCarType($var)
    {
        GPBUtil::checkInt32($var);
        $this->call_car_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string xpsid = 85;</code>
     * @return string
     */
    public function getXpsid()
    {
        return $this->xpsid;
    }

    /**
     * Generated from protobuf field <code>string xpsid = 85;</code>
     * @param string $var
     * @return $this
     */
    public function setXpsid($var)
    {
        GPBUtil::checkString($var, True);
        $this->xpsid = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string xpsid_root = 86;</code>
     * @return string
     */
    public function getXpsidRoot()
    {
        return $this->xpsid_root;
    }

    /**
     * Generated from protobuf field <code>string xpsid_root = 86;</code>
     * @param string $var
     * @return $this
     */
    public function setXpsidRoot($var)
    {
        GPBUtil::checkString($var, True);
        $this->xpsid_root = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string act_id = 87;</code>
     * @return string
     */
    public function getActId()
    {
        return isset($this->act_id) ? $this->act_id : '';
    }

    public function hasActId()
    {
        return isset($this->act_id);
    }

    public function clearActId()
    {
        unset($this->act_id);
    }

    /**
     * Generated from protobuf field <code>string act_id = 87;</code>
     * @param string $var
     * @return $this
     */
    public function setActId($var)
    {
        GPBUtil::checkString($var, True);
        $this->act_id = $var;

        return $this;
    }

    /**
     *冒泡来源
     *
     * Generated from protobuf field <code>int32 from_type = 88;</code>
     * @return int
     */
    public function getFromType()
    {
        return isset($this->from_type) ? $this->from_type : 0;
    }

    public function hasFromType()
    {
        return isset($this->from_type);
    }

    public function clearFromType()
    {
        unset($this->from_type);
    }

    /**
     *冒泡来源
     *
     * Generated from protobuf field <code>int32 from_type = 88;</code>
     * @param int $var
     * @return $this
     */
    public function setFromType($var)
    {
        GPBUtil::checkInt32($var);
        $this->from_type = $var;

        return $this;
    }

    /**
     *是否需要展示事件 （通勤完单优惠大促：CommuteCouponFinishOrder）
     *
     * Generated from protobuf field <code>string is_need_hide_event = 89;</code>
     * @return string
     */
    public function getIsNeedHideEvent()
    {
        return isset($this->is_need_hide_event) ? $this->is_need_hide_event : '';
    }

    public function hasIsNeedHideEvent()
    {
        return isset($this->is_need_hide_event);
    }

    public function clearIsNeedHideEvent()
    {
        unset($this->is_need_hide_event);
    }

    /**
     *是否需要展示事件 （通勤完单优惠大促：CommuteCouponFinishOrder）
     *
     * Generated from protobuf field <code>string is_need_hide_event = 89;</code>
     * @param string $var
     * @return $this
     */
    public function setIsNeedHideEvent($var)
    {
        GPBUtil::checkString($var, True);
        $this->is_need_hide_event = $var;

        return $this;
    }

    /**
     *是否需要展示事件 （通勤完单优惠大促：CommuteCouponFinishOrder）
     *
     * Generated from protobuf field <code>int32 font_scale_type = 90;</code>
     * @return int
     */
    public function getFontScaleType()
    {
        return isset($this->font_scale_type) ? $this->font_scale_type : 0;
    }

    public function hasFontScaleType()
    {
        return isset($this->font_scale_type);
    }

    public function clearFontScaleType()
    {
        unset($this->font_scale_type);
    }

    /**
     *是否需要展示事件 （通勤完单优惠大促：CommuteCouponFinishOrder）
     *
     * Generated from protobuf field <code>int32 font_scale_type = 90;</code>
     * @param int $var
     * @return $this
     */
    public function setFontScaleType($var)
    {
        GPBUtil::checkInt32($var);
        $this->font_scale_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 form_style_exp = 91;</code>
     * @return int
     */
    public function getFormStyleExp()
    {
        return isset($this->form_style_exp) ? $this->form_style_exp : 0;
    }

    public function hasFormStyleExp()
    {
        return isset($this->form_style_exp);
    }

    public function clearFormStyleExp()
    {
        unset($this->form_style_exp);
    }

    /**
     * Generated from protobuf field <code>int32 form_style_exp = 91;</code>
     * @param int $var
     * @return $this
     */
    public function setFormStyleExp($var)
    {
        GPBUtil::checkInt32($var);
        $this->form_style_exp = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string one_stop_version = 92;</code>
     * @return string
     */
    public function getOneStopVersion()
    {
        return isset($this->one_stop_version) ? $this->one_stop_version : '';
    }

    public function hasOneStopVersion()
    {
        return isset($this->one_stop_version);
    }

    public function clearOneStopVersion()
    {
        unset($this->one_stop_version);
    }

    /**
     * Generated from protobuf field <code>string one_stop_version = 92;</code>
     * @param string $var
     * @return $this
     */
    public function setOneStopVersion($var)
    {
        GPBUtil::checkString($var, True);
        $this->one_stop_version = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 best_shift_distance = 93;</code>
     * @return int
     */
    public function getBestShiftDistance()
    {
        return isset($this->best_shift_distance) ? $this->best_shift_distance : 0;
    }

    public function hasBestShiftDistance()
    {
        return isset($this->best_shift_distance);
    }

    public function clearBestShiftDistance()
    {
        unset($this->best_shift_distance);
    }

    /**
     * Generated from protobuf field <code>int32 best_shift_distance = 93;</code>
     * @param int $var
     * @return $this
     */
    public function setBestShiftDistance($var)
    {
        GPBUtil::checkInt32($var);
        $this->best_shift_distance = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 best_shift_exp_group = 94;</code>
     * @return int
     */
    public function getBestShiftExpGroup()
    {
        return isset($this->best_shift_exp_group) ? $this->best_shift_exp_group : 0;
    }

    public function hasBestShiftExpGroup()
    {
        return isset($this->best_shift_exp_group);
    }

    public function clearBestShiftExpGroup()
    {
        unset($this->best_shift_exp_group);
    }

    /**
     * Generated from protobuf field <code>int32 best_shift_exp_group = 94;</code>
     * @param int $var
     * @return $this
     */
    public function setBestShiftExpGroup($var)
    {
        GPBUtil::checkInt32($var);
        $this->best_shift_exp_group = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 rec_form = 95;</code>
     * @return int
     */
    public function getRecForm()
    {
        return isset($this->rec_form) ? $this->rec_form : 0;
    }

    public function hasRecForm()
    {
        return isset($this->rec_form);
    }

    public function clearRecForm()
    {
        unset($this->rec_form);
    }

    /**
     * Generated from protobuf field <code>int32 rec_form = 95;</code>
     * @param int $var
     * @return $this
     */
    public function setRecForm($var)
    {
        GPBUtil::checkInt32($var);
        $this->rec_form = $var;

        return $this;
    }

}

