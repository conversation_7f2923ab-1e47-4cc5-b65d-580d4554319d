<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.FeeDescItem</code>
 */
class FeeDescItem extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string fee_desc = 1;</code>
     */
    protected $fee_desc = '';
    /**
     * Generated from protobuf field <code>string fee_desc_icon = 2;</code>
     */
    protected $fee_desc_icon = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $fee_desc
     *     @type string $fee_desc_icon
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string fee_desc = 1;</code>
     * @return string
     */
    public function getFeeDesc()
    {
        return $this->fee_desc;
    }

    /**
     * Generated from protobuf field <code>string fee_desc = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_desc = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_desc_icon = 2;</code>
     * @return string
     */
    public function getFeeDescIcon()
    {
        return isset($this->fee_desc_icon) ? $this->fee_desc_icon : '';
    }

    public function hasFeeDescIcon()
    {
        return isset($this->fee_desc_icon);
    }

    public function clearFeeDescIcon()
    {
        unset($this->fee_desc_icon);
    }

    /**
     * Generated from protobuf field <code>string fee_desc_icon = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeDescIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_desc_icon = $var;

        return $this;
    }

}

