<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.MapInfo</code>
 */
class MapInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string mapinfo_cache_token = 1;</code>
     */
    protected $mapinfo_cache_token = '';
    /**
     * Generated from protobuf field <code>bool show_mini_bus_station = 2;</code>
     */
    protected $show_mini_bus_station = false;
    /**
     * Generated from protobuf field <code>string mapinfo_start_cache_token = 3;</code>
     */
    protected $mapinfo_start_cache_token = '';
    /**
     * Generated from protobuf field <code>string mapinfo_dest_cache_token = 4;</code>
     */
    protected $mapinfo_dest_cache_token = '';
    /**
     * Generated from protobuf field <code>int32 best_view_type = 5;</code>
     */
    protected $best_view_type = 0;
    /**
     *自动驾驶的路线id
     *
     * Generated from protobuf field <code>string voy_route_id = 6;</code>
     */
    protected $voy_route_id = '';
    /**
     *小巴类型：1-智能小巴（给端上跳转地图组件使用）
     *
     * Generated from protobuf field <code>int32 minibus_type = 7;</code>
     */
    protected $minibus_type = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $mapinfo_cache_token
     *     @type bool $show_mini_bus_station
     *     @type string $mapinfo_start_cache_token
     *     @type string $mapinfo_dest_cache_token
     *     @type int $best_view_type
     *     @type string $voy_route_id
     *          自动驾驶的路线id
     *     @type int $minibus_type
     *          小巴类型：1-智能小巴（给端上跳转地图组件使用）
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string mapinfo_cache_token = 1;</code>
     * @return string
     */
    public function getMapinfoCacheToken()
    {
        return $this->mapinfo_cache_token;
    }

    /**
     * Generated from protobuf field <code>string mapinfo_cache_token = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setMapinfoCacheToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->mapinfo_cache_token = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool show_mini_bus_station = 2;</code>
     * @return bool
     */
    public function getShowMiniBusStation()
    {
        return $this->show_mini_bus_station;
    }

    /**
     * Generated from protobuf field <code>bool show_mini_bus_station = 2;</code>
     * @param bool $var
     * @return $this
     */
    public function setShowMiniBusStation($var)
    {
        GPBUtil::checkBool($var);
        $this->show_mini_bus_station = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string mapinfo_start_cache_token = 3;</code>
     * @return string
     */
    public function getMapinfoStartCacheToken()
    {
        return $this->mapinfo_start_cache_token;
    }

    /**
     * Generated from protobuf field <code>string mapinfo_start_cache_token = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setMapinfoStartCacheToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->mapinfo_start_cache_token = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string mapinfo_dest_cache_token = 4;</code>
     * @return string
     */
    public function getMapinfoDestCacheToken()
    {
        return $this->mapinfo_dest_cache_token;
    }

    /**
     * Generated from protobuf field <code>string mapinfo_dest_cache_token = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setMapinfoDestCacheToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->mapinfo_dest_cache_token = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 best_view_type = 5;</code>
     * @return int
     */
    public function getBestViewType()
    {
        return $this->best_view_type;
    }

    /**
     * Generated from protobuf field <code>int32 best_view_type = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setBestViewType($var)
    {
        GPBUtil::checkInt32($var);
        $this->best_view_type = $var;

        return $this;
    }

    /**
     *自动驾驶的路线id
     *
     * Generated from protobuf field <code>string voy_route_id = 6;</code>
     * @return string
     */
    public function getVoyRouteId()
    {
        return $this->voy_route_id;
    }

    /**
     *自动驾驶的路线id
     *
     * Generated from protobuf field <code>string voy_route_id = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setVoyRouteId($var)
    {
        GPBUtil::checkString($var, True);
        $this->voy_route_id = $var;

        return $this;
    }

    /**
     *小巴类型：1-智能小巴（给端上跳转地图组件使用）
     *
     * Generated from protobuf field <code>int32 minibus_type = 7;</code>
     * @return int
     */
    public function getMinibusType()
    {
        return $this->minibus_type;
    }

    /**
     *小巴类型：1-智能小巴（给端上跳转地图组件使用）
     *
     * Generated from protobuf field <code>int32 minibus_type = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setMinibusType($var)
    {
        GPBUtil::checkInt32($var);
        $this->minibus_type = $var;

        return $this;
    }

}

