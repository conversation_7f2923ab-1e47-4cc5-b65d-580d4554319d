<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.VerifyLuxuryPreferPageSensitiveWordRequest</code>
 */
class VerifyLuxuryPreferPageSensitiveWordRequest extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string to_be_verify_word = 1;</code>
     */
    protected $to_be_verify_word = '';
    /**
     * Generated from protobuf field <code>string lang = 2;</code>
     */
    protected $lang = '';
    /**
     * Generated from protobuf field <code>string token = 3;</code>
     */
    protected $token = '';
    /**
     * Generated from protobuf field <code>int32 detection_type = 4;</code>
     */
    protected $detection_type = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $to_be_verify_word
     *     @type string $lang
     *     @type string $token
     *     @type int $detection_type
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string to_be_verify_word = 1;</code>
     * @return string
     */
    public function getToBeVerifyWord()
    {
        return $this->to_be_verify_word;
    }

    /**
     * Generated from protobuf field <code>string to_be_verify_word = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setToBeVerifyWord($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_be_verify_word = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string lang = 2;</code>
     * @return string
     */
    public function getLang()
    {
        return $this->lang;
    }

    /**
     * Generated from protobuf field <code>string lang = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setLang($var)
    {
        GPBUtil::checkString($var, True);
        $this->lang = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string token = 3;</code>
     * @return string
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     * Generated from protobuf field <code>string token = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->token = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 detection_type = 4;</code>
     * @return int
     */
    public function getDetectionType()
    {
        return $this->detection_type;
    }

    /**
     * Generated from protobuf field <code>int32 detection_type = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setDetectionType($var)
    {
        GPBUtil::checkInt32($var);
        $this->detection_type = $var;

        return $this;
    }

}

