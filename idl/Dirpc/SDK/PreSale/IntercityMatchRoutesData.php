<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.IntercityMatchRoutesData</code>
 */
class IntercityMatchRoutesData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string time_title = 1;</code>
     */
    protected $time_title = '';
    /**
     * Generated from protobuf field <code>string sub_title = 2;</code>
     */
    protected $sub_title = '';
    /**
     * Generated from protobuf field <code>string left_text = 3;</code>
     */
    protected $left_text = '';
    /**
     * Generated from protobuf field <code>string right_text = 4;</code>
     */
    protected $right_text = '';
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.IntercityTimeSpan time_span = 5;</code>
     */
    private $time_span;
    /**
     *第一组的第一个时间片是否可以直接发单，不弹窗
     *
     * Generated from protobuf field <code>bool first_span_fit = 6;</code>
     */
    protected $first_span_fit = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $time_title
     *     @type string $sub_title
     *     @type string $left_text
     *     @type string $right_text
     *     @type \Dirpc\SDK\PreSale\IntercityTimeSpan[]|\Nuwa\Protobuf\Internal\RepeatedField $time_span
     *     @type bool $first_span_fit
     *          第一组的第一个时间片是否可以直接发单，不弹窗
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string time_title = 1;</code>
     * @return string
     */
    public function getTimeTitle()
    {
        return $this->time_title;
    }

    /**
     * Generated from protobuf field <code>string time_title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTimeTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->time_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string sub_title = 2;</code>
     * @return string
     */
    public function getSubTitle()
    {
        return $this->sub_title;
    }

    /**
     * Generated from protobuf field <code>string sub_title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setSubTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string left_text = 3;</code>
     * @return string
     */
    public function getLeftText()
    {
        return $this->left_text;
    }

    /**
     * Generated from protobuf field <code>string left_text = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setLeftText($var)
    {
        GPBUtil::checkString($var, True);
        $this->left_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string right_text = 4;</code>
     * @return string
     */
    public function getRightText()
    {
        return $this->right_text;
    }

    /**
     * Generated from protobuf field <code>string right_text = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setRightText($var)
    {
        GPBUtil::checkString($var, True);
        $this->right_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.IntercityTimeSpan time_span = 5;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getTimeSpan()
    {
        return $this->time_span;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.IntercityTimeSpan time_span = 5;</code>
     * @param \Dirpc\SDK\PreSale\IntercityTimeSpan[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setTimeSpan($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\IntercityTimeSpan::class);
        $this->time_span = $arr;

        return $this;
    }

    /**
     *第一组的第一个时间片是否可以直接发单，不弹窗
     *
     * Generated from protobuf field <code>bool first_span_fit = 6;</code>
     * @return bool
     */
    public function getFirstSpanFit()
    {
        return isset($this->first_span_fit) ? $this->first_span_fit : false;
    }

    public function hasFirstSpanFit()
    {
        return isset($this->first_span_fit);
    }

    public function clearFirstSpanFit()
    {
        unset($this->first_span_fit);
    }

    /**
     *第一组的第一个时间片是否可以直接发单，不弹窗
     *
     * Generated from protobuf field <code>bool first_span_fit = 6;</code>
     * @param bool $var
     * @return $this
     */
    public function setFirstSpanFit($var)
    {
        GPBUtil::checkBool($var);
        $this->first_span_fit = $var;

        return $this;
    }

}

