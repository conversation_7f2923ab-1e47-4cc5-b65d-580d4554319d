<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.OptionData</code>
 */
class OptionData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string title = 1;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>string icon = 2;</code>
     */
    protected $icon = '';
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.OptionService service_list = 3;</code>
     */
    private $service_list;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $title
     *     @type string $icon
     *     @type \Dirpc\SDK\PreSale\OptionService[]|\Nuwa\Protobuf\Internal\RepeatedField $service_list
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string icon = 2;</code>
     * @return string
     */
    public function getIcon()
    {
        return $this->icon;
    }

    /**
     * Generated from protobuf field <code>string icon = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->icon = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.OptionService service_list = 3;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getServiceList()
    {
        return $this->service_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.OptionService service_list = 3;</code>
     * @param \Dirpc\SDK\PreSale\OptionService[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setServiceList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\OptionService::class);
        $this->service_list = $arr;

        return $this;
    }

}

