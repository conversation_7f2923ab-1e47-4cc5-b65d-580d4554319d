<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.PromoteSalesText</code>
 */
class PromoteSalesText extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *规则
     *
     * Generated from protobuf field <code>repeated int32 rule_type = 1;</code>
     */
    private $rule_type;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int[]|\Nuwa\Protobuf\Internal\RepeatedField $rule_type
     *          规则
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *规则
     *
     * Generated from protobuf field <code>repeated int32 rule_type = 1;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getRuleType()
    {
        return $this->rule_type;
    }

    /**
     *规则
     *
     * Generated from protobuf field <code>repeated int32 rule_type = 1;</code>
     * @param int[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setRuleType($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::INT32);
        $this->rule_type = $arr;

        return $this;
    }

}

