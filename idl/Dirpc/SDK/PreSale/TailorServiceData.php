<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.TailorServiceData</code>
 */
class TailorServiceData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *偏好选项
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewPreferInfo prefer_info = 1;</code>
     */
    protected $prefer_info = null;
    /**
     *头部文案
     *
     * Generated from protobuf field <code>string head = 2;</code>
     */
    protected $head = '';
    /**
     *整个头图
     *
     * Generated from protobuf field <code>string head_img = 3;</code>
     */
    protected $head_img = '';
    /**
     *主标题
     *
     * Generated from protobuf field <code>string title = 4;</code>
     */
    protected $title = '';
    /**
     *副标题
     *
     * Generated from protobuf field <code>string sub_title = 5;</code>
     */
    protected $sub_title = '';
    /**
     *副标题跳转链接
     *
     * Generated from protobuf field <code>string sub_title_link = 6;</code>
     */
    protected $sub_title_link = '';
    /**
     *是否可以编辑
     *
     * Generated from protobuf field <code>int32 disable = 7;</code>
     */
    protected $disable = 0;
    /**
     *暖心服务数据
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ServiceData warm_info = 8;</code>
     */
    private $warm_info;
    /**
     *暖心服务文案
     *
     * Generated from protobuf field <code>string warm_head = 9;</code>
     */
    protected $warm_head = null;
    /**
     *升级服务文案
     *
     * Generated from protobuf field <code>string upgrade_head = 10;</code>
     */
    protected $upgrade_head = null;
    /**
     *升级服务数据
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ServiceData upgrade_info = 11;</code>
     */
    private $upgrade_info;
    /**
     *提示
     *
     * Generated from protobuf field <code>string tip = 12;</code>
     */
    protected $tip = '';
    /**
     *提示链接
     *
     * Generated from protobuf field <code>string tip_link = 13;</code>
     */
    protected $tip_link = '';
    /**
     *暂定
     *
     * Generated from protobuf field <code>string estimate_trace_id = 14;</code>
     */
    protected $estimate_trace_id = '';
    /**
     *服务不可用提示
     *
     * Generated from protobuf field <code>string warm_desc = 15;</code>
     */
    protected $warm_desc = null;
    /**
     *服务不可用提示
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CarEstimateData estimate_car_level_data = 16;</code>
     */
    private $estimate_car_level_data;
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.DriverEstimateData estimate_driver_level_data = 17;</code>
     */
    private $estimate_driver_level_data;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ExtraService extra_service = 18;</code>
     */
    protected $extra_service = null;
    /**
     *定制服务页面
     *
     * Generated from protobuf field <code>int32 theme = 19;</code>
     */
    protected $theme = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\NewPreferInfo $prefer_info
     *          偏好选项
     *     @type string $head
     *          头部文案
     *     @type string $head_img
     *          整个头图
     *     @type string $title
     *          主标题
     *     @type string $sub_title
     *          副标题
     *     @type string $sub_title_link
     *          副标题跳转链接
     *     @type int $disable
     *          是否可以编辑
     *     @type \Dirpc\SDK\PreSale\ServiceData[]|\Nuwa\Protobuf\Internal\RepeatedField $warm_info
     *          暖心服务数据
     *     @type string $warm_head
     *          暖心服务文案
     *     @type string $upgrade_head
     *          升级服务文案
     *     @type \Dirpc\SDK\PreSale\ServiceData[]|\Nuwa\Protobuf\Internal\RepeatedField $upgrade_info
     *          升级服务数据
     *     @type string $tip
     *          提示
     *     @type string $tip_link
     *          提示链接
     *     @type string $estimate_trace_id
     *          暂定
     *     @type string $warm_desc
     *          服务不可用提示
     *     @type \Dirpc\SDK\PreSale\CarEstimateData[]|\Nuwa\Protobuf\Internal\RepeatedField $estimate_car_level_data
     *          服务不可用提示
     *     @type \Dirpc\SDK\PreSale\DriverEstimateData[]|\Nuwa\Protobuf\Internal\RepeatedField $estimate_driver_level_data
     *     @type \Dirpc\SDK\PreSale\ExtraService $extra_service
     *     @type int $theme
     *          定制服务页面
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *偏好选项
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewPreferInfo prefer_info = 1;</code>
     * @return \Dirpc\SDK\PreSale\NewPreferInfo
     */
    public function getPreferInfo()
    {
        return isset($this->prefer_info) ? $this->prefer_info : null;
    }

    public function hasPreferInfo()
    {
        return isset($this->prefer_info);
    }

    public function clearPreferInfo()
    {
        unset($this->prefer_info);
    }

    /**
     *偏好选项
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewPreferInfo prefer_info = 1;</code>
     * @param \Dirpc\SDK\PreSale\NewPreferInfo $var
     * @return $this
     */
    public function setPreferInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NewPreferInfo::class);
        $this->prefer_info = $var;

        return $this;
    }

    /**
     *头部文案
     *
     * Generated from protobuf field <code>string head = 2;</code>
     * @return string
     */
    public function getHead()
    {
        return $this->head;
    }

    /**
     *头部文案
     *
     * Generated from protobuf field <code>string head = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setHead($var)
    {
        GPBUtil::checkString($var, True);
        $this->head = $var;

        return $this;
    }

    /**
     *整个头图
     *
     * Generated from protobuf field <code>string head_img = 3;</code>
     * @return string
     */
    public function getHeadImg()
    {
        return $this->head_img;
    }

    /**
     *整个头图
     *
     * Generated from protobuf field <code>string head_img = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setHeadImg($var)
    {
        GPBUtil::checkString($var, True);
        $this->head_img = $var;

        return $this;
    }

    /**
     *主标题
     *
     * Generated from protobuf field <code>string title = 4;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     *主标题
     *
     * Generated from protobuf field <code>string title = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     *副标题
     *
     * Generated from protobuf field <code>string sub_title = 5;</code>
     * @return string
     */
    public function getSubTitle()
    {
        return $this->sub_title;
    }

    /**
     *副标题
     *
     * Generated from protobuf field <code>string sub_title = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setSubTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_title = $var;

        return $this;
    }

    /**
     *副标题跳转链接
     *
     * Generated from protobuf field <code>string sub_title_link = 6;</code>
     * @return string
     */
    public function getSubTitleLink()
    {
        return $this->sub_title_link;
    }

    /**
     *副标题跳转链接
     *
     * Generated from protobuf field <code>string sub_title_link = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setSubTitleLink($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_title_link = $var;

        return $this;
    }

    /**
     *是否可以编辑
     *
     * Generated from protobuf field <code>int32 disable = 7;</code>
     * @return int
     */
    public function getDisable()
    {
        return $this->disable;
    }

    /**
     *是否可以编辑
     *
     * Generated from protobuf field <code>int32 disable = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setDisable($var)
    {
        GPBUtil::checkInt32($var);
        $this->disable = $var;

        return $this;
    }

    /**
     *暖心服务数据
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ServiceData warm_info = 8;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getWarmInfo()
    {
        return $this->warm_info;
    }

    /**
     *暖心服务数据
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ServiceData warm_info = 8;</code>
     * @param \Dirpc\SDK\PreSale\ServiceData[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setWarmInfo($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\ServiceData::class);
        $this->warm_info = $arr;

        return $this;
    }

    /**
     *暖心服务文案
     *
     * Generated from protobuf field <code>string warm_head = 9;</code>
     * @return string
     */
    public function getWarmHead()
    {
        return isset($this->warm_head) ? $this->warm_head : '';
    }

    public function hasWarmHead()
    {
        return isset($this->warm_head);
    }

    public function clearWarmHead()
    {
        unset($this->warm_head);
    }

    /**
     *暖心服务文案
     *
     * Generated from protobuf field <code>string warm_head = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setWarmHead($var)
    {
        GPBUtil::checkString($var, True);
        $this->warm_head = $var;

        return $this;
    }

    /**
     *升级服务文案
     *
     * Generated from protobuf field <code>string upgrade_head = 10;</code>
     * @return string
     */
    public function getUpgradeHead()
    {
        return isset($this->upgrade_head) ? $this->upgrade_head : '';
    }

    public function hasUpgradeHead()
    {
        return isset($this->upgrade_head);
    }

    public function clearUpgradeHead()
    {
        unset($this->upgrade_head);
    }

    /**
     *升级服务文案
     *
     * Generated from protobuf field <code>string upgrade_head = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setUpgradeHead($var)
    {
        GPBUtil::checkString($var, True);
        $this->upgrade_head = $var;

        return $this;
    }

    /**
     *升级服务数据
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ServiceData upgrade_info = 11;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getUpgradeInfo()
    {
        return $this->upgrade_info;
    }

    /**
     *升级服务数据
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ServiceData upgrade_info = 11;</code>
     * @param \Dirpc\SDK\PreSale\ServiceData[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setUpgradeInfo($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\ServiceData::class);
        $this->upgrade_info = $arr;

        return $this;
    }

    /**
     *提示
     *
     * Generated from protobuf field <code>string tip = 12;</code>
     * @return string
     */
    public function getTip()
    {
        return $this->tip;
    }

    /**
     *提示
     *
     * Generated from protobuf field <code>string tip = 12;</code>
     * @param string $var
     * @return $this
     */
    public function setTip($var)
    {
        GPBUtil::checkString($var, True);
        $this->tip = $var;

        return $this;
    }

    /**
     *提示链接
     *
     * Generated from protobuf field <code>string tip_link = 13;</code>
     * @return string
     */
    public function getTipLink()
    {
        return $this->tip_link;
    }

    /**
     *提示链接
     *
     * Generated from protobuf field <code>string tip_link = 13;</code>
     * @param string $var
     * @return $this
     */
    public function setTipLink($var)
    {
        GPBUtil::checkString($var, True);
        $this->tip_link = $var;

        return $this;
    }

    /**
     *暂定
     *
     * Generated from protobuf field <code>string estimate_trace_id = 14;</code>
     * @return string
     */
    public function getEstimateTraceId()
    {
        return $this->estimate_trace_id;
    }

    /**
     *暂定
     *
     * Generated from protobuf field <code>string estimate_trace_id = 14;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateTraceId($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_trace_id = $var;

        return $this;
    }

    /**
     *服务不可用提示
     *
     * Generated from protobuf field <code>string warm_desc = 15;</code>
     * @return string
     */
    public function getWarmDesc()
    {
        return isset($this->warm_desc) ? $this->warm_desc : '';
    }

    public function hasWarmDesc()
    {
        return isset($this->warm_desc);
    }

    public function clearWarmDesc()
    {
        unset($this->warm_desc);
    }

    /**
     *服务不可用提示
     *
     * Generated from protobuf field <code>string warm_desc = 15;</code>
     * @param string $var
     * @return $this
     */
    public function setWarmDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->warm_desc = $var;

        return $this;
    }

    /**
     *服务不可用提示
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CarEstimateData estimate_car_level_data = 16;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getEstimateCarLevelData()
    {
        return $this->estimate_car_level_data;
    }

    /**
     *服务不可用提示
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CarEstimateData estimate_car_level_data = 16;</code>
     * @param \Dirpc\SDK\PreSale\CarEstimateData[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setEstimateCarLevelData($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\CarEstimateData::class);
        $this->estimate_car_level_data = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.DriverEstimateData estimate_driver_level_data = 17;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getEstimateDriverLevelData()
    {
        return $this->estimate_driver_level_data;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.DriverEstimateData estimate_driver_level_data = 17;</code>
     * @param \Dirpc\SDK\PreSale\DriverEstimateData[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setEstimateDriverLevelData($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\DriverEstimateData::class);
        $this->estimate_driver_level_data = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ExtraService extra_service = 18;</code>
     * @return \Dirpc\SDK\PreSale\ExtraService
     */
    public function getExtraService()
    {
        return isset($this->extra_service) ? $this->extra_service : null;
    }

    public function hasExtraService()
    {
        return isset($this->extra_service);
    }

    public function clearExtraService()
    {
        unset($this->extra_service);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ExtraService extra_service = 18;</code>
     * @param \Dirpc\SDK\PreSale\ExtraService $var
     * @return $this
     */
    public function setExtraService($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\ExtraService::class);
        $this->extra_service = $var;

        return $this;
    }

    /**
     *定制服务页面
     *
     * Generated from protobuf field <code>int32 theme = 19;</code>
     * @return int
     */
    public function getTheme()
    {
        return $this->theme;
    }

    /**
     *定制服务页面
     *
     * Generated from protobuf field <code>int32 theme = 19;</code>
     * @param int $var
     * @return $this
     */
    public function setTheme($var)
    {
        GPBUtil::checkInt32($var);
        $this->theme = $var;

        return $this;
    }

}

