<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NewFormPreferData</code>
 */
class NewFormPreferData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string desc = 1;</code>
     */
    protected $desc = '';
    /**
     * Generated from protobuf field <code>string fee_msg = 2;</code>
     */
    protected $fee_msg = '';
    /**
     * Generated from protobuf field <code>string info_url = 3;</code>
     */
    protected $info_url = '';
    /**
     *增值服务id
     *
     * Generated from protobuf field <code>int32 id = 4;</code>
     */
    protected $id = 0;
    /**
     *增值服务id
     *
     * Generated from protobuf field <code>int32 count = 5;</code>
     */
    protected $count = 0;
    /**
     *是否勾选
     *
     * Generated from protobuf field <code>int32 is_selected = 6;</code>
     */
    protected $is_selected = 0;
    /**
     *是否勾选
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormPreferDataTag tag_list = 7;</code>
     */
    private $tag_list;
    /**
     * Generated from protobuf field <code>string jump_url = 8;</code>
     */
    protected $jump_url = null;
    /**
     * Generated from protobuf field <code>string fee_amount = 9;</code>
     */
    protected $fee_amount = null;
    /**
     *单钩样式
     *
     * Generated from protobuf field <code>int32 single_style = 10;</code>
     */
    protected $single_style = null;
    /**
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 11;</code>
     */
    private $fee_desc_list;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $desc
     *     @type string $fee_msg
     *     @type string $info_url
     *     @type int $id
     *          增值服务id
     *     @type int $count
     *          增值服务id
     *     @type int $is_selected
     *          是否勾选
     *     @type \Dirpc\SDK\PreSale\NewFormPreferDataTag[]|\Nuwa\Protobuf\Internal\RepeatedField $tag_list
     *          是否勾选
     *     @type string $jump_url
     *     @type string $fee_amount
     *     @type int $single_style
     *          单钩样式
     *     @type \Dirpc\SDK\PreSale\NewFormFeeDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $fee_desc_list
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string desc = 1;</code>
     * @return string
     */
    public function getDesc()
    {
        return $this->desc;
    }

    /**
     * Generated from protobuf field <code>string desc = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->desc = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_msg = 2;</code>
     * @return string
     */
    public function getFeeMsg()
    {
        return $this->fee_msg;
    }

    /**
     * Generated from protobuf field <code>string fee_msg = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string info_url = 3;</code>
     * @return string
     */
    public function getInfoUrl()
    {
        return $this->info_url;
    }

    /**
     * Generated from protobuf field <code>string info_url = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setInfoUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->info_url = $var;

        return $this;
    }

    /**
     *增值服务id
     *
     * Generated from protobuf field <code>int32 id = 4;</code>
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     *增值服务id
     *
     * Generated from protobuf field <code>int32 id = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setId($var)
    {
        GPBUtil::checkInt32($var);
        $this->id = $var;

        return $this;
    }

    /**
     *增值服务id
     *
     * Generated from protobuf field <code>int32 count = 5;</code>
     * @return int
     */
    public function getCount()
    {
        return $this->count;
    }

    /**
     *增值服务id
     *
     * Generated from protobuf field <code>int32 count = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setCount($var)
    {
        GPBUtil::checkInt32($var);
        $this->count = $var;

        return $this;
    }

    /**
     *是否勾选
     *
     * Generated from protobuf field <code>int32 is_selected = 6;</code>
     * @return int
     */
    public function getIsSelected()
    {
        return $this->is_selected;
    }

    /**
     *是否勾选
     *
     * Generated from protobuf field <code>int32 is_selected = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setIsSelected($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_selected = $var;

        return $this;
    }

    /**
     *是否勾选
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormPreferDataTag tag_list = 7;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getTagList()
    {
        return $this->tag_list;
    }

    /**
     *是否勾选
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormPreferDataTag tag_list = 7;</code>
     * @param \Dirpc\SDK\PreSale\NewFormPreferDataTag[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setTagList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\NewFormPreferDataTag::class);
        $this->tag_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string jump_url = 8;</code>
     * @return string
     */
    public function getJumpUrl()
    {
        return isset($this->jump_url) ? $this->jump_url : '';
    }

    public function hasJumpUrl()
    {
        return isset($this->jump_url);
    }

    public function clearJumpUrl()
    {
        unset($this->jump_url);
    }

    /**
     * Generated from protobuf field <code>string jump_url = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setJumpUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->jump_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_amount = 9;</code>
     * @return string
     */
    public function getFeeAmount()
    {
        return isset($this->fee_amount) ? $this->fee_amount : '';
    }

    public function hasFeeAmount()
    {
        return isset($this->fee_amount);
    }

    public function clearFeeAmount()
    {
        unset($this->fee_amount);
    }

    /**
     * Generated from protobuf field <code>string fee_amount = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeAmount($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_amount = $var;

        return $this;
    }

    /**
     *单钩样式
     *
     * Generated from protobuf field <code>int32 single_style = 10;</code>
     * @return int
     */
    public function getSingleStyle()
    {
        return isset($this->single_style) ? $this->single_style : 0;
    }

    public function hasSingleStyle()
    {
        return isset($this->single_style);
    }

    public function clearSingleStyle()
    {
        unset($this->single_style);
    }

    /**
     *单钩样式
     *
     * Generated from protobuf field <code>int32 single_style = 10;</code>
     * @param int $var
     * @return $this
     */
    public function setSingleStyle($var)
    {
        GPBUtil::checkInt32($var);
        $this->single_style = $var;

        return $this;
    }

    /**
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 11;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getFeeDescList()
    {
        return $this->fee_desc_list;
    }

    /**
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 11;</code>
     * @param \Dirpc\SDK\PreSale\NewFormFeeDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFeeDescList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\NewFormFeeDesc::class);
        $this->fee_desc_list = $arr;

        return $this;
    }

}

