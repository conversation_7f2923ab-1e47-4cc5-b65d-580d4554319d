<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideRuleBottomContentLeftPopup</code>
 */
class SideRuleBottomContentLeftPopup extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *图片
     *
     * Generated from protobuf field <code>string top_img_url = 1;</code>
     */
    protected $top_img_url = '';
    /**
     *文案
     *
     * Generated from protobuf field <code>repeated string rule_texts = 2;</code>
     */
    private $rule_texts;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $top_img_url
     *          图片
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $rule_texts
     *          文案
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     *图片
     *
     * Generated from protobuf field <code>string top_img_url = 1;</code>
     * @return string
     */
    public function getTopImgUrl()
    {
        return $this->top_img_url;
    }

    /**
     *图片
     *
     * Generated from protobuf field <code>string top_img_url = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTopImgUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->top_img_url = $var;

        return $this;
    }

    /**
     *文案
     *
     * Generated from protobuf field <code>repeated string rule_texts = 2;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getRuleTexts()
    {
        return $this->rule_texts;
    }

    /**
     *文案
     *
     * Generated from protobuf field <code>repeated string rule_texts = 2;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setRuleTexts($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->rule_texts = $arr;

        return $this;
    }

}

