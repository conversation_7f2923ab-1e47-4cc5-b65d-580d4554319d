<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.Category</code>
 */
class Category extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *分组id
     *
     * Generated from protobuf field <code>int32 category_id = 1;</code>
     */
    protected $category_id = 0;
    /**
     *座位信息
     *
     * Generated from protobuf field <code>int32 seat_num = 2;</code>
     */
    protected $seat_num = 0;
    /**
     *座位文案
     *
     * Generated from protobuf field <code>string seat_desc = 3;</code>
     */
    protected $seat_desc = '';
    /**
     *座位文案
     *
     * Generated from protobuf field <code>string seat_prefix_desc = 4;</code>
     */
    protected $seat_prefix_desc = '';
    /**
     *是否渲染该品类 0/1
     *
     * Generated from protobuf field <code>int32 enable_display = 5;</code>
     */
    protected $enable_display = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $category_id
     *          分组id
     *     @type int $seat_num
     *          座位信息
     *     @type string $seat_desc
     *          座位文案
     *     @type string $seat_prefix_desc
     *          座位文案
     *     @type int $enable_display
     *          是否渲染该品类 0/1
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *分组id
     *
     * Generated from protobuf field <code>int32 category_id = 1;</code>
     * @return int
     */
    public function getCategoryId()
    {
        return $this->category_id;
    }

    /**
     *分组id
     *
     * Generated from protobuf field <code>int32 category_id = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setCategoryId($var)
    {
        GPBUtil::checkInt32($var);
        $this->category_id = $var;

        return $this;
    }

    /**
     *座位信息
     *
     * Generated from protobuf field <code>int32 seat_num = 2;</code>
     * @return int
     */
    public function getSeatNum()
    {
        return $this->seat_num;
    }

    /**
     *座位信息
     *
     * Generated from protobuf field <code>int32 seat_num = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setSeatNum($var)
    {
        GPBUtil::checkInt32($var);
        $this->seat_num = $var;

        return $this;
    }

    /**
     *座位文案
     *
     * Generated from protobuf field <code>string seat_desc = 3;</code>
     * @return string
     */
    public function getSeatDesc()
    {
        return $this->seat_desc;
    }

    /**
     *座位文案
     *
     * Generated from protobuf field <code>string seat_desc = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setSeatDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->seat_desc = $var;

        return $this;
    }

    /**
     *座位文案
     *
     * Generated from protobuf field <code>string seat_prefix_desc = 4;</code>
     * @return string
     */
    public function getSeatPrefixDesc()
    {
        return $this->seat_prefix_desc;
    }

    /**
     *座位文案
     *
     * Generated from protobuf field <code>string seat_prefix_desc = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setSeatPrefixDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->seat_prefix_desc = $var;

        return $this;
    }

    /**
     *是否渲染该品类 0/1
     *
     * Generated from protobuf field <code>int32 enable_display = 5;</code>
     * @return int
     */
    public function getEnableDisplay()
    {
        return $this->enable_display;
    }

    /**
     *是否渲染该品类 0/1
     *
     * Generated from protobuf field <code>int32 enable_display = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setEnableDisplay($var)
    {
        GPBUtil::checkInt32($var);
        $this->enable_display = $var;

        return $this;
    }

}

