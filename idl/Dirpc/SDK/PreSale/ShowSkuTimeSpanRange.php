<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.ShowSkuTimeSpanRange</code>
 */
class ShowSkuTimeSpanRange extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string value = 1;</code>
     */
    protected $value = '';
    /**
     * Generated from protobuf field <code>string tags = 2;</code>
     */
    protected $tags = '';
    /**
     * Generated from protobuf field <code>string tips = 3;</code>
     */
    protected $tips = '';
    /**
     * Generated from protobuf field <code>bool available = 4;</code>
     */
    protected $available = false;
    /**
     * Generated from protobuf field <code>string available_tags = 5;</code>
     */
    protected $available_tags = '';
    /**
     * Generated from protobuf field <code>string base_price_desc = 6;</code>
     */
    protected $base_price_desc = '';
    /**
     * Generated from protobuf field <code>string price_desc = 7;</code>
     */
    protected $price_desc = '';
    /**
     * Generated from protobuf field <code>int32 order_type = 8;</code>
     */
    protected $order_type = 0;
    /**
     * Generated from protobuf field <code>string left_label = 9;</code>
     */
    protected $left_label = '';
    /**
     * Generated from protobuf field <code>string right_label = 10;</code>
     */
    protected $right_label = '';
    /**
     * Generated from protobuf field <code>string label = 11;</code>
     */
    protected $label = '';
    /**
     * Generated from protobuf field <code>string title = 12;</code>
     */
    protected $title = null;
    /**
     * Generated from protobuf field <code>string sub_title = 13;</code>
     */
    protected $sub_title = null;
    /**
     * Generated from protobuf field <code>string icon = 14;</code>
     */
    protected $icon = null;
    /**
     * Generated from protobuf field <code>string sku_desc = 15;</code>
     */
    protected $sku_desc = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $value
     *     @type string $tags
     *     @type string $tips
     *     @type bool $available
     *     @type string $available_tags
     *     @type string $base_price_desc
     *     @type string $price_desc
     *     @type int $order_type
     *     @type string $left_label
     *     @type string $right_label
     *     @type string $label
     *     @type string $title
     *     @type string $sub_title
     *     @type string $icon
     *     @type string $sku_desc
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string value = 1;</code>
     * @return string
     */
    public function getValue()
    {
        return $this->value;
    }

    /**
     * Generated from protobuf field <code>string value = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setValue($var)
    {
        GPBUtil::checkString($var, True);
        $this->value = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string tags = 2;</code>
     * @return string
     */
    public function getTags()
    {
        return $this->tags;
    }

    /**
     * Generated from protobuf field <code>string tags = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTags($var)
    {
        GPBUtil::checkString($var, True);
        $this->tags = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string tips = 3;</code>
     * @return string
     */
    public function getTips()
    {
        return $this->tips;
    }

    /**
     * Generated from protobuf field <code>string tips = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setTips($var)
    {
        GPBUtil::checkString($var, True);
        $this->tips = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool available = 4;</code>
     * @return bool
     */
    public function getAvailable()
    {
        return $this->available;
    }

    /**
     * Generated from protobuf field <code>bool available = 4;</code>
     * @param bool $var
     * @return $this
     */
    public function setAvailable($var)
    {
        GPBUtil::checkBool($var);
        $this->available = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string available_tags = 5;</code>
     * @return string
     */
    public function getAvailableTags()
    {
        return $this->available_tags;
    }

    /**
     * Generated from protobuf field <code>string available_tags = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setAvailableTags($var)
    {
        GPBUtil::checkString($var, True);
        $this->available_tags = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string base_price_desc = 6;</code>
     * @return string
     */
    public function getBasePriceDesc()
    {
        return $this->base_price_desc;
    }

    /**
     * Generated from protobuf field <code>string base_price_desc = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setBasePriceDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->base_price_desc = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string price_desc = 7;</code>
     * @return string
     */
    public function getPriceDesc()
    {
        return $this->price_desc;
    }

    /**
     * Generated from protobuf field <code>string price_desc = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setPriceDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->price_desc = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 order_type = 8;</code>
     * @return int
     */
    public function getOrderType()
    {
        return $this->order_type;
    }

    /**
     * Generated from protobuf field <code>int32 order_type = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setOrderType($var)
    {
        GPBUtil::checkInt32($var);
        $this->order_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string left_label = 9;</code>
     * @return string
     */
    public function getLeftLabel()
    {
        return $this->left_label;
    }

    /**
     * Generated from protobuf field <code>string left_label = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setLeftLabel($var)
    {
        GPBUtil::checkString($var, True);
        $this->left_label = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string right_label = 10;</code>
     * @return string
     */
    public function getRightLabel()
    {
        return $this->right_label;
    }

    /**
     * Generated from protobuf field <code>string right_label = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setRightLabel($var)
    {
        GPBUtil::checkString($var, True);
        $this->right_label = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string label = 11;</code>
     * @return string
     */
    public function getLabel()
    {
        return $this->label;
    }

    /**
     * Generated from protobuf field <code>string label = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setLabel($var)
    {
        GPBUtil::checkString($var, True);
        $this->label = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string title = 12;</code>
     * @return string
     */
    public function getTitle()
    {
        return isset($this->title) ? $this->title : '';
    }

    public function hasTitle()
    {
        return isset($this->title);
    }

    public function clearTitle()
    {
        unset($this->title);
    }

    /**
     * Generated from protobuf field <code>string title = 12;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string sub_title = 13;</code>
     * @return string
     */
    public function getSubTitle()
    {
        return isset($this->sub_title) ? $this->sub_title : '';
    }

    public function hasSubTitle()
    {
        return isset($this->sub_title);
    }

    public function clearSubTitle()
    {
        unset($this->sub_title);
    }

    /**
     * Generated from protobuf field <code>string sub_title = 13;</code>
     * @param string $var
     * @return $this
     */
    public function setSubTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string icon = 14;</code>
     * @return string
     */
    public function getIcon()
    {
        return isset($this->icon) ? $this->icon : '';
    }

    public function hasIcon()
    {
        return isset($this->icon);
    }

    public function clearIcon()
    {
        unset($this->icon);
    }

    /**
     * Generated from protobuf field <code>string icon = 14;</code>
     * @param string $var
     * @return $this
     */
    public function setIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->icon = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string sku_desc = 15;</code>
     * @return string
     */
    public function getSkuDesc()
    {
        return isset($this->sku_desc) ? $this->sku_desc : '';
    }

    public function hasSkuDesc()
    {
        return isset($this->sku_desc);
    }

    public function clearSkuDesc()
    {
        unset($this->sku_desc);
    }

    /**
     * Generated from protobuf field <code>string sku_desc = 15;</code>
     * @param string $var
     * @return $this
     */
    public function setSkuDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->sku_desc = $var;

        return $this;
    }

}

