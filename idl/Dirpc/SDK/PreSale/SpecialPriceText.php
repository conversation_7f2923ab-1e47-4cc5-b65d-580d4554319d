<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SpecialPriceText</code>
 */
class SpecialPriceText extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *规则
     *
     * Generated from protobuf field <code>repeated int32 rule_type = 1;</code>
     */
    private $rule_type;
    /**
     *文案
     *
     * Generated from protobuf field <code>string text = 2;</code>
     */
    protected $text = '';
    /**
     *强弹标识
     *
     * Generated from protobuf field <code>int32 is_force_notice = 3;</code>
     */
    protected $is_force_notice = null;
    /**
     *多行样式
     *
     * Generated from protobuf field <code>int32 multi_line_style_type = 4;</code>
     */
    protected $multi_line_style_type = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int[]|\Nuwa\Protobuf\Internal\RepeatedField $rule_type
     *          规则
     *     @type string $text
     *          文案
     *     @type int $is_force_notice
     *          强弹标识
     *     @type int $multi_line_style_type
     *          多行样式
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *规则
     *
     * Generated from protobuf field <code>repeated int32 rule_type = 1;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getRuleType()
    {
        return $this->rule_type;
    }

    /**
     *规则
     *
     * Generated from protobuf field <code>repeated int32 rule_type = 1;</code>
     * @param int[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setRuleType($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::INT32);
        $this->rule_type = $arr;

        return $this;
    }

    /**
     *文案
     *
     * Generated from protobuf field <code>string text = 2;</code>
     * @return string
     */
    public function getText()
    {
        return $this->text;
    }

    /**
     *文案
     *
     * Generated from protobuf field <code>string text = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setText($var)
    {
        GPBUtil::checkString($var, True);
        $this->text = $var;

        return $this;
    }

    /**
     *强弹标识
     *
     * Generated from protobuf field <code>int32 is_force_notice = 3;</code>
     * @return int
     */
    public function getIsForceNotice()
    {
        return isset($this->is_force_notice) ? $this->is_force_notice : 0;
    }

    public function hasIsForceNotice()
    {
        return isset($this->is_force_notice);
    }

    public function clearIsForceNotice()
    {
        unset($this->is_force_notice);
    }

    /**
     *强弹标识
     *
     * Generated from protobuf field <code>int32 is_force_notice = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setIsForceNotice($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_force_notice = $var;

        return $this;
    }

    /**
     *多行样式
     *
     * Generated from protobuf field <code>int32 multi_line_style_type = 4;</code>
     * @return int
     */
    public function getMultiLineStyleType()
    {
        return isset($this->multi_line_style_type) ? $this->multi_line_style_type : 0;
    }

    public function hasMultiLineStyleType()
    {
        return isset($this->multi_line_style_type);
    }

    public function clearMultiLineStyleType()
    {
        unset($this->multi_line_style_type);
    }

    /**
     *多行样式
     *
     * Generated from protobuf field <code>int32 multi_line_style_type = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setMultiLineStyleType($var)
    {
        GPBUtil::checkInt32($var);
        $this->multi_line_style_type = $var;

        return $this;
    }

}

