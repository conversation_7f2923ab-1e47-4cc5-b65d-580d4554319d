<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.MatchRoutesData</code>
 */
class MatchRoutesData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string time_title = 1;</code>
     */
    protected $time_title = '';
    /**
     * Generated from protobuf field <code>string sub_title = 2;</code>
     */
    protected $sub_title = '';
    /**
     * Generated from protobuf field <code>string left_text = 3;</code>
     */
    protected $left_text = '';
    /**
     * Generated from protobuf field <code>string right_text = 4;</code>
     */
    protected $right_text = '';
    /**
     * Generated from protobuf field <code>string route_end_name = 5;</code>
     */
    protected $route_end_name = '';
    /**
     * Generated from protobuf field <code>string price_tip = 6;</code>
     */
    protected $price_tip = '';
    /**
     * Generated from protobuf field <code>string none_range_pic = 7;</code>
     */
    protected $none_range_pic = '';
    /**
     * Generated from protobuf field <code>string none_range_tip = 8;</code>
     */
    protected $none_range_tip = '';
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.TimeSpan time_span = 9;</code>
     */
    private $time_span;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $time_title
     *     @type string $sub_title
     *     @type string $left_text
     *     @type string $right_text
     *     @type string $route_end_name
     *     @type string $price_tip
     *     @type string $none_range_pic
     *     @type string $none_range_tip
     *     @type \Dirpc\SDK\PreSale\TimeSpan[]|\Nuwa\Protobuf\Internal\RepeatedField $time_span
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string time_title = 1;</code>
     * @return string
     */
    public function getTimeTitle()
    {
        return $this->time_title;
    }

    /**
     * Generated from protobuf field <code>string time_title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTimeTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->time_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string sub_title = 2;</code>
     * @return string
     */
    public function getSubTitle()
    {
        return $this->sub_title;
    }

    /**
     * Generated from protobuf field <code>string sub_title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setSubTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string left_text = 3;</code>
     * @return string
     */
    public function getLeftText()
    {
        return $this->left_text;
    }

    /**
     * Generated from protobuf field <code>string left_text = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setLeftText($var)
    {
        GPBUtil::checkString($var, True);
        $this->left_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string right_text = 4;</code>
     * @return string
     */
    public function getRightText()
    {
        return $this->right_text;
    }

    /**
     * Generated from protobuf field <code>string right_text = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setRightText($var)
    {
        GPBUtil::checkString($var, True);
        $this->right_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string route_end_name = 5;</code>
     * @return string
     */
    public function getRouteEndName()
    {
        return $this->route_end_name;
    }

    /**
     * Generated from protobuf field <code>string route_end_name = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setRouteEndName($var)
    {
        GPBUtil::checkString($var, True);
        $this->route_end_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string price_tip = 6;</code>
     * @return string
     */
    public function getPriceTip()
    {
        return $this->price_tip;
    }

    /**
     * Generated from protobuf field <code>string price_tip = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setPriceTip($var)
    {
        GPBUtil::checkString($var, True);
        $this->price_tip = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string none_range_pic = 7;</code>
     * @return string
     */
    public function getNoneRangePic()
    {
        return $this->none_range_pic;
    }

    /**
     * Generated from protobuf field <code>string none_range_pic = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setNoneRangePic($var)
    {
        GPBUtil::checkString($var, True);
        $this->none_range_pic = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string none_range_tip = 8;</code>
     * @return string
     */
    public function getNoneRangeTip()
    {
        return $this->none_range_tip;
    }

    /**
     * Generated from protobuf field <code>string none_range_tip = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setNoneRangeTip($var)
    {
        GPBUtil::checkString($var, True);
        $this->none_range_tip = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.TimeSpan time_span = 9;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getTimeSpan()
    {
        return $this->time_span;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.TimeSpan time_span = 9;</code>
     * @param \Dirpc\SDK\PreSale\TimeSpan[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setTimeSpan($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\TimeSpan::class);
        $this->time_span = $arr;

        return $this;
    }

}

