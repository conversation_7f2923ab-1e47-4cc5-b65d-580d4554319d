<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: carpool.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.InvitationDataV2</code>
 */
class InvitationDataV2 extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *背景图片
     *
     * Generated from protobuf field <code>string bg_image = 1;</code>
     */
    protected $bg_image = null;
    /**
     *浮标文案
     *
     * Generated from protobuf field <code>string intro_msg = 2;</code>
     */
    protected $intro_msg = null;
    /**
     *浮标url
     *
     * Generated from protobuf field <code>string intro_url = 3;</code>
     */
    protected $intro_url = null;
    /**
     *当前用户落地页状态
     *
     * Generated from protobuf field <code>int32 status = 4;</code>
     */
    protected $status = null;
    /**
     *角色
     *
     * Generated from protobuf field <code>int32 role = 5;</code>
     */
    protected $role = null;
    /**
     *主卡位置行程卡片
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.TravelInfo travel_info = 6;</code>
     */
    protected $travel_info = null;
    /**
     *按钮
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.InvitationButtonV2 button = 7;</code>
     */
    private $button;
    /**
     *预估信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationEstimateInfoV2 estimate_info = 8;</code>
     */
    protected $estimate_info = null;
    /**
     *额外信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationExtraInfoV2 extra_info = 9;</code>
     */
    protected $extra_info = null;
    /**
     *页面标题
     *
     * Generated from protobuf field <code>string page_title = 10;</code>
     */
    protected $page_title = null;
    /**
     *座位数组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationCarpoolSeatModule carpool_seat_module = 11;</code>
     */
    protected $carpool_seat_module = null;
    /**
     *主卡下方拼友行程卡片
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.CarpoolerTravel carpooler_travel = 12;</code>
     */
    protected $carpooler_travel = null;
    /**
     *预估toast
     *
     * Generated from protobuf field <code>string estimate_loading = 13;</code>
     */
    protected $estimate_loading = null;
    /**
     *路径跳转
     *
     * Generated from protobuf field <code>string jump_url = 14;</code>
     */
    protected $jump_url = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $bg_image
     *          背景图片
     *     @type string $intro_msg
     *          浮标文案
     *     @type string $intro_url
     *          浮标url
     *     @type int $status
     *          当前用户落地页状态
     *     @type int $role
     *          角色
     *     @type \Dirpc\SDK\PreSale\TravelInfo $travel_info
     *          主卡位置行程卡片
     *     @type \Dirpc\SDK\PreSale\InvitationButtonV2[]|\Nuwa\Protobuf\Internal\RepeatedField $button
     *          按钮
     *     @type \Dirpc\SDK\PreSale\InvitationEstimateInfoV2 $estimate_info
     *          预估信息
     *     @type \Dirpc\SDK\PreSale\InvitationExtraInfoV2 $extra_info
     *          额外信息
     *     @type string $page_title
     *          页面标题
     *     @type \Dirpc\SDK\PreSale\InvitationCarpoolSeatModule $carpool_seat_module
     *          座位数组件
     *     @type \Dirpc\SDK\PreSale\CarpoolerTravel $carpooler_travel
     *          主卡下方拼友行程卡片
     *     @type string $estimate_loading
     *          预估toast
     *     @type string $jump_url
     *          路径跳转
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\Carpool::initOnce();
        parent::__construct($data);
    }

    /**
     *背景图片
     *
     * Generated from protobuf field <code>string bg_image = 1;</code>
     * @return string
     */
    public function getBgImage()
    {
        return isset($this->bg_image) ? $this->bg_image : '';
    }

    public function hasBgImage()
    {
        return isset($this->bg_image);
    }

    public function clearBgImage()
    {
        unset($this->bg_image);
    }

    /**
     *背景图片
     *
     * Generated from protobuf field <code>string bg_image = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setBgImage($var)
    {
        GPBUtil::checkString($var, True);
        $this->bg_image = $var;

        return $this;
    }

    /**
     *浮标文案
     *
     * Generated from protobuf field <code>string intro_msg = 2;</code>
     * @return string
     */
    public function getIntroMsg()
    {
        return isset($this->intro_msg) ? $this->intro_msg : '';
    }

    public function hasIntroMsg()
    {
        return isset($this->intro_msg);
    }

    public function clearIntroMsg()
    {
        unset($this->intro_msg);
    }

    /**
     *浮标文案
     *
     * Generated from protobuf field <code>string intro_msg = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setIntroMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->intro_msg = $var;

        return $this;
    }

    /**
     *浮标url
     *
     * Generated from protobuf field <code>string intro_url = 3;</code>
     * @return string
     */
    public function getIntroUrl()
    {
        return isset($this->intro_url) ? $this->intro_url : '';
    }

    public function hasIntroUrl()
    {
        return isset($this->intro_url);
    }

    public function clearIntroUrl()
    {
        unset($this->intro_url);
    }

    /**
     *浮标url
     *
     * Generated from protobuf field <code>string intro_url = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setIntroUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->intro_url = $var;

        return $this;
    }

    /**
     *当前用户落地页状态
     *
     * Generated from protobuf field <code>int32 status = 4;</code>
     * @return int
     */
    public function getStatus()
    {
        return isset($this->status) ? $this->status : 0;
    }

    public function hasStatus()
    {
        return isset($this->status);
    }

    public function clearStatus()
    {
        unset($this->status);
    }

    /**
     *当前用户落地页状态
     *
     * Generated from protobuf field <code>int32 status = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setStatus($var)
    {
        GPBUtil::checkInt32($var);
        $this->status = $var;

        return $this;
    }

    /**
     *角色
     *
     * Generated from protobuf field <code>int32 role = 5;</code>
     * @return int
     */
    public function getRole()
    {
        return isset($this->role) ? $this->role : 0;
    }

    public function hasRole()
    {
        return isset($this->role);
    }

    public function clearRole()
    {
        unset($this->role);
    }

    /**
     *角色
     *
     * Generated from protobuf field <code>int32 role = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setRole($var)
    {
        GPBUtil::checkInt32($var);
        $this->role = $var;

        return $this;
    }

    /**
     *主卡位置行程卡片
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.TravelInfo travel_info = 6;</code>
     * @return \Dirpc\SDK\PreSale\TravelInfo
     */
    public function getTravelInfo()
    {
        return isset($this->travel_info) ? $this->travel_info : null;
    }

    public function hasTravelInfo()
    {
        return isset($this->travel_info);
    }

    public function clearTravelInfo()
    {
        unset($this->travel_info);
    }

    /**
     *主卡位置行程卡片
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.TravelInfo travel_info = 6;</code>
     * @param \Dirpc\SDK\PreSale\TravelInfo $var
     * @return $this
     */
    public function setTravelInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\TravelInfo::class);
        $this->travel_info = $var;

        return $this;
    }

    /**
     *按钮
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.InvitationButtonV2 button = 7;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getButton()
    {
        return $this->button;
    }

    /**
     *按钮
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.InvitationButtonV2 button = 7;</code>
     * @param \Dirpc\SDK\PreSale\InvitationButtonV2[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setButton($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\InvitationButtonV2::class);
        $this->button = $arr;

        return $this;
    }

    /**
     *预估信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationEstimateInfoV2 estimate_info = 8;</code>
     * @return \Dirpc\SDK\PreSale\InvitationEstimateInfoV2
     */
    public function getEstimateInfo()
    {
        return isset($this->estimate_info) ? $this->estimate_info : null;
    }

    public function hasEstimateInfo()
    {
        return isset($this->estimate_info);
    }

    public function clearEstimateInfo()
    {
        unset($this->estimate_info);
    }

    /**
     *预估信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationEstimateInfoV2 estimate_info = 8;</code>
     * @param \Dirpc\SDK\PreSale\InvitationEstimateInfoV2 $var
     * @return $this
     */
    public function setEstimateInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\InvitationEstimateInfoV2::class);
        $this->estimate_info = $var;

        return $this;
    }

    /**
     *额外信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationExtraInfoV2 extra_info = 9;</code>
     * @return \Dirpc\SDK\PreSale\InvitationExtraInfoV2
     */
    public function getExtraInfo()
    {
        return isset($this->extra_info) ? $this->extra_info : null;
    }

    public function hasExtraInfo()
    {
        return isset($this->extra_info);
    }

    public function clearExtraInfo()
    {
        unset($this->extra_info);
    }

    /**
     *额外信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationExtraInfoV2 extra_info = 9;</code>
     * @param \Dirpc\SDK\PreSale\InvitationExtraInfoV2 $var
     * @return $this
     */
    public function setExtraInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\InvitationExtraInfoV2::class);
        $this->extra_info = $var;

        return $this;
    }

    /**
     *页面标题
     *
     * Generated from protobuf field <code>string page_title = 10;</code>
     * @return string
     */
    public function getPageTitle()
    {
        return isset($this->page_title) ? $this->page_title : '';
    }

    public function hasPageTitle()
    {
        return isset($this->page_title);
    }

    public function clearPageTitle()
    {
        unset($this->page_title);
    }

    /**
     *页面标题
     *
     * Generated from protobuf field <code>string page_title = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setPageTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->page_title = $var;

        return $this;
    }

    /**
     *座位数组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationCarpoolSeatModule carpool_seat_module = 11;</code>
     * @return \Dirpc\SDK\PreSale\InvitationCarpoolSeatModule
     */
    public function getCarpoolSeatModule()
    {
        return isset($this->carpool_seat_module) ? $this->carpool_seat_module : null;
    }

    public function hasCarpoolSeatModule()
    {
        return isset($this->carpool_seat_module);
    }

    public function clearCarpoolSeatModule()
    {
        unset($this->carpool_seat_module);
    }

    /**
     *座位数组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationCarpoolSeatModule carpool_seat_module = 11;</code>
     * @param \Dirpc\SDK\PreSale\InvitationCarpoolSeatModule $var
     * @return $this
     */
    public function setCarpoolSeatModule($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\InvitationCarpoolSeatModule::class);
        $this->carpool_seat_module = $var;

        return $this;
    }

    /**
     *主卡下方拼友行程卡片
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.CarpoolerTravel carpooler_travel = 12;</code>
     * @return \Dirpc\SDK\PreSale\CarpoolerTravel
     */
    public function getCarpoolerTravel()
    {
        return isset($this->carpooler_travel) ? $this->carpooler_travel : null;
    }

    public function hasCarpoolerTravel()
    {
        return isset($this->carpooler_travel);
    }

    public function clearCarpoolerTravel()
    {
        unset($this->carpooler_travel);
    }

    /**
     *主卡下方拼友行程卡片
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.CarpoolerTravel carpooler_travel = 12;</code>
     * @param \Dirpc\SDK\PreSale\CarpoolerTravel $var
     * @return $this
     */
    public function setCarpoolerTravel($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\CarpoolerTravel::class);
        $this->carpooler_travel = $var;

        return $this;
    }

    /**
     *预估toast
     *
     * Generated from protobuf field <code>string estimate_loading = 13;</code>
     * @return string
     */
    public function getEstimateLoading()
    {
        return isset($this->estimate_loading) ? $this->estimate_loading : '';
    }

    public function hasEstimateLoading()
    {
        return isset($this->estimate_loading);
    }

    public function clearEstimateLoading()
    {
        unset($this->estimate_loading);
    }

    /**
     *预估toast
     *
     * Generated from protobuf field <code>string estimate_loading = 13;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateLoading($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_loading = $var;

        return $this;
    }

    /**
     *路径跳转
     *
     * Generated from protobuf field <code>string jump_url = 14;</code>
     * @return string
     */
    public function getJumpUrl()
    {
        return isset($this->jump_url) ? $this->jump_url : '';
    }

    public function hasJumpUrl()
    {
        return isset($this->jump_url);
    }

    public function clearJumpUrl()
    {
        unset($this->jump_url);
    }

    /**
     *路径跳转
     *
     * Generated from protobuf field <code>string jump_url = 14;</code>
     * @param string $var
     * @return $this
     */
    public function setJumpUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->jump_url = $var;

        return $this;
    }

}

