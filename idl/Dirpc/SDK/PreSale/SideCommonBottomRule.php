<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideCommonBottomRule</code>
 */
class SideCommonBottomRule extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *多个品类的rule_type不相同
     *
     * Generated from protobuf field <code>map<string, string> multi_scene_text_v2 = 2;</code>
     */
    private $multi_scene_text_v2;
    /**
     *多个品类的rule_type相同
     *
     * Generated from protobuf field <code>map<string, string> single_scene_text = 3;</code>
     */
    private $single_scene_text;
    /**
     *多个品类的rule_type相同
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.MultiRuleConf> multi_scene_conf = 4;</code>
     */
    private $multi_scene_conf;
    /**
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.MultiRuleConf> single_scene_conf = 5;</code>
     */
    private $single_scene_conf;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type array|\Nuwa\Protobuf\Internal\MapField $multi_scene_text_v2
     *          多个品类的rule_type不相同
     *     @type array|\Nuwa\Protobuf\Internal\MapField $single_scene_text
     *          多个品类的rule_type相同
     *     @type array|\Nuwa\Protobuf\Internal\MapField $multi_scene_conf
     *          多个品类的rule_type相同
     *     @type array|\Nuwa\Protobuf\Internal\MapField $single_scene_conf
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     *多个品类的rule_type不相同
     *
     * Generated from protobuf field <code>map<string, string> multi_scene_text_v2 = 2;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getMultiSceneTextV2()
    {
        return $this->multi_scene_text_v2;
    }

    /**
     *多个品类的rule_type不相同
     *
     * Generated from protobuf field <code>map<string, string> multi_scene_text_v2 = 2;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setMultiSceneTextV2($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->multi_scene_text_v2 = $arr;

        return $this;
    }

    /**
     *多个品类的rule_type相同
     *
     * Generated from protobuf field <code>map<string, string> single_scene_text = 3;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getSingleSceneText()
    {
        return $this->single_scene_text;
    }

    /**
     *多个品类的rule_type相同
     *
     * Generated from protobuf field <code>map<string, string> single_scene_text = 3;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setSingleSceneText($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->single_scene_text = $arr;

        return $this;
    }

    /**
     *多个品类的rule_type相同
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.MultiRuleConf> multi_scene_conf = 4;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getMultiSceneConf()
    {
        return $this->multi_scene_conf;
    }

    /**
     *多个品类的rule_type相同
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.MultiRuleConf> multi_scene_conf = 4;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setMultiSceneConf($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\MultiRuleConf::class);
        $this->multi_scene_conf = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.MultiRuleConf> single_scene_conf = 5;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getSingleSceneConf()
    {
        return $this->single_scene_conf;
    }

    /**
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.MultiRuleConf> single_scene_conf = 5;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setSingleSceneConf($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\MultiRuleConf::class);
        $this->single_scene_conf = $arr;

        return $this;
    }

}

