<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.CustomData</code>
 */
class CustomData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *个性化服务唯一id
     *
     * Generated from protobuf field <code>int32 id = 1;</code>
     */
    protected $id = 0;
    /**
     *互斥服务id
     *
     * Generated from protobuf field <code>repeated int32 mutex_ids = 2;</code>
     */
    private $mutex_ids;
    /**
     *个性化服务数量限制
     *
     * Generated from protobuf field <code>int32 max = 3;</code>
     */
    protected $max = 0;
    /**
     *title
     *
     * Generated from protobuf field <code>string title = 4;</code>
     */
    protected $title = '';
    /**
     *摘要，如"限时0元"
     *
     * Generated from protobuf field <code>string desc = 5;</code>
     */
    protected $desc = null;
    /**
     *账单计价
     *
     * Generated from protobuf field <code>string price = 6;</code>
     */
    protected $price = null;
    /**
     *原价，固定配置，端上用于删除线展示
     *
     * Generated from protobuf field <code>string origin_price = 7;</code>
     */
    protected $origin_price = '';
    /**
     *单位，如："人"或"次"
     *
     * Generated from protobuf field <code>string unit = 8;</code>
     */
    protected $unit = '';
    /**
     *已确认的人数或次数，默认为1人或1次，但不代表用户已勾选此服务
     *
     * Generated from protobuf field <code>string num = 9;</code>
     */
    protected $num = '';
    /**
     *已选择的人数/次数，默认为0
     *
     * Generated from protobuf field <code>int32 selected_count = 10;</code>
     */
    protected $selected_count = 0;
    /**
     *详情
     *
     * Generated from protobuf field <code>string detail = 11;</code>
     */
    protected $detail = null;
    /**
     *状态，1-服务正常，2-服务不可选，点击出tips
     *
     * Generated from protobuf field <code>int32 status = 12;</code>
     */
    protected $status = 0;
    /**
     *提示
     *
     * Generated from protobuf field <code>string tips = 13;</code>
     */
    protected $tips = null;
    /**
     *图标
     *
     * Generated from protobuf field <code>string icon = 14;</code>
     */
    protected $icon = null;
    /**
     *选择数量的标题
     *
     * Generated from protobuf field <code>string num_selector_title = 15;</code>
     */
    protected $num_selector_title = null;
    /**
     *服务数量选择的副标题
     *
     * Generated from protobuf field <code>string num_selector_subtitle = 16;</code>
     */
    protected $num_selector_subtitle = null;
    /**
     *个性化服务弹窗，如新手引导弹窗
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ServicePopup popup = 17;</code>
     */
    protected $popup = null;
    /**
     *现价的单价
     *
     * Generated from protobuf field <code>string unit_price = 18;</code>
     */
    protected $unit_price = null;
    /**
     *原价的单价
     *
     * Generated from protobuf field <code>string origin_unit_price = 19;</code>
     */
    protected $origin_unit_price = null;
    /**
     *原价的单价
     *
     * Generated from protobuf field <code>string light_icon = 20;</code>
     */
    protected $light_icon = '';
    /**
     * Generated from protobuf field <code>string product_id = 21;</code>
     */
    protected $product_id = '';
    /**
     * Generated from protobuf field <code>string service_id = 22;</code>
     */
    protected $service_id = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $id
     *          个性化服务唯一id
     *     @type int[]|\Nuwa\Protobuf\Internal\RepeatedField $mutex_ids
     *          互斥服务id
     *     @type int $max
     *          个性化服务数量限制
     *     @type string $title
     *          title
     *     @type string $desc
     *          摘要，如"限时0元"
     *     @type string $price
     *          账单计价
     *     @type string $origin_price
     *          原价，固定配置，端上用于删除线展示
     *     @type string $unit
     *          单位，如："人"或"次"
     *     @type string $num
     *          已确认的人数或次数，默认为1人或1次，但不代表用户已勾选此服务
     *     @type int $selected_count
     *          已选择的人数/次数，默认为0
     *     @type string $detail
     *          详情
     *     @type int $status
     *          状态，1-服务正常，2-服务不可选，点击出tips
     *     @type string $tips
     *          提示
     *     @type string $icon
     *          图标
     *     @type string $num_selector_title
     *          选择数量的标题
     *     @type string $num_selector_subtitle
     *          服务数量选择的副标题
     *     @type \Dirpc\SDK\PreSale\ServicePopup $popup
     *          个性化服务弹窗，如新手引导弹窗
     *     @type string $unit_price
     *          现价的单价
     *     @type string $origin_unit_price
     *          原价的单价
     *     @type string $light_icon
     *          原价的单价
     *     @type string $product_id
     *     @type string $service_id
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *个性化服务唯一id
     *
     * Generated from protobuf field <code>int32 id = 1;</code>
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     *个性化服务唯一id
     *
     * Generated from protobuf field <code>int32 id = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setId($var)
    {
        GPBUtil::checkInt32($var);
        $this->id = $var;

        return $this;
    }

    /**
     *互斥服务id
     *
     * Generated from protobuf field <code>repeated int32 mutex_ids = 2;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getMutexIds()
    {
        return $this->mutex_ids;
    }

    /**
     *互斥服务id
     *
     * Generated from protobuf field <code>repeated int32 mutex_ids = 2;</code>
     * @param int[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setMutexIds($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::INT32);
        $this->mutex_ids = $arr;

        return $this;
    }

    /**
     *个性化服务数量限制
     *
     * Generated from protobuf field <code>int32 max = 3;</code>
     * @return int
     */
    public function getMax()
    {
        return $this->max;
    }

    /**
     *个性化服务数量限制
     *
     * Generated from protobuf field <code>int32 max = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setMax($var)
    {
        GPBUtil::checkInt32($var);
        $this->max = $var;

        return $this;
    }

    /**
     *title
     *
     * Generated from protobuf field <code>string title = 4;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     *title
     *
     * Generated from protobuf field <code>string title = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     *摘要，如"限时0元"
     *
     * Generated from protobuf field <code>string desc = 5;</code>
     * @return string
     */
    public function getDesc()
    {
        return isset($this->desc) ? $this->desc : '';
    }

    public function hasDesc()
    {
        return isset($this->desc);
    }

    public function clearDesc()
    {
        unset($this->desc);
    }

    /**
     *摘要，如"限时0元"
     *
     * Generated from protobuf field <code>string desc = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->desc = $var;

        return $this;
    }

    /**
     *账单计价
     *
     * Generated from protobuf field <code>string price = 6;</code>
     * @return string
     */
    public function getPrice()
    {
        return isset($this->price) ? $this->price : '';
    }

    public function hasPrice()
    {
        return isset($this->price);
    }

    public function clearPrice()
    {
        unset($this->price);
    }

    /**
     *账单计价
     *
     * Generated from protobuf field <code>string price = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setPrice($var)
    {
        GPBUtil::checkString($var, True);
        $this->price = $var;

        return $this;
    }

    /**
     *原价，固定配置，端上用于删除线展示
     *
     * Generated from protobuf field <code>string origin_price = 7;</code>
     * @return string
     */
    public function getOriginPrice()
    {
        return $this->origin_price;
    }

    /**
     *原价，固定配置，端上用于删除线展示
     *
     * Generated from protobuf field <code>string origin_price = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setOriginPrice($var)
    {
        GPBUtil::checkString($var, True);
        $this->origin_price = $var;

        return $this;
    }

    /**
     *单位，如："人"或"次"
     *
     * Generated from protobuf field <code>string unit = 8;</code>
     * @return string
     */
    public function getUnit()
    {
        return $this->unit;
    }

    /**
     *单位，如："人"或"次"
     *
     * Generated from protobuf field <code>string unit = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setUnit($var)
    {
        GPBUtil::checkString($var, True);
        $this->unit = $var;

        return $this;
    }

    /**
     *已确认的人数或次数，默认为1人或1次，但不代表用户已勾选此服务
     *
     * Generated from protobuf field <code>string num = 9;</code>
     * @return string
     */
    public function getNum()
    {
        return $this->num;
    }

    /**
     *已确认的人数或次数，默认为1人或1次，但不代表用户已勾选此服务
     *
     * Generated from protobuf field <code>string num = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setNum($var)
    {
        GPBUtil::checkString($var, True);
        $this->num = $var;

        return $this;
    }

    /**
     *已选择的人数/次数，默认为0
     *
     * Generated from protobuf field <code>int32 selected_count = 10;</code>
     * @return int
     */
    public function getSelectedCount()
    {
        return $this->selected_count;
    }

    /**
     *已选择的人数/次数，默认为0
     *
     * Generated from protobuf field <code>int32 selected_count = 10;</code>
     * @param int $var
     * @return $this
     */
    public function setSelectedCount($var)
    {
        GPBUtil::checkInt32($var);
        $this->selected_count = $var;

        return $this;
    }

    /**
     *详情
     *
     * Generated from protobuf field <code>string detail = 11;</code>
     * @return string
     */
    public function getDetail()
    {
        return isset($this->detail) ? $this->detail : '';
    }

    public function hasDetail()
    {
        return isset($this->detail);
    }

    public function clearDetail()
    {
        unset($this->detail);
    }

    /**
     *详情
     *
     * Generated from protobuf field <code>string detail = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setDetail($var)
    {
        GPBUtil::checkString($var, True);
        $this->detail = $var;

        return $this;
    }

    /**
     *状态，1-服务正常，2-服务不可选，点击出tips
     *
     * Generated from protobuf field <code>int32 status = 12;</code>
     * @return int
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     *状态，1-服务正常，2-服务不可选，点击出tips
     *
     * Generated from protobuf field <code>int32 status = 12;</code>
     * @param int $var
     * @return $this
     */
    public function setStatus($var)
    {
        GPBUtil::checkInt32($var);
        $this->status = $var;

        return $this;
    }

    /**
     *提示
     *
     * Generated from protobuf field <code>string tips = 13;</code>
     * @return string
     */
    public function getTips()
    {
        return isset($this->tips) ? $this->tips : '';
    }

    public function hasTips()
    {
        return isset($this->tips);
    }

    public function clearTips()
    {
        unset($this->tips);
    }

    /**
     *提示
     *
     * Generated from protobuf field <code>string tips = 13;</code>
     * @param string $var
     * @return $this
     */
    public function setTips($var)
    {
        GPBUtil::checkString($var, True);
        $this->tips = $var;

        return $this;
    }

    /**
     *图标
     *
     * Generated from protobuf field <code>string icon = 14;</code>
     * @return string
     */
    public function getIcon()
    {
        return isset($this->icon) ? $this->icon : '';
    }

    public function hasIcon()
    {
        return isset($this->icon);
    }

    public function clearIcon()
    {
        unset($this->icon);
    }

    /**
     *图标
     *
     * Generated from protobuf field <code>string icon = 14;</code>
     * @param string $var
     * @return $this
     */
    public function setIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->icon = $var;

        return $this;
    }

    /**
     *选择数量的标题
     *
     * Generated from protobuf field <code>string num_selector_title = 15;</code>
     * @return string
     */
    public function getNumSelectorTitle()
    {
        return isset($this->num_selector_title) ? $this->num_selector_title : '';
    }

    public function hasNumSelectorTitle()
    {
        return isset($this->num_selector_title);
    }

    public function clearNumSelectorTitle()
    {
        unset($this->num_selector_title);
    }

    /**
     *选择数量的标题
     *
     * Generated from protobuf field <code>string num_selector_title = 15;</code>
     * @param string $var
     * @return $this
     */
    public function setNumSelectorTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->num_selector_title = $var;

        return $this;
    }

    /**
     *服务数量选择的副标题
     *
     * Generated from protobuf field <code>string num_selector_subtitle = 16;</code>
     * @return string
     */
    public function getNumSelectorSubtitle()
    {
        return isset($this->num_selector_subtitle) ? $this->num_selector_subtitle : '';
    }

    public function hasNumSelectorSubtitle()
    {
        return isset($this->num_selector_subtitle);
    }

    public function clearNumSelectorSubtitle()
    {
        unset($this->num_selector_subtitle);
    }

    /**
     *服务数量选择的副标题
     *
     * Generated from protobuf field <code>string num_selector_subtitle = 16;</code>
     * @param string $var
     * @return $this
     */
    public function setNumSelectorSubtitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->num_selector_subtitle = $var;

        return $this;
    }

    /**
     *个性化服务弹窗，如新手引导弹窗
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ServicePopup popup = 17;</code>
     * @return \Dirpc\SDK\PreSale\ServicePopup
     */
    public function getPopup()
    {
        return isset($this->popup) ? $this->popup : null;
    }

    public function hasPopup()
    {
        return isset($this->popup);
    }

    public function clearPopup()
    {
        unset($this->popup);
    }

    /**
     *个性化服务弹窗，如新手引导弹窗
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ServicePopup popup = 17;</code>
     * @param \Dirpc\SDK\PreSale\ServicePopup $var
     * @return $this
     */
    public function setPopup($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\ServicePopup::class);
        $this->popup = $var;

        return $this;
    }

    /**
     *现价的单价
     *
     * Generated from protobuf field <code>string unit_price = 18;</code>
     * @return string
     */
    public function getUnitPrice()
    {
        return isset($this->unit_price) ? $this->unit_price : '';
    }

    public function hasUnitPrice()
    {
        return isset($this->unit_price);
    }

    public function clearUnitPrice()
    {
        unset($this->unit_price);
    }

    /**
     *现价的单价
     *
     * Generated from protobuf field <code>string unit_price = 18;</code>
     * @param string $var
     * @return $this
     */
    public function setUnitPrice($var)
    {
        GPBUtil::checkString($var, True);
        $this->unit_price = $var;

        return $this;
    }

    /**
     *原价的单价
     *
     * Generated from protobuf field <code>string origin_unit_price = 19;</code>
     * @return string
     */
    public function getOriginUnitPrice()
    {
        return isset($this->origin_unit_price) ? $this->origin_unit_price : '';
    }

    public function hasOriginUnitPrice()
    {
        return isset($this->origin_unit_price);
    }

    public function clearOriginUnitPrice()
    {
        unset($this->origin_unit_price);
    }

    /**
     *原价的单价
     *
     * Generated from protobuf field <code>string origin_unit_price = 19;</code>
     * @param string $var
     * @return $this
     */
    public function setOriginUnitPrice($var)
    {
        GPBUtil::checkString($var, True);
        $this->origin_unit_price = $var;

        return $this;
    }

    /**
     *原价的单价
     *
     * Generated from protobuf field <code>string light_icon = 20;</code>
     * @return string
     */
    public function getLightIcon()
    {
        return $this->light_icon;
    }

    /**
     *原价的单价
     *
     * Generated from protobuf field <code>string light_icon = 20;</code>
     * @param string $var
     * @return $this
     */
    public function setLightIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->light_icon = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string product_id = 21;</code>
     * @return string
     */
    public function getProductId()
    {
        return $this->product_id;
    }

    /**
     * Generated from protobuf field <code>string product_id = 21;</code>
     * @param string $var
     * @return $this
     */
    public function setProductId($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string service_id = 22;</code>
     * @return string
     */
    public function getServiceId()
    {
        return $this->service_id;
    }

    /**
     * Generated from protobuf field <code>string service_id = 22;</code>
     * @param string $var
     * @return $this
     */
    public function setServiceId($var)
    {
        GPBUtil::checkString($var, True);
        $this->service_id = $var;

        return $this;
    }

}

