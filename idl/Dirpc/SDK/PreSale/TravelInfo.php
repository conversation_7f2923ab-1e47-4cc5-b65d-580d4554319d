<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: carpool.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.TravelInfo</code>
 */
class TravelInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *卡片标题
     *
     * Generated from protobuf field <code>string title = 1;</code>
     */
    protected $title = null;
    /**
     *是否置灰, 1-置灰
     *
     * Generated from protobuf field <code>int32 zero_saturate = 2;</code>
     */
    protected $zero_saturate = null;
    /**
     *头像
     *
     * Generated from protobuf field <code>string head_icon = 3;</code>
     */
    protected $head_icon = null;
    /**
     *出发时间文案
     *
     * Generated from protobuf field <code>string departure_text = 4;</code>
     */
    protected $departure_text = null;
    /**
     *座位数信息
     *
     * Generated from protobuf field <code>string seat_info = 5;</code>
     */
    protected $seat_info = null;
    /**
     *起点名称
     *
     * Generated from protobuf field <code>string start_name = 6;</code>
     */
    protected $start_name = null;
    /**
     *终点名称
     *
     * Generated from protobuf field <code>string dest_name = 7;</code>
     */
    protected $dest_name = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $title
     *          卡片标题
     *     @type int $zero_saturate
     *          是否置灰, 1-置灰
     *     @type string $head_icon
     *          头像
     *     @type string $departure_text
     *          出发时间文案
     *     @type string $seat_info
     *          座位数信息
     *     @type string $start_name
     *          起点名称
     *     @type string $dest_name
     *          终点名称
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\Carpool::initOnce();
        parent::__construct($data);
    }

    /**
     *卡片标题
     *
     * Generated from protobuf field <code>string title = 1;</code>
     * @return string
     */
    public function getTitle()
    {
        return isset($this->title) ? $this->title : '';
    }

    public function hasTitle()
    {
        return isset($this->title);
    }

    public function clearTitle()
    {
        unset($this->title);
    }

    /**
     *卡片标题
     *
     * Generated from protobuf field <code>string title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     *是否置灰, 1-置灰
     *
     * Generated from protobuf field <code>int32 zero_saturate = 2;</code>
     * @return int
     */
    public function getZeroSaturate()
    {
        return isset($this->zero_saturate) ? $this->zero_saturate : 0;
    }

    public function hasZeroSaturate()
    {
        return isset($this->zero_saturate);
    }

    public function clearZeroSaturate()
    {
        unset($this->zero_saturate);
    }

    /**
     *是否置灰, 1-置灰
     *
     * Generated from protobuf field <code>int32 zero_saturate = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setZeroSaturate($var)
    {
        GPBUtil::checkInt32($var);
        $this->zero_saturate = $var;

        return $this;
    }

    /**
     *头像
     *
     * Generated from protobuf field <code>string head_icon = 3;</code>
     * @return string
     */
    public function getHeadIcon()
    {
        return isset($this->head_icon) ? $this->head_icon : '';
    }

    public function hasHeadIcon()
    {
        return isset($this->head_icon);
    }

    public function clearHeadIcon()
    {
        unset($this->head_icon);
    }

    /**
     *头像
     *
     * Generated from protobuf field <code>string head_icon = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setHeadIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->head_icon = $var;

        return $this;
    }

    /**
     *出发时间文案
     *
     * Generated from protobuf field <code>string departure_text = 4;</code>
     * @return string
     */
    public function getDepartureText()
    {
        return isset($this->departure_text) ? $this->departure_text : '';
    }

    public function hasDepartureText()
    {
        return isset($this->departure_text);
    }

    public function clearDepartureText()
    {
        unset($this->departure_text);
    }

    /**
     *出发时间文案
     *
     * Generated from protobuf field <code>string departure_text = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setDepartureText($var)
    {
        GPBUtil::checkString($var, True);
        $this->departure_text = $var;

        return $this;
    }

    /**
     *座位数信息
     *
     * Generated from protobuf field <code>string seat_info = 5;</code>
     * @return string
     */
    public function getSeatInfo()
    {
        return isset($this->seat_info) ? $this->seat_info : '';
    }

    public function hasSeatInfo()
    {
        return isset($this->seat_info);
    }

    public function clearSeatInfo()
    {
        unset($this->seat_info);
    }

    /**
     *座位数信息
     *
     * Generated from protobuf field <code>string seat_info = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setSeatInfo($var)
    {
        GPBUtil::checkString($var, True);
        $this->seat_info = $var;

        return $this;
    }

    /**
     *起点名称
     *
     * Generated from protobuf field <code>string start_name = 6;</code>
     * @return string
     */
    public function getStartName()
    {
        return isset($this->start_name) ? $this->start_name : '';
    }

    public function hasStartName()
    {
        return isset($this->start_name);
    }

    public function clearStartName()
    {
        unset($this->start_name);
    }

    /**
     *起点名称
     *
     * Generated from protobuf field <code>string start_name = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setStartName($var)
    {
        GPBUtil::checkString($var, True);
        $this->start_name = $var;

        return $this;
    }

    /**
     *终点名称
     *
     * Generated from protobuf field <code>string dest_name = 7;</code>
     * @return string
     */
    public function getDestName()
    {
        return isset($this->dest_name) ? $this->dest_name : '';
    }

    public function hasDestName()
    {
        return isset($this->dest_name);
    }

    public function clearDestName()
    {
        unset($this->dest_name);
    }

    /**
     *终点名称
     *
     * Generated from protobuf field <code>string dest_name = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setDestName($var)
    {
        GPBUtil::checkString($var, True);
        $this->dest_name = $var;

        return $this;
    }

}

