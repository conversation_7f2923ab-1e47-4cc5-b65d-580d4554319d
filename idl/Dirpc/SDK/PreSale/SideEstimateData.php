<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideEstimateData</code>
 */
class SideEstimateData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *顶部沟通激励组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.TopRule top_rule = 3;</code>
     */
    protected $top_rule = null;
    /**
     *底部价格沟通+支付沟通
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BottomRule bottom_rule = 4;</code>
     */
    protected $bottom_rule = null;
    /**
     *表单渲染
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.SideBubble> form_data = 5;</code>
     */
    private $form_data;
    /**
     *拦截弹窗
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideInterceptData intercept_data = 6;</code>
     */
    protected $intercept_data = null;
    /**
     *引导弹窗
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideGuidePopupData guide_popup_data = 7;</code>
     */
    protected $guide_popup_data = null;
    /**
     *tab信息
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.EstimateTab> estimate_tab = 8;</code>
     */
    private $estimate_tab;
    /**
     *导流条
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideEstGuideBar guide_bar = 9;</code>
     */
    protected $guide_bar = null;
    /**
     *8.0 tab修正,气泡
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.EstimateTab> one_stop_tab = 10;</code>
     */
    private $one_stop_tab;
    /**
     *半弹层 已下线 deprecated
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectLayer eject_layer = 11;</code>
     */
    protected $eject_layer = null;
    /**
     *半弹层新
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectLayerInfo eject_layer_info = 12;</code>
     */
    protected $eject_layer_info = null;
    /**
     *协议弹窗
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.AgreementPopup agreement_popup = 13;</code>
     */
    protected $agreement_popup = null;
    /**
     *用户引导
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.UserGuideInfo user_guide_info = 14;</code>
     */
    protected $user_guide_info = null;
    /**
     *盒子是否出气泡
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.LayoutData> layout_data = 15;</code>
     */
    private $layout_data;
    /**
     *底部弹窗拦截
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BottomInterceptData bottom_intercept_data = 16;</code>
     */
    protected $bottom_intercept_data = null;
    /**
     *呼返膨胀弹窗
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ExpandPopup expand_popup = 17;</code>
     */
    protected $expand_popup = null;
    /**
     *按钮处沟通组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ButtonLeftRule button_left_rule = 18;</code>
     */
    protected $button_left_rule = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\TopRule $top_rule
     *          顶部沟通激励组件
     *     @type \Dirpc\SDK\PreSale\BottomRule $bottom_rule
     *          底部价格沟通+支付沟通
     *     @type array|\Nuwa\Protobuf\Internal\MapField $form_data
     *          表单渲染
     *     @type \Dirpc\SDK\PreSale\SideInterceptData $intercept_data
     *          拦截弹窗
     *     @type \Dirpc\SDK\PreSale\SideGuidePopupData $guide_popup_data
     *          引导弹窗
     *     @type array|\Nuwa\Protobuf\Internal\MapField $estimate_tab
     *          tab信息
     *     @type \Dirpc\SDK\PreSale\SideEstGuideBar $guide_bar
     *          导流条
     *     @type array|\Nuwa\Protobuf\Internal\MapField $one_stop_tab
     *          8.0 tab修正,气泡
     *     @type \Dirpc\SDK\PreSale\EjectLayer $eject_layer
     *          半弹层 已下线 deprecated
     *     @type \Dirpc\SDK\PreSale\EjectLayerInfo $eject_layer_info
     *          半弹层新
     *     @type \Dirpc\SDK\PreSale\AgreementPopup $agreement_popup
     *          协议弹窗
     *     @type \Dirpc\SDK\PreSale\UserGuideInfo $user_guide_info
     *          用户引导
     *     @type array|\Nuwa\Protobuf\Internal\MapField $layout_data
     *          盒子是否出气泡
     *     @type \Dirpc\SDK\PreSale\BottomInterceptData $bottom_intercept_data
     *          底部弹窗拦截
     *     @type \Dirpc\SDK\PreSale\ExpandPopup $expand_popup
     *          呼返膨胀弹窗
     *     @type \Dirpc\SDK\PreSale\ButtonLeftRule $button_left_rule
     *          按钮处沟通组件
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     *顶部沟通激励组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.TopRule top_rule = 3;</code>
     * @return \Dirpc\SDK\PreSale\TopRule
     */
    public function getTopRule()
    {
        return isset($this->top_rule) ? $this->top_rule : null;
    }

    public function hasTopRule()
    {
        return isset($this->top_rule);
    }

    public function clearTopRule()
    {
        unset($this->top_rule);
    }

    /**
     *顶部沟通激励组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.TopRule top_rule = 3;</code>
     * @param \Dirpc\SDK\PreSale\TopRule $var
     * @return $this
     */
    public function setTopRule($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\TopRule::class);
        $this->top_rule = $var;

        return $this;
    }

    /**
     *底部价格沟通+支付沟通
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BottomRule bottom_rule = 4;</code>
     * @return \Dirpc\SDK\PreSale\BottomRule
     */
    public function getBottomRule()
    {
        return isset($this->bottom_rule) ? $this->bottom_rule : null;
    }

    public function hasBottomRule()
    {
        return isset($this->bottom_rule);
    }

    public function clearBottomRule()
    {
        unset($this->bottom_rule);
    }

    /**
     *底部价格沟通+支付沟通
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BottomRule bottom_rule = 4;</code>
     * @param \Dirpc\SDK\PreSale\BottomRule $var
     * @return $this
     */
    public function setBottomRule($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\BottomRule::class);
        $this->bottom_rule = $var;

        return $this;
    }

    /**
     *表单渲染
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.SideBubble> form_data = 5;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getFormData()
    {
        return $this->form_data;
    }

    /**
     *表单渲染
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.SideBubble> form_data = 5;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setFormData($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\SideBubble::class);
        $this->form_data = $arr;

        return $this;
    }

    /**
     *拦截弹窗
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideInterceptData intercept_data = 6;</code>
     * @return \Dirpc\SDK\PreSale\SideInterceptData
     */
    public function getInterceptData()
    {
        return isset($this->intercept_data) ? $this->intercept_data : null;
    }

    public function hasInterceptData()
    {
        return isset($this->intercept_data);
    }

    public function clearInterceptData()
    {
        unset($this->intercept_data);
    }

    /**
     *拦截弹窗
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideInterceptData intercept_data = 6;</code>
     * @param \Dirpc\SDK\PreSale\SideInterceptData $var
     * @return $this
     */
    public function setInterceptData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\SideInterceptData::class);
        $this->intercept_data = $var;

        return $this;
    }

    /**
     *引导弹窗
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideGuidePopupData guide_popup_data = 7;</code>
     * @return \Dirpc\SDK\PreSale\SideGuidePopupData
     */
    public function getGuidePopupData()
    {
        return isset($this->guide_popup_data) ? $this->guide_popup_data : null;
    }

    public function hasGuidePopupData()
    {
        return isset($this->guide_popup_data);
    }

    public function clearGuidePopupData()
    {
        unset($this->guide_popup_data);
    }

    /**
     *引导弹窗
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideGuidePopupData guide_popup_data = 7;</code>
     * @param \Dirpc\SDK\PreSale\SideGuidePopupData $var
     * @return $this
     */
    public function setGuidePopupData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\SideGuidePopupData::class);
        $this->guide_popup_data = $var;

        return $this;
    }

    /**
     *tab信息
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.EstimateTab> estimate_tab = 8;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getEstimateTab()
    {
        return $this->estimate_tab;
    }

    /**
     *tab信息
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.EstimateTab> estimate_tab = 8;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setEstimateTab($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\EstimateTab::class);
        $this->estimate_tab = $arr;

        return $this;
    }

    /**
     *导流条
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideEstGuideBar guide_bar = 9;</code>
     * @return \Dirpc\SDK\PreSale\SideEstGuideBar
     */
    public function getGuideBar()
    {
        return isset($this->guide_bar) ? $this->guide_bar : null;
    }

    public function hasGuideBar()
    {
        return isset($this->guide_bar);
    }

    public function clearGuideBar()
    {
        unset($this->guide_bar);
    }

    /**
     *导流条
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideEstGuideBar guide_bar = 9;</code>
     * @param \Dirpc\SDK\PreSale\SideEstGuideBar $var
     * @return $this
     */
    public function setGuideBar($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\SideEstGuideBar::class);
        $this->guide_bar = $var;

        return $this;
    }

    /**
     *8.0 tab修正,气泡
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.EstimateTab> one_stop_tab = 10;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getOneStopTab()
    {
        return $this->one_stop_tab;
    }

    /**
     *8.0 tab修正,气泡
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.EstimateTab> one_stop_tab = 10;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setOneStopTab($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\EstimateTab::class);
        $this->one_stop_tab = $arr;

        return $this;
    }

    /**
     *半弹层 已下线 deprecated
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectLayer eject_layer = 11;</code>
     * @return \Dirpc\SDK\PreSale\EjectLayer
     */
    public function getEjectLayer()
    {
        return isset($this->eject_layer) ? $this->eject_layer : null;
    }

    public function hasEjectLayer()
    {
        return isset($this->eject_layer);
    }

    public function clearEjectLayer()
    {
        unset($this->eject_layer);
    }

    /**
     *半弹层 已下线 deprecated
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectLayer eject_layer = 11;</code>
     * @param \Dirpc\SDK\PreSale\EjectLayer $var
     * @return $this
     */
    public function setEjectLayer($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\EjectLayer::class);
        $this->eject_layer = $var;

        return $this;
    }

    /**
     *半弹层新
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectLayerInfo eject_layer_info = 12;</code>
     * @return \Dirpc\SDK\PreSale\EjectLayerInfo
     */
    public function getEjectLayerInfo()
    {
        return isset($this->eject_layer_info) ? $this->eject_layer_info : null;
    }

    public function hasEjectLayerInfo()
    {
        return isset($this->eject_layer_info);
    }

    public function clearEjectLayerInfo()
    {
        unset($this->eject_layer_info);
    }

    /**
     *半弹层新
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectLayerInfo eject_layer_info = 12;</code>
     * @param \Dirpc\SDK\PreSale\EjectLayerInfo $var
     * @return $this
     */
    public function setEjectLayerInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\EjectLayerInfo::class);
        $this->eject_layer_info = $var;

        return $this;
    }

    /**
     *协议弹窗
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.AgreementPopup agreement_popup = 13;</code>
     * @return \Dirpc\SDK\PreSale\AgreementPopup
     */
    public function getAgreementPopup()
    {
        return isset($this->agreement_popup) ? $this->agreement_popup : null;
    }

    public function hasAgreementPopup()
    {
        return isset($this->agreement_popup);
    }

    public function clearAgreementPopup()
    {
        unset($this->agreement_popup);
    }

    /**
     *协议弹窗
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.AgreementPopup agreement_popup = 13;</code>
     * @param \Dirpc\SDK\PreSale\AgreementPopup $var
     * @return $this
     */
    public function setAgreementPopup($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\AgreementPopup::class);
        $this->agreement_popup = $var;

        return $this;
    }

    /**
     *用户引导
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.UserGuideInfo user_guide_info = 14;</code>
     * @return \Dirpc\SDK\PreSale\UserGuideInfo
     */
    public function getUserGuideInfo()
    {
        return isset($this->user_guide_info) ? $this->user_guide_info : null;
    }

    public function hasUserGuideInfo()
    {
        return isset($this->user_guide_info);
    }

    public function clearUserGuideInfo()
    {
        unset($this->user_guide_info);
    }

    /**
     *用户引导
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.UserGuideInfo user_guide_info = 14;</code>
     * @param \Dirpc\SDK\PreSale\UserGuideInfo $var
     * @return $this
     */
    public function setUserGuideInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\UserGuideInfo::class);
        $this->user_guide_info = $var;

        return $this;
    }

    /**
     *盒子是否出气泡
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.LayoutData> layout_data = 15;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getLayoutData()
    {
        return $this->layout_data;
    }

    /**
     *盒子是否出气泡
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.LayoutData> layout_data = 15;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setLayoutData($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\LayoutData::class);
        $this->layout_data = $arr;

        return $this;
    }

    /**
     *底部弹窗拦截
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BottomInterceptData bottom_intercept_data = 16;</code>
     * @return \Dirpc\SDK\PreSale\BottomInterceptData
     */
    public function getBottomInterceptData()
    {
        return isset($this->bottom_intercept_data) ? $this->bottom_intercept_data : null;
    }

    public function hasBottomInterceptData()
    {
        return isset($this->bottom_intercept_data);
    }

    public function clearBottomInterceptData()
    {
        unset($this->bottom_intercept_data);
    }

    /**
     *底部弹窗拦截
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BottomInterceptData bottom_intercept_data = 16;</code>
     * @param \Dirpc\SDK\PreSale\BottomInterceptData $var
     * @return $this
     */
    public function setBottomInterceptData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\BottomInterceptData::class);
        $this->bottom_intercept_data = $var;

        return $this;
    }

    /**
     *呼返膨胀弹窗
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ExpandPopup expand_popup = 17;</code>
     * @return \Dirpc\SDK\PreSale\ExpandPopup
     */
    public function getExpandPopup()
    {
        return isset($this->expand_popup) ? $this->expand_popup : null;
    }

    public function hasExpandPopup()
    {
        return isset($this->expand_popup);
    }

    public function clearExpandPopup()
    {
        unset($this->expand_popup);
    }

    /**
     *呼返膨胀弹窗
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ExpandPopup expand_popup = 17;</code>
     * @param \Dirpc\SDK\PreSale\ExpandPopup $var
     * @return $this
     */
    public function setExpandPopup($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\ExpandPopup::class);
        $this->expand_popup = $var;

        return $this;
    }

    /**
     *按钮处沟通组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ButtonLeftRule button_left_rule = 18;</code>
     * @return \Dirpc\SDK\PreSale\ButtonLeftRule
     */
    public function getButtonLeftRule()
    {
        return isset($this->button_left_rule) ? $this->button_left_rule : null;
    }

    public function hasButtonLeftRule()
    {
        return isset($this->button_left_rule);
    }

    public function clearButtonLeftRule()
    {
        unset($this->button_left_rule);
    }

    /**
     *按钮处沟通组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ButtonLeftRule button_left_rule = 18;</code>
     * @param \Dirpc\SDK\PreSale\ButtonLeftRule $var
     * @return $this
     */
    public function setButtonLeftRule($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\ButtonLeftRule::class);
        $this->button_left_rule = $var;

        return $this;
    }

}

