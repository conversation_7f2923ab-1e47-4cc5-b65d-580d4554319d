<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.CategoryData</code>
 */
class CategoryData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 category_id = 1;</code>
     */
    protected $category_id = 0;
    /**
     * Generated from protobuf field <code>string title = 2;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>string sub_title = 3;</code>
     */
    protected $sub_title = '';
    /**
     * Generated from protobuf field <code>string icon = 4;</code>
     */
    protected $icon = '';
    /**
     * Generated from protobuf field <code>repeated string bg_gradients = 5;</code>
     */
    private $bg_gradients;
    /**
     * Generated from protobuf field <code>int32 is_selected = 6;</code>
     */
    protected $is_selected = 0;
    /**
     *表单上的分组标题
     *
     * Generated from protobuf field <code>string section_title = 7;</code>
     */
    protected $section_title = '';
    /**
     *折叠文案
     *
     * Generated from protobuf field <code>string fold_text = 8;</code>
     */
    protected $fold_text = '';
    /**
     *是否折叠
     *
     * Generated from protobuf field <code>int32 is_fold = 9;</code>
     */
    protected $is_fold = 0;
    /**
     *点击后是否展开
     *
     * Generated from protobuf field <code>int32 click_need_expand = 10;</code>
     */
    protected $click_need_expand = 0;
    /**
     *子标题文案
     *
     * Generated from protobuf field <code>string sub_category_title = 11;</code>
     */
    protected $sub_category_title = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $category_id
     *     @type string $title
     *     @type string $sub_title
     *     @type string $icon
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $bg_gradients
     *     @type int $is_selected
     *     @type string $section_title
     *          表单上的分组标题
     *     @type string $fold_text
     *          折叠文案
     *     @type int $is_fold
     *          是否折叠
     *     @type int $click_need_expand
     *          点击后是否展开
     *     @type string $sub_category_title
     *          子标题文案
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 category_id = 1;</code>
     * @return int
     */
    public function getCategoryId()
    {
        return $this->category_id;
    }

    /**
     * Generated from protobuf field <code>int32 category_id = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setCategoryId($var)
    {
        GPBUtil::checkInt32($var);
        $this->category_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string title = 2;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string sub_title = 3;</code>
     * @return string
     */
    public function getSubTitle()
    {
        return $this->sub_title;
    }

    /**
     * Generated from protobuf field <code>string sub_title = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setSubTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string icon = 4;</code>
     * @return string
     */
    public function getIcon()
    {
        return $this->icon;
    }

    /**
     * Generated from protobuf field <code>string icon = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->icon = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated string bg_gradients = 5;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getBgGradients()
    {
        return $this->bg_gradients;
    }

    /**
     * Generated from protobuf field <code>repeated string bg_gradients = 5;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBgGradients($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->bg_gradients = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 is_selected = 6;</code>
     * @return int
     */
    public function getIsSelected()
    {
        return $this->is_selected;
    }

    /**
     * Generated from protobuf field <code>int32 is_selected = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setIsSelected($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_selected = $var;

        return $this;
    }

    /**
     *表单上的分组标题
     *
     * Generated from protobuf field <code>string section_title = 7;</code>
     * @return string
     */
    public function getSectionTitle()
    {
        return $this->section_title;
    }

    /**
     *表单上的分组标题
     *
     * Generated from protobuf field <code>string section_title = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setSectionTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->section_title = $var;

        return $this;
    }

    /**
     *折叠文案
     *
     * Generated from protobuf field <code>string fold_text = 8;</code>
     * @return string
     */
    public function getFoldText()
    {
        return $this->fold_text;
    }

    /**
     *折叠文案
     *
     * Generated from protobuf field <code>string fold_text = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setFoldText($var)
    {
        GPBUtil::checkString($var, True);
        $this->fold_text = $var;

        return $this;
    }

    /**
     *是否折叠
     *
     * Generated from protobuf field <code>int32 is_fold = 9;</code>
     * @return int
     */
    public function getIsFold()
    {
        return $this->is_fold;
    }

    /**
     *是否折叠
     *
     * Generated from protobuf field <code>int32 is_fold = 9;</code>
     * @param int $var
     * @return $this
     */
    public function setIsFold($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_fold = $var;

        return $this;
    }

    /**
     *点击后是否展开
     *
     * Generated from protobuf field <code>int32 click_need_expand = 10;</code>
     * @return int
     */
    public function getClickNeedExpand()
    {
        return $this->click_need_expand;
    }

    /**
     *点击后是否展开
     *
     * Generated from protobuf field <code>int32 click_need_expand = 10;</code>
     * @param int $var
     * @return $this
     */
    public function setClickNeedExpand($var)
    {
        GPBUtil::checkInt32($var);
        $this->click_need_expand = $var;

        return $this;
    }

    /**
     *子标题文案
     *
     * Generated from protobuf field <code>string sub_category_title = 11;</code>
     * @return string
     */
    public function getSubCategoryTitle()
    {
        return isset($this->sub_category_title) ? $this->sub_category_title : '';
    }

    public function hasSubCategoryTitle()
    {
        return isset($this->sub_category_title);
    }

    public function clearSubCategoryTitle()
    {
        unset($this->sub_category_title);
    }

    /**
     *子标题文案
     *
     * Generated from protobuf field <code>string sub_category_title = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setSubCategoryTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_category_title = $var;

        return $this;
    }

}

