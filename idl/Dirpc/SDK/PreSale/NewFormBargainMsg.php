<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NewFormBargainMsg</code>
 */
class NewFormBargainMsg extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string text = 1;</code>
     */
    protected $text = '';
    /**
     * Generated from protobuf field <code>string guide_path = 2;</code>
     */
    protected $guide_path = '';
    /**
     * Generated from protobuf field <code>string bubble_img_url = 3;</code>
     */
    protected $bubble_img_url = '';
    /**
     * Generated from protobuf field <code>string underline_color = 4;</code>
     */
    protected $underline_color = '';
    /**
     * Generated from protobuf field <code>bool show_plus_and_minus = 5;</code>
     */
    protected $show_plus_and_minus = null;
    /**
     * Generated from protobuf field <code>string fee_msg_prefix = 6;</code>
     */
    protected $fee_msg_prefix = null;
    /**
     * Generated from protobuf field <code>string fee_msg_amount = 7;</code>
     */
    protected $fee_msg_amount = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $text
     *     @type string $guide_path
     *     @type string $bubble_img_url
     *     @type string $underline_color
     *     @type bool $show_plus_and_minus
     *     @type string $fee_msg_prefix
     *     @type string $fee_msg_amount
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string text = 1;</code>
     * @return string
     */
    public function getText()
    {
        return $this->text;
    }

    /**
     * Generated from protobuf field <code>string text = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setText($var)
    {
        GPBUtil::checkString($var, True);
        $this->text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string guide_path = 2;</code>
     * @return string
     */
    public function getGuidePath()
    {
        return $this->guide_path;
    }

    /**
     * Generated from protobuf field <code>string guide_path = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setGuidePath($var)
    {
        GPBUtil::checkString($var, True);
        $this->guide_path = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string bubble_img_url = 3;</code>
     * @return string
     */
    public function getBubbleImgUrl()
    {
        return $this->bubble_img_url;
    }

    /**
     * Generated from protobuf field <code>string bubble_img_url = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setBubbleImgUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->bubble_img_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string underline_color = 4;</code>
     * @return string
     */
    public function getUnderlineColor()
    {
        return $this->underline_color;
    }

    /**
     * Generated from protobuf field <code>string underline_color = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setUnderlineColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->underline_color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool show_plus_and_minus = 5;</code>
     * @return bool
     */
    public function getShowPlusAndMinus()
    {
        return isset($this->show_plus_and_minus) ? $this->show_plus_and_minus : false;
    }

    public function hasShowPlusAndMinus()
    {
        return isset($this->show_plus_and_minus);
    }

    public function clearShowPlusAndMinus()
    {
        unset($this->show_plus_and_minus);
    }

    /**
     * Generated from protobuf field <code>bool show_plus_and_minus = 5;</code>
     * @param bool $var
     * @return $this
     */
    public function setShowPlusAndMinus($var)
    {
        GPBUtil::checkBool($var);
        $this->show_plus_and_minus = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_msg_prefix = 6;</code>
     * @return string
     */
    public function getFeeMsgPrefix()
    {
        return isset($this->fee_msg_prefix) ? $this->fee_msg_prefix : '';
    }

    public function hasFeeMsgPrefix()
    {
        return isset($this->fee_msg_prefix);
    }

    public function clearFeeMsgPrefix()
    {
        unset($this->fee_msg_prefix);
    }

    /**
     * Generated from protobuf field <code>string fee_msg_prefix = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsgPrefix($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg_prefix = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_msg_amount = 7;</code>
     * @return string
     */
    public function getFeeMsgAmount()
    {
        return isset($this->fee_msg_amount) ? $this->fee_msg_amount : '';
    }

    public function hasFeeMsgAmount()
    {
        return isset($this->fee_msg_amount);
    }

    public function clearFeeMsgAmount()
    {
        unset($this->fee_msg_amount);
    }

    /**
     * Generated from protobuf field <code>string fee_msg_amount = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsgAmount($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg_amount = $var;

        return $this;
    }

}

