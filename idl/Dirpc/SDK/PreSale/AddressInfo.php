<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: carpool.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.AddressInfo</code>
 */
class AddressInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.StartAddressData start_address_info = 1;</code>
     */
    protected $start_address_info = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EndAddressData end_address_info = 2;</code>
     */
    protected $end_address_info = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\StartAddressData $start_address_info
     *     @type \Dirpc\SDK\PreSale\EndAddressData $end_address_info
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\Carpool::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.StartAddressData start_address_info = 1;</code>
     * @return \Dirpc\SDK\PreSale\StartAddressData
     */
    public function getStartAddressInfo()
    {
        return isset($this->start_address_info) ? $this->start_address_info : null;
    }

    public function hasStartAddressInfo()
    {
        return isset($this->start_address_info);
    }

    public function clearStartAddressInfo()
    {
        unset($this->start_address_info);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.StartAddressData start_address_info = 1;</code>
     * @param \Dirpc\SDK\PreSale\StartAddressData $var
     * @return $this
     */
    public function setStartAddressInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\StartAddressData::class);
        $this->start_address_info = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EndAddressData end_address_info = 2;</code>
     * @return \Dirpc\SDK\PreSale\EndAddressData
     */
    public function getEndAddressInfo()
    {
        return isset($this->end_address_info) ? $this->end_address_info : null;
    }

    public function hasEndAddressInfo()
    {
        return isset($this->end_address_info);
    }

    public function clearEndAddressInfo()
    {
        unset($this->end_address_info);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EndAddressData end_address_info = 2;</code>
     * @param \Dirpc\SDK\PreSale\EndAddressData $var
     * @return $this
     */
    public function setEndAddressInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\EndAddressData::class);
        $this->end_address_info = $var;

        return $this;
    }

}

