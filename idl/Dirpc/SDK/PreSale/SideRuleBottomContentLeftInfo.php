<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideRuleBottomContentLeftInfo</code>
 */
class SideRuleBottomContentLeftInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *icon
     *
     * Generated from protobuf field <code>string icon = 1;</code>
     */
    protected $icon = '';
    /**
     *文案
     *
     * Generated from protobuf field <code>string text_image = 2;</code>
     */
    protected $text_image = '';
    /**
     *交互样式；0代表无；1代表半弹层；
     *
     * Generated from protobuf field <code>int32 action_type = 3;</code>
     */
    protected $action_type = 0;
    /**
     *弹窗
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleBottomContentLeftPopup popup_info = 4;</code>
     */
    protected $popup_info = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $icon
     *          icon
     *     @type string $text_image
     *          文案
     *     @type int $action_type
     *          交互样式；0代表无；1代表半弹层；
     *     @type \Dirpc\SDK\PreSale\SideRuleBottomContentLeftPopup $popup_info
     *          弹窗
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     *icon
     *
     * Generated from protobuf field <code>string icon = 1;</code>
     * @return string
     */
    public function getIcon()
    {
        return $this->icon;
    }

    /**
     *icon
     *
     * Generated from protobuf field <code>string icon = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->icon = $var;

        return $this;
    }

    /**
     *文案
     *
     * Generated from protobuf field <code>string text_image = 2;</code>
     * @return string
     */
    public function getTextImage()
    {
        return $this->text_image;
    }

    /**
     *文案
     *
     * Generated from protobuf field <code>string text_image = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTextImage($var)
    {
        GPBUtil::checkString($var, True);
        $this->text_image = $var;

        return $this;
    }

    /**
     *交互样式；0代表无；1代表半弹层；
     *
     * Generated from protobuf field <code>int32 action_type = 3;</code>
     * @return int
     */
    public function getActionType()
    {
        return $this->action_type;
    }

    /**
     *交互样式；0代表无；1代表半弹层；
     *
     * Generated from protobuf field <code>int32 action_type = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setActionType($var)
    {
        GPBUtil::checkInt32($var);
        $this->action_type = $var;

        return $this;
    }

    /**
     *弹窗
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleBottomContentLeftPopup popup_info = 4;</code>
     * @return \Dirpc\SDK\PreSale\SideRuleBottomContentLeftPopup
     */
    public function getPopupInfo()
    {
        return isset($this->popup_info) ? $this->popup_info : null;
    }

    public function hasPopupInfo()
    {
        return isset($this->popup_info);
    }

    public function clearPopupInfo()
    {
        unset($this->popup_info);
    }

    /**
     *弹窗
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleBottomContentLeftPopup popup_info = 4;</code>
     * @param \Dirpc\SDK\PreSale\SideRuleBottomContentLeftPopup $var
     * @return $this
     */
    public function setPopupInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\SideRuleBottomContentLeftPopup::class);
        $this->popup_info = $var;

        return $this;
    }

}

