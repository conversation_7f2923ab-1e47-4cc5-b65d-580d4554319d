<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 *价格轴
 *
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.PriceAxle</code>
 */
class PriceAxle extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *价格轴预期文案
     *
     * Generated from protobuf field <code>string price_axle_title = 1;</code>
     */
    protected $price_axle_title = null;
    /**
     *价格轴类型
     *
     * Generated from protobuf field <code>int32 type = 2;</code>
     */
    protected $type = null;
    /**
     *价格轴左边界
     *
     * Generated from protobuf field <code>int32 left_boundary = 3;</code>
     */
    protected $left_boundary = null;
    /**
     *价格轴右边界
     *
     * Generated from protobuf field <code>int32 right_boundary = 4;</code>
     */
    protected $right_boundary = null;
    /**
     *划分方法
     *
     * Generated from protobuf field <code>int32 division_method = 5;</code>
     */
    protected $division_method = null;
    /**
     *划分价格
     *
     * Generated from protobuf field <code>int32 division_price = 6;</code>
     */
    protected $division_price = null;
    /**
     *禁止品类列表
     *
     * Generated from protobuf field <code>repeated int32 ban_product_category = 7;</code>
     */
    private $ban_product_category;
    /**
     *步长
     *
     * Generated from protobuf field <code>int32 slide_step = 8;</code>
     */
    protected $slide_step = null;
    /**
     *锚点
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.AnchorPoint anchor_point = 9;</code>
     */
    private $anchor_point;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $price_axle_title
     *          价格轴预期文案
     *     @type int $type
     *          价格轴类型
     *     @type int $left_boundary
     *          价格轴左边界
     *     @type int $right_boundary
     *          价格轴右边界
     *     @type int $division_method
     *          划分方法
     *     @type int $division_price
     *          划分价格
     *     @type int[]|\Nuwa\Protobuf\Internal\RepeatedField $ban_product_category
     *          禁止品类列表
     *     @type int $slide_step
     *          步长
     *     @type \Dirpc\SDK\PreSale\AnchorPoint[]|\Nuwa\Protobuf\Internal\RepeatedField $anchor_point
     *          锚点
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *价格轴预期文案
     *
     * Generated from protobuf field <code>string price_axle_title = 1;</code>
     * @return string
     */
    public function getPriceAxleTitle()
    {
        return isset($this->price_axle_title) ? $this->price_axle_title : '';
    }

    public function hasPriceAxleTitle()
    {
        return isset($this->price_axle_title);
    }

    public function clearPriceAxleTitle()
    {
        unset($this->price_axle_title);
    }

    /**
     *价格轴预期文案
     *
     * Generated from protobuf field <code>string price_axle_title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setPriceAxleTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->price_axle_title = $var;

        return $this;
    }

    /**
     *价格轴类型
     *
     * Generated from protobuf field <code>int32 type = 2;</code>
     * @return int
     */
    public function getType()
    {
        return isset($this->type) ? $this->type : 0;
    }

    public function hasType()
    {
        return isset($this->type);
    }

    public function clearType()
    {
        unset($this->type);
    }

    /**
     *价格轴类型
     *
     * Generated from protobuf field <code>int32 type = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkInt32($var);
        $this->type = $var;

        return $this;
    }

    /**
     *价格轴左边界
     *
     * Generated from protobuf field <code>int32 left_boundary = 3;</code>
     * @return int
     */
    public function getLeftBoundary()
    {
        return isset($this->left_boundary) ? $this->left_boundary : 0;
    }

    public function hasLeftBoundary()
    {
        return isset($this->left_boundary);
    }

    public function clearLeftBoundary()
    {
        unset($this->left_boundary);
    }

    /**
     *价格轴左边界
     *
     * Generated from protobuf field <code>int32 left_boundary = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setLeftBoundary($var)
    {
        GPBUtil::checkInt32($var);
        $this->left_boundary = $var;

        return $this;
    }

    /**
     *价格轴右边界
     *
     * Generated from protobuf field <code>int32 right_boundary = 4;</code>
     * @return int
     */
    public function getRightBoundary()
    {
        return isset($this->right_boundary) ? $this->right_boundary : 0;
    }

    public function hasRightBoundary()
    {
        return isset($this->right_boundary);
    }

    public function clearRightBoundary()
    {
        unset($this->right_boundary);
    }

    /**
     *价格轴右边界
     *
     * Generated from protobuf field <code>int32 right_boundary = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setRightBoundary($var)
    {
        GPBUtil::checkInt32($var);
        $this->right_boundary = $var;

        return $this;
    }

    /**
     *划分方法
     *
     * Generated from protobuf field <code>int32 division_method = 5;</code>
     * @return int
     */
    public function getDivisionMethod()
    {
        return isset($this->division_method) ? $this->division_method : 0;
    }

    public function hasDivisionMethod()
    {
        return isset($this->division_method);
    }

    public function clearDivisionMethod()
    {
        unset($this->division_method);
    }

    /**
     *划分方法
     *
     * Generated from protobuf field <code>int32 division_method = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setDivisionMethod($var)
    {
        GPBUtil::checkInt32($var);
        $this->division_method = $var;

        return $this;
    }

    /**
     *划分价格
     *
     * Generated from protobuf field <code>int32 division_price = 6;</code>
     * @return int
     */
    public function getDivisionPrice()
    {
        return isset($this->division_price) ? $this->division_price : 0;
    }

    public function hasDivisionPrice()
    {
        return isset($this->division_price);
    }

    public function clearDivisionPrice()
    {
        unset($this->division_price);
    }

    /**
     *划分价格
     *
     * Generated from protobuf field <code>int32 division_price = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setDivisionPrice($var)
    {
        GPBUtil::checkInt32($var);
        $this->division_price = $var;

        return $this;
    }

    /**
     *禁止品类列表
     *
     * Generated from protobuf field <code>repeated int32 ban_product_category = 7;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getBanProductCategory()
    {
        return $this->ban_product_category;
    }

    /**
     *禁止品类列表
     *
     * Generated from protobuf field <code>repeated int32 ban_product_category = 7;</code>
     * @param int[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBanProductCategory($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::INT32);
        $this->ban_product_category = $arr;

        return $this;
    }

    /**
     *步长
     *
     * Generated from protobuf field <code>int32 slide_step = 8;</code>
     * @return int
     */
    public function getSlideStep()
    {
        return isset($this->slide_step) ? $this->slide_step : 0;
    }

    public function hasSlideStep()
    {
        return isset($this->slide_step);
    }

    public function clearSlideStep()
    {
        unset($this->slide_step);
    }

    /**
     *步长
     *
     * Generated from protobuf field <code>int32 slide_step = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setSlideStep($var)
    {
        GPBUtil::checkInt32($var);
        $this->slide_step = $var;

        return $this;
    }

    /**
     *锚点
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.AnchorPoint anchor_point = 9;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getAnchorPoint()
    {
        return $this->anchor_point;
    }

    /**
     *锚点
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.AnchorPoint anchor_point = 9;</code>
     * @param \Dirpc\SDK\PreSale\AnchorPoint[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setAnchorPoint($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\AnchorPoint::class);
        $this->anchor_point = $arr;

        return $this;
    }

}

