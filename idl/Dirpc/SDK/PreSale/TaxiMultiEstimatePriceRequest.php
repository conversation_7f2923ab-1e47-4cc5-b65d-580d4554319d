<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 *新出租入参
 *
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.TaxiMultiEstimatePriceRequest</code>
 */
class TaxiMultiEstimatePriceRequest extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *客户端参数*
     *
     * Generated from protobuf field <code>string token = 1;</code>
     */
    protected $token = '';
    /**
     * Generated from protobuf field <code>string app_version = 2;</code>
     */
    protected $app_version = '';
    /**
     * Generated from protobuf field <code>int32 access_key_id = 3;</code>
     */
    protected $access_key_id = 0;
    /**
     * Generated from protobuf field <code>int32 channel = 4;</code>
     */
    protected $channel = 0;
    /**
     * Generated from protobuf field <code>int32 client_type = 5;</code>
     */
    protected $client_type = 0;
    /**
     * Generated from protobuf field <code>string lang = 6;</code>
     */
    protected $lang = '';
    /**
     * Generated from protobuf field <code>string a3_token = 7;</code>
     */
    protected $a3_token = '';
    /**
     * Generated from protobuf field <code>string pixels = 8;</code>
     */
    protected $pixels = '';
    /**
     * Generated from protobuf field <code>string maptype = 9;</code>
     */
    protected $maptype = '';
    /**
     * Generated from protobuf field <code>string imei = 10;</code>
     */
    protected $imei = '';
    /**
     * Generated from protobuf field <code>string suuid = 11;</code>
     */
    protected $suuid = '';
    /**
     * Generated from protobuf field <code>string terminal_id = 12;</code>
     */
    protected $terminal_id = '';
    /**
     * Generated from protobuf field <code>int32 origin_id = 13;</code>
     */
    protected $origin_id = 0;
    /**
     * Generated from protobuf field <code>int32 platform_type = 14;</code>
     */
    protected $platform_type = 0;
    /**
     * Generated from protobuf field <code>string openid = 15;</code>
     */
    protected $openid = '';
    /**
     * Generated from protobuf field <code>int32 guide_type = 16;</code>
     */
    protected $guide_type = 0;
    /**
     * Generated from protobuf field <code>string from = 17;</code>
     */
    protected $from = '';
    /**
     * Generated from protobuf field <code>string preferred_route_id = 18;</code>
     */
    protected $preferred_route_id = '';
    /**
     * Generated from protobuf field <code>string dialog_id = 19;</code>
     */
    protected $dialog_id = '';
    /**
     * Generated from protobuf field <code>string source_channel = 20;</code>
     */
    protected $source_channel = null;
    /**
     * Generated from protobuf field <code>double screen_scale = 21;</code>
     */
    protected $screen_scale = null;
    /**
     *预估表单样式，0:老样式，1:单行
     *
     * Generated from protobuf field <code>int32 estimate_style_type = 22;</code>
     */
    protected $estimate_style_type = null;
    /**
     *起终点相关信息*
     *
     * Generated from protobuf field <code>double lat = 30;</code>
     */
    protected $lat = 0.0;
    /**
     * Generated from protobuf field <code>double lng = 31;</code>
     */
    protected $lng = 0.0;
    /**
     * Generated from protobuf field <code>double from_lat = 32;</code>
     */
    protected $from_lat = 0.0;
    /**
     * Generated from protobuf field <code>double from_lng = 33;</code>
     */
    protected $from_lng = 0.0;
    /**
     * Generated from protobuf field <code>string from_poi_id = 34;</code>
     */
    protected $from_poi_id = '';
    /**
     * Generated from protobuf field <code>string from_poi_type = 35;</code>
     */
    protected $from_poi_type = '';
    /**
     * Generated from protobuf field <code>string from_address = 36;</code>
     */
    protected $from_address = '';
    /**
     * Generated from protobuf field <code>string from_name = 37;</code>
     */
    protected $from_name = '';
    /**
     * Generated from protobuf field <code>double to_lat = 38;</code>
     */
    protected $to_lat = 0.0;
    /**
     * Generated from protobuf field <code>double to_lng = 39;</code>
     */
    protected $to_lng = 0.0;
    /**
     * Generated from protobuf field <code>string to_poi_id = 40;</code>
     */
    protected $to_poi_id = '';
    /**
     * Generated from protobuf field <code>string to_poi_type = 41;</code>
     */
    protected $to_poi_type = '';
    /**
     * Generated from protobuf field <code>string to_address = 42;</code>
     */
    protected $to_address = '';
    /**
     * Generated from protobuf field <code>string to_name = 43;</code>
     */
    protected $to_name = '';
    /**
     * Generated from protobuf field <code>int32 dest_poi_code = 44;</code>
     */
    protected $dest_poi_code = 0;
    /**
     * Generated from protobuf field <code>string dest_poi_tag = 45;</code>
     */
    protected $dest_poi_tag = '';
    /**
     *订单属性等信息
     *
     * Generated from protobuf field <code>string menu_id = 50;</code>
     */
    protected $menu_id = '';
    /**
     * Generated from protobuf field <code>int32 page_type = 51;</code>
     */
    protected $page_type = 0;
    /**
     * Generated from protobuf field <code>int32 call_car_type = 52;</code>
     */
    protected $call_car_type = 0;
    /**
     * Generated from protobuf field <code>string call_car_phone = 53;</code>
     */
    protected $call_car_phone = '';
    /**
     *用户类型 0表示普通用户，2表示企业用户
     *
     * Generated from protobuf field <code>int32 user_type = 54;</code>
     */
    protected $user_type = 0;
    /**
     *时间戳
     *
     * Generated from protobuf field <code>string departure_time = 55;</code>
     */
    protected $departure_time = '';
    /**
     *支付类型
     *
     * Generated from protobuf field <code>int32 payments_type = 56;</code>
     */
    protected $payments_type = 0;
    /**
     *用户勾选项
     *
     * Generated from protobuf field <code>string multi_require_product = 57;</code>
     */
    protected $multi_require_product = '';
    /**
     *客户端是否进行过上拉操作
     *
     * Generated from protobuf field <code>int32 has_scroll = 58;</code>
     */
    protected $has_scroll = 0;
    /**
     *预约单
     *
     * Generated from protobuf field <code>int32 order_type = 59;</code>
     */
    protected $order_type = 0;
    /**
     *原始入口页面（如预约跳转接送机）
     *
     * Generated from protobuf field <code>int32 origin_page_type = 60;</code>
     */
    protected $origin_page_type = 0;
    /**
     *途经点参数 json array 字符串
     *
     * Generated from protobuf field <code>string stopover_points = 61;</code>
     */
    protected $stopover_points = '';
    /**
     *业务信息
     *
     * Generated from protobuf field <code>int32 shake_flag = 80;</code>
     */
    protected $shake_flag = null;
    /**
     * Generated from protobuf field <code>string pre_trace_id = 81;</code>
     */
    protected $pre_trace_id = null;
    /**
     *城际拼车订单出发时间
     *
     * Generated from protobuf field <code>string departure_range = 82;</code>
     */
    protected $departure_range = null;
    /**
     *航班出发地三字码,如CTU
     *
     * Generated from protobuf field <code>string flight_dep_code = 120;</code>
     */
    protected $flight_dep_code = null;
    /**
     *航班出发航站楼，如T2
     *
     * Generated from protobuf field <code>string flight_dep_terminal = 121;</code>
     */
    protected $flight_dep_terminal = null;
    /**
     *航班起飞时间字符串
     *
     * Generated from protobuf field <code>string traffic_dep_time = 122;</code>
     */
    protected $traffic_dep_time = null;
    /**
     *航班落地三字码,如CTU
     *
     * Generated from protobuf field <code>string flight_arr_code = 123;</code>
     */
    protected $flight_arr_code = null;
    /**
     *航班落地航站楼，如T2
     *
     * Generated from protobuf field <code>string flight_arr_terminal = 124;</code>
     */
    protected $flight_arr_terminal = null;
    /**
     *航班到达时间字符串
     *
     * Generated from protobuf field <code>string traffic_arr_time = 125;</code>
     */
    protected $traffic_arr_time = null;
    /**
     *航班号，如CA1405
     *
     * Generated from protobuf field <code>string traffic_number = 126;</code>
     */
    protected $traffic_number = null;
    /**
     *接送机类型 1-接机，2-送机，端上入口，无入口传0
     *
     * Generated from protobuf field <code>int32 airport_type = 127;</code>
     */
    protected $airport_type = null;
    /**
     *接机时为航班落地航站楼id，送机时为出发航站楼id，如1573
     *
     * Generated from protobuf field <code>int32 airport_id = 128;</code>
     */
    protected $airport_id = null;
    /**
     *用车偏移时间（接机时，单位：秒）
     *
     * Generated from protobuf field <code>int32 shift_time = 129;</code>
     */
    protected $shift_time = null;
    /**
     *活动id，去掉原有x_activity_id
     *
     * Generated from protobuf field <code>int32 activity_id = 150;</code>
     */
    protected $activity_id = null;
    /**
     *车票ID
     *
     * Generated from protobuf field <code>string biz_ticket = 155;</code>
     */
    protected $biz_ticket = null;
    /**
     *专车是否限制超远途订单
     *
     * Generated from protobuf field <code>int32 too_far_order_limit = 160;</code>
     */
    protected $too_far_order_limit = null;
    /**
     *极端天气场景（如暴雨等）
     *
     * Generated from protobuf field <code>int32 special_scene_param = 170;</code>
     */
    protected $special_scene_param = null;
    /**
     * Generated from protobuf field <code>string luxury_select_carlevels = 180;</code>
     */
    protected $luxury_select_carlevels = null;
    /**
     * Generated from protobuf field <code>string luxury_select_driver = 181;</code>
     */
    protected $luxury_select_driver = null;
    /**
     * Generated from protobuf field <code>string agent_type = 190;</code>
     */
    protected $agent_type = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $token
     *          客户端参数*
     *     @type string $app_version
     *     @type int $access_key_id
     *     @type int $channel
     *     @type int $client_type
     *     @type string $lang
     *     @type string $a3_token
     *     @type string $pixels
     *     @type string $maptype
     *     @type string $imei
     *     @type string $suuid
     *     @type string $terminal_id
     *     @type int $origin_id
     *     @type int $platform_type
     *     @type string $openid
     *     @type int $guide_type
     *     @type string $from
     *     @type string $preferred_route_id
     *     @type string $dialog_id
     *     @type string $source_channel
     *     @type float $screen_scale
     *     @type int $estimate_style_type
     *          预估表单样式，0:老样式，1:单行
     *     @type float $lat
     *          起终点相关信息*
     *     @type float $lng
     *     @type float $from_lat
     *     @type float $from_lng
     *     @type string $from_poi_id
     *     @type string $from_poi_type
     *     @type string $from_address
     *     @type string $from_name
     *     @type float $to_lat
     *     @type float $to_lng
     *     @type string $to_poi_id
     *     @type string $to_poi_type
     *     @type string $to_address
     *     @type string $to_name
     *     @type int $dest_poi_code
     *     @type string $dest_poi_tag
     *     @type string $menu_id
     *          订单属性等信息
     *     @type int $page_type
     *     @type int $call_car_type
     *     @type string $call_car_phone
     *     @type int $user_type
     *          用户类型 0表示普通用户，2表示企业用户
     *     @type string $departure_time
     *          时间戳
     *     @type int $payments_type
     *          支付类型
     *     @type string $multi_require_product
     *          用户勾选项
     *     @type int $has_scroll
     *          客户端是否进行过上拉操作
     *     @type int $order_type
     *          预约单
     *     @type int $origin_page_type
     *          原始入口页面（如预约跳转接送机）
     *     @type string $stopover_points
     *          途经点参数 json array 字符串
     *     @type int $shake_flag
     *          业务信息
     *     @type string $pre_trace_id
     *     @type string $departure_range
     *          城际拼车订单出发时间
     *     @type string $flight_dep_code
     *          航班出发地三字码,如CTU
     *     @type string $flight_dep_terminal
     *          航班出发航站楼，如T2
     *     @type string $traffic_dep_time
     *          航班起飞时间字符串
     *     @type string $flight_arr_code
     *          航班落地三字码,如CTU
     *     @type string $flight_arr_terminal
     *          航班落地航站楼，如T2
     *     @type string $traffic_arr_time
     *          航班到达时间字符串
     *     @type string $traffic_number
     *          航班号，如CA1405
     *     @type int $airport_type
     *          接送机类型 1-接机，2-送机，端上入口，无入口传0
     *     @type int $airport_id
     *          接机时为航班落地航站楼id，送机时为出发航站楼id，如1573
     *     @type int $shift_time
     *          用车偏移时间（接机时，单位：秒）
     *     @type int $activity_id
     *          活动id，去掉原有x_activity_id
     *     @type string $biz_ticket
     *          车票ID
     *     @type int $too_far_order_limit
     *          专车是否限制超远途订单
     *     @type int $special_scene_param
     *          极端天气场景（如暴雨等）
     *     @type string $luxury_select_carlevels
     *     @type string $luxury_select_driver
     *     @type string $agent_type
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *客户端参数*
     *
     * Generated from protobuf field <code>string token = 1;</code>
     * @return string
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     *客户端参数*
     *
     * Generated from protobuf field <code>string token = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->token = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string app_version = 2;</code>
     * @return string
     */
    public function getAppVersion()
    {
        return $this->app_version;
    }

    /**
     * Generated from protobuf field <code>string app_version = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setAppVersion($var)
    {
        GPBUtil::checkString($var, True);
        $this->app_version = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 access_key_id = 3;</code>
     * @return int
     */
    public function getAccessKeyId()
    {
        return $this->access_key_id;
    }

    /**
     * Generated from protobuf field <code>int32 access_key_id = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setAccessKeyId($var)
    {
        GPBUtil::checkInt32($var);
        $this->access_key_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 channel = 4;</code>
     * @return int
     */
    public function getChannel()
    {
        return $this->channel;
    }

    /**
     * Generated from protobuf field <code>int32 channel = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setChannel($var)
    {
        GPBUtil::checkInt32($var);
        $this->channel = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 client_type = 5;</code>
     * @return int
     */
    public function getClientType()
    {
        return $this->client_type;
    }

    /**
     * Generated from protobuf field <code>int32 client_type = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setClientType($var)
    {
        GPBUtil::checkInt32($var);
        $this->client_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string lang = 6;</code>
     * @return string
     */
    public function getLang()
    {
        return $this->lang;
    }

    /**
     * Generated from protobuf field <code>string lang = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setLang($var)
    {
        GPBUtil::checkString($var, True);
        $this->lang = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string a3_token = 7;</code>
     * @return string
     */
    public function getA3Token()
    {
        return $this->a3_token;
    }

    /**
     * Generated from protobuf field <code>string a3_token = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setA3Token($var)
    {
        GPBUtil::checkString($var, True);
        $this->a3_token = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string pixels = 8;</code>
     * @return string
     */
    public function getPixels()
    {
        return $this->pixels;
    }

    /**
     * Generated from protobuf field <code>string pixels = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setPixels($var)
    {
        GPBUtil::checkString($var, True);
        $this->pixels = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string maptype = 9;</code>
     * @return string
     */
    public function getMaptype()
    {
        return $this->maptype;
    }

    /**
     * Generated from protobuf field <code>string maptype = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setMaptype($var)
    {
        GPBUtil::checkString($var, True);
        $this->maptype = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string imei = 10;</code>
     * @return string
     */
    public function getImei()
    {
        return $this->imei;
    }

    /**
     * Generated from protobuf field <code>string imei = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setImei($var)
    {
        GPBUtil::checkString($var, True);
        $this->imei = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string suuid = 11;</code>
     * @return string
     */
    public function getSuuid()
    {
        return $this->suuid;
    }

    /**
     * Generated from protobuf field <code>string suuid = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setSuuid($var)
    {
        GPBUtil::checkString($var, True);
        $this->suuid = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string terminal_id = 12;</code>
     * @return string
     */
    public function getTerminalId()
    {
        return $this->terminal_id;
    }

    /**
     * Generated from protobuf field <code>string terminal_id = 12;</code>
     * @param string $var
     * @return $this
     */
    public function setTerminalId($var)
    {
        GPBUtil::checkString($var, True);
        $this->terminal_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 origin_id = 13;</code>
     * @return int
     */
    public function getOriginId()
    {
        return $this->origin_id;
    }

    /**
     * Generated from protobuf field <code>int32 origin_id = 13;</code>
     * @param int $var
     * @return $this
     */
    public function setOriginId($var)
    {
        GPBUtil::checkInt32($var);
        $this->origin_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 platform_type = 14;</code>
     * @return int
     */
    public function getPlatformType()
    {
        return $this->platform_type;
    }

    /**
     * Generated from protobuf field <code>int32 platform_type = 14;</code>
     * @param int $var
     * @return $this
     */
    public function setPlatformType($var)
    {
        GPBUtil::checkInt32($var);
        $this->platform_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string openid = 15;</code>
     * @return string
     */
    public function getOpenid()
    {
        return $this->openid;
    }

    /**
     * Generated from protobuf field <code>string openid = 15;</code>
     * @param string $var
     * @return $this
     */
    public function setOpenid($var)
    {
        GPBUtil::checkString($var, True);
        $this->openid = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 guide_type = 16;</code>
     * @return int
     */
    public function getGuideType()
    {
        return $this->guide_type;
    }

    /**
     * Generated from protobuf field <code>int32 guide_type = 16;</code>
     * @param int $var
     * @return $this
     */
    public function setGuideType($var)
    {
        GPBUtil::checkInt32($var);
        $this->guide_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from = 17;</code>
     * @return string
     */
    public function getFrom()
    {
        return $this->from;
    }

    /**
     * Generated from protobuf field <code>string from = 17;</code>
     * @param string $var
     * @return $this
     */
    public function setFrom($var)
    {
        GPBUtil::checkString($var, True);
        $this->from = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string preferred_route_id = 18;</code>
     * @return string
     */
    public function getPreferredRouteId()
    {
        return $this->preferred_route_id;
    }

    /**
     * Generated from protobuf field <code>string preferred_route_id = 18;</code>
     * @param string $var
     * @return $this
     */
    public function setPreferredRouteId($var)
    {
        GPBUtil::checkString($var, True);
        $this->preferred_route_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string dialog_id = 19;</code>
     * @return string
     */
    public function getDialogId()
    {
        return $this->dialog_id;
    }

    /**
     * Generated from protobuf field <code>string dialog_id = 19;</code>
     * @param string $var
     * @return $this
     */
    public function setDialogId($var)
    {
        GPBUtil::checkString($var, True);
        $this->dialog_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string source_channel = 20;</code>
     * @return string
     */
    public function getSourceChannel()
    {
        return isset($this->source_channel) ? $this->source_channel : '';
    }

    public function hasSourceChannel()
    {
        return isset($this->source_channel);
    }

    public function clearSourceChannel()
    {
        unset($this->source_channel);
    }

    /**
     * Generated from protobuf field <code>string source_channel = 20;</code>
     * @param string $var
     * @return $this
     */
    public function setSourceChannel($var)
    {
        GPBUtil::checkString($var, True);
        $this->source_channel = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double screen_scale = 21;</code>
     * @return float
     */
    public function getScreenScale()
    {
        return isset($this->screen_scale) ? $this->screen_scale : 0.0;
    }

    public function hasScreenScale()
    {
        return isset($this->screen_scale);
    }

    public function clearScreenScale()
    {
        unset($this->screen_scale);
    }

    /**
     * Generated from protobuf field <code>double screen_scale = 21;</code>
     * @param float $var
     * @return $this
     */
    public function setScreenScale($var)
    {
        GPBUtil::checkDouble($var);
        $this->screen_scale = $var;

        return $this;
    }

    /**
     *预估表单样式，0:老样式，1:单行
     *
     * Generated from protobuf field <code>int32 estimate_style_type = 22;</code>
     * @return int
     */
    public function getEstimateStyleType()
    {
        return isset($this->estimate_style_type) ? $this->estimate_style_type : 0;
    }

    public function hasEstimateStyleType()
    {
        return isset($this->estimate_style_type);
    }

    public function clearEstimateStyleType()
    {
        unset($this->estimate_style_type);
    }

    /**
     *预估表单样式，0:老样式，1:单行
     *
     * Generated from protobuf field <code>int32 estimate_style_type = 22;</code>
     * @param int $var
     * @return $this
     */
    public function setEstimateStyleType($var)
    {
        GPBUtil::checkInt32($var);
        $this->estimate_style_type = $var;

        return $this;
    }

    /**
     *起终点相关信息*
     *
     * Generated from protobuf field <code>double lat = 30;</code>
     * @return float
     */
    public function getLat()
    {
        return $this->lat;
    }

    /**
     *起终点相关信息*
     *
     * Generated from protobuf field <code>double lat = 30;</code>
     * @param float $var
     * @return $this
     */
    public function setLat($var)
    {
        GPBUtil::checkDouble($var);
        $this->lat = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double lng = 31;</code>
     * @return float
     */
    public function getLng()
    {
        return $this->lng;
    }

    /**
     * Generated from protobuf field <code>double lng = 31;</code>
     * @param float $var
     * @return $this
     */
    public function setLng($var)
    {
        GPBUtil::checkDouble($var);
        $this->lng = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double from_lat = 32;</code>
     * @return float
     */
    public function getFromLat()
    {
        return $this->from_lat;
    }

    /**
     * Generated from protobuf field <code>double from_lat = 32;</code>
     * @param float $var
     * @return $this
     */
    public function setFromLat($var)
    {
        GPBUtil::checkDouble($var);
        $this->from_lat = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double from_lng = 33;</code>
     * @return float
     */
    public function getFromLng()
    {
        return $this->from_lng;
    }

    /**
     * Generated from protobuf field <code>double from_lng = 33;</code>
     * @param float $var
     * @return $this
     */
    public function setFromLng($var)
    {
        GPBUtil::checkDouble($var);
        $this->from_lng = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_poi_id = 34;</code>
     * @return string
     */
    public function getFromPoiId()
    {
        return $this->from_poi_id;
    }

    /**
     * Generated from protobuf field <code>string from_poi_id = 34;</code>
     * @param string $var
     * @return $this
     */
    public function setFromPoiId($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_poi_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_poi_type = 35;</code>
     * @return string
     */
    public function getFromPoiType()
    {
        return $this->from_poi_type;
    }

    /**
     * Generated from protobuf field <code>string from_poi_type = 35;</code>
     * @param string $var
     * @return $this
     */
    public function setFromPoiType($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_poi_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_address = 36;</code>
     * @return string
     */
    public function getFromAddress()
    {
        return $this->from_address;
    }

    /**
     * Generated from protobuf field <code>string from_address = 36;</code>
     * @param string $var
     * @return $this
     */
    public function setFromAddress($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_address = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_name = 37;</code>
     * @return string
     */
    public function getFromName()
    {
        return $this->from_name;
    }

    /**
     * Generated from protobuf field <code>string from_name = 37;</code>
     * @param string $var
     * @return $this
     */
    public function setFromName($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double to_lat = 38;</code>
     * @return float
     */
    public function getToLat()
    {
        return $this->to_lat;
    }

    /**
     * Generated from protobuf field <code>double to_lat = 38;</code>
     * @param float $var
     * @return $this
     */
    public function setToLat($var)
    {
        GPBUtil::checkDouble($var);
        $this->to_lat = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double to_lng = 39;</code>
     * @return float
     */
    public function getToLng()
    {
        return $this->to_lng;
    }

    /**
     * Generated from protobuf field <code>double to_lng = 39;</code>
     * @param float $var
     * @return $this
     */
    public function setToLng($var)
    {
        GPBUtil::checkDouble($var);
        $this->to_lng = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_poi_id = 40;</code>
     * @return string
     */
    public function getToPoiId()
    {
        return $this->to_poi_id;
    }

    /**
     * Generated from protobuf field <code>string to_poi_id = 40;</code>
     * @param string $var
     * @return $this
     */
    public function setToPoiId($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_poi_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_poi_type = 41;</code>
     * @return string
     */
    public function getToPoiType()
    {
        return $this->to_poi_type;
    }

    /**
     * Generated from protobuf field <code>string to_poi_type = 41;</code>
     * @param string $var
     * @return $this
     */
    public function setToPoiType($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_poi_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_address = 42;</code>
     * @return string
     */
    public function getToAddress()
    {
        return $this->to_address;
    }

    /**
     * Generated from protobuf field <code>string to_address = 42;</code>
     * @param string $var
     * @return $this
     */
    public function setToAddress($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_address = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_name = 43;</code>
     * @return string
     */
    public function getToName()
    {
        return $this->to_name;
    }

    /**
     * Generated from protobuf field <code>string to_name = 43;</code>
     * @param string $var
     * @return $this
     */
    public function setToName($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 dest_poi_code = 44;</code>
     * @return int
     */
    public function getDestPoiCode()
    {
        return $this->dest_poi_code;
    }

    /**
     * Generated from protobuf field <code>int32 dest_poi_code = 44;</code>
     * @param int $var
     * @return $this
     */
    public function setDestPoiCode($var)
    {
        GPBUtil::checkInt32($var);
        $this->dest_poi_code = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string dest_poi_tag = 45;</code>
     * @return string
     */
    public function getDestPoiTag()
    {
        return $this->dest_poi_tag;
    }

    /**
     * Generated from protobuf field <code>string dest_poi_tag = 45;</code>
     * @param string $var
     * @return $this
     */
    public function setDestPoiTag($var)
    {
        GPBUtil::checkString($var, True);
        $this->dest_poi_tag = $var;

        return $this;
    }

    /**
     *订单属性等信息
     *
     * Generated from protobuf field <code>string menu_id = 50;</code>
     * @return string
     */
    public function getMenuId()
    {
        return $this->menu_id;
    }

    /**
     *订单属性等信息
     *
     * Generated from protobuf field <code>string menu_id = 50;</code>
     * @param string $var
     * @return $this
     */
    public function setMenuId($var)
    {
        GPBUtil::checkString($var, True);
        $this->menu_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 page_type = 51;</code>
     * @return int
     */
    public function getPageType()
    {
        return $this->page_type;
    }

    /**
     * Generated from protobuf field <code>int32 page_type = 51;</code>
     * @param int $var
     * @return $this
     */
    public function setPageType($var)
    {
        GPBUtil::checkInt32($var);
        $this->page_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 call_car_type = 52;</code>
     * @return int
     */
    public function getCallCarType()
    {
        return $this->call_car_type;
    }

    /**
     * Generated from protobuf field <code>int32 call_car_type = 52;</code>
     * @param int $var
     * @return $this
     */
    public function setCallCarType($var)
    {
        GPBUtil::checkInt32($var);
        $this->call_car_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string call_car_phone = 53;</code>
     * @return string
     */
    public function getCallCarPhone()
    {
        return $this->call_car_phone;
    }

    /**
     * Generated from protobuf field <code>string call_car_phone = 53;</code>
     * @param string $var
     * @return $this
     */
    public function setCallCarPhone($var)
    {
        GPBUtil::checkString($var, True);
        $this->call_car_phone = $var;

        return $this;
    }

    /**
     *用户类型 0表示普通用户，2表示企业用户
     *
     * Generated from protobuf field <code>int32 user_type = 54;</code>
     * @return int
     */
    public function getUserType()
    {
        return $this->user_type;
    }

    /**
     *用户类型 0表示普通用户，2表示企业用户
     *
     * Generated from protobuf field <code>int32 user_type = 54;</code>
     * @param int $var
     * @return $this
     */
    public function setUserType($var)
    {
        GPBUtil::checkInt32($var);
        $this->user_type = $var;

        return $this;
    }

    /**
     *时间戳
     *
     * Generated from protobuf field <code>string departure_time = 55;</code>
     * @return string
     */
    public function getDepartureTime()
    {
        return $this->departure_time;
    }

    /**
     *时间戳
     *
     * Generated from protobuf field <code>string departure_time = 55;</code>
     * @param string $var
     * @return $this
     */
    public function setDepartureTime($var)
    {
        GPBUtil::checkString($var, True);
        $this->departure_time = $var;

        return $this;
    }

    /**
     *支付类型
     *
     * Generated from protobuf field <code>int32 payments_type = 56;</code>
     * @return int
     */
    public function getPaymentsType()
    {
        return $this->payments_type;
    }

    /**
     *支付类型
     *
     * Generated from protobuf field <code>int32 payments_type = 56;</code>
     * @param int $var
     * @return $this
     */
    public function setPaymentsType($var)
    {
        GPBUtil::checkInt32($var);
        $this->payments_type = $var;

        return $this;
    }

    /**
     *用户勾选项
     *
     * Generated from protobuf field <code>string multi_require_product = 57;</code>
     * @return string
     */
    public function getMultiRequireProduct()
    {
        return $this->multi_require_product;
    }

    /**
     *用户勾选项
     *
     * Generated from protobuf field <code>string multi_require_product = 57;</code>
     * @param string $var
     * @return $this
     */
    public function setMultiRequireProduct($var)
    {
        GPBUtil::checkString($var, True);
        $this->multi_require_product = $var;

        return $this;
    }

    /**
     *客户端是否进行过上拉操作
     *
     * Generated from protobuf field <code>int32 has_scroll = 58;</code>
     * @return int
     */
    public function getHasScroll()
    {
        return $this->has_scroll;
    }

    /**
     *客户端是否进行过上拉操作
     *
     * Generated from protobuf field <code>int32 has_scroll = 58;</code>
     * @param int $var
     * @return $this
     */
    public function setHasScroll($var)
    {
        GPBUtil::checkInt32($var);
        $this->has_scroll = $var;

        return $this;
    }

    /**
     *预约单
     *
     * Generated from protobuf field <code>int32 order_type = 59;</code>
     * @return int
     */
    public function getOrderType()
    {
        return $this->order_type;
    }

    /**
     *预约单
     *
     * Generated from protobuf field <code>int32 order_type = 59;</code>
     * @param int $var
     * @return $this
     */
    public function setOrderType($var)
    {
        GPBUtil::checkInt32($var);
        $this->order_type = $var;

        return $this;
    }

    /**
     *原始入口页面（如预约跳转接送机）
     *
     * Generated from protobuf field <code>int32 origin_page_type = 60;</code>
     * @return int
     */
    public function getOriginPageType()
    {
        return $this->origin_page_type;
    }

    /**
     *原始入口页面（如预约跳转接送机）
     *
     * Generated from protobuf field <code>int32 origin_page_type = 60;</code>
     * @param int $var
     * @return $this
     */
    public function setOriginPageType($var)
    {
        GPBUtil::checkInt32($var);
        $this->origin_page_type = $var;

        return $this;
    }

    /**
     *途经点参数 json array 字符串
     *
     * Generated from protobuf field <code>string stopover_points = 61;</code>
     * @return string
     */
    public function getStopoverPoints()
    {
        return $this->stopover_points;
    }

    /**
     *途经点参数 json array 字符串
     *
     * Generated from protobuf field <code>string stopover_points = 61;</code>
     * @param string $var
     * @return $this
     */
    public function setStopoverPoints($var)
    {
        GPBUtil::checkString($var, True);
        $this->stopover_points = $var;

        return $this;
    }

    /**
     *业务信息
     *
     * Generated from protobuf field <code>int32 shake_flag = 80;</code>
     * @return int
     */
    public function getShakeFlag()
    {
        return isset($this->shake_flag) ? $this->shake_flag : 0;
    }

    public function hasShakeFlag()
    {
        return isset($this->shake_flag);
    }

    public function clearShakeFlag()
    {
        unset($this->shake_flag);
    }

    /**
     *业务信息
     *
     * Generated from protobuf field <code>int32 shake_flag = 80;</code>
     * @param int $var
     * @return $this
     */
    public function setShakeFlag($var)
    {
        GPBUtil::checkInt32($var);
        $this->shake_flag = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string pre_trace_id = 81;</code>
     * @return string
     */
    public function getPreTraceId()
    {
        return isset($this->pre_trace_id) ? $this->pre_trace_id : '';
    }

    public function hasPreTraceId()
    {
        return isset($this->pre_trace_id);
    }

    public function clearPreTraceId()
    {
        unset($this->pre_trace_id);
    }

    /**
     * Generated from protobuf field <code>string pre_trace_id = 81;</code>
     * @param string $var
     * @return $this
     */
    public function setPreTraceId($var)
    {
        GPBUtil::checkString($var, True);
        $this->pre_trace_id = $var;

        return $this;
    }

    /**
     *城际拼车订单出发时间
     *
     * Generated from protobuf field <code>string departure_range = 82;</code>
     * @return string
     */
    public function getDepartureRange()
    {
        return isset($this->departure_range) ? $this->departure_range : '';
    }

    public function hasDepartureRange()
    {
        return isset($this->departure_range);
    }

    public function clearDepartureRange()
    {
        unset($this->departure_range);
    }

    /**
     *城际拼车订单出发时间
     *
     * Generated from protobuf field <code>string departure_range = 82;</code>
     * @param string $var
     * @return $this
     */
    public function setDepartureRange($var)
    {
        GPBUtil::checkString($var, True);
        $this->departure_range = $var;

        return $this;
    }

    /**
     *航班出发地三字码,如CTU
     *
     * Generated from protobuf field <code>string flight_dep_code = 120;</code>
     * @return string
     */
    public function getFlightDepCode()
    {
        return isset($this->flight_dep_code) ? $this->flight_dep_code : '';
    }

    public function hasFlightDepCode()
    {
        return isset($this->flight_dep_code);
    }

    public function clearFlightDepCode()
    {
        unset($this->flight_dep_code);
    }

    /**
     *航班出发地三字码,如CTU
     *
     * Generated from protobuf field <code>string flight_dep_code = 120;</code>
     * @param string $var
     * @return $this
     */
    public function setFlightDepCode($var)
    {
        GPBUtil::checkString($var, True);
        $this->flight_dep_code = $var;

        return $this;
    }

    /**
     *航班出发航站楼，如T2
     *
     * Generated from protobuf field <code>string flight_dep_terminal = 121;</code>
     * @return string
     */
    public function getFlightDepTerminal()
    {
        return isset($this->flight_dep_terminal) ? $this->flight_dep_terminal : '';
    }

    public function hasFlightDepTerminal()
    {
        return isset($this->flight_dep_terminal);
    }

    public function clearFlightDepTerminal()
    {
        unset($this->flight_dep_terminal);
    }

    /**
     *航班出发航站楼，如T2
     *
     * Generated from protobuf field <code>string flight_dep_terminal = 121;</code>
     * @param string $var
     * @return $this
     */
    public function setFlightDepTerminal($var)
    {
        GPBUtil::checkString($var, True);
        $this->flight_dep_terminal = $var;

        return $this;
    }

    /**
     *航班起飞时间字符串
     *
     * Generated from protobuf field <code>string traffic_dep_time = 122;</code>
     * @return string
     */
    public function getTrafficDepTime()
    {
        return isset($this->traffic_dep_time) ? $this->traffic_dep_time : '';
    }

    public function hasTrafficDepTime()
    {
        return isset($this->traffic_dep_time);
    }

    public function clearTrafficDepTime()
    {
        unset($this->traffic_dep_time);
    }

    /**
     *航班起飞时间字符串
     *
     * Generated from protobuf field <code>string traffic_dep_time = 122;</code>
     * @param string $var
     * @return $this
     */
    public function setTrafficDepTime($var)
    {
        GPBUtil::checkString($var, True);
        $this->traffic_dep_time = $var;

        return $this;
    }

    /**
     *航班落地三字码,如CTU
     *
     * Generated from protobuf field <code>string flight_arr_code = 123;</code>
     * @return string
     */
    public function getFlightArrCode()
    {
        return isset($this->flight_arr_code) ? $this->flight_arr_code : '';
    }

    public function hasFlightArrCode()
    {
        return isset($this->flight_arr_code);
    }

    public function clearFlightArrCode()
    {
        unset($this->flight_arr_code);
    }

    /**
     *航班落地三字码,如CTU
     *
     * Generated from protobuf field <code>string flight_arr_code = 123;</code>
     * @param string $var
     * @return $this
     */
    public function setFlightArrCode($var)
    {
        GPBUtil::checkString($var, True);
        $this->flight_arr_code = $var;

        return $this;
    }

    /**
     *航班落地航站楼，如T2
     *
     * Generated from protobuf field <code>string flight_arr_terminal = 124;</code>
     * @return string
     */
    public function getFlightArrTerminal()
    {
        return isset($this->flight_arr_terminal) ? $this->flight_arr_terminal : '';
    }

    public function hasFlightArrTerminal()
    {
        return isset($this->flight_arr_terminal);
    }

    public function clearFlightArrTerminal()
    {
        unset($this->flight_arr_terminal);
    }

    /**
     *航班落地航站楼，如T2
     *
     * Generated from protobuf field <code>string flight_arr_terminal = 124;</code>
     * @param string $var
     * @return $this
     */
    public function setFlightArrTerminal($var)
    {
        GPBUtil::checkString($var, True);
        $this->flight_arr_terminal = $var;

        return $this;
    }

    /**
     *航班到达时间字符串
     *
     * Generated from protobuf field <code>string traffic_arr_time = 125;</code>
     * @return string
     */
    public function getTrafficArrTime()
    {
        return isset($this->traffic_arr_time) ? $this->traffic_arr_time : '';
    }

    public function hasTrafficArrTime()
    {
        return isset($this->traffic_arr_time);
    }

    public function clearTrafficArrTime()
    {
        unset($this->traffic_arr_time);
    }

    /**
     *航班到达时间字符串
     *
     * Generated from protobuf field <code>string traffic_arr_time = 125;</code>
     * @param string $var
     * @return $this
     */
    public function setTrafficArrTime($var)
    {
        GPBUtil::checkString($var, True);
        $this->traffic_arr_time = $var;

        return $this;
    }

    /**
     *航班号，如CA1405
     *
     * Generated from protobuf field <code>string traffic_number = 126;</code>
     * @return string
     */
    public function getTrafficNumber()
    {
        return isset($this->traffic_number) ? $this->traffic_number : '';
    }

    public function hasTrafficNumber()
    {
        return isset($this->traffic_number);
    }

    public function clearTrafficNumber()
    {
        unset($this->traffic_number);
    }

    /**
     *航班号，如CA1405
     *
     * Generated from protobuf field <code>string traffic_number = 126;</code>
     * @param string $var
     * @return $this
     */
    public function setTrafficNumber($var)
    {
        GPBUtil::checkString($var, True);
        $this->traffic_number = $var;

        return $this;
    }

    /**
     *接送机类型 1-接机，2-送机，端上入口，无入口传0
     *
     * Generated from protobuf field <code>int32 airport_type = 127;</code>
     * @return int
     */
    public function getAirportType()
    {
        return isset($this->airport_type) ? $this->airport_type : 0;
    }

    public function hasAirportType()
    {
        return isset($this->airport_type);
    }

    public function clearAirportType()
    {
        unset($this->airport_type);
    }

    /**
     *接送机类型 1-接机，2-送机，端上入口，无入口传0
     *
     * Generated from protobuf field <code>int32 airport_type = 127;</code>
     * @param int $var
     * @return $this
     */
    public function setAirportType($var)
    {
        GPBUtil::checkInt32($var);
        $this->airport_type = $var;

        return $this;
    }

    /**
     *接机时为航班落地航站楼id，送机时为出发航站楼id，如1573
     *
     * Generated from protobuf field <code>int32 airport_id = 128;</code>
     * @return int
     */
    public function getAirportId()
    {
        return isset($this->airport_id) ? $this->airport_id : 0;
    }

    public function hasAirportId()
    {
        return isset($this->airport_id);
    }

    public function clearAirportId()
    {
        unset($this->airport_id);
    }

    /**
     *接机时为航班落地航站楼id，送机时为出发航站楼id，如1573
     *
     * Generated from protobuf field <code>int32 airport_id = 128;</code>
     * @param int $var
     * @return $this
     */
    public function setAirportId($var)
    {
        GPBUtil::checkInt32($var);
        $this->airport_id = $var;

        return $this;
    }

    /**
     *用车偏移时间（接机时，单位：秒）
     *
     * Generated from protobuf field <code>int32 shift_time = 129;</code>
     * @return int
     */
    public function getShiftTime()
    {
        return isset($this->shift_time) ? $this->shift_time : 0;
    }

    public function hasShiftTime()
    {
        return isset($this->shift_time);
    }

    public function clearShiftTime()
    {
        unset($this->shift_time);
    }

    /**
     *用车偏移时间（接机时，单位：秒）
     *
     * Generated from protobuf field <code>int32 shift_time = 129;</code>
     * @param int $var
     * @return $this
     */
    public function setShiftTime($var)
    {
        GPBUtil::checkInt32($var);
        $this->shift_time = $var;

        return $this;
    }

    /**
     *活动id，去掉原有x_activity_id
     *
     * Generated from protobuf field <code>int32 activity_id = 150;</code>
     * @return int
     */
    public function getActivityId()
    {
        return isset($this->activity_id) ? $this->activity_id : 0;
    }

    public function hasActivityId()
    {
        return isset($this->activity_id);
    }

    public function clearActivityId()
    {
        unset($this->activity_id);
    }

    /**
     *活动id，去掉原有x_activity_id
     *
     * Generated from protobuf field <code>int32 activity_id = 150;</code>
     * @param int $var
     * @return $this
     */
    public function setActivityId($var)
    {
        GPBUtil::checkInt32($var);
        $this->activity_id = $var;

        return $this;
    }

    /**
     *车票ID
     *
     * Generated from protobuf field <code>string biz_ticket = 155;</code>
     * @return string
     */
    public function getBizTicket()
    {
        return isset($this->biz_ticket) ? $this->biz_ticket : '';
    }

    public function hasBizTicket()
    {
        return isset($this->biz_ticket);
    }

    public function clearBizTicket()
    {
        unset($this->biz_ticket);
    }

    /**
     *车票ID
     *
     * Generated from protobuf field <code>string biz_ticket = 155;</code>
     * @param string $var
     * @return $this
     */
    public function setBizTicket($var)
    {
        GPBUtil::checkString($var, True);
        $this->biz_ticket = $var;

        return $this;
    }

    /**
     *专车是否限制超远途订单
     *
     * Generated from protobuf field <code>int32 too_far_order_limit = 160;</code>
     * @return int
     */
    public function getTooFarOrderLimit()
    {
        return isset($this->too_far_order_limit) ? $this->too_far_order_limit : 0;
    }

    public function hasTooFarOrderLimit()
    {
        return isset($this->too_far_order_limit);
    }

    public function clearTooFarOrderLimit()
    {
        unset($this->too_far_order_limit);
    }

    /**
     *专车是否限制超远途订单
     *
     * Generated from protobuf field <code>int32 too_far_order_limit = 160;</code>
     * @param int $var
     * @return $this
     */
    public function setTooFarOrderLimit($var)
    {
        GPBUtil::checkInt32($var);
        $this->too_far_order_limit = $var;

        return $this;
    }

    /**
     *极端天气场景（如暴雨等）
     *
     * Generated from protobuf field <code>int32 special_scene_param = 170;</code>
     * @return int
     */
    public function getSpecialSceneParam()
    {
        return isset($this->special_scene_param) ? $this->special_scene_param : 0;
    }

    public function hasSpecialSceneParam()
    {
        return isset($this->special_scene_param);
    }

    public function clearSpecialSceneParam()
    {
        unset($this->special_scene_param);
    }

    /**
     *极端天气场景（如暴雨等）
     *
     * Generated from protobuf field <code>int32 special_scene_param = 170;</code>
     * @param int $var
     * @return $this
     */
    public function setSpecialSceneParam($var)
    {
        GPBUtil::checkInt32($var);
        $this->special_scene_param = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string luxury_select_carlevels = 180;</code>
     * @return string
     */
    public function getLuxurySelectCarlevels()
    {
        return isset($this->luxury_select_carlevels) ? $this->luxury_select_carlevels : '';
    }

    public function hasLuxurySelectCarlevels()
    {
        return isset($this->luxury_select_carlevels);
    }

    public function clearLuxurySelectCarlevels()
    {
        unset($this->luxury_select_carlevels);
    }

    /**
     * Generated from protobuf field <code>string luxury_select_carlevels = 180;</code>
     * @param string $var
     * @return $this
     */
    public function setLuxurySelectCarlevels($var)
    {
        GPBUtil::checkString($var, True);
        $this->luxury_select_carlevels = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string luxury_select_driver = 181;</code>
     * @return string
     */
    public function getLuxurySelectDriver()
    {
        return isset($this->luxury_select_driver) ? $this->luxury_select_driver : '';
    }

    public function hasLuxurySelectDriver()
    {
        return isset($this->luxury_select_driver);
    }

    public function clearLuxurySelectDriver()
    {
        unset($this->luxury_select_driver);
    }

    /**
     * Generated from protobuf field <code>string luxury_select_driver = 181;</code>
     * @param string $var
     * @return $this
     */
    public function setLuxurySelectDriver($var)
    {
        GPBUtil::checkString($var, True);
        $this->luxury_select_driver = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string agent_type = 190;</code>
     * @return string
     */
    public function getAgentType()
    {
        return isset($this->agent_type) ? $this->agent_type : '';
    }

    public function hasAgentType()
    {
        return isset($this->agent_type);
    }

    public function clearAgentType()
    {
        unset($this->agent_type);
    }

    /**
     * Generated from protobuf field <code>string agent_type = 190;</code>
     * @param string $var
     * @return $this
     */
    public function setAgentType($var)
    {
        GPBUtil::checkString($var, True);
        $this->agent_type = $var;

        return $this;
    }

}

