<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: carpool.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.EndAddressData</code>
 */
class EndAddressData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string to_poi_id = 1;</code>
     */
    protected $to_poi_id = '';
    /**
     * Generated from protobuf field <code>string to_area = 2;</code>
     */
    protected $to_area = '';
    /**
     * Generated from protobuf field <code>string to_lng = 3;</code>
     */
    protected $to_lng = '';
    /**
     * Generated from protobuf field <code>string to_lat = 4;</code>
     */
    protected $to_lat = '';
    /**
     * Generated from protobuf field <code>string to_name = 5;</code>
     */
    protected $to_name = '';
    /**
     * Generated from protobuf field <code>string to_address = 6;</code>
     */
    protected $to_address = '';
    /**
     * Generated from protobuf field <code>string to_area_name = 7;</code>
     */
    protected $to_area_name = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $to_poi_id
     *     @type string $to_area
     *     @type string $to_lng
     *     @type string $to_lat
     *     @type string $to_name
     *     @type string $to_address
     *     @type string $to_area_name
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\Carpool::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string to_poi_id = 1;</code>
     * @return string
     */
    public function getToPoiId()
    {
        return $this->to_poi_id;
    }

    /**
     * Generated from protobuf field <code>string to_poi_id = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setToPoiId($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_poi_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_area = 2;</code>
     * @return string
     */
    public function getToArea()
    {
        return $this->to_area;
    }

    /**
     * Generated from protobuf field <code>string to_area = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setToArea($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_area = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_lng = 3;</code>
     * @return string
     */
    public function getToLng()
    {
        return $this->to_lng;
    }

    /**
     * Generated from protobuf field <code>string to_lng = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setToLng($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_lng = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_lat = 4;</code>
     * @return string
     */
    public function getToLat()
    {
        return $this->to_lat;
    }

    /**
     * Generated from protobuf field <code>string to_lat = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setToLat($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_lat = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_name = 5;</code>
     * @return string
     */
    public function getToName()
    {
        return $this->to_name;
    }

    /**
     * Generated from protobuf field <code>string to_name = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setToName($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_address = 6;</code>
     * @return string
     */
    public function getToAddress()
    {
        return $this->to_address;
    }

    /**
     * Generated from protobuf field <code>string to_address = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setToAddress($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_address = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_area_name = 7;</code>
     * @return string
     */
    public function getToAreaName()
    {
        return $this->to_area_name;
    }

    /**
     * Generated from protobuf field <code>string to_area_name = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setToAreaName($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_area_name = $var;

        return $this;
    }

}

