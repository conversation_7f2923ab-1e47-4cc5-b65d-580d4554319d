<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.bargainPopup</code>
 */
class bargainPopup extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *弹框标题
     *
     * Generated from protobuf field <code>string title = 1;</code>
     */
    protected $title = null;
    /**
     *子标题-低于推荐价
     *
     * Generated from protobuf field <code>string sub_title_min = 2;</code>
     */
    protected $sub_title_min = null;
    /**
     *子标题-等于推荐价
     *
     * Generated from protobuf field <code>string sub_title_equal = 3;</code>
     */
    protected $sub_title_equal = null;
    /**
     *子标题-高于推荐价
     *
     * Generated from protobuf field <code>string sub_title_max = 4;</code>
     */
    protected $sub_title_max = null;
    /**
     *左侧价格标签
     *
     * Generated from protobuf field <code>repeated int32 left_price_tag = 5;</code>
     */
    private $left_price_tag;
    /**
     *右侧价格标签
     *
     * Generated from protobuf field <code>repeated int32 right_price_tag = 6;</code>
     */
    private $right_price_tag;
    /**
     *按钮文案
     *
     * Generated from protobuf field <code>string button_text = 7;</code>
     */
    protected $button_text = null;
    /**
     *上下限信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BargainFeeMargin fee_margin = 8;</code>
     */
    protected $fee_margin = null;
    /**
     *费用描述信息的模版
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_content_template = 9;</code>
     */
    protected $fee_desc_content_template = null;
    /**
     *预估价模版
     *
     * Generated from protobuf field <code>string fee_msg_template = 10;</code>
     */
    protected $fee_msg_template = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $title
     *          弹框标题
     *     @type string $sub_title_min
     *          子标题-低于推荐价
     *     @type string $sub_title_equal
     *          子标题-等于推荐价
     *     @type string $sub_title_max
     *          子标题-高于推荐价
     *     @type int[]|\Nuwa\Protobuf\Internal\RepeatedField $left_price_tag
     *          左侧价格标签
     *     @type int[]|\Nuwa\Protobuf\Internal\RepeatedField $right_price_tag
     *          右侧价格标签
     *     @type string $button_text
     *          按钮文案
     *     @type \Dirpc\SDK\PreSale\BargainFeeMargin $fee_margin
     *          上下限信息
     *     @type \Dirpc\SDK\PreSale\NewFormFeeDesc $fee_desc_content_template
     *          费用描述信息的模版
     *     @type string $fee_msg_template
     *          预估价模版
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *弹框标题
     *
     * Generated from protobuf field <code>string title = 1;</code>
     * @return string
     */
    public function getTitle()
    {
        return isset($this->title) ? $this->title : '';
    }

    public function hasTitle()
    {
        return isset($this->title);
    }

    public function clearTitle()
    {
        unset($this->title);
    }

    /**
     *弹框标题
     *
     * Generated from protobuf field <code>string title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     *子标题-低于推荐价
     *
     * Generated from protobuf field <code>string sub_title_min = 2;</code>
     * @return string
     */
    public function getSubTitleMin()
    {
        return isset($this->sub_title_min) ? $this->sub_title_min : '';
    }

    public function hasSubTitleMin()
    {
        return isset($this->sub_title_min);
    }

    public function clearSubTitleMin()
    {
        unset($this->sub_title_min);
    }

    /**
     *子标题-低于推荐价
     *
     * Generated from protobuf field <code>string sub_title_min = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setSubTitleMin($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_title_min = $var;

        return $this;
    }

    /**
     *子标题-等于推荐价
     *
     * Generated from protobuf field <code>string sub_title_equal = 3;</code>
     * @return string
     */
    public function getSubTitleEqual()
    {
        return isset($this->sub_title_equal) ? $this->sub_title_equal : '';
    }

    public function hasSubTitleEqual()
    {
        return isset($this->sub_title_equal);
    }

    public function clearSubTitleEqual()
    {
        unset($this->sub_title_equal);
    }

    /**
     *子标题-等于推荐价
     *
     * Generated from protobuf field <code>string sub_title_equal = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setSubTitleEqual($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_title_equal = $var;

        return $this;
    }

    /**
     *子标题-高于推荐价
     *
     * Generated from protobuf field <code>string sub_title_max = 4;</code>
     * @return string
     */
    public function getSubTitleMax()
    {
        return isset($this->sub_title_max) ? $this->sub_title_max : '';
    }

    public function hasSubTitleMax()
    {
        return isset($this->sub_title_max);
    }

    public function clearSubTitleMax()
    {
        unset($this->sub_title_max);
    }

    /**
     *子标题-高于推荐价
     *
     * Generated from protobuf field <code>string sub_title_max = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setSubTitleMax($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_title_max = $var;

        return $this;
    }

    /**
     *左侧价格标签
     *
     * Generated from protobuf field <code>repeated int32 left_price_tag = 5;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getLeftPriceTag()
    {
        return $this->left_price_tag;
    }

    /**
     *左侧价格标签
     *
     * Generated from protobuf field <code>repeated int32 left_price_tag = 5;</code>
     * @param int[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setLeftPriceTag($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::INT32);
        $this->left_price_tag = $arr;

        return $this;
    }

    /**
     *右侧价格标签
     *
     * Generated from protobuf field <code>repeated int32 right_price_tag = 6;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getRightPriceTag()
    {
        return $this->right_price_tag;
    }

    /**
     *右侧价格标签
     *
     * Generated from protobuf field <code>repeated int32 right_price_tag = 6;</code>
     * @param int[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setRightPriceTag($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::INT32);
        $this->right_price_tag = $arr;

        return $this;
    }

    /**
     *按钮文案
     *
     * Generated from protobuf field <code>string button_text = 7;</code>
     * @return string
     */
    public function getButtonText()
    {
        return isset($this->button_text) ? $this->button_text : '';
    }

    public function hasButtonText()
    {
        return isset($this->button_text);
    }

    public function clearButtonText()
    {
        unset($this->button_text);
    }

    /**
     *按钮文案
     *
     * Generated from protobuf field <code>string button_text = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setButtonText($var)
    {
        GPBUtil::checkString($var, True);
        $this->button_text = $var;

        return $this;
    }

    /**
     *上下限信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BargainFeeMargin fee_margin = 8;</code>
     * @return \Dirpc\SDK\PreSale\BargainFeeMargin
     */
    public function getFeeMargin()
    {
        return isset($this->fee_margin) ? $this->fee_margin : null;
    }

    public function hasFeeMargin()
    {
        return isset($this->fee_margin);
    }

    public function clearFeeMargin()
    {
        unset($this->fee_margin);
    }

    /**
     *上下限信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BargainFeeMargin fee_margin = 8;</code>
     * @param \Dirpc\SDK\PreSale\BargainFeeMargin $var
     * @return $this
     */
    public function setFeeMargin($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\BargainFeeMargin::class);
        $this->fee_margin = $var;

        return $this;
    }

    /**
     *费用描述信息的模版
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_content_template = 9;</code>
     * @return \Dirpc\SDK\PreSale\NewFormFeeDesc
     */
    public function getFeeDescContentTemplate()
    {
        return isset($this->fee_desc_content_template) ? $this->fee_desc_content_template : null;
    }

    public function hasFeeDescContentTemplate()
    {
        return isset($this->fee_desc_content_template);
    }

    public function clearFeeDescContentTemplate()
    {
        unset($this->fee_desc_content_template);
    }

    /**
     *费用描述信息的模版
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_content_template = 9;</code>
     * @param \Dirpc\SDK\PreSale\NewFormFeeDesc $var
     * @return $this
     */
    public function setFeeDescContentTemplate($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NewFormFeeDesc::class);
        $this->fee_desc_content_template = $var;

        return $this;
    }

    /**
     *预估价模版
     *
     * Generated from protobuf field <code>string fee_msg_template = 10;</code>
     * @return string
     */
    public function getFeeMsgTemplate()
    {
        return isset($this->fee_msg_template) ? $this->fee_msg_template : '';
    }

    public function hasFeeMsgTemplate()
    {
        return isset($this->fee_msg_template);
    }

    public function clearFeeMsgTemplate()
    {
        unset($this->fee_msg_template);
    }

    /**
     *预估价模版
     *
     * Generated from protobuf field <code>string fee_msg_template = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsgTemplate($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg_template = $var;

        return $this;
    }

}

