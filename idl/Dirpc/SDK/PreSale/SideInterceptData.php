<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideInterceptData</code>
 */
class SideInterceptData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string bg_url = 1;</code>
     */
    protected $bg_url = '';
    /**
     * Generated from protobuf field <code>string title = 2;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>string sub_title = 3;</code>
     */
    protected $sub_title = '';
    /**
     * Generated from protobuf field <code>string content = 4;</code>
     */
    protected $content = '';
    /**
     * Generated from protobuf field <code>string content_desc = 5;</code>
     */
    protected $content_desc = '';
    /**
     * Generated from protobuf field <code>string left_button = 6;</code>
     */
    protected $left_button = '';
    /**
     * Generated from protobuf field <code>string right_button = 7;</code>
     */
    protected $right_button = '';
    /**
     * Generated from protobuf field <code>string extra = 8;</code>
     */
    protected $extra = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $bg_url
     *     @type string $title
     *     @type string $sub_title
     *     @type string $content
     *     @type string $content_desc
     *     @type string $left_button
     *     @type string $right_button
     *     @type string $extra
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string bg_url = 1;</code>
     * @return string
     */
    public function getBgUrl()
    {
        return $this->bg_url;
    }

    /**
     * Generated from protobuf field <code>string bg_url = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setBgUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->bg_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string title = 2;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string sub_title = 3;</code>
     * @return string
     */
    public function getSubTitle()
    {
        return $this->sub_title;
    }

    /**
     * Generated from protobuf field <code>string sub_title = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setSubTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string content = 4;</code>
     * @return string
     */
    public function getContent()
    {
        return $this->content;
    }

    /**
     * Generated from protobuf field <code>string content = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setContent($var)
    {
        GPBUtil::checkString($var, True);
        $this->content = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string content_desc = 5;</code>
     * @return string
     */
    public function getContentDesc()
    {
        return $this->content_desc;
    }

    /**
     * Generated from protobuf field <code>string content_desc = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setContentDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->content_desc = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string left_button = 6;</code>
     * @return string
     */
    public function getLeftButton()
    {
        return $this->left_button;
    }

    /**
     * Generated from protobuf field <code>string left_button = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setLeftButton($var)
    {
        GPBUtil::checkString($var, True);
        $this->left_button = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string right_button = 7;</code>
     * @return string
     */
    public function getRightButton()
    {
        return $this->right_button;
    }

    /**
     * Generated from protobuf field <code>string right_button = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setRightButton($var)
    {
        GPBUtil::checkString($var, True);
        $this->right_button = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string extra = 8;</code>
     * @return string
     */
    public function getExtra()
    {
        return $this->extra;
    }

    /**
     * Generated from protobuf field <code>string extra = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setExtra($var)
    {
        GPBUtil::checkString($var, True);
        $this->extra = $var;

        return $this;
    }

}

