<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SubmitTailorServiceRequest</code>
 */
class SubmitTailorServiceRequest extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string token = 1;</code>
     */
    protected $token = '';
    /**
     *0 预估 1 IM
     *
     * Generated from protobuf field <code>string source = 2;</code>
     */
    protected $source = '';
    /**
     *0 预估 1 IM
     *
     * Generated from protobuf field <code>string options = 3;</code>
     */
    protected $options = '';
    /**
     * Generated from protobuf field <code>int32 business_id = 4;</code>
     */
    protected $business_id = null;
    /**
     * Generated from protobuf field <code>int32 require_level = 5;</code>
     */
    protected $require_level = null;
    /**
     * Generated from protobuf field <code>string oid = 6;</code>
     */
    protected $oid = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $token
     *     @type string $source
     *          0 预估 1 IM
     *     @type string $options
     *          0 预估 1 IM
     *     @type int $business_id
     *     @type int $require_level
     *     @type string $oid
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string token = 1;</code>
     * @return string
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     * Generated from protobuf field <code>string token = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->token = $var;

        return $this;
    }

    /**
     *0 预估 1 IM
     *
     * Generated from protobuf field <code>string source = 2;</code>
     * @return string
     */
    public function getSource()
    {
        return $this->source;
    }

    /**
     *0 预估 1 IM
     *
     * Generated from protobuf field <code>string source = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setSource($var)
    {
        GPBUtil::checkString($var, True);
        $this->source = $var;

        return $this;
    }

    /**
     *0 预估 1 IM
     *
     * Generated from protobuf field <code>string options = 3;</code>
     * @return string
     */
    public function getOptions()
    {
        return $this->options;
    }

    /**
     *0 预估 1 IM
     *
     * Generated from protobuf field <code>string options = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setOptions($var)
    {
        GPBUtil::checkString($var, True);
        $this->options = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 business_id = 4;</code>
     * @return int
     */
    public function getBusinessId()
    {
        return isset($this->business_id) ? $this->business_id : 0;
    }

    public function hasBusinessId()
    {
        return isset($this->business_id);
    }

    public function clearBusinessId()
    {
        unset($this->business_id);
    }

    /**
     * Generated from protobuf field <code>int32 business_id = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setBusinessId($var)
    {
        GPBUtil::checkInt32($var);
        $this->business_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 require_level = 5;</code>
     * @return int
     */
    public function getRequireLevel()
    {
        return isset($this->require_level) ? $this->require_level : 0;
    }

    public function hasRequireLevel()
    {
        return isset($this->require_level);
    }

    public function clearRequireLevel()
    {
        unset($this->require_level);
    }

    /**
     * Generated from protobuf field <code>int32 require_level = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setRequireLevel($var)
    {
        GPBUtil::checkInt32($var);
        $this->require_level = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string oid = 6;</code>
     * @return string
     */
    public function getOid()
    {
        return isset($this->oid) ? $this->oid : '';
    }

    public function hasOid()
    {
        return isset($this->oid);
    }

    public function clearOid()
    {
        unset($this->oid);
    }

    /**
     * Generated from protobuf field <code>string oid = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setOid($var)
    {
        GPBUtil::checkString($var, True);
        $this->oid = $var;

        return $this;
    }

}

