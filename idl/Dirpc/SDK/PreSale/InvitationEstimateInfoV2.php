<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: carpool.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.InvitationEstimateInfoV2</code>
 */
class InvitationEstimateInfoV2 extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *订单类型：0实时单；1预约单
     *
     * Generated from protobuf field <code>string order_type = 1;</code>
     */
    protected $order_type = null;
    /**
     *出发时间
     *
     * Generated from protobuf field <code>string departure_range = 2;</code>
     */
    protected $departure_range = null;
    /**
     *菜单id
     *
     * Generated from protobuf field <code>string menu_id = 3;</code>
     */
    protected $menu_id = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $order_type
     *          订单类型：0实时单；1预约单
     *     @type string $departure_range
     *          出发时间
     *     @type string $menu_id
     *          菜单id
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\Carpool::initOnce();
        parent::__construct($data);
    }

    /**
     *订单类型：0实时单；1预约单
     *
     * Generated from protobuf field <code>string order_type = 1;</code>
     * @return string
     */
    public function getOrderType()
    {
        return isset($this->order_type) ? $this->order_type : '';
    }

    public function hasOrderType()
    {
        return isset($this->order_type);
    }

    public function clearOrderType()
    {
        unset($this->order_type);
    }

    /**
     *订单类型：0实时单；1预约单
     *
     * Generated from protobuf field <code>string order_type = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setOrderType($var)
    {
        GPBUtil::checkString($var, True);
        $this->order_type = $var;

        return $this;
    }

    /**
     *出发时间
     *
     * Generated from protobuf field <code>string departure_range = 2;</code>
     * @return string
     */
    public function getDepartureRange()
    {
        return isset($this->departure_range) ? $this->departure_range : '';
    }

    public function hasDepartureRange()
    {
        return isset($this->departure_range);
    }

    public function clearDepartureRange()
    {
        unset($this->departure_range);
    }

    /**
     *出发时间
     *
     * Generated from protobuf field <code>string departure_range = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setDepartureRange($var)
    {
        GPBUtil::checkString($var, True);
        $this->departure_range = $var;

        return $this;
    }

    /**
     *菜单id
     *
     * Generated from protobuf field <code>string menu_id = 3;</code>
     * @return string
     */
    public function getMenuId()
    {
        return isset($this->menu_id) ? $this->menu_id : '';
    }

    public function hasMenuId()
    {
        return isset($this->menu_id);
    }

    public function clearMenuId()
    {
        unset($this->menu_id);
    }

    /**
     *菜单id
     *
     * Generated from protobuf field <code>string menu_id = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setMenuId($var)
    {
        GPBUtil::checkString($var, True);
        $this->menu_id = $var;

        return $this;
    }

}

