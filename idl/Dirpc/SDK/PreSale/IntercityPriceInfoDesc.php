<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.IntercityPriceInfoDesc</code>
 */
class IntercityPriceInfoDesc extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *文字支持高亮
     *
     * Generated from protobuf field <code>string content = 1;</code>
     */
    protected $content = '';
    /**
     *左侧icon
     *
     * Generated from protobuf field <code>string left_icon = 2;</code>
     */
    protected $left_icon = '';
    /**
     *&#47;/ ex：抵{18}元；font_color表示 18的颜色
     *
     * Generated from protobuf field <code>string font_color = 3;</code>
     */
    protected $font_color = '';
    /**
     *背景颜色
     *
     * Generated from protobuf field <code>string bg_fill_color = 4;</code>
     */
    protected $bg_fill_color = '';
    /**
     *边框颜色
     *
     * Generated from protobuf field <code>string border_color = 5;</code>
     */
    protected $border_color = '';
    /**
     *ex：抵{18}元；font_text_color表示 抵和元的颜色
     *
     * Generated from protobuf field <code>string font_text_color = 6;</code>
     */
    protected $font_text_color = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $content
     *          文字支持高亮
     *     @type string $left_icon
     *          左侧icon
     *     @type string $font_color
     *          &#47;/ ex：抵{18}元；font_color表示 18的颜色
     *     @type string $bg_fill_color
     *          背景颜色
     *     @type string $border_color
     *          边框颜色
     *     @type string $font_text_color
     *          ex：抵{18}元；font_text_color表示 抵和元的颜色
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *文字支持高亮
     *
     * Generated from protobuf field <code>string content = 1;</code>
     * @return string
     */
    public function getContent()
    {
        return $this->content;
    }

    /**
     *文字支持高亮
     *
     * Generated from protobuf field <code>string content = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setContent($var)
    {
        GPBUtil::checkString($var, True);
        $this->content = $var;

        return $this;
    }

    /**
     *左侧icon
     *
     * Generated from protobuf field <code>string left_icon = 2;</code>
     * @return string
     */
    public function getLeftIcon()
    {
        return $this->left_icon;
    }

    /**
     *左侧icon
     *
     * Generated from protobuf field <code>string left_icon = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setLeftIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->left_icon = $var;

        return $this;
    }

    /**
     *&#47;/ ex：抵{18}元；font_color表示 18的颜色
     *
     * Generated from protobuf field <code>string font_color = 3;</code>
     * @return string
     */
    public function getFontColor()
    {
        return $this->font_color;
    }

    /**
     *&#47;/ ex：抵{18}元；font_color表示 18的颜色
     *
     * Generated from protobuf field <code>string font_color = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setFontColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->font_color = $var;

        return $this;
    }

    /**
     *背景颜色
     *
     * Generated from protobuf field <code>string bg_fill_color = 4;</code>
     * @return string
     */
    public function getBgFillColor()
    {
        return $this->bg_fill_color;
    }

    /**
     *背景颜色
     *
     * Generated from protobuf field <code>string bg_fill_color = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setBgFillColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->bg_fill_color = $var;

        return $this;
    }

    /**
     *边框颜色
     *
     * Generated from protobuf field <code>string border_color = 5;</code>
     * @return string
     */
    public function getBorderColor()
    {
        return $this->border_color;
    }

    /**
     *边框颜色
     *
     * Generated from protobuf field <code>string border_color = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setBorderColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->border_color = $var;

        return $this;
    }

    /**
     *ex：抵{18}元；font_text_color表示 抵和元的颜色
     *
     * Generated from protobuf field <code>string font_text_color = 6;</code>
     * @return string
     */
    public function getFontTextColor()
    {
        return $this->font_text_color;
    }

    /**
     *ex：抵{18}元；font_text_color表示 抵和元的颜色
     *
     * Generated from protobuf field <code>string font_text_color = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setFontTextColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->font_text_color = $var;

        return $this;
    }

}

