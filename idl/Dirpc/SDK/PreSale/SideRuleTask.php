<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideRuleTask</code>
 */
class SideRuleTask extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string task_id = 1;</code>
     */
    protected $task_id = '';
    /**
     * Generated from protobuf field <code>int32 task_status = 2;</code>
     */
    protected $task_status = 0;
    /**
     *已领取状态下的当前进度 for进度条
     *
     * Generated from protobuf field <code>int32 cur_count = 3;</code>
     */
    protected $cur_count = null;
    /**
     *已领取状态下的总进度 for进度条
     *
     * Generated from protobuf field <code>int32 total_count = 4;</code>
     */
    protected $total_count = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $task_id
     *     @type int $task_status
     *     @type int $cur_count
     *          已领取状态下的当前进度 for进度条
     *     @type int $total_count
     *          已领取状态下的总进度 for进度条
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string task_id = 1;</code>
     * @return string
     */
    public function getTaskId()
    {
        return $this->task_id;
    }

    /**
     * Generated from protobuf field <code>string task_id = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTaskId($var)
    {
        GPBUtil::checkString($var, True);
        $this->task_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 task_status = 2;</code>
     * @return int
     */
    public function getTaskStatus()
    {
        return $this->task_status;
    }

    /**
     * Generated from protobuf field <code>int32 task_status = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setTaskStatus($var)
    {
        GPBUtil::checkInt32($var);
        $this->task_status = $var;

        return $this;
    }

    /**
     *已领取状态下的当前进度 for进度条
     *
     * Generated from protobuf field <code>int32 cur_count = 3;</code>
     * @return int
     */
    public function getCurCount()
    {
        return isset($this->cur_count) ? $this->cur_count : 0;
    }

    public function hasCurCount()
    {
        return isset($this->cur_count);
    }

    public function clearCurCount()
    {
        unset($this->cur_count);
    }

    /**
     *已领取状态下的当前进度 for进度条
     *
     * Generated from protobuf field <code>int32 cur_count = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setCurCount($var)
    {
        GPBUtil::checkInt32($var);
        $this->cur_count = $var;

        return $this;
    }

    /**
     *已领取状态下的总进度 for进度条
     *
     * Generated from protobuf field <code>int32 total_count = 4;</code>
     * @return int
     */
    public function getTotalCount()
    {
        return isset($this->total_count) ? $this->total_count : 0;
    }

    public function hasTotalCount()
    {
        return isset($this->total_count);
    }

    public function clearTotalCount()
    {
        unset($this->total_count);
    }

    /**
     *已领取状态下的总进度 for进度条
     *
     * Generated from protobuf field <code>int32 total_count = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setTotalCount($var)
    {
        GPBUtil::checkInt32($var);
        $this->total_count = $var;

        return $this;
    }

}

