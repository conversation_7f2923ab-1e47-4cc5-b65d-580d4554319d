<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.PreferOption</code>
 */
class PreferOption extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 id = 1;</code>
     */
    protected $id = null;
    /**
     * Generated from protobuf field <code>string text = 2;</code>
     */
    protected $text = null;
    /**
     * Generated from protobuf field <code>bool is_select = 3;</code>
     */
    protected $is_select = null;
    /**
     * Generated from protobuf field <code>string gray_icon = 4;</code>
     */
    protected $gray_icon = null;
    /**
     * Generated from protobuf field <code>string light_icon = 5;</code>
     */
    protected $light_icon = null;
    /**
     * Generated from protobuf field <code>string toast = 6;</code>
     */
    protected $toast = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $id
     *     @type string $text
     *     @type bool $is_select
     *     @type string $gray_icon
     *     @type string $light_icon
     *     @type string $toast
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 id = 1;</code>
     * @return int
     */
    public function getId()
    {
        return isset($this->id) ? $this->id : 0;
    }

    public function hasId()
    {
        return isset($this->id);
    }

    public function clearId()
    {
        unset($this->id);
    }

    /**
     * Generated from protobuf field <code>int32 id = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setId($var)
    {
        GPBUtil::checkInt32($var);
        $this->id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string text = 2;</code>
     * @return string
     */
    public function getText()
    {
        return isset($this->text) ? $this->text : '';
    }

    public function hasText()
    {
        return isset($this->text);
    }

    public function clearText()
    {
        unset($this->text);
    }

    /**
     * Generated from protobuf field <code>string text = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setText($var)
    {
        GPBUtil::checkString($var, True);
        $this->text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool is_select = 3;</code>
     * @return bool
     */
    public function getIsSelect()
    {
        return isset($this->is_select) ? $this->is_select : false;
    }

    public function hasIsSelect()
    {
        return isset($this->is_select);
    }

    public function clearIsSelect()
    {
        unset($this->is_select);
    }

    /**
     * Generated from protobuf field <code>bool is_select = 3;</code>
     * @param bool $var
     * @return $this
     */
    public function setIsSelect($var)
    {
        GPBUtil::checkBool($var);
        $this->is_select = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string gray_icon = 4;</code>
     * @return string
     */
    public function getGrayIcon()
    {
        return isset($this->gray_icon) ? $this->gray_icon : '';
    }

    public function hasGrayIcon()
    {
        return isset($this->gray_icon);
    }

    public function clearGrayIcon()
    {
        unset($this->gray_icon);
    }

    /**
     * Generated from protobuf field <code>string gray_icon = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setGrayIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->gray_icon = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string light_icon = 5;</code>
     * @return string
     */
    public function getLightIcon()
    {
        return isset($this->light_icon) ? $this->light_icon : '';
    }

    public function hasLightIcon()
    {
        return isset($this->light_icon);
    }

    public function clearLightIcon()
    {
        unset($this->light_icon);
    }

    /**
     * Generated from protobuf field <code>string light_icon = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setLightIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->light_icon = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string toast = 6;</code>
     * @return string
     */
    public function getToast()
    {
        return isset($this->toast) ? $this->toast : '';
    }

    public function hasToast()
    {
        return isset($this->toast);
    }

    public function clearToast()
    {
        unset($this->toast);
    }

    /**
     * Generated from protobuf field <code>string toast = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setToast($var)
    {
        GPBUtil::checkString($var, True);
        $this->toast = $var;

        return $this;
    }

}

