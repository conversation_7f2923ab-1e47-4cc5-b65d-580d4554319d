<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NewFormAutoDriveRedirectionInfo</code>
 */
class NewFormAutoDriveRedirectionInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *无人车上车点
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormRedirectionInfo start_info = 1;</code>
     */
    protected $start_info = null;
    /**
     *无人车下车点
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormRedirectionInfo end_info = 2;</code>
     */
    protected $end_info = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\NewFormRedirectionInfo $start_info
     *          无人车上车点
     *     @type \Dirpc\SDK\PreSale\NewFormRedirectionInfo $end_info
     *          无人车下车点
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *无人车上车点
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormRedirectionInfo start_info = 1;</code>
     * @return \Dirpc\SDK\PreSale\NewFormRedirectionInfo
     */
    public function getStartInfo()
    {
        return isset($this->start_info) ? $this->start_info : null;
    }

    public function hasStartInfo()
    {
        return isset($this->start_info);
    }

    public function clearStartInfo()
    {
        unset($this->start_info);
    }

    /**
     *无人车上车点
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormRedirectionInfo start_info = 1;</code>
     * @param \Dirpc\SDK\PreSale\NewFormRedirectionInfo $var
     * @return $this
     */
    public function setStartInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NewFormRedirectionInfo::class);
        $this->start_info = $var;

        return $this;
    }

    /**
     *无人车下车点
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormRedirectionInfo end_info = 2;</code>
     * @return \Dirpc\SDK\PreSale\NewFormRedirectionInfo
     */
    public function getEndInfo()
    {
        return isset($this->end_info) ? $this->end_info : null;
    }

    public function hasEndInfo()
    {
        return isset($this->end_info);
    }

    public function clearEndInfo()
    {
        unset($this->end_info);
    }

    /**
     *无人车下车点
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormRedirectionInfo end_info = 2;</code>
     * @param \Dirpc\SDK\PreSale\NewFormRedirectionInfo $var
     * @return $this
     */
    public function setEndInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NewFormRedirectionInfo::class);
        $this->end_info = $var;

        return $this;
    }

}

