<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.RightSubTitle</code>
 */
class RightSubTitle extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string content = 1;</code>
     */
    protected $content = '';
    /**
     * Generated from protobuf field <code>string font_color = 2;</code>
     */
    protected $font_color = '';
    /**
     * Generated from protobuf field <code>string border_color = 3;</code>
     */
    protected $border_color = '';
    /**
     * Generated from protobuf field <code>repeated string bg_gradients = 4;</code>
     */
    private $bg_gradients;
    /**
     * Generated from protobuf field <code>string icon_url = 5;</code>
     */
    protected $icon_url = '';
    /**
     * Generated from protobuf field <code>string highlight_color = 6;</code>
     */
    protected $highlight_color = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $content
     *     @type string $font_color
     *     @type string $border_color
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $bg_gradients
     *     @type string $icon_url
     *     @type string $highlight_color
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string content = 1;</code>
     * @return string
     */
    public function getContent()
    {
        return $this->content;
    }

    /**
     * Generated from protobuf field <code>string content = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setContent($var)
    {
        GPBUtil::checkString($var, True);
        $this->content = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string font_color = 2;</code>
     * @return string
     */
    public function getFontColor()
    {
        return $this->font_color;
    }

    /**
     * Generated from protobuf field <code>string font_color = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setFontColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->font_color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string border_color = 3;</code>
     * @return string
     */
    public function getBorderColor()
    {
        return $this->border_color;
    }

    /**
     * Generated from protobuf field <code>string border_color = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setBorderColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->border_color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated string bg_gradients = 4;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getBgGradients()
    {
        return $this->bg_gradients;
    }

    /**
     * Generated from protobuf field <code>repeated string bg_gradients = 4;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBgGradients($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->bg_gradients = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string icon_url = 5;</code>
     * @return string
     */
    public function getIconUrl()
    {
        return $this->icon_url;
    }

    /**
     * Generated from protobuf field <code>string icon_url = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setIconUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->icon_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string highlight_color = 6;</code>
     * @return string
     */
    public function getHighlightColor()
    {
        return $this->highlight_color;
    }

    /**
     * Generated from protobuf field <code>string highlight_color = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setHighlightColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->highlight_color = $var;

        return $this;
    }

}

