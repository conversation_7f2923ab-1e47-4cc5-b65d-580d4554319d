<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.GuideTarget</code>
 */
class GuideTarget extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *30KM以上推荐{xxx}, 含颜色
     *
     * Generated from protobuf field <code>string product_msg = 1;</code>
     */
    protected $product_msg = '';
    /**
     *30KM以上推荐{xxx}, 含颜色
     *
     * Generated from protobuf field <code>string product_desc = 2;</code>
     */
    protected $product_desc = '';
    /**
     *天天特价
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ProductTag product_tag = 3;</code>
     */
    private $product_tag;
    /**
     *按钮文案
     *
     * Generated from protobuf field <code>string btn_content = 4;</code>
     */
    protected $btn_content = '';
    /**
     *导流到哪个顶导
     *
     * Generated from protobuf field <code>string menu_id = 5;</code>
     */
    protected $menu_id = '';
    /**
     *导流到哪个页面
     *
     * Generated from protobuf field <code>int32 page_type = 6;</code>
     */
    protected $page_type = 0;
    /**
     *导流到页面的名称
     *
     * Generated from protobuf field <code>string menu_name = 7;</code>
     */
    protected $menu_name = '';
    /**
     *跳转链接
     *
     * Generated from protobuf field <code>string to_link = 8;</code>
     */
    protected $to_link = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $product_msg
     *          30KM以上推荐{xxx}, 含颜色
     *     @type string $product_desc
     *          30KM以上推荐{xxx}, 含颜色
     *     @type \Dirpc\SDK\PreSale\ProductTag[]|\Nuwa\Protobuf\Internal\RepeatedField $product_tag
     *          天天特价
     *     @type string $btn_content
     *          按钮文案
     *     @type string $menu_id
     *          导流到哪个顶导
     *     @type int $page_type
     *          导流到哪个页面
     *     @type string $menu_name
     *          导流到页面的名称
     *     @type string $to_link
     *          跳转链接
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *30KM以上推荐{xxx}, 含颜色
     *
     * Generated from protobuf field <code>string product_msg = 1;</code>
     * @return string
     */
    public function getProductMsg()
    {
        return $this->product_msg;
    }

    /**
     *30KM以上推荐{xxx}, 含颜色
     *
     * Generated from protobuf field <code>string product_msg = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setProductMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_msg = $var;

        return $this;
    }

    /**
     *30KM以上推荐{xxx}, 含颜色
     *
     * Generated from protobuf field <code>string product_desc = 2;</code>
     * @return string
     */
    public function getProductDesc()
    {
        return $this->product_desc;
    }

    /**
     *30KM以上推荐{xxx}, 含颜色
     *
     * Generated from protobuf field <code>string product_desc = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setProductDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_desc = $var;

        return $this;
    }

    /**
     *天天特价
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ProductTag product_tag = 3;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getProductTag()
    {
        return $this->product_tag;
    }

    /**
     *天天特价
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ProductTag product_tag = 3;</code>
     * @param \Dirpc\SDK\PreSale\ProductTag[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setProductTag($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\ProductTag::class);
        $this->product_tag = $arr;

        return $this;
    }

    /**
     *按钮文案
     *
     * Generated from protobuf field <code>string btn_content = 4;</code>
     * @return string
     */
    public function getBtnContent()
    {
        return $this->btn_content;
    }

    /**
     *按钮文案
     *
     * Generated from protobuf field <code>string btn_content = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setBtnContent($var)
    {
        GPBUtil::checkString($var, True);
        $this->btn_content = $var;

        return $this;
    }

    /**
     *导流到哪个顶导
     *
     * Generated from protobuf field <code>string menu_id = 5;</code>
     * @return string
     */
    public function getMenuId()
    {
        return $this->menu_id;
    }

    /**
     *导流到哪个顶导
     *
     * Generated from protobuf field <code>string menu_id = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setMenuId($var)
    {
        GPBUtil::checkString($var, True);
        $this->menu_id = $var;

        return $this;
    }

    /**
     *导流到哪个页面
     *
     * Generated from protobuf field <code>int32 page_type = 6;</code>
     * @return int
     */
    public function getPageType()
    {
        return $this->page_type;
    }

    /**
     *导流到哪个页面
     *
     * Generated from protobuf field <code>int32 page_type = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setPageType($var)
    {
        GPBUtil::checkInt32($var);
        $this->page_type = $var;

        return $this;
    }

    /**
     *导流到页面的名称
     *
     * Generated from protobuf field <code>string menu_name = 7;</code>
     * @return string
     */
    public function getMenuName()
    {
        return $this->menu_name;
    }

    /**
     *导流到页面的名称
     *
     * Generated from protobuf field <code>string menu_name = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setMenuName($var)
    {
        GPBUtil::checkString($var, True);
        $this->menu_name = $var;

        return $this;
    }

    /**
     *跳转链接
     *
     * Generated from protobuf field <code>string to_link = 8;</code>
     * @return string
     */
    public function getToLink()
    {
        return $this->to_link;
    }

    /**
     *跳转链接
     *
     * Generated from protobuf field <code>string to_link = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setToLink($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_link = $var;

        return $this;
    }

}

