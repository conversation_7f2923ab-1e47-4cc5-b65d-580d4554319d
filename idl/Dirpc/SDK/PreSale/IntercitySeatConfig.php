<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.IntercitySeatConfig</code>
 */
class IntercitySeatConfig extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 value = 1;</code>
     */
    protected $value = 0;
    /**
     * Generated from protobuf field <code>string label_2 = 2;</code>
     */
    protected $label_2 = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $value
     *     @type string $label_2
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 value = 1;</code>
     * @return int
     */
    public function getValue()
    {
        return $this->value;
    }

    /**
     * Generated from protobuf field <code>int32 value = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setValue($var)
    {
        GPBUtil::checkInt32($var);
        $this->value = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string label_2 = 2;</code>
     * @return string
     */
    public function getLabel2()
    {
        return $this->label_2;
    }

    /**
     * Generated from protobuf field <code>string label_2 = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setLabel2($var)
    {
        GPBUtil::checkString($var, True);
        $this->label_2 = $var;

        return $this;
    }

}

