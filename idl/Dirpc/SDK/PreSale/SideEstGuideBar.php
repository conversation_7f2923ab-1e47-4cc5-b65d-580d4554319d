<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideEstGuideBar</code>
 */
class SideEstGuideBar extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *渲染样式：1代表强推荐 / 2代表弱推荐
     *
     * Generated from protobuf field <code>int64 style_type = 1;</code>
     */
    protected $style_type = 0;
    /**
     *操作行为：1代表跳页面 / 2代表toast / 3 代表跳页面，销毁原页面 / 4 发单
     *
     * Generated from protobuf field <code>int64 action_type = 2;</code>
     */
    protected $action_type = 0;
    /**
     *操作行为：1代表跳页面 / 2代表toast / 3 代表跳页面，销毁原页面 / 4 发单
     *
     * Generated from protobuf field <code>string left_icon = 3;</code>
     */
    protected $left_icon = '';
    /**
     * Generated from protobuf field <code>string title = 4;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SideEstSubTitle sub_title_list = 5;</code>
     */
    private $sub_title_list;
    /**
     * Generated from protobuf field <code>string fee_msg = 6;</code>
     */
    protected $fee_msg = '';
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SideEstFeeDesc fee_desc_list = 7;</code>
     */
    private $fee_desc_list;
    /**
     * Generated from protobuf field <code>string recommend_toast = 8;</code>
     */
    protected $recommend_toast = '';
    /**
     * Generated from protobuf field <code>string button_text = 9;</code>
     */
    protected $button_text = '';
    /**
     *pick_on_time:必有车
     *
     * Generated from protobuf field <code>string business_type = 10;</code>
     */
    protected $business_type = '';
    /**
     *pick_on_time:必有车
     *
     * Generated from protobuf field <code>string jump_uri = 11;</code>
     */
    protected $jump_uri = '';
    /**
     * Generated from protobuf field <code>string estimate_id = 12;</code>
     */
    protected $estimate_id = '';
    /**
     *费用明细链接，目前只有必有车使用
     *
     * Generated from protobuf field <code>string fee_detail_url = 13;</code>
     */
    protected $fee_detail_url = '';
    /**
     *按钮详细信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.GuideBarButtonInfo button_info = 14;</code>
     */
    protected $button_info = null;
    /**
     *导流的背景色
     *
     * Generated from protobuf field <code>repeated string background_gradients = 15;</code>
     */
    private $background_gradients;
    /**
     *必有车预估信息，供发单使用
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PNewOrderParams pneworder_params_from_side = 16;</code>
     */
    protected $pneworder_params_from_side = null;
    /**
     *包框颜色
     *
     * Generated from protobuf field <code>string border_color = 17;</code>
     */
    protected $border_color = '';
    /**
     *跳转的tab id
     *
     * Generated from protobuf field <code>string guide_path = 18;</code>
     */
    protected $guide_path = null;
    /**
     *跳转参数
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideActionParams guide_params = 19;</code>
     */
    protected $guide_params = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $style_type
     *          渲染样式：1代表强推荐 / 2代表弱推荐
     *     @type int|string $action_type
     *          操作行为：1代表跳页面 / 2代表toast / 3 代表跳页面，销毁原页面 / 4 发单
     *     @type string $left_icon
     *          操作行为：1代表跳页面 / 2代表toast / 3 代表跳页面，销毁原页面 / 4 发单
     *     @type string $title
     *     @type \Dirpc\SDK\PreSale\SideEstSubTitle[]|\Nuwa\Protobuf\Internal\RepeatedField $sub_title_list
     *     @type string $fee_msg
     *     @type \Dirpc\SDK\PreSale\SideEstFeeDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $fee_desc_list
     *     @type string $recommend_toast
     *     @type string $button_text
     *     @type string $business_type
     *          pick_on_time:必有车
     *     @type string $jump_uri
     *          pick_on_time:必有车
     *     @type string $estimate_id
     *     @type string $fee_detail_url
     *          费用明细链接，目前只有必有车使用
     *     @type \Dirpc\SDK\PreSale\GuideBarButtonInfo $button_info
     *          按钮详细信息
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $background_gradients
     *          导流的背景色
     *     @type \Dirpc\SDK\PreSale\PNewOrderParams $pneworder_params_from_side
     *          必有车预估信息，供发单使用
     *     @type string $border_color
     *          包框颜色
     *     @type string $guide_path
     *          跳转的tab id
     *     @type \Dirpc\SDK\PreSale\SideActionParams $guide_params
     *          跳转参数
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     *渲染样式：1代表强推荐 / 2代表弱推荐
     *
     * Generated from protobuf field <code>int64 style_type = 1;</code>
     * @return int|string
     */
    public function getStyleType()
    {
        return $this->style_type;
    }

    /**
     *渲染样式：1代表强推荐 / 2代表弱推荐
     *
     * Generated from protobuf field <code>int64 style_type = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setStyleType($var)
    {
        GPBUtil::checkInt64($var);
        $this->style_type = $var;

        return $this;
    }

    /**
     *操作行为：1代表跳页面 / 2代表toast / 3 代表跳页面，销毁原页面 / 4 发单
     *
     * Generated from protobuf field <code>int64 action_type = 2;</code>
     * @return int|string
     */
    public function getActionType()
    {
        return $this->action_type;
    }

    /**
     *操作行为：1代表跳页面 / 2代表toast / 3 代表跳页面，销毁原页面 / 4 发单
     *
     * Generated from protobuf field <code>int64 action_type = 2;</code>
     * @param int|string $var
     * @return $this
     */
    public function setActionType($var)
    {
        GPBUtil::checkInt64($var);
        $this->action_type = $var;

        return $this;
    }

    /**
     *操作行为：1代表跳页面 / 2代表toast / 3 代表跳页面，销毁原页面 / 4 发单
     *
     * Generated from protobuf field <code>string left_icon = 3;</code>
     * @return string
     */
    public function getLeftIcon()
    {
        return $this->left_icon;
    }

    /**
     *操作行为：1代表跳页面 / 2代表toast / 3 代表跳页面，销毁原页面 / 4 发单
     *
     * Generated from protobuf field <code>string left_icon = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setLeftIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->left_icon = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string title = 4;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SideEstSubTitle sub_title_list = 5;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getSubTitleList()
    {
        return $this->sub_title_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SideEstSubTitle sub_title_list = 5;</code>
     * @param \Dirpc\SDK\PreSale\SideEstSubTitle[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSubTitleList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\SideEstSubTitle::class);
        $this->sub_title_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_msg = 6;</code>
     * @return string
     */
    public function getFeeMsg()
    {
        return $this->fee_msg;
    }

    /**
     * Generated from protobuf field <code>string fee_msg = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SideEstFeeDesc fee_desc_list = 7;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getFeeDescList()
    {
        return $this->fee_desc_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SideEstFeeDesc fee_desc_list = 7;</code>
     * @param \Dirpc\SDK\PreSale\SideEstFeeDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFeeDescList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\SideEstFeeDesc::class);
        $this->fee_desc_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string recommend_toast = 8;</code>
     * @return string
     */
    public function getRecommendToast()
    {
        return $this->recommend_toast;
    }

    /**
     * Generated from protobuf field <code>string recommend_toast = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setRecommendToast($var)
    {
        GPBUtil::checkString($var, True);
        $this->recommend_toast = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string button_text = 9;</code>
     * @return string
     */
    public function getButtonText()
    {
        return $this->button_text;
    }

    /**
     * Generated from protobuf field <code>string button_text = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setButtonText($var)
    {
        GPBUtil::checkString($var, True);
        $this->button_text = $var;

        return $this;
    }

    /**
     *pick_on_time:必有车
     *
     * Generated from protobuf field <code>string business_type = 10;</code>
     * @return string
     */
    public function getBusinessType()
    {
        return $this->business_type;
    }

    /**
     *pick_on_time:必有车
     *
     * Generated from protobuf field <code>string business_type = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setBusinessType($var)
    {
        GPBUtil::checkString($var, True);
        $this->business_type = $var;

        return $this;
    }

    /**
     *pick_on_time:必有车
     *
     * Generated from protobuf field <code>string jump_uri = 11;</code>
     * @return string
     */
    public function getJumpUri()
    {
        return $this->jump_uri;
    }

    /**
     *pick_on_time:必有车
     *
     * Generated from protobuf field <code>string jump_uri = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setJumpUri($var)
    {
        GPBUtil::checkString($var, True);
        $this->jump_uri = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string estimate_id = 12;</code>
     * @return string
     */
    public function getEstimateId()
    {
        return $this->estimate_id;
    }

    /**
     * Generated from protobuf field <code>string estimate_id = 12;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateId($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_id = $var;

        return $this;
    }

    /**
     *费用明细链接，目前只有必有车使用
     *
     * Generated from protobuf field <code>string fee_detail_url = 13;</code>
     * @return string
     */
    public function getFeeDetailUrl()
    {
        return $this->fee_detail_url;
    }

    /**
     *费用明细链接，目前只有必有车使用
     *
     * Generated from protobuf field <code>string fee_detail_url = 13;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeDetailUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_detail_url = $var;

        return $this;
    }

    /**
     *按钮详细信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.GuideBarButtonInfo button_info = 14;</code>
     * @return \Dirpc\SDK\PreSale\GuideBarButtonInfo
     */
    public function getButtonInfo()
    {
        return isset($this->button_info) ? $this->button_info : null;
    }

    public function hasButtonInfo()
    {
        return isset($this->button_info);
    }

    public function clearButtonInfo()
    {
        unset($this->button_info);
    }

    /**
     *按钮详细信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.GuideBarButtonInfo button_info = 14;</code>
     * @param \Dirpc\SDK\PreSale\GuideBarButtonInfo $var
     * @return $this
     */
    public function setButtonInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\GuideBarButtonInfo::class);
        $this->button_info = $var;

        return $this;
    }

    /**
     *导流的背景色
     *
     * Generated from protobuf field <code>repeated string background_gradients = 15;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getBackgroundGradients()
    {
        return $this->background_gradients;
    }

    /**
     *导流的背景色
     *
     * Generated from protobuf field <code>repeated string background_gradients = 15;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBackgroundGradients($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->background_gradients = $arr;

        return $this;
    }

    /**
     *必有车预估信息，供发单使用
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PNewOrderParams pneworder_params_from_side = 16;</code>
     * @return \Dirpc\SDK\PreSale\PNewOrderParams
     */
    public function getPneworderParamsFromSide()
    {
        return isset($this->pneworder_params_from_side) ? $this->pneworder_params_from_side : null;
    }

    public function hasPneworderParamsFromSide()
    {
        return isset($this->pneworder_params_from_side);
    }

    public function clearPneworderParamsFromSide()
    {
        unset($this->pneworder_params_from_side);
    }

    /**
     *必有车预估信息，供发单使用
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PNewOrderParams pneworder_params_from_side = 16;</code>
     * @param \Dirpc\SDK\PreSale\PNewOrderParams $var
     * @return $this
     */
    public function setPneworderParamsFromSide($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\PNewOrderParams::class);
        $this->pneworder_params_from_side = $var;

        return $this;
    }

    /**
     *包框颜色
     *
     * Generated from protobuf field <code>string border_color = 17;</code>
     * @return string
     */
    public function getBorderColor()
    {
        return $this->border_color;
    }

    /**
     *包框颜色
     *
     * Generated from protobuf field <code>string border_color = 17;</code>
     * @param string $var
     * @return $this
     */
    public function setBorderColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->border_color = $var;

        return $this;
    }

    /**
     *跳转的tab id
     *
     * Generated from protobuf field <code>string guide_path = 18;</code>
     * @return string
     */
    public function getGuidePath()
    {
        return isset($this->guide_path) ? $this->guide_path : '';
    }

    public function hasGuidePath()
    {
        return isset($this->guide_path);
    }

    public function clearGuidePath()
    {
        unset($this->guide_path);
    }

    /**
     *跳转的tab id
     *
     * Generated from protobuf field <code>string guide_path = 18;</code>
     * @param string $var
     * @return $this
     */
    public function setGuidePath($var)
    {
        GPBUtil::checkString($var, True);
        $this->guide_path = $var;

        return $this;
    }

    /**
     *跳转参数
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideActionParams guide_params = 19;</code>
     * @return \Dirpc\SDK\PreSale\SideActionParams
     */
    public function getGuideParams()
    {
        return isset($this->guide_params) ? $this->guide_params : null;
    }

    public function hasGuideParams()
    {
        return isset($this->guide_params);
    }

    public function clearGuideParams()
    {
        unset($this->guide_params);
    }

    /**
     *跳转参数
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideActionParams guide_params = 19;</code>
     * @param \Dirpc\SDK\PreSale\SideActionParams $var
     * @return $this
     */
    public function setGuideParams($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\SideActionParams::class);
        $this->guide_params = $var;

        return $this;
    }

}

