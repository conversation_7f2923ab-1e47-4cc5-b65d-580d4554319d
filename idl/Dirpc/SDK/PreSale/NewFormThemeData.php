<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NewFormThemeData</code>
 */
class NewFormThemeData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 theme_type = 1;</code>
     */
    protected $theme_type = null;
    /**
     *主题颜色
     *
     * Generated from protobuf field <code>string theme_color = 2;</code>
     */
    protected $theme_color = null;
    /**
     *选中品类背景渐变色
     *
     * Generated from protobuf field <code>repeated string selected_bg_gradients = 3;</code>
     */
    private $selected_bg_gradients;
    /**
     *品类外框背景渐变色
     *
     * Generated from protobuf field <code>repeated string outer_bg_gradients = 4;</code>
     */
    private $outer_bg_gradients;
    /**
     *顶部推荐标题
     *
     * Generated from protobuf field <code>string title = 5;</code>
     */
    protected $title = null;
    /**
     *标题左侧Icon
     *
     * Generated from protobuf field <code>string icon = 6;</code>
     */
    protected $icon = null;
    /**
     *右侧标题
     *
     * Generated from protobuf field <code>string right_text = 7;</code>
     */
    protected $right_text = null;
    /**
     *右侧icon
     *
     * Generated from protobuf field <code>string right_icon = 8;</code>
     */
    protected $right_icon = null;
    /**
     *0.5盒子 右侧跳转链接
     *
     * Generated from protobuf field <code>string right_info_url = 9;</code>
     */
    protected $right_info_url = null;
    /**
     *倒计时(秒)  (当前时间-券过期时间)/1000
     *
     * Generated from protobuf field <code>int64 expire_time = 10;</code>
     */
    protected $expire_time = null;
    /**
     *是否展示背景色
     *
     * Generated from protobuf field <code>int64 disable_selected_bg = 11;</code>
     */
    protected $disable_selected_bg = null;
    /**
     *字体颜色
     *
     * Generated from protobuf field <code>string text_color = 12;</code>
     */
    protected $text_color = null;
    /**
     *字体颜色
     *
     * Generated from protobuf field <code>string border_color = 13;</code>
     */
    protected $border_color = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $theme_type
     *     @type string $theme_color
     *          主题颜色
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $selected_bg_gradients
     *          选中品类背景渐变色
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $outer_bg_gradients
     *          品类外框背景渐变色
     *     @type string $title
     *          顶部推荐标题
     *     @type string $icon
     *          标题左侧Icon
     *     @type string $right_text
     *          右侧标题
     *     @type string $right_icon
     *          右侧icon
     *     @type string $right_info_url
     *          0.5盒子 右侧跳转链接
     *     @type int|string $expire_time
     *          倒计时(秒)  (当前时间-券过期时间)/1000
     *     @type int|string $disable_selected_bg
     *          是否展示背景色
     *     @type string $text_color
     *          字体颜色
     *     @type string $border_color
     *          字体颜色
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 theme_type = 1;</code>
     * @return int
     */
    public function getThemeType()
    {
        return isset($this->theme_type) ? $this->theme_type : 0;
    }

    public function hasThemeType()
    {
        return isset($this->theme_type);
    }

    public function clearThemeType()
    {
        unset($this->theme_type);
    }

    /**
     * Generated from protobuf field <code>int32 theme_type = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setThemeType($var)
    {
        GPBUtil::checkInt32($var);
        $this->theme_type = $var;

        return $this;
    }

    /**
     *主题颜色
     *
     * Generated from protobuf field <code>string theme_color = 2;</code>
     * @return string
     */
    public function getThemeColor()
    {
        return isset($this->theme_color) ? $this->theme_color : '';
    }

    public function hasThemeColor()
    {
        return isset($this->theme_color);
    }

    public function clearThemeColor()
    {
        unset($this->theme_color);
    }

    /**
     *主题颜色
     *
     * Generated from protobuf field <code>string theme_color = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setThemeColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->theme_color = $var;

        return $this;
    }

    /**
     *选中品类背景渐变色
     *
     * Generated from protobuf field <code>repeated string selected_bg_gradients = 3;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getSelectedBgGradients()
    {
        return $this->selected_bg_gradients;
    }

    /**
     *选中品类背景渐变色
     *
     * Generated from protobuf field <code>repeated string selected_bg_gradients = 3;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSelectedBgGradients($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->selected_bg_gradients = $arr;

        return $this;
    }

    /**
     *品类外框背景渐变色
     *
     * Generated from protobuf field <code>repeated string outer_bg_gradients = 4;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getOuterBgGradients()
    {
        return $this->outer_bg_gradients;
    }

    /**
     *品类外框背景渐变色
     *
     * Generated from protobuf field <code>repeated string outer_bg_gradients = 4;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setOuterBgGradients($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->outer_bg_gradients = $arr;

        return $this;
    }

    /**
     *顶部推荐标题
     *
     * Generated from protobuf field <code>string title = 5;</code>
     * @return string
     */
    public function getTitle()
    {
        return isset($this->title) ? $this->title : '';
    }

    public function hasTitle()
    {
        return isset($this->title);
    }

    public function clearTitle()
    {
        unset($this->title);
    }

    /**
     *顶部推荐标题
     *
     * Generated from protobuf field <code>string title = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     *标题左侧Icon
     *
     * Generated from protobuf field <code>string icon = 6;</code>
     * @return string
     */
    public function getIcon()
    {
        return isset($this->icon) ? $this->icon : '';
    }

    public function hasIcon()
    {
        return isset($this->icon);
    }

    public function clearIcon()
    {
        unset($this->icon);
    }

    /**
     *标题左侧Icon
     *
     * Generated from protobuf field <code>string icon = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->icon = $var;

        return $this;
    }

    /**
     *右侧标题
     *
     * Generated from protobuf field <code>string right_text = 7;</code>
     * @return string
     */
    public function getRightText()
    {
        return isset($this->right_text) ? $this->right_text : '';
    }

    public function hasRightText()
    {
        return isset($this->right_text);
    }

    public function clearRightText()
    {
        unset($this->right_text);
    }

    /**
     *右侧标题
     *
     * Generated from protobuf field <code>string right_text = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setRightText($var)
    {
        GPBUtil::checkString($var, True);
        $this->right_text = $var;

        return $this;
    }

    /**
     *右侧icon
     *
     * Generated from protobuf field <code>string right_icon = 8;</code>
     * @return string
     */
    public function getRightIcon()
    {
        return isset($this->right_icon) ? $this->right_icon : '';
    }

    public function hasRightIcon()
    {
        return isset($this->right_icon);
    }

    public function clearRightIcon()
    {
        unset($this->right_icon);
    }

    /**
     *右侧icon
     *
     * Generated from protobuf field <code>string right_icon = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setRightIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->right_icon = $var;

        return $this;
    }

    /**
     *0.5盒子 右侧跳转链接
     *
     * Generated from protobuf field <code>string right_info_url = 9;</code>
     * @return string
     */
    public function getRightInfoUrl()
    {
        return isset($this->right_info_url) ? $this->right_info_url : '';
    }

    public function hasRightInfoUrl()
    {
        return isset($this->right_info_url);
    }

    public function clearRightInfoUrl()
    {
        unset($this->right_info_url);
    }

    /**
     *0.5盒子 右侧跳转链接
     *
     * Generated from protobuf field <code>string right_info_url = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setRightInfoUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->right_info_url = $var;

        return $this;
    }

    /**
     *倒计时(秒)  (当前时间-券过期时间)/1000
     *
     * Generated from protobuf field <code>int64 expire_time = 10;</code>
     * @return int|string
     */
    public function getExpireTime()
    {
        return isset($this->expire_time) ? $this->expire_time : 0;
    }

    public function hasExpireTime()
    {
        return isset($this->expire_time);
    }

    public function clearExpireTime()
    {
        unset($this->expire_time);
    }

    /**
     *倒计时(秒)  (当前时间-券过期时间)/1000
     *
     * Generated from protobuf field <code>int64 expire_time = 10;</code>
     * @param int|string $var
     * @return $this
     */
    public function setExpireTime($var)
    {
        GPBUtil::checkInt64($var);
        $this->expire_time = $var;

        return $this;
    }

    /**
     *是否展示背景色
     *
     * Generated from protobuf field <code>int64 disable_selected_bg = 11;</code>
     * @return int|string
     */
    public function getDisableSelectedBg()
    {
        return isset($this->disable_selected_bg) ? $this->disable_selected_bg : 0;
    }

    public function hasDisableSelectedBg()
    {
        return isset($this->disable_selected_bg);
    }

    public function clearDisableSelectedBg()
    {
        unset($this->disable_selected_bg);
    }

    /**
     *是否展示背景色
     *
     * Generated from protobuf field <code>int64 disable_selected_bg = 11;</code>
     * @param int|string $var
     * @return $this
     */
    public function setDisableSelectedBg($var)
    {
        GPBUtil::checkInt64($var);
        $this->disable_selected_bg = $var;

        return $this;
    }

    /**
     *字体颜色
     *
     * Generated from protobuf field <code>string text_color = 12;</code>
     * @return string
     */
    public function getTextColor()
    {
        return isset($this->text_color) ? $this->text_color : '';
    }

    public function hasTextColor()
    {
        return isset($this->text_color);
    }

    public function clearTextColor()
    {
        unset($this->text_color);
    }

    /**
     *字体颜色
     *
     * Generated from protobuf field <code>string text_color = 12;</code>
     * @param string $var
     * @return $this
     */
    public function setTextColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->text_color = $var;

        return $this;
    }

    /**
     *字体颜色
     *
     * Generated from protobuf field <code>string border_color = 13;</code>
     * @return string
     */
    public function getBorderColor()
    {
        return isset($this->border_color) ? $this->border_color : '';
    }

    public function hasBorderColor()
    {
        return isset($this->border_color);
    }

    public function clearBorderColor()
    {
        unset($this->border_color);
    }

    /**
     *字体颜色
     *
     * Generated from protobuf field <code>string border_color = 13;</code>
     * @param string $var
     * @return $this
     */
    public function setBorderColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->border_color = $var;

        return $this;
    }

}

