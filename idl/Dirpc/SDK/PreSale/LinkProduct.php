<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.LinkProduct</code>
 */
class LinkProduct extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *默认文案
     *
     * Generated from protobuf field <code>string default_text = 1;</code>
     */
    protected $default_text = null;
    /**
     *选中文案
     *
     * Generated from protobuf field <code>string select_text = 2;</code>
     */
    protected $select_text = null;
    /**
     *是否选中
     *
     * Generated from protobuf field <code>int32 is_select = 3;</code>
     */
    protected $is_select = null;
    /**
     *附着其他品类id
     *
     * Generated from protobuf field <code>int32 product_category = 4;</code>
     */
    protected $product_category = 0;
    /**
     *拼车选中文案
     *
     * Generated from protobuf field <code>string carpool_select_text = 5;</code>
     */
    protected $carpool_select_text = null;
    /**
     *拼车价格文案
     *
     * Generated from protobuf field <code>string carpool_success_text = 6;</code>
     */
    protected $carpool_success_text = null;
    /**
     *勾选了link的品类后 = 发单两个品类
     *
     * Generated from protobuf field <code>int32 is_multi_select = 7;</code>
     */
    protected $is_multi_select = null;
    /**
     *小 i 的跳转地址
     *
     * Generated from protobuf field <code>string info_url = 8;</code>
     */
    protected $info_url = null;
    /**
     *左icon
     *
     * Generated from protobuf field <code>string icon_url = 9;</code>
     */
    protected $icon_url = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $default_text
     *          默认文案
     *     @type string $select_text
     *          选中文案
     *     @type int $is_select
     *          是否选中
     *     @type int $product_category
     *          附着其他品类id
     *     @type string $carpool_select_text
     *          拼车选中文案
     *     @type string $carpool_success_text
     *          拼车价格文案
     *     @type int $is_multi_select
     *          勾选了link的品类后 = 发单两个品类
     *     @type string $info_url
     *          小 i 的跳转地址
     *     @type string $icon_url
     *          左icon
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *默认文案
     *
     * Generated from protobuf field <code>string default_text = 1;</code>
     * @return string
     */
    public function getDefaultText()
    {
        return isset($this->default_text) ? $this->default_text : '';
    }

    public function hasDefaultText()
    {
        return isset($this->default_text);
    }

    public function clearDefaultText()
    {
        unset($this->default_text);
    }

    /**
     *默认文案
     *
     * Generated from protobuf field <code>string default_text = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setDefaultText($var)
    {
        GPBUtil::checkString($var, True);
        $this->default_text = $var;

        return $this;
    }

    /**
     *选中文案
     *
     * Generated from protobuf field <code>string select_text = 2;</code>
     * @return string
     */
    public function getSelectText()
    {
        return isset($this->select_text) ? $this->select_text : '';
    }

    public function hasSelectText()
    {
        return isset($this->select_text);
    }

    public function clearSelectText()
    {
        unset($this->select_text);
    }

    /**
     *选中文案
     *
     * Generated from protobuf field <code>string select_text = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setSelectText($var)
    {
        GPBUtil::checkString($var, True);
        $this->select_text = $var;

        return $this;
    }

    /**
     *是否选中
     *
     * Generated from protobuf field <code>int32 is_select = 3;</code>
     * @return int
     */
    public function getIsSelect()
    {
        return isset($this->is_select) ? $this->is_select : 0;
    }

    public function hasIsSelect()
    {
        return isset($this->is_select);
    }

    public function clearIsSelect()
    {
        unset($this->is_select);
    }

    /**
     *是否选中
     *
     * Generated from protobuf field <code>int32 is_select = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setIsSelect($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_select = $var;

        return $this;
    }

    /**
     *附着其他品类id
     *
     * Generated from protobuf field <code>int32 product_category = 4;</code>
     * @return int
     */
    public function getProductCategory()
    {
        return $this->product_category;
    }

    /**
     *附着其他品类id
     *
     * Generated from protobuf field <code>int32 product_category = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setProductCategory($var)
    {
        GPBUtil::checkInt32($var);
        $this->product_category = $var;

        return $this;
    }

    /**
     *拼车选中文案
     *
     * Generated from protobuf field <code>string carpool_select_text = 5;</code>
     * @return string
     */
    public function getCarpoolSelectText()
    {
        return isset($this->carpool_select_text) ? $this->carpool_select_text : '';
    }

    public function hasCarpoolSelectText()
    {
        return isset($this->carpool_select_text);
    }

    public function clearCarpoolSelectText()
    {
        unset($this->carpool_select_text);
    }

    /**
     *拼车选中文案
     *
     * Generated from protobuf field <code>string carpool_select_text = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setCarpoolSelectText($var)
    {
        GPBUtil::checkString($var, True);
        $this->carpool_select_text = $var;

        return $this;
    }

    /**
     *拼车价格文案
     *
     * Generated from protobuf field <code>string carpool_success_text = 6;</code>
     * @return string
     */
    public function getCarpoolSuccessText()
    {
        return isset($this->carpool_success_text) ? $this->carpool_success_text : '';
    }

    public function hasCarpoolSuccessText()
    {
        return isset($this->carpool_success_text);
    }

    public function clearCarpoolSuccessText()
    {
        unset($this->carpool_success_text);
    }

    /**
     *拼车价格文案
     *
     * Generated from protobuf field <code>string carpool_success_text = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setCarpoolSuccessText($var)
    {
        GPBUtil::checkString($var, True);
        $this->carpool_success_text = $var;

        return $this;
    }

    /**
     *勾选了link的品类后 = 发单两个品类
     *
     * Generated from protobuf field <code>int32 is_multi_select = 7;</code>
     * @return int
     */
    public function getIsMultiSelect()
    {
        return isset($this->is_multi_select) ? $this->is_multi_select : 0;
    }

    public function hasIsMultiSelect()
    {
        return isset($this->is_multi_select);
    }

    public function clearIsMultiSelect()
    {
        unset($this->is_multi_select);
    }

    /**
     *勾选了link的品类后 = 发单两个品类
     *
     * Generated from protobuf field <code>int32 is_multi_select = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setIsMultiSelect($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_multi_select = $var;

        return $this;
    }

    /**
     *小 i 的跳转地址
     *
     * Generated from protobuf field <code>string info_url = 8;</code>
     * @return string
     */
    public function getInfoUrl()
    {
        return isset($this->info_url) ? $this->info_url : '';
    }

    public function hasInfoUrl()
    {
        return isset($this->info_url);
    }

    public function clearInfoUrl()
    {
        unset($this->info_url);
    }

    /**
     *小 i 的跳转地址
     *
     * Generated from protobuf field <code>string info_url = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setInfoUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->info_url = $var;

        return $this;
    }

    /**
     *左icon
     *
     * Generated from protobuf field <code>string icon_url = 9;</code>
     * @return string
     */
    public function getIconUrl()
    {
        return isset($this->icon_url) ? $this->icon_url : '';
    }

    public function hasIconUrl()
    {
        return isset($this->icon_url);
    }

    public function clearIconUrl()
    {
        unset($this->icon_url);
    }

    /**
     *左icon
     *
     * Generated from protobuf field <code>string icon_url = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setIconUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->icon_url = $var;

        return $this;
    }

}

