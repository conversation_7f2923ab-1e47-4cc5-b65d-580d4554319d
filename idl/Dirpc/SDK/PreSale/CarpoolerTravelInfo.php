<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: carpool.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.CarpoolerTravelInfo</code>
 */
class CarpoolerTravelInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string head_icon = 1;</code>
     */
    protected $head_icon = null;
    /**
     * Generated from protobuf field <code>string departure_text = 2;</code>
     */
    protected $departure_text = null;
    /**
     * Generated from protobuf field <code>string seat_info = 3;</code>
     */
    protected $seat_info = null;
    /**
     * Generated from protobuf field <code>string start_name = 4;</code>
     */
    protected $start_name = null;
    /**
     * Generated from protobuf field <code>string dest_name = 5;</code>
     */
    protected $dest_name = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $head_icon
     *     @type string $departure_text
     *     @type string $seat_info
     *     @type string $start_name
     *     @type string $dest_name
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\Carpool::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string head_icon = 1;</code>
     * @return string
     */
    public function getHeadIcon()
    {
        return isset($this->head_icon) ? $this->head_icon : '';
    }

    public function hasHeadIcon()
    {
        return isset($this->head_icon);
    }

    public function clearHeadIcon()
    {
        unset($this->head_icon);
    }

    /**
     * Generated from protobuf field <code>string head_icon = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setHeadIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->head_icon = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string departure_text = 2;</code>
     * @return string
     */
    public function getDepartureText()
    {
        return isset($this->departure_text) ? $this->departure_text : '';
    }

    public function hasDepartureText()
    {
        return isset($this->departure_text);
    }

    public function clearDepartureText()
    {
        unset($this->departure_text);
    }

    /**
     * Generated from protobuf field <code>string departure_text = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setDepartureText($var)
    {
        GPBUtil::checkString($var, True);
        $this->departure_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string seat_info = 3;</code>
     * @return string
     */
    public function getSeatInfo()
    {
        return isset($this->seat_info) ? $this->seat_info : '';
    }

    public function hasSeatInfo()
    {
        return isset($this->seat_info);
    }

    public function clearSeatInfo()
    {
        unset($this->seat_info);
    }

    /**
     * Generated from protobuf field <code>string seat_info = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setSeatInfo($var)
    {
        GPBUtil::checkString($var, True);
        $this->seat_info = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string start_name = 4;</code>
     * @return string
     */
    public function getStartName()
    {
        return isset($this->start_name) ? $this->start_name : '';
    }

    public function hasStartName()
    {
        return isset($this->start_name);
    }

    public function clearStartName()
    {
        unset($this->start_name);
    }

    /**
     * Generated from protobuf field <code>string start_name = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setStartName($var)
    {
        GPBUtil::checkString($var, True);
        $this->start_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string dest_name = 5;</code>
     * @return string
     */
    public function getDestName()
    {
        return isset($this->dest_name) ? $this->dest_name : '';
    }

    public function hasDestName()
    {
        return isset($this->dest_name);
    }

    public function clearDestName()
    {
        unset($this->dest_name);
    }

    /**
     * Generated from protobuf field <code>string dest_name = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setDestName($var)
    {
        GPBUtil::checkString($var, True);
        $this->dest_name = $var;

        return $this;
    }

}

