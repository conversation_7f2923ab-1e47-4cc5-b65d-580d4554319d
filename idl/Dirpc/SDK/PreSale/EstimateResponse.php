<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.EstimateResponse</code>
 */
class EstimateResponse extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.EstimateData estimate_data = 1;</code>
     */
    private $estimate_data;
    /**
     * Generated from protobuf field <code>string estimate_trace_id = 2;</code>
     */
    protected $estimate_trace_id = '';
    /**
     *是否支持多选，预约单时不支持 0-不支持；1-支持
     *
     * Generated from protobuf field <code>int32 is_support_multi_selection = 3;</code>
     */
    protected $is_support_multi_selection = 0;
    /**
     *分组配置
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CategoryInfo category_info_list = 4;</code>
     */
    private $category_info_list;
    /**
     *价格沟通组件文案
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SpecialRule special_rule = 5;</code>
     */
    protected $special_rule = null;
    /**
     *费用明细页地址
     *
     * Generated from protobuf field <code>string fee_detail_url = 6;</code>
     */
    protected $fee_detail_url = '';
    /**
     *动调、春节服务费等发单拦截页
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PluginPageInfo plugin_page_info = 7;</code>
     */
    protected $plugin_page_info = null;
    /**
     *6.0支付方式并集
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.UserPayInfo user_pay_info = 8;</code>
     */
    protected $user_pay_info = null;
    /**
     *用户所选支付方式与勾选表单支付方式不匹配
     *
     * Generated from protobuf field <code>string pay_type_disable_msg = 9;</code>
     */
    protected $pay_type_disable_msg = null;
    /**
     *弹窗信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.GuideInfo guide_info = 10;</code>
     */
    protected $guide_info = null;
    /**
     *营销类沟通文案
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PromoteSalesRule promote_sales_rule = 11;</code>
     */
    protected $promote_sales_rule = null;
    /**
     *品牌信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BrandInfo brand_info = 12;</code>
     */
    protected $brand_info = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\EstimateData[]|\Nuwa\Protobuf\Internal\RepeatedField $estimate_data
     *     @type string $estimate_trace_id
     *     @type int $is_support_multi_selection
     *          是否支持多选，预约单时不支持 0-不支持；1-支持
     *     @type \Dirpc\SDK\PreSale\CategoryInfo[]|\Nuwa\Protobuf\Internal\RepeatedField $category_info_list
     *          分组配置
     *     @type \Dirpc\SDK\PreSale\SpecialRule $special_rule
     *          价格沟通组件文案
     *     @type string $fee_detail_url
     *          费用明细页地址
     *     @type \Dirpc\SDK\PreSale\PluginPageInfo $plugin_page_info
     *          动调、春节服务费等发单拦截页
     *     @type \Dirpc\SDK\PreSale\UserPayInfo $user_pay_info
     *          6.0支付方式并集
     *     @type string $pay_type_disable_msg
     *          用户所选支付方式与勾选表单支付方式不匹配
     *     @type \Dirpc\SDK\PreSale\GuideInfo $guide_info
     *          弹窗信息
     *     @type \Dirpc\SDK\PreSale\PromoteSalesRule $promote_sales_rule
     *          营销类沟通文案
     *     @type \Dirpc\SDK\PreSale\BrandInfo $brand_info
     *          品牌信息
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.EstimateData estimate_data = 1;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getEstimateData()
    {
        return $this->estimate_data;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.EstimateData estimate_data = 1;</code>
     * @param \Dirpc\SDK\PreSale\EstimateData[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setEstimateData($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\EstimateData::class);
        $this->estimate_data = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string estimate_trace_id = 2;</code>
     * @return string
     */
    public function getEstimateTraceId()
    {
        return $this->estimate_trace_id;
    }

    /**
     * Generated from protobuf field <code>string estimate_trace_id = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateTraceId($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_trace_id = $var;

        return $this;
    }

    /**
     *是否支持多选，预约单时不支持 0-不支持；1-支持
     *
     * Generated from protobuf field <code>int32 is_support_multi_selection = 3;</code>
     * @return int
     */
    public function getIsSupportMultiSelection()
    {
        return $this->is_support_multi_selection;
    }

    /**
     *是否支持多选，预约单时不支持 0-不支持；1-支持
     *
     * Generated from protobuf field <code>int32 is_support_multi_selection = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setIsSupportMultiSelection($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_support_multi_selection = $var;

        return $this;
    }

    /**
     *分组配置
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CategoryInfo category_info_list = 4;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getCategoryInfoList()
    {
        return $this->category_info_list;
    }

    /**
     *分组配置
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CategoryInfo category_info_list = 4;</code>
     * @param \Dirpc\SDK\PreSale\CategoryInfo[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setCategoryInfoList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\CategoryInfo::class);
        $this->category_info_list = $arr;

        return $this;
    }

    /**
     *价格沟通组件文案
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SpecialRule special_rule = 5;</code>
     * @return \Dirpc\SDK\PreSale\SpecialRule
     */
    public function getSpecialRule()
    {
        return isset($this->special_rule) ? $this->special_rule : null;
    }

    public function hasSpecialRule()
    {
        return isset($this->special_rule);
    }

    public function clearSpecialRule()
    {
        unset($this->special_rule);
    }

    /**
     *价格沟通组件文案
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SpecialRule special_rule = 5;</code>
     * @param \Dirpc\SDK\PreSale\SpecialRule $var
     * @return $this
     */
    public function setSpecialRule($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\SpecialRule::class);
        $this->special_rule = $var;

        return $this;
    }

    /**
     *费用明细页地址
     *
     * Generated from protobuf field <code>string fee_detail_url = 6;</code>
     * @return string
     */
    public function getFeeDetailUrl()
    {
        return $this->fee_detail_url;
    }

    /**
     *费用明细页地址
     *
     * Generated from protobuf field <code>string fee_detail_url = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeDetailUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_detail_url = $var;

        return $this;
    }

    /**
     *动调、春节服务费等发单拦截页
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PluginPageInfo plugin_page_info = 7;</code>
     * @return \Dirpc\SDK\PreSale\PluginPageInfo
     */
    public function getPluginPageInfo()
    {
        return isset($this->plugin_page_info) ? $this->plugin_page_info : null;
    }

    public function hasPluginPageInfo()
    {
        return isset($this->plugin_page_info);
    }

    public function clearPluginPageInfo()
    {
        unset($this->plugin_page_info);
    }

    /**
     *动调、春节服务费等发单拦截页
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PluginPageInfo plugin_page_info = 7;</code>
     * @param \Dirpc\SDK\PreSale\PluginPageInfo $var
     * @return $this
     */
    public function setPluginPageInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\PluginPageInfo::class);
        $this->plugin_page_info = $var;

        return $this;
    }

    /**
     *6.0支付方式并集
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.UserPayInfo user_pay_info = 8;</code>
     * @return \Dirpc\SDK\PreSale\UserPayInfo
     */
    public function getUserPayInfo()
    {
        return isset($this->user_pay_info) ? $this->user_pay_info : null;
    }

    public function hasUserPayInfo()
    {
        return isset($this->user_pay_info);
    }

    public function clearUserPayInfo()
    {
        unset($this->user_pay_info);
    }

    /**
     *6.0支付方式并集
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.UserPayInfo user_pay_info = 8;</code>
     * @param \Dirpc\SDK\PreSale\UserPayInfo $var
     * @return $this
     */
    public function setUserPayInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\UserPayInfo::class);
        $this->user_pay_info = $var;

        return $this;
    }

    /**
     *用户所选支付方式与勾选表单支付方式不匹配
     *
     * Generated from protobuf field <code>string pay_type_disable_msg = 9;</code>
     * @return string
     */
    public function getPayTypeDisableMsg()
    {
        return isset($this->pay_type_disable_msg) ? $this->pay_type_disable_msg : '';
    }

    public function hasPayTypeDisableMsg()
    {
        return isset($this->pay_type_disable_msg);
    }

    public function clearPayTypeDisableMsg()
    {
        unset($this->pay_type_disable_msg);
    }

    /**
     *用户所选支付方式与勾选表单支付方式不匹配
     *
     * Generated from protobuf field <code>string pay_type_disable_msg = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setPayTypeDisableMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->pay_type_disable_msg = $var;

        return $this;
    }

    /**
     *弹窗信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.GuideInfo guide_info = 10;</code>
     * @return \Dirpc\SDK\PreSale\GuideInfo
     */
    public function getGuideInfo()
    {
        return isset($this->guide_info) ? $this->guide_info : null;
    }

    public function hasGuideInfo()
    {
        return isset($this->guide_info);
    }

    public function clearGuideInfo()
    {
        unset($this->guide_info);
    }

    /**
     *弹窗信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.GuideInfo guide_info = 10;</code>
     * @param \Dirpc\SDK\PreSale\GuideInfo $var
     * @return $this
     */
    public function setGuideInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\GuideInfo::class);
        $this->guide_info = $var;

        return $this;
    }

    /**
     *营销类沟通文案
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PromoteSalesRule promote_sales_rule = 11;</code>
     * @return \Dirpc\SDK\PreSale\PromoteSalesRule
     */
    public function getPromoteSalesRule()
    {
        return isset($this->promote_sales_rule) ? $this->promote_sales_rule : null;
    }

    public function hasPromoteSalesRule()
    {
        return isset($this->promote_sales_rule);
    }

    public function clearPromoteSalesRule()
    {
        unset($this->promote_sales_rule);
    }

    /**
     *营销类沟通文案
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PromoteSalesRule promote_sales_rule = 11;</code>
     * @param \Dirpc\SDK\PreSale\PromoteSalesRule $var
     * @return $this
     */
    public function setPromoteSalesRule($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\PromoteSalesRule::class);
        $this->promote_sales_rule = $var;

        return $this;
    }

    /**
     *品牌信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BrandInfo brand_info = 12;</code>
     * @return \Dirpc\SDK\PreSale\BrandInfo
     */
    public function getBrandInfo()
    {
        return isset($this->brand_info) ? $this->brand_info : null;
    }

    public function hasBrandInfo()
    {
        return isset($this->brand_info);
    }

    public function clearBrandInfo()
    {
        unset($this->brand_info);
    }

    /**
     *品牌信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BrandInfo brand_info = 12;</code>
     * @param \Dirpc\SDK\PreSale\BrandInfo $var
     * @return $this
     */
    public function setBrandInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\BrandInfo::class);
        $this->brand_info = $var;

        return $this;
    }

}

