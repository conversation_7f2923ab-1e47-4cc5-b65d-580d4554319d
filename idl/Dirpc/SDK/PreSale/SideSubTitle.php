<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideSubTitle</code>
 */
class SideSubTitle extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string content = 1;</code>
     */
    protected $content = '';
    /**
     * Generated from protobuf field <code>string font_color = 2;</code>
     */
    protected $font_color = '';
    /**
     * Generated from protobuf field <code>string border_color = 3;</code>
     */
    protected $border_color = '';
    /**
     * Generated from protobuf field <code>repeated string bg_gradients = 4;</code>
     */
    private $bg_gradients;
    /**
     * Generated from protobuf field <code>string icon_url = 5;</code>
     */
    protected $icon_url = '';
    /**
     *双勾和未勾时候外层盒子是否展示标签
     *
     * Generated from protobuf field <code>int32 is_union_hidden = 6;</code>
     */
    protected $is_union_hidden = 0;
    /**
     *该标签在外层是否过滤掉
     *
     * Generated from protobuf field <code>int32 is_box_outer_show = 7;</code>
     */
    protected $is_box_outer_show = 0;
    /**
     *标签链接
     *
     * Generated from protobuf field <code>string link_url = 8;</code>
     */
    protected $link_url = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $content
     *     @type string $font_color
     *     @type string $border_color
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $bg_gradients
     *     @type string $icon_url
     *     @type int $is_union_hidden
     *          双勾和未勾时候外层盒子是否展示标签
     *     @type int $is_box_outer_show
     *          该标签在外层是否过滤掉
     *     @type string $link_url
     *          标签链接
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string content = 1;</code>
     * @return string
     */
    public function getContent()
    {
        return $this->content;
    }

    /**
     * Generated from protobuf field <code>string content = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setContent($var)
    {
        GPBUtil::checkString($var, True);
        $this->content = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string font_color = 2;</code>
     * @return string
     */
    public function getFontColor()
    {
        return $this->font_color;
    }

    /**
     * Generated from protobuf field <code>string font_color = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setFontColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->font_color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string border_color = 3;</code>
     * @return string
     */
    public function getBorderColor()
    {
        return $this->border_color;
    }

    /**
     * Generated from protobuf field <code>string border_color = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setBorderColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->border_color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated string bg_gradients = 4;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getBgGradients()
    {
        return $this->bg_gradients;
    }

    /**
     * Generated from protobuf field <code>repeated string bg_gradients = 4;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBgGradients($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->bg_gradients = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string icon_url = 5;</code>
     * @return string
     */
    public function getIconUrl()
    {
        return $this->icon_url;
    }

    /**
     * Generated from protobuf field <code>string icon_url = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setIconUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->icon_url = $var;

        return $this;
    }

    /**
     *双勾和未勾时候外层盒子是否展示标签
     *
     * Generated from protobuf field <code>int32 is_union_hidden = 6;</code>
     * @return int
     */
    public function getIsUnionHidden()
    {
        return $this->is_union_hidden;
    }

    /**
     *双勾和未勾时候外层盒子是否展示标签
     *
     * Generated from protobuf field <code>int32 is_union_hidden = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setIsUnionHidden($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_union_hidden = $var;

        return $this;
    }

    /**
     *该标签在外层是否过滤掉
     *
     * Generated from protobuf field <code>int32 is_box_outer_show = 7;</code>
     * @return int
     */
    public function getIsBoxOuterShow()
    {
        return $this->is_box_outer_show;
    }

    /**
     *该标签在外层是否过滤掉
     *
     * Generated from protobuf field <code>int32 is_box_outer_show = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setIsBoxOuterShow($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_box_outer_show = $var;

        return $this;
    }

    /**
     *标签链接
     *
     * Generated from protobuf field <code>string link_url = 8;</code>
     * @return string
     */
    public function getLinkUrl()
    {
        return $this->link_url;
    }

    /**
     *标签链接
     *
     * Generated from protobuf field <code>string link_url = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setLinkUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->link_url = $var;

        return $this;
    }

}

