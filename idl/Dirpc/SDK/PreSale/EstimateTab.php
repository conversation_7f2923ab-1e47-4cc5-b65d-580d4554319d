<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.EstimateTab</code>
 */
class EstimateTab extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string tab_name = 1;</code>
     */
    protected $tab_name = '';
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.TabExtraTag extra_tag = 2;</code>
     */
    protected $extra_tag = null;
    /**
     * Generated from protobuf field <code>string pic_url = 3;</code>
     */
    protected $pic_url = '';
    /**
     * Generated from protobuf field <code>string left_pic_url = 4;</code>
     */
    protected $left_pic_url = '';
    /**
     * Generated from protobuf field <code>string top_bubble_url = 5;</code>
     */
    protected $top_bubble_url = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $tab_name
     *     @type \Dirpc\SDK\PreSale\TabExtraTag $extra_tag
     *     @type string $pic_url
     *     @type string $left_pic_url
     *     @type string $top_bubble_url
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string tab_name = 1;</code>
     * @return string
     */
    public function getTabName()
    {
        return $this->tab_name;
    }

    /**
     * Generated from protobuf field <code>string tab_name = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTabName($var)
    {
        GPBUtil::checkString($var, True);
        $this->tab_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.TabExtraTag extra_tag = 2;</code>
     * @return \Dirpc\SDK\PreSale\TabExtraTag
     */
    public function getExtraTag()
    {
        return isset($this->extra_tag) ? $this->extra_tag : null;
    }

    public function hasExtraTag()
    {
        return isset($this->extra_tag);
    }

    public function clearExtraTag()
    {
        unset($this->extra_tag);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.TabExtraTag extra_tag = 2;</code>
     * @param \Dirpc\SDK\PreSale\TabExtraTag $var
     * @return $this
     */
    public function setExtraTag($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\TabExtraTag::class);
        $this->extra_tag = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string pic_url = 3;</code>
     * @return string
     */
    public function getPicUrl()
    {
        return $this->pic_url;
    }

    /**
     * Generated from protobuf field <code>string pic_url = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setPicUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->pic_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string left_pic_url = 4;</code>
     * @return string
     */
    public function getLeftPicUrl()
    {
        return $this->left_pic_url;
    }

    /**
     * Generated from protobuf field <code>string left_pic_url = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setLeftPicUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->left_pic_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string top_bubble_url = 5;</code>
     * @return string
     */
    public function getTopBubbleUrl()
    {
        return $this->top_bubble_url;
    }

    /**
     * Generated from protobuf field <code>string top_bubble_url = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setTopBubbleUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->top_bubble_url = $var;

        return $this;
    }

}

