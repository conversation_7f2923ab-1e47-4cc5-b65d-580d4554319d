<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 *发单参数
 *
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NewFormExtraMap</code>
 */
class NewFormExtraMap extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 product_id = 1;</code>
     */
    protected $product_id = 0;
    /**
     * Generated from protobuf field <code>int32 business_id = 2;</code>
     */
    protected $business_id = 0;
    /**
     * Generated from protobuf field <code>int32 combo_type = 3;</code>
     */
    protected $combo_type = 0;
    /**
     * Generated from protobuf field <code>int32 require_level = 4;</code>
     */
    protected $require_level = 0;
    /**
     * Generated from protobuf field <code>int32 level_type = 5;</code>
     */
    protected $level_type = 0;
    /**
     * Generated from protobuf field <code>int32 combo_id = 6;</code>
     */
    protected $combo_id = 0;
    /**
     * Generated from protobuf field <code>int32 route_type = 7;</code>
     */
    protected $route_type = 0;
    /**
     * Generated from protobuf field <code>int32 is_special_price = 8;</code>
     */
    protected $is_special_price = 0;
    /**
     * Generated from protobuf field <code>int32 count_price_type = 9;</code>
     */
    protected $count_price_type = 0;
    /**
     * Generated from protobuf field <code>string port_type = 10;</code>
     */
    protected $port_type = null;
    /**
     * Generated from protobuf field <code>int32 bargain_from_type = 11;</code>
     */
    protected $bargain_from_type = 0;
    /**
     * Generated from protobuf field <code>int32 is_default_auth = 12;</code>
     */
    protected $is_default_auth = 0;
    /**
     * Generated from protobuf field <code>int32 etp = 13;</code>
     */
    protected $etp = 0;
    /**
     * Generated from protobuf field <code>string extra_custom_feature = 14;</code>
     */
    protected $extra_custom_feature = null;
    /**
     *城际拼车订单出发时间
     *
     * Generated from protobuf field <code>string departure_range = 15;</code>
     */
    protected $departure_range = null;
    /**
     *用户选择的拼车座位数
     *
     * Generated from protobuf field <code>int32 carpool_seat_num = 16;</code>
     */
    protected $carpool_seat_num = null;
    /**
     *城际自营惊喜独享标识
     *
     * Generated from protobuf field <code>string is_intercity_surprise_alone = 17;</code>
     */
    protected $is_intercity_surprise_alone = null;
    /**
     *待授权的协议列表
     *
     * Generated from protobuf field <code>repeated string need_auth_list = 18;</code>
     */
    private $need_auth_list;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $product_id
     *     @type int $business_id
     *     @type int $combo_type
     *     @type int $require_level
     *     @type int $level_type
     *     @type int $combo_id
     *     @type int $route_type
     *     @type int $is_special_price
     *     @type int $count_price_type
     *     @type string $port_type
     *     @type int $bargain_from_type
     *     @type int $is_default_auth
     *     @type int $etp
     *     @type string $extra_custom_feature
     *     @type string $departure_range
     *          城际拼车订单出发时间
     *     @type int $carpool_seat_num
     *          用户选择的拼车座位数
     *     @type string $is_intercity_surprise_alone
     *          城际自营惊喜独享标识
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $need_auth_list
     *          待授权的协议列表
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 product_id = 1;</code>
     * @return int
     */
    public function getProductId()
    {
        return $this->product_id;
    }

    /**
     * Generated from protobuf field <code>int32 product_id = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setProductId($var)
    {
        GPBUtil::checkInt32($var);
        $this->product_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 business_id = 2;</code>
     * @return int
     */
    public function getBusinessId()
    {
        return $this->business_id;
    }

    /**
     * Generated from protobuf field <code>int32 business_id = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setBusinessId($var)
    {
        GPBUtil::checkInt32($var);
        $this->business_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 combo_type = 3;</code>
     * @return int
     */
    public function getComboType()
    {
        return $this->combo_type;
    }

    /**
     * Generated from protobuf field <code>int32 combo_type = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setComboType($var)
    {
        GPBUtil::checkInt32($var);
        $this->combo_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 require_level = 4;</code>
     * @return int
     */
    public function getRequireLevel()
    {
        return $this->require_level;
    }

    /**
     * Generated from protobuf field <code>int32 require_level = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setRequireLevel($var)
    {
        GPBUtil::checkInt32($var);
        $this->require_level = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 level_type = 5;</code>
     * @return int
     */
    public function getLevelType()
    {
        return $this->level_type;
    }

    /**
     * Generated from protobuf field <code>int32 level_type = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setLevelType($var)
    {
        GPBUtil::checkInt32($var);
        $this->level_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 combo_id = 6;</code>
     * @return int
     */
    public function getComboId()
    {
        return $this->combo_id;
    }

    /**
     * Generated from protobuf field <code>int32 combo_id = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setComboId($var)
    {
        GPBUtil::checkInt32($var);
        $this->combo_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 route_type = 7;</code>
     * @return int
     */
    public function getRouteType()
    {
        return $this->route_type;
    }

    /**
     * Generated from protobuf field <code>int32 route_type = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setRouteType($var)
    {
        GPBUtil::checkInt32($var);
        $this->route_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 is_special_price = 8;</code>
     * @return int
     */
    public function getIsSpecialPrice()
    {
        return $this->is_special_price;
    }

    /**
     * Generated from protobuf field <code>int32 is_special_price = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setIsSpecialPrice($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_special_price = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 count_price_type = 9;</code>
     * @return int
     */
    public function getCountPriceType()
    {
        return $this->count_price_type;
    }

    /**
     * Generated from protobuf field <code>int32 count_price_type = 9;</code>
     * @param int $var
     * @return $this
     */
    public function setCountPriceType($var)
    {
        GPBUtil::checkInt32($var);
        $this->count_price_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string port_type = 10;</code>
     * @return string
     */
    public function getPortType()
    {
        return isset($this->port_type) ? $this->port_type : '';
    }

    public function hasPortType()
    {
        return isset($this->port_type);
    }

    public function clearPortType()
    {
        unset($this->port_type);
    }

    /**
     * Generated from protobuf field <code>string port_type = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setPortType($var)
    {
        GPBUtil::checkString($var, True);
        $this->port_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 bargain_from_type = 11;</code>
     * @return int
     */
    public function getBargainFromType()
    {
        return $this->bargain_from_type;
    }

    /**
     * Generated from protobuf field <code>int32 bargain_from_type = 11;</code>
     * @param int $var
     * @return $this
     */
    public function setBargainFromType($var)
    {
        GPBUtil::checkInt32($var);
        $this->bargain_from_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 is_default_auth = 12;</code>
     * @return int
     */
    public function getIsDefaultAuth()
    {
        return $this->is_default_auth;
    }

    /**
     * Generated from protobuf field <code>int32 is_default_auth = 12;</code>
     * @param int $var
     * @return $this
     */
    public function setIsDefaultAuth($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_default_auth = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 etp = 13;</code>
     * @return int
     */
    public function getEtp()
    {
        return $this->etp;
    }

    /**
     * Generated from protobuf field <code>int32 etp = 13;</code>
     * @param int $var
     * @return $this
     */
    public function setEtp($var)
    {
        GPBUtil::checkInt32($var);
        $this->etp = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string extra_custom_feature = 14;</code>
     * @return string
     */
    public function getExtraCustomFeature()
    {
        return isset($this->extra_custom_feature) ? $this->extra_custom_feature : '';
    }

    public function hasExtraCustomFeature()
    {
        return isset($this->extra_custom_feature);
    }

    public function clearExtraCustomFeature()
    {
        unset($this->extra_custom_feature);
    }

    /**
     * Generated from protobuf field <code>string extra_custom_feature = 14;</code>
     * @param string $var
     * @return $this
     */
    public function setExtraCustomFeature($var)
    {
        GPBUtil::checkString($var, True);
        $this->extra_custom_feature = $var;

        return $this;
    }

    /**
     *城际拼车订单出发时间
     *
     * Generated from protobuf field <code>string departure_range = 15;</code>
     * @return string
     */
    public function getDepartureRange()
    {
        return isset($this->departure_range) ? $this->departure_range : '';
    }

    public function hasDepartureRange()
    {
        return isset($this->departure_range);
    }

    public function clearDepartureRange()
    {
        unset($this->departure_range);
    }

    /**
     *城际拼车订单出发时间
     *
     * Generated from protobuf field <code>string departure_range = 15;</code>
     * @param string $var
     * @return $this
     */
    public function setDepartureRange($var)
    {
        GPBUtil::checkString($var, True);
        $this->departure_range = $var;

        return $this;
    }

    /**
     *用户选择的拼车座位数
     *
     * Generated from protobuf field <code>int32 carpool_seat_num = 16;</code>
     * @return int
     */
    public function getCarpoolSeatNum()
    {
        return isset($this->carpool_seat_num) ? $this->carpool_seat_num : 0;
    }

    public function hasCarpoolSeatNum()
    {
        return isset($this->carpool_seat_num);
    }

    public function clearCarpoolSeatNum()
    {
        unset($this->carpool_seat_num);
    }

    /**
     *用户选择的拼车座位数
     *
     * Generated from protobuf field <code>int32 carpool_seat_num = 16;</code>
     * @param int $var
     * @return $this
     */
    public function setCarpoolSeatNum($var)
    {
        GPBUtil::checkInt32($var);
        $this->carpool_seat_num = $var;

        return $this;
    }

    /**
     *城际自营惊喜独享标识
     *
     * Generated from protobuf field <code>string is_intercity_surprise_alone = 17;</code>
     * @return string
     */
    public function getIsIntercitySurpriseAlone()
    {
        return isset($this->is_intercity_surprise_alone) ? $this->is_intercity_surprise_alone : '';
    }

    public function hasIsIntercitySurpriseAlone()
    {
        return isset($this->is_intercity_surprise_alone);
    }

    public function clearIsIntercitySurpriseAlone()
    {
        unset($this->is_intercity_surprise_alone);
    }

    /**
     *城际自营惊喜独享标识
     *
     * Generated from protobuf field <code>string is_intercity_surprise_alone = 17;</code>
     * @param string $var
     * @return $this
     */
    public function setIsIntercitySurpriseAlone($var)
    {
        GPBUtil::checkString($var, True);
        $this->is_intercity_surprise_alone = $var;

        return $this;
    }

    /**
     *待授权的协议列表
     *
     * Generated from protobuf field <code>repeated string need_auth_list = 18;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getNeedAuthList()
    {
        return $this->need_auth_list;
    }

    /**
     *待授权的协议列表
     *
     * Generated from protobuf field <code>repeated string need_auth_list = 18;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setNeedAuthList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->need_auth_list = $arr;

        return $this;
    }

}

