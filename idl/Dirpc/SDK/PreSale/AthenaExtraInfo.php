<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.AthenaExtraInfo</code>
 */
class AthenaExtraInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string guide_scene = 1;</code>
     */
    protected $guide_scene = null;
    /**
     * Generated from protobuf field <code>string athena_id = 2;</code>
     */
    protected $athena_id = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $guide_scene
     *     @type string $athena_id
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string guide_scene = 1;</code>
     * @return string
     */
    public function getGuideScene()
    {
        return isset($this->guide_scene) ? $this->guide_scene : '';
    }

    public function hasGuideScene()
    {
        return isset($this->guide_scene);
    }

    public function clearGuideScene()
    {
        unset($this->guide_scene);
    }

    /**
     * Generated from protobuf field <code>string guide_scene = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setGuideScene($var)
    {
        GPBUtil::checkString($var, True);
        $this->guide_scene = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string athena_id = 2;</code>
     * @return string
     */
    public function getAthenaId()
    {
        return isset($this->athena_id) ? $this->athena_id : '';
    }

    public function hasAthenaId()
    {
        return isset($this->athena_id);
    }

    public function clearAthenaId()
    {
        unset($this->athena_id);
    }

    /**
     * Generated from protobuf field <code>string athena_id = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setAthenaId($var)
    {
        GPBUtil::checkString($var, True);
        $this->athena_id = $var;

        return $this;
    }

}

