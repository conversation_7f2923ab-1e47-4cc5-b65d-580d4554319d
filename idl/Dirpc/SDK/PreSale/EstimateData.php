<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.EstimateData</code>
 */
class EstimateData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *车型文案
     *
     * Generated from protobuf field <code>string intro_msg = 1;</code>
     */
    protected $intro_msg = '';
    /**
     *价格信息
     *
     * Generated from protobuf field <code>string fee_msg = 2;</code>
     */
    protected $fee_msg = '';
    /**
     *车型
     *
     * Generated from protobuf field <code>int32 require_level = 3;</code>
     */
    protected $require_level = 0;
    /**
     *业务线
     *
     * Generated from protobuf field <code>int32 business_id = 4;</code>
     */
    protected $business_id = 0;
    /**
     *产品线ID
     *
     * Generated from protobuf field <code>int32 product_id = 5;</code>
     */
    protected $product_id = 0;
    /**
     *combo_type
     *
     * Generated from protobuf field <code>int32 combo_type = 6;</code>
     */
    protected $combo_type = 0;
    /**
     *品类ID
     *
     * Generated from protobuf field <code>int32 product_category = 7;</code>
     */
    protected $product_category = 0;
    /**
     *价格补充信息（券/动调/黑金会员等）
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CommonTag price_info_desc = 8;</code>
     */
    private $price_info_desc;
    /**
     *分组ID
     *
     * Generated from protobuf field <code>int32 category_id = 9;</code>
     */
    protected $category_id = 0;
    /**
     *车型右边的推荐语
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.CommonTag recommend_tag = 10;</code>
     */
    protected $recommend_tag = null;
    /**
     *勾选按钮上的气泡tag
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.CommonTag selection_tag = 11;</code>
     */
    protected $selection_tag = null;
    /**
     *附着其他品类
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.LinkProduct link_product = 12;</code>
     */
    protected $link_product = null;
    /**
     *拼车座位数组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.CarpoolSeatModule carpool_seat_module = 13;</code>
     */
    protected $carpool_seat_module = null;
    /**
     *预估ID
     *
     * Generated from protobuf field <code>string estimate_id = 14;</code>
     */
    protected $estimate_id = '';
    /**
     *车型下方的小字描述
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SubTitleInfo sub_title_list = 15;</code>
     */
    private $sub_title_list;
    /**
     *品类下的价格沟通规则、文案和强弹标识
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SpecialPriceText special_price_text = 16;</code>
     */
    protected $special_price_text = null;
    /**
     *城际拼车时间片组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.MatchRoutesData match_routes_data = 17;</code>
     */
    protected $match_routes_data = null;
    /**
     *是否隐藏，两口价用
     *
     * Generated from protobuf field <code>int32 hidden = 18;</code>
     */
    protected $hidden = null;
    /**
     *拼成乐时间片
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.StationCarpoolBooking station_carpool_booking = 19;</code>
     */
    protected $station_carpool_booking = null;
    /**
     *费用价格
     *
     * Generated from protobuf field <code>string fee_amount = 20;</code>
     */
    protected $fee_amount = '';
    /**
     *路线ID，发单参数
     *
     * Generated from protobuf field <code>int32 combo_id = 21;</code>
     */
    protected $combo_id = null;
    /**
     *个性化服务数据
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SceneData scene_data = 22;</code>
     */
    private $scene_data;
    /**
     *粤港车口岸信息
     *
     * Generated from protobuf field <code>string port_type = 23;</code>
     */
    protected $port_type = null;
    /**
     *路线ID
     *
     * Generated from protobuf field <code>repeated string route_id_list = 24;</code>
     */
    private $route_id_list;
    /**
     *个性化服务半弹窗顶部图片
     *
     * Generated from protobuf field <code>string custom_service_popup_image = 25;</code>
     */
    protected $custom_service_popup_image = null;
    /**
     *附加服务的icon
     *
     * Generated from protobuf field <code>repeated string attach_service_icon = 26;</code>
     */
    private $attach_service_icon;
    /**
     *车型表单的支付方式
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.FormUserPayInfo user_pay_info = 27;</code>
     */
    protected $user_pay_info = null;
    /**
     *路线类型
     *
     * Generated from protobuf field <code>int32 route_type = 28;</code>
     */
    protected $route_type = null;
    /**
     *产品tag描述(多是角标形式)
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ExtraTag extra_tag = 29;</code>
     */
    private $extra_tag;
    /**
     *卡片背景图
     *
     * Generated from protobuf field <code>string selected_background_url = 30;</code>
     */
    protected $selected_background_url = null;
    /**
     *单品类多价格展示
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.MultiPriceDesc multi_price_desc = 31;</code>
     */
    private $multi_price_desc;
    /**
     *是否展示在外层推荐位 0-不展示；1-展示
     *
     * Generated from protobuf field <code>int32 recommend_type = 50;</code>
     */
    protected $recommend_type = 0;
    /**
     *是否选中 （实时单有多项，预估单仅选中一项）0-不选中；1-选中
     *
     * Generated from protobuf field <code>int32 select_type = 51;</code>
     */
    protected $select_type = 0;
    /**
     *表单展示类型（拼成乐/城际 展示在导流位）0-anycar列表中；1-导流位
     *
     * Generated from protobuf field <code>int32 form_show_type = 52;</code>
     */
    protected $form_show_type = 0;
    /**
     *展示在导流位上的提示文案（「去预约」）
     *
     * Generated from protobuf field <code>string guide_show_type_tips = 53;</code>
     */
    protected $guide_show_type_tips = '';
    /**
     *专车，豪车个性化设置展示
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PreferData prefer_data = 54;</code>
     */
    protected $prefer_data = null;
    /**
     *是否命中动调
     *
     * Generated from protobuf field <code>int32 hit_dynamic_price = 55;</code>
     */
    protected $hit_dynamic_price = null;
    /**
     *导流位出发时间提示信息
     *
     * Generated from protobuf field <code>string depart_tag = 56;</code>
     */
    protected $depart_tag = null;
    /**
     *是否第三方产品线
     *
     * Generated from protobuf field <code>int32 is_tripcloud = 57;</code>
     */
    protected $is_tripcloud = null;
    /**
     *专车个性化服务
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OptionData option_data = 58;</code>
     */
    protected $option_data = null;
    /**
     *无人车地址
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.AutoDriveRedirectionInfo auto_driving_address_info = 59;</code>
     */
    protected $auto_driving_address_info = null;
    /**
     *不可用状态
     *
     * Generated from protobuf field <code>int32 disabled = 60;</code>
     */
    protected $disabled = null;
    /**
     *使用信息（车型下方的下拉文案）
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.UsageInfo usage_info = 61;</code>
     */
    protected $usage_info = null;
    /**
     *不可用文案
     *
     * Generated from protobuf field <code>string disabled_text = 62;</code>
     */
    protected $disabled_text = null;
    /**
     *场景 1-滴滴特快
     *
     * Generated from protobuf field <code>int32 level_type = 63;</code>
     */
    protected $level_type = null;
    /**
     *计价模式
     *
     * Generated from protobuf field <code>int32 count_price_type = 64;</code>
     */
    protected $count_price_type = null;
    /**
     *产品描述背景图
     *
     * Generated from protobuf field <code>string background_url = 65;</code>
     */
    protected $background_url = null;
    /**
     *产品描述落地页
     *
     * Generated from protobuf field <code>string detail_url = 66;</code>
     */
    protected $detail_url = null;
    /**
     *个性化服务文案
     *
     * Generated from protobuf field <code>string scene_data_head = 67;</code>
     */
    protected $scene_data_head = null;
    /**
     *个性化服务说明
     *
     * Generated from protobuf field <code>string scene_data_head_link = 68;</code>
     */
    protected $scene_data_head_link = null;
    /**
     *营销类沟通组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PromoteSalesText promote_sales_text = 69;</code>
     */
    protected $promote_sales_text = null;
    /**
     *6.0出租车业务多品类、短途特惠
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.AggregationEstimateData sub_product = 70;</code>
     */
    private $sub_product;
    /**
     *二级冒泡页的标题
     *
     * Generated from protobuf field <code>string sub_page_title = 71;</code>
     */
    protected $sub_page_title = null;
    /**
     *二级冒泡页的子标题
     *
     * Generated from protobuf field <code>string sub_page_sub_title = 72;</code>
     */
    protected $sub_page_sub_title = null;
    /**
     *标识 1标识是短途特惠 2标识出租车
     *
     * Generated from protobuf field <code>int32 sub_group_id = 73;</code>
     */
    protected $sub_group_id = null;
    /**
     *优选出租车icon图标
     *
     * Generated from protobuf field <code>string sub_intro_icon = 74;</code>
     */
    protected $sub_intro_icon = null;
    /**
     *混合支付的预估价信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.MixedFeeMsg mixed_fee_msg = 75;</code>
     */
    protected $mixed_fee_msg = null;
    /**
     *预估快车价信息
     *
     * Generated from protobuf field <code>string cutoff_fee_msg = 76;</code>
     */
    protected $cutoff_fee_msg = null;
    /**
     *预估快车价信息
     *
     * Generated from protobuf field <code>string cutoff_fee_info = 77;</code>
     */
    protected $cutoff_fee_info = null;
    /**
     *导流推荐信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RecommendGuide recommend_guide = 78;</code>
     */
    protected $recommend_guide = null;
    /**
     *仅表示聚合子页面是否可以直接弹出，弹出时机端控制（可用于特惠联盟、出租车）
     *
     * Generated from protobuf field <code>bool show_sub_page_directly = 79;</code>
     */
    protected $show_sub_page_directly = null;
    /**
     *特惠联盟参考价，用于计算最大可省
     *
     * Generated from protobuf field <code>string reference_fee_amount = 80;</code>
     */
    protected $reference_fee_amount = null;
    /**
     *预估show h5命中类型
     *
     * Generated from protobuf field <code>int32 hit_show_h5_type = 81;</code>
     */
    protected $hit_show_h5_type = null;
    /**
     *预估品类主题标识
     *
     * Generated from protobuf field <code>int32 theme_type = 82;</code>
     */
    protected $theme_type = null;
    /**
     *sub_title_list隐藏平滑移动信息
     *
     * Generated from protobuf field <code>bool sub_title_hidden_others = 83;</code>
     */
    protected $sub_title_hidden_others = null;
    /**
     *预估品类主题数据
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BubbleThemeData theme_data = 84;</code>
     */
    protected $theme_data = null;
    /**
     *新出租独立小程序推荐文案
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.TaxiRecommendInfTag unitaix_recommend_tag = 85;</code>
     */
    protected $unitaix_recommend_tag = null;
    /**
     *载人车倒计时预估数据
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ExtraEstimateData extra_estimate_data = 86;</code>
     */
    protected $extra_estimate_data = null;
    /**
     *0.5盒子
     *
     * Generated from protobuf field <code>string sub_right_title = 87;</code>
     */
    protected $sub_right_title = null;
    /**
     *0.5盒子
     *
     * Generated from protobuf field <code>string box_id = 90;</code>
     */
    protected $box_id = null;
    /**
     *导流位跳转链接
     *
     * Generated from protobuf field <code>string guide_path = 91;</code>
     */
    protected $guide_path = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $intro_msg
     *          车型文案
     *     @type string $fee_msg
     *          价格信息
     *     @type int $require_level
     *          车型
     *     @type int $business_id
     *          业务线
     *     @type int $product_id
     *          产品线ID
     *     @type int $combo_type
     *          combo_type
     *     @type int $product_category
     *          品类ID
     *     @type \Dirpc\SDK\PreSale\CommonTag[]|\Nuwa\Protobuf\Internal\RepeatedField $price_info_desc
     *          价格补充信息（券/动调/黑金会员等）
     *     @type int $category_id
     *          分组ID
     *     @type \Dirpc\SDK\PreSale\CommonTag $recommend_tag
     *          车型右边的推荐语
     *     @type \Dirpc\SDK\PreSale\CommonTag $selection_tag
     *          勾选按钮上的气泡tag
     *     @type \Dirpc\SDK\PreSale\LinkProduct $link_product
     *          附着其他品类
     *     @type \Dirpc\SDK\PreSale\CarpoolSeatModule $carpool_seat_module
     *          拼车座位数组件
     *     @type string $estimate_id
     *          预估ID
     *     @type \Dirpc\SDK\PreSale\SubTitleInfo[]|\Nuwa\Protobuf\Internal\RepeatedField $sub_title_list
     *          车型下方的小字描述
     *     @type \Dirpc\SDK\PreSale\SpecialPriceText $special_price_text
     *          品类下的价格沟通规则、文案和强弹标识
     *     @type \Dirpc\SDK\PreSale\MatchRoutesData $match_routes_data
     *          城际拼车时间片组件
     *     @type int $hidden
     *          是否隐藏，两口价用
     *     @type \Dirpc\SDK\PreSale\StationCarpoolBooking $station_carpool_booking
     *          拼成乐时间片
     *     @type string $fee_amount
     *          费用价格
     *     @type int $combo_id
     *          路线ID，发单参数
     *     @type \Dirpc\SDK\PreSale\SceneData[]|\Nuwa\Protobuf\Internal\RepeatedField $scene_data
     *          个性化服务数据
     *     @type string $port_type
     *          粤港车口岸信息
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $route_id_list
     *          路线ID
     *     @type string $custom_service_popup_image
     *          个性化服务半弹窗顶部图片
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $attach_service_icon
     *          附加服务的icon
     *     @type \Dirpc\SDK\PreSale\FormUserPayInfo $user_pay_info
     *          车型表单的支付方式
     *     @type int $route_type
     *          路线类型
     *     @type \Dirpc\SDK\PreSale\ExtraTag[]|\Nuwa\Protobuf\Internal\RepeatedField $extra_tag
     *          产品tag描述(多是角标形式)
     *     @type string $selected_background_url
     *          卡片背景图
     *     @type \Dirpc\SDK\PreSale\MultiPriceDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $multi_price_desc
     *          单品类多价格展示
     *     @type int $recommend_type
     *          是否展示在外层推荐位 0-不展示；1-展示
     *     @type int $select_type
     *          是否选中 （实时单有多项，预估单仅选中一项）0-不选中；1-选中
     *     @type int $form_show_type
     *          表单展示类型（拼成乐/城际 展示在导流位）0-anycar列表中；1-导流位
     *     @type string $guide_show_type_tips
     *          展示在导流位上的提示文案（「去预约」）
     *     @type \Dirpc\SDK\PreSale\PreferData $prefer_data
     *          专车，豪车个性化设置展示
     *     @type int $hit_dynamic_price
     *          是否命中动调
     *     @type string $depart_tag
     *          导流位出发时间提示信息
     *     @type int $is_tripcloud
     *          是否第三方产品线
     *     @type \Dirpc\SDK\PreSale\OptionData $option_data
     *          专车个性化服务
     *     @type \Dirpc\SDK\PreSale\AutoDriveRedirectionInfo $auto_driving_address_info
     *          无人车地址
     *     @type int $disabled
     *          不可用状态
     *     @type \Dirpc\SDK\PreSale\UsageInfo $usage_info
     *          使用信息（车型下方的下拉文案）
     *     @type string $disabled_text
     *          不可用文案
     *     @type int $level_type
     *          场景 1-滴滴特快
     *     @type int $count_price_type
     *          计价模式
     *     @type string $background_url
     *          产品描述背景图
     *     @type string $detail_url
     *          产品描述落地页
     *     @type string $scene_data_head
     *          个性化服务文案
     *     @type string $scene_data_head_link
     *          个性化服务说明
     *     @type \Dirpc\SDK\PreSale\PromoteSalesText $promote_sales_text
     *          营销类沟通组件
     *     @type \Dirpc\SDK\PreSale\AggregationEstimateData[]|\Nuwa\Protobuf\Internal\RepeatedField $sub_product
     *          6.0出租车业务多品类、短途特惠
     *     @type string $sub_page_title
     *          二级冒泡页的标题
     *     @type string $sub_page_sub_title
     *          二级冒泡页的子标题
     *     @type int $sub_group_id
     *          标识 1标识是短途特惠 2标识出租车
     *     @type string $sub_intro_icon
     *          优选出租车icon图标
     *     @type \Dirpc\SDK\PreSale\MixedFeeMsg $mixed_fee_msg
     *          混合支付的预估价信息
     *     @type string $cutoff_fee_msg
     *          预估快车价信息
     *     @type string $cutoff_fee_info
     *          预估快车价信息
     *     @type \Dirpc\SDK\PreSale\RecommendGuide $recommend_guide
     *          导流推荐信息
     *     @type bool $show_sub_page_directly
     *          仅表示聚合子页面是否可以直接弹出，弹出时机端控制（可用于特惠联盟、出租车）
     *     @type string $reference_fee_amount
     *          特惠联盟参考价，用于计算最大可省
     *     @type int $hit_show_h5_type
     *          预估show h5命中类型
     *     @type int $theme_type
     *          预估品类主题标识
     *     @type bool $sub_title_hidden_others
     *          sub_title_list隐藏平滑移动信息
     *     @type \Dirpc\SDK\PreSale\BubbleThemeData $theme_data
     *          预估品类主题数据
     *     @type \Dirpc\SDK\PreSale\TaxiRecommendInfTag $unitaix_recommend_tag
     *          新出租独立小程序推荐文案
     *     @type \Dirpc\SDK\PreSale\ExtraEstimateData $extra_estimate_data
     *          载人车倒计时预估数据
     *     @type string $sub_right_title
     *          0.5盒子
     *     @type string $box_id
     *          0.5盒子
     *     @type string $guide_path
     *          导流位跳转链接
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *车型文案
     *
     * Generated from protobuf field <code>string intro_msg = 1;</code>
     * @return string
     */
    public function getIntroMsg()
    {
        return $this->intro_msg;
    }

    /**
     *车型文案
     *
     * Generated from protobuf field <code>string intro_msg = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setIntroMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->intro_msg = $var;

        return $this;
    }

    /**
     *价格信息
     *
     * Generated from protobuf field <code>string fee_msg = 2;</code>
     * @return string
     */
    public function getFeeMsg()
    {
        return $this->fee_msg;
    }

    /**
     *价格信息
     *
     * Generated from protobuf field <code>string fee_msg = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg = $var;

        return $this;
    }

    /**
     *车型
     *
     * Generated from protobuf field <code>int32 require_level = 3;</code>
     * @return int
     */
    public function getRequireLevel()
    {
        return $this->require_level;
    }

    /**
     *车型
     *
     * Generated from protobuf field <code>int32 require_level = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setRequireLevel($var)
    {
        GPBUtil::checkInt32($var);
        $this->require_level = $var;

        return $this;
    }

    /**
     *业务线
     *
     * Generated from protobuf field <code>int32 business_id = 4;</code>
     * @return int
     */
    public function getBusinessId()
    {
        return $this->business_id;
    }

    /**
     *业务线
     *
     * Generated from protobuf field <code>int32 business_id = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setBusinessId($var)
    {
        GPBUtil::checkInt32($var);
        $this->business_id = $var;

        return $this;
    }

    /**
     *产品线ID
     *
     * Generated from protobuf field <code>int32 product_id = 5;</code>
     * @return int
     */
    public function getProductId()
    {
        return $this->product_id;
    }

    /**
     *产品线ID
     *
     * Generated from protobuf field <code>int32 product_id = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setProductId($var)
    {
        GPBUtil::checkInt32($var);
        $this->product_id = $var;

        return $this;
    }

    /**
     *combo_type
     *
     * Generated from protobuf field <code>int32 combo_type = 6;</code>
     * @return int
     */
    public function getComboType()
    {
        return $this->combo_type;
    }

    /**
     *combo_type
     *
     * Generated from protobuf field <code>int32 combo_type = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setComboType($var)
    {
        GPBUtil::checkInt32($var);
        $this->combo_type = $var;

        return $this;
    }

    /**
     *品类ID
     *
     * Generated from protobuf field <code>int32 product_category = 7;</code>
     * @return int
     */
    public function getProductCategory()
    {
        return $this->product_category;
    }

    /**
     *品类ID
     *
     * Generated from protobuf field <code>int32 product_category = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setProductCategory($var)
    {
        GPBUtil::checkInt32($var);
        $this->product_category = $var;

        return $this;
    }

    /**
     *价格补充信息（券/动调/黑金会员等）
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CommonTag price_info_desc = 8;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getPriceInfoDesc()
    {
        return $this->price_info_desc;
    }

    /**
     *价格补充信息（券/动调/黑金会员等）
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CommonTag price_info_desc = 8;</code>
     * @param \Dirpc\SDK\PreSale\CommonTag[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setPriceInfoDesc($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\CommonTag::class);
        $this->price_info_desc = $arr;

        return $this;
    }

    /**
     *分组ID
     *
     * Generated from protobuf field <code>int32 category_id = 9;</code>
     * @return int
     */
    public function getCategoryId()
    {
        return $this->category_id;
    }

    /**
     *分组ID
     *
     * Generated from protobuf field <code>int32 category_id = 9;</code>
     * @param int $var
     * @return $this
     */
    public function setCategoryId($var)
    {
        GPBUtil::checkInt32($var);
        $this->category_id = $var;

        return $this;
    }

    /**
     *车型右边的推荐语
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.CommonTag recommend_tag = 10;</code>
     * @return \Dirpc\SDK\PreSale\CommonTag
     */
    public function getRecommendTag()
    {
        return isset($this->recommend_tag) ? $this->recommend_tag : null;
    }

    public function hasRecommendTag()
    {
        return isset($this->recommend_tag);
    }

    public function clearRecommendTag()
    {
        unset($this->recommend_tag);
    }

    /**
     *车型右边的推荐语
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.CommonTag recommend_tag = 10;</code>
     * @param \Dirpc\SDK\PreSale\CommonTag $var
     * @return $this
     */
    public function setRecommendTag($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\CommonTag::class);
        $this->recommend_tag = $var;

        return $this;
    }

    /**
     *勾选按钮上的气泡tag
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.CommonTag selection_tag = 11;</code>
     * @return \Dirpc\SDK\PreSale\CommonTag
     */
    public function getSelectionTag()
    {
        return isset($this->selection_tag) ? $this->selection_tag : null;
    }

    public function hasSelectionTag()
    {
        return isset($this->selection_tag);
    }

    public function clearSelectionTag()
    {
        unset($this->selection_tag);
    }

    /**
     *勾选按钮上的气泡tag
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.CommonTag selection_tag = 11;</code>
     * @param \Dirpc\SDK\PreSale\CommonTag $var
     * @return $this
     */
    public function setSelectionTag($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\CommonTag::class);
        $this->selection_tag = $var;

        return $this;
    }

    /**
     *附着其他品类
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.LinkProduct link_product = 12;</code>
     * @return \Dirpc\SDK\PreSale\LinkProduct
     */
    public function getLinkProduct()
    {
        return isset($this->link_product) ? $this->link_product : null;
    }

    public function hasLinkProduct()
    {
        return isset($this->link_product);
    }

    public function clearLinkProduct()
    {
        unset($this->link_product);
    }

    /**
     *附着其他品类
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.LinkProduct link_product = 12;</code>
     * @param \Dirpc\SDK\PreSale\LinkProduct $var
     * @return $this
     */
    public function setLinkProduct($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\LinkProduct::class);
        $this->link_product = $var;

        return $this;
    }

    /**
     *拼车座位数组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.CarpoolSeatModule carpool_seat_module = 13;</code>
     * @return \Dirpc\SDK\PreSale\CarpoolSeatModule
     */
    public function getCarpoolSeatModule()
    {
        return isset($this->carpool_seat_module) ? $this->carpool_seat_module : null;
    }

    public function hasCarpoolSeatModule()
    {
        return isset($this->carpool_seat_module);
    }

    public function clearCarpoolSeatModule()
    {
        unset($this->carpool_seat_module);
    }

    /**
     *拼车座位数组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.CarpoolSeatModule carpool_seat_module = 13;</code>
     * @param \Dirpc\SDK\PreSale\CarpoolSeatModule $var
     * @return $this
     */
    public function setCarpoolSeatModule($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\CarpoolSeatModule::class);
        $this->carpool_seat_module = $var;

        return $this;
    }

    /**
     *预估ID
     *
     * Generated from protobuf field <code>string estimate_id = 14;</code>
     * @return string
     */
    public function getEstimateId()
    {
        return $this->estimate_id;
    }

    /**
     *预估ID
     *
     * Generated from protobuf field <code>string estimate_id = 14;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateId($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_id = $var;

        return $this;
    }

    /**
     *车型下方的小字描述
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SubTitleInfo sub_title_list = 15;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getSubTitleList()
    {
        return $this->sub_title_list;
    }

    /**
     *车型下方的小字描述
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SubTitleInfo sub_title_list = 15;</code>
     * @param \Dirpc\SDK\PreSale\SubTitleInfo[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSubTitleList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\SubTitleInfo::class);
        $this->sub_title_list = $arr;

        return $this;
    }

    /**
     *品类下的价格沟通规则、文案和强弹标识
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SpecialPriceText special_price_text = 16;</code>
     * @return \Dirpc\SDK\PreSale\SpecialPriceText
     */
    public function getSpecialPriceText()
    {
        return isset($this->special_price_text) ? $this->special_price_text : null;
    }

    public function hasSpecialPriceText()
    {
        return isset($this->special_price_text);
    }

    public function clearSpecialPriceText()
    {
        unset($this->special_price_text);
    }

    /**
     *品类下的价格沟通规则、文案和强弹标识
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SpecialPriceText special_price_text = 16;</code>
     * @param \Dirpc\SDK\PreSale\SpecialPriceText $var
     * @return $this
     */
    public function setSpecialPriceText($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\SpecialPriceText::class);
        $this->special_price_text = $var;

        return $this;
    }

    /**
     *城际拼车时间片组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.MatchRoutesData match_routes_data = 17;</code>
     * @return \Dirpc\SDK\PreSale\MatchRoutesData
     */
    public function getMatchRoutesData()
    {
        return isset($this->match_routes_data) ? $this->match_routes_data : null;
    }

    public function hasMatchRoutesData()
    {
        return isset($this->match_routes_data);
    }

    public function clearMatchRoutesData()
    {
        unset($this->match_routes_data);
    }

    /**
     *城际拼车时间片组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.MatchRoutesData match_routes_data = 17;</code>
     * @param \Dirpc\SDK\PreSale\MatchRoutesData $var
     * @return $this
     */
    public function setMatchRoutesData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\MatchRoutesData::class);
        $this->match_routes_data = $var;

        return $this;
    }

    /**
     *是否隐藏，两口价用
     *
     * Generated from protobuf field <code>int32 hidden = 18;</code>
     * @return int
     */
    public function getHidden()
    {
        return isset($this->hidden) ? $this->hidden : 0;
    }

    public function hasHidden()
    {
        return isset($this->hidden);
    }

    public function clearHidden()
    {
        unset($this->hidden);
    }

    /**
     *是否隐藏，两口价用
     *
     * Generated from protobuf field <code>int32 hidden = 18;</code>
     * @param int $var
     * @return $this
     */
    public function setHidden($var)
    {
        GPBUtil::checkInt32($var);
        $this->hidden = $var;

        return $this;
    }

    /**
     *拼成乐时间片
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.StationCarpoolBooking station_carpool_booking = 19;</code>
     * @return \Dirpc\SDK\PreSale\StationCarpoolBooking
     */
    public function getStationCarpoolBooking()
    {
        return isset($this->station_carpool_booking) ? $this->station_carpool_booking : null;
    }

    public function hasStationCarpoolBooking()
    {
        return isset($this->station_carpool_booking);
    }

    public function clearStationCarpoolBooking()
    {
        unset($this->station_carpool_booking);
    }

    /**
     *拼成乐时间片
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.StationCarpoolBooking station_carpool_booking = 19;</code>
     * @param \Dirpc\SDK\PreSale\StationCarpoolBooking $var
     * @return $this
     */
    public function setStationCarpoolBooking($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\StationCarpoolBooking::class);
        $this->station_carpool_booking = $var;

        return $this;
    }

    /**
     *费用价格
     *
     * Generated from protobuf field <code>string fee_amount = 20;</code>
     * @return string
     */
    public function getFeeAmount()
    {
        return $this->fee_amount;
    }

    /**
     *费用价格
     *
     * Generated from protobuf field <code>string fee_amount = 20;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeAmount($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_amount = $var;

        return $this;
    }

    /**
     *路线ID，发单参数
     *
     * Generated from protobuf field <code>int32 combo_id = 21;</code>
     * @return int
     */
    public function getComboId()
    {
        return isset($this->combo_id) ? $this->combo_id : 0;
    }

    public function hasComboId()
    {
        return isset($this->combo_id);
    }

    public function clearComboId()
    {
        unset($this->combo_id);
    }

    /**
     *路线ID，发单参数
     *
     * Generated from protobuf field <code>int32 combo_id = 21;</code>
     * @param int $var
     * @return $this
     */
    public function setComboId($var)
    {
        GPBUtil::checkInt32($var);
        $this->combo_id = $var;

        return $this;
    }

    /**
     *个性化服务数据
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SceneData scene_data = 22;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getSceneData()
    {
        return $this->scene_data;
    }

    /**
     *个性化服务数据
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SceneData scene_data = 22;</code>
     * @param \Dirpc\SDK\PreSale\SceneData[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSceneData($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\SceneData::class);
        $this->scene_data = $arr;

        return $this;
    }

    /**
     *粤港车口岸信息
     *
     * Generated from protobuf field <code>string port_type = 23;</code>
     * @return string
     */
    public function getPortType()
    {
        return isset($this->port_type) ? $this->port_type : '';
    }

    public function hasPortType()
    {
        return isset($this->port_type);
    }

    public function clearPortType()
    {
        unset($this->port_type);
    }

    /**
     *粤港车口岸信息
     *
     * Generated from protobuf field <code>string port_type = 23;</code>
     * @param string $var
     * @return $this
     */
    public function setPortType($var)
    {
        GPBUtil::checkString($var, True);
        $this->port_type = $var;

        return $this;
    }

    /**
     *路线ID
     *
     * Generated from protobuf field <code>repeated string route_id_list = 24;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getRouteIdList()
    {
        return $this->route_id_list;
    }

    /**
     *路线ID
     *
     * Generated from protobuf field <code>repeated string route_id_list = 24;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setRouteIdList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->route_id_list = $arr;

        return $this;
    }

    /**
     *个性化服务半弹窗顶部图片
     *
     * Generated from protobuf field <code>string custom_service_popup_image = 25;</code>
     * @return string
     */
    public function getCustomServicePopupImage()
    {
        return isset($this->custom_service_popup_image) ? $this->custom_service_popup_image : '';
    }

    public function hasCustomServicePopupImage()
    {
        return isset($this->custom_service_popup_image);
    }

    public function clearCustomServicePopupImage()
    {
        unset($this->custom_service_popup_image);
    }

    /**
     *个性化服务半弹窗顶部图片
     *
     * Generated from protobuf field <code>string custom_service_popup_image = 25;</code>
     * @param string $var
     * @return $this
     */
    public function setCustomServicePopupImage($var)
    {
        GPBUtil::checkString($var, True);
        $this->custom_service_popup_image = $var;

        return $this;
    }

    /**
     *附加服务的icon
     *
     * Generated from protobuf field <code>repeated string attach_service_icon = 26;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getAttachServiceIcon()
    {
        return $this->attach_service_icon;
    }

    /**
     *附加服务的icon
     *
     * Generated from protobuf field <code>repeated string attach_service_icon = 26;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setAttachServiceIcon($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->attach_service_icon = $arr;

        return $this;
    }

    /**
     *车型表单的支付方式
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.FormUserPayInfo user_pay_info = 27;</code>
     * @return \Dirpc\SDK\PreSale\FormUserPayInfo
     */
    public function getUserPayInfo()
    {
        return isset($this->user_pay_info) ? $this->user_pay_info : null;
    }

    public function hasUserPayInfo()
    {
        return isset($this->user_pay_info);
    }

    public function clearUserPayInfo()
    {
        unset($this->user_pay_info);
    }

    /**
     *车型表单的支付方式
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.FormUserPayInfo user_pay_info = 27;</code>
     * @param \Dirpc\SDK\PreSale\FormUserPayInfo $var
     * @return $this
     */
    public function setUserPayInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\FormUserPayInfo::class);
        $this->user_pay_info = $var;

        return $this;
    }

    /**
     *路线类型
     *
     * Generated from protobuf field <code>int32 route_type = 28;</code>
     * @return int
     */
    public function getRouteType()
    {
        return isset($this->route_type) ? $this->route_type : 0;
    }

    public function hasRouteType()
    {
        return isset($this->route_type);
    }

    public function clearRouteType()
    {
        unset($this->route_type);
    }

    /**
     *路线类型
     *
     * Generated from protobuf field <code>int32 route_type = 28;</code>
     * @param int $var
     * @return $this
     */
    public function setRouteType($var)
    {
        GPBUtil::checkInt32($var);
        $this->route_type = $var;

        return $this;
    }

    /**
     *产品tag描述(多是角标形式)
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ExtraTag extra_tag = 29;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getExtraTag()
    {
        return $this->extra_tag;
    }

    /**
     *产品tag描述(多是角标形式)
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ExtraTag extra_tag = 29;</code>
     * @param \Dirpc\SDK\PreSale\ExtraTag[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setExtraTag($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\ExtraTag::class);
        $this->extra_tag = $arr;

        return $this;
    }

    /**
     *卡片背景图
     *
     * Generated from protobuf field <code>string selected_background_url = 30;</code>
     * @return string
     */
    public function getSelectedBackgroundUrl()
    {
        return isset($this->selected_background_url) ? $this->selected_background_url : '';
    }

    public function hasSelectedBackgroundUrl()
    {
        return isset($this->selected_background_url);
    }

    public function clearSelectedBackgroundUrl()
    {
        unset($this->selected_background_url);
    }

    /**
     *卡片背景图
     *
     * Generated from protobuf field <code>string selected_background_url = 30;</code>
     * @param string $var
     * @return $this
     */
    public function setSelectedBackgroundUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->selected_background_url = $var;

        return $this;
    }

    /**
     *单品类多价格展示
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.MultiPriceDesc multi_price_desc = 31;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getMultiPriceDesc()
    {
        return $this->multi_price_desc;
    }

    /**
     *单品类多价格展示
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.MultiPriceDesc multi_price_desc = 31;</code>
     * @param \Dirpc\SDK\PreSale\MultiPriceDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setMultiPriceDesc($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\MultiPriceDesc::class);
        $this->multi_price_desc = $arr;

        return $this;
    }

    /**
     *是否展示在外层推荐位 0-不展示；1-展示
     *
     * Generated from protobuf field <code>int32 recommend_type = 50;</code>
     * @return int
     */
    public function getRecommendType()
    {
        return $this->recommend_type;
    }

    /**
     *是否展示在外层推荐位 0-不展示；1-展示
     *
     * Generated from protobuf field <code>int32 recommend_type = 50;</code>
     * @param int $var
     * @return $this
     */
    public function setRecommendType($var)
    {
        GPBUtil::checkInt32($var);
        $this->recommend_type = $var;

        return $this;
    }

    /**
     *是否选中 （实时单有多项，预估单仅选中一项）0-不选中；1-选中
     *
     * Generated from protobuf field <code>int32 select_type = 51;</code>
     * @return int
     */
    public function getSelectType()
    {
        return $this->select_type;
    }

    /**
     *是否选中 （实时单有多项，预估单仅选中一项）0-不选中；1-选中
     *
     * Generated from protobuf field <code>int32 select_type = 51;</code>
     * @param int $var
     * @return $this
     */
    public function setSelectType($var)
    {
        GPBUtil::checkInt32($var);
        $this->select_type = $var;

        return $this;
    }

    /**
     *表单展示类型（拼成乐/城际 展示在导流位）0-anycar列表中；1-导流位
     *
     * Generated from protobuf field <code>int32 form_show_type = 52;</code>
     * @return int
     */
    public function getFormShowType()
    {
        return $this->form_show_type;
    }

    /**
     *表单展示类型（拼成乐/城际 展示在导流位）0-anycar列表中；1-导流位
     *
     * Generated from protobuf field <code>int32 form_show_type = 52;</code>
     * @param int $var
     * @return $this
     */
    public function setFormShowType($var)
    {
        GPBUtil::checkInt32($var);
        $this->form_show_type = $var;

        return $this;
    }

    /**
     *展示在导流位上的提示文案（「去预约」）
     *
     * Generated from protobuf field <code>string guide_show_type_tips = 53;</code>
     * @return string
     */
    public function getGuideShowTypeTips()
    {
        return $this->guide_show_type_tips;
    }

    /**
     *展示在导流位上的提示文案（「去预约」）
     *
     * Generated from protobuf field <code>string guide_show_type_tips = 53;</code>
     * @param string $var
     * @return $this
     */
    public function setGuideShowTypeTips($var)
    {
        GPBUtil::checkString($var, True);
        $this->guide_show_type_tips = $var;

        return $this;
    }

    /**
     *专车，豪车个性化设置展示
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PreferData prefer_data = 54;</code>
     * @return \Dirpc\SDK\PreSale\PreferData
     */
    public function getPreferData()
    {
        return isset($this->prefer_data) ? $this->prefer_data : null;
    }

    public function hasPreferData()
    {
        return isset($this->prefer_data);
    }

    public function clearPreferData()
    {
        unset($this->prefer_data);
    }

    /**
     *专车，豪车个性化设置展示
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PreferData prefer_data = 54;</code>
     * @param \Dirpc\SDK\PreSale\PreferData $var
     * @return $this
     */
    public function setPreferData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\PreferData::class);
        $this->prefer_data = $var;

        return $this;
    }

    /**
     *是否命中动调
     *
     * Generated from protobuf field <code>int32 hit_dynamic_price = 55;</code>
     * @return int
     */
    public function getHitDynamicPrice()
    {
        return isset($this->hit_dynamic_price) ? $this->hit_dynamic_price : 0;
    }

    public function hasHitDynamicPrice()
    {
        return isset($this->hit_dynamic_price);
    }

    public function clearHitDynamicPrice()
    {
        unset($this->hit_dynamic_price);
    }

    /**
     *是否命中动调
     *
     * Generated from protobuf field <code>int32 hit_dynamic_price = 55;</code>
     * @param int $var
     * @return $this
     */
    public function setHitDynamicPrice($var)
    {
        GPBUtil::checkInt32($var);
        $this->hit_dynamic_price = $var;

        return $this;
    }

    /**
     *导流位出发时间提示信息
     *
     * Generated from protobuf field <code>string depart_tag = 56;</code>
     * @return string
     */
    public function getDepartTag()
    {
        return isset($this->depart_tag) ? $this->depart_tag : '';
    }

    public function hasDepartTag()
    {
        return isset($this->depart_tag);
    }

    public function clearDepartTag()
    {
        unset($this->depart_tag);
    }

    /**
     *导流位出发时间提示信息
     *
     * Generated from protobuf field <code>string depart_tag = 56;</code>
     * @param string $var
     * @return $this
     */
    public function setDepartTag($var)
    {
        GPBUtil::checkString($var, True);
        $this->depart_tag = $var;

        return $this;
    }

    /**
     *是否第三方产品线
     *
     * Generated from protobuf field <code>int32 is_tripcloud = 57;</code>
     * @return int
     */
    public function getIsTripcloud()
    {
        return isset($this->is_tripcloud) ? $this->is_tripcloud : 0;
    }

    public function hasIsTripcloud()
    {
        return isset($this->is_tripcloud);
    }

    public function clearIsTripcloud()
    {
        unset($this->is_tripcloud);
    }

    /**
     *是否第三方产品线
     *
     * Generated from protobuf field <code>int32 is_tripcloud = 57;</code>
     * @param int $var
     * @return $this
     */
    public function setIsTripcloud($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_tripcloud = $var;

        return $this;
    }

    /**
     *专车个性化服务
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OptionData option_data = 58;</code>
     * @return \Dirpc\SDK\PreSale\OptionData
     */
    public function getOptionData()
    {
        return isset($this->option_data) ? $this->option_data : null;
    }

    public function hasOptionData()
    {
        return isset($this->option_data);
    }

    public function clearOptionData()
    {
        unset($this->option_data);
    }

    /**
     *专车个性化服务
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OptionData option_data = 58;</code>
     * @param \Dirpc\SDK\PreSale\OptionData $var
     * @return $this
     */
    public function setOptionData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\OptionData::class);
        $this->option_data = $var;

        return $this;
    }

    /**
     *无人车地址
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.AutoDriveRedirectionInfo auto_driving_address_info = 59;</code>
     * @return \Dirpc\SDK\PreSale\AutoDriveRedirectionInfo
     */
    public function getAutoDrivingAddressInfo()
    {
        return isset($this->auto_driving_address_info) ? $this->auto_driving_address_info : null;
    }

    public function hasAutoDrivingAddressInfo()
    {
        return isset($this->auto_driving_address_info);
    }

    public function clearAutoDrivingAddressInfo()
    {
        unset($this->auto_driving_address_info);
    }

    /**
     *无人车地址
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.AutoDriveRedirectionInfo auto_driving_address_info = 59;</code>
     * @param \Dirpc\SDK\PreSale\AutoDriveRedirectionInfo $var
     * @return $this
     */
    public function setAutoDrivingAddressInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\AutoDriveRedirectionInfo::class);
        $this->auto_driving_address_info = $var;

        return $this;
    }

    /**
     *不可用状态
     *
     * Generated from protobuf field <code>int32 disabled = 60;</code>
     * @return int
     */
    public function getDisabled()
    {
        return isset($this->disabled) ? $this->disabled : 0;
    }

    public function hasDisabled()
    {
        return isset($this->disabled);
    }

    public function clearDisabled()
    {
        unset($this->disabled);
    }

    /**
     *不可用状态
     *
     * Generated from protobuf field <code>int32 disabled = 60;</code>
     * @param int $var
     * @return $this
     */
    public function setDisabled($var)
    {
        GPBUtil::checkInt32($var);
        $this->disabled = $var;

        return $this;
    }

    /**
     *使用信息（车型下方的下拉文案）
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.UsageInfo usage_info = 61;</code>
     * @return \Dirpc\SDK\PreSale\UsageInfo
     */
    public function getUsageInfo()
    {
        return isset($this->usage_info) ? $this->usage_info : null;
    }

    public function hasUsageInfo()
    {
        return isset($this->usage_info);
    }

    public function clearUsageInfo()
    {
        unset($this->usage_info);
    }

    /**
     *使用信息（车型下方的下拉文案）
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.UsageInfo usage_info = 61;</code>
     * @param \Dirpc\SDK\PreSale\UsageInfo $var
     * @return $this
     */
    public function setUsageInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\UsageInfo::class);
        $this->usage_info = $var;

        return $this;
    }

    /**
     *不可用文案
     *
     * Generated from protobuf field <code>string disabled_text = 62;</code>
     * @return string
     */
    public function getDisabledText()
    {
        return isset($this->disabled_text) ? $this->disabled_text : '';
    }

    public function hasDisabledText()
    {
        return isset($this->disabled_text);
    }

    public function clearDisabledText()
    {
        unset($this->disabled_text);
    }

    /**
     *不可用文案
     *
     * Generated from protobuf field <code>string disabled_text = 62;</code>
     * @param string $var
     * @return $this
     */
    public function setDisabledText($var)
    {
        GPBUtil::checkString($var, True);
        $this->disabled_text = $var;

        return $this;
    }

    /**
     *场景 1-滴滴特快
     *
     * Generated from protobuf field <code>int32 level_type = 63;</code>
     * @return int
     */
    public function getLevelType()
    {
        return isset($this->level_type) ? $this->level_type : 0;
    }

    public function hasLevelType()
    {
        return isset($this->level_type);
    }

    public function clearLevelType()
    {
        unset($this->level_type);
    }

    /**
     *场景 1-滴滴特快
     *
     * Generated from protobuf field <code>int32 level_type = 63;</code>
     * @param int $var
     * @return $this
     */
    public function setLevelType($var)
    {
        GPBUtil::checkInt32($var);
        $this->level_type = $var;

        return $this;
    }

    /**
     *计价模式
     *
     * Generated from protobuf field <code>int32 count_price_type = 64;</code>
     * @return int
     */
    public function getCountPriceType()
    {
        return isset($this->count_price_type) ? $this->count_price_type : 0;
    }

    public function hasCountPriceType()
    {
        return isset($this->count_price_type);
    }

    public function clearCountPriceType()
    {
        unset($this->count_price_type);
    }

    /**
     *计价模式
     *
     * Generated from protobuf field <code>int32 count_price_type = 64;</code>
     * @param int $var
     * @return $this
     */
    public function setCountPriceType($var)
    {
        GPBUtil::checkInt32($var);
        $this->count_price_type = $var;

        return $this;
    }

    /**
     *产品描述背景图
     *
     * Generated from protobuf field <code>string background_url = 65;</code>
     * @return string
     */
    public function getBackgroundUrl()
    {
        return isset($this->background_url) ? $this->background_url : '';
    }

    public function hasBackgroundUrl()
    {
        return isset($this->background_url);
    }

    public function clearBackgroundUrl()
    {
        unset($this->background_url);
    }

    /**
     *产品描述背景图
     *
     * Generated from protobuf field <code>string background_url = 65;</code>
     * @param string $var
     * @return $this
     */
    public function setBackgroundUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->background_url = $var;

        return $this;
    }

    /**
     *产品描述落地页
     *
     * Generated from protobuf field <code>string detail_url = 66;</code>
     * @return string
     */
    public function getDetailUrl()
    {
        return isset($this->detail_url) ? $this->detail_url : '';
    }

    public function hasDetailUrl()
    {
        return isset($this->detail_url);
    }

    public function clearDetailUrl()
    {
        unset($this->detail_url);
    }

    /**
     *产品描述落地页
     *
     * Generated from protobuf field <code>string detail_url = 66;</code>
     * @param string $var
     * @return $this
     */
    public function setDetailUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->detail_url = $var;

        return $this;
    }

    /**
     *个性化服务文案
     *
     * Generated from protobuf field <code>string scene_data_head = 67;</code>
     * @return string
     */
    public function getSceneDataHead()
    {
        return isset($this->scene_data_head) ? $this->scene_data_head : '';
    }

    public function hasSceneDataHead()
    {
        return isset($this->scene_data_head);
    }

    public function clearSceneDataHead()
    {
        unset($this->scene_data_head);
    }

    /**
     *个性化服务文案
     *
     * Generated from protobuf field <code>string scene_data_head = 67;</code>
     * @param string $var
     * @return $this
     */
    public function setSceneDataHead($var)
    {
        GPBUtil::checkString($var, True);
        $this->scene_data_head = $var;

        return $this;
    }

    /**
     *个性化服务说明
     *
     * Generated from protobuf field <code>string scene_data_head_link = 68;</code>
     * @return string
     */
    public function getSceneDataHeadLink()
    {
        return isset($this->scene_data_head_link) ? $this->scene_data_head_link : '';
    }

    public function hasSceneDataHeadLink()
    {
        return isset($this->scene_data_head_link);
    }

    public function clearSceneDataHeadLink()
    {
        unset($this->scene_data_head_link);
    }

    /**
     *个性化服务说明
     *
     * Generated from protobuf field <code>string scene_data_head_link = 68;</code>
     * @param string $var
     * @return $this
     */
    public function setSceneDataHeadLink($var)
    {
        GPBUtil::checkString($var, True);
        $this->scene_data_head_link = $var;

        return $this;
    }

    /**
     *营销类沟通组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PromoteSalesText promote_sales_text = 69;</code>
     * @return \Dirpc\SDK\PreSale\PromoteSalesText
     */
    public function getPromoteSalesText()
    {
        return isset($this->promote_sales_text) ? $this->promote_sales_text : null;
    }

    public function hasPromoteSalesText()
    {
        return isset($this->promote_sales_text);
    }

    public function clearPromoteSalesText()
    {
        unset($this->promote_sales_text);
    }

    /**
     *营销类沟通组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PromoteSalesText promote_sales_text = 69;</code>
     * @param \Dirpc\SDK\PreSale\PromoteSalesText $var
     * @return $this
     */
    public function setPromoteSalesText($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\PromoteSalesText::class);
        $this->promote_sales_text = $var;

        return $this;
    }

    /**
     *6.0出租车业务多品类、短途特惠
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.AggregationEstimateData sub_product = 70;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getSubProduct()
    {
        return $this->sub_product;
    }

    /**
     *6.0出租车业务多品类、短途特惠
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.AggregationEstimateData sub_product = 70;</code>
     * @param \Dirpc\SDK\PreSale\AggregationEstimateData[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSubProduct($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\AggregationEstimateData::class);
        $this->sub_product = $arr;

        return $this;
    }

    /**
     *二级冒泡页的标题
     *
     * Generated from protobuf field <code>string sub_page_title = 71;</code>
     * @return string
     */
    public function getSubPageTitle()
    {
        return isset($this->sub_page_title) ? $this->sub_page_title : '';
    }

    public function hasSubPageTitle()
    {
        return isset($this->sub_page_title);
    }

    public function clearSubPageTitle()
    {
        unset($this->sub_page_title);
    }

    /**
     *二级冒泡页的标题
     *
     * Generated from protobuf field <code>string sub_page_title = 71;</code>
     * @param string $var
     * @return $this
     */
    public function setSubPageTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_page_title = $var;

        return $this;
    }

    /**
     *二级冒泡页的子标题
     *
     * Generated from protobuf field <code>string sub_page_sub_title = 72;</code>
     * @return string
     */
    public function getSubPageSubTitle()
    {
        return isset($this->sub_page_sub_title) ? $this->sub_page_sub_title : '';
    }

    public function hasSubPageSubTitle()
    {
        return isset($this->sub_page_sub_title);
    }

    public function clearSubPageSubTitle()
    {
        unset($this->sub_page_sub_title);
    }

    /**
     *二级冒泡页的子标题
     *
     * Generated from protobuf field <code>string sub_page_sub_title = 72;</code>
     * @param string $var
     * @return $this
     */
    public function setSubPageSubTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_page_sub_title = $var;

        return $this;
    }

    /**
     *标识 1标识是短途特惠 2标识出租车
     *
     * Generated from protobuf field <code>int32 sub_group_id = 73;</code>
     * @return int
     */
    public function getSubGroupId()
    {
        return isset($this->sub_group_id) ? $this->sub_group_id : 0;
    }

    public function hasSubGroupId()
    {
        return isset($this->sub_group_id);
    }

    public function clearSubGroupId()
    {
        unset($this->sub_group_id);
    }

    /**
     *标识 1标识是短途特惠 2标识出租车
     *
     * Generated from protobuf field <code>int32 sub_group_id = 73;</code>
     * @param int $var
     * @return $this
     */
    public function setSubGroupId($var)
    {
        GPBUtil::checkInt32($var);
        $this->sub_group_id = $var;

        return $this;
    }

    /**
     *优选出租车icon图标
     *
     * Generated from protobuf field <code>string sub_intro_icon = 74;</code>
     * @return string
     */
    public function getSubIntroIcon()
    {
        return isset($this->sub_intro_icon) ? $this->sub_intro_icon : '';
    }

    public function hasSubIntroIcon()
    {
        return isset($this->sub_intro_icon);
    }

    public function clearSubIntroIcon()
    {
        unset($this->sub_intro_icon);
    }

    /**
     *优选出租车icon图标
     *
     * Generated from protobuf field <code>string sub_intro_icon = 74;</code>
     * @param string $var
     * @return $this
     */
    public function setSubIntroIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_intro_icon = $var;

        return $this;
    }

    /**
     *混合支付的预估价信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.MixedFeeMsg mixed_fee_msg = 75;</code>
     * @return \Dirpc\SDK\PreSale\MixedFeeMsg
     */
    public function getMixedFeeMsg()
    {
        return isset($this->mixed_fee_msg) ? $this->mixed_fee_msg : null;
    }

    public function hasMixedFeeMsg()
    {
        return isset($this->mixed_fee_msg);
    }

    public function clearMixedFeeMsg()
    {
        unset($this->mixed_fee_msg);
    }

    /**
     *混合支付的预估价信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.MixedFeeMsg mixed_fee_msg = 75;</code>
     * @param \Dirpc\SDK\PreSale\MixedFeeMsg $var
     * @return $this
     */
    public function setMixedFeeMsg($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\MixedFeeMsg::class);
        $this->mixed_fee_msg = $var;

        return $this;
    }

    /**
     *预估快车价信息
     *
     * Generated from protobuf field <code>string cutoff_fee_msg = 76;</code>
     * @return string
     */
    public function getCutoffFeeMsg()
    {
        return isset($this->cutoff_fee_msg) ? $this->cutoff_fee_msg : '';
    }

    public function hasCutoffFeeMsg()
    {
        return isset($this->cutoff_fee_msg);
    }

    public function clearCutoffFeeMsg()
    {
        unset($this->cutoff_fee_msg);
    }

    /**
     *预估快车价信息
     *
     * Generated from protobuf field <code>string cutoff_fee_msg = 76;</code>
     * @param string $var
     * @return $this
     */
    public function setCutoffFeeMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->cutoff_fee_msg = $var;

        return $this;
    }

    /**
     *预估快车价信息
     *
     * Generated from protobuf field <code>string cutoff_fee_info = 77;</code>
     * @return string
     */
    public function getCutoffFeeInfo()
    {
        return isset($this->cutoff_fee_info) ? $this->cutoff_fee_info : '';
    }

    public function hasCutoffFeeInfo()
    {
        return isset($this->cutoff_fee_info);
    }

    public function clearCutoffFeeInfo()
    {
        unset($this->cutoff_fee_info);
    }

    /**
     *预估快车价信息
     *
     * Generated from protobuf field <code>string cutoff_fee_info = 77;</code>
     * @param string $var
     * @return $this
     */
    public function setCutoffFeeInfo($var)
    {
        GPBUtil::checkString($var, True);
        $this->cutoff_fee_info = $var;

        return $this;
    }

    /**
     *导流推荐信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RecommendGuide recommend_guide = 78;</code>
     * @return \Dirpc\SDK\PreSale\RecommendGuide
     */
    public function getRecommendGuide()
    {
        return isset($this->recommend_guide) ? $this->recommend_guide : null;
    }

    public function hasRecommendGuide()
    {
        return isset($this->recommend_guide);
    }

    public function clearRecommendGuide()
    {
        unset($this->recommend_guide);
    }

    /**
     *导流推荐信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RecommendGuide recommend_guide = 78;</code>
     * @param \Dirpc\SDK\PreSale\RecommendGuide $var
     * @return $this
     */
    public function setRecommendGuide($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\RecommendGuide::class);
        $this->recommend_guide = $var;

        return $this;
    }

    /**
     *仅表示聚合子页面是否可以直接弹出，弹出时机端控制（可用于特惠联盟、出租车）
     *
     * Generated from protobuf field <code>bool show_sub_page_directly = 79;</code>
     * @return bool
     */
    public function getShowSubPageDirectly()
    {
        return isset($this->show_sub_page_directly) ? $this->show_sub_page_directly : false;
    }

    public function hasShowSubPageDirectly()
    {
        return isset($this->show_sub_page_directly);
    }

    public function clearShowSubPageDirectly()
    {
        unset($this->show_sub_page_directly);
    }

    /**
     *仅表示聚合子页面是否可以直接弹出，弹出时机端控制（可用于特惠联盟、出租车）
     *
     * Generated from protobuf field <code>bool show_sub_page_directly = 79;</code>
     * @param bool $var
     * @return $this
     */
    public function setShowSubPageDirectly($var)
    {
        GPBUtil::checkBool($var);
        $this->show_sub_page_directly = $var;

        return $this;
    }

    /**
     *特惠联盟参考价，用于计算最大可省
     *
     * Generated from protobuf field <code>string reference_fee_amount = 80;</code>
     * @return string
     */
    public function getReferenceFeeAmount()
    {
        return isset($this->reference_fee_amount) ? $this->reference_fee_amount : '';
    }

    public function hasReferenceFeeAmount()
    {
        return isset($this->reference_fee_amount);
    }

    public function clearReferenceFeeAmount()
    {
        unset($this->reference_fee_amount);
    }

    /**
     *特惠联盟参考价，用于计算最大可省
     *
     * Generated from protobuf field <code>string reference_fee_amount = 80;</code>
     * @param string $var
     * @return $this
     */
    public function setReferenceFeeAmount($var)
    {
        GPBUtil::checkString($var, True);
        $this->reference_fee_amount = $var;

        return $this;
    }

    /**
     *预估show h5命中类型
     *
     * Generated from protobuf field <code>int32 hit_show_h5_type = 81;</code>
     * @return int
     */
    public function getHitShowH5Type()
    {
        return isset($this->hit_show_h5_type) ? $this->hit_show_h5_type : 0;
    }

    public function hasHitShowH5Type()
    {
        return isset($this->hit_show_h5_type);
    }

    public function clearHitShowH5Type()
    {
        unset($this->hit_show_h5_type);
    }

    /**
     *预估show h5命中类型
     *
     * Generated from protobuf field <code>int32 hit_show_h5_type = 81;</code>
     * @param int $var
     * @return $this
     */
    public function setHitShowH5Type($var)
    {
        GPBUtil::checkInt32($var);
        $this->hit_show_h5_type = $var;

        return $this;
    }

    /**
     *预估品类主题标识
     *
     * Generated from protobuf field <code>int32 theme_type = 82;</code>
     * @return int
     */
    public function getThemeType()
    {
        return isset($this->theme_type) ? $this->theme_type : 0;
    }

    public function hasThemeType()
    {
        return isset($this->theme_type);
    }

    public function clearThemeType()
    {
        unset($this->theme_type);
    }

    /**
     *预估品类主题标识
     *
     * Generated from protobuf field <code>int32 theme_type = 82;</code>
     * @param int $var
     * @return $this
     */
    public function setThemeType($var)
    {
        GPBUtil::checkInt32($var);
        $this->theme_type = $var;

        return $this;
    }

    /**
     *sub_title_list隐藏平滑移动信息
     *
     * Generated from protobuf field <code>bool sub_title_hidden_others = 83;</code>
     * @return bool
     */
    public function getSubTitleHiddenOthers()
    {
        return isset($this->sub_title_hidden_others) ? $this->sub_title_hidden_others : false;
    }

    public function hasSubTitleHiddenOthers()
    {
        return isset($this->sub_title_hidden_others);
    }

    public function clearSubTitleHiddenOthers()
    {
        unset($this->sub_title_hidden_others);
    }

    /**
     *sub_title_list隐藏平滑移动信息
     *
     * Generated from protobuf field <code>bool sub_title_hidden_others = 83;</code>
     * @param bool $var
     * @return $this
     */
    public function setSubTitleHiddenOthers($var)
    {
        GPBUtil::checkBool($var);
        $this->sub_title_hidden_others = $var;

        return $this;
    }

    /**
     *预估品类主题数据
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BubbleThemeData theme_data = 84;</code>
     * @return \Dirpc\SDK\PreSale\BubbleThemeData
     */
    public function getThemeData()
    {
        return isset($this->theme_data) ? $this->theme_data : null;
    }

    public function hasThemeData()
    {
        return isset($this->theme_data);
    }

    public function clearThemeData()
    {
        unset($this->theme_data);
    }

    /**
     *预估品类主题数据
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BubbleThemeData theme_data = 84;</code>
     * @param \Dirpc\SDK\PreSale\BubbleThemeData $var
     * @return $this
     */
    public function setThemeData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\BubbleThemeData::class);
        $this->theme_data = $var;

        return $this;
    }

    /**
     *新出租独立小程序推荐文案
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.TaxiRecommendInfTag unitaix_recommend_tag = 85;</code>
     * @return \Dirpc\SDK\PreSale\TaxiRecommendInfTag
     */
    public function getUnitaixRecommendTag()
    {
        return isset($this->unitaix_recommend_tag) ? $this->unitaix_recommend_tag : null;
    }

    public function hasUnitaixRecommendTag()
    {
        return isset($this->unitaix_recommend_tag);
    }

    public function clearUnitaixRecommendTag()
    {
        unset($this->unitaix_recommend_tag);
    }

    /**
     *新出租独立小程序推荐文案
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.TaxiRecommendInfTag unitaix_recommend_tag = 85;</code>
     * @param \Dirpc\SDK\PreSale\TaxiRecommendInfTag $var
     * @return $this
     */
    public function setUnitaixRecommendTag($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\TaxiRecommendInfTag::class);
        $this->unitaix_recommend_tag = $var;

        return $this;
    }

    /**
     *载人车倒计时预估数据
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ExtraEstimateData extra_estimate_data = 86;</code>
     * @return \Dirpc\SDK\PreSale\ExtraEstimateData
     */
    public function getExtraEstimateData()
    {
        return isset($this->extra_estimate_data) ? $this->extra_estimate_data : null;
    }

    public function hasExtraEstimateData()
    {
        return isset($this->extra_estimate_data);
    }

    public function clearExtraEstimateData()
    {
        unset($this->extra_estimate_data);
    }

    /**
     *载人车倒计时预估数据
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ExtraEstimateData extra_estimate_data = 86;</code>
     * @param \Dirpc\SDK\PreSale\ExtraEstimateData $var
     * @return $this
     */
    public function setExtraEstimateData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\ExtraEstimateData::class);
        $this->extra_estimate_data = $var;

        return $this;
    }

    /**
     *0.5盒子
     *
     * Generated from protobuf field <code>string sub_right_title = 87;</code>
     * @return string
     */
    public function getSubRightTitle()
    {
        return isset($this->sub_right_title) ? $this->sub_right_title : '';
    }

    public function hasSubRightTitle()
    {
        return isset($this->sub_right_title);
    }

    public function clearSubRightTitle()
    {
        unset($this->sub_right_title);
    }

    /**
     *0.5盒子
     *
     * Generated from protobuf field <code>string sub_right_title = 87;</code>
     * @param string $var
     * @return $this
     */
    public function setSubRightTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_right_title = $var;

        return $this;
    }

    /**
     *0.5盒子
     *
     * Generated from protobuf field <code>string box_id = 90;</code>
     * @return string
     */
    public function getBoxId()
    {
        return isset($this->box_id) ? $this->box_id : '';
    }

    public function hasBoxId()
    {
        return isset($this->box_id);
    }

    public function clearBoxId()
    {
        unset($this->box_id);
    }

    /**
     *0.5盒子
     *
     * Generated from protobuf field <code>string box_id = 90;</code>
     * @param string $var
     * @return $this
     */
    public function setBoxId($var)
    {
        GPBUtil::checkString($var, True);
        $this->box_id = $var;

        return $this;
    }

    /**
     *导流位跳转链接
     *
     * Generated from protobuf field <code>string guide_path = 91;</code>
     * @return string
     */
    public function getGuidePath()
    {
        return isset($this->guide_path) ? $this->guide_path : '';
    }

    public function hasGuidePath()
    {
        return isset($this->guide_path);
    }

    public function clearGuidePath()
    {
        unset($this->guide_path);
    }

    /**
     *导流位跳转链接
     *
     * Generated from protobuf field <code>string guide_path = 91;</code>
     * @param string $var
     * @return $this
     */
    public function setGuidePath($var)
    {
        GPBUtil::checkString($var, True);
        $this->guide_path = $var;

        return $this;
    }

}

