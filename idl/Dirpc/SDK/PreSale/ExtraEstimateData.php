<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.ExtraEstimateData</code>
 */
class ExtraEstimateData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *价格信息
     *
     * Generated from protobuf field <code>string fee_msg = 1;</code>
     */
    protected $fee_msg = null;
    /**
     *单品类多价格展示
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.MultiPriceDesc multi_price_desc = 2;</code>
     */
    private $multi_price_desc;
    /**
     *预估品类主题数据
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BubbleThemeData theme_data = 3;</code>
     */
    protected $theme_data = null;
    /**
     *预估品类主题数据
     *
     * Generated from protobuf field <code>int32 count_down = 4;</code>
     */
    protected $count_down = null;
    /**
     * Generated from protobuf field <code>string left_down_icon_text = 5;</code>
     */
    protected $left_down_icon_text = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.AthenaExtraInfo extra_order_params = 6;</code>
     */
    protected $extra_order_params = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $fee_msg
     *          价格信息
     *     @type \Dirpc\SDK\PreSale\MultiPriceDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $multi_price_desc
     *          单品类多价格展示
     *     @type \Dirpc\SDK\PreSale\BubbleThemeData $theme_data
     *          预估品类主题数据
     *     @type int $count_down
     *          预估品类主题数据
     *     @type string $left_down_icon_text
     *     @type \Dirpc\SDK\PreSale\AthenaExtraInfo $extra_order_params
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *价格信息
     *
     * Generated from protobuf field <code>string fee_msg = 1;</code>
     * @return string
     */
    public function getFeeMsg()
    {
        return isset($this->fee_msg) ? $this->fee_msg : '';
    }

    public function hasFeeMsg()
    {
        return isset($this->fee_msg);
    }

    public function clearFeeMsg()
    {
        unset($this->fee_msg);
    }

    /**
     *价格信息
     *
     * Generated from protobuf field <code>string fee_msg = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg = $var;

        return $this;
    }

    /**
     *单品类多价格展示
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.MultiPriceDesc multi_price_desc = 2;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getMultiPriceDesc()
    {
        return $this->multi_price_desc;
    }

    /**
     *单品类多价格展示
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.MultiPriceDesc multi_price_desc = 2;</code>
     * @param \Dirpc\SDK\PreSale\MultiPriceDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setMultiPriceDesc($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\MultiPriceDesc::class);
        $this->multi_price_desc = $arr;

        return $this;
    }

    /**
     *预估品类主题数据
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BubbleThemeData theme_data = 3;</code>
     * @return \Dirpc\SDK\PreSale\BubbleThemeData
     */
    public function getThemeData()
    {
        return isset($this->theme_data) ? $this->theme_data : null;
    }

    public function hasThemeData()
    {
        return isset($this->theme_data);
    }

    public function clearThemeData()
    {
        unset($this->theme_data);
    }

    /**
     *预估品类主题数据
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BubbleThemeData theme_data = 3;</code>
     * @param \Dirpc\SDK\PreSale\BubbleThemeData $var
     * @return $this
     */
    public function setThemeData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\BubbleThemeData::class);
        $this->theme_data = $var;

        return $this;
    }

    /**
     *预估品类主题数据
     *
     * Generated from protobuf field <code>int32 count_down = 4;</code>
     * @return int
     */
    public function getCountDown()
    {
        return isset($this->count_down) ? $this->count_down : 0;
    }

    public function hasCountDown()
    {
        return isset($this->count_down);
    }

    public function clearCountDown()
    {
        unset($this->count_down);
    }

    /**
     *预估品类主题数据
     *
     * Generated from protobuf field <code>int32 count_down = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setCountDown($var)
    {
        GPBUtil::checkInt32($var);
        $this->count_down = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string left_down_icon_text = 5;</code>
     * @return string
     */
    public function getLeftDownIconText()
    {
        return isset($this->left_down_icon_text) ? $this->left_down_icon_text : '';
    }

    public function hasLeftDownIconText()
    {
        return isset($this->left_down_icon_text);
    }

    public function clearLeftDownIconText()
    {
        unset($this->left_down_icon_text);
    }

    /**
     * Generated from protobuf field <code>string left_down_icon_text = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setLeftDownIconText($var)
    {
        GPBUtil::checkString($var, True);
        $this->left_down_icon_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.AthenaExtraInfo extra_order_params = 6;</code>
     * @return \Dirpc\SDK\PreSale\AthenaExtraInfo
     */
    public function getExtraOrderParams()
    {
        return isset($this->extra_order_params) ? $this->extra_order_params : null;
    }

    public function hasExtraOrderParams()
    {
        return isset($this->extra_order_params);
    }

    public function clearExtraOrderParams()
    {
        unset($this->extra_order_params);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.AthenaExtraInfo extra_order_params = 6;</code>
     * @param \Dirpc\SDK\PreSale\AthenaExtraInfo $var
     * @return $this
     */
    public function setExtraOrderParams($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\AthenaExtraInfo::class);
        $this->extra_order_params = $var;

        return $this;
    }

}

