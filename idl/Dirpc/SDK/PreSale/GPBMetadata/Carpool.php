<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: carpool.proto

namespace Dirpc\SDK\PreSale\GPBMetadata;

class Carpool
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Nuwa\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "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"
        ), true);

        static::$is_initialized = true;
    }
}

