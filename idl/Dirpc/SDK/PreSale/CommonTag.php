<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.CommonTag</code>
 */
class CommonTag extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string content = 1;</code>
     */
    protected $content = '';
    /**
     * Generated from protobuf field <code>string left_icon = 2;</code>
     */
    protected $left_icon = '';
    /**
     *是否需要转动
     *
     * Generated from protobuf field <code>int32 show_anim = 3;</code>
     */
    protected $show_anim = null;
    /**
     *纯图片标签
     *
     * Generated from protobuf field <code>string icon = 4;</code>
     */
    protected $icon = null;
    /**
     *标签文字颜色
     *
     * Generated from protobuf field <code>repeated string bg_gradients = 5;</code>
     */
    private $bg_gradients;
    /**
     *标签文字颜色
     *
     * Generated from protobuf field <code>string bg_fill_color = 6;</code>
     */
    protected $bg_fill_color = null;
    /**
     *标签文字颜色
     *
     * Generated from protobuf field <code>string font_color = 7;</code>
     */
    protected $font_color = null;
    /**
     *类型
     *
     * Generated from protobuf field <code>string type = 8;</code>
     */
    protected $type = null;
    /**
     *金额
     *
     * Generated from protobuf field <code>string amount = 9;</code>
     */
    protected $amount = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $content
     *     @type string $left_icon
     *     @type int $show_anim
     *          是否需要转动
     *     @type string $icon
     *          纯图片标签
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $bg_gradients
     *          标签文字颜色
     *     @type string $bg_fill_color
     *          标签文字颜色
     *     @type string $font_color
     *          标签文字颜色
     *     @type string $type
     *          类型
     *     @type string $amount
     *          金额
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string content = 1;</code>
     * @return string
     */
    public function getContent()
    {
        return $this->content;
    }

    /**
     * Generated from protobuf field <code>string content = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setContent($var)
    {
        GPBUtil::checkString($var, True);
        $this->content = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string left_icon = 2;</code>
     * @return string
     */
    public function getLeftIcon()
    {
        return $this->left_icon;
    }

    /**
     * Generated from protobuf field <code>string left_icon = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setLeftIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->left_icon = $var;

        return $this;
    }

    /**
     *是否需要转动
     *
     * Generated from protobuf field <code>int32 show_anim = 3;</code>
     * @return int
     */
    public function getShowAnim()
    {
        return isset($this->show_anim) ? $this->show_anim : 0;
    }

    public function hasShowAnim()
    {
        return isset($this->show_anim);
    }

    public function clearShowAnim()
    {
        unset($this->show_anim);
    }

    /**
     *是否需要转动
     *
     * Generated from protobuf field <code>int32 show_anim = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setShowAnim($var)
    {
        GPBUtil::checkInt32($var);
        $this->show_anim = $var;

        return $this;
    }

    /**
     *纯图片标签
     *
     * Generated from protobuf field <code>string icon = 4;</code>
     * @return string
     */
    public function getIcon()
    {
        return isset($this->icon) ? $this->icon : '';
    }

    public function hasIcon()
    {
        return isset($this->icon);
    }

    public function clearIcon()
    {
        unset($this->icon);
    }

    /**
     *纯图片标签
     *
     * Generated from protobuf field <code>string icon = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->icon = $var;

        return $this;
    }

    /**
     *标签文字颜色
     *
     * Generated from protobuf field <code>repeated string bg_gradients = 5;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getBgGradients()
    {
        return $this->bg_gradients;
    }

    /**
     *标签文字颜色
     *
     * Generated from protobuf field <code>repeated string bg_gradients = 5;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBgGradients($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->bg_gradients = $arr;

        return $this;
    }

    /**
     *标签文字颜色
     *
     * Generated from protobuf field <code>string bg_fill_color = 6;</code>
     * @return string
     */
    public function getBgFillColor()
    {
        return isset($this->bg_fill_color) ? $this->bg_fill_color : '';
    }

    public function hasBgFillColor()
    {
        return isset($this->bg_fill_color);
    }

    public function clearBgFillColor()
    {
        unset($this->bg_fill_color);
    }

    /**
     *标签文字颜色
     *
     * Generated from protobuf field <code>string bg_fill_color = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setBgFillColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->bg_fill_color = $var;

        return $this;
    }

    /**
     *标签文字颜色
     *
     * Generated from protobuf field <code>string font_color = 7;</code>
     * @return string
     */
    public function getFontColor()
    {
        return isset($this->font_color) ? $this->font_color : '';
    }

    public function hasFontColor()
    {
        return isset($this->font_color);
    }

    public function clearFontColor()
    {
        unset($this->font_color);
    }

    /**
     *标签文字颜色
     *
     * Generated from protobuf field <code>string font_color = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setFontColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->font_color = $var;

        return $this;
    }

    /**
     *类型
     *
     * Generated from protobuf field <code>string type = 8;</code>
     * @return string
     */
    public function getType()
    {
        return isset($this->type) ? $this->type : '';
    }

    public function hasType()
    {
        return isset($this->type);
    }

    public function clearType()
    {
        unset($this->type);
    }

    /**
     *类型
     *
     * Generated from protobuf field <code>string type = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkString($var, True);
        $this->type = $var;

        return $this;
    }

    /**
     *金额
     *
     * Generated from protobuf field <code>string amount = 9;</code>
     * @return string
     */
    public function getAmount()
    {
        return isset($this->amount) ? $this->amount : '';
    }

    public function hasAmount()
    {
        return isset($this->amount);
    }

    public function clearAmount()
    {
        unset($this->amount);
    }

    /**
     *金额
     *
     * Generated from protobuf field <code>string amount = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setAmount($var)
    {
        GPBUtil::checkString($var, True);
        $this->amount = $var;

        return $this;
    }

}

