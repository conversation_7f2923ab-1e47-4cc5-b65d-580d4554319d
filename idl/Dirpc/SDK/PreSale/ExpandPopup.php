<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.ExpandPopup</code>
 */
class ExpandPopup extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string title = 1;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>string content = 2;</code>
     */
    protected $content = '';
    /**
     * Generated from protobuf field <code>string amount = 3;</code>
     */
    protected $amount = '';
    /**
     * Generated from protobuf field <code>string img_url = 4;</code>
     */
    protected $img_url = '';
    /**
     * Generated from protobuf field <code>string gif_img_url = 5;</code>
     */
    protected $gif_img_url = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $title
     *     @type string $content
     *     @type string $amount
     *     @type string $img_url
     *     @type string $gif_img_url
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string content = 2;</code>
     * @return string
     */
    public function getContent()
    {
        return $this->content;
    }

    /**
     * Generated from protobuf field <code>string content = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setContent($var)
    {
        GPBUtil::checkString($var, True);
        $this->content = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string amount = 3;</code>
     * @return string
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * Generated from protobuf field <code>string amount = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setAmount($var)
    {
        GPBUtil::checkString($var, True);
        $this->amount = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string img_url = 4;</code>
     * @return string
     */
    public function getImgUrl()
    {
        return $this->img_url;
    }

    /**
     * Generated from protobuf field <code>string img_url = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setImgUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->img_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string gif_img_url = 5;</code>
     * @return string
     */
    public function getGifImgUrl()
    {
        return $this->gif_img_url;
    }

    /**
     * Generated from protobuf field <code>string gif_img_url = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setGifImgUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->gif_img_url = $var;

        return $this;
    }

}

