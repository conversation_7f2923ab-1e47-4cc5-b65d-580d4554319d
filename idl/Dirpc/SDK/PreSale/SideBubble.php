<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideBubble</code>
 */
class SideBubble extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SideSubTitle sub_title_list = 2;</code>
     */
    private $sub_title_list;
    /**
     * Generated from protobuf field <code>bool sub_title_hidden_others = 3;</code>
     */
    protected $sub_title_hidden_others = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideEstimateComboRecommend combo_recommend = 4;</code>
     */
    protected $combo_recommend = null;
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SideNewFormFeeDesc fee_desc_list = 5;</code>
     */
    private $fee_desc_list;
    /**
     * Generated from protobuf field <code>string recommend_bubble = 6;</code>
     */
    protected $recommend_bubble = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RightSubTitle right_sub_title = 7;</code>
     */
    protected $right_sub_title = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RecPrizeData rec_prize_data = 8;</code>
     */
    protected $rec_prize_data = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\SideSubTitle[]|\Nuwa\Protobuf\Internal\RepeatedField $sub_title_list
     *     @type bool $sub_title_hidden_others
     *     @type \Dirpc\SDK\PreSale\SideEstimateComboRecommend $combo_recommend
     *     @type \Dirpc\SDK\PreSale\SideNewFormFeeDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $fee_desc_list
     *     @type string $recommend_bubble
     *     @type \Dirpc\SDK\PreSale\RightSubTitle $right_sub_title
     *     @type \Dirpc\SDK\PreSale\RecPrizeData $rec_prize_data
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SideSubTitle sub_title_list = 2;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getSubTitleList()
    {
        return $this->sub_title_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SideSubTitle sub_title_list = 2;</code>
     * @param \Dirpc\SDK\PreSale\SideSubTitle[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSubTitleList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\SideSubTitle::class);
        $this->sub_title_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool sub_title_hidden_others = 3;</code>
     * @return bool
     */
    public function getSubTitleHiddenOthers()
    {
        return isset($this->sub_title_hidden_others) ? $this->sub_title_hidden_others : false;
    }

    public function hasSubTitleHiddenOthers()
    {
        return isset($this->sub_title_hidden_others);
    }

    public function clearSubTitleHiddenOthers()
    {
        unset($this->sub_title_hidden_others);
    }

    /**
     * Generated from protobuf field <code>bool sub_title_hidden_others = 3;</code>
     * @param bool $var
     * @return $this
     */
    public function setSubTitleHiddenOthers($var)
    {
        GPBUtil::checkBool($var);
        $this->sub_title_hidden_others = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideEstimateComboRecommend combo_recommend = 4;</code>
     * @return \Dirpc\SDK\PreSale\SideEstimateComboRecommend
     */
    public function getComboRecommend()
    {
        return isset($this->combo_recommend) ? $this->combo_recommend : null;
    }

    public function hasComboRecommend()
    {
        return isset($this->combo_recommend);
    }

    public function clearComboRecommend()
    {
        unset($this->combo_recommend);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideEstimateComboRecommend combo_recommend = 4;</code>
     * @param \Dirpc\SDK\PreSale\SideEstimateComboRecommend $var
     * @return $this
     */
    public function setComboRecommend($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\SideEstimateComboRecommend::class);
        $this->combo_recommend = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SideNewFormFeeDesc fee_desc_list = 5;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getFeeDescList()
    {
        return $this->fee_desc_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SideNewFormFeeDesc fee_desc_list = 5;</code>
     * @param \Dirpc\SDK\PreSale\SideNewFormFeeDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFeeDescList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\SideNewFormFeeDesc::class);
        $this->fee_desc_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string recommend_bubble = 6;</code>
     * @return string
     */
    public function getRecommendBubble()
    {
        return isset($this->recommend_bubble) ? $this->recommend_bubble : '';
    }

    public function hasRecommendBubble()
    {
        return isset($this->recommend_bubble);
    }

    public function clearRecommendBubble()
    {
        unset($this->recommend_bubble);
    }

    /**
     * Generated from protobuf field <code>string recommend_bubble = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setRecommendBubble($var)
    {
        GPBUtil::checkString($var, True);
        $this->recommend_bubble = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RightSubTitle right_sub_title = 7;</code>
     * @return \Dirpc\SDK\PreSale\RightSubTitle
     */
    public function getRightSubTitle()
    {
        return isset($this->right_sub_title) ? $this->right_sub_title : null;
    }

    public function hasRightSubTitle()
    {
        return isset($this->right_sub_title);
    }

    public function clearRightSubTitle()
    {
        unset($this->right_sub_title);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RightSubTitle right_sub_title = 7;</code>
     * @param \Dirpc\SDK\PreSale\RightSubTitle $var
     * @return $this
     */
    public function setRightSubTitle($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\RightSubTitle::class);
        $this->right_sub_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RecPrizeData rec_prize_data = 8;</code>
     * @return \Dirpc\SDK\PreSale\RecPrizeData
     */
    public function getRecPrizeData()
    {
        return isset($this->rec_prize_data) ? $this->rec_prize_data : null;
    }

    public function hasRecPrizeData()
    {
        return isset($this->rec_prize_data);
    }

    public function clearRecPrizeData()
    {
        unset($this->rec_prize_data);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RecPrizeData rec_prize_data = 8;</code>
     * @param \Dirpc\SDK\PreSale\RecPrizeData $var
     * @return $this
     */
    public function setRecPrizeData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\RecPrizeData::class);
        $this->rec_prize_data = $var;

        return $this;
    }

}

