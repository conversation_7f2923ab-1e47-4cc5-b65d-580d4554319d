<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.BargainFeeMargin</code>
 */
class BargainFeeMargin extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *上限
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BargainFeeMarginNode ceil = 1;</code>
     */
    protected $ceil = null;
    /**
     *下限
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BargainFeeMarginNode floor1 = 2;</code>
     */
    protected $floor1 = null;
    /**
     *下下限
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BargainFeeMarginNode floor2 = 3;</code>
     */
    protected $floor2 = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\BargainFeeMarginNode $ceil
     *          上限
     *     @type \Dirpc\SDK\PreSale\BargainFeeMarginNode $floor1
     *          下限
     *     @type \Dirpc\SDK\PreSale\BargainFeeMarginNode $floor2
     *          下下限
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *上限
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BargainFeeMarginNode ceil = 1;</code>
     * @return \Dirpc\SDK\PreSale\BargainFeeMarginNode
     */
    public function getCeil()
    {
        return isset($this->ceil) ? $this->ceil : null;
    }

    public function hasCeil()
    {
        return isset($this->ceil);
    }

    public function clearCeil()
    {
        unset($this->ceil);
    }

    /**
     *上限
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BargainFeeMarginNode ceil = 1;</code>
     * @param \Dirpc\SDK\PreSale\BargainFeeMarginNode $var
     * @return $this
     */
    public function setCeil($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\BargainFeeMarginNode::class);
        $this->ceil = $var;

        return $this;
    }

    /**
     *下限
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BargainFeeMarginNode floor1 = 2;</code>
     * @return \Dirpc\SDK\PreSale\BargainFeeMarginNode
     */
    public function getFloor1()
    {
        return isset($this->floor1) ? $this->floor1 : null;
    }

    public function hasFloor1()
    {
        return isset($this->floor1);
    }

    public function clearFloor1()
    {
        unset($this->floor1);
    }

    /**
     *下限
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BargainFeeMarginNode floor1 = 2;</code>
     * @param \Dirpc\SDK\PreSale\BargainFeeMarginNode $var
     * @return $this
     */
    public function setFloor1($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\BargainFeeMarginNode::class);
        $this->floor1 = $var;

        return $this;
    }

    /**
     *下下限
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BargainFeeMarginNode floor2 = 3;</code>
     * @return \Dirpc\SDK\PreSale\BargainFeeMarginNode
     */
    public function getFloor2()
    {
        return isset($this->floor2) ? $this->floor2 : null;
    }

    public function hasFloor2()
    {
        return isset($this->floor2);
    }

    public function clearFloor2()
    {
        unset($this->floor2);
    }

    /**
     *下下限
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BargainFeeMarginNode floor2 = 3;</code>
     * @param \Dirpc\SDK\PreSale\BargainFeeMarginNode $var
     * @return $this
     */
    public function setFloor2($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\BargainFeeMarginNode::class);
        $this->floor2 = $var;

        return $this;
    }

}

