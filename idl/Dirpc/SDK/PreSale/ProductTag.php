<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.ProductTag</code>
 */
class ProductTag extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *无下发, 使用默认图标
     *
     * Generated from protobuf field <code>string icon = 1;</code>
     */
    protected $icon = null;
    /**
     *无下发, 使用默认图标
     *
     * Generated from protobuf field <code>string content = 2;</code>
     */
    protected $content = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $icon
     *          无下发, 使用默认图标
     *     @type string $content
     *          无下发, 使用默认图标
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *无下发, 使用默认图标
     *
     * Generated from protobuf field <code>string icon = 1;</code>
     * @return string
     */
    public function getIcon()
    {
        return isset($this->icon) ? $this->icon : '';
    }

    public function hasIcon()
    {
        return isset($this->icon);
    }

    public function clearIcon()
    {
        unset($this->icon);
    }

    /**
     *无下发, 使用默认图标
     *
     * Generated from protobuf field <code>string icon = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->icon = $var;

        return $this;
    }

    /**
     *无下发, 使用默认图标
     *
     * Generated from protobuf field <code>string content = 2;</code>
     * @return string
     */
    public function getContent()
    {
        return $this->content;
    }

    /**
     *无下发, 使用默认图标
     *
     * Generated from protobuf field <code>string content = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setContent($var)
    {
        GPBUtil::checkString($var, True);
        $this->content = $var;

        return $this;
    }

}

