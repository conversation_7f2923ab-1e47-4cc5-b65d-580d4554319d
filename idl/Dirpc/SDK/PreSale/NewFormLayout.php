<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NewFormLayout</code>
 */
class NewFormLayout extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormGroup groups = 1;</code>
     */
    private $groups;
    /**
     * Generated from protobuf field <code>int32 form_show_type = 2;</code>
     */
    protected $form_show_type = 0;
    /**
     *是否支持多选，预约单时不支持 0-不支持；1-支持
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormThemeData theme_data = 3;</code>
     */
    protected $theme_data = null;
    /**
     *是否支持多选，预约单时不支持 0-不支持；1-支持
     *
     * Generated from protobuf field <code>double price = 4;</code>
     */
    protected $price = null;
    /**
     *三方表单侧边栏分组id
     *
     * Generated from protobuf field <code>int32 category_id = 5;</code>
     */
    protected $category_id = null;
    /**
     *折叠状态
     *
     * Generated from protobuf field <code>int32 fold_type = 6;</code>
     */
    protected $fold_type = null;
    /**
     *子标题
     *
     * Generated from protobuf field <code>int32 sub_category_type = 7;</code>
     */
    protected $sub_category_type = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\NewFormGroup[]|\Nuwa\Protobuf\Internal\RepeatedField $groups
     *     @type int $form_show_type
     *     @type \Dirpc\SDK\PreSale\NewFormThemeData $theme_data
     *          是否支持多选，预约单时不支持 0-不支持；1-支持
     *     @type float $price
     *          是否支持多选，预约单时不支持 0-不支持；1-支持
     *     @type int $category_id
     *          三方表单侧边栏分组id
     *     @type int $fold_type
     *          折叠状态
     *     @type int $sub_category_type
     *          子标题
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormGroup groups = 1;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getGroups()
    {
        return $this->groups;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormGroup groups = 1;</code>
     * @param \Dirpc\SDK\PreSale\NewFormGroup[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setGroups($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\NewFormGroup::class);
        $this->groups = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 form_show_type = 2;</code>
     * @return int
     */
    public function getFormShowType()
    {
        return $this->form_show_type;
    }

    /**
     * Generated from protobuf field <code>int32 form_show_type = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setFormShowType($var)
    {
        GPBUtil::checkInt32($var);
        $this->form_show_type = $var;

        return $this;
    }

    /**
     *是否支持多选，预约单时不支持 0-不支持；1-支持
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormThemeData theme_data = 3;</code>
     * @return \Dirpc\SDK\PreSale\NewFormThemeData
     */
    public function getThemeData()
    {
        return isset($this->theme_data) ? $this->theme_data : null;
    }

    public function hasThemeData()
    {
        return isset($this->theme_data);
    }

    public function clearThemeData()
    {
        unset($this->theme_data);
    }

    /**
     *是否支持多选，预约单时不支持 0-不支持；1-支持
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormThemeData theme_data = 3;</code>
     * @param \Dirpc\SDK\PreSale\NewFormThemeData $var
     * @return $this
     */
    public function setThemeData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NewFormThemeData::class);
        $this->theme_data = $var;

        return $this;
    }

    /**
     *是否支持多选，预约单时不支持 0-不支持；1-支持
     *
     * Generated from protobuf field <code>double price = 4;</code>
     * @return float
     */
    public function getPrice()
    {
        return isset($this->price) ? $this->price : 0.0;
    }

    public function hasPrice()
    {
        return isset($this->price);
    }

    public function clearPrice()
    {
        unset($this->price);
    }

    /**
     *是否支持多选，预约单时不支持 0-不支持；1-支持
     *
     * Generated from protobuf field <code>double price = 4;</code>
     * @param float $var
     * @return $this
     */
    public function setPrice($var)
    {
        GPBUtil::checkDouble($var);
        $this->price = $var;

        return $this;
    }

    /**
     *三方表单侧边栏分组id
     *
     * Generated from protobuf field <code>int32 category_id = 5;</code>
     * @return int
     */
    public function getCategoryId()
    {
        return isset($this->category_id) ? $this->category_id : 0;
    }

    public function hasCategoryId()
    {
        return isset($this->category_id);
    }

    public function clearCategoryId()
    {
        unset($this->category_id);
    }

    /**
     *三方表单侧边栏分组id
     *
     * Generated from protobuf field <code>int32 category_id = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setCategoryId($var)
    {
        GPBUtil::checkInt32($var);
        $this->category_id = $var;

        return $this;
    }

    /**
     *折叠状态
     *
     * Generated from protobuf field <code>int32 fold_type = 6;</code>
     * @return int
     */
    public function getFoldType()
    {
        return isset($this->fold_type) ? $this->fold_type : 0;
    }

    public function hasFoldType()
    {
        return isset($this->fold_type);
    }

    public function clearFoldType()
    {
        unset($this->fold_type);
    }

    /**
     *折叠状态
     *
     * Generated from protobuf field <code>int32 fold_type = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setFoldType($var)
    {
        GPBUtil::checkInt32($var);
        $this->fold_type = $var;

        return $this;
    }

    /**
     *子标题
     *
     * Generated from protobuf field <code>int32 sub_category_type = 7;</code>
     * @return int
     */
    public function getSubCategoryType()
    {
        return isset($this->sub_category_type) ? $this->sub_category_type : 0;
    }

    public function hasSubCategoryType()
    {
        return isset($this->sub_category_type);
    }

    public function clearSubCategoryType()
    {
        unset($this->sub_category_type);
    }

    /**
     *子标题
     *
     * Generated from protobuf field <code>int32 sub_category_type = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setSubCategoryType($var)
    {
        GPBUtil::checkInt32($var);
        $this->sub_category_type = $var;

        return $this;
    }

}

