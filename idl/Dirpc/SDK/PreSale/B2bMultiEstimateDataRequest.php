<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.B2bMultiEstimateDataRequest</code>
 */
class B2bMultiEstimateDataRequest extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *客户端参数*
     *
     * Generated from protobuf field <code>string token = 1;</code>
     */
    protected $token = '';
    /**
     * Generated from protobuf field <code>string app_version = 2;</code>
     */
    protected $app_version = '';
    /**
     * Generated from protobuf field <code>int32 access_key_id = 3;</code>
     */
    protected $access_key_id = 0;
    /**
     * Generated from protobuf field <code>int32 channel = 4;</code>
     */
    protected $channel = 0;
    /**
     * Generated from protobuf field <code>int32 client_type = 5;</code>
     */
    protected $client_type = 0;
    /**
     * Generated from protobuf field <code>string lang = 6;</code>
     */
    protected $lang = '';
    /**
     * Generated from protobuf field <code>string maptype = 7;</code>
     */
    protected $maptype = '';
    /**
     * Generated from protobuf field <code>string terminal_id = 8;</code>
     */
    protected $terminal_id = '';
    /**
     * Generated from protobuf field <code>int32 origin_id = 9;</code>
     */
    protected $origin_id = 0;
    /**
     * Generated from protobuf field <code>int32 platform_type = 10;</code>
     */
    protected $platform_type = 0;
    /**
     * Generated from protobuf field <code>string area = 11;</code>
     */
    protected $area = '';
    /**
     * Generated from protobuf field <code>string estimate_trace_id = 12;</code>
     */
    protected $estimate_trace_id = '';
    /**
     *encode过的预估id列表["eid1","e1d2"]
     *
     * Generated from protobuf field <code>string estimate_id_list = 13;</code>
     */
    protected $estimate_id_list = '';
    /**
     *枚举 标识想要获取哪些数据 1:动调信息 2:特殊费用 3: 费用详情 请求费用详情时，estimate_id_list里请只放一个eid 返回只返回一个 4 专车预约单计价费用
     *
     * Generated from protobuf field <code>int32 type = 14;</code>
     */
    protected $type = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $token
     *          客户端参数*
     *     @type string $app_version
     *     @type int $access_key_id
     *     @type int $channel
     *     @type int $client_type
     *     @type string $lang
     *     @type string $maptype
     *     @type string $terminal_id
     *     @type int $origin_id
     *     @type int $platform_type
     *     @type string $area
     *     @type string $estimate_trace_id
     *     @type string $estimate_id_list
     *          encode过的预估id列表["eid1","e1d2"]
     *     @type int $type
     *          枚举 标识想要获取哪些数据 1:动调信息 2:特殊费用 3: 费用详情 请求费用详情时，estimate_id_list里请只放一个eid 返回只返回一个 4 专车预约单计价费用
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *客户端参数*
     *
     * Generated from protobuf field <code>string token = 1;</code>
     * @return string
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     *客户端参数*
     *
     * Generated from protobuf field <code>string token = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->token = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string app_version = 2;</code>
     * @return string
     */
    public function getAppVersion()
    {
        return $this->app_version;
    }

    /**
     * Generated from protobuf field <code>string app_version = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setAppVersion($var)
    {
        GPBUtil::checkString($var, True);
        $this->app_version = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 access_key_id = 3;</code>
     * @return int
     */
    public function getAccessKeyId()
    {
        return $this->access_key_id;
    }

    /**
     * Generated from protobuf field <code>int32 access_key_id = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setAccessKeyId($var)
    {
        GPBUtil::checkInt32($var);
        $this->access_key_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 channel = 4;</code>
     * @return int
     */
    public function getChannel()
    {
        return $this->channel;
    }

    /**
     * Generated from protobuf field <code>int32 channel = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setChannel($var)
    {
        GPBUtil::checkInt32($var);
        $this->channel = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 client_type = 5;</code>
     * @return int
     */
    public function getClientType()
    {
        return $this->client_type;
    }

    /**
     * Generated from protobuf field <code>int32 client_type = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setClientType($var)
    {
        GPBUtil::checkInt32($var);
        $this->client_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string lang = 6;</code>
     * @return string
     */
    public function getLang()
    {
        return $this->lang;
    }

    /**
     * Generated from protobuf field <code>string lang = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setLang($var)
    {
        GPBUtil::checkString($var, True);
        $this->lang = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string maptype = 7;</code>
     * @return string
     */
    public function getMaptype()
    {
        return $this->maptype;
    }

    /**
     * Generated from protobuf field <code>string maptype = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setMaptype($var)
    {
        GPBUtil::checkString($var, True);
        $this->maptype = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string terminal_id = 8;</code>
     * @return string
     */
    public function getTerminalId()
    {
        return $this->terminal_id;
    }

    /**
     * Generated from protobuf field <code>string terminal_id = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setTerminalId($var)
    {
        GPBUtil::checkString($var, True);
        $this->terminal_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 origin_id = 9;</code>
     * @return int
     */
    public function getOriginId()
    {
        return $this->origin_id;
    }

    /**
     * Generated from protobuf field <code>int32 origin_id = 9;</code>
     * @param int $var
     * @return $this
     */
    public function setOriginId($var)
    {
        GPBUtil::checkInt32($var);
        $this->origin_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 platform_type = 10;</code>
     * @return int
     */
    public function getPlatformType()
    {
        return $this->platform_type;
    }

    /**
     * Generated from protobuf field <code>int32 platform_type = 10;</code>
     * @param int $var
     * @return $this
     */
    public function setPlatformType($var)
    {
        GPBUtil::checkInt32($var);
        $this->platform_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string area = 11;</code>
     * @return string
     */
    public function getArea()
    {
        return $this->area;
    }

    /**
     * Generated from protobuf field <code>string area = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setArea($var)
    {
        GPBUtil::checkString($var, True);
        $this->area = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string estimate_trace_id = 12;</code>
     * @return string
     */
    public function getEstimateTraceId()
    {
        return $this->estimate_trace_id;
    }

    /**
     * Generated from protobuf field <code>string estimate_trace_id = 12;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateTraceId($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_trace_id = $var;

        return $this;
    }

    /**
     *encode过的预估id列表["eid1","e1d2"]
     *
     * Generated from protobuf field <code>string estimate_id_list = 13;</code>
     * @return string
     */
    public function getEstimateIdList()
    {
        return $this->estimate_id_list;
    }

    /**
     *encode过的预估id列表["eid1","e1d2"]
     *
     * Generated from protobuf field <code>string estimate_id_list = 13;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateIdList($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_id_list = $var;

        return $this;
    }

    /**
     *枚举 标识想要获取哪些数据 1:动调信息 2:特殊费用 3: 费用详情 请求费用详情时，estimate_id_list里请只放一个eid 返回只返回一个 4 专车预约单计价费用
     *
     * Generated from protobuf field <code>int32 type = 14;</code>
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     *枚举 标识想要获取哪些数据 1:动调信息 2:特殊费用 3: 费用详情 请求费用详情时，estimate_id_list里请只放一个eid 返回只返回一个 4 专车预约单计价费用
     *
     * Generated from protobuf field <code>int32 type = 14;</code>
     * @param int $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkInt32($var);
        $this->type = $var;

        return $this;
    }

}

