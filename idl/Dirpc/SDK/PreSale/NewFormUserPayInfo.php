<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NewFormUserPayInfo</code>
 */
class NewFormUserPayInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string payment_id = 1;</code>
     */
    protected $payment_id = '';
    /**
     *??
     *
     * Generated from protobuf field <code>string business_const_set = 2;</code>
     */
    protected $business_const_set = '';
    /**
     *该支付方式前缀描述
     *
     * Generated from protobuf field <code>string fee_msg_prefix = 4;</code>
     */
    protected $fee_msg_prefix = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $payment_id
     *     @type string $business_const_set
     *          ??
     *     @type string $fee_msg_prefix
     *          该支付方式前缀描述
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string payment_id = 1;</code>
     * @return string
     */
    public function getPaymentId()
    {
        return $this->payment_id;
    }

    /**
     * Generated from protobuf field <code>string payment_id = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setPaymentId($var)
    {
        GPBUtil::checkString($var, True);
        $this->payment_id = $var;

        return $this;
    }

    /**
     *??
     *
     * Generated from protobuf field <code>string business_const_set = 2;</code>
     * @return string
     */
    public function getBusinessConstSet()
    {
        return $this->business_const_set;
    }

    /**
     *??
     *
     * Generated from protobuf field <code>string business_const_set = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setBusinessConstSet($var)
    {
        GPBUtil::checkString($var, True);
        $this->business_const_set = $var;

        return $this;
    }

    /**
     *该支付方式前缀描述
     *
     * Generated from protobuf field <code>string fee_msg_prefix = 4;</code>
     * @return string
     */
    public function getFeeMsgPrefix()
    {
        return isset($this->fee_msg_prefix) ? $this->fee_msg_prefix : '';
    }

    public function hasFeeMsgPrefix()
    {
        return isset($this->fee_msg_prefix);
    }

    public function clearFeeMsgPrefix()
    {
        unset($this->fee_msg_prefix);
    }

    /**
     *该支付方式前缀描述
     *
     * Generated from protobuf field <code>string fee_msg_prefix = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsgPrefix($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg_prefix = $var;

        return $this;
    }

}

