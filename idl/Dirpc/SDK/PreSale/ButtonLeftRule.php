<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.ButtonLeftRule</code>
 */
class ButtonLeftRule extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *查询字典，key为具体event，比如 special_rule
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.SideCommonButtonLeftRule> common = 1;</code>
     */
    private $common;
    /**
     *底部价格沟通+支付沟通，key为eid
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.SideRuleButtonLeft> rule = 2;</code>
     */
    private $rule;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type array|\Nuwa\Protobuf\Internal\MapField $common
     *          查询字典，key为具体event，比如 special_rule
     *     @type array|\Nuwa\Protobuf\Internal\MapField $rule
     *          底部价格沟通+支付沟通，key为eid
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     *查询字典，key为具体event，比如 special_rule
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.SideCommonButtonLeftRule> common = 1;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getCommon()
    {
        return $this->common;
    }

    /**
     *查询字典，key为具体event，比如 special_rule
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.SideCommonButtonLeftRule> common = 1;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setCommon($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\SideCommonButtonLeftRule::class);
        $this->common = $arr;

        return $this;
    }

    /**
     *底部价格沟通+支付沟通，key为eid
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.SideRuleButtonLeft> rule = 2;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getRule()
    {
        return $this->rule;
    }

    /**
     *底部价格沟通+支付沟通，key为eid
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.SideRuleButtonLeft> rule = 2;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setRule($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\SideRuleButtonLeft::class);
        $this->rule = $arr;

        return $this;
    }

}

