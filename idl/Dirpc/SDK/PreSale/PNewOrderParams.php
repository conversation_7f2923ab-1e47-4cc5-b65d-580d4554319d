<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.PNewOrderParams</code>
 */
class PNewOrderParams extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string agent_type = 1;</code>
     */
    protected $agent_type = '';
    /**
     * Generated from protobuf field <code>int32 type = 2;</code>
     */
    protected $type = 0;
    /**
     * Generated from protobuf field <code>int32 page_type = 3;</code>
     */
    protected $page_type = 0;
    /**
     * Generated from protobuf field <code>int64 is_support_multi_selection = 4;</code>
     */
    protected $is_support_multi_selection = 0;
    /**
     * Generated from protobuf field <code>string stopover_points = 5;</code>
     */
    protected $stopover_points = '';
    /**
     * Generated from protobuf field <code>string multi_require_product = 6;</code>
     */
    protected $multi_require_product = '';
    /**
     * Generated from protobuf field <code>string estimate_trace_id = 7;</code>
     */
    protected $estimate_trace_id = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $agent_type
     *     @type int $type
     *     @type int $page_type
     *     @type int|string $is_support_multi_selection
     *     @type string $stopover_points
     *     @type string $multi_require_product
     *     @type string $estimate_trace_id
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string agent_type = 1;</code>
     * @return string
     */
    public function getAgentType()
    {
        return $this->agent_type;
    }

    /**
     * Generated from protobuf field <code>string agent_type = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setAgentType($var)
    {
        GPBUtil::checkString($var, True);
        $this->agent_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 type = 2;</code>
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Generated from protobuf field <code>int32 type = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkInt32($var);
        $this->type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 page_type = 3;</code>
     * @return int
     */
    public function getPageType()
    {
        return $this->page_type;
    }

    /**
     * Generated from protobuf field <code>int32 page_type = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setPageType($var)
    {
        GPBUtil::checkInt32($var);
        $this->page_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int64 is_support_multi_selection = 4;</code>
     * @return int|string
     */
    public function getIsSupportMultiSelection()
    {
        return $this->is_support_multi_selection;
    }

    /**
     * Generated from protobuf field <code>int64 is_support_multi_selection = 4;</code>
     * @param int|string $var
     * @return $this
     */
    public function setIsSupportMultiSelection($var)
    {
        GPBUtil::checkInt64($var);
        $this->is_support_multi_selection = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string stopover_points = 5;</code>
     * @return string
     */
    public function getStopoverPoints()
    {
        return $this->stopover_points;
    }

    /**
     * Generated from protobuf field <code>string stopover_points = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setStopoverPoints($var)
    {
        GPBUtil::checkString($var, True);
        $this->stopover_points = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string multi_require_product = 6;</code>
     * @return string
     */
    public function getMultiRequireProduct()
    {
        return $this->multi_require_product;
    }

    /**
     * Generated from protobuf field <code>string multi_require_product = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setMultiRequireProduct($var)
    {
        GPBUtil::checkString($var, True);
        $this->multi_require_product = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string estimate_trace_id = 7;</code>
     * @return string
     */
    public function getEstimateTraceId()
    {
        return $this->estimate_trace_id;
    }

    /**
     * Generated from protobuf field <code>string estimate_trace_id = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateTraceId($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_trace_id = $var;

        return $this;
    }

}

