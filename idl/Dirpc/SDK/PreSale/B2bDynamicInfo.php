<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.B2bDynamicInfo</code>
 */
class B2bDynamicInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *会员动调保护封顶价格（-1不封顶，0免动调，>0 具体金额）
     *
     * Generated from protobuf field <code>double member_dynamic_capping = 1;</code>
     */
    protected $member_dynamic_capping = 0.0;
    /**
     *实际动调会花费的金额 如果命中会员溢价保护比如只需要付十块 那这个地方价格就是10
     *
     * Generated from protobuf field <code>double dynamic_diff_price = 2;</code>
     */
    protected $dynamic_diff_price = 0.0;
    /**
     *没有会员溢价保护时的动调增幅价格
     *
     * Generated from protobuf field <code>double dynamic_price_without_member_capping = 3;</code>
     */
    protected $dynamic_price_without_member_capping = 0.0;
    /**
     *是否触发会员封顶
     *
     * Generated from protobuf field <code>bool is_hit_member_capping = 4;</code>
     */
    protected $is_hit_member_capping = false;
    /**
     *是否命中动调封顶
     *
     * Generated from protobuf field <code>bool is_hit_dynamic_capping = 5;</code>
     */
    protected $is_hit_dynamic_capping = false;
    /**
     *等于1：当前动调为倍数增长 实际增长的倍数为dynamic_times的值 否则 为金额涨幅
     *
     * Generated from protobuf field <code>int32 if_use_times = 6;</code>
     */
    protected $if_use_times = 0;
    /**
     *如：0.2 表示0.2倍
     *
     * Generated from protobuf field <code>double dynamic_times = 7;</code>
     */
    protected $dynamic_times = 0.0;
    /**
     *动调后账单价格
     *
     * Generated from protobuf field <code>double dynamic_total_fee = 8;</code>
     */
    protected $dynamic_total_fee = 0.0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type float $member_dynamic_capping
     *          会员动调保护封顶价格（-1不封顶，0免动调，>0 具体金额）
     *     @type float $dynamic_diff_price
     *          实际动调会花费的金额 如果命中会员溢价保护比如只需要付十块 那这个地方价格就是10
     *     @type float $dynamic_price_without_member_capping
     *          没有会员溢价保护时的动调增幅价格
     *     @type bool $is_hit_member_capping
     *          是否触发会员封顶
     *     @type bool $is_hit_dynamic_capping
     *          是否命中动调封顶
     *     @type int $if_use_times
     *          等于1：当前动调为倍数增长 实际增长的倍数为dynamic_times的值 否则 为金额涨幅
     *     @type float $dynamic_times
     *          如：0.2 表示0.2倍
     *     @type float $dynamic_total_fee
     *          动调后账单价格
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *会员动调保护封顶价格（-1不封顶，0免动调，>0 具体金额）
     *
     * Generated from protobuf field <code>double member_dynamic_capping = 1;</code>
     * @return float
     */
    public function getMemberDynamicCapping()
    {
        return $this->member_dynamic_capping;
    }

    /**
     *会员动调保护封顶价格（-1不封顶，0免动调，>0 具体金额）
     *
     * Generated from protobuf field <code>double member_dynamic_capping = 1;</code>
     * @param float $var
     * @return $this
     */
    public function setMemberDynamicCapping($var)
    {
        GPBUtil::checkDouble($var);
        $this->member_dynamic_capping = $var;

        return $this;
    }

    /**
     *实际动调会花费的金额 如果命中会员溢价保护比如只需要付十块 那这个地方价格就是10
     *
     * Generated from protobuf field <code>double dynamic_diff_price = 2;</code>
     * @return float
     */
    public function getDynamicDiffPrice()
    {
        return $this->dynamic_diff_price;
    }

    /**
     *实际动调会花费的金额 如果命中会员溢价保护比如只需要付十块 那这个地方价格就是10
     *
     * Generated from protobuf field <code>double dynamic_diff_price = 2;</code>
     * @param float $var
     * @return $this
     */
    public function setDynamicDiffPrice($var)
    {
        GPBUtil::checkDouble($var);
        $this->dynamic_diff_price = $var;

        return $this;
    }

    /**
     *没有会员溢价保护时的动调增幅价格
     *
     * Generated from protobuf field <code>double dynamic_price_without_member_capping = 3;</code>
     * @return float
     */
    public function getDynamicPriceWithoutMemberCapping()
    {
        return $this->dynamic_price_without_member_capping;
    }

    /**
     *没有会员溢价保护时的动调增幅价格
     *
     * Generated from protobuf field <code>double dynamic_price_without_member_capping = 3;</code>
     * @param float $var
     * @return $this
     */
    public function setDynamicPriceWithoutMemberCapping($var)
    {
        GPBUtil::checkDouble($var);
        $this->dynamic_price_without_member_capping = $var;

        return $this;
    }

    /**
     *是否触发会员封顶
     *
     * Generated from protobuf field <code>bool is_hit_member_capping = 4;</code>
     * @return bool
     */
    public function getIsHitMemberCapping()
    {
        return $this->is_hit_member_capping;
    }

    /**
     *是否触发会员封顶
     *
     * Generated from protobuf field <code>bool is_hit_member_capping = 4;</code>
     * @param bool $var
     * @return $this
     */
    public function setIsHitMemberCapping($var)
    {
        GPBUtil::checkBool($var);
        $this->is_hit_member_capping = $var;

        return $this;
    }

    /**
     *是否命中动调封顶
     *
     * Generated from protobuf field <code>bool is_hit_dynamic_capping = 5;</code>
     * @return bool
     */
    public function getIsHitDynamicCapping()
    {
        return $this->is_hit_dynamic_capping;
    }

    /**
     *是否命中动调封顶
     *
     * Generated from protobuf field <code>bool is_hit_dynamic_capping = 5;</code>
     * @param bool $var
     * @return $this
     */
    public function setIsHitDynamicCapping($var)
    {
        GPBUtil::checkBool($var);
        $this->is_hit_dynamic_capping = $var;

        return $this;
    }

    /**
     *等于1：当前动调为倍数增长 实际增长的倍数为dynamic_times的值 否则 为金额涨幅
     *
     * Generated from protobuf field <code>int32 if_use_times = 6;</code>
     * @return int
     */
    public function getIfUseTimes()
    {
        return $this->if_use_times;
    }

    /**
     *等于1：当前动调为倍数增长 实际增长的倍数为dynamic_times的值 否则 为金额涨幅
     *
     * Generated from protobuf field <code>int32 if_use_times = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setIfUseTimes($var)
    {
        GPBUtil::checkInt32($var);
        $this->if_use_times = $var;

        return $this;
    }

    /**
     *如：0.2 表示0.2倍
     *
     * Generated from protobuf field <code>double dynamic_times = 7;</code>
     * @return float
     */
    public function getDynamicTimes()
    {
        return $this->dynamic_times;
    }

    /**
     *如：0.2 表示0.2倍
     *
     * Generated from protobuf field <code>double dynamic_times = 7;</code>
     * @param float $var
     * @return $this
     */
    public function setDynamicTimes($var)
    {
        GPBUtil::checkDouble($var);
        $this->dynamic_times = $var;

        return $this;
    }

    /**
     *动调后账单价格
     *
     * Generated from protobuf field <code>double dynamic_total_fee = 8;</code>
     * @return float
     */
    public function getDynamicTotalFee()
    {
        return $this->dynamic_total_fee;
    }

    /**
     *动调后账单价格
     *
     * Generated from protobuf field <code>double dynamic_total_fee = 8;</code>
     * @param float $var
     * @return $this
     */
    public function setDynamicTotalFee($var)
    {
        GPBUtil::checkDouble($var);
        $this->dynamic_total_fee = $var;

        return $this;
    }

}

