<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SubTitleObject</code>
 */
class SubTitleObject extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *"{9.5}折"
     *
     * Generated from protobuf field <code>string coupon_info = 1;</code>
     */
    protected $coupon_info = null;
    /**
     *"x {1}张"
     *
     * Generated from protobuf field <code>string coupon_number = 2;</code>
     */
    protected $coupon_number = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $coupon_info
     *          "{9.5}折"
     *     @type string $coupon_number
     *          "x {1}张"
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     *"{9.5}折"
     *
     * Generated from protobuf field <code>string coupon_info = 1;</code>
     * @return string
     */
    public function getCouponInfo()
    {
        return isset($this->coupon_info) ? $this->coupon_info : '';
    }

    public function hasCouponInfo()
    {
        return isset($this->coupon_info);
    }

    public function clearCouponInfo()
    {
        unset($this->coupon_info);
    }

    /**
     *"{9.5}折"
     *
     * Generated from protobuf field <code>string coupon_info = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setCouponInfo($var)
    {
        GPBUtil::checkString($var, True);
        $this->coupon_info = $var;

        return $this;
    }

    /**
     *"x {1}张"
     *
     * Generated from protobuf field <code>string coupon_number = 2;</code>
     * @return string
     */
    public function getCouponNumber()
    {
        return isset($this->coupon_number) ? $this->coupon_number : '';
    }

    public function hasCouponNumber()
    {
        return isset($this->coupon_number);
    }

    public function clearCouponNumber()
    {
        unset($this->coupon_number);
    }

    /**
     *"x {1}张"
     *
     * Generated from protobuf field <code>string coupon_number = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setCouponNumber($var)
    {
        GPBUtil::checkString($var, True);
        $this->coupon_number = $var;

        return $this;
    }

}

