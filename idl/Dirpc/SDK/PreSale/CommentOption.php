<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.CommentOption</code>
 */
class CommentOption extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 option_id = 1;</code>
     */
    protected $option_id = null;
    /**
     * Generated from protobuf field <code>string text = 2;</code>
     */
    protected $text = null;
    /**
     * Generated from protobuf field <code>bool is_select = 3;</code>
     */
    protected $is_select = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $option_id
     *     @type string $text
     *     @type bool $is_select
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 option_id = 1;</code>
     * @return int
     */
    public function getOptionId()
    {
        return isset($this->option_id) ? $this->option_id : 0;
    }

    public function hasOptionId()
    {
        return isset($this->option_id);
    }

    public function clearOptionId()
    {
        unset($this->option_id);
    }

    /**
     * Generated from protobuf field <code>int32 option_id = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setOptionId($var)
    {
        GPBUtil::checkInt32($var);
        $this->option_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string text = 2;</code>
     * @return string
     */
    public function getText()
    {
        return isset($this->text) ? $this->text : '';
    }

    public function hasText()
    {
        return isset($this->text);
    }

    public function clearText()
    {
        unset($this->text);
    }

    /**
     * Generated from protobuf field <code>string text = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setText($var)
    {
        GPBUtil::checkString($var, True);
        $this->text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool is_select = 3;</code>
     * @return bool
     */
    public function getIsSelect()
    {
        return isset($this->is_select) ? $this->is_select : false;
    }

    public function hasIsSelect()
    {
        return isset($this->is_select);
    }

    public function clearIsSelect()
    {
        unset($this->is_select);
    }

    /**
     * Generated from protobuf field <code>bool is_select = 3;</code>
     * @param bool $var
     * @return $this
     */
    public function setIsSelect($var)
    {
        GPBUtil::checkBool($var);
        $this->is_select = $var;

        return $this;
    }

}

