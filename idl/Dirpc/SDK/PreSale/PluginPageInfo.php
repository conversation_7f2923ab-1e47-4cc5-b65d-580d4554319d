<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.PluginPageInfo</code>
 */
class PluginPageInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 type = 1;</code>
     */
    protected $type = 0;
    /**
     * Generated from protobuf field <code>string show_h5 = 2;</code>
     */
    protected $show_h5 = '';
    /**
     * Generated from protobuf field <code>string confirm_h5 = 3;</code>
     */
    protected $confirm_h5 = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $type
     *     @type string $show_h5
     *     @type string $confirm_h5
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 type = 1;</code>
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Generated from protobuf field <code>int32 type = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkInt32($var);
        $this->type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string show_h5 = 2;</code>
     * @return string
     */
    public function getShowH5()
    {
        return $this->show_h5;
    }

    /**
     * Generated from protobuf field <code>string show_h5 = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setShowH5($var)
    {
        GPBUtil::checkString($var, True);
        $this->show_h5 = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string confirm_h5 = 3;</code>
     * @return string
     */
    public function getConfirmH5()
    {
        return $this->confirm_h5;
    }

    /**
     * Generated from protobuf field <code>string confirm_h5 = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setConfirmH5($var)
    {
        GPBUtil::checkString($var, True);
        $this->confirm_h5 = $var;

        return $this;
    }

}

