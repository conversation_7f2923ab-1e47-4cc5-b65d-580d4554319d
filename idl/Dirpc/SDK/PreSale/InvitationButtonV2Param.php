<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: carpool.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.InvitationButtonV2Param</code>
 */
class InvitationButtonV2Param extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.AddressInfo address_info = 1;</code>
     */
    protected $address_info = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\AddressInfo $address_info
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\Carpool::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.AddressInfo address_info = 1;</code>
     * @return \Dirpc\SDK\PreSale\AddressInfo
     */
    public function getAddressInfo()
    {
        return isset($this->address_info) ? $this->address_info : null;
    }

    public function hasAddressInfo()
    {
        return isset($this->address_info);
    }

    public function clearAddressInfo()
    {
        unset($this->address_info);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.AddressInfo address_info = 1;</code>
     * @param \Dirpc\SDK\PreSale\AddressInfo $var
     * @return $this
     */
    public function setAddressInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\AddressInfo::class);
        $this->address_info = $var;

        return $this;
    }

}

