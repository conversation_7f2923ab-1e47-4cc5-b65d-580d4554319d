<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.GuideBarButtonInfo</code>
 */
class GuideBarButtonInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *背景色
     *
     * Generated from protobuf field <code>repeated string background_gradients = 1;</code>
     */
    private $background_gradients;
    /**
     *文本颜色
     *
     * Generated from protobuf field <code>string text_color = 2;</code>
     */
    protected $text_color = '';
    /**
     *包框颜色
     *
     * Generated from protobuf field <code>string border_color = 3;</code>
     */
    protected $border_color = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $background_gradients
     *          背景色
     *     @type string $text_color
     *          文本颜色
     *     @type string $border_color
     *          包框颜色
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     *背景色
     *
     * Generated from protobuf field <code>repeated string background_gradients = 1;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getBackgroundGradients()
    {
        return $this->background_gradients;
    }

    /**
     *背景色
     *
     * Generated from protobuf field <code>repeated string background_gradients = 1;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBackgroundGradients($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->background_gradients = $arr;

        return $this;
    }

    /**
     *文本颜色
     *
     * Generated from protobuf field <code>string text_color = 2;</code>
     * @return string
     */
    public function getTextColor()
    {
        return $this->text_color;
    }

    /**
     *文本颜色
     *
     * Generated from protobuf field <code>string text_color = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTextColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->text_color = $var;

        return $this;
    }

    /**
     *包框颜色
     *
     * Generated from protobuf field <code>string border_color = 3;</code>
     * @return string
     */
    public function getBorderColor()
    {
        return $this->border_color;
    }

    /**
     *包框颜色
     *
     * Generated from protobuf field <code>string border_color = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setBorderColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->border_color = $var;

        return $this;
    }

}

