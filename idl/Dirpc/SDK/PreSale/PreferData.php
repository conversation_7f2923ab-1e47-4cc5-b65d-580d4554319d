<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 *专车，豪车定制服务设置
 *
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.PreferData</code>
 */
class PreferData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *标题：可选服务 | 已选服务
     *
     * Generated from protobuf field <code>string prefer_desc = 1;</code>
     */
    protected $prefer_desc = '';
    /**
     *已选服务数量
     *
     * Generated from protobuf field <code>int32 prefer_select_count = 2;</code>
     */
    protected $prefer_select_count = 0;
    /**
     *可选服务总数
     *
     * Generated from protobuf field <code>int32 prefer_total_count = 3;</code>
     */
    protected $prefer_total_count = 0;
    /**
     *用户选择服务的标签和图标
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.PreferTag prefer_display_tags = 4;</code>
     */
    private $prefer_display_tags;
    /**
     *0-全页面；1-半弹屏
     *
     * Generated from protobuf field <code>int32 prefer_popup_type = 5;</code>
     */
    protected $prefer_popup_type = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $prefer_desc
     *          标题：可选服务 | 已选服务
     *     @type int $prefer_select_count
     *          已选服务数量
     *     @type int $prefer_total_count
     *          可选服务总数
     *     @type \Dirpc\SDK\PreSale\PreferTag[]|\Nuwa\Protobuf\Internal\RepeatedField $prefer_display_tags
     *          用户选择服务的标签和图标
     *     @type int $prefer_popup_type
     *          0-全页面；1-半弹屏
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *标题：可选服务 | 已选服务
     *
     * Generated from protobuf field <code>string prefer_desc = 1;</code>
     * @return string
     */
    public function getPreferDesc()
    {
        return $this->prefer_desc;
    }

    /**
     *标题：可选服务 | 已选服务
     *
     * Generated from protobuf field <code>string prefer_desc = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setPreferDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->prefer_desc = $var;

        return $this;
    }

    /**
     *已选服务数量
     *
     * Generated from protobuf field <code>int32 prefer_select_count = 2;</code>
     * @return int
     */
    public function getPreferSelectCount()
    {
        return $this->prefer_select_count;
    }

    /**
     *已选服务数量
     *
     * Generated from protobuf field <code>int32 prefer_select_count = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setPreferSelectCount($var)
    {
        GPBUtil::checkInt32($var);
        $this->prefer_select_count = $var;

        return $this;
    }

    /**
     *可选服务总数
     *
     * Generated from protobuf field <code>int32 prefer_total_count = 3;</code>
     * @return int
     */
    public function getPreferTotalCount()
    {
        return $this->prefer_total_count;
    }

    /**
     *可选服务总数
     *
     * Generated from protobuf field <code>int32 prefer_total_count = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setPreferTotalCount($var)
    {
        GPBUtil::checkInt32($var);
        $this->prefer_total_count = $var;

        return $this;
    }

    /**
     *用户选择服务的标签和图标
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.PreferTag prefer_display_tags = 4;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getPreferDisplayTags()
    {
        return $this->prefer_display_tags;
    }

    /**
     *用户选择服务的标签和图标
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.PreferTag prefer_display_tags = 4;</code>
     * @param \Dirpc\SDK\PreSale\PreferTag[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setPreferDisplayTags($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\PreferTag::class);
        $this->prefer_display_tags = $arr;

        return $this;
    }

    /**
     *0-全页面；1-半弹屏
     *
     * Generated from protobuf field <code>int32 prefer_popup_type = 5;</code>
     * @return int
     */
    public function getPreferPopupType()
    {
        return $this->prefer_popup_type;
    }

    /**
     *0-全页面；1-半弹屏
     *
     * Generated from protobuf field <code>int32 prefer_popup_type = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setPreferPopupType($var)
    {
        GPBUtil::checkInt32($var);
        $this->prefer_popup_type = $var;

        return $this;
    }

}

