<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.AgreementPopup</code>
 */
class AgreementPopup extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string title = 1;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>string content = 2;</code>
     */
    protected $content = '';
    /**
     * Generated from protobuf field <code>string sub_content = 3;</code>
     */
    protected $sub_content = '';
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectButton left_button = 4;</code>
     */
    protected $left_button = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectButton right_button = 5;</code>
     */
    protected $right_button = null;
    /**
     * Generated from protobuf field <code>string omega_event_id = 6;</code>
     */
    protected $omega_event_id = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $title
     *     @type string $content
     *     @type string $sub_content
     *     @type \Dirpc\SDK\PreSale\EjectButton $left_button
     *     @type \Dirpc\SDK\PreSale\EjectButton $right_button
     *     @type string $omega_event_id
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string content = 2;</code>
     * @return string
     */
    public function getContent()
    {
        return $this->content;
    }

    /**
     * Generated from protobuf field <code>string content = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setContent($var)
    {
        GPBUtil::checkString($var, True);
        $this->content = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string sub_content = 3;</code>
     * @return string
     */
    public function getSubContent()
    {
        return $this->sub_content;
    }

    /**
     * Generated from protobuf field <code>string sub_content = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setSubContent($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_content = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectButton left_button = 4;</code>
     * @return \Dirpc\SDK\PreSale\EjectButton
     */
    public function getLeftButton()
    {
        return isset($this->left_button) ? $this->left_button : null;
    }

    public function hasLeftButton()
    {
        return isset($this->left_button);
    }

    public function clearLeftButton()
    {
        unset($this->left_button);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectButton left_button = 4;</code>
     * @param \Dirpc\SDK\PreSale\EjectButton $var
     * @return $this
     */
    public function setLeftButton($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\EjectButton::class);
        $this->left_button = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectButton right_button = 5;</code>
     * @return \Dirpc\SDK\PreSale\EjectButton
     */
    public function getRightButton()
    {
        return isset($this->right_button) ? $this->right_button : null;
    }

    public function hasRightButton()
    {
        return isset($this->right_button);
    }

    public function clearRightButton()
    {
        unset($this->right_button);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectButton right_button = 5;</code>
     * @param \Dirpc\SDK\PreSale\EjectButton $var
     * @return $this
     */
    public function setRightButton($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\EjectButton::class);
        $this->right_button = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string omega_event_id = 6;</code>
     * @return string
     */
    public function getOmegaEventId()
    {
        return $this->omega_event_id;
    }

    /**
     * Generated from protobuf field <code>string omega_event_id = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setOmegaEventId($var)
    {
        GPBUtil::checkString($var, True);
        $this->omega_event_id = $var;

        return $this;
    }

}

