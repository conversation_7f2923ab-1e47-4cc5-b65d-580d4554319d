<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.CapExtraMap</code>
 */
class CapExtraMap extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 product_id = 1;</code>
     */
    protected $product_id = 0;
    /**
     * Generated from protobuf field <code>int32 business_id = 2;</code>
     */
    protected $business_id = 0;
    /**
     * Generated from protobuf field <code>int32 combo_type = 3;</code>
     */
    protected $combo_type = 0;
    /**
     * Generated from protobuf field <code>int32 require_level = 4;</code>
     */
    protected $require_level = 0;
    /**
     * Generated from protobuf field <code>int32 level_type = 5;</code>
     */
    protected $level_type = 0;
    /**
     * Generated from protobuf field <code>int32 count_price_type = 6;</code>
     */
    protected $count_price_type = 0;
    /**
     * Generated from protobuf field <code>int32 product_category = 7;</code>
     */
    protected $product_category = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $product_id
     *     @type int $business_id
     *     @type int $combo_type
     *     @type int $require_level
     *     @type int $level_type
     *     @type int $count_price_type
     *     @type int $product_category
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 product_id = 1;</code>
     * @return int
     */
    public function getProductId()
    {
        return $this->product_id;
    }

    /**
     * Generated from protobuf field <code>int32 product_id = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setProductId($var)
    {
        GPBUtil::checkInt32($var);
        $this->product_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 business_id = 2;</code>
     * @return int
     */
    public function getBusinessId()
    {
        return $this->business_id;
    }

    /**
     * Generated from protobuf field <code>int32 business_id = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setBusinessId($var)
    {
        GPBUtil::checkInt32($var);
        $this->business_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 combo_type = 3;</code>
     * @return int
     */
    public function getComboType()
    {
        return $this->combo_type;
    }

    /**
     * Generated from protobuf field <code>int32 combo_type = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setComboType($var)
    {
        GPBUtil::checkInt32($var);
        $this->combo_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 require_level = 4;</code>
     * @return int
     */
    public function getRequireLevel()
    {
        return $this->require_level;
    }

    /**
     * Generated from protobuf field <code>int32 require_level = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setRequireLevel($var)
    {
        GPBUtil::checkInt32($var);
        $this->require_level = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 level_type = 5;</code>
     * @return int
     */
    public function getLevelType()
    {
        return $this->level_type;
    }

    /**
     * Generated from protobuf field <code>int32 level_type = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setLevelType($var)
    {
        GPBUtil::checkInt32($var);
        $this->level_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 count_price_type = 6;</code>
     * @return int
     */
    public function getCountPriceType()
    {
        return $this->count_price_type;
    }

    /**
     * Generated from protobuf field <code>int32 count_price_type = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setCountPriceType($var)
    {
        GPBUtil::checkInt32($var);
        $this->count_price_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 product_category = 7;</code>
     * @return int
     */
    public function getProductCategory()
    {
        return $this->product_category;
    }

    /**
     * Generated from protobuf field <code>int32 product_category = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setProductCategory($var)
    {
        GPBUtil::checkInt32($var);
        $this->product_category = $var;

        return $this;
    }

}

