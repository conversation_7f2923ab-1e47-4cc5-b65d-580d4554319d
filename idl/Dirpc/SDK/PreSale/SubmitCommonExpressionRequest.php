<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SubmitCommonExpressionRequest</code>
 */
class SubmitCommonExpressionRequest extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string token = 1;</code>
     */
    protected $token = '';
    /**
     * Generated from protobuf field <code>string common_expression = 2;</code>
     */
    protected $common_expression = '';
    /**
     * Generated from protobuf field <code>int32 business_id = 3;</code>
     */
    protected $business_id = 0;
    /**
     * Generated from protobuf field <code>int32 action_type = 4;</code>
     */
    protected $action_type = 0;
    /**
     * Generated from protobuf field <code>string lang = 5;</code>
     */
    protected $lang = '';
    /**
     * Generated from protobuf field <code>int64 uid = 6;</code>
     */
    protected $uid = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $token
     *     @type string $common_expression
     *     @type int $business_id
     *     @type int $action_type
     *     @type string $lang
     *     @type int|string $uid
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string token = 1;</code>
     * @return string
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     * Generated from protobuf field <code>string token = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->token = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string common_expression = 2;</code>
     * @return string
     */
    public function getCommonExpression()
    {
        return $this->common_expression;
    }

    /**
     * Generated from protobuf field <code>string common_expression = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setCommonExpression($var)
    {
        GPBUtil::checkString($var, True);
        $this->common_expression = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 business_id = 3;</code>
     * @return int
     */
    public function getBusinessId()
    {
        return $this->business_id;
    }

    /**
     * Generated from protobuf field <code>int32 business_id = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setBusinessId($var)
    {
        GPBUtil::checkInt32($var);
        $this->business_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 action_type = 4;</code>
     * @return int
     */
    public function getActionType()
    {
        return $this->action_type;
    }

    /**
     * Generated from protobuf field <code>int32 action_type = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setActionType($var)
    {
        GPBUtil::checkInt32($var);
        $this->action_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string lang = 5;</code>
     * @return string
     */
    public function getLang()
    {
        return $this->lang;
    }

    /**
     * Generated from protobuf field <code>string lang = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setLang($var)
    {
        GPBUtil::checkString($var, True);
        $this->lang = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int64 uid = 6;</code>
     * @return int|string
     */
    public function getUid()
    {
        return $this->uid;
    }

    /**
     * Generated from protobuf field <code>int64 uid = 6;</code>
     * @param int|string $var
     * @return $this
     */
    public function setUid($var)
    {
        GPBUtil::checkInt64($var);
        $this->uid = $var;

        return $this;
    }

}

