<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 *新出租独立小程序推荐文案信息
 *
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.TaxiRecommendInfTag</code>
 */
class TaxiRecommendInfTag extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *文案内容
     *
     * Generated from protobuf field <code>string content = 1;</code>
     */
    protected $content = '';
    /**
     *tag图标
     *
     * Generated from protobuf field <code>string tag_icon = 2;</code>
     */
    protected $tag_icon = '';
    /**
     *气泡下方图标
     *
     * Generated from protobuf field <code>string left_icon = 3;</code>
     */
    protected $left_icon = '';
    /**
     *背景图片
     *
     * Generated from protobuf field <code>string bg_image = 4;</code>
     */
    protected $bg_image = '';
    /**
     *字体颜色
     *
     * Generated from protobuf field <code>string font_color = 5;</code>
     */
    protected $font_color = '';
    /**
     *配置展示信息
     *
     * Generated from protobuf field <code>string conf_info = 6;</code>
     */
    protected $conf_info = '';
    /**
     *保留字段
     *
     * Generated from protobuf field <code>string other_info = 7;</code>
     */
    protected $other_info = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $content
     *          文案内容
     *     @type string $tag_icon
     *          tag图标
     *     @type string $left_icon
     *          气泡下方图标
     *     @type string $bg_image
     *          背景图片
     *     @type string $font_color
     *          字体颜色
     *     @type string $conf_info
     *          配置展示信息
     *     @type string $other_info
     *          保留字段
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *文案内容
     *
     * Generated from protobuf field <code>string content = 1;</code>
     * @return string
     */
    public function getContent()
    {
        return $this->content;
    }

    /**
     *文案内容
     *
     * Generated from protobuf field <code>string content = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setContent($var)
    {
        GPBUtil::checkString($var, True);
        $this->content = $var;

        return $this;
    }

    /**
     *tag图标
     *
     * Generated from protobuf field <code>string tag_icon = 2;</code>
     * @return string
     */
    public function getTagIcon()
    {
        return $this->tag_icon;
    }

    /**
     *tag图标
     *
     * Generated from protobuf field <code>string tag_icon = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTagIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->tag_icon = $var;

        return $this;
    }

    /**
     *气泡下方图标
     *
     * Generated from protobuf field <code>string left_icon = 3;</code>
     * @return string
     */
    public function getLeftIcon()
    {
        return $this->left_icon;
    }

    /**
     *气泡下方图标
     *
     * Generated from protobuf field <code>string left_icon = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setLeftIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->left_icon = $var;

        return $this;
    }

    /**
     *背景图片
     *
     * Generated from protobuf field <code>string bg_image = 4;</code>
     * @return string
     */
    public function getBgImage()
    {
        return $this->bg_image;
    }

    /**
     *背景图片
     *
     * Generated from protobuf field <code>string bg_image = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setBgImage($var)
    {
        GPBUtil::checkString($var, True);
        $this->bg_image = $var;

        return $this;
    }

    /**
     *字体颜色
     *
     * Generated from protobuf field <code>string font_color = 5;</code>
     * @return string
     */
    public function getFontColor()
    {
        return $this->font_color;
    }

    /**
     *字体颜色
     *
     * Generated from protobuf field <code>string font_color = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setFontColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->font_color = $var;

        return $this;
    }

    /**
     *配置展示信息
     *
     * Generated from protobuf field <code>string conf_info = 6;</code>
     * @return string
     */
    public function getConfInfo()
    {
        return $this->conf_info;
    }

    /**
     *配置展示信息
     *
     * Generated from protobuf field <code>string conf_info = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setConfInfo($var)
    {
        GPBUtil::checkString($var, True);
        $this->conf_info = $var;

        return $this;
    }

    /**
     *保留字段
     *
     * Generated from protobuf field <code>string other_info = 7;</code>
     * @return string
     */
    public function getOtherInfo()
    {
        return isset($this->other_info) ? $this->other_info : '';
    }

    public function hasOtherInfo()
    {
        return isset($this->other_info);
    }

    public function clearOtherInfo()
    {
        unset($this->other_info);
    }

    /**
     *保留字段
     *
     * Generated from protobuf field <code>string other_info = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setOtherInfo($var)
    {
        GPBUtil::checkString($var, True);
        $this->other_info = $var;

        return $this;
    }

}

