<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use <PERSON>uwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideActionParams</code>
 */
class SideActionParams extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 region_id = 1;</code>
     */
    protected $region_id = 0;
    /**
     *起点站点id
     *
     * Generated from protobuf field <code>int32 start_station_id = 2;</code>
     */
    protected $start_station_id = 0;
    /**
     *终点站点id
     *
     * Generated from protobuf field <code>int32 end_station_id = 3;</code>
     */
    protected $end_station_id = 0;
    /**
     *终点站点id
     *
     * Generated from protobuf field <code>int32 route_id = 4;</code>
     */
    protected $route_id = 0;
    /**
     * Generated from protobuf field <code>int32 page_type = 5;</code>
     */
    protected $page_type = 0;
    /**
     * Generated from protobuf field <code>int32 distance_type = 6;</code>
     */
    protected $distance_type = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $region_id
     *     @type int $start_station_id
     *          起点站点id
     *     @type int $end_station_id
     *          终点站点id
     *     @type int $route_id
     *          终点站点id
     *     @type int $page_type
     *     @type int $distance_type
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 region_id = 1;</code>
     * @return int
     */
    public function getRegionId()
    {
        return $this->region_id;
    }

    /**
     * Generated from protobuf field <code>int32 region_id = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setRegionId($var)
    {
        GPBUtil::checkInt32($var);
        $this->region_id = $var;

        return $this;
    }

    /**
     *起点站点id
     *
     * Generated from protobuf field <code>int32 start_station_id = 2;</code>
     * @return int
     */
    public function getStartStationId()
    {
        return $this->start_station_id;
    }

    /**
     *起点站点id
     *
     * Generated from protobuf field <code>int32 start_station_id = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setStartStationId($var)
    {
        GPBUtil::checkInt32($var);
        $this->start_station_id = $var;

        return $this;
    }

    /**
     *终点站点id
     *
     * Generated from protobuf field <code>int32 end_station_id = 3;</code>
     * @return int
     */
    public function getEndStationId()
    {
        return $this->end_station_id;
    }

    /**
     *终点站点id
     *
     * Generated from protobuf field <code>int32 end_station_id = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setEndStationId($var)
    {
        GPBUtil::checkInt32($var);
        $this->end_station_id = $var;

        return $this;
    }

    /**
     *终点站点id
     *
     * Generated from protobuf field <code>int32 route_id = 4;</code>
     * @return int
     */
    public function getRouteId()
    {
        return $this->route_id;
    }

    /**
     *终点站点id
     *
     * Generated from protobuf field <code>int32 route_id = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setRouteId($var)
    {
        GPBUtil::checkInt32($var);
        $this->route_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 page_type = 5;</code>
     * @return int
     */
    public function getPageType()
    {
        return $this->page_type;
    }

    /**
     * Generated from protobuf field <code>int32 page_type = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setPageType($var)
    {
        GPBUtil::checkInt32($var);
        $this->page_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 distance_type = 6;</code>
     * @return int
     */
    public function getDistanceType()
    {
        return $this->distance_type;
    }

    /**
     * Generated from protobuf field <code>int32 distance_type = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setDistanceType($var)
    {
        GPBUtil::checkInt32($var);
        $this->distance_type = $var;

        return $this;
    }

}

