<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: carpool.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.StartAddressData</code>
 */
class StartAddressData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string from_poi_id = 1;</code>
     */
    protected $from_poi_id = '';
    /**
     * Generated from protobuf field <code>string from_area = 2;</code>
     */
    protected $from_area = '';
    /**
     * Generated from protobuf field <code>string from_lng = 3;</code>
     */
    protected $from_lng = '';
    /**
     * Generated from protobuf field <code>string from_lat = 4;</code>
     */
    protected $from_lat = '';
    /**
     * Generated from protobuf field <code>string from_name = 5;</code>
     */
    protected $from_name = '';
    /**
     * Generated from protobuf field <code>string from_address = 6;</code>
     */
    protected $from_address = '';
    /**
     * Generated from protobuf field <code>string from_area_name = 7;</code>
     */
    protected $from_area_name = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $from_poi_id
     *     @type string $from_area
     *     @type string $from_lng
     *     @type string $from_lat
     *     @type string $from_name
     *     @type string $from_address
     *     @type string $from_area_name
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\Carpool::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string from_poi_id = 1;</code>
     * @return string
     */
    public function getFromPoiId()
    {
        return $this->from_poi_id;
    }

    /**
     * Generated from protobuf field <code>string from_poi_id = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setFromPoiId($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_poi_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_area = 2;</code>
     * @return string
     */
    public function getFromArea()
    {
        return $this->from_area;
    }

    /**
     * Generated from protobuf field <code>string from_area = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setFromArea($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_area = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_lng = 3;</code>
     * @return string
     */
    public function getFromLng()
    {
        return $this->from_lng;
    }

    /**
     * Generated from protobuf field <code>string from_lng = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setFromLng($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_lng = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_lat = 4;</code>
     * @return string
     */
    public function getFromLat()
    {
        return $this->from_lat;
    }

    /**
     * Generated from protobuf field <code>string from_lat = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setFromLat($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_lat = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_name = 5;</code>
     * @return string
     */
    public function getFromName()
    {
        return $this->from_name;
    }

    /**
     * Generated from protobuf field <code>string from_name = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setFromName($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_address = 6;</code>
     * @return string
     */
    public function getFromAddress()
    {
        return $this->from_address;
    }

    /**
     * Generated from protobuf field <code>string from_address = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setFromAddress($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_address = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_area_name = 7;</code>
     * @return string
     */
    public function getFromAreaName()
    {
        return $this->from_area_name;
    }

    /**
     * Generated from protobuf field <code>string from_area_name = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setFromAreaName($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_area_name = $var;

        return $this;
    }

}

