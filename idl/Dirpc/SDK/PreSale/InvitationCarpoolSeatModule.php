<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: carpool.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.InvitationCarpoolSeatModule</code>
 */
class InvitationCarpoolSeatModule extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string title = 1;</code>
     */
    protected $title = null;
    /**
     * Generated from protobuf field <code>string seats_exceed_msg = 2;</code>
     */
    protected $seats_exceed_msg = null;
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SeatOption option_list = 3;</code>
     */
    private $option_list;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $title
     *     @type string $seats_exceed_msg
     *     @type \Dirpc\SDK\PreSale\SeatOption[]|\Nuwa\Protobuf\Internal\RepeatedField $option_list
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\Carpool::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @return string
     */
    public function getTitle()
    {
        return isset($this->title) ? $this->title : '';
    }

    public function hasTitle()
    {
        return isset($this->title);
    }

    public function clearTitle()
    {
        unset($this->title);
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string seats_exceed_msg = 2;</code>
     * @return string
     */
    public function getSeatsExceedMsg()
    {
        return isset($this->seats_exceed_msg) ? $this->seats_exceed_msg : '';
    }

    public function hasSeatsExceedMsg()
    {
        return isset($this->seats_exceed_msg);
    }

    public function clearSeatsExceedMsg()
    {
        unset($this->seats_exceed_msg);
    }

    /**
     * Generated from protobuf field <code>string seats_exceed_msg = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setSeatsExceedMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->seats_exceed_msg = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SeatOption option_list = 3;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getOptionList()
    {
        return $this->option_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SeatOption option_list = 3;</code>
     * @param \Dirpc\SDK\PreSale\SeatOption[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setOptionList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\SeatOption::class);
        $this->option_list = $arr;

        return $this;
    }

}

