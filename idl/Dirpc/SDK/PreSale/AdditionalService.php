<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.AdditionalService</code>
 */
class AdditionalService extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *服务id
     *
     * Generated from protobuf field <code>int32 id = 1;</code>
     */
    protected $id = 0;
    /**
     *需要携带宠物
     *
     * Generated from protobuf field <code>string text = 2;</code>
     */
    protected $text = '';
    /**
     *需要携带宠物
     *
     * Generated from protobuf field <code>string icon = 3;</code>
     */
    protected $icon = '';
    /**
     *是否勾选 0未勾选 1:已勾选
     *
     * Generated from protobuf field <code>int32 select = 4;</code>
     */
    protected $select = 0;
    /**
     *是否勾选 0未勾选 1:已勾选
     *
     * Generated from protobuf field <code>string detail = 5;</code>
     */
    protected $detail = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $id
     *          服务id
     *     @type string $text
     *          需要携带宠物
     *     @type string $icon
     *          需要携带宠物
     *     @type int $select
     *          是否勾选 0未勾选 1:已勾选
     *     @type string $detail
     *          是否勾选 0未勾选 1:已勾选
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *服务id
     *
     * Generated from protobuf field <code>int32 id = 1;</code>
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     *服务id
     *
     * Generated from protobuf field <code>int32 id = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setId($var)
    {
        GPBUtil::checkInt32($var);
        $this->id = $var;

        return $this;
    }

    /**
     *需要携带宠物
     *
     * Generated from protobuf field <code>string text = 2;</code>
     * @return string
     */
    public function getText()
    {
        return $this->text;
    }

    /**
     *需要携带宠物
     *
     * Generated from protobuf field <code>string text = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setText($var)
    {
        GPBUtil::checkString($var, True);
        $this->text = $var;

        return $this;
    }

    /**
     *需要携带宠物
     *
     * Generated from protobuf field <code>string icon = 3;</code>
     * @return string
     */
    public function getIcon()
    {
        return $this->icon;
    }

    /**
     *需要携带宠物
     *
     * Generated from protobuf field <code>string icon = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->icon = $var;

        return $this;
    }

    /**
     *是否勾选 0未勾选 1:已勾选
     *
     * Generated from protobuf field <code>int32 select = 4;</code>
     * @return int
     */
    public function getSelect()
    {
        return $this->select;
    }

    /**
     *是否勾选 0未勾选 1:已勾选
     *
     * Generated from protobuf field <code>int32 select = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setSelect($var)
    {
        GPBUtil::checkInt32($var);
        $this->select = $var;

        return $this;
    }

    /**
     *是否勾选 0未勾选 1:已勾选
     *
     * Generated from protobuf field <code>string detail = 5;</code>
     * @return string
     */
    public function getDetail()
    {
        return $this->detail;
    }

    /**
     *是否勾选 0未勾选 1:已勾选
     *
     * Generated from protobuf field <code>string detail = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setDetail($var)
    {
        GPBUtil::checkString($var, True);
        $this->detail = $var;

        return $this;
    }

}

