<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.BrandInfo</code>
 */
class BrandInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string title = 1;</code>
     */
    protected $title = null;
    /**
     * Generated from protobuf field <code>string sub_title = 2;</code>
     */
    protected $sub_title = null;
    /**
     * Generated from protobuf field <code>string bg_img = 3;</code>
     */
    protected $bg_img = null;
    /**
     * Generated from protobuf field <code>string start_color = 4;</code>
     */
    protected $start_color = null;
    /**
     * Generated from protobuf field <code>string end_color = 5;</code>
     */
    protected $end_color = null;
    /**
     * Generated from protobuf field <code>string font_color = 6;</code>
     */
    protected $font_color = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $title
     *     @type string $sub_title
     *     @type string $bg_img
     *     @type string $start_color
     *     @type string $end_color
     *     @type string $font_color
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @return string
     */
    public function getTitle()
    {
        return isset($this->title) ? $this->title : '';
    }

    public function hasTitle()
    {
        return isset($this->title);
    }

    public function clearTitle()
    {
        unset($this->title);
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string sub_title = 2;</code>
     * @return string
     */
    public function getSubTitle()
    {
        return isset($this->sub_title) ? $this->sub_title : '';
    }

    public function hasSubTitle()
    {
        return isset($this->sub_title);
    }

    public function clearSubTitle()
    {
        unset($this->sub_title);
    }

    /**
     * Generated from protobuf field <code>string sub_title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setSubTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string bg_img = 3;</code>
     * @return string
     */
    public function getBgImg()
    {
        return isset($this->bg_img) ? $this->bg_img : '';
    }

    public function hasBgImg()
    {
        return isset($this->bg_img);
    }

    public function clearBgImg()
    {
        unset($this->bg_img);
    }

    /**
     * Generated from protobuf field <code>string bg_img = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setBgImg($var)
    {
        GPBUtil::checkString($var, True);
        $this->bg_img = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string start_color = 4;</code>
     * @return string
     */
    public function getStartColor()
    {
        return isset($this->start_color) ? $this->start_color : '';
    }

    public function hasStartColor()
    {
        return isset($this->start_color);
    }

    public function clearStartColor()
    {
        unset($this->start_color);
    }

    /**
     * Generated from protobuf field <code>string start_color = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setStartColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->start_color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string end_color = 5;</code>
     * @return string
     */
    public function getEndColor()
    {
        return isset($this->end_color) ? $this->end_color : '';
    }

    public function hasEndColor()
    {
        return isset($this->end_color);
    }

    public function clearEndColor()
    {
        unset($this->end_color);
    }

    /**
     * Generated from protobuf field <code>string end_color = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setEndColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->end_color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string font_color = 6;</code>
     * @return string
     */
    public function getFontColor()
    {
        return isset($this->font_color) ? $this->font_color : '';
    }

    public function hasFontColor()
    {
        return isset($this->font_color);
    }

    public function clearFontColor()
    {
        unset($this->font_color);
    }

    /**
     * Generated from protobuf field <code>string font_color = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setFontColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->font_color = $var;

        return $this;
    }

}

