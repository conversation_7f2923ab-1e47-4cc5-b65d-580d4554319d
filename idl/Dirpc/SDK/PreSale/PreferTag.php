<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.PreferTag</code>
 */
class PreferTag extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *显示图标
     *
     * Generated from protobuf field <code>string display_icon = 1;</code>
     */
    protected $display_icon = '';
    /**
     *显示名称
     *
     * Generated from protobuf field <code>repeated string display_names = 2;</code>
     */
    private $display_names;
    /**
     *选择服务的数量，如果是0则不展示数量
     *
     * Generated from protobuf field <code>int32 display_count = 3;</code>
     */
    protected $display_count = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $display_icon
     *          显示图标
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $display_names
     *          显示名称
     *     @type int $display_count
     *          选择服务的数量，如果是0则不展示数量
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *显示图标
     *
     * Generated from protobuf field <code>string display_icon = 1;</code>
     * @return string
     */
    public function getDisplayIcon()
    {
        return $this->display_icon;
    }

    /**
     *显示图标
     *
     * Generated from protobuf field <code>string display_icon = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setDisplayIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->display_icon = $var;

        return $this;
    }

    /**
     *显示名称
     *
     * Generated from protobuf field <code>repeated string display_names = 2;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getDisplayNames()
    {
        return $this->display_names;
    }

    /**
     *显示名称
     *
     * Generated from protobuf field <code>repeated string display_names = 2;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setDisplayNames($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->display_names = $arr;

        return $this;
    }

    /**
     *选择服务的数量，如果是0则不展示数量
     *
     * Generated from protobuf field <code>int32 display_count = 3;</code>
     * @return int
     */
    public function getDisplayCount()
    {
        return $this->display_count;
    }

    /**
     *选择服务的数量，如果是0则不展示数量
     *
     * Generated from protobuf field <code>int32 display_count = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setDisplayCount($var)
    {
        GPBUtil::checkInt32($var);
        $this->display_count = $var;

        return $this;
    }

}

