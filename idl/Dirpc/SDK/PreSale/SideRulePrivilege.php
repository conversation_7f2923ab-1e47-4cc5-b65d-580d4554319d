<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideRulePrivilege</code>
 */
class SideRulePrivilege extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *权益包id
     *
     * Generated from protobuf field <code>int32 package_id = 1;</code>
     */
    protected $package_id = 0;
    /**
     *权益场景
     *
     * Generated from protobuf field <code>string privilege_source = 2;</code>
     */
    protected $privilege_source = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $package_id
     *          权益包id
     *     @type string $privilege_source
     *          权益场景
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     *权益包id
     *
     * Generated from protobuf field <code>int32 package_id = 1;</code>
     * @return int
     */
    public function getPackageId()
    {
        return $this->package_id;
    }

    /**
     *权益包id
     *
     * Generated from protobuf field <code>int32 package_id = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setPackageId($var)
    {
        GPBUtil::checkInt32($var);
        $this->package_id = $var;

        return $this;
    }

    /**
     *权益场景
     *
     * Generated from protobuf field <code>string privilege_source = 2;</code>
     * @return string
     */
    public function getPrivilegeSource()
    {
        return $this->privilege_source;
    }

    /**
     *权益场景
     *
     * Generated from protobuf field <code>string privilege_source = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setPrivilegeSource($var)
    {
        GPBUtil::checkString($var, True);
        $this->privilege_source = $var;

        return $this;
    }

}

