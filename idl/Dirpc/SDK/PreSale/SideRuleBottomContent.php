<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideRuleBottomContent</code>
 */
class SideRuleBottomContent extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *文案
     *
     * Generated from protobuf field <code>string text = 1;</code>
     */
    protected $text = '';
    /**
     *文案颜色
     *
     * Generated from protobuf field <code>string text_color = 2;</code>
     */
    protected $text_color = '';
    /**
     *背景色：无值背景透明，有1个纯色，多个渐变；端处理透明度，我们下发六位的色值 #ffffff
     *
     * Generated from protobuf field <code>repeated string background_gradients = 3;</code>
     */
    private $background_gradients;
    /**
     *交互样式；0代表无；1代表半弹层；
     *
     * Generated from protobuf field <code>int32 action_type = 4;</code>
     */
    protected $action_type = 0;
    /**
     *是否有箭头
     *
     * Generated from protobuf field <code>bool has_arrow = 5;</code>
     */
    protected $has_arrow = false;
    /**
     *是否强弹
     *
     * Generated from protobuf field <code>bool is_force_notice = 6;</code>
     */
    protected $is_force_notice = null;
    /**
     *text里的高亮颜色
     *
     * Generated from protobuf field <code>string text_highlight_color = 7;</code>
     */
    protected $text_highlight_color = null;
    /**
     *左侧图标
     *
     * Generated from protobuf field <code>string left_icon = 8;</code>
     */
    protected $left_icon = null;
    /**
     *跳转链接
     *
     * Generated from protobuf field <code>string link_url = 9;</code>
     */
    protected $link_url = '';
    /**
     *左侧图标
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ComboRecommend combo_recommend = 10;</code>
     */
    protected $combo_recommend = null;
    /**
     *渲染风格
     *
     * Generated from protobuf field <code>int32 style = 11;</code>
     */
    protected $style = 0;
    /**
     *请求link的参数
     *
     * Generated from protobuf field <code>map<string, string> link_params = 12;</code>
     */
    private $link_params;
    /**
     *右边的按钮
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleButton button = 13;</code>
     */
    protected $button = null;
    /**
     *请求link的方式：0:get 1:post
     *
     * Generated from protobuf field <code>int32 request_method = 14;</code>
     */
    protected $request_method = null;
    /**
     *请求link成功后的动作 1：请求预估 2：请求沟通接口
     *
     * Generated from protobuf field <code>int32 request_success_type = 15;</code>
     */
    protected $request_success_type = null;
    /**
     *是否因风控追加参数 0:不追加 1: 追加参数 "lat","lng", "ddfp"，
     *
     * Generated from protobuf field <code>int32 risk_control_judgment = 16;</code>
     */
    protected $risk_control_judgment = null;
    /**
     *0（默认）：端上现有逻辑 1：最多展示两行
     *
     * Generated from protobuf field <code>int32 multi_line_style_type = 17;</code>
     */
    protected $multi_line_style_type = null;
    /**
     *信息授权 type
     *
     * Generated from protobuf field <code>string user_information_authorization = 18;</code>
     */
    protected $user_information_authorization = null;
    /**
     *是否刷新预估
     *
     * Generated from protobuf field <code>bool refresh_estimate = 19;</code>
     */
    protected $refresh_estimate = null;
    /**
     *动效文案
     *
     * Generated from protobuf field <code>string animation_text = 20;</code>
     */
    protected $animation_text = null;
    /**
     *左侧信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleBottomContentLeftInfo left_info = 21;</code>
     */
    protected $left_info = null;
    /**
     *弹窗信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleBottomContentPopupInfo popup_info = 22;</code>
     */
    protected $popup_info = null;
    /**
     *需要端回传的参数
     *
     * Generated from protobuf field <code>map<string, string> callback_extra_infos = 23;</code>
     */
    private $callback_extra_infos;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $text
     *          文案
     *     @type string $text_color
     *          文案颜色
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $background_gradients
     *          背景色：无值背景透明，有1个纯色，多个渐变；端处理透明度，我们下发六位的色值 #ffffff
     *     @type int $action_type
     *          交互样式；0代表无；1代表半弹层；
     *     @type bool $has_arrow
     *          是否有箭头
     *     @type bool $is_force_notice
     *          是否强弹
     *     @type string $text_highlight_color
     *          text里的高亮颜色
     *     @type string $left_icon
     *          左侧图标
     *     @type string $link_url
     *          跳转链接
     *     @type \Dirpc\SDK\PreSale\ComboRecommend $combo_recommend
     *          左侧图标
     *     @type int $style
     *          渲染风格
     *     @type array|\Nuwa\Protobuf\Internal\MapField $link_params
     *          请求link的参数
     *     @type \Dirpc\SDK\PreSale\SideRuleButton $button
     *          右边的按钮
     *     @type int $request_method
     *          请求link的方式：0:get 1:post
     *     @type int $request_success_type
     *          请求link成功后的动作 1：请求预估 2：请求沟通接口
     *     @type int $risk_control_judgment
     *          是否因风控追加参数 0:不追加 1: 追加参数 "lat","lng", "ddfp"，
     *     @type int $multi_line_style_type
     *          0（默认）：端上现有逻辑 1：最多展示两行
     *     @type string $user_information_authorization
     *          信息授权 type
     *     @type bool $refresh_estimate
     *          是否刷新预估
     *     @type string $animation_text
     *          动效文案
     *     @type \Dirpc\SDK\PreSale\SideRuleBottomContentLeftInfo $left_info
     *          左侧信息
     *     @type \Dirpc\SDK\PreSale\SideRuleBottomContentPopupInfo $popup_info
     *          弹窗信息
     *     @type array|\Nuwa\Protobuf\Internal\MapField $callback_extra_infos
     *          需要端回传的参数
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     *文案
     *
     * Generated from protobuf field <code>string text = 1;</code>
     * @return string
     */
    public function getText()
    {
        return $this->text;
    }

    /**
     *文案
     *
     * Generated from protobuf field <code>string text = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setText($var)
    {
        GPBUtil::checkString($var, True);
        $this->text = $var;

        return $this;
    }

    /**
     *文案颜色
     *
     * Generated from protobuf field <code>string text_color = 2;</code>
     * @return string
     */
    public function getTextColor()
    {
        return $this->text_color;
    }

    /**
     *文案颜色
     *
     * Generated from protobuf field <code>string text_color = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTextColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->text_color = $var;

        return $this;
    }

    /**
     *背景色：无值背景透明，有1个纯色，多个渐变；端处理透明度，我们下发六位的色值 #ffffff
     *
     * Generated from protobuf field <code>repeated string background_gradients = 3;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getBackgroundGradients()
    {
        return $this->background_gradients;
    }

    /**
     *背景色：无值背景透明，有1个纯色，多个渐变；端处理透明度，我们下发六位的色值 #ffffff
     *
     * Generated from protobuf field <code>repeated string background_gradients = 3;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBackgroundGradients($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->background_gradients = $arr;

        return $this;
    }

    /**
     *交互样式；0代表无；1代表半弹层；
     *
     * Generated from protobuf field <code>int32 action_type = 4;</code>
     * @return int
     */
    public function getActionType()
    {
        return $this->action_type;
    }

    /**
     *交互样式；0代表无；1代表半弹层；
     *
     * Generated from protobuf field <code>int32 action_type = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setActionType($var)
    {
        GPBUtil::checkInt32($var);
        $this->action_type = $var;

        return $this;
    }

    /**
     *是否有箭头
     *
     * Generated from protobuf field <code>bool has_arrow = 5;</code>
     * @return bool
     */
    public function getHasArrow()
    {
        return $this->has_arrow;
    }

    /**
     *是否有箭头
     *
     * Generated from protobuf field <code>bool has_arrow = 5;</code>
     * @param bool $var
     * @return $this
     */
    public function setHasArrow($var)
    {
        GPBUtil::checkBool($var);
        $this->has_arrow = $var;

        return $this;
    }

    /**
     *是否强弹
     *
     * Generated from protobuf field <code>bool is_force_notice = 6;</code>
     * @return bool
     */
    public function getIsForceNotice()
    {
        return isset($this->is_force_notice) ? $this->is_force_notice : false;
    }

    public function hasIsForceNotice()
    {
        return isset($this->is_force_notice);
    }

    public function clearIsForceNotice()
    {
        unset($this->is_force_notice);
    }

    /**
     *是否强弹
     *
     * Generated from protobuf field <code>bool is_force_notice = 6;</code>
     * @param bool $var
     * @return $this
     */
    public function setIsForceNotice($var)
    {
        GPBUtil::checkBool($var);
        $this->is_force_notice = $var;

        return $this;
    }

    /**
     *text里的高亮颜色
     *
     * Generated from protobuf field <code>string text_highlight_color = 7;</code>
     * @return string
     */
    public function getTextHighlightColor()
    {
        return isset($this->text_highlight_color) ? $this->text_highlight_color : '';
    }

    public function hasTextHighlightColor()
    {
        return isset($this->text_highlight_color);
    }

    public function clearTextHighlightColor()
    {
        unset($this->text_highlight_color);
    }

    /**
     *text里的高亮颜色
     *
     * Generated from protobuf field <code>string text_highlight_color = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setTextHighlightColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->text_highlight_color = $var;

        return $this;
    }

    /**
     *左侧图标
     *
     * Generated from protobuf field <code>string left_icon = 8;</code>
     * @return string
     */
    public function getLeftIcon()
    {
        return isset($this->left_icon) ? $this->left_icon : '';
    }

    public function hasLeftIcon()
    {
        return isset($this->left_icon);
    }

    public function clearLeftIcon()
    {
        unset($this->left_icon);
    }

    /**
     *左侧图标
     *
     * Generated from protobuf field <code>string left_icon = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setLeftIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->left_icon = $var;

        return $this;
    }

    /**
     *跳转链接
     *
     * Generated from protobuf field <code>string link_url = 9;</code>
     * @return string
     */
    public function getLinkUrl()
    {
        return $this->link_url;
    }

    /**
     *跳转链接
     *
     * Generated from protobuf field <code>string link_url = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setLinkUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->link_url = $var;

        return $this;
    }

    /**
     *左侧图标
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ComboRecommend combo_recommend = 10;</code>
     * @return \Dirpc\SDK\PreSale\ComboRecommend
     */
    public function getComboRecommend()
    {
        return isset($this->combo_recommend) ? $this->combo_recommend : null;
    }

    public function hasComboRecommend()
    {
        return isset($this->combo_recommend);
    }

    public function clearComboRecommend()
    {
        unset($this->combo_recommend);
    }

    /**
     *左侧图标
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ComboRecommend combo_recommend = 10;</code>
     * @param \Dirpc\SDK\PreSale\ComboRecommend $var
     * @return $this
     */
    public function setComboRecommend($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\ComboRecommend::class);
        $this->combo_recommend = $var;

        return $this;
    }

    /**
     *渲染风格
     *
     * Generated from protobuf field <code>int32 style = 11;</code>
     * @return int
     */
    public function getStyle()
    {
        return $this->style;
    }

    /**
     *渲染风格
     *
     * Generated from protobuf field <code>int32 style = 11;</code>
     * @param int $var
     * @return $this
     */
    public function setStyle($var)
    {
        GPBUtil::checkInt32($var);
        $this->style = $var;

        return $this;
    }

    /**
     *请求link的参数
     *
     * Generated from protobuf field <code>map<string, string> link_params = 12;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getLinkParams()
    {
        return $this->link_params;
    }

    /**
     *请求link的参数
     *
     * Generated from protobuf field <code>map<string, string> link_params = 12;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setLinkParams($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->link_params = $arr;

        return $this;
    }

    /**
     *右边的按钮
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleButton button = 13;</code>
     * @return \Dirpc\SDK\PreSale\SideRuleButton
     */
    public function getButton()
    {
        return isset($this->button) ? $this->button : null;
    }

    public function hasButton()
    {
        return isset($this->button);
    }

    public function clearButton()
    {
        unset($this->button);
    }

    /**
     *右边的按钮
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleButton button = 13;</code>
     * @param \Dirpc\SDK\PreSale\SideRuleButton $var
     * @return $this
     */
    public function setButton($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\SideRuleButton::class);
        $this->button = $var;

        return $this;
    }

    /**
     *请求link的方式：0:get 1:post
     *
     * Generated from protobuf field <code>int32 request_method = 14;</code>
     * @return int
     */
    public function getRequestMethod()
    {
        return isset($this->request_method) ? $this->request_method : 0;
    }

    public function hasRequestMethod()
    {
        return isset($this->request_method);
    }

    public function clearRequestMethod()
    {
        unset($this->request_method);
    }

    /**
     *请求link的方式：0:get 1:post
     *
     * Generated from protobuf field <code>int32 request_method = 14;</code>
     * @param int $var
     * @return $this
     */
    public function setRequestMethod($var)
    {
        GPBUtil::checkInt32($var);
        $this->request_method = $var;

        return $this;
    }

    /**
     *请求link成功后的动作 1：请求预估 2：请求沟通接口
     *
     * Generated from protobuf field <code>int32 request_success_type = 15;</code>
     * @return int
     */
    public function getRequestSuccessType()
    {
        return isset($this->request_success_type) ? $this->request_success_type : 0;
    }

    public function hasRequestSuccessType()
    {
        return isset($this->request_success_type);
    }

    public function clearRequestSuccessType()
    {
        unset($this->request_success_type);
    }

    /**
     *请求link成功后的动作 1：请求预估 2：请求沟通接口
     *
     * Generated from protobuf field <code>int32 request_success_type = 15;</code>
     * @param int $var
     * @return $this
     */
    public function setRequestSuccessType($var)
    {
        GPBUtil::checkInt32($var);
        $this->request_success_type = $var;

        return $this;
    }

    /**
     *是否因风控追加参数 0:不追加 1: 追加参数 "lat","lng", "ddfp"，
     *
     * Generated from protobuf field <code>int32 risk_control_judgment = 16;</code>
     * @return int
     */
    public function getRiskControlJudgment()
    {
        return isset($this->risk_control_judgment) ? $this->risk_control_judgment : 0;
    }

    public function hasRiskControlJudgment()
    {
        return isset($this->risk_control_judgment);
    }

    public function clearRiskControlJudgment()
    {
        unset($this->risk_control_judgment);
    }

    /**
     *是否因风控追加参数 0:不追加 1: 追加参数 "lat","lng", "ddfp"，
     *
     * Generated from protobuf field <code>int32 risk_control_judgment = 16;</code>
     * @param int $var
     * @return $this
     */
    public function setRiskControlJudgment($var)
    {
        GPBUtil::checkInt32($var);
        $this->risk_control_judgment = $var;

        return $this;
    }

    /**
     *0（默认）：端上现有逻辑 1：最多展示两行
     *
     * Generated from protobuf field <code>int32 multi_line_style_type = 17;</code>
     * @return int
     */
    public function getMultiLineStyleType()
    {
        return isset($this->multi_line_style_type) ? $this->multi_line_style_type : 0;
    }

    public function hasMultiLineStyleType()
    {
        return isset($this->multi_line_style_type);
    }

    public function clearMultiLineStyleType()
    {
        unset($this->multi_line_style_type);
    }

    /**
     *0（默认）：端上现有逻辑 1：最多展示两行
     *
     * Generated from protobuf field <code>int32 multi_line_style_type = 17;</code>
     * @param int $var
     * @return $this
     */
    public function setMultiLineStyleType($var)
    {
        GPBUtil::checkInt32($var);
        $this->multi_line_style_type = $var;

        return $this;
    }

    /**
     *信息授权 type
     *
     * Generated from protobuf field <code>string user_information_authorization = 18;</code>
     * @return string
     */
    public function getUserInformationAuthorization()
    {
        return isset($this->user_information_authorization) ? $this->user_information_authorization : '';
    }

    public function hasUserInformationAuthorization()
    {
        return isset($this->user_information_authorization);
    }

    public function clearUserInformationAuthorization()
    {
        unset($this->user_information_authorization);
    }

    /**
     *信息授权 type
     *
     * Generated from protobuf field <code>string user_information_authorization = 18;</code>
     * @param string $var
     * @return $this
     */
    public function setUserInformationAuthorization($var)
    {
        GPBUtil::checkString($var, True);
        $this->user_information_authorization = $var;

        return $this;
    }

    /**
     *是否刷新预估
     *
     * Generated from protobuf field <code>bool refresh_estimate = 19;</code>
     * @return bool
     */
    public function getRefreshEstimate()
    {
        return isset($this->refresh_estimate) ? $this->refresh_estimate : false;
    }

    public function hasRefreshEstimate()
    {
        return isset($this->refresh_estimate);
    }

    public function clearRefreshEstimate()
    {
        unset($this->refresh_estimate);
    }

    /**
     *是否刷新预估
     *
     * Generated from protobuf field <code>bool refresh_estimate = 19;</code>
     * @param bool $var
     * @return $this
     */
    public function setRefreshEstimate($var)
    {
        GPBUtil::checkBool($var);
        $this->refresh_estimate = $var;

        return $this;
    }

    /**
     *动效文案
     *
     * Generated from protobuf field <code>string animation_text = 20;</code>
     * @return string
     */
    public function getAnimationText()
    {
        return isset($this->animation_text) ? $this->animation_text : '';
    }

    public function hasAnimationText()
    {
        return isset($this->animation_text);
    }

    public function clearAnimationText()
    {
        unset($this->animation_text);
    }

    /**
     *动效文案
     *
     * Generated from protobuf field <code>string animation_text = 20;</code>
     * @param string $var
     * @return $this
     */
    public function setAnimationText($var)
    {
        GPBUtil::checkString($var, True);
        $this->animation_text = $var;

        return $this;
    }

    /**
     *左侧信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleBottomContentLeftInfo left_info = 21;</code>
     * @return \Dirpc\SDK\PreSale\SideRuleBottomContentLeftInfo
     */
    public function getLeftInfo()
    {
        return isset($this->left_info) ? $this->left_info : null;
    }

    public function hasLeftInfo()
    {
        return isset($this->left_info);
    }

    public function clearLeftInfo()
    {
        unset($this->left_info);
    }

    /**
     *左侧信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleBottomContentLeftInfo left_info = 21;</code>
     * @param \Dirpc\SDK\PreSale\SideRuleBottomContentLeftInfo $var
     * @return $this
     */
    public function setLeftInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\SideRuleBottomContentLeftInfo::class);
        $this->left_info = $var;

        return $this;
    }

    /**
     *弹窗信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleBottomContentPopupInfo popup_info = 22;</code>
     * @return \Dirpc\SDK\PreSale\SideRuleBottomContentPopupInfo
     */
    public function getPopupInfo()
    {
        return isset($this->popup_info) ? $this->popup_info : null;
    }

    public function hasPopupInfo()
    {
        return isset($this->popup_info);
    }

    public function clearPopupInfo()
    {
        unset($this->popup_info);
    }

    /**
     *弹窗信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleBottomContentPopupInfo popup_info = 22;</code>
     * @param \Dirpc\SDK\PreSale\SideRuleBottomContentPopupInfo $var
     * @return $this
     */
    public function setPopupInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\SideRuleBottomContentPopupInfo::class);
        $this->popup_info = $var;

        return $this;
    }

    /**
     *需要端回传的参数
     *
     * Generated from protobuf field <code>map<string, string> callback_extra_infos = 23;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getCallbackExtraInfos()
    {
        return $this->callback_extra_infos;
    }

    /**
     *需要端回传的参数
     *
     * Generated from protobuf field <code>map<string, string> callback_extra_infos = 23;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setCallbackExtraInfos($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->callback_extra_infos = $arr;

        return $this;
    }

}

