<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.CapEstimateResponse</code>
 */
class CapEstimateResponse extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CapEstimateData estimate_data = 1;</code>
     */
    private $estimate_data;
    /**
     * Generated from protobuf field <code>string estimate_trace_id = 2;</code>
     */
    protected $estimate_trace_id = '';
    /**
     * Generated from protobuf field <code>string fee_detail_url = 3;</code>
     */
    protected $fee_detail_url = '';
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.Category category_info_list = 4;</code>
     */
    private $category_info_list;
    /**
     *是否支持多选，预约单时不支持 0:不支持；1:支持
     *
     * Generated from protobuf field <code>int32 is_support_multi_selection = 5;</code>
     */
    protected $is_support_multi_selection = 0;
    /**
     *座位前缀文案
     *
     * Generated from protobuf field <code>string seat_prefix_desc = 9;</code>
     */
    protected $seat_prefix_desc = null;
    /**
     *6.0支付方式并集
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.UserPayInfo user_pay_info = 10;</code>
     */
    protected $user_pay_info = null;
    /**
     *不拼座表单样式
     *
     * Generated from protobuf field <code>int32 form_style = 12;</code>
     */
    protected $form_style = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\CapEstimateData[]|\Nuwa\Protobuf\Internal\RepeatedField $estimate_data
     *     @type string $estimate_trace_id
     *     @type string $fee_detail_url
     *     @type \Dirpc\SDK\PreSale\Category[]|\Nuwa\Protobuf\Internal\RepeatedField $category_info_list
     *     @type int $is_support_multi_selection
     *          是否支持多选，预约单时不支持 0:不支持；1:支持
     *     @type string $seat_prefix_desc
     *          座位前缀文案
     *     @type \Dirpc\SDK\PreSale\UserPayInfo $user_pay_info
     *          6.0支付方式并集
     *     @type int $form_style
     *          不拼座表单样式
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CapEstimateData estimate_data = 1;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getEstimateData()
    {
        return $this->estimate_data;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CapEstimateData estimate_data = 1;</code>
     * @param \Dirpc\SDK\PreSale\CapEstimateData[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setEstimateData($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\CapEstimateData::class);
        $this->estimate_data = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string estimate_trace_id = 2;</code>
     * @return string
     */
    public function getEstimateTraceId()
    {
        return $this->estimate_trace_id;
    }

    /**
     * Generated from protobuf field <code>string estimate_trace_id = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateTraceId($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_trace_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_detail_url = 3;</code>
     * @return string
     */
    public function getFeeDetailUrl()
    {
        return $this->fee_detail_url;
    }

    /**
     * Generated from protobuf field <code>string fee_detail_url = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeDetailUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_detail_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.Category category_info_list = 4;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getCategoryInfoList()
    {
        return $this->category_info_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.Category category_info_list = 4;</code>
     * @param \Dirpc\SDK\PreSale\Category[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setCategoryInfoList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\Category::class);
        $this->category_info_list = $arr;

        return $this;
    }

    /**
     *是否支持多选，预约单时不支持 0:不支持；1:支持
     *
     * Generated from protobuf field <code>int32 is_support_multi_selection = 5;</code>
     * @return int
     */
    public function getIsSupportMultiSelection()
    {
        return $this->is_support_multi_selection;
    }

    /**
     *是否支持多选，预约单时不支持 0:不支持；1:支持
     *
     * Generated from protobuf field <code>int32 is_support_multi_selection = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setIsSupportMultiSelection($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_support_multi_selection = $var;

        return $this;
    }

    /**
     *座位前缀文案
     *
     * Generated from protobuf field <code>string seat_prefix_desc = 9;</code>
     * @return string
     */
    public function getSeatPrefixDesc()
    {
        return isset($this->seat_prefix_desc) ? $this->seat_prefix_desc : '';
    }

    public function hasSeatPrefixDesc()
    {
        return isset($this->seat_prefix_desc);
    }

    public function clearSeatPrefixDesc()
    {
        unset($this->seat_prefix_desc);
    }

    /**
     *座位前缀文案
     *
     * Generated from protobuf field <code>string seat_prefix_desc = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setSeatPrefixDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->seat_prefix_desc = $var;

        return $this;
    }

    /**
     *6.0支付方式并集
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.UserPayInfo user_pay_info = 10;</code>
     * @return \Dirpc\SDK\PreSale\UserPayInfo
     */
    public function getUserPayInfo()
    {
        return isset($this->user_pay_info) ? $this->user_pay_info : null;
    }

    public function hasUserPayInfo()
    {
        return isset($this->user_pay_info);
    }

    public function clearUserPayInfo()
    {
        unset($this->user_pay_info);
    }

    /**
     *6.0支付方式并集
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.UserPayInfo user_pay_info = 10;</code>
     * @param \Dirpc\SDK\PreSale\UserPayInfo $var
     * @return $this
     */
    public function setUserPayInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\UserPayInfo::class);
        $this->user_pay_info = $var;

        return $this;
    }

    /**
     *不拼座表单样式
     *
     * Generated from protobuf field <code>int32 form_style = 12;</code>
     * @return int
     */
    public function getFormStyle()
    {
        return $this->form_style;
    }

    /**
     *不拼座表单样式
     *
     * Generated from protobuf field <code>int32 form_style = 12;</code>
     * @param int $var
     * @return $this
     */
    public function setFormStyle($var)
    {
        GPBUtil::checkInt32($var);
        $this->form_style = $var;

        return $this;
    }

}

