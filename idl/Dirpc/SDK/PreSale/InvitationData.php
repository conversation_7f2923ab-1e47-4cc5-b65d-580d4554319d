<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.InvitationData</code>
 */
class InvitationData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *背景图片
     *
     * Generated from protobuf field <code>string bg_image = 1;</code>
     */
    protected $bg_image = null;
    /**
     *详情链接
     *
     * Generated from protobuf field <code>string detail_url = 2;</code>
     */
    protected $detail_url = null;
    /**
     *标题
     *
     * Generated from protobuf field <code>string title = 3;</code>
     */
    protected $title = null;
    /**
     *副标题
     *
     * Generated from protobuf field <code>string sub_title = 4;</code>
     */
    protected $sub_title = null;
    /**
     *出发时间文案
     *
     * Generated from protobuf field <code>string departure_text = 5;</code>
     */
    protected $departure_text = null;
    /**
     *出发地址
     *
     * Generated from protobuf field <code>string start_name = 6;</code>
     */
    protected $start_name = null;
    /**
     *出发区县
     *
     * Generated from protobuf field <code>string start_county = 7;</code>
     */
    protected $start_county = null;
    /**
     *目的地址
     *
     * Generated from protobuf field <code>string dest_name = 8;</code>
     */
    protected $dest_name = null;
    /**
     *目的区县
     *
     * Generated from protobuf field <code>string dest_county = 9;</code>
     */
    protected $dest_county = null;
    /**
     *价格
     *
     * Generated from protobuf field <code>string price = 10;</code>
     */
    protected $price = null;
    /**
     *划线价
     *
     * Generated from protobuf field <code>string origin_price = 11;</code>
     */
    protected $origin_price = null;
    /**
     *高亮色值
     *
     * Generated from protobuf field <code>string high_light_color = 12;</code>
     */
    protected $high_light_color = null;
    /**
     *状态
     *
     * Generated from protobuf field <code>string status = 13;</code>
     */
    protected $status = null;
    /**
     *角色 1邀约人 2受邀人
     *
     * Generated from protobuf field <code>string role = 14;</code>
     */
    protected $role = null;
    /**
     *按钮
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.InvitationButton button = 15;</code>
     */
    private $button;
    /**
     *标签
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationTag tag = 16;</code>
     */
    protected $tag = null;
    /**
     *已加入行程文案
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationJoinInfo joined_info = 17;</code>
     */
    protected $joined_info = null;
    /**
     *预估信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationEstimateInfo estimate_info = 18;</code>
     */
    protected $estimate_info = null;
    /**
     *额外信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationExtraInfo extra_info = 19;</code>
     */
    protected $extra_info = null;
    /**
     *页面title
     *
     * Generated from protobuf field <code>string page_title = 20;</code>
     */
    protected $page_title = null;
    /**
     *邀约类型 1 拼成乐 2 城际拼车
     *
     * Generated from protobuf field <code>int32 invitation_type = 21;</code>
     */
    protected $invitation_type = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $bg_image
     *          背景图片
     *     @type string $detail_url
     *          详情链接
     *     @type string $title
     *          标题
     *     @type string $sub_title
     *          副标题
     *     @type string $departure_text
     *          出发时间文案
     *     @type string $start_name
     *          出发地址
     *     @type string $start_county
     *          出发区县
     *     @type string $dest_name
     *          目的地址
     *     @type string $dest_county
     *          目的区县
     *     @type string $price
     *          价格
     *     @type string $origin_price
     *          划线价
     *     @type string $high_light_color
     *          高亮色值
     *     @type string $status
     *          状态
     *     @type string $role
     *          角色 1邀约人 2受邀人
     *     @type \Dirpc\SDK\PreSale\InvitationButton[]|\Nuwa\Protobuf\Internal\RepeatedField $button
     *          按钮
     *     @type \Dirpc\SDK\PreSale\InvitationTag $tag
     *          标签
     *     @type \Dirpc\SDK\PreSale\InvitationJoinInfo $joined_info
     *          已加入行程文案
     *     @type \Dirpc\SDK\PreSale\InvitationEstimateInfo $estimate_info
     *          预估信息
     *     @type \Dirpc\SDK\PreSale\InvitationExtraInfo $extra_info
     *          额外信息
     *     @type string $page_title
     *          页面title
     *     @type int $invitation_type
     *          邀约类型 1 拼成乐 2 城际拼车
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *背景图片
     *
     * Generated from protobuf field <code>string bg_image = 1;</code>
     * @return string
     */
    public function getBgImage()
    {
        return isset($this->bg_image) ? $this->bg_image : '';
    }

    public function hasBgImage()
    {
        return isset($this->bg_image);
    }

    public function clearBgImage()
    {
        unset($this->bg_image);
    }

    /**
     *背景图片
     *
     * Generated from protobuf field <code>string bg_image = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setBgImage($var)
    {
        GPBUtil::checkString($var, True);
        $this->bg_image = $var;

        return $this;
    }

    /**
     *详情链接
     *
     * Generated from protobuf field <code>string detail_url = 2;</code>
     * @return string
     */
    public function getDetailUrl()
    {
        return isset($this->detail_url) ? $this->detail_url : '';
    }

    public function hasDetailUrl()
    {
        return isset($this->detail_url);
    }

    public function clearDetailUrl()
    {
        unset($this->detail_url);
    }

    /**
     *详情链接
     *
     * Generated from protobuf field <code>string detail_url = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setDetailUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->detail_url = $var;

        return $this;
    }

    /**
     *标题
     *
     * Generated from protobuf field <code>string title = 3;</code>
     * @return string
     */
    public function getTitle()
    {
        return isset($this->title) ? $this->title : '';
    }

    public function hasTitle()
    {
        return isset($this->title);
    }

    public function clearTitle()
    {
        unset($this->title);
    }

    /**
     *标题
     *
     * Generated from protobuf field <code>string title = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     *副标题
     *
     * Generated from protobuf field <code>string sub_title = 4;</code>
     * @return string
     */
    public function getSubTitle()
    {
        return isset($this->sub_title) ? $this->sub_title : '';
    }

    public function hasSubTitle()
    {
        return isset($this->sub_title);
    }

    public function clearSubTitle()
    {
        unset($this->sub_title);
    }

    /**
     *副标题
     *
     * Generated from protobuf field <code>string sub_title = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setSubTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_title = $var;

        return $this;
    }

    /**
     *出发时间文案
     *
     * Generated from protobuf field <code>string departure_text = 5;</code>
     * @return string
     */
    public function getDepartureText()
    {
        return isset($this->departure_text) ? $this->departure_text : '';
    }

    public function hasDepartureText()
    {
        return isset($this->departure_text);
    }

    public function clearDepartureText()
    {
        unset($this->departure_text);
    }

    /**
     *出发时间文案
     *
     * Generated from protobuf field <code>string departure_text = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setDepartureText($var)
    {
        GPBUtil::checkString($var, True);
        $this->departure_text = $var;

        return $this;
    }

    /**
     *出发地址
     *
     * Generated from protobuf field <code>string start_name = 6;</code>
     * @return string
     */
    public function getStartName()
    {
        return isset($this->start_name) ? $this->start_name : '';
    }

    public function hasStartName()
    {
        return isset($this->start_name);
    }

    public function clearStartName()
    {
        unset($this->start_name);
    }

    /**
     *出发地址
     *
     * Generated from protobuf field <code>string start_name = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setStartName($var)
    {
        GPBUtil::checkString($var, True);
        $this->start_name = $var;

        return $this;
    }

    /**
     *出发区县
     *
     * Generated from protobuf field <code>string start_county = 7;</code>
     * @return string
     */
    public function getStartCounty()
    {
        return isset($this->start_county) ? $this->start_county : '';
    }

    public function hasStartCounty()
    {
        return isset($this->start_county);
    }

    public function clearStartCounty()
    {
        unset($this->start_county);
    }

    /**
     *出发区县
     *
     * Generated from protobuf field <code>string start_county = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setStartCounty($var)
    {
        GPBUtil::checkString($var, True);
        $this->start_county = $var;

        return $this;
    }

    /**
     *目的地址
     *
     * Generated from protobuf field <code>string dest_name = 8;</code>
     * @return string
     */
    public function getDestName()
    {
        return isset($this->dest_name) ? $this->dest_name : '';
    }

    public function hasDestName()
    {
        return isset($this->dest_name);
    }

    public function clearDestName()
    {
        unset($this->dest_name);
    }

    /**
     *目的地址
     *
     * Generated from protobuf field <code>string dest_name = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setDestName($var)
    {
        GPBUtil::checkString($var, True);
        $this->dest_name = $var;

        return $this;
    }

    /**
     *目的区县
     *
     * Generated from protobuf field <code>string dest_county = 9;</code>
     * @return string
     */
    public function getDestCounty()
    {
        return isset($this->dest_county) ? $this->dest_county : '';
    }

    public function hasDestCounty()
    {
        return isset($this->dest_county);
    }

    public function clearDestCounty()
    {
        unset($this->dest_county);
    }

    /**
     *目的区县
     *
     * Generated from protobuf field <code>string dest_county = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setDestCounty($var)
    {
        GPBUtil::checkString($var, True);
        $this->dest_county = $var;

        return $this;
    }

    /**
     *价格
     *
     * Generated from protobuf field <code>string price = 10;</code>
     * @return string
     */
    public function getPrice()
    {
        return isset($this->price) ? $this->price : '';
    }

    public function hasPrice()
    {
        return isset($this->price);
    }

    public function clearPrice()
    {
        unset($this->price);
    }

    /**
     *价格
     *
     * Generated from protobuf field <code>string price = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setPrice($var)
    {
        GPBUtil::checkString($var, True);
        $this->price = $var;

        return $this;
    }

    /**
     *划线价
     *
     * Generated from protobuf field <code>string origin_price = 11;</code>
     * @return string
     */
    public function getOriginPrice()
    {
        return isset($this->origin_price) ? $this->origin_price : '';
    }

    public function hasOriginPrice()
    {
        return isset($this->origin_price);
    }

    public function clearOriginPrice()
    {
        unset($this->origin_price);
    }

    /**
     *划线价
     *
     * Generated from protobuf field <code>string origin_price = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setOriginPrice($var)
    {
        GPBUtil::checkString($var, True);
        $this->origin_price = $var;

        return $this;
    }

    /**
     *高亮色值
     *
     * Generated from protobuf field <code>string high_light_color = 12;</code>
     * @return string
     */
    public function getHighLightColor()
    {
        return isset($this->high_light_color) ? $this->high_light_color : '';
    }

    public function hasHighLightColor()
    {
        return isset($this->high_light_color);
    }

    public function clearHighLightColor()
    {
        unset($this->high_light_color);
    }

    /**
     *高亮色值
     *
     * Generated from protobuf field <code>string high_light_color = 12;</code>
     * @param string $var
     * @return $this
     */
    public function setHighLightColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->high_light_color = $var;

        return $this;
    }

    /**
     *状态
     *
     * Generated from protobuf field <code>string status = 13;</code>
     * @return string
     */
    public function getStatus()
    {
        return isset($this->status) ? $this->status : '';
    }

    public function hasStatus()
    {
        return isset($this->status);
    }

    public function clearStatus()
    {
        unset($this->status);
    }

    /**
     *状态
     *
     * Generated from protobuf field <code>string status = 13;</code>
     * @param string $var
     * @return $this
     */
    public function setStatus($var)
    {
        GPBUtil::checkString($var, True);
        $this->status = $var;

        return $this;
    }

    /**
     *角色 1邀约人 2受邀人
     *
     * Generated from protobuf field <code>string role = 14;</code>
     * @return string
     */
    public function getRole()
    {
        return isset($this->role) ? $this->role : '';
    }

    public function hasRole()
    {
        return isset($this->role);
    }

    public function clearRole()
    {
        unset($this->role);
    }

    /**
     *角色 1邀约人 2受邀人
     *
     * Generated from protobuf field <code>string role = 14;</code>
     * @param string $var
     * @return $this
     */
    public function setRole($var)
    {
        GPBUtil::checkString($var, True);
        $this->role = $var;

        return $this;
    }

    /**
     *按钮
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.InvitationButton button = 15;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getButton()
    {
        return $this->button;
    }

    /**
     *按钮
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.InvitationButton button = 15;</code>
     * @param \Dirpc\SDK\PreSale\InvitationButton[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setButton($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\InvitationButton::class);
        $this->button = $arr;

        return $this;
    }

    /**
     *标签
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationTag tag = 16;</code>
     * @return \Dirpc\SDK\PreSale\InvitationTag
     */
    public function getTag()
    {
        return isset($this->tag) ? $this->tag : null;
    }

    public function hasTag()
    {
        return isset($this->tag);
    }

    public function clearTag()
    {
        unset($this->tag);
    }

    /**
     *标签
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationTag tag = 16;</code>
     * @param \Dirpc\SDK\PreSale\InvitationTag $var
     * @return $this
     */
    public function setTag($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\InvitationTag::class);
        $this->tag = $var;

        return $this;
    }

    /**
     *已加入行程文案
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationJoinInfo joined_info = 17;</code>
     * @return \Dirpc\SDK\PreSale\InvitationJoinInfo
     */
    public function getJoinedInfo()
    {
        return isset($this->joined_info) ? $this->joined_info : null;
    }

    public function hasJoinedInfo()
    {
        return isset($this->joined_info);
    }

    public function clearJoinedInfo()
    {
        unset($this->joined_info);
    }

    /**
     *已加入行程文案
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationJoinInfo joined_info = 17;</code>
     * @param \Dirpc\SDK\PreSale\InvitationJoinInfo $var
     * @return $this
     */
    public function setJoinedInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\InvitationJoinInfo::class);
        $this->joined_info = $var;

        return $this;
    }

    /**
     *预估信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationEstimateInfo estimate_info = 18;</code>
     * @return \Dirpc\SDK\PreSale\InvitationEstimateInfo
     */
    public function getEstimateInfo()
    {
        return isset($this->estimate_info) ? $this->estimate_info : null;
    }

    public function hasEstimateInfo()
    {
        return isset($this->estimate_info);
    }

    public function clearEstimateInfo()
    {
        unset($this->estimate_info);
    }

    /**
     *预估信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationEstimateInfo estimate_info = 18;</code>
     * @param \Dirpc\SDK\PreSale\InvitationEstimateInfo $var
     * @return $this
     */
    public function setEstimateInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\InvitationEstimateInfo::class);
        $this->estimate_info = $var;

        return $this;
    }

    /**
     *额外信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationExtraInfo extra_info = 19;</code>
     * @return \Dirpc\SDK\PreSale\InvitationExtraInfo
     */
    public function getExtraInfo()
    {
        return isset($this->extra_info) ? $this->extra_info : null;
    }

    public function hasExtraInfo()
    {
        return isset($this->extra_info);
    }

    public function clearExtraInfo()
    {
        unset($this->extra_info);
    }

    /**
     *额外信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationExtraInfo extra_info = 19;</code>
     * @param \Dirpc\SDK\PreSale\InvitationExtraInfo $var
     * @return $this
     */
    public function setExtraInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\InvitationExtraInfo::class);
        $this->extra_info = $var;

        return $this;
    }

    /**
     *页面title
     *
     * Generated from protobuf field <code>string page_title = 20;</code>
     * @return string
     */
    public function getPageTitle()
    {
        return isset($this->page_title) ? $this->page_title : '';
    }

    public function hasPageTitle()
    {
        return isset($this->page_title);
    }

    public function clearPageTitle()
    {
        unset($this->page_title);
    }

    /**
     *页面title
     *
     * Generated from protobuf field <code>string page_title = 20;</code>
     * @param string $var
     * @return $this
     */
    public function setPageTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->page_title = $var;

        return $this;
    }

    /**
     *邀约类型 1 拼成乐 2 城际拼车
     *
     * Generated from protobuf field <code>int32 invitation_type = 21;</code>
     * @return int
     */
    public function getInvitationType()
    {
        return isset($this->invitation_type) ? $this->invitation_type : 0;
    }

    public function hasInvitationType()
    {
        return isset($this->invitation_type);
    }

    public function clearInvitationType()
    {
        unset($this->invitation_type);
    }

    /**
     *邀约类型 1 拼成乐 2 城际拼车
     *
     * Generated from protobuf field <code>int32 invitation_type = 21;</code>
     * @param int $var
     * @return $this
     */
    public function setInvitationType($var)
    {
        GPBUtil::checkInt32($var);
        $this->invitation_type = $var;

        return $this;
    }

}

