<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.IntercityCarpoolSeatModule</code>
 */
class IntercityCarpoolSeatModule extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.IntercitySeatConfig seat_config = 1;</code>
     */
    private $seat_config;
    /**
     * Generated from protobuf field <code>int32 select_value = 2;</code>
     */
    protected $select_value = 0;
    /**
     * Generated from protobuf field <code>int32 select_index = 3;</code>
     */
    protected $select_index = 0;
    /**
     *座位数超选提示
     *
     * Generated from protobuf field <code>string seats_exceed_toast = 4;</code>
     */
    protected $seats_exceed_toast = '';
    /**
     *座位数超选提示
     *
     * Generated from protobuf field <code>string button_desc = 5;</code>
     */
    protected $button_desc = '';
    /**
     *儿童票人数
     *
     * Generated from protobuf field <code>int32 select_count_child_ticket = 6;</code>
     */
    protected $select_count_child_ticket = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\IntercitySeatConfig[]|\Nuwa\Protobuf\Internal\RepeatedField $seat_config
     *     @type int $select_value
     *     @type int $select_index
     *     @type string $seats_exceed_toast
     *          座位数超选提示
     *     @type string $button_desc
     *          座位数超选提示
     *     @type int $select_count_child_ticket
     *          儿童票人数
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.IntercitySeatConfig seat_config = 1;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getSeatConfig()
    {
        return $this->seat_config;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.IntercitySeatConfig seat_config = 1;</code>
     * @param \Dirpc\SDK\PreSale\IntercitySeatConfig[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSeatConfig($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\IntercitySeatConfig::class);
        $this->seat_config = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 select_value = 2;</code>
     * @return int
     */
    public function getSelectValue()
    {
        return $this->select_value;
    }

    /**
     * Generated from protobuf field <code>int32 select_value = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setSelectValue($var)
    {
        GPBUtil::checkInt32($var);
        $this->select_value = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 select_index = 3;</code>
     * @return int
     */
    public function getSelectIndex()
    {
        return $this->select_index;
    }

    /**
     * Generated from protobuf field <code>int32 select_index = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setSelectIndex($var)
    {
        GPBUtil::checkInt32($var);
        $this->select_index = $var;

        return $this;
    }

    /**
     *座位数超选提示
     *
     * Generated from protobuf field <code>string seats_exceed_toast = 4;</code>
     * @return string
     */
    public function getSeatsExceedToast()
    {
        return $this->seats_exceed_toast;
    }

    /**
     *座位数超选提示
     *
     * Generated from protobuf field <code>string seats_exceed_toast = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setSeatsExceedToast($var)
    {
        GPBUtil::checkString($var, True);
        $this->seats_exceed_toast = $var;

        return $this;
    }

    /**
     *座位数超选提示
     *
     * Generated from protobuf field <code>string button_desc = 5;</code>
     * @return string
     */
    public function getButtonDesc()
    {
        return $this->button_desc;
    }

    /**
     *座位数超选提示
     *
     * Generated from protobuf field <code>string button_desc = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setButtonDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->button_desc = $var;

        return $this;
    }

    /**
     *儿童票人数
     *
     * Generated from protobuf field <code>int32 select_count_child_ticket = 6;</code>
     * @return int
     */
    public function getSelectCountChildTicket()
    {
        return $this->select_count_child_ticket;
    }

    /**
     *儿童票人数
     *
     * Generated from protobuf field <code>int32 select_count_child_ticket = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setSelectCountChildTicket($var)
    {
        GPBUtil::checkInt32($var);
        $this->select_count_child_ticket = $var;

        return $this;
    }

}

