<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.InvitationEstimateInfo</code>
 */
class InvitationEstimateInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string order_type = 1;</code>
     */
    protected $order_type = null;
    /**
     * Generated from protobuf field <code>string departure_range = 2;</code>
     */
    protected $departure_range = null;
    /**
     * Generated from protobuf field <code>string menu_id = 3;</code>
     */
    protected $menu_id = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.AddrPageInfo addr_page_info = 4;</code>
     */
    protected $addr_page_info = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $order_type
     *     @type string $departure_range
     *     @type string $menu_id
     *     @type \Dirpc\SDK\PreSale\AddrPageInfo $addr_page_info
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string order_type = 1;</code>
     * @return string
     */
    public function getOrderType()
    {
        return isset($this->order_type) ? $this->order_type : '';
    }

    public function hasOrderType()
    {
        return isset($this->order_type);
    }

    public function clearOrderType()
    {
        unset($this->order_type);
    }

    /**
     * Generated from protobuf field <code>string order_type = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setOrderType($var)
    {
        GPBUtil::checkString($var, True);
        $this->order_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string departure_range = 2;</code>
     * @return string
     */
    public function getDepartureRange()
    {
        return isset($this->departure_range) ? $this->departure_range : '';
    }

    public function hasDepartureRange()
    {
        return isset($this->departure_range);
    }

    public function clearDepartureRange()
    {
        unset($this->departure_range);
    }

    /**
     * Generated from protobuf field <code>string departure_range = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setDepartureRange($var)
    {
        GPBUtil::checkString($var, True);
        $this->departure_range = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string menu_id = 3;</code>
     * @return string
     */
    public function getMenuId()
    {
        return isset($this->menu_id) ? $this->menu_id : '';
    }

    public function hasMenuId()
    {
        return isset($this->menu_id);
    }

    public function clearMenuId()
    {
        unset($this->menu_id);
    }

    /**
     * Generated from protobuf field <code>string menu_id = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setMenuId($var)
    {
        GPBUtil::checkString($var, True);
        $this->menu_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.AddrPageInfo addr_page_info = 4;</code>
     * @return \Dirpc\SDK\PreSale\AddrPageInfo
     */
    public function getAddrPageInfo()
    {
        return isset($this->addr_page_info) ? $this->addr_page_info : null;
    }

    public function hasAddrPageInfo()
    {
        return isset($this->addr_page_info);
    }

    public function clearAddrPageInfo()
    {
        unset($this->addr_page_info);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.AddrPageInfo addr_page_info = 4;</code>
     * @param \Dirpc\SDK\PreSale\AddrPageInfo $var
     * @return $this
     */
    public function setAddrPageInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\AddrPageInfo::class);
        $this->addr_page_info = $var;

        return $this;
    }

}

