<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.EjectLayerInfo</code>
 */
class EjectLayerInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.HeadInfo head_info = 1;</code>
     */
    protected $head_info = null;
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SwitchItem switch_list = 2;</code>
     */
    private $switch_list;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.CloseButton close_button = 3;</code>
     */
    protected $close_button = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ConfirmButton confirm_button = 4;</code>
     */
    protected $confirm_button = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\HeadInfo $head_info
     *     @type \Dirpc\SDK\PreSale\SwitchItem[]|\Nuwa\Protobuf\Internal\RepeatedField $switch_list
     *     @type \Dirpc\SDK\PreSale\CloseButton $close_button
     *     @type \Dirpc\SDK\PreSale\ConfirmButton $confirm_button
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.HeadInfo head_info = 1;</code>
     * @return \Dirpc\SDK\PreSale\HeadInfo
     */
    public function getHeadInfo()
    {
        return isset($this->head_info) ? $this->head_info : null;
    }

    public function hasHeadInfo()
    {
        return isset($this->head_info);
    }

    public function clearHeadInfo()
    {
        unset($this->head_info);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.HeadInfo head_info = 1;</code>
     * @param \Dirpc\SDK\PreSale\HeadInfo $var
     * @return $this
     */
    public function setHeadInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\HeadInfo::class);
        $this->head_info = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SwitchItem switch_list = 2;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getSwitchList()
    {
        return $this->switch_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SwitchItem switch_list = 2;</code>
     * @param \Dirpc\SDK\PreSale\SwitchItem[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSwitchList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\SwitchItem::class);
        $this->switch_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.CloseButton close_button = 3;</code>
     * @return \Dirpc\SDK\PreSale\CloseButton
     */
    public function getCloseButton()
    {
        return isset($this->close_button) ? $this->close_button : null;
    }

    public function hasCloseButton()
    {
        return isset($this->close_button);
    }

    public function clearCloseButton()
    {
        unset($this->close_button);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.CloseButton close_button = 3;</code>
     * @param \Dirpc\SDK\PreSale\CloseButton $var
     * @return $this
     */
    public function setCloseButton($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\CloseButton::class);
        $this->close_button = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ConfirmButton confirm_button = 4;</code>
     * @return \Dirpc\SDK\PreSale\ConfirmButton
     */
    public function getConfirmButton()
    {
        return isset($this->confirm_button) ? $this->confirm_button : null;
    }

    public function hasConfirmButton()
    {
        return isset($this->confirm_button);
    }

    public function clearConfirmButton()
    {
        unset($this->confirm_button);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ConfirmButton confirm_button = 4;</code>
     * @param \Dirpc\SDK\PreSale\ConfirmButton $var
     * @return $this
     */
    public function setConfirmButton($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\ConfirmButton::class);
        $this->confirm_button = $var;

        return $this;
    }

}

