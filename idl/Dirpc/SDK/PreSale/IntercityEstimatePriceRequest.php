<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.IntercityEstimatePriceRequest</code>
 */
class IntercityEstimatePriceRequest extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *用户相关 *
     *
     * Generated from protobuf field <code>string token = 1;</code>
     */
    protected $token = '';
    /**
     *用户类型，0表示普通用户，2表示企业用户
     *
     * Generated from protobuf field <code>int32 user_type = 2;</code>
     */
    protected $user_type = 0;
    /**
     *用户类型，0表示普通用户，2表示企业用户
     *
     * Generated from protobuf field <code>string openid = 3;</code>
     */
    protected $openid = '';
    /**
     *端相关参数 *
     *
     * Generated from protobuf field <code>string app_version = 4;</code>
     */
    protected $app_version = '';
    /**
     * Generated from protobuf field <code>int32 access_key_id = 5;</code>
     */
    protected $access_key_id = 0;
    /**
     * Generated from protobuf field <code>int32 channel = 6;</code>
     */
    protected $channel = 0;
    /**
     * Generated from protobuf field <code>int32 client_type = 7;</code>
     */
    protected $client_type = 0;
    /**
     * Generated from protobuf field <code>string lang = 8;</code>
     */
    protected $lang = '';
    /**
     * Generated from protobuf field <code>string maptype = 9;</code>
     */
    protected $maptype = '';
    /**
     * Generated from protobuf field <code>string imei = 10;</code>
     */
    protected $imei = '';
    /**
     * Generated from protobuf field <code>string suuid = 11;</code>
     */
    protected $suuid = '';
    /**
     * Generated from protobuf field <code>string terminal_id = 12;</code>
     */
    protected $terminal_id = '';
    /**
     * Generated from protobuf field <code>int32 origin_id = 13;</code>
     */
    protected $origin_id = 0;
    /**
     * Generated from protobuf field <code>int32 platform_type = 14;</code>
     */
    protected $platform_type = 0;
    /**
     * Generated from protobuf field <code>string from = 15;</code>
     */
    protected $from = '';
    /**
     * Generated from protobuf field <code>string pixels = 16;</code>
     */
    protected $pixels = '';
    /**
     * Generated from protobuf field <code>int32 datatype = 17;</code>
     */
    protected $datatype = 0;
    /**
     *起终点相关信息*
     *
     * Generated from protobuf field <code>double lat = 30;</code>
     */
    protected $lat = 0.0;
    /**
     * Generated from protobuf field <code>double lng = 31;</code>
     */
    protected $lng = 0.0;
    /**
     * Generated from protobuf field <code>double from_lat = 32;</code>
     */
    protected $from_lat = 0.0;
    /**
     * Generated from protobuf field <code>double from_lng = 33;</code>
     */
    protected $from_lng = 0.0;
    /**
     * Generated from protobuf field <code>string from_poi_id = 34;</code>
     */
    protected $from_poi_id = '';
    /**
     * Generated from protobuf field <code>string from_poi_type = 35;</code>
     */
    protected $from_poi_type = '';
    /**
     * Generated from protobuf field <code>string from_address = 36;</code>
     */
    protected $from_address = '';
    /**
     * Generated from protobuf field <code>string from_name = 37;</code>
     */
    protected $from_name = '';
    /**
     * Generated from protobuf field <code>double to_lat = 38;</code>
     */
    protected $to_lat = 0.0;
    /**
     * Generated from protobuf field <code>double to_lng = 39;</code>
     */
    protected $to_lng = 0.0;
    /**
     * Generated from protobuf field <code>string to_poi_id = 40;</code>
     */
    protected $to_poi_id = '';
    /**
     * Generated from protobuf field <code>string to_poi_type = 41;</code>
     */
    protected $to_poi_type = '';
    /**
     * Generated from protobuf field <code>string to_address = 42;</code>
     */
    protected $to_address = '';
    /**
     * Generated from protobuf field <code>string to_name = 43;</code>
     */
    protected $to_name = '';
    /**
     *订单属性等信息
     *
     * Generated from protobuf field <code>string menu_id = 50;</code>
     */
    protected $menu_id = '';
    /**
     *用户勾选项
     *
     * Generated from protobuf field <code>string multi_require_product = 51;</code>
     */
    protected $multi_require_product = '';
    /**
     *用户选择的支付方式
     *
     * Generated from protobuf field <code>int32 payments_type = 52;</code>
     */
    protected $payments_type = 0;
    /**
     *订单类型
     *
     * Generated from protobuf field <code>int32 order_type = 53;</code>
     */
    protected $order_type = 0;
    /**
     *页面类型
     *
     * Generated from protobuf field <code>int32 page_type = 54;</code>
     */
    protected $page_type = 0;
    /**
     *城际拼车订单出发时间
     *
     * Generated from protobuf field <code>string departure_range = 55;</code>
     */
    protected $departure_range = null;
    /**
     *是否为客企扫二维码上车预估
     *
     * Generated from protobuf field <code>string agent_type = 56;</code>
     */
    protected $agent_type = null;
    /**
     * Generated from protobuf field <code>string seat_detail_info = 57;</code>
     */
    protected $seat_detail_info = null;
    /**
     * Generated from protobuf field <code>int64 route_id = 58;</code>
     */
    protected $route_id = null;
    /**
     *webx公参
     *
     * Generated from protobuf field <code>string xpsid = 191;</code>
     */
    protected $xpsid = null;
    /**
     * Generated from protobuf field <code>string xpsid_root = 192;</code>
     */
    protected $xpsid_root = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $token
     *          用户相关 *
     *     @type int $user_type
     *          用户类型，0表示普通用户，2表示企业用户
     *     @type string $openid
     *          用户类型，0表示普通用户，2表示企业用户
     *     @type string $app_version
     *          端相关参数 *
     *     @type int $access_key_id
     *     @type int $channel
     *     @type int $client_type
     *     @type string $lang
     *     @type string $maptype
     *     @type string $imei
     *     @type string $suuid
     *     @type string $terminal_id
     *     @type int $origin_id
     *     @type int $platform_type
     *     @type string $from
     *     @type string $pixels
     *     @type int $datatype
     *     @type float $lat
     *          起终点相关信息*
     *     @type float $lng
     *     @type float $from_lat
     *     @type float $from_lng
     *     @type string $from_poi_id
     *     @type string $from_poi_type
     *     @type string $from_address
     *     @type string $from_name
     *     @type float $to_lat
     *     @type float $to_lng
     *     @type string $to_poi_id
     *     @type string $to_poi_type
     *     @type string $to_address
     *     @type string $to_name
     *     @type string $menu_id
     *          订单属性等信息
     *     @type string $multi_require_product
     *          用户勾选项
     *     @type int $payments_type
     *          用户选择的支付方式
     *     @type int $order_type
     *          订单类型
     *     @type int $page_type
     *          页面类型
     *     @type string $departure_range
     *          城际拼车订单出发时间
     *     @type string $agent_type
     *          是否为客企扫二维码上车预估
     *     @type string $seat_detail_info
     *     @type int|string $route_id
     *     @type string $xpsid
     *          webx公参
     *     @type string $xpsid_root
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *用户相关 *
     *
     * Generated from protobuf field <code>string token = 1;</code>
     * @return string
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     *用户相关 *
     *
     * Generated from protobuf field <code>string token = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->token = $var;

        return $this;
    }

    /**
     *用户类型，0表示普通用户，2表示企业用户
     *
     * Generated from protobuf field <code>int32 user_type = 2;</code>
     * @return int
     */
    public function getUserType()
    {
        return $this->user_type;
    }

    /**
     *用户类型，0表示普通用户，2表示企业用户
     *
     * Generated from protobuf field <code>int32 user_type = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setUserType($var)
    {
        GPBUtil::checkInt32($var);
        $this->user_type = $var;

        return $this;
    }

    /**
     *用户类型，0表示普通用户，2表示企业用户
     *
     * Generated from protobuf field <code>string openid = 3;</code>
     * @return string
     */
    public function getOpenid()
    {
        return $this->openid;
    }

    /**
     *用户类型，0表示普通用户，2表示企业用户
     *
     * Generated from protobuf field <code>string openid = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setOpenid($var)
    {
        GPBUtil::checkString($var, True);
        $this->openid = $var;

        return $this;
    }

    /**
     *端相关参数 *
     *
     * Generated from protobuf field <code>string app_version = 4;</code>
     * @return string
     */
    public function getAppVersion()
    {
        return $this->app_version;
    }

    /**
     *端相关参数 *
     *
     * Generated from protobuf field <code>string app_version = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setAppVersion($var)
    {
        GPBUtil::checkString($var, True);
        $this->app_version = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 access_key_id = 5;</code>
     * @return int
     */
    public function getAccessKeyId()
    {
        return $this->access_key_id;
    }

    /**
     * Generated from protobuf field <code>int32 access_key_id = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setAccessKeyId($var)
    {
        GPBUtil::checkInt32($var);
        $this->access_key_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 channel = 6;</code>
     * @return int
     */
    public function getChannel()
    {
        return $this->channel;
    }

    /**
     * Generated from protobuf field <code>int32 channel = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setChannel($var)
    {
        GPBUtil::checkInt32($var);
        $this->channel = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 client_type = 7;</code>
     * @return int
     */
    public function getClientType()
    {
        return $this->client_type;
    }

    /**
     * Generated from protobuf field <code>int32 client_type = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setClientType($var)
    {
        GPBUtil::checkInt32($var);
        $this->client_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string lang = 8;</code>
     * @return string
     */
    public function getLang()
    {
        return $this->lang;
    }

    /**
     * Generated from protobuf field <code>string lang = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setLang($var)
    {
        GPBUtil::checkString($var, True);
        $this->lang = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string maptype = 9;</code>
     * @return string
     */
    public function getMaptype()
    {
        return $this->maptype;
    }

    /**
     * Generated from protobuf field <code>string maptype = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setMaptype($var)
    {
        GPBUtil::checkString($var, True);
        $this->maptype = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string imei = 10;</code>
     * @return string
     */
    public function getImei()
    {
        return $this->imei;
    }

    /**
     * Generated from protobuf field <code>string imei = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setImei($var)
    {
        GPBUtil::checkString($var, True);
        $this->imei = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string suuid = 11;</code>
     * @return string
     */
    public function getSuuid()
    {
        return $this->suuid;
    }

    /**
     * Generated from protobuf field <code>string suuid = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setSuuid($var)
    {
        GPBUtil::checkString($var, True);
        $this->suuid = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string terminal_id = 12;</code>
     * @return string
     */
    public function getTerminalId()
    {
        return $this->terminal_id;
    }

    /**
     * Generated from protobuf field <code>string terminal_id = 12;</code>
     * @param string $var
     * @return $this
     */
    public function setTerminalId($var)
    {
        GPBUtil::checkString($var, True);
        $this->terminal_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 origin_id = 13;</code>
     * @return int
     */
    public function getOriginId()
    {
        return $this->origin_id;
    }

    /**
     * Generated from protobuf field <code>int32 origin_id = 13;</code>
     * @param int $var
     * @return $this
     */
    public function setOriginId($var)
    {
        GPBUtil::checkInt32($var);
        $this->origin_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 platform_type = 14;</code>
     * @return int
     */
    public function getPlatformType()
    {
        return $this->platform_type;
    }

    /**
     * Generated from protobuf field <code>int32 platform_type = 14;</code>
     * @param int $var
     * @return $this
     */
    public function setPlatformType($var)
    {
        GPBUtil::checkInt32($var);
        $this->platform_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from = 15;</code>
     * @return string
     */
    public function getFrom()
    {
        return $this->from;
    }

    /**
     * Generated from protobuf field <code>string from = 15;</code>
     * @param string $var
     * @return $this
     */
    public function setFrom($var)
    {
        GPBUtil::checkString($var, True);
        $this->from = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string pixels = 16;</code>
     * @return string
     */
    public function getPixels()
    {
        return $this->pixels;
    }

    /**
     * Generated from protobuf field <code>string pixels = 16;</code>
     * @param string $var
     * @return $this
     */
    public function setPixels($var)
    {
        GPBUtil::checkString($var, True);
        $this->pixels = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 datatype = 17;</code>
     * @return int
     */
    public function getDatatype()
    {
        return $this->datatype;
    }

    /**
     * Generated from protobuf field <code>int32 datatype = 17;</code>
     * @param int $var
     * @return $this
     */
    public function setDatatype($var)
    {
        GPBUtil::checkInt32($var);
        $this->datatype = $var;

        return $this;
    }

    /**
     *起终点相关信息*
     *
     * Generated from protobuf field <code>double lat = 30;</code>
     * @return float
     */
    public function getLat()
    {
        return $this->lat;
    }

    /**
     *起终点相关信息*
     *
     * Generated from protobuf field <code>double lat = 30;</code>
     * @param float $var
     * @return $this
     */
    public function setLat($var)
    {
        GPBUtil::checkDouble($var);
        $this->lat = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double lng = 31;</code>
     * @return float
     */
    public function getLng()
    {
        return $this->lng;
    }

    /**
     * Generated from protobuf field <code>double lng = 31;</code>
     * @param float $var
     * @return $this
     */
    public function setLng($var)
    {
        GPBUtil::checkDouble($var);
        $this->lng = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double from_lat = 32;</code>
     * @return float
     */
    public function getFromLat()
    {
        return $this->from_lat;
    }

    /**
     * Generated from protobuf field <code>double from_lat = 32;</code>
     * @param float $var
     * @return $this
     */
    public function setFromLat($var)
    {
        GPBUtil::checkDouble($var);
        $this->from_lat = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double from_lng = 33;</code>
     * @return float
     */
    public function getFromLng()
    {
        return $this->from_lng;
    }

    /**
     * Generated from protobuf field <code>double from_lng = 33;</code>
     * @param float $var
     * @return $this
     */
    public function setFromLng($var)
    {
        GPBUtil::checkDouble($var);
        $this->from_lng = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_poi_id = 34;</code>
     * @return string
     */
    public function getFromPoiId()
    {
        return $this->from_poi_id;
    }

    /**
     * Generated from protobuf field <code>string from_poi_id = 34;</code>
     * @param string $var
     * @return $this
     */
    public function setFromPoiId($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_poi_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_poi_type = 35;</code>
     * @return string
     */
    public function getFromPoiType()
    {
        return $this->from_poi_type;
    }

    /**
     * Generated from protobuf field <code>string from_poi_type = 35;</code>
     * @param string $var
     * @return $this
     */
    public function setFromPoiType($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_poi_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_address = 36;</code>
     * @return string
     */
    public function getFromAddress()
    {
        return $this->from_address;
    }

    /**
     * Generated from protobuf field <code>string from_address = 36;</code>
     * @param string $var
     * @return $this
     */
    public function setFromAddress($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_address = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_name = 37;</code>
     * @return string
     */
    public function getFromName()
    {
        return $this->from_name;
    }

    /**
     * Generated from protobuf field <code>string from_name = 37;</code>
     * @param string $var
     * @return $this
     */
    public function setFromName($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double to_lat = 38;</code>
     * @return float
     */
    public function getToLat()
    {
        return $this->to_lat;
    }

    /**
     * Generated from protobuf field <code>double to_lat = 38;</code>
     * @param float $var
     * @return $this
     */
    public function setToLat($var)
    {
        GPBUtil::checkDouble($var);
        $this->to_lat = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double to_lng = 39;</code>
     * @return float
     */
    public function getToLng()
    {
        return $this->to_lng;
    }

    /**
     * Generated from protobuf field <code>double to_lng = 39;</code>
     * @param float $var
     * @return $this
     */
    public function setToLng($var)
    {
        GPBUtil::checkDouble($var);
        $this->to_lng = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_poi_id = 40;</code>
     * @return string
     */
    public function getToPoiId()
    {
        return $this->to_poi_id;
    }

    /**
     * Generated from protobuf field <code>string to_poi_id = 40;</code>
     * @param string $var
     * @return $this
     */
    public function setToPoiId($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_poi_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_poi_type = 41;</code>
     * @return string
     */
    public function getToPoiType()
    {
        return $this->to_poi_type;
    }

    /**
     * Generated from protobuf field <code>string to_poi_type = 41;</code>
     * @param string $var
     * @return $this
     */
    public function setToPoiType($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_poi_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_address = 42;</code>
     * @return string
     */
    public function getToAddress()
    {
        return $this->to_address;
    }

    /**
     * Generated from protobuf field <code>string to_address = 42;</code>
     * @param string $var
     * @return $this
     */
    public function setToAddress($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_address = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_name = 43;</code>
     * @return string
     */
    public function getToName()
    {
        return $this->to_name;
    }

    /**
     * Generated from protobuf field <code>string to_name = 43;</code>
     * @param string $var
     * @return $this
     */
    public function setToName($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_name = $var;

        return $this;
    }

    /**
     *订单属性等信息
     *
     * Generated from protobuf field <code>string menu_id = 50;</code>
     * @return string
     */
    public function getMenuId()
    {
        return $this->menu_id;
    }

    /**
     *订单属性等信息
     *
     * Generated from protobuf field <code>string menu_id = 50;</code>
     * @param string $var
     * @return $this
     */
    public function setMenuId($var)
    {
        GPBUtil::checkString($var, True);
        $this->menu_id = $var;

        return $this;
    }

    /**
     *用户勾选项
     *
     * Generated from protobuf field <code>string multi_require_product = 51;</code>
     * @return string
     */
    public function getMultiRequireProduct()
    {
        return $this->multi_require_product;
    }

    /**
     *用户勾选项
     *
     * Generated from protobuf field <code>string multi_require_product = 51;</code>
     * @param string $var
     * @return $this
     */
    public function setMultiRequireProduct($var)
    {
        GPBUtil::checkString($var, True);
        $this->multi_require_product = $var;

        return $this;
    }

    /**
     *用户选择的支付方式
     *
     * Generated from protobuf field <code>int32 payments_type = 52;</code>
     * @return int
     */
    public function getPaymentsType()
    {
        return $this->payments_type;
    }

    /**
     *用户选择的支付方式
     *
     * Generated from protobuf field <code>int32 payments_type = 52;</code>
     * @param int $var
     * @return $this
     */
    public function setPaymentsType($var)
    {
        GPBUtil::checkInt32($var);
        $this->payments_type = $var;

        return $this;
    }

    /**
     *订单类型
     *
     * Generated from protobuf field <code>int32 order_type = 53;</code>
     * @return int
     */
    public function getOrderType()
    {
        return $this->order_type;
    }

    /**
     *订单类型
     *
     * Generated from protobuf field <code>int32 order_type = 53;</code>
     * @param int $var
     * @return $this
     */
    public function setOrderType($var)
    {
        GPBUtil::checkInt32($var);
        $this->order_type = $var;

        return $this;
    }

    /**
     *页面类型
     *
     * Generated from protobuf field <code>int32 page_type = 54;</code>
     * @return int
     */
    public function getPageType()
    {
        return $this->page_type;
    }

    /**
     *页面类型
     *
     * Generated from protobuf field <code>int32 page_type = 54;</code>
     * @param int $var
     * @return $this
     */
    public function setPageType($var)
    {
        GPBUtil::checkInt32($var);
        $this->page_type = $var;

        return $this;
    }

    /**
     *城际拼车订单出发时间
     *
     * Generated from protobuf field <code>string departure_range = 55;</code>
     * @return string
     */
    public function getDepartureRange()
    {
        return isset($this->departure_range) ? $this->departure_range : '';
    }

    public function hasDepartureRange()
    {
        return isset($this->departure_range);
    }

    public function clearDepartureRange()
    {
        unset($this->departure_range);
    }

    /**
     *城际拼车订单出发时间
     *
     * Generated from protobuf field <code>string departure_range = 55;</code>
     * @param string $var
     * @return $this
     */
    public function setDepartureRange($var)
    {
        GPBUtil::checkString($var, True);
        $this->departure_range = $var;

        return $this;
    }

    /**
     *是否为客企扫二维码上车预估
     *
     * Generated from protobuf field <code>string agent_type = 56;</code>
     * @return string
     */
    public function getAgentType()
    {
        return isset($this->agent_type) ? $this->agent_type : '';
    }

    public function hasAgentType()
    {
        return isset($this->agent_type);
    }

    public function clearAgentType()
    {
        unset($this->agent_type);
    }

    /**
     *是否为客企扫二维码上车预估
     *
     * Generated from protobuf field <code>string agent_type = 56;</code>
     * @param string $var
     * @return $this
     */
    public function setAgentType($var)
    {
        GPBUtil::checkString($var, True);
        $this->agent_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string seat_detail_info = 57;</code>
     * @return string
     */
    public function getSeatDetailInfo()
    {
        return isset($this->seat_detail_info) ? $this->seat_detail_info : '';
    }

    public function hasSeatDetailInfo()
    {
        return isset($this->seat_detail_info);
    }

    public function clearSeatDetailInfo()
    {
        unset($this->seat_detail_info);
    }

    /**
     * Generated from protobuf field <code>string seat_detail_info = 57;</code>
     * @param string $var
     * @return $this
     */
    public function setSeatDetailInfo($var)
    {
        GPBUtil::checkString($var, True);
        $this->seat_detail_info = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int64 route_id = 58;</code>
     * @return int|string
     */
    public function getRouteId()
    {
        return isset($this->route_id) ? $this->route_id : 0;
    }

    public function hasRouteId()
    {
        return isset($this->route_id);
    }

    public function clearRouteId()
    {
        unset($this->route_id);
    }

    /**
     * Generated from protobuf field <code>int64 route_id = 58;</code>
     * @param int|string $var
     * @return $this
     */
    public function setRouteId($var)
    {
        GPBUtil::checkInt64($var);
        $this->route_id = $var;

        return $this;
    }

    /**
     *webx公参
     *
     * Generated from protobuf field <code>string xpsid = 191;</code>
     * @return string
     */
    public function getXpsid()
    {
        return isset($this->xpsid) ? $this->xpsid : '';
    }

    public function hasXpsid()
    {
        return isset($this->xpsid);
    }

    public function clearXpsid()
    {
        unset($this->xpsid);
    }

    /**
     *webx公参
     *
     * Generated from protobuf field <code>string xpsid = 191;</code>
     * @param string $var
     * @return $this
     */
    public function setXpsid($var)
    {
        GPBUtil::checkString($var, True);
        $this->xpsid = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string xpsid_root = 192;</code>
     * @return string
     */
    public function getXpsidRoot()
    {
        return isset($this->xpsid_root) ? $this->xpsid_root : '';
    }

    public function hasXpsidRoot()
    {
        return isset($this->xpsid_root);
    }

    public function clearXpsidRoot()
    {
        unset($this->xpsid_root);
    }

    /**
     * Generated from protobuf field <code>string xpsid_root = 192;</code>
     * @param string $var
     * @return $this
     */
    public function setXpsidRoot($var)
    {
        GPBUtil::checkString($var, True);
        $this->xpsid_root = $var;

        return $this;
    }

}

