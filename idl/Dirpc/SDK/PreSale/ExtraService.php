<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.ExtraService</code>
 */
class ExtraService extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string title = 1;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>string default_text = 2;</code>
     */
    protected $default_text = '';
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CommonExpression common_expressions = 3;</code>
     */
    private $common_expressions;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $title
     *     @type string $default_text
     *     @type \Dirpc\SDK\PreSale\CommonExpression[]|\Nuwa\Protobuf\Internal\RepeatedField $common_expressions
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string default_text = 2;</code>
     * @return string
     */
    public function getDefaultText()
    {
        return $this->default_text;
    }

    /**
     * Generated from protobuf field <code>string default_text = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setDefaultText($var)
    {
        GPBUtil::checkString($var, True);
        $this->default_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CommonExpression common_expressions = 3;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getCommonExpressions()
    {
        return $this->common_expressions;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CommonExpression common_expressions = 3;</code>
     * @param \Dirpc\SDK\PreSale\CommonExpression[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setCommonExpressions($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\CommonExpression::class);
        $this->common_expressions = $arr;

        return $this;
    }

}

