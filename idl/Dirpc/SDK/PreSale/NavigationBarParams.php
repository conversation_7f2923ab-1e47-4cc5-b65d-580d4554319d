<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NavigationBarParams</code>
 */
class NavigationBarParams extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string tab_id = 1;</code>
     */
    protected $tab_id = null;
    /**
     * Generated from protobuf field <code>int32 page_type = 2;</code>
     */
    protected $page_type = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $tab_id
     *     @type int $page_type
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string tab_id = 1;</code>
     * @return string
     */
    public function getTabId()
    {
        return isset($this->tab_id) ? $this->tab_id : '';
    }

    public function hasTabId()
    {
        return isset($this->tab_id);
    }

    public function clearTabId()
    {
        unset($this->tab_id);
    }

    /**
     * Generated from protobuf field <code>string tab_id = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTabId($var)
    {
        GPBUtil::checkString($var, True);
        $this->tab_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 page_type = 2;</code>
     * @return int
     */
    public function getPageType()
    {
        return isset($this->page_type) ? $this->page_type : 0;
    }

    public function hasPageType()
    {
        return isset($this->page_type);
    }

    public function clearPageType()
    {
        unset($this->page_type);
    }

    /**
     * Generated from protobuf field <code>int32 page_type = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setPageType($var)
    {
        GPBUtil::checkInt32($var);
        $this->page_type = $var;

        return $this;
    }

}

