<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: carpool.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.CarpoolerTravel</code>
 */
class CarpoolerTravel extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *卡片名称
     *
     * Generated from protobuf field <code>string title = 1;</code>
     */
    protected $title = null;
    /**
     *是否置灰卡片 1-置灰
     *
     * Generated from protobuf field <code>int32 zero_saturate = 2;</code>
     */
    protected $zero_saturate = null;
    /**
     *拼友行程
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CarpoolerTravelInfo infos = 3;</code>
     */
    private $infos;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $title
     *          卡片名称
     *     @type int $zero_saturate
     *          是否置灰卡片 1-置灰
     *     @type \Dirpc\SDK\PreSale\CarpoolerTravelInfo[]|\Nuwa\Protobuf\Internal\RepeatedField $infos
     *          拼友行程
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\Carpool::initOnce();
        parent::__construct($data);
    }

    /**
     *卡片名称
     *
     * Generated from protobuf field <code>string title = 1;</code>
     * @return string
     */
    public function getTitle()
    {
        return isset($this->title) ? $this->title : '';
    }

    public function hasTitle()
    {
        return isset($this->title);
    }

    public function clearTitle()
    {
        unset($this->title);
    }

    /**
     *卡片名称
     *
     * Generated from protobuf field <code>string title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     *是否置灰卡片 1-置灰
     *
     * Generated from protobuf field <code>int32 zero_saturate = 2;</code>
     * @return int
     */
    public function getZeroSaturate()
    {
        return isset($this->zero_saturate) ? $this->zero_saturate : 0;
    }

    public function hasZeroSaturate()
    {
        return isset($this->zero_saturate);
    }

    public function clearZeroSaturate()
    {
        unset($this->zero_saturate);
    }

    /**
     *是否置灰卡片 1-置灰
     *
     * Generated from protobuf field <code>int32 zero_saturate = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setZeroSaturate($var)
    {
        GPBUtil::checkInt32($var);
        $this->zero_saturate = $var;

        return $this;
    }

    /**
     *拼友行程
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CarpoolerTravelInfo infos = 3;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getInfos()
    {
        return $this->infos;
    }

    /**
     *拼友行程
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CarpoolerTravelInfo infos = 3;</code>
     * @param \Dirpc\SDK\PreSale\CarpoolerTravelInfo[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setInfos($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\CarpoolerTravelInfo::class);
        $this->infos = $arr;

        return $this;
    }

}

