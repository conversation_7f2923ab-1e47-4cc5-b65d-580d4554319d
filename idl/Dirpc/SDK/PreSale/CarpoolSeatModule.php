<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.CarpoolSeatModule</code>
 */
class CarpoolSeatModule extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string title = 1;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>string subtitle = 2;</code>
     */
    protected $subtitle = '';
    /**
     * Generated from protobuf field <code>string title_2 = 3;</code>
     */
    protected $title_2 = '';
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SeatConfig seat_config = 4;</code>
     */
    private $seat_config;
    /**
     * Generated from protobuf field <code>int32 select_value = 5;</code>
     */
    protected $select_value = 0;
    /**
     * Generated from protobuf field <code>int32 select_index = 6;</code>
     */
    protected $select_index = 0;
    /**
     * Generated from protobuf field <code>string button_desc = 7;</code>
     */
    protected $button_desc = '';
    /**
     *座位数超选提示
     *
     * Generated from protobuf field <code>string seats_exceed_toast = 8;</code>
     */
    protected $seats_exceed_toast = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $title
     *     @type string $subtitle
     *     @type string $title_2
     *     @type \Dirpc\SDK\PreSale\SeatConfig[]|\Nuwa\Protobuf\Internal\RepeatedField $seat_config
     *     @type int $select_value
     *     @type int $select_index
     *     @type string $button_desc
     *     @type string $seats_exceed_toast
     *          座位数超选提示
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string subtitle = 2;</code>
     * @return string
     */
    public function getSubtitle()
    {
        return $this->subtitle;
    }

    /**
     * Generated from protobuf field <code>string subtitle = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setSubtitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->subtitle = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string title_2 = 3;</code>
     * @return string
     */
    public function getTitle2()
    {
        return $this->title_2;
    }

    /**
     * Generated from protobuf field <code>string title_2 = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle2($var)
    {
        GPBUtil::checkString($var, True);
        $this->title_2 = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SeatConfig seat_config = 4;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getSeatConfig()
    {
        return $this->seat_config;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SeatConfig seat_config = 4;</code>
     * @param \Dirpc\SDK\PreSale\SeatConfig[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSeatConfig($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\SeatConfig::class);
        $this->seat_config = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 select_value = 5;</code>
     * @return int
     */
    public function getSelectValue()
    {
        return $this->select_value;
    }

    /**
     * Generated from protobuf field <code>int32 select_value = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setSelectValue($var)
    {
        GPBUtil::checkInt32($var);
        $this->select_value = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 select_index = 6;</code>
     * @return int
     */
    public function getSelectIndex()
    {
        return $this->select_index;
    }

    /**
     * Generated from protobuf field <code>int32 select_index = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setSelectIndex($var)
    {
        GPBUtil::checkInt32($var);
        $this->select_index = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string button_desc = 7;</code>
     * @return string
     */
    public function getButtonDesc()
    {
        return $this->button_desc;
    }

    /**
     * Generated from protobuf field <code>string button_desc = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setButtonDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->button_desc = $var;

        return $this;
    }

    /**
     *座位数超选提示
     *
     * Generated from protobuf field <code>string seats_exceed_toast = 8;</code>
     * @return string
     */
    public function getSeatsExceedToast()
    {
        return $this->seats_exceed_toast;
    }

    /**
     *座位数超选提示
     *
     * Generated from protobuf field <code>string seats_exceed_toast = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setSeatsExceedToast($var)
    {
        GPBUtil::checkString($var, True);
        $this->seats_exceed_toast = $var;

        return $this;
    }

}

