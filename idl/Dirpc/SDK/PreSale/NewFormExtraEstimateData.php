<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NewFormExtraEstimateData</code>
 */
class NewFormExtraEstimateData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string fee_msg = 1;</code>
     */
    protected $fee_msg = '';
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 2;</code>
     */
    private $fee_desc_list;
    /**
     * Generated from protobuf field <code>int32 count_down = 3;</code>
     */
    protected $count_down = 0;
    /**
     * Generated from protobuf field <code>map<string, string> extra_order_params = 5;</code>
     */
    private $extra_order_params;
    /**
     * Generated from protobuf field <code>string left_down_icon_text = 6;</code>
     */
    protected $left_down_icon_text = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $fee_msg
     *     @type \Dirpc\SDK\PreSale\NewFormFeeDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $fee_desc_list
     *     @type int $count_down
     *     @type array|\Nuwa\Protobuf\Internal\MapField $extra_order_params
     *     @type string $left_down_icon_text
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string fee_msg = 1;</code>
     * @return string
     */
    public function getFeeMsg()
    {
        return $this->fee_msg;
    }

    /**
     * Generated from protobuf field <code>string fee_msg = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 2;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getFeeDescList()
    {
        return $this->fee_desc_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 2;</code>
     * @param \Dirpc\SDK\PreSale\NewFormFeeDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFeeDescList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\NewFormFeeDesc::class);
        $this->fee_desc_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 count_down = 3;</code>
     * @return int
     */
    public function getCountDown()
    {
        return $this->count_down;
    }

    /**
     * Generated from protobuf field <code>int32 count_down = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setCountDown($var)
    {
        GPBUtil::checkInt32($var);
        $this->count_down = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>map<string, string> extra_order_params = 5;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getExtraOrderParams()
    {
        return $this->extra_order_params;
    }

    /**
     * Generated from protobuf field <code>map<string, string> extra_order_params = 5;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setExtraOrderParams($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->extra_order_params = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string left_down_icon_text = 6;</code>
     * @return string
     */
    public function getLeftDownIconText()
    {
        return $this->left_down_icon_text;
    }

    /**
     * Generated from protobuf field <code>string left_down_icon_text = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setLeftDownIconText($var)
    {
        GPBUtil::checkString($var, True);
        $this->left_down_icon_text = $var;

        return $this;
    }

}

