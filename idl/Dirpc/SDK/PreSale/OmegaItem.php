<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.OmegaItem</code>
 */
class OmegaItem extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OmegaValue show = 1;</code>
     */
    protected $show = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OmegaValue click = 2;</code>
     */
    protected $click = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\OmegaValue $show
     *     @type \Dirpc\SDK\PreSale\OmegaValue $click
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OmegaValue show = 1;</code>
     * @return \Dirpc\SDK\PreSale\OmegaValue
     */
    public function getShow()
    {
        return isset($this->show) ? $this->show : null;
    }

    public function hasShow()
    {
        return isset($this->show);
    }

    public function clearShow()
    {
        unset($this->show);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OmegaValue show = 1;</code>
     * @param \Dirpc\SDK\PreSale\OmegaValue $var
     * @return $this
     */
    public function setShow($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\OmegaValue::class);
        $this->show = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OmegaValue click = 2;</code>
     * @return \Dirpc\SDK\PreSale\OmegaValue
     */
    public function getClick()
    {
        return isset($this->click) ? $this->click : null;
    }

    public function hasClick()
    {
        return isset($this->click);
    }

    public function clearClick()
    {
        unset($this->click);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OmegaValue click = 2;</code>
     * @param \Dirpc\SDK\PreSale\OmegaValue $var
     * @return $this
     */
    public function setClick($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\OmegaValue::class);
        $this->click = $var;

        return $this;
    }

}

