<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: carpool.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.InvitationExtraInfoV2</code>
 */
class InvitationExtraInfoV2 extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *微信卡片
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ShareInfoV2 share_info = 1;</code>
     */
    protected $share_info = null;
    /**
     *客态页面下发
     *
     * Generated from protobuf field <code>int32 inviter_status = 2;</code>
     */
    protected $inviter_status = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\ShareInfoV2 $share_info
     *          微信卡片
     *     @type int $inviter_status
     *          客态页面下发
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\Carpool::initOnce();
        parent::__construct($data);
    }

    /**
     *微信卡片
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ShareInfoV2 share_info = 1;</code>
     * @return \Dirpc\SDK\PreSale\ShareInfoV2
     */
    public function getShareInfo()
    {
        return isset($this->share_info) ? $this->share_info : null;
    }

    public function hasShareInfo()
    {
        return isset($this->share_info);
    }

    public function clearShareInfo()
    {
        unset($this->share_info);
    }

    /**
     *微信卡片
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ShareInfoV2 share_info = 1;</code>
     * @param \Dirpc\SDK\PreSale\ShareInfoV2 $var
     * @return $this
     */
    public function setShareInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\ShareInfoV2::class);
        $this->share_info = $var;

        return $this;
    }

    /**
     *客态页面下发
     *
     * Generated from protobuf field <code>int32 inviter_status = 2;</code>
     * @return int
     */
    public function getInviterStatus()
    {
        return isset($this->inviter_status) ? $this->inviter_status : 0;
    }

    public function hasInviterStatus()
    {
        return isset($this->inviter_status);
    }

    public function clearInviterStatus()
    {
        unset($this->inviter_status);
    }

    /**
     *客态页面下发
     *
     * Generated from protobuf field <code>int32 inviter_status = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setInviterStatus($var)
    {
        GPBUtil::checkInt32($var);
        $this->inviter_status = $var;

        return $this;
    }

}

