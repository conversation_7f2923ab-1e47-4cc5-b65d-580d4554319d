<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.RecData</code>
 */
class RecData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RecTag rec_tag = 1;</code>
     */
    protected $rec_tag = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RecTag rec_right_tag = 2;</code>
     */
    protected $rec_right_tag = null;
    /**
     * Generated from protobuf field <code>string rec_bg_color = 3;</code>
     */
    protected $rec_bg_color = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\RecTag $rec_tag
     *     @type \Dirpc\SDK\PreSale\RecTag $rec_right_tag
     *     @type string $rec_bg_color
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RecTag rec_tag = 1;</code>
     * @return \Dirpc\SDK\PreSale\RecTag
     */
    public function getRecTag()
    {
        return isset($this->rec_tag) ? $this->rec_tag : null;
    }

    public function hasRecTag()
    {
        return isset($this->rec_tag);
    }

    public function clearRecTag()
    {
        unset($this->rec_tag);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RecTag rec_tag = 1;</code>
     * @param \Dirpc\SDK\PreSale\RecTag $var
     * @return $this
     */
    public function setRecTag($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\RecTag::class);
        $this->rec_tag = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RecTag rec_right_tag = 2;</code>
     * @return \Dirpc\SDK\PreSale\RecTag
     */
    public function getRecRightTag()
    {
        return isset($this->rec_right_tag) ? $this->rec_right_tag : null;
    }

    public function hasRecRightTag()
    {
        return isset($this->rec_right_tag);
    }

    public function clearRecRightTag()
    {
        unset($this->rec_right_tag);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RecTag rec_right_tag = 2;</code>
     * @param \Dirpc\SDK\PreSale\RecTag $var
     * @return $this
     */
    public function setRecRightTag($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\RecTag::class);
        $this->rec_right_tag = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string rec_bg_color = 3;</code>
     * @return string
     */
    public function getRecBgColor()
    {
        return isset($this->rec_bg_color) ? $this->rec_bg_color : '';
    }

    public function hasRecBgColor()
    {
        return isset($this->rec_bg_color);
    }

    public function clearRecBgColor()
    {
        unset($this->rec_bg_color);
    }

    /**
     * Generated from protobuf field <code>string rec_bg_color = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setRecBgColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->rec_bg_color = $var;

        return $this;
    }

}

