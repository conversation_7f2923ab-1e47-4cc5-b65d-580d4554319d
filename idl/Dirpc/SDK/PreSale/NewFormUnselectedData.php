<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NewFormUnselectedData</code>
 */
class NewFormUnselectedData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 1;</code>
     */
    private $fee_desc_list;
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormMultiPrice multi_price_list = 2;</code>
     */
    private $multi_price_list;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\NewFormFeeDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $fee_desc_list
     *     @type \Dirpc\SDK\PreSale\NewFormMultiPrice[]|\Nuwa\Protobuf\Internal\RepeatedField $multi_price_list
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 1;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getFeeDescList()
    {
        return $this->fee_desc_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 1;</code>
     * @param \Dirpc\SDK\PreSale\NewFormFeeDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFeeDescList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\NewFormFeeDesc::class);
        $this->fee_desc_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormMultiPrice multi_price_list = 2;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getMultiPriceList()
    {
        return $this->multi_price_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormMultiPrice multi_price_list = 2;</code>
     * @param \Dirpc\SDK\PreSale\NewFormMultiPrice[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setMultiPriceList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\NewFormMultiPrice::class);
        $this->multi_price_list = $arr;

        return $this;
    }

}

