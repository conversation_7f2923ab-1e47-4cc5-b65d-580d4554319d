<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.AggregationPriceInfoTag</code>
 */
class AggregationPriceInfoTag extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string content = 1;</code>
     */
    protected $content = '';
    /**
     * Generated from protobuf field <code>string left_icon = 2;</code>
     */
    protected $left_icon = '';
    /**
     *是否需要转动
     *
     * Generated from protobuf field <code>int32 show_anim = 3;</code>
     */
    protected $show_anim = null;
    /**
     *是否需要转动
     *
     * Generated from protobuf field <code>double amount = 4;</code>
     */
    protected $amount = 0.0;
    /**
     * Generated from protobuf field <code>int32 type = 5;</code>
     */
    protected $type = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $content
     *     @type string $left_icon
     *     @type int $show_anim
     *          是否需要转动
     *     @type float $amount
     *          是否需要转动
     *     @type int $type
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string content = 1;</code>
     * @return string
     */
    public function getContent()
    {
        return $this->content;
    }

    /**
     * Generated from protobuf field <code>string content = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setContent($var)
    {
        GPBUtil::checkString($var, True);
        $this->content = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string left_icon = 2;</code>
     * @return string
     */
    public function getLeftIcon()
    {
        return $this->left_icon;
    }

    /**
     * Generated from protobuf field <code>string left_icon = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setLeftIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->left_icon = $var;

        return $this;
    }

    /**
     *是否需要转动
     *
     * Generated from protobuf field <code>int32 show_anim = 3;</code>
     * @return int
     */
    public function getShowAnim()
    {
        return isset($this->show_anim) ? $this->show_anim : 0;
    }

    public function hasShowAnim()
    {
        return isset($this->show_anim);
    }

    public function clearShowAnim()
    {
        unset($this->show_anim);
    }

    /**
     *是否需要转动
     *
     * Generated from protobuf field <code>int32 show_anim = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setShowAnim($var)
    {
        GPBUtil::checkInt32($var);
        $this->show_anim = $var;

        return $this;
    }

    /**
     *是否需要转动
     *
     * Generated from protobuf field <code>double amount = 4;</code>
     * @return float
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     *是否需要转动
     *
     * Generated from protobuf field <code>double amount = 4;</code>
     * @param float $var
     * @return $this
     */
    public function setAmount($var)
    {
        GPBUtil::checkDouble($var);
        $this->amount = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 type = 5;</code>
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Generated from protobuf field <code>int32 type = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkInt32($var);
        $this->type = $var;

        return $this;
    }

}

