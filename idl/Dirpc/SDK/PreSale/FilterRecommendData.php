<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.FilterRecommendData</code>
 */
class FilterRecommendData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string filter_id = 1;</code>
     */
    protected $filter_id = null;
    /**
     *1:选中
     *
     * Generated from protobuf field <code>int32 is_selected = 2;</code>
     */
    protected $is_selected = null;
    /**
     *1:选中
     *
     * Generated from protobuf field <code>string filter_name = 3;</code>
     */
    protected $filter_name = null;
    /**
     * Generated from protobuf field <code>int32 filter_count = 4;</code>
     */
    protected $filter_count = null;
    /**
     * Generated from protobuf field <code>string expect_info = 5;</code>
     */
    protected $expect_info = null;
    /**
     * Generated from protobuf field <code>repeated string fee_desc_list = 6;</code>
     */
    private $fee_desc_list;
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.FilterData filter_data = 7;</code>
     */
    private $filter_data;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $filter_id
     *     @type int $is_selected
     *          1:选中
     *     @type string $filter_name
     *          1:选中
     *     @type int $filter_count
     *     @type string $expect_info
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $fee_desc_list
     *     @type \Dirpc\SDK\PreSale\FilterData[]|\Nuwa\Protobuf\Internal\RepeatedField $filter_data
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string filter_id = 1;</code>
     * @return string
     */
    public function getFilterId()
    {
        return isset($this->filter_id) ? $this->filter_id : '';
    }

    public function hasFilterId()
    {
        return isset($this->filter_id);
    }

    public function clearFilterId()
    {
        unset($this->filter_id);
    }

    /**
     * Generated from protobuf field <code>string filter_id = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setFilterId($var)
    {
        GPBUtil::checkString($var, True);
        $this->filter_id = $var;

        return $this;
    }

    /**
     *1:选中
     *
     * Generated from protobuf field <code>int32 is_selected = 2;</code>
     * @return int
     */
    public function getIsSelected()
    {
        return isset($this->is_selected) ? $this->is_selected : 0;
    }

    public function hasIsSelected()
    {
        return isset($this->is_selected);
    }

    public function clearIsSelected()
    {
        unset($this->is_selected);
    }

    /**
     *1:选中
     *
     * Generated from protobuf field <code>int32 is_selected = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setIsSelected($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_selected = $var;

        return $this;
    }

    /**
     *1:选中
     *
     * Generated from protobuf field <code>string filter_name = 3;</code>
     * @return string
     */
    public function getFilterName()
    {
        return isset($this->filter_name) ? $this->filter_name : '';
    }

    public function hasFilterName()
    {
        return isset($this->filter_name);
    }

    public function clearFilterName()
    {
        unset($this->filter_name);
    }

    /**
     *1:选中
     *
     * Generated from protobuf field <code>string filter_name = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setFilterName($var)
    {
        GPBUtil::checkString($var, True);
        $this->filter_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 filter_count = 4;</code>
     * @return int
     */
    public function getFilterCount()
    {
        return isset($this->filter_count) ? $this->filter_count : 0;
    }

    public function hasFilterCount()
    {
        return isset($this->filter_count);
    }

    public function clearFilterCount()
    {
        unset($this->filter_count);
    }

    /**
     * Generated from protobuf field <code>int32 filter_count = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setFilterCount($var)
    {
        GPBUtil::checkInt32($var);
        $this->filter_count = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string expect_info = 5;</code>
     * @return string
     */
    public function getExpectInfo()
    {
        return isset($this->expect_info) ? $this->expect_info : '';
    }

    public function hasExpectInfo()
    {
        return isset($this->expect_info);
    }

    public function clearExpectInfo()
    {
        unset($this->expect_info);
    }

    /**
     * Generated from protobuf field <code>string expect_info = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setExpectInfo($var)
    {
        GPBUtil::checkString($var, True);
        $this->expect_info = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated string fee_desc_list = 6;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getFeeDescList()
    {
        return $this->fee_desc_list;
    }

    /**
     * Generated from protobuf field <code>repeated string fee_desc_list = 6;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFeeDescList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->fee_desc_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.FilterData filter_data = 7;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getFilterData()
    {
        return $this->filter_data;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.FilterData filter_data = 7;</code>
     * @param \Dirpc\SDK\PreSale\FilterData[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFilterData($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\FilterData::class);
        $this->filter_data = $arr;

        return $this;
    }

}

