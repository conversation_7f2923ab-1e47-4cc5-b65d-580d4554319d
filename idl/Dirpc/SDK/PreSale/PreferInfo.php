<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.PreferInfo</code>
 */
class PreferInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string title = 1;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>string remark = 2;</code>
     */
    protected $remark = '';
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.PreferOption prefer_option = 3;</code>
     */
    private $prefer_option;
    /**
     * Generated from protobuf field <code>string head = 4;</code>
     */
    protected $head = null;
    /**
     * Generated from protobuf field <code>string head_link = 5;</code>
     */
    protected $head_link = null;
    /**
     * Generated from protobuf field <code>int32 is_support_title = 6;</code>
     */
    protected $is_support_title = null;
    /**
     * Generated from protobuf field <code>int32 is_support_remark = 7;</code>
     */
    protected $is_support_remark = null;
    /**
     * Generated from protobuf field <code>int32 is_im_direct_send = 8;</code>
     */
    protected $is_im_direct_send = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $title
     *     @type string $remark
     *     @type \Dirpc\SDK\PreSale\PreferOption[]|\Nuwa\Protobuf\Internal\RepeatedField $prefer_option
     *     @type string $head
     *     @type string $head_link
     *     @type int $is_support_title
     *     @type int $is_support_remark
     *     @type int $is_im_direct_send
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string remark = 2;</code>
     * @return string
     */
    public function getRemark()
    {
        return $this->remark;
    }

    /**
     * Generated from protobuf field <code>string remark = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setRemark($var)
    {
        GPBUtil::checkString($var, True);
        $this->remark = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.PreferOption prefer_option = 3;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getPreferOption()
    {
        return $this->prefer_option;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.PreferOption prefer_option = 3;</code>
     * @param \Dirpc\SDK\PreSale\PreferOption[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setPreferOption($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\PreferOption::class);
        $this->prefer_option = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string head = 4;</code>
     * @return string
     */
    public function getHead()
    {
        return isset($this->head) ? $this->head : '';
    }

    public function hasHead()
    {
        return isset($this->head);
    }

    public function clearHead()
    {
        unset($this->head);
    }

    /**
     * Generated from protobuf field <code>string head = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setHead($var)
    {
        GPBUtil::checkString($var, True);
        $this->head = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string head_link = 5;</code>
     * @return string
     */
    public function getHeadLink()
    {
        return isset($this->head_link) ? $this->head_link : '';
    }

    public function hasHeadLink()
    {
        return isset($this->head_link);
    }

    public function clearHeadLink()
    {
        unset($this->head_link);
    }

    /**
     * Generated from protobuf field <code>string head_link = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setHeadLink($var)
    {
        GPBUtil::checkString($var, True);
        $this->head_link = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 is_support_title = 6;</code>
     * @return int
     */
    public function getIsSupportTitle()
    {
        return isset($this->is_support_title) ? $this->is_support_title : 0;
    }

    public function hasIsSupportTitle()
    {
        return isset($this->is_support_title);
    }

    public function clearIsSupportTitle()
    {
        unset($this->is_support_title);
    }

    /**
     * Generated from protobuf field <code>int32 is_support_title = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setIsSupportTitle($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_support_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 is_support_remark = 7;</code>
     * @return int
     */
    public function getIsSupportRemark()
    {
        return isset($this->is_support_remark) ? $this->is_support_remark : 0;
    }

    public function hasIsSupportRemark()
    {
        return isset($this->is_support_remark);
    }

    public function clearIsSupportRemark()
    {
        unset($this->is_support_remark);
    }

    /**
     * Generated from protobuf field <code>int32 is_support_remark = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setIsSupportRemark($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_support_remark = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 is_im_direct_send = 8;</code>
     * @return int
     */
    public function getIsImDirectSend()
    {
        return isset($this->is_im_direct_send) ? $this->is_im_direct_send : 0;
    }

    public function hasIsImDirectSend()
    {
        return isset($this->is_im_direct_send);
    }

    public function clearIsImDirectSend()
    {
        unset($this->is_im_direct_send);
    }

    /**
     * Generated from protobuf field <code>int32 is_im_direct_send = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setIsImDirectSend($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_im_direct_send = $var;

        return $this;
    }

}

