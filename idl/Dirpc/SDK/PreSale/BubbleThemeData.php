<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.BubbleThemeData</code>
 */
class BubbleThemeData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *车型图片
     *
     * Generated from protobuf field <code>string car_image = 1;</code>
     */
    protected $car_image = null;
    /**
     *主题颜色
     *
     * Generated from protobuf field <code>string theme_color = 2;</code>
     */
    protected $theme_color = '';
    /**
     *卡片阴影颜色
     *
     * Generated from protobuf field <code>string shadow_color = 3;</code>
     */
    protected $shadow_color = null;
    /**
     *选中品类背景渐变色
     *
     * Generated from protobuf field <code>repeated string selected_bg_gradients = 5;</code>
     */
    private $selected_bg_gradients;
    /**
     *未选中品类背景渐变色
     *
     * Generated from protobuf field <code>repeated string unselected_bg_gradients = 6;</code>
     */
    private $unselected_bg_gradients;
    /**
     *品类底部背景渐变色
     *
     * Generated from protobuf field <code>repeated string bottom_bg_gradients = 7;</code>
     */
    private $bottom_bg_gradients;
    /**
     *品类底部标签列表
     *
     * Generated from protobuf field <code>repeated string bottom_label_list = 8;</code>
     */
    private $bottom_label_list;
    /**
     *品类外框背景渐变色
     *
     * Generated from protobuf field <code>repeated string outer_bg_gradients = 9;</code>
     */
    private $outer_bg_gradients;
    /**
     *按钮字体颜色
     *
     * Generated from protobuf field <code>string button_font_color = 11;</code>
     */
    protected $button_font_color = null;
    /**
     *内部(专豪二级服务，盒子已选品类)，背景色
     *
     * Generated from protobuf field <code>string inner_bg_color = 12;</code>
     */
    protected $inner_bg_color = null;
    /**
     *顶部推荐标题
     *
     * Generated from protobuf field <code>string title = 13;</code>
     */
    protected $title = null;
    /**
     *顶部推荐副标题
     *
     * Generated from protobuf field <code>string sub_title = 14;</code>
     */
    protected $sub_title = null;
    /**
     *标题左侧Icon
     *
     * Generated from protobuf field <code>string title_icon = 15;</code>
     */
    protected $title_icon = null;
    /**
     *标题右侧的小车等图标的图片
     *
     * Generated from protobuf field <code>string right_image = 16;</code>
     */
    protected $right_image = null;
    /**
     *底纹
     *
     * Generated from protobuf field <code>string texture_image = 17;</code>
     */
    protected $texture_image = null;
    /**
     *右侧标题
     *
     * Generated from protobuf field <code>string right_title = 18;</code>
     */
    protected $right_title = null;
    /**
     *0.5盒子 右侧跳转链接
     *
     * Generated from protobuf field <code>string right_info_url = 19;</code>
     */
    protected $right_info_url = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $car_image
     *          车型图片
     *     @type string $theme_color
     *          主题颜色
     *     @type string $shadow_color
     *          卡片阴影颜色
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $selected_bg_gradients
     *          选中品类背景渐变色
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $unselected_bg_gradients
     *          未选中品类背景渐变色
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $bottom_bg_gradients
     *          品类底部背景渐变色
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $bottom_label_list
     *          品类底部标签列表
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $outer_bg_gradients
     *          品类外框背景渐变色
     *     @type string $button_font_color
     *          按钮字体颜色
     *     @type string $inner_bg_color
     *          内部(专豪二级服务，盒子已选品类)，背景色
     *     @type string $title
     *          顶部推荐标题
     *     @type string $sub_title
     *          顶部推荐副标题
     *     @type string $title_icon
     *          标题左侧Icon
     *     @type string $right_image
     *          标题右侧的小车等图标的图片
     *     @type string $texture_image
     *          底纹
     *     @type string $right_title
     *          右侧标题
     *     @type string $right_info_url
     *          0.5盒子 右侧跳转链接
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *车型图片
     *
     * Generated from protobuf field <code>string car_image = 1;</code>
     * @return string
     */
    public function getCarImage()
    {
        return isset($this->car_image) ? $this->car_image : '';
    }

    public function hasCarImage()
    {
        return isset($this->car_image);
    }

    public function clearCarImage()
    {
        unset($this->car_image);
    }

    /**
     *车型图片
     *
     * Generated from protobuf field <code>string car_image = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setCarImage($var)
    {
        GPBUtil::checkString($var, True);
        $this->car_image = $var;

        return $this;
    }

    /**
     *主题颜色
     *
     * Generated from protobuf field <code>string theme_color = 2;</code>
     * @return string
     */
    public function getThemeColor()
    {
        return $this->theme_color;
    }

    /**
     *主题颜色
     *
     * Generated from protobuf field <code>string theme_color = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setThemeColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->theme_color = $var;

        return $this;
    }

    /**
     *卡片阴影颜色
     *
     * Generated from protobuf field <code>string shadow_color = 3;</code>
     * @return string
     */
    public function getShadowColor()
    {
        return isset($this->shadow_color) ? $this->shadow_color : '';
    }

    public function hasShadowColor()
    {
        return isset($this->shadow_color);
    }

    public function clearShadowColor()
    {
        unset($this->shadow_color);
    }

    /**
     *卡片阴影颜色
     *
     * Generated from protobuf field <code>string shadow_color = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setShadowColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->shadow_color = $var;

        return $this;
    }

    /**
     *选中品类背景渐变色
     *
     * Generated from protobuf field <code>repeated string selected_bg_gradients = 5;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getSelectedBgGradients()
    {
        return $this->selected_bg_gradients;
    }

    /**
     *选中品类背景渐变色
     *
     * Generated from protobuf field <code>repeated string selected_bg_gradients = 5;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSelectedBgGradients($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->selected_bg_gradients = $arr;

        return $this;
    }

    /**
     *未选中品类背景渐变色
     *
     * Generated from protobuf field <code>repeated string unselected_bg_gradients = 6;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getUnselectedBgGradients()
    {
        return $this->unselected_bg_gradients;
    }

    /**
     *未选中品类背景渐变色
     *
     * Generated from protobuf field <code>repeated string unselected_bg_gradients = 6;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setUnselectedBgGradients($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->unselected_bg_gradients = $arr;

        return $this;
    }

    /**
     *品类底部背景渐变色
     *
     * Generated from protobuf field <code>repeated string bottom_bg_gradients = 7;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getBottomBgGradients()
    {
        return $this->bottom_bg_gradients;
    }

    /**
     *品类底部背景渐变色
     *
     * Generated from protobuf field <code>repeated string bottom_bg_gradients = 7;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBottomBgGradients($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->bottom_bg_gradients = $arr;

        return $this;
    }

    /**
     *品类底部标签列表
     *
     * Generated from protobuf field <code>repeated string bottom_label_list = 8;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getBottomLabelList()
    {
        return $this->bottom_label_list;
    }

    /**
     *品类底部标签列表
     *
     * Generated from protobuf field <code>repeated string bottom_label_list = 8;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBottomLabelList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->bottom_label_list = $arr;

        return $this;
    }

    /**
     *品类外框背景渐变色
     *
     * Generated from protobuf field <code>repeated string outer_bg_gradients = 9;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getOuterBgGradients()
    {
        return $this->outer_bg_gradients;
    }

    /**
     *品类外框背景渐变色
     *
     * Generated from protobuf field <code>repeated string outer_bg_gradients = 9;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setOuterBgGradients($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->outer_bg_gradients = $arr;

        return $this;
    }

    /**
     *按钮字体颜色
     *
     * Generated from protobuf field <code>string button_font_color = 11;</code>
     * @return string
     */
    public function getButtonFontColor()
    {
        return isset($this->button_font_color) ? $this->button_font_color : '';
    }

    public function hasButtonFontColor()
    {
        return isset($this->button_font_color);
    }

    public function clearButtonFontColor()
    {
        unset($this->button_font_color);
    }

    /**
     *按钮字体颜色
     *
     * Generated from protobuf field <code>string button_font_color = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setButtonFontColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->button_font_color = $var;

        return $this;
    }

    /**
     *内部(专豪二级服务，盒子已选品类)，背景色
     *
     * Generated from protobuf field <code>string inner_bg_color = 12;</code>
     * @return string
     */
    public function getInnerBgColor()
    {
        return isset($this->inner_bg_color) ? $this->inner_bg_color : '';
    }

    public function hasInnerBgColor()
    {
        return isset($this->inner_bg_color);
    }

    public function clearInnerBgColor()
    {
        unset($this->inner_bg_color);
    }

    /**
     *内部(专豪二级服务，盒子已选品类)，背景色
     *
     * Generated from protobuf field <code>string inner_bg_color = 12;</code>
     * @param string $var
     * @return $this
     */
    public function setInnerBgColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->inner_bg_color = $var;

        return $this;
    }

    /**
     *顶部推荐标题
     *
     * Generated from protobuf field <code>string title = 13;</code>
     * @return string
     */
    public function getTitle()
    {
        return isset($this->title) ? $this->title : '';
    }

    public function hasTitle()
    {
        return isset($this->title);
    }

    public function clearTitle()
    {
        unset($this->title);
    }

    /**
     *顶部推荐标题
     *
     * Generated from protobuf field <code>string title = 13;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     *顶部推荐副标题
     *
     * Generated from protobuf field <code>string sub_title = 14;</code>
     * @return string
     */
    public function getSubTitle()
    {
        return isset($this->sub_title) ? $this->sub_title : '';
    }

    public function hasSubTitle()
    {
        return isset($this->sub_title);
    }

    public function clearSubTitle()
    {
        unset($this->sub_title);
    }

    /**
     *顶部推荐副标题
     *
     * Generated from protobuf field <code>string sub_title = 14;</code>
     * @param string $var
     * @return $this
     */
    public function setSubTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_title = $var;

        return $this;
    }

    /**
     *标题左侧Icon
     *
     * Generated from protobuf field <code>string title_icon = 15;</code>
     * @return string
     */
    public function getTitleIcon()
    {
        return isset($this->title_icon) ? $this->title_icon : '';
    }

    public function hasTitleIcon()
    {
        return isset($this->title_icon);
    }

    public function clearTitleIcon()
    {
        unset($this->title_icon);
    }

    /**
     *标题左侧Icon
     *
     * Generated from protobuf field <code>string title_icon = 15;</code>
     * @param string $var
     * @return $this
     */
    public function setTitleIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->title_icon = $var;

        return $this;
    }

    /**
     *标题右侧的小车等图标的图片
     *
     * Generated from protobuf field <code>string right_image = 16;</code>
     * @return string
     */
    public function getRightImage()
    {
        return isset($this->right_image) ? $this->right_image : '';
    }

    public function hasRightImage()
    {
        return isset($this->right_image);
    }

    public function clearRightImage()
    {
        unset($this->right_image);
    }

    /**
     *标题右侧的小车等图标的图片
     *
     * Generated from protobuf field <code>string right_image = 16;</code>
     * @param string $var
     * @return $this
     */
    public function setRightImage($var)
    {
        GPBUtil::checkString($var, True);
        $this->right_image = $var;

        return $this;
    }

    /**
     *底纹
     *
     * Generated from protobuf field <code>string texture_image = 17;</code>
     * @return string
     */
    public function getTextureImage()
    {
        return isset($this->texture_image) ? $this->texture_image : '';
    }

    public function hasTextureImage()
    {
        return isset($this->texture_image);
    }

    public function clearTextureImage()
    {
        unset($this->texture_image);
    }

    /**
     *底纹
     *
     * Generated from protobuf field <code>string texture_image = 17;</code>
     * @param string $var
     * @return $this
     */
    public function setTextureImage($var)
    {
        GPBUtil::checkString($var, True);
        $this->texture_image = $var;

        return $this;
    }

    /**
     *右侧标题
     *
     * Generated from protobuf field <code>string right_title = 18;</code>
     * @return string
     */
    public function getRightTitle()
    {
        return isset($this->right_title) ? $this->right_title : '';
    }

    public function hasRightTitle()
    {
        return isset($this->right_title);
    }

    public function clearRightTitle()
    {
        unset($this->right_title);
    }

    /**
     *右侧标题
     *
     * Generated from protobuf field <code>string right_title = 18;</code>
     * @param string $var
     * @return $this
     */
    public function setRightTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->right_title = $var;

        return $this;
    }

    /**
     *0.5盒子 右侧跳转链接
     *
     * Generated from protobuf field <code>string right_info_url = 19;</code>
     * @return string
     */
    public function getRightInfoUrl()
    {
        return isset($this->right_info_url) ? $this->right_info_url : '';
    }

    public function hasRightInfoUrl()
    {
        return isset($this->right_info_url);
    }

    public function clearRightInfoUrl()
    {
        unset($this->right_info_url);
    }

    /**
     *0.5盒子 右侧跳转链接
     *
     * Generated from protobuf field <code>string right_info_url = 19;</code>
     * @param string $var
     * @return $this
     */
    public function setRightInfoUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->right_info_url = $var;

        return $this;
    }

}

