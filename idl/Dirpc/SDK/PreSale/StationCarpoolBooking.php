<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.StationCarpoolBooking</code>
 */
class StationCarpoolBooking extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 is_interrupt = 1;</code>
     */
    protected $is_interrupt = null;
    /**
     * Generated from protobuf field <code>string dialog_title = 2;</code>
     */
    protected $dialog_title = null;
    /**
     * Generated from protobuf field <code>string dialog_sub_title = 3;</code>
     */
    protected $dialog_sub_title = null;
    /**
     * Generated from protobuf field <code>string title = 4;</code>
     */
    protected $title = null;
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CarpoolBookingTimeSpan time_span = 5;</code>
     */
    private $time_span;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $is_interrupt
     *     @type string $dialog_title
     *     @type string $dialog_sub_title
     *     @type string $title
     *     @type \Dirpc\SDK\PreSale\CarpoolBookingTimeSpan[]|\Nuwa\Protobuf\Internal\RepeatedField $time_span
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 is_interrupt = 1;</code>
     * @return int
     */
    public function getIsInterrupt()
    {
        return isset($this->is_interrupt) ? $this->is_interrupt : 0;
    }

    public function hasIsInterrupt()
    {
        return isset($this->is_interrupt);
    }

    public function clearIsInterrupt()
    {
        unset($this->is_interrupt);
    }

    /**
     * Generated from protobuf field <code>int32 is_interrupt = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setIsInterrupt($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_interrupt = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string dialog_title = 2;</code>
     * @return string
     */
    public function getDialogTitle()
    {
        return isset($this->dialog_title) ? $this->dialog_title : '';
    }

    public function hasDialogTitle()
    {
        return isset($this->dialog_title);
    }

    public function clearDialogTitle()
    {
        unset($this->dialog_title);
    }

    /**
     * Generated from protobuf field <code>string dialog_title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setDialogTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->dialog_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string dialog_sub_title = 3;</code>
     * @return string
     */
    public function getDialogSubTitle()
    {
        return isset($this->dialog_sub_title) ? $this->dialog_sub_title : '';
    }

    public function hasDialogSubTitle()
    {
        return isset($this->dialog_sub_title);
    }

    public function clearDialogSubTitle()
    {
        unset($this->dialog_sub_title);
    }

    /**
     * Generated from protobuf field <code>string dialog_sub_title = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setDialogSubTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->dialog_sub_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string title = 4;</code>
     * @return string
     */
    public function getTitle()
    {
        return isset($this->title) ? $this->title : '';
    }

    public function hasTitle()
    {
        return isset($this->title);
    }

    public function clearTitle()
    {
        unset($this->title);
    }

    /**
     * Generated from protobuf field <code>string title = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CarpoolBookingTimeSpan time_span = 5;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getTimeSpan()
    {
        return $this->time_span;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CarpoolBookingTimeSpan time_span = 5;</code>
     * @param \Dirpc\SDK\PreSale\CarpoolBookingTimeSpan[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setTimeSpan($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\CarpoolBookingTimeSpan::class);
        $this->time_span = $arr;

        return $this;
    }

}

