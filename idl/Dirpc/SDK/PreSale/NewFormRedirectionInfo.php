<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NewFormRedirectionInfo</code>
 */
class NewFormRedirectionInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string poi_id = 1;</code>
     */
    protected $poi_id = null;
    /**
     * Generated from protobuf field <code>string displayname = 2;</code>
     */
    protected $displayname = null;
    /**
     * Generated from protobuf field <code>string address = 3;</code>
     */
    protected $address = null;
    /**
     * Generated from protobuf field <code>double lat = 4;</code>
     */
    protected $lat = null;
    /**
     * Generated from protobuf field <code>double lng = 5;</code>
     */
    protected $lng = null;
    /**
     * Generated from protobuf field <code>string coordinate_type = 6;</code>
     */
    protected $coordinate_type = null;
    /**
     * Generated from protobuf field <code>int32 city_id = 7;</code>
     */
    protected $city_id = null;
    /**
     * Generated from protobuf field <code>string city_name = 8;</code>
     */
    protected $city_name = null;
    /**
     * Generated from protobuf field <code>string gap = 9;</code>
     */
    protected $gap = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $poi_id
     *     @type string $displayname
     *     @type string $address
     *     @type float $lat
     *     @type float $lng
     *     @type string $coordinate_type
     *     @type int $city_id
     *     @type string $city_name
     *     @type string $gap
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string poi_id = 1;</code>
     * @return string
     */
    public function getPoiId()
    {
        return isset($this->poi_id) ? $this->poi_id : '';
    }

    public function hasPoiId()
    {
        return isset($this->poi_id);
    }

    public function clearPoiId()
    {
        unset($this->poi_id);
    }

    /**
     * Generated from protobuf field <code>string poi_id = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setPoiId($var)
    {
        GPBUtil::checkString($var, True);
        $this->poi_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string displayname = 2;</code>
     * @return string
     */
    public function getDisplayname()
    {
        return isset($this->displayname) ? $this->displayname : '';
    }

    public function hasDisplayname()
    {
        return isset($this->displayname);
    }

    public function clearDisplayname()
    {
        unset($this->displayname);
    }

    /**
     * Generated from protobuf field <code>string displayname = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setDisplayname($var)
    {
        GPBUtil::checkString($var, True);
        $this->displayname = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string address = 3;</code>
     * @return string
     */
    public function getAddress()
    {
        return isset($this->address) ? $this->address : '';
    }

    public function hasAddress()
    {
        return isset($this->address);
    }

    public function clearAddress()
    {
        unset($this->address);
    }

    /**
     * Generated from protobuf field <code>string address = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setAddress($var)
    {
        GPBUtil::checkString($var, True);
        $this->address = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double lat = 4;</code>
     * @return float
     */
    public function getLat()
    {
        return isset($this->lat) ? $this->lat : 0.0;
    }

    public function hasLat()
    {
        return isset($this->lat);
    }

    public function clearLat()
    {
        unset($this->lat);
    }

    /**
     * Generated from protobuf field <code>double lat = 4;</code>
     * @param float $var
     * @return $this
     */
    public function setLat($var)
    {
        GPBUtil::checkDouble($var);
        $this->lat = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double lng = 5;</code>
     * @return float
     */
    public function getLng()
    {
        return isset($this->lng) ? $this->lng : 0.0;
    }

    public function hasLng()
    {
        return isset($this->lng);
    }

    public function clearLng()
    {
        unset($this->lng);
    }

    /**
     * Generated from protobuf field <code>double lng = 5;</code>
     * @param float $var
     * @return $this
     */
    public function setLng($var)
    {
        GPBUtil::checkDouble($var);
        $this->lng = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string coordinate_type = 6;</code>
     * @return string
     */
    public function getCoordinateType()
    {
        return isset($this->coordinate_type) ? $this->coordinate_type : '';
    }

    public function hasCoordinateType()
    {
        return isset($this->coordinate_type);
    }

    public function clearCoordinateType()
    {
        unset($this->coordinate_type);
    }

    /**
     * Generated from protobuf field <code>string coordinate_type = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setCoordinateType($var)
    {
        GPBUtil::checkString($var, True);
        $this->coordinate_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 city_id = 7;</code>
     * @return int
     */
    public function getCityId()
    {
        return isset($this->city_id) ? $this->city_id : 0;
    }

    public function hasCityId()
    {
        return isset($this->city_id);
    }

    public function clearCityId()
    {
        unset($this->city_id);
    }

    /**
     * Generated from protobuf field <code>int32 city_id = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setCityId($var)
    {
        GPBUtil::checkInt32($var);
        $this->city_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string city_name = 8;</code>
     * @return string
     */
    public function getCityName()
    {
        return isset($this->city_name) ? $this->city_name : '';
    }

    public function hasCityName()
    {
        return isset($this->city_name);
    }

    public function clearCityName()
    {
        unset($this->city_name);
    }

    /**
     * Generated from protobuf field <code>string city_name = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setCityName($var)
    {
        GPBUtil::checkString($var, True);
        $this->city_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string gap = 9;</code>
     * @return string
     */
    public function getGap()
    {
        return isset($this->gap) ? $this->gap : '';
    }

    public function hasGap()
    {
        return isset($this->gap);
    }

    public function clearGap()
    {
        unset($this->gap);
    }

    /**
     * Generated from protobuf field <code>string gap = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setGap($var)
    {
        GPBUtil::checkString($var, True);
        $this->gap = $var;

        return $this;
    }

}

