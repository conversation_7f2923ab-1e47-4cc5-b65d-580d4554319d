<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 *锚点
 *
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.AnchorPoint</code>
 */
class AnchorPoint extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *锚点价格
     *
     * Generated from protobuf field <code>int32 anchor_fee = 1;</code>
     */
    protected $anchor_fee = null;
    /**
     *锚点文案
     *
     * Generated from protobuf field <code>string anchor_text = 2;</code>
     */
    protected $anchor_text = null;
    /**
     *锚点文案颜色
     *
     * Generated from protobuf field <code>string anchor_text_color = 3;</code>
     */
    protected $anchor_text_color = null;
    /**
     *锚点icon
     *
     * Generated from protobuf field <code>string anchor_icon = 4;</code>
     */
    protected $anchor_icon = null;
    /**
     *价格字体颜色
     *
     * Generated from protobuf field <code>string anchor_fee_color = 5;</code>
     */
    protected $anchor_fee_color = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $anchor_fee
     *          锚点价格
     *     @type string $anchor_text
     *          锚点文案
     *     @type string $anchor_text_color
     *          锚点文案颜色
     *     @type string $anchor_icon
     *          锚点icon
     *     @type string $anchor_fee_color
     *          价格字体颜色
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *锚点价格
     *
     * Generated from protobuf field <code>int32 anchor_fee = 1;</code>
     * @return int
     */
    public function getAnchorFee()
    {
        return isset($this->anchor_fee) ? $this->anchor_fee : 0;
    }

    public function hasAnchorFee()
    {
        return isset($this->anchor_fee);
    }

    public function clearAnchorFee()
    {
        unset($this->anchor_fee);
    }

    /**
     *锚点价格
     *
     * Generated from protobuf field <code>int32 anchor_fee = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setAnchorFee($var)
    {
        GPBUtil::checkInt32($var);
        $this->anchor_fee = $var;

        return $this;
    }

    /**
     *锚点文案
     *
     * Generated from protobuf field <code>string anchor_text = 2;</code>
     * @return string
     */
    public function getAnchorText()
    {
        return isset($this->anchor_text) ? $this->anchor_text : '';
    }

    public function hasAnchorText()
    {
        return isset($this->anchor_text);
    }

    public function clearAnchorText()
    {
        unset($this->anchor_text);
    }

    /**
     *锚点文案
     *
     * Generated from protobuf field <code>string anchor_text = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setAnchorText($var)
    {
        GPBUtil::checkString($var, True);
        $this->anchor_text = $var;

        return $this;
    }

    /**
     *锚点文案颜色
     *
     * Generated from protobuf field <code>string anchor_text_color = 3;</code>
     * @return string
     */
    public function getAnchorTextColor()
    {
        return isset($this->anchor_text_color) ? $this->anchor_text_color : '';
    }

    public function hasAnchorTextColor()
    {
        return isset($this->anchor_text_color);
    }

    public function clearAnchorTextColor()
    {
        unset($this->anchor_text_color);
    }

    /**
     *锚点文案颜色
     *
     * Generated from protobuf field <code>string anchor_text_color = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setAnchorTextColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->anchor_text_color = $var;

        return $this;
    }

    /**
     *锚点icon
     *
     * Generated from protobuf field <code>string anchor_icon = 4;</code>
     * @return string
     */
    public function getAnchorIcon()
    {
        return isset($this->anchor_icon) ? $this->anchor_icon : '';
    }

    public function hasAnchorIcon()
    {
        return isset($this->anchor_icon);
    }

    public function clearAnchorIcon()
    {
        unset($this->anchor_icon);
    }

    /**
     *锚点icon
     *
     * Generated from protobuf field <code>string anchor_icon = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setAnchorIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->anchor_icon = $var;

        return $this;
    }

    /**
     *价格字体颜色
     *
     * Generated from protobuf field <code>string anchor_fee_color = 5;</code>
     * @return string
     */
    public function getAnchorFeeColor()
    {
        return isset($this->anchor_fee_color) ? $this->anchor_fee_color : '';
    }

    public function hasAnchorFeeColor()
    {
        return isset($this->anchor_fee_color);
    }

    public function clearAnchorFeeColor()
    {
        unset($this->anchor_fee_color);
    }

    /**
     *价格字体颜色
     *
     * Generated from protobuf field <code>string anchor_fee_color = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setAnchorFeeColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->anchor_fee_color = $var;

        return $this;
    }

}

