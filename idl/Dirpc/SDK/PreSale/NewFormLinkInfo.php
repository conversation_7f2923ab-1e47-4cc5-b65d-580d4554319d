<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NewFormLinkInfo</code>
 */
class NewFormLinkInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string link_product = 1;</code>
     */
    protected $link_product = '';
    /**
     * Generated from protobuf field <code>int32 is_selected = 2;</code>
     */
    protected $is_selected = 0;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormPreferData prefer_data = 3;</code>
     */
    protected $prefer_data = null;
    /**
     * Generated from protobuf field <code>string fee_msg = 4;</code>
     */
    protected $fee_msg = '';
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 5;</code>
     */
    private $fee_desc_list;
    /**
     * Generated from protobuf field <code>string info_url = 6;</code>
     */
    protected $info_url = '';
    /**
     * Generated from protobuf field <code>int32 is_strength = 7;</code>
     */
    protected $is_strength = 0;
    /**
     *1表示单勾
     *
     * Generated from protobuf field <code>int32 select_style = 8;</code>
     */
    protected $select_style = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $link_product
     *     @type int $is_selected
     *     @type \Dirpc\SDK\PreSale\NewFormPreferData $prefer_data
     *     @type string $fee_msg
     *     @type \Dirpc\SDK\PreSale\NewFormFeeDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $fee_desc_list
     *     @type string $info_url
     *     @type int $is_strength
     *     @type int $select_style
     *          1表示单勾
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string link_product = 1;</code>
     * @return string
     */
    public function getLinkProduct()
    {
        return $this->link_product;
    }

    /**
     * Generated from protobuf field <code>string link_product = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setLinkProduct($var)
    {
        GPBUtil::checkString($var, True);
        $this->link_product = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 is_selected = 2;</code>
     * @return int
     */
    public function getIsSelected()
    {
        return $this->is_selected;
    }

    /**
     * Generated from protobuf field <code>int32 is_selected = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setIsSelected($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_selected = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormPreferData prefer_data = 3;</code>
     * @return \Dirpc\SDK\PreSale\NewFormPreferData
     */
    public function getPreferData()
    {
        return isset($this->prefer_data) ? $this->prefer_data : null;
    }

    public function hasPreferData()
    {
        return isset($this->prefer_data);
    }

    public function clearPreferData()
    {
        unset($this->prefer_data);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormPreferData prefer_data = 3;</code>
     * @param \Dirpc\SDK\PreSale\NewFormPreferData $var
     * @return $this
     */
    public function setPreferData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NewFormPreferData::class);
        $this->prefer_data = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_msg = 4;</code>
     * @return string
     */
    public function getFeeMsg()
    {
        return $this->fee_msg;
    }

    /**
     * Generated from protobuf field <code>string fee_msg = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 5;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getFeeDescList()
    {
        return $this->fee_desc_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 5;</code>
     * @param \Dirpc\SDK\PreSale\NewFormFeeDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFeeDescList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\NewFormFeeDesc::class);
        $this->fee_desc_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string info_url = 6;</code>
     * @return string
     */
    public function getInfoUrl()
    {
        return $this->info_url;
    }

    /**
     * Generated from protobuf field <code>string info_url = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setInfoUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->info_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 is_strength = 7;</code>
     * @return int
     */
    public function getIsStrength()
    {
        return $this->is_strength;
    }

    /**
     * Generated from protobuf field <code>int32 is_strength = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setIsStrength($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_strength = $var;

        return $this;
    }

    /**
     *1表示单勾
     *
     * Generated from protobuf field <code>int32 select_style = 8;</code>
     * @return int
     */
    public function getSelectStyle()
    {
        return $this->select_style;
    }

    /**
     *1表示单勾
     *
     * Generated from protobuf field <code>int32 select_style = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setSelectStyle($var)
    {
        GPBUtil::checkInt32($var);
        $this->select_style = $var;

        return $this;
    }

}

