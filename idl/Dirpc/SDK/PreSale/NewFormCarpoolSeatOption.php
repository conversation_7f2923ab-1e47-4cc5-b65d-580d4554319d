<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NewFormCarpoolSeatOption</code>
 */
class NewFormCarpoolSeatOption extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string label = 1;</code>
     */
    protected $label = '';
    /**
     * Generated from protobuf field <code>int32 value = 2;</code>
     */
    protected $value = 0;
    /**
     * Generated from protobuf field <code>bool selected = 3;</code>
     */
    protected $selected = false;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $label
     *     @type int $value
     *     @type bool $selected
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string label = 1;</code>
     * @return string
     */
    public function getLabel()
    {
        return $this->label;
    }

    /**
     * Generated from protobuf field <code>string label = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setLabel($var)
    {
        GPBUtil::checkString($var, True);
        $this->label = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 value = 2;</code>
     * @return int
     */
    public function getValue()
    {
        return $this->value;
    }

    /**
     * Generated from protobuf field <code>int32 value = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setValue($var)
    {
        GPBUtil::checkInt32($var);
        $this->value = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool selected = 3;</code>
     * @return bool
     */
    public function getSelected()
    {
        return $this->selected;
    }

    /**
     * Generated from protobuf field <code>bool selected = 3;</code>
     * @param bool $var
     * @return $this
     */
    public function setSelected($var)
    {
        GPBUtil::checkBool($var);
        $this->selected = $var;

        return $this;
    }

}

