<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideEstimateComboRecommend</code>
 */
class SideEstimateComboRecommend extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string estimate_id = 1;</code>
     */
    protected $estimate_id = '';
    /**
     * Generated from protobuf field <code>int32 goods_id = 2;</code>
     */
    protected $goods_id = 0;
    /**
     * Generated from protobuf field <code>int32 best_batch_id = 3;</code>
     */
    protected $best_batch_id = 0;
    /**
     * Generated from protobuf field <code>string high_light = 4;</code>
     */
    protected $high_light = null;
    /**
     * Generated from protobuf field <code>string left_icon = 5;</code>
     */
    protected $left_icon = '';
    /**
     * Generated from protobuf field <code>string left_title = 6;</code>
     */
    protected $left_title = '';
    /**
     * Generated from protobuf field <code>string right_title = 7;</code>
     */
    protected $right_title = null;
    /**
     * Generated from protobuf field <code>repeated string sub_title = 8;</code>
     */
    private $sub_title;
    /**
     * Generated from protobuf field <code>string service_fee_msg = 9;</code>
     */
    protected $service_fee_msg = '';
    /**
     * Generated from protobuf field <code>string fee_info_desc = 10;</code>
     */
    protected $fee_info_desc = null;
    /**
     * Generated from protobuf field <code>string fee_desc_url = 11;</code>
     */
    protected $fee_desc_url = null;
    /**
     * Generated from protobuf field <code>string fee_msg = 12;</code>
     */
    protected $fee_msg = '';
    /**
     * Generated from protobuf field <code>int32 select_type = 14;</code>
     */
    protected $select_type = null;
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SideNewFormFeeDesc price_info_desc = 15;</code>
     */
    private $price_info_desc;
    /**
     * Generated from protobuf field <code>map<string, string> extra = 16;</code>
     */
    private $extra;
    /**
     * Generated from protobuf field <code>int32 type = 17;</code>
     */
    protected $type = null;
    /**
     * Generated from protobuf field <code>string fee_amount = 18;</code>
     */
    protected $fee_amount = null;
    /**
     * Generated from protobuf field <code>string car_fee_msg = 19;</code>
     */
    protected $car_fee_msg = null;
    /**
     * Generated from protobuf field <code>string car_fee_amount = 20;</code>
     */
    protected $car_fee_amount = null;
    /**
     * Generated from protobuf field <code>string combo_button_subtitle = 21;</code>
     */
    protected $combo_button_subtitle = null;
    /**
     * Generated from protobuf field <code>string fee_msg_template = 22;</code>
     */
    protected $fee_msg_template = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $estimate_id
     *     @type int $goods_id
     *     @type int $best_batch_id
     *     @type string $high_light
     *     @type string $left_icon
     *     @type string $left_title
     *     @type string $right_title
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $sub_title
     *     @type string $service_fee_msg
     *     @type string $fee_info_desc
     *     @type string $fee_desc_url
     *     @type string $fee_msg
     *     @type int $select_type
     *     @type \Dirpc\SDK\PreSale\SideNewFormFeeDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $price_info_desc
     *     @type array|\Nuwa\Protobuf\Internal\MapField $extra
     *     @type int $type
     *     @type string $fee_amount
     *     @type string $car_fee_msg
     *     @type string $car_fee_amount
     *     @type string $combo_button_subtitle
     *     @type string $fee_msg_template
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string estimate_id = 1;</code>
     * @return string
     */
    public function getEstimateId()
    {
        return $this->estimate_id;
    }

    /**
     * Generated from protobuf field <code>string estimate_id = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateId($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 goods_id = 2;</code>
     * @return int
     */
    public function getGoodsId()
    {
        return $this->goods_id;
    }

    /**
     * Generated from protobuf field <code>int32 goods_id = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setGoodsId($var)
    {
        GPBUtil::checkInt32($var);
        $this->goods_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 best_batch_id = 3;</code>
     * @return int
     */
    public function getBestBatchId()
    {
        return $this->best_batch_id;
    }

    /**
     * Generated from protobuf field <code>int32 best_batch_id = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setBestBatchId($var)
    {
        GPBUtil::checkInt32($var);
        $this->best_batch_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string high_light = 4;</code>
     * @return string
     */
    public function getHighLight()
    {
        return isset($this->high_light) ? $this->high_light : '';
    }

    public function hasHighLight()
    {
        return isset($this->high_light);
    }

    public function clearHighLight()
    {
        unset($this->high_light);
    }

    /**
     * Generated from protobuf field <code>string high_light = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setHighLight($var)
    {
        GPBUtil::checkString($var, True);
        $this->high_light = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string left_icon = 5;</code>
     * @return string
     */
    public function getLeftIcon()
    {
        return $this->left_icon;
    }

    /**
     * Generated from protobuf field <code>string left_icon = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setLeftIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->left_icon = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string left_title = 6;</code>
     * @return string
     */
    public function getLeftTitle()
    {
        return $this->left_title;
    }

    /**
     * Generated from protobuf field <code>string left_title = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setLeftTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->left_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string right_title = 7;</code>
     * @return string
     */
    public function getRightTitle()
    {
        return isset($this->right_title) ? $this->right_title : '';
    }

    public function hasRightTitle()
    {
        return isset($this->right_title);
    }

    public function clearRightTitle()
    {
        unset($this->right_title);
    }

    /**
     * Generated from protobuf field <code>string right_title = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setRightTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->right_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated string sub_title = 8;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getSubTitle()
    {
        return $this->sub_title;
    }

    /**
     * Generated from protobuf field <code>repeated string sub_title = 8;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSubTitle($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->sub_title = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string service_fee_msg = 9;</code>
     * @return string
     */
    public function getServiceFeeMsg()
    {
        return $this->service_fee_msg;
    }

    /**
     * Generated from protobuf field <code>string service_fee_msg = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setServiceFeeMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->service_fee_msg = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_info_desc = 10;</code>
     * @return string
     */
    public function getFeeInfoDesc()
    {
        return isset($this->fee_info_desc) ? $this->fee_info_desc : '';
    }

    public function hasFeeInfoDesc()
    {
        return isset($this->fee_info_desc);
    }

    public function clearFeeInfoDesc()
    {
        unset($this->fee_info_desc);
    }

    /**
     * Generated from protobuf field <code>string fee_info_desc = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeInfoDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_info_desc = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_desc_url = 11;</code>
     * @return string
     */
    public function getFeeDescUrl()
    {
        return isset($this->fee_desc_url) ? $this->fee_desc_url : '';
    }

    public function hasFeeDescUrl()
    {
        return isset($this->fee_desc_url);
    }

    public function clearFeeDescUrl()
    {
        unset($this->fee_desc_url);
    }

    /**
     * Generated from protobuf field <code>string fee_desc_url = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeDescUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_desc_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_msg = 12;</code>
     * @return string
     */
    public function getFeeMsg()
    {
        return $this->fee_msg;
    }

    /**
     * Generated from protobuf field <code>string fee_msg = 12;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 select_type = 14;</code>
     * @return int
     */
    public function getSelectType()
    {
        return isset($this->select_type) ? $this->select_type : 0;
    }

    public function hasSelectType()
    {
        return isset($this->select_type);
    }

    public function clearSelectType()
    {
        unset($this->select_type);
    }

    /**
     * Generated from protobuf field <code>int32 select_type = 14;</code>
     * @param int $var
     * @return $this
     */
    public function setSelectType($var)
    {
        GPBUtil::checkInt32($var);
        $this->select_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SideNewFormFeeDesc price_info_desc = 15;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getPriceInfoDesc()
    {
        return $this->price_info_desc;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SideNewFormFeeDesc price_info_desc = 15;</code>
     * @param \Dirpc\SDK\PreSale\SideNewFormFeeDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setPriceInfoDesc($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\SideNewFormFeeDesc::class);
        $this->price_info_desc = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>map<string, string> extra = 16;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getExtra()
    {
        return $this->extra;
    }

    /**
     * Generated from protobuf field <code>map<string, string> extra = 16;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setExtra($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->extra = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 type = 17;</code>
     * @return int
     */
    public function getType()
    {
        return isset($this->type) ? $this->type : 0;
    }

    public function hasType()
    {
        return isset($this->type);
    }

    public function clearType()
    {
        unset($this->type);
    }

    /**
     * Generated from protobuf field <code>int32 type = 17;</code>
     * @param int $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkInt32($var);
        $this->type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_amount = 18;</code>
     * @return string
     */
    public function getFeeAmount()
    {
        return isset($this->fee_amount) ? $this->fee_amount : '';
    }

    public function hasFeeAmount()
    {
        return isset($this->fee_amount);
    }

    public function clearFeeAmount()
    {
        unset($this->fee_amount);
    }

    /**
     * Generated from protobuf field <code>string fee_amount = 18;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeAmount($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_amount = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string car_fee_msg = 19;</code>
     * @return string
     */
    public function getCarFeeMsg()
    {
        return isset($this->car_fee_msg) ? $this->car_fee_msg : '';
    }

    public function hasCarFeeMsg()
    {
        return isset($this->car_fee_msg);
    }

    public function clearCarFeeMsg()
    {
        unset($this->car_fee_msg);
    }

    /**
     * Generated from protobuf field <code>string car_fee_msg = 19;</code>
     * @param string $var
     * @return $this
     */
    public function setCarFeeMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->car_fee_msg = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string car_fee_amount = 20;</code>
     * @return string
     */
    public function getCarFeeAmount()
    {
        return isset($this->car_fee_amount) ? $this->car_fee_amount : '';
    }

    public function hasCarFeeAmount()
    {
        return isset($this->car_fee_amount);
    }

    public function clearCarFeeAmount()
    {
        unset($this->car_fee_amount);
    }

    /**
     * Generated from protobuf field <code>string car_fee_amount = 20;</code>
     * @param string $var
     * @return $this
     */
    public function setCarFeeAmount($var)
    {
        GPBUtil::checkString($var, True);
        $this->car_fee_amount = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string combo_button_subtitle = 21;</code>
     * @return string
     */
    public function getComboButtonSubtitle()
    {
        return isset($this->combo_button_subtitle) ? $this->combo_button_subtitle : '';
    }

    public function hasComboButtonSubtitle()
    {
        return isset($this->combo_button_subtitle);
    }

    public function clearComboButtonSubtitle()
    {
        unset($this->combo_button_subtitle);
    }

    /**
     * Generated from protobuf field <code>string combo_button_subtitle = 21;</code>
     * @param string $var
     * @return $this
     */
    public function setComboButtonSubtitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->combo_button_subtitle = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_msg_template = 22;</code>
     * @return string
     */
    public function getFeeMsgTemplate()
    {
        return isset($this->fee_msg_template) ? $this->fee_msg_template : '';
    }

    public function hasFeeMsgTemplate()
    {
        return isset($this->fee_msg_template);
    }

    public function clearFeeMsgTemplate()
    {
        unset($this->fee_msg_template);
    }

    /**
     * Generated from protobuf field <code>string fee_msg_template = 22;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsgTemplate($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg_template = $var;

        return $this;
    }

}

