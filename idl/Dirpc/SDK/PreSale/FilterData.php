<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.FilterData</code>
 */
class FilterData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *索引到具体车型group
     *
     * Generated from protobuf field <code>string group_id = 1;</code>
     */
    protected $group_id = null;
    /**
     *盒子有时存在. 具体盒子内哪个车型被勾选
     *
     * Generated from protobuf field <code>repeated int32 selected_products = 2;</code>
     */
    private $selected_products;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $group_id
     *          索引到具体车型group
     *     @type int[]|\Nuwa\Protobuf\Internal\RepeatedField $selected_products
     *          盒子有时存在. 具体盒子内哪个车型被勾选
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *索引到具体车型group
     *
     * Generated from protobuf field <code>string group_id = 1;</code>
     * @return string
     */
    public function getGroupId()
    {
        return isset($this->group_id) ? $this->group_id : '';
    }

    public function hasGroupId()
    {
        return isset($this->group_id);
    }

    public function clearGroupId()
    {
        unset($this->group_id);
    }

    /**
     *索引到具体车型group
     *
     * Generated from protobuf field <code>string group_id = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setGroupId($var)
    {
        GPBUtil::checkString($var, True);
        $this->group_id = $var;

        return $this;
    }

    /**
     *盒子有时存在. 具体盒子内哪个车型被勾选
     *
     * Generated from protobuf field <code>repeated int32 selected_products = 2;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getSelectedProducts()
    {
        return $this->selected_products;
    }

    /**
     *盒子有时存在. 具体盒子内哪个车型被勾选
     *
     * Generated from protobuf field <code>repeated int32 selected_products = 2;</code>
     * @param int[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSelectedProducts($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::INT32);
        $this->selected_products = $arr;

        return $this;
    }

}

