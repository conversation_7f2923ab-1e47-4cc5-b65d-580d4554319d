<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.PageGuideResponseData</code>
 */
class PageGuideResponseData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *背景图(头部), 没有就用默认
     *
     * Generated from protobuf field <code>string bg_img = 1;</code>
     */
    protected $bg_img = null;
    /**
     *页面标题
     *
     * Generated from protobuf field <code>string page_title = 2;</code>
     */
    protected $page_title = null;
    /**
     *目标品类(页面)相关数据, 有导流时下发
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.GuideTarget target = 3;</code>
     */
    protected $target = null;
    /**
     *无导流品类时下发, 其他情况不下发
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NoGuideMsg no_guide = 4;</code>
     */
    protected $no_guide = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $bg_img
     *          背景图(头部), 没有就用默认
     *     @type string $page_title
     *          页面标题
     *     @type \Dirpc\SDK\PreSale\GuideTarget $target
     *          目标品类(页面)相关数据, 有导流时下发
     *     @type \Dirpc\SDK\PreSale\NoGuideMsg $no_guide
     *          无导流品类时下发, 其他情况不下发
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *背景图(头部), 没有就用默认
     *
     * Generated from protobuf field <code>string bg_img = 1;</code>
     * @return string
     */
    public function getBgImg()
    {
        return isset($this->bg_img) ? $this->bg_img : '';
    }

    public function hasBgImg()
    {
        return isset($this->bg_img);
    }

    public function clearBgImg()
    {
        unset($this->bg_img);
    }

    /**
     *背景图(头部), 没有就用默认
     *
     * Generated from protobuf field <code>string bg_img = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setBgImg($var)
    {
        GPBUtil::checkString($var, True);
        $this->bg_img = $var;

        return $this;
    }

    /**
     *页面标题
     *
     * Generated from protobuf field <code>string page_title = 2;</code>
     * @return string
     */
    public function getPageTitle()
    {
        return isset($this->page_title) ? $this->page_title : '';
    }

    public function hasPageTitle()
    {
        return isset($this->page_title);
    }

    public function clearPageTitle()
    {
        unset($this->page_title);
    }

    /**
     *页面标题
     *
     * Generated from protobuf field <code>string page_title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setPageTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->page_title = $var;

        return $this;
    }

    /**
     *目标品类(页面)相关数据, 有导流时下发
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.GuideTarget target = 3;</code>
     * @return \Dirpc\SDK\PreSale\GuideTarget
     */
    public function getTarget()
    {
        return isset($this->target) ? $this->target : null;
    }

    public function hasTarget()
    {
        return isset($this->target);
    }

    public function clearTarget()
    {
        unset($this->target);
    }

    /**
     *目标品类(页面)相关数据, 有导流时下发
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.GuideTarget target = 3;</code>
     * @param \Dirpc\SDK\PreSale\GuideTarget $var
     * @return $this
     */
    public function setTarget($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\GuideTarget::class);
        $this->target = $var;

        return $this;
    }

    /**
     *无导流品类时下发, 其他情况不下发
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NoGuideMsg no_guide = 4;</code>
     * @return \Dirpc\SDK\PreSale\NoGuideMsg
     */
    public function getNoGuide()
    {
        return isset($this->no_guide) ? $this->no_guide : null;
    }

    public function hasNoGuide()
    {
        return isset($this->no_guide);
    }

    public function clearNoGuide()
    {
        unset($this->no_guide);
    }

    /**
     *无导流品类时下发, 其他情况不下发
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NoGuideMsg no_guide = 4;</code>
     * @param \Dirpc\SDK\PreSale\NoGuideMsg $var
     * @return $this
     */
    public function setNoGuide($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NoGuideMsg::class);
        $this->no_guide = $var;

        return $this;
    }

}

