<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: carpool.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.ShareInfoV2</code>
 */
class ShareInfoV2 extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *小程序页面path
     *
     * Generated from protobuf field <code>string path = 1;</code>
     */
    protected $path = '';
    /**
     *微信卡片title
     *
     * Generated from protobuf field <code>string title = 2;</code>
     */
    protected $title = '';
    /**
     *按钮背景图
     *
     * Generated from protobuf field <code>string button_img_url = 3;</code>
     */
    protected $button_img_url = '';
    /**
     *卡片背景图
     *
     * Generated from protobuf field <code>string bg_img = 4;</code>
     */
    protected $bg_img = null;
    /**
     *按钮文案
     *
     * Generated from protobuf field <code>string button_text = 5;</code>
     */
    protected $button_text = null;
    /**
     *样式
     *
     * Generated from protobuf field <code>int32 style = 6;</code>
     */
    protected $style = null;
    /**
     *出发时间文案
     *
     * Generated from protobuf field <code>string departure_text = 7;</code>
     */
    protected $departure_text = null;
    /**
     *起点名称
     *
     * Generated from protobuf field <code>string start_name = 8;</code>
     */
    protected $start_name = null;
    /**
     *终点名称
     *
     * Generated from protobuf field <code>string dest_name = 9;</code>
     */
    protected $dest_name = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $path
     *          小程序页面path
     *     @type string $title
     *          微信卡片title
     *     @type string $button_img_url
     *          按钮背景图
     *     @type string $bg_img
     *          卡片背景图
     *     @type string $button_text
     *          按钮文案
     *     @type int $style
     *          样式
     *     @type string $departure_text
     *          出发时间文案
     *     @type string $start_name
     *          起点名称
     *     @type string $dest_name
     *          终点名称
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\Carpool::initOnce();
        parent::__construct($data);
    }

    /**
     *小程序页面path
     *
     * Generated from protobuf field <code>string path = 1;</code>
     * @return string
     */
    public function getPath()
    {
        return $this->path;
    }

    /**
     *小程序页面path
     *
     * Generated from protobuf field <code>string path = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setPath($var)
    {
        GPBUtil::checkString($var, True);
        $this->path = $var;

        return $this;
    }

    /**
     *微信卡片title
     *
     * Generated from protobuf field <code>string title = 2;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     *微信卡片title
     *
     * Generated from protobuf field <code>string title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     *按钮背景图
     *
     * Generated from protobuf field <code>string button_img_url = 3;</code>
     * @return string
     */
    public function getButtonImgUrl()
    {
        return $this->button_img_url;
    }

    /**
     *按钮背景图
     *
     * Generated from protobuf field <code>string button_img_url = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setButtonImgUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->button_img_url = $var;

        return $this;
    }

    /**
     *卡片背景图
     *
     * Generated from protobuf field <code>string bg_img = 4;</code>
     * @return string
     */
    public function getBgImg()
    {
        return isset($this->bg_img) ? $this->bg_img : '';
    }

    public function hasBgImg()
    {
        return isset($this->bg_img);
    }

    public function clearBgImg()
    {
        unset($this->bg_img);
    }

    /**
     *卡片背景图
     *
     * Generated from protobuf field <code>string bg_img = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setBgImg($var)
    {
        GPBUtil::checkString($var, True);
        $this->bg_img = $var;

        return $this;
    }

    /**
     *按钮文案
     *
     * Generated from protobuf field <code>string button_text = 5;</code>
     * @return string
     */
    public function getButtonText()
    {
        return isset($this->button_text) ? $this->button_text : '';
    }

    public function hasButtonText()
    {
        return isset($this->button_text);
    }

    public function clearButtonText()
    {
        unset($this->button_text);
    }

    /**
     *按钮文案
     *
     * Generated from protobuf field <code>string button_text = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setButtonText($var)
    {
        GPBUtil::checkString($var, True);
        $this->button_text = $var;

        return $this;
    }

    /**
     *样式
     *
     * Generated from protobuf field <code>int32 style = 6;</code>
     * @return int
     */
    public function getStyle()
    {
        return isset($this->style) ? $this->style : 0;
    }

    public function hasStyle()
    {
        return isset($this->style);
    }

    public function clearStyle()
    {
        unset($this->style);
    }

    /**
     *样式
     *
     * Generated from protobuf field <code>int32 style = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setStyle($var)
    {
        GPBUtil::checkInt32($var);
        $this->style = $var;

        return $this;
    }

    /**
     *出发时间文案
     *
     * Generated from protobuf field <code>string departure_text = 7;</code>
     * @return string
     */
    public function getDepartureText()
    {
        return isset($this->departure_text) ? $this->departure_text : '';
    }

    public function hasDepartureText()
    {
        return isset($this->departure_text);
    }

    public function clearDepartureText()
    {
        unset($this->departure_text);
    }

    /**
     *出发时间文案
     *
     * Generated from protobuf field <code>string departure_text = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setDepartureText($var)
    {
        GPBUtil::checkString($var, True);
        $this->departure_text = $var;

        return $this;
    }

    /**
     *起点名称
     *
     * Generated from protobuf field <code>string start_name = 8;</code>
     * @return string
     */
    public function getStartName()
    {
        return isset($this->start_name) ? $this->start_name : '';
    }

    public function hasStartName()
    {
        return isset($this->start_name);
    }

    public function clearStartName()
    {
        unset($this->start_name);
    }

    /**
     *起点名称
     *
     * Generated from protobuf field <code>string start_name = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setStartName($var)
    {
        GPBUtil::checkString($var, True);
        $this->start_name = $var;

        return $this;
    }

    /**
     *终点名称
     *
     * Generated from protobuf field <code>string dest_name = 9;</code>
     * @return string
     */
    public function getDestName()
    {
        return isset($this->dest_name) ? $this->dest_name : '';
    }

    public function hasDestName()
    {
        return isset($this->dest_name);
    }

    public function clearDestName()
    {
        unset($this->dest_name);
    }

    /**
     *终点名称
     *
     * Generated from protobuf field <code>string dest_name = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setDestName($var)
    {
        GPBUtil::checkString($var, True);
        $this->dest_name = $var;

        return $this;
    }

}

