<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.GetInvitationInfoRequest</code>
 */
class GetInvitationInfoRequest extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *邀约订单号
     *
     * Generated from protobuf field <code>string invite_oid = 1;</code>
     */
    protected $invite_oid = '';
    /**
     *邀约订单号
     *
     * Generated from protobuf field <code>string token = 2;</code>
     */
    protected $token = '';
    /**
     * Generated from protobuf field <code>string app_version = 3;</code>
     */
    protected $app_version = '';
    /**
     * Generated from protobuf field <code>int32 access_key_id = 4;</code>
     */
    protected $access_key_id = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $invite_oid
     *          邀约订单号
     *     @type string $token
     *          邀约订单号
     *     @type string $app_version
     *     @type int $access_key_id
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *邀约订单号
     *
     * Generated from protobuf field <code>string invite_oid = 1;</code>
     * @return string
     */
    public function getInviteOid()
    {
        return $this->invite_oid;
    }

    /**
     *邀约订单号
     *
     * Generated from protobuf field <code>string invite_oid = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setInviteOid($var)
    {
        GPBUtil::checkString($var, True);
        $this->invite_oid = $var;

        return $this;
    }

    /**
     *邀约订单号
     *
     * Generated from protobuf field <code>string token = 2;</code>
     * @return string
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     *邀约订单号
     *
     * Generated from protobuf field <code>string token = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->token = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string app_version = 3;</code>
     * @return string
     */
    public function getAppVersion()
    {
        return $this->app_version;
    }

    /**
     * Generated from protobuf field <code>string app_version = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setAppVersion($var)
    {
        GPBUtil::checkString($var, True);
        $this->app_version = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 access_key_id = 4;</code>
     * @return int
     */
    public function getAccessKeyId()
    {
        return isset($this->access_key_id) ? $this->access_key_id : 0;
    }

    public function hasAccessKeyId()
    {
        return isset($this->access_key_id);
    }

    public function clearAccessKeyId()
    {
        unset($this->access_key_id);
    }

    /**
     * Generated from protobuf field <code>int32 access_key_id = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setAccessKeyId($var)
    {
        GPBUtil::checkInt32($var);
        $this->access_key_id = $var;

        return $this;
    }

}

