<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.CapEstimateData</code>
 */
class CapEstimateData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *品牌文案
     *
     * Generated from protobuf field <code>string intro_msg = 1;</code>
     */
    protected $intro_msg = '';
    /**
     *价格信息
     *
     * Generated from protobuf field <code>string fee_msg = 2;</code>
     */
    protected $fee_msg = '';
    /**
     *车型
     *
     * Generated from protobuf field <code>int32 require_level = 3;</code>
     */
    protected $require_level = 0;
    /**
     *业务线
     *
     * Generated from protobuf field <code>int32 business_id = 4;</code>
     */
    protected $business_id = 0;
    /**
     *产品线ID
     *
     * Generated from protobuf field <code>int32 product_id = 5;</code>
     */
    protected $product_id = 0;
    /**
     *combo_type
     *
     * Generated from protobuf field <code>int32 combo_type = 6;</code>
     */
    protected $combo_type = 0;
    /**
     *预估id
     *
     * Generated from protobuf field <code>string estimate_id = 8;</code>
     */
    protected $estimate_id = '';
    /**
     *分组id
     *
     * Generated from protobuf field <code>int32 category_id = 12;</code>
     */
    protected $category_id = 0;
    /**
     *当前预估在表单是否被选中 0-未选中 1-被选中
     *
     * Generated from protobuf field <code>int32 select_type = 13;</code>
     */
    protected $select_type = 0;
    /**
     *当前拼车的座位数
     *
     * Generated from protobuf field <code>int32 seat_num = 16;</code>
     */
    protected $seat_num = null;
    /**
     *价格
     *
     * Generated from protobuf field <code>string fee_amount = 17;</code>
     */
    protected $fee_amount = '';
    /**
     *路线ID
     *
     * Generated from protobuf field <code>repeated string route_id_list = 18;</code>
     */
    private $route_id_list;
    /**
     *聚合产品
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.AggregationCapEstimateData sub_product = 19;</code>
     */
    private $sub_product;
    /**
     *冒泡提示
     *
     * Generated from protobuf field <code>string bubble_tip = 20;</code>
     */
    protected $bubble_tip = null;
    /**
     *价格展示（复用新表单）
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 25;</code>
     */
    private $fee_desc_list;
    /**
     *单品类多价格展示 (复用新表单)
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormMultiPrice multi_price_list = 26;</code>
     */
    private $multi_price_list;
    /**
     *服务费
     *
     * Generated from protobuf field <code>string extra_price_desc = 31;</code>
     */
    protected $extra_price_desc = '';
    /**
     *发单参数
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.CapExtraMap extra_map = 28;</code>
     */
    protected $extra_map = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormUserPayInfo user_pay_info = 27;</code>
     */
    protected $user_pay_info = null;
    /**
     *样式2 多一层嵌套 的 不拼车聚合
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.MultiAggregationCapEstimateData multi_product = 23;</code>
     */
    private $multi_product;
    /**
     *样式2 多一层嵌套 的 不拼车聚合
     *
     * Generated from protobuf field <code>int32 product_category = 35;</code>
     */
    protected $product_category = 0;
    /**
     * Generated from protobuf field <code>int32 count_price_type = 22;</code>
     */
    protected $count_price_type = 0;
    /**
     * Generated from protobuf field <code>int32 level_type = 33;</code>
     */
    protected $level_type = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $intro_msg
     *          品牌文案
     *     @type string $fee_msg
     *          价格信息
     *     @type int $require_level
     *          车型
     *     @type int $business_id
     *          业务线
     *     @type int $product_id
     *          产品线ID
     *     @type int $combo_type
     *          combo_type
     *     @type string $estimate_id
     *          预估id
     *     @type int $category_id
     *          分组id
     *     @type int $select_type
     *          当前预估在表单是否被选中 0-未选中 1-被选中
     *     @type int $seat_num
     *          当前拼车的座位数
     *     @type string $fee_amount
     *          价格
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $route_id_list
     *          路线ID
     *     @type \Dirpc\SDK\PreSale\AggregationCapEstimateData[]|\Nuwa\Protobuf\Internal\RepeatedField $sub_product
     *          聚合产品
     *     @type string $bubble_tip
     *          冒泡提示
     *     @type \Dirpc\SDK\PreSale\NewFormFeeDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $fee_desc_list
     *          价格展示（复用新表单）
     *     @type \Dirpc\SDK\PreSale\NewFormMultiPrice[]|\Nuwa\Protobuf\Internal\RepeatedField $multi_price_list
     *          单品类多价格展示 (复用新表单)
     *     @type string $extra_price_desc
     *          服务费
     *     @type \Dirpc\SDK\PreSale\CapExtraMap $extra_map
     *          发单参数
     *     @type \Dirpc\SDK\PreSale\NewFormUserPayInfo $user_pay_info
     *     @type \Dirpc\SDK\PreSale\MultiAggregationCapEstimateData[]|\Nuwa\Protobuf\Internal\RepeatedField $multi_product
     *          样式2 多一层嵌套 的 不拼车聚合
     *     @type int $product_category
     *          样式2 多一层嵌套 的 不拼车聚合
     *     @type int $count_price_type
     *     @type int $level_type
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *品牌文案
     *
     * Generated from protobuf field <code>string intro_msg = 1;</code>
     * @return string
     */
    public function getIntroMsg()
    {
        return $this->intro_msg;
    }

    /**
     *品牌文案
     *
     * Generated from protobuf field <code>string intro_msg = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setIntroMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->intro_msg = $var;

        return $this;
    }

    /**
     *价格信息
     *
     * Generated from protobuf field <code>string fee_msg = 2;</code>
     * @return string
     */
    public function getFeeMsg()
    {
        return $this->fee_msg;
    }

    /**
     *价格信息
     *
     * Generated from protobuf field <code>string fee_msg = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg = $var;

        return $this;
    }

    /**
     *车型
     *
     * Generated from protobuf field <code>int32 require_level = 3;</code>
     * @return int
     */
    public function getRequireLevel()
    {
        return $this->require_level;
    }

    /**
     *车型
     *
     * Generated from protobuf field <code>int32 require_level = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setRequireLevel($var)
    {
        GPBUtil::checkInt32($var);
        $this->require_level = $var;

        return $this;
    }

    /**
     *业务线
     *
     * Generated from protobuf field <code>int32 business_id = 4;</code>
     * @return int
     */
    public function getBusinessId()
    {
        return $this->business_id;
    }

    /**
     *业务线
     *
     * Generated from protobuf field <code>int32 business_id = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setBusinessId($var)
    {
        GPBUtil::checkInt32($var);
        $this->business_id = $var;

        return $this;
    }

    /**
     *产品线ID
     *
     * Generated from protobuf field <code>int32 product_id = 5;</code>
     * @return int
     */
    public function getProductId()
    {
        return $this->product_id;
    }

    /**
     *产品线ID
     *
     * Generated from protobuf field <code>int32 product_id = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setProductId($var)
    {
        GPBUtil::checkInt32($var);
        $this->product_id = $var;

        return $this;
    }

    /**
     *combo_type
     *
     * Generated from protobuf field <code>int32 combo_type = 6;</code>
     * @return int
     */
    public function getComboType()
    {
        return $this->combo_type;
    }

    /**
     *combo_type
     *
     * Generated from protobuf field <code>int32 combo_type = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setComboType($var)
    {
        GPBUtil::checkInt32($var);
        $this->combo_type = $var;

        return $this;
    }

    /**
     *预估id
     *
     * Generated from protobuf field <code>string estimate_id = 8;</code>
     * @return string
     */
    public function getEstimateId()
    {
        return $this->estimate_id;
    }

    /**
     *预估id
     *
     * Generated from protobuf field <code>string estimate_id = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateId($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_id = $var;

        return $this;
    }

    /**
     *分组id
     *
     * Generated from protobuf field <code>int32 category_id = 12;</code>
     * @return int
     */
    public function getCategoryId()
    {
        return $this->category_id;
    }

    /**
     *分组id
     *
     * Generated from protobuf field <code>int32 category_id = 12;</code>
     * @param int $var
     * @return $this
     */
    public function setCategoryId($var)
    {
        GPBUtil::checkInt32($var);
        $this->category_id = $var;

        return $this;
    }

    /**
     *当前预估在表单是否被选中 0-未选中 1-被选中
     *
     * Generated from protobuf field <code>int32 select_type = 13;</code>
     * @return int
     */
    public function getSelectType()
    {
        return $this->select_type;
    }

    /**
     *当前预估在表单是否被选中 0-未选中 1-被选中
     *
     * Generated from protobuf field <code>int32 select_type = 13;</code>
     * @param int $var
     * @return $this
     */
    public function setSelectType($var)
    {
        GPBUtil::checkInt32($var);
        $this->select_type = $var;

        return $this;
    }

    /**
     *当前拼车的座位数
     *
     * Generated from protobuf field <code>int32 seat_num = 16;</code>
     * @return int
     */
    public function getSeatNum()
    {
        return isset($this->seat_num) ? $this->seat_num : 0;
    }

    public function hasSeatNum()
    {
        return isset($this->seat_num);
    }

    public function clearSeatNum()
    {
        unset($this->seat_num);
    }

    /**
     *当前拼车的座位数
     *
     * Generated from protobuf field <code>int32 seat_num = 16;</code>
     * @param int $var
     * @return $this
     */
    public function setSeatNum($var)
    {
        GPBUtil::checkInt32($var);
        $this->seat_num = $var;

        return $this;
    }

    /**
     *价格
     *
     * Generated from protobuf field <code>string fee_amount = 17;</code>
     * @return string
     */
    public function getFeeAmount()
    {
        return $this->fee_amount;
    }

    /**
     *价格
     *
     * Generated from protobuf field <code>string fee_amount = 17;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeAmount($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_amount = $var;

        return $this;
    }

    /**
     *路线ID
     *
     * Generated from protobuf field <code>repeated string route_id_list = 18;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getRouteIdList()
    {
        return $this->route_id_list;
    }

    /**
     *路线ID
     *
     * Generated from protobuf field <code>repeated string route_id_list = 18;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setRouteIdList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->route_id_list = $arr;

        return $this;
    }

    /**
     *聚合产品
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.AggregationCapEstimateData sub_product = 19;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getSubProduct()
    {
        return $this->sub_product;
    }

    /**
     *聚合产品
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.AggregationCapEstimateData sub_product = 19;</code>
     * @param \Dirpc\SDK\PreSale\AggregationCapEstimateData[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSubProduct($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\AggregationCapEstimateData::class);
        $this->sub_product = $arr;

        return $this;
    }

    /**
     *冒泡提示
     *
     * Generated from protobuf field <code>string bubble_tip = 20;</code>
     * @return string
     */
    public function getBubbleTip()
    {
        return isset($this->bubble_tip) ? $this->bubble_tip : '';
    }

    public function hasBubbleTip()
    {
        return isset($this->bubble_tip);
    }

    public function clearBubbleTip()
    {
        unset($this->bubble_tip);
    }

    /**
     *冒泡提示
     *
     * Generated from protobuf field <code>string bubble_tip = 20;</code>
     * @param string $var
     * @return $this
     */
    public function setBubbleTip($var)
    {
        GPBUtil::checkString($var, True);
        $this->bubble_tip = $var;

        return $this;
    }

    /**
     *价格展示（复用新表单）
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 25;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getFeeDescList()
    {
        return $this->fee_desc_list;
    }

    /**
     *价格展示（复用新表单）
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 25;</code>
     * @param \Dirpc\SDK\PreSale\NewFormFeeDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFeeDescList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\NewFormFeeDesc::class);
        $this->fee_desc_list = $arr;

        return $this;
    }

    /**
     *单品类多价格展示 (复用新表单)
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormMultiPrice multi_price_list = 26;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getMultiPriceList()
    {
        return $this->multi_price_list;
    }

    /**
     *单品类多价格展示 (复用新表单)
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormMultiPrice multi_price_list = 26;</code>
     * @param \Dirpc\SDK\PreSale\NewFormMultiPrice[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setMultiPriceList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\NewFormMultiPrice::class);
        $this->multi_price_list = $arr;

        return $this;
    }

    /**
     *服务费
     *
     * Generated from protobuf field <code>string extra_price_desc = 31;</code>
     * @return string
     */
    public function getExtraPriceDesc()
    {
        return $this->extra_price_desc;
    }

    /**
     *服务费
     *
     * Generated from protobuf field <code>string extra_price_desc = 31;</code>
     * @param string $var
     * @return $this
     */
    public function setExtraPriceDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->extra_price_desc = $var;

        return $this;
    }

    /**
     *发单参数
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.CapExtraMap extra_map = 28;</code>
     * @return \Dirpc\SDK\PreSale\CapExtraMap
     */
    public function getExtraMap()
    {
        return isset($this->extra_map) ? $this->extra_map : null;
    }

    public function hasExtraMap()
    {
        return isset($this->extra_map);
    }

    public function clearExtraMap()
    {
        unset($this->extra_map);
    }

    /**
     *发单参数
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.CapExtraMap extra_map = 28;</code>
     * @param \Dirpc\SDK\PreSale\CapExtraMap $var
     * @return $this
     */
    public function setExtraMap($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\CapExtraMap::class);
        $this->extra_map = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormUserPayInfo user_pay_info = 27;</code>
     * @return \Dirpc\SDK\PreSale\NewFormUserPayInfo
     */
    public function getUserPayInfo()
    {
        return isset($this->user_pay_info) ? $this->user_pay_info : null;
    }

    public function hasUserPayInfo()
    {
        return isset($this->user_pay_info);
    }

    public function clearUserPayInfo()
    {
        unset($this->user_pay_info);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormUserPayInfo user_pay_info = 27;</code>
     * @param \Dirpc\SDK\PreSale\NewFormUserPayInfo $var
     * @return $this
     */
    public function setUserPayInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NewFormUserPayInfo::class);
        $this->user_pay_info = $var;

        return $this;
    }

    /**
     *样式2 多一层嵌套 的 不拼车聚合
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.MultiAggregationCapEstimateData multi_product = 23;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getMultiProduct()
    {
        return $this->multi_product;
    }

    /**
     *样式2 多一层嵌套 的 不拼车聚合
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.MultiAggregationCapEstimateData multi_product = 23;</code>
     * @param \Dirpc\SDK\PreSale\MultiAggregationCapEstimateData[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setMultiProduct($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\MultiAggregationCapEstimateData::class);
        $this->multi_product = $arr;

        return $this;
    }

    /**
     *样式2 多一层嵌套 的 不拼车聚合
     *
     * Generated from protobuf field <code>int32 product_category = 35;</code>
     * @return int
     */
    public function getProductCategory()
    {
        return $this->product_category;
    }

    /**
     *样式2 多一层嵌套 的 不拼车聚合
     *
     * Generated from protobuf field <code>int32 product_category = 35;</code>
     * @param int $var
     * @return $this
     */
    public function setProductCategory($var)
    {
        GPBUtil::checkInt32($var);
        $this->product_category = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 count_price_type = 22;</code>
     * @return int
     */
    public function getCountPriceType()
    {
        return $this->count_price_type;
    }

    /**
     * Generated from protobuf field <code>int32 count_price_type = 22;</code>
     * @param int $var
     * @return $this
     */
    public function setCountPriceType($var)
    {
        GPBUtil::checkInt32($var);
        $this->count_price_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 level_type = 33;</code>
     * @return int
     */
    public function getLevelType()
    {
        return $this->level_type;
    }

    /**
     * Generated from protobuf field <code>int32 level_type = 33;</code>
     * @param int $var
     * @return $this
     */
    public function setLevelType($var)
    {
        GPBUtil::checkInt32($var);
        $this->level_type = $var;

        return $this;
    }

}

