<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.IntercityMatchShowSkuData</code>
 */
class IntercityMatchShowSkuData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string bottom_text = 1;</code>
     */
    protected $bottom_text = '';
    /**
     * Generated from protobuf field <code>string signal_text = 2;</code>
     */
    protected $signal_text = '';
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ShowSkuTimeSpanRange time_span = 3;</code>
     */
    private $time_span;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $bottom_text
     *     @type string $signal_text
     *     @type \Dirpc\SDK\PreSale\ShowSkuTimeSpanRange[]|\Nuwa\Protobuf\Internal\RepeatedField $time_span
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string bottom_text = 1;</code>
     * @return string
     */
    public function getBottomText()
    {
        return $this->bottom_text;
    }

    /**
     * Generated from protobuf field <code>string bottom_text = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setBottomText($var)
    {
        GPBUtil::checkString($var, True);
        $this->bottom_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string signal_text = 2;</code>
     * @return string
     */
    public function getSignalText()
    {
        return $this->signal_text;
    }

    /**
     * Generated from protobuf field <code>string signal_text = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setSignalText($var)
    {
        GPBUtil::checkString($var, True);
        $this->signal_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ShowSkuTimeSpanRange time_span = 3;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getTimeSpan()
    {
        return $this->time_span;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ShowSkuTimeSpanRange time_span = 3;</code>
     * @param \Dirpc\SDK\PreSale\ShowSkuTimeSpanRange[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setTimeSpan($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\ShowSkuTimeSpanRange::class);
        $this->time_span = $arr;

        return $this;
    }

}

