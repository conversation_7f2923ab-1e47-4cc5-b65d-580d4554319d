<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.TabExtraData</code>
 */
class TabExtraData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.Classify classify = 1;</code>
     */
    protected $classify = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\Classify $classify
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.Classify classify = 1;</code>
     * @return \Dirpc\SDK\PreSale\Classify
     */
    public function getClassify()
    {
        return isset($this->classify) ? $this->classify : null;
    }

    public function hasClassify()
    {
        return isset($this->classify);
    }

    public function clearClassify()
    {
        unset($this->classify);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.Classify classify = 1;</code>
     * @param \Dirpc\SDK\PreSale\Classify $var
     * @return $this
     */
    public function setClassify($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\Classify::class);
        $this->classify = $var;

        return $this;
    }

}

