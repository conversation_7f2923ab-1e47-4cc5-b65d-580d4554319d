<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.OptionService</code>
 */
class OptionService extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 option_id = 1;</code>
     */
    protected $option_id = 0;
    /**
     * Generated from protobuf field <code>string title = 2;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>string desc = 3;</code>
     */
    protected $desc = '';
    /**
     * Generated from protobuf field <code>int32 selected_count = 4;</code>
     */
    protected $selected_count = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $option_id
     *     @type string $title
     *     @type string $desc
     *     @type int $selected_count
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 option_id = 1;</code>
     * @return int
     */
    public function getOptionId()
    {
        return $this->option_id;
    }

    /**
     * Generated from protobuf field <code>int32 option_id = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setOptionId($var)
    {
        GPBUtil::checkInt32($var);
        $this->option_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string title = 2;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string desc = 3;</code>
     * @return string
     */
    public function getDesc()
    {
        return $this->desc;
    }

    /**
     * Generated from protobuf field <code>string desc = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->desc = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 selected_count = 4;</code>
     * @return int
     */
    public function getSelectedCount()
    {
        return $this->selected_count;
    }

    /**
     * Generated from protobuf field <code>int32 selected_count = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setSelectedCount($var)
    {
        GPBUtil::checkInt32($var);
        $this->selected_count = $var;

        return $this;
    }

}

