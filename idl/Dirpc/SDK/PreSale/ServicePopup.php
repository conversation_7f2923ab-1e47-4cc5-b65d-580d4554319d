<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.ServicePopup</code>
 */
class ServicePopup extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string button_detail = 1;</code>
     */
    protected $button_detail = null;
    /**
     * Generated from protobuf field <code>string button_ok = 2;</code>
     */
    protected $button_ok = null;
    /**
     * Generated from protobuf field <code>string detail_link = 3;</code>
     */
    protected $detail_link = null;
    /**
     * Generated from protobuf field <code>string note_content = 4;</code>
     */
    protected $note_content = null;
    /**
     * Generated from protobuf field <code>int32 service_id = 5;</code>
     */
    protected $service_id = null;
    /**
     * Generated from protobuf field <code>int32 product_id = 6;</code>
     */
    protected $product_id = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $button_detail
     *     @type string $button_ok
     *     @type string $detail_link
     *     @type string $note_content
     *     @type int $service_id
     *     @type int $product_id
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string button_detail = 1;</code>
     * @return string
     */
    public function getButtonDetail()
    {
        return isset($this->button_detail) ? $this->button_detail : '';
    }

    public function hasButtonDetail()
    {
        return isset($this->button_detail);
    }

    public function clearButtonDetail()
    {
        unset($this->button_detail);
    }

    /**
     * Generated from protobuf field <code>string button_detail = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setButtonDetail($var)
    {
        GPBUtil::checkString($var, True);
        $this->button_detail = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string button_ok = 2;</code>
     * @return string
     */
    public function getButtonOk()
    {
        return isset($this->button_ok) ? $this->button_ok : '';
    }

    public function hasButtonOk()
    {
        return isset($this->button_ok);
    }

    public function clearButtonOk()
    {
        unset($this->button_ok);
    }

    /**
     * Generated from protobuf field <code>string button_ok = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setButtonOk($var)
    {
        GPBUtil::checkString($var, True);
        $this->button_ok = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string detail_link = 3;</code>
     * @return string
     */
    public function getDetailLink()
    {
        return isset($this->detail_link) ? $this->detail_link : '';
    }

    public function hasDetailLink()
    {
        return isset($this->detail_link);
    }

    public function clearDetailLink()
    {
        unset($this->detail_link);
    }

    /**
     * Generated from protobuf field <code>string detail_link = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setDetailLink($var)
    {
        GPBUtil::checkString($var, True);
        $this->detail_link = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string note_content = 4;</code>
     * @return string
     */
    public function getNoteContent()
    {
        return isset($this->note_content) ? $this->note_content : '';
    }

    public function hasNoteContent()
    {
        return isset($this->note_content);
    }

    public function clearNoteContent()
    {
        unset($this->note_content);
    }

    /**
     * Generated from protobuf field <code>string note_content = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setNoteContent($var)
    {
        GPBUtil::checkString($var, True);
        $this->note_content = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 service_id = 5;</code>
     * @return int
     */
    public function getServiceId()
    {
        return isset($this->service_id) ? $this->service_id : 0;
    }

    public function hasServiceId()
    {
        return isset($this->service_id);
    }

    public function clearServiceId()
    {
        unset($this->service_id);
    }

    /**
     * Generated from protobuf field <code>int32 service_id = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setServiceId($var)
    {
        GPBUtil::checkInt32($var);
        $this->service_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 product_id = 6;</code>
     * @return int
     */
    public function getProductId()
    {
        return $this->product_id;
    }

    /**
     * Generated from protobuf field <code>int32 product_id = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setProductId($var)
    {
        GPBUtil::checkInt32($var);
        $this->product_id = $var;

        return $this;
    }

}

