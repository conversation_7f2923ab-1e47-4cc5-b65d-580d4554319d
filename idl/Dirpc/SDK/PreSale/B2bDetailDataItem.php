<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.B2bDetailDataItem</code>
 */
class B2bDetailDataItem extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *预估ID
     *
     * Generated from protobuf field <code>string estimate_id = 1;</code>
     */
    protected $estimate_id = '';
    /**
     *动调相关信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.B2bDynamicInfo dynamic_info = 2;</code>
     */
    protected $dynamic_info = null;
    /**
     *特殊费用
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.B2bSpecialFee special_fee = 3;</code>
     */
    protected $special_fee = null;
    /**
     *会员相关信息(动调使用)
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.B2bDynamicMemberInfo member_info = 4;</code>
     */
    protected $member_info = null;
    /**
     *费用详情 json 透传账单费用详情，请自行解析 http://promise.intra.xiaojukeji.com/promise#/chibi/doc?id=getFeeDetailInfos_5ff45253ae794a560a7ac7c2&parent_id=5ff45253ae794a560a7ac7c2&type=file&key=getFeeDetailInfos
     *
     * Generated from protobuf field <code>string fee_detail = 5;</code>
     */
    protected $fee_detail = null;
    /**
     *请求计价规则接口需要的token
     *
     * Generated from protobuf field <code>string price_token = 6;</code>
     */
    protected $price_token = null;
    /**
     *用于预约单专车计价规则获取
     *
     * Generated from protobuf field <code>string booking_rules = 7;</code>
     */
    protected $booking_rules = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $estimate_id
     *          预估ID
     *     @type \Dirpc\SDK\PreSale\B2bDynamicInfo $dynamic_info
     *          动调相关信息
     *     @type \Dirpc\SDK\PreSale\B2bSpecialFee $special_fee
     *          特殊费用
     *     @type \Dirpc\SDK\PreSale\B2bDynamicMemberInfo $member_info
     *          会员相关信息(动调使用)
     *     @type string $fee_detail
     *          费用详情 json 透传账单费用详情，请自行解析 http://promise.intra.xiaojukeji.com/promise#/chibi/doc?id=getFeeDetailInfos_5ff45253ae794a560a7ac7c2&parent_id=5ff45253ae794a560a7ac7c2&type=file&key=getFeeDetailInfos
     *     @type string $price_token
     *          请求计价规则接口需要的token
     *     @type string $booking_rules
     *          用于预约单专车计价规则获取
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *预估ID
     *
     * Generated from protobuf field <code>string estimate_id = 1;</code>
     * @return string
     */
    public function getEstimateId()
    {
        return $this->estimate_id;
    }

    /**
     *预估ID
     *
     * Generated from protobuf field <code>string estimate_id = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateId($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_id = $var;

        return $this;
    }

    /**
     *动调相关信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.B2bDynamicInfo dynamic_info = 2;</code>
     * @return \Dirpc\SDK\PreSale\B2bDynamicInfo
     */
    public function getDynamicInfo()
    {
        return isset($this->dynamic_info) ? $this->dynamic_info : null;
    }

    public function hasDynamicInfo()
    {
        return isset($this->dynamic_info);
    }

    public function clearDynamicInfo()
    {
        unset($this->dynamic_info);
    }

    /**
     *动调相关信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.B2bDynamicInfo dynamic_info = 2;</code>
     * @param \Dirpc\SDK\PreSale\B2bDynamicInfo $var
     * @return $this
     */
    public function setDynamicInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\B2bDynamicInfo::class);
        $this->dynamic_info = $var;

        return $this;
    }

    /**
     *特殊费用
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.B2bSpecialFee special_fee = 3;</code>
     * @return \Dirpc\SDK\PreSale\B2bSpecialFee
     */
    public function getSpecialFee()
    {
        return isset($this->special_fee) ? $this->special_fee : null;
    }

    public function hasSpecialFee()
    {
        return isset($this->special_fee);
    }

    public function clearSpecialFee()
    {
        unset($this->special_fee);
    }

    /**
     *特殊费用
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.B2bSpecialFee special_fee = 3;</code>
     * @param \Dirpc\SDK\PreSale\B2bSpecialFee $var
     * @return $this
     */
    public function setSpecialFee($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\B2bSpecialFee::class);
        $this->special_fee = $var;

        return $this;
    }

    /**
     *会员相关信息(动调使用)
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.B2bDynamicMemberInfo member_info = 4;</code>
     * @return \Dirpc\SDK\PreSale\B2bDynamicMemberInfo
     */
    public function getMemberInfo()
    {
        return isset($this->member_info) ? $this->member_info : null;
    }

    public function hasMemberInfo()
    {
        return isset($this->member_info);
    }

    public function clearMemberInfo()
    {
        unset($this->member_info);
    }

    /**
     *会员相关信息(动调使用)
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.B2bDynamicMemberInfo member_info = 4;</code>
     * @param \Dirpc\SDK\PreSale\B2bDynamicMemberInfo $var
     * @return $this
     */
    public function setMemberInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\B2bDynamicMemberInfo::class);
        $this->member_info = $var;

        return $this;
    }

    /**
     *费用详情 json 透传账单费用详情，请自行解析 http://promise.intra.xiaojukeji.com/promise#/chibi/doc?id=getFeeDetailInfos_5ff45253ae794a560a7ac7c2&parent_id=5ff45253ae794a560a7ac7c2&type=file&key=getFeeDetailInfos
     *
     * Generated from protobuf field <code>string fee_detail = 5;</code>
     * @return string
     */
    public function getFeeDetail()
    {
        return isset($this->fee_detail) ? $this->fee_detail : '';
    }

    public function hasFeeDetail()
    {
        return isset($this->fee_detail);
    }

    public function clearFeeDetail()
    {
        unset($this->fee_detail);
    }

    /**
     *费用详情 json 透传账单费用详情，请自行解析 http://promise.intra.xiaojukeji.com/promise#/chibi/doc?id=getFeeDetailInfos_5ff45253ae794a560a7ac7c2&parent_id=5ff45253ae794a560a7ac7c2&type=file&key=getFeeDetailInfos
     *
     * Generated from protobuf field <code>string fee_detail = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeDetail($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_detail = $var;

        return $this;
    }

    /**
     *请求计价规则接口需要的token
     *
     * Generated from protobuf field <code>string price_token = 6;</code>
     * @return string
     */
    public function getPriceToken()
    {
        return isset($this->price_token) ? $this->price_token : '';
    }

    public function hasPriceToken()
    {
        return isset($this->price_token);
    }

    public function clearPriceToken()
    {
        unset($this->price_token);
    }

    /**
     *请求计价规则接口需要的token
     *
     * Generated from protobuf field <code>string price_token = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setPriceToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->price_token = $var;

        return $this;
    }

    /**
     *用于预约单专车计价规则获取
     *
     * Generated from protobuf field <code>string booking_rules = 7;</code>
     * @return string
     */
    public function getBookingRules()
    {
        return isset($this->booking_rules) ? $this->booking_rules : '';
    }

    public function hasBookingRules()
    {
        return isset($this->booking_rules);
    }

    public function clearBookingRules()
    {
        unset($this->booking_rules);
    }

    /**
     *用于预约单专车计价规则获取
     *
     * Generated from protobuf field <code>string booking_rules = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setBookingRules($var)
    {
        GPBUtil::checkString($var, True);
        $this->booking_rules = $var;

        return $this;
    }

}

