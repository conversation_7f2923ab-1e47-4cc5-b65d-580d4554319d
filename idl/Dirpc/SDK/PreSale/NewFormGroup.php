<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NewFormGroup</code>
 */
class NewFormGroup extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *1单车型｜车大，2三方盒子，3出租车盒子，4高峰盒子，99导流, 101 司乘议价
     *
     * Generated from protobuf field <code>int32 type = 1;</code>
     */
    protected $type = null;
    /**
     *1单车型｜车大，2三方盒子，3出租车盒子，4高峰盒子，99导流, 101 司乘议价
     *
     * Generated from protobuf field <code>repeated string products = 2;</code>
     */
    private $products;
    /**
     * Generated from protobuf field <code>int32 is_selected = 4;</code>
     */
    protected $is_selected = null;
    /**
     * Generated from protobuf field <code>string car_icon = 5;</code>
     */
    protected $car_icon = null;
    /**
     * Generated from protobuf field <code>string car_title = 6;</code>
     */
    protected $car_title = null;
    /**
     * Generated from protobuf field <code>string popup_title = 7;</code>
     */
    protected $popup_title = null;
    /**
     * Generated from protobuf field <code>string popup_sub_title = 8;</code>
     */
    protected $popup_sub_title = null;
    /**
     *当用户没有选择出租车的时候，出租车盒子需要外露的一个单独的车型
     *
     * Generated from protobuf field <code>string recommend_product = 9;</code>
     */
    protected $recommend_product = null;
    /**
     *车大
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormLinkInfo link_info = 11;</code>
     */
    protected $link_info = null;
    /**
     *导流地址
     *
     * Generated from protobuf field <code>string guide_path = 12;</code>
     */
    protected $guide_path = null;
    /**
     *导流按钮文案
     *
     * Generated from protobuf field <code>string button_text = 13;</code>
     */
    protected $button_text = null;
    /**
     *导流按钮文案
     *
     * Generated from protobuf field <code>int32 box_id = 14;</code>
     */
    protected $box_id = null;
    /**
     * Generated from protobuf field <code>string box_desc = 15;</code>
     */
    protected $box_desc = null;
    /**
     * Generated from protobuf field <code>string fee_desc = 16;</code>
     */
    protected $fee_desc = null;
    /**
     * Generated from protobuf field <code>string fee_desc_icon = 17;</code>
     */
    protected $fee_desc_icon = null;
    /**
     * Generated from protobuf field <code>int32 jump_type = 18;</code>
     */
    protected $jump_type = null;
    /**
     *group主键id 规则: type + boxid||product_category||subgroup_id
     *
     * Generated from protobuf field <code>string group_id = 20;</code>
     */
    protected $group_id = null;
    /**
     *盒子的标签
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.GroupSubTitle sub_title = 21;</code>
     */
    protected $sub_title = null;
    /**
     *按钮样式，不设置默认原样式
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ButtonStyle button_style = 22;</code>
     */
    protected $button_style = null;
    /**
     *司乘议价砍价信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormBargainMsg bargain_msg = 23;</code>
     */
    protected $bargain_msg = null;
    /**
     *盒子外层费用描述
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 24;</code>
     */
    private $fee_desc_list;
    /**
     *盒子外层费用描述
     *
     * Generated from protobuf field <code>string popup_toast = 25;</code>
     */
    protected $popup_toast = null;
    /**
     * Generated from protobuf field <code>int32 action_type = 26;</code>
     */
    protected $action_type = null;
    /**
     * Generated from protobuf field <code>int32 guide_style = 27;</code>
     */
    protected $guide_style = null;
    /**
     *司乘议价弹框信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.bargainPopup bargain_popup = 28;</code>
     */
    protected $bargain_popup = null;
    /**
     *盒子外层费用
     *
     * Generated from protobuf field <code>string fee_msg = 29;</code>
     */
    protected $fee_msg = null;
    /**
     *盒子外层费用
     *
     * Generated from protobuf field <code>string fee_msg_template = 39;</code>
     */
    protected $fee_msg_template = null;
    /**
     * Generated from protobuf field <code>string fee_detail_url = 30;</code>
     */
    protected $fee_detail_url = null;
    /**
     *字体样式 0 正常 1 大字
     *
     * Generated from protobuf field <code>int32 style_type = 31;</code>
     */
    protected $style_type = null;
    /**
     *1 关闭阴影
     *
     * Generated from protobuf field <code>int32 disable_shadow = 32;</code>
     */
    protected $disable_shadow = null;
    /**
     *button透传参数、端上调用后端接口使用
     *
     * Generated from protobuf field <code>map<string, string> button_params = 33;</code>
     */
    private $button_params;
    /**
     *button透传参数、端上调用后端接口使用
     *
     * Generated from protobuf field <code>string guide_params = 34;</code>
     */
    protected $guide_params = null;
    /**
     *1 压缩间距
     *
     * Generated from protobuf field <code>int32 is_compressed = 35;</code>
     */
    protected $is_compressed = null;
    /**
     *埋点参数
     *
     * Generated from protobuf field <code>map<string, string> omg_data = 36;</code>
     */
    private $omg_data;
    /**
     *增加未勾选的盒子标题
     *
     * Generated from protobuf field <code>string unselect_popup_title = 37;</code>
     */
    protected $unselect_popup_title = null;
    /**
     *推荐表单推荐区车型标签
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RecData rec_data = 38;</code>
     */
    protected $rec_data = null;
    /**
     *推荐表单推荐区车型标签
     *
     * Generated from protobuf field <code>int32 car_icon_type = 40;</code>
     */
    protected $car_icon_type = null;
    /**
     *1 不展示etp 0 展示etp
     *
     * Generated from protobuf field <code>int32 is_sub_nodisplay_etp = 41;</code>
     */
    protected $is_sub_nodisplay_etp = null;
    /**
     *0 默认， 1 弹层内 没有子车型暴漏
     *
     * Generated from protobuf field <code>int32 popup_style_type = 42;</code>
     */
    protected $popup_style_type = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $type
     *          1单车型｜车大，2三方盒子，3出租车盒子，4高峰盒子，99导流, 101 司乘议价
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $products
     *          1单车型｜车大，2三方盒子，3出租车盒子，4高峰盒子，99导流, 101 司乘议价
     *     @type int $is_selected
     *     @type string $car_icon
     *     @type string $car_title
     *     @type string $popup_title
     *     @type string $popup_sub_title
     *     @type string $recommend_product
     *          当用户没有选择出租车的时候，出租车盒子需要外露的一个单独的车型
     *     @type \Dirpc\SDK\PreSale\NewFormLinkInfo $link_info
     *          车大
     *     @type string $guide_path
     *          导流地址
     *     @type string $button_text
     *          导流按钮文案
     *     @type int $box_id
     *          导流按钮文案
     *     @type string $box_desc
     *     @type string $fee_desc
     *     @type string $fee_desc_icon
     *     @type int $jump_type
     *     @type string $group_id
     *          group主键id 规则: type + boxid||product_category||subgroup_id
     *     @type \Dirpc\SDK\PreSale\GroupSubTitle $sub_title
     *          盒子的标签
     *     @type \Dirpc\SDK\PreSale\ButtonStyle $button_style
     *          按钮样式，不设置默认原样式
     *     @type \Dirpc\SDK\PreSale\NewFormBargainMsg $bargain_msg
     *          司乘议价砍价信息
     *     @type \Dirpc\SDK\PreSale\NewFormFeeDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $fee_desc_list
     *          盒子外层费用描述
     *     @type string $popup_toast
     *          盒子外层费用描述
     *     @type int $action_type
     *     @type int $guide_style
     *     @type \Dirpc\SDK\PreSale\bargainPopup $bargain_popup
     *          司乘议价弹框信息
     *     @type string $fee_msg
     *          盒子外层费用
     *     @type string $fee_msg_template
     *          盒子外层费用
     *     @type string $fee_detail_url
     *     @type int $style_type
     *          字体样式 0 正常 1 大字
     *     @type int $disable_shadow
     *          1 关闭阴影
     *     @type array|\Nuwa\Protobuf\Internal\MapField $button_params
     *          button透传参数、端上调用后端接口使用
     *     @type string $guide_params
     *          button透传参数、端上调用后端接口使用
     *     @type int $is_compressed
     *          1 压缩间距
     *     @type array|\Nuwa\Protobuf\Internal\MapField $omg_data
     *          埋点参数
     *     @type string $unselect_popup_title
     *          增加未勾选的盒子标题
     *     @type \Dirpc\SDK\PreSale\RecData $rec_data
     *          推荐表单推荐区车型标签
     *     @type int $car_icon_type
     *          推荐表单推荐区车型标签
     *     @type int $is_sub_nodisplay_etp
     *          1 不展示etp 0 展示etp
     *     @type int $popup_style_type
     *          0 默认， 1 弹层内 没有子车型暴漏
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *1单车型｜车大，2三方盒子，3出租车盒子，4高峰盒子，99导流, 101 司乘议价
     *
     * Generated from protobuf field <code>int32 type = 1;</code>
     * @return int
     */
    public function getType()
    {
        return isset($this->type) ? $this->type : 0;
    }

    public function hasType()
    {
        return isset($this->type);
    }

    public function clearType()
    {
        unset($this->type);
    }

    /**
     *1单车型｜车大，2三方盒子，3出租车盒子，4高峰盒子，99导流, 101 司乘议价
     *
     * Generated from protobuf field <code>int32 type = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkInt32($var);
        $this->type = $var;

        return $this;
    }

    /**
     *1单车型｜车大，2三方盒子，3出租车盒子，4高峰盒子，99导流, 101 司乘议价
     *
     * Generated from protobuf field <code>repeated string products = 2;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getProducts()
    {
        return $this->products;
    }

    /**
     *1单车型｜车大，2三方盒子，3出租车盒子，4高峰盒子，99导流, 101 司乘议价
     *
     * Generated from protobuf field <code>repeated string products = 2;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setProducts($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->products = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 is_selected = 4;</code>
     * @return int
     */
    public function getIsSelected()
    {
        return isset($this->is_selected) ? $this->is_selected : 0;
    }

    public function hasIsSelected()
    {
        return isset($this->is_selected);
    }

    public function clearIsSelected()
    {
        unset($this->is_selected);
    }

    /**
     * Generated from protobuf field <code>int32 is_selected = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setIsSelected($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_selected = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string car_icon = 5;</code>
     * @return string
     */
    public function getCarIcon()
    {
        return isset($this->car_icon) ? $this->car_icon : '';
    }

    public function hasCarIcon()
    {
        return isset($this->car_icon);
    }

    public function clearCarIcon()
    {
        unset($this->car_icon);
    }

    /**
     * Generated from protobuf field <code>string car_icon = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setCarIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->car_icon = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string car_title = 6;</code>
     * @return string
     */
    public function getCarTitle()
    {
        return isset($this->car_title) ? $this->car_title : '';
    }

    public function hasCarTitle()
    {
        return isset($this->car_title);
    }

    public function clearCarTitle()
    {
        unset($this->car_title);
    }

    /**
     * Generated from protobuf field <code>string car_title = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setCarTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->car_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string popup_title = 7;</code>
     * @return string
     */
    public function getPopupTitle()
    {
        return isset($this->popup_title) ? $this->popup_title : '';
    }

    public function hasPopupTitle()
    {
        return isset($this->popup_title);
    }

    public function clearPopupTitle()
    {
        unset($this->popup_title);
    }

    /**
     * Generated from protobuf field <code>string popup_title = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setPopupTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->popup_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string popup_sub_title = 8;</code>
     * @return string
     */
    public function getPopupSubTitle()
    {
        return isset($this->popup_sub_title) ? $this->popup_sub_title : '';
    }

    public function hasPopupSubTitle()
    {
        return isset($this->popup_sub_title);
    }

    public function clearPopupSubTitle()
    {
        unset($this->popup_sub_title);
    }

    /**
     * Generated from protobuf field <code>string popup_sub_title = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setPopupSubTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->popup_sub_title = $var;

        return $this;
    }

    /**
     *当用户没有选择出租车的时候，出租车盒子需要外露的一个单独的车型
     *
     * Generated from protobuf field <code>string recommend_product = 9;</code>
     * @return string
     */
    public function getRecommendProduct()
    {
        return isset($this->recommend_product) ? $this->recommend_product : '';
    }

    public function hasRecommendProduct()
    {
        return isset($this->recommend_product);
    }

    public function clearRecommendProduct()
    {
        unset($this->recommend_product);
    }

    /**
     *当用户没有选择出租车的时候，出租车盒子需要外露的一个单独的车型
     *
     * Generated from protobuf field <code>string recommend_product = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setRecommendProduct($var)
    {
        GPBUtil::checkString($var, True);
        $this->recommend_product = $var;

        return $this;
    }

    /**
     *车大
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormLinkInfo link_info = 11;</code>
     * @return \Dirpc\SDK\PreSale\NewFormLinkInfo
     */
    public function getLinkInfo()
    {
        return isset($this->link_info) ? $this->link_info : null;
    }

    public function hasLinkInfo()
    {
        return isset($this->link_info);
    }

    public function clearLinkInfo()
    {
        unset($this->link_info);
    }

    /**
     *车大
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormLinkInfo link_info = 11;</code>
     * @param \Dirpc\SDK\PreSale\NewFormLinkInfo $var
     * @return $this
     */
    public function setLinkInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NewFormLinkInfo::class);
        $this->link_info = $var;

        return $this;
    }

    /**
     *导流地址
     *
     * Generated from protobuf field <code>string guide_path = 12;</code>
     * @return string
     */
    public function getGuidePath()
    {
        return isset($this->guide_path) ? $this->guide_path : '';
    }

    public function hasGuidePath()
    {
        return isset($this->guide_path);
    }

    public function clearGuidePath()
    {
        unset($this->guide_path);
    }

    /**
     *导流地址
     *
     * Generated from protobuf field <code>string guide_path = 12;</code>
     * @param string $var
     * @return $this
     */
    public function setGuidePath($var)
    {
        GPBUtil::checkString($var, True);
        $this->guide_path = $var;

        return $this;
    }

    /**
     *导流按钮文案
     *
     * Generated from protobuf field <code>string button_text = 13;</code>
     * @return string
     */
    public function getButtonText()
    {
        return isset($this->button_text) ? $this->button_text : '';
    }

    public function hasButtonText()
    {
        return isset($this->button_text);
    }

    public function clearButtonText()
    {
        unset($this->button_text);
    }

    /**
     *导流按钮文案
     *
     * Generated from protobuf field <code>string button_text = 13;</code>
     * @param string $var
     * @return $this
     */
    public function setButtonText($var)
    {
        GPBUtil::checkString($var, True);
        $this->button_text = $var;

        return $this;
    }

    /**
     *导流按钮文案
     *
     * Generated from protobuf field <code>int32 box_id = 14;</code>
     * @return int
     */
    public function getBoxId()
    {
        return isset($this->box_id) ? $this->box_id : 0;
    }

    public function hasBoxId()
    {
        return isset($this->box_id);
    }

    public function clearBoxId()
    {
        unset($this->box_id);
    }

    /**
     *导流按钮文案
     *
     * Generated from protobuf field <code>int32 box_id = 14;</code>
     * @param int $var
     * @return $this
     */
    public function setBoxId($var)
    {
        GPBUtil::checkInt32($var);
        $this->box_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string box_desc = 15;</code>
     * @return string
     */
    public function getBoxDesc()
    {
        return isset($this->box_desc) ? $this->box_desc : '';
    }

    public function hasBoxDesc()
    {
        return isset($this->box_desc);
    }

    public function clearBoxDesc()
    {
        unset($this->box_desc);
    }

    /**
     * Generated from protobuf field <code>string box_desc = 15;</code>
     * @param string $var
     * @return $this
     */
    public function setBoxDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->box_desc = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_desc = 16;</code>
     * @return string
     */
    public function getFeeDesc()
    {
        return isset($this->fee_desc) ? $this->fee_desc : '';
    }

    public function hasFeeDesc()
    {
        return isset($this->fee_desc);
    }

    public function clearFeeDesc()
    {
        unset($this->fee_desc);
    }

    /**
     * Generated from protobuf field <code>string fee_desc = 16;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_desc = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_desc_icon = 17;</code>
     * @return string
     */
    public function getFeeDescIcon()
    {
        return isset($this->fee_desc_icon) ? $this->fee_desc_icon : '';
    }

    public function hasFeeDescIcon()
    {
        return isset($this->fee_desc_icon);
    }

    public function clearFeeDescIcon()
    {
        unset($this->fee_desc_icon);
    }

    /**
     * Generated from protobuf field <code>string fee_desc_icon = 17;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeDescIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_desc_icon = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 jump_type = 18;</code>
     * @return int
     */
    public function getJumpType()
    {
        return isset($this->jump_type) ? $this->jump_type : 0;
    }

    public function hasJumpType()
    {
        return isset($this->jump_type);
    }

    public function clearJumpType()
    {
        unset($this->jump_type);
    }

    /**
     * Generated from protobuf field <code>int32 jump_type = 18;</code>
     * @param int $var
     * @return $this
     */
    public function setJumpType($var)
    {
        GPBUtil::checkInt32($var);
        $this->jump_type = $var;

        return $this;
    }

    /**
     *group主键id 规则: type + boxid||product_category||subgroup_id
     *
     * Generated from protobuf field <code>string group_id = 20;</code>
     * @return string
     */
    public function getGroupId()
    {
        return isset($this->group_id) ? $this->group_id : '';
    }

    public function hasGroupId()
    {
        return isset($this->group_id);
    }

    public function clearGroupId()
    {
        unset($this->group_id);
    }

    /**
     *group主键id 规则: type + boxid||product_category||subgroup_id
     *
     * Generated from protobuf field <code>string group_id = 20;</code>
     * @param string $var
     * @return $this
     */
    public function setGroupId($var)
    {
        GPBUtil::checkString($var, True);
        $this->group_id = $var;

        return $this;
    }

    /**
     *盒子的标签
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.GroupSubTitle sub_title = 21;</code>
     * @return \Dirpc\SDK\PreSale\GroupSubTitle
     */
    public function getSubTitle()
    {
        return isset($this->sub_title) ? $this->sub_title : null;
    }

    public function hasSubTitle()
    {
        return isset($this->sub_title);
    }

    public function clearSubTitle()
    {
        unset($this->sub_title);
    }

    /**
     *盒子的标签
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.GroupSubTitle sub_title = 21;</code>
     * @param \Dirpc\SDK\PreSale\GroupSubTitle $var
     * @return $this
     */
    public function setSubTitle($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\GroupSubTitle::class);
        $this->sub_title = $var;

        return $this;
    }

    /**
     *按钮样式，不设置默认原样式
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ButtonStyle button_style = 22;</code>
     * @return \Dirpc\SDK\PreSale\ButtonStyle
     */
    public function getButtonStyle()
    {
        return isset($this->button_style) ? $this->button_style : null;
    }

    public function hasButtonStyle()
    {
        return isset($this->button_style);
    }

    public function clearButtonStyle()
    {
        unset($this->button_style);
    }

    /**
     *按钮样式，不设置默认原样式
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ButtonStyle button_style = 22;</code>
     * @param \Dirpc\SDK\PreSale\ButtonStyle $var
     * @return $this
     */
    public function setButtonStyle($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\ButtonStyle::class);
        $this->button_style = $var;

        return $this;
    }

    /**
     *司乘议价砍价信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormBargainMsg bargain_msg = 23;</code>
     * @return \Dirpc\SDK\PreSale\NewFormBargainMsg
     */
    public function getBargainMsg()
    {
        return isset($this->bargain_msg) ? $this->bargain_msg : null;
    }

    public function hasBargainMsg()
    {
        return isset($this->bargain_msg);
    }

    public function clearBargainMsg()
    {
        unset($this->bargain_msg);
    }

    /**
     *司乘议价砍价信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormBargainMsg bargain_msg = 23;</code>
     * @param \Dirpc\SDK\PreSale\NewFormBargainMsg $var
     * @return $this
     */
    public function setBargainMsg($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NewFormBargainMsg::class);
        $this->bargain_msg = $var;

        return $this;
    }

    /**
     *盒子外层费用描述
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 24;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getFeeDescList()
    {
        return $this->fee_desc_list;
    }

    /**
     *盒子外层费用描述
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 24;</code>
     * @param \Dirpc\SDK\PreSale\NewFormFeeDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFeeDescList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\NewFormFeeDesc::class);
        $this->fee_desc_list = $arr;

        return $this;
    }

    /**
     *盒子外层费用描述
     *
     * Generated from protobuf field <code>string popup_toast = 25;</code>
     * @return string
     */
    public function getPopupToast()
    {
        return isset($this->popup_toast) ? $this->popup_toast : '';
    }

    public function hasPopupToast()
    {
        return isset($this->popup_toast);
    }

    public function clearPopupToast()
    {
        unset($this->popup_toast);
    }

    /**
     *盒子外层费用描述
     *
     * Generated from protobuf field <code>string popup_toast = 25;</code>
     * @param string $var
     * @return $this
     */
    public function setPopupToast($var)
    {
        GPBUtil::checkString($var, True);
        $this->popup_toast = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 action_type = 26;</code>
     * @return int
     */
    public function getActionType()
    {
        return isset($this->action_type) ? $this->action_type : 0;
    }

    public function hasActionType()
    {
        return isset($this->action_type);
    }

    public function clearActionType()
    {
        unset($this->action_type);
    }

    /**
     * Generated from protobuf field <code>int32 action_type = 26;</code>
     * @param int $var
     * @return $this
     */
    public function setActionType($var)
    {
        GPBUtil::checkInt32($var);
        $this->action_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 guide_style = 27;</code>
     * @return int
     */
    public function getGuideStyle()
    {
        return isset($this->guide_style) ? $this->guide_style : 0;
    }

    public function hasGuideStyle()
    {
        return isset($this->guide_style);
    }

    public function clearGuideStyle()
    {
        unset($this->guide_style);
    }

    /**
     * Generated from protobuf field <code>int32 guide_style = 27;</code>
     * @param int $var
     * @return $this
     */
    public function setGuideStyle($var)
    {
        GPBUtil::checkInt32($var);
        $this->guide_style = $var;

        return $this;
    }

    /**
     *司乘议价弹框信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.bargainPopup bargain_popup = 28;</code>
     * @return \Dirpc\SDK\PreSale\bargainPopup
     */
    public function getBargainPopup()
    {
        return isset($this->bargain_popup) ? $this->bargain_popup : null;
    }

    public function hasBargainPopup()
    {
        return isset($this->bargain_popup);
    }

    public function clearBargainPopup()
    {
        unset($this->bargain_popup);
    }

    /**
     *司乘议价弹框信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.bargainPopup bargain_popup = 28;</code>
     * @param \Dirpc\SDK\PreSale\bargainPopup $var
     * @return $this
     */
    public function setBargainPopup($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\bargainPopup::class);
        $this->bargain_popup = $var;

        return $this;
    }

    /**
     *盒子外层费用
     *
     * Generated from protobuf field <code>string fee_msg = 29;</code>
     * @return string
     */
    public function getFeeMsg()
    {
        return isset($this->fee_msg) ? $this->fee_msg : '';
    }

    public function hasFeeMsg()
    {
        return isset($this->fee_msg);
    }

    public function clearFeeMsg()
    {
        unset($this->fee_msg);
    }

    /**
     *盒子外层费用
     *
     * Generated from protobuf field <code>string fee_msg = 29;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg = $var;

        return $this;
    }

    /**
     *盒子外层费用
     *
     * Generated from protobuf field <code>string fee_msg_template = 39;</code>
     * @return string
     */
    public function getFeeMsgTemplate()
    {
        return isset($this->fee_msg_template) ? $this->fee_msg_template : '';
    }

    public function hasFeeMsgTemplate()
    {
        return isset($this->fee_msg_template);
    }

    public function clearFeeMsgTemplate()
    {
        unset($this->fee_msg_template);
    }

    /**
     *盒子外层费用
     *
     * Generated from protobuf field <code>string fee_msg_template = 39;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsgTemplate($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg_template = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_detail_url = 30;</code>
     * @return string
     */
    public function getFeeDetailUrl()
    {
        return isset($this->fee_detail_url) ? $this->fee_detail_url : '';
    }

    public function hasFeeDetailUrl()
    {
        return isset($this->fee_detail_url);
    }

    public function clearFeeDetailUrl()
    {
        unset($this->fee_detail_url);
    }

    /**
     * Generated from protobuf field <code>string fee_detail_url = 30;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeDetailUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_detail_url = $var;

        return $this;
    }

    /**
     *字体样式 0 正常 1 大字
     *
     * Generated from protobuf field <code>int32 style_type = 31;</code>
     * @return int
     */
    public function getStyleType()
    {
        return isset($this->style_type) ? $this->style_type : 0;
    }

    public function hasStyleType()
    {
        return isset($this->style_type);
    }

    public function clearStyleType()
    {
        unset($this->style_type);
    }

    /**
     *字体样式 0 正常 1 大字
     *
     * Generated from protobuf field <code>int32 style_type = 31;</code>
     * @param int $var
     * @return $this
     */
    public function setStyleType($var)
    {
        GPBUtil::checkInt32($var);
        $this->style_type = $var;

        return $this;
    }

    /**
     *1 关闭阴影
     *
     * Generated from protobuf field <code>int32 disable_shadow = 32;</code>
     * @return int
     */
    public function getDisableShadow()
    {
        return isset($this->disable_shadow) ? $this->disable_shadow : 0;
    }

    public function hasDisableShadow()
    {
        return isset($this->disable_shadow);
    }

    public function clearDisableShadow()
    {
        unset($this->disable_shadow);
    }

    /**
     *1 关闭阴影
     *
     * Generated from protobuf field <code>int32 disable_shadow = 32;</code>
     * @param int $var
     * @return $this
     */
    public function setDisableShadow($var)
    {
        GPBUtil::checkInt32($var);
        $this->disable_shadow = $var;

        return $this;
    }

    /**
     *button透传参数、端上调用后端接口使用
     *
     * Generated from protobuf field <code>map<string, string> button_params = 33;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getButtonParams()
    {
        return $this->button_params;
    }

    /**
     *button透传参数、端上调用后端接口使用
     *
     * Generated from protobuf field <code>map<string, string> button_params = 33;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setButtonParams($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->button_params = $arr;

        return $this;
    }

    /**
     *button透传参数、端上调用后端接口使用
     *
     * Generated from protobuf field <code>string guide_params = 34;</code>
     * @return string
     */
    public function getGuideParams()
    {
        return isset($this->guide_params) ? $this->guide_params : '';
    }

    public function hasGuideParams()
    {
        return isset($this->guide_params);
    }

    public function clearGuideParams()
    {
        unset($this->guide_params);
    }

    /**
     *button透传参数、端上调用后端接口使用
     *
     * Generated from protobuf field <code>string guide_params = 34;</code>
     * @param string $var
     * @return $this
     */
    public function setGuideParams($var)
    {
        GPBUtil::checkString($var, True);
        $this->guide_params = $var;

        return $this;
    }

    /**
     *1 压缩间距
     *
     * Generated from protobuf field <code>int32 is_compressed = 35;</code>
     * @return int
     */
    public function getIsCompressed()
    {
        return isset($this->is_compressed) ? $this->is_compressed : 0;
    }

    public function hasIsCompressed()
    {
        return isset($this->is_compressed);
    }

    public function clearIsCompressed()
    {
        unset($this->is_compressed);
    }

    /**
     *1 压缩间距
     *
     * Generated from protobuf field <code>int32 is_compressed = 35;</code>
     * @param int $var
     * @return $this
     */
    public function setIsCompressed($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_compressed = $var;

        return $this;
    }

    /**
     *埋点参数
     *
     * Generated from protobuf field <code>map<string, string> omg_data = 36;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getOmgData()
    {
        return $this->omg_data;
    }

    /**
     *埋点参数
     *
     * Generated from protobuf field <code>map<string, string> omg_data = 36;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setOmgData($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->omg_data = $arr;

        return $this;
    }

    /**
     *增加未勾选的盒子标题
     *
     * Generated from protobuf field <code>string unselect_popup_title = 37;</code>
     * @return string
     */
    public function getUnselectPopupTitle()
    {
        return isset($this->unselect_popup_title) ? $this->unselect_popup_title : '';
    }

    public function hasUnselectPopupTitle()
    {
        return isset($this->unselect_popup_title);
    }

    public function clearUnselectPopupTitle()
    {
        unset($this->unselect_popup_title);
    }

    /**
     *增加未勾选的盒子标题
     *
     * Generated from protobuf field <code>string unselect_popup_title = 37;</code>
     * @param string $var
     * @return $this
     */
    public function setUnselectPopupTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->unselect_popup_title = $var;

        return $this;
    }

    /**
     *推荐表单推荐区车型标签
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RecData rec_data = 38;</code>
     * @return \Dirpc\SDK\PreSale\RecData
     */
    public function getRecData()
    {
        return isset($this->rec_data) ? $this->rec_data : null;
    }

    public function hasRecData()
    {
        return isset($this->rec_data);
    }

    public function clearRecData()
    {
        unset($this->rec_data);
    }

    /**
     *推荐表单推荐区车型标签
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RecData rec_data = 38;</code>
     * @param \Dirpc\SDK\PreSale\RecData $var
     * @return $this
     */
    public function setRecData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\RecData::class);
        $this->rec_data = $var;

        return $this;
    }

    /**
     *推荐表单推荐区车型标签
     *
     * Generated from protobuf field <code>int32 car_icon_type = 40;</code>
     * @return int
     */
    public function getCarIconType()
    {
        return isset($this->car_icon_type) ? $this->car_icon_type : 0;
    }

    public function hasCarIconType()
    {
        return isset($this->car_icon_type);
    }

    public function clearCarIconType()
    {
        unset($this->car_icon_type);
    }

    /**
     *推荐表单推荐区车型标签
     *
     * Generated from protobuf field <code>int32 car_icon_type = 40;</code>
     * @param int $var
     * @return $this
     */
    public function setCarIconType($var)
    {
        GPBUtil::checkInt32($var);
        $this->car_icon_type = $var;

        return $this;
    }

    /**
     *1 不展示etp 0 展示etp
     *
     * Generated from protobuf field <code>int32 is_sub_nodisplay_etp = 41;</code>
     * @return int
     */
    public function getIsSubNodisplayEtp()
    {
        return isset($this->is_sub_nodisplay_etp) ? $this->is_sub_nodisplay_etp : 0;
    }

    public function hasIsSubNodisplayEtp()
    {
        return isset($this->is_sub_nodisplay_etp);
    }

    public function clearIsSubNodisplayEtp()
    {
        unset($this->is_sub_nodisplay_etp);
    }

    /**
     *1 不展示etp 0 展示etp
     *
     * Generated from protobuf field <code>int32 is_sub_nodisplay_etp = 41;</code>
     * @param int $var
     * @return $this
     */
    public function setIsSubNodisplayEtp($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_sub_nodisplay_etp = $var;

        return $this;
    }

    /**
     *0 默认， 1 弹层内 没有子车型暴漏
     *
     * Generated from protobuf field <code>int32 popup_style_type = 42;</code>
     * @return int
     */
    public function getPopupStyleType()
    {
        return isset($this->popup_style_type) ? $this->popup_style_type : 0;
    }

    public function hasPopupStyleType()
    {
        return isset($this->popup_style_type);
    }

    public function clearPopupStyleType()
    {
        unset($this->popup_style_type);
    }

    /**
     *0 默认， 1 弹层内 没有子车型暴漏
     *
     * Generated from protobuf field <code>int32 popup_style_type = 42;</code>
     * @param int $var
     * @return $this
     */
    public function setPopupStyleType($var)
    {
        GPBUtil::checkInt32($var);
        $this->popup_style_type = $var;

        return $this;
    }

}

