<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.OmegaData</code>
 */
class OmegaData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string omega_event_id = 1;</code>
     */
    protected $omega_event_id = null;
    /**
     * Generated from protobuf field <code>map<string, string> omega_parameter = 2;</code>
     */
    private $omega_parameter;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $omega_event_id
     *     @type array|\Nuwa\Protobuf\Internal\MapField $omega_parameter
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string omega_event_id = 1;</code>
     * @return string
     */
    public function getOmegaEventId()
    {
        return isset($this->omega_event_id) ? $this->omega_event_id : '';
    }

    public function hasOmegaEventId()
    {
        return isset($this->omega_event_id);
    }

    public function clearOmegaEventId()
    {
        unset($this->omega_event_id);
    }

    /**
     * Generated from protobuf field <code>string omega_event_id = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setOmegaEventId($var)
    {
        GPBUtil::checkString($var, True);
        $this->omega_event_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>map<string, string> omega_parameter = 2;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getOmegaParameter()
    {
        return $this->omega_parameter;
    }

    /**
     * Generated from protobuf field <code>map<string, string> omega_parameter = 2;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setOmegaParameter($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->omega_parameter = $arr;

        return $this;
    }

}

