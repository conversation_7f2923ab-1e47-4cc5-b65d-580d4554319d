<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.ShareInfo</code>
 */
class ShareInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string path = 1;</code>
     */
    protected $path = '';
    /**
     * Generated from protobuf field <code>string title = 2;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>string image_url = 3;</code>
     */
    protected $image_url = '';
    /**
     * Generated from protobuf field <code>string bg_img = 4;</code>
     */
    protected $bg_img = null;
    /**
     * Generated from protobuf field <code>string button_text = 5;</code>
     */
    protected $button_text = null;
    /**
     * Generated from protobuf field <code>int32 style = 6;</code>
     */
    protected $style = null;
    /**
     * Generated from protobuf field <code>string price = 7;</code>
     */
    protected $price = null;
    /**
     * Generated from protobuf field <code>string origin_price = 8;</code>
     */
    protected $origin_price = null;
    /**
     * Generated from protobuf field <code>string start_name = 9;</code>
     */
    protected $start_name = null;
    /**
     * Generated from protobuf field <code>string dest_name = 10;</code>
     */
    protected $dest_name = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $path
     *     @type string $title
     *     @type string $image_url
     *     @type string $bg_img
     *     @type string $button_text
     *     @type int $style
     *     @type string $price
     *     @type string $origin_price
     *     @type string $start_name
     *     @type string $dest_name
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string path = 1;</code>
     * @return string
     */
    public function getPath()
    {
        return $this->path;
    }

    /**
     * Generated from protobuf field <code>string path = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setPath($var)
    {
        GPBUtil::checkString($var, True);
        $this->path = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string title = 2;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string image_url = 3;</code>
     * @return string
     */
    public function getImageUrl()
    {
        return $this->image_url;
    }

    /**
     * Generated from protobuf field <code>string image_url = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setImageUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->image_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string bg_img = 4;</code>
     * @return string
     */
    public function getBgImg()
    {
        return isset($this->bg_img) ? $this->bg_img : '';
    }

    public function hasBgImg()
    {
        return isset($this->bg_img);
    }

    public function clearBgImg()
    {
        unset($this->bg_img);
    }

    /**
     * Generated from protobuf field <code>string bg_img = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setBgImg($var)
    {
        GPBUtil::checkString($var, True);
        $this->bg_img = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string button_text = 5;</code>
     * @return string
     */
    public function getButtonText()
    {
        return isset($this->button_text) ? $this->button_text : '';
    }

    public function hasButtonText()
    {
        return isset($this->button_text);
    }

    public function clearButtonText()
    {
        unset($this->button_text);
    }

    /**
     * Generated from protobuf field <code>string button_text = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setButtonText($var)
    {
        GPBUtil::checkString($var, True);
        $this->button_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 style = 6;</code>
     * @return int
     */
    public function getStyle()
    {
        return isset($this->style) ? $this->style : 0;
    }

    public function hasStyle()
    {
        return isset($this->style);
    }

    public function clearStyle()
    {
        unset($this->style);
    }

    /**
     * Generated from protobuf field <code>int32 style = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setStyle($var)
    {
        GPBUtil::checkInt32($var);
        $this->style = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string price = 7;</code>
     * @return string
     */
    public function getPrice()
    {
        return isset($this->price) ? $this->price : '';
    }

    public function hasPrice()
    {
        return isset($this->price);
    }

    public function clearPrice()
    {
        unset($this->price);
    }

    /**
     * Generated from protobuf field <code>string price = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setPrice($var)
    {
        GPBUtil::checkString($var, True);
        $this->price = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string origin_price = 8;</code>
     * @return string
     */
    public function getOriginPrice()
    {
        return isset($this->origin_price) ? $this->origin_price : '';
    }

    public function hasOriginPrice()
    {
        return isset($this->origin_price);
    }

    public function clearOriginPrice()
    {
        unset($this->origin_price);
    }

    /**
     * Generated from protobuf field <code>string origin_price = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setOriginPrice($var)
    {
        GPBUtil::checkString($var, True);
        $this->origin_price = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string start_name = 9;</code>
     * @return string
     */
    public function getStartName()
    {
        return isset($this->start_name) ? $this->start_name : '';
    }

    public function hasStartName()
    {
        return isset($this->start_name);
    }

    public function clearStartName()
    {
        unset($this->start_name);
    }

    /**
     * Generated from protobuf field <code>string start_name = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setStartName($var)
    {
        GPBUtil::checkString($var, True);
        $this->start_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string dest_name = 10;</code>
     * @return string
     */
    public function getDestName()
    {
        return isset($this->dest_name) ? $this->dest_name : '';
    }

    public function hasDestName()
    {
        return isset($this->dest_name);
    }

    public function clearDestName()
    {
        unset($this->dest_name);
    }

    /**
     * Generated from protobuf field <code>string dest_name = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setDestName($var)
    {
        GPBUtil::checkString($var, True);
        $this->dest_name = $var;

        return $this;
    }

}

