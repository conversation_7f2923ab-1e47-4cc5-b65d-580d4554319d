<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.LuxEstimateResponse</code>
 */
class LuxEstimateResponse extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CarEstimateData estimate_car_level_data = 1;</code>
     */
    private $estimate_car_level_data;
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.DriverEstimateData estimate_driver_level_data = 2;</code>
     */
    private $estimate_driver_level_data;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PreferInfo prefer_info = 3;</code>
     */
    protected $prefer_info = null;
    /**
     * Generated from protobuf field <code>string title = 4;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>string sub_title = 5;</code>
     */
    protected $sub_title = '';
    /**
     * Generated from protobuf field <code>string head_img = 6;</code>
     */
    protected $head_img = '';
    /**
     * Generated from protobuf field <code>string start_bg_color = 7;</code>
     */
    protected $start_bg_color = '';
    /**
     * Generated from protobuf field <code>string end_bg_color = 8;</code>
     */
    protected $end_bg_color = '';
    /**
     * Generated from protobuf field <code>string estimate_trace_id = 9;</code>
     */
    protected $estimate_trace_id = '';
    /**
     * Generated from protobuf field <code>int32 default_select_tab = 10;</code>
     */
    protected $default_select_tab = 0;
    /**
     * Generated from protobuf field <code>int32 theme = 11;</code>
     */
    protected $theme = 0;
    /**
     * Generated from protobuf field <code>int32 disable = 12;</code>
     */
    protected $disable = 0;
    /**
     * Generated from protobuf field <code>int32 show_tab = 13;</code>
     */
    protected $show_tab = 0;
    /**
     * Generated from protobuf field <code>string sub_title_link = 14;</code>
     */
    protected $sub_title_link = '';
    /**
     *红包
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.DispatchFee dispatch_fee = 15;</code>
     */
    protected $dispatch_fee = null;
    /**
     *捎话
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.Comment comment = 16;</code>
     */
    protected $comment = null;
    /**
     *个性化服务数据
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CustomData custom_info = 17;</code>
     */
    private $custom_info;
    /**
     *个性化服务文案
     *
     * Generated from protobuf field <code>string custom_head = 18;</code>
     */
    protected $custom_head = null;
    /**
     *个性化服务说明
     *
     * Generated from protobuf field <code>string custom_head_link = 19;</code>
     */
    protected $custom_head_link = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\CarEstimateData[]|\Nuwa\Protobuf\Internal\RepeatedField $estimate_car_level_data
     *     @type \Dirpc\SDK\PreSale\DriverEstimateData[]|\Nuwa\Protobuf\Internal\RepeatedField $estimate_driver_level_data
     *     @type \Dirpc\SDK\PreSale\PreferInfo $prefer_info
     *     @type string $title
     *     @type string $sub_title
     *     @type string $head_img
     *     @type string $start_bg_color
     *     @type string $end_bg_color
     *     @type string $estimate_trace_id
     *     @type int $default_select_tab
     *     @type int $theme
     *     @type int $disable
     *     @type int $show_tab
     *     @type string $sub_title_link
     *     @type \Dirpc\SDK\PreSale\DispatchFee $dispatch_fee
     *          红包
     *     @type \Dirpc\SDK\PreSale\Comment $comment
     *          捎话
     *     @type \Dirpc\SDK\PreSale\CustomData[]|\Nuwa\Protobuf\Internal\RepeatedField $custom_info
     *          个性化服务数据
     *     @type string $custom_head
     *          个性化服务文案
     *     @type string $custom_head_link
     *          个性化服务说明
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CarEstimateData estimate_car_level_data = 1;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getEstimateCarLevelData()
    {
        return $this->estimate_car_level_data;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CarEstimateData estimate_car_level_data = 1;</code>
     * @param \Dirpc\SDK\PreSale\CarEstimateData[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setEstimateCarLevelData($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\CarEstimateData::class);
        $this->estimate_car_level_data = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.DriverEstimateData estimate_driver_level_data = 2;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getEstimateDriverLevelData()
    {
        return $this->estimate_driver_level_data;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.DriverEstimateData estimate_driver_level_data = 2;</code>
     * @param \Dirpc\SDK\PreSale\DriverEstimateData[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setEstimateDriverLevelData($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\DriverEstimateData::class);
        $this->estimate_driver_level_data = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PreferInfo prefer_info = 3;</code>
     * @return \Dirpc\SDK\PreSale\PreferInfo
     */
    public function getPreferInfo()
    {
        return isset($this->prefer_info) ? $this->prefer_info : null;
    }

    public function hasPreferInfo()
    {
        return isset($this->prefer_info);
    }

    public function clearPreferInfo()
    {
        unset($this->prefer_info);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PreferInfo prefer_info = 3;</code>
     * @param \Dirpc\SDK\PreSale\PreferInfo $var
     * @return $this
     */
    public function setPreferInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\PreferInfo::class);
        $this->prefer_info = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string title = 4;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string sub_title = 5;</code>
     * @return string
     */
    public function getSubTitle()
    {
        return $this->sub_title;
    }

    /**
     * Generated from protobuf field <code>string sub_title = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setSubTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string head_img = 6;</code>
     * @return string
     */
    public function getHeadImg()
    {
        return $this->head_img;
    }

    /**
     * Generated from protobuf field <code>string head_img = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setHeadImg($var)
    {
        GPBUtil::checkString($var, True);
        $this->head_img = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string start_bg_color = 7;</code>
     * @return string
     */
    public function getStartBgColor()
    {
        return $this->start_bg_color;
    }

    /**
     * Generated from protobuf field <code>string start_bg_color = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setStartBgColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->start_bg_color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string end_bg_color = 8;</code>
     * @return string
     */
    public function getEndBgColor()
    {
        return $this->end_bg_color;
    }

    /**
     * Generated from protobuf field <code>string end_bg_color = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setEndBgColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->end_bg_color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string estimate_trace_id = 9;</code>
     * @return string
     */
    public function getEstimateTraceId()
    {
        return $this->estimate_trace_id;
    }

    /**
     * Generated from protobuf field <code>string estimate_trace_id = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateTraceId($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_trace_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 default_select_tab = 10;</code>
     * @return int
     */
    public function getDefaultSelectTab()
    {
        return $this->default_select_tab;
    }

    /**
     * Generated from protobuf field <code>int32 default_select_tab = 10;</code>
     * @param int $var
     * @return $this
     */
    public function setDefaultSelectTab($var)
    {
        GPBUtil::checkInt32($var);
        $this->default_select_tab = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 theme = 11;</code>
     * @return int
     */
    public function getTheme()
    {
        return $this->theme;
    }

    /**
     * Generated from protobuf field <code>int32 theme = 11;</code>
     * @param int $var
     * @return $this
     */
    public function setTheme($var)
    {
        GPBUtil::checkInt32($var);
        $this->theme = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 disable = 12;</code>
     * @return int
     */
    public function getDisable()
    {
        return $this->disable;
    }

    /**
     * Generated from protobuf field <code>int32 disable = 12;</code>
     * @param int $var
     * @return $this
     */
    public function setDisable($var)
    {
        GPBUtil::checkInt32($var);
        $this->disable = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 show_tab = 13;</code>
     * @return int
     */
    public function getShowTab()
    {
        return $this->show_tab;
    }

    /**
     * Generated from protobuf field <code>int32 show_tab = 13;</code>
     * @param int $var
     * @return $this
     */
    public function setShowTab($var)
    {
        GPBUtil::checkInt32($var);
        $this->show_tab = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string sub_title_link = 14;</code>
     * @return string
     */
    public function getSubTitleLink()
    {
        return $this->sub_title_link;
    }

    /**
     * Generated from protobuf field <code>string sub_title_link = 14;</code>
     * @param string $var
     * @return $this
     */
    public function setSubTitleLink($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_title_link = $var;

        return $this;
    }

    /**
     *红包
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.DispatchFee dispatch_fee = 15;</code>
     * @return \Dirpc\SDK\PreSale\DispatchFee
     */
    public function getDispatchFee()
    {
        return isset($this->dispatch_fee) ? $this->dispatch_fee : null;
    }

    public function hasDispatchFee()
    {
        return isset($this->dispatch_fee);
    }

    public function clearDispatchFee()
    {
        unset($this->dispatch_fee);
    }

    /**
     *红包
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.DispatchFee dispatch_fee = 15;</code>
     * @param \Dirpc\SDK\PreSale\DispatchFee $var
     * @return $this
     */
    public function setDispatchFee($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\DispatchFee::class);
        $this->dispatch_fee = $var;

        return $this;
    }

    /**
     *捎话
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.Comment comment = 16;</code>
     * @return \Dirpc\SDK\PreSale\Comment
     */
    public function getComment()
    {
        return isset($this->comment) ? $this->comment : null;
    }

    public function hasComment()
    {
        return isset($this->comment);
    }

    public function clearComment()
    {
        unset($this->comment);
    }

    /**
     *捎话
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.Comment comment = 16;</code>
     * @param \Dirpc\SDK\PreSale\Comment $var
     * @return $this
     */
    public function setComment($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\Comment::class);
        $this->comment = $var;

        return $this;
    }

    /**
     *个性化服务数据
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CustomData custom_info = 17;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getCustomInfo()
    {
        return $this->custom_info;
    }

    /**
     *个性化服务数据
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CustomData custom_info = 17;</code>
     * @param \Dirpc\SDK\PreSale\CustomData[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setCustomInfo($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\CustomData::class);
        $this->custom_info = $arr;

        return $this;
    }

    /**
     *个性化服务文案
     *
     * Generated from protobuf field <code>string custom_head = 18;</code>
     * @return string
     */
    public function getCustomHead()
    {
        return isset($this->custom_head) ? $this->custom_head : '';
    }

    public function hasCustomHead()
    {
        return isset($this->custom_head);
    }

    public function clearCustomHead()
    {
        unset($this->custom_head);
    }

    /**
     *个性化服务文案
     *
     * Generated from protobuf field <code>string custom_head = 18;</code>
     * @param string $var
     * @return $this
     */
    public function setCustomHead($var)
    {
        GPBUtil::checkString($var, True);
        $this->custom_head = $var;

        return $this;
    }

    /**
     *个性化服务说明
     *
     * Generated from protobuf field <code>string custom_head_link = 19;</code>
     * @return string
     */
    public function getCustomHeadLink()
    {
        return isset($this->custom_head_link) ? $this->custom_head_link : '';
    }

    public function hasCustomHeadLink()
    {
        return isset($this->custom_head_link);
    }

    public function clearCustomHeadLink()
    {
        unset($this->custom_head_link);
    }

    /**
     *个性化服务说明
     *
     * Generated from protobuf field <code>string custom_head_link = 19;</code>
     * @param string $var
     * @return $this
     */
    public function setCustomHeadLink($var)
    {
        GPBUtil::checkString($var, True);
        $this->custom_head_link = $var;

        return $this;
    }

}

