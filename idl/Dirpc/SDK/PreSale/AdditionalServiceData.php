<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.AdditionalServiceData</code>
 */
class AdditionalServiceData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string page_title = 1;</code>
     */
    protected $page_title = '';
    /**
     * Generated from protobuf field <code>string title = 2;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>string tips_url = 3;</code>
     */
    protected $tips_url = '';
    /**
     * Generated from protobuf field <code>repeated string sub_title_list = 4;</code>
     */
    private $sub_title_list;
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.AdditionalService service_list = 5;</code>
     */
    private $service_list;
    /**
     * Generated from protobuf field <code>string guide_text = 6;</code>
     */
    protected $guide_text = '';
    /**
     * Generated from protobuf field <code>int32 guide_times = 7;</code>
     */
    protected $guide_times = 0;
    /**
     * Generated from protobuf field <code>int32 version = 8;</code>
     */
    protected $version = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $page_title
     *     @type string $title
     *     @type string $tips_url
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $sub_title_list
     *     @type \Dirpc\SDK\PreSale\AdditionalService[]|\Nuwa\Protobuf\Internal\RepeatedField $service_list
     *     @type string $guide_text
     *     @type int $guide_times
     *     @type int $version
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string page_title = 1;</code>
     * @return string
     */
    public function getPageTitle()
    {
        return $this->page_title;
    }

    /**
     * Generated from protobuf field <code>string page_title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setPageTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->page_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string title = 2;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string tips_url = 3;</code>
     * @return string
     */
    public function getTipsUrl()
    {
        return $this->tips_url;
    }

    /**
     * Generated from protobuf field <code>string tips_url = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setTipsUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->tips_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated string sub_title_list = 4;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getSubTitleList()
    {
        return $this->sub_title_list;
    }

    /**
     * Generated from protobuf field <code>repeated string sub_title_list = 4;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSubTitleList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->sub_title_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.AdditionalService service_list = 5;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getServiceList()
    {
        return $this->service_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.AdditionalService service_list = 5;</code>
     * @param \Dirpc\SDK\PreSale\AdditionalService[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setServiceList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\AdditionalService::class);
        $this->service_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string guide_text = 6;</code>
     * @return string
     */
    public function getGuideText()
    {
        return $this->guide_text;
    }

    /**
     * Generated from protobuf field <code>string guide_text = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setGuideText($var)
    {
        GPBUtil::checkString($var, True);
        $this->guide_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 guide_times = 7;</code>
     * @return int
     */
    public function getGuideTimes()
    {
        return $this->guide_times;
    }

    /**
     * Generated from protobuf field <code>int32 guide_times = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setGuideTimes($var)
    {
        GPBUtil::checkInt32($var);
        $this->guide_times = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 version = 8;</code>
     * @return int
     */
    public function getVersion()
    {
        return $this->version;
    }

    /**
     * Generated from protobuf field <code>int32 version = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setVersion($var)
    {
        GPBUtil::checkInt32($var);
        $this->version = $var;

        return $this;
    }

}

