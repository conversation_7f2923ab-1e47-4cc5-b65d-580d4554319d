<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.OrderButtonInfo</code>
 */
class OrderButtonInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string expect_info_text = 1;</code>
     */
    protected $expect_info_text = '';
    /**
     * Generated from protobuf field <code>string sendorder_button_title = 2;</code>
     */
    protected $sendorder_button_title = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NavigationBar left_button = 3;</code>
     */
    protected $left_button = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $expect_info_text
     *     @type string $sendorder_button_title
     *     @type \Dirpc\SDK\PreSale\NavigationBar $left_button
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string expect_info_text = 1;</code>
     * @return string
     */
    public function getExpectInfoText()
    {
        return $this->expect_info_text;
    }

    /**
     * Generated from protobuf field <code>string expect_info_text = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setExpectInfoText($var)
    {
        GPBUtil::checkString($var, True);
        $this->expect_info_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string sendorder_button_title = 2;</code>
     * @return string
     */
    public function getSendorderButtonTitle()
    {
        return isset($this->sendorder_button_title) ? $this->sendorder_button_title : '';
    }

    public function hasSendorderButtonTitle()
    {
        return isset($this->sendorder_button_title);
    }

    public function clearSendorderButtonTitle()
    {
        unset($this->sendorder_button_title);
    }

    /**
     * Generated from protobuf field <code>string sendorder_button_title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setSendorderButtonTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->sendorder_button_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NavigationBar left_button = 3;</code>
     * @return \Dirpc\SDK\PreSale\NavigationBar
     */
    public function getLeftButton()
    {
        return isset($this->left_button) ? $this->left_button : null;
    }

    public function hasLeftButton()
    {
        return isset($this->left_button);
    }

    public function clearLeftButton()
    {
        unset($this->left_button);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NavigationBar left_button = 3;</code>
     * @param \Dirpc\SDK\PreSale\NavigationBar $var
     * @return $this
     */
    public function setLeftButton($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NavigationBar::class);
        $this->left_button = $var;

        return $this;
    }

}

