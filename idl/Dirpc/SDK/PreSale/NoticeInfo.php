<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NoticeInfo</code>
 */
class NoticeInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.Tag tag_list = 1;</code>
     */
    private $tag_list;
    /**
     * Generated from protobuf field <code>string right_text = 2;</code>
     */
    protected $right_text = null;
    /**
     * Generated from protobuf field <code>string jump_url = 3;</code>
     */
    protected $jump_url = null;
    /**
     * Generated from protobuf field <code>repeated string background_gradients = 4;</code>
     */
    private $background_gradients;
    /**
     * Generated from protobuf field <code>string border_color = 5;</code>
     */
    protected $border_color = null;
    /**
     * Generated from protobuf field <code>int32 action_type = 6;</code>
     */
    protected $action_type = null;
    /**
     * Generated from protobuf field <code>string bubble_arrow_top = 7;</code>
     */
    protected $bubble_arrow_top = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OmegaItem omega = 8;</code>
     */
    protected $omega = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\Tag[]|\Nuwa\Protobuf\Internal\RepeatedField $tag_list
     *     @type string $right_text
     *     @type string $jump_url
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $background_gradients
     *     @type string $border_color
     *     @type int $action_type
     *     @type string $bubble_arrow_top
     *     @type \Dirpc\SDK\PreSale\OmegaItem $omega
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.Tag tag_list = 1;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getTagList()
    {
        return $this->tag_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.Tag tag_list = 1;</code>
     * @param \Dirpc\SDK\PreSale\Tag[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setTagList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\Tag::class);
        $this->tag_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string right_text = 2;</code>
     * @return string
     */
    public function getRightText()
    {
        return isset($this->right_text) ? $this->right_text : '';
    }

    public function hasRightText()
    {
        return isset($this->right_text);
    }

    public function clearRightText()
    {
        unset($this->right_text);
    }

    /**
     * Generated from protobuf field <code>string right_text = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setRightText($var)
    {
        GPBUtil::checkString($var, True);
        $this->right_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string jump_url = 3;</code>
     * @return string
     */
    public function getJumpUrl()
    {
        return isset($this->jump_url) ? $this->jump_url : '';
    }

    public function hasJumpUrl()
    {
        return isset($this->jump_url);
    }

    public function clearJumpUrl()
    {
        unset($this->jump_url);
    }

    /**
     * Generated from protobuf field <code>string jump_url = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setJumpUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->jump_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated string background_gradients = 4;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getBackgroundGradients()
    {
        return $this->background_gradients;
    }

    /**
     * Generated from protobuf field <code>repeated string background_gradients = 4;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBackgroundGradients($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->background_gradients = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string border_color = 5;</code>
     * @return string
     */
    public function getBorderColor()
    {
        return isset($this->border_color) ? $this->border_color : '';
    }

    public function hasBorderColor()
    {
        return isset($this->border_color);
    }

    public function clearBorderColor()
    {
        unset($this->border_color);
    }

    /**
     * Generated from protobuf field <code>string border_color = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setBorderColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->border_color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 action_type = 6;</code>
     * @return int
     */
    public function getActionType()
    {
        return isset($this->action_type) ? $this->action_type : 0;
    }

    public function hasActionType()
    {
        return isset($this->action_type);
    }

    public function clearActionType()
    {
        unset($this->action_type);
    }

    /**
     * Generated from protobuf field <code>int32 action_type = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setActionType($var)
    {
        GPBUtil::checkInt32($var);
        $this->action_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string bubble_arrow_top = 7;</code>
     * @return string
     */
    public function getBubbleArrowTop()
    {
        return isset($this->bubble_arrow_top) ? $this->bubble_arrow_top : '';
    }

    public function hasBubbleArrowTop()
    {
        return isset($this->bubble_arrow_top);
    }

    public function clearBubbleArrowTop()
    {
        unset($this->bubble_arrow_top);
    }

    /**
     * Generated from protobuf field <code>string bubble_arrow_top = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setBubbleArrowTop($var)
    {
        GPBUtil::checkString($var, True);
        $this->bubble_arrow_top = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OmegaItem omega = 8;</code>
     * @return \Dirpc\SDK\PreSale\OmegaItem
     */
    public function getOmega()
    {
        return isset($this->omega) ? $this->omega : null;
    }

    public function hasOmega()
    {
        return isset($this->omega);
    }

    public function clearOmega()
    {
        unset($this->omega);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OmegaItem omega = 8;</code>
     * @param \Dirpc\SDK\PreSale\OmegaItem $var
     * @return $this
     */
    public function setOmega($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\OmegaItem::class);
        $this->omega = $var;

        return $this;
    }

}

