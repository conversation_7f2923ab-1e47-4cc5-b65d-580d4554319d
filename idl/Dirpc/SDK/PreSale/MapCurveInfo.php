<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.MapCurveInfo</code>
 */
class MapCurveInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 is_draw_curve = 1;</code>
     */
    protected $is_draw_curve = 0;
    /**
     * Generated from protobuf field <code>string bubble_text = 2;</code>
     */
    protected $bubble_text = '';
    /**
     * Generated from protobuf field <code>string jump_url = 3;</code>
     */
    protected $jump_url = '';
    /**
     *曲线类型：0-不展示曲线；1-展示蓝灰曲线（小巴快线）；2-展示蓝色曲线（小巴普通&智能小巴）【na: 7.0.6, wx: 6.10.25以后使用】
     *
     * Generated from protobuf field <code>int32 curve_type = 4;</code>
     */
    protected $curve_type = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $is_draw_curve
     *     @type string $bubble_text
     *     @type string $jump_url
     *     @type int $curve_type
     *          曲线类型：0-不展示曲线；1-展示蓝灰曲线（小巴快线）；2-展示蓝色曲线（小巴普通&智能小巴）【na: 7.0.6, wx: 6.10.25以后使用】
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 is_draw_curve = 1;</code>
     * @return int
     */
    public function getIsDrawCurve()
    {
        return $this->is_draw_curve;
    }

    /**
     * Generated from protobuf field <code>int32 is_draw_curve = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setIsDrawCurve($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_draw_curve = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string bubble_text = 2;</code>
     * @return string
     */
    public function getBubbleText()
    {
        return $this->bubble_text;
    }

    /**
     * Generated from protobuf field <code>string bubble_text = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setBubbleText($var)
    {
        GPBUtil::checkString($var, True);
        $this->bubble_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string jump_url = 3;</code>
     * @return string
     */
    public function getJumpUrl()
    {
        return $this->jump_url;
    }

    /**
     * Generated from protobuf field <code>string jump_url = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setJumpUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->jump_url = $var;

        return $this;
    }

    /**
     *曲线类型：0-不展示曲线；1-展示蓝灰曲线（小巴快线）；2-展示蓝色曲线（小巴普通&智能小巴）【na: 7.0.6, wx: 6.10.25以后使用】
     *
     * Generated from protobuf field <code>int32 curve_type = 4;</code>
     * @return int
     */
    public function getCurveType()
    {
        return isset($this->curve_type) ? $this->curve_type : 0;
    }

    public function hasCurveType()
    {
        return isset($this->curve_type);
    }

    public function clearCurveType()
    {
        unset($this->curve_type);
    }

    /**
     *曲线类型：0-不展示曲线；1-展示蓝灰曲线（小巴快线）；2-展示蓝色曲线（小巴普通&智能小巴）【na: 7.0.6, wx: 6.10.25以后使用】
     *
     * Generated from protobuf field <code>int32 curve_type = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setCurveType($var)
    {
        GPBUtil::checkInt32($var);
        $this->curve_type = $var;

        return $this;
    }

}

