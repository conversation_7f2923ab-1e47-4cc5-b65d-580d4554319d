<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SubTitleInfo</code>
 */
class SubTitleInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string content = 1;</code>
     */
    protected $content = '';
    /**
     * Generated from protobuf field <code>string font_color = 2;</code>
     */
    protected $font_color = null;
    /**
     * Generated from protobuf field <code>string background_color = 3;</code>
     */
    protected $background_color = null;
    /**
     * Generated from protobuf field <code>string border_color = 4;</code>
     */
    protected $border_color = null;
    /**
     * Generated from protobuf field <code>string icon_url = 5;</code>
     */
    protected $icon_url = null;
    /**
     * Generated from protobuf field <code>string background_transparency = 6;</code>
     */
    protected $background_transparency = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $content
     *     @type string $font_color
     *     @type string $background_color
     *     @type string $border_color
     *     @type string $icon_url
     *     @type string $background_transparency
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string content = 1;</code>
     * @return string
     */
    public function getContent()
    {
        return $this->content;
    }

    /**
     * Generated from protobuf field <code>string content = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setContent($var)
    {
        GPBUtil::checkString($var, True);
        $this->content = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string font_color = 2;</code>
     * @return string
     */
    public function getFontColor()
    {
        return isset($this->font_color) ? $this->font_color : '';
    }

    public function hasFontColor()
    {
        return isset($this->font_color);
    }

    public function clearFontColor()
    {
        unset($this->font_color);
    }

    /**
     * Generated from protobuf field <code>string font_color = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setFontColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->font_color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string background_color = 3;</code>
     * @return string
     */
    public function getBackgroundColor()
    {
        return isset($this->background_color) ? $this->background_color : '';
    }

    public function hasBackgroundColor()
    {
        return isset($this->background_color);
    }

    public function clearBackgroundColor()
    {
        unset($this->background_color);
    }

    /**
     * Generated from protobuf field <code>string background_color = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setBackgroundColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->background_color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string border_color = 4;</code>
     * @return string
     */
    public function getBorderColor()
    {
        return isset($this->border_color) ? $this->border_color : '';
    }

    public function hasBorderColor()
    {
        return isset($this->border_color);
    }

    public function clearBorderColor()
    {
        unset($this->border_color);
    }

    /**
     * Generated from protobuf field <code>string border_color = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setBorderColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->border_color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string icon_url = 5;</code>
     * @return string
     */
    public function getIconUrl()
    {
        return isset($this->icon_url) ? $this->icon_url : '';
    }

    public function hasIconUrl()
    {
        return isset($this->icon_url);
    }

    public function clearIconUrl()
    {
        unset($this->icon_url);
    }

    /**
     * Generated from protobuf field <code>string icon_url = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setIconUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->icon_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string background_transparency = 6;</code>
     * @return string
     */
    public function getBackgroundTransparency()
    {
        return isset($this->background_transparency) ? $this->background_transparency : '';
    }

    public function hasBackgroundTransparency()
    {
        return isset($this->background_transparency);
    }

    public function clearBackgroundTransparency()
    {
        unset($this->background_transparency);
    }

    /**
     * Generated from protobuf field <code>string background_transparency = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setBackgroundTransparency($var)
    {
        GPBUtil::checkString($var, True);
        $this->background_transparency = $var;

        return $this;
    }

}

