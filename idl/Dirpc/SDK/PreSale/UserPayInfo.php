<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.UserPayInfo</code>
 */
class UserPayInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *标题
     *
     * Generated from protobuf field <code>string title = 1;</code>
     */
    protected $title = '';
    /**
     *副标题
     *
     * Generated from protobuf field <code>string sub_title = 2;</code>
     */
    protected $sub_title = '';
    /**
     *支付列表
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.UserPayInfoItem payment_list = 3;</code>
     */
    private $payment_list;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $title
     *          标题
     *     @type string $sub_title
     *          副标题
     *     @type \Dirpc\SDK\PreSale\UserPayInfoItem[]|\Nuwa\Protobuf\Internal\RepeatedField $payment_list
     *          支付列表
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *标题
     *
     * Generated from protobuf field <code>string title = 1;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     *标题
     *
     * Generated from protobuf field <code>string title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     *副标题
     *
     * Generated from protobuf field <code>string sub_title = 2;</code>
     * @return string
     */
    public function getSubTitle()
    {
        return $this->sub_title;
    }

    /**
     *副标题
     *
     * Generated from protobuf field <code>string sub_title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setSubTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_title = $var;

        return $this;
    }

    /**
     *支付列表
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.UserPayInfoItem payment_list = 3;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getPaymentList()
    {
        return $this->payment_list;
    }

    /**
     *支付列表
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.UserPayInfoItem payment_list = 3;</code>
     * @param \Dirpc\SDK\PreSale\UserPayInfoItem[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setPaymentList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\UserPayInfoItem::class);
        $this->payment_list = $arr;

        return $this;
    }

}

