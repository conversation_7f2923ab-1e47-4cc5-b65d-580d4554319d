<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.BargainRangePopup</code>
 */
class BargainRangePopup extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *价格滑动条上界
     *
     * Generated from protobuf field <code>double price_limit_upper = 1;</code>
     */
    protected $price_limit_upper = 0.0;
    /**
     *价格滑动条下界
     *
     * Generated from protobuf field <code>double price_limit_lower = 2;</code>
     */
    protected $price_limit_lower = 0.0;
    /**
     *推荐价上界
     *
     * Generated from protobuf field <code>double recommend_price_upper = 3;</code>
     */
    protected $recommend_price_upper = 0.0;
    /**
     *推荐价下界
     *
     * Generated from protobuf field <code>double recommend_price_lower = 4;</code>
     */
    protected $recommend_price_lower = 0.0;
    /**
     *等应答最高价
     *
     * Generated from protobuf field <code>double wait_reply_price_upper = 5;</code>
     */
    protected $wait_reply_price_upper = null;
    /**
     *快车预估实付价格
     *
     * Generated from protobuf field <code>double fast_car_estimate_fee = 6;</code>
     */
    protected $fast_car_estimate_fee = 0.0;
    /**
     *特惠快车预估实付价格
     *
     * Generated from protobuf field <code>double sp_fast_car_estimate_fee = 7;</code>
     */
    protected $sp_fast_car_estimate_fee = 0.0;
    /**
     *弹窗标题
     *
     * Generated from protobuf field <code>string title = 8;</code>
     */
    protected $title = '';
    /**
     *弹窗副标题
     *
     * Generated from protobuf field <code>string sub_title = 9;</code>
     */
    protected $sub_title = '';
    /**
     *价格上界锚点文案
     *
     * Generated from protobuf field <code>string price_limit_upper_text = 10;</code>
     */
    protected $price_limit_upper_text = '';
    /**
     *价低文案提示
     *
     * Generated from protobuf field <code>string low_price_bubble_text = 11;</code>
     */
    protected $low_price_bubble_text = '';
    /**
     *加价文案提示
     *
     * Generated from protobuf field <code>string high_price_bubble_text = 12;</code>
     */
    protected $high_price_bubble_text = '';
    /**
     *确认出价文案
     *
     * Generated from protobuf field <code>string btn_text = 13;</code>
     */
    protected $btn_text = '';
    /**
     *价格下划线颜色
     *
     * Generated from protobuf field <code>string underline_color = 14;</code>
     */
    protected $underline_color = '';
    /**
     *弹窗背景图
     *
     * Generated from protobuf field <code>string background_img = 15;</code>
     */
    protected $background_img = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type float $price_limit_upper
     *          价格滑动条上界
     *     @type float $price_limit_lower
     *          价格滑动条下界
     *     @type float $recommend_price_upper
     *          推荐价上界
     *     @type float $recommend_price_lower
     *          推荐价下界
     *     @type float $wait_reply_price_upper
     *          等应答最高价
     *     @type float $fast_car_estimate_fee
     *          快车预估实付价格
     *     @type float $sp_fast_car_estimate_fee
     *          特惠快车预估实付价格
     *     @type string $title
     *          弹窗标题
     *     @type string $sub_title
     *          弹窗副标题
     *     @type string $price_limit_upper_text
     *          价格上界锚点文案
     *     @type string $low_price_bubble_text
     *          价低文案提示
     *     @type string $high_price_bubble_text
     *          加价文案提示
     *     @type string $btn_text
     *          确认出价文案
     *     @type string $underline_color
     *          价格下划线颜色
     *     @type string $background_img
     *          弹窗背景图
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *价格滑动条上界
     *
     * Generated from protobuf field <code>double price_limit_upper = 1;</code>
     * @return float
     */
    public function getPriceLimitUpper()
    {
        return $this->price_limit_upper;
    }

    /**
     *价格滑动条上界
     *
     * Generated from protobuf field <code>double price_limit_upper = 1;</code>
     * @param float $var
     * @return $this
     */
    public function setPriceLimitUpper($var)
    {
        GPBUtil::checkDouble($var);
        $this->price_limit_upper = $var;

        return $this;
    }

    /**
     *价格滑动条下界
     *
     * Generated from protobuf field <code>double price_limit_lower = 2;</code>
     * @return float
     */
    public function getPriceLimitLower()
    {
        return $this->price_limit_lower;
    }

    /**
     *价格滑动条下界
     *
     * Generated from protobuf field <code>double price_limit_lower = 2;</code>
     * @param float $var
     * @return $this
     */
    public function setPriceLimitLower($var)
    {
        GPBUtil::checkDouble($var);
        $this->price_limit_lower = $var;

        return $this;
    }

    /**
     *推荐价上界
     *
     * Generated from protobuf field <code>double recommend_price_upper = 3;</code>
     * @return float
     */
    public function getRecommendPriceUpper()
    {
        return $this->recommend_price_upper;
    }

    /**
     *推荐价上界
     *
     * Generated from protobuf field <code>double recommend_price_upper = 3;</code>
     * @param float $var
     * @return $this
     */
    public function setRecommendPriceUpper($var)
    {
        GPBUtil::checkDouble($var);
        $this->recommend_price_upper = $var;

        return $this;
    }

    /**
     *推荐价下界
     *
     * Generated from protobuf field <code>double recommend_price_lower = 4;</code>
     * @return float
     */
    public function getRecommendPriceLower()
    {
        return $this->recommend_price_lower;
    }

    /**
     *推荐价下界
     *
     * Generated from protobuf field <code>double recommend_price_lower = 4;</code>
     * @param float $var
     * @return $this
     */
    public function setRecommendPriceLower($var)
    {
        GPBUtil::checkDouble($var);
        $this->recommend_price_lower = $var;

        return $this;
    }

    /**
     *等应答最高价
     *
     * Generated from protobuf field <code>double wait_reply_price_upper = 5;</code>
     * @return float
     */
    public function getWaitReplyPriceUpper()
    {
        return isset($this->wait_reply_price_upper) ? $this->wait_reply_price_upper : 0.0;
    }

    public function hasWaitReplyPriceUpper()
    {
        return isset($this->wait_reply_price_upper);
    }

    public function clearWaitReplyPriceUpper()
    {
        unset($this->wait_reply_price_upper);
    }

    /**
     *等应答最高价
     *
     * Generated from protobuf field <code>double wait_reply_price_upper = 5;</code>
     * @param float $var
     * @return $this
     */
    public function setWaitReplyPriceUpper($var)
    {
        GPBUtil::checkDouble($var);
        $this->wait_reply_price_upper = $var;

        return $this;
    }

    /**
     *快车预估实付价格
     *
     * Generated from protobuf field <code>double fast_car_estimate_fee = 6;</code>
     * @return float
     */
    public function getFastCarEstimateFee()
    {
        return $this->fast_car_estimate_fee;
    }

    /**
     *快车预估实付价格
     *
     * Generated from protobuf field <code>double fast_car_estimate_fee = 6;</code>
     * @param float $var
     * @return $this
     */
    public function setFastCarEstimateFee($var)
    {
        GPBUtil::checkDouble($var);
        $this->fast_car_estimate_fee = $var;

        return $this;
    }

    /**
     *特惠快车预估实付价格
     *
     * Generated from protobuf field <code>double sp_fast_car_estimate_fee = 7;</code>
     * @return float
     */
    public function getSpFastCarEstimateFee()
    {
        return $this->sp_fast_car_estimate_fee;
    }

    /**
     *特惠快车预估实付价格
     *
     * Generated from protobuf field <code>double sp_fast_car_estimate_fee = 7;</code>
     * @param float $var
     * @return $this
     */
    public function setSpFastCarEstimateFee($var)
    {
        GPBUtil::checkDouble($var);
        $this->sp_fast_car_estimate_fee = $var;

        return $this;
    }

    /**
     *弹窗标题
     *
     * Generated from protobuf field <code>string title = 8;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     *弹窗标题
     *
     * Generated from protobuf field <code>string title = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     *弹窗副标题
     *
     * Generated from protobuf field <code>string sub_title = 9;</code>
     * @return string
     */
    public function getSubTitle()
    {
        return $this->sub_title;
    }

    /**
     *弹窗副标题
     *
     * Generated from protobuf field <code>string sub_title = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setSubTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_title = $var;

        return $this;
    }

    /**
     *价格上界锚点文案
     *
     * Generated from protobuf field <code>string price_limit_upper_text = 10;</code>
     * @return string
     */
    public function getPriceLimitUpperText()
    {
        return $this->price_limit_upper_text;
    }

    /**
     *价格上界锚点文案
     *
     * Generated from protobuf field <code>string price_limit_upper_text = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setPriceLimitUpperText($var)
    {
        GPBUtil::checkString($var, True);
        $this->price_limit_upper_text = $var;

        return $this;
    }

    /**
     *价低文案提示
     *
     * Generated from protobuf field <code>string low_price_bubble_text = 11;</code>
     * @return string
     */
    public function getLowPriceBubbleText()
    {
        return $this->low_price_bubble_text;
    }

    /**
     *价低文案提示
     *
     * Generated from protobuf field <code>string low_price_bubble_text = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setLowPriceBubbleText($var)
    {
        GPBUtil::checkString($var, True);
        $this->low_price_bubble_text = $var;

        return $this;
    }

    /**
     *加价文案提示
     *
     * Generated from protobuf field <code>string high_price_bubble_text = 12;</code>
     * @return string
     */
    public function getHighPriceBubbleText()
    {
        return $this->high_price_bubble_text;
    }

    /**
     *加价文案提示
     *
     * Generated from protobuf field <code>string high_price_bubble_text = 12;</code>
     * @param string $var
     * @return $this
     */
    public function setHighPriceBubbleText($var)
    {
        GPBUtil::checkString($var, True);
        $this->high_price_bubble_text = $var;

        return $this;
    }

    /**
     *确认出价文案
     *
     * Generated from protobuf field <code>string btn_text = 13;</code>
     * @return string
     */
    public function getBtnText()
    {
        return $this->btn_text;
    }

    /**
     *确认出价文案
     *
     * Generated from protobuf field <code>string btn_text = 13;</code>
     * @param string $var
     * @return $this
     */
    public function setBtnText($var)
    {
        GPBUtil::checkString($var, True);
        $this->btn_text = $var;

        return $this;
    }

    /**
     *价格下划线颜色
     *
     * Generated from protobuf field <code>string underline_color = 14;</code>
     * @return string
     */
    public function getUnderlineColor()
    {
        return $this->underline_color;
    }

    /**
     *价格下划线颜色
     *
     * Generated from protobuf field <code>string underline_color = 14;</code>
     * @param string $var
     * @return $this
     */
    public function setUnderlineColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->underline_color = $var;

        return $this;
    }

    /**
     *弹窗背景图
     *
     * Generated from protobuf field <code>string background_img = 15;</code>
     * @return string
     */
    public function getBackgroundImg()
    {
        return $this->background_img;
    }

    /**
     *弹窗背景图
     *
     * Generated from protobuf field <code>string background_img = 15;</code>
     * @param string $var
     * @return $this
     */
    public function setBackgroundImg($var)
    {
        GPBUtil::checkString($var, True);
        $this->background_img = $var;

        return $this;
    }

}

