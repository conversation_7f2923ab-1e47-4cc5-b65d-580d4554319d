<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.CategoryInfo</code>
 */
class CategoryInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *与estimate_data中的category_id对应
     *
     * Generated from protobuf field <code>int32 category_id = 1;</code>
     */
    protected $category_id = 0;
    /**
     *eg: 经济型/舒适型
     *
     * Generated from protobuf field <code>string name = 2;</code>
     */
    protected $name = '';
    /**
     *分类下划线颜色
     *
     * Generated from protobuf field <code>string underline_color = 3;</code>
     */
    protected $underline_color = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $category_id
     *          与estimate_data中的category_id对应
     *     @type string $name
     *          eg: 经济型/舒适型
     *     @type string $underline_color
     *          分类下划线颜色
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *与estimate_data中的category_id对应
     *
     * Generated from protobuf field <code>int32 category_id = 1;</code>
     * @return int
     */
    public function getCategoryId()
    {
        return $this->category_id;
    }

    /**
     *与estimate_data中的category_id对应
     *
     * Generated from protobuf field <code>int32 category_id = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setCategoryId($var)
    {
        GPBUtil::checkInt32($var);
        $this->category_id = $var;

        return $this;
    }

    /**
     *eg: 经济型/舒适型
     *
     * Generated from protobuf field <code>string name = 2;</code>
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     *eg: 经济型/舒适型
     *
     * Generated from protobuf field <code>string name = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, True);
        $this->name = $var;

        return $this;
    }

    /**
     *分类下划线颜色
     *
     * Generated from protobuf field <code>string underline_color = 3;</code>
     * @return string
     */
    public function getUnderlineColor()
    {
        return $this->underline_color;
    }

    /**
     *分类下划线颜色
     *
     * Generated from protobuf field <code>string underline_color = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setUnderlineColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->underline_color = $var;

        return $this;
    }

}

