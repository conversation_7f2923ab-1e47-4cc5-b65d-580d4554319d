<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.UserGuideInfo</code>
 */
class UserGuideInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string icon = 1;</code>
     */
    protected $icon = '';
    /**
     *0:无引导  1:纯小手  2:呼吸+小手
     *
     * Generated from protobuf field <code>int32 style = 2;</code>
     */
    protected $style = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $icon
     *     @type int $style
     *          0:无引导  1:纯小手  2:呼吸+小手
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string icon = 1;</code>
     * @return string
     */
    public function getIcon()
    {
        return $this->icon;
    }

    /**
     * Generated from protobuf field <code>string icon = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->icon = $var;

        return $this;
    }

    /**
     *0:无引导  1:纯小手  2:呼吸+小手
     *
     * Generated from protobuf field <code>int32 style = 2;</code>
     * @return int
     */
    public function getStyle()
    {
        return $this->style;
    }

    /**
     *0:无引导  1:纯小手  2:呼吸+小手
     *
     * Generated from protobuf field <code>int32 style = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setStyle($var)
    {
        GPBUtil::checkInt32($var);
        $this->style = $var;

        return $this;
    }

}

