<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideRuleTopContent</code>
 */
class SideRuleTopContent extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *标题 = 主标题 + 副标题
     *
     * Generated from protobuf field <code>string text = 1;</code>
     */
    protected $text = '';
    /**
     *标题颜色
     *
     * Generated from protobuf field <code>string text_color = 2;</code>
     */
    protected $text_color = '';
    /**
     *高亮颜色
     *
     * Generated from protobuf field <code>string text_highlight_color = 3;</code>
     */
    protected $text_highlight_color = null;
    /**
     *是否有箭头，仅style=0/1，端进行处理
     *
     * Generated from protobuf field <code>bool has_arrow = 4;</code>
     */
    protected $has_arrow = false;
    /**
     *跳转链接
     *
     * Generated from protobuf field <code>string link_url = 5;</code>
     */
    protected $link_url = '';
    /**
     *渲染风格；0代表一行文案居中；1代表一行文案居左；2代表上小下大；3代表上大下小；4代表双行主标题大小
     *
     * Generated from protobuf field <code>int32 style = 6;</code>
     */
    protected $style = 0;
    /**
     *交互样式，action_type:2 ，跳H5，action_type:3  请求任务
     *
     * Generated from protobuf field <code>int32 action_type = 7;</code>
     */
    protected $action_type = 0;
    /**
     *背景色：无值背景透明，有1个纯色，多个渐变
     *
     * Generated from protobuf field <code>repeated string background_gradients = 8;</code>
     */
    private $background_gradients;
    /**
     *背景图
     *
     * Generated from protobuf field <code>string bg_image = 9;</code>
     */
    protected $bg_image = null;
    /**
     *右边的按钮，style=1/2/3/4支持
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleButton button = 10;</code>
     */
    protected $button = null;
    /**
     *任务下发task信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleTask task = 11;</code>
     */
    protected $task = null;
    /**
     *左侧图标，仅在style=2/3/4时存在值
     *
     * Generated from protobuf field <code>string left_icon = 12;</code>
     */
    protected $left_icon = null;
    /**
     *领取权益信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRulePrivilege privilege = 13;</code>
     */
    protected $privilege = null;
    /**
     *是否因风控追加参数 0:不追加 1: 追加参数 "lat","lng", "ddfp"，
     *
     * Generated from protobuf field <code>int32 risk_control_judgment = 14;</code>
     */
    protected $risk_control_judgment = null;
    /**
     *请求link成功后的动作 1：请求预估 2：请求沟通接口
     *
     * Generated from protobuf field <code>int32 request_success_type = 15;</code>
     */
    protected $request_success_type = null;
    /**
     *请求link携带的参数
     *
     * Generated from protobuf field <code>map<string, string> link_params = 16;</code>
     */
    private $link_params;
    /**
     *第一行左边的icon，目前只在style为3、5时候有值
     *
     * Generated from protobuf field <code>string first_line_left_icon = 17;</code>
     */
    protected $first_line_left_icon = null;
    /**
     *请求link的方式：0:get 1:post
     *
     * Generated from protobuf field <code>int32 request_method = 18;</code>
     */
    protected $request_method = null;
    /**
     *过期时间，沟通组件倒计时使用
     *
     * Generated from protobuf field <code>int32 expire_time = 19;</code>
     */
    protected $expire_time = null;
    /**
     *第二行展示的标签们
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SideRuleElement second_line_elements = 20;</code>
     */
    private $second_line_elements;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $text
     *          标题 = 主标题 + 副标题
     *     @type string $text_color
     *          标题颜色
     *     @type string $text_highlight_color
     *          高亮颜色
     *     @type bool $has_arrow
     *          是否有箭头，仅style=0/1，端进行处理
     *     @type string $link_url
     *          跳转链接
     *     @type int $style
     *          渲染风格；0代表一行文案居中；1代表一行文案居左；2代表上小下大；3代表上大下小；4代表双行主标题大小
     *     @type int $action_type
     *          交互样式，action_type:2 ，跳H5，action_type:3  请求任务
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $background_gradients
     *          背景色：无值背景透明，有1个纯色，多个渐变
     *     @type string $bg_image
     *          背景图
     *     @type \Dirpc\SDK\PreSale\SideRuleButton $button
     *          右边的按钮，style=1/2/3/4支持
     *     @type \Dirpc\SDK\PreSale\SideRuleTask $task
     *          任务下发task信息
     *     @type string $left_icon
     *          左侧图标，仅在style=2/3/4时存在值
     *     @type \Dirpc\SDK\PreSale\SideRulePrivilege $privilege
     *          领取权益信息
     *     @type int $risk_control_judgment
     *          是否因风控追加参数 0:不追加 1: 追加参数 "lat","lng", "ddfp"，
     *     @type int $request_success_type
     *          请求link成功后的动作 1：请求预估 2：请求沟通接口
     *     @type array|\Nuwa\Protobuf\Internal\MapField $link_params
     *          请求link携带的参数
     *     @type string $first_line_left_icon
     *          第一行左边的icon，目前只在style为3、5时候有值
     *     @type int $request_method
     *          请求link的方式：0:get 1:post
     *     @type int $expire_time
     *          过期时间，沟通组件倒计时使用
     *     @type \Dirpc\SDK\PreSale\SideRuleElement[]|\Nuwa\Protobuf\Internal\RepeatedField $second_line_elements
     *          第二行展示的标签们
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     *标题 = 主标题 + 副标题
     *
     * Generated from protobuf field <code>string text = 1;</code>
     * @return string
     */
    public function getText()
    {
        return $this->text;
    }

    /**
     *标题 = 主标题 + 副标题
     *
     * Generated from protobuf field <code>string text = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setText($var)
    {
        GPBUtil::checkString($var, True);
        $this->text = $var;

        return $this;
    }

    /**
     *标题颜色
     *
     * Generated from protobuf field <code>string text_color = 2;</code>
     * @return string
     */
    public function getTextColor()
    {
        return $this->text_color;
    }

    /**
     *标题颜色
     *
     * Generated from protobuf field <code>string text_color = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTextColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->text_color = $var;

        return $this;
    }

    /**
     *高亮颜色
     *
     * Generated from protobuf field <code>string text_highlight_color = 3;</code>
     * @return string
     */
    public function getTextHighlightColor()
    {
        return isset($this->text_highlight_color) ? $this->text_highlight_color : '';
    }

    public function hasTextHighlightColor()
    {
        return isset($this->text_highlight_color);
    }

    public function clearTextHighlightColor()
    {
        unset($this->text_highlight_color);
    }

    /**
     *高亮颜色
     *
     * Generated from protobuf field <code>string text_highlight_color = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setTextHighlightColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->text_highlight_color = $var;

        return $this;
    }

    /**
     *是否有箭头，仅style=0/1，端进行处理
     *
     * Generated from protobuf field <code>bool has_arrow = 4;</code>
     * @return bool
     */
    public function getHasArrow()
    {
        return $this->has_arrow;
    }

    /**
     *是否有箭头，仅style=0/1，端进行处理
     *
     * Generated from protobuf field <code>bool has_arrow = 4;</code>
     * @param bool $var
     * @return $this
     */
    public function setHasArrow($var)
    {
        GPBUtil::checkBool($var);
        $this->has_arrow = $var;

        return $this;
    }

    /**
     *跳转链接
     *
     * Generated from protobuf field <code>string link_url = 5;</code>
     * @return string
     */
    public function getLinkUrl()
    {
        return $this->link_url;
    }

    /**
     *跳转链接
     *
     * Generated from protobuf field <code>string link_url = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setLinkUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->link_url = $var;

        return $this;
    }

    /**
     *渲染风格；0代表一行文案居中；1代表一行文案居左；2代表上小下大；3代表上大下小；4代表双行主标题大小
     *
     * Generated from protobuf field <code>int32 style = 6;</code>
     * @return int
     */
    public function getStyle()
    {
        return $this->style;
    }

    /**
     *渲染风格；0代表一行文案居中；1代表一行文案居左；2代表上小下大；3代表上大下小；4代表双行主标题大小
     *
     * Generated from protobuf field <code>int32 style = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setStyle($var)
    {
        GPBUtil::checkInt32($var);
        $this->style = $var;

        return $this;
    }

    /**
     *交互样式，action_type:2 ，跳H5，action_type:3  请求任务
     *
     * Generated from protobuf field <code>int32 action_type = 7;</code>
     * @return int
     */
    public function getActionType()
    {
        return $this->action_type;
    }

    /**
     *交互样式，action_type:2 ，跳H5，action_type:3  请求任务
     *
     * Generated from protobuf field <code>int32 action_type = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setActionType($var)
    {
        GPBUtil::checkInt32($var);
        $this->action_type = $var;

        return $this;
    }

    /**
     *背景色：无值背景透明，有1个纯色，多个渐变
     *
     * Generated from protobuf field <code>repeated string background_gradients = 8;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getBackgroundGradients()
    {
        return $this->background_gradients;
    }

    /**
     *背景色：无值背景透明，有1个纯色，多个渐变
     *
     * Generated from protobuf field <code>repeated string background_gradients = 8;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBackgroundGradients($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->background_gradients = $arr;

        return $this;
    }

    /**
     *背景图
     *
     * Generated from protobuf field <code>string bg_image = 9;</code>
     * @return string
     */
    public function getBgImage()
    {
        return isset($this->bg_image) ? $this->bg_image : '';
    }

    public function hasBgImage()
    {
        return isset($this->bg_image);
    }

    public function clearBgImage()
    {
        unset($this->bg_image);
    }

    /**
     *背景图
     *
     * Generated from protobuf field <code>string bg_image = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setBgImage($var)
    {
        GPBUtil::checkString($var, True);
        $this->bg_image = $var;

        return $this;
    }

    /**
     *右边的按钮，style=1/2/3/4支持
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleButton button = 10;</code>
     * @return \Dirpc\SDK\PreSale\SideRuleButton
     */
    public function getButton()
    {
        return isset($this->button) ? $this->button : null;
    }

    public function hasButton()
    {
        return isset($this->button);
    }

    public function clearButton()
    {
        unset($this->button);
    }

    /**
     *右边的按钮，style=1/2/3/4支持
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleButton button = 10;</code>
     * @param \Dirpc\SDK\PreSale\SideRuleButton $var
     * @return $this
     */
    public function setButton($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\SideRuleButton::class);
        $this->button = $var;

        return $this;
    }

    /**
     *任务下发task信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleTask task = 11;</code>
     * @return \Dirpc\SDK\PreSale\SideRuleTask
     */
    public function getTask()
    {
        return isset($this->task) ? $this->task : null;
    }

    public function hasTask()
    {
        return isset($this->task);
    }

    public function clearTask()
    {
        unset($this->task);
    }

    /**
     *任务下发task信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleTask task = 11;</code>
     * @param \Dirpc\SDK\PreSale\SideRuleTask $var
     * @return $this
     */
    public function setTask($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\SideRuleTask::class);
        $this->task = $var;

        return $this;
    }

    /**
     *左侧图标，仅在style=2/3/4时存在值
     *
     * Generated from protobuf field <code>string left_icon = 12;</code>
     * @return string
     */
    public function getLeftIcon()
    {
        return isset($this->left_icon) ? $this->left_icon : '';
    }

    public function hasLeftIcon()
    {
        return isset($this->left_icon);
    }

    public function clearLeftIcon()
    {
        unset($this->left_icon);
    }

    /**
     *左侧图标，仅在style=2/3/4时存在值
     *
     * Generated from protobuf field <code>string left_icon = 12;</code>
     * @param string $var
     * @return $this
     */
    public function setLeftIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->left_icon = $var;

        return $this;
    }

    /**
     *领取权益信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRulePrivilege privilege = 13;</code>
     * @return \Dirpc\SDK\PreSale\SideRulePrivilege
     */
    public function getPrivilege()
    {
        return isset($this->privilege) ? $this->privilege : null;
    }

    public function hasPrivilege()
    {
        return isset($this->privilege);
    }

    public function clearPrivilege()
    {
        unset($this->privilege);
    }

    /**
     *领取权益信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRulePrivilege privilege = 13;</code>
     * @param \Dirpc\SDK\PreSale\SideRulePrivilege $var
     * @return $this
     */
    public function setPrivilege($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\SideRulePrivilege::class);
        $this->privilege = $var;

        return $this;
    }

    /**
     *是否因风控追加参数 0:不追加 1: 追加参数 "lat","lng", "ddfp"，
     *
     * Generated from protobuf field <code>int32 risk_control_judgment = 14;</code>
     * @return int
     */
    public function getRiskControlJudgment()
    {
        return isset($this->risk_control_judgment) ? $this->risk_control_judgment : 0;
    }

    public function hasRiskControlJudgment()
    {
        return isset($this->risk_control_judgment);
    }

    public function clearRiskControlJudgment()
    {
        unset($this->risk_control_judgment);
    }

    /**
     *是否因风控追加参数 0:不追加 1: 追加参数 "lat","lng", "ddfp"，
     *
     * Generated from protobuf field <code>int32 risk_control_judgment = 14;</code>
     * @param int $var
     * @return $this
     */
    public function setRiskControlJudgment($var)
    {
        GPBUtil::checkInt32($var);
        $this->risk_control_judgment = $var;

        return $this;
    }

    /**
     *请求link成功后的动作 1：请求预估 2：请求沟通接口
     *
     * Generated from protobuf field <code>int32 request_success_type = 15;</code>
     * @return int
     */
    public function getRequestSuccessType()
    {
        return isset($this->request_success_type) ? $this->request_success_type : 0;
    }

    public function hasRequestSuccessType()
    {
        return isset($this->request_success_type);
    }

    public function clearRequestSuccessType()
    {
        unset($this->request_success_type);
    }

    /**
     *请求link成功后的动作 1：请求预估 2：请求沟通接口
     *
     * Generated from protobuf field <code>int32 request_success_type = 15;</code>
     * @param int $var
     * @return $this
     */
    public function setRequestSuccessType($var)
    {
        GPBUtil::checkInt32($var);
        $this->request_success_type = $var;

        return $this;
    }

    /**
     *请求link携带的参数
     *
     * Generated from protobuf field <code>map<string, string> link_params = 16;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getLinkParams()
    {
        return $this->link_params;
    }

    /**
     *请求link携带的参数
     *
     * Generated from protobuf field <code>map<string, string> link_params = 16;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setLinkParams($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->link_params = $arr;

        return $this;
    }

    /**
     *第一行左边的icon，目前只在style为3、5时候有值
     *
     * Generated from protobuf field <code>string first_line_left_icon = 17;</code>
     * @return string
     */
    public function getFirstLineLeftIcon()
    {
        return isset($this->first_line_left_icon) ? $this->first_line_left_icon : '';
    }

    public function hasFirstLineLeftIcon()
    {
        return isset($this->first_line_left_icon);
    }

    public function clearFirstLineLeftIcon()
    {
        unset($this->first_line_left_icon);
    }

    /**
     *第一行左边的icon，目前只在style为3、5时候有值
     *
     * Generated from protobuf field <code>string first_line_left_icon = 17;</code>
     * @param string $var
     * @return $this
     */
    public function setFirstLineLeftIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->first_line_left_icon = $var;

        return $this;
    }

    /**
     *请求link的方式：0:get 1:post
     *
     * Generated from protobuf field <code>int32 request_method = 18;</code>
     * @return int
     */
    public function getRequestMethod()
    {
        return isset($this->request_method) ? $this->request_method : 0;
    }

    public function hasRequestMethod()
    {
        return isset($this->request_method);
    }

    public function clearRequestMethod()
    {
        unset($this->request_method);
    }

    /**
     *请求link的方式：0:get 1:post
     *
     * Generated from protobuf field <code>int32 request_method = 18;</code>
     * @param int $var
     * @return $this
     */
    public function setRequestMethod($var)
    {
        GPBUtil::checkInt32($var);
        $this->request_method = $var;

        return $this;
    }

    /**
     *过期时间，沟通组件倒计时使用
     *
     * Generated from protobuf field <code>int32 expire_time = 19;</code>
     * @return int
     */
    public function getExpireTime()
    {
        return isset($this->expire_time) ? $this->expire_time : 0;
    }

    public function hasExpireTime()
    {
        return isset($this->expire_time);
    }

    public function clearExpireTime()
    {
        unset($this->expire_time);
    }

    /**
     *过期时间，沟通组件倒计时使用
     *
     * Generated from protobuf field <code>int32 expire_time = 19;</code>
     * @param int $var
     * @return $this
     */
    public function setExpireTime($var)
    {
        GPBUtil::checkInt32($var);
        $this->expire_time = $var;

        return $this;
    }

    /**
     *第二行展示的标签们
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SideRuleElement second_line_elements = 20;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getSecondLineElements()
    {
        return $this->second_line_elements;
    }

    /**
     *第二行展示的标签们
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SideRuleElement second_line_elements = 20;</code>
     * @param \Dirpc\SDK\PreSale\SideRuleElement[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSecondLineElements($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\SideRuleElement::class);
        $this->second_line_elements = $arr;

        return $this;
    }

}

