<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NewFormEstimateData</code>
 */
class NewFormEstimateData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *标识
     *
     * Generated from protobuf field <code>string estimate_id = 1;</code>
     */
    protected $estimate_id = '';
    /**
     * Generated from protobuf field <code>int32 product_category = 2;</code>
     */
    protected $product_category = 0;
    /**
     *一些场景标识
     *
     * Generated from protobuf field <code>int32 hit_dynamic_price = 3;</code>
     */
    protected $hit_dynamic_price = 0;
    /**
     * Generated from protobuf field <code>int32 hit_show_h5_type = 4;</code>
     */
    protected $hit_show_h5_type = 0;
    /**
     * Generated from protobuf field <code>bool is_tripcloud = 5;</code>
     */
    protected $is_tripcloud = false;
    /**
     *车型数据
     *
     * Generated from protobuf field <code>string car_title = 6;</code>
     */
    protected $car_title = '';
    /**
     * Generated from protobuf field <code>string car_icon = 7;</code>
     */
    protected $car_icon = '';
    /**
     *价格信息
     *
     * Generated from protobuf field <code>string fee_amount = 8;</code>
     */
    protected $fee_amount = '';
    /**
     * Generated from protobuf field <code>string fee_msg = 9;</code>
     */
    protected $fee_msg = '';
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 10;</code>
     */
    private $fee_desc_list;
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormMultiPrice multi_price_list = 11;</code>
     */
    private $multi_price_list;
    /**
     * Generated from protobuf field <code>string fee_msg_template = 42;</code>
     */
    protected $fee_msg_template = null;
    /**
     *费用描述前置icon
     *
     * Generated from protobuf field <code>string fee_msg_prefix_icon = 44;</code>
     */
    protected $fee_msg_prefix_icon = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormUnselectedData unselected_data = 27;</code>
     */
    protected $unselected_data = null;
    /**
     * Generated from protobuf field <code>string min_fee_amount = 37;</code>
     */
    protected $min_fee_amount = null;
    /**
     * Generated from protobuf field <code>string fee_range_template = 38;</code>
     */
    protected $fee_range_template = null;
    /**
     *支付信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormUserPayInfo user_pay_info = 12;</code>
     */
    protected $user_pay_info = null;
    /**
     *附加
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormPreferData prefer_data = 13;</code>
     */
    protected $prefer_data = null;
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormCarpoolSeatOption carpool_seat_list = 14;</code>
     */
    private $carpool_seat_list;
    /**
     * Generated from protobuf field <code>repeated string route_id_list = 15;</code>
     */
    private $route_id_list;
    /**
     *发单参数
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormExtraMap extra_map = 16;</code>
     */
    protected $extra_map = null;
    /**
     *木得办法
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormExtraEstimateData extra_estimate_data = 17;</code>
     */
    protected $extra_estimate_data = null;
    /**
     *木得办法
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormAutoDriveRedirectionInfo auto_driving_address_info = 18;</code>
     */
    protected $auto_driving_address_info = null;
    /**
     *勾选状态
     *
     * Generated from protobuf field <code>int32 is_selected = 19;</code>
     */
    protected $is_selected = 0;
    /**
     *勾选状态
     *
     * Generated from protobuf field <code>string depart_tag = 20;</code>
     */
    protected $depart_tag = null;
    /**
     *出租车盒子选中车型前的icon
     *
     * Generated from protobuf field <code>string sub_intro_icon = 21;</code>
     */
    protected $sub_intro_icon = null;
    /**
     *拼车选座组件/拼车顺路组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RadioSetting radio_setting = 22;</code>
     */
    protected $radio_setting = null;
    /**
     *市内拼车的顺路标签（实验，后续验证无收益记得下掉）
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OrderOption order_option = 23;</code>
     */
    protected $order_option = null;
    /**
     *车型标签
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.GroupSubTitle sub_title = 26;</code>
     */
    protected $sub_title = null;
    /**
     *是否计算价格 0:默认,需要计算价格  1:不计算
     *
     * Generated from protobuf field <code>int32 is_hide_price = 28;</code>
     */
    protected $is_hide_price = null;
    /**
     *券前价，目前只有司乘议价会用到
     *
     * Generated from protobuf field <code>double need_pay_fee_amount = 30;</code>
     */
    protected $need_pay_fee_amount = null;
    /**
     *小巴座位数组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewCarpoolSeatModule carpool_seat_module = 31;</code>
     */
    protected $carpool_seat_module = null;
    /**
     *品类下发横条+跳转组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NoticeInfo notice_info = 32;</code>
     */
    protected $notice_info = null;
    /**
     *不可用状态
     *
     * Generated from protobuf field <code>int32 disabled = 33;</code>
     */
    protected $disabled = null;
    /**
     *不可用信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.DisabledInfo disabled_info = 35;</code>
     */
    protected $disabled_info = null;
    /**
     *带给地图的数据
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.MapInfo map_info = 36;</code>
     */
    protected $map_info = null;
    /**
     *提示文案
     *
     * Generated from protobuf field <code>string multi_route_tip_type = 39;</code>
     */
    protected $multi_route_tip_type = null;
    /**
     *多勾惠选车弹窗数据
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BargainRangePopup bargain_range_popup = 40;</code>
     */
    protected $bargain_range_popup = null;
    /**
     *地图画线和气泡信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.MapCurveInfo map_curve_info = 41;</code>
     */
    protected $map_curve_info = null;
    /**
     *是否只展示一条预估路线 0否 1是
     *
     * Generated from protobuf field <code>int32 is_single_route = 43;</code>
     */
    protected $is_single_route = null;
    /**
     *三方车型icon类型 0（老的）, 1（单车型新的）
     *
     * Generated from protobuf field <code>int32 car_icon_type = 45;</code>
     */
    protected $car_icon_type = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $estimate_id
     *          标识
     *     @type int $product_category
     *     @type int $hit_dynamic_price
     *          一些场景标识
     *     @type int $hit_show_h5_type
     *     @type bool $is_tripcloud
     *     @type string $car_title
     *          车型数据
     *     @type string $car_icon
     *     @type string $fee_amount
     *          价格信息
     *     @type string $fee_msg
     *     @type \Dirpc\SDK\PreSale\NewFormFeeDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $fee_desc_list
     *     @type \Dirpc\SDK\PreSale\NewFormMultiPrice[]|\Nuwa\Protobuf\Internal\RepeatedField $multi_price_list
     *     @type string $fee_msg_template
     *     @type string $fee_msg_prefix_icon
     *          费用描述前置icon
     *     @type \Dirpc\SDK\PreSale\NewFormUnselectedData $unselected_data
     *     @type string $min_fee_amount
     *     @type string $fee_range_template
     *     @type \Dirpc\SDK\PreSale\NewFormUserPayInfo $user_pay_info
     *          支付信息
     *     @type \Dirpc\SDK\PreSale\NewFormPreferData $prefer_data
     *          附加
     *     @type \Dirpc\SDK\PreSale\NewFormCarpoolSeatOption[]|\Nuwa\Protobuf\Internal\RepeatedField $carpool_seat_list
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $route_id_list
     *     @type \Dirpc\SDK\PreSale\NewFormExtraMap $extra_map
     *          发单参数
     *     @type \Dirpc\SDK\PreSale\NewFormExtraEstimateData $extra_estimate_data
     *          木得办法
     *     @type \Dirpc\SDK\PreSale\NewFormAutoDriveRedirectionInfo $auto_driving_address_info
     *          木得办法
     *     @type int $is_selected
     *          勾选状态
     *     @type string $depart_tag
     *          勾选状态
     *     @type string $sub_intro_icon
     *          出租车盒子选中车型前的icon
     *     @type \Dirpc\SDK\PreSale\RadioSetting $radio_setting
     *          拼车选座组件/拼车顺路组件
     *     @type \Dirpc\SDK\PreSale\OrderOption $order_option
     *          市内拼车的顺路标签（实验，后续验证无收益记得下掉）
     *     @type \Dirpc\SDK\PreSale\GroupSubTitle $sub_title
     *          车型标签
     *     @type int $is_hide_price
     *          是否计算价格 0:默认,需要计算价格  1:不计算
     *     @type float $need_pay_fee_amount
     *          券前价，目前只有司乘议价会用到
     *     @type \Dirpc\SDK\PreSale\NewCarpoolSeatModule $carpool_seat_module
     *          小巴座位数组件
     *     @type \Dirpc\SDK\PreSale\NoticeInfo $notice_info
     *          品类下发横条+跳转组件
     *     @type int $disabled
     *          不可用状态
     *     @type \Dirpc\SDK\PreSale\DisabledInfo $disabled_info
     *          不可用信息
     *     @type \Dirpc\SDK\PreSale\MapInfo $map_info
     *          带给地图的数据
     *     @type string $multi_route_tip_type
     *          提示文案
     *     @type \Dirpc\SDK\PreSale\BargainRangePopup $bargain_range_popup
     *          多勾惠选车弹窗数据
     *     @type \Dirpc\SDK\PreSale\MapCurveInfo $map_curve_info
     *          地图画线和气泡信息
     *     @type int $is_single_route
     *          是否只展示一条预估路线 0否 1是
     *     @type int $car_icon_type
     *          三方车型icon类型 0（老的）, 1（单车型新的）
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *标识
     *
     * Generated from protobuf field <code>string estimate_id = 1;</code>
     * @return string
     */
    public function getEstimateId()
    {
        return $this->estimate_id;
    }

    /**
     *标识
     *
     * Generated from protobuf field <code>string estimate_id = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateId($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 product_category = 2;</code>
     * @return int
     */
    public function getProductCategory()
    {
        return $this->product_category;
    }

    /**
     * Generated from protobuf field <code>int32 product_category = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setProductCategory($var)
    {
        GPBUtil::checkInt32($var);
        $this->product_category = $var;

        return $this;
    }

    /**
     *一些场景标识
     *
     * Generated from protobuf field <code>int32 hit_dynamic_price = 3;</code>
     * @return int
     */
    public function getHitDynamicPrice()
    {
        return $this->hit_dynamic_price;
    }

    /**
     *一些场景标识
     *
     * Generated from protobuf field <code>int32 hit_dynamic_price = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setHitDynamicPrice($var)
    {
        GPBUtil::checkInt32($var);
        $this->hit_dynamic_price = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 hit_show_h5_type = 4;</code>
     * @return int
     */
    public function getHitShowH5Type()
    {
        return $this->hit_show_h5_type;
    }

    /**
     * Generated from protobuf field <code>int32 hit_show_h5_type = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setHitShowH5Type($var)
    {
        GPBUtil::checkInt32($var);
        $this->hit_show_h5_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool is_tripcloud = 5;</code>
     * @return bool
     */
    public function getIsTripcloud()
    {
        return $this->is_tripcloud;
    }

    /**
     * Generated from protobuf field <code>bool is_tripcloud = 5;</code>
     * @param bool $var
     * @return $this
     */
    public function setIsTripcloud($var)
    {
        GPBUtil::checkBool($var);
        $this->is_tripcloud = $var;

        return $this;
    }

    /**
     *车型数据
     *
     * Generated from protobuf field <code>string car_title = 6;</code>
     * @return string
     */
    public function getCarTitle()
    {
        return $this->car_title;
    }

    /**
     *车型数据
     *
     * Generated from protobuf field <code>string car_title = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setCarTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->car_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string car_icon = 7;</code>
     * @return string
     */
    public function getCarIcon()
    {
        return $this->car_icon;
    }

    /**
     * Generated from protobuf field <code>string car_icon = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setCarIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->car_icon = $var;

        return $this;
    }

    /**
     *价格信息
     *
     * Generated from protobuf field <code>string fee_amount = 8;</code>
     * @return string
     */
    public function getFeeAmount()
    {
        return $this->fee_amount;
    }

    /**
     *价格信息
     *
     * Generated from protobuf field <code>string fee_amount = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeAmount($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_amount = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_msg = 9;</code>
     * @return string
     */
    public function getFeeMsg()
    {
        return $this->fee_msg;
    }

    /**
     * Generated from protobuf field <code>string fee_msg = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 10;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getFeeDescList()
    {
        return $this->fee_desc_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc_list = 10;</code>
     * @param \Dirpc\SDK\PreSale\NewFormFeeDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFeeDescList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\NewFormFeeDesc::class);
        $this->fee_desc_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormMultiPrice multi_price_list = 11;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getMultiPriceList()
    {
        return $this->multi_price_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormMultiPrice multi_price_list = 11;</code>
     * @param \Dirpc\SDK\PreSale\NewFormMultiPrice[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setMultiPriceList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\NewFormMultiPrice::class);
        $this->multi_price_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_msg_template = 42;</code>
     * @return string
     */
    public function getFeeMsgTemplate()
    {
        return isset($this->fee_msg_template) ? $this->fee_msg_template : '';
    }

    public function hasFeeMsgTemplate()
    {
        return isset($this->fee_msg_template);
    }

    public function clearFeeMsgTemplate()
    {
        unset($this->fee_msg_template);
    }

    /**
     * Generated from protobuf field <code>string fee_msg_template = 42;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsgTemplate($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg_template = $var;

        return $this;
    }

    /**
     *费用描述前置icon
     *
     * Generated from protobuf field <code>string fee_msg_prefix_icon = 44;</code>
     * @return string
     */
    public function getFeeMsgPrefixIcon()
    {
        return isset($this->fee_msg_prefix_icon) ? $this->fee_msg_prefix_icon : '';
    }

    public function hasFeeMsgPrefixIcon()
    {
        return isset($this->fee_msg_prefix_icon);
    }

    public function clearFeeMsgPrefixIcon()
    {
        unset($this->fee_msg_prefix_icon);
    }

    /**
     *费用描述前置icon
     *
     * Generated from protobuf field <code>string fee_msg_prefix_icon = 44;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsgPrefixIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg_prefix_icon = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormUnselectedData unselected_data = 27;</code>
     * @return \Dirpc\SDK\PreSale\NewFormUnselectedData
     */
    public function getUnselectedData()
    {
        return isset($this->unselected_data) ? $this->unselected_data : null;
    }

    public function hasUnselectedData()
    {
        return isset($this->unselected_data);
    }

    public function clearUnselectedData()
    {
        unset($this->unselected_data);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormUnselectedData unselected_data = 27;</code>
     * @param \Dirpc\SDK\PreSale\NewFormUnselectedData $var
     * @return $this
     */
    public function setUnselectedData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NewFormUnselectedData::class);
        $this->unselected_data = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string min_fee_amount = 37;</code>
     * @return string
     */
    public function getMinFeeAmount()
    {
        return isset($this->min_fee_amount) ? $this->min_fee_amount : '';
    }

    public function hasMinFeeAmount()
    {
        return isset($this->min_fee_amount);
    }

    public function clearMinFeeAmount()
    {
        unset($this->min_fee_amount);
    }

    /**
     * Generated from protobuf field <code>string min_fee_amount = 37;</code>
     * @param string $var
     * @return $this
     */
    public function setMinFeeAmount($var)
    {
        GPBUtil::checkString($var, True);
        $this->min_fee_amount = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_range_template = 38;</code>
     * @return string
     */
    public function getFeeRangeTemplate()
    {
        return isset($this->fee_range_template) ? $this->fee_range_template : '';
    }

    public function hasFeeRangeTemplate()
    {
        return isset($this->fee_range_template);
    }

    public function clearFeeRangeTemplate()
    {
        unset($this->fee_range_template);
    }

    /**
     * Generated from protobuf field <code>string fee_range_template = 38;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeRangeTemplate($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_range_template = $var;

        return $this;
    }

    /**
     *支付信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormUserPayInfo user_pay_info = 12;</code>
     * @return \Dirpc\SDK\PreSale\NewFormUserPayInfo
     */
    public function getUserPayInfo()
    {
        return isset($this->user_pay_info) ? $this->user_pay_info : null;
    }

    public function hasUserPayInfo()
    {
        return isset($this->user_pay_info);
    }

    public function clearUserPayInfo()
    {
        unset($this->user_pay_info);
    }

    /**
     *支付信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormUserPayInfo user_pay_info = 12;</code>
     * @param \Dirpc\SDK\PreSale\NewFormUserPayInfo $var
     * @return $this
     */
    public function setUserPayInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NewFormUserPayInfo::class);
        $this->user_pay_info = $var;

        return $this;
    }

    /**
     *附加
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormPreferData prefer_data = 13;</code>
     * @return \Dirpc\SDK\PreSale\NewFormPreferData
     */
    public function getPreferData()
    {
        return isset($this->prefer_data) ? $this->prefer_data : null;
    }

    public function hasPreferData()
    {
        return isset($this->prefer_data);
    }

    public function clearPreferData()
    {
        unset($this->prefer_data);
    }

    /**
     *附加
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormPreferData prefer_data = 13;</code>
     * @param \Dirpc\SDK\PreSale\NewFormPreferData $var
     * @return $this
     */
    public function setPreferData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NewFormPreferData::class);
        $this->prefer_data = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormCarpoolSeatOption carpool_seat_list = 14;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getCarpoolSeatList()
    {
        return $this->carpool_seat_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormCarpoolSeatOption carpool_seat_list = 14;</code>
     * @param \Dirpc\SDK\PreSale\NewFormCarpoolSeatOption[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setCarpoolSeatList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\NewFormCarpoolSeatOption::class);
        $this->carpool_seat_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated string route_id_list = 15;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getRouteIdList()
    {
        return $this->route_id_list;
    }

    /**
     * Generated from protobuf field <code>repeated string route_id_list = 15;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setRouteIdList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->route_id_list = $arr;

        return $this;
    }

    /**
     *发单参数
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormExtraMap extra_map = 16;</code>
     * @return \Dirpc\SDK\PreSale\NewFormExtraMap
     */
    public function getExtraMap()
    {
        return isset($this->extra_map) ? $this->extra_map : null;
    }

    public function hasExtraMap()
    {
        return isset($this->extra_map);
    }

    public function clearExtraMap()
    {
        unset($this->extra_map);
    }

    /**
     *发单参数
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormExtraMap extra_map = 16;</code>
     * @param \Dirpc\SDK\PreSale\NewFormExtraMap $var
     * @return $this
     */
    public function setExtraMap($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NewFormExtraMap::class);
        $this->extra_map = $var;

        return $this;
    }

    /**
     *木得办法
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormExtraEstimateData extra_estimate_data = 17;</code>
     * @return \Dirpc\SDK\PreSale\NewFormExtraEstimateData
     */
    public function getExtraEstimateData()
    {
        return isset($this->extra_estimate_data) ? $this->extra_estimate_data : null;
    }

    public function hasExtraEstimateData()
    {
        return isset($this->extra_estimate_data);
    }

    public function clearExtraEstimateData()
    {
        unset($this->extra_estimate_data);
    }

    /**
     *木得办法
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormExtraEstimateData extra_estimate_data = 17;</code>
     * @param \Dirpc\SDK\PreSale\NewFormExtraEstimateData $var
     * @return $this
     */
    public function setExtraEstimateData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NewFormExtraEstimateData::class);
        $this->extra_estimate_data = $var;

        return $this;
    }

    /**
     *木得办法
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormAutoDriveRedirectionInfo auto_driving_address_info = 18;</code>
     * @return \Dirpc\SDK\PreSale\NewFormAutoDriveRedirectionInfo
     */
    public function getAutoDrivingAddressInfo()
    {
        return isset($this->auto_driving_address_info) ? $this->auto_driving_address_info : null;
    }

    public function hasAutoDrivingAddressInfo()
    {
        return isset($this->auto_driving_address_info);
    }

    public function clearAutoDrivingAddressInfo()
    {
        unset($this->auto_driving_address_info);
    }

    /**
     *木得办法
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormAutoDriveRedirectionInfo auto_driving_address_info = 18;</code>
     * @param \Dirpc\SDK\PreSale\NewFormAutoDriveRedirectionInfo $var
     * @return $this
     */
    public function setAutoDrivingAddressInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NewFormAutoDriveRedirectionInfo::class);
        $this->auto_driving_address_info = $var;

        return $this;
    }

    /**
     *勾选状态
     *
     * Generated from protobuf field <code>int32 is_selected = 19;</code>
     * @return int
     */
    public function getIsSelected()
    {
        return $this->is_selected;
    }

    /**
     *勾选状态
     *
     * Generated from protobuf field <code>int32 is_selected = 19;</code>
     * @param int $var
     * @return $this
     */
    public function setIsSelected($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_selected = $var;

        return $this;
    }

    /**
     *勾选状态
     *
     * Generated from protobuf field <code>string depart_tag = 20;</code>
     * @return string
     */
    public function getDepartTag()
    {
        return isset($this->depart_tag) ? $this->depart_tag : '';
    }

    public function hasDepartTag()
    {
        return isset($this->depart_tag);
    }

    public function clearDepartTag()
    {
        unset($this->depart_tag);
    }

    /**
     *勾选状态
     *
     * Generated from protobuf field <code>string depart_tag = 20;</code>
     * @param string $var
     * @return $this
     */
    public function setDepartTag($var)
    {
        GPBUtil::checkString($var, True);
        $this->depart_tag = $var;

        return $this;
    }

    /**
     *出租车盒子选中车型前的icon
     *
     * Generated from protobuf field <code>string sub_intro_icon = 21;</code>
     * @return string
     */
    public function getSubIntroIcon()
    {
        return isset($this->sub_intro_icon) ? $this->sub_intro_icon : '';
    }

    public function hasSubIntroIcon()
    {
        return isset($this->sub_intro_icon);
    }

    public function clearSubIntroIcon()
    {
        unset($this->sub_intro_icon);
    }

    /**
     *出租车盒子选中车型前的icon
     *
     * Generated from protobuf field <code>string sub_intro_icon = 21;</code>
     * @param string $var
     * @return $this
     */
    public function setSubIntroIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_intro_icon = $var;

        return $this;
    }

    /**
     *拼车选座组件/拼车顺路组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RadioSetting radio_setting = 22;</code>
     * @return \Dirpc\SDK\PreSale\RadioSetting
     */
    public function getRadioSetting()
    {
        return isset($this->radio_setting) ? $this->radio_setting : null;
    }

    public function hasRadioSetting()
    {
        return isset($this->radio_setting);
    }

    public function clearRadioSetting()
    {
        unset($this->radio_setting);
    }

    /**
     *拼车选座组件/拼车顺路组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.RadioSetting radio_setting = 22;</code>
     * @param \Dirpc\SDK\PreSale\RadioSetting $var
     * @return $this
     */
    public function setRadioSetting($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\RadioSetting::class);
        $this->radio_setting = $var;

        return $this;
    }

    /**
     *市内拼车的顺路标签（实验，后续验证无收益记得下掉）
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OrderOption order_option = 23;</code>
     * @return \Dirpc\SDK\PreSale\OrderOption
     */
    public function getOrderOption()
    {
        return isset($this->order_option) ? $this->order_option : null;
    }

    public function hasOrderOption()
    {
        return isset($this->order_option);
    }

    public function clearOrderOption()
    {
        unset($this->order_option);
    }

    /**
     *市内拼车的顺路标签（实验，后续验证无收益记得下掉）
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OrderOption order_option = 23;</code>
     * @param \Dirpc\SDK\PreSale\OrderOption $var
     * @return $this
     */
    public function setOrderOption($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\OrderOption::class);
        $this->order_option = $var;

        return $this;
    }

    /**
     *车型标签
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.GroupSubTitle sub_title = 26;</code>
     * @return \Dirpc\SDK\PreSale\GroupSubTitle
     */
    public function getSubTitle()
    {
        return isset($this->sub_title) ? $this->sub_title : null;
    }

    public function hasSubTitle()
    {
        return isset($this->sub_title);
    }

    public function clearSubTitle()
    {
        unset($this->sub_title);
    }

    /**
     *车型标签
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.GroupSubTitle sub_title = 26;</code>
     * @param \Dirpc\SDK\PreSale\GroupSubTitle $var
     * @return $this
     */
    public function setSubTitle($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\GroupSubTitle::class);
        $this->sub_title = $var;

        return $this;
    }

    /**
     *是否计算价格 0:默认,需要计算价格  1:不计算
     *
     * Generated from protobuf field <code>int32 is_hide_price = 28;</code>
     * @return int
     */
    public function getIsHidePrice()
    {
        return isset($this->is_hide_price) ? $this->is_hide_price : 0;
    }

    public function hasIsHidePrice()
    {
        return isset($this->is_hide_price);
    }

    public function clearIsHidePrice()
    {
        unset($this->is_hide_price);
    }

    /**
     *是否计算价格 0:默认,需要计算价格  1:不计算
     *
     * Generated from protobuf field <code>int32 is_hide_price = 28;</code>
     * @param int $var
     * @return $this
     */
    public function setIsHidePrice($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_hide_price = $var;

        return $this;
    }

    /**
     *券前价，目前只有司乘议价会用到
     *
     * Generated from protobuf field <code>double need_pay_fee_amount = 30;</code>
     * @return float
     */
    public function getNeedPayFeeAmount()
    {
        return isset($this->need_pay_fee_amount) ? $this->need_pay_fee_amount : 0.0;
    }

    public function hasNeedPayFeeAmount()
    {
        return isset($this->need_pay_fee_amount);
    }

    public function clearNeedPayFeeAmount()
    {
        unset($this->need_pay_fee_amount);
    }

    /**
     *券前价，目前只有司乘议价会用到
     *
     * Generated from protobuf field <code>double need_pay_fee_amount = 30;</code>
     * @param float $var
     * @return $this
     */
    public function setNeedPayFeeAmount($var)
    {
        GPBUtil::checkDouble($var);
        $this->need_pay_fee_amount = $var;

        return $this;
    }

    /**
     *小巴座位数组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewCarpoolSeatModule carpool_seat_module = 31;</code>
     * @return \Dirpc\SDK\PreSale\NewCarpoolSeatModule
     */
    public function getCarpoolSeatModule()
    {
        return isset($this->carpool_seat_module) ? $this->carpool_seat_module : null;
    }

    public function hasCarpoolSeatModule()
    {
        return isset($this->carpool_seat_module);
    }

    public function clearCarpoolSeatModule()
    {
        unset($this->carpool_seat_module);
    }

    /**
     *小巴座位数组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewCarpoolSeatModule carpool_seat_module = 31;</code>
     * @param \Dirpc\SDK\PreSale\NewCarpoolSeatModule $var
     * @return $this
     */
    public function setCarpoolSeatModule($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NewCarpoolSeatModule::class);
        $this->carpool_seat_module = $var;

        return $this;
    }

    /**
     *品类下发横条+跳转组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NoticeInfo notice_info = 32;</code>
     * @return \Dirpc\SDK\PreSale\NoticeInfo
     */
    public function getNoticeInfo()
    {
        return isset($this->notice_info) ? $this->notice_info : null;
    }

    public function hasNoticeInfo()
    {
        return isset($this->notice_info);
    }

    public function clearNoticeInfo()
    {
        unset($this->notice_info);
    }

    /**
     *品类下发横条+跳转组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NoticeInfo notice_info = 32;</code>
     * @param \Dirpc\SDK\PreSale\NoticeInfo $var
     * @return $this
     */
    public function setNoticeInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NoticeInfo::class);
        $this->notice_info = $var;

        return $this;
    }

    /**
     *不可用状态
     *
     * Generated from protobuf field <code>int32 disabled = 33;</code>
     * @return int
     */
    public function getDisabled()
    {
        return isset($this->disabled) ? $this->disabled : 0;
    }

    public function hasDisabled()
    {
        return isset($this->disabled);
    }

    public function clearDisabled()
    {
        unset($this->disabled);
    }

    /**
     *不可用状态
     *
     * Generated from protobuf field <code>int32 disabled = 33;</code>
     * @param int $var
     * @return $this
     */
    public function setDisabled($var)
    {
        GPBUtil::checkInt32($var);
        $this->disabled = $var;

        return $this;
    }

    /**
     *不可用信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.DisabledInfo disabled_info = 35;</code>
     * @return \Dirpc\SDK\PreSale\DisabledInfo
     */
    public function getDisabledInfo()
    {
        return isset($this->disabled_info) ? $this->disabled_info : null;
    }

    public function hasDisabledInfo()
    {
        return isset($this->disabled_info);
    }

    public function clearDisabledInfo()
    {
        unset($this->disabled_info);
    }

    /**
     *不可用信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.DisabledInfo disabled_info = 35;</code>
     * @param \Dirpc\SDK\PreSale\DisabledInfo $var
     * @return $this
     */
    public function setDisabledInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\DisabledInfo::class);
        $this->disabled_info = $var;

        return $this;
    }

    /**
     *带给地图的数据
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.MapInfo map_info = 36;</code>
     * @return \Dirpc\SDK\PreSale\MapInfo
     */
    public function getMapInfo()
    {
        return isset($this->map_info) ? $this->map_info : null;
    }

    public function hasMapInfo()
    {
        return isset($this->map_info);
    }

    public function clearMapInfo()
    {
        unset($this->map_info);
    }

    /**
     *带给地图的数据
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.MapInfo map_info = 36;</code>
     * @param \Dirpc\SDK\PreSale\MapInfo $var
     * @return $this
     */
    public function setMapInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\MapInfo::class);
        $this->map_info = $var;

        return $this;
    }

    /**
     *提示文案
     *
     * Generated from protobuf field <code>string multi_route_tip_type = 39;</code>
     * @return string
     */
    public function getMultiRouteTipType()
    {
        return isset($this->multi_route_tip_type) ? $this->multi_route_tip_type : '';
    }

    public function hasMultiRouteTipType()
    {
        return isset($this->multi_route_tip_type);
    }

    public function clearMultiRouteTipType()
    {
        unset($this->multi_route_tip_type);
    }

    /**
     *提示文案
     *
     * Generated from protobuf field <code>string multi_route_tip_type = 39;</code>
     * @param string $var
     * @return $this
     */
    public function setMultiRouteTipType($var)
    {
        GPBUtil::checkString($var, True);
        $this->multi_route_tip_type = $var;

        return $this;
    }

    /**
     *多勾惠选车弹窗数据
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BargainRangePopup bargain_range_popup = 40;</code>
     * @return \Dirpc\SDK\PreSale\BargainRangePopup
     */
    public function getBargainRangePopup()
    {
        return isset($this->bargain_range_popup) ? $this->bargain_range_popup : null;
    }

    public function hasBargainRangePopup()
    {
        return isset($this->bargain_range_popup);
    }

    public function clearBargainRangePopup()
    {
        unset($this->bargain_range_popup);
    }

    /**
     *多勾惠选车弹窗数据
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.BargainRangePopup bargain_range_popup = 40;</code>
     * @param \Dirpc\SDK\PreSale\BargainRangePopup $var
     * @return $this
     */
    public function setBargainRangePopup($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\BargainRangePopup::class);
        $this->bargain_range_popup = $var;

        return $this;
    }

    /**
     *地图画线和气泡信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.MapCurveInfo map_curve_info = 41;</code>
     * @return \Dirpc\SDK\PreSale\MapCurveInfo
     */
    public function getMapCurveInfo()
    {
        return isset($this->map_curve_info) ? $this->map_curve_info : null;
    }

    public function hasMapCurveInfo()
    {
        return isset($this->map_curve_info);
    }

    public function clearMapCurveInfo()
    {
        unset($this->map_curve_info);
    }

    /**
     *地图画线和气泡信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.MapCurveInfo map_curve_info = 41;</code>
     * @param \Dirpc\SDK\PreSale\MapCurveInfo $var
     * @return $this
     */
    public function setMapCurveInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\MapCurveInfo::class);
        $this->map_curve_info = $var;

        return $this;
    }

    /**
     *是否只展示一条预估路线 0否 1是
     *
     * Generated from protobuf field <code>int32 is_single_route = 43;</code>
     * @return int
     */
    public function getIsSingleRoute()
    {
        return isset($this->is_single_route) ? $this->is_single_route : 0;
    }

    public function hasIsSingleRoute()
    {
        return isset($this->is_single_route);
    }

    public function clearIsSingleRoute()
    {
        unset($this->is_single_route);
    }

    /**
     *是否只展示一条预估路线 0否 1是
     *
     * Generated from protobuf field <code>int32 is_single_route = 43;</code>
     * @param int $var
     * @return $this
     */
    public function setIsSingleRoute($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_single_route = $var;

        return $this;
    }

    /**
     *三方车型icon类型 0（老的）, 1（单车型新的）
     *
     * Generated from protobuf field <code>int32 car_icon_type = 45;</code>
     * @return int
     */
    public function getCarIconType()
    {
        return isset($this->car_icon_type) ? $this->car_icon_type : 0;
    }

    public function hasCarIconType()
    {
        return isset($this->car_icon_type);
    }

    public function clearCarIconType()
    {
        unset($this->car_icon_type);
    }

    /**
     *三方车型icon类型 0（老的）, 1（单车型新的）
     *
     * Generated from protobuf field <code>int32 car_icon_type = 45;</code>
     * @param int $var
     * @return $this
     */
    public function setCarIconType($var)
    {
        GPBUtil::checkInt32($var);
        $this->car_icon_type = $var;

        return $this;
    }

}

