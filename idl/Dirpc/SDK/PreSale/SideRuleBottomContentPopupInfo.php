<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideRuleBottomContentPopupInfo</code>
 */
class SideRuleBottomContentPopupInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *背景图url
     *
     * Generated from protobuf field <code>string top_img_url = 1;</code>
     */
    protected $top_img_url = '';
    /**
     *奖励图
     *
     * Generated from protobuf field <code>string img_url = 2;</code>
     */
    protected $img_url = '';
    /**
     *底部文案
     *
     * Generated from protobuf field <code>string bottom_text = 3;</code>
     */
    protected $bottom_text = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $top_img_url
     *          背景图url
     *     @type string $img_url
     *          奖励图
     *     @type string $bottom_text
     *          底部文案
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     *背景图url
     *
     * Generated from protobuf field <code>string top_img_url = 1;</code>
     * @return string
     */
    public function getTopImgUrl()
    {
        return $this->top_img_url;
    }

    /**
     *背景图url
     *
     * Generated from protobuf field <code>string top_img_url = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTopImgUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->top_img_url = $var;

        return $this;
    }

    /**
     *奖励图
     *
     * Generated from protobuf field <code>string img_url = 2;</code>
     * @return string
     */
    public function getImgUrl()
    {
        return $this->img_url;
    }

    /**
     *奖励图
     *
     * Generated from protobuf field <code>string img_url = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setImgUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->img_url = $var;

        return $this;
    }

    /**
     *底部文案
     *
     * Generated from protobuf field <code>string bottom_text = 3;</code>
     * @return string
     */
    public function getBottomText()
    {
        return $this->bottom_text;
    }

    /**
     *底部文案
     *
     * Generated from protobuf field <code>string bottom_text = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setBottomText($var)
    {
        GPBUtil::checkString($var, True);
        $this->bottom_text = $var;

        return $this;
    }

}

