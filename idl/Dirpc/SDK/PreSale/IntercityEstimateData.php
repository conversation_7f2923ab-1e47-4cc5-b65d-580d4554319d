<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.IntercityEstimateData</code>
 */
class IntercityEstimateData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *车型图片名称
     *
     * Generated from protobuf field <code>string intro_image = 1;</code>
     */
    protected $intro_image = '';
    /**
     *价格信息
     *
     * Generated from protobuf field <code>string fee_msg = 2;</code>
     */
    protected $fee_msg = '';
    /**
     *背景图
     *
     * Generated from protobuf field <code>string bg_image = 3;</code>
     */
    protected $bg_image = '';
    /**
     *特殊价格沟通
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.IntercitySpecialPriceText special_price_text = 4;</code>
     */
    protected $special_price_text = null;
    /**
     *边框色
     *
     * Generated from protobuf field <code>string border_color = 5;</code>
     */
    protected $border_color = '';
    /**
     *选中右上角图标
     *
     * Generated from protobuf field <code>string corner_image = 6;</code>
     */
    protected $corner_image = '';
    /**
     *路线类型
     *
     * Generated from protobuf field <code>int32 route_type = 7;</code>
     */
    protected $route_type = 0;
    /**
     *车型
     *
     * Generated from protobuf field <code>int32 require_level = 8;</code>
     */
    protected $require_level = 0;
    /**
     *业务线
     *
     * Generated from protobuf field <code>int32 business_id = 9;</code>
     */
    protected $business_id = 0;
    /**
     *产品线ID
     *
     * Generated from protobuf field <code>int32 product_id = 10;</code>
     */
    protected $product_id = 0;
    /**
     *combo_type
     *
     * Generated from protobuf field <code>int32 combo_type = 11;</code>
     */
    protected $combo_type = 0;
    /**
     *品类ID
     *
     * Generated from protobuf field <code>int32 product_category = 12;</code>
     */
    protected $product_category = 0;
    /**
     *预估id
     *
     * Generated from protobuf field <code>string estimate_id = 13;</code>
     */
    protected $estimate_id = '';
    /**
     *特点标题
     *
     * Generated from protobuf field <code>string feature_title = 14;</code>
     */
    protected $feature_title = '';
    /**
     *特点副标题
     *
     * Generated from protobuf field <code>string feature_sub_title = 15;</code>
     */
    protected $feature_sub_title = '';
    /**
     *价格信息第一行
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.IntercityPriceInfoDesc price_info_1 = 16;</code>
     */
    private $price_info_1;
    /**
     *价格信息第二行
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.IntercityPriceInfoDesc price_info_2 = 17;</code>
     */
    private $price_info_2;
    /**
     *拼车座位数组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.IntercityCarpoolSeatModule carpool_seat_module = 18;</code>
     */
    protected $carpool_seat_module = null;
    /**
     *城际拼车时间片组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.IntercityMatchRoutesData match_routes_data = 19;</code>
     */
    protected $match_routes_data = null;
    /**
     *路线ID，发单参数
     *
     * Generated from protobuf field <code>int32 combo_id = 20;</code>
     */
    protected $combo_id = 0;
    /**
     *是否默认勾选
     *
     * Generated from protobuf field <code>int32 select_type = 21;</code>
     */
    protected $select_type = 0;
    /**
     *单纯价格，供端埋点用
     *
     * Generated from protobuf field <code>string fee_amount = 23;</code>
     */
    protected $fee_amount = '';
    /**
     *user_pay_info
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.IntercityFormUserPayInfo user_pay_info = 24;</code>
     */
    protected $user_pay_info = null;
    /**
     *城际拼车卡片扩展库存推荐
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.IntercityMatchShowSkuData match_show_sku_data = 25;</code>
     */
    protected $match_show_sku_data = null;
    /**
     *城际拼车卡片是否可用，是否可以通过外漏的卡片直接发单
     *
     * Generated from protobuf field <code>bool is_unavailable = 26;</code>
     */
    protected $is_unavailable = null;
    /**
     *预估show h5命中类型
     *
     * Generated from protobuf field <code>int32 hit_show_h5_type = 27;</code>
     */
    protected $hit_show_h5_type = null;
    /**
     *是否支持选座弹层
     *
     * Generated from protobuf field <code>int32 support_select_seat = 28;</code>
     */
    protected $support_select_seat = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $intro_image
     *          车型图片名称
     *     @type string $fee_msg
     *          价格信息
     *     @type string $bg_image
     *          背景图
     *     @type \Dirpc\SDK\PreSale\IntercitySpecialPriceText $special_price_text
     *          特殊价格沟通
     *     @type string $border_color
     *          边框色
     *     @type string $corner_image
     *          选中右上角图标
     *     @type int $route_type
     *          路线类型
     *     @type int $require_level
     *          车型
     *     @type int $business_id
     *          业务线
     *     @type int $product_id
     *          产品线ID
     *     @type int $combo_type
     *          combo_type
     *     @type int $product_category
     *          品类ID
     *     @type string $estimate_id
     *          预估id
     *     @type string $feature_title
     *          特点标题
     *     @type string $feature_sub_title
     *          特点副标题
     *     @type \Dirpc\SDK\PreSale\IntercityPriceInfoDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $price_info_1
     *          价格信息第一行
     *     @type \Dirpc\SDK\PreSale\IntercityPriceInfoDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $price_info_2
     *          价格信息第二行
     *     @type \Dirpc\SDK\PreSale\IntercityCarpoolSeatModule $carpool_seat_module
     *          拼车座位数组件
     *     @type \Dirpc\SDK\PreSale\IntercityMatchRoutesData $match_routes_data
     *          城际拼车时间片组件
     *     @type int $combo_id
     *          路线ID，发单参数
     *     @type int $select_type
     *          是否默认勾选
     *     @type string $fee_amount
     *          单纯价格，供端埋点用
     *     @type \Dirpc\SDK\PreSale\IntercityFormUserPayInfo $user_pay_info
     *          user_pay_info
     *     @type \Dirpc\SDK\PreSale\IntercityMatchShowSkuData $match_show_sku_data
     *          城际拼车卡片扩展库存推荐
     *     @type bool $is_unavailable
     *          城际拼车卡片是否可用，是否可以通过外漏的卡片直接发单
     *     @type int $hit_show_h5_type
     *          预估show h5命中类型
     *     @type int $support_select_seat
     *          是否支持选座弹层
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *车型图片名称
     *
     * Generated from protobuf field <code>string intro_image = 1;</code>
     * @return string
     */
    public function getIntroImage()
    {
        return $this->intro_image;
    }

    /**
     *车型图片名称
     *
     * Generated from protobuf field <code>string intro_image = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setIntroImage($var)
    {
        GPBUtil::checkString($var, True);
        $this->intro_image = $var;

        return $this;
    }

    /**
     *价格信息
     *
     * Generated from protobuf field <code>string fee_msg = 2;</code>
     * @return string
     */
    public function getFeeMsg()
    {
        return $this->fee_msg;
    }

    /**
     *价格信息
     *
     * Generated from protobuf field <code>string fee_msg = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg = $var;

        return $this;
    }

    /**
     *背景图
     *
     * Generated from protobuf field <code>string bg_image = 3;</code>
     * @return string
     */
    public function getBgImage()
    {
        return $this->bg_image;
    }

    /**
     *背景图
     *
     * Generated from protobuf field <code>string bg_image = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setBgImage($var)
    {
        GPBUtil::checkString($var, True);
        $this->bg_image = $var;

        return $this;
    }

    /**
     *特殊价格沟通
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.IntercitySpecialPriceText special_price_text = 4;</code>
     * @return \Dirpc\SDK\PreSale\IntercitySpecialPriceText
     */
    public function getSpecialPriceText()
    {
        return isset($this->special_price_text) ? $this->special_price_text : null;
    }

    public function hasSpecialPriceText()
    {
        return isset($this->special_price_text);
    }

    public function clearSpecialPriceText()
    {
        unset($this->special_price_text);
    }

    /**
     *特殊价格沟通
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.IntercitySpecialPriceText special_price_text = 4;</code>
     * @param \Dirpc\SDK\PreSale\IntercitySpecialPriceText $var
     * @return $this
     */
    public function setSpecialPriceText($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\IntercitySpecialPriceText::class);
        $this->special_price_text = $var;

        return $this;
    }

    /**
     *边框色
     *
     * Generated from protobuf field <code>string border_color = 5;</code>
     * @return string
     */
    public function getBorderColor()
    {
        return $this->border_color;
    }

    /**
     *边框色
     *
     * Generated from protobuf field <code>string border_color = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setBorderColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->border_color = $var;

        return $this;
    }

    /**
     *选中右上角图标
     *
     * Generated from protobuf field <code>string corner_image = 6;</code>
     * @return string
     */
    public function getCornerImage()
    {
        return $this->corner_image;
    }

    /**
     *选中右上角图标
     *
     * Generated from protobuf field <code>string corner_image = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setCornerImage($var)
    {
        GPBUtil::checkString($var, True);
        $this->corner_image = $var;

        return $this;
    }

    /**
     *路线类型
     *
     * Generated from protobuf field <code>int32 route_type = 7;</code>
     * @return int
     */
    public function getRouteType()
    {
        return $this->route_type;
    }

    /**
     *路线类型
     *
     * Generated from protobuf field <code>int32 route_type = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setRouteType($var)
    {
        GPBUtil::checkInt32($var);
        $this->route_type = $var;

        return $this;
    }

    /**
     *车型
     *
     * Generated from protobuf field <code>int32 require_level = 8;</code>
     * @return int
     */
    public function getRequireLevel()
    {
        return $this->require_level;
    }

    /**
     *车型
     *
     * Generated from protobuf field <code>int32 require_level = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setRequireLevel($var)
    {
        GPBUtil::checkInt32($var);
        $this->require_level = $var;

        return $this;
    }

    /**
     *业务线
     *
     * Generated from protobuf field <code>int32 business_id = 9;</code>
     * @return int
     */
    public function getBusinessId()
    {
        return $this->business_id;
    }

    /**
     *业务线
     *
     * Generated from protobuf field <code>int32 business_id = 9;</code>
     * @param int $var
     * @return $this
     */
    public function setBusinessId($var)
    {
        GPBUtil::checkInt32($var);
        $this->business_id = $var;

        return $this;
    }

    /**
     *产品线ID
     *
     * Generated from protobuf field <code>int32 product_id = 10;</code>
     * @return int
     */
    public function getProductId()
    {
        return $this->product_id;
    }

    /**
     *产品线ID
     *
     * Generated from protobuf field <code>int32 product_id = 10;</code>
     * @param int $var
     * @return $this
     */
    public function setProductId($var)
    {
        GPBUtil::checkInt32($var);
        $this->product_id = $var;

        return $this;
    }

    /**
     *combo_type
     *
     * Generated from protobuf field <code>int32 combo_type = 11;</code>
     * @return int
     */
    public function getComboType()
    {
        return $this->combo_type;
    }

    /**
     *combo_type
     *
     * Generated from protobuf field <code>int32 combo_type = 11;</code>
     * @param int $var
     * @return $this
     */
    public function setComboType($var)
    {
        GPBUtil::checkInt32($var);
        $this->combo_type = $var;

        return $this;
    }

    /**
     *品类ID
     *
     * Generated from protobuf field <code>int32 product_category = 12;</code>
     * @return int
     */
    public function getProductCategory()
    {
        return $this->product_category;
    }

    /**
     *品类ID
     *
     * Generated from protobuf field <code>int32 product_category = 12;</code>
     * @param int $var
     * @return $this
     */
    public function setProductCategory($var)
    {
        GPBUtil::checkInt32($var);
        $this->product_category = $var;

        return $this;
    }

    /**
     *预估id
     *
     * Generated from protobuf field <code>string estimate_id = 13;</code>
     * @return string
     */
    public function getEstimateId()
    {
        return $this->estimate_id;
    }

    /**
     *预估id
     *
     * Generated from protobuf field <code>string estimate_id = 13;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateId($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_id = $var;

        return $this;
    }

    /**
     *特点标题
     *
     * Generated from protobuf field <code>string feature_title = 14;</code>
     * @return string
     */
    public function getFeatureTitle()
    {
        return $this->feature_title;
    }

    /**
     *特点标题
     *
     * Generated from protobuf field <code>string feature_title = 14;</code>
     * @param string $var
     * @return $this
     */
    public function setFeatureTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->feature_title = $var;

        return $this;
    }

    /**
     *特点副标题
     *
     * Generated from protobuf field <code>string feature_sub_title = 15;</code>
     * @return string
     */
    public function getFeatureSubTitle()
    {
        return $this->feature_sub_title;
    }

    /**
     *特点副标题
     *
     * Generated from protobuf field <code>string feature_sub_title = 15;</code>
     * @param string $var
     * @return $this
     */
    public function setFeatureSubTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->feature_sub_title = $var;

        return $this;
    }

    /**
     *价格信息第一行
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.IntercityPriceInfoDesc price_info_1 = 16;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getPriceInfo1()
    {
        return $this->price_info_1;
    }

    /**
     *价格信息第一行
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.IntercityPriceInfoDesc price_info_1 = 16;</code>
     * @param \Dirpc\SDK\PreSale\IntercityPriceInfoDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setPriceInfo1($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\IntercityPriceInfoDesc::class);
        $this->price_info_1 = $arr;

        return $this;
    }

    /**
     *价格信息第二行
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.IntercityPriceInfoDesc price_info_2 = 17;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getPriceInfo2()
    {
        return $this->price_info_2;
    }

    /**
     *价格信息第二行
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.IntercityPriceInfoDesc price_info_2 = 17;</code>
     * @param \Dirpc\SDK\PreSale\IntercityPriceInfoDesc[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setPriceInfo2($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\IntercityPriceInfoDesc::class);
        $this->price_info_2 = $arr;

        return $this;
    }

    /**
     *拼车座位数组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.IntercityCarpoolSeatModule carpool_seat_module = 18;</code>
     * @return \Dirpc\SDK\PreSale\IntercityCarpoolSeatModule
     */
    public function getCarpoolSeatModule()
    {
        return isset($this->carpool_seat_module) ? $this->carpool_seat_module : null;
    }

    public function hasCarpoolSeatModule()
    {
        return isset($this->carpool_seat_module);
    }

    public function clearCarpoolSeatModule()
    {
        unset($this->carpool_seat_module);
    }

    /**
     *拼车座位数组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.IntercityCarpoolSeatModule carpool_seat_module = 18;</code>
     * @param \Dirpc\SDK\PreSale\IntercityCarpoolSeatModule $var
     * @return $this
     */
    public function setCarpoolSeatModule($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\IntercityCarpoolSeatModule::class);
        $this->carpool_seat_module = $var;

        return $this;
    }

    /**
     *城际拼车时间片组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.IntercityMatchRoutesData match_routes_data = 19;</code>
     * @return \Dirpc\SDK\PreSale\IntercityMatchRoutesData
     */
    public function getMatchRoutesData()
    {
        return isset($this->match_routes_data) ? $this->match_routes_data : null;
    }

    public function hasMatchRoutesData()
    {
        return isset($this->match_routes_data);
    }

    public function clearMatchRoutesData()
    {
        unset($this->match_routes_data);
    }

    /**
     *城际拼车时间片组件
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.IntercityMatchRoutesData match_routes_data = 19;</code>
     * @param \Dirpc\SDK\PreSale\IntercityMatchRoutesData $var
     * @return $this
     */
    public function setMatchRoutesData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\IntercityMatchRoutesData::class);
        $this->match_routes_data = $var;

        return $this;
    }

    /**
     *路线ID，发单参数
     *
     * Generated from protobuf field <code>int32 combo_id = 20;</code>
     * @return int
     */
    public function getComboId()
    {
        return $this->combo_id;
    }

    /**
     *路线ID，发单参数
     *
     * Generated from protobuf field <code>int32 combo_id = 20;</code>
     * @param int $var
     * @return $this
     */
    public function setComboId($var)
    {
        GPBUtil::checkInt32($var);
        $this->combo_id = $var;

        return $this;
    }

    /**
     *是否默认勾选
     *
     * Generated from protobuf field <code>int32 select_type = 21;</code>
     * @return int
     */
    public function getSelectType()
    {
        return $this->select_type;
    }

    /**
     *是否默认勾选
     *
     * Generated from protobuf field <code>int32 select_type = 21;</code>
     * @param int $var
     * @return $this
     */
    public function setSelectType($var)
    {
        GPBUtil::checkInt32($var);
        $this->select_type = $var;

        return $this;
    }

    /**
     *单纯价格，供端埋点用
     *
     * Generated from protobuf field <code>string fee_amount = 23;</code>
     * @return string
     */
    public function getFeeAmount()
    {
        return $this->fee_amount;
    }

    /**
     *单纯价格，供端埋点用
     *
     * Generated from protobuf field <code>string fee_amount = 23;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeAmount($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_amount = $var;

        return $this;
    }

    /**
     *user_pay_info
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.IntercityFormUserPayInfo user_pay_info = 24;</code>
     * @return \Dirpc\SDK\PreSale\IntercityFormUserPayInfo
     */
    public function getUserPayInfo()
    {
        return isset($this->user_pay_info) ? $this->user_pay_info : null;
    }

    public function hasUserPayInfo()
    {
        return isset($this->user_pay_info);
    }

    public function clearUserPayInfo()
    {
        unset($this->user_pay_info);
    }

    /**
     *user_pay_info
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.IntercityFormUserPayInfo user_pay_info = 24;</code>
     * @param \Dirpc\SDK\PreSale\IntercityFormUserPayInfo $var
     * @return $this
     */
    public function setUserPayInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\IntercityFormUserPayInfo::class);
        $this->user_pay_info = $var;

        return $this;
    }

    /**
     *城际拼车卡片扩展库存推荐
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.IntercityMatchShowSkuData match_show_sku_data = 25;</code>
     * @return \Dirpc\SDK\PreSale\IntercityMatchShowSkuData
     */
    public function getMatchShowSkuData()
    {
        return isset($this->match_show_sku_data) ? $this->match_show_sku_data : null;
    }

    public function hasMatchShowSkuData()
    {
        return isset($this->match_show_sku_data);
    }

    public function clearMatchShowSkuData()
    {
        unset($this->match_show_sku_data);
    }

    /**
     *城际拼车卡片扩展库存推荐
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.IntercityMatchShowSkuData match_show_sku_data = 25;</code>
     * @param \Dirpc\SDK\PreSale\IntercityMatchShowSkuData $var
     * @return $this
     */
    public function setMatchShowSkuData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\IntercityMatchShowSkuData::class);
        $this->match_show_sku_data = $var;

        return $this;
    }

    /**
     *城际拼车卡片是否可用，是否可以通过外漏的卡片直接发单
     *
     * Generated from protobuf field <code>bool is_unavailable = 26;</code>
     * @return bool
     */
    public function getIsUnavailable()
    {
        return isset($this->is_unavailable) ? $this->is_unavailable : false;
    }

    public function hasIsUnavailable()
    {
        return isset($this->is_unavailable);
    }

    public function clearIsUnavailable()
    {
        unset($this->is_unavailable);
    }

    /**
     *城际拼车卡片是否可用，是否可以通过外漏的卡片直接发单
     *
     * Generated from protobuf field <code>bool is_unavailable = 26;</code>
     * @param bool $var
     * @return $this
     */
    public function setIsUnavailable($var)
    {
        GPBUtil::checkBool($var);
        $this->is_unavailable = $var;

        return $this;
    }

    /**
     *预估show h5命中类型
     *
     * Generated from protobuf field <code>int32 hit_show_h5_type = 27;</code>
     * @return int
     */
    public function getHitShowH5Type()
    {
        return isset($this->hit_show_h5_type) ? $this->hit_show_h5_type : 0;
    }

    public function hasHitShowH5Type()
    {
        return isset($this->hit_show_h5_type);
    }

    public function clearHitShowH5Type()
    {
        unset($this->hit_show_h5_type);
    }

    /**
     *预估show h5命中类型
     *
     * Generated from protobuf field <code>int32 hit_show_h5_type = 27;</code>
     * @param int $var
     * @return $this
     */
    public function setHitShowH5Type($var)
    {
        GPBUtil::checkInt32($var);
        $this->hit_show_h5_type = $var;

        return $this;
    }

    /**
     *是否支持选座弹层
     *
     * Generated from protobuf field <code>int32 support_select_seat = 28;</code>
     * @return int
     */
    public function getSupportSelectSeat()
    {
        return isset($this->support_select_seat) ? $this->support_select_seat : 0;
    }

    public function hasSupportSelectSeat()
    {
        return isset($this->support_select_seat);
    }

    public function clearSupportSelectSeat()
    {
        unset($this->support_select_seat);
    }

    /**
     *是否支持选座弹层
     *
     * Generated from protobuf field <code>int32 support_select_seat = 28;</code>
     * @param int $var
     * @return $this
     */
    public function setSupportSelectSeat($var)
    {
        GPBUtil::checkInt32($var);
        $this->support_select_seat = $var;

        return $this;
    }

}

