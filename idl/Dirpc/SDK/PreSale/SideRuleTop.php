<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideRuleTop</code>
 */
class SideRuleTop extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *活动事件标识，比如 special_rule
     *
     * Generated from protobuf field <code>string event = 1;</code>
     */
    protected $event = '';
    /**
     *规则列表，之前的rule_type
     *
     * Generated from protobuf field <code>repeated int32 event_sub_list = 2;</code>
     */
    private $event_sub_list;
    /**
     *eid，前端硬是要加
     *
     * Generated from protobuf field <code>string estimate_id = 3;</code>
     */
    protected $estimate_id = '';
    /**
     *权重
     *
     * Generated from protobuf field <code>int32 weight = 5;</code>
     */
    protected $weight = 0;
    /**
     *具体内容
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleTopContent content = 7;</code>
     */
    protected $content = null;
    /**
     *埋点信息
     *
     * Generated from protobuf field <code>map<string, string> track = 8;</code>
     */
    private $track;
    /**
     *omega埋点信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OmegaParam omega_param = 9;</code>
     */
    protected $omega_param = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $event
     *          活动事件标识，比如 special_rule
     *     @type int[]|\Nuwa\Protobuf\Internal\RepeatedField $event_sub_list
     *          规则列表，之前的rule_type
     *     @type string $estimate_id
     *          eid，前端硬是要加
     *     @type int $weight
     *          权重
     *     @type \Dirpc\SDK\PreSale\SideRuleTopContent $content
     *          具体内容
     *     @type array|\Nuwa\Protobuf\Internal\MapField $track
     *          埋点信息
     *     @type \Dirpc\SDK\PreSale\OmegaParam $omega_param
     *          omega埋点信息
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     *活动事件标识，比如 special_rule
     *
     * Generated from protobuf field <code>string event = 1;</code>
     * @return string
     */
    public function getEvent()
    {
        return $this->event;
    }

    /**
     *活动事件标识，比如 special_rule
     *
     * Generated from protobuf field <code>string event = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setEvent($var)
    {
        GPBUtil::checkString($var, True);
        $this->event = $var;

        return $this;
    }

    /**
     *规则列表，之前的rule_type
     *
     * Generated from protobuf field <code>repeated int32 event_sub_list = 2;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getEventSubList()
    {
        return $this->event_sub_list;
    }

    /**
     *规则列表，之前的rule_type
     *
     * Generated from protobuf field <code>repeated int32 event_sub_list = 2;</code>
     * @param int[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setEventSubList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::INT32);
        $this->event_sub_list = $arr;

        return $this;
    }

    /**
     *eid，前端硬是要加
     *
     * Generated from protobuf field <code>string estimate_id = 3;</code>
     * @return string
     */
    public function getEstimateId()
    {
        return $this->estimate_id;
    }

    /**
     *eid，前端硬是要加
     *
     * Generated from protobuf field <code>string estimate_id = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateId($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_id = $var;

        return $this;
    }

    /**
     *权重
     *
     * Generated from protobuf field <code>int32 weight = 5;</code>
     * @return int
     */
    public function getWeight()
    {
        return $this->weight;
    }

    /**
     *权重
     *
     * Generated from protobuf field <code>int32 weight = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setWeight($var)
    {
        GPBUtil::checkInt32($var);
        $this->weight = $var;

        return $this;
    }

    /**
     *具体内容
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleTopContent content = 7;</code>
     * @return \Dirpc\SDK\PreSale\SideRuleTopContent
     */
    public function getContent()
    {
        return isset($this->content) ? $this->content : null;
    }

    public function hasContent()
    {
        return isset($this->content);
    }

    public function clearContent()
    {
        unset($this->content);
    }

    /**
     *具体内容
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideRuleTopContent content = 7;</code>
     * @param \Dirpc\SDK\PreSale\SideRuleTopContent $var
     * @return $this
     */
    public function setContent($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\SideRuleTopContent::class);
        $this->content = $var;

        return $this;
    }

    /**
     *埋点信息
     *
     * Generated from protobuf field <code>map<string, string> track = 8;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getTrack()
    {
        return $this->track;
    }

    /**
     *埋点信息
     *
     * Generated from protobuf field <code>map<string, string> track = 8;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setTrack($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->track = $arr;

        return $this;
    }

    /**
     *omega埋点信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OmegaParam omega_param = 9;</code>
     * @return \Dirpc\SDK\PreSale\OmegaParam
     */
    public function getOmegaParam()
    {
        return isset($this->omega_param) ? $this->omega_param : null;
    }

    public function hasOmegaParam()
    {
        return isset($this->omega_param);
    }

    public function clearOmegaParam()
    {
        unset($this->omega_param);
    }

    /**
     *omega埋点信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OmegaParam omega_param = 9;</code>
     * @param \Dirpc\SDK\PreSale\OmegaParam $var
     * @return $this
     */
    public function setOmegaParam($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\OmegaParam::class);
        $this->omega_param = $var;

        return $this;
    }

}

