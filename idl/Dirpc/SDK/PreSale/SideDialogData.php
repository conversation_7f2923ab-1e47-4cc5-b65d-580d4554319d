<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideDialogData</code>
 */
class SideDialogData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string img_url = 1;</code>
     */
    protected $img_url = '';
    /**
     * Generated from protobuf field <code>double img_ratio = 2;</code>
     */
    protected $img_ratio = 0.0;
    /**
     * Generated from protobuf field <code>string title = 3;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>string cancel_text = 4;</code>
     */
    protected $cancel_text = '';
    /**
     * Generated from protobuf field <code>string confirm_text = 5;</code>
     */
    protected $confirm_text = '';
    /**
     * Generated from protobuf field <code>string confirm_color = 6;</code>
     */
    protected $confirm_color = '';
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideDialogSubContent sub_content = 7;</code>
     */
    protected $sub_content = null;
    /**
     * Generated from protobuf field <code>int32 dialog_style = 8;</code>
     */
    protected $dialog_style = null;
    /**
     * Generated from protobuf field <code>string dialog_id = 9;</code>
     */
    protected $dialog_id = null;
    /**
     * Generated from protobuf field <code>string content = 10;</code>
     */
    protected $content = null;
    /**
     *动效图
     *
     * Generated from protobuf field <code>string light_img_url = 11;</code>
     */
    protected $light_img_url = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $img_url
     *     @type float $img_ratio
     *     @type string $title
     *     @type string $cancel_text
     *     @type string $confirm_text
     *     @type string $confirm_color
     *     @type \Dirpc\SDK\PreSale\SideDialogSubContent $sub_content
     *     @type int $dialog_style
     *     @type string $dialog_id
     *     @type string $content
     *     @type string $light_img_url
     *          动效图
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string img_url = 1;</code>
     * @return string
     */
    public function getImgUrl()
    {
        return $this->img_url;
    }

    /**
     * Generated from protobuf field <code>string img_url = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setImgUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->img_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double img_ratio = 2;</code>
     * @return float
     */
    public function getImgRatio()
    {
        return $this->img_ratio;
    }

    /**
     * Generated from protobuf field <code>double img_ratio = 2;</code>
     * @param float $var
     * @return $this
     */
    public function setImgRatio($var)
    {
        GPBUtil::checkDouble($var);
        $this->img_ratio = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string title = 3;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string cancel_text = 4;</code>
     * @return string
     */
    public function getCancelText()
    {
        return $this->cancel_text;
    }

    /**
     * Generated from protobuf field <code>string cancel_text = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setCancelText($var)
    {
        GPBUtil::checkString($var, True);
        $this->cancel_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string confirm_text = 5;</code>
     * @return string
     */
    public function getConfirmText()
    {
        return $this->confirm_text;
    }

    /**
     * Generated from protobuf field <code>string confirm_text = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setConfirmText($var)
    {
        GPBUtil::checkString($var, True);
        $this->confirm_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string confirm_color = 6;</code>
     * @return string
     */
    public function getConfirmColor()
    {
        return $this->confirm_color;
    }

    /**
     * Generated from protobuf field <code>string confirm_color = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setConfirmColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->confirm_color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideDialogSubContent sub_content = 7;</code>
     * @return \Dirpc\SDK\PreSale\SideDialogSubContent
     */
    public function getSubContent()
    {
        return isset($this->sub_content) ? $this->sub_content : null;
    }

    public function hasSubContent()
    {
        return isset($this->sub_content);
    }

    public function clearSubContent()
    {
        unset($this->sub_content);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideDialogSubContent sub_content = 7;</code>
     * @param \Dirpc\SDK\PreSale\SideDialogSubContent $var
     * @return $this
     */
    public function setSubContent($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\SideDialogSubContent::class);
        $this->sub_content = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 dialog_style = 8;</code>
     * @return int
     */
    public function getDialogStyle()
    {
        return isset($this->dialog_style) ? $this->dialog_style : 0;
    }

    public function hasDialogStyle()
    {
        return isset($this->dialog_style);
    }

    public function clearDialogStyle()
    {
        unset($this->dialog_style);
    }

    /**
     * Generated from protobuf field <code>int32 dialog_style = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setDialogStyle($var)
    {
        GPBUtil::checkInt32($var);
        $this->dialog_style = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string dialog_id = 9;</code>
     * @return string
     */
    public function getDialogId()
    {
        return isset($this->dialog_id) ? $this->dialog_id : '';
    }

    public function hasDialogId()
    {
        return isset($this->dialog_id);
    }

    public function clearDialogId()
    {
        unset($this->dialog_id);
    }

    /**
     * Generated from protobuf field <code>string dialog_id = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setDialogId($var)
    {
        GPBUtil::checkString($var, True);
        $this->dialog_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string content = 10;</code>
     * @return string
     */
    public function getContent()
    {
        return isset($this->content) ? $this->content : '';
    }

    public function hasContent()
    {
        return isset($this->content);
    }

    public function clearContent()
    {
        unset($this->content);
    }

    /**
     * Generated from protobuf field <code>string content = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setContent($var)
    {
        GPBUtil::checkString($var, True);
        $this->content = $var;

        return $this;
    }

    /**
     *动效图
     *
     * Generated from protobuf field <code>string light_img_url = 11;</code>
     * @return string
     */
    public function getLightImgUrl()
    {
        return isset($this->light_img_url) ? $this->light_img_url : '';
    }

    public function hasLightImgUrl()
    {
        return isset($this->light_img_url);
    }

    public function clearLightImgUrl()
    {
        unset($this->light_img_url);
    }

    /**
     *动效图
     *
     * Generated from protobuf field <code>string light_img_url = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setLightImgUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->light_img_url = $var;

        return $this;
    }

}

