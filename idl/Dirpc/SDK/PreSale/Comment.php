<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.Comment</code>
 */
class Comment extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string head = 1;</code>
     */
    protected $head = '';
    /**
     * Generated from protobuf field <code>string sub_head = 2;</code>
     */
    protected $sub_head = '';
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CommentOption comment_option = 3;</code>
     */
    private $comment_option;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $head
     *     @type string $sub_head
     *     @type \Dirpc\SDK\PreSale\CommentOption[]|\Nuwa\Protobuf\Internal\RepeatedField $comment_option
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string head = 1;</code>
     * @return string
     */
    public function getHead()
    {
        return $this->head;
    }

    /**
     * Generated from protobuf field <code>string head = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setHead($var)
    {
        GPBUtil::checkString($var, True);
        $this->head = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string sub_head = 2;</code>
     * @return string
     */
    public function getSubHead()
    {
        return $this->sub_head;
    }

    /**
     * Generated from protobuf field <code>string sub_head = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setSubHead($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_head = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CommentOption comment_option = 3;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getCommentOption()
    {
        return $this->comment_option;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CommentOption comment_option = 3;</code>
     * @param \Dirpc\SDK\PreSale\CommentOption[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setCommentOption($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\CommentOption::class);
        $this->comment_option = $arr;

        return $this;
    }

}

