<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.ButtonInfo</code>
 */
class ButtonInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 action = 1;</code>
     */
    protected $action = 0;
    /**
     * Generated from protobuf field <code>string event = 2;</code>
     */
    protected $event = '';
    /**
     * Generated from protobuf field <code>string extra_params = 3;</code>
     */
    protected $extra_params = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $action
     *     @type string $event
     *     @type string $extra_params
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 action = 1;</code>
     * @return int
     */
    public function getAction()
    {
        return $this->action;
    }

    /**
     * Generated from protobuf field <code>int32 action = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setAction($var)
    {
        GPBUtil::checkInt32($var);
        $this->action = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string event = 2;</code>
     * @return string
     */
    public function getEvent()
    {
        return $this->event;
    }

    /**
     * Generated from protobuf field <code>string event = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setEvent($var)
    {
        GPBUtil::checkString($var, True);
        $this->event = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string extra_params = 3;</code>
     * @return string
     */
    public function getExtraParams()
    {
        return isset($this->extra_params) ? $this->extra_params : '';
    }

    public function hasExtraParams()
    {
        return isset($this->extra_params);
    }

    public function clearExtraParams()
    {
        unset($this->extra_params);
    }

    /**
     * Generated from protobuf field <code>string extra_params = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setExtraParams($var)
    {
        GPBUtil::checkString($var, True);
        $this->extra_params = $var;

        return $this;
    }

}

