<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.InvitationButton</code>
 */
class InvitationButton extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 type = 1;</code>
     */
    protected $type = 0;
    /**
     * Generated from protobuf field <code>int32 theme_type = 2;</code>
     */
    protected $theme_type = 0;
    /**
     * Generated from protobuf field <code>string color = 3;</code>
     */
    protected $color = '';
    /**
     * Generated from protobuf field <code>string end_color = 4;</code>
     */
    protected $end_color = null;
    /**
     * Generated from protobuf field <code>string text = 5;</code>
     */
    protected $text = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $type
     *     @type int $theme_type
     *     @type string $color
     *     @type string $end_color
     *     @type string $text
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 type = 1;</code>
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Generated from protobuf field <code>int32 type = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkInt32($var);
        $this->type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 theme_type = 2;</code>
     * @return int
     */
    public function getThemeType()
    {
        return $this->theme_type;
    }

    /**
     * Generated from protobuf field <code>int32 theme_type = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setThemeType($var)
    {
        GPBUtil::checkInt32($var);
        $this->theme_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string color = 3;</code>
     * @return string
     */
    public function getColor()
    {
        return $this->color;
    }

    /**
     * Generated from protobuf field <code>string color = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string end_color = 4;</code>
     * @return string
     */
    public function getEndColor()
    {
        return isset($this->end_color) ? $this->end_color : '';
    }

    public function hasEndColor()
    {
        return isset($this->end_color);
    }

    public function clearEndColor()
    {
        unset($this->end_color);
    }

    /**
     * Generated from protobuf field <code>string end_color = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setEndColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->end_color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string text = 5;</code>
     * @return string
     */
    public function getText()
    {
        return $this->text;
    }

    /**
     * Generated from protobuf field <code>string text = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setText($var)
    {
        GPBUtil::checkString($var, True);
        $this->text = $var;

        return $this;
    }

}

