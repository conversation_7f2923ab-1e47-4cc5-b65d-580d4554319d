<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.FilterNormal</code>
 */
class FilterNormal extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string filter_title = 1;</code>
     */
    protected $filter_title = null;
    /**
     * Generated from protobuf field <code>string filter_sub_title = 2;</code>
     */
    protected $filter_sub_title = null;
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.FilterNormalData filter_list = 3;</code>
     */
    private $filter_list;
    /**
     * Generated from protobuf field <code>int32 refresh_type = 4;</code>
     */
    protected $refresh_type = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $filter_title
     *     @type string $filter_sub_title
     *     @type \Dirpc\SDK\PreSale\FilterNormalData[]|\Nuwa\Protobuf\Internal\RepeatedField $filter_list
     *     @type int $refresh_type
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string filter_title = 1;</code>
     * @return string
     */
    public function getFilterTitle()
    {
        return isset($this->filter_title) ? $this->filter_title : '';
    }

    public function hasFilterTitle()
    {
        return isset($this->filter_title);
    }

    public function clearFilterTitle()
    {
        unset($this->filter_title);
    }

    /**
     * Generated from protobuf field <code>string filter_title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setFilterTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->filter_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string filter_sub_title = 2;</code>
     * @return string
     */
    public function getFilterSubTitle()
    {
        return isset($this->filter_sub_title) ? $this->filter_sub_title : '';
    }

    public function hasFilterSubTitle()
    {
        return isset($this->filter_sub_title);
    }

    public function clearFilterSubTitle()
    {
        unset($this->filter_sub_title);
    }

    /**
     * Generated from protobuf field <code>string filter_sub_title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setFilterSubTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->filter_sub_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.FilterNormalData filter_list = 3;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getFilterList()
    {
        return $this->filter_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.FilterNormalData filter_list = 3;</code>
     * @param \Dirpc\SDK\PreSale\FilterNormalData[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFilterList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\FilterNormalData::class);
        $this->filter_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 refresh_type = 4;</code>
     * @return int
     */
    public function getRefreshType()
    {
        return isset($this->refresh_type) ? $this->refresh_type : 0;
    }

    public function hasRefreshType()
    {
        return isset($this->refresh_type);
    }

    public function clearRefreshType()
    {
        unset($this->refresh_type);
    }

    /**
     * Generated from protobuf field <code>int32 refresh_type = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setRefreshType($var)
    {
        GPBUtil::checkInt32($var);
        $this->refresh_type = $var;

        return $this;
    }

}

