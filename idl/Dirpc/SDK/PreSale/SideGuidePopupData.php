<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideGuidePopupData</code>
 */
class SideGuidePopupData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 product_category = 1;</code>
     */
    protected $product_category = 0;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideDialogData dialog_data = 2;</code>
     */
    protected $dialog_data = null;
    /**
     * Generated from protobuf field <code>map<string, string> track = 3;</code>
     */
    private $track;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $product_category
     *     @type \Dirpc\SDK\PreSale\SideDialogData $dialog_data
     *     @type array|\Nuwa\Protobuf\Internal\MapField $track
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 product_category = 1;</code>
     * @return int
     */
    public function getProductCategory()
    {
        return $this->product_category;
    }

    /**
     * Generated from protobuf field <code>int32 product_category = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setProductCategory($var)
    {
        GPBUtil::checkInt32($var);
        $this->product_category = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideDialogData dialog_data = 2;</code>
     * @return \Dirpc\SDK\PreSale\SideDialogData
     */
    public function getDialogData()
    {
        return isset($this->dialog_data) ? $this->dialog_data : null;
    }

    public function hasDialogData()
    {
        return isset($this->dialog_data);
    }

    public function clearDialogData()
    {
        unset($this->dialog_data);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideDialogData dialog_data = 2;</code>
     * @param \Dirpc\SDK\PreSale\SideDialogData $var
     * @return $this
     */
    public function setDialogData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\SideDialogData::class);
        $this->dialog_data = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>map<string, string> track = 3;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getTrack()
    {
        return $this->track;
    }

    /**
     * Generated from protobuf field <code>map<string, string> track = 3;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setTrack($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->track = $arr;

        return $this;
    }

}

