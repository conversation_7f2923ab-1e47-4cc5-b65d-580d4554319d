<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.LayoutData</code>
 */
class LayoutData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string recommend_bubble = 1;</code>
     */
    protected $recommend_bubble = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $recommend_bubble
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string recommend_bubble = 1;</code>
     * @return string
     */
    public function getRecommendBubble()
    {
        return $this->recommend_bubble;
    }

    /**
     * Generated from protobuf field <code>string recommend_bubble = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setRecommendBubble($var)
    {
        GPBUtil::checkString($var, True);
        $this->recommend_bubble = $var;

        return $this;
    }

}

