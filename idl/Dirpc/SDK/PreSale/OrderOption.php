<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.OrderOption</code>
 */
class OrderOption extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string field = 1;</code>
     */
    protected $field = '';
    /**
     * Generated from protobuf field <code>string selected_value = 2;</code>
     */
    protected $selected_value = '';
    /**
     * Generated from protobuf field <code>string default_value = 3;</code>
     */
    protected $default_value = '';
    /**
     * Generated from protobuf field <code>string title = 4;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ModeListOption mode_list = 5;</code>
     */
    private $mode_list;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $field
     *     @type string $selected_value
     *     @type string $default_value
     *     @type string $title
     *     @type \Dirpc\SDK\PreSale\ModeListOption[]|\Nuwa\Protobuf\Internal\RepeatedField $mode_list
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string field = 1;</code>
     * @return string
     */
    public function getField()
    {
        return $this->field;
    }

    /**
     * Generated from protobuf field <code>string field = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setField($var)
    {
        GPBUtil::checkString($var, True);
        $this->field = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string selected_value = 2;</code>
     * @return string
     */
    public function getSelectedValue()
    {
        return $this->selected_value;
    }

    /**
     * Generated from protobuf field <code>string selected_value = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setSelectedValue($var)
    {
        GPBUtil::checkString($var, True);
        $this->selected_value = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string default_value = 3;</code>
     * @return string
     */
    public function getDefaultValue()
    {
        return $this->default_value;
    }

    /**
     * Generated from protobuf field <code>string default_value = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setDefaultValue($var)
    {
        GPBUtil::checkString($var, True);
        $this->default_value = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string title = 4;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ModeListOption mode_list = 5;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getModeList()
    {
        return $this->mode_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.ModeListOption mode_list = 5;</code>
     * @param \Dirpc\SDK\PreSale\ModeListOption[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setModeList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\ModeListOption::class);
        $this->mode_list = $arr;

        return $this;
    }

}

