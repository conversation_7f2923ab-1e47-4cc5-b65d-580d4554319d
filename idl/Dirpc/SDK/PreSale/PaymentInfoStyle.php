<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.PaymentInfoStyle</code>
 */
class PaymentInfoStyle extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *支付方式名称
     *
     * Generated from protobuf field <code>string content = 1;</code>
     */
    protected $content = '';
    /**
     *字体颜色
     *
     * Generated from protobuf field <code>string font_color = 2;</code>
     */
    protected $font_color = null;
    /**
     *背景颜色
     *
     * Generated from protobuf field <code>string background_color = 3;</code>
     */
    protected $background_color = null;
    /**
     *背景透明度
     *
     * Generated from protobuf field <code>string background_transparency = 4;</code>
     */
    protected $background_transparency = null;
    /**
     *边框颜色
     *
     * Generated from protobuf field <code>string border_color = 5;</code>
     */
    protected $border_color = null;
    /**
     *高亮字体颜色
     *
     * Generated from protobuf field <code>string highlight_font_color = 6;</code>
     */
    protected $highlight_font_color = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $content
     *          支付方式名称
     *     @type string $font_color
     *          字体颜色
     *     @type string $background_color
     *          背景颜色
     *     @type string $background_transparency
     *          背景透明度
     *     @type string $border_color
     *          边框颜色
     *     @type string $highlight_font_color
     *          高亮字体颜色
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *支付方式名称
     *
     * Generated from protobuf field <code>string content = 1;</code>
     * @return string
     */
    public function getContent()
    {
        return $this->content;
    }

    /**
     *支付方式名称
     *
     * Generated from protobuf field <code>string content = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setContent($var)
    {
        GPBUtil::checkString($var, True);
        $this->content = $var;

        return $this;
    }

    /**
     *字体颜色
     *
     * Generated from protobuf field <code>string font_color = 2;</code>
     * @return string
     */
    public function getFontColor()
    {
        return isset($this->font_color) ? $this->font_color : '';
    }

    public function hasFontColor()
    {
        return isset($this->font_color);
    }

    public function clearFontColor()
    {
        unset($this->font_color);
    }

    /**
     *字体颜色
     *
     * Generated from protobuf field <code>string font_color = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setFontColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->font_color = $var;

        return $this;
    }

    /**
     *背景颜色
     *
     * Generated from protobuf field <code>string background_color = 3;</code>
     * @return string
     */
    public function getBackgroundColor()
    {
        return isset($this->background_color) ? $this->background_color : '';
    }

    public function hasBackgroundColor()
    {
        return isset($this->background_color);
    }

    public function clearBackgroundColor()
    {
        unset($this->background_color);
    }

    /**
     *背景颜色
     *
     * Generated from protobuf field <code>string background_color = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setBackgroundColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->background_color = $var;

        return $this;
    }

    /**
     *背景透明度
     *
     * Generated from protobuf field <code>string background_transparency = 4;</code>
     * @return string
     */
    public function getBackgroundTransparency()
    {
        return isset($this->background_transparency) ? $this->background_transparency : '';
    }

    public function hasBackgroundTransparency()
    {
        return isset($this->background_transparency);
    }

    public function clearBackgroundTransparency()
    {
        unset($this->background_transparency);
    }

    /**
     *背景透明度
     *
     * Generated from protobuf field <code>string background_transparency = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setBackgroundTransparency($var)
    {
        GPBUtil::checkString($var, True);
        $this->background_transparency = $var;

        return $this;
    }

    /**
     *边框颜色
     *
     * Generated from protobuf field <code>string border_color = 5;</code>
     * @return string
     */
    public function getBorderColor()
    {
        return isset($this->border_color) ? $this->border_color : '';
    }

    public function hasBorderColor()
    {
        return isset($this->border_color);
    }

    public function clearBorderColor()
    {
        unset($this->border_color);
    }

    /**
     *边框颜色
     *
     * Generated from protobuf field <code>string border_color = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setBorderColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->border_color = $var;

        return $this;
    }

    /**
     *高亮字体颜色
     *
     * Generated from protobuf field <code>string highlight_font_color = 6;</code>
     * @return string
     */
    public function getHighlightFontColor()
    {
        return isset($this->highlight_font_color) ? $this->highlight_font_color : '';
    }

    public function hasHighlightFontColor()
    {
        return isset($this->highlight_font_color);
    }

    public function clearHighlightFontColor()
    {
        unset($this->highlight_font_color);
    }

    /**
     *高亮字体颜色
     *
     * Generated from protobuf field <code>string highlight_font_color = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setHighlightFontColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->highlight_font_color = $var;

        return $this;
    }

}

