<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.CarpoolBookingTime</code>
 */
class CarpoolBookingTime extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string text = 1;</code>
     */
    protected $text = null;
    /**
     * Generated from protobuf field <code>int32 hour = 2;</code>
     */
    protected $hour = null;
    /**
     * Generated from protobuf field <code>int32 gap = 3;</code>
     */
    protected $gap = null;
    /**
     * Generated from protobuf field <code>string desc = 4;</code>
     */
    protected $desc = null;
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.Minute minute_list = 5;</code>
     */
    private $minute_list;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $text
     *     @type int $hour
     *     @type int $gap
     *     @type string $desc
     *     @type \Dirpc\SDK\PreSale\Minute[]|\Nuwa\Protobuf\Internal\RepeatedField $minute_list
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string text = 1;</code>
     * @return string
     */
    public function getText()
    {
        return isset($this->text) ? $this->text : '';
    }

    public function hasText()
    {
        return isset($this->text);
    }

    public function clearText()
    {
        unset($this->text);
    }

    /**
     * Generated from protobuf field <code>string text = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setText($var)
    {
        GPBUtil::checkString($var, True);
        $this->text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 hour = 2;</code>
     * @return int
     */
    public function getHour()
    {
        return isset($this->hour) ? $this->hour : 0;
    }

    public function hasHour()
    {
        return isset($this->hour);
    }

    public function clearHour()
    {
        unset($this->hour);
    }

    /**
     * Generated from protobuf field <code>int32 hour = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setHour($var)
    {
        GPBUtil::checkInt32($var);
        $this->hour = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 gap = 3;</code>
     * @return int
     */
    public function getGap()
    {
        return isset($this->gap) ? $this->gap : 0;
    }

    public function hasGap()
    {
        return isset($this->gap);
    }

    public function clearGap()
    {
        unset($this->gap);
    }

    /**
     * Generated from protobuf field <code>int32 gap = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setGap($var)
    {
        GPBUtil::checkInt32($var);
        $this->gap = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string desc = 4;</code>
     * @return string
     */
    public function getDesc()
    {
        return isset($this->desc) ? $this->desc : '';
    }

    public function hasDesc()
    {
        return isset($this->desc);
    }

    public function clearDesc()
    {
        unset($this->desc);
    }

    /**
     * Generated from protobuf field <code>string desc = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->desc = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.Minute minute_list = 5;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getMinuteList()
    {
        return $this->minute_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.Minute minute_list = 5;</code>
     * @param \Dirpc\SDK\PreSale\Minute[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setMinuteList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\Minute::class);
        $this->minute_list = $arr;

        return $this;
    }

}

