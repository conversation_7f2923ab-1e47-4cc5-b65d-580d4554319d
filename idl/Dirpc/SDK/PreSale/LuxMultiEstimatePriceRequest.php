<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.LuxMultiEstimatePriceRequest</code>
 */
class LuxMultiEstimatePriceRequest extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string token = 1;</code>
     */
    protected $token = '';
    /**
     * Generated from protobuf field <code>string appversion = 2;</code>
     */
    protected $appversion = '';
    /**
     *端来源标识 http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=118857095
     *
     * Generated from protobuf field <code>int32 access_key_id = 3;</code>
     */
    protected $access_key_id = 0;
    /**
     *客户端下载来源
     *
     * Generated from protobuf field <code>int32 channel = 4;</code>
     */
    protected $channel = 0;
    /**
     *区分客户端：安卓乘客端 1； IOS乘客端 101； 所有webapp客户端 201； 企业级 301 ；openApi 401；导流系统 501；
     *
     * Generated from protobuf field <code>int32 client_type = 5;</code>
     */
    protected $client_type = 0;
    /**
     *语言
     *
     * Generated from protobuf field <code>string lang = 6;</code>
     */
    protected $lang = '';
    /**
     *反作弊token
     *
     * Generated from protobuf field <code>string a3_token = 7;</code>
     */
    protected $a3_token = '';
    /**
     *分辨率
     *
     * Generated from protobuf field <code>string pixels = 8;</code>
     */
    protected $pixels = '';
    /**
     *地图类型
     *
     * Generated from protobuf field <code>string maptype = 9;</code>
     */
    protected $maptype = '';
    /**
     *设备号
     *
     * Generated from protobuf field <code>string imei = 10;</code>
     */
    protected $imei = '';
    /**
     *设备识别id
     *
     * Generated from protobuf field <code>string suuid = 11;</code>
     */
    protected $suuid = '';
    /**
     *终端标识，区分不同端来源;
     *
     * Generated from protobuf field <code>string terminal_id = 12;</code>
     */
    protected $terminal_id = '';
    /**
     *品牌ID:1滴滴； 2优步；3长平,默认滴滴
     *
     * Generated from protobuf field <code>int32 origin_id = 13;</code>
     */
    protected $origin_id = 0;
    /**
     *1 IOS 2 android 3 webapp 4 oepenapi 5 b2b 6 guide
     *
     * Generated from protobuf field <code>int32 platform_type = 14;</code>
     */
    protected $platform_type = 0;
    /**
     *第三方平台id 微信openid 支付宝openid
     *
     * Generated from protobuf field <code>string openid = 15;</code>
     */
    protected $openid = '';
    /**
     *暂不知道用途，建议透传主预估参数
     *
     * Generated from protobuf field <code>string from = 16;</code>
     */
    protected $from = '';
    /**
     *起终点相关信息*
     *
     * Generated from protobuf field <code>double lat = 30;</code>
     */
    protected $lat = 0.0;
    /**
     * Generated from protobuf field <code>double lng = 31;</code>
     */
    protected $lng = 0.0;
    /**
     * Generated from protobuf field <code>double from_lat = 32;</code>
     */
    protected $from_lat = 0.0;
    /**
     * Generated from protobuf field <code>double from_lng = 33;</code>
     */
    protected $from_lng = 0.0;
    /**
     * Generated from protobuf field <code>string from_poi_id = 34;</code>
     */
    protected $from_poi_id = '';
    /**
     * Generated from protobuf field <code>string from_poi_type = 35;</code>
     */
    protected $from_poi_type = '';
    /**
     * Generated from protobuf field <code>string from_address = 36;</code>
     */
    protected $from_address = '';
    /**
     * Generated from protobuf field <code>string from_name = 37;</code>
     */
    protected $from_name = '';
    /**
     * Generated from protobuf field <code>double to_lat = 38;</code>
     */
    protected $to_lat = 0.0;
    /**
     * Generated from protobuf field <code>double to_lng = 39;</code>
     */
    protected $to_lng = 0.0;
    /**
     * Generated from protobuf field <code>string to_poi_id = 40;</code>
     */
    protected $to_poi_id = '';
    /**
     * Generated from protobuf field <code>string to_poi_type = 41;</code>
     */
    protected $to_poi_type = '';
    /**
     * Generated from protobuf field <code>string to_address = 42;</code>
     */
    protected $to_address = '';
    /**
     * Generated from protobuf field <code>string to_name = 43;</code>
     */
    protected $to_name = '';
    /**
     * Generated from protobuf field <code>string dest_poi_code = 44;</code>
     */
    protected $dest_poi_code = '';
    /**
     * Generated from protobuf field <code>string dest_poi_tag = 45;</code>
     */
    protected $dest_poi_tag = '';
    /**
     *是否使用溢价保护
     *
     * Generated from protobuf field <code>string use_dpa = 46;</code>
     */
    protected $use_dpa = '';
    /**
     *顶导ID
     *
     * Generated from protobuf field <code>string menu_id = 50;</code>
     */
    protected $menu_id = '';
    /**
     *页面类型
     *
     * Generated from protobuf field <code>int32 page_type = 51;</code>
     */
    protected $page_type = 0;
    /**
     *代叫类型
     *
     * Generated from protobuf field <code>int32 call_car_type = 52;</code>
     */
    protected $call_car_type = 0;
    /**
     *代叫手机号
     *
     * Generated from protobuf field <code>string call_car_phone = 53;</code>
     */
    protected $call_car_phone = '';
    /**
     *用户类型：1普通用户；2企业用户
     *
     * Generated from protobuf field <code>int32 user_type = 54;</code>
     */
    protected $user_type = 0;
    /**
     *时间戳
     *
     * Generated from protobuf field <code>string departure_time = 55;</code>
     */
    protected $departure_time = '';
    /**
     *支付类型
     *
     * Generated from protobuf field <code>int32 payments_type = 56;</code>
     */
    protected $payments_type = 0;
    /**
     *订单类型
     *
     * Generated from protobuf field <code>int32 order_type = 57;</code>
     */
    protected $order_type = 0;
    /**
     *原始入口页面（如预约跳转接送机）
     *
     * Generated from protobuf field <code>int32 origin_page_type = 58;</code>
     */
    protected $origin_page_type = 0;
    /**
     *航班出发地三字码,如CTU
     *
     * Generated from protobuf field <code>string flight_dep_code = 120;</code>
     */
    protected $flight_dep_code = null;
    /**
     *航班出发航站楼，如T2
     *
     * Generated from protobuf field <code>string flight_dep_terminal = 121;</code>
     */
    protected $flight_dep_terminal = null;
    /**
     *航班起飞时间字符串
     *
     * Generated from protobuf field <code>string traffic_dep_time = 122;</code>
     */
    protected $traffic_dep_time = null;
    /**
     *航班落地三字码,如CTU
     *
     * Generated from protobuf field <code>string flight_arr_code = 123;</code>
     */
    protected $flight_arr_code = null;
    /**
     *航班落地航站楼，如T2
     *
     * Generated from protobuf field <code>string flight_arr_terminal = 124;</code>
     */
    protected $flight_arr_terminal = null;
    /**
     *航班到达时间字符串
     *
     * Generated from protobuf field <code>string traffic_arr_time = 125;</code>
     */
    protected $traffic_arr_time = null;
    /**
     *航班号，如CA1405
     *
     * Generated from protobuf field <code>string traffic_number = 126;</code>
     */
    protected $traffic_number = null;
    /**
     *接送机类型 1-接机，2-送机，端上入口，无入口传0
     *
     * Generated from protobuf field <code>int32 airport_type = 127;</code>
     */
    protected $airport_type = null;
    /**
     *接机时为航班落地航站楼id，送机时为出发航站楼id，如1573
     *
     * Generated from protobuf field <code>int32 airport_id = 128;</code>
     */
    protected $airport_id = null;
    /**
     *用车偏移时间（接机时，单位：秒）
     *
     * Generated from protobuf field <code>int32 shift_time = 129;</code>
     */
    protected $shift_time = null;
    /**
     *用车偏移时间（接机时，单位：秒）
     *
     * Generated from protobuf field <code>int32 railway_type = 130;</code>
     */
    protected $railway_type = null;
    /**
     * Generated from protobuf field <code>int32 railway_id = 131;</code>
     */
    protected $railway_id = null;
    /**
     *选中车型, 示例: 1000 1500
     *
     * Generated from protobuf field <code>string luxury_select_carlevels = 140;</code>
     */
    protected $luxury_select_carlevels = null;
    /**
     *选中司机 示例: -1 580543123784568
     *
     * Generated from protobuf field <code>string luxury_select_driver = 141;</code>
     */
    protected $luxury_select_driver = null;
    /**
     * Generated from protobuf field <code>int32 business_id = 142;</code>
     */
    protected $business_id = null;
    /**
     * Generated from protobuf field <code>int32 require_level = 143;</code>
     */
    protected $require_level = null;
    /**
     * Generated from protobuf field <code>string oid = 144;</code>
     */
    protected $oid = null;
    /**
     *预估tab 0车型 1司机
     *
     * Generated from protobuf field <code>int32 tab_type = 145;</code>
     */
    protected $tab_type = null;
    /**
     *用户勾选的个性化增值服务
     *
     * Generated from protobuf field <code>string custom_feature = 146;</code>
     */
    protected $custom_feature = null;
    /**
     *品类类型
     *
     * Generated from protobuf field <code>int32 product_category = 147;</code>
     */
    protected $product_category = null;
    /**
     *是否关闭乘客推荐
     *
     * Generated from protobuf field <code>int32 is_close_prefer = 148;</code>
     */
    protected $is_close_prefer = null;
    /**
     *是否多勾
     *
     * Generated from protobuf field <code>int32 is_multi_select = 149;</code>
     */
    protected $is_multi_select = null;
    /**
     *是否多勾
     *
     * Generated from protobuf field <code>int32 source_id = 150;</code>
     */
    protected $source_id = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $token
     *     @type string $appversion
     *     @type int $access_key_id
     *          端来源标识 http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=118857095
     *     @type int $channel
     *          客户端下载来源
     *     @type int $client_type
     *          区分客户端：安卓乘客端 1； IOS乘客端 101； 所有webapp客户端 201； 企业级 301 ；openApi 401；导流系统 501；
     *     @type string $lang
     *          语言
     *     @type string $a3_token
     *          反作弊token
     *     @type string $pixels
     *          分辨率
     *     @type string $maptype
     *          地图类型
     *     @type string $imei
     *          设备号
     *     @type string $suuid
     *          设备识别id
     *     @type string $terminal_id
     *          终端标识，区分不同端来源;
     *     @type int $origin_id
     *          品牌ID:1滴滴； 2优步；3长平,默认滴滴
     *     @type int $platform_type
     *          1 IOS 2 android 3 webapp 4 oepenapi 5 b2b 6 guide
     *     @type string $openid
     *          第三方平台id 微信openid 支付宝openid
     *     @type string $from
     *          暂不知道用途，建议透传主预估参数
     *     @type float $lat
     *          起终点相关信息*
     *     @type float $lng
     *     @type float $from_lat
     *     @type float $from_lng
     *     @type string $from_poi_id
     *     @type string $from_poi_type
     *     @type string $from_address
     *     @type string $from_name
     *     @type float $to_lat
     *     @type float $to_lng
     *     @type string $to_poi_id
     *     @type string $to_poi_type
     *     @type string $to_address
     *     @type string $to_name
     *     @type string $dest_poi_code
     *     @type string $dest_poi_tag
     *     @type string $use_dpa
     *          是否使用溢价保护
     *     @type string $menu_id
     *          顶导ID
     *     @type int $page_type
     *          页面类型
     *     @type int $call_car_type
     *          代叫类型
     *     @type string $call_car_phone
     *          代叫手机号
     *     @type int $user_type
     *          用户类型：1普通用户；2企业用户
     *     @type string $departure_time
     *          时间戳
     *     @type int $payments_type
     *          支付类型
     *     @type int $order_type
     *          订单类型
     *     @type int $origin_page_type
     *          原始入口页面（如预约跳转接送机）
     *     @type string $flight_dep_code
     *          航班出发地三字码,如CTU
     *     @type string $flight_dep_terminal
     *          航班出发航站楼，如T2
     *     @type string $traffic_dep_time
     *          航班起飞时间字符串
     *     @type string $flight_arr_code
     *          航班落地三字码,如CTU
     *     @type string $flight_arr_terminal
     *          航班落地航站楼，如T2
     *     @type string $traffic_arr_time
     *          航班到达时间字符串
     *     @type string $traffic_number
     *          航班号，如CA1405
     *     @type int $airport_type
     *          接送机类型 1-接机，2-送机，端上入口，无入口传0
     *     @type int $airport_id
     *          接机时为航班落地航站楼id，送机时为出发航站楼id，如1573
     *     @type int $shift_time
     *          用车偏移时间（接机时，单位：秒）
     *     @type int $railway_type
     *          用车偏移时间（接机时，单位：秒）
     *     @type int $railway_id
     *     @type string $luxury_select_carlevels
     *          选中车型, 示例: 1000 1500
     *     @type string $luxury_select_driver
     *          选中司机 示例: -1 580543123784568
     *     @type int $business_id
     *     @type int $require_level
     *     @type string $oid
     *     @type int $tab_type
     *          预估tab 0车型 1司机
     *     @type string $custom_feature
     *          用户勾选的个性化增值服务
     *     @type int $product_category
     *          品类类型
     *     @type int $is_close_prefer
     *          是否关闭乘客推荐
     *     @type int $is_multi_select
     *          是否多勾
     *     @type int $source_id
     *          是否多勾
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string token = 1;</code>
     * @return string
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     * Generated from protobuf field <code>string token = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->token = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string appversion = 2;</code>
     * @return string
     */
    public function getAppversion()
    {
        return $this->appversion;
    }

    /**
     * Generated from protobuf field <code>string appversion = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setAppversion($var)
    {
        GPBUtil::checkString($var, True);
        $this->appversion = $var;

        return $this;
    }

    /**
     *端来源标识 http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=118857095
     *
     * Generated from protobuf field <code>int32 access_key_id = 3;</code>
     * @return int
     */
    public function getAccessKeyId()
    {
        return $this->access_key_id;
    }

    /**
     *端来源标识 http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=118857095
     *
     * Generated from protobuf field <code>int32 access_key_id = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setAccessKeyId($var)
    {
        GPBUtil::checkInt32($var);
        $this->access_key_id = $var;

        return $this;
    }

    /**
     *客户端下载来源
     *
     * Generated from protobuf field <code>int32 channel = 4;</code>
     * @return int
     */
    public function getChannel()
    {
        return $this->channel;
    }

    /**
     *客户端下载来源
     *
     * Generated from protobuf field <code>int32 channel = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setChannel($var)
    {
        GPBUtil::checkInt32($var);
        $this->channel = $var;

        return $this;
    }

    /**
     *区分客户端：安卓乘客端 1； IOS乘客端 101； 所有webapp客户端 201； 企业级 301 ；openApi 401；导流系统 501；
     *
     * Generated from protobuf field <code>int32 client_type = 5;</code>
     * @return int
     */
    public function getClientType()
    {
        return $this->client_type;
    }

    /**
     *区分客户端：安卓乘客端 1； IOS乘客端 101； 所有webapp客户端 201； 企业级 301 ；openApi 401；导流系统 501；
     *
     * Generated from protobuf field <code>int32 client_type = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setClientType($var)
    {
        GPBUtil::checkInt32($var);
        $this->client_type = $var;

        return $this;
    }

    /**
     *语言
     *
     * Generated from protobuf field <code>string lang = 6;</code>
     * @return string
     */
    public function getLang()
    {
        return $this->lang;
    }

    /**
     *语言
     *
     * Generated from protobuf field <code>string lang = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setLang($var)
    {
        GPBUtil::checkString($var, True);
        $this->lang = $var;

        return $this;
    }

    /**
     *反作弊token
     *
     * Generated from protobuf field <code>string a3_token = 7;</code>
     * @return string
     */
    public function getA3Token()
    {
        return $this->a3_token;
    }

    /**
     *反作弊token
     *
     * Generated from protobuf field <code>string a3_token = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setA3Token($var)
    {
        GPBUtil::checkString($var, True);
        $this->a3_token = $var;

        return $this;
    }

    /**
     *分辨率
     *
     * Generated from protobuf field <code>string pixels = 8;</code>
     * @return string
     */
    public function getPixels()
    {
        return $this->pixels;
    }

    /**
     *分辨率
     *
     * Generated from protobuf field <code>string pixels = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setPixels($var)
    {
        GPBUtil::checkString($var, True);
        $this->pixels = $var;

        return $this;
    }

    /**
     *地图类型
     *
     * Generated from protobuf field <code>string maptype = 9;</code>
     * @return string
     */
    public function getMaptype()
    {
        return $this->maptype;
    }

    /**
     *地图类型
     *
     * Generated from protobuf field <code>string maptype = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setMaptype($var)
    {
        GPBUtil::checkString($var, True);
        $this->maptype = $var;

        return $this;
    }

    /**
     *设备号
     *
     * Generated from protobuf field <code>string imei = 10;</code>
     * @return string
     */
    public function getImei()
    {
        return $this->imei;
    }

    /**
     *设备号
     *
     * Generated from protobuf field <code>string imei = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setImei($var)
    {
        GPBUtil::checkString($var, True);
        $this->imei = $var;

        return $this;
    }

    /**
     *设备识别id
     *
     * Generated from protobuf field <code>string suuid = 11;</code>
     * @return string
     */
    public function getSuuid()
    {
        return $this->suuid;
    }

    /**
     *设备识别id
     *
     * Generated from protobuf field <code>string suuid = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setSuuid($var)
    {
        GPBUtil::checkString($var, True);
        $this->suuid = $var;

        return $this;
    }

    /**
     *终端标识，区分不同端来源;
     *
     * Generated from protobuf field <code>string terminal_id = 12;</code>
     * @return string
     */
    public function getTerminalId()
    {
        return $this->terminal_id;
    }

    /**
     *终端标识，区分不同端来源;
     *
     * Generated from protobuf field <code>string terminal_id = 12;</code>
     * @param string $var
     * @return $this
     */
    public function setTerminalId($var)
    {
        GPBUtil::checkString($var, True);
        $this->terminal_id = $var;

        return $this;
    }

    /**
     *品牌ID:1滴滴； 2优步；3长平,默认滴滴
     *
     * Generated from protobuf field <code>int32 origin_id = 13;</code>
     * @return int
     */
    public function getOriginId()
    {
        return $this->origin_id;
    }

    /**
     *品牌ID:1滴滴； 2优步；3长平,默认滴滴
     *
     * Generated from protobuf field <code>int32 origin_id = 13;</code>
     * @param int $var
     * @return $this
     */
    public function setOriginId($var)
    {
        GPBUtil::checkInt32($var);
        $this->origin_id = $var;

        return $this;
    }

    /**
     *1 IOS 2 android 3 webapp 4 oepenapi 5 b2b 6 guide
     *
     * Generated from protobuf field <code>int32 platform_type = 14;</code>
     * @return int
     */
    public function getPlatformType()
    {
        return $this->platform_type;
    }

    /**
     *1 IOS 2 android 3 webapp 4 oepenapi 5 b2b 6 guide
     *
     * Generated from protobuf field <code>int32 platform_type = 14;</code>
     * @param int $var
     * @return $this
     */
    public function setPlatformType($var)
    {
        GPBUtil::checkInt32($var);
        $this->platform_type = $var;

        return $this;
    }

    /**
     *第三方平台id 微信openid 支付宝openid
     *
     * Generated from protobuf field <code>string openid = 15;</code>
     * @return string
     */
    public function getOpenid()
    {
        return $this->openid;
    }

    /**
     *第三方平台id 微信openid 支付宝openid
     *
     * Generated from protobuf field <code>string openid = 15;</code>
     * @param string $var
     * @return $this
     */
    public function setOpenid($var)
    {
        GPBUtil::checkString($var, True);
        $this->openid = $var;

        return $this;
    }

    /**
     *暂不知道用途，建议透传主预估参数
     *
     * Generated from protobuf field <code>string from = 16;</code>
     * @return string
     */
    public function getFrom()
    {
        return $this->from;
    }

    /**
     *暂不知道用途，建议透传主预估参数
     *
     * Generated from protobuf field <code>string from = 16;</code>
     * @param string $var
     * @return $this
     */
    public function setFrom($var)
    {
        GPBUtil::checkString($var, True);
        $this->from = $var;

        return $this;
    }

    /**
     *起终点相关信息*
     *
     * Generated from protobuf field <code>double lat = 30;</code>
     * @return float
     */
    public function getLat()
    {
        return $this->lat;
    }

    /**
     *起终点相关信息*
     *
     * Generated from protobuf field <code>double lat = 30;</code>
     * @param float $var
     * @return $this
     */
    public function setLat($var)
    {
        GPBUtil::checkDouble($var);
        $this->lat = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double lng = 31;</code>
     * @return float
     */
    public function getLng()
    {
        return $this->lng;
    }

    /**
     * Generated from protobuf field <code>double lng = 31;</code>
     * @param float $var
     * @return $this
     */
    public function setLng($var)
    {
        GPBUtil::checkDouble($var);
        $this->lng = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double from_lat = 32;</code>
     * @return float
     */
    public function getFromLat()
    {
        return $this->from_lat;
    }

    /**
     * Generated from protobuf field <code>double from_lat = 32;</code>
     * @param float $var
     * @return $this
     */
    public function setFromLat($var)
    {
        GPBUtil::checkDouble($var);
        $this->from_lat = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double from_lng = 33;</code>
     * @return float
     */
    public function getFromLng()
    {
        return $this->from_lng;
    }

    /**
     * Generated from protobuf field <code>double from_lng = 33;</code>
     * @param float $var
     * @return $this
     */
    public function setFromLng($var)
    {
        GPBUtil::checkDouble($var);
        $this->from_lng = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_poi_id = 34;</code>
     * @return string
     */
    public function getFromPoiId()
    {
        return $this->from_poi_id;
    }

    /**
     * Generated from protobuf field <code>string from_poi_id = 34;</code>
     * @param string $var
     * @return $this
     */
    public function setFromPoiId($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_poi_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_poi_type = 35;</code>
     * @return string
     */
    public function getFromPoiType()
    {
        return $this->from_poi_type;
    }

    /**
     * Generated from protobuf field <code>string from_poi_type = 35;</code>
     * @param string $var
     * @return $this
     */
    public function setFromPoiType($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_poi_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_address = 36;</code>
     * @return string
     */
    public function getFromAddress()
    {
        return $this->from_address;
    }

    /**
     * Generated from protobuf field <code>string from_address = 36;</code>
     * @param string $var
     * @return $this
     */
    public function setFromAddress($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_address = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string from_name = 37;</code>
     * @return string
     */
    public function getFromName()
    {
        return $this->from_name;
    }

    /**
     * Generated from protobuf field <code>string from_name = 37;</code>
     * @param string $var
     * @return $this
     */
    public function setFromName($var)
    {
        GPBUtil::checkString($var, True);
        $this->from_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double to_lat = 38;</code>
     * @return float
     */
    public function getToLat()
    {
        return $this->to_lat;
    }

    /**
     * Generated from protobuf field <code>double to_lat = 38;</code>
     * @param float $var
     * @return $this
     */
    public function setToLat($var)
    {
        GPBUtil::checkDouble($var);
        $this->to_lat = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double to_lng = 39;</code>
     * @return float
     */
    public function getToLng()
    {
        return $this->to_lng;
    }

    /**
     * Generated from protobuf field <code>double to_lng = 39;</code>
     * @param float $var
     * @return $this
     */
    public function setToLng($var)
    {
        GPBUtil::checkDouble($var);
        $this->to_lng = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_poi_id = 40;</code>
     * @return string
     */
    public function getToPoiId()
    {
        return $this->to_poi_id;
    }

    /**
     * Generated from protobuf field <code>string to_poi_id = 40;</code>
     * @param string $var
     * @return $this
     */
    public function setToPoiId($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_poi_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_poi_type = 41;</code>
     * @return string
     */
    public function getToPoiType()
    {
        return $this->to_poi_type;
    }

    /**
     * Generated from protobuf field <code>string to_poi_type = 41;</code>
     * @param string $var
     * @return $this
     */
    public function setToPoiType($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_poi_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_address = 42;</code>
     * @return string
     */
    public function getToAddress()
    {
        return $this->to_address;
    }

    /**
     * Generated from protobuf field <code>string to_address = 42;</code>
     * @param string $var
     * @return $this
     */
    public function setToAddress($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_address = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_name = 43;</code>
     * @return string
     */
    public function getToName()
    {
        return $this->to_name;
    }

    /**
     * Generated from protobuf field <code>string to_name = 43;</code>
     * @param string $var
     * @return $this
     */
    public function setToName($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string dest_poi_code = 44;</code>
     * @return string
     */
    public function getDestPoiCode()
    {
        return $this->dest_poi_code;
    }

    /**
     * Generated from protobuf field <code>string dest_poi_code = 44;</code>
     * @param string $var
     * @return $this
     */
    public function setDestPoiCode($var)
    {
        GPBUtil::checkString($var, True);
        $this->dest_poi_code = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string dest_poi_tag = 45;</code>
     * @return string
     */
    public function getDestPoiTag()
    {
        return $this->dest_poi_tag;
    }

    /**
     * Generated from protobuf field <code>string dest_poi_tag = 45;</code>
     * @param string $var
     * @return $this
     */
    public function setDestPoiTag($var)
    {
        GPBUtil::checkString($var, True);
        $this->dest_poi_tag = $var;

        return $this;
    }

    /**
     *是否使用溢价保护
     *
     * Generated from protobuf field <code>string use_dpa = 46;</code>
     * @return string
     */
    public function getUseDpa()
    {
        return $this->use_dpa;
    }

    /**
     *是否使用溢价保护
     *
     * Generated from protobuf field <code>string use_dpa = 46;</code>
     * @param string $var
     * @return $this
     */
    public function setUseDpa($var)
    {
        GPBUtil::checkString($var, True);
        $this->use_dpa = $var;

        return $this;
    }

    /**
     *顶导ID
     *
     * Generated from protobuf field <code>string menu_id = 50;</code>
     * @return string
     */
    public function getMenuId()
    {
        return $this->menu_id;
    }

    /**
     *顶导ID
     *
     * Generated from protobuf field <code>string menu_id = 50;</code>
     * @param string $var
     * @return $this
     */
    public function setMenuId($var)
    {
        GPBUtil::checkString($var, True);
        $this->menu_id = $var;

        return $this;
    }

    /**
     *页面类型
     *
     * Generated from protobuf field <code>int32 page_type = 51;</code>
     * @return int
     */
    public function getPageType()
    {
        return $this->page_type;
    }

    /**
     *页面类型
     *
     * Generated from protobuf field <code>int32 page_type = 51;</code>
     * @param int $var
     * @return $this
     */
    public function setPageType($var)
    {
        GPBUtil::checkInt32($var);
        $this->page_type = $var;

        return $this;
    }

    /**
     *代叫类型
     *
     * Generated from protobuf field <code>int32 call_car_type = 52;</code>
     * @return int
     */
    public function getCallCarType()
    {
        return $this->call_car_type;
    }

    /**
     *代叫类型
     *
     * Generated from protobuf field <code>int32 call_car_type = 52;</code>
     * @param int $var
     * @return $this
     */
    public function setCallCarType($var)
    {
        GPBUtil::checkInt32($var);
        $this->call_car_type = $var;

        return $this;
    }

    /**
     *代叫手机号
     *
     * Generated from protobuf field <code>string call_car_phone = 53;</code>
     * @return string
     */
    public function getCallCarPhone()
    {
        return $this->call_car_phone;
    }

    /**
     *代叫手机号
     *
     * Generated from protobuf field <code>string call_car_phone = 53;</code>
     * @param string $var
     * @return $this
     */
    public function setCallCarPhone($var)
    {
        GPBUtil::checkString($var, True);
        $this->call_car_phone = $var;

        return $this;
    }

    /**
     *用户类型：1普通用户；2企业用户
     *
     * Generated from protobuf field <code>int32 user_type = 54;</code>
     * @return int
     */
    public function getUserType()
    {
        return $this->user_type;
    }

    /**
     *用户类型：1普通用户；2企业用户
     *
     * Generated from protobuf field <code>int32 user_type = 54;</code>
     * @param int $var
     * @return $this
     */
    public function setUserType($var)
    {
        GPBUtil::checkInt32($var);
        $this->user_type = $var;

        return $this;
    }

    /**
     *时间戳
     *
     * Generated from protobuf field <code>string departure_time = 55;</code>
     * @return string
     */
    public function getDepartureTime()
    {
        return $this->departure_time;
    }

    /**
     *时间戳
     *
     * Generated from protobuf field <code>string departure_time = 55;</code>
     * @param string $var
     * @return $this
     */
    public function setDepartureTime($var)
    {
        GPBUtil::checkString($var, True);
        $this->departure_time = $var;

        return $this;
    }

    /**
     *支付类型
     *
     * Generated from protobuf field <code>int32 payments_type = 56;</code>
     * @return int
     */
    public function getPaymentsType()
    {
        return $this->payments_type;
    }

    /**
     *支付类型
     *
     * Generated from protobuf field <code>int32 payments_type = 56;</code>
     * @param int $var
     * @return $this
     */
    public function setPaymentsType($var)
    {
        GPBUtil::checkInt32($var);
        $this->payments_type = $var;

        return $this;
    }

    /**
     *订单类型
     *
     * Generated from protobuf field <code>int32 order_type = 57;</code>
     * @return int
     */
    public function getOrderType()
    {
        return $this->order_type;
    }

    /**
     *订单类型
     *
     * Generated from protobuf field <code>int32 order_type = 57;</code>
     * @param int $var
     * @return $this
     */
    public function setOrderType($var)
    {
        GPBUtil::checkInt32($var);
        $this->order_type = $var;

        return $this;
    }

    /**
     *原始入口页面（如预约跳转接送机）
     *
     * Generated from protobuf field <code>int32 origin_page_type = 58;</code>
     * @return int
     */
    public function getOriginPageType()
    {
        return $this->origin_page_type;
    }

    /**
     *原始入口页面（如预约跳转接送机）
     *
     * Generated from protobuf field <code>int32 origin_page_type = 58;</code>
     * @param int $var
     * @return $this
     */
    public function setOriginPageType($var)
    {
        GPBUtil::checkInt32($var);
        $this->origin_page_type = $var;

        return $this;
    }

    /**
     *航班出发地三字码,如CTU
     *
     * Generated from protobuf field <code>string flight_dep_code = 120;</code>
     * @return string
     */
    public function getFlightDepCode()
    {
        return isset($this->flight_dep_code) ? $this->flight_dep_code : '';
    }

    public function hasFlightDepCode()
    {
        return isset($this->flight_dep_code);
    }

    public function clearFlightDepCode()
    {
        unset($this->flight_dep_code);
    }

    /**
     *航班出发地三字码,如CTU
     *
     * Generated from protobuf field <code>string flight_dep_code = 120;</code>
     * @param string $var
     * @return $this
     */
    public function setFlightDepCode($var)
    {
        GPBUtil::checkString($var, True);
        $this->flight_dep_code = $var;

        return $this;
    }

    /**
     *航班出发航站楼，如T2
     *
     * Generated from protobuf field <code>string flight_dep_terminal = 121;</code>
     * @return string
     */
    public function getFlightDepTerminal()
    {
        return isset($this->flight_dep_terminal) ? $this->flight_dep_terminal : '';
    }

    public function hasFlightDepTerminal()
    {
        return isset($this->flight_dep_terminal);
    }

    public function clearFlightDepTerminal()
    {
        unset($this->flight_dep_terminal);
    }

    /**
     *航班出发航站楼，如T2
     *
     * Generated from protobuf field <code>string flight_dep_terminal = 121;</code>
     * @param string $var
     * @return $this
     */
    public function setFlightDepTerminal($var)
    {
        GPBUtil::checkString($var, True);
        $this->flight_dep_terminal = $var;

        return $this;
    }

    /**
     *航班起飞时间字符串
     *
     * Generated from protobuf field <code>string traffic_dep_time = 122;</code>
     * @return string
     */
    public function getTrafficDepTime()
    {
        return isset($this->traffic_dep_time) ? $this->traffic_dep_time : '';
    }

    public function hasTrafficDepTime()
    {
        return isset($this->traffic_dep_time);
    }

    public function clearTrafficDepTime()
    {
        unset($this->traffic_dep_time);
    }

    /**
     *航班起飞时间字符串
     *
     * Generated from protobuf field <code>string traffic_dep_time = 122;</code>
     * @param string $var
     * @return $this
     */
    public function setTrafficDepTime($var)
    {
        GPBUtil::checkString($var, True);
        $this->traffic_dep_time = $var;

        return $this;
    }

    /**
     *航班落地三字码,如CTU
     *
     * Generated from protobuf field <code>string flight_arr_code = 123;</code>
     * @return string
     */
    public function getFlightArrCode()
    {
        return isset($this->flight_arr_code) ? $this->flight_arr_code : '';
    }

    public function hasFlightArrCode()
    {
        return isset($this->flight_arr_code);
    }

    public function clearFlightArrCode()
    {
        unset($this->flight_arr_code);
    }

    /**
     *航班落地三字码,如CTU
     *
     * Generated from protobuf field <code>string flight_arr_code = 123;</code>
     * @param string $var
     * @return $this
     */
    public function setFlightArrCode($var)
    {
        GPBUtil::checkString($var, True);
        $this->flight_arr_code = $var;

        return $this;
    }

    /**
     *航班落地航站楼，如T2
     *
     * Generated from protobuf field <code>string flight_arr_terminal = 124;</code>
     * @return string
     */
    public function getFlightArrTerminal()
    {
        return isset($this->flight_arr_terminal) ? $this->flight_arr_terminal : '';
    }

    public function hasFlightArrTerminal()
    {
        return isset($this->flight_arr_terminal);
    }

    public function clearFlightArrTerminal()
    {
        unset($this->flight_arr_terminal);
    }

    /**
     *航班落地航站楼，如T2
     *
     * Generated from protobuf field <code>string flight_arr_terminal = 124;</code>
     * @param string $var
     * @return $this
     */
    public function setFlightArrTerminal($var)
    {
        GPBUtil::checkString($var, True);
        $this->flight_arr_terminal = $var;

        return $this;
    }

    /**
     *航班到达时间字符串
     *
     * Generated from protobuf field <code>string traffic_arr_time = 125;</code>
     * @return string
     */
    public function getTrafficArrTime()
    {
        return isset($this->traffic_arr_time) ? $this->traffic_arr_time : '';
    }

    public function hasTrafficArrTime()
    {
        return isset($this->traffic_arr_time);
    }

    public function clearTrafficArrTime()
    {
        unset($this->traffic_arr_time);
    }

    /**
     *航班到达时间字符串
     *
     * Generated from protobuf field <code>string traffic_arr_time = 125;</code>
     * @param string $var
     * @return $this
     */
    public function setTrafficArrTime($var)
    {
        GPBUtil::checkString($var, True);
        $this->traffic_arr_time = $var;

        return $this;
    }

    /**
     *航班号，如CA1405
     *
     * Generated from protobuf field <code>string traffic_number = 126;</code>
     * @return string
     */
    public function getTrafficNumber()
    {
        return isset($this->traffic_number) ? $this->traffic_number : '';
    }

    public function hasTrafficNumber()
    {
        return isset($this->traffic_number);
    }

    public function clearTrafficNumber()
    {
        unset($this->traffic_number);
    }

    /**
     *航班号，如CA1405
     *
     * Generated from protobuf field <code>string traffic_number = 126;</code>
     * @param string $var
     * @return $this
     */
    public function setTrafficNumber($var)
    {
        GPBUtil::checkString($var, True);
        $this->traffic_number = $var;

        return $this;
    }

    /**
     *接送机类型 1-接机，2-送机，端上入口，无入口传0
     *
     * Generated from protobuf field <code>int32 airport_type = 127;</code>
     * @return int
     */
    public function getAirportType()
    {
        return isset($this->airport_type) ? $this->airport_type : 0;
    }

    public function hasAirportType()
    {
        return isset($this->airport_type);
    }

    public function clearAirportType()
    {
        unset($this->airport_type);
    }

    /**
     *接送机类型 1-接机，2-送机，端上入口，无入口传0
     *
     * Generated from protobuf field <code>int32 airport_type = 127;</code>
     * @param int $var
     * @return $this
     */
    public function setAirportType($var)
    {
        GPBUtil::checkInt32($var);
        $this->airport_type = $var;

        return $this;
    }

    /**
     *接机时为航班落地航站楼id，送机时为出发航站楼id，如1573
     *
     * Generated from protobuf field <code>int32 airport_id = 128;</code>
     * @return int
     */
    public function getAirportId()
    {
        return isset($this->airport_id) ? $this->airport_id : 0;
    }

    public function hasAirportId()
    {
        return isset($this->airport_id);
    }

    public function clearAirportId()
    {
        unset($this->airport_id);
    }

    /**
     *接机时为航班落地航站楼id，送机时为出发航站楼id，如1573
     *
     * Generated from protobuf field <code>int32 airport_id = 128;</code>
     * @param int $var
     * @return $this
     */
    public function setAirportId($var)
    {
        GPBUtil::checkInt32($var);
        $this->airport_id = $var;

        return $this;
    }

    /**
     *用车偏移时间（接机时，单位：秒）
     *
     * Generated from protobuf field <code>int32 shift_time = 129;</code>
     * @return int
     */
    public function getShiftTime()
    {
        return isset($this->shift_time) ? $this->shift_time : 0;
    }

    public function hasShiftTime()
    {
        return isset($this->shift_time);
    }

    public function clearShiftTime()
    {
        unset($this->shift_time);
    }

    /**
     *用车偏移时间（接机时，单位：秒）
     *
     * Generated from protobuf field <code>int32 shift_time = 129;</code>
     * @param int $var
     * @return $this
     */
    public function setShiftTime($var)
    {
        GPBUtil::checkInt32($var);
        $this->shift_time = $var;

        return $this;
    }

    /**
     *用车偏移时间（接机时，单位：秒）
     *
     * Generated from protobuf field <code>int32 railway_type = 130;</code>
     * @return int
     */
    public function getRailwayType()
    {
        return isset($this->railway_type) ? $this->railway_type : 0;
    }

    public function hasRailwayType()
    {
        return isset($this->railway_type);
    }

    public function clearRailwayType()
    {
        unset($this->railway_type);
    }

    /**
     *用车偏移时间（接机时，单位：秒）
     *
     * Generated from protobuf field <code>int32 railway_type = 130;</code>
     * @param int $var
     * @return $this
     */
    public function setRailwayType($var)
    {
        GPBUtil::checkInt32($var);
        $this->railway_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 railway_id = 131;</code>
     * @return int
     */
    public function getRailwayId()
    {
        return isset($this->railway_id) ? $this->railway_id : 0;
    }

    public function hasRailwayId()
    {
        return isset($this->railway_id);
    }

    public function clearRailwayId()
    {
        unset($this->railway_id);
    }

    /**
     * Generated from protobuf field <code>int32 railway_id = 131;</code>
     * @param int $var
     * @return $this
     */
    public function setRailwayId($var)
    {
        GPBUtil::checkInt32($var);
        $this->railway_id = $var;

        return $this;
    }

    /**
     *选中车型, 示例: 1000 1500
     *
     * Generated from protobuf field <code>string luxury_select_carlevels = 140;</code>
     * @return string
     */
    public function getLuxurySelectCarlevels()
    {
        return isset($this->luxury_select_carlevels) ? $this->luxury_select_carlevels : '';
    }

    public function hasLuxurySelectCarlevels()
    {
        return isset($this->luxury_select_carlevels);
    }

    public function clearLuxurySelectCarlevels()
    {
        unset($this->luxury_select_carlevels);
    }

    /**
     *选中车型, 示例: 1000 1500
     *
     * Generated from protobuf field <code>string luxury_select_carlevels = 140;</code>
     * @param string $var
     * @return $this
     */
    public function setLuxurySelectCarlevels($var)
    {
        GPBUtil::checkString($var, True);
        $this->luxury_select_carlevels = $var;

        return $this;
    }

    /**
     *选中司机 示例: -1 580543123784568
     *
     * Generated from protobuf field <code>string luxury_select_driver = 141;</code>
     * @return string
     */
    public function getLuxurySelectDriver()
    {
        return isset($this->luxury_select_driver) ? $this->luxury_select_driver : '';
    }

    public function hasLuxurySelectDriver()
    {
        return isset($this->luxury_select_driver);
    }

    public function clearLuxurySelectDriver()
    {
        unset($this->luxury_select_driver);
    }

    /**
     *选中司机 示例: -1 580543123784568
     *
     * Generated from protobuf field <code>string luxury_select_driver = 141;</code>
     * @param string $var
     * @return $this
     */
    public function setLuxurySelectDriver($var)
    {
        GPBUtil::checkString($var, True);
        $this->luxury_select_driver = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 business_id = 142;</code>
     * @return int
     */
    public function getBusinessId()
    {
        return isset($this->business_id) ? $this->business_id : 0;
    }

    public function hasBusinessId()
    {
        return isset($this->business_id);
    }

    public function clearBusinessId()
    {
        unset($this->business_id);
    }

    /**
     * Generated from protobuf field <code>int32 business_id = 142;</code>
     * @param int $var
     * @return $this
     */
    public function setBusinessId($var)
    {
        GPBUtil::checkInt32($var);
        $this->business_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 require_level = 143;</code>
     * @return int
     */
    public function getRequireLevel()
    {
        return isset($this->require_level) ? $this->require_level : 0;
    }

    public function hasRequireLevel()
    {
        return isset($this->require_level);
    }

    public function clearRequireLevel()
    {
        unset($this->require_level);
    }

    /**
     * Generated from protobuf field <code>int32 require_level = 143;</code>
     * @param int $var
     * @return $this
     */
    public function setRequireLevel($var)
    {
        GPBUtil::checkInt32($var);
        $this->require_level = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string oid = 144;</code>
     * @return string
     */
    public function getOid()
    {
        return isset($this->oid) ? $this->oid : '';
    }

    public function hasOid()
    {
        return isset($this->oid);
    }

    public function clearOid()
    {
        unset($this->oid);
    }

    /**
     * Generated from protobuf field <code>string oid = 144;</code>
     * @param string $var
     * @return $this
     */
    public function setOid($var)
    {
        GPBUtil::checkString($var, True);
        $this->oid = $var;

        return $this;
    }

    /**
     *预估tab 0车型 1司机
     *
     * Generated from protobuf field <code>int32 tab_type = 145;</code>
     * @return int
     */
    public function getTabType()
    {
        return isset($this->tab_type) ? $this->tab_type : 0;
    }

    public function hasTabType()
    {
        return isset($this->tab_type);
    }

    public function clearTabType()
    {
        unset($this->tab_type);
    }

    /**
     *预估tab 0车型 1司机
     *
     * Generated from protobuf field <code>int32 tab_type = 145;</code>
     * @param int $var
     * @return $this
     */
    public function setTabType($var)
    {
        GPBUtil::checkInt32($var);
        $this->tab_type = $var;

        return $this;
    }

    /**
     *用户勾选的个性化增值服务
     *
     * Generated from protobuf field <code>string custom_feature = 146;</code>
     * @return string
     */
    public function getCustomFeature()
    {
        return isset($this->custom_feature) ? $this->custom_feature : '';
    }

    public function hasCustomFeature()
    {
        return isset($this->custom_feature);
    }

    public function clearCustomFeature()
    {
        unset($this->custom_feature);
    }

    /**
     *用户勾选的个性化增值服务
     *
     * Generated from protobuf field <code>string custom_feature = 146;</code>
     * @param string $var
     * @return $this
     */
    public function setCustomFeature($var)
    {
        GPBUtil::checkString($var, True);
        $this->custom_feature = $var;

        return $this;
    }

    /**
     *品类类型
     *
     * Generated from protobuf field <code>int32 product_category = 147;</code>
     * @return int
     */
    public function getProductCategory()
    {
        return isset($this->product_category) ? $this->product_category : 0;
    }

    public function hasProductCategory()
    {
        return isset($this->product_category);
    }

    public function clearProductCategory()
    {
        unset($this->product_category);
    }

    /**
     *品类类型
     *
     * Generated from protobuf field <code>int32 product_category = 147;</code>
     * @param int $var
     * @return $this
     */
    public function setProductCategory($var)
    {
        GPBUtil::checkInt32($var);
        $this->product_category = $var;

        return $this;
    }

    /**
     *是否关闭乘客推荐
     *
     * Generated from protobuf field <code>int32 is_close_prefer = 148;</code>
     * @return int
     */
    public function getIsClosePrefer()
    {
        return isset($this->is_close_prefer) ? $this->is_close_prefer : 0;
    }

    public function hasIsClosePrefer()
    {
        return isset($this->is_close_prefer);
    }

    public function clearIsClosePrefer()
    {
        unset($this->is_close_prefer);
    }

    /**
     *是否关闭乘客推荐
     *
     * Generated from protobuf field <code>int32 is_close_prefer = 148;</code>
     * @param int $var
     * @return $this
     */
    public function setIsClosePrefer($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_close_prefer = $var;

        return $this;
    }

    /**
     *是否多勾
     *
     * Generated from protobuf field <code>int32 is_multi_select = 149;</code>
     * @return int
     */
    public function getIsMultiSelect()
    {
        return isset($this->is_multi_select) ? $this->is_multi_select : 0;
    }

    public function hasIsMultiSelect()
    {
        return isset($this->is_multi_select);
    }

    public function clearIsMultiSelect()
    {
        unset($this->is_multi_select);
    }

    /**
     *是否多勾
     *
     * Generated from protobuf field <code>int32 is_multi_select = 149;</code>
     * @param int $var
     * @return $this
     */
    public function setIsMultiSelect($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_multi_select = $var;

        return $this;
    }

    /**
     *是否多勾
     *
     * Generated from protobuf field <code>int32 source_id = 150;</code>
     * @return int
     */
    public function getSourceId()
    {
        return isset($this->source_id) ? $this->source_id : 0;
    }

    public function hasSourceId()
    {
        return isset($this->source_id);
    }

    public function clearSourceId()
    {
        unset($this->source_id);
    }

    /**
     *是否多勾
     *
     * Generated from protobuf field <code>int32 source_id = 150;</code>
     * @param int $var
     * @return $this
     */
    public function setSourceId($var)
    {
        GPBUtil::checkInt32($var);
        $this->source_id = $var;

        return $this;
    }

}

