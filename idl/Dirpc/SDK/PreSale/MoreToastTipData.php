<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.MoreToastTipData</code>
 */
class MoreToastTipData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string text_color = 1;</code>
     */
    protected $text_color = '';
    /**
     * Generated from protobuf field <code>string car_icon = 2;</code>
     */
    protected $car_icon = '';
    /**
     * Generated from protobuf field <code>string bg_color = 3;</code>
     */
    protected $bg_color = '';
    /**
     * Generated from protobuf field <code>string border_color = 4;</code>
     */
    protected $border_color = '';
    /**
     * Generated from protobuf field <code>string text = 5;</code>
     */
    protected $text = '';
    /**
     * Generated from protobuf field <code>string arrow_icon = 6;</code>
     */
    protected $arrow_icon = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $text_color
     *     @type string $car_icon
     *     @type string $bg_color
     *     @type string $border_color
     *     @type string $text
     *     @type string $arrow_icon
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string text_color = 1;</code>
     * @return string
     */
    public function getTextColor()
    {
        return $this->text_color;
    }

    /**
     * Generated from protobuf field <code>string text_color = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTextColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->text_color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string car_icon = 2;</code>
     * @return string
     */
    public function getCarIcon()
    {
        return $this->car_icon;
    }

    /**
     * Generated from protobuf field <code>string car_icon = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setCarIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->car_icon = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string bg_color = 3;</code>
     * @return string
     */
    public function getBgColor()
    {
        return $this->bg_color;
    }

    /**
     * Generated from protobuf field <code>string bg_color = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setBgColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->bg_color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string border_color = 4;</code>
     * @return string
     */
    public function getBorderColor()
    {
        return $this->border_color;
    }

    /**
     * Generated from protobuf field <code>string border_color = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setBorderColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->border_color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string text = 5;</code>
     * @return string
     */
    public function getText()
    {
        return $this->text;
    }

    /**
     * Generated from protobuf field <code>string text = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setText($var)
    {
        GPBUtil::checkString($var, True);
        $this->text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string arrow_icon = 6;</code>
     * @return string
     */
    public function getArrowIcon()
    {
        return $this->arrow_icon;
    }

    /**
     * Generated from protobuf field <code>string arrow_icon = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setArrowIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->arrow_icon = $var;

        return $this;
    }

}

