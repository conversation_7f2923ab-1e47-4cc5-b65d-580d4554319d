<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.RadioSetting</code>
 */
class RadioSetting extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *当前组件对应的字段
     *
     * Generated from protobuf field <code>string field = 1;</code>
     */
    protected $field = '';
    /**
     *常驻 不依赖勾选
     *
     * Generated from protobuf field <code>bool is_hold = 2;</code>
     */
    protected $is_hold = false;
    /**
     *常驻 不依赖勾选
     *
     * Generated from protobuf field <code>int32 selected_value = 3;</code>
     */
    protected $selected_value = 0;
    /**
     *切换勾选后 是否要重新预估
     *
     * Generated from protobuf field <code>bool need_refresh = 4;</code>
     */
    protected $need_refresh = false;
    /**
     *切换勾选后 是否要重新预估
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.RadioOption options = 5;</code>
     */
    private $options;
    /**
     *标识拼座组件在表单上的展示位置，0是左边，1是右边
     *
     * Generated from protobuf field <code>int32 assembly_style = 6;</code>
     */
    protected $assembly_style = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $field
     *          当前组件对应的字段
     *     @type bool $is_hold
     *          常驻 不依赖勾选
     *     @type int $selected_value
     *          常驻 不依赖勾选
     *     @type bool $need_refresh
     *          切换勾选后 是否要重新预估
     *     @type \Dirpc\SDK\PreSale\RadioOption[]|\Nuwa\Protobuf\Internal\RepeatedField $options
     *          切换勾选后 是否要重新预估
     *     @type int $assembly_style
     *          标识拼座组件在表单上的展示位置，0是左边，1是右边
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *当前组件对应的字段
     *
     * Generated from protobuf field <code>string field = 1;</code>
     * @return string
     */
    public function getField()
    {
        return $this->field;
    }

    /**
     *当前组件对应的字段
     *
     * Generated from protobuf field <code>string field = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setField($var)
    {
        GPBUtil::checkString($var, True);
        $this->field = $var;

        return $this;
    }

    /**
     *常驻 不依赖勾选
     *
     * Generated from protobuf field <code>bool is_hold = 2;</code>
     * @return bool
     */
    public function getIsHold()
    {
        return $this->is_hold;
    }

    /**
     *常驻 不依赖勾选
     *
     * Generated from protobuf field <code>bool is_hold = 2;</code>
     * @param bool $var
     * @return $this
     */
    public function setIsHold($var)
    {
        GPBUtil::checkBool($var);
        $this->is_hold = $var;

        return $this;
    }

    /**
     *常驻 不依赖勾选
     *
     * Generated from protobuf field <code>int32 selected_value = 3;</code>
     * @return int
     */
    public function getSelectedValue()
    {
        return $this->selected_value;
    }

    /**
     *常驻 不依赖勾选
     *
     * Generated from protobuf field <code>int32 selected_value = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setSelectedValue($var)
    {
        GPBUtil::checkInt32($var);
        $this->selected_value = $var;

        return $this;
    }

    /**
     *切换勾选后 是否要重新预估
     *
     * Generated from protobuf field <code>bool need_refresh = 4;</code>
     * @return bool
     */
    public function getNeedRefresh()
    {
        return $this->need_refresh;
    }

    /**
     *切换勾选后 是否要重新预估
     *
     * Generated from protobuf field <code>bool need_refresh = 4;</code>
     * @param bool $var
     * @return $this
     */
    public function setNeedRefresh($var)
    {
        GPBUtil::checkBool($var);
        $this->need_refresh = $var;

        return $this;
    }

    /**
     *切换勾选后 是否要重新预估
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.RadioOption options = 5;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getOptions()
    {
        return $this->options;
    }

    /**
     *切换勾选后 是否要重新预估
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.RadioOption options = 5;</code>
     * @param \Dirpc\SDK\PreSale\RadioOption[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setOptions($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\RadioOption::class);
        $this->options = $arr;

        return $this;
    }

    /**
     *标识拼座组件在表单上的展示位置，0是左边，1是右边
     *
     * Generated from protobuf field <code>int32 assembly_style = 6;</code>
     * @return int
     */
    public function getAssemblyStyle()
    {
        return $this->assembly_style;
    }

    /**
     *标识拼座组件在表单上的展示位置，0是左边，1是右边
     *
     * Generated from protobuf field <code>int32 assembly_style = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setAssemblyStyle($var)
    {
        GPBUtil::checkInt32($var);
        $this->assembly_style = $var;

        return $this;
    }

}

