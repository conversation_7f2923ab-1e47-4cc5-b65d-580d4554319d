<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.BargainFeeMarginNode</code>
 */
class BargainFeeMarginNode extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *金额
     *
     * Generated from protobuf field <code>string amount = 1;</code>
     */
    protected $amount = null;
    /**
     *提示
     *
     * Generated from protobuf field <code>string notice = 2;</code>
     */
    protected $notice = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $amount
     *          金额
     *     @type string $notice
     *          提示
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *金额
     *
     * Generated from protobuf field <code>string amount = 1;</code>
     * @return string
     */
    public function getAmount()
    {
        return isset($this->amount) ? $this->amount : '';
    }

    public function hasAmount()
    {
        return isset($this->amount);
    }

    public function clearAmount()
    {
        unset($this->amount);
    }

    /**
     *金额
     *
     * Generated from protobuf field <code>string amount = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setAmount($var)
    {
        GPBUtil::checkString($var, True);
        $this->amount = $var;

        return $this;
    }

    /**
     *提示
     *
     * Generated from protobuf field <code>string notice = 2;</code>
     * @return string
     */
    public function getNotice()
    {
        return isset($this->notice) ? $this->notice : '';
    }

    public function hasNotice()
    {
        return isset($this->notice);
    }

    public function clearNotice()
    {
        unset($this->notice);
    }

    /**
     *提示
     *
     * Generated from protobuf field <code>string notice = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setNotice($var)
    {
        GPBUtil::checkString($var, True);
        $this->notice = $var;

        return $this;
    }

}

