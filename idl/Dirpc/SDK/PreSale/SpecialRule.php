<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SpecialRule</code>
 */
class SpecialRule extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *多场景通用文案
     *
     * Generated from protobuf field <code>string multi_scene_text = 1;</code>
     */
    protected $multi_scene_text = '';
    /**
     *单场景多车型文案 key为RuleType
     *
     * Generated from protobuf field <code>map<string, string> single_scene_text = 2;</code>
     */
    private $single_scene_text;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $multi_scene_text
     *          多场景通用文案
     *     @type array|\Nuwa\Protobuf\Internal\MapField $single_scene_text
     *          单场景多车型文案 key为RuleType
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *多场景通用文案
     *
     * Generated from protobuf field <code>string multi_scene_text = 1;</code>
     * @return string
     */
    public function getMultiSceneText()
    {
        return $this->multi_scene_text;
    }

    /**
     *多场景通用文案
     *
     * Generated from protobuf field <code>string multi_scene_text = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setMultiSceneText($var)
    {
        GPBUtil::checkString($var, True);
        $this->multi_scene_text = $var;

        return $this;
    }

    /**
     *单场景多车型文案 key为RuleType
     *
     * Generated from protobuf field <code>map<string, string> single_scene_text = 2;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getSingleSceneText()
    {
        return $this->single_scene_text;
    }

    /**
     *单场景多车型文案 key为RuleType
     *
     * Generated from protobuf field <code>map<string, string> single_scene_text = 2;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setSingleSceneText($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->single_scene_text = $arr;

        return $this;
    }

}

