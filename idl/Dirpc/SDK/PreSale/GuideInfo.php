<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.GuideInfo</code>
 */
class GuideInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 show_type = 1;</code>
     */
    protected $show_type = 0;
    /**
     * Generated from protobuf field <code>string title = 2;</code>
     */
    protected $title = null;
    /**
     * Generated from protobuf field <code>string background_url = 3;</code>
     */
    protected $background_url = null;
    /**
     * Generated from protobuf field <code>string link_url = 4;</code>
     */
    protected $link_url = null;
    /**
     * Generated from protobuf field <code>string link_text = 5;</code>
     */
    protected $link_text = null;
    /**
     * Generated from protobuf field <code>string dialog_id = 6;</code>
     */
    protected $dialog_id = null;
    /**
     * Generated from protobuf field <code>string dialog_close_type = 7;</code>
     */
    protected $dialog_close_type = null;
    /**
     * Generated from protobuf field <code>int32 select_product_category = 8;</code>
     */
    protected $select_product_category = null;
    /**
     * Generated from protobuf field <code>string left_button_text = 9;</code>
     */
    protected $left_button_text = null;
    /**
     * Generated from protobuf field <code>string right_button_text = 10;</code>
     */
    protected $right_button_text = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $show_type
     *     @type string $title
     *     @type string $background_url
     *     @type string $link_url
     *     @type string $link_text
     *     @type string $dialog_id
     *     @type string $dialog_close_type
     *     @type int $select_product_category
     *     @type string $left_button_text
     *     @type string $right_button_text
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 show_type = 1;</code>
     * @return int
     */
    public function getShowType()
    {
        return $this->show_type;
    }

    /**
     * Generated from protobuf field <code>int32 show_type = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setShowType($var)
    {
        GPBUtil::checkInt32($var);
        $this->show_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string title = 2;</code>
     * @return string
     */
    public function getTitle()
    {
        return isset($this->title) ? $this->title : '';
    }

    public function hasTitle()
    {
        return isset($this->title);
    }

    public function clearTitle()
    {
        unset($this->title);
    }

    /**
     * Generated from protobuf field <code>string title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string background_url = 3;</code>
     * @return string
     */
    public function getBackgroundUrl()
    {
        return isset($this->background_url) ? $this->background_url : '';
    }

    public function hasBackgroundUrl()
    {
        return isset($this->background_url);
    }

    public function clearBackgroundUrl()
    {
        unset($this->background_url);
    }

    /**
     * Generated from protobuf field <code>string background_url = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setBackgroundUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->background_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string link_url = 4;</code>
     * @return string
     */
    public function getLinkUrl()
    {
        return isset($this->link_url) ? $this->link_url : '';
    }

    public function hasLinkUrl()
    {
        return isset($this->link_url);
    }

    public function clearLinkUrl()
    {
        unset($this->link_url);
    }

    /**
     * Generated from protobuf field <code>string link_url = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setLinkUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->link_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string link_text = 5;</code>
     * @return string
     */
    public function getLinkText()
    {
        return isset($this->link_text) ? $this->link_text : '';
    }

    public function hasLinkText()
    {
        return isset($this->link_text);
    }

    public function clearLinkText()
    {
        unset($this->link_text);
    }

    /**
     * Generated from protobuf field <code>string link_text = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setLinkText($var)
    {
        GPBUtil::checkString($var, True);
        $this->link_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string dialog_id = 6;</code>
     * @return string
     */
    public function getDialogId()
    {
        return isset($this->dialog_id) ? $this->dialog_id : '';
    }

    public function hasDialogId()
    {
        return isset($this->dialog_id);
    }

    public function clearDialogId()
    {
        unset($this->dialog_id);
    }

    /**
     * Generated from protobuf field <code>string dialog_id = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setDialogId($var)
    {
        GPBUtil::checkString($var, True);
        $this->dialog_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string dialog_close_type = 7;</code>
     * @return string
     */
    public function getDialogCloseType()
    {
        return isset($this->dialog_close_type) ? $this->dialog_close_type : '';
    }

    public function hasDialogCloseType()
    {
        return isset($this->dialog_close_type);
    }

    public function clearDialogCloseType()
    {
        unset($this->dialog_close_type);
    }

    /**
     * Generated from protobuf field <code>string dialog_close_type = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setDialogCloseType($var)
    {
        GPBUtil::checkString($var, True);
        $this->dialog_close_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 select_product_category = 8;</code>
     * @return int
     */
    public function getSelectProductCategory()
    {
        return isset($this->select_product_category) ? $this->select_product_category : 0;
    }

    public function hasSelectProductCategory()
    {
        return isset($this->select_product_category);
    }

    public function clearSelectProductCategory()
    {
        unset($this->select_product_category);
    }

    /**
     * Generated from protobuf field <code>int32 select_product_category = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setSelectProductCategory($var)
    {
        GPBUtil::checkInt32($var);
        $this->select_product_category = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string left_button_text = 9;</code>
     * @return string
     */
    public function getLeftButtonText()
    {
        return isset($this->left_button_text) ? $this->left_button_text : '';
    }

    public function hasLeftButtonText()
    {
        return isset($this->left_button_text);
    }

    public function clearLeftButtonText()
    {
        unset($this->left_button_text);
    }

    /**
     * Generated from protobuf field <code>string left_button_text = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setLeftButtonText($var)
    {
        GPBUtil::checkString($var, True);
        $this->left_button_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string right_button_text = 10;</code>
     * @return string
     */
    public function getRightButtonText()
    {
        return isset($this->right_button_text) ? $this->right_button_text : '';
    }

    public function hasRightButtonText()
    {
        return isset($this->right_button_text);
    }

    public function clearRightButtonText()
    {
        unset($this->right_button_text);
    }

    /**
     * Generated from protobuf field <code>string right_button_text = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setRightButtonText($var)
    {
        GPBUtil::checkString($var, True);
        $this->right_button_text = $var;

        return $this;
    }

}

