<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: carpool.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.InvitationButtonV2</code>
 */
class InvitationButtonV2 extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 type = 1;</code>
     */
    protected $type = 0;
    /**
     * Generated from protobuf field <code>string img = 2;</code>
     */
    protected $img = '';
    /**
     * Generated from protobuf field <code>string jump_url = 3;</code>
     */
    protected $jump_url = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationButtonV2Param params = 4;</code>
     */
    protected $params = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $type
     *     @type string $img
     *     @type string $jump_url
     *     @type \Dirpc\SDK\PreSale\InvitationButtonV2Param $params
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\Carpool::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 type = 1;</code>
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Generated from protobuf field <code>int32 type = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkInt32($var);
        $this->type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string img = 2;</code>
     * @return string
     */
    public function getImg()
    {
        return $this->img;
    }

    /**
     * Generated from protobuf field <code>string img = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setImg($var)
    {
        GPBUtil::checkString($var, True);
        $this->img = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string jump_url = 3;</code>
     * @return string
     */
    public function getJumpUrl()
    {
        return isset($this->jump_url) ? $this->jump_url : '';
    }

    public function hasJumpUrl()
    {
        return isset($this->jump_url);
    }

    public function clearJumpUrl()
    {
        unset($this->jump_url);
    }

    /**
     * Generated from protobuf field <code>string jump_url = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setJumpUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->jump_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationButtonV2Param params = 4;</code>
     * @return \Dirpc\SDK\PreSale\InvitationButtonV2Param
     */
    public function getParams()
    {
        return isset($this->params) ? $this->params : null;
    }

    public function hasParams()
    {
        return isset($this->params);
    }

    public function clearParams()
    {
        unset($this->params);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InvitationButtonV2Param params = 4;</code>
     * @param \Dirpc\SDK\PreSale\InvitationButtonV2Param $var
     * @return $this
     */
    public function setParams($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\InvitationButtonV2Param::class);
        $this->params = $var;

        return $this;
    }

}

