<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.InviteOrderInfo</code>
 */
class InviteOrderInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string oid = 1;</code>
     */
    protected $oid = null;
    /**
     * Generated from protobuf field <code>string product_id = 2;</code>
     */
    protected $product_id = null;
    /**
     * Generated from protobuf field <code>string area = 3;</code>
     */
    protected $area = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $oid
     *     @type string $product_id
     *     @type string $area
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string oid = 1;</code>
     * @return string
     */
    public function getOid()
    {
        return isset($this->oid) ? $this->oid : '';
    }

    public function hasOid()
    {
        return isset($this->oid);
    }

    public function clearOid()
    {
        unset($this->oid);
    }

    /**
     * Generated from protobuf field <code>string oid = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setOid($var)
    {
        GPBUtil::checkString($var, True);
        $this->oid = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string product_id = 2;</code>
     * @return string
     */
    public function getProductId()
    {
        return isset($this->product_id) ? $this->product_id : '';
    }

    public function hasProductId()
    {
        return isset($this->product_id);
    }

    public function clearProductId()
    {
        unset($this->product_id);
    }

    /**
     * Generated from protobuf field <code>string product_id = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setProductId($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string area = 3;</code>
     * @return string
     */
    public function getArea()
    {
        return isset($this->area) ? $this->area : '';
    }

    public function hasArea()
    {
        return isset($this->area);
    }

    public function clearArea()
    {
        unset($this->area);
    }

    /**
     * Generated from protobuf field <code>string area = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setArea($var)
    {
        GPBUtil::checkString($var, True);
        $this->area = $var;

        return $this;
    }

}

