<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NewFormEstimateResponse</code>
 */
class NewFormEstimateResponse extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string estimate_trace_id = 1;</code>
     */
    protected $estimate_trace_id = '';
    /**
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.NewFormEstimateData> estimate_data = 2;</code>
     */
    private $estimate_data;
    /**
     *是否支持多选，预约单时不支持 0-不支持；1-支持
     *
     * Generated from protobuf field <code>int32 is_support_multi_selection = 3;</code>
     */
    protected $is_support_multi_selection = 0;
    /**
     *费用明细页地址
     *
     * Generated from protobuf field <code>string fee_detail_url = 4;</code>
     */
    protected $fee_detail_url = '';
    /**
     *动调、春节服务费等发单拦截页
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PluginPageInfo plugin_page_info = 5;</code>
     */
    protected $plugin_page_info = null;
    /**
     *6.0支付方式并集
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.UserPayInfo user_pay_info = 6;</code>
     */
    protected $user_pay_info = null;
    /**
     *布局
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormLayout layout = 7;</code>
     */
    private $layout;
    /**
     *预估完成后提示文案
     *
     * Generated from protobuf field <code>string toast_tip = 8;</code>
     */
    protected $toast_tip = null;
    /**
     *发单参数 预估级别
     *
     * Generated from protobuf field <code>map<string, string> p_new_order_params = 9;</code>
     */
    private $p_new_order_params;
    /**
     *附加需求信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.AdditionalServiceData additional_service_data = 10;</code>
     */
    protected $additional_service_data = null;
    /**
     *筛选器信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.FilterInfo filter_info = 11;</code>
     */
    protected $filter_info = null;
    /**
     *行程预测
     *
     * Generated from protobuf field <code>map<string, string> travel_forecast = 12;</code>
     */
    private $travel_forecast;
    /**
     *行程预测
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.OperationItem operation_list = 13;</code>
     */
    private $operation_list;
    /**
     *三方表单侧边栏信息
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CategoryData category_info = 14;</code>
     */
    private $category_info;
    /**
     *发单按钮信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OrderButtonInfo order_button_info = 15;</code>
     */
    protected $order_button_info = null;
    /**
     *预约单是否出新样式：0不出，1 出
     *
     * Generated from protobuf field <code>int32 selection_style_type = 16;</code>
     */
    protected $selection_style_type = null;
    /**
     *是否屏蔽代叫按钮 1 屏蔽 0 不屏蔽
     *
     * Generated from protobuf field <code>int32 is_callcar_disabled = 17;</code>
     */
    protected $is_callcar_disabled = null;
    /**
     *提示文案全集
     *
     * Generated from protobuf field <code>map<string, string> multi_route_tips_data = 18;</code>
     */
    private $multi_route_tips_data;
    /**
     *价格轴
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PriceAxle price_axle = 19;</code>
     */
    protected $price_axle = null;
    /**
     *侧边栏展示策略
     *
     * Generated from protobuf field <code>int32 show_category = 20;</code>
     */
    protected $show_category = null;
    /**
     *新版操作台
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NavigationBar navigation_bar = 21;</code>
     */
    private $navigation_bar;
    /**
     *端请求mamba透传参数
     *
     * Generated from protobuf field <code>map<string, int32> real_params = 22;</code>
     */
    private $real_params;
    /**
     *预期信息
     *
     * Generated from protobuf field <code>map<string, int32> expect_params = 23;</code>
     */
    private $expect_params;
    /**
     *盒子信息
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.NewFormGroup> group_data = 24;</code>
     */
    private $group_data;
    /**
     *新版toast
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.MoreToastTipData more_toast_tip = 25;</code>
     */
    protected $more_toast_tip = null;
    /**
     *新推荐表单标识
     *
     * Generated from protobuf field <code>int32 rec_form = 26;</code>
     */
    protected $rec_form = null;
    /**
     *推荐layout
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormLayout rec_layout = 27;</code>
     */
    private $rec_layout;
    /**
     *新style标识
     *
     * Generated from protobuf field <code>int32 form_style_exp = 28;</code>
     */
    protected $form_style_exp = null;
    /**
     *端请求mamba透传参数
     *
     * Generated from protobuf field <code>map<string, int32> side_params = 29;</code>
     */
    private $side_params;
    /**
     *端请求mamba透传参数
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.TabExtraData tab_extra_data = 30;</code>
     */
    protected $tab_extra_data = null;
    /**
     *底部操作台 价格展示模版
     *
     * Generated from protobuf field <code>string fee_msg_template = 31;</code>
     */
    protected $fee_msg_template = null;
    /**
     *是否大屏适配
     *
     * Generated from protobuf field <code>int32 phone_adaptation = 32;</code>
     */
    protected $phone_adaptation = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $estimate_trace_id
     *     @type array|\Nuwa\Protobuf\Internal\MapField $estimate_data
     *     @type int $is_support_multi_selection
     *          是否支持多选，预约单时不支持 0-不支持；1-支持
     *     @type string $fee_detail_url
     *          费用明细页地址
     *     @type \Dirpc\SDK\PreSale\PluginPageInfo $plugin_page_info
     *          动调、春节服务费等发单拦截页
     *     @type \Dirpc\SDK\PreSale\UserPayInfo $user_pay_info
     *          6.0支付方式并集
     *     @type \Dirpc\SDK\PreSale\NewFormLayout[]|\Nuwa\Protobuf\Internal\RepeatedField $layout
     *          布局
     *     @type string $toast_tip
     *          预估完成后提示文案
     *     @type array|\Nuwa\Protobuf\Internal\MapField $p_new_order_params
     *          发单参数 预估级别
     *     @type \Dirpc\SDK\PreSale\AdditionalServiceData $additional_service_data
     *          附加需求信息
     *     @type \Dirpc\SDK\PreSale\FilterInfo $filter_info
     *          筛选器信息
     *     @type array|\Nuwa\Protobuf\Internal\MapField $travel_forecast
     *          行程预测
     *     @type \Dirpc\SDK\PreSale\OperationItem[]|\Nuwa\Protobuf\Internal\RepeatedField $operation_list
     *          行程预测
     *     @type \Dirpc\SDK\PreSale\CategoryData[]|\Nuwa\Protobuf\Internal\RepeatedField $category_info
     *          三方表单侧边栏信息
     *     @type \Dirpc\SDK\PreSale\OrderButtonInfo $order_button_info
     *          发单按钮信息
     *     @type int $selection_style_type
     *          预约单是否出新样式：0不出，1 出
     *     @type int $is_callcar_disabled
     *          是否屏蔽代叫按钮 1 屏蔽 0 不屏蔽
     *     @type array|\Nuwa\Protobuf\Internal\MapField $multi_route_tips_data
     *          提示文案全集
     *     @type \Dirpc\SDK\PreSale\PriceAxle $price_axle
     *          价格轴
     *     @type int $show_category
     *          侧边栏展示策略
     *     @type \Dirpc\SDK\PreSale\NavigationBar[]|\Nuwa\Protobuf\Internal\RepeatedField $navigation_bar
     *          新版操作台
     *     @type array|\Nuwa\Protobuf\Internal\MapField $real_params
     *          端请求mamba透传参数
     *     @type array|\Nuwa\Protobuf\Internal\MapField $expect_params
     *          预期信息
     *     @type array|\Nuwa\Protobuf\Internal\MapField $group_data
     *          盒子信息
     *     @type \Dirpc\SDK\PreSale\MoreToastTipData $more_toast_tip
     *          新版toast
     *     @type int $rec_form
     *          新推荐表单标识
     *     @type \Dirpc\SDK\PreSale\NewFormLayout[]|\Nuwa\Protobuf\Internal\RepeatedField $rec_layout
     *          推荐layout
     *     @type int $form_style_exp
     *          新style标识
     *     @type array|\Nuwa\Protobuf\Internal\MapField $side_params
     *          端请求mamba透传参数
     *     @type \Dirpc\SDK\PreSale\TabExtraData $tab_extra_data
     *          端请求mamba透传参数
     *     @type string $fee_msg_template
     *          底部操作台 价格展示模版
     *     @type int $phone_adaptation
     *          是否大屏适配
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string estimate_trace_id = 1;</code>
     * @return string
     */
    public function getEstimateTraceId()
    {
        return $this->estimate_trace_id;
    }

    /**
     * Generated from protobuf field <code>string estimate_trace_id = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateTraceId($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_trace_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.NewFormEstimateData> estimate_data = 2;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getEstimateData()
    {
        return $this->estimate_data;
    }

    /**
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.NewFormEstimateData> estimate_data = 2;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setEstimateData($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\NewFormEstimateData::class);
        $this->estimate_data = $arr;

        return $this;
    }

    /**
     *是否支持多选，预约单时不支持 0-不支持；1-支持
     *
     * Generated from protobuf field <code>int32 is_support_multi_selection = 3;</code>
     * @return int
     */
    public function getIsSupportMultiSelection()
    {
        return $this->is_support_multi_selection;
    }

    /**
     *是否支持多选，预约单时不支持 0-不支持；1-支持
     *
     * Generated from protobuf field <code>int32 is_support_multi_selection = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setIsSupportMultiSelection($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_support_multi_selection = $var;

        return $this;
    }

    /**
     *费用明细页地址
     *
     * Generated from protobuf field <code>string fee_detail_url = 4;</code>
     * @return string
     */
    public function getFeeDetailUrl()
    {
        return $this->fee_detail_url;
    }

    /**
     *费用明细页地址
     *
     * Generated from protobuf field <code>string fee_detail_url = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeDetailUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_detail_url = $var;

        return $this;
    }

    /**
     *动调、春节服务费等发单拦截页
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PluginPageInfo plugin_page_info = 5;</code>
     * @return \Dirpc\SDK\PreSale\PluginPageInfo
     */
    public function getPluginPageInfo()
    {
        return isset($this->plugin_page_info) ? $this->plugin_page_info : null;
    }

    public function hasPluginPageInfo()
    {
        return isset($this->plugin_page_info);
    }

    public function clearPluginPageInfo()
    {
        unset($this->plugin_page_info);
    }

    /**
     *动调、春节服务费等发单拦截页
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PluginPageInfo plugin_page_info = 5;</code>
     * @param \Dirpc\SDK\PreSale\PluginPageInfo $var
     * @return $this
     */
    public function setPluginPageInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\PluginPageInfo::class);
        $this->plugin_page_info = $var;

        return $this;
    }

    /**
     *6.0支付方式并集
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.UserPayInfo user_pay_info = 6;</code>
     * @return \Dirpc\SDK\PreSale\UserPayInfo
     */
    public function getUserPayInfo()
    {
        return isset($this->user_pay_info) ? $this->user_pay_info : null;
    }

    public function hasUserPayInfo()
    {
        return isset($this->user_pay_info);
    }

    public function clearUserPayInfo()
    {
        unset($this->user_pay_info);
    }

    /**
     *6.0支付方式并集
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.UserPayInfo user_pay_info = 6;</code>
     * @param \Dirpc\SDK\PreSale\UserPayInfo $var
     * @return $this
     */
    public function setUserPayInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\UserPayInfo::class);
        $this->user_pay_info = $var;

        return $this;
    }

    /**
     *布局
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormLayout layout = 7;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getLayout()
    {
        return $this->layout;
    }

    /**
     *布局
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormLayout layout = 7;</code>
     * @param \Dirpc\SDK\PreSale\NewFormLayout[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setLayout($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\NewFormLayout::class);
        $this->layout = $arr;

        return $this;
    }

    /**
     *预估完成后提示文案
     *
     * Generated from protobuf field <code>string toast_tip = 8;</code>
     * @return string
     */
    public function getToastTip()
    {
        return isset($this->toast_tip) ? $this->toast_tip : '';
    }

    public function hasToastTip()
    {
        return isset($this->toast_tip);
    }

    public function clearToastTip()
    {
        unset($this->toast_tip);
    }

    /**
     *预估完成后提示文案
     *
     * Generated from protobuf field <code>string toast_tip = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setToastTip($var)
    {
        GPBUtil::checkString($var, True);
        $this->toast_tip = $var;

        return $this;
    }

    /**
     *发单参数 预估级别
     *
     * Generated from protobuf field <code>map<string, string> p_new_order_params = 9;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getPNewOrderParams()
    {
        return $this->p_new_order_params;
    }

    /**
     *发单参数 预估级别
     *
     * Generated from protobuf field <code>map<string, string> p_new_order_params = 9;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setPNewOrderParams($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->p_new_order_params = $arr;

        return $this;
    }

    /**
     *附加需求信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.AdditionalServiceData additional_service_data = 10;</code>
     * @return \Dirpc\SDK\PreSale\AdditionalServiceData
     */
    public function getAdditionalServiceData()
    {
        return isset($this->additional_service_data) ? $this->additional_service_data : null;
    }

    public function hasAdditionalServiceData()
    {
        return isset($this->additional_service_data);
    }

    public function clearAdditionalServiceData()
    {
        unset($this->additional_service_data);
    }

    /**
     *附加需求信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.AdditionalServiceData additional_service_data = 10;</code>
     * @param \Dirpc\SDK\PreSale\AdditionalServiceData $var
     * @return $this
     */
    public function setAdditionalServiceData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\AdditionalServiceData::class);
        $this->additional_service_data = $var;

        return $this;
    }

    /**
     *筛选器信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.FilterInfo filter_info = 11;</code>
     * @return \Dirpc\SDK\PreSale\FilterInfo
     */
    public function getFilterInfo()
    {
        return isset($this->filter_info) ? $this->filter_info : null;
    }

    public function hasFilterInfo()
    {
        return isset($this->filter_info);
    }

    public function clearFilterInfo()
    {
        unset($this->filter_info);
    }

    /**
     *筛选器信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.FilterInfo filter_info = 11;</code>
     * @param \Dirpc\SDK\PreSale\FilterInfo $var
     * @return $this
     */
    public function setFilterInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\FilterInfo::class);
        $this->filter_info = $var;

        return $this;
    }

    /**
     *行程预测
     *
     * Generated from protobuf field <code>map<string, string> travel_forecast = 12;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getTravelForecast()
    {
        return $this->travel_forecast;
    }

    /**
     *行程预测
     *
     * Generated from protobuf field <code>map<string, string> travel_forecast = 12;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setTravelForecast($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->travel_forecast = $arr;

        return $this;
    }

    /**
     *行程预测
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.OperationItem operation_list = 13;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getOperationList()
    {
        return $this->operation_list;
    }

    /**
     *行程预测
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.OperationItem operation_list = 13;</code>
     * @param \Dirpc\SDK\PreSale\OperationItem[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setOperationList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\OperationItem::class);
        $this->operation_list = $arr;

        return $this;
    }

    /**
     *三方表单侧边栏信息
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CategoryData category_info = 14;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getCategoryInfo()
    {
        return $this->category_info;
    }

    /**
     *三方表单侧边栏信息
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CategoryData category_info = 14;</code>
     * @param \Dirpc\SDK\PreSale\CategoryData[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setCategoryInfo($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\CategoryData::class);
        $this->category_info = $arr;

        return $this;
    }

    /**
     *发单按钮信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OrderButtonInfo order_button_info = 15;</code>
     * @return \Dirpc\SDK\PreSale\OrderButtonInfo
     */
    public function getOrderButtonInfo()
    {
        return isset($this->order_button_info) ? $this->order_button_info : null;
    }

    public function hasOrderButtonInfo()
    {
        return isset($this->order_button_info);
    }

    public function clearOrderButtonInfo()
    {
        unset($this->order_button_info);
    }

    /**
     *发单按钮信息
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OrderButtonInfo order_button_info = 15;</code>
     * @param \Dirpc\SDK\PreSale\OrderButtonInfo $var
     * @return $this
     */
    public function setOrderButtonInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\OrderButtonInfo::class);
        $this->order_button_info = $var;

        return $this;
    }

    /**
     *预约单是否出新样式：0不出，1 出
     *
     * Generated from protobuf field <code>int32 selection_style_type = 16;</code>
     * @return int
     */
    public function getSelectionStyleType()
    {
        return isset($this->selection_style_type) ? $this->selection_style_type : 0;
    }

    public function hasSelectionStyleType()
    {
        return isset($this->selection_style_type);
    }

    public function clearSelectionStyleType()
    {
        unset($this->selection_style_type);
    }

    /**
     *预约单是否出新样式：0不出，1 出
     *
     * Generated from protobuf field <code>int32 selection_style_type = 16;</code>
     * @param int $var
     * @return $this
     */
    public function setSelectionStyleType($var)
    {
        GPBUtil::checkInt32($var);
        $this->selection_style_type = $var;

        return $this;
    }

    /**
     *是否屏蔽代叫按钮 1 屏蔽 0 不屏蔽
     *
     * Generated from protobuf field <code>int32 is_callcar_disabled = 17;</code>
     * @return int
     */
    public function getIsCallcarDisabled()
    {
        return isset($this->is_callcar_disabled) ? $this->is_callcar_disabled : 0;
    }

    public function hasIsCallcarDisabled()
    {
        return isset($this->is_callcar_disabled);
    }

    public function clearIsCallcarDisabled()
    {
        unset($this->is_callcar_disabled);
    }

    /**
     *是否屏蔽代叫按钮 1 屏蔽 0 不屏蔽
     *
     * Generated from protobuf field <code>int32 is_callcar_disabled = 17;</code>
     * @param int $var
     * @return $this
     */
    public function setIsCallcarDisabled($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_callcar_disabled = $var;

        return $this;
    }

    /**
     *提示文案全集
     *
     * Generated from protobuf field <code>map<string, string> multi_route_tips_data = 18;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getMultiRouteTipsData()
    {
        return $this->multi_route_tips_data;
    }

    /**
     *提示文案全集
     *
     * Generated from protobuf field <code>map<string, string> multi_route_tips_data = 18;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setMultiRouteTipsData($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->multi_route_tips_data = $arr;

        return $this;
    }

    /**
     *价格轴
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PriceAxle price_axle = 19;</code>
     * @return \Dirpc\SDK\PreSale\PriceAxle
     */
    public function getPriceAxle()
    {
        return isset($this->price_axle) ? $this->price_axle : null;
    }

    public function hasPriceAxle()
    {
        return isset($this->price_axle);
    }

    public function clearPriceAxle()
    {
        unset($this->price_axle);
    }

    /**
     *价格轴
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PriceAxle price_axle = 19;</code>
     * @param \Dirpc\SDK\PreSale\PriceAxle $var
     * @return $this
     */
    public function setPriceAxle($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\PriceAxle::class);
        $this->price_axle = $var;

        return $this;
    }

    /**
     *侧边栏展示策略
     *
     * Generated from protobuf field <code>int32 show_category = 20;</code>
     * @return int
     */
    public function getShowCategory()
    {
        return isset($this->show_category) ? $this->show_category : 0;
    }

    public function hasShowCategory()
    {
        return isset($this->show_category);
    }

    public function clearShowCategory()
    {
        unset($this->show_category);
    }

    /**
     *侧边栏展示策略
     *
     * Generated from protobuf field <code>int32 show_category = 20;</code>
     * @param int $var
     * @return $this
     */
    public function setShowCategory($var)
    {
        GPBUtil::checkInt32($var);
        $this->show_category = $var;

        return $this;
    }

    /**
     *新版操作台
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NavigationBar navigation_bar = 21;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getNavigationBar()
    {
        return $this->navigation_bar;
    }

    /**
     *新版操作台
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NavigationBar navigation_bar = 21;</code>
     * @param \Dirpc\SDK\PreSale\NavigationBar[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setNavigationBar($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\NavigationBar::class);
        $this->navigation_bar = $arr;

        return $this;
    }

    /**
     *端请求mamba透传参数
     *
     * Generated from protobuf field <code>map<string, int32> real_params = 22;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getRealParams()
    {
        return $this->real_params;
    }

    /**
     *端请求mamba透传参数
     *
     * Generated from protobuf field <code>map<string, int32> real_params = 22;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setRealParams($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::INT32);
        $this->real_params = $arr;

        return $this;
    }

    /**
     *预期信息
     *
     * Generated from protobuf field <code>map<string, int32> expect_params = 23;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getExpectParams()
    {
        return $this->expect_params;
    }

    /**
     *预期信息
     *
     * Generated from protobuf field <code>map<string, int32> expect_params = 23;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setExpectParams($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::INT32);
        $this->expect_params = $arr;

        return $this;
    }

    /**
     *盒子信息
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.NewFormGroup> group_data = 24;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getGroupData()
    {
        return $this->group_data;
    }

    /**
     *盒子信息
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.NewFormGroup> group_data = 24;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setGroupData($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\NewFormGroup::class);
        $this->group_data = $arr;

        return $this;
    }

    /**
     *新版toast
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.MoreToastTipData more_toast_tip = 25;</code>
     * @return \Dirpc\SDK\PreSale\MoreToastTipData
     */
    public function getMoreToastTip()
    {
        return isset($this->more_toast_tip) ? $this->more_toast_tip : null;
    }

    public function hasMoreToastTip()
    {
        return isset($this->more_toast_tip);
    }

    public function clearMoreToastTip()
    {
        unset($this->more_toast_tip);
    }

    /**
     *新版toast
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.MoreToastTipData more_toast_tip = 25;</code>
     * @param \Dirpc\SDK\PreSale\MoreToastTipData $var
     * @return $this
     */
    public function setMoreToastTip($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\MoreToastTipData::class);
        $this->more_toast_tip = $var;

        return $this;
    }

    /**
     *新推荐表单标识
     *
     * Generated from protobuf field <code>int32 rec_form = 26;</code>
     * @return int
     */
    public function getRecForm()
    {
        return isset($this->rec_form) ? $this->rec_form : 0;
    }

    public function hasRecForm()
    {
        return isset($this->rec_form);
    }

    public function clearRecForm()
    {
        unset($this->rec_form);
    }

    /**
     *新推荐表单标识
     *
     * Generated from protobuf field <code>int32 rec_form = 26;</code>
     * @param int $var
     * @return $this
     */
    public function setRecForm($var)
    {
        GPBUtil::checkInt32($var);
        $this->rec_form = $var;

        return $this;
    }

    /**
     *推荐layout
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormLayout rec_layout = 27;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getRecLayout()
    {
        return $this->rec_layout;
    }

    /**
     *推荐layout
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.NewFormLayout rec_layout = 27;</code>
     * @param \Dirpc\SDK\PreSale\NewFormLayout[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setRecLayout($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\NewFormLayout::class);
        $this->rec_layout = $arr;

        return $this;
    }

    /**
     *新style标识
     *
     * Generated from protobuf field <code>int32 form_style_exp = 28;</code>
     * @return int
     */
    public function getFormStyleExp()
    {
        return isset($this->form_style_exp) ? $this->form_style_exp : 0;
    }

    public function hasFormStyleExp()
    {
        return isset($this->form_style_exp);
    }

    public function clearFormStyleExp()
    {
        unset($this->form_style_exp);
    }

    /**
     *新style标识
     *
     * Generated from protobuf field <code>int32 form_style_exp = 28;</code>
     * @param int $var
     * @return $this
     */
    public function setFormStyleExp($var)
    {
        GPBUtil::checkInt32($var);
        $this->form_style_exp = $var;

        return $this;
    }

    /**
     *端请求mamba透传参数
     *
     * Generated from protobuf field <code>map<string, int32> side_params = 29;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getSideParams()
    {
        return $this->side_params;
    }

    /**
     *端请求mamba透传参数
     *
     * Generated from protobuf field <code>map<string, int32> side_params = 29;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setSideParams($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::INT32);
        $this->side_params = $arr;

        return $this;
    }

    /**
     *端请求mamba透传参数
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.TabExtraData tab_extra_data = 30;</code>
     * @return \Dirpc\SDK\PreSale\TabExtraData
     */
    public function getTabExtraData()
    {
        return isset($this->tab_extra_data) ? $this->tab_extra_data : null;
    }

    public function hasTabExtraData()
    {
        return isset($this->tab_extra_data);
    }

    public function clearTabExtraData()
    {
        unset($this->tab_extra_data);
    }

    /**
     *端请求mamba透传参数
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.TabExtraData tab_extra_data = 30;</code>
     * @param \Dirpc\SDK\PreSale\TabExtraData $var
     * @return $this
     */
    public function setTabExtraData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\TabExtraData::class);
        $this->tab_extra_data = $var;

        return $this;
    }

    /**
     *底部操作台 价格展示模版
     *
     * Generated from protobuf field <code>string fee_msg_template = 31;</code>
     * @return string
     */
    public function getFeeMsgTemplate()
    {
        return isset($this->fee_msg_template) ? $this->fee_msg_template : '';
    }

    public function hasFeeMsgTemplate()
    {
        return isset($this->fee_msg_template);
    }

    public function clearFeeMsgTemplate()
    {
        unset($this->fee_msg_template);
    }

    /**
     *底部操作台 价格展示模版
     *
     * Generated from protobuf field <code>string fee_msg_template = 31;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsgTemplate($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg_template = $var;

        return $this;
    }

    /**
     *是否大屏适配
     *
     * Generated from protobuf field <code>int32 phone_adaptation = 32;</code>
     * @return int
     */
    public function getPhoneAdaptation()
    {
        return isset($this->phone_adaptation) ? $this->phone_adaptation : 0;
    }

    public function hasPhoneAdaptation()
    {
        return isset($this->phone_adaptation);
    }

    public function clearPhoneAdaptation()
    {
        unset($this->phone_adaptation);
    }

    /**
     *是否大屏适配
     *
     * Generated from protobuf field <code>int32 phone_adaptation = 32;</code>
     * @param int $var
     * @return $this
     */
    public function setPhoneAdaptation($var)
    {
        GPBUtil::checkInt32($var);
        $this->phone_adaptation = $var;

        return $this;
    }

}

