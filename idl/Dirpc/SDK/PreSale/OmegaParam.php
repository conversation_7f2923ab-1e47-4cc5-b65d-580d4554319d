<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.OmegaParam</code>
 */
class OmegaParam extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string click_name = 1;</code>
     */
    protected $click_name = null;
    /**
     * Generated from protobuf field <code>string show_name = 2;</code>
     */
    protected $show_name = null;
    /**
     * Generated from protobuf field <code>map<string, string> extension = 3;</code>
     */
    private $extension;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $click_name
     *     @type string $show_name
     *     @type array|\Nuwa\Protobuf\Internal\MapField $extension
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string click_name = 1;</code>
     * @return string
     */
    public function getClickName()
    {
        return isset($this->click_name) ? $this->click_name : '';
    }

    public function hasClickName()
    {
        return isset($this->click_name);
    }

    public function clearClickName()
    {
        unset($this->click_name);
    }

    /**
     * Generated from protobuf field <code>string click_name = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setClickName($var)
    {
        GPBUtil::checkString($var, True);
        $this->click_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string show_name = 2;</code>
     * @return string
     */
    public function getShowName()
    {
        return isset($this->show_name) ? $this->show_name : '';
    }

    public function hasShowName()
    {
        return isset($this->show_name);
    }

    public function clearShowName()
    {
        unset($this->show_name);
    }

    /**
     * Generated from protobuf field <code>string show_name = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setShowName($var)
    {
        GPBUtil::checkString($var, True);
        $this->show_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>map<string, string> extension = 3;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getExtension()
    {
        return $this->extension;
    }

    /**
     * Generated from protobuf field <code>map<string, string> extension = 3;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setExtension($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->extension = $arr;

        return $this;
    }

}

