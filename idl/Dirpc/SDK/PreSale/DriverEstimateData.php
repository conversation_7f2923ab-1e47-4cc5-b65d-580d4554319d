<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.DriverEstimateData</code>
 */
class DriverEstimateData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string intro_msg = 1;</code>
     */
    protected $intro_msg = '';
    /**
     * Generated from protobuf field <code>string intro_icon = 2;</code>
     */
    protected $intro_icon = '';
    /**
     * Generated from protobuf field <code>string profile_link = 3;</code>
     */
    protected $profile_link = '';
    /**
     * Generated from protobuf field <code>string fee_detail_url = 4;</code>
     */
    protected $fee_detail_url = '';
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.FeeDescItem fee_desc_info = 5;</code>
     */
    private $fee_desc_info;
    /**
     * Generated from protobuf field <code>string fee_msg = 6;</code>
     */
    protected $fee_msg = null;
    /**
     * Generated from protobuf field <code>string estimate_id = 7;</code>
     */
    protected $estimate_id = '';
    /**
     * Generated from protobuf field <code>int32 require_level = 8;</code>
     */
    protected $require_level = 0;
    /**
     * Generated from protobuf field <code>int32 business_id = 9;</code>
     */
    protected $business_id = 0;
    /**
     * Generated from protobuf field <code>int32 combo_type = 10;</code>
     */
    protected $combo_type = null;
    /**
     * Generated from protobuf field <code>int32 product_category = 11;</code>
     */
    protected $product_category = 0;
    /**
     * Generated from protobuf field <code>string driver_id = 12;</code>
     */
    protected $driver_id = null;
    /**
     * Generated from protobuf field <code>int32 is_real_driver = 13;</code>
     */
    protected $is_real_driver = null;
    /**
     * Generated from protobuf field <code>int32 is_default = 14;</code>
     */
    protected $is_default = 0;
    /**
     * Generated from protobuf field <code>int32 is_online = 15;</code>
     */
    protected $is_online = null;
    /**
     * Generated from protobuf field <code>int32 down_broadcast = 16;</code>
     */
    protected $down_broadcast = null;
    /**
     *企业豪华车费用
     *
     * Generated from protobuf field <code>double estimate_fee = 17;</code>
     */
    protected $estimate_fee = null;
    /**
     *企业豪华车费用
     *
     * Generated from protobuf field <code>string driver_desc = 18;</code>
     */
    protected $driver_desc = '';
    /**
     * Generated from protobuf field <code>string driver_desc_link = 19;</code>
     */
    protected $driver_desc_link = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $intro_msg
     *     @type string $intro_icon
     *     @type string $profile_link
     *     @type string $fee_detail_url
     *     @type \Dirpc\SDK\PreSale\FeeDescItem[]|\Nuwa\Protobuf\Internal\RepeatedField $fee_desc_info
     *     @type string $fee_msg
     *     @type string $estimate_id
     *     @type int $require_level
     *     @type int $business_id
     *     @type int $combo_type
     *     @type int $product_category
     *     @type string $driver_id
     *     @type int $is_real_driver
     *     @type int $is_default
     *     @type int $is_online
     *     @type int $down_broadcast
     *     @type float $estimate_fee
     *          企业豪华车费用
     *     @type string $driver_desc
     *          企业豪华车费用
     *     @type string $driver_desc_link
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string intro_msg = 1;</code>
     * @return string
     */
    public function getIntroMsg()
    {
        return $this->intro_msg;
    }

    /**
     * Generated from protobuf field <code>string intro_msg = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setIntroMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->intro_msg = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string intro_icon = 2;</code>
     * @return string
     */
    public function getIntroIcon()
    {
        return $this->intro_icon;
    }

    /**
     * Generated from protobuf field <code>string intro_icon = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setIntroIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->intro_icon = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string profile_link = 3;</code>
     * @return string
     */
    public function getProfileLink()
    {
        return $this->profile_link;
    }

    /**
     * Generated from protobuf field <code>string profile_link = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setProfileLink($var)
    {
        GPBUtil::checkString($var, True);
        $this->profile_link = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_detail_url = 4;</code>
     * @return string
     */
    public function getFeeDetailUrl()
    {
        return $this->fee_detail_url;
    }

    /**
     * Generated from protobuf field <code>string fee_detail_url = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeDetailUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_detail_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.FeeDescItem fee_desc_info = 5;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getFeeDescInfo()
    {
        return $this->fee_desc_info;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.FeeDescItem fee_desc_info = 5;</code>
     * @param \Dirpc\SDK\PreSale\FeeDescItem[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFeeDescInfo($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\FeeDescItem::class);
        $this->fee_desc_info = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_msg = 6;</code>
     * @return string
     */
    public function getFeeMsg()
    {
        return isset($this->fee_msg) ? $this->fee_msg : '';
    }

    public function hasFeeMsg()
    {
        return isset($this->fee_msg);
    }

    public function clearFeeMsg()
    {
        unset($this->fee_msg);
    }

    /**
     * Generated from protobuf field <code>string fee_msg = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string estimate_id = 7;</code>
     * @return string
     */
    public function getEstimateId()
    {
        return $this->estimate_id;
    }

    /**
     * Generated from protobuf field <code>string estimate_id = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateId($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 require_level = 8;</code>
     * @return int
     */
    public function getRequireLevel()
    {
        return $this->require_level;
    }

    /**
     * Generated from protobuf field <code>int32 require_level = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setRequireLevel($var)
    {
        GPBUtil::checkInt32($var);
        $this->require_level = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 business_id = 9;</code>
     * @return int
     */
    public function getBusinessId()
    {
        return $this->business_id;
    }

    /**
     * Generated from protobuf field <code>int32 business_id = 9;</code>
     * @param int $var
     * @return $this
     */
    public function setBusinessId($var)
    {
        GPBUtil::checkInt32($var);
        $this->business_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 combo_type = 10;</code>
     * @return int
     */
    public function getComboType()
    {
        return isset($this->combo_type) ? $this->combo_type : 0;
    }

    public function hasComboType()
    {
        return isset($this->combo_type);
    }

    public function clearComboType()
    {
        unset($this->combo_type);
    }

    /**
     * Generated from protobuf field <code>int32 combo_type = 10;</code>
     * @param int $var
     * @return $this
     */
    public function setComboType($var)
    {
        GPBUtil::checkInt32($var);
        $this->combo_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 product_category = 11;</code>
     * @return int
     */
    public function getProductCategory()
    {
        return $this->product_category;
    }

    /**
     * Generated from protobuf field <code>int32 product_category = 11;</code>
     * @param int $var
     * @return $this
     */
    public function setProductCategory($var)
    {
        GPBUtil::checkInt32($var);
        $this->product_category = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string driver_id = 12;</code>
     * @return string
     */
    public function getDriverId()
    {
        return isset($this->driver_id) ? $this->driver_id : '';
    }

    public function hasDriverId()
    {
        return isset($this->driver_id);
    }

    public function clearDriverId()
    {
        unset($this->driver_id);
    }

    /**
     * Generated from protobuf field <code>string driver_id = 12;</code>
     * @param string $var
     * @return $this
     */
    public function setDriverId($var)
    {
        GPBUtil::checkString($var, True);
        $this->driver_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 is_real_driver = 13;</code>
     * @return int
     */
    public function getIsRealDriver()
    {
        return isset($this->is_real_driver) ? $this->is_real_driver : 0;
    }

    public function hasIsRealDriver()
    {
        return isset($this->is_real_driver);
    }

    public function clearIsRealDriver()
    {
        unset($this->is_real_driver);
    }

    /**
     * Generated from protobuf field <code>int32 is_real_driver = 13;</code>
     * @param int $var
     * @return $this
     */
    public function setIsRealDriver($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_real_driver = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 is_default = 14;</code>
     * @return int
     */
    public function getIsDefault()
    {
        return $this->is_default;
    }

    /**
     * Generated from protobuf field <code>int32 is_default = 14;</code>
     * @param int $var
     * @return $this
     */
    public function setIsDefault($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_default = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 is_online = 15;</code>
     * @return int
     */
    public function getIsOnline()
    {
        return isset($this->is_online) ? $this->is_online : 0;
    }

    public function hasIsOnline()
    {
        return isset($this->is_online);
    }

    public function clearIsOnline()
    {
        unset($this->is_online);
    }

    /**
     * Generated from protobuf field <code>int32 is_online = 15;</code>
     * @param int $var
     * @return $this
     */
    public function setIsOnline($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_online = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 down_broadcast = 16;</code>
     * @return int
     */
    public function getDownBroadcast()
    {
        return isset($this->down_broadcast) ? $this->down_broadcast : 0;
    }

    public function hasDownBroadcast()
    {
        return isset($this->down_broadcast);
    }

    public function clearDownBroadcast()
    {
        unset($this->down_broadcast);
    }

    /**
     * Generated from protobuf field <code>int32 down_broadcast = 16;</code>
     * @param int $var
     * @return $this
     */
    public function setDownBroadcast($var)
    {
        GPBUtil::checkInt32($var);
        $this->down_broadcast = $var;

        return $this;
    }

    /**
     *企业豪华车费用
     *
     * Generated from protobuf field <code>double estimate_fee = 17;</code>
     * @return float
     */
    public function getEstimateFee()
    {
        return isset($this->estimate_fee) ? $this->estimate_fee : 0.0;
    }

    public function hasEstimateFee()
    {
        return isset($this->estimate_fee);
    }

    public function clearEstimateFee()
    {
        unset($this->estimate_fee);
    }

    /**
     *企业豪华车费用
     *
     * Generated from protobuf field <code>double estimate_fee = 17;</code>
     * @param float $var
     * @return $this
     */
    public function setEstimateFee($var)
    {
        GPBUtil::checkDouble($var);
        $this->estimate_fee = $var;

        return $this;
    }

    /**
     *企业豪华车费用
     *
     * Generated from protobuf field <code>string driver_desc = 18;</code>
     * @return string
     */
    public function getDriverDesc()
    {
        return $this->driver_desc;
    }

    /**
     *企业豪华车费用
     *
     * Generated from protobuf field <code>string driver_desc = 18;</code>
     * @param string $var
     * @return $this
     */
    public function setDriverDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->driver_desc = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string driver_desc_link = 19;</code>
     * @return string
     */
    public function getDriverDescLink()
    {
        return $this->driver_desc_link;
    }

    /**
     * Generated from protobuf field <code>string driver_desc_link = 19;</code>
     * @param string $var
     * @return $this
     */
    public function setDriverDescLink($var)
    {
        GPBUtil::checkString($var, True);
        $this->driver_desc_link = $var;

        return $this;
    }

}

