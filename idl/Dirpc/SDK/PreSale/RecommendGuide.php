<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.RecommendGuide</code>
 */
class RecommendGuide extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 guide_scene = 1;</code>
     */
    protected $guide_scene = null;
    /**
     * Generated from protobuf field <code>int32 guide_type = 2;</code>
     */
    protected $guide_type = null;
    /**
     * Generated from protobuf field <code>int32 count_down = 3;</code>
     */
    protected $count_down = null;
    /**
     * Generated from protobuf field <code>string recommend_text = 4;</code>
     */
    protected $recommend_text = null;
    /**
     * Generated from protobuf field <code>string recommend_toast = 5;</code>
     */
    protected $recommend_toast = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $guide_scene
     *     @type int $guide_type
     *     @type int $count_down
     *     @type string $recommend_text
     *     @type string $recommend_toast
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 guide_scene = 1;</code>
     * @return int
     */
    public function getGuideScene()
    {
        return isset($this->guide_scene) ? $this->guide_scene : 0;
    }

    public function hasGuideScene()
    {
        return isset($this->guide_scene);
    }

    public function clearGuideScene()
    {
        unset($this->guide_scene);
    }

    /**
     * Generated from protobuf field <code>int32 guide_scene = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setGuideScene($var)
    {
        GPBUtil::checkInt32($var);
        $this->guide_scene = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 guide_type = 2;</code>
     * @return int
     */
    public function getGuideType()
    {
        return isset($this->guide_type) ? $this->guide_type : 0;
    }

    public function hasGuideType()
    {
        return isset($this->guide_type);
    }

    public function clearGuideType()
    {
        unset($this->guide_type);
    }

    /**
     * Generated from protobuf field <code>int32 guide_type = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setGuideType($var)
    {
        GPBUtil::checkInt32($var);
        $this->guide_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 count_down = 3;</code>
     * @return int
     */
    public function getCountDown()
    {
        return isset($this->count_down) ? $this->count_down : 0;
    }

    public function hasCountDown()
    {
        return isset($this->count_down);
    }

    public function clearCountDown()
    {
        unset($this->count_down);
    }

    /**
     * Generated from protobuf field <code>int32 count_down = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setCountDown($var)
    {
        GPBUtil::checkInt32($var);
        $this->count_down = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string recommend_text = 4;</code>
     * @return string
     */
    public function getRecommendText()
    {
        return isset($this->recommend_text) ? $this->recommend_text : '';
    }

    public function hasRecommendText()
    {
        return isset($this->recommend_text);
    }

    public function clearRecommendText()
    {
        unset($this->recommend_text);
    }

    /**
     * Generated from protobuf field <code>string recommend_text = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setRecommendText($var)
    {
        GPBUtil::checkString($var, True);
        $this->recommend_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string recommend_toast = 5;</code>
     * @return string
     */
    public function getRecommendToast()
    {
        return isset($this->recommend_toast) ? $this->recommend_toast : '';
    }

    public function hasRecommendToast()
    {
        return isset($this->recommend_toast);
    }

    public function clearRecommendToast()
    {
        unset($this->recommend_toast);
    }

    /**
     * Generated from protobuf field <code>string recommend_toast = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setRecommendToast($var)
    {
        GPBUtil::checkString($var, True);
        $this->recommend_toast = $var;

        return $this;
    }

}

