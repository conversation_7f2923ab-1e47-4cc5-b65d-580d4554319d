<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 *粤港车独立错误结构
 *
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.ErrButtonData</code>
 */
class ErrButtonData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *粤港车独立错误结构
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ErrButton button = 1;</code>
     */
    protected $button = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\ErrButton $button
     *          粤港车独立错误结构
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *粤港车独立错误结构
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ErrButton button = 1;</code>
     * @return \Dirpc\SDK\PreSale\ErrButton
     */
    public function getButton()
    {
        return isset($this->button) ? $this->button : null;
    }

    public function hasButton()
    {
        return isset($this->button);
    }

    public function clearButton()
    {
        unset($this->button);
    }

    /**
     *粤港车独立错误结构
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ErrButton button = 1;</code>
     * @param \Dirpc\SDK\PreSale\ErrButton $var
     * @return $this
     */
    public function setButton($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\ErrButton::class);
        $this->button = $var;

        return $this;
    }

}

