<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideRuleButton</code>
 */
class SideRuleButton extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string text = 1;</code>
     */
    protected $text = '';
    /**
     * Generated from protobuf field <code>string font_color = 2;</code>
     */
    protected $font_color = '';
    /**
     *背景色：无值背景透明，有1个纯色，多个渐变
     *
     * Generated from protobuf field <code>repeated string background_gradients = 3;</code>
     */
    private $background_gradients;
    /**
     *背景色：无值背景透明，有1个纯色，多个渐变
     *
     * Generated from protobuf field <code>string border_color = 4;</code>
     */
    protected $border_color = null;
    /**
     *button中link的action_type
     *
     * Generated from protobuf field <code>int32 button_type = 5;</code>
     */
    protected $button_type = null;
    /**
     *button中link的action_type
     *
     * Generated from protobuf field <code>string link_url = 6;</code>
     */
    protected $link_url = null;
    /**
     *请求link的参数
     *
     * Generated from protobuf field <code>map<string, string> link_params = 7;</code>
     */
    private $link_params;
    /**
     *需要端回传的参数
     *
     * Generated from protobuf field <code>map<string, string> callback_extra_infos = 8;</code>
     */
    private $callback_extra_infos;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $text
     *     @type string $font_color
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $background_gradients
     *          背景色：无值背景透明，有1个纯色，多个渐变
     *     @type string $border_color
     *          背景色：无值背景透明，有1个纯色，多个渐变
     *     @type int $button_type
     *          button中link的action_type
     *     @type string $link_url
     *          button中link的action_type
     *     @type array|\Nuwa\Protobuf\Internal\MapField $link_params
     *          请求link的参数
     *     @type array|\Nuwa\Protobuf\Internal\MapField $callback_extra_infos
     *          需要端回传的参数
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string text = 1;</code>
     * @return string
     */
    public function getText()
    {
        return $this->text;
    }

    /**
     * Generated from protobuf field <code>string text = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setText($var)
    {
        GPBUtil::checkString($var, True);
        $this->text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string font_color = 2;</code>
     * @return string
     */
    public function getFontColor()
    {
        return $this->font_color;
    }

    /**
     * Generated from protobuf field <code>string font_color = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setFontColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->font_color = $var;

        return $this;
    }

    /**
     *背景色：无值背景透明，有1个纯色，多个渐变
     *
     * Generated from protobuf field <code>repeated string background_gradients = 3;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getBackgroundGradients()
    {
        return $this->background_gradients;
    }

    /**
     *背景色：无值背景透明，有1个纯色，多个渐变
     *
     * Generated from protobuf field <code>repeated string background_gradients = 3;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBackgroundGradients($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->background_gradients = $arr;

        return $this;
    }

    /**
     *背景色：无值背景透明，有1个纯色，多个渐变
     *
     * Generated from protobuf field <code>string border_color = 4;</code>
     * @return string
     */
    public function getBorderColor()
    {
        return isset($this->border_color) ? $this->border_color : '';
    }

    public function hasBorderColor()
    {
        return isset($this->border_color);
    }

    public function clearBorderColor()
    {
        unset($this->border_color);
    }

    /**
     *背景色：无值背景透明，有1个纯色，多个渐变
     *
     * Generated from protobuf field <code>string border_color = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setBorderColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->border_color = $var;

        return $this;
    }

    /**
     *button中link的action_type
     *
     * Generated from protobuf field <code>int32 button_type = 5;</code>
     * @return int
     */
    public function getButtonType()
    {
        return isset($this->button_type) ? $this->button_type : 0;
    }

    public function hasButtonType()
    {
        return isset($this->button_type);
    }

    public function clearButtonType()
    {
        unset($this->button_type);
    }

    /**
     *button中link的action_type
     *
     * Generated from protobuf field <code>int32 button_type = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setButtonType($var)
    {
        GPBUtil::checkInt32($var);
        $this->button_type = $var;

        return $this;
    }

    /**
     *button中link的action_type
     *
     * Generated from protobuf field <code>string link_url = 6;</code>
     * @return string
     */
    public function getLinkUrl()
    {
        return isset($this->link_url) ? $this->link_url : '';
    }

    public function hasLinkUrl()
    {
        return isset($this->link_url);
    }

    public function clearLinkUrl()
    {
        unset($this->link_url);
    }

    /**
     *button中link的action_type
     *
     * Generated from protobuf field <code>string link_url = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setLinkUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->link_url = $var;

        return $this;
    }

    /**
     *请求link的参数
     *
     * Generated from protobuf field <code>map<string, string> link_params = 7;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getLinkParams()
    {
        return $this->link_params;
    }

    /**
     *请求link的参数
     *
     * Generated from protobuf field <code>map<string, string> link_params = 7;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setLinkParams($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->link_params = $arr;

        return $this;
    }

    /**
     *需要端回传的参数
     *
     * Generated from protobuf field <code>map<string, string> callback_extra_infos = 8;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getCallbackExtraInfos()
    {
        return $this->callback_extra_infos;
    }

    /**
     *需要端回传的参数
     *
     * Generated from protobuf field <code>map<string, string> callback_extra_infos = 8;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setCallbackExtraInfos($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->callback_extra_infos = $arr;

        return $this;
    }

}

