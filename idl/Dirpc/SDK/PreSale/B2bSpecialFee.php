<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.B2bSpecialFee</code>
 */
class B2bSpecialFee extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *跨城费
     *
     * Generated from protobuf field <code>double cross_city_fee = 1;</code>
     */
    protected $cross_city_fee = null;
    /**
     *高速费
     *
     * Generated from protobuf field <code>double highway_fee = 2;</code>
     */
    protected $highway_fee = null;
    /**
     *预约单限制基础费
     *
     * Generated from protobuf field <code>double limit_fee = 3;</code>
     */
    protected $limit_fee = null;
    /**
     *春节服务费
     *
     * Generated from protobuf field <code>double red_packet_fee = 4;</code>
     */
    protected $red_packet_fee = null;
    /**
     *价格类型是否是一口价
     *
     * Generated from protobuf field <code>int32 count_price_type = 5;</code>
     */
    protected $count_price_type = null;
    /**
     *选司务员费
     *
     * Generated from protobuf field <code>double designated_driver_fee = 6;</code>
     */
    protected $designated_driver_fee = null;
    /**
     *起步价
     *
     * Generated from protobuf field <code>double start_price = 7;</code>
     */
    protected $start_price = null;
    /**
     *综合能耗费
     *
     * Generated from protobuf field <code>double energy_consume_fee = 8;</code>
     */
    protected $energy_consume_fee = null;
    /**
     *空驶距离or超里程距离，公里
     *
     * Generated from protobuf field <code>double empty_distance = 9;</code>
     */
    protected $empty_distance = null;
    /**
     *普通时段计费里程,公里
     *
     * Generated from protobuf field <code>double normal_distance = 10;</code>
     */
    protected $normal_distance = null;
    /**
     *普通时段计费时间,分钟
     *
     * Generated from protobuf field <code>double normal_time = 11;</code>
     */
    protected $normal_time = null;
    /**
     *预估总价格，单位：元
     *
     * Generated from protobuf field <code>double total_fee = 12;</code>
     */
    protected $total_fee = null;
    /**
     *最低消费金额
     *
     * Generated from protobuf field <code>double limit_lowest_fee = 13;</code>
     */
    protected $limit_lowest_fee = null;
    /**
     *起步里程
     *
     * Generated from protobuf field <code>double start_distance = 14;</code>
     */
    protected $start_distance = null;
    /**
     *起步时长
     *
     * Generated from protobuf field <code>double start_time = 15;</code>
     */
    protected $start_time = null;
    /**
     *账单获取批量预估详情页的fee_detail_info，请自行解析 http://promise.intra.xiaojukeji.com/promise#/chibi/doc?id=getFeeDetailInfos_5ff45253ae794a560a7ac7c2&parent_id=5ff45253ae794a560a7ac7c2&type=file&key=getFeeDetailInfos
     *
     * Generated from protobuf field <code>string fee_detail_info = 16;</code>
     */
    protected $fee_detail_info = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type float $cross_city_fee
     *          跨城费
     *     @type float $highway_fee
     *          高速费
     *     @type float $limit_fee
     *          预约单限制基础费
     *     @type float $red_packet_fee
     *          春节服务费
     *     @type int $count_price_type
     *          价格类型是否是一口价
     *     @type float $designated_driver_fee
     *          选司务员费
     *     @type float $start_price
     *          起步价
     *     @type float $energy_consume_fee
     *          综合能耗费
     *     @type float $empty_distance
     *          空驶距离or超里程距离，公里
     *     @type float $normal_distance
     *          普通时段计费里程,公里
     *     @type float $normal_time
     *          普通时段计费时间,分钟
     *     @type float $total_fee
     *          预估总价格，单位：元
     *     @type float $limit_lowest_fee
     *          最低消费金额
     *     @type float $start_distance
     *          起步里程
     *     @type float $start_time
     *          起步时长
     *     @type string $fee_detail_info
     *          账单获取批量预估详情页的fee_detail_info，请自行解析 http://promise.intra.xiaojukeji.com/promise#/chibi/doc?id=getFeeDetailInfos_5ff45253ae794a560a7ac7c2&parent_id=5ff45253ae794a560a7ac7c2&type=file&key=getFeeDetailInfos
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *跨城费
     *
     * Generated from protobuf field <code>double cross_city_fee = 1;</code>
     * @return float
     */
    public function getCrossCityFee()
    {
        return isset($this->cross_city_fee) ? $this->cross_city_fee : 0.0;
    }

    public function hasCrossCityFee()
    {
        return isset($this->cross_city_fee);
    }

    public function clearCrossCityFee()
    {
        unset($this->cross_city_fee);
    }

    /**
     *跨城费
     *
     * Generated from protobuf field <code>double cross_city_fee = 1;</code>
     * @param float $var
     * @return $this
     */
    public function setCrossCityFee($var)
    {
        GPBUtil::checkDouble($var);
        $this->cross_city_fee = $var;

        return $this;
    }

    /**
     *高速费
     *
     * Generated from protobuf field <code>double highway_fee = 2;</code>
     * @return float
     */
    public function getHighwayFee()
    {
        return isset($this->highway_fee) ? $this->highway_fee : 0.0;
    }

    public function hasHighwayFee()
    {
        return isset($this->highway_fee);
    }

    public function clearHighwayFee()
    {
        unset($this->highway_fee);
    }

    /**
     *高速费
     *
     * Generated from protobuf field <code>double highway_fee = 2;</code>
     * @param float $var
     * @return $this
     */
    public function setHighwayFee($var)
    {
        GPBUtil::checkDouble($var);
        $this->highway_fee = $var;

        return $this;
    }

    /**
     *预约单限制基础费
     *
     * Generated from protobuf field <code>double limit_fee = 3;</code>
     * @return float
     */
    public function getLimitFee()
    {
        return isset($this->limit_fee) ? $this->limit_fee : 0.0;
    }

    public function hasLimitFee()
    {
        return isset($this->limit_fee);
    }

    public function clearLimitFee()
    {
        unset($this->limit_fee);
    }

    /**
     *预约单限制基础费
     *
     * Generated from protobuf field <code>double limit_fee = 3;</code>
     * @param float $var
     * @return $this
     */
    public function setLimitFee($var)
    {
        GPBUtil::checkDouble($var);
        $this->limit_fee = $var;

        return $this;
    }

    /**
     *春节服务费
     *
     * Generated from protobuf field <code>double red_packet_fee = 4;</code>
     * @return float
     */
    public function getRedPacketFee()
    {
        return isset($this->red_packet_fee) ? $this->red_packet_fee : 0.0;
    }

    public function hasRedPacketFee()
    {
        return isset($this->red_packet_fee);
    }

    public function clearRedPacketFee()
    {
        unset($this->red_packet_fee);
    }

    /**
     *春节服务费
     *
     * Generated from protobuf field <code>double red_packet_fee = 4;</code>
     * @param float $var
     * @return $this
     */
    public function setRedPacketFee($var)
    {
        GPBUtil::checkDouble($var);
        $this->red_packet_fee = $var;

        return $this;
    }

    /**
     *价格类型是否是一口价
     *
     * Generated from protobuf field <code>int32 count_price_type = 5;</code>
     * @return int
     */
    public function getCountPriceType()
    {
        return isset($this->count_price_type) ? $this->count_price_type : 0;
    }

    public function hasCountPriceType()
    {
        return isset($this->count_price_type);
    }

    public function clearCountPriceType()
    {
        unset($this->count_price_type);
    }

    /**
     *价格类型是否是一口价
     *
     * Generated from protobuf field <code>int32 count_price_type = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setCountPriceType($var)
    {
        GPBUtil::checkInt32($var);
        $this->count_price_type = $var;

        return $this;
    }

    /**
     *选司务员费
     *
     * Generated from protobuf field <code>double designated_driver_fee = 6;</code>
     * @return float
     */
    public function getDesignatedDriverFee()
    {
        return isset($this->designated_driver_fee) ? $this->designated_driver_fee : 0.0;
    }

    public function hasDesignatedDriverFee()
    {
        return isset($this->designated_driver_fee);
    }

    public function clearDesignatedDriverFee()
    {
        unset($this->designated_driver_fee);
    }

    /**
     *选司务员费
     *
     * Generated from protobuf field <code>double designated_driver_fee = 6;</code>
     * @param float $var
     * @return $this
     */
    public function setDesignatedDriverFee($var)
    {
        GPBUtil::checkDouble($var);
        $this->designated_driver_fee = $var;

        return $this;
    }

    /**
     *起步价
     *
     * Generated from protobuf field <code>double start_price = 7;</code>
     * @return float
     */
    public function getStartPrice()
    {
        return isset($this->start_price) ? $this->start_price : 0.0;
    }

    public function hasStartPrice()
    {
        return isset($this->start_price);
    }

    public function clearStartPrice()
    {
        unset($this->start_price);
    }

    /**
     *起步价
     *
     * Generated from protobuf field <code>double start_price = 7;</code>
     * @param float $var
     * @return $this
     */
    public function setStartPrice($var)
    {
        GPBUtil::checkDouble($var);
        $this->start_price = $var;

        return $this;
    }

    /**
     *综合能耗费
     *
     * Generated from protobuf field <code>double energy_consume_fee = 8;</code>
     * @return float
     */
    public function getEnergyConsumeFee()
    {
        return isset($this->energy_consume_fee) ? $this->energy_consume_fee : 0.0;
    }

    public function hasEnergyConsumeFee()
    {
        return isset($this->energy_consume_fee);
    }

    public function clearEnergyConsumeFee()
    {
        unset($this->energy_consume_fee);
    }

    /**
     *综合能耗费
     *
     * Generated from protobuf field <code>double energy_consume_fee = 8;</code>
     * @param float $var
     * @return $this
     */
    public function setEnergyConsumeFee($var)
    {
        GPBUtil::checkDouble($var);
        $this->energy_consume_fee = $var;

        return $this;
    }

    /**
     *空驶距离or超里程距离，公里
     *
     * Generated from protobuf field <code>double empty_distance = 9;</code>
     * @return float
     */
    public function getEmptyDistance()
    {
        return isset($this->empty_distance) ? $this->empty_distance : 0.0;
    }

    public function hasEmptyDistance()
    {
        return isset($this->empty_distance);
    }

    public function clearEmptyDistance()
    {
        unset($this->empty_distance);
    }

    /**
     *空驶距离or超里程距离，公里
     *
     * Generated from protobuf field <code>double empty_distance = 9;</code>
     * @param float $var
     * @return $this
     */
    public function setEmptyDistance($var)
    {
        GPBUtil::checkDouble($var);
        $this->empty_distance = $var;

        return $this;
    }

    /**
     *普通时段计费里程,公里
     *
     * Generated from protobuf field <code>double normal_distance = 10;</code>
     * @return float
     */
    public function getNormalDistance()
    {
        return isset($this->normal_distance) ? $this->normal_distance : 0.0;
    }

    public function hasNormalDistance()
    {
        return isset($this->normal_distance);
    }

    public function clearNormalDistance()
    {
        unset($this->normal_distance);
    }

    /**
     *普通时段计费里程,公里
     *
     * Generated from protobuf field <code>double normal_distance = 10;</code>
     * @param float $var
     * @return $this
     */
    public function setNormalDistance($var)
    {
        GPBUtil::checkDouble($var);
        $this->normal_distance = $var;

        return $this;
    }

    /**
     *普通时段计费时间,分钟
     *
     * Generated from protobuf field <code>double normal_time = 11;</code>
     * @return float
     */
    public function getNormalTime()
    {
        return isset($this->normal_time) ? $this->normal_time : 0.0;
    }

    public function hasNormalTime()
    {
        return isset($this->normal_time);
    }

    public function clearNormalTime()
    {
        unset($this->normal_time);
    }

    /**
     *普通时段计费时间,分钟
     *
     * Generated from protobuf field <code>double normal_time = 11;</code>
     * @param float $var
     * @return $this
     */
    public function setNormalTime($var)
    {
        GPBUtil::checkDouble($var);
        $this->normal_time = $var;

        return $this;
    }

    /**
     *预估总价格，单位：元
     *
     * Generated from protobuf field <code>double total_fee = 12;</code>
     * @return float
     */
    public function getTotalFee()
    {
        return isset($this->total_fee) ? $this->total_fee : 0.0;
    }

    public function hasTotalFee()
    {
        return isset($this->total_fee);
    }

    public function clearTotalFee()
    {
        unset($this->total_fee);
    }

    /**
     *预估总价格，单位：元
     *
     * Generated from protobuf field <code>double total_fee = 12;</code>
     * @param float $var
     * @return $this
     */
    public function setTotalFee($var)
    {
        GPBUtil::checkDouble($var);
        $this->total_fee = $var;

        return $this;
    }

    /**
     *最低消费金额
     *
     * Generated from protobuf field <code>double limit_lowest_fee = 13;</code>
     * @return float
     */
    public function getLimitLowestFee()
    {
        return isset($this->limit_lowest_fee) ? $this->limit_lowest_fee : 0.0;
    }

    public function hasLimitLowestFee()
    {
        return isset($this->limit_lowest_fee);
    }

    public function clearLimitLowestFee()
    {
        unset($this->limit_lowest_fee);
    }

    /**
     *最低消费金额
     *
     * Generated from protobuf field <code>double limit_lowest_fee = 13;</code>
     * @param float $var
     * @return $this
     */
    public function setLimitLowestFee($var)
    {
        GPBUtil::checkDouble($var);
        $this->limit_lowest_fee = $var;

        return $this;
    }

    /**
     *起步里程
     *
     * Generated from protobuf field <code>double start_distance = 14;</code>
     * @return float
     */
    public function getStartDistance()
    {
        return isset($this->start_distance) ? $this->start_distance : 0.0;
    }

    public function hasStartDistance()
    {
        return isset($this->start_distance);
    }

    public function clearStartDistance()
    {
        unset($this->start_distance);
    }

    /**
     *起步里程
     *
     * Generated from protobuf field <code>double start_distance = 14;</code>
     * @param float $var
     * @return $this
     */
    public function setStartDistance($var)
    {
        GPBUtil::checkDouble($var);
        $this->start_distance = $var;

        return $this;
    }

    /**
     *起步时长
     *
     * Generated from protobuf field <code>double start_time = 15;</code>
     * @return float
     */
    public function getStartTime()
    {
        return isset($this->start_time) ? $this->start_time : 0.0;
    }

    public function hasStartTime()
    {
        return isset($this->start_time);
    }

    public function clearStartTime()
    {
        unset($this->start_time);
    }

    /**
     *起步时长
     *
     * Generated from protobuf field <code>double start_time = 15;</code>
     * @param float $var
     * @return $this
     */
    public function setStartTime($var)
    {
        GPBUtil::checkDouble($var);
        $this->start_time = $var;

        return $this;
    }

    /**
     *账单获取批量预估详情页的fee_detail_info，请自行解析 http://promise.intra.xiaojukeji.com/promise#/chibi/doc?id=getFeeDetailInfos_5ff45253ae794a560a7ac7c2&parent_id=5ff45253ae794a560a7ac7c2&type=file&key=getFeeDetailInfos
     *
     * Generated from protobuf field <code>string fee_detail_info = 16;</code>
     * @return string
     */
    public function getFeeDetailInfo()
    {
        return isset($this->fee_detail_info) ? $this->fee_detail_info : '';
    }

    public function hasFeeDetailInfo()
    {
        return isset($this->fee_detail_info);
    }

    public function clearFeeDetailInfo()
    {
        unset($this->fee_detail_info);
    }

    /**
     *账单获取批量预估详情页的fee_detail_info，请自行解析 http://promise.intra.xiaojukeji.com/promise#/chibi/doc?id=getFeeDetailInfos_5ff45253ae794a560a7ac7c2&parent_id=5ff45253ae794a560a7ac7c2&type=file&key=getFeeDetailInfos
     *
     * Generated from protobuf field <code>string fee_detail_info = 16;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeDetailInfo($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_detail_info = $var;

        return $this;
    }

}

