<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.UserPayInfoItem</code>
 */
class UserPayInfoItem extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *支付方式id
     *
     * Generated from protobuf field <code>string tag = 1;</code>
     */
    protected $tag = '';
    /**
     *支付方式描述
     *
     * Generated from protobuf field <code>string msg = 2;</code>
     */
    protected $msg = '';
    /**
     *企业成本中心设置
     *
     * Generated from protobuf field <code>string business_const_set = 3;</code>
     */
    protected $business_const_set = '';
    /**
     *是否可用
     *
     * Generated from protobuf field <code>int32 disabled = 4;</code>
     */
    protected $disabled = null;
    /**
     *是否选中
     *
     * Generated from protobuf field <code>int32 isSelected = 5;</code>
     */
    protected $isSelected = 0;
    /**
     *车型支持描述信息
     *
     * Generated from protobuf field <code>string car_support_desc = 6;</code>
     */
    protected $car_support_desc = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $tag
     *          支付方式id
     *     @type string $msg
     *          支付方式描述
     *     @type string $business_const_set
     *          企业成本中心设置
     *     @type int $disabled
     *          是否可用
     *     @type int $isSelected
     *          是否选中
     *     @type string $car_support_desc
     *          车型支持描述信息
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *支付方式id
     *
     * Generated from protobuf field <code>string tag = 1;</code>
     * @return string
     */
    public function getTag()
    {
        return $this->tag;
    }

    /**
     *支付方式id
     *
     * Generated from protobuf field <code>string tag = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTag($var)
    {
        GPBUtil::checkString($var, True);
        $this->tag = $var;

        return $this;
    }

    /**
     *支付方式描述
     *
     * Generated from protobuf field <code>string msg = 2;</code>
     * @return string
     */
    public function getMsg()
    {
        return $this->msg;
    }

    /**
     *支付方式描述
     *
     * Generated from protobuf field <code>string msg = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->msg = $var;

        return $this;
    }

    /**
     *企业成本中心设置
     *
     * Generated from protobuf field <code>string business_const_set = 3;</code>
     * @return string
     */
    public function getBusinessConstSet()
    {
        return $this->business_const_set;
    }

    /**
     *企业成本中心设置
     *
     * Generated from protobuf field <code>string business_const_set = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setBusinessConstSet($var)
    {
        GPBUtil::checkString($var, True);
        $this->business_const_set = $var;

        return $this;
    }

    /**
     *是否可用
     *
     * Generated from protobuf field <code>int32 disabled = 4;</code>
     * @return int
     */
    public function getDisabled()
    {
        return isset($this->disabled) ? $this->disabled : 0;
    }

    public function hasDisabled()
    {
        return isset($this->disabled);
    }

    public function clearDisabled()
    {
        unset($this->disabled);
    }

    /**
     *是否可用
     *
     * Generated from protobuf field <code>int32 disabled = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setDisabled($var)
    {
        GPBUtil::checkInt32($var);
        $this->disabled = $var;

        return $this;
    }

    /**
     *是否选中
     *
     * Generated from protobuf field <code>int32 isSelected = 5;</code>
     * @return int
     */
    public function getIsSelected()
    {
        return $this->isSelected;
    }

    /**
     *是否选中
     *
     * Generated from protobuf field <code>int32 isSelected = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setIsSelected($var)
    {
        GPBUtil::checkInt32($var);
        $this->isSelected = $var;

        return $this;
    }

    /**
     *车型支持描述信息
     *
     * Generated from protobuf field <code>string car_support_desc = 6;</code>
     * @return string
     */
    public function getCarSupportDesc()
    {
        return isset($this->car_support_desc) ? $this->car_support_desc : '';
    }

    public function hasCarSupportDesc()
    {
        return isset($this->car_support_desc);
    }

    public function clearCarSupportDesc()
    {
        unset($this->car_support_desc);
    }

    /**
     *车型支持描述信息
     *
     * Generated from protobuf field <code>string car_support_desc = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setCarSupportDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->car_support_desc = $var;

        return $this;
    }

}

