<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.CarpoolBookingTimeSpan</code>
 */
class CarpoolBookingTimeSpan extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string title = 1;</code>
     */
    protected $title = null;
    /**
     * Generated from protobuf field <code>string date = 2;</code>
     */
    protected $date = null;
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CarpoolBookingTime time = 3;</code>
     */
    private $time;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $title
     *     @type string $date
     *     @type \Dirpc\SDK\PreSale\CarpoolBookingTime[]|\Nuwa\Protobuf\Internal\RepeatedField $time
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @return string
     */
    public function getTitle()
    {
        return isset($this->title) ? $this->title : '';
    }

    public function hasTitle()
    {
        return isset($this->title);
    }

    public function clearTitle()
    {
        unset($this->title);
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string date = 2;</code>
     * @return string
     */
    public function getDate()
    {
        return isset($this->date) ? $this->date : '';
    }

    public function hasDate()
    {
        return isset($this->date);
    }

    public function clearDate()
    {
        unset($this->date);
    }

    /**
     * Generated from protobuf field <code>string date = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setDate($var)
    {
        GPBUtil::checkString($var, True);
        $this->date = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CarpoolBookingTime time = 3;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getTime()
    {
        return $this->time;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.CarpoolBookingTime time = 3;</code>
     * @param \Dirpc\SDK\PreSale\CarpoolBookingTime[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setTime($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\CarpoolBookingTime::class);
        $this->time = $arr;

        return $this;
    }

}

