<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.MultiRuleConf</code>
 */
class MultiRuleConf extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *左侧icon
     *
     * Generated from protobuf field <code>string left_icon = 1;</code>
     */
    protected $left_icon = null;
    /**
     *文案
     *
     * Generated from protobuf field <code>string text = 2;</code>
     */
    protected $text = null;
    /**
     *跳转链接
     *
     * Generated from protobuf field <code>string link_url = 3;</code>
     */
    protected $link_url = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $left_icon
     *          左侧icon
     *     @type string $text
     *          文案
     *     @type string $link_url
     *          跳转链接
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     *左侧icon
     *
     * Generated from protobuf field <code>string left_icon = 1;</code>
     * @return string
     */
    public function getLeftIcon()
    {
        return isset($this->left_icon) ? $this->left_icon : '';
    }

    public function hasLeftIcon()
    {
        return isset($this->left_icon);
    }

    public function clearLeftIcon()
    {
        unset($this->left_icon);
    }

    /**
     *左侧icon
     *
     * Generated from protobuf field <code>string left_icon = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setLeftIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->left_icon = $var;

        return $this;
    }

    /**
     *文案
     *
     * Generated from protobuf field <code>string text = 2;</code>
     * @return string
     */
    public function getText()
    {
        return isset($this->text) ? $this->text : '';
    }

    public function hasText()
    {
        return isset($this->text);
    }

    public function clearText()
    {
        unset($this->text);
    }

    /**
     *文案
     *
     * Generated from protobuf field <code>string text = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setText($var)
    {
        GPBUtil::checkString($var, True);
        $this->text = $var;

        return $this;
    }

    /**
     *跳转链接
     *
     * Generated from protobuf field <code>string link_url = 3;</code>
     * @return string
     */
    public function getLinkUrl()
    {
        return isset($this->link_url) ? $this->link_url : '';
    }

    public function hasLinkUrl()
    {
        return isset($this->link_url);
    }

    public function clearLinkUrl()
    {
        unset($this->link_url);
    }

    /**
     *跳转链接
     *
     * Generated from protobuf field <code>string link_url = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setLinkUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->link_url = $var;

        return $this;
    }

}

