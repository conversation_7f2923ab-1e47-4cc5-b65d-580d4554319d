<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.IntercityEstimateResult</code>
 */
class IntercityEstimateResult extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.IntercityEstimateData estimate_data = 1;</code>
     */
    private $estimate_data;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.UserPayInfo user_pay_info = 2;</code>
     */
    protected $user_pay_info = null;
    /**
     * Generated from protobuf field <code>string estimate_trace_id = 3;</code>
     */
    protected $estimate_trace_id = '';
    /**
     * Generated from protobuf field <code>string fee_detail_url = 4;</code>
     */
    protected $fee_detail_url = '';
    /**
     * Generated from protobuf field <code>string background_url = 5;</code>
     */
    protected $background_url = '';
    /**
     * Generated from protobuf field <code>string detail_url = 6;</code>
     */
    protected $detail_url = '';
    /**
     * Generated from protobuf field <code>string error_url = 7;</code>
     */
    protected $error_url = '';
    /**
     * Generated from protobuf field <code>string force_notice_toast = 8;</code>
     */
    protected $force_notice_toast = '';
    /**
     *拼车座位数组件, 两个品类公用的
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.IntercityCarpoolSeatModule carpool_seat_module = 9;</code>
     */
    protected $carpool_seat_module = null;
    /**
     *春节服务费等发单拦截页
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PluginPageInfo plugin_page_info = 10;</code>
     */
    protected $plugin_page_info = null;
    /**
     *春节服务费等发单拦截页
     *
     * Generated from protobuf field <code>string barrage_text = 11;</code>
     */
    protected $barrage_text = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\IntercityEstimateData[]|\Nuwa\Protobuf\Internal\RepeatedField $estimate_data
     *     @type \Dirpc\SDK\PreSale\UserPayInfo $user_pay_info
     *     @type string $estimate_trace_id
     *     @type string $fee_detail_url
     *     @type string $background_url
     *     @type string $detail_url
     *     @type string $error_url
     *     @type string $force_notice_toast
     *     @type \Dirpc\SDK\PreSale\IntercityCarpoolSeatModule $carpool_seat_module
     *          拼车座位数组件, 两个品类公用的
     *     @type \Dirpc\SDK\PreSale\PluginPageInfo $plugin_page_info
     *          春节服务费等发单拦截页
     *     @type string $barrage_text
     *          春节服务费等发单拦截页
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.IntercityEstimateData estimate_data = 1;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getEstimateData()
    {
        return $this->estimate_data;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.IntercityEstimateData estimate_data = 1;</code>
     * @param \Dirpc\SDK\PreSale\IntercityEstimateData[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setEstimateData($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\IntercityEstimateData::class);
        $this->estimate_data = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.UserPayInfo user_pay_info = 2;</code>
     * @return \Dirpc\SDK\PreSale\UserPayInfo
     */
    public function getUserPayInfo()
    {
        return isset($this->user_pay_info) ? $this->user_pay_info : null;
    }

    public function hasUserPayInfo()
    {
        return isset($this->user_pay_info);
    }

    public function clearUserPayInfo()
    {
        unset($this->user_pay_info);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.UserPayInfo user_pay_info = 2;</code>
     * @param \Dirpc\SDK\PreSale\UserPayInfo $var
     * @return $this
     */
    public function setUserPayInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\UserPayInfo::class);
        $this->user_pay_info = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string estimate_trace_id = 3;</code>
     * @return string
     */
    public function getEstimateTraceId()
    {
        return $this->estimate_trace_id;
    }

    /**
     * Generated from protobuf field <code>string estimate_trace_id = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setEstimateTraceId($var)
    {
        GPBUtil::checkString($var, True);
        $this->estimate_trace_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_detail_url = 4;</code>
     * @return string
     */
    public function getFeeDetailUrl()
    {
        return $this->fee_detail_url;
    }

    /**
     * Generated from protobuf field <code>string fee_detail_url = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeDetailUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_detail_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string background_url = 5;</code>
     * @return string
     */
    public function getBackgroundUrl()
    {
        return $this->background_url;
    }

    /**
     * Generated from protobuf field <code>string background_url = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setBackgroundUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->background_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string detail_url = 6;</code>
     * @return string
     */
    public function getDetailUrl()
    {
        return $this->detail_url;
    }

    /**
     * Generated from protobuf field <code>string detail_url = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setDetailUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->detail_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string error_url = 7;</code>
     * @return string
     */
    public function getErrorUrl()
    {
        return $this->error_url;
    }

    /**
     * Generated from protobuf field <code>string error_url = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setErrorUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->error_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string force_notice_toast = 8;</code>
     * @return string
     */
    public function getForceNoticeToast()
    {
        return $this->force_notice_toast;
    }

    /**
     * Generated from protobuf field <code>string force_notice_toast = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setForceNoticeToast($var)
    {
        GPBUtil::checkString($var, True);
        $this->force_notice_toast = $var;

        return $this;
    }

    /**
     *拼车座位数组件, 两个品类公用的
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.IntercityCarpoolSeatModule carpool_seat_module = 9;</code>
     * @return \Dirpc\SDK\PreSale\IntercityCarpoolSeatModule
     */
    public function getCarpoolSeatModule()
    {
        return isset($this->carpool_seat_module) ? $this->carpool_seat_module : null;
    }

    public function hasCarpoolSeatModule()
    {
        return isset($this->carpool_seat_module);
    }

    public function clearCarpoolSeatModule()
    {
        unset($this->carpool_seat_module);
    }

    /**
     *拼车座位数组件, 两个品类公用的
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.IntercityCarpoolSeatModule carpool_seat_module = 9;</code>
     * @param \Dirpc\SDK\PreSale\IntercityCarpoolSeatModule $var
     * @return $this
     */
    public function setCarpoolSeatModule($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\IntercityCarpoolSeatModule::class);
        $this->carpool_seat_module = $var;

        return $this;
    }

    /**
     *春节服务费等发单拦截页
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PluginPageInfo plugin_page_info = 10;</code>
     * @return \Dirpc\SDK\PreSale\PluginPageInfo
     */
    public function getPluginPageInfo()
    {
        return isset($this->plugin_page_info) ? $this->plugin_page_info : null;
    }

    public function hasPluginPageInfo()
    {
        return isset($this->plugin_page_info);
    }

    public function clearPluginPageInfo()
    {
        unset($this->plugin_page_info);
    }

    /**
     *春节服务费等发单拦截页
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.PluginPageInfo plugin_page_info = 10;</code>
     * @param \Dirpc\SDK\PreSale\PluginPageInfo $var
     * @return $this
     */
    public function setPluginPageInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\PluginPageInfo::class);
        $this->plugin_page_info = $var;

        return $this;
    }

    /**
     *春节服务费等发单拦截页
     *
     * Generated from protobuf field <code>string barrage_text = 11;</code>
     * @return string
     */
    public function getBarrageText()
    {
        return isset($this->barrage_text) ? $this->barrage_text : '';
    }

    public function hasBarrageText()
    {
        return isset($this->barrage_text);
    }

    public function clearBarrageText()
    {
        unset($this->barrage_text);
    }

    /**
     *春节服务费等发单拦截页
     *
     * Generated from protobuf field <code>string barrage_text = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setBarrageText($var)
    {
        GPBUtil::checkString($var, True);
        $this->barrage_text = $var;

        return $this;
    }

}

