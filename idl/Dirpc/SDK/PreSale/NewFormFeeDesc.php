<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NewFormFeeDesc</code>
 */
class NewFormFeeDesc extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *边框RGB色值, 如#000000, 空串标识无边框
     *
     * Generated from protobuf field <code>string border_color = 1;</code>
     */
    protected $border_color = '';
    /**
     *边框RGB色值, 如#000000, 空串标识无边框
     *
     * Generated from protobuf field <code>string content = 2;</code>
     */
    protected $content = '';
    /**
     * Generated from protobuf field <code>string icon = 3;</code>
     */
    protected $icon = '';
    /**
     * Generated from protobuf field <code>double amount = 4;</code>
     */
    protected $amount = 0.0;
    /**
     * Generated from protobuf field <code>int32 type = 5;</code>
     */
    protected $type = 0;
    /**
     * Generated from protobuf field <code>string text_color = 6;</code>
     */
    protected $text_color = '';
    /**
     * Generated from protobuf field <code>string highlight_color = 7;</code>
     */
    protected $highlight_color = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $border_color
     *          边框RGB色值, 如#000000, 空串标识无边框
     *     @type string $content
     *          边框RGB色值, 如#000000, 空串标识无边框
     *     @type string $icon
     *     @type float $amount
     *     @type int $type
     *     @type string $text_color
     *     @type string $highlight_color
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *边框RGB色值, 如#000000, 空串标识无边框
     *
     * Generated from protobuf field <code>string border_color = 1;</code>
     * @return string
     */
    public function getBorderColor()
    {
        return $this->border_color;
    }

    /**
     *边框RGB色值, 如#000000, 空串标识无边框
     *
     * Generated from protobuf field <code>string border_color = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setBorderColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->border_color = $var;

        return $this;
    }

    /**
     *边框RGB色值, 如#000000, 空串标识无边框
     *
     * Generated from protobuf field <code>string content = 2;</code>
     * @return string
     */
    public function getContent()
    {
        return $this->content;
    }

    /**
     *边框RGB色值, 如#000000, 空串标识无边框
     *
     * Generated from protobuf field <code>string content = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setContent($var)
    {
        GPBUtil::checkString($var, True);
        $this->content = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string icon = 3;</code>
     * @return string
     */
    public function getIcon()
    {
        return $this->icon;
    }

    /**
     * Generated from protobuf field <code>string icon = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->icon = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double amount = 4;</code>
     * @return float
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * Generated from protobuf field <code>double amount = 4;</code>
     * @param float $var
     * @return $this
     */
    public function setAmount($var)
    {
        GPBUtil::checkDouble($var);
        $this->amount = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 type = 5;</code>
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Generated from protobuf field <code>int32 type = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkInt32($var);
        $this->type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string text_color = 6;</code>
     * @return string
     */
    public function getTextColor()
    {
        return $this->text_color;
    }

    /**
     * Generated from protobuf field <code>string text_color = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setTextColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->text_color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string highlight_color = 7;</code>
     * @return string
     */
    public function getHighlightColor()
    {
        return $this->highlight_color;
    }

    /**
     * Generated from protobuf field <code>string highlight_color = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setHighlightColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->highlight_color = $var;

        return $this;
    }

}

