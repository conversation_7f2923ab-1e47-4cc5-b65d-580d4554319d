<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: carpool.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SeatOption</code>
 */
class SeatOption extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string label = 1;</code>
     */
    protected $label = null;
    /**
     * Generated from protobuf field <code>int32 value = 2;</code>
     */
    protected $value = null;
    /**
     * Generated from protobuf field <code>bool selected = 3;</code>
     */
    protected $selected = null;
    /**
     * Generated from protobuf field <code>bool disable = 4;</code>
     */
    protected $disable = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $label
     *     @type int $value
     *     @type bool $selected
     *     @type bool $disable
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\Carpool::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string label = 1;</code>
     * @return string
     */
    public function getLabel()
    {
        return isset($this->label) ? $this->label : '';
    }

    public function hasLabel()
    {
        return isset($this->label);
    }

    public function clearLabel()
    {
        unset($this->label);
    }

    /**
     * Generated from protobuf field <code>string label = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setLabel($var)
    {
        GPBUtil::checkString($var, True);
        $this->label = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 value = 2;</code>
     * @return int
     */
    public function getValue()
    {
        return isset($this->value) ? $this->value : 0;
    }

    public function hasValue()
    {
        return isset($this->value);
    }

    public function clearValue()
    {
        unset($this->value);
    }

    /**
     * Generated from protobuf field <code>int32 value = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setValue($var)
    {
        GPBUtil::checkInt32($var);
        $this->value = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool selected = 3;</code>
     * @return bool
     */
    public function getSelected()
    {
        return isset($this->selected) ? $this->selected : false;
    }

    public function hasSelected()
    {
        return isset($this->selected);
    }

    public function clearSelected()
    {
        unset($this->selected);
    }

    /**
     * Generated from protobuf field <code>bool selected = 3;</code>
     * @param bool $var
     * @return $this
     */
    public function setSelected($var)
    {
        GPBUtil::checkBool($var);
        $this->selected = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool disable = 4;</code>
     * @return bool
     */
    public function getDisable()
    {
        return isset($this->disable) ? $this->disable : false;
    }

    public function hasDisable()
    {
        return isset($this->disable);
    }

    public function clearDisable()
    {
        unset($this->disable);
    }

    /**
     * Generated from protobuf field <code>bool disable = 4;</code>
     * @param bool $var
     * @return $this
     */
    public function setDisable($var)
    {
        GPBUtil::checkBool($var);
        $this->disable = $var;

        return $this;
    }

}

