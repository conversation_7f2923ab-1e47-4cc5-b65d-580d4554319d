<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.PageGuideRequest</code>
 */
class PageGuideRequest extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *用户信息
     *
     * Generated from protobuf field <code>string token = 1;</code>
     */
    protected $token = '';
    /**
     *端信息
     *
     * Generated from protobuf field <code>string app_version = 2;</code>
     */
    protected $app_version = '';
    /**
     * Generated from protobuf field <code>int32 access_key_id = 3;</code>
     */
    protected $access_key_id = 0;
    /**
     * Generated from protobuf field <code>string menu_id = 4;</code>
     */
    protected $menu_id = '';
    /**
     * Generated from protobuf field <code>int32 page_type = 5;</code>
     */
    protected $page_type = 0;
    /**
     * Generated from protobuf field <code>string imei = 6;</code>
     */
    protected $imei = '';
    /**
     *地理信息
     *
     * Generated from protobuf field <code>double cur_lat = 7;</code>
     */
    protected $cur_lat = 0.0;
    /**
     * Generated from protobuf field <code>double cur_lng = 8;</code>
     */
    protected $cur_lng = 0.0;
    /**
     * Generated from protobuf field <code>double from_lat = 9;</code>
     */
    protected $from_lat = 0.0;
    /**
     * Generated from protobuf field <code>double from_lng = 10;</code>
     */
    protected $from_lng = 0.0;
    /**
     * Generated from protobuf field <code>double to_lat = 11;</code>
     */
    protected $to_lat = 0.0;
    /**
     * Generated from protobuf field <code>double to_lng = 12;</code>
     */
    protected $to_lng = 0.0;
    /**
     * Generated from protobuf field <code>string maptype = 13;</code>
     */
    protected $maptype = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $token
     *          用户信息
     *     @type string $app_version
     *          端信息
     *     @type int $access_key_id
     *     @type string $menu_id
     *     @type int $page_type
     *     @type string $imei
     *     @type float $cur_lat
     *          地理信息
     *     @type float $cur_lng
     *     @type float $from_lat
     *     @type float $from_lng
     *     @type float $to_lat
     *     @type float $to_lng
     *     @type string $maptype
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *用户信息
     *
     * Generated from protobuf field <code>string token = 1;</code>
     * @return string
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     *用户信息
     *
     * Generated from protobuf field <code>string token = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->token = $var;

        return $this;
    }

    /**
     *端信息
     *
     * Generated from protobuf field <code>string app_version = 2;</code>
     * @return string
     */
    public function getAppVersion()
    {
        return $this->app_version;
    }

    /**
     *端信息
     *
     * Generated from protobuf field <code>string app_version = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setAppVersion($var)
    {
        GPBUtil::checkString($var, True);
        $this->app_version = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 access_key_id = 3;</code>
     * @return int
     */
    public function getAccessKeyId()
    {
        return $this->access_key_id;
    }

    /**
     * Generated from protobuf field <code>int32 access_key_id = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setAccessKeyId($var)
    {
        GPBUtil::checkInt32($var);
        $this->access_key_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string menu_id = 4;</code>
     * @return string
     */
    public function getMenuId()
    {
        return $this->menu_id;
    }

    /**
     * Generated from protobuf field <code>string menu_id = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setMenuId($var)
    {
        GPBUtil::checkString($var, True);
        $this->menu_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 page_type = 5;</code>
     * @return int
     */
    public function getPageType()
    {
        return $this->page_type;
    }

    /**
     * Generated from protobuf field <code>int32 page_type = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setPageType($var)
    {
        GPBUtil::checkInt32($var);
        $this->page_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string imei = 6;</code>
     * @return string
     */
    public function getImei()
    {
        return $this->imei;
    }

    /**
     * Generated from protobuf field <code>string imei = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setImei($var)
    {
        GPBUtil::checkString($var, True);
        $this->imei = $var;

        return $this;
    }

    /**
     *地理信息
     *
     * Generated from protobuf field <code>double cur_lat = 7;</code>
     * @return float
     */
    public function getCurLat()
    {
        return $this->cur_lat;
    }

    /**
     *地理信息
     *
     * Generated from protobuf field <code>double cur_lat = 7;</code>
     * @param float $var
     * @return $this
     */
    public function setCurLat($var)
    {
        GPBUtil::checkDouble($var);
        $this->cur_lat = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double cur_lng = 8;</code>
     * @return float
     */
    public function getCurLng()
    {
        return $this->cur_lng;
    }

    /**
     * Generated from protobuf field <code>double cur_lng = 8;</code>
     * @param float $var
     * @return $this
     */
    public function setCurLng($var)
    {
        GPBUtil::checkDouble($var);
        $this->cur_lng = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double from_lat = 9;</code>
     * @return float
     */
    public function getFromLat()
    {
        return $this->from_lat;
    }

    /**
     * Generated from protobuf field <code>double from_lat = 9;</code>
     * @param float $var
     * @return $this
     */
    public function setFromLat($var)
    {
        GPBUtil::checkDouble($var);
        $this->from_lat = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double from_lng = 10;</code>
     * @return float
     */
    public function getFromLng()
    {
        return $this->from_lng;
    }

    /**
     * Generated from protobuf field <code>double from_lng = 10;</code>
     * @param float $var
     * @return $this
     */
    public function setFromLng($var)
    {
        GPBUtil::checkDouble($var);
        $this->from_lng = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double to_lat = 11;</code>
     * @return float
     */
    public function getToLat()
    {
        return $this->to_lat;
    }

    /**
     * Generated from protobuf field <code>double to_lat = 11;</code>
     * @param float $var
     * @return $this
     */
    public function setToLat($var)
    {
        GPBUtil::checkDouble($var);
        $this->to_lat = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double to_lng = 12;</code>
     * @return float
     */
    public function getToLng()
    {
        return $this->to_lng;
    }

    /**
     * Generated from protobuf field <code>double to_lng = 12;</code>
     * @param float $var
     * @return $this
     */
    public function setToLng($var)
    {
        GPBUtil::checkDouble($var);
        $this->to_lng = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string maptype = 13;</code>
     * @return string
     */
    public function getMaptype()
    {
        return $this->maptype;
    }

    /**
     * Generated from protobuf field <code>string maptype = 13;</code>
     * @param string $var
     * @return $this
     */
    public function setMaptype($var)
    {
        GPBUtil::checkString($var, True);
        $this->maptype = $var;

        return $this;
    }

}

