<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.B2bDynamicMemberInfo</code>
 */
class B2bDynamicMemberInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *会员等级
     *
     * Generated from protobuf field <code>int32 level_id = 1;</code>
     */
    protected $level_id = 0;
    /**
     *会员等级
     *
     * Generated from protobuf field <code>string level_name = 2;</code>
     */
    protected $level_name = '';
    /**
     * Generated from protobuf field <code>int32 is_auto = 3;</code>
     */
    protected $is_auto = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $level_id
     *          会员等级
     *     @type string $level_name
     *          会员等级
     *     @type int $is_auto
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *会员等级
     *
     * Generated from protobuf field <code>int32 level_id = 1;</code>
     * @return int
     */
    public function getLevelId()
    {
        return $this->level_id;
    }

    /**
     *会员等级
     *
     * Generated from protobuf field <code>int32 level_id = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setLevelId($var)
    {
        GPBUtil::checkInt32($var);
        $this->level_id = $var;

        return $this;
    }

    /**
     *会员等级
     *
     * Generated from protobuf field <code>string level_name = 2;</code>
     * @return string
     */
    public function getLevelName()
    {
        return $this->level_name;
    }

    /**
     *会员等级
     *
     * Generated from protobuf field <code>string level_name = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setLevelName($var)
    {
        GPBUtil::checkString($var, True);
        $this->level_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 is_auto = 3;</code>
     * @return int
     */
    public function getIsAuto()
    {
        return $this->is_auto;
    }

    /**
     * Generated from protobuf field <code>int32 is_auto = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setIsAuto($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_auto = $var;

        return $this;
    }

}

