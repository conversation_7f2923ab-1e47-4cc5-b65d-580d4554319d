<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.EjectLayer</code>
 */
class EjectLayer extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string omega_event_id = 1;</code>
     */
    protected $omega_event_id = '';
    /**
     * Generated from protobuf field <code>string title = 2;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>string content = 3;</code>
     */
    protected $content = '';
    /**
     * Generated from protobuf field <code>string top_icon = 4;</code>
     */
    protected $top_icon = null;
    /**
     * Generated from protobuf field <code>string link_url = 5;</code>
     */
    protected $link_url = null;
    /**
     * Generated from protobuf field <code>string link_name = 6;</code>
     */
    protected $link_name = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectButton left_button = 8;</code>
     */
    protected $left_button = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectButton right_button = 9;</code>
     */
    protected $right_button = null;
    /**
     * Generated from protobuf field <code>string request_url = 10;</code>
     */
    protected $request_url = '';
    /**
     * Generated from protobuf field <code>string request_method = 11;</code>
     */
    protected $request_method = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $omega_event_id
     *     @type string $title
     *     @type string $content
     *     @type string $top_icon
     *     @type string $link_url
     *     @type string $link_name
     *     @type \Dirpc\SDK\PreSale\EjectButton $left_button
     *     @type \Dirpc\SDK\PreSale\EjectButton $right_button
     *     @type string $request_url
     *     @type string $request_method
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string omega_event_id = 1;</code>
     * @return string
     */
    public function getOmegaEventId()
    {
        return $this->omega_event_id;
    }

    /**
     * Generated from protobuf field <code>string omega_event_id = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setOmegaEventId($var)
    {
        GPBUtil::checkString($var, True);
        $this->omega_event_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string title = 2;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string content = 3;</code>
     * @return string
     */
    public function getContent()
    {
        return $this->content;
    }

    /**
     * Generated from protobuf field <code>string content = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setContent($var)
    {
        GPBUtil::checkString($var, True);
        $this->content = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string top_icon = 4;</code>
     * @return string
     */
    public function getTopIcon()
    {
        return isset($this->top_icon) ? $this->top_icon : '';
    }

    public function hasTopIcon()
    {
        return isset($this->top_icon);
    }

    public function clearTopIcon()
    {
        unset($this->top_icon);
    }

    /**
     * Generated from protobuf field <code>string top_icon = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setTopIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->top_icon = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string link_url = 5;</code>
     * @return string
     */
    public function getLinkUrl()
    {
        return isset($this->link_url) ? $this->link_url : '';
    }

    public function hasLinkUrl()
    {
        return isset($this->link_url);
    }

    public function clearLinkUrl()
    {
        unset($this->link_url);
    }

    /**
     * Generated from protobuf field <code>string link_url = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setLinkUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->link_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string link_name = 6;</code>
     * @return string
     */
    public function getLinkName()
    {
        return isset($this->link_name) ? $this->link_name : '';
    }

    public function hasLinkName()
    {
        return isset($this->link_name);
    }

    public function clearLinkName()
    {
        unset($this->link_name);
    }

    /**
     * Generated from protobuf field <code>string link_name = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setLinkName($var)
    {
        GPBUtil::checkString($var, True);
        $this->link_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectButton left_button = 8;</code>
     * @return \Dirpc\SDK\PreSale\EjectButton
     */
    public function getLeftButton()
    {
        return isset($this->left_button) ? $this->left_button : null;
    }

    public function hasLeftButton()
    {
        return isset($this->left_button);
    }

    public function clearLeftButton()
    {
        unset($this->left_button);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectButton left_button = 8;</code>
     * @param \Dirpc\SDK\PreSale\EjectButton $var
     * @return $this
     */
    public function setLeftButton($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\EjectButton::class);
        $this->left_button = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectButton right_button = 9;</code>
     * @return \Dirpc\SDK\PreSale\EjectButton
     */
    public function getRightButton()
    {
        return isset($this->right_button) ? $this->right_button : null;
    }

    public function hasRightButton()
    {
        return isset($this->right_button);
    }

    public function clearRightButton()
    {
        unset($this->right_button);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.EjectButton right_button = 9;</code>
     * @param \Dirpc\SDK\PreSale\EjectButton $var
     * @return $this
     */
    public function setRightButton($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\EjectButton::class);
        $this->right_button = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string request_url = 10;</code>
     * @return string
     */
    public function getRequestUrl()
    {
        return $this->request_url;
    }

    /**
     * Generated from protobuf field <code>string request_url = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setRequestUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->request_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string request_method = 11;</code>
     * @return string
     */
    public function getRequestMethod()
    {
        return $this->request_method;
    }

    /**
     * Generated from protobuf field <code>string request_method = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setRequestMethod($var)
    {
        GPBUtil::checkString($var, True);
        $this->request_method = $var;

        return $this;
    }

}

