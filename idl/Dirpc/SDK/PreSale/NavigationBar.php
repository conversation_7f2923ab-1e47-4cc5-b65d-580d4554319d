<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NavigationBar</code>
 */
class NavigationBar extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *操作台组件唯一标识
     *
     * Generated from protobuf field <code>string key = 1;</code>
     */
    protected $key = '';
    /**
     *展示文案 (仅在某些组件有)
     *
     * Generated from protobuf field <code>string title = 2;</code>
     */
    protected $title = null;
    /**
     *图标
     *
     * Generated from protobuf field <code>string icon = 3;</code>
     */
    protected $icon = null;
    /**
     *跳转链接
     *
     * Generated from protobuf field <code>string link = 4;</code>
     */
    protected $link = null;
    /**
     *动画 可不传
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.Animation animation = 5;</code>
     */
    protected $animation = null;
    /**
     *高亮颜色
     *
     * Generated from protobuf field <code>string highlight_color = 6;</code>
     */
    protected $highlight_color = null;
    /**
     *是否高亮
     *
     * Generated from protobuf field <code>int32 is_highlight = 7;</code>
     */
    protected $is_highlight = null;
    /**
     *点击携带参数
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NavigationBarParams params = 8;</code>
     */
    protected $params = null;
    /**
     *点击弹窗
     *
     * Generated from protobuf field <code>map<string, string> popup = 9;</code>
     */
    private $popup;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $key
     *          操作台组件唯一标识
     *     @type string $title
     *          展示文案 (仅在某些组件有)
     *     @type string $icon
     *          图标
     *     @type string $link
     *          跳转链接
     *     @type \Dirpc\SDK\PreSale\Animation $animation
     *          动画 可不传
     *     @type string $highlight_color
     *          高亮颜色
     *     @type int $is_highlight
     *          是否高亮
     *     @type \Dirpc\SDK\PreSale\NavigationBarParams $params
     *          点击携带参数
     *     @type array|\Nuwa\Protobuf\Internal\MapField $popup
     *          点击弹窗
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *操作台组件唯一标识
     *
     * Generated from protobuf field <code>string key = 1;</code>
     * @return string
     */
    public function getKey()
    {
        return $this->key;
    }

    /**
     *操作台组件唯一标识
     *
     * Generated from protobuf field <code>string key = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setKey($var)
    {
        GPBUtil::checkString($var, True);
        $this->key = $var;

        return $this;
    }

    /**
     *展示文案 (仅在某些组件有)
     *
     * Generated from protobuf field <code>string title = 2;</code>
     * @return string
     */
    public function getTitle()
    {
        return isset($this->title) ? $this->title : '';
    }

    public function hasTitle()
    {
        return isset($this->title);
    }

    public function clearTitle()
    {
        unset($this->title);
    }

    /**
     *展示文案 (仅在某些组件有)
     *
     * Generated from protobuf field <code>string title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     *图标
     *
     * Generated from protobuf field <code>string icon = 3;</code>
     * @return string
     */
    public function getIcon()
    {
        return isset($this->icon) ? $this->icon : '';
    }

    public function hasIcon()
    {
        return isset($this->icon);
    }

    public function clearIcon()
    {
        unset($this->icon);
    }

    /**
     *图标
     *
     * Generated from protobuf field <code>string icon = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->icon = $var;

        return $this;
    }

    /**
     *跳转链接
     *
     * Generated from protobuf field <code>string link = 4;</code>
     * @return string
     */
    public function getLink()
    {
        return isset($this->link) ? $this->link : '';
    }

    public function hasLink()
    {
        return isset($this->link);
    }

    public function clearLink()
    {
        unset($this->link);
    }

    /**
     *跳转链接
     *
     * Generated from protobuf field <code>string link = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setLink($var)
    {
        GPBUtil::checkString($var, True);
        $this->link = $var;

        return $this;
    }

    /**
     *动画 可不传
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.Animation animation = 5;</code>
     * @return \Dirpc\SDK\PreSale\Animation
     */
    public function getAnimation()
    {
        return isset($this->animation) ? $this->animation : null;
    }

    public function hasAnimation()
    {
        return isset($this->animation);
    }

    public function clearAnimation()
    {
        unset($this->animation);
    }

    /**
     *动画 可不传
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.Animation animation = 5;</code>
     * @param \Dirpc\SDK\PreSale\Animation $var
     * @return $this
     */
    public function setAnimation($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\Animation::class);
        $this->animation = $var;

        return $this;
    }

    /**
     *高亮颜色
     *
     * Generated from protobuf field <code>string highlight_color = 6;</code>
     * @return string
     */
    public function getHighlightColor()
    {
        return isset($this->highlight_color) ? $this->highlight_color : '';
    }

    public function hasHighlightColor()
    {
        return isset($this->highlight_color);
    }

    public function clearHighlightColor()
    {
        unset($this->highlight_color);
    }

    /**
     *高亮颜色
     *
     * Generated from protobuf field <code>string highlight_color = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setHighlightColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->highlight_color = $var;

        return $this;
    }

    /**
     *是否高亮
     *
     * Generated from protobuf field <code>int32 is_highlight = 7;</code>
     * @return int
     */
    public function getIsHighlight()
    {
        return isset($this->is_highlight) ? $this->is_highlight : 0;
    }

    public function hasIsHighlight()
    {
        return isset($this->is_highlight);
    }

    public function clearIsHighlight()
    {
        unset($this->is_highlight);
    }

    /**
     *是否高亮
     *
     * Generated from protobuf field <code>int32 is_highlight = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setIsHighlight($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_highlight = $var;

        return $this;
    }

    /**
     *点击携带参数
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NavigationBarParams params = 8;</code>
     * @return \Dirpc\SDK\PreSale\NavigationBarParams
     */
    public function getParams()
    {
        return isset($this->params) ? $this->params : null;
    }

    public function hasParams()
    {
        return isset($this->params);
    }

    public function clearParams()
    {
        unset($this->params);
    }

    /**
     *点击携带参数
     *
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NavigationBarParams params = 8;</code>
     * @param \Dirpc\SDK\PreSale\NavigationBarParams $var
     * @return $this
     */
    public function setParams($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NavigationBarParams::class);
        $this->params = $var;

        return $this;
    }

    /**
     *点击弹窗
     *
     * Generated from protobuf field <code>map<string, string> popup = 9;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getPopup()
    {
        return $this->popup;
    }

    /**
     *点击弹窗
     *
     * Generated from protobuf field <code>map<string, string> popup = 9;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setPopup($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->popup = $arr;

        return $this;
    }

}

