<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.ServiceItem</code>
 */
class ServiceItem extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *个性化服务唯一id
     *
     * Generated from protobuf field <code>int64 id = 1;</code>
     */
    protected $id = 0;
    /**
     *个性化服务子类型(eg:10601 携宠-带箱)
     *
     * Generated from protobuf field <code>int64 service_type = 2;</code>
     */
    protected $service_type = 0;
    /**
     *服务名称
     *
     * Generated from protobuf field <code>string service_name = 3;</code>
     */
    protected $service_name = '';
    /**
     *服务描述
     *
     * Generated from protobuf field <code>string service_desc = 4;</code>
     */
    protected $service_desc = '';
    /**
     *icon
     *
     * Generated from protobuf field <code>string icon = 5;</code>
     */
    protected $icon = '';
    /**
     *勾选次数
     *
     * Generated from protobuf field <code>int64 selected_count = 6;</code>
     */
    protected $selected_count = 0;
    /**
     *价格文案，如 "{40}元"
     *
     * Generated from protobuf field <code>string price_msg = 7;</code>
     */
    protected $price_msg = '';
    /**
     *价格文案，如 "{40}元/次"
     *
     * Generated from protobuf field <code>string price_desc = 8;</code>
     */
    protected $price_desc = '';
    /**
     *价格
     *
     * Generated from protobuf field <code>double price = 9;</code>
     */
    protected $price = 0.0;
    /**
     *text
     *
     * Generated from protobuf field <code>string desc = 10;</code>
     */
    protected $desc = null;
    /**
     *url
     *
     * Generated from protobuf field <code>string tag = 11;</code>
     */
    protected $tag = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $id
     *          个性化服务唯一id
     *     @type int|string $service_type
     *          个性化服务子类型(eg:10601 携宠-带箱)
     *     @type string $service_name
     *          服务名称
     *     @type string $service_desc
     *          服务描述
     *     @type string $icon
     *          icon
     *     @type int|string $selected_count
     *          勾选次数
     *     @type string $price_msg
     *          价格文案，如 "{40}元"
     *     @type string $price_desc
     *          价格文案，如 "{40}元/次"
     *     @type float $price
     *          价格
     *     @type string $desc
     *          text
     *     @type string $tag
     *          url
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *个性化服务唯一id
     *
     * Generated from protobuf field <code>int64 id = 1;</code>
     * @return int|string
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     *个性化服务唯一id
     *
     * Generated from protobuf field <code>int64 id = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setId($var)
    {
        GPBUtil::checkInt64($var);
        $this->id = $var;

        return $this;
    }

    /**
     *个性化服务子类型(eg:10601 携宠-带箱)
     *
     * Generated from protobuf field <code>int64 service_type = 2;</code>
     * @return int|string
     */
    public function getServiceType()
    {
        return $this->service_type;
    }

    /**
     *个性化服务子类型(eg:10601 携宠-带箱)
     *
     * Generated from protobuf field <code>int64 service_type = 2;</code>
     * @param int|string $var
     * @return $this
     */
    public function setServiceType($var)
    {
        GPBUtil::checkInt64($var);
        $this->service_type = $var;

        return $this;
    }

    /**
     *服务名称
     *
     * Generated from protobuf field <code>string service_name = 3;</code>
     * @return string
     */
    public function getServiceName()
    {
        return $this->service_name;
    }

    /**
     *服务名称
     *
     * Generated from protobuf field <code>string service_name = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setServiceName($var)
    {
        GPBUtil::checkString($var, True);
        $this->service_name = $var;

        return $this;
    }

    /**
     *服务描述
     *
     * Generated from protobuf field <code>string service_desc = 4;</code>
     * @return string
     */
    public function getServiceDesc()
    {
        return $this->service_desc;
    }

    /**
     *服务描述
     *
     * Generated from protobuf field <code>string service_desc = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setServiceDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->service_desc = $var;

        return $this;
    }

    /**
     *icon
     *
     * Generated from protobuf field <code>string icon = 5;</code>
     * @return string
     */
    public function getIcon()
    {
        return $this->icon;
    }

    /**
     *icon
     *
     * Generated from protobuf field <code>string icon = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->icon = $var;

        return $this;
    }

    /**
     *勾选次数
     *
     * Generated from protobuf field <code>int64 selected_count = 6;</code>
     * @return int|string
     */
    public function getSelectedCount()
    {
        return $this->selected_count;
    }

    /**
     *勾选次数
     *
     * Generated from protobuf field <code>int64 selected_count = 6;</code>
     * @param int|string $var
     * @return $this
     */
    public function setSelectedCount($var)
    {
        GPBUtil::checkInt64($var);
        $this->selected_count = $var;

        return $this;
    }

    /**
     *价格文案，如 "{40}元"
     *
     * Generated from protobuf field <code>string price_msg = 7;</code>
     * @return string
     */
    public function getPriceMsg()
    {
        return $this->price_msg;
    }

    /**
     *价格文案，如 "{40}元"
     *
     * Generated from protobuf field <code>string price_msg = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setPriceMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->price_msg = $var;

        return $this;
    }

    /**
     *价格文案，如 "{40}元/次"
     *
     * Generated from protobuf field <code>string price_desc = 8;</code>
     * @return string
     */
    public function getPriceDesc()
    {
        return $this->price_desc;
    }

    /**
     *价格文案，如 "{40}元/次"
     *
     * Generated from protobuf field <code>string price_desc = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setPriceDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->price_desc = $var;

        return $this;
    }

    /**
     *价格
     *
     * Generated from protobuf field <code>double price = 9;</code>
     * @return float
     */
    public function getPrice()
    {
        return $this->price;
    }

    /**
     *价格
     *
     * Generated from protobuf field <code>double price = 9;</code>
     * @param float $var
     * @return $this
     */
    public function setPrice($var)
    {
        GPBUtil::checkDouble($var);
        $this->price = $var;

        return $this;
    }

    /**
     *text
     *
     * Generated from protobuf field <code>string desc = 10;</code>
     * @return string
     */
    public function getDesc()
    {
        return isset($this->desc) ? $this->desc : '';
    }

    public function hasDesc()
    {
        return isset($this->desc);
    }

    public function clearDesc()
    {
        unset($this->desc);
    }

    /**
     *text
     *
     * Generated from protobuf field <code>string desc = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->desc = $var;

        return $this;
    }

    /**
     *url
     *
     * Generated from protobuf field <code>string tag = 11;</code>
     * @return string
     */
    public function getTag()
    {
        return isset($this->tag) ? $this->tag : '';
    }

    public function hasTag()
    {
        return isset($this->tag);
    }

    public function clearTag()
    {
        unset($this->tag);
    }

    /**
     *url
     *
     * Generated from protobuf field <code>string tag = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setTag($var)
    {
        GPBUtil::checkString($var, True);
        $this->tag = $var;

        return $this;
    }

}

