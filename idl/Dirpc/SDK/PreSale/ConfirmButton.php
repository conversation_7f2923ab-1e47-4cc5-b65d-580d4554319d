<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.ConfirmButton</code>
 */
class ConfirmButton extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string text = 1;</code>
     */
    protected $text = '';
    /**
     * Generated from protobuf field <code>string exchange_text = 2;</code>
     */
    protected $exchange_text = '';
    /**
     * Generated from protobuf field <code>string bubble_text = 3;</code>
     */
    protected $bubble_text = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ButtonInfo info = 4;</code>
     */
    protected $info = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $text
     *     @type string $exchange_text
     *     @type string $bubble_text
     *     @type \Dirpc\SDK\PreSale\ButtonInfo $info
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string text = 1;</code>
     * @return string
     */
    public function getText()
    {
        return $this->text;
    }

    /**
     * Generated from protobuf field <code>string text = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setText($var)
    {
        GPBUtil::checkString($var, True);
        $this->text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string exchange_text = 2;</code>
     * @return string
     */
    public function getExchangeText()
    {
        return $this->exchange_text;
    }

    /**
     * Generated from protobuf field <code>string exchange_text = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setExchangeText($var)
    {
        GPBUtil::checkString($var, True);
        $this->exchange_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string bubble_text = 3;</code>
     * @return string
     */
    public function getBubbleText()
    {
        return isset($this->bubble_text) ? $this->bubble_text : '';
    }

    public function hasBubbleText()
    {
        return isset($this->bubble_text);
    }

    public function clearBubbleText()
    {
        unset($this->bubble_text);
    }

    /**
     * Generated from protobuf field <code>string bubble_text = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setBubbleText($var)
    {
        GPBUtil::checkString($var, True);
        $this->bubble_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ButtonInfo info = 4;</code>
     * @return \Dirpc\SDK\PreSale\ButtonInfo
     */
    public function getInfo()
    {
        return isset($this->info) ? $this->info : null;
    }

    public function hasInfo()
    {
        return isset($this->info);
    }

    public function clearInfo()
    {
        unset($this->info);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ButtonInfo info = 4;</code>
     * @param \Dirpc\SDK\PreSale\ButtonInfo $var
     * @return $this
     */
    public function setInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\ButtonInfo::class);
        $this->info = $var;

        return $this;
    }

}

