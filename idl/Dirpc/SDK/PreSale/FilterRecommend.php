<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.FilterRecommend</code>
 */
class FilterRecommend extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.FilterRecommendData filter_list = 1;</code>
     */
    private $filter_list;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\FilterRecommendData[]|\Nuwa\Protobuf\Internal\RepeatedField $filter_list
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.FilterRecommendData filter_list = 1;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getFilterList()
    {
        return $this->filter_list;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.FilterRecommendData filter_list = 1;</code>
     * @param \Dirpc\SDK\PreSale\FilterRecommendData[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFilterList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\FilterRecommendData::class);
        $this->filter_list = $arr;

        return $this;
    }

}

