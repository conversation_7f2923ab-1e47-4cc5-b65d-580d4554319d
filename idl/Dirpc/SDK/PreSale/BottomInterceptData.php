<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.BottomInterceptData</code>
 */
class BottomInterceptData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 intercept_style = 1;</code>
     */
    protected $intercept_style = 0;
    /**
     * Generated from protobuf field <code>string title_icon = 2;</code>
     */
    protected $title_icon = '';
    /**
     * Generated from protobuf field <code>string text = 3;</code>
     */
    protected $text = '';
    /**
     * Generated from protobuf field <code>string expire_text = 4;</code>
     */
    protected $expire_text = null;
    /**
     * Generated from protobuf field <code>int32 expire_time = 5;</code>
     */
    protected $expire_time = null;
    /**
     * Generated from protobuf field <code>repeated string bg_gradients = 6;</code>
     */
    private $bg_gradients;
    /**
     * Generated from protobuf field <code>string center_image = 7;</code>
     */
    protected $center_image = null;
    /**
     * Generated from protobuf field <code>repeated string center_bg_gradients = 8;</code>
     */
    private $center_bg_gradients;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InfoDesc left_info = 9;</code>
     */
    protected $left_info = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InfoDesc right_info = 10;</code>
     */
    protected $right_info = null;
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.InterceptButton buttons = 11;</code>
     */
    private $buttons;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OmegaData omega_data = 12;</code>
     */
    protected $omega_data = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OmegaData btn_omega_data = 13;</code>
     */
    protected $btn_omega_data = null;
    /**
     * Generated from protobuf field <code>string background_image = 14;</code>
     */
    protected $background_image = null;
    /**
     * Generated from protobuf field <code>string center_price_text = 15;</code>
     */
    protected $center_price_text = null;
    /**
     * Generated from protobuf field <code>string center_left_text = 16;</code>
     */
    protected $center_left_text = null;
    /**
     * Generated from protobuf field <code>string center_right_text = 17;</code>
     */
    protected $center_right_text = null;
    /**
     * Generated from protobuf field <code>string bottom_desc = 18;</code>
     */
    protected $bottom_desc = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $intercept_style
     *     @type string $title_icon
     *     @type string $text
     *     @type string $expire_text
     *     @type int $expire_time
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $bg_gradients
     *     @type string $center_image
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $center_bg_gradients
     *     @type \Dirpc\SDK\PreSale\InfoDesc $left_info
     *     @type \Dirpc\SDK\PreSale\InfoDesc $right_info
     *     @type \Dirpc\SDK\PreSale\InterceptButton[]|\Nuwa\Protobuf\Internal\RepeatedField $buttons
     *     @type \Dirpc\SDK\PreSale\OmegaData $omega_data
     *     @type \Dirpc\SDK\PreSale\OmegaData $btn_omega_data
     *     @type string $background_image
     *     @type string $center_price_text
     *     @type string $center_left_text
     *     @type string $center_right_text
     *     @type string $bottom_desc
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 intercept_style = 1;</code>
     * @return int
     */
    public function getInterceptStyle()
    {
        return $this->intercept_style;
    }

    /**
     * Generated from protobuf field <code>int32 intercept_style = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setInterceptStyle($var)
    {
        GPBUtil::checkInt32($var);
        $this->intercept_style = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string title_icon = 2;</code>
     * @return string
     */
    public function getTitleIcon()
    {
        return $this->title_icon;
    }

    /**
     * Generated from protobuf field <code>string title_icon = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTitleIcon($var)
    {
        GPBUtil::checkString($var, True);
        $this->title_icon = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string text = 3;</code>
     * @return string
     */
    public function getText()
    {
        return $this->text;
    }

    /**
     * Generated from protobuf field <code>string text = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setText($var)
    {
        GPBUtil::checkString($var, True);
        $this->text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string expire_text = 4;</code>
     * @return string
     */
    public function getExpireText()
    {
        return isset($this->expire_text) ? $this->expire_text : '';
    }

    public function hasExpireText()
    {
        return isset($this->expire_text);
    }

    public function clearExpireText()
    {
        unset($this->expire_text);
    }

    /**
     * Generated from protobuf field <code>string expire_text = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setExpireText($var)
    {
        GPBUtil::checkString($var, True);
        $this->expire_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 expire_time = 5;</code>
     * @return int
     */
    public function getExpireTime()
    {
        return isset($this->expire_time) ? $this->expire_time : 0;
    }

    public function hasExpireTime()
    {
        return isset($this->expire_time);
    }

    public function clearExpireTime()
    {
        unset($this->expire_time);
    }

    /**
     * Generated from protobuf field <code>int32 expire_time = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setExpireTime($var)
    {
        GPBUtil::checkInt32($var);
        $this->expire_time = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated string bg_gradients = 6;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getBgGradients()
    {
        return $this->bg_gradients;
    }

    /**
     * Generated from protobuf field <code>repeated string bg_gradients = 6;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBgGradients($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->bg_gradients = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string center_image = 7;</code>
     * @return string
     */
    public function getCenterImage()
    {
        return isset($this->center_image) ? $this->center_image : '';
    }

    public function hasCenterImage()
    {
        return isset($this->center_image);
    }

    public function clearCenterImage()
    {
        unset($this->center_image);
    }

    /**
     * Generated from protobuf field <code>string center_image = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setCenterImage($var)
    {
        GPBUtil::checkString($var, True);
        $this->center_image = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated string center_bg_gradients = 8;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getCenterBgGradients()
    {
        return $this->center_bg_gradients;
    }

    /**
     * Generated from protobuf field <code>repeated string center_bg_gradients = 8;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setCenterBgGradients($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->center_bg_gradients = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InfoDesc left_info = 9;</code>
     * @return \Dirpc\SDK\PreSale\InfoDesc
     */
    public function getLeftInfo()
    {
        return isset($this->left_info) ? $this->left_info : null;
    }

    public function hasLeftInfo()
    {
        return isset($this->left_info);
    }

    public function clearLeftInfo()
    {
        unset($this->left_info);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InfoDesc left_info = 9;</code>
     * @param \Dirpc\SDK\PreSale\InfoDesc $var
     * @return $this
     */
    public function setLeftInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\InfoDesc::class);
        $this->left_info = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InfoDesc right_info = 10;</code>
     * @return \Dirpc\SDK\PreSale\InfoDesc
     */
    public function getRightInfo()
    {
        return isset($this->right_info) ? $this->right_info : null;
    }

    public function hasRightInfo()
    {
        return isset($this->right_info);
    }

    public function clearRightInfo()
    {
        unset($this->right_info);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InfoDesc right_info = 10;</code>
     * @param \Dirpc\SDK\PreSale\InfoDesc $var
     * @return $this
     */
    public function setRightInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\InfoDesc::class);
        $this->right_info = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.InterceptButton buttons = 11;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getButtons()
    {
        return $this->buttons;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.InterceptButton buttons = 11;</code>
     * @param \Dirpc\SDK\PreSale\InterceptButton[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setButtons($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\InterceptButton::class);
        $this->buttons = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OmegaData omega_data = 12;</code>
     * @return \Dirpc\SDK\PreSale\OmegaData
     */
    public function getOmegaData()
    {
        return isset($this->omega_data) ? $this->omega_data : null;
    }

    public function hasOmegaData()
    {
        return isset($this->omega_data);
    }

    public function clearOmegaData()
    {
        unset($this->omega_data);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OmegaData omega_data = 12;</code>
     * @param \Dirpc\SDK\PreSale\OmegaData $var
     * @return $this
     */
    public function setOmegaData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\OmegaData::class);
        $this->omega_data = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OmegaData btn_omega_data = 13;</code>
     * @return \Dirpc\SDK\PreSale\OmegaData
     */
    public function getBtnOmegaData()
    {
        return isset($this->btn_omega_data) ? $this->btn_omega_data : null;
    }

    public function hasBtnOmegaData()
    {
        return isset($this->btn_omega_data);
    }

    public function clearBtnOmegaData()
    {
        unset($this->btn_omega_data);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.OmegaData btn_omega_data = 13;</code>
     * @param \Dirpc\SDK\PreSale\OmegaData $var
     * @return $this
     */
    public function setBtnOmegaData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\OmegaData::class);
        $this->btn_omega_data = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string background_image = 14;</code>
     * @return string
     */
    public function getBackgroundImage()
    {
        return isset($this->background_image) ? $this->background_image : '';
    }

    public function hasBackgroundImage()
    {
        return isset($this->background_image);
    }

    public function clearBackgroundImage()
    {
        unset($this->background_image);
    }

    /**
     * Generated from protobuf field <code>string background_image = 14;</code>
     * @param string $var
     * @return $this
     */
    public function setBackgroundImage($var)
    {
        GPBUtil::checkString($var, True);
        $this->background_image = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string center_price_text = 15;</code>
     * @return string
     */
    public function getCenterPriceText()
    {
        return isset($this->center_price_text) ? $this->center_price_text : '';
    }

    public function hasCenterPriceText()
    {
        return isset($this->center_price_text);
    }

    public function clearCenterPriceText()
    {
        unset($this->center_price_text);
    }

    /**
     * Generated from protobuf field <code>string center_price_text = 15;</code>
     * @param string $var
     * @return $this
     */
    public function setCenterPriceText($var)
    {
        GPBUtil::checkString($var, True);
        $this->center_price_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string center_left_text = 16;</code>
     * @return string
     */
    public function getCenterLeftText()
    {
        return isset($this->center_left_text) ? $this->center_left_text : '';
    }

    public function hasCenterLeftText()
    {
        return isset($this->center_left_text);
    }

    public function clearCenterLeftText()
    {
        unset($this->center_left_text);
    }

    /**
     * Generated from protobuf field <code>string center_left_text = 16;</code>
     * @param string $var
     * @return $this
     */
    public function setCenterLeftText($var)
    {
        GPBUtil::checkString($var, True);
        $this->center_left_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string center_right_text = 17;</code>
     * @return string
     */
    public function getCenterRightText()
    {
        return isset($this->center_right_text) ? $this->center_right_text : '';
    }

    public function hasCenterRightText()
    {
        return isset($this->center_right_text);
    }

    public function clearCenterRightText()
    {
        unset($this->center_right_text);
    }

    /**
     * Generated from protobuf field <code>string center_right_text = 17;</code>
     * @param string $var
     * @return $this
     */
    public function setCenterRightText($var)
    {
        GPBUtil::checkString($var, True);
        $this->center_right_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string bottom_desc = 18;</code>
     * @return string
     */
    public function getBottomDesc()
    {
        return isset($this->bottom_desc) ? $this->bottom_desc : '';
    }

    public function hasBottomDesc()
    {
        return isset($this->bottom_desc);
    }

    public function clearBottomDesc()
    {
        unset($this->bottom_desc);
    }

    /**
     * Generated from protobuf field <code>string bottom_desc = 18;</code>
     * @param string $var
     * @return $this
     */
    public function setBottomDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->bottom_desc = $var;

        return $this;
    }

}

