<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.FilterNormalData</code>
 */
class FilterNormalData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string filter_id = 1;</code>
     */
    protected $filter_id = null;
    /**
     *1:选中
     *
     * Generated from protobuf field <code>int32 is_selected = 2;</code>
     */
    protected $is_selected = null;
    /**
     *1:选中
     *
     * Generated from protobuf field <code>string filter_name = 3;</code>
     */
    protected $filter_name = null;
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.FilterData filter_data = 7;</code>
     */
    private $filter_data;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $filter_id
     *     @type int $is_selected
     *          1:选中
     *     @type string $filter_name
     *          1:选中
     *     @type \Dirpc\SDK\PreSale\FilterData[]|\Nuwa\Protobuf\Internal\RepeatedField $filter_data
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string filter_id = 1;</code>
     * @return string
     */
    public function getFilterId()
    {
        return isset($this->filter_id) ? $this->filter_id : '';
    }

    public function hasFilterId()
    {
        return isset($this->filter_id);
    }

    public function clearFilterId()
    {
        unset($this->filter_id);
    }

    /**
     * Generated from protobuf field <code>string filter_id = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setFilterId($var)
    {
        GPBUtil::checkString($var, True);
        $this->filter_id = $var;

        return $this;
    }

    /**
     *1:选中
     *
     * Generated from protobuf field <code>int32 is_selected = 2;</code>
     * @return int
     */
    public function getIsSelected()
    {
        return isset($this->is_selected) ? $this->is_selected : 0;
    }

    public function hasIsSelected()
    {
        return isset($this->is_selected);
    }

    public function clearIsSelected()
    {
        unset($this->is_selected);
    }

    /**
     *1:选中
     *
     * Generated from protobuf field <code>int32 is_selected = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setIsSelected($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_selected = $var;

        return $this;
    }

    /**
     *1:选中
     *
     * Generated from protobuf field <code>string filter_name = 3;</code>
     * @return string
     */
    public function getFilterName()
    {
        return isset($this->filter_name) ? $this->filter_name : '';
    }

    public function hasFilterName()
    {
        return isset($this->filter_name);
    }

    public function clearFilterName()
    {
        unset($this->filter_name);
    }

    /**
     *1:选中
     *
     * Generated from protobuf field <code>string filter_name = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setFilterName($var)
    {
        GPBUtil::checkString($var, True);
        $this->filter_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.FilterData filter_data = 7;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getFilterData()
    {
        return $this->filter_data;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.FilterData filter_data = 7;</code>
     * @param \Dirpc\SDK\PreSale\FilterData[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFilterData($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\FilterData::class);
        $this->filter_data = $arr;

        return $this;
    }

}

