<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NewPreferInfo</code>
 */
class NewPreferInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.PreferOption prefer_option = 1;</code>
     */
    private $prefer_option;
    /**
     * Generated from protobuf field <code>string head = 2;</code>
     */
    protected $head = '';
    /**
     * Generated from protobuf field <code>string head_link = 3;</code>
     */
    protected $head_link = '';
    /**
     * Generated from protobuf field <code>string remark = 4;</code>
     */
    protected $remark = '';
    /**
     * Generated from protobuf field <code>string appellation_ways = 5;</code>
     */
    protected $appellation_ways = '';
    /**
     * Generated from protobuf field <code>int32 is_remember_history_prefer = 6;</code>
     */
    protected $is_remember_history_prefer = 0;
    /**
     * Generated from protobuf field <code>string title = 7;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>string greet = 8;</code>
     */
    protected $greet = '';
    /**
     * Generated from protobuf field <code>string is_remember_history_prefer_title = 9;</code>
     */
    protected $is_remember_history_prefer_title = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\PreferOption[]|\Nuwa\Protobuf\Internal\RepeatedField $prefer_option
     *     @type string $head
     *     @type string $head_link
     *     @type string $remark
     *     @type string $appellation_ways
     *     @type int $is_remember_history_prefer
     *     @type string $title
     *     @type string $greet
     *     @type string $is_remember_history_prefer_title
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.PreferOption prefer_option = 1;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getPreferOption()
    {
        return $this->prefer_option;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.PreferOption prefer_option = 1;</code>
     * @param \Dirpc\SDK\PreSale\PreferOption[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setPreferOption($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\PreferOption::class);
        $this->prefer_option = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string head = 2;</code>
     * @return string
     */
    public function getHead()
    {
        return $this->head;
    }

    /**
     * Generated from protobuf field <code>string head = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setHead($var)
    {
        GPBUtil::checkString($var, True);
        $this->head = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string head_link = 3;</code>
     * @return string
     */
    public function getHeadLink()
    {
        return $this->head_link;
    }

    /**
     * Generated from protobuf field <code>string head_link = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setHeadLink($var)
    {
        GPBUtil::checkString($var, True);
        $this->head_link = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string remark = 4;</code>
     * @return string
     */
    public function getRemark()
    {
        return $this->remark;
    }

    /**
     * Generated from protobuf field <code>string remark = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setRemark($var)
    {
        GPBUtil::checkString($var, True);
        $this->remark = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string appellation_ways = 5;</code>
     * @return string
     */
    public function getAppellationWays()
    {
        return $this->appellation_ways;
    }

    /**
     * Generated from protobuf field <code>string appellation_ways = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setAppellationWays($var)
    {
        GPBUtil::checkString($var, True);
        $this->appellation_ways = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 is_remember_history_prefer = 6;</code>
     * @return int
     */
    public function getIsRememberHistoryPrefer()
    {
        return $this->is_remember_history_prefer;
    }

    /**
     * Generated from protobuf field <code>int32 is_remember_history_prefer = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setIsRememberHistoryPrefer($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_remember_history_prefer = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string title = 7;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string greet = 8;</code>
     * @return string
     */
    public function getGreet()
    {
        return $this->greet;
    }

    /**
     * Generated from protobuf field <code>string greet = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setGreet($var)
    {
        GPBUtil::checkString($var, True);
        $this->greet = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string is_remember_history_prefer_title = 9;</code>
     * @return string
     */
    public function getIsRememberHistoryPreferTitle()
    {
        return $this->is_remember_history_prefer_title;
    }

    /**
     * Generated from protobuf field <code>string is_remember_history_prefer_title = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setIsRememberHistoryPreferTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->is_remember_history_prefer_title = $var;

        return $this;
    }

}

