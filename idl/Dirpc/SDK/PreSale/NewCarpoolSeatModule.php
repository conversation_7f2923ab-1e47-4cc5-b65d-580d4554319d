<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NewCarpoolSeatModule</code>
 */
class NewCarpoolSeatModule extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string title = 1;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>int32 max_count = 2;</code>
     */
    protected $max_count = 0;
    /**
     * Generated from protobuf field <code>int32 select_count = 3;</code>
     */
    protected $select_count = 0;
    /**
     *座位数超选提示
     *
     * Generated from protobuf field <code>string seats_exceed_toast = 4;</code>
     */
    protected $seats_exceed_toast = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $title
     *     @type int $max_count
     *     @type int $select_count
     *     @type string $seats_exceed_toast
     *          座位数超选提示
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 max_count = 2;</code>
     * @return int
     */
    public function getMaxCount()
    {
        return $this->max_count;
    }

    /**
     * Generated from protobuf field <code>int32 max_count = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setMaxCount($var)
    {
        GPBUtil::checkInt32($var);
        $this->max_count = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 select_count = 3;</code>
     * @return int
     */
    public function getSelectCount()
    {
        return $this->select_count;
    }

    /**
     * Generated from protobuf field <code>int32 select_count = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setSelectCount($var)
    {
        GPBUtil::checkInt32($var);
        $this->select_count = $var;

        return $this;
    }

    /**
     *座位数超选提示
     *
     * Generated from protobuf field <code>string seats_exceed_toast = 4;</code>
     * @return string
     */
    public function getSeatsExceedToast()
    {
        return $this->seats_exceed_toast;
    }

    /**
     *座位数超选提示
     *
     * Generated from protobuf field <code>string seats_exceed_toast = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setSeatsExceedToast($var)
    {
        GPBUtil::checkString($var, True);
        $this->seats_exceed_toast = $var;

        return $this;
    }

}

