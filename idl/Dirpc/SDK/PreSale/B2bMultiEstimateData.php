<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.B2bMultiEstimateData</code>
 */
class B2bMultiEstimateData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *key为预估id
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.B2bDetailDataItem> item = 1;</code>
     */
    private $item;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type array|\Nuwa\Protobuf\Internal\MapField $item
     *          key为预估id
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *key为预估id
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.B2bDetailDataItem> item = 1;</code>
     * @return \Nuwa\Protobuf\Internal\MapField
     */
    public function getItem()
    {
        return $this->item;
    }

    /**
     *key为预估id
     *
     * Generated from protobuf field <code>map<string, .Dirpc.SDK.PreSale.B2bDetailDataItem> item = 1;</code>
     * @param array|\Nuwa\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setItem($var)
    {
        $arr = GPBUtil::checkMapField($var, \Nuwa\Protobuf\Internal\GPBType::STRING, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\B2bDetailDataItem::class);
        $this->item = $arr;

        return $this;
    }

}

