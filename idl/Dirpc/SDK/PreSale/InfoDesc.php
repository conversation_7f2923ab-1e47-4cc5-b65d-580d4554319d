<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.InfoDesc</code>
 */
class InfoDesc extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string title = 1;</code>
     */
    protected $title = null;
    /**
     * Generated from protobuf field <code>string sub_title = 2;</code>
     */
    protected $sub_title = null;
    /**
     * Generated from protobuf field <code>string bg_image = 3;</code>
     */
    protected $bg_image = null;
    /**
     * Generated from protobuf field <code>repeated string sub_bg_gradients = 4;</code>
     */
    private $sub_bg_gradients;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $title
     *     @type string $sub_title
     *     @type string $bg_image
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $sub_bg_gradients
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @return string
     */
    public function getTitle()
    {
        return isset($this->title) ? $this->title : '';
    }

    public function hasTitle()
    {
        return isset($this->title);
    }

    public function clearTitle()
    {
        unset($this->title);
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string sub_title = 2;</code>
     * @return string
     */
    public function getSubTitle()
    {
        return isset($this->sub_title) ? $this->sub_title : '';
    }

    public function hasSubTitle()
    {
        return isset($this->sub_title);
    }

    public function clearSubTitle()
    {
        unset($this->sub_title);
    }

    /**
     * Generated from protobuf field <code>string sub_title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setSubTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string bg_image = 3;</code>
     * @return string
     */
    public function getBgImage()
    {
        return isset($this->bg_image) ? $this->bg_image : '';
    }

    public function hasBgImage()
    {
        return isset($this->bg_image);
    }

    public function clearBgImage()
    {
        unset($this->bg_image);
    }

    /**
     * Generated from protobuf field <code>string bg_image = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setBgImage($var)
    {
        GPBUtil::checkString($var, True);
        $this->bg_image = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated string sub_bg_gradients = 4;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getSubBgGradients()
    {
        return $this->sub_bg_gradients;
    }

    /**
     * Generated from protobuf field <code>repeated string sub_bg_gradients = 4;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSubBgGradients($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->sub_bg_gradients = $arr;

        return $this;
    }

}

