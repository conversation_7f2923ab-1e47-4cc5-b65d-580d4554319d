<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.GroupCarTag</code>
 */
class GroupCarTag extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *车型标签文案
     *
     * Generated from protobuf field <code>string content = 1;</code>
     */
    protected $content = '';
    /**
     *字体未选中颜色
     *
     * Generated from protobuf field <code>string font_color = 2;</code>
     */
    protected $font_color = '';
    /**
     *字体选中颜色
     *
     * Generated from protobuf field <code>string font_select_color = 3;</code>
     */
    protected $font_select_color = '';
    /**
     *边框未选中颜色
     *
     * Generated from protobuf field <code>string border_color = 4;</code>
     */
    protected $border_color = '';
    /**
     *边框选中颜色
     *
     * Generated from protobuf field <code>string border_select_color = 5;</code>
     */
    protected $border_select_color = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $content
     *          车型标签文案
     *     @type string $font_color
     *          字体未选中颜色
     *     @type string $font_select_color
     *          字体选中颜色
     *     @type string $border_color
     *          边框未选中颜色
     *     @type string $border_select_color
     *          边框选中颜色
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *车型标签文案
     *
     * Generated from protobuf field <code>string content = 1;</code>
     * @return string
     */
    public function getContent()
    {
        return $this->content;
    }

    /**
     *车型标签文案
     *
     * Generated from protobuf field <code>string content = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setContent($var)
    {
        GPBUtil::checkString($var, True);
        $this->content = $var;

        return $this;
    }

    /**
     *字体未选中颜色
     *
     * Generated from protobuf field <code>string font_color = 2;</code>
     * @return string
     */
    public function getFontColor()
    {
        return $this->font_color;
    }

    /**
     *字体未选中颜色
     *
     * Generated from protobuf field <code>string font_color = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setFontColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->font_color = $var;

        return $this;
    }

    /**
     *字体选中颜色
     *
     * Generated from protobuf field <code>string font_select_color = 3;</code>
     * @return string
     */
    public function getFontSelectColor()
    {
        return $this->font_select_color;
    }

    /**
     *字体选中颜色
     *
     * Generated from protobuf field <code>string font_select_color = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setFontSelectColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->font_select_color = $var;

        return $this;
    }

    /**
     *边框未选中颜色
     *
     * Generated from protobuf field <code>string border_color = 4;</code>
     * @return string
     */
    public function getBorderColor()
    {
        return $this->border_color;
    }

    /**
     *边框未选中颜色
     *
     * Generated from protobuf field <code>string border_color = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setBorderColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->border_color = $var;

        return $this;
    }

    /**
     *边框选中颜色
     *
     * Generated from protobuf field <code>string border_select_color = 5;</code>
     * @return string
     */
    public function getBorderSelectColor()
    {
        return $this->border_select_color;
    }

    /**
     *边框选中颜色
     *
     * Generated from protobuf field <code>string border_select_color = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setBorderSelectColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->border_select_color = $var;

        return $this;
    }

}

