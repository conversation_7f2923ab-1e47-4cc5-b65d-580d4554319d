<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.NewFormMultiPrice</code>
 */
class NewFormMultiPrice extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string fee_msg = 1;</code>
     */
    protected $fee_msg = '';
    /**
     * Generated from protobuf field <code>double fee_amount = 2;</code>
     */
    protected $fee_amount = 0.0;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc = 3;</code>
     */
    protected $fee_desc = null;
    /**
     * Generated from protobuf field <code>string fee_icon_url = 4;</code>
     */
    protected $fee_icon_url = null;
    /**
     * Generated from protobuf field <code>int32 is_large_font = 5;</code>
     */
    protected $is_large_font = 0;
    /**
     * Generated from protobuf field <code>int32 font_size = 6;</code>
     */
    protected $font_size = 0;
    /**
     * Generated from protobuf field <code>string fee_msg_template = 7;</code>
     */
    protected $fee_msg_template = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $fee_msg
     *     @type float $fee_amount
     *     @type \Dirpc\SDK\PreSale\NewFormFeeDesc $fee_desc
     *     @type string $fee_icon_url
     *     @type int $is_large_font
     *     @type int $font_size
     *     @type string $fee_msg_template
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string fee_msg = 1;</code>
     * @return string
     */
    public function getFeeMsg()
    {
        return $this->fee_msg;
    }

    /**
     * Generated from protobuf field <code>string fee_msg = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double fee_amount = 2;</code>
     * @return float
     */
    public function getFeeAmount()
    {
        return $this->fee_amount;
    }

    /**
     * Generated from protobuf field <code>double fee_amount = 2;</code>
     * @param float $var
     * @return $this
     */
    public function setFeeAmount($var)
    {
        GPBUtil::checkDouble($var);
        $this->fee_amount = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc = 3;</code>
     * @return \Dirpc\SDK\PreSale\NewFormFeeDesc
     */
    public function getFeeDesc()
    {
        return isset($this->fee_desc) ? $this->fee_desc : null;
    }

    public function hasFeeDesc()
    {
        return isset($this->fee_desc);
    }

    public function clearFeeDesc()
    {
        unset($this->fee_desc);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.NewFormFeeDesc fee_desc = 3;</code>
     * @param \Dirpc\SDK\PreSale\NewFormFeeDesc $var
     * @return $this
     */
    public function setFeeDesc($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\NewFormFeeDesc::class);
        $this->fee_desc = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_icon_url = 4;</code>
     * @return string
     */
    public function getFeeIconUrl()
    {
        return isset($this->fee_icon_url) ? $this->fee_icon_url : '';
    }

    public function hasFeeIconUrl()
    {
        return isset($this->fee_icon_url);
    }

    public function clearFeeIconUrl()
    {
        unset($this->fee_icon_url);
    }

    /**
     * Generated from protobuf field <code>string fee_icon_url = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeIconUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_icon_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 is_large_font = 5;</code>
     * @return int
     */
    public function getIsLargeFont()
    {
        return $this->is_large_font;
    }

    /**
     * Generated from protobuf field <code>int32 is_large_font = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setIsLargeFont($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_large_font = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 font_size = 6;</code>
     * @return int
     */
    public function getFontSize()
    {
        return $this->font_size;
    }

    /**
     * Generated from protobuf field <code>int32 font_size = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setFontSize($var)
    {
        GPBUtil::checkInt32($var);
        $this->font_size = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fee_msg_template = 7;</code>
     * @return string
     */
    public function getFeeMsgTemplate()
    {
        return isset($this->fee_msg_template) ? $this->fee_msg_template : '';
    }

    public function hasFeeMsgTemplate()
    {
        return isset($this->fee_msg_template);
    }

    public function clearFeeMsgTemplate()
    {
        unset($this->fee_msg_template);
    }

    /**
     * Generated from protobuf field <code>string fee_msg_template = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsgTemplate($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg_template = $var;

        return $this;
    }

}

