<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SeatConfig</code>
 */
class SeatConfig extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 value = 1;</code>
     */
    protected $value = 0;
    /**
     * Generated from protobuf field <code>string label = 2;</code>
     */
    protected $label = '';
    /**
     * Generated from protobuf field <code>string label_2 = 3;</code>
     */
    protected $label_2 = '';
    /**
     * Generated from protobuf field <code>string label_explanation = 4;</code>
     */
    protected $label_explanation = '';
    /**
     * Generated from protobuf field <code>string select_text = 5;</code>
     */
    protected $select_text = '';
    /**
     * Generated from protobuf field <code>string price_desc = 6;</code>
     */
    protected $price_desc = '';
    /**
     * Generated from protobuf field <code>bool disable = 7;</code>
     */
    protected $disable = false;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $value
     *     @type string $label
     *     @type string $label_2
     *     @type string $label_explanation
     *     @type string $select_text
     *     @type string $price_desc
     *     @type bool $disable
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 value = 1;</code>
     * @return int
     */
    public function getValue()
    {
        return $this->value;
    }

    /**
     * Generated from protobuf field <code>int32 value = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setValue($var)
    {
        GPBUtil::checkInt32($var);
        $this->value = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string label = 2;</code>
     * @return string
     */
    public function getLabel()
    {
        return $this->label;
    }

    /**
     * Generated from protobuf field <code>string label = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setLabel($var)
    {
        GPBUtil::checkString($var, True);
        $this->label = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string label_2 = 3;</code>
     * @return string
     */
    public function getLabel2()
    {
        return $this->label_2;
    }

    /**
     * Generated from protobuf field <code>string label_2 = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setLabel2($var)
    {
        GPBUtil::checkString($var, True);
        $this->label_2 = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string label_explanation = 4;</code>
     * @return string
     */
    public function getLabelExplanation()
    {
        return $this->label_explanation;
    }

    /**
     * Generated from protobuf field <code>string label_explanation = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setLabelExplanation($var)
    {
        GPBUtil::checkString($var, True);
        $this->label_explanation = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string select_text = 5;</code>
     * @return string
     */
    public function getSelectText()
    {
        return $this->select_text;
    }

    /**
     * Generated from protobuf field <code>string select_text = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setSelectText($var)
    {
        GPBUtil::checkString($var, True);
        $this->select_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string price_desc = 6;</code>
     * @return string
     */
    public function getPriceDesc()
    {
        return $this->price_desc;
    }

    /**
     * Generated from protobuf field <code>string price_desc = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setPriceDesc($var)
    {
        GPBUtil::checkString($var, True);
        $this->price_desc = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool disable = 7;</code>
     * @return bool
     */
    public function getDisable()
    {
        return $this->disable;
    }

    /**
     * Generated from protobuf field <code>bool disable = 7;</code>
     * @param bool $var
     * @return $this
     */
    public function setDisable($var)
    {
        GPBUtil::checkBool($var);
        $this->disable = $var;

        return $this;
    }

}

