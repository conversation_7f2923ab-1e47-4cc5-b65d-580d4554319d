<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.TimeSpan</code>
 */
class TimeSpan extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string title = 1;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>string date = 2;</code>
     */
    protected $date = '';
    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.TimeSpanRange range = 3;</code>
     */
    private $range;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $title
     *     @type string $date
     *     @type \Dirpc\SDK\PreSale\TimeSpanRange[]|\Nuwa\Protobuf\Internal\RepeatedField $range
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string date = 2;</code>
     * @return string
     */
    public function getDate()
    {
        return $this->date;
    }

    /**
     * Generated from protobuf field <code>string date = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setDate($var)
    {
        GPBUtil::checkString($var, True);
        $this->date = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.TimeSpanRange range = 3;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getRange()
    {
        return $this->range;
    }

    /**
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.TimeSpanRange range = 3;</code>
     * @param \Dirpc\SDK\PreSale\TimeSpanRange[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setRange($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\TimeSpanRange::class);
        $this->range = $arr;

        return $this;
    }

}

