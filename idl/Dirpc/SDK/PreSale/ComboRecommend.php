<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.ComboRecommend</code>
 */
class ComboRecommend extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *{+15}元
     *
     * Generated from protobuf field <code>string service_fee_msg = 1;</code>
     */
    protected $service_fee_msg = '';
    /**
     *{9.5}折 x {1}张 最高抵10
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SubTitleObject sub_title_list = 2;</code>
     */
    private $sub_title_list;
    /**
     *是否选中
     *
     * Generated from protobuf field <code>int32 is_selected = 3;</code>
     */
    protected $is_selected = 0;
    /**
     *是否选中
     *
     * Generated from protobuf field <code>int32 goods_id = 4;</code>
     */
    protected $goods_id = 0;
    /**
     *遮罩图片
     *
     * Generated from protobuf field <code>string cover_img_url = 5;</code>
     */
    protected $cover_img_url = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $service_fee_msg
     *          {+15}元
     *     @type \Dirpc\SDK\PreSale\SubTitleObject[]|\Nuwa\Protobuf\Internal\RepeatedField $sub_title_list
     *          {9.5}折 x {1}张 最高抵10
     *     @type int $is_selected
     *          是否选中
     *     @type int $goods_id
     *          是否选中
     *     @type string $cover_img_url
     *          遮罩图片
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     *{+15}元
     *
     * Generated from protobuf field <code>string service_fee_msg = 1;</code>
     * @return string
     */
    public function getServiceFeeMsg()
    {
        return $this->service_fee_msg;
    }

    /**
     *{+15}元
     *
     * Generated from protobuf field <code>string service_fee_msg = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setServiceFeeMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->service_fee_msg = $var;

        return $this;
    }

    /**
     *{9.5}折 x {1}张 最高抵10
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SubTitleObject sub_title_list = 2;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getSubTitleList()
    {
        return $this->sub_title_list;
    }

    /**
     *{9.5}折 x {1}张 最高抵10
     *
     * Generated from protobuf field <code>repeated .Dirpc.SDK.PreSale.SubTitleObject sub_title_list = 2;</code>
     * @param \Dirpc\SDK\PreSale\SubTitleObject[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSubTitleList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::MESSAGE, \Dirpc\SDK\PreSale\SubTitleObject::class);
        $this->sub_title_list = $arr;

        return $this;
    }

    /**
     *是否选中
     *
     * Generated from protobuf field <code>int32 is_selected = 3;</code>
     * @return int
     */
    public function getIsSelected()
    {
        return $this->is_selected;
    }

    /**
     *是否选中
     *
     * Generated from protobuf field <code>int32 is_selected = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setIsSelected($var)
    {
        GPBUtil::checkInt32($var);
        $this->is_selected = $var;

        return $this;
    }

    /**
     *是否选中
     *
     * Generated from protobuf field <code>int32 goods_id = 4;</code>
     * @return int
     */
    public function getGoodsId()
    {
        return $this->goods_id;
    }

    /**
     *是否选中
     *
     * Generated from protobuf field <code>int32 goods_id = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setGoodsId($var)
    {
        GPBUtil::checkInt32($var);
        $this->goods_id = $var;

        return $this;
    }

    /**
     *遮罩图片
     *
     * Generated from protobuf field <code>string cover_img_url = 5;</code>
     * @return string
     */
    public function getCoverImgUrl()
    {
        return $this->cover_img_url;
    }

    /**
     *遮罩图片
     *
     * Generated from protobuf field <code>string cover_img_url = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setCoverImgUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->cover_img_url = $var;

        return $this;
    }

}

