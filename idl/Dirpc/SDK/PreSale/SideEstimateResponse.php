<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideEstimateResponse</code>
 */
class SideEstimateResponse extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 errno = 1;</code>
     */
    protected $errno = 0;
    /**
     * Generated from protobuf field <code>string errmsg = 2;</code>
     */
    protected $errmsg = '';
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideEstimateData data = 3;</code>
     */
    protected $data = null;
    /**
     * Generated from protobuf field <code>string trace_id = 4;</code>
     */
    protected $trace_id = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $errno
     *     @type string $errmsg
     *     @type \Dirpc\SDK\PreSale\SideEstimateData $data
     *     @type string $trace_id
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 errno = 1;</code>
     * @return int
     */
    public function getErrno()
    {
        return $this->errno;
    }

    /**
     * Generated from protobuf field <code>int32 errno = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setErrno($var)
    {
        GPBUtil::checkInt32($var);
        $this->errno = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string errmsg = 2;</code>
     * @return string
     */
    public function getErrmsg()
    {
        return $this->errmsg;
    }

    /**
     * Generated from protobuf field <code>string errmsg = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setErrmsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->errmsg = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideEstimateData data = 3;</code>
     * @return \Dirpc\SDK\PreSale\SideEstimateData
     */
    public function getData()
    {
        return isset($this->data) ? $this->data : null;
    }

    public function hasData()
    {
        return isset($this->data);
    }

    public function clearData()
    {
        unset($this->data);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.SideEstimateData data = 3;</code>
     * @param \Dirpc\SDK\PreSale\SideEstimateData $var
     * @return $this
     */
    public function setData($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\SideEstimateData::class);
        $this->data = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string trace_id = 4;</code>
     * @return string
     */
    public function getTraceId()
    {
        return $this->trace_id;
    }

    /**
     * Generated from protobuf field <code>string trace_id = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setTraceId($var)
    {
        GPBUtil::checkString($var, True);
        $this->trace_id = $var;

        return $this;
    }

}

