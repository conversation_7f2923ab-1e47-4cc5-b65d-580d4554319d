<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.SideRuleElement</code>
 */
class SideRuleElement extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string text = 1;</code>
     */
    protected $text = '';
    /**
     * Generated from protobuf field <code>string text_color = 2;</code>
     */
    protected $text_color = '';
    /**
     * Generated from protobuf field <code>repeated string bg_gradients = 3;</code>
     */
    private $bg_gradients;
    /**
     * Generated from protobuf field <code>string border_color = 4;</code>
     */
    protected $border_color = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $text
     *     @type string $text_color
     *     @type string[]|\Nuwa\Protobuf\Internal\RepeatedField $bg_gradients
     *     @type string $border_color
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string text = 1;</code>
     * @return string
     */
    public function getText()
    {
        return $this->text;
    }

    /**
     * Generated from protobuf field <code>string text = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setText($var)
    {
        GPBUtil::checkString($var, True);
        $this->text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string text_color = 2;</code>
     * @return string
     */
    public function getTextColor()
    {
        return $this->text_color;
    }

    /**
     * Generated from protobuf field <code>string text_color = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTextColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->text_color = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated string bg_gradients = 3;</code>
     * @return \Nuwa\Protobuf\Internal\RepeatedField
     */
    public function getBgGradients()
    {
        return $this->bg_gradients;
    }

    /**
     * Generated from protobuf field <code>repeated string bg_gradients = 3;</code>
     * @param string[]|\Nuwa\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBgGradients($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Nuwa\Protobuf\Internal\GPBType::STRING);
        $this->bg_gradients = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string border_color = 4;</code>
     * @return string
     */
    public function getBorderColor()
    {
        return isset($this->border_color) ? $this->border_color : '';
    }

    public function hasBorderColor()
    {
        return isset($this->border_color);
    }

    public function clearBorderColor()
    {
        unset($this->border_color);
    }

    /**
     * Generated from protobuf field <code>string border_color = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setBorderColor($var)
    {
        GPBUtil::checkString($var, True);
        $this->border_color = $var;

        return $this;
    }

}

