<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: side_estimate.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.RecPrizeData</code>
 */
class RecPrizeData extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *商品类型
     *
     * Generated from protobuf field <code>int64 rec_type = 1;</code>
     */
    protected $rec_type = 0;
    /**
     *商品id
     *
     * Generated from protobuf field <code>string rec_id = 2;</code>
     */
    protected $rec_id = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $rec_type
     *          商品类型
     *     @type string $rec_id
     *          商品id
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\SideEstimate::initOnce();
        parent::__construct($data);
    }

    /**
     *商品类型
     *
     * Generated from protobuf field <code>int64 rec_type = 1;</code>
     * @return int|string
     */
    public function getRecType()
    {
        return $this->rec_type;
    }

    /**
     *商品类型
     *
     * Generated from protobuf field <code>int64 rec_type = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setRecType($var)
    {
        GPBUtil::checkInt64($var);
        $this->rec_type = $var;

        return $this;
    }

    /**
     *商品id
     *
     * Generated from protobuf field <code>string rec_id = 2;</code>
     * @return string
     */
    public function getRecId()
    {
        return $this->rec_id;
    }

    /**
     *商品id
     *
     * Generated from protobuf field <code>string rec_id = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setRecId($var)
    {
        GPBUtil::checkString($var, True);
        $this->rec_id = $var;

        return $this;
    }

}

