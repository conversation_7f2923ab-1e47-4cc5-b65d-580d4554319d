<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.InvitationExtraInfo</code>
 */
class InvitationExtraInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ShareInfo share_info = 1;</code>
     */
    protected $share_info = null;
    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InviteOrderInfo order_info = 2;</code>
     */
    protected $order_info = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Dirpc\SDK\PreSale\ShareInfo $share_info
     *     @type \Dirpc\SDK\PreSale\InviteOrderInfo $order_info
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ShareInfo share_info = 1;</code>
     * @return \Dirpc\SDK\PreSale\ShareInfo
     */
    public function getShareInfo()
    {
        return isset($this->share_info) ? $this->share_info : null;
    }

    public function hasShareInfo()
    {
        return isset($this->share_info);
    }

    public function clearShareInfo()
    {
        unset($this->share_info);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.ShareInfo share_info = 1;</code>
     * @param \Dirpc\SDK\PreSale\ShareInfo $var
     * @return $this
     */
    public function setShareInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\ShareInfo::class);
        $this->share_info = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InviteOrderInfo order_info = 2;</code>
     * @return \Dirpc\SDK\PreSale\InviteOrderInfo
     */
    public function getOrderInfo()
    {
        return isset($this->order_info) ? $this->order_info : null;
    }

    public function hasOrderInfo()
    {
        return isset($this->order_info);
    }

    public function clearOrderInfo()
    {
        unset($this->order_info);
    }

    /**
     * Generated from protobuf field <code>.Dirpc.SDK.PreSale.InviteOrderInfo order_info = 2;</code>
     * @param \Dirpc\SDK\PreSale\InviteOrderInfo $var
     * @return $this
     */
    public function setOrderInfo($var)
    {
        GPBUtil::checkMessage($var, \Dirpc\SDK\PreSale\InviteOrderInfo::class);
        $this->order_info = $var;

        return $this;
    }

}

