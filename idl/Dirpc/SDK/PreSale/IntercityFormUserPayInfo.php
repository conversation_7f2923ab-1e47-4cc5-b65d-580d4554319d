<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.IntercityFormUserPayInfo</code>
 */
class IntercityFormUserPayInfo extends \Nuwa\Protobuf\Internal\Message
{
    /**
     *支付方式id
     *
     * Generated from protobuf field <code>string payment_id = 1;</code>
     */
    protected $payment_id = '';
    /**
     *企业成本中心设置
     *
     * Generated from protobuf field <code>string business_const_set = 2;</code>
     */
    protected $business_const_set = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $payment_id
     *          支付方式id
     *     @type string $business_const_set
     *          企业成本中心设置
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     *支付方式id
     *
     * Generated from protobuf field <code>string payment_id = 1;</code>
     * @return string
     */
    public function getPaymentId()
    {
        return $this->payment_id;
    }

    /**
     *支付方式id
     *
     * Generated from protobuf field <code>string payment_id = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setPaymentId($var)
    {
        GPBUtil::checkString($var, True);
        $this->payment_id = $var;

        return $this;
    }

    /**
     *企业成本中心设置
     *
     * Generated from protobuf field <code>string business_const_set = 2;</code>
     * @return string
     */
    public function getBusinessConstSet()
    {
        return isset($this->business_const_set) ? $this->business_const_set : '';
    }

    public function hasBusinessConstSet()
    {
        return isset($this->business_const_set);
    }

    public function clearBusinessConstSet()
    {
        unset($this->business_const_set);
    }

    /**
     *企业成本中心设置
     *
     * Generated from protobuf field <code>string business_const_set = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setBusinessConstSet($var)
    {
        GPBUtil::checkString($var, True);
        $this->business_const_set = $var;

        return $this;
    }

}

