<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.Minute</code>
 */
class Minute extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 minute = 1;</code>
     */
    protected $minute = null;
    /**
     * Generated from protobuf field <code>string value = 2;</code>
     */
    protected $value = null;
    /**
     * Generated from protobuf field <code>bool default_selected = 3;</code>
     */
    protected $default_selected = null;
    /**
     * Generated from protobuf field <code>string value_now = 4;</code>
     */
    protected $value_now = null;
    /**
     * Generated from protobuf field <code>string checkbox_text = 5;</code>
     */
    protected $checkbox_text = null;
    /**
     * Generated from protobuf field <code>string button_text = 6;</code>
     */
    protected $button_text = null;
    /**
     * Generated from protobuf field <code>string button_text_now = 7;</code>
     */
    protected $button_text_now = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $minute
     *     @type string $value
     *     @type bool $default_selected
     *     @type string $value_now
     *     @type string $checkbox_text
     *     @type string $button_text
     *     @type string $button_text_now
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 minute = 1;</code>
     * @return int
     */
    public function getMinute()
    {
        return isset($this->minute) ? $this->minute : 0;
    }

    public function hasMinute()
    {
        return isset($this->minute);
    }

    public function clearMinute()
    {
        unset($this->minute);
    }

    /**
     * Generated from protobuf field <code>int32 minute = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setMinute($var)
    {
        GPBUtil::checkInt32($var);
        $this->minute = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string value = 2;</code>
     * @return string
     */
    public function getValue()
    {
        return isset($this->value) ? $this->value : '';
    }

    public function hasValue()
    {
        return isset($this->value);
    }

    public function clearValue()
    {
        unset($this->value);
    }

    /**
     * Generated from protobuf field <code>string value = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setValue($var)
    {
        GPBUtil::checkString($var, True);
        $this->value = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool default_selected = 3;</code>
     * @return bool
     */
    public function getDefaultSelected()
    {
        return isset($this->default_selected) ? $this->default_selected : false;
    }

    public function hasDefaultSelected()
    {
        return isset($this->default_selected);
    }

    public function clearDefaultSelected()
    {
        unset($this->default_selected);
    }

    /**
     * Generated from protobuf field <code>bool default_selected = 3;</code>
     * @param bool $var
     * @return $this
     */
    public function setDefaultSelected($var)
    {
        GPBUtil::checkBool($var);
        $this->default_selected = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string value_now = 4;</code>
     * @return string
     */
    public function getValueNow()
    {
        return isset($this->value_now) ? $this->value_now : '';
    }

    public function hasValueNow()
    {
        return isset($this->value_now);
    }

    public function clearValueNow()
    {
        unset($this->value_now);
    }

    /**
     * Generated from protobuf field <code>string value_now = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setValueNow($var)
    {
        GPBUtil::checkString($var, True);
        $this->value_now = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string checkbox_text = 5;</code>
     * @return string
     */
    public function getCheckboxText()
    {
        return isset($this->checkbox_text) ? $this->checkbox_text : '';
    }

    public function hasCheckboxText()
    {
        return isset($this->checkbox_text);
    }

    public function clearCheckboxText()
    {
        unset($this->checkbox_text);
    }

    /**
     * Generated from protobuf field <code>string checkbox_text = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setCheckboxText($var)
    {
        GPBUtil::checkString($var, True);
        $this->checkbox_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string button_text = 6;</code>
     * @return string
     */
    public function getButtonText()
    {
        return isset($this->button_text) ? $this->button_text : '';
    }

    public function hasButtonText()
    {
        return isset($this->button_text);
    }

    public function clearButtonText()
    {
        unset($this->button_text);
    }

    /**
     * Generated from protobuf field <code>string button_text = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setButtonText($var)
    {
        GPBUtil::checkString($var, True);
        $this->button_text = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string button_text_now = 7;</code>
     * @return string
     */
    public function getButtonTextNow()
    {
        return isset($this->button_text_now) ? $this->button_text_now : '';
    }

    public function hasButtonTextNow()
    {
        return isset($this->button_text_now);
    }

    public function clearButtonTextNow()
    {
        unset($this->button_text_now);
    }

    /**
     * Generated from protobuf field <code>string button_text_now = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setButtonTextNow($var)
    {
        GPBUtil::checkString($var, True);
        $this->button_text_now = $var;

        return $this;
    }

}

