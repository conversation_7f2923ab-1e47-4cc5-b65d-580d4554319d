<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pre-sale.proto

namespace Dirpc\SDK\PreSale;

use Nuwa\Protobuf\Internal\GPBType;
use Nuwa\Protobuf\Internal\RepeatedField;
use Nuwa\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Dirpc.SDK.PreSale.MixedFeeMsg</code>
 */
class MixedFeeMsg extends \Nuwa\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string fee_msg = 1;</code>
     */
    protected $fee_msg = null;
    /**
     * Generated from protobuf field <code>string sub_fee_msg = 2;</code>
     */
    protected $sub_fee_msg = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $fee_msg
     *     @type string $sub_fee_msg
     * }
     */
    public function __construct($data = NULL) {
        \Dirpc\SDK\PreSale\GPBMetadata\PreSale::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string fee_msg = 1;</code>
     * @return string
     */
    public function getFeeMsg()
    {
        return isset($this->fee_msg) ? $this->fee_msg : '';
    }

    public function hasFeeMsg()
    {
        return isset($this->fee_msg);
    }

    public function clearFeeMsg()
    {
        unset($this->fee_msg);
    }

    /**
     * Generated from protobuf field <code>string fee_msg = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setFeeMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->fee_msg = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string sub_fee_msg = 2;</code>
     * @return string
     */
    public function getSubFeeMsg()
    {
        return isset($this->sub_fee_msg) ? $this->sub_fee_msg : '';
    }

    public function hasSubFeeMsg()
    {
        return isset($this->sub_fee_msg);
    }

    public function clearSubFeeMsg()
    {
        unset($this->sub_fee_msg);
    }

    /**
     * Generated from protobuf field <code>string sub_fee_msg = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setSubFeeMsg($var)
    {
        GPBUtil::checkString($var, True);
        $this->sub_fee_msg = $var;

        return $this;
    }

}

