namespace php Dirpc.SDK.PreSale
namespace go Dirpc.SDK.PreSale

struct SideEstimateRequest {
    1: required string    token
    2: required string    app_version
    3: required i32       access_key_id
    4: required i32       channel
    5: required i32       client_type
    6: required string    lang
    7: required string    utc_offset
    9: required string    maptype
    10: required string   ddfp
    12: required string   terminal_id
    13: required i32      origin_id
    14: required i32      platform_type
    15: required string   model
    16: required string  tab_id // 冒泡tabID http://ab.intra.xiaojukeji.com/conf/1/11322/dict/1296
    17: required i32      page_type
    18: required i32      user_type
    19: required string   tab_list // 前端当前的tab信息，包括tab_id 和 是否默认选中  (6.5预估表单+carpool预估表单 + 8.0表单tab)
    20: optional i32      estimate_style_type // 预估表单样式，0:老样式，1:单行  2双排新表单 3多tab新表单 4:一站式出行
    21: optional i32      expand_popup_status // 是否展示膨胀弹窗 0:不展示 1展示
    22: optional i32      already_auto_refresh_estimate // 是否已自动刷新预估  0:否 1是

    30: required i32        area
    33: required double     from_lat
    34: required double     from_lng
    35: required double     lat // 当前定位点
    36: required double     lng // 当前定位点
    38: required double     to_lat
    39: required double     to_lng
    40: required string     from_name
    41: required string     to_name
    42: required string     from_poi_id
    43: required string     from_poi_type
    44: required string     to_poi_id
    45: required string     to_poi_type
    46: required string     from_address
    47: required string     to_address

    48: required string     route_id     // 用户当前路线id

    54: required i32        payments_type //用户选择的支付方式
    55: required string     departure_time  //时间戳

    56: required i32        button_style   // pGetDynamicConfig下发的按钮样式

    79: required i32        rec_theme_type // 推荐主题样式

    80: required string     multi_product_category //  {"estimate_id":"eid新快车","is_select_reccombo":0}

    81: required string     estimate_trace_id

    82: required string     stopover_points // 途经点信息

    83: required int32      order_type        //订单类型：0 是实时 1是预约

    84: required int32      call_car_type     //CallCarType 叫车类型：0为普通叫车；1为代叫车

    85: required string      xpsid

    86: required string      xpsid_root

    87: optional string      act_id
    88: optional i32         from_type // 冒泡来源
    //todo 只是短期方案，和产品拉齐，后续陈引如果还要做类似规范（沟通组件按钮点击后事件消失），需要拉端介入改为通用方案
    89: optional string      is_need_hide_event //是否需要展示事件 （通勤完单优惠大促：CommuteCouponFinishOrder）
    90: optional i32         font_scale_type
    91: optional i32         form_style_exp
    92: optional string      one_stop_version
    93: optional i32         best_shift_distance
    94: optional i32         best_shift_exp_group
    95: optional i32         rec_form
}

struct SideRuleButton {
    1: required string  text
    2: required string  font_color
    3: required list<string>  background_gradients // 背景色：无值背景透明，有1个纯色，多个渐变
    4: optional string  border_color
    5: optional i32 button_type // button中link的action_type
    6: optional string link_url
    7: optional map<string,string>  link_params // 请求link的参数
    8: optional map<string, string> callback_extra_infos // 需要端回传的参数
}

struct SideRuleTask {
    1: required string task_id
    2: required i32 task_status
    3: optional i32 cur_count //已领取状态下的当前进度 for进度条
    4: optional i32 total_count //已领取状态下的总进度 for进度条
}

struct SideRulePrivilege {
    1: required i32     package_id          // 权益包id
    2: required string  privilege_source    // 权益场景
}

struct SideRuleElement {
    1: required string text
    2: required string text_color
    3: required list<string> bg_gradients
    4: optional string border_color
}

struct SideRuleTopContent {
    1: required string              text                        // 标题 = 主标题 + 副标题
    2: required string              text_color                  // 标题颜色
    3: optional string              text_highlight_color        // 高亮颜色
    4: required bool                has_arrow                   // 是否有箭头，仅style=0/1，端进行处理
    5: required string              link_url                    // 跳转链接
    6: required i32                 style                       // 渲染风格；0代表一行文案居中；1代表一行文案居左；2代表上小下大；3代表上大下小；4代表双行主标题大小
    7: required i32                 action_type                 // 交互样式，action_type:2 ，跳H5，action_type:3  请求任务
    8: optional list<string>        background_gradients        // 背景色：无值背景透明，有1个纯色，多个渐变
    9: optional string              bg_image                    // 背景图
    10: optional SideRuleButton     button                      // 右边的按钮，style=1/2/3/4支持
    11: optional SideRuleTask       task                        // 任务下发task信息
    12: optional string             left_icon                   // 左侧图标，仅在style=2/3/4时存在值
    13: optional SideRulePrivilege  privilege                   // 领取权益信息
    14: optional i32                risk_control_judgment       // 是否因风控追加参数 0:不追加 1: 追加参数 "lat","lng", "ddfp"，
    15: optional i32                request_success_type        // 请求link成功后的动作 1：请求预估 2：请求沟通接口
    16: optional map<string,string>   link_params    // 请求link携带的参数
    17: optional string             first_line_left_icon        // 第一行左边的icon，目前只在style为3、5时候有值
    18: optional i32                request_method              // 请求link的方式：0:get 1:post
    19: optional i32             expire_time                 // 过期时间，沟通组件倒计时使用
    20: optional list<SideRuleElement>  second_line_elements      // 第二行展示的标签们
}

struct SideRuleBottomContent {
    1: required string              text                    // 文案
    2: required string              text_color              // 文案颜色
    3: required list<string>  background_gradients          // 背景色：无值背景透明，有1个纯色，多个渐变；端处理透明度，我们下发六位的色值 #ffffff
    4: required i32                  action_type            // 交互样式；0代表无；1代表半弹层；
    5: required bool                has_arrow               // 是否有箭头
    6: optional bool                 is_force_notice        // 是否强弹
    7: optional string              text_highlight_color    // text里的高亮颜色
    8: optional string             left_icon                // 左侧图标
    9: required string              link_url                // 跳转链接
    10: optional ComboRecommend     combo_recommend         // 左侧图标
    11: required i32                 style                  // 渲染风格
    12: optional map<string,string>  link_params            // 请求link的参数
    13: optional SideRuleButton     button                  // 右边的按钮
    14: optional i32                request_method              // 请求link的方式：0:get 1:post
    15: optional i32                request_success_type        // 请求link成功后的动作 1：请求预估 2：请求沟通接口
    16: optional i32                risk_control_judgment       // 是否因风控追加参数 0:不追加 1: 追加参数 "lat","lng", "ddfp"，
    17: optional i32                 multi_line_style_type      // 0（默认）：端上现有逻辑 1：最多展示两行
    18: optional string            user_information_authorization     // 信息授权 type
    19: optional bool               refresh_estimate           // 是否刷新预估
    20: optional string            animation_text               // 动效文案
    21: optional SideRuleBottomContentLeftInfo            left_info     // 左侧信息
    22: optional SideRuleBottomContentPopupInfo           popup_info     // 弹窗信息
    23: optional map<string, string> callback_extra_infos                // 需要端回传的参数
}

struct SideRuleBottomContentLeftInfo {
    1: required string                                      icon                    // icon
    2: required string                                      text_image              // 文案
    3: required i32                                         action_type             // 交互样式；0代表无；1代表半弹层；
    4: required SideRuleBottomContentLeftPopup              popup_info              // 弹窗
}

struct SideRuleBottomContentLeftPopup {
    1: required string              top_img_url           // 图片
    2: required list<string>        rule_texts              // 文案
}

struct SideRuleBottomContentPopupInfo {
    1: required string              top_img_url              // 背景图url
    2: required string              img_url                  // 奖励图
    3: required string              bottom_text              // 底部文案
}

struct SideRuleButtonLeftContent {
    1: required string              text                    // 文案
    2: required string              text_color              // 文案颜色
    3: required list<string>  background_gradients          // 背景色：无值背景透明，有1个纯色，多个渐变；端处理透明度，我们下发六位的色值 #ffffff
    4: required i32                  action_type            // 交互样式；0代表无；1代表半弹层；
    5: required bool                has_arrow               // 是否有箭头
    6: optional bool                 is_force_notice        // 是否强弹
    7: optional string              text_highlight_color    // text里的高亮颜色
    8: optional string             left_icon                // 左侧图标
    9: required string              link_url                // 跳转链接
    10: optional ComboRecommend     combo_recommend         // 左侧图标
    11: required i32                 style                  // 渲染风格
    12: optional map<string,string>  link_params            // 请求link的参数
    13: optional SideRuleButton     button                  // 右边的按钮
    14: optional i32                request_method              // 请求link的方式：0:get 1:post
    15: optional i32                request_success_type        // 请求link成功后的动作 1：请求预估 2：请求沟通接口
    16: optional i32                risk_control_judgment       // 是否因风控追加参数 0:不追加 1: 追加参数 "lat","lng", "ddfp"，
    17: optional i32                 multi_line_style_type      // 0（默认）：端上现有逻辑 1：最多展示两行
}

struct ComboRecommend {
    1: required string                          service_fee_msg    // {+15}元
    2: required list<SubTitleObject>            sub_title_list     // {9.5}折 x {1}张 最高抵10
    3: required i32                             is_selected        // 是否选中
    4: required i32                             goods_id
    5: required string                          cover_img_url      // 遮罩图片
}

struct SubTitleObject {
    1: optional string   coupon_info          // "{9.5}折"
    2: optional string   coupon_number          // "x {1}张"
}

struct OmegaParam {
    1: optional string             click_name
    2: optional string             show_name
    3: optional map<string,string> extension
}

struct SideRuleTop {
    1: required string               event                  // 活动事件标识，比如 special_rule
    2: required list<i32>            event_sub_list         // 规则列表，之前的rule_type
    3: required string               estimate_id            // eid，前端硬是要加
    5: required i32                  weight                 // 权重
    7: required SideRuleTopContent   content                // 具体内容
    8: optional map<string,string>   track                  // 埋点信息
    9: optional OmegaParam           omega_param            // omega埋点信息
}


struct SideRuleBottom {
    1: required string                 event                   // 沟通事件标识，比如 special_rule
    2: required list<i32>              event_sub_list          // 规则列表，之前的rule_type
    3: required string                 estimate_id             // eid，前端硬是要加
    5: required i32                    weight                  // 权重
    6: required SideRuleBottomContent  content                 // 具体内容
    7: optional map<string,string>     track                   // 埋点信息
    9: optional OmegaParam             omega_param             // omega埋点信息
}

struct SideRuleButtonLeft {
    1: required string                 event                   // 沟通事件标识，比如 special_rule
    2: required list<i32>              event_sub_list          // 规则列表，之前的rule_type
    3: required string                 estimate_id             // eid，前端硬是要加
    5: required i32                    weight                  // 权重
    6: required SideRuleButtonLeftContent  content                 // 具体内容
    7: optional map<string,string>     track                   // 埋点信息
    9: optional OmegaParam             omega_param             // omega埋点信息
}

struct SideRightBubble {
    1: required i32     type
    2: required string  content
    3: required string  content_icon
}

struct SideSubTitle {
    1: required string          content
    2: required string          font_color
    3: required string          border_color
    4: required list<string>    bg_gradients
    5: required string          icon_url
    6: required i32             is_union_hidden // 双勾和未勾时候外层盒子是否展示标签
    7: required i32             is_box_outer_show // 该标签在外层是否过滤掉
    8: required string             link_url // 标签链接
}

struct RightSubTitle {
    1: required string          content
    2: required string          font_color
    3: required string          border_color
    4: required list<string>    bg_gradients
    5: required string          icon_url
    6: required string          highlight_color
}

struct SideNewFormFeeDesc {
	1: required string border_color // 边框RGB色值, 如#000000, 空串标识无边框
	2: required string content
	3: required string icon
    4: required double amount
    5: required i32    type
    6: required string text_color
    7: required string highlight_color
}

struct SideEstimateComboRecommend {
    1: required string estimate_id
    2: required i32 goods_id
    3: required i32 best_batch_id
    4: optional string high_light
    5: required string left_icon
    6: required string left_title
    7: optional string right_title
    8: required list<string> sub_title
    9: required string service_fee_msg
    10: optional string fee_info_desc
    11: optional string fee_desc_url
    12: required string fee_msg
    14: optional i32 select_type
    15: optional list<SideNewFormFeeDesc> price_info_desc
    16: optional map<string, string> extra
    17: optional i32 type
    18: optional string fee_amount
    19: optional string car_fee_msg
    20: optional string car_fee_amount
    21: optional string combo_button_subtitle
    22: optional string    fee_msg_template
}

struct SideBubble {
    2: required list<SideSubTitle>  sub_title_list
    3: optional bool                sub_title_hidden_others
    4: optional SideEstimateComboRecommend combo_recommend
    5: optional list<SideNewFormFeeDesc> fee_desc_list
    6: optional string recommend_bubble
    7: optional RightSubTitle        right_sub_title
    8: optional RecPrizeData         rec_prize_data
}

struct RecPrizeData {
    1: required i64                 rec_type                 // 商品类型
    2: required string              rec_id                   // 商品id
}

struct SideInterceptData {
    1: required string  bg_url
    2: required string  title
    3: required string  sub_title
    4: required string  content
    5: required string  content_desc
    6: required string  left_button
    7: required string  right_button
    8: required string  extra
}

struct BottomInterceptData {
    1: required  i32    intercept_style
    2: required  string title_icon
    3: required  string text
    4: optional  string expire_text
    5: optional  i32    expire_time
    6: optional  list<string>   bg_gradients
    7: optional  string         center_image
    8: optional  list<string>   center_bg_gradients
    9: optional  InfoDesc left_info
    10: optional InfoDesc right_info
    11: optional list<InterceptButton>  buttons
    12: optional OmegaData omega_data
    13: optional OmegaData btn_omega_data
    14: optional string    background_image
    15: optional string    center_price_text
    16: optional string    center_left_text
    17: optional string    center_right_text
    18: optional  string    bottom_desc
}

struct OmegaData {
    1: optional string omega_event_id
    2: optional map<string, string> omega_parameter
}

struct InfoDesc {
    1: optional string  title
    2: optional string  sub_title
    3: optional string  bg_image
    4: optional list<string>  sub_bg_gradients
}

struct InterceptButton {
    1: required string text
    2: optional string  font_color
    3: optional list<string>  bg_gradients
    4: optional string  border_color
}

struct SideDialogSubContent {
    1: required string  link
    2: required string  text
}

struct SideDialogData {
    1: required string                  img_url
    2: required double                  img_ratio
    3: required string                  title
    4: required string                  cancel_text
    5: required string                  confirm_text
    6: required string                  confirm_color
    7: required SideDialogSubContent    sub_content
    8: optional i32                     dialog_style
    9: optional string                  dialog_id
    10: optional string                 content
    11: optional string                 light_img_url // 动效图
}

struct SideGuidePopupData {
    1: required i32             product_category
    2: required SideDialogData  dialog_data
    3: optional map<string,string>     track
}

struct SideCommonTopRule {
    2: optional map<string,string>  multi_scene_text_v2     //  多个品类的rule_type不相同
    3: optional map<string,string>  single_scene_text       //  多个品类的rule_type相同
    4: optional map<string,MultiRuleConf> multi_scene_conf
    5: optional map<string,MultiRuleConf> single_scene_conf
}

struct SideCommonBottomRule {
    2: optional map<string,string>  multi_scene_text_v2     //  多个品类的rule_type不相同
    3: optional map<string,string>  single_scene_text       //  多个品类的rule_type相同
    4: optional map<string,MultiRuleConf> multi_scene_conf
    5: optional map<string,MultiRuleConf> single_scene_conf
}

struct SideCommonButtonLeftRule {
    2: optional map<string,string>  multi_scene_text_v2     //  多个品类的rule_type不相同
    3: optional map<string,string>  single_scene_text       //  多个品类的rule_type相同
    4: optional map<string,MultiRuleConf> multi_scene_conf
    5: optional map<string,MultiRuleConf> single_scene_conf
}

struct MultiRuleConf {
    1: optional string left_icon  // 左侧icon
    2: optional string text // 文案
    3: optional string link_url // 跳转链接
}

struct TopRule {
    1: required map<string,SideCommonTopRule> common            // 查询字典，key为具体event，比如 recipe_rule
    2: required map<string,SideRuleTop>     rule                // 顶部沟通激励组件，key为eid
}

struct BottomRule {
    1: required map<string,SideCommonBottomRule> common         // 查询字典，key为具体event，比如 special_rule
    2: required map<string,SideRuleBottom> rule                 // 底部价格沟通+支付沟通，key为eid
}

struct ButtonLeftRule {
    1: required map<string,SideCommonButtonLeftRule> common         // 查询字典，key为具体event，比如 special_rule
    2: required map<string,SideRuleButtonLeft> rule                 // 底部价格沟通+支付沟通，key为eid
}

struct TabExtraTag {
    1: required string content
    2: required string font_color
}

struct EstimateTab {
   1: required string tab_name
   2: required TabExtraTag extra_tag
   3: required string pic_url
   4: required string left_pic_url
   5: required string top_bubble_url
}


struct SideEstSubTitle {
    1: required string content
    2: required string font_color
}

struct SideEstFeeDesc {
    1: required string content
    2: required string font_color
    3: required string border_color
}

struct SideActionParams {
    1: required i32 region_id
    2: required i32 start_station_id          // 起点站点id
    3: required i32 end_station_id          // 终点站点id
    4: required i32 route_id
    5: required i32 page_type
    6: required i32 distance_type
}

struct SideEstGuideBar {
    1: required i64 style_type                           // 渲染样式：1代表强推荐 / 2代表弱推荐
    2: required i64 action_type                          // 操作行为：1代表跳页面 / 2代表toast / 3 代表跳页面，销毁原页面 / 4 发单
    3: required string left_icon
    4: required string title
    5: required list<SideEstSubTitle> sub_title_list
    6: required string fee_msg
    7: required list<SideEstFeeDesc>  fee_desc_list
    8: required string recommend_toast
    9: required string button_text
    10: required string business_type  //pick_on_time:必有车
    11: required string jump_uri
    12: required string estimate_id
    13: required string fee_detail_url //费用明细链接，目前只有必有车使用
    14: required GuideBarButtonInfo button_info //按钮详细信息
    15: required list<string> background_gradients // 导流的背景色
    16: required PNewOrderParams pneworder_params_from_side //必有车预估信息，供发单使用
    17: required string border_color //包框颜色
    18: optional string             guide_path //跳转的tab id
    19: optional SideActionParams   guide_params //跳转参数
}
struct PNewOrderParams {
    1: required string              agent_type
    2: required i32              type
    3: required i32              page_type
    4: required i64              is_support_multi_selection
    5: required string              stopover_points
    6: required string              multi_require_product
    7: required string          estimate_trace_id
}

struct GuideBarButtonInfo {
    1: required list<string>  background_gradients //背景色
    2: required string  text_color //文本颜色
    3: required string  border_color //包框颜色
}


struct EjectLayer {
     1: required string omega_event_id
     2: required string title
     3: required string content
     4: optional string top_icon
     5: optional string link_url
     6: optional string link_name
     8: required EjectButton left_button
     9: required EjectButton right_button
     10: required string request_url
     11: required string request_method
}

struct EjectButton {
     1: required string text
     2: required EjectButtonParams params
     3: optional string bubble_text
}

struct EjectButtonParams {
     1: required i32 action
     2: required string event
     3: optional string params
}

struct EjectLayerInfo {
    1: optional HeadInfo         head_info
    2: optional list<SwitchItem> switch_list
    3: optional CloseButton      close_button
    4: optional ConfirmButton    confirm_button
}

struct HeadInfo {
    1: required string bg_img
    2: required string title
    3: required string sub_title
}

struct SwitchItem {
    1: required string key
    2: required string title
    3: required string sub_title
    4: required string left_icon
    5: required i32    switch_status
    6: required string link
}

struct CloseButton {
    1: required string text
}

struct ConfirmButton {
    1: required string text
    2: required string exchange_text
    3: optional string bubble_text
    4: optional ButtonInfo info
}

struct ButtonInfo {
    1: required i32 action
    2: required string event
    3: optional string extra_params
}

struct AgreementPopup {
    1: required string title
    2: required string content
    3: required string sub_content
    4: required EjectButton left_button
    5: required EjectButton right_button
    6: required string omega_event_id
}

struct UserGuideInfo {
    1: required string icon
    2: required i32    style    // 0:无引导  1:纯小手  2:呼吸+小手
}

struct ExpandPopup {
    1: required string title
    2: required string content
    3: required string amount
    4: required string img_url
    5: required string gif_img_url
}

struct SideEstimateData {
    3: required TopRule                     top_rule            // 顶部沟通激励组件
    4: required BottomRule                  bottom_rule         // 底部价格沟通+支付沟通
    5: required map<string,SideBubble>      form_data           // 表单渲染
    6: required SideInterceptData           intercept_data      // 拦截弹窗
    7: optional SideGuidePopupData          guide_popup_data    // 引导弹窗
    8: optional map<string, EstimateTab>    estimate_tab        // tab信息
    9: optional SideEstGuideBar             guide_bar           // 导流条
    10: optional map<string, EstimateTab>   one_stop_tab        // 8.0 tab修正,气泡
    11: optional EjectLayer                 eject_layer         // 半弹层 已下线 deprecated
    12: optional EjectLayerInfo             eject_layer_info     // 半弹层新
    13: optional AgreementPopup             agreement_popup      // 协议弹窗
    14: optional UserGuideInfo              user_guide_info      // 用户引导
    15: optional map<string, LayoutData>    layout_data          // 盒子是否出气泡
    16: optional BottomInterceptData        bottom_intercept_data // 底部弹窗拦截
    17: optional ExpandPopup                expand_popup // 呼返膨胀弹窗
    18: required ButtonLeftRule             button_left_rule         // 按钮处沟通组件
}

struct LayoutData{
    1: required string recommend_bubble
}

struct SideEstimateResponse {
    1: required i32              errno
    2: required string           errmsg
    3: optional SideEstimateData data
    4: required string           trace_id
}