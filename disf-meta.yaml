# disf-meta.yaml作为本模块的服务元信息，必须存放到build.sh同级目录（一般情况下是代码库的根目录）
# 语言、协议相关信息直接影响到本模块接入DiSF，请务必保证正确并及时更新维护。
# 错误的格式、错误的配置，可能会导致scmpf打包失败，如遇到此问题请联系"DiSF人工客服"，或加入"服务治理用户群"。
# 为方便后续同学维护，请复制本注释到您的disf-meta.yaml文件中。
# 格式规范：http://wiki.intra.xiaojukeji.com/x/7Di9CQ
  
# 模块负责人（必选，可填写多个）
owner: congming,wangbizhou,wangdongyang,dingwei
# 模块语言（必选：php/golang/c++/java/...）
language: php
# 模块描述（必选）
description: 用户需求确认
# 模块部署的odin节点，到父节点即可（必选，可填写多个）
deployOdinNs:
- pre-sale.gs.biz.didi.com
# 模块（服务自身）默认协议、端口（必选）
mainProtocol:
  protocol: http
  port: 8000
