<?php
/**
 * Nuwa-PHP - A PHP Framework For Web.
 *
 * <AUTHOR>
 */
define('NUWA_START', microtime(true));
define('PROJ_PATH', __DIR__ . '/');
define('APP_PATH', PROJ_PATH . 'app/');

// 青铜门配置的命名空间，乘客bronze_door_passenger、司机bronze_door_driver
define('DOOR_NAMESPACE', "bronze_door_passenger");

// 线下测试配置使用，上线前务必删除！！！值为湾流平台创建的分支名
//define('DOOR_VERSION_NAME', "dev_branch_name");
//@TODO 临时修复方案
define('ENVIRONMENT', 'production');

/**
 * Register The Auto Loader.
 */
require(PROJ_PATH . 'vendor/autoload.php');

/**
 * require constants.php for biz
 */
require_once (PROJ_PATH.'config/constants.php');

/**
 * Create The Application.
 *
 * read app/config/app.php and instance application.
 */
$app = Nuwa\Core\Application::create(realpath(PROJ_PATH));

/**
 * Bootstrap the application for HTTP requests.
 *
 * foreach(app/bootstrap/Bootstrap->bootstrappers as bootstrapper)
 *     bootstrapper->bootstrap()
 * end
 */
$app->bootstrap();

/**
 * Run The Application
 *
 * $plugin->routerStartup()
 * route($request)
 * $plugin->routerShutdown()
 *
 * $plugin->dispatchLoopStartup()
 *
 * initView
 * do {
 *     $plugin->preDispatch()
 *
 *     dispatcher($request, $response, $view)) // call controller->action
 *
 *     $plugin->postDispatch()
 * } while (--nesting > 0 && !request_is_dispatched())
 *
 * $plugin->dispatchLoopShutdown()
 */
$app->run();
