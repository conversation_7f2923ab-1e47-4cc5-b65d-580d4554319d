<?php
/**
 * Created by PhpStorm.
 * <AUTHOR> <<EMAIL>>
 * Date: 2024/2/4
 * Time: 19:28 PM
 */
namespace PreSale\Models\GuidePath;
use BizLib\Utils\Language;
use Disf\SPL\Trace;

class GuidePath
{

    const NAV3 = "na-v3";
    const NAV2 = "na-v2";
    const WEB = "webapp_path";
    
    const DEFAULTPRODUCTCATEGORY = "default";

    /**
     * 根据不同端获取引导path
     * @param $iPCId
     * @param $sAccessKeyId
     * @param $aPathConfig
     * @return string
     */

     public function getGuidePath($iPCId, $sAccessKeyId, $sLang = null) {
        $aPathConfig = Language::getDecodedTextFromDcmp('estimate_new_form-guide_path', $sLang);
        $sResult = "";
        if (!empty($aPathConfig[$sAccessKeyId][$iPCId])) {
            $sResult = $aPathConfig[$sAccessKeyId][$iPCId];
            $sResult         = Language::replaceTag(
                $sResult,
                ['product_category' => $iPCId,'trace_id' => Trace::traceId()]
            );
            return $sResult;
        }
        if (!empty($aPathConfig[$sAccessKeyId][self::DEFAULTPRODUCTCATEGORY])) {
            $sResult = $aPathConfig[$sAccessKeyId][self::DEFAULTPRODUCTCATEGORY];
            $sResult         = Language::replaceTag(
                $sResult,
                ['product_category' => $iPCId,'trace_id' => Trace::traceId()]
            );
            return $sResult;
        }
        return $sResult;
    }
}