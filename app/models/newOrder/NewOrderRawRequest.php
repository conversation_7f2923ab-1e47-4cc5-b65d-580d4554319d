<?php

namespace PreSale\Models\newOrder;

class NewOrderRawRequest
{
    private $_ci;
    public $starting_poi_id;
    public $dest_poi_id;
    public $area;
    public $lat;
    public $lng;
    public $flat;
    public $from_lat; // 站点拼车时站点起始坐标纬度
    public $flng;
    public $from_lng; // 站点拼车时站点起始坐标经度
    public $tlat;
    public $tlng;
    public $maptype;
    public $encrypt_oid;
    public $channel;
    public $token;
    public $driver_token;
    public $callcar_type;
    public $callcar_phone;
    public $callcar_name;
    public $user_pay_info;
    public $estimate_price;
    public $business_submit;
    public $sdk_openid;
    public $sdk_package;
    public $sdk_timestamp;
    public $sdk_noncestr;
    public $sdk_sign;
    public $sdk_version;
    public $access_token;
    public $a3_token;
    public $es_ip;
    public $es_imei;
    public $es_suuid;
    public $es_o_source;
    public $es_company_id;
    public $es_pay_type;
    public $airport_id;
    public $appTime;
    public $time;
    public $bookingTime;
    public $ip_address;
    public $default_f_srctag;
    public $default_f_uid;
    public $default_f_searchid;
    public $default_t_srctag;
    public $default_t_uid;
    public $default_t_searchid;
    public $choose_f_srctag;
    public $choose_f_uid;
    public $choose_f_searchid;
    public $choose_t_srctag;
    public $choose_t_uid;
    public $choose_t_searchid;
    public $if_move;
    public $if_cf;
    public $is_multicar;
    public $default_voucher;
    public $suuid;
    public $encrypt_code;
    public $card_id;
    public $fsource;
    public $fsource_tag;
    public $order_source;
    public $order_source_car;
    public $default_fsource;
    public $datatype;
    public $guidance_id;
    public $traffic_number;
    public $traffic_dep_time;
    public $FlightDepcode;
    public $FlightArrcode;
    public $depart_delay_time;
    public $guide_from_airport;
    public $is_cip;
    public $is_traffic_insure;
    public $depart_ahead_time;
    public $otype;
    public $scene_type;
    public $combo_type;
    public $car_pool;
    public $is_station;
    public $pool_seat;
    public $carpool_show;
    public $meal_model;
    public $like_wait;
    public $is_select_insurance;
    public $departure_range;
    public $x_activity_id;
    public $x_activity_type;
    public $is_region;
    public $rented_info;
    public $gov_order_mark;
    public $enable_reassign;
    public $enable_line_up;
    public $to_area;
    public $tip;
    public $extraInfo;
    public $tsource;
    public $openid;
    public $imei;
    public $business_id;
    public $strategy_token;
    public $combo_id;
    public $require_level;
    public $input;
    public $type;
    public $estimate_trace_id;
    public $bubble_trace_id;
    public $estimate_id;
    public $fromName;
    public $fromAddress;
    public $toName;
    public $toAddress;
    public $fromAddressAll;
    public $toAddressAll;
    public $appversion;
    public $appVersion;
    public $from;
    public $platform_type;
    public $designated_driver;
    public $client_type;
    public $open_ability;
    public $lang;
    public $show_md5;
    public $custom_feature;
    public $origin_id;
    public $specialPoiid;
    public $specialPoiName;
    public $specialHistory;
    public $specialPoiSceneType;
    public $specialPoiParkingProperty;
    public $source_product;
    public $source_scene;
    public $guide_scene;
    public $flier;
    public $agentType;
    public $lastOrderId;
    public $model;
    public $all_get;
    public $all_post;
    public $multiRequireProduct;
    public $double_check;
    public $roaming;
    public $b2b_callcar;
    public $athena_id;
    public $os_version; //版本号
    public $wifi_mac; //链接wifi的mac地址
    public $idfa; //ios广告唯一标识 android不传
    public $menu_id;
    public $sub_menu_id;
    public $iSpecialSceneParam;
    public $iCountPriceType; // 计价模式
    public $portCode; //港口编号
    public $portType; //港口类型（港深|其他）
    public $openidType; //openid对应的环境类型
    public $wsgEnv; //安全sdk环境参数
    public $iSpecialRatePoolSeat; //特价拼车mvp实验webapp新加参数
    public $isLuxuryDowncastConfirm; //豪华车，表明乘客是否愿意向下听单
    public $iDispatchType; //派单类型,暂时只给openapi使用
    public $wapNoIdentity; //wapwebapp 用户是否有真身

    // 微信小程序、miniApp开关（由于其版本比主版本低，为也不维护版本号，采用多传参数方案，）
    public $bVideoSwitch; // 录像
    public $bRecordSwitch; // 录音
    public $bRealNameAuthSwitch; // 实名认证
    public $bEmergencyContactSwitch; // 紧急联系人
    public $bForceEmergencyContactSwitch; //强制紧急联系人
    public $iFeatureEnable; // 是否启用某个功能，由于端每次一个新的功能都需要加一个字段，
                            // 后续字段会越来越多，不好维护，和端@卢海名确认新增一个位字段，
                            // 后续收敛到这个统一的字段
    public $sDestPoiCode; // 目的地POI代码
    public $sDestPoiTag; // 目的地POI标签
    public $iStartBroadcastTime; //拼车波次分单用户选择的时间

    //12306订单新增参数
    public $sRailwayNumber; //火车车次
    public $iRailwayStationId; //火车站id
    public $iRailwayStation; //火车站名
    public $sRailwayArrTime; // 到站时间
    public $sPartnerOid; //合作方订单id
    public $sPartnerEncryptStr; //合作方机密字符串，需要解密验证（不建议使用该字段传参，该字段仅用于解密验证）

    public $aSelectMemberCoupons; //用户选择的权益券


    public function __construct() {
        $this->starting_poi_id = $this->getRequest()->fetchGetPost('starting_poi_id', false, true);
        $this->dest_poi_id     = $this->getRequest()->fetchGetPost('dest_poi_id', false, true);
        $this->area            = intval($this->getRequest()->getPost('area', false, true));
        $this->lat      = (float)($this->getRequest()->getPost('lat', false, true));
        $this->lng      = (float)($this->getRequest()->getPost('lng', false, true));
        $this->flat     = (float)($this->getRequest()->getPost('flat', false, true));
        $this->from_lat = $this->getRequest()->getPost('from_lat', false, true);
        // 站点拼车时的起始坐标纬度
        $this->flng     = (float)($this->getRequest()->getPost('flng', false, true));
        $this->from_lng = $this->getRequest()->getPost('from_lng', false, true);
        // 站点拼车时的起始坐标经度
        $this->tlat            = (float)($this->getRequest()->getPost('tlat', false, true));
        $this->tlng            = (float)($this->getRequest()->getPost('tlng', false, true));
        $this->maptype         = (string)($this->getRequest()->fetchGetPost('maptype', false, true));
        $this->encrypt_oid     = $this->getRequest()->getPost('encrypt_oid', false, true);
        $this->channel         = intval($this->getRequest()->getPost('channel', false, true));
        $this->token           = $this->getRequest()->getPost('token', false, true);
        $this->driver_token    = $this->getRequest()->getPost('driver_token', false, true);
        $this->callcar_type    = $this->getRequest()->fetchGetPost('callcar_type', false, true);
        $this->callcar_phone   = $this->getRequest()->fetchGetPost('callcar_phone', false, true);
        $this->callcar_name    = $this->getRequest()->fetchGetPost('callcar_name', false, true);
        $this->user_pay_info   = $this->getRequest()->getPost('user_pay_info', false, true);
        $this->estimate_price  = $this->getRequest()->getPost('estimate_price', false, true);
        $this->business_submit = $this->getRequest()->getPost('business_submit', false, true);
        $this->sdk_openid      = (string)($this->getRequest()->fetchGetPost('sdk_openid', false, true));
        $this->sdk_package     = $this->getRequest()->fetchGetPost('sdk_package', false, true);
        $this->sdk_timestamp   = intval($this->getRequest()->fetchGetPost('sdk_timestamp', false, true));
        $this->sdk_noncestr    = $this->getRequest()->fetchGetPost('sdk_noncestr', false, true);
        $this->sdk_sign        = $this->getRequest()->fetchGetPost('sdk_sign', false, true);
        $this->sdk_version     = $this->getRequest()->fetchGetPost('sdk_version', false, true);
        $this->access_token    = (string)($this->getRequest()->fetchGetPost('access_token', false, true));
        $this->a3_token        = (string)($this->getRequest()->fetchGetPost('a3_token', false, true));
        $this->es_ip           = (string)($this->getRequest()->fetchGetPost('es_ip', false, true));
        $this->es_imei         = (string)($this->getRequest()->fetchGetPost('es_imei', false, true));
        $this->es_suuid        = (string)($this->getRequest()->fetchGetPost('es_suuid', false, true));
        $this->es_o_source     = (string)($this->getRequest()->fetchGetPost('es_o_source', false, true));
        $this->es_company_id   = (string)($this->getRequest()->fetchGetPost('es_company_id', false, true));
        $this->es_pay_type     = (string)($this->getRequest()->fetchGetPost('es_pay_type', false, true));
        $this->airport_id      = intval($this->getRequest()->getPost('airport_id', false, true));
        $this->appTime         = intval($this->getRequest()->getPost('appTime', false, true));
        $this->time            = (string)($this->getRequest()->getPost('time', false, true));
        $this->bookingTime     = intval($this->getRequest()->getPost('bookingTime', false, true));
        $this->ip_address      = (new \BizLib\Utils\Request())->getIpAddress();
        $this->default_f_srctag = (string)($this->getRequest()->getPost('default_f_srctag', false, true));
        //默认起点srctag
        $this->default_f_uid = (string)($this->getRequest()->getPost('default_f_uid', false, true));
        //默认起点uid
        $this->default_f_searchid = (string)($this->getRequest()->getPost('default_f_searchid', false, true));
        //默认起点searchid
        $this->default_t_srctag = (string)($this->getRequest()->getPost('default_t_srctag', false, true));
        //默认终点srctag
        $this->default_t_uid = (string)($this->getRequest()->getPost('default_t_uid', false, true));
        //默认终点uid
        $this->default_t_searchid = (string)($this->getRequest()->getPost('default_t_searchid', false, true));
        //默认终点searchid
        $this->choose_f_srctag = (string)($this->getRequest()->getPost('choose_f_srctag', false, true));
        //用户选择起点srctag
        $this->choose_f_uid = (string)($this->getRequest()->getPost('choose_f_uid', false, true));
        //用户选择起点uid
        $this->choose_f_searchid = (string)($this->getRequest()->getPost('choose_f_searchid', false, true));
        //用户选择起点searchid
        $this->choose_t_srctag = (string)($this->getRequest()->getPost('choose_t_srctag', false, true));
        //用户选择终点srctag
        $this->choose_t_uid = (string)($this->getRequest()->getPost('choose_t_uid', false, true));
        //用户选择终点uid
        $this->choose_t_searchid = (string)($this->getRequest()->getPost('choose_t_searchid', false, true));
        //用户选择终点searchid
        $this->if_move = (string)($this->getRequest()->getPost('if_move', false, true));
        //是否拖拽
        $this->if_cf = (string)($this->getRequest()->getPost('if_cf', false, true));
        //是否默认展现备选热门点
        $this->is_multicar      = intval($this->getRequest()->fetchGetPost('is_multicar', false, true));
        $this->default_voucher  = intval($this->getRequest()->getPost('default_voucher', false, true));
        $this->suuid            = $this->getRequest()->fetchGetPost('suuid', false, true);
        $this->encrypt_code     = (string)($this->getRequest()->fetchGetPost('encrypt_code', false, true));
        $this->card_id          = (string)($this->getRequest()->fetchGetPost('card_id', false, true));
        $this->fsource          = intval($this->getRequest()->fetchGetPost('fsource', false, true));
        $this->fsource_tag      = intval($this->getRequest()->getPost('fsource_tag', false, true));
        $this->tsource          = intval($this->getRequest()->fetchGetPost('tsource', false, true));
        $this->order_source     = $this->getRequest()->getPost('order_source', false, true);
        $this->order_source_car = $this->getRequest()->getPost('order_source_car', false, true);
        $this->default_fsource  = $this->getRequest()->getPost('default_fsource', false, true);
        $this->datatype         = $this->getRequest()->fetchGetPost('datatype', false, true);
        $this->guidance_id      = $this->getRequest()->fetchGetPost('guidance_id', false, true);
        $this->traffic_number   = (string)($this->getRequest()->getPost('traffic_number', false, true));
        $this->traffic_dep_time = (string)($this->getRequest()->getPost('traffic_dep_time', false, true));
        //航班出发日期
        $this->flightDepcode = (string)($this->getRequest()->getPost('FlightDepcode', false, true));
        //航班出发地三字码
        $this->flightArrcode = (string)($this->getRequest()->getPost('FlightArrcode', false, true));
        //航班目的地三字码
        $this->depart_delay_time = intval($this->getRequest()->getPost('depart_delay_time', false, true));
        //航班到达后多久出发
        $this->guide_from_airport = intval($this->getRequest()->getPost('guide_from_airport', false, true));
        //是否有地接服务
        $this->is_cip = intval($this->getRequest()->getPost('is_cip', false, true));
        //是否勾选cip服务
        $this->is_traffic_insure = intval($this->getRequest()->getPost('is_traffic_insure', false, true));
        //是否有送机险服务
        $this->depart_ahead_time = intval($this->getRequest()->getPost('depart_ahead_time', false, true));
        //航班起飞前多久出发
        $this->otype        = intval($this->getRequest()->getPost('otype', false, true));
        $this->scene_type   = intval($this->getRequest()->fetchGetPost('scene_type', false, true));
        $this->combo_type   = intval($this->getRequest()->fetchGetPost('combo_type', false, true));
        $this->car_pool     = intval($this->getRequest()->fetchGetPost('car_pool', false, true));
        $this->is_station   = intval($this->getRequest()->fetchGetPost('is_station', false, true));
        $this->pool_seat    = $this->getRequest()->getPost('pool_seat', false, true);
        $this->carpool_show = intval($this->getRequest()->fetchGetPost('carpool_show', false, true));
        $this->meal_model   = intval($this->getRequest()->getPost('meal_model', false, true));
        $this->like_wait    = intval($this->getRequest()->fetchGetPost('like_wait', false, true));
        $this->is_select_insurance = intval($this->getRequest()->fetchGetPost('is_select_insurance', false, true));
        $this->departure_range     = $this->getRequest()->getPost('departure_range', false, true);
        $this->x_activity_id       = $this->getRequest()->getPost('x_activity_id', false, true);
        $this->x_activity_type     = $this->getRequest()->getPost('x_activity_type', false, true);
        $this->is_region           = $this->getRequest()->fetchGetPost('is_region', false, true);
        //传0是0，不传为false
        $this->rented_info     = $this->getRequest()->fetchGetPost('rented_info', false, true);
        $this->gov_order_mark  = $this->getRequest()->fetchGetPost('gov_order_mark', false, true);
        $this->enable_reassign = $this->getRequest()->getPost('enable_reassign', false, true);
        $this->enable_line_up  = $this->getRequest()->getPost('enable_line_up', false, true);
        $this->to_area         = intval($this->getRequest()->fetchGetPost('to_area', false, true));
        $this->tip            = intval($this->getRequest()->getPost('tip', false, true));
        $this->extraInfo      = (string)($this->getRequest()->getPost('extraInfo', false, true));
        $this->openid         = (string)($this->getRequest()->getPost('openid', false, true));
        $this->imei           = $this->getRequest()->fetchGetPost('imei', false, true);
        $this->business_id    = intval($this->getRequest()->fetchGetPost('business_id', false, true));
        $this->strategy_token = $this->getRequest()->getPost('strategy_token', false, true);
        $this->combo_id       = intval($this->getRequest()->getPost('combo_id', false, true));
        $this->require_level  = $this->getRequest()->getPost('require_level', false, true);
        $this->input          = intval($this->getRequest()->getPost('input', false, true));
        $this->type           = intval($this->getRequest()->getPost('type', false, true));
        $this->estimate_trace_id = (string)($this->getRequest()->fetchGetPost('estimate_trace_id', false, true));
        $this->bubble_trace_id   = (string)($this->getRequest()->fetchGetPost('bubble_trace_id', false, true));
        $this->estimate_id       = (string)($this->getRequest()->fetchGetPost('estimate_id', false, true));
        $this->fromName          = (string)($this->getRequest()->getPost('fromName', false, true));
        $this->fromAddress       = (string)($this->getRequest()->getPost('fromAddress', false, true));
        $this->toName            = (string)($this->getRequest()->getPost('toName', false, true));
        $this->toAddress         = (string)($this->getRequest()->getPost('toAddress', false, true));
        $this->fromAddressAll    = (string)($this->getRequest()->getPost('fromAddressAll', false, true));
        $this->toAddressAll      = (string)($this->getRequest()->getPost('toAddressAll', false, true));
        $this->appversion        = $this->getRequest()->fetchGetPost('appversion', false, true);
        $this->appVersion        = $this->getRequest()->fetchGetPost('appVersion', false, true);
        $this->from          = $this->getRequest()->getPost('from', false, true);
        $this->platform_type = intval($this->getRequest()->fetchGetPost('platform_type', false, true));
        $this->designated_driver = (string)($this->getRequest()->fetchGetPost('designated_driver', false, true));
        $this->client_type       = intval($this->getRequest()->fetchGetPost('client_type', false, true));
        $this->open_ability      = $this->getRequest()->getPost('open_ability', false, true);
        $this->lang           = $this->getRequest()->fetchGetPost('lang', false, true);
        $this->show_md5       = (string)($this->getRequest()->fetchGetPost('show_md5', false, true));
        $this->custom_feature = $this->getRequest()->getPost('custom_feature', false, true);
        $this->origin_id      = intval($this->getRequest()->fetchGetPost('origin_id', false, true));
        $this->specialPoiid   = $this->getRequest()->getPost('specialPoiid', false, true);
        $this->specialPoiName = $this->getRequest()->getPost('specialPoiName', false, true);
        $this->specialHistory = $this->getRequest()->getPost('specialHistory', false, true);
        $this->specialPoiSceneType       = $this->getRequest()->getPost('specialPoiSceneType', false, true);
        $this->specialPoiParkingProperty = $this->getRequest()->getPost('specialPoiParkingProperty', false, true);
        $this->source_product            = $this->getRequest()->fetchGetPost('source_product', false, true);
        $this->source_scene = $this->getRequest()->getPost('source_scene', false, true);
        $this->guide_scene  = $this->getRequest()->getPost('guide_scene', false, true);
        $this->flier        = intval($this->getRequest()->fetchGetPost('flier', false, true));
        $this->agentType    = (string)($this->getRequest()->fetchGetPost('agent_type', false, true));
        $this->lastOrderId  = (string)($this->getRequest()->fetchGetPost('last_order_id', false, true));
        $this->model        = (string)($this->getRequest()->fetchGetPost('model', false, true));
        $this->all_get      = $this->getRequest()->getQuery(null, false, true);
        $this->all_post     = $this->getRequest()->getPost(null, false, true);
        $this->multiRequireProduct = $this->getRequest()->getPost('multi_require_product', false, true);
        $this->double_check        = intval($this->getRequest()->fetchGetPost('double_check', false, true));
        $this->terminal_id         = intval($this->getRequest()->fetchGetPost('terminal_id', false, true));
        $this->roaming = intval($this->getRequest()->fetchGetPost('roaming', false, true));
        //是否是漫游订单
        $this->b2b_callcar = intval($this->getRequest()->fetchGetPost('b2b_callcar', false, true));
        $this->athena_id   = (string)($this->getRequest()->fetchGetPost('athena_id', false, true));
        $this->os_version  = (string)($this->getRequest()->fetchGetPost('os', false, true));
        $this->wifi_mac    = (string)($this->getRequest()->fetchGetPost('wifi_mac', false, true));
        $this->idfa        = (string)($this->getRequest()->fetchGetPost('idfa', false, true));
        $this->menu_id     = (string)($this->getRequest()->fetchGetPost('menu_id', false, true));
        $this->sub_menu_id = (string)($this->getRequest()->fetchGetPost('sub_menu_id', false, true));
        $this->use_dpa     = '0' === $this->getRequest()->fetchGetPost('use_dpa', false, true) ? 0 : 1;
        //注意:是明确指明为不使用，其他默认使用
        $this->iSpecialSceneParam = intval($this->getRequest()->fetchGetPost('special_scene_param', false, true));
        $this->iCountPriceType    = intval($this->getRequest()->fetchGetPost('count_price_type', false, true));
        $this->portCode           = (string)($this->getRequest()->fetchGetPost('port_code', false, true));
        $this->portType           = (string)($this->getRequest()->fetchGetPost('port_type', false, true));
        $this->openidType         = (string)($this->getRequest()->fetchGetPost('openid_type', false, true));
        //openid对应的环境类型
        $this->wsgEnv        = (string)($this->getRequest()->fetchGetPost('wsgenv', false, true));
        $this->wapNoIdentity = intval($this->getRequest()->fetchGetPost('wap_no_identity', false, true));
        $this->isLuxuryDowncastConfirm = $this->getRequest()->fetchGetPost('is_luxury_downcast_confirm', false, true);
        //豪华车，表明乘客是否愿意向下听单
        $this->iDispatchType        = intval($this->getRequest()->fetchGetPost('dispatch_type', false, true));
        $this->iSpecialRatePoolSeat = intval($this->getRequest()->fetchGetPost('special_rate_pool_seat', false, true));
        $this->bVideoSwitch         = 1 == intval($this->getRequest()->fetchGetPost('video_switch', false, true)) ? true : false;
        // 录像
        $this->bRecordSwitch = 1 == intval($this->getRequest()->fetchGetPost('record_switch', false, true)) ? true : false;
        // 录音
        $this->bRealNameAuthSwitch = 1 == intval($this->getRequest()->fetchGetPost('real_name_auth_switch', false, true)) ? true : false;
        // 实名认证
        $this->bEmergencyContactSwitch = 1 == intval($this->getRequest()->fetchGetPost('emergency_contact_switch', false, true)) ? true : false;
        // 紧急联系人
        $this->bForceEmergencyContactSwitch = 1 == intval($this->getRequest()->fetchGetPost('force_emergency_contact_switch', false, true)) ? true : false;
        // 强制检查紧急联系人
        $this->iFeatureEnable      = intval($this->getRequest()->fetchGetPost('feature_enable', false, true));
        $this->sDestPoiCode        = intval($this->getRequest()->fetchGetPost('dest_poi_code', false, true));
        $this->sDestPoiTag         = intval($this->getRequest()->fetchGetPost('dest_poi_tag', false, true));
        $this->iStartBroadcastTime = intval($this->getRequest()->fetchGetPost('start_broadcast_time', false, true));
        $this->sRailwayNumber      = $this->getRequest()->fetchGetPost('railway_number', false, true);
        //火车车次
        $this->iRailwayStationId = $this->getRequest()->fetchGetPost('railway_station_id', false, true);
        //火车站id
        $this->iRailwayStation = $this->getRequest()->fetchGetPost('railway_station', false, true);
        //火车站名
        $this->sRailwayArrTime = $this->getRequest()->fetchGetPost('railway_arr_time', false, true);
        $this->sPartnerOid     = $this->getRequest()->fetchGetPost('partner_oid', false, true);
        //合作方订单id
        $this->sPartnerEncryptStr = $this->getRequest()->fetchGetPost('partner_encrypt_str', false, true);
        //合作方加密字符串：火车站名、乘客手机、到站时间、加密订单号
        $sSelectPrivIds = $this->getRequest()->fetchGetPost('select_priv_ids', false, true);
        $this->aSelectMemberCoupons = empty($sSelectPrivIds) ? [] : explode(',', $sSelectPrivIds);
    }

    /**
     * @return NewOrderRawRequest
     */
    public static function create() {
        return ImmutableObject::wrap(new self());
    }
}
