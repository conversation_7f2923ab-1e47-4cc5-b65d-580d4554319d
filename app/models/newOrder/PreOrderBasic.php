<?php

namespace PreSale\Models\newOrder;

class PreOrderBasic
{
    // will write to aOrderInfo
    public $iTip;
    public $sExtraInfo;
    public $iProductId;
    public $sStrategyToken;
    public $iComboId;
    public $sRequireLevel;
    public $iVersion; //默认订单版本1等待60秒订单;2等待90秒订单;3等待5分钟订单,这个字段感觉没什么用
    public $iSourceType;
    public $sBirthTime;
    public $iInput;
    public $iType;
    public $iChannel;
    // not write to aOrderInfo
    public $bB2bChannel;
    public $sEstimateTraceId;
    public $sBubbleTraceId;
    public $sEstimateId; // 多表单预估需要传过来的estimate_id
    public $bMultiEstimate; // 多车型预估
    public $sRawStartingName;
    public $sRawDestName;
    public $sFrom;
    public $sFromName;
    public $sFromAddress;
    public $sDestName;
    public $sDestAddress;
    public $iOriginId;
    public $iWebApp;
    public $sEncryptOid;

    public $sAppVersion;
    public $sAppVersionAfterRepaired;

    public $sLanguage;
    public $aCustomFeature;
    public $iFastCar;
    public $aPlatformGuideInfo = [];
    public $aOrderUserDefined  = [];        //订单扩展字段，可随意添加数据 http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=107426182
    public $bPlatformGuided;
    public $sImei;
    public $sShowMd5;

    // 新加，还未分类
    public $sFSource;
    public $sTSource;
    public $sOrderSource;
    public $sOrderSourceCar;
    public $sDefaultFSource;
    public $sDataType;
    public $iGuidanceId;
    public $iLikeWait;
    public $iSelectInsurance;
    public $sOpenId;
    public $sSDKOpenId;
    public $sIp;
    public $sDeviceId;
    public $sSuuid;
    public $sToken;
    public $iClientType;
    public $sModel;
    public $aRequestDataReal;
    public $sAgentType;
    public $iB2bCallCar;
    public $sMenuId;
    public $sSubMenuId;
    public $iDispatchType; //派单类型,暂时只给openapi使用
    public $iWapNoIdentity;
    public $iPartnerId; //合作方id，http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=*********

    //require_level & require_level_text
    public $aCarType;
    public $sDesignatedDriver;
    public $iDataType;

    public $bIsUniTaxi;
    public $iFromUber;

    public $iPlatformType;
    public $aOpenAbility;

    public $aPoiInfo;

    public $iWithFlightNumber;

    public $iTerminalId;
    public $aMultiRequireProduct;
    public $iRoaming;

    // 计价模式
    public $iCountPriceType;
    // 导流场景
    public $iGuideScene;
    public $iStartBroadCastTime;
}
