<?php

namespace PreSale\Models\newOrder;

class PreOrderPassenger
{
    public $iPassengerRole;
    public $sCreateTime;
    public $sToken;

    public $sCallcarPhone;
    public $sCallcarName;
    // 乘车人
    public $sPassengerPhone; // 乘车人手机号

    // 叫车人
    public $iPassengerId;
    public $iCallCarPassengerId; // 代叫车乘车人pid
    public $aPassengerInfo; // $aPassengerInfo['phone'] 叫车人手机号
    public $aPassengerDbInfo;
    public $iPassengerInfoChannel;
    public $iPassengerDbInfoChannel;

    // 这个是历史数据，但需要有passengerId才能获取，所以放到这个步骤
    public $sCachedEstimateTraceId;

    // 是否代叫车
    public $bCallcar;
    // 带叫车原始参数
    public $aCallCarInfo;

    //会员信息
    public $aMember;                // member原始数据.
//    public $iMemberLevelId;         //会员等级
//    public $sMemberLevelName;       //会员名称
//    public $sMemberLevelIcon;       //会员等级icon
//    public $sMemberLevelCoreIcon;   //会员等级core icon
//    public $aMemberPrivileges;      //会员权益
    public $aMemberMultiProducts;   //批量会员信息列表（anycar使用）

    //用户选择的权益券
    public $aSelectMemberCoupons;
}
