<?php

namespace PreSale\Models\lbs;

use BizLib\Client\LbsppProxyApiClient;
use Biz<PERSON>ib\Config as NuwaConfig;
use BizLib\Log as NuwaLog;
use BizLib\Utils\UtilHelper;
// DOC: http://docs.xiaojukeji.com/pages/viewpage.action?pageId=6818602
use Disf\SPL\Trace;
use BizLib\Client\Lbspp5ProxyApiClient;

class Lbs
{
    /** @var $lbsppclient LbsppProxyApiClient */
    public $lbsppclient;
    const TYPE_ORDER      = 1;
    const TYPE_DRIVER     = 2;
    const ORDER_EXPIRE    = 300;
    const DRIVER_EXPIRE   = 300;
    const LBS5_TOKEN      = '933973fb3391e8c70467fcd9cc7ce010';
    const LBS5_TABLE_NAME = 'gs-order';
    const P_NEW           = 2; //已发单,等待应答

    private $_NewLbsOpen   = 0;
    private $_busid        = 258;
    private $_busType      = 1;
    private $_busOrderType = 3;
    private $_arrLogParam  = array();

    private $_aSetConfig = array();

    public function __construct() {
        $openConfig = NuwaConfig::config('config_lbs', 'new_lbs_open');
        $den        = $openConfig['pct'];
        if (0 == rand($den, 10) % 10 && 1 == $openConfig['open']) {
            $this->_NewLbsOpen = 1;
        }

        $this->_busid   = NuwaConfig::config('config_lbs', 'new_lbs_busid');
        $this->_busType = NuwaConfig::config('config_lbs', 'new_lbs_bustype');
    }

    private function _setConfig($iType) {
        if (isset($this->_aSetConfig[$iType])) {
            return true;
        }

        $this->iSendTimeOut    = NuwaConfig::config('config_lbs', 'send_timeout');
        $this->iReceiveTimeOut = NuwaConfig::config('config_lbs', 'receive_timeout');

        if (self::TYPE_ORDER == $iType) {
            $aServer  = NuwaConfig::config('config_lbs', 'order_servers');
            $sTransId = NuwaConfig::config('config_lbs', 'order_trans_id');
        } else {
            $aServer  = NuwaConfig::config('config_lbs', 'driver_servers');
            $sTransId = NuwaConfig::config('config_lbs', 'driver_trans_id');
        }

        //log_notice("LBS_ADD_LOG transId:$sTransId servers:".json_encode($aServer));
//        $this->didilbs->set_config($aServer, $sTransId, $this->iSendTimeOut, $this->iReceiveTimeOut);
        $this->_aSetConfig[$iType] = true;

        return true;
    }

    // $iMapType // 1百度 2腾讯
    // iOid 高位id 包含区号和订单号
    public function addOrder($iOid, $fLng, $fLat, $iMapType = 2, $iTimeOut = null, $aOrderInfo = array(), $aStatus = array()) {
        $this->_setConfig(self::TYPE_ORDER);
        if (is_null($iTimeOut)) {
            $iTimeOut = self::ORDER_EXPIRE;
        }

        $sTraceId   = Trace::traceId();
        $sToken     = self::LBS5_TOKEN;
        $sTableName = self::LBS5_TABLE_NAME;
        $oLbspp5ProxyApiClient = new Lbspp5ProxyApiClient();
        $bIsCarPool            = UtilHelper::checkOrderExtraType($aOrderInfo['extra_type'], array('o_carpool'));
        $iProductId            = isset($aOrderInfo['product_id']) ? $aOrderInfo['product_id'] : GS_PRICE_GULFSTREAM;
        $iPushProductId        = \BizLib\Utils\Product::getCommonProductId($iProductId);
        $aStatus['biztype']    = $iPushProductId;
        $aStatus['bizstatus']  = self::P_NEW; //已发单,等待应答
        $aStatus['order_type'] = $aOrderInfo['type'];
        $aStatus['car_type']   = $aOrderInfo['require_level'];
        $aStatus['carpool']    = $bIsCarPool ? 1 : 0;
        $aResult = $oLbspp5ProxyApiClient->getUpdateRes($sTraceId, $sToken, $iOid, $sTableName, $fLng, $fLat, $iMapType, time(), $aStatus);
        $this->_arrLogParam['Lbspp5ProxyApiClient_result'] = json_encode($aResult);
        NuwaLog::updateLogHeadData($this->_arrLogParam);

        return $aResult;
    }

    //暂时没有用到, 使用时注意验证正确性
    public function delOrder($iOid, $fLng, $fLat, $iMapType) {
        $this->_setConfig(self::TYPE_ORDER);
        if ($this->_NewLbsOpen) {
            $logid = Trace::getLogid();
            $this->lbsppclient = new LbsppProxyApiClient();
            $aResult           = $this->lbsppclient->DeleteCoordinate($this->_busid, $this->_busOrderType, $iOid, $fLng, $fLat, $iMapType, $logid);
        }

        if (empty($aResult['ret'])) {
            NuwaLog::warning('errno:'.O_ERRNO_DEL_TO_LBS."| errmsg: del order from LBS err|oid:$iOid|fLng:$fLng|fLat:$fLat|iMapType:$iMapType");
        }

        return $aResult;
    }

    //暂时没有用到, 使用时注意验证正确性
//    public function searchOrder($iOid, $iCoordType = 2, $iTimeOut = null)
//    {
//        $this->_setConfig(self::TYPE_ORDER);
//        if (is_null($iTimeOut)) {
//            $iTimeOut = $this->iTimeOut;
//        }
//        if ($this->_NewLbsOpen) {
//            $logid = get_logid();
//            $this->load->thirdservice('lbsppclient');
//            $aResult = $this->lbsppclient->GetCoordinatesInArea($this->_busid,  $this->_busOrderType,array($iOid),$iTimeOut,$iCoordType,$logid);
//
//        }
//        if (empty($aResult['ret'])) {
//            log_warning("errno: LBS_ERROR | errmsg: search order to LBS err|oid:$iOid|iCoorType:$iCoordType|iTimeOut:$iTimeOut");
//        }
//        return $aResult;
//    }
//
//    public function searchNearOrder($fLng, $fLat, $iDistance, $iLimit=100, $iCoordType=1, $iStatus=-1, $iNeedCoord=true, $iResultCoordType=1)
//    {
//        $this->_setConfig(self::TYPE_ORDER);
//        if ($this->_NewLbsOpen) {
//            $logid = get_logid();
//            $this->load->thirdservice('lbsppclient');
//            $aResult = $this->lbsppclient->GetCoordinatesInArea($this->_busid, $this->_busOrderType, $fLng, $fLat, 2, $iDistance, $iLimit, self::ORDER_EXPIRE, $iStatus,$logid);
//            $this->_arrLogParam['lbs_searchNearOrder_param'] = json_encode(array("lng"=>$fLng,"lat"=>$fLat));
//            $this->_arrLogParam['lbs_result'] = json_encode($aResult);
//            $this->_arrLogParam['logId'] = $logid;
//            log_add_basic($this->_arrLogParam);
//
//        }
//        if (empty($aResult['ret'])) {
//            log_warning("errno: LBS_ERROR | errmsg: search near order to LBS err|fLng:$fLng|fLat:$fLat|iDistance:$iDistance|ret:".json_encode($aResult));
//        }
//        return $aResult;
//    }
//
//    public function searchNearDriver($fLng, $fLat, $iDistance, $iLimit=100, $iCoordType=1, $iStatus=-1, $iNeedCoord=true, $iResultCoordType=1)
//    {
//        $this->_setConfig(self::TYPE_DRIVER);
//        $logid = get_logid();
//        if ($this->_NewLbsOpen) {
//            $this->load->thirdservice('lbsppclient');
//            $timeAgo = 300; //5分钟内更新的数据
//            $aResult = $this->lbsppclient->GetCoordinatesInArea($this->_busid, $this->_busType, $fLng, $fLat, $iCoordType, $iDistance, $iLimit, $timeAgo, $iStatus,$logid);
//        }
//        $this->_arrLogParam['lbs_open'] = $this->_NewLbsOpen;
//        $this->_arrLogParam['lbs_result'] = json_encode($aResult);
//        $this->_arrLogParam['logId'] = $logid;
//        log_add_basic($this->_arrLogParam);
//        if (empty($aResult['ret'])) {
//            log_warning("errno: LBS_ERROR | errmsg: search near Driver to LBS err|fLng:$fLng|fLat:$fLat|iDistance:$iDistance|ret:".json_encode($aResult));
//        }
//        return $aResult;
//    }
}
