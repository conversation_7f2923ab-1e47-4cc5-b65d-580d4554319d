<?php
namespace PreSale\Models\featurePlugin;

class Common
{
    /* 功能列表 **/
    const FEATURE_CARPOOL_SHUNLU_TAG            = 1000;  // 拼车顺路标签
    const FEATURE_COMBO_SALE                    = 1001;  // 车型下挂的套餐搭售
    const FEATURE_SPACIOUS_FEE_DESC_LIST        = 1002;  // 车大的优惠标签
    const FEATURE_STATION_CARPOOL_FAST_CAR_DIFF = 1003;  // 极速拼车的"比快车省"标签
    const FEATURE_CATEGORY_INFO                 = 1004;  // 侧边栏，v3返回的category_info字段
    const FEATURE_X_DISCOUNT_BOX                = 1005;  // X折特价车盒子
    const FEATURE_FILTER_INFO                   = 1007;  // 筛选器
    const FEATURE_SINKING_EXP                   = 1008;  // 下沉表单实验

    const FEATURE_PERSONALIZED_SWITCH_LAYER     = 1010;  // 个性化开关弹层
    const FEATURE_GUIDE_POPUP                   = 1011;  // 导流弹窗
    const FEATURE_GUIDE_BAR                     = 1012;  // 导流bar
    const FEATURE_USER_GUIDE_INFO               = 1012;  // 新流手势引导
    const FEATURE_RECOMMEND_BUBBLE              = 1013;  // 勾选推荐气泡
    const FEATURE_STATION_CARPOOL_DISCOUNT_TAG  = 1014;  // 极速拼车优惠标签

    // 价格沟通 1050-1100
    const FEATURE_INTER_CITY_CARPOOL_CAP_PRICE  = 1050;  // 价格沟通：远途拼车一口价 rule_type = 17
    const FEATURE_CARPOOL_DUAL_PRICE_RULE       = 1051;  // 价格沟通：两口价 rule_type = 21
    const FEATURE_SPECIAL_RATE_RULE             = 1052;  // 价格沟通：特惠一口价 rule_type = 7
    const FEATURE_PREMIUM_CAP_PRICE_RULE        = 1053;  // 价格沟通：专车一口价 rule_type = 4
    const FEATURE_FAST_CAR_CAP_PRICE_RULE       = 1054;  // 价格沟通：快车一口价 rule_type = 13
    const FEATURE_A_PLUS_CAP_PRICE_RULE         = 1055;  // 价格沟通：A+一口价 rule_type = 14
    const FEATURE_TAXI_CAP_PRICE_RULE           = 1056;  // 价格沟通：出租车一口价 rule_type = 16
}