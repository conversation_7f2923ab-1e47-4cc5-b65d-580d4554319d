<?php

namespace PreSale\Models\carTicket;

use <PERSON>izCom<PERSON>\Constants\OrderNTuple;
use BizL<PERSON>\Utils\Language;
use Nuwa\ApolloSDK\Apollo;

/**
 * 车票业务
 * Class BizCarTicket
 * @package PreSale\Models\carTicket
 */
class BizCarTicket
{

    /**
     * //查询的业务类型。默认是"260,xxx"，快车
     * @var string
     */
    private static $_sCarTypeSelect  = '260';
    const RULE_TYPE_CAR_TICKET_FLASH = '1';   //车票 - 快车文案

    /**
     * // 返回值;
     * {
    "260":{
    "count":1,
    "max_ticket_price":"10",
    "activity_name":"海底捞"
    }
    }
     * @var null
     */
    private static $_aRetQueryRecActivity = null;

    private static $_aRetCarTicketTip = null;

    /**
     * @param string $sTicket     用户登录token
     * @param string $sLat        $sLat
     * @param string $sLng        $sLng
     * @param string $sBusinessId $sBusinessId
     * @return null
     */
    public function queryRecActivity($sTicket, $sLat, $sLng, $sBusinessId) {
        if (empty($sLat) || empty($sLng) || empty($sBusinessId) || empty($sTicket)) {
            return null;
        }

        $aRetActivity = $this->_getRetQueryRecActivity($sTicket, $sLat, $sLng);

        //rpc 车票
        return $aRetActivity[$sBusinessId];
    }

    /**
     * 获取营销文案
     * @param string $sTicket $sTicket
     * @param string $sLat    $sLat
     * @param string $sLng    $sLng
     * @return array|null
     */
    public function getPromoteSalesTip($sTicket, $sLat, $sLng) {
        if (null !== self::$_aRetCarTicketTip) {
            return self::$_aRetCarTicketTip;
        }

        $aRetActivity = $this->_getRetQueryRecActivity($sTicket, $sLat, $sLng);
        if (empty($aRetActivity)) {
            self::$_aRetCarTicketTip = [];
            return self::$_aRetCarTicketTip;
        }

        foreach ($aRetActivity as $iBusiness => $aItemActivity) {
            $this->_initFastCarTip($iBusiness,$aItemActivity);
        }

        return self::$_aRetCarTicketTip;
    }

    /**
     * 获取营销url
     * @return string
     */
    public function getPromoteSalesUrl() {
        return Language::getTextFromDcmp('promote_sales_rule_explain-car_ticket_url');
    }

    /**
     * 初始化快车的tip
     * @param int   $iBusiness     $iBusiness
     * @param array $aItemActivity $aItemActivity
     * @return void
     */
    private function _initFastCarTip($iBusiness, $aItemActivity) {
        if (OrderNTuple::COMMON_PRODUCT_ID_FAST_CAR == $iBusiness) {
            $aReplaceVars = [
                'max_ticket_price' => $aItemActivity['max_ticket_price'],
                'activity_name'    => $aItemActivity['activity_name'],
                'count'            => $aItemActivity['count'],
            ];

            if ($aItemActivity['count'] <= 0) {
                return;
            } elseif (1 == $aItemActivity['count']) {
                self::$_aRetCarTicketTip[self::RULE_TYPE_CAR_TICKET_FLASH] = Language::getTextFromDcmp('promote_sales_rule_explain-single_scene_rule_1_num_equal_1',$aReplaceVars);
            } else {
                self::$_aRetCarTicketTip[self::RULE_TYPE_CAR_TICKET_FLASH] = Language::getTextFromDcmp('promote_sales_rule_explain-single_scene_rule_1_num_more_1',$aReplaceVars);
            }
        }

        return;
    }

    /**
     * 获取rpc的结果，使用静态变量存储
     * @param string $sTicket $sTicket
     * @param string $sLat    $sLat
     * @param string $sLng    $sLng
     * @return array|null
     */
    private function _getRetQueryRecActivity($sTicket, $sLat, $sLng) {
        if (self::$_aRetQueryRecActivity !== null) {
            return self::$_aRetQueryRecActivity;
        }

        if (empty($sTicket) || empty($sLat) || empty($sLng)) {
            \BizLib\Log::info('_getRetQueryRecActivity empty params'.json_encode([$sTicket, $sLat, $sLng]));
            return [];
        }

        //apollo控制放量->加上$_SERVER可以控制只有oMultiEstimatePrice接口才调用展示。预期管理的anycar预估接口无需调用
        if (!Apollo::getInstance()->featureToggle(
            'gs_biz_ticket_show_switch',
            array_merge($_SERVER,['key' => time(), 'from' => 'pre-sale',])
        )->allow()
        ) {
            self::$_aRetQueryRecActivity = [];
            return self::$_aRetQueryRecActivity;
        }

        $aRet = (new \BizLib\Client\BizTicketClient())->queryRecActivity($sTicket, $sLat, $sLng,self::$_sCarTypeSelect);

        if (empty($aRet) || 0 != $aRet['errno'] || empty($aRet['data'])) {
            self::$_aRetQueryRecActivity = [];
            return self::$_aRetQueryRecActivity;
        }

        self::$_aRetQueryRecActivity = $aRet['data'];

        //rpc;
        return self::$_aRetQueryRecActivity;
    }
}
