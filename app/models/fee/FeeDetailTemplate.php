<?php
/**
 * 费用明细（预估）模板model.
 *
 * <AUTHOR>
 * @date 2017/10/23
 */

namespace PreSale\Models\fee;

use BizLib\Config as NuwaConfig;
use BizLib\Utils\Language;
use BizLib\Utils\NumberHelper;
use BizCommon\Utils\Product;

class FeeDetailTemplate
{
    /*
    模板数据结构：
    {
        "page_title":"快车车费预估",
        "title":"普通型",
        "items":[
            {
                "sub_title":"不拼车",
                "need_pay_detail":[
                    {
                        "name":"里程费(29.50公里)",
                        "value":"47.20元",
                        "sub_items":[
                            {
                                "name":"普通时段(29.50公里)",
                                "value":"47.20元"
                            }
                        ]
                    },
                    {
                        "name":"时长费(52分钟)",
                        "value":"45.00元",
                        "sub_items":[
                            {
                                "name":"20:24-21:00(35分钟)",
                                "value":"28.00元"
                            },
                            {
                                "name":"21:00-21:17(17分钟)",
                                "value":"17.00元"
                            }
                        ]
                    },
                    {
                        "name":"远途费(9.48公里)",
                        "value":"7.58元"
                    },
                    {
                        "name":"临时加价0.8倍",
                        "value":"20.00元",
                        "hint":{
                            "bind_key":"value",
                            "content":"当处于高峰时段，周围司机较少，或司机距离您较远的情况下，为了促进成交，鼓励司机更快接单，平台会对订单适当加价，加价金额全部给到司机。为保障乘客的利益与出行体验，加价会封顶不会无限制增加"
                        }
                    }
                ],
                "need_pay":{
                    "total_fee":"预估应付119.78元"
                },
                "real_pay":{
                    "bill_total_fee":119.78,
                    "total_fee":"预估实付<span>${{total_fee}}</span>元",
                    "intros":[
                        "预估费用仅供参考，实际费用可能会因交通情况、天气等因素而不同"
                    ]
                },
                "extra_info":{
                    "is_limit_fee":false,
                    "limit_fee_intro":""
                }
            }
        ]
    }
    */
    const SHAKE_RESULT_SECOND_ESTIMATEBIND_SUCCESS = '2';

    const DEFAULT_COUPON = 1; //最终使用券类型:券系统券
    const MIS_COUPON     = 2; //最终使用券类型:pope券

    private $_aTemplate;

    private $_oCI;

    /**
     * FeeDetailTemplate constructor.
     *
     * @param $aTemplate
     */
    public function __construct($aTemplate = array()) {
        $this->_aTemplate = $aTemplate;
    }

    /**
     * 设置费用明细模板
     *
     * @param $aTemplate
     */
    public function setTemplate($aTemplate) {
        $this->_aTemplate = $aTemplate;
    }

    /**
     * 获取费用明细.
     *
     * @param array $fDiscount
     * @param array $sCurrencyUnit
     * @param array $aActivityInfo
     * @param array $aBills
     *
     * @return array
     */
    public function getFeeDetail($aParams) {
        $fDiscount     = $aParams['discount'];
        $sCurrencyUnit = $aParams['currency_unit'];
        $aActivityInfo = $aParams['activity_info'] ?? [];
        $aBills        = $aParams['bills'] ?? [];
        $aDiscount     = $aParams['discount_list'] ?? [];
        $aNoncarpoolEstimateDetail = $aParams['non_carpool_estimate'] ?? [];
        $sShakeFlag    = $aParams['shake_flag'] ?? '';
        $iProductID    = $aParams['product_id'] ?? 0;
        $fComparePrice = $aParams['compare_price'] ?? 0;

        if (empty($this->_aTemplate)) {
            return array();
        }

        // 由于activity没有根据one_conf拆分存储，而是适应老的预估结构。此处carpool的按照one_conf的方式处理，其余的不动
        // 这个函数处理思路也是清奇，调用处，用传不传$aActivityInfo来区分拼车流程和非拼车流程，!empty($aActivityInfo) 就代表了拼车
        if (!empty($aActivityInfo)) {
            foreach ($this->_aTemplate['items'] as $key => $item) {
                $this->_aTemplate['items'][$key] = $this->_processItem($item, $aActivityInfo[$key], $sCurrencyUnit, $aBills, $sShakeFlag);
            }

            return $this->_aTemplate;
        }

        //处理第一项item
        $aItem0 = $this->_aTemplate['items'][0];
        //出租车产品因政府定价，item0 存放预估价格声明。主要为了兼容H5不做修改。
        if (!isset($this->_aTemplate['items'][0]['real_pay'])) {
            $aItem0 = $this->_aTemplate['items'][1];
        }

        $aFormatDiscount = [];
        if (!empty($aDiscount) && is_array($aDiscount)) {
            $aDiscountColumn = array_column($aDiscount, 'type');
            if (count($aDiscountColumn) == count($aDiscount)) {
                $aFormatDiscount = array_combine($aDiscountColumn, $aDiscount);
            }
        }

        $fDiscountTotal = 0.0;
        //如有，增加折扣项 这个是price算出来的券字段，对于券的子类型文案，需要增加一个互斥逻辑
        if ($fDiscount > 0 && empty($aFormatDiscount['citycard']['amount'])) {
            $fDiscountTotal += $fDiscount;
            $sDiscount       = sprintf('%.1f', $fDiscount);   //价格信息统一展示小数点后一位
            $name            = NuwaConfig::text('config_text', 'activityMis_coupon_msg');
            if (self::SHAKE_RESULT_SECOND_ESTIMATEBIND_SUCCESS == $sShakeFlag) {
                $name = NuwaConfig::text('config_text', 'activityMis_shake_coupon_msg');
            }

            $aItem0['discount'][] = [
                'name'      => $name,
                'value'     => "-{$sDiscount}{$sCurrencyUnit}",
                'sub_items' => [
                    ['name' => $aNoncarpoolEstimateDetail['extra_desc'] ?? '', 'value' => ''],
                ],
            ];
        }

        $aEstimateFeeDetail = NuwaConfig::text('config_text', 'estimate_fee_detail');
        //打车金项
        if (isset($aFormatDiscount['reward']['amount']) && $aFormatDiscount['reward']['amount'] > 0) {
            $fDiscountTotal      += $aFormatDiscount['reward']['amount'];
            $aItem0['discount'][] = [
                'name'  => $aEstimateFeeDetail['reward_title'],
                'value' => Language::replaceTag(
                    $aEstimateFeeDetail['discount_fee_value'],
                    array(
                        'fee'             => NumberHelper::numberFormatDisplay($aFormatDiscount['reward']['amount'], '', 2),
                        'currency_unit'   => $sCurrencyUnit,
                        'currency_symbol' => '',
                    )
                ),
            ];
        }

        //畅行卡项
        if (isset($aFormatDiscount['backcard']['amount']) && $aFormatDiscount['backcard']['amount'] > 0) {
            $fDiscountTotal      += $aFormatDiscount['backcard']['amount'];
            $aItem0['discount'][] = [
                'name'  => $aEstimateFeeDetail['backcard_title'],
                'value' => Language::replaceTag(
                    $aEstimateFeeDetail['discount_fee_value'],
                    array(
                        'fee'             => NumberHelper::numberFormatDisplay($aFormatDiscount['backcard']['amount'], '', 2),
                        'currency_unit'   => $sCurrencyUnit,
                        'currency_symbol' => '',
                    )
                ),
            ];
        }

        //市民卡项
        if (isset($aFormatDiscount['citycard']['amount']) && $aFormatDiscount['citycard']['amount'] > 0) {
            $fDiscountTotal      += $aFormatDiscount['citycard']['amount'];
            $aItem0['discount'][] = [
                'name'  => $aEstimateFeeDetail['citycard_title'],
                'value' => Language::replaceTag(
                    $aEstimateFeeDetail['discount_fee_value'],
                    array(
                        'fee'             => NumberHelper::numberFormatDisplay($aFormatDiscount['citycard']['amount'], '', 2),
                        'currency_unit'   => $sCurrencyUnit,
                        'currency_symbol' => '',
                    )
                ),
            ];
        }

        //信息费(出租车不显示)
        //出租车新增预估详情页，信息费不在discount中
       // if (isset($aFormatDiscount['infofee']['amount']) && $aFormatDiscount['infofee']['amount'] > 0) {
           // $aItem0['discount'][] = [
               // 'name' => '',
               // 'value' => Language::replaceTag($aEstimateFeeDetail['infofee_value'],
                   // [
                       // 'num'             => $aFormatDiscount['infofee']['amount'],
                   // ]
               // ),
               // 'hint'  => [
                   // 'link' => $aEstimateFeeDetail['infofee_msg_url'],
               // ],
           // ];
       // }
        //限时特惠
        if (isset($aFormatDiscount['limittime']['amount']) && $aFormatDiscount['limittime']['amount'] > 0) {
            $fDiscountTotal      += $aFormatDiscount['limittime']['amount'];
            $aItem0['discount'][] = [
                'name'  => $aEstimateFeeDetail['limittime_title'],
                'value' => Language::replaceTag(
                    $aEstimateFeeDetail['discount_fee_value'],
                    array(
                        'fee'             => NumberHelper::numberFormatDisplay($aFormatDiscount['limittime']['amount'], '', 2),
                        'currency_unit'   => $sCurrencyUnit,
                        'currency_symbol' => '',
                    )
                ),
            ];
        }

        //最低消费文案处理
        if ($aItem0['extra_info']['is_limit_fee']) {
            if (isset($aItem0['discount'])) {   //有折扣，最低消费文案放在应付的说明里
                if (!isset($aItem0['need_pay']['intros'])) {
                    $aItem0['need_pay']['intros'] = array();
                }

                array_unshift($aItem0['need_pay']['intros'], $aItem0['extra_info']['limit_fee_intro']);
            } else {    //无折扣，最低消费文案放在实付的说明里
                if (!isset($aItem0['real_pay']['intros'])) {
                    $aItem0['real_pay']['intros'] = array();
                }

                array_unshift($aItem0['real_pay']['intros'], $aItem0['extra_info']['limit_fee_intro']);
            }
        }

        //计算实付
        $fRealPayTotalFee = $aItem0['real_pay']['bill_total_fee'];
        if ($fDiscountTotal > 0) {
            $fRealPayTotalFee -= $fDiscountTotal;
        }

        if ($fRealPayTotalFee < 0) {
            $fRealPayTotalFee = 0;
        }

        //全网预估价**元
        /*
        if (Product::isKFlowerByProductID($iProductID) && $fRealPayTotalFee < $fComparePrice) {
            $aItem0['real_pay']['compare_fee'] = Language::replaceTag(
                $aEstimateFeeDetail['compare_price_text'],
                array(
                    'fee'             => NumberHelper::numberFormatDisplay($fComparePrice, '', 0),
                    'currency_unit'   => $sCurrencyUnit,
                    'currency_symbol' => '',
                )
            );
        }
        */

        $sRealPayTotalFee = sprintf('%.1f', $fRealPayTotalFee); //价格信息统一展示小数点后一位

        //替换模板中的实付项
        $sRealPayTotalFeeText            = str_replace('${{total_fee}}', $sRealPayTotalFee, $aItem0['real_pay']['total_fee']);
        $aItem0['real_pay']['total_fee'] = $sRealPayTotalFeeText;

        //unset item0的无用字段
        unset($aItem0['extra_info']);

        //设置item0
        if (!isset($this->_aTemplate['items'][0]['real_pay'])) {
            $this->_aTemplate['items'][1] = $aItem0;
        } else {
            $this->_aTemplate['items'][0] = $aItem0;
        }

        return $this->_aTemplate;
    }

    private function _processItem($aItem, $aActivity, $sCurrencyUnit, $aBills, $sShakeFlag) {
        $fDiscountTotal = 0.0;

        $aFormatDiscount = [];
        if (!empty($aActivity['discount_desc']) && is_array($aActivity['discount_desc'])) {
            $aDiscountColumn = array_column($aActivity['discount_desc'], 'type');
            if (!empty($aDiscountColumn) && count($aDiscountColumn) == count($aActivity['discount_desc'])) {
                $aFormatDiscount = array_combine($aDiscountColumn, $aActivity['discount_desc']);
            }
        }

        //折扣项
        if (isset($aActivity['estimate_detail']['amount']) && $aActivity['estimate_detail']['amount'] > 0 && empty($aFormatDiscount['citycard']['amount'])) {
            $sDiscount       = sprintf('%.1f', $aActivity['estimate_detail']['amount']);   //价格信息统一展示小数点后一位
            $fDiscountTotal += $aActivity['estimate_detail']['amount'];
            $name            = NuwaConfig::text('config_text', 'activityMis_coupon_msg');
            if (self::SHAKE_RESULT_SECOND_ESTIMATEBIND_SUCCESS == $sShakeFlag) {
                $name = NuwaConfig::text('config_text', 'activityMis_shake_coupon_msg');
            }

            $aDisCountItem = [
                'name'  => $name,
                'value' => "-{$sDiscount}{$sCurrencyUnit}",
            ];
            if (self::isOnlyStudentCardCoupon($aActivity['coupon_info'])
                && !empty($aActivity['estimate_detail']['extra_desc'])
            ) {
                $aDisCountItem['sub_items'] = [
                    ['name' => $aActivity['estimate_detail']['extra_desc'], 'value' => ''],
                ];
            }

            $aItem['discount'][] = $aDisCountItem;
        }

        $aEstimateFeeDetail = NuwaConfig::text('config_text', 'estimate_fee_detail');
        //打车金项
        if (isset($aFormatDiscount['reward']['amount']) && $aFormatDiscount['reward']['amount'] > 0) {
            $fDiscountTotal     += $aFormatDiscount['reward']['amount'];
            $aItem['discount'][] = [
                'name'  => $aEstimateFeeDetail['reward_title'],
                'value' => Language::replaceTag(
                    $aEstimateFeeDetail['discount_fee_value'],
                    array(
                        'fee'             => NumberHelper::numberFormatDisplay($aFormatDiscount['reward']['amount'], '', 1),
                        'currency_unit'   => $sCurrencyUnit,
                        'currency_symbol' => '',
                    )
                ),
            ];
        }

        //畅行卡项
        if (isset($aFormatDiscount['backcard']['amount']) && $aFormatDiscount['backcard']['amount'] > 0) {
            $fDiscountTotal     += $aFormatDiscount['backcard']['amount'];
            $aItem['discount'][] = [
                'name'  => $aEstimateFeeDetail['backcard_title'],
                'value' => Language::replaceTag(
                    $aEstimateFeeDetail['discount_fee_value'],
                    array(
                        'fee'             => NumberHelper::numberFormatDisplay($aFormatDiscount['backcard']['amount'], '', 1),
                        'currency_unit'   => $sCurrencyUnit,
                        'currency_symbol' => '',
                    )
                ),
            ];
        }

        //市民卡项
        if (isset($aFormatDiscount['citycard']['amount']) && $aFormatDiscount['citycard']['amount'] > 0) {
            $fDiscountTotal     += $aFormatDiscount['citycard']['amount'];
            $aItem['discount'][] = [
                'name'  => $aEstimateFeeDetail['citycard_title'],
                'value' => Language::replaceTag(
                    $aEstimateFeeDetail['discount_fee_value'],
                    array(
                        'fee'             => NumberHelper::numberFormatDisplay($aFormatDiscount['citycard']['amount'], '', 1),
                        'currency_unit'   => $sCurrencyUnit,
                        'currency_symbol' => '',
                    )
                ),
            ];
        }

        //限时特惠
        if (isset($aFormatDiscount['limittime']['amount']) && $aFormatDiscount['limittime']['amount'] > 0) {
            $fDiscountTotal     += $aFormatDiscount['limittime']['amount'];
            $aItem['discount'][] = [
                'name'  => $aEstimateFeeDetail['limittime_title'],
                'value' => Language::replaceTag(
                    $aEstimateFeeDetail['discount_fee_value'],
                    array(
                        'fee'             => NumberHelper::numberFormatDisplay($aFormatDiscount['limittime']['amount'], '', 1),
                        'currency_unit'   => $sCurrencyUnit,
                        'currency_symbol' => '',
                    )
                ),
            ];
        }

        //最低消费文案处理
        if ($aItem['extra_info']['is_limit_fee']) {
            if (isset($aItem['discount'])) {   //有折扣，最低消费文案放在应付的说明里
                if (!isset($aItem['need_pay']['intros'])) {
                    $aItem['need_pay']['intros'] = array();
                }

                array_unshift($aItem['need_pay']['intros'], $aItem['extra_info']['limit_fee_intro']);
            } else {    //无折扣，最低消费文案放在实付的说明里
                if (!isset($aItem['real_pay']['intros'])) {
                    $aItem['real_pay']['intros'] = array();
                }

                array_unshift($aItem['real_pay']['intros'], $aItem['extra_info']['limit_fee_intro']);
            }
        }

        //计算实付
        $fRealPayTotalFee = $aItem['real_pay']['bill_total_fee'];
        if ($fDiscountTotal > 0) {
            $fRealPayTotalFee -= $fDiscountTotal;
        }

        if ($fRealPayTotalFee < 0) {
            $fRealPayTotalFee = 0;
        }

        $sRealPayTotalFee = sprintf('%.1f', $fRealPayTotalFee); //价格信息统一展示小数点后一位

        //替换模板中的实付项
        $sRealPayTotalFeeText           = str_replace('${{total_fee}}', $sRealPayTotalFee, $aItem['real_pay']['total_fee']);
        $aItem['real_pay']['total_fee'] = $sRealPayTotalFeeText;

        //unset item0的无用字段
        unset($aItem['extra_info']);
        if (!empty($aBills['carpool']['carpool_fail_fee']) || !empty($aBills['carpool_fail_fee'])) {
            unset($aItem['need_pay']['total_fee']);
        }

        return $aItem;
    }

    /**
     * 只有学生券
     * reserved_b 为student_coupon 是拼快都能用的学生优惠券
     * batchid = 1547264 是 拼车专属的学生优惠券.
     *
     * @param $aCouponInfo
     *
     * @return bool
     */
    public static function isOnlyStudentCardCoupon($aCouponInfo) {
        if (empty($aCouponInfo) || !isset($aCouponInfo['default_coupon']) || empty($aCouponInfo['default_coupon'])) {
            return false;
        }

        if (('student_coupon' == $aCouponInfo['default_coupon']['reserved_b']
            || '1547264' == $aCouponInfo['default_coupon']['batchid'])
            && empty($aCouponInfo['activity_coupon'])
        ) {
            return true;
        }

        return false;
    }

    /**
     * 套餐券
     * reserved_b 为package_mall套餐券
     *
     * @param array $aCouponInfo      券信息
     * @param int   $iFinalCouponType 券类型
     *
     * @return bool
     */
    public static function isPackageMallCoupon($aCouponInfo, $iFinalCouponType) {
        if (empty($aCouponInfo) || !isset($aCouponInfo['default_coupon']) || empty($aCouponInfo['default_coupon'])) {
            return false;
        }

        if ('package_mall' == $aCouponInfo['default_coupon']['reserved_b']
            && self::DEFAULT_COUPON == $iFinalCouponType
        ) {
            return true;
        }

        return false;
    }

    /**
     * 月卡
     * reserved_b 为monthly_card标识
     *
     * @param array $aCouponInfo aCouponInfo
     * @return bool
     */
    public static function isMonthlyCardCoupon($aCouponInfo) {
        if (empty($aCouponInfo) || !isset($aCouponInfo['default_coupon']) || empty($aCouponInfo['default_coupon'])) {
            return false;
        }

        if ('monthly_card' == $aCouponInfo['default_coupon']['reserved_b'] && empty($aCouponInfo['activity_coupon'])
        ) {
            return true;
        }

        return false;
    }

    /**
     * 畅行会员优惠券
     *
     * @param array $aCouponInfo 券信息
     * @return bool
     */
    public static function isPaidMemberCoupon($aCouponInfo) {
        if (empty($aCouponInfo) || !isset($aCouponInfo['default_coupon']) || empty($aCouponInfo['default_coupon'])) {
            return false;
        }

        if ('paid_member' == $aCouponInfo['default_coupon']['reserved_b'] && empty($aCouponInfo['activity_coupon'])
        ) {
            return true;
        }

        return false;
    }
}
