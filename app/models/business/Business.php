<?php
/**
 * Created by PhpStorm.
 * <AUTHOR> <<EMAIL>>
 * Date: 2019/9/19
 * Time: 4:14 PM
 */
namespace PreSale\Models\Business;

use BizLib\Client\CriusClient;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Log;
use BizLib\Utils\UtilHelper;
use BizLib\Utils\Request;

/**
 * Class Business
 * @package PreSale\Models\Business
 */
class Business
{
    const WORK_VERSION = '3.7';
    const DEGRADE_OPEN = true;   //默认不开启企业用户支付降级

    /**
     * 判断是否为企业用户和现在系统中的isvip区分开, 单是判断企业用户channel可以忽略
     * @param string $sPassengerPhone 乘客手机
     * @param int    $iChannel        渠道
     * @param string $sAppVersion     app版本
     *
     * @return bool
     */
    public function isBusinessUser($sPassengerPhone, $iChannel = 0, $sAppVersion = '') {
        return $this->isVip($sPassengerPhone, $iChannel, $sAppVersion);
    }

    /**
     * 检查是否为企业用户(沿用企业那边的命名习惯)
     * @param string $sPassengerPhone 乘客手机
     * @param int    $iChannel        渠道
     * @param string $sAppVersion     app版本
     *
     * @return bool
     */
    public function isVip($sPassengerPhone, $iChannel, $sAppVersion) {
        if (empty($sPassengerPhone)) {
            return false;
        }

        if (!$this->isOpen($iChannel, $sPassengerPhone, $sAppVersion)) {
            $bIsVip = false;
        } else {
            $aResult = (new CriusClient())->isVip(array('passenger_phone' => $sPassengerPhone));
            if (isset($aResult['status']) && GLOBAL_SUCCESS == $aResult['status'] && !empty($aResult['data']['is_vip'])) {
                $bIsVip = true;
            } else {
                $bIsVip = false;
            }
        }

        return $bIsVip;
    }

    /**
     * 过滤渠道和app版本
     * @param int    $iChannel        渠道
     * @param string $sPassengerPhone 乘客手机
     * @param string $sAppVersion     app版本
     *
     * @return bool
     */
    public function isOpen($iChannel, $sPassengerPhone, $sAppVersion) {
        // order channel doc: http://wiki.xiaojukeji.com/index.php?doc-view-917
        //过滤订单列表的channel不走企业支付
        if ($this->isFilterChannel($iChannel)) {
            return false;
        }

        $whiteList = array(
            '***********',
            '***********',
            '***********',
            '***********',
        );

        //默认不降级 当 $bOpen 返回 true == self::DEGRADE_OPEN 时降级
        $bOpen = UtilHelper::getFusingSwitch('business_pay_degrade_switch');
        if (self::DEGRADE_OPEN === $bOpen && (!in_array($sPassengerPhone, $whiteList))) {
            Log::warning(
                Msg::formatArray(
                    Code::E_PASSENGER_NEWORDER_BUSINESS_PAY_DEGRADE,
                    array(
                        'req' => 'channel: '.$iChannel.' passengerPhone: '.$sPassengerPhone,
                    )
                )
            );
            return false;
        }

        if ($this->_checkVersion($sAppVersion)) {
            return true;
        }

        return false;
    }

    /**
     * 判断企业级channel
     * @param int $iChannel channel
     * @return bool
     */
    public function isFilterChannel($iChannel) {
        if (UtilHelper::isB2bChannel($iChannel)) {
            return true;
        }

        return false;
    }

    /**
     * 版本过滤, 版本检查,低版本不支持
     * @param string $sAppVersion app版本
     *
     * @return bool
     */
    private function _checkVersion($sAppVersion) {
        if (empty($sAppVersion)) {
            $sAppVersion = Request::getInstance()->fetchGetPost('appversion', false);
        }

        if (empty($sAppVersion)) {
            $sAppVersion = Request::getInstance()->fetchGetPost('appVersion', false);
        }

        if (UtilHelper::compareAppVersion($sAppVersion, self::WORK_VERSION) < 0) {
            return false;
        }

        return true;
    }
}
