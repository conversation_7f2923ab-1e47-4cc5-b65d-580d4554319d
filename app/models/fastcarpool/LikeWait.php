<?php

namespace PreSale\Models\fastcarpool;

use BizLib\Config as NuwaConfig;
use BizLib\Libraries\RedisDB;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Common as UtilsCommon;

/***************************************************************************
 *
 * Copyright (c) 2014 xiaojukeji.com, Inc. All Rights Reserved
 * <AUTHOR>
 * @desc 拼车愿等项目model
 * @date 2016-01-26
 *
 **************************************************************************/
/**===
 * @property Project $Project
 * @property Passenger $Passenger
 ===*/
class LikeWait
{
    /** @var $redisdb RedisDB */
    public $redisdb;
    const CACHE_TIME            = 7200;
    const REWARD_STATUS_DEFAULT = 0; //未发放
    const REWARD_STATUS_PUT     = 1; //已发放
    const REWARD_STATUS_GET     = 2; //已领取
    const REWARD_VERSION        = '4.1.5';
    const SALES_EXPIRE          = 7200;
    const TYPE_COMBO_CARPOOL    = 4; //标识拼车
    const LIKE_WAIT_VERSION     = '4.2.5'; //愿等需求的版本 待改
    private $_arrLogParams      = array();

    public function __construct() {
    }

    // public function getWaitDiscount($likeWait,$waitDiscount,$floatDiscount,$likeWaitFlag){
    // if(!$likeWait || !$likeWaitFlag){
    // return $floatDiscount;
    // }else{
    // return $floatDiscount>$waitDiscount ? $floatDiscount - $waitDiscount : $floatDiscount;
    // }
//
    // }
//
    // public function getExtraWaitMsg($likeWaitOpenFlag,$estimateFee,$waitDiscount,$waitMinute){
    // $this->load->config('config_carpool',true);
    // $arrCarPool = $this->config->item( 'carpool_wait_msg', 'config_carpool');
    // $extra_price = sprintf('%.1f',$estimateFee*$waitDiscount);
    // if($likeWaitOpenFlag){
    // return array(
    // 'wait_show_msg'=>sprintf($arrCarPool[1],$waitMinute,$extra_price),
    // 'wait_selected_msg'=>sprintf($arrCarPool[2],$waitMinute,$extra_price),
    // 'wait_extra_price'=>$extra_price,
    // 'wait_pop_msg'=>sprintf($arrCarPool[3],$waitMinute,$extra_price),
//
//
    // );
    // }else{
    // return array();
    // }
    // }
    public function getLikeWaitMsg($couponInfo, $likeWait, $waitMinute) {
        $arrCarPool = NuwaConfig::config('config_carpool', 'carpool_wait_msg');
        $arrRet     = array('like_wait' => $likeWait);
        if ($likeWait) {
            if (!empty($couponInfo)) {
                $arrRet['coupon_info']['coupon_value'] = $couponInfo['coupon_value'];
                $arrRet['coupon_info']['tips']         = $arrCarPool['4'];
            } else {
                $arrRet['coupon_info'] = array();
            }

            $arrRet['count_time']      = $waitMinute * 60;
            $arrRet['left_time']       = $waitMinute * 60;
            $arrRet['wait_msg_title']  = $arrCarPool['6'];
            $arrRet['wait_msg_title2'] = $arrCarPool['6'];
        }

        return $arrRet;
    }

    /*
    public function cacheLikeWait($likeWait, $orderId, $waitMinute) {
        if (!empty($likeWait)) {
            if (empty($waitMinute)) {
                $waitMinute = 10;
            }

            $this->redisdb = RedisDB::getInstance();
            $prefix        = UtilsCommon::getRedisPrefix(P_ORDER_LIKE_WAIT_FLAG);
            $strKey        = $prefix.$orderId;
            $re            = $this->redisdb->setex($strKey, 1800, $waitMinute);
            if (!$re) {
                NuwaLog::warning(sprintf('errmsg:cacheLikeWait set fail strKey:%s', $strKey));
            }
        }
    }
    */

    // public function getLikeWaitFlag($orderId){
// $this->load->library('RedisDB');
// $prefix = getRedisPrefix(P_ORDER_LIKE_WAIT_FLAG);
// $strKey = $prefix.$orderId;
// $re = $this->redisdb->get($strKey);
// return $re === false ? 0 : $re;
// }
}
