<?php

namespace PreSale\Models\Member;

use BizCommon\Logics\Order\MemberPrivilegeLogic;

/**
 * Member
 */
class Member
{
    //会员权益是和订单绑定的，而会员不止一个订单，这样是否合适？
    //多个订单，就new多个会员。
    private $_aMemberPrivilege      = null;
    private $_aMultiMemberPrivilege = null;
    private $_aMemberInfo;
    private $_aMemberPrivilegesUsed = null;
    private $_aSelectMemberCoupons;


    //会员极速应答type
    const LICHENG_YUQI_MEMBER_RAPID_TYPE = 'rapid_response';
    //会员快速应答type
    const LICHENG_YUQI_MEMBER_PRIORITY_TYPE = 'priority_response';
    // 专快通用
    const ZHUANKUAI_MEMBER_FAST_TYPE = 'fast_response';

    // 追加特快key
    const ADD_APLUS = 'member_privilege_add_aplus';

    /**
     * setMemberPrivilege
     *
     * @param array $aMemberPrivilege MemberPrivilege
     * @return mixed
     */
    public function setMemberPrivilege($aMemberPrivilege) {
        $this->_aMemberPrivilege = $aMemberPrivilege;
    }

    /**
     * initMemberPrivilegeByOrderFeature
     *
     * @param array $aOrderFeatures OrderFeatures
     * @param bool  $bSingleMock    是否要single mock
     * @return mixed
     */
    public function initMemberPrivilegeByOrderFeature($aOrderFeatures, $bSingleMock = false) {
        if ($bSingleMock) {
            $aOrderFeatures = $this->memberSingleMock($aOrderFeatures);
        }

        // 会员信息
        if (isset($aOrderFeatures['member_level_id']) && !empty($aOrderFeatures['member_level_id'])) {
            $aMemberInfo = [
                'member_level_id'            => $aOrderFeatures['member_level_id'] ?? 0,
                'member_level_name'          => $aOrderFeatures['member_level_name'] ?? '',
                'member_level_icon'          => $aOrderFeatures['member_level_icon'] ?? '',
                'member_level_core_icon'     => $aOrderFeatures['member_level_core_icon'] ?? '',
                'member_privilege_new_order' => $aOrderFeatures['member_privilege_new_order'] ?? '',
            ];
            if (!empty($aOrderFeatures['member_ocean_card'])) {
                $aMemberInfo['member_ocean_card'] = json_decode($aOrderFeatures['member_ocean_card'], true);
            }

            $this->setMemberInfo($aMemberInfo);
        }

        // 专豪徽章会员信息
        if (LuxuryMemberLogic::isLuxuryMember($aMemberInfo['member_privilege_new_order'])) {
            $aMemberInfo['luxury_member_level_id']      = $aMemberInfo['member_privilege_new_order']['luxury_member_level_id'];
            $aMemberInfo['luxury_member_level_name']    = $aMemberInfo['member_privilege_new_order']['luxury_member_level_name'];
            $aMemberInfo['luxury_member_level_icon']    = $aMemberInfo['member_privilege_new_order']['luxury_member_level_icon'];
            $aMemberInfo['luxury_member_expire_at']     = $aMemberInfo['member_privilege_new_order']['luxury_member_expire_at'];
            $aMemberInfo['luxury_member_identity_type'] = $aMemberInfo['member_privilege_new_order']['luxury_member_identity_type'];
            $this->setMemberInfo($aMemberInfo);
        }

        // 会员快速通道权益
        if (isset($aOrderFeatures['member_privilege_new_order']) && !empty($aOrderFeatures['member_privilege_new_order'])) {
            $aMemberPrivilege = json_decode($aOrderFeatures['member_privilege_new_order'], true);
            $this->setMemberPrivilege($aMemberPrivilege);
        }
    }

    /**
     * setMemberPrivilegesUsed
     *
     * @param array $aMemberPrivilegesUsed $aMemberPrivilegesUsed
     * @return mixed
     */
    public function setMemberPrivilegesUsed($aMemberPrivilegesUsed) {
        $this->_aMemberPrivilegesUsed = $aMemberPrivilegesUsed;
    }

    /**
     * setSelectMemberCoupons
     *
     * @param array $aSelectMemberCoupons aSelectMemberCoupons
     * @return mixed
     */
    public function setSelectMemberCoupons($aSelectMemberCoupons) {
        $this->_aSelectMemberCoupons = $aSelectMemberCoupons;
    }

    /**
     * getMemberInfoPrivilege
     *
     * @return array
     */
    public function getMemberInfoPrivilege() {
        $aMemberPrivilege = [];
        if (!empty($this->_aMemberInfo['privileges'])) {
            $aMemberPrivilege = $this->_aMemberInfo['privileges'];
        }

        return $aMemberPrivilege;
    }

    /**
     * setMemberInfo
     *
     * @param mixed $aMemberInfo array $aMemberInfo
     * @return void
     */
    public function setMemberInfo($aMemberInfo) {
        $this->_aMemberInfo = $aMemberInfo;
    }

    /**
     * getMemberInfo
     *
     * @return mixed
     */
    public function getMemberInfo() {
        return $this->_aMemberInfo;
    }

    /**
     * getOrderMemberPrivilege
     *
     * @return null
     */
    public function getOrderMemberPrivilege() {
        return $this->_aMemberPrivilege;
    }

    /**
     * getMemberPrivilegesUsed
     *
     * @return null
     */
    public function getMemberPrivilegesUsed() {
        return $this->_aMemberPrivilegesUsed;
    }

    /**
     * getSelectedMemberCoupons
     *
     * @return mixed
     */
    public function getSelectedMemberCoupons() {
        return $this->_aSelectMemberCoupons;
    }

    /**
     * @param string $sPrivilegeType PrivilegeType
     * @param bool   $bNeedPrefix    NeedPrefix
     * @return mixed|string
     */
    public function getPrivilegeByType($sPrivilegeType, $bNeedPrefix = false) {
        $sPrivilegeName = self::getPrivilegeNameByType($sPrivilegeType, $bNeedPrefix);

        return $this->_aMemberPrivilege[$sPrivilegeName] ?? '';
    }

    /**
     * @param string $sPrivilegeType PrivilegeType
     * @param bool   $bNeedPrefix    NeedPrefix
     * @return mixed|string
     */
    public static function getPrivilegeNameByType($sPrivilegeType, $bNeedPrefix = false) {
        if ($bNeedPrefix) {
            $sPrivilegeName = sprintf('member_privilege_%s', $sPrivilegeType);
        } else {
            $sPrivilegeName = $sPrivilegeType;
        }

        return $sPrivilegeName;
    }

    /**
     * 返回一个会员权益构造体
     *
     * @param array $aMemberPrivilege 权益
     * @return array
     */
    public static function getMemberPrivFastWay($aMemberPrivilege) {
        $aMemberPrivFastWayCoupons = $aMemberPrivilege['member_privilege_fast_way_coupon'] ?? [];
        //这个之所以那么搞，因为会员的query接口白金以下权益不给fast_way数据，只好用getUserOption接口数据
        if (0 == count($aMemberPrivFastWayCoupons)) {
            return null;
        }

        $aMemberPrivFastWay = $aMemberPrivFastWayCoupons[0];

        foreach ($aMemberPrivFastWayCoupons as $aFastWay) {
            if (MemberPrivilegeLogic::fromPaidMember($aMemberPrivilege['member_info'], $aFastWay)) {
                $aMemberPrivFastWay = $aFastWay;
            }

            if (self::LICHENG_YUQI_MEMBER_RAPID_TYPE == $aFastWay['type']) {
                $aMemberPrivFastWay = $aFastWay;
                break;
            }
        }

        return $aMemberPrivFastWay;
    }

    /**
     * @param array $aMultiProductMembers ofs特征
     * @return array
     */
    public function memberSingleMock($aMultiProductMembers) {
        if (!empty($aMultiProductMembers)
            && 1 == count($aMultiProductMembers)
        ) {
            $aCurrentMember = current($aMultiProductMembers);
            $aFields        = [
                'member_level_id',
                'member_level_name',
                'member_level_icon',
                'member_level_core_icon',
                'member_privilege_new_order',
                'member_ocean_card',
            ];

            if (LuxuryMemberLogic::isLuxuryMember($aCurrentMember)) {
                array_push($aFields, 'luxury_member_level_id', 'luxury_member_level_name', 'luxury_member_level_icon', 'luxury_member_expire_at', 'luxury_member_identity_type');
            }

            $aOrderFeatures = [];
            foreach ($aFields as $sField) {
                $aOrderFeatures[$sField] = $aCurrentMember[$sField];
            }

            return $aOrderFeatures;
        }

        return $aMultiProductMembers;
    }
}
