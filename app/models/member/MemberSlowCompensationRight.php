<?php

namespace PreSale\Models\member;

use BizLib\Client\DMemberClient;
use BizLib\Log as NuwaLog;

/**
 * Member
 */
class MemberSlowCompensationRight
{
    //会员慢必赔权益类型
    const PRIVILEGE_TYPE_MEMBER_SLOW_COMPENSATION = 2;
    //领取状态
    const UN_RECEIVED_STATUS = 0;//待领取
    const RECEIVED_STATUS = 1;//已领取
    //使用状态
    const UN_EFFECT_STATUS = 0;//不可使用
    const EFFECT_STATUS = 1;//可使用
    //券类型
    const COUPON_TYPE_REDUCTION = 3;//立减券
    const COUPON_TYPE_DISCOUNT = 100;//折扣券
    //签名信息
    const CALLER = 'api';
    const SECRET = "u9popvuGPt5m7Mbh";
    //psideEstimate中多次调用，用此字段进行标识避免重复调用
    private $_bIsRequested = false;
    private $_aMemberSlowCompensationRightRes;

    private static $_oInstance;

    /**
     * constructor
     */
    private function __construct() {
    }

    /**
     * @return MemberSlowCompensationRight
     */
    public static function getInstance() {
        if (self::$_oInstance == null) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * @param string $sUid 用户uid
     * @param int $iCityId 城市id
     * @return array
     */
    public function initReq($sUid, $iCityId) {
        $aInfo = array(
            'uid'            => $sUid,
            'city_id'        => $iCityId,
            'privilege_type' => self::PRIVILEGE_TYPE_MEMBER_SLOW_COMPENSATION,
        );
        return $aInfo;
    }

    /**
     * @param array $aParams 参数
     * @return array|mixed
     */
    public function getMemberSlowCompensationRight($aParams) {
        $oMemberClient = new DMemberClient();
        $aRet = $oMemberClient->queryPrivilegeReceiveInfo($aParams);
        if (isset($aRet['errno']) && 0 == $aRet['errno']) {
            $aResult = $aRet['data'] ?? [];
            return $aResult;
        }
        return [];
    }



    /**
     *  获取会员慢必赔信息
     * @param string $sUid 用户uid
     * @param int $iCityId 城市id
     * @return array|mixed
     */
    public function getMemberSlowCompensationRightRes($sUid, $iCityId) {
        $aParams= $this->initReq($sUid, $iCityId);
        if ($this->_bIsRequested) {
            return $this->_aMemberSlowCompensationRightRes;
        }
        $this->_aMemberSlowCompensationRightRes = $this->getMemberSlowCompensationRight($aParams);
        //整数的折扣券需要去掉小数位，例：8.0->8,从数据源进行修改
        if ($this->_aMemberSlowCompensationRightRes['coupon_type'] == MemberSlowCompensationRight::COUPON_TYPE_DISCOUNT && $this->_aMemberSlowCompensationRightRes['coupon_amount'] == (int)$this->_aMemberSlowCompensationRightRes['coupon_amount']) {
            $this->_aMemberSlowCompensationRightRes['coupon_amount'] = (int)$this->_aMemberSlowCompensationRightRes['coupon_amount'];
        }
        $this->_bIsRequested = true;
        return $this->_aMemberSlowCompensationRightRes;
    }


    /**
     * 判断是否可用
     * @param array $aMemberSlowCompensationRightRes 入参
     * @return bool
     */
    public function isEffect($aMemberSlowCompensationRightRes) {
        if (empty($aMemberSlowCompensationRightRes)) {
            return false;
        }
        //已领取&可用状态表示可以使用
        if ($aMemberSlowCompensationRightRes['receive_status'] == self::RECEIVED_STATUS && $aMemberSlowCompensationRightRes['effect_status'] == self::EFFECT_STATUS) {
            return true;
        }
        return false;
    }


    /**
     * 判断是否可领取
     * @param array $aMemberSlowCompensationRightRes 入参
     * @return bool
     */
    public function canReceive($aMemberSlowCompensationRightRes) {
        if (empty($aMemberSlowCompensationRightRes)) {
            return false;
        }
        //待领取状态表示可以进行领取
        if ($aMemberSlowCompensationRightRes['receive_status']==self::UN_RECEIVED_STATUS) {
            return true;
        }
        return false;
    }
}

