<?php

namespace PreSale\Models\Member;

use BizLib\Utils\CarLevel;
use BizLib\Utils\Product;

/**
 * AnycarMember
 */
class AnycarMember
{
    private $_aMultiMemberInfo = null;

    private static $_oInstance = null;

    private static $_aPriorities = ['is_auto', 'paid_member', 'default'];

    const ZHUANCHE_PRODUCT           = Product::PRODUCT_ID_DEFAULT.'_'.CarLevel::DIDI_SHUSHI_CAR_LEVEL;
    const ZHUANCHE_BUSSINESS_PRODUCT = Product::PRODUCT_ID_BUSINESS.'_'.CarLevel::DIDI_SHUSHI_CAR_LEVEL;

    const MEMBER_VERSION_V3 = 3;

    /**
     * construct...
     */
    public function __construct() {

    }

    /**
     * 获取单例
     *
     * @return AnycarMember
     */
    public static function getInstance() {
        if (self::$_oInstance === null) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * @param array $aMultiMemberInfo multi member info
     * @return void
     */
    public function setMultiMemberInfo($aMultiMemberInfo) {
        $this->_aMultiMemberInfo = $aMultiMemberInfo;
    }

    /**
     * getMultiMemberInfo
     *
     * @return array
     */
    public function getMultiMemberInfo() {
        return $this->_aMultiMemberInfo;
    }

    /**
     * 将ofs返回的数据转换成anycar会员对象
     *
     * @param array $aOfsData ofs中存储的anycar会员数据
     * @return array group_key为索引的会员信息
     * ['1_100_0':{"member_info":{},"member_privilege_add_aplus":{},"member_privilege_fast_way":{},"member_privilege_dpa":{},"member_privilege_fast_way_coupon":{},"member_privilege_free_wait":{}}]
     */
    public static function translateFromOfsData($aOfsData) {
        if (empty($aOfsData)) {
            return [];
        }

        $aMultiMember = [];
        foreach ($aOfsData as $sGroupKey => $aMemberOfs) {
            $aMemberPrivilegeNewOrder = json_decode($aMemberOfs['member_privilege_new_order'], true);
            if (empty($aMemberPrivilegeNewOrder)) {
                continue;
            }

            $aMultiMember[$sGroupKey] = $aMemberPrivilegeNewOrder;
        }

        return $aMultiMember;
    }

    /** 获取追加aplus元素
     * @return mixed
     */
    public function extractOfsAddAplus() {
        foreach ($this->_aMultiMemberInfo as $aItem) {
            if (!empty($aItem[Member::ADD_APLUS])) {
                return $aItem[Member::ADD_APLUS];
            }
        }
    }
}
