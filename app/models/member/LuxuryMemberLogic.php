<?php

namespace PreSale\Models\Member;

/**
 * LuxuryMemberLogic
 */
class LuxuryMemberLogic
{
    // 权益区分 专豪徽章的目前是 40000001 ～ 40000004
    const PACKAGE_ID_START = 40000001;
    const PACKAGE_ID_END   = 40000004;

    /**
     * 判断是否是专豪徽章会员权益
     * @param array $aPrivilegeInfo 权益信息
     * @return bool
     */
    public static function isLuxuryPrivilege($aPrivilegeInfo) {
        if (isset($aPrivilegeInfo['package_id']) && $aPrivilegeInfo['package_id'] >= self::PACKAGE_ID_START && $aPrivilegeInfo['package_id'] <= self::PACKAGE_ID_END) {
            return true;
        }

        return false;
    }

    /**
     * 判断是否有专豪徽章会员
     * @param array $aOfsMemberInfo 订单特征中的会员信息
     * @return bool
     */
    public static function isLuxuryMember($aOfsMemberInfo) {
        if (isset($aOfsMemberInfo['luxury_member_level_id']) && !empty($aOfsMemberInfo['luxury_member_level_id'])) {
            return true;
        }

        return false;
    }
}
