<?php

namespace PreSale\Models\order;

use BizLib\Log as NuwaLog;
use BizLib\Utils\MapHelper;
use BizLib\Utils\UtilHelper;
/***************************************************************************
 *
 * Copyright (c) 2017 xiaojukeji.com, Inc. All Rights Reserved
 *
 **************************************************************************/
/*
 * @file orderPriorityLineup.php
 *
 * <AUTHOR>
 * @date 2017/10/18
 * @brief
 *  订单优先排队.
 **/
use BizLib\Constants;
use BizCommon\Models\Order\LineUpOrderComModel;
use BizCommon\Models\Order\RemoteOrderComModel;
use BizCommon\Models\Order\CancelOrderComModel;

class OrderPriorityLineup
{
    const CHECK_LAST_ORDER_FINISH_TIME = 300;  // 检查上一笔订单的完成时间：5分钟内
    const CHECK_POSITION_DISTANCE      = 500;    // 检查订单出发点的距离. 500m
    const PASSENGER_DUTY = 1; //乘客责任
    const DRIVER_DUTY    = 2; //司机责任

    /**
     * 是否优先派单(1优先派单、0不优先派单).
     *
     * @var int
     */
    private $_flag = 0;

    /**
     * 基础信息集合.
     *
     * @var array
     */
    private $_basicInfo = [];

    public function __construct() {
    }

    /**
     * 是否 实时排队订单.
     *
     * @param $aOrderInfo
     *
     * @return bool
     */
    private function _isLineUpOrder($aOrderInfo) {
        $oLineUpModel   = new LineUpOrderComModel();
        $bIsLineUpOrder = $oLineUpModel->isLineUpOrder($aOrderInfo['extra_type']);

        $this->_basicInfo['is_line_up_order'] = (int)($bIsLineUpOrder);

        return $bIsLineUpOrder;
    }

    /**
     * 是否 公益出口订单.
     *
     * @param $aOrderInfo
     *
     * @return bool
     */
//    private function _isDonateOrder($aOrderInfo) {
//        $bIsDonateOrder                      = checkOrderExtraType($aOrderInfo['extra_type'], ['o_donate']);
//        $this->_basicInfo['is_donate_order'] = (int) $bIsDonateOrder;
//
//        return $bIsDonateOrder;
//    }

    /**
     * 是否 应急通道订单.
     *
     * @param $aOrderInfo
     *
     * @return bool
     */
    private function _isFastWayOrder($aOrderInfo) {
        $bIsFastWayOrder = UtilHelper::checkOrderExtraType($aOrderInfo['extra_type'], ['o_fastway']);
        $this->_basicInfo['is_fast_way_order'] = (int) $bIsFastWayOrder;

        return $bIsFastWayOrder;
    }

    /**
     * 是否 同为拼车或者同为不拼车相同.
     *
     * @param $aOrderInfo
     * @param $aLastOrderInfo
     *
     * @return bool
     */
    private function _isSameCarpoolType($aOrderInfo, $aLastOrderInfo) {
        $bCurIsCarpool  = UtilHelper::checkOrderExtraType($aOrderInfo['extra_type'], ['o_carpool']) || UtilHelper::checkOrderExtraType($aOrderInfo['extra_type'], ['o_station_carpool']);
        $bLastIsCarpool = UtilHelper::checkOrderExtraType($aLastOrderInfo['extra_type'], ['o_carpool']) || UtilHelper::checkOrderExtraType($aLastOrderInfo['extra_type'], ['o_station_carpool']);

        $bIsSameCarpoolType = ($bCurIsCarpool === $bLastIsCarpool) ? true : false;
        $this->_basicInfo['is_same_carpool_type'] = (int)($bIsSameCarpoolType);

        return $bIsSameCarpoolType;
    }

    /**
     * 是否有一个为 代人叫车 订单.
     *
     * @param $aOrderInfo
     * @param $aLastOrderInfo
     *
     * @return bool
     */
    private function _hasCallCarOrder($aOrderInfo, $aLastOrderInfo) {
        $bIsCallCarOrder     = UtilHelper::isCallCar($aOrderInfo['extra_type']);
        $bIsLastCallCarOrder = UtilHelper::isCallCar($aLastOrderInfo['extra_type']);
        $bHasCallCarOrder    = $bIsCallCarOrder || $bIsLastCallCarOrder;
        $this->_basicInfo['is_call_car_order'] = (int)($bHasCallCarOrder);

        return $bHasCallCarOrder;
    }

    /**
     * 是否 n分钟内的取消订单.
     *
     * @param $aOrderInfo
     *
     * @return bool
     */
    private function _isCancelStatusAndInTime($aOrderInfo) {
        $bIsCancelOrder = false;
        if (in_array(
            $aOrderInfo['order_status'],
            array(
                Constants\OrderSystem::ST_CANCELLED_AFTER_STRIVED,  //抢单后取消（乘客）
                Constants\OrderSystem::ST_RELOAD_FINISH,  //未能完成服务状态
                Constants\OrderSystem::ST_CUSTOMER_SERVICE_CLOSE_ORDER,  //客服关闭
            )
        )
        ) {
            $bIsCancelOrder = true;
        }

        $bInTime = (time() - strtotime($aOrderInfo['finish_time'])) < self::CHECK_LAST_ORDER_FINISH_TIME ? true : false;

        $this->_basicInfo['order_status']     = (int)($aOrderInfo['order_status']);
        $this->_basicInfo['finish_time']      = strtotime($aOrderInfo['finish_time']);
        $this->_basicInfo['current_time']     = time();
        $this->_basicInfo['is_cancel_order']  = (int)($bIsCancelOrder);
        $this->_basicInfo['is_in_valid_time'] = (int)($bInTime);

        return $bIsCancelOrder && $bInTime;
    }

    /**
     * 判责状态，乘客是否有责.
     *
     * @param $aOrderInfo
     *
     * @return int
     */
    private function _isPassengerDuty($aOrderInfo) {
        $iDuty          = $aOrderInfo['duty'] ?? 0;
        $bPassengerDuty = (self::PASSENGER_DUTY === $iDuty) ? true : false;

        $this->_basicInfo['duty'] = (int)($iDuty);
        $this->_basicInfo['is_passenger_duty'] = (int)($bPassengerDuty);

        return $bPassengerDuty;
    }

    /**
     * 产品线/车型是否相同.
     *
     * @param $aOrderInfo
     * @param $aLastOrderInfo
     *
     * @return bool
     */
    private function _isSameRequireLevel($aOrderInfo, $aLastOrderInfo) {
        $bSameProductId = ($aOrderInfo['product_id'] === $aLastOrderInfo['product_id']) ? true : false;
        $bSameLevel     = ($aOrderInfo['require_level'] === $aLastOrderInfo['require_level']) ? true : false;

        $this->_basicInfo['is_same_product_id']    = (int)($bSameProductId);
        $this->_basicInfo['is_same_require_level'] = (int)($bSameLevel);

        return $bSameProductId && $bSameLevel;
    }

    /**
     * 新老订单，上车点半径是否满足.
     *
     * @param $aOrderInfo
     * @param $aLastOrderInfo
     *
     * @return bool
     */
    private function _isValidPosition($aOrderInfo, $aLastOrderInfo) {
        $fCurrentStartLat = $aOrderInfo['starting_lat'];
        $fCurrentStartLng = $aOrderInfo['starting_lng'];

        $fLastOrderStartLat = $aLastOrderInfo['starting_lat'];
        $fLastOrderStartLng = $aLastOrderInfo['starting_lng'];
        $iDistance          = MapHelper::getDistance($fCurrentStartLat, $fCurrentStartLng, $fLastOrderStartLat, $fLastOrderStartLng);

        $bValidDistance = $iDistance < self::CHECK_POSITION_DISTANCE ? true : false;
        $this->_basicInfo['is_in_area'] = (int)($bValidDistance);
        $this->_basicInfo['distance']   = $iDistance;

        return $bValidDistance;
    }

    /**
     * 是否相同的乘客手机号.
     *
     * @param $aOrderInfo
     * @param $aLastOrderInfo
     *
     * @return bool
     */
    private function _isSamePassengerPhone($aOrderInfo, $aLastOrderInfo) {
        $sCurPassengerPhone  = $aOrderInfo['passenger_phone'];
        $sLastPassengerPhone = $aLastOrderInfo['passenger_phone'];

        $bIsSamePassengerPhone = ($sCurPassengerPhone === $sLastPassengerPhone) ? true : false;
        $this->_basicInfo['is_same_passenger_phone'] = (int)($bIsSamePassengerPhone);

        return $bIsSamePassengerPhone;
    }

    /**
     * 是否 远程调度订单.
     *
     * @param $aOrderInfo
     *
     * @return bool
     */
    private function _isRemoteDispatchOrder($aOrderInfo) {
        $aFarOrderInfo          = RemoteOrderComModel::getInstance()->getFarOrderInfo($aOrderInfo);
        $fRemoteDispatchFee     = round($aFarOrderInfo['far_driver_bonus'] ?? 0.0, 2);
        $bIsRemoteDispatchOrder = $fRemoteDispatchFee > 0;
        $this->_basicInfo['is_remote_dispatch_order'] = (int) $bIsRemoteDispatchOrder;

        return $bIsRemoteDispatchOrder;
    }

    /**
     * 是否 同时呼叫anycar异常状态.
     *
     * @param $aAnyCarException
     *
     * @return int
     */
    private function _isBothCallAnyCarException($aAnyCarException) {
        $iBothAnyCarException = 0;
        if (isset($aAnyCarException['exception_time'])) {
            $bInTime = (time() - $aAnyCarException['exception_time']) < 300 ? true : false; // 5分钟内
            if ($bInTime) {
                $iBothAnyCarException = 1;
            }
        }

        $this->_basicInfo['is_both_call_anycar_exception'] = $iBothAnyCarException;

        return $iBothAnyCarException;
    }

    /**
     * 返回flag信息.
     *
     * @return array
     */
    private function _returnFlag() {
        NuwaLog::notice(sprintf('CanceledOrderLineupLogic|getPriorityFlag|flag=%s|basicInfo=%s', $this->_flag, json_encode($this->_basicInfo)));

        return [
            'flag'       => $this->_flag,
            'basic_info' => $this->_basicInfo,
        ];
    }

    /**
     * 从PFS获取上一笔取消订单.
     *
     * @param $iPassengerId
     * @param $sPassengerPhone
     *
     * @return array
     */
    private function _getLastCancelOrderFromPfs($iPassengerId, $sPassengerPhone) {
        $oCancelOrderModel = new CancelOrderComModel();
        $aLastCancelOrder  = $oCancelOrderModel->getLastCancelOrderForPriority($iPassengerId, $sPassengerPhone);

        return [
            'order_info'                 => $aLastCancelOrder['order_info'] ?? [],
            'both_call_anycar_exception' => $aLastCancelOrder['both_call_anycar_exception'] ?? [],
        ];
    }

    /**
     * 检查是否符合 同时呼叫anycar 异常.
     *
     * @param $aBothCallAnyCarException
     *
     * @return bool|int
     */
    private function _checkBothCallAnyCarException($aBothCallAnyCarException) {
        if (empty($aBothCallAnyCarException)) {
            return false;
        }

        $bFlag = $this->_isBothCallAnyCarException($aBothCallAnyCarException);

        return $bFlag;
    }

    /**
     * 检查 优先派规则.
     *
     * @param $aLastCancelOrder
     * @param $aOrderInfo
     *
     * @return bool
     */
    private function _checkRuleForPriority($aLastCancelOrder, $aOrderInfo) {
        if (empty($aLastCancelOrder)) {
            return false;
        }

        $iOrderId = UtilHelper::genHighIntOrderIdV2($aLastCancelOrder['order_id'], $aLastCancelOrder['district']);

        $this->_basicInfo['order_id']            = (int)($iOrderId);
        $this->_basicInfo['low_order_id']        = (int)($aLastCancelOrder['order_id']);
        $this->_basicInfo['passenger_id']        = $aLastCancelOrder['passenger_id'];
        $this->_basicInfo['cur_passenger_id']    = $aOrderInfo['passenger_id'];
        $this->_basicInfo['passenger_phone']     = $aLastCancelOrder['passenger_phone'];
        $this->_basicInfo['cur_passenger_phone'] = $aOrderInfo['passenger_phone'];
        $this->_basicInfo['require_level']       = (int)($aLastCancelOrder['require_level']);
        $this->_basicInfo['product_id']          = (int)($aLastCancelOrder['product_id']);
        $this->_basicInfo['extra_type']          = (int)($aLastCancelOrder['extra_type']);
        $this->_basicInfo['starting_lat']        = (float)($aLastCancelOrder['starting_lat']);
        $this->_basicInfo['starting_lng']        = (float)($aLastCancelOrder['starting_lng']);
        // 取消类型（1乘客取消，2司机取消，3客服关单, 0其他）
        $this->_basicInfo['cancel_type'] = isset($aLastCancelOrder['cancel_type']) ? (int)($aLastCancelOrder['cancel_type']) : 0;

        // 2.优先分单逻辑, 满足如下所有条件
        //  (1) 上一单 实时排队订单
        // $bIsLineUpOrder = $this->_isLineUpOrder($aLastCancelOrder);
        //  (2) 当前订单、上一笔订单 非代人叫车 订单
        // $bhasCallCarOrder = $this->_hasCallCarOrder($aOrderInfo, $aLastCancelOrder);
        //  (3) 上一笔订单：5分钟内的取消订单
        $bIsCancelOrderInTime = $this->_isCancelStatusAndInTime($aLastCancelOrder);

        //  (4) 乘客无责
        $bPassengerDuty = $this->_isPassengerDuty($aLastCancelOrder);

        //  (5) 同一业务线，同一车型
        $bSameRequireLevel = $this->_isSameRequireLevel($aOrderInfo, $aLastCancelOrder);

        //  (6) 上车点半径500m内
        $bValidPosition = $this->_isValidPosition($aOrderInfo, $aLastCancelOrder);

        //  (7) 公益应急通道订单，不优先分单
        // $bIsDonateOrder = $this->_isDonateOrder($aLastCancelOrder);
        //  (8) 同为拼车或者同为不拼车
        $bSameCarPoolType = $this->_isSameCarpoolType($aOrderInfo, $aLastCancelOrder);

        //  (9) 同一手机号（解决企业代叫车，passenger_id相同的问题）
        $bIsSamePassengerPhone = $this->_isSamePassengerPhone($aOrderInfo, $aLastCancelOrder);

        //  (10) 远程调度订单，不优先分单
        // $bIsRemoteDispatchOrder = $this->_isRemoteDispatchOrder($aLastCancelOrder);
        //  (11) 应急通道订单，不优先分单
        // $bIsFastWayOrder = $this->_isFastWayOrder($aLastCancelOrder);
        $bFlag = $bIsCancelOrderInTime
                && !$bPassengerDuty
                && $bSameRequireLevel
                && $bValidPosition
                && $bSameCarPoolType
                && $bIsSamePassengerPhone;

        return $bFlag;
    }

    /**
     * 判断当前订单 是否优先分单.
     *
     * @param $aOrderInfo
     *
     * @return array
     */
    public function getPriorityFlag($aOrderInfo) {
        // 1.从PFS获取上一笔取消订单
        $aLastCancelData          = $this->_getLastCancelOrderFromPfs($aOrderInfo['passenger_id'], $aOrderInfo['passenger_phone']);
        $aLastCancelOrder         = $aLastCancelData['order_info'] ?? [];
        $aBothCallAnyCarException = $aLastCancelData['both_call_anycar_exception'] ?? [];

        $bRuleFlag      = $this->_checkRuleForPriority($aLastCancelOrder, $aOrderInfo);
        $bExceptionFlag = $this->_checkBothCallAnyCarException($aBothCallAnyCarException);

        $bFlag       = $bRuleFlag || $bExceptionFlag;
        $this->_flag = (int)($bFlag);

        return $this->_returnFlag();
    }
}
