<?php

namespace PreSale\Models\order;

use BizLib\Utils\PublicLog;

/***************************************************************************
 *
 * Copyright (c) 2014 xiaojukeji.com, Inc. All Rights Reserved
 * $Id$
 *
 **************************************************************************/
/**===
 * @property businessApi $businessApi
 * @property CityPriceDao $CityPriceDao
 * @property Order $Order
 * @property Passenger $Passenger
 ===*/
class OrderLimitFee
{
    const WORK_VERSIONID      = '3.7';
    const FLAG_SHOW_LIMIT_FEE = 'g_show_limit_fee';
    const FLAG_LIMIT_FEE_NEW  = 'g_limit_fee_new';

    public function __construct() {
    }

    //记录统计日志
    public function writeStatisLog($sCity, $aCarLevel, $sLimitFee = 0, $iPrice = 0, $iOid = 0) {
        $aLogItem['uuid'] = \BizLib\Utils\Request::getInstance()->fetchGetPost('uuid', false);
        //$aLogItem['time'] = time(); //public 日志中自动添加时间
        $aLogItem['imei'] = \BizLib\Utils\Request::getInstance()->fetchGetPost('imei', false);
        $aLogItem['city'] = $sCity;
        $appVersion       = \BizLib\Utils\Request::getInstance()->fetchGetPost('appversion', false);
        if (empty($appVersion)) {
            $appVersion = \BizLib\Utils\Request::getInstance()->fetchGetPost('appVersion', false);
        }

        $aLogItem['appversion']        = $appVersion;
        $aLogItem['require_car_level'] = implode(',', $aCarLevel);
        if (empty($iOid)) {
            $aLogItem['show_tips']      = empty($sLimitFee) ? 0 : 1;
            $aLogItem['limit_fee']      = $sLimitFee;
            $aLogItem['estimate_price'] = $iPrice;
            $aLogItem['opera_stat_key'] = self::FLAG_SHOW_LIMIT_FEE;
        } else {
            $aLogItem['order_id']       = $iOid;
            $aLogItem['opera_stat_key'] = self::FLAG_LIMIT_FEE_NEW;
        }

        PublicLog::writeLogForOfflineCal('public', $aLogItem);
    }
}
