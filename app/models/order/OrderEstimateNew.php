<?php

namespace PreSale\Models\order;

use BizLib\Libraries\RedisDB;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\UtilHelper;
use BizCommon\Models\Bill\EstimateBill;
use BizLib\Constants;
use BizCommon\Logics\Carpool\CarPoolOpenLogic;
use BizCommon\Models\Passenger\Passenger;
use BizCommon\Models\Order\Order;
use BizCommon\Models\BaseModel;

/***************************************************************************
 *
 * Copyright (c) 2014 xiaojukeji.com, Inc. All Rights Reserved
 * <AUTHOR>
 * @date 2015-04-22
 *
 **************************************************************************/
/**===
 * @property Passenger $Passenger
 * @property FastEnsure $FastEnsure
 * @property LikeWait $LikeWait
 * @property CityPrice $CityPrice
 * @property Coupon $Coupon
 * @property CarLevel $CarLevel
 * @property OrderDynamicPrice $OrderDynamicPrice
 * @property PayAbility $PayAbility
 * @property Project $Project
 * @property ActivityMis $ActivityMis
 * @property OrderLimitFee $OrderLimitFee
 * @property EstimateBill $EstimateBill
 * @property Reward $Reward
 * @property Order $Order
 * @property OrderRelationProcess $OrderRelationProcess
 ===*/
class OrderEstimateNew extends BaseModel
{
    /** @var $estimateBill EstimateBill */
    public $estimateBill;
    /** @var $redisdb RedisDB */
    public $redisdb;
    const ESTIMATE_PRICE_REDIS_SUFFIX = '_1';
    const ESTIMATE_TRACE_ID_TO_BILL_PRODUCT_INFO_EXPIRE_TIME = 1800;
    const ZERO     = 0;
    const THOUSAND = 1000; //米和千米换算

    public function __construct() {
        parent::__construct();
    }

    //缓存预估时账单返回的产品线信息
    public function setCacheProductInfoOnBubble($sBubbleTraceId, $sProductInfo) {
        $sKey          = UtilsCommon::getRedisPrefix(P_BUBBLE_CACHE_BILL_PRODUCT_INFO).$sBubbleTraceId;
        $this->redisdb = RedisDB::getInstance();

        return $this->redisdb->setex($sKey, self::ESTIMATE_TRACE_ID_TO_BILL_PRODUCT_INFO_EXPIRE_TIME, $sProductInfo);
    }

    /**
     * 封装拼车获取预估价 先取预估费时的缓存 缓存失效再去调用计价系统
     *
     * @param string $district       区号
     * @param int    $iProductId     产品线id
     * @param int    $isCarPool      是否是拼车
     * @param int    $passengerCount 乘车人数
     *
     * @return array
     */
    public function getCarPoolPrePrice($strDistrict, $iProductId, $passengerCount, $requireLevel, $params, $sEstimateTraceId = '', $sSuffix = self::ESTIMATE_PRICE_REDIS_SUFFIX) {
        $result        = array('cap_price' => 0,);
        $this->redisdb = RedisDB::getInstance();           //引入redis类库

        $strKey = '';
        // 先使用 traceid 当 key 查一下，这是新预估接口的做法，如果查不到，再用老的方式
        if (!empty($sEstimateTraceId)) {
            if ('_1' == $sSuffix) {
                $strKey = UtilsCommon::getRedisPrefix(P_CARPOOL_ESTIMATE_PRICE).$sEstimateTraceId;
            } elseif ('_2' == $sSuffix) {
                $strKey = UtilsCommon::getRedisPrefix(P_NONCARPOOL_ESTIMATE_PRICE).$sEstimateTraceId;
            }
        }

        if (!empty($strKey) && $ret = $this->redisdb->get($strKey)) {
            $sEstimatepriceList = json_decode($ret, true);
        } else {
            $strKeyPre = UtilsCommon::getRedisPrefix(P_CARPOOL_ESTIMATE_PRICE);
            $strKey    = $strKeyPre.md5($params['imei'].'_'.round($params['flng'], 3).'_'.round($params['flat'], 3).'_'.round($params['tlng'], 3).'_'.round($params['tlat'], 3)).'_'.$iProductId.'_'.$strDistrict.$sSuffix;
            if ($ret = $this->redisdb->get($strKey)) {
                $sEstimatepriceList = json_decode($ret, true);
            }
        }

        if (isset($sEstimatepriceList['detail'], $sEstimatepriceList['start_dest_distance'], $sEstimatepriceList['order_time_cost'])) {
            $initDetailFeeArray = array(
                'start_price'     => 0,
                'normal_price'    => 0,
                'time_price'      => 0,
                'low_speed_price' => 0,
                'night_price'     => 0,
                'empty_price'     => 0,
            );
            $pre_totle_fee      = isset($sEstimatepriceList['total_fee'][$requireLevel]) ? $sEstimatepriceList['total_fee'][$requireLevel] : 0;
            $cap_price          = isset($sEstimatepriceList['result'][$requireLevel]) ? $sEstimatepriceList['result'][$requireLevel] : 0;
            $disCountArr        = isset($sEstimatepriceList['discount'][$requireLevel]['driver_first'], $sEstimatepriceList['discount'][$requireLevel]['driver_second']) ? $sEstimatepriceList['discount'][$requireLevel] : array('driver_first' => 0, 'driver_second' => 0);
            $pre_detail_fee     = isset($sEstimatepriceList['detail'][$requireLevel]) ? $sEstimatepriceList['detail'][$requireLevel] : array();
            $result = array(
                'passenger_count'     => isset($sEstimatepriceList['passenger_count']) ? $sEstimatepriceList['passenger_count'] : 2,
                'open_pool_seat'      => isset($sEstimatepriceList['open_pool_seat']) ? $sEstimatepriceList['open_pool_seat'] : false,
                'pool_seat_discount'  => array(
                    'one_dynamic_discount'   => isset($sEstimatepriceList['discount'][$requireLevel]['one_dynamic_discount']) ? $sEstimatepriceList['discount'][$requireLevel]['one_dynamic_discount'] : 0,
                    'two_dynamic_discount'   => isset($sEstimatepriceList['discount'][$requireLevel]['two_dynamic_discount']) ? $sEstimatepriceList['discount'][$requireLevel]['two_dynamic_discount'] : 0,
                    'three_dynamic_discount' => isset($sEstimatepriceList['discount'][$requireLevel]['three_dynamic_discount']) ? $sEstimatepriceList['discount'][$requireLevel]['three_dynamic_discount'] : 0,
                ),
                'open_carpool_succ'   => isset($sEstimatepriceList['open_carpool_succ']) ? $sEstimatepriceList['open_carpool_succ'] : false,
                'start_dest_distance' => $sEstimatepriceList['start_dest_distance'],
                'order_time_cost'     => $sEstimatepriceList['order_time_cost'],
                'pre_total_fee'       => $pre_totle_fee,
                'pre_detail_fee'      => array_merge($initDetailFeeArray, $pre_detail_fee),
                'cap_price'           => (float)($cap_price),
                'discount'            => array(
                    'driver_first'  => (float)($disCountArr['driver_first']),
                    'driver_second' => (float)($disCountArr['driver_second']),
                ),
                'station_id'          => isset($sEstimatepriceList['station_id']) ? $sEstimatepriceList['station_id'] : '',
                'price_discount'      => isset($sEstimatepriceList['price_discount']) ? $sEstimatepriceList['price_discount'] : 0,
                'dynamic_discount'    => isset($sEstimatepriceList['dynamic_discount']) ? $sEstimatepriceList['dynamic_discount'] : 0,
                'discount_type'       => isset($sEstimatepriceList['discount_type']) ? $sEstimatepriceList['discount_type'] : 0,
                'wait_minute'         => isset($sEstimatepriceList['wait_minute']) ? $sEstimatepriceList['wait_minute'] : 10,
            );
        }

        return $result;
    }

    /**
     * 获取anycar的预估价格缓存.
     *
     * @param $sEstimateTraceId
     *
     * @return array|mixed
     */
    public function getAnyCarPrePriceFromCache($sEstimateTraceId) {
        $aEstimatePrice = [];
        if (empty($sEstimateTraceId)) {
            return $aEstimatePrice;
        }

        $this->redisdb = RedisDB::getInstance();           //引入redis类库

        $sKey           = UtilsCommon::getRedisPrefix(P_ANYCAR_ESTIMATE_PRICE).$sEstimateTraceId;
        $aRet           = $this->redisdb->get($sKey);
        $aEstimatePrice = json_decode($aRet, true);

        return $aEstimatePrice;
    }

    /**
     * @param string $sTraceId
     * @param int    $iRequireLevel
     * @param array  $aEstimatedArrival
     *
     * @return mixed
     */
    public function cacheEstimatedArrival($sTraceId, $iRequireLevel, $aEstimatedArrival) {
        $sKey     = UtilsCommon::getRedisPrefix(P_CARPOOL_ESTIMATE_ARRIVAL).$sTraceId.'_'.$iRequireLevel;
        $oRedisdb = \PreSale\Logics\redismove\RedisWrapper::getInstance(P_CARPOOL_ESTIMATE_ARRIVAL);

        return $oRedisdb->setex($sKey, self::ESTIMATE_TRACE_ID_TO_BILL_PRODUCT_INFO_EXPIRE_TIME, json_encode($aEstimatedArrival));
    }

    /**
     * @param string $sTraceId
     * @param int    $iRequireLevel
     *
     * @return array|mixed
     */
    public function getEstimatedArrivalCache($sTraceId, $iRequireLevel) {
        $sKey     = UtilsCommon::getRedisPrefix(P_CARPOOL_ESTIMATE_ARRIVAL).$sTraceId.'_'.$iRequireLevel;
        $oRedisdb = \PreSale\Logics\redismove\RedisWrapper::getInstance(P_CARPOOL_ESTIMATE_ARRIVAL);
        $sCache   = $oRedisdb->get($sKey);
        $aEstimatedArrival = json_decode($sCache, true);
        if (!is_array($aEstimatedArrival)) {
            return [];
        }

        return $aEstimatedArrival;
    }

    private function _genPositionInfo($metre, $minute) {
        $positionInfo = array(
            'info'   => array('error' => 0,),
            'detail' => array(
                'distance' => isset($metre) ? (float)($metre) : 0.0,
                'time'     => isset($minute) ? (int)($minute) : 0,
            ),
        );

        return $positionInfo;
    }

    /**
     * @param string $sEstimateId EstimateId
     * @param array  $aExtResult  ExtResult
     * @return mixed
     */
    public function cacheExtResult($sEstimateId, $aExtResult) {
        if (empty($aExtResult)) {
            return;
        }

        $sKey          = UtilsCommon::getRedisPrefix('p_carpool_ext_info').$sEstimateId;
        $this->redisdb = RedisDB::getInstance();
        $this->redisdb->setex(
            $sKey,
            self::ESTIMATE_TRACE_ID_TO_BILL_PRODUCT_INFO_EXPIRE_TIME,
            json_encode($aExtResult)
        );
        return;
    }

    /**
     * @param string $sEstimateId  EstimateId
     * @param string $miniBusToken miniBusToken
     * @return mixed
     */
    public function cacheMiniBusTokenResult($sEstimateId, $miniBusToken) {
        if (empty($miniBusToken) || empty($sEstimateId)) {
            return;
        }

        $sKey          = UtilsCommon::getRedisPrefix('p_carpool_minibus_map_token').$sEstimateId;
        $this->redisdb = RedisDB::getInstance();
        $this->redisdb->setex(
            $sKey,
            self::ESTIMATE_TRACE_ID_TO_BILL_PRODUCT_INFO_EXPIRE_TIME,
            $miniBusToken
        );
        return;
    }

    /**
     * @param string $sEstimateId EstimateId
     * @return mixed
     */
    public function getMiniBusTokenResult($sEstimateId) {
        if (empty($sEstimateId)) {
            return;
        }

        $this->redisdb = RedisDB::getInstance();
        return $this->redisdb->get(UtilsCommon::getRedisPrefix('p_carpool_minibus_map_token').$sEstimateId);

    }

    /**
     * @param string $sEstimateId EstimateId
     * @param array  $miniBusInfo miniBusInfo
     * @return mixed
     */
    public function cacheMiniBusInfoResult($sEstimateId, $miniBusInfo) {
        if (empty($miniBusInfo) || empty($sEstimateId)) {
            return;
        }

        $sKey          = UtilsCommon::getRedisPrefix('p_carpool_minibus_info').$sEstimateId;
        $this->redisdb = RedisDB::getInstance();
        $this->redisdb->setex(
            $sKey,
            self::ESTIMATE_TRACE_ID_TO_BILL_PRODUCT_INFO_EXPIRE_TIME,
            json_encode($miniBusInfo)
        );
        return;
    }

    /**
     * @param string $sEstimateId EstimateId
     * @return mixed
     */
    public function getMiniBusInfoResult($sEstimateId) {
        if (empty($sEstimateId)) {
            return;
        }

        $this->redisdb = RedisDB::getInstance();
        $sInfo         = $this->redisdb->get(UtilsCommon::getRedisPrefix('p_carpool_minibus_info').$sEstimateId);
        $aMiniBusInfo  = json_decode($sInfo, true);
        if (!is_array($aMiniBusInfo)) {
            return [];
        }

        return $aMiniBusInfo;

    }
}
