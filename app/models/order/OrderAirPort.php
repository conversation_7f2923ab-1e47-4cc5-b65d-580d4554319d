<?php

namespace PreSale\Models\order;

use BizLib\Libraries\RedisDB;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Common as UtilsCommon;
/**===
===*/
use BizCommon\Models\BaseModel;

class OrderAirPort extends BaseModel
{
    /** @var $redisdb RedisDB */
    public $redisdb;
    const TRACEID_COMBO_TYPE_CACHE_TIME = 180;

    public function __construct() {
        parent::__construct();
    }

    /**
     * @desc 根据trace id判断是否接机单（从缓存）
     *
     * @param $sTraceId
     *
     * @return array{
    'combo_type'
     * }
     */
    public function getAirPortInfoByTraceId($sTraceId) {
        $aAirportInfo = array();
        if (empty($sTraceId)) {
            return $aAirportInfo;
        }

        $this->redisdb = RedisDB::getInstance();
        $sKey          = UtilsCommon::getRedisPrefix(P_TRACEID_ESTIMATE_COMBOTYPE).$sTraceId;
        $sAirportInfoCache = $this->redisdb->get($sKey);
        $aAirportInfo      = json_decode($sAirportInfoCache, true);
        if (!$aAirportInfo || !is_array($aAirportInfo)) {
            return array();
        }

        NuwaLog::warning('get cache P_TRACEID_ESTIMATE_COMBOTYPE ,key:'.$sKey.', ret:'.$sAirportInfoCache);

        return $aAirportInfo;
    }

    /**
     * @desc 设置trace id判断是否接机单
     *
     * @param $sTraceId
     * @param $aAirportInfo
     *
     * @return bool
     */
    public function setAirPortInfoByTraceId($sTraceId, $aAirportInfo) {
        if (empty($sTraceId) || empty($aAirportInfo)) {
            return false;
        }

        $aAirportInfo  = json_encode($aAirportInfo);
        $this->redisdb = RedisDB::getInstance();
        $sKey          = UtilsCommon::getRedisPrefix(P_TRACEID_ESTIMATE_COMBOTYPE).$sTraceId;
        $boolResult    = $this->redisdb->setex($sKey, self::TRACEID_COMBO_TYPE_CACHE_TIME, $aAirportInfo);

        return $boolResult;
    }
}
