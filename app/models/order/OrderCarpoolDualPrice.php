<?php
/**
 * desc:处理拼车两口价相关逻辑
 * Author: feng<PERSON><PERSON>@didiglobal.com
 * Time: 2019/3/1 上午11:19.
 */

namespace PreSale\Models\order;

use BizCommon\Constants\Carpool;
use BizLib\Utils\Language;
use BizLib\Config as NuwaConfig;
use BizLib\Log;
use PreSale\Logics\estimatePrice\multiResponse\component\priceDescInfo\Common;
use BizLib\Utils\NumberHelper;
use PreSale\Logics\estimatePrice\multiResponse\MainDataRepo;
use BizLib\Utils\UtilHelper;
use BizCommon\Utils\Horae;
use PreSale\Logics\estimatePrice\CarpoolCommuteCardLogic;

class OrderCarpoolDualPrice
{

    /**
     * @desc是否两口价v2
     * @param array $aItem anycar里面的一组
     * @return bool
     */
    public function isNewDualCarpoolPriceBubble(array $aItem) {
        return $aItem['carpool_price_type'] ?? 0;
    }

    /**
     * @desc是否两口价
     * @param array $aItem anycar里面的一组
     * @return bool
     */
    public function isCarpoolDualPrice(array $aItem) {
        return $aItem['is_dual_carpool_price'] ?? 0;
    }

    /**
     * @desc carpool dual price check
     * @param array $aInfo aInfo
     * @return bool
     */
    public function isCarpoolDualPriceDisplayOpen($aInfo) {
        if (!Horae::isCarpoolUnSuccessFlatPrice($aInfo) && !Horae::isCarpoolUnSuccessFlatPrice($aInfo['order_info']['n_tuple'])) {
            return false;
        }

        return true;
    }

    /**
     * @desc get dual price after coupon price
     * @param array $aConfirmProductEstimateInfo aConfirmProductEstimateInfo
     * @return array|bool
     */
    public function getEstimateDualPriceWithCoupons($aConfirmProductEstimateInfo) {
        if (empty($aConfirmProductEstimateInfo) || !isset($aConfirmProductEstimateInfo['activity_info'])) {
            Log::warning('get carpool Dual Price Activity Info fail');

            return false;
        }

        if (2 !== count($aConfirmProductEstimateInfo['activity_info'])) {
            Log::warning('get carpool Dual Price Estimate Info fail');

            return false;
        }

        if (empty($aConfirmProductEstimateInfo['activity_info'][0]['estimate_fee'])
            || empty($aConfirmProductEstimateInfo['activity_info'][1]['estimate_fee'])
        ) {
            return false;
        }

        return [$aConfirmProductEstimateInfo['activity_info'][0]['estimate_fee'], $aConfirmProductEstimateInfo['activity_info'][1]['estimate_fee']];
    }

    /**
     * @desc get dual price coupon info
     * @param array $aActivityInfo aActivityInfo
     * @return array
     */
    public function getCouponsFromEstimateActivityInfo($aActivityInfo) {
        $aCoupons = [];
        if (empty($aActivityInfo[0]['discount_desc']) && empty($aActivityInfo[1]['discount_desc'])) {
            return $aCoupons;
        }

        $aCoupons = [
            [
                'amount' => $this->_getSumDiscountAmount($aActivityInfo[0]['discount_desc']),
            ],
            [
                'amount' => $this->_getSumDiscountAmount($aActivityInfo[1]['discount_desc']),
            ],
        ];

        return $aCoupons;
    }

    /**
     * @desc get dual price coupon info
     * @param array $aCouponsInfo   aCouponsInfo
     * @param array $aDiscountsBill aDiscountsBill
     * @return array
     */
    public function getCouponsFromEstimateDiscountsBill($aCouponsInfo, $aDiscountsBill) {
        if (empty($aDiscountsBill['normal']['discounts_total']) && empty($aDiscountsBill['carpool_fail']['discounts_total'])) {
            return $aCouponsInfo;
        }

        $aCoupons = [
            [
                'amount' => abs($aDiscountsBill['normal']['discounts_total'] ?? 0) + ($aCouponsInfo[0]['amount'] ?? 0),
            ],
            [
                'amount' => abs($aDiscountsBill['carpool_fail']['discounts_total'] ?? 0) + ($aCouponsInfo[1]['amount'] ?? 0),
            ],
        ];

        return $aCoupons;
    }

    /**
     * @desc compute coupon
     * @param array $aDiscountItem aDiscountItem
     * @return float
     */
    private function _getSumDiscountAmount($aDiscountItem) {
        $fAmount = 0;
        if (empty($aDiscountItem) || !is_array($aDiscountItem)) {
            return $fAmount;
        }

        foreach ($aDiscountItem as $aItem) {
            $fAmount += $aItem['amount'];
        }

        return $fAmount;
    }

    /**
     * @desc anycar add product estimate dual price msg
     * @param array $aOrderItem      aOrderItem
     * @param bool  $bHighLightPrice 是否大写
     * @return array
     */
    public function processAnyEstimateDisplay($aOrderItem, $bHighLightPrice) {
        $aTextConfig = NuwaConfig::text('config_text', 'pAnycarEstimate');

        if (Carpool::CARPOOL_DAY == $aOrderItem['pricing_type'] && 0 == $aOrderItem['price']) {
            $sCarpoolSuccDesc = Language::replaceTag(
                $aTextConfig['carpool_dual_price_succ_desc_carpool_day'],
                [
                    'carpool_success_price' => $aOrderItem['price'],
                ]
            );
        } else {
            if ($bHighLightPrice) {
                $sCarpoolSuccDesc = Language::replaceTag(
                    $aTextConfig['carpool_dual_price_succ_desc_highlight'],
                    [
                        'carpool_success_price' => $aOrderItem['price'],
                    ]
                );
            } else {
                $sCarpoolSuccDesc = Language::replaceTag(
                    $aTextConfig['carpool_dual_price_succ_desc'],
                    [
                        'carpool_success_price' => $aOrderItem['price'],
                    ]
                );
            }
        }

        if ($this->isNewDualCarpoolPriceBubble($aOrderItem)) {
            $sCarpoolFailDesc = $aTextConfig['carpool_new_dual_price_desc'];
        } else {
            if ($bHighLightPrice) {
                $sCarpoolFailDesc = Language::replaceTag(
                    $aTextConfig['carpool_dual_price_fail_desc_highlight'],
                    [
                        'carpool_fail_price' => $aOrderItem['carpool_fail_discount_fee'],

                    ]
                );
            } else {
                $sCarpoolFailDesc = Language::replaceTag(
                    $aTextConfig['carpool_dual_price_fail_desc'],
                    [
                        'carpool_fail_price' => $aOrderItem['carpool_fail_discount_fee'],

                    ]
                );
            }
        }

        //使用省钱卡的展示文案
        if (CarpoolCommuteCardLogic::getInstance()->getExtInfoByEstimateID($aOrderItem['estimate_id'])) {
            $sCarpoolSuccDesc = Language::replaceTag(
                $aTextConfig['carpool_dual_price_succ_desc_carpool_card'],
                [
                    'carpool_success_price' => $aOrderItem['price'],
                ]
            );
            $sCarpoolFailDesc = Language::replaceTag(
                $aTextConfig['carpool_dual_price_fail_desc_carpool_card'],
                [
                    'carpool_fail_price' => $aOrderItem['carpool_fail_discount_fee'],

                ]
            );
        }

        return [$sCarpoolSuccDesc, $sCarpoolFailDesc];
    }

    /**
     * desc anycar estimate dual price msg
     * @param array  $aOrderItem    aOrderItem
     * @param string $sCurrencyUnit sCurrencyUnit
     * @return array
     */
    public function anycarBubbleDisplay($aOrderItem, $sCurrencyUnit) {
        $aMultiPriceDesc = [];
        $fSuccCoupon     = $aOrderItem['coupon_amount'] ?? 0;
        $fFailCoupon     = $aOrderItem['carpool_fail_coupon_amount'] ?? 0;
        //分层定价
        $fSuccCoupon = $fSuccCoupon + abs($aOrderItem['discount_bills']['normal']['discounts_total'] ?? 0);
        $fFailCoupon = $fFailCoupon + abs($aOrderItem['discount_bills']['carpool_fail']['discounts_total'] ?? 0);

        $fPriceValueSucc = $aOrderItem['price'];
        $fPriceValueFail = $aOrderItem['carpool_fail_discount_fee'];

        $aAnyCarText        = NuwaConfig::text('config_text', 'anycar');
        $sTextKeyPrice      = 'anycar_carpool_dual_price_with_seat';
        $sTextKeyCouponSucc = 'anycar_carpool_dual_price_coupon_succ_desc';
        $sTextKeyCouponFail = 'anycar_carpool_dual_price_coupon_fail_desc';
        $sPriceDesc         = Language::replaceTag(
            $aAnyCarText[$sTextKeyPrice],
            [
                'amount_succ'   => $fPriceValueSucc,
                'amount_fail'   => $fPriceValueFail,
                'currency_unit' => $sCurrencyUnit,
            ]
        );

        $fPriceValue = ($fPriceValueSucc <= $fPriceValueFail) ? $fPriceValueSucc : $fPriceValueFail;
        if (Horae::isCarpoolUnsuccRealTimePrice($aOrderItem) || Horae::isCarpoolUnsuccRealTimePrice($aOrderItem['order_info']['n_tuple'] ?? [])) {
            // 未拼成实时计价时, 不用比大小
            $fPriceValue = $fPriceValueSucc;
        }

        if (!$this->isCarpoolDualPriceDisplayOpen($aOrderItem)) {
            return [$fPriceValue, $sPriceDesc, $aMultiPriceDesc];
        }

        if (!empty($fSuccCoupon)) {
            $aCouponSuccDesc[] = Language::replaceTag(
                $aAnyCarText[$sTextKeyCouponSucc],
                [
                    'amount'        => $fSuccCoupon,
                    'currency_unit' => $sCurrencyUnit,
                ]
            );
        }

        if (!empty($fFailCoupon)) {
            $aCouponFailDesc[] = Language::replaceTag(
                $aAnyCarText[$sTextKeyCouponFail],
                [
                    'amount'        => $fFailCoupon,
                    'currency_unit' => $sCurrencyUnit,
                ]
            );
        }

        $aTextConfig     = NuwaConfig::text('config_text', 'getEstimateFeeInfo');
        $aMultiPriceDesc = [
            [
                'estimate_fee' => $fPriceValueFail,
                'price_desc'   => !empty($aCouponFailDesc) ? implode(',', $aCouponFailDesc) : '',
                'fee_msg'      => Language::replaceTag(
                    $aTextConfig['carpool_fail_price_desc'],
                    [
                        'carpool_fail_price' => $fPriceValueFail,
                        'currency_unit'      => $sCurrencyUnit,
                    ]
                ),
            ],
            [
                'estimate_fee' => $fPriceValueSucc,
                'price_desc'   => !empty($aCouponSuccDesc) ? implode(',', $aCouponSuccDesc) : '',
                'fee_msg'      => Language::replaceTag(
                    $aTextConfig['carpool_success_price_desc'],
                    [
                        'carpool_success_price' => $fPriceValueSucc,
                        'currency_unit'         => $sCurrencyUnit,
                    ]
                ),
            ],
        ];

        return [$fPriceValue, $sPriceDesc, $aMultiPriceDesc];
    }

    /**
     * anycar下拼车两口价展示
     * @param array  $aOrderItem    anycar 拼车项
     * @param string $sCurrencyUnit 钱单位
     * @return array
     */
    public function anyCarBubbleDetailPage($aOrderItem, $sCurrencyUnit) {
        $fSuccCoupon = $aOrderItem['coupon_amount'] ?? 0;
        $fFailCoupon = $aOrderItem['carpool_fail_coupon_amount'] ?? 0;

        $fPriceValueSucc = $aOrderItem['price'];
        $fPriceValueFail = $aOrderItem['carpool_fail_discount_fee'];
        $aAnyCarText     = NuwaConfig::text('config_text', 'anycar');

        if ($this->isNewDualCarpoolPriceBubble($aOrderItem)) {
            $sPriceDesc = Language::replaceTag(
                $aAnyCarText['anycar_carpool_new_dual_price_for_detail'],
                [
                    'amount_succ'   => (float)($aOrderItem['price']),
                    'currency_unit' => $sCurrencyUnit,
                ]
            );
        } else {
            $sPriceDesc = Language::replaceTag(
                $aAnyCarText['anycar_carpool_dual_price_for_detail'],
                [
                    'amount_succ'   => $fPriceValueSucc,
                    'amount_fail'   => $fPriceValueFail,
                    'currency_unit' => $sCurrencyUnit,
                ]
            );
        }

        $sTextKeyCouponMerge = 'anycar_carpool_dual_price_coupon_merge_desc_for_detail';
        $sTextKeyCouponSucc  = 'anycar_carpool_dual_price_coupon_succ_desc_for_detail';
        $sTextKeyCouponFail  = 'anycar_carpool_dual_price_coupon_fail_desc_for_detail';
        $sCouponAmountDesc   = '';
        if (!empty($fSuccCoupon) || !empty($fFailCoupon)) {
            $aCouponDesc = [];
            if ($fSuccCoupon == $fFailCoupon) {
                $aCouponDesc[] = Language::replaceTag(
                    $aAnyCarText[$sTextKeyCouponMerge],
                    [
                        'amount'        => $fSuccCoupon,
                        'currency_unit' => $sCurrencyUnit,
                    ]
                );
            } else {
                if (!empty($fSuccCoupon)) {
                    $aCouponDesc[] = Language::replaceTag(
                        $aAnyCarText[$sTextKeyCouponSucc],
                        [
                            'amount'        => $fSuccCoupon,
                            'currency_unit' => $sCurrencyUnit,
                        ]
                    );
                }

                if (!empty($fFailCoupon)) {
                    $aCouponDesc[] = Language::replaceTag(
                        $aAnyCarText[$sTextKeyCouponFail],
                        [
                            'amount'        => $fFailCoupon,
                            'currency_unit' => $sCurrencyUnit,
                        ]
                    );
                }
            }

            $sCouponAmountDesc = implode('/', $aCouponDesc);
        }

        return [$sPriceDesc, $sCouponAmountDesc];
    }


    /*
     * 老端和报价单失败，会走这个兜底，fuck!!!!!
     */
    public function getFeeDescforAnycarDetailPageOld($aOrderItem, $sCurrencyUnit) {
        $sPriceDesc = $sCouponDesc = '';
        $aText      = NuwaConfig::text('config_text', 'anycar');

        $fSuccCoupon = $aOrderItem['coupon_amount'] ?? 0;
        $fFailCoupon = $aOrderItem['carpool_fail_coupon_amount'] ?? 0;

        $sPriceDesc = Language::replaceTag(
            $aText['anycar_carpool_dual_price_for_detail'],
            [
                'amount_succ'   => $aOrderItem['price'],
                'amount_fail'   => (float)(bcsub($aOrderItem['carpool_fail_fee'], $fFailCoupon, 1)),
                'currency_unit' => $sCurrencyUnit,
            ]
        );
        if (empty($fFailCoupon) || empty($fSuccCoupon)) {
            return [$sPriceDesc, $sCouponDesc];
        }

        $aCouponDesc = [];
        if ($fSuccCoupon == $fFailCoupon) {
            $aCouponDesc[] = Language::replaceTag(
                $aText['anycar_carpool_dual_price_coupon_merge_desc_for_detail'],
                [
                    'amount'        => (float)($fSuccCoupon),
                    'currency_unit' => $sCurrencyUnit,
                ]
            );
        } else {
            if (!empty($fSuccCoupon)) {
                $aCouponDesc[] = Language::replaceTag(
                    $aText['anycar_carpool_dual_price_coupon_succ_desc_for_detail'],
                    [
                        'amount'        => (float)($fSuccCoupon),
                        'currency_unit' => $sCurrencyUnit,
                    ]
                );
            }

            if (!empty($fFailCoupon)) {
                $aCouponDesc[] = Language::replaceTag(
                    $aText['anycar_carpool_dual_price_coupon_fail_desc_for_detail'],
                    [
                        'amount'        => (float)($fFailCoupon),
                        'currency_unit' => $sCurrencyUnit,
                    ]
                );
            }
        }

        $sCouponDesc = implode('/', $aCouponDesc);

        return [$sPriceDesc, $sCouponDesc];
    }

    /**
     * 两口价新形态的冒泡处理
     * @param array  $aItem         anycar中间一项
     * @param string $sCurrencyUnit 金额单位
     * @return array
     */
    public function newDualCarpoolPriceAnyCarBubble($aItem, $sCurrencyUnit) {
        $aAnyCarTextConf = NuwaConfig::text('config_text', 'anycar');
        $sText           = $aAnyCarTextConf['anycar_carpool_new_dual_price'];

        if (Carpool::CARPOOL_DAY == $aItem['pricing_type'] && 0 == $aItem['price']) {
            $sText = $aAnyCarTextConf['anycar_carpool_new_dual_price_carpool_day'];
        }

        list($sPriceText, $sCouponAmountDesc) = explode('/', $sText);
        $sPriceDesc = Language::replaceTag(
            $sPriceText,
            [
                'amount_succ'   => (float)($aItem['price']),
                'currency_unit' => $sCurrencyUnit,
            ]
        );
        return [$sPriceDesc, $sCouponAmountDesc];
    }

    /**
     * @param array  $aItem           aItem
     * @param string $sCurrencyUnit   currentUnit
     * @param string $sCurrencySymbol currencySymbol
     * @return array $aRet
     */
    public function dualCarpoolPriceInfoDesc($aItem, $sCurrencyUnit, $sCurrencySymbol) {
        $aDiscountDesc = [
            'total'     => 0.0,
            'num'       => 0,
            'desc'      => '',
            'desc_icon' => '',
        ];
        $aDiscountInfo = $aItem['discount_desc'];
        if (!empty($aDiscountInfo)) {
            foreach ($aDiscountInfo as $aDiscountItem) {
                if (($fDiscount = abs($aDiscountItem['amount'] ?? 0)) > 0 && 'infofee' != $aDiscountItem['type']) {
                    $aDiscountDesc['total'] += $fDiscount;
                }
            }
        }

        $aActivityDiscount = $aItem['discount_bills'];
        if (!empty($fDiscountTotal = $aActivityDiscount['normal']['discounts_total'])) {
            $aDiscountDesc['total'] += abs($fDiscountTotal);
        }

        $aCarpoolDescConfig = NuwaConfig::text('config_carpool', 'dual_carpool_estimate_fee');

        if (0 == $aDiscountDesc['total']) {
            return [];
        }

        if (MainDataRepo::isAnyCarMenu() && (UtilHelper::compareAppVersion(MainDataRepo::getAppVersion(), '5.4.12') >= 0 || MainDataRepo::isApplet())) {
            $sTitle = 'discount_title_new';
        } else {
            $sTitle = 'discount_title';
        }

        $aDiscountDesc['desc'] = Language::replaceTag(
            $aCarpoolDescConfig[$sTitle],
            array(
                'currency_symbol' => $sCurrencySymbol,
                'num'             => NumberHelper::numberFormatDisplay($aDiscountDesc['total']),
                'currency_unit'   => $sCurrencyUnit,
            )
        );

        $aRet = Common::formatPriceInfo([$aDiscountDesc['desc']], [], []);

        return $aRet;
    }
}
