<?php
namespace PreSale\Models\order;

use Biz<PERSON>ib\Config as NuwaConfig;
use BizLib\Utils\Language;
use BizLib\Utils\NumberHelper;
use BizLib\Utils\Horae;
use BizLib\Log as NuwaLog;
use Nuwa\ApolloSDK\Apollo;
use BizCommon\Models\Rpc\OrderSystemRpc;

/**
 * Class OrderInterCityCarpool
 * @package PreSale\Models\order
 */
class OrderInterCityCarpool
{
    const DISPLAY_CARPOOL_SUCCESS_FEE_NAME = 'carpool_succ_fee';
    const TARGET_APPVERSION = '5.1.24';
    const ENTRY_INTER_CITY  = 1;
    const ENTRY_REAL_TIME   = 2;
    const LONG_ROUTE        = 1; //长单

    private $_aCommonInfo;
    private $_aOrderInfo;
    private static $_sEntryScene = self::ENTRY_INTER_CITY;
    private static $_iRouteType;
    private $_oApollo;

    /**
     * 是否符合两口价场景.
     *
     * @param int $iCarpoolSeat iCarpoolSeat
     * @param int $iComboType   iComboType
     *
     * @return bool
     */
    public static function isDoublePriceAvailable(int $iCarpoolSeat, int $iComboType): bool {
        return Horae::isInterCityCarPoolScene($iComboType) && 1 == $iCarpoolSeat;
    }

    /**
     * 两口价的价格处理.
     *
     * @param int   $iCarpoolSeat     iCarpoolSeat
     * @param int   $iComboType       iComboType
     * @param array $aCarpoolBillInfo aCarpoolBillInfo
     * @param array $aBizPriceField   aBizPriceField
     * @return array
     */
    public static function getCarpoolSuccessFee(int $iCarpoolSeat, int $iComboType, array $aCarpoolBillInfo, array $aBizPriceField): array {
        if (self::isDoublePriceAvailable($iCarpoolSeat, $iComboType) && isset($aCarpoolBillInfo['display_lines'])) {
            $aCapSuccFeeInfo = array_filter(
                $aCarpoolBillInfo['display_lines'],
                function ($aDisplaySingle) {
                    return self::DISPLAY_CARPOOL_SUCCESS_FEE_NAME === (string)($aDisplaySingle['name']);
                }
            );
            $aCapSuccFeeInfo = current($aCapSuccFeeInfo);
            if (!empty($aCapSuccFeeInfo)) {
                $iPriceGap = (int)($aCarpoolBillInfo['basic_total_fee']) - (int)($aCapSuccFeeInfo['value']);
                if ($iPriceGap > 0) {
                    $aBizPriceField['carpool_success_gap'] = $iPriceGap;
                    $aBizPriceField['carpool_success_fee'] = (int)($aCapSuccFeeInfo['value']);
                }
            }
        }

        return $aBizPriceField;
    }

    /**
     * OrderInterCityCarpool constructor.
     * @param array $aInfo aInfo
     */
    public function __construct(array $aInfo) {
        //$this->_aInfo = $aInfo;
        $this->_aCommonInfo    = $aInfo['common_info'];
        $this->_aOrderInfo     = $aInfo['order_info'];
        $this->_aPassengerInfo = $aInfo['passenger_info'];
        $this->_aBillInfo      = $aInfo['bill_info'];
        $this->_aActivityInfo  = $aInfo['activity_info'];
        $this->_oApollo        = $oApollo = new Apollo();
    }

    /**
     * 设置入口来源的场景.
     *
     * @param int $scene scene
     * @return void
     */
    public static function setEntryScene($scene) {
        if (self::ENTRY_INTER_CITY == $scene || self::ENTRY_REAL_TIME == $scene) {
            self::$_sEntryScene = $scene;
        }
    }

    /**
     * @desc 设置路线标签
     *
     * @param int $iRouteType iRouteType
     * @return void
     */
    public static function setRouteType($iRouteType) {
        self::$_iRouteType = $iRouteType;
    }

    /**
     * 入口是否来自发单预估入口.
     *
     * @return bool
     */
    public function isFromRealTimeEntry() {
        return self::ENTRY_REAL_TIME == self::$_sEntryScene;
    }

    /**
     * @desc 是否长单路线
     *
     * @return bool
     */
    public function isLongRouteType() {
        return self::LONG_ROUTE == self::$_iRouteType;
    }

    /**
     * @param array $responseParam responseParam
     * @return mixed
     */
    public function processPretreatment($responseParam) {
        $aCarpoolData = $responseParam['carpool_data'];
        $sCurrency    = $this->_aBillInfo['currency'] ?? '';
        list($sSymbol, $sUnit) = \BizLib\Utils\Currency::getSymbolUnit($sCurrency, $this->_aOrderInfo);
        $aAreaId   = $this->_aOrderInfo['area'];
        $aIntroMsg = $this->_getConfigContent('inter_carpool_intro_msg');
        if (isset($aIntroMsg[$aAreaId]) || isset($aIntroMsg['default'])) {
            $aCarpoolData['intro_msg'] = isset($aIntroMsg[$aAreaId]) ? $aIntroMsg[$aAreaId] : $aIntroMsg['default'];
        }

        $iButtonText = $this->_getButtonText($aAreaId);
        if (!empty($iButtonText)) {
            $aCarpoolData['button_text'] = $iButtonText;
        }

        $iPriceGap = 0;
        if (isset($this->_aBillInfo['bills'][$this->_aOrderInfo['require_level']]['carpool']['carpool_success_gap'])
            && !empty($this->_aBillInfo['bills'][$this->_aOrderInfo['require_level']]['carpool']['carpool_success_gap'])
        ) {
            $iPriceGap = $this->_aBillInfo['bills'][$this->_aOrderInfo['require_level']]['carpool']['carpool_success_gap'];
        } elseif (isset($this->_aBillInfo['biz_price_field']['carpool_success_gap'])
            && !empty($this->_aBillInfo['biz_price_field']['carpool_success_gap'])
        ) {
            $iPriceGap = $this->_aBillInfo['biz_price_field']['carpool_success_gap'];
        }

        if (empty($iPriceGap)) {
            $responseParam['carpool_data'] = $aCarpoolData;

            return $responseParam;
        }

        $sAppVersion = $this->_aCommonInfo['app_version'];
        $aTextConfig = NuwaConfig::text('config_text', 'getEstimateFeeInfo');

        if (!$this->isFromRealTimeEntry()) {
            $aCarpoolData['double_price_desc'] = Language::replaceTag(
                $aTextConfig['intercity_douple_price_desc'],
                [
                    'carpool_success_gap' => $iPriceGap,
                    'currency_unit'       => $sUnit,
                ]
            );
            if (version_compare($sAppVersion, self::TARGET_APPVERSION, '<')
                && 1 == $this->_aOrderInfo['carpool_seat_num']
                && empty($this->_aCommonInfo['from'])
            ) {
                // 如果是老版本，且需要显示两口价，则把 intro_msg 替换
                $aCarpoolData['intro_msg'] = $aCarpoolData['double_price_desc'];
            }
        }

        $responseParam['carpool_data'] = $aCarpoolData;

        return $responseParam;
    }

    /**
     * @param array $aResponse aResponse
     * @return mixed
     */
    public function getIntroMsg($aResponse) {
        $aAreaId   = $this->_aOrderInfo['area'];
        $aIntroMsg = $this->_getConfigContent('inter_carpool_intro_msg');
        if (isset($aIntroMsg[$aAreaId]) || isset($aIntroMsg['default'])) {
            $aResponse['intro_msg'] = isset($aIntroMsg[$aAreaId]) ? $aIntroMsg[$aAreaId] : $aIntroMsg['default'];
        }

        $iPriceGap = $this->_getInterCityPriceGap();
        if ($iPriceGap > 0) {
            $sAppVersion = $this->_aCommonInfo['app_version'];
            $aTextConfig = NuwaConfig::text('config_text', 'getEstimateFeeInfo');
            $sCurrency   = $this->_aBillInfo['currency'] ?? '';
            list($sSymbol, $sUnit) = \BizLib\Utils\Currency::getSymbolUnit($sCurrency, $this->_aOrderInfo);
            if (!$this->isFromRealTimeEntry()) {
                if (version_compare($sAppVersion, self::TARGET_APPVERSION, '<')
                    && 1 == $this->_aOrderInfo['carpool_seat_num']
                    && empty($this->_aCommonInfo['from'])
                ) {
                    // 如果是老版本，且需要显示两口价，则把 intro_msg 替换
                    $aResponse['intro_msg'] = Language::replaceTag(
                        $aTextConfig['intercity_douple_price_desc'],
                        [
                            'carpool_success_gap' => $iPriceGap,
                            'currency_unit'       => $sUnit,
                        ]
                    );
                }
            }
        }

        return $aResponse;
    }

    /**
     * @param array $aResponse aResponse
     * @return mixed
     */
    public function getPriceDescInfo($aResponse) {

        $iPriceGap = $this->_getInterCityPriceGap();
        if ($iPriceGap > 0) {
            $aTextConfig = NuwaConfig::text('config_text', 'getEstimateFeeInfo');
            $sCurrency   = $this->_aBillInfo['currency'] ?? '';
            list($sSymbol, $sUnit) = \BizLib\Utils\Currency::getSymbolUnit($sCurrency, $this->_aOrderInfo);
            if (!$this->isFromRealTimeEntry()) {
                $aResponse['double_price_desc']      = Language::replaceTag(
                    $aTextConfig['intercity_douple_price_desc'],
                    [
                        'carpool_success_gap' => $iPriceGap,
                        'currency_unit'       => $sUnit,
                    ]
                );
                $aResponse['double_price_available'] = 1;
            }
        }

        return $aResponse;
    }


    /**
     * 获取城际拼车两口价差值
     * @return int
     */
    private function _getInterCityPriceGap() {
        $iPriceGap = 0;
        if (isset($this->_aBillInfo['bills'][$this->_aOrderInfo['require_level']]['carpool_success_gap'])
            && !empty($this->_aBillInfo['bills'][$this->_aOrderInfo['require_level']]['carpool_success_gap'])
        ) {
            $iPriceGap = $this->_aBillInfo['bills'][$this->_aOrderInfo['require_level']]['carpool_success_gap'];
        } elseif (isset($this->_aBillInfo['biz_price_field']['carpool_success_gap'])
            && !empty($this->_aBillInfo['biz_price_field']['carpool_success_gap'])
        ) {
            $iPriceGap = $this->_aBillInfo['biz_price_field']['carpool_success_gap'];
        }

        return $iPriceGap;
    }



    /**
     * 获取城际拼车fee_msg
     * @param array $aFeeMsg 费用信息
     * @return mixed
     */
    public function getFeeMsg($aFeeMsg) {
        //命中跨城拼车三叉戟场景
        if ($this->isFromRealTimeEntry()) {
            $aFeeMsg['estimate_fee_text'] = $aFeeMsg['fee_msg'];
        }

        $iPriceGap = $this->_getInterCityPriceGap();
        if (empty($iPriceGap)) {
            return $aFeeMsg;
        }

        $sCurrency = $this->_aBillInfo['currency'] ?? '';
        list($sSymbol, $sUnit) = \BizLib\Utils\Currency::getSymbolUnit($sCurrency, $this->_aOrderInfo);
        $aTextConfig           = NuwaConfig::text('config_text', 'getEstimateFeeInfo');
        $aFeeMsg['fee_msg']    = Language::replaceTag(
            $aTextConfig['intercity_cap_succ_info'],
            [
                'fee'           => NumberHelper::numberFormatDisplay($this->_aActivityInfo[0]['discount_fee']),
                'currency_unit' => $sUnit,
            ]
        );

        if ($this->isFromRealTimeEntry()) {
            $aFeeMsg['double_price_available'] = 1;
            $aFeeMsg['estimate_fee_text']      = Language::replaceTag(
                $aTextConfig['intercity_douple_price_desc'],
                [
                    'carpool_success_gap' => $iPriceGap,
                    'currency_unit'       => $sUnit,
                ]
            );
        }

        return $aFeeMsg;
    }


    /**
     * 城际拼车dc_extra_info
     * @param array $aDcExtraInfo aDcExtraInfo
     * @return array
     */
    public function getDcExtraInfo($aDcExtraInfo) {
        $aTextConfig = NuwaConfig::text('config_text', 'getEstimateFeeInfo');
        //命中跨城拼车三叉戟场景 && 命中长单
        if ($this->isFromRealTimeEntry() && $this->isLongRouteType()) {
            $aDcExtraInfo[] = [
                'route_tag' => $aTextConfig['intercity_long_route_tag'],
            ];
        }

        return $aDcExtraInfo;
    }


    /**
     * 城际拼车departure_time_text、departure_time_bubble
     * @param array $aResponse aResponse
     * @return mixed
     */
    public function getDepartureTimeText($aResponse) {
        //命中跨城拼车三叉戟场景
        if ($this->isFromRealTimeEntry()) {
            $aResponse['departure_time_text']   = $this->_getDepartureTimeText($this->_aOrderInfo, $this->_aPassengerInfo, 'text');
            $aResponse['departure_time_bubble'] = $this->_getDepartureTimeText($this->_aOrderInfo, $this->_aPassengerInfo, 'bubble');
        }

        return $aResponse;
    }


    /**
     * 城际拼车button text
     * @param string $sButtonText sButtonText
     * @return mixed
     */
    public function getButtonText($sButtonText) {
        $aAreaId = $this->_aOrderInfo['area'];
        $sButton = $this->_getButtonText($aAreaId);
        if (!empty($sButton)) {
            $sButtonText = $sButton;
        }

        return $sButtonText;
    }


    /**
     * @param array $aInfo         aInfo
     * @param array $responseParam responseParam
     * @return mixed
     */
    public function processBillLogic($aInfo, $responseParam) {
        $aCarpoolData = $responseParam['carpool_data'];
        $sCurrency    = $this->_aBillInfo['currency'] ?? '';
        list($sSymbol, $sUnit) = \BizLib\Utils\Currency::getSymbolUnit($sCurrency, $this->_aOrderInfo);
        $aTextConfig           = NuwaConfig::text('config_text', 'getEstimateFeeInfo');

        //命中跨城拼车三叉戟场景
        if ($this->isFromRealTimeEntry()) {
            $aCarpoolData['estimate_fee_text']     = $aCarpoolData['fee_msg'];
            $aCarpoolData['departure_time_text']   = $this->_getDepartureTimeText($this->_aOrderInfo, $this->_aPassengerInfo, 'text');
            $aCarpoolData['departure_time_bubble'] = $this->_getDepartureTimeText($this->_aOrderInfo, $this->_aPassengerInfo, 'bubble');
            if ($this->isLongRouteType()) { //命中长单 展示"长单特价"
                $aCarpoolData['dc_extra_info'][] = [
                    'route_tag' => $aTextConfig['intercity_long_route_tag'],
                ];
            }
        }

        $iPriceGap = 0;
        if (isset($this->_aBillInfo['bills'][$this->_aOrderInfo['require_level']]['carpool']['carpool_success_gap'])
            && !empty($this->_aBillInfo['bills'][$this->_aOrderInfo['require_level']]['carpool']['carpool_success_gap'])
        ) {
            $iPriceGap = $this->_aBillInfo['bills'][$this->_aOrderInfo['require_level']]['carpool']['carpool_success_gap'];
        } elseif (isset($this->_aBillInfo['biz_price_field']['carpool_success_gap'])
            && !empty($this->_aBillInfo['biz_price_field']['carpool_success_gap'])
        ) {
            $iPriceGap = $this->_aBillInfo['biz_price_field']['carpool_success_gap'];
        }

        if (empty($iPriceGap)) {
            $responseParam['carpool_data'] = $aCarpoolData;

            return $responseParam;
        }

        $aCarpoolData['fee_msg'] = Language::replaceTag(
            $aTextConfig['intercity_cap_succ_info'],
            [
                'fee'           => NumberHelper::numberFormatDisplay($this->_aActivityInfo['discount_fee']),
                'currency_unit' => $sUnit,
            ]
        );
        if ($this->isFromRealTimeEntry()) {
            $aCarpoolData['double_price_available'] = 1;
            $aCarpoolData['estimate_fee_text']      = Language::replaceTag(
                $aTextConfig['intercity_douple_price_desc'],
                [
                    'carpool_success_gap' => $iPriceGap,
                    'currency_unit'       => $sUnit,
                ]
            );
        }

        $responseParam['carpool_data'] = $aCarpoolData;

        return $responseParam;
    }

    /**
     * 发车时间文案.
     * @param array  $aOrderInfo     aOrderInfo
     * @param array  $aPassengerInfo aPassengerInfo
     * @param string $type           type
     * @return string
     */
    private function _getDepartureTimeText($aOrderInfo, $aPassengerInfo, $type = 'text') {
        $aEstimateFeeTextConfig = NuwaConfig::text('config_text', 'getEstimateFeeInfo');
        $timeSpan = isset($this->_aBillInfo['route_info']['time_span'][0]['range'][0]) ? $this->_aBillInfo['route_info']['time_span'][0]['range'][0] : '';
        if (empty($timeSpan)) {
            return '';
        }

        list($startTime, $endTime) = explode(',', substr($timeSpan['value'], 1, -1));
        $oApollo       = new \Nuwa\ApolloSDK\Apollo();
        $oApolloToggle = $oApollo->featureToggle(
            'cuilizhizi_2018_11_21_content_test',
            [
                'key'   => $aPassengerInfo['phone'],
                'phone' => $aPassengerInfo['phone'],
                'city'  => $aOrderInfo['area'],
            ]
        );
        if ('text' == $type) {
            if ($oApolloToggle->allow() && 'treatment_group' == $oApolloToggle->getGroupName()) {
                return Language::replaceTag(
                    $aEstimateFeeTextConfig['intercity_departure_time_text_AB'],
                    [
                        'start_time' => date('H:i', $startTime),
                    ]
                );
            } else {
                return Language::replaceTag(
                    $aEstimateFeeTextConfig['intercity_departure_time_text'],
                    [
                        'end_time' => date('H:i', $endTime),
                    ]
                );
            }
        } elseif ('bubble' == $type) {
            if ($oApolloToggle->allow() && 'treatment_group' == $oApolloToggle->getGroupName()) {
                return Language::replaceTag(
                    $aEstimateFeeTextConfig['intercity_departure_time_bubble_AB'],
                    [
                        'start_time' => date('H:i', $startTime),
                    ]
                );
            } else {
                return Language::replaceTag(
                    $aEstimateFeeTextConfig['intercity_departure_time_bubble'],
                    [
                        'end_time' => date('H:i', $endTime),
                    ]
                );
            }
        }
    }

    /**
     * 获取发单按钮文案
     *
     * @param int $iAreaId area
     * @return mixed|string
     */
    private function _getButtonText($iAreaId) {
        $iCapacityId   = $this->_aBillInfo['match_routes']['capacity_id'];
        $aCapacityInfo = \BizCommon\Logics\Carpool\InterCarpoolCapacity::getCapacityInfoById($iAreaId, $iCapacityId);
        if (isset($aCapacityInfo['type']) && \BizCommon\Utils\Horae::bInterKeYunCapacity($aCapacityInfo['type'])) {
            //如果是已经绑定客企运力的路线，按照运力名称配置
            $aParams     = [
                'capacity_abbr' => $aCapacityInfo['abbreviation'],
            ];
            $aDataConfig = json_decode(Language::getTextFromDcmp('config_inter_carpool-keyun', $aParams), true);
            if (!empty($aDataConfig['button_text'])) {
                return $aDataConfig['button_text'];
            }
        } else {
            //否者按通用城市配置
            $aButtonMsg = $this->_getConfigContent('inter_carpool_new_order_button_text');
            if (isset($aButtonMsg[$iAreaId]) || isset($aButtonMsg['default'])) {
                return isset($aButtonMsg[$iAreaId]) ? $aButtonMsg[$iAreaId] : $aButtonMsg['default'];
            }
        }

        return '';
    }

    /**
     * 获取Apollo配置
     * @param string $sConfigKey sConfigKey
     * @return array
     */
    private function _getConfigContent($sConfigKey) {
        if (empty($sConfigKey)) {
            return [];
        }

        $oConfig           = $this->_oApollo->getConfigResult('inter_carpool_conf', $sConfigKey);
        list($bSucc, $ret) = $oConfig->getAllConfig();
        if (!$bSucc) {
            NuwaLog::warning('get inter carpool config failed||config_key=' . $sConfigKey . '||errmsg=' . $ret);
            return [];
        }

        return $ret;
    }


    /**
     * @param int $iPassengerId iPassengerId
     * @return array
     */
    public static function getRunningOrders($iPassengerId) {
        $iOffset    = 0;
        $iLimit     = 10;
        $iStartTime = time() - 3600 * 2;
        $iEndTime   = time() + 3600 * 2;
        $aCondition = [
            'order_status' => [
                \BizLib\Constants\OrderSystem::ST_STRIVED,
            ],
            'combo_type'   => \BizLib\Constants\Horae::TYPE_COMBO_CARPOOL_INTER_CITY,
        ];
        $aFields    = [
            'combo_id',
            'area',
            'combo_type',
            'departure_time',
            'starting_name',
            'dest_name',
            'district',
            'dest_lat',
            'dest_lng',
            'starting_lat',
            'starting_lng',
        ];

        $oOrderSystemRpc = new OrderSystemRpc();
        return $oOrderSystemRpc->getPassengerHistoryOrder($iPassengerId, $iOffset, $iLimit, $iStartTime, $iEndTime, $aCondition, $aFields);

    }
}
