<?php

namespace PreSale\Models\order;

use BizLib\Libraries\RedisDB;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Common;
/*
 * desc:获取订单特征数据相关
 * Author: <EMAIL>
 * Time: 2018/8/23 下午5:35.
 */
use BizLib\Client\OfsClient;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;

class OrderFeature
{
    /** @var $redisdb RedisDB */
    public $redisdb;
    private $_oOFS;
    const TRACEID_CACHE_TIME = 1800;

    public function __construct() {
    }

    public function getOrderFeature($iOrderId, $aOrderFeature, $aOption = []) {
        $this->_oOFS = new OfsClient();
        $aResult     = $this->_oOFS->getOrderFeature($iOrderId, $aOrderFeature, $aOption);
        if (empty($aResult) || $aResult['errno']) {
            NuwaLog::warning(sprintf(Msg::formatArray(Code::E_COMMON_OFS_QUERY_FAIL), ['order_id' => $iOrderId]));

            return [];
        }

        return $aResult['result'];
    }

    private function _buildRedisKey($sEstimateId) {
        $this->redisdb    = RedisDB::getInstance();
        $sSceneFeatureKey = Common::getRedisPrefix(P_SCENE_FEATURE).$sEstimateId;

        return $sSceneFeatureKey;
    }

    /**
     * @Desc:根据围栏时间自动给订单打特征
     *
     * @param $sEstimateTraceId
     * @param $aSceneFeatures
     *
     * @property setSceneFeature $setSceneFeature
     * @Author:<EMAIL>
     */
    public function setSceneFeature($sEstimateId, $aSceneFeatures) {
        $this->redisdb = RedisDB::getInstance();
        if (empty($sEstimateId) || empty($aSceneFeatures)) {
            return false;
        }

        $sSceneFeatureKey = $this->_buildRedisKey($sEstimateId);
        $bResult          = $this->redisdb->setex($sSceneFeatureKey, self::TRACEID_CACHE_TIME, json_encode($aSceneFeatures));
        if (empty($bResult)) {
            NuwaLog::warning(sprintf('errno:'.CACHE_ERRNO_SET.' | errmsg:set cache fail key:%s , value:%s', $sSceneFeatureKey, json_encode($aSceneFeatures)));
        }

        return $bResult;
    }

    /**
     * @Desc:
     *
     * @param $sEstimateTraceId
     *
     * @property getSceneFeature $getSceneFeature
     * @Author:<EMAIL>
     */
    public function getSceneFeature($sEstimateId) {
        $this->redisdb = RedisDB::getInstance();
        if (empty($sEstimateId)) {
            return false;
        }

        $sSceneFeatureKey = $this->_buildRedisKey($sEstimateId);
        $sSceneFeatures   = $this->redisdb->get($sSceneFeatureKey);
        if (empty($sSceneFeatures)) {
            NuwaLog::updateLogHeadData(['scene_features_from_redis' => $sSceneFeatures]);

            return [];
        }

        $aSceneFeatures = json_decode($sSceneFeatures, true);
        if (empty($aSceneFeatures)) {
            NuwaLog::updateLogHeadData(['scene_features_from_redis' => $sSceneFeatures]);

            return [];
        }

        return $aSceneFeatures;
    }
}
