<?php
namespace PreSale\Models\order;

use BizLib\Config;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\Language;
use BizLib\Utils\CarLevel;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\estimatePrice\bill\CommonBillLogic;
use PreSale\Logics\estimatePrice\DecisionLogic;
use PreSale\Logics\estimatePrice\multiResponse\MainDataRepo;
use PreSale\Logics\commonAbility\SpringRedPacketFormatter;
use Xiaoju\Apollo\Apollo as ApolloV2;
use BizCommon\Utils\Horae;
use PreSale\Logics\estimatePrice\multiResponse\Util;
use BizLib\Utils\NumberHelper;

/**
 * Class OrderCarpool
 * carpool订单相关处理类？
 * 当前主要处理返回阶段的相关数据，但命名上不大合理
 * 需要拼车.
 * @package PreSale\Models\order
 */
class OrderCarpool
{
    const DISPLAY_CARPOOL_SUCCESS_FEE_NAME = 'carpool_succ_fee';
    const TARGET_APPVERSION = '5.1.24';
    const ENTRY_INTER_CITY  = 1;
    const ENTRY_REAL_TIME   = 2;

    private $_aCommonInfo;
    private $_aOrderInfo;
    // 国际化货币符号
    private $_sCurrencySymbol;

    // 国际化"现金单位"
    private $_sCurrencyUnit;

    //分层定价优惠
    private $_aDiscountsBill;

    // 预估新表单文案信息
    private $_aEstimateNewFormText;
    /**
     * @var $_aInfo
     */
    private $_aInfo;
    /**
     * @var $_aBillDetail
     */
    private $_aBillDetail;

    /**
     * @var SpringRedPacketFormatter
     */
    private $_oFormatter;

    /**
     * OrderCarpool constructor.
     * @param array $aInfo aInfo
     */
    public function __construct(array $aInfo) {
        $this->_aInfo         = $aInfo;
        $this->_aCommonInfo   = $aInfo['common_info'];
        $this->_aOrderInfo    = $aInfo['order_info'];
        $this->_aBillInfo     = $aInfo['bill_info'];
        $this->_aActivityInfo = $aInfo['activity_info'];
        $sCurrency            = $this->_aBillInfo['currency'] ?? '';
        $sCarLevel            = $this->_aOrderInfo['require_level'];
        $this->_aDiscountsBill       = $this->_aBillInfo['bills'][$sCarLevel]['discounts_bill'] ?? [];
        $this->_aEstimateNewFormText = NuwaConfig::text('estimate_new_form', 'estimate_form_info');
        list($sSymbol, $sUnit)       = \BizLib\Utils\Currency::getSymbolUnit($sCurrency, $this->_aOrderInfo);
        $this->_sCurrencySymbol      = $sSymbol;
        $this->_sCurrencyUnit        = $sUnit;
        $this->_aBillDetail          = $this->_aBillInfo['bills'][$sCarLevel];
        $this->_oFormatter           = new SpringRedPacketFormatter($this->_aInfo, true);
    }


    /**
     * @param array $aInfo         aInfo
     * @param array $responseParam responseParam
     * @return mixed
     */
    public function processBillLogic($aInfo, $responseParam) {
        $aTextConfig  = NuwaConfig::text('config_text', 'getEstimateFeeInfo');
        $aCarpoolData = $responseParam['carpool_data'];
        //两口价
        if (isset($this->_aBillInfo['bills'][CarLevel::DIDI_PUTONG_CAR_LEVEL]['carpool']['carpool_fail_fee'])
            && isset($this->_aActivityInfo['carpool'][1]['estimate_fee'])
        ) {
            $aCarpoolData['double_price_desc'] = Language::replaceTag(
                $aTextConfig['carpool_fail_price_desc'],
                [
                    'carpool_fail_price' => $this->_aActivityInfo['carpool'][1]['estimate_fee'],
                    'currency_unit'      => $this->_sCurrencyUnit,
                ]
            );
            $aCarpoolData['fee_msg']           = Language::replaceTag(
                $aTextConfig['carpool_success_price_desc'],
                [
                    'carpool_success_price' => $aCarpoolData['fee_amount'],
                    'currency_unit'         => $this->_sCurrencyUnit,
                ]
            );
            $aCarpoolData['price_desc']        = [];

            // 由于春节红包必须在全场景下展示，因为在最后做了desc清空的逻辑，目前只能在这重新拼接一次拼车两口价的春节红包描述了，很坑，但是暂时也没有别的办法
            list($sRedPacketDesc, ) = $this->_oFormatter->getRedPacketInfoLite();
            if (!empty($sRedPacketDesc)) {
                $aCarpoolData['price_desc'][] = $sRedPacketDesc;
            }

            $aPriceDesc = $aCarpoolData['price_desc'];
            if (!empty($aPriceDesc)) {
                $aCarpoolData['price_desc'] = implode(',', $aPriceDesc);
            } else {
                $aCarpoolData['price_desc'] = '';
            }

            if (version_compare((string)($aInfo['common_info']['app_version']), '5.2.46') >= 0) {
                $aPriceDescParam = !empty($aCarpoolData['price_desc']) ? explode(',', $aCarpoolData['price_desc']) : [];
                $aPolloParam     = [
                    'key'       => $aInfo['passenger_info']['pid'],
                    'phone'     => $aInfo['passenger_info']['phone'],
                    'city'      => $aInfo['order_info']['area'],
                    'car_level' => $aInfo['order_info']['require_level'],
                ];

                $oUpgradeApollo = (new ApolloV2())->featureToggle('gs_upgrade_price_desc',$aPolloParam)->allow();

               // $oH5NotDelayApollo = (new ApolloV2())->featureToggle('gs_h5_not_delay', $aPolloParam)->allow();
                $oH5NotDelayApollo = true;
                //新表单
                if ($oUpgradeApollo && $oH5NotDelayApollo) {
                    $aPriceInfoDesc = [];
                    if (!empty($aPriceDescParam)) {
                        $aPriceInfoDescDefault = Language::getDecodedTextFromDcmp('estimate_price_info_desc-default');
                        foreach ($aPriceDescParam as $key => $aValue) {
                            $aPriceInfoDesc[$key]            = $aPriceInfoDescDefault;
                            $aPriceInfoDesc[$key]['content'] = $aPriceDesc[$key];
                        }
                    }

                    $aCarpoolData['price_info_desc'] = $aPriceInfoDesc;
                }
            }
        } elseif (CarLevel::DIDI_UNITAXI_PUTONG_CAR_LEVEL == $this->_aOrderInfo['require_level']) {
            $aCarpoolData['double_price_desc'] = $aTextConfig['unione_carpool_fail_price_desc'];
            $aCarpoolData['fee_msg']           = Language::replaceTag(
                $aTextConfig['unione_carpool_success_price_desc'],
                [
                    'carpool_success_price' => $aCarpoolData['fee_amount'],
                    'currency_unit'         => $this->_sCurrencyUnit,
                ]
            );
        }

        $responseParam['carpool_data'] = $aCarpoolData;
        return $responseParam;
    }


    /**
     * @param array $aResponse aResponse
     * @return mixed
     */
    public function getPriceDescInfo($aResponse) {
        $aTextConfig = NuwaConfig::text('config_text', 'getEstimateFeeInfo');
        //两口价
        if (isset($this->_aBillInfo['bills'][CarLevel::DIDI_PUTONG_CAR_LEVEL]['carpool_fail_fee'])
            && isset($this->_aActivityInfo[1]['estimate_fee'])
        ) {
            $aResponse['double_price_desc'] = Language::replaceTag(
                $aTextConfig['carpool_fail_price_desc'],
                [
                    'carpool_fail_price' => $this->_aActivityInfo[1]['estimate_fee'],
                    'currency_unit'      => $this->_sCurrencyUnit,
                ]
            );

            $aResponse['price_desc'] = [];
            // 由于春节红包必须在全场景下展示，因为在最后做了desc清空的逻辑，目前只能在这重新拼接一次拼车两口价的春节红包描述了，很坑，但是暂时也没有别的办法
            $aCarpoolBillDisplayLines = CommonBillLogic::formatDisplayLines($this->_aBillInfo['bills'][CarLevel::DIDI_PUTONG_CAR_LEVEL]['display_lines']);
            $fCarpoolRedPacket        = $aCarpoolBillDisplayLines['red_packet']['value'] ?? 0.0;
            if ($fCarpoolRedPacket > 0) {
                $sCarpoolRedPacketDesc     = Language::replaceTag(
                    NuwaConfig::text('config_text', 'red_packet_estimate_title'),
                    array(
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'name'            => NuwaConfig::text('config_text', 'red_packet_name'),
                        'num'             => $fCarpoolRedPacket,
                        'currency_unit'   => $this->_sCurrencyUnit,
                    )
                );
                $aResponse['price_desc'][] = $sCarpoolRedPacketDesc;
            }

            $aPriceDesc = $aResponse['price_desc'];
            if (!empty($aPriceDesc)) {
                $aResponse['price_desc'] = implode(',', $aPriceDesc);
            } else {
                $aResponse['price_desc'] = '';
            }
        } elseif (CarLevel::DIDI_UNITAXI_PUTONG_CAR_LEVEL == $this->_aOrderInfo['require_level']) {
            $aResponse['double_price_desc'] = $aTextConfig['unione_carpool_fail_price_desc'];
        }

        // 两口价v2
        if (Horae::isCarpoolUnsuccRealTimePrice($this->_aOrderInfo)) {
            $aResponse['double_price_desc'] = $aTextConfig['carpool_dual_price_v2_fail_price_desc'];
        }

        return $aResponse;
    }

    /**
     * @desc 春节红包
     * @param array  $aItem           aItem
     * @param string $sCurrencyUnit   sCurrencyUnit
     * @param string $sCurrencySymbol sCurrencySymbol
     * @return array
     */
    public static function getRedPacketInfo($aItem, $sCurrencyUnit, $sCurrencySymbol) {
        $sPriceDesc = $sPriceDescIcon = '';
        $aCarpoolBillDisplayLines = CommonBillLogic::formatDisplayLines($aItem['display_lines']);
        $fRedPacketValue          = $aCarpoolBillDisplayLines['red_packet']['value'] ?? 0.0;
        if ($fRedPacketValue > 0) {
            $sDesc          = Language::replaceTag(
                NuwaConfig::text('config_text', 'red_packet_estimate_title'),
                array(
                    'currency_symbol' => $sCurrencySymbol,
                    'name'            => NuwaConfig::text('config_text', 'red_packet_name'),
                    'num'             => $fRedPacketValue,
                    'currency_unit'   => $sCurrencyUnit,
                )
            );
            $sPriceDesc     = $sDesc;
            $sPriceDescIcon = '';
        }

        return [$sPriceDesc,$sPriceDescIcon, $fRedPacketValue];
    }

    /**
     * @desc multi price desc info
     * @return array
     */
    public function getMultiPriceDescInfo() {
        $aSuccessPriceDesc   = [];
        $aUnSuccessPriceDesc = [];
        $aDualPriceDesc      = [];
        if (Horae::isCarpoolUnSuccessFlatPrice($this->_aOrderInfo)) {
            //有春节红包,优先级最高
            list($sRedPacketDesc, , $fRedPacketAmount) = self::getRedPacketInfo($this->_aBillInfo['bills'][CarLevel::DIDI_PUTONG_CAR_LEVEL], $this->_sCurrencyUnit, $this->_sCurrencySymbol);
            if ($fRedPacketAmount > 0) {
                $aPriceDesc     = [$sRedPacketDesc, $sRedPacketDesc];
                $aPriceDescIcon = [];
            }

            if (empty($aPriceDesc)) {
                list($aPriceDesc, $aPriceDescIcon) = $this->_getBusinessPaymentInfo();
            }

            if (empty($aPriceDesc)) {
                $aPriceDesc     = $this->_processDualPriceDesc();
                $aPriceDescIcon = [];
            }

            if (!empty($aPriceDesc[0])) {
                $aSuccessPriceDesc[] = $aPriceDesc[0];
            }

            if (!empty($aPriceDesc[1])) {
                $aUnSuccessPriceDesc[] = $aPriceDesc[1];
            }

            $fCarpoolFailFee    = $this->_aActivityInfo[1]['estimate_fee'] ?? 0;
            $fCarpoolSuccessFee = $this->_aActivityInfo[0]['estimate_fee'] ?? 0;

            // 6.0新表单单行模式
            if (Util::isEstimateFormDoubleLineStyle($this->_aInfo)) {
            // if (Util::isEstimateFormSingleLineStyle($this->_aInfo) || Util::isEstimateFormDoubleLineStyle($this->_aInfo)) {
                if (Util::isBusinessPay($this->_aInfo)) {
                    $fCarpoolFailFee    = 0;
                    $fCarpoolSuccessFee = 0;
                }
            }

            $aTextConfig    = NuwaConfig::text('config_text', 'getEstimateFeeInfo');
            $aDualPriceDesc = [
                [
                    'estimate_fee' => $fCarpoolFailFee,
                    'price_desc'   => !empty($aUnSuccessPriceDesc) ? implode(',', $aUnSuccessPriceDesc) : '',
                    'fee_msg'      => Language::replaceTag(
                        $aTextConfig['carpool_fail_price_desc'],
                        [
                            'carpool_fail_price' => $fCarpoolFailFee,
                            'currency_unit'      => $this->_sCurrencyUnit,
                        ]
                    ),
                    'left_icon'    => $aPriceDescIcon[0] ?? '',
                    'show_anim'    => 0,
                ],
                [
                    'estimate_fee' => $fCarpoolSuccessFee,
                    'price_desc'   => !empty($aSuccessPriceDesc) ? implode(',', $aSuccessPriceDesc) : '',
                    'fee_msg'      => Language::replaceTag(
                        $aTextConfig['carpool_success_price_desc'],
                        [
                            'carpool_success_price' => $fCarpoolSuccessFee,
                            'currency_unit'         => $this->_sCurrencyUnit,
                        ]
                    ),
                    'left_icon'    => $aPriceDescIcon[1] ?? '',
                    'show_anim'    => 0,
                ],
            ];
        }

        return $aDualPriceDesc;
    }

    /**
     * 6.2.12 以上双排表单，展示 企-**元
     * @return array
     */
    private function _getBusinessPaymentInfo() {
        if (version_compare($this->_aCommonInfo['app_version'], '6.2.12') < 0) {
            return[[], []];
        }

        if (!Util::isEstimateFormDoubleLineStyle($this->_aInfo)) {
            return [[], []];
        }

        if (!Util::isBusinessPay($this->_aInfo)) {
            return[[], []];
        }

        $fDeductFeeSucc = $this->_aActivityInfo[0]['estimate_fee'];
        $fDeductFeeFail = $this->_aActivityInfo[1]['estimate_fee'];

        $aText          = NuwaConfig::text('estimate_new_form', 'estimate_form_info');
        $sSuccDeduction = Language::replaceTag($aText['common_dec_price_desc'], ['fee_amount' => $fDeductFeeSucc, 'currency_unit' => $this->_sCurrencyUnit]);
        $sFailDeduction = Language::replaceTag($aText['common_dec_price_desc'], ['fee_amount' => $fDeductFeeFail, 'currency_unit' => $this->_sCurrencyUnit]);
        $sIcon          = $aText['business_pay_icon'];
        return [[$sSuccDeduction, $sFailDeduction], [$sIcon, $sIcon]];
    }


    /**
     * @param array $aFeeMsg aFeeMsg
     * @return mixed
     */
    public function getFeeMsg($aFeeMsg) {
        $aTextConfig = NuwaConfig::text('config_text', 'getEstimateFeeInfo');
        if (isset($this->_aBillInfo['bills'][CarLevel::DIDI_PUTONG_CAR_LEVEL]['carpool_fail_fee'])
            && isset($this->_aActivityInfo[1]['estimate_fee'])
        ) {
            //两口价
            $aFeeMsg['fee_msg'] = Language::replaceTag(
                $aTextConfig['carpool_success_price_desc'],
                [
                    'carpool_success_price' => $aFeeMsg['fee_amount'],
                    'currency_unit'         => $this->_sCurrencyUnit,
                ]
            );
        } elseif (CarLevel::DIDI_UNITAXI_PUTONG_CAR_LEVEL == $this->_aOrderInfo['require_level']) {
            $aFeeMsg['fee_msg'] = Language::replaceTag(
                $aTextConfig['unione_carpool_success_price_desc'],
                [
                    'carpool_success_price' => $aFeeMsg['fee_amount'],
                    'currency_unit'         => $this->_sCurrencyUnit,
                ]
            );
        }

        //两口价v2
        if (Horae::isCarpoolUnsuccRealTimePrice($this->_aOrderInfo)) {
            $aFeeMsg['fee_msg'] = Language::replaceTag(
                $aTextConfig['carpool_dual_price_v2_success_price_desc'],
                [
                    'carpool_success_price' => $aFeeMsg['fee_amount'],
                    'currency_unit'         => $this->_sCurrencyUnit,
                ]
            );
        }

        return $aFeeMsg;
    }

    /**
     * 处理两口价v3-展示 "比快车省xx元"
     * @return array
     */
    private function _processDualPriceDesc(): array {
        $sSuccPriceDesc = '';
        $sFailPriceDesc = '';

        $fCarpoolFailFee    = $this->_aActivityInfo[1]['estimate_fee'] ?? 0;
        $fCarpoolSuccessFee = $this->_aActivityInfo[0]['estimate_fee'] ?? 0;
        if (empty($fCarpoolFailFee)|| empty($fCarpoolSuccessFee)) {
            return [];
        }

        // $fPreTotalFee = $this->_aBillDetail['pre_total_fee'];// 快车综合预估价
        $sEstimateFee = MainDataRepo::getFastCarEstimateFeeV2();

        $fSuccDiff = round($sEstimateFee - $fCarpoolSuccessFee, 2);
        $fFailDiff = round($sEstimateFee - $fCarpoolFailFee, 2);

        $aCarpoolDescConfig = Language::getDecodedTextFromDcmp('config_carpool-estimate_fee_discount_msg');
        $sFormat            = $aCarpoolDescConfig['cheaper_than_flash'] ?? '';
        if ($fSuccDiff > 0) {
            $sSuccPriceDesc = Language::replaceTag(
                $sFormat,
                [
                    'price'           => NumberHelper::numberFormatDisplay($fSuccDiff, '', 2),
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'currency_unit'   => $this->_sCurrencyUnit,
                ]
            );
        }

        if ($fFailDiff > 0) {
            $sFailPriceDesc = Language::replaceTag(
                $sFormat,
                [
                    'price'           => NumberHelper::numberFormatDisplay($fFailDiff, '', 2),
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'currency_unit'   => $this->_sCurrencyUnit,
                ]
            );
        }

        return [$sSuccPriceDesc, $sFailPriceDesc];
    }

    /**
     * @desc 大额优惠小金币图标
     * @return array
     */
    public function getPreferentialIcon() {
        $aData            = [];
        $sCarLevel        = $this->_aInfo['order_info']['require_level'];
        $fDynamicTotalFee = $this->_aInfo['bill_info']['bills'][$sCarLevel]['dynamic_total_fee'];
        $aPreferentialConfigText = Config::text('config_text', 'preferential_feel_info');
        if (!empty($fDynamicTotalFee) && isset($this->_aActivityInfo[0]['estimate_fee']) && isset($this->_aActivityInfo[1]['estimate_fee']) && !empty($aPreferentialConfigText['discount_val'])) {
            $fCarpoolFailFee    = $this->_aActivityInfo[1]['estimate_fee'];
            $fCarpoolSuccessFee = $this->_aActivityInfo[0]['estimate_fee'];
            $iDiscountConf      = $aPreferentialConfigText['discount_val'];
            $iDiscountFail      = ceil(($fCarpoolFailFee / $fDynamicTotalFee) * 100);
            $iDiscountSuccess   = ceil(($fCarpoolSuccessFee / $fDynamicTotalFee) * 100);
            $sPriceDescIcon     = $aPreferentialConfigText['gold_icon_url'] ?? '';
            if ($iDiscountSuccess <= (int)$iDiscountConf || DecisionLogic::getInstance()->getPreferentialRecommendFlag($this->_aInfo['order_info']['product_category'])) { //小于等于8折
                if ($this->hitPreferentialApollo()) {
                    $aData['carpool_success'] = ['left_gif' => $sPriceDescIcon, 'show_anim' => 1];
                }
            }

            if ($iDiscountFail <= (int)$iDiscountConf || DecisionLogic::getInstance()->getPreferentialRecommendFlag($this->_aInfo['order_info']['product_category'])) { //小于等于8折
                if ($this->hitPreferentialApollo()) {
                    $aData['carpool_fail'] = ['left_gif' => $sPriceDescIcon, 'show_anim' => 1];
                }
            }
        }

        return $aData;
    }

    /**
     * @desc 命中大额优惠apollo
     * @return bool
     */
    public function hitPreferentialApollo() {
        $oApollo       = new Apollo();
        $oApolloResult = $oApollo->featureToggle(
            'preferential_feel_switch',
            [
                'key'              => $this->_aInfo['passenger_info']['phone'],
                'phone'            => $this->_aInfo['passenger_info']['phone'],
                'app_version'      => $this->_aCommonInfo['app_version'],
                'access_key_id'    => $this->_aCommonInfo['access_key_id'],
                'lang'             => $this->_aCommonInfo['lang'],
                'city'             => $this->_aOrderInfo['area'],
                'open_time'        => date('H:i'),
                'product_category' => $this->_aOrderInfo['product_category'],
                'week'             => date('w'),
            ]
        );
        if ($oApolloResult->allow() && $this->showPreferential() && DecisionLogic::getInstance()->getCapacityBalanceFlag()) {
            return true;
        }

        return false;
    }

    /**
     * @desc 大额优惠感知实验
     * @return bool
     */
    public function showPreferential() {
        $oApollo        = new Apollo();
        $oFeatureToggle = $oApollo->featureToggle(
            'youhuiganzhi_experience',
            array(
                Apollo::APOLLO_INDIVIDUAL_ID => $this->_aInfo['passenger_info']['pid'],
                'key'                        => $this->_aInfo['passenger_info']['pid'],
                'phone'                      => $this->_aInfo['passenger_info']['phone'],
                'lang'                       => $this->_aCommonInfo['lang'],
                'city'                       => $this->_aOrderInfo['area'],
                'open_time'                  => date('H:i'),
                'week'                       => date('w'),
            )
        );
        if ($oFeatureToggle->allow() && 'control_group' == $oFeatureToggle->getGroupName()) {
            return true;
        }

        return false;
    }
}
