<?php

namespace PreSale\Models\order;

use BizLib\Log as NuwaLog;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\UtilHelper;
/*
 * @Desc 动调condirm
 * <AUTHOR>
 * @Date 2017-07-27
 */
use BizLib\Utils;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use PreSale\Logics\order\AnyCarOrderLogic;
use BizCommon\Models\Order\OrderDynamicPrice;

/**
 * Class orderDynamicConfirm.
 *
 * @property OrderDynamicPrice OrderDynamicPrice
 */
class OrderDynamicConfirm
{
    /** @var $orderEstimateNew OrderEstimateNew */
    public $orderEstimateNew;
    const REWARD_SHOW_CONTENT = '订单最高以%s倍结算'; //**************NOTE: 智能补贴播单时会自动去掉"最高", 修改文案时注意跟策略沟通
    const REWARD_TTS_CONTENT  = '最高%s倍'; //**************NOTE: 智能补贴播单时会自动去掉"最高", 修改文案时注意跟策略沟通

    public function __construct() {
        $this->orderDynamicPrice = OrderDynamicPrice::getInstance();
    }

    public function processDynamicPrice($aOrderInfo, $aGetPriceReq) {
        $intNotDynamic       = 0; //默认需要动态
        $bIsInterCityCarPool = Utils\Horae::isInterCityCarPoolScene($aOrderInfo['combo_type']);
        if ($bIsInterCityCarPool) {   //跨城拼车不需要动调
            $intNotDynamic = 1;
        }

        //检查乘客端上传的动态调价是否是服务端下发的
        if ((UtilHelper::compareAppVersion($aGetPriceReq['app_version'], OrderDynamicPrice::DYNAMIC_SHOW_VERSION) >= 0 || !empty($aGetPriceReq['show_md5'])) && !$bIsInterCityCarPool) {
            $dynamicParams = array(
                'flng'          => $aOrderInfo['starting_lng'],
                'flat'          => $aOrderInfo['starting_lat'],
                'tlng'          => $aOrderInfo['dest_lng'],
                'tlat'          => $aOrderInfo['dest_lat'],
                'car_pool'      => $aGetPriceReq['car_pool'],
                'district'      => $aOrderInfo['district'],
                'product_id'    => $aOrderInfo['product_id'],
                'require_level' => $aOrderInfo['require_level'],
                'order_type'    => $aOrderInfo['type'],
            );
            $result        = $this->_checkDynamicPrice($aGetPriceReq['passenger_id'], $aGetPriceReq['imei'], $aGetPriceReq['show_md5'], $dynamicParams, $aGetPriceReq['estimate_cache_id']);
            if (0 != $result['errno']) {
                return $result;
            }

            $intNotDynamic = $result['not_need_dynamic'];
        }

        //获取动态价格信息
        $aDynamicPriceInfo = $this->_getOrderDynamicPriceInfo($aOrderInfo, $aGetPriceReq['imei'], $intNotDynamic, $aGetPriceReq['car_pool'], $aGetPriceReq['estimate_cache_id']);
        $aDynamicPriceInfo['dynamic_price_open_flag'] = $this->orderDynamicPrice->dynamicPriceOpenFlag($aOrderInfo);
        $aDynamicPriceInfo['not_need_dynamic']        = $intNotDynamic;

        return UtilsCommon::getErrMsg(GLOBAL_SUCCESS) + ['data' => ['dynamic_price' => $aDynamicPriceInfo], ];
    }

    /**
     * 获取anycar的动调.
     *
     * @param array $aOrderInfo       订单信息
     * @param array $sEstimateTraceId estimate_id
     *
     * @return array|bool
     */
    public function getAnyCarDynamicPrice($aOrderInfo, $sEstimateId) {
        //获取动态价格信息
        $aMultiDynamicPriceInfo['not_need_dynamic']   = 1;
        $aMultiDynamicPriceInfo['dynamic_price_info'] = [];
        // kafka会校验以下参数是否存在，这里仅做初始化，具体数据在multi里
        $aMultiDynamicPriceInfo['arr_dynamic_price']  = array();
        $aMultiDynamicPriceInfo['dynamic_price_info'] = array();
        $aMultiDynamicPriceInfo['dynamic_kind']       = 0;
        $aMultiDynamicPriceInfo['dynamic_type']       = 0;
        $aMultiDynamicPriceInfo['dynamic_version']    = 0;
        if (empty($sEstimateId)) {
            return $aMultiDynamicPriceInfo;
        }

        $this->orderEstimateNew = new OrderEstimateNew();
        $aAnyCarPrePrice        = $this->orderEstimateNew->getAnyCarPrePriceFromCache($sEstimateId); // AnyCar预估缓存的所有数据包括动调
        if (!isset($aAnyCarPrePrice['multi_info']) || empty($aAnyCarPrePrice['multi_info']) || !is_array($aAnyCarPrePrice['multi_info'])) {
            NuwaLog::warning(
                \BizLib\ErrCode\Msg::formatArray(
                    \BizLib\ErrCode\Code::E_PASSENGER_ANYCAR_GET_ESTIMATE_PRICE_ERROR,
                    array(
                        'estimate_cache_id' => $sEstimateId,
                        'district'          => $aOrderInfo['district'],
                        'oid'               => $aOrderInfo['order_id'],
                        'product_id'        => $aOrderInfo['product_id'],
                    )
                )
            );

            return $aMultiDynamicPriceInfo;
        }

        $iNotNeedDynamic = 1;
        $aMultiInfo      = AnyCarOrderLogic::getInstance()->getMultiBillInfo($aAnyCarPrePrice);
        $aAnyCarPrePrice = AnyCarOrderLogic::getInstance()->getSelectedEnabledProducts($aMultiInfo);
        foreach ($aAnyCarPrePrice as $aItem) {
            if (isset($aItem['dynamic_diff_price']) && $aItem['dynamic_diff_price'] > 0) {
                $iNotNeedDynamic = 0; // 如果所有产品线，都没有动调，则不需要
            }

            if (isset($aItem['expire_time']) && $aItem['expire_time'] <= time()) {// 动调缓存过期
                break;
            }

            $aDynamicPriceInfo = [
                'dynamic_price'             => $aItem['dynamic_diff_price'] ?? 0,
                'dynamic'                   => $aItem['dynamic'] ?? 0,
                'is_hit_dynamic_capping'    => $aItem['is_hit_dynamic_capping'] ?? false,
                'is_has_dynamic'            => $aItem['is_has_dynamic'] ?? 0,
                'dynamic_type'              => $aItem['dynamic_type'] ?? 0,
                'arrive_time'               => $aItem['arrive_time'] ?? 0,
                'rank_probability'          => $aItem['rank_probability'] ?? 0,
                'if_use_times'              => $aItem['if_use_times'] ?? 1,
                'dynamic_times'             => $aItem['dynamic_times'] ?? 0,
                'dynamic_price_id'          => $aItem['dynamic_price_id'] ?? '',
                'dynamic_discount_id'       => $aItem['dynamic_discount_id'] ?? '',
                'dynamic_discount'          => $aItem['dynamic_discount'] ?? 0,
                'dynamic_kind'              => $aItem['dynamic_kind'] ?? 0,
                'near_driver_num'           => $aItem['near_driver_num'] ?? 0,
                'near_order_num'            => $aItem['near_order_num'] ?? 0,
                'avg_driver_start_distance' => $aItem['avg_driver_start_distance'] ?? 0,
                'place'                     => $aItem['place'] ?? 0,
                'wait_discount'             => $aItem['wait_discount'] ?? 0,
                'wait_minute'               => $aItem['wait_minute'] ?? 10,
                'car_level'                 => $aItem['require_level'],
                'order_type'                => $aItem['order_type'],
                'start_dest_distance'       => $aItem['start_dest_distance'],
                'start_dest_time'           => $aItem['start_dest_time'],
            ];

            if (!empty($aItem['level_id'])) {
                $aDynamicPriceInfo['member'] = [
                    'level_id'        => $aItem['level_id'],
                    'dynamic_capping' => $aItem['member_dynamic_capping'] ?? -1,
                    'dynamic_priv_id' => $aItem['dynamic_priv_id'] ?? '',
                ];
            }

            // 跟李想确认播单策略已不用reward_show_all、reward_tts_all、reward_show_dynamic、reward_tts_dynamic
            //$aDynamicPriceInfo['reward_show_all'] = '';
            //$aDynamicPriceInfo['reward_tts_all']  = '';
            if (isset($aItem['if_use_times']) && 1 == $aItem['if_use_times']) {
                // if ($aItem['dynamic_times'] > 0) {
                   // $fDynamicTimes                            = $aItem['dynamic_times'] + 1;
                   // $aDynamicPriceInfo['reward_show_all']     = sprintf(self::REWARD_SHOW_CONTENT, $fDynamicTimes);
                   // $aDynamicPriceInfo['reward_tts_all']      = sprintf(self::REWARD_TTS_CONTENT, $fDynamicTimes);
                   // $aDynamicPriceInfo['reward_show_dynamic'] = sprintf(self::REWARD_SHOW_CONTENT, $fDynamicTimes);
                   // $aDynamicPriceInfo['reward_tts_dynamic']  = sprintf(self::REWARD_TTS_CONTENT, $fDynamicTimes);
               // }
            } else {
                $aDynamicPriceInfo['dynamic_times'] = 0;
            }

            $sGroupKey = implode('_', [$aItem['product_id'], $aItem['require_level'], $aItem['combo_type']]);
            $aMultiDynamicPriceInfo['dynamic_price_info'][$sGroupKey] = $aDynamicPriceInfo;
        }

        $aMultiDynamicPriceInfo['not_need_dynamic'] = $iNotNeedDynamic;

        return $aMultiDynamicPriceInfo;
    }

    //调用策略获取动态调价等信息
    private function _getOrderDynamicPriceInfo($orderInfo, $sImei, $iNotNeedDynamic, $iCarPool = false, $sEstimateTraceId = '') {
        $aDynamicPriceInfo = array();
        $aDynamicPriceInfo['arr_dynamic_price']  = array();
        $aDynamicPriceInfo['dynamic_price_info'] = array();
        $aDynamicPriceInfo['dynamic_kind']       = 0;
        $aDynamicPriceInfo['dynamic_type']       = 0;
        $aDynamicPriceInfo['dynamic_version']    = 0;
        $prefix      = UtilsCommon::getRedisPrefix(P_ESTIMATEPRICE_DYNAMIC_PRICE);
        $redisPrefix = md5($sImei.'_'.round($orderInfo['starting_lng'], 2).'_'.round($orderInfo['starting_lat'], 2).'_'.round($orderInfo['dest_lng'], 2).'_'.round($orderInfo['dest_lat'], 2)).'_'.$orderInfo['product_id'].'_'.$orderInfo['district'].'_';
        $sNewPrefix  = '';
        if (!empty($sEstimateTraceId)) {
            $sNewPrefix = UtilsCommon::getRedisPrefix(P_ESTIMATEPRICE_DYNAMIC_PRICE).$sEstimateTraceId.'_';   // new prefix 是新预估接口中使用的新的 prefix，使用 traceid 替代 md5
        }

        $arrDynamicPrice = $this->orderDynamicPrice->getDynamicPriceInfo($orderInfo['require_level'], array('channel' => $orderInfo['channel'], 'product_id' => $orderInfo['product_id'], 'area' => $orderInfo['area'], 'prefix' => $prefix.$redisPrefix, 'new_prefix' => $sNewPrefix));
        ksort($arrDynamicPrice);
        if (0 == count($arrDynamicPrice)) {
            NuwaLog::notice(
                Msg::formatArray(
                    Code::E_ORDER_DYNAMIC_PRICE_EMPTY,
                    array(
                        'district'    => $orderInfo['district'],
                        'oid'         => $orderInfo['order_id'],
                        'prefix'      => $prefix,
                        'redisPrefix' => $redisPrefix,
                        'new_prefix'  => $sNewPrefix,
                    )
                )
            );
        }

        if (!is_array($arrDynamicPrice) || empty($arrDynamicPrice)) {
            return $aDynamicPriceInfo;
        }

        $bIsCarpool = 1 == $iCarPool;
        foreach ($arrDynamicPrice as &$dynamicPrice) {
            $dynamicPrice = $bIsCarpool ? $dynamicPrice['carpool'] : $dynamicPrice['noncarpool'];

            //订单类型不一致,不允许发单(实时/预约)
            if ((int)($dynamicPrice['order_type']) != (int)($orderInfo['type'])) {
                $arrDynamicPrice = array();
                break;
            }

            if (isset($dynamicPrice['dynamic_kind'])) {
                $aDynamicPriceInfo['dynamic_kind'] = $dynamicPrice['dynamic_kind'];
            }

            //dynamic_type 动调已弃用,暂时保留以防下游崩溃, 不再做检查
            if (isset($dynamicPrice['dynamic_type'])) {
                $aDynamicPriceInfo['dynamic_type'] = $dynamicPrice['dynamic_type'];
            }

            if (isset($dynamicPrice['if_use_times']) && 1 == $dynamicPrice['if_use_times'] && 0 == $orderInfo['dynamic_version']) {
                $aDynamicPriceInfo['dynamic_version'] = 1;
            }

            $dynamicPrice['reward_show_dynamic'] = '';
            $dynamicPrice['reward_tts_dynamic']  = '';

            //DRIVER_NEGATIVE_DYNAMIC
            $fPassengerDynamicTimes = $dynamicPrice['dynamic_times'] ?? 0;
            $fDriverDynamicTimes    = max($fPassengerDynamicTimes, 0);
            if (1 == $aDynamicPriceInfo['dynamic_version'] && $fDriverDynamicTimes > 0 && 0 == (int)($iNotNeedDynamic)) {
                $fRewardDynamicTimes = $fDriverDynamicTimes + 1;
                $dynamicPrice['reward_show_all']     = sprintf(self::REWARD_SHOW_CONTENT, $fRewardDynamicTimes);
                $dynamicPrice['reward_tts_all']      = sprintf(self::REWARD_TTS_CONTENT, $fRewardDynamicTimes);
                $dynamicPrice['reward_show_dynamic'] = sprintf(self::REWARD_SHOW_CONTENT, $fRewardDynamicTimes);
                $dynamicPrice['reward_tts_dynamic']  = sprintf(self::REWARD_TTS_CONTENT, $fRewardDynamicTimes);
            } else {
                $dynamicPrice['reward_show_all'] = '';
                $dynamicPrice['reward_tts_all']  = '';
            }

            $iCarLevel = $dynamicPrice['car_level'];
            //乘客动调
            if (isset($dynamicPrice['dynamic_price']) && 0 != $dynamicPrice['dynamic_price'] && 0 == (int)($iNotNeedDynamic)) {
                $aDynamicPriceInfo['arr_dynamic_price'][$dynamicPrice['car_level']] = $dynamicPrice['dynamic_price'];
            } else {//异常处理
                $dynamicPrice['dynamic_type']  = 0;
                $dynamicPrice['dynamic_price'] = 0;
            }
        }

        $aDynamicPriceInfo['dynamic_price_info'] = $arrDynamicPrice;

        return $aDynamicPriceInfo;
    }

    private function _checkDynamicPrice($pid, $imei, $showMd5, $dynamicParams, $sEstimateTraceId) {
        $arrReturn   = array('errno' => 0,);
        $prefix      = UtilsCommon::getRedisPrefix(P_ESTIMATEPRICE_DYNAMIC_PRICE);
        $redisPrefix = md5($imei.'_'.round($dynamicParams['flng'], 2).'_'.round($dynamicParams['flat'], 2).'_'.round($dynamicParams['tlng'], 2).'_'.round($dynamicParams['tlat'], 2)).'_'.$dynamicParams['product_id'].'_'.$dynamicParams['district'].'_';
        $sNewPrefix  = '';
        if (!empty($sEstimateTraceId)) {
            $sNewPrefix = UtilsCommon::getRedisPrefix(P_ESTIMATEPRICE_DYNAMIC_PRICE).$sEstimateTraceId.'_';   // new prefix 是新预估接口中使用的新的 prefix，使用 traceid 替代 md5
        }

        $arrDynamicPrice = $this->orderDynamicPrice->getDynamicPriceInfo($dynamicParams['require_level'], array('prefix' => $prefix.$redisPrefix, 'new_prefix' => $sNewPrefix));
        $sCarLevel       = $dynamicParams['require_level'];
        $aDynamicPrice   = 1 == $dynamicParams['car_pool'] ? $arrDynamicPrice[$sCarLevel]['carpool'] : $arrDynamicPrice[$sCarLevel]['noncarpool'];
        $fEstimate       = 1 == $dynamicParams['car_pool'] ? $aDynamicPrice['carpool_estimate'] : $aDynamicPrice['default_estimate'];
        if ((int)($dynamicParams['order_type']) != (int)($aDynamicPrice['order_type'])) {
            return $arrReturn;
        }

        $dynamicPrice     = isset($aDynamicPrice['dynamic_price']) ? $aDynamicPrice['dynamic_price'] : 0;
        $iNewEstimateMark = $aDynamicPrice['new_estimate_mark'] ?? 0;   // 新预估接口的标记。新预估接口不再 check md5
        $arrReturn['not_need_dynamic'] = 0 != $dynamicPrice ? 0 : 1;
        if (0 != $dynamicPrice && 1 != $iNewEstimateMark && !$this->orderDynamicPrice->checkShowMd5($imei, $dynamicPrice, $showMd5)) {
            $arrReturn = UtilsCommon::getErrMsg(P_ERRNO_DYNAMIC_PRICE);
            NuwaLog::warning(sprintf('errno:'.P_ERRNO_DYNAMIC_PRICE.' | errmsg:dynamic price error pid:%s oldShowMd5:%s dynamicPriceInfo:%s', $pid, $showMd5, json_encode($arrDynamicPrice)));
        }

        return $arrReturn;
    }
}
