<?php

namespace PreSale\Models\picasso;

use BizLib\Client\PicassoJudgeClient;
use Nuwa\ApolloSDK\Apollo;

/**
 * 查询picasso的特征
 * Class PicassoSelectTag
 * @package PreSale\Models\picasso
 */
class PicassoSelectTag
{

    const NS = 'gs_picasso_tag_select_ns';

    const KEY = 'pre-sale_pMultiEstimatePrice';

    /**
     * 乘客TAG
     * @var int
     */
    private static $_aPassengerTagIds = -1;

    /**
     * 获取recognition需要的PicassoTag 的映射
     * @return string
     */
    private function _getPreSaleRecognitionNeedPicassoTag() {

        list($ok, $aConfigData) = Apollo::getInstance()->getConfigsByNamespaceAndConditions(self::NS,['key' => self::KEY])->getAllConfigData();

        if (!$ok || empty($aConfigData)) {
            return '';
        }

        $aRet = array_values($aConfigData);
        return implode(',',$aRet[0]['select_passenger_tags']);
    }


    /**
     * 获取用户拥有的的tag标签
     * @param string $sPassengerId $sPassengerId
     * @return array
     */
    public function getPicassoPassengerTagIds($sPassengerId) {

        if (empty($sPassengerId)) {
            return [];
        }

        if (-1 != self::$_aPassengerTagIds) {
            return self::$_aPassengerTagIds;
        }

        $sTagAll = $this->_getPreSaleRecognitionNeedPicassoTag();
        if (empty($sTagAll)) {
            self::$_aPassengerTagIds = [];
            return self::$_aPassengerTagIds;
        }

        $aRet = (new PicassoJudgeClient())->judge($sPassengerId,$sTagAll);
        self::$_aPassengerTagIds = [];
        if (empty($aRet) || empty($aRet['data']) || 0 != $aRet['errno']) {
            return self::$_aPassengerTagIds;
        }

        foreach ($aRet['data'] as $sTagId => $iValue) {
            if (1 == $iValue) {
                self::$_aPassengerTagIds[] = $sTagId;
            }
        }

        return self::$_aPassengerTagIds;
    }
}
