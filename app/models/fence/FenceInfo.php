<?php
namespace PreSale\Models\fence;

use BizCommon\Models\BaseModel;
use BizLib\Constants\OrderSystem;
use BizLib\Utils\Registry;
use BizCommon\Logics\Scene\SceneChooseLogic;

/**
 * Class FenceInfo
 * @package PreSale\Models\fence
 */
class FenceInfo extends BaseModel
{
    private static $_oInstance = null;

    /**
     * FenceInfo constructor.
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * @return mixed|FenceInfo|null
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * @param array $aOrderInfo     $aOrderInfo
     * @param array $aPassengerInfo $aPassengerInfo
     * @return array|mixed
     * @throws \Exception e
     */
    public function getMultiFenceInfo($aOrderInfo, $aPassengerInfo) {
        $iCacheProductId = OrderSystem::PRODUCT_ID_FAST_CAR;
        $sKey            = "getOtypeByAddrName_{$iCacheProductId}_{$aOrderInfo['from_lat']}_{$aOrderInfo['from_lng']}_{$aOrderInfo['to_lat']}_{$aOrderInfo['to_lng']}";
        $sFenceInfo      = Registry::getInstance()->get($sKey);
        $aFenceInfo      = json_decode($sFenceInfo, true);
        if (empty($aFenceInfo)) {
            $oSceneChoose = new SceneChooseLogic($aPassengerInfo['pid'], $aPassengerInfo['phone']);
            $aFenceInfo   = $oSceneChoose->getMultiFenceInfo(
                $aOrderInfo['starting_name'],
                $aOrderInfo['dest_name'],
                $aOrderInfo['from_lat'],
                $aOrderInfo['from_lng'],
                $aOrderInfo['to_lat'],
                $aOrderInfo['to_lng'],
                $aOrderInfo['product_id']
            );
            Registry::getInstance()->set($sKey, json_encode($aFenceInfo));
        }

        return $aFenceInfo;
    }
}
