<?php

namespace PreSale\Models\rpc;

use BizLib\Utils\LogHelper;
use Disf\SPL\Trace;
/***************************************************************************
 * 获取popefs特征model
 *
 * Copyright (c) 2018 xiaojukeji.com, Inc. All Rights Reserved
 * <AUTHOR>
 * @version 2018-11-27
 *
 **************************************************************************/
use BizLib\Client\PopefsClient;
use Dirpc\SDK\Popefs;

class PopefsRpc
{
    /**
     * @var _oClient|null
     */
    private $_oClient = null;

    /**
     * @var $_aFeatureFileds|null
     *                             该部分特征若有修改，需在dds上同步一份，用于兜底
     */
    private $_aFeatureFileds = array(
        'demand_feature.last_diversion_time',
        'demand_feature.diversion_interval',
        'demand_feature.is_accept_diversion',
        'demand_feature.finish_order_count',
        'demand_feature.daily_pay_amount',
        'demand_feature.call_order_count',
        'demand_feature.purchase_power',
        'demand_feature.high_potential_products',
        'demand_feature.real_time_response_rate',
        'demand_feature.average_response_time',
        'demand_feature.car_pool_success_rate',
        'demand_feature.queue_length',
        'demand_feature.congestion_index',
        'demand_feature.user_portrait',
        'demand_feature.area_order_count',
        'demand_feature.activity_analysis',
        'demand_feature.call_carpool',
        'demand_feature.call_carpool_station',
        'ufs.passenger.carpool_fresh_educated',
        'demand_feature.call_special',
        'ufs.passenger.special_fresh_educated',
        'ufs.passenger.product_has_bubbled',
        'ufs.passenger.new_youxiang_showed',
        'ufs.passenger.ihap_cashpayment.can_use_cash_payment',
        'ufs.passenger.carpool_long.order_fresh',
        'ufs.passenger.taxi.station_carpool_finish_cnt',
        'ufs.passenger.carpool_long.create_count',
        'ufs.passenger.taxi.carpool_fresh_educated',
        'ufs.passenger.carpool_dual_price_fresh_educated',
        'ufs.passenger.special_taxi.is_last_order',
        'ufs.passenger.commute_card.last_buy_time',
        'ufs.passenger.commute_card.present_times',
        'ufs.passenger.commute_card.continuous_buy_times',
        'ufs.passenger.carpool.hold_type',
        'ufs.passenger.carpool.activity_hit_cnt',
        'ufs.passenger.carpool.activity_call',
        'ufs.passenger.carpool_new_price.finish_count',
        'ufs.passenger.carpool_new_price.create_count',
        // 一键叫活动类型 0 非一键叫 1 一键叫 2 雄安智慧公交
        'ufs.passenger.carpool.x_activity_type',
        'ufs.passenger.privilege_groups',
        'ufs.passenger.statistics.carpool_success.order_cnt',
        'ufs.passenger.statistics.aplus.finish_order_cnt',
        'ufs.passenger.statistics.carpool_joy_finish_cnt',//拼成乐累计完单量
        'ufs.passenger.statistics.carpool_joy_30d_history_finish_cnt', //拼成乐30天完单量，
        'ufs.passenger.fastcar.last_90d_finish_cnt',//快车近90天完单量
        'ufs.passenger.carpool.last_90d_finish_cnt',//拼车近90天完单量
        'ufs.passenger.new_long_rent.finish_order_car_level', // 新包车上一笔完单车型
//        'ufs.passenger.statistics.carpool.last_90d_inactive',//快车（不包含优享）90天有完单且市内拼车90天内无完单的用户
//        'ufs.passenger.statistics.carpool.last_60d_inactive',//快车（不包含优享）60天有完单且市内拼车90天内无完单的用户
//        'ufs.passenger.statistics.carpool.last_30d_inactive',//快车（不包含优享）30天有完单且市内拼车90天内无完单的用户
        //该部分特征若有修改，需在dds上同步一份，用于兜底,坑!!!
        // 以下特征不用于dds决策默认选中、排序，不需要同步到dds
        'ufs.passenger.reply_preference_setting', // anycar乘客选择的应答偏好设置
    );

    const PASSENGER_SERVICE_TAG_PREFIX = 'pope.tagservice.';
    /**
     * PopefsRpc constructor.
     */
    public function __construct() {
        $this->_oClient = new PopefsClient();
    }

    /**
     * 批量获取popefs特征
     *
     * @param $aPopefsReq
     */
    public function getPopefsMFeatures($aPopefsReq) {
        $aLocInfo    = array('start_loc' => array('lat' => $aPopefsReq['lat'], 'lng' => $aPopefsReq['lng']));
        $aParams     = array(
            'pid'                                 => (int)($aPopefsReq['pid']),
            'route_id'                            => $aPopefsReq['route_id'],
            'demand_feature.location'             => json_encode($aLocInfo, true),
            'demand_feature.user_portrait_params' => '{"stat_demographic":"1","stat_order":"1","stat_order_time":"1", "stat_car_type":"1", "stat_car_type_time":"1"}',
            'area_order_count_params.longitude'   => $aPopefsReq['lng'],
            'area_order_count_params.latitude'    => $aPopefsReq['lat'],
            'area_order_count_params.type'        => 'stat_order,stat_car_type',
            'activity_id'                         => '*',
            'activity_key'                        => '*',
            'date'                                => date('Ymd', time()),
        );
        $aMGetParams = new Popefs\MGetParams();
        $aMGetParams->params = $aParams;
        $this->addFeatures($aPopefsReq['service_tag']);
        $aMGetParams->features = $this->_aFeatureFileds;
        $aTraceInfo            = new Popefs\Trace();
        $aTraceInfo->traceID   = Trace::traceId();
        $aTraceInfo->caller    = MODULE_NAME;
        $aTraceInfo->spanID    = Trace::spanId();
        // $aTraceInfo->srcMethod = $_SERVER['REQUEST_URI'];
        $aTraceInfo->srcMethod   = '';
        $aTraceInfo->hintCode    = LogHelper::getHintCode();
        $aTraceInfo->hintContent = Trace::hintContent();
        $oMGetResult = $this->_oClient->mGetFeatures($aMGetParams, $aTraceInfo);
        $sMGetResult = json_encode($oMGetResult);
        $aMGetResult = json_decode($sMGetResult, true);

        return $aMGetResult;
    }

    /**
     * @param array $aServiceTag ServiceTag
     * @return void
     */
    public function addFeatures($aServiceTag) {
        $aFeatures = [];
        if (empty($aServiceTag)) {
            return;
        }

        foreach ($aServiceTag as $value) {
            $aValueTag = explode(',', $value);
            foreach ($aValueTag as $sItem) {
                array_push($aFeatures, self::PASSENGER_SERVICE_TAG_PREFIX.$sItem);
            }
        }

        if (!empty($aFeatures)) {
            $this->_aFeatureFileds = array_merge($this->_aFeatureFileds, $aFeatures);
        }
    }

    /**
     * @Desc:
     * @param mixed[] $oPopeFsResult Array structure to count the elements of.
     * @return array
     * @property formatResponse $formatResponse
     * @Author:<EMAIL>
     */
    public function formatResponse($oPopeFsResult) {
        if (empty($oPopeFsResult)|| empty($oPopeFsResult['data']) ||empty($oPopeFsResult['data']['featureMap'])) {
            return [];
        }

        $aMap = $oPopeFsResult['data']['featureMap'];
        $aRet = [];
        foreach ($this->_aFeatureFileds as $k) {
            if (!empty($k) && isset($aMap[$k])) {
                $aRet[$k] = $aMap[$k]['val'];
            }
        }

        return $aRet;
    }
}
