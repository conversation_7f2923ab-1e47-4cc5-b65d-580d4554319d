<?php

namespace PreSale\Models\rpc;

use BizLib\Client\ClientTimeOutConfig;
use Dirpc\SDK\Hundun\Client;
use Dirpc\SDK\Hundun\SceneReq;

/**
 * Class 券rpc
 * @package CouponRpc
 */
class HundunRpc
{
    use ClientTimeOutConfig;

    const MODULE_NAME = Client::MODULE_NAME;
    private $_oDirpcClient;

    protected static $_oInstance = null;


    /**
     * @return HundunRpc
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * HundunClient constructor.
     * @param string $sServiceName name
     * @param array  $aConfig      config
     */
    public function __construct($sServiceName = self::MODULE_NAME, $aConfig = []) {
        $aSdkConfig          = array();
        $aSdkConfig          = array_merge($aSdkConfig, $aConfig);
        $this->_oDirpcClient = new Client($sServiceName, $aSdkConfig);
    }

    /**
     * 通过hundun获得增值服务
     *
     * @param SceneReq $aReqParam 请求增值服务入餐
     * @return array 返回增值服务结果
     */
    public function getServiceList(SceneReq $aReqParam) {
        $aHunDunResult = $this->_oDirpcClient->GetNewScene($aReqParam);
        if (0 != $aHunDunResult['errno'] || !isset($aHunDunResult['errno'])) {
            // hundun响应有问题
            return [];
        }

        $aResponse = $aHunDunResult['data'];
        if (empty($aResponse) || !is_array($aResponse)) {
            // hundun响应有问题
            return [];
        }

        return $aResponse;
    }
}
