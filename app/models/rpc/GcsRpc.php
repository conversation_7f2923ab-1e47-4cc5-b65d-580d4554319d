<?php
/**
 * Created by PhpStorm.
 * User: didi
 * Date: 2018/7/11
 * Time: 上午11:29.
 */

namespace PreSale\Models\rpc;

use BizLib\Log as NuwaLog;
use BizLib\Client\GcsClient;
use BizLib\ErrCode;

class GcsRpc
{
    const CALLER = 'gulfstream';
    public static function getGridInfo($flat, $flng, $tlat, $tlng) {
        $gcsClient = new GcsClient(GcsClient::MODULE_NAME, [], self::CALLER);
        $aResult   = $gcsClient->getGrid($flat, $flng, $tlat, $tlng);
        if (empty($aResult)) {
            NuwaLog::warning(ErrCode\Msg::formatArray(ErrCode\Code::E_COMMON_HTTP_READ_FAIL, ['gcs_result' => json_encode($aResult)]));

            return [];
        }

        return $aResult;
    }
}
