<?php
namespace PreSale\Models\rpc;

use BizLib\Client\PassportClient;
use BizLib\Exception\ExceptionWithResp;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\RespCode;
use BizLib\Utils\UtilHelper;

/**
 * Class PassportRpc
 * @package PreSale\Models\rpc
 */
class PassportRpc
{
    const ERRNO_OK = 0;

    const HTTP_STATUS_SUCCESS = 200;

    const ERRNO_INVALID_TOKEN = -201;

    /**
     * function getPassengerInfoByToken
     * @param  string $sToken token
     * @throws ExceptionWithResp  With response errno
     * @desc   如果访问passport失败，需要继续run下去，需要在logic层包一下，接收throw
     * @return array
     */
    public static function getPassengerInfoByToken($sToken) {
        if (!is_string($sToken)) {
            throw new ExceptionWithResp(
                Code::E_COMMON_TOKEN_INVALID,
                RespCode::P_PARAMS_ERROR
            );
        }

        $oClient = new PassportClient();
        $aResult = $oClient->validateToken($sToken);
        // 系统层面错误
        if (self::ERRNO_OK != $aResult['errno'] || self::HTTP_STATUS_SUCCESS != $aResult['code']) {
            throw new ExceptionWithResp(
                Code::E_COMMON_HTTP_PASSPORT_PASSENGER_ERROR,
                RespCode::P_PARAMS_ERROR
            );
        }

        // token失效
        if (self::ERRNO_INVALID_TOKEN == $aResult['result']['errno']) {
            throw new ExceptionWithResp(
                Code::E_COMMON_TOKEN_INVALID,
                RespCode::P_TOKEN_ERROR
            );
        }

        // 参数错误
        if (self::ERRNO_OK != $aResult['result']['errno']) {
            throw new ExceptionWithResp(
                Code::E_COMMON_TOKEN_INVALID,
                RespCode::P_PARAMS_ERROR
            );
        }

        return [
            'uid'          => $aResult['result']['uid'],
            'duid'         => $aResult['result']['duid'],
            'pid'          => UtilHelper::getPassengerIdByUidNew($aResult['result']['uid']),
            'phone'        => $aResult['result']['country_calling_code'] . $aResult['result']['cell'],
            'channel'      => $aResult['result']['channel'],
            'status'       => 0,
            'appid'        => $aResult['result']['appid'],
            'role'         => $aResult['result']['role'],
            'country_code' => $aResult['result']['country_calling_code'],
        ];
    }
}
