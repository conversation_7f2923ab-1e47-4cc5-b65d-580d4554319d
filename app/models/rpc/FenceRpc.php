<?php

namespace PreSale\Models\rpc;

use BizLib\Client\GeoFenceClientNew;
use BizLib\Config as NuwaConfig;
use BizLib\Log as NuwaLog;
use Disf\SPL\Trace;

/**
 * Class FenceRpc
 * @package PreSale\Models\rpc
 */
Class FenceRpc
{
    protected static $_oInstance = null;

    /**
     * @return FenceRpc
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * @desc 获取坐标所在的围栏信息
     * @param array $aCoordinates 坐标
     * @param array $aGroupId     groupid
     * @return array
     */
    public function getMultiInFence($aCoordinates, $aGroupId) {
        $sTraceId      = Trace::traceId();
        $sServiceToken = NuwaConfig::config('config_url', 'fenceservice_token');
        $oGeoFence     = new GeoFenceClientNew();
        $aGeoFenceRet  = $oGeoFence->getMultiInFence($aCoordinates, $aGroupId, $sTraceId, $sServiceToken);
        if (isset($aGeoFenceRet['errno']) && 0 == $aGeoFenceRet['errno']) {
            $aResult = $aGeoFenceRet['fence_info'] ?? [];
            return $aResult;
        }

        NuwaLog::warning('get fence list error:%s'.json_encode($aGeoFenceRet));
        return [];
    }
}
