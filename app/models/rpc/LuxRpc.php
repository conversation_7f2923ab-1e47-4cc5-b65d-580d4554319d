<?php

/**
 * Created by PhpStorm.
 * <AUTHOR> <<EMAIL>>
 * Date: 2018/9/22
 * Time: 下午8:06.
 */

namespace PreSale\Models\rpc;

use BizLib\Client\MerlinClient;
use BizLib\Config as NuwaConfig;
use BizLib\Log;
use BizLib\Log as NuwaLog;
use BizLib\Utils\CurlHelper;
use BizLib\ErrCode\Msg;
use BizLib\ErrCode\Code;
use Xiaoju\Apollo\Apollo;
use PreSale\Logics\estimatePrice\OptionServiceLogic;

/**
 * Kop SDK.
 *
 * 用于获取乘客个性化偏好设置.
 *
 * <AUTHOR> <<EMAIL>>
 */
class LuxRpc
{
    const GET_PREFER_60    = 'gs.l.prefer.60.2';
    const UPDATE_PREFER_60 = 'gs.l.prefer.60.update.internal';

    protected static $_oInstance       = null;
    protected $aAssignDriverSortedList = [];

    /**
     * @return LuxRpc
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * 获取偏好司机列表
     * @param int    $iUserId        用户id
     * @param int    $iAreaId        业务参数
     * @param array  $aCoordInfo     坐标参数
     * @param int    $iDepartureTime 出发时间
     * @param int    $iOrderType     订单类型
     * @param string $sLang          语言
     * @param bool   $bClosePrefer   是否关闭偏好
     * @param int    $iAccessKeyId   端来源标识
     * @param string $sAppVersion    端版本
     * @return array
     **/
    public function getAssignDriverList(
        $iUserId,
        $iAreaId,
        $aCoordInfo,
        $iDepartureTime,
        $iOrderType,
        $sLang,
        $bClosePrefer,
        $iAccessKeyId,
        $sAppVersion
    ) {
        $aData = [];

        $iPid       = \BizLib\Utils\UtilHelper::getPassengerIdByUidNew($iUserId);
        $aApiParams = [
            'uid'             => $iUserId,
            'areaId'          => $iAreaId,
            'from_lat'        => $aCoordInfo['from_lat'],
            'from_lng'        => $aCoordInfo['from_lng'],
            'from_name'       => $aCoordInfo['from_name'],
            'to_lat'          => $aCoordInfo['to_lat'],
            'to_lng'          => $aCoordInfo['to_lng'],
            'to_name'         => $aCoordInfo['to_name'],
            'departure_time'  => $iDepartureTime,
            'order_type'      => $iOrderType,
            'lang'            => $sLang,
            'lang_str'        => $sLang,
            'city_id'         => $iAreaId,
            'is_close_prefer' => $bClosePrefer,
            'access_key_id'   => $iAccessKeyId,
            'app_version'     => $sAppVersion,
        ];
        $oToggle    = \Nuwa\ApolloSDK\Apollo::getInstance()->featureToggle(
            'gs_preference_driver_switch',
            [
                'pid'  => $iPid,
                'city' => $iAreaId,
                'key'  => time(),
            ]
        );
        if ($oToggle->allow()) {
            $aRet  = (new MerlinClient())->getPreferenceDriverList($aApiParams);
            $aData = $this->_parseAssignDriverResult($aRet, $aApiParams);
            if (empty($this->aAssignDriverSortedList)) {
                if (!empty($aData) && isset($aData['driverList']) && !empty($aData['driverList'])) {
                    $this->aAssignDriverSortedList = $aData['driverList'];
                }
            }
        }

        return $aData;
    }

    /**
     * get 返回结果
     * @param array $aRet   aRet
     * @param array $aParam aParam
     * @return array
     */
    private function _parseAssignDriverResult($aRet, $aParam) {
        if (0 != $aRet['errno']) {
            NuwaLog::warning(Msg::formatArray(Code::E_REQUEST_MERLIN_GET_PREFERENCE_DRIVER_FAIL, ['req' => json_encode($aParam), 'resp' => json_encode($aRet)]));
            return [];
        }

        $aRet['data']['driverList'] = array();
        foreach ($aRet['data']['driver_list'] as $k => $arrDriver) {
            $aRet['data']['driverList'][] = array(
                'driverId'       => $arrDriver['driver_id'],
                'is_real_driver' => $arrDriver['is_real_driver'],
                'isonline'       => $arrDriver['is_online'],
                'profile'        => $arrDriver['profile'],
                'headImg'        => $arrDriver['head_img'],
                'profileLink'    => $arrDriver['profile_link'],
                'requireLevel'   => $arrDriver['require_level'],
                'driverDesc'     => $arrDriver['driver_desc'],
                'driverDescLink' => $arrDriver['driver_desc_link'],
            );
        }

        unset($aRet['data']['driver_list']);
        return $aRet['data'];
    }



    /**
     * 获取司机排序列表
     * @return array
     **/
    public function getAssignDriverSortedList() {
        return $this->aAssignDriverSortedList;
    }

    /**
     * @param array $aParams $aParams
     * @return array
     */
    public function updateUserPreferencesInfo($aParams) {
        if (OptionServiceLogic::isOptionServiceToHundun($aParams)) {
            OptionServiceLogic::submitOptionService($aParams);
            return [];
        }

        // 切换Merlin
        return $this->_updatePreference60ToMerlin($aParams['uid'], $aParams['options']);
    }

    /**
     * 获取用户偏好设置
     * @param int    $iUserId 用户ID
     * @param string $sLang   语言
     * @return array
     **/
    public function getUserPreferencesInfo($iUserId, $sLang) {
        return $this->_getPreference60FromMerlin($iUserId, $sLang);
    }

    /**
     * 获取用户偏好设置
     * @param array $aParam 参数
     * @return array
     **/
    private function _updateLuxApiParams($aParam) {
        $aParam['apiVersion'] = '1.0.0';
        $aParam['appVersion'] = '1.0.0';
        $aParam['ttid']       = '1.0.0';
        $aParam['osType']     = '1.0.0';
        $aParam['osVersion']  = '1.0.0';
        $aParam['hwId']       = '1.0.0';
        $aParam['mobileType'] = '1.0.0';
        $aParam['timestamp']  = floor(microtime(true) * 1000);
        $aParam['appKey']     = NuwaConfig::config('config_url', 'kop_app_key');
        return $aParam;
    }

    /**
     * 是否是合约车用户
     * @param array  $aPlatParams kop参数
     * @param array  $aBizParams  业务参数
     * @param string $sUrl        url
     * @return array
     **/
    private function _requestToKop($aPlatParams, $aBizParams, $sUrl) {
        $aOpt = [
            CURLOPT_TIMEOUT_MS        => 300,
            CURLOPT_NOSIGNAL          => true,
            CURLOPT_CONNECTTIMEOUT_MS => 500,
            CURLOPT_POST              => 1,
            CURLOPT_HTTPHEADER        => ['Content-type: text/plain'],
        ];
        $aPlatParams['sign'] = $this->_genKopSign($aPlatParams, $aBizParams);
        $sBizParams          = json_encode($aBizParams);
        //$sUrl = 'http://10.179.161.59:8083/gateway';
        $aPlatParams['appKey'] = $aPlatParams['appKey'];
        $sUrl .= '?' . http_build_query($aPlatParams);

        $aRet = CurlHelper::curl($sUrl, $sBizParams, $aOpt, 1);

        return $aRet;
    }

    /**
     * 解析结果
     * @param array  $aRet   kop返回
     * @param array  $aParam 业务参数
     * @param string $sUrl   url
     * @return array
     **/
    private function _parsePreFerResult($aRet, $aParam, $sUrl) {
        if (!empty($aRet) && 0 == $aRet['errno'] && isset($aRet['ret'])) {
            if (empty($aRet['ret'])) {
                NuwaLog::info(sprintf('Request kop api failed.||platParam=%s||uri=%s||resp=%s', json_encode($aParam),$sUrl,json_encode($aRet)));
                return [];
            }

            NuwaLog::info(sprintf('Request kop success.||platParam=%s||uri=%s||resp=%s', json_encode($aParam),$sUrl,json_encode($aRet)));
            return $this->_parseResult(json_decode($aRet['ret'], true), $aParam, $sUrl);
        } else {
            NuwaLog::info(sprintf('Request kop failed.||platParam=%s||uri=%s', json_encode($aParam),$sUrl));
        }

        return [];
    }

    /**
     * 解析结果
     * @param array  $aRet   kop返回
     * @param array  $aParam 业务参数
     * @param string $sUrl   url
     * @return array
     **/
    private function _parseResult($aRet, $aParam, $sUrl) {
        if (!empty($aRet) && 200 == $aRet['code'] && isset($aRet['data'])) {
            if (empty($aRet['data'])) {
                NuwaLog::info(sprintf('Request kop api failed.||platParam=%s||uri=%s||resp=%s', json_encode($aParam),$sUrl,json_encode($aRet)));
                return [];
            }

            NuwaLog::info(sprintf('Request kop success.||platParam=%s||uri=%s||resp=%s', json_encode($aParam),$sUrl,json_encode($aRet)));
            return $aRet['data'];
        } else {
            NuwaLog::info(sprintf('Request kop failed.||platParam=%s||uri=%s', json_encode($aParam),$sUrl));
        }

        return [];
    }

    /**
     * 获取签名
     * @param array $aPlatParams kop参数
     * @param array $aBizParams  业务参数
     * @return string
     **/
    private function _genKopSign(array $aPlatParams, array $aBizParams) {
        //$sAppSec = '0b2c2f5bd7614598914269e98b1d34fc'; //兜底
        //$sAppSec = '5503e54cc39f460d91c8cfb1957380e5';
        $sAppSec = NuwaConfig::config('config_url', 'kop_app_sec');

        $aSign = array_merge($aPlatParams, $aBizParams);
        foreach ($aSign as $k => $v) {
            if (null == $v) {
                unset($aSign[$k]);
            }
        }

        ksort($aSign);
        $sSign = $sAppSec;
        foreach ($aSign as $k => $v) {
            $sSign .= $k.$v;
        }

        return md5($sSign.$sAppSec);
    }

    /**
     * @param int    $iUserId 用户uid
     * @param string $sLang   语言
     * @return array
     */
    private function _getPreference60FromMerlin($iUserId, $sLang) {
        $aParams       = [
            'uid'  => $iUserId,
            'lang' => $sLang,
        ];
        $oMerlinClient = new MerlinClient();
        $aRet          = $oMerlinClient->getPreference60($aParams);
        $aMerlinRet    = [];
        if (isset($aRet['errno']) && 0 == $aRet['errno']) {
            if (isset($aRet['data'])) {
                $aMerlinRet = $aRet['data'];
            }
        } else {
            Log::warning(
                Msg::formatArray(
                    Code::E_REQUEST_MERLIN_GET_PREFER_60_FAIL,
                    [
                        'params' => json_encode($aParams),
                        'ret'    => json_encode($aRet),
                    ]
                )
            );
        }

        return $aMerlinRet;
    }

    /**
     * @param int    $iUid    用户uid
     * @param string $sOption 偏好选项
     * @return array
     */
    private function _updatePreference60ToMerlin($iUid, $sOption) {
        $aParams       = [
            'uid'     => $iUid,
            'options' => $sOption,
        ];
        $oMerlinClient = new MerlinClient();
        $aRet          = $oMerlinClient->updatePreference60($aParams);
        $aMerlinRet    = [];
        if (isset($aRet['errno']) && 0 == $aRet['errno']) {
            $aMerlinRet = $aRet;
        } else {
            Log::warning(
                Msg::formatArray(
                    Code::E_REQUEST_MERLIN_UPDATE_PREFER_60_FAIL,
                    [
                        'params' => json_encode($aParams),
                        'ret'    => json_encode($aRet),
                    ]
                )
            );
        }

        return $aMerlinRet;
    }
}
