<?php


namespace PreSale\Models\rpc;

use \BizLib\Client\UfsClient;

/**
 * Class UfsRpc
 * @package Rpc
 */
class UfsRpc
{
    /**
     * @param string $sStartGrid start_grid
     * @param string $sDestGrid  dest_grid
     * @param array  $aConfIds   conf_ids
     * @return array
     */
    public static function getUfsMultiFeature($sStartGrid, $sDestGrid, $aConfIds) {
        $oUfsClient = new UfsClient();

        $aFeatures = ['carpool.grid_discount',];

        $aUfsKeys = [];
        foreach ($aConfIds as $iConf) {
            $aCondition = [
                'start_grid_id' => $sStartGrid,
                'conf_id'       => $iConf,
                'end_grid_id'   => $sDestGrid,
            ];
            $sUfskey    = array_values($oUfsClient->buildGetFeaturesKeyMap($aFeatures, $aCondition, 'driver'));
            $aUfsKeys   = array_merge($aUfsKeys, $sUfskey);
        }

        $aResult = $oUfsClient->mget($aUfsKeys);
        if (empty($aResult) || 0 != $aResult['errno'] || empty($aResult['result'])) {
            return [];
        }

        foreach ($aConfIds as $index => $iConf) {
            if (!empty($aResult['result'][$aUfsKeys[$index]])) {
                return $aResult['result'][$aUfsKeys[$index]];
            }
        }

        return [];
    }
}
