<?php

namespace PreSale\Models\rpc;

use BizLib\Client\CouponClient;
use BizLib\Config as NuwaConfig;
use BizLib\Log as NuwaLog;
use BizLib\Utils\LogHelper;
use Disf\SPL\Trace;
use BizLib\Client\PopefsClient;
use Dirpc\SDK\Popefs;

/**
 * Class 券rpc
 * @package CouponRpc
 */
class CouponRpc
{
    const SIGN = 'Dd#4r8coupon?_7rfG@';

    protected static $_oInstance = null;


    /**
     * @return CouponRpc
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * @desc 通过batch_id去查券系统
     * @param int    $iBatchId 券批次
     * @param string $slang    语言
     * @return array
     */
    public function getCouponInfo($iBatchId, $slang) {
        $oCouponClient = new CouponClient();
        $aParam        = [
            'batchid' => $iBatchId,
            'lang'    => $slang,
            'sign'    => '',
        ];

        $queryString = '';
        unset($aParam['sign']);
        if (is_array($aParam) && !empty($aParam) && ksort($aParam)) {
            foreach ($aParam as $key => $value) {
                if (!empty($value)) { // 注意：php 中empty的含义是：0, "", "0", null 都不能与签名计算
                    $queryString .= ("$key=$value" . '&');
                }
            }
        }

        // 生成签名：mb_strtoupper(md5(原字符串&key=md(密钥)))
        $aParam['sign'] = mb_strtoupper(md5($queryString . 'key=' . md5(self::SIGN)));
        $oBatchResult   = $oCouponClient->getBatch($aParam);
        if (empty($oBatchResult) || !isset($oBatchResult['errno']) || (0 != $oBatchResult['errno'])) {
            //查券mis异常
            NuwaLog::warning('get coupon system error:'.json_encode($oBatchResult));
            return[];
        }

        $oBatchData = $oBatchResult['info'];
        if (empty($oBatchData) || empty($oBatchData['batchinfo'])) {
            //查券mis异常
            NuwaLog::warning('get coupon system error:'.json_encode($oBatchResult));
            return[];
        }

        return $oBatchData;
    }
}
