<?php
namespace PreSale\Models\pixels;

use BizLib\Constants\Common as ConstantsCommon;

class Pixels
{

    const SMALL_SCREEN  = 'small';
    const MEDIUM_SCREEN = 'medium';
    const LARGE_SCREEN  = 'large';

    /**
     * 是否是小屏幕机型
     * @param int $iAccessKeyId access_key_id
     * @param int $iWidth       width
     * @param int $iHeight      height
     * @return bool
     */
    public static function IsSmallScreen($iAccessKeyId, $iWidth, $iHeight) {
        if ($iHeight == 0) {
            return false;
        }

        $whRation         = $iWidth / $iHeight;
        $whRatioThreshold = -1;
        $hpxThreshold     = -1;

        switch ($iAccessKeyId) {
            case ConstantsCommon::DIDI_ANDROID_PASSENGER_APP:
                // 安卓应用，只根据屏幕高度判断
                $hpxThreshold = 1280;
                break;
            case ConstantsCommon::DIDI_IOS_PASSENGER_APP:
                // iOS应用，根据宽高比判断
                $whRatioThreshold = 0.563;
                break;

            case ConstantsCommon::DIDI_WECHAT_MINI_PROGRAM:
                // no break
            case ConstantsCommon::DIDI_ALIPAY_MINI_PROGRAM:
                // 微信小程序、支付宝小程序，设置宽高比阈值
                $whRatioThreshold = 0.563;
                $hpxThreshold     = 1280;
                break;

            default:
                // 其他情况均为非小屏幕
                return false;
        }

        if ($whRatioThreshold < 0 || $whRation >= $whRatioThreshold) {
            if ($hpxThreshold < 0 || $iHeight <= $hpxThreshold) {
                return true;
            }
        }

        return false;
    }

    /**
     * 是否是中屏机型
     * @param int $iAccessKeyId access_key_id
     * @param int $iWidth       width
     * @param int $iHeight      height
     * @return bool
     */
    public static function IsMediumScreen($iAccessKeyId, $iWidth, $iHeight) {
        if ($iHeight == 0) {
            return false;
        }

        $whRation = $iWidth / $iHeight;

        switch ($iAccessKeyId) {
            case ConstantsCommon::DIDI_IOS_PASSENGER_APP:
                return $whRation < 0.563 && $whRation > 0.47;

            case ConstantsCommon::DIDI_ANDROID_PASSENGER_APP:
                return $iHeight <= 1980 && $iHeight > 1280;

            case ConstantsCommon::DIDI_WECHAT_MINI_PROGRAM:
                // no break
            case ConstantsCommon::DIDI_ALIPAY_MINI_PROGRAM:
                return $iHeight <= 1980 && $iHeight > 1280 && $whRation < 0.563;

            default:
                return false;
        }
    }

    /**
     * 是否是大屏机型
     * @param int $iAccessKeyId access_key_id
     * @param int $iWidth       width
     * @param int $iHeight      height
     * @return bool
     */
    public static function IsLargeScreen($iAccessKeyId, $iWidth, $iHeight) {
        if ($iHeight == 0) {
            return false;
        }

        $whRation = $iWidth / $iHeight;

        switch ($iAccessKeyId) {
            case ConstantsCommon::DIDI_IOS_PASSENGER_APP:
                return $whRation <= 0.47;

            case ConstantsCommon::DIDI_ANDROID_PASSENGER_APP:
                return $iHeight > 1980;

            case ConstantsCommon::DIDI_WECHAT_MINI_PROGRAM:
                // no break
            case ConstantsCommon::DIDI_ALIPAY_MINI_PROGRAM:
                return $iHeight > 1980 && $whRation < 0.51;

            default:
                return false;
        }
    }

    /**
     * @param int $iAccessKeyId access_key_id
     * @param int $iWidth       width
     * @param int $iHeight      height
     * @return string
     */
    public static function GetScreenSize($iAccessKeyId, $iWidth, $iHeight) {
        if (self::IsSmallScreen($iAccessKeyId, $iWidth, $iHeight)) {
            return self::SMALL_SCREEN;
        }

        if (self::IsMediumScreen($iAccessKeyId, $iWidth, $iHeight)) {
            return self::MEDIUM_SCREEN;
        }

        if (self::IsLargeScreen($iAccessKeyId, $iWidth, $iHeight)) {
            return self::LARGE_SCREEN;
        }

        // 兜底中屏
        return self::MEDIUM_SCREEN;
    }
}
