<?php
/**
 * 登录用户打开app时的请求，这个主要是pGetWanliuinfo接口时调用，pGetFlag的android的6.0不再请求了……
 * <AUTHOR> <<EMAIL>>
 * @date 2020-04-28 20:34:34
 */
namespace PreSale\Models\carrera;

use BizCommon\Models\Carrera\PassengerTopic;

/**
 * Class LoginPassengerOpenAppTopic
 * @package PreSale\Models\carrera
 */
class LoginPassengerOpenAppTopic extends PassengerTopic
{
    const TOPIC = 'wanliu_login_passenger_open_app';
    const TYPE  = 21011;

    // 理想消息格式
    private static $_aFormat = [
        'biztype'  => 'integer', // 258专车, 268试乘试驾
        'role'     => 'integer', // 1司机 2乘客 3订单
        'uid'      => 'integer', // 64位司机ID
        'gpid'     => 'string',
        'lng'      => 'double', // 经度
        'lat'      => 'double', // 纬度
        'phone'    => 'string', // 乘客手机号
        'city_id'  => 'integer', // 城市id
        'district' => 'string',//区号
    ];

    /**
     * @param array $aParams params
     * @return void
     */
    public function sync($aParams) {
        if (empty($aParams)) {
            return;
        }

        $aData = $this->_fillData($aParams);

        $iHashKey = (int)($aData['gpid']); // 64位整型driver_id

        // 参数校验
        static::formatCheck(self::TOPIC, $iHashKey, $aData, static::$_aFormat);

        static::send($aData, $iHashKey, self::TOPIC, self::TYPE);
    }

    /**
     * @param array $aParams param
     * @return array
     */
    private function _fillData($aParams) {
        $aData = parent::fillData($aParams);

        $aData += array(
            'lat'           => isset($aParams['lat']) ? (float)($aParams['lat']) : 0.0,
            'lng'           => isset($aParams['lng']) ? (float)($aParams['lng']) : 0.0,
            'phone'         => (string)$aParams['phone'] ?? '',
            'city_id'       => isset($aParams['city_id']) && -1 != $aParams['city_id'] ? (int)($aParams['city_id']) : 0,
            'district'      => $aParams['district'] ?? '',
            'channel'       => $aParams['channel'],
            'access_key_id' => $aParams['access_key_id'],
            'imei'          => $aParams['imei'] ?? '',
            'dd_oaid'       => $aParams['dd_oaid'] ?? '',
            'idfa'          => $aParams['idfa'] ?? '',
            'openid'        => $aParams['openid'] ?? '',
            'dchn'          => $aParams['dchn'] ?? '',
        );

        if (!empty($aParams['platform_type'])) {
            $aData['platform_type'] = $aParams['platform_type'];
        }

        if (!empty($aParams['client_type'])) {
            $aData['client_type'] = $aParams['client_type'];
        }

        return $aData;
    }
}
