<?php
/**
 * desc: 乘客预估同步Kafka
 * modified: <EMAIL>
 * date: 2017/8/16.
 */

namespace PreSale\Models\carrera;

use BizLib\Client\CarreraClient;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Log;
use BizLib\Utils\Product;
use BizCommon\Models\Carrera\PassengerTopic;
use BizLib\Utils\ProductCategory;
use Nuwa\Core\Dispatcher;

class PassengerEstimateTopic extends PassengerTopic
{
    // 乘客预估topic定义
    const TOPIC = 'wanliu_passenger_estimate';
    // topic类型定义
    const TYPE = 21030;

    // 理想消息格式
    private static $_aFormat = [
        'area'                  => 'integer', // 区域id
        'role'                  => 'integer', // 角色，１＝司机，２＝乘客，３＝订单(消息中该值为2)
        'create_time'           => 'string', // 创建时间
        'channel'               => 'integer', // 订单渠道
        'car_type'              => 'string', // 车型
        'biztype'               => 'integer', // 产品线id
        'uid'                   => 'integer', // 对象id（64位乘客ID）
        'gpid'                  => 'string', // 库中乘客id
        'dest_lat'              => 'double', // 目的地纬度
        'district'              => 'string', // 地区
        'product_id'            => 'integer', // 内部产品线
        'starting_lng'          => 'double', // 起始经度
        'passenger_phone'       => 'string', // 乘客手机号
        'starting_lat'          => 'double', // 起始纬度
        'county'                => 'string', // 起始区县ID
        'dest_lng'              => 'double', // 目的地经度
        'from_poi_id'           => 'string', // 起始poiId
        'to_poi_id'             => 'string', // 目的地poiId
        'scene_type'            => 'integer', // 场景类型
        'is_anycar'             => 'integer', // 是否anycar订单
        'multi_require_product' => 'string', // 包含的产品线列表
        'preference_product'    => 'string', // anycar选中的车型偏好
        'n_tuple'               => 'string', // 产品N元组
        'starting_name'         => 'string', // 起点名称
        'dest_name'             => 'string', // 终点名称
        'select_type'           => 'integer', //6.0 anycar选中类型
        'recommend_type'        => 'integer', //6.0 anycar推荐区展示类型
        'form_show_type'        => 'integer', //6.0 当前车型展示位置
        'menu_id'               => 'string',  //顶导ID
        'departure_time'        => 'integer', //出发时间
    ];

    public function sync($aPassengerInfo) {
        if (empty($aPassengerInfo)) {
            return false;
        }

        $aData = $this->_fillData($aPassengerInfo);

        $iHashKey = mt_rand(); // 不能以gpid做hash key，因为高德等三方用的是相同gpid，导致hash不均匀

        // 参数校验
        static::formatCheck(self::TOPIC, $iHashKey, $aData, static::$_aFormat);

        static::send($aData, $iHashKey, self::TOPIC, self::TYPE);
    }

    private function _fillData($aPassengerInfo) {
        $aData = parent::fillData($aPassengerInfo);

        $iProductId = isset($aPassengerInfo['product_id']) ? $aPassengerInfo['product_id'] : Product::PRODUCT_ID_DEFAULT;

        $aData += array(
            'passenger_phone'         => isset($aPassengerInfo['passenger_phone']) ? (string)($aPassengerInfo['passenger_phone']) : '',
            'district'                => isset($aPassengerInfo['district']) ? (string)($aPassengerInfo['district']) : '',
            'area'                    => isset($aPassengerInfo['area']) ? (int)($aPassengerInfo['area']) : 0,
            'to_city'                 => isset($aPassengerInfo['to_city']) ? (int)($aPassengerInfo['to_city']) : 0,
            'route_id'                => isset($aPassengerInfo['route_id']) ? (string)($aPassengerInfo['route_id']) : '',
            'channel'                 => isset($aPassengerInfo['channel']) ? (int)($aPassengerInfo['channel']) : 0,
            'starting_lng'            => isset($aPassengerInfo['starting_lng']) ? (float)($aPassengerInfo['starting_lng']) : 0.0,
            'starting_lat'            => isset($aPassengerInfo['starting_lat']) ? (float)($aPassengerInfo['starting_lat']) : 0.0,
            'county'                  => isset($aPassengerInfo['county']) ? (string)($aPassengerInfo['county']) : '',
            'dest_lng'                => isset($aPassengerInfo['dest_lng']) ? (float)($aPassengerInfo['dest_lng']) : 0.0,
            'dest_lat'                => isset($aPassengerInfo['dest_lat']) ? (float)($aPassengerInfo['dest_lat']) : 0.0,
            'from_poi_id'             => isset($aPassengerInfo['from_poi_id']) ? (string)($aPassengerInfo['from_poi_id']) : '',
            'to_poi_id'               => isset($aPassengerInfo['to_poi_id']) ? (string)($aPassengerInfo['to_poi_id']) : '',
            'create_time'             => isset($aPassengerInfo['create_time']) ? (string)($aPassengerInfo['create_time']) : '',
            'product_id'              => $iProductId,
            'car_type'                => isset($aPassengerInfo['car_type']) ? (string)($aPassengerInfo['car_type']) : '',
            'scene_type'              => isset($aPassengerInfo['scene_type']) ? (int)($aPassengerInfo['scene_type']) : 0,
            'is_anycar'               => isset($aPassengerInfo['is_anycar']) ? (int)($aPassengerInfo['is_anycar']) : 0,
            'multi_require_product'   => isset($aPassengerInfo['multi_require_product']) ? (string)($aPassengerInfo['multi_require_product']) : '',
            'preference_product'      => isset($aPassengerInfo['preference_product']) ? (string)($aPassengerInfo['preference_product']) : '',
            'n_tuple'                 => $aPassengerInfo['n_tuple'] ?? '',
            'call_car'                => $aPassengerInfo['call_car'] ?? 0,
            'off_peak_push_timestamp' => isset($aPassengerInfo['off_peak_push_timestamp']) ? (int)($aPassengerInfo['off_peak_push_timestamp']) : -1,
            'current_lng'             => $aPassengerInfo['current_lng'] ?? 0,
            'current_lat'             => $aPassengerInfo['current_lat'] ?? 0,
            'client_type'             => $aPassengerInfo['client_type'] ?? 0,
            'platform_type'           => $aPassengerInfo['platform_type'] ?? 0,
            'estimate_distance_metre' => $aPassengerInfo['estimate_distance_metre'] ?? 0,
            'estimate_time_minutes'   => $aPassengerInfo['estimate_time_minutes'] ?? 0,
            'estimate_fee'            => $aPassengerInfo['estimate_fee'] ?? 0,
            'starting_name'           => $aPassengerInfo['starting_name'] ?? '',
            'dest_name'               => $aPassengerInfo['dest_name'] ?? '',
            'departure_time'          => (int)$aPassengerInfo['departure_time'] ?? 0,
            'is_fast_car'             => $aPassengerInfo['is_fast_car'] ?? (int)(Product::isFastcar($iProductId)),
            'oType'                   => isset($aPassengerInfo['oType']) ? $aPassengerInfo['oType'] : -1,
            'app_version'             => $aPassengerInfo['app_version'] ?? '',
            'origin_id'               => $aPassengerInfo['origin_id'] ?? 0,
            'basic_total_fee'         => isset($aPassengerInfo['basic_total_fee']) ? $aPassengerInfo['basic_total_fee'] : -1,
            'final_coupon_value'      => isset($aPassengerInfo['final_coupon_value']) ? $aPassengerInfo['final_coupon_value'] : -1,
            'coupon_info'             => $aPassengerInfo['coupon_info']??'',
            'pay_type'                => $aPassengerInfo['pay_type'] ?? 0,
            'dynamic_total_fee'       => $aPassengerInfo['dynamic_total_fee'] ?? 0,
            'cap_price'               => $aPassengerInfo['cap_price'] ?? 0,
            'dynamic_diff_price'      => $aPassengerInfo['dynamic_diff_price'] ?? 0,
            'dynamic_info'            => $aPassengerInfo['dynamic_info'] ?? '',
            'select_type'             => isset($aPassengerInfo['select_type']) ? (int)$aPassengerInfo['select_type'] : 0,
            'recommend_type'          => isset($aPassengerInfo['recommend_type']) ? (int)$aPassengerInfo['recommend_type'] : 0,
            'form_show_type'          => isset($aPassengerInfo['form_show_type']) ? (int)$aPassengerInfo['form_show_type'] : 0,
            'menu_id'                 => isset($aPassengerInfo['menu_id']) ? (string)$aPassengerInfo['menu_id'] : '',
            'map_request'             => $aPassengerInfo['map_request'] ?? '',
        );

        if (isset($aPassengerInfo['estimate_type']) || isset($aPassengerInfo['bubble_id']) || isset($aPassengerInfo['estimate_id'])) {
            $aData['estimate_type'] = isset($aPassengerInfo['estimate_type']) ? (string)($aPassengerInfo['estimate_type']) : '0';
            $aData['bubble_id']     = isset($aPassengerInfo['bubble_id']) ? (string)($aPassengerInfo['bubble_id']) : '';
            $aData['estimate_id']   = isset($aPassengerInfo['estimate_id']) ? (string)($aPassengerInfo['estimate_id']) : '';
            $aData['combo_type']    = isset($aPassengerInfo['combo_type']) ? (int)($aPassengerInfo['combo_type']) : 0;
        }

        if (!empty($aPassengerInfo['product_category'])) {
            $aData['product_category'] = $aPassengerInfo['product_category'];
        } elseif (isset($aPassengerInfo['n_tuple'])) {
            $aData['product_category'] = (new ProductCategory())->getProductCategoryByNTuple(json_decode($aPassengerInfo['n_tuple'], true));
        }

        $aData['bubble_trace_id'] = $aPassengerInfo['estimate_trace_id'] ?? '';

        return $aData;
    }

    /**
     * function batchSync
     * @param array $aMqBatchInfo mq info
     * @return bool
     */
    public function batchSync($aMqBatchInfo) {
        if (empty($aMqBatchInfo)) {
            return false;
        }

        $aBatchBody = [];
        // $iHashKey   = time(); // 不能以gpid做hash key，因为高德等三方用的是相同gpid，导致hash不均匀
        $iHashKey   = mt_rand(0, 16383);
        $iUtcOffset = Dispatcher::getInstance()->getRequest()->getInt('utc_offset', 0);
        $sMapType   = Dispatcher::getInstance()->getRequest()->getStr('map_type', '');
        if (empty($sMapType)) {
            $sMapType = Dispatcher::getInstance()->getRequest()->getStr('maptype', ''); // 糊
        }

        foreach ($aMqBatchInfo as $aMqInfo) {
            if (empty($aMqInfo)) {
                continue;
            }

            $aData = $this->_fillData($aMqInfo);
            static::formatCheck(self::TOPIC, $iHashKey, $aData, static::$_aFormat);
            $aBody = [
                'no'   => 0,
                'type' => self::TYPE,
                'ct'   => time(),
                'data' => $aData,
            ];
            $aBody['map_type']         = $sMapType;
            $aBody['data']['map_type'] = $sMapType;
            if (!empty($iUtcOffset)) {
                $aBody['utc_offset']         = $iUtcOffset;
                $aBody['data']['utc_offset'] = $iUtcOffset;
            }

            $aBatchBody[] = json_encode($aBody, JSON_UNESCAPED_UNICODE);
        }

        $oCarrera = CarreraClient::getInstance();
        $oRet     = $oCarrera->batchSend(self::TOPIC, $iHashKey, $aBatchBody);
        if (is_object($oRet) && $oRet->code > 1) {
            $sErrMsg = [
                'topic'    => self::TOPIC,
                'hash_key' => $iHashKey,
            ];
            Log::warning(Msg::formatArray(Code::E_COMMON_CARRERA_SEND_FAIL, $sErrMsg));
            return false;
        }

        return true;
    }
}
