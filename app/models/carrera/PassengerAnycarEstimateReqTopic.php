<?php


namespace PreSale\Models\carrera;

use BizCommon\Models\Carrera\PassengerTopic;
use BizCommon\Models\Order\OrderLossRemand;
use BizLib\Client\CarreraClient;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Log;
use BizLib\Utils\Product;
use Nuwa\Core\Dispatcher;

/**
 * Class PassengerAnycarEstimateReqTopic
 * @package Carrera
 */
class PassengerAnycarEstimateReqTopic extends PassengerTopic
{
    // 乘客pAnyCarEstimate预估reqtopic定义
    const TOPIC = 'wanliu_passenger_anycar_estimate_req';
    // topic类型定义
    const TYPE = 21071;

    // 理想消息格式
    private static $_aFormat = [
        'estimate_id'           => 'string', //预估ID
        'area'                  => 'integer', // 区域id
        'role'                  => 'integer', // 角色，１＝司机，２＝乘客，３＝订单(消息中该值为2)
        'passenger_phone'       => 'string', // 乘客手机号
        'gpid'                  => 'string', // 库中乘客id
        'uid'                   => 'integer', // 乘客uid
        'require_level'         => 'integer', //车型
        'county'                => 'string', // 起始区县ID
        'district'              => 'string', // 地区
        'product_id'            => 'integer', // 内部产品线
        'client_type'           => 'integer', //端类型
        'from_poi_id'           => 'string', // 起始poiId
        'starting_lng'          => 'double', // 起始经度
        'starting_lat'          => 'double', // 起始纬度
        'to_poi_id'             => 'string', // 目的地poiId
        'dest_lng'              => 'double', // 起始经度
        'dest_lat'              => 'double', // 起始纬度
        'starting_name'         => 'string', // 起点名称
        'dest_name'             => 'string', // 终点名称
        'app_version'           => 'string', // 版本信息
        'access_key_id'         => 'integer', // 终端来源标识
        'menu_id'               => 'string', // 顶导id
        'carpool_type'          => 'integer',
        'is_dual_carpool_price' => 'boolean',
        'carpool_price_type'    => 'integer',
        'departure_time'        => 'string',
        'combo_type'            => 'integer',
    ];

    /**
     * @param array $aPassengerInfo kafka信息
     * @return bool
     */
    public function sync($aPassengerInfo) {
        if (empty($aPassengerInfo)) {
            return false;
        }

        $aData = $this->_fillData($aPassengerInfo);

        $iHashKey = (int)($aData['gpid']); // 64位整型driver_id

        // 参数校验
        static::formatCheck(self::TOPIC, $iHashKey, $aData, static::$_aFormat);

        static::send($aData, $iHashKey, self::TOPIC, self::TYPE);
    }

    /**
     * @param array $aPassengerInfo aPassengerInfo
     * @return array
     */
    private function _fillData($aPassengerInfo) {
        $aData = parent::fillData($aPassengerInfo);

        $iProductId = isset($aPassengerInfo['product_id']) ? $aPassengerInfo['product_id'] : Product::PRODUCT_ID_DEFAULT;

        $aData += array(
            'estimate_id'           => isset($aPassengerInfo['estimate_id']) ? (string)($aPassengerInfo['estimate_id']) : '',
            'passenger_phone'       => isset($aPassengerInfo['passenger_phone']) ? (string)($aPassengerInfo['passenger_phone']) : '',
            'area'                  => isset($aPassengerInfo['area']) ? (int)($aPassengerInfo['area']) : 0,
            'require_level'         => isset($aPassengerInfo['require_level']) ? (int)($aPassengerInfo['require_level']) : 0,
            'county'                => isset($aPassengerInfo['county']) ? (string)($aPassengerInfo['county']) : '',
            'district'              => isset($aPassengerInfo['district']) ? (string)($aPassengerInfo['district']) : '',
            'product_id'            => $iProductId,
            'client_type'           => $aPassengerInfo['client_type'] ?? 0,
            'from_poi_id'           => isset($aPassengerInfo['from_poi_id']) ? (string)($aPassengerInfo['from_poi_id']) : '',
            'starting_lng'          => isset($aPassengerInfo['starting_lng']) ? (float)($aPassengerInfo['starting_lng']) : 0.0,
            'starting_lat'          => isset($aPassengerInfo['starting_lat']) ? (float)($aPassengerInfo['starting_lat']) : 0.0,
            'to_poi_id'             => isset($aPassengerInfo['to_poi_id']) ? (string)($aPassengerInfo['to_poi_id']) : '',
            'dest_lng'              => isset($aPassengerInfo['dest_lng']) ? (float)($aPassengerInfo['dest_lng']) : 0.0,
            'dest_lat'              => isset($aPassengerInfo['dest_lat']) ? (float)($aPassengerInfo['dest_lat']) : 0.0,
            'starting_name'         => isset($aPassengerInfo['starting_name']) ? (string)($aPassengerInfo['starting_name']) : '',
            'dest_name'             => isset($aPassengerInfo['dest_name']) ? (string)($aPassengerInfo['dest_name']) : '',
            'app_version'           => isset($aPassengerInfo['app_version']) ? (string)($aPassengerInfo['app_version']) : '',
            'access_key_id'         => isset($aPassengerInfo['access_key_id']) ? (int)($aPassengerInfo['access_key_id']) : '',
            'menu_id'               => $aPassengerInfo['menu_id'] ?: '',
            'combo_type'            => $aPassengerInfo['combo_type'] ?? 0,
            'carpool_price_type'    => $aPassengerInfo['carpool_price_type'] ?? 0,
            'carpool_type'          => $aPassengerInfo['carpool_type'] ?? 0,
            'is_dual_carpool_price' => $aPassengerInfo['is_dual_carpool_price'] ?? 0,
            'departure_time'        => $aPassengerInfo['departure_time'] ?? '',
        );

        return $aData;
    }

    /**
     * function batchSync
     * @param array $aMqBatchInfo mq info
     * @return bool
     */
    public function batchSync($aMqBatchInfo) {
        if (empty($aMqBatchInfo)) {
            return false;
        }

        $aBatchBody = [];
        $iHashKey   = 0;
        $iUtcOffset = Dispatcher::getInstance()->getRequest()->fetchGetPost('utc_offset', false);
        $sMapType   = Dispatcher::getInstance()->getRequest()->fetchGetPost('map_type', false);
        foreach ($aMqBatchInfo as $aMqInfo) {
            if (empty($aMqInfo)) {
                continue;
            }

            $aData    = $this->_fillData($aMqInfo);
            $iHashKey = (int)($aData['gpid']);
            static::formatCheck(self::TOPIC, $iHashKey, $aData, static::$_aFormat);
            $aBody = [
                'no'   => 0,
                'type' => self::TYPE,
                'ct'   => time(),
                'data' => $aData,
            ];
            $aBody['map_type']         = $sMapType;
            $aBody['data']['map_type'] = $sMapType;
            false !== $iUtcOffset && $aBody['utc_offset']         = $iUtcOffset;
            false !== $iUtcOffset && $aBody['data']['utc_offset'] = $iUtcOffset;
            $aBatchBody[] = json_encode($aBody, JSON_UNESCAPED_UNICODE);
        }

        empty($iHashKey) && $iHashKey = rand(0, 128);

        $oCarrera = CarreraClient::getInstance();
        $oRet     = $oCarrera->batchSend(self::TOPIC, $iHashKey, $aBatchBody);
        if (is_object($oRet) && $oRet->code > 1) {
            $sErrMsg = [
                'topic'    => self::TOPIC,
                'hash_key' => $iHashKey,
            ];
            Log::warning(Msg::formatArray(Code::E_COMMON_CARRERA_SEND_FAIL, $sErrMsg));
            return false;
        }

        return true;
    }
}
