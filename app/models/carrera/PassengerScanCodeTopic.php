<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * <AUTHOR> <<EMAIL>>
 * Date: 2020/3/24
 * Time: 8:07 PM
 */

namespace PreSale\Models\carrera;

use BizCommon\Models\Carrera\PassengerTopic;
use BizLib\Log;

/**
 * Class PassengerScanCodeTopic
 * @package PreSale\Models\carrera
 */
class PassengerScanCodeTopic extends PassengerTopic
{
    const TOPIC = 'wanliu_scan_code_callback';
    const TYPE  = 21080;

    // 消息的理想格式
    private static $_aFormat = [
        'gpid'      => 'string',
        'driver_id' => 'string',
    ];

    /**
     * @param array $aParams 参数
     * @return mixed
     */
    public function sync($aParams) {
        if (empty($aParams)) {
            return;
        }

        $aData = $this->_fillData($aParams);

        $iHashKey = (int)($aData['gpid']); // 乘客pid

        static::formatCheck(self::TOPIC, $iHashKey, $aData, static::$_aFormat);

        static::send($aData, $iHashKey, self::TOPIC, self::TYPE);
    }

    /**
     * @param array $aParams 参数
     * @return array
     */
    private function _fillData($aParams) {
        $aData = parent::fillData($aParams);

        $aData += array(
            'lng'             => (float) $aParams['lng'],
            'lat'             => (float) $aParams['lat'],
            'lang'            => (string) $aParams['lang'],
            'city_id'         => (int) $aParams['city_id'],
            'page_type'       => (int) $aParams['page_type'],
            'product_id'      => (int) $aParams['product_id'],
            'passenger_phone' => (string) $aParams['passenger_phone'],
            'driver_id'       => (string) $aParams['driver_id'],
            'product_info'    => (string) json_encode($aParams['product_info']),
        );

        return $aData;
    }
}
