<?php
/**
 * desc: 乘客打开app同步Kafka
 * modified: <EMAIL>
 * date: 2017/8/16.
 */

namespace PreSale\Models\carrera;

use BizLib\Utils\DidiPushInterface;
use BizCommon\Models\Carrera\PassengerTopic;

class PassengerOpenAppTopic extends PassengerTopic
{
    /** @var $didi_push_interface DidiPushInterface */
    public $didi_push_interface;
    // 乘客打开App topic定义
    const TOPIC = 'wanliu_passenger_openapp';
    // topic类型定义
    const TYPE = 21010;

    // 理想消息格式
    private static $_aFormat = [
        'biztype'         => 'integer', // 产品线id
        'uid'             => 'integer', // 对象id（64位乘客ID）
        'role'            => 'integer', // 角色，１＝司机，２＝乘客，３＝订单(消息中该值为2)
        'gpid'            => 'string', // 库中乘客id
        'lng'             => 'double', // 经度
        'passenger_id'    => 'integer', // 乘客id
        'district'        => 'string', // 区号
        'lat'             => 'double', // 纬度
        'passenger_phone' => 'string', // 乘客手机号
        'city_id'         => 'integer', // 城市id
    ];

    public function sync($aPassengerInfo) {
        if (empty($aPassengerInfo)) {
            return false;
        }

        $aData = $this->_fillData($aPassengerInfo);

        $iHashKey = (int)($aData['gpid']); // 64位整型driver_id

        // 参数校验
        static::formatCheck(self::TOPIC, $iHashKey, $aData, static::$_aFormat);

        static::send($aData, $iHashKey, self::TOPIC, self::TYPE);
    }

    private function _fillData($aPassengerInfo) {
        $aData = parent::fillData($aPassengerInfo);
        $this->didi_push_interface = new DidiPushInterface();
        $iFormatPassengerId        = $this->didi_push_interface->GetUidByRoleAndId(\DidiPush\Role::Passenger, (int)($aPassengerInfo['passenger_id']));

        $aData += array(
            'lat'             => isset($aPassengerInfo['lat']) ? (float)($aPassengerInfo['lat']) : 0.0,
            'lng'             => isset($aPassengerInfo['lng']) ? (float)($aPassengerInfo['lng']) : 0.0,
            'passenger_id'    => $iFormatPassengerId,
            'passenger_phone' => isset($aPassengerInfo['passenger_phone']) ? (string)($aPassengerInfo['passenger_phone']) : '',
            'district'        => isset($aPassengerInfo['district']) ? (string)($aPassengerInfo['district']) : '',
            'city_id'         => isset($aPassengerInfo['city_id']) ? (int)($aPassengerInfo['city_id']) : 0,
        );

        return $aData;
    }
}
