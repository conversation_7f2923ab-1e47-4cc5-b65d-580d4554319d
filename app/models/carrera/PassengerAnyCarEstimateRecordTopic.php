<?php
namespace PreSale\Models\carrera;

use BizCommon\Models\Carrera\BasicTopic;

/**
 * Class PassengerAnyCarEstimateRecordTopic
 * @package PreSale\Models\carrera
 */
class PassengerAnyCarEstimateRecordTopic extends BasicTopic
{

    const TOPIC = 'wanliu_passenger_anycar_estimate_record';

    const TYPE = 21072;

    private static $_aFormat = [
        'req' => 'string', // 请求json信息
        'rsp' => 'string', // 响应json信息
    ];


    /**
     * @param array $aParams   请求参数
     * @param array $aResponse 请求响应
     * @return array
     */
    private function _fillData(array $aParams, array $aResponse) {
        $aData = [
            'req' => json_encode($aParams),
            'rsp' => base64_encode(json_encode($aResponse)),
        ];
        return $aData;
    }


    /**
     * @param array $aParams   请求参数
     * @param array $aResponse 请求响应
     * @return void
     */
    public function sync(array $aParams, array $aResponse) {
        // fill Data
        $aData = $this->_fillData($aParams, $aResponse);

        // hash key
        $sHashKey = $aParams['pid'];

        // format check
        static::formatCheck(self::TOPIC,$sHashKey,$aData,static::$_aFormat);

        // send
        static::send($aData, $sHashKey, self::TOPIC, self::TYPE);
    }
}
