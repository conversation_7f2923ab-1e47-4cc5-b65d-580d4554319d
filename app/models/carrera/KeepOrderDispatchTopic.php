<?php
/**
 * 延时消息，接送机预约单改派持续播单.
 *
 * <AUTHOR>
 */

namespace PreSale\Models\carrera;

use BizCommon\Models\Carrera\BasicTopic;

class KeepOrderDispatchTopic extends BasicTopic
{
    const TOPIC = 'wanliu_keep_order_dispatch';
    const TYPE  = 80014;

    // 消息的理想格式
    private static $_aFormat = [
        'order_id'       => 'integer',
        'district'       => 'string',
        'dynamic_info'   => 'string',
        'feature_filter' => 'string',
        'times'          => 'integer',
    ];

    public function sync($aParams) {
        if (empty($aParams)) {
            return;
        }

        $aData = $this->_fillData($aParams);

        // 消息格式校验
        static::formatCheckDelay(self::TOPIC, $aData, static::$_aFormat);

        // 构建延时消息对象
        $aDelayMeta = $this->buildDealyMessage($aParams);

        //发送延时消息
        static::sendDelay($aData, $aDelayMeta, self::TOPIC, self::TYPE);
    }

    private function _fillData($aParams) {
        $aData = array(
            'order_id'       => (int) $aParams['order_id'],
            'district'       => (string) $aParams['district'],
            'dynamic_info'   => (string) $aParams['dynamic_info'],
            'feature_filter' => (string) $aParams['feature_filter'],
            'times'          => (int) $aParams['times'],
        );

        return $aData;
    }

    /**
     * 对传入参数构建延时信息.
     *
     * @param $aParams
     *
     * @return \Disf\SPL\CarreraThrift\DelayMeta
     */
    public function buildDealyMessage($aParams) {
        $aDelayMeta = new \Disf\SPL\CarreraThrift\DelayMeta(
            array(
                'timestamp' => $aParams['delayed_time'], // 延迟执行时间
                'dmsgtype'  => 2, // 2-延迟消息
            )
        );

        return $aDelayMeta;
    }
}
