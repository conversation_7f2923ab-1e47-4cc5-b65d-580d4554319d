<?php
/**
 *
 * Copyright (c) 2019 xiaojukeji.com, Inc. All Rights Reserved.
 * @Author: wang<PERSON><PERSON><PERSON><PERSON>@didichuxing.com
 * @Date: 2019/5/17 下午6:16
 * @Desc: 物品遗失订单，播单后写延时队列，用于超时后给乘客短信感知
 * @wiki:
 *
 */

namespace PreSale\Models\carrera;

use BizCommon\Models\Carrera\BasicTopic;
use BizCommon\Models\Order\OrderLossRemand;

class LossRemandTimeoutTopic extends BasicTopic
{
    const TIME_OUT_BUFFER = 30; // 缓存30s

    const TOPIC = 'wanliu_loss_remand_order_timeout_callback';
    const TYPE  = 80045;

    // 消息的理想格式
    private static $_aFormat = [
        'order_id'   => 'string',
        'district'   => 'string',
        'extra_type' => 'integer',
        'type'       => 'integer',
        'product_id' => 'integer',
    ];

    public function sync($aParams) {
        if (empty($aParams)) {
            return;
        }

        $aData = $this->_fillData($aParams);

        // 消息格式校验
        static::formatCheckDelay(self::TOPIC, $aData, static::$_aFormat);

        // 构建延时消息对象
        $aDelayMeta = $this->_buildDelayMessage($aParams);

        //发送延时消息
        static::sendDelay($aData, $aDelayMeta, self::TOPIC, self::TYPE);
    }

    private function _fillData($aParams) {
        $aData = array(
            'order_id'   => (string) $aParams['order_id'],
            'district'   => (string) $aParams['district'],
            'extra_type' => (int) $aParams['extra_type'],
            'type'       => (int) $aParams['type'],
            'product_id' => (int) $aParams['product_id'],
        );

        return $aData;
    }

    /**
     * 对传入参数构建延时信息.
     *
     * @param $aParams
     *
     * @return \Disf\SPL\CarreraThrift\DelayMeta
     */
    private function _buildDelayMessage($aParams) {
        $iDelayTime = time() + OrderLossRemand::getBroadcastTime() + self::TIME_OUT_BUFFER;
        $aDelayMeta = new \Disf\SPL\CarreraThrift\DelayMeta(
            array(
                'timestamp' => $iDelayTime, // 延迟执行时间
                'dmsgtype'  => 2, // 2-延迟消息
            )
        );

        return $aDelayMeta;
    }
}
