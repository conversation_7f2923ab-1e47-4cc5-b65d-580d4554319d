<?php
/**
 * desc: 乘客预估req同步Kafka
 * <AUTHOR> <<EMAIL>>
 */

namespace PreSale\Models\carrera;

use BizLib\Client\CarreraClient;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Log;
use BizLib\Utils\Product;
use BizCommon\Models\Carrera\PassengerTopic;
use Nuwa\Core\Dispatcher;

/**
 * Class PassengerEstimateReqTopic
 * @package PreSale\Models\carrera
 */
class PassengerMultiEstimateReqTopic extends PassengerTopic
{
    // 乘客预估reqtopic定义
    const TOPIC = 'wanliu_passenger_multi_estimate_req';
    // topic类型定义
    const TYPE = 21080;

    // 理想消息格式
    private static $_aFormat = [
        'estimate_trace_id'         => 'string',
        'area'                      => 'integer', // 区域id
        'role'                      => 'integer', // 角色，１＝司机，２＝乘客，３＝订单(消息中该值为2)
        'passenger_phone'           => 'string', // 乘客手机号
        'passenger_id'              => 'string', // 乘客PID
        'gpid'                      => 'string', // 库中乘客id
        'uid'                       => 'integer', // 乘客uid
        'county'                    => 'string', // 起始区县ID
        'district'                  => 'string', // 地区
        'client_type'               => 'integer', //端类型
        'from_poi_id'               => 'string', // 起始poiId
        'starting_lng'              => 'double', // 起始经度
        'starting_lat'              => 'double', // 起始纬度
        'current_lng'               => 'double', // 当前经度
        'current_lat'               => 'double', // 当前纬度
        'to_poi_id'                 => 'string', // 目的地poiId
        'dest_lng'                  => 'double', // 起始经度
        'dest_lat'                  => 'double', // 起始纬度
        'starting_name'             => 'string', // 起点名称
        'dest_name'                 => 'string', // 终点名称
        'from_poi_type'             => 'string', // 起点的点位来源标签
        'to_poi_type'               => 'string', // 终点的点位来源标签
        'stopover_points'           => 'string', // 途经点信息json
        'app_version'               => 'string', // 版本信息
        'access_key_id'             => 'integer', // 终端来源标识
        'menu_id'                   => 'string', // 顶导id
        'page_type'                 => 'integer', //页面id
        'lang'                      => 'string',
        'order_type'                => 'integer',
        'product_list'              => 'array',
        'pay_type'                  => 'integer',
        'tab_id'                    => 'string',
        'athena_rpc_trigger_switch' => 'integer',
    ];


    private static $_productFormat = [
        'estimate_id'           => 'string',
        'combo_type'            => 'integer', // 字段
        'carpool_type'          => 'integer', // 字段
        'carpool_price_type'    => 'integer', // 字段
        'is_dual_carpool_price' => 'boolean', // 是不是拼车两口价
        'departure_time'        => 'string', // 出发时间
        'require_level'         => 'integer', //车型
        'product_id'            => 'integer', // 内部产品线
        'level_type'            => 'integer',
        'is_special_price'      => 'boolean',
        'business_id'           => 'integer',
        'member_level'          => 'integer',
    ];
    /**
     * @param array $aPassengerInfo kafka信息
     * @return bool
     */
    public function sync($aPassengerInfo) {
        if (empty($aPassengerInfo)) {
            return false;
        }

        $aData = $this->_fillData($aPassengerInfo);

        // $iHashKey = (int)($aData['gpid']); // 64位整型driver_id
        $iHashKey = mt_rand(0, 16383);

        // 参数校验
        static::formatCheck(self::TOPIC, $iHashKey, $aData, static::$_aFormat);

        foreach ($aData['product_list'] as $aProduct) {
            static::formatCheck(self::TOPIC, $iHashKey, $aProduct, static::$_productFormat);
        }

        static::send($aData, $iHashKey, self::TOPIC, self::TYPE);
    }

    /**
     * @param array $aPassengerInfo aPassengerInfo
     * @return array
     */
    private function _fillData($aPassengerInfo) {
        $aData      = parent::fillData($aPassengerInfo);
        $iProductId = isset($aPassengerInfo['product_id']) ? $aPassengerInfo['product_id'] : Product::PRODUCT_ID_DEFAULT;

        $aData += array(
            'timeStamp'                 => time(),
            'micro_time_stamp'          => round(microtime(true) * 1000),
            'estimate_trace_id'         => $aPassengerInfo['estimate_trace_id'] ?? '',
            'passenger_phone'           => isset($aPassengerInfo['passenger_phone']) ? (string)($aPassengerInfo['passenger_phone']) : '',
            'passenger_id'              => isset($aPassengerInfo['passenger_id']) ? (string)($aPassengerInfo['passenger_id']) : '',
            'area'                      => isset($aPassengerInfo['area']) ? (int)($aPassengerInfo['area']) : 0,
            'require_level'             => isset($aPassengerInfo['require_level']) ? (int)($aPassengerInfo['require_level']) : 0,
            'county'                    => isset($aPassengerInfo['county']) ? (string)($aPassengerInfo['county']) : '',
            'district'                  => isset($aPassengerInfo['district']) ? (string)($aPassengerInfo['district']) : '',
            'product_id'                => $iProductId,
            'client_type'               => $aPassengerInfo['client_type'] ?? 0,
            'map_type'                  => isset($aPassengerInfo['map_type']) ? (string)($aPassengerInfo['map_type']) : '',
            'from_poi_id'               => isset($aPassengerInfo['from_poi_id']) ? (string)($aPassengerInfo['from_poi_id']) : '',
            'starting_lng'              => isset($aPassengerInfo['starting_lng']) ? (float)($aPassengerInfo['starting_lng']) : 0.0,
            'starting_lat'              => isset($aPassengerInfo['starting_lat']) ? (float)($aPassengerInfo['starting_lat']) : 0.0,
            'current_lng'               => isset($aPassengerInfo['current_lng']) ? (float)($aPassengerInfo['current_lng']) : 0.0,
            'current_lat'               => isset($aPassengerInfo['current_lat']) ? (float)($aPassengerInfo['current_lat']) : 0.0,
            'to_poi_id'                 => isset($aPassengerInfo['to_poi_id']) ? (string)($aPassengerInfo['to_poi_id']) : '',
            'dest_lng'                  => isset($aPassengerInfo['dest_lng']) ? (float)($aPassengerInfo['dest_lng']) : 0.0,
            'dest_lat'                  => isset($aPassengerInfo['dest_lat']) ? (float)($aPassengerInfo['dest_lat']) : 0.0,
            'starting_name'             => isset($aPassengerInfo['starting_name']) ? (string)($aPassengerInfo['starting_name']) : '',
            'dest_name'                 => isset($aPassengerInfo['dest_name']) ? (string)($aPassengerInfo['dest_name']) : '',
            'from_poi_type'             => isset($aPassengerInfo['from_poi_type']) ? (string)($aPassengerInfo['from_poi_type']) : '',
            'to_poi_type'               => isset($aPassengerInfo['to_poi_type']) ? (string)($aPassengerInfo['to_poi_type']) : '',
            'stopover_points'           => isset($aPassengerInfo['stopover_points']) ? (string)($aPassengerInfo['stopover_points']) : '',
            'app_version'               => isset($aPassengerInfo['app_version']) ? (string)($aPassengerInfo['app_version']) : '',
            'access_key_id'             => isset($aPassengerInfo['access_key_id']) ? (int)($aPassengerInfo['access_key_id']) : '',
            'menu_id'                   => $aPassengerInfo['menu_id'] ?: '',
            'lang'                      => $aPassengerInfo['lang'] ?? '',
            'order_type'                => $aPassengerInfo['order_type'] ?? 0,
            'page_type'                 => $aPassengerInfo['page_type'] ?? 0,
            'channel'                   => isset($aPassengerInfo['channel']) ? (string)($aPassengerInfo['channel']) : '',
            'product_list'              => $aPassengerInfo['product_list'],
            'has_dual_price_carpool'    => $aPassengerInfo['has_dual_price_carpool'] ?? 0,
            'has_intercity_carpool'     => $aPassengerInfo['has_intercity_carpool'] ?? 0,
            'user_type'                 => $aPassengerInfo['user_type'] ?? 1,
            'pay_type'                  => $aPassengerInfo['pay_type'] ?? 0,
            'to_city'                   => $aPassengerInfo['to_city'] ?? 0,
            'call_car'                  => $aPassengerInfo['call_car'] ?? 0,
            'tab_id'                    => $aPassengerInfo['tab_id'] ?? '',
            'choose_f_searchid'         => $aPassengerInfo['choose_f_searchid'] ?? '',
            'choose_t_searchid'         => $aPassengerInfo['choose_t_searchid'] ?? '',
            'athena_rpc_trigger_switch' => $aPassengerInfo['athena_rpc_trigger_switch'] ?? 0,
        );

        if (!empty($aPassengerInfo['_source'])) {
            $aData['_source'] = (string)$aPassengerInfo['_source'];
        }

        return $aData;
    }

    /**
     * function batchSync
     * @param array $aMqBatchInfo mq info
     * @return bool
     */
    public function batchSync($aMqBatchInfo) {
        if (empty($aMqBatchInfo)) {
            return false;
        }

        $aBatchBody = [];
        $iHashKey   = 0;
        $iUtcOffset = Dispatcher::getInstance()->getRequest()->fetchGetPost('utc_offset', false);
        $sMapType   = Dispatcher::getInstance()->getRequest()->fetchGetPost('map_type', false);
        foreach ($aMqBatchInfo as $aMqInfo) {
            if (empty($aMqInfo)) {
                continue;
            }

            $aData    = $this->_fillData($aMqInfo);
            $iHashKey = (int)($aData['gpid']);
            static::formatCheck(self::TOPIC, $iHashKey, $aData, static::$_aFormat);
            $aBody = [
                'no'   => 0,
                'type' => self::TYPE,
                'ct'   => time(),
                'data' => $aData,
            ];
            $aBody['map_type']         = $sMapType;
            $aBody['data']['map_type'] = $sMapType;
            false !== $iUtcOffset && $aBody['utc_offset']         = $iUtcOffset;
            false !== $iUtcOffset && $aBody['data']['utc_offset'] = $iUtcOffset;
            $aBatchBody[] = json_encode($aBody, JSON_UNESCAPED_UNICODE);
        }

        empty($iHashKey) && $iHashKey = rand(0, 128);

        $oCarrera = CarreraClient::getInstance();
        $oRet     = $oCarrera->batchSend(self::TOPIC, $iHashKey, $aBatchBody);
        if (is_object($oRet) && $oRet->code > 1) {
            $sErrMsg = [
                'topic'    => self::TOPIC,
                'hash_key' => $iHashKey,
            ];
            Log::warning(Msg::formatArray(Code::E_COMMON_CARRERA_SEND_FAIL, $sErrMsg));
            return false;
        }

        return true;
    }
}
