<?php
/**
 * desc: 乘客预估req同步Kafka
 * <AUTHOR> <<EMAIL>>
 */

namespace PreSale\Models\carrera;

use BizLib\Client\CarreraClient;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Log;
use BizLib\Utils\Product;
use BizCommon\Models\Carrera\PassengerTopic;
use Nuwa\Core\Dispatcher;

/**
 * Class PassengerEstimateReqTopic
 * @package PreSale\Models\carrera
 */
class PassengerEstimateReqTopic extends PassengerTopic
{
    // 乘客预估reqtopic定义
    const TOPIC = 'wanliu_passenger_estimate_req';
    // topic类型定义
    const TYPE = 21070;

    // 理想消息格式
    private static $_aFormat = [
        'estimate_id'           => 'string', //预估ID
        'area'                  => 'integer', // 区域id
        'role'                  => 'integer', // 角色，１＝司机，２＝乘客，３＝订单(消息中该值为2)
        'passenger_phone'       => 'string', // 乘客手机号
        'gpid'                  => 'string', // 库中乘客id
        'uid'                   => 'integer', // 乘客uid
        'require_level'         => 'integer', //车型
        'county'                => 'string', // 起始区县ID
        'district'              => 'string', // 地区
        'product_id'            => 'integer', // 内部产品线
        'client_type'           => 'integer', //端类型
        'from_poi_id'           => 'string', // 起始poiId
        'starting_lng'          => 'double', // 起始经度
        'starting_lat'          => 'double', // 起始纬度
        'to_poi_id'             => 'string', // 目的地poiId
        'dest_lng'              => 'double', // 起始经度
        'dest_lat'              => 'double', // 起始纬度
        'starting_name'         => 'string', // 起点名称
        'dest_name'             => 'string', // 终点名称
        'from_poi_type'         => 'string', // 起点的点位来源标签
        'to_poi_type'           => 'string', // 终点的点位来源标签
        'stopover_points'       => 'string', // 途经点信息json
        'app_version'           => 'string', // 版本信息
        'access_key_id'         => 'integer', // 终端来源标识
        'menu_id'               => 'string', // 顶导id
        'combo_type'            => 'integer', // 字段
        'carpool_type'          => 'integer', // 字段
        'carpool_price_type'    => 'integer', // 字段
        'is_dual_carpool_price' => 'boolean', // 是不是拼车两口价
        'departure_time'        => 'string', // 出发时间
        'order_type'            => 'integer', // 订单类型
        'pay_type'              => 'integer', // 支付方式
        'business_id'           => 'integer', // 产品线
    ];

    /**
     * @param array $aPassengerInfo kafka信息
     * @return bool
     */
    public function sync($aPassengerInfo) {
        if (empty($aPassengerInfo)) {
            return false;
        }

        $aData = $this->_fillData($aPassengerInfo);

        $iHashKey = (int)($aData['gpid']); // 64位整型driver_id

        // 参数校验
        static::formatCheck(self::TOPIC, $iHashKey, $aData, static::$_aFormat);

        static::send($aData, $iHashKey, self::TOPIC, self::TYPE);
    }

    /**
     * @param array $aPassengerInfo aPassengerInfo
     * @return array
     */
    private function _fillData($aPassengerInfo) {
        $aData = parent::fillData($aPassengerInfo);

        $iProductId = isset($aPassengerInfo['product_id']) ? $aPassengerInfo['product_id'] : Product::PRODUCT_ID_DEFAULT;

        $aData += array(
            'estimate_id'           => isset($aPassengerInfo['estimate_id']) ? (string)($aPassengerInfo['estimate_id']) : '',
            'passenger_phone'       => isset($aPassengerInfo['passenger_phone']) ? (string)($aPassengerInfo['passenger_phone']) : '',
            'area'                  => isset($aPassengerInfo['area']) ? (int)($aPassengerInfo['area']) : 0,
            'require_level'         => isset($aPassengerInfo['require_level']) ? (int)($aPassengerInfo['require_level']) : 0,
            'county'                => isset($aPassengerInfo['county']) ? (string)($aPassengerInfo['county']) : '',
            'district'              => isset($aPassengerInfo['district']) ? (string)($aPassengerInfo['district']) : '',
            'product_id'            => $iProductId,
            'client_type'           => $aPassengerInfo['client_type'] ?? 0,
            'from_poi_id'           => isset($aPassengerInfo['from_poi_id']) ? (string)($aPassengerInfo['from_poi_id']) : '',
            'starting_lng'          => isset($aPassengerInfo['starting_lng']) ? (float)($aPassengerInfo['starting_lng']) : 0.0,
            'starting_lat'          => isset($aPassengerInfo['starting_lat']) ? (float)($aPassengerInfo['starting_lat']) : 0.0,
            'to_poi_id'             => isset($aPassengerInfo['to_poi_id']) ? (string)($aPassengerInfo['to_poi_id']) : '',
            'dest_lng'              => isset($aPassengerInfo['dest_lng']) ? (float)($aPassengerInfo['dest_lng']) : 0.0,
            'dest_lat'              => isset($aPassengerInfo['dest_lat']) ? (float)($aPassengerInfo['dest_lat']) : 0.0,
            'starting_name'         => isset($aPassengerInfo['starting_name']) ? (string)($aPassengerInfo['starting_name']) : '',
            'dest_name'             => isset($aPassengerInfo['dest_name']) ? (string)($aPassengerInfo['dest_name']) : '',
            'from_poi_type'         => isset($aPassengerInfo['from_poi_type']) ? (string)($aPassengerInfo['from_poi_type']) : '',
            'to_poi_type'           => isset($aPassengerInfo['to_poi_type']) ? (string)($aPassengerInfo['to_poi_type']) : '',
            'stopover_points'       => isset($aPassengerInfo['stopover_points']) ? (string)($aPassengerInfo['stopover_points']) : '',
            'app_version'           => isset($aPassengerInfo['app_version']) ? (string)($aPassengerInfo['app_version']) : '',
            'access_key_id'         => isset($aPassengerInfo['access_key_id']) ? (int)($aPassengerInfo['access_key_id']) : '',
            'menu_id'               => $aPassengerInfo['menu_id'] ?: '',
            'combo_type'            => isset($aPassengerInfo['combo_type']) ? (int)$aPassengerInfo['combo_type'] : 0,
            'carpool_type'          => isset($aPassengerInfo['carpool_type']) ? (int)$aPassengerInfo['carpool_type'] : 0,
            'carpool_price_type'    => isset($aPassengerInfo['carpool_price_type']) ? (int)$aPassengerInfo['carpool_price_type'] : 0,
            'is_dual_carpool_price' => empty($aPassengerInfo['is_dual_carpool_price']) ? false : true,
            'departure_time'        => $aPassengerInfo['departure_time'] ?? '',
            'channel'               => isset($aPassengerInfo['channel']) ? (int)($aPassengerInfo['channel']) : 0,
            'order_type'            => $aPassengerInfo['order_type'] ?? 0,
            'pay_type'              => $aPassengerInfo['pay_type'] ?? 0,
            'business_id'           => $aPassengerInfo['business_id'] ?? 0,
        );

        return $aData;
    }

    /**
     * function batchSync
     * @param array $aMqBatchInfo mq info
     * @return bool
     */
    public function batchSync($aMqBatchInfo) {
        if (empty($aMqBatchInfo)) {
            return false;
        }

        $aBatchBody = [];
        // $iHashKey   = 0;
        $iHashKey   = mt_rand(0, 16383);
        $iUtcOffset = Dispatcher::getInstance()->getRequest()->getInt('utc_offset', 0);
        $sMapType   = Dispatcher::getInstance()->getRequest()->getStr('map_type', '');
        if (empty($sMapType)) {
            $sMapType = Dispatcher::getInstance()->getRequest()->getStr('maptype', ''); // 糊
        }

        foreach ($aMqBatchInfo as $aMqInfo) {
            if (empty($aMqInfo)) {
                continue;
            }

            $aData = $this->_fillData($aMqInfo);
            // $iHashKey = (int)($aData['gpid']);
            static::formatCheck(self::TOPIC, $iHashKey, $aData, static::$_aFormat);
            $aBody = [
                'no'   => 0,
                'type' => self::TYPE,
                'ct'   => time(),
                'data' => $aData,
            ];
            $aBody['map_type']         = $sMapType;
            $aBody['data']['map_type'] = $sMapType;
            if (!empty($iUtcOffset)) {
                $aBody['utc_offset']         = $iUtcOffset;
                $aBody['data']['utc_offset'] = $iUtcOffset;
            }

            // 利用bool短路也行
            // false !== $iUtcOffset && $aBody['utc_offset']         = $iUtcOffset;
            // false !== $iUtcOffset && $aBody['data']['utc_offset'] = $iUtcOffset;
            $aBatchBody[] = json_encode($aBody, JSON_UNESCAPED_UNICODE);
        }

        // empty($iHashKey) && $iHashKey = rand(0, 128);
        $oCarrera = CarreraClient::getInstance();
        $oRet     = $oCarrera->batchSend(self::TOPIC, $iHashKey, $aBatchBody);
        if (is_object($oRet) && $oRet->code > 1) {
            $sErrMsg = [
                'topic'    => self::TOPIC,
                'hash_key' => $iHashKey,
            ];
            Log::warning(Msg::formatArray(Code::E_COMMON_CARRERA_SEND_FAIL, $sErrMsg));
            return false;
        }

        return true;
    }
}
