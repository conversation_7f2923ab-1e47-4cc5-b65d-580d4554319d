<?php

namespace PreSale\Models\guideCardItem\container;

use PreSale\Models\guideCardView\guideCard\BaseGuideCard;

/**
 * 追加车型容器
 */
class GuideAnycarContainer extends BaseGuideContainer implements \JsonSerializable
{
    /**
     * 容器垂直渐变色，无渐变色传单个颜色即可
     * @var array
     */
    private $_aGradientColor = [];

    /**
     * 卡片列表
     * @var array
     */
    private $_aCardList = [];

    /**
     * @return array
     */
    public function getAGradientColor() {
        return $this->_aGradientColor;
    }

    /**
     * @param array $aGradientColor 容器背景色配置
     * @return void
     */
    public function setAGradientColor($aGradientColor) {
        $this->_aGradientColor = $aGradientColor;
    }

    /**
     * @return BaseGuideCard[]
     */
    public function getCardList() {
        return $this->_aCardList;
    }

    /**
     * @param BaseGuideCard[] $aCardList 卡片列表
     * @return void
     */
    public function setCardList($aCardList) {
        $this->_aCardList = $aCardList;
    }

    /**
     * @return array
     */
    public function jsonSerialize() {
        return [
            'container_type' => BaseGuideContainer::CONTAINER_TYPE_NO_BORDER,
            'container_data' => [
                'bg_gradient_color' => $this->_aGradientColor,
                'card_list'         => $this->_aCardList,
            ],
        ];
    }
}
