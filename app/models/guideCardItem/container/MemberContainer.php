<?php

namespace PreSale\Models\guideCardItem\container;

use PreSale\Models\guideCardView\guideCard\BaseGuideCard;

/**
 * 会员包框
 */
class MemberContainer extends BaseGuideContainer implements \JsonSerializable
{
    private $_sTitleImage = '';

    /**
     * 迭代二期
     * @var array
     */
    private $_aBgGradientColor;

    /** 让端上复用原有逻辑，等二期切到无guide_pos的情况
     * @var int
     */
    private $_iGuidePos;
    /**
     * @var array
     */
    private $_aCardList = [];

    /**
     * @return array
     */
    public function getBgGradientColor() {
        return $this->_aBgGradientColor;
    }

    /**
     * @param array $aBgGradientColor 垂直渐变色
     * @return $this
     */
    public function setBgGradientColor($aBgGradientColor) {
        $this->_aBgGradientColor = $aBgGradientColor;
        return $this;
    }

    /**
     * @param string $sTitleImage titleImage 会员图片
     * @return $this
     */
    public function setTitleImage($sTitleImage) {
        $this->_sTitleImage = $sTitleImage;
        return $this;
    }

    /**
     * @return int
     */
    public function getGuidePos() {
        return $this->_iGuidePos;
    }

    /**
     * @param int $iGuidePos guidePos
     * @return $this
     */
    public function setGuidePos($iGuidePos) {
        $this->_iGuidePos = $iGuidePos;
        return $this;
    }

    /**
     * @param BaseGuideCard[] $aCardList cardList
     * @return $this
     */
    public function setCardList($aCardList) {
        $this->_aCardList = $aCardList;
        return $this;
    }

    /**
     * @return BaseGuideCard[]
     */
    public function getCardList() {
        return $this->_aCardList;
    }

    /**
     * @return array|mixed
     */
    public function jsonSerialize() {
        $aRet = [
            'container_type' => BaseGuideContainer::CONTAINER_TYPE_MEMBER,
            'container_data' => [
                'title_image' => $this->_sTitleImage,
                'card_list'   => $this->_aCardList,
            ],
        ];

        if (!empty($this->_aBgGradientColor)) {
            $aRet['container_data']['bg_gradient_color'] = $this->_aBgGradientColor;
        }

        if (!empty($this->_iGuidePos)) {
            $aRet['guide_pos'] = (string)$this->_iGuidePos;
        }

        return $aRet;
    }
}
