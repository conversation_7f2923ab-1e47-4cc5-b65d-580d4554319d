<?php

namespace PreSale\Models\guideCardItem\container;

use PreSale\Models\guideCardView\guideCard\BaseGuideCard;

/**
 * 出口卡片包框base类
 */
abstract class BaseGuideContainer
{
    // 聚合样式 （n张卡片的外框）
    const CONTAINER_TYPE_MEMBER         = 1; // 聚合包框样式 (包含固定边距)
    const CONTAINER_TYPE_GRADIENT_COLOR = 2; // 通用出口卡片样式包框 （包含固定边距)
    const CONTAINER_TYPE_NO_STYLE       = 3; // 无样式包框
    const CONTAINER_TYPE_NO_BORDER      = 4; // 默认包框样式 (无固定边距 适用于追加车型这种无固定边距的出口)
    const CONTAINER_TYPE_GRADIENT_V2    = 5; // v2包框样式 (加价调度新的边框)

    /**
     * @return BaseGuideCard[]
     */
    abstract public function getCardList();

    /**
     * @param BaseGuideCard[] $aCardList cardList
     * @return void
     */
    abstract public function setCardList($aCardList);
}
