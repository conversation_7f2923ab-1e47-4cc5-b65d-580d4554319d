<?php

namespace PreSale\Models\guideCardItem;

use BizLib\Utils\Language;

/**
 * 获取取消挽留athena推荐文案
 */
class TextService
{
    private $_sNamespaceKey;

    /**
     * @param string $sNamespaceKey namespaceKey
     * @return void
     */
    public function setNamespaceKey($sNamespaceKey) {
        $this->_sNamespaceKey = $sNamespaceKey;
    }

    /**
     * 获取 config_text.php -> pGetOrderMatchInfo 的文案.
     * @param string $sKey        文案key
     * @param array  $aReplaceTag 要替换的tag
     * @return string
     */
    public function getText($sKey, $aReplaceTag = []) {
        // 从dcmp获取文案
        $sTextKey = $this->_sNamespaceKey.$sKey;
        //兜底避免Dcmp获取失败
        return Language::getTextFromDcmp($sTextKey, $aReplaceTag);
    }
}
