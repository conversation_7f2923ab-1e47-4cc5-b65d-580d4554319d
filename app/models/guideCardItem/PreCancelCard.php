<?php

namespace PreSale\Models\guideCardItem;

use BizCommon\Logics\Anycar\AnyCarCommonLogic;
use BizCommon\Logics\PassengerBaseLogic;
use BizCommon\Utils\Horae as HoraeUtil;
use BizLib\Client\MambaClient;
use BizLib\Constants\Horae;
use BizLib\Constants\OrderSystem;
use BizLib\Log as NuwaLog;
use BizLib\Utils\ApolloHelper;
use BizLib\Utils\Language;
use BizLib\Utils\Passport;
use BizLib\Utils\ProductCategory;
use BizLib\Utils\UtilHelper;
use Dukang\PropertyConst\ProductCenter\ProductMaterial;
use Nuwa\ApolloSDK\Apollo;
use PPreCancelOrderController;
use PreSale\Infrastructure\Repository\Ufs\UfsRepository;
use PreSale\Logics\anycar\AnycarEstimateLogic;
use PreSale\Logics\order\AnyCarOrderLogic;
use PreSale\Models\guideCardItem\button\actionData\ActionData;
use PreSale\Models\guideCardItem\button\GuideCardStyleManage;
use PreSale\Models\guideCardItem\button\OmniButton;
use PreSale\Models\guideCardItem\container\BaseGuideContainer;
use PreSale\Models\guideCardItem\guideCard\GuideBookingCard;
use PreSale\Models\guideCardView\guideCard\BaseGuideCard;
use PreSale\Models\guideCardView\guideCard\GuideFartherCard;
use PreSale\Domain\Model\MiniBus\MiniBus;
use PreSale\Models\guideCardView\guideView\BaseViewRender;
use BizLib\Client\DuseApiClient;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;
use Pts\Scene\Constants\Scene;
use Xiaoju\Apollo\ApolloConstant;
use BizLib\Config;
use PreSale\Logics\compensation\Common;
use PreSale\Logics\compensation\CommonCompensationLogic;
use BizCommon\Logics\Carpool\CarpoolPriceFormat as CarpoolPriceUtil;
use BizLib\Config as NuwaConfig;

/**
 * 取消挽留弹窗
 */
class PreCancelCard implements \JsonSerializable
{

    const  BOOKING_PRE_CANCEL_TIME = 'booking_pre_cancel_go_now_time';

    // 预取消挽留场景
    const PREPARE_CANCEL_ORDER_SCENE = 'prepare_cancel_order_scene';

    // 长单感知优化场景实验命中结果ufs key
    const UFS_KEY_OF_LONG_DISTANCE_PERCEPTION_OPTIMIZE = 'long_distance_order.experiment_result';

    // 是否命中长单感知优化实验组
    const IS_HIT_LONG_DISTANCE_PERCEPTION_STRATEGY = 1;

    const COUPON_TYPE_DEDUCATION= 'coupon_type_deducation'; // 立减券
    const COUPON_TYPE_DISCOUNT = 'coupon_type_discount'; // 折扣券

    const COMPENSATION_ING = 'compensation_ing'; // 获取中

    const COMPENSATION_DONE = 'compensation_done'; // 已获得

    const SUPPLY_SCENE_TYPE_PRIVILEGE = 1;    //  愿等场景

    const SUPPLY_SCENE_TYPE_APPEND_ANYCAR = 2; // 追加场景

    const EXPIRE_THRESHOLD_FOR_COUPON = 3 * 24 * 60 * 60;

    /**
     * @var string
     */
    private $_sTopImage;

    /**
     * @var string
     */
    private $_sTitle;

    /**
     * @var string
     */
    private $_subTitle;

    /**
     * @var string
     */
    private $_subTitleColor;

    /**
     * @var string
     */
    private $_sToastMsg;

    /**
     * @var string
     */
    private $_sCountTime;

    /**
     * @var string
     */
    private $_sCountType;

    /**
     * @var string
     */
    private $_sIsSpecialRate;

    /**
     * @var array
     */
    private $_aCardInfo;

    /**
     * @var array
     */
    private $_oButtons;

    /**
     * @var OmniOmegaInfo
     */
    private $_oOmegaInfo;

    /**
     * @var OmniOmegaInfo
     */
    private $_oHiddenOmegaInfo;

    /**
     * @var OmniOmegaInfo
     */
    private $_oCloseOmegaInfo;

    /**
     * @var string
     */
    private $_sBgColor;

    /**
     * @var string
     */
    private $_sTopImageScale;

    /**
     * @var string
     */
    private $_sCouponMsg;

    /**
     * @var string
     */
    private $_sCouponDesc;

    /**
     * @var float
     */
    private $_fDiscountAmount;

    /**
     * @var int
     */
    private $_iQueueRank;

    /**
     * @var int
     */
    private $_iQueueLen;

    /**
     * @var int
     */
    private $_iEtsTime;

    /**
     * @var int
     */
    private $_iWaitTime;

    const MINI_BUS_PRE_MATCH_FAIL = -1;

    /**
     * @param string $sCouponMsg 券信息
     * @return void
     */
    public function setCouponMsg($sCouponMsg) {
        $this->_sCouponMsg = $sCouponMsg;
    }

    /**
     * @param string $sCouponDesc 券描述
     * @return void
     */
    public function setCouponDesc($sCouponDesc) {
        $this->_sCouponDesc = $sCouponDesc;
    }

    /**
     * @return mixed
     */
    public function getTopImage() {
        return $this->_sTopImage;
    }

    /**
     * @param string $sTopImage 顶部图片
     * @return void
     */
    public function setTopImage($sTopImage) {
        $this->_sTopImage = $sTopImage;
    }

    /**
     * @return string
     */
    public function getTitle() {
        return $this->_sTitle;
    }

    /**
     * @param string $sTitle 主标题
     * @return void
     */
    public function setTitle($sTitle) {
        $this->_sTitle = $sTitle;
    }

    /**
     * @return array
     */
    public function getCardInfo() {
        return $this->_aCardInfo;
    }

    /**
     * @param array $aCardInfo 卡片数据
     * @return void
     */
    public function setCardInfo($aCardInfo) {
        $this->_aCardInfo = $aCardInfo;
    }

    /**
     * @return array
     */
    public function getButtons() {
        return $this->_oButtons;
    }

    /**
     * @param array $oButtons 按钮数据
     * @return void
     */
    public function setButtons($oButtons) {
        $this->_oButtons = $oButtons;
    }

    /**
     * @return string
     */
    public function getSubTitle() {
        return $this->_subTitle;
    }

    /**
     * @param string $subTitle 副标题
     * @return void
     */
    public function setSubTitle($subTitle) {
        $this->_subTitle = $subTitle;
    }

    /**
     * @return string
     */
    public function getSubTitleColor() {
        return $this->_subTitleColor;
    }

    /**
     * @param string $subTitleColor 副标题颜色
     * @return void
     */
    public function setSubTitleColor($subTitleColor) {
        $this->_subTitleColor = $subTitleColor;
    }


    /**
     * @return string
     */
    public function getToastMsg() {
        return $this->_sToastMsg;
    }

    /**
     * @param string $sToastMsg toast_msg
     * @return void
     */
    public function setToastMsg($sToastMsg) {
        $this->_sToastMsg = $sToastMsg;
    }

    /**
     * @return string
     */
    public function getCountTime() {
        return $this->_sCountTime;
    }

    /**
     * @param string $sCountTime count_time
     * @return void
     */
    public function setCountTime($sCountTime) {
        $this->_sCountTime = $sCountTime;
    }

    /**
     * @return string
     */
    public function getCountType() {
        return $this->_sCountType;
    }

    /**
     * @param string $sCountType count_type
     * @return void
     */
    public function setCountType($sCountType) {
        $this->_sCountType = $sCountType;
    }

    /**
     * @return string
     */
    public function getIsSpecialRate() {
        return $this->_sIsSpecialRate;
    }

    /**
     * @param string $sIsSpecialRate is_special_rate
     * @return void
     */
    public function setIsSpecialRate($sIsSpecialRate) {
        $this->_sIsSpecialRate = $sIsSpecialRate;
    }

    /**
     * @return OmniOmegaInfo
     */
    public function getOOmegaInfo() {
        return $this->_oOmegaInfo;
    }

    /**
     * @param OmniOmegaInfo $oOmegaInfo 埋点数据
     * @return void
     */
    public function setOOmegaInfo($oOmegaInfo) {
        $this->_oOmegaInfo = $oOmegaInfo;
    }

    /**
     * @param OmniOmegaInfo $oHiddenOmegaInfo 埋点数据
     * @return void
     */
    public function setHiddenOmegaInfo($oHiddenOmegaInfo) {
        $this->_oHiddenOmegaInfo = $oHiddenOmegaInfo;
    }

    /**
     * @param OmniOmegaInfo $oCloseOmegaInfo 埋点数据
     * @return void
     */
    public function setCloseOmegaInfo($oCloseOmegaInfo) {
        $this->_oCloseOmegaInfo = $oCloseOmegaInfo;
    }

    /**
     * @return string
     */
    public function getBgColor() {
        return $this->_sBgColor;
    }

    /**
     * @param  string $sBgColor 取消订单挽留卡片背景色
     * @return void
     */
    public function setBgColor($sBgColor) {
        $this->_sBgColor = $sBgColor;
    }

    /**
     * @return string
     */
    public function getTopImageScale() {
        return $this->_sTopImageScale;
    }

    /**
     * @param  string $sTopImageScale 标题图大小
     * @return void
     */
    public function setTopImageScale($sTopImageScale) {
        $this->_sTopImageScale = $sTopImageScale;
    }

    /**
     * @param BaseGuideContainer[] $aCardInfo                可导流的出口
     * @param array                $aBookingGoNowCardInfo    立刻出发卡片
     * @param array                $aCancelCard              取消挽留卡片数据
     * @param array                $aRequest                 请求参数
     * @param array                $aOrderInfo               订单数据
     * @param bool                 $bAllowShowNewStyle       是否展示新样式
     * @param MatchResultRuntime   $oRuntimeData             数据总线
     * @param bool                 $isPreMatchOrder          是否是预匹配
     * @param array                $aAthenaExpectInfo        Athena预期信息
     * @param bool                 $isBookingMockDriverOrder 是否是预约单有车订单
     * @return void
     */
    public function buildCard($aCardInfo, $aBookingGoNowCardInfo, $aCancelCard, $aRequest, $aOrderInfo, $bAllowShowNewStyle, $oRuntimeData,$isPreMatchOrder,$aAthenaExpectInfo,$isBookingMockDriverOrder) {

        // 新实验判断
        $bHitNewStyle = false;
        $aNewCardText = [];
        if ($oRuntimeData->bNewPopExam && $this->_isAllowNew($aOrderInfo,$isPreMatchOrder)) {
            $bHitNewStyle = true;
            $aNewCardText = $this->_getNewStyleCardText($oRuntimeData,$aAthenaExpectInfo);
        }


        //  文案的选择要根据 预约出发时间与现在的时间差值来判断得到。
        $aCardText = $this->getCardText($aOrderInfo, $aBookingGoNowCardInfo, $isPreMatchOrder, $oRuntimeData, $isBookingMockDriverOrder);

        $this->initMaterial($aOrderInfo['line_up'], $aCardText, $bAllowShowNewStyle, $aCancelCard);

        $sConfirmButtonText = $aCardText['multiple_recommend_confirm_button_text'];
        $sCancelButtonText  = $aCardText['multiple_recommend_cancel_button_text'];

        // 处理cardInfo
        if (!empty($aCardInfo) && count($aCardInfo) > 0 && empty($aCancelCard['privilege'])) {
            $iIsRecommend = 1;
            $aCardList    = ($aCardInfo[0])->getCardList();
            $oGuideCard   = $aCardList[0];
            $iRecType     = $oGuideCard->getIRecType();
            if (BaseViewRender::REC_TYPE_APPEND_ANYCAR == $iRecType || BaseViewRender::REC_TYPE_RELATION_PASSENGER == $iRecType
                || BaseViewRender::REC_TYPE_CARPOOL_CAR == $iRecType
            ) {
                $aEstimateInfoOfOmega = $oRuntimeData->getGuideAnycarEstimateDataOfOmega();
            }

            if (BaseViewRender::REC_TYPE_APPEND_MULTIPLE_RECOMMEND_ANYCAR == $iRecType) {
                $aEstimateInfoOfOmega = $oRuntimeData->getGuideMultiAnycarEstimateDataOfOmega();
            }
        } else {
            $iIsRecommend = 0;
            $iRecType     = -1;
        }

        $bGoNowCardFlag            = false;
        $bCheckBooingOrderABSwitch = \BizCommon\Logics\Order\BookingOrder::checkBooingOrderABSwitch($aOrderInfo);
        if ($bCheckBooingOrderABSwitch) {
            $this->setSubTitle('');
            // 处理 立即呼叫 $aBookingGoNowCardInfo
            if (!empty($aBookingGoNowCardInfo) && count($aBookingGoNowCardInfo) > 0) {
                $aCardList      = ($aBookingGoNowCardInfo[0])->getCardList();
                $oGuideCard     = $aCardList[0];
                $iIsRecommend   = 0;
                $iRecType       = -1;
                $bGoNowCardFlag = true;
            }
        }

        if (!empty($aCancelCard['privilege']) && count($aCancelCard['privilege']) > 0 && !$bGoNowCardFlag) {
            // 把privilege 转换成新的卡片结构返回
            $aPrivilege = $aCancelCard['privilege'];
            // 复用加价调度卡片
            foreach ($aPrivilege as $item) {
                $oCard         = new GuideFartherCard();
                $sCardTitle    = $item['title'] ?? '';
                $sCardSubTitle = $item['msg'] ?? '';
                $sLinkUrl      = $item['link_url'] ?? '';
                $oCard->setTitle($sCardTitle);
                $oCard->setSubTitle([$sCardSubTitle]);
                $oButton = new OmniButton();
                $oButton->setText($aCardText['button_text']);
                $oButton->setActionType(OmniButton::ACTION_TYPE_LINK_H5);
                $oActionData = new ActionData();
                $oActionData->setSUrl($sLinkUrl);
                $oButton->setActionData($oActionData);
                $oButton->setButtonStyle(GuideCardStyleManage::getDefaultButtonStyle());
                $oCard->setButton($oButton);
                $oGuideContainer = (new GuideViewList())->getContainerCard($oCard);
                $aCardInfo[]     = $oGuideContainer;
            }
        }
        $aExtendFeature = json_decode($aOrderInfo['extend_feature'],true);
        $aMRPEstimateInfo = [];
        if (is_array($aExtendFeature['multi_require_product'])) {
            foreach ($aExtendFeature['multi_require_product'] as $aItem) {
                $aMRPEstimateInfo[$aItem['product_category']] = [
                    'estimate_id' => $aItem['estimate_id'],
                    'page_type'   => $aExtendFeature['page_type'],
                    'product_id'  => $aItem['product_id'],
                ];
                if($aItem['is_append']) {
                    $aMRPEstimateInfo[$aItem['product_category']]['page_type'] = 20;//等应答页面
                }
            }
        }

        // 处理卡片曝光埋点
        $oOmegaInfo   = new OmniOmegaInfo();
        $aOmegaParams = [
            'order_id'          => $aOrderInfo['order_id'],
            'uid'               => UtilHelper::getUidByPassengerId($aOrderInfo['passenger_id'], Passport::PASSENGER_ROLE),
            'area'              => $aOrderInfo['area'],
            'is_recommend'      => $iIsRecommend,
            'rec_type'          => $iRecType,
            'mrp_estimate_info' => json_encode($aMRPEstimateInfo),
        ];

        if ($isPreMatchOrder) {
            $aOmegaParams['activity'] = 'retain_reassign';
            $aOmegaParams['activity_status'] = 1;
        }

        if ($bHitNewStyle && !empty($aNewCardText)) {
            $aOmegaParams['commu_type']  = $aNewCardText['commu_type'];
            $aOmegaParams['coupon_type'] = $aNewCardText['coupon_type'];
        }

        if (!empty($aEstimateInfoOfOmega)) {
            $aOmegaParams['estimate_info'] = json_encode($aEstimateInfoOfOmega);
        }

        $oOmegaInfo->setSKey(OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_SHOW, $aRequest['access_key_id']))
            ->setAParams($aOmegaParams);
        $this->setOOmegaInfo($oOmegaInfo);
        if ($bCheckBooingOrderABSwitch && $bGoNowCardFlag) {
            // 命中实验   GoNowCard 有数据
            $this->setCardInfo($aBookingGoNowCardInfo);
        } else {
            $this->setCardInfo($aCardInfo);
        }

        // 生成按钮
        $oLeftButton  = new OmniButton();
        $oRightButton = new OmniButton();
        if ($bAllowShowNewStyle) {
            // 设置按钮埋点
            $oCancelOmega = new OmniOmegaInfo();
            $aCancelActionOmegaParam = [
                'order_id'          => $aOrderInfo['order_id'],
                'uid'               => UtilHelper::getUidByPassengerId($aOrderInfo['passenger_id'], Passport::PASSENGER_ROLE),
                'area'              => $aOrderInfo['area'],
                'mrp_estimate_info' => json_encode($aMRPEstimateInfo),
            ];

            if ($isPreMatchOrder) {
                $aCancelActionOmegaParam['activity'] = "retain_reassign";
                $aCancelActionOmegaParam['custom_activity_status'] = 1;
            }

            if ($bHitNewStyle && !empty($aNewCardText)) {
                $aCancelActionOmegaParam['coupon_type'] = $aNewCardText['coupon_type'];
            }

            $oCancelOmega->setSKey(OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_CONFIRM_CANCEL_CK, $aRequest['access_key_id']))
                ->setAParams($aCancelActionOmegaParam);

            $oLeftButton->setText($sConfirmButtonText)
                ->setActionType(OmniButton::ACTION_TYPE_CANCEL_ORDER)
                ->setOmegaInfo($oCancelOmega)
                ->setButtonStyle(GuideCardStyleManage::getMultipleRecommendConfirmCancelButtonStyle());
            // 设置按钮埋点
            $oNoCancelOmega = new OmniOmegaInfo();

            $aNoCancelActionOmegaParam = [
                'order_id'          => $aOrderInfo['order_id'],
                'uid'               => UtilHelper::getUidByPassengerId($aOrderInfo['passenger_id'], Passport::PASSENGER_ROLE),
                'area'              => $aOrderInfo['area'],
                'mrp_estimate_info' => json_encode($aMRPEstimateInfo),
                'estimate_info'     => $aOmegaParams['estimate_info'],
            ];
            if ($isPreMatchOrder) {
                $aNoCancelActionOmegaParam['activity'] = "retain_reassign";
                $aNoCancelActionOmegaParam['custom_activity_status'] = 1;
            }
            if ($bHitNewStyle && !empty($aNewCardText)) {
                $aNoCancelActionOmegaParam['coupon_type'] = $aNewCardText['coupon_type'];
            }
            $oNoCancelOmega->setSKey(OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_NO_CANCEL_CK, $aRequest['access_key_id']))
                ->setAParams($aNoCancelActionOmegaParam);
            $oRightButton->setText($sCancelButtonText)
                ->setActionType(OmniButton::ACTION_TYPE_CLOSE_POPUP)
                ->setOmegaInfo($oNoCancelOmega)
                ->setButtonStyle(GuideCardStyleManage::getMultipleRecommendNoCancelButtonStyle());
            $this->setButtons([$oLeftButton, $oRightButton]);
        } else {
            // 设置按钮埋点
            $oNoCancelOmega = new OmniOmegaInfo();
            $aNoCancelActionOmegaParam = [
                'order_id'      => $aOrderInfo['order_id'],
                'uid'           => UtilHelper::getUidByPassengerId($aOrderInfo['passenger_id'], Passport::PASSENGER_ROLE),
                'area'          => $aOrderInfo['area'],
                'estimate_info' => $aOmegaParams['estimate_info'],
            ];

            if ($isPreMatchOrder) {
                $aNoCancelActionOmegaParam['activity'] = "retain_reassign";
                $aNoCancelActionOmegaParam['custom_activity_status'] = 1;
            }

            $oNoCancelOmega->setSKey(OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_NO_CANCEL_CK, $aRequest['access_key_id']))
                ->setAParams($aNoCancelActionOmegaParam);
            $oLeftButton->setText($sCancelButtonText)
                ->setActionType(OmniButton::ACTION_TYPE_CLOSE_POPUP)
                ->setOmegaInfo($oNoCancelOmega)
                ->setButtonStyle(GuideCardStyleManage::getNoCancelButtonStyle($oRuntimeData->iIsWaitAnswerUpgrade));
            // 设置按钮埋点
            $oCancelOmega = new OmniOmegaInfo();
            $aCancelActionOmegaParam = [
                'order_id'      => $aOrderInfo['order_id'],
                'uid'           => UtilHelper::getUidByPassengerId($aOrderInfo['passenger_id'], Passport::PASSENGER_ROLE),
                'area'          => $aOrderInfo['area'],
                'estimate_info' => $aOmegaParams['estimate_info'],
            ];

            if ($isPreMatchOrder) {
                $aCancelActionOmegaParam['activity'] = "retain_reassign";
                $aCancelActionOmegaParam['custom_activity_status'] = 1;
            }
            $oCancelOmega->setSKey(OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_CONFIRM_CANCEL_CK, $aRequest['access_key_id']))
                ->setAParams($aCancelActionOmegaParam);
            $oRightButton->setText($sConfirmButtonText)
                ->setActionType(OmniButton::ACTION_TYPE_CANCEL_ORDER)
                ->setOmegaInfo($oCancelOmega)
                ->setButtonStyle(GuideCardStyleManage::getConfirmCancelButtonStyle($oRuntimeData->iIsWaitAnswerUpgrade));
                $this->setButtons([$oRightButton, $oLeftButton]);
        }

        // 市内小巴 只勾选了小巴 取消挽留文案
        if (\BizCommon\Utils\Horae::isOnlyMiniBusCarpoolOrder($aOrderInfo)) {
            $oExtendFeature = json_decode($aOrderInfo['extend_feature'], true);
            $aMRP = $oExtendFeature['multi_require_product']??[];
            $bIsMiniBusPreMatchSuccess = true;
            //是否预匹配成功
            foreach ($aMRP as $oProduct) {
                if(OrderSystem::CARPOOL_TYPE_MINI_BUS == $oProduct['carpool_type'] && self::MINI_BUS_PRE_MATCH_FAIL == $oProduct['etp']) {
                    $bIsMiniBusPreMatchSuccess = false;
                    break;
                }
            }

            $oApollo = Apollo::getInstance();
            $oToggle = $oApollo->featureToggle('minibus_prematch_fail_cfg',[
                ApolloConstant::APOLLO_INDIVIDUAL_ID => $aOrderInfo['passenger_id'],
                'city'                               => $aOrderInfo['area'],
                'app_version'                        => $oRuntimeData->getRequest()['appversion'],
                'access_key_id'                      => $oRuntimeData->getRequest()['access_key_id'],
                'phone'                              => $aOrderInfo['passenger_phone'],
            ]);

            if($oToggle->allow() && $oRuntimeData->iIsWaitAnswerUpgrade) {
                $aMiniBusProduct = [];
                $fTP1EstimateFee = 0.0;//TP1价格最高
                foreach ($aMRP as $aProduct) {
                    if ($aProduct['estimate_fee'] > $fTP1EstimateFee) {
                        $fTP1EstimateFee = $aProduct['estimate_fee'];
                    }
                }

                $aProductList = [];
                if(!empty($oRuntimeData->oAnycarEstimateViewModel->aAnycarEstimate)) {
                    $aProductList = $oRuntimeData->oAnycarEstimateViewModel->getProductList();
                }
                $fFastCarEstimateFee = -1;
                foreach ($aProductList as $aProduct) {
                    if(ProductCategory::PRODUCT_CATEGORY_FAST == $aProduct['product_category']) {
                        $fFastCarEstimateFee = $aProduct['price'];
                        break;
                    }
                }
                $sEstimateFee = CarpoolPriceUtil::priceFormatCeil($fTP1EstimateFee, $aOrderInfo['area'], $aOrderInfo['passenger_id'], '');
                $sCheapFee = CarpoolPriceUtil::priceFormatCeil(bcsub(strval($fFastCarEstimateFee), $sEstimateFee, 2), $aOrderInfo['area'], $aOrderInfo['passenger_id'], '');
                $aReplaceHolder = [
                    'fee'         => $sEstimateFee,
                    'cheap_price' => $sCheapFee,
                ];
                $aText       = json_decode(Language::getTextFromDcmp('pre_cancel-mini_bus',$aReplaceHolder),true)??[];
                $aText = $aText['candidate_2'];
                $this->setTitle($aText['title']);
                $this->setSubTitleColor($aText['sub_title_color']);
                $this->setSubTitle($aReplaceHolder['cheap_price'] <= 0 ?$aText['subtitle_without_diff']:$aText['subtitle']);
                $this->setTopImage($aText['top_image']);
                $this->setCardInfo(null);
                $aButtons = $this->getButtons();
                $oLeftButton =$aButtons[0];
                $oRightButton = $aButtons[1];
                $oLeftButton->setButtonStyle(GuideCardStyleManage::getConfirmCancelButtonStyleForMiniBus());
                $oRightButton->setButtonStyle(GuideCardStyleManage::getNoCancelButtonStyleForminiBus());
                $this->setButtons([$oLeftButton, $oRightButton]);
            } else {
                $aText       = json_decode(Language::getTextFromDcmp('pre_cancel-mini_bus'),true)??[];
                $this->setTitle($aText['title']);
                if(!(Horae::PAGE_TYPE_MINIBUS == $oExtendFeature['page_type'] && !$bIsMiniBusPreMatchSuccess)) {
                    $aToggleCondition = [
                        'key'  => $aOrderInfo['passenger_id'],
                        'city' => $aOrderInfo['area'],
                    ];
                    $oToggle = \Xiaoju\Apollo\Apollo::getInstance()->featureToggle('minibus_wait_page_hold_time', $aToggleCondition);

                    $iMiniBusHoldTime = $oToggle->getParameter('minibus_wait_page_default_hold_time', 30);
                    if(time() < strtotime($aOrderInfo['_create_time']) + $iMiniBusHoldTime) {
                        $this->setSubTitle(Language::replaceTag($aText['subtitle'], ['expire_time' => $iMiniBusHoldTime]));
                    }
                }

                $this->setTopImage($aText['top_image']);
                // 不追加车型
                $this->setCardInfo(null);
            }
        }

        // 智能小巴 只勾选了小巴 取消挽留文案
        if (\BizCommon\Utils\Horae::isOnlySmartMiniBusCarpoolOrder($aOrderInfo)) {
            $oExtendFeature = json_decode($aOrderInfo['extend_feature'], true);
            $aMRP = $oExtendFeature['multi_require_product']??[];

            $aTP1Item =[];//TP1
            foreach ($aMRP as $aProduct) {
                if (empty($aTP1Item) || $aProduct['estimate_fee'] > $aTP1Item['estimate_fee']) {
                    $aTP1Item = $aProduct;
                }
            }

            $aReplaceHolder = [
                'tp1_price' => $aTP1Item['estimate_fee'],
            ];
            $aText = json_decode(Language::getTextFromDcmp('pre_cancel-smart_carpool',$aReplaceHolder),true)??[];
            $aText = ($aText[(string)$aTP1Item['product_id']]??$aText["default"])['style_1'];

            $this->setTopImage($aText['top_image']);
            $this->setTitle($aText['title']);
            $this->setSubTitle($aText['subtitle']);
            $this->setSubTitleColor($aText['sub_title_color']);
            $this->setCardInfo(null);
            $aButtons = $this->getButtons();
            $oLeftButton =$aButtons[0];
            $oRightButton = $aButtons[1];
            $oLeftButton->setButtonStyle(GuideCardStyleManage::getConfirmCancelButtonStyleForSmartCarpool());
            $oRightButton->setButtonStyle(GuideCardStyleManage::getNoCancelButtonStyleForSmartCarpool());
            $this->setButtons([$oLeftButton, $oRightButton]);

        }

        // 只发单惠选车时的文案
        if (is_array($aExtendFeature['multi_require_product'])
            && 1 == sizeof($aExtendFeature['multi_require_product'])
            && \BizCommon\Utils\Horae::isAnycarFastRangeOrder($aOrderInfo)
        ) {
            $aText = Language::getDecodedTextFromDcmp('pre_cancel-fast_car_range');
            $area = $oRuntimeData->getOrderInfo()['area'];
            $lang = $oRuntimeData->getRequest()['lang'];
            $aText['confirm_button_text'] = ProductMaterial::FormatProductMaterial($aText['confirm_button_text'],$area,$lang);
            $this->setTitle($aText['common_title']);
            $this->setSubTitle(null);
            $this->setTopImage($aText['top_image']);
            if (OmniButton::ACTION_TYPE_CANCEL_ORDER == $oRightButton->getActionType()) {
                $oRightButton->setText($aText['confirm_button_text']);
                $oLeftButton->setText($aText['cancel_button_text']);
            } else {
                $oRightButton->setText($aText['cancel_button_text']);
                $oLeftButton->setText($aText['confirm_button_text']);
            }

            $this->setButtons([$oRightButton, $oLeftButton]);
            // 不追加车型
            $this->setCardInfo(null);
            if (time() - strtotime($aOrderInfo['new_time']) <= $aText['time']) {
                $aFastCarRangeRT = $this->_getFastCarRangeRT($aOrderInfo);
                if (!empty($aFastCarRangeRT)) {
                    $iPrice = floor($aFastCarRangeRT['fast_car_estimate_fee'] - $aFastCarRangeRT['max_price']);
                    if ($iPrice > 0) {
                        $this->setTitle(Language::replaceTag($aText['title'], ['price' => $iPrice]));
                    }
                }
            }
        }

        if ($bHitNewStyle && !empty($aNewCardText)) {
            $this->setTitle($aNewCardText['title']);
            $this->setSubTitle("");
            $this->setTopImage($aNewCardText['top_image']);
            $this->setCouponMsg($aNewCardText['coupon_msg']);
            $this->setCouponDesc($aNewCardText['coupon_desc']);
            $aOmegaParams = [
                'order_id'      => $aOrderInfo['order_id'],
                'uid'           => UtilHelper::getUidByPassengerId($aOrderInfo['passenger_id'], Passport::PASSENGER_ROLE),
                'area'          => $aOrderInfo['area'],
                'is_recommend'  => $iIsRecommend,
                'rec_type'      => $iRecType,
                'commu_type'    => $aNewCardText['commu_type'],
                'coupon_type'   => $aNewCardText['coupon_type'],
                'estimate_info' => $aEstimateInfoOfOmega ?? null,
            ];
            $oHiddenOmegaInfo = new OmniOmegaInfo();
            $oHiddenOmegaInfo->setSKey(OmniOmegaInfo::PRE_CANCEL_ORDER_RETAIN_DURING_SW)->setAParams($aOmegaParams);
            $this->setHiddenOmegaInfo($oHiddenOmegaInfo);

            $oCloseOmegaInfo = new OmniOmegaInfo();
            $oCloseOmegaInfo->setSKey(OmniOmegaInfo::getOmegaSourceReplaceV2(OmniOmegaInfo::PRE_CANCEL_ORDER_CANCELINTER_CK, $aRequest['access_key_id']))
                ->setAParams($aOmegaParams);
            $this->setCloseOmegaInfo($oCloseOmegaInfo);
        }

    }

    /**
     * @param BaseGuideContainer[] $aCardInfo 可导流的出口
     * @param array $aRequest 请求参数
     * @param MatchResultRuntime   $oRuntimeData 数据总线
     * @param array $aOrderMatchRecommendResult 导流推荐数据
     * @param int $iSceneType 场景类型
     * @param array $aOrderEtsInfo ets信息
     * @param array $aAthenaExpectInfo  Athena预期信息
     * @return void
     */
    public function buildCardV2($aCardInfo, $aRequest, $oRuntimeData, $aOrderMatchRecommendResult, $iSceneType, $aOrderEtsInfo, $aAthenaExpectInfo) {
        // 先弄个兜底咯
        $aText     = NuwaConfig::text('pre_cancel', 'new_guide_card_v2_conf');
        $this->_buildDefaultCard($aRequest, $aText);

        switch ($iSceneType) {
            case self::SUPPLY_SCENE_TYPE_PRIVILEGE:
                $this->_buildPrivilegeCard($oRuntimeData, $aRequest, $aOrderEtsInfo, $aAthenaExpectInfo, $aText);
                break;
            case self::SUPPLY_SCENE_TYPE_APPEND_ANYCAR:
                $this->_buildAppendAnyCarCard($aCardInfo, $aRequest, $aOrderMatchRecommendResult, $aText);
                break;
        }

        // 最后走品类特殊逻辑，防止被实验覆盖了
        $this->_buildProductSpecialCard($oRuntimeData, $aRequest, $aText);
    }
    /**
     * @param array $aRequest 请求参数
     * @param array $aText 文案
     * @return void
     */
    private function _buildDefaultCard($aRequest, $aText) {
        $aDefaultText = $aText['default'];
        $aOmegaParams['text'] = $aDefaultText['title'];
        $aOmegaParams['rec_type'] = BaseViewRender::REC_TYPE_DEFAULT;
        $oLeftButton  = new OmniButton();
        $oLeftButton->setText($aDefaultText['cancel_button_text'])
            ->setActionType(OmniButton::ACTION_TYPE_CANCEL_ORDER)
            ->setButtonStyle(GuideCardStyleManage::getMultipleRecommendConfirmCancelButtonStyle());

        $oConfirmOmegaInfo = new OmniOmegaInfo();
        $oConfirmOmegaInfo->setSKey(OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_NO_CANCEL_CK, $aRequest['access_key_id']))
            ->setAParams($aOmegaParams);
        $oRightButton = new OmniButton();
        $oRightButton->setText($aDefaultText['confirm_button_text'])
            ->setActionType(OmniButton::ACTION_TYPE_CLOSE_POPUP)
            ->setOmegaInfo($oConfirmOmegaInfo)
            ->setButtonStyle(GuideCardStyleManage::getMultipleRecommendNoCancelButtonStyle());

        $oOmegaInfo = new OmniOmegaInfo();
        $oOmegaInfo->setSKey(OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_SHOW, $aRequest['access_key_id']))
            ->setAParams($aOmegaParams);
        $this->setTitle($aDefaultText['title']);
        $this->setTopImage($aDefaultText['top_image']);
        $this->setCardInfo(null);
        $this->setButtons([$oLeftButton, $oRightButton]);
        $this->setOOmegaInfo($oOmegaInfo);
    }

    /**
     * @param BaseGuideContainer[] $aCardInfo 可导流的出口
     * @param array $aRequest 请求参数
     * @param array $aOrderMatchRecommendResult  导流推荐数据
     * @param array $aText 文案
     * @return void
     */
    private function _buildAppendAnyCarCard($aCardInfo, $aRequest, $aOrderMatchRecommendResult, $aText) {
        if (empty($aCardInfo)) {
            return;
        }

        $aCardList    = ($aCardInfo[0])->getCardList();
        if (empty($aCardList)) {
            return;
        }

        $oGuideCard   = $aCardList[0];
        if (empty($oGuideCard)) {
            return;
        }

        switch ($oGuideCard->getIRecType()) {
            case BaseViewRender::REC_TYPE_GUIDE_FARTHER:
                $this->_buildFartherCard($aCardInfo, $aRequest, $aOrderMatchRecommendResult, $oGuideCard, $aText);
                break;
            case BaseViewRender::REC_TYPE_APPEND_MULTIPLE_RECOMMEND_ANYCAR:
                $this->_buildMultiRecommendAnyCarCard($aCardInfo, $aRequest, $oGuideCard, $aText);
                break;
            default:
                break;
        }
    }

    /**
     * @param BaseGuideContainer[] $aCardInfo 可导流的出口
     * @param MatchResultRuntime   $oRuntimeData 数据总线
     * @param array $aOrderMatchRecommendResult  导流推荐数据
     * @param BaseGuideCard $oGuideCard 卡片
     * @param array $aText 文案
     * @return void
     */
    private function _buildFartherCard($aCardInfo, $aRequest, $aOrderMatchRecommendResult, $oGuideCard, $aText) {
        $aFartherText = $aText['farther'];
        $aCarNameList = json_decode(Language::getTextFromDcmp('config_anycar-anycar_simple_name'), true);
        $sCarTitle     = $aCarNameList[AnyCarCommonLogic::getGroupKey($aOrderMatchRecommendResult['target_product'])] ?? '车辆';
        $sTitle = Language::replaceTag($aFartherText['title'], ['car_title' => $sCarTitle]);

        // 埋点参数
        $aOmegaParams = $oGuideCard->getOmegaParams();
        $aOmegaParams['text'] = $sTitle;
        $oLeftButton  = new OmniButton();
        $oLeftButton->setText($aFartherText['cancel_button_text'])
            ->setActionType(OmniButton::ACTION_TYPE_CANCEL_ORDER)
            ->setButtonStyle(GuideCardStyleManage::getMultipleRecommendCancelButtonStyle());

        // 埋点
        $oConfirmOmegaInfo = new OmniOmegaInfo();
        $oConfirmOmegaInfo->setSKey(OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_RECOMMEND_CK, $aRequest['access_key_id']))
            ->setAParams($aOmegaParams);
        // 点击按钮的参数
        if (GuideFactory::GUIDE_TYPE_FARTHER_FOR_ANYCAR == $aOrderMatchRecommendResult['type']) {
            $sGroupKey            = AnyCarCommonLogic::getGroupKey($aOrderMatchRecommendResult['target_product']);
            $sMultiRequireProduct = [
                [
                    'group_key'    => $sGroupKey,
                    'max_distance' => $aOrderMatchRecommendResult['etpDistance'],
                ],
            ];
            $aActionParams = [
                'guide_pos'             => GuideFactory::GUIDE_TYPE_FARTHER_FOR_ANYCAR,
                'multi_require_product' => json_encode($sMultiRequireProduct),
            ];
        } else {
            $aActionParams = [
                'guide_pos'    => GuideFactory::GUIDE_TYPE_FARTHER,
                'max_distance' => $aOrderMatchRecommendResult['etpDistance'],
            ];
        }

        $oRightButton = new OmniButton();
        $oRightButton->setText($aFartherText['confirm_button_text'])
            ->setActionType(OmniButton::ACTION_TYPE_UPDATE_ORDER)
            ->setOmegaInfo($oConfirmOmegaInfo)
            ->setAButtonParams($aActionParams)
            ->setButtonStyle(GuideCardStyleManage::getMultipleRecommendConfirmButtonStyle());

        $oOmegaInfo = new OmniOmegaInfo();
        $oOmegaInfo->setSKey(OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_SHOW, $aRequest['access_key_id']))
            ->setAParams($aOmegaParams);
        $this->setTitle($sTitle);
        $this->setTopImage($aFartherText['top_image']);
        $this->setCardInfo($aCardInfo);
        $this->setButtons([$oLeftButton, $oRightButton]);
        $this->setOOmegaInfo($oOmegaInfo);
    }

    /**
     * @param BaseGuideContainer[] $aCardInfo 可导流的出口
     * @param array $aRequest 请求参数
     * @param BaseGuideCard $oGuideCard 卡片
     * @param array $aText 文案
     * @return void
     */
    private function _buildMultiRecommendAnyCarCard($aCardInfo, $aRequest, $oGuideCard, $aText) {
        $aMultiRecommendText = $aText['multi_recommend'];
        // 埋点参数
        $aOmegaParams = $oGuideCard->getOmegaParams();
        $aOmegaParams['text'] = $aMultiRecommendText['title'];

        $oLeftButton  = new OmniButton();
        $oLeftButton->setText($aMultiRecommendText['cancel_button_text'])
            ->setActionType(OmniButton::ACTION_TYPE_CANCEL_ORDER)
            ->setButtonStyle(GuideCardStyleManage::getMultipleRecommendCancelButtonStyle());

        $oConfirmOmegaInfo = new OmniOmegaInfo();
        $oConfirmOmegaInfo->setSKey(OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_RECOMMEND_CK, $aRequest['access_key_id']))
            ->setAParams($aOmegaParams);
        $oRightButton = new OmniButton();
        $oRightButton->setText($aMultiRecommendText['confirm_button_text'])
            ->setActionType(OmniButton::ACTION_TYPE_ANYCAR_NEW_ORDER)
            ->setAButtonParams($oGuideCard->getActionParams())
            ->setOmegaInfo($oConfirmOmegaInfo)
            ->setButtonStyle(GuideCardStyleManage::getMultipleRecommendConfirmButtonStyle());

        $oOmegaInfo = new OmniOmegaInfo();
        $oOmegaInfo->setSKey(OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_SHOW, $aRequest['access_key_id']))
            ->setAParams($aOmegaParams);
        $this->setTitle($aMultiRecommendText['title']);
        $this->setTopImage($aMultiRecommendText['top_image']);
        $this->setCardInfo($aCardInfo);
        $this->setButtons([$oLeftButton, $oRightButton]);
        $this->setOOmegaInfo($oOmegaInfo);
    }

    /**
     * 构建权益卡片
     * @param MatchResultRuntime $oRuntimeData 数据总线
     * @param array $aRequest 请求参数
     * @param array $aOrderEtsInfo ets信息
     * @param array $aAthenaExpectInfo Athena预期信息
     * @param array $aText 文案
     * @return void
     */
    private function _buildPrivilegeCard($oRuntimeData, $aRequest, $aOrderEtsInfo, $aAthenaExpectInfo, $aText) {
        $aPrivilegeText = $aText['privilege'];
        $aCardInfo = $this->_getPrivilegeCardInfo($oRuntimeData, $aOrderEtsInfo, $aAthenaExpectInfo, $aPrivilegeText);
        // 命中兜底了
        if (empty($aCardInfo)) {
            return;
        }

        $aOmegaParams = [
            'text'     => $aCardInfo['title'],
            'rec_type' => $aCardInfo['rec_type'],
        ];

        $aButtons = $this->_buildPrivilegeCardButton($aOmegaParams, $aRequest, $aText);

        $this->_setPrivilegeCardInfo($aCardInfo, $aButtons, $aOmegaParams, $aRequest);
    }

    /**
     * 获取权益卡片信息
     * @param MatchResultRuntime $oRuntimeData 数据总线
     * @param array $aOrderEtsInfo ets信息
     * @param array $aAthenaExpectInfo  athena预期
     * @param array $aPrivilegeText 文案
     * @return array
     */
    private function _getPrivilegeCardInfo($oRuntimeData, $aOrderEtsInfo, $aAthenaExpectInfo, $aPrivilegeText) {
        // 无车赔场景
        $aCardInfo = $this->_getCompensationCardInfo($oRuntimeData, $aPrivilegeText);
        if (!empty($aCardInfo)) {
            return $aCardInfo;
        }

        // 升舱场景
        if ($this->_hitUpgrade($oRuntimeData)) {
            return [
                'rec_type'  => BaseViewRender::REC_TYPE_UPGRADE,
                'title'     => $aPrivilegeText['upgrade']['title'],
                'top_image' => $aPrivilegeText['upgrade']['top_image'],
            ];
        }

        // 优惠券场景
        if ($this->_hitCoupon($oRuntimeData)) {
            return [
                'rec_type'    => BaseViewRender::REC_TYPE_COUPON,
                'title'       => Language::replaceTag(
                    $aPrivilegeText['coupon']['title'],
                    ['coupon_amount' => $this->_fDiscountAmount]
                ),
                'top_image'   => $aPrivilegeText['coupon']['top_image'],
                'coupon_msg'  => Language::replaceTag($aPrivilegeText['coupon']['coupon_msg'],
                    ['coupon_amount' => $this->_fDiscountAmount]),
                'coupon_desc' => $aPrivilegeText['coupon']['coupon_desc'],
            ];
        }

        // 排队场景
        if ($this->_hitQueue($aAthenaExpectInfo)) {
            return $this->_getQueueCardInfo($aPrivilegeText);
        }

        // ets场景
        if ($this->_hitEts($oRuntimeData, $aAthenaExpectInfo)) {
            return $this->_getEtsCardInfo($aAthenaExpectInfo, $aPrivilegeText);
        }

        return null;
    }

    /**
     * 获取无车赔卡片信息
     * @param MatchResultRuntime $oRuntimeData 数据总线
     * @param array $aPrivilegeText 文案
     * @return array
     */
    private function _getCompensationCardInfo($oRuntimeData, $aPrivilegeText) {
        $oCommonCompensationLogic = CommonCompensationLogic::getInstance();
        $oCommonCompensationLogic->init($oRuntimeData->getCompensationLogic(),$oRuntimeData->getNormalNoCarCompensationLogic());
        $oCouponInfo = $oCommonCompensationLogic->getCouponInfo();
        // 赔付是否成功
        if (empty($oCouponInfo) || $oCouponInfo->getCompensationStatus() != Common::COMPENSATION_STATUS_SUCCESS) {
            return null;
        }

        $iCouponType = $oCouponInfo->getCouponType();
        $aCompensationText = $aPrivilegeText['compensation'];
        $aCouponInfo = $this->_getCompensationCouponInfo($oCouponInfo, $iCouponType, $aCompensationText);
        if (empty($aCouponInfo)) {
            return null;
        }

        return [
            'rec_type'    => BaseViewRender::REC_TYPE_COMPENSATION_SUCCESS,
            'title'       => $aCouponInfo['title'],
            'top_image'   => $aCompensationText['top_image'],
            'coupon_msg'  => $aCouponInfo['coupon_msg'],
            'coupon_desc' => $aCouponInfo['coupon_desc'],
        ];
    }

    /**
     * 获取赔付信息
     * @param object $oCouponInfo 券信息
     * @param int $iCouponType 券类型
     * @param array $aCompensationText 文案
     * @return array
     */
    private function _getCompensationCouponInfo($oCouponInfo, $iCouponType, $aCompensationText) {
        if ($iCouponType == CommonCompensationLogic::COUPON_TYPE_AMOUNT) {
            $sAmount = $this->formatNumFloor($oCouponInfo->getCouponAmount(), 1);
            return [
                'title'       => Language::replaceTag($aCompensationText['amount_title'], ['amount' => $sAmount]),
                'coupon_msg'  => Language::replaceTag($aCompensationText['coupon_msg_deducation'], ['num' => $sAmount]),
                'coupon_desc' => $aCompensationText['coupon_desc_deducation'],
            ];
        }

        if ($iCouponType == CommonCompensationLogic::COUPON_TYPE_DISCOUNT) {
            $sDiscount = $this->formatNumFloor($oCouponInfo->getCouponDiscount(), 1);
            return [
                'title'       => Language::replaceTag($aCompensationText['discount_title'], ['discount' => $sDiscount]),
                'coupon_msg'  => Language::replaceTag($aCompensationText['coupon_msg_discount'], ['num' => $sDiscount]),
                'coupon_desc' => $aCompensationText['coupon_desc_discount'],
            ];
        }

        return null;
    }

    /**
     * 获取排队卡片信息
     * @param array $aPrivilegeText 文案
     * @return array
     */
    private function _getQueueCardInfo($aPrivilegeText) {
        $aQueueText = $aPrivilegeText['queue'];
        if ($this->_iQueueRank <= 3) {
            $sTitle = Language::replaceTag($aQueueText['top_priority']['title'], ['rank' => $this->_iQueueRank]);
            $sTopImage = $aQueueText['top_priority']['top_image'];
        } else {
            $iRankDiff = $this->_iQueueLen - $this->_iQueueRank;
            $sTitle = $iRankDiff > 3 ? Language::replaceTag($aQueueText['high_pressure']['title'], ['num' => $iRankDiff]) : $aQueueText['growing_queue']['title'];
            $sTopImage = $iRankDiff > 3 ? $aQueueText['high_pressure']['top_image'] : $aQueueText['growing_queue']['top_image'];
        }

        return [
            'rec_type'  => BaseViewRender::REC_TYPE_QUEUE,
            'title'     => $sTitle,
            'top_image' => $sTopImage,
        ];
    }

    /**
     * 获取ets卡片信息
     * @param array $aAthenaExpectInfo Athena预期信息
     * @param array $aPrivilegeText 文案
     * @return array
     */
    private function _getEtsCardInfo($aAthenaExpectInfo, $aPrivilegeText) {
        $aEtsText = $aPrivilegeText['ets'];
        $iEts = $this->_getEts($aAthenaExpectInfo); // 实际ets
        if (empty($iEts)) {
            return null;
        }

        // 已等时间≤发单预估ETS*80%
        if ($this->_iWaitTime > $this->_iEtsTime * 0.8) {
            $sTitle = $iEts > 60 ? Language::replaceTag($aEtsText['comfort']['title_minute'], ['minute' => ceil($iEts / 60)]) : Language::replaceTag($aEtsText['comfort']['title_second'], ['second' => $iEts]);
            $sTopImage = $aEtsText['comfort']['top_image'];
        } else {
            $sTitle = $iEts > 60 ? Language::replaceTag($aEtsText['default']['title_minute'], ['minute' => ceil($iEts / 60)]) : Language::replaceTag($aEtsText['default']['title_second'], ['second' => $iEts]);
            $sTopImage = $aEtsText['default']['top_image'];
        }

        return [
            'rec_type'  => BaseViewRender::REC_TYPE_ETS,
            'title'     => $sTitle,
            'top_image' => $sTopImage,
        ];
    }

    /**
     * 构建权益卡片按钮
     * @param array $aOmegaParams 埋点数据
     * @param array $aRequest 请求参数
     * @param array $aText 文案
     * @return array
     */
    private function _buildPrivilegeCardButton($aOmegaParams, $aRequest, $aText) {
        $oLeftButton = new OmniButton();
        $oLeftButton->setText($aText['default']['cancel_button_text'])
            ->setActionType(OmniButton::ACTION_TYPE_CANCEL_ORDER)
            ->setButtonStyle(GuideCardStyleManage::getMultipleRecommendConfirmCancelButtonStyle());

        $oConfirmOmegaInfo = new OmniOmegaInfo();
        $oConfirmOmegaInfo->setSKey(OmniOmegaInfo::getOmegaSourceReplace(
            OmniOmegaInfo::PRE_CANCEL_ORDER_NO_CANCEL_CK,
            $aRequest['access_key_id']
        ))->setAParams($aOmegaParams);

        $oRightButton = new OmniButton();
        $oRightButton->setText($aText['default']['confirm_button_text'])
            ->setActionType(OmniButton::ACTION_TYPE_CLOSE_POPUP)
            ->setOmegaInfo($oConfirmOmegaInfo)
            ->setButtonStyle(GuideCardStyleManage::getMultipleRecommendNoCancelButtonStyle());

        return [$oLeftButton, $oRightButton];
    }

    /**
     * 设置权益卡片信息
     * @param array $aCardInfo 卡片信息
     * @param array $aButtons 按钮
     * @param array $aOmegaParams 埋点
     * @param array $aRequest 请求参数
     * @return void
     */
    private function _setPrivilegeCardInfo($aCardInfo, $aButtons, $aOmegaParams, $aRequest) {
        $oOmegaInfo = new OmniOmegaInfo();
        $oOmegaInfo->setSKey(OmniOmegaInfo::getOmegaSourceReplace(
            OmniOmegaInfo::PRE_CANCEL_ORDER_SHOW,
            $aRequest['access_key_id']
        ))->setAParams($aOmegaParams);

        $this->setTitle($aCardInfo['title']);
        $this->setTopImage($aCardInfo['top_image']);
        $this->setCardInfo(null);
        $this->setButtons($aButtons);
        $this->setOOmegaInfo($oOmegaInfo);
        $this->setCouponMsg($aCardInfo['coupon_msg']);
        $this->setCouponDesc($aCardInfo['coupon_desc']);
    }

    /**
     * 命中本单可用的升舱
     * @param MatchResultRuntime   $oRuntimeData 数据总线
     * @return bool
     */
    private function _hitUpgrade($oRuntimeData) {
        $aOrderInfo = $oRuntimeData->getOrderInfo();
        if (!isset($aOrderInfo['extend_feature'])) {
            return false;
        }

        $aOrderInfoExtendFeature = json_decode($aOrderInfo['extend_feature'],true);
        if (empty($aOrderInfoExtendFeature)) {
            return false;
        }

        if (!isset($aOrderInfoExtendFeature['multi_require_product'])) {
            return false;
        }

        $aMultiProductRequire = $aOrderInfoExtendFeature['multi_require_product'];
        foreach ($aMultiProductRequire as $aItem) {
            if (empty($aItem['scene_list'])) {
                continue;
            }

            $aSceneList = json_decode($aItem['scene_list'], true);
            foreach ($aSceneList as $aScene) {
                if (empty($aScene['id'])) {
                    continue;
                }

                if ($aScene['id'] == Scene::WAIT_FREE_UPGRADE_ID) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 命中即将过期的优惠券
     * @param MatchResultRuntime   $oRuntimeData 数据总线
     * @return bool
     */
    private function _hitCoupon($oRuntimeData) {
        $aQuotation = $oRuntimeData->aQuotation;
        if(empty($aQuotation)) {
            return false;
        }

        $fOptimalCouponAmount = 0.0;
        foreach ($aQuotation as $aOneQuotation) {
            if (empty($aOneQuotation) || empty($aOneQuotation['discount_desc'])) {
                continue;
            }

            $aDiscountDesc = json_decode($aOneQuotation['discount_desc'], true);
            if (empty($aDiscountDesc) || sizeof($aDiscountDesc) < 1) {
                continue;
            }

            foreach ($aDiscountDesc as $aItem) {
                if (empty($aItem) || empty($aItem['amount']) || empty($aItem['expire_time'])) {
                    continue;
                }

                // 已经过期或者超过三天才过期
                $iTimeDiff = strtotime($aItem['expire_time']) - time();
                if ($iTimeDiff < 0 || $iTimeDiff > self::EXPIRE_THRESHOLD_FOR_COUPON) {
                    continue;
                }

                // 计算一个券优惠最大值
                if ($aItem['amount'] > $fOptimalCouponAmount) {
                    $fOptimalCouponAmount    = $aItem['amount'];
                }
            }
        }

        if (!empty($fOptimalCouponAmount)) {
            $this->_fDiscountAmount = $fOptimalCouponAmount;
            return true;
        }

        return false;
    }

    /**
     * 命中排队场景
     * @param array $aAthenaExpectInfo Athena预期信息
     * @return bool
     */
    private function _hitQueue($aAthenaExpectInfo) {
        if (empty($aAthenaExpectInfo)) {
            return false;
        }

        if (empty($aAthenaExpectInfo['order_match_global_scene_expect']) || empty($aAthenaExpectInfo['order_match_global_scene_expect']['product_infos'])) {
            return false;
        }

        $aProductQueueMap = [];
        $aProductInfos = $aAthenaExpectInfo['order_match_global_scene_expect']['product_infos'];
        foreach ($aProductInfos as $aProductInfo) {
            if (empty($aProductInfo['product_category']) || empty($aProductInfo['expect_info']) || empty($aProductInfo['expect_info']['queue_info'])) {
                continue;
            }

            $aQueueInfo = $aProductInfo['expect_info']['queue_info'];
            if (empty($aQueueInfo['queue_len']) || empty($aQueueInfo['rank']) || $aQueueInfo['queue_len'] <= 0 || $aQueueInfo['rank'] <= 0) {
                continue;
            }

            $aProductQueueMap[$aProductInfo['product_category']] = array(
                'queue_len' => $aQueueInfo['queue_len'],
                'rank'      => $aQueueInfo['rank'],
            );
        }

        if (empty($aProductQueueMap)) {
            return false;
        }

        // 快车＞专车＞优享＞拼车
        uksort($aProductQueueMap, function ($key1, $key2) {
            $priorityOrder = [
                '1' => 0,
                '6' => 1,
                '8' => 2,
                '3' => 3,
            ];
            $priority1 = $priorityOrder[$key1] ?? PHP_INT_MAX;
            $priority2 = $priorityOrder[$key2] ?? PHP_INT_MAX;

            if ($priority1 !== $priority2) {
                return $priority1 <=> $priority2;
            }

            return $key1 <=> $key2;
        });

        $aQueueInfo = current($aProductQueueMap);
        $this->_iQueueLen = $aQueueInfo['queue_len'];
        $this->_iQueueRank = $aQueueInfo['rank'];
        if ($this->_iQueueLen <= 0 || $this->_iQueueRank <= 0) {
            return false;
        }

        if ($this->_iQueueLen >= $this->_iQueueRank) {
            return true;
        }

        return false;
    }

    /**
     * 命中ets场景
     * @param MatchResultRuntime $oRuntimeData 数据总线
     * @param array $aAthenaExpectInfo athena预期信息
     * @return bool
     */
    private function _hitEts($oRuntimeData, $aAthenaExpectInfo) {
        if (empty($aAthenaExpectInfo) || empty($aAthenaExpectInfo['order_match_global_scene_expect']) || empty($aAthenaExpectInfo['order_match_global_scene_expect']['origin_time_start'])) {
            return false;
        }

        $iOrderBirthTime = strtotime($oRuntimeData->getOrderInfo()['_birth_time']);
        $this->_iWaitTime = time() - $iOrderBirthTime; // 已经等待的时长 = 当前时间 - 发单时间
        $this->_iEtsTime = $aAthenaExpectInfo['order_match_global_scene_expect']['origin_time_start'] - $iOrderBirthTime; // 发单预估ets时长 = athena发单时预估应答时间 - 发单时间
        if ($this->_iWaitTime < 0 ||  $this->_iEtsTime < 0) {
            return false;
        }

        if ($this->_iWaitTime > $this->_iEtsTime) {
            return false;
        }

        return true;
    }


    /**
     * @param MatchResultRuntime   $oRuntimeData 数据总线
     * @param array $aRequest 请求参数
     * @param array $aText 文案
     * @return void
     */
    private function _buildProductSpecialCard($oRuntimeData, $aRequest, array $aText) {
        $aOrderInfo = $oRuntimeData->getOrderInfo();
        $aExtendFeature = json_decode($aOrderInfo['extend_feature'], true);

        $aDefaultText = $aText['default'];
        $oLeftButton  = new OmniButton();
        $oRightButton = new OmniButton();
        // 市内小巴 只勾选了小巴 取消挽留文案
        if (\BizCommon\Utils\Horae::isOnlyMiniBusCarpoolOrder($aOrderInfo)) {
            $aMRP = $aExtendFeature['multi_require_product'] ?? [];
            $bIsMiniBusPreMatchSuccess = true;
            //是否预匹配成功
            foreach ($aMRP as $oProduct) {
                if(OrderSystem::CARPOOL_TYPE_MINI_BUS == $oProduct['carpool_type'] && self::MINI_BUS_PRE_MATCH_FAIL == $oProduct['etp']) {
                    $bIsMiniBusPreMatchSuccess = false;
                    break;
                }
            }

            $oApollo = Apollo::getInstance();
            $oToggle = $oApollo->featureToggle('minibus_prematch_fail_cfg',[
                ApolloConstant::APOLLO_INDIVIDUAL_ID => $aOrderInfo['passenger_id'],
                'city'                               => $aOrderInfo['area'],
                'app_version'                        => $oRuntimeData->getRequest()['appversion'],
                'access_key_id'                      => $oRuntimeData->getRequest()['access_key_id'],
                'phone'                              => $aOrderInfo['passenger_phone'],
            ]);

            if($oToggle->allow() && $oRuntimeData->iIsWaitAnswerUpgrade) {
                $fTP1EstimateFee = 0.0;//TP1价格最高
                foreach ($aMRP as $aProduct) {
                    if ($aProduct['estimate_fee'] > $fTP1EstimateFee) {
                        $fTP1EstimateFee = $aProduct['estimate_fee'];
                    }
                }

                $aProductList = [];
                if(!empty($oRuntimeData->oAnycarEstimateViewModel->aAnycarEstimate)) {
                    $aProductList = $oRuntimeData->oAnycarEstimateViewModel->getProductList();
                }
                $fFastCarEstimateFee = -1;
                foreach ($aProductList as $aProduct) {
                    if(ProductCategory::PRODUCT_CATEGORY_FAST == $aProduct['product_category']) {
                        $fFastCarEstimateFee = $aProduct['price'];
                        break;
                    }
                }
                $sEstimateFee = CarpoolPriceUtil::priceFormatCeil($fTP1EstimateFee, $aOrderInfo['area'], $aOrderInfo['passenger_id'], '');
                $sCheapFee = CarpoolPriceUtil::priceFormatCeil(bcsub(strval($fFastCarEstimateFee), $sEstimateFee, 2), $aOrderInfo['area'], $aOrderInfo['passenger_id'], '');
                $aReplaceHolder = [
                    'fee'         => $sEstimateFee,
                    'cheap_price' => $sCheapFee,
                ];
                $aText       = json_decode(Language::getTextFromDcmp('pre_cancel-mini_bus',$aReplaceHolder),true)??[];
                $aText = $aText['candidate_2'];
                $this->setTitle($aText['title']);
                $this->setSubTitleColor($aText['sub_title_color']);
                $this->setSubTitle($aReplaceHolder['cheap_price'] <= 0 ?$aText['subtitle_without_diff']:$aText['subtitle']);
                $this->setTopImage($aText['top_image']);
                $this->setCardInfo(null);
            } else {
                $aText = json_decode(Language::getTextFromDcmp('pre_cancel-mini_bus'),true) ?? [];
                $this->setTitle($aText['title']);
                if(!(Horae::PAGE_TYPE_MINIBUS == $aExtendFeature['page_type'] && !$bIsMiniBusPreMatchSuccess)) {
                    $aToggleCondition = [
                        'key'  => $aOrderInfo['passenger_id'],
                        'city' => $aOrderInfo['area'],
                    ];
                    $oToggle = \Xiaoju\Apollo\Apollo::getInstance()->featureToggle('minibus_wait_page_hold_time', $aToggleCondition);

                    $iMiniBusHoldTime = $oToggle->getParameter('minibus_wait_page_default_hold_time', 30);
                    if(time() < strtotime($aOrderInfo['_create_time']) + $iMiniBusHoldTime) {
                        $this->setSubTitle(Language::replaceTag($aText['subtitle'], ['expire_time' => $iMiniBusHoldTime]));
                    }
                }
                $this->setTopImage($aText['top_image']);
                $this->setCardInfo(null);
            }

            $oLeftButton->setText($aDefaultText['cancel_button_text'])
                ->setActionType(OmniButton::ACTION_TYPE_CANCEL_ORDER)
                ->setButtonStyle(GuideCardStyleManage::getConfirmCancelButtonStyleForMiniBus());
            $aOmegaParams['text'] = $this->getTitle();
            $aOmegaParams['rec_type'] = BaseViewRender::REC_TYPE_MINI_BUS;
            $oConfirmOmegaInfo = new OmniOmegaInfo();
            $oConfirmOmegaInfo->setSKey(OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_RECOMMEND_CK, $aRequest['access_key_id']))
                ->setAParams($aOmegaParams);
            $oRightButton->setText($aDefaultText['confirm_button_text'])
                ->setActionType(OmniButton::ACTION_TYPE_CLOSE_POPUP)
                ->setOmegaInfo($oConfirmOmegaInfo)
                ->setButtonStyle(GuideCardStyleManage::getNoCancelButtonStyleForminiBus());
            $oOmegaInfo = new OmniOmegaInfo();
            $oOmegaInfo->setSKey(OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_SHOW, $aRequest['access_key_id']))
                ->setAParams($aOmegaParams);
            $this->setOOmegaInfo($oOmegaInfo);
            $this->setButtons([$oLeftButton, $oRightButton]);
        }

        // 智能小巴 只勾选了小巴 取消挽留文案
        if (\BizCommon\Utils\Horae::isOnlySmartMiniBusCarpoolOrder($aOrderInfo)) {
            $aMRP = $aExtendFeature['multi_require_product'] ?? [];
            $aTP1Item =[];//TP1
            foreach ($aMRP as $aProduct) {
                if (empty($aTP1Item) || $aProduct['estimate_fee'] > $aTP1Item['estimate_fee']) {
                    $aTP1Item = $aProduct;
                }
            }

            $aReplaceHolder = [
                'tp1_price' => $aTP1Item['estimate_fee'],
            ];
            $aText = json_decode(Language::getTextFromDcmp('pre_cancel-smart_carpool',$aReplaceHolder),true)??[];
            $aText = ($aText[(string)$aTP1Item['product_id']]??$aText["default"])['style_1'];
            $this->setTopImage($aText['top_image']);
            $this->setTitle($aText['title']);
            $this->setSubTitle($aText['subtitle']);
            $this->setSubTitleColor($aText['sub_title_color']);
            $this->setCardInfo(null);

            $oLeftButton->setText($aDefaultText['cancel_button_text'])
                ->setActionType(OmniButton::ACTION_TYPE_CANCEL_ORDER)
                ->setButtonStyle(GuideCardStyleManage::getConfirmCancelButtonStyleForSmartCarpool());
            $aOmegaParams['text'] = $this->getTitle();
            $aOmegaParams['rec_type'] = BaseViewRender::REC_TYPE_SMART_BUS;
            $oConfirmOmegaInfo = new OmniOmegaInfo();
            $oConfirmOmegaInfo->setSKey(OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_RECOMMEND_CK, $aRequest['access_key_id']))
                ->setAParams($aOmegaParams);
            $oRightButton->setText($aDefaultText['confirm_button_text'])
                ->setActionType(OmniButton::ACTION_TYPE_CLOSE_POPUP)
                ->setOmegaInfo($oConfirmOmegaInfo)
                ->setButtonStyle(GuideCardStyleManage::getNoCancelButtonStyleForSmartCarpool());
            $oOmegaInfo = new OmniOmegaInfo();
            $oOmegaInfo->setSKey(OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_SHOW, $aRequest['access_key_id']))
                ->setAParams($aOmegaParams);
            $this->setOOmegaInfo($oOmegaInfo);
            $this->setButtons([$oLeftButton, $oRightButton]);
        }

        // 只发单惠选车时的文案
        if (is_array($aExtendFeature['multi_require_product'])
            && 1 == sizeof($aExtendFeature['multi_require_product'])
            && \BizCommon\Utils\Horae::isAnycarFastRangeOrder($aOrderInfo)
        ) {
            $aText = Language::getDecodedTextFromDcmp('pre_cancel-fast_car_range');
            $area = $oRuntimeData->getOrderInfo()['area'];
            $lang = $oRuntimeData->getRequest()['lang'];
            $aText['confirm_button_text'] = ProductMaterial::FormatProductMaterial($aText['confirm_button_text'],$area,$lang);
            $this->setTitle($aText['common_title']);
            $this->setSubTitle(null);
            $this->setTopImage($aText['top_image']);
            // 不追加车型
            $this->setCardInfo(null);
            if (time() - strtotime($aOrderInfo['new_time']) <= $aText['time']) {
                $aFastCarRangeRT = $this->_getFastCarRangeRT($aOrderInfo);
                if (!empty($aFastCarRangeRT)) {
                    $iPrice = floor($aFastCarRangeRT['fast_car_estimate_fee'] - $aFastCarRangeRT['max_price']);
                    if ($iPrice > 0) {
                        $this->setTitle(Language::replaceTag($aText['title'], ['price' => $iPrice]));
                    }
                }
            }

            $oLeftButton->setText($aText['confirm_button_text'])
                ->setActionType(OmniButton::ACTION_TYPE_CANCEL_ORDER)
                ->setButtonStyle(GuideCardStyleManage::getMultipleRecommendConfirmCancelButtonStyle());
            $aOmegaParams['text'] = $this->getTitle();
            $aOmegaParams['rec_type'] = BaseViewRender::REC_TYPE_FAST_RANGE;
            $oConfirmOmegaInfo = new OmniOmegaInfo();
            $oConfirmOmegaInfo->setSKey(OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_RECOMMEND_CK, $aRequest['access_key_id']))
                ->setAParams($aOmegaParams);
            $oRightButton->setText($aText['cancel_button_text'])
                ->setActionType(OmniButton::ACTION_TYPE_CLOSE_POPUP)
                ->setOmegaInfo($oConfirmOmegaInfo)
                ->setButtonStyle(GuideCardStyleManage::getMultipleRecommendNoCancelButtonStyle());
            $oOmegaInfo = new OmniOmegaInfo();
            $oOmegaInfo->setSKey(OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_SHOW, $aRequest['access_key_id']))
                ->setAParams($aOmegaParams);
            $this->setOOmegaInfo($oOmegaInfo);
            $this->setButtons([$oLeftButton, $oRightButton]);
        }
    }

    /**
     * @decs 命中新样式判断
     * @param array $aOrderInfo     订单数据
     * @param bool  $isPreMatchOrder  预匹配
     * @return bool
     */
    private function _isAllowNew($aOrderInfo,$isPreMatchOrder) {
        // 预约单
        if (OrderSystem::TYPE_ORDER_BOOKING == $aOrderInfo['type']) {
            return false;
        }

        // 预匹配
        if ($isPreMatchOrder) {
            return false;
        }

        // 单勾小巴
        if (\BizCommon\Utils\Horae::isOnlyMiniBusCarpoolOrder($aOrderInfo)) {
            return false;
        }


        // 单勾特惠
        $aExtendFeature = json_decode($aOrderInfo['extend_feature'],true);
        if (is_array($aExtendFeature['multi_require_product'])
            && 1 == sizeof($aExtendFeature['multi_require_product'])
            && \BizCommon\Utils\Horae::isAnycarFastRangeOrder($aOrderInfo)
        ) {
            return false;
        }

        return true;
    }

    /**
     * @param array $aOrderInfo 订单数据
     * @return array
     */
    private function _getFastCarRangeRT($aOrderInfo) {
        $oDuseApiClient = new DuseApiClient();
        $sTraceId       = \Disf\SPL\Trace::traceId();
        $sSpanId        = \Disf\SPL\Trace::spanId();
        $iFormatOrderId = UtilHelper::genHighIntOrderId($aOrderInfo['order_id'], $aOrderInfo['district']);
        $iCityId        = \BizLib\Utils\MapHelper::getAreaIdByDistrict($aOrderInfo['district']);
        $aResult        = $oDuseApiClient->getOrder(
            'gulfstream',
            $iFormatOrderId,
            $iCityId,
            array('passenger_bargain_range_info'),
            'gs-api',
            $sTraceId,
            $sSpanId,
            $aOrderInfo
        );
        if (0 != $aResult['errno']) {
            return [];
        }

        if (!empty($aResult['result']['passenger_bargain_range_info'])) {
            return json_decode($aResult['result']['passenger_bargain_range_info'], true);
        }

        return [];
    }

    /**
     * @return array
     */
    public function jsonSerialize() {
        $aRet = [
            'top_image'       => $this->_sTopImage,
            'title'           => $this->_sTitle,
            'top_image_scale' => $this->_sTopImageScale,
            'bg_color'        => $this->_sBgColor,
            'buttons'         => $this->_oButtons,
        ];

        if (!empty($this->_subTitle)) {
            $aRet['sub_title'] = $this->_subTitle;
        }

        if (!empty($this->_subTitleColor)) {
            $aRet['sub_title_color'] = $this->_subTitleColor;
        }

        if (!empty($this->_aCardInfo)) {
            $aRet['card_info'] = $this->_aCardInfo;
        }

        if (!empty($this->_oOmegaInfo)) {
            $aRet['omega_info'] = $this->_oOmegaInfo;
        }

        if (!empty($this->_oHiddenOmegaInfo)) {
            $aRet['duration_omega'] = $this->_oHiddenOmegaInfo;
        }

        if (!empty($this->_oCloseOmegaInfo)) {
            $aRet['close_omega_info'] = $this->_oCloseOmegaInfo;
        }

        if (!empty($this->_sCouponMsg)) {
            $aRet['coupon_msg'] = $this->_sCouponMsg;
        }

        if (!empty($this->_sCouponDesc)) {
            $aRet['coupon_desc'] = $this->_sCouponDesc;
        }

        return $aRet;
    }

    /**
     * @param $line_up
     * @param $aCardText
     * @param bool $bAllowShowNewStyle
     * @param array $aCancelCard
     * @return void
     */
    public function initMaterial($line_up, $aCardText, bool $bAllowShowNewStyle, array $aCancelCard): void {
        $sIsLineUp = $line_up;
        if ('0' == $sIsLineUp) {
            $sTitle = $aCardText['title_not_in_line_up'];
        } elseif ('1' == $sIsLineUp) {
            $sTitle = $aCardText['title_in_line_up'];
        }

        $sTopImage = $bAllowShowNewStyle ? $aCardText['multiple_recommend_top_image'] : $aCardText['top_image'];
        $sToastMsg = $aCancelCard['toast_msg'] ?? '';
        $sSubTitle = $aCancelCard['sub_title'] ?? $aCardText['subtitle'];

        $sCountTime     = $aCancelCard['count_time'] ?? '';
        $sCountType     = $aCancelCard['count_type'] ?? '';
        $sIsSpecialRate = $aCancelCard['is_special_rate'] ?? '';
        $sBgColor       = $aCardText['bg_color'];
        $sTopImageScale = $aCardText['top_image_scale'];
        $this->setTitle($sTitle);
        $this->setSubTitle($sSubTitle);
        $this->setTopImage($sTopImage);
        $this->setToastMsg($sToastMsg);
        $this->setCountTime($sCountTime);
        $this->setCountType($sCountType);
        $this->setIsSpecialRate($sIsSpecialRate);
        $this->setBgColor($sBgColor);
        $this->setTopImageScale($sTopImageScale);
    }

    /**
     * @param array              $aOrderInfo            aOrderInfo
     * @param array              $aBookingGoNowCardInfo aBookingGoNowCardInfo
     * @param bool               $isPreMatchOrder       isPreMatchOrder
     * @param MatchResultRuntime $oRuntimeData          oRuntimeData
     * @param bool               $isBookingMockDriverOrder 是否是预约单有车订单
     * @return mixed|string
     */
    public function getCardText($aOrderInfo, $aBookingGoNowCardInfo,$isPreMatchOrder, $oRuntimeData, $isBookingMockDriverOrder) {
        // dos数据获取
        $type = $aOrderInfo['type'];
        if ($isBookingMockDriverOrder) {
            $aDcmpText = Language::getTextFromDcmp('pre_cancel-booking_mock_card');
            $aDcmpText = json_decode($aDcmpText, true);
            if (!empty($aBookingGoNowCardInfo) && count($aBookingGoNowCardInfo) > 0) {
                $aCardText = $aDcmpText['has_go_now_card'];
            } else {
                //您已预约成功需要判断当前是否在倒计时阶段，倒计时阶段还未分配虚拟司机
                list($bOk, $iConf) = \Nuwa\ApolloSDK\Apollo::getInstance()
                    ->getConfigResult('booking_order_virtual', 'booking_order_virtual_count_down_seconds_config')
                    ->getConfig('count_down_seconds');
                if (!$bOk || !isset($iConf)) {
                    $iCountDownTime = 15; //兜底15秒
                } else {
                    $iCountDownTime = $iConf;
                }
                $iWaitTime = time() - strtotime($aOrderInfo['_birth_time']);
                if ($iWaitTime < $iCountDownTime) {
                    //还在倒计时页面
                    $aCardText = $aDcmpText['not_has_go_now_card'];
                }else {
                    $aCardText = $aDcmpText['not_has_go_now_card'];
                }
            }
        } elseif (OrderSystem::TYPE_ORDER_BOOKING == $type && \BizCommon\Logics\Order\BookingOrder::checkBooingOrderABSwitch($aOrderInfo)) {
            $sDcmpText = Language::getTextFromDcmp('pre_cancel-booking_guide_card');
            $aDcmpText = json_decode($sDcmpText, true);

            $iDepartureTimeSection = (int)strtotime($aOrderInfo['departure_time'] ?? 0) - time();

            $longTermThreshold  = $this->_getBookingOrderApolloConfig(self::BOOKING_PRE_CANCEL_TIME, 'long_term_threshold');
            $iLongTermThreshold = $longTermThreshold != [] ? $longTermThreshold : 3600;

            $goNowTime  = $this->_getBookingOrderApolloConfig(self::BOOKING_PRE_CANCEL_TIME, 'go_now_time');
            $iGoNowTime = $goNowTime != [] ? $goNowTime : 900;

            if ($iDepartureTimeSection >= $iLongTermThreshold) {
                $aCardText = $aDcmpText['long'] ?? '';
                $aCardText['title_not_in_line_up'] = str_replace('%s', floor($iDepartureTimeSection / 3600), $aCardText['title_not_in_line_up']);
                $aCardText['title_in_line_up']     = str_replace('%s', floor($iDepartureTimeSection / 3600), $aCardText['title_in_line_up']);
            } elseif (($iDepartureTimeSection > $iGoNowTime) && ($iDepartureTimeSection < $iLongTermThreshold)) {
                $aCardText = $aDcmpText['middle'] ?? '';
            } elseif (!empty($aBookingGoNowCardInfo) && count($aBookingGoNowCardInfo) > 0) {   // 要确认 预约单立即出发卡片有数据。
                $aCardText = $aDcmpText['short'] ?? '';
            } else {
                $aCardText = $aDcmpText['middle'] ?? '';
            }
        } elseif ($isPreMatchOrder) {
            $sCardText = Language::getTextFromDcmp('pre_cancel-pre_match_card');
            $aCardText = json_decode($sCardText, true);
        } else {
            $sCardText = Language::getTextFromDcmp('pre_cancel-guide_card');
            $aCardText = json_decode($sCardText, true);
            // 处理非排队命中长单场景的主标题文案
            self::_setMainTitleOfLongDistancePerception($aOrderInfo, $aCardText);

        }

        return $aCardText;
    }


    /**
     * @decs 获取新样式卡片内容
     * @param MatchResultRuntime $oRuntimeData          oRuntimeData
     * @param array              $aAthenaExpectInfo     Athena预期信息
     * @return array
     */
    private function _getNewStyleCardText($oRuntimeData,$aAthenaExpectInfo) {
        list($bHasOptimalCoupon,$fCouponAmount,$sCustomTag) = $this->_getOptimalCoupon($oRuntimeData);
        $iEts = $this->_getEts($aAthenaExpectInfo);
        $sLang = $oRuntimeData->getRequest()['lang'];
        $aPublicConf  = Config::text('pre_cancel', 'new_guide_card_public_conf',array(),$sLang);
        $aSpecialConf = Config::text('pre_cancel', 'new_guide_card_special_conf',array(),$sLang);
        if (empty($aPublicConf) || empty($aSpecialConf)) {
            return [];
        }
        $sTitle      = '';
        $aTitleCouponInfo = [];
        $iCommuType  = 0;
        $iCouponType = 0;
        $oCommonCompensationLogic = CommonCompensationLogic::getInstance();
        $oCommonCompensationLogic->init($oRuntimeData->getCompensationLogic(),$oRuntimeData->getNormalNoCarCompensationLogic());
        $oCouponInfo = $oCommonCompensationLogic->getCouponInfo();

        if (!empty($oCouponInfo) ) {
            $sImg = $aSpecialConf['img']['no_car_compensation'];
            $sCompensationCouponNum = '';
            $sCompensationCouponType = '';
            $iCompensationStatus = $oCouponInfo->getCompensationStatus();
            $iCouponType = $oCouponInfo->getCouponType();
            switch ($iCompensationStatus) {
                case Common::COMPENSATION_STATUS_ADD_SUCCESS:
                    if ($iCouponType == CommonCompensationLogic::COUPON_TYPE_AMOUNT) {
                        $sTitleConf = $aSpecialConf['title']['no_car_compensation']['wait_deducation'];
                        $sCompensationCouponNum = $this->formatNumFloor($oCouponInfo->getCouponAmount(),1);
                        $sCompensationCouponType = self::COUPON_TYPE_DEDUCATION;
                    }elseif ($iCouponType == CommonCompensationLogic::COUPON_TYPE_DISCOUNT) {
                        $sTitleConf = $aSpecialConf['title']['no_car_compensation']['wait_discount'];
                        $sCompensationCouponNum =$this->formatNumFloor($oCouponInfo->getCouponDiscount(),1);
                        $sCompensationCouponType = self::COUPON_TYPE_DISCOUNT;
                    }
                    $iCommuType = 3;
                    break;
                case Common::COMPENSATION_STATUS_SUCCESS:
                    if ($iCouponType == CommonCompensationLogic::COUPON_TYPE_AMOUNT) {
                        $sTitleConf = $aSpecialConf['title']['no_car_compensation']['got_deducation'];
                        $sCompensationCouponNum = $this->formatNumFloor($oCouponInfo->getCouponAmount(),1);
                        $sCompensationCouponType = self::COUPON_TYPE_DEDUCATION;
                    }elseif ($iCouponType == CommonCompensationLogic::COUPON_TYPE_DISCOUNT) {
                        $sTitleConf = $aSpecialConf['title']['no_car_compensation']['got_discount'];
                        $sCompensationCouponNum =$this->formatNumFloor($oCouponInfo->getCouponDiscount(),1);
                        $sCompensationCouponType = self::COUPON_TYPE_DISCOUNT;
                    }
                    $iCouponType = 1;
                    break;
            }

            $sTitle = Language::replaceTag(
                $sTitleConf,
                ['num' => $sCompensationCouponNum]
            );
            $aTitleCouponInfo = $this->_getTitleCouponInfo($aSpecialConf,$sCompensationCouponType,$sCompensationCouponNum);

        } elseif($bHasOptimalCoupon) {
            $sImg = $aSpecialConf['img']['normal_coupon'];
            if (Common::isCouponCustomTag($sCustomTag)) {
                $sTitleConf = $aSpecialConf['title']['use_coupon_compensation'];
                $iCouponType = 1;
            } else {
                $sTitleConf = $aSpecialConf['title']['use_coupon_normal'];
                $iCouponType = 2;
            }

            $sTitle =  Language::replaceTag(
                $sTitleConf,
                ['num' => $this->formatNumFloor($fCouponAmount,1)]
            );

            $aTitleCouponInfo = $this->_getTitleCouponInfo($aSpecialConf,self::COUPON_TYPE_DEDUCATION,$this->formatNumFloor($fCouponAmount,1));

        } else {

            if ($iEts <= 5) {
                $sTitle     = $aSpecialConf['title']['default']['quick'];
                $sImg       = $aSpecialConf['img']['default']['quick'];
                $iCommuType = 1;
            } else {
                $sTitle     = $aSpecialConf['title']['default']['slow'];
                $sImg       = $aSpecialConf['img']['default']['slow'];
                $iCommuType = 2;
            }
        }

        $aCardText                = $aPublicConf;
        $aCardText['title']       = $sTitle;
        $aCardText['top_image']   = $sImg;
        $aCardText['coupon_msg']  = $aTitleCouponInfo['coupon_msg'] ?? '';
        $aCardText['coupon_desc'] = $aTitleCouponInfo['coupon_desc'] ?? '';
        $aCardText['commu_type']  = $iCommuType;
        $aCardText['coupon_type'] = $iCouponType;

        return $aCardText;
    }


    /**
     * @decs 获取ETS
     * @param array  $aSpecialConf 特殊配置
     * @param string $sCouponType  券类型
     * @param string $sCouponNum 券金额/折扣
     * @return array
     */
    private function _getTitleCouponInfo($aSpecialConf,$sCouponType,$sCouponNum) {

        $sCouponMsg  = '';
        $sCouponDesc = '';

        if (self::COUPON_TYPE_DEDUCATION == $sCouponType) {
            $sCouponMsg = Language::replaceTag(
                $aSpecialConf['coupon_type_deducation'],
                ['num' => $sCouponNum]
            );
            $sCouponDesc = $aSpecialConf['coupon_desc_deducation'];
        }

        if (self::COUPON_TYPE_DISCOUNT == $sCouponType) {
            $sCouponMsg = Language::replaceTag(
                $aSpecialConf['coupon_type_discount'],
                ['num' => $sCouponNum]
            );
            $sCouponDesc = $aSpecialConf['coupon_desc_discount'];
        }

        return [
            "coupon_msg"  => $sCouponMsg,
            "coupon_desc" => $sCouponDesc
        ];

    }


    /**
     * @decs 获取ETS
     * @param array $aAthenaExpectInfo Athena预期信息
     * @return int
     */
    private function _getEts($aAthenaExpectInfo) {

        if (empty($aAthenaExpectInfo)) {
            return 0;
        }

        if (empty($aAthenaExpectInfo['order_match_global_scene_expect']) || empty($aAthenaExpectInfo['order_match_global_scene_expect']['time_start'])) {
            return 0;
        }

        $iAnswerTime = $aAthenaExpectInfo['order_match_global_scene_expect']['time_start'];
        $iNowTime    = time();
        if ($iNowTime >= $iAnswerTime) {
            return 0;
        }

        return $iAnswerTime-$iNowTime;
    }


    /**
     * @decs 获取最优券信息
     * @param MatchResultRuntime $oRuntimeData oRuntimeData
     * @return array
     */
    private function _getOptimalCoupon($oRuntimeData)
    {

        $aQuotation = $oRuntimeData->aQuotation;

        if(empty($aQuotation)) {
            return [];
        }

        // 最优券面额
        $fOptimalCouponAmount = 0.0;
        $sCustomTag = '';

        foreach ($aQuotation as $aOneQuotation) {
            if (empty($aOneQuotation) || empty($aOneQuotation['discount_desc'])) {
                continue;
            }

            $aDiscountDesc = json_decode($aOneQuotation['discount_desc'], true) ?? [];

            if (empty($aDiscountDesc) || sizeof($aDiscountDesc) < 1) {
                continue;
            }

            foreach ($aDiscountDesc as $aItem) {
                if (empty($aItem) || empty($aItem['amount'])) {
                    continue;
                }

                if ($aItem['amount'] > $fOptimalCouponAmount) {
                    $fOptimalCouponAmount    = $aItem['amount'];
                    $sCustomTag              = $aItem['custom_tag'];
                }
            }

        }

        if (empty($fOptimalCouponAmount)) {
            return [false,0.0,""];
        }

        return[true,$fOptimalCouponAmount,$sCustomTag];
    }

    /**
     * @param array $aOrderInfo 订单信息
     * @param array $aCardText  预取消挽留卡片数据
     * @return void
     */
    private function _setMainTitleOfLongDistancePerception($aOrderInfo, &$aCardText) {
        // 未命中长单感知优化场景或者排队订单则无需处理
        if (!HoraeUtil::isLongDistanceOrderPerceptionOptimize($aOrderInfo) || '0' != $aOrderInfo['line_up']) {
            return;
        }
        $aRet = UfsRepository::getUfsFeature(
            [self::UFS_KEY_OF_LONG_DISTANCE_PERCEPTION_OPTIMIZE],
            ['order_id' => $aOrderInfo['order_id']],
            'passenger'
        );
        if (empty($aRet) || self::IS_HIT_LONG_DISTANCE_PERCEPTION_STRATEGY != $aRet[self::UFS_KEY_OF_LONG_DISTANCE_PERCEPTION_OPTIMIZE]) {
            return;
        }
        $title = \BizLib\Utils\Language::getDecodedTextFromDcmp('config_order-long_order_communicate_perception_text')[self::PREPARE_CANCEL_ORDER_SCENE]['text'];
        $aCardText['title_not_in_line_up'] = $title;
    }

    /**
     * @param $sFileName
     * @param $sKeyName
     * @return array|mixed
     */
    private function _getBookingOrderApolloConfig($sFileName, $sKeyName) {
        $aConfig = ApolloHelper::getConfigContent('booking_order_conf', $sFileName);
        if (isset($aConfig[$sKeyName])) {
            return $aConfig[$sKeyName];
        }

        return [];
    }


    /**
     * 向下取整，保留x位小数
     * @param float $fNum   数字
     * @param int   $iDigit 保留位数
     * @return string
     */
    public function formatNumFloor($fNum, $iDigit) {

        $sTransNum = strval($fNum);

        //不进行位数处理
        if ($iDigit < 0) {
            return $sTransNum;
        }

        //计算当前有几位
        $iPosition = strrpos($sTransNum,'.');
        $iSubFee = substr($sTransNum,$iPosition+1);
        $iFloatLen = strlen($iSubFee);

        //位数小于要精确的位数不做处理，防止精度丢失
        if ($iFloatLen <= $iDigit) {
            return $sTransNum;
        }

        $iTimes = pow(10,$iDigit);
        return number_format(floor($sTransNum*$iTimes)/$iTimes,$iDigit,'.','');
    }
}
