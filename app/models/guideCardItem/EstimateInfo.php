<?php

namespace PreSale\Models\guideCardItem;

/**
 * 追加车型预估数据
 */
class EstimateInfo
{
    public $bFresh;

    public $sEstimateId;

    public $sEstimateTraceId;

    public $aProductList;

    public $sFeeDetailUrl;

    /**
     * 等待应答迭代三期 表单组件样式
     * @var array
     */
    public $aLayout;

    /**
     * 等待应答迭代三期 表单组件车型数据
     * @var array
     */
    public $aEstimateData;

    /**
     * @return array
     */
    public function render() {
        $aRet = [
            'refresh'           => $this->bFresh,
            'estimate_id'       => $this->sEstimateId,
            'estimate_trace_id' => $this->sEstimateTraceId,
            'fee_detail_url'    => $this->sFeeDetailUrl,
        ];
        if (!empty($this->aLayout)) {
            $aRet['layout'] = $this->aLayout;
        }

        if (!empty($this->aEstimateData)) {
            $aRet['estimate_data'] = $this->aEstimateData;
        }

        if (!empty($this->aProductList)) {
            $aRet['product_list'] = $this->aProductList;
        }

        return $aRet;
    }
}
