<?php

namespace PreSale\Models\guideCardItem\button;

use PreSale\Models\guideCardItem\button\actionData\BasicActionData;
use PreSale\Models\guideCardItem\OmniOmegaInfo;

/**
 * OmniButton
 */
class OmniButton implements IBaseButton, \JsonSerializable
{
    // 这部分是分请求的接口
    const ACTION_TYPE_ANYCAR_NEW_ORDER   = 1; // 请求pAnycarNewOrder
    const ACTION_TYPE_UPDATE_ORDER       = 2; // 请求pUpdateOrder
    const ACTION_TYPE_CLOSE_POPUP        = 3; // 关闭当前窗口
    const ACTION_TYPE_REFRESH_SELF       = 4; // 重新请求pOrderMatch
    const ACTION_TYPE_NO_ACTION          = 5; // 什么都不做
    const ACTION_TYPE_RENDER_SELF        = 6; // 将params中的数据render到弹窗上
    const ACTION_TYPE_GET_PRE_MATCH_INFO = 7; // 请求pGetPreMatchInfo
    const ACTION_TYPE_CANCEL_ORDER       = 8; // 请求cancelOrder接口
    const ACTION_TYPE_LINK_H5            = 9; // 跳转h5页面

    protected $_sText        = ''; // 按钮展示文案
    protected $_iActionType  = 0; //按钮动作类型
    protected $_iIsHighlight = 0; // 是否高亮

    /** 按钮参数 可能是页面数据 可能是url参数
     * @var array
     */
    protected $_aButtonParams = [];

    /**
     * @var OmniOmegaInfo
     */
    protected $_oOmegaInfo = [];

    /**
     * @var BasicActionData
     */
    protected $_oActionData;

    /**
     * @var ButtonStyle
     */
    protected $_oButtonStyle;

    /**
     * @return string
     */
    public function getText() {
        return $this->_sText;
    }

    /**
     * @param string $sText Text
     * @return OmniButton
     */
    public function setText($sText) {
        $this->_sText = $sText;
        return $this;
    }

    /**
     * @return int
     */
    public function getActionType() {
        return $this->_iActionType;
    }

    /**
     * @param int $iActionType ActionType
     * @return OmniButton
     */
    public function setActionType($iActionType) {
        $this->_iActionType = $iActionType;
        return $this;
    }

    /**
     * @return array
     */
    public function getAButtonParams() {
        return $this->_aButtonParams;
    }

    /**
     * @param array|object $aButtonParams buttonParams
     * @return OmniButton
     */
    public function setAButtonParams($aButtonParams) {
        $this->_aButtonParams = $aButtonParams;
        return $this;
    }

    /**
     * @return OmniOmegaInfo
     */
    public function getOmegaInfo(): OmniOmegaInfo {
        return $this->_oOmegaInfo;
    }

    /**
     * @param OmniOmegaInfo $oOmniOmegaInfo triggerOmega
     * @return OmniButton
     */
    public function setOmegaInfo($oOmniOmegaInfo) {
        $this->_oOmegaInfo = $oOmniOmegaInfo;
        return $this;
    }

    /**
     * @param int $iIsHighlight highLight
     * @return IBaseButton
     */
    public function setIsHighlight($iIsHighlight) {
        $this->_iIsHighlight = $iIsHighlight;
        return $this;
    }

    /**
     * @return BasicActionData
     */
    public function getActionData() {
        return $this->_oActionData;
    }

    /**
     * @param BasicActionData $oActionData action_data
     * @return IBaseButton
     */
    public function setActionData($oActionData) {
        $this->_oActionData = $oActionData;
        return $this;
    }

    /**
     * @param ButtonStyle $oButtonStyle button_style
     * @return IBaseButton
     */
    public function setButtonStyle($oButtonStyle) {
        $this->_oButtonStyle = $oButtonStyle;
        return $this;
    }

    /**
     * @return ButtonStyle
     */
    public function getButtonStyle() {
        return $this->_oButtonStyle;
    }

    /**
     * @return array|mixed
     */
    public function jsonSerialize() {
        $aRet = [
            'action_type' => $this->_iActionType,
        ];
        if (!empty($this->_sText)) {
            $aRet['text'] = $this->_sText;
        }

        if (!empty($this->_oOmegaInfo)) {
            $aRet['action_omega'] = $this->_oOmegaInfo;
        }

        if (!empty($this->_aButtonParams)) {
            $aRet['action_params'] = $this->_aButtonParams;
        }

        if (!empty($this->_iIsHighlight)) {
            $aRet['is_highlight'] = $this->_iIsHighlight;
        }

        if (!empty($this->_oActionData)) {
            $aRet['action_data'] = $this->_oActionData;
        }

        if (!empty($this->_oButtonStyle)) {
            $aRet['style'] = $this->_oButtonStyle;
        }

        return $aRet;
    }
}
