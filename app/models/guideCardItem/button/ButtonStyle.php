<?php

namespace PreSale\Models\guideCardItem\button;

/**
 * 按钮样式
 */
class ButtonStyle implements \JsonSerializable
{
    /**
     * @var string
     */
    private $_sFontColor;

    /**
     * @var array
     */
    private $_aBgGradientColor;

    /**
     * @var string
     */
    private $_sBorderColor;

    /**
     * @var string
     */
    private $_sCornerRadius;

    /**
     * @var string
     */
    private $_sFontWeight;

    /**
     * @return string
     */
    public function getFontColor() {
        return $this->_sFontColor;
    }

    /**
     * @param string $sFontColor 字体颜色
     * @return void
     */
    public function setFontColor($sFontColor) {
        $this->_sFontColor = $sFontColor;
    }

    /**
     * @return array
     */
    public function getBgGradientColor() {
        return $this->_aBgGradientColor;
    }

    /**
     * @param array $aBgGradientColor 垂直渐变色
     * @return void
     */
    public function setBgGradientColor($aBgGradientColor) {
        $this->_aBgGradientColor = $aBgGradientColor;
    }

    /**
     * @return string
     */
    public function getBorderColor() {
        return $this->_sBorderColor;
    }

    /**
     * @param string $sBorderColor 边框颜色
     * @return void
     */
    public function setBorderColor($sBorderColor) {
        $this->_sBorderColor = $sBorderColor;
    }

    /**
     * @return string
     */
    public function getCornerRadius() {
        return $this->_sCornerRadius;
    }

    /**
     * @param string $sCornerRadius button圆角大小
     * @return void
     */
    public function setCornerRadius($sCornerRadius) {
        $this->_sCornerRadius = $sCornerRadius;
    }

    /**
     * @return string
     */
    public function getFontWeight() {
        return $this->_sFontWeight;
    }

    /**
     * @param string $sFontWeight 字体宽度
     * @return void
     */
    public function setFontWeight($sFontWeight) {
        $this->_sFontWeight = $sFontWeight;
    }

    /**
     * @return array
     */
    public function jsonSerialize() {
        $aRet = [];
        if (!empty($this->_aBgGradientColor)) {
            $aRet['bg_gradient_color'] = $this->_aBgGradientColor;
        }

        if (!empty($this->_sBorderColor)) {
            $aRet['border_color'] = $this->_sBorderColor;
        }

        if (!empty($this->_sFontColor)) {
            $aRet['font_color'] = $this->_sFontColor;
        }

        if (!empty($this->_sCornerRadius)) {
            $aRet['corner_radius'] = $this->_sCornerRadius;
        }
        if (!empty($this->_sFontWeight)) {
            $aRet['font_weight'] = $this->_sFontWeight;
        }

        return $aRet;
    }
}
