<?php

namespace PreSale\Models\guideCardItem\button\actionData;

/**
 * 按钮中的弹窗，url等数据
 */
class ActionData extends BasicActionData
{
    private $_sUrl;

    /**
     * @return mixed
     */
    public function getSUrl() {
        return $this->_sUrl;
    }

    /**
     * @param string $sUrl url
     * @return void
     */
    public function setSUrl($sUrl) {
        $this->_sUrl = $sUrl;
    }

    /**
     * @return array
     */
    public function jsonSerialize() {
        $aRet = [];
        if (!empty($this->_sUrl)) {
            $aRet['url'] = $this->_sUrl;
        }

        return $aRet;
    }
}
