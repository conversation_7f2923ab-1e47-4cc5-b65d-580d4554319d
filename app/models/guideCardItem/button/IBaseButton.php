<?php

namespace PreSale\Models\guideCardItem\button;

use PreSale\Models\guideCardItem\OmniOmegaInfo;

/**
 * 按钮接口
 */
interface IBaseButton
{
    /**
     * @return string
     */
    public function getText();

    /**
     * @param string $sText Text
     * @return IBaseButton
     */
    public function setText($sText);

    /**
     * @return int
     */
    public function getActionType();

    /**
     * @param int $iActionType ActionType
     * @return IBaseButton
     */
    public function setActionType($iActionType);


    /**
     * @return array
     */
    public function getAButtonParams();

    /**
     * @param array|object $aButtonParams buttonParams
     * @return IBaseButton
     */
    public function setAButtonParams($aButtonParams);

    /**
     * @return OmniOmegaInfo
     */
    public function getOmegaInfo();

    /**
     * @param OmniOmegaInfo $oOmegaInfo omniOmegaInfo
     * @return mixed
     */
    public function setOmegaInfo($oOmegaInfo);

    /**
     * @param ButtonStyle $oButtonStyle button_style
     * @return mixed
     */
    public function setButtonStyle($oButtonStyle);

    /**
     * @return ButtonStyle
     */
    public function getButtonStyle();
}
