<?php

namespace PreSale\Models\guideCardItem\button;

use BizLib\Utils\Language;

/**
 * 获取取消挽留弹窗出口卡片包框和按钮样式
 */
class GuideCardStyleManage
{
    const GUIDE_CARD_STYLE_KEY = 'pre_cancel-guide_card_style';

    /**
     * 获取取消挽留弹窗中卡片按钮的样式
     * @param bool $iIsWaitAnswerUpgrade 是否走新等待应答框架样式
     * @return ButtonStyle
     */
    public static function getDefaultButtonStyle($iIsWaitAnswerUpgrade = false) {
        $sButtonStyleText = Language::getTextFromDcmp(self::GUIDE_CARD_STYLE_KEY);
        $oButtonStyle     = new ButtonStyle();
        if (!empty($sButtonStyleText)) {
            $aButtonStyleConfig = json_decode($sButtonStyleText, true);
            $oButtonStyle->setFontColor($aButtonStyleConfig['button_style']['font_color']);
            $oButtonStyle->setBorderColor($aButtonStyleConfig['button_style']['border_color']);
            $oButtonStyle->setBgGradientColor($aButtonStyleConfig['button_style']['bg_gradient_color']);
            if ($iIsWaitAnswerUpgrade) {
                $oButtonStyle->setBorderColor($aButtonStyleConfig['button_style']['border_color_v2']);
                $oButtonStyle->setBgGradientColor($aButtonStyleConfig['button_style']['bg_gradient_color_v2']);
            }
        }

        return $oButtonStyle;
    }

    /**
     * 获取多推荐弹窗一键追加按钮样式
     * @return ButtonStyle 按钮样式
     */
    public static function getMultipleRecommendButtonStyle() {
        $sButtonStyleText = Language::getTextFromDcmp(self::GUIDE_CARD_STYLE_KEY);
        $oButtonStyle     = new ButtonStyle();
        if (!empty($sButtonStyleText)) {
            $aButtonStyleConfig = json_decode($sButtonStyleText, true);
            $oButtonStyle->setFontColor($aButtonStyleConfig['multiple_recommend_button_style']['font_color']);
            $oButtonStyle->setCornerRadius($aButtonStyleConfig['multiple_recommend_button_style']['corner_radius']);
            $oButtonStyle->setBgGradientColor($aButtonStyleConfig['multiple_recommend_button_style']['bg_gradient_color']);
        }

        return $oButtonStyle;
    }

    /**
     * 获取取消挽留弹窗中暂不取消按钮的样式
     * @return ButtonStyle
     */
    public static function getNoCancelButtonStyle($iIsWaitAnswerUpgrade = false) {
        $sButtonStyleText = Language::getTextFromDcmp(self::GUIDE_CARD_STYLE_KEY);
        $oButtonStyle     = new ButtonStyle();
        if (!empty($sButtonStyleText)) {
            $aButtonStyleConfig = json_decode($sButtonStyleText, true);
            $oButtonStyle->setFontColor($aButtonStyleConfig['no_cancel_button_style']['font_color']);
            $oButtonStyle->setBorderColor($aButtonStyleConfig['no_cancel_button_style']['border_color']);
            $oButtonStyle->setBgGradientColor($aButtonStyleConfig['no_cancel_button_style']['bg_gradient_color']);
            if ($iIsWaitAnswerUpgrade) {
                $oButtonStyle->setFontColor($aButtonStyleConfig['no_cancel_button_style']['font_color_v2']);
                $oButtonStyle->setBorderColor($aButtonStyleConfig['no_cancel_button_style']['border_color_v2']);
                $oButtonStyle->setBgGradientColor($aButtonStyleConfig['no_cancel_button_style']['bg_gradient_color_v2']);
            }
        }

        return $oButtonStyle;
    }

    /**
     * 获取取消挽留弹窗中确认取消按钮的样式
     * @return ButtonStyle
     */
    public static function getConfirmCancelButtonStyle($iIsWaitAnswerUpgrade = false) {
        $sButtonStyleText = Language::getTextFromDcmp(self::GUIDE_CARD_STYLE_KEY);
        $oButtonStyle     = new ButtonStyle();
        if (!empty($sButtonStyleText)) {
            $aButtonStyleConfig = json_decode($sButtonStyleText, true);
            $oButtonStyle->setFontColor($aButtonStyleConfig['cancel_button_style']['font_color']);
            $oButtonStyle->setBorderColor($aButtonStyleConfig['cancel_button_style']['border_color']);
            $oButtonStyle->setBgGradientColor($aButtonStyleConfig['cancel_button_style']['bg_gradient_color']);
            if ($iIsWaitAnswerUpgrade) {
                $oButtonStyle->setFontColor($aButtonStyleConfig['cancel_button_style']['font_color_v2']);
                $oButtonStyle->setBorderColor($aButtonStyleConfig['cancel_button_style']['border_color_v2']);
                $oButtonStyle->setBgGradientColor($aButtonStyleConfig['cancel_button_style']['bg_gradient_color_v2']);
            }
        }

        return $oButtonStyle;
    }

    /**
     * 获取取消挽留多推荐弹窗中确认取消按钮的样式
     * @return ButtonStyle
     */
    public static function getMultipleRecommendConfirmCancelButtonStyle() {
        $sButtonStyleText = Language::getTextFromDcmp(self::GUIDE_CARD_STYLE_KEY);
        $oButtonStyle     = new ButtonStyle();
        if (!empty($sButtonStyleText)) {
            $aButtonStyleConfig = json_decode($sButtonStyleText, true);
            $oButtonStyle->setFontColor($aButtonStyleConfig['multiple_recommend_cancel_button_style']['font_color']);
            $oButtonStyle->setBorderColor($aButtonStyleConfig['multiple_recommend_cancel_button_style']['border_color']);
            $oButtonStyle->setCornerRadius($aButtonStyleConfig['multiple_recommend_cancel_button_style']['corner_radius']);
            $oButtonStyle->setBgGradientColor($aButtonStyleConfig['multiple_recommend_cancel_button_style']['bg_gradient_color']);
        }

        return $oButtonStyle;
    }

    /**
     * 获取取消挽留追加场景狠心取消按钮的样式
     * @return ButtonStyle
     */
    public static function getMultipleRecommendCancelButtonStyle() {
        $sButtonStyleText = Language::getTextFromDcmp(self::GUIDE_CARD_STYLE_KEY);
        $oButtonStyle     = new ButtonStyle();
        if (!empty($sButtonStyleText)) {
            $aButtonStyleConfig = json_decode($sButtonStyleText, true);
            $oButtonStyle->setFontColor($aButtonStyleConfig['multiple_recommend_cancel_button_style_v2']['font_color']);
            $oButtonStyle->setBorderColor($aButtonStyleConfig['multiple_recommend_cancel_button_style_v2']['border_color']);
            $oButtonStyle->setCornerRadius($aButtonStyleConfig['multiple_recommend_cancel_button_style_v2']['corner_radius']);
            $oButtonStyle->setBgGradientColor($aButtonStyleConfig['multiple_recommend_cancel_button_style_v2']['bg_gradient_color']);
        }

        return $oButtonStyle;
    }

    /**
     * 获取取消挽留多推荐弹窗中速速追加按钮的样式
     * @return ButtonStyle
     */
    public static function getMultipleRecommendConfirmButtonStyle() {
        $sButtonStyleText = Language::getTextFromDcmp(self::GUIDE_CARD_STYLE_KEY);
        $oButtonStyle     = new ButtonStyle();
        if (!empty($sButtonStyleText)) {
            $aButtonStyleConfig = json_decode($sButtonStyleText, true);
            $oButtonStyle->setFontColor($aButtonStyleConfig['multiple_recommend_confirm_button_style_v2']['font_color']);
            $oButtonStyle->setCornerRadius($aButtonStyleConfig['multiple_recommend_confirm_button_style_v2']['corner_radius']);
            $oButtonStyle->setBgGradientColor($aButtonStyleConfig['multiple_recommend_confirm_button_style_v2']['bg_gradient_color']);
        }

        return $oButtonStyle;
    }

    /**
     * 小巴按钮颜色
     * @return ButtonStyle
     */
    public static function getConfirmCancelButtonStyleForMiniBus() {
        $sButtonStyleText = Language::getTextFromDcmp(self::GUIDE_CARD_STYLE_KEY);
        $oButtonStyle     = new ButtonStyle();
        if (!empty($sButtonStyleText)) {
            $aButtonStyleConfig = json_decode($sButtonStyleText, true);
            $oButtonStyle->setFontColor($aButtonStyleConfig['cancel_button_style_for_minibus']['font_color']);
            $oButtonStyle->setBorderColor($aButtonStyleConfig['cancel_button_style_for_minibus']['border_color']);
            $oButtonStyle->setCornerRadius($aButtonStyleConfig['cancel_button_style_for_minibus']['corner_radius']);
            $oButtonStyle->setBgGradientColor($aButtonStyleConfig['cancel_button_style_for_minibus']['bg_gradient_color']);
        }

        return $oButtonStyle;
    }

    /**
     * 智能小巴按钮颜色
     * @return ButtonStyle
     */
    public static function getConfirmCancelButtonStyleForSmartCarpool() {
        $sButtonStyleText = Language::getTextFromDcmp(self::GUIDE_CARD_STYLE_KEY);
        $oButtonStyle     = new ButtonStyle();
        if (!empty($sButtonStyleText)) {
            $aButtonStyleConfig = json_decode($sButtonStyleText, true);
            $oButtonStyle->setFontColor($aButtonStyleConfig['cancel_button_style_for_smart_carpool']['font_color']);
            $oButtonStyle->setBorderColor($aButtonStyleConfig['cancel_button_style_for_smart_carpool']['border_color']);
            $oButtonStyle->setCornerRadius($aButtonStyleConfig['cancel_button_style_for_smart_carpool']['corner_radius']);
            $oButtonStyle->setBgGradientColor($aButtonStyleConfig['cancel_button_style_for_smart_carpool']['bg_gradient_color']);
        }

        return $oButtonStyle;
    }

    /**
     * 获取取消挽留多推荐弹窗中暂不取消按钮的样式
     * @return ButtonStyle
     */
    public static function getMultipleRecommendNoCancelButtonStyle() {
        $sButtonStyleText = Language::getTextFromDcmp(self::GUIDE_CARD_STYLE_KEY);
        $oButtonStyle     = new ButtonStyle();
        if (!empty($sButtonStyleText)) {
            $aButtonStyleConfig = json_decode($sButtonStyleText, true);
            $oButtonStyle->setFontColor($aButtonStyleConfig['multiple_recommend_no_cancel_button_style']['font_color']);
            $oButtonStyle->setCornerRadius($aButtonStyleConfig['multiple_recommend_no_cancel_button_style']['corner_radius']);
            $oButtonStyle->setBgGradientColor($aButtonStyleConfig['multiple_recommend_no_cancel_button_style']['bg_gradient_color']);
            $oButtonStyle->setFontWeight($aButtonStyleConfig['multiple_recommend_no_cancel_button_style']['font_weight']);
        }

        return $oButtonStyle;
    }

    /**
     * 小巴按钮颜色
     * @return ButtonStyle
     */
    public static function getNoCancelButtonStyleForminiBus() {
        $sButtonStyleText = Language::getTextFromDcmp(self::GUIDE_CARD_STYLE_KEY);
        $oButtonStyle     = new ButtonStyle();
        if (!empty($sButtonStyleText)) {
            $aButtonStyleConfig = json_decode($sButtonStyleText, true);
            $oButtonStyle->setFontColor($aButtonStyleConfig['no_cancel_button_style_for_minibus']['font_color']);
            $oButtonStyle->setCornerRadius($aButtonStyleConfig['no_cancel_button_style_for_minibus']['corner_radius']);
            $oButtonStyle->setBgGradientColor($aButtonStyleConfig['no_cancel_button_style_for_minibus']['bg_gradient_color']);
            $oButtonStyle->setFontWeight($aButtonStyleConfig['no_cancel_button_style_for_minibus']['font_weight']);
        }

        return $oButtonStyle;
    }

    /**
     * 智能小巴按钮颜色
     * @return ButtonStyle
     */
    public static function getNoCancelButtonStyleForSmartCarpool() {
        $sButtonStyleText = Language::getTextFromDcmp(self::GUIDE_CARD_STYLE_KEY);
        $oButtonStyle     = new ButtonStyle();
        if (!empty($sButtonStyleText)) {
            $aButtonStyleConfig = json_decode($sButtonStyleText, true);
            $oButtonStyle->setFontColor($aButtonStyleConfig['no_cancel_button_style_for_smart_carpool']['font_color']);
            $oButtonStyle->setCornerRadius($aButtonStyleConfig['no_cancel_button_style_for_smart_carpool']['corner_radius']);
            $oButtonStyle->setBgGradientColor($aButtonStyleConfig['no_cancel_button_style_for_smart_carpool']['bg_gradient_color']);
            $oButtonStyle->setFontWeight($aButtonStyleConfig['no_cancel_button_style_for_smart_carpool']['font_weight']);
        }

        return $oButtonStyle;
    }

    /**
     * 获取包框的垂直渐变色
     * @return array|mixed
     */
    public static function getContainerBgGradientColor() {
        $sStyleText       = Language::getTextFromDcmp(self::GUIDE_CARD_STYLE_KEY);
        $aBgGradientColor = [];
        if (!empty($sStyleText)) {
            $aStyleConfig     = json_decode($sStyleText, true);
            $aStyle           = $aStyleConfig['container_style'];
            $aBgGradientColor = $aStyle['bg_gradient_color'];
        }

        return $aBgGradientColor;
    }

    /**
     * 获取包框的垂直渐变色
     * @return array|mixed
     */
    public static function getFartherContainerBgGradientColor() {
        $sStyleText       = Language::getTextFromDcmp(self::GUIDE_CARD_STYLE_KEY);
        $aBgGradientColor = [];
        if (!empty($sStyleText)) {
            $aStyleConfig     = json_decode($sStyleText, true);
            $aStyle           = $aStyleConfig['farther_container_style'];
            $aBgGradientColor = $aStyle['bg_gradient_color'];
        }

        return $aBgGradientColor;
    }

    /**
     * 获取多推荐弹窗卡片包框的垂直渐变色
     * @return array|mixed
     */
    public static function getMultiRecommendContainerBgGradientColor() {
        $sStyleText       = Language::getTextFromDcmp(self::GUIDE_CARD_STYLE_KEY);
        $aBgGradientColor = [];
        if (!empty($sStyleText)) {
            $aStyleConfig     = json_decode($sStyleText, true);
            $aStyle           = $aStyleConfig['multiple_recommend_container_style'];
            $aBgGradientColor = $aStyle['bg_gradient_color'];
        }

        return $aBgGradientColor;
    }

    /**
     * 获取多推荐弹窗卡片包框V2的垂直渐变色
     * @return array|mixed
     */
    public static function getMultiRecommendContainerBgGradientColorV2() {
        $sStyleText       = Language::getTextFromDcmp(self::GUIDE_CARD_STYLE_KEY);
        $aBgGradientColor = [];
        if (!empty($sStyleText)) {
            $aStyleConfig     = json_decode($sStyleText, true);
            $aStyle           = $aStyleConfig['multiple_recommend_container_style_v2'];
            $aBgGradientColor = $aStyle['bg_gradient_color'];
        }

        return $aBgGradientColor;
    }
}
