<?php

namespace PreSale\Models\guideCardView\guideView;

use BizCommon\Logics\Anycar\AnyCarCommonLogic;
use BizLib\Constants\OrderSystem;
use BizLib\Utils\Language;
use BizLib\Utils\MemberVersion;
use BizLib\Utils\Passport;
use BizLib\Utils\UtilHelper;
use PreSale\Models\guideCardItem\biz\GuideViewBiz;
use PreSale\Models\guideCardItem\button\GuideCardStyleManage;
use PreSale\Models\guideCardItem\button\OmniButton;
use PreSale\Models\guideCardItem\GuideFactory;
use PreSale\Models\guideCardItem\MatchResultRuntime;
use PreSale\Models\guideCardItem\MemberViewModel;
use PreSale\Models\guideCardItem\OmniOmegaInfo;
use PreSale\Models\guideCardView\guideCard\BaseGuideCard;
use PreSale\Models\guideCardView\guideCard\ElementMemberCard;
use PreSale\Models\Member\LuxuryMemberLogic;
use PreSale\Models\Member\Member;

/**
 * GuideFastWayPremiumView 专车 快速应答
 */
class GuideFastWayPremiumView extends BaseViewRender
{
    const ZHUANCHE_BUSINESS_NAME = '专车';

    private $_aRecommendResult;

    private $_bLuxuryMember;

    private $_aMemberPrivFastWay;

    private $_bAggregate = false;

    /**
     * @param MatchResultRuntime $oRuntimeData     运行时数据总线
     * @param array              $aRecommendResult 导流推荐数据
     * @param bool               $bAggregate       bAggregate
     */
    public function __construct($oRuntimeData, $aRecommendResult, $bAggregate = false) {
        parent::__construct($oRuntimeData);
        $this->_aRecommendResult = $aRecommendResult;
        $this->_bAggregate       = $bAggregate;
    }

    /**
     * @return int
     */
    public function getRecommendHitType() {
        $aOrderInfo = $this->_oRuntimeData->getOrderInfo();

        //如果是新增的多勾发单出推荐快车快速通道
        if (!$this->_oRuntimeData->bIsSingleCar) {
            // 当前只对专车可以
            if (!in_array($this->_aRecommendResult['target_product']['product_id'], [\BizLib\Utils\Product::PRODUCT_ID_DEFAULT])) {
                // 会员2.0将对企业级专车开放
                if (!(MemberVersion::isNewMember() && in_array($this->_aRecommendResult['target_product']['product_id'], [\BizLib\Utils\Product::PRODUCT_ID_BUSINESS]))) {
                    return self::HT_NOHIT;
                }
            }

            $oMemberModel = $this->_oRuntimeData->oMultiMember;
        } else {
            // 当前只对专车可以
            if (!in_array($aOrderInfo['product_id'], [\BizLib\Utils\Product::PRODUCT_ID_DEFAULT])) {
                // 会员2.0将对企业级专车开放
                if (!(MemberVersion::isNewMember() && in_array($aOrderInfo['product_id'], [\BizLib\Utils\Product::PRODUCT_ID_BUSINESS]))) {
                    return self::HT_NOHIT;
                }
            }

            $oMemberModel = $this->_oRuntimeData->oMember;
        }

        // 实时单才可以
        if (OrderSystem::TYPE_ORDER_NOW != $aOrderInfo['type']) {
            return self::HT_NOHIT;
        }

        if (empty($oMemberModel)) {
            return self::HT_NOHIT;
        }

        //如果是新增的多勾发单出推荐快车快速通道
        if (!$this->_oRuntimeData->bIsSingleCar) {
            $sGroupKey        = AnyCarCommonLogic::getGroupKey($this->_aRecommendResult['target_product']); //athena需传
            $aMultiMemberInfo = $oMemberModel->getMultiMemberInfo();
            $aMemberPrivilegeNewOrder = $aMultiMemberInfo[$sGroupKey] ?? [];
            $iPtVersion = $aMemberPrivilegeNewOrder['member_info']['pt_version'] ?? 3;
        } else {
            $aMemberPrivilegeNewOrder = $oMemberModel->getOrderMemberPrivilege();
            $iPtVersion = $oMemberModel->getPrivilegeByType('member_info')['pt_version'] ?? 3;
        }

        $aMemberInfo = $aMemberPrivilegeNewOrder['member_info'];
        MemberVersion::setVersion($iPtVersion);

        // 无会员信息返回
        if ((empty($aMemberInfo['member_level_id']) && empty($aMemberInfo['luxury_member_level_id'])) || empty($aMemberPrivilegeNewOrder)) {
            return self::HT_NOHIT;
        }

        $aMemberPrivFastWay = Member::getMemberPrivFastWay($aMemberPrivilegeNewOrder);
        if (empty($aMemberPrivFastWay)) {
            return self::HT_NOHIT;
        }

        if (1 == $aMemberPrivFastWay['is_auto']) {
            return self::HT_NOHIT;
        }

        $this->_aMemberPrivFastWay = $aMemberPrivFastWay;
        $this->_bLuxuryMember      = LuxuryMemberLogic::isLuxuryPrivilege($aMemberPrivFastWay);
        if (GuideFactory::GUIDE_TYPE_FASTWAY_COUPON == $this->_aRecommendResult['type'] || $this->_bAggregate) {
            return self::HT_HIT;
        }

        return self::HT_NOHIT;
    }

    /**
     * @return BaseGuideCard
     */
    public function execute() {
        if (empty($this->_aRecommendResult)) {
            return null;
        }

        $oCard = new ElementMemberCard();
        // athena 17只针对专车快速通道
        $aMemberPrivFastWay = $this->_aMemberPrivFastWay;
        $aButtonParams      = [
            'guide_pos'       => GuideFactory::GUIDE_TYPE_FASTWAY_COUPON,
            'select_priv_ids' => $aMemberPrivFastWay['priv_id'],
        ];
        if (!$this->_oRuntimeData->bIsSingleCar) {
            $sGroupKey = AnyCarCommonLogic::getGroupKey($this->_aRecommendResult['target_product']);
            $aButtonParams['group_key'] = $sGroupKey;
        }

        $aText        = Language::getDecodedTextFromDcmp('pre_cancel-fast_way_premium_member_guide_v2');
        $sRemainTimes = GuideViewBiz::getRemainTimesText($aMemberPrivFastWay['remain_times']);

        $sTitle           = $aText['title'];
        $sTitleMarkerText = Language::replaceTag($aText['title_marker'],['remain_times' => $sRemainTimes]);

        $oCard->setSTitle($sTitle)
            ->addTitleMarkerText($sTitleMarkerText)
            ->setIGuidePos($this->_aRecommendResult['type'])
            ->setAParams($aButtonParams);

        if (!empty($this->_aRecommendResult['extra_info']['wait_time_saved'])) {
            $oCard->setASubtitle([GuideViewBiz::getEstimateAdvanceTime($this->_aRecommendResult['extra_info']['wait_time_saved'])]);
        }

        list($iMemberType, $iMemberLevel) = MemberViewModel::getMemberTypeAndLevel($aMemberPrivFastWay, $this->_oRuntimeData);
        $oCard->getOMemberModel()->addMemberInfo($iMemberType, $iMemberLevel);
        GuideViewBiz::getMemberTitleMarker($oCard,$iMemberType);
        // 处理按钮
        $oButton = new OmniButton();
        $oButton->setText($aText['button_text']);
        $oButton->setActionType(OmniButton::ACTION_TYPE_UPDATE_ORDER);
        $oButton->setAButtonParams($aButtonParams);
        $oButton->setButtonStyle(GuideCardStyleManage::getDefaultButtonStyle($this->_oRuntimeData->iIsWaitAnswerUpgrade));
        $oOmegaInfo = new OmniOmegaInfo();
        $aOrderInfo = $this->_oRuntimeData->getOrderInfo();
        $oOmegaInfo->setSKey(OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_RECOMMEND_CK, $this->_oRuntimeData->getRequest()['access_key_id']))
            ->setAParams(
                [
                    'order_id'     => $aOrderInfo['order_id'],
                    'uid'          => UtilHelper::getUidByPassengerId($aOrderInfo['passenger_id'], Passport::PASSENGER_ROLE),
                    'area'         => $aOrderInfo['area'],
                    'rec_type'     => BaseViewRender::REC_TYPE_MEMBER_PRIVILEGE,
                    'is_recommend' => 1,
                ]
            );
        $oButton->setOmegaInfo($oOmegaInfo);
        $oCard->setOButton($oButton);
        $oCard->setIRecType(BaseViewRender::REC_TYPE_MEMBER_PRIVILEGE);
        return $oCard;
    }
}
