<?php

namespace PreSale\Models\guideCardView\guideView;

use BizCommon\Constants\OrderNTuple;
use BizLib\Log;
use BizLib\Utils\Language;
use BizLib\Utils\Passport;
use BizLib\Utils\UtilHelper;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TpForm;
use PreSale\Models\guideCardItem\biz\GuideViewBiz;
use PreSale\Models\guideCardItem\button\GuideCardStyleManage;
use PreSale\Models\guideCardItem\button\OmniButton;
use PreSale\Models\guideCardItem\GuideFactory;
use PreSale\Models\guideCardItem\MatchResultRuntime;
use PreSale\Models\guideCardItem\OmniOmegaInfo;
use PreSale\Models\guideCardView\guideCard\BaseGuideCard;
use PreSale\Models\guideCardView\guideCard\ElementMemberCard;

/**
 * 会员追加特快
 */
class GuideMemberAddAPlusView extends BaseViewRender
{

    private $_aRecommendResult;

    private $_bAggregate = false;

    /**
     * 改变车型请求.
     *
     * 包含增加和删除操作。
     */
    const TYPE_ALTER_CAR_LEVEL = 1;

    /**
     * @param MatchResultRuntime $oRuntimeData     运行时数据总线
     * @param array              $aRecommendResult 导流推荐数据
     * @param bool               $bAggregate       bAggregate
     */
    public function __construct($oRuntimeData, $aRecommendResult, $bAggregate = false) {
        parent::__construct($oRuntimeData);
        $this->_aRecommendResult = $aRecommendResult;
        $this->_bAggregate       = $bAggregate;
    }

    /**
     * @return int
     */
    public function getRecommendHitType() {
        if (GuideFactory::GUIDE_TYPE_MEMBER_ADD_A_PLUS == $this->_aRecommendResult['type'] || $this->_bAggregate) {
            // spa费用小于0代表有折扣，才可出
            $this->_oRuntimeData->oSpsModel->buildOnce();
            if ($this->_oRuntimeData->oSpsModel->getSpsFee() < 0) {
                return self::HT_HIT;
            }
        }

        return self::HT_NOHIT;
    }

    /**
     * @return void
     */
    public function prepareViewModel() {
        $this->_oRuntimeData->oAnycarEstimateViewModel->buildOnce();
    }

    /**
     * @return BaseGuideCard
     */
    public function execute() {
        $oCard = new ElementMemberCard();

        // 取特快
        $aAnycarEstimate = $this->_oRuntimeData->oAnycarEstimateViewModel->aAnycarEstimate;
        $aAplusItem      = $this->_getAplusEstimateItem();
        if (empty($aAplusItem)) {
            $oCard->bEnable = false;
            return $oCard;
        }

        // 取sps优惠价格
        $iSavedPrice = $this->_oRuntimeData->oSpsModel->getFormattedFee();
        if ($iSavedPrice <= 0) {
            $oCard->bEnable = false;
            return $oCard;
        }

        $aParams = $this->_getParams($aAnycarEstimate, $aAplusItem);
        $aText   = Language::getDecodedTextFromDcmp('pre_cancel-guide_member_add_aplus_view_v2');

        $oClickOmega = new OmniOmegaInfo();
        $aOrderInfo  = $this->_oRuntimeData->getOrderInfo();
        $oClickOmega->setSKey(OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_RECOMMEND_CK, $this->_oRuntimeData->getRequest()['access_key_id']))
            ->setAParams(
                [
                    'order_id'     => $aOrderInfo['order_id'],
                    'uid'          => UtilHelper::getUidByPassengerId($aOrderInfo['passenger_id'], Passport::PASSENGER_ROLE),
                    'area'         => $aOrderInfo['area'],
                    'rec_type'     => BaseViewRender::REC_TYPE_MEMBER_APPEND_APLUS,
                    'is_recommend' => 1,
                ]
            );
        $oButton = new OmniButton();
        $oButton->setText($aText['button_text'])
            ->setAButtonParams(['select_priv_ids' => json_encode($aParams), 'guide_pos' => GuideFactory::GUIDE_TYPE_FASTWAY_FOR_ANYCAR])
            ->setActionType(OmniButton::ACTION_TYPE_UPDATE_ORDER)
            ->setOmegaInfo($oClickOmega)
            ->setButtonStyle(GuideCardStyleManage::getDefaultButtonStyle($this->_oRuntimeData->iIsWaitAnswerUpgrade));

        $aAddAplusItem = $this->_oRuntimeData->oMultiMember->extractOfsAddAplus();

        $aSubtitle = [];
        // 节约时间
        if (!empty($this->_aRecommendResult['extra_info']['wait_time_saved'])) {
            $aSubtitle[] = GuideViewBiz::getEstimateAdvanceTime($this->_aRecommendResult['extra_info']['wait_time_saved']);
        }

        // 立减x元
        $aSubtitle[] = Language::replaceTag($aText['saved_price'], ['saved_price' => $iSavedPrice]);
        $sPrice      = $aAplusItem['price'] - $this->_oRuntimeData->oSpsModel->getFormattedFee() < 0 ? 0 : $aAplusItem['price'] - $this->_oRuntimeData->oSpsModel->getFormattedFee();

        // 预计 xx 元
        $aSubtitle[] = Language::replaceTag($aText['price'], ['price' => $sPrice]);

        $sTitleMarker = Language::replaceTag($aText['remain_times'], ['remain_times' => GuideViewBiz::getRemainTimesText($aAddAplusItem['remain_times'])]);
        $sTitle       = $aText['title'];
        self::_tpReplaceBusinessName($sTitle,$aText);

        $oCard->setSTitle($sTitle)
            ->addTitleMarkerText($sTitleMarker)
            ->setOButton($oButton)
            ->setIGuidePos(GuideFactory::GUIDE_TYPE_MEMBER_ADD_A_PLUS)
            ->setASubtitle($aSubtitle)
            ->getOMemberModel()
            ->addMemberInfo(OmniOmegaInfo::MEMBER_LICHENG, $this->_oRuntimeData->getMemberLevelId());         // 目前只有里程会员有这项权益, 先写死了
        $oCard->setIRecType(BaseViewRender::REC_TYPE_MEMBER_APPEND_APLUS);
        return $oCard;
    }

    /**
     * @param string $sTitleMarker sTitleMarker
     * @param array  $aText        aText
     * @return void
     */
    private function _tpReplaceBusinessName(&$sTitleMarker, $aText) {
        if (!$this->_oRuntimeData->isIterationSwitchV3()) {
            return;
        }

        $aAplusItem    = $this->_getAplusEstimateItem();
        $aOrderInfo    = $this->_oRuntimeData->getOrderInfo();
        $iOrderId      = $aOrderInfo['order_id'];
        $iProductId    = \BizLib\Utils\Product::getProductIdByBusinessId($aAplusItem['business_id']);
        $sEstimateID   = $aAplusItem['estimate_id'];
        $iUserId       = UtilHelper::getUidByPassengerId($aOrderInfo['passenger_id'], \BizLib\Constants\Common::BIZ_TYPE_PASSENGER);
        $sBusinessName = TpForm::getTpLabelText('',$iOrderId,$iProductId,$sEstimateID);
        if (!empty($sBusinessName)) {
            $sTitleMarker = Language::replaceTag($aText['tp_title_marker'], ['business_name' => $sBusinessName]);
        }
    }

    /**
     * @return array
     */
    private function _getAplusEstimateItem() {
        foreach ($this->_oRuntimeData->oAnycarEstimateViewModel->getProductList() as $aItem) {
            if (OrderNTuple::DIDI_PUTONG_CAR_LEVEL == $aItem['require_level'] && OrderNTuple::LEVEL_TYPE_APLUS == $aItem['level_type']) {
                return $aItem;
            }
        }

        return [];
    }

    /**
     * @param array $aAnycarEstimate anycarEstimate
     * @param array $aAplusItem      aplusItem
     * @return array
     */
    private function _getParams($aAnycarEstimate, $aAplusItem) {
        $aExtraInfo = [
            'anycar' => [
                'access_key_id'             => $this->_oRuntimeData->getRequest()['access_key_id'],
                'estimate_id'               => $aAnycarEstimate['estimate_id'],
                'estimate_trace_id'         => $aAnycarEstimate['estimate_trace_id'],
                'multi_require_product_add' => [[
                    'business_id'      => $aAplusItem['business_id'],
                    'combo_type'       => $aAplusItem['combo_type'],
                    'estimate_id'      => $aAplusItem['estimate_id'],
                    'is_selected'      => 1,
                    'level_type'       => OrderNTuple::LEVEL_TYPE_APLUS,
                    'product_category' => $aAplusItem['product_category'],
                    'require_level'    => $aAplusItem['require_level'],
                ],
                ],
                'type'                      => self::TYPE_ALTER_CAR_LEVEL,
            ],
        ];
        $aParams    = [
            'extraInfo'  => json_encode($aExtraInfo),
            'v6_version' => 1,
            'mock'       => 'add_aplus_mock',
            'guide_pos'  => GuideFactory::GUIDE_TYPE_FASTWAY_FOR_ANYCAR,
        ];
        return $aParams;
    }
}
