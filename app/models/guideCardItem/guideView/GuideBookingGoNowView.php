<?php

namespace PreSale\Models\guideCardItem\guideView;

use BizCommon\Logics\Anycar\AnyCarCommonLogic;
use BizCommon\Logics\ProductCategoryCustomize\ProductCategoryTitleCustomizeLogic;
use BizLib\Client\MambaClient;
use BizLib\Constants\Horae;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Language;
use BizLib\Utils\Passport;
use BizLib\Utils\Product;
use BizLib\Utils;
use BizLib\Constants;
use BizLib\Utils\ProductCategory;
use BizLib\Utils\UtilHelper;
use PreSale\Infrastructure\Repository\Redis\GuideCardRedisRepository;
use PreSale\Logics\sideEstimate\component\communicate\event\EventConst;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TaxiPricingBoxLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TpForm;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutAbstract;
use PreSale\Models\guideCardItem\biz\GuideViewBiz;
use PreSale\Models\guideCardItem\button\GuideCardStyleManage;
use PreSale\Models\guideCardItem\button\OmniButton;
use PreSale\Models\guideCardItem\EstimateInfo;
use PreSale\Models\guideCardItem\guideCard\GuideBookingCard;
use PreSale\Models\guideCardItem\GuideFactory;
use PreSale\Models\guideCardItem\MatchResultRuntime;
use PreSale\Models\guideCardItem\OmniOmegaInfo;
use PreSale\Models\guideCardView\guideCard\BaseGuideCard;
use PreSale\Models\guideCardView\guideCard\ElementMemberCard;
use PreSale\Models\guideCardView\guideCard\GuideAnycarCard;
use PreSale\Models\guideCardView\guideCard\GuideAnycarCardV3;
use PreSale\Models\guideCardView\guideView\BaseViewRender;
use PreSale\Models\guideCardView\viewModel\AnycarEstimateViewModel;

/**
 *  预约单 关单 - 立即出发 View
 */
class GuideBookingGoNowView extends BaseViewRender
{

    /**
     * @param MatchResultRuntime $oRuntimeData 运行时数据总线
     *
     */
    public function __construct($oRuntimeData) {
        parent::__construct($oRuntimeData);

        $sText   = Language::getTextFromDcmp('pre_cancel-booking_go_now');
        $this->_aText = json_decode($sText, true) ?: [];
    }

    /**
     * @return void
     */
    public function prepareViewModel() {
        $this->_oRuntimeData->oOrderBookingPreCancelEstimateViewModel->buildOnce();
    }

    /**
     * @return int
     */
    public function getRecommendHitType() {

        return self::HT_NOHIT;
    }

    /**
     * @return BaseGuideCard
     */
    public function execute() {

        $aRequest   = $this->_oRuntimeData->getRequest();
        $aOrderInfo = $this->_oRuntimeData->getOrderInfo();

        // 粤港车
        if (\BizLib\Utils\Horae::isShenGangFlatRate($aOrderInfo['combo_type'])) {
            return null;
        }

        // 返回卡片信息
        $oGuideCard = new GuideBookingCard();

        $aText        = $this->_aText;
        $sTitle           = $aText['title']??'现在呼叫%s，预估很快有车接你';
        $sTitle = sprintf($sTitle, $this->_convertProductName($aOrderInfo));
        $oGuideCard->setTitle($sTitle);
        $oEstimateInfo = new EstimateInfo();

        $oEstimateInfo->bFresh = true;
        $oEstimateInfo->sEstimateId      = $this->_oRuntimeData->oOrderBookingPreCancelEstimateViewModel->aOrderBookingPreCaneclEstimate['estimate_id'];
        $oEstimateInfo->sEstimateTraceId = $this->_oRuntimeData->oOrderBookingPreCancelEstimateViewModel->aOrderBookingPreCaneclEstimate['estimate_trace_id'];
        $oEstimateInfo->sFeeDetailUrl = $this->_oRuntimeData->oOrderBookingPreCancelEstimateViewModel->aOrderBookingPreCaneclEstimate['fee_detail_url'];
        // 这个数据是售前接口 直接透传
        $aProductList =  $this->_oRuntimeData->oOrderBookingPreCancelEstimateViewModel->aOrderBookingPreCaneclEstimate['estimate_data'];
        $oEstimateInfo->aEstimateData = $aProductList;
        // 获取Layout
        $aLayout = $this->_getOrderBookingPreCancelEstimateLayout($aRequest, $aOrderInfo, $aProductList);
        // 增加兜底处理 如果 layout 和 estimatedata 不匹配 记录日志并返回空
        if (empty($aLayout) || count($aLayout) !=  count($aProductList)) {
            NuwaLog::warning('guide booking pre cancel  layout not match estimate_data');
            return null;
        }

        $oEstimateInfo->aLayout       = $aLayout;
        $oGuideCard->setEstimateInfo($oEstimateInfo);

        $oOmegaInfo = new OmniOmegaInfo();
        $oOmegaInfo->setSKey(OmniOmegaInfo::ORDER_CLOSE_POPUP_CALLNOW_SHOW);
        $oOmegaInfo->setAParams(
            [
                'uid'           => UtilHelper::getUidByPassengerId($this->_oRuntimeData->getOrderInfo()['passenger_id'], Passport::PASSENGER_ROLE),
            ]
        );
        $oGuideCard->setOmegaInfo($oOmegaInfo);

        return $oGuideCard;
    }

    /**
     * @param $aRequest
     * @param $aOrderInfo
     * @param $aProductList
     * @return array|mixed|null
     */
    private function _getOrderBookingPreCancelEstimateLayout($aRequest, $aOrderInfo, $aProductList = []) {

        if ($this->_oRuntimeData->isIterationSwitchV3()) {
            $aLayout = [];
            if (!empty($aProductList)) {
                $aProductCategotyList = [];
                foreach ($aProductList as $key => $aProductItem) {
                    $aProductCategotyList[] = $aProductItem['product_category'];
                }

                if (!empty($this->_oRuntimeData->oOrderBookingPreCancelEstimateViewModel->aOrderBookingPreCaneclEstimate['layout'])) {
                    foreach ($this->_oRuntimeData->oOrderBookingPreCancelEstimateViewModel->aOrderBookingPreCaneclEstimate['layout'] as $key => $aLayoutItem) {
                        $aGroup = $aLayoutItem['groups'][0];
                        $aGroup['action_type'] = EventConst::ACTION_TYPE_HALF_POP;
                        $aGroup['type'] = LayoutAbstract::GUIDE_GROUP_TYPE;
                        $aGroup['extra_map'] = $this->_buildAnyCarNewOrderParams($aRequest, $aOrderInfo,"pre-sale");
                        $aGroup['omega_info'] = $this->_buildOmegaInfo($aOrderInfo, $aRequest);
                        $aGroup['button_style'] = $this->buildButtonStylee();
                        $aGroup['button_text'] = $this->_aText["button_text"]??'立刻呼叫';
                        $aLayoutItem['groups'][0] = $aGroup;
                        $bNeed = true;
                        if (!empty($aGroup['products'])) {
                            foreach ($aGroup['products'] as $key => $iProductCategory) {
                                if (!in_array($iProductCategory, $aProductCategotyList)) {
                                    $bNeed = false;
                                    break;
                                }
                            }
                        }

                        if ($bNeed) {
                            $aLayout[] = $aLayoutItem;
                        }
                    }
                }
            } else {
                $aLayout = $this->_oRuntimeData->oOrderBookingPreCancelEstimateViewModel->aOrderBookingPreCaneclEstimate['layout'];
            }
            return $aLayout;
        }
        return null;
    }



    private function buildButtonStylee() {

        $aButtonStyleConfig = $this->_aText['button_style'];
        $sBorderRadius = $aButtonStyleConfig["border_radius"]??"30";
        if (in_array($this->_oRuntimeData->getRequest()['access_key_id'], [Constants\Common::DIDI_IOS_PASSENGER_APP, Constants\Common::DIDI_ANDROID_PASSENGER_APP])) {
            $sBorderRadius = $aButtonStyleConfig["border_radius_na"]??"15";
        }
            $aButtonStyle = [
            "border_color"  => $aButtonStyleConfig['border_color']??"#FF6435",
            "font_color"    => $aButtonStyleConfig['font_color']??"#FFFFFF",
            "bg_gradients"  => $aButtonStyleConfig['bg_gradients']??["#FF6435","#FF6435"],
            "corner_radius" => $aButtonStyleConfig['corner_radius']??"25",
            "border_radius" => $sBorderRadius
        ];
            return $aButtonStyle;

    }

    private function _buildOmegaInfo($aOrderInfo, $aRequest) {

        $oOmegaInfo = new OmniOmegaInfo();
        $aOmegaParams = [
            'uid' => UtilHelper::getUidByPassengerId($aOrderInfo['passenger_id'], Passport::PASSENGER_ROLE),
        ];

        $oOmegaInfo->setSKey(OmniOmegaInfo::ORDER_CLOSE_POPUP_CALLNOW_CK)
            ->setAParams($aOmegaParams);
        return $oOmegaInfo;
    }

    /**
     * @param $aRequest
     * @param $aOrderInfo
     * @param $sFrom
     * @return array
     */
    private function _buildAnyCarNewOrderParams($aRequest, $aOrderInfo, $sFrom) {

        $aMultiRequireProduct = $this->_buildMultRequireProduct();
        $iSeatNum     = in_array($aRequest['carpool_seat_num'], [1, 2]) ? $aRequest['carpool_seat_num'] : 1;
        $aEstimateReq = [
            'token'                 => $aRequest['token'],
            'a3_token'              => $aRequest['a3_token'],
            'map_type'              => $aRequest['map_type'],
            'estimate_id'           => $this->_oRuntimeData->oOrderBookingPreCancelEstimateViewModel->aOrderBookingPreCaneclEstimate['estimate_id'],
            'estimate_trace_id'     => $this->_oRuntimeData->oOrderBookingPreCancelEstimateViewModel->aOrderBookingPreCaneclEstimate['estimate_trace_id'],
            'pool_seat'             => $iSeatNum,
            'origin_id'             =>  "1",
            'order_id'              => UtilHelper::encodeId($aOrderInfo['order_id'], $aOrderInfo['district']),
            'lang'                  => $aRequest['lang'] ?? 'zh-CN',
            'multi_require_product' => $aMultiRequireProduct,
            'appversion'            => $aRequest['appversion'] ?? $aRequest['app_version'],
            'datatype'              => $aRequest['datatype'],
            'channel'               => $aRequest['channel'],
            'v6_version'            => $aRequest['v6_version'] ?? false,
            'access_key_id'         => $aRequest['access_key_id'],
            'client_type'           => $aRequest['client_type'],
        ];
        return $aEstimateReq;
    }

    private function _buildMultRequireProduct() {

        $aEstimateDataList = [];
        $aEstimateData= array_shift($this->_oRuntimeData->oOrderBookingPreCancelEstimateViewModel->aOrderBookingPreCaneclEstimate['estimate_data']);
        $aMultRequireProduct = [
            'business_id'       =>  $aEstimateData['scene_info']['business_id'],
            'require_level'     =>  $aEstimateData['scene_info']['require_level'],
            'level_type'        =>  $aEstimateData['scene_info']['level_type'],
            'is_selected'       =>  $aEstimateData['is_selected'],
            'product_category'  =>  $aEstimateData['product_category'],
            'combo_type'        =>  $aEstimateData['scene_info']['combo_type'],
            'estimate_id'       =>  $aEstimateData['estimate_id'],
        ];
        $aEstimateDataList[] = $aMultRequireProduct;

        return json_encode($aEstimateDataList);
    }

    /**
     *
     * @param $aOrderInfo
     * @return string
     */
    private function _convertProductName($aOrderInfo) {
        $sProduct = '';
        if (Product::isFastcar($aOrderInfo['product_id'])) {
            $sProduct = '快车';
        }

        if (Product::isFirstClass($aOrderInfo['product_id'])) {
            $sProduct = '豪华车';
        }

        if (Product::isSpecial($aOrderInfo['product_id'])) {
            $sProduct = '专车';
        }

        if (Product::isUniTaxi($aOrderInfo['product_id'])) {
            $sProduct = '出租车';
        }

        if(Utils\CarLevel::DIDI_YOUXIANG_CAR_LEVEL == $aOrderInfo['require_level']) {
            $sProduct = '优享';
        }

        return $sProduct;
    }
}
