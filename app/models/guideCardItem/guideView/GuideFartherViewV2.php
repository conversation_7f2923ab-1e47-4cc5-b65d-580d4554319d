<?php

namespace PreSale\Models\guideCardView\guideView;

use BizCommon\Logics\Anycar\AnyCarCommonLogic;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\Language;
use PreSale\Models\guideCardItem\GuideFactory;
use PreSale\Models\guideCardItem\MatchResultRuntime;
use PreSale\Models\guideCardView\guideCard\GuideAnycarV2Card;

/**
 * 加价调度
 */
class GuideFartherViewV2 extends BaseViewRender
{
    /**
     * 导流数据
     * @var array
     */
    private $_aOrderMatchRecommendResult;

    /**
     * @param MatchResultRuntime $oRuntimeData 运行时数据总线
     * @param array $aOrderMatchRecommendResult athena推荐
     * @param array $aGuideRecommendInfo athena推荐
     * @param array $aAthenaExpectInfo athena期望信息
     */
    public function __construct($oRuntimeData, $aOrderMatchRecommendResult, $aGuideRecommendInfo, $aAthenaExpectInfo) {
        parent::__construct($oRuntimeData);
        $this->_aOrderMatchRecommendResult = $aOrderMatchRecommendResult;
    }

    /**
     * @return int
     */
    public function getRecommendHitType() {
        if (GuideFactory::GUIDE_TYPE_FARTHER == $this->_aOrderMatchRecommendResult['type']) {
            return self::HT_HIT;
        }

        // athena现支持多勾发单也返回一个推荐的加价调度车型
        if (GuideFactory::GUIDE_TYPE_FARTHER_FOR_ANYCAR == $this->_aOrderMatchRecommendResult['type']) {
            return self::HT_HIT;
        }

        return self::HT_NOHIT;
    }

    /**
     * @return GuideAnycarV2Card
     */
    public function execute() {
        // 复用追加车型推荐卡片样式
        $oGuideCard = new GuideAnycarV2Card();
        $aText = NuwaConfig::text('pre_cancel', 'farther_view_v2');
        if (empty($aText)) {
            return null;
        }

        $aRecommendResult = $this->_aOrderMatchRecommendResult;
        if (empty($aRecommendResult) || empty($aRecommendResult['etp'])) {
            return null;
        }

        $iEts = $aRecommendResult['etp'];
        $sTitle = Language::replaceTag($aText['title_etp_min'], ['ets_time' => $iEts]);
        if (!isset($aRecommendResult['add_money']) || !isset($aRecommendResult['etpDistance'])) {
            return null;
        }

        $sFeeDesc = Language::replaceTag($aText['fee_desc'], ['add_money' => $aRecommendResult['add_money'],]);
        $aCarNameList = json_decode(Language::getTextFromDcmp('config_anycar-anycar_simple_name'), true);
        $sCarTitle = $aCarNameList[AnyCarCommonLogic::getGroupKey($aRecommendResult['target_product'])] ?? '车辆';
        $sSubTitle = Language::replaceTag($aText['sub_title'], ['distance' => $aRecommendResult['etpDistance'], 'car_title' => $sCarTitle]);

        $oGuideCard->setTitle($sTitle);
        $oGuideCard->setSubTitle($sSubTitle);
        $oGuideCard->setIcon($aText['icon']);
        $oGuideCard->setFeeDesc($sFeeDesc);
        $oGuideCard->setIRecType(BaseViewRender::REC_TYPE_GUIDE_FARTHER);
        $aOmegaParams = [
            'rec_type'                  => $oGuideCard->getIRecType(),
            'product_category'          => $aRecommendResult['target_product']['product_category'],
            'estimate_answered_seconds' => $iEts,
            'estimate_add_price'        => $aRecommendResult['add_money'],
        ];
        $oGuideCard->setOmegaParams($aOmegaParams);
        return $oGuideCard;
    }
}
