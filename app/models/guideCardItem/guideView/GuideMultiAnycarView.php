<?php

namespace PreSale\Models\guideCardView\guideView;

use BizCommon\Logics\Anycar\AnyCarCommonLogic;
use BizCommon\Logics\ProductCategoryCustomize\ProductCategoryTitleCustomizeLogic;
use BizLib\Config;
use BizLib\Constants\Horae;
use BizLib\Log;
use BizLib\Utils\Language;
use BizLib\Utils\Passport;
use BizLib\Utils\ProductCategory;
use BizLib\Utils\UtilHelper;
use PPreCancelOrderController;
use PreSale\Infrastructure\Repository\Redis\GuideCardRedisRepository;
use PreSale\Infrastructure\Repository\Rpc\MartiniRepository;
use PreSale\Logics\order\NewtonLogic;
use PreSale\Logics\v3Estimate\multiResponse\Component\carpoolSeat\CarpoolSeatInfo;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\NewtonUtil;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TpForm;
use PreSale\Models\guideCardItem\button\GuideCardStyleManage;
use PreSale\Models\guideCardItem\button\OmniButton;
use PreSale\Models\guideCardItem\common\SceneManager;
use PreSale\Models\guideCardItem\EstimateInfo;
use PreSale\Models\guideCardItem\GuideFactory;
use PreSale\Models\guideCardItem\MatchResultRuntime;
use PreSale\Models\guideCardItem\OmniOmegaInfo;
use PreSale\Models\guideCardView\guideCard\BaseGuideCard;
use PreSale\Models\guideCardView\guideCard\GuideMultiAnycarCard;
use PreSale\Models\guideCardView\viewModel\AnycarEstimateViewModel;
use BizLib\Log as NuwaLog;
use PreSale\Models\guideCardItem\common\Etp2EtsSceneProcess;
use TripcloudCommon\Utils\Product as TripcloudProduct;
use Xiaoju\Apollo\Apollo;

/**
 * 追加车型多推荐
 */
class GuideMultiAnycarView extends BaseViewRender
{

    /**
     * 导流数据
     * @var array
     */
    private $_aOrderMatchRecommendResult;

    const ANIMATION_SCENE_QUEUE = 2;

    /**
     * 导流追加车型数据
     * @var array
     */
    private $_aGuideItem;

    /**
     * Etp转Ets开关
     * @var \Xiaoju\Apollo\DefaultToggle|\Xiaoju\Apollo\FeatureToggle|\Xiaoju\Apollo\ToggleResult
     */
    private $_oEtp2etsSwitch;

    const TAXI_BOX_GROUP_ID = '5_5';

    /**
     * @param MatchResultRuntime $oRuntimeData               运行时数据总线
     * @param array              $aOrderMatchRecommendResult athena推荐
     * @param bool               $bAggregate                 bAggregate
     * @param array              $aGuideRecommendInfo        athena推荐
     */
    public function __construct($oRuntimeData, $aOrderMatchRecommendResult, $bAggregate, $aGuideRecommendInfo) {
        parent::__construct($oRuntimeData);
        $this->_aOrderMatchRecommendResult = $aOrderMatchRecommendResult;
        $sText        = $oRuntimeData->oTextService->getText('guide_append_multiple_anycar_v6');
        if ($oRuntimeData->bNewPopExam) {
            $sText  = $oRuntimeData->oTextService->getText('guide_append_multiple_anycar_v6_v2');
        }
        $this->_aText = json_decode($sText, true) ?: [];
        $aOrderInfo   = $oRuntimeData->getOrderInfo();
        $this->_oRuntimeData = $oRuntimeData;
        $sAppVersion         = $this->_oRuntimeData->getRequest()['app_version'];
        if (empty($sAppVersion)) {
            $sAppVersion = $this->_oRuntimeData->getRequest()['appVersion'];
        }

        if (empty($sAppVersion)) {
            $sAppVersion = $this->_oRuntimeData->getRequest()['appversion'];
        }

        $this->_oEtp2etsSwitch = Apollo::getInstance()->featureToggle(
            'etp_to_ets_knife_switch',
            [
                'key'           => $aOrderInfo['passenger_id'],
                'city'          => $aOrderInfo['area'],
                'pid'           => $aOrderInfo['passenger_id'],
                'lang'          => $this->_oRuntimeData->getRequest()['lang'],
                'app_version'   => $sAppVersion,
                'access_key_id' => $aOrderInfo['p_access_key_id'],
            ]
        );

        if (is_array($aGuideRecommendInfo)) {
            foreach ($aGuideRecommendInfo as $aItem) {
                if (GuideFactory::GUIDE_TYPE_ANYCAR == $aItem['type']) {
                    $this->_aGuideItem = $aItem;
                }
            }
        }
    }

    /**
     * @return void
     */
    public function prepareViewModel() {
        $this->_oRuntimeData->oAnycarEstimateViewModel->buildOnce();
    }

    /**
     * @return int
    */
    public function getRecommendHitType() {
        // 如果命中预付发单，屏蔽追加车型
        if (NewtonLogic::judgeDesignatedNewOrder($this->_oRuntimeData)) {
            return self::HT_NOHIT;
        }

        // 如果apollo未开量不命中
        if (!$this->_oRuntimeData->bAllowMultiAppendCar) {
            return self::HT_NOHIT;
        }

        // 如果athena返回的是多推荐，走multiAnycarView
        if (!$this->_oRuntimeData->isIterationSwitchV3() || (!empty($this->_aOrderMatchRecommendResult['extra_info']) && empty($this->_aOrderMatchRecommendResult['extra_info']['answer_rate_improve']))) {
            return self::HT_NOHIT;
        }

        // 如果没有缓存的预估数据 不渲染
        if (empty($this->_oRuntimeData->oAnycarEstimateViewModel->aAnycarEstimate)) {
            return self::HT_NOHIT;
        }

        if (GuideFactory::GUIDE_TYPE_ANYCAR == $this->_aOrderMatchRecommendResult['type']) {
            return self::HT_HIT;
        }

        // 追加拼车用户点击座位数后，需要重新渲染
        if (!empty($this->_oRuntimeData->getRequest()['carpool_seat_num'])) {
            return self::HT_HIT;
        }

        return self::HT_NOHIT;
    }

    /**
     * 获取多车型推荐出口卡片
     * @return BaseGuideCard
     */
    public function execute() {
        $oGuideCard = new GuideMultiAnycarCard();

        // 处理多车型推荐出口卡片
        $aOrderMatchRecommendResult = $this->_aOrderMatchRecommendResult;

        // 处理拼车切座
        if (empty($aOrderMatchRecommendResult)) {
            // 推荐追加拼车，由于athena只在首次命中时返回推荐数据，而在推荐时长（如15s）内轮询时不再返回推荐数据，而追加拼车由于选座组件的出现，
            // 卡片的最终渲染会发生变化。而因为数据源（athena返的推荐）并不是每次都有，我们所拥有的只有上一次卡片的渲染数据。因此对于追加拼车，
            // 我们1.当上张卡片是追加拼车卡片时，HIT_TYPE=HT_HIT；2.不再走用athena数据源的渲染逻辑，而是对原卡片做渲染的替换
            if (!empty($this->_oRuntimeData->getRequest()['carpool_seat_num'])) {
                $oGuideCard = GuideCardRedisRepository::get($this->_oRuntimeData->getOrderInfo()['passenger_id'], BaseViewRender::CARD_ID_MULTI_APPEND_CARPOOL);
                return $this->_appendCarpoolReRender($oGuideCard);
            }
        }

        // 处理推荐弹窗卡片出口标题
        $aTargetProductList = $aOrderMatchRecommendResult['target_product_list'];
        // 如果Athena没有推荐任何车型，不展示推荐出口卡片
        if (empty($aTargetProductList)) {
            return $oGuideCard;
        }

        $aExtraInfo = $aOrderMatchRecommendResult['extra_info'];

        $sTitle = self::_getMultipleRecommendGuideCardTitle($aTargetProductList, $aExtraInfo);
        // 处理要返回的推荐车型信息
        $aTargetRecommendProductList = array();
        $aTargetProductListIndexMap  = array();
        $aTargetProductSourceData    = array();
        foreach ($aTargetProductList as $iIndex => $aTargetProduct) {
            $aTargetProductSourceData[$aTargetProduct['product_category']] = $aTargetProduct;
            $aTargetProduct = self::_updateProduct($aTargetProduct);
            $aTargetRecommendProductList[$aTargetProduct['product_category']] = $aTargetProduct;
            $aTargetProductListIndexMap[$aTargetProduct['product_category']]  = $iIndex;
        }

        $oEstimateInfo = new EstimateInfo();
        $aProductList  = array();
        if (0 == !sizeof($aTargetRecommendProductList)) {
            $oEstimateInfo->bFresh           = true;
            $aMultiRequireProduct            = $aTargetRecommendProductList;
            $oEstimateInfo->sEstimateId      = $this->_oRuntimeData->oAnycarEstimateViewModel->aAnycarEstimate['estimate_id'];
            $oEstimateInfo->sEstimateTraceId = $this->_oRuntimeData->oAnycarEstimateViewModel->aAnycarEstimate['estimate_trace_id'];
            $aProductList = $this->_judgeAndSetProductCustomTitle($this->_oRuntimeData->getOrderInfo(), $aMultiRequireProduct,$aTargetProductSourceData);
            $oEstimateInfo->sFeeDetailUrl = $this->_oRuntimeData->oAnycarEstimateViewModel->aAnycarEstimate['fee_detail_url'];
            // 获取去除盒子类型的layout
            $aLayout = $this->_oRuntimeData->oAnycarEstimateViewModel->getAnycarEstimateLayout(array_values($aTargetRecommendProductList), AnycarEstimateViewModel::NO_BOX_LAYOUT);
            // 增加兜底处理 如果 layout 和 estimatedata 不匹配 记录日志并返回空
            if (empty($aLayout) || count($aLayout) != count($aProductList)) {
                NuwaLog::warning('guideMultiAnycarView layout not match estimate_data');
                return null;
            }

            // 按照athena返回的车型顺序排序
            usort(
                $aLayout,
                function ($layout1, $layout2) use ($aTargetProductListIndexMap) {
                // 获取layout中的products
                    $aGroup1           = $layout1['groups'][0];
                    $aGroup2           = $layout2['groups'][0];
                    $iProductCategory1 = $aGroup1['products'][0];
                    $iProductCategory2 = $aGroup2['products'][0];
                    if ($aTargetProductListIndexMap[$iProductCategory1] == $aTargetProductListIndexMap[$iProductCategory2]) {
                        return 0;
                    }

                    return $aTargetProductListIndexMap[$iProductCategory1] > $aTargetProductListIndexMap[$iProductCategory2] ? 1 : -1;
                }
            );
            $oEstimateInfo->aLayout = $aLayout;
        } else {
            $oEstimateInfo->bFresh = false;
        }

        $oEstimateInfo->aEstimateData = $aProductList;
        $this->_hackTaxiDoublePriceV3($oEstimateInfo->aEstimateData);
        self::_hackBoxCarIcon($oEstimateInfo);
        $oGuideCard->setEstimateInfo($oEstimateInfo);
        $oGuideCard->setTitle($sTitle);
        $sBgColor = $this->_getText('bg_color');
        $oGuideCard->setBgColor($sBgColor);
        $sHighlightTitleColor = $this->_getText('highlight_title_color');
        $sLineColor           = $this->_getText('line_color');
        $oGuideCard->setLineColor($sLineColor);
        $oGuideCard->setHighlightTitleColor($sHighlightTitleColor);
        // 处理button，一键追加按钮复用之前的按钮，保持不变
        $oButton = new OmniButton();
        $oButton->setText($this->_getText('button_text'));
        $oButton->setActionType(OmniButton::ACTION_TYPE_ANYCAR_NEW_ORDER);
        $oButton->setButtonStyle(GuideCardStyleManage::getMultipleRecommendButtonStyle());
        $aParams = [
            'guide_pos' => GuideFactory::GUIDE_TYPE_ANYCAR,
            'source'    => PPreCancelOrderController::ATHENA_GUIDE_SOURCE_CANCEL_DETAINMENT,
        ];
        $oButton->setAButtonParams($aParams);
        $oGuideCard->setButton($oButton);
        $sOmegaKey  = OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_RECOMMEND_CK, $this->_oRuntimeData->getRequest()['access_key_id']);
        $oOmegaInfo = new OmniOmegaInfo();
        $oOmegaInfo->setSKey($sOmegaKey);
        $aEstimateInfoOfOmega = array();
        $sEstimateTraceId     = $this->_oRuntimeData->oAnycarEstimateViewModel->aAnycarEstimate['estimate_trace_id'];
        foreach ($aProductList as $sProductCategory => $aProduct) {
            $aCustomTagList = [];
            foreach ($aProduct['fee_desc_list'] as $aFeeDesc) {
                $aCustomTagList[] = $aFeeDesc['custom_tag'];
            }
            $aEstimateInfoOfOmega[$sProductCategory] = [
                'estimate_id'       => $aProduct['estimate_id'],
                'estimate_trace_id' => $sEstimateTraceId,
                'is_selected'       => $aProduct['is_selected'],
                'custom_tag_list'   => $aCustomTagList,
            ];
            //埋点增加图片链接
            if (!empty($aProduct['fee_desc_list']) && is_array($aProduct['fee_desc_list'])) {
                $aIconLink = [];
                foreach ($aProduct['fee_desc_list'] as $aFeeDesc) {
                    if (!empty($aFeeDesc['icon'])) {
                        $aIconLink[] = $aFeeDesc['icon'];
                    }
                }

                $aEstimateInfoOfOmega[$sProductCategory]['icon_link'] = $aIconLink;
            }
        }

        // 将埋点数据中需要的预估数据放入数据总线
        $this->_oRuntimeData->setGuideMultiAnycarEstimateDataOfOmega($aEstimateInfoOfOmega);
        $oGuideCard->setIRecType(BaseViewRender::REC_TYPE_APPEND_MULTIPLE_RECOMMEND_ANYCAR);
        $oOmegaInfo->setAParams(
            [
                'order_id'      => $this->_oRuntimeData->getOrderInfo()['order_id'],
                'uid'           => UtilHelper::getUidByPassengerId($this->_oRuntimeData->getOrderInfo()['passenger_id'], Passport::PASSENGER_ROLE),
                'city_id'       => $this->_oRuntimeData->getOrderInfo()['area'],
                'is_recommend'  => 1,
                'rec_type'      => $oGuideCard->getIRecType(),
                'estimate_info' => json_encode($aEstimateInfoOfOmega),
            ]
        );
        $oButton->setOmegaInfo($oOmegaInfo);
        return $oGuideCard;
    }

    /**
     * 处理拼车切座
     * @param GuideMultiAnycarCard $oGuideCard 处理前的多车型推荐出口卡片
     * @return BaseGuideCard 处理后的多车型推荐出口卡片
     */
    private function _appendCarpoolReRender($oGuideCard) {
        if (empty($oGuideCard)) {
            return null;
        }

        // 判断座位数是否一致，一致则不更新
        $oEstimateInfo = $oGuideCard->getEstimateInfo();
        // 存在多个车型
        $aSeatNums     = [];
        $aEstimateData = $oEstimateInfo->aEstimateData;
        foreach ($aEstimateData as $aProductItem) {
            $sGroupKey = AnyCarCommonLogic::getGroupKey($aProductItem);
            if (AnyCarCommonLogic::PRODUCT_KEY_CARPOOL == $sGroupKey) {
                $aSeatNums = $aProductItem['seat_nums'];
                break;
            }
        }

        $iOldSeatNum = 1;
        if (!empty($aSeatNums)) {
            foreach ($aSeatNums as $aSeatItem) {
                if (1 == $aSeatItem['is_selected']) {
                    $iOldSeatNum = $aSeatItem['num'];
                }
            }
        }

        if ($this->_oRuntimeData->getRequest()['carpool_seat_num'] != $iOldSeatNum) {
            // 需要更新拼车预估数据
            $this->_oRuntimeData->oAnycarEstimateViewModel->buildOnce();
            $aEstimateData = $this->_updateCarpoolEstimateData($aEstimateData);
        }

        $aMultiRequireProduct         = $aEstimateData;
        $oEstimateInfo->aEstimateData = $this->_judgeAndSetProductCustomTitle($this->_oRuntimeData->getOrderInfo(), $aMultiRequireProduct);
        $oGuideCard->setEstimateInfo($oEstimateInfo);
        return $oGuideCard;
    }

    /**
     * 获取多推荐弹窗出口卡片的标题
     * @param array $aTargetProductList 推荐车型数据集合
     * @param array $aExtraInfo         推荐结果中的额外信息
     * @return string 标题
     */
    private function _getMultipleRecommendGuideCardTitle($aTargetProductList, $aExtraInfo) {
        $iAmountOfTargetProductList = 0;
        $aEtpList = array();

        foreach ($aTargetProductList as $aTargetProduct) {
            $sEtp = $aTargetProduct['extra_info']['etp'];
            // etp为0的情况，代表该车型无车或者排队
            $iAmountOfTargetProductList++;
            if (empty($sEtp) || '0' == $sEtp) {
                continue;
            }

            array_unshift($aEtpList, (int)($sEtp));
        }

        if($this->_oRuntimeData->bNewPopExam) {
            $sTitlePrefix = $this->_getText('title_prefix_v2');
            $sTitleSuffix = '';
            if (0 != sizeof($aEtpList)) {
                $sTitleSuffix =  $this->_getText('title_has_etp_suffix_v2', ['ets_min' => (string)(min($aEtpList))]);
            }
            return $sTitlePrefix."\n".$sTitleSuffix;
        }

        $sTitlePrefix       = $iAmountOfTargetProductList > 1 ? $this->_getText(
            'title_with_multiple_product_prefix',
            ['amount' => $iAmountOfTargetProductList]
        ) : $this->_getText('title_with_single_product_prefix');
        $sAnswerRateImprove = $aExtraInfo['answer_rate_improve'];
        $iFloorImproveRateOfAnswer = $this->_getText('floor_improve_rate_of_answer');
        $iUpperImproveRateOfAnswer = $this->_getText('upper_improve_rate_of_answer');
        $bIsCanImproveAnswerRate   = !empty($sAnswerRateImprove) && (int)((float)($sAnswerRateImprove) * 100) >= $iFloorImproveRateOfAnswer && (int)((float)($sAnswerRateImprove) * 100) <= $iUpperImproveRateOfAnswer;
        if ($bIsCanImproveAnswerRate) {
            $sTitleSuffix = $this->_getText('title_has_valid_answer_rate_suffix', ['improve_rate' => (int)((float)($sAnswerRateImprove) * 100)]);
            return $sTitlePrefix."\n".$sTitleSuffix;
        }

        if ($this->_oEtp2etsSwitch->allow() && MatchResultRuntime::EXP_CONTROL_GROUP != $this->_oRuntimeData->iExperimentalGroup) {
            $sConfigKey       = $this->_oEtp2etsSwitch->getParameter('config_key','cancel_stay_popup_ets_condition_config');
            $aConditionConfig = Config::getBizConfig($sConfigKey,'condition_config');
            $aCondCfg         = $aConditionConfig['condition_config'];
            $sDocKey          = $aConditionConfig['doc_key'];
            $iMinEts          = $this->_aOrderMatchRecommendResult['extra_info']['cancel_rec_ets'];
            $sTitleSuffix     = $this->_getEtsStr($iMinEts,$aCondCfg,$sDocKey);
            return $sTitlePrefix."\n".$sTitleSuffix;
        }

        $sTitleSuffix = 0 == sizeof($aEtpList) ? $this->_getText('title_has_no_etp_suffix') : $this->_getText('title_has_etp_suffix', ['etp' => (string)(min($aEtpList))]);
        return $sTitlePrefix."\n".$sTitleSuffix;
    }

    /**
     * 更新拼车预估价格数据
     * @param array $aEstimateData 推荐的车型数据
     * @return array
     */
    private function _updateCarpoolEstimateData($aEstimateData) {
        // 获取预估数据
        $this->_oRuntimeData->oAnycarEstimateViewModel->buildOnce();
        $aProductList = $this->_oRuntimeData->oAnycarEstimateViewModel->getProductList();
        if (empty($aProductList)) {
            return $aEstimateData;
        }

        foreach ($aEstimateData as &$aProductItem) {
            $sGroupKey = AnyCarCommonLogic::getGroupKey($aProductItem);
            if (AnyCarCommonLogic::PRODUCT_KEY_CARPOOL == $sGroupKey) {
                // 获取拼车新的预估数据
                $aCarpoolEstimateItem = $this->_getCarpoolEstimateData();
                $aProductItem         = array_merge($aProductItem, $aCarpoolEstimateItem);
                break;
            }
        }

        return $aEstimateData;
    }

    /**
     * 从追加车型预估数据中获取拼车预估数据
     *
     * @return array
     */
    private function _getCarpoolEstimateData() {
        $aProductList = $this->_oRuntimeData->oAnycarEstimateViewModel->getProductList();
        foreach ($aProductList as $aProductItem) {
            $sGroupKey = AnyCarCommonLogic::getGroupKey($aProductItem);
            if (AnyCarCommonLogic::PRODUCT_KEY_CARPOOL == $sGroupKey) {
                return $aProductItem;
            }
        }

        return [];
    }

    /**
     * 判别&设置自定义品类title
     *
     * @param array $aOrderInfo           orderInfo
     * @param array $aEstimateProductList aEstimateProductList
     * @param array $aTargetProductSourceData sourceData
     *
     * @return array $aEstimateProductList
     */
    private function _judgeAndSetProductCustomTitle($aOrderInfo, $aEstimateProductList, $aTargetProductSourceData = []) {
        if (empty($aOrderInfo) || empty($aEstimateProductList)) {
            return $aEstimateProductList;
        }

        $bIsLineup = false;
        // 挽留弹窗拉齐和等待应答追加车型列表的ets，需用追加车型的是否排队信息
        foreach ($this->_aGuideItem['multi_require_product'] as $aProductItem) {
            if (self::ANIMATION_SCENE_QUEUE == $aProductItem['scene_flag']) {
                $bIsLineup = true;
                break;
            }
        }

        $oProductCategory = new ProductCategory();

        foreach ($aEstimateProductList as &$aProductItem) {
            $aProductCategoryMap = [
                'business_id'   => $aProductItem['business_id'],
                'require_level' => $aProductItem['require_level'],
                'combo_type'    => $aProductItem['combo_type'],
            ];

            if (!empty($aProductItem['product_category']) && ProductCategory::PRODUCT_CATEGORY_UNIONE_SPECIAL_PRICE == empty($aProductItem['product_category'])) {
                $iProductCategory = $aProductItem['product_category'];
            } else {
                $iProductCategory = $oProductCategory->getProductCategory($aProductCategoryMap);
            }

            $oProductCategoryTitleCustomLogic = new ProductCategoryTitleCustomizeLogic();
            $bProductCategoryCustomOpened     = $oProductCategoryTitleCustomLogic->isProductCategoryCustomTitleOpened(
                [
                    'passenger_id'  => $aOrderInfo['passenger_id'],
                    'city'          => $aOrderInfo['area'],
                    'phone'         => $aOrderInfo['passenger_phone'],
                    'category'      => $iProductCategory,
                    'access_key_id' => $this->_oRuntimeData->getRequest()['access_key_id'],
                    'lang'          => $this->_oRuntimeData->getRequest()['lang'],
                ]
            );

            if ($bProductCategoryCustomOpened) {
                $aCustomProduct = $oProductCategoryTitleCustomLogic->getProductCategoryCustomTitle($aOrderInfo['area'],$iProductCategory);
                if (!empty($aCustomProduct['intro_msg'])) {
                    $aProductItem['business_name'] = $aCustomProduct['intro_msg'];
                    $aProductItem['car_title']     = $aCustomProduct['intro_msg'];
                }
            }

            // 拼车非排队场景实验
            $sCarpoolEtp = '';
            if (self::judgeCarpoolQueueExperimentText($aProductItem,$aTargetProductSourceData)) {
                $sCarpoolEtp = self::_buildCarpoolQueueExperimentText($aProductItem);
            }

            if (!empty($sCarpoolEtp)) {
                $aProductItem['etp_str'] = $sCarpoolEtp;
                continue;
            }

            if (\BizCommon\Utils\Horae::isMiniBusCarpool($aProductItem)) {
                continue;
            }

            if ($this->_oEtp2etsSwitch->allow() && MatchResultRuntime::EXP_CONTROL_GROUP != $this->_oRuntimeData->iExperimentalGroup) {
                $iUndertakeEts    = Etp2EtsSceneProcess::getInstance()->getUndertakeEts($aProductItem);
                $sConfigKey       = $this->_oEtp2etsSwitch->getParameter('config_product_list_key','etp_to_ets_experiment_condition_config');
                $aConditionConfig = Config::getBizConfig($sConfigKey,'condition_config')[(string)($this->_oRuntimeData->iExperimentalGroup)];
                $aCondCfg         = $aConditionConfig['condition_config'];
                $sDocKey          = $aConditionConfig['doc_key'];
                $iEts = empty($aProductItem['extra_info']['ets']) ? $iUndertakeEts : $aProductItem['extra_info']['ets'];
                $aProductItem['etp_str'] = $this->_getEtsStr($iEts,$aCondCfg,$sDocKey,$aProductItem,$bIsLineup);
                continue;
            }

            // 如果athena没有下发该车型的etp，或者etp为0，就不下发到端上展示
            $sEtp = $aProductItem['etp'];
            if (!empty($sEtp) && '0' != $sEtp) {
                $aProductItem['etp_str'] = $this->_getText('etp_str', ['etp' => $sEtp]);
            }
        }

        return $aEstimateProductList;
    }

    /**
     * 获取拼车排队实验文案
     * @param array $aProductItem productItem
     * @return mixed|string
     */
    private function _buildCarpoolQueueExperimentText($aProductItem) {
        $sText = $this->_oRuntimeData->oTextService->getText('guide_append_ayncar_carpool');
        $aText = json_decode($sText, true) ?: [];
        if (empty($aText) || empty($aText['carpool_pre_cancel_queue_experiment'])) {
            return '';
        }

        $aText            = $aText['carpool_pre_cancel_queue_experiment'];
        $sExText          = $aText['default'];
        $iProductCategory = $aProductItem['product_category'] ?? -1;
        if (!empty($aText[(string)$iProductCategory])) {
            $sExText = $aText[(string)$iProductCategory];
        }

        return $sExText;
    }

    /**
     * 判断是否命中拼车不排队实验
     * @param array $aProductItem             productItem
     * @param array $aTargetProductSourceData sourceData
     * @return bool
     */
    public function judgeCarpoolQueueExperimentText($aProductItem, $aTargetProductSourceData) {

        // 只对站点拼车做实验
        if (empty($aTargetProductSourceData)
            || !isset($aProductItem['product_category'])
            || ProductCategory::PRODUCT_CATEGORY_CARPOOL_STATION != $aProductItem['product_category']
            || empty($aTargetProductSourceData[$aProductItem['product_category']])
        ) {
            return false;
        }

        // 最新ets数据都在extra_info下,判断ets及是否快车排队
        $aCarpoolEtsSource = $aTargetProductSourceData[$aProductItem['product_category']];
        $sEts = $aCarpoolEtsSource['extra_info']['ets'] ?? null;
        if (empty($sEts)) {
            return false;
        }

        $iEts = (int)$sEts;

        $aOrderInfo = $this->_oRuntimeData->getOrderInfo();
        // 获取实验名称，实验可能随时变动，但实验内容条件不可随便更改，否则可能处理实验逻辑会有问题。
        $oApollo = new \Nuwa\ApolloSDK\Apollo();
        $oToggle = $oApollo->featureToggle(
            'carpool_queue_experiment_name',
            [
                'key'              => $aOrderInfo['passenger_id'],
                'pid'              => $aOrderInfo['passenger_id'],
                'city'             => $aOrderInfo['area'],
                'phone'            => $aOrderInfo['passenger_phone'],
                'product_category' => $aProductItem['product_category'] ?? -1,
                'business_id'      => $aProductItem['business_id'] ?? -1,
                'require_level'    => $aProductItem['require_level'] ?? -1,
                'combo_type'       => $aProductItem['combo_type'] ?? -1,
                'scene_flag'       => $aProductItem['scene_flag'] ?? -1,
                'caller_func'      => 'pre_cancel',
            ]
        );
        if (!$oToggle->allow() || empty($sAbExName = $oToggle->getParameter('ab_name',''))) {
            return false;
        }

        $oExFeatureToggle = $oApollo->featureToggle(
            $sAbExName,
            [
                'key'              => $aOrderInfo['passenger_id'],
                'pid'              => $aOrderInfo['passenger_id'],
                'city'             => $aOrderInfo['area'],
                'phone'            => $aOrderInfo['passenger_phone'],
                'product_category' => $aProductItem['product_category'],
                'business_id'      => $aProductItem['business_id'],
                'require_level'    => $aProductItem['require_level'],
                'combo_type'       => $aProductItem['combo_type'],
                'scene_flag'       => $aProductItem['scene_flag'],
            ]
        );

        // 未命中实验
        if (!$oExFeatureToggle->allow() || 'control_group' == $oExFeatureToggle->getGroupName()) {
            return false;
        }

        // 如果实验都通过，但是快车没命中排队，不做实验
        if (!$this->isFastCarInLine($aProductItem)) {
            return false;
        }

        // 默认10分钟
        $iDemarcationSeconds = $oExFeatureToggle->getParameter('demarcation_seconds',600);

        if ($iEts <= $iDemarcationSeconds) {
            return false;
        }

        return true;
    }

    /** 等待应答V3，拼车不排队时，查询快车的排队信息，快车不排队时显示ETP，快车排队时显示拼成优先派车
     * @param array $aProductItem 导流推荐的车型数据
     * @return bool
     */
    public function isFastCarInLine($aProductItem) {

        if (!$this->_oRuntimeData->isIterationSwitchV3()) {
            return false;
        }

        if (Horae::TYPE_COMBO_CARPOOL != $aProductItem['combo_type']) {
            return false;
        }

        $oMartiniRepository = new MartiniRepository();
        return $oMartiniRepository->checkFastCarIsLineUp($this->_oRuntimeData->getOrderInfo());
    }

    /**
     * 根据条件配置和文案key获取ets文案
     * @param int    $iEts         预期ets
     * @param array  $aCondCfg     条件配置
     * @param string $sDocKey      文案配置
     * @param array  $aProductItem 车型信息
     * @param bool   $bIslineup    是否排队
     * @return mixed|string
     */
    private function _getEtsStr($iEts, $aCondCfg, $sDocKey, $aProductItem = array(), $bIslineup = false) {
        if (!empty($aProductItem) && TripcloudProduct::isTripcloudByBusinessID($aProductItem['business_id'])) {
            return '';
        }

        if (empty($iEts)) {
            //兜底逻辑
            return Language::getDecodedTextFromDcmp('pre_cancel-cancel_stay_popup_ets')['undertake_text'];
        }


        //弹窗的排队信息ranking和排队场景信息scene_flag放在extra info中 不为空且为int的时候读
        if (empty($aProductItem['ranking']) && !empty($aProductItem['extra_info']['ranking'])
            && is_numeric($aProductItem['extra_info']['ranking']) && !strpos($aProductItem['extra_info']['ranking'],'.')
        ) {
            $aProductItem['ranking'] = (int)$aProductItem['extra_info']['ranking'];
        }

        if (empty($aProductItem['scene_flag']) && !empty($aProductItem['extra_info']['scene_flag'])
            && is_numeric($aProductItem['extra_info']['scene_flag']) && !strpos($aProductItem['extra_info']['scene_flag'],'.')
        ) {
            $aProductItem['scene_flag'] = (int)$aProductItem['extra_info']['scene_flag'];
        }

        return Etp2EtsSceneProcess::getInstance()->getEtsStr($iEts,$aCondCfg,$sDocKey,$aProductItem,(int)($bIslineup));
    }

    /**
     * 填充预估数据
     * @param array $aTargetProduct 推荐的车型数据
     * @return array
     */
    private function _updateProduct($aTargetProduct) {
        // 获取预估数据
        $this->_oRuntimeData->oAnycarEstimateViewModel->buildOnce();
        $aProductList = $this->_oRuntimeData->oAnycarEstimateViewModel->getProductList();
        if (empty($aProductList)) {
            return $aTargetProduct;
        }

        $sEtp = $aTargetProduct['extra_info']['etp'];
        $iProductCategoryIdInTargetProduct = $aTargetProduct['product_category'];
        foreach ($aProductList as $aEstimateItem) {
            $iProductCategoryIdInEstimateItem = $aEstimateItem['product_category'];
            if ($iProductCategoryIdInTargetProduct != $iProductCategoryIdInEstimateItem) {
                continue;
            }

            //防止extra info被覆盖
            $aEstimateExtraInfo = array_merge($aTargetProduct['extra_info'], $aEstimateItem['extra_info']);
            // 当前产品的预估数据
            $aTargetProduct = array_merge($aTargetProduct, $aEstimateItem);
            // 默认需要勾选中
            $aTargetProduct['is_selected'] = 1;
            $aTargetProduct['extra_info'] = $aEstimateExtraInfo;

            if (!empty($aTargetProduct['side_extra']) && !empty($aTargetProduct['side_extra']['right_sub_title']) && !empty($aTargetProduct['side_extra']['right_sub_title']['disabled'])) {
                $aTargetProduct['side_extra']['right_sub_title'] = null;
            }
        }

        $aTargetProduct['etp'] = $sEtp;
        return $aTargetProduct;
    }

    /**
     * 弹窗na逻辑，盒子从layout取car_icon,非盒子从estimate_data获取car_icon，
     * 由于estimate_data从预估获取，等待应答展示盒子，故estimate_data中car_icon为空，导致弹窗不展示车标
     * @param object $oEstimateInfo oEstimateInfo
     * @return void
     */
    private function _hackBoxCarIcon($oEstimateInfo) {
        $aTargetEstimateData = $oEstimateInfo->aEstimateData;
        $aLayout = $oEstimateInfo->aLayout;
        foreach ($aLayout as $item) {
            if (1 == $item['groups'][0]['type']) {
                $aProducts = $item['groups'][0]['products'];
                foreach ($aProducts as $iProductcategory) {
                    if (empty($aTargetEstimateData[$iProductcategory]['car_icon'])) {
                        $aTargetEstimateData[$iProductcategory]['car_icon'] = $item['groups'][0]['car_icon'];
                    }
                }
            }
        }

        $oEstimateInfo->aEstimateData = $aTargetEstimateData;
    }

    /**
     * @param  array $aEstimateProductList aEstimateProductList
     * @return bool|void
     */
    private function _hackTaxiDoublePriceV3(&$aEstimateProductList) {
        $aRequest   = $this->_oRuntimeData->getRequest();
        $aOrderInfo = $this->_oRuntimeData->getOrderInfo();
        $apolloUser = [
            'key'           => UtilHelper::getUidByPassengerId($aOrderInfo['passenger_id'], \BizLib\Constants\Common::BIZ_TYPE_PASSENGER),
            'lang'          => $aRequest['lang'],
            'phone'         => $aOrderInfo['passenger_phone'],
            'city'          => $aOrderInfo['area'],
            'access_key_id' => $aRequest['access_key_id'],
            'app_version'   => $aRequest['appversion'],
            'county_id'     => $aOrderInfo['county'],
            'order_type'    => $aOrderInfo['type'],
            'pid'           => $aOrderInfo['passenger_id'],
        ];

        $apolloParams = array_merge(
            $apolloUser,
            [
                'guide_pos' => GuideFactory::GUIDE_TYPE_ANYCAR,
                'event'     => '_hackTaxiDoublePriceV3',
                'module'    => MODULE_NAME,
            ]
        );
        $oToggle      = \Xiaoju\Apollo\Apollo::getInstance()->featureToggle('taxi_double_price_offline', $apolloParams);
        if ($oToggle->allow()) {
         // 命中出租车盒子，需要替换盒子的 car_title、car_icon
            if (TpForm::unionIsShowBox($apolloUser)) {
                $aConfig = explode(',', $oToggle->getParameter('allow_product_category', ''));
                foreach ($aConfig as $iId) {
                    if (!empty($aEstimateProductList[$iId]['extra_info']['origin_icon'])) {
                        $aEstimateProductList[$iId]['car_icon'] = $aEstimateProductList[$iId]['extra_info']['origin_icon'];
                    }

                    if (ProductCategory::PRODUCT_CATEGORY_TAXI_MARKETISATION_PUTONG == $iId) {
                        $aEstimateProductList[$iId]['car_title'] = $this->_getText('taxi_online_price_car_name');
                    }

                    if (ProductCategory::PRODUCT_CATEGORY_UNIONE == $iId) {
                        $aEstimateProductList[$iId]['car_title'] = $this->_getText('taxi_offline_price_car_name');
                    }

                    $sBoxCarTitle = $this->_oRuntimeData->oAnycarEstimateViewModel->getAnycarEstimateBoxCarTitleByCategory(
                        (string)
                        $iId,
                        self::TAXI_BOX_GROUP_ID
                    );
                    if (!empty($sBoxCarTitle)) {
                        $aEstimateProductList[$iId]['car_title'] = $sBoxCarTitle;
                    }
                }
            }

            return;
        }

        $aEstimateData = $this->_oRuntimeData->oAnycarEstimateViewModel->aAnycarEstimate['estimate_data'];

        if (empty($aEstimateProductList['7']) || empty($aEstimateData['188'])) {
            return;
        }

        $sProductName = $this->_getText('taxi_double_price_car_name');
        TpForm::dealUnionDoublePriceProductName($sProductName,$apolloUser);
        $aEstimateProductList['7']['car_title']     = $sProductName;
        $aEstimateProductList['7']['business_name'] = $sProductName;
        if (!TpForm::unionIsShowRangePriceBox($apolloUser) && !TpForm::unionIsReplaceName($apolloUser)) {
            $aEstimateProductList['7']['fee_amount'] = $aEstimateData['188']['fee_amount'];
            $aEstimateProductList['7']['fee_msg']    = $aEstimateData['188']['fee_msg'];
            $aEstimateProductList['7']['price']      = $aEstimateData['188']['price'];
            $aEstimateProductList['7']['dynamic_total_fee'] = $aEstimateData['188']['dynamic_total_fee'];
            $aEstimateProductList['7']['fee_desc_list']     = $aEstimateData['188']['fee_desc_list'];

            $aEstimateProductList['7']['car_icon'] = $this->_getText('taxi_double_price_car_icon');
        }
    }
}
