<?php

namespace PreSale\Models\guideCardView\guideView;

use BizLib\Utils\Language;
use PreSale\Models\guideCardItem\MatchResultRuntime;

/**
 * BaseViewRender
 */
abstract class BaseViewRender
{
    const HT_HIT   = 1;//当前状态命中该场景
    const HT_KEEP  = 2;//当前状态未命中，但如果现在端上已经在展示之前命中过的卡片，那还可以继续展示
    const HT_NOHIT = 3;//当前状态未命中，如果端上已经在展示之前命中过的卡片，也不再继续展示

    /**
     * card_id为每种沟通卡片唯一标识，正常情况在new一个cardModel时自动生成。
     * 对于端来说，当card_id变化时，会将上一个卡片隐去，再上显出本次卡片。
     * 但在特殊情况下，业务需要在卡片内容变化后，只要刷新卡片，而不要下隐上显卡片，就可以通过set该值为某固定常量来实现。
     * p.s. 目前的逻辑有点绕，我们内部在渲染完卡片后genSig生成了一个唯一hash，当hash值相同时就会把端上传下来的card_id对应的缓存再返给端上，
     * 从而实现相同内容的卡片保持card_id不变
     */
    const CARD_ID_APPEND_CARPOOL       = 1; // 追加拼车卡片
    const CARD_ID_LOCK_CAR_RECOMMEND   = 2; // 锁车推荐卡片
    const CARD_ID_MULTI_APPEND_CARPOOL = 3; // 推荐追加多车型包含拼车卡片

    // 推荐类型
    const REC_TYPE_APPEND_ANYCAR       = 1; // 追加车型（非拼
    const REC_TYPE_RELATION_PASSENGER  = 2; // 拼友
    const REC_TYPE_CARPOOL_CAR         = 3; // 载人车
    const REC_TYPE_MEMBER_PRIVILEGE    = 4; // 会员权益 快速通道
    const REC_TYPE_GUIDE_FARTHER       = 5; //加价调度
    const REC_TYPE_LOCK_CAR            = 6; //锁车
    const REC_TYPE_MEMBER_APPEND_APLUS = 7; // 会员追加特快
    const REC_TYPE_APPEND_MULTIPLE_RECOMMEND_ANYCAR = 8; // 追加车型（多车型推荐）
    const REC_TYPE_COMPENSATION_SUCCESS = 9; // 无车赔赔付成功
    const REC_TYPE_UPGRADE    = 10; // 升舱
    const REC_TYPE_COUPON  = 11; // 临期优惠券
    const REC_TYPE_QUEUE = 12; // 排队位次
    const REC_TYPE_ETS  = 13; // 等待时间临近ETS
    const REC_TYPE_MINI_BUS = 14; // 单勾市内小巴
    const REC_TYPE_SMART_BUS = 15; // 单勾智能小巴
    const REC_TYPE_FAST_RANGE = 16; // 单勾惠选车
    const REC_TYPE_DEFAULT = 99; // 兜底

    /**
     * @var MatchResultRuntime
     */
    protected $_oRuntimeData;

    protected $_aText = [];

    /**
     * @param MatchResultRuntime $oRuntimeData 运行时数据总线
     */
    public function __construct($oRuntimeData) {
        $this->_oRuntimeData = $oRuntimeData;
    }

    /**
     * 前置操作
     * @return void
     */
    public function prepareViewModel() {

    }

    /**
     * @return mixed
     */
    public function execute() {
        return [];
    }

    /**
     * @return array|mixed|null
     */
    public function doRender() {
        $this->prepareViewModel();
        $oView = $this->execute();
        if (is_array($oView) && empty($oView)) {
            // 端希望返回null..
            $oView = null;
        }

        return $oView;
    }

    /**
     * @return int
     */
    abstract public function getRecommendHitType();

    /**
     * _getText
     *
     * @param string $sKey        文案key
     * @param array  $aReplaceTag 替换变量
     * @return string|array
     */
    protected function _getText($sKey, $aReplaceTag = []) {
        $textFormat = $sText = $this->_aText[$sKey] ?? '';
        if (!empty($aReplaceTag)) {
            if (is_array($textFormat)) {
                $sText = json_encode($textFormat);
            }

            $sText = Language::replaceTag($sText, $aReplaceTag);
            if (is_array($textFormat)) {
                $sText = json_decode($sText,true);
            }
        }

        return $sText;
    }
}
