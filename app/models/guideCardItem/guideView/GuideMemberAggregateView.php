<?php

namespace PreSale\Models\guideCardView\guideView;

use PreSale\Models\guideCardItem\GuideFactory;
use PreSale\Models\guideCardItem\MatchResultRuntime;
use PreSale\Models\guideCardView\guideCard\BaseGuideCard;

/**
 * 会员聚合出口
 */
class GuideMemberAggregateView extends BaseViewRender
{

    private $_aRecommendResult;
    private $_aEmergencyRecommend;
    private $_bContainsAddAplus = false;
    private $_oProxyView;

    /**
     * @param MatchResultRuntime $oRuntimeData     运行时数据总线
     * @param array              $aRecommendResult 导流推荐
     */
    public function __construct($oRuntimeData, $aRecommendResult) {
        parent::__construct($oRuntimeData);
        $this->_aRecommendResult = $aRecommendResult;
    }

    /**
     * @return int
     */
    public function getRecommendHitType() {
        if (GuideFactory::GUIDE_TYPE_GUIDE_MEMBER_AGGREGATE == $this->_aRecommendResult['type']) {
            $aEmergencyRecommend = [];
            foreach ($this->_aRecommendResult['emergency_recommend'] as $aItem) {
                // 先check追加特快是否可出
                if (GuideFactory::GUIDE_TYPE_MEMBER_ADD_A_PLUS == $aItem['type']) {
                    $this->_oRuntimeData->oSpsModel->buildOnce();
                    // sps费用小于0代表有抵扣，才会出出口；否则用权益更亏，就不出该出口
                    if ($this->_oRuntimeData->oSpsModel->getSpsFee() < 0) {
                        $aEmergencyRecommend[]    = $aItem['type'];
                        $this->_bContainsAddAplus = true;
                    }
                } else {
                    $aEmergencyRecommend[] = $aItem['type'];
                }
            }

            // 过滤后没有推荐，不渲染
            if (empty($aEmergencyRecommend)) {
                return self::HT_NOHIT;
            }

            $this->_aEmergencyRecommend = $aEmergencyRecommend;
            // 快/专的快速通道连接到原有逻辑上 proxy
            if ([GuideFactory::GUIDE_TYPE_FASTWAY] == $aEmergencyRecommend) {
                $this->_oProxyView = new GuideFastWayExpressView($this->_oRuntimeData, $this->_aRecommendResult, true);
                return $this->_oProxyView->getRecommendHitType();
            } elseif ([GuideFactory::GUIDE_TYPE_FASTWAY_COUPON] == $aEmergencyRecommend) {
                $this->_oProxyView = new GuideFastWayPremiumView($this->_oRuntimeData, $this->_aRecommendResult, true);
                return $this->_oProxyView->getRecommendHitType();
            } elseif ($this->_bContainsAddAplus) {
                $this->_oProxyView = new GuideMemberAddAPlusView($this->_oRuntimeData, $this->_aRecommendResult, true);
                return  $this->_oProxyView->getRecommendHitType();
            }
        }

        return self::HT_NOHIT;
    }

    /**
     * @return BaseGuideCard|null
     */
    public function execute() {
        // 快\专 快速通道链到原有逻辑
        if (!empty($this->_oProxyView)) {
            return $this->_oProxyView->doRender();
        }

        // 聚合展示 todo 一期暂时hold
        return null;
    }
}
