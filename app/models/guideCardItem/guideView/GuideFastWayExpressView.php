<?php

namespace PreSale\Models\guideCardView\guideView;

use BizCommon\Logics\Anycar\AnyCarCommonLogic;
use BizLib\Log;
use BizLib\Utils\Language;
use BizLib\Utils\Passport;
use BizLib\Utils\Product;
use BizLib\Utils\ProductCategory;
use BizLib\Utils\UtilHelper;
use PreSale\Models\guideCardItem\biz\GuideViewBiz;
use PreSale\Models\guideCardItem\button\GuideCardStyleManage;
use PreSale\Models\guideCardItem\button\OmniButton;
use PreSale\Models\guideCardItem\GuideFactory;
use PreSale\Models\guideCardItem\MatchResultRuntime;
use PreSale\Models\guideCardItem\MemberViewModel;
use PreSale\Models\guideCardItem\OmniOmegaInfo;
use PreSale\Models\guideCardView\guideCard\BaseGuideCard;
use PreSale\Models\guideCardView\guideCard\ElementMemberCard;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TpForm;

/**
 * GuideFastWayExpressView 快速通道
 */
class GuideFastWayExpressView extends BaseViewRender
{
    private $_aRecommendResult;
    private $_bAggregate = false;

    /**
     * @param MatchResultRuntime $oRuntimeData     运行时数据总线
     * @param array              $aRecommendResult 导流推荐数据
     * @param bool               $bAggregate       bAggregate
     */
    public function __construct($oRuntimeData, $aRecommendResult, $bAggregate = false) {
        parent::__construct($oRuntimeData);
        $this->_aRecommendResult = $aRecommendResult;
        $this->_bAggregate       = $bAggregate;
    }

    /**
     * @return void
     */
    public function prepareViewModel() {
        $this->_oRuntimeData->oAnycarEstimateViewModel->buildOnce();
    }

    /**
     * @return int
     */
    public function getRecommendHitType() {
        if (GuideFactory::GUIDE_TYPE_FASTWAY == $this->_aRecommendResult['type'] || $this->_bAggregate) {
            return self::HT_HIT;
        }

        return self::HT_NOHIT;
    }

    /**
     * @return BaseGuideCard
     */
    public function execute() {
        $aGuideItem = $this->_aRecommendResult;

        //如果是新增的多勾发单出推荐快车快速通道
        if (!$this->_oRuntimeData->bIsSingleCar) {
            $sGroupKey    = AnyCarCommonLogic::getGroupKey($aGuideItem['target_product']);
            $oMemberModel = $this->_oRuntimeData->oMultiMember;

            $aMultiMemberInfo         = $oMemberModel->getMultiMemberInfo();
            $aMemberPrivilegeNewOrder = $aMultiMemberInfo[$sGroupKey] ?? [];
            $aMemberPrivFastWay       = $aMemberPrivilegeNewOrder['member_privilege_fast_way'];
        } else {
            $oMemberModel       = $this->_oRuntimeData->oMember;
            $aMemberPrivFastWay = $oMemberModel->getPrivilegeByType('fast_way', true);
        }

        $aText = Language::getDecodedTextFromDcmp('pre_cancel-fast_way_member_guide_v2');

        // 剩余次数
        $sRemainTimes = GuideViewBiz::getRemainTimesText($aMemberPrivFastWay['remain_times']);
        $sTitleMarker = Language::replaceTag($aText['title_marker'], ['remain_times' => $sRemainTimes]);
        self::_tpReplaceBusinessName($sTitleMarker,$aText,$sRemainTimes);
        $aButtonParams = [
            'guide_pos' => GuideFactory::GUIDE_TYPE_FASTWAY,
        ];
        if (!$this->_oRuntimeData->bIsSingleCar) {
            $aButtonParams['group_key'] = $sGroupKey;
        }

        $oCard = new ElementMemberCard();
        $oCard->setSTitle($aText['title'])
            ->addTitleMarkerText($sTitleMarker)
            ->setIGuidePos($this->_aRecommendResult['type'])
            ->setAParams($aButtonParams);

        if (!empty($aGuideItem['extra_info']['wait_time_saved'])) {
            $oCard->setASubtitle([GuideViewBiz::getEstimateAdvanceTime($aGuideItem['extra_info']['wait_time_saved'])])
                ->setIWaitTimeSaved($aGuideItem['extra_info']['wait_time_saved']);
        }

        list($iMemberType, $iMemberLevel) = MemberViewModel::getMemberTypeAndLevel($aMemberPrivFastWay, $this->_oRuntimeData);
        $oCard->getOMemberModel()->addMemberInfo($iMemberType, $iMemberLevel);
        GuideViewBiz::getMemberTitleMarker($oCard,$iMemberType);
        // 处理按钮
        $oButton = new OmniButton();
        $oButton->setText($aText['button_text']);
        $oButton->setActionType(OmniButton::ACTION_TYPE_UPDATE_ORDER);
        $oButton->setAButtonParams($aButtonParams);
        $oButton->setButtonStyle(GuideCardStyleManage::getDefaultButtonStyle($this->_oRuntimeData->iIsWaitAnswerUpgrade));
        // 处理按钮点击埋点
        $sOmegaKey  = OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_RECOMMEND_CK, $this->_oRuntimeData->getRequest()['access_key_id']);
        $aOrderInfo = $this->_oRuntimeData->getOrderInfo();
        $oOmegaInfo = new OmniOmegaInfo();
        $oOmegaInfo->setSKey($sOmegaKey);
        $oOmegaInfo->setAParams(
            [
                'order_id'     => $aOrderInfo['order_id'],
                'uid'          => UtilHelper::getUidByPassengerId($aOrderInfo['passenger_id'], Passport::PASSENGER_ROLE),
                'city_id'      => $aOrderInfo['area'],
                'rec_type'     => BaseViewRender::REC_TYPE_MEMBER_PRIVILEGE,
                'is_recommend' => 1,
            ]
        );
        $oButton->setOmegaInfo($oOmegaInfo);
        $oCard->setOButton($oButton);
        $oCard->setIRecType(BaseViewRender::REC_TYPE_MEMBER_PRIVILEGE);
        return $oCard;
    }

    /**
     * @param string $sTitleMarker sTitleMarker
     * @param array  $aText        aText
     * @param string $sRemainTimes sRemainTimes
     * @return void
     */
    private function _tpReplaceBusinessName(&$sTitleMarker, $aText, $sRemainTimes) {
        if (!$this->_oRuntimeData->isIterationSwitchV3()) {
            return;
        }

        $aOrderInfo     = $this->_oRuntimeData->getOrderInfo();
        $iOrderId       = $aOrderInfo['order_id'];
        $aExtendFeature = json_decode($aOrderInfo['extend_feature'], true) ?? [];
        $aMultiRequireProduct = $aExtendFeature['multi_require_product'];
        $iProductId           = -1;
        $iProductCategory     = -1;
        foreach ($aMultiRequireProduct as $aEstimateInfo) {
            $iProductId = Product::getProductIdByBusinessId($aEstimateInfo['business_id']);
            if (ProductCategory::PRODUCT_CATEGORY_FAST == $aEstimateInfo['product_category']) {
                $iProductCategory = $aEstimateInfo['product_category'];
                break;
            }
        }

        //$iUserId      = UtilHelper::getUidByPassengerId($aOrderInfo['passenger_id'], \BizLib\Constants\Common::BIZ_TYPE_PASSENGER);
        $sBusinessName = TpForm::getTpLabelTextFromDos($aOrderInfo,$iProductCategory);
        if (!empty($sBusinessName)) {
            $sTitleMarker = Language::replaceTag($aText['tp_title_marker'], ['business_name' => $sBusinessName, 'remain_times' => $sRemainTimes]);
        }
    }
}
