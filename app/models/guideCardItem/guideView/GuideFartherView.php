<?php

namespace PreSale\Models\guideCardView\guideView;

use BizCommon\Logics\Anycar\AnyCarCommonLogic;
use BizLib\Utils\Language;
use BizLib\Utils\Passport;
use BizLib\Utils\UtilHelper;
use PreSale\Models\guideCardItem\button\OmniButton;
use PreSale\Models\guideCardItem\guideCard\GuideFatherCardV2;
use PreSale\Models\guideCardItem\GuideFactory;
use PreSale\Models\guideCardItem\MatchResultRuntime;
use PreSale\Models\guideCardItem\OmniOmegaInfo;
use PreSale\Models\guideCardView\guideCard\BaseGuideCard;
use PreSale\Models\guideCardView\guideCard\GuideFartherCard;
use PreSale\Models\guideCardItem\button\GuideCardStyleManage;

/**
 * 加价调度
 */
class GuideFartherView extends BaseViewRender
{
    private $_aOrderMatchRecommendResult;

    // 命中新加多勾发单返回支持单品类场景
    private $_bIsAnycarRecom = false;

    /**
     * @param MatchResultRuntime $oRuntimeData               运行时数据总线
     * @param array              $aOrderMatchRecommendResult 导流推荐数据
     */
    public function __construct($oRuntimeData, $aOrderMatchRecommendResult) {
        parent::__construct($oRuntimeData);
        $this->_aOrderMatchRecommendResult = $aOrderMatchRecommendResult;
        $oTextService = $oRuntimeData->oTextService;
        $sText        = $oTextService->getText('guide_farther_v6_iteration');
        if ($oRuntimeData->bNewPopExam) {
            $sText        = $oTextService->getText('guide_farther_v6_iteration_v2');
        }
        $this->_aText = json_decode($sText, true);
    }

    /**
     * @return int
     */
    public function getRecommendHitType() {
        if (GuideFactory::GUIDE_TYPE_FARTHER == $this->_aOrderMatchRecommendResult['type']) {
            return self::HT_HIT;
        }

        //athena现支持多勾发单也返回一个推荐的加价调度车型
        if (GuideFactory::GUIDE_TYPE_FARTHER_FOR_ANYCAR == $this->_aOrderMatchRecommendResult['type']) {
            $this->_bIsAnycarRecom = true;
            return self::HT_HIT;
        }

        return self::HT_NOHIT;
    }

    /**
     * @return BaseGuideCard|null
     */
    public function execute() {
        $oCard            = new GuideFartherCard();
        $aRecommendResult = $this->_aOrderMatchRecommendResult;
        if (empty($aRecommendResult)) {
            return null;
        }
        if ($this->_oRuntimeData->bNewPopExam) {
            $oCard  = new GuideFatherCardV2();
            $oCard->setTitleImg($this->_getText('title_img'));
        }

        $aExtraInfo = $aRecommendResult['extra_info'];
        $oButton    = new OmniButton();

        if ($this->_bIsAnycarRecom) {
            $aCarNameList = json_decode(Language::getTextFromDcmp('config_anycar-anycar_simple_name'), true);
            $sCarName     = $aCarNameList[AnyCarCommonLogic::getGroupKey($aRecommendResult['target_product'])] ?? '';
            $oCard->setTitle($this->_getText('title_anycar', ['distance' => $aRecommendResult['etpDistance'], 'business_name' => $sCarName])); // 加价调度x公里内车辆
        } else {
            $oCard->setTitle($this->_getText('title', ['distance' => $aRecommendResult['etpDistance']])); // 加价调度x公里内车辆
        }

        $aSubtitle      = [];
        $sTimeSubtitle  = $this->_getTimeSubTitle($aRecommendResult);
        $sPriceSubTitle = $this->_getPriceSubTitle($aRecommendResult);
        $aSubtitle = $this->getASubtitle($sTimeSubtitle, $aSubtitle, $sPriceSubTitle);

        $oCard->setSubTitle($aSubtitle);
        $oButton->setText($this->_getText('button_text'));
        // 加价调度button
        $oButton->setActionType(OmniButton::ACTION_TYPE_UPDATE_ORDER);

        if ($this->_bIsAnycarRecom) {
            //多车型时，需要把athena传递给我们的group_key等信息通过56通道给带到pUpdateOrderInfo
            $sGroupKey            = AnyCarCommonLogic::getGroupKey($aRecommendResult['target_product']);
            $sMultiRequireProduct = [
                [
                    'group_key'    => $sGroupKey,
                    'max_distance' => $aRecommendResult['etpDistance'],
                ],
            ];
            $sActionParam = [
                'guide_pos'             => GuideFactory::GUIDE_TYPE_FARTHER_FOR_ANYCAR,
                'multi_require_product' => json_encode($sMultiRequireProduct),
            ];
        } else {
            $sActionParam = [
                'guide_pos'    => GuideFactory::GUIDE_TYPE_FARTHER,
                'max_distance' => $aRecommendResult['etpDistance'],
            ];
        }

        $oButton->setAButtonParams($sActionParam);
        $oButton->setButtonStyle(GuideCardStyleManage::getDefaultButtonStyle($this->_oRuntimeData->iIsWaitAnswerUpgrade));
        // 按钮点击埋点数据
        $aOrderInfo = $this->_oRuntimeData->getOrderInfo();
        $sOmegaKey  = OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_RECOMMEND_CK, $this->_oRuntimeData->getRequest()['access_key_id']);
        $oOmegaInfo = new OmniOmegaInfo();
        $oOmegaInfo->setSKey($sOmegaKey);
        $oOmegaInfo->setAParams(
            [
                'order_id'     => $aOrderInfo['order_id'],
                'uid'          => UtilHelper::getUidByPassengerId($aOrderInfo['passenger_id'], Passport::PASSENGER_ROLE),
                'city_id'      => $aOrderInfo['area'],
                'rec_type'     => BaseViewRender::REC_TYPE_GUIDE_FARTHER,
                'is_recommend' => 1,
            ]
        );
        $oButton->setOmegaInfo($oOmegaInfo);
        $oCard->setButton($oButton);
        $oCard->setIRecType(BaseViewRender::REC_TYPE_GUIDE_FARTHER);
        return $oCard;
    }

    /**
     * 获取时间的副标题 预计x分钟上车
     * @param array $aRecommendResult 导流数据
     * @return string
     */
    private function _getTimeSubTitle($aRecommendResult) {
        //extra_info ets信息在这里只有加价调度出口才会有，单车型加价调度type为2
        //ETS ETS优于ETP
        $aExtraInfo    = $aRecommendResult['extra_info'];
        $sTimeSubtitle = '';
        if (isset($aExtraInfo['extra_info']['ets']) && $aExtraInfo['extra_info']['ets'] > 0) {
            $iEts = $aExtraInfo['extra_info']['ets'];
            if ($iEts < 60) {
                //时间单位: 秒
                $sTimeSubtitle = $this->_getText('time_subtitle', ['ets_time' => $iEts]);
            } else {
                //时间单位: 分
                $sTimeSubtitle = $this->_getText('time_subtitle_min', ['ets_time' => round($iEts / 60)]);
            }
        } else {
            $sTimeSubtitle = $this->_getText('time_subtitle_min', ['ets_time' => $aRecommendResult['etp']]);
        }

        return $sTimeSubtitle;
    }

    /**
     * 获取金额的副标题， 预计加价x元
     * @param array $aRecommendResult 导流数据
     * @return string
     */
    private function _getPriceSubTitle($aRecommendResult) {
        $sPriceSubtitle = '';
        if (isset($aRecommendResult['add_money'])) {
            $sPriceSubtitle = $this->_getText('price_subtitle', ['amount' => $aRecommendResult['add_money']]);
        }

        return $sPriceSubtitle;
    }

    /**
     * @param string $sTimeSubtitle
     * @param array $aSubtitle
     * @param string $sPriceSubTitle
     * @return array
     */
    public function getASubtitle(string $sTimeSubtitle, array $aSubtitle, string $sPriceSubTitle): array
    {
        if ($this->_oRuntimeData->bNewPopExam) {
            if ('' != $sPriceSubTitle) {
                $aSubtitle[] = $sPriceSubTitle;
            }

            if ('' != $sTimeSubtitle) {
                $aSubtitle[] = $sTimeSubtitle;
            }
        } else {
            if ('' != $sTimeSubtitle) {
                $aSubtitle[] = $sTimeSubtitle;
            }

            if ('' != $sPriceSubTitle) {
                $aSubtitle[] = $sPriceSubTitle;
            }
        }
        return $aSubtitle;
    }
}
