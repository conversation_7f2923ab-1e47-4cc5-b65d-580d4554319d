<?php

namespace PreSale\Models\guideCardView\guideView;

use BizLib\Config as NuwaConfig;
use BizLib\Utils\Language;
use PPreCancelOrderController;
use PreSale\Logics\order\NewtonLogic;
use PreSale\Models\guideCardItem\GuideFactory;
use PreSale\Models\guideCardItem\MatchResultRuntime;
use PreSale\Models\guideCardView\guideCard\BaseGuideCard;
use PreSale\Models\guideCardView\guideCard\GuideAnycarV2Card;

/**
 * 追加车型推荐V2
 */
class GuideAnycarViewV2 extends BaseViewRender
{
    /**
     * 导流数据
     * @var array
     */
    private $_aOrderMatchRecommendResult;

    /**
     * athena预期信息
     * @var array
     */
    private $_aAthenaExpectInfo;

    /**
     * 命中单车型
     * @var bool
     */
    private $_bHitAnyCar;

    /**
     * 命中多车型
     * @var bool
     */
    private $_bHitMultiAnyCar;

    /**
     * @param MatchResultRuntime $oRuntimeData 运行时数据总线
     * @param array $aOrderMatchRecommendResult athena推荐
     * @param array $aGuideRecommendInfo athena推荐
     * @param array $aAthenaExpectInfo athena期望信息
     */
    public function __construct($oRuntimeData, $aOrderMatchRecommendResult, $aGuideRecommendInfo, $aAthenaExpectInfo) {
        parent::__construct($oRuntimeData);
        $this->_aOrderMatchRecommendResult = $aOrderMatchRecommendResult;
        $this->_aAthenaExpectInfo = $aAthenaExpectInfo;
    }

    /**
     * @return void
     */
    public function prepareViewModel() {
        $this->_oRuntimeData->oAnycarEstimateViewModel->buildOnce();
    }

    /**
     * @return int
     */
    public function getRecommendHitType() {
        // 如果命中预付发单，屏蔽追加车型
        if (NewtonLogic::judgeDesignatedNewOrder($this->_oRuntimeData)) {
            return self::HT_NOHIT;
        }

        // 如果没有缓存的预估数据 不渲染
        if (empty($this->_oRuntimeData->oAnycarEstimateViewModel->aAnycarEstimate)) {
            return self::HT_NOHIT;
        }

        if (GuideFactory::GUIDE_TYPE_ANYCAR != $this->_aOrderMatchRecommendResult['type']) {
            return self::HT_NOHIT;
        }

        // 之前的命中条件
        $bNotHitAnyCarOldLogic = !empty($this->_aOrderMatchRecommendResult['extra_info']) && !empty($this->_aOrderMatchRecommendResult['extra_info']['answer_rate_improve']);
        $this->_bHitAnyCar = !$this->_oRuntimeData->bAllowMultiAppendCar && !$bNotHitAnyCarOldLogic;

        // 之前的命中条件
        $bNotHitMultiAnyCarOldLogic = !$this->_oRuntimeData->isIterationSwitchV3() || (!empty($this->_aOrderMatchRecommendResult['extra_info']) && empty($this->_aOrderMatchRecommendResult['extra_info']['answer_rate_improve']));
        $this->_bHitMultiAnyCar = $this->_oRuntimeData->bAllowMultiAppendCar && !$bNotHitMultiAnyCarOldLogic;
        if (!$this->_bHitMultiAnyCar && !$this->_bHitAnyCar) {
            return self::HT_NOHIT;
        }

        // 没有车型
        $aTargetProduct = $this->_aOrderMatchRecommendResult['target_product'];
        $aTargetProductList = $this->_aOrderMatchRecommendResult['target_product_list'];
        if (empty($aTargetProduct) && empty($aTargetProductList)) {
            return self::HT_NOHIT;
        }

        return self::HT_HIT;
    }

    /**
     * 获取追加推荐出口卡片
     * @return BaseGuideCard
     */
    public function execute() {
        $aEstimateData = $this->_oRuntimeData->oAnycarEstimateViewModel->aAnycarEstimate['estimate_data'];
        $aRawEstimateData = $this->_oRuntimeData->oAnycarEstimateViewModel->aAnycarEstimate['raw_estimate_data'];
        $aTargetProduct = $this->_aOrderMatchRecommendResult['target_product'];
        $aTargetProductList = $this->_aOrderMatchRecommendResult['target_product_list'];
        // 单车型推荐
        if ($this->_bHitAnyCar && !empty($aTargetProduct)) {
            return $this->_buildAnyCarView($aTargetProduct, $aEstimateData, $aRawEstimateData);
        }

        // 单车型推荐
        if ($this->_bHitMultiAnyCar && !empty($aTargetProductList) && count($aTargetProductList) == 1) {
            return $this->_buildAnyCarView($aTargetProductList[0], $aEstimateData, $aRawEstimateData);
        }

        // 多车型推荐
        if ($this->_bHitMultiAnyCar && !empty($aTargetProductList)) {
            return $this->_buildMultiAnyCarView($aTargetProductList, $aEstimateData, $aRawEstimateData);
        }

        return null;
    }

    /**
     * 构建多车型卡片
     * @param array $aTargetProduct 推荐车型数据
     * @param array $aEstimateData 预估数据
     * @param array $aRawEstimateData 预估纯数据
     * @return GuideAnycarV2Card
     */
    private function _buildAnyCarView($aTargetProduct, $aEstimateData, $aRawEstimateData) {
        $oGuideCard = new GuideAnycarV2Card();
        // 没有取到文案
        $aText     = NuwaConfig::text('pre_cancel', 'anycar_view_v2');
        if (empty($aText)) {
            return null;
        }

        $aProductEtsMap = $this->_buildProductEtsMap();
        if (empty($aProductEtsMap)) {
            return null;
        }

        $iProductCategory = $aTargetProduct['product_category'];
        $fEstimateFee = $aRawEstimateData[$iProductCategory]['estimate_fee'];
        $sCarTitle = $aEstimateData[$iProductCategory]['car_title'];
        $sFeeDesc = Language::replaceTag($aText['fee_desc_single'], ['estimate_fee' => $fEstimateFee]);
        $sTitle = $aProductEtsMap[$iProductCategory] <= 0 ? $aText['default_title'] : Language::replaceTag($aText['title'], ['time' => ceil($aProductEtsMap[$iProductCategory] / 60)]);
        $sSubTitle = Language::replaceTag($aText['sub_title'], ['car_title_list' => $sCarTitle]);
        if (!empty($aEstimateData[$iProductCategory]['carpool_seat_module']) ||!empty($aEstimateData[$iProductCategory]['carpool_seat_list'])) {
            $iPoolSeat = empty($aEstimateData['seat_num']) ? 1 : $aEstimateData['seat_num'];
        }

        $aMultiProduct[] = [
            'require_level'    => $aEstimateData[$iProductCategory]['extra_map']['require_level'] ?? 0,
            'combo_type'       => $aEstimateData[$iProductCategory]['extra_map']['combo_type'] ?? 0,
            'business_id'      => $aEstimateData[$iProductCategory]['extra_map']['business_id'] ?? 0,
            'level_type'       => $aEstimateData[$iProductCategory]['extra_map']['level_type'] ?? 0,
            'carpool_type'     => $aEstimateData[$iProductCategory]['extra_map']['carpool_type'] ?? 0,
            'is_default_auth'  => $aEstimateData[$iProductCategory]['extra_map']['is_default_auth'] ?? 0,
            'estimate_id'      => $aEstimateData[$iProductCategory]['estimate_id'] ?? '',
            'product_category' => $iProductCategory,
        ];
        $aActionParams = [
            'guide_pos'             => GuideFactory::GUIDE_TYPE_ANYCAR,
            'source'                => PPreCancelOrderController::ATHENA_GUIDE_SOURCE_CANCEL_DETAINMENT,
            'estimate_trace_id'     => $this->_oRuntimeData->oAnycarEstimateViewModel->aAnycarEstimate['estimate_trace_id'],
            'multi_require_product' => json_encode($aMultiProduct),
            'pool_seat'             => empty($iPoolSeat) ? 1 : $iPoolSeat,
            'carpool_seat_num'      => empty($iPoolSeat) ? 1 : $iPoolSeat,
        ];
        $oGuideCard->setActionParams($aActionParams);
        $oGuideCard->setTitle($sTitle);
        $oGuideCard->setSubTitle($sSubTitle);
        $oGuideCard->setIcon($aText['icon']);
        $oGuideCard->setFeeDesc($sFeeDesc);
        $oGuideCard->setIRecType(BaseViewRender::REC_TYPE_APPEND_MULTIPLE_RECOMMEND_ANYCAR);
        $aOmegaParams = [
            'rec_type'         => $oGuideCard->getIRecType(),
            'product_category' => $iProductCategory,
        ];
        $oGuideCard->setOmegaParams($aOmegaParams);
        return $oGuideCard;
    }

    /**
     * 构建多车型卡片
     * @param array $aTargetProductList 推荐车型数据
     * @param array $aEstimateData 预估数据
     * @param array $aRawEstimateData 预估纯数据
     * @return GuideAnycarV2Card
     */
    private function _buildMultiAnyCarView($aTargetProductList, $aEstimateData, $aRawEstimateData) {
        $oGuideCard = new GuideAnycarV2Card();
        // 没有取到文案
        $aText     = NuwaConfig::text('pre_cancel', 'anycar_view_v2');
        if (empty($aText)) {
            return null;
        }

        $aProductEtsMap = $this->_buildProductEtsMap();
        if (empty($aProductEtsMap)) {
            return null;
        }

        $aEstimateFees = [];
        $aCarTitleList = [];
        $aProductEtsList = [];
        $aProductCategoryList = [];
        foreach ($aTargetProductList as $aTargetProduct) {
            $iProductCategory = $aTargetProduct['product_category'];
            $fEstimateFee = $aRawEstimateData[$iProductCategory]['estimate_fee'];
            $sCarTitle = $aEstimateData[$iProductCategory]['car_title'];
            $aEstimateFees[] = $fEstimateFee;
            $aCarTitleList[] = $sCarTitle;
            $aProductEtsList[] = empty($aProductEtsMap[$iProductCategory]) ? PHP_INT_MAX : $aProductEtsMap[$iProductCategory];
            $aProductCategoryList[] = $iProductCategory;
            $aMultiProducts[] = [
                'require_level'    => $aEstimateData[$iProductCategory]['extra_map']['require_level'] ?? 0,
                'combo_type'       => $aEstimateData[$iProductCategory]['extra_map']['combo_type'] ?? 0,
                'business_id'      => $aEstimateData[$iProductCategory]['extra_map']['business_id'] ?? 0,
                'level_type'       => $aEstimateData[$iProductCategory]['extra_map']['level_type'] ?? 0,
                'carpool_type'     => $aEstimateData[$iProductCategory]['extra_map']['carpool_type'] ?? 0,
                'is_default_auth'  => $aEstimateData[$iProductCategory]['extra_map']['is_default_auth'] ?? 0,
                'estimate_id'      => $aEstimateData[$iProductCategory]['estimate_id'] ?? '',
                'product_category' => $iProductCategory,
            ];

            if (!empty($aEstimateData[$iProductCategory]['carpool_seat_module']) ||!empty($aEstimateData[$iProductCategory]['carpool_seat_list'])) {
                $iPoolSeat = empty($aEstimateData['seat_num']) ? 1 : $aEstimateData['seat_num'];
            }
        }

        if (empty($aEstimateFees) || empty($aCarTitleList) || empty($aProductEtsList) || empty($aProductCategoryList) || empty($aMultiProducts)) {
            return null;
        }

        $fMinEstimateFee = min($aEstimateFees);
        $fMaxEstimateFee = max($aEstimateFees);
        $iMinEts = min($aProductEtsList);
        $sFeeDesc = Language::replaceTag($aText['fee_desc_multi'],
            [
                'min_estimate_fee' => $fMinEstimateFee,
                'max_estimate_fee' => $fMaxEstimateFee,
            ]
        );
        $sSubTitle = Language::replaceTag($aText['sub_title'], ['car_title_list' => implode('、', $aCarTitleList)]);

        // 异常场景
        if ($iMinEts == PHP_INT_MAX || $iMinEts <= 0) {
            $sTitle = $aText['default_title'];
        } elseif ($iMinEts < 60) {
            $sTitle = Language::replaceTag($aText['title_second'], ['time' => $iMinEts]);
        } else {
            $sTitle =  Language::replaceTag($aText['title'], ['time' => ceil($iMinEts / 60)]);
        }

        $aActionParams = [
            'guide_pos'             => GuideFactory::GUIDE_TYPE_ANYCAR,
            'source'                => PPreCancelOrderController::ATHENA_GUIDE_SOURCE_CANCEL_DETAINMENT,
            'estimate_trace_id'     => $this->_oRuntimeData->oAnycarEstimateViewModel->aAnycarEstimate['estimate_trace_id'],
            'multi_require_product' => json_encode($aMultiProducts),
            'pool_seat'             => empty($iPoolSeat) ? 1 : $iPoolSeat,
            'carpool_seat_num'      => empty($iPoolSeat) ? 1 : $iPoolSeat,
        ];
        $oGuideCard->setActionParams($aActionParams);
        $oGuideCard->setTitle($sTitle);
        $oGuideCard->setSubTitle($sSubTitle);
        $oGuideCard->setIcon($aText['icon']);
        $oGuideCard->setFeeDesc($sFeeDesc);
        $oGuideCard->setIRecType(BaseViewRender::REC_TYPE_APPEND_MULTIPLE_RECOMMEND_ANYCAR);
        $aOmegaParams = [
            'rec_type'         => $oGuideCard->getIRecType(),
            'product_category' => json_encode($aProductCategoryList),
        ];
        $oGuideCard->setOmegaParams($aOmegaParams);
        return $oGuideCard;
    }

    /**
     * 构建车型预估应答时间
     * @return array
     */
    private function _buildProductEtsMap() {
        $aProductEtsMap = [];
        if (empty($this->_aAthenaExpectInfo) || empty($this->_aAthenaExpectInfo['product_scene_expect']) || empty($this->_aAthenaExpectInfo['product_scene_expect']['product_infos'])) {
            return null;
        }

        $aProductInfos = $this->_aAthenaExpectInfo['product_scene_expect']['product_infos'];
        foreach ($aProductInfos as $aProductInfo) {
            if (empty($aProductInfo['product_category']) || empty($aProductInfo['expect_info']) || empty($aProductInfo['expect_info']['ets'])) {
                continue;
            }

            $aProductEtsMap[$aProductInfo['product_category']] = $aProductInfo['expect_info']['ets'];
        }

        return $aProductEtsMap;
    }
}

