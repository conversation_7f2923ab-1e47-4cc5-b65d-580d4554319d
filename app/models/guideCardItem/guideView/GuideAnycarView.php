<?php

namespace PreSale\Models\guideCardView\guideView;

use BizCommon\Logics\Anycar\AnyCarCommonLogic;
use BizCommon\Logics\ProductCategoryCustomize\ProductCategoryTitleCustomizeLogic;
use BizLib\Config;
use BizLib\Log;
use BizLib\Utils\Language;
use BizLib\Utils\Nuwa;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Passport;
use BizLib\Utils\Product;
use BizLib\Utils\ProductCategory;
use BizLib\Utils\UtilHelper;
use Nuwa\ApolloSDK\Apollo;
use PPreCancelOrderController;
use PreSale\Infrastructure\Repository\Redis\GuideCardRedisRepository;
use PreSale\Logics\order\NewtonLogic;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\NewtonUtil;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TaxiPricingBoxLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TpForm;
use PreSale\Models\guideCardItem\button\GuideCardStyleManage;
use PreSale\Models\guideCardItem\button\OmniButton;
use PreSale\Models\guideCardItem\common\Etp2EtsSceneProcess;
use PreSale\Models\guideCardItem\EstimateInfo;
use PreSale\Models\guideCardItem\GuideFactory;
use PreSale\Models\guideCardItem\MatchResultRuntime;
use PreSale\Models\guideCardItem\OmniOmegaInfo;
use PreSale\Models\guideCardView\guideCard\BaseGuideCard;
use PreSale\Models\guideCardView\guideCard\GuideAnycarCard;
use BizLib\Constants\Horae;
use PreSale\Models\guideCardView\guideCard\GuideAnycarCardV3;
use PreSale\Models\guideCardView\viewModel\AnycarEstimateViewModel;

/**
 * 追加车型推荐
 *
 */
class GuideAnycarView extends BaseViewRender
{
    /**
     * 导流数据
     * @var array
     */
    private $_aOrderMatchRecommendResult;
    /**
     * Etp转Ets开关
     * @var \Xiaoju\Apollo\DefaultToggle|\Xiaoju\Apollo\FeatureToggle|\Xiaoju\Apollo\ToggleResult
     */
    private $_oEtp2etsSwitch;

    /**
     * 导流追加车型数据
     * @var array
     */
    private $_aGuideItem;

    const ANIMATION_SCENE_QUEUE = 2;

    const TAXI_BOX_GROUP_ID = '5_5';

    /**
     * @param MatchResultRuntime $oRuntimeData               运行时数据总线
     * @param array              $aOrderMatchRecommendResult athena推荐
     * @param bool               $bAggregate                 bAggregate
     * @param array              $aGuideRecommendInfo        athena推荐
     */
    public function __construct($oRuntimeData, $aOrderMatchRecommendResult, $bAggregate, $aGuideRecommendInfo) {
        parent::__construct($oRuntimeData);
        $this->_aOrderMatchRecommendResult = $aOrderMatchRecommendResult;
        $sText        = $oRuntimeData->oTextService->getText('guide_append_anycar_v6');
        $this->_aText = json_decode($sText, true) ?: [];
        $aOrderInfo   = $oRuntimeData->getOrderInfo();
        $sAppVersion  = $this->_oRuntimeData->getRequest()['app_version'];
        if (empty($sAppVersion)) {
            $sAppVersion = $this->_oRuntimeData->getRequest()['appVersion'];
        }

        if (empty($sAppVersion)) {
            $sAppVersion = $this->_oRuntimeData->getRequest()['appversion'];
        }

        $this->_oEtp2etsSwitch = \Xiaoju\Apollo\Apollo::getInstance()->featureToggle(
            'etp_to_ets_knife_switch',
            [
                'key'           => $aOrderInfo['passenger_id'],
                'city'          => $aOrderInfo['area'],
                'pid'           => $aOrderInfo['passenger_id'],
                'lang'          => $this->_oRuntimeData->getRequest()['lang'],
                'app_version'   => $sAppVersion,
                'access_key_id' => $aOrderInfo['p_access_key_id'],
            ]
        );

        if (is_array($aGuideRecommendInfo)) {
            foreach ($aGuideRecommendInfo as $aItem) {
                if (GuideFactory::GUIDE_TYPE_ANYCAR == $aItem['type']) {
                    $this->_aGuideItem = $aItem;
                }
            }
        }
    }

    /**
     * @return void
     */
    public function prepareViewModel() {
        $this->_oRuntimeData->oAnycarEstimateViewModel->buildOnce();
    }

    /**
     * @return int
    */
    public function getRecommendHitType() {
        // 如果命中预付发单，屏蔽追加车型
        if (NewtonLogic::judgeDesignatedNewOrder($this->_oRuntimeData)) {
            return self::HT_NOHIT;
        }

        // 如果命中多推荐追加车型apollo 走多推荐追加车型
        if ($this->_oRuntimeData->bAllowMultiAppendCar) {
            return self::HT_NOHIT;
        }

        // 如果athena返回的是多推荐，走multiAnycarView
        if (!empty($this->_aOrderMatchRecommendResult['extra_info']) && !empty($this->_aOrderMatchRecommendResult['extra_info']['answer_rate_improve'])) {
            return self::HT_NOHIT;
        }

        // 如果没有缓存的预估数据 不渲染
        if (empty($this->_oRuntimeData->oAnycarEstimateViewModel->aAnycarEstimate)) {
            return self::HT_NOHIT;
        }

        if (GuideFactory::GUIDE_TYPE_ANYCAR == $this->_aOrderMatchRecommendResult['type']) {
            return self::HT_HIT;
        }

        // 追加拼车用户点击座位数后，需要重新渲染
        if (!empty($this->_oRuntimeData->getRequest()['carpool_seat_num'])) {
            return self::HT_HIT;
        }

        return self::HT_NOHIT;
    }

    /**
     * @return BaseGuideCard
     */
    public function execute() {
        $aOrderMatchRecommendResult = $this->_aOrderMatchRecommendResult;
        if (empty($aOrderMatchRecommendResult)) {
            // 推荐追加拼车，由于athena只在首次命中时返回推荐数据，而在推荐时长（如15s）内轮询时不再返回推荐数据，而追加拼车由于选座组件的出现，
            // 卡片的最终渲染会发生变化。而因为数据源（athena返的推荐）并不是每次都有，我们所拥有的只有上一次卡片的渲染数据。因此对于追加拼车，
            // 我们1.当上张卡片是追加拼车卡片时，HIT_TYPE=HT_HIT；2.不再走用athena数据源的渲染逻辑，而是对原卡片做渲染的替换
            if (!empty($this->_oRuntimeData->getRequest()['carpool_seat_num'])) {
                $oGuideCard = GuideCardRedisRepository::get($this->_oRuntimeData->getOrderInfo()['passenger_id'], BaseViewRender::CARD_ID_APPEND_CARPOOL);
                return $this->_appendCarpoolReRender($oGuideCard);
            }
        }

        $sTitle = '';
        if ($this->_oEtp2etsSwitch->allow() && MatchResultRuntime::EXP_CONTROL_GROUP != $this->_oRuntimeData->iExperimentalGroup) {
            $sTitle = $this->_getTitleV2($aOrderMatchRecommendResult);
        } else {
            $sTitle = $this->_getTitleV1($aOrderMatchRecommendResult);
        }

        // 获取导流推荐追加的车型基本信息
        $iShowType      = $aOrderMatchRecommendResult['show_type'] ?? 0; // 载人车、拼友
        $aTargetProduct = $aOrderMatchRecommendResult['target_product'];
        $aExtraInfo     = $aOrderMatchRecommendResult['extra_info'];
        $aTargetProduct = [
            'business_id'   => $aTargetProduct['business_id'],
            'combo_type'    => $aTargetProduct['combo_type'],
            'require_level' => $aTargetProduct['require_level'],
            'level_type'    => $aTargetProduct['level_type'],
            'estimate_id'   => $aExtraInfo['sub_estimate_id'],
            'is_selected'   => 0,
        ];
        $oEstimateInfo  = new EstimateInfo();
        $aProductList   = [];
        if (!empty($aTargetProduct)) {
            $oEstimateInfo->bFresh = true;
            $aTargetProduct        = $this->_updateProduct($aTargetProduct);
            if ($this->_oRuntimeData->isIterationSwitchV3()) {
                $aMultiRequireProduct = $aTargetProduct;
            } else {
                $aMultiRequireProduct = [$aTargetProduct];
            }

            $oEstimateInfo->sEstimateId      = $this->_oRuntimeData->oAnycarEstimateViewModel->aAnycarEstimate['estimate_id'];
            $oEstimateInfo->sEstimateTraceId = $this->_oRuntimeData->oAnycarEstimateViewModel->aAnycarEstimate['estimate_trace_id'];
            $aProductList = $this->_judgeAndSetProductCustomTitle($this->_oRuntimeData->getOrderInfo(), $aMultiRequireProduct);
            $oEstimateInfo->sFeeDetailUrl = $this->_oRuntimeData->oAnycarEstimateViewModel->aAnycarEstimate['fee_detail_url'];
            $oEstimateInfo->aLayout       = $this->_oRuntimeData->oAnycarEstimateViewModel->getAnycarEstimateLayout(array_values($aTargetProduct), AnycarEstimateViewModel::NO_BOX_LAYOUT);
        } else {
            $oEstimateInfo->bFresh = false;
        }

        if ($this->_oRuntimeData->isIterationSwitchV3()) {
            $oGuideCard = new GuideAnycarCardV3();
            $oEstimateInfo->aEstimateData = $aProductList;
            $sSubTitle = $this->_getText('subtitle', ['business_name' => array_values($aTargetProduct)[0]['business_name']]);
            $this->_hackTaxiDoublePriceV3($oEstimateInfo->aEstimateData,$sSubTitle);
            $this->_hackTpFormCarTitleV3($oEstimateInfo->aEstimateData,$sSubTitle);
            $this->_hackBoxCarIcon($oEstimateInfo);
        } else {
            $oGuideCard = new GuideAnycarCard();
            $oEstimateInfo->aProductList = $aProductList;
            $sSubTitle = $this->_getText('subtitle', ['business_name' => $aTargetProduct['business_name']]);
        }

        $oGuideCard->setEstimateInfo($oEstimateInfo);
        $oGuideCard->setTitle($sTitle);
        $oGuideCard->setSubTitle($sSubTitle);
        if (Horae::RECOMMEND_CARPOOL_CAR == $iShowType) {
            // 推荐载人车
            $iRecType = BaseViewRender::REC_TYPE_CARPOOL_CAR;
        } elseif (Horae::RECOMMEND_RELATION_PASSENGER == $iShowType) {
            // 推荐可打包拼友
            $iRecType = BaseViewRender::REC_TYPE_RELATION_PASSENGER;
        } else {
            // 追加拼车由于有选座组件，需要selectionCard
            $iRecType = BaseViewRender::REC_TYPE_APPEND_ANYCAR;
        }

        $oButton      = new OmniButton();
        $aProductList = $oEstimateInfo->aEstimateData;
        $aEstimateInfoOfOmega = array();
        $sEstimateTraceId     = $this->_oRuntimeData->oAnycarEstimateViewModel->aAnycarEstimate['estimate_trace_id'];
        foreach ($aProductList as $sProductCategory => $aProduct) {
            $aEstimateInfoOfOmega[$sProductCategory] = [
                'estimate_id'       => $aProduct['estimate_id'],
                'estimate_trace_id' => $sEstimateTraceId,
                'is_selected'       => $aProduct['is_selected'],
            ];
            //埋点增加图片链接
            if (!empty($aProduct['fee_desc_list']) && is_array($aProduct['fee_desc_list'])) {
                $aIconLink = [];
                foreach ($aProduct['fee_desc_list'] as $aFeeDesc) {
                    if (!empty($aFeeDesc['icon'])) {
                        $aIconLink[] = $aFeeDesc['icon'];
                    }
                }

                $aEstimateInfoOfOmega[$sProductCategory]['icon_link'] = $aIconLink;
            }
        }

        // 将埋点数据中需要的预估数据放入数据总线
        $this->_oRuntimeData->setGuideAnycarEstimateDataOfOmega($aEstimateInfoOfOmega);
        $oButton->setText($this->_getText('button_text'));
        $oButton->setActionType(OmniButton::ACTION_TYPE_ANYCAR_NEW_ORDER);
        $oButton->setButtonStyle(GuideCardStyleManage::getDefaultButtonStyle());
        $aParams = [
            'guide_pos' => GuideFactory::GUIDE_TYPE_ANYCAR,
            'source'    => PPreCancelOrderController::ATHENA_GUIDE_SOURCE_CANCEL_DETAINMENT,
        ];
        $oButton->setAButtonParams($aParams);
        $oGuideCard->setButton($oButton);
        $sOmegaKey  = OmniOmegaInfo::getOmegaSourceReplace(OmniOmegaInfo::PRE_CANCEL_ORDER_RECOMMEND_CK, $this->_oRuntimeData->getRequest()['access_key_id']);
        $oOmegaInfo = new OmniOmegaInfo();
        $oOmegaInfo->setSKey($sOmegaKey);
        $oOmegaInfo->setAParams(
            [
                'order_id'      => $this->_oRuntimeData->getOrderInfo()['order_id'],
                'uid'           => UtilHelper::getUidByPassengerId($this->_oRuntimeData->getOrderInfo()['passenger_id'], Passport::PASSENGER_ROLE),
                'city_id'       => $this->_oRuntimeData->getOrderInfo()['area'],
                'rec_type'      => $iRecType,
                'is_recommend'  => 1,
                'estimate_info' => json_encode($aEstimateInfoOfOmega),
            ]
        );
        $oButton->setOmegaInfo($oOmegaInfo);
        $oGuideCard->setIRecType($iRecType);
        return $oGuideCard;
    }

    /**
     * 弹窗na逻辑，盒子从layout取car_icon,非盒子从estimate_data获取car_icon，
     * 由于estimate_data从预估获取，等待应答展示盒子，故estimate_data中car_icon为空，导致弹窗不展示车标
     * @param object $oEstimateInfo oEstimateInfo
     * @return void
     */
    private function _hackBoxCarIcon(&$oEstimateInfo) {
        $aTargetEstimateData = $oEstimateInfo->aEstimateData;
        $aLayout = $oEstimateInfo->aLayout;
        foreach ($aLayout as $item) {
            if (1 == $item['groups'][0]['type']) {
                $aProducts = $item['groups'][0]['products'];
                foreach ($aProducts as $iProductcategory) {
                    if (empty($aTargetEstimateData[$iProductcategory]['car_icon'])) {
                        $aTargetEstimateData[$iProductcategory]['car_icon'] = $item['groups'][0]['car_icon'];
                    }
                }
            }
        }

        $oEstimateInfo->aEstimateData = $aTargetEstimateData;
    }

    /**
     * @param array  $aEstimateProductList aEstimateProductList
     * @param string $sSubTitle            sSubTitle
     * @return void
     */
    private function _hackTpFormCarTitleV3(&$aEstimateProductList, &$sSubTitle) {
        $aOrderInfo = $this->_oRuntimeData->getOrderInfo();
        $iOrderId   = $aOrderInfo['order_id'];
        foreach ($aEstimateProductList as $key => &$aEstimateProduct) {
            $iProductId  = Product::getProductIdByBusinessId($aEstimateProduct['business_id']);
            $sEstimateID = $aEstimateProduct['estimate_id'];
            $iUserId     = UtilHelper::getUidByPassengerId($aOrderInfo['passenger_id'], \BizLib\Constants\Common::BIZ_TYPE_PASSENGER);
            if (TpForm::isHitTpForm($iOrderId,$iProductId,$sEstimateID)) {
                unset($aEstimateProduct['car_tag']);
            }

            $sBusinessName = TpForm::getTpLabelText($aEstimateProduct['car_title'],$iOrderId,$iProductId,$sEstimateID);
            if (empty($sBusinessName)) {
                return;
            }

            $aEstimateProduct['car_title'] = $sBusinessName;
            $sSubTitle = $this->_getText('subtitle', ['business_name' => $sBusinessName]);
        }
    }

    /**
     * @param array  $aEstimateProductList aEstimateProductList
     * @param string $sSubTitle            sSubTitle
     * @return bool|void
     */
    private function _hackTaxiDoublePriceV3(&$aEstimateProductList, &$sSubTitle) {
        $aRequest   = $this->_oRuntimeData->getRequest();
        $aOrderInfo = $this->_oRuntimeData->getOrderInfo();
        $apolloUser = [
            'key'           => UtilHelper::getUidByPassengerId($aOrderInfo['passenger_id'], \BizLib\Constants\Common::BIZ_TYPE_PASSENGER),
            'lang'          => $aRequest['lang'],
            'phone'         => $aOrderInfo['passenger_phone'],
            'city'          => $aOrderInfo['area'],
            'access_key_id' => $aRequest['access_key_id'],
            'app_version'   => $aRequest['appversion'],
            'county_id'     => $aOrderInfo['county'],
            'order_type'    => $aOrderInfo['type'],
            'pid'           => $aOrderInfo['passenger_id'],
        ];

        $apolloParams = array_merge(
            $apolloUser,
            [
                'guide_pos' => GuideFactory::GUIDE_TYPE_ANYCAR,
                'event'     => '_hackTaxiDoublePriceV3',
                'module'    => MODULE_NAME,
            ]
        );
        $oToggle      = \Xiaoju\Apollo\Apollo::getInstance()->featureToggle('taxi_double_price_offline', $apolloParams);
        if ($oToggle->allow()) {
             // 命中出租车盒子，需要替换盒子的 car_title、car_icon
            if (TpForm::unionIsShowBox($apolloUser)) {
                $aConfig = explode(',', $oToggle->getParameter('allow_product_category', ''));
                foreach ($aConfig as $iId) {
                    if (!empty($aEstimateProductList[$iId]['extra_info']['origin_icon'])) {
                        $aEstimateProductList[$iId]['car_icon'] = $aEstimateProductList[$iId]['extra_info']['origin_icon'];
                    }

                    if (ProductCategory::PRODUCT_CATEGORY_TAXI_MARKETISATION_PUTONG == $iId) {
                        $aEstimateProductList[$iId]['car_title'] = $this->_getText('taxi_online_price_car_name');
                    }

                    if (ProductCategory::PRODUCT_CATEGORY_UNIONE == $iId) {
                        $aEstimateProductList[$iId]['car_title'] = $this->_getText('taxi_offline_price_car_name');
                    }

                    $sBoxCarTitle = $this->_oRuntimeData->oAnycarEstimateViewModel->getAnycarEstimateBoxCarTitleByCategory(
                        (string)
                        $iId,
                        self::TAXI_BOX_GROUP_ID
                    );
                    if (!empty($sBoxCarTitle)) {
                        $aEstimateProductList[$iId]['car_title'] = $sBoxCarTitle;
                    }
                }
            }

            return;
        }

        $aEstimateData = $this->_oRuntimeData->oAnycarEstimateViewModel->aAnycarEstimate['estimate_data'];

        if (empty($aEstimateProductList['7']) || empty($aEstimateData['188'])) {
            return;
        }

        $sProductName = $this->_getText('taxi_double_price_car_name');
        TpForm::dealUnionDoublePriceProductName($sProductName,$apolloUser);
        $sSubTitle = $this->_getText('subtitle', ['business_name' => $sProductName]);

        $aEstimateProductList['7']['car_title']     = $sProductName;
        $aEstimateProductList['7']['business_name'] = $sProductName;
        if (!TpForm::unionIsShowRangePriceBox($apolloUser) && !TpForm::unionIsReplaceName($apolloUser)) {
            $aEstimateProductList['7']['fee_amount'] = $aEstimateData['188']['fee_amount'];
            $aEstimateProductList['7']['fee_msg']    = $aEstimateData['188']['fee_msg'];
            $aEstimateProductList['7']['price']      = $aEstimateData['188']['price'];
            $aEstimateProductList['7']['dynamic_total_fee'] = $aEstimateData['188']['dynamic_total_fee'];
            $aEstimateProductList['7']['fee_desc_list']     = $aEstimateData['188']['fee_desc_list'];

            $aEstimateProductList['7']['car_icon'] = $this->_getText('taxi_double_price_car_icon');
        }
    }

    /**
     * 拼车修改座位时会重新请求该接口，需要缓存下上一次的拼车数据，并取出来对比座位数是否一致，不一致需要修改预估数据
     * @param GuideAnycarCard $oGuideCard 追加车型卡片
     * @return BaseGuideCard
     */
    private function _appendCarpoolReRender($oGuideCard) {
        if (empty($oGuideCard)) {
            return null;
        }

        // 判断座位数是否一致，一致则不更新
        $oEstimateInfo = $oGuideCard->getEstimateInfo();
        // 目前只有一个车型
        if ($this->_oRuntimeData->isIterationSwitchV3()) {
            $aEstimateData      = $oEstimateInfo->aEstimateData;
                $aTargetProduct = array_values($aEstimateData)[0];
                $aSeatNums      = $aTargetProduct['seat_nums'];
        } else {
            $aTargetProduct = $oEstimateInfo->aProductList[0];
            $aSeatNums      = $aTargetProduct['seat_nums'];
        }

            $iOldSeatNum = 1;
        foreach ($aSeatNums as $aSeatItem) {
            if (1 == $aSeatItem['is_selected']) {
                $iOldSeatNum = $aSeatItem['num'];
            }
        }

        if ($this->_oRuntimeData->getRequest()['carpool_seat_num'] != $iOldSeatNum) {
            // 需要更新拼车预估数据
            $this->_oRuntimeData->oAnycarEstimateViewModel->buildOnce();
            // 目前只有一个车型
            $aProduct       = [
                'business_id'      => $aTargetProduct['business_id'],
                'combo_type'       => $aTargetProduct['combo_type'],
                'require_level'    => $aTargetProduct['require_level'],
                'level_type'       => $aTargetProduct['level_type'],
                'estimate_id'      => $aTargetProduct['sub_estimate_id'],
                'product_category' => $aTargetProduct['product_category'],
            ];
            $aTargetProduct = $this->_updateProduct($aProduct);
        }

            // 只有勾选后才可以切座 切座时is_selected 为 1
        if ($this->_oRuntimeData->isIterationSwitchV3()) {
            foreach ($aTargetProduct as  &$aProductItem) {
                $aProductItem['is_selected'] = 1;
            }

            $aMultiRequireProduct         = $aTargetProduct;
            $oEstimateInfo->aEstimateData = $this->_judgeAndSetProductCustomTitle($this->_oRuntimeData->getOrderInfo(), $aMultiRequireProduct);
        } else {
            $aTargetProduct['is_selected'] = 1;
            $aMultiRequireProduct          = [$aTargetProduct];
            $oEstimateInfo->aProductList   = $this->_judgeAndSetProductCustomTitle($this->_oRuntimeData->getOrderInfo(), $aMultiRequireProduct);
        }

            $oGuideCard->setEstimateInfo($oEstimateInfo);

            return $oGuideCard;
    }

    /**
     * 判别&设置自定义品类title
     *
     * @param array $aOrderInfo           orderInfo
     * @param array $aEstimateProductList aEstimateProductList
     *
     * @return array $aEstimateProductList
     */
    private function _judgeAndSetProductCustomTitle($aOrderInfo, $aEstimateProductList) {
        if (empty($aOrderInfo) || empty($aEstimateProductList)) {
            return $aEstimateProductList;
        }

        $bIsLineup = false;
     // 挽留弹窗拉齐和等待应答追加车型列表的ets，需用追加车型的是否排队信息
        foreach ($this->_aGuideItem['multi_require_product'] as $aProductItem) {
            if (self::ANIMATION_SCENE_QUEUE == $aProductItem['scene_flag']) {
                $bIsLineup = true;
                break;
            }
        }

        $oProductCategory = new ProductCategory();

        foreach ($aEstimateProductList as &$aProductItem) {
            $aProductCategoryMap = [
                'business_id'   => $aProductItem['business_id'],
                'require_level' => $aProductItem['require_level'],
                'combo_type'    => $aProductItem['combo_type'],
            ];

            if (!empty($aProductItem['product_category']) && ProductCategory::PRODUCT_CATEGORY_UNIONE_SPECIAL_PRICE == empty($aProductItem['product_category'])) {
                $iProductCategory = $aProductItem['product_category'];
            } else {
                $iProductCategory = $oProductCategory->getProductCategory($aProductCategoryMap);
            }

            $oProductCategoryTitleCustomLogic = new ProductCategoryTitleCustomizeLogic();
            $bProductCategoryCustomOpened     = $oProductCategoryTitleCustomLogic->isProductCategoryCustomTitleOpened(
                [
                    'passenger_id'  => $aOrderInfo['passenger_id'],
                    'city'          => $aOrderInfo['area'],
                    'phone'         => $aOrderInfo['passenger_phone'],
                    'category'      => $iProductCategory,
                    'access_key_id' => $this->_oRuntimeData->getRequest()['access_key_id'],
                    'lang'          => $this->_oRuntimeData->getRequest()['lang'],
                ]
            );

            if ($bProductCategoryCustomOpened) {
                $aCustomProduct = $oProductCategoryTitleCustomLogic->getProductCategoryCustomTitle($aOrderInfo['area'],$iProductCategory);
                if (!empty($aCustomProduct['intro_msg'])) {
                    $aProductItem['business_name'] = $aCustomProduct['intro_msg'];
                    $aProductItem['car_title']     = $aCustomProduct['intro_msg'];
                }
            }

            if ($this->_oEtp2etsSwitch->allow() && MatchResultRuntime::EXP_CONTROL_GROUP != $this->_oRuntimeData->iExperimentalGroup) {
                $iUndertakeEts    = Etp2EtsSceneProcess::getInstance()->getUndertakeEts($aProductItem);
                $sConfigKey       = $this->_oEtp2etsSwitch->getParameter('config_product_list_key','etp_to_ets_experiment_condition_config');
                $aConditionConfig = Config::getBizConfig($sConfigKey,'condition_config')[(string)($this->_oRuntimeData->iExperimentalGroup)];
                $aCondCfg         = $aConditionConfig['condition_config'];
                $sDocKey          = $aConditionConfig['doc_key'];
                $iEts = empty($aProductItem['extra_info']['ets']) ? $iUndertakeEts : $aProductItem['extra_info']['ets'];
                $aProductItem['etp_str'] = $this->_getEtsStr($iEts,$aCondCfg,$sDocKey,$aProductItem,$bIsLineup);
                continue;
            }
        }

        return $aEstimateProductList;
    }

    /**
     * 填充预估数据
     * @param array $aTargetProduct 推荐的车型数据
     * @return array
     */
    private function _updateProduct($aTargetProduct) {
        // 获取预估数据
        $this->_oRuntimeData->oAnycarEstimateViewModel->buildOnce();
        $aProductList = $this->_oRuntimeData->oAnycarEstimateViewModel->getProductList();
        if (empty($aProductList)) {
            return $aTargetProduct;
        }

        if (Apollo::getInstance()->featureToggle(
            'special_price_taxi_group_key_gen_switch',
            [
                'key'              => $this->_oRuntimeData->getOrderInfo()['passenger_id'],
                'pid'              => $this->_oRuntimeData->getOrderInfo()['passenger_id'],
                'product_category' => $aTargetProduct['product_category'],
            ]
        )->allow()
        ) {
            $sTargetGroupKey = AnyCarCommonLogic::getGroupKey($aTargetProduct,$aTargetProduct['product_category']);
        } else {
            $sTargetGroupKey = AnyCarCommonLogic::getGroupKey($aTargetProduct);
        }

        foreach ($aProductList as $key => $aEstimateItem) {
            if (Apollo::getInstance()->featureToggle(
                'special_price_taxi_group_key_gen_switch',
                [
                    'key'              => $this->_oRuntimeData->getOrderInfo()['passenger_id'],
                    'pid'              => $this->_oRuntimeData->getOrderInfo()['passenger_id'],
                    'product_category' => $aEstimateItem['product_category'],
                ]
            )->allow()
            ) {
                $sGroupKey = AnyCarCommonLogic::getGroupKey($aEstimateItem, $aEstimateItem['product_category']);
            } else {
                $sGroupKey = AnyCarCommonLogic::getGroupKey($aEstimateItem);
            }

            if ($sGroupKey == $sTargetGroupKey) {
                // 当前产品的预估数据
                $aTargetProduct = array_merge($aTargetProduct, $aEstimateItem);
                $aTargetProduct['is_selected'] = 0;
            }

            if (!empty($aTargetProduct['side_extra']) && !empty($aTargetProduct['side_extra']['right_sub_title']) && !empty($aTargetProduct['side_extra']['right_sub_title']['disabled'])) {
                $aTargetProduct['side_extra']['right_sub_title'] = null;
            }
        }

        if ($this->_oRuntimeData->isIterationSwitchV3()) {
            // map结构
            $aNewTargetProduct[$aTargetProduct['product_category']] = $aTargetProduct;
            $aTargetProduct = $aNewTargetProduct;
        }

        return $aTargetProduct;
    }

    /**
     * @param array $aOrderMatchRecommendResult athena推荐车型参数
     * @return array|string
     */
    private function _getTitleV1($aOrderMatchRecommendResult) {
        $sTitle = $this->_getText('title', ['etp' => $aOrderMatchRecommendResult['etp']]);
        if (isset($aOrderMatchRecommendResult['extra_info']['ets'])) {
            $iEts = $aOrderMatchRecommendResult['extra_info']['ets'];
            if ($iEts < 60) {
                $sTitle = $this->_getText('title_ets_sec', ['ets_sec' => $iEts]);
            } else {
                $sTitle = $this->_getText('title_ets_min', ['ets_min' => (int)(($iEts + 29) / 60)]);
            }
        }

        //无etp、ets时展示更快上车
        if (isset($aOrderMatchRecommendResult['extra_info']['skip_rec_etp']) && 1 == $aOrderMatchRecommendResult['extra_info']['skip_rec_etp']) {
            $sTitle = $this->_getText('title_skip_rec_etp');
        }

        return $sTitle;
    }

    /**
     * 获取追加车型列表主标题
     * @param array $aOrderMatchRecommendResult athena 推荐车型
     * @return mixed|string
     */
    private function _getTitleV2(array $aOrderMatchRecommendResult) {
        $sConfigKey       = $this->_oEtp2etsSwitch->getParameter('config_key','cancel_stay_popup_ets_condition_config');
        $aConditionConfig = Config::getBizConfig($sConfigKey,'condition_config');
        $aCondCfg         = $aConditionConfig['condition_config'];
        $sDocKey          = $aConditionConfig['doc_key'];
        return $this->_getEtsStr($aOrderMatchRecommendResult['extra_info']['cancel_rec_ets'],$aCondCfg,$sDocKey);
    }

    /**
     * 根据条件配置和文案key获取ets文案
     * @param int    $iEts     预期ets
     * @param array  $aCondCfg 条件配置
     * @param string $sDocKey  文案配置
     * @return mixed|string
     */
    private function _getEtsStr($iEts, $aCondCfg, $sDocKey) {
        if (empty($iEts)) {
            //兜底逻辑
            return Language::getDecodedTextFromDcmp('pre_cancel-cancel_stay_popup_ets')['undertake_text'];
        }

        return Etp2EtsSceneProcess::getInstance()->getEtsStr($iEts,$aCondCfg,$sDocKey);
    }
}
