<?php
namespace PreSale\Models\guideCardItem;

/**
 * guideFactory
 */
class GuideFactory
{
    const GUIDE_TYPE_CARPOOL = 1;     //同时找拼车
    const GUIDE_TYPE_FARTHER = 2;     //远程派单
    const GUIDE_TYPE_GONGYI  = 5;     //公益加价出口
    const GUIDE_TYPE_BUS     = 6;     // 公交
    const GUIDE_TYPE_FASTWAY = 7;     //应急通道出口
    const GUIDE_TYPE_TIP     = 8;     //无应答unione展示tip
    const GUIDE_TYPE_ANYCAR  = 9;     //anycar出口
    const GUIDE_TYPE_LOCK_CAR_RECOMMEND     = 522;  //锁车推荐
    const GUIDE_TYPE_PASSENGER_TIP          = 12;    //乘客感谢费出口
    const GUIDE_TYPE_HK_NORMAL_TIP          = 14;    //香港普通小费
    const GUIDE_TYPE_HK_REMOTE_DISPATCH_TIP = 15;    //香港远程调度小费
    const GUIDE_TYPE_BIKE            = 16;    // 单车
    const GUIDE_TYPE_FASTWAY_COUPON  = 17;    //快速通道权益券
    const GUIDE_TYPE_OCEAN_MEMBER    = 18;  // 洋流会员体验卡
    const GUIDE_TYPE_ACCOMPANY_ORDER = 20; // 陪伴出行banner展示（根据dos信息直接展示）
    const GUIDE_TYPE_TIME_SHARING    = 47;    // 分时租赁
    const GUIDE_TYPE_FASTWAY_FOR_ANYCAR = 55;    //anycar快速通道
    const GUIDE_TYPE_FARTHER_FOR_ANYCAR = 56; // anycar加价调度
    const GUIDE_TYPE_PICK_ON_TIME       = 58; //极高确出口
    const GUIDE_TYPE_BOX = 59; // 盒子出口
    const GUIDE_TYPE_MEMBER_ADD_A_PLUS         = 61; // 会员优惠追加特快
    const GUIDE_TYPE_PREMIUM_SPEED_ANSWER      = 64; //专车极速应答出口
    const GUIDE_TYPE_UNIONE_PICKUP_BY_METER    = 111;   // 出租车打表来接
    const GUIDE_TYPE_DRAW_LIKE_WAIT_REWARD     = 112; // 领取福利金红包
    const GUIDE_TYPE_LOW_ANSWER_RATE_RECOMMEND = 524;  // 应答率低推荐追加车型（展示应答率）
    const GUIDE_TYPE_GUIDE_MULTI_SELECT        = 525; // 引导用户多勾
    const GUIDE_TYPE_GUIDE_MEMBER_AGGREGATE    = 526; // 会员聚合出口（包含 快/专快速通道 及 优惠追加特快）
    const GUIDE_TYPE_CARPOOL_DUAL_PRICE_TIP    = 1000;  // 拼车两口价提示文案，自定义的类型1000-2000

    const GUIDE_TYPE_LIKE_WAIT_CARPOOL_DEPART_IMMDEDIATELY = 1001; // 愿等拼车立即出发

    const GUIDE_TYPE_ORDER_RULE = 1002; // 预约规则

    const GUIDE_TYPE_AGGREGATE_MEMBER = 1004; // 会员聚合
}
