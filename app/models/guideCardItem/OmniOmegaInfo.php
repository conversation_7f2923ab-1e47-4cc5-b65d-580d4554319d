<?php

namespace PreSale\Models\guideCardItem;

use BizLib\Constants\Common;
use BizLib\Utils\Language;

/**
 * 埋点数据
 */
class OmniOmegaInfo implements \JsonSerializable
{
    // na与小程序埋点不同
    const PRE_CANCEL_ORDER_SHOW         = 'wyc_{{source}}_waitpage_retain_sw';     // 挽留弹窗-曝光
    const PRE_CANCEL_ORDER_RETAIN_DURING_SW = 'wyc_waitpage_retain_during_sw';     // 弹窗曝光时长埋点
    const PRE_CANCEL_ORDER_RECOMMEND_CK = 'wyc_{{source}}_waitpage_retain_ck';    // 挽留弹窗挽留功能-点击
    const PRE_CANCEL_ORDER_CANCELINTER_CK = 'wyc_{{source}}_waitpage_cancelinter_ck'; // 关闭按钮点击埋点
    const PRE_CANCEL_ORDER_CONFIRM_CANCEL_CK = 'wyc_{{source}}_waitpage_retain_cancel_ck'; // 挽留弹窗确认取消-点击
    const PRE_CANCEL_ORDER_NO_CANCEL_CK      = 'wyc_{{source}}_waitpage_retain_nocancel_ck'; // 挽留弹窗暂不取消-点击

    const ORDER_CLOSE_POPUP_CALLNOW_SHOW = 'wyc_didiapp_order_close_popup_callnow_sw';  // 预约单 挽留弹窗 转实时单 -曝光
    const ORDER_CLOSE_POPUP_CALLNOW_CK = 'wyc_didiapp_order_close_popup_callnow_ck';     // 预约单 挽留弹窗 转实时单 -点击


    const NA_KEY  = 'ckd';
    const NA_KEY2 = 'six';
    const XCX_KEY = 'xcx';

    const MEMBER_LICHENG        = 0; // 里程会员
    const MEMBER_PAID           = 1; //付费会员
    const MEMBER_ZHUANHAO_MEDAL = 2; // 专豪勋章
    const MEMBER_ZHUANHAO_PAID  = 3; // 专豪付费会员


    private $_sKey;
    private $_aParams;

    /**
     * @param mixed $sKey key
     * @return OmniOmegaInfo
     */
    public function setSKey($sKey) {
        $this->_sKey = $sKey;
        return $this;
    }

    /**
     * @param mixed $aParams params
     * @return OmniOmegaInfo
     */
    public function setAParams($aParams) {
        $this->_aParams = $aParams;
        return $this;
    }

    /**
     * @return array
     */
    public function getAParams() {
        return $this->_aParams;
    }

    /**
     * @return array|mixed
     */
    public function jsonSerialize() {
        $aRet = ['key' => $this->_sKey];
        if (!empty($this->_aParams)) {
            $aRet['params'] = $this->_aParams;
        }

        return $aRet;
    }


    /**
     * @param string $sKey         key
     * @param int    $iAccessKeyId accessKeyId
     * @return mixed
     */
    public static function getOmegaSourceReplace($sKey, $iAccessKeyId) {
        if (in_array($iAccessKeyId,[Common::DIDI_IOS_PASSENGER_APP, Common::DIDI_ANDROID_PASSENGER_APP])) {
            return Language::replaceTag($sKey, ['source' => self::NA_KEY]);
        } else {
            return Language::replaceTag($sKey, ['source' => self::XCX_KEY]);
        }

    }

    /**
     * @param string $sKey         key
     * @param int    $iAccessKeyId accessKeyId
     * @return mixed
     */
    public static function getOmegaSourceReplaceV2($sKey, $iAccessKeyId) {
        if (in_array($iAccessKeyId,[Common::DIDI_IOS_PASSENGER_APP, Common::DIDI_ANDROID_PASSENGER_APP])) {
            return Language::replaceTag($sKey, ['source' => self::NA_KEY2]);
        } else {
            return Language::replaceTag($sKey, ['source' => self::XCX_KEY]);
        }

    }
}
