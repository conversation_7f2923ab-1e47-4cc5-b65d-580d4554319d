<?php

namespace PreSale\Models\guideCardItem;

use <PERSON>izCommon\Logics\Order\MemberPrivilegeLogic;
use PreSale\Models\Member\LuxuryMemberLogic;

/** 这个model是给会员卡片用的，包含除卡片idl外的一些附加信息
 * Class MemberViewModel
 * @package Transaction\DuKang\Model\MatchResult
 */
class MemberViewModel
{
    /** 是否是弹窗内的卡片（渲染逻辑需要不同）
     * @var bool
     */
    private $_bIsPopup = false;

    /** 是否勾选，弹窗中的卡片需要
     * @var bool
     */
    private $_bIsSelected = false;
    /** 会员类型，用来生成外层数据或随权益的小icon *由于type=55多车型权益有多个会员，所以需要是数组
     * @var int[]
     */
    private $_aMemberType;

    /** 里程会员/专豪勋章会员有等级，附属在这俩memberType上
     * @var int[]
     */
    private $_aMemberLevel;


    /**
     * @return bool
     */
    public function isBIsSelected(): bool {
        return $this->_bIsSelected;
    }

    /**
     * @param bool $bIsSelected isSelected
     * @return MemberViewModel
     */
    public function setBIsSelected(bool $bIsSelected): MemberViewModel {
        $this->_bIsSelected = $bIsSelected;
        return $this;
    }


    /**
     * @return bool
     */
    public function isBIsPopup(): bool {
        return $this->_bIsPopup;
    }

    /**
     * @param bool $bIsPopup isPopup
     * @return MemberViewModel
     */
    public function setBIsPopup(bool $bIsPopup): MemberViewModel {
        $this->_bIsPopup = $bIsPopup;
        return $this;
    }

    /**
     * @return int[]
     */
    public function getAMemberType() {
        return $this->_aMemberType;
    }

    /**
     * @param int[] $aMemberType aMemberType
     * @return MemberViewModel
     */
    public function setAMemberType(array $aMemberType): MemberViewModel {
        $this->_aMemberType = $aMemberType;
        return $this;
    }

    /**
     * @return int[]
     */
    public function getAMemberLevel(): array {
        return $this->_aMemberLevel;
    }

    /**
     * @param int[] $aMemberLevel aMemberLevel
     * @return MemberViewModel
     */
    public function setAMemberLevel(array $aMemberLevel): MemberViewModel {
        $this->_aMemberLevel = $aMemberLevel;
        return $this;
    }

    /**
     * @param int $iMemberType  iMemberType
     * @param int $iMemberLevel iMemberLevel
     * @return $this
     */
    public function addMemberInfo($iMemberType, $iMemberLevel = 0) {
        $this->_aMemberType[]  = $iMemberType;
        $this->_aMemberLevel[] = $iMemberLevel;
        return $this;
    }

    /**
     * @param array              $aMemberPriv  会员返的权益信息（包含权益来源等）
     * @param MatchResultRuntime $oRuntimeData 数据总线
     * @return array
     */
    public static function getMemberTypeAndLevel($aMemberPriv, $oRuntimeData) {
        // 付费会员
        if (MemberPrivilegeLogic::PRIVILEGE_SOURCE_PAID_MEMBER == $aMemberPriv['privilege_source']) {
            return [OmniOmegaInfo::MEMBER_PAID, ''];
        } elseif (MemberPrivilegeLogic::PRIVILEGE_SOURCE_PREMIUM_PAID_MEMBER == $aMemberPriv['privilege_source']) {
            // 专豪付费会员
            return [OmniOmegaInfo::MEMBER_ZHUANHAO_PAID, ''];
        } elseif (LuxuryMemberLogic::isLuxuryPrivilege($aMemberPriv)) {
            // 专豪勋章会员
            return [OmniOmegaInfo::MEMBER_ZHUANHAO_MEDAL, $oRuntimeData->getLuxuryLevelId()];
        } elseif ($oRuntimeData->isMemberV3()) {
            // 里程会员
            $iLevelId = $oRuntimeData->getMemberLevelId();
            return [OmniOmegaInfo::MEMBER_LICHENG, $iLevelId];
        }
    }
}
