<?php

namespace PreSale\Models\guideCardView\guideCard;

use PreSale\Models\guideCardItem\button\IBaseButton;
use PreSale\Models\guideCardItem\button\OmniButton;
use PreSale\Models\guideCardItem\MemberViewModel;
use PreSale\Models\guideCardItem\OmniOmegaInfo;

/**
 * 会员卡片
 */
class ElementMemberCard extends BaseGuideCard implements \JsonSerializable
{
    /**
     * @var MemberViewModel
     */
    private $_oMemberModel;

    private $_sTitle = '';
    /**{'text': 'xx',
     * @var []
     */
    private $_aTitleMarker = [];
    private $_aSubtitle    = [];
    /**
     * @var OmniOmegaInfo
     */
    private $_oOmegaInfo;
    /**
     * @var OmniButton
     */
    private $_oButton = null;

    /**
     * 节约时间（需要取时间最少的）
     * @var int
     */
    private $_iWaitTimeSaved = null;

    /** 暂存的params数据，后续决策是放在button上还是card中
     * @var array
     */
    private $_aParams = [];

    /** 是否可适用（对于快速通道，不管能否适用都需要给用户展示出来，但不可用时按钮需置灰）
     * @var
     */
    private $_bDisabled;

    /**
     * ElementMemberCard constructor.
     */
    public function __construct() {
        $this->_oMemberModel = new MemberViewModel();
    }

    /**
     * @param string $sTitle title
     * @return $this
     */
    public function setSTitle($sTitle) {
        $this->_sTitle = $sTitle;
        return $this;
    }

    /**
     * @return string[]
     */
    public function getATitleMarker() {
        return $this->_aTitleMarker;
    }

    /**
     * @param string[] $aTitleMarker titleMarker
     * @return $this
     */
    public function setATitleMarker($aTitleMarker) {
        $this->_aTitleMarker = $aTitleMarker;
        return $this;
    }

    /**
     * @param string $sText text
     * @return $this
     */
    public function addTitleMarkerText($sText) {
        $this->_aTitleMarker[] = ['text' => $sText];
        return $this;
    }

    /**
     * @return IBaseButton
     */
    public function getOButton() {
        return $this->_oButton;
    }

    /**
     * @param IBaseButton $oButton button
     * @return $this
     */
    public function setOButton($oButton) {
        $this->_oButton = $oButton;
        return $this;
    }

    /**
     * @return int
     */
    public function getIWaitTimeSaved() {
        return $this->_iWaitTimeSaved;
    }

    /**
     * @param int $iWaitTimeSaved waitTimeSaved
     * @return $this
     */
    public function setIWaitTimeSaved($iWaitTimeSaved) {
        $this->_iWaitTimeSaved = $iWaitTimeSaved;
        return $this;
    }

    /**
     * @param OmniOmegaInfo $oOmegaInfo omegaInfo
     * @return $this
     */
    public function setOOmegaInfo($oOmegaInfo) {
        $this->_oOmegaInfo = $oOmegaInfo;
        return $this;
    }

    /**
     * @return array
     */
    public function getASubtitle() {
        return $this->_aSubtitle;
    }

    /**
     * @param array $aSubtitle subtitles
     * @return $this
     */
    public function setASubtitle($aSubtitle) {
        $this->_aSubtitle = $aSubtitle;
        return $this;
    }

    /**
     * @return MemberViewModel
     */
    public function getOMemberModel() {
        if (empty($this->_oMemberModel)) {
            $this->_oMemberModel = new MemberViewModel();
        }

        return $this->_oMemberModel;
    }

    /**
     * @param MemberViewModel $oMemberModel memberModel
     * @return $this
     */
    public function setOMemberModel($oMemberModel) {
        $this->_oMemberModel = $oMemberModel;
        return $this;
    }

    /**
     * @return array
     */
    public function getAParams() {
        return $this->_aParams;
    }

    /**
     * @param array $aParams params
     * @return $this
     */
    public function setAParams(array $aParams) {
        $this->_aParams = $aParams;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getBDisabled() {
        return $this->_bDisabled;
    }

    /**
     * @param mixed $bDisabled disabled
     * @return $this
     */
    public function setBDisabled($bDisabled) {
        $this->_bDisabled = $bDisabled;
        return $this;
    }

    /**
     * @return int|mixed
     */
    public function getCardType() {
        if ($this->_oMemberModel->isBIsPopup()) {
            return BaseGuideCard::GUIDE_TYPE_POPUP_MEMBER;
        } else {
            return BaseGuideCard::GUIDE_TYPE_MEMBER;
        }
    }

    /**
     * @return array
     */
    public function jsonSerialize() {
        // 如果是弹窗的话，没有button，params在第一层级
        if ($this->_oMemberModel->isBIsPopup()) {
            $aRet = [
                'card_type' => BaseGuideCard::GUIDE_TYPE_POPUP_MEMBER,
                'card_data' => [
                    'title'       => $this->_sTitle,
                    'params'      => $this->_aParams,
                    'omega_info'  => $this->_oOmegaInfo,
                    'is_selected' => $this->_oMemberModel->isBIsSelected() ? 1 : 0,
                    'disabled'    => $this->_bDisabled ? 1 : 0,
                ],
            ];

            if (!empty($this->_aSubtitle)) {
                $aRet['card_data']['subtitle'] = $this->_aSubtitle;
            }
        } else {
            $aRet = [
                'card_type' => BaseGuideCard::GUIDE_TYPE_MEMBER,
                'card_data' => [
                    'title'      => $this->_sTitle,
                    'button'     => $this->_oButton,
                    'omega_info' => $this->_oOmegaInfo,
                ],
            ];

            if (!empty($this->_aSubtitle)) {
                $aRet['card_data']['subtitle'] = $this->_aSubtitle;
            }
        }

        if (!empty($this->_aTitleMarker)) {
            $aRet['card_data']['title_marker'] = $this->_aTitleMarker;
        }

        return $aRet;
    }

    /**
     * @return OmniOmegaInfo
     */
    public function getOmegaInfo() {
        return $this->_oOmegaInfo;
    }
}
