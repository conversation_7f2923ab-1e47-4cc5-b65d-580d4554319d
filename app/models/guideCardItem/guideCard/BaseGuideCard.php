<?php

namespace PreSale\Models\guideCardView\guideCard;

use PreSale\Models\guideCardItem\OmniOmegaInfo;

/**
 * 出口卡片
 */
abstract class BaseGuideCard
{
    private $_iGuidePos;

    private $_iRecType;

    private $_aOmegaParams;

    private $_aActionParams;

    // 卡片样式
    const GUIDE_TYPE_MEMBER       = 1; // 会员卡片样式
    const GUIDE_TYPE_POPUP_MEMBER = 2; // 会员卡片在弹窗中样式
    const GUIDE_TYPE_TIP          = 5; // 添加红包、追加车型、打表来接
    const GUIDE_TYPE_ANYCAR       = 3; // 追加车型
    const GUIDE_TYPE_RULE         = 4; // 预约规则
    const GUIDE_TYPE_PRE_CANCEL_ANYCAR = 6; // 挽留弹窗追加车型
    const GUIDE_TYPE_ITERATE_ANYCAR    = 7; // 等待应答迭代三期 追加车型出口卡片
    const GUIDE_HK_TAXI_TIP            = 8; // 香港出租车添加、追加红包卡片
    const GUIDE_TYPE_PRE_CANCEL_ANYCAR_V3 = 9; // 等待应答迭代三期 取消挽留弹窗追加车型卡片
    const GUIDE_TYPE_PRE_CANCEL_MULTI_ANYCAR = 14; // 取消挽留弹窗追加多车型卡片

    const GUIDE_TYPE_BOOKING_ORDER_PRE_CANCEL_NOW_GO = 17; // 取消挽留弹窗 预约单场景 立即出发卡片。

    const GUIDE_TYPE_PRE_CANCEL_FATHER = 24; // 新版加价调度卡片

    const GUIDE_TYPE_ANYCAR_NEW_STYLE = 29; // 新版追加车型卡片


    /**
     * @return mixed
     */
    public function getGuidePos() {
        return $this->_iGuidePos;
    }

    /**
     * @param mixed $iGuidePos guidePos
     * @return BaseGuideCard
     */
    public function setIGuidePos($iGuidePos) {
        $this->_iGuidePos = $iGuidePos;
        return $this;
    }

    /**
     * @return int $iRecType recType
     */
    public function getIRecType() {
        return $this->_iRecType;
    }

    /**
     * @param int $iRecType recType
     * @return void
     */
    public function setIRecType($iRecType) {
        $this->_iRecType = $iRecType;
    }

    /**
     * @return mixed
     */
    abstract public function getCardType();

    /**
     * @return OmniOmegaInfo
     */
    abstract public function getOmegaInfo();

    /**
     * @return array
     */
    public function getOmegaParams() {
        return $this->_aOmegaParams;
    }

    /**
     * @param array $aOmegaParams 埋点参数
     * @return void
     */
    public function setOmegaParams($aOmegaParams) {
        $this->_aOmegaParams = $aOmegaParams;
    }

    /**
     * @return array
     */
    public function getActionParams() {
        return $this->_aActionParams;
    }

    /**
     * @param array $aActionParams 埋点参数
     * @return void
     */
    public function setActionParams($aActionParams) {
        $this->_aActionParams = $aActionParams;
    }

    public $bEnable = true;
}
