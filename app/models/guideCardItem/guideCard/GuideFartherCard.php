<?php

namespace PreSale\Models\guideCardView\guideCard;

use PreSale\Models\guideCardItem\button\IBaseButton;

/**
 * 加价调度出口
 */
class GuideFartherCard extends BaseGuideCard implements \JsonSerializable
{
    /**
     * 主标题
     * @var string
     */
    private $_sTitle;

    /**
     * 副标题
     * @var array
     */
    private $_aSubTitle;

    /**
     * 按钮
     * @var IBaseButton
     */
    private $_oButton;

    /**
     * 埋点数据
     * @var array
     */
    private $_aOmegaInfo;

    /**
     * @return string
     */
    public function getTitle() {
        return $this->_sTitle;
    }

    /**
     * @param string $sTitle 主标题
     * @return void
     */
    public function setTitle($sTitle) {
        $this->_sTitle = $sTitle;
    }

    /**
     * @return array
     */
    public function getSubTitle() {
        return $this->_aSubTitle;
    }

    /**
     * @param array $aSubTitle 副标题
     * @return void
     */
    public function setSubTitle($aSubTitle) {
        $this->_aSubTitle = $aSubTitle;
    }

    /**
     * @return IBaseButton
     */
    public function getButton() {
        return $this->_oButton;
    }

    /**
     * @param IBaseButton $oButton 按钮
     * @return void
     */
    public function setButton($oButton) {
        $this->_oButton = $oButton;
    }

    /**
     * @return array
     */
    public function getOmegaInfo() {
        return $this->_aOmegaInfo;
    }

    /**
     * @param array $aOmegaInfo 埋点数据
     * @return void
     */
    public function setOmegaInfo(array $aOmegaInfo) {
        $this->_aOmegaInfo = $aOmegaInfo;
    }

    /**
     * @return array 获取卡片数据
     */
    private function _getCardData() {
        $aCardData = [
            'title'    => $this->_sTitle,
            'subtitle' => $this->_aSubTitle,
        ];
        if (!empty($this->_sPrice)) {
            $aCardData['price'] = $this->_sPrice;
        }

        if (null != $this->_oButton) {
            $aCardData['button'] = $this->_oButton;
        }

        if (!empty($this->_aOmegaInfo)) {
            $aCardData['omega_info'] = $this->_aOmegaInfo;
        }

        return $aCardData;
    }

    /**
     * @return mixed|void
     */
    public function getCardType() {
        return BaseGuideCard::GUIDE_TYPE_TIP;
    }

    /**
     * @return array
     */
    public function jsonSerialize() {
        return [
            'card_type' => BaseGuideCard::GUIDE_TYPE_TIP,
            'card_data' => $this->_getCardData(),
        ];
    }
}
