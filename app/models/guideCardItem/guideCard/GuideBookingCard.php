<?php

namespace PreSale\Models\guideCardItem\guideCard;

use PreSale\Models\guideCardItem\button\IBaseButton;
use PreSale\Models\guideCardItem\EstimateInfo;
use PreSale\Models\guideCardItem\OmniOmegaInfo;
use PreSale\Models\guideCardView\guideCard\BaseGuideCard;

class GuideBookingCard  extends BaseGuideCard implements \JsonSerializable
{

    /**
     * 标题
     * @var string
     */
    private $_sTitle;

    /**
     * @var string
     */
    private $_sSubTitle;

    /**
     * @var EstimateInfo
     */
    private $_oEstimateInfo;


    /**
     * 埋点数据
     * @var OmniOmegaInfo
     */
    private $_aOmegaInfo;

    /**
     * 按钮
     * @var IBaseButton
     */
    private $_oButton;

    /**
     * @return string
     */
    public function getTitle() {
        return $this->_sTitle;
    }

    /**
     * @param string $sTitle 主标题
     * @return void
     */
    public function setTitle($sTitle) {
        $this->_sTitle = $sTitle;
    }

    /**
     * @return string
     */
    public function getSubTitle() {
        return $this->_sSubTitle;
    }

    /**
     * @param string $sSubTitle 副标题
     * @return void
     */
    public function setSubTitle($sSubTitle) {
        $this->_sSubTitle = $sSubTitle;
    }

    /**
     * @return EstimateInfo
     */
    public function getEstimateInfo() {
        return $this->_oEstimateInfo;
    }

    /**
     * @param EstimateInfo $oEstimateInfo 预估数据
     * @return void
     */
    public function setEstimateInfo($oEstimateInfo) {
        $this->_oEstimateInfo = $oEstimateInfo;
    }

    /**
     * @return OmniOmegaInfo
     */
    public function getOmegaInfo() {
        return $this->_aOmegaInfo;
    }

    /**
     * @param OmniOmegaInfo $aOmegaInfo 埋点数据
     * @return void
     */
    public function setOmegaInfo($aOmegaInfo) {
        $this->_aOmegaInfo = $aOmegaInfo;
    }

    /**
     * @return IBaseButton
     */
    public function getButton() {
        return $this->_oButton;
    }

    /**
     * @param IBaseButton $oButton 按钮
     * @return void
     */
    public function setButton($oButton) {
        $this->_oButton = $oButton;
    }

    /**
     * @return array
     */
    private function _getCardData() {
        $aRet = ['title' => $this->_sTitle];
        if (!empty($this->_sSubTitle)) {
            $aRet['sub_title'] = $this->_sSubTitle;
        }

        if (!empty($this->_oEstimateInfo)) {
            $aRet['estimate_info'] = $this->_oEstimateInfo->render();
        }

        if (!empty($this->_aOmegaInfo)) {
            $aRet['omega_info'] = $this->_aOmegaInfo;
        }

        if (null != $this->_oButton) {
            $aRet['button'] = $this->_oButton;
        }

        return $aRet;
    }

    /**
     * @return int|mixed
     */
    public function getCardType() {
        return BaseGuideCard::GUIDE_TYPE_BOOKING_ORDER_PRE_CANCEL_NOW_GO;
    }

    /**
     * @return array
     */
    public function jsonSerialize() {
        return [
            'card_type' => BaseGuideCard::GUIDE_TYPE_BOOKING_ORDER_PRE_CANCEL_NOW_GO,
            'card_data' => $this->_getCardData(),
        ];
    }
}