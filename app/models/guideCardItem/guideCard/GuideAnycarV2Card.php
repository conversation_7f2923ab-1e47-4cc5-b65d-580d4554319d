<?php

namespace PreSale\Models\guideCardView\guideCard;

use PreSale\Models\guideCardItem\OmniOmegaInfo;

/**
 * 追加车型推荐新样式
 */
class GuideAnycarV2Card extends BaseGuideCard implements \JsonSerializable
{

    /**
     * 标题
     * @var string
     */
    private $_sTitle;

    /**
     * 副标题
     * @var string
     */
    private $_sSubTitle;

    /**
     * Icon
     * @var string
     */
    private $_sIcon;

    /**
     * 费用描述
     * @var string
     */
    private $_sFeeDesc;

    /**
     * 埋点数据
     * @var OmniOmegaInfo
     */
    private $_aOmegaInfo;

    /**
     * @return string
     */
    public function getTitle() {
        return $this->_sTitle;
    }

    /**
     * @param string $sTitle 主标题
     * @return void
     */
    public function setTitle($sTitle) {
        $this->_sTitle = $sTitle;
    }

    /**
     * @return string
     */
    public function getSubTitle() {
        return $this->_sSubTitle;
    }

    /**
     * @param string $sSubTitle 副标题
     * @return void
     */
    public function setSubTitle($sSubTitle) {
        $this->_sSubTitle = $sSubTitle;
    }

    /**
     * @return string
     */
    public function getIcon() {
        return $this->_sIcon;
    }

    /**
     * @param string $sIcon 图标
     * @return void
     */
    public function setIcon($sIcon) {
        $this->_sIcon = $sIcon;
    }

    /**
     * @return string
     */
    public function getFeeDesc() {
        return $this->_sFeeDesc;
    }

    /**
     * @param string $sFeeDesc 费用字段
     * @return void
     */
    public function setFeeDesc($sFeeDesc) {
        $this->_sFeeDesc = $sFeeDesc;
    }

    /**
     * @return OmniOmegaInfo
     */
    public function getOmegaInfo() {
        return $this->_aOmegaInfo;
    }

    /**
     * @param OmniOmegaInfo $aOmegaInfo 埋点数据
     * @return void
     */
    public function setOmegaInfo($aOmegaInfo) {
        $this->_aOmegaInfo = $aOmegaInfo;
    }

    /**
     * @return array
     */
    private function _getCardData() {
        $aRet = ['title' => $this->_sTitle];
        if (!empty($this->_sSubTitle)) {
            $aRet['sub_title'] = $this->_sSubTitle;
        }

        if (!empty($this->_sIcon)) {
            $aRet['icon'] = $this->_sIcon;
        }

        if (!empty($this->_aOmegaInfo)) {
            $aRet['omega_info'] = $this->_aOmegaInfo;
        }

        if (!empty($this->_sFeeDesc)) {
            $aRet['fee_desc'] = $this->_sFeeDesc;
        }

        return $aRet;
    }

    /**
     * @return int
     */
    public function getCardType() {
        return BaseGuideCard::GUIDE_TYPE_ANYCAR_NEW_STYLE;
    }

    /**
     * @return array
     */
    public function jsonSerialize() {
        return [
            'card_type' => BaseGuideCard::GUIDE_TYPE_ANYCAR_NEW_STYLE,
            'card_data' => $this->_getCardData(),
        ];
    }
}
