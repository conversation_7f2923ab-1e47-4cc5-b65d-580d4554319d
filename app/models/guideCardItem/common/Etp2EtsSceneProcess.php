<?php

namespace PreSale\Models\guideCardItem\common;

use BizLib\Config as NuwaConfig;
use BizLib\Utils\Language;
use BizLib\Utils\Product;
use BizLib\Utils\ProductCategory;

/**
 * @desc: Etp2Ets处理方法实现
 * 需求文档：http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=1088360549
 * 技术方案：https://cooper.didichuxing.com/knowledge/2200846284313/2200886380722
 * @author: jie.zhang
 * @date: 2023/9/15 17:35
 */
class Etp2EtsSceneProcess
{
    const SEC_REPLACE       = 1;//文案操作模式，秒替换
    const MIN_REPLACE       = 2;//文案操作模式，分钟替换
    const NO_REPLACE        = 3;//不替换
    const DEFAULT_ETS       = 60;//默认ETS值
    const DEFAULT_CAR_MODEL = '0'; //默认车型
    const SYMBOL_EQ         = 0;//等于
    const SYMBOL_GT         = 1;//大于
    const SYMBOL_LT         = 2;//小于

    const SCENE_FLAG_DEFAULT     = 0;
    const SCENE_FLAG_QUICK_REPLY = 1;
    const SCENE_FLAG_LINEUP      = 2;


    /**
     * @var self
     * 当前类的事例
     */
    protected static $_aInstance;

    /**
     * @return string
     */
    public static function name() {
        return get_called_class();
    }

    /**
     * @return Etp2EtsSceneProcess|static
     */
    public static function getInstance() {
        if (isset(self::$_aInstance[static::name()])) {
            return self::$_aInstance[static::name()];
        }

        return self::$_aInstance[static::name()] = new static();
    }

    /**
     * 获取ets文案
     * @param int    $iEts         ets值
     * @param array  $aCondCfg     配置好的条件
     * @param string $sDocKey      文案key
     * @param array  $aProductItem 产品线数据
     * @Param int    $iIsLineup    是否是排队场景
     * @return mixed|string
     */
    public function getEtsStr(int $iEts, array $aCondCfg, string $sDocKey,array $aProductItem=array(),int $iIsLineup = -1) {
        $iIsQueueProduct = $this->_isQueueCarProduct($aProductItem);
        $sText           = '';
        $aEtsText        = Language::getDecodedTextFromDcmp('pre_cancel-'.$sDocKey);
        foreach ($aCondCfg as $item) {
            $iLt      = $item['lt'];
            $iGe      = $item['ge'];
            $sTextKey = $item['text_key'];
            if (empty($iGe) || $iGe < 0) {
                $iGe = PHP_INT_MAX;
            }

            if (empty($item['format'])) {
                $item['format'] = self::NO_REPLACE;
            }

            if ($iIsQueueProduct != -1 && !empty($item['is_queue_product']) && $iIsQueueProduct != $item['is_queue_product']) {
                continue;
            }

            if (!empty($item['is_queue_product']) && $iIsQueueProduct == -1) {
                continue;
            }

            if ($iIsLineup != -1 && !empty($item['is_lineup']) && $iIsLineup != $item['is_lineup']) {
                continue;
            }

            if (!empty($item['is_lineup']) && $iIsLineup == -1) {
                continue;
            }

            if (!empty($item['ranking_left']) && !empty($aProductItem) && !empty($aProductItem['ranking']) && $aProductItem['ranking'] <= $item['ranking_left']) {
                //ranking_left 排队人数边界
                continue;
            }

            if (!empty($item['ranking_left']) && (empty($aProductItem) || empty($aProductItem['ranking']))) {
                continue;
            }

            if (!empty($item['product_category_list']) && is_array($item['product_category_list']) && !empty($aProductItem) && !empty($aProductItem['product_category']) && !in_array($aProductItem['product_category'],$item['product_category_list'])) {
                //品类判断
                continue;
            }

            if (!empty($item['product_category_list'] && (empty($aProductItem) || empty($aProductItem['product_category'])))) {
                continue;
            }

            if (!empty($item['lt']) && !empty($iEts) && $iEts <= $iLt) {
                continue;
            }

            if (!empty($item['lt']) && empty($iEts)) {
                continue;
            }

            if(!empty($iEts) && $iEts > $iGe) {
                continue;
            }

            if (empty($aProductItem['ranking'])) {
                $sText = $this->_getProductItemText($aEtsText[$sTextKey],$iEts,$item['format']);
                break;
            }
            $sText = $this->_getProductItemText($aEtsText[$sTextKey],$iEts,$item['format'],$aProductItem['ranking']);
            break;
        }

        return $sText;
    }

    /**
     * @param mixed $mLeftVal    左值
     * @param int   $iSymbolType 符号
     * @param mixed $mRightValue 右值
     * @return bool
     */
    private function _getOneExpResult($mLeftVal, int $iSymbolType, $mRightValue) {
        if (!isset($mLeftVal) || !isset($mRightValue)) {
            return false;
        }

        switch ($iSymbolType) {
            case self::SYMBOL_EQ:
                return in_array($mLeftVal,$mRightValue);
            case self::SYMBOL_LT:
                return $mLeftVal < $mRightValue;
            case self::SYMBOL_GT:
                return $mLeftVal > $mRightValue;
        }

        return false;
    }

    /**
     * @param array $aOneCondition 单个串联&&表达式 eg.（a<b）&&(c>d)
     * @param array $aNTuple       产品线n元组
     * @return bool
     */
    private  function _getOneConditionResult(array $aOneCondition, array $aNTuple) {
        foreach ($aOneCondition as $item) {
            $mLeftVal    = $aNTuple[$item['field']];
            $iSymbolType = $item['symbol_type'];
            $mRightValue = $item['val'];
            if (!$this->_getOneExpResult($mLeftVal,$iSymbolType,$mRightValue)) {
                return false;
            }
        }

        return true;
    }

    /**
     * @param array $aCondition 单个车型的所有表达式
     * @param array $aNTuple    品类n元组
     * @return bool
     */
    private function _getAllConditionResult(array $aCondition, array $aNTuple) {
        foreach ($aCondition as $item) {
            if ($this->_getOneConditionResult($item,$aNTuple)) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param array $aProductItem 产品线n元组
     * @return int|mixed
     */
    public function getUndertakeEts(array $aProductItem) {
        $aUndertakeEtsCfg = NuwaConfig::getBizConfig('fail_ets_undertake_config','undertake_ets_config');
        $aNTuple2carModel = NuwaConfig::getBizConfig('fail_ets_undertake_config','ntuple_to_car_model');
        $aNTupleMap       = $aNTuple2carModel['ntuple_map'];
        $aNTuple          = [];
        foreach ($aNTupleMap as $sKey => $sVal) {
            $aNTuple[$sKey] = $aProductItem[$sVal];
        }

        $aConditionConfig = $aNTuple2carModel['condition_config'];
        $aCarModel        = self::DEFAULT_CAR_MODEL;
        foreach ($aConditionConfig as $sKey => $aCondition) {
            if ($this->_getAllConditionResult($aCondition,$aNTuple)) {
                $aCarModel = $sKey;
                break;
            }
        }

        if (!empty($aUndertakeEtsCfg[$aCarModel]['ets'])) {
            return $aUndertakeEtsCfg[$aCarModel]['ets'];
        }

        return self::DEFAULT_ETS;
    }


    /**
     * 稳步占位符替换
     * @param string $etsText 文案
     * @param int    $ets     ets值
     * @param int    $format  格式类型
     * @param int    $ranking 排队
     * @return mixed|string
     */
    private function _getProductItemText(string $etsText, int $ets, int $format, int $ranking=0) {
        $sText = '';
        switch ($format) {
            case self::SEC_REPLACE:
                $sText = Language::replaceTag($etsText, ['ets_time' => $ets, 'ranking' => $ranking]);
                break;
            case self::MIN_REPLACE:
                $sText = Language::replaceTag($etsText, ['ets_time' => ceil($ets / 60), 'ranking' => $ranking]);
                break;
            case self::NO_REPLACE:
                $sText = $etsText;
                break;
            default:
                $sText = $etsText;
        }

        return $sText;
    }

    /**
     * @param int $iTargetEts 推荐车型的ets
     * @param int $iMaxEts    发单车型最小ets
     * @return int
     */
    public function getRelativeEtsMin(int $iTargetEts, int $iMaxEts) {
        if (!isset($iTargetEts)||!isset($iMaxEts)) {
            return 0;
        }

        return floor(($iMaxEts - $iTargetEts) / 60);
    }


    /**
     * 判断是否是排队车型
     * @param array $aProductItem 车型信息
     * @return int
     */
    private function _isQueueCarProduct(array $aProductItem) {
        if (empty($aProductItem) || empty($aProductItem['scene_flag'])) {
            return -1;
        }
        if (self::SCENE_FLAG_LINEUP == $aProductItem['scene_flag']) {
            return 1;
        }
        return 0;
    }
}
