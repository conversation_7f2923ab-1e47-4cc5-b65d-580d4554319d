<?php

namespace PreSale\Models\guideCardItem\common;

use Biz<PERSON>ommon\Models\Order\OrderPromise;
use BizLib\Constants\OrderSystem;
use BizLib\Utils\ApolloHelper;
use BizLib\Utils\Language;
use BizLib\Utils\Product;
use BizLib\Utils\UtilHelper;
use BizLib\Constants;
use PreSale\Models\guideCardItem\MatchResultRuntime;
use Xiaoju\Apollo\Apollo;
use Xiaoju\Apollo\ApolloConstant;

/**
 * Class SceneManager
 * @package Transaction\DuKang\Model\MatchResult
 */
class SceneManager
{
    /**
     * @var SceneManager
     */
    private static $_instance;

    /**
     * @var MatchResultRuntime $oRuntimeData
     */
    private $_oRuntimeData;

    /**
     * @var bool
     */
    private $_bLineUp;


    /**
     * SceneManager constructor.
     * @param MatchResultRuntime $oRuntimeData 数据总线
     * @return void
     */
    public function __construct(MatchResultRuntime $oRuntimeData) {
        $this->_oRuntimeData = $oRuntimeData;
    }

    /**
     * @return bool
     */
    public function isLineUp() {
        return $this->_bLineUp;
    }

    /**
     * @return SceneManager
     */
    public function setLineUp() {
        if (isset($this->_bLineUp)) {
            return $this;
        }

        $this->_bLineUp = false;
        if ($this->_isLineUp()) {
            $this->_bLineUp = true;
        }

        return $this;
    }


    /**
     * @param MatchResultRuntime $oRuntimeData 数据总线
     * @return SceneManager
     */
    public static function getInstance(MatchResultRuntime $oRuntimeData) {
        if (self::$_instance == null) {
            self::$_instance = new self($oRuntimeData);
        }

        return self::$_instance;
    }

    /**
     * @return bool
     */
    private function _isLineUp() {
        $aOrderInfo  = $this->_oRuntimeData->getOrderInfo();

        if (! UtilHelper::checkOrderExtraType($aOrderInfo['extra_type'], ['o_lineup'])) {
            return false;
        }

        return true;
    }
}
