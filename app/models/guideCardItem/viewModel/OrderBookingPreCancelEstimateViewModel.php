<?php

namespace PreSale\Models\guideCardView\viewModel;

use BizLib\Client\MambaClient;
use BizLib\Libraries\RedisDB;
use BizLib\Utils\Common;
use BizLib\Utils\Product;
use BizLib\Utils\ProductCategory;
use BizLib\Utils\UtilHelper;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TaxiPricingBoxLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TpForm;
use PreSale\Models\guideCardItem\MatchResultRuntime;

/**
 * 预约单 挽留弹窗 预估 Model
 */
class OrderBookingPreCancelEstimateViewModel extends BaseViewModel
{

    /**
     * @var OrderBookingPreCancelEstimateViewModel
     */
    private static $_oInstance = null;

    /**
     * 预估数据
     * @var array
     */
    public $aOrderBookingPreCaneclEstimate;

    /**
     * @param MatchResultRuntime $oMatchResultRuntime 运行时数据总线
     * @return AnycarEstimateViewModel
     */
    public static function getInstance($oMatchResultRuntime) {
        if (self::$_oInstance == null) {
            self::$_oInstance = new OrderBookingPreCancelEstimateViewModel($oMatchResultRuntime);
        }

        return self::$_oInstance;
    }

    /**
     * @return void
     */
    public function doBuild() {
        $aRequest   = $this->_oRuntimeData->getRequest();
        $aOrderInfo = $this->_oRuntimeData->getOrderInfo();

        $this->aOrderBookingPreCaneclEstimate = $this->_getBookingCancelEstimate($aRequest, $aOrderInfo, "pre-sale");

    }

    /**
     * @param $aRequest
     * @param $aOrderInfo
     * @param $sFrom
     * @return array|mixed
     */
    private function _getBookingCancelEstimate($aRequest, $aOrderInfo, $sFrom) {
        $iSeatNum     = in_array($aRequest['carpool_seat_num'], [1, 2]) ? $aRequest['carpool_seat_num'] : 1;
        $oClient      = new MambaClient();
        $aEstimateReq = [
            'token'                 => $aRequest['token'],
            'order_id'              => UtilHelper::encodeId($aOrderInfo['order_id'], $aOrderInfo['district']),
            'lang'                  => $aRequest['lang'] ?? 'zh-CN',
            'carpool_seat_num'      => $iSeatNum,
            'multi_require_product' => $aRequest['multi_require_product'] ?? '[]',
            'app_version'           => $aRequest['appversion'] ?? $aRequest['app_version'],
            'datatype'              => $aRequest['datatype'],
            'channel'               => $aRequest['channel'],
            'from'                  => $sFrom,
            'v6_version'            => $aRequest['v6_version'] ?? false,
            'access_key_id'         => $aRequest['access_key_id'],
            'client_type'           => $aRequest['client_type'],
        ];

        $aEstimateResp =  $oClient->pOrderBookingPreCancelEstimate($aEstimateReq);
        if (isset($aEstimateResp['errno']) && 0 == $aEstimateResp['errno'] && !empty($aEstimateResp['data'])) {
            $aEstimateData = $aEstimateResp['data'];
            array_shift($aEstimateData)['is_selected'] = 1;
            $aEstimateData['seat_num'] = $iSeatNum;
        } else {
            $aEstimateData = []; // 设置一个空缓存
        }

        return $aEstimateData;
    }
}
