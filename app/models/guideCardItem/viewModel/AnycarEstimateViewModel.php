<?php

namespace PreSale\Models\guideCardView\viewModel;

use BizCommon\Utils\Horae;
use BizLib\Client\Cache\CarpoolRedisClient;
use BizLib\Client\MambaClient;
use BizLib\Libraries\RedisDB;
use BizLib\Log;
use BizLib\Utils\Common;
use BizLib\Utils\Product;
use BizLib\Utils\ProductCategory;
use BizLib\Utils\UtilHelper;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TaxiPricingBoxLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TpForm;
use PreSale\Models\guideCardItem\GuideFactory;
use PreSale\Models\guideCardItem\MatchResultRuntime;
use Xiaoju\Apollo\Apollo;

/**
 * 获取追加车型预估数据
 */
class AnycarEstimateViewModel extends BaseViewModel
{
    const NO_BOX_LAYOUT = 0;    // 不包含盒子的layout
    const NORMAL_LAYOUT = 1;    // 正常layout

    const SHORT_DISTANCE_GROUP_TYPE = 2;    // 三方盒子
    const TAXI_GROUP_TYPE           = 3;    // 出租车盒子
    const PEAK_GROUP_TYPE           = 4;    // 高峰盒子
    const TP_GROUP_TYPE = 5;    // TP盒子
    const TAXI_PRICING_BOX_GROUP_TYPE = 6;  // 出租车计价盒子

    const ATHENA_GUIDE_SOURCE_CANCEL_DETAINMENT = 3; // 取消挽留弹窗中推荐卡片点击的导流

    /**
     * @var AnycarEstimateViewModel
     */
    private static $_oInstance = null;

    /**
     * 预估数据
     * @var array
     */
    public $aAnycarEstimate;

    /**
     * @param MatchResultRuntime $oMatchResultRuntime 运行时数据总线
     * @return AnycarEstimateViewModel
     */
    public static function getInstance($oMatchResultRuntime) {
        if (self::$_oInstance == null) {
            self::$_oInstance = new AnycarEstimateViewModel($oMatchResultRuntime);
        }

        return self::$_oInstance;
    }

    /**
     * @return void
     */
    public function doBuild() {
        $aRequest   = $this->_oRuntimeData->getRequest();
        $aOrderInfo = $this->_oRuntimeData->getOrderInfo();
        $this->aAnycarEstimate = $this->_getAnycarEstimateCache($aRequest, $aOrderInfo);
        if (isset($aRequest['carpool_seat_num']) && empty($this->aAnycarEstimate)) {
            $this->aAnycarEstimate = $this->_getAnycarEstimate($aRequest, $aOrderInfo, 'anycar_v3');
        }

        //价差沟通屏蔽
        if (!empty($this->aAnycarEstimate['layout'])) {
            $this->aAnycarEstimate['layout'] = $this->_buildPriceDiffNotice($this->aAnycarEstimate['layout']);
        }
    }

    /**
     * @desc 多车型推荐不出价差提醒
     * @param array $aLayout $aLayout
     * @return array
     */
    private function _buildPriceDiffNotice($aLayout) {
        //多车型推荐不展示价差沟通。这里之所以改成0是因为用了预估渲染的数据
        if (is_array($aLayout)) {
            $aNewLayout = [];
            foreach ($aLayout as $key => $aLayoutItem) {
                if (is_array($aLayoutItem['groups'])) {
                    $aFixGroups = [];
                    foreach ($aLayoutItem['groups'] as $k => $aGroupItem) {
                        $aGroupItem['is_diff_amount']    = 0; // 是否是差价模式
                        $aGroupItem['is_hide_diff_desc'] = 0; // 差价模式下是否显示预估
                        $aFixGroups[$k] = $aGroupItem;
                    }

                    $aLayoutItem['groups'] = $aFixGroups;
                }

                $aNewLayout[$key] = $aLayoutItem;
            }

            $aLayout = $aNewLayout;
        }

        Log::info('price diff hide');
        return $aLayout;
    }

    /**
     * 从等待应答缓存处取出预估数据
     * @param array $aRequest   请求参数
     * @param array $aOrderInfo 订单信息
     * @return array 预估数据
     */
    private function _getAnycarEstimateCache($aRequest, $aOrderInfo) {
        $iDefaultSeatNum = $this->_buildCarpoolSeatNum($aRequest, $aOrderInfo);
        $iSeatNum        = in_array($aRequest['carpool_seat_num'], [1, 2]) ? $aRequest['carpool_seat_num'] : $iDefaultSeatNum;
        Log::info(sprintf('_getAnycarEstimateCache SeatNum %s, Request carpool_seat_num %s', $iSeatNum, $aRequest['carpool_seat_num']));
        if ($this->_oRuntimeData->hitOrderMatchTraffic()) {
            // hitOrderMatchTraffic 调用AnyCarEstimateV4接口获取数据
            $aEstimateData = $this->_getAnycarEstimate($aRequest, $aOrderInfo, "");
        } else {
            $sKey = Common::getRedisPrefix(P_GUIDE_ANYCAR_ESTIMATE_PRICE) . $aOrderInfo['order_id'];
            $sRet = RedisDB::getInstance()->get($sKey);
            if ($sRet === null) { //  redis连接失败
                $aEstimateData = [];
            } elseif (false === $sRet) { //  未查到该key
                $aEstimateData = [];
            } else {
                $aEstimateData = json_decode($sRet, true);
            }
        }

        //  如果dos没有拼车座位数，也没有切座，那以缓存最新的为准
        if (isset($aEstimateData['seat_num']) && 0 == $iSeatNum) {
            $iSeatNum = $aEstimateData['seat_num'];
            Log::info(sprintf('is use aEstimateData seat_num SeatNum %s', $iSeatNum));
        }

        if (isset($aEstimateData['seat_num']) && $aEstimateData['seat_num'] != $iSeatNum) { // 座位数不一致，也当做是未查到key
            $aEstimateData = [];
        }
        return $aEstimateData;
    }

    /**
     * @param array  $aRequest   请求参数
     * @param array  $aOrderInfo 订单信息
     * @param string $sFrom      来源
     * @return array 预估数据
     */
    private function _getAnycarEstimate($aRequest, $aOrderInfo, $sFrom) {
        $iDefaultSeatNum = $this->_buildCarpoolSeatNum($aRequest, $aOrderInfo);
        $iSeatNum        = in_array($aRequest['carpool_seat_num'], [1, 2]) ? $aRequest['carpool_seat_num'] : $iDefaultSeatNum;
        Log::info(sprintf('getAnycarEstimate SeatNum %s, Request carpool_seat_num %s', $iSeatNum, $aRequest['carpool_seat_num']));
        $oClient = new MambaClient();
        $aEstimateReq = [
            'token'                 => $aRequest['token'],
            'order_id'              => UtilHelper::encodeId($aOrderInfo['order_id'], $aOrderInfo['district']),
            'lang'                  => $aRequest['lang'] ?? 'zh-CN',
            'carpool_seat_num'      => $iSeatNum,
            'multi_require_product' => $aRequest['multi_require_product'] ?? '[]',
            'app_version'           => $aRequest['appversion'] ?? $aRequest['app_version'],
            'datatype'              => $aRequest['datatype'],
            'channel'               => $aRequest['channel'],
            'from'                  => $sFrom,
            'v6_version'            => $aRequest['v6_version'] ?? false,
            'access_key_id'         => $aRequest['access_key_id'],
            'client_type'           => $aRequest['client_type'],
        ];
        if ($this->_oRuntimeData->hitOrderMatchTraffic()) {
            $aEstimateResp = $oClient->pAnyCarEstimateV4($aEstimateReq);
        } elseif ($this->_oRuntimeData->isIterationSwitchV3()) {
            $aEstimateResp = $oClient->anycarEstimateV3($aEstimateReq);
        } else {
            $aEstimateResp = $oClient->pAnyCarEstimate($aEstimateReq);
        }

        if (isset($aEstimateResp['errno']) && 0 == $aEstimateResp['errno'] && !empty($aEstimateResp['data'])) {
            $aEstimateData = $aEstimateResp['data'];
            $aEstimateData['seat_num'] = $iSeatNum;
        } else {
            $aEstimateData = []; // 设置一个空缓存
        }

        if ($this->_oRuntimeData->hitOrderMatchTraffic()) {
            $aEstimateData = $aEstimateData['estimate_info'] ?? [];
        }

        //小巴etp缓存，只做V3
        if (\BizLib\Constants\Common::ESTIMATE_TYPE_CONFIRM == $this->_oRuntimeData->isIterationSwitchV3() && !empty($aEstimateData) && !empty($aEstimateData['raw_estimate_data'])) {
            $aRet = [];
            foreach ($aEstimateData['raw_estimate_data'] as $sPc => $aData) {
                if (Horae::isMiniBusCarpool($aData)) {
                    $aRet[$aData['estimate_id']] = $aData;
                }
            }

            $iExpireTime = 90;
            $sKey        = 'P_CARPOOL_XIAOBA_ESTIMATE_DATA_' . $aEstimateData['estimate_trace_id'];
            $sRet        = json_encode($aRet);
            CarpoolRedisClient::getInstance()->setex($sKey, $iExpireTime, $sRet);
        }

        return $aEstimateData;
    }

    /**
     * 拼车座位数 不选默认是1；此处处理是：如果乘客未选座位数，则用已选拼车的座位数作为默认
     * @param array $aRequest   oRequest
     * @param array $aOrderInfo OrderInfo
     * @return int
     */
    private function _buildCarpoolSeatNum($aRequest, $aOrderInfo) {
        // 如果乘客主动选择了座位数
        if (!empty($aRequest['carpool_seat_num'])) {
            return $aRequest['carpool_seat_num'];
        }

        // 如果乘客没有主动勾选座位数
        $aExtendFeature       = json_decode($aOrderInfo['extend_feature'], true) ?? [];
        $aMultiRequireProduct = $aExtendFeature['multi_require_product'] ?? [];
        // 且已呼叫市内拼车 则用其座位数
        foreach ($aMultiRequireProduct as $aProduct) {
            if ((Horae::isLowPriceCarpool($aProduct) || Horae::isDualPriceCarpoolV3($aProduct) || Horae::isMiniBusCarpool($aProduct))) {
                return $aProduct['passenger_count'];
            }
        }

        return 0;
    }

    /**
     * 获取预估数据
     * @return array
     */
    public function getProductList() {
        if (!$this->_oRuntimeData->isIterationSwitchV3() && !$this->_oRuntimeData->hitOrderMatchTraffic()) {
            return $this->aAnycarEstimate['product_list'];
        } else {
            // 从v3的结构中取预估数据
            $aEstimateData = $this->aAnycarEstimate['estimate_data'];
            if (!is_array($aEstimateData)) {
                return $aEstimateData;
            }

            foreach ($aEstimateData as $iProductCategory => &$aEstimateItem) {
                // scene_info中的数据移动到外层
                $aEstimateItem = array_merge($aEstimateItem, $aEstimateItem['scene_info']);
                if (array_key_exists('p_new_order_params', $aEstimateItem)) {
                    $aEstimateItem = array_merge($aEstimateItem, $aEstimateItem['p_new_order_params']);
                }

                // 处理price和business_name
                $aEstimateItem['price']         = (float)$aEstimateItem['fee_amount'];
                $aEstimateItem['business_name'] = $aEstimateItem['car_title'];
                // 处理 seat_nums
                if (!empty($aEstimateItem['carpool_seat_list'])) {
                    foreach ($aEstimateItem['carpool_seat_list'] as $aCarpoolSeatItem) {
                        $aEstimateItem['seat_nums'][] = [
                            'text'        => $aCarpoolSeatItem['label'],
                            'num'         => $aCarpoolSeatItem['value'],
                            'is_selected' => $aCarpoolSeatItem['selected'],
                        ];
                    }
                }

                //获取标签数据
                // 标签放入 side_extra下的sub_title_list
                if (!empty($aEstimateItem['sub_title_list'])) {
                    $aEstimateItem['side_extra']['sub_title_list'] = $aEstimateItem['sub_title_list'];
                }
            }

            return $aEstimateData;
        }
    }

    /**
     * 获取追加车型预估v3接口的layout数据
     * @param array $aProductList 需要展示的车型数据
     * @return array
     */
    public function getAnycarEstimateLayout($aProductList = [], $iLayoutType = self::NORMAL_LAYOUT) {
        if ($this->_oRuntimeData->isIterationSwitchV3()) {
            switch ($iLayoutType) {
                case self::NORMAL_LAYOUT:
                    $aLayout = [];
                    if (!empty($aProductList)) {
                        $aProductCategotyList = [];
                        foreach ($aProductList as $key => $aProductItem) {
                            $aProductCategotyList[] = $aProductItem['product_category'];
                        }

                        if (!empty($this->aAnycarEstimate['layout'])) {
                            foreach ($this->aAnycarEstimate['layout'] as $key => $aLayoutItem) {
                                $aGroup = $aLayoutItem['groups'][0];
                                $bNeed  = true;
                                if (!empty($aGroup['products'])) {
                                    foreach ($aGroup['products'] as $key => $iProductCategory) {
                                        if (!in_array($iProductCategory, $aProductCategotyList)) {
                                            $bNeed = false;
                                            break;
                                        }
                                    }
                                }

                                if ($bNeed) {
                                    $aLayout[] = $aLayoutItem;
                                }
                            }
                        }
                    } else {
                        $aLayout = $this->aAnycarEstimate['layout'];
                    }
                    return $aLayout;
                case self::NO_BOX_LAYOUT:
                    return $this->_getAnycarEstimateLayoutNoBox($aProductList);
                default:
                    return array();
            }
        }

        return array();
    }

    /**
     * 获取追加车型预估v3接口的layout数据，有盒子的情况下转换为非盒子
     *
     * @param array $aProductList productList
     * @return array
     */
    private function _getAnycarEstimateLayoutNoBox($aProductList = []) {
        if ($this->_oRuntimeData->isIterationSwitchV3()) {
            $aNoBoxLayout = [];
            $aLayout      = $this->aAnycarEstimate['layout'];
            $aProductCategotyList = [];
            foreach ($aProductList as $key => $aProductItem) {
                $aProductCategotyList[] = $aProductItem['product_category'];
            }

            $bHasProductList = !empty($aProductCategotyList);
            foreach ($aLayout as $aLayoutItem) {
                if (empty($aLayoutItem)) {
                    continue;
                }

                foreach ($aLayoutItem['groups'] as $aGroup) {
                    $aNewLayout = $aLayoutItem;
                    $this->_reBuildTaxiDoublePriceMerge($aGroup);
                    $this->_reBuildTpFormProductCategory($aProductCategotyList,$aGroup);
                    $aProducts = $aGroup['products'];
                    $aNewLayout['groups'] = [$aGroup];
                    $aNewLayout['theme_data'] = null;
                    if (empty($aProducts)) {
                        continue;
                    }

                    // 盒子
                    if ($this->_isBox($aGroup)) {
                        foreach ($aProducts as $iGroupProduct) {
                            if (!$bHasProductList || in_array($iGroupProduct, $aProductCategotyList)) {
                                $aGroup['products']        = [$iGroupProduct];
                                $aGroup['type']            = 1;
                                $aGroup['car_title']       = '';
                                $aGroup['car_icon']        = '';
                                $aGroup['popup_title']     = '';
                                $aGroup['popup_sub_title'] = '';
                                $aNoBoxLayoutItem          = $aNewLayout;
                                $aNoBoxLayoutItem['groups'] = [$aGroup];
                                $aNoBoxLayout[] = $aNoBoxLayoutItem;
                            }
                        }
                    } elseif (!$bHasProductList || in_array($aProducts[0], $aProductCategotyList)) {
                        $aNoBoxLayout[] = $aNewLayout;
                    }
                }
            }

            return $aNoBoxLayout;
        }

        return array();
    }

    /**
     * 判断是否为盒子车型
     *
     * @param array $aGroup group
     * @return bool
     */
    private function _isBox($aGroup) {
        $iType         = $aGroup['type'];
        $aBoxTypeArray = [
            self::SHORT_DISTANCE_GROUP_TYPE,
            self::TAXI_GROUP_TYPE,
            self::PEAK_GROUP_TYPE,
            self::TP_GROUP_TYPE,
            self::TAXI_PRICING_BOX_GROUP_TYPE,
        ];
        if (in_array($iType, $aBoxTypeArray)) {
            return true;
        }

        return false;
    }

    /**
     * @param array $aProductCategotyList aProductCategotyList
     * @param array $aGroup               aGroup
     * @return void
     */
    private function _reBuildTpFormProductCategory($aProductCategotyList, &$aGroup) {
        $this->buildOnce();
        $aEstimateData = $this->getProductList();
        $aOrderInfo    = $this->_oRuntimeData->getOrderInfo();
        $iOrderId      = $aOrderInfo['order_id'];
        $iUserId       = UtilHelper::getUidByPassengerId($aOrderInfo['passenger_id'], \BizLib\Constants\Common::BIZ_TYPE_PASSENGER);
        $aProducts     = $aGroup['products'];
        $aHackProducts = [];
        foreach ($aProductCategotyList as $iProductCategory) {
            $aEstimateInfo = $aEstimateData[$iProductCategory];
            $sEstimateID   = $aEstimateInfo['estimate_id'];
            $iProductId    = Product::getProductIdByBusinessId($aEstimateInfo['business_id']);
            if (TpForm::isHitTpForm($iOrderId,$iProductId,$sEstimateID) && in_array($iProductCategory,$aProducts)) {
                array_push($aHackProducts,$iProductCategory);
                $aGroup['car_title'] = TpForm::getTpLabelText($aEstimateInfo['car_title'],$iOrderId,$iProductId,$sEstimateID);
            }
        }

        if (count($aHackProducts) > 0) {
            $aGroup['type']     = 1;
            $aGroup['products'] = array_values($aHackProducts);
        }

    }

    /**
     * @param array $aGroup aGroup
     * @return void
     */
    private function _reBuildTaxiDoublePriceMerge(&$aGroup) {
        if (!in_array(ProductCategory::PRODUCT_CATEGORY_UNIONE,$aGroup['products'])
            && !in_array(ProductCategory::PRODUCT_CATEGORY_TAXI_MARKETISATION_PUTONG,$aGroup['products'])
        ) {
            return;
        }

        $aRequest   = $this->_oRuntimeData->getRequest();
        $aOrderInfo = $this->_oRuntimeData->getOrderInfo();
        $apolloUser = [
            'key'           => UtilHelper::getUidByPassengerId($aOrderInfo['passenger_id'], \BizLib\Constants\Common::BIZ_TYPE_PASSENGER),
            'lang'          => $aRequest['lang'],
            'phone'         => $aOrderInfo['passenger_phone'],
            'city'          => $aOrderInfo['area'],
            'access_key_id' => $aRequest['access_key_id'],
            'app_version'   => $aRequest['appversion'] ?? $aRequest['app_version'],
            'county_id'     => $aOrderInfo['county'],
            'order_type'    => $aOrderInfo['type'],
            'pid'           => $aOrderInfo['passenger_id'],
            'guide_pos'     => GuideFactory::GUIDE_TYPE_ANYCAR,
            'source'        => self::ATHENA_GUIDE_SOURCE_CANCEL_DETAINMENT,

        ];

        if (!TaxiPricingBoxLayout::isPricingBox($apolloUser)) {
            return;
        }

        //支持多车型追加弹窗 出租车这里不再去掉188在线计价
        $oToggle = Apollo::getInstance()->featureToggle('taxi_double_price_offline', $apolloUser);
        if ($oToggle->allow()) {
            return;
        }

        $aProducts = $aGroup['products'];
        foreach ($aProducts as $key => $item) {
            if (ProductCategory::PRODUCT_CATEGORY_TAXI_MARKETISATION_PUTONG == $item) {
                unset($aProducts[$key]);
            }
        }

        $aGroup['products'] = array_values($aProducts);
        $aGroup['type']     = 1;

        //todo 后续跟版调整为 getProductList(); 兼容v1、2、3
        $aEstimateData = $this->aAnycarEstimate['estimate_data'];
        if (empty($aEstimateData) || empty($aEstimateData['188'])) {
            return;
        }

        $sFeeDescUrl            = $this->aAnycarEstimate['fee_detail_url'];
        $aGroup['fee_desc_url'] = sprintf('%s?token=%s&estimate_id=%s',$sFeeDescUrl,$aRequest['token'],$aEstimateData['188']['estimate_id']);
    }

    /**
     * 输入品类 获得计价盒子的盒子标题拼计价标题 如 "出租车-在线计价"
     * @param string $sProductCategory sProductCategory
     * @param string $sGroupID         sGroupID
     * @return string
     */
    public function getAnycarEstimateBoxCarTitleByCategory($sProductCategory, $sGroupID = '') {
        if ($this->_oRuntimeData->isIterationSwitchV3()) {
            $aLayout = $this->aAnycarEstimate['layout'];
            foreach ($aLayout as $aLayoutItem) {
                $aGroup    = $aLayoutItem['groups'][0];
                $aProducts = $aGroup['products'];
                if (!empty($aProducts) && is_array($aProducts) && in_array($sProductCategory,$aProducts)) {
                    //某些盒子需要判定group id是否符合
                    if (!empty($sGroupID) && $sGroupID != $aGroup['group_id']) {
                        continue;
                    }

                    $sBoxTitle = $aGroup['car_title'];
                    break;
                }
            }

            $aEstimateData  = $this->aAnycarEstimate['estimate_data'];
            $sEstimateTitle = $aEstimateData[$sProductCategory]['car_title'];
            if (!empty($sBoxTitle) && !empty($sEstimateTitle)) {
                return $sBoxTitle.'-'.$sEstimateTitle;
            }
        }

        return '';
    }

    /**
     * 获取小巴预估etp
     * @return bool
     */
    public function getMiniBusEtp() {
        $iDefaultEtp = -1;
        if (empty($this->aAnycarEstimate) || empty($this->aAnycarEstimate['raw_estimate_data'])) {
            Log::info('getMiniBusEtp is DefaultEtp');
            return $iDefaultEtp;
        }

        $aRawEstimateData = $this->aAnycarEstimate['raw_estimate_data'];
        foreach ($aRawEstimateData as $aEstimateItem) {
            if (Horae::isMiniBusCarpool($aEstimateItem)) {
                return $aEstimateItem['etp'] ?? $iDefaultEtp;
            }
        }

        Log::info('RawEstimateData not mini bus，use DefaultEtp');
        return $iDefaultEtp;
    }
}
