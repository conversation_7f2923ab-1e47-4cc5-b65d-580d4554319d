<?php

namespace PreSale\Models\guideCardView\viewModel;

use PreSale\Models\guideCardItem\MatchResultRuntime;

/**
 * BaseViewModel
 */
abstract class BaseViewModel
{
    /**
     * @var MatchResultRuntime
     */
    protected $_oRuntimeData;

    protected $_aDependencyModel;

    protected static $_bIsSetup = array();

    /**
     * @param MatchResultRuntime $oRuntimeData 运行时数据总线
     */
    protected function __construct($oRuntimeData) {
        $this->_oRuntimeData = $oRuntimeData;
    }

    /**
     * @return void
     */
    public function buildOnce() {
        // 依赖的model先行build
        if (!empty($this->_aDependencyModel)) {
            foreach ($this->_aDependencyModel as $oModel) {
                $oModel->buildOnce();
            }
        }

        $sKey = $this->getModelKey();
        if (empty($sKey)) {
            $this->doBuild();
            return;
        }

        $iOrderId = $this->_oRuntimeData->getOrderInfo()['order_id'] ?? '';
        $sKey     = $iOrderId.$sKey;

        if (self::$_bIsSetup[$sKey]) {
            return;
        }

        $this->doBuild();

        self::$_bIsSetup[$sKey] = true;
    }

    /**
     * 注册model，由于部分model会依赖其他model的构建，因此通过注册的方式将依赖先build好
     * @param BaseViewModel $oViewModel viewModel
     * @return void
     */
    protected function _registerModel($oViewModel) {
        $this->_aDependencyModel[] = $oViewModel;
        return;
    }

    /** 以后再加model不需要再自己起名了，直接用类名标识
     * @return string
     */
    protected function getModelKey() {
        return get_class($this);
    }

    /**
     * @return mixed
     */
    abstract public function doBuild();
}
