<?php

namespace PreSale\Models\guideCardView\viewModel;

use BizLib\Client\SpsClient;
use BizLib\Log;
use PreSale\Models\guideCardItem\MatchResultRuntime;
use BizCommon\Constants\OrderNTuple;

/**
 * SpsModel
 */
class SpsModel extends BaseViewModel
{
    private static $_oInstance;
    private $_iFee;

    /**
     * @param MatchResultRuntime $oRuntimeData 运行时数据总线
     */
    protected function __construct($oRuntimeData) {
        parent::__construct($oRuntimeData);
        $this->_registerModel($oRuntimeData->oAnycarEstimateViewModel);
    }

    /**
     * @param MatchResultRuntime $oRuntimeData 运行时数据总线
     * @return SpsModel
     */
    public static function getInstance($oRuntimeData) {
        if (self::$_oInstance == null) {
            self::$_oInstance = new self($oRuntimeData);
        }

        return self::$_oInstance;
    }

    /**
     * @return mixed|void
     * @throws \Exception exception
     */
    public function doBuild() {
        $this->_iFee = $this->_getSpsFee();
    }

    /**
     * @return float|int
     */
    public function getFormattedFee() {
        return (-1) * $this->_iFee / 100;
    }

    /**
     * @return mixed
     */
    public function getSpsFee() {
        return $this->_iFee;
    }

    /** 请求sps获取特快费用项
     * @return float
     * @throws \Exception 异常
     */
    private function _getSpsFee() {
        $oSpsClient = new SpsClient();
        // 从dos里取快车价，从anycarEstimate取特快价， 从ofs里取会员加价
        $iLevelId = current($this->_oRuntimeData->oMultiMember->getMultiMemberInfo())['member_info']['member_level_id'];
        // 取特快一口价预估数据
        $aAplusItem = null;
        $this->_oRuntimeData->oAnycarEstimateViewModel->buildOnce();
        foreach ($this->_oRuntimeData->oAnycarEstimateViewModel->getProductList() as $aItem) {
            if (OrderNTuple::DIDI_PUTONG_CAR_LEVEL == $aItem['require_level'] && OrderNTuple::LEVEL_TYPE_APLUS == $aItem['level_type']) {
                $aAplusItem = $aItem;
                break;
            }
        }

        if (empty($aAplusItem)) {
            return null;
        }

        // sps由于工期紧，复用原有idl，所以这些字段名都奇奇怪怪
        // wiki: http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=818018768
        $aOrderInfo    = $this->_oRuntimeData->getOrderInfo();
        $aReqOrderInfo = [
            'passenger_id'  => $aOrderInfo['passenger_id'],
            'require_level' => $aAplusItem['require_level'],
            'district'      => $aOrderInfo['district'],
            'estimate_id'   => $aAplusItem['estimate_id'],
            'order_n_tuple' => [
                'level_type' => $aAplusItem['level_type'],
            ],
        ];

        $aParams[] = [
            'order_info' => $aReqOrderInfo,
            'biz_data'   => json_encode(['order_match_struct' => ['level_id' => $iLevelId]]), // 将会员id传过去
        ];
        $aSpsRet   = $oSpsClient->orderMatch(['req' => $aParams]);
        $iAmount   = $this->_getUpgradeAplusDiscount($aSpsRet);
        return $iAmount;
    }

    /** 获取追加特快的费用项
     * @param array $aResult sps ordermatch return
     * @return int
     */
    private function _getUpgradeAplusDiscount($aResult) {
        if (!empty($aResult['data'])) {
            foreach ($aResult['data'] as $aData) {
                if (!empty($aData['resp_fee_info'])) {
                    foreach ($aData['resp_fee_info'] as $aItem) {
                        if ('sps_upgrade_aplus_discount' == $aItem['fee_name']) {
                            return $aItem['amount'];
                        }
                    }
                }
            }
        }

        return 0;
    }
}
