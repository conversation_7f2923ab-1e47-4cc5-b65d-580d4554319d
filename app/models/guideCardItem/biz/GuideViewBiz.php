<?php

namespace PreSale\Models\guideCardItem\biz;

use BizLib\Log;
use BizLib\Utils\Language;
use PreSale\Models\guideCardItem\OmniOmegaInfo;
use PreSale\Models\guideCardView\guideCard\ElementMemberCard;

/**
 * 取消挽留出口biz类
 */
class GuideViewBiz
{
    /**
     * @param int $iRemainTimes remainTimes
     * @return string
     */
    public static function getRemainTimesText($iRemainTimes) {
        $aText = Language::getDecodedTextFromDcmp('pre_cancel-remain_times_text');
        // 无限次
        if (-1 == $iRemainTimes) {
            return $aText['unlimited'];
        } else {
            // 剩x次
            return Language::replaceTag($aText['limited'], ['remain_times' => $iRemainTimes]);
        }
    }

    /**
     * @param int $iWaitTimeSaved waitTimeSaved
     * @return string
     */
    public static function getEstimateAdvanceTime($iWaitTimeSaved) {
        $sText = Language::getTextFromDcmp('pre_cancel-estimate_advance_time', ['wait_time_saved' => $iWaitTimeSaved]);
        return $sText;
    }

    /**
     * @param ElementMemberCard $oCard       oCard
     * @param int               $iMemberType iMemberType
     */
    public static function getMemberTitleMarker(ElementMemberCard $oCard,$iMemberType) {
        $aIcon = Language::getDecodedTextFromDcmp('order_match-member_v3_icons');
        if (in_array($iMemberType, [OmniOmegaInfo::MEMBER_ZHUANHAO_MEDAL, OmniOmegaInfo::MEMBER_LICHENG])) {
            $aMemberTypes  = $oCard->getOMemberModel()->getAMemberType();
            $aMemberLevels = $oCard->getOMemberModel()->getAMemberLevel();
            $aTitleMarker  = $oCard->getATitleMarker();
            foreach ($aMemberTypes as $key => $iMemberType) {
                if (OmniOmegaInfo::MEMBER_ZHUANHAO_MEDAL == $iMemberType) {
                    // 勋章会员有多个level
                    $aTitleMarker[$key]['icon'] = $aIcon[$iMemberType][$aMemberLevels[$key]] ?? "";
                } elseif (OmniOmegaInfo::MEMBER_LICHENG == $iMemberType) {
                    // 里程有多个level
                    $aTitleMarker[$key]['icon'] = $aIcon[$iMemberType][$aMemberLevels[$key]] ?? "";
                } else {
                    // 其他会员直接取相应文案
                    $aTitleMarker[$key]['icon'] = $aIcon[$iMemberType] ?? "";
                }
            }

            $oCard->setATitleMarker($aTitleMarker);
        }
    }
}
