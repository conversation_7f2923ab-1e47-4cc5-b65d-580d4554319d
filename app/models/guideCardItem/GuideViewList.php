<?php
namespace PreSale\Models\guideCardItem;

use BizLib\Log;
use BizLib\Utils\ProductCategory;
use PreSale\Infrastructure\Repository\Redis\GuideCardRedisRepository;
use PreSale\Logics\sideEstimate\paramSet\source\Base;
use PreSale\Models\guideCardItem\button\GuideCardStyleManage;
use PreSale\Models\guideCardItem\container\BaseGuideContainer;
use PreSale\Models\guideCardItem\container\GradientColorContainer;
use PreSale\Models\guideCardItem\container\GuideAnycarContainer;
use PreSale\Models\guideCardItem\container\GuideFatherContainer;
use PreSale\Models\guideCardView\guideCard\BaseGuideCard;
use PreSale\Models\guideCardView\guideCard\GuideAnycarCard;
use PreSale\Models\guideCardView\guideView\BaseViewRender;

/**
 * 获取取消挽留athena推荐出口
 */
class GuideViewList
{
    const MEMBER_GUIDE_POS = [
        GuideFactory::GUIDE_TYPE_FASTWAY,
        GuideFactory::GUIDE_TYPE_FASTWAY_COUPON,
        GuideFactory::GUIDE_TYPE_PREMIUM_SPEED_ANSWER,
        GuideFactory::GUIDE_TYPE_FASTWAY_FOR_ANYCAR,
        GuideFactory::GUIDE_TYPE_MEMBER_ADD_A_PLUS,
    ];

    const CONTAINER_STYLE_KEY = 'pre_cancel-guide_card_style';

    const GUIDE_CARD_VIEW_CLASS = [
        '\PreSale\Models\guideCardView\guideView\GuideAnycarView',
        '\PreSale\Models\guideCardView\guideView\GuideMultiAnycarView',
        '\PreSale\Models\guideCardView\guideView\GuideFartherView',
        '\PreSale\Models\guideCardView\guideView\GuideFastWayExpressView',
        '\PreSale\Models\guideCardView\guideView\GuideFastWayPremiumView',
        '\PreSale\Models\guideCardView\guideView\GuideMemberAddAPlusView',
        '\PreSale\Models\guideCardView\guideView\GuideMemberAggregateView',
    ];

    const GUIDE_CARD_VIEW_CLASS_V2 = [
        '\PreSale\Models\guideCardView\guideView\GuideAnycarViewV2',
        '\PreSale\Models\guideCardView\guideView\GuideFartherViewV2',
    ];

    const SUPPLY_SCENE_TYPE_APPEND_ANYCAR = 2; // 追加场景

    /**
     * 获取导流出口卡片
     * @param array              $aOrderMatchRecommendResult 导流推荐数据
     * @param MatchResultRuntime $oRuntimeData               运行时数据总线
     * @param array              $aGuideRecommendInfo        导流推荐数据
     * @return BaseGuideCard
     */
    public function getGuideCard($aOrderMatchRecommendResult, $oRuntimeData, $aGuideRecommendInfo) {
        $oGuideCard = null;
        foreach (self::GUIDE_CARD_VIEW_CLASS as $sGuideCardViewClass) {
            $oGuideView = new $sGuideCardViewClass($oRuntimeData, $aOrderMatchRecommendResult, false, $aGuideRecommendInfo);
            if (BaseViewRender::HT_HIT == $oGuideView->getRecommendHitType()) {
                $oGuideCard = $oGuideView->doRender();
                break;
            }
        }

        // 如果是追加车型 并且拼车，缓存卡片
        if ($oGuideCard != null && in_array($oGuideCard->getCardType(), [BaseGuideCard::GUIDE_TYPE_PRE_CANCEL_ANYCAR, BaseGuideCard::GUIDE_TYPE_PRE_CANCEL_ANYCAR_V3]) && $this->_isAppendCarpool($oRuntimeData, $oGuideCard)) {
            // 拼车缓存guideCard
            GuideCardRedisRepository::save($oRuntimeData->getOrderInfo()['passenger_id'], BaseViewRender::CARD_ID_APPEND_CARPOOL, $oGuideCard);
        }

        // 多车型追加车型带有拼车，缓存卡片
        if ($oGuideCard != null && BaseGuideCard::GUIDE_TYPE_PRE_CANCEL_MULTI_ANYCAR == $oGuideCard->getCardType() && $this->_isAppendCarpool($oRuntimeData, $oGuideCard)) {
            // 多车包含拼车缓存 guideCard
            GuideCardRedisRepository::save($oRuntimeData->getOrderInfo()['passenger_id'], BaseViewRender::CARD_ID_MULTI_APPEND_CARPOOL, $oGuideCard);
        }

        return $oGuideCard;
    }

    /**
     * 获取导流出口卡片
     * @param array              $aOrderMatchRecommendResult 导流推荐数据
     * @param MatchResultRuntime $oRuntimeData               运行时数据总线
     * @param int                $iSceneType                 场景类型
     * @param array              $aGuideRecommendInfo        导流推荐数据
     * @param array              $aAthenaExpectInfo          athena期望信息
     * @return BaseGuideCard
     */
    public function getGuideCardV2($aOrderMatchRecommendResult, $oRuntimeData, $iSceneType, $aGuideRecommendInfo, $aAthenaExpectInfo) {
        $oGuideCard = null;
        foreach (self::GUIDE_CARD_VIEW_CLASS_V2 as $sGuideCardViewClass) {
            if ($iSceneType != self::SUPPLY_SCENE_TYPE_APPEND_ANYCAR) {
                continue;
            }

            $oGuideView = new $sGuideCardViewClass($oRuntimeData, $aOrderMatchRecommendResult, $aGuideRecommendInfo, $aAthenaExpectInfo);
            if (BaseViewRender::HT_HIT == $oGuideView->getRecommendHitType()) {
                $oGuideCard = $oGuideView->doRender();
                break;
            }
        }

        return $oGuideCard;
    }


    /**
     * @param BaseGuideCard $oGuideCard 出口卡片
     * @return BaseGuideContainer
     */
    public function getContainerCard($oGuideCard) {
        if ($oGuideCard == null) {
            return null;
        }

        $iCardType = $oGuideCard->getCardType();
        switch ($iCardType) {
            case BaseGuideCard::GUIDE_TYPE_PRE_CANCEL_ANYCAR: // no break
            case BaseGuideCard::GUIDE_TYPE_PRE_CANCEL_ANYCAR_V3: // no break
            case BaseGuideCard::GUIDE_TYPE_PRE_CANCEL_MULTI_ANYCAR: // no break
                // 挽留弹窗中追加车型
                $oGuideCardContainer = new GuideAnycarContainer();
                $oGuideCardContainer->setCardList([$oGuideCard]);
                $oGuideCardContainer->setAGradientColor(GuideCardStyleManage::getMultiRecommendContainerBgGradientColor());
                break;
            case BaseGuideCard::GUIDE_TYPE_PRE_CANCEL_FATHER:
                // 挽留弹窗中加价调度
                $oGuideCardContainer = new GuideFatherContainer();
                $oGuideCardContainer->setCardList([$oGuideCard]);
                $oGuideCardContainer->setGradientColor(GuideCardStyleManage::getFartherContainerBgGradientColor());
                break;
            case BaseGuideCard::GUIDE_TYPE_ANYCAR_NEW_STYLE:
                // 挽留弹窗中追加车型
                $oGuideCardContainer = new GuideAnycarContainer();
                $oGuideCardContainer->setCardList([$oGuideCard]);
                $oGuideCardContainer->setAGradientColor(GuideCardStyleManage::getMultiRecommendContainerBgGradientColorV2());
                break;
            default:
                 // 其它样式
                $oGuideCardContainer = new GradientColorContainer();
                $oGuideCardContainer->setCardList([$oGuideCard]);
                $oGuideCardContainer->setGradientColor(GuideCardStyleManage::getContainerBgGradientColor());
        }

        return $oGuideCardContainer;
    }

    /**
     * @param MatchResultRuntime $oRuntimeData 运行时数据
     * @param GuideAnycarCard    $oGuideCard   追加车型卡片
     * @return bool
     */
    private function _isAppendCarpool($oRuntimeData, $oGuideCard) {
        $oEstimateInfo = $oGuideCard->getEstimateInfo();
        if ($oRuntimeData->isIterationSwitchV3()) {
            $aProductList = array_values($oEstimateInfo->aEstimateData);
        } else {
            $aProductList = $oEstimateInfo->aProductList;
        }

        foreach ($aProductList as $aItem) {
            if (ProductCategory::PRODUCT_CATEGORY_CARPOOL_STATION == $aItem['product_category']) {
                return true;
            }
        }

        return false;
    }
}
