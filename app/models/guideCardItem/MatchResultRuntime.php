<?php

namespace PreSale\Models\guideCardItem;

use BizCommon\Utils\Horae;
use BizLib\Constants\Common;
use BizLib\Constants\OrderSystem;
use BizLib\Utils\UtilHelper;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Infrastructure\Repository\Ufs\UfsRepository;
use PreSale\Models\guideCardView\viewModel\AnycarEstimateViewModel;
use PreSale\Models\guideCardView\viewModel\OrderBookingPreCancelEstimateViewModel;
use PreSale\Infrastructure\Ofs\OrderOfsRepository;
use BizCommon\Logics\Anycar\AnyCarCommonLogic;
use BizLib\Constants\Ofs as OfsConstants;
use BizLib\Log as NuwaLog;
use BizLib\ErrCode\Msg;
use BizLib\ErrCode\Code;
use PreSale\Models\guideCardView\viewModel\SpsModel;
use PreSale\Models\Member\AnycarMember;
use PreSale\Models\Member\LuxuryMemberLogic;
use PreSale\Models\Member\Member;
use Transaction\Dto\IDL\GetOrderMatchInfo\GetOrderMatchInfoParam;
use PreSale\Logics\compensation\CompensationLogic;
use PreSale\Logics\compensation\NormalNoCarCompensateLogic;


/**
 * MatchResultRuntime
 */
class MatchResultRuntime
{

    /**
     * 请求参数
     * @var array
     */
    private $_aRequest;

    /**
     * 订单数据
     * @var array
     */
    private $_aOrderInfo;

    /**
     * @var AnycarEstimateViewModel
     */
    public $oAnycarEstimateViewModel;

    /**
     * @var SpsModel
     */
    public $oSpsModel;

    public $oOrderBookingPreCancelEstimateViewModel;

    /**
     * @var TextService
     */
    public $oTextService;

    /**
     * @var Member
     */
    public $oMember;

    /**
     * 是否单车型，包括非anycar和anycar单车型
     * @var bool
     */
    public $bIsSingleCar = false;

    /**
     * @desc single mock的 orderInfo
     * @var array
     */
    private $_aLocalOrderInfo = array();

    /**
     * @var AnycarMember
     */
    public $oMultiMember;

    /**
     * 是否等待应答迭代三期
     * @var bool
     */
    private $_bIterationSwitchV3;

    /**
     * 是否走新等待应答框架升级需求
     * @var bool
     */
    public $iIsWaitAnswerUpgrade = false;

    /**
     * 是否走多车型推荐
     * @var boolean
     */
    public $bAllowMultiAppendCar = false;

    /**
     * 多车型推荐样式预估埋点数据
     * @var array
     */
    public $aGuideMultiAnycarEstimateDataOfOmega = array();

    /**
     * 单车型推荐样式预估埋点数据
     * @var array
     */
    public $aGuideAnycarEstimateDataOfOmega = array();

    /**
     * 实验对照组
     */
    const EXP_CONTROL_GROUP = 0;
    /**
     * 取消挽留页车型列表展示实验组
     * @var int
     */
    public $iExperimentalGroup = 0;

    /**
     * 取消挽留页新弹层
     * @var bool
     */
    public $bNewPopExam = false;


    /**
     * 报价单
     * @var array
     */
    public $aQuotation = [];


    /**
     *
     * @var CompensationLogic
     */
    private $_oCompensationLogic = null;

    /**
     *
     * @var NormalNoCarCompensateLogic
     */
    private $_oNormalNoCarCompensateLogic = null;

    /**
     * @var bool 是否命中新等应答切量
     */
    private $_bHitOrderMatchTraffic = false;


    /**
     * @param array $aRequest   请求数据
     * @param array $aOrderInfo 订单数据
     */
    public function __construct($aRequest, $aOrderInfo) {
        $this->_aRequest   = $aRequest;
        $this->_aOrderInfo = $aOrderInfo;
        $this->oAnycarEstimateViewModel = AnycarEstimateViewModel::getInstance($this);
        $this->oSpsModel = SpsModel::getInstance($this);
        $this->oOrderBookingPreCancelEstimateViewModel = OrderBookingPreCancelEstimateViewModel::getInstance($this);
        $this->_buildBasic();
    }

    /**
     * @return void
     */
    private function _buildBasic() {
        $this->_buildTextService();
        $this->_getIsSingleCar();
        $this->_buildMember($this->_aRequest);
        $this->_buildHitOrderMatchUpgrade($this->_aOrderInfo);
    }


    /**
     * @param array $aOrderInfo orderInfo
     * @return void
     */
    private function _buildHitOrderMatchUpgrade($aOrderInfo) {
        $aFeature = ["order_match.traffic_switch"];
        $aCondition = ['order_id' => $aOrderInfo['order_id']];
        $aRet = UfsRepository::getUfsFeature($aFeature, $aCondition);
        if (empty($aRet)) {
            return;
        }

        $this->_bHitOrderMatchTraffic = $aRet["order_match.traffic_switch"] == '1';
    }

    /**
     * 是否命中等应答切量
     * @return bool
     */
    public function hitOrderMatchTraffic() {
        return $this->_bHitOrderMatchTraffic;
    }

    /**
     * @decs 构建实验
     * @param array $aRequest       请求数据
     * @param array $aOrderInfo     订单数据
     * @param bool  $isShowNewStyle 新样式
     * @param bool  $bIsShowPreMatchCard  预匹配
     * @return void
     */
    public function buildNewPopExam($aRequest,$aOrderInfo,$isShowNewStyle,$bIsShowPreMatchCard) {

        $aParams = [
            'key'           => $aOrderInfo['passenger_id'],
            'phone'         => $aOrderInfo['passenger_phone'],
            'city'          => $aOrderInfo['area'],
            'pid'           => $aOrderInfo['passenger_id'],
            'access_key_id' => $aRequest['access_key_id'],
            'app_version'   => $aRequest['app_version'] ?? $aRequest['appversion'],
            'lang'          => $aRequest['lang'],
        ];
        if ($isShowNewStyle) {
            $aParams['is_show_new_style'] = 1;
        }

        if ($aRequest['v6_version']) {
            $aParams['v6_version'] = 1;
        }

        if ($this->iIsWaitAnswerUpgrade) {
            $aParams['is_wait_answer_upgrade'] = 1;
        }


        if ($this->isAllowNewPopExam($aOrderInfo,$bIsShowPreMatchCard)) {
            $aParams['is_allow'] = 1;
        }

        $aApolloToggle = Apollo::getInstance()->featureToggle('Waiting_cancel_1',$aParams);

        if ($aApolloToggle->allow() && $aApolloToggle->getGroupName()=='treatment_group') {
            $this->bNewPopExam = true;
        }


    }


    /**
     * @decs 非实验场景判断
     * @param array $aOrderInfo     订单数据
     * @param bool  $bIsShowPreMatchCard  预匹配
     * @return bool
     */
    public function isAllowNewPopExam($aOrderInfo,$bIsShowPreMatchCard) {
        // 预约单
        if (OrderSystem::TYPE_ORDER_BOOKING == $aOrderInfo['type']) {
            return false;
        }

        // 预匹配
        if ($bIsShowPreMatchCard) {
            return false;
        }

        // 单勾小巴
        if (\BizCommon\Utils\Horae::isOnlyMiniBusCarpoolOrder($aOrderInfo)) {
            return false;
        }


        // 单勾特惠
        $aExtendFeature = json_decode($aOrderInfo['extend_feature'],true);
        if (is_array($aExtendFeature['multi_require_product'])
            && 1 == sizeof($aExtendFeature['multi_require_product'])
            && \BizCommon\Utils\Horae::isAnycarFastRangeOrder($aOrderInfo)
        ) {
            return false;
        }

        return true;

    }

    /**
     * @return void
     */
    private function _buildTextService() {
        $this->oTextService = new TextService();
        $this->oTextService->setNamespaceKey('pre_cancel-');
    }

    /**
     * @param array $aRequest 请求数据
     * @return void
     */
    private function _buildMember($aRequest) {
        $aOrderFeature    = $this->_getOrderFeatures();
        $aMultiMemberInfo = json_decode($aOrderFeature['multi_product_members'], true);
        if ((isset($aOrderFeature['member_level_id']) && !empty($aOrderFeature['member_level_id'])) || LuxuryMemberLogic::isLuxuryMember($aOrderFeature)) {
            $this->oMember = new Member();
            $this->oMember->initMemberPrivilegeByOrderFeature($aOrderFeature, false);
        } elseif ($this->bIsSingleCar) {
            // 单车型走这块
            $this->oMember = new Member();
            $this->oMember->initMemberPrivilegeByOrderFeature($aMultiMemberInfo, true);
            // 单车型也把multiMember new了，对于单/多车型都有的会员权益，就可以用一套了
            $this->oMultiMember = AnycarMember::getInstance();
            $aMultiMemberInfo   = AnycarMember::translateFromOfsData($aMultiMemberInfo);
            $this->oMultiMember->setMultiMemberInfo($aMultiMemberInfo);
        } else {
            $this->oMultiMember = AnycarMember::getInstance();
            $aMultiMemberInfo   = AnycarMember::translateFromOfsData($aMultiMemberInfo);
            $this->oMultiMember->setMultiMemberInfo($aMultiMemberInfo);
        }
    }

    /**
     * 获取订单特征
     * @return array|mixed
     */
    private function _getOrderFeatures() {
        $oOrderOfsRepository = new OrderOfsRepository();
        $aOrderFeature       = [];
        $aFields = [
            'order_match_select_guide_type',    // 排队出口类型
            'is_canceled_priority_line_up',     // 是否取消优先派订单
            'special_scene_param',              // 是否极端天气

        ];
        if (!AnyCarCommonLogic::isSingleCarMock($this->_aOrderInfo)) {
            $aFields = array_merge(
                $aFields,
                ['multi_product_members']
            );
        } else {
            $aFields = array_merge(
                $aFields,
                [
                    'member_level_id',
                    'member_level_name',
                    'member_level_icon',
                    'member_level_core_icon',
                    'member_privilege_new_order',
                    'member_ocean_card',
                ]
            );
        }

        $aFeatureKeys = [OfsConstants::SCENE_BASIC => $aFields,];

        $iOid    = $this->_aOrderInfo['order_id'];
        $aOfsRes = $oOrderOfsRepository->getOrderFeature($iOid, $aFeatureKeys);
        if (0 != $aOfsRes['errno']) {
            NuwaLog::warning(
                Msg::formatArray(
                    Code::E_COMMON_OFS_QUERY_FAIL,
                    [
                        'message' => $aOfsRes['errmsg'],
                    ]
                )
            );
        } else {
            $aOrderFeature = $aOfsRes['result'][OfsConstants::SCENE_BASIC] ?? [];
        }

        return $aOrderFeature;
    }

    /**
     * @return void
     */
    private function _getIsSingleCar() {
        $aOrderInfo           = $this->_aOrderInfo;
        $aExtendFeature       = json_decode($aOrderInfo['extend_feature'], true) ?? [];
        $aMultiRequireProduct = $aExtendFeature['multi_require_product'] ?? [];
        if (empty($aMultiRequireProduct) || ! $aOrderInfo['is_anycar']) {
            $this->bIsSingleCar     = true;
            $this->_aLocalOrderInfo = $aOrderInfo;
        } else {
            if (AnyCarCommonLogic::isSingleCarMock($aOrderInfo)) {
                $this->_aLocalOrderInfo = AnyCarCommonLogic::mockSingleCar($aOrderInfo);
                $this->bIsSingleCar     = true;
            } elseif (1 == count($aMultiRequireProduct)) {
                $this->_aLocalOrderInfo = AnyCarCommonLogic::mockSingleCar($aOrderInfo);
                $this->_aLocalOrderInfo['is_anycar'] = 1;
                $this->bIsSingleCar = true;
            } else {
                $this->_aLocalOrderInfo = $aOrderInfo;
                $this->bIsSingleCar     = false;
            }
        }
    }

    /**
     * @return mixed
     */
    public function getLuxuryLevelId() {
        if (!empty($this->oMultiMember)) {
            $iLevelId = current($this->oMultiMember->getMultiMemberInfo())['member_info']['luxury_member_level_id'];
        } else {
            $iLevelId = $this->oMember->getMemberInfo()['luxury_member_level_id'];
        }

        return $iLevelId;
    }

    /**
     * @return bool
     */
    public function isMemberV3() {
        if (!empty($this->oMultiMember)) {
            $iMemberVersion = current($this->oMultiMember->getMultiMemberInfo())['member_info']['member_version'];
        } else {
            $iMemberVersion = $this->oMember->getMemberInfo()['member_version'];
        }

        return 3 == $iMemberVersion;
    }

    /** 获取会员名
     * @return mixed
     */
    public function getMemberLevelId() {
        if (!empty($this->oMultiMember)) {
            $iLevelId = current($this->oMultiMember->getMultiMemberInfo())['member_info']['member_level_id'];
        } else {
            $iLevelId = $this->oMember->getMemberInfo()['member_level_id'];
        }

        return $iLevelId;
    }

    /**
     * @return array
     */
    public function getRequest() {
        return $this->_aRequest;
    }

    /**
     * @param array $aRequest 请求参数
     * @return void
     */
    public function setRequest($aRequest) {
        $this->_aRequest = $aRequest;
    }

    /**
     * @return array
     */
    public function getOrderInfo() {
        return $this->_aLocalOrderInfo;
    }

    /**
     * @param array $aOrderInfo 订单数据
     * @return void
     */
    public function setAOrderInfo($aOrderInfo) {
        $this->_aOrderInfo = $aOrderInfo;
    }

    /**
     * 是否命中等待应答迭代三期 apollo
     * @return bool
     */
    public function isIterationSwitchV3() {
        if (isset($this->_bIterationSwitchV3)) {
            return $this->_bIterationSwitchV3;
        }

        $oApollo    = new Apollo();
        $aOrderInfo = $this->getOrderInfo();
        $oToggle    = $oApollo->featureToggle(
            'order_match_v3_iteration',
            [
                'key'           => UtilHelper::getUidByPassengerId($aOrderInfo['passenger_id'], Common::BIZ_TYPE_PASSENGER),
                'city'          => $aOrderInfo['area'],
                'county'        => $aOrderInfo['county'],
                'pid'           => $aOrderInfo['passenger_id'],
                'phone'         => $aOrderInfo['passenger_phone'],
                'lang'          => $this->getRequest()['lang'],
                'access_key_id' => $this->getRequest()['access_key_id'],
                'version'       => $this->getRequest()['appversion'] ?? $this->getRequest()['app_version'],
            ]
        );
        if ($oToggle->allow()) {
            $sGroupName = $oToggle->getGroupName();
            if (in_array($sGroupName, ['exp_group_1', 'exp_group_2'])) {
                $this->_bIterationSwitchV3 = true;
            } else {
                $this->_bIterationSwitchV3 = false;
            }
        } else {
            $this->_bIterationSwitchV3 = false;
        }

        return $this->_bIterationSwitchV3;
    }


    /**
     * @return array
     */
    public function getGuideMultiAnycarEstimateDataOfOmega() {
        return $this->aGuideMultiAnycarEstimateDataOfOmega;
    }

    /**
     * @param array $aEstimateDataOfOmega 埋点数据中需要的预估数据
     * @return void
     */
    public function setGuideMultiAnycarEstimateDataOfOmega($aEstimateDataOfOmega) {
        $this->aGuideMultiAnycarEstimateDataOfOmega = $aEstimateDataOfOmega;
    }

    /**
     * @return array
     */
    public function getGuideAnycarEstimateDataOfOmega() {
        return $this->aGuideAnycarEstimateDataOfOmega;
    }

    /**
     * @param array $aEstimateDataOfOmega 埋点数据中需要的预估数据
     * @return void
     */
    public function setGuideAnycarEstimateDataOfOmega($aEstimateDataOfOmega) {
        $this->aGuideAnycarEstimateDataOfOmega = $aEstimateDataOfOmega;
    }


    /**
     * @param CompensationLogic $oCompensationLogic
     * @return void
     */
    public function setCompensationLogic($oCompensationLogic) {
        $this->_oCompensationLogic = $oCompensationLogic;
    }

    /**
     * @param NormalNoCarCompensateLogic $oNormalNoCarCompensateLogic
     * @return void
     */
    public function setNormalNoCarCompensationLogic($oNormalNoCarCompensateLogic) {
        $this->_oNormalNoCarCompensateLogic = $oNormalNoCarCompensateLogic;
    }

    /**
     *
     * @return CompensationLogic
     */
    public function getCompensationLogic() {
        return $this->_oCompensationLogic;
    }

    /**
     *
     * @return NormalNoCarCompensateLogic
     */
    public function getNormalNoCarCompensationLogic() {
       return  $this->_oNormalNoCarCompensateLogic;
    }
}
