<?php
namespace PreSale\Models\tagService;

use PreSale\Infrastructure\Repository\Tag;
use Xiaoju\Apollo\Apollo;

/**
 * Class PersonTag 请求标签系统获得人群标签
 * @package PreSale\Logics\tagService
 */
class PersonTag
{
    const APOLLO_CONF_PERSON_TAG = 'athena_passenger_tags';

    private $_usertags;

    /**
     * @var PersonTag
     */
    private static $_oInstance;

    /**
     * @param array $aParams 为apollo传参
     * PersonTag constructor.
     */
    private function __construct(array $aParams) {
        $oApollo = APOLLO::getInstance()->featureToggle(
            self::APOLLO_CONF_PERSON_TAG,
            [
                'key'           => $aParams['pid'],
                'pid'           => $aParams['pid'],
                'lang'          => $aParams['lang'],
                'access_key_id' => $aParams['access_key_id'],
                'app_version'   => $aParams['app_version'],
            ]
        );

        if ($oApollo->allow()) {
            $this->_usertags = $oApollo->getAllParameters();
        }
    }

    /**
     * @param array $aParams 为apollo传参
     * @return PersonTag
     */
    public static function getInstance(array $aParams): PersonTag {
        if (self::$_oInstance === null) {
            self::$_oInstance = new self($aParams);
        }

        return self::$_oInstance;
    }

    /**
     * 请求标签系统获得user_tag
     * @param $pid ...
     * @return string|null
     */
    public function getPersonTag($pid): string {
        if (empty($pid)) {
            return 'normal_user';
        }

        $usertags = json_decode($this->_usertags['user_tags'],true);
        $tags     = array_map(
            function ($tag) {
                return $tag['tag_id'];
            },
            $usertags
        );
        $dtags    = implode(',',$tags);

        $aResult = Tag\TagServiceRepository::getPassengerTagResult(
            (string)$pid,
            $dtags,
            Tag\TagServiceRepository::DEFAULT_CALLER
        );

        if ($aResult == null) {
            return 'normal_user';
        }

        foreach ($usertags as $tag) {
            if (in_array($tag['tag_id'], $aResult)) {
                return $tag['tag_name'];
            }
        }

        return 'normal_user';
    }
}
