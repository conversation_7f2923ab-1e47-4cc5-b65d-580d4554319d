<?php

namespace PreSale\Models\cache;

use BizLib\Libraries\RedisDB;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Common as UtilsCommon;
use Disf\SPL\Trace;

/**
 * 预付单缓存.
 *
 * <AUTHOR> <<EMAIL>>
 */
class PrepayOrder
{
    /** @var $redisdb RedisDB */
    public $redisdb;
    const EXPIRE_TIME = 18000; // 30 mins

    private $_oCI;

    public function __construct() {
        $this->redisdb = RedisDB::getInstance();
    }

    /**
     * 写预付单缓存.
     */
    /*
    public function set($aValue) {
        if (!is_array($aValue)) {
            return false;
        }

        $sValue = json_encode($aValue);
        $sKey   = UtilsCommon::getRedisPrefix(P_PREPAY_ORDER_CACHE).Trace::traceId();
        $bRet   = $this->redisdb->setex($sKey, self::EXPIRE_TIME, $sValue);
        if (!$bRet) {
            $aError = UtilsCommon::getErrMsg(CREATEORDER_PREPAY_CACHE_FAIL);
            NuwaLog::warning(sprintf('errno:%s|errmsg:%s|description:%s|value:%s|key:%s', $aError['errno'], $aError['errmsg'], 'failed to set cache', $sValue, $sKey));
        }

        return $bRet;
    }
    */

    /*
     * 读预付单缓存.
     */
    /*
    public function get($sTraceId) {
        $sKey = UtilsCommon::getRedisPrefix(P_PREPAY_ORDER_CACHE).$sTraceId;
        $aRet = $this->redisdb->get($sKey);

        if (!$aRet) {
            $aError = UtilsCommon::getErrMsg(CREATEORDER_PREPAY_CACHE_FAIL);
            NuwaLog::warning(sprintf('errno:%s|errmsg:%s|description:%s|key:%s', $aError['errno'], $aError['errmsg'], 'failed to get cache', $sKey));
        }

        return $aRet;
    }
    */
}
