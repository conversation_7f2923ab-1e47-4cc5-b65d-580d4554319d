<?php

namespace PreSale\Models\scene;

use PreSale\Logics\estimatePrice\multiRequest\Product;
use PreSale\Models\picasso\PicassoSelectTag;

/**
 * 视觉障碍识别
 * Class SceneBlind
 * @package PreSale\Models\scene
 */
class SceneBlind implements ISceneBase
{

    const BLIND_TAG = ['1194059'];

    const APOLLO_NAME = 'gs_blind_with_guide_dog_switch';

    private $_oSceneCommon;

    /**
     * SceneBlind constructor.
     * @param SceneCommon $oSceneCommon $oSceneCommon
     */
    public function __construct(SceneCommon $oSceneCommon) {
        $this->_oSceneCommon = $oSceneCommon;
    }

    /**
     * @param array $aOrder                $aOrderNTuple
     * @param array $aAnycarRequireProduct $aAnycarRequireProduct
     * @return mixed|void
     */
    public function recognition(&$aOrder, &$aAnycarRequireProduct) {
        //代叫过滤
        if (1 == $this->_oSceneCommon->aOrderInfo['call_car_type']) {
            return;
        }

        //人群识别
        // $aPassengerTagIds = (new PicassoSelectTag())->getPicassoPassengerTagIds($this->_oSceneCommon->aPassengerInfo['pid']);
        // if (empty($aPassengerTagIds) || empty(array_intersect($aPassengerTagIds,self::BLIND_TAG))) {
        //    return;
        // }
        $aParam = $this->_oSceneCommon->getApolloParam($aAnycarRequireProduct);

        if (!\Nuwa\ApolloSDK\Apollo::getInstance()->featureToggle(self::APOLLO_NAME,$aParam)->allow()) {
            return;
        }

        //anycar和普通的订单，场景字段在不同的地方。
        if (\BizLib\Utils\Product::isAnyCar($this->_oSceneCommon->aOrderInfo['product_id'])) {
            $aAnycarRequireProduct['blind_type'] = 1;
        } else {
            $aOrder['n_tuple']['blind_type'] = 1;
        }

    }
}
