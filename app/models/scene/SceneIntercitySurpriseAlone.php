<?php

namespace PreSale\Models\scene;


/**
 * 城际自营惊喜独享标识
 * Class SceneDisability
 * @package PreSale\Models\scene
 */
class SceneIntercitySurpriseAlone
{
    const INTERCITY_SURPRISE_ALONE = 46;

    /**
     * 城际惊喜独享标识
     * @param string $sSceneList sScene 场景字段
     * @return bool
     */
    public static function isIntercitySurpriseAlone($sSceneList) {

        if (!isset($sSceneList) ||empty($sSceneList)) {
            return false;
        }

        $aSceneList = json_decode($sSceneList,true);
        foreach ($aSceneList as $aItem) {
            if (self::INTERCITY_SURPRISE_ALONE == $aItem['id']) {
                return true;
            }
        }

        return false;
    }
}
