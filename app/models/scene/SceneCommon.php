<?php

namespace PreSale\Models\scene;

use BizLib\Utils\Product;

/**
 * 场景类公共方式
 * Class SceneCommon
 * @package PreSale\Models\scene
 */
class SceneCommon
{
    public $aMultiRequireProduct;
    public $aOrderInfo;
    public $aPassengerInfo;
    public $aCommonInfo;

    /**
     * SceneCommon constructor.
     * @param array $aPassengerInfo       $aPassengerInfo
     * @param array $aCommonInfo          $aCommonInfo
     * @param array $aOrderInfo           $aOrderInfo
     * @param array $aMultiRequireProduct $aMultiRequireProduct
     */
    public function __construct($aPassengerInfo, $aCommonInfo, $aOrderInfo, $aMultiRequireProduct) {
        $this->aMultiRequireProduct = $aMultiRequireProduct;
        $this->aOrderInfo           = $aOrderInfo;
        $this->aPassengerInfo       = $aPassengerInfo;
        $this->aCommonInfo          = $aCommonInfo;
    }

    /**
     * 获取apollo参数
     * @param array $aAnycarRequireProduct $aAnycarRequireProduct
     * @return array
     */
    public function getApolloParam($aAnycarRequireProduct) {
        $aParam = array_merge(
            $this->aPassengerInfo,
            $this->aCommonInfo,
            $this->aOrderInfo,
            [
                'key'  => $this->aPassengerInfo['phone'],
                'city' => $this->aOrderInfo['area'],
            ]
        );

        if (Product::isAnyCar($this->aOrderInfo['product_id'])) {
            return array_merge(
                $aParam,
                [
                    'product_id'    => $aAnycarRequireProduct['product_id'],
                    'business_id'   => $aAnycarRequireProduct['business_id'],
                    'require_level' => $aAnycarRequireProduct['require_level'],
                ]
            );
        }

        return $aParam;
    }
}
