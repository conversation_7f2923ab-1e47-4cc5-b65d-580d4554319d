<?php

namespace PreSale\Models\strategy;

use <PERSON><PERSON><PERSON><PERSON>\Log as NuwaLog;
/***************************************************************************
 * 需求决策服务model
 *
 * Copyright (c) 2018 xiaojukeji.com, Inc. All Rights Reserved
 * <AUTHOR>
 * @version 2018-05-31
 *
 **************************************************************************/
use BizCommon\Models\BaseModel;
use BizLib\Client\EstimateDecisionClient;
use Dirpc\SDK\EstimateDecision;

class DecisionService extends BaseModel
{
    /**
     * @var OrderSystemClient|null
     */
    private $_oClient = null;

    /**
     * DecisionService constructor.
     */
    public function __construct() {
        parent::__construct();
        $this->_oClient = new EstimateDecisionClient();
    }

    /**
     * @param $aDecisionReq
     *
     * @return array|EstimateDecision\DecisionResponse
     */
    public function getDecisionResult($aDecisionReq) {
        $aDecisionResult = array();
        if (empty($aDecisionReq)) {
            return $aDecisionResult;
        }

        $aDecisionRequest         = new EstimateDecision\DecisionRequest();
        $aDecisionRequest->user   = new EstimateDecision\UserInfo($aDecisionReq['user']);
        $aDecisionRequest->common = new EstimateDecision\CommonInfo($aDecisionReq['common']);
        $aDecisionRequest->scene  = new EstimateDecision\SceneInfo($aDecisionReq['scene']);
        $aDecisionRequest->athena_info  = $aDecisionReq['athena_info'];
        $aDecisionRequest->athena_extra = new EstimateDecision\AthenaExtra($aDecisionReq['athena_extra']);
        if (isset($aDecisionReq['source_from'])) {
            $aDecisionRequest->source_from = $aDecisionReq['source_from'];
        }

        if (isset($aDecisionReq['feature_data'])) {
            $aDecisionRequest->feature_data = $aDecisionReq['feature_data'];
        }

        foreach ($aDecisionReq['products'] as $index => $aProductItem) {
            $aDecisionRequest->products[$index] = new EstimateDecision\ProductInfo($aProductItem);
        }

        if (isset($aDecisionReq['decision_type'])) {
            $aDecisionRequest->decision_type = $aDecisionReq['decision_type'];
        }

        if (isset($aDecisionReq['preference_product'])) {
            $aDecisionRequest->preference_product = $aDecisionReq['preference_product'];
        }

        $aDecisionResult = $this->_oClient->decision($aDecisionRequest);
        if (isset($aDecisionResult['errno']) && 0 != $aDecisionResult['errno']) {
            NuwaLog::warning(sprintf('errno:%s|errmsg:%s|decision_result:%s', $aDecisionResult['errno'], $aDecisionResult['errmsg'], json_encode($aDecisionResult)));

            return array();
        }

        return $aDecisionResult['data'];
    }

    /**
     * @param $aIdentificationReq
     *
     * @return array|EstimateDecision\IdentificationResponse
     */
    public function getIdentificationResult($aIdentificationReq) {
        $aIdentificationResult = array();
        if (empty($aIdentificationReq)) {
            return $aIdentificationResult;
        }

        $aIdentificationRequest = new EstimateDecision\OneconfRequest($aIdentificationReq);
        $aIdentificationResult  = $this->_oClient->oneconf($aIdentificationRequest);
        if (isset($aIdentificationResult['errno']) && 0 != $aIdentificationResult['errno']) {
//            NuwaLog::warning(sprintf(
//                'errno:%s|errmsg:%s|identification_result:%s',
//                $aIdentificationResult['errno'],
//                $aIdentificationResult['errmsg'],
//                json_encode($aIdentificationResult)));
            return array();
        }

        return $aIdentificationResult['data']['oneconf_response'];
    }

    /**
     * @param $aRecordGetReq
     *
     * @return array
     */
    public function getRecordResult($aReq) {
        $aRecordGetResp = array();
        if (empty($aReq)) {
            return $aRecordGetResp;
        }

        $aRecordGetReq           = new EstimateDecision\RecordGetReq();
        $aRecordGetReq->trace_id = $aReq['trace_id'];
        $aRecordGetReq->fields   = $aReq['fields'];
        $aRecordGetResp          = $this->_oClient->getrecord($aRecordGetReq);
        if (isset($aRecordGetResp['errno']) && 0 != $aRecordGetResp['errno']) {
            NuwaLog::warning(sprintf('errno:%s|errmsg:%s|decision_result:%s', $aRecordGetResp['errno'], $aRecordGetResp['errmsg'], json_encode($aRecordGetResp)));

            return array();
        }

        return $aRecordGetResp['data'];
    }

    /**
     * @param array $aReq $aReq
     * @return array $aProductsRet $aProductsRet
     */
    public function getProductsResult($aReq) {
        $aProductsResp = array();
        if (empty($aReq)) {
            return $aProductsResp;
        }

        $aProductsReq = new EstimateDecision\ProductsReq($aReq);
        $aProductsRet = $this->_oClient->products($aProductsReq);
        if (isset($aProductsRet['errno']) && 0 != $aProductsRet['errno']) {
            NuwaLog::warning(sprintf('errno:%s|errmsg:%s|getProductsResult:%s', $aProductsRet['errno'], $aProductsRet['errmsg'], json_encode($aProductsRet)));

            return array();
        }

        return $aProductsRet['data'];
    }
}
