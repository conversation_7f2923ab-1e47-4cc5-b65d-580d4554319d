<?php

use Nuwa\Core\Controller;
use Nuwa\Core\Dispatcher;

/**
 * Controller 继承 Nuwa\Core\Controller.
 *
 * 类名以 Controller 后缀
 */
class WelcomeController extends Controller
{
    /**
     * Action 以 Action 后缀
     *
     * Access url:
     *     http://host:port
     *     http://host:port/Welcome
     *     http://host:port/Welcome/index
     *     http://host:port/index/Welcome/index
     */
    public function indexAction() {
        Dispatcher::getInstance()->enableView();
        $this->getView()->content = 'Hello DiDi.';
    }

    /**
     * 静态方法测试.
     *
     * @return string
     */
    public static function staticMethod() {
        return 'The controllers static method is OK!';
    }

    /**
     * CLI 测试用例.
     */
    public function messageAction() {
        echo 'Hello, Didi!';
    }
}
