<?php

use Nuwa\Core\Controller;
use Nuwa\Core\Application;

/**
 * 错误页面.
 */
class ErrorController extends Controller
{
    private $_config;

    public function init() {
        $this->_config = Application::app()->getConfig();
    }

    public function errorAction() {
        $exception = $this->getRequest()->getParam('exception');

        //  if errors are enabled show the full trace
        $showErrors           = $this->_config->application->showErrors;
        $this->_view->trace   = ($showErrors) ? $exception->getTraceAsString() : '';
        $this->_view->message = ($showErrors) ? $exception->getMessage() : '';

        //  Yaf has a few different types of errors
        switch (true) :
            case $exception instanceof Yaf_Exception_LoadFailed:
                return $this->_pageNotFound();
            default:
                return $this->_unknownError();
        endswitch;
    }

    private function _pageNotFound() {
        $this->_view->error = 'Page was not found';
    }

    private function _unknownError() {
        $this->_view->error = 'Application Error';
    }
}
