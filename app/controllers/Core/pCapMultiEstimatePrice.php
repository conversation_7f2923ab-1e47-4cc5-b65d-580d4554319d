<?php

use BizLib\Utils;
use PreSale\Logics\capEstimatePrice\multiRequest\ProductList;
use PreSale\Logics\capEstimatePrice\multiRequest\Product;
use Dirpc\SDK\PreSale\CapMultiEstimatePriceRequest as Request;
use Dirpc\SDK\PreSale\CapMultiEstimatePriceResponse as Response;
use PreSale\Logics\capEstimatePrice\multiRequest\PriceLogic;
use PreSale\Logics\capEstimatePrice\multiResponse\MainRender;
use BizLib\Config as NuwaConfig;
use BizLib\ExceptionHandler;
use BizLib\ErrCode\RespCode;
use BizLib\Exception\ExceptionRespInterface;
use PreSale\Logics\capEstimatePrice\multiResponse\CapMultiEstimatePublicLog as PublicLog;
use PreSale\Models\carrera\PassengerEstimateReqTopic;
use PreSale\Models\carrera\PassengerEstimateTopic;

/**
 *
 * @desc: 默认拼表单接口
 * @wiki: http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=773466734
 *
 */

class PCapMultiEstimatePriceController extends \PreSale\Core\Controller
{
    /**
     * 入口请求
     * @return void
     */
    public function indexAction() {
        try {
//            //参数初始化
//            $oRequest         = Utils\Request::getInstance();
//            $oEstimateRequest = new Request();
//            $oEstimateRequest->mergeFromJsonArray($oRequest->get());
//
//            //构建请求数据
//            $oProductList = ProductList::getInstance($oEstimateRequest);
//            $aProductList = $oProductList->buildProductList();
//
//            //写预估入参kafka(目前主要提供给地图使用)
//            $this->_writeMultiReqKafka($aProductList);
//
//            //获取账单、支付方式、存量券、pope活动券数据
//            $oPriceLogic        = new PriceLogic();
//            $aResponseParamsNew = $oPriceLogic->getMultiResponse($aProductList);
//
//            //渲染对端数据
//            $oMainRender = MainRender::getInstance($aResponseParamsNew);
//            $oMainRender->setProductList($aProductList);
//            $oMainRender->buildAInfo();
//            $aRenderInfo = $oMainRender->multiExecute();
//
//            //格式化输出
//            $oResponse = new Response();
//            $oResponse->mergeFromJsonArray($aRenderInfo);
//            $sResponseJson = $oResponse->serializeToJsonString();
//            $aResponseInfo = json_decode($sResponseJson, true);
        } catch (Exception $e) {
//            $aErrMsg       = NuwaConfig::text('errno', 'pEstimatePrice_error_msg');
//            $oHandler      = ExceptionHandler::getInstance();
//            $aResponseInfo = $oHandler->handleException($e, ['err_msg' => $aErrMsg, ]);
//            $oResponse     = new Response();
//            $oResponse->mergeFromJsonArray($aResponseInfo);
//            if ($e instanceof ExceptionRespInterface
//                && RespCode::R_ESTIMATE_DOWNSTREAM_FAIL_DEGRADE == $e->getRespCode()
//            ) {
//                header('HTTP/1.1 596 limit access', true, 596);
//            }
//
//            $this->sendJson($oResponse);
//            return;
        }

//        $this->sendJson($sResponseJson, (string)($this->getRequest()->getQuery('callback', false)));
//        fastcgi_finish_request();
//
//        $this->_writeMultiResKafka($aResponseParamsNew, $aResponseInfo);
//
//        (new PublicLog($oEstimateRequest, $aResponseParamsNew, $aResponseInfo, $aProductList))->multiWritePublicLog();
    }

    /**
     * @param Product[] $aProductList $aProductList
     * @return void
     */
//    private function _writeMultiReqKafka($aProductList) {
//        $oTopic     = new PassengerEstimateReqTopic();
//        $aBatchData = [];
//
//        foreach ($aProductList as $oProduct) {
//            $aData = [
//                // 标识
//                'estimate_id'     => $oProduct->oOrderInfo->sEstimateID,
//                // 乘客
//                'passenger_phone' => $oProduct->oPassengerInfo->sPhone,
//                'passenger_id'    => $oProduct->oPassengerInfo->iPid,
//                // 端
//                'app_version'     => $oProduct->oCommonInfo->sAppVersion,
//                'access_key_id'   => $oProduct->oCommonInfo->iAccessKeyID,
//                'menu_id'         => $oProduct->oOrderInfo->sMenuID,
//                'client_type'     => $oProduct->oCommonInfo->iClientType,
//                // 地理
//                'area'            => $oProduct->oAreaInfo->iArea,
//                'county'          => $oProduct->oAreaInfo->iFromCounty,
//                'district'        => $oProduct->oAreaInfo->iDistrict,
//                'from_poi_id'     => $oProduct->oAreaInfo->sFromPoiId,
//                'starting_lng'    => $oProduct->oAreaInfo->fFromLng,
//                'starting_lat'    => $oProduct->oAreaInfo->fFromLat,
//                'starting_name'   => $oProduct->oAreaInfo->sStartingName,
//                'to_poi_id'       => $oProduct->oAreaInfo->sToPoiId,
//                'dest_lng'        => $oProduct->oAreaInfo->fToLng,
//                'dest_lat'        => $oProduct->oAreaInfo->fToLat,
//                'dest_name'       => $oProduct->oAreaInfo->sToName,
//                'from_poi_type'   => $oProduct->oAreaInfo->sFromPoiType,
//                'to_poi_type'     => $oProduct->oAreaInfo->sToPoiType,
//                'stopover_points' => !empty($oProduct->oCommonInfo->sStopoverPoints) ? $oProduct->oCommonInfo->sStopoverPoints : '[]',
//                // 产品
//                'require_level'   => $oProduct->oOrderInfo->iRequireLevel,
//                'product_id'      => $oProduct->oOrderInfo->iProductId,
//
//            ];
//            $aBatchData[] = $aData;
//        }
//
//        $oTopic->batchSync($aBatchData);
//    }

    /**
     * function _writeMultiResKafka
     * @param array $aResponseParamsNew new info
     * @param array $aResponseInfo      info
     * @return void
     */
//    private function _writeMultiResKafka($aResponseParamsNew, $aResponseInfo) {
//        $aBatchData = [];
//        foreach ($aResponseParamsNew as $aInfosNew) {
//            $aCurEstimateData = [];
//            foreach ($aResponseInfo['data']['estimate_data'] as $aData) {
//                if ($aData['estimate_id'] == $aInfosNew['order_info']['estimate_id']) {
//                    $aCurEstimateData = $aData;
//                }
//            }
//
//            if (!empty($aCurEstimateData)) {
//                $aData = $this->_genKafkaForMultiNew($aInfosNew, $aCurEstimateData);
//                !empty($aData) && $aBatchData[] = $aData;
//            }
//        }
//
//        if (!empty($aBatchData)) {
//            $oTopic = new PassengerEstimateTopic();
//            $oTopic->batchSync($aBatchData);
//        }
//    }

    /**
     * @param array $aInfos        aInfos
     * @param array $aEstimateData aEstimateData
     * @return array
     */
//    private function _genKafkaForMultiNew(array $aInfos, array $aEstimateData) {
//
//        $sCarLevel = $aInfos['order_info']['require_level'];
//
//        if (empty($aInfos['passenger_info']['pid'])) {
//            return [];
//        }
//
//        if (!isset($aInfos['bill_info'], $aInfos['bill_info']['bills'], $aInfos['bill_info']['bills'][$sCarLevel])) {
//            return [];
//        }
//
//        //拼车
//        if (Utils\Horae::isCarpool($aInfos['order_info']['combo_type'], $aInfos['order_info']['require_level'])) {
//            if (empty($aInfos['bill_info']['is_carpool_open'])) {
//                return [];
//            }
//        }
//
//        $aProductInfo = $aInfos['bill_info']['product_infos'][$sCarLevel];
//        $aBillInfo    = $aInfos['bill_info']['bills'][$sCarLevel];
//
//        $aPassengerKafka = array();
//        $aPassengerKafka['passenger_phone'] = $aInfos['passenger_info']['phone'];
//        $aPassengerKafka['passenger_id']    = $aInfos['passenger_info']['pid'];
//        $aPassengerKafka['district']        = $aInfos['order_info']['district'];
//        $aPassengerKafka['area']            = $aInfos['order_info']['area'];
//        $aPassengerKafka['channel']         = $aInfos['order_info']['channel'];
//        $aPassengerKafka['starting_lng']    = $aInfos['order_info']['from_lng'];
//        $aPassengerKafka['starting_lat']    = $aInfos['order_info']['from_lat'];
//        $aPassengerKafka['county']          = (string)($aInfos['order_info']['county']);
//        $aPassengerKafka['dest_lng']        = $aInfos['order_info']['to_lng'];
//        $aPassengerKafka['dest_lat']        = $aInfos['order_info']['to_lat'];
//        $aPassengerKafka['from_poi_id']     = $aInfos['order_info']['from_poi_id'];
//        $aPassengerKafka['to_poi_id']       = $aInfos['order_info']['to_poi_id'];
//        $aPassengerKafka['create_time']     = time();
//        $aPassengerKafka['product_id']      = $aInfos['order_info']['product_id'];
//        $aPassengerKafka['scene_type']      = $aInfos['order_info']['scene_type'] ?? 0;
//        $aPassengerKafka['car_type']        = $sCarLevel;
//        $aPassengerKafka['multi_require_product'] = '';
//        $aPassengerKafka['preference_product']    = '';
//        $aPassengerKafka['is_anycar']      = 0;
//        $aPassengerKafka['n_tuple']        = json_encode($aInfos['order_info']['n_tuple'], JSON_UNESCAPED_UNICODE);
//        $aPassengerKafka['current_lng']    = (float)($this->getRequest()->getQuery('lng', false));
//        $aPassengerKafka['current_lat']    = (float)($this->getRequest()->getQuery('lat', false));
//        $aPassengerKafka['client_type']    = (int)$this->getRequest()->getQuery('client_type', false);
//        $aPassengerKafka['platform_type']  = (int)$this->getRequest()->getQuery('platform_type', false);
//        $aPassengerKafka['starting_name']  = (string)($aInfos['order_info']['starting_name']);
//        $aPassengerKafka['dest_name']      = (string)($aInfos['order_info']['dest_name']);
//        $aPassengerKafka['departure_time'] = $aInfos['order_info']['departure_time'];
//        $aPassengerKafka['is_fast_car']    = (int)($aInfos['order_info']['is_fast_car']);
//        $aPassengerKafka['oType']          = $aInfos['order_info']['oType'];
//        $aPassengerKafka['app_version']    = $aInfos['common_info']['app_version'];
//        $aPassengerKafka['origin_id']      = $aInfos['common_info']['origin_id'];
//        $aPassengerKafka['menu_id']        = $aInfos['order_info']['menu_id'];
//
//        $aBill = $aInfos['bill_info'];
//        $aPassengerKafka['bubble_id']   = $aBill['estimate_id'];
//        $aPassengerKafka['estimate_id'] = $aBill['estimate_id'];
//        $aPassengerKafka['combo_type']  = (int)($aProductInfo['combo_type']); // 账单的combo_type
//
//        //拼车
//        if (Utils\Horae::isCarpool($aInfos['order_info']['combo_type'], $aInfos['order_info']['require_level'])) {
//            $aPassengerKafka['combo_type'] = (int)($aProductInfo['combo_type'] ?? \BizLib\Constants\Horae::TYPE_COMBO_CARPOOL);
//        }
//
//        $aPassengerKafka['estimate_distance_metre'] = $aInfos['bill_info']['driver_metre'];
//        $aPassengerKafka['estimate_time_minutes']   = $aInfos['bill_info']['driver_minute'];
//        $aPassengerKafka['basic_total_fee']         = $aBillInfo['basic_total_fee'] ?? 0;
//        $aPassengerKafka['dynamic_total_fee']       = $aBillInfo['dynamic_total_fee'] ?? 0;
//        $aPassengerKafka['dynamic_diff_price']      = $aBillInfo['dynamic_diff_price'] ?? 0;
//        $aPassengerKafka['estimate_fee']            = $aInfos['activity_info'][0]['estimate_fee'];
//        $aPassengerKafka['cap_price']          = $aBillInfo['cap_price'];
//        $aPassengerKafka['final_coupon_value'] = $aInfos['activity_info'][0]['final_coupon_value'];
//        $aPassengerKafka['pay_type']           = $this->_getSelectedPayType($aInfos['payments_info']['user_pay_info']['busi_payments'] ?? []);
//
//        $aDynamicInfo = $aBillInfo['dynamic_info'] ?? [];
//        unset($aDynamicInfo['dynamic_price_id']);
//        $aPassengerKafka['dynamic_info'] = json_encode($aDynamicInfo);
//
//        $aPassengerKafka['select_type']    = $aEstimateData['select_type'] ?? 0;
//        $aPassengerKafka['recommend_type'] = $aEstimateData['recommend_type'] ?? 0;
//        $aPassengerKafka['form_show_type'] = $aEstimateData['form_show_type'] ?? 0;
//        return $aPassengerKafka;
//    }

    /**
     * @param array $aPayments aPayments
     * @return int
     */
//    private function _getSelectedPayType($aPayments) {
//        foreach ($aPayments as $aPayment) {
//            if (!empty($aPayment['isSelected'])) {
//                return $aPayment['tag'];
//            }
//        }
//
//        return 0;
//    }
}
