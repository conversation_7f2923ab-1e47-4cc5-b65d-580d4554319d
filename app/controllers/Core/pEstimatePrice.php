<?php

use BizLib\Config as NuwaConfig;
use BizLib\Utils\PublicLog;
use BizCommon\Models\order\Order;
/*
 * 根据乘客输入出发地、目的地进行车费预估入口
 * 目前调用端:滴滴乘客端、微信滴滴出行webApp入口、支付宝滴滴出行webApp入口、Uber中国乘客端、企业级网页和企业级乘客端、OpenApi.
 *
 * <AUTHOR>
 * @date: 17/2/24
 */
use PreSale\Logics\estimatePrice\ParamsLogic;
use PreSale\Logics\estimatePrice\response\ResponseLogic;
use PreSale\Logics\estimatePrice\EstimateLogic;
use PreSale\Models\carrera\PassengerEstimateTopic;
use PreSale\Logics\estimatePrice\bill\CommonBillLogic;
use BizLib\ExceptionHandler;

/**
 * Class pEstimatePrice.
 */

class pEstimatePriceController extends \PreSale\Core\Controller
{
    public function indexAction() {
        try {
            //参数处理实例
            $oParams = ParamsLogic::getInstance();

            //获取预估需要的参数
            $aEstimatePriceParams = $oParams->getBillParams((array) $this->getRequest()->getQuery(null, false, true));

            //获取账单、支付方式、优惠券、乘客运营活动券数据
            $aResponseParams = EstimateLogic::getInstance($aEstimatePriceParams)->getResponseData();

            //对返回数据进行格式化
            $aResponseInfo = ResponseLogic::getInstance($aResponseParams, array())->execute();
        } catch (\Exception $e) {
            $aErrMsg       = NuwaConfig::text('errno', 'pEstimatePrice_error_msg');
            $oHandler      = ExceptionHandler::getInstance();
            $aResponseInfo = $oHandler->handleException($e, ['err_msg' => $aErrMsg, ]);
            $this->sendJson($aResponseInfo, (string)($this->getRequest()->getQuery('callback', false)));

            return;
        }

        $this->sendJson($aResponseInfo, (string)($this->getRequest()->getQuery('callback', false)));
        fastcgi_finish_request();
        $this->_writePublicLog($aResponseParams, $aResponseInfo);
        $this->_writeKafka($aResponseParams);
    }

    /**
     * 写public日志.
     *
     * @param array $aInfos 所有数据
     */
    private function _writePublicLog(array $aInfos, array $aResponseInfo) {
        $sCarLevel         = $aInfos['order_info']['require_level'];
        $aBillInfos        = $aInfos['bill_info']['bills'][$sCarLevel];
        $aDynamicPriceData = $this->_getDynamicStatisticData($aInfos);
        $aCouponLog        = $this->_getCouponLog($aInfos['order_info']['channel'], $aInfos['activity_info']['coupon_info'] ?? []);
        if (\BizLib\Constants\Horae::O_TRAFFIC_TYPE_GS_FROM_AIRPORT == $aInfos['order_info']['oType']) {
            $traffic_type = Order::TYPE_GS_FROM_AIRPORT;
        } elseif (\BizLib\Constants\Horae::O_TRAFFIC_TYPE_GS_TO_AIRPORT == $aInfos['order_info']['oType']) {
            $traffic_type = Order::TYPE_GS_TO_AIRPORT;
        } else {
            $traffic_type = $aInfos['order_info']['combo_type'];
        }

        $aBillInfos['carpool']['display_lines']    = CommonBillLogic::formatDisplayLines($aBillInfos['carpool']['display_lines'] ?? []);
        $aBillInfos['noncarpool']['display_lines'] = CommonBillLogic::formatDisplayLines($aBillInfos['noncarpool']['display_lines'] ?? []);
        $aEstimateStatistic = [
            'opera_stat_key'                       => $this->_getPublicKey($aInfos),

            'pid'                                  => $aInfos['passenger_info']['pid'] ?? 0,
            'phone'                                => $aInfos['passenger_info']['phone'] ?? 0,
            'coupon'                               => json_encode($aCouponLog),
            'channel'                              => $aInfos['order_info']['channel'],
            'product_id'                           => $aInfos['order_info']['product_id'],
            'area'                                 => $aInfos['order_info']['area'],
            'district'                             => $aInfos['order_info']['district'],
            'require_car_level'                    => $sCarLevel,
            'flng'                                 => $aInfos['order_info']['from_lng'],
            'flat'                                 => $aInfos['order_info']['from_lat'],
            'tlng'                                 => $aInfos['order_info']['to_lng'],
            'tlat'                                 => $aInfos['order_info']['to_lat'],
            'like_wait'                            => $aInfos['order_info']['willing_wait'],
            'order_type'                           => $aInfos['order_info']['order_type'],
            'guide_state'                          => $aInfos['order_info']['guide_state'],
            'from_name'                            => str_replace(PHP_EOL, '', $aInfos['order_info']['from_name']),
            'to_name'                              => str_replace(PHP_EOL, '', $aInfos['order_info']['to_name']),
            'scene_type'                           => (int)($aInfos['order_info']['scene_type']),  //场景类型：跨城拼车在用
            'route_id'                             => (int)($aInfos['bill_info']['route_info']['route_id']),
            'route_group'                          => (int)($aInfos['bill_info']['route_info']['route_group']),
            'from_city'                            => $aInfos['bill_info']['route_info']['from_city'] ?? '',
            'dest_city'                            => $aInfos['bill_info']['route_info']['dest_city'] ?? '',
            'left_departure_time'                  => empty($aInfos['order_info']['departure_range'][0]) ? intval(json_decode($aInfos['bill_info']['route_info']['suggest_time'], true)[0]) : (int)($aInfos['order_info']['departure_range'][0]),
            'right_departure_time'                 => empty($aInfos['order_info']['departure_range'][1]) ? intval(json_decode($aInfos['bill_info']['route_info']['suggest_time'], true)[1]) : (int)($aInfos['order_info']['departure_range'][1]),

            'dynamic'                              => $aDynamicPriceData['is_dynamic'],
            'dynamic_price_id'                     => $aDynamicPriceData['dynamic_id'],
            'carpool_dynamic_price_id'             => $aDynamicPriceData['carpool_dynamic_id'],
            'dynamic_price'                        => json_encode($aDynamicPriceData['dynamic_price']),
            'dynamic_kind'                         => json_encode($aDynamicPriceData['dynamic_kind']),
            'dynamic_type'                         => json_encode($aDynamicPriceData['dynamic_type']),
            'near_driver_num'                      => json_encode($aDynamicPriceData['near_driver_num']),
            'near_order_num'                       => json_encode($aDynamicPriceData['near_order_num']),
            'avg_driver_start_distance'            => json_encode($aDynamicPriceData['avg_driver_start_distance']),
            'start_dest_distance'                  => json_encode($aDynamicPriceData['start_dest_distance']),
            'place'                                => json_encode($aDynamicPriceData['place']),
            'default_carpool'                      => $aInfos['bill_info']['is_carpool_open'] ? $aResponseInfo['data']['estimate_data'][0]['is_default'] : 0, // 0 默认非拼车 1拼车

            'bubble_id'                            => $aInfos['bill_info']['estimate_id'],
            'time_cost'                            => $aInfos['bill_info']['driver_minute'],
            'pre_total_fee'                        => $aBillInfos['noncarpool']['total_fee'] ?? 0,
            'nocarpool_fee'                        => $aInfos['activity_info']['estimate_fee'],
            'is_carpool_request'                   => (int)($aInfos['bill_info']['is_carpool_open']),
            'carpool_origin_fee'                   => $aBillInfos['carpool']['dynamic_total_fee'] ?? 0,
            'discount_fee'                         => $aInfos['activity_info']['discount_fee'],

            'station_id'                           => $aInfos['bill_info']['carpool_station_info']['uid'] ?? 0,
            'dynamic_discount_id'                  => $aBillInfos['noncarpool']['discount_info']['dynamic_price_id'] ?? '',
            'carpool_dynamic_discount_id'          => $aBillInfos['carpool']['discount_info']['dynamic_price_id'] ?? '',

            'likeWait_open_flag'                   => $aInfos['bill_info']['is_carpool_open'],
            'wait_discount'                        => $aBillInfos['carpool']['wait_discount'] ?? 0.1,
            'wait_minute'                          => $aBillInfos['carpool']['dynamic_info']['wait_minute'] ?? 10,

            'traffic_type'                         => $traffic_type,
            'scene_enter'                          => $aInfos['order_info']['scene_enter'],
            'call_car_type'                        => $aInfos['order_info']['call_car_type'],

            'is_hit_member_capping'                => (int)($aBillInfos['noncarpool']['is_hit_member_capping'] ?? 0),
            'member_dynamic_capping'               => $aBillInfos['noncarpool']['member_dynamic_capping'] ?? -1,
            'dynamic_price_without_member_capping' => $aBillInfos['noncarpool']['dynamic_price_without_member_capping'] ?? 0,
            'member_level_id'                      => $aInfos['passenger_info']['member_profile']['level_id'] ?? 0,

            'imei'                                 => $aInfos['common_info']['imei'],
            'appversion'                           => $aInfos['common_info']['app_version'],
            'user_type'                            => $aInfos['common_info']['data_type'],
            'datatype'                             => $aInfos['common_info']['data_type'],
            'pLang'                                => $aInfos['common_info']['lang'],
            'default_pay_type'                     => $aInfos['payments_info']['user_pay_info']['default_tag'] ?? 0,

            'br_default_pay_type'                  => $this->_getSelectedPayType($aInfos['payments_info']['user_pay_info']['busi_payments'] ?? []), // 巴西支付方式
            'carpool_default_pay_type'             => $this->_getSelectedPayType($aInfos['payments_info']['user_pay_info']['carpool']['busi_payments'] ?? []),
            'noncarpool_default_pay_type'          => $this->_getSelectedPayType($aInfos['payments_info']['user_pay_info']['noncarpool']['busi_payments'] ?? []),

            'designated_driver'                    => $aInfos['common_info']['designated_driver'],
            'combo_type'                           => $aInfos['order_info']['combo_type'],
            'county'                               => (string)($aInfos['order_info']['county']),

            //小费功能已下线
            'tip'                                  => 0,
            //目标用户已下线
            'target_user'                          => 1,
            //智能导流已下线
            'is_smart_request'                     => 0,
            //使用动态折扣或mis折扣已下线
            'price_discount'                       => 0,
            'dynamic_discount'                     => 0,
            'discount_type'                        => 0,
            'carpool_red_packet'                   => $aBillInfos['carpool']['display_lines']['red_packet']['value'] ?? 0.0,
            'noncarpool_red_packet'                => $aBillInfos['noncarpool']['display_lines']['red_packet']['value'] ?? 0.0,
            'carpool_red_packet_list'              => json_encode($aBillInfos['carpool']['red_packet_list'] ?? []),
            'noncarpool_red_packet_list'           => json_encode($aBillInfos['noncarpool']['red_packet_list'] ?? []),
            'fixed_preferential'                   => $aBillInfos['noncarpool']['fixed_preferential'] ?? 0,
        ];
        PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);
    }

    /**
     * 获取public日志key.
     *
     * @param array $aInfos
     *
     * @return string
     */
    private function _getPublicKey($aInfos) {
        if ($aInfos['common_info']['is_from_guide']) {
            return 'g_order_prefee_show_platform';
        }

        return 'g_order_prefee_show';
    }

    /**
     * 获取动态调价数据.
     *
     * @return array 动态调价相关字段
     */
    private function _getDynamicStatisticData($aInfos) {
        $sCarLevel = $aInfos['order_info']['require_level'];
        $aBillInfo = $aInfos['bill_info']['bills'][$sCarLevel];
        $aResult['dynamic_id']         = $aBillInfo['noncarpool']['dynamic_info']['dynamic_price_id'] ?? 0;
        $aResult['carpool_dynamic_id'] = $aBillInfo['carpool']['dynamic_info']['dynamic_price_id'] ?? 0;
        $aResult['is_dynamic']         = 0;
        $fDynamicPrice = $aBillInfo['noncarpool']['dynamic_diff_price'] ?? 0;
        $fWhitoutMemberCappingDynamicPrice = $aBillInfo['noncarpool']['dynamic_price_without_member_capping'] ?? 0;
        $fCarpoolPoolDynamicPrice          = $aBillInfo['carpool']['dynamic_diff_price'] ?? 0;
        $fWhitoutMemberCappingCarpoolPoolDynamicPrice = $aBillInfo['carpool']['dynamic_price_without_member_capping'] ?? 0;
        if ($fDynamicPrice > 0 || $fCarpoolPoolDynamicPrice > 0
            || $fWhitoutMemberCappingDynamicPrice || $fWhitoutMemberCappingCarpoolPoolDynamicPrice
        ) {
            $aResult['is_dynamic'] = 1;
        }

        $aResult['dynamic_price'][$sCarLevel]   = array(
            'carpool'    => $fCarpoolPoolDynamicPrice,
            'noncarpool' => $fDynamicPrice,
        );
        $aResult['dynamic_kind'][$sCarLevel]    = array(
            'carpool'    => $aBillInfo['carpool']['dynamic_info']['dynamic_kind'] ?? 0,
            'noncarpool' => $aBillInfo['noncarpool']['dynamic_info']['dynamic_kind'] ?? 0,
        );
        $aResult['dynamic_type'][$sCarLevel]    = array(
            'carpool'    => $aBillInfo['carpool']['dynamic_info']['dynamic_type'] ?? 0,
            'noncarpool' => $aBillInfo['noncarpool']['dynamic_info']['dynamic_type'] ?? 0,
        );
        $aResult['near_driver_num'][$sCarLevel] = array(
            'carpool'    => $aBillInfo['carpool']['dynamic_info']['near_driver_num'] ?? 0,
            'noncarpool' => $aBillInfo['noncarpool']['dynamic_info']['near_driver_num'] ?? 0,
        );
        $aResult['near_order_num'][$sCarLevel]  = array(
            'carpool'    => $aBillInfo['carpool']['dynamic_info']['near_order_num'] ?? 0,
            'noncarpool' => $aBillInfo['noncarpool']['dynamic_info']['near_order_num'] ?? 0,
        );
        $aResult['avg_driver_start_distance'][$sCarLevel] = array(
            'carpool'    => $aBillInfo['carpool']['dynamic_info']['avg_driver_start_distance'] ?? 0,
            'noncarpool' => $aBillInfo['noncarpool']['dynamic_info']['avg_driver_start_distance'] ?? 0,
        );
        $aResult['place'][$sCarLevel] = array(
            'carpool'    => $aBillInfo['carpool']['dynamic_info']['place'] ?? 0,
            'noncarpool' => $aBillInfo['noncarpool']['dynamic_info']['place'] ?? 0,
        );
        $aResult['start_dest_distance'][$sCarLevel] = array(
            'carpool'    => round($aInfos['bill_info']['driver_metre'] / 1000, 2),
            'noncarpool' => round($aInfos['bill_info']['driver_metre'] / 1000, 2),
        );

        return $aResult;
    }

    /**
     * 乘客订单预估信息写入kafka.
     *
     * @param array $aInfos kafka信息
     */
    private function _writeKafka(array $aInfos) {
        if (!empty($aInfos['passenger_info']['pid'])) {
            $sCarLevel       = $aInfos['order_info']['require_level'];
            $aPassengerKafka = array();
            $aPassengerKafka['passenger_phone'] = $aInfos['passenger_info']['phone'];
            $aPassengerKafka['passenger_id']    = $aInfos['passenger_info']['pid'];
            $aPassengerKafka['district']        = $aInfos['order_info']['district'];
            $aPassengerKafka['area']            = $aInfos['order_info']['area'];
            $aPassengerKafka['channel']         = $aInfos['order_info']['channel'];
            $aPassengerKafka['starting_lng']    = $aInfos['order_info']['from_lng'];
            $aPassengerKafka['starting_lat']    = $aInfos['order_info']['from_lat'];
            $aPassengerKafka['county']          = (string)($aInfos['order_info']['county']);
            $aPassengerKafka['dest_lng']        = $aInfos['order_info']['to_lng'];
            $aPassengerKafka['dest_lat']        = $aInfos['order_info']['to_lat'];
            $aPassengerKafka['from_poi_id']     = $aInfos['order_info']['from_poi_id'];
            $aPassengerKafka['to_poi_id']       = $aInfos['order_info']['to_poi_id'];
            $aPassengerKafka['create_time']     = time();
            $aPassengerKafka['product_id']      = $aInfos['order_info']['product_id'];
            $aPassengerKafka['scene_type']      = $aInfos['order_info']['scene_type'];
            $aPassengerKafka['car_type']        = $sCarLevel;

            $aOrderInfo = $aInfos['order_info'];
            $aNewField  = \BizCommon\Logics\Order\FieldConverter::getNDimensionalNewField($aOrderInfo);
            $aOrderInfo = array_merge($aNewField, $aOrderInfo);
            $aPassengerKafka['n_tuple'] = json_encode(\BizCommon\Logics\Order\FieldOrderNTuple::getOrderNTupleByOrder($aOrderInfo), JSON_UNESCAPED_UNICODE);

            $oPassengerEstimateTopic = new PassengerEstimateTopic();
            $oPassengerEstimateTopic->sync($aPassengerKafka);
        }
    }

    /**
     * 生成券相关的统计信息.
     *
     * @param int   $iChannel    乘客端渠道
     * @param array $aCouponInfo 默认券和乘客运营券信息
     */
    private function _getCouponLog($iChannel, $aCouponInfo) {
        foreach (['carpool', 'noncarpool'] as $sMode) {
            if (empty($aCouponInfo[$sMode]['default_coupon'])) {
                $aRet[$sMode]['default_coupon'] = [];
            } else {
                $aRet[$sMode]['default_coupon'] = [
                    'channel'       => $iChannel,
                    'batchid'       => $aCouponInfo[$sMode]['default_coupon']['batchid'] ?? 0,
                    'amount'        => ($aCouponInfo[$sMode]['default_coupon']['amount'] ?? 0) / 100,
                    'estimate_show' => $aCouponInfo[$sMode]['default_coupon']['estimate_show'] ?? 0,
                ];
            }

            if (empty($aCouponInfo[$sMode]['activity_coupon'])) {
                $aRet[$sMode]['activity_coupon'] = [];
            } else {
                $aRet[$sMode]['activity_coupon'] = [
                    'channel'       => $iChannel,
                    'batchid'       => $aCouponInfo[$sMode]['activity_coupon']['batchid'] ?? 0,
                    'amount'        => $aCouponInfo[$sMode]['activity_coupon']['money'] ?? 0,
                    'estimate_show' => $aCouponInfo[$sMode]['activity_coupon']['estimate_show'] ?? 0,
                ];
            }
        }

        return $aRet;
    }

    private function _getSelectedPayType($aPayments) {
        foreach ($aPayments as $aPayment) {
            if (!empty($aPayment['isSelected'])) {
                return $aPayment['tag'];
            }
        }

        return 0;
    }
}
