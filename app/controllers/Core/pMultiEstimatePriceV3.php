<?php

use B<PERSON><PERSON>om<PERSON>\Utils\Horae;
use BizLib\Config as NuwaConfig;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\ErrCode;
use BizLib\ErrCode\RespCode;
use BizLib\Exception\ExceptionWithResp;
use BizLib\Log as NuwaLog;
use BizLib\Utils;
use BizCommon\Constants\OrderNTuple;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\ProductCategory;
use PreSale\Logics\athena\TriggerBubbleAthena;
use PreSale\Logics\v3Estimate\CategoryInfoLogic;
use PreSale\Logics\v3Estimate\ClassifyFoldLogic;
use PreSale\Logics\v3Estimate\FeatureRecord;
use Dirpc\SDK\PreSale\NewFormEstimateData;
use Disf\SPL\Trace;
use Dirpc\SDK\PreSale\MultiEstimatePriceRequest as Request;
use Dirpc\SDK\PreSale\NewFormMultiEstimatePriceResponse as Response;
use Dirpc\SDK\PreSale\ErrButtonResponse as ErrButtonResponse;
use Dirpc\SDK\PreSale\ErrButtonData as ErrButtonData;
use Dirpc\SDK\PreSale\ErrButton as ErrButton;
use Nuwa\Protobuf\Internal\Message;
use PreSale\Core\Controller;
use PreSale\Infrastructure\Util\AsyncMode\AsyncContainer;
use PreSale\Infrastructure\Util\RenderElapsedTimeLogTrait;
use PreSale\Logics\communicateInfo\estimateData\EstimateDataLite;
use PreSale\Logics\newFormTab\Tab;
use PreSale\Logics\v3Estimate\AthenaRecommend;
use PreSale\Logics\v3Estimate\FormAB\NewStyleFormABParam;
use PreSale\Logics\v3Estimate\FormAB\SurpriseDiscountABParam;
use PreSale\Logics\v3Estimate\IntercityAnycarFilter;
use PreSale\Logics\v3Estimate\IntercityClassifyTab;
use PreSale\Logics\v3Estimate\CarpoolGuideBarChoose;
use PreSale\Logics\v3Estimate\multiResponse\layout\builder\ClassifyRecLayoutBuilder;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\BargainRangeLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\FarMustCheaperBoxLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\ChaoZhiDaBoxLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\SurpriseDiscountBoxLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutAbstract;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutBuilder;
use PreSale\Logics\v3Estimate\BizProduct;
use PreSale\Logics\v3Estimate\MultiEstimatePublicLog;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;
use PreSale\Logics\v3Estimate\DdsDecision;
use PreSale\Logics\v3Estimate\PriceLogic;
use PreSale\Logics\v3Estimate\CompensationLogic;
use PreSale\Logics\v3Estimate\multiRequest\ProductList;
use PreSale\Logics\v3Estimate\DecisionV2Service;
use PreSale\Models\carrera\PassengerEstimateTopic;
use BizLib\ExceptionHandler;
use BizLib\Exception\ExceptionRespInterface;
use PreSale\Logics\v3Estimate\multiResponse\MainRender;
use PreSale\Logics\estimatePrice\params\ParamsLogic;
use PreSale\Logics\estimatePrice\EstimateDegradeCode;
use BizLib\Log;
use PreSale\Models\carrera\PassengerMultiEstimateReqTopic;
use PreSale\Logics\v3Estimate\multiResponse\PreRender\CarpoolPreMatchLogic;
use BizCommon\Models\Order\Order;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\v3Estimate\StationBusInfoLogic;
use BizLib\Utils\Language;
use Dukang\PropertyConst\Order\OrderEstimatePcId;
use TripcloudCommon\Utils\Product as TripcloudProduct;

/**
 * Class pMultiEstimatePrice.
 */
class PMultiEstimatePriceV3Controller extends Controller
{
    use RenderElapsedTimeLogTrait;

    private $_aUnResponse = [
        'errno'  => -666,
        'errmsg' => 'unknown',
    ];

    /**
     * @return void
     */
    public function indexAction() {
        Message::setEmitDefaults(true);
        try {
            // 参数初始化
            $oRequest         = Utils\Request::getInstance();
            $oEstimateRequest = new Request();
            //让参数支持get和post请求
            $aGetParam  = $oRequest->get();
            $aPostParam = $oRequest->post(null,  false, false);
            $aGetParam  = is_array($aGetParam) ? $aGetParam : array();
            $aPostParam = is_array($aPostParam) ? $aPostParam : array();
            $oEstimateRequest->mergeFromJsonArray(array_merge($aGetParam, $aPostParam));
            ParamsLogic::checkDaCheAnyCarParams($oEstimateRequest);

            //构建通用参数
            $oBizCommonInfo = new BizCommonInfo($oEstimateRequest);
            // 获取基础品类
            $oProductLogic  = new ProductList($oEstimateRequest, $oBizCommonInfo);
            $aBizProductMap = $oProductLogic->buildProductMap($oBizCommonInfo);
            // 写预估入参kafka 批量的 (地图/Athena/dapes均使用)
            $this->_writeMultiReqKafkaNew($oBizCommonInfo, $aBizProductMap);
            // 触发 Athena 进行特征计算
             $this->callAthenaTrigger($oBizCommonInfo, $aBizProductMap);

            //调duse 获取站点巴士班次路线等
            $oStationBusInfoLogic = new StationBusInfoLogic($oBizCommonInfo,$aBizProductMap);
            $oBusInfo       = $oStationBusInfoLogic->getBusInfo();
            $aBizProductMap = $oStationBusInfoLogic->rewrite($aBizProductMap, $oBusInfo);

            // 调用price-api获取账单、支付方式、优惠券、乘客运营活动券数据 补充至单项的Products中
            $oPriceLogic    = new PriceLogic($oBizCommonInfo, $aBizProductMap, $oBusInfo, $this->bIsCheatingTraffic);
            $aBizProductMap = $oPriceLogic->getBizProductsWithPrice();

            // 调用赔付trigger接口，获取赔付侧策略的命中结果，赔付侧异步触发athena计算赔付模型
            $oCompensationLogic = CompensationLogic::getInstance();
            $oCompensationLogic->init($oBizCommonInfo, $aBizProductMap);

            //调用DDS 获取决策信息，分组/排序/选中
            $oDecisionLogic = DdsDecision::getInstance();
            $oDecisionLogic->init($oBizCommonInfo, $aBizProductMap);

            //读取配置 重新给聚合品类盒子进行赋值
            $oDecisionV2Service = DecisionV2Service::getInstance();
            $oDecisionV2Service->init($oBizCommonInfo, $aBizProductMap);

            // DDS Decision  (*过滤品类)
            $aBizProductMap = $oDecisionLogic->filterProducts($aBizProductMap);

            // 用于mock异常场景，提供给端QA回归的能力
            $this->_mockExceptionReturn($oBizCommonInfo);

            //设置预估数据的缓存，用于沟通组件渲染表单其他数据
            $oEstimateDataLite = new EstimateDataLite();
            $oEstimateDataLite->initEstimateDataCache($aBizProductMap);

            $aBizProductMap = $this->filterProductsAfterPrice($aBizProductMap, $oBizCommonInfo);

            //批量获取拼车预匹配数据（愿等、ETD、ETS、小巴地图token等）
            CarpoolPreMatchLogic::getInstance()->loadCarpoolPreMatchInfoV3($aBizProductMap, $oBizCommonInfo);
            //通过预匹配数据过滤小巴不可用车型
            $aBizProductMap = $this->filterProductsBeforeAthena($aBizProductMap, $oBizCommonInfo);

            //调用Athena 获取推荐信息
            $oAthenaRecommend = AthenaRecommend::getInstance();
            $oAthenaRecommend->init($oBizCommonInfo, $aBizProductMap);

            //根据Athena的返回结果，对盒子重新移动
            $oDecisionV2Service->moveSubGroupByAthena();

            //根据Athena的返回结果，重新给聚合品类盒子进行赋值
            $oDecisionV2Service->filterAggregationPcID();

            // Athena 品类准入  (*过滤品类)
            $aBizProductMap = $oAthenaRecommend->filterBizProducts($aBizProductMap);

            // 渲染前  品类准入  (*过滤品类)
            list($aBizProductMap, $aRemoved) = $this->filterProductsBeforeRender($aBizProductMap, $oBizCommonInfo);

            // 渲染前 处理品类/盒子折叠or收盒子
            ClassifyFoldLogic::getInstance()->init($oBizCommonInfo, $aBizProductMap);

            // 渲染对端数据
            $aRenderInfo = UtilsCommon::pairErrNo(GLOBAL_SUCCESS);
            //构建estimate_data 和 external_data
            $oMainRender = new MainRender($aBizProductMap, $oBizCommonInfo, $aRemoved);
            if (NewStyleFormABParam::getInstance($oBizCommonInfo->getApolloParams(null)) ->getHitNewStyleForm()) {
                $aEstimateData = $oMainRender->buildEstimateDataFromMamba();
            } else {
                $aEstimateData = $oMainRender->buildEstimateData();
            }

            $oMainRender->postBuild();
            $aRenderInfo['data']['estimate_data'] = $aEstimateData;
            $aRenderInfo = $oMainRender->buildExternalData($aRenderInfo);

            // 构建提示文案
            $aRenderInfo['data']['multi_route_tips_data'] = $this->_buildMultiRouteTipsData();

            // 构建layout
            $oLayoutBuilder = LayoutBuilder::select($aBizProductMap, $oBizCommonInfo, $oBusInfo);
            $aLayout        = $oLayoutBuilder->buildLayout();
            $aRenderInfo['data']['layout'] = $aLayout;

            // 构建推荐layout
            $oRecLayoutBuilder = new ClassifyRecLayoutBuilder($aBizProductMap, $oBizCommonInfo, $oBusInfo);
            $aRecLayout        = $oRecLayoutBuilder->buildLayout();
            $aRenderInfo['data']['rec_layout'] = $aRecLayout;

            $aRenderInfo['data']['group_data']    = $this->_buildGroupData();
            $aRenderInfo['data']['category_info'] = $this->buildCategoryInfo($oBizCommonInfo, $aBizProductMap);

            // 格式化输出
            $oResponse = new Response();
            $oResponse->mergeFromJsonArray($aRenderInfo);
            $sResponseJson = $oResponse->serializeToJsonString();
            $aResponseInfo = json_decode($sResponseJson, true);
            if (isset($aResponseInfo['errno'])) {
                if (!empty($aResponseInfo['errno'])) {
                    $this->_printErrNoAndErrMsg($aResponseInfo);
                } elseif (0 != $aResponseInfo['errno']) {
                    $this->_printErrNoAndErrMsg($this->_aUnResponse);
                }
            } else {
                $this->_printErrNoAndErrMsg($this->_aUnResponse);
            }
        } catch (Exception $e) {
            $aErrMsg       = NuwaConfig::text('errno', 'pEstimatePrice_error_msg');
            $oHandler      = ExceptionHandler::getInstance();
            $aResponseInfo = $oHandler->handleException($e, ['err_msg' => $aErrMsg, ]);
            $oResponse     = new Response();
            $oResponse->mergeFromJsonArray($aResponseInfo);
            //对粤港车的err有特殊处理逻辑
            if (Code::E_PASSENGER_HK_MAINLAND_CAR_EMPTY == $e->getCode()) {
                $oResponse = $this->_buildHKMainLandCarResponse();
            }

            if ($e instanceof ExceptionRespInterface
                // && RespCode::R_ESTIMATE_DOWNSTREAM_FAIL_DEGRADE == $e->getRespCode()
                && EstimateDegradeCode::isDegradeCode($e->getRespCode())
            ) {
                header('HTTP/1.1 596 limit access', true, 596);
            }

            $this->_printErrNoAndErrMsg($aResponseInfo);

            $this->sendJson($oResponse, (string)($this->getRequest()->getQuery('callback', false)));
            return;
        }

        $oEstimateDataLite->appendEstimateDataCache($aEstimateData);
        $oEstimateDataLite->setEstimateDataCache(Trace::traceId());

        $this->sendJson($sResponseJson, (string)($this->getRequest()->getQuery('callback', false)));
        fastcgi_finish_request();

        AsyncContainer::run();

        $this->_writeMultiResKafka($oBizCommonInfo, $aBizProductMap, $aEstimateData);

        (new MultiEstimatePublicLog(
            $oEstimateRequest,
            $oBizCommonInfo,
            $aBizProductMap,
            $aEstimateData,
            $aLayout,
            $aRecLayout,
            $aRenderInfo['data']['filter_info']
        ))->multiWritePublicLog();
    }

    /**
     * @param array $aResponseInfo Array structure to count the elements of.
     * @return void
     * @property _printErrNoAndErrMsg _printErrNoAndErrMsg
     * @Author:<EMAIL>
     */
    private function _printErrNoAndErrMsg($aResponseInfo) {
        Log::updateLogHeadData(
            array(
                'errno'  => $aResponseInfo['errno'],
                'errmsg' => $aResponseInfo['errmsg'],
            )
        );
    }

    /**
     * @param BizCommonInfo $oBizCommonInfo $oBizCommonInfo
     * @param BizProduct[]  $aBizProducts   $aBizProducts
     * @return void
     */
    private function _writeMultiReqKafkaNew(BizCommonInfo $oBizCommonInfo, $aBizProducts) {
        if (count($aBizProducts) <= 0) {
            return;
        }

        $oTopic     = new PassengerMultiEstimateReqTopic();
        $aBatchData = [
            'estimate_trace_id'         => Trace::traceId(),
            'area'                      => $oBizCommonInfo->oAreaInfo->iArea,
            'passenger_phone'           => $oBizCommonInfo->oPassengerInfo->sPhone,
            'passenger_id'              => $oBizCommonInfo->oPassengerInfo->iPid,
            'county'                    => $oBizCommonInfo->oAreaInfo->iFromCounty,
            'district'                  => $oBizCommonInfo->oAreaInfo->iDistrict,
            'client_type'               => $oBizCommonInfo->oCommonInfo->iClientType,
            'from_poi_id'               => $oBizCommonInfo->oAreaInfo->sFromPoiId,
            'starting_lng'              => $oBizCommonInfo->oAreaInfo->fFromLng,
            'starting_lat'              => $oBizCommonInfo->oAreaInfo->fFromLat,
            'current_lat'               => $oBizCommonInfo->oAreaInfo->fCurLat,
            'current_lng'               => $oBizCommonInfo->oAreaInfo->fCurLng,
            'starting_name'             => $oBizCommonInfo->oAreaInfo->sStartingName,
            'to_poi_id'                 => $oBizCommonInfo->oAreaInfo->sToPoiId,
            'dest_lng'                  => $oBizCommonInfo->oAreaInfo->fToLng,
            'dest_lat'                  => $oBizCommonInfo->oAreaInfo->fToLat,
            'dest_name'                 => $oBizCommonInfo->oAreaInfo->sToName,
            'from_poi_type'             => $oBizCommonInfo->oAreaInfo->sFromPoiType,
            'to_poi_type'               => $oBizCommonInfo->oAreaInfo->sToPoiType,
            'choose_f_searchid'         => $oBizCommonInfo->oAreaInfo->sChooseFSearchid,
            'choose_t_searchid'         => $oBizCommonInfo->oAreaInfo->sChooseTSearchid,
            'stopover_points'           => json_encode($oBizCommonInfo->oAreaInfo->aStopoverPoints),
            'app_version'               => $oBizCommonInfo->oCommonInfo->sAppVersion,
            'access_key_id'             => $oBizCommonInfo->oCommonInfo->iAccessKeyID,
            'menu_id'                   => $oBizCommonInfo->oCommonInfo->sMenuId,
            'departure_time'            => date(\DateTime::RFC3339, $oBizCommonInfo->oCommonInfo->iDepartureTime),
            'map_type'                  => $oBizCommonInfo->oAreaInfo->sMapType,
            'lang'                      => $oBizCommonInfo->oCommonInfo->sLang,
            'order_type'                => $oBizCommonInfo->oCommonInfo->iOrderType,
            'page_type'                 => $oBizCommonInfo->oCommonInfo->iPageType,
            'channel'                   => $oBizCommonInfo->oCommonInfo->sChannel,
            'user_type'                 => $oBizCommonInfo->oCommonInfo->iUserType,
            '_source'                   => 'v3', //写死当前接口的来源，是新表单给athena做个标识
            'pay_type'                  => $oBizCommonInfo->oCommonInfo->iPaymentType,
            'to_city'                   => $oBizCommonInfo->oAreaInfo->iToArea,
            'call_car'                  => $oBizCommonInfo->oCommonInfo->iCallCarType,
            'tab_id'                    => $oBizCommonInfo->oCommonInfo->sTabId,
            'athena_rpc_trigger_switch' => TriggerBubbleAthena::getAthenaTriggerSwitch($oBizCommonInfo),
        ];

        $iHasDualPriceCarpoolV3 = $iHasIntercityCarpool = 0;
        $aProducts = [];

        foreach ($aBizProducts as $oBizProduct) {
            $oProduct = $oBizProduct->oProduct;

            $aData       = [
                'estimate_id'           => $oProduct->oOrderInfo->sEstimateID,
                'product_id'            => $oProduct->oOrderInfo->iProductId,
                'business_id'           => $oProduct->oOrderInfo->iBusinessId,
                'require_level'         => $oProduct->oOrderInfo->iRequireLevel,
                'combo_type'            => $oProduct->oOrderInfo->iComboType,
                'product_category'      => $oProduct->oOrderInfo->iProductCategory,
                'is_special_price'      => $oProduct->oOrderInfo->iIsSpecialPrice,
                'level_type'            => $oProduct->oOrderInfo->iLevelType,
                'carpool_type'          => $oProduct->oOrderInfo->iCarpoolType,
                'carpool_price_type'    => $oProduct->oOrderInfo->iCarpoolPriceType,
                'is_dual_carpool_price' => $oProduct->oOrderInfo->bIsDualCarpoolPrice,
                'departure_time'        => date(\DateTime::RFC3339, $oProduct->oOrderInfo->iDepartureTime),
                'member_level'          => $oProduct->aMemberInfo['level_id'] ?? 0,
            ];
            $aProducts[] = $aData;

            if (Horae::isCarpoolUnSuccessFlatPrice($aData)) {
                $iHasDualPriceCarpoolV3 = 1;
            }

            if (Horae::isInterCityCarpool($aData)) {
                $iHasIntercityCarpool = 1;
            }
        }

        $aBatchData['has_dual_price_carpool'] = $iHasDualPriceCarpoolV3;
        $aBatchData['has_intercity_carpool']  = $iHasIntercityCarpool;

        $aBatchData['product_list'] = $aProducts;
        $oTopic->sync($aBatchData);
    }

    /**
     * @param BizCommonInfo $oBizCommonInfo $oBizCommonInfo
     * @param BizProduct[]  $aBizProductMap $aBizProductMap
     * @return \Dirpc\SDK\PreSale\CategoryData[]
     */
    public function buildCategoryInfo($oBizCommonInfo, $aBizProductMap) {
        $categoryInfo = CategoryInfoLogic::getInstance($oBizCommonInfo, $aBizProductMap)->getCategoryInfo();
        if (!empty($categoryInfo)) {
            FeatureRecord::getInstance()->RecordFeature(FeatureRecord::FEATURE_CATEGORY_INFO);
        }

        return $categoryInfo ?? null;
    }

    /**
     *  组装地图路线请求的参数--用于地图落盘同起终点的包含路线
     * @param int   $iUid        uid
     * @param array $aOrderInfo  orderInfo
     * @param array $aCommonInfo commonInfo
     * @return string
     */
    private function _formatMapRequest($iUid, $aOrderInfo, $aCommonInfo) {
        $aMapRequest = [
            'uid'              => (string)$iUid,
            'origin'           => $aOrderInfo['from_lng'].','.$aOrderInfo['from_lat'],
            'origin_name'      => $aOrderInfo['from_name'],
            'origin_id'        => $aOrderInfo['from_poi_id'],
            'destination'      => $aOrderInfo['to_lng'].','.$aOrderInfo['to_lat'],
            'destination_name' => $aOrderInfo['to_name'],
            'destination_id'   => $aOrderInfo['to_poi_id'],
            'city'             => (string)$aOrderInfo['area'],
            'destination_city' => (string)$aOrderInfo['to_area'],
            'departure_time'   => (string)$aOrderInfo['departure_time'],
            'app_version'      => (string)$aCommonInfo['app_version'],
            'search_version'   => '1',
            //以下参数是地图分配的
            'caller_id'        => 'passenger_estimate',
            'acc_key'          => 'JS4eXtMCsEpSB2s0f5hrW67r',
            'product_id'       => '111',
            'g_poly'           => '1',
            'transit_mode'     => '2',
        ];

        $d = '';
        switch ($aOrderInfo['access_key_id']) {
            case BizLib\Constants\Common::DIDI_IOS_PASSENGER_APP:
                $d = '1';
                break;
            case BizLib\Constants\Common::DIDI_ANDROID_PASSENGER_APP:
                $d = '2';
                break;
            case BizLib\Constants\Common::DIDI_WECHAT_MINI_PROGRAM:
                // no break
            case BizLib\Constants\Common::DIDI_ALIPAY_MINI_PROGRAM:
                $d = '3';
                break;
            default:
                break;
        }

        $aMapRequest['d'] = $d;
        return json_encode($aMapRequest);
    }


    /**
     * @param BizCommonInfo       $oBizCommonInfo $oBizCommonInfo
     * @param BizProduct          $oBizProduct    $oBizProduct
     * @param NewFormEstimateData $oEstimateData  表单渲染数据
     * @return array
     */
    private function _writeKafkaForMultiNew($oBizCommonInfo, $oBizProduct, $oEstimateData) {
        $oPassengerInfo = $oBizCommonInfo->oPassengerInfo;
        $oProduct       = $oBizProduct->oProduct;
        $oAreaInfo      = $oBizCommonInfo->oAreaInfo;
        $oCommonInfo    = $oBizCommonInfo->oCommonInfo;
        $oPriceInfo     = $oBizProduct->oPriceInfo;
        $oBillInfo      = $oBizProduct->getBillInfo();
        if (empty($oPassengerInfo) || empty($oProduct->oOrderInfo) || empty($oBillInfo)) {
            return [];
        }

        $aPassengerKafka = array();
        $aPassengerKafka['estimate_trace_id'] = Trace::traceId();
        $aPassengerKafka['passenger_phone']   = $oPassengerInfo->sPhone;
        $aPassengerKafka['passenger_id']      = $oPassengerInfo->iPid;
        $aPassengerKafka['district']          = $oAreaInfo->iDistrict;
        $aPassengerKafka['area']         = $oAreaInfo->iArea;
        $aPassengerKafka['channel']      = $oCommonInfo->sChannel;
        $aPassengerKafka['starting_lng'] = $oAreaInfo->fFromLng;
        $aPassengerKafka['starting_lat'] = $oAreaInfo->fFromLat;
        $aPassengerKafka['county']       = (string)$oAreaInfo->iFromCounty;
        $aPassengerKafka['dest_lng']     = $oAreaInfo->fToLng;
        $aPassengerKafka['dest_lat']     = $oAreaInfo->fToLat;
        $aPassengerKafka['to_city']      = $oAreaInfo->iToArea;
        $aPassengerKafka['from_poi_id']  = $oAreaInfo->sFromPoiId;
        $aPassengerKafka['to_poi_id']    = $oAreaInfo->sToPoiId;
        $aPassengerKafka['create_time']  = time();
        $aPassengerKafka['product_id']   = $oProduct->oOrderInfo->iProductId;
        $aPassengerKafka['scene_type']   = $oProduct->oOrderInfo->iSceneType;
        $aPassengerKafka['car_type']     = $oProduct->oOrderInfo->iRequireLevel;
        $aPassengerKafka['multi_require_product'] = '';
        $aPassengerKafka['preference_product']    = '';
        $aPassengerKafka['is_anycar']      = 0;
        $aPassengerKafka['n_tuple']        = json_encode($oProduct->oOrderInfo->toArray(), JSON_UNESCAPED_UNICODE);
        $aPassengerKafka['call_car']       = $oProduct->oOrderInfo->iCallCarType;
        $aPassengerKafka['current_lng']    = (float)($oAreaInfo->fCurLng);
        $aPassengerKafka['current_lat']    = (float)($oAreaInfo->fCurLat);
        $aPassengerKafka['client_type']    = (int)$oCommonInfo->iClientType;
        $aPassengerKafka['platform_type']  = (int)$oCommonInfo->iPlatformType;
        $aPassengerKafka['starting_name']  = (string)($oAreaInfo->sStartingName);
        $aPassengerKafka['dest_name']      = (string)($oAreaInfo->sDestName);
        $aPassengerKafka['departure_time'] = $oCommonInfo->iDepartureTime;
        $aPassengerKafka['is_fast_car']    = (int)(Utils\Product::isFastcar($oProduct->oOrderInfo->iProductId));
        $aPassengerKafka['oType']          = $oProduct->oOrderInfo->iOType;
        $aPassengerKafka['app_version']    = $oCommonInfo->sAppVersion;
        $aPassengerKafka['origin_id']      = $oCommonInfo->iOriginId;
        $aPassengerKafka['menu_id']        = $oCommonInfo->sMenuId;
        $aPassengerKafka['bubble_id']      = $oProduct->oOrderInfo->sEstimateID;
        $aPassengerKafka['estimate_id']    = $oProduct->oOrderInfo->sEstimateID;
        $aPassengerKafka['combo_type']     = (int)($oProduct->oOrderInfo->iComboType);
        $aPassengerKafka['estimate_distance_metre'] = $oBillInfo->getDriverMetre();
        $aPassengerKafka['estimate_time_minutes']   = $oBillInfo->getDriverMinute();
        // $aPassengerKafka['basic_total_fee']         = $oBillInfo->getTotalFee();
        $aPassengerKafka['dynamic_total_fee']  = $oBillInfo->getDynamicTotalFee();
        $aPassengerKafka['basic_total_fee']    = $oBillInfo->getBasicTotalFee();
        $aPassengerKafka['dynamic_diff_price'] = $oBillInfo->getDynamicDiffPrice();
        $aPassengerKafka['estimate_fee']       = $oPriceInfo->getEstimateFee();
        $aPassengerKafka['cap_price']          = $oBillInfo->getCapPrice();

        $oDiscountSet = $oPriceInfo->getDiscountSet();
        if (!empty($oDiscountSet)) {
            $oCoupon = $oDiscountSet->getCoupon();
            if (!empty($oCoupon)) {
                $aPassengerKafka['final_coupon_value'] = (float)$oCoupon->getAmount();
                $aPassengerKafka['coupon_info']        = json_encode($oCoupon);
            }
        }

        $aPassengerKafka['pay_type']     = $oBizProduct->getPaymentInfo()->getDefaultPayType();
        $aPassengerKafka['dynamic_info'] = json_encode($oBillInfo->getDynamicInfo());
        $aPassengerKafka['select_type']  = $oEstimateData->getIsSelected();

        //$aPassengerKafka['recommend_type'] = $aEstimateData['recommend_type'];
        //$aPassengerKafka['form_show_type'] = $aEstimateData['form_show_type'];
//            if (Horae::isInterCityCarpool($aInfos['order_info'])) {
//                $aPassengerKafka['map_request'] = $this->_formatMapRequest($aInfos['passenger_info']['uid'], $aInfos['order_info'], $aInfos['common_info']);
//            }
        $oRouteIdList = $oEstimateData->getRouteIdList()->jsonSerialize();
        if (is_array($oRouteIdList) && count($oRouteIdList) >= 1) {
            $aPassengerKafka['route_id'] = $oRouteIdList[0];
        }

        return $aPassengerKafka;
    }


    /**
     * function _writeMultiResKafka
     * @param BizCommonInfo         $oBizCommonInfo   $oBizCommonInfo
     * @param BizProduct[]          $aBizProductMap   $aBizProductMap
     * @param NewFormEstimateData[] $aEstimateDataMap $aEstimateDataMap
     * @return void
     */
    private function _writeMultiResKafka($oBizCommonInfo, $aBizProductMap, $aEstimateDataMap) {
        $aBatchData = [];
        foreach ($aBizProductMap as $oBizProduct) {
            $aCurEstimateData = $aEstimateDataMap[$oBizProduct->getProductCategory()];
            if (!empty($aCurEstimateData)) {
                $aData = $this->_writeKafkaForMultiNew($oBizCommonInfo, $oBizProduct, $aCurEstimateData);
                !empty($aData) && $aBatchData[] = $aData;
            }
        }

        if (!empty($aBatchData)) {
            $oTopic = new PassengerEstimateTopic();
            $oTopic->batchSync($aBatchData);
        }
    }

    /**
     * 渲染前过滤不符合要求的品类
     * @param BizProduct[]  $aBizProductMap $aBizProductMap
     * @param BizCommonInfo $oBizCommonInfo BizCommonInfo
     * @return array
     */
    public function filterProductsBeforeRender($aBizProductMap, $oBizCommonInfo) {
        // 原因 => 被移除的prod信息
        $aRemoved = [
            MainRender::REMOVED_BY_PAYMENT => [],
            MainRender::REMOVED_BY_OTHER   => [],
        ];

        $this->_filterProductsBeforeRenderByPayment($oBizCommonInfo, $aBizProductMap, $aRemoved[MainRender::REMOVED_BY_PAYMENT]);

        if (!IntercityClassifyTab::getIntercityDisplayFromAthenaSwitch($oBizCommonInfo)) {
            if (!IntercityClassifyTab::getInstance($oBizCommonInfo, $aBizProductMap)->getBShowIntercityClassifyTab($oBizCommonInfo, $aBizProductMap)) {
                unset($aBizProductMap[OrderEstimatePcId::EstimatePcIdTrainTicket]);
                unset($aBizProductMap[OrderEstimatePcId::EstimatePcIdCarpoolSFCar]);
                unset($aBizProductMap[OrderEstimatePcId::EstimatePcIdCarpoolCrossSFCar]);
            }
        }

        //如果没有快车，则车大和甄选快车不曝光。因为车大和甄选快车是以下挂到快车展示的。
        if (empty($aBizProductMap[ProductCategory::PRODUCT_CATEGORY_FAST])) {
            unset($aBizProductMap[ProductCategory::PRODUCT_CATEGORY_HANDPICKED_FAST]);
            unset($aBizProductMap[ProductCategory::PRODUCT_CATEGORY_SPACIOUS_CAR]);
        }

        // 惊喜盒子过滤
        $oSurpriseDiscountABParam = SurpriseDiscountABParam::getInstance($oBizCommonInfo);
        // 如果没命中开关 或者 没有自营惊喜特价品类，需要吧所有三方惊喜特价干掉
        if (!$oSurpriseDiscountABParam->isHitSurpriseDiscountBox() || !isset($aBizProductMap[SurpriseDiscountBoxLayout::BASE_PC_ID])) {
            $aFilterPcIdList = [];
            foreach ($aBizProductMap as $iPcID => $oBizProduct) {
                // 惊喜三方
                if (SurpriseDiscountBoxLayout::BASE_LEVEL_TYPE == $oBizProduct->getLevelType() && TripcloudProduct::isTripcloudByProductID($oBizProduct->getProductId())) {
                    $aFilterPcIdList[] = $iPcID;
                }
            }
            if (!empty($aFilterPcIdList)) {
                NuwaLog::info(sprintf('惊喜盒子未开城或自营惊喜特价为空，过滤全部三方惊喜特价|$aFilterPcIdList:%s', json_encode($aFilterPcIdList)));
                foreach ($aFilterPcIdList as $iPcId) {
                    unset($aBizProductMap[$iPcId]);
                }
            }
        }
        return [$aBizProductMap, $aRemoved];
    }

    /**
     * 渲染前 - 支付方式过滤（目前仅香港业务）
     * @param BizCommonInfo $oBizCommonInfo    BizCommonInfo
     * @param BizProduct[]  $aBizProductMap    $aBizProductMap
     * @param array         $aRemovedByPayment $aRemoved
     * @return void
     */
    private function _filterProductsBeforeRenderByPayment($oBizCommonInfo, &$aBizProductMap, &$aRemovedByPayment) {
        // 根据支付方式过滤品类
        // 目前仅香港业务需要, eg: 自营的士，商家品类
        $iUserSelectPayType = $oBizCommonInfo->oCommonInfo->iPaymentType;

        // 个人支付都支持，不需要判断车型是否支持
        if (empty($iUserSelectPayType) || $iUserSelectPayType <= 0 || Order::BUSINESS_PAY_BY_PERSON_NEW == $iUserSelectPayType) {
            return;
        }

        foreach ($aBizProductMap as $iPcID => $oBizProduct) {
            // 仅香港品类，且不是打表的士
            if (!Horae::isHongKongProduct($oBizProduct->getProductID()) || ProductCategory::PRODUCT_CATEGORY_HK_TAXI == $iPcID) {
                continue;
            }

            // @var \Dirpc\SDK\PriceApi\EstimateNewFormPaymentInfo
            $oPayInfo = $oBizProduct->oPriceInfo->getPaymentInfo();
            if (empty($oPayInfo)) {
                unset($aBizProductMap[$iPcID]);
            } elseif ($iUserSelectPayType != $oPayInfo->getDefaultPayType()) {
                // 乘客自己选则了支付方式, 结果还和默认支付方式不同, 说明不支持乘客选择的方式
                $aRemovedByPayment[$iPcID] = $oBizProduct;
                unset($aBizProductMap[$iPcID]);
            }
        }
    }

    /**
     * 渲染前过滤不符合要求的品类
     * @param BizProduct[]  $aBizProductMap $aBizProductMap
     * @param BizCommonInfo $oBizCommonInfo BizCommonInfo
     * @return array
     */
    public function filterProductsAfterPrice($aBizProductMap, $oBizCommonInfo) {

        // 没有选车费  过滤车大品类
        if (!empty($aBizProductMap[ProductCategory::PRODUCT_CATEGORY_SPACIOUS_CAR])) {
            $aBillInfo      = $aBizProductMap[ProductCategory::PRODUCT_CATEGORY_SPACIOUS_CAR]->oPriceInfo->getBillInfo();
            $aFeeDetailInfo = $aBillInfo['fee_detail_info'];
            if (!isset($aFeeDetailInfo['talos_spacious_car_selection_fee'])) {
                unset($aBizProductMap[ProductCategory::PRODUCT_CATEGORY_SPACIOUS_CAR]);
            }
        }

        // 过滤远必省品类实验
        $aBizProductMap = FarMustCheaperBoxLayout::filterFastMustCheaperBox($oBizCommonInfo, $aBizProductMap);

        // 超值达价格不一致，过滤盒子
        $aBizProductMap = ChaoZhiDaBoxLayout::filterChaoZhiDaBoxAfterPrice($oBizCommonInfo, $aBizProductMap);

        $aBizProductMap = SurpriseDiscountBoxLayout::filterAfterPrice($oBizCommonInfo, $aBizProductMap);

        //自选车价格range品类的准入
        $aBizProductMap = BargainRangeLayout::filterBargainRange($oBizCommonInfo, $aBizProductMap);

        // 城际拼车导流实验（顺风车，火车票等导流品类）
        if (!IntercityClassifyTab::getIntercityDisplayFromAthenaSwitch($oBizCommonInfo)) {
            $aBizProductMap = IntercityClassifyTab::getInstance($oBizCommonInfo, $aBizProductMap)->filterIntercity($oBizCommonInfo, $aBizProductMap);
        }

        $aBizProductMap = IntercityAnycarFilter::filterIntercityByPrice($oBizCommonInfo, $aBizProductMap);

        // 拼车导流位逻辑汇总：提取自athena,decision
        $aBizProductMap = CarpoolGuideBarChoose::getInstance($oBizCommonInfo, $aBizProductMap)->filterCarpoolGuideBar($oBizCommonInfo, $aBizProductMap);

        return $aBizProductMap;
    }

    /**
     * 通过预匹配数据过滤小巴不可用车型
     * @param BizProduct[]  $aBizProductMap $aBizProductMap
     * @param BizCommonInfo $oBizCommonInfo BizCommonInfo
     * @return array
     */
    public function filterProductsBeforeAthena($aBizProductMap, $oBizCommonInfo) {
        $aMiniProductMap    = [];
        $iRightPcID         = 0;
        $iRightLevelTypeMin = 100000;

        // 这里的智能小巴逻辑是选择最小一档的LevelType中，pcId最小的（理论上不会出现多商户重叠，做一个兜底）
        $aSmartProductMap = [];
        $iSmartPcID = 0;
        $iSmartLevelTypeMin = 100000;

        foreach ($aBizProductMap as $iPcID => $oBizProduct) {
            if (OrderNTuple::CARPOOL_TYPE_MINI_BUS == $oBizProduct->oProduct->oOrderInfo->iCarpoolType) {
                $miniBusInfo = CarpoolPreMatchLogic::getInstance()->getMiniBusInfo($oBizProduct);
                if (empty($miniBusInfo) || empty($miniBusInfo['etp_info']['etp_time_duration']) || empty($miniBusInfo['mapinfo_cache_token'])) {
                    unset($aBizProductMap[$iPcID]);
                    continue;
                }

                if (-1 == $miniBusInfo['etp_info']['etp_time_duration'] && !Apollo::getInstance()->featureToggle(
                    'gs_wait_minibus_open',
                    $oBizCommonInfo->getApolloParams(null)
                )->allow()
                ) {
                    unset($aBizProductMap[$iPcID]);
                    continue;
                }

                if (!empty($miniBusInfo)) {
                    $aMiniProductMap[$iPcID] = $oBizProduct;
                    if ($oBizProduct->getLevelType() < $iRightLevelTypeMin) {
                        $iRightLevelTypeMin = $oBizProduct->getLevelType();
                        $iRightPcID         = $iPcID;
                    }
                    continue;
                }
            }
            if (OrderNTuple::CARPOOL_TYPE_SMART_BUS == $oBizProduct->oProduct->oOrderInfo->iCarpoolType) {
                $smartBusInfo = CarpoolPreMatchLogic::getInstance()->getMiniBusInfo($oBizProduct);
                if (empty($smartBusInfo) || empty($smartBusInfo['etp_info']['etp_time_duration'])) {
                    unset($aBizProductMap[$iPcID]);
                    continue;
                }
                $aSmartProductMap[$iPcID] = $oBizProduct;
                if ($oBizProduct->getLevelType() < $iSmartLevelTypeMin) {
                    $iSmartLevelTypeMin = $oBizProduct->getLevelType();
                    $iSmartPcID = $iPcID;
                } elseif ($oBizProduct->getLevelType() == $iSmartLevelTypeMin && $iPcID < $iSmartPcID) {
                    $iSmartPcID = $iPcID;
                }
            }
        }

        // 有可用小巴品类
        if (!empty($aMiniProductMap)) {
            if (1 == sizeof($aMiniProductMap)) {
                $oBizCommonInfo->oCommonInfo->bOnlyMinibus = true;
            }

            foreach ($aMiniProductMap as $iPcID => $oBizProduct) {
                if ($iRightPcID != $iPcID) {
                    unset($aBizProductMap[$iPcID]);
                }
            }
        }

        // 有可用的智能小巴品类
        if (!empty($aSmartProductMap)) {
            foreach ($aSmartProductMap as  $iPcID => $oBizProduct) {
                if ($iSmartPcID != $iPcID) {
                    unset($aBizProductMap[$iPcID]);
                }
            }
        }

        return $aBizProductMap;
    }


    /**
     * 构建提示文案
     * @return array
     */
    private function _buildMultiRouteTipsData() {
        $aConfig = Language::getDecodedTextFromDcmp('config_text-multi_route_tips_data');
        if (empty($aConfig)) {
            return [];
        }

        foreach ($aConfig as $sType => $sText) {
            if (!empty($sText)) {
                $multiRouteTipsData[$sType] = $sText;
            }
        }

        return $multiRouteTipsData;

    }

    /**
     * 构建粤港车err跳转文案
     * @return array|ErrButtonResponse
     */
    private function _buildHKMainLandCarResponse() {
        $oRequest         = Utils\Request::getInstance();
        $oEstimateRequest = new Request();
        //让参数支持get和post请求
        $aGetParam  = $oRequest->get();
        $aPostParam = $oRequest->post(null,  false, false);
        $aGetParam  = is_array($aGetParam) ? $aGetParam : array();
        $aPostParam = is_array($aPostParam) ? $aPostParam : array();
        $oEstimateRequest->mergeFromJsonArray(array_merge($aGetParam, $aPostParam));

        $iAccessKeyId    = $oEstimateRequest->getAccessKeyId();
        $aConfig         = Language::getDecodedTextFromDcmp('errno-hk_mainland_err_info');
        $oResponse       = new ErrButtonResponse();
        $aResponseData   = new ErrButtonData();
        $aResponseButton = new ErrButton();
        $oResponse->setErrno($aConfig['errNo']);
        $oResponse->setErrmsg($aConfig['errMsg']);
        $aResponseButton->setText($aConfig['button']);
        if (1 == $iAccessKeyId || 2 == $iAccessKeyId) {
            $aResponseButton->setUrl($aConfig['NA_url']);
        }

        if (9 == $iAccessKeyId || 22 == $iAccessKeyId) {
            $aResponseButton->setUrl($aConfig['App_url']);
        }

        $aResponseData->setButton($aResponseButton);
        $oResponse->setData($aResponseData);
        return $oResponse;
    }

    /**
     * mock构建异常
     * @param BizCommonInfo $oBizCommonInfo BizCommonInfo
     * @throws ExceptionWithResp 获取价格失败抛异常
     * @return void
     */
    private function _mockExceptionReturn($oBizCommonInfo) {
        // 为端上设置mock逻辑，方便回归测试异常场景
        $oApollo       = new Apollo();
        $mock596Switch = $oApollo->featureToggle(
            'gs_mock_596_toggle',
            $oBizCommonInfo->getApolloParams(null)
        );
        $iCode         = (int) $mock596Switch->getParameter('mock_errno', 0);
        if ($mock596Switch->allow()) {
            if (EstimateDegradeCode::R_ESTIMATE_DEGRADE_PRODUCTS_FAIL == $iCode) {
                throw new ExceptionWithResp(
                    ErrCode\Code::E_DDS_GET_PRODUCTS_FAIL,
                    EstimateDegradeCode::R_ESTIMATE_DEGRADE_PRODUCTS_FAIL,
                    '',
                    ['dds_products_response' => []]
                );
            }
        }
    }

    /**
     * @return array|null
     */
    private function _buildGroupData() {

        $classifyFoldLogic  = ClassifyFoldLogic::getInstance();
        $flowBoxSubGroupMap = $classifyFoldLogic->getAFinalInBoxSubGroupIdMap();
        if (empty($flowBoxSubGroupMap)) {
            return null;
        }

        $aGroupData = [];
        foreach ($flowBoxSubGroupMap as $categoryId => $iSubGroupIdMap) {
            if (!empty($iSubGroupIdMap) && is_array($iSubGroupIdMap)) {
                foreach ($iSubGroupIdMap as $iSubGroupId => $value) {
                    $groupId = LayoutAbstract::buildGroupId(AthenaRecommend::SHORT_DISTANCE_TYPE, $iSubGroupId);
                    $aLayout = $classifyFoldLogic->getAFinalInBoxLayoutByGroupId($groupId);
                    $aGroupData[$groupId] = $aLayout['groups'][0];
                }
            }
        }

        return $aGroupData;
    }

    /**
     * 同步调用 Athena 特征计算
     * @param BizCommonInfo $oBizCommonInfo  $oBizCommonInfo
     * @param BizProduct[]  $aBizProductList $aBizProductList
     * @return void
     */
    private function callAthenaTrigger(BizCommonInfo $oBizCommonInfo, $aBizProductList) {
        // 判断 Apollo 切量开关
        if (!TriggerBubbleAthena::getAthenaTriggerSwitch($oBizCommonInfo)) {
            return;
        }
        // 调用 athena_api TriggerBubbleAthena
        $oAthenaApi = TriggerBubbleAthena::getInstance();
        $oAthenaApi->TriggerBubbleAthena($oBizCommonInfo, $aBizProductList);
    }
}
