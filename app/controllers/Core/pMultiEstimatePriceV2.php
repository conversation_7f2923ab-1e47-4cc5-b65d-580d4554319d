<?php

use B<PERSON><PERSON><PERSON><PERSON>\Constants\OrderNTuple;
use BizCommon\Models\Cache\EstimatePrice;
use Biz<PERSON>om<PERSON>\Utils\Horae;
use BizLib\Config as NuwaConfig;
use BizCommon\Constants\PrefixSuffix;
use BizLib\Client\Cache\StorageClient;
use BizLib\Constants\OrderSystem;
use BizLib\ErrCode\Code;
use BizLib\Utils;
use BizLib\Utils\CarLevel;
use BizLib\Utils\Language;
use BizLib\Utils\Product;
use Disf\SPL\Trace;
use Nebula\Exception\Route\InterRouteException;
use Dirpc\SDK\PreSale\MultiEstimatePriceRequest as Request;
use Dirpc\SDK\PreSale\MultiEstimatePriceResponse as Response;
use Nuwa\Protobuf\Internal\Message;
use PreSale\Core\Controller;
use PreSale\Infrastructure\Util\AsyncMode\AsyncContainer;
use PreSale\Infrastructure\Util\RenderElapsedTimeLogTrait;
use PreSale\Logics\estimatePriceV2\multiRequest\PriceLogic;
use PreSale\Logics\estimatePriceV2\multiRequest\ProductList;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use PreSale\Logics\estimatePriceV2\multiResponse\MultiEstimatePublicLogV2;
use PreSale\Logics\estimatePriceV2\response\SceneResponseLogicV2;
use PreSale\Models\carrera\PassengerEstimateReqTopic;
use PreSale\Models\carrera\PassengerEstimateTopic;
use PreSale\Logics\order\AnyCarOrderLogic;
use BizLib\ExceptionHandler;
use BizLib\Exception\ExceptionRespInterface;
use PreSale\Logics\estimatePriceV2\DecisionLogic;
use PreSale\Logics\estimatePriceV2\multiResponse\MainRender;
use PreSale\Logics\estimatePriceV2\params\ParamsLogic;
use PreSale\Logics\estimatePriceV2\EstimateDegradeCode;
use BizLib\ErrCode\RespCode;
use BizLib\Log;
use PreSale\Models\carrera\PassengerMultiEstimateReqTopic;
use Xiaoju\Apollo\Apollo;

/**
 * Class pMultiEstimatePriceV2.
 */
class PMultiEstimatePriceV2Controller extends Controller
{
    use RenderElapsedTimeLogTrait;

    const ESTIMATE_PARAM_CACHE_TIME = 432000; //5 hours

    private $_aUnResponse = [
        'errno'  => -666,
        'errmsg' => 'unknown',
    ];

    /**
     * @return void
     */
    public function indexAction() {
        Message::setEmitDefaults(true);
        try {
            // 参数初始化
            $oRequest         = Utils\Request::getInstance();
            $oEstimateRequest = new Request();
            $oEstimateRequest->mergeFromJsonArray($oRequest->get());
            ParamsLogic::checkDaCheAnyCarParams($oEstimateRequest);

            // 调用dds服务，获取当前城市已开服的品类（product）数据
            $oProductList = ProductList::getInstance($oEstimateRequest);
            $aProductList = $oProductList->buildProductList();

            // 写预估入参kafka(目前主要提供给地图使用)--批量上线后，下游切换，下线非批量
            $this->_writeMultiReqKafka($aProductList);
            // 写预估入参kafka 批量的 (地图/Athena/dapes均使用)
            $this->_writeMultiReqKafkaNew($aProductList);

            // 获取账单、支付方式、优惠券、乘客运营活动券数据
            $oPriceLogic        = new PriceLogic();
            $oPriceLogic->setBIsCheatingTraffic($this->bIsCheatingTraffic);
            $aResponseParamsNew = $oPriceLogic->getMultiResponse($aProductList);
            $aResponseParamsNew = $this->_processDDSResponse($aResponseParamsNew);


            // 渲染对端数据
            $oMainRender = MainRender::getInstance($aResponseParamsNew, []);
            $oMainRender->setProductList($aProductList);
            $oMainRender->buildAInfo();
            $aRenderInfo = $oMainRender->multiExecute();

            // 格式化输出
            $oResponse = new Response();
            $oResponse->mergeFromJsonArray($aRenderInfo);
            $sResponseJson = $oResponse->serializeToJsonString();
            $aResponseInfo = json_decode($sResponseJson, true);
            if (isset($aResponseInfo['errno'])) {
                if (!empty($aResponseInfo['errno'])) {
                    $this->_printErrNoAndErrMsg($aResponseInfo);
                } elseif (empty($aResponseInfo['errno']) && 0 != $aResponseInfo['errno']) {
                    $this->_printErrNoAndErrMsg($this->_aUnResponse);
                }
            } else {
                $this->_printErrNoAndErrMsg($this->_aUnResponse);
            }
        } catch (InterRouteException $e) {
            $aErrMsg   = NuwaConfig::text('errno', 'pEstimatePrice_error_msg');
            $oHandler  = ExceptionHandler::getInstance();
            $sMenuId   = $this->getRequest()->getQuery('menu_id', '');
            $sPageType = $this->getRequest()->getQuery('page_type', '');

            if (Code::E_COMMON_AREA_NOT_OPEN_SERVICE == $e->getRespCode()
                && MainDataRepo::isInterCarpoolPullPage($sMenuId, $sPageType)
            ) {
                $aResponseInfo['errno']  = $e->getRespCode();
                $aResponseInfo['errmsg'] = $e->getRespMsg();
                $aResponseInfo['data']   = $e->getRespData();
            } else {
                //handleException底层有wf日志,预期内非零错误码不要使用
                $aResponseInfo = $oHandler->handleException($e, ['err_msg' => $aErrMsg, ]);
            }

            $aMapContext  = json_decode(Language::getTextFromDcmp('config_inter_carpool-intercept_dialog'), true);
            $aInterErrMsg = $aMapContext[$e->getRespCode()] ?? [];
            $aResponseInfo['data']['intercept_dialog'] = $aInterErrMsg;
            if (RespCode::P_ESTIMATE_CARPOOL_APP_VERSION_ILLEGAL == $e->getRespCode()) {
                $aResponseInfo['errmsg'] = $aInterErrMsg['title'];
                $aResponseInfo['data']   = $aInterErrMsg;
            }

            $this->_printErrNoAndErrMsg($aResponseInfo);

            $this->sendJson($aResponseInfo, (string)($this->getRequest()->getQuery('callback', false)));
            return;
        } catch (Exception $e) {
            $aErrMsg       = NuwaConfig::text('errno', 'pEstimatePrice_error_msg');
            $oHandler      = ExceptionHandler::getInstance();
            $aResponseInfo = $oHandler->handleException($e, ['err_msg' => $aErrMsg, ]);
            $oResponse     = new Response();
            $oResponse->mergeFromJsonArray($aResponseInfo);
            if ($e instanceof ExceptionRespInterface
                // && RespCode::R_ESTIMATE_DOWNSTREAM_FAIL_DEGRADE == $e->getRespCode()
                && EstimateDegradeCode::isDegradeCode($e->getRespCode())
            ) {
                header('HTTP/1.1 596 limit access', true, 596);
            }

            $this->_printErrNoAndErrMsg($aResponseInfo);

            $this->sendJson($oResponse, (string)($this->getRequest()->getQuery('callback', false)));
            return;
        }

        $this->_writeParamsCache((array) $this->getRequest()->getQuery(null, false), $aResponseInfo);

        $this->sendJson($sResponseJson, (string)($this->getRequest()->getQuery('callback', false)));
        fastcgi_finish_request();

        $aResponseInfo = $this->_spreadResponse($aResponseInfo);

        SceneResponseLogicV2::getInstance()->sendCommuteCardForFree($aResponseParamsNew, $aResponseInfo);

        $this->_writeMultiResKafka($aResponseParamsNew, $aResponseInfo);

        (new MultiEstimatePublicLogV2($oEstimateRequest, $aResponseParamsNew, $aResponseInfo, $aProductList))->multiWritePublicLog();
        AsyncContainer::run();
    }


    /**
     * @param array $aResponseInfo Array structure to count the elements of.
     * @return void
     * @property _printErrNoAndErrMsg _printErrNoAndErrMsg
     * @Author:<EMAIL>
     */
    private function _printErrNoAndErrMsg($aResponseInfo) {
        Log::updateLogHeadData(
            array(
                'errno'  => $aResponseInfo['errno'],
                'errmsg' => $aResponseInfo['errmsg'],
            )
        );
    }

    /**
     * @param array $aProductList $aProductList
     * @return void
     */
    private function _writeMultiReqKafka(array $aProductList) {
        $oTopic     = new PassengerEstimateReqTopic();
        $aBatchData = [];

        $oProduct = $aProductList[0];
        if (!Apollo::getInstance()->featureToggle(
            'kafka_switch_wanliu_passenger_multi_req',
            [
                'pid'   => $oProduct->oPassengerInfo->iPid,
                'phone' => $oProduct->oPassengerInfo->sPhone,
                'city'  => $oProduct->oAreaInfo->iArea,
            ]
        )->allow()
        ) {
            return;
        }

        // @var \PreSale\Logics\estimatePrice\multiRequest\Product $oProduct
        foreach ($aProductList as $oProduct) {
            $aData        = [
                'estimate_id'           => $oProduct->oOrderInfo->sEstimateID,
                'area'                  => $oProduct->oAreaInfo->iArea,
                'passenger_phone'       => $oProduct->oPassengerInfo->sPhone,
                'passenger_id'          => $oProduct->oPassengerInfo->iPid,
                'require_level'         => $oProduct->oOrderInfo->iRequireLevel,
                'county'                => $oProduct->oAreaInfo->iFromCounty,
                'district'              => $oProduct->oAreaInfo->iDistrict,
                'product_id'            => $oProduct->oOrderInfo->iProductId,
                'client_type'           => $oProduct->oCommonInfo->iClientType,
                'from_poi_id'           => $oProduct->oAreaInfo->sFromPoiId,
                'starting_lng'          => $oProduct->oAreaInfo->fFromLng,
                'starting_lat'          => $oProduct->oAreaInfo->fFromLat,
                'starting_name'         => $oProduct->oAreaInfo->sStartingName,
                'to_poi_id'             => $oProduct->oAreaInfo->sToPoiId,
                'dest_lng'              => $oProduct->oAreaInfo->fToLng,
                'dest_lat'              => $oProduct->oAreaInfo->fToLat,
                'dest_name'             => $oProduct->oAreaInfo->sToName,
                'from_poi_type'         => $oProduct->oAreaInfo->sFromPoiType,
                'to_poi_type'           => $oProduct->oAreaInfo->sToPoiType,
                'stopover_points'       => json_encode($oProduct->oOrderInfo->aStopoverPoints),
                'app_version'           => $oProduct->oCommonInfo->sAppVersion,
                'access_key_id'         => $oProduct->oCommonInfo->iAccessKeyID,
                'menu_id'               => $oProduct->oOrderInfo->sMenuID,

                'combo_type'            => $oProduct->oOrderInfo->iComboType,
                'carpool_type'          => $oProduct->oOrderInfo->iCarpoolType,
                'carpool_price_type'    => $oProduct->oOrderInfo->iCarpoolPriceType,
                'is_dual_carpool_price' => $oProduct->oOrderInfo->bIsDualCarpoolPrice,
                'departure_time'        => date(\DateTime::RFC3339, $oProduct->oOrderInfo->iDepartureTime),
            ];
            $aBatchData[] = $aData;
        }

        $oTopic->batchSync($aBatchData);
    }

    /**
     * @param \PreSale\Logics\estimatePrice\multiRequest\Product[] $aProductList 品类列表
     * @return void
     */
    private function _writeMultiReqKafkaNew(array $aProductList) {
        if (count($aProductList) <= 0) {
            return;
        }

        $oProduct = $aProductList[0];
        $oTopic   = new PassengerMultiEstimateReqTopic();

        $aBatchData = [
            'estimate_trace_id' => Trace::traceId(),
            'area'              => $oProduct->oAreaInfo->iArea,
            'passenger_phone'   => $oProduct->oPassengerInfo->sPhone,
            'passenger_id'      => $oProduct->oPassengerInfo->iPid,
            'county'            => $oProduct->oAreaInfo->iFromCounty,
            'district'          => $oProduct->oAreaInfo->iDistrict,
            'client_type'       => $oProduct->oCommonInfo->iClientType,
            'map_type'          => $oProduct->oAreaInfo->sMapType,
            'from_poi_id'       => $oProduct->oAreaInfo->sFromPoiId,
            'starting_lng'      => $oProduct->oAreaInfo->fFromLng,
            'starting_lat'      => $oProduct->oAreaInfo->fFromLat,
            'starting_name'     => $oProduct->oAreaInfo->sStartingName,
            'to_poi_id'         => $oProduct->oAreaInfo->sToPoiId,
            'dest_lng'          => $oProduct->oAreaInfo->fToLng,
            'dest_lat'          => $oProduct->oAreaInfo->fToLat,
            'dest_name'         => $oProduct->oAreaInfo->sToName,
            'from_poi_type'     => $oProduct->oAreaInfo->sFromPoiType,
            'to_poi_type'       => $oProduct->oAreaInfo->sToPoiType,
            'stopover_points'   => json_encode($oProduct->oOrderInfo->aStopoverPoints),
            'app_version'       => $oProduct->oCommonInfo->sAppVersion,
            'access_key_id'     => $oProduct->oCommonInfo->iAccessKeyID,
            'menu_id'           => $oProduct->oOrderInfo->sMenuID,
            'departure_time'    => date(\DateTime::RFC3339, $oProduct->oOrderInfo->iDepartureTime),
            'lang'              => $oProduct->oCommonInfo->sLang,
            'order_type'        => $oProduct->oOrderInfo->iOrderType,
            'page_type'         => $oProduct->oCommonInfo->iPageType,
            'channel'           => $oProduct->oCommonInfo->sChannel,
            'pay_type'          => $oProduct->oOrderInfo->sPaymentsType,
            'to_city'           => $oProduct->oAreaInfo->iToArea,
            'call_car'          => $oProduct->oOrderInfo->iCallCarType,
        ];

        $iHasDualPriceCarpoolV3 = $iHasIntercityCarpool = 0;
        $iMemberLevel = 0;
        if (!empty($oProduct->oPassengerInfo->aPassengerInfo['member_profile'])) {
            $iMemberLevel = $oProduct->oPassengerInfo->aPassengerInfo['member_profile']['level_id'] ?? 0;
        }

        $aProducts = [];
        // @var \PreSale\Logics\estimatePrice\multiRequest\Product $oProduct
        foreach ($aProductList as $oProduct) {
            $aData       = [
                'estimate_id'           => $oProduct->oOrderInfo->sEstimateID,
                'product_id'            => $oProduct->oOrderInfo->iProductId,
                'business_id'           => $oProduct->oOrderInfo->iBusinessId,
                'require_level'         => $oProduct->oOrderInfo->iRequireLevel,
                'combo_type'            => $oProduct->oOrderInfo->iComboType,
                'product_category'      => $oProduct->oOrderInfo->iProductCategory,
                'is_special_price'      => $oProduct->oOrderInfo->iIsSpecialPrice,
                'level_type'            => $oProduct->oOrderInfo->iLevelType,
                'carpool_type'          => $oProduct->oOrderInfo->iCarpoolType,
                'carpool_price_type'    => $oProduct->oOrderInfo->iCarpoolPriceType,
                'is_dual_carpool_price' => $oProduct->oOrderInfo->bIsDualCarpoolPrice,
                'departure_time'        => date(\DateTime::RFC3339, $oProduct->oOrderInfo->iDepartureTime),
                'route_type'            => $oProduct->oOrderInfo->iRouteType,
                'member_level'          => $iMemberLevel,
            ];
            $aProducts[] = $aData;

            if (Horae::isCarpoolUnSuccessFlatPrice($aData)) {
                $iHasDualPriceCarpoolV3 = 1;
            }

            if (Horae::isInterCityCarpool($aData)) {
                $iHasIntercityCarpool = 1;
            }
        }

        $aBatchData['has_dual_price_carpool'] = $iHasDualPriceCarpoolV3;
        $aBatchData['has_intercity_carpool']  = $iHasIntercityCarpool;

        $aBatchData['product_list'] = $aProducts;
        $oTopic->sync($aBatchData);
    }


    /**
     *  组装地图路线请求的参数--用于地图落盘同起终点的包含路线
     * @param int   $iUid        uid
     * @param array $aOrderInfo  orderInfo
     * @param array $aCommonInfo commonInfo
     * @return string
     */
    private function _formatMapRequest($iUid, $aOrderInfo, $aCommonInfo) {
        $aMapRequest = [
            'uid'              => (string)$iUid,
            'origin'           => $aOrderInfo['from_lng'].','.$aOrderInfo['from_lat'],
            'origin_name'      => $aOrderInfo['from_name'],
            'origin_id'        => $aOrderInfo['from_poi_id'],
            'destination'      => $aOrderInfo['to_lng'].','.$aOrderInfo['to_lat'],
            'destination_name' => $aOrderInfo['to_name'],
            'destination_id'   => $aOrderInfo['to_poi_id'],
            'city'             => (string)$aOrderInfo['area'],
            'destination_city' => (string)$aOrderInfo['to_area'],
            'departure_time'   => (string)$aOrderInfo['departure_time'],
            'app_version'      => (string)$aCommonInfo['app_version'],
            'search_version'   => '1',
            //以下参数是地图分配的
            'caller_id'        => 'passenger_estimate',
            'acc_key'          => 'JS4eXtMCsEpSB2s0f5hrW67r',
            'product_id'       => '111',
            'g_poly'           => '1',
            'transit_mode'     => '2',
        ];

        $d = '';
        switch ($aOrderInfo['access_key_id']) {
            case BizLib\Constants\Common::DIDI_IOS_PASSENGER_APP:
                $d = '1';
                break;
            case BizLib\Constants\Common::DIDI_ANDROID_PASSENGER_APP:
                $d = '2';
                break;
            case BizLib\Constants\Common::DIDI_WECHAT_MINI_PROGRAM:
                // no break
            case BizLib\Constants\Common::DIDI_ALIPAY_MINI_PROGRAM:
                $d = '3';
                break;
            default:
                break;
        }

        $aMapRequest['d'] = $d;
        return json_encode($aMapRequest);
    }


    /**
     * @param array $aInfos        aInfos
     * @param array $aEstimateData aEstimateData
     * @return array
     */
    private function _writeKafkaForMultiNew(array $aInfos, array $aEstimateData) {
        if (!empty($aInfos['passenger_info']['pid'])) {
            $sCarLevel       = $aInfos['order_info']['require_level'];
            $aProductInfo    = $aInfos['bill_info']['product_infos'][$sCarLevel];
            $aBillInfo       = $aInfos['bill_info']['bills'][$sCarLevel];
            $aPassengerKafka = array();
            $aPassengerKafka['passenger_phone'] = $aInfos['passenger_info']['phone'];
            $aPassengerKafka['passenger_id']    = $aInfos['passenger_info']['pid'];
            $aPassengerKafka['district']        = $aInfos['order_info']['district'];
            $aPassengerKafka['area']            = $aInfos['order_info']['area'];
            $aPassengerKafka['to_city']         = $aInfos['order_info']['to_area'];
            $aPassengerKafka['channel']         = $aInfos['order_info']['channel'];
            $aPassengerKafka['starting_lng']    = $aInfos['order_info']['from_lng'];
            $aPassengerKafka['starting_lat']    = $aInfos['order_info']['from_lat'];
            $aPassengerKafka['county']          = (string)($aInfos['order_info']['county']);
            $aPassengerKafka['dest_lng']        = $aInfos['order_info']['to_lng'];
            $aPassengerKafka['dest_lat']        = $aInfos['order_info']['to_lat'];
            $aPassengerKafka['from_poi_id']     = $aInfos['order_info']['from_poi_id'];
            $aPassengerKafka['to_poi_id']       = $aInfos['order_info']['to_poi_id'];
            $aPassengerKafka['create_time']     = time();
            $aPassengerKafka['product_id']      = $aInfos['order_info']['product_id'];
            $aPassengerKafka['scene_type']      = $aInfos['order_info']['scene_type'];
            $aPassengerKafka['car_type']        = $sCarLevel;
            $aPassengerKafka['multi_require_product'] = '';
            $aPassengerKafka['preference_product']    = '';
            $aPassengerKafka['is_anycar']      = 0;
            $aPassengerKafka['n_tuple']        = json_encode($aInfos['order_info']['n_tuple'], JSON_UNESCAPED_UNICODE);
            $aPassengerKafka['call_car']       = $aInfos['order_info']['call_car_type'];
            $aPassengerKafka['current_lng']    = (float)($this->getRequest()->getQuery('lng', false));
            $aPassengerKafka['current_lat']    = (float)($this->getRequest()->getQuery('lat', false));
            $aPassengerKafka['client_type']    = (int)$this->getRequest()->getQuery('client_type', false);
            $aPassengerKafka['platform_type']  = (int)$this->getRequest()->getQuery('platform_type', false);
            $aPassengerKafka['starting_name']  = (string)($aInfos['order_info']['starting_name']);
            $aPassengerKafka['dest_name']      = (string)($aInfos['order_info']['dest_name']);
            $aPassengerKafka['departure_time'] = $aInfos['order_info']['departure_time'];
            $aPassengerKafka['is_fast_car']    = (int)($aInfos['order_info']['is_fast_car']);
            $aPassengerKafka['oType']          = $aInfos['order_info']['oType'];

            $aPassengerKafka['app_version'] = $aInfos['common_info']['app_version'];
            $aPassengerKafka['origin_id']   = $aInfos['common_info']['origin_id'];
            $aPassengerKafka['menu_id']     = $aInfos['order_info']['menu_id'];

            if (!isset($aInfos['bill_info'], $aInfos['bill_info']['bills'], $aInfos['bill_info']['bills'][$sCarLevel])) {
                return [];
            }

            $aBill = $aInfos['bill_info'];
            $aPassengerKafka['bubble_id']   = $aBill['estimate_id'];
            $aPassengerKafka['estimate_id'] = $aBill['estimate_id'];
            $aPassengerKafka['combo_type']  = (int)($aProductInfo['combo_type']);
            //拼车
            if (Utils\Horae::isCarpool($aInfos['order_info']['combo_type'], $aInfos['order_info']['require_level'])) {
                if (empty($aInfos['bill_info']['is_carpool_open'])) {
                    return [];
                }

                $aPassengerKafka['combo_type'] = (int)($aProductInfo['combo_type'] ?? \BizLib\Constants\Horae::TYPE_COMBO_CARPOOL);
            } elseif (Product::isAnyCar($aInfos['order_info']['product_id'])) {
                $aPassengerKafka['require_car_level']     = '0';
                $aPassengerKafka['multi_require_product'] = AnyCarOrderLogic::getInstance()->buildMultiForPublicLogKafka($aInfos['multi_require_product']);
                $aPassengerKafka['preference_product']    = AnyCarOrderLogic::getInstance()->buildPreferenceForPublicLogKafka($aInfos['multi_require_product'], $aInfos['order_info']['channel']);
                $aPassengerKafka['is_anycar'] = 1;
            }

            $aPassengerKafka['estimate_distance_metre'] = $aInfos['bill_info']['driver_metre'];
            $aPassengerKafka['estimate_time_minutes']   = $aInfos['bill_info']['driver_minute'];
            $aPassengerKafka['basic_total_fee']         = $aInfos['bill_info']['bills'][$sCarLevel]['basic_total_fee'];
            $aPassengerKafka['dynamic_total_fee']       = $aBillInfo['dynamic_total_fee'];
            $aPassengerKafka['dynamic_diff_price']      = $aBillInfo['dynamic_diff_price'];
            $aPassengerKafka['estimate_fee']            = $aInfos['activity_info'][0]['estimate_fee'];
            $aPassengerKafka['cap_price']          = $aBillInfo['cap_price'];
            $aPassengerKafka['final_coupon_value'] = $aInfos['activity_info'][0]['final_coupon_value'];
            $aPassengerKafka['pay_type']           = $this->_getSelectedPayType($aInfos['payments_info']['user_pay_info']['busi_payments'] ?? []);

            $aDynamicInfo = $aBillInfo['dynamic_info'];
            unset($aDynamicInfo['dynamic_price_id']);
            $aPassengerKafka['dynamic_info'] = json_encode($aDynamicInfo);

            $aPassengerKafka['select_type']    = $aEstimateData['select_type'];
            $aPassengerKafka['recommend_type'] = $aEstimateData['recommend_type'];
            $aPassengerKafka['form_show_type'] = $aEstimateData['form_show_type'];

            if (Horae::isInterCityCarpool($aInfos['order_info'])) {
                $aPassengerKafka['map_request'] = $this->_formatMapRequest($aInfos['passenger_info']['uid'], $aInfos['order_info'], $aInfos['common_info']);
            }

            $aPassengerKafka['estimate_trace_id'] = Trace::traceId();

            return $aPassengerKafka;
        }

        return [];
    }

    /**
     * @param array $aPayments aPayments
     * @return int
     */
    private function _getSelectedPayType($aPayments) {
        foreach ($aPayments as $aPayment) {
            if (!empty($aPayment['isSelected'])) {
                return $aPayment['tag'];
            }
        }

        return 0;
    }

    /**
     * @param array $aParams       $aParams
     * @param array $aResponseInfo $aResponseInfo
     * @return void
     */
    private function _writeParamsCache($aParams, $aResponseInfo) {
        if (OrderSystem::TYPE_ORDER_BOOKING === (int)($aParams['order_type'])) {
            return;
        }

        if (empty($aResponseInfo['data']['estimate_data']) || !is_array($aResponseInfo['data']['estimate_data'])) {
            return;
        }

        $aAllowKey = [
            implode('_', [Product::PRODUCT_ID_FAST_CAR, CarLevel::DIDI_PUTONG_CAR_LEVEL, 0]),
            implode('_', [Product::PRODUCT_ID_FAST_CAR, CarLevel::DIDI_YOUXIANG_CAR_LEVEL, 0]),
            implode('_', [OrderNTuple::PRODUCT_ID_ANY_CAR, CarLevel::DIDI_ANY_CAR_CAR_LEVEL, 0]),
            AnyCarOrderLogic::PRODUCT_KEY_CARPOOL, //拼车
            AnyCarOrderLogic::PRODUCT_KEY_KUAICHE_FLAT_RATE, //快车区域一口价303
            AnyCarOrderLogic::PRODUCT_KEY_KUAICHE_FACTOR_FLAT_RATE, //快车多因素一口价309
            AnyCarOrderLogic::PRODUCT_KEY_YOUXIANG_FACTOR_FLAT_RATE, //优享多因素一口价309
        ];

        foreach ($aResponseInfo['data']['estimate_data'] as $aItem) {
            if (empty($aItem['estimate_id'])) {
                continue;
            }

            $iProductId = Product::getProductIdByBusinessId($aItem['business_id']);
            $sGroupKey  = implode('_', [$iProductId, $aItem['require_level'], $aItem['combo_type']]);
            if (in_array($sGroupKey, $aAllowKey) || Utils\Horae::isMultiFactorFlatRateV2($aItem)) {
                $sKey = PrefixSuffix::S_PASSENGER_ESTIMATE_PARAM.$aItem['estimate_id'];
                StorageClient::getInstance(StorageClient::STORE)->setex($sKey, self::ESTIMATE_PARAM_CACHE_TIME, json_encode($aParams));
            }
        }

        //预估eid之间关联起来
        $this->_setLinkEIDCache($aResponseInfo);

        // 记录：预估车型的展现。暂时存储在cache，后续迁移到报价单Dise
        // 2020-12-21: 目前看有两处使用
        //             1. 拼车通勤卡读取 @see pre-sale/app/logics/carpool/CarpoolCommuteCard.php#L291
        //             2. transaction/app/Infrastructure/Repository/OrderExtensionRepository.php#L499
        $aEstimateShowProducts = [];
        foreach ($aResponseInfo['data']['estimate_data'] as $aItem) {
            $aEstimateShowProducts[] = [
                'estimate_id'   => $aItem['estimate_id'],
                'business_id'   => $aItem['business_id'],
                'require_level' => $aItem['require_level'],
                'combo_type'    => $aItem['combo_type'],
                'fee_msg'       => $aItem['fee_msg'],
                'combo_ids'     => isset($aItem['combo_id']) ? (string)($aItem['combo_id']) : '',
            ];
        }

        $sEstimateTraceId = $aResponseInfo['data']['estimate_trace_id'];
        EstimatePrice::getInstance()->setCacheEstimateShowProductList($sEstimateTraceId, $aEstimateShowProducts);
    }

    /**
     * @param array $aResponseInfo $aResponseInfo
     * @return void
     */
    private function _setLinkEIDCache($aResponseInfo) {
        //预估eid之间关联起来，且缓存下来
        $aEIDLinkMap = [
            Utils\ProductCategory::PRODUCT_CATEGORY_CARPOOL_STATION => Utils\ProductCategory::PRODUCT_CATEGORY_FAST,
            Utils\ProductCategory::PRODUCT_CATEGORY_UNIONE_CARPOOL  => Utils\ProductCategory::PRODUCT_CATEGORY_UNIONE,
        ];

        $aLinkKey   = [];
        $aLinkValue = [];
        foreach ($aResponseInfo['data']['estimate_data'] as $aItem) {
            if (in_array($aItem['product_category'], array_keys($aEIDLinkMap))) {
                $aLinkKey[$aItem['product_category']] = $aItem['estimate_id'];
            }

            if (in_array($aItem['product_category'], array_values($aEIDLinkMap))) {
                $aLinkValue[$aItem['product_category']] = $aItem['estimate_id'];
            }
        }

        if (!empty($aLinkKey) && !empty($aLinkValue)) {
            foreach ($aEIDLinkMap as $key => $value) {
                if (in_array($key, array_keys($aLinkKey)) && in_array($value, array_keys($aLinkValue))) {
                    BizCommon\Models\Cache\EstimatePrice::getInstance()->setEstimateLink($aLinkKey[$key], $aLinkValue[$value]);
                }
            }
        }
    }

    /**
     * 处理dds的返回，本来dds/decision的返回只用来展示，但是anycar的is_select从dds过来，
     * 目前代码里还是使用aInfo里的is_select，临时根据dds/decision的结果，修改aInfo里的数据
     * @param array $aInfos 计价数据
     * @return array 修改后的数据
     */
    private function _processDDSResponse($aInfos) {
        DecisionLogic::getInstance()->setDecisionParams($aInfos);
        DecisionLogic::getInstance()->executeDecisionV2();
        $aInfos = DecisionLogic::getInstance()->filerProducts();
        DecisionLogic::getInstance()->buildGroupConf();
        return $aInfos;
    }

    /**
     * 将返回结果平铺展开，用于记录日志，和后续处理
     * @param array $response 返回结果
     * @return array
     */
    private function _spreadResponse($response) {
        $estiamteData = [];
        foreach ($response['data']['estimate_data'] as $value) {
            if (empty($value['sub_product'])) {
                $estiamteData[] = $value;
            } else {
                foreach ($value['sub_product'] as $subValue) {
                    $estiamteData[] = $subValue;
                }
            }
        }

        $response['data']['estimate_data'] = $estiamteData;

        return $response;

    }

    /**
     * function _writeMultiResKafka
     * @param array $aResponseParamsNew new info
     * @param array $aResponseInfo      info
     * @return void
     */
    private function _writeMultiResKafka($aResponseParamsNew, $aResponseInfo) {
        $aBatchData = [];
        foreach ($aResponseParamsNew as $aInfosNew) {
            $aCurEstimateData = [];
            foreach ($aResponseInfo['data']['estimate_data'] as $aData) {
                if ($aData['estimate_id'] == $aInfosNew['order_info']['estimate_id']) {
                    $aCurEstimateData = $aData;
                }
            }

            if (!empty($aCurEstimateData)) {
                $aData = $this->_writeKafkaForMultiNew($aInfosNew, $aCurEstimateData);
                !empty($aData) && $aBatchData[] = $aData;
            }
        }

        if (!empty($aBatchData)) {
            $oTopic = new PassengerEstimateTopic();
            $oTopic->batchSync($aBatchData);
        }
    }
}
