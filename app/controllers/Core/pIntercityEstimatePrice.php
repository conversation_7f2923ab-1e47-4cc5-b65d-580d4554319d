<?php

use B<PERSON><PERSON>ib\Log as NuwaLog;
use Nebula\Exception\Route\InterRouteException;
use PreSale\Core\Controller;
use BizLib\Config as NuwaConfig;
use BizLib\ExceptionHandler;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\RespCode;
use Dirpc\SDK\PreSale\IntercityEstimatePriceRequest as Request;
use Dirpc\SDK\PreSale\IntercityEstimatePriceResponse as Response;
use PreSale\Logics\intercityEstimate\asyncLogic\AsyncHandler;
use PreSale\Logics\intercityEstimate\multiRequest\Decision;
use PreSale\Logics\intercityEstimatePrice\multiRequest\PriceLogic;
use PreSale\Logics\intercityEstimatePrice\multiRequest\ProductList;
use PreSale\Logics\intercityEstimatePrice\multiResponse\MainRender;
use PreSale\Logics\estimatePrice\multiResponse\MainDataRepo;
use BizLib\Utils\Language;
use BizLib\ErrCode\Msg;

/**
 * 城际拼车（远程特价、远程特快）场景页预估接口
 * http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=587870906
 * Class PIntercityEstimatePriceController
 */
class PIntercityEstimatePriceController extends Controller
{
    /**
     * 接口Handler
     * @return void
     */
    public function indexAction() {
        try {
            // 参数初始化
            $oEstimateRequest = new Request();
            $oEstimateRequest->mergeFromJsonArray(\BizLib\Utils\Request::getInstance()->get());

            // 构建请求数据
            $oProductList = ProductList::getInstance($oEstimateRequest)->preBuild();
            $aProductList = $oProductList->buildProductList();
            $oCommonInfo = $oProductList->buildCommonInfo();

            // 获取价格信息
            $aInfos = (new PriceLogic(...$aProductList))->getMultiResponse();

            // Decision过滤
            Decision::getInstance()->initDecision($aInfos);
            $aInfos = Decision::getInstance()->filterAInfo($aInfos);

            // 渲染Response
            $oMainRender = MainRender::getInstance($aInfos, $oCommonInfo, $oEstimateRequest, ...$aProductList)->buildAInfo();
            $aRenderInfo = $oMainRender->multiExecute();

            // 格式化数据
            $oRsp = new Response();
            $oRsp->mergeFromJsonArray($aRenderInfo);
            $aRspInfo = json_decode($oRsp->serializeToJsonString(),true);
            $this->sendJson($aRspInfo, (string)$this->getRequest()->getQuery('callback',false));

            fastcgi_finish_request();

            $oAsync = new AsyncHandler($oEstimateRequest,$aInfos,$aRenderInfo,...$aProductList);
            $oAsync->multiWritePublicLog();
            $oAsync->batchWriteKafka();
        } catch (InterRouteException $e) {
            $sMenuId   = $this->getRequest()->getQuery('menu_id', '');
            $sPageType = $this->getRequest()->getQuery('page_type', '');
            if (Code::E_COMMON_AREA_NOT_OPEN_SERVICE == $e->getRespCode() && MainDataRepo::isInterCarpoolPullPage($sMenuId, $sPageType)) {
                $sJumpUrl = Language::getTextFromDcmp('config_inter_carpool-area_not_open_h5_url');
                $sJumpUrl = empty($sJumpUrl) ? 'https://v.didi.cn/k5aDxAY' : $sJumpUrl;
                $sErrMsg  = Language::getTextFromDcmp('config_inter_carpool-area_not_open_errmsg');
                $sErrMsg  = empty($sErrMsg) ? Msg::get(Code::E_COMMON_AREA_NOT_OPEN_SERVICE) : $sErrMsg;

                if ((MainDataRepo::isMiniAppClient(MainDataRepo::getAccessKeyId()) && version_compare(MainDataRepo::getAppVersion(), '6.2.17') >= 0)
                    || (MainDataRepo::isNativeClient(MainDataRepo::getAccessKeyId()) && version_compare(MainDataRepo::getAppVersion(), '6.2.18') >= 0)
                ) {
                    $sH5Url     = Language::getTextFromDcmp('carpool_intercity-area_not_open_guide_url');
                    $sAppletUrl = Language::getTextFromDcmp('carpool_intercity-area_not_open_guide_applet_url');
                    if (MainDataRepo::isNativeClient(MainDataRepo::getAccessKeyId()) && !empty($sH5Url)) {
                        $sJumpUrl = $sH5Url;
                    } elseif (MainDataRepo::isMiniAppClient(MainDataRepo::getAccessKeyId()) && !empty($sAppletUrl)) {
                        $sJumpUrl = $sAppletUrl;
                    }

                    $sMsg = Language::getTextFromDcmp('carpool_intercity-area_not_open_estimate_msg');
                    if (!empty($sMsg)) {
                        $sErrMsg = $sMsg;
                    }
                }

                $aResponseInfo['errno']  = $e->getRespCode();
                $aResponseInfo['errmsg'] = $sErrMsg;
                $aResponseInfo['data']   = ['error_url' => $sJumpUrl];
            } else {
                // handleException底层有wf日志 预期内非零错误码不要使用
                $aErrMsg       = NuwaConfig::text('errno', 'pEstimatePrice_error_msg');
                $aResponseInfo = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $aErrMsg, ]);
            }

            $aMapContext  = json_decode(Language::getTextFromDcmp('config_inter_carpool-intercept_dialog'), true);
            $aInterErrMsg = $aMapContext[$e->getRespCode()] ?? [];
            $aResponseInfo['data']['intercept_dialog'] = $aInterErrMsg;
            if (RespCode::P_ESTIMATE_CARPOOL_APP_VERSION_ILLEGAL == $e->getRespCode()) {
                $aResponseInfo['errmsg'] = $aInterErrMsg['title'];
                $aResponseInfo['data']   = $aInterErrMsg;
            }

            $this->sendJson($aResponseInfo, (string)($this->getRequest()->getQuery('callback', false)));
        } catch (Exception $e) {
            // TODO 删代码
            $aErrMsg       = NuwaConfig::text('errno', 'pEstimatePrice_error_msg');
            $aResponseInfo = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $aErrMsg, ]);
            $oResponse     = new Response();
            $oResponse->mergeFromJsonArray($aResponseInfo);
            $this->sendJson($oResponse, (string)($this->getRequest()->getQuery('callback', false)));
        }
    }
}
