<?php
/**
 * 根据乘客输入出发地、目的地同时豪华车车型以及司务员进行批量预估和价格展示
 * 目前调用端:滴滴乘客端、微信滴滴出行webApp入口、支付宝滴滴出行webApp入口、Uber中国乘客端、企业级网页和企业级乘客端、OpenApi.
 *
 * <AUTHOR> <<EMAIL>>
 * @date: 20/2/24
 */


use BizLib\Config as NuwaConfig;
use BizLib\ExceptionHandler;
use BizLib\Utils;
use Dirpc\SDK\PreSale\LuxMultiEstimatePriceRequest as Request;
use Dirpc\SDK\PreSale\LuxMultiEstimatePriceResponse as Response;
use PreSale\Core\Controller;
use PreSale\Logics\luxEstimatePrice\multiRequest\PriceLogic;
use PreSale\Logics\luxEstimatePrice\multiRequest\ProductList;
use PreSale\Logics\luxEstimatePrice\multiRequest\UserPreferInfoLogic;
use PreSale\Logics\luxEstimatePrice\multiResponse\MainRender;
use PreSale\Logics\luxEstimatePrice\multiResponse\MultiEstimatePublicLogV2;

/**
 * Class pLuxMultiEstimatePrice.
 */
class PLuxMultiEstimatePriceController extends Controller
{
    /**
     * @return void
     */
    public function indexAction() {
        \Nuwa\Protobuf\Internal\Message::setEmitDefaults(true);
        try {
            //参数初始化
            $oRequest         = Utils\Request::getInstance();
            $oEstimateRequest = new Request();
            $oEstimateRequest->mergeFromJsonArray($oRequest->get());

            //构建请求数据
            $oProductList = new ProductList($oEstimateRequest);
            $aProductList = $oProductList->buildProductList();

            //获取账单、支付方式、优惠券、乘客运营活动券数据
            $oPriceLogic        = new PriceLogic();
            $aResponseParamsNew = $oPriceLogic->getMultiResponse($aProductList);

            //获取用户偏好设置选项
            $oUserPreferLogic = new UserPreferInfoLogic();
            $aUserPreferInfo  = $oUserPreferLogic->getUserPreferResponse($aProductList);

            //渲染对端数据
            $aRenderInfo = MainRender::getInstance($aResponseParamsNew, $aUserPreferInfo)->multiExecute();

            $aResponseInfo = $aRenderInfo;
            //格式化输出
            $oResponse = new Response();
            $oResponse->mergeFromJsonArray($aRenderInfo);
            $aResponseInfo = $oResponse->serializeToJsonArray();
        } catch (Exception $e) {
            $aErrMsg       = NuwaConfig::text('errno', 'pEstimatePrice_error_msg');
            $oHandler      = ExceptionHandler::getInstance();
            $aResponseInfo = $oHandler->handleException($e, ['err_msg' => $aErrMsg,]);
            $this->sendJson($aResponseInfo, (string)($this->getRequest()->getQuery('callback', false)));
            return;
        }

        $this->sendJson($aResponseInfo, (string)($this->getRequest()->getQuery('callback', false)));
        fastcgi_finish_request();

        (new MultiEstimatePublicLogV2($aResponseParamsNew, $aResponseInfo, $aProductList))->multiWritePublicLog();
    }
}
