<?php

if (!function_exists('fastcgi_finish_request')) {
    function fastcgi_finish_request() {}
}

use BizLib\Config as NuwaConfig;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Common as UtilsCommon;

class PNewOrderController extends \PreSale\Core\Controller
{
    public function indexAction() {
        if ($this->_forward2Refactoring()) {
            return;
        }

        $aErrMsgConf = NuwaConfig::text('errno', 'pNewOrder_error_msg');
        $aError      = UtilsCommon::getErrMsg(1026, $aErrMsgConf);
        $this->output($aError);
    }

    private function _forward2Refactoring() {
        $sAlias   = 'disf!biz-gs-transaction';
        $sNewPath = '/gulfstream/transaction/v1/'.basename(__DIR__).'/pNewOrder/index';
        try {
            $aResp = \BizLib\Utils\Curl2InRouter::executeByAlias($sAlias, $sNewPath);
            // errno == 0 表示无错误：如果 http code 不为 200，则 errno 赋值为 http code，所以这里使用 errno 来判断
            if (0 == $aResp['errno']) {
                json_decode($aResp['ret'], true);
                $iJsonError = json_last_error();
                if (JSON_ERROR_NONE === $iJsonError) {
                    echo $aResp['ret'];
                    return true;
                }
            }
        } catch (Exception $e) {
            NuwaLog::warning('PRE-SALE_SPLIT '.basename(__DIR__).'/'.__CLASS__.'/'.__FUNCTION__.' step4 response error|error no: ' . $e->getCode() . '|error msg: ' . $e->getMessage());
        }

        NuwaLog::warning(sprintf('PRE-SALE_SPLIT '.basename(__DIR__).'/'.__CLASS__.'/'.__FUNCTION__.' step5 fail||resp errno=%s||resp errmsg=%s||response=%s', $aResp['errno'], $aResp['errmsg'], $aResp['ret']));

        return false;
        //--end 拆分导流
    }
}
