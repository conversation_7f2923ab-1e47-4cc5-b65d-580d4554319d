<?php

use BizCommon\Models\Order\OrderStopoverPoints;
use BizLib\Config as NuwaConfig;
use BizLib\Log;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\MapHelper;
use BizLib\Utils\PublicLog;
use BizLib\Log as NuwaLog;
use BizLib\Exception\ExceptionRespInterface;
use Disf\SPL\Trace;
/*
 * 根据乘客输入出发地、目的地同时对拼车、非拼车和优享进行批量预估和价格展示
 * 目前调用端:滴滴乘客端、微信滴滴出行webApp入口、支付宝滴滴出行webApp入口、Uber中国乘客端、企业级网页和企业级乘客端、OpenApi.
 *
 * <AUTHOR>
 * @date: 17/09/16
 */

use BizCommon\Constants\PrefixSuffix;
use BizLib\Client\Cache\StorageClient;
use BizLib\Utils;
use BizLib\Utils\Product;
use PreSale\Logics\estimatePrice\multiResponse\MainHelper;
use PreSale\Logics\estimatePrice\multiResponse\MainRender;
use PreSale\Logics\estimatePrice\multiResponse\MultiEstimatePublicLog;
use PreSale\Logics\estimatePrice\ParamsLogic;
use PreSale\Logics\estimatePrice\params\SceneParamsLogic;
use PreSale\Logics\estimatePrice\response\ResponseLogic;
use PreSale\Logics\estimatePrice\response\SceneResponseLogic;
use PreSale\Logics\estimatePrice\response\SceneResponseLogicV2;
use PreSale\Logics\estimatePrice\response\PretreatmentLogic;
use PreSale\Logics\estimatePrice\PriceLogicV2;
use PreSale\Logics\estimatePrice\PriceLogic;
use PreSale\Logics\estimatePrice\EstimateLogic;
use PreSale\Models\carrera\PassengerEstimateReqTopic;
use PreSale\Models\carrera\PassengerEstimateTopic;
use PreSale\Logics\order\AnyCarOrderLogic;
use PreSale\Logics\estimatePrice\bill\CommonBillLogic;
use PreSale\Logics\carpool\CarpoolCommuteCard;
use BizLib\ExceptionHandler;
use BizLib\ErrCode;
use BizLib\Utils\Language;
use Nebula\Exception\Route\InterRouteException;
use BizCommon\Models\Order\Order;
use BizCommon\Models\Order\OrderStation;
//use PreSale\Domain\Model\UIComponent\Hook\Hook as ComponentHook;
use PreSale\Logics\estimatePrice\DecisionLogic;
use BizCommon\Utils\Common as BizCommons;
use PreSale\Logics\estimatePrice\multiResponse\MainDataRepo;
use BizLib\ErrCode\Msg;

/**
 * Class pMultiEstimatePrice.
 */
class PMultiEstimatePriceController extends \PreSale\Core\Controller
{
    const ESTIMATE_PARAM_CACHE_TIME = 432000; //5 hours

    private $_aUnResponse = [
        'errno'  => -666,
        'errmsg' => 'unknown',
    ];
    /**
     * @var bool
     */
    private $_bEstimateDegrade = false;

    private $_aResponseExtraInfo = [];

    public function indexAction() {
        $aOneConfData = array();

        try {
            //参数处理实例
            $oParams = ParamsLogic::getInstance();
            //获取端上传的最初的参数
            $aOriginParams = (array) $this->getRequest()->getQuery(null, false, true);
            $this->_fixOriginParam($aOriginParams);
            $oParams->setRequest($aOriginParams);
            $oParams->oldVersionIntercept($aOriginParams);
            list($aEstimatePriceParams, $aOneConfData) = $oParams->getMultiBillParams($aOriginParams);
            $aPassengerInfo = current($aEstimatePriceParams)['passenger_info'];

            $this->_writeMultiReqKafka($aEstimatePriceParams, $aOriginParams);

            AnyCarOrderLogic::getInstance()->reSortAnycarProductRank((int)($aOriginParams['from_area']), $aPassengerInfo['pid'], $aPassengerInfo['phone']);

            $aResponseParamsNew = PriceLogicV2::getInstance($aEstimatePriceParams)->getMultiResponse();

            $aResponseParamsNew = $this->_processDDSResponse($aResponseParamsNew);

            //渲染对端数据
            $aResponseInfo = MainRender::getInstance($aResponseParamsNew, $aOneConfData)->multiExecute();
            if (isset($aResponseInfo['errno'])) {
                if (!empty($aResponseInfo['errno'])) {
                    $this->_printErrNoAndErrMsg($aResponseInfo);
                } elseif (empty($aResponseInfo['errno']) && 0 != $aResponseInfo['errno']) {
                    $this->_printErrNoAndErrMsg($this->_aUnResponse);
                }
            } else {
                $this->_printErrNoAndErrMsg($this->_aUnResponse);
            }
        } catch (InterRouteException $e) {
            $aErrMsg       = NuwaConfig::text('errno', 'pEstimatePrice_error_msg');
            $oHandler      = ExceptionHandler::getInstance();
            $aResponseInfo = $oHandler->handleException($e, ['err_msg' => $aErrMsg, ]);
            if (\BizCommon\Utils\Horae::isLowPriceCarpoolEntry($this->getRequest()->getQuery('menu_id', false))) {
                $aLowPriceErrMsg = $this->_mapLowPriceCarpoolErrMsg($e);
                $aRespData       = $e->getRespData();
                $aPassengerInfo  = [];
                if (!empty($aRespData)&&isset($aRespData['passenger_info'])) {
                        $aPassengerInfo = $aRespData['passenger_info'];
                    unset($aRespData['passenger_info']);
                }

                $aResponseInfo['data'] = empty($aRespData) ? $aLowPriceErrMsg : $aRespData;
                // 没有命中线路也要打印便于显示
                if (!empty($aOriginParams)) {
                    $this->_weiteLowPriceFailPublicLog($aOriginParams,$e,$aPassengerInfo);
                }

                $sJumpUrl = '';
                if (version_compare(MainDataRepo::getAppVersion(), '6.2.16') >= 0) {
                    $sH5Url     = Language::getTextFromDcmp('config_carpool-area_not_open_guide_url');
                    $sAppletUrl = Language::getTextFromDcmp('config_carpool-area_not_open_guide_applet_url');
                    if (!empty($sH5Url) && !MainDataRepo::isMiniAppClient(MainDataRepo::getAccessKeyId())) {
                        $sJumpUrl = $sH5Url;
                    } elseif (MainDataRepo::isMiniAppClient(MainDataRepo::getAccessKeyId()) && !empty($sAppletUrl)) {
                        $sJumpUrl = $sAppletUrl;
                    }
                }

                if (!empty($sJumpUrl)) {
                    $aResponseInfo['data'] = ['error_url' => $sJumpUrl];
                }

                $sMsg = Language::getTextFromDcmp('config_carpool-area_not_open_estimate_msg');
                if (!empty($sMsg)) {
                    $aResponseInfo['errmsg'] = $sMsg;
                }
            } elseif (\BizLib\Constants\Horae::HORAE_SCENE_TYPE_CARPOOL_INTER_CITY == intval($this->getRequest()->getQuery('scene_type', false))) {
                $aInterErrMsg = $this->_mapInterErrMsg($e);
                if (\BizLib\ErrCode\RespCode::P_ESTIMATE_CARPOOL_APP_VERSION_ILLEGAL == $e->getRespCode()) {
                    $aResponseInfo['data'] = $aInterErrMsg;
                } else {
                    $aResponseInfo['data']['intercept_dialog'] = $aInterErrMsg;
                }
            } elseif (\BizLib\Constants\Common::MENU_CARPOOL == $this->getRequest()->getQuery('menu_id', false) && ErrCode\Code::E_COMMON_AREA_NOT_OPEN_SERVICE == $e->getCode()) {
                $aResponseInfo['data'] = $e->getRespData();
            }

            $this->_printErrNoAndErrMsg($aResponseInfo);

            $this->sendJson($aResponseInfo, (string)($this->getRequest()->getQuery('callback', false)));

            return;
        } catch (\Exception $e) {
            // 5.2.18之后，快车增加强显逻辑
            if (\BizLib\Utils\Product::COMMON_PRODUCT_ID_FAST_CAR == intval($this->getRequest()->getQuery('business_id', false)) && version_compare((string)($this->getRequest()->getQuery('app_version', false)), '5.2.18') >= 0 && ErrCode\Code::E_PRICE_REQUEST_FAIL == $e->getCode()) {
                    $aEstimateData = array();
                    $aEstimateData = $this->_setStrongDisplay($aEstimateData, $aOneConfData, (array) $this->getRequest()->getQuery(null, false, false));
                if (!empty($aEstimateData)) {
                    $aResponseInfo = UtilsCommon::getErrMsg(GLOBAL_SUCCESS);
                    $aResponseInfo['data']['estimate_data'] = $aEstimateData;
                } else {
                    $aErrMsg       = NuwaConfig::text('errno', 'pEstimatePrice_error_msg');
                    $oHandler      = ExceptionHandler::getInstance();
                    $aResponseInfo = $oHandler->handleException($e, ['err_msg' => $aErrMsg, ]);
                }
            } else {
                $aErrMsg       = NuwaConfig::text('errno', 'pEstimatePrice_error_msg');
                $oHandler      = ExceptionHandler::getInstance();
                $aResponseInfo = $oHandler->handleException($e, ['err_msg' => $aErrMsg, ]);
            }

            $this->_printErrNoAndErrMsg($aResponseInfo);

            $this->sendJson($aResponseInfo, (string)($this->getRequest()->getQuery('callback', false)));

            return;
        }

        $this->_writeParamsCache((array) $this->getRequest()->getQuery(null, false), $aResponseInfo);

        $this->sendJson($aResponseInfo, (string)($this->getRequest()->getQuery('callback', false)));
        fastcgi_finish_request();

        SceneResponseLogicV2::getInstance()->sendCommuteCardForFree($aResponseParamsNew, $aResponseInfo);

        $this->_writeMultiResKafka($aResponseParamsNew, $aResponseInfo);

        (new MultiEstimatePublicLog($aOriginParams, $aResponseParamsNew, $aResponseInfo))->multiWritePublicLog();
        \PreSale\Infrastructure\Util\AsyncMode\AsyncContainer::run();

    }

    /**
     * @param array $aResponseInfo Array structure to count the elements of.
     * @return void
     * @property _printErrNoAndErrMsg _printErrNoAndErrMsg
     * @Author:<EMAIL>
     */
    private function _printErrNoAndErrMsg($aResponseInfo) {
        NuwaLog::updateLogHeadData(
            array(
                'errno'  => $aResponseInfo['errno'],
                'errmsg' => $aResponseInfo['errmsg'],
            )
        );
    }

    /**
     * @Desc:
     * @param mixed[] $aInfos Array structure to count the elements of.
     * @return bool
     * @property _shouldRemove $_shouldRemove
     * @Author:<EMAIL>
     */
    private function _shouldRemove($aInfos) {
        //去掉remove_flag=true 的项，否则影响日志
        if (\BizCommon\Utils\Horae::isLowPriceCarpoolEntry($aInfos['order_info']['menu_id'])) {
            if (!\BizCommon\Utils\Horae::isLowPriceCarpool($aInfos['order_info'])) {
                return true;
            }
        }

        if (!\BizCommon\Utils\Horae::isLowPriceCarpoolEntry($aInfos['order_info']['menu_id'])) {
            if (\BizCommon\Utils\Horae::isLowPriceCarpool($aInfos['order_info'])) {
                return true;
            }
        }

        //去除anycar顶导下的城际拼车日志
        if (\BizCommon\Utils\Horae::bInterCarpool($aInfos['order_info']) && \BizLib\Constants\Common::MENU_ANYCAR == $aInfos['order_info']['menu_id']) {
            return true;
        }

        return false;
    }

    /**
     * @Desc:
     * @param mixed[] $aOriginParams  Array structure to count the elements of.
     * @param mixed[] $e              Array structure to count the elements of.
     * @param mixed[] $aPassengerInfo Array structure to count the elements of.
     * @return void
     * @property _weiteLowPriceFailPublicLog $_weiteLowPriceFailPublicLog
     * @Author:<EMAIL>
     */
    private function _weiteLowPriceFailPublicLog($aOriginParams, $e, $aPassengerInfo) {
        $aEstimateStatistic = [
            'opera_stat_key'    => 'g_order_prefee_show_not_open',
            'area'              => $aOriginParams['city_id'],
            'business_id'       => $aOriginParams['business_id'],
            'from_area'         => $aOriginParams['from_area'],
            'order_type'        => $aOriginParams['order_type'],
            'data_type'         => $aOriginParams['data_type'],
            'require_car_level' => $aOriginParams['require_level'],
            'require_level'     => $aOriginParams['require_level'],
            'flng'              => $aOriginParams['from_lng'],
            'flat'              => $aOriginParams['from_lat'],
            'tlng'              => $aOriginParams['to_lng'],
            'tlat'              => $aOriginParams['to_lat'],
            'menu_id'           => $aOriginParams['menu_id'],
            'not_open_errcode'  => $e->getRespCode(),
            'pid'               => !empty($aPassengerInfo)&&!empty($aPassengerInfo['pid']) ? $aPassengerInfo['pid'] : 0,
            'phone'             => !empty($aPassengerInfo)&&!empty($aPassengerInfo['phone']) ? $aPassengerInfo['phone'] : 0,
            'dviceid'           => $aOriginParams['dviceid'],
            'imei'              => $aOriginParams['imei'],
            'channel'           => $aOriginParams['channel'],

        ];
        PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);
        return;
    }
    /*
     * 设置强显车型
     */
    private function _setStrongDisplay($aEstimateData, $aOneConfData, $aParams) {
        $aAreaInfo = MapHelper::getAreaInfoByLoc((float)($aParams['from_lng']), (float)($aParams['from_lat']));
        if (!empty($aAreaInfo)) {
            $iArea = (int)($aAreaInfo['id']);
        } else {
            $iArea = 0;
        }

        $aEstimateDataOfStrongDisplay = $aEstimateData;
        foreach ($aOneConfData as $aOneConf) {
            if (!isset($aOneConf['strong_display']) || 0 == $aOneConf['strong_display']) {
                continue;
            }

            $isFind = false;
            foreach ($aEstimateData as $aData) {
                if ($aData['business_id'] == $aOneConf['business_id']
                    && $aData['require_level'] == $aOneConf['require_level']
                    && $aData['combo_type'] == $aOneConf['combo_type']
                ) {
                    $isFind = true;
                    break;
                }
            }

            if (!$isFind) {
                $strongDisplayData = array();
                $strongDisplayData['error_status']  = 1;
                $strongDisplayData['business_id']   = $aOneConf['business_id'];
                $strongDisplayData['require_level'] = $aOneConf['require_level'];
                $strongDisplayData['combo_type']    = $aOneConf['combo_type'];
                $strongDisplayData['scene_type']    = (int)(BizLib\Utils\Horae::getSceneType((int)($aOneConf['combo_type'])));
                if (isset($aOneConf['gray_icon'])) {
                    $strongDisplayData['gray_icon'] = $aOneConf['gray_icon'];
                }

                if (isset($aOneConf['light_icon'])) {
                    $strongDisplayData['light_icon'] = $aOneConf['light_icon'];
                }

                if (isset($aOneConf['map_icon'])) {
                    $strongDisplayData['map_icon'] = $aOneConf['map_icon'];
                }

                $strongDisplayData['fee_msg']   = NuwaConfig::text('config_text', 'estimate_fail_text');
                $strongDisplayData['intro_msg'] = $this->_setStrongDisplayIntroMsg($strongDisplayData, $iArea);
                if (empty($strongDisplayData['intro_msg']) && !empty($aOneConf['name'])) { // 若没有找到相应文案，则直接拿oneconf文案兜底
                    $strongDisplayData['intro_msg'] = $aOneConf['name'];
                }

                $aEstimateDataOfStrongDisplay[] = $strongDisplayData;
            }
        }

        return $aEstimateDataOfStrongDisplay;
    }

    /*
     * 设置强显车型的intro_msg
     */
    private function _setStrongDisplayIntroMsg($strongDisplayData, $iArea) {
        if (\BizLib\Utils\Product::isAnyCar(\BizLib\Utils\Product::getProductIdByBusinessId($strongDisplayData['business_id']))) {
            $aAnyCarText = NuwaConfig::text('config_text', 'anycar');

            return $aAnyCarText['intro_msg'];
        } else {
            $aConfig       = NuwaConfig::text('config_text', 'getEstimateFeeInfo');
            $aEstimateText = NuwaConfig::text('config_text', 'mOrderEstimateNew');
            $aOneConf      = ParamsLogic::getInstance()->getOneConf();
            if (\BizLib\Utils\CarLevel::DIDI_PUTONG_CAR_LEVEL == $strongDisplayData['require_level'] && !empty($aOneConf)) {
                if (\BizLib\Constants\Horae::TYPE_COMBO_CARPOOL == $strongDisplayData['combo_type'] || \BizLib\Utils\Horae::isInterCityCarPoolScene($strongDisplayData['combo_type'])) {
                    return $aEstimateText['car_pool_desc'];
                } elseif (\BizCommon\Utils\Order::isSpecialRateV2($strongDisplayData)) {
                    return Language::getTextFromDcmp('special_rate-product_title', []);
                } else {
                    return $aConfig['express_title'];
                }
            }

            if (\BizLib\Utils\CarLevel::DIDI_XIAOBA_CAR_LEVEL == $strongDisplayData['require_level']) {
                return $aEstimateText['carpool_xiaoba_title'];
            }

            //专车认证
            $iComboType = $strongDisplayData['combo_type'] ?? 0;
            $iSceneType = $strongDisplayData['scene_type'] ?? 0;
            if (0 == $iComboType && 0 == $iSceneType && (\BizLib\Utils\CarLevel::DIDI_SHUSHI_CAR_LEVEL == $strongDisplayData['require_level']
                || \BizLib\Utils\CarLevel::DIDI_HAOHUA_CAR_LEVEL == $strongDisplayData['require_level']
                || \BizLib\Utils\CarLevel::DIDI_SHANGWU_CAR_LEVEL == $strongDisplayData['require_level'])
            ) {
                $aNoSeeArea = NuwaConfig::config('common', 'no_show_zhuanche_desc_city');
                if (!in_array($iArea, $aNoSeeArea) && empty($aOneConf)) {
                    return $aConfig['zhuanche_desc'];
                }
            }

            if (\BizLib\Utils\CarLevel::DIDI_YOUXIANG_CAR_LEVEL == $strongDisplayData['require_level']) {
                if (isset($aOneConf) && !empty($aOneConf)) {
                    return $aConfig['multi_youxiang_desc'];
                } else {
                    return $aConfig['youxiang_desc'];
                }
            }

            if (\BizLib\Constants\OrderSystem::PRODUCT_ID_UBER_FAST_CAR == \BizLib\Utils\Product::getProductIdByBusinessId($strongDisplayData['business_id'])) {
                return $aEstimateText['uber_intro_des1'].' '.$aEstimateText['uber_cut_down_msg1'];
            }

            return '';
        }
    }

    /**
     * @Desc:
     * @param InterRouteException $e mixed[] Array structure to count the elements of.
     * @return bool
     * @property _mapLowPriceCarpoolErrMsg $_mapLowPriceCarpoolErrMsg
     * @Author:<EMAIL>
     */
    private function _mapLowPriceCarpoolErrMsg(InterRouteException $e) {
         $iErrno     = $e->getRespCode();
        $aMapContext = json_decode(Language::getTextFromDcmp('config_carpool-low_price_carpool_not_open_response',['-']), true);
        return $aMapContext[$iErrno] ?? [];
    }
    /**
     * @param InterRouteException $e Exception
     * @return array
     */
    private function _mapInterErrMsg(InterRouteException $e) {

        $iErrno      = $e->getRespCode();
        $aMapContext = json_decode(Language::getTextFromDcmp('config_inter_carpool-intercept_dialog'), true);
        return $aMapContext[$iErrno] ?? [];
    }

    /**
     * @param array $aEstimateParamm aEstimateParamm
     * @return void
     */
    private function _writeMultiReqKafka(array $aEstimateParamm, array $aOriginParams) {
        $oTopic     = new PassengerEstimateReqTopic();
        $aBatchData = [];

        //format途经点数据
        $aStopoverPoints = array();
        if (!empty($aOriginParams['stopover_points'])) {
            $aPointsInfo     = json_decode($aOriginParams['stopover_points'], true);
            $aPointsInfo     = OrderStopoverPoints::getPointsInfoFromRequest($aPointsInfo);
            $aStopoverPoints = $aPointsInfo;
        }

        foreach ($aEstimateParamm as $aItem) {
            $aData = [
                'estimate_id'        => $aItem['order_info']['estimate_id'],
                'area'               => $aItem['order_info']['area'],
                'passenger_phone'    => $aItem['passenger_info']['phone'],
                'passenger_id'       => $aItem['passenger_info']['pid'],
                'require_level'      => $aItem['order_info']['require_level'],
                'county'             => $aItem['order_info']['county'],
                'district'           => $aItem['order_info']['district'],
                'product_id'         => $aItem['order_info']['product_id'],
                'client_type'        => $aItem['common_info']['client_type'],
                'from_poi_id'        => $aItem['order_info']['from_poi_id'],
                'starting_lng'       => $aItem['order_info']['from_lng'],
                'starting_lat'       => $aItem['order_info']['from_lat'],
                'starting_name'      => $aItem['order_info']['starting_name'],
                'to_poi_id'          => $aItem['order_info']['to_poi_id'],
                'dest_lng'           => $aItem['order_info']['to_lng'],
                'dest_lat'           => $aItem['order_info']['to_lat'],
                'dest_name'          => $aItem['order_info']['dest_name'],
                'from_poi_type'      => $aOriginParams['from_poi_type'] ?? '',
                'to_poi_type'        => $aOriginParams['to_poi_type'] ?? '',
                'stopover_points'    => json_encode($aStopoverPoints),
                'app_version'        => $aItem['common_info']['app_version'],
                'access_key_id'      => $aItem['common_info']['access_key_id'],
                'menu_id'            => $aItem['order_info']['menu_id'] ?: '',
                'carpool_type'       => $aItem['order_info']['carpool_type'],
                'carpool_price_type' => $aItem['order_info']['carpool_price_type'],
                'combo_type'         => $aItem['order_info']['combo_type'],
                'departure_time'     => date(\DateTime::RFC3339, $aItem['order_info']['departure_time']),
                'channel'           =>  $aItem['order_info']['channel'],
                'order_type'        =>  $aItem['order_info']['order_type'],
                'pay_type'          =>  $aItem['order_info']['payments_type'],
                'business_id'       =>  $aItem['order_info']['business_id'],
            ];
            // $oTopic->sync($aData);
            $aBatchData[] = $aData;
        }

        $oTopic->batchSync($aBatchData);
    }


    /**
     * @param array $aInfos        aInfos
     * @param array $aResponseInfo aResponseInfo
     * @return array
     */
    private function _writeKafkaForMultiNew(array $aInfos, array $aResponseInfo) {
        if (!empty($aInfos['passenger_info']['pid'])) {
            $sCarLevel       = $aInfos['order_info']['require_level'];
            $aProductInfo    = $aInfos['bill_info']['product_infos'][$sCarLevel];
            $aBillInfo       = $aInfos['bill_info']['bills'][$sCarLevel];
            $aPassengerKafka = array();
            $aPassengerKafka['passenger_phone'] = $aInfos['passenger_info']['phone'];
            $aPassengerKafka['passenger_id']    = $aInfos['passenger_info']['pid'];
            $aPassengerKafka['district']        = $aInfos['order_info']['district'];
            $aPassengerKafka['area']            = $aInfos['order_info']['area'];
            $aPassengerKafka['channel']         = $aInfos['order_info']['channel'];
            $aPassengerKafka['starting_lng']    = $aInfos['order_info']['from_lng'];
            $aPassengerKafka['starting_lat']    = $aInfos['order_info']['from_lat'];
            $aPassengerKafka['county']          = (string)($aInfos['order_info']['county']);
            $aPassengerKafka['dest_lng']        = $aInfos['order_info']['to_lng'];
            $aPassengerKafka['dest_lat']        = $aInfos['order_info']['to_lat'];
            $aPassengerKafka['from_poi_id']     = $aInfos['order_info']['from_poi_id'];
            $aPassengerKafka['to_poi_id']       = $aInfos['order_info']['to_poi_id'];
            $aPassengerKafka['create_time']     = time();
            $aPassengerKafka['product_id']      = $aInfos['order_info']['product_id'];
            $aPassengerKafka['scene_type']      = $aInfos['order_info']['scene_type'];
            $aPassengerKafka['car_type']        = $sCarLevel;
            $aPassengerKafka['multi_require_product'] = '';
            $aPassengerKafka['preference_product']    = '';
            $aPassengerKafka['is_anycar']      = 0;
            $aPassengerKafka['n_tuple']        = json_encode($aInfos['order_info']['n_tuple'], JSON_UNESCAPED_UNICODE);
            $aPassengerKafka['current_lng']    = (float)($this->getRequest()->getQuery('lng', false));
            $aPassengerKafka['current_lat']    = (float)($this->getRequest()->getQuery('lat', false));
            $aPassengerKafka['client_type']    = intval($this->getRequest()->getQuery('client_type', false));
            $aPassengerKafka['platform_type']  = intval($this->getRequest()->getQuery('platform_type', false));
            $aPassengerKafka['starting_name']  = (string)($aInfos['order_info']['starting_name']);
            $aPassengerKafka['dest_name']      = (string)($aInfos['order_info']['dest_name']);
            $aPassengerKafka['departure_time'] = $aInfos['order_info']['departure_time'];
            $aPassengerKafka['is_fast_car']    = (int)($aInfos['order_info']['is_fast_car']);
            $aPassengerKafka['oType']          = $aInfos['order_info']['oType'];

            $aPassengerKafka['app_version'] = $aInfos['common_info']['app_version'];
            $aPassengerKafka['origin_id']   = $aInfos['common_info']['origin_id'];

            // $oPassengerEstimateTopic = new PassengerEstimateTopic();
            if (!isset($aInfos['bill_info'], $aInfos['bill_info']['bills'], $aInfos['bill_info']['bills'][$sCarLevel])) {
                return [];
            }

            $aSceneType = [(int)(BizLib\Utils\Horae::getSceneType((int)($aProductInfo['combo_type'])))];
            $aBill      = $aInfos['bill_info'];
            $aPassengerKafka['bubble_id']   = $aBill['estimate_id'];
            $aPassengerKafka['estimate_id'] = $aBill['estimate_id'];
            $aPassengerKafka['combo_type']  = (int)($aProductInfo['combo_type']);
            //拼车
            if (Utils\Horae::isCarpool($aInfos['order_info']['combo_type'], $aInfos['order_info']['require_level'])) {
                if (empty($aInfos['bill_info']['is_carpool_open'])) {
                    return [];
                }

                $aSceneType = [
                    \BizLib\Constants\Horae::HORAE_SCENE_TYPE_CARPOOL,
                    \BizLib\Constants\Horae::HORAE_SCENE_TYPE_STATION_CARPOOL,
                ];
                $aPassengerKafka['combo_type'] = (int)($aProductInfo['combo_type'] ?? \BizLib\Constants\Horae::TYPE_COMBO_CARPOOL);
            } elseif (Product::isAnyCar($aInfos['order_info']['product_id'])) {
                $aPassengerKafka['require_car_level']     = '0';
                $aPassengerKafka['multi_require_product'] = AnyCarOrderLogic::getInstance()->buildMultiForPublicLogKafka($aInfos['multi_require_product']);
                $aPassengerKafka['preference_product']    = AnyCarOrderLogic::getInstance()->buildPreferenceForPublicLogKafka($aInfos['multi_require_product'], $aInfos['order_info']['channel']);
                $aPassengerKafka['is_anycar'] = 1;
            }

            $aPassengerKafka['estimate_type']           = $this->_isDefault($sCarLevel, $aSceneType, $aResponseInfo);
            $aPassengerKafka['off_peak_push_timestamp'] = ResponseLogic::OFF_PEAK_PUSH_INFO == $aResponseInfo['data']['push_info']['type'] ? ($aPassengerKafka['estimate_type'] ? time() : -1) : -1;
            $aPassengerKafka['estimate_distance_metre'] = $aInfos['bill_info']['driver_metre'];
            $aPassengerKafka['estimate_time_minutes']   = $aInfos['bill_info']['driver_minute'];
            $aPassengerKafka['basic_total_fee']         = $aInfos['bill_info']['bills'][$sCarLevel]['basic_total_fee'];
            $aPassengerKafka['dynamic_total_fee']       = $aBillInfo['dynamic_total_fee'];
            $aPassengerKafka['dynamic_diff_price']      = $aBillInfo['dynamic_diff_price'];
            $aPassengerKafka['estimate_fee']            = $aInfos['activity_info'][0]['estimate_fee'];
            $aPassengerKafka['cap_price']          = $aBillInfo['cap_price'];
            $aPassengerKafka['final_coupon_value'] = $aInfos['activity_info'][0]['final_coupon_value'];
            $aPassengerKafka['pay_type']           = $this->_getSelectedPayType($aInfos['payments_info']['user_pay_info']['busi_payments'] ?? []);

            $aDynamicInfo = $aBillInfo['dynamic_info'];
            unset($aDynamicInfo['dynamic_price_id']);
            $aPassengerKafka['dynamic_info'] = json_encode($aDynamicInfo);

            $aPassengerKafka['estimate_trace_id'] = Trace::traceId();
            // $oPassengerEstimateTopic->sync($aPassengerKafka);
            return $aPassengerKafka;
        }

        return [];
    }

    /**
     * @param array $aPayments aPayments
     * @return int
     */
    private function _getSelectedPayType($aPayments) {
        foreach ($aPayments as $aPayment) {
            if (!empty($aPayment['isSelected'])) {
                return $aPayment['tag'];
            }
        }

        return 0;
    }


    private function _isDefault($sCarLevel, $aSceneTypes, $aResponse) {
        $aEstimateItem = current($aResponse['data']['estimate_data']);
        if (1 == count($aResponse['data']['estimate_data']) && $aEstimateItem['require_level'] == $sCarLevel) {
            return 1;
        }

        foreach ($aResponse['data']['estimate_data'] as $aSection) {
            if ($aSection['require_level'] == $sCarLevel && in_array($aSection['scene_type'], $aSceneTypes)) {
                //当快车附带了拼车信息且is_select==1时，快车返回is_default=1 但不算做冒泡；需要讲estimate_type改为0
                if (isset($aSection['link_product']) && $aSection['link_product']['is_select']) {
                    return 0;
                } else {
                    return $aSection['is_default'];
                }
            }
        }

        return 0;
    }

    /**
     * @param $aRawInput
     *
     * @return array|mixed
     */
//    private function _processMultiEstimateDegrade($aRawInput) {
//        //获取账单、支付方式、优惠券、乘客运营活动券数据
//        $aResponseParams = EstimateLogic::getInstance($aRawInput[0])->getResponseData();
//
//        //对返回数据进行格式化
//        $aResponseParams = array($aResponseParams);
//        $oResponse       = ResponseLogic::getInstance($aResponseParams, array());
//        $oResponse->setEstimateDegrade(true);
//        $this->_bEstimateDegrade = true;
//        $aResponseInfo           = $oResponse->multiExecute();
//
//        return [$aResponseParams, $aResponseInfo];
//    }

    /**
     * @param $aRawInput
     *
     * @return bool
     */
//    private function _checkDegrade($aRawInput) {
//        // 降级开关
//        if ((new \Xiaoju\Apollo\Apollo())->featureToggle(
//            'multi-estimate-degrade-toggle',
//            array(
//                'pid'         => (int)($aRawInput[0]['passenger_info']['pid']),
//                'district'    => (string)($aRawInput[0]['order_info']['district']),
//                'phone'       => (string)($aRawInput[0]['passenger_info']['phone']),
//                'app_version' => (string)($aRawInput[0]['common_info']['app_version']),
//            )
//        )->allow()
//        ) {
//            return true;
//        }
//
//        return false;
//    }

    /**
     * 存储预估参数.
     *
     * @param null
     *
     */
    private function _writeParamsCache($aParams, $aResponseInfo) {
        if (\BizLib\Constants\OrderSystem::TYPE_ORDER_BOOKING === (int)($aParams['order_type'])) {
            return;
        }

        if (empty($aResponseInfo['data']['estimate_data']) || !is_array($aResponseInfo['data']['estimate_data'])) {
            return;
        }

        $aAllowKey = [
            implode('_', [Product::PRODUCT_ID_FAST_CAR, \BizLib\Utils\CarLevel::DIDI_PUTONG_CAR_LEVEL, 0]),
            implode('_', [Product::PRODUCT_ID_FAST_CAR, \BizLib\Utils\CarLevel::DIDI_YOUXIANG_CAR_LEVEL, 0]),
            implode('_', [\BizCommon\Constants\OrderNTuple::PRODUCT_ID_ANY_CAR, \BizLib\Utils\CarLevel::DIDI_ANY_CAR_CAR_LEVEL, 0]),
            AnyCarOrderLogic::PRODUCT_KEY_CARPOOL, //拼车
            AnyCarOrderLogic::PRODUCT_KEY_BUSINESS_KUAICHE, //企业快车
            AnyCarOrderLogic::PRODUCT_KEY_BUSINESS_CARPOOL, //企业拼车
            AnyCarOrderLogic::PRODUCT_KEY_KUAICHE_FLAT_RATE, //快车区域一口价303
            AnyCarOrderLogic::PRODUCT_KEY_KUAICHE_FACTOR_FLAT_RATE, //快车多因素一口价309
            AnyCarOrderLogic::PRODUCT_KEY_YOUXIANG_FACTOR_FLAT_RATE, //优享多因素一口价309
        ];

        foreach ($aResponseInfo['data']['estimate_data'] as $aItem) {
            if (empty($aItem['estimate_id'])) {
                continue;
            }

            $iProductId = Product::getProductIdByBusinessId($aItem['business_id']);
            $sGroupKey  = implode('_', [$iProductId, $aItem['require_level'], $aItem['combo_type']]);

            if (in_array($sGroupKey, $aAllowKey) || Utils\Horae::isMultiFactorFlatRateV2($aItem)) {
                $sKey = PrefixSuffix::S_PASSENGER_ESTIMATE_PARAM.$aItem['estimate_id'];
                StorageClient::getInstance(StorageClient::STORE)->setex($sKey, self::ESTIMATE_PARAM_CACHE_TIME, json_encode($aParams));
            }
        }

        //预估eid之间关联起来
        $this->_setLinkEIDCache($aResponseInfo);

        // 记录：预估车型的展现。暂时存储在cache，后续迁移到报价单Dise
        $aEstimateShowProducts = [];
        foreach ($aResponseInfo['data']['estimate_data'] as $aItem) {
            $aEstimateShowProducts[] = [
                'estimate_id'   => $aItem['estimate_id'],
                'business_id'   => $aItem['business_id'],
                'require_level' => $aItem['require_level'],
                'combo_type'    => $aItem['combo_type'],
                'fee_msg'       => $aItem['fee_msg'],
                'combo_ids'     => isset($aItem['combo_id']) ? (string)($aItem['combo_id']) : '',
            ];
        }

        $sEstimateTraceId = $aResponseInfo['data']['estimate_trace_id'];
        \BizCommon\Models\Cache\EstimatePrice::getInstance()->setCacheEstimateShowProductList($sEstimateTraceId, $aEstimateShowProducts);
    }

    /**
     * @param array $aResponseInfo $aResponseInfo
     * @return void
     */
    private function _setLinkEIDCache($aResponseInfo) {
        //预估eid之间关联起来，且缓存下来
        $aEIDLinkMap = [
            Utils\ProductCategory::PRODUCT_CATEGORY_CARPOOL_STATION => Utils\ProductCategory::PRODUCT_CATEGORY_FAST,
            Utils\ProductCategory::PRODUCT_CATEGORY_UNIONE_CARPOOL  => Utils\ProductCategory::PRODUCT_CATEGORY_UNIONE,
        ];

        $aLinkKey   = [];
        $aLinkValue = [];
        foreach ($aResponseInfo['data']['estimate_data'] as $aItem) {
            if (in_array($aItem['product_category'], array_keys($aEIDLinkMap))) {
                $aLinkKey[$aItem['product_category']] = $aItem['estimate_id'];
            }

            if (in_array($aItem['product_category'], array_values($aEIDLinkMap))) {
                $aLinkValue[$aItem['product_category']] = $aItem['estimate_id'];
            }
        }

        if (!empty($aLinkKey) && !empty($aLinkValue)) {
            foreach ($aEIDLinkMap as $key => $value) {
                if (in_array($key, array_keys($aLinkKey)) && in_array($value, array_keys($aLinkValue))) {
                    BizCommon\Models\Cache\EstimatePrice::getInstance()->setEstimateLink($aLinkKey[$key], $aLinkValue[$value]);
                }
            }
        }
    }

    /**
     * @param array $aOriginParams 预估端上参数
     * @return void
     */
    private function _fixOriginParam(&$aOriginParams) {
        // 端上传参bug，导致anycar发单是带上的预约单标记，会导致duse的addOrder接口报错，需要anycar订单强制改成实时单
        if (\BizLib\Constants\Common::MENU_ANYCAR == $aOriginParams['menu_id']) {
            $aOriginParams['order_type'] = \BizLib\Constants\OrderSystem::TYPE_ORDER_NOW;
        }

        $aOriginParams['menu_id'] = (string)($aOriginParams['menu_id']);
    }

    /**
     * 处理dds的返回，本来dds/decision的返回只用来展示，但是anycar的is_select从dds过来，
     * 目前代码里还是使用aInfo里的is_select，临时根据dds/decision的结果，修改aInfo里的数据
     * @param array $aInfos 计价数据
     * @return array 修改后的数据
     */
    private function _processDDSResponse($aInfos) {
        DecisionLogic::getInstance()->setDecisionParams($aInfos);
        DecisionLogic::getInstance()->executeDecision();

        $aDDSAnycarSelection = DecisionLogic::getInstance()->getAnycarSelection();
        foreach ($aInfos as &$aInfo) {
            if (Product::isAnyCar($aInfo['order_info']['product_id'])) {
                if (!empty($aInfo['preference_product'])) {
                    $aPreferenceProduct = json_decode($aInfo['preference_product'], true);
                }

                foreach ($aInfo['bill_info']['multi_info'] as &$aItem) {
                    $aItem['is_selected'] = AnyCarOrderLogic::getInstance()->getSubItemIsSelected($aItem, $aPreferenceProduct, $aDDSAnycarSelection, $aInfo['passenger_info']['phone']);
                }

                foreach ($aInfo['multi_require_product'] as &$aMultiRequireProduct) {
                    $aMultiRequireProduct['is_selected'] = AnyCarOrderLogic::getInstance()->getSubItemIsSelected($aMultiRequireProduct, $aPreferenceProduct, $aDDSAnycarSelection, $aInfo['passenger_info']['phone']);
                }

                break;
            }
        }

        return $aInfos;
    }

    /**
     * function _writeMultiResKafka
     * @param array $aResponseParamsNew new info
     * @param array $aResponseInfo      info
     * @return void
     */
    private function _writeMultiResKafka($aResponseParamsNew, $aResponseInfo) {
        $aBatchData = [];
        foreach ($aResponseParamsNew as $aInfosNew) {
            if ($this->_shouldRemove($aInfosNew)) {
                continue;
            }

            $aData = $this->_writeKafkaForMultiNew($aInfosNew, $aResponseInfo);
            !empty($aData) && $aBatchData[] = $aData;
        }

        if (!empty($aBatchData)) {
            $oTopic = new PassengerEstimateTopic();
            $oTopic->batchSync($aBatchData);
        }
    }
}
