<?php

//use BizCommon\Constants\OrderNTuple;
//use BizCommon\Constants\PrefixSuffix;
//use BizCommon\Models\Cache\EstimatePrice;
//use BizLib\Client\Cache\StorageClient;
//use BizLib\Config as NuwaConfig;
//use BizLib\Constants\OrderSystem;
//use BizLib\ErrCode\RespCode;
//use BizLib\Exception\ExceptionRespInterface;
//use BizLib\ExceptionHandler;
//use BizLib\Utils;
//use BizLib\Utils\CarLevel;
//use BizLib\Utils\Product;
//use Dirpc\SDK\PreSale\MultiEstimatePriceResponse as Response;
//use Dirpc\SDK\PreSale\TaxiMultiEstimatePriceRequest as Request;
//use Nebula\Exception\Route\InterRouteException;
//use Nuwa\Protobuf\Internal\Message;
use PreSale\Core\Controller;
//use PreSale\Infrastructure\Util\AsyncMode\AsyncContainer;
//use PreSale\Logics\taxiEstimatePrice\DecisionLogic;
//use PreSale\Logics\taxiEstimatePrice\KafkaLogic;
//use PreSale\Logics\taxiEstimatePrice\multiRequest\PriceLogic;
//use PreSale\Logics\taxiEstimatePrice\multiRequest\ProductList;
//use PreSale\Logics\taxiEstimatePrice\multiResponse\MainRender;
//use PreSale\Logics\taxiEstimatePrice\multiResponse\MultiEstimatePublicLogV2;
//use PreSale\Logics\taxiEstimatePrice\params\ParamsLogic;

/**
 * 出租车快的小程序专用的预估接口
 * wiki: http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=*********
 * Class pTaxiMultiEstimatePrice.
 */
class PTaxiMultiEstimatePriceController extends Controller
{
    const ESTIMATE_PARAM_CACHE_TIME = 432000; //5 hours

    /**
     * @return void
     */
    public function indexAction() {
//        Message::setEmitDefaults(true);
//        try {
//            //参数初始化
//            $oRequest         = Utils\Request::getInstance();
//            $oEstimateRequest = new Request();
//            $oEstimateRequest->mergeFromJsonArray($oRequest->get());
//            ParamsLogic::checkDaCheAnyCarParams($oEstimateRequest);
//
//            // 调用dds服务，获取当前城市已开服的品类（product）数据
//            $oProductList = ProductList::getInstance($oEstimateRequest);
//            $aProductList = $oProductList->buildProductList();
//
//            //写预估入参kafka(目前主要提供给地图使用)
//            $oKafkaLogic = new KafkaLogic();
//            $oKafkaLogic->writeMultiReqKafka($aProductList);
//
//            //获取账单、支付方式、优惠券、乘客运营活动券数据
//            $oPriceLogic     = new PriceLogic();
//            $aResponseParams = $oPriceLogic->getMultiResponse($aProductList);
//            $aResponseParams = $this->_processDDSResponse($aResponseParams);
//
//            //渲染对端数据
//            $oMainRender = MainRender::getInstance($aResponseParams, []);
//            $oMainRender->setProductList($aProductList);
//            $oMainRender->buildAInfo();
//            $aRenderInfo = $oMainRender->multiExecute();
//
//            //格式化输出
//            $oResponse = new Response();
//            $oResponse->mergeFromJsonArray($aRenderInfo);
//            $sResponseJson = $oResponse->serializeToJsonString();
//            $aResponseInfo = json_decode($sResponseJson, true);
//        } catch (\Exception $e) {
//            $aErrMsg       = NuwaConfig::text('errno', 'pEstimatePrice_error_msg');
//            $oHandler      = ExceptionHandler::getInstance();
//            $aResponseInfo = $oHandler->handleException($e, ['err_msg' => $aErrMsg, ]);
//            $oResponse     = new Response();
//            $oResponse->mergeFromJsonArray($aResponseInfo);
//            if ($e instanceof ExceptionRespInterface
//                && RespCode::R_ESTIMATE_DOWNSTREAM_FAIL_DEGRADE == $e->getRespCode()
//            ) {
//                header('HTTP/1.1 596 limit access', true, 596);
//            }
//
//            $this->sendJson($oResponse, (string)($this->getRequest()->getQuery('callback', false)));
//            return;
//        }
//
//        $this->_writeParamsCache((array) $this->getRequest()->getQuery(null, false), $aResponseInfo);
//
//        $this->sendJson($sResponseJson, (string)($this->getRequest()->getQuery('callback', false)));
//        fastcgi_finish_request();
//
//        $aResponseInfo = $this->_spreadResponse($aResponseInfo);
//
//        $oKafkaLogic->writeMultiResKafka($aResponseParams, $aResponseInfo);
//
//        (new MultiEstimatePublicLogV2(
//            $oEstimateRequest,
//            $aResponseParams,
//            $aResponseInfo,
//            $aProductList
//        ))->multiWritePublicLog();
//        AsyncContainer::run();
    }
    /**
     * @param array $aParams       $aParams
     * @param array $aResponseInfo $aResponseInfo
     * @return void
     */
//    private function _writeParamsCache(array $aParams, array $aResponseInfo) {
//        if (OrderSystem::TYPE_ORDER_BOOKING === (int)($aParams['order_type'])) {
//            return;
//        }
//
//        if (empty($aResponseInfo['data']['estimate_data']) || !is_array($aResponseInfo['data']['estimate_data'])) {
//            return;
//        }
//
//        $aAllowKey = [
//            implode(
//                '_',
//                [
//                    OrderNTuple::PRODUCT_ID_ANY_CAR,
//                    CarLevel::DIDI_ANY_CAR_CAR_LEVEL,
//                    0,
//                ]
//            ),
//        ];
//
//        foreach ($aResponseInfo['data']['estimate_data'] as $aItem) {
//            if (empty($aItem['estimate_id'])) {
//                continue;
//            }
//
//            $iProductId = Product::getProductIdByBusinessId($aItem['business_id']);
//            $sGroupKey  = implode('_', [$iProductId, $aItem['require_level'], $aItem['combo_type']]);
//            if (in_array($sGroupKey, $aAllowKey) || Utils\Horae::isMultiFactorFlatRateV2($aItem)) {
//                $sKey = PrefixSuffix::S_PASSENGER_ESTIMATE_PARAM.$aItem['estimate_id'];
//                StorageClient::getInstance(StorageClient::STORE)
//                ->setex($sKey, self::ESTIMATE_PARAM_CACHE_TIME, json_encode($aParams));
//            }
//        }
//
//        //预估eid之间关联起来
//        $this->_setLinkEIDCache($aResponseInfo);
//
//        // 记录：预估车型的展现。暂时存储在cache，后续迁移到报价单Dise
//        // 2020-12-21: 目前看有两处使用
//        //             1. 拼车通勤卡读取 @see pre-sale/app/logics/carpool/CarpoolCommuteCard.php#L291
//        //             2. transaction/app/Infrastructure/Repository/OrderExtensionRepository.php#L499
//        $aEstimateShowProducts = [];
//        foreach ($aResponseInfo['data']['estimate_data'] as $aItem) {
//            $aEstimateShowProducts[] = [
//                'estimate_id'   => $aItem['estimate_id'],
//                'business_id'   => $aItem['business_id'],
//                'require_level' => $aItem['require_level'],
//                'combo_type'    => $aItem['combo_type'],
//                'fee_msg'       => $aItem['fee_msg'],
//                'combo_ids'     => isset($aItem['combo_id']) ? (string)($aItem['combo_id']) : '',
//            ];
//        }
//
//        $sEstimateTraceId = $aResponseInfo['data']['estimate_trace_id'];
//        EstimatePrice::getInstance()
//        ->setCacheEstimateShowProductList($sEstimateTraceId, $aEstimateShowProducts);
//    }

    /**
     * @param array $aResponseInfo $aResponseInfo
     * @return void
     */
//    private function _setLinkEIDCache(array $aResponseInfo) {
//        //预估eid之间关联起来，且缓存下来
//        $aEIDLinkMap = [
//            Utils\ProductCategory::PRODUCT_CATEGORY_CARPOOL_STATION => Utils\ProductCategory::PRODUCT_CATEGORY_FAST,
//            Utils\ProductCategory::PRODUCT_CATEGORY_UNIONE_CARPOOL  => Utils\ProductCategory::PRODUCT_CATEGORY_UNIONE,
//        ];
//
//        $aLinkKey   = [];
//        $aLinkValue = [];
//        foreach ($aResponseInfo['data']['estimate_data'] as $aItem) {
//            if (in_array($aItem['product_category'], array_keys($aEIDLinkMap))) {
//                $aLinkKey[$aItem['product_category']] = $aItem['estimate_id'];
//            }
//
//            if (in_array($aItem['product_category'], array_values($aEIDLinkMap))) {
//                $aLinkValue[$aItem['product_category']] = $aItem['estimate_id'];
//            }
//        }
//
//        if (!empty($aLinkKey) && !empty($aLinkValue)) {
//            foreach ($aEIDLinkMap as $key => $value) {
//                if (in_array($key, array_keys($aLinkKey)) && in_array($value, array_keys($aLinkValue))) {
//                    BizCommon\Models\Cache\EstimatePrice::getInstance()
//                    ->setEstimateLink($aLinkKey[$key], $aLinkValue[$value]);
//                }
//            }
//        }
//    }

    /**
     * 处理dds的返回，本来dds/decision的返回只用来展示，但是anycar的is_select从dds过来，
     * 目前代码里还是使用aInfo里的is_select，临时根据dds/decision的结果，修改aInfo里的数据
     * @param array $aInfos 计价数据
     * @return array 修改后的数据
     */
//    private function _processDDSResponse(array $aInfos): array {
//        DecisionLogic::getInstance()->setDecisionParams($aInfos);
//        DecisionLogic::getInstance()->executeDecision();
//        DecisionLogic::getInstance()->buildGroupConf();
//        return $aInfos;
//    }

    /**
     * 将返回结果平铺展开，用于记录日志，和后续处理
     * @param array $response 返回结果
     * @return array
     */
//    private function _spreadResponse(array $response): array {
//        $estiamteData = [];
//        foreach ($response['data']['estimate_data'] as $value) {
//            if (empty($value['sub_product'])) {
//                $estiamteData[] = $value;
//            } else {
//                foreach ($value['sub_product'] as $subValue) {
//                    $estiamteData[] = $subValue;
//                }
//            }
//        }
//
//        $response['data']['estimate_data'] = $estiamteData;
//
//        return $response;
//
//    }
}
