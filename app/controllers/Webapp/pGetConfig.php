<?php

use BizLib\Config as NuwaConfig;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\UtilHelper;
/*
 *
 * @authors <PERSON> (chen<PERSON><PERSON>@didichuxing.com)
 * @oldAuthors <EMAIL>
 * @date    2017-07-14 19:14:33
 * @desc    专车配置接口，返回发票类型，投诉类型，评价类型等配置信息。
 * @wiki    http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=108861888
 */
use BizLib\ExceptionHandler;
use BizLib\Exception\ExceptionWithResp;
use BizLib\ErrCode\RespCode;
use BizLib\ErrCode\Code;
use BizCommon\Models\Passenger\Passenger;

class PGetConfigController extends \PreSale\Core\Controller
{
    /**
     * 乘客端配置.
     *
     * @param int $iVersion 客户端存储的版本号
     *                      return array(
     *                      version =>  1,//当前配置版本号
     *                      invoiceType  =>  array(//发票类型
     *                      '叫车服务费',...
     *                      ),
     *                      complaintType   =>  array(//投诉类型
     *                      '司机迟到',...
     *                      ),
     *                      commentType =>  array(
     *                      'commentTips'  =>  '您的评价，让我们做得更好',
     *                      'goodCmtTips'   =>  '请监督司机是否存在以下问题',
     *                      '   badCmtTips'    =>  '请把您的不满告诉我们',
     *                      'good'  =>  array(
     *                      '为我开车门',...//好评类型
     *                      ),
     *                      'good'  =>  array(
     *                      '司机迟到',...//差评类型
     *                      ),
     *                      ),
     *                      )
     */
    const VERSION = 9;

    public function indexAction() {
        try {
            list($iVersion, $sOid, $sOrderId, $sDistrict, $iPid) = $this->_getParams();
            $aResult            = UtilsCommon::getErrMsg(RespCode::P_SUCCESS, $this->aErrnoMsg);
            $aResult['version'] = self::VERSION;
            //版本号
            if ($iVersion == $aResult['version']) {
                $aResult['errno'] = (int)($aResult['errno']);
                $this->sendTextJson($aResult);

                return;
            }

            $aPassengerConfig = $this->_getPassengerConfig();
            $aShareConfig     = $this->_getShareConfig($sOid, $sOrderId, $iPid);
            $aResult          = array_merge($aResult, $aPassengerConfig, $aShareConfig, $aCommonConfig);
        } catch (\Exception $e) {
            $aResult = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg, ]);
        }

        $aResult['errno'] = (int)($aResult['errno']);
        $this->sendTextJson($aResult);
    }

    /**
     * 获取参数并校验.
     *
     * <AUTHOR>
     * @datetime 2017-06-13T20:58:33+0800
     *
     * @return array
     */
    private function _getParams() {
        $iVersion = intval($this->getRequest()->getQuery('version', false));
        $sToken   = addslashes($this->getRequest()->getQuery('token', false));
        $sOid     = (string)($this->getRequest()->getQuery('oid', false));
        //检查参数
        if (empty($sToken) || empty($sOid)) {
            $aReturn = UtilsCommon::getErrMsg(RespCode::P_ERRNO_PARAMS_ERROR);
            throw new ExceptionWithResp(Code::E_COMMON_PARAM_INVALID_VALUE, $aReturn['errno'], $aReturn['errmsg'], ['token' => $sToken, 'orderid' => $sOid, ]);
        }

        //$this->load->model('passenger/Passenger');
        $iPid = Passenger::getInstance()->getPidByToken($sToken);
        if ($iPid <= 0) {
            $aReturn = UtilsCommon::getErrMsg(RespCode::P_PARAMS_ERROR);
            throw new ExceptionWithResp(Code::E_COMMON_TOKEN_INVALID, $aReturn['errno'], $aReturn['errmsg'], ['token' => $sToken, 'orderid' => $sOid, ]);
        }

        $aResult = UtilHelper::decodeId($sOid);
        if (!empty($aResult['district']) && !empty($aResult['oid'])) {
            return [$iVersion, $sOid, $aResult['oid'], $aResult['district'], $iPid, ];
        } else {
            $aReturn = UtilsCommon::getErrMsg(RespCode::P_ERRNO_PARAMS_ERROR);
            throw new ExceptionWithResp(Code::E_COMMON_OID_DECODE_ERROR, $aReturn['errno'], $aReturn['errmsg'], ['district' => $aResult['district'], 'oid' => $aResult['oid'], ]);
        }
    }

    /**
     * 获取config_passenger中的配置.
     *
     * <AUTHOR>
     * @datetime 2017-07-10T10:50:31+0800
     *
     * @return array
     */
    private function _getPassengerConfig() {
        return NuwaConfig::config('config_passenger', 'config_passenger');
    }

    /**
     * 获取config_share中的配置.
     *
     * <AUTHOR>
     * @datetime 2017-07-10T10:50:31+0800
     *
     * @return array
     */
    private function _getShareConfig($sOid, $sOrderId, $iPid) {
        $aShareList = NuwaConfig::config('config_share', 'gulfstreamShare');
        if (isset($aShareList[0])) {
            $iKey = (int)($sOrderId) + $iPid;
            $sSig = UtilHelper::getSecretSig($iKey);
            $aShareList[0]['weixinShareUrl'] = sprintf($aShareList[0]['weixinShareUrl'], $sSig, $sOid);

            return ['share' => $aShareList[0]];
        }

        return [];
    }
}
