<?php

/*
 *
 * <AUTHOR>
 * @date 2018/9/14
 */
use BizLib\Utils\Request;
use Xiaoju\Apollo\Apollo as ApolloV2;
use Nuwa\Core\Application;

/**
 * class pGetLite.
 */
class pGetLiteController extends \PreSale\Core\Controller
{
    public function init() {
        parent::init();
        $this->setViewpath(Application::app()->getPath('views'));
    }

    public function checkParams() {
        //参数处理
        $oRequest        = Request::getInstance();
        $aParams['test'] = $oRequest->getStr('test');
        $aParams['oid']  = $oRequest->getStr('oid'); //端上上传的设备oid，非订单oid
        $aParams['lite-whitelist-oid'] = $oRequest->getStr('lite-whitelist-oid');

        return $aParams;
    }

    public function indexAction() {
        $aParams        = $this->checkParams();
        $oApolloV2      = new ApolloV2();
        $sPhone         = $_COOKIE['lite_phone'];
        $oFeatureToggle = $oApolloV2->featureToggle(
            'passenger_lite_version',
            array(
                'key'   => $aParams['oid'],
                'test'  => $aParams['test'],
                'oid'   => $aParams['oid'],
                'lwo'   => $aParams['lite-whitelist-oid'],
                'phone' => $sPhone,
            )
        );
        if ($oFeatureToggle->allow()) {
            $this->getView()->display('webapp6.0/lite6.html');
        } else {
            $this->getView()->display('webapp6.0/index.html');
        }
    }
}
