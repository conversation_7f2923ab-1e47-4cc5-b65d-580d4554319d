<?php

use BizLib\Log as NuwaLog;
use Nuwa\Core\Application;
/*
 * *************************************************************************
 *
 * Copyright (c) 2017 xiaojukeji.com, Inc. All Rights Reserved
 * @desc: webapp首页
 * @wiki :
 * @原author: l<PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
 * <AUTHOR> <EMAIL>
 *
 * @package : application/controllers/webapp/
 *
 *
 *************************************************************************
 */
use BizLib\Utils\Request;

class PIndexController extends \PreSale\Core\Controller
{
    public function init() {
        parent::init();
        $this->setViewpath(Application::app()->getPath('views'));
    }

    public function indexAction() {
        list($sPage, $sOpenId, $iCityId) = $this->_getAndCheckParams();
        // 车票新增
        if (isset($sPage) && ('diditicket' == $sPage)) {
           // $this->load->view('webapp5.0/ticket.html');
            $this->getView()->display('webapp5.0/ticket.html');
            return;
        }

        $aData = ['isPush' => false, ];
        try {
           // //webapp 长链接apollo开关
           // if ((new \Xiaoju\Apollo\Apollo())->featureToggle(
               // 'gs_webapp_push_switch',
               // array(
                   // 'openid' => $sOpenId,
                   // 'cityid' => $iCityId,
                   // 'key'    => time(),
               // )
           // )->allow()
           // ) {
               // $aData['isPush'] = true;
           // }
            $aData['isPush'] = true;

            // webapp首页改版灰度.
            if ((new \Xiaoju\Apollo\Apollo())->featureToggle(
                'ab_for_webapp_gulfstream',
                [
                    'key'    => $sOpenId,
                    'city'   => $iCityId,
                    'openid' => $sOpenId,
                ]
            )->allow()
            ) {
                $this->getView()->display('webapp5.0/newindex.html', ['data' => json_encode($aData)]);
                //$this->load->view('webapp5.0/newindex.html', ['data'=>json_encode($aData)]);
            } else {
                $this->getView()->display('webapp5.0/index.html', ['data' => json_encode($aData)]);
                //$this->load->view('webapp5.0/index.html', ['data'=>json_encode($aData)]);
            }
        } catch (\Exception $e) {
            NuwaLog::warning(sprintf('load webapp html failed! [%s]. use default page:webapp5.0/index.html.', $e->getMessage()));
            $this->getView()->display('webapp5.0/index.html', ['data' => json_encode($aData)]);
            //$this->load->view('webapp5.0/index.html', ['data'=>json_encode($aData)]);
        }
    }

    /*
     * 参数处理和验证
     * @params  null
     * @return  array
     */
    private function _getAndCheckParams() {
        $oReq    = Request::getInstance();
        $sPage   = $oReq->getStr('page');
        $sOpenId = $oReq->getStr('openid');
        $iCityId = $oReq->getInt('area');

        $sOpenId = isset($sOpenId) ? $sOpenId : '';
        $iCityId = isset($iCityId) ? $iCityId : 0;

        return [$sPage, $sOpenId, $iCityId, ];
    }
}
