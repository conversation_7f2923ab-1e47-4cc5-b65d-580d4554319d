<?php

use BizLib\Libraries\RedisDB;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\MapHelper;
use BizCommon\Models\Activity\Project;
use BizCommon\Models\Pay\PayAbility;
/**===
 * @property FastCarPool $FastCarPool
 * @property WxAgent $WxAgent
 * @property PassengerPrompt $PassengerPrompt
 * @property Passenger $Passenger
 * @property PassengerGuide $PassengerGuide
 * @property Project $Project
 * @property LowestDiscount $LowestDiscount
 * @property ASyncPassengerKafka $ASyncPassengerKafka
 ===*/
use BizLib\ExceptionHandler;
use BizLib\Exception\ExceptionWithResp;
use BizLib\ErrCode\RespCode;
use BizLib\ErrCode\Code;

class pGetFlagController extends \PreSale\Core\Controller
{
    /** @var $redisdb RedisDB */
    public $redisdb;
    /** @var $payAbility PayAbility */
    public $payAbility;
    /** @var $project Project */
    public $project;
    public $aLogParam;

    const WLWEBAPP          = 'webapp'; //标识包括qqwebapp和wlwebapp
    const QQWEBAPP          = 'qqwebapp';
    const ALIWEBAPP         = 'aliwebapp';
    const VERSION           = '1';
    const DEFAULT_WAIT_TIME = 35;

    public function init() {
        parent::init();
    }

    public function indexAction() {
        $sFrom = (string)($this->getRequest()->fetchGetPost('from', false));
        //是app还是webapp
        $sCallBack = (string)($this->getRequest()->getQuery('callback', false));
        try {
            list($sPhone, $fLng, $fLat, $aDefaultOutData) = $this->_getParams();
            $aCityInfo = MapHelper::getAreaInfoByLoc($fLng, $fLat);
            if (empty($aCityInfo)) {
                NuwaLog::warning(sprintf('controllers/webapp/pGetFlag | errmsg:arrCityInfo is empty | phone:%s | lng:%s | lat:%s', $sPhone, $fLng, $fLat));
                $this->_sendJsonOrJsonp($aDefaultOutData, $sFrom, $sCallBack);

                return;
            }

            $aOutData = $this->_handelOutDataByCityInfo($aCityInfo);
            $aWapInfo = $this->_handleOutDataByWapInfo($sFrom, $sPhone, $aCityInfo);
            $aOutData = array_merge($aDefaultOutData, $aOutData, $aWapInfo);
            $this->_sendJsonOrJsonp($aOutData, $sFrom, $sCallBack);
            fastcgi_finish_request();
            $this->aLogParam['errno'] = RespCode::P_SUCCESS;
            $this->aLogParam['data']  = json_encode($aOutData);
            NuwaLog::updateLogHeadData($this->aLogParam);
        } catch (\Exception $e) {
            $aResult = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg, ]);
            $this->_sendJsonOrJsonp($aResult, $sFrom, $sCallBack);
        }
    }

    /**
     * 根据传的参数判断是否需要返回jsonp格式.
     *
     * @param json   $aData
     * @param string $sFrom     是native还是webapp
     * @param string $sCallback
     *                          lishuangshuang
     */
    private function _sendJsonOrJsonp($aData, $sFrom = '', $sCallback = '') {
        if (!empty($sFrom) && false !== strpos($sFrom, self::WLWEBAPP) && !empty($sCallback)) {
            $this->sendTextJson($aData, $sCallback);
        } else {
            $this->sendTextJson($aData);
        }
    }

    /**
     * 获取参数并校验.
     *
     * <AUTHOR>
     * @datetime 2017-06-13T20:58:33+0800
     *
     * @return array
     */
    private function _getParams() {
        $sPhone = intval($this->getRequest()->getQuery('phone', false));
        $fLng   = (float)($this->getRequest()->getQuery('lng', false));
        $fLat   = (float)($this->getRequest()->getQuery('lat', false));
        if (empty($fLng) || empty($fLat)) {
            $aReturn = UtilsCommon::getErrMsg(RespCode::P_ERRNO_PARAMS_ERROR, $this->aErrnoMsg);
            throw new ExceptionWithResp(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                $aReturn['errno'],
                $aReturn['errmsg'],
                [
                    'lat' => $fLat,
                    'lng' => $fLng,
                ]
            );
        }

        $aOutData = [
            'errno'           => 0,
            'errmsg'          => 'ok',
            'wanliu_flag'     => 1,   //湾流产品线是否显示
            'home_flag'       => 0,   //首页湾流创可贴tip是否显示
            'wait_flag'       => 0,   //等待接驾超过wait_time时间后是否显示湾流创可贴
            'wait_time'       => self::DEFAULT_WAIT_TIME,  //等待接驾超过的时间
            'wait_image'      => '',
            'tip_image'       => '',
            'dialog_flag'     => 0,   //取消订单时是否询问使用wanliu
            'dialog_title'    => '',
            'dialog_content'  => '',
            'guide_wait_time' => self::DEFAULT_WAIT_TIME,
            'layer'           => 0,
            'version'         => self::VERSION,
        ];
        $aOutData['project_info']['fast_car']['open']         = 0;
        $aOutData['project_info']['fast_car']['bill_ability'] = 0;
        $aOutData['project_info']['fast_car']['msg']          = '';
        $aOutData['project_info']['fast_car']['enter_name']   = '快车';

        return [$sPhone, $fLng, $fLat, $aOutData, ];
    }

    /**
     * 设置停留时间.
     *
     * <AUTHOR>
     * @datetime 2017-07-31T12:00:58+0800
     *
     * @param array $aCityInfo 城市信息
     *
     * @return string
     */
    private function _getWaitTime($aCityInfo) {

       // $this->redisdb  = RedisDB::getInstance();
       // $sKeyPrefix     = UtilsCommon::getRedisPrefix(P_GUIDE_WAITE_TIME);
       // $sKey           = sprintf('%s_%s', $sKeyPrefix, $aCityInfo['district']);
       // $sWaitTime      = $this->redisdb->get($sKey);
       // $sGuideWaitTime = '';
       // if (!empty($sWaitTime) && (int)($sWaitTime) > 0) {
           // $sGuideWaitTime = $sWaitTime;
       // } else {
           // $sGuideWaitTime = !empty($aCityInfo['limit']['wait_time']) ? $aCityInfo['limit']['wait_time'] : self::DEFAULT_WAIT_TIME;
       // }
        $sGuideWaitTime = !empty($aCityInfo['limit']['wait_time']) ? $aCityInfo['limit']['wait_time'] : self::DEFAULT_WAIT_TIME;

        return $sGuideWaitTime;
    }

    /**
     * 对于wapapp过来的请求特殊处理的数据.
     *
     * <AUTHOR>
     * @datetime 2017-07-31T12:01:39+0800
     *
     * @param string $sFrom     来源
     * @param string $sPhone    手机号
     * @param array  $aCityInfo 城市信息
     *
     * @return array
     */
    private function _handleOutDataByWapInfo($sFrom, $sPhone, $aCityInfo) {
        $aOutData         = [];
        $this->payAbility = new PayAbility();
        if ($this->payAbility->checkCouponAbitlity($sFrom)) {
            $this->project            = new Project();
            $aOutData['project_info'] = $this->project->validateProjectOpen($sPhone, $aCityInfo['district']);
        }

        //手Q关闭专车入口
        if (!empty($sFrom) && self::QQWEBAPP == $sFrom) {
            $aOutData['wanliu_flag'] = 0;
            $aOutData['project_info']['fast_car']['open']         = 0;
            $aOutData['project_info']['fast_car']['bill_ability'] = 0;
        }

        return $aOutData;
    }

    /**
     * 根据城市信息设置返回信息.
     *
     * <AUTHOR>
     * @datetime 2017-07-31T12:03:09+0800
     *
     * @param array $aCityInfo 城市信息
     *
     * @return array
     */
    private function _handelOutDataByCityInfo($aCityInfo) {
        $aLimitInfo            = $aCityInfo['limit'];
        $aOutData['wait_flag'] = !empty($aLimitInfo['wait_flag']) ? $aLimitInfo['wait_flag'] : 1;
        $aOutData['wait_image']     = !empty($aLimitInfo['wait_image']) ? $aLimitInfo['wait_image'] : '';
        $aOutData['tip_image']      = !empty($aLimitInfo['tip_image']) ? $aLimitInfo['tip_image'] : '';
        $aOutData['dialog_flag']    = !empty($aLimitInfo['dialog_flag']) ? $aLimitInfo['dialog_flag'] : 0;
        $aOutData['dialog_title']   = !empty($aLimitInfo['dialog_title']) ? $aLimitInfo['dialog_title'] : '';
        $aOutData['dialog_content'] = !empty($aLimitInfo['dialog_content']) ? $aLimitInfo['dialog_content'] : '';

        $aOutData['wait_time'] = $aOutData['guide_wait_time'] = $this->_getWaitTime($aCityInfo);

        return $aOutData;
    }
}
