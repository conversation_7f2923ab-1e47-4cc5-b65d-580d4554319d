<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * <AUTHOR> <<EMAIL>>
 * Date: 2021/3/24
 * Time: 10:34 上午
 */

use BizLib\Exception\ExceptionWithResp;
use BizLib\ErrCode\RespCode;
use BizLib\Error\ExceptionHandler;
use PreSale\Logics\order\CruiseOrderLogic;
use BizLib\Utils\Common;
use BizLib\Constants\Horae;

/**
 * Class PCheckAndConfirmScanCodeController
 */
class PGetCruiseIndexController extends \PreSale\Core\Controller
{

    public $aParams = [];

    /**
     * 初始化
     * @return void
     */
    public function init() {
        parent::init();
    }

    /**
     * action
     * @return void
     */
    public function indexAction() {
        try {
            $aResponse = Common::getErrMsg(GLOBAL_SUCCESS);
            $this->_checkParams();
            $oCruiseOrderLogic = CruiseOrderLogic::getInstance();
            $oCruiseOrderLogic->loadInfos($this->aParams);
            $aResponse['data'] = $oCruiseOrderLogic->getResponseParam();
        } catch (\Exception $e) {
            $sMessage  = $e->getMessage();
            $aResponse = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg,]);
        }

        $this->sendJson($aResponse);

    }

    /**
     * 参数校验
     * @return void
     * @throws ExceptionWithResp ExceptionWithResp.
     */
    private function _checkParams() {
        $this->aParams['scan_token'] = $this->oRequest->getStr('scan_token');
        $iDriverId = $this->oRequest->getStr('driver_id');
        if (!empty($iDriverId)) {
            $this->aParams['driver_id'] = $iDriverId;
        }

        if ((empty($this->aParams['scan_token']) && empty($iDriverId))) {
            throw new ExceptionWithResp(
                RespCode::P_PARAMS_ERROR,
                (string)RespCode::P_PARAMS_ERROR,
                '',
                'errmsg:param error! scan_token:' . $this->aParams['scan_token']
            );
        }

        $this->aParams['city_id'] = $this->oRequest->getInt('city_id');
        if (empty($this->aParams['city_id'])) {
            throw new ExceptionWithResp(
                RespCode::P_PARAMS_ERROR,
                (string)RespCode::P_PARAMS_ERROR,
                '',
                'errmsg:param error! city_id'. $this->aParams['city_id']
            );
        }

        $this->aParams['page_type'] = $this->oRequest->getStr('page_type');
        if (Horae::PAGE_TYPE_CRUISE != $this->aParams['page_type']) {
            throw new ExceptionWithResp(
                RespCode::P_PARAMS_ERROR,
                (string)RespCode::P_PARAMS_ERROR,
                '',
                'errmsg:param error! page_type:' .  $this->aParams['page_type']
            );
        }
    }
}
