<?php

use BizLib\Config as NuwaConfig;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Exception\ExceptionWithResp;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\ExceptionHandler;
use BizLib\ErrCode\RespCode;
use BizLib\Utils\Language;
use BizLib\Utils\MapHelper;
use BizLib\Utils\NumberHelper;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Core\Controller;
use PreSale\Logics\commonAbility\CalendarConfig;
use PreSale\Logics\commonAbility\SpringRedPacketFormatter;
use PreSale\Logics\commonAbility\SpsFeeConfig;
use BizCommon\Logics\FeeName\SpringRedPacketFeeConfig;
use PreSale\Logics\v3Estimate\multiResponse\Component\introMsg\Util as v3Util;
use Dukang\PropertyConst\Order\OrderEstimatePcId;

/**
 * Class pGetSpringRedPacketInfo
 * 1. 节假日服务费宣教页
 * 2. 预估拦截页
 */
class PGetSpringRedPacketInfoController extends Controller
{
    const EXPRESS_ENUM = 0; // 快车枚举
    const POOL_ENUM = 5; // 拼车枚举
    const YUE_GANG_ENUM = 4; // 粤港车枚举
    private $aSpsConfig = null;
    private $_iDisplayType = 0;
    private $_iIsNewConf = 0;

    /**
     * 根据品类和城市获取计价规则详情
     * @return void
     * @throws ExceptionWithResp e
     */
    public function indexAction() {
        $this->aSpsConfig = [];

        $aParams       = $_GET;
        $aResponseInfo = UtilsCommon::pairErrNo(RespCode::P_SUCCESS, $this->aErrnoMsg);

        $aPageInfo = array();
        try {
            //验证参数
            $aArea = $this->getArea($aParams['city_id'], $aParams['flng'], $aParams['flat'], $aParams['lang']);
            if (empty($aArea) || empty($aArea['id']) || empty($aArea['name'])) {
                $aResponseInfo = UtilsCommon::pairErrNo(RespCode::P_PARAMS_ERROR,[RespCode::P_PARAMS_ERROR => '未获取到城市信息']);
                $this->sendTextJson($aResponseInfo);
                return;
            }

            $aParams['lang'] = $aParams['lang'] ?? 'zh-CN';
            // 短期hack英文城市名
            if (Language::EN_US == $aParams['lang']) {
                $aArea['name'] = $aArea['city_name_en'];
            }

            $aConfig = NuwaConfig::text('config_text', 'red_packet_config_v2', array(), $aParams['lang']);
            if (empty($aConfig)) {
                throw new ExceptionWithResp(
                    Code::E_COMMON_CONFIG_NOT_FOUNT,
                    RespCode::P_SERVICE_NOT_OPEN,
                    '',
                    [
                        'exceptionMsg' => [
                            'msg'      => 'dcmp config empty',
                            'moreInfo' => ['args' => $aParams,],
                        ],
                    ]
                );
            }

            $oSpsConfig = SpsFeeConfig::getInstance();
            // 宣教页请求sps设置特殊的caller
            $oSpsConfig->setCaller('publicity_page');
            $oSpsConfig->init($aArea['id'], false, [], (int)($aArea['countyid'] ?? 0));
            $this->aSpsConfig = $oSpsConfig->getFeeConfig();
            $this->_iDisplayType = $oSpsConfig->getDisplayType();
            $this->_iIsNewConf = $oSpsConfig->getIsNewConf();

            if (true == $aParams['is_intercept_page']) {
                // 拦截页
                $aConfig = $aConfig['intercept_page'];
                $aPageInfo['content_list'] = $this->_getCalendar(
                    $aConfig,
                    $aArea['id'],
                    (int)($aArea['countyid'] ?? 0),
                    $aParams['lang']
                );

                $aPageInfo = $this->_getInterceptPageInfo($aConfig, $aPageInfo, $aParams, $aArea);
            } else {
                // 宣教页处理
                $sKeyPublicity = 'driver_publicity';
                if (true == $aParams['is_passenger']) {
                    $sKeyPublicity = 'passenger_publicity';
                }
                $aConfig = $aConfig[$sKeyPublicity];

                $aPageInfo = $this->_getPublicityPageInfo($aConfig, $aPageInfo, $aArea,  $aParams['lang']);
            }

            $aResponseInfo['data'] = $aPageInfo;
        } catch (\Exception $e) {
            $aContext      = ['dltag' => '_com_request_out_failure', 'err_msg' => $this->aErrnoMsg];
            $aResponseInfo = ExceptionHandler::getInstance()->handleException($e, $aContext);
        }

        $this->sendTextJson($aResponseInfo);
    }

    /**
     * 验证参数.
     * @param string $sCityId 城市
     * @param float  $fLng    坐标
     * @param float  $fLat    坐标
     * @return array
     */
    public function getArea($sCityId, $fLng, $fLat, $slang): array {
        if (!empty($fLng) && !empty($fLat)) {
            try {
                return MapHelper::getAreaInfoByLocAndLang($fLng, $fLat, $slang);
            } catch (Exception $e) {
                NuwaLog::warning(
                    Msg::formatArray(
                        Code::E_COMMON_PARAM_ERROR,
                        array(
                            'fLng' => $fLng,
                            'fLat' => $fLat,
                            'mst'  => 'error getAreaInfoByLoc',
                        )
                    )
                );
            }
        }

        if (!empty($sCityId)) {
            return MapHelper::getAreaInfoByAreaId($sCityId);
        }

        return [];
    }

    /**
     * @param array  $aConfig   文案配置
     * @param int    $iCityId   城市
     * @param int    $iCountyId 区县
     * @param string $sLang     lang
     * @return array|void
     */
    private function _getCalendar($aConfig, $iCityId, $iCountyId, $sLang) {
        $aCalendar = array();

        // http://ab.intra.xiaojukeji.com/launch/1047/normal/137713?activeKey=detail
        /*
        $oToggle = \Xiaoju\Apollo\Apollo::getInstance()->featureToggle(
            'gs_red_packet_page',
            [
                'key'  => $iCityId,
                'city' => $iCityId,
            ]
        );
        if ($oToggle->allow()) {
            switch ($oToggle->getGroupName()) {
                case 'close':
                    // 能进来这个,说明该城市被屏蔽,需要关城
                    return $aCalendar;
                case 'data_error':
                    // 下游数据错误, 返回默认日历
                    return $aConfig['default_content_list'];
                default:
                    break;
            }
        }
        */

        $aData = $this->aSpsConfig;
        if (!empty($aData) && isset($aData['condition'])) {
            return $this->_getHolidayFeeCalendar($aData['condition'],$aConfig, $sLang);
        } elseif (!empty($aData) && isset($aData['conf_list'])
            && sizeof($aData['conf_list']) > 0
            && !empty($aData['conf_list'][0])
        ) {
            return $this->_getHolidayFeeCalendar($aData['conf_list'][0],$aConfig, $sLang);
        }

        return $aCalendar;
    }

    /**
     * 获取日期
     * @param string $sBeginTime 开始
     * @param string $sEndTime   结束
     * @return false|string
     */
    protected static function _getTimeStr($sBeginTime, $sEndTime) {
        return date('H:i', strtotime($sBeginTime)).'-'.date('H:i', strtotime($sEndTime));
    }

    /**
     * @param array $aConfig   文案
     * @param array $aPageInfo 页面数据
     * @param array $aParams   参数
     * @param array $aArea     地区
     * @return array
     */
    private function _getInterceptPageInfo($aConfig, $aPageInfo, $aParams, $aArea): array {
        $aPageInfo['page_title']  = $aConfig['page_title'];
        $aPageInfo['content_img'] = $aConfig['content_img'];
        $aPageInfo['content']     = $aConfig['content_intercept'];

        $aPageInfo['content_title'] = Language::replaceTag(
            $aConfig['content_title'],
            [
                'area' => ($aArea['name'] ?? ''),
            ]
        );

        if (!empty($aParams['red_packet_amount']) && $aParams['red_packet_amount'] > 0) {
            // 有红包金额参数
            $sValue = Language::replaceTag(
                $aConfig['value'],
                [
                    'num' => $aParams['red_packet_amount'],
                ]
            );
            $aPageInfo['content_title'] = Language::replaceTag(
                $aConfig['content_title_intercept'],
                ['value' => $sValue,]
            );
        } else {
            $aPageInfo['content_title'] = Language::replaceTag(
                $aConfig['content_title_intercept'],
                ['value' => '',]
            );
        }

        $aPageInfo['button_text']  = $aConfig['button_text'];
        $aPageInfo['car_desc']     = $aConfig['car_desc'];
        $aPageInfo['content_down'] = $aConfig['content_down'];
        $aPageInfo['extra_tip']    = $aConfig['extra_tip'];

        if ($this->_iIsNewConf) {
            $aPageInfo['fee_table'] = $this->_getFeeTableNew($aParams['lang'], $aArea['id']);
            return $aPageInfo;
        }
        $aPageInfo['fee_table'] = $this->_getFeeTable($aParams['lang']);
        return $aPageInfo; //有感叹号
    }

    /**
     * 宣教页
     * @param array  $aConfig   文案
     * @param array  $aPageInfo 页面数据
     * @param array  $aArea     地区
     * @param string $sLang     $sLang
     * @return array
     */
    private function _getPublicityPageInfo($aConfig, array $aPageInfo, $aArea, $sLang): array {
        // car_desc和日历的展示靠开关控制
        $iCounty = (int)($aArea['countyid'] ?? 0);
        if ($this->_isShowCalendar($aArea['id'], $iCounty)) {
            $aPageInfo['content_list'] = $this->_getCalendar($aConfig, $aArea['id'], $iCounty, $sLang);
            if (empty($aPageInfo['content_list'])) {
                // 日历空 -> 返回该城市未开通
                $aPageInfo['content_img']   = $aConfig['top_img_url_intercept'];
                $aPageInfo['content_title'] = $aConfig['no_open_title'];
                return $aPageInfo;
            }

            $aPageInfo['car_desc'] = $aConfig['car_desc'];
        }

        $aPageInfo['page_title']  = $aConfig['page_title'];
        $aPageInfo['content_img'] = $aConfig['content_img'];

        list($cityName, $countyName) = $this->__getCityNameAndCountyName($iCounty, $aArea);

        // 替换成区县名称
        if (!empty($this->aSpsConfig) && !empty($aArea['county_name']) && !empty($this->aSpsConfig['conf_list'])) {
            foreach ($this->aSpsConfig['conf_list'] as $aConf) {
                // 命中老版本(财神配置下发)
                if (!$this->_iIsNewConf && isset($aConf['county_conf']) && !empty($aConf['county_conf'])) {
                    $countyName = $aArea['county_name'];
                    break;
                }
                // 命中新版本(上传配置下发)
                if ($this->_iIsNewConf && in_array(strval($iCounty), $aConf['county_list'])) {
                    $countyName = $aArea['county_name'];
                    break;
                }
            }
        }

        $aParam   = ['city_id' => $aArea['id']];
        $bIsRangeDifferential = $this->_iIsNewConf == 1 && $this->_iDisplayType == 1;
        $aContentConfig = SpringRedPacketFeeConfig::getSpringRedPacketConfig($aParam, $sLang, $bIsRangeDifferential);
        $sFeeName = $aContentConfig['title'] ?? '';
        if (!empty($sFeeName) && !empty($aConfig['content_title_new'])) {
            $aConfig['content_title'] = sprintf($aConfig['content_title_new'], $sFeeName);
        }

        $aPageInfo['content_title'] = Language::replaceTag(
            $aConfig['content_title'],
            [
                'area'   => $cityName,
                'county' => $countyName,
            ]
        );

        if (Language::EN_US == $sLang && !empty($cityName) && !empty($countyName)) {
            $aPageInfo['content_title'] = Language::replaceTag(
                $aConfig['content_title'],
                [
                    'area'   => $cityName,
                    'county' => $countyName.', ',
                ]
            );
        }

        $aPageInfo['content']      = $aConfig['content'];
        $aPageInfo['content_down'] = $aConfig['content_down'];
        $aPageInfo['extra_tip']    = $aConfig['extra_tip'];

        // 乘客宣教页支持分城市配置文案
        !empty($aContentConfig['content']) && $aPageInfo['content'] = json_decode($aContentConfig['content'], true);
        !empty($aContentConfig['content_below_table']) && $aPageInfo['content_down'] = json_decode($aContentConfig['content_below_table']);
        !empty($aContentConfig['disclaimer']) && $aPageInfo['extra_tip'] = $aContentConfig['disclaimer'];

        // 春节服务费表格
        // 命中区分里程定价
        if ($this->_iIsNewConf) {
            $aPageInfo['fee_table'] = $this->_getFeeTableNew($sLang, $aArea['id']);
            return $aPageInfo;
        }
        $aPageInfo['fee_table'] = $this->_getFeeTable($sLang);
        return $aPageInfo; //没有感叹号
    }

    /**
     * 是否展示日历
     * http://ab.intra.xiaojukeji.com/launch/1047/normal/148953?activeKey=detail
     * @param int $iCityId 城市
     * @return bool
     */
    private function _isShowCalendar($iCityId, $iCounty) {
        return true;
        /*
        $oToggle = \Xiaoju\Apollo\Apollo::getInstance()->featureToggle(
            'gs_fee_page_show_calendar',
            [
                'key'    => $iCityId,
                'city'   => $iCityId,
                'county' => $iCounty,
            ]
        );
        return $oToggle->allow();
        */
    }

    /**
     * @param  array  $aConditionList $aConditionList
     * @param  array  $aConfig        $aConfig
     * @param  string $sLang          $sLang
     * @return array
     */
    private function _getHolidayFeeCalendar($aConditionList, $aConfig, $sLang) {

        $oCalendar = array();

        usort(
            $aConditionList,
            function ($a, $b): int {
                if ($a['start_date'] > $b['start_date']) {
                    return 1;
                } else {
                    return -1;
                }

            }
        );

        foreach ($aConditionList as $aData) {
            $sStartDate = CalendarConfig::getInstance()->getDateStr($aData['start_date'], $sLang);
            $sEndDate   = CalendarConfig::getInstance()->getDateStr($aData['end_date'], $sLang);
            if (empty($sStartDate) || empty($sEndDate)) {
                continue;
            }

            $aDayCalendar = [];
            if ($sStartDate == $sEndDate) {
                // 一天
                $aDayCalendar['date'] = $sStartDate;
            } else {
                $aDayCalendar['date'] = $sStartDate.' - '.$sEndDate;
            }

            foreach ($aData['date_range'] as $aDataRange) {
                $sTime  = $this->_getTimeStr($aDataRange['begin_time'], $aDataRange['end_time']);
                $sPrice = Language::replaceTag(
                    $aConfig['price_template'],
                    [
                        'num' => NumberHelper::numberFormatDisplay($aDataRange['fee_value']),
                    ]
                );
                $aDayCalendar['list'][] = [
                    'time'  => $sTime,
                    'price' => $sPrice,
                ];
            }

            $oCalendar[] = $aDayCalendar;
        }

        return $oCalendar;
    }


    /**
     * @param int   $iCounty $iCounty
     * @param array $aArea   $iCounty
     * @return array
     */
    private function __getCityNameAndCountyName($iCounty, $aArea) {
        $cityName         = $aArea['name'] ?? '';
        $countyName       = '';
        $countyNameToggle = Apollo::getInstance()->featureToggle(
            'festival_fee_publicity_county_out',
            [
                'key'    => $iCounty,
                'city'   => $aArea['id'],
                'county' => $iCounty,
            ]
        );
        if (!$countyNameToggle->allow()) {
            return [$cityName, $countyName];
        }

        $countyName = $aArea['county_name'] ?? '';
        if ('1' == $countyNameToggle->getParameter('disable_city', '0') && !empty($countyName)) {
            $cityName = '';
        }

        return [$cityName, $countyName];
    }

    private function _getFeeTable($sLang) {
        if (empty($this->aSpsConfig)) {
            return [];
        }

        $aDcmpConfig        = Language::getDecodedTextFromDcmp(
            'config_text-red_packet_fee_table_category_v2', $sLang
        );
        $aSpecialProductIds   = $aDcmpConfig['special_product_ids'];
        $aPremierProductIds   = $aDcmpConfig['premier_product_ids'];
        $aSceneToName         = $aDcmpConfig['scene_to_name'];
        $aProductNameMap      = $aDcmpConfig['product_id_name'];
        $sDefaultName         = $aProductNameMap['0'];
        $sAllName             = $aProductNameMap['-1'];
        $aHeaders             = $aDcmpConfig['headers'];
        $sPriceUint           = $aDcmpConfig['price_unit'];
        $aTable               = [];
        $aTable['header']     = $aHeaders;
        $aTable['categories'] = [];
        //豪华车费项明细
        $bHasSpecial        = false;
        $aSpecialCategories = [];
        //专车费项明细
        $bHasPremier        = false;
        $aPremierCategories = [];
        //其它计价类型
        $sSpecialScene        = '1';
        $bHasSpecialComboType        = false;
        $aSpecialComboTypeCategories = [];
        $sNotSpecialName = $aProductNameMap['-5'];
        $sSpecialProductIdKey = '9';
        $sPremierProductIdKey = '1';
        $sSpecialProductIdKey = '9';
        $sPremierProductIdKey = '1';

        $aConfList = $this->aSpsConfig['conf_list'];
        // 2. 构造category
        foreach ($aConfList as $aConf) {
            $bCurHasSpecialComboType = false;
            $bCurHasSpecial = false;
            $bCurHasPremier = false;
            // 2.1 构造category名称
            $aSceneList = $aConf['scene_list'];
            $iScene     = 0;
            if (count($aSceneList) > 0) {
                $iScene = $aSceneList[0];
            }

            $aProductIds = $aConf['product_list'];
            if (empty($aProductIds)) {
                continue;
            }

            $aNames    = [];
            $aCategory = [];
            // 特殊计价场景
            if (!empty($aSceneToName[strval($iScene)])) {
                $aNames[] = $aSceneToName[strval($iScene)];
                $bCurHasSpecialComboType = true;
                $bHasSpecialComboType    = true;
            } else {
                // 差异化定价product id 全集
                $aAllSPecial = array_merge($aSpecialProductIds, $aPremierProductIds);
                // 包含差异化定价product id 全集之外元素则为非差异化定价 
                if (count(array_diff($aProductIds, $aAllSPecial))) {
                    if (1 == sizeof($aConfList)) {
                        $aNames[] = $sAllName;
                    } else {
                        $aNames[] = $sDefaultName;
                    }
                } else {
                    // 差异化定价
                    // 豪华车差异化定价
                    if (array_intersect($aProductIds, $aSpecialProductIds)) {
                        $bCurHasSpecial = true;
                        $bHasSpecial    = true;
                        $aNames[]       = $aProductNameMap[$sSpecialProductIdKey];
                    }

                    // 专车差异化定价
                    if (array_intersect($aProductIds, $aPremierProductIds)) {
                        $bCurHasPremier = true;
                        $bHasPremier   = true;
                        $aNames[]       = $aProductNameMap[$sPremierProductIdKey];
                    }
                }
            }

            $sep = '、';
            if (Language::EN_US == $sLang) {
                $sep = '/';
            }
            $aCategory['name'] = implode($sep, $aNames);

            // 2.2 相同category中，日期范围排序
            $aConds = $aConf['condition'];

            // 2.3 相同日期范围，时间段排序
            $aInfos = [];
            foreach ($aConds as $aCondition) {
                $aInfo          = [];
                $aInfo['date']  = $this->__formatDate($aCondition['start_date']).'-'.$this->__formatDate($aCondition['end_date']);
                $aInfo['items'] = [];
                foreach ($aCondition['date_range'] as $aRange) {
                    $aItem            = [];
                    $aItem['time']    = $aRange['begin_time'].'-'.$aRange['end_time'];
                    $aItem['price']   = $aRange['fee_value'].$sPriceUint;
                    $aInfo['items'][] = $aItem;
                }

                $aInfos[] = $aInfo;
            }

            $aCategory['infos'] = $aInfos;
            if ($bCurHasSpecialComboType) {
                // 如果存在特殊计价类型
                $aSpecialComboTypeCategories[] = $aCategory;
            } elseif ($bCurHasSpecial) {
                //如果存在豪华车
                $aSpecialCategories[] = $aCategory;
            } elseif ($bCurHasPremier) {
                //如果存在专车
                $aPremierCategories[] = $aCategory;
            } else {
                $aTable['categories'][] = $aCategory;
            }
        }

        if ($bHasSpecialComboType) {
            $aTable['categories'] = array_merge($aTable['categories'], $aSpecialComboTypeCategories);
        }
        //将豪华车和专车的费项放在最后
        if (sizeof($aSpecialCategories) > 0) {
            $aTable['categories'][] = $aSpecialCategories[0];
        }
        if (sizeof($aPremierCategories) > 0) {
            $aTable['categories'][] = $aPremierCategories[0];
        }

        //重新更名默认品类
        if ($sDefaultName == $aTable['categories'][0]['name'] &&
                ($bHasPremier || $bHasSpecial || $bHasSpecialComboType)
            ) {
            $aSpecialNames = [];
            // 存在专车差异化定价
            if ($bHasPremier) {
                $aSpecialNames[] = $aProductNameMap['1'];
            }

            // 存在豪华车差异化定价
            if ($bHasSpecial) {
                $aSpecialNames[] = $aProductNameMap['9'];
            }

            // 存在粤港车差异化定价
            if ($bHasSpecialComboType) {
                $aSpecialNames[] = $aSceneToName[$sSpecialScene];
            }

            $sep = '、';
            if (Language::EN_US == $sLang) {
                $sep = '/';
            }
            $sSpecialNames = implode($sep, $aSpecialNames);
            $aTable['categories'][0]['name'] = sprintf($sNotSpecialName, $sSpecialNames);
        }

        return $aTable;
    }

    /**
     * @param string $sLang $sLang
     * @param int    $iArea $iArea
     * @return array
     */
    private function _getFeeTableNew($sLang, $iArea) {
        if (empty($this->aSpsConfig)) {
            return [];
        }

        $aDcmpConfig        = Language::getDecodedTextFromDcmp(
            'config_text-red_packet_fee_table_category_v3', $sLang
        );
        $aEnumToName   = $aDcmpConfig['enum_to_name'];
        $aEnumToCategories = array();
        $sDefaultName = $aEnumToName['0'];
        $sNotSpecialName = $aEnumToName['-2'];
        $sAllName = $aEnumToName['-1'];
        $aAll = $aDcmpConfig['all'];
        $aHeaders             = $aDcmpConfig['headers'];
        $aTable               = [];
        $aTable['header']     = $aHeaders;

        if ($this->_iDisplayType) {
            $aTable['header'] = $aDcmpConfig['headers_mileage_differential']; 
        }

        $aDifferentialCategories = [];
        $aDifferentialCategoryNames = [];
        $aCategories = [];
        $aConfList = $this->aSpsConfig['conf_list'];
        // 2. 构造category
        foreach ($aConfList as $aConf) {
            $aInfos = [];
            $aCategoryList = $aConf['category_list'];
            $aConds = $aConf['category_conf'];
            if (empty($aConds)) {
                continue;
            }
            foreach ($aConds as $aCondition) {
                $aInfo['date']  = $this->__formatDate($aCondition['start_date']).'-'.$this->__formatDate($aCondition['end_date']);
                $aInfo['items'] = $this->__formatPeriodMilePrice($aCondition['period_mile_price'], $aDcmpConfig, $this->_iDisplayType);
                $aInfos[] = $aInfo;
            }
            $aNameList = [];
            $iEnumKey = 0;
            foreach ($aCategoryList as $iCategory) {
                // 特殊品类和默认品类放在一块 按照默认品类处理
                if (in_array(0, $aCategoryList)) {
                    $aNameList[] = $sDefaultName;
                    break;
                }
                $iEnumKey = $iCategory;
                $sName = $this->_getProductName($iCategory, $aEnumToName[strval($iCategory)], $iArea, $sLang);
                $aNameList[] = $sName;
                if ($iCategory != 0 && !in_array($iCategory, $aDifferentialCategories)) {
                    $aDifferentialCategories[] = $iCategory; 
                }
            }
            $aCategory['infos'] = $aInfos;
            $sep = '、';
            if (Language::EN_US == $sLang) {
                $sep = '/';
            }
            $aCategory['name'] = implode($sep, $aNameList);

            $aEnumToCategories[$iEnumKey][] = $aCategory;
        }

        
        ksort($aEnumToCategories);
        usort($aDifferentialCategories, function($a, $b){
            return $a - $b;
        });
        foreach($aEnumToCategories as $aCategory) {
            $aCategories = array_merge($aCategories, $aCategory);
        }

        foreach($aDifferentialCategories as $iCategory) {
            $aDifferentialCategoryNames[] = $this->_getProductName($iCategory, $aEnumToName[strval($iCategory)], $iArea, $sLang);
        }

        $aNormalCategoryNames = [];
        foreach ($aAll as $sEnum => $sName) {
            // 非特殊计价
            if (in_array($sEnum, $aDifferentialCategories)) {
                continue;
            }

            // 非特殊枚举 || 非粤港车
            $iEnum = intval($sEnum);
            if ($iEnum == self::YUE_GANG_ENUM) {
                continue;
            }

            if ($iEnum == self::EXPRESS_ENUM || $iEnum == self::POOL_ENUM) {
                $aNormalCategoryNames[] = $sName;
                continue;
            }

            $aNormalCategoryNames[] = $this->_getProductName($iEnum, $aEnumToName[strval($iEnum)], $iArea, $sLang);
        }

        if(count($aCategories) == 1 && empty($aDifferentialCategoryNames)) {
            $aCategories[0]['name'] = $sAllName;
        }

        //重新更名默认品类
        if ($sDefaultName == $aCategories[0]['name'] && count($aDifferentialCategoryNames) > 0) {
            $sep = '、';
            if (Language::EN_US == $sLang) {
                $sep = '/';
            }

            // 加个灰度兜底
            $oToggle = \Xiaoju\Apollo\Apollo::getInstance()->featureToggle(
                'gs_spring_red_packet_switch',
                [
                    'key'  => $iArea,
                    'city' => $iArea,
                ]
            );

            // 灰度
            if ($oToggle->allow()) {
                $sep = "\n";

                $sNormalNames = implode($sep, $aNormalCategoryNames);
                $aCategories[0]['name'] = $sNormalNames;
            } else {
                $sSpecialNames = implode($sep, $aDifferentialCategoryNames);
                $aCategories[0]['name'] = sprintf($sNotSpecialName, $sSpecialNames);
            }
        }

        if ($this->_iDisplayType) {
            $aTable['mileage_differential_categories'] = $aCategories;
        } else {
            $aTable['categories'] = $aCategories;
        }

        return $aTable;
    }

    /**
     * 将形如 2024-01-12 字符串 格式化为 2024.01.12
     * @param $sRawDate
     * @return string
     */
    private function __formatDate($sRawDate) {
        $sYear  = substr($sRawDate, 0, 4);
        $sMonth = substr($sRawDate, 5, 2);
        $sDate  = substr($sRawDate, 8, 2);

        return $sYear.'.'.$sMonth.'.'.$sDate;
    }

    private function __formatPeriodMilePrice($aPeriodMilePrice, $aDcmpConfig, $iDisplayType) {
        $aRets = [];
        foreach ($aPeriodMilePrice as $oPeriodMilePrice) {
            if (empty($oPeriodMilePrice['mile_price'])) {
                continue;
            }
            foreach($oPeriodMilePrice['period_list'] as $aPeriod) {
                if(!is_array($aPeriod['period']) || count($aPeriod['period']) != 2) {
                    continue;
                }
                $aItem['time'] = $this->__formatTime($aPeriod['period'][0], $aPeriod['period'][1]);
                if ($iDisplayType) {
                    $aItem['mile_price'] = $this->__formatMilePrice($oPeriodMilePrice['mile_price'], $aDcmpConfig);
                } else {
                    $iFeeValue = $oPeriodMilePrice['mile_price'][0]['fee_value']; 
                    $aItem['price'] = $iFeeValue.$aDcmpConfig['price_unit'];
                }
                $aRets[] = $aItem;
            }
        }
        return $aRets;
    }

    private function __formatTime($sStartTime, $sEndTime) {
        return $aRets[] = $sStartTime.'-'.$sEndTime;
    }

    private function __formatMilePrice($aMilePrice, $aDcmpConfig) {
        $aRets = [];
        foreach ($aMilePrice as $oMilePrice) {
            $aInfo['mile'] = $this->formatMile($oMilePrice['start_mile'], $oMilePrice['end_mile'], $aDcmpConfig);
            if (sizeof($aMilePrice) == 1) {
                $aInfo['mile'] = $aDcmpConfig['all_range_mile'];
            }
            $aInfo['price'] = $oMilePrice['fee_value'].$aDcmpConfig['price_unit'];
            $aRets[] = $aInfo;
        }
        return $aRets;
    }

    private function formatMile($iStartMile, $iEndMile, $aDcmpConfig) {
        if ($iStartMile == 0) {
            return sprintf($aDcmpConfig['left_range_mile'], $iEndMile);
        }
        if ($iEndMile == 10000) {
            return sprintf($aDcmpConfig['right_range_mile'], $iStartMile);
        }

        return sprintf($aDcmpConfig['between_range_mile'], $iStartMile, $iEndMile);
    }

    /**
     * @param int    $iNameEnum $iNameEnum
     * @param string $sName     $sName
     * @param int    $iArea     $iArea
     * @param string $sLang     $sName
     * @return string
     */
    private function _getProductName($iNameEnum, $sName, $iArea, $sLang) {
        $iNameEnumForButian = 3;
        if ($iNameEnum != $iNameEnumForButian) {
            return $sName;
        }
        $sProductName = v3Util::getCarTitleByProductCategoryCity(OrderEstimatePcId::EstimatePcIdFastTaxi, $iArea, $sLang);
        if (!empty($sProductName)) {
            return $sProductName;
        }
        return $sName;
    }
}

