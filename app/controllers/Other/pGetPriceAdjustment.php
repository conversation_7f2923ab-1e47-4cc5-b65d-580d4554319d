<?php

/**
 * kf调价公示
 * wiki: http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=443121341
 * <AUTHOR> <<EMAIL>>
 * @date 2020/10/14
 */

use BizLib\Utils\Common as UtilsCommon;
use BizLib\ExceptionHandler;
use BizLib\ErrCode\RespCode;
use PreSale\Logics\priceAdjustment\PriceAdjustmentLogic;

/**
 * Class PGetPriceAdjustmentController
 */
class PGetPriceAdjustmentController extends \PreSale\Core\Controller
{
    /**
     * 入口函数
     *
     * @return void
     */
    public function indexAction() {
        //初始化Request对象
        $oPriceAdjustmentLogic = new PriceAdjustmentLogic($_GET);

        try {
            //获取默认返回值
            $aResponseInfo = UtilsCommon::getErrMsg(RespCode::P_SUCCESS, $this->aErrnoMsg);

            //验证参数
            list($iProductId, $aAreaInfo) = $oPriceAdjustmentLogic->verify();

            //获取调价公示
            $aPriceRule            = $oPriceAdjustmentLogic->getPriceAdjustment($iProductId, $aAreaInfo);
            $aResponseInfo['data'] = $aPriceRule;

            if (empty($aResponseInfo['data'])) {
                $aResponseInfo = UtilsCommon::getErrMsg(RespCode::P_ERRNO_NOT_OPEN_SERVICE, $this->aErrnoMsg);
            }
        } catch (\Exception $e) {
            //异常处理上下文
            $aContext = [
                'dltag'   => '_com_request_out_failure',
                'err_msg' => $this->aErrnoMsg,
            ];

            //异常处理并得到返回值
            $aResponseInfo = ExceptionHandler::getInstance()->handleException($e, $aContext);
        }

        //输出返回值
        $this->sendTextJson($aResponseInfo, $oPriceAdjustmentLogic->getCallback());
    }
}

