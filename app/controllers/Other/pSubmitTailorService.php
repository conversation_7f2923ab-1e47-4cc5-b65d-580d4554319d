<?php
/**
 * <AUTHOR> <<EMAIL>>
 */

use BizCommon\Models\Passenger\Passenger;
use BizLib\Utils;
use BizLib\Utils\Common as UtilsCommon;
use Dirpc\SDK\PreSale\SubmitTailorServiceRequest as Request;
use Dirpc\SDK\PreSale\SubmitTailorServiceResponse as Response;
use BizLib\Utils\Product;
use PreSale\Logics\estimatePrice\OptionServiceLogic;
use PreSale\Models\rpc\LuxRpc;
use BizLib\Utils\Language;
use BizLib\Config as NuwaConfig;

/**
 * Class PSubmitTailorServiceController
 */
class PSubmitTailorServiceController extends \PreSale\Core\Controller
{

    /**
     * @return void
     */
    public function indexAction() {
        \Nuwa\Protobuf\Internal\Message::setEmitDefaults(true);
        //参数初始化
        $oRequest       = Utils\Request::getInstance();
        $oSubmitRequest = new Request();
        $oSubmitRequest->mergeFromJsonArray($oRequest->post());

        $iBusinessId = $oSubmitRequest->getBusinessId();

        $aPassengerInfo = Passenger::getInstance()->getPassengerByToken($oSubmitRequest->getToken());
        $sOptions = $oSubmitRequest->getOptions();
        $aParams = [
            'product_id'  => Product::getProductIdByBusinessId($iBusinessId),
            'options'     => $sOptions,
            'business_id' => $iBusinessId,
            'pid'         => $aPassengerInfo['pid'],
            'uid'         => $aPassengerInfo['uid'],
            'source'      => $oSubmitRequest->getSource(),
            'lang'        => Language::getLanguage(),
            'from_kind'   => OptionServiceLogic::FORM_KIND_SUBMIT,
        ];
        //格式化输出
        $oResponse = new Response();
        // 过滤请求入参中可能存在的备注敏感词
        $aParams = self::_verifySensitiveWord($sOptions, $aParams);

        if (Product::COMMON_PRODUCT_ID_FIRST_CLASS_CAR == $iBusinessId) {
            LuxRpc::getInstance()->updateUserPreferencesInfo($aParams);
        } elseif (Product::COMMON_PRODUCT_ID_HK_TAXI == $iBusinessId) {
            OptionServiceLogic::submitHkService($aParams);
        } else {
            $aParams['require_level'] = $oSubmitRequest->getRequireLevel();
            $aParams['oid']           = $oSubmitRequest->getOid();

            OptionServiceLogic::submitServiceV2($aParams);
        }

        $aRenderInfo = UtilsCommon::getErrMsg(GLOBAL_SUCCESS);
        $oResponse->mergeFromJsonArray($aRenderInfo);
        $aResponseInfo = $oResponse->serializeToJsonArray();
        $this->sendJson($aResponseInfo, (string)($this->getRequest()->getQuery('callback', false)));
    }

    /**
     * @param string                                         $sOptions  额外参数
     * @param array                                          $aParams   请求入参数
     * @return array $aParams
     */
    private function _verifySensitiveWord($sOptions, $aParams) : array {
        $oOptions = json_decode($sOptions, true);
        // 备注敏感词机审检测
        $aDetectParams['data'] = [
            'content'      => $oOptions['remark'],
            'content_type' => REMARK_DETECT_TYPE,
            'pid'          => $aParams['pid'],
        ];
        $bIsSensitiveWord = UtilsCommon::isSensitiveWordInLuxuryPreferPageDirpc($aDetectParams);
        if ($bIsSensitiveWord) {
            $aSensitiveWordTips = NuwaConfig::text('config_passenger', 'luxury_optional_page_sensitive_word_tips');
            $oOptions['remark'] = $aSensitiveWordTips['remark'];
            $sOptions = json_encode($oOptions);
            $aParams['options'] = $sOptions;
        }
        return $aParams;
    }
}
