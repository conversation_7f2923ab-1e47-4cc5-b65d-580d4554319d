<?php

use BizLib\Utils;
use BizLib\Utils\MapHelper;
use BizCommon\Models\Passenger\Passenger;
use Dirpc\SDK\PreSale\LuxMultiEstimatePriceRequest as Request;
use Dirpc\SDK\PreSale\TailorServiceResponse as Response;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\estimatePrice\OptionServiceLogic;
use PreSale\Logics\estimatePriceV2\OptionServiceLogicV2;
use BizLib\Config as NuwaConfig;
use BizLib\ExceptionHandler;
use BizLib\Utils\Product;
use Disf\SPL\Trace;
use PreSale\Logics\estimatePriceV2\PersonalizedServiceLogicV2;
use PreSale\Logics\luxEstimatePrice\multiRequest\PriceLogic;
use PreSale\Logics\LuxEstimatePrice\multiRequest\ProductList;
use PreSale\Logics\luxEstimatePrice\multiResponse\MainRender;
use PreSale\Logics\luxuryPreferInfo\LuxuryPreferInfoLogic;

/**
 * Class PGetTailorServiceV2Controller
 */
class PGetTailorServiceV2Controller extends \PreSale\Core\Controller
{
    // 专车定制迭代页面主题
    const THEME_OF_SPECIAL_CUSTOM_PAGE = 1;
    // 豪华车定制迭代页面主题
    const THEME_OF_LUXURY_CUSTOM_PAGE = 2;
    // 调用源为v2接口
    const SOURCE_OF_PREFER_REQUEST = 2;
    //hundun 切量开关
    const APOLLO_TOGGLE_HUNDUN_CHEKCOUT_CONTROLLER = 'tailor_service_check_to_hundun';

    /**
     * indexAction
     * @return void
     */
    public function indexAction() {
        try {
            $oRequest         = Utils\Request::getInstance();
            $oEstimateRequest = new Request();
            $oEstimateRequest->mergeFromJsonArray($oRequest->get());
            $aPassengerInfo = Passenger::getInstance()->getPassengerByToken($oEstimateRequest->getToken());
            $aAreaInfo      = MapHelper::getAreaInfoByLoc((float)($oEstimateRequest->getFromLng()), (float)($oEstimateRequest->getFromLat()));
            // lat、lng是定位的经纬度
            if (empty($aAreaInfo['id'])) {
                $aAreaInfo = MapHelper::getAreaInfoByLoc((float)($oEstimateRequest->getLng()), (float)($oEstimateRequest->getLat()));
            }
            $iBusinessId  = $oEstimateRequest->getBusinessId();
            $bIsLuxury    = Product::COMMON_PRODUCT_ID_FIRST_CLASS_CAR == $iBusinessId;
            $sLang        = $oEstimateRequest->getLang();
            $iPid         = $aPassengerInfo['pid'];
            $iAreaId      = $aAreaInfo['id'];
            $aParams = [
                'app_version'   => $oEstimateRequest->getAppversion(),
                'product_id'    => Product::getProductIdByBusinessId($iBusinessId),
                'lang'          => $sLang,
                'business_id'   => $iBusinessId,
                'pid'           => $iPid,
                'uid'           => $aPassengerInfo['uid'],
                'phone'         => $aPassengerInfo['phone'],
                'oid'           => $oEstimateRequest->getOid(),
                'require_level' => $oEstimateRequest->getRequireLevel(),
                'access_key_id' => $oEstimateRequest->getAccessKeyId(),
                'area'          => $iAreaId ?? 0,
            ];

            $oCustomServiceLogic = PersonalizedServiceLogicV2::getInstance();
            // 如果是豪华车业务线，需要定制处理一些业务
            if ($bIsLuxury) {
                //构建请求数据
                $oProductList = new ProductList($oEstimateRequest);
                $aProductList = $oProductList->buildProductList();

                if (!empty($aProductList)) {
                    //获取账单、支付方式、优惠券、乘客运营活动券数据
                    $oPriceLogic        = new PriceLogic();
                    $aResponseParamsNew = $oPriceLogic->getMultiResponse($aProductList);
                }
                //获取用户偏好设置选项
                $oLuxuryPreferLogic = new LuxuryPreferInfoLogic();
                $aRenderInfo  = $oLuxuryPreferLogic->getLuxuryPreferResponse($aProductList);
                // 渲染对端数据之前，标识当前调用的是v2的偏好设置页面接口
                $aRenderInfo['prefer_request_source'] = self::SOURCE_OF_PREFER_REQUEST;
                // 渲染对端数据
                $aRenderInfo = MainRender::getInstance($aResponseParamsNew, $aRenderInfo)->multiExecute();
                // 设置昵称相关的文案
                $aRenderInfo = $oCustomServiceLogic->setNickNameText($aRenderInfo);
                // 设置更多服务的相关数据
                $aRenderInfo = $oCustomServiceLogic->setMoreServiceText($aRenderInfo);
                $aRenderInfo = $oCustomServiceLogic->setCommonExpressions($aRenderInfo, $iPid, $sLang, $iAreaId);
                // 设置豪华车定制页面的主题
                $aRenderInfo['data']['theme'] = self::THEME_OF_LUXURY_CUSTOM_PAGE;
                // 设置豪华车定制页面服务反馈相关的文案
                $aRenderInfo = $oCustomServiceLogic->setServiceFeedbackText($aRenderInfo);
                // 豪华车接入彩蛋
                $aParams['oid'] = $oEstimateRequest->getOid();
                // 小程序之类的第一次调用豪华车偏好设置页时，不会传require_level，所以加上默认值1000-任意豪华车
                $aParams['require_level'] = ($oEstimateRequest->getRequireLevel()) ?: 1000;
                $aParams['area']          = $aAreaInfo['id'] ?? 0;
                $aParams['access_key_id'] = ($oEstimateRequest->getAccessKeyId()) ?: 0;
                $aRenderInfo = OptionServiceLogic::setActivityData($aRenderInfo, $aParams);
            } else {
                $aRenderInfo = OptionServiceLogicV2::getService($aParams);
                // 设置专车定制页面的主题
                $aRenderInfo['data']['theme'] = self::THEME_OF_SPECIAL_CUSTOM_PAGE;
            }

            if (empty($oEstimateRequest->getOid())) {
                $sCustomFeatures     = $oEstimateRequest->getCustomFeature() ?? null;
                $aCustomFeatures     = json_decode($sCustomFeatures, true);

                // 偏好页切量开关
                $oApollo          = new Apollo();
                $bIsCheckToHundun = $oApollo->featureToggle(
                    self::APOLLO_TOGGLE_HUNDUN_CHEKCOUT_CONTROLLER,
                    [
                        'city'   => $aAreaInfo['id'],
                        'phone'  => $aPassengerInfo['phone'],
                        'caller' => 'pre_sale',
                    ]
                )->allow();

                if ($bIsCheckToHundun) {
                    $oCustomServiceLogic->loadCustomServiceformHundun($oEstimateRequest, $aPassengerInfo, $aAreaInfo);
                    $aCustomInfo = $oCustomServiceLogic->buildNewCustomServiceWithHundun(
                        $aCustomFeatures,
                        $oEstimateRequest,
                        $aPassengerInfo,
                        $aAreaInfo,
                        $bIsLuxury
                    );

                    if (!empty($aCustomInfo)) {
                        $aRenderInfo['data'] = array_merge($aCustomInfo, $aRenderInfo['data']);
                    }
                } else { //切量之后删掉
                    $oCustomServiceLogic->loadCustomServiceSecondaryPage($oEstimateRequest, $aPassengerInfo, $aAreaInfo);
                    $aCustomInfo = $oCustomServiceLogic->buildNewCustomService(
                        $aCustomFeatures,
                        $oEstimateRequest,
                        $aPassengerInfo,
                        $aAreaInfo,
                        $bIsLuxury
                    );

                    if (!empty($aCustomInfo)) {
                        $aRenderInfo['data'] = array_merge($aCustomInfo, $aRenderInfo['data']);
                    }
                }
            }

            $aRenderInfo['data']['estimate_trace_id'] = Trace::traceId();
            $oResponse = new Response();
            $oResponse->mergeFromJsonArray($aRenderInfo);
            $aResponseInfo = $oResponse->serializeToJsonArray();
        } catch (Exception $e) {
            $aErrMsg       = NuwaConfig::text('errno', 'pEstimatePrice_error_msg');
            $oHandler      = ExceptionHandler::getInstance();
            $aResponseInfo = $oHandler->handleException($e, ['err_msg' => $aErrMsg,]);
            $this->sendJson($aResponseInfo, (string)($this->getRequest()->getQuery('callback', false)));
            return;
        }

        $this->sendJson($aResponseInfo, (string)($this->getRequest()->getQuery('callback', false)));
        fastcgi_finish_request();
    }
}
