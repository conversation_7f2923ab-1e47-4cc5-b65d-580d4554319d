<?php
/**
 * 获取特定场景下开通产品线
 *
 * <AUTHOR>
 * @date 2018/12/13
 */

use BizLib\Utils\Request;
use PreSale\Core\BaseController;
use PreSale\Logics\passenger\POlderModeProductLogic;
use BizLib\Exception\InvalidArgumentException;
use BizLib\ErrCode;

class PGetSceneProductsController extends BaseController
{
    public function checkParams() {
        //参数处理
        $oRequest         = Request::getInstance();
        $aParams['scene'] = strtolower($oRequest->getStr('scene'));

        return $aParams;
    }

    public function execute(array $aParams) {
        if ('older_mode' == $aParams['scene']) {
            $oPOlderModeProductLogic = POlderModeProductLogic::getInstance();
            $aProducts = $oPOlderModeProductLogic->execute();
            return $aProducts;
        }

        throw new InvalidArgumentException(
            ErrCode\Code::E_COMMON_PARAM_INVALID_VALUE,
            array('scene' => $aParams['scene'])
        );
    }
}
