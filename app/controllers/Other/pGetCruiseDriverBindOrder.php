<?php
/**
 * Created by PhpStorm.
 * <AUTHOR> <<EMAIL>>
 * Date: 2021/3/24
 * Time: 2:22 下午
 */
use BizLib\Exception\ExceptionWithResp;
use BizLib\ErrCode\RespCode;
use BizLib\Error\ExceptionHandler;
use PreSale\Logics\order\CruiseDriverBindOrderLogic;
use BizLib\Utils\Common;
use BizLib\Constants\Horae;

/**
 * Class PGetCruiseDriverBindOrderController
 */
class PGetCruiseDriverBindOrderController extends \PreSale\Core\Controller
{
    public $aParams = [];

    /**
     * 初始化
     * @return void
     */
    public function init() {
        parent::init();
    }

    /**
     * action
     * @return void
     */
    public function indexAction() {
        try {
            $aResponse = Common::getErrMsg(GLOBAL_SUCCESS);
            $this->_checkParams();
            $oCruiseDriverBindOrderLogic = CruiseDriverBindOrderLogic::getInstance();
            $oCruiseDriverBindOrderLogic->loadInfos($this->aParams);
            $oCruiseDriverBindOrderLogic->getBindOrder();
            $oCruiseDriverBindOrderLogic->updateBindOrder();
            $aResponse['data'] = $oCruiseDriverBindOrderLogic->getResponseParam();
        } catch (\Exception $e) {
            $sMessage  = $e->getMessage();
            $aResponse = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg,]);
        }

        $this->sendJson($aResponse);

    }

    /**
     * 参数校验
     * @return void
     * @throws ExceptionWithResp ExceptionWithResp.
     */
    private function _checkParams() {
        $this->aParams['token']     = $this->oRequest->getStr('token');
        $this->aParams['driver_id'] = $this->oRequest->getStr('driver_id');
        if (empty($this->aParams['driver_id']) ||empty($this->aParams['token'])) {
            throw new ExceptionWithResp(
                RespCode::P_PARAMS_ERROR,
                (string)RespCode::P_PARAMS_ERROR,
                '',
                'errmsg:param error! token:' . $this->aParams['token'] . 'driver_id:' .$this->aParams['driver_id']
            );
        }
    }
}
