<?php
/***************************************************************************
 *
 * Copyright (c) 2018 xiaojukeji.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file pAnycarEstimate.php
 *
 * <AUTHOR>
 * @date 2018/7/27
 * @brief
 *  等待应答出口，提供Anycar价格预估的接口.
 *
 **/
use PreSale\Core\BaseController;
use BizLib\Utils\Request;
use BizCommon\Logics\PassengerBaseLogic;
use PreSale\Logics\anycar\AnycarEstimateLogic;
use PreSale\Logics\order\AnyCarOrderLogic;

class pAnycarEstimateController extends BaseController
{
    /**
     * @var AnycarEstimateLogic Logic
     */
    private $_oAnycarEstimateLogic = null;

    private $_aOrderInfo = [];

    public function checkParams() {
        $oRequest            = Request::getInstance();
        $aParams['token']    = $oRequest->getStr('token');
        $aParams['order_id'] = $oRequest->getStr('order_id');
        $aParams['lang']     = $oRequest->getStr('lang');
        $aParams['carpool_seat_num']      = $oRequest->getInt('carpool_seat_num', 1);
        $aParams['multi_require_product'] = $oRequest->getStr('multi_require_product');
        $aParams['app_version']           = $oRequest->getStr('appversion');
        $aParams['data_type']     = $oRequest->getStr('datatype');
        $aParams['channel']       = $oRequest->getStr('channel');
        $aParams['v6_version']    = $oRequest->getstr('v6_version');
        $aParams['access_key_id'] = $oRequest->getstr('access_key_id');

        return $aParams;
    }

    /**
     * 1. 获取预估报价单
     * 2. 请求预估接口 anycar
     * 3. 请求athena获取排名数据.
     **/
    public function execute(array $aParams) {
        $aOrderDetailParams = PassengerBaseLogic::getInstance()->getParams($aParams);
        $this->_aOrderInfo  = $aOrderDetailParams['order_info'];
        if (empty($this->_aOrderInfo['app_version'])) {
            $this->_aOrderInfo['app_version'] = $aParams['app_version'];
        }

        if (empty($this->_aOrderInfo['v6_version'])) {
            $this->_aOrderInfo['v6_version'] = $aParams['v6_version'];
        }

        if (empty($this->_aOrderInfo['access_key_id'])) {
            $this->_aOrderInfo['access_key_id'] = $aParams['access_key_id'];
        }

        AnyCarOrderLogic::getInstance()->reSortAnycarProductRank($this->_aOrderInfo['area'], $this->_aOrderInfo['pid'], $this->_aOrderInfo['phone']);
        $this->_oAnycarEstimateLogic = new AnycarEstimateLogic();

        return $this->_oAnycarEstimateLogic->getEstimatePriceInfo($this->_aOrderInfo, $aOrderDetailParams, $aParams);
    }

    /**
     *  public 日志.
     */
    protected function _processLog() {
        $this->_oAnycarEstimateLogic->writePublicLog($this->_aOrderInfo);

        $this->_aRequestParam['pid'] = $this->_aOrderInfo['passenger_id'];
        $this->_oAnycarEstimateLogic->recordMQMessage($this->_aRequestParam, $this->_aResult);
    }
}
