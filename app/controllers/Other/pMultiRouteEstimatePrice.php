<?php

use BizLib\Config as NuwaConfig;
use BizLib\Utils\PublicLog;
use BizLib\Log as NuwaLog;
/*
 * 根据乘客输入出发地、目的地同时对拼车、非拼车和优享进行批量预估和价格展示
 * 目前调用端:滴滴乘客端、微信滴滴出行webApp入口、支付宝滴滴出行webApp入口、Uber中国乘客端、企业级网页和企业级乘客端、OpenApi.
 *
 * <AUTHOR>
 * @date: 17/09/16
 */

use BizLib\Utils\Product;
use BizLib\Utils\UtilHelper;
use BizLib\Constants\Dos;
use PreSale\Logics\estimatePrice\ParamsLogic;
use PreSale\Logics\estimatePrice\response\ResponseLogic;
use PreSale\Logics\estimatePrice\PriceLogic;
use PreSale\Infrastructure\Repository\Rpc\OrderDosRepository;
use BizLib\ExceptionHandler;
use BizCommon\Models\Passenger\Passenger;
use PreSale\Models\Business\Business;

/**
 * Class PMultiRouteEstimatePrice.
 */
class PMultiRouteEstimatePriceController extends \PreSale\Core\Controller
{
    /**
     * @var bool
     */
    private $_aResponseExtraInfo = [];

    private $_aOneConfData = [];

    private $_aEstimatePriceParam = [];

    private $_aPassengerInfo = [];

    const P_ERRNO_PARAMS_ERROR = 10631;
    const P_ERRNO_ORDER_ERROR  = 1072;

    public function indexAction() {
        try {
            //参数处理实例
            $oParams = ParamsLogic::getInstance();
            $aParams = (array) $this->getRequest()->getQuery(null, false, true);
            $oParams->aIsMultiRoute = true;
            $this->_checkParams();
            $aOrderInfo = $this->_getOrderInfo($aParams);
            $this->_buildPassengerInfo($aOrderInfo, $aParams);

            $this->_checkOrder($aOrderInfo, $this->_aPassengerInfo);

            $this->_buildOneConfData($aOrderInfo, $aParams);

            $this->_aEstimatePriceParam = $this->_buildEstimatePriceParams($aOrderInfo, $aParams);
            $oPriceLogic = PriceLogic::getInstance($this->_aEstimatePriceParam);
            $oPriceLogic->aIsMultiRoute = true;

            $oParams->setOneConf($this->_aOneConfData);

            //获取账单、支付方式、优惠券、乘客运营活动券数据
            $aResponseParams = $oPriceLogic->getMultiResponse();

            //对返回数据进行格式化
            $aResponseInfo = ResponseLogic::getInstance($aResponseParams, $this->_aOneConfData, $this->_aResponseExtraInfo, (array) $this->getRequest()->getQuery(null, false, true))->multiExecute();
            if (!empty($aResponseInfo['data']['estimate_data'])) {
                foreach ($aResponseInfo['data']['estimate_data'] as $aResponse) {
                    $this->_writePublicLogForMulti($aResponse, $aOrderInfo->getOrderInfo(), $aParams);
                }
            }
        } catch (\Exception $e) {
            $aErrMsg       = NuwaConfig::text('errno', 'errno_msg');
            $oHandler      = ExceptionHandler::getInstance();
            $aResponseInfo = $oHandler->handleException($e, ['err_msg' => $aErrMsg, ]);
        }

        $this->sendJson($aResponseInfo, (string)($this->getRequest()->getQuery('callback', false)));
    }

    private function _checkParams() {
        $aCheckNeedParams = ['oid', 'token', 'app_version', 'client_type', 'platform_type', 'route_ids'];
        foreach ($aCheckNeedParams as $sKey) {
            $sValue = $this->oRequest->getStr($sKey);
            if (empty($sValue)) {
                throw new \Exception('', self::P_ERRNO_PARAMS_ERROR);
            }
        }
    }

    private function _getOrderInfo($aParams) {
        $sOid       = $aParams['oid'];
        $aDecodeId  = UtilHelper::decodeId($sOid);
        $aOrderInfo = OrderDosRepository::getOrder($aDecodeId['oid'], $aDecodeId['district']);
                return $aOrderInfo;
    }


    private function _checkOrder($aOrderInfo, $aPassengerInfo) {
        if (empty($aOrderInfo)) {
            throw new \Exception('', self::P_ERRNO_ORDER_ERROR);
        }

        if ($aPassengerInfo['pid'] != $aOrderInfo->passenger_id) {
            NuwaLog::warning('passenger_info error, order_info :'. json_encode($aOrderInfo) . ' passenger_info : '. json_encode($aPassengerInfo));
            throw new \Exception('', self::P_ERRNO_ORDER_ERROR);
        }

        //仅支持普通快车&优享
        if (!((\BizLib\Utils\CarLevel::DIDI_PUTONG_CAR_LEVEL == $aOrderInfo->require_level
            || \BizLib\Utils\CarLevel::DIDI_YOUXIANG_CAR_LEVEL == $aOrderInfo->require_level)
            && Product::COMMON_PRODUCT_ID_FAST_CAR == $aOrderInfo->business_id
            && Dos::TYPE_COMBO_DEFAULT == $aOrderInfo->combo_type)
        ) {
            NuwaLog::warning('order type is error');
            throw new \Exception('', self::P_ERRNO_ORDER_ERROR);
        }

        return true;
    }

    private function _buildEstimatePriceParams($aOrderInfo, $aParams) {
        $aRouteIDs            = explode(',', $aParams['route_ids']);
        $aEstimatePriceParams = [];
        $aOrderInfo           = $this->_buildOrderInfo($aOrderInfo, $aParams);
        foreach ($aRouteIDs as $sRouteID) {
            $aParams['route_id'] = $sRouteID;
            $aCommonInfo         = $this->_buildCommonInfo($aOrderInfo, $aParams);
            $iProductId          = $aOrderInfo['product_id'];
            $sCarLevel           = $aOrderInfo['require_level'];
            $iComboType          = $aOrderInfo['combo_type'];
            $aOrderInfo['estimate_id'] = UtilHelper::getEstimateId($iProductId, $sCarLevel, $iComboType);

            $aEstimatePriceParam    = [
                'passenger_info' => $this->_aPassengerInfo,
                'common_info'    => $aCommonInfo,
                'order_info'     => $aOrderInfo,
            ];
            $aEstimatePriceParams[] = $aEstimatePriceParam;
        }

        return $aEstimatePriceParams;
    }

    private function _buildOneConfData($aOrderInfo, $aParams) {
        $aOneConf  = [
            'business_id'   => $aOrderInfo->business_id,
            'require_level' => $aOrderInfo->require_level,
            'combo_type'    => $aOrderInfo->combo_type,
            'is_default'    => 0,
        ];
        $aRouteIDs = explode(',', $aParams['route_ids']);
        for ($i = 0; $i < count($aRouteIDs); $i++) {
            $this->_aOneConfData[] = $aOneConf;
        }
    }

    private function _buildPassengerInfo($aOrderInfo, $aParams) {
        //获取乘客信息
        $aPassengerInfo        = Passenger::getInstance()->getPassengerByTokenFromPassport($aParams['token']);
        $this->_aPassengerInfo = [
            'uid'   => $aPassengerInfo['uid'],
            'pid'   => $aPassengerInfo['pid'],
            'phone' => $aOrderInfo->passenger_phone,
            //'channel' =>,
            'role'  => $aPassengerInfo['role'],
        ];
    }

    private function _buildCommonInfo($aOrderInfo, $aParams) {
        $aData = [
            'token'           => $aParams['token'],
            'business_id'     => $aOrderInfo['business_id'],
            'origin_id'       => 1,   //端品牌标识
            'app_version'     => $aParams['app_version'],
            'client_type'     => $aParams['client_type'],
            'imei'            => $aParams['imei'],
            'lang'            => $aParams['lang'],
            'one_conf'        => json_encode($this->_aOneConfData),
            'is_from_webapp'  => false,
            'is_from_b2b'     => false,
            'is_from_guide'   => false,
            'is_from_openapi' => false,
            'platform_type'   => $aParams['platform_type'],
            'route_id'        => (int)($aParams['route_id']),
        ];
        return $aData;
    }

    private function _buildOrderInfo($aOrderInfo, $aParams) {
        $bIsVip = (new Business())->isBusinessUser($aOrderInfo->passenger_phone, 0, $aParams['app_version']);
        $aData  = [
            'product_id'        => $aOrderInfo->product_id,
            'current_lng'       => $aOrderInfo->current_lng,
            'current_lat'       => $aOrderInfo->current_lat,
            'area'              => $aOrderInfo->area,
            'order_type'        => $aOrderInfo->order_type,
            'from_lng'          => $aOrderInfo->starting_lng,
            'from_lat'          => $aOrderInfo->starting_lat,
            'from_poi_id'       => $aOrderInfo->starting_poi_id,
            //'from_poi_type' => '',
            'from_address'      => $aOrderInfo->starting_name,
            //'from_name' => '',
            'to_lng'            => $aOrderInfo->dest_lng,
            'to_lat'            => $aOrderInfo->dest_lat,
            'to_poi_id'         => $aOrderInfo->dest_poi_id,
            'to_address'        => $aOrderInfo->dest_name,
            //'to_name' => '',
            'departure_time'    => strtotime($aOrderInfo->departure_time),
            'starting_name'     => $aOrderInfo->starting_name,
            'dest_name'         => $aOrderInfo->dest_name,
            'combo_type'        => $aOrderInfo->combo_type,
            'require_level'     => $aOrderInfo->require_level,
            'business_id'       => $aOrderInfo->business_id,
            'order_type'        => 0,
            'scene_type'        => 0,
            'call_car_type'     => 0,
            'is_fast_car'       => Product::isFastcar($aOrderInfo->product_id),
            'district'          => $aOrderInfo->district,
            'abstract_district' => $aOrderInfo->district . ',' . $aOrderInfo->county,
            'payments_type'     => $aOrderInfo->pay_type,
            'user_type'         => $bIsVip ? 2 : '',

        ];
        return $aData;
    }

    /**
     * 写public日志.
     *
     * @param array $aInfos 所有数据
     */
    private function _writePublicLogForMulti(array $aResponse, array $aOrderInfo, array $aRequest) {
        $aEstimateStatistic = [
            'opera_stat_key'    => 'g_order_multiroute_prefee_show',
            'pid'               => $aOrderInfo['passenger_id'],
            'phone'             => $aOrderInfo['passenger_phone'],
            'area'              => $aOrderInfo['area'],
            'district'          => $aOrderInfo['district'],
            'order_id'          => $aOrderInfo['order_id'],
            'product_id'        => $aOrderInfo['product_id'],
            'business_id'       => $aOrderInfo['business_id'],
            'require_car_level' => $aOrderInfo['require_level'],
            'combo_type'        => $aOrderInfo['combo_type'],
            'route_id'          => $aResponse['route_id'] ?? 0,
            'pre_total_fee'     => $aResponse['fee_amount'] ?? -1,
            'basic_fee'         => $aResponse['basic_fee'] ?? -1,
            'current_lat'       => $aRequest['lat'],
            'current_lng'       => $aRequest['lng'],
        ];
        PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);
    }
}
