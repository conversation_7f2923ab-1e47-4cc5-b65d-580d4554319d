<?php

use Biz<PERSON>om<PERSON>\Models\Passenger\Passenger;
use Biz<PERSON>ib\Client\UfsClient;
use BizLib\Constants\Common;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Exception\ExceptionWithResp;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\ExceptionHandler;
use BizLib\ErrCode\RespCode;
use BizLib\Utils\Language;
use PreSale\Core\Controller;

/**
 * Class pGetCarCheckRulePage  车型勾选推荐规则说明页
 */
class PGetCarCheckRulePageController extends Controller
{

    /**
     * 乘客信息
     */
    private $_aPassengerInfo;

    const USER_DECISION_GULFSTREAM_CAR_SELECT = 'base.user_decision_gulfstream_car_select_backup'; // 平台推荐车型勾选
    const RECOMMEND_CAR_SELECT_COMMONLY_CAR   = 'user_decision_gulfstream_car_select_based_commonly_car';// 我的常用车型 开关
    const COMMONLY_CAR_LIST = 'user_decision.car_list_v2';// 车型列表key
    const PERSONAL_RECOMMEND_CAR_SELECT = 'user_decision_gulfstream.car_select_syncretic';//车型个性化推荐
    /**
     * @var string[] ufs key list
     */
    public static $aKeyList = [
        self::USER_DECISION_GULFSTREAM_CAR_SELECT,
        self::COMMONLY_CAR_LIST,
        self::PERSONAL_RECOMMEND_CAR_SELECT,
    ];

    /**
     * dcmp获取页面
     * @return void
     */
    public function indexAction() {

        $aResponseInfo = UtilsCommon::pairErrNo(RespCode::P_SUCCESS, $this->aErrnoMsg);

        try {
            //验证参数
            $this->verify($_GET);

            // 页面配置 (DCMP中取 可灵活配置文案)
            $aConf = Language::getDecodedTextFromDcmp('config_text-car_check_rule_page');
            if (empty($aConf['title']) || empty($aConf['content'])) {
                $this->sendTextJson($aResponseInfo);
                return;
            }

            $aResponseInfo['data']['title']   = $aConf['title'];
            $aResponseInfo['data']['content'] = $aConf['content'];

            if (!is_array($aConf['data_list']) || sizeof($aConf['data_list']) <= 0) {
                $this->sendTextJson($aResponseInfo);
                return;
            }

            $aRet = $this->_getUfsFeature(self::$aKeyList);

            $aDataList = [];
            foreach ($aConf['data_list'] as $aItem) {
                if (empty($aItem['key'])) {
                    continue;
                }

                if (self::USER_DECISION_GULFSTREAM_CAR_SELECT == $aItem['key']) {
                    $aItem['is_selected'] = (int)$aRet[self::USER_DECISION_GULFSTREAM_CAR_SELECT];
                }

                if (self::RECOMMEND_CAR_SELECT_COMMONLY_CAR == $aItem['key']
                ) {
                    if (in_array($_GET['access_key_id'],[Common::DIDI_WECHAT_MINI_PROGRAM, Common::DIDI_ALIPAY_MINI_PROGRAM,])) {
                        continue;
                    }

                    $aItem = $this->_getMyCommonlyUsedCar($aRet, $aItem);
                }

                if (!empty($aItem)) {
                    $aDataList[] = $aItem;
                }
            }

            $aResponseInfo['data']['data_list'] = $aDataList;
        } catch (\Exception $e) {
            $aContext = [
                'dltag'   => '_com_request_out_failure',
                'err_msg' => $this->aErrnoMsg,
            ];

            //异常处理并得到返回值
            $aResponseInfo = ExceptionHandler::getInstance()->handleException($e, $aContext);
        }

        $this->sendTextJson($aResponseInfo);
    }

    /**
     * 处理我的常用车型
     * @param array $aUfsResult ufs响应
     * @param array $aItem      $aItem
     * @return array
     */
    private function _getMyCommonlyUsedCar($aUfsResult, $aItem) {

        // 获取开关标识
        $aItem['is_selected'] = (int) $aUfsResult[self::RECOMMEND_CAR_SELECT_COMMONLY_CAR];

        // 获取ufs, 取出已勾选车型
        $aUfsCarList = json_decode($aUfsResult[self::COMMONLY_CAR_LIST], true);

        $aPcIdList = [];
        if (!empty($aUfsCarList) && is_array($aUfsCarList)) {
            foreach ($aUfsCarList as $aCar) {
                if (1 == $aCar['is_selected']) {
                    $aPcIdList[] = $aCar['pc_id'];
                }
            }
        }

        // 遍历默认车型
        foreach ($aItem['default_car_list'] as $aCar) {
            if (in_array($aCar['pc_id'], $aPcIdList)) {
                $aCar['is_selected'] = 1;
            } else {
                $aCar['is_selected'] = 0;
            }

            $aItem['car_list'][] = $aCar;
        }

        unset($aItem['default_car_list']);

        return $aItem;
    }

    /**
     * 验证参数.
     * @param array $aRequest get请求
     * @return void
     * @throws ExceptionWithResp 自定义异常
     */
    public function verify($aRequest) {

        if (empty($aRequest['token'])) {
            throw new ExceptionWithResp(
                Code::E_COMMON_TOKEN_INVALID,
                RespCode::P_PARAMS_ERROR,
                '',
                [
                    'exceptionMsg' => [
                        'msg'      => 'token is empty',
                        'moreInfo' => [
                            'args' => (array)$this,
                        ],
                    ],
                ]
            );
        }

        $aPassenger = (Passenger::getInstance())->getPassengerByToken($aRequest['token']);
        $sUid       = $aPassenger['uid'] ?? 0;

        if (empty($sUid)) {
            throw new ExceptionWithResp(
                Code::E_COMMON_TOKEN_INVALID,
                RespCode::P_PARAMS_ERROR,
                '',
                [
                    'exceptionMsg' => [
                        'msg'      => 'uid is empty (token is invalid)',
                        'moreInfo' => [
                            'args' => (array)$this,
                            'uid'  => $sUid,
                        ],
                    ],
                ]
            );
        }

        $this->_aPassengerInfo = $aPassenger;
    }

    /**
     * @param array $aFeature keys
     * @return array
     */
    private function _getUfsFeature($aFeature) {
        $aCondition = ['uid' => $this->_aPassengerInfo['uid']];
        $aRes       = (new UfsClient())->getFeature($aFeature, $aCondition, 'passenger');
        if (UfsClient::SUCCESS_CODE !== $aRes['errno']) {
            NuwaLog::warning(
                Msg::formatArray(
                    Code::E_COMMON_GET_UFS_FAIL,
                    ['featureValues' => $aFeature, 'condition' => $aCondition, 'setUfsRes' => $aRes]
                )
            );
            return [];
        }

        return $aRes['result'];
    }
}
