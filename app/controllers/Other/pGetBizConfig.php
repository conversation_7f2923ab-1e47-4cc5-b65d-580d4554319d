<?php

use BizLib\Config as NuwaConfig;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\Language;
/*
 * Created by PhpStorm.
 * User: heyuehui
 * Desc: 批量获取各业务线配置
 * wiki: http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=132925657
 * Date: 2018/5/9
 * Time: 13:51
 */

use BizLib\Utils\Request;
use BizLib\ErrCode\Code;
use BizLib\Exception\InvalidArgumentException;
use BizLib\ExceptionHandler;
use Xiaoju\Apollo\Apollo as ApolloV2;

/**
 * Class PGetBizConfigController
 */
class PGetBizConfigController extends \PreSale\Core\Controller
{
    const VERSION        = 4;
    const VERSION_V2     = 5;
    const LANG_CODE_STEP = 1000;

    /**
     * @return void
     */
    public function init() {
        parent::init();
    }

    /**
     * @return void
     */
    public function indexAction() {
        try {
            $aRet         = UtilsCommon::getErrMsg(GLOBAL_SUCCESS);
            $aParams      = $this->_getInputParams();
            $aBizConfig   = $this->_getPassengerBizConfig($aParams);
            $aRet['data'] = $this->_assembleConfig($aBizConfig);
        } catch (\Exception $e) {
            $aErrMsg = NuwaConfig::text('errno', 'pGetBizConfig_error_msg');
            $aRet    = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $aErrMsg, ]);
        }

        $aRet['errno'] = (int)($aRet['errno']);
        $this->sendTextJson($aRet);
    }

    /**
     * @param string $aParams 参数
     * @return array
     */
    private function _getPassengerBizConfig($aParams) {
        $aRet         = [];
        $aBizConfList = $aParams['biz_conf'];
        if (empty($aBizConfList)) {
            return $aRet;
        }

        $iCurrentServerVersion = $this->_getCurrentServerVersion($aParams);
        $aConfigList           = NuwaConfig::config('config_passenger_biz', 'config_passenger_biz');

        // 根据端上上传的业务线列表获取配置，如果版本号相同的则过滤掉
        foreach ($aBizConfList as $aConf) {
            $iBusinessId = $aConf['business_id'];
            $iVersion    = $aConf['version'];
            if ($iVersion == $iCurrentServerVersion) {
                $aRet[$iBusinessId] = ['version' => $iVersion,];
                continue;
            }

            $aRet[$iBusinessId]            = $aConfigList[$iBusinessId] ?? [];
            $aRet[$iBusinessId]['version'] = $iCurrentServerVersion;
        }

        return $aRet;
    }

    /**
     * @param array $aConfig 参数
     * @return mixed
     */
    private function _assembleConfig($aConfig) {
        // 如何要获取的业务线配置数量大于1，通过数组的交集取出common部分，以节省流量
        if (!empty($aConfig) && (count($aConfig) > 1)) {
            $aCommonConfig = call_user_func_array('array_intersect_assoc', $aConfig);
            foreach ($aCommonConfig as $sKey => $v) {
                if (('version' == $sKey) || is_array($v)) {
                    unset($aCommonConfig[$sKey]);
                }
            }

            if (!empty($aCommonConfig)) {
                foreach ($aConfig as $iBusinessId => $aConf) {
                    $aConfig[$iBusinessId] = array_diff($aConf, $aCommonConfig);
                }

                $aConfig['0'] = $aCommonConfig;
            }
        }

        return $aConfig;
    }

    /**
     * 乘客端会缓存配置,如果切换语言会到导致缓存不更新,此处会根据语言返回一个版本号,来刷客户端的缓存.
     *
     * <AUTHOR>
     * @datetime 2018-05-10
     *
     * @param string $sLanguage 语种
     *
     * @return int
     */
    private function _getCurrentServerVersion($aParam) {
        $iVersionBase = $this->_getVersion($aParam);
        $aSupportLang = Language::getSupportLanguages();
        $aSupportLang = array_flip($aSupportLang);
        $iVersion     = $iVersionBase + self::LANG_CODE_STEP * (int)($aSupportLang[$aParam['lang']]);

        return $iVersion;
    }

    private function _getVersion($aParam) {
        $aApolloParams = [
            'app_version'   => $aParam['app_version'],
            'access_key_id' => $aParam['access_key_id'],
            'lang'          => $aParam['lang']
        ];
        if (ApolloV2::getInstance()->featureToggle('biz_conf_version_v2', $aApolloParams)->allow()) {
            return self::VERSION_V2;
        }

        return self::VERSION;
    }

    /**
     * @return mixed
     *
     * @throws InvalidArgumentException
     */
    private function _getInputParams() {
        $oRequest        = Request::getInstance();
        $aParams['lang'] = $oRequest->getStr('lang');
        $sBizConf        = $oRequest->getStr('business_conf');
        if (empty($sBizConf) || empty($aParams['lang'])) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                array(
                    'biz_conf' => $sBizConf,
                    'lang'     => $aParams['lang'],
                )
            );
        }

        $aSupportLang = Language::getSupportLanguages();
        if (!in_array($aParams['lang'], $aSupportLang)) {
            $aParams['lang'] = Language::getLocalLanguage();
        }

        $aParams['biz_conf'] = json_decode($sBizConf, true);
        if (JSON_ERROR_NONE != json_last_error()) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                array(
                    'biz_conf' => $sBizConf,
                    'lang'     => $aParams['lang'],
                )
            );
        }

        $aParams['access_key_id'] = $oRequest->getStr('access_key_id');
        $aParams['app_version']   = $oRequest->getStr('appversion');

        return $aParams;
    }
}
