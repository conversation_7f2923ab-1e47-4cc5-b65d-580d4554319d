<?php
/**
 * Created by PhpStorm.
 * Date: 20/5/22
 * Time: 14:42
 * @category Category
 * @package FileDirFileName
 * <AUTHOR> <<EMAIL>>
 * @link ${link}
 */
use BizLib\Utils\Request;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Constants\Common as ConsCommon;
use BizLib\ErrCode\Code;
use BizLib\Utils\Language;
use BizLib\Exception\InvalidArgumentException;
use BizLib\ExceptionHandler;
use Nuwa\ApolloSDK\Apollo;
use BizLib\Log as NuwaLog;
use BizLib\Libraries\RedisDB;
use BizCommon\Models\Passenger\Passenger;
use BizLib\Utils\NumberHelper;
use PreSale\Logics\carpool\Coupon;

/**
 * Class PGetBarrageController
 * @property PGetBarrageController $PGetBarrageController
 */
class PGetBarrageController extends \PreSale\Core\Controller
{
    /**
     * @Desc:
     * mixed[] Array structure to count the elements of.
     * @return void
     * @property init $init
     * @Author:<EMAIL>
     */
    public function init() {
        parent::init();
    }

    /**
     * @Desc:
     * mixed[] Array structure to count the elements of.
     * @throws  mixed[] Exception Array structure to count the elements of.
     * @return void
     * @property indexAction $indexAction
     * @Author:<EMAIL>
     */
    public function indexAction() {
        $aRet = UtilsCommon::getErrMsg(GLOBAL_SUCCESS);
        try {
            $aParams          = $this->_getParam();
            $oLowPriceCarpool = new \PreSale\Logics\carpool\LowPriceCarpool();
            $aRet['data']     = $oLowPriceCarpool->getBarrageInfo($aParams);
        } catch (\Exception $e) {
            $aErrMsg = NuwaConfig::text('errno', 'pGetBizConfig_error_msg');
            $aRet    = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $aErrMsg, ]);
        }

        $aRet['errno'] = (int)($aRet['errno']);
        $this->sendTextJson($aRet);
    }

    /**
     * @Desc:
     * mixed[] Array structure to count the elements of.
     * @return array
     * @throws InvalidArgumentException structure to count the elements of.
     * @property _getParam $_getParam
     * @Author:<EMAIL>
     */
    private function _getParam() {
        $oRequest    = Request::getInstance();
        $sLang       = $oRequest->getStr('lang') ?? Language::getLocalLanguage();
        $sMenuId     = $oRequest->getStr('menu_id');
        $iArea       = $oRequest->getInt('city_id');
        $sToken      = $oRequest->getStr('token');
        $sAppVersion = $oRequest->getStr('appversion');
        $fLng        = $oRequest->getFloat('lng');
        $fLat        = $oRequest->getFloat('lat');

        $aParams = [
            'area'          => $iArea,
            'token'         => $sToken,
            'lang'          => $sLang,
            'app_version'   => $sAppVersion,
            'menu_id'       => $sMenuId,
            // 'lng'          => $fLng,
            // 'lat'          => $fLat,
            'origin_id'     => $oRequest->getInt('origin_id'),
            'client_type'   => $oRequest->getInt('client_type'),
            'map_type'      => $oRequest->getStr('map_type'),
            'channel'       => $oRequest->getInt('channel'),
            'a3_token'      => $oRequest->getStr('a3_token'),
            'network_type'  => $oRequest->getStr('networkType'),
            'from_name'     => $oRequest->getStr('from_name'),
            'from_address'  => $oRequest->getStr('from_address'),
            'dviceid'       => $oRequest->getStr('dviceid'),
            'imei'          => $oRequest->getStr('imei'),
            'estimate_id'   => $oRequest->getStr('estimate_id'),
            'access_key_id' => $oRequest->getStr('access_key_id'),
        ];

        $aAllowMenuIds = [ConsCommon::MENU_PINCHECHE];
        if (empty($iArea) || !in_array($sMenuId, $aAllowMenuIds)) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                array(
                    'city_id' => $iArea,
                    'menu_id' => $sMenuId,
                )
            );
        }

        if ('zh-CN' != $sLang) {
        // if ('zh-CN' != $sLang ||empty($fLng)||empty($fLat)) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                array(
                    'lang'  => $sLang,
                    'token' => $sToken,
                    // 'lng'   => $fLng,
                    // 'lat'   => $fLat,
                )
            );
        }

        return $aParams;
    }
}
