<?php

use BizLib\Utils\Request;
use <PERSON>izCommon\Logics\PassengerBaseLogic;
use PreSale\Logics\anycar\ExitEstimateLogic;
use PreSale\Core\BaseController;

/**
 * 极高确一期："http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=544257697
 * Class PExitEstimate
 */
class PExitEstimateController extends BaseController
{

    /**
     * @var ExitEstimateLogic
     */
    private $_oExitEstimateLogic = null;

    private $_aOrderInfo = [];

    /**
     * 负责接口入参解析初始化
     * @return array|mixed
     */
    public function checkParams() {
        $oRequest            = Request::getInstance();
        $aParams['token']    = $oRequest->getStr('token');
        $aParams['order_id'] = $oRequest->getStr('order_id');
        $aParams['lang']     = $oRequest->getStr('lang');
        $aParams['multi_require_product'] = $oRequest->getStr('multi_require_product');
        $aParams['app_version']           = $oRequest->getStr('app_version');
        $aParams['data_type']     = $oRequest->getStr('datatype');
        $aParams['channel']       = $oRequest->getStr('channel');
        $aParams['access_key_id'] = $oRequest->getstr('access_key_id');
        $aParams['agent_type']    = $oRequest->getstr('agent_type');

        return $aParams;
    }

    /**
     * 其他参数处理
     * @param array $aParams $aParams
     * @return void
     */
    protected function updateParams($aParams) {
        $aOrderDetailParams = PassengerBaseLogic::getInstance()->getParams($aParams);
        $this->_aOrderInfo  = $aOrderDetailParams['order_info'];
        if (empty($this->_aOrderInfo['app_version'])) {
            $this->_aOrderInfo['app_version'] = $aParams['app_version'];
        }

        if (empty($this->_aOrderInfo['v6_version'])) {
            $this->_aOrderInfo['v6_version'] = $aParams['v6_version'];
        }

        if (empty($this->_aOrderInfo['access_key_id'])) {
            $this->_aOrderInfo['access_key_id'] = $aParams['access_key_id'];
        }
    }

    /**
     * @param array $aParams 入参
     * @return array|void
     * @throws Exception 异常
     */
    public function execute(array $aParams) {
        $this->updateParams($aParams);
        $this->_oExitEstimateLogic = new ExitEstimateLogic();
        return $this->_oExitEstimateLogic->getEstimatePriceInfo($this->_aOrderInfo,$aParams);
    }


    /**
     * 写日志
     * @return void
     */
    protected function _processLog() {
        $this->_oExitEstimateLogic->writePublicLog();
    }
}










