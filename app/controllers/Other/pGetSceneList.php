<?php
/**
 * <AUTHOR> <<EMAIL>>
 * Desc: 乘客侧6.0获取场景列表
 * Date: 2020/2/5
 * Time: 上午11:01
 */

use BizLib\Config as NuwaConfig;
use BizLib\ErrCode\Msg;
use BizLib\Log;
use PreSale\Logics\scene\SceneListLogic;
use PreSale\Logics\scene\NewSceneListLogic;
use BizLib\ErrCode\Code;
use BizLib\Utils;
use BizLib\Utils\Language;
use BizLib\Utils\PublicLog;
use BizCommon\Models\Passenger\Passenger;
use BizLib\Client\PassportClient;

/**
 * Class PGetSceneListController
 */
class PGetSceneListController extends \PreSale\Core\Controller
{
    /**
     * init
     * @return void
     */
    public function init() {
        parent::init();
    }

    /**
     * indexAction
     * @return void
     */
    public function indexAction() {
        try {
            //1. 参数获取
            $aParams = $this->_getParams();

            //2. 信息获取
            $aInfo = $this->_getAllInfo($aParams);

            // 灰度流量控制
            $oToggle = \Nuwa\ApolloSDK\Apollo::getInstance()->featureToggle(
                'scene_version_control',
                [
                    'app_version'   => $aParams['app_version'],
                    'access_key_id' => $aParams['access_key_id'],
                    'pid'           => $aInfo['passenger_info']['pid'] ?? '',
                ]
            );

            if ($oToggle->allow()) {
                // 3. 获取场景位数据
                $oNewSceneListLogic =new NewSceneListLogic($aParams);
                $aScene =$oNewSceneListLogic->getSceneDataFromProsche($aParams,$aInfo);
                $aRet = $oNewSceneListLogic->formatData($aParams,$aScene);
            } else {
                //3. 配置获取
                $oSceneListLogic = new SceneListLogic($aParams);
                $aConf           = $oSceneListLogic->getSceneConf($aParams);

                //4. 入口过滤
                $aList = $oSceneListLogic->filter($aConf, $aInfo);

                //5. 入口互斥
                $aList = $oSceneListLogic->rejecter($aList, $aInfo);

                //6. 入口渲染
                $aList = $oSceneListLogic->render($aList, $aInfo);

                //7. 排序
                $aList = $oSceneListLogic->rank($aList, $aInfo);

                //8. 整体format
                $aRet = $oSceneListLogic->formatList($aList, $aInfo);
            }
            // 添加public 日志
            $this->_writeSceneDataInfo($aInfo, $aRet);
            $this->sendJson($aRet);
        } catch (\Exception $e) {
            $aRet = array('errno' => 0, 'errmsg' => 'success');
            if (0 != $e->getCode()) {
                $aRet['errno'] = $e->getCode();
                if (!empty($e->getMessage())) {
                    $aRet['errmsg'] = $e->getMessage();
                }
            }
            $this->sendJson($aRet);
        }

    }

    /**
     * @desc 参数获取
     * @return mixed
     * @throws \Exception 异常
     */
    private function _getParams() {
        $aParams['area']          = $this->getRequest()->getQuery('area', '');
        $aParams['lat']           = $this->getRequest()->getQuery('flat', '');
        $aParams['lng']           = $this->getRequest()->getQuery('flng', '');
        $aParams['token']         = $this->getRequest()->getQuery('token', '');
        $aParams['app_version']   = $this->getRequest()->getQuery('appversion', '');
        $aParams['access_key_id'] = $this->getRequest()->getQuery('access_key_id', '');
        $aParams['lang']          = $this->getRequest()->getQuery('lang', '') ?? Language::ZH_CN;
        $aParams['channel']       = $this->getRequest()->getQuery('channel', '');
        $aParams['menu_id']       = $this->getRequest()->getQuery('menu_id', '');
        $aParams['v6x_version']   = $this->getRequest()->getQuery('v6x_version', 0);
        $aParams['city_id']       = $this->getRequest()->getQuery('city_id', 0);

        if (empty($aParams['app_version'])) {
            Log::warning(Msg::formatArray(Code::E_COMMON_PARAM_INVALID_VALUE, ['params' => $aParams]));
            throw new \Exception(Msg::get(Code::E_COMMON_PARAM_INVALID_VALUE), Code::E_COMMON_PARAM_INVALID_VALUE);
        }

        // ZG要求只有登陆态才能下发场景位入口
        if (empty($aParams['token'])) {
            Log::warning(Msg::formatArray(Code::E_COMMON_PARAM_INVALID_VALUE, ['params' => $aParams]));
            throw new \Exception(Msg::get(Code::E_COMMON_PARAM_INVALID_VALUE), Code::E_COMMON_PARAM_INVALID_VALUE);
        }

        if (!empty($aParams['area'])) {
            return $aParams;
        }

        if (!empty($aParams['city_id'])) {
            $aParams['area'] = $aParams['city_id'];
            return $aParams;
        }

        if (!empty($aParams['lat']) && !empty($aParams['lng'])) {
            $aParams['area'] = Utils\MapHelper::getAreaByLoc($aParams['lng'], $aParams['lat']);
        }

        if (empty($aParams['area'])) {
            // Log::warning(Msg::formatArray(Code::E_COMMON_COORDINATE_INVALID, ['params' => $aParams]));
            throw new \Exception(Msg::get(Code::E_COMMON_COORDINATE_INVALID), Code::E_COMMON_COORDINATE_INVALID);
        }

        return $aParams;
    }

    /**
     * @desc 根据入参获取业务信息
     * @param array $aParams 请求入参
     * @return array
     */
    private function _getAllInfo($aParams) {
        $aBizInfo            = [];
        $aBizInfo['request'] = $aParams;
        if (!empty($aParams['token'])) {
            $aBizInfo['passenger_info'] = Passenger::getInstance()->getPassengerByTokenFromPassport($aParams['token']);
            $aPassengerResult           = (new PassportClient())->getPassengerByUid($aBizInfo['passenger_info']['uid']);
            $aBizInfo['passenger_info']['passenger_phone'] = $aPassengerResult['result']['cell'] ?? '';
        }

        $aBizInfo['apollo'] = array(
            'area'          => $aParams['area'],
            'uid'           => $aBizInfo['passenger_info']['uid'] ?? '',
            'pid'           => $aBizInfo['passenger_info']['pid'] ?? '',
            'phone'         => $aBizInfo['passenger_info']['passenger_phone'] ?? '',
            'app_version'   => $aParams['app_version'],
            'city'          => $aParams['area'],
            'order_city'    => $aParams['area'],
            'v6x_version'   => $aParams['v6x_version'],
            'access_key_id' => $aParams['access_key_id'],
            'caller'        => 'pre-sale',
        );
        return $aBizInfo;
    }

    /**
     * @desc 场景位public日志
     * @param array $aParams 请求入参
     * @param array $aRet    场景位返回值
     * @return void
     */
    private function _writeSceneDataInfo($aParams, $aRet) {
        if(empty($aRet) || $aRet['errno'] != 0 || empty($aRet['data']) || empty($aRet['data']['scene_list'])) {
            return;
        }
        foreach ($aRet['data']['scene_list'] as $index => $aInfo) {
            $aSceneListInfo = [
                'opera_stat_key' => 'g_scene_info',
                'scene_name'     => $aInfo['text'],
                'city_id'        => $aParams['request']['area'],
                'pid'            => $aParams['passenger_info']['pid'],
                'access_key_id'  => $aParams['request']['access_key_id'],
                'sort_idx'       => $index + 1,
            ];
            if (!empty($aInfo['page_type'])) {
                $aSceneListInfo['page_type'] = $aInfo['page_type'];
            }
            if (!empty($aInfo['scene_id'])) {
                $aSceneListInfo['scene_id'] = $aInfo['scene_id'];
            }
            PublicLog::writeLogForOfflineCal('public', $aSceneListInfo);
        }
    }
}
