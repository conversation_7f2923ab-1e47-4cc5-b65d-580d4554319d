<?php

use BizCommon\Logics\Order\FieldOrderNTuple;
use BizCommon\Logics\Themis\ThemisComLogic;
use BizLib\Client\DoorGodClient;
use BizLib\Config as NuwaConfig;
use BizLib\Constants\OrderSystem;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\Product;
use BizLib\ExceptionHandler;
use BizLib\ErrCode\RespCode;
use BizLib\Utils\ProductCategory;
use PreSale\Logics\priceRule\GetPriceRuleByTokenReq;
use PreSale\Logics\priceRule\GetPriceRuleByStrategyToken;
use PreSale\Logics\priceRule\GetPriceRuleByOidReq;
use PreSale\Logics\priceRule\BuildPriceRule;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\v3Estimate\multiResponse\Component\introMsg\Util;

/**
 * Class pGetPriceRuleV2.
 */
class PGetPriceRuleV2Controller extends \PreSale\Core\Controller
{

    const WaitingForServiceFinishService = 4;

    const SourcePriceRuleByStrategyToken = 1;
    const SourcePriceRuleByToken         = 2;

    const PRICE_RULE_REQ_FROM_HOMEPAGE    = 1;
    const PRICE_RULE_REQ_FROM_ESTIMATE    = 2;
    const PRICE_RULE_REQ_FROM_DURING_TRIP = 3;

    /**
     * 不同场景下获取计价规则详情
     * @return void
     */
    public function indexAction() {
        $iBusinessID = $this->oRequest->getStr('business_id');
        if (!empty($iBusinessID) && \BizCommon\Utils\Horae::isHongKongThirdByBusiness((int)$iBusinessID)) {
            $this->_getPriceRuleForHKThird();
            return;
        }

        $sPriceToken    = $this->oRequest->getStr('price_token');
        $sStrategyToken = $this->oRequest->getStr('strategy_token');

        if (!empty($sStrategyToken)) {
            // 乘客端侧边栏进来的费用详情
            $this->_getPriceRuleByStrategyToken();
        } elseif (!empty($sPriceToken)) {
            // 预估冒泡的费用详情
            $this->_getPriceRuleByToken();
        } else {
            // 行程中、结束计费、历史订单的费用详情
            $this->_getPriceRuleByOid();
        }
    }

    /**
     * 根据计价token获取计价规则详情
     * 端入口：预估价.
     * @return void
     */
    private function _getPriceRuleByToken() {
        // 初始化Request对象
        $oRequest = new GetPriceRuleByTokenReq($_GET);

        try {
            // 获取默认返回值
            $aResponseInfo = UtilsCommon::getErrMsg(RespCode::P_SUCCESS, $this->aErrnoMsg);

            // 验证参数
            list($iProductId, $iDepartureTimestamp, $aAreaInfo, $sPhone, $aPassenger, $iAccessKeyId, $sAppVersion) = $oRequest->verify();

            // 调用账单服务
            $oBillPriceRule = new BuildPriceRule();
            $aOrderInfoReq  = $this->_buildOrderInfo($oRequest, $aAreaInfo, $aPassenger, $iAccessKeyId, $sAppVersion, $iProductId);

            $aOrderExtra = [];
            if (!empty($oRequest->sEnableCapPrice)) {
                $aOrderExtra['enable_cap_price'] = $oRequest->sEnableCapPrice;
            }

            if (!empty($oRequest->getSFenceIdBoth())) {
                list($iStartFenceId, $iEndFenceId) = array_map(
                    function ($sVal) {
                        return (int)$sVal;
                    },
                    array_pad(explode(',', $oRequest->getSFenceIdBoth()), 2, '0')
                );
                if (!empty($iStartFenceId)) {
                    $aOrderExtra['sps_start_fence_id'] = $iStartFenceId;
                    $aOrderExtra['sps_end_fence_id']   = $iEndFenceId;
                } else {
                    $aOrderInfoReq['fence_id'] = $iStartFenceId;
                }
            }


            $aPriceRule = $oBillPriceRule->getPriceStrategyV2(
                $oRequest->getPriceToken(),
                $aOrderInfoReq,
                $oRequest->getLang(),
                $oRequest->getAccessKeyId(),
                $aAreaInfo,
                self::PRICE_RULE_REQ_FROM_ESTIMATE,
                $aOrderExtra,
                $this->_buildPassengerParam($iProductId, $aOrderInfoReq)
            );
            // 使用pcid拿到的carname替换bill返回的title
            // 豪华车细分车型的名称配置
            $aConfig       = NuwaConfig::text('config_text', 'lux_car_level_config');
            $aCarLevelConf = $aConfig[(string)($oRequest->getIRequireLevel())] ?? '';
            if ($oRequest->getProductCategoryId() > 0) {
                $sTitlePre = Util::getCarTitleByProductCategory($oRequest->getProductCategoryId());

                // 出租车按城市配置品类名
                $sTaxiTitleCity = Util::getCarTitleByProductCategoryCity(
                    $oRequest->getProductCategoryId(),
                    $aAreaInfo['id'],
                    $oRequest->getLang()
                );
                if (!empty($sTaxiTitleCity)) {
                    $sTitlePre = $sTaxiTitleCity;
                }

                // 出租车按区县配置品类名
                $sTaxiTitleCounty = Util::getCarTitleByProductCategoryCountyId(
                    $oRequest->getProductCategoryId(),
                    $aAreaInfo['countyid'] ?? 0,
                    $aAreaInfo['county_name'] ?? '',
                    $oRequest->getLang()
                );
                if (!empty($sTaxiTitleCounty)) {
                    $sTitlePre = $sTaxiTitleCounty;
                }

                $aPriceRule['title'] = $sTitlePre;
                if (!empty($aCarLevelConf)) { // 豪华车品类
                    $sTitleSuf = $aCarLevelConf['level_name'] ?? '';
                    if (!empty($sTitleSuf)) {
                        $aPriceRule['title'] = $sTitlePre.'-'.$sTitleSuf;
                    }
                }

                // 车型图标
                $sCarIcon = $this->_getTopImage($aAreaInfo['id'], $oRequest->getLang(), $oRequest->getProductCategoryId());
                if (!empty($sCarIcon)) {
                    $aPriceRule['url'] = $sCarIcon;
                }
            }

            $aOrderNTuple = $this->_fillOrderNTupleForFeeConfig($aOrderInfoReq['order_n_tuple'] ?? [], (int)$oRequest->getILevelType(), $iProductId, $aPriceRule['combo_type']);
            $oBillPriceRule->setOrderNTuple($aOrderNTuple);
            $aResponseInfo['data'] = $oBillPriceRule->addPriceRuleV2(
                self::SourcePriceRuleByToken,
                $aPriceRule,
                $iProductId,
                $aAreaInfo,
                $oRequest->getLang(),
                $iDepartureTimestamp,
                $sPhone,
                $aPassenger['pid']
            );
        } catch (\Exception $e) {
            // 异常处理上下文
            $aContext = [
                'dltag'   => '_com_request_out_failure',
                'err_msg' => $this->aErrnoMsg,
            ];

            // 异常处理并得到返回值
            $aResponseInfo = ExceptionHandler::getInstance()->handleException($e, $aContext);
        }

        // 输出返回值
        $this->sendTextJson($aResponseInfo, $oRequest->getCallback());
    }

    /**
     * @param GetPriceRuleByTokenReq $oRequest     请求
     * @param array                  $aAreaInfo    位置
     * @param array                  $aPassenger   Passenger
     * @param int                    $iAccessKeyId 来源标识
     * @param string                 $sAppVersion  版本号
     * @return array
     */
    private function _buildOrderInfo(GetPriceRuleByTokenReq $oRequest, $aAreaInfo, $aPassenger, $iAccessKeyId, $sAppVersion, $iProductId) {
        $sStartingLng = !empty((string)($oRequest->getFLng())) ? (string)($oRequest->getFLng()) : '0';
        $sStartingLat = !empty((string)($oRequest->getFLat())) ? (string)($oRequest->getFLat()) : '0';
        $sEndingLng   = !empty((string)($oRequest->getTLng())) ? (string)($oRequest->getTLng()) : '0';
        $sEndingLat   = !empty((string)($oRequest->getTLat())) ? (string)($oRequest->getTLat()) : '0';
        $iCarpoolType = $oRequest->getCarpoolType();
        $iProductId   = Product::getProductIdByBusinessId($oRequest->getBusinessId());
        $aOrderInfo   = [
            'departure_time'  => $oRequest->getDepartureTimeYmdHis(),
            'trip_country'    => $oRequest->getTripCountry(),
            'starting_lng'    => $sStartingLng,
            'starting_lat'    => $sStartingLat,
            'dest_lng'        => $sEndingLng,
            'dest_lat'        => $sEndingLat,
            'passenger_id'    => $aPassenger['pid'],
            'passenger_phone' => $aPassenger['phone'],
            'channel'         => $oRequest->getChannel(),
        ];
        if (!empty($oRequest->getILevelType())) {
            $aOrderInfo['order_n_tuple']['level_type'] = (int)$oRequest->getILevelType();
        }
        if (!empty($oRequest->getComboType())) {
            $aOrderInfo['order_n_tuple']['combo_type'] = $oRequest->getComboType();
        }
        if (!empty($oRequest->getCarLevel())) {
            $aOrderInfo['order_n_tuple']['car_level'] = $oRequest->getCarLevel();
        }

        $this->_fillBillParams($aOrderInfo, $aAreaInfo, $aPassenger, $iAccessKeyId, $sAppVersion, $iProductId, $iCarpoolType);

        return $aOrderInfo;
    }

    /**
     * 根据订单ID获取计价规则详情
     * 端入口：行程中、结束计费、历史订单.
     * @return void
     */
    private function _getPriceRuleByOid() {
        // 初始化Request对象
        $oRequest = new GetPriceRuleByOidReq($_GET);

        try {
            // 获取默认返回值
            $aResponseInfo = UtilsCommon::getErrMsg(RespCode::P_SUCCESS, $this->aErrnoMsg);

            // 验证参数并返回需要的参数验证过程中产生的中间值
            list($aOrderInfo, $aAreaInfo) = $oRequest->verify();

            $oRequest->setTripCountry($aOrderInfo['country_iso_code']);
            // 解析得到计价token
            list($sPriceToken) = explode(':', $aOrderInfo['strategy_token']);

            // 调用账单服务
            $aOrderInfoReq = [
                'order_id'          => (int)($aOrderInfo['order_id']),
                'driver_id'         => (int)($aOrderInfo['driver_id']),
                'driver_phone'      => (string)($aOrderInfo['driver_phone']),
                'passenger_id'      => (int)($aOrderInfo['passenger_id']),
                'passenger_phone'   => (string)($aOrderInfo['passenger_phone']),
                'departure_time'    => $aOrderInfo['departure_time'],
                'begin_charge_time' => $aOrderInfo['begin_charge_time'],
                // 'trip_country'      => $oRequest->getTripCountry(),
                'trip_country'      => $aOrderInfo['country_iso_code'],
                'starting_lng'      => (string)($aOrderInfo['starting_lng']),
                'starting_lat'      => (string)($aOrderInfo['starting_lat']),
                'dest_lng'          => (string)($aOrderInfo['dest_lng']),
                'dest_lat'          => (string)($aOrderInfo['dest_lat']),
                'driver_product_id' => (string)($aOrderInfo['driver_product_id']),
                'strive_car_level'  => (string)($aOrderInfo['strive_car_level']),
                'order_n_tuple'     => FieldOrderNTuple::getOrderNTupleByOrder($aOrderInfo),
            ];

            if ($this->_isHitWaitingForService($aOrderInfo)) {
                $aOrderInfoReq['wait_fee_type'] = 2;
            }

            $aOrderExtra = [];
            if (!empty($oRequest->sEnableCapPrice)) {
                $aOrderExtra['enable_cap_price'] = $oRequest->sEnableCapPrice;
            }

            list($iSpsStartFenceID, $iSpsEndFenceID) = $this->_getSpsFenceID($aOrderInfo);
            if (!empty($iSpsStartFenceID) && empty($iSpsEndFenceID)) {
                $aOrderExtra['fence_id'] = $iSpsStartFenceID;
            } elseif (!empty($iSpsStartFenceID) && !empty($iSpsEndFenceID)) {
                $aOrderExtra['sps_start_fence_id'] = $iSpsStartFenceID;
                $aOrderExtra['sps_end_fence_id']   = $iSpsEndFenceID;
            }

            $oBillPriceRule = new BuildPriceRule();
            $iProductId = intval($aOrderInfo['order_n_tuple']['product_id'] ?? $aOrderInfo['product_id']);
            $aPriceRule     = $oBillPriceRule->getPriceStrategyV2(
                $sPriceToken,
                $aOrderInfoReq,
                $oRequest->getLang(),
                $oRequest->getAccessKeyId(),
                [
                    'district' => $aOrderInfo['district'],
                    'countyid' => $aOrderInfo['county_id'],
                ],
                self::PRICE_RULE_REQ_FROM_DURING_TRIP,
                $aOrderExtra,
                $this->_buildPassengerParam($iProductId, $aOrderInfoReq)
            );

            $aResponseInfo['data'] = $oBillPriceRule->addPriceRuleForOrderV2(
                $aPriceRule,
                $aOrderInfo,
                $aAreaInfo,
                $oRequest->getLang()
            );
        } catch (\Exception $e) {
            // 异常处理上下文
            $aContext = [
                'dltag'   => '_com_request_out_failure',
                'err_msg' => $this->aErrnoMsg,
            ];

            // 异常处理并得到返回值
            $aResponseInfo = ExceptionHandler::getInstance()->handleException($e, $aContext);
        }

        // 输出返回值
        $this->sendTextJson($aResponseInfo, $oRequest->getCallback());
    }

    /**
     * 根据strategy_token获取计价规则详情
     * 端入口：乘客端侧边栏.
     * @return void
     */
    private function _getPriceRuleByStrategyToken() {
        // 初始化Request对象
        $oRequest = new GetPriceRuleByStrategyToken($_GET);

        try {
            // 获取默认返回值
            $aResponseInfo = UtilsCommon::getErrMsg(RespCode::P_SUCCESS, $this->aErrnoMsg);

            // 获取并检查参数
            list($aAreaInfo, $aPassenger, $iAccessKeyId, $sAppVersion) = $oRequest->getAndCheckParams();

            $aOrderInfoReq = [
                'trip_country' => $aAreaInfo['canonical_country_code'] ?? '',
                'fence_id'     => (int)$oRequest->getFenceId(),
            ];
            $iProductId    = Product::getProductIdByBusinessId($oRequest->getBussinessId()) ?? 0;
            $this->_fillBillParams($aOrderInfoReq, $aAreaInfo, $aPassenger, $iAccessKeyId, $sAppVersion, $iProductId, 0);

            // 调用账单服务
            $oBillPriceRule = new BuildPriceRule();
            $aPriceRule     = $oBillPriceRule->getPriceStrategyV2(
                $oRequest->getStrategyToken(),
                $aOrderInfoReq,
                $oRequest->getLang(),
                $oRequest->getAccessKeyId(),
                $aAreaInfo,
                self::PRICE_RULE_REQ_FROM_HOMEPAGE
            );

            $aOrderNTuple = $this->_fillOrderNTupleForFeeConfig($aOrderInfoReq['order_n_tuple'] ?? [], (int)$oRequest->getLevelType(), $iProductId, $aPriceRule['combo_type']);
            $oBillPriceRule->setOrderNTuple($aOrderNTuple);
            $aResponseInfo['data'] = $oBillPriceRule->addPriceRuleV2(
                self::SourcePriceRuleByStrategyToken,
                $aPriceRule,
                $sProductId        = 1,
                $aAreaInfo,
                $oRequest->getLang(),
                0,
                $aPassenger['phone'],
                $aPassenger['pid']
            );
        } catch (\Exception $e) {
            // 异常处理并得到返回值
            $aResponseInfo = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg,]);
        }

        // 输出返回值
        $this->sendTextJson($aResponseInfo);
    }

    /**
     * 香港三方业务获取计价规则
     * @return void
     */
    private function _getPriceRuleForHKThird() {
        try {
            // 获取默认返回值
            $aResponseInfo = UtilsCommon::pairErrNo(RespCode::P_SUCCESS, $this->aErrnoMsg);

            $aPriceRule = (new BuildPriceRule())->getStrategyByTokenOrProductForIntercity(
                $this->_buildGetStrategyByTokenOrProductRequest()
            );
            $aResponseInfo['data'] = $aPriceRule;
        } catch (\Exception $e) {
            $aResponseInfo = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg,]);
        }

        $this->sendTextJson($aResponseInfo);
    }

    /**
     * 构建plutus请求request
     * @return array
     * @throws \BizLib\Exception\ExceptionWithResp ExceptionWithResp
     */
    private function _buildGetStrategyByTokenOrProductRequest() {
        $sPriceToken    = $this->oRequest->getStr('price_token');
        $sStrategyToken = $this->oRequest->getStr('strategy_token');

        // 获取以下plutus入参 $aAreaInfo $aOrderInfo
        $aOrderInfoReq = [];
        if (!empty($sStrategyToken)) { // 入口：乘客端「我的」->「设置」->「用户指南」->「计价规则」
            $oRequest = new GetPriceRuleByStrategyToken($_GET);
            list($aAreaInfo, , ,) = $oRequest->getAndCheckParams();
        } elseif (!empty($sPriceToken)) { // 入口: 乘客端「预估表单」->「费用说明」->「计价规则」
            $oRequest = new GetPriceRuleByTokenReq($_GET);
            list(, , $aAreaInfo, , , ,) = $oRequest->verify();

            $sStrategyToken = $sPriceToken;
            $aOrderInfoReq['starting_lat'] = $oRequest->getFLat() ?? '0';
            $aOrderInfoReq['starting_lng'] = $oRequest->getFLng() ?? '0';
            $aOrderInfoReq['dest_lat']     = $oRequest->getTLat() ?? '0';
            $aOrderInfoReq['dest_lng']     = $oRequest->getTLng() ?? '0';
        } else { // 入口：行程中、结束计费、历史订单的费用详情
            $oRequest = new GetPriceRuleByOidReq($_GET);
            list($aOrderInfo, $aAreaInfo) = $oRequest->verify();

            list($sStrategyToken) = explode(':', $aOrderInfo['strategy_token']);
            $aOrderInfoReq['starting_lat'] = $aOrderInfo['starting_lat'];
            $aOrderInfoReq['starting_lng'] = $aOrderInfo['starting_lng'];
            $aOrderInfoReq['dest_lat']     = $aOrderInfo['dest_lat'];
            $aOrderInfoReq['dest_lng']     = $aOrderInfo['dest_lng'];
        }

        $sAbstractDistrict = (empty($aAreaInfo['countyid']) || empty($aAreaInfo['district'])) ? '' : $aAreaInfo['district'].','.$aAreaInfo['countyid'];
        $aOrderInfoReq['trip_country'] = $aOrderInfo['country_iso_code'] ?? ((string)$aAreaInfo['canonical_country_code'] ?? 'HK');
        return [
            'strategy_token'    => (string)$sStrategyToken,
            'district'          => (string)$aAreaInfo['district'] ?? '',
            'abstract_district' => $sAbstractDistrict,
            'area'              => (int)$aAreaInfo['id'] ?? 1,
            'role'              => 2,
            'lang'              => (string)$oRequest->getLang() ?? \BizLib\Utils\Language::ZH_CN,
            'display_type'      => 1,
            'order_info'        => $aOrderInfoReq,
        ];
    }

    /**
     * 补充请求账单参数
     *
     * @param array  $aOrderInfo   请求账单入参
     * @param array  $aAreaInfo    城市
     * @param array  $aPassenger   乘客信息
     * @param int    $iAccessKeyId 来源标识
     * @param string $sAppVersion  版本信息
     * @param int    $iCarpoolType 拼车类型
     * @return void
     */
    private function _fillBillParams(&$aOrderInfo, $aAreaInfo, $aPassenger, $iAccessKeyId, $sAppVersion, $iProductId, $iCarpoolType) {
        $oApolloClient     = Apollo::getInstance();
        $aApolloParameters = array(
            'key'           => $aPassenger['pid'],
            'phone'         => $aPassenger['phone'],
            'city'          => (int)$aAreaInfo['id'],
            'access_key_id' => $iAccessKeyId,
            'app_version'   => $sAppVersion,
            'pid'           => $aPassenger['pid'],
        );

        if (!empty($iCarpoolType)) {
            $aOrderInfo['order_n_tuple']['carpool_type'] = (int)$iCarpoolType;
        }

        if ($oApolloClient->featureToggle('gs_dixiaodi_open_switch',$aApolloParameters)->allow()) {
            $aOrderInfo['order_n_tuple']['level_type'] = 4;
        }

        if ($oApolloClient->featureToggle('spacious_car_alliance_open_city_launch', $aApolloParameters)->allow()) {
            $aOrderInfo['order_n_tuple']['spacious_car_alliance'] = 1;
        }

        $aApolloParameters['product_id'] = $iProductId ?? 0;
        if ($oApolloClient->featureToggle('gs_pay_for_waiting_rule_toggle', $aApolloParameters)->allow()) {
            $aOrderInfo['wait_fee_type'] = 2;
        }
    }

    /**
     * 补充调用 sps getFeeConfig 接口所需的n元组参数
     *
     * @param array  $aOrderNTuple 请求账单入参
     * @param int    $iLevelType levelType,
     * @param int    $iProductId product_id
     * @param int    $iComboType combo_type
     * @return array
     */
    private function _fillOrderNTupleForFeeConfig($aOrderNTuple, $iLevelType, $iProductId, $iComboType) {
        $aOrderNTuple = $aOrderNTuple ?? [];
        $aOrderNTuple['level_type'] = $iLevelType;
        $aOrderNTuple['product_id']  = $iProductId;
        $aOrderNTuple['combo_type']  = $iComboType;
        return $aOrderNTuple;
    }


    /**
     * 调用治理查询超时等待费状态
     *
     * @param array $aOrderInfo
     * @return bool
     */
    private function _isHitWaitingForService($aOrderInfo) {

        $aParam = array(
            'key'           => $aOrderInfo['passenger_id'],
            'pid'           => $aOrderInfo['passenger_id'],
            'city'          => $aOrderInfo['area'],
            'phone'         => $aOrderInfo['passenger_phone'],
            'order_type'    => $aOrderInfo['type'],
            'airport_type'  => $aOrderInfo['airport_type'],
            'railway_type'  => $aOrderInfo['railway_type'],
            'access_key_id' => $aOrderInfo['p_access_key_id'],
            'product_id'    => $aOrderInfo['product_id'],
        );
        if (!Apollo::getInstance()->featureToggle('gs_pay_for_waiting_toggle', $aParam)->allow()) {
            return false;
        }

        $aReq = array(
            'order_info' => $aOrderInfo,
            'event_info' => array('key' => ThemisComLogic::EVENT_LATE_TIME_INFO_BOTH_SIDES, 'channel' => 0),
            'user_info'  => array('uid' => $aOrderInfo['passenger_id'], 'role' => ThemisComLogic::PASSENGER_ROLE),
            'extra_info' => null,
            'caller'     => 'pre-sale',
            'channel'    => 0,
        );

        $aResult = (new DoorGodClient())->passengerQuery($aReq);

        if (isset($aResult['errno']) && 0 != $aResult['errno']) {
            //记录失败请求
            NuwaLog::warning(
                Msg::formatArray(
                    Code::E_COMMON_HTTP_READ_FAIL,
                    [
                        'info'   => 'EventQuery req error',
                        'errno'  => $aResult['errno'],
                        'errmsg' => $aResult['errmsg'],
                        'body'   => json_encode($aReq),
                        'result' => json_encode($aResult),
                    ]
                )
            );
            return false;
        }

        if (!empty($aResult['data'])
            && !empty($aResult['data']['wait_fee_card_info'])
            && !empty($aResult['data']['wait_fee_card_info']['status'])
            && self::WaitingForServiceFinishService == $aResult['data']['wait_fee_card_info']['status']
        ) {
            return true;
        }

        return false;
    }

    /**
     * @param array $aOrderInfo $aOrderInfo
     * @return array
     */
    private function _getSpsFenceID($aOrderInfo) {
        if (ProductCategory::PRODUCT_CATEGORY_TAXI_MARKETISATION_PUTONG == $aOrderInfo['estimate_pc_id']) {
            $sExtendFeature   = $aOrderInfo['extend_feature'];
            $aExtendFeature   = json_decode($sExtendFeature, true);
            $iSpsStartFenceID = $aExtendFeature['neworder_info']['start_fence_id'];
            $iSpsEndFenceID   = $aExtendFeature['neworder_info']['end_fence_id'];

            return [$iSpsStartFenceID, $iSpsEndFenceID];
        }

        return [];
    }

    /**
     * 生成passenger_param参数，结构体参考：https://cooper.didichuxing.com/knowledge/2201113079961/2201343816154
     * @param int   $iProductId 产品线id
     * @param array $aOrderInfo 订单信息
     * @return array
     */
    private function _buildPassengerParam(int $iProductId, array $aOrderInfo): array {
        return [
            'product_id'   => $iProductId,
            'search_param' => [
                'combo_type' => $aOrderInfo['order_n_tuple']['combo_type'],
                'car_level'  => $aOrderInfo['order_n_tuple']['car_level'] ?? $aOrderInfo['order_n_tuple']['require_level'],
            ],
        ];
    }
    /**
     * _getTopImage 获取计价规则页顶部图片
     * @param int    $iCityID 城市id
     * @param string $sLang   语言
     * @param int    $iPcID   品类
     * @return string
     */
    private function _getTopImage($iCityID, $sLang, $iPcID) {
        $aCondition = [
            'product_category' => $iPcID,
            'city'             => $iCityID,
        ];
        $oConfResult = Apollo::getInstance()->getConfigsByNamespaceAndConditions('fee_detail_icon', $aCondition);
        list($bOk, $aAllConfig) = $oConfResult->getAllConfigData();
        if (!$bOk) {
            return '';
        }

        $aConfigs = array_values($aAllConfig);
        if (1 == count($aConfigs)) {
            $aConf = $aConfigs[0];
            return $aConf['rule_car_icon'];
        }

        foreach ($aAllConfig as $sKey => $aConfig) {
            $aKey = explode('_', $sKey);
            if (!is_array($aKey) || 3 > count($aKey)) {
                continue;
            }

            if ($sLang == $aKey[2]) {
                return $aConfig['rule_car_icon'];
            }
        }
        return '';
    }
}
