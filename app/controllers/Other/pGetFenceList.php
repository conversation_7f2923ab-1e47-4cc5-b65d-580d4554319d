<?php

use Biz<PERSON>ib\Client\BillClient;
use Biz<PERSON>ib\Utils\MapHelper;
use Biz<PERSON>ib\Utils\Common as UtilsCommon;
use BizLib\ExceptionHandler;
use BizLib\ErrCode\RespCode;
use BizLib\ErrCode\Code;
use BizLib\Exception\ExceptionWithResp;
use BizCommon\Models\Passenger\Passenger;
use BizLib\Client\OrderSystemClient;
use BizLib\Utils\UtilHelper;

/**
 * Class pGetFenceList.
 */
class PGetFenceListController extends \PreSale\Core\Controller
{
    const NEED_SPS = '1';

    /**
     * 获取围栏列表
     * @return void
     * @throws \Exception 异常
     */
    public function indexAction() {
        try {
            //默认返回值
            $aResponse = UtilsCommon::getErrMsg(RespCode::P_SUCCESS, $this->aErrnoMsg);

            //获取并检查参数
            list($sLang, $sStrategyToken, $sPriceToken, $aAreaInfo, $aOrderInfo, $sNeedSps) = $this->_getAndCheckParams();

            //获取不同场景计价token
            if (!empty($sStrategyToken)) {
                $sRuleToken = $sStrategyToken;
            } elseif (!empty($sPriceToken)) {
                $sRuleToken = $sPriceToken;
            } else {
                //解析得到计价token
                list($sRuleToken) = explode(':', $aOrderInfo['strategy_token']);
            }

            //调用账单服务
            $aBillResult = (new BillClient())->getFenceList(
                2,
                $sLang,
                $aAreaInfo['district'],
                $aAreaInfo['abstract_district'],
                $sRuleToken,
                (int)$aAreaInfo['id']
            );
            if (empty($aBillResult['result']['data'])) {
                throw new \Exception('', RespCode::D_SYSTEM_ERROR);
            }

            $aData = $aBillResult['result']['data'];
            if (self::NEED_SPS == $sNeedSps) {
                $aData = $this->_reformatFenceList($aData);
            }

            //拼装返回值
            $aResponse['data'] = $aData;
        } catch (\Exception $e) {
            //异常处理并得到返回值
            $aResponse = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg,]);
        }

        //输出返回值
        $this->sendTextJson($aResponse);
    }


    /**
     * 格式化返回围栏
     * @param array $aData $aData
     * @return array|string[][]
     */
    private function _reformatFenceList($aData) {
        return array_map(
            function ($aFenceInfo) {
                if (empty($aFenceInfo['sps_start_fence_id'])) {
                    $aFenceInfo['fence_id_both'] = (string)$aFenceInfo['fence_id'];
                    return $aFenceInfo;
                }

                return [
                    'fence_id_both' => $aFenceInfo['sps_start_fence_id'].','.$aFenceInfo['sps_end_fence_id'],
                    'fence_name'    => $aFenceInfo['sps_start_fence_name'].'～'.$aFenceInfo['sps_end_fence_name'],
                ];
            },
            $aData
        );
    }

    /**
     * @desc 参数获取
     * @return mixed
     * @throws ExceptionWithResp 异常
     */
    private function _getAndCheckParams() {
        $sLang          = $this->oRequest->getStr('lang');
        $sFlng          = $this->oRequest->getStr('flng');
        $sFlat          = $this->oRequest->getStr('flat');
        $sToken         = $this->oRequest->getStr('token');
        $sCityId        = $this->oRequest->getStr('city_id');
        $sStrategyToken = $this->oRequest->getStr('strategy_token');
        $sPriceToken    = $this->oRequest->getStr('price_token');
        $sOid           = $this->oRequest->getStr('oid');
        $sNeedSps       = $this->oRequest->getStr('need_sps');

        //token此处是可选参数，如果token不为空，验证token是否合法
        if (!empty($sToken)) {
            $aPassenger = (Passenger::getInstance())->getPassengerByToken($sToken);
            $sPid       = $aPassenger['pid'] ?? 0;
            if (empty($sPid)) {
                throw new ExceptionWithResp(
                    Code::E_COMMON_TOKEN_INVALID,
                    RespCode::P_PARAMS_ERROR,
                    '',
                    [
                        'exceptionMsg' => [
                            'msg'      => 'pid is empty (token is invalid)',
                            'moreInfo' => [
                                'args' => (array) $this,
                                'pid'  => $sPid,
                            ],
                        ],
                    ]
                );
            }
        }

        //验证strategy_token
        if (empty($sStrategyToken) && empty($sPriceToken) && empty($sOid)) {
            throw new ExceptionWithResp(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                RespCode::P_PARAMS_ERROR,
                '',
                [
                    'exceptionMsg' => [
                        'msg'      => 'strategy token, price token, and oid all is empty',
                        'moreInfo' => [
                            'strategy_token' => $sStrategyToken,
                            'price_token'    => $sPriceToken,
                            'oid'            => $sOid,
                        ],
                    ],
                ]
            );
        }

        //如果经纬度不为空，根据地经纬度获取区域信息，否走用传入的城市ID和区县ID，再否则抛异常
        if (!empty($sFlng) && !empty($sFlat)) {
            $aAreaInfo = MapHelper::getAreaInfoByLoc($sFlng, $sFlat);
        } elseif (!empty($sCityId)) {
            $aAreaInfo = MapHelper::getAreaInfoByAreaId($sCityId);
        } else {
            throw new ExceptionWithResp(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                RespCode::P_PARAMS_ERROR,
                '',
                [
                    'exceptionMsg' => [
                        'msg'      => 'flng, flat， city id is empty',
                        'moreInfo' => [
                            'flng'    => $sFlng,
                            'flat'    => $sFlat,
                            'city_id' => $sCityId,
                        ],
                    ],
                ]
            );
        }

        if (empty($aAreaInfo['district'])) {
            throw new ExceptionWithResp(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                RespCode::P_PARAMS_ERROR,
                '',
                [
                    'exceptionMsg' => [
                        'msg'      => 'district id is empty',
                        'moreInfo' => [
                            'area_info'   => $aAreaInfo,
                            'district_id' => $aAreaInfo['district'],
                        ],
                    ],
                ]
            );
        }

        //Abstract district
        if (!empty($aAreaInfo['countyid'])) {
            $aAreaInfo['abstract_district'] = $aAreaInfo['district'] . ',' . $aAreaInfo['countyid'];
        }

        $aOrderInfo = [];
        if (!empty($sOid)) {
            $aOrderInfo   = UtilHelper::decodeId($sOid);
            $iOrderId     = $aOrderInfo['oid'];
            $sDistrict    = $aOrderInfo['district'];
            $oOrderSystem = new OrderSystemClient();
            $aOrderInfo   = $oOrderSystem->getOrderInfo($iOrderId, $sDistrict);
            $aOrderInfo   = $aOrderInfo['result']['order_info'];
        }

        if (empty($aAreaInfo['id'])) {
            $aAreaInfo['id'] = $sCityId;
        }

        return [$sLang, $sStrategyToken, $sPriceToken, $aAreaInfo, $aOrderInfo, $sNeedSps];
    }

    /**
     * 是否通过入参token获取
     * @param array $aOrderInfo order_info
     * @return bool
     */
    private function _isGetByToken($aOrderInfo) {
        if (empty($aOrderInfo)) {
            return true;
        }

        if (\BizLib\Constants\OrderSystem::ST_UNSTRIVED == $aOrderInfo['order_status']) {
            return true;
        }

        return false;
    }
}
