<?php

use BizCommon\Logics\RegionalGrowth\ConfigLogic;
use BizCommon\Utils\Common;
use BizCommon\Utils\Version;
use BizLib\Config as NuwaConfig;
use BizLib\Constants;
use BizLib\ErrCode\RespCode;
use BizLib\ExceptionHandler;
use BizLib\Utils\BizConfigLoader;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\Language;
use BizLib\Utils\MapHelper;
use BizLib\Utils\Product;
use BizLib\Utils\UtilHelper;
use Xiaoju\Apollo\Apollo as ApolloV2;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Domain\Service\KFlowerConfig\KFConfig;
use PreSale\Models\carrera\LoginPassengerOpenAppTopic;
use TripcloudCommon\Constants\ApolloConfConstant;
use TripcloudCommon\Utils\ApolloConf;
use TripcloudCommon\Utils\TCAppVersion;

/*
 *
 * @authors <PERSON> (<EMAIL>)
 * @oldAuthors wangxia<PERSON>@diditaxi.com.cn
 * @date    2017-07-07 18:53:54
 * @desc    专车配置接口，返回发票类型，投诉类型，评价类型等配置信息。
 * @wiki    http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=71898379
 */

class PGetConfigController extends \PreSale\Core\Controller
{
    /**
     * 乘客端配置.
     *
     * @param int $iVersion 客户端存储的版本号use
     *                      return array(
     *                      version =>  1,//当前配置版本号
    invoiceType  =>  array(//发票类型
    '叫车服务费',...
    ),
    complaintType   =>  array(//投诉类型
    '司机迟到',...
    ),
    commentType =>  array(
    'commentTips'  =>  '您的评价，让我们做得更好',
    'goodCmtTips'   =>  '请监督司机是否存在以下问题',
    '   badCmtTips'    =>  '请把您的不满告诉我们',
    'good'  =>  array(
    '为我开车门',...//好评类型
    ),
    'good'  =>  array(
    '司机迟到',...//差评类型
    ),
    ),
     *)
     */

    const VERSION = 130;        //版本号

    const KF_VERSION        = 2;
    const LANG_CODE_STEP    = 1000;
    const MINI_FEE_DOUBT_H5 = 'https://help.xiaojukeji.com/static/im/index.html';

    private $_bUnionePickUpByMeterCity = false;

    /**
     * @return void
     */
    public function indexAction() {
        try {
            list($iOriginId, $iTerminalId, $iVersion, $iCityId, $iAccessKeyId) = $this->_getParams();
            $iCurVersion        = $this->getLanguageCode($iAccessKeyId);
            $iCurVersion        = $iCurVersion + $this->getPushVersion() + $this->getTCAppVersion();
            $aResult            = UtilsCommon::getErrMsg(RespCode::P_SUCCESS);
            $aResult['version'] = $iCurVersion;
            $aResult['airport_number_switch'] = NuwaConfig::config('config_passenger', 'airport_number_switch');

            //打表来接城市 版本号+1
            $this->_checkUnionePickUpByMeterCity($iOriginId, $iTerminalId, $iVersion, $iCityId);
            if ($this->_bUnionePickUpByMeterCity) {
                $iCurVersion        = $iCurVersion + 1;
                $aResult['version'] = $iCurVersion;
            }

            if ($iVersion == $iCurVersion) {
                $aResult['errno'] = (int)($aResult['errno']);
                $this->sendTextJson($aResult);

                fastcgi_finish_request();
                $this->_postRequest();
                return;
            }
            $aResult['wyc_dyc_business']      = $this->_getWycBusiness();
            $aResult['wyc_productline_maps']  = $this->_getWycBusinessMap();

            $aPassengerConfig   = $this->_getPassengerConfig($iOriginId, $iTerminalId, $iCityId, $iAccessKeyId);
            $aCarLevelMapConfig = $this->_getCarLevelMapConfig();
            $aAirportConfig     = $this->_getAirPortConfig();
            $aOtherConfig       = $this->_getOtherConfig();
            $aResult            = array_merge($aResult, $aPassengerConfig, $aCarLevelMapConfig, $aAirportConfig, $aOtherConfig);
            //降级预案执行
            $aResult = $this->_lowerRank($aResult, $iCurVersion);
        } catch (\Exception $e) {
            $aResult = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg, ]);
        }

        $aResult['errno'] = (int)($aResult['errno']);
        $this->sendTextJson($aResult);

        fastcgi_finish_request();
        $this->_postRequest();
    }

    /**
     * @return void
     */
    private function _postRequest() {
        $sToken        = $this->getRequest()->fetchGetPost('token', false);
        $fLat          = $this->getRequest()->fetchGetPost('lat', false);
        $fLng          = $this->getRequest()->fetchGetPost('lng', false);
        $iCityId       = $this->getRequest()->fetchGetPost('city_id', 0);
        $iChannel      = $this->getRequest()->fetchGetPost('channel', false);
        $iAccessKeyId  = $this->getRequest()->fetchGetPost('access_key_id', false);
        $iPlatformType = $this->getRequest()->fetchGetPost('platform_type', false);
        $iClientType   = $this->getRequest()->fetchGetPost('client_type', false);
        $sImei         = $this->getRequest()->fetchGetPost('imei', '');
        $sDdOaid       = $this->getRequest()->fetchGetPost('dd_oaid', '');
        $sIdfa         = $this->getRequest()->fetchGetPost('idfa', '');
        $sOpenId       = $this->getRequest()->fetchGetPost('openid', '');
        $sDchn         = $this->getRequest()->fetchGetPost('dchn', '');
        $iPhone        = 0;
        if (empty($sToken)) {
            return;
        }

        //1.获取乘客信息
        $oPassenger = \BizCommon\Models\Passenger\Passenger::getInstance();
        $aPassenger = $oPassenger->getPassengerByToken($sToken);
        if (isset($aPassenger['phone'])) {
            $iPhone = $aPassenger['phone'];
        }

        $iPid        = !empty($aPassenger['pid']) ? $aPassenger['pid'] : 0;
        $aCityInfo   = MapHelper::getAreaInfoByLoc($fLng, $fLat);
        $aTopicParam = [
            'passenger_id'  => $aPassenger['pid'],
            'city_id'       => $aCityInfo['id'] ?? $iCityId,
            'lat'           => $fLat,
            'lng'           => $fLng,
            'phone'         => $iPhone,
            'district'      => $aCityInfo['district'],
            'channel'       => $iChannel,
            'access_key_id' => $iAccessKeyId,
            'platform_type' => $iPlatformType,
            'client_type'   => $iClientType,
            'imei'          => $sImei,
            'dd_oaid'       => $sDdOaid,
            'idfa'          => $sIdfa,
            'openid'        => $sOpenId,
            'dchn'          => $sDchn,
        ];

        $oTopic = new LoginPassengerOpenAppTopic();
        $oTopic->sync($aTopicParam);
    }

    /**
     * @desc 霸王花客户端获取配置接口
     * @return void
     */
    public function kFlowerConfigAction() {
        try {
            list($iOriginId, $iTerminalId, $iVersion, $iCityId, $iAccessKeyId) = $this->_getParams();
            $iCurVersion        = $this->getLanguageCode($iAccessKeyId);
            $iCurVersion        = $iCurVersion + Version::getCurrVersion($this->getRequest()->getControllerName());
            $aResult            = UtilsCommon::getErrMsg(RespCode::P_SUCCESS);
            $aResult['version'] = $iCurVersion;

            if ($iVersion == $iCurVersion) {
                $aResult['errno'] = (int)($aResult['errno']);
                $this->sendTextJson($aResult);

                return;
            }

            $oKFlowerConfig     = new KFConfig();
            $aPassengerConfig   = $oKFlowerConfig->getPassengerConfig($iCityId);
            $aCarLevelMapConfig = $oKFlowerConfig->getCarLevelConfig();
            $aOtherConfig       = $oKFlowerConfig->getOtherConfig();
            $aResult            = array_merge($aResult, $aPassengerConfig, $aCarLevelMapConfig, $aOtherConfig);
            //降级预案执行
            $aResult = $this->_lowerRank($aResult, $iCurVersion);
        } catch (\Exception $e) {
            $aResult = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg, ]);
        }

        $aResult['errno'] = (int)($aResult['errno']);
        $this->sendTextJson($aResult);

        fastcgi_finish_request();
        $this->_postRequest();
    }

    /**
     * 是否是出租车三公里打表来接开城城市
     *
     * @param int $iCityId
     *
     * @return bool
     */
    private function _checkUnionePickUpByMeterCity($iOriginId, $iTerminalId, $iVersion, $iCityId) {
        if ($this->_bUnionePickUpByMeterCity) {
            return;
        }

        if (!empty($iCityId) && 0 != $iCityId
            && (new ApolloV2())->featureToggle(
                'unione_pickup_by_meter',
                array(
                    'key'         => time(),
                    'city'        => $iCityId,
                    'iOriginId'   => $iOriginId,
                    'iTerminalId' => $iTerminalId,
                    'iVersion'    => $iVersion,
                )
            )->allow()
        ) {
            $this->_bUnionePickUpByMeterCity = true;
        }
    }

    /**
     * 降级预案执行.
     *
     * <AUTHOR>
     * @datetime 2017-07-20T18:42:43+0800
     *
     * @param array $aResult     返回值
     * @param int   $iCurVersion 当前版本
     *
     * @return array
     */
    private function _lowerRank($aResult, $iCurVersion) {
        $aSwitchOpenInfo = UtilHelper::getSwitchOpenInfo('passenger_time_interval_switch');
        if (isset($aSwitchOpenInfo['open']) && !empty($aSwitchOpenInfo['open'])) {
            if (isset($aSwitchOpenInfo['time_interval']) && !empty($aSwitchOpenInfo['time_interval']) && isset($aSwitchOpenInfo['version_offset']) && !empty($aSwitchOpenInfo['version_offset'])) {
                $aResult            = array_merge($aResult, $aSwitchOpenInfo['time_interval']);
                $aResult['version'] = $iCurVersion + (int)($aSwitchOpenInfo['version_offset']);
            }
        }

        return $aResult;
    }

    /**
     * 乘客端会缓存配置,如果切换语言会到导致缓存不更新,此处会根据语言返回一个版本号,来刷客户端的缓存.
     *
     * <AUTHOR>
     * @datetime 2017-07-26T15:20:57+0800
     *
     * @param string $sLanguage 语种
     *
     * @return int
     */
    public function getLanguageCode($iAccessKeyId, $sLanguage = '') {
        $aSupportLang = Language::getSupportLanguages();
        if (empty($sLanguage) || !in_array($sLanguage, $aSupportLang)) {
            $sLanguage = $this->getRequest()->fetchGetPost('lang', false);
            if (empty($sLanguage) || !in_array($sLanguage, $aSupportLang)) {
                $sHeaderLang = Language::getHeaderLanguage();
                if (isset($sHeaderLang) && in_array($sHeaderLang, $aSupportLang)) {
                    $sLanguage = $sHeaderLang;
                } else {
                    $sLanguage = Language::getLocalLanguage();
                }
            }
        }

        $aSupportLang = array_flip($aSupportLang);
        if (\BizCommon\Utils\Common::isFromKFlower($iAccessKeyId)) {
            $iVersion = self::KF_VERSION + self::LANG_CODE_STEP * (int)($aSupportLang[$sLanguage]);
        } else {
            $iVersionConfig = (int)Language::getTextFromDcmp('config_text-get_config_version');
            if (empty($iVersionConfig)) {
                $iVersion = self::VERSION + self::LANG_CODE_STEP * (int)($aSupportLang[$sLanguage]);
            } else {
                $iVersion = $iVersionConfig + self::LANG_CODE_STEP * (int)($aSupportLang[$sLanguage]);
            }
        }

        return $iVersion;
    }

    /**
     * @return int
     */
    public function getPushVersion() {
        return BizConfigLoader::getPGetConfigVersion();
    }

    /**
     * @return int
     */
    public function getTCAppVersion() {
        return TCAppVersion::getTCAppVersion();
    }

    /**
     * @return string
     */
    public function getAppVersion() {
        return $this->getRequest()->fetchGetPost('appversion', false);
    }

    /**
     * 获取config_passenger中的配置.
     *
     * <AUTHOR>
     * @datetime 2017-07-10T10:50:31+0800
     *
     * @param int $iOriginId   [description]
     * @param int $iTerminalId 终端id
     * @param int $iCityId     城市ID
     *
     * @return array
     */
    private function _getPassengerConfig($iOriginId, $iTerminalId, $iCityId, $iAccessKeyId) {
        $aResult = [];

        //根据端设定不同的乘客配置
        $aConfig = NuwaConfig::config('config_passenger', 'passenger_origin_config');
        if (!empty($iOriginId) && array_key_exists($iOriginId, $aConfig)) {
            $aResult = $aConfig[$iOriginId];
        }

        $aPassengerConfig = NuwaConfig::config('config_passenger', 'config_passenger');
        //h5链接配置化 richeal
        if (\BizCommon\Utils\Common::isFromKFlower($iAccessKeyId)) {
            $iProductId = \BizLib\Utils\Product::PRODUCT_ID_K_FLOWER;
        } else {
            $iProductId = Product::getDefaultProductIdByTerminalId($iOriginId, $iTerminalId);
        }

        $aPassengerConfig['estimate_detail_url'] = UtilHelper::getConfigUrl('estimate_detail_url', $iProductId);
        $aPassengerConfig['hk_taxi_rule_url']    = UtilHelper::getConfigUrl('hk_taxi_rule_url', $iProductId);
        $aPassengerConfig['fee_detail_h5']       = UtilHelper::getConfigUrl('fee_detail_h5', $iProductId);
        $aPassengerConfig['cancel_trip_url']     = UtilHelper::getConfigUrl('cancel_trip_url', $iProductId);
        $aPassengerConfig['passenger_cancel_trip_reason_page'] = UtilHelper::getConfigUrl('passenger_cancel_trip_reason_page', $iProductId);
        //
        //url切换   临时方案  年后下掉
        //
        if (Constants\OrderSystem::PRODUCT_ID_FAST_CAR == $iProductId) {
            $aPassengerConfig['cancel_trip_url'] = 'https://page.udache.com/passenger/apps/cancel-trip-new/index.html';
            $aPassengerConfig['passenger_cancel_trip_reason_page'] = 'https://page.udache.com/passenger/apps/cancel-reason-new/index.html';
        }

        $aPassengerConfig['cancel_rule_h5_url'] = UtilHelper::getConfigUrl('cancel_rule_h5_url', $iProductId);
        $aApolloParam   = [
            'key'        => time(),
            'product_id' => $iProductId,
            'city'       => $iCityId,
            'pos_type'   => 'passenger_final_page_cancel',
        ];
        $oFeatureToggle = Apollo::getInstance()->featureToggle('gs_new_cancel_rule_link', $aApolloParam);
        if ($oFeatureToggle->allow()) {
            $aPassengerConfig['cancel_rule_h5_url'] = $oFeatureToggle->getParameter('link', '');
        }

        $aPassengerConfig['cpf_auth_h5_url'] = UtilHelper::getConfigUrl('cpf_auth_h5_url', $iProductId);
        $aPassengerConfig['carpool_late_fee_detail_url']      = UtilHelper::getConfigUrl('carpool_late_fee_detail_url');
        $aPassengerConfig['carpool_late_fee_packet_rule_url'] = UtilHelper::getConfigUrl('carpool_late_fee_packet_rule_url');
        $aResult['real_time_fee_detail_url'] = UtilHelper::getConfigUrl('real_time_fee_detail_url', $iProductId);
        //跨城拼车文案,由于格式不一样端上维护比价麻烦，采用添加新的字段
        $aResult['product_introduce']     = array(
            'tips'      => NuwaConfig::text('config_passenger', 'inter_city_carpool_url'),
            'url_links' => UtilHelper::getConfigUrl('inter_city_carpool_url', $iProductId),
        );
        $aResult['product_introduce_new'] = $this->_getInterCityProductIntroduce($iProductId);
        $aInterCityConfig = \BizCommon\Logics\Carpool\InterCarpoolCity::getCityAllConf($iCityId);
        //城际新手教育页
        $aPassengerConfig['intercity_carpool_guide_url'] = $aInterCityConfig['fresh_education'] ?? $aPassengerConfig['intercity_carpool_guide_url'];
        //小程序不认可取消费
        if (Constants\Common::DIDI_ALIPAY_MINI_PROGRAM == $iAccessKeyId || Constants\Common::DIDI_WECHAT_MINI_PROGRAM == $iAccessKeyId) {
            $sAppVersion = $this->getAppVersion();
            $oFeatureToggle = Apollo::getInstance()->featureToggle(
                'fee_doubt_h5_mini_switch_toggle',
                [
                    'key'           => time(),
                    'app_version'   => $sAppVersion,
                    'access_key_id' => $iAccessKeyId,
                ]
            );
            if ($oFeatureToggle->allow()) {
                $aPassengerConfig['fee_doubt_h5'] = $oFeatureToggle->getParameter('fee_doubt_h5', self::MINI_FEE_DOUBT_H5);
            }
        }
        // 稍话
        $aResult['extraInfo'] = NuwaConfig::text('config_passenger', 'extraInfo');

        // 区域渗透资源下发
        if (Common::isRegionalWeChatMiniProgram($iAccessKeyId)) {
            $oFeatureToggle = Apollo::getInstance()->featureToggle(
                'regional_growth_price_insurance',
                [
                    'key'        => time(),
                    'product_id' => $iProductId,
                    'city'       => $iCityId,
                ]
            );

            // 冒泡资源位
            $aPassengerConfig['regional_growth_estimate_banner_img_url'] = ConfigLogic::getConfig('estimate_banner_img', $iCityId)['image_url'] ?? '';
            // 贵必赔
            $aPassengerConfig['regional_growth_price_insurance_url'] = $oFeatureToggle->allow() ? $oFeatureToggle->getParameter('link', '') : '';
            // 行程中价格凸显
            $aPassengerConfig['regional_growth_price_perception_img_url'] = ConfigLogic::getConfig('price_perception_img', $iCityId)['image_url'] ?? '';
            // 订单结束页价格凸显
            $aPassengerConfig['regional_growth_price_discount_img_url'] = ConfigLogic::getConfig('price_discount_img', $iCityId)['image_url'] ?? '';
        }

        //出租车打表来接开城
        if ($this->_bUnionePickUpByMeterCity) {
            foreach ($aResult['extraInfo'] as $index => &$aConfig) {
                if (0 == $aConfig['type'] && 307 == $aConfig['product']) {
                    $sPickUpText = Language::getTextFromDcmp('config_passenger-unione_pickup_by_meter');
                    array_push($aConfig['data'], $sPickUpText);
                    break;
                }
            }
        }

        $aResult = array_merge($aPassengerConfig, $aResult);

        return $aResult;
    }

    /**
     * 从dcmp获取城际拼车的product_interduce_new
     * 由于以前的product_interduce的结构没有区分城市，字段结构和product_interduce_new
     * 不同，端上不想复用product_interduce避免维护麻烦，新起字段product_interduce_new
     * 来返回分城市的信息
     *
     * @param int $iProductId 参数
     * @return array | mixed
     */
    private function _getInterCityProductIntroduce($iProductId) {
        //默认的类型兜底
        $aProductIntro = [
            'default' => [
                'tips'      => NuwaConfig::text('config_passenger', 'inter_city_carpool_url'),
                'url_links' => UtilHelper::getConfigUrl('inter_city_carpool_url', $iProductId),
            ],
        ];

        //dmc获取文案和url
        $sProductIntro = Language::getTextFromDcmp('config_inter_carpool-product_introduce', []);
        if (empty($sProductIntro)) {
            return $aProductIntro;
        }

        $aProductIntro = json_decode($sProductIntro, true);
        if (empty($aProductIntro)) {
            return $aProductIntro;
        }

        return $aProductIntro;
    }

    /**
     * 获取car_level_mapping中的配置.
     *
     * <AUTHOR>
     * @datetime 2017-07-10T10:50:31+0800
     *
     * @return array
     */
    private function _getCarLevelMapConfig() {
        $aResult = NuwaConfig::config('car_level_mapping', 'car_level_mapping');
        //新版乘客端会用新配置
        //$aResult['car_level_map'] = $this->config->item('biz_car_level_mapping', 'car_level_mapping');
        $aResult['car_level_map'] = \BizLib\Utils\CarLevel::getCarLevelMapping();
        //wiki -- http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=132281414
        //258是专车公共产品线，1402为豪华车车型
        //添加目的为专车下开放豪车入口，形成豪华车的双入口
        $apolloV2 = new \Xiaoju\Apollo\Apollo();
        if ($apolloV2->featureToggle('gs_zhuanche_luxlevel_switch', array('key' => mt_rand(1, 1000), 'switch_id' => '1'))->allow()) {
            if (isset($aResult['car_level_map']['258'])) {
                $aLuxLevel = array('level_id' => 1402, 'level_type' => 4);
                array_push($aResult['car_level_map']['258'], $aLuxLevel);
            }
        }

        return $aResult;
    }

    /**
     * 获取config_airport和message中相关机场的配置.
     *
     * <AUTHOR>
     * @datetime 2017-07-10T10:50:31+0800
     *
     * @return array
     */
    private function _getAirPortConfig() {
        $aResult['header_conf'] = $aResult['yunying_conf'] = [];
        //机场推荐上车点指导文案 在message的config文件里
        $rs = NuwaConfig::config('message');
        $aResult['airport_recommend_poi_title']    = $rs['message']['airport_recommend_poi_title'];
        $aResult['airport_recommend_poi_subtitle'] = $rs['message']['airport_recommend_poi_subtitle'];
        return $aResult;
    }

    /**
     * 设置其他配置项.
     *
     * <AUTHOR>
     * @datetime 2017-07-10T10:50:31+0800
     *
     * @return array
     */
    private function _getOtherConfig() {
        $aResult['project_info'] = [
            'project_icon_url'           => 'https://static.udache.com/gulfstream/api/passenger/activity/<EMAIL>',
            'driver_icon_url'            => 'https://static.udache.com/gulfstream/api/passenger/activity/<EMAIL>',

            'project_icon_url_sp'        => 'https://static.udache.com/gulfstream/api/passenger/activity/<EMAIL>',
            'driver_icon_url_sp'         => 'https://static.udache.com/gulfstream/api/passenger/activity/<EMAIL>',

            'project_icon_android'       => 'https://static.udache.com/gulfstream/api/passenger/activity/android_home1080_1.png',
            'driver_icon_android'        => 'https://static.udache.com/gulfstream/api/passenger/activity/android_fu1080.png',

            'project_icon_android_sp'    => 'https://static.udache.com/gulfstream/api/passenger/activity/android_home720.png',
            'driver_icon_android_sp'     => 'https://static.udache.com/gulfstream/api/passenger/activity/android_fu720.png',

            'project_icon_android_third' => 'https://static.udache.com/gulfstream/api/passenger/activity/android_home480.png',
            'driver_icon_android_third'  => 'https://static.udache.com/gulfstream/api/passenger/activity/android_fu480.png',
        ];
        //司机迟到
        $aResult['driver_late_bubble_msg'] = NuwaConfig::text('config_passenger', 'driver_late_bubble_msg');
        //拼车终点时间buffer配置
        $aResult['carpool_buff'] = rand(10, 15);

        //司机感谢费,乘客引导文案
        $aTipMsg = NuwaConfig::text('config_tip_fee', 'tip_fee');
        $aResult['thanks_tips'] = $aTipMsg['guide_msg'];

        // 巴西一口价提示文案
        $aFixPriceConf        = NuwaConfig::text('config_alert_passenger', 'fixed_price_communication');
        $aPriceChangeReminder = NuwaConfig::text('config_alert_passenger', 'price_change_reminder');
        if (!empty($aFixPriceConf) && !empty($aPriceChangeReminder)) {
            $aResult['fixed_price_conf'] = [
                'fixed_price_communication' => $aFixPriceConf,
                'price_change_reminder'     => $aPriceChangeReminder,
            ];
        }

        //端上历史订单点击后，是根据订单的产品ID跳转的，后续可能会不断接入不同的第三方运力。
        //为了接入方便，所有第三方产品线会统一映射到快车产品线上，走的是apollo配
        //置，这里返回了该映射关系
        $aResult['tripcloud_productline_maps'] = $this->_getTripcloudInfo();

        //StopOverPoint Tips
        $aResult['stopover_point_tips'] = $this->_getStopOverPointTips();

        return $aResult;
    }

    /**
     * 获取第三方订单，button_control 详情
     *
     * @param array $aOrderInfo 订单信息
     *
     * @return array
     */
    private function _getTripcloudInfo() {
        $aRet    = array();
        $aConfig = ApolloConf::getTripcloudProductlineMaps();

        if (!empty($aConfig)) {
            $aRet = $aConfig;
        }

        return $aRet;
    }

    /**
     * 获取参数并校验.
     *
     * <AUTHOR>
     * @datetime 2017-06-13T20:58:33+0800
     *
     * @return array
     */
    private function _getParams() {
        $iOriginId    = $this->getRequest()->fetchGetPost('origin_id', false);
        $iTerminalId  = $this->getRequest()->fetchGetPost('terminal_id', false);
        $iVersion     = intval($this->getRequest()->getQuery('version', false));
        $iAccessKeyId = intval($this->getRequest()->getQuery('access_key_id', 0));

        $iCityId = $this->getRequest()->getHeader('Cityid');
        if (empty($iCityId) || 0 == $iCityId) {
            $iCityId = $this->getRequest()->fetchGetPost('city_id', false);
        }

        return [$iOriginId, $iTerminalId, $iVersion, $iCityId, $iAccessKeyId];
    }

    /**
     * 获取途经点提示文案
     * @return array|mixed
     */
    private function _getStopOverPointTips() {
        $aTips = [];
        $sTips = Language::getTextFromDcmp('config_activity-stopover_point_tips');
        if (\Dirpc\SDK\Dcmp\Constants::EMPTY_CONTENT != $sTips) {
            $aTips = json_decode($sTips,true);
        }

        return $aTips;
    }

    /**
     * 获取网约车业务
     * @return array|mixed
     */
    private function _getWycBusiness() {
        $sWycBusiness = Language::getTextFromDcmp('config_text-wyc_dyc_business');
        if (!empty($sWycBusiness)) {
            return json_decode($sWycBusiness,true);
        }

        return [];
    }

    /**
     * 获取网约车业务
     * @return array|mixed
     */
    private function _getWycBusinessMap() {
        $sWycBusiness = Language::getTextFromDcmp('config_text-wyc_productline_maps');
        if (!empty($sWycBusiness)) {
            return json_decode($sWycBusiness,true);
        }

        return [];
    }
}
