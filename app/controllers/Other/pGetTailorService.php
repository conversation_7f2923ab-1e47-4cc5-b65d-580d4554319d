<?php
/**
 * <AUTHOR> <<EMAIL>>
 */

use BizCommon\Models\Passenger\Passenger;
use BizLib\Utils;
use BizLib\Utils\MapHelper;
use BizLib\Utils\Product;
use Dirpc\SDK\PreSale\LuxMultiEstimatePriceRequest as Request;
use Dirpc\SDK\PreSale\LuxMultiEstimatePriceResponse as Response;
use PreSale\Logics\estimatePrice\OptionServiceLogic;
use PreSale\Logics\estimatePriceV2\PersonalizedCustomServiceLogic;
use PreSale\Logics\luxEstimatePrice\multiRequest\PriceLogic;
use PreSale\Logics\luxEstimatePrice\multiRequest\ProductList;
use PreSale\Logics\luxEstimatePrice\multiRequest\UserPreferInfoLogic;
use PreSale\Logics\luxEstimatePrice\multiResponse\MainRender;
use PreSale\Logics\luxEstimatePrice\multiResponse\MultiEstimatePublicLogV2;
use BizLib\Config as NuwaConfig;
use BizLib\ExceptionHandler;

/**
 * Class PGetTailorServiceController
 */
class PGetTailorServiceController extends \PreSale\Core\Controller
{

    // 调用源为v1接口
    const SOURCE_OF_PREFER_REQUEST = 1;

    /**
     * @return void
     */
    public function indexAction() {
        \Nuwa\Protobuf\Internal\Message::setEmitDefaults(true);
        try {
            //参数初始化
            $oRequest         = Utils\Request::getInstance();
            $oEstimateRequest = new Request();
            $oEstimateRequest->mergeFromJsonArray($oRequest->get());

            $aPassengerInfo = Passenger::getInstance()->getPassengerByToken($oEstimateRequest->getToken());
            $aAreaInfo      = MapHelper::getAreaInfoByLoc((float)($oEstimateRequest->getFromLng()), (float)($oEstimateRequest->getFromLat()));
            if (empty($aAreaInfo['id'])) {
                $aAreaInfo = MapHelper::getAreaInfoByLoc((float)($oEstimateRequest->getLng()), (float)($oEstimateRequest->getLat()));
            }

            $iBusinessId        = $oEstimateRequest->getBusinessId();
            $aResponseParamsNew = [];
            $aRenderInfo        = [];
            $aParams            = [
                'app_version' => $oEstimateRequest->getAppversion(),
                'product_id'  => Product::getProductIdByBusinessId($iBusinessId),
                'lang'        => $oEstimateRequest->getLang(),
                'business_id' => $iBusinessId,
                'pid'         => $aPassengerInfo['pid'],
                'uid'         => $aPassengerInfo['uid'],
                'phone'       => $aPassengerInfo['phone'],
                'from_kind'   => OptionServiceLogic::FORM_KIND_GET,
            ];

            if (in_array(
                $iBusinessId,
                [
                    Product::COMMON_PRODUCT_ID_FIRST_CLASS_CAR,
                    Product::COMMON_PRODUCT_ID_BUSINESS_LUXURY,
                ]
            )
            ) {
                //构建请求数据
                $oProductList = new ProductList($oEstimateRequest);
                $aProductList = $oProductList->buildProductList();

                //获取账单、支付方式、优惠券、乘客运营活动券数据
                if (!empty($aProductList)) {
                    //获取账单、支付方式、优惠券、乘客运营活动券数据
                    $oPriceLogic        = new PriceLogic();
                    $aResponseParamsNew = $oPriceLogic->getMultiResponse($aProductList);
                }
                //获取用户偏好设置选项
                $oUserPreferLogic = new UserPreferInfoLogic();
                $aUserPreferInfo  = $oUserPreferLogic->getUserPreferResponse($aProductList);

                // 渲染对端数据之前，标识当前调用的是v1的偏好设置页面接口
                $aUserPreferInfo['prefer_request_source'] = self::SOURCE_OF_PREFER_REQUEST;

                //渲染对端数据
                $aRenderInfo = MainRender::getInstance($aResponseParamsNew, $aUserPreferInfo)->multiExecute();
                if (Product::COMMON_PRODUCT_ID_FIRST_CLASS_CAR == $iBusinessId) {
	                // 豪华车接入彩蛋
                    $aParams['oid'] = $oEstimateRequest->getOid();
	                // 小程序之类的第一次调用豪华车偏好设置页时，不会传require_level，所以加上默认值1000-任意豪华车
                    $aParams['require_level'] = ($oEstimateRequest->getRequireLevel()) ?: 1000;
                    $aParams['area']          = $aAreaInfo['id'] ?? 0;
                    $aParams['access_key_id'] = ($oEstimateRequest->getAccessKeyId()) ?: 0;
                    $aRenderInfo = OptionServiceLogic::setActivityData($aRenderInfo, $aParams);
                }
            } elseif (Product::COMMON_PRODUCT_ID_HK_TAXI == $iBusinessId) {
                $aRenderInfo = OptionServiceLogic::getHkService($aParams);
            } else {
                $aParams['oid']           = $oEstimateRequest->getOid();
                $aParams['require_level'] = $oEstimateRequest->getRequireLevel();
                $aParams['area']          = $aAreaInfo['id'] ?? 0;

                $aRenderInfo = OptionServiceLogic::getServiceV2($aParams);
            }

            //格式化输出
            $sCustomFeatures     = $oEstimateRequest->getCustomFeature() ?? null;
            $aCustomFeatures     = json_decode($sCustomFeatures, true);
            $oCustomServiceLogic = PersonalizedCustomServiceLogic::getInstance();
	        // 获取ssse的增值化服务数据
            $oCustomServiceLogic->loadCustomServiceSecondaryPage($oEstimateRequest, $aPassengerInfo, $aAreaInfo);
            $aCustomInfo = $oCustomServiceLogic->buildSsseCustomService($aCustomFeatures, $oEstimateRequest, $aPassengerInfo, $aAreaInfo);
            if (!empty($aCustomInfo)) {
                $aRenderInfo['data'] = array_merge($aCustomInfo, $aRenderInfo['data']);
            }

            $oResponse = new Response();
            $oResponse->mergeFromJsonArray($aRenderInfo);
            $aResponseInfo = $oResponse->serializeToJsonArray();
        } catch (Exception $e) {
            $aErrMsg       = NuwaConfig::text('errno', 'pEstimatePrice_error_msg');
            $oHandler      = ExceptionHandler::getInstance();
            $aResponseInfo = $oHandler->handleException($e, ['err_msg' => $aErrMsg,]);
            $this->sendJson($aResponseInfo, (string)($this->getRequest()->getQuery('callback', false)));
            return;
        }

        $this->sendJson($aResponseInfo, (string)($this->getRequest()->getQuery('callback', false)));
        fastcgi_finish_request();

        (new MultiEstimatePublicLogV2($aResponseParamsNew, $aResponseInfo, $aProductList))->multiWritePublicLog();
    }
}
