<?php

use <PERSON>izCommon\Models\Passenger\Passenger;
use Biz<PERSON>ib\Client\OrderSystemClient;
use BizLib\Utils\Common;
use BizLib\Config;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Exception\InvalidArgumentException;
use BizLib\ExceptionHandler;
use BizLib\Libraries\RedisDB;
use BizLib\Log;
use BizLib\Utils\Request;
use PreSale\Core\Controller;
use PreSale\Logics\carpool\InvitationCarpool;
use Dirpc\SDK\PreSale\GetInvitationInfoRequest;
use Dirpc\SDK\PreSale\GetInvitationInfoResponse;
use BizLib\Utils\PublicLog;
use BizLib\Utils\UtilHelper;

/**
 * Class PGetInvitationInfoController
 */
Class PGetInvitationInfoController extends Controller
{
    const INVITATION_UV_RECORD_EXPIRE = 24 * 60 * 60;

    /**
     * @return void
     */
    public function init() {
        parent::init();
    }

    /**
     * @return void
     * @throws \Nuwa\Protobuf\Internal\GPBDecodeException comment
     */
    public function indexAction() {
        \Nuwa\Protobuf\Internal\Message::setEmitDefaults(true);
        try {
            $aRet           = Common::getErrMsg(GLOBAL_SUCCESS);
            $aParams        = $this->_getParams();
            $oInviteCarpool = new InvitationCarpool();
            $oInviteCarpool->setInviteOrderInfo($aParams['invite_order_info'], $aParams['invite_oid']);
            $aRet['data'] = $oInviteCarpool->getInvitationInfo($aParams['passenger']['pid'], $aParams['passenger']['phone'], $aParams['app_version'], $aParams['access_key_id']);
            $oResponse    = new GetInvitationInfoResponse();
            $oResponse->mergeFromJsonArray($aRet);
        } catch (\Exception $e) {
            $aErrMsg   = Config::text('errno', 'errno_msg');
            $aRet      = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $aErrMsg, ]);
            $oResponse = new GetInvitationInfoResponse();
            $oResponse->mergeFromJsonArray($aRet);
            $this->sendJson($oResponse);
            return;
        }

        $this->sendJson($oResponse);

        fastcgi_finish_request();
        $this->_writePublicLog($aParams, $aRet['data']);
        $this->_recordReqUv($aParams);
    }

    /**
     * @return array
     * @throws InvalidArgumentException exception
     * @throws \BizLib\Exception\ExceptionWithResp exception
     */
    private function _getParams() {
        $oRequest       = Request::getInstance();
        $oInvitationReq = new GetInvitationInfoRequest();
        $oInvitationReq->mergeFromJsonArray($oRequest->get());
        $sToken       = $oInvitationReq->getToken();
        $sInviteOid   = $oInvitationReq->getInviteOid();
        $sAppVersion  = $oInvitationReq->getAppVersion();
        $iAccessKeyId = $oInvitationReq->getAccessKeyId();

        if (empty($sToken) || empty($sInviteOid)) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                $oRequest->get()
            );
        }

        $aPassenger = Passenger::getInstance()->getPassengerByTokenFromPassport($sToken);
        if (empty($aPassenger)) {
            throw new \BizLib\Exception\ExceptionWithResp(
                Code::E_COMMON_TOKEN_INVALID,
                \BizLib\ErrCode\RespCode::P_PARAMS_ERROR,
                '',
                $oRequest->get()
            );
        }

        $aOrderIds = UtilHelper::decodeIdV3($sInviteOid);
        $aRet      = (new OrderSystemClient())->getOrderInfo($aOrderIds['oid'], $aOrderIds['district']);
        if (empty($aRet['result']['order_info']) || 0 != $aRet['result']['errno']) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                $oRequest->get()
            );
        }

        return [
            'token'             => $sToken,
            'invite_oid'        => $sInviteOid,
            'passenger'         => $aPassenger,
            'app_version'       => $sAppVersion,
            'access_key_id'     => $iAccessKeyId,
            'invite_order_info' => $aRet['result']['order_info'],
        ];
    }

    /**
     * @param array $aParam   comment
     * @param array $aResData comment
     * @return void
     */
    private function _writePublicLog($aParam, $aResData) {
        $sKey     = 'g_get_invitation_info';
        $aOrderId = \BizLib\Utils\UtilHelper::decodeId($aParam['invite_oid']);
        $aLogData = [
            'opera_stat_key'  => $sKey,
            'pid'             => $aParam['passenger']['pid'],
            'app_version'     => $aParam['app_version'],
            'order_id'        => $aOrderId['oid'],
            'district'        => $aOrderId['district'],
            'status'          => $aResData['status'],
            'role'            => $aResData['role'],
            'invitation_type' => $aResData['invitation_type'] ?? 1,
        ];

        PublicLog::writeLogForOfflineCal('public', $aLogData);
    }

    /**
     * 记录访问人数
     *
     * @param array $aParam param
     * @return void
     */
    private function _recordReqUv($aParam) {
        $aOrderIds = UtilHelper::decodeIdV3($aParam['invite_oid']);
        if (empty($aOrderIds['oid']) || empty($aOrderIds['district']) || empty($aParam['passenger']['pid'])) {
            return;
        }

        if ($aParam['passenger']['pid'] == $aParam['invite_order_info']['passenger_id']) {
            return;
        }

        $sHighId = UtilHelper::genHighIntOrderId($aOrderIds['oid'], $aOrderIds['district']);
        $sKey    = Common::getRedisPrefix(O_INVITATION_ORDER_VISIT_UV) . $sHighId;
        $oRedis  = RedisDB::getInstance();
        if (!$oRedis->exist($sKey)) {
            $oRedis->sAdd($sKey, $aParam['passenger']['pid']);
            $oRedis->expire($sKey, self::INVITATION_UV_RECORD_EXPIRE);
            return;
        }

        $oRedis->sAdd($sKey, $aParam['passenger']['pid']);
    }
}
