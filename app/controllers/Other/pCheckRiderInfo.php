<?php
/**
 * Copyright (c) 2019 xiaojukeji.com, Inc. All Rights Reserved.
 * <AUTHOR> <<EMAIL>>
 * @date   2019/8/06 下午15:21
 * @desc   代叫车信息验证，但此验证实际只能前端透传，不能阻止后端直接curl发单
 * @wiki   http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=233935034
 */

use PreSale\Dto\CheckRiderInfoRequest;
use PreSale\Application\Service\CheckRiderInfoService;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\Common as UtilsCommon;

/**
 * Class PCheckRiderInfoController
 */
class PCheckRiderInfoController extends \PreSale\Core\Controller
{
    /**
     * 主action
     * @return void
     */
    public function indexAction() {
        try {
            $request = new CheckRiderInfoRequest();
            $request->init();

            $service  = new CheckRiderInfoService();
            $response = $service->execute($request);

            $this->echoSuccessResponseWithJson($response);
        } catch (\Exception $e) {
            $this->_handleException($e);
        }
    }

    /**
     * @param \Exception $e e
     * @return void
     */
    private function _handleException(\Exception $e) {
        // TODO: 记录public日志
        $iErrCode    = $e->getCode();
        $sErrMsg     = $e->getMessage();
        $aErrMsgConf = NuwaConfig::text('errno', 'check_booking_ability_error_msg');
        $aError      = UtilsCommon::getErrMsg($iErrCode, $aErrMsgConf);
        $sCallback   = $this->getRequest()->getStr('callback', '');

        // 情况 2，自定义 errmsg 或拼接或透传
        if (!empty($sErrMsg)) {
            $aError['errmsg'] = $sErrMsg;
        }

        // 情况 3，定义了 errmsg 的错误返回，也是最基础的返回
        if (!($e instanceof \PreSale\Exception\BaseException)) {
            $this->sendJson($aError, $sCallback);

            return;
        }

        // 情况 4, 此时为 BaseException
        if (isset($e->mErrno)) {
            $aError['errno'] = $e->mErrno; // 情况 4 1`，见该属性注释
        }

        $aError = array_merge($aError ?? [], $e->mData); // 情况 4 2`，没有 errmsg 或携带附属信息
        $this->sendJson($aError, $sCallback);
    }
}
