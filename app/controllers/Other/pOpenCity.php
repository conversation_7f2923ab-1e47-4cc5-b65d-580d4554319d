<?php

/**
 * 开城数据.
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2020/3/5
 */

use PreSale\Core\Controller;
use BizLib\ExceptionHandler;
use BizLib\Exception\ExceptionWithResp;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\RespCode;
use BizLib\Utils\Common;
use BizLib\Utils\MapHelper;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\openCity\OpenCityLogic;

/**
 * Class POpenCityController
 */
class POpenCityController extends Controller
{
    // 开城状态枚举值
    const OPEN_CITY_STATUS_NORMAL  = 1; // 开城
    const OPEN_CITY_STATUS_WARM_UP = 2; // 预热
    const OPEN_CITY_STATUS_CLOSE   = 3; // 下线

    private $_aParams;

    /**
     * 构建函数
     *
     * @return void
     */
    public function init() {
        parent::init();
    }

    /**
     * 入口函数
     *
     * @return void
     */
    public function indexAction() {
        try {
            $this->_getParams();

            $this->_checkParams();

            $aResult = Common::getErrMsg(RespCode::P_SUCCESS, $this->aErrnoMsg);

            $aOpenCityData = $this->_getOpenCityData();

            $aResult['data']['baseConf'] = $aOpenCityData;

            $this->sendJson($aResult);
        } catch (\Exception $e) {
            $aResult = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg,]);
            $this->sendJson($aResult);
        }

        return;
    }

    /**
     * 获取参数
     *
     * @return void
     */
    private function _getParams() {
        $aParams['phone']       = $this->getRequest()->fetchGetPost('phone', false);
        $aParams['city_id']     = $this->getRequest()->fetchGetPost('city_id', false);
        $aParams['cur_lat']     = $this->getRequest()->fetchGetPost('cur_lat', false);
        $aParams['cur_lng']     = $this->getRequest()->fetchGetPost('cur_lng', false);
        $aParams['app_version'] = $this->getRequest()->fetchGetPost('app_version', false);
        $aParams['lang']        = $this->getRequest()->fetchGetPost('lang', false);

        $this->_aParams = $aParams;
    }

    /**
     * 校验参数
     *
     * @return void
     *
     * @throws ExceptionWithResp 异常
     */
    private function _checkParams() {
        if (empty($this->_aParams['cur_lng']) || empty($this->_aParams['cur_lat'])) {
            throw new ExceptionWithResp(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                RespCode::P_PARAMS_ERROR,
                '',
                [
                    'params' => json_encode($this->_aParams),
                ]
            );
        }
    }

    /**
     * 获取开城数据
     *
     * @return array
     */
    private function _getOpenCityData() {
        $aCityInfo = MapHelper::getAreaInfoByLoc($this->_aParams['cur_lng'], $this->_aParams['cur_lat']);
        if (empty($aCityInfo)) {
            return [];
        }

        $iCityId         = $aCityInfo['id'];
        $aCondition      = array(
            'city_id'     => $iCityId,
            'county_id'   => $aCityInfo['countyid'],
            'phone'       => $this->_aParams['phone'],
            'app_version' => $this->_aParams['app_version'],
        );
        $aOpenCityConfig = (new OpenCityLogic())->getOpenCityConfigFromApollo($aCondition);
//        $aConfig = Apollo::getInstance()->getConfigsByNamespaceAndConditions(
//            'kflower_open_city_conf',
//            ['city_id' => $iCityId,]
//        );
//
//        list($ok, $aRet) = $aConfig->getAllConfigData();
//        $aRet            = array_values($aRet);
//        if (empty($aRet)) {
//            return [];
//        }
//
//        \BizLib\Log::notice(sprintf('get one conf from apollo ret=%s', json_encode($aRet)));
//
//        $aOpenCityConfig = [];
//        foreach ($aRet as $aItem) {
//            if (self::OPEN_CITY_STATUS_NORMAL != $aItem['open_status']
//                && self::OPEN_CITY_STATUS_WARM_UP != $aItem['open_status']
//            ) {
//                continue;
//            }
//
//            if (self::OPEN_CITY_STATUS_WARM_UP == $aItem['open_status']
//                && !$this->_isOpenCityWhiteListByCityAndPhone($aItem['city_id'], $this->_aParams['phone'])
//            ) {
//                continue;
//            }
//
//            if (version_compare($this->_aParams['app_version'], $aItem['app_version']) < 0) {
//                continue;
//            }
//
//            if (!empty($aItem['county_id']) && is_array($aItem['county_id']) &&
//                !in_array($aCityInfo['countyid'], $aItem['county_id'])) {
//                continue;
//            }
//
//            array_push($aOpenCityConfig, $aItem);
//        }
        return $this->_formatOpenCityData($aOpenCityConfig);
    }

    /**
     * 根据城市id和手机号判断是否是开城白名单用户
     *
     * @param int $iCity  城市
     * @param int $iPhone 手机号
     * @return bool
     */
    private function _isOpenCityWhiteListByCityAndPhone($iCity, $iPhone) {
        if (empty($iCity) || empty($iPhone)) {
            return false;
        }

        $aOpenCityWhiteList = $this->_getOpenCityWhiteListConfigFromApollo($iCity);
        if (in_array($iPhone, $aOpenCityWhiteList)) {
            return true;
        }

        return false;
    }

    /**
     * 根据city获取开城白名单信息信息
     * @param int $iCity 城市
     * @return array
     */
    private function _getOpenCityWhiteListConfigFromApollo($iCity) {
        list($ok, $aOpenCityWhiteListConfig) = Apollo::getInstance()->getConfigResult('kflower_open_city_white_list_conf', 'open_city_white_list')->getConfig($iCity);
        if (!is_array($aOpenCityWhiteListConfig)) {
            return [];
        }

        return $aOpenCityWhiteListConfig;
    }

    /**
     * 格式化返回
     *
     * @param array $aOpenCityConfig 开城配置
     *
     * @return array
     */
    private function _formatOpenCityData($aOpenCityConfig) {
        $aBaseConfList = [];
        foreach ($aOpenCityConfig as $aItem) {
            $aSubMenuRange   = [];
            $sDefaultSubMenu = '';
            foreach ($aItem['sub_menu_range'] as $aSubMenuData) {
                $aSubMenu = [
                    'subMenuId'    => $aSubMenuData['sub_menu_id'],
                    'subMenuNumId' => (int)$aSubMenuData['car_level'],
                    'name'         => $aSubMenuData['name'],
                    'comboType'    => (int)$aSubMenuData['combo_type'],
                ];

                array_push($aSubMenuRange, $aSubMenu);
                if (true === $aSubMenuData['default_selected']) {
                    $sDefaultSubMenu = $aSubMenuData['sub_menu_id'];
                }
            }

            $aBaseConfItem = [
                'cityId'         => (int)$aItem['city_id'],
                'menuId'         => $aItem['menu_id'],
                'menuNumId'      => (int)$aItem['business_id'],
//                'openStatus'     => (int)$aItem['open_status'],
                'openStatus'     => 1,
                'submenuRange'   => $aSubMenuRange,
                'defaultSubmenu' => $sDefaultSubMenu,
                'name'           => $aItem['name'],
            ];
            array_push($aBaseConfList, $aBaseConfItem);
        }

        return $aBaseConfList;
    }
}
