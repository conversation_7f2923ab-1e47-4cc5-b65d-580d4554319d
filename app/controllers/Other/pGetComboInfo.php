<?php

use BizLib\Config as NuwaConfig;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\MapHelper;
use BizLib\Utils\UtilHelper;
/*
 * *************************************************************************
 *
 * Copyright (c) 2017 xiaojukeji.com, Inc. All Rights Reserved
 * @desc: 拉取套餐信息，包车计价规则
 * @wiki :
 * @原author: <EMAIL>
 * <AUTHOR> <EMAIL>
 *
 * @package : application/controllers/passenger/
 *
 *
 *************************************************************************
 */
use BizLib\Utils\Product;
use BizLib\Constants\OrderSystem;
use BizLib\ExceptionHandler;
use BizLib\Exception\ExceptionWithResp;
use BizLib\ErrCode\RespCode;
use BizLib\ErrCode\Code;
use BizCommon\Models\Mis\CityPriceNew;

/**===
 * @property CityPriceNew $CityPriceNew
 * @property Passenger $Passenger
 * @property businessApi $businessApi
===*/

class PGetComboInfoController extends \PreSale\Core\Controller
{
    private $_aErrMsg = array();

    public function init() {
        parent::init();
        $this->cityPriceNew = CityPriceNew::getInstance();
        $this->_aErrMsg     = NuwaConfig::text('errno', 'errno_msg');
    }

    public function indexAction() {
        try {
            list($iChannel, $iFastCar, $iComboType, $sDistrict, $iBusinessId) = $this->_getAndCheckParams();
            //获取产品线ID
            $iProductId = Product::getProductId($iFastCar, OrderSystem::ORIGIN_ID_DIDI, UtilHelper::isB2bChannel($iChannel), $iBusinessId);

            //获取计价token
            $aPriceCfg = $this->cityPriceNew->getPriceTokenSchemaId($iProductId, $iComboType);
            if (empty($aPriceCfg['token']) || empty($aPriceCfg['schema_id'])) {
                $aReturn = UtilsCommon::getErrMsg(RespCode::P_ERRNO_PARAMS_ERROR, $this->_aErrMsg);
                throw new ExceptionWithResp(
                    Code::E_COMMON_PARAM_INVALID_VALUE,
                    $aReturn['errno'],
                    $aReturn['errmsg'],
                    ['ProductId' => $iProductId, 'ComboType' => $iComboType]
                );
            }

            //获取计价规则
            $aPrice    = $this->cityPriceNew->getCityPriceByDistrict($sDistrict, $iProductId, $aPriceCfg['schema_id'], $iComboType);
            $aPriceRet = $this->cityPriceNew->sortComboInfo($aPrice);
            $aTmpRet   = $this->_formatComboInfo($aPriceRet);
            if (!empty($aTmpRet)) {
                $aRet         = UtilsCommon::getErrMsg(RespCode::P_SUCCESS, $this->_aErrMsg);
                $aRet['data'] = $aTmpRet;
                $aRet['default_car_level'] = Product::getDefaultCarlevel($iProductId);
            } else {
                $aReturn = UtilsCommon::getErrMsg(RespCode::P_GET_COMBO_FAIL, $this->_aErrMsg);
                throw new ExceptionWithResp(
                    Code::E_COMMON_PARAM_INVALID_VALUE,
                    $aReturn['errno'],
                    $aReturn['errmsg']
                );
            }
        } catch (\Exception $e) {
            $aRet = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->_aErrMsg, ]);
        }

        $aRet['errno'] = (string)($aRet['errno']);
        $this->sendTextJson($aRet);

        return;
    }

    /**
     * @desc 获取并校验参数
     * @params
     *
     * @return array
     **/
    private function _getAndCheckParams() {
        $iChannel    = intval($this->getRequest()->getQuery('channel', false));
        $iFastCar    = intval($this->getRequest()->getQuery('flier', false));
        $iComboType  = intval($this->getRequest()->getQuery('combo_type', false));
        $iArea       = intval($this->getRequest()->getQuery('area', false));
        $iBusinessId = intval($this->getRequest()->getQuery('business_id', false));
        $sDistrict   = null;

        $aAreaInfo = MapHelper::getAreaInfoByAreaId($iArea);
        if (!empty($aAreaInfo)) {
            $sDistrict = $aAreaInfo['district'];
        }

        if (empty($aAreaInfo) || empty($sDistrict)) {
            $aReturn = UtilsCommon::getErrMsg(RespCode::P_ERRNO_PARAMS_ERROR, $this->_aErrMsg);
            throw new ExceptionWithResp(Code::E_COMMON_PARAM_INVALID_VALUE, $aReturn['errno'], $aReturn['errmsg'], ['area' => $iArea, ]);
        }

        if (empty($iBusinessId)) { // 默认专车
            $iBusinessId = Product::COMMON_PRODUCT_ID_DEFAULT;
        }

        return [$iChannel, $iFastCar, $iComboType, $sDistrict, $iBusinessId];
    }

    /**
     * @desc 组织套餐信息
     * @User: <EMAIL>
     * @Date: 2016/9/5
     **/
    private function _formatComboInfo($aPriceRet) {
        $aRet = array();
        if (!empty($aPriceRet)) {
            foreach ($aPriceRet as $carKey => $carVal) {
                foreach ($carVal as $priceKey => $priceValue) {
                    $aRet[$carKey][$priceKey]['combo_id']         = $priceValue['combo_id'];
                    $aRet[$carKey][$priceKey]['is_default_combo'] = $priceValue['is_default_combo'];
                    $aRet[$carKey][$priceKey]['combo_fee']        = $priceValue['package'];
                    $aRet[$carKey][$priceKey]['combo_distance']   = $priceValue['package_distance'];
                    $aRet[$carKey][$priceKey]['combo_time']       = $priceValue['package_time'];
                    $aComboDesc       = NuwaConfig::text('config_text', 'combo_desc');
                    $aComboDescOption = NuwaConfig::text('config_text', 'combo_desc_option');
                    if (Product::isCharteredCarByProductId($priceValue['product_id'])) {
                        $aRet[$carKey][$priceKey]['combo_day']         = $priceValue['package_day'];
                        $aRet[$carKey][$priceKey]['combo_desc']        = sprintf($aComboDesc['chartered_car'], $priceValue['package_day'], $priceValue['package_distance']);
                        $aRet[$carKey][$priceKey]['combo_desc_option'] = sprintf($aComboDescOption['chartered_car'], $priceValue['package_day'], $priceValue['package_distance']);
                    } else {
                        $aRet[$carKey][$priceKey]['combo_desc'] = sprintf($aComboDesc['default'], round($priceValue['package_time'] / 60, 1), $priceValue['package_distance']);
                        //$aRet[$carKey][$priceKey]['combo_desc'] = '包车 '.round($priceValue['package_time']/60,1).'小时'.$priceValue['package_distance'].'公里';
                    }
                }
            }
        }

        return $aRet;
    }
}
