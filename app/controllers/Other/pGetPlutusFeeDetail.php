<?php

use BizLib\ErrCode\Code;
use BizLib\Utils\Common;
use BizLib\ErrCode\RespCode;
use BizLib\Exception\ExceptionWithResp;
use BizLib\Config as NuwaConfig;
use BizLib\Client\DosClient;
use BizLib\ExceptionHandler;
use PreSale\Logics\estimatePrice\FeeDetailLogic;
use BizLib\Utils\UtilHelper;

/**
 * @authors liangdongxu (<EMAIL>)
 * @date    2020-12-16 16:11:06
 * @desc    预估费用详情页
 */
class PGetPlutusFeeDetailController extends \PreSale\Core\Controller
{

    /**
     * @authors liangdongxu (<EMAIL>)
     * @date    2020-12-16 16:11:06
     * @desc    入口
     * @return  void
     */
    public function indexAction() {
        try {
            $aRet            = Common::getErrMsg(GLOBAL_SUCCESS);
            $aRet['data']    = [];
            $aParams         = $this->_getInputParams();
            $oFeeDetailLogic = FeeDetailLogic::getInstance();
            $feeDetail       = $oFeeDetailLogic->getFeeDetailInfos($aParams);
            $aRet['data']    = $feeDetail;
        } catch (\Exception $e) {
            $aRet = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg,]);
        }

        $aRet['errno'] = (int)($aRet['errno']);
        $this->sendTextJson($aRet);
        return;
    }

    /**
     * @date    2020-12-16 16:11:06
     * @desc    参数验证
     * @throws ExceptionWithResp 异常信息
     * @return array  params
     */
    private function _getInputParams() {
        //获取参数
        $sEstimateID = $this->oRequest->getStr('estimate_id');
        $lang        = $this->oRequest->getStr('lang') ?? \BizLib\Utils\Language::getLocalLanguage();

        //检查参数
        if (empty($sEstimateID)) {
            throw new ExceptionWithResp(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                RespCode::P_PARAMS_ERROR,
                'errmsg:param error! estimate_id:'.$sEstimateID
            );
        }

        $aParams = [
            'estimate_id' => $sEstimateID,
            'lang'        => $lang,
        ];

        return $aParams;
    }
}
