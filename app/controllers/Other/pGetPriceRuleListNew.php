<?php

use BizLib\Client\BillClient;
use BizLib\Utils\MapHelper;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\Product;
use BizLib\ExceptionHandler;
use BizLib\ErrCode\RespCode;
use BizLib\ErrCode\Code;
use BizLib\Exception\ExceptionWithResp;
use \BizCommon\Models\Passenger\Passenger;
use Nuwa\ApolloSDK\Apollo;
use BizLib\Utils\Horae;
use BizLib\Constants;

/**
 * Class pGetPriceRuleNew.
 */
class PGetPriceRuleListNewController extends \PreSale\Core\Controller
{
    /**
     * 从账单获取计价规则列表
     * @return void
     * @throws \Exception 异常
     */
    public function indexAction() {
        try {
            //默认返回值
            $aResponse = UtilsCommon::getErrMsg(RespCode::P_SUCCESS, $this->aErrnoMsg);

            //获取并检查参数
            list($sLang, $iProductId, $aAreaInfo, $aPassenger, $iAccessKeyId, $sAppVersion, $iComboType) = $this->_getAndCheckParams();

            //拼装调用账单服务的参数
            $aBillParams = [
                'district'          => $aAreaInfo['district'],
                'role'              => 2,  //推动在disf bill sdk里加此常量定义
                'lang'              => $sLang,
                'driver_id'         => '',
                'bind_car_level'    => '',
                'abstract_district' => $aAreaInfo['abstract_district'],
                'trip_country'      => $aAreaInfo['canonical_country_code'],
                'product_id'        => $iProductId,
                'area'              => (int)$aAreaInfo['id'],
            ];

            if (-1 != $iComboType) {
                $aBillParams['combo_type'] = $iComboType;
            }

            $this->_fillBillParams($aBillParams, $aAreaInfo, $aPassenger, $iAccessKeyId, $sAppVersion);
            $iDisplayType = 1;  //获取新数据结构

            //调用账单服务
            $aBillResult = (new BillClient())->getMultiStrategiesList([$aBillParams], $iDisplayType);
            if (empty($aBillResult['result']['data'])) {
                throw new \Exception('', RespCode::D_SYSTEM_ERROR);
            }

            //拼装返回值
            $aResponse['data'] = $aBillResult['result']['data']['data'];
        } catch (\Exception $e) {
            //异常处理并得到返回值
            $aResponse = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg,]);
        }

        //输出返回值
        $this->sendTextJson($aResponse);
    }

    /**
     * @desc 参数获取
     * @return mixed
     * @throws ExceptionWithResp 异常
     */
    private function _getAndCheckParams() {
        $sBusinessId  = $this->oRequest->getStr('business_id');
        $sLang        = $this->oRequest->getStr('lang');
        $sFlng        = $this->oRequest->getStr('flng');
        $sFlat        = $this->oRequest->getStr('flat');
        $sToken       = $this->oRequest->getStr('token');
        $sCityId      = $this->oRequest->getStr('city_id');
        $sCarLevel    = $this->oRequest->getStr('car_level');
        $sSceneType   = $this->oRequest->getStr('scene_type');
        $iAccessKeyId = $this->oRequest->getStr('access_key_id');
        $sAppVersion  = $this->oRequest->getStr('app_version');

        //验证必要参数是否为空
        if (empty($sBusinessId)) {
            throw new ExceptionWithResp(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                RespCode::P_PARAMS_ERROR,
                '',
                [
                    'exceptionMsg' => [
                        'msg'      => 'business id is empty',
                        'moreInfo' => ['business_id' => $sBusinessId,],
                    ],
                ]
            );
        }

        //验证business id是否合法
        $iProductId = Product::getProductIdByBusinessId($sBusinessId);
        if (empty($iProductId)) {
            throw new ExceptionWithResp(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                RespCode::P_PARAMS_ERROR,
                '',
                [
                    'exceptionMsg' => [
                        'msg'      => 'product id is empty',
                        'moreInfo' => [
                            'business_id' => $sBusinessId,
                            'product_id'  => $iProductId,
                        ],
                    ],
                ]
            );
        }

        //如果经纬度不为空，根据地经纬度获取区域信息，否走用传入的城市ID和区县ID，再否则抛异常
        if (!empty($sFlng) && !empty($sFlat)) {
            $aAreaInfo = MapHelper::getAreaInfoByLoc($sFlng, $sFlat);
        } elseif (!empty($sCityId)) {
            $aAreaInfo = MapHelper::getAreaInfoByAreaId($sCityId);
        } else {
            throw new ExceptionWithResp(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                RespCode::P_PARAMS_ERROR,
                '',
                [
                    'exceptionMsg' => [
                        'msg'      => 'flng, flat or city id is empty',
                        'moreInfo' => [
                            'flng'    => $sFlng,
                            'flat'    => $sFlat,
                            'city_id' => $sCityId,
                        ],
                    ],
                ]
            );
        }

        if (empty($aAreaInfo['district'])) {
            throw new ExceptionWithResp(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                RespCode::P_PARAMS_ERROR,
                '',
                [
                    'exceptionMsg' => [
                        'msg'      => 'district id is empty',
                        'moreInfo' => [
                            'area_info'   => $aAreaInfo,
                            'district_id' => $aAreaInfo['district'],
                        ],
                    ],
                ]
            );
        }

        //Abstract district
        if (!empty($aAreaInfo['countyid'])) {
            $aAreaInfo['abstract_district'] = $aAreaInfo['district'] . ',' . $aAreaInfo['countyid'];
        }

        //验证token
        if ($this->_strongCheckToken($iProductId, $sCarLevel, $sSceneType, $aAreaInfo['district']) && empty($sToken)) {
            throw new ExceptionWithResp(
                Code::E_COMMON_TOKEN_INVALID,
                RespCode::P_PARAMS_ERROR,
                '',
                [
                    'exceptionMsg' => [
                        'msg'      => 'token is invalid',
                        'moreInfo' => [
                            'product_id'  => $iProductId,
                            'car_level'   => $sCarLevel,
                            'sSceneType'  => $sSceneType,
                            'district_id' => $aAreaInfo['district'],
                            'token'       => $sToken,
                        ],
                    ],
                ]
            );
        }

        //如果token不为空，验证token是否合法
        $aPassenger = [];
        if (!empty($sToken)) {
            $aPassenger = (Passenger::getInstance())->getPassengerByToken($sToken);
            $sPid       = $aPassenger['pid'] ?? 0;
            if (empty($sPid)) {
                throw new ExceptionWithResp(
                    Code::E_COMMON_TOKEN_INVALID,
                    RespCode::P_PARAMS_ERROR,
                    '',
                    [
                        'exceptionMsg' => [
                            'msg'      => 'pid is empty (token is invalid)',
                            'moreInfo' => [
                                'args' => (array) $this,
                                'pid'  => $sPid,
                            ],
                        ],
                    ]
                );
            }
        }

        if (empty($aAreaInfo['id'])) {
            $aAreaInfo['id'] = $sCityId;
        }

        //场景参数
        $iComboType = -1;
        if (is_numeric($sSceneType)) {
            $iComboType = Horae::getComboType($sSceneType);
        }

        return [$sLang, $iProductId, $aAreaInfo, $aPassenger, $iAccessKeyId, $sAppVersion, $iComboType];
    }

    /**
     * 是否需强校验token
     * @param int    $iProductId 产品线
     * @param string $sCarLevel  车型
     * @param string $sSceneType 场景
     * @param string $sDistrict  区域
     * @return bool
     */
    private function _strongCheckToken($iProductId, $sCarLevel, $sSceneType, $sDistrict) {
        return true;
    }

    /**
     * 补充请求账单参数
     *
     * @param array  $aBillParams  请求账单入参
     * @param array  $aAreaInfo    城市
     * @param array  $aPassenger   乘客信息
     * @param int    $iAccessKeyId 来源标识
     * @param string $sAppVersion  版本信息
     *
     * @return void
     */
    private function _fillBillParams(&$aBillParams, $aAreaInfo, $aPassenger, $iAccessKeyId, $sAppVersion) {
        $oApolloClient     = Apollo::getInstance();
        $aApolloParameters = array(
            'key'           => $aPassenger['pid'],
            'phone'         => $aPassenger['phone'],
            'city'          => (int)$aAreaInfo['id'],
            'access_key_id' => $iAccessKeyId,
            'app_version'   => $sAppVersion,
        );

        if ($oApolloClient->featureToggle('gs_dixiaodi_open_switch',$aApolloParameters)->allow()) {
            $aBillParams['order_n_tuple']['level_type'] = 4;
        }
    }
}
