<?php

use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\Language;
use BizLib\Utils\PublicLog;
use BizLib\Utils\UtilHelper;
use PreSale\Models\strategy\DecisionService;
use BizLib\ExceptionHandler;
use BizLib\Constants;
use PreSale\Logics\estimatePrice\ParamsLogic;
use PreSale\Models\carrera\PassengerEstimateTopic;
use PreSale\Models\order\OrderCarpoolDualPrice;
use PreSale\Logics\order\AnyCarOrderLogic;
use BizLib\Client\AthenaApiClient;
use PreSale\Logics\estimatePrice\DecisionLogic;
use PreSale\Logics\estimatePrice\FreshEducationLogic;
use BizCommon\Constants\OrderNTuple;
use BizCommon\Models\Passenger\Passenger;
use BizCommon\Models\Order\OrderStation;
use BizCommon\Models\Order\Order;
use PreSale\Infrastructure\Repository\Redis\CarpoolLongOrderBubble;
use BizLib\Constants\Common as ConstantsCommon;

class PSelectItemController extends \PreSale\Core\Controller
{
    /** @var $athena AthenaApiClient */
    public $athena;
    /** @var $orderStation OrderStation */
    public $orderStation;
    const CONFIRM_PUBLIC_KEY = 'g_order_prefee_show';

    const DDS_SOURCE_FROM_MARK = 'confirm';

    const REMOVE_BUBBLE_INFO_VERSION = '5.3.10';

    const MINI_PROGRAM_REMOVE_BUBBLE_INFO_VERSION = '5.4.14';

    /**
     * @var null|DecisionLogic
     */
    private $_oDecision = null;

    private $_aDecisionParams = [];

    private $_aDecisionRsp = [];
    /**
     * @var \BizCommon\Models\Cache\EstimatePrice
     */
    private $_oEstimatePriceCache;

    //这次接口调用是否展示了拼车通勤卡的气泡
    private $_iShowCommuteCardBubble = 0;

    //这次接口调用是否展示了拼车通勤卡的h5
    private $_iShowCommuteCardH5 = 0;

    //这次接口调用是否展示了两口价教育(统计需求)
    private $_iShowDualPriceEducation = 0;

    /**
     * @var \OrderStation
     */
    private $_oOrderStation;

    private $_aAthenaSelectAffirmReq = [];

    private $_aAthenaGuideInfo = [];

    const ATHENA_SELECT_TYPE = 1;

    public function init($type = null) {
        parent::init($type);
        $this->athena     = new AthenaApiClient();
        $this->_oDecision = DecisionLogic::getInstance();
        $this->_oEstimatePriceCache = \BizCommon\Models\Cache\EstimatePrice::getInstance();
    }

    public function indexAction() {
        try {
            $oParams        = ParamsLogic::getInstance();
            $aConfirmParams = $oParams->getConfirmParam((array) $this->getRequest()->getQuery(null, false, false));
            $sBubbleID      = $this->_setEstimateIdCache($aConfirmParams);
            $aResp          = UtilsCommon::getErrMsg(GLOBAL_SUCCESS);

            //pSelectItem改造后，只做统计相关功能：预估埋点和写kafka。业务信息通过接口pGetBubbleInfo拉取。
            if ($this->_supportBubbleInfo($aConfirmParams['common_info'])) {
                $this->_aAthenaGuideInfo = $this->_getGuideInfoFromAthena($aConfirmParams);
                $this->_aDecisionParams  = $this->_buildDDSReq($aConfirmParams);
                $this->_oDecision->setDecisionParams(array($aConfirmParams));
                $this->_aDecisionRsp = $this->_oDecision->getDecisionResultDirect($this->_aDecisionParams);
                $this->_buildResponse($aConfirmParams, $aResp);
                $this->_setCommuteCardCache($aConfirmParams);
            }
        } catch (\Exception $e) {
            $oHandler      = ExceptionHandler::getInstance();
            $aResponseInfo = $oHandler->handleException($e);
            $this->sendJson($aResponseInfo);

            return;
        }

        $this->sendJson($aResp);
        fastcgi_finish_request();
        $this->_writePublicLog($aConfirmParams, $sBubbleID);
        $this->_writeKafka($aConfirmParams, $sBubbleID);
    }

    /**
     * @param array $aCommonInfos 参数
     * @return bool
     */
    private function _supportBubbleInfo($aCommonInfos) {
        $aNativeAccessKeyId = [
            ConstantsCommon::DIDI_IOS_PASSENGER_APP,
            ConstantsCommon::DIDI_ANDROID_PASSENGER_APP,
        ];
        if (in_array($aCommonInfos['access_key_id'],$aNativeAccessKeyId)
            && version_compare($aCommonInfos['app_version'], self::REMOVE_BUBBLE_INFO_VERSION) >= 0
        ) {
            return false;
        }

        $aMiniProgramAccessKeyId = [
            ConstantsCommon::DIDI_ALIPAY_MINI_PROGRAM,
            ConstantsCommon::DIDI_WECHAT_MINI_PROGRAM,
        ];
        if (in_array($aCommonInfos['access_key_id'],$aMiniProgramAccessKeyId)
            && version_compare($aCommonInfos['app_version'], self::MINI_PROGRAM_REMOVE_BUBBLE_INFO_VERSION) >= 0
        ) {
            return false;
        }

        return true;
    }

    /**
     * @param $aConfirmParams
     * @param $aResp
     */
    private function _buildAthenaInfo($aConfirmParams, &$aResp) {
        if (empty($aConfirmParams) || $this->_checkDegrade($aConfirmParams)) {
            return;
        }

        $aAthenaInfo = $this->_getGuideInfoFromAthena($aConfirmParams);
        if (empty($aAthenaInfo) || !isset($aAthenaInfo['errno']) || 0 != $aAthenaInfo['errno'] || empty($aAthenaInfo['data'])) {
            $aAthenaInfo = $this->_getGuideInfoFromDDS($aConfirmParams);
        }

        if (!empty($aAthenaInfo) && isset($aAthenaInfo['errno']) && 0 == $aAthenaInfo['errno']) {
            $aResp['data']['athena_info'] = json_encode($aAthenaInfo);
        }

        return;
    }

    /**
     * 目前可能有2个信息需要透出
     * 1. 导流
     * 2. 新手教育组件.
     *
     * @param $aResp
     */
    private function _buildResponse($aConfirmParams, &$aResp) {
        // 超值出租车切换tab时不需要导流信息
        if (isset($aConfirmParams['product_category'])
            && \BizLib\Utils\ProductCategory::PRODUCT_CATEGORY_UNIONE_SPECIAL_PRICE == $aConfirmParams['product_category']
        ) {
            return;
        }

        $aDDSAthenaInfo = $this->_buildGuideInfo($aConfirmParams);
        $bDDSAthenaFlag = $aDDSAthenaInfo['bubble_flag'];
        unset($aDDSAthenaInfo['bubble_flag']);
        $_aGuideInfo = array();
        if (!empty($aDDSAthenaInfo) && 0 == $aDDSAthenaInfo['errno']) {
            $_aGuideInfo = $aDDSAthenaInfo;
        }

        if (!$bDDSAthenaFlag && !empty($this->_aAthenaGuideInfo) && isset($this->_aAthenaGuideInfo['data']) && !empty($this->_aAthenaGuideInfo['data'])) {
            $_aGuideInfo = $this->_aAthenaGuideInfo;
        }

        if (!$this->_checkDegrade($aConfirmParams) && !empty($_aGuideInfo)) {
            $aResp['data']['athena_info'] = json_encode($_aGuideInfo);
        }

        $_aEducationInfo = $this->_buildFreshEducationInfo($aConfirmParams);
        if (!empty($_aEducationInfo)) {
            $aResp['data']['fresh_guide_page'] = $_aEducationInfo;
        }
    }

    /**
     * @param array $aConfirmParams
     *
     * @return array
     * @wiki http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=152846864
     */
    private function _getGuideInfoFromAthena($aConfirmParams) {
        if ($this->_checkDegrade($aConfirmParams)) {
            return array();
        }

        $this->_aAthenaSelectAffirmReq = $this->_buildAthenaReq($aConfirmParams);
        $aGuideInfo = $this->athena->getSelectAffirmInfo($this->_aAthenaSelectAffirmReq);
        if (empty($aGuideInfo) || !isset($aGuideInfo['errno']) || 0 != $aGuideInfo['errno'] || empty($aGuideInfo['guide_result'])) {
            return array();
        }

        $aAthenaInfo = json_decode($aGuideInfo['guide_result'], JSON_UNESCAPED_UNICODE);
        if (isset($aAthenaInfo['errno']) && 0 != $aAthenaInfo['errno']) {
            return array();
        }

        return $aAthenaInfo;
    }

    /**
     * @date 2018.8.15
     *
     * <AUTHOR>
     *
     * @param array $aConfirmParams
     *
     * @return array
     */
    private function _buildAthenaReq($aConfirmParams) {
        $aAthenaSelectAffirmReq = array(
            'stage'           => self::ATHENA_SELECT_TYPE,
            'estimate_id'     => $aConfirmParams['pre_estimate_id'],
            'client_type'     => $aConfirmParams['common_info']['client_type'],
            'app_version'     => $aConfirmParams['common_info']['app_version'],
            'lang'            => $aConfirmParams['common_info']['lang'],
            'order_type'      => $aConfirmParams['order_info']['order_type'],
            'cur_business_id' => $aConfirmParams['common_info']['business_id'],
            'combo_type'      => $aConfirmParams['order_info']['combo_type'],
            'require_level'   => $aConfirmParams['order_info']['require_level'],
            'phone'           => $aConfirmParams['passenger_info']['phone'] ?? '',
            'pid'             => $aConfirmParams['passenger_info']['pid'] ?? 0,
            'city_id'         => $aConfirmParams['order_info']['area'],
        );

        return $aAthenaSelectAffirmReq;
    }

    /**
     * @date 2018.8.13
     *
     * <AUTHOR>
     * @description if get no data from athena then call dds::decision
     *
     * @param array $aConfirmParams
     *
     * @return string
     * @wiki http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=153069685
     */
    private function _getGuideInfoFromDDS($aConfirmParams) {
        $aDecisionParams = $this->_buildDDSReq($aConfirmParams);
        $_aGuideInfo     = DecisionService::getInstance()->getDecisionResult($aDecisionParams);
        if (empty($_aGuideInfo)) {
            return array();
        }

        $this->_oDecision->setDecisionResp($_aGuideInfo);
        $this->_oDecision->setDecisionParams(array($aConfirmParams));

        $_aGuideInfo = $this->_oDecision->getDDSAthenaInfo();
        unset($_aGuideInfo['bubble_flag']);

        return $_aGuideInfo;
    }

    /**
     * 从dds的返回中获取导流信息.
     *
     * @return array
     */
    private function _buildGuideInfo($aConfirmParams) {
        // anycar默认选中气泡优先级高
        if (\BizLib\Utils\Product::isAnyCar($aConfirmParams['order_info']['product_id'])) {
            $aAnycarAthenaInfo = $this->_buildAnycarBubble($aConfirmParams);
            if (!empty($aAnycarAthenaInfo)) {
                return $aAnycarAthenaInfo;
            }
        }

        $aAthenaRes = $this->_oDecision->getDDSAthenaInfo();
        if (empty($aAthenaRes)) {
            //dds的导流信息优先，没有的话就使用拼车通勤卡的气泡
            $aAthenaRes = $this->_buildCarpoolCommuteBubble($aConfirmParams);
        }

        // $aAthenaRes = $this->_buildLongOrderBubble($aConfirmParams, $aAthenaRes);
        return $aAthenaRes;
    }

    /*
    private function _buildLongOrderBubble($aConfirmParams, $aAthenaRes) {
        $sBubbleId = $aConfirmParams['pre_estimate_id'];
        if (empty($sBubbleId) || empty($aAthenaRes)) {
            return $aAthenaRes;
        }

        if (\BizLib\Constants\OrderSystem::PRODUCT_ID_FAST_CAR == $aConfirmParams['order_info']['product_id']
            && \BizLib\Utils\CarLevel::DIDI_XIAOBA_CAR_LEVEL == $aConfirmParams['order_info']['require_level']
            && \BizLib\Constants\OrderSystem::TYPE_COMBO_CARPOOL == $aConfirmParams['order_info']['combo_type']
        ) {
            $iShowCount = CarpoolLongOrderBubble::getShowCount($sBubbleId);
            if (0 == $iShowCount) {
                CarpoolLongOrderBubble::setShowCount($sBubbleId);
            } else {
                return  array(
                    'errno'       => 0,
                    'errmsg'      => 'ok',
                    'data'        => '',
                    'bubble_flag' => false,
                );
            }
        }

        return $aAthenaRes;
    }
    */

    /**
     * 构建拼车通勤卡的气泡
     *
     * @param $aConfirmParams
     *
     * @return array
     */
    private function _buildCarpoolCommuteBubble($aConfirmParams) {
        //点选拼车
        if ((\BizLib\Constants\OrderSystem::TYPE_COMBO_CARPOOL == $aConfirmParams['order_info']['combo_type']
            && \BizLib\Utils\CarLevel::DIDI_PUTONG_CAR_LEVEL == $aConfirmParams['order_info']['require_level'])
        ) {
            $aCommuteInfo = $this->_oEstimatePriceCache->getCommuteInfo($aConfirmParams['passenger_info']['pid'], $aConfirmParams['pre_estimate_id']);
            //如果carpool_commute_info不为空，则代表
            if (!empty($aCommuteInfo) && !empty($aCommuteInfo['commute_card_info']) && $aCommuteInfo['need_show_commute_card_bubble']) {
                $aDecision    = [
                    'business_id'   => $aConfirmParams['common_info']['business_id'],
                    'product_id'    => $aConfirmParams['order_info']['product_id'],
                    'combo_type'    => $aConfirmParams['order_info']['combo_type'],
                    'require_level' => $aConfirmParams['order_info']['require_level'],
                ];
                $aGuideInfo[] = $this->_oDecision->buildGuideInfo($aDecision, 'carpool_commute', $aCommuteInfo);
            }
        }

        if (empty($aGuideInfo)) {
            return [];
        }

        $aAthenaInfo = array(
            'errno'  => 0,
            'errmsg' => 'ok',
            'data'   => array('guide' => $aGuideInfo,),
        );
        $this->_iShowCommuteCardBubble = 1;

        return $aAthenaInfo;
    }

    /**
     * 构建anycar推荐气泡
     * selectItem 顶导anycar下不会进来，所以只处理快车顶导下anycar逻辑
     *
     * @param array $aConfirmParams 基本信息
     * @return array
     */
    private function _buildAnycarBubble($aConfirmParams) {
        if (!\BizLib\Utils\Product::isAnyCar($aConfirmParams['order_info']['product_id'])) {
            return [];
        }

        $aCarNameConfig  = json_decode(Language::getTextFromDcmp('config_anycar-anycar_simple_name'), true);
        $aSelectCarNames = $aRecommendCarNames = [];
        $aMultiInfo      = json_decode($aConfirmParams['quotation_info']['multi_info'], true) ?: [];
        foreach ($aMultiInfo as $aProduct) {
            $sGroupKey = implode('_', [$aProduct['product_id'], $aProduct['require_level'], $aProduct['combo_type']]);
            $sCarName  = $aCarNameConfig[$sGroupKey] ?? '';
            if (empty($sCarName)) {
                continue;
            }

            if ($aProduct['is_recommend']) {
                $aRecommendCarNames[] = $sCarName;
            }

            if ($aProduct['is_selected']) {
                $aSelectCarNames[] = $sCarName;
            }
        }

        if (empty($aRecommendCarNames)) {
            return [];
        }

        $sBubbleText  = Language::getTextFromDcmp('config_anycar-recommend_more_car_with_name', ['car_names' => implode('、', $aSelectCarNames), 'recommend_car_names' => implode('、', $aRecommendCarNames)]);
        $iShowType    = DecisionLogic::TYPE_ANYCAR_RECOMMEND_BUBBLE;
        $aDecision    = [
            'business_id'   => $aConfirmParams['common_info']['business_id'],
            'product_id'    => $aConfirmParams['order_info']['product_id'],
            'combo_type'    => $aConfirmParams['order_info']['combo_type'],
            'require_level' => $aConfirmParams['order_info']['require_level'],
            'bubble_flag'   => true,
            'bubble_text'   => $sBubbleText,
            'show_type'     => $iShowType,
        ];
        $aGuideInfo[] = $this->_oDecision->buildGuideInfo($aDecision);

        $aAthenaInfo = array(
            'errno'  => 0,
            'errmsg' => 'ok',
            'data'   => array('guide' => $aGuideInfo,),
        );
        return $aAthenaInfo;
    }

    /**
     * 获取新手教育返回.
     *
     * @param $aConfirmParams
     *
     * @return array|mixed|string
     */
    private function _buildFreshEducationInfo($aConfirmParams) {
        $aProductInfo = $this->_oDecision->getDecisionProductInfo($aConfirmParams['order_info']['product_id'], $aConfirmParams['order_info']['require_level'], $aConfirmParams['order_info']['combo_type']);
        if ($aProductInfo['fresh_education']) {
            return (new FreshEducationLogic($aProductInfo, $aConfirmParams))->getFreshEducationConfig();
        }

        return [];
    }

    /**
     * @date 2018.08.15
     *
     * @param array $aConfirmParams
     *
     * <AUTHOR>
     *
     * @return array
     */
    private function _buildDDSReq($aConfirmParams) {
        // 超值出租车切换tab时不需要导流信息
        if (isset($aConfirmParams['product_category'])
            && \BizLib\Utils\ProductCategory::PRODUCT_CATEGORY_UNIONE_SPECIAL_PRICE == $aConfirmParams['product_category']
        ) {
            return array();
        }

        $aUser           = array(
            // todo   uid id
            'phone' => $aConfirmParams['passenger_info']['phone'] ?? '',
            'pid'   => $aConfirmParams['passenger_info']['pid'] ?? 0,
        );
        $aCommon         = array(
            'start_lat'         => $aConfirmParams['order_info']['from_lat'],
            'start_lng'         => $aConfirmParams['order_info']['from_lng'],
            'to_lat'            => $aConfirmParams['order_info']['to_lat'],
            'to_lng'            => $aConfirmParams['order_info']['to_lng'],
            'client_type'       => $aConfirmParams['common_info']['client_type'],
            'city'              => $aConfirmParams['order_info']['area'] ?? 0, //zhuyi
            'county'            => $aConfirmParams['order_info']['trip_country'] ?? 0, //doudi?
            'menu_id'           => $aConfirmParams['order_info']['menu_id'] ?? 0,
            'sub_menu_id'       => $aConfirmParams['order_info']['sub_menu_id'],
            'estimate_trace_id' => $aConfirmParams['estimate_trace_id'],
            'app_version'       => $aConfirmParams['common_info']['app_version'],
        );
        $aProducts       = array(
            array(
                'product_id'            => $aConfirmParams['order_info']['product_id'],
                'business_id'           => $aConfirmParams['common_info']['business_id'],
                'require_level'         => $aConfirmParams['order_info']['require_level'],
                'combo_type'            => $aConfirmParams['order_info']['combo_type'],
                'carpool_type'          => $this->_getCarpoolTypeByEstimateOrderInfo($aConfirmParams),
                'is_dual_carpool_price' => $aConfirmParams['n_tuple']['is_dual_carpool_price'],
                'estimate_id'           => $aConfirmParams['pre_estimate_id'],
                'is_default'            => 1,
                'station_id'            => $aConfirmParams['order_info']['current_station_id'] ?? '',
                'pre_total_fee'         => 0,
                'estimate_fee'          => 0,
                'discount'              => new \Dirpc\SDK\EstimateDecision\DiscountInfo(),
                'dynamic'               => new \Dirpc\SDK\EstimateDecision\DynamicInfo(),
                'carpool_long_order'    => $aConfirmParams['n_tuple']['carpool_long_order'],
                'carpool_price_type'    => $aConfirmParams['n_tuple']['carpool_price_type'],
            ),
        );
        $aDecisionParams = array(
            'user'        => $aUser,
            'common'      => $aCommon,
            'products'    => $aProducts,
            'source_from' => self::DDS_SOURCE_FROM_MARK,
        );

        return $aDecisionParams;
    }

    /**
     * @param array $aConfirmParams
     */
    private function _writeKafka(array $aConfirmParams, $sEstimateID) {
        if (!empty($aConfirmParams['passenger_info']['pid'])) {
            $aConfirmKafka = array();
            $aConfirmKafka['passenger_phone'] = $aConfirmParams['passenger_info']['phone'];
            $aConfirmKafka['passenger_id']    = $aConfirmParams['passenger_info']['pid'];
            $aConfirmKafka['district']        = $aConfirmParams['order_info']['district'];
            $aConfirmKafka['area']            = $aConfirmParams['order_info']['area'];
            $aConfirmKafka['channel']         = $aConfirmParams['order_info']['channel'];
            $aConfirmKafka['starting_lng']    = $aConfirmParams['order_info']['from_lng'];
            $aConfirmKafka['starting_lat']    = $aConfirmParams['order_info']['from_lat'];
            $aConfirmKafka['county']          = (string)($aConfirmParams['order_info']['county']);
            $aConfirmKafka['dest_lng']        = $aConfirmParams['order_info']['to_lng'];
            $aConfirmKafka['dest_lat']        = $aConfirmParams['order_info']['to_lat'];
            $aConfirmKafka['create_time']     = time();
            $aConfirmKafka['product_id']      = $aConfirmParams['order_info']['product_id'];
            $aConfirmKafka['car_type']        = $aConfirmParams['order_info']['require_level'];
            $aConfirmKafka['estimate_type']   = Constants\Common::ESTIMATE_TYPE_CONFIRM;
            $aConfirmKafka['bubble_id']       = $sEstimateID;
            $aConfirmKafka['estimate_id']     = $aConfirmParams['pre_estimate_id'];
            $aConfirmKafka['combo_type']      = $aConfirmParams['order_info']['combo_type'] ?? 0;
            $aConfirmKafka['scene_type']      = $aConfirmParams['order_info']['scene_type'];
            $aConfirmKafka['starting_name']   = (string)($aConfirmParams['order_info']['starting_name']);
            $aConfirmKafka['dest_name']       = (string)($aConfirmParams['order_info']['dest_name']);

            $aOrderInfo = $aConfirmParams['order_info'];
            $aNewField  = \BizCommon\Logics\Order\FieldConverter::getNDimensionalNewField($aOrderInfo);
            $aOrderInfo = array_merge($aNewField, $aOrderInfo);
            $aConfirmKafka['n_tuple'] = json_encode(\BizCommon\Logics\Order\FieldOrderNTuple::getOrderNTupleByOrder($aOrderInfo), JSON_UNESCAPED_UNICODE);
            // 备注: multi_require_product里未获取价格pre_total_fee...
            $aNewMultiRequireProduct = AnyCarOrderLogic::getInstance()
                ->getNewMultiRequireProductForEstimate($aConfirmParams['multi_require_product']);

            $aConfirmKafka['multi_require_product'] = !empty($aNewMultiRequireProduct) ? json_encode($aNewMultiRequireProduct) : '';
            $aConfirmKafka['is_anycar'] = \BizLib\Utils\Product::isAnyCar($aConfirmParams['order_info']['product_id']);

            $oPassengerEstimateTopic = new PassengerEstimateTopic();
            $oPassengerEstimateTopic->sync($aConfirmKafka);
        }
    }

    /**
     * @param array $aConfirmParams
     * @param int   $sPreEstimateId
     */
    private function _writePublicLog(array $aConfirmParams, $sEstimateId) {
        // $this->load->model('order/OrderStation');
        $aStationInfo       = array();
        $sTraceId           = $aConfirmParams['estimate_trace_id'];
        $sChannel           = $aConfirmParams['passenger_info']['channel'];
        $sCarLevel          = $aConfirmParams['order_info']['require_level'];
        $sConfirmEstimateId = $aConfirmParams['pre_estimate_id'];
        $iComboType         = $aConfirmParams['order_info']['combo_type'];
        $iProductId         = $aConfirmParams['order_info']['product_id'];
        $iSceneType         = $aConfirmParams['order_info']['scene_type'];
        $aDynamicPriceCache = $this->_oEstimatePriceCache->getDynamicPriceCache($sCarLevel, $sConfirmEstimateId);
        $aDynamicPriceCache = $this->_formatDynamicPrice($aConfirmParams, $aDynamicPriceCache);
        // $aConfirmCache      = $this->_oEstimatePriceCache->getConfirmCache($sConfirmEstimateId);
        $aDecisionProductInfo = $this->_oDecision->getDecisionProductInfo($aConfirmParams['order_info']['product_id'], $aConfirmParams['order_info']['require_level'], $aConfirmParams['order_info']['combo_type']);
        if (\BizCommon\Utils\Horae::isCarpoolDualPrice($aDecisionProductInfo) && $aDecisionProductInfo['fresh_education']) {
            $this->_iShowDualPriceEducation = 1;
        }

        // todo activity_info为一个list，切换过程代码
        // if (empty($aConfirmCache['activity_info'][0])) {
        //     $aConfirmCache['activity_info'][0] = $aConfirmCache['activity_info'];
        // }
        if (Constants\Horae::TYPE_COMBO_CARPOOL == $iComboType
            && \BizLib\Utils\Product::PRODUCT_ID_FAST_CAR == $iProductId
            && Constants\Horae::HORAE_SCENE_TYPE_STATION_CARPOOL == $iSceneType
        ) {
            $aStationInfo = OrderStation::getInstance()->getStationInfoByTraceId($sTraceId, $sCarLevel);
        }

        // $aCouponLog = $this->_getCouponLog($sChannel, $iComboType, $aConfirmCache['activity_info'][0]['coupon_info']);
        if (\BizLib\Constants\Horae::O_TRAFFIC_TYPE_GS_FROM_AIRPORT == $aConfirmParams['order_info']['oType']) {
            $iTrafficType = Order::TYPE_GS_FROM_AIRPORT;
        } elseif (\BizLib\Constants\Horae::O_TRAFFIC_TYPE_GS_TO_AIRPORT == $aConfirmParams['order_info']['oType']) {
            $iTrafficType = Order::TYPE_GS_TO_AIRPORT;
        } else {
            $iTrafficType = $aConfirmParams['order_info']['combo_type'];
        }

        // 获取anycar的multi_require_product, 包含了预估价等
        // $aNewMultiRequireProduct = AnyCarOrderLogic::getInstance()->getMultiBillInfo($aConfirmCache['bill_info']);
        $aConfirmLogParams = [
            'opera_stat_key'               => self::CONFIRM_PUBLIC_KEY,

            'pid'                          => $aConfirmParams['passenger_info']['pid'] ?? 0,
            'phone'                        => $aConfirmParams['passenger_info']['phone'] ?? 0,
            // 'coupon'                               => json_encode($aCouponLog),
            'product_id'                   => $aConfirmParams['order_info']['product_id'],
            'area'                         => $aConfirmParams['order_info']['area'],
            'to_area'                      => $aConfirmParams['order_info']['to_area'],
            'district'                     => $aConfirmParams['order_info']['district'],
            'require_car_level'            => $aConfirmParams['order_info']['require_level'],
            'require_level'                => $aConfirmParams['order_info']['require_level'],
            'flng'                         => $aConfirmParams['order_info']['from_lng'],
            'flat'                         => $aConfirmParams['order_info']['from_lat'],
            'tlng'                         => $aConfirmParams['order_info']['to_lng'],
            'tlat'                         => $aConfirmParams['order_info']['to_lat'],
            'like_wait'                    => $aConfirmParams['order_info']['willing_wait'],
            'order_type'                   => $aConfirmParams['order_info']['order_type'],
            'guide_state'                  => $aConfirmParams['order_info']['guide_state'],
            'from_name'                    => str_replace(PHP_EOL, '', $aConfirmParams['order_info']['from_name']),
            'to_name'                      => str_replace(PHP_EOL, '', $aConfirmParams['order_info']['to_name']),
            'scene_type'                   => (int)($aConfirmParams['order_info']['scene_type']),

            'dynamic'                      => $aDynamicPriceCache['is_dynamic'],
            'dynamic_price_id'             => $aDynamicPriceCache['dynamic_id'],
            'carpool_dynamic_price_id'     => $aDynamicPriceCache['carpool_dynamic_id'],
            'dynamic_price'                => json_encode($aDynamicPriceCache['dynamic_price']),
            'dynamic_kind'                 => json_encode($aDynamicPriceCache['dynamic_kind']),
            'dynamic_type'                 => json_encode($aDynamicPriceCache['dynamic_type']),
            'near_driver_num'              => json_encode($aDynamicPriceCache['near_driver_num']),
            'near_order_num'               => json_encode($aDynamicPriceCache['near_order_num']),
            'avg_driver_start_distance'    => json_encode($aDynamicPriceCache['avg_driver_start_distance']),
            'start_dest_distance'          => json_encode($aDynamicPriceCache['start_dest_distance']),
            'place'                        => json_encode($aDynamicPriceCache['place']),
            // 'pre_total_fee'                        => $aConfirmCache['bill_info']['total_fee'] ?? 0,
            // 'carpool_origin_fee'                   => json_encode(
            //     [
            //         'cap_price'          => $aConfirmCache['bill_info']['dynamic_total_fee'] ?? 0,
            //         'carpool_fail_price' => $aConfirmCache['bill_info']['carpool_fail_dynamic_total_fee'] ?? 0,
            //     ]
            // ),
            // 'dynamic_discount_id'                  => !$this->_bIsCarpool($iComboType) ? $aConfirmCache['bill_info']['dynamic_price_id'] : '',
            // 'carpool_dynamic_discount_id'          => $this->_bIsCarpool($iComboType) ? $aConfirmCache['bill_info']['dynamic_price_id'] : '',
            // 'wait_discount'                        => $this->_bIsCarpool($iComboType) ? $aConfirmCache['bill_info']['wait_discount'] : 0.1,
            // 'wait_minute'                          => $this->_bIsCarpool($iComboType) ? $aConfirmCache['bill_info']['wait_minute'] : 10,
            // 'is_hit_member_capping'                => (int)($aConfirmCache['bill_info']['is_hit_member_capping'] ?? 0), //缺失
            // 'member_dynamic_capping'               => $aConfirmCache['bill_info']['member_dynamic_capping'] ?? -1,  //member_dynamic_capping
            // 'dynamic_price_without_member_capping' => $aConfirmCache['bill_info']['dynamic_price_without_member_capping'] ?? 0,               //dynamic_price_without_member_capping
            // 'br_default_pay_type'                  => $this->_getSelectedPayType($aConfirmCache['payments_info']['user_pay_info']['busi_payments'] ?? []), // 巴西支付方式
            // 'carpool_default_pay_type'             => $this->_getSelectedPayType($aConfirmCache['payments_info']['user_pay_info']['carpool']['busi_payments'] ?? []),
            // 'noncarpool_default_pay_type'          => $this->_getSelectedPayType($aConfirmCache['payments_info']['user_pay_info']['noncarpool']['busi_payments'] ?? []),
            // 'default_carpool'                      => $aConfirmCache['bill_info']['is_carpool_open'] ?? 0, // 0 默认非拼车 1拼车
            // 'is_carpool_request'                   => (int)($aConfirmCache['bill_info']['is_carpool_open']),
            // 'likeWait_open_flag'                   => $aConfirmCache['bill_info']['is_carpool_open'],
            'bubble_id'                    => $sEstimateId,
            // 'time_cost'                            => $aConfirmCache['bill_info']['driver_minute'],
            // 'driver_second'                        => $aConfirmCache['bill_info']['driver_second'],
            // 'discount_fee'                         => json_encode(
            //    [
            //        'cap_discount_fee'          => $aConfirmCache['activity_info'][0]['discount_fee'],
            //        'carpool_fail_discount_fee' => $aConfirmCache['activity_info'][1]['discount_fee'] ?? null,
            //    ]
            // ),
            // 'nocarpool_fee'                        => $aConfirmCache['activity_info'][0]['estimate_fee'],
            'station_id'                   => $aStationInfo['station_id'] ?? '0',
            'traffic_type'                 => $iTrafficType,
            'scene_enter'                  => $aConfirmParams['order_info']['scene_enter'],
            'call_car_type'                => $aConfirmParams['order_info']['call_car_type'],

            'member_level_id'              => $aConfirmParams['passenger_info']['member_profile']['level_id'] ?? 0,
            'imei'                         => $aConfirmParams['common_info']['imei'],
            'appversion'                   => $aConfirmParams['common_info']['app_version'],
            'user_type'                    => $aConfirmParams['common_info']['data_type'],
            'datatype'                     => $aConfirmParams['common_info']['data_type'],
            'pLang'                        => $aConfirmParams['common_info']['lang'],
            // 'default_pay_type'                     => $aConfirmCache['payments_info']['user_pay_info']['default_tag'] ?? 0,
            'carpool_seat_num'             => $aConfirmParams['order_info']['carpool_seat_num'],
            'anycar_carpool_seat_num'      => $aConfirmParams['order_info']['anycar_carpool_seat_num'],
            'county'                       => (string)($aConfirmParams['order_info']['county']),
            'to_county'                    => (string)($aConfirmParams['order_info']['to_county']),
            'designated_driver'            => $aConfirmParams['common_info']['designated_driver'],
            'combo_type'                   => $aConfirmParams['order_info']['combo_type'] ?? 0,
            // 'multi_require_product'                => AnyCarOrderLogic::getInstance()->buildMultiForPublicLogKafka($aNewMultiRequireProduct),
            // 'preference_product'                   => AnyCarOrderLogic::getInstance()->buildPreferenceForPublicLogKafka($aNewMultiRequireProduct, $sChannel),
            'is_anycar'                    => \BizLib\Utils\Product::isAnyCar($iProductId),

            //小费功能已下线
            'tip'                          => 0,
            //目标用户已下线
            'target_user'                  => 1,
            //智能导流已下线
            'is_smart_request'             => 0,
            //使用动态折扣或mis折扣已下线
            'price_discount'               => 0,
            'dynamic_discount'             => 0,
            'discount_type'                => 0,
            'estimate_type'                => Constants\Common::ESTIMATE_TYPE_CONFIRM,
            'estimate_id'                  => $sConfirmEstimateId,
            'is_show_commute_card_h5'      => $this->_iShowCommuteCardH5,
            'extra_stops'                  => $aConfirmParams['order_info']['extra_stops'],
            'is_show_commute_card_bubble'  => $this->_iShowCommuteCardBubble,
            'is_show_dual_price_education' => $this->_iShowDualPriceEducation,
        ];

        // product_category=39表示特价出租车
        $aConfirmLogParams['is_special_price'] = 0;
        if (\BizLib\Utils\ProductCategory::PRODUCT_CATEGORY_UNIONE_SPECIAL_PRICE == $aConfirmParams['product_category']) {
            $aConfirmLogParams['is_special_price'] = 1;
        }

        PublicLog::writeLogForOfflineCal('public', $aConfirmLogParams);

        return;
    }

    /**
     * @param array $aConfirmParams confirmParams
     * @return string
     */
    private function _setEstimateIdCache($aConfirmParams) {
        $iPid = $aConfirmParams['passenger_info']['pid'];
        $sConfirmEstimateId = $aConfirmParams['pre_estimate_id'];
        $iProductId         = $aConfirmParams['order_info']['product_id'];
        $iComboType         = $aConfirmParams['order_info']['combo_type'];
        $sCarLevel          = $aConfirmParams['order_info']['require_level'];
        $sEstimateId        = UtilHelper::getEstimateId($iProductId, $iComboType, $sCarLevel);
        $this->_oEstimatePriceCache->setSelectedEstimateId($sEstimateId, $iPid);
        $this->_oEstimatePriceCache->setEstimateId($sConfirmEstimateId, $iPid);
        // 点选anyCar 设置anyCar的冒泡特征
//        if ((\BizLib\Constants\Horae::HORAE_SCENE_TYPE_DEFAULT == $aConfirmParams['order_info']['scene_type']
//            && \BizLib\Utils\CarLevel::DIDI_ANY_CAR_CAR_LEVEL == $aConfirmParams['order_info']['require_level'])
//            || \BizLib\Constants\Horae::HORAE_SCENE_TYPE_SPECIAL_RATE == $aConfirmParams['order_info']['scene_type']
//            || \BizLib\Utils\CarLevel::DIDI_XIAOBA_CAR_LEVEL == $aConfirmParams['order_info']['require_level']
//        ) {
//            //$this->load->model('passenger/Passenger');
//            Passenger::getInstance()->setBubbleTag($iPid, $iProductId, 1);
//        }
        return $sEstimateId;
    }

    private function _setCommuteCardCache($aConfirmParams) {
        //点选拼车
        if ((\BizLib\Constants\OrderSystem::TYPE_COMBO_CARPOOL == $aConfirmParams['order_info']['combo_type']
            && \BizLib\Utils\CarLevel::DIDI_PUTONG_CAR_LEVEL == $aConfirmParams['order_info']['require_level'])
        ) {
            $aRetCommuteInfo = $this->_oEstimatePriceCache->getCommuteInfo($aConfirmParams['passenger_info']['pid'], $aConfirmParams['pre_estimate_id']);
            if (!empty($aRetCommuteInfo)) {
                $sRouteId = $aRetCommuteInfo['route_id'];

                if (true == $aRetCommuteInfo['show_h5']) {
                    // 记录该用户这次冒泡是否展示了H5，用于发单时判断是否过拒绝购买通勤卡
                    if (!$aRetCommuteInfo['has_showed_H5']) {
                        $aRetCommuteInfo['has_showed_H5'] = 1;
                        $this->_oEstimatePriceCache->setCommuteInfo($aConfirmParams['passenger_info']['pid'], $aConfirmParams['pre_estimate_id'], $aRetCommuteInfo);
                    }

                    //记录该用户该路线是否展示过，为了X天展示一次
                    $bCommuteCardShow = $this->_oEstimatePriceCache->getCommuteCardCache($aConfirmParams['passenger_info']['pid'], $sRouteId);
                    if (!$bCommuteCardShow) {
                        $this->_iShowCommuteCardH5 = 1;
                        $this->_oEstimatePriceCache->setCommuteCardCache($aConfirmParams['passenger_info']['pid'], $sRouteId);
                    }
                }

                //如果没有展示过气泡，但是需要展示气泡，并且这次调用接口也展示了。那么就需要做标记。标记的作用是用来记录拒绝了多少次。
                if (!$aRetCommuteInfo['has_showed_bubble'] && $aRetCommuteInfo['need_show_commute_card_bubble'] && $this->_iShowCommuteCardBubble) {
                    $aRetCommuteInfo['has_showed_bubble'] = 1;
                    $this->_oEstimatePriceCache->setCommuteInfo($aConfirmParams['passenger_info']['pid'], $aConfirmParams['pre_estimate_id'], $aRetCommuteInfo);
                }
            }
        }

        return;
    }

    /**
     * @param $aPayments
     *
     * @return int
     */
    private function _getSelectedPayType($aPayments) {
        foreach ($aPayments as $aPayment) {
            if (!empty($aPayment['isSelected'])) {
                return $aPayment['tag'];
            }
        }

        return 0;
    }

    /**
     * 生成券相关的统计信息.
     *
     * @param int   $iChannel    乘客端渠道
     * @param array $aCouponInfo 默认券和乘客运营券信息
     */
    private function _getCouponLog($iChannel, $iComboType, $aCouponInfo) {
        $aRet  = array();
        $sMode = 'noncarpool';
        if (Constants\Horae::TYPE_COMBO_CARPOOL == $iComboType || Constants\Horae::TYPE_COMBO_CARPOOL_INTER_CITY == $iComboType) {
            $sMode = 'carpool';
        }

        if (empty($aCouponInfo['default_coupon'])) {
            $aRet[$sMode]['default_coupon'] = [];
        } else {
            $aRet[$sMode]['default_coupon'] = [
                'channel'       => $iChannel,
                'batchid'       => $aCouponInfo['default_coupon']['batchid'] ?? 0,
                'amount'        => ($aCouponInfo['default_coupon']['amount'] ?? 0) / 100,
                'estimate_show' => $aCouponInfo['default_coupon']['estimate_show'] ?? 0,
            ];
        }

        if (empty($aCouponInfo['activity_coupon'])) {
            $aRet[$sMode]['activity_coupon'] = [];
        } else {
            $aRet[$sMode]['activity_coupon'] = [
                'channel'       => $iChannel,
                'batchid'       => $aCouponInfo['activity_coupon']['batchid'] ?? 0,
                'amount'        => $aCouponInfo['activity_coupon']['money'] ?? 0,
                'estimate_show' => $aCouponInfo['activity_coupon']['estimate_show'] ?? 0,
            ];
        }

        return $aRet;
    }

    /**
     * 获取动态调价数据.
     *
     * @return array 动态调价相关字段
     */
    private function _formatDynamicPrice($aConfirmInfo, $aDynamicCache) {
        $sCarLevel = $aConfirmInfo['order_info']['require_level'];
        $aResult['dynamic_id']         = $aDynamicCache['noncarpool']['dynamic_price_id'] ?? 0;
        $aResult['carpool_dynamic_id'] = $aDynamicCache['carpool']['dynamic_price_id'] ?? 0;
        $aResult['is_dynamic']         = 0;
        $fDynamicPrice = $aDynamicCache['noncarpool']['dynamic_price'] ?? 0;
        $fWhitoutMemberCappingDynamicPrice = $aDynamicCache['noncarpool']['dynamic_price'] ?? 0;
        $fCarpoolPoolDynamicPrice          = $aDynamicCache['carpool']['dynamic_price'] ?? 0;
        $fWhitoutMemberCappingCarpoolPoolDynamicPrice = $aDynamicCache['carpool']['dynamic_price'] ?? 0;
        if ($fDynamicPrice > 0 || $fCarpoolPoolDynamicPrice > 0
            || $fWhitoutMemberCappingDynamicPrice || $fWhitoutMemberCappingCarpoolPoolDynamicPrice
        ) {
            $aResult['is_dynamic'] = 1;
        }

        $aResult['dynamic_price'][$sCarLevel]   = array(
            'carpool'    => $fCarpoolPoolDynamicPrice,
            'noncarpool' => $fDynamicPrice,
        );
        $aResult['dynamic_kind'][$sCarLevel]    = array(
            'carpool'    => $aDynamicCache['carpool']['dynamic_kind'] ?? 0,
            'noncarpool' => $aDynamicCache['noncarpool']['dynamic_kind'] ?? 0,
        );
        $aResult['dynamic_type'][$sCarLevel]    = array(
            'carpool'    => $aDynamicCache['carpool']['dynamic_type'] ?? 0,
            'noncarpool' => $aDynamicCache['noncarpool']['dynamic_type'] ?? 0,
        );
        $aResult['near_driver_num'][$sCarLevel] = array(
            'carpool'    => $aDynamicCache['carpool']['near_driver_num'] ?? 0,
            'noncarpool' => $aDynamicCache['noncarpool']['near_driver_num'] ?? 0,
        );
        $aResult['near_order_num'][$sCarLevel]  = array(
            'carpool'    => $aDynamicCache['carpool']['near_order_num'] ?? 0,
            'noncarpool' => $aDynamicCache['noncarpool']['near_order_num'] ?? 0,
        );
        $aResult['avg_driver_start_distance'][$sCarLevel] = array(
            'carpool'    => $aDynamicCache['carpool']['avg_driver_start_distance'] ?? 0,
            'noncarpool' => $aDynamicCache['noncarpool']['avg_driver_start_distance'] ?? 0,
        );
        $aResult['place'][$sCarLevel] = array(
            'carpool'    => $aDynamicCache['carpool']['place'] ?? 0,
            'noncarpool' => $aDynamicCache['noncarpool']['place'] ?? 0,
        );
        $aResult['start_dest_distance'][$sCarLevel] = array(
            'carpool'    => $aDynamicCache['carpool']['start_dest_distance'] ?? 0,
            'noncarpool' => $aDynamicCache['noncarpool']['start_dest_distance'] ?? 0,
        );

        return $aResult;
    }

    /**
     * @param $iComboType
     *
     * @return bool
     */
    private function _bIsCarpool($iComboType) {
        if (Constants\Horae::TYPE_COMBO_CARPOOL == $iComboType || Constants\Horae::TYPE_COMBO_CARPOOL_INTER_CITY == $iComboType) {
            return true;
        }

        return false;
    }

    /**
     * 导流交互优化开关。
     *
     * apollo switch
     */
    private function _checkDegrade($aConfirmParams) {
        // 预约单没有气泡
        if (empty($aConfirmParams) || empty($aConfirmParams['order_info']) || !empty($aConfirmParams['order_info']['order_type'])) {
            return true;
        }

        // 小于5.2.20,旧的导流交互模式
        if (version_compare($aConfirmParams['common_info']['app_version'], '5.2.22') < 0) {
            return true;
        }

        return false;

       // $apolloV2 = new \Xiaoju\Apollo\Apollo();
        // return !$apolloV2->featureToggle(
           // 'gs_athena_interaction_optimize_switch',
           // array(
               // 'key'         => rand(1, 500000),
               // 'pid'         => (int)($aConfirmParams['passenger_info']['pid']),
               // 'city'        => (string)($aConfirmParams['order_info']['area']),
               // 'phone'       => (string)($aConfirmParams['passenger_info']['phone']),
               // 'app_version' => (string)($aConfirmParams['common_info']['app_version']),
               // 'client_type' => (string)($aConfirmParams['common_info']['client_type']),
           // )
       // )->allow();
    }

    /**
     * 根据预估信息确定carpool_type.
     *
     * @param $aParams
     *
     * @return int
     */
    private function _getCarpoolTypeByEstimateOrderInfo($aParams) {
        if (BizLib\Constants\Horae::TYPE_COMBO_CARPOOL_INTER_CITY == $aParams['order_info']['combo_type']) {
            return OrderNTuple::CARPOOL_TYPE_INTERCITY;
        } elseif (BizLib\Constants\Horae::TYPE_COMBO_CARPOOL == $aParams['order_info']['combo_type']) {
            if ('zh-CN' != Language::getLanguage()) {
                return OrderNTuple::CARPOOL_TYPE_NORMAL;
            }

            if ($aParams['order_info']['current_station_id']) {
                return OrderNTuple::CARPOOL_TYPE_STATION;
            } else {
                $this->orderStation   = new OrderStation();
                $this->_oOrderStation = $this->orderStation;
                $aStationList         = $this->_oOrderStation->getStationInfoByTraceId($aParams['estimate_trace_id'], $aParams['order_info']['require_level']);
                if (!empty($aStationList)) {
                    return OrderNTuple::CARPOOL_TYPE_STATION;
                }
            }
        }

        return OrderNTuple::CARPOOL_TYPE_NONE;
    }
}
