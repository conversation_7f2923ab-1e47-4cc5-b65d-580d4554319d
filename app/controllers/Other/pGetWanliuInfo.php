<?php

use BizCommon\Models\Passenger\PassengerUFS;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\MapHelper;
use BizLib\Utils\Request;
use BizLib\Constants;
use BizCommon\Models\Passenger\PassengerPFS;
use PreSale\Models\Business\Business;

/**===
 * @property Project $Project
 * @property Passenger $Passenger
 * @property Business $business
 * @property PassengerPFS $_oPassengerPFS
===*/

/**
 * Class PGetWanliuInfoController
 */
class PGetWanliuInfoController extends \PreSale\Core\Controller
{
    const VERSION = 1;
    const PRODUCT_ID_FAST_CAR  = 3;
    const NEW_USER_ORDER_COUNT = 3;

    const WLWEBAPP         = 'webapp';
    const CALL_FASTCAR_TAG = 'call_fastcar';	//PFS
    const APP_VERSION      = 'appversion';
    private $_oPassengerPFS;
    private $_oPassengerUFS;

    public function init() {
        parent::init();
        $this->_oPassengerPFS = PassengerPFS::getInstance();
        $this->_oPassengerUFS = PassengerUFS::getInstance();
        $this->passenger      = \BizCommon\Models\Passenger\Passenger::getInstance();
    }

    public function indexAction() {
        // list($sToken, $iSearchType, $sCallback, $sProjectFrom, $fLat, $fLng, $sAppVersion) = $this->_getAndCheckParams();
        list($sToken, $iSearchType, $sCallback, $sProjectFrom, $sAppVersion) = $this->_getAndCheckParams();
        $aUserInfo = [];
        $iPhone    = 0;
        //1.获取乘客信息
        $aPassenger = $this->passenger->getPassengerByToken($sToken);
        if (isset($aPassenger['phone'])) {
            $iPhone = $aPassenger['phone'];
        }

        $iPid = !empty($aPassenger['pid']) ? $aPassenger['pid'] : 0;
        list($iCallFastcar, $sPfsAppVersion) = $this->_getPassengerFeature($iPid);
        list($iIsSpecialRateUsed, $iNewUser) = $this->_getUFSPassengerFeature($iPid);
        //2.获取用户信息
        if (1 == $iSearchType) {
            if (empty($sAppVersion)) {
                $sAppVersion = $sPfsAppVersion;
            }

            $aUserInfo = $this->_getUserInfo($iPhone, $sAppVersion);
        }

        $aUserInfo['is_special_rate_used']      = $iIsSpecialRateUsed;
        $aUserInfo['is_miniapp_dache_new_user'] = $iNewUser;

        //3.获取拼车信息
        $aPassengerConfigInfo = ['car_pool_switch' => 0, ];
        // $aPassengerConfigInfo = $this->_getCarPoolInfo($iPhone, $iPid, $fLat, $fLng);
        //4.评论方式，老代码中有1%小流量，经过风光确认下掉
        $iMustComment        = 0;
        $iMustCommentFastCar = 0;
        $aConfigPassenger    = NuwaConfig::config('config_passenger', 'config_passenger');
        $iCallCarOpenFlag    = isset($aConfigPassenger['callcar_open']) ? $aConfigPassenger['callcar_open'] : 0;
        if ($iCallCarOpenFlag && $iPid) {
            $iCallcarOpen = 1;
        } else {
            $iCallcarOpen = 0;
        }

        $aRet = [
            'errno'                 => 0,
            'errmsg'                => 'success',
            'user_info'             => $aUserInfo,
            'version'               => self::VERSION,
            'passenger_config_info' => $aPassengerConfigInfo,
            'must_comment'          => $iMustComment,
            'must_comment_fastcar'  => $iMustCommentFastCar,
            'callcar_open'          => $iCallcarOpen,
            'show_type'             => 0 == $iCallFastcar ? 1 : 0, //1 : 不展示快捷新客提示, 0: 展示
        ];
        if (!empty($sProjectFrom) && false !== strpos($sProjectFrom, self::WLWEBAPP) && !empty($sCallback)) {
            $this->sendTextJson($aRet, $sCallback);
        } else {
            $this->sendTextJson($aRet);
        }

        return;
    }

    /**
     * 获取用户信息, 暂时只获取是否企业用户
     * @param int    $iPhone      乘客手机号
     * @param string $sAppVersion 版本号
     * @return array
     */
    private function _getUserInfo($iPhone, $sAppVersion) {
        $aRet = [];
        if (!empty($iPhone)) {
            $bIsVip            = (new Business())->isBusinessUser($iPhone,  0, $sAppVersion);
            $aRet['user_type'] = $bIsVip ? 2 : '';
        } else {
            return $aRet;
        }

        return $aRet;
    }

    /*
     * 获取城市开通拼车与否及拼车人数配置
     * @params $iPhone int
     * @params $iPid   int
     * @params $fLag   float
     * @params $fLng   float
     *
     * @return  $aCarPoolInfo array
     * @auther <EMAIL>
     */
    private function _getCarPoolInfo($iPhone, $iPid, $fLag, $fLng) {
        $aCarPoolInfo = ['car_pool_switch' => 0, ];
        $aAreaInfo    = MapHelper::getAreaInfoByLoc($fLng, $fLag);
        if (!empty($aAreaInfo)) {
            $sDistrict = $aAreaInfo['district'];
        } else {
            return $aCarPoolInfo;
        }

        return $aCarPoolInfo;
    }

    /**
     * @desc 获取并校验参数
     * @params
     *
     * @return array
     **/
    private function _getAndCheckParams() {
        $oReq         = Request::getInstance();
        $sToken       = $oReq->getStr('token');
        $iSearchType  = $oReq->getInt('search_type');
        $sCallback    = $oReq->getStr('callback');
        $sProjectFrom = $oReq->getStr('from');
        // $fLat         = $oReq->getFloat('lat');
        // $fLng         = $oReq->getFloat('lng');
        $sAppVersion = $oReq->getStr('appversion');

        // return [$sToken, $iSearchType, $sCallback, $sProjectFrom, $fLat, $fLng, $sAppVersion];
        return [$sToken, $iSearchType, $sCallback, $sProjectFrom, $sAppVersion];
    }

    /**
     * @desc 通过pid获取用户快捷订单量
     *
     * @param int $iPid
     *
     * @return array
     */
    private function _getPassengerFeature($iPid) {
        $aRet = [
            -1, //-1 pfs访问失败,或未登录用户,0 完单数为0
            '',
        ];
        if (empty($iPid)) {
            return $aRet;
        }

        $aPassengerFeature = $this->_oPassengerPFS->getPFS(
            $iPid,
            [
                Constants\Pfs::SCENE_STATS => [
                    self::CALL_FASTCAR_TAG,
                    self::APP_VERSION,
                ],
            ]
        );
        if (empty($aPassengerFeature) && false == $aPassengerFeature) {
            return $aRet;
        }

        $iRet = 0;
        $sRet = '';
        if (!empty($aPassengerFeature[Constants\Pfs::SCENE_STATS][self::CALL_FASTCAR_TAG])) {
            $iRet = (int)($aPassengerFeature[Constants\Pfs::SCENE_STATS][self::CALL_FASTCAR_TAG]);
        }

        if (!empty($aPassengerFeature[Constants\Pfs::SCENE_STATS][self::CALL_FASTCAR_TAG])) {
            $sRet = (string)($aPassengerFeature[Constants\Pfs::SCENE_STATS][self::APP_VERSION]);
        }

        return [$iRet, $sRet];
    }

    /**
     * @desc 通过pid获取用户快捷订单量
     *
     * @param int $iPid 用户pid
     *
     * @return array
     */
    private function _getUFSPassengerFeature($iPid) {
        $aRet = [
            0, //pid为空
        ];
        if (empty($iPid)) {
            return $aRet;
        }

        $aKeys = ['special_rate.is_last_order','mini_program.create_order_count_60'];

        $aConditions        = ['passenger_id' => $iPid,];
        $aFeatureRet        = $this->_oPassengerUFS->getUfsFeature($aKeys, $aConditions, 'passenger');
        $iIsSpecialRateUsed = 1 == $aFeatureRet['special_rate.is_last_order'] ? 1 : 0;
        $iNewUser           = 0;
        if (isset($aFeatureRet['mini_program.create_order_count_60']) && $aFeatureRet['mini_program.create_order_count_60'] <= self::NEW_USER_ORDER_COUNT) {
            $iNewUser = 1;
        }

        return [$iIsSpecialRateUsed,$iNewUser];
    }
}
