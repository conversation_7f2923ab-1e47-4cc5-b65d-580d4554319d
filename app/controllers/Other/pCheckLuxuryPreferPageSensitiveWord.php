<?php

use BizLib\Utils;
use Dirpc\SDK\PreSale\VerifyLuxuryPreferPageSensitiveWordRequest as Request;
use Dirpc\SDK\PreSale\VerifyLuxuryPreferPageSensitiveWordResponse as Response;
use BizLib\Utils\Common;
use BizCommon\Models\Passenger\Passenger;
use BizLib\Config as NuwaConfig;

/**
 * Class PCheckLuxuryPreferPageSensitiveWordController
 */
class PCheckLuxuryPreferPageSensitiveWordController extends \PreSale\Core\Controller
{

    /**
     * @return void
     */
    public function indexAction() {
        $oRequest = Utils\Request::getInstance();
        $oCheckSensitiveWordRequest = new Request();
        $oCheckSensitiveWordRequest->mergeFromJsonArray($oRequest->post());
        $sTobeVerifyWord = $oCheckSensitiveWordRequest->getToBeVerifyWord();
        $iContentType    = $oCheckSensitiveWordRequest->getDetectionType();
        $sToken          = $oCheckSensitiveWordRequest->getToken();
        $aPassengerInfo  = self::_getPassengerInfo($sToken);
        //格式化输出
        $oResponse = new Response();
        $oResponse = self::_verifySensitiveWord($sTobeVerifyWord, $iContentType, $aPassengerInfo['pid'], $oResponse);
        $aResponseInfo = $oResponse->serializeToJsonArray();
        $this->sendJson($aResponseInfo, (string)($this->getRequest()->getQuery('callback', false)));
    }

    /**
     * @param string                                                         $sTobeVerifyWord 待校验参数
     * @param int                                                            $iContentType    检测类型
     * @param string                                                         $sPassengerId    乘客id
     * @param \Dirpc\SDK\PreSale\VerifyLuxuryPreferPageSensitiveWordResponse $oResponse       返回结果
     * @return object $oResponse
     */
    private function _verifySensitiveWord($sTobeVerifyWord, $iContentType, $sPassengerId, $oResponse) {
        $aSensitiveWordTips = NuwaConfig::text('config_passenger', 'luxury_optional_page_sensitive_word_tips');
        // 先检测待检测词是否全是空格，如果全是空格没必要进行敏感词机审，属于无效昵称
        $bIsAllSpaceInString = Common::isAllSpaceInString($sTobeVerifyWord);
        if ($bIsAllSpaceInString) {
            $oResponse->setErrno(NICKNAME_CONTENT_INVALID_TYPE);
            $oResponse->setErrmsg($aSensitiveWordTips['invalid_nickname']);
            return $oResponse;
        }
        $aDetectParams['data'] = [
            'content'      => $sTobeVerifyWord,
            'content_type' => $iContentType,
            'pid'          => $sPassengerId,
        ];
        $bIsSensitiveWord = Common::isSensitiveWordInLuxuryPreferPageDirpc($aDetectParams);
        if ($bIsSensitiveWord) {
            $oResponse->setErrno(NICKNAME_CONTENT_VERIFY_FAILED_TYPE);
            $oResponse->setErrmsg($aSensitiveWordTips['sensitive_nickname']);
        }
        return $oResponse;
    }

    /**
     * 获取用户信息参数
     * @param string $sToken 用户token
     * @return array|mixed
     */
    private function _getPassengerInfo($sToken) {
        if (!empty($sToken)) {
            return (new Passenger())->getPassengerByToken($sToken);
        }
        return array();
    }
}
