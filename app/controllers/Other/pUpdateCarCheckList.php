<?php

use BizCommon\Models\Passenger\Passenger;
use BizLib\Client\UfsClient;
use BizLib\Constants\Ufs;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Exception\ExceptionWithResp;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\ExceptionHandler;
use BizLib\ErrCode\RespCode;
use BizLib\Utils\Language;
use PreSale\Core\Controller;

/**
 * Class pUpdateCarCheckList  更新车型勾选推荐开关
 */
class PUpdateCarCheckListController extends Controller
{

    /**
     * 乘客信息
     */
    private $_aPassengerInfo;

    /**
     * 请求体信息
     */
    private $_aRequestBody;

    const COMMONLY_CAR_LIST = 'user_decision.car_list_v2';// 车型列表key

    /**
     * 根据品类和城市获取计价规则详情
     * @return void
     */
    public function indexAction() {

        $aResponseInfo = UtilsCommon::pairErrNo(RespCode::P_SUCCESS, $this->aErrnoMsg);

        try {
            //验证参数
            $this->verify($_GET);

            $aFeature    = [self::COMMONLY_CAR_LIST => json_encode($this->_aRequestBody['car_list'])];
            $aConditions = ['uid' => $this->_aPassengerInfo['uid']];

            $aRes = (new UfsClient())->setFeature($aFeature, $aConditions, 'passenger');

            if (UfsClient::SUCCESS_CODE !== $aRes['errno']) {
                NuwaLog::warning(
                    Msg::formatArray(
                        Code::E_COMMON_SET_UFS_FAIL,
                        ['featureValues' => $aFeature, 'condition' => $aConditions, 'setUfsRes' => $aRes]
                    )
                );
            }
        } catch (\Exception $e) {
            $aContext = [
                'dltag'   => '_com_request_out_failure',
                'err_msg' => $this->aErrnoMsg,
            ];

            //异常处理并得到返回值
            $aResponseInfo = ExceptionHandler::getInstance()->handleException($e, $aContext);
        }

        $this->sendTextJson($aResponseInfo);
    }

    /**
     * 验证参数.
     * @param array $aRequest get请求
     * @return void
     * @throws ExceptionWithResp 自定义异常
     */
    public function verify($aRequest) {
        if (empty($aRequest['token'])) {
            throw new ExceptionWithResp(
                Code::E_COMMON_TOKEN_INVALID,
                RespCode::P_PARAMS_ERROR,
                '',
                [
                    'exceptionMsg' => [
                        'msg'      => 'token is empty',
                        'moreInfo' => [
                            'args' => (array)$this,
                        ],
                    ],
                ]
            );
        }

        $aPassenger = (Passenger::getInstance())->getPassengerByToken($aRequest['token']);
        $sUid       = $aPassenger['uid'] ?? 0;

        if (empty($sUid)) {
            throw new ExceptionWithResp(
                Code::E_COMMON_TOKEN_INVALID,
                RespCode::P_PARAMS_ERROR,
                '',
                [
                    'exceptionMsg' => [
                        'msg'      => 'uid is empty (token is invalid)',
                        'moreInfo' => [
                            'args' => (array)$this,
                            'uid'  => $sUid,
                        ],
                    ],
                ]
            );
        }

        $this->_aPassengerInfo = $aPassenger;

        $post = (array)json_decode(file_get_contents('php://input'), true);
        if (!self::COMMONLY_CAR_LIST != $post['key']) {
            throw new ExceptionWithResp(
                Code::E_COMMON_CONFIG_NOT_FOUNT,
                RespCode::P_PARAMS_ERROR,
                '',
                [
                    'exceptionMsg' => [
                        'msg'      => 'ufs key is invalid',
                        'moreInfo' => [
                            'args' => (array)$this,
                            'uid'  => $sUid,
                        ],
                    ],
                ]
            );
        }

        $this->_aRequestBody = $post;
    }
}
