<?php

use BizCommon\Models\Passenger\Passenger;
use Biz<PERSON>ib\Client\MemberGoSystemClient;
use BizLib\Client\OneConfClient;
use BizLib\Config;
use BizLib\Constants\Common;
use BizLib\ErrCode\RespCode;
use BizLib\ExceptionHandler;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\Language;
use BizLib\Utils\MapHelper;
use BizLib\Utils\PublicLog;
use BizLib\Utils\Request;
use PreSale\Logics\filter\FilterLogic;
use PreSale\Logics\newFormTab\globalData\SelectTab;
use PreSale\Logics\newFormTab\OneStopTab;
use PreSale\Logics\newFormTab\NewOneStopTab;
use PreSale\Logics\newFormTab\BuildOneStopTab;
use PreSale\Logics\newFormTab\OrderButtonInfo;
use PreSale\Logics\newFormTab\Tab;
use PreSale\Models\rpc\FenceRpc;
use Xiaoju\Apollo\Apollo;
use PreSale\Models\tagService;

/**
 * Class PGetDynamicConfigController
 */
class PGetDynamicConfigController extends \PreSale\Core\Controller
{


    /**
     * @return void
     */
    public function indexAction() {
        try {
            $aParams   = $this->_initParams();
            $aResponse = UtilsCommon::pairErrNo(RespCode::P_SUCCESS);

            $aResult['hit_sp_form']          = 0; //小程序前端表单是否命中特价版 -- 已下线
            $aResult['hit_estimate_v3_form'] = 1; //NA/小程序 6.5表单



            // 一站式出行
            $aOneStopRes = (new BuildOneStopTab())->build($aParams);
            $aResult['one_stop_tab']            = $aOneStopRes['one_stop_tab'];
            $aResult['is_show_new_maas_subtab'] = $aOneStopRes['is_show_new_maas_subtab'] ?? 0;
            $aResult['homepage_version_tag']    = $aOneStopRes['homepage_version_tag'];
            $aResult['one_stop_style']          = $aOneStopRes['one_stop_style'];

            //网约车tab (新表单tab信息)
            $aCarHailingTab          = (new Tab())->buildEstimateV3Tab($aParams);
            $aResult['estimate_tab'] = $aCarHailingTab;

            $aResult['hit_rec_carpool_v2'] = 1; // 直接赋值全量

            // 会员身份信息
            $aResult['member_info'] = $this->_buildMemberInfo($aParams);

            $aResult['estimate_tab_style'] = 0; // tab样式 功能废弃
            $aResult['hit_select_tab']     = 0; // 功能废弃
            $aResult['hit_bargain_form']   = 0; // 司乘议价独立页  关量

            $aResult['order_button_info'] = (new OrderButtonInfo())->build($aParams, $aCarHailingTab);
            // 冒泡页品类预期 1：走api下发（pGetFormRealData） 0：走旧链路（平滑移动）
            $aResult['hit_new_expect'] = $this->_getHitNewExpect($aParams);

            $timeAndLocationDiff            = $this->_getTimeAndLocationDiff($aParams);
            $aResult['estimate_cache_time'] = $timeAndLocationDiff['estimate_cache_time'];
            $aResult['estimate_location_diff'] = $timeAndLocationDiff['estimate_location_diff'];

            $aResponse['data'] = $aResult;

            $this->_writePublic($aParams, $aResult);
        } catch (\Exception $e) {
            $aErrMsg  = BizLib\Config::text('errno', 'pGetDynamicConfig_error_msg');
            $oHandler = ExceptionHandler::getInstance();
            $oHandler->handleException($e, ['err_msg' => $aErrMsg, ]);
        }

        $this->sendJson($aResponse);
    }

    /**
     * @desc 获取并校验参数
     *
     * @return array
     **/
    private function _initParams() {
        $oReq   = Request::getInstance();
        $sToken = $oReq->getStr('token');
        $fLat   = $oReq->getFloat('lat');
        $fLng   = $oReq->getFloat('lng');

        $fFromLat     = $oReq->getFloat('from_lat');
        $fFromLng     = $oReq->getFloat('from_lng');
        $sAppVersion  = $oReq->getStr('app_version');
        $iAccessKeyId = $oReq->getStr('access_key_id');
        $sLang        = $oReq->getStr('lang');

        $aParams = [
            'app_version'   => $sAppVersion,
            'access_key_id' => $iAccessKeyId,
            'from_lat'      => $fFromLat,
            'from_lng'      => $fFromLng,
            'lat'           => $fLat,
            'lng'           => $fLng,
            'lang'          => $sLang,
            'local_tab_id'  => $oReq->getStr('local_tab_id'),
        ];

        $sPixels = $oReq->getStr('pixels', '');
        if (!empty($sPixels)) {
            $sWidth            = trim(explode('*', $sPixels)[0]);
            $sHeight           = trim(explode('*', $sPixels)[1]);
            $aParams['width']  = (int)$sWidth;
            $aParams['height'] = (int)$sHeight;
        }

        if (!empty($sToken)) {
            $aPassengerInfo = Passenger::getInstance()->getPassengerByToken($sToken);
            if (!empty($aPassengerInfo)) {
                $aParams['phone'] = $aPassengerInfo['phone'];
                $aParams['pid']   = $aPassengerInfo['pid'];
                $aParams['uid']   = $aPassengerInfo['uid'];
            }
        }

        if (!empty($fLat) && !empty($fLng)) {
            try {
                $aAreaInfo = MapHelper::getAreaInfoByLoc($fLng, $fLat);
                if (!empty($aAreaInfo)) {
                    $aParams['city']     = $aAreaInfo['id'];
                    $aParams['county']   = $aAreaInfo['countyid'];
                    $aParams['district'] = $aAreaInfo['district'];
                }
            } catch (Exception $e) {
                BizLib\Log::notice('get area info fail', ['err' => $e->getMessage()]);
            }
        }

        if (!empty($fFromLng) && !empty($fFromLat)) {
            try {
                $aAreaInfo = MapHelper::getAreaInfoByLoc($fFromLng, $fFromLat);
                if (!empty($aAreaInfo)) {
                    $aParams['from_city']     = $aAreaInfo['id'];
                    $aParams['from_county']   = $aAreaInfo['countyid'];
                    $aParams['from_district'] = $aAreaInfo['district'];
                }
            } catch (Exception $e) {
                BizLib\Log::notice('get area info fail', ['err' => $e->getMessage()]);
            }
        }

        $aParams['person_tag'] = tagService\PersonTag::getInstance($aParams)->getPersonTag($aParams['pid']);

        return $aParams;
    }

    /**
     * @param array $aParams aParams
     * @return array
     */
    private function _buildMemberInfo($aParams) {
        $sApolloName = Language::getTextFromDcmp('config_text-bubble_page_member_apollo');
        $oApollo     = Apollo::getInstance()->featureToggle(
            $sApolloName,
            [
                Xiaoju\Apollo\ApolloConstant::APOLLO_INDIVIDUAL_ID => $aParams['pid'],
                'key'                                              => $aParams['pid'],
                'pid'                                              => $aParams['pid'],
                'phone'                                            => $aParams['phone'],
                'from_city'                                        => $aParams['from_city'],
                'city'                                             => $aParams['city'],
                'lang'                                             => $aParams['lang'],
                'access_key_id'                                    => $aParams['access_key_id'],
                'app_version'                                      => $aParams['app_version'],
            ]
        );

        if (!$oApollo->allow()) {
            return null;
        }

        $iHit = $oApollo->getParameter('is_show', 0);
        if (1 != $iHit) {
            return null;
        }

        $oMemberClient = new MemberGoSystemClient();
        $aMemberRes    = $oMemberClient->getMemberInfoByPid($aParams['pid'], 1);
        $aMemberData   = $aMemberRes['data'];
        if (empty($aMemberData)) {
            return null;
        }

        if (0 == $aMemberData['level_id']) {
            // 用户未授权会员情况
            return null;
        }

        $aConfig      = Config::text('config_text', 'bubble_page_member_config');
        $aLevelConfig = $aConfig[$aMemberData['level_id']];

        return [
            'icon' => $aLevelConfig['icon'],
        ];
    }

    /**
     * @param array $aParams  全部参数
     * @param array $aAllConf 所有配置
     * @return void
     */
    private function _writePublic($aParams, $aAllConf) {
        $aStat = [
            'opera_stat_key'    => 'g_order_dynamic_config',
            'passenger_id'      => $aParams['pid'],
            'uid'               => $aParams['uid'],
            'city'              => $aParams['city'],
            'access_key_id'     => $aParams['access_key_id'],
            'app_version'       => $aParams['app_version'],
            'is_v3'             => $aAllConf['hit_estimate_v3_form'],
            'hit_v8'            => SelectTab::getInstance()->getHitV8Exp() ? 1 : 0,
            'home_page_version' => SelectTab::getInstance()->getHomePageVersion(),
            'select_tab_reason' => SelectTab::getInstance()->getLandingReason(),
            'select_tab'        => SelectTab::getInstance()->getSelectTab(),
        ];

        PublicLog::writeLogForOfflineCal('public', $aStat);
    }



    /**
     * @param array $aParams 全部参数
     * @return int
     */
    private function _getHitNewExpect($aParams) {
        $oApollo = Apollo::getInstance()->featureToggle(
            'bubble_product_expect_using_new_method',
            [
                'key'           => $aParams['pid'],
                'pid'           => $aParams['pid'],
                'phone'         => $aParams['phone'],
                'from_city'     => $aParams['from_city'],
                'city'          => $aParams['city'],
                'lang'          => $aParams['lang'],
                'access_key_id' => $aParams['access_key_id'],
                'app_version'   => $aParams['app_version'],
            ]
        );

        if ($oApollo->allow()) {
            return 1;
        }

        return 0;
    }

    /**
     * @param array $aParams 全部参数
     * @return array 时间和距离偏移量
     */
    private function _getTimeAndLocationDiff($aParams) {
        $oApollo = Apollo::getInstance()->featureToggle(
            'form_esti_req_count_toggle',
            [
                'key'           => $aParams['pid'],
                'pid'           => $aParams['pid'],
                'from_city'     => $aParams['from_city'],
                'city'          => $aParams['city'],
                'lang'          => $aParams['lang'],
                'access_key_id' => $aParams['access_key_id'],
                'app_version'   => $aParams['app_version'],
            ]
        );
        if ($oApollo->allow()) {
            return [
                'estimate_cache_time'    => $oApollo->getParameter('estimate_cache_time',0),
                'estimate_location_diff' => $oApollo->getParameter('estimate_location_diff',0),
            ];
        }

        return [
            'estimate_cache_time'    => 0,
            'estimate_location_diff' => 0,
        ];

    }
}
