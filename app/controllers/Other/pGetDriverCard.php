<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @date 2022/4/6
 * @time 4:47 PM
 * @desc "客企扫码上车获取司机卡片信息和弹窗信息"
 * @wiki http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=831444007
 */

use BizLib\Utils\Request;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Constants\Common as ConsCommon;
use BizLib\ErrCode\Code;
use BizLib\Utils\Language;
use BizLib\Exception\InvalidArgumentException;
use BizLib\ExceptionHandler;
use BizLib\Config as NuwaConfig;

/**
 * Class PGetDriverCardController
 * @property PGetDriverCardController $PGetDriverCardController
 */
class PGetDriverCardController extends \PreSale\Core\Controller
{
    /**
     * @Desc: mixed[] Array structure to count the elements of.
     * @property init $init
     * @return void
     * @Author:<EMAIL>
     */
    public function init() {
        parent::init();
    }

    /**
     * @return void
     */
    public function indexAction() {
        try {
            $aRet    = UtilsCommon::getErrMsg(GLOBAL_SUCCESS);
            $aParams = $this->_getInputParams();

            $oInterCity   = new \PreSale\Logics\carpool\InterCity();
            $aRet['data'] = $oInterCity->getDriverCard($aParams);
        } catch (\Exception $e) {
            $aErrMsg = NuwaConfig::text('errno', 'pGetBizConfig_error_msg');
            $aRet    = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $aErrMsg, ]);
        }

        $aRet['errno'] = (int)($aRet['errno']);
        $this->sendTextJson($aRet);
    }

    /**
     * @Desc: mixed[] Array structure to count the elements of.
     * @return array
     * @throws InvalidArgumentException <comment for threw> .
     * @property _getInputParams $_getInputParams
     * @Author:<EMAIL>
     */
    private function _getInputParams() {
        $oRequest   = Request::getInstance();
        $iCompanyId = $oRequest->getInt('company_id');
        $sAgentType = $oRequest->getStr('agent_type');
        $sAppId     = $oRequest->getStr('app_id');
        $sTripCloudIntercityScanInfo = $oRequest->getStr('tripcloud_intercity_scaninfo');
        $iArea         = $oRequest->getInt('city_id');
        $sToken        = $oRequest->getStr('token');
        $sLang         = $oRequest->getStr('lang') ?? Language::getLocalLanguage();
        $sMenuId       = $oRequest->getStr('menu_id');
        $sAppVersion   = $oRequest->getStr('app_version');
        $iAccessKeyId  = $oRequest->getInt('access_key_id');
        $sChannel      = $oRequest->getStr('channel');
        $iIsWyc        = $oRequest->getInt('is_wyc');
        $aAllowMenuIds = [ConsCommon::MENU_PINCHECHE, ConsCommon::MENU_INTERCITY_CARPOOL];
        if (empty($iArea) || !in_array($sMenuId, $aAllowMenuIds)) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                array(
                    'city_id' => $iArea,
                    'menu_id' => $sMenuId,
                )
            );
        }

        if ('zh-CN' != $sLang) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                array(
                    'lang'  => $sLang,
                    'token' => $sToken,
                )
            );
        }

        if (\BizLib\Constants\Common::AGENT_TYPE_TC_SCAN != $sAgentType) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                array(
                    'agent_type' => $sAgentType,
                    'token'      => $sToken,
                )
            );
        }

        $aParams = [
            'area'                         => $iArea,
            'app_version'                  => $sAppVersion,
            'access_key_id'                => $iAccessKeyId,
            'menu_id'                      => $sMenuId,
            'channel'                      => $sChannel,
            'company_id'                   => $iCompanyId,
            'agent_type'                   => $sAgentType,
            'app_id'                       => $sAppId,
            'tripcloud_intercity_scaninfo' => $sTripCloudIntercityScanInfo,
            'is_wyc'                       => $iIsWyc ?? 0,
        ];

        return $aParams;
    }
}
