<?php
use BizLib\Utils\Common;
use BizLib\ErrCode\RespCode;
use BizLib\Utils\Request;
use BizLib\Config as NuwaConfig;
use BizLib\ErrCode\Code;
use BizLib\ExceptionHandler;
use PreSale\Logics\passenger\SpecialFeeLogic;
use BizLib\Exception\ExceptionWithResp;
use BizLib\Exception\InvalidArgumentException;
use BizCommon\Models\Passenger\Passenger;

/**
 * PGetSpecialRuleController 控制接口类
 *
 * <AUTHOR> <<EMAIL>>
 */

class PGetSpecialRuleController extends \PreSale\Core\Controller
{
    /**
     * init
     *
     * @return void
     */
    public function init() {
        parent::init();
    }

    private $_sErrMsg = 'FAIL';
    /**
     * Fetches info for employee
     *
     * @return void
     */
    public function indexAction() {
        try {
            //获取默认返回值
            $aResponse         = Common::getErrMsg(GLOBAL_SUCCESS);
            $aResponse['data'] = [];
            $aParams           = $this->_getInputParams();
            $oSpecialFeeLogic  = new SpecialFeeLogic($aParams);
            $aData = $oSpecialFeeLogic->getSpecialRuleText();
        } catch (\Exception $e) {
            $aResponse = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg]);
        }

        if (!empty($aData)) {
            $aResponse['data']  = $aData;
            $aResponse['errno'] = (int)($aResponse['errno']);
        } else {
            $aResponse['errno']  = RespCode::R_COMMON_SERVER_ERROR;
            $aResponse['errmsg'] = $this->_sErrMsg;
        }

        $this->sendTextJson($aResponse);

        return;
    }
    /**
     * getParams
     *
     * @return array
     * @throws ExceptionWithResp  参数错误异常
     */
    private function _getInputParams() {
        $oRequest = Request::getInstance();
        $aParams['estimate_id']   = $oRequest->fetchGetPost('estimate_id', false);
        $aParams['rule_type']     = json_decode($oRequest->fetchGetPost('rule_type', false), true);
        $aParams['lang']          = $oRequest->fetchGetPost('lang', false);
        $aParams['app_version']   = $oRequest->fetchGetPost('appversion', false);
        $aParams['token']         = $oRequest->fetchGetPost('token', '');
        $aParams['access_key_id'] = $oRequest->fetchGetPost('access_key_id', false);
        $aParams['area']          = $oRequest->fetchGetPost('city_id', false) ?? 0;
        if (empty($aParams['estimate_id']) || !is_array($aParams['rule_type']) || empty($aParams['lang'])) {
            throw new ExceptionWithResp(
                RespCode::P_PARAMS_ERROR,
                (string) RespCode::P_PARAMS_ERROR,
                'errmsg:param error! estimate_id:'.$aParams['estimate_id'].
                '||rule_type:'.$aParams['rule_type'].'lang:'.$aParams['lang']
            );
        }

        $aParams['passenger'] = (new Passenger())->getPassengerByToken($aParams['token']);
        $aParams['phone']     = $aParams['passenger']['phone'] ?? '';

        return $aParams;
    }
}
