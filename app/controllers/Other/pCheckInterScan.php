<?php

use BizLib\Exception\ExceptionWithResp;
use BizLib\ErrCode\RespCode;
use BizLib\Error\ExceptionHandler;
use BizCommon\Models\Rpc\PassportRpc;
use BizCommon\Infrastructure\Repository\Driver\UranusRepository;
use BizCommon\Models\Rpc\UranusSystem;
use BizCommon\Models\Passenger\Passenger;
use BizLib\Utils\Language;
use PreSale\Logics\order\InterCityCarpoolOrderLogic;
use BizCommon\Utils\Time;
use BizLib\Utils\UtilHelper;
use Nuwa\ApolloSDK\Apollo as NuwaApollo;

/**
 * Class PCheckInterScanController
 */
class PCheckInterScanController extends \PreSale\Core\Controller
{
    private $_sDriverTicket = '';

    private $_sPassengerToken = '';

    /**
     * 初始化
     * @return void
     */
    public function init() {
        parent::init();
    }

    /**
     * action
     * @return void
     */
    public function indexAction() {
        try {
            $this->_checkParams();
            $aInfos = $this->_loadInfos();
            $aNeedReassignOrders = InterCityCarpoolOrderLogic::getNeedReassignOrders($aInfos['passenger_id']);
            $aResponse           = $this->_formatResponse($aInfos, $aNeedReassignOrders);
        } catch (\Exception $e) {
            $aResponse = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg,]);
        }

        $this->sendJson($aResponse);
    }

    /**
     * 参数校验
     * @return void
     * @throws ExceptionWithResp ExceptionWithResp.
     */
    private function _checkParams() {
        $sPassengerToken        = $this->oRequest->getStr('token');
        $sDriverToken           = $this->oRequest->getStr('inter_scan_token');
        $this->_sDriverTicket   = \BizCommon\Logics\Carpool\InterCarpoolQrCode::getTicketByScanToken($sPassengerToken, $sDriverToken);
        $this->_sPassengerToken = $sPassengerToken;
    }


    /**
     * 获取司机、乘客、车辆信息
     * @return array
     * @throws ExceptionWithResp ExceptionWithResp.
     */
    private function _loadInfos() {
        $iPassengerId = Passenger::getInstance()->getPidByToken($this->_sPassengerToken);
        if (empty($iPassengerId) || empty($this->_sDriverTicket)) {
            throw new ExceptionWithResp(
                RespCode::P_CHECK_QR_CODE_ERROR,
                (string) RespCode::P_CHECK_QR_CODE_ERROR,
                '',
                'errmsg:param error! driver_token:' . $this->_sDriverTicket . 'passenger_token:' . $this->_sPassengerToken
            );
        }

        $aPassportInfo = (new PassportRpc())->getPassportInfoFromPassPort($this->_sDriverTicket);
        $iDriverId     = $aPassportInfo['uid'] ?? false;
        $aDriverInfo   = (new UranusRepository())->getDriverAllInfo($iDriverId);
        $aCarInfo      = UranusSystem::getCarInfoByGvid($aDriverInfo['aDriverBiz']['online_gvid']);
        if (empty($iDriverId) || empty($aDriverInfo) || empty($aCarInfo)) {
            throw new ExceptionWithResp(
                RespCode::P_CHECK_QR_CODE_ERROR,
                (string) RespCode::P_CHECK_QR_CODE_ERROR,
                '',
                'errmsg:param error! driver_token:' . $this->_sDriverTicket . 'passenger_token:' . $this->_sPassengerToken
            );
        }

        return [
            'passenger_id' => $iPassengerId,
            'driver_name'  => $aDriverInfo['aDriverFeature']['driverName'],
            'car_type'     => $aCarInfo['brand_desc'],
            'car_plate'    => $aCarInfo['plate_no'],
        ];
    }


    /**
     * 格式化
     * @param array $aInfos              aInfo
     * @param array $aNeedReassignOrders aNeedReassignOrders
     * @return array
     */
    private function _formatResponse($aInfos, $aNeedReassignOrders) {
        $aResponse['errno']  = \BizLib\ErrCode\Code::E_SUCCESS;
        $aResponse['errmsg'] = '';

        $aText = json_decode(Language::getTextFromDcmp('config_inter_carpool-scan_code', ['driver_name' => $aInfos['driver_name']]), true);
        if (!empty($aNeedReassignOrders)) {
            $aResponse['data']['need_reassign_card'] = $this->_formatReassignCard($aInfos, $aNeedReassignOrders, $aText);
            $aResponse['data']['buttons']            = $aText['reassign']['buttons'] ?? [];
            $aResponse['data']['new_order_card']     = null;
        } else {
            $aResponse['data']['new_order_card']['title']       = $aText['new_order']['title'];
            $aResponse['data']['new_order_card']['driver_name'] = $aInfos['driver_name'];
            $aResponse['data']['new_order_card']['car_type']    = $aInfos['car_type'];
            $aResponse['data']['new_order_card']['plate_title'] = substr($aInfos['car_plate'],0,4);
            $aResponse['data']['new_order_card']['plate_no']    = substr($aInfos['car_plate'],4);
            $aResponse['data']['new_order_card']['bottom_text'] = $aText['new_order']['bottom_text'];
            $aResponse['data']['buttons']            = $aText['new_order']['buttons'] ?? [];
            $aResponse['data']['need_reassign_card'] = null;
        }

        return $aResponse;
    }

    /**
     * @param array $aInfos             aInfos
     * @param array $aNeedReassignOrder aNeedReassignOrder
     * @param array $aText              aText
     * @return mixed
     */
    private function _formatReassignCard($aInfos, $aNeedReassignOrder, $aText) {
        $aNeedReassignCard['driver_name'] = $aInfos['driver_name'];
        $aNeedReassignCard['car_type']    = $aInfos['car_type'];
        $aNeedReassignCard['plate_title'] = substr($aInfos['car_plate'],0,4);
        $aNeedReassignCard['plate_no']    = substr($aInfos['car_plate'],4);
        $aNeedReassignCard['bottom_text'] = $aText['reassign']['bottom_text'];

        $iDepartureTime = strtotime($aNeedReassignOrder['departure_time']);
        $sTime          = date('H:i', $iDepartureTime);
        $sDay           = Time::getDiffDaysTag($iDepartureTime);
        $aNeedReassignCard['departure_time_text'] = $sDay . ' ' . $sTime;
        $aNeedReassignCard['starting_name']       = $this->_getShortAddressName($aNeedReassignOrder['starting_name']);
        $aNeedReassignCard['dest_name']           = $this->_getShortAddressName($aNeedReassignOrder['dest_name']);
        $aNeedReassignCard['dlng']  = $aNeedReassignOrder['dest_lng'];
        $aNeedReassignCard['dlat']  = $aNeedReassignOrder['dest_lat'];
        $aNeedReassignCard['slng']  = $aNeedReassignOrder['starting_lng'];
        $aNeedReassignCard['slat']  = $aNeedReassignOrder['starting_lat'];
        $aNeedReassignCard['title'] = Language::replaceTag($aText['reassign']['title'], ['departure_time_text' => $sDay . $sTime]);
        $aNeedReassignCard['oid']   = UtilHelper::encodeId($aNeedReassignOrder['order_id'], $aNeedReassignOrder['district']);

        return $aNeedReassignCard;
    }

    /**
     * @param string $sAddress sAddress
     * @return mixed
     */
    private function _getShortAddressName($sAddress) {
        $aAddressInfo = explode('|', $sAddress);
        return $aAddressInfo[1] ?? $aAddressInfo[0];
    }
}
