<?php

/*
 * 获取出租车信息费配置
 * wiki: http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=188491275
 * <AUTHOR>
 * @date 2019/02/01
 */

use BizLib\Utils\Common as UtilsCommon;
use BizLib\ExceptionHandler;
use BizLib\ErrCode\RespCode;
use PreSale\Logics\priceRule\GetUnioneInfoFeeReq;
use BizLib\Client\SpsClient;

/**
 * Class pGetUnioneInfoFeeController.
 */
class PGetUnioneInfoFeeController extends \PreSale\Core\Controller
{

    public function indexAction() {
        //初始化Request对象
        $oRequest = new GetUnioneInfoFeeReq($_GET);

        try {
            //获取默认返回值
            $aResponseInfo = UtilsCommon::getErrMsg(RespCode::P_SUCCESS, $this->aErrnoMsg);

            //验证参数
            $iArea = $oRequest->verify();
            if (empty($iArea)) {
                throw new \Exception('坐标信息为空', P_ERRNO_PARAMS_ERROR);
            }

            //从请求sps获取信息费
            $oSps    = new SpsClient();
            $aResult = $oSps->getInfoFeePriceRule(['city' => $iArea]);
            if (!isset($aResult['errno'])
                || GLOBAL_SUCCESS != $aResult['errno']
                || empty($aResult['data'])
            ) {
                throw new \Exception('请求sps失败', P_ERRNO_PARAMS_ERROR);
            }

            $aResponseInfo['data'] = $aResult['data'];
        } catch (\Exception $e) {
            //异常处理上下文
            $aContext = [
                'dltag'   => '_com_request_out_failure',
                'err_msg' => $this->aErrnoMsg,
            ];

            //异常处理并得到返回值
            $aResponseInfo = ExceptionHandler::getInstance()->handleException($e, $aContext);
        }

        //输出返回值
        $this->sendTextJson($aResponseInfo);
    }
}
