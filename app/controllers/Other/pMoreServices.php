<?php

/**
 *
 * <AUTHOR>
 * @date 2018/9/6
 */
use BizLib\Utils\Request;
use PreSale\Core\BaseController;
use BizLib\Constants\Common;

/**
 * class pMoreServices.
 */
class pMoreServicesController extends BaseController
{

    private $_aSort = ['callfor', 'feedback', 'wait'];
    /**
     * 抽象方法实现.
     */
    public function checkParams() {
        //参数处理
        $oRequest = Request::getInstance();
        $aParams  = array();
        $aParams['client_type'] = $oRequest->getStr('client_type');
        return $aParams;
    }

    /**
     * 抽象方法实现.
     */
    public function execute(array $aParams) {
        $aList = array(
            array(
                'img'    => 'https://dpubstatic.udache.com/static/dpubimg/BkSHq6nwm.png',
                'id'     => 'callfor',
                'desc'   => '代叫',
                'enable' => true,
            ),
            array(
                'img'    => 'https://dpubstatic.udache.com/static/dpubimg/rkz2AGfFQ.png',
                'id'     => 'wait',
                'desc'   => '即将开放',
                'enable' => false,
            ),
            array(
                'img'    => 'https://dpubstatic.udache.com/static/dpubimg/r1bh0zMKX.png',
                'id'     => 'wait',
                'desc'   => '即将开放',
                'enable' => false,
            ),
        );

        if (Common::CLIENT_TYPE_WEBAPP == $aParams['client_type']) {
            $aAddList = array(
                'img'    => 'https://dpubstatic.udache.com/static/dpubimg/882574da-9bf3-4d37-a846-df1613c0e034.png',
                'link'   => '//tiyan.xiaojukeji.com/crm/feedback/feedbackh5/v2',
                'id'     => 'feedback',
                'desc'   => '意见反馈',
                'enable' => true,
            );
            $aList[]  = $aAddList;
        }

        $aNewList = [];
        foreach ($this->_aSort as $aSortKey) {
            foreach ($aList as $aListItem) {
                if ($aListItem['id'] == $aSortKey) {
                    $aNewList[] = $aListItem;
                }
            }
        }

        $data['list'] = $aNewList;
        return (object) $data;
    }
}
