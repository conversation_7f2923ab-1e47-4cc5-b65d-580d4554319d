<?php
/**
 * Created by PhpStorm.
 * Date: 19/7/29
 * Time: 19:18
 * @category Category
 *
 * @package FileDirFileName
 *
 * <AUTHOR> <<EMAIL>>
 * @link ${link}
 *
 * @link ${link}
 */
use BizLib\Utils\Request;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Constants\Common as ConsCommon;
use BizLib\ErrCode\Code;
use BizLib\Utils\Language;
use BizLib\Exception\InvalidArgumentException;
use BizLib\ExceptionHandler;
use Nuwa\ApolloSDK\Apollo;
use BizLib\Log as NuwaLog;
use BizLib\Libraries\RedisDB;
use BizCommon\Models\Passenger\Passenger;
use BizLib\Utils\NumberHelper;
use PreSale\Logics\carpool\Coupon;

/**
 * Class PGetBannerInfoController
 * @property PGetBannerInfoController $PGetBannerInfoController
 */
class PGetBannerInfoController extends \PreSale\Core\Controller
{
    /**
     * @Desc: mixed[] Array structure to count the elements of.
     * @property init $init
     * @return void
     * @Author:<EMAIL>
     */
    public function init() {
        parent::init();
    }

    /**
     * @return void
     */
    public function indexAction() {
        try {
            $aRet    = UtilsCommon::getErrMsg(GLOBAL_SUCCESS);
            $aParams = $this->_getInputParams();

            if (ConsCommon::MENU_PINCHECHE == $aParams['menu_id']) {
                $oLowPriceCarpool = new \PreSale\Logics\carpool\LowPriceCarpool();
                $aRet['data']     = $oLowPriceCarpool->getBannerInfo($aParams);
            } elseif (ConsCommon::MENU_INTERCITY_CARPOOL == $aParams['menu_id']) {
                $oInterCity   = new \PreSale\Logics\carpool\InterCity();
                $aRet['data'] = $oInterCity->getBannerConf($aParams);
            }
        } catch (\Exception $e) {
            $aErrMsg = NuwaConfig::text('errno', 'pGetBizConfig_error_msg');
            $aRet    = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $aErrMsg, ]);
        }

        $aRet['errno'] = (int)($aRet['errno']);
        $this->sendTextJson($aRet);
    }

    /**
     * @Desc: mixed[] Array structure to count the elements of.
     * @return array
     * @throws InvalidArgumentException <comment for threw> .
     * @property _getInputParams $_getInputParams
     * @Author:<EMAIL>
     */
    private function _getInputParams() {
        $oRequest        = Request::getInstance();
        $sLang           = $oRequest->getStr('lang') ?? Language::getLocalLanguage();
        $sMenuId         = $oRequest->getStr('menu_id');
        $iArea           = $oRequest->getInt('city_id');
        $sToken          = $oRequest->getStr('token');
        $sAppVersion     = $oRequest->getStr('appversion');
        $iAccessKeyId    = $oRequest->getInt('access_key_id');
        $sEstimateId     = $oRequest->getStr('estimate_id');
        $iInvitationType = $oRequest->getInt('invitation_type');
        $iMatchCode      = $oRequest->getInt('match_code');
        $fLat            = $oRequest->getFloat('from_lat');
        $fLng            = $oRequest->getFloat('from_lng');
        $sPhone          = $oRequest->getStr('phone');
        $aAllowMenuIds   = [ConsCommon::MENU_PINCHECHE, ConsCommon::MENU_INTERCITY_CARPOOL];
        if (empty($iArea) || !in_array($sMenuId, $aAllowMenuIds)) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                array(
                    'city_id' => $iArea,
                    'menu_id' => $sMenuId,
                )
            );
        }

        if ('zh-CN' != $sLang) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                array(
                    'lang'  => $sLang,
                    'token' => $sToken,
                )
            );
        }

        $aParams = [
            'area'            => $iArea,
            'token'           => $sToken,
            'lang'            => $sLang,
            'app_version'     => $sAppVersion,
            'access_key_id'   => $iAccessKeyId,
            'estimate_id'     => $sEstimateId,
            'menu_id'         => $sMenuId,
            'invitation_type' => $iInvitationType,
            'match_code'      => $iMatchCode,
            'dviceid'         => $oRequest->getStr('dviceid'),
            'imei'            => $oRequest->getStr('imei'),
            'channel'         => $oRequest->getStr('channel'),
            'from_lat'        => $fLat,
            'from_lng'        => $fLng,
            'phone'           => $sPhone,
        ];

        return $aParams;
    }
}
