<?php

use BizCommon\Logics\Order\OrderStatusComLogic;
use BizCommon\Logics\Order\TimeoutCloseLineUpPriority;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\ProductCategory;
use BizLib\Utils\UtilHelper;
/*
 * 订单播单超时后，端上请求该接口获取导流相关信息
 *
 * <AUTHOR>
 * @date: 17/06/20
 */
use BizCommon\Models\Rpc\OrderSystemRpc;
use Dirpc\SDK\Dcmp\CopyModelUtils;
use PreSale\Logics\athena\PreCancelLogic;
use BizCommon\Models\Passenger\Passenger;
use BizCommon\Utils\Common;
use BizLib\Utils\Language;
use BizLib\Utils\DateHelper;
use BizLib\Client\PriceApiClient;
use BizLib\ErrCode\Code;
use BizLib\Log as NuwaLog;
use BizLib\ErrCode\Msg;
use BizCommon\Constants\OrderNTuple;
use BizCommon\Logics\Carpool\CarpoolBrand;
use BizCommon\Logics\CommuteCard\CarpoolGoRight\WaitingStage;
use BizLib\Constants;
use BizLib\Utils\Product;
use BizCommon\Utils\Horae;
use Dukang\PropertyConst\Product\ProductFromType;
use PreSale\Infrastructure\Repository\Ufs\UfsRepository;
use BizCommon\Models\Rpc\UranusSystem;
use xiaoju\Apollo\Apollo;

class POrderTimeoutController extends \PreSale\Core\Controller
{
    const DISCARDED_ORDER_ID = 'TkRJNU5EazJOekk1TmpBPQ=='; //已丢弃的订单id

    const UFS_SMART_BUS_SCAN_ORDER_DRVER_ID = 'smart_bus_scan_order.driver_id'; // 公交扫码发单司机id

    private static $_aPopupClassFactory = [
        '\PreSale\Logics\orderTimeout\BookingCompensationPopup',
    ];

    // 等待应答6.0标识
    private $_bV6dot0 = false;

    public function indexAction() {
        try {
            //$this->load->model('passenger/Passenger');
            $sToken      = $this->getRequest()->getQuery('token', false);
            $sEncodeOid  = $this->getRequest()->getQuery('order_id', false);
            $sLang       = $this->getRequest()->getQuery('lang', false);
            $sAppVersion = $this->getRequest()->getQuery('appversion', false);
            // 等待应答6.0标识
            $this->_bV6dot0 = $this->getRequest()->getQuery('v6_version', false);

            $iAccessKeyId = $this->getRequest()->getQuery('access_key_id', 0);

            $aReq = [
                'lang'           => $sLang,
                'app_version'    => $sAppVersion,
                'access_key_id'  => $iAccessKeyId,
            ];

            //静默单处理
            if (self::DISCARDED_ORDER_ID == $sEncodeOid && Common::isFromKFlower($iAccessKeyId)) {
                $aRet = $this->_getDiscardOrderResponse();
                $this->sendJson($aRet);

                return;
            }

            $aOrderDecodeResult = UtilHelper::decodeId($sEncodeOid);
            $iOrderId           = $aOrderDecodeResult['oid'];
            $sDistrict          = $aOrderDecodeResult['district'];
            if (empty($iOrderId) || empty($sDistrict)) {
                throw new \Exception('订单id错误', P_ERRNO_PARAMS_ERROR);
            }

            $iPid = Passenger::getInstance()->getPidByToken($sToken);
            if ($iPid <= 0) {
                throw new \Exception('token无效', P_ERRNO_TOKEN_ERROR);
            }

            $oOrderSystemRpc = new OrderSystemRpc();
            $aOrderInfo      = $oOrderSystemRpc->getOrderInfo($sDistrict, $iOrderId);

            if (empty($aOrderInfo)) {
                throw new \Exception('订单不存在', P_ERRNO_PARAMS_ERROR);
            }

            if ($iPid != $aOrderInfo['passenger_id']) {
                throw new \Exception('验证失败', P_ERRNO_PARAMS_ERROR);
            }

            $oPreCancelLogic = new PreCancelLogic();
            $aRetentionInfo  = $oPreCancelLogic->getRetentionInfo($aOrderInfo, PreCancelLogic::STAGE_ORDER_TIMEOUT, $sLang, $iAccessKeyId);
            $aRet            = UtilsCommon::getErrMsg(GLOBAL_SUCCESS, $this->aErrnoMsg);
            $aCancelCard     = $this->_getCancelCard($aOrderInfo, $aRetentionInfo, $sAppVersion, $iAccessKeyId);
            if (empty($aCancelCard)) {
                throw new \Exception('未配置超时卡片', P_ERRNO_PARAMS_ERROR);
            }

            // 旧弹窗协议
            $aRet['data']['cancel_info']   = $aCancelCard;
            $aRet['data']['guide_list']    = $this->_getGuideListCard($aRetentionInfo);
            $aRet['data']['address_info']  = $this->_getAddressInfo($aOrderInfo);

            // 新弹窗协议
            $aRet['data']['popup']         = $this->_getPopup($aReq, $aOrderInfo);
            $this->sendJson($aRet);
        } catch (\Exception $e) {
            $aRet = UtilsCommon::getErrMsg($e->getCode(), $this->aErrnoMsg);
            $this->sendJson($aRet);
        }
    }

    /**
     * mock返回
     *
     * @return array|bool
     */
    private function _getDiscardOrderResponse() {
        $aOrderTimeoutCard = NuwaConfig::text('config_passenger', 'order_timeout_card');
        $aTimeOutInfo      = $aOrderTimeoutCard['card_setting'][5]['card_no_guide'] ?? [];

        $aRet = UtilsCommon::getErrMsg(GLOBAL_SUCCESS, $this->aErrnoMsg);
        $aRet['data']['cancel_info'] = $aTimeOutInfo;

        return $aRet;
    }

    /**
     * @param array  $aOrderInfo     order_info
     * @param array  $aRetentionInfo retention_info
     * @param string $sAppVersion    app_version
     * @param int    $iAccessKeyId   access_key_id
     * @return array|mixed
     */
    private function _getCancelCard($aOrderInfo, $aRetentionInfo, $sAppVersion, $iAccessKeyId) {
        // 境外单
        if ( 0 != $aOrderInfo['is_oversea']) {
            $aCancelCard = NuwaConfig::text('config_passenger', 'oversea_order_timeout_card');
            return $aCancelCard;
        }

        // 6.0等待应答超时卡片
        if ($this->_bV6dot0) {
            // 预约单
            if ($this->_isBookingOrder($aOrderInfo)) {
                $aCard = $this->_getBookingCancelCard($aOrderInfo);
                if (!empty($aCard)) {
                    return $aCard;
                }
            }

            // 市内小巴only
            if (\BizCommon\Utils\Horae::isOnlyMiniBusCarpoolOrder($aOrderInfo)) {
                return Language::getDecodedTextFromDcmp('config_carpool-only_mini_bus_timeout');
            }

            if (\BizCommon\Utils\Horae::isOnlySmartMiniBusCarpoolOrder($aOrderInfo)) {
                return $this->_getOnlySmartBusCancelCard($aOrderInfo, $iAccessKeyId, $sAppVersion);
            }

            $aOrderTimeoutCard = NuwaConfig::text('config_passenger', 'order_timeout_card_v6dot0');
        } else {
            $aOrderTimeoutCard = NuwaConfig::text('config_passenger', 'order_timeout_card');
        }

        // 如果是城际大巴
        if (Horae::isStationToStationInterCarpool($aOrderInfo) && !empty($aOrderTimeoutCard['carpool_station'])) {
            return $aOrderTimeoutCard['carpool_station'];
        }

        // 如果是无忧卡派车但未派到
        if (\BizCommon\Utils\Horae::isInterCityPoolFullGoBooking($aOrderInfo) && PreCancelLogic::isInterCityGoCardWork($aOrderInfo['order_id'])) {
            $aText = Language::getDecodedTextFromDcmp('config_carpool-inter_city_carpool_timeout_go_card');
            if (!empty($aText)) {
                return $aText;
            }
        }

        $iFromGuideType = PreCancelLogic::transGuideType($aOrderInfo['product_id'], $aOrderInfo['require_level'], $aRetentionInfo['combo_type']);
        if (\BizCommon\Utils\Horae::isLowPriceCarpool($aOrderInfo)) {
            return $this->_getLowPriceCarpoolCard($aOrderInfo, $sAppVersion);
        }

        // 优先从文案平台获取（DCMP）并填充到$aOrderTimeoutCard中
        if (\BizCommon\Utils\Order::isSpecialRateV2($aOrderInfo)) {
            $iFromGuideType = 4;
            $sTimeoutTips   = Language::getTextFromDcmp('special_rate-timeout_tips_for_ordertimeout_api');
            $aTimeoutTips   = [];
            if ($sTimeoutTips) {
                $aTimeoutTips = json_decode($sTimeoutTips, true);
            }

            $aOrderTimeoutCard['card_setting'][PreCancelLogic::GUIDE_TYPE_SPECIAL] = $aTimeoutTips;
        }

        $aSetting = empty($aOrderTimeoutCard['card_setting'][$iFromGuideType]) ? $aOrderTimeoutCard['card_setting'][0] : $aOrderTimeoutCard['card_setting'][$iFromGuideType];

        // 超值出租车，aRetentionInfo为空
        if (in_array($aOrderInfo['product_id'],[\BizLib\Constants\OrderSystem::PRODUCT_ID_UNITAXI, \BizLib\Constants\OrderSystem::PRODUCT_ID_BUSINESS_TAXI_CAR])
            && \BizLib\Utils\CarLevel::DIDI_UNITAXI_PUTONG_CAR_LEVEL == $aOrderInfo['require_level']
            && \BizLib\Constants\Horae::TYPE_COMBO_DEFAULT == $aOrderInfo['combo_type']
            && 1 == $aOrderInfo['is_special_price']
        ) {
            $sConfirmTextTips = Language::getTextFromDcmp('special_price_union-timeout_tips_for_ordertimeout_api');
            $aSetting         = json_decode($sConfirmTextTips, true);
        }

        // 物品遗失订单超时弹窗
        if (\BizCommon\Utils\Horae::isLossRemandOrder($aOrderInfo)) {
            // 6.0版本使用新版结构
            if ($this->_bV6dot0) {
                $sLossRemandDcmpKey = 'config_passenger-loss_remand_order_timeout_card_new';
            } else {
                $sLossRemandDcmpKey = 'config_passenger-loss_remand_order_timeout_card';
            }

            $sConfirmTextTips = Language::getTextFromDcmp($sLossRemandDcmpKey);
            $aSetting         = json_decode($sConfirmTextTips, true);
        }

        if (isset($aOrderInfo['scan_code_type']) && OrderNTuple::SCAN_CODE_TYPE == $aOrderInfo['scan_code_type']) {
            $sConfirmTextTips = Language::getTextFromDcmp('config_passenger-scan_code_timeout_card');
            $aSetting         = json_decode($sConfirmTextTips, true);
        }

        //一键叫如果有播单超时卡片，则使用该种卡片
        if (\BizCommon\Utils\Horae::isOneKeyCallOrder($aOrderInfo)) {
            $sConfirmTextTips = Language::getTextFromDcmp('config_passenger-one_key_call_timeout_card_'.$aOrderInfo['combo_id']);
            if (!empty($sConfirmTextTips)) {
                $aSetting = json_decode($sConfirmTextTips, true);
            }
        }

        //实时单排队订单播单超时 返回下一笔订单优先派的文案
        list($bLineUpPriority, $iTimeLimit) = $this->_getLineUpTimeoutPriorityInfo($aOrderInfo);
        if ($bLineUpPriority) {
            $sConfirmTextTips = Language::getTextFromDcmp('timeout_card-line_up_order_timeout_card');
            if (!empty($sConfirmTextTips)) {
                $aSetting = json_decode($sConfirmTextTips, true);
                foreach ($aSetting as $i => $aText) {
                    $sDepartureTime            = date('H:i', $iTimeLimit);
                    $aSetting[$i]['sub_title'] = CopyModelUtils::translateTemplate($aText['sub_title'], ['d_time' => $sDepartureTime]);
                }
            }
        }

        //短时预约超时卡片
        if ($aOrderInfo['is_short_book']) {
            return $this->_getShortBookCard($aOrderInfo, $aSetting);
        }

        if (empty($aRetentionInfo)) {
            return $aSetting['card_no_guide'] ?? [];
        } else {
            return $aSetting['card_guide'] ?? [];
        }
    }

    /**
     * 获取小巴公交超时弹窗
     * @param array  $aOrderInfo   orderInfo
     * @param int    $iAccessKeyId accessKeyId
     * @param string $sAppVersion  appVersion
     * @return array
     */
    private function _getOnlySmartBusCancelCard($aOrderInfo, $iAccessKeyId, $sAppVersion) {
        if (ProductFromType::FromTypeFromTypeScanInCar == $aOrderInfo['from_type']) {   // 小巴公交扫码发单 
            $aCancelCard = $this->_getOnlySmartBusScanOrderCancelCard($aOrderInfo, $iAccessKeyId, $sAppVersion);
            if (!empty($aCancelCard)) {
                return $aCancelCard;
            }
        }

        return NuwaConfig::text('config_passenger', 'order_timeout_card_v6dot0');;
    }

    /**
     * @param array  $aOrderInfo   $aOrderInfo
     * @param int    $iAccessKeyId $iAccessKeyId
     * @param string $sAppVersion  $sAppVersion
     * @return array
     */
    private function _getOnlySmartBusScanOrderCancelCard($aOrderInfo, $iAccessKeyId, $sAppVersion) {
        // 版本控制
       if (!Apollo::getInstance()->featureToggle(
            'gs_smart_carpool_scan_order_fix', 
            [
                'key'           => $aOrderInfo['passenger_id'],
                'access_key_id' => $iAccessKeyId,
                'app_version'   => $sAppVersion,
                'passenger_id'  => $aOrderInfo['passenger_id'],
                'product_id'    => $aOrderInfo['product_id'],
                'city'          => $aOrderInfo['area'],
            ]
        )->allow()) {
            return array();
        }

        // 扫码上车场景，乘客在车上，理论上只有一个品类
        $aExtendFeature = json_decode($aOrderInfo['extend_feature'], true);
        if (empty($aExtendFeature['multi_require_product']) || count($aExtendFeature['multi_require_product']) != 1) {
            return array();
        }

        $aSmartBusFeature = current($aExtendFeature['multi_require_product']);

        // 获取driver_id和车牌号
        $aCondition = [
            'order_id' => $aOrderInfo['order_id'],
        ];
        $aUfsFields = [
            self::UFS_SMART_BUS_SCAN_ORDER_DRVER_ID
        ];
        $aUfsRes = UfsRepository::getUfsFeature($aUfsFields, $aCondition, 'order');
        if (empty($aUfsRes) || empty($aUfsRes[self::UFS_SMART_BUS_SCAN_ORDER_DRVER_ID])) { // 返回默认弹窗数据
            return array();
        }
        $sDriverId = $aUfsRes[self::UFS_SMART_BUS_SCAN_ORDER_DRVER_ID];
        // 获取车牌号
        $aDriverInfo = UranusSystem::getDriverBizInfo($sDriverId, ['online_gvid'], UranusSystem::SDK_SMART_BUS_PRODUCT);
        if (empty($aDriverInfo) || empty($aDriverInfo['online_gvid'])) {
            return array();
        }
        $iGvid = $aDriverInfo['online_gvid'];
        $aCarInfo = UranusSystem::getCarInfoByGvid($iGvid,['plate_no'], UranusSystem::SDK_SMART_BUS_PRODUCT);
        if (empty($aCarInfo) || empty($aCarInfo['plate_no'])) {
            return array();
        }

        $sPlateNo = $aCarInfo['plate_no'];

        $aReplaceParams = [
            'plate_no'   => $sPlateNo,
            'product_id' => $aSmartBusFeature['product_id'],
            'city_id'    => $aOrderInfo['area'],
        ];
        $sTextConfig = Language::getTextFromDcmp('config_carpool-only_mini_bus_scan_timeout', $aReplaceParams);
        $aCancelCard = json_decode($sTextConfig, true);
        // 埋点数据
        $aCancelCard['omega_param'] = [
            'key' => 'wyc_zncar_intravel_csgd_sw',
            'params' => [
                'carpool_type' => OrderNTuple::CARPOOL_TYPE_SMART_BUS,
                'button_text'  => $aCancelCard['cancel_button']['text'],
            ]
        ];
        $aCancelCard['cancel_button']['action_omega'] = [
            'key' => 'wyc_zncar_intravel_csgd_ck',
            'params' => [
                'carpool_type' => OrderNTuple::CARPOOL_TYPE_SMART_BUS,
                'button_text'  => $aCancelCard['cancel_button']['text'],
            ]
        ];

        return $aCancelCard;
    }
    
    /**
     * @param array $aOrderInfo $aOrderInfo
     * @return array
     */
    private function _getLineUpTimeoutPriorityInfo($aOrderInfo) {
        if (!TimeoutCloseLineUpPriority::preOrderBasicCheck($aOrderInfo)) {
            return [false, -1];
        }

        if (!$this->_checkTimeoutCloseProductCategory($aOrderInfo)) {
            return [false, -1];
        }

        $iBroadcastExpireTime = OrderStatusComLogic::getInstance()->getOrderExpireTime($aOrderInfo);
        $iNowTime       = time();
        $iTimeLimit     = $iBroadcastExpireTime + TimeoutCloseLineUpPriority::CUR_LAST_CREATE_TIME_DIFF_LIMIT;
        $bTimeLimitPass = $iNowTime < $iTimeLimit;

        return [$bTimeLimitPass, $iTimeLimit];
    }

    /**
     * @param array $aOrderInfo $aOrderInfo
     * @return bool
     */
    private function _checkTimeoutCloseProductCategory($aOrderInfo) {
        $aCurrentProducts = [];
        if (Product::isAnyCar($aOrderInfo['product_id'])) {
            $aExtendFeature       = json_decode($aOrderInfo['extend_feature'], true);
            $aMultiRequireProduct = $aExtendFeature['multi_require_product'] ?? [];
            foreach ($aMultiRequireProduct as $aProduct) {
                $aCurrentProducts[] = $aProduct['product_category'];
            }
        } else {
            $aCurrentProducts[] = (new ProductCategory())->getProductCategoryByNTuple($aOrderInfo);
        }

        return (!empty(array_intersect($aCurrentProducts, TimeoutCloseLineUpPriority::$aSupportProductCategory)));
    }

    private function _getGuideListCard($aRetentionInfo) {
        if (empty($aRetentionInfo)) {
            return [];
        }

        $aOrderTimeoutCard = NuwaConfig::text('config_passenger', 'order_timeout_card');
        $aCardList         = [];
        foreach ($aRetentionInfo as $aRetention) {
            $iProductId   = \BizLib\Utils\Product::getProductIdByBusinessId($aRetention['guide_product']);
            $iGuideType   = PreCancelLogic::transGuideType($iProductId, $aRetention['guide_require_level'], $aRetention['guide_combo_type']);
            $sName        = $aOrderTimeoutCard['guide_car_name'][$iGuideType] ?? '';
            $aCardSetting = empty($aOrderTimeoutCard['car_guide_setting'][$iGuideType]) ? $aOrderTimeoutCard['car_guide_setting'][0] : $aOrderTimeoutCard['car_guide_setting'][$iGuideType];
            if (empty($sName) || empty($aCardSetting) || empty($aRetention['pre_price']) || empty($aRetention['wait_time'])) {
                continue;
            }

            $aParams    = [
                'name'      => $sName,
                'price'     => $aRetention['pre_price'],
                'wait_time' => $aRetention['wait_time'],
            ];
            $aGuideCard = [
                'guide_scene'   => $aRetention['guide_scene'],
                'business_id'   => $aRetention['guide_product'],
                'require_level' => $aRetention['guide_require_level'],
                'combo_type'    => $aRetention['guide_combo_type'],
                'title'         => Language::replaceTag($aCardSetting['title'], $aParams),
                'confirm_info'  => [
                    'title'       => Language::replaceTag($aCardSetting['confirm_title'], $aParams),
                    'sub_title'   => Language::replaceTag($aCardSetting['confirm_sub_title'], $aParams),
                    'button_text' => Language::replaceTag($aCardSetting['confirm_button'], $aParams),
                ],
                'athena_id'     => $aRetention['athena_id'],
                'transparent'   => json_encode(
                    [
                        'estimate_trace_id' => (string) $aRetention['estimate_trace_id'],
                        'guide_scene'       => (int) $aRetention['guide_scene'],
                        'athena_id'         => (string) $aRetention['athena_id'],
                    ]
                ),
            ];
            if (PreCancelLogic::GUIDE_TYPE_CARPOOL == $iGuideType) {
                $aGuideCard['confirm_info']['seat_nums'] = [
                    [
                        'text'     => Language::replaceTag($aOrderTimeoutCard['guide_carpool_seat_text'], ['seat_num' => 1]),
                        'num'      => 1,
                        'selected' => true,
                    ],
                    [
                        'text'     => Language::replaceTag($aOrderTimeoutCard['guide_carpool_seat_text'], ['seat_num' => 2]),
                        'num'      => 2,
                        'selected' => false,
                    ],
                ];
            }

            $aCardList[] = $aGuideCard;
        }

        return $aCardList;
    }

    /**
     * 地址信息
     *
     * @param $aOrderInfo
     * @return array[]
     */
    private function _getAddressInfo($aOrderInfo) {
        $aAddressInfo = [
            'start_address_info' => [
                'from_poi_id' => $aOrderInfo['starting_poi_id'] ?? '',
                'from_area'   => $aOrderInfo['area'] ?? '',
                'from_lat'    => $aOrderInfo['starting_lat'] ?? '',
                'from_lng'    => $aOrderInfo['starting_lng'] ?? '',
                'from_name'   => $aOrderInfo['starting_name'] ?? '',
                'canonical_country_code' => $aOrderInfo['country_iso_code'] ?? '',
            ],
            'end_address_info' => [
                'to_poi_id' => $aOrderInfo['dest_poi_id'] ?? '',
                'to_area'   => $aOrderInfo['to_area'] ?? '',
                'to_lat'    => $aOrderInfo['dest_lat'] ?? '',
                'to_lng'    => $aOrderInfo['dest_lng'] ?? '',
                'to_name'   => $aOrderInfo['dest_name'] ?? '',
                'canonical_country_code' => $aOrderInfo['country_iso_code'] ?? '',
            ],
        ];

        return $aAddressInfo;
    }

    /**
     * 弹窗数据
     *
     * @param $aReq
     * @param $aOrderInfo
     * @return array
     */
    private function _getPopup($aReq, $aOrderInfo) {

        foreach (self::$_aPopupClassFactory as $oPopupClass) {
            $oPupop = new $oPopupClass($aReq, $aOrderInfo);
            if ($oPupop->IsHit()) {
                NuwaLog::notice('hit popup ' . $oPopupClass);

                return $oPupop->RenderData();
            }
        }

        return [];
    }

    /**
     * @param array $aOrderInfo $aOrderInfo
     * @param array $aSetting   $aSetting
     *
     * @return array
     */
    private function _getShortBookCard($aOrderInfo, $aSetting) {
        $aResponse = $aSetting['card_book'];

        $aPriceParams = [
            'estimate_id' => $aOrderInfo['estimate_id'],
            'fields'      => ['estimate_fee',],
        ];

        $aQuotation = (new PriceApiClient())->getQuotation($aPriceParams);
        if (!isset($aQuotation['errno']) || Code::E_SUCCESS != $aQuotation['errno'] || empty($aQuotation['data'])) {
            NuwaLog::warning(Msg::formatArray(Code::E_COMMON_GET_QUOTATION_FAIL, ['req' => json_encode($aPriceParams), 'resp' => json_encode($aQuotation)]));

            return [];
        }

        $iDepartureTime = strtotime($aOrderInfo['_birth_time']) + (int)NuwaConfig::getBizConfig('common', 'valid_interval_short_book');
        $aResponse['departure_time_text'] = Language::replaceTag(
            $aResponse['departure_time_text'],
            [
                'date' => DateHelper::getSimpleDate($iDepartureTime),
                'time' => date('H:i', $iDepartureTime),
            ]
        );

        $aResponse['estimate_fee_text'] = Language::replaceTag(
            $aResponse['estimate_fee_text'],
            [
                'fee' => $aQuotation['data']['estimate_fee'],
            ]
        );

        $aResponse['basic_fee_text'] = Language::replaceTag(
            $aResponse['basic_fee_text'],
            [
                'fee' => $aOrderInfo['cap_price'],
            ]
        );

        $aResponse['fee_desc_bubble'] = Language::replaceTag(
            $aResponse['fee_desc_bubble'],
            [
                'fee' => $aOrderInfo['cap_price'] - $aQuotation['data']['estimate_fee'],
            ]
        );

        return $aResponse;
    }

    /**
     * 拼成乐超时卡片
     * @param array  $aOrderInfo  order_info
     * @param string $sAppVersion app_version
     * @return mixed
     */
    private function _getLowPriceCarpoolCard($aOrderInfo, $sAppVersion) {
        //等待激励
        $aRewardText = json_decode(Language::getTextFromDcmp('config_carpool-low_price_carpool_timeout_wait_reward',['-']),true);
        $aConfig     = \BizCommon\Logics\Carpool\WaitReward::getConfig($aOrderInfo);
        $fPrice      = 0.0;
        if (!empty($aConfig)) {
            list($fPrice,$iDiff) = \BizCommon\Logics\Carpool\WaitReward::getInstance()->reallTimeRewardInfo($aConfig,$aOrderInfo);
        }

        $aTimeOutCard = CarpoolBrand::text('config_carpool', 'low_price_carpool_timeout_card', $sAppVersion, $aOrderInfo);
//        $aTimeOutCard = json_decode(Language::getTextFromDcmp('config_carpool-low_price_carpool_timeout_card',['-']),true);
        if (\BizCommon\Utils\Horae::isPoolInTripCarpool($aOrderInfo)) {//边走边拼
            $aRet = $aTimeOutCard['pool_in_trip'];
            if (!empty($aConfig) && !empty($aConfig['max_money']) && $fPrice > 0) {
                $aRet['sub_title'] = sprintf($aRewardText['no_driver'],$aConfig['max_money'] / 100);
            }
        } else {//普通拼成乐
            // 使用拼成乐权益卡的情况下，文案不同
            if (WaitingStage::waitingStageUsedCard($aOrderInfo)) {
                $aRet = $aTimeOutCard['carpool_commute_card'];
                return $aRet;
            }

            $oClient     = new \BizLib\Client\HotspotClient();
            $aOrderList  = [];
            $oPackResult = $oClient->getPinkeRecTripResult($aOrderInfo);
            if (!empty($oPackResult)
                && empty($oPackResult->error_code)
                && ! empty($oPackResult->rec_result)
                && ! empty($oPackResult->rec_result->order_list)
            ) {
                $iHighOrderId = \BizLib\Utils\UtilHelper::genHighIntOrderId($aOrderInfo['order_id'], $aOrderInfo['district']);
                foreach ($oPackResult->rec_result->order_list as $aOrderItem) {
                    if ($aOrderItem->order_id != $iHighOrderId) {
                        $aOrderList [] = $aOrderItem;
                    }
                }
            }

            if (!empty($aOrderList)) {
                $aRet = $aTimeOutCard['normal']['no_driver'];
                if (!empty($aConfig)&&!empty($aConfig['max_money'])&& $fPrice > 0) {
                    $aRet['sub_title'] = sprintf($aRewardText['no_driver'],$aConfig['max_money'] / 100);
                }
            } else {
                $aRet = $aTimeOutCard['normal']['no_relation_passenger'];
                if (!empty($aConfig)&&!empty($aConfig['max_money'])&& $fPrice > 0) {
                    $aRet['sub_title'] = sprintf($aRewardText['no_relation_passenger'],$aConfig['max_money'] / 100);
                }
            }
        }

        return $aRet;
    }

    /**
     * 预约单
     * @param array $aOrderInfo order_info
     * @return bool
     */
    private function _isBookingOrder($aOrderInfo) {
        return Constants\OrderSystem::TYPE_ORDER_BOOKING == $aOrderInfo['type']
            && !Horae::isInterCityCarpool($aOrderInfo);
    }

    /**
     * 预约单超时卡片
     * @param array $aOrderInfo order_info
     * @return array
     */
    private function _getBookingCancelCard($aOrderInfo) {
        $aBookingText = json_decode(Language::getTextFromDcmp('config_passenger-booking_timeout'),true);
        if (Product::isFastcar($aOrderInfo['product_id'])) {
            return $aBookingText['fast'];
        }

        if (Product::isFirstClass($aOrderInfo['product_id'])) {
            return $aBookingText['luxury'];
        }

        if (Product::isSpecial($aOrderInfo['product_id'])) {
            return $aBookingText['special'];
        }

        return [];
    }
}
