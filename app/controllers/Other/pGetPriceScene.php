<?php

use BizLib\Config as NuwaConfig;
use BizLib\Libraries\RedisDB;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Common as UtilsCommon;

class PGetPriceSceneController extends \PreSale\Core\Controller
{
    /** @var $redisdb RedisDB */
    public $redisdb;
    const PRICE_EXPIRE = 600;

    public function init() {
        parent::init();
    }

    public function indexAction() {
        $district  = (string)($this->getRequest()->getQuery('district', false));
        $sLang     = (string)($this->getRequest()->getQuery('lang', false));
        $area      = intval($this->getRequest()->getQuery('area', false));
        $iUserType = intval($this->getRequest()->getQuery('user_type', false));
        $iCommonProductId = intval($this->getRequest()->getQuery('business_id', false));
        //公共产品线id
        $callback = (string)($this->getRequest()->getQuery('callback', false));
        $fastCar  = intval($this->getRequest()->getQuery('flier', false));
        if ($iCommonProductId) {
            $iProductId = \BizLib\Utils\Product::getProductIdByBusinessId($iCommonProductId);
        } elseif ($fastCar) {
            $iProductId = \BizLib\Constants\OrderSystem::PRODUCT_ID_FAST_CAR; //快车订单 3
        } else {
            $iProductId = \BizLib\Constants\OrderSystem::PRODUCT_ID_DEFAULT; //专车订单 1
        }

        $cityInfo = \BizLib\Utils\Locsvr::getCityInfoById($area);
        $district = !empty($cityInfo) ? $cityInfo['district'] : '';
        if (empty($area) || empty($district) || empty($iProductId)) {
            $this->sendJson(UtilsCommon::getErrMsg(GLOBAL_PARAMS_ERROR, NuwaConfig::text('errno', 'errno_msg')), $callback);

            return;
        }

        if (1 == $iUserType) {
            $intRole = 1; //1司机
        } else {
            $intRole = 2; //2乘客
        }

        $aRet            = UtilsCommon::getErrMsg(GLOBAL_SUCCESS);
        $aBillStrategies = $this->_getBillStrategies($iProductId, $district, $area, $intRole);
        if (empty($aBillStrategies) || !isset($aBillStrategies['strategies'])) {
            $this->sendJson(UtilsCommon::getErrMsg(P_ERRNO_PARAMS_ERROR, NuwaConfig::text('errno', 'errno_msg')), $callback);
            return;
        }

        $aTitleList    = NuwaConfig::text('config_order', 'order_combo_type_title', '', $sLang);
        $aRet['data']  = array();
        $aComboTypeIds = $this->_getComboTypeId($aBillStrategies, $aTitleList);
        foreach ($aComboTypeIds as $key => $iComboTypeId) {
            $sSceneType = \BizLib\Utils\Horae::getSceneType($iComboTypeId);
            $aRet['data'][$sSceneType] = array(
                'scene_type' => $sSceneType,
                'title'      => $aTitleList[$iComboTypeId],
            );
        }

        ksort($aRet['data']);
        $aRet['data'] = array_values($aRet['data']);
        $this->sendJson($aRet, $callback);

        return;
    }

    private function _getComboTypeId($aBillStrategies, $aTitleList) {
        $aRet        = array();
        $aStrategies = array_values($aBillStrategies['strategies']);
        if (empty($aStrategies) || !is_array($aStrategies)) {
            return $aRet;
        }

        foreach ($aStrategies as $aStrategie) {
            if (isset($aTitleList[$aStrategie['combo_type']]) && !isset($aRet['combo_type_'.$aStrategie['combo_type']])) {
                $aRet['combo_type_'.$aStrategie['combo_type']] = $aStrategie['combo_type'];
            }
        }

        return array_values($aRet);
    }

    /**
     * 调用 plutus 获得计价配置.
     */
    private function _getBillStrategies($iProductId, $sDistrict, $sarea, $iRole = 2) {
        $aParams = array(
            'product_id' => (int)($iProductId),
            'district'   => $sDistrict,
            'area'       => $sarea,
            'role'       => (int)($iRole),
            'sign'       => true,
        );

        $oRedisdb = \PreSale\Logics\redismove\RedisWrapper::getInstance(D_CITY_COMBO_TITLE);
        $sKey     = UtilsCommon::getRedisPrefix(D_CITY_COMBO_TITLE).md5(json_encode($aParams));
        if (!empty($aCacheData = $oRedisdb->get($sKey))) {
            return json_decode($aCacheData, true);
        }

        $oBillClient = new \BizLib\Client\BillClient();
        $aGetBillStrategiesResult = $oBillClient->getStrategies($aParams);
        if (isset($aGetBillStrategiesResult['result']['errno']) && 0 === $aGetBillStrategiesResult['result']['errno']) {
            $bRet = $oRedisdb->setex($sKey, self::PRICE_EXPIRE, json_encode($aGetBillStrategiesResult['result']['data']));
            if (!$bRet) {
                NuwaLog::warning("set redis failed|key=$sKey");
            }

            return $aGetBillStrategiesResult['result']['data'];
        }

        NuwaLog::warning(sprintf('errno:'.ERRNO_CURL_BILL_BEGIN.'|errmsg:model/bill/getBillStrategies rpc:%s params:%s result:%s', 'getStrategies', json_encode($aParams), json_encode($aGetBillStrategiesResult)));

        return array();
    }
}
