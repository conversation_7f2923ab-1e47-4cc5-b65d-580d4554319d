<?php

use BizLib\Log as NuwaLog;
use BizLib\Utils\Common as UtilsCommon;
/*
 * Class PGetStationStatus.
 *
 * @desc 获取运力预匹配的站点状态
 * @desc 端上预估后，若为站点拼车单，轮训此接口刷新预匹配时间
 *
 * @property OrderStation $OrderStation
 */
use idl\gulfstream\passenger\stationStatus;
use PreSale\Logics\scene\carpool\StationStatusLogic;
use BizLib\Exception\ExceptionWithResp;
use BizLib\ErrCode;
use BizCommon\Models\Passenger\Passenger;

class PGetStationStatusController extends \PreSale\Core\Controller
{
    protected $oBuildStation;

    public function init() {
        parent::init();
        //  $this->load->model('passenger/Passenger');
        $this->oBuildStation = new StationStatusLogic();
    }

    public function indexAction() {
        try {
            $aRet = UtilsCommon::getErrMsg(GLOBAL_SUCCESS);

            // 1、检查参数
            list($oRequest, $iRequireLevel) = $this->_checkParams();

            // 2、build data
            list($oStationStatusData, $aFlag) = $this->_buildData($oRequest, $iRequireLevel);
            if (!empty($aFlag)) {
                $aRet += $aFlag;
                echo json_encode($aRet);

                return;
            }

            // 3、处理并返回结果
            $aRet += $this->_process($oStationStatusData, $iRequireLevel);
            echo json_encode($aRet);

            return;
        } catch (ExceptionWithResp $ex) {
            $aError = UtilsCommon::getErrMsg($ex->getRespCode());
            NuwaLog::warning($ex->getMessage());
            echo json_encode($aError);
        } catch (\Exception $e) {
            $aError = UtilsCommon::getErrMsg((string)($e->getCode()));
            NuwaLog::warning('|errno:'.$aError['errno'].'|errmsg:'.$aError['errmsg']);
            echo json_encode($aError);

            return;
        }
    }

    private function _checkParams() {
        $sToken           = (string)($this->getRequest()->fetchGetPost('token', false));
        $iStationId       = (string)($this->getRequest()->fetchGetPost('station_id', false));
        $sEstimateTraceId = (string)($this->getRequest()->fetchGetPost('estimate_trace_id', false));
        $iArea            = intval($this->getRequest()->fetchGetPost('area', false));
        $sLang            = (string)($this->getRequest()->fetchGetPost('lang', false));
        $sImei            = (string)($this->getRequest()->fetchGetPost('imei', false));
        $sDdfp            = (string)($this->getRequest()->fetchGetPost('ddfp', false));
        $iChannel         = intval($this->getRequest()->fetchGetPost('channel', false));
        $sFrom            = (string)($this->getRequest()->fetchGetPost('from', false));
        $sStartPoiId      = (string)($this->getRequest()->fetchGetPost('starting_poi_id', false));
        $sDstPoiId        = (string)($this->getRequest()->fetchGetPost('dest_poi_id', false));
        $sAppversion      = (string)($this->getRequest()->fetchGetPost('appversion', false));
        $sNetworkType     = (string)($this->getRequest()->fetchGetPost('networkType', false));
        $iPhone           = intval($this->getRequest()->fetchGetPost('phone', false));
        $iPid          = 0;
        $iRequireLevel = intval($this->getRequest()->fetchGetPost('require_level', false));
        if (empty($iRequireLevel)) {
            //5.2.8 以前版本没有传require_level 默认都读站点拼车的缓存
            $iRequireLevel = \BizLib\Utils\CarLevel::DIDI_PUTONG_CAR_LEVEL;
        }

        // 参数整改
        if (empty($sImei)) {
            $sImei = $sDdfp;
        }

        if (!$iStationId || !$sEstimateTraceId || !$iArea || !$sToken) {
            throw new ExceptionWithResp(
                ErrCode\Code::E_COMMON_PARAM_ERROR,
                ErrCode\RespCode::P_PARAMS_ERROR,
                '',
                array(
                    'station_id'        => $iStationId,
                    'estimate_trace_id' => $sEstimateTraceId,
                    'area'              => $iArea,
                    'token'             => $sToken,
                )
            );
        }

        if ($sToken) {
            $aPassengerInfo = Passenger::getInstance()->getPassengerByToken($sToken);
            if (empty($aPassengerInfo) || !isset($aPassengerInfo['pid']) || $aPassengerInfo['pid'] <= 0) {
                NuwaLog::warning(sprintf('errno:'.P_ERRNO_TOKEN_ERROR.' | errmsg:passenger token error token:%s ', $sToken));
                throw new \Exception('', P_ERRNO_TOKEN_ERROR);
            }

            $iPid = $aPassengerInfo['pid'];
            if (empty($this->_iPhone) && isset($aPassengerInfo['phone'])) {
                $iPhone = $aPassengerInfo['phone'];
            }
        }

        $oRequest = new stationStatus\Request();
        $oRequest->passenger_id      = $iPid;
        $oRequest->station_id        = $iStationId;
        $oRequest->estimate_trace_id = $sEstimateTraceId;
        $oRequest->area         = $iArea;
        $oRequest->lang         = $sLang;
        $oRequest->imei         = $sImei;
        $oRequest->channel      = $iChannel;
        $oRequest->from_type    = $sFrom;
        $oRequest->start_poi    = $sStartPoiId;
        $oRequest->dest_poi     = $sDstPoiId;
        $oRequest->appversion   = $sAppversion;
        $oRequest->network_type = $sNetworkType;
        $oRequest->phone        = $iPhone;

        return [$oRequest, $iRequireLevel];
    }

    /*
     * @desc build data
     * @param stationStatus\Request $oRequest
     * @return stationStatus\StationStatusData
     */
    private function _buildData($oRequest, $iRequireLevel) {
        $oStationStatusData = new stationStatus\StationStatusData();
        $oStationStatusData->estimate_trace_id = $oRequest->estimate_trace_id;
        $oStationStatusData->area         = $oRequest->area;
        $oStationStatusData->passenger_id = $oRequest->passenger_id;
        $oStationStatusData->station_id   = $oRequest->station_id;
        $oStationStatusData->lang         = $oRequest->lang;
        $oStationStatusData->imei         = $oRequest->imei;
        $oStationStatusData->appversion   = $oRequest->appversion;
        $oStationStatusData->channel      = $oRequest->channel;
        $oStationStatusData->from_type    = $oRequest->from_type;
        $oStationStatusData->start_poi    = $oRequest->start_poi;
        $oStationStatusData->dest_poi     = $oRequest->dest_poi;
        $oStationStatusData->network_type = $oRequest->network_type;
        $oStationStatusData->phone        = $oRequest->phone;
        $this->oBuildStation->stationConfigApolloTest($oStationStatusData);
        //根据traceid从缓存获取站点信息
        $this->oBuildStation->getStationInfoByTraceId($oStationStatusData, $iRequireLevel);

        //从hotspot_duse获取运力和预匹配时间
        $aFlag = $this->oBuildStation->getDepartureTimeForecast($oStationStatusData, $iRequireLevel);

        return [$oStationStatusData, $aFlag];
    }

    /*
     * @desc process data
     * @param stationStatus\StationStatusData $oStationStatusData
     * @return array $aRet
     */
    private function _process(StationStatus\StationStatusData $oStationStatusData, $iRequireLevel) {
        $aStationInfo = $this->oBuildStation->processStation($oStationStatusData, $iRequireLevel);

        return $aStationInfo;
    }
}
