<?php

use B<PERSON><PERSON>ib\Error\ExceptionHandler;
use B<PERSON><PERSON>ib\Client\DuseApiClient;
use BizCommon\Models\Rpc\PassportRpc;
use BizCommon\Models\Passenger\Passenger;
use BizLib\Exception\ExceptionWithResp;
use BizLib\ErrCode\RespCode;
use BizLib\Utils\Common;
use Disf\SPL\Trace;

/**
 * Class PConfirmInterScan
 */
class PConfirmInterScanController extends \PreSale\Core\Controller
{
    private $_sDriverTicket = '';

    private $_sPassengerToken = '';

    private $_iArea;

    const LOCK_DRIVER_TIME = 5 * 60;

    /**
     * 初始化
     * @return void
     */
    public function init() {
        parent::init();
    }

    /**
     * action
     * @return void
     */
    public function indexAction() {
        try {
            $aResponse = Common::getErrMsg(GLOBAL_SUCCESS);
            $this->_checkParams();
            $aInfos         = $this->_loadInfos();
            $oDuseApiClient = new DuseApiClient();
            $oDuseApiClient->updateDriver('gulfstream', $aInfos['driver_id'], $this->_iArea, ['scan_code_expiry' => $aInfos['expire_time']], MODULE_NAME, Trace::traceId(), '');
        } catch (\Exception $e) {
            $aResponse = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg,]);
        }

        $this->sendJson($aResponse);

    }

    /**
     * 参数校验
     * @return void
     * @throws ExceptionWithResp ExceptionWithResp.
     */
    private function _checkParams() {
        $sPassengerToken        = $this->oRequest->getStr('token');
        $sDriverToken           = $this->oRequest->getStr('inter_scan_token');
        $this->_iArea           = (int)$this->oRequest->getStr('city_id');
        $this->_sDriverTicket   = \BizCommon\Logics\Carpool\InterCarpoolQrCode::getTicketByScanToken($sPassengerToken, $sDriverToken);
        $this->_sPassengerToken = $sPassengerToken;
    }

    /**
     * @return array
     * @throws ExceptionWithResp ExceptionWithResp Exception
     */
    private function _loadInfos() {
        $iPassengerId = Passenger::getInstance()->getPidByToken($this->_sPassengerToken);
        if (empty($iPassengerId) || empty($this->_sDriverTicket)) {
            throw new ExceptionWithResp(
                RespCode::P_PARAMS_ERROR,
                (string)RespCode::P_PARAMS_ERROR,
                '',
                'errmsg:param error! driver_token:' . $this->_sDriverTicket . 'passenger_token:' . $this->_sPassengerToken
            );
        }

        $aPassportInfo = (new PassportRpc())->getPassportInfoFromPassPort($this->_sDriverTicket);
        $iDriverId     = $aPassportInfo['uid'] ?? false;
        $iExpireTime   = time() + self::LOCK_DRIVER_TIME;
        return [
            'passenger_id' => $iPassengerId,
            'driver_id'    => $iDriverId,
            'expire_time'  => $iExpireTime,
        ];
    }
}
