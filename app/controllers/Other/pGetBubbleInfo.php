<?php
/**
 * Created by PhpStorm.
 * <AUTHOR> <<EMAIL>>
 * Date: 2019/9/18
 * Time: 2:03 PM
 */

use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\Language;
use BizLib\Utils\PublicLog;
use BizLib\Utils\UtilHelper;
use BizLib\ExceptionHandler;
use PreSale\Logics\estimatePrice\ParamsLogic;
use PreSale\Models\order\OrderCarpoolDualPrice;
use BizLib\Client\AthenaApiClient;
use PreSale\Logics\estimatePrice\DecisionLogic;
use PreSale\Logics\estimatePrice\FreshEducationLogic;
use BizCommon\Constants\OrderNTuple;
use BizCommon\Models\Order\OrderStation;
use PreSale\Infrastructure\Repository\Redis\CarpoolLongOrderBubble;
use PreSale\Logics\estimatePrice\MarkingBubbleLogic;
use BizLib\Log as NuwaLog;

/**
 * Class PGetBubbleInfoController 拉去气泡信息
 */
class PGetBubbleInfoController extends \PreSale\Core\Controller
{

    public $athena;

    public $orderStation;

    const ESTIMATE_INFO_PUBLIC_KEY = 'g_order_estimate_info';

    const DDS_SOURCE_FROM_MARK = 'confirm';

    /**
     * @var null|DecisionLogic
     */
    private $_oDecision = null;

    private $_aDecisionParams = [];

    private $_aDecisionRsp = [];

    private $_oMarkingBubbleLogic = null;

    /**
     * @var \BizCommon\Models\Cache\EstimatePrice
     */
    private $_oEstimatePriceCache;

    //这次接口调用是否展示了拼车通勤卡的气泡
    private $_iShowCommuteCardBubble = 0;

    //这次接口调用是否展示了拼车通勤卡的h5
    private $_iShowCommuteCardH5 = 0;

    //这次接口调用是否展示了两口价教育(统计需求)
    private $_iShowDualPriceEducation = 0;

    private $_iShowPackageCoupon = 0;

    /**
     * @var \OrderStation
     */
    private $_oOrderStation;

    private $_aAthenaSelectAffirmReq = [];

    private $_aAthenaGuideInfo = [];

    const ATHENA_SELECT_TYPE = 1;

    /**
     * @param null $type 参数
     * @return mixed
     */
    public function init($type = null) {
        parent::init($type);
        $this->athena     = new AthenaApiClient();
        $this->_oDecision = DecisionLogic::getInstance();
        $this->_oMarkingBubbleLogic = MarkingBubbleLogic::getInstance();
        $this->_oEstimatePriceCache = \BizCommon\Models\Cache\EstimatePrice::getInstance();
    }

    /**
     * @return mixed
     */
    public function indexAction() {
        try {
            $oParams        = ParamsLogic::getInstance();
            $aConfirmParams = $oParams->getConfirmParam((array) $this->getRequest()->getQuery(null, false, false));
            $aResp          = UtilsCommon::getErrMsg(GLOBAL_SUCCESS);
            $this->_aAthenaGuideInfo = $this->_getGuideInfoFromAthena($aConfirmParams);
            $this->_aDecisionParams  = $this->_buildDDSReq($aConfirmParams);
            $this->_oDecision->setDecisionParams(array($aConfirmParams));
            $this->_aDecisionRsp = $this->_oDecision->getDecisionResultDirect($this->_aDecisionParams);

            $this->_buildResponse($aConfirmParams, $aResp);
            $this->_setCommuteCardCache($aConfirmParams);
        } catch (\Exception $e) {
            $oHandler      = ExceptionHandler::getInstance();
            $aResponseInfo = $oHandler->handleException($e);
            $this->sendJson($aResponseInfo);
            return;
        }

        $this->sendJson($aResp);
        fastcgi_finish_request();

        $this->_writePublicLog($aConfirmParams);
    }

    /**
     * 目前可能有2个信息需要透出
     * 1. 导流
     * 2. 新手教育组件.
     *
     * @param array $aConfirmParams 参数
     * @param array $aResp          参数
     * @return mixed
     */
    private function _buildResponse($aConfirmParams, &$aResp) {
        // 超值出租车切换tab时不需要导流信息
        if (isset($aConfirmParams['product_category'])
            && \BizLib\Utils\ProductCategory::PRODUCT_CATEGORY_UNIONE_SPECIAL_PRICE == $aConfirmParams['product_category']
        ) {
            return;
        }

        $aDDSAthenaInfo = $this->_buildGuideInfo($aConfirmParams);
        $bDDSAthenaFlag = $aDDSAthenaInfo['bubble_flag'];
        unset($aDDSAthenaInfo['bubble_flag']);
        $_aGuideInfo = array();
        if (!empty($aDDSAthenaInfo) && 0 == $aDDSAthenaInfo['errno']) {
            $_aGuideInfo = $aDDSAthenaInfo;
        }

        if (!$bDDSAthenaFlag && !empty($this->_aAthenaGuideInfo) && isset($this->_aAthenaGuideInfo['data']) && !empty($this->_aAthenaGuideInfo['data'])) {
            $_aGuideInfo = $this->_aAthenaGuideInfo;
        }

        //套餐券气泡优先级最低 && 支持套餐券场景
        if (empty($_aGuideInfo)) {
            $this->_oMarkingBubbleLogic->setParams($aConfirmParams);
            $_aGuideInfo = $this->_oMarkingBubbleLogic->getMarkingBubbleInfo();
            $this->_iShowPackageCoupon = empty($_aGuideInfo) ? 0 : 1;
        }

        if (!$this->_checkDegrade($aConfirmParams) && !empty($_aGuideInfo)) {
            $aResp['data']['athena_info'] = json_encode($_aGuideInfo);
        }

        $_aEducationInfo = $this->_buildFreshEducationInfo($aConfirmParams);
        if (!empty($_aEducationInfo)) {
            $aResp['data']['fresh_guide_page'] = $_aEducationInfo;
        }
    }


    /**
     * @param array $aConfirmParams 参数
     *
     * @return array
     * @wiki http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=152846864
     */
    private function _getGuideInfoFromAthena($aConfirmParams) {
        if ($this->_checkDegrade($aConfirmParams)) {
            return array();
        }

        $this->_aAthenaSelectAffirmReq = $this->_buildAthenaReq($aConfirmParams);
        $aGuideInfo = $this->athena->getSelectAffirmInfo($this->_aAthenaSelectAffirmReq);
        if (empty($aGuideInfo) || !isset($aGuideInfo['errno']) || 0 != $aGuideInfo['errno'] || empty($aGuideInfo['guide_result'])) {
            return array();
        }

        $aAthenaInfo = json_decode($aGuideInfo['guide_result'], JSON_UNESCAPED_UNICODE);
        if (isset($aAthenaInfo['errno']) && 0 != $aAthenaInfo['errno']) {
            return array();
        }

        return $aAthenaInfo;
    }


    /**
     * @date 2018.8.15
     * <AUTHOR>
     * @param array $aConfirmParams 参数
     * @return array
     */
    private function _buildAthenaReq($aConfirmParams) {
        $aAthenaSelectAffirmReq = array(
            'stage'           => self::ATHENA_SELECT_TYPE,
            'estimate_id'     => $aConfirmParams['pre_estimate_id'],
            'client_type'     => $aConfirmParams['common_info']['client_type'],
            'app_version'     => $aConfirmParams['common_info']['app_version'],
            'lang'            => $aConfirmParams['common_info']['lang'],
            'order_type'      => $aConfirmParams['order_info']['order_type'],
            'cur_business_id' => $aConfirmParams['common_info']['business_id'],
            'combo_type'      => $aConfirmParams['order_info']['combo_type'],
            'require_level'   => $aConfirmParams['order_info']['require_level'],
            'phone'           => $aConfirmParams['passenger_info']['phone'] ?? '',
            'pid'             => $aConfirmParams['passenger_info']['pid'] ?? 0,
            'city_id'         => $aConfirmParams['order_info']['area'],
        );

        return $aAthenaSelectAffirmReq;
    }

    /**
     * 从dds的返回中获取导流信息.
     * @param array $aConfirmParams 参数
     * @return array
     */
    private function _buildGuideInfo($aConfirmParams) {
        // anycar默认选中气泡优先级高
        if (\BizLib\Utils\Product::isAnyCar($aConfirmParams['order_info']['product_id'])) {
            $aAnycarAthenaInfo = $this->_buildAnycarBubble($aConfirmParams);
            if (!empty($aAnycarAthenaInfo)) {
                return $aAnycarAthenaInfo;
            }
        }

        $aAthenaRes = $this->_oDecision->getDDSAthenaInfo();
        if (empty($aAthenaRes)) {
            //dds的导流信息优先，没有的话就使用拼车通勤卡的气泡
            $aAthenaRes = $this->_buildCarpoolCommuteBubble($aConfirmParams);
        }

        // $aAthenaRes = $this->_buildLongOrderBubble($aConfirmParams, $aAthenaRes);
        return $aAthenaRes;
    }

    /**
     * @param array $aConfirmParams 参数
     * @param array $aAthenaRes     参数
     * @return array
     */
    /*
    private function _buildLongOrderBubble($aConfirmParams, $aAthenaRes) {
        $sBubbleId = $aConfirmParams['pre_estimate_id'];
        if (empty($sBubbleId) || empty($aAthenaRes)) {
            return $aAthenaRes;
        }

        if (\BizLib\Constants\OrderSystem::PRODUCT_ID_FAST_CAR == $aConfirmParams['order_info']['product_id']
            && \BizLib\Utils\CarLevel::DIDI_XIAOBA_CAR_LEVEL == $aConfirmParams['order_info']['require_level']
            && \BizLib\Constants\OrderSystem::TYPE_COMBO_CARPOOL == $aConfirmParams['order_info']['combo_type']
        ) {
            $iShowCount = CarpoolLongOrderBubble::getShowCount($sBubbleId);
            if (0 == $iShowCount) {
                CarpoolLongOrderBubble::setShowCount($sBubbleId);
            } else {
                return  array(
                    'errno'       => 0,
                    'errmsg'      => 'ok',
                    'data'        => '',
                    'bubble_flag' => false,
                );
            }
        }

        return $aAthenaRes;
    }
    */

    /**
     * 构建拼车通勤卡的气泡
     * @param array $aConfirmParams 参数
     * @return array
     */
    private function _buildCarpoolCommuteBubble($aConfirmParams) {
        //点选拼车
        if ((\BizLib\Constants\OrderSystem::TYPE_COMBO_CARPOOL == $aConfirmParams['order_info']['combo_type']
            && \BizLib\Utils\CarLevel::DIDI_PUTONG_CAR_LEVEL == $aConfirmParams['order_info']['require_level'])
        ) {
            $aCommuteInfo = $this->_oEstimatePriceCache->getCommuteInfo($aConfirmParams['passenger_info']['pid'], $aConfirmParams['pre_estimate_id']);
            //如果carpool_commute_info不为空，则代表
            if (!empty($aCommuteInfo) && !empty($aCommuteInfo['commute_card_info']) && $aCommuteInfo['need_show_commute_card_bubble']) {
                $aDecision    = [
                    'business_id'   => $aConfirmParams['common_info']['business_id'],
                    'product_id'    => $aConfirmParams['order_info']['product_id'],
                    'combo_type'    => $aConfirmParams['order_info']['combo_type'],
                    'require_level' => $aConfirmParams['order_info']['require_level'],
                ];
                $aGuideInfo[] = $this->_oDecision->buildGuideInfo($aDecision, 'carpool_commute', $aCommuteInfo);
            }
        }

        if (empty($aGuideInfo)) {
            return [];
        }

        $aAthenaInfo = array(
            'errno'  => 0,
            'errmsg' => 'ok',
            'data'   => array('guide' => $aGuideInfo,),
        );
        $this->_iShowCommuteCardBubble = 1;

        return $aAthenaInfo;
    }

    /**
     * 构建anycar推荐气泡
     * selectItem 顶导anycar下不会进来，所以只处理快车顶导下anycar逻辑
     * @param array $aConfirmParams 基本信息
     * @return array
     */
    private function _buildAnycarBubble($aConfirmParams) {
        if (!\BizLib\Utils\Product::isAnyCar($aConfirmParams['order_info']['product_id'])) {
            return [];
        }

        $aCarNameConfig  = json_decode(Language::getTextFromDcmp('config_anycar-anycar_simple_name'), true);
        $aSelectCarNames = $aRecommendCarNames = [];
        $aMultiInfo      = json_decode($aConfirmParams['quotation_info']['multi_info'], true) ?: [];
        foreach ($aMultiInfo as $aProduct) {
            $sGroupKey = implode('_', [$aProduct['product_id'], $aProduct['require_level'], $aProduct['combo_type']]);
            $sCarName  = $aCarNameConfig[$sGroupKey] ?? '';
            if (empty($sCarName)) {
                continue;
            }

            if ($aProduct['is_recommend']) {
                $aRecommendCarNames[] = $sCarName;
            }

            if ($aProduct['is_selected']) {
                $aSelectCarNames[] = $sCarName;
            }
        }

        if (empty($aRecommendCarNames)) {
            return [];
        }

        $sBubbleText  = Language::getTextFromDcmp('config_anycar-recommend_more_car_with_name', ['car_names' => implode('、', $aSelectCarNames), 'recommend_car_names' => implode('、', $aRecommendCarNames)]);
        $iShowType    = DecisionLogic::TYPE_ANYCAR_RECOMMEND_BUBBLE;
        $aDecision    = [
            'business_id'   => $aConfirmParams['common_info']['business_id'],
            'product_id'    => $aConfirmParams['order_info']['product_id'],
            'combo_type'    => $aConfirmParams['order_info']['combo_type'],
            'require_level' => $aConfirmParams['order_info']['require_level'],
            'bubble_flag'   => true,
            'bubble_text'   => $sBubbleText,
            'show_type'     => $iShowType,
        ];
        $aGuideInfo[] = $this->_oDecision->buildGuideInfo($aDecision);

        $aAthenaInfo = array(
            'errno'  => 0,
            'errmsg' => 'ok',
            'data'   => array('guide' => $aGuideInfo,),
        );
        return $aAthenaInfo;
    }

    /**
     * 获取新手教育返回.
     * @param array $aConfirmParams 参数
     * @return array|mixed|string
     */
    private function _buildFreshEducationInfo($aConfirmParams) {
        $aProductInfo = $this->_oDecision->getDecisionProductInfo($aConfirmParams['order_info']['product_id'], $aConfirmParams['order_info']['require_level'], $aConfirmParams['order_info']['combo_type']);
        if ($aProductInfo['fresh_education']) {
            return (new FreshEducationLogic($aProductInfo, $aConfirmParams))->getFreshEducationConfig();
        }

        return [];
    }

    /**
     * @date 2018.08.15
     * @param array $aConfirmParams 参数
     * <AUTHOR>
     * @return array
     */
    private function _buildDDSReq($aConfirmParams) {
        // 超值出租车切换tab时不需要导流信息
        if (isset($aConfirmParams['product_category'])
            && \BizLib\Utils\ProductCategory::PRODUCT_CATEGORY_UNIONE_SPECIAL_PRICE == $aConfirmParams['product_category']
        ) {
            return array();
        }

        $aUser           = array(
            'phone' => $aConfirmParams['passenger_info']['phone'] ?? '',
            'pid'   => $aConfirmParams['passenger_info']['pid'] ?? 0,
        );
        $aCommon         = array(
            'start_lat'         => $aConfirmParams['order_info']['from_lat'],
            'start_lng'         => $aConfirmParams['order_info']['from_lng'],
            'to_lat'            => $aConfirmParams['order_info']['to_lat'],
            'to_lng'            => $aConfirmParams['order_info']['to_lng'],
            'client_type'       => $aConfirmParams['common_info']['client_type'],
            'city'              => $aConfirmParams['order_info']['area'] ?? 0, //zhuyi
            'county'            => $aConfirmParams['order_info']['trip_country'] ?? 0, //doudi?
            'menu_id'           => $aConfirmParams['order_info']['menu_id'] ?? 0,
            'sub_menu_id'       => $aConfirmParams['order_info']['sub_menu_id'],
            'estimate_trace_id' => $aConfirmParams['estimate_trace_id'],
            'app_version'       => $aConfirmParams['common_info']['app_version'],
        );
        $aProducts       = array(
            array(
                'product_id'            => $aConfirmParams['order_info']['product_id'],
                'business_id'           => $aConfirmParams['common_info']['business_id'],
                'require_level'         => $aConfirmParams['order_info']['require_level'],
                'combo_type'            => $aConfirmParams['order_info']['combo_type'],
                'carpool_type'          => $this->_getCarpoolTypeByEstimateOrderInfo($aConfirmParams),
                'is_dual_carpool_price' => $aConfirmParams['n_tuple']['is_dual_carpool_price'],
                'estimate_id'           => $aConfirmParams['pre_estimate_id'],
                'is_default'            => 1,
                'station_id'            => $aConfirmParams['order_info']['current_station_id'] ?? '',
                'pre_total_fee'         => 0,
                'estimate_fee'          => 0,
                'discount'              => new \Dirpc\SDK\EstimateDecision\DiscountInfo(),
                'dynamic'               => new \Dirpc\SDK\EstimateDecision\DynamicInfo(),
                'carpool_long_order'    => $aConfirmParams['n_tuple']['carpool_long_order'],
                'carpool_price_type'    => $aConfirmParams['n_tuple']['carpool_price_type'],
            ),
        );
        $aDecisionParams = array(
            'user'        => $aUser,
            'common'      => $aCommon,
            'products'    => $aProducts,
            'source_from' => self::DDS_SOURCE_FROM_MARK,
        );

        return $aDecisionParams;
    }

    /**
     * @param array $aConfirmParams 参数
     *  @return mixed
     */
    private function _writePublicLog(array $aConfirmParams) {
        $aDecisionProductInfo = $this->_oDecision->getDecisionProductInfo($aConfirmParams['order_info']['product_id'], $aConfirmParams['order_info']['require_level'], $aConfirmParams['order_info']['combo_type']);
        if (\BizCommon\Utils\Horae::isCarpoolDualPrice($aDecisionProductInfo) && $aDecisionProductInfo['fresh_education']) {
            $this->_iShowDualPriceEducation = 1;
        }

        $aEstimateStatistic = [
            'opera_stat_key'               => self::ESTIMATE_INFO_PUBLIC_KEY,
            'estimate_id'                  => $aConfirmParams['pre_estimate_id'],
            'area'                         => $aConfirmParams['order_info']['area'],
            'product_id'                   => $aConfirmParams['order_info']['product_id'],
            'require_level'                => $aConfirmParams['order_info']['require_level'],
            'origin_combo_type'            => $aConfirmParams['order_info']['combo_type'],
            'combo_type'                   => $aConfirmParams['order_info']['combo_type'], //因为账单可能修改combo_type，此处特别记录一下请求中的combo_type
            'is_default'                   => 0,
            'has_operation'                => 0,
            'is_show_commute_card_h5'      => $this->_iShowCommuteCardH5,
            'is_show_commute_card_bubble'  => $this->_iShowCommuteCardBubble,
            'is_show_dual_price_education' => $this->_iShowDualPriceEducation,
            'is_show_package_coupon'       => $this->_iShowPackageCoupon,
        ];

        PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);

        return;
    }

    /**
     *  @param array $aConfirmParams 入参
     *  @return mixed
     */
    private function _setCommuteCardCache($aConfirmParams) {
        //点选拼车
        if ((\BizLib\Constants\OrderSystem::TYPE_COMBO_CARPOOL == $aConfirmParams['order_info']['combo_type']
            && \BizLib\Utils\CarLevel::DIDI_PUTONG_CAR_LEVEL == $aConfirmParams['order_info']['require_level'])
        ) {
            $aRetCommuteInfo = $this->_oEstimatePriceCache->getCommuteInfo($aConfirmParams['passenger_info']['pid'], $aConfirmParams['pre_estimate_id']);
            if (!empty($aRetCommuteInfo)) {
                $sRouteId = $aRetCommuteInfo['route_id'];

                if (true == $aRetCommuteInfo['show_h5']) {
                    // 记录该用户这次冒泡是否展示了H5，用于发单时判断是否过拒绝购买通勤卡
                    if (!$aRetCommuteInfo['has_showed_H5']) {
                        $aRetCommuteInfo['has_showed_H5'] = 1;
                        $this->_oEstimatePriceCache->setCommuteInfo($aConfirmParams['passenger_info']['pid'], $aConfirmParams['pre_estimate_id'], $aRetCommuteInfo);
                    }

                    //记录该用户该路线是否展示过，为了X天展示一次
                    $bCommuteCardShow = $this->_oEstimatePriceCache->getCommuteCardCache($aConfirmParams['passenger_info']['pid'], $sRouteId);
                    if (!$bCommuteCardShow) {
                        $this->_iShowCommuteCardH5 = 1;
                        $this->_oEstimatePriceCache->setCommuteCardCache($aConfirmParams['passenger_info']['pid'], $sRouteId);
                    }
                }

                //如果没有展示过气泡，但是需要展示气泡，并且这次调用接口也展示了。那么就需要做标记。标记的作用是用来记录拒绝了多少次。
                if (!$aRetCommuteInfo['has_showed_bubble'] && $aRetCommuteInfo['need_show_commute_card_bubble'] && $this->_iShowCommuteCardBubble) {
                    $aRetCommuteInfo['has_showed_bubble'] = 1;
                    $this->_oEstimatePriceCache->setCommuteInfo($aConfirmParams['passenger_info']['pid'], $aConfirmParams['pre_estimate_id'], $aRetCommuteInfo);
                }
            }
        }

        return;
    }


    /**
     * 导流交互优化开关。
     * @param array $aConfirmParams 参数
     * @return bool
     */
    private function _checkDegrade($aConfirmParams) {
        // 预约单没有气泡
        if (empty($aConfirmParams) || empty($aConfirmParams['order_info']) || !empty($aConfirmParams['order_info']['order_type'])) {
            return true;
        }

        // 小于5.2.20,旧的导流交互模式
        if (version_compare($aConfirmParams['common_info']['app_version'], '5.2.22') < 0) {
            return true;
        }

        return false;
    }

    /**
     * 根据预估信息确定carpool_type.
     * @param array $aParams 参数
     * @return int
     */
    private function _getCarpoolTypeByEstimateOrderInfo($aParams) {
        if (BizLib\Constants\Horae::TYPE_COMBO_CARPOOL_INTER_CITY == $aParams['order_info']['combo_type']) {
            return OrderNTuple::CARPOOL_TYPE_INTERCITY;
        } elseif (BizLib\Constants\Horae::TYPE_COMBO_CARPOOL == $aParams['order_info']['combo_type']) {
            if ('zh-CN' != Language::getLanguage()) {
                return OrderNTuple::CARPOOL_TYPE_NORMAL;
            }

            if ($aParams['order_info']['current_station_id']) {
                return OrderNTuple::CARPOOL_TYPE_STATION;
            } else {
                $this->orderStation   = new OrderStation();
                $this->_oOrderStation = $this->orderStation;
                $aStationList         = $this->_oOrderStation->getStationInfoByTraceId($aParams['estimate_trace_id'], $aParams['order_info']['require_level']);
                if (!empty($aStationList)) {
                    return OrderNTuple::CARPOOL_TYPE_STATION;
                }
            }
        }

        return OrderNTuple::CARPOOL_TYPE_NONE;
    }
}
