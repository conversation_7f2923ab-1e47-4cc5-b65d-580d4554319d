<?php

use BizLib\Utils;
use PreSale\Logics\estimatePriceV2\MultiEstimateDataLogic;
use Dirpc\SDK\PreSale\B2bMultiEstimateDataRequest as Request;
use Dirpc\SDK\PreSale\B2bMultiEstimateDataResponse as Response;
use Dirpc\SDK\PreSale\B2bMultiEstimateData;
use BizLib\Exception\InvalidArgumentException;
use BizLib\ErrCode\Code;
use BizLib\ExceptionHandler;
use Dirpc\SDK\PreSale\B2bDetailDataItem;
use TripcloudCommon\Utils\Product as TripcloudProduct;

/**
 * @authors shexuemeng (<EMAIL>)
 * @date    2021-12-14 16:11:06
 * @desc    企业接入预估6.0 通过eid列表批量获取费用明细、特殊费用、动调
 */
class PGetMultiEstimateDataController extends \PreSale\Core\Controller
{
    const ERROR_CODE_SUCCESS = 0;

    /**
     * @var MultiEstimateDataLogic
     */
    private $_oFeeDetailLogic;
    private $_aTypes = [];
    private $_oEstimateRequest;
    private $_oResponse;
    private $_aEstimateList;
    private $_aEstimatePrices; // 批量调用GetEstimatePriceByEstimateId所得到的{"eid":{...},}

    /**
     * indexAction
     * @return void
     */
    public function indexAction() {
        try {
            $oRequest         = Utils\Request::getInstance();
            $oEstimateRequest = new Request();
            $oEstimateRequest->mergeFromJsonArray($oRequest->get());
            MultiEstimateDataLogic::checkParams($oEstimateRequest);
            $this->_oEstimateRequest = $oEstimateRequest;
            $this->_initEstimateList();
            $aTypes        = MultiEstimateDataLogic::getB2bEstimateDataType($oEstimateRequest);
            $this->_aTypes = $aTypes;
            $this->_oFeeDetailLogic = MultiEstimateDataLogic::getInstance();
            $this->_oFeeDetailLogic->setEstimateRequest($this->_oEstimateRequest);
            $this->_oResponse = $this->_initResponse();
            $this->_buildFeeDetail();
            $this->_buildDynamicDetails();
            $this->_buildSpecialFees();
            $this->_buildBookingRules();
        } catch (\Exception $e) {
            $this->_oResponse = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg,]);
        }

        $this->sendTextJson($this->_oResponse);
        return;
    }

    /**
     * _buildFeeDetail
     * @return void
     */
    private function _buildFeeDetail() {
        if (!in_array(MultiEstimateDataLogic::B2B_ESTIMATE_FEE_DETAIL_TYPE, $this->_aTypes)) {
            return;
        }

        $this->_oResponse = $this->_oFeeDetailLogic->buildPlutusFeeDetails($this->_aEstimateList, $this->_oResponse);
    }

    /**
     * _buildDynamicDetails
     * @return void
     */
    private function _buildDynamicDetails() {
        if (!in_array(MultiEstimateDataLogic::B2B_ESTIMATE_DYNAMIC_TYPE, $this->_aTypes)) {
            return;
        }

        if (empty($this->_aEstimatePrices)) {
            $this->_aEstimatePrices = $this->_oFeeDetailLogic->getEstimatePrices($this->_aEstimateList);
        }

        $this->_oResponse = $this->_oFeeDetailLogic->buildDynamicDetails(
            $this->_aEstimatePrices,
            $this->_oResponse,
            $this->_aEstimateList
        );
    }

    /**
     * _buildSpecialFees
     * @return void
     */
    private function _buildSpecialFees() {
        if (!in_array(MultiEstimateDataLogic::B2B_ESTIMATE_SPECIAL_FEE_TYPE, $this->_aTypes)) {
            return;
        }

        if (empty($this->_aEstimatePrices)) {
            $this->_aEstimatePrices = $this->_oFeeDetailLogic->getEstimatePrices($this->_aEstimateList);
        }

        $this->_oResponse = $this->_oFeeDetailLogic->buildSpecialFees($this->_aEstimatePrices, $this->_oResponse);
    }

    /**
     * _buildBookingRules
     * @return void
     */
    private function _buildBookingRules() {
        if (!in_array(MultiEstimateDataLogic::B2B_ESTIMATE_FEE_BOOKING_RULE_TYPE, $this->_aTypes)) {
            return;
        }

        $this->_oResponse = $this->_oFeeDetailLogic->buildBookingRules(
            $this->_oResponse,
            $this->_aEstimateList
        );
    }
    /**
     * @desc  初始化reponse
     * @return array $oResponse 费用详情
     */
    private function _initResponse() {
        $oResponse = new Response();
        $oResponse->setErrno(self::ERROR_CODE_SUCCESS);
        $oMultiEstimateData = new B2bMultiEstimateData();
        $aItems = [];
        foreach ($this->_aEstimateList as $sEid) {
            if (empty($sEid)) {
                continue;
            }

            $oB2bDetailDataItem = new B2bDetailDataItem();
            $oB2bDetailDataItem->setEstimateId($sEid);
            $aItems[$sEid] = $oB2bDetailDataItem;
        }

        $oMultiEstimateData->setItem($aItems);
        $oResponse->setData($oMultiEstimateData);
        return $oResponse;
    }
    /**
     * @desc  获取预估id列表并赋值
     * @throws InvalidArgumentException 参数校验失败异常
     * @return void
     */
    private function _initEstimateList() {
        $aRes = json_decode($this->_oEstimateRequest->getEstimateIdList(), true);
        if (!is_array($aRes) || (0 === count($aRes))) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                ['estimate_id_list' => $this->_oEstimateRequest->getEstimateIdList()]
            );
        }

        $this->_aEstimateList = $aRes;
    }
}
