<?php
use BizLib\Utils\Common;
use BizCommon\Models\Passenger\Passenger;
use BizLib\ErrCode\RespCode;
use BizLib\Utils\Request;
use BizLib\Config as NuwaConfig;
use BizLib\ErrCode\Code;
use BizLib\ExceptionHandler;
use BizLib\Exception\ExceptionWithResp;
use PreSale\Logics\communicateInfo\CommunicatePageLogic;
use BizLib\Constants\Common as Constants;

/**
 * 获取多个价格沟通说明
 * Class PGetMultiSpecialRuleController
 */
class PGetMultiSpecialRuleController extends \PreSale\Core\Controller
{

    const SPECIAL_RULE       = 1;
    const PROMOTE_SALES_RULE = 2;
    const RECIPE_RULE        = 3;
    const DIDI_PAY_RULE      = 4;


    /**
     * init
     *
     * @return void
     */
    public function init() {
        parent::init();
    }

    private $_sErrMsg = 'FAIL';
    /**
     * Fetches info for employee
     *
     * @return void
     */
    public function indexAction() {
        try {
            //获取默认返回值
            $aResponse = Common::getErrMsg(GLOBAL_SUCCESS);
//            $aResponse['data'] = [];
            $aParams = $this->_getInputParams();

            $aPassengerInfo = Passenger::getInstance()->getPassengerByToken($aParams['token']);
            if (empty($aPassengerInfo)) {
                $aResponse['data'] = [];
                $this->sendTextJson($aResponse);
                return;
            }

            $aParams['phone'] = $aPassengerInfo['phone'];
            $aParams['pid']   = $aPassengerInfo['pid'];

            $oCommunicatePageLogic = new CommunicatePageLogic($aParams);
            $aResponse['data']     = $oCommunicatePageLogic->getCommunicatePage();
        } catch (\Exception $e) {
            $aResponse = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg]);
        }

//        if (!empty($aData)) {
//            $aResponse['data']  = $aData;
//            $aResponse['errno'] = (int)($aResponse['errno']);
//        } else {
//            $aResponse['errno']  = RespCode::R_COMMON_SERVER_ERROR;
//            $aResponse['errmsg'] = $this->_sErrMsg;
//        }
        $this->sendTextJson($aResponse);

//        return;
    }
    /**
     * getParams
     *
     * @return array
     * @throws ExceptionWithResp  参数错误异常
     */
    private function _getInputParams() {
        $oRequest = Request::getInstance();
        $aParams['tab_conf_list'] = json_decode($oRequest->fetchGetPost('tab_conf', false), true);
        $aParams['lang']          = $oRequest->fetchGetPost('lang', false);
        $aParams['app_version']   = $oRequest->fetchGetPost('appversion', false);
        $aParams['area']          = $oRequest->fetchGetPost('city_id', false) ?? 0;
        $aParams['token']         = $oRequest->fetchGetPost('token', false);
        $aParams['trip_cityid']   = $oRequest->fetchGetPost('trip_cityid', false) ?? 0;
        $aParams['access_key_id'] = $oRequest->fetchGetPost('access_key_id', false);
        if (!is_array($aParams['tab_conf_list']) || empty($aParams['lang'])) {
            throw new ExceptionWithResp(
                RespCode::P_PARAMS_ERROR,
                (string) RespCode::P_PARAMS_ERROR,
                'errmsg:param error! tab_conf_list:'.$aParams['tab_conf_list'].'lang:'.$aParams['lang']
            );
        }

        if (empty($aParams['area']) && !empty($aParams['access_key_id']) && Constants::DIDI_IOS_PASSENGER_APP == $aParams['access_key_id']) {
            $aParams['area'] = $aParams['trip_cityid'] ?? 0;
        }

        return $aParams;
    }
}
