<?php
/**
 * Copyright (c) 2019 xiaojukeji.com, Inc. All Rights Reserved.
 * @Author: x<PERSON><PERSON><PERSON>@didiglobal.com
 * @Date  : 2019/4/26 下午15:21
 * @Desc  : 预约能力检查【当前为物品遗失归还入口使用，后续需求可以参考补充】
 * @wiki  : http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=184100853
 *        TODO：如果没有cityid，需要通过坐标转一下，端上可能获取不到cityid
 */

use PreSale\Dto\CheckBookingAbilityRequest;
use PreSale\Application\Service\CheckBookingAbilityService;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\PublicLog;

class PCheckBookingAbilityController extends \PreSale\Core\Controller
{
    public function indexAction() {
        try {
            $request = new CheckBookingAbilityRequest();
            $request->init();

            $service  = new CheckBookingAbilityService();
            $response = $service->execute($request);

            $this->echoSuccessResponseWithJson($response);
        } catch (\Exception $e) {
            $this->_handleException($e);
        }

        $this->_writePublicLog($request);
    }

    private function _handleException(\Exception $e) {
        // TODO: 记录public日志
        $iErrCode    = $e->getCode();
        $sErrMsg     = $e->getMessage();
        $aErrMsgConf = NuwaConfig::text('errno', 'check_booking_ability_error_msg');
        $aError      = UtilsCommon::getErrMsg($iErrCode, $aErrMsgConf);
        $sCallback   = $this->getRequest()->getStr('callback', '');

        // 情况 2，自定义 errmsg 或拼接或透传
        if (!empty($sErrMsg)) {
            $aError['errmsg'] = $sErrMsg;
        }

        // 情况 3，定义了 errmsg 的错误返回，也是最基础的返回
        if (!($e instanceof \PreSale\Exception\BaseException)) {
            $this->sendJson($aError, $sCallback);

            return;
        }

        // 情况 4, 此时为 BaseException
        if (isset($e->mErrno)) {
            $aError['errno'] = $e->mErrno; // 情况 4 1`，见该属性注释
        }

        $aError = array_merge($aError ?? [], $e->mData); // 情况 4 2`，没有 errmsg 或携带附属信息
        $this->sendJson($aError, $sCallback);
    }

    /**
     * @param CheckBookingAbilityRequest $oRequest request
     * @return void
     */
    private function _writePublicLog(CheckBookingAbilityRequest $oRequest) {
        $aRecord = [
            'opera_stat_key' => 'gs_order_check_booking_ability',
            'token'          => $oRequest->token,
            'order_id'       => $oRequest->sOriginOrderid,
            'area'           => $oRequest->area,
            'app_version'    => $oRequest->sAppVersion,
            'lang'           => $oRequest->lang,
            'flat'           => $oRequest->fLat,
            'flng'           => $oRequest->fLng,
            'loss_remand'    => $oRequest->iLossRemand,
        ];
        PublicLog::writeLogForOfflineCal('public', $aRecord);
    }
}
