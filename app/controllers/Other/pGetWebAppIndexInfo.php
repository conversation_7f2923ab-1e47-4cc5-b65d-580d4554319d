<?php

use BizCommon\Logics\RegionalGrowth\ConfigLogic;
use BizCommon\Models\Passenger\Passenger;
use BizCommon\Utils\Common;
use BizLib\Client\PhoenixApiClient;
use BizLib\Client\AdxClient;
use BizLib\Config as NuwaConfig;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\RespCode;
use BizLib\Exception\ExceptionWithResp;
use BizLib\ExceptionHandler;
use BizLib\Utils\Common as UtilsCommon;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Core\Controller;
use BizLib\Utils\MapHelper;
use BizLib\Client\CouponClient;
use BizLib\Utils\NumberHelper;

/**
 * Class PGetWebAppIndexInfoController
 */
class PGetWebAppIndexInfoController extends Controller
{

    const INDEX_RESOURCE_NAME = 'dxd_applet_first_page_enter';
    /**
     * @return void
     * @throws Exception|ExceptionWithResp 异常
     */
    public function indexAction() {
        try {
            $sToken         = $this->getRequest()->getStr('token');
            $sChannel       = $this->getRequest()->getStr('channel');
            $iAccessKeyId   = $this->getRequest()->getInt('access_key_id');
            $iCityId        = $this->getRequest()->getInt('city_id');
            $fLat           = $this->getRequest()->getFloat('location_lat');
            $fLng           = $this->getRequest()->getFloat('location_lng');
            $sAPPVersion    = $this->getRequest()->getStr('app_version');
            $sHeight        = $this->getRequest()->getStr('height');
            $sWidth         = $this->getRequest()->getStr('width');
            $aPassengerInfo = [];

            if (!Common::isRegionalWeChatMiniProgram($iAccessKeyId)) {
                throw new ExceptionWithResp(Code::E_COMMON_PARAM_ERROR, RespCode::P_PARAMS_ERROR);
            }

            if (!empty($sToken)) {
                $aPassengerInfo = Passenger::getInstance()->getPassengerByTokenFromPassport($sToken);

                if (!isset($aPassengerInfo['uid']) || !isset($aPassengerInfo['phone'])) {
                    throw new ExceptionWithResp(Code::E_COMMON_HTTP_PASSPORT_PASSENGER_ERROR, RespCode::P_PARAMS_ERROR);
                }
            }

            // 获取城市信息
            $aCityInfo = [];
            if (!empty($fLng) && !empty($fLat)) {
                $aCityInfo = MapHelper::getAreaInfoByLoc($fLng, $fLat);
            }

            // 获取首页 banner
            $aHomePageBannerConfig = ConfigLogic::getConfig('home_page_banner_config', $iCityId)['banner_info'] ?? new stdClass();
            // 重点资源位
            $aHomePageActivityConfig = ConfigLogic::getConfig('home_page_activity_config', $iCityId)['activity_info'] ?? new stdClass();
            // 导流位
            $aHomePageRecommendConfig = ConfigLogic::getConfig('home_page_recommend_config', $iCityId)['recommend_list'] ?? [];

            // 折扣信息
            $sDiscountText = '';
            $sDiscountUrl  = '';

            // 赚钱中心
            $fAmountMoney = 0;
            if (!empty($aPassengerInfo)) {
                $aRemoteAmountMoneyResult = (new PhoenixApiClient())->getAmountAssetForRegionalGrowth($sToken, $sAPPVersion, $fLat, $fLng, $iCityId);
                if (isset($aRemoteAmountMoneyResult['errno']) && RespCode::D_SUCCESS == $aRemoteAmountMoneyResult['errno']) {
                    $fAmountMoney = round($aRemoteAmountMoneyResult['data']['totalAmount'] ?? 0);
                }
            }

            list($sIcon, $sContent, $sLink) = $this->_getMoneyCenterInfo($sToken, $fLat, $fLng, $sAPPVersion, $iCityId, $sHeight, $sWidth);
            if (!empty($sIcon) || !empty($sContent) || !empty($sLink)) {
                //展示准星数据
                $aHomePageMoneyCenterInfo = NuwaConfig::text('regional_growth', 'money_center_info_by_adx', ['amount_money' => $fAmountMoney, 'icon' => $sIcon, 'content' => $sContent, 'link' => $sLink]);
            } else {
                //展示dcmp兜底数据
                $aHomePageMoneyCenterInfo = NuwaConfig::text('regional_growth', 'money_center_info', ['amount_money' => $fAmountMoney]);
            }

            unset($aHomePageMoneyCenterInfo['bt_message_default']);

            $aResponse = UtilsCommon::getErrMsg(GLOBAL_SUCCESS, $this->aErrnoMsg);
            $aResponse['data']['banner_info']       = $aHomePageBannerConfig;
            $aResponse['data']['discount_text']     = $sDiscountText;
            $aResponse['data']['discount_url']      = $sDiscountUrl;
            $aResponse['data']['activity_info']     = $aHomePageActivityConfig;
            $aResponse['data']['recommend_list']    = $aHomePageRecommendConfig;
            $aResponse['data']['money_center_info'] = $aHomePageMoneyCenterInfo;
        } catch (\Exception $oException) {
            $aResponse = ExceptionHandler::getInstance()->handleException($oException, ['err_msg' => $this->aErrnoMsg]);
        }

        $this->sendJson($aResponse);
    }

    /**
     * _getCouponInfo
     * <AUTHOR> <<EMAIL>>
     * @param  int    $iPid     $iPid
     * @param  string $sChannel $sChannel
     * @return array|mixed
     */
    private function _getCouponInfo($iPid, $sChannel) {
        $aSortGroup    = [
            [
                'condition' => ['expire_time' => ['op' => '>','val' => date('Y-m-d H:i:s')]],
                'sort'      => [['field' => 'coupon_amount','order' => 'desc']],
            ],
        ];
        $aParam        = array(
            'pid'          => $iPid,
            'merge_coupon' => 1,    //是否合并同类券(同一批次同有效期同券面额)1合并
            'optionparams' => base64_encode(json_encode(array('expire_flag' => '1'))), //仅返回未过期的可使用券
            'sort_group'   => urlencode(base64_encode(json_encode($aSortGroup))),
            'filter'       => base64_encode(json_encode((object)array('channel' => array($sChannel)))),
            'productid'    => 210,  //特惠快车传210
        );
        $oCouponClient = new CouponClient();
        $aCouponRet    = $oCouponClient->getAllCoupons($aParam);
        if (isset($aCouponRet['errno']) && (0 == $aCouponRet['errno']) && !empty($aCouponRet['info'])) {
            return $aCouponRet['info'][0];
        } else {
            return array();
        }
    }

    /**
     * _getMoneyCenterInfo
     * <AUTHOR> <<EMAIL>>
     * @param string $sToken      $sToken
     * @param float  $fLat        $fLat
     * @param float  $fLng        $fLng
     * @param string $sAPPVersion $sAPPVersion
     * @param int    $iCityId     $iCityId
     * @param string $sHeight     $sHeight
     * @param string $sWidth      $sWidth
     * @return string[]
     */
    private function _getMoneyCenterInfo($sToken, $fLat, $fLng, $sAPPVersion, $iCityId, $sHeight, $sWidth) {
        $aAdxParams = [
            'resource_names' => self::INDEX_RESOURCE_NAME,
            'token'          => $sToken,
            'platform_type'  => 3,
            'lat'            => $fLat,
            'lng'            => $fLng,
            'appversion'     => $sAPPVersion,
            'city_id'        => $iCityId,
            'height'         => $sHeight,
            'width'          => $sWidth,
            'send_time'      => microtime(true),
            'ip'             => $_SERVER['REMOTE_ADDR'] ?? '',
        ];
        $aAdxResult = (new AdxClient())->getRecomConfigsByAdx($aAdxParams);
        $sIcon      = '';
        $sContent   = '';
        $sLink      = '';
        if (isset($aAdxResult['errno']) && RespCode::D_SUCCESS == $aAdxResult['errno']) {
            $sIcon    = $aAdxResult['result']['data'][self::INDEX_RESOURCE_NAME][0]['icon'] ?? '';
            $sContent = $aAdxResult['result']['data'][self::INDEX_RESOURCE_NAME][0]['content'] ?? '';
            $sLink    = $aAdxResult['result']['data'][self::INDEX_RESOURCE_NAME][0]['link'] ?? '';
        }

        return array(
            $sIcon,
            $sContent,
            $sLink,
        );
    }
}

