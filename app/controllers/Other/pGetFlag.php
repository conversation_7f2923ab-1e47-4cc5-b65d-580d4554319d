<?php

use BizLib\Config as NuwaConfig;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\MapHelper;
use BizLib\Utils\UtilHelper;
/*
 *
 * @authors <PERSON> (chenjin<PERSON>@didichuxing.com)
 * @oldAuthors wkeke(<EMAIL>)
 * @date    2017-07-19 19:56:09
 * @desc    获取开启湾流的开关。
 *          公共平台会下发关于城市的配置，
 *          open_wanliu，城市是否开启湾流，
 *          wanliu_limit ,开通城市是否开启限流。
 *          开启限流后通过此接口来确定是否开启湾流。
 * @wiki http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=71898413
 */
use BizLib\ExceptionHandler;
use BizLib\Exception\ExceptionWithResp;
use BizLib\ErrCode\RespCode;
use BizLib\ErrCode\Code;
use PreSale\Logics\passenger\PassengerPrompt;
use PreSale\Models\carrera\PassengerOpenAppTopic;

class pGetFlagController extends \PreSale\Core\Controller
{
    public $aLogParam;
    public $aPassengerInfo = [];

    public function init() {
        parent::init();
    }

    /**
     * 获取参数并校验.
     *
     * <AUTHOR>
     * @datetime 2017-06-13T20:58:33+0800
     *
     * @return array
     */
    private function _getParams() {
        $sPhone       = $this->oRequest->getInt('phone');
        $fLng         = $this->oRequest->getFloat('lng');
        $fLat         = $this->oRequest->getFloat('lat');
        $iVersionFlag = $this->oRequest->getInt('versionFlag'); //代表不同的策略版本
        if (empty($fLng) || empty($fLat)) {
            $aReturn = UtilsCommon::getErrMsg(RespCode::P_ERRNO_PARAMS_ERROR, $this->aErrnoMsg);
            throw new ExceptionWithResp(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                $aReturn['errno'],
                $aReturn['errmsg'],
                [
                    'lat' => $fLat,
                    'lng' => $fLng,
                ]
            );
        }

        $aOutData = [
            'errno'           => RespCode::P_SUCCESS,
            'errmsg'          => 'ok',
            'wanliu_flag'     => 1,   //湾流产品线是否显示
            'home_flag'       => 0,   //首页湾流创可贴tip是否显示
            'wait_flag'       => 0,   //等待接驾超过wait_time时间后是否显示湾流创可贴
            'wait_time'       => 0,   //等待接驾超过的时间
            'wait_image'      => '',
            'tip_image'       => '',
            'dialog_flag'     => 0,   //取消订单时是否询问使用wanliu
            'dialog_title'    => '',
            'dialog_content'  => '',
            'guide_wait_time' => '',
            'wxagent'         => 0,
            'city_open'       => 0,
            'project_info'    => ['open' => -1, ],
        ];

        return [$sPhone, $fLng, $fLat, $iVersionFlag, $aOutData];
    }

    public function indexAction() {
        $sFrom = $this->oRequest->getStr('from');
        //webapp使用 来源
        $sCallBack = $this->oRequest->getStr('callback');
        //webapp使用 jsonp回调函数名
        try {
            list($sPhone, $fLng, $fLat, $iVersionFlag, $aOutData) = $this->_getParams();
            $aCityInfo = MapHelper::getAreaInfoByLoc($fLng, $fLat);
            if (empty($aCityInfo)) {
                //此前看见过专车入口，但目前城市没有开通,仅需显示入口，不能主动导流
                $this->aLogParam['city_open_wanliu'] = 0;
                $sDistrict = '';
            } else {
                $this->aLogParam['city_open_wanliu'] = 1;
                $aLimitInfo = $aCityInfo['limit'];
                $sDistrict  = $aCityInfo['district'];
                $aOutData['city_open']      = 1;
                $aOutData['wait_flag']      = $aLimitInfo['wait_flag'];
                $aOutData['wait_image']     = $aLimitInfo['wait_image'];
                $aOutData['tip_image']      = $aLimitInfo['tip_image'];
                $aOutData['dialog_flag']    = $aLimitInfo['dialog_flag'];
                $aOutData['dialog_title']   = $aLimitInfo['dialog_title'];
                $aOutData['dialog_content'] = $aLimitInfo['dialog_content'];

                //获取时间
                $aReturn = (new PassengerPrompt())->getWaitTime($iVersionFlag, $sDistrict, $sPhone, $aLimitInfo);
                $aOutData['fastcar_wait_time']        = $aReturn['fastCarWaitTime'];
                $aOutData['guide_wait_time']          = $aOutData['wait_time']          = $aReturn['taxiCarWaitTime'];
                $aOutData['guide_for_taxi_wait_time'] = $aReturn['gulfCarWaitTime'];

                //微信代扣入口字段添加 BY AndyCong
                if (!empty($aCityInfo['district']) && !empty($sPhone)) {
                    $aOutData['wxagent'] = 1;
                }
            }

            //过年回家入口开关获取
            $aOutData['project_info'] = (\BizCommon\Models\Activity\Project::getInstance())->validateProjectOpen($sPhone, $sDistrict);
            //当前区域是否开通快车拼车,新手引导使用，开通即展示引导,车型和产品线只能先写死
            $fMinDiscount = $iFastCarpoolFlag                  = 0;
            $aOutData['wait_reply_msg'] = '';
            //这两个字段和端对了，没什么用，但是解析了，逻辑先下掉
            $aOutData['fast_carpool_flag'] = $iFastCarpoolFlag;
            //标识当前区域是否开通了快车拼车
            //webapp开关
            UtilHelper::isWebapp($sFrom) && $aOutData['show_booking_flag'] = 1;
            $aOutData['airport_guide_flag']     = NuwaConfig::config('config_passenger', 'airport_guide_flag');
            $aOutData['carpool_guide_optimize'] = NuwaConfig::text('config_passenger', 'carpool_guide_optimize');
            1 == (int)($aOutData['airport_guide_flag']) && $aOutData['airport_guide_optimize'] = NuwaConfig::config('config_passenger', 'airport_guide_optimize');
            //若存在折扣值，显示具体折扣
            if ($fMinDiscount > 0) {
                $sCityConfDiscount = NuwaConfig::text('config_passenger', 'carpool_guide_optimize_city_conf_title2');
                $aOutData['carpool_guide_optimize']['title2'] = sprintf($sCityConfDiscount, $fMinDiscount);
            }

            $aOutData['guide_type'] = $this->_getGuideType($sPhone, $aCityInfo);
            $sData = json_encode($aOutData);
            echo UtilHelper::outputCallBack($sData, $sFrom, $sCallBack);
            fastcgi_finish_request();
            $this->aLogParam['phone'] = $sPhone;
            $this->aLogParam['lng']   = $fLng;
            $this->aLogParam['lat']   = $fLat;
            $this->aLogParam['errno'] = RespCode::P_SUCCESS;
            $this->aLogParam['data']  = $sData;
            NuwaLog::updateLogHeadData($this->aLogParam);
            if (empty($this->aPassengerInfo) && !empty($sPhone)) {
                $this->passenger      = \BizCommon\Models\Passenger\Passenger::getInstance();
                $this->aPassengerInfo = $this->passenger->getPassengerInfoByPhone($sPhone);
            }

            if (!empty($this->aPassengerInfo)) {
                $aPassengerKafka        = $this->aPassengerInfo;
                $aPassengerKafka['lat'] = $fLat;
                $aPassengerKafka['lng'] = $fLng;
                $aPassengerKafka['district']        = $sDistrict;
                $aPassengerKafka['passenger_phone'] = $sPhone;
                $aPassengerKafka['city_id']         = $this->oRequest->getInt('city_id');
                $oPassengerOpenAppTopic = new PassengerOpenAppTopic();
                $oPassengerOpenAppTopic->sync($aPassengerKafka);
            }
        } catch (\Exception $e) {
            $aResult = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg, ]);
            echo UtilHelper::outputCallBack(json_encode($aResult), $sFrom, $sCallBack);
        }
    }

    /**
     * 获取一对多导流方案.
     *
     * <AUTHOR>
     * @datetime 2017-07-31T15:36:57+0800
     *
     * @param string $sPhone    手机号
     * @param array  $aCityInfo 城市信息
     *
     * @return array
     */
    private function _getGuideType($sPhone, $aCityInfo) {
        //一对多导流方案
        $iGuideType         = 1;
        $aCityOpenGuideType = NuwaConfig::config('config_order', 'rule_NetPercentage');
        if (in_array($sPhone, $aCityOpenGuideType['whitelist'])) {
            $iGuideType = 2; //小流量,一对多接口
        } elseif (isset($aCityInfo['id']) && array_key_exists($aCityInfo['id'], $aCityOpenGuideType)) {
            $lowPecentage  = floor($aCityOpenGuideType[$aCityInfo['id']]['low_net'] / 10);
            $finalPhoneNum = substr($sPhone, -1);
            if ($finalPhoneNum < $lowPecentage) {
                $iGuideType = 2; //小流量,一对多接口
            } else {
                $iGuideType = 1; //大流量,一对一接口
            }
        }

        return $iGuideType;
    }
}
