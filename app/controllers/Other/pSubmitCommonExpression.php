<?php

use BizCommon\Models\Passenger\Passenger;
use BizLib\Utils;
use BizLib\Utils\Language;
use Dirpc\SDK\PreSale\SubmitCommonExpressionRequest as Request;
use Dirpc\SDK\PreSale\SubmitCommonExpressionResponse as Response;
use PreSale\Logics\estimatePrice\OptionServiceLogic;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Config as NuwaConfig;

/**
 * Class PSubmitCommonExpressionController
 */
class PSubmitCommonExpressionController extends \PreSale\Core\Controller
{

    /**
     * @return void
     */
    public function indexAction() {
        $oRequest       = Utils\Request::getInstance();
        $oSubmitRequest = new Request();
        $oSubmitRequest->mergeFromJsonArray($oRequest->post());
        $sCommonExpression = $oSubmitRequest->getCommonExpression();
        $aPassengerInfo = self::_getPassengerInfo($oSubmitRequest->getToken());
        $sPassengerId = $aPassengerInfo['pid'];
        $aRes = array();
        $aDetectParams['data'] = [
            'content'      => $sCommonExpression,
            'content_type' => COMMON_EXPRESSION_DETECT_TYPE,
            'pid'          => $sPassengerId,
        ];
        // 判断常用语是否是敏感词
        $bIsSensitiveWord = UtilsCommon::isSensitiveWordInLuxuryPreferPageDirpc($aDetectParams);
        if ($bIsSensitiveWord) {
            $aSensitiveWordTips = NuwaConfig::text('config_passenger', 'luxury_optional_page_sensitive_word_tips');
            $aRes['errno'] = COMMON_EXPRESSION_VERIFY_FAILED_TYPE;
            $aRes['errmsg'] = $aSensitiveWordTips['common_expression'];
        } else {
            $aParams = [
                'business_id'       => $oSubmitRequest->getBusinessId(),
                'token'             => $oSubmitRequest->getToken(),
                'uid'               => $oSubmitRequest->getUid(),
                'lang'              => Language::getLanguage(),
                'common_expression' => $sCommonExpression,
                'action_type'       => $oSubmitRequest->getActionType(),
            ];
            $aRes = OptionServiceLogic::submitCommonExpression($aParams);
        }
        // 给端上返回指定的错误码
        if (!empty($aRes) && $aRes['errno'] == 0) {
            $aRenderInfo = UtilsCommon::getErrMsg(GLOBAL_SUCCESS);
        } else {
            $aRenderInfo =
                [
                    'errno'  => $aRes['errno'],
                    'errmsg' => $aRes['errmsg'],
                ];
        }
        //格式化输出
        $oResponse = new Response();
        $oResponse->mergeFromJsonArray($aRenderInfo);
        $aResponseInfo = $oResponse->serializeToJsonArray();
        $this->sendJson($aResponseInfo, (string)($this->getRequest()->getQuery('callback', false)));
    }

    /**
     * 获取用户信息参数
     * @param string $sToken 用户token
     * @return array|mixed
     */
    private function _getPassengerInfo($sToken) {
        if (!empty($sToken)) {
            return (new Passenger())->getPassengerByToken($sToken);
        }
        return array();
    }
}
