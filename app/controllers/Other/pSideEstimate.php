<?php

use Biz<PERSON>ib\ErrCode\Code;
use BizLib\Utils;
use Dirpc\SDK\PreSale\SideEstimateRequest as Request;
use Dirpc\SDK\PreSale\SideEstimateResponse as Response;
use PreSale\Logics\sideEstimate\Entry;
use PreSale\Logics\sideEstimate\globalData\AthenaExtension;
use PreSale\Logics\sideEstimate\globalData\CompensationLogic;
use PreSale\Logics\sideEstimate\globalData\PickOnTimeEstimateInfo;
use PreSale\Logics\sideEstimate\paramSet\ParamSet;
use BizLib\Config as NuwaConfig;
use BizLib\Constants\Horae;
use BizLib\ExceptionHandler;
use PreSale\Logics\newFormTab\Tab;
use PreSale\Logics\sideEstimate\globalData\CarpoolTabInfo;

/**
 * Class PSideEstimateController
 */
class PSideEstimateController extends \PreSale\Core\Controller
{

    const TAB_ID_INTERCITY = 'intercity';

    /**
     * @return void
     */
    public function indexAction() {
        try {
            $oRequest         = Utils\Request::getInstance();
            $oEstimateRequest = new Request();
            //让参数支持get和post请求
            $aGetParam   = $oRequest->get();
            $aPostParam  = $oRequest->post(null,  false, false);
            $aGetParam   = is_array($aGetParam) ? $aGetParam : array();
            $aPostParam  = is_array($aPostParam) ? $aPostParam : array();
            $oEstimateRequest->mergeFromJsonArray(array_merge($aGetParam, $aPostParam));
            $this->_checkParams($oEstimateRequest);

            $oSet = new ParamSet($oEstimateRequest);
            $oSet->buildFeaturePlugin();
            $oSet->buildUserInfo();
            $oSet->buildQuotation();
            $oSet->buildText();
            $oSet->buildTabData();
            $oSet->buildMultiSideEstimate();
            $oSet->buildFlightInfo();

            $this->_initGlobalData($oSet);

            $oEntry = new Entry($oSet);
            $aData  = $oEntry->buildResponse();

            $oRsp = new Response();
            $oRsp->mergeFromJsonArray($aData);
            $this->sendJson($oRsp->serializeToJsonArray());

            fastcgi_finish_request();

            $oEntry->writePublicLog($oSet, $oRsp);
        } catch (Exception $e) {
            $aErrMsg       = NuwaConfig::text('errno', 'pEstimatePrice_error_msg');
            $oHandler      = ExceptionHandler::getInstance();
            $aResponseInfo = $oHandler->handleException($e, ['err_msg' => $aErrMsg, ]);
            $oResponse     = new Response();
            $oResponse->mergeFromJsonArray($aResponseInfo);
            $this->sendJson($oResponse->serializeToJsonArray());
        }
    }

    /**
     * 初始化确认使用的全局数据
     * @param ParamSet $oParamsSet $oParamsSet
     * @return void
     */
    private function _initGlobalData(ParamSet $oParamsSet) {

        // $needTabInfo = function ($sTabId) use ($oParamsSet) : bool {
        //     return count($oParamsSet->aTabList) >= 2 && isset($oParamsSet->aTabList[$sTabId]) && ($oParamsSet->oReq->getTabId() != $sTabId);
        // };
        $onlyTabInfo = function ($sTabId) use ($oParamsSet) : bool {
            return ((1 == count($oParamsSet->aTabList) && isset($oParamsSet->aTabList[$sTabId])) || 0 == count($oParamsSet->aTabList)) && ($oParamsSet->oReq->getTabId() == $sTabId);
        };

        $HaveTabInfo = function () use ($oParamsSet) : bool {
            return count($oParamsSet->aTabList) >= 2;
        };
        //如果当前Tab多余两个 或 如果当前只有默认拼tab时
        if ($onlyTabInfo(Tab::RECCARPOOL_TAB) || $HaveTabInfo() || $onlyTabInfo(Tab::MINIBUS_TAB)) {
            CarpoolTabInfo::getInstance()->fetchCarpoolTabProductInfo($oParamsSet->oReq, $oParamsSet->oQuotation);
        }

        AthenaExtension::getInstance()->fetchAthenaExtensionInfo($oParamsSet->oReq, $oParamsSet->oUser, $oParamsSet->oQuotation, $oParamsSet->aTabList, $oParamsSet);
        //必有车预估
        PickOnTimeEstimateInfo::getInstance()->fetchPickOnTimeEstimate($oParamsSet);
        CompensationLogic::getInstance()->getMultiCompensationAbility($oParamsSet);

    }


    /**
     * @param Request $oReq 接口入参
     * @throws Exception/void ...
     * @return void
     */
    private function _checkParams(Request $oReq) {
        if (empty($oReq->getMultiProductCategory()) && ($oReq->getTabId() != Tab::MINIBUS_TAB)) {
            goto SideEstimateParamErr;
        }

        if (empty($oReq->getEstimateTraceId())) {
            goto SideEstimateParamErr;
        }

        if (Horae::PAGE_TYPE_INTER_CITY == $oReq->getPageType()) {
            $oReq->setTabId(self::TAB_ID_INTERCITY);
        }

        if (empty($oReq->getTabId())) {
            goto SideEstimateParamErr;
        }

        if (empty($oReq->getAccessKeyId())) {
            goto SideEstimateParamErr;
        }

        if (empty($oReq->getToken())) {
            goto SideEstimateParamErr;
        }

        return;

        SideEstimateParamErr:
            $oEx = new Exception(Code::getMsg(Code::E_COMMON_PARAM_ERROR),Code::E_COMMON_PARAM_ERROR);
            throw $oEx;
    }
}
