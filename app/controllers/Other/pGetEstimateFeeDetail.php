<?php

use Biz<PERSON>ommon\Constants\OrderNTuple;
use BizCommon\Logics\Order\OrderComLogic;
use BizCommon\Models\Cache\EstimatePrice;
use BizCommon\Models\Order\Order;
use BizCommon\Utils\Horae as BizCommonHorae;
use BizLib\Client\DosClient;
use BizLib\Config;
use BizLib\Config as NuwaConfig;
use BizLib\Constants;
use BizLib\Constants\OrderSystem;
use BizLib\ErrCode\RespCode;
use BizLib\Exception\ExceptionWithResp;
use BizLib\ExceptionHandler;
use BizLib\Log as NuwaLog;
use BizLib\Utils\ApolloHelper;
use BizLib\Utils\Horae;
use BizLib\Utils\Language;
use BizLib\Utils\NumberHelper;
use BizLib\Utils\Product as UtilProduct;
use BizLib\Utils\ProductCategory;
use BizLib\Utils\UtilHelper;
use BizLib\Utils\Currency;
use Dukang\PropertyConst\Order\OrderEstimatePcId;
use Nuwa\ApolloSDK\Apollo;
use Nuwa\ApolloSDK\Apollo as NuwaApollo;
use PreSale\Core\Controller;
use PreSale\Logics\estimatePrice\EstimateFeeDetailLogic;
use PreSale\Logics\estimatePrice\FeeDetailLogic;
use PreSale\Logics\estimatePrice\FeeMemberInfo;
use PreSale\Logics\estimatePrice\multiResponse\component\priceDescInfo\PriceItemFormatter;
use PreSale\Logics\estimatePriceV2\multiResponse\virtualGroupCar\TaxiPricingSingle;
use PreSale\Logics\order\AnyCarOrderLogic;
use PreSale\Logics\taxi\TaxiCarType;
use PreSale\Logics\taxi\TaxiPeakFee;
use PreSale\Logics\v3Estimate\multiResponse\Component\introMsg\NormalIntroMsgV2;
use PreSale\Logics\v3Estimate\multiResponse\Component\introMsg\Util;
use PreSale\Models\order\OrderCarpoolDualPrice;
use TripcloudCommon\Utils\Product as TripcloudProduct;
use PreSale\Infrastructure\Repository\Redis\RecComboCache;
use BizCommon\Models\Passenger\Passenger;
use PreSale\Logics\v3Estimate\multiResponse\Component\basicFeeMsg\Util as FeeUtil;
use TripcloudCommon\Utils\ApolloConf as TripcloudApolloConf;

/**
 * Class PGetEstimateFeeDetailController
 * @desc 预估费用详情页
 * @wiki http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=*********
 */
class PGetEstimateFeeDetailController extends Controller
{
    const ZERO           = 0;
    const FEE_DETAIL_FAQ = 'fee_detail_page_FAQ_';
    const PAGE_TYPE_5    = 0; //5版本的费用明细页
    const PAGE_TYPE_6    = 1; //6版本的费用明细页

    /**fee_type**/
    const FEE_TYPE_CARPOOL_LOW_PRICE = 0; //拼成乐费用
    const FEE_TYPE_POOL_IN_TRIP      = 1; //拼成乐边走边拼费用
    const FEE_TYPE_CARPOOL_GO_CARD   = 2; //拼成乐权益卡未拼成费用
    const TICKET_TYPE_COUPON = '7'; // 大巴优待票
    const DETAIL_STAGE_PRE   = 'pre';
    const DETAIL_STAGE_AFTER = 'after';

    // 出租车峰期加价标识
    const TAXI_PEAK_FEE      = 'taxi_peak_price';
    const TAXI_PEAK_DISCOUNT = 'taxi_peak_discount';

    // 支付宝支付券标识
    const COUPON_SOURCE_ALIPAY = 'alipay';
    // A+一口价key
    const APLUS_CAP_PRICE = 'multi_factor_cap_price';

    // tab
    const TAB_REQUIRED_FULL     = 'pincheche_full';
    const TAB_REQUIRED_ONE_SEAT = 'pincheche_one_seat';

    const MULTI_FACTOR_FLAT_RATE_ESTIMATE_EXPIRE = 120; //多因素一口价报价的有效时间

    const COUPON_TYPE = 111; // type:111 滴滴拼车折扣券

    // RevolvingAccountDiscountType 折扣类型
    const RevolvingAccountDiscountType = 'revolvingAccountDiscount';
    const RANGE_MIN_PRICE = 'fast_range_min_price';
    const RANGE_MAX_PRICE = 'fast_range_max_price';

    const RANGE_TOTAL_MIN_PRICE = 'fast_total_range_min_price';
    const RANGE_TOTAL_MAX_PRICE = 'fast_total_range_max_price';
    const MIN_PRICE       = 'min_price';
    const MAX_PRICE       = 'max_price';

    private $_isDualPrice       = false;
    private $_oCarpoolDualPrice = null;
    private $_aParams;
    private $_aExamParams = [];

    const RENTAL_COMBO       = 10009;
    const PREMIER_PRODUCT_ID = 1;

    /**
     * @return void
     */
    public function indexAction() {

        try {
            $this->_getAndCheckParams();

            // 获取费用详情信息
            $aRet = $this->_getFeeInfo($this->_aParams['estimate_id'], $this->_aParams['short_book'], $this->_aParams['stage'], $this->_aParams['tab_required']);

            // 构建实验参数
            $this->_aExamParams = $this->_getApolloParams($aRet);

            if (!isset($aRet['discount_desc_anycar']) || empty($aRet['discount_desc_anycar'])) {
                $aRet = $this->_formatFeeDetail($aRet, $this->_aParams['lang'], $this->_aParams['order_id'], $this->_aParams['estimate_id'], $this->_aParams['page_type']);
            } else {
                $aRet = $this->_formatFeeDetailAnycar($aRet, $this->_aParams['lang'], $this->_aParams['preference_product']);
            }

            $aResponse['errno'] = self::ZERO;
            $aResponse['data']  = $aRet;
        } catch (\Exception $e) {
            $aResponse = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg,]);
        }

        $this->sendTextJson($aResponse);

        return;
    }

    /**
     * 获取预估费用详情
     * @param int $iEstimateID 预估ID，预估阶段产生
     * @param int $iShortBook  $iShortBook
     * @return array
     * @throws \BizLib\Exception\InvalidArgumentException|ExceptionWithResp 参数异常
     */
    private function _getFeeInfo($iEstimateID, $iShortBook = -1) {
        $oFeeDetailLogic = FeeDetailLogic::getInstance();
        $oFeeDetailLogic->initParams($this->_aParams);
        $aRet = $oFeeDetailLogic->getFeeDetailData(
            $iEstimateID,
            $iShortBook,
            $this->_aParams['lang'],
            $this->_aParams['uid']
        );

        return $aRet;
    }


    /**
     * @param array $aRet
     * @return array
     */
    private function _getApolloParams($aRet) {

        if (empty($aRet) || empty($aRet['n_tuple'])) {
            return [];
        }

        $aParams = [
            'key'               => $this->_aParams['pid'],
            'pid'               => $this->_aParams['pid'],
            'city'              => $aRet['area'],
            'city_id'           => $aRet['area'],
            'p_access_key_id'   => $this->_aParams['access_key_id'],
            'app_version'       => $this->_aParams['app_version'],
            'lang'              => $this->_aParams['lang'],
            'caller'            => 'pre-sale',
            'product_id'        => $aRet['n_tuple']['product_id'],
            'combo_type'        => $aRet['n_tuple']['combo_type'],
            'carpool_type'      => $aRet['n_tuple']['carpool_type'],
        ];

        return $aParams;

    }

    /**
     * @throws ExceptionWithResp 异常
     * @return void
     */
    private function _getAndCheckParams() {
        //获取参数
        $sEstimateID        = $this->oRequest->getStr('estimate_id');
        $sLanguage          = $this->oRequest->getStr('lang');
        $sPreferenceProduct = $this->oRequest->getStr('preference_product');
        $iPageType          = $this->oRequest->getStr('page_type');
        $sChargeDate        = $this->oRequest->getStr('charge_date');
        $iShortBook         = $this->oRequest->getInt('short_book', -1); // ??
        $sTabRequired       = $this->oRequest->getStr('tab');
        $iSelectRecCombo    = $this->oRequest->getInt('is_select_rec_combo'); //是否勾选了套餐
        $sOid   = $this->oRequest->getStr('oid');
        $iToken = $this->oRequest->getStr('token');

        $sPassengerInfo = Passenger::getInstance()->getPassengerByTokenFromPassport($iToken);
        $iPid           = $sPassengerInfo['pid'];

        $aOrderId = UtilHelper::decodeId($sOid);
        $sStage   = self::DETAIL_STAGE_PRE;
        if (empty($sEstimateID)) {
            list($sEstimateID, $iCarpoolRequireNum) = $this->_loadEstimateMetaFromDos($aOrderId);
            $sStage = self::DETAIL_STAGE_AFTER;
        }

        if (empty($iPageType)) {
            $iPageType = self::PAGE_TYPE_5;
        }

        //检查参数
        if (empty($sEstimateID)) {
            throw new ExceptionWithResp(
                RespCode::P_PARAMS_ERROR,
                (string) RespCode::P_PARAMS_ERROR,
                'errmsg:param error! estimate_id:' . $sEstimateID,
                'errmsg:param error! oid:' . $sOid
            );
        }

        $aPreferenceProduct = [];
        if (!empty($sPreferenceProduct)) {
            $aPreferenceProduct = json_decode($sPreferenceProduct, true);
        }

        $this->_aParams = [
            'oid'                 => $sOid,
            'estimate_id'         => $sEstimateID,
            'lang'                => $sLanguage,
            'preference_product'  => $aPreferenceProduct,
            'page_type'           => $iPageType,
            'order_id'            => $aOrderId,
            'charge_date'         => $sChargeDate,
            'short_book'          => $iShortBook,
            'tab_required'        => $sTabRequired,
            'stage'               => $sStage,
            'is_select_rec_combo' => $iSelectRecCombo,
            'pid'                 => $iPid,
            'uid'                 => $sPassengerInfo['uid'],
            'phone'               => $sPassengerInfo['phone'],
            'app_version'         => $this->oRequest->getStr('app_version'),
            'access_key_id'       => $this->oRequest->getStr('access_key_id'),
            'tab_id'              => $this->oRequest->getStr('tab_id'),
            self::RANGE_MIN_PRICE => $this->oRequest->getStr(self::RANGE_MIN_PRICE),
            self::RANGE_MAX_PRICE => $this->oRequest->getStr(self::RANGE_MAX_PRICE),
        ];
    }

    /**
     * 获取费用明细
     * @param array  $aFeeDetailData 预估详情数据
     * @param string $sLanguage      语言
     * @param array  $aOrderId       订单 ID
     * @param int    $iEstimateID    预估 ID
     * @param int    $iPageType      费用明细页
     * @return array
     * @throws ExceptionWithResp  ExceptionWithResp
     * @throws \BizLib\Exception\InvalidArgumentException ExceptionWithResp
     */
    private function _formatFeeDetail($aFeeDetailData, $sLanguage, $aOrderId, $iEstimateID, $iPageType) {
        if (empty($aFeeDetailData)) {
            return array();
        }

        if ($this->_bAddOtherProductFeeDetail($aFeeDetailData['n_tuple'], $iPageType)) {
            // fee_type == 1 ??
            unset($aFeeDetailData['fee_desc']['items'][1]);
            $iEstimateId = EstimatePrice::getInstance()->getEstimateLink($iEstimateID);
            $aRet        = $this->_getFeeInfo($iEstimateId);
            $aFeeDetailData['fee_desc']['items'] = array_merge($aRet['fee_desc']['items'], $aFeeDetailData['fee_desc']['items']);

            // 正常优惠列表
            if (isset($aFeeDetailData['discount_desc'][1])) {
                unset($aFeeDetailData['discount_desc'][1]);
            }

            if (isset($aRet['discount_desc'][1])) {
                unset($aRet['discount_desc'][1]);
            }

            $aFeeDetailData['discount_desc'] = array_merge($aRet['discount_desc'], $aFeeDetailData['discount_desc']);
        }

        //过滤费用项
        $aFeeDetailData = $this->_formatFeeDesc($aFeeDetailData, $sLanguage, $aOrderId, $iPageType);

        //常见问题获取
        $aFAQConfig = $this->_getAllQuestions($aFeeDetailData['n_tuple']);
        $aFeeDetailData['fee_desc'] = array_merge($aFeeDetailData['fee_desc'], $aFAQConfig);

        // 产品优势获取
        $sProductCategory  = $aFeeDetailData['product_category'];
        $aProductAdvantage = $this->_getProductAdvantages($sProductCategory);
        $aFeeDetailData['fee_desc']['product_advantages'] = $aProductAdvantage;

        //优惠说明获取
        $aDiscountInfo = $this->_getDiscountInfo($sProductCategory);
        $aFeeDetailData['fee_desc']['discount_info'] = $aDiscountInfo;

        //背景颜色获取
        $aBackgroundColor = $this->_getBackgroundColor($sProductCategory);
        $aFeeDetailData['fee_desc']['bg_colors'] = $aBackgroundColor;

        //车大联盟page_title获取
        $sPageTitle = $this->_getSpaciousPageTitle($sProductCategory);
        if (!empty($sPageTitle)) {
            $aFeeDetailData['fee_desc']['page_title'] = $sPageTitle;
        }

        //车大联盟标题条颜色获取
        $sTitleBar = $this->_getTitileBar($sProductCategory);
        $aFeeDetailData['fee_desc']['title_bar_color'] = $sTitleBar;

        $aFeeDetailData['fee_desc']['price_rule_hidden'] = 0;

        // 普通出租车、优选出租车、超值出租车的计价规则按钮关闭，一车两价开启
        if (
            TaxiCarType::isCNPutongTaxi($aFeeDetailData['n_tuple'])
            || TaxiCarType::isCNYouxuanTaxi($aFeeDetailData['n_tuple'])
            || TaxiCarType::isTaxiFixedPrice($aFeeDetailData['n_tuple'])
            || BizCommonHorae::isBargainOrder($aFeeDetailData['n_tuple'])
            || TaxiCarType::isCountyTaxi($sProductCategory)
            || TripcloudApolloConf::isYuexingSaaSBusiness((string)$aFeeDetailData['n_tuple']['business_id'])
        ) {
            $aFeeDetailData['fee_desc']['price_rule_hidden'] = 1;
        }

        //6.0详情页资源（产品描述 + 头图 + 标题）
        if (self::PAGE_TYPE_6 == $iPageType) {
            $aFeeDetailResource = $this->_getFeeDetailResource($aFeeDetailData);
            if (!empty($aFeeDetailResource)) {
                $aFeeDetailData['fee_desc'] = array_merge($aFeeDetailData['fee_desc'], $aFeeDetailResource);
            }

            $aFeeDetailData['fee_desc']['business_id']   = $aFeeDetailData['n_tuple']['business_id'];
            $aFeeDetailData['fee_desc']['require_level'] = $aFeeDetailData['n_tuple']['require_level'];
            if (TripcloudProduct::isTripcloudByProductID($aFeeDetailData['n_tuple']['product_id'])) {
                $aFeeDetailData['fee_desc']['is_trip_cloud'] = 1;
            } else {
                $aFeeDetailData['fee_desc']['is_trip_cloud'] = 0;
            }

            // 香港商家品类
            if (\BizCommon\Utils\Horae::isHongKongSaaS($aFeeDetailData['n_tuple']['product_id'])) {
                $aConf = Language::getDecodedTextFromDcmp('hk_product-third_business_estimate_fee_info', $this->_aParams['lang']);
                if (!empty($aConf['info'])) {
                    $aFeeDetailData['fee_desc'] = array_merge($aFeeDetailData['fee_desc'], $aConf['info']);
                }

                if (!empty($aConf[$aFeeDetailData['n_tuple']['product_id']]['info'])) {
                    $aFeeDetailData['fee_desc'] = array_merge($aFeeDetailData['fee_desc'], $aConf[$aFeeDetailData['n_tuple']['product_id']]['info']);
                }

                $aFeeDetailData['fee_desc']['service_provider']['icon'] = $aFeeDetailData['fee_desc']['service_provider']['icon'] ??
                    Util::getCarIconByProductCategory(
                        $aFeeDetailData['product_category'],
                        $aFeeDetailData['n_tuple']['carpool_type'],
                        '',
                        $aFeeDetailData['area'],
                        $this->_aParams['lang']
                );
            }

            $oToggle = Apollo::getInstance()->featureToggle(
                'premier_rental_price_rule',
                [
                    'city'         => $aFeeDetailData['area'],
                    'pid'          => $this->_aParams['pid'],
                    'passenger_id' => $this->_aParams['pid'],
                ]
            );
            if (
                $oToggle->allow()
                && self::RENTAL_COMBO == $aFeeDetailData['n_tuple']['combo_type']
                && self::PREMIER_PRODUCT_ID == $aFeeDetailData['n_tuple']['product_id']
            ) {
                list($bOk, $aConfigs) = Apollo::getInstance()->getConfigsByNamespaceAndConditions(
                    'tag_service',
                    [
                        'tag_name' => 'premier_rental_price_rule_url',
                    ]
                )->getAllConfigData();
                if ($bOk && !empty($aConfigs)) {
                    $aUrlConfig = current($aConfigs);
                    $aFeeDetailData['fee_desc']['price_rule_url'] = $aUrlConfig['tag_number'];
                }
            }
        }

        if (\BizCommon\Utils\Horae::isHongKongProduct($aFeeDetailData['n_tuple']['product_id'])) {
            $aFeeDetailData['fee_desc']['is_hk_product'] = 1;
        }

        return $aFeeDetailData['fee_desc'];
    }

    /**
     * @param array $aNtuple   n_tuple
     * @param int   $iPageType page_type
     * @return bool
     */
    private function _bAddOtherProductFeeDetail($aNtuple, $iPageType) {
        if (BizCommonHorae::isCarpoolUnsuccRealTimePrice($aNtuple) && self::PAGE_TYPE_6 == $iPageType) {
            return true;
        }

        if (BizCommonHorae::bUnioneStationCarpool($aNtuple) && self::PAGE_TYPE_6 == $iPageType) {
            return true;
        }

        return false;
    }

    /**
     * 格式化费用描述信息
     * @param array  $aFeeDetailData data
     * @param string $sLanguage      lang
     * @param array  $aOrderId       oid
     * @param int    $iPageType      page_type
     * @return mixed
     */
    private function _formatFeeDesc($aFeeDetailData, $sLanguage, $aOrderId, $iPageType = 0) {
        //过滤费用项
        $aFeeDetailData = $this->_feeItemFilter($aFeeDetailData, $aOrderId);

        if (!isset($aFeeDetailData['fee_desc']['items'][0]['real_pay'])) {
            $aFeeDetailDataFeeDescItem0 = array_shift($aFeeDetailData['fee_desc']['items']);
        }

        //霸王花优惠项标签配置
        $aTagStyleConfig = ApolloHelper::getConfigContent('king_flower_config', 'bwh_price_dec_img');
        $aStyleConfig    = $aTagStyleConfig['discount_tag_style'];
        $aCouponConfig   = $aTagStyleConfig['special_coupon_style'] ?? [];

        $aEstimateFeeDetail = NuwaConfig::text('config_text', 'estimate_fee_detail');

        $aRealPayTotalFeeCarpoolUnSuccessFlatPrice = array();
        $iProductCategory = (new ProductCategory())->getProductCategoryByNTuple($aFeeDetailData['n_tuple']);
        $iProductId       = $aFeeDetailData['n_tuple']['product_id'];

        $aDiscountSet = $aFeeDetailData['discount_set'];

        foreach ($aFeeDetailData['fee_desc']['items'] as $iIndex => $aBillDesc) {
            $iFeeType        = $aBillDesc['fee_type'] ?? $iIndex;
            $aDiscountDesc   = $aFeeDetailData['discount_desc'][$iFeeType];
            $aDiscountDescV2 = [];
            $iPbdCallReturnAmount = 0;
            $iAlipayCouponAmount  = 0;
            $fIncreaseTotal       = 0.0;

            if (!empty($aBillDesc['_carpool_scene_discount_item']) && !empty($aBillDesc['_carpool_scene_discount_item_v2'])) {
                $aDiscountDesc   = $aBillDesc['_carpool_scene_discount_item'] ?? [];
                $aDiscountDescV2 = $aBillDesc['_carpool_scene_discount_item_v2'] ?? [];
            }

            // pbd呼返-先取内层拼车，再取外层非拼车
            if (isset($aBillDesc['_carpool_scene_discount_set']) && isset($aBillDesc['_carpool_scene_discount_set']['pbd_call_return']) && $aBillDesc['_carpool_scene_discount_set']['pbd_call_return']['amount'] > 0) {
                $iPbdCallReturnAmount = $aBillDesc['_carpool_scene_discount_set']['pbd_call_return']['amount'];
            } elseif (isset($aDiscountSet) && isset($aDiscountSet['pbd_call_return']) && $aDiscountSet['pbd_call_return']['amount'] > 0) {
                $iPbdCallReturnAmount = $aDiscountSet['pbd_call_return']['amount'];
            }

            // external_coupon(支付宝券)
            if (isset($aBillDesc['_carpool_scene_discount_set']) && isset($aBillDesc['_carpool_scene_discount_set']['external_coupon'])) {
                $aExternalCoupon = $aBillDesc['_carpool_scene_discount_set']['external_coupon'];
                if (self::COUPON_SOURCE_ALIPAY == $aExternalCoupon['coupon_source']) {
                    $iAlipayCouponAmount = $aExternalCoupon['amount'];
                }
            } elseif (isset($aDiscountSet) && isset($aDiscountSet['external_coupon'])) {
                $aExternalCoupon = $aDiscountSet['external_coupon'];
                if (self::COUPON_SOURCE_ALIPAY == $aExternalCoupon['coupon_source']) {
                    $iAlipayCouponAmount = $aExternalCoupon['amount'];
                }
            }

            $fDiscountTotal = 0.0;
            $sUnit          = Language::getUnit($sLanguage);
            //香港出租车使用HK$
            if (\BizCommon\Utils\Horae::isHongKongProduct((int)$aFeeDetailData['n_tuple']['product_id'])) {
                $sCurrency         = $aFeeDetailData['currency'];
                $sUnit['currency'] = Currency::getCurrencyUnit($sCurrency, $sLanguage);
            }

            // 限定出租车
            if (OrderSystem::PRODUCT_ID_UNITAXI == $iProductId) {
                foreach ($aBillDesc['need_pay_detail'] as &$val) {
                    // 峰期加价
                    if (self::TAXI_PEAK_FEE == $val['key']) {
                        $aTaxiPeakFeeText = \PreSale\Logics\taxi\TaxiPeakFee::DCMP_INTERACTIVE_DEFAULT_TEXT_KEY;
                        // 如果获取不到文案，过滤
                        if (empty($aTaxiPeakFeeText)) {
                            continue;
                        }

                        $aTextInfo = json_decode(Language::getTextFromDcmp($aTaxiPeakFeeText), true);
                        $hintLink  = $aTextInfo['interactive']['info_url'];
                        $urlParam  = [
                            'city_id'          => $aFeeDetailData['area'],
                            'product_category' => $iProductCategory,
                            'product_id'       => $aFeeDetailData['n_tuple']['product_id'],
                            'estimate_id'      => $this->_aParams['estimate_id'],
                        ];

                        //apollo开关控制是否下发新H5
                        $oApollo  = new Apollo();
                        $bIsAllow = $oApollo->featureToggle(
                            'taxi_peak_fee_rule_propaganda',
                            array(
                                'pid'          => $this->_aParams['pid'],
                                'passenger_id' => $this->_aParams['pid'],
                                'city'         => $aFeeDetailData['area'],
                                'from'         => 1,
                            )
                        )->allow();

                        if ($bIsAllow) {
                            $hintLink = TaxiPeakFee::getPeakFeeDcmpByKey(
                                'h5_url'
                            );

                            $urlParam = [
                                'departure_time'   => $aFeeDetailData['url_params']['departure_time'],
                                'product_category' => $iProductCategory,
                                'product_id'       => $aFeeDetailData['n_tuple']['product_id'],
                                'estimate_id'      => $this->_aParams['estimate_id'],
                                'county_id'        => $aFeeDetailData['county_id'],
                                'trip_cityid'      => $aFeeDetailData['area'],
                            ];
                        }

                        if (empty($hintLink)) {
                            continue;
                        }

                        // 出租车峰期加价价格说明h5页面
                        $val['hint'] = [
                            'link' => sprintf($hintLink, http_build_query($urlParam)),
                        ];
                        $sText       = Language::getTextFromDcmp('taxi_peak_fee-is_charged_for_details');
                        // 页面下方追加峰期费用描述
                        $aFeeDesc = json_decode($sText, true);
                        if (empty($aFeeDesc)) {
                            continue;
                        }

                        // 重置价格说明文案
                        $aBillDesc['real_pay']['intros'] = $aFeeDesc;
                    }
                }

                foreach ($aBillDesc['discount'] as &$val) {
                    // 峰期折扣
                    if (self::TAXI_PEAK_DISCOUNT == $val['key']) {
                        $aTaxiPeakFeeText = \PreSale\Logics\taxi\TaxiPeakFee::DCMP_INTERACTIVE_DEFAULT_TEXT_KEY;
                        // 如果获取不到文案，过滤
                        if (empty($aTaxiPeakFeeText)) {
                            continue;
                        }

                        $aTextInfo = json_decode(Language::getTextFromDcmp($aTaxiPeakFeeText), true);
                        $hintLink  = $aTextInfo['interactive']['info_url'];
                        $urlParam  = [
                            'city_id'          => $aFeeDetailData['area'],
                            'product_category' => $iProductCategory,
                            'product_id'       => $aFeeDetailData['n_tuple']['product_id'],
                            'estimate_id'      => $this->_aParams['estimate_id'],
                        ];

                        //apollo开关控制是否下发新H5
                        $oApollo  = new Apollo();
                        $bIsAllow = $oApollo->featureToggle(
                            'taxi_peak_fee_rule_propaganda',
                            array(
                                'pid'          => $this->_aParams['pid'],
                                'passenger_id' => $this->_aParams['pid'],
                                'city'         => $aFeeDetailData['area'],
                                'from'         => 1,
                            )
                        )->allow();

                        if ($bIsAllow) {
                            $hintLink = TaxiPeakFee::getPeakFeeDcmpByKey(
                                'h5_url'
                            );

                            $urlParam = [
                                'departure_time'   => $aFeeDetailData['url_params']['departure_time'],
                                'product_category' => $iProductCategory,
                                'product_id'       => $aFeeDetailData['n_tuple']['product_id'],
                                'estimate_id'      => $this->_aParams['estimate_id'],
                                'county_id'        => $aFeeDetailData['county_id'],
                                'trip_cityid'      => $aFeeDetailData['area'],
                            ];
                        }

                        if (empty($hintLink)) {
                            continue;
                        }

                        // 出租车峰期加价价格说明h5页面
                        $val['hint'] = [
                            'link' => sprintf($hintLink, http_build_query($urlParam)),
                        ];
                    }
                }
            }

            // 特快range模式
            if ($this->_hitAPlusPriceRange($aFeeDetailData)) {
                foreach ($aBillDesc['need_pay_detail'] as &$aAPlusVal) {
                    if (self::APLUS_CAP_PRICE == $aAPlusVal['key']) {
                        $aAPlusRangeConf = Language::getDecodedTextFromDcmp('config_text-aplus_range_config');
                        $aAPlusRangeText = $aAPlusRangeConf['fee_detail_text'] ?? '';
                        if (empty($aAPlusRangeText)) {
                            continue;
                        }

                        $aAPlusVal['value'] = $aAPlusRangeText . $aAPlusVal['value'];
                        if (!empty($aAPlusVal['sub_items'])) {
                            foreach ($aAPlusVal['sub_items'] as &$aAPlusSubItems) {
                                $aAPlusSubItems['value'] = $aAPlusRangeText . $aAPlusSubItems['value'];
                            }
                        }
                    }
                }
            }

            //价格权益
            if (isset($aFeeDetailData['price_privilege_type']) && OrderNTuple::PRICE_PRIVILEGE_TYPE_FAST_UP_DEFAULT == $aFeeDetailData['price_privilege_type']) {
                $aBillDesc['discount'][] = [
                    'name'           => $aEstimateFeeDetail['price_privilege_title'],
                    'value'          => $aEstimateFeeDetail['price_privilege_value'],
                    'text'           => $aStyleConfig['coupon']['text'],
                    'text_color'     => $aStyleConfig['coupon']['text_color'],
                    'text_bg_colors' => $aStyleConfig['coupon']['text_bg_colors'],
                    'border_color'   => $aStyleConfig['coupon']['border_color'],
                ];

                if (!isset($aBillDesc['real_pay']['intros'])) {
                    $aBillDesc['real_pay']['intros'] = array();
                }

                $aIntro = [
                    'value'      => $aEstimateFeeDetail['price_privilege_intros'],
                    'font_color' => $aEstimateFeeDetail['price_privilege_font_color'],
                ];
                array_unshift($aBillDesc['real_pay']['intros'], $aIntro);
            }

            //惊喜专线
            if (isset($aDiscountDesc['special_line']['amount']) && $aDiscountDesc['special_line']['amount'] > 0) {
                $aBillDesc['discount'][] = [
                    'value'          => Language::replaceTag(
                        $aEstimateFeeDetail['discount_fee_value'],
                        array(
                            'fee'             => NumberHelper::numberFormatDisplay($aDiscountDesc['special_line']['amount'], '', 2),
                            'currency_unit'   => $sUnit['currency'],
                            'currency_symbol' => '',
                        )
                    ),
                    'text'           => $aStyleConfig['surprise_special_fee']['text'],
                    'text_color'     => $aStyleConfig['surprise_special_fee']['text_color'],
                    'text_bg_colors' => $aStyleConfig['surprise_special_fee']['text_bg_colors'],
                ];
            }

            //折扣券文案
            if (isset($aDiscountDesc['coupon']['amount']) && $aDiscountDesc['coupon']['amount'] > 0 && $this->_canShowCoupon($aFeeDetailData)) {
                $sDiscount = sprintf('%.2f', $aDiscountDesc['coupon']['amount']);   //价格信息统一展示小数点后2位
                if ($aBillDesc['real_pay']['bill_total_fee'] - $aDiscountDesc['coupon']['amount'] < 0) {
                    $sDiscount = sprintf('%.2f', $aBillDesc['real_pay']['bill_total_fee']);
                }

                $fDiscountTotal += $sDiscount;
                $sCouponType     = $aDiscountDesc['coupon']['coupon_type'] ?? '';
                $sBadge          = $aEstimateFeeDetail['coupon_badge'][$sCouponType] ?? '';
                $aCouponHint     = $aEstimateFeeDetail['coupon_hint'][$sCouponType] ?? [];

                $aHint      = [];
                $sCustomTag = $aDiscountDesc['coupon']['custom_tag'] ?? '';
                //在没有badge的时候 展示呼返的icon/hint
                if (empty($sBadge) && PriceItemFormatter::COUPON_TAG_POPE_CALLRETURN == $sCustomTag) {
                    // 呼返券icon
                    // 下面的key, 名为呼返, 实为惊喜优惠
                    $sBadgeImg = $aEstimateFeeDetail['pope_callreturn_icon'] ?? '';
                    $aHint     = $aEstimateFeeDetail['pope_callreturn_hint'] ?? [];
                }

                if (!empty($aCouponHint)) {
                    $aHint = $aCouponHint;
                }

                //券规则调整地址
                if (empty($this->_aParams['oid'])) {
                    if (!empty($aDiscountDescV2)) {
                        $couponID = $aDiscountDescV2['coupon']['coupon_id'] ?? '';
                    } else {
                        $couponID = $aDiscountDesc['coupon']['coupon_id'] ?? '';
                    }

                    //两口价V2：拼车一口价，未拼成快车、拼成乐V1：拼成只有一个价格
                    //$aHint不为空时不下发跳转链接，下发弹窗，目前有coupon_type='111'的预估价折扣券类型
                    if (
                        !$this->_bAddOtherProductFeeDetail($aFeeDetailData['n_tuple'], $iPageType)
                        && !empty($aEstimateFeeDetail['coupon_msg_url'] && empty($aHint))
                    ) {
                        $aHint['link'] = $aEstimateFeeDetail['coupon_msg_url'] . '?coupon_id=' . $couponID . '&lang=' . $this->_aParams['lang'];
                    }
                }

                if ('call_return' == $sCouponType) {
                    $sDiscountTagText = $aCouponConfig['call_return']['detail_text'];
                } else {
                    $sDiscountTagText = $aStyleConfig['coupon']['text'];
                }

                $sDiscountName = $this->_getDiscountName($sCustomTag, $iProductCategory);
                if (empty($sDiscountName)) {
                    // 默认
                    $sDiscountName = $aEstimateFeeDetail['coupon_title'];
                }

                $aDiscountItem = [
                    'name'           => $sDiscountName,
                    'badge'          => $sBadge,
                    'badge_img'      => $sBadgeImg,
                    'value'          => Language::replaceTag(
                        $aEstimateFeeDetail['discount_fee_value'],
                        array(
                            'fee'             => NumberHelper::numberFormatDisplay($sDiscount, '', 2),
                            'currency_unit'   => $sUnit['currency'],
                            'currency_symbol' => '',
                        )
                    ),
                    'hint'           => empty($aHint) ? (object)[] : $aHint,
                    'text'           => $sDiscountTagText,
                    'text_color'     => $aStyleConfig['coupon']['text_color'],
                    'text_bg_colors' => $aStyleConfig['coupon']['text_bg_colors'],
                    'border_color'   => $aStyleConfig['coupon']['border_color'],
                    'sub_items'      => [
                        ['name' => $aEstimateFeeDetail['coupon_policy_desc'],],
                    ],
                ];

                // 优惠券折扣详情
                $iCouponDiscount = $aDiscountDesc['coupon']['coupon_discount']; // 折扣: 3折 = $iCouponDiscount为30
                $iCouponType     = $aDiscountDesc['coupon']['coupon_type'];
                if (
                    isset($iCouponDiscount) && self::COUPON_TYPE == $iCouponType
                    && $iCouponDiscount > 0
                    &&  $aFeeDetailData['n_tuple']['carpool_type'] > 0
                ) {
                    $fCouponDiscount = 0 == $iCouponDiscount % 10 ? $iCouponDiscount / 10 : NumberHelper::numberFormatDisplay($iCouponDiscount / 10, '', 1);

                    $aDiscountItem['name']   = Language::replaceTag(
                        $aEstimateFeeDetail['carpool_coupon_title'],
                        array('discount' => $fCouponDiscount,)
                    );
                    $aDiscountItem['detail'] = Language::replaceTag(
                        $aEstimateFeeDetail['carpool_coupon_detail'],
                        array('discount' => $fCouponDiscount,)
                    );
                }

                $aBillDesc['discount'][] = $aDiscountItem;
            }

            //打车月卡
            if (isset($aDiscountDesc['month']['amount']) && $aDiscountDesc['month']['amount'] > 0) {
                $sDiscount       = sprintf('%.2f', $aDiscountDesc['month']['amount']);   //价格信息统一展示小数点后2位
                $fDiscountTotal += $sDiscount;
                $sBadge          = $aEstimateFeeDetail['coupon_badge'][4] ?? '';
                $aBillDesc['discount'][] = [
                    'name'  => $aEstimateFeeDetail['coupon_title'],
                    'badge' => $sBadge,
                    'value' => Language::replaceTag(
                        $aEstimateFeeDetail['discount_fee_value'],
                        array(
                            'fee'             => NumberHelper::numberFormatDisplay($aDiscountDesc['month']['amount'], '', 2),
                            'currency_unit'   => $sUnit['currency'],
                            'currency_symbol' => '',
                        )
                    ),
                ];
            }

            //学生卡
            if (isset($aDiscountDesc['student']['amount']) && $aDiscountDesc['student']['amount'] > 0) {
                $sDiscount       = sprintf('%.2f', $aDiscountDesc['student']['amount']);   //价格信息统一展示小数点后2位
                $fDiscountTotal += $sDiscount;
                $aBillDesc['discount'][] = [
                    'name'      => $aEstimateFeeDetail['coupon_title'],
                    'value'     => Language::replaceTag(
                        $aEstimateFeeDetail['discount_fee_value'],
                        array(
                            'fee'             => NumberHelper::numberFormatDisplay($aDiscountDesc['student']['amount'], '', 2),
                            'currency_unit'   => $sUnit['currency'],
                            'currency_symbol' => '',
                        )
                    ),
                    'sub_items' => [
                        ['name' => $aEstimateFeeDetail['student_sub_title'], 'value' => ''],
                    ],
                ];
            }

            //打车金项
            if (isset($aDiscountDesc['reward']['amount']) && $aDiscountDesc['reward']['amount'] > 0) {
                $fDiscountTotal         += $aDiscountDesc['reward']['amount'];
                $aBillDesc['discount'][] = [
                    'name'           => $aEstimateFeeDetail['reward_title'],
                    'value'          => Language::replaceTag(
                        $aEstimateFeeDetail['discount_fee_value'],
                        array(
                            'fee'             => NumberHelper::numberFormatDisplay($aDiscountDesc['reward']['amount'], '', 2),
                            'currency_unit'   => $sUnit['currency'],
                            'currency_symbol' => '',
                        )
                    ),
                    'text'           => $aStyleConfig['user_reward']['text'],
                    'text_color'     => $aStyleConfig['user_reward']['text_color'],
                    'text_bg_colors' => $aStyleConfig['user_reward']['text_bg_colors'],
                    'border_color'   => $aStyleConfig['user_reward']['border_color'],
                ];
            }

            //香港打车金项 DiDi Dollar
            if (isset($aDiscountDesc['hkbonus']['amount']) && $aDiscountDesc['hkbonus']['amount'] > 0) {
                $fDiscountTotal         += $aDiscountDesc['hkbonus']['amount'];
                $aBillDesc['discount'][] = [
                    'name'  => $aEstimateFeeDetail['hkbonus_title'],
                    'value' => Language::replaceTag(
                        $aEstimateFeeDetail['discount_fee_value'],
                        array(
                            'fee'             => NumberHelper::numberFormatDisplay($aDiscountDesc['hkbonus']['amount'], '', 2),
                            'currency_unit'   => $sUnit['currency'],
                            'currency_symbol' => '',
                        )
                    ),
                ];
            }

            //内循环账户折扣项
            if (isset($aDiscountDesc[self::RevolvingAccountDiscountType]['amount']) && $aDiscountDesc[self::RevolvingAccountDiscountType]['amount'] > 0) {
                $fDiscountTotal         += $aDiscountDesc[self::RevolvingAccountDiscountType]['amount'];
                $aBillDesc['discount'][] = [
                    'name'  => $aEstimateFeeDetail['revolving_account_discount_title'],
                    'value' => Language::replaceTag(
                        $aEstimateFeeDetail['discount_fee_value'],
                        array(
                            'fee'             => NumberHelper::numberFormatDisplay($aDiscountDesc[self::RevolvingAccountDiscountType]['amount'], '', 2),
                            'currency_unit'   => $sUnit['currency'],
                            'currency_symbol' => '',
                        )
                    ),
                    'type'  => \PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo\PriceItemFormatter::PRICE_TYPE_REVOLVING_ACCOUNT_DISCOUNT,
                ];
            }

            //三方呼返优惠
            if ($aDiscountSet['trip_cloud_discount'] && $aDiscountSet['trip_cloud_discount']['deduction_amount'] > 0) {
                $tripCloudDiscountAmount = $aDiscountSet['trip_cloud_discount']['deduction_amount'] / 100.0;
                $fDiscountTotal         += $tripCloudDiscountAmount;
                $aBillDesc['discount'][] = [
                    'name'  => $aEstimateFeeDetail['trip_cloud_discount'],
                    'value' => Language::replaceTag(
                        $aEstimateFeeDetail['discount_fee_value'],
                        array(
                            'fee'             => NumberHelper::numberFormatDisplay($tripCloudDiscountAmount, '', 2),
                            'currency_unit'   => $sUnit['currency'],
                            'currency_symbol' => '',
                        )
                    ),
                    'hint'  => $aEstimateFeeDetail['trip_cloud_discount_hint'],
                ];
            }

            // pbd呼返补贴
            if ($iPbdCallReturnAmount > 0) {
                $fCallReturn     = $iPbdCallReturnAmount / 100.0;
                $fDiscountTotal += $fCallReturn;
                $aBillDesc['discount'][] = [
                    'key'    => 'mpt_pbd_call_return',
                    'number' => -$fCallReturn,
                    'name'   => $aEstimateFeeDetail['pbd_call_return'],
                    'value'  => Language::replaceTag(
                        $aEstimateFeeDetail['discount_fee_value'],
                        array(
                            'fee'             => NumberHelper::numberFormatDisplay($fCallReturn, '', 2),
                            'currency_unit'   => $sUnit['currency'],
                            'currency_symbol' => '',
                        )
                    ),
                ];
            }

            // 大车出行卡感知
            if (isset($aDiscountSet) && isset($aDiscountSet['bus_card']) && $aDiscountSet['bus_card']['amount'] > 0) {
                $fBusCard        = $aDiscountSet['bus_card']['amount'] / 100.0;
                $fDiscountTotal += $fBusCard;

                $aTicketSeatInfo = json_decode($aFeeDetailData['ticket_seat_info'], true);
                $aTicketSeatInfo = is_array($aTicketSeatInfo) ? $aTicketSeatInfo : [];
                foreach ($aTicketSeatInfo as $aSeatItem) {
                    if (self::TICKET_TYPE_COUPON == $aSeatItem['type']) {
                        $aSubItems = [['name' => $aEstimateFeeDetail['bus_card_sub_items']]];
                        break;
                    }
                }

                $aBillDesc['discount'][] = [
                    'number' => -$fBusCard,
                    'name'   => $aEstimateFeeDetail['bus_card'],
                    'value'  => Language::replaceTag(
                        $aEstimateFeeDetail['discount_fee_value'],
                        array(
                            'fee'             => NumberHelper::numberFormatDisplay($fBusCard, '', 2),
                            'currency_unit'   => $sUnit['currency'],
                            'currency_symbol' => '',
                        )
                    ),
                    'sub_items' => $aSubItems,
                ];
            }

            // 周三会员折上折
            // 先取内层拼车，再取外层非拼车
            if (
                isset($aBillDesc['_carpool_scene_discount_set'])
                && isset($aBillDesc['_carpool_scene_discount_set']['discount_card'])
                && $aBillDesc['_carpool_scene_discount_set']['discount_card']['amount'] > 0
            ) {
                $aDiscountCard = $aBillDesc['_carpool_scene_discount_set']['discount_card'];
            } elseif (
                isset($aDiscountSet) && isset($aDiscountSet['discount_card']) && $aDiscountSet['discount_card']['amount'] > 0
            ) {
                $aDiscountCard = $aDiscountSet['discount_card'];
            }

            if (isset($aDiscountCard)) {
                $fMemberDiscount = $aDiscountCard['amount'] / 100.0;
                $fDiscountTotal += $fMemberDiscount;

                $sName = $aEstimateFeeDetail['member_discount_default'];
                if (!empty($aFeeDetailData['member_info']['level_id'])) {
                    $sName = Language::replaceTag(
                        $aEstimateFeeDetail['member_discount'],
                        array('level_name' => $aFeeDetailData['member_info']['level_id'])
                    );
                }

                $aBillDesc['discount'][] = [
                    'number' => -$fMemberDiscount,
                    'name'   => $sName,
                    'value'  => Language::replaceTag(
                        $aEstimateFeeDetail['discount_fee_value'],
                        array(
                            'fee'             => NumberHelper::numberFormatDisplay($fMemberDiscount, '', 2),
                            'currency_unit'   => $sUnit['currency'],
                            'currency_symbol' => '',
                        )
                    ),
                ];
            }

            // 新流折上折
            if (
                isset($aDiscountSet) && isset($aDiscountSet['discount_card'])
                && $aDiscountSet['discount_card']['amount'] > 0
                && 'newloss_discount' == $aDiscountSet['discount_card']['custom_tag']
            ) {
                $fMemberDiscount = $aDiscountSet['discount_card']['amount'] / 100.0;
                $fDiscountTotal += $fMemberDiscount;

                $sName = $aEstimateFeeDetail['new_loss_discount'] ?? '';

                $aBillDesc['discount'][] = [
                    'number' => -$fMemberDiscount,
                    'name'   => $sName,
                    'value'  => Language::replaceTag(
                        $aEstimateFeeDetail['discount_fee_value'],
                        array(
                            'fee'             => NumberHelper::numberFormatDisplay($fMemberDiscount, '', 2),
                            'currency_unit'   => $sUnit['currency'],
                            'currency_symbol' => '',
                        )
                    ),
                ];
            }

            // 单单省钱卡，上下文if中有很多可以公用的部分可以抽象出来独立为一个函数，增加代码复用度，todo
            if (isset($aDiscountDesc['economical_card_right']['amount']) && $aDiscountDesc['economical_card_right']['amount'] > 0) {
                $fDiscountTotal         += $aDiscountDesc['economical_card_right']['amount'];
                $aBillDesc['discount'][] = [
                    'name'  => $aEstimateFeeDetail['economical_card_title'],
                    'value' => Language::replaceTag(
                        $aEstimateFeeDetail['discount_fee_value'],
                        array(
                            'fee'             => NumberHelper::numberFormatDisplay($aDiscountDesc['economical_card_right']['amount'], '', 2),
                            'currency_unit'   => $sUnit['currency'],
                            'currency_symbol' => '',
                        )
                    ),
                ];
            }

            //畅行卡项
            if (isset($aDiscountDesc['backcard']['amount']) && $aDiscountDesc['backcard']['amount'] > 0) {
                $fDiscountTotal         += $aDiscountDesc['backcard']['amount'];
                $aBillDesc['discount'][] = [
                    'name'  => $aEstimateFeeDetail['backcard_title'],
                    'value' => Language::replaceTag(
                        $aEstimateFeeDetail['discount_fee_value'],
                        array(
                            'fee'             => NumberHelper::numberFormatDisplay($aDiscountDesc['backcard']['amount'], '', 2),
                            'currency_unit'   => $sUnit['currency'],
                            'currency_symbol' => '',
                        )
                    ),
                ];
            }

            //超级会员项
            if (isset($aDiscountDesc['paid_member']['amount']) && $aDiscountDesc['paid_member']['amount'] > 0) {
                $fDiscountTotal         += $aDiscountDesc['paid_member']['amount'];
                $aBillDesc['discount'][] = [
                    'name'  => $aEstimateFeeDetail['paid_member_title'],
                    'value' => Language::replaceTag(
                        $aEstimateFeeDetail['discount_fee_value'],
                        array(
                            'fee'             => NumberHelper::numberFormatDisplay($aDiscountDesc['paid_member']['amount'], '', 2),
                            'currency_unit'   => $sUnit['currency'],
                            'currency_symbol' => '',
                        )
                    ),
                ];
            }

            //市民卡项
            if (isset($aDiscountDesc['citycard']['amount']) && $aDiscountDesc['citycard']['amount'] > 0) {
                $fDiscountTotal         += $aDiscountDesc['citycard']['amount'];
                $aBillDesc['discount'][] = [
                    'name'  => $aEstimateFeeDetail['citycard_title'],
                    'value' => Language::replaceTag(
                        $aEstimateFeeDetail['discount_fee_value'],
                        array(
                            'fee'             => NumberHelper::numberFormatDisplay($aDiscountDesc['citycard']['amount'], '', 2),
                            'currency_unit'   => $sUnit['currency'],
                            'currency_symbol' => '',
                        )
                    ),
                ];
            }

            //限时特惠
            if (isset($aDiscountDesc['limittime']['amount']) && $aDiscountDesc['limittime']['amount'] > 0) {
                $fDiscountTotal         += $aDiscountDesc['limittime']['amount'];
                $aBillDesc['discount'][] = [
                    'name'  => $aEstimateFeeDetail['limittime_title'],
                    'value' => Language::replaceTag(
                        $aEstimateFeeDetail['discount_fee_value'],
                        array(
                            'fee'             => NumberHelper::numberFormatDisplay($aDiscountDesc['limittime']['amount'], '', 1),
                            'currency_unit'   => $sUnit['currency'],
                            'currency_symbol' => '',
                        )
                    ),
                ];
            }

            //短时预约折扣金
            if (isset($aDiscountDesc['short_wait']['amount']) && $aDiscountDesc['short_wait']['amount'] > 0) {
                $fDiscountTotal         += $aDiscountDesc['short_wait']['amount'];
                $aBillDesc['discount'][] = [
                    'value'          => Language::replaceTag(
                        $aEstimateFeeDetail['discount_fee_value'],
                        array(
                            'fee'             => NumberHelper::numberFormatDisplay($aDiscountDesc['short_wait']['amount'], '', 2),
                            'currency_unit'   => $sUnit['currency'],
                            'currency_symbol' => '',
                        )
                    ),
                    'text'           => $aStyleConfig['sps_short_waiting_discount']['text'],
                    'text_color'     => $aStyleConfig['sps_short_waiting_discount']['text_color'],
                    'text_bg_colors' => $aStyleConfig['sps_short_waiting_discount']['text_bg_colors'],
                    'border_color'   => $aStyleConfig['sps_short_waiting_discount']['border_color'],
                ];
            }

            //省钱卡, 已经没了
            if (isset($aDiscountDesc['kcard']['amount']) && $aDiscountDesc['kcard']['amount'] > 0) {
                $fDiscountTotal         += $aDiscountDesc['kcard']['amount'];
                $aBillDesc['discount'][] = [
                    'value'          => Language::replaceTag(
                        $aEstimateFeeDetail['discount_fee_value'],
                        array(
                            'fee'             => NumberHelper::numberFormatDisplay($aDiscountDesc['kcard']['amount'], '', 2),
                            'currency_unit'   => $sUnit['currency'],
                            'currency_symbol' => '',
                        )
                    ),
                    'text'           => $aStyleConfig['saving_card']['text'],
                    'text_color'     => $aStyleConfig['saving_card']['text_color'],
                    'text_bg_colors' => $aStyleConfig['saving_card']['text_bg_colors'],
                ];
            }

            if (!empty($aDiscountDesc['spec_ziyoubao']) &&  $aDiscountDesc['spec_ziyoubao']['deduction_amount'] > 0) {
                $fDiscountTotal         += $aDiscountDesc['spec_ziyoubao']['amount'];
                $_aDescConfig            = $aEstimateFeeDetail['spec_ziyoubao_desc'];
                $aBillDesc['discount'][] = [

                    'name'  => Language::replaceTag(
                        $_aDescConfig['name'],
                        [
                            'mile' => NumberHelper::numberFormatDisplay(
                                $aDiscountDesc['spec_ziyoubao']['deduction_mile'] / 1000.0,
                                '',
                                $_aDescConfig['mile_precision']
                            ),
                        ]
                    ), // 优惠的名称, 显示在左边
                    // 'badge' => '', // 优惠右边的标签,
                    // 'badge_img' => '', // 同上面
                    'value' => Language::replaceTag(
                        $_aDescConfig['value'],
                        [
                            'currency_symbol' => '',
                            'currency_unit'   => $sUnit['currency'],
                            'fee'             => NumberHelper::numberFormatDisplay(
                                $aDiscountDesc['spec_ziyoubao']['amount'],
                                '',
                                $_aDescConfig['fee_precision']
                            ),
                        ]
                    ), // 优惠的价值, 显示在右边

                    // 猜: 花小猪使用
                    // 'text'           => $aStyleConfig['saving_card']['text'],
                    // 'text_color'     => $aStyleConfig['saving_card']['text_color'],
                    // 'text_bg_colors' => $aStyleConfig['saving_card']['text_bg_colors'],
                ];
            }

            //最低消费文案处理
            if ($aBillDesc['extra_info']['is_limit_fee']) {
                if (isset($aBillDesc['discount'])) {   //有折扣，最低消费文案放在应付的说明里
                    if (!isset($aBillDesc['need_pay']['intros'])) {
                        $aBillDesc['need_pay']['intros'] = array();
                    }

                    array_unshift($aBillDesc['need_pay']['intros'], $aBillDesc['extra_info']['limit_fee_intro']);
                } else {    //无折扣，最低消费文案放在实付的说明里
                    if (!isset($aBillDesc['real_pay']['intros'])) {
                        $aBillDesc['real_pay']['intros'] = array();
                    }

                    array_unshift($aBillDesc['real_pay']['intros'], $aBillDesc['extra_info']['limit_fee_intro']);
                }
            }

            //A+品类
            $bIsAPlus = false;
            if (isset($aFeeDetailData['n_tuple']) && BizCommonHorae::isAPlus($aFeeDetailData['n_tuple'])) {
                $bIsAPlus = true;
            }

            //处理多因素一口价的报价有效性
            $bIsMultiFactor = Horae::isMultiFactorFlatRateV2(['combo_type' => $aFeeDetailData['combo_type'], 'count_price_type' => $aFeeDetailData['count_price_type']]);
            if (($bIsMultiFactor || $bIsAPlus) && !empty($aFeeDetailData['estimate_time'])) {
                if (!isset($aBillDesc['real_pay']['intros'])) {
                    $aBillDesc['real_pay']['intros'] = array();
                }

                $sExpireDate = date('H:i:s', $aFeeDetailData['estimate_time'] + self::MULTI_FACTOR_FLAT_RATE_ESTIMATE_EXPIRE);
                $aIntro      = [
                    'value' => Config::text('config_fee_text', 'multi_factor_flat_estimate_expire', ['expire_date' => $sExpireDate]),
                ];

                array_unshift($aBillDesc['real_pay']['intros'], $aIntro);
            }

            //勾选了推荐套餐
            if (isset($this->_aParams['is_select_rec_combo']) && 1 == $this->_aParams['is_select_rec_combo']) {
                unset($aBillDesc['discount']); //删除其他优惠
                $fDiscountTotal = 0;
                $arrRecCombo    = RecComboCache::getRecComboCache($this->_aParams['estimate_id']);

                if (!empty($arrRecCombo)) {
                    $arrDiscount      = $arrRecCombo['discount'];
                    $arrNeedPayDetail = $arrRecCombo['need_pay_detail'];
                    //添加优惠信息
                    $aBillDesc['discount'][] = $arrDiscount;
                    //添加套餐价
                    array_push($aBillDesc['need_pay_detail'], $arrNeedPayDetail);
                    // 总费用=行程费用（起步价+里程费+时常费+其他行程费）+套餐费
                    $aBillDesc['real_pay']['bill_total_fee'] += isset($arrNeedPayDetail['number']) ? $arrNeedPayDetail['number'] : 0;
                    $fDiscountTotal += isset($arrDiscount['number']) ? $arrDiscount['number'] : 0;
                    $aBillDesc['need_pay']['need_pay_total_fee'] += isset($arrNeedPayDetail['number']) ? $arrNeedPayDetail['number'] : 0;
                    $sNeedPayTotalFee   = sprintf('%.2f', $aBillDesc['need_pay']['need_pay_total_fee']); //价格信息统一展示小数点后2位
                    $aEstimateFeeDetail = NuwaConfig::text('config_text', 'estimate_fee_detail');
                    $aBillDesc['need_pay']['need_pay_total_fee'] = sprintf('%s%s', $sNeedPayTotalFee,  $sUnit['currency']);
                    $aBillDesc['need_pay']['total_fee']          = sprintf($aEstimateFeeDetail['need_pay_total_fee'],  $sNeedPayTotalFee,  $sUnit['currency']);
                }
            }

            // 支付宝支付券
            if ($iAlipayCouponAmount > 0) {
                $fAlipayCoupon           = $iAlipayCouponAmount / 100.0;
                $fDiscountTotal         += $fAlipayCoupon;
                $aBillDesc['discount'][] = [
                    'number'    => -$fAlipayCoupon,
                    'name'      => $aEstimateFeeDetail['alipay_coupon_text'],
                    'value'     => Language::replaceTag(
                        $aEstimateFeeDetail['discount_fee_value'],
                        array(
                            'fee'             => NumberHelper::numberFormatDisplay($fAlipayCoupon, '', 2),
                            'currency_unit'   => $sUnit['currency'],
                            'currency_symbol' => '',
                        )
                    ),
                    'sub_items' => [
                        ['name' => $aEstimateFeeDetail['alipay_coupon_policy_desc'],],
                    ],
                ];
            }

            if ($aFeeDetailData[FeeDetailLogic::REBOOK_SERVICE_FEE] > 0) {
                $rebookServiceChargeText        = Language::getDecodedTextFromDcmp('config_text-rebook_service_charge', $this->_aParams['lang']);
                $aBillDesc['need_pay_detail'][] = [
                    'name'  => $rebookServiceChargeText['title'],
                    'value' => Language::replaceTag(
                        $rebookServiceChargeText['content'],
                        [
                            'num'  => FeeUtil::setFloatFormat($aFeeDetailData[FeeDetailLogic::REBOOK_SERVICE_FEE], 2),
                            'unit' => $sUnit['currency'],
                        ]
                    ),
                ];

                $totalFeeWithRebookFee = ($aBillDesc['real_pay']['bill_total_fee'] * 100.0 + $aFeeDetailData[FeeDetailLogic::REBOOK_SERVICE_FEE] * 100.0) / 100.0;
                $aBillDesc['need_pay']['total_fee'] = Language::replaceTag(
                    $aBillDesc['real_pay']['total_fee_desc'],
                    ['total_fee' => $totalFeeWithRebookFee,]
                );
                $fIncreaseTotal += $aFeeDetailData[FeeDetailLogic::REBOOK_SERVICE_FEE];
            }

            //计算实付
            // 存在小数点精度问题
            $fRealPayTotalFee = $aBillDesc['real_pay']['bill_total_fee'];
            if ($fDiscountTotal > 0) {
                $fRealPayTotalFee -= $fDiscountTotal;
            }

            if ($fIncreaseTotal > 0) {
                $fRealPayTotalFee += $fIncreaseTotal;
            }

            if ($fRealPayTotalFee < 0) {
                $fRealPayTotalFee = 0;
            }

            $aRealPayTotalFeeCarpoolUnSuccessFlatPrice[] = $fRealPayTotalFee;

            $sRealPayTotalFee = sprintf('%.2f', $fRealPayTotalFee); //价格信息统一展示小数点后2位

            if ($this->_hitAPlusPriceRange($aFeeDetailData)) {
                $aAPlusRangeConf      = Language::getDecodedTextFromDcmp('config_text-aplus_range_config');
                $aAPlusRangeText      = $aAPlusRangeConf['fee_detail_text'] ?? '';
                $sRealPayTotalFeeText = str_replace('<span>', $aAPlusRangeText . '<span>', $aBillDesc['real_pay']['total_fee']);
                $aBillDesc['real_pay']['total_fee'] = $sRealPayTotalFeeText;
            }

            $sRealPayTotalFeeText = str_replace('${{total_fee}}', $sRealPayTotalFee, $aBillDesc['real_pay']['total_fee']);

            $aBillDesc['real_pay']['total_fee']    = $sRealPayTotalFeeText;
            $aBillDesc['real_pay']['payment_info'] = $this->_getPaymentInfo($aFeeDetailData, $sRealPayTotalFee, $sUnit['currency']); //支付方式info

            //unset item的无用字段
            if ($this->_bFilterLableTitle($aFeeDetailData['n_tuple'], $iPageType)) {
                unset($aBillDesc['label_title']);
            }

            unset($aBillDesc['extra_info']);

            // 读实验
            $aApolloToggle = Apollo::getInstance()->featureToggle('protector_vision',$this->_aExamParams);

            if ($aApolloToggle->allow()) {
                $sHitGroupName = $aApolloToggle->getGroupName();
                // 设置标签
                if (($sHitGroupName == 'treatment_group_1' || $sHitGroupName == 'treatment_group_2') && !empty($aBillDesc['real_pay'])) {
                    // 读配置
                    $aBodyguardConf = NuwaConfig::text('config_text', 'bodyguard_package');
                    if (!empty($aBodyguardConf) && !empty($aBodyguardConf['estimate_fee_detail'])) {
                        $aBillDesc['real_pay']['icon'] = $aBodyguardConf['estimate_fee_detail']['left_icon'];
                    }

                }
            }


            //设置item0
            $aFeeDetailData['fee_desc']['items'][$iIndex] = $aBillDesc;
        }

        // 判断是否是拼成两口价未拼成一口价 需求wiki:http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=687553864
        if (
            BizCommonHorae::isCarpoolUnSuccessFlatPrice($aFeeDetailData['n_tuple'])
            && 2 == count($aFeeDetailData['fee_desc']['items'])
            && 2 == count($aRealPayTotalFeeCarpoolUnSuccessFlatPrice)
        ) {
            $fCarpoolSuccessFee = $aRealPayTotalFeeCarpoolUnSuccessFlatPrice[0];
            $fCarpoolFailFee    = $aRealPayTotalFeeCarpoolUnSuccessFlatPrice[1];
            // 判断是否是两口价合并一口价（比较两个实付金额是否相等）
            if (round($fCarpoolSuccessFee, 2) == round($fCarpoolFailFee, 2)) {
                // intros 增加一句文案
                $oIntrosConfig = Language::getDecodedTextFromDcmp('estimate_dache_anycar-carpool_price_info')['price_time_limit'];
                if (!empty($oIntrosConfig)) {
                    $aFeeDetailData['fee_desc']['items'][0]['real_pay']['intros'][] = $oIntrosConfig;
                    $aFeeDetailData['fee_desc']['items'][1]['real_pay']['intros'][] = $oIntrosConfig;
                }
            }
        }

        $aFeeDetailData['fee_desc']['items'] = array_values($aFeeDetailData['fee_desc']['items']);

        $aFeeDetailData['fee_desc']['url_params'] = $aFeeDetailData['url_params'];

        //处理limo多日订单的账单感详情
        if (!empty($this->_aParams['charge_date']) && !empty($aFeeDetailData['fee_desc']['new_fee_detail'])) {
            $iDay = count(explode(',', $this->_aParams['charge_date']));
            if ($iDay > 1) {
                if (!empty($aFeeDetailData['fee_desc']['new_fee_detail']['real_pay']['real_pay_data'])) {
                    $aRealPayData = $aFeeDetailData['fee_desc']['new_fee_detail']['real_pay']['real_pay_data'];
                    if (!empty($aRealPayData['total_fee'])) {
                        $aFeeDetailData['fee_desc']['new_fee_detail']['real_pay']['total_fee_desc_multi'] = sprintf($aRealPayData['unit']['total_fee_frame'], $aRealPayData['total_fee'] * $iDay, $aRealPayData['unit']['price_unit']);
                    } elseif (!empty($aRealPayData['distance']) && !empty($aRealPayData['time'])) {
                        $aFeeDetailData['fee_desc']['new_fee_detail']['real_pay']['total_fee_desc_multi'] = sprintf($aRealPayData['unit']['total_consume_frame'], $aRealPayData['distance'] * $iDay, $aRealPayData['unit']['dis_unit'], $aRealPayData['time'] * $iDay, $aRealPayData['unit']['time_unit']);
                    }
                }

                //增加多日订单提醒
                $aFeeDetailData['fee_desc']['new_fee_detail']['real_pay']['fee_desc_supplement'] = $aFeeDetailData['fee_desc']['new_fee_detail']['real_pay']['fee_desc_multi_supplement'];
            }
        }

        if (!empty($aFeeDetailDataFeeDescItem0)) {
            array_unshift($aFeeDetailData['fee_desc']['items'], $aFeeDetailDataFeeDescItem0);
        }

        return $aFeeDetailData;
    }

    /**
     * 是否能展示优惠券信息
     * @param $aFeeDetailData
     * @return bool
     */
    private function _canShowCoupon($aFeeDetailData)
    {
        //司乘议价品类不展示优惠券信息
        if (UtilProduct::COMMON_PRODUCT_ID_BARGAIN_CAR == $aFeeDetailData['n_tuple']['business_id']) {
            return false;
        }

        return true;
    }
    /**
     * @desc 通过tag获取券名称
     * @param string $sCustomTag       券标签
     * @param int    $iProductCategory pcId
     * @return bool
     */
    private function _getDiscountName($sCustomTag, $iProductCategory)
    {
        if (empty($sCustomTag)) {
            return '';
        }

        // 取下配置
        $aCouponConfig = NuwaConfig::text('config_text', 'fee_detail_coupon_config');
        if (empty($aCouponConfig) || empty($aCouponConfig[$sCustomTag])) {
            return '';
        }

        // 1. 校验是否限制品类
        if (
            !empty($aCouponConfig[$sCustomTag]['support_pc_id'])
            && !in_array($iProductCategory, $aCouponConfig[$sCustomTag]['support_pc_id'])
        ) {
            return '';
        }

        // 2. 校验是否含有品类专属文案
        if (!empty($aCouponConfig[$sCustomTag]['pc_id_special_name'][$iProductCategory])) {
            return $aCouponConfig[$sCustomTag]['pc_id_special_name'][$iProductCategory];
        }

        return $aCouponConfig[$sCustomTag]['name'];
    }

    /**
     * @desc 过滤lable_title
     * @param array $aNtuple   n_tuple
     * @param int   $iPageType page_type
     * @return bool
     */
    private function _bFilterLableTitle($aNtuple, $iPageType) {
        if (BizCommonHorae::isCarpoolUnsuccRealTimePrice($aNtuple) && self::PAGE_TYPE_6 == $iPageType) {
            return false;
        }

        if (BizCommonHorae::isCarpoolUnSuccessFlatPrice($aNtuple) && self::PAGE_TYPE_6 == $iPageType) {
            return false;
        }

        if (BizCommonHorae::bUnioneStationCarpool($aNtuple) && self::PAGE_TYPE_6 == $iPageType) {
            return false;
        }

        if (BizCommonHorae::isLowPriceCarpool($aNtuple) && self::PAGE_TYPE_6 == $iPageType) {
            return false;
        }

        if (BizCommonHorae::isInterCityDualPrice($aNtuple) && self::PAGE_TYPE_6 == $iPageType) {
            return false;
        }

        return true;
    }

    /**
     * 获取anycar的费用详情.
     * @param array  $aFeeDetailData     data
     * @param string $sLanguage          lang
     * @param array  $aPreferenceProduct p
     * @return array
     */
    private function _formatFeeDetailAnyCar($aFeeDetailData, $sLanguage, $aPreferenceProduct = []) {
        $aAnyCarText   = NuwaConfig::text('config_text', 'anycar', [], $sLanguage);
        $aEstimateText = NuwaConfig::text('config_text', 'mOrderEstimateNew');

        $aMultiInfo = $aFeeDetailData['discount_desc_anycar'];
        if (!empty($aPreferenceProduct)) {
            $aSelectedProducts = AnyCarOrderLogic::getInstance()->getPreferenceProducts($aMultiInfo, $aPreferenceProduct);
        } else {
            $aSelectedProducts = AnyCarOrderLogic::getInstance()->getSelectedEnabledProducts($aMultiInfo);
        }

        $sAnyCarType = AnyCarOrderLogic::getInstance()->getAnyCarType($aMultiInfo);
        $aFeeItems   = [];
        $sUnit       = Language::getUnit($sLanguage);

        if (!empty($aSelectedProducts)) {
            $aFeeDetailList    = [];
            $aDisplayNameGroup = [];
            foreach ($aSelectedProducts as $aItem) {
                $fValue = 0 != $aItem['discount_fee'] ? $aItem['discount_fee'] : $aItem['total_fee'];

                $sGroupKey = \BizCommon\Logics\Anycar\AnyCarCommonLogic::getGroupKey($aItem);
                switch ($sGroupKey) {
                    case AnyCarOrderLogic::PRODUCT_KEY_UNIONE:
                        // unione: 打表计价
                        $sValue = $aAnyCarText['fee_info_unione'];
                        break;
                    case AnyCarOrderLogic::PRODUCT_KEY_CARPOOL:
                        // 一口价
                        $iSeatNum = !empty($aFeeDetailData['carpool_seat_num']) ? $aFeeDetailData['carpool_seat_num'] : 1;
                        $sValue   = Language::replaceTag(
                            $aAnyCarText['carpool_estimate_price'],
                            [
                                'seat_num'        => $iSeatNum,
                                'currency_symbol' => '',
                                'num'             => NumberHelper::numberFormatDisplay($fValue),
                                'currency_unit'   => $sUnit['currency'],
                            ]
                        );
                        //两口价
                        $this->_oCarpoolDualPrice = new OrderCarpoolDualPrice();
                        if ($this->_oCarpoolDualPrice->isCarpoolDualPrice($aFeeDetailData['n_tuple'])) {
                            $this->_isDualPrice = true;
                            list($sPriceDesc, $sCouponDesc) = $this->_oCarpoolDualPrice->anyCarBubbleDetailPage($aItem, $sUnit['currency']);
                            $sValue = $sPriceDesc;
                        }
                        break;
                    default:
                        $sValue = $this->_getFeeValue($fValue, $sUnit);
                        break;
                }

                $sDisplayName = OrderComLogic::getProductDisplayNameByCarLevel(
                    $aItem['require_level'],
                    $aItem['combo_type'],
                    OrderSystem::TYPE_ANYCAR_FAST == $sAnyCarType
                );

                if (!empty($aItem['level_type'])) {
                    $aBusinessNameConfig = Language::getDecodedTextFromDcmp('config_anycar-business_name_text');
                    $sDisplayName        = $aBusinessNameConfig[$sGroupKey];
                }

                $aDisplayNameGroup[] = $sDisplayName;

                $aSubItems = [];

                // 春节服务费
                if (isset($aItem['red_packet']) && $aItem['red_packet'] > 0) {
                    $aSubItems[] = [
                        'name'  => '',
                        'value' => Language::replaceTag(
                            $aAnyCarText['anycar_spring_festival_red_packet_desc_for_detail'],
                            [
                                'red_packet' => $aItem['red_packet'],
                            ]
                        ),
                    ];
                }

                // 动调加价
                $fDynamicTimes = isset($aItem['dynamic_times']) ? (float)($aItem['dynamic_times']) : 0;
                $bIfUseTimes   = $aItem['if_use_times'] ?? false;
                if ($bIfUseTimes && !empty($fDynamicTimes)) {
                    $aSubItems[] = [
                        'name'  => '',
                        'value' => Language::replaceTag(
                            $aAnyCarText['dynamic_multiple_msg'],
                            ['num' => $fDynamicTimes,]
                        ),
                    ];
                }

                // 第三方折扣信息
                if (isset($aItem['trip_cloud_discount_fee']) && 0 != $aItem['trip_cloud_discount_fee']) {
                    $aSubItems[] = [
                        'name'  => '',
                        'value' => Language::replaceTag(
                            $aAnyCarText['anycar_discount_msg_' . $aItem['business_id']],
                            [
                                'discount_rate' => sprintf('%.2f', 10 * $aItem['trip_cloud_discount_fee_rate']),
                                'discount_fee'  => sprintf('%.2f', $aItem['trip_cloud_discount_fee']),
                            ]
                        ),
                        'hint'  => [
                            'bind_key' => 'value',
                            'content'  => $aAnyCarText['anycar_discount_hint_' . $aItem['business_id']],
                        ],
                    ];
                }

                // 优惠项统计
                $aDiscountDesc = [
                    'total' => 0.0,
                    'num'   => 0,
                ];

                // 计算总优惠价格
                if (AnyCarOrderLogic::PRODUCT_KEY_CARPOOL == $sGroupKey && $this->_isDualPrice) {
                    if (!empty($sCouponDesc)) {
                        $aSubItems[] = [
                            'name'  => '',
                            'value' => $sCouponDesc,
                        ];
                    }
                } elseif (!empty($aItem['discount_desc'])) {
                    foreach ($aItem['discount_desc'] as $aDiscountItem) {
                        if (($fDiscount = abs($aDiscountItem['amount'] ?? 0)) > 0 && 'infofee' != $aDiscountItem['type']) {
                            ++$aDiscountDesc['num'];
                            $aDiscountDesc['total'] += $fDiscount;
                        }

                        if (!empty($aDiscountItem['type']) && !empty($aDiscountItem['amount']) && 'citycard' === $aDiscountItem['type'] && $aDiscountItem['amount'] > 0) {
                            $aSubItems[] = [
                                'name'  => '',
                                'value' => Language::replaceTag(
                                    $aAnyCarText['citycard_msg'],
                                    [
                                        'currency_symbol' => '',
                                        'num'             => $aDiscountItem['amount'],
                                        'currency_unit'   => $sUnit['currency'],
                                    ]
                                ),
                            ];
                        }

                        if (!empty($aDiscountItem['type']) && !empty($aDiscountItem['amount']) && 'backcard' === $aDiscountItem['type'] && $aDiscountItem['amount'] > 0) {
                            $aSubItems[] = [
                                'name'  => '',
                                'value' => Language::replaceTag(
                                    $aAnyCarText['backcard_msg'],
                                    [
                                        'currency_symbol' => '',
                                        'num'             => $aDiscountItem['amount'],
                                        'currency_unit'   => $sUnit['currency'],
                                    ]
                                ),
                            ];
                        }

                        if (!empty($aDiscountItem['type']) && !empty($aDiscountItem['amount']) && 'coupon' === $aDiscountItem['type'] && $aDiscountItem['amount'] > 0) {
                            //是否触发了溢价保护, 保护+劵
                            if (-$aItem['dynamic_member_reduce'] > 0 && Constants\OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR == $aItem['product_id']) {
                                $sMemberProtectCoupText = $aEstimateText['member_protect_coupon_msg_lux'];
                                $sLevelName  = $aItem['level_name'];
                                $aSubItems[] = [
                                    'name'  => '',
                                    'value' => Language::replaceTag(
                                        $sMemberProtectCoupText,
                                        [
                                            'level_name'      => $sLevelName,
                                            'priv_name'       => $aItem['privileges']['dpa']['name'],
                                            'currency_symbol' => '',
                                            'num'             => $aDiscountItem['amount'] - $aItem['dynamic_member_reduce'],
                                            'currency_unit'   => $sUnit['currency'],
                                        ]
                                    ),
                                ];
                            } else {
                                if (
                                    TripcloudProduct::isTripcloudByBusinessID($aItem['business_id'])
                                    && isset($aItem['trip_cloud_discount_fee'])
                                    && 0 != $aItem['trip_cloud_discount_fee']
                                ) {
                                    $fCouponAmount = isset($aItem['coupon_amount']) ? (float)($aItem['coupon_amount']) : 0;
                                    if ($fCouponAmount > $aItem['total_fee']) {
                                        $fCouponAmount = $aItem['total_fee'];
                                    }

                                    $aSubItems[] = [
                                        'name'  => '',
                                        'value' => Language::replaceTag(
                                            $aAnyCarText['anycar_coupon_msg'],
                                            [
                                                'currency_symbol' => '',
                                                'num'             => sprintf('%.2f', -$fCouponAmount),
                                                'currency_unit'   => $sUnit['currency'],
                                            ]
                                        ),
                                    ];
                                } else {
                                    $aSubItems[] = [
                                        'name'  => '',
                                        'value' => Language::replaceTag(
                                            $aAnyCarText['coupon_msg'],
                                            [
                                                'currency_symbol' => '',
                                                'num'             => $aDiscountItem['amount'],
                                                'currency_unit'   => $sUnit['currency'],
                                            ]
                                        ),
                                    ];
                                }
                            }
                        } else {
                            //是否触发了溢价保护
                            if (-$aItem['dynamic_member_reduce'] > 0 && Constants\OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR == $aItem['product_id']) {
                                $sMemberProtectText = $aEstimateText['member_protect_msg_lux'];
                                $sLevelName         = $aItem['level_name'];
                                $aSubItems[]        = [
                                    'name'  => '',
                                    'value' => Language::replaceTag(
                                        $sMemberProtectText,
                                        [
                                            'level_name'      => $sLevelName,
                                            'priv_name'       => $aItem['privileges']['dpa']['name'],
                                            'currency_symbol' => '',
                                            'amount'          => -$aItem['dynamic_member_reduce'],
                                            'currency_unit'   => $sUnit['currency'],
                                        ]
                                    ),
                                ];
                            }
                        }

                        if (!empty($aDiscountItem['type']) && !empty($aDiscountItem['amount']) && 'reward' === $aDiscountItem['type'] && $aDiscountItem['amount'] > 0) {
                            $aSubItems[] = [
                                'name'  => '',
                                'value' => Language::replaceTag(
                                    $aAnyCarText['reward_msg'],
                                    [
                                        'currency_symbol' => '',
                                        'num'             => $aDiscountItem['amount'],
                                        'currency_unit'   => $sUnit['currency'],
                                    ]
                                ),
                            ];
                        }

                        if (!empty($aDiscountItem['type']) && !empty($aDiscountItem['amount']) && 'student' === $aDiscountItem['type'] && $aDiscountItem['amount'] > 0) {
                            $aSubItems[] = [
                                'name'  => '',
                                'value' => Language::replaceTag(
                                    $aAnyCarText['student_msg'],
                                    [
                                        'currency_symbol' => '',
                                        'num'             => $aDiscountItem['amount'],
                                        'currency_unit'   => $sUnit['currency'],
                                    ]
                                ),
                            ];
                        }

                        if (!empty($aDiscountItem['type']) && !empty($aDiscountItem['amount']) && 'month' === $aDiscountItem['type'] && $aDiscountItem['amount'] > 0) {
                            $aSubItems[] = [
                                'name'  => '',
                                'value' => Language::replaceTag(
                                    $aAnyCarText['month_msg'],
                                    [
                                        'currency_symbol' => '',
                                        'num'             => $aDiscountItem['amount'],
                                        'currency_unit'   => $sUnit['currency'],
                                    ]
                                ),
                            ];
                        }

                        if (!empty($aDiscountItem['type']) && !empty($aDiscountItem['amount']) && 'shake' === $aDiscountItem['type'] && $aDiscountItem['amount'] > 0) {
                            $aSubItems[] = [
                                'name'  => '',
                                'value' => Language::replaceTag(
                                    $aAnyCarText['shake_msg'],
                                    [
                                        'currency_symbol' => '',
                                        'num'             => $aDiscountItem['amount'],
                                        'currency_unit'   => $sUnit['currency'],
                                    ]
                                ),
                            ];
                        }

                        if (!empty($aDiscountItem['type']) && !empty($aDiscountItem['amount']) && 'infofee' === $aDiscountItem['type'] && $aDiscountItem['amount'] > 0) {
                            $aSubItems[] = [
                                'name'  => '',
                                'value' => Language::replaceTag(
                                    $aAnyCarText['infofee_msg'],
                                    [
                                        'num' => $aDiscountItem['amount'],
                                    ]
                                ),
                                'hint'  => [
                                    'link' => $aAnyCarText['infofee_msg_url'],
                                ],
                            ];
                        }
                    }
                } else {
                    //是否触发了溢价保护
                    if (-$aItem['dynamic_member_reduce'] > 0 && Constants\OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR == $aItem['product_id']) {
                        $sMemberProtectText = $aEstimateText['member_protect_msg_lux'];
                        $sLevelName         = $aItem['level_name'];
                        $aSubItems[]        = [
                            'name'  => '',
                            'value' => Language::replaceTag(
                                $sMemberProtectText,
                                [
                                    'level_name'      => $sLevelName,
                                    'priv_name'       => $aItem['privileges']['dpa']['name'],
                                    'currency_symbol' => '',
                                    'amount'          => -$aItem['dynamic_member_reduce'],
                                    'currency_unit'   => $sUnit['currency'],
                                ]
                            ),
                        ];
                    }

                    // 优惠券信息
                    if (
                        TripcloudProduct::isTripcloudByBusinessID($aItem['business_id'])
                        && isset($aItem['trip_cloud_discount_fee'])
                        && 0 != $aItem['trip_cloud_discount_fee']
                    ) {
                        $fCouponAmount = isset($aItem['coupon_amount']) ? (float)($aItem['coupon_amount']) : 0;
                        if ($fCouponAmount > $aItem['total_fee']) {
                            $fCouponAmount = $aItem['total_fee'];
                        }

                        if (!empty($fCouponAmount)) {
                            $aSubItems[] = [
                                'name'  => '',
                                'value' => Language::replaceTag(
                                    $aAnyCarText['anycar_coupon_msg'],
                                    [
                                        'currency_symbol' => '',
                                        'num'             => sprintf('%.2f', $fCouponAmount),
                                        'currency_unit'   => $sUnit['currency'],
                                    ]
                                ),
                            ];
                        }
                    } else {
                        $fCouponAmount = isset($aItem['coupon_amount']) ? (float)($aItem['coupon_amount']) : 0;
                        if (!empty($fCouponAmount)) {
                            $aSubItems[] = [
                                'name'  => '',
                                'value' => Language::replaceTag(
                                    $aAnyCarText['coupon_msg'],
                                    [
                                        'currency_symbol' => '',
                                        'num'             => $fCouponAmount,
                                        'currency_unit'   => $sUnit['currency'],
                                    ]
                                ),
                            ];
                        }
                    }
                }

                $aFeeDetailList[] = [
                    'name'      => $sDisplayName,
                    'value'     => $sValue,
                    'sub_items' => $aSubItems,
                ];
            }

            foreach ($aFeeDetailList as $iIndex => $aDetail) {
                $aItem = [];
                if (0 === $iIndex) {
                    $aItem['sub_title'] = $aAnyCarText['fee_detail_sub_title'];
                }

                $aItem['need_pay_detail'][] = $aDetail;
                $aFeeItems[] = $aItem;
            }

            $sProductIntroKey = 'anycar_product_desc_v2';
            if (Constants\OrderSystem::TYPE_ANYCAR_FIRST_CLASS == $sAnyCarType) {
                $sProductIntroKey = 'luxury_anycar_product_desc';
            }

            // 可呼叫快车、优享、出租车
            $sAnyCarProductDesc = Language::replaceTag(
                $aAnyCarText[$sProductIntroKey],
                [
                    'display_name' => implode('、', $aDisplayNameGroup),
                ]
            );
        } else {
            $sAnyCarProductDesc = $aAnyCarText['anycar_product_desc_disabled'];
            $aFeeItems[]        = [
                'sub_title'       => $aAnyCarText['fee_detail_sub_title'],
                'need_pay_detail' => [
                    [
                        'intros' => [
                            [
                                'value' => $aAnyCarText['fee_detail_disabled'],
                            ],
                        ],
                    ],
                ],
            ];
        }

        return $aEstimatedFeeDetail = [
            'page_title'        => $aAnyCarText['fee_detail_title'],
            'url_params'        => $aFeeDetailData['url_params'],
            'title'             => '',
            'price_rule_hidden' => 1,   // 是否隐藏 "计价规则详情"
            'product_intro'     => $sAnyCarProductDesc,
            'items'             => $aFeeItems,
        ];
    }

    /**
     * 获取费用项 例如:10.5元.
     *
     * @param float  $fAmount Amount
     * @param string $sUnit   Unit
     *
     * @return string
     */
    private function _getFeeValue(float $fAmount, $sUnit) {
        $aEstimateText = NuwaConfig::text('config_text', 'mOrderEstimateNew');
        return Language::replaceTag(
            $aEstimateText['fee_info_v2'],
            [
                'currency_symbol' => '',
                'fee'             => NumberHelper::numberFormatDisplay($fAmount),
                'currency_unit'   => $sUnit['currency'],
            ]
        );
    }

    /**
     * @param array $aNTuple n元组
     * @return array|null[]
     * @throws Exception e
     */
    private function _getAllQuestions($aNTuple) {
        $aToggleData         = $aNTuple;
        $aToggleData['lang'] = Language::getDefaultLanguage();
        $aToggleData['car_level'] = $aToggleData['require_level'];

        $oApollo       = new NuwaApollo();
        $oApolloResult = $oApollo->featureToggle('fee_detail_FAQ_name', $aToggleData);
        $bAllow        = $oApolloResult->allow();
        $sConfigName   = null;
        if ($bAllow) {
            $sConfigName = $oApolloResult->getParameter('key', null);
        }

        $aConfig = [];
        if (!empty($sConfigName)) {
            $aConfig = ApolloHelper::getConfigContent('pre_estimate_fee_detail_FAQ', $sConfigName);
        }

        if (empty($aConfig)) {
            return ['all_question' => null];
        }

        return $aConfig;
    }

    /**
     * @param string $sProductCategory PoductCategory
     * @return array|null[]
     * @throws Exception e
     */
    private function _getProductAdvantages($sProductCategory) {
        //通过key进入dcmp获得字段
        $aConfig = Language::getDecodedTextFromDcmp('config_fee_text-fee_detail_product_advantage');
        if (empty($aConfig)) {
            return [];
        }

        $aConfig = $aConfig[$sProductCategory];
        if (empty($aConfig)) {
            return [];
        }

        return $aConfig;
    }

    /**
     * @param string $sProductCategory PoductCategory
     * @return array|null[]
     * @throws Exception e
     */
    private function _getDiscountInfo($sProductCategory) {
        //通过key进入dcmp获得字段
        $aConfig = Language::getDecodedTextFromDcmp('config_fee_text-fee_detail_discount_info');
        if (empty($aConfig)) {
            return [];
        }

        $aConfig = $aConfig[$sProductCategory];
        if (empty($aConfig)) {
            return [];
        }

        return $aConfig;
    }

    /**
     * @param string $sProductCategory PoductCategory
     * @return string|''
     * @throws Exception e
     */
    private function _getSpaciousPageTitle($sProductCategory) {
        $aConfig = Language::getDecodedTextFromDcmp('config_fee_text-fee_detail_pag_title');
        if (empty($aConfig)) {
            return '';
        }

        $aConfig = $aConfig[$sProductCategory];
        if (empty($aConfig)) {
            return '';
        }

        return $aConfig['page_title'];
    }

    /**
     * @param string $sProductCategory PoductCategory
     * @return array|null[]
     * @throws Exception e
     */
    private function _getBackgroundColor($sProductCategory) {
        //通过key进入dcmp获得字段
        $aConfig = Language::getDecodedTextFromDcmp('config_fee_text-fee_detail_bg_colors');
        if (empty($aConfig)) {
            return [];
        }

        $aConfig = $aConfig[$sProductCategory];
        if (empty($aConfig)) {
            return [];
        }

        return $aConfig;
    }

    /**
     * @param string $sProductCategory PoductCategory
     * @return string|''
     * @throws Exception e
     */
    private function _getTitileBar($sProductCategory) {
        //通过key进入dcmp获得字段
        $aConfig = Language::getDecodedTextFromDcmp('config_fee_text-fee_title_bar_colors');
        if (empty($aConfig)) {
            return '';
        }

        $aConfig = $aConfig[$sProductCategory];
        if (empty($aConfig)) {
            return '';
        }

        return $aConfig;
    }

    /**
     * @param array $aFeeDetailData aFeeDetailData
     * @return array
     */
    private function _getFeeDetailResource($aFeeDetailData) {
        //详情页添加服务项
        $oEstimateFeeDetailLogic = new EstimateFeeDetailLogic($aFeeDetailData);
        $oEstimateFeeDetailLogic->setPid($this->_aParams['pid']);
        $aConfig['service_item'] = $oEstimateFeeDetailLogic->getServiceItem();
        $aToggleData         = $aFeeDetailData['n_tuple'];
        $aToggleData['city'] = (int)$aFeeDetailData['area'] ?? 0;
        $oApollo       = new NuwaApollo();
        $oApolloResult = $oApollo->featureToggle('fee_detail_resource_switch', $aToggleData);
        $bAllow        = $oApolloResult->allow();
        $sConfigName   = null;
        $sConfigNameProductCategory = null;
        if ($bAllow) {
            $sConfigName = $oApolloResult->getParameter('file_name', null);
        }

        if (!empty($aFeeDetailData['product_category'])) {
            $aToggleProductCategoryData['product_category'] = $aFeeDetailData['product_category'];
            $aToggleProductCategoryData['city'] = (int)$aFeeDetailData['area'] ?? 0;
            $oApolloResult = $oApollo->featureToggle('fee_detail_resource_switch', $aToggleProductCategoryData);
            $bAllow        = $oApolloResult->allow();
            if ($bAllow) {
                $sConfigNameProductCategory = $oApolloResult->getParameter('file_name', null);
            }
        }

        if (!empty($sConfigNameProductCategory)) {
            $sConfigName = $sConfigNameProductCategory;
        }

        // 获取特殊业务费用详情配置
        $sConfigName   = $this->_getSpecialFeeDetailConfig($aFeeDetailData['n_tuple'], $sConfigName);
        $aDetailConfig = [];
        if (!empty($sConfigName)) {
            $sConfigName   = $sConfigName . '_' . $this->_aParams['lang'];
            $aDetailConfig = ApolloHelper::getConfigContent('estimate_fee_detail_config', $sConfigName);
        }

        if (empty($aDetailConfig['price_rule_url'])) {
            unset($aDetailConfig['price_rule_url']);
        }

        $aConfig      = array_merge($aDetailConfig, $aConfig);

        $sFeeEstimate = NuwaConfig::text('estimate_form_v3', 'fee_estimate');
        // 根据pcid 从预估表单物料配置 获取 product_title 进行兜底
        if (!empty($aFeeDetailData['product_category']) && empty($aConfig['product_title'])) {
            $sCarTitle = Util::getCarTitleByProductCategory($aFeeDetailData['product_category']);
            $aConfig['product_title'] = $sCarTitle . $sFeeEstimate;
        }

        if (!empty($aConfig)) {
            $area = $aFeeDetailData['area'];
            $lang =  $this->_aParams['lang'];
            $aConfig['product_title'] = \Dukang\PropertyConst\ProductCenter\ProductMaterial::FormatProductMaterial($aConfig['product_title'],$area,$lang);
        }

        // init 计价盒子
        TaxiPricingSingle::getInstance()->init(
            [
                'key'           => $this->_aParams['uid'],
                'pid'           => $this->_aParams['pid'],
                'phone'         => $this->_aParams['phone'],
                'app_version'   => $this->_aParams['app_version'],
                'access_key_id' => $this->_aParams['access_key_id'],
                'lang'          => $this->_aParams['lang'],
                'city'          => $aFeeDetailData['area'],
                'county_id'     => $aFeeDetailData['county_id'],
                'tab_id'        => $this->_aParams['tab_id'],
                'page_type'     => $this->_aParams['page_type'] ?? 0,
            ]
        );
        $iProductCategory = (new ProductCategory())->getProductCategoryByNTuple($aFeeDetailData['n_tuple']);
        if (TaxiPricingSingle::getInstance()->judgePcIdInBox($iProductCategory)) {
            $aConfig['product_desc'] = TaxiPricingSingle::getInstance()->getCarTitle($iProductCategory);
        }

        // 出租车一车两价品类名可配置化
        if ((!TaxiPricingSingle::getInstance()->judgePcIdInBox($iProductCategory) && TaxiCarType::isTaxiMarketingPrice($aFeeDetailData['n_tuple']))
            || (OrderEstimatePcId::EstimatePcIdFastTaxi == $aFeeDetailData['product_category'] && 'zh-CN' == $this->_aParams['lang'])
        ) {
            $iProductCategory = (new ProductCategory())->getProductCategoryByNTuple($aFeeDetailData['n_tuple']);
            $sSearchStr       = strstr($aConfig['product_title'], '-', true);

            $sProductName
                = TaxiCarType::getTaxiMarketingPriceProductName($aFeeDetailData['area'], $iProductCategory);

            $aConfig['product_title'] = str_replace($sSearchStr, $sProductName, $aConfig['product_title']);
            $aConfig['product_desc']  = str_replace($sSearchStr, $sProductName, $aConfig['product_desc']);

            // 当未配置 $aConfig['product_detail'] 时，需为 [] 才不会引发端上展示问题，因此做特殊处理
            if (count($aConfig['product_detail']) > 0) {
                $aConfig['product_detail'][0]['title']
                    = str_replace($sSearchStr, $sProductName, $aConfig['product_detail'][0]['title']);

                $aConfig['product_detail'][0]['text']
                    = str_replace($sSearchStr, $sProductName, $aConfig['product_detail'][0]['text']);
            }

            // 如果是补天mvp三方需求，需要拼上相关的service_provider证照内容
            $aServiceProvider = TaxiCarType::getTaxiServiceProvider($aFeeDetailData['area'], $iProductCategory);
            if (!empty($aServiceProvider)) {
                $aConfig['service_provider'] = $aServiceProvider;
            }
        }

        if ($this->_hitAPlusPriceRange($aFeeDetailData)) {
            // 命中特快range模式
            $aAPlusRangeConf = Language::getDecodedTextFromDcmp('config_text-aplus_range_config');
            $aConfig['product_detail'][] = $aAPlusRangeConf['product_detail'];
        }

        // 区县维度命名
        $sProductNameCounty = Util::getCarTitleByProductCategoryCountyId(
            (int)$aFeeDetailData['product_category'],
            $aFeeDetailData['county_id'],
            $aFeeDetailData['county_name'],
            $this->_aParams['lang']
        );

        if (!empty($sProductNameCounty)) {
            $aConfig['product_title'] = $sProductNameCounty . $sFeeEstimate;
        }

        // 车型图
        $sIconConfig = $this->_getTopIcon((int)$aFeeDetailData['area'], $aFeeDetailData['product_category'], $this->_aParams['lang']);
        if (!empty($sIconConfig)) {
            $aConfig['car_icon'] = $sIconConfig;
        }

        return $aConfig;
    }

    /**
     * 特殊业务费用详情配置
     *
     * @param array  $aNTuple     N 元组
     * @param string $sConfigName sConfigName
     *
     * @return string
     */
    private function _getSpecialFeeDetailConfig($aNTuple, $sConfigName) {
        // 地铁接驳车配置
        if (Constants\OrderSystem::PRODUCT_ID_FAST_CAR == $aNTuple['product_id'] && \BizCommon\Constants\OrderNTuple::CARPOOL_TYPE_STATION == $aNTuple['carpool_type'] && 5 == $aNTuple['route_type']) {
            return 'metro_fee_detail';
        }

        return $sConfigName;
    }

    /**
     * 过滤费用明细项
     * @param array $aFeeDetailData 费用明细
     * @param array $aOrderId       订单id
     * @return mixed
     */
    private function _feeItemFilter($aFeeDetailData, $aOrderId)
    {
        $aSubTitle = \BizLib\Config::text('config_fee_text', 'estimate_fee_detail_sub_title');

        //拼成乐首页需要屏蔽掉未拼成的费用项
        if (BizCommonHorae::isLowPriceCarpool($aFeeDetailData['n_tuple']) && !BizCommonHorae::isLowPriceCarpoolV2($aFeeDetailData['n_tuple'])) {
            foreach ($aFeeDetailData['fee_desc']['items'] as $iIndex => $aShadow) {
                $sTitle = $aSubTitle['carpool'] ?? '';
                switch ($aShadow['fee_type']) {
                    case self::FEE_TYPE_CARPOOL_LOW_PRICE:
                        $sTitle = $aSubTitle['carpool_success'];
                        break;
                    case self::FEE_TYPE_CARPOOL_GO_CARD:
                        // no break
                    case self::FEE_TYPE_POOL_IN_TRIP:
                        $sTitle = $aSubTitle['carpool_fail'];
                        break;
                    default:
                        break;
                }

                $aFeeDetailData['fee_desc']['items'][$iIndex]['label_title'] = $sTitle;
            }

            //没有oid 表示为首页的费用明细
            if (empty($aOrderId['oid'])) {
                // 预估详情页
                $oFeatureToggle = (new NuwaApollo())->featureToggle(
                    'multi_fail_fee',
                    [
                        'order_id'     => $aOrderId['oid'],
                        'carpool_type' => $aFeeDetailData['n_tuple']['carpool_type'],
                    ]
                );

                if (!$oFeatureToggle->allow()) {
                    // 这个ab全量了, 不会走到这个分支
                    //dcmp配置读取失败时，unset掉第二项（未拼成费用）
                    if (empty($aSubTitle['carpool_fail'])) {
                        unset($aFeeDetailData['fee_desc']['items'][1]);
                    } else {
                        foreach ($aFeeDetailData['fee_desc']['items'] as $index => $item) {
                            if ($aSubTitle['carpool_fail'] == $item['sub_title']) {
                                unset($aFeeDetailData['fee_desc']['items'][$index]);
                                break;
                            }
                        }
                    }
                } else {
                    $aFeeDetailData = $this->_unsetFeeDetail($aFeeDetailData, self::FEE_TYPE_POOL_IN_TRIP);
                }
            } else {
                $aFeeDetailData = $this->_unsetFeeDetail($aFeeDetailData, self::FEE_TYPE_CARPOOL_GO_CARD);
            }
        } elseif (BizCommonHorae::isLowPriceCarpoolV2($aFeeDetailData['n_tuple'])) {
            // 给每个tab赋值一个显示的label_title
            foreach ($aFeeDetailData['fee_desc']['items'] as $iIndex => $aShadow) {
                $sTitle = $aSubTitle['pincheche_no_partner']; // 默认"未拼到人"
                if ($aShadow['option']['pool_num'] > 0) {
                    $sTitle = Language::replaceTag(
                        $aSubTitle['pincheche_pool'],
                        ['num' => $aShadow['option']['pool_num']]
                    );
                }

                $aFeeDetailData['fee_desc']['items'][$iIndex]['label_title'] = $sTitle;
            }

            if (self::TAB_REQUIRED_FULL == $this->_aParams['tab_required']) {
                // 只需要 "拼满" 的价格信息, 此时预估详情页只需要展示拼满的页面即可
                // 目前对拼满的定义为 pool_num == 2 && seat_num == 1
                $iIndex = -1;
                foreach ($aFeeDetailData['fee_desc']['items'] as $idx => $value) {
                    $option = $value['option'] ?? ['pool_num' => -1, 'seat_num' => -1];
                    if (2 == $option['pool_num'] && 1 == $option['seat_num']) {
                        $iIndex = $idx;
                    }
                }

                if ($iIndex >= 0) {
                    $aFeeDetailData['fee_desc']['items'] = [
                        $aFeeDetailData['fee_desc']['items'][$iIndex],
                    ];
                }

                return $aFeeDetailData;
            }

            if (self::TAB_REQUIRED_ONE_SEAT == $this->_aParams['tab_required']) {
                // 只需要 "1座拼到1人" 的价格信息
                $iIndex = -1;
                foreach ($aFeeDetailData['fee_desc']['items'] as $idx => $value) {
                    $option = $value['option'] ?? ['pool_num' => -1, 'seat_num' => -1];
                    if (1 == $option['pool_num'] && 1 == $option['seat_num']) {
                        $iIndex = $idx;
                    }
                }

                if ($iIndex >= 0) {
                    $aFeeDetailData['fee_desc']['items'] = [
                        $aFeeDetailData['fee_desc']['items'][$iIndex],
                    ];
                }

                return $aFeeDetailData;
            }

            // 预估页面 - 费用详情页
            // 只展示 拼到人 及 未拼到人且有畅拼卡 的情况
            // // 需求PRD：http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=542813623
            if (self::DETAIL_STAGE_PRE == $this->_aParams['stage']) {
                // 在发单前打开详情页和在等待应答阶段打开, 看到的内容是不一样的
                // 未拼到人对应 pool_num == 0
                $aFeeItems = [];
                foreach ($aFeeDetailData['fee_desc']['items'] as $idx => $value) {
                    $option = $value['option'] ?? ['pool_num' => 0, 'seat_num' => 0];
                    if (0 != $option['pool_num']) {
                        $aFeeItems[] = $value;
                    }

                    if (0 == $option['pool_num'] && !empty($option['with_commute_card'])) {
                        $aFeeItems[] = $value;
                    }
                }

                $aFeeDetailData['fee_desc']['items'] = $aFeeItems;
            }

            // 等待应答 - 费用详情页
            // 只展示 拼到人 及 未拼到人且没有畅拼卡的情况，即 不展示畅拼卡费用感知
            // 需求PRD：http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=542813623
            if (self::DETAIL_STAGE_AFTER == $this->_aParams['stage']) {
                $aFeeItems = [];
                foreach ($aFeeDetailData['fee_desc']['items'] as $idx => $value) {
                    $option = $value['option'] ?? ['pool_num' => 0, 'seat_num' => 0];
                    if (0 != $option['pool_num']) {
                        $aFeeItems[] = $value;
                    }

                    if (0 == $option['pool_num'] && empty($option['with_commute_card'])) {
                        $aFeeItems[] = $value;
                    }
                }

                $aFeeDetailData['fee_desc']['items'] = $aFeeItems;
            }

            // 按照拼到人数排序
            // 由于价格和优惠的对应是利用array的index对齐, 所以存在错位, 属于坑
            // 这里将优惠挂在每个item下，省去排序后的对应
            usort(
                $aFeeDetailData['fee_desc']['items'],
                function ($e0, $e1) {
                    $o0 = $e0['option'];
                    $o1 = $e1['option'];
                    if ($o0['pool_num'] == $o1['pool_num']) {
                        return 0;
                    }

                    return ($o0['pool_num'] > $o1['pool_num']) ? -1 : 1;
                }
            );
        }

        //两口价
        if (BizCommonHorae::isInterCityDualPrice($aFeeDetailData['n_tuple'])) {
            foreach ($aFeeDetailData['fee_desc']['items'] as $iIndex => $aShadow) {
                if ($aShadow['option']['is_carpool_success']) {
                    $sTitle = $aSubTitle['carpool_success'];
                } else {
                    $sTitle = $aSubTitle['carpool_fail'];
                }

                $aFeeDetailData['fee_desc']['items'][$iIndex]['label_title'] = $sTitle;
            }
        }

        return $aFeeDetailData;
    }

    /**
     * @param array $aFeeDetailData fee_detail_data
     * @param int   $iFeeType       fee_type
     * @return mixed
     */
    private function _unsetFeeDetail($aFeeDetailData, $iFeeType) {
        foreach ($aFeeDetailData['fee_desc']['items'] as $index => $item) {
            if ($iFeeType == $item['fee_type']) {
                unset($aFeeDetailData['fee_desc']['items'][$index]);
                break;
            }
        }

        foreach ($aFeeDetailData['discount_desc'] as $key => $value) {
            if ($iFeeType == $key) {
                unset($aFeeDetailData['discount_desc'][$key]);
                break;
            }
        }

        return $aFeeDetailData;
    }

    /**
     * @param array $aOrderId order_id
     * @return array
     */
    private function _loadEstimateMetaFromDos($aOrderId) {
        if (empty($aOrderId['oid']) || empty($aOrderId['district'])) {
            return array('', 1);
        }

        $oDosClient         = new DosClient();
        $aOrderInfo         = $oDosClient->getOrderInfo($aOrderId['oid'], $aOrderId['district']);
        $sEstimateId        = $aOrderInfo['result']['order_info']['estimate_id'] ?? '';
        $iCarpoolRequireNum = $aOrderInfo['result']['order_info']['carpool_require_num'] ?? 1;
        // 拼成乐单勾6.0订单取子单的estimate_id和carpool_require_num
        if (\BizCommon\Utils\Horae::isAnycarOnlyLowPriceCarpool($aOrderInfo['result']['order_info'])) {
            $aExtendFeature       = json_decode($aOrderInfo['result']['order_info']['extend_feature'], true);
            $aMultiRequireProduct = $aExtendFeature['multi_require_product'] ?? [];
            foreach ($aMultiRequireProduct as $aProduct) {
                $sEstimateId        = $aProduct['estimate_id'];
                $iCarpoolRequireNum = $aProduct['carpool_require_num'] ?? 1;
                break;
            }
        }

        return array($sEstimateId, $iCarpoolRequireNum);
    }

    /**
     * 获取支付方式
     * @param array  $aFeeDetailData   detailData
     * @param string $sRealPayTotalFee totalFee
     * @param string $sUnit            unit
     * @return array
     */
    private function _getPaymentInfo($aFeeDetailData, $sRealPayTotalFee, $sUnit) {
        $aPayInfoConfig = NuwaConfig::text('payment_type', 'mixed_fee_msg');
        if ($aFeeDetailData['is_mixed_payment']) {
            $aMixPaymentInfo = json_decode($aFeeDetailData['mixed_payment_info'], true);
            $fDeductFee      = sprintf('%.2f', $aMixPaymentInfo['deduct_fee']); //企业支付的价格
            $fPersonalFee    = sprintf('%.2f', $sRealPayTotalFee - $fDeductFee); //个人支付的价格
            $fPersonalFee    = $fPersonalFee > 0 ? $fPersonalFee : 0;
            $aMixSubItems    = [
                [
                    'font_color' => $aPayInfoConfig['personal_pay_color'] ?? '',
                    'name'       => $aPayInfoConfig['payment_type_' . Order::NOT_BUSINESS_USER] ?? '',
                    'value'      => $this->_formatFeeMsgWithSymbol('default_estimate_v2', $fPersonalFee, $sUnit),
                ],
                [
                    'font_color' => $aPayInfoConfig['business_pay_color'] ?? '',
                    'name'       => $aPayInfoConfig['payment_type_' . Order::BUSINESS_PAY_BY_BUSINESS_BALANCE] ?? '',
                    'value'      => $this->_formatFeeMsgWithSymbol('default_estimate_v2', $fDeductFee, $sUnit),
                ],
            ];
        } else {
            $sPayName     = $aPayInfoConfig['payment_type_' . (int)$aFeeDetailData['default_pay_type']] ?? '';
            $aMixSubItems = [
                [
                    'name'  => $sPayName,
                    'value' => $this->_formatFeeMsgWithSymbol('default_estimate_v2', $sRealPayTotalFee, $sUnit),
                ],
            ];
        }

        if (!empty($aFeeDetailData[self::RANGE_MIN_PRICE]) && !empty($aFeeDetailData[self::RANGE_MAX_PRICE])) {

            $aRangeFee    = [self::MIN_PRICE => $aFeeDetailData[self::RANGE_TOTAL_MIN_PRICE], self::MAX_PRICE => $aFeeDetailData[self::RANGE_TOTAL_MAX_PRICE]];
            $sRangeFeeMsg = $this->_formatRangeFeeMsg('range_fee_desc', $aRangeFee, $sUnit);
            if (!empty($sRangeFeeMsg)) {
                $sPayName     = $aPayInfoConfig['payment_type_' . (int)$aFeeDetailData['default_pay_type']] ?? '';
                $aMixSubItems = [
                    [
                        'name'  => $sPayName,
                        'value' => $sRangeFeeMsg,
                    ],
                ];
            }
        }

        $sPaymentTypeText = $aPayInfoConfig['payment_type_text'] ?? '';
        $aRet = [
            'name'      => $sPaymentTypeText,
            'sub_items' => $aMixSubItems,
        ];

        return $aRet;
    }

    /**
     * 格式化fee_msg
     * @param string $sKey         sKey
     * @param string $sEstimateFee sEstimateFee
     * @param string $sUnit        sEstimateFee
     * @return mixed
     */
    protected function _formatFeeMsgWithSymbol($sKey, $sEstimateFee, $sUnit) {
        $aConfig = NuwaConfig::text('config_text', 'getEstimateFeeInfo');
        $sFeeMsg = Language::replaceTag(
            $aConfig[$sKey],
            [
                'currency_symbol' => '',
                'num'             => NumberHelper::numberFormatDisplay($sEstimateFee),
                'currency_unit'   => $sUnit,
            ]
        );

        return $sFeeMsg;
    }

    /**
     * 格式化区间fee_msg
     * @param $sKey
     * @param $aRangeFee
     * @param $sUnit
     * @return mixed
     */
    private function _formatRangeFeeMsg($sKey, $aRangeFee, $sUnit) {
        $aConfig = NuwaConfig::text('config_text', 'getEstimateFeeInfo');
        if (empty($aRangeFee[self::MIN_PRICE]) || empty($aRangeFee[self::MAX_PRICE])) {
            return '';
        }

        $aReplace = [
            self::MIN_PRICE => NumberHelper::numberFormatDisplay($aRangeFee[self::MIN_PRICE]),
            self::MAX_PRICE => NumberHelper::numberFormatDisplay($aRangeFee[self::MAX_PRICE]),
            'currency_unit' => $sUnit,
        ];

        return Language::replaceTag($aConfig[$sKey], $aReplace);
    }

    /**
     * 获取车大首单减价项
     * @param array $aFeeDetailInfo 费项
     * @return array $bIsHasDiscount:   $sSelectionFee:
     */
    public static function getSpaciousCarDiscountFee($aFeeDetailInfo) {
        return $aFeeDetailInfo['sps_choose_car_fee_discount'];
    }

    /**
     * 获取车大会员折扣
     * @param array $aFeeDetailInfo 费项
     * @return array $bIsHasDiscount:   $sSelectionFee:
     */
    public static function getSpaciousCarMemberDiscountFee($aFeeDetailInfo) {
        return $aFeeDetailInfo['sps_spacious_car_member_discount'];
    }

    /**
     * 是否是特快价格range模式
     * @param array $aFeeDetailData ..
     * @return bool
     */
    private function _hitAPlusPriceRange($aFeeDetailData) {
        return $aFeeDetailData[FeeDetailLogic::ENABLE_APLUS_RANGE] ?? 0;
    }

    /**
     * 返回顶部车型图标
     * @param int    $iCityID 城市id
     * @param int    $iPcID   品类id
     * @param string $sLang   语言
     * @return string
     */
    private function _getTopIcon(int $iCityID, int $iPcID, string $sLang) {
        $aCondition = [
            'city'             => $iCityID,
            'product_category' => $iPcID,
        ];
        $oConfResult = Apollo::getInstance()->getConfigsByNamespaceAndConditions('fee_detail_icon', $aCondition);
        list($bOk, $aAllConfig) = $oConfResult->getAllConfigData();
        if (!$bOk) {
            return '';
        }

        $aConfigs = array_values($aAllConfig);
        if (1 == count($aConfigs)) {
            $aConf = $aConfigs[0];
            return $aConf['detail_car_icon'];
        }

        foreach ($aAllConfig as $sKey => $aConfig) {
            $aKey = explode('_', $sKey);
            if (!is_array($aKey) || 3 > count($aKey)) {
                continue;
            }

            if ($sLang == $aKey[2]) {
                return $aConfig['detail_car_icon'];
            }
        }
        return '';
    }
}
