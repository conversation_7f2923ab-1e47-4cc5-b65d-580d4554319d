<?php

use BizCommon\Models\Passenger\Passenger;
use BizCommon\Utils\Common;
use BizLib\Utils\Common as UtilsCommon;
use PreSale\Logics\athena\PreCancelLogic;
use BizLib\Utils\MapHelper;

/**
 * Class PPreCancelEstimateController
 *
 * 乘客取消预估，先调用该接口，返回挽留信息
 */
class PPreCancelEstimateController extends \PreSale\Core\Controller
{
    /**
     * @return mixed
     */
    public function indexAction() {
        try {
            $sToken       = $this->getRequest()->getStr('token');
            $iAccessKeyId = $this->getRequest()->getInt('access_key_id');
            $sEstimateId  = $this->getRequest()->getStr('estimate_id');
            $fLng         = $this->getRequest()->getStr('location_lng');
            $fLat         = $this->getRequest()->getStr('location_lat');

            $iPid = Passenger::getInstance()->getPidByToken($sToken);
            $aRet = UtilsCommon::getErrMsg(GLOBAL_SUCCESS, $this->aErrnoMsg);

            if ($iPid <= 0 || !Common::isRegionalWeChatMiniProgram($iAccessKeyId)) {
                $aRet['data']['cancel_info'] = new stdClass();
                return $this->sendJson($aRet);
            }

            $aAreaInfo      = MapHelper::getAreaInfoByLoc($fLng, $fLat);
            $aOrderInfo     = [
                'estimate_id'  => $sEstimateId,
                'passenger_id' => $iPid,
                'area'         => $aAreaInfo['id'],
            ];
            $aRetentionInfo = (new PreCancelLogic())->getRegionalGrowthRetentionInfo($aOrderInfo, PreCancelLogic::STAGE_ESTIMATE_CANCEL, $sToken);

            $aRet['data']['cancel_info'] = empty($aRetentionInfo) ? new stdClass() : $aRetentionInfo;
            $this->sendJson($aRet);
        } catch (\Exception $e) {
            $aRet = UtilsCommon::getErrMsg($e->getCode(), $this->aErrnoMsg);
            $this->sendJson($aRet);
        }
    }
}
