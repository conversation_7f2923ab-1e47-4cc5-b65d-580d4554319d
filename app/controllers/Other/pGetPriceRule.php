<?php

use BizLib\Config as NuwaConfig;
use BizLib\Constants\Common as ConstantsCommon;
use BizLib\Exception\ExceptionWithResp;
use BizLib\Utils\Common as UtilsCommon;
/*
 * 计价规则详情接口
 * wiki: http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=100904772
 * <AUTHOR>
 * @date 2017/07/02
 */

use BizLib\Log as NuwaLog;
use BizLib\Utils\Horae;
use BizLib\ExceptionHandler;
use BizLib\ErrCode\RespCode;
use BizLib\Client\TCPassengerClient;
use BizLib\Utils\UtilHelper;
use PreSale\Logics\priceRule\GetPriceRuleListReq;
use BizCommon\Logics\PriceRule\GetPriceRuleByOidReq;
use PreSale\Logics\priceRule\GetPriceRuleByTokenReq;
use PreSale\Logics\priceRule\GetPriceRuleByProduct;
use PreSale\Logics\priceRule\BuildPriceRule;
use PreSale\Logics\estimatePrice\FeeDetailLogic;
use BizCommon\Models\Bill\Bill;
use TripcloudCommon\Utils\Product as TripcloudProduct;
use BizLib\Client\OrderSystemClient;
use BizLib\Client\TicketApiClient;
use PreSale\Logics\estimatePrice\ArrayDiff;
use Xiaoju\Apollo\Apollo as ApolloV2;
use BizLib\ErrCode\Msg;
use BizLib\ErrCode\Code;
use Dukang\PropertyConst\Product\ProductBusinessId;

/**
 * Class pGetPriceRule.
 */
class PGetPriceRuleController extends \PreSale\Core\Controller
{

    /**
     * 根据计价token获取计价规则详情
     * 端入口：预估价.
     * @return void
     */
    private function _getPriceRuleByToken() {
        //初始化Request对象
        $oRequest = new GetPriceRuleByTokenReq($_GET);

        try {
            //获取默认返回值
            $aResponseInfo = UtilsCommon::getErrMsg(RespCode::P_SUCCESS, $this->aErrnoMsg);

            //验证参数
            list($iProductId, $iDepartureTimestamp, $aAreaInfo, $sPhone, $aPassenger) = $oRequest->verify();
            $aPid = $aPassenger['pid'] ?? 0;

            //调用账单服务
            $oBillPriceRule = new BuildPriceRule();
            $aOrderInfoReq  = $this->_buildOrderInfo($oRequest, $aAreaInfo);
            $aPriceRule     = $oBillPriceRule->getPriceStrategy(
                $oRequest->getPriceToken(),
                $aOrderInfoReq,
                $oRequest->getLang(),
                $oRequest->getAccessKeyId(),
                $aAreaInfo
            );

            //拼装返回值
            //$aResponseInfo['data'] = $aPriceRule;
            $aResponseInfo['data'] = (new BuildPriceRule())->addPriceRuleForEstimate($aPriceRule, $iProductId, $aAreaInfo, $iDepartureTimestamp, $sPhone, $aPid);
        } catch (\Exception $e) {
            //异常处理上下文
            $aContext = [
                'dltag'   => '_com_request_out_failure',
                'err_msg' => $this->aErrnoMsg,
            ];

            //异常处理并得到返回值
            $aResponseInfo = ExceptionHandler::getInstance()->handleException($e, $aContext);
        }

        //输出返回值
        $this->sendTextJson($aResponseInfo, $oRequest->getCallback());
    }

    /**
     * @return void
     */
    private function _getPriceRuleByProduct() {
        //初始化Request对象
        $oRequest = new GetPriceRuleByProduct($_GET);

        try {
            //获取默认返回值
            $aResponseInfo = UtilsCommon::getErrMsg(RespCode::P_SUCCESS, $this->aErrnoMsg);

            //验证参数
            list($iProductId, $iDepartureTimestamp, $aAreaInfo, $sPhone) = $oRequest->verify();

            //调用账单服务
            $oBillPriceRule  = new BuildPriceRule();
            $aOrderInfoReq   = $this->_buildOrderInfoForProductPrice($oRequest, $aAreaInfo);
            $aPassengerParam = $this->_buildPassengerParam($iProductId, $aAreaInfo, $oRequest);
            $aPriceRule      = $oBillPriceRule->getPriceStrategy(
                '',
                $aOrderInfoReq,
                $oRequest->getLang(),
                $oRequest->getAccessKeyId(),
                $aAreaInfo,
                $aPassengerParam,
                $oRequest->getRuleShowType()
            );

            //拼装返回值
            $aResponseInfo['data'] = (new BuildPriceRule())->addPriceRuleForEstimate($aPriceRule, $iProductId, $aAreaInfo, $iDepartureTimestamp, $sPhone);

            if (empty($aResponseInfo['data'])) {
                $aResponseInfo = UtilsCommon::getErrMsg(RespCode::P_ERRNO_NOT_OPEN_SERVICE, $this->aErrnoMsg);
            }
        } catch (\Exception $e) {
            //异常处理上下文
            $aContext = [
                'dltag'   => '_com_request_out_failure',
                'err_msg' => $this->aErrnoMsg,
            ];

            //异常处理并得到返回值
            $aResponseInfo = ExceptionHandler::getInstance()->handleException($e, $aContext);
        }

        //输出返回值
        $this->sendTextJson($aResponseInfo, $oRequest->getCallback());
    }

    /**
     * @param GetPriceRuleByTokenReq $oRequest  请求
     * @param array                  $aAreaInfo 城市信息
     * @return array
     */
    private function _buildOrderInfo(GetPriceRuleByTokenReq $oRequest, $aAreaInfo) {
        $sStartingLng = !empty((string)($oRequest->getFLng())) ? (string)($oRequest->getFLng()) : '0';
        $sStartingLat = !empty((string)($oRequest->getFLat())) ? (string)($oRequest->getFLat()) : '0';
        $aOrderInfo   = [
            'departure_time' => $oRequest->getDepartureTimeYmdHis(),
            // 'trip_country'   => $oRequest->getTripCountry(),
            'trip_country'   => $aAreaInfo['canonical_country_code'] ?? '',
            'starting_lng'   => $sStartingLng,
            'starting_lat'   => $sStartingLat,
            'channel'        => $oRequest->getChannel(),
        ];
        return $aOrderInfo;
    }

    /**
     * @param GetPriceRuleByProduct $oRequest  请求
     * @param array                 $aAreaInfo 城市信息
     * @return array
     */
    private function _buildOrderInfoForProductPrice(GetPriceRuleByProduct $oRequest, $aAreaInfo) {
        $sStartingLng = !empty((string)($oRequest->getFLng())) ? (string)($oRequest->getFLng()) : '0';
        $sStartingLat = !empty((string)($oRequest->getFLat())) ? (string)($oRequest->getFLat()) : '0';
        return [
            'departure_time' => $oRequest->getDepartureTimeYmdHis(),
            // 'trip_country'   => $oRequest->getTripCountry(),
            'trip_country'   => $aAreaInfo['canonical_country_code'] ?? '',
            'starting_lng'   => $sStartingLng,
            'starting_lat'   => $sStartingLat,
        ];
    }

    /**
     * @param int                   $iProductId 产品id
     * @param array                 $aAreaInfo  位置信息
     * @param GetPriceRuleByProduct $oRequest   请求
     * @return array
     */
    private function _buildPassengerParam($iProductId, $aAreaInfo, GetPriceRuleByProduct $oRequest) {
        $aSearchParam    = [
            'combo_type'        => (string)($oRequest->getComboType()),
            'car_level'         => (string)($oRequest->getRequireLevel()),
            'district'          => $aAreaInfo['district'],
            'abstract_district' => (isset($aAreaInfo['countyid']) && !empty($aAreaInfo['countyid'])) ? (string)($aAreaInfo['district'].','.$aAreaInfo['countyid']) : '',
        ];
        $aPassengerParam = [
            'product_id'   => $iProductId,
            'search_param' => $aSearchParam,
        ];

        return $aPassengerParam;
    }

    /**
     * 第三方计价详情数据
     * 端入口：行程中、结束计费、历史订单.
     * @return void
     * @throws ExceptionWithResp 异常
     */
    private function _getPriceRuleFromTripcloud() {
        //初始化Request对象
        $sOid          = $this->getRequest()->getQuery('oid', false);
        $formatOrderId = '';
        $sPriceToken   = $this->getRequest()->getQuery('price_token', false);
        $sChannel      = $this->getRequest()->getQuery('channel', false);
        $iComboId      = $this->getRequest()->getQuery('combo_id', 0);
        $sCarpoolType = $this->getRequest()->getQuery('carpool_type', '');
        $sBusShiftId = $this->getRequest()->getQuery('bus_shift_id', '');
        $iStationId = $this->getRequest()->getQuery('station_id', 0);
        $iDestStationId = $this->getRequest()->getQuery('dest_station_id', 0);
        $iLevelType     = $this->getRequest()->getQuery('level_type', 0);
        $sCityId        = $this->getRequest()->getQuery('city_id', '');
        $carLevel       = $this->getRequest()->getQuery('car_level', '');
        $iDepartureTime = $this->getRequest()->getQuery('departure_time', '');
        $iBusinessId    = $this->getRequest()->getQuery('business_id', 0);
        $iLang          = $this->getRequest()->getQuery('lang', 'zh-CN');
        $iFenceID       = $this->getRequest()->getQuery('fence_id', 0);

        if (empty($sPriceToken) && empty($sOid)) {
            $iBusinessId       = $this->getRequest()->getQuery('business_id', false);
            $aPriceTokenConfig = TripcloudProduct::getPriceTokenByProductId($iBusinessId);
            $sPriceToken       = $aPriceTokenConfig['price_token'] ?? '';
        }

        $sDistrict = '';
        $sFLng     = '';
        $sFLat     = '';
        $sTLng     = '';
        $sTLat     = '';
        if (!empty($sOid)) {
            $oRequest = new GetPriceRuleByOidReq($_GET);
            //验证参数并返回需要的参数验证过程中产生的中间值
            list($aOrderInfo) = $oRequest->verify();
            $formatOrderId    = UtilHelper::genHighIntOrderId($aOrderInfo['order_id'], $aOrderInfo['district']);
            $sDistrict        = $aOrderInfo['district'];

            $aExtendFeature = json_decode($aOrderInfo['extend_feature'], true) ?? [];
            if (isset($aExtendFeature['neworder_info']['estimate_fence_id'])) {
                $iFenceID = $aExtendFeature['neworder_info']['estimate_fence_id'];
            }
        } else {
            $oRequest = new GetPriceRuleByTokenReq($_GET);
            if (!empty($sPriceToken)) {
                //侧边计价规则，PM需求为直接跳到计价详情页，仅有business_id一个
                $oRequest->setPriceToken($sPriceToken);
            }

            //验证参数
            $aAreaInfo   = $oRequest->getArea();
            $sPriceToken = $oRequest->getPriceToken();
            $sDistrict   = $aAreaInfo['district'];
            $sFLng       = $oRequest->getFLng();
            $sFLat       = $oRequest->getFLat();
            $sTLng       = $oRequest->getTLng();
            $sTLat       = $oRequest->getTLat();
        }

        try {
            //获取默认返回值
            $aResponseInfo = UtilsCommon::getErrMsg(RespCode::P_SUCCESS, $this->aErrnoMsg);
            //验证参数并返回需要的参数验证过程中产生的中间值
            list($_,$_,$_,$_,$aPassengerInfo) = $oRequest->verify();
            //获取trip cloud 请求入参
            $aReq = array(
                'order_id'        => $formatOrderId,
                'price_token'     => $sPriceToken,
                'district'        => $sDistrict,
                'f_lng'           => $sFLng,
                'f_lat'           => $sFLat,
                't_lng'           => $sTLng,
                't_lat'           => $sTLat,
                'combo_id'        => $iComboId,
                'access_key_id'   => (string)$oRequest->getAccessKeyId(),
                'carpool_type'    => $sCarpoolType,
                'bus_shift_id'    => $sBusShiftId,
                'station_id'      => $iStationId,
                'dest_station_id' => $iDestStationId,
                'city_id'         => $sCityId,
                'flng'            => $sFLng,
                'flat'            => $sFLat,
                'tlng'            => $sTLng,
                'tlat'            => $sTLat,
                'departure_time'  => $iDepartureTime,
                'car_level'       => $carLevel,
                'business_id'     => $iBusinessId,
                'lang'            => $iLang,
                'fence_id'        => $iFenceID,
            );
            if (!empty($sChannel)) {
                $aReq['channel'] = $sChannel;
            }

            if (!empty($iLevelType)) {
                $aOrderNTuple = [
                    'level_type' => $iLevelType,
                ];
                $aReq['order_n_tuple'] = json_encode($aOrderNTuple);
            }
            // 定制化需求：自动驾驶产品线增加传pid，勿学
            if ($iBusinessId == ProductBusinessId::BusinessIdAutopilot) {
                $aReq['passenger_id'] = (string)$aPassengerInfo['pid'];
            }
            $aResult = $this->_getPriceRuleResp($iBusinessId, (int)$sCarpoolType, $aReq);
            if (0 != $aResult['errno'] || !isset($aResult['data'])) {
                $aPriceResult = array();
            } else {
                $aPriceResult = $aResult['data'];
            }

            if ($oRequest->getAccessKeyId() == ConstantsCommon::ENTERPRISE_API_SYSTEM && !empty($aPriceResult)) {
                // 企业级端的三方品类计价规则，添加平台使用费说明文案
                $aPriceResult = $this->_addPlatformFee($aPriceResult);
            }

            //拼装返回值
            $aResponseInfo['data'] = $aPriceResult;
        } catch (\Exception $e) {
            //异常处理上下文
            $aContext = [
                'dltag'   => '_com_request_out_failure',
                'err_msg' => $this->aErrnoMsg,
            ];

            //异常处理并得到返回值
            $aResponseInfo = ExceptionHandler::getInstance()->handleException($e, $aContext);
        }

        //输出返回值
        $this->sendTextJson($aResponseInfo, $oRequest->getCallback());
    }

    /**
     * 根据订单ID获取计价规则详情
     * 端入口：行程中、结束计费、历史订单.
     * @return void
     */
    private function _getPriceRuleByOid() {
        //初始化Request对象
        $oRequest = new GetPriceRuleByOidReq($_GET);

        try {
            //获取默认返回值
            $aResponseInfo = UtilsCommon::getErrMsg(RespCode::P_SUCCESS, $this->aErrnoMsg);

            //验证参数并返回需要的参数验证过程中产生的中间值
            list($aOrderInfo) = $oRequest->verify();

            $oRequest->setTripCountry($aOrderInfo['country_iso_code']);
            //解析得到计价token
            list($sPriceToken) = explode(':', $aOrderInfo['strategy_token']);

            //调用账单服务
            $aOrderInfoReq  = [
                'order_id'          => (int)($aOrderInfo['order_id']),
                'driver_id'         => (int)($aOrderInfo['driver_id']),
                'driver_phone'      => (string)($aOrderInfo['driver_phone']),
                'passenger_id'      => (int)($aOrderInfo['passenger_id']),
                'passenger_phone'   => (string)($aOrderInfo['passenger_phone']),
                'departure_time'    => $aOrderInfo['departure_time'],
                'begin_charge_time' => $aOrderInfo['begin_charge_time'],
                // 'trip_country'      => $oRequest->getTripCountry(),
                'trip_country'      => $aOrderInfo['country_iso_code'],
                'starting_lng'      => (string)($aOrderInfo['starting_lng']),
                'starting_lat'      => (string)($aOrderInfo['starting_lat']),
                'driver_product_id' => (string)($aOrderInfo['driver_product_id']),
                'strive_car_level'  => (string)($aOrderInfo['strive_car_level']),
            ];
            $oBillPriceRule = new BuildPriceRule();
            $aPriceRule     = $oBillPriceRule->getPriceStrategy(
                $sPriceToken,
                $aOrderInfoReq,
                $oRequest->getLang(),
                $oRequest->getAccessKeyId(),
                [
                    'district' => $aOrderInfo['district'],
                    'countyid' => $aOrderInfo['county_id'],
                ]
            );

            //拼装返回值
            //$aResponseInfo['data'] = $aPriceRule;
            $aResponseInfo['data'] = (new BuildPriceRule())->addPriceRuleForOrder($aPriceRule, $aOrderInfo);
        } catch (\Exception $e) {
            //异常处理上下文
            $aContext = [
                'dltag'   => '_com_request_out_failure',
                'err_msg' => $this->aErrnoMsg,
            ];

            //异常处理并得到返回值
            $aResponseInfo = ExceptionHandler::getInstance()->handleException($e, $aContext);
        }

        //输出返回值
        $this->sendTextJson($aResponseInfo, $oRequest->getCallback());
    }

    /**
     * @return void
     */
    private function _getMultiPriceRule() {
        //初始化Request对象
        $oRequest = new GetPriceRuleListReq($_GET);

        $sBizType       = $oRequest->getBizType();
        $iDepartureTime = $oRequest->getDepartureTime();

        try {
            //默认返回值
            $aResponseInfo = UtilsCommon::getErrMsg(RespCode::P_SUCCESS, $this->aErrnoMsg);

            //验证参数并返回需要的参数验证过程中产生的中间值
            list($sProductId, $aAreaInfo, $sPhone, $aPid) = $oRequest->verify();
            //拼装调用账单服务的参数
            $aPriceRuleReq = $oRequest->buildPriceRuleParams();

            //调用账单服务
            $oBill          = new Bill();
            $aPriceRuleResp = $oBill->getMultiStrategeisV2($aPriceRuleReq);
            $aPriceRuleResp = $aPriceRuleResp['multi_response'] ?? [];

            //拼装返回值
            $aPriceRules           = (new BuildPriceRule())->buildMultiPriceRuleForEstimate($aPriceRuleResp, $aPriceRuleReq, $sProductId, $sBizType, $aAreaInfo, $iDepartureTime, $sPhone, $aPid);
            $aResponseInfo['data'] = $aPriceRules[0];
            $aResponseInfo['data']['multi_strategies'] = $aPriceRules;
            (new BuildPriceRule())->postBuildPriceRule($oRequest->getPid(), $aPriceRuleReq);
        } catch (\Exception $e) {
            //异常处理上下文
            $aContext = [
                'dltag'   => '_com_request_out_failure',
                'err_msg' => $this->aErrnoMsg,
            ];

            //异常处理并得到返回值
            $aResponseInfo = ExceptionHandler::getInstance()->handleException($e, $aContext);
        }

        //输出返回值
        $this->sendTextJson($aResponseInfo, $oRequest->getCallback());
    }

    /**
     * @throws ExceptionWithResp e
     * @return void
     */
    public function indexAction() {
        $sOid        = $this->getRequest()->getQuery('oid', false);
        $sBizType    = $this->getRequest()->getQuery('biz_type', false);
        $iBusinessId = $this->getRequest()->getQuery('business_id', false);
        $sPriceToken = $this->getRequest()->getQuery('price_token', false);

        $aOrderInfo = $this->_getOrderInfo($sOid);
        if (TripcloudProduct::isTripcloudByBusinessID($iBusinessId) && false == \TripcloudCommon\Utils\Order::isDownwardOrder($aOrderInfo)) {
            $this->_getPriceRuleFromTripcloud();
        } elseif (1 == $sBizType) {
            $this->_getMultiPriceRule();
        } elseif ($this->_isGetByToken($aOrderInfo) && empty($sPriceToken)) {
            $this->_getPriceRuleByProduct();
        } elseif ($this->_isGetByToken($aOrderInfo)) {
            $this->_getPriceRuleByToken();
        } else {
            $this->_getPriceRuleByOid();
        }
    }

    /**
     * @param string $sOid 订单id
     * @return array
     */
    private function _getOrderInfo($sOid) {

        if (empty($sOid)) {
            return [];
        }

        try {
            $aOrderInfo   = UtilHelper::decodeId($sOid);
            $iOrderId     = $aOrderInfo['oid'];
            $sDistrict    = $aOrderInfo['district'];
            $oOrderSystem = new OrderSystemClient();
            $aOrderInfo   = $oOrderSystem->getOrderInfo($iOrderId, $sDistrict);
            $aOrderInfo   = $aOrderInfo['result']['order_info'];
            return $aOrderInfo;
        } catch (\Exception $e) {
            NuwaLog::notice('getOrderInfo fail', ['sOid' => $sOid]);
            return [];
        }
    }

    /**
     * 是否通过入参token获取
     * @param array $aOrderInfo order_info
     * @return bool
     */
    private function _isGetByToken($aOrderInfo) {
        if (empty($aOrderInfo)) {
            return true;
        }

        if (\BizLib\Constants\OrderSystem::ST_UNSTRIVED == $aOrderInfo['order_status']) {
            return true;
        }

        return false;
    }

    private function _addPlatformFee($aResult) {
        if (empty($aResult['strategies'])) {
            return $aResult;
        }

        $aConfig             = NuwaConfig::text('config_text', 'platform_fee_price_rule_content');
        $sPlatformName       = $aConfig['name'];
        $aPlatformSubTitle   = array();
        $aPlatformSubTitle[] = [
            "name" => $aConfig['sub_title']
        ];
        $aPlatformItem       = [
            "name"      => $sPlatformName,
            "sub_title" => $aPlatformSubTitle,
        ];

        foreach ($aResult['strategies'] as &$aStrategiesItem) {
            if (empty($aStrategiesItem)) {
                continue;
            }

            foreach ($aStrategiesItem as &$aPeriodItem) {
                if (empty($aPeriodItem['strategy'])) {
                    continue;
                }

                foreach ($aPeriodItem['strategy'] as &$aStrategyItem) {
                    if (empty($aStrategyItem)) {
                        continue;
                    }

                    foreach ($aStrategyItem as &$aOrderTypeItem) {
                        $aOrderTypeItem['items'][] = $aPlatformItem;
                    }
                }
            }
        }

        return $aResult;
    }

    /**
     * @desc 获取带产品线key的列表.
     *
     * @param array $iBusinessId $aReq
     *
     * @return object
     */
    private function _getPriceRuleResp($iBusinessId, $iCarpoolType, $aReq) {
        list($model, $aParams) = FeeDetailLogic::genModel(0, $iBusinessId, $iCarpoolType, "PGetPriceRule");
        $priceRule = [];
        switch ($model) {
            case ArrayDiff::fromDivertSelectTicket:
                $tcPriceRule = (new TCPassengerClient())->pGetPriceRule($aReq);
                $ticketPriceRule = (new TicketApiClient())->PGetPriceRule($aReq);
                $this->_genDiff($tcPriceRule, $ticketPriceRule, $aReq['bus_shift_id'], $aParams);
                $priceRule = $ticketPriceRule;
                break;

            case ArrayDiff::fromDivertSelectTc:
                $tcPriceRule = (new TCPassengerClient())->pGetPriceRule($aReq);
                $ticketPriceRule = (new TicketApiClient())->PGetPriceRule($aReq);
                $this->_genDiff($tcPriceRule, $ticketPriceRule, $aReq['bus_shift_id'], $aParams);
                $priceRule = $tcPriceRule;
                break;

            case ArrayDiff::fromTicketApi:
                $ticketPriceRule = (new TicketApiClient())->PGetPriceRule($aReq);
                $priceRule = $ticketPriceRule;
                break;

            case ArrayDiff::fromTripCloud:
                $tcPriceRule = (new TCPassengerClient())->pGetPriceRule($aReq);
                $priceRule = $tcPriceRule;
                break;

            default:
                break;
        }
        return $priceRule;
    }

    /**
     * @desc 获取带产品线key的列表.
     *
     * @param array $aResp $aNewResp $sBusShiftId $aParameters
     *
     * @return object
     */
    private function _genDiff($aResp, $aNewResp, $sBusShiftId, $aParameters) {
        // 比diff
        $aIgnorePaths = array(
            'ignore_added_paths'    => explode(',', $aParameters['ignore_added_paths']),
            'ignore_removed_paths'  => explode(',', $aParameters['ignore_removed_paths']),
            'ignore_modified_paths' => explode(',', $aParameters['ignore_modified_paths']),
            'ignore_diff_hash'      => explode(',', $aParameters['ignore_diff_hash']),
            'simple_hash_enable'    => (bool)($aParameters['simple_hash_enable']),
        );
        $aDiff = ArrayDiff::compareDiff($aResp, $aNewResp, $aIgnorePaths);
        if (!empty($aDiff)) {
            NuwaLog::warning(Msg::formatArray(Code::E_COMMON_HTTP_TRIPCLOUD_FEE_DETAIL, ['bus_shift_id' => $sBusShiftId, 'price_rule_diff' => json_encode($aDiff)]));
        }
        return;
    }
}
