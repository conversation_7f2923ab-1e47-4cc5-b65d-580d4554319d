<?php

use BizLib\ErrCode\RespCode;
use PreSale\Logics\fullPageInfo\GetFullPageInfoLogic;
use PreSale\Core\Controller;
use BizLib\ExceptionHandler;
use PreSale\Dto\GetFullPageInfoRequest;
use BizLib\Utils\Common as UtilsCommon;

/**
 * @desc  : 乘客端6.0全页面通用信息获取
 * @Author: wangyuwill<<EMAIL>>
 */
class PGetFullPageInfoController extends Controller
{
    /**
     * 构建函数
     * @return void
     */
    public function init() {
        parent::init();
    }

    /**
     * 入口函数
     * @return void
     */
    public function indexAction() {
        //获取默认返回值
        $aResponseInfo = UtilsCommon::pairErrNo(RespCode::P_SUCCESS, $this->aErrnoMsg);

        try {
            $oRequest = new GetFullPageInfoRequest();
            $oRequest->init();

            $aResponseInfo['data'] = (new GetFullPageInfoLogic())->build($oRequest);
        } catch (Exception $oException) {
            $aResponseInfo = ExceptionHandler::getInstance()->handleException($oException, ['err_msg' => $this->aErrnoMsg]);
        }

        $this->sendJson($aResponseInfo);

    }
}

