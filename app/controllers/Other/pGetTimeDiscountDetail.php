<?php

use \PreSale\Core\Controller;
use BizLib\Utils\Common;
use BizLib\Utils\Request;
use BizLib\Config as NuwaConfig;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\RespCode;
use BizLib\ExceptionHandler;
use PreSale\Logics\passenger\TimeDiscountLogic;
use BizLib\Exception\ExceptionWithResp;

/**
 * @desc    供需系数-时段单单优惠详情(落地页)
 * @authors liangdongxu (<EMAIL>)
 * @date    2021-09-22
 */
class PGetTimeDiscountDetailController extends Controller
{
    /**
     * @desc desc
     * @return void
     * @throws Exception exception
     */
    public function indexAction() {
        try {
            $aParams      = $this->_getInputParams();
            $aRet         = Common::pairErrNo(GLOBAL_SUCCESS);
            $aRet['data'] = (new TimeDiscountLogic())->getTimeDiscountDetail($aParams);
        } catch (Exception $e) {
            $aErrMsg = NuwaConfig::text('errno', 'p_get_activity_status_error_msg');
            $aRet    = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $aErrMsg]);
        }

        $this->sendTextJson($aRet);
    }

    /**
     * @desc 参数校验
     * @return array 参数
     * @throws ExceptionWithResp <comment for threw>
     */
    private function _getInputParams() {
        $oRequest           = Request::getInstance();
        $aParams['lang']    = $oRequest->getStr('lang');
        $aParams['city_id'] = $oRequest->getInt('city_id');
        $aParams['county_id'] = $oRequest->getInt('county_id');

        if (empty($aParams['city_id']) || empty($aParams['county_id'])) {
            throw new ExceptionWithResp(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                RespCode::P_PARAMS_ERROR,
                '',
                'errmsg:param error! aParam_json:'.json_encode($aParams)
            );
        }

        return $aParams;
    }
}
