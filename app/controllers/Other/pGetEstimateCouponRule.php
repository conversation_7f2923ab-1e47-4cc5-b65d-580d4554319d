<?php
use BizLib\Utils\Common;
use BizLib\Utils\Request;
use BizLib\Config as NuwaConfig;
use BizLib\ErrCode\Code;
use BizLib\ExceptionHandler;
use PreSale\Logics\passenger\PGetCouponLogic;
use BizLib\Exception\InvalidArgumentException;
use BizLib\Utils\Language;

/**
 * Class PGetCommunityInfoController
 * 预估券规则接口
 */
class PGetEstimateCouponRuleController extends \PreSale\Core\Controller
{
    /**
     * init
     * @return void
     */
    public function init() {
        parent::init();
    }

    /**
     * index
     * @return void
     */
    public function indexAction() {
        try {
            $aRet           = Common::getErrMsg(GLOBAL_SUCCESS);
            $aRet['data']   = [];
            $aParams        = $this->_getInputParams();
            $oActivityLogic = new PGetCouponLogic($aParams);
            $aRet['data']   = $oActivityLogic->getCouponInfo();
        } catch (\Exception $e) {
            $aErrMsg = NuwaConfig::text('errno', 'p_get_activity_info_error_msg');
            $aRet    = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $aErrMsg]);
        }

        $aRet['errno'] = (int)($aRet['errno']);
        $this->sendTextJson($aRet);
        return;
    }

    /**
     * @desc 参数检查http://eng.xiaojukeji.com/group/23/service/17450/pipeline/309722
     * @return mixed
     * @throws InvalidArgumentException exception
     */
    private function _getInputParams() {
        $oRequest         = Request::getInstance();
        $aParams['lang']  = $oRequest->getStr('lang') ?? Language::getLocalLanguage();
        $aParams['token'] = $oRequest->getStr('token');
        $aParams['estimate_id']   = $oRequest->getStr('estimate_id');
        $aParams['access_key_id'] = $oRequest->getInt('access_key_id');
        $aParams['coupon_id']     = $oRequest->getStr('coupon_id');
        if (empty($aParams['token']) || empty($aParams['lang']) || empty($aParams['estimate_id'])) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                $aParams
            );
        }

        return $aParams;
    }
}
