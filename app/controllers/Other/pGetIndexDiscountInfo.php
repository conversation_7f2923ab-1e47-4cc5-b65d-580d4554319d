<?php

use PreSale\Core\Controller;
use BizLib\ExceptionHandler;
use BizLib\Exception\ExceptionWithResp;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\RespCode;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\Common;
use BizLib\Utils\Language;
use PreSale\Logics\passenger\IndexDiscountInfoLogic;

/**
 *
 * Copyraight (c) 2020 xiaojukeji.com, Inc. All Rights Reserved.
 * @author: <EMAIL>
 * @date: 2020/9/18 3:14 下午
 * @desc: 获取首页折扣/优惠信息提示，展示小助手位置，与其互斥，目前（2020.9.18）仅有小程序使用
 *        设计wiki: http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=427151201
 * @wiki:
 *
 */
class PGetIndexDiscountInfoController extends Controller
{
    /**
     * 初始化信息
     * @return void
     */
    public function init() {
        parent::init();
    }

    /**
     * 请求入口函数信息
     * @return void
     */
    public function indexAction() {
        try {
            // 获取参数
            $aResponse          = Common::getErrMsg(GLOBAL_SUCCESS);
            $aParams            = $this->_getParams();
            $oIndexDiscountInfo = new IndexDiscountInfoLogic($aParams);
            $oIndexDiscountInfo->checkBizInfo();
            $aResponse['data'] = $oIndexDiscountInfo->getDiscountInfo();
            $this->sendJson($aResponse);
        } catch (Exception $oException) {
            $aErrMsg = NuwaConfig::text('errno', strtolower($this->sControllerName) . '_errno_msg');
            $aRet    = ExceptionHandler::getInstance()->handleException($oException, ['err_msg' => $aErrMsg]);
            $this->sendJson($aRet);
        }
    }

    /**
     * @throws ExceptionWithResp exception
     * @return array
     */
    private function _getParams() {
        $aRawParams = [
            'token'            => $this->oRequest->getStr('token'),
            'app_version'      => !empty($this->oRequest->getStr('app_version')) ? $this->oRequest->getStr('app_version') : $this->oRequest->getStr('appVersion'),
            'lang'             => $this->oRequest->getStr('lang', Language::ZH_CN),
            'access_key_id'    => $this->oRequest->getStr('access_key_id'),
            'lat'              => $this->oRequest->getFloat('lat'),
            'lng'              => $this->oRequest->getFloat('lng'),
            'city_id'          => $this->oRequest->getInt('city_id'),
            'channel'          => $this->oRequest->getInt('channel'),
            'menu_id'          => $this->oRequest->getStr('menu_id'),
            'homepage_version' => $this->oRequest->getStr('homepage_version'),
        ];
        if (empty($aRawParams['token']) || empty($aRawParams['access_key_id']) || empty($aRawParams['app_version'])) {
            throw new ExceptionWithResp(
                Code::E_COMMON_PARAM_ERROR,
                RespCode::P_PARAMS_ERROR,
                '',
                ['input_raw_params' => $aRawParams]
            );
        }

        return $aRawParams;
    }
}
