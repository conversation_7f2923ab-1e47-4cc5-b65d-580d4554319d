<?php
/*
 * 乘客应答前取消，先调用该接口，返回二次确认框以及导流信息
 * 二次确认后再调用pCancelOrder
 * 今后等待应答页取消阻断尽量收束到该接口，不再从pGetOrderMatchInfo接口中取挽留文案
 *
 * <AUTHOR>
 * @date: 17/06/20
 */

use BizCommon\Constants\OrderNTuple;
use BizCommon\Infrastructure\Repository\LikeWaiteBounsRepository;
use BizCommon\Logics\Anycar\AnyCarCommonLogic;
use BizCommon\Logics\Anycar\BothCallAnycarLogic;
use BizCommon\Logics\Order\BookingOrder;
use BizCommon\Logics\Order\FieldOrderNTuple;
use BizCommon\Models\Order\OrderCompensation;
use BizCommon\Models\Order\OrderStation;
use BizCommon\Models\Passenger\Passenger;
use BizCommon\Models\Rpc\OrderSystemRpc;
use BizCommon\Utils\Common;
use BizCommon\Utils\Common as UtilCommon;
use BizCommon\Utils\Horae as BizCommonHorae;
use BizLib\Client\AthenaApiV3Client;
use BizLib\Client\NewtonCommonClient;
use BizLib\Config as NuwaConfig;
use BizLib\Constants\Horae;
use BizLib\Constants\OrderSystem;
use BizLib\ErrCode\RespCode;
use BizLib\Exception\ExceptionWithResp;
use BizLib\Libraries\RedisDB;
use BizLib\Log;
use BizLib\Log as NuwaLog;
use BizLib\Utils\ApolloHelper;
use BizLib\Utils\CarLevel;
use BizLib\Utils\Channel;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\Language;
use BizLib\Utils\Product;
use BizLib\Utils\ProductCategory;
use BizLib\Utils\Request;
use BizLib\Utils\UtilHelper;
use Dirpc\SDK\PriceApi\Client;
use Engine\GaiaSdkPhp\Gaia;
use Nuwa\ApolloSDK\Apollo;
use BizCommon\Constants\Stage;
use BizCommon\Domain\CarpoolComponent\WaitRewardComponent;
use PreSale\Infrastructure\Repository\PassengerRepository;
use PreSale\Infrastructure\Repository\Redis\PrepayInterceptResultRepository;
use PreSale\Logics\athena\PreCancelLogic;
use BizCommon\Infrastructure\Repository\Redis\AthenaQueuePredictRepository;
use BizLib\Constants;
use BizCommon\Logics\CommuteCard\CarpoolGoRight\WaitingStage;
use BizCommon\Models\Order\LineUpOrderComModel;
use BizCommon\Infrastructure\Repository\UfsRepository;
use PreSale\Logics\compensation\CompensationLogic;
use PreSale\Logics\compensation\NormalNoCarCompensateLogic;
use PreSale\Logics\sideEstimate\paramSet\source\Quotation;
use PreSale\Models\guideCardItem\button\GuideCardStyleManage;
use PreSale\Models\guideCardItem\container\GuideAnycarContainer;
use PreSale\Models\guideCardItem\guideView\GuideBookingGoNowView;
use PreSale\Models\guideCardItem\GuideViewList;
use PreSale\Models\guideCardItem\PreCancelCard;
use PreSale\Models\guideCardItem\MatchResultRuntime;
use PreSale\Infrastructure\Repository\Redis\PriceRepository;
use TripcloudCommon\Utils\ApolloConf as TripCloudApolloConf;
use \BizLib\Client\DuseApiClient;
use BizLib\Client\PriceApiClient;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizCommon\Models\Order\Order;
use BizLib\Utils\Product as UtilProduct;
use TripcloudCommon\Utils\Product as TripcloudProduct;

/**
 * Class PPreCancelOrderController
 */
class PPreCancelOrderController extends \PreSale\Core\Controller
{
    const PREFIX_P_ORDER_HOLD_TYPE_CACHE = 'p_order_hold_type_cache_'; //订单愿等类型
    const DISCARDED_ORDER_ID = 'TkRJNU5EazJOekk1TmpBPQ=='; //已丢弃的订单id

    //快车排队相关常量
    const BLURRY_TYPE_NULL = '';     //排位熔断空值，默认
    const BLURRY_TYPE_RANK = 'rank'; //排位熔断类型，模糊排位人数
    const BLURRY_TYPE_TIME = 'time'; //排位熔断类型，模糊等待时间
    //熔断阈值
    const QUEUE_RANK_BLURRY_LIMIT = 200; // 排位熔断
    const QUEUE_TIME_BLURRY_LIMIT = 7200; // 时间熔断（2h）

    const APLUS_NO_CAR_COMPENSATION      = 'aplus'; //滴滴特快无车赔
    const TAXI_REWARD_COMPENSATIO        = 'taxi'; // 出租车等应答立减金补偿
    const NEW_GUIDE_RECOMMEND_STYLE      = '1';

    // 反作弊接口拦截状态
    const SILENCE_INTERCEPT = 3;  // 静默
    const REQUIRE_PREPAY    = 2;  // 预付
    const NORMAL_DISCHARGED = 0;  // 正常放行

    const CALL_NEWTON_BEFORE_ATHENA_BARGAIN_NODE = '3';

    const NOT_SHOW_POP_UP_RECOMMEND = '1';

    const ATHENA_GUIDE_SOURCE_CANCEL_DETAINMENT = 3; // 取消挽留弹窗中推荐卡片点击的导流


    const BOOKING_MOCK_DRIVER_ORDER_GO_NOW_TIME = 3600;

    private $_sToken = '';

    private $_bV6dot0 = false;

    private $_bV5dot0 = false;

    private $_aAthenaInfo = [];

    private $_order;

    private $_aOrderInfo = [];

    /**
     * 数据总线
     * @var MatchResultRuntime
     */
    private $_oMatchResultRuntime;

    /**
     * 是否出取消弹窗新推荐样式
     * @var bool
     */
    private $_bAllowShowNewStyle;

    private $_sPrematchBroadcast;

    /**
     * @return mixed
     * @throws \Exception \Exception.
     */
    public function indexAction() {
        try {
            $this->_order = new Order();
            // $this->load->model('passenger/Passenger');
            $sToken      = $this->getRequest()->getQuery('token', false);
            $sEncodeOid  = $this->getRequest()->getQuery('order_id', false);
            $sLang       = $this->getRequest()->getQuery('lang', false);
            $sAppVersion = $this->getRequest()->getQuery('app_version', false);
            // 等待应答6.0标识
            $this->_bV6dot0 = $this->getRequest()->getQuery('v6_version', false);

            // 等待应答5.0标识
            $this->_bV5dot0 = $this->getRequest()->getQuery('v5_version', false);

            if (empty($sAppVersion)) {
                $sAppVersion = $this->getRequest()->getQuery('appversion', false);
            }

            $iAccessKeyId = $this->getRequest()->getQuery('access_key_id', 0);
            $iClientType  = intval($this->getRequest()->getQuery('client_type', false));
            $sCaller      = $this->getRequest()->getQuery('caller', false);
            $iIsWaitAnswerUpgrade = $this->getRequest()->getQuery('is_wait_answer_upgrade', 0);

            $this->_sToken = $sToken;
            //静默单处理
            if (self::DISCARDED_ORDER_ID == $sEncodeOid && Common::isFromKFlower($iAccessKeyId)) {
                $aRet = $this->_getDiscardOrderResponse();
                $this->sendJson($aRet);

                return;
            }

            $aOrderDecodeResult = UtilHelper::decodeId($sEncodeOid);
            $iOrderId           = $aOrderDecodeResult['oid'];
            $sDistrict          = $aOrderDecodeResult['district'];
            if (empty($iOrderId) || empty($sDistrict)) {
                throw new \Exception('订单id错误', P_ERRNO_PARAMS_ERROR);
            }

            $iPid = Passenger::getInstance()->getPidByToken($sToken);
            if ($iPid <= 0) {
                throw new \Exception('token无效', P_ERRNO_TOKEN_ERROR);
            }

            $oOrderSystemRpc = new OrderSystemRpc();
            $aOrderInfo      = $oOrderSystemRpc->getOrderInfo($sDistrict, $iOrderId);

            // 预付订单的情况，调整extra_info字段的格式，保证athena能接收到参数
            if (UtilHelper::checkOrderExtraType($aOrderInfo['extra_type'], 'o_prepay')) {
                if (isset($aOrderInfo['extra_info']) && !empty($aOrderInfo['extra_info'])) {
                    $aExtraInfo  = json_decode($aOrderInfo['extra_info'], true);
                    if (isset($aExtraInfo['pre_payment'])) {
                        $aOrderInfo['extra_info'] = ['pre_payment' => strval($aExtraInfo['pre_payment']),];
                    }
                }
            }

            if (empty($aOrderInfo)) {
                throw new \Exception('订单不存在', P_ERRNO_PARAMS_ERROR);
            }

            $this->_aOrderInfo = $aOrderInfo;

            $this->_sPrematchBroadcast = $this->_getPreMatchOrder();
            // 获取支持放量城市的开关判定结果
            $this->_bAllowShowNewStyle = self::_showPreCancelCardStyleSwitch($this->getRequest(), $aOrderInfo);
            // 放量过的城市才需要在取消挽留阶段展示新样式的推荐出口卡片
            if ($this->_bAllowShowNewStyle) {
                if (empty($aOrderInfo['extra_info'])) {
                    $aOrderInfo['extra_info'] = ['pre_cancel_rec_multi_car' => self::NEW_GUIDE_RECOMMEND_STYLE,];
                } else {
                    $aOrderInfo['extra_info']['pre_cancel_rec_multi_car'] = self::NEW_GUIDE_RECOMMEND_STYLE;
                }
            }

            $aOrderInfo['version_6'] = $this->_bV6dot0; // 将端版本存入OrderInfo为了后面函数调用中使用版本号信息

            if ($iPid != $aOrderInfo['passenger_id']) {
                throw new \Exception('验证失败', P_ERRNO_PARAMS_ERROR);
            }

            $isPreMatchOrder    = $this->_isPreMatchOrder();
            $isShowPreMatchCard = $this->_isShowPrematchCard();

            $isBookingMockDriverOrder = $this->_isBookingMockDriverOrder($aOrderInfo);
            //仅呼叫小神车时没有取消挽留推荐弹窗
            $bisOnlyDidiMini = $this-> _isOnlyDidiMiniOrder($aOrderInfo);

            $oPreCancelLogic = new PreCancelLogic();
            $aRetentionInfo  = $oPreCancelLogic->getRetentionInfo($aOrderInfo, PreCancelLogic::STAGE_PRE_CANCEL, $sLang, $iAccessKeyId, $isPreMatchOrder);
            $aRet            = UtilsCommon::getErrMsg(GLOBAL_SUCCESS, $this->aErrnoMsg);
            // 构建运行时数据总线
            $aReqRawParams = (array)$this->getRequest()->getQuery(null, false, true);
            $this->_oMatchResultRuntime = $this->_buildMatchResultRuntime(
                $aReqRawParams,
                $aOrderInfo,
                $iIsWaitAnswerUpgrade,
            );
            if ($this->_iterationSwitch($aReqRawParams, $aOrderInfo)) {
                // 添加上版本信息
                $aOrderInfo['app_version'] = $sAppVersion;
            }

            $oCompensationLogic = CompensationLogic::getInstance();
            $oCompensationLogic->init($aReqRawParams, $aOrderInfo);

            $oNormalNoCarCompensationLogic = NormalNoCarCompensateLogic::getInstance();
            $oNormalNoCarCompensationLogic->init($aReqRawParams, $aOrderInfo);

            $this->_oMatchResultRuntime->setCompensationLogic($oCompensationLogic);
            $this->_oMatchResultRuntime->setNormalNoCarCompensationLogic($oNormalNoCarCompensationLogic);

            // 构建实验
            if ($this->_iterationSwitch($aReqRawParams, $aOrderInfo)) {

                $this->_oMatchResultRuntime->buildNewPopExam($aReqRawParams, $aOrderInfo, $this->_bAllowShowNewStyle,$isShowPreMatchCard);
            }

            $aCancelCard = $this->_getCancelCard($aOrderInfo, $aRetentionInfo, $sAppVersion, $iClientType, $sLang, $sCaller, $iAccessKeyId, $isPreMatchOrder);
            if (empty($aCancelCard)) {
                throw new \Exception('未配置取消卡片', P_ERRNO_PARAMS_ERROR);
            }
            $aAthenaExpectInfo =  $this->_getAthenaBubbleExpectInfo($aOrderInfo, $this->_oMatchResultRuntime->oAnycarEstimateViewModel->aAnycarEstimate['raw_estimate_data']);

            if ($this->_iterationSwitch($aReqRawParams, $aOrderInfo)) {
                $aCardInfo = [];
                $isBookingOrder = BookingOrder::checkBooingOrderABSwitch($aOrderInfo);
                $isValidVersion = self::_checklBookingOrderPreCancelVersion($sAppVersion, $iAccessKeyId);
                $bAllowGaia = $this->_buildOrderMatchGripperOptimize($aOrderInfo, $iAccessKeyId, $sAppVersion);

                // 命中实验且不是预定单条件
                // 预匹配暂时不走 athena 推荐 || 仅呼叫小神车不出挽留推荐弹窗
                if ($bAllowGaia && !($isBookingOrder && $isValidVersion) && !$isPreMatchOrder && !$bisOnlyDidiMini) {
                    $this->_oMatchResultRuntime->bAllowMultiAppendCar = $this->_bAllowShowNewStyle; // 是否允许多车型追加
                    if (empty($this->_aAthenaInfo)) {
                        $this->_aAthenaInfo = $this->_getAthenaInfo($aOrderInfo);
                    }

                    $oGuideViewList = new GuideViewList();
                    $oGuideCard = $oGuideViewList->getGuideCardV2($this->_aAthenaInfo['order_match_recommend_result'], $this->_oMatchResultRuntime, $this->_aAthenaInfo['pre_cancel_scene_type'], $this->_aAthenaInfo['guide_recommend_info'], $aAthenaExpectInfo);
                    if (!empty($oGuideCard)) {
                        $oGuideContainer = $oGuideViewList->getContainerCard($oGuideCard);
                        $aCardInfo[]     = $oGuideContainer;
                    }

                    $oPreCancelCard = new PreCancelCard();
                    $oPreCancelCard->buildCardV2($aCardInfo, $aReqRawParams, $this->_oMatchResultRuntime, $this->_aAthenaInfo['order_match_recommend_result'], $this->_aAthenaInfo['pre_cancel_scene_type'], $this->_aAthenaInfo['order_ets_info'], $aAthenaExpectInfo);
                } else {
                    // 预匹配暂时不走 athena 推荐 || 仅呼叫小神车不出挽留推荐弹窗
                    if (!$isPreMatchOrder && !$bisOnlyDidiMini) {
                        // 等待应答迭代二期 增加出口推荐
                        if (empty($this->_aAthenaInfo)) {
                            $this->_aAthenaInfo = $this->_getAthenaInfo($aOrderInfo);
                        }

                        $oGuideViewList = new GuideViewList();
                        $aCardInfo      = [];
                        $iEtsExpGroup   = 0;
                        if (!empty($this->_aAthenaInfo['extra'])) {
                            $iEtsExpGroup = $this->_aAthenaInfo['extra']['ets_exp_group'] ?? 0;
                        }

                        if (empty($aCancelCard['privilege'])) {
                            $this->_oMatchResultRuntime->bAllowMultiAppendCar = $this->_bAllowShowNewStyle;
                            $this->_oMatchResultRuntime->iExperimentalGroup   = $iEtsExpGroup;
                            $oGuideCard = $oGuideViewList->getGuideCard($this->_aAthenaInfo['order_match_recommend_result'], $this->_oMatchResultRuntime, $this->_aAthenaInfo['guide_recommend_info']);
                            if (!empty($oGuideCard)) {
                                $oGuideContainer = $oGuideViewList->getContainerCard($oGuideCard);
                                $aCardInfo[]     = $oGuideContainer;
                            }
                        }
                    }

                    $aBookingGoNowCardInfo = [];
                    if ($isBookingOrder && $isValidVersion) {
                        // 预定单 取消挽留弹窗 不走athena
                        $iDepartureTimeSection = (int)strtotime($aOrderInfo['departure_time'] ?? 0) - time();
                        $iGoNowTime            = $this->_getBookingOrderApolloConfig('booking_pre_cancel_go_now_time', 'go_now_time') == [] ? 900 : $this->_getBookingOrderApolloConfig('booking_pre_cancel_go_now_time', 'go_now_time');
                        if ($isBookingMockDriverOrder) {
                            $iGoNowTime = self::BOOKING_MOCK_DRIVER_ORDER_GO_NOW_TIME;
                        }
                        if ($iDepartureTimeSection < $iGoNowTime) {
                            $this->_oMatchResultRuntime->oOrderBookingPreCancelEstimateViewModel->buildOnce();
                            $guideBookingGoNowView = new GuideBookingGoNowView($this->_oMatchResultRuntime);
                            $oGuideCard            = $guideBookingGoNowView->doRender();
                            // 挽留弹窗中追加车型
                            if ($oGuideCard != null) {
                                $oGuideCardContainer = new GuideAnycarContainer();
                                $oGuideCardContainer->setCardList([$oGuideCard]);
                                $aBookingGoNowCardInfo[] = $oGuideCardContainer;
                            }
                        }
                    }

                    $oPreCancelCard = new PreCancelCard();

                    $oPreCancelCard->buildCard($aCardInfo, $aBookingGoNowCardInfo, $aCancelCard, $aReqRawParams, $aOrderInfo,
                        $this->_bAllowShowNewStyle,$this->_oMatchResultRuntime,$isShowPreMatchCard,$aAthenaExpectInfo, $isBookingMockDriverOrder);
                }

                $aRet['data']['cancel_content'] = $oPreCancelCard;
            } else {
                // 出租车等应答立减金取消挽留(发单时单勾出租车场景)
//                if ($oPreCancelLogic->isCityOpened($aOrderInfo) && $oPreCancelLogic->isPutongTaxi($aOrderInfo)) {
//                    $sTitle = $oPreCancelLogic->renderTitle($aOrderInfo);
//                    if (!empty($sTitle)) {
//                        $aCancelCard['title'] = $sTitle;
//                    }
//                }
                $aRet['data']['cancel_info'] = $aCancelCard;
            }

            $aRet['data']['guide_list'] = $this->_getGuideListCard($aRetentionInfo);
            $this->sendJson($aRet);
        } catch (\Exception $e) {
            $aRet = UtilsCommon::getErrMsg($e->getCode(), $this->aErrnoMsg);
            $this->sendJson($aRet);
        }
    }

    /**
     * 预取消卡片样式城市放量开关
     * @param \Nuwa\Core\Request\Request|\Nuwa\Core\Request\RequestCli $aRequest   请求入参
     * @param array                                                    $aOrderInfo 订单信息
     * @return bool
     */
    private function _showPreCancelCardStyleSwitch($aRequest, $aOrderInfo) {
        $oApollo = new Apollo();

        $oToggle = $oApollo->featureToggle(
            'gs_pre_cancel_order_card_ctiy_switch',
            [
                'key'           => $aOrderInfo['passenger_id'],
                'city'          => $aOrderInfo['area'],
                'county'        => $aOrderInfo['county'],
                'pid'           => $aOrderInfo['passenger_id'],
                'phone'         => $aOrderInfo['passenger_phone'],
                'lang'          => $aRequest->getQuery('lang', false),
                'access_key_id' => $aRequest->getQuery('access_key_id', 0),
                'version'       => $aRequest->getQuery('appversion') ?? $aRequest->getQuery('app_version'),
            ]
        );
        return $oToggle->allow();
    }

    /**
     * mock返回
     *
     * @return array|bool
     */
    private function _getDiscardOrderResponse() {
        $aPreCancelCard = NuwaConfig::text('config_passenger', 'pre_cancel_card');
        $aCancelInfo    = $aPreCancelCard['card_setting'][5]['card_no_guide'] ?? [];

        $aRet = UtilsCommon::getErrMsg(GLOBAL_SUCCESS, $this->aErrnoMsg);
        $aRet['data']['cancel_info'] = $aCancelInfo;

        return $aRet;
    }

    /** 将anycar单车型mock成单车型发单处理
     * @param array $aOrderInfo orderInfo
     * @return mixed
     */
    private function _getAnycarSingleMockedOrderInfo($aOrderInfo) {
        $aExtendFeature       = json_decode($aOrderInfo['extend_feature'], true) ?? [];
        $aMultiRequireProduct = $aExtendFeature['multi_require_product'] ?? [];

        if (empty($aMultiRequireProduct) || ! $aOrderInfo['is_anycar']) {
            return $aOrderInfo;
        } else {
            if (AnyCarCommonLogic::isSingleCarMock($aOrderInfo)) {
                return AnyCarCommonLogic::mockSingleCar($aOrderInfo);
            } elseif (1 == count($aMultiRequireProduct)) {
                return AnyCarCommonLogic::mockSingleCar($aOrderInfo);
            } else {
                return $aOrderInfo;
            }
        }
    }

    /**
     * 取消文案
     * @param array  $aOrderInfo     aOrderInfo
     * @param array  $aRetentionInfo aRetentionInfo
     * @param string $sAppVersion    sAppVersion
     * @param int    $iClientType    iClientType
     * @param string $sLang          lang
     * @param string $sCaller        sCaller
     * @param int    $iAccessKeyId   iAccessKeyId
     *
     * @return array
     */
    private function _getCancelCard($aOrderInfo, $aRetentionInfo, $sAppVersion, $iClientType, $sLang, $sCaller, $iAccessKeyId, $isPreMatchOrder = false) {
        // 是否是预匹配订单
        if ($isPreMatchOrder) {
            $sConfirmTextTips = NuwaConfig::text('config_passenger', 'pre_match_cancel_card');
            return $sConfirmTextTips['card_no_guide'] ?? [];
        }

        // 是不是小程序的 anycar
        $bIsMiniProgramAnycar = ($aOrderInfo['is_anycar'] || $aOrderInfo['type'] )&&\BizCommon\Utils\Common::isFromMainMiniProgram($iAccessKeyId);
        // 是不是鸿鹄客企
        $bIsTCIntercityCarpool = TripCloudApolloConf::isTCIntercityCarpoolByProductID($aOrderInfo['product_id']);

        // anycar 包含滴滴特快/极速拼车/出租车 并且命中无车赔，取消后弹窗挽留
        $aCardContent = $this->_PreCancelNoCarCompensation($aOrderInfo, $sLang, $bIsMiniProgramAnycar);
        if (!empty($aCardContent)) {
            return $aCardContent;
        }

        // 遗失物品订单(去除其他场景干扰)
        if (BizCommonHorae::isLossRemandOrder($aOrderInfo)) {
            $sConfirmTextTips = NuwaConfig::text('config_passenger', 'loss_remand_pre_cancel_card');
            return $sConfirmTextTips['card_no_guide'] ?? [];
        }

        $aSingleMockedOrderInfo = $this->_getAnycarSingleMockedOrderInfo($aOrderInfo);
        // 6.0下远途特快非排队
        if ($this->_bV6dot0 && BizCommonHorae::isInterCarpoolGoRightNow($aSingleMockedOrderInfo) && !$aSingleMockedOrderInfo['line_up']) {
            $aText = NuwaConfig::text('config_passenger','pre_cancel_inter_go_right_now_v6dot0');
            // 60s内弹窗
            if (time() - strtotime($aSingleMockedOrderInfo['_create_time']) < 60) {
                return $aText['within_60'];
            } else {
                // 默认弹窗
                return $aText['default'];
            }
        }

        //愿等礼金2.0
        // 屏蔽小程序的 anycar
        if (!(\BizCommon\Utils\Common::isFromMainMiniProgram($iAccessKeyId) && $aOrderInfo['is_anycar'])) {
            $oWaitRewardComponent = WaitRewardComponent::getInstance(Stage::STAGE_PRE_CANCEL)->buildFeature($aOrderInfo);
            $aComponentRet        = $oWaitRewardComponent->build();
            if (BizCommonHorae::isInterCityCarpool($aOrderInfo) && !empty($aComponentRet['cancel_info'])) {
                return $this->_getInterWaitReward(($aComponentRet['cancel_info']));
            }

            if (!empty($aComponentRet['cancel_info'])) {
                return $aComponentRet['cancel_info'];
            }
        }

        //拼成乐2.0：取消阻断文案   文案优先级： 愿等礼金（等待激励）> 排队场景 >普通
        if (BizCommonHorae::isLowPriceCarpool($aOrderInfo)) {
            $aCardContentConf = NuwaConfig::text('config_passenger', 'pre_cancel_low_price_carpool');

            // 排队文案
            $aLineUpIntercept = $this->_lineUpIntercept($aOrderInfo, $sAppVersion, $aCardContentConf);
            if (!empty($aLineUpIntercept)) {
                return $aLineUpIntercept;
            }

            // ets挽留
            $aEtsIntercept = $this->_etsIntercept($aOrderInfo, $aCardContentConf);
            if (!empty($aEtsIntercept)) {
                return $aEtsIntercept;
            }

            // 兜底返回
            // 边走边拼
            if (BizCommonHorae::isPoolInTripCarpool($aOrderInfo)) {
                return $aCardContentConf['pool_in_trip'];
            }

            return $this->_cardLinkReturnWrapper($aOrderInfo, $aCardContentConf);
        }

        // 如果是无忧卡派车但未派到
        if (BizCommonHorae::isInterCityPoolFullGoBooking($aOrderInfo) && PreCancelLogic::isInterCityGoCardWork($aOrderInfo['order_id'])) {
            $aText = Language::getDecodedTextFromDcmp('config_carpool-inter_city_carpool_cancel_order_go_card');
            if (!empty($aText)) {
                return $aText;
            }
        }

        if (BizCommonHorae::isInterCityPoolFullGoBooking($aOrderInfo)) {
            $aContent = (new PreCancelLogic())->getInterCarpoolFullDepartCancelTips($aOrderInfo, $sAppVersion);
            //城际需要去掉取消的highlight
            //$aContent['cancel_button_highlight'] = 0;
            return $aContent;
        }

        // 6.0预约单
        if ($this->_bV6dot0
            && OrderSystem::TYPE_ORDER_BOOKING == $aOrderInfo['type']
            && !BizCommonHorae::isInterBookingScene($aOrderInfo)
        ) {
            return (new PreCancelLogic())->getPreCancelBookingOrder($aOrderInfo);
        }

        // 鸿鹄客企获取等待应答文案
        $aTCIntercityConfig = [];
        if ($bIsTCIntercityCarpool) {
            $aTCIntercityConfig = (new PreCancelLogic())->getPreCancelTCIntercityCarpool($aOrderInfo);
        }

        // 6.0 城际非拼满走
        if ($this->_bV6dot0
            && BizCommonHorae::isInterBookingScene($aOrderInfo)
        ) {
            $aCardContentConf = NuwaConfig::text('config_passenger', 'h5_pre_cancel');
            $aCardContent     = (new PreCancelLogic())->getPreCancelInfoForBookingOrder($aOrderInfo);
            if (!empty($aCardContent)) {
                $aRightsInfo = OrderCompensation::buildCompensation($aOrderInfo, $sLang);

                // 等待应答页6.0 privilege构造 compensation 新增tag相关字段
                $aCardContent['privilege']   = $aRightsInfo['rights_list'] ?? [];
                $aCardContent['rights_info'] = $aRightsInfo;

                $aRet = array_merge($aCardContentConf, $aCardContent);
                // 鸿鹄客企等待应答优化
                if ($bIsTCIntercityCarpool) {
                    return array_merge($aRet, $aTCIntercityConfig);
                }

                return $aRet;
            }
        }

        //预约单的pPreCancelOrder点进去是H5, 走另一份配置
        if (!$bIsMiniProgramAnycar && \BizCommon\Models\Order\OrderPromise::isPromiseV2($aOrderInfo, $sAppVersion, $iAccessKeyId) && $this->_checkCaller($sCaller)) {
            $aCardContentConf = NuwaConfig::text('config_passenger', 'h5_pre_cancel');
            $aCardContent     = (new PreCancelLogic())->getPreCancelInfoForBookingOrder($aOrderInfo);
            if (!empty($aCardContent)) {
                $aRightsInfo = OrderCompensation::buildCompensation($aOrderInfo, $sLang);
                $aCardContent['rights_info'] = $aRightsInfo;
                return array_merge($aCardContentConf, $aCardContent);
            }
        }

        if ($bIsMiniProgramAnycar) {
            $aPreCancelCard = NuwaConfig::text('config_passenger', 'pre_cancel_card_mini_program');
        } elseif ($this->_bV6dot0) {
            $aPreCancelCard = NuwaConfig::text('config_passenger', 'pre_cancel_card_v6dot0');
        } else {
            $aPreCancelCard = NuwaConfig::text('config_passenger', 'pre_cancel_card');
        }

        // 鸿鹄客企等待应答优化，小程序等场景
        if ($bIsTCIntercityCarpool) {
            $aPreCancelCard['card_setting'][PreCancelLogic::GUIDE_TYPE_NONE]['card_no_guide']['title']     = $aTCIntercityConfig['title'];
            $aPreCancelCard['card_setting'][PreCancelLogic::GUIDE_TYPE_NONE]['card_no_guide']['sub_title'] = $aTCIntercityConfig['sub_title'];
        }

        $iFromGuideType = PreCancelLogic::transGuideType($aOrderInfo['product_id'], $aOrderInfo['require_level'], $aOrderInfo['combo_type']);

        // 优先从文案平台获取（DCMP）并填充到$aPreCancelCard中
        switch (true) {
            case \BizCommon\Utils\Order::isSpecialRateV2($aOrderInfo): //no break
            case \BizCommon\Utils\Common::isRegionalWeChatMiniProgram($iAccessKeyId):
                $iFromGuideType = 4;
                $sCancelTips    = Language::getTextFromDcmp('special_rate-cancel_tips_for_precancel_api');
                $aCancelTips    = [];
                if ($sCancelTips) {
                    $aCancelTips = json_decode($sCancelTips, true);
                }

                $aPreCancelCard['card_setting'][PreCancelLogic::GUIDE_TYPE_SPECIAL] = $aCancelTips;
                break;
            default:
                break;
        }

        $aSetting = empty($aPreCancelCard['card_setting'][$iFromGuideType]) ? $aPreCancelCard['card_setting'][0] : $aPreCancelCard['card_setting'][$iFromGuideType];

        // 超值出租车，aRetentionInfo为空
        if (in_array($aOrderInfo['product_id'],[OrderSystem::PRODUCT_ID_UNITAXI, OrderSystem::PRODUCT_ID_BUSINESS_TAXI_CAR])
            && CarLevel::DIDI_UNITAXI_PUTONG_CAR_LEVEL == $aOrderInfo['require_level']
            && Horae::TYPE_COMBO_DEFAULT == $aOrderInfo['combo_type']
            && 1 == $aOrderInfo['is_special_price']
        ) {
            $sConfirmTextTips = Language::getTextFromDcmp('special_price_union-cancel_tips_for_precancel_api');
            $aSetting         = json_decode($sConfirmTextTips, true);
        }

        // 霸王花有司机拉单取消挽留
        if ($this->_isKFOrderPulled($aOrderInfo)) {
            $aKFOrderPulledConf        = NuwaConfig::text('config_passenger', 'kf_order_pulled_pre_cancel_card');
            $aSetting['card_no_guide'] = array_merge($aSetting['card_no_guide'], $aKFOrderPulledConf);
        }

        // 区域渗透取消挽留
        if (Common::isRegionalWeChatMiniProgram($iAccessKeyId) && Channel::isRegionalGrowthWxMiniProgram($aOrderInfo['channel'])) {
            $sConfirmTextTips          = (new PreCancelLogic())->getRegionalGrowthRetentionInfo($aOrderInfo, PreCancelLogic::STAGE_PRE_CANCEL, $this->_sToken);
            $aSetting['card_no_guide'] = array_merge($aSetting['card_no_guide'], $sConfirmTextTips);
        }

        // 等待应答6.0拼车、快车排队预取消弹窗
        if ($this->_bV6dot0) {
            $this->_aAthenaInfo = $this->_getAthenaInfo($aOrderInfo);

            // 推荐拼友、载人车推荐后 优先派挽留
            $aCarpoolPriority = $this->_carpoolPriority($this->_aAthenaInfo);
            if (!empty($aCarpoolPriority)) {
                return $aCarpoolPriority;
            }

            // 等待应答6.0快车排队预取消弹窗，此处拼车过滤逻辑比较复杂直接全量请求了Athena，因此快车不再单独请求直接使用结果
            if (UtilHelper::checkOrderExtraType($aOrderInfo['extra_type'], ['o_lineup'])
            ) {
                if ($this->_confirmQueueCarType($aOrderInfo)
                    && !LineUpOrderComModel::getInstance()->isFastCarQueueNewForm($aOrderInfo, $sAppVersion)
                ) {
                    $aFastCarQueuePreCancel = $this->_buildFastCarLineUpCancelInfo($aOrderInfo, $this->_aAthenaInfo);
                    if (!empty($aFastCarQueuePreCancel)) {
                        $aSetting['card_no_guide'] = $aFastCarQueuePreCancel;
                    }
                }
            }

            // 新沉流优先派用户取消 (过滤非中文版与预约单）
            if ($this->_isNewSilencePassenger($aOrderInfo, $sLang)) {
                if (empty($aSetting['card_no_guide']['sub_title'])) {
                    $aSetting['card_no_guide']['sub_title'] = '';
                } else {
                    $aSetting['card_no_guide']['sub_title'] .= "\n";
                }
            } elseif ($this->_isGaokaoPriorityOrder($aOrderInfo)) {
                if (empty($aSetting['card_no_guide']['sub_title'])) {
                    $aSetting['card_no_guide']['sub_title'] = Language::getTextFromDcmp('config_passenger-gaokao_priority_pre_cancel');
                } else {
                    $aSetting['card_no_guide']['sub_title'] .= "\n" . Language::getTextFromDcmp('config_passenger-gaokao_priority_pre_cancel');
                }
            }
        }

        // 如果是5.0单勾特惠
        $bIsSingleSpecialRate = $this->_isSingleSpecialRate($aOrderInfo);
        if ($this->_bV5dot0 && $bIsSingleSpecialRate) {
            $this->_aAthenaInfo = $this->_getAthenaInfoV5($aOrderInfo);
        }

        list($bHasReward, $aBonusInfo) = $this->_getNotTakeLikeWaitBonus($aOrderInfo);
        if ($bHasReward) {
            $iBonusForm = $aBonusInfo['bonus_form'] ?? 0;
            if (2 == $iBonusForm && $this->_bV6dot0 && $this->_isHoldFinished($this->_aAthenaInfo)) {
                $iStartTime = $aBonusInfo['start_time'] ?? 0;
                $iDuration  = $aBonusInfo['duration'] ?? 0;
                $iAmount    = $aBonusInfo['amount'] ?? 0;
                // 是否已等到时间
                $iScene = ($iStartTime + $iDuration > time()) ? 0 : 1;
                // 文案信息
                $aText = NuwaConfig::text('config_passenger', 'express_like_wait_cost_reduce_pre_cancel', ['amount' => sprintf('%.2f',($iAmount / 100.0))]);
                // 取文案成功更新卡片信息
                if (!empty($aText)) {
                    return [
                        'title'                => $aText['title'][$iScene] ?? '',
                        'sub_title'            => $aText['sub_title'] ?? '',
                        'cancel_button_title'  => $aText['cancel_button_title'] ?? '',
                        'confirm_button_title' => $aText['confirm_button_title'] ?? '',
                        'bg_icon'              => $aText['bg_icon'][$iScene] ?? '',
                    ];
                }
            } else {
                $aLikeWaitBonusCancel      = Language::getTextFromDcmp('pre_cancel-like_wait_bonus');
                $aSetting['card_no_guide'] = array_merge($aSetting['card_no_guide'], json_decode($aLikeWaitBonusCancel, true) ?? []);
            }
        }

        if (empty($aRetentionInfo)) {
            $aCard = $aSetting['card_no_guide'] ?? [];
            //半指派取消挽留
            $iSpecialRetainTime = $this->_getSpecialRetainTime();
            if (!empty($iSpecialRetainTime)) {
                $aCard = $this->_getSpecialRetainCardInfo($iSpecialRetainTime, $aCard);
            }

            //hack愿等逻辑ab实验文案
            if ($aCard && UtilHelper::checkOrderExtraType($aOrderInfo['extra_type'], ['o_carpool'])) {
                $aCard = $this->_getCarpoolCancelText($aCard, $aOrderInfo, $sAppVersion, $iClientType, $iAccessKeyId);
            }
        } else {
            $aCard = $aSetting['card_guide'] ?? [];
        }

        // 出租车等应答立减金取消挽留(发单时单勾出租车场景)
        $oPreCancelLogic = new PreCancelLogic();
        if ($oPreCancelLogic->isCityOpened($aOrderInfo)
            && $oPreCancelLogic->isPutongTaxi($aOrderInfo)
            && Language::ZH_CN != $sLang
        ) {
            $sTitle = $oPreCancelLogic->renderTitle($aOrderInfo);
            if (!empty($sTitle)) {
                $aCard['title'] = $sTitle;
            }
        }

        //tp 特惠单勾
        if ($oPreCancelLogic->isSpecialRateSingleTpForm($aOrderInfo)) {
            $sTitle           = '';
            $iCurTime         = strtotime('now');
            $iOrderCreateTime = strtotime($aOrderInfo['_create_time']);
            $aTpInfo          = $oPreCancelLogic::getSpecialRateTpInfoFromDos($aOrderInfo);
            if (!empty($aTpInfo)) {
                $aText      = json_decode(Language::getTextFromDcmp('config_text-special_rate_tp_cancel_content'), true);
                $iThreshold = $aText['time_threshold'] ?? 55;
                $sEts       = $aTpInfo['group_info']['spflash_ets'];
                $sEtp       = $aTpInfo['etp'] ?? '0';
                $sLeftEtp   = $aTpInfo['group_info']['spflash_left_etp'] ?? '';
                $sRightEtp  = $aTpInfo['group_info']['spflash_right_etp'] ?? '';
                $iEts       = $iOrderCreateTime + (int)$sEts;
                if ((int)$sEts > $iThreshold && $iEts % 60 > 0) {
                    $iEts = $iEts + 60 - $iEts % 60;
                }

                //预计上车点时间点
                if (empty($sLeftEtp) || empty($sRightEtp)) {
                    $iEtp = $iOrderCreateTime + (int)$sEtp * 60;
                } else {
                    $iEtp = $iOrderCreateTime + (int)$sRightEtp * 60;
                }

                if ($iEtp % 60 > 0) {
                    $iEtp = $iEtp + 60 - $iEtp % 60;
                }

                if ($iCurTime <= $iEtp) {
                    if ($iCurTime < $iEts) {
                        if ($iEts - $iCurTime <= $iThreshold) {
                            $iTimer = $iEts - $iCurTime;
                            if ($iTimer % 5 > 0) {
                                $iTimer = $iTimer + 5 - $iTimer % 5;
                            }

                            $sTitle = sprintf($aText['title_second'],$iTimer);
                        } else {
                            $iTimer = $iEts - $iCurTime;
                            if ($iTimer % 60 > 0) {
                                $iTimer = $iTimer + 60 - $iTimer % 60;
                            }

                            $sTitle = sprintf($aText['title_minute_01'],$iTimer / 60);
                        }
                    } else {
                        $iTimer = $iEtp - $iCurTime;
                        if ($iTimer % 60 > 0) {
                            $iTimer = $iTimer + 60 - $iTimer % 60;
                        }

                        $sTitle = sprintf($aText['title_minute_02'],$iTimer / 60);
                    }
                } else {
                    $sTitle = $aText['title_default'];
                }
            }

            if (!empty($sTitle)) {
                $aCard['title'] = $sTitle;
            }
        }

        return $aCard;
    }

    /**
     * @param array  $aOrderInfo 订单
     * @param string $sLang      l
     * @return array
     */
    private function _preCancelNoCarCompensation($aOrderInfo, $sLang, $bIsMiniProgramAnycar) {
        $preCancelLogic = new PreCancelLogic();
        // anycar 包含滴滴特快并且命中慢必赔
        if ($this->_bIsIncludeProduct($aOrderInfo, ProductCategory::PRODUCT_CATEGORY_APLUS)) {
            $aAplusCompensation = $preCancelLogic->getPreCancelAplusNoCarOrder($aOrderInfo, $sLang);
        }

        // anycar 包含普通出租车品类
        if ($this->_bIsIncludeProduct($aOrderInfo, ProductCategory::PRODUCT_CATEGORY_UNIONE)) {
            if (($preCancelLogic->isCityOpened($aOrderInfo))) {
                $aTaxiRewardCompensation = $preCancelLogic->getPreCancelTaxiRewardNoCarOrder($aOrderInfo, $sLang, $bIsMiniProgramAnycar);
            }
        }

        if (!empty($aAplusCompensation)) {
            $sTag = self::APLUS_NO_CAR_COMPENSATION;
        } else {
            $sTag = '';
        }

        $iAplusCountTime     = PHP_INT_MAX;
        $iDualPriceCountTime = PHP_INT_MAX;

        $iAplusCompensationValue     = 0;
        $iDualPriceCompensationValue = 0;

        if (!empty($aAplusCompensation)) {
            $iAplusCountTime         = $aAplusCompensation['count_time'];
            $iAplusCompensationValue = json_decode($aAplusCompensation['compensation_contents'], true)['coupon_amount'];
        }

        if (!empty($aDualPriceCompensation)) {
            $iDualPriceCountTime         = $aDualPriceCompensation['count_time'];
            $iDualPriceCompensationValue = json_decode($aDualPriceCompensation['compensation_content'], true)['amount'];
        }

        if (!empty($aTaxiRewardCompensation)) {
            $iTaxiCountTime = $aTaxiRewardCompensation['count_time'];
            $iMinCountTime  = min($iAplusCountTime, $iDualPriceCountTime);
            if ($iTaxiCountTime < $iMinCountTime && $iMinCountTime > 0 && $iTaxiCountTime > 0) {
                $sTag = self::TAXI_REWARD_COMPENSATIO;
            }

            $iTaxiCompensationValue = $aTaxiRewardCompensation['compensation_value'];
            if ($iTaxiCountTime == min($iAplusCountTime, $iDualPriceCountTime) && min($iAplusCountTime, $iDualPriceCountTime) > 0) {
                if ($iTaxiCompensationValue >= max($iAplusCompensationValue, $iDualPriceCompensationValue)) {
                    $sTag = self::TAXI_REWARD_COMPENSATIO;
                }
            }
        }

        $aResult = array();
        if (self::APLUS_NO_CAR_COMPENSATION == $sTag) {
            if (empty($aAplusCompensation)) {
                return [];
            }

            // 获取文案
            $aContents = json_decode($aAplusCompensation['compensation_contents'], true);
            $aExtra    = json_decode($aAplusCompensation['extra'], true);
            $aResult   = $preCancelLogic->buildAplusNoCarText($aAplusCompensation['count_time'], $aExtra['estimate_strive_time'], $aContents, $aOrderInfo['p_access_key_id']);
        }

        if (self::TAXI_REWARD_COMPENSATIO == $sTag) {
            $aResult = $aTaxiRewardCompensation['cancel_info'];
        }

        return $aResult;
    }

    /**
     * @desc 取消挽留时长
     * @return int
     */
    private function _getSpecialRetainTime() {
        $iRetainTime = 0;
        $iSceneFlag  = 0;
        $bIsShowed   = false;
        $iDuration   = 0;
        if ($this->_bV6dot0) {
            $aAthenaAnimation = $this->_aAthenaInfo['athena_animation_info'] ?? [];
            $aHoldInfo        = $aAthenaAnimation['animation_info'][0] ?? [];
            $iSceneFlag       = $aHoldInfo['animation_scene'] ?? 0;
            $bIsShowed        = empty($aHoldInfo['extra_info']['has_spFlash_recommend_retain']) ? false : true;
            $iDuration        = $aHoldInfo['duration'] ?? 0;
        }

        if ($this->_bV5dot0) {
            $aExtraInfo = $this->_aAthenaInfo['predict_time_card']['extra_info'] ?? [];
            if (!empty($aExtraInfo) && is_array($aExtraInfo)) {
                $iSceneFlag = $aExtraInfo['animation_scene'] ?? 0;
                $bIsShowed  = empty($aExtraInfo['has_spFlash_recommend_retain']) ? false : true;
                $iDuration  = $aExtraInfo['duration'] ?? 0;
            }
        }

        if (372008 == $iSceneFlag && !empty($iDuration) && !$bIsShowed) {
            $iRetainTime = (int)$iDuration;
        }

        return $iRetainTime;
    }

    /**
     * @param int   $iSpecialRetainTime SpecialRetainTime
     * @param array $aCard              Card
     * @return mixed
     */
    private function _getSpecialRetainCardInfo($iSpecialRetainTime, $aCard) {
        $aPreCancelCard      = NuwaConfig::text('config_passenger', 'pre_cancel_retain');
        $aCard['title']      = $aPreCancelCard['title'] ?? '';
        $aCard['count_time'] = $iSpecialRetainTime;
        $aCard['count_type'] = 2;//倒计时
        $aCard['sub_title']  = $aPreCancelCard['sub_title'] ?? '';
        $aCard['head_img']   = $aPreCancelCard['head_img'] ?? '';
        $aCard['toast_msg']  = $aPreCancelCard['toast_msg'] ?? '';
        $aCard['cancel_action_type'] = 1; //立即刷新等待应答接口
        $aCard['is_special_rate']    = 1; //是否特惠快车
        $aCard['bg_icon']            = $aPreCancelCard['bg_icon'] ?? '';

        return $aCard;
    }

    /** 拼车优先接场景
     * @param array $aAthenaInfo athena返回
     * @return array
     */
    private function _carpoolPriority($aAthenaInfo) {
        if (!is_array($aAthenaInfo['order_etp_etq_info'])) {
            return [];
        }

        foreach ($aAthenaInfo['order_etp_etq_info'] as $aInfo) {
            // 拼车优先接场景
            if (!empty($aInfo['queue_extra_para']['is_carpool_priority'])
                || (!empty($aInfo['queue_extra_para']['carpool_priority_countdown']))
            ) {
                $bIsCarpoolPriority        = $aInfo['queue_extra_para']['is_carpool_priority'];
                $iCarpoolPriorityCountdown = $aInfo['queue_extra_para']['carpool_priority_countdown'];
                $aText = json_decode(Language::getTextFromDcmp('pre_cancel-carpool_priority'), true);

                //即将优先派
                if (!$bIsCarpoolPriority && $iCarpoolPriorityCountdown > 0) {
                    $aText = $aText['pre'];
                    $aRet  = [
                        'title'                => $aText['title'],
                        'sub_title'            => $aText['subtitle'],
                        'cancel_button_title'  => $aText['cancel_button_title'],
                        'confirm_button_title' => $aText['confirm_button_title'],
                        'bg_icon'              => $aText['bg_icon'],
                    ];
                    return $aRet;
                }

                //正在优先派
                if ($bIsCarpoolPriority && $iCarpoolPriorityCountdown <= 0) {
                    $aText = $aText['post'];
                    $aRet  = [
                        'title'                => $aText['title'],
                        'sub_title'            => $aText['subtitle'],
                        'cancel_button_title'  => $aText['cancel_button_title'],
                        'confirm_button_title' => $aText['confirm_button_title'],
                        'bg_icon'              => $aText['bg_icon'],
                    ];
                    return $aRet;
                }
            }
        }

        return [];
    }
    /** 取消阻断导流
     * @param array $aOrderInfo orderinfo
     * @return bool
     */
    private function _isCardLinkText($aOrderInfo) {
        $iDepartureTimeSection = (int)strtotime($aOrderInfo['departure_time'] ?? 0) - time();
        if ((!BizCommonHorae::isPoolInTripCarpool($aOrderInfo)) && ($iDepartureTimeSection > 0) && ($iDepartureTimeSection < 1800)) {
            $oFeatureToggle = (new Apollo())->featureToggle(
                'carpool_precancel_guide_ab',
                [
                    Apollo::APOLLO_INDIVIDUAL_ID => $aOrderInfo['passenger_id'] ?? mt_rand(1,500),
                    'city'                       => $aOrderInfo['area'] ?? '',
                    'phone'                      => $aOrderInfo['passenger_phone'] ?? '',
                ]
            );
            if ($oFeatureToggle->allow() && 'treatment_group' == $oFeatureToggle->getGroupName()) {
                return true;
            }
        }

        return false;
    }

    /** 如果是可能有导流出口的场景，需要调该接口
     * @param array $aOrderInfo       orderinfo
     * @param array $aCardContentConf conf
     * @return mixed
     */
    private function _cardLinkReturnWrapper($aOrderInfo, $aCardContentConf) {
        if ($this->_isCardLinkText($aOrderInfo)) {
            return $aCardContentConf['card_link'];
        } else {
            return $aCardContentConf['card_no_link'];
        }
    }

    /** ETS阻断文案
     * @param array $aOrderInfo       orderinfo
     * @param array $aCardContentConf dcmp文案配置
     * @return bool|mixed
     */
    private function _etsIntercept($aOrderInfo, $aCardContentConf) {
        $oEts = new \BizCommon\Domain\CarpoolComponent\Model\Ets($aOrderInfo);
        $aTip = $oEts->getCancelTip();
        if (!empty($aTip['title']) && !empty($aTip['sub_title'])) {
            $aCardContentConf['card_link']['title']     = $aCardContentConf['card_no_link']['title'] = $aTip['title'];
            $aCardContentConf['card_link']['sub_title'] = $aCardContentConf['card_no_link']['sub_title'] = $aTip['sub_title'];
            return $this->_cardLinkReturnWrapper($aOrderInfo, $aCardContentConf);
        }

        return false;
    }

    /** 排队出口
     * @param array  $aOrderInfo       orderinfo
     * @param string $sAppVersion      appversion
     * @param array  $aCardContentConf dcmp文案
     * @return mixed
     */
    private function _lineUpIntercept($aOrderInfo, $sAppVersion, $aCardContentConf) {
        if (UtilHelper::checkOrderExtraType($aOrderInfo['extra_type'], 'o_lineup')
            && UtilHelper::compareAppVersion($sAppVersion, '6.0.12') >= 0
        ) {
            // 场景排除，权益卡生效前
            if (WaitingStage::waitingStageUsedCard($aOrderInfo) && (time() < WaitingStage::getNoMatchSendCarTime($aOrderInfo))) {
                return false;
            } else {
                $aRet = $aCardContentConf['pool_in_trip_line_up'];

                // pGetOrderMatchInfo缓存的队伍长度信息
                $sQueueLength = AthenaQueuePredictRepository::getQueueLength($aOrderInfo['order_id']);
                // redis请求失败兜底
                if (!empty($sQueueLength)) {
                    $aRet['sub_title'] = sprintf($aRet['sub_title'], $sQueueLength);
                } else {
                    $aRet['sub_title'] = '取消再叫车需重新排队，等待更久';
                }

                return $aRet;
            }
        }

        return false;
    }

    /**
     * 霸王花订单是否有司机拉单了
     *
     * @param array $aOrderInfo 订单信息
     *
     * @return bool
     */
    private function _isKFOrderPulled($aOrderInfo) {
        if (\BizLib\Utils\Product::PRODUCT_ID_K_FLOWER != $aOrderInfo['product_id']) {
            return false;
        }

        $oToggle = (new Apollo())->featureToggle(
            'kflower_order_pulled_cancel_retention',
            [
                'individual_id' => $aOrderInfo['passenger_id'],
                'pid'           => $aOrderInfo['passenger_id'],
                'phone'         => $aOrderInfo['passenger_phone'],
                'city'          => $aOrderInfo['area'],
            ]
        );

        if (!$oToggle->allow() || 'control_group' == $oToggle->getGroupName()) {
            return false;
        }

       // $iPulledFlag = RedisDB::getInstance()->get(UtilsCommon::getRedisPrefix(O_DRIVER_PULL_FLAG) . $aOrderInfo['order_id']);
       // if (empty($iPulledFlag)) {
           // return false;
       // }
        return true;
    }

    /**
     * @param array $aRetentionInfo aRetentionInfo
     * @return array
     */
    private function _getGuideListCard($aRetentionInfo) {
        if (empty($aRetentionInfo)) {
            return [];
        }

        $aPreCancelCard = NuwaConfig::text('config_passenger', 'pre_cancel_card');
        $aCardList      = [];
        foreach ($aRetentionInfo as $aRetention) {
            $iProductId   = \BizLib\Utils\Product::getProductIdByBusinessId($aRetention['guide_product']);
            $iGuideType   = PreCancelLogic::transGuideType($iProductId, $aRetention['guide_require_level'], $aRetention['guide_combo_type']);
            $sName        = $aPreCancelCard['guide_car_name'][$iGuideType] ?? '';
            $aCardSetting = empty($aPreCancelCard['car_guide_setting'][$iGuideType]) ? $aPreCancelCard['car_guide_setting'][0] : $aPreCancelCard['car_guide_setting'][$iGuideType];
            if (empty($sName) || empty($aCardSetting) || empty($aRetention['pre_price']) || empty($aRetention['wait_time'])) {
                continue;
            }

            $aParams    = [
                'name'      => $sName,
                'price'     => $aRetention['pre_price'],
                'wait_time' => $aRetention['wait_time'],
            ];
            $aGuideCard = [
                'guide_scene'   => $aRetention['guide_scene'],
                'business_id'   => $aRetention['guide_product'],
                'require_level' => $aRetention['guide_require_level'],
                'combo_type'    => $aRetention['guide_combo_type'],
                'title'         => Language::replaceTag($aCardSetting['title'], $aParams),
                'button_text'   => Language::replaceTag($aCardSetting['button'], $aParams),
                'confirm_info'  => [
                    'title'       => Language::replaceTag($aCardSetting['confirm_title'], $aParams),
                    'sub_title'   => Language::replaceTag($aCardSetting['confirm_sub_title'], $aParams),
                    'button_text' => Language::replaceTag($aCardSetting['confirm_button'], $aParams),
                ],
                'athena_id'     => $aRetention['athena_id'],
                'transparent'   => json_encode(
                    [
                        'estimate_trace_id' => (string)$aRetention['estimate_trace_id'],
                        'guide_scene'       => (int)$aRetention['guide_scene'],
                        'athena_id'         => (string)$aRetention['athena_id'],
                    ]
                ),
            ];
            if (PreCancelLogic::GUIDE_TYPE_CARPOOL == $iGuideType) {
                $aGuideCard['confirm_info']['seat_nums'] = [
                    [
                        'text'     => Language::replaceTag($aPreCancelCard['guide_carpool_seat_text'], ['seat_num' => 1]),
                        'num'      => 1,
                        'selected' => true,
                    ],
                    [
                        'text'     => Language::replaceTag($aPreCancelCard['guide_carpool_seat_text'], ['seat_num' => 2]),
                        'num'      => 2,
                        'selected' => false,
                    ],
                ];
            }

            $aCardList[] = $aGuideCard;
        }

        return $aCardList;
    }


    /**
     * 获取拼车取消阻断文案
     * @param array  $aCancelInfo  cancel_info
     * @param array  $aOrderInfo   order_info
     * @param string $sAppVersion  app_version
     * @param int    $iClientType  client_type
     * @param int    $iAccessKeyId access_key_id
     * @return mixed
     */
    private function _getCarpoolCancelText($aCancelInfo, $aOrderInfo, $sAppVersion, $iClientType, $iAccessKeyId) {
        $aExtraInfo = [
            'app_version' => $sAppVersion,
            'client_type' => $iClientType,
        ];
        $aMatchInfo = OrderStation::getInstance()->getHoldInfo($aOrderInfo, $aExtraInfo);
        if (!empty($aMatchInfo['cancel_text'])) {
            $aCancelInfo['sub_title'] = $aMatchInfo['cancel_text'];
        }

        //获取两口价愿等取消阻断文案
        $oApollo        = Apollo::getInstance();
        $oFeatureToggle = $oApollo->featureToggle(
            'carpool_like_wait_v3_1',
            [
                'key'           => $aOrderInfo['passenger_id'],
                'city'          => $aOrderInfo['area'],
                'phone'         => $aOrderInfo['passenger_phone'],
                'app_version'   => $sAppVersion,
                'access_key_id' => $iAccessKeyId,
                'time'          => time(),

            ]
        );
        //未命中实验对照组，返回之前线上的阻断文案
        if (!$oFeatureToggle->allow() || 'control_group' == $oFeatureToggle->getGroupName()) {
            return $aCancelInfo;
        }

        //返回新版阻断文案
        if ($aOrderInfo['is_dual_carpool_price'] && !empty($aMatchInfo['hold_time'])) {
            $iWaitSeconds = time() - strtotime($aOrderInfo['_birth_time']);
            $oRedisDB     = RedisDB::getInstance();
            $iHoldType    = (int)($oRedisDB->get(self::PREFIX_P_ORDER_HOLD_TYPE_CACHE . $aOrderInfo['order_id']));
            $aReplaceVars = [
                'wait_minute' => floor($iWaitSeconds / 60),
                'wait_second' => $iWaitSeconds % 60,
            ];
            //文案结构第一层key为愿等类型，第二层key为已等待时间分界点，例如以30s为分界点，等待30秒之内和超过30秒会返回不同的取消阻断文案
            //若未针对该愿等类型配置取消阻断文案，则返回默认配置
            $aCancelTips = json_decode(Language::getTextFromDcmp('config_passenger-pre_cancel_carpool_dual_hold', $aReplaceVars), true);
            if (isset($aCancelTips[$iHoldType]) && is_array($aCancelTips[$iHoldType])) {
                foreach ($aCancelTips[$iHoldType] as $iTime => $aCancelInfo) {
                    if ($iWaitSeconds <= (int)$iTime) {
                        return $aCancelInfo;
                    }
                }
            }
        }

        return $aCancelInfo;
    }

    /**
     * 检查Caller
     * @param string $sCaller sCaller
     * @return bool
     */
    private function _checkCaller($sCaller) {
        if ('h5' == $sCaller) {
            return true;
        }

        return false;
    }

    /**
     * @desc 判定是否单勾特惠
     * @param array $aOrderInfo OrderInfo
     * @return bool
     */
    private function _isSingleSpecialRate($aOrderInfo) {
        $aExtendFeature = json_decode($aOrderInfo['extend_feature'], true);
        if (!is_array($aExtendFeature) && empty($aExtendFeature)) {
            return false;
        }

        $aProducts = $aExtendFeature['multi_require_product'] ?? [];
        if (1 != count($aProducts)) {
            return false;
        }

        $aProduct = array_values($aProducts)[0];
        return \BizCommon\Utils\Order::isSpecialRateV2($aProduct);
    }

    /**
     * Function _confirmQueueCarType 排队车型确认（目前只用在快车和优享上）
     *
     * @param  array $aOrderInfo 订单信息
     * @return bool
     */
    private function _confirmQueueCarType($aOrderInfo) {
        if ($aOrderInfo['is_anycar']) {
            $aExtendFeature = json_decode($aOrderInfo['extend_feature'], true);
            $aProducts      = $aExtendFeature['multi_require_product'] ?? [];
            if (count($aProducts) > 1) {
                return false;
            }

            if (1 == count($aProducts)) {
                $aOrderInfo = AnyCarCommonLogic::mockSingleCar($aOrderInfo);
            }
        }

        // 目前仅快车和优享
        $aAllow = [ProductCategory::PRODUCT_CATEGORY_FAST, ProductCategory::PRODUCT_CATEGORY_YOUXIANG];
        // 计算pc
        $iProductCategory = (new ProductCategory())->getProductCategoryByNTuple(
            FieldOrderNTuple::getOrderNTupleByOrder($aOrderInfo)
        );

        if (in_array($iProductCategory, $aAllow)) {
            return true;
        }

        return false;
    }

    /**
     * @desc 新沉流用户
     * @param array $aOrderInfo aOrderInfo
     * @return bool
     */
    private function _isNewSilencePassengerLineup($aOrderInfo) {
        $aRecord = UfsRepository::getUfsFeature(
            ['priority_assign_flag'],
            ['order_id' => $aOrderInfo['order_id'],],
            'order'
        );

        $sPriority = $aRecord['priority_assign_flag'] ?? '';
        if (empty($sPriority)) {
            return false;
        }

        $aPriority = json_decode($sPriority, true);
        if (!empty($aPriority['new_silence_fast_car_priority'])) {
            return true;
        }

        return false;
    }

    /**
     * @param array $aOrderInfo orderInfo
     * @return bool
     */
    private function _isNewSilencePassengerNoReply($aOrderInfo) {
        $aRecord = UfsRepository::getUfsFeature(
            ['new_passenger.is_new_passenger'],
            ['order_id' => $aOrderInfo['order_id'],],
            'order'
        );
        if (!empty($aRecord['new_passenger.is_new_passenger'])) {
            return true;
        }

        return false;
    }

    /**
     * @param array $aOrderInfo orderInfo
     * @return bool
     */
    private function _isGaokaoPriorityOrder($aOrderInfo) {
        // ufs特征 + 快车排队
        if (!$this->isContainFastCar($aOrderInfo) || !$aOrderInfo['line_up']) {
            return false;
        }

        $aRecord = UfsRepository::getUfsFeature(
            ['exam_feature.is_gaokao_order'],
            ['order_id' => $aOrderInfo['order_id'],],
            'order'
        );
        if (!empty($aRecord['exam_feature.is_gaokao_order'])) {
            return true;
        }

        return false;

    }
    /**
     * @param array  $aOrderInfo orderInfo
     * @param string $sLang      language
     * @return bool
     */
    private function _isNewSilencePassenger($aOrderInfo, $sLang) {
        // 预约单无
        if (1 == $aOrderInfo['type']) {
            return false;
        }

        // 非中文版无
        if (Language::ZH_CN != $sLang) {
            return false;
        }

        // 只有包含快车或单发快车才有
        if (!$this->isContainFastCar($aOrderInfo)) {
            return false;
        }

        // 排队链路
        if ($aOrderInfo['line_up'] && $this->_isNewSilencePassengerLineup($aOrderInfo)) {
            return true;
        }

        // 非排队链路
        if (!$aOrderInfo['line_up'] && $this->_isNewSilencePassengerNoReply($aOrderInfo)) {
            return true;
        }

        return false;
    }

    /** 是否包含快车
     * @param array $aOrderInfo orderinfo
     * @return bool
     */
    public function isContainFastCar($aOrderInfo) {
        if (AnyCarCommonLogic::PRODUCT_KEY_KUAICHE == AnyCarCommonLogic::getGroupKey($aOrderInfo)) {
            return true;
        }

        $aExtendFeature = json_decode($aOrderInfo['extend_feature'], true);
        foreach ($aExtendFeature['multi_require_product'] as $key => $value) {
            if (AnyCarCommonLogic::PRODUCT_KEY_KUAICHE == $key) {
                return true;
            }
        }

        return false;
    }


    /**
     * @desc 新沉流优先派文案
     * @return array
     */
    private function _buildNewSilenceLineUpCancelInfo() {
        $aText = NuwaConfig::text('config_passenger', 'fast_car_line_up_pre_cancel_text');
        return [
            'title'                => $aText['cancel_title_new_silence'],
            'sub_title'            => $aText['cancel_text_new_silence'],
            'cancel_button_title'  => $aText['cancel_button_title'],
            'confirm_button_title' => $aText['confirm_button_title'],
            'bg_icon'              => $aText['cancel_emotional_img_url'],
        ];
    }

    /**
     * Function _buildFastCarLineUpCancelInfo 快车排队预取消信息构建
     * 排队预取消弹窗原来耦合在ordermatch的返回中，等待应答6.0将其收敛至此（与其他场景/车型保持一致）
     * @param  array $aOrderInfo       订单信息
     * @param  array $aAthenaQueueInfo Athena返回的排队信息
     * @return array
     */
    private function _buildFastCarLineUpCancelInfo($aOrderInfo, $aAthenaQueueInfo) {
        $aText = NuwaConfig::text('config_passenger', 'fast_car_line_up_pre_cancel_text');

        // 从Athena获取排队信息，外层全局请求了，此处不再请求了
        //$aAthenaQueueInfo = $this->_getAthenaInfo($aOrderInfo);
        // etp/q信息
        $aEtqEtpInfo = $aAthenaQueueInfo['order_etp_etq_info'][0] ?? [];
        // 未获取到文案或者ETQ信息返回空
        if (empty($aText) || empty($aEtqEtpInfo) || !$this->_isHoldFinished($aAthenaQueueInfo)) {
            return [];
        }

        // 排位
        $iRank = (int)($aEtqEtpInfo['rank'] ?? 0);
        // 队列长度
        $iQueueLength = (int)($aEtqEtpInfo['queue_length'] ?? 0);
        // 等待时间
        $iWaitTime = (int)($aEtqEtpInfo['wait_time'] ?? 0);
        // 熔断类型
        $sBlurryType = $this->_getBlurryType($iRank, $iWaitTime);
        // 播单相关信息
        $aScheduleData = $aAthenaQueueInfo['schedule_info'] ?? [];

        // 1. 用户在#1，共1人排队
        // 2. 用户在#1，共X人排队（X>1）
        // 3. 用户在#2~5，共Y人排队（Y>用户当前排位2倍，例如：Y>10）
        // 4. 其他
        $sCancelTitle = $aText['cancel_title'];
        $sCancelText  = Language::replaceTag(
            $aText['cancel_text'],
            ['num' => $iQueueLength,]
        );

        $sCancelBtnTitle  = $aText['cancel_button_title'];
        $sConfirmBtnTitle = $aText['confirm_button_title'];

        if (1 === $iRank) {
            $sCancelTitle = $aText['cancel_title_1'];
            if (1 === $iQueueLength) {
                $sCancelText = $aText['cancel_text_1'];
            }
        } elseif ($iRank >= 2 && $iRank <= 5 && $iQueueLength > ($iRank * 2)) {
            $sCancelTitle = Language::replaceTag(
                $aText['cancel_title_x'],
                ['num' => $iRank]
            );
        }

        $iScheduledDriverNum = $aScheduleData['driver_cnt'];
        $iSchedulePeriod     = $aScheduleData['schedule_period'];
        $iSchedulePeriod     = empty($iSchedulePeriod) ? 1 : (int)($iSchedulePeriod / 60);
        // 1.当用户排在第一位时不显示调度信息
        if (0 === $aScheduleData['err_no'] && $iScheduledDriverNum && $iRank > 1
            && $iScheduledDriverNum / $iQueueLength <= 2
            && 'zh-CN' == $this->getRequest()->getQuery('lang', false)
            && $this->_openDriverScheduleInfo($aOrderInfo)
        ) {
            $sCancelTitle = $aText('cancel_title_v2');
            if ($iScheduledDriverNum < 100) {
                $sCancelText = Language::replaceTag(
                    $aText['cancel_text_with_scheduled_driver_num'],
                    [
                        'schedule_period' => $iSchedulePeriod,
                        'driver_cnt'      => $iScheduledDriverNum,
                        'all'             => $iQueueLength,
                    ]
                );
            } else {
                $sCancelText = Language::replaceTag(
                    $aText['cancel_text_with_scheduled_driver_num_ht_100'],
                    [
                        'schedule_period' => $iSchedulePeriod,
                        'all'             => $iQueueLength,
                    ]
                );
            }
        }

        // 排队等待时间过长或是排名超过200，模糊文案
        if (self::BLURRY_TYPE_TIME == $sBlurryType) {
            $sCancelTitle = $aText['cancel_title'];
            $sCancelText  = $aText['cancel_text_blur_time'];
        }

        if (self::BLURRY_TYPE_RANK == $sBlurryType) {
            $sCancelTitle = $aText['cancel_title'];
            $sCancelText  = $aText['cancel_text_blur_rank'];
        }

        $sEmotionalImg = $aText['cancel_emotional_img_url'];

        return [
            'title'                => $sCancelTitle,
            'sub_title'            => $sCancelText,
            'cancel_button_title'  => $sCancelBtnTitle,
            'confirm_button_title' => $sConfirmBtnTitle,
            'bg_icon'              => $sEmotionalImg,
        ];

    }

    /**
     * Function _isHoldFinished hold动画是否播放完毕
     * hold动画播放完毕之前预取消不出排队信息
     * @param  array $aAthenaQueueInfo Athena排队信息
     * @return bool(true-播放完毕，false-hold未播放完)
     */
    private function _isHoldFinished($aAthenaQueueInfo) {
        // Athena动画信息
        $aAthenaAnimation = $aAthenaQueueInfo['athena_animation_info'] ?? [];
        // 获取hold动画
        $aHoldInfo  = $aAthenaAnimation['animation_info'][0] ?? [];
        $iStartTime = $aAthenaAnimation['start_time'] ?? 0;
        $iCurTime   = $aAthenaAnimation['cur_time'] ?? 0;
        $iSceneFlag = $aHoldInfo['animation_scene'] ?? 0;

        if (empty($aHoldInfo) || -1 != $iSceneFlag) {
            $iDuration = 0;
        } else {
            $iDuration = $aHoldInfo['duration'] ?? 0;
            //动画是否播放完成
            if ($iCurTime - $iStartTime >= $iDuration) {
                $iDuration = 0;
            }
        }

        return (0 == $iDuration);
    }

    /**
     * Function _getBlurryType 获取熔断类型
     *
     * @param  int $iRank     排位
     * @param  int $iWaitTime 等待时间
     * @return string
     */
    private function _getBlurryType($iRank, $iWaitTime) {
        $sBlurryType = self::BLURRY_TYPE_NULL;

        if ($iWaitTime >= self::QUEUE_TIME_BLURRY_LIMIT) {
            $sBlurryType = self::BLURRY_TYPE_TIME;
        }

        if ($iRank >= self::QUEUE_RANK_BLURRY_LIMIT) {
            $sBlurryType = self::BLURRY_TYPE_RANK;
        }

        return $sBlurryType;

    }

    /**
     * Function _getAthenaInfo 获取Athena返回信息
     *
     * @param array $aAthenaBaseParams 订单信息
     * @return array
     * @throws ExceptionWithResp
     */
    private function _getAthenaInfo($aAthenaBaseParams) {
        //参数组装
        $sAppVersion = $this->getRequest()->getQuery('app_version', false);

        if (empty($sAppVersion)) {
            $sAppVersion = $this->getRequest()->getQuery('appversion', false);
        }

        $aAthenaBaseParams['app_version']   = $sAppVersion;
        $aAthenaBaseParams['access_key_id'] = $this->getRequest()->getQuery('access_key_id', false);

        // 可导流产品线
        $aMultiRequireProduct = BothCallAnycarLogic::getInstance()->getShowMultiRequireProduct($aAthenaBaseParams);
        if ($this->_iterationSwitch($this->_oMatchResultRuntime->getRequest(), $this->_oMatchResultRuntime->getOrderInfo())) {
            $this->_oMatchResultRuntime->oAnycarEstimateViewModel->buildOnce();
            if (!empty($this->_oMatchResultRuntime->oAnycarEstimateViewModel->aAnycarEstimate['layout'])) {
                //屏蔽下挂展示的品类，因为取消挽留这里端上处理不了下挂的品类
                $this->_oMatchResultRuntime->oAnycarEstimateViewModel->aAnycarEstimate['layout'] = $this->_delLinkInfo($this->_oMatchResultRuntime->oAnycarEstimateViewModel->aAnycarEstimate['layout']);
            }
            $aAnycarEstimate = $this->_oMatchResultRuntime->oAnycarEstimateViewModel->getProductList();
            // 处理预估价格
            $aMultiRequireProduct = PriceRepository::getAnycarWithEstimateInfo(
                $aAnycarEstimate,
                $this->_oMatchResultRuntime->getOrderInfo(),
                $this->_oMatchResultRuntime->getRequest(),
                $aMultiRequireProduct,
                $this->_oMatchResultRuntime->isIterationSwitchV3()
            );
        }

        // 用户已选择产品线
        $aMultiOrderProduct = $this->_getUserCalledProductList($aAthenaBaseParams);

        // 可导流产品线
        $aAthenaBaseParams['multi_add_product'] = $this->_buildAthenaOrderMatchProductLine($aMultiRequireProduct, 0);
        // 用户已选择产品线
        $aAthenaBaseParams['multi_order_product'] = $this->_buildAthenaOrderMatchProductLine($aMultiOrderProduct, 1);

        //extraInfo中加一个标记告诉Athena此次请求是预取消请求的
        if (empty($aAthenaBaseParams['extra_info'])) {
            $aAthenaBaseParams['extra_info'] = ['is_from_pre_cancel_order' => '1',];
        } else {
            $aAthenaBaseParams['extra_info']['is_from_pre_cancel_order'] = '1';
        }

        // 弹层新样式
        if ($this->_oMatchResultRuntime->bNewPopExam) {
            $aAthenaBaseParams['extra_info']['new_pre_cancel_strategy'] = '1';
        }


        $aAthenaParams = [
            'order_id'            => UtilHelper::genHighIntOrderId($aAthenaBaseParams['order_id'], $aAthenaBaseParams['district']),
            'pid'                 => $aAthenaBaseParams['passenger_id'],
            'city_id'             => $aAthenaBaseParams['area'],
            'lang'                => $this->getRequest()->getQuery('lang',false),
            'phone'               => $aAthenaBaseParams['passenger_phone'],
            'client_type'         => $this->getRequest()->getQuery('client_type', false),
            'app_version'         => $sAppVersion,
            'access_key_id'       => $this->getRequest()->getQuery('access_key_id', false),
            'order_type'          => $aAthenaBaseParams['type'],
            'from_lng'            => $aAthenaBaseParams['starting_lng'],
            'from_lat'            => $aAthenaBaseParams['starting_lat'],
            'to_lng'              => $aAthenaBaseParams['dest_lng'],
            'to_lat'              => $aAthenaBaseParams['dest_lat'],
            'app_inactive'        => $this->getRequest()->getQuery('app_inactive', false),
            'is_queue'            => UtilHelper::checkOrderExtraType($aAthenaBaseParams['extra_type'], array('o_lineup')) ? 1 : 0,
            'menu_id'             => $this->getRequest()->getQuery('menu_id', false),
            'is_anycar'           => $aAthenaBaseParams['is_anycar'],
            'multi_add_product'   => $aAthenaBaseParams['multi_add_product'] ?? [],
            'multi_order_product' => $aAthenaBaseParams['multi_order_product'] ?? [],
            'extra_info'          => $aAthenaBaseParams['extra_info'],
        ];

        $bHasPopupRecommend = true;
        // 如果用户点击追加车型时命中预付拦截，告知Athena不出弹窗推荐
        $sMultiRequireInterceptResultKey = UtilsCommon::getRedisPrefix(O_MULTI_REQUIRE_PRODUCT_INTERCEPT_RESULT).$this->getRequest()->getQuery('order_id', false);
        $aInterceptHandleResult          = PrepayInterceptResultRepository::getPrepayInterceptResult($sMultiRequireInterceptResultKey);
        if (!empty($aInterceptHandleResult)) {
            // 告知athena不用出弹窗推荐
            $aAthenaParams['extra_info']['has_popup'] = self::NOT_SHOW_POP_UP_RECOMMEND;
            $bHasPopupRecommend = false;
        }

        // 如果athena有弹窗推荐且通过灰度开关，根据灰度开关判定是否需要请求反作弊接口
        $bNeedCallNewton = self::_callNewtonForAppendingCarSwitch($this->getRequest(), $aAthenaBaseParams);
        if ($bNeedCallNewton && $bHasPopupRecommend) {
            // 判定是否允许在追加车型阶段请求反作弊接口
            if (self::_allowCallNewtonProductInAppendCar($aAthenaBaseParams, $aMultiRequireProduct)) {
                // 先从Redis中获取结果
                $sNewtonResultRedisKey = UtilsCommon::getRedisPrefix(O_MULTI_ADD_PRODUCT_INTERCEPT_RESULT) . $aAthenaBaseParams['order_id'];
                $aNewtonResult         = PrepayInterceptResultRepository::getPrepayInterceptResult($sNewtonResultRedisKey);
                if (!empty($aNewtonResult)) {
                    $aAthenaParams = self::_handleAthenaReqByNewtonResp($aNewtonResult, $aAthenaParams);
                    NuwaLog::info(sprintf('hit append car intercept cache in order match|order_id:%s|redis key:%s', $aAthenaBaseParams['order_id'], $sNewtonResultRedisKey));
                } else {
                    // 请求newton服务的反作弊接口获取拦截结果
                    $aRet = self::_getInterceptResultFromNewton($aAthenaBaseParams, $aMultiRequireProduct, $aMultiOrderProduct);
                    // 接口请求失败不写入redis，下次轮询重新请求安全侧反作弊接口
                    if ($aRet && 0 == $aRet['errno']) {
                        // 将拦截结果存入Redis
                        PrepayInterceptResultRepository::setPrepayInterceptResult($aAthenaBaseParams['order_id'], $aRet);
                        $aAthenaParams = self::_handleAthenaReqByNewtonResp($aRet, $aAthenaParams);
                    }

                    NuwaLog::info(sprintf('miss append car intercept cache in order match|order_id:%s|redis key:%s', $aAthenaBaseParams['order_id'], $sNewtonResultRedisKey));
                }
            }
        }

        // 请求Athena的参数新增screen_height,如果获取不到透传0值给Athena
        $aAthenaParams['extra_info']['screen_height']          = (string)($this->getRequest()->getQuery('height', '0'));
        $aAthenaParams['extra_info']['is_wait_answer_upgrade'] = (string)($this->getRequest()->getQuery('is_wait_answer_upgrade', '0'));

        if ($this->_iterationSwitch($this->_oMatchResultRuntime->getRequest(), $this->_oMatchResultRuntime->getOrderInfo())) {
            $aAthenaParams['is_from_pre_cancel_order'] = 1;
        }

        $aAthenaResult = (new AthenaApiV3Client())->orderMatchInfoV6($aAthenaParams);
        if (isset($aAthenaResult['errno']) && 0 == $aAthenaResult['errno']) {
            return $aAthenaResult;
        }

        return [];
    }

    /**
     * 调用newton服务反作弊接口获取拦截结果
     * @param array $aAthenaBaseParams  订单信息
     * @param array $aMultiAddProduct   可导流的产品线
     * @param array $aMultiOrderProduct 已选择的产品线
     * @return mixed
     * @throws ExceptionWithResp
     */
    private function _getInterceptResultFromNewton($aAthenaBaseParams, $aMultiAddProduct, $aMultiOrderProduct) {
        // 请求Athena之前调用风控接口，自选车、必有车、花小猪、企业级、pbd需要全部过滤
        $aPassengerInfo = self::_getAndCheckPassengerInfo();
        $aRequestParam  = array();
        $aRequestParam['order_info']     = $this->_prepareOrderInfo($aAthenaBaseParams, $aPassengerInfo, $aMultiAddProduct, $aMultiOrderProduct);
        $aRequestParam['estimate_info']  = $this->_prepareEstimateInfo($aAthenaBaseParams);
        $aRequestParam['app_info']       = $this->_prepareAppInfo($aAthenaBaseParams, $aPassengerInfo);
        $aRequestParam['passenger_info'] = $this->_preparePassengerInfo($aPassengerInfo);

        $oNewtonCommonClient = new NewtonCommonClient();
        $aRet = $oNewtonCommonClient->newOrder($aRequestParam);
        if (false === $aRet || 0 != $aRet['errno']) {
            // 访问newton系统时出现网络问题，继续为用户展示可推荐追加的车型
            $aError = UtilsCommon::getErrMsg(CREATEORDER_MINOS_CHECK_NETWORK_ERROR);
            NuwaLog::warning(sprintf('errno:%s|errmsg:%s|req:%s|ret:%s', $aError['errno'], $aError['errmsg'], json_encode($aRequestParam), json_encode($aRet)));
        }

        return $aRet;
    }

    /**
     * 根据Newton反作弊接口的返回处理请求Athena的参数
     * @param array $aNewtonResp   反作弊处理结果
     * @param array $aAthenaParams athena请求参数
     * @return array
     */
    private function _handleAthenaReqByNewtonResp($aNewtonResp, $aAthenaParams) {
        if (isset($aNewtonResp['is_spam'], $aNewtonResp['antispam_action']) && 1 === $aNewtonResp['is_spam']) {
            switch ($aNewtonResp['antispam_action']) {
                case self::SILENCE_INTERCEPT: // no break
                case self::REQUIRE_PREPAY:
                    // 告知athena不用出弹窗推荐
                    $aAthenaParams['extra_info']['has_popup'] = '1';
                    break;
                default:
                    break;
            }
        }

        return $aAthenaParams;
    }

    /**
     * 判定当前对应是否允许在追加车型阶段请求反作弊接口
     * @param array $aAthenaBaseParams 订单信息
     * @return bool
     */
    private function _allowCallNewtonProductInAppendCar($aAthenaBaseParams, $aMultiRequireProduct) {
        // 企业级
        $bIsBusiness = UtilProduct::isBusiness($aAthenaBaseParams['product_id']);
        // 必有车
        $bIsMustHasCar = '1' == $aAthenaBaseParams['is_pick_on_time'];
        // pbd渠道
        $bIsPbd = UtilHelper::isPbdChannel($aAthenaBaseParams['channel']);
        // 花小猪
        $bIsKFlower    = UtilCommon::isFromKFlower($this->getRequest()->getQuery('access_key_id', false));
        $bHasOptionCar = false;
        // 自选车，自选车的话需要判定可追加的车型中是否有自选车
        foreach ($aMultiRequireProduct as $oProduct) {
            if (OrderNTuple::COMMON_PRODUCT_ID_BARGAIN_CAR == $oProduct['business_id']
                && OrderNTuple::DIDI_BARGAIN_CAR_LEVEL == $oProduct['require_level']
            ) {
                $bHasOptionCar = true;
                break;
            }
        }

        // 可导流车型列表不为数组或者容量为空，直接不请求反作弊接口
        if (!is_array($aMultiRequireProduct) || empty($aMultiRequireProduct)) {
            return false;
        }

        if ($bIsBusiness || $bIsMustHasCar || $bIsPbd || $bIsKFlower || $bHasOptionCar) {
            return false;
        }

        return true;
    }

    /**
     * 获取反作弊接口需要的订单信息
     * @param array $aAthenaBaseParams  订单信息
     * @param array $aPassengerInfo     乘客信息
     * @param array $aMultiAddProduct   可导流的产品线
     * @param array $aMultiOrderProduct 已选择的产品线
     * @return false|string
     * @throws ExceptionWithResp
     */
    private function _prepareOrderInfo($aAthenaBaseParams, $aPassengerInfo, $aMultiAddProduct, $aMultiOrderProduct) {
        $iExtraType      = $aAthenaBaseParams['extra_type'];
        $iTmpTrafficType = $this->_order->isGsAirportOrder($iExtraType);
        $iPid            = $aPassengerInfo['passenger_id'] ?? $aPassengerInfo['pid'];
        $aMultiAddProductWithGroupKey = array();
        foreach ($aMultiAddProduct as $oProduct) {
            $oProduct['product_id'] = Product::getProductIdByBusinessId($oProduct['business_id']);
            $sGroupKey = AnyCarCommonLogic::getGroupKey($oProduct);
            $aMultiAddProductWithGroupKey[$sGroupKey] = $oProduct;
            $sEstimateId  = $oProduct['sub_estimate_id'];
            $aPriceParams = [
                'estimate_id' => $sEstimateId,
                'fields'      => [
                    'exact_estimate_fee',
                    'cap_price',
                    'dynamic_times',
                    'pre_total_fee',
                    'dynamic_total_fee',
                    'red_packet',
                ],
            ];

            if (!empty($sEstimateId)) {
                $aQuotation = (new PriceApiClient(Client::MODULE_NAME))->getQuotation($aPriceParams);
            }
            if (isset($aQuotation['errno']) && 0 == $aQuotation['errno'] &&!empty($aQuotation['data'])) {
                $aMultiAddProductWithGroupKey[$sGroupKey]['estimate_fee']  = $aQuotation['data']['exact_estimate_fee'];
                $aMultiAddProductWithGroupKey[$sGroupKey]['cap_price']     = $aQuotation['data']['cap_price'];
                $aMultiAddProductWithGroupKey[$sGroupKey]['dynamic_times'] = $aQuotation['data']['dynamic_times'];
                $iPreTotalFee = $aQuotation['data']['pre_total_fee'];
                $aMultiAddProductWithGroupKey[$sGroupKey]['pre_total_fee'] = 0 != $iPreTotalFee ? $iPreTotalFee : $aQuotation['data']['dynamic_total_fee'];
                $aMultiAddProductWithGroupKey[$sGroupKey]['red_packet']    = $aQuotation['data']['red_packet'];
                $aMultiAddProductWithGroupKey[$sGroupKey]['group_key']     = $sGroupKey;
            }
        }

        $aOrderInfo = [
            'passenger_id'               => $iPid,
            'caller_phone'               => $aPassengerInfo['phone'],
            'order_id'                   => $aAthenaBaseParams['order_id'],
            'district'                   => $aAthenaBaseParams['district'],
            'area'                       => $aAthenaBaseParams['area'],
            'product_id'                 => $aAthenaBaseParams['product_id'],
            'passenger_phone'            => $aAthenaBaseParams['passenger_phone'],
            'type'                       => $aAthenaBaseParams['type'],
            'channel'                    => $aAthenaBaseParams['channel'],
            'current_lng'                => $aAthenaBaseParams['current_lng'],
            'current_lat'                => $aAthenaBaseParams['current_lat'],
            'starting_lng'               => $aAthenaBaseParams['starting_lng'],
            'starting_lat'               => $aAthenaBaseParams['starting_lat'],
            'dest_lng'                   => $aAthenaBaseParams['dest_lng'],
            'dest_lat'                   => $aAthenaBaseParams['dest_lat'],
            'departure_time'             => $aAthenaBaseParams['departure_time'],
            '_create_time'               => $aAthenaBaseParams['_create_time'],
            '_birth_time'                => date('Y-m-d H:i:s', time()),
            'combo_type'                 => $aAthenaBaseParams['combo_type'],
            'scene_type'                 => (int)(BizLib\Utils\Horae::getSceneType((int)($aAthenaBaseParams['combo_type']))),
            'carpool_type'               => $aAthenaBaseParams['carpool_type'],
            'pay_type'                   => $aAthenaBaseParams['pay_type'],
            'require_level'              => $aAthenaBaseParams['require_level'],
            'extra_type'                 => $iExtraType,
            'callcar_type'               => $aAthenaBaseParams['call_car'],
            'starting_name'              => $aAthenaBaseParams['starting_name'],
            'dest_name'                  => $aAthenaBaseParams['dest_name'],
            'county'                     => $aAthenaBaseParams['county'],
            'traffic_type'               => empty($iTmpTrafficType) ? $aAthenaBaseParams['combo_type'] : $iTmpTrafficType,
            'multi_require_product'      => !empty($aMultiAddProductWithGroupKey) ? $aMultiAddProductWithGroupKey : [],
            'is_anycar'                  => (int)($aAthenaBaseParams['is_anycar']),
            'partner_id'                 => (int)($aAthenaBaseParams['partner_id']),
            'railway_type'               => (int)($aAthenaBaseParams['railway_type']),
            'airport_type'               => (int)($aAthenaBaseParams['airport_type']),
            'loss_remand'                => (int)($aAthenaBaseParams['loss_remand']),
            'long_rent_type'             => (int)($aAthenaBaseParams['long_rent_type']),
            'has_no_car'                 => 0, // 是否有车, 1代表无车
            'scan_code_type'             => (int)($aAthenaBaseParams['scan_code_type']),
            'is_mixed_payment'           => $aAthenaBaseParams['is_mixed_payment'],
            'estimate_id'                => $aAthenaBaseParams['estimate_id'],
            'level_type'                 => $aAthenaBaseParams['level_type'],
            'is_tc_intercity_scan_order' => $aAthenaBaseParams['is_tc_intercity_scan_order'] ?? 0,
            // 个性化出行-风控数据同步
            'scene_list'                 => $aAthenaBaseParams['scene_list'],
            'start_product'              => !empty($aMultiOrderProduct) ? $aMultiOrderProduct : [],
            'bargain_node'               => self::CALL_NEWTON_BEFORE_ATHENA_BARGAIN_NODE,
        ];

        return json_encode($aOrderInfo);
    }

    /**
     * 获取反作弊接口需要的价格信息
     * @param array $aAthenaBaseParams 订单信息
     * @return false|string
     */
    private function _prepareEstimateInfo($aAthenaBaseParams) {
        $aExtendFeature = json_decode($aAthenaBaseParams['extend_feature'], true);
        $iRedPacket     = 0;
        if (isset($aExtendFeature['estimate_info']['red_packet']) && $aExtendFeature['estimate_info']['red_packet'] > 0) {
            $iRedPacket = $aExtendFeature['estimate_info']['red_packet'];
        }

        $iFixedPreferential = 0;
        if (isset($aExtendFeature['estimate_info']['fixed_preferential']) && $aExtendFeature['estimate_info']['fixed_preferential'] > 0) {
            $iFixedPreferential = $aExtendFeature['estimate_info']['fixed_preferential'];
        }

        $aEstimateInfo = [
            'pre_total_fee'       => $aAthenaBaseParams['pre_total_fee'] ?? 0,
            'start_dest_distance' => $aAthenaBaseParams['start_dest_distance'] ?? 0,
            'cap_price'           => $aAthenaBaseParams['cap_price'] ?? 0,
            'red_packet'          => $iRedPacket,
            'fixed_preferential'  => $iFixedPreferential,
        ];

        return json_encode($aEstimateInfo);
    }

    /**
     * 获取端来源信息
     * @param array $aAthenaBaseParams 订单信息
     * @param array $aPassengerInfo    乘客信息
     * @return false|string
     */
    private function _prepareAppInfo($aAthenaBaseParams, $aPassengerInfo) {
        return json_encode(
            array(
                'from'            => $this->getRequest()->getQuery('from', false) ?? 'native',
                'device_id'       => $this->getRequest()->getQuery('device_id', false),
                'ip'              => (new Request())->getIpAddress(),
                'app_version'     => $this->getRequest()->getQuery('appversion') ?? $this->getRequest()->getQuery('app_version'),
                'data_type'       => $this->getRequest()->getQuery('data_type') ?? $this->getRequest()->getQuery('datatype'),
                'origin_id'       => $this->getRequest()->getQuery('origin_id', false),
                'a3_token'        => $this->getRequest()->getQuery('a3_token', false),
                'channel'         => (string) ($aAthenaBaseParams['channel']),
                'access_key_id'   => $this->getRequest()->getQuery('access_key_id', false),
                'is_dache_anycar' => (int)(Constants\Common::MENU_DACHE_ANYCAR == $aAthenaBaseParams['menu_id']),
                'app_id'          => $aPassengerInfo['app_id'],
            )
        );
    }

    /**
     * 获取反作弊接口需要的乘客信息
     * @param array $aPassengerInfo 乘客信息
     * @return array
     */
    private function _preparePassengerInfo($aPassengerInfo) {
        // 获取db里的passenger信息
        $iPid         = $aPassengerInfo['passenger_id'] ?? $aPassengerInfo['pid'];
        $oDbPassenger = PassengerRepository::getDbPassenger($iPid);
        $sPassengerCreateTime = $oDbPassenger->create_time;
        $sCountPay            = $oDbPassenger->count_pay;
        $sCountComplete       = $oDbPassenger->count_complete;

        return array(
            'passenger_id'    => $iPid,
            '_create_time'    => $sPassengerCreateTime ?? '',
            'count_pay'       => (int)$sCountPay,
            'count_complete'  => (int)$sCountComplete,
            'passenger_token' => $this->_sToken,
            'verify_status'   => $oDbPassenger->verify_status ?? 1,
            'auth_state'      => 2, // 默认的值，老代码没有定义常量，不清楚具体含义
            'member_level_id' => 3, // 默认的值，老代码没有定义常量，不清楚具体含义
        );
    }

    /**
     * 获取乘客信息
     * @return array|mixed
     * @throws ExceptionWithResp
     */
    private function _getAndCheckPassengerInfo() {
        $aPassengerInfo = (new Passenger())->getPassengerByTokenFromPassport($this->_sToken);
        if (!isset($aPassengerInfo['phone'])) {
            $aError = UtilsCommon::getErrMsg(GLOBAL_PARAMS_ERROR);
            NuwaLog::warning(
                sprintf(
                    'errno:%s|errmsg:%s|token:%s|passengerInfo:%s|_getPassengerInfo_failed',
                    $aError['errno'],
                    $aError['errmsg'],
                    $this->_sToken,
                    json_encode($aPassengerInfo)
                )
            );
            throw new ExceptionWithResp(
                Code::E_COMMON_PARAM_ERROR,
                RespCode::P_ERRNO_PARAMS_ERROR,
                '',
                ['token' => $this->_sToken, 'passenger_info' => $aPassengerInfo]
            );
        }

        return $aPassengerInfo;
    }

    /**
     * @param \Nuwa\Core\Request\Request|\Nuwa\Core\Request\RequestCli $aRequest   请求入参
     * @param array                                                    $aOrderInfo 订单信息
     * @return bool|mixed
     */
    private function _callNewtonForAppendingCarSwitch($aRequest, $aOrderInfo) {
        $oApollo = new Apollo();
        $oToggle = $oApollo->featureToggle(
            'gs_append_car_prepay_newton_switch',
            [
                'key'           => $aOrderInfo['passenger_id'],
                'city'          => $aOrderInfo['area'],
                'county'        => $aOrderInfo['county'],
                'pid'           => $aOrderInfo['passenger_id'],
                'phone'         => $aOrderInfo['passenger_phone'],
                'lang'          => $aRequest->getQuery('lang', false),
                'access_key_id' => $aRequest->getQuery('access_key_id', 0),
                'version'       => $aRequest->getQuery('appversion') ?? $aRequest->getQuery('app_version'),
            ]
        );
        return $oToggle->allow();
    }

    /**
     * @Desc   将产品线信息整理成Athena需要的OrderMatchMultiProductLine格式
     * @param  array $aMultiRequireProducts 待格式化数组
     * @param  int   $iIsSelected           是否用户选择
     * @return array
     */
    private function _buildAthenaOrderMatchProductLine($aMultiRequireProducts, $iIsSelected = 0) {
        if (empty($aMultiRequireProducts)) {
            return [];
        } else {
            $aFormatProducts = [];
            $aEstimateIdList = [];
            foreach ($aMultiRequireProducts as $aSingleProduct) {
                if (empty($aSingleProduct)) {
                    continue;
                }

                if (empty($aSingleProduct['business_id'])) {
                    $aSingleProduct['business_id'] = Product::getCommonProductId($aSingleProduct['product_id']);
                } elseif (empty($aSingleProduct['product_id'])) {
                    $aSingleProduct['product_id'] = Product::getProductIdByBusinessId($aSingleProduct['business_id']);
                }

                $sGroupKey = $aSingleProduct['group_key'] ?? AnyCarCommonLogic::getGroupKey($aSingleProduct);

                $aSingleFormatProduct = [
                    'business_id'      => $aSingleProduct['business_id'] ?? 0,
                    'product_id'       => $aSingleProduct['product_id'] ?? 0,
                    'require_level'    => $aSingleProduct['require_level'] ?? 0,
                    'combo_type'       => $aSingleProduct['combo_type'] ?? 0,
                    'level_type'       => $aSingleProduct['level_type'] ?? 0,
                    'product_category' => $aSingleProduct['product_category'] ?? (new ProductCategory())->getProductCategoryByNTuple($aSingleProduct),
                    'group_key'        => $sGroupKey,
                    'business_name'    => $aSingleProduct['business_name'] ?? '',
                    'estimate_fee'     => $aSingleProduct['estimate_fee'] ?? -1,
                    'coupon_amount'    => $aSingleProduct['coupon_amount'] ?? 0,
                    'dynamic_times'    => $aSingleProduct['dynamic_times'] ?? 0,
                    'is_selected'      => $iIsSelected,
                    'extra'            => $aSingleProduct['extra'] ?? [],
                    'estimate_id'      => $aSingleProduct['estimate_id'] ?? '',
                    'is_default_auth'  => $aSingleProduct['is_default_auth'] ?? '0',
                    'sub_group_id'     => $aSingleProduct['sub_group_id'] ?? 0,
                    'carpool_type'     => $aSingleProduct['carpool_type'] ?? 0,
                ];

                //是否是三方车型
                $aSingleFormatProduct['is_tripcloud'] = false;
                if (!empty($aSingleFormatProduct['product_id']) && TripcloudProduct::isTripcloudByProductID($aSingleFormatProduct['product_id'])
                ) {
                    $aSingleFormatProduct['is_tripcloud'] = true;
                }

                // 判断小巴是否满足预匹配和实验条件
                if (\BizCommon\Utils\Horae::isMiniBusCarpool($aSingleProduct) && $this->_isMiniBusAppendAB()) {
                    $iMiniBusEtp = $this->_oMatchResultRuntime->oAnycarEstimateViewModel->getMiniBusEtp();
                    Log::info(sprintf('buildAthenaOrderMatchProductLine MiniBusEtp=%s', $iMiniBusEtp));
                    if (-1 != $iMiniBusEtp) {
                        $aSingleFormatProduct['recommend_status'] = 1;// 预匹配成功满足条件推荐
                    }
                }

                array_push($aFormatProducts, $aSingleFormatProduct);
                // 记录下eid，用于获取用户已呼叫车型的价格信息
                if (1 == $iIsSelected && !empty($aSingleProduct['estimate_id'])) {
                    $aEstimateIdList[] = $aSingleProduct['estimate_id'];
                }
            }

            if (!empty($aEstimateIdList) && $this->_callAthenaAddEstimateFeeFieldSwitch($this->_oMatchResultRuntime->getRequest(), $this->_oMatchResultRuntime->getOrderInfo())) {
                // 用户已选择车型从预估报价单获取发单时的预估价格数据
                $aQuotationRet = $this->_getQuotationBatch($aEstimateIdList);
                if (!empty($aQuotationRet)) {
                    foreach ($aFormatProducts as &$aProductItem) {
                        $sEstimateId    = $aProductItem['estimate_id'];
                        $aQuotationInfo = $aQuotationRet[$sEstimateId];
                        $aProductItem['estimate_fee']  = $aQuotationInfo['estimate_fee'];
                        $aProductItem['dynamic_times'] = $aQuotationInfo['dynamic_times'];
                    }
                }
            }

            return $aFormatProducts;
        }
    }

    /**
     * @param  array $aRawEstimateData 预估原始数据
     * @return array
     */
    private function _buildCanAddProduct($aRawEstimateData) {
        if (empty($aRawEstimateData)) {
            return [];
        }

        $aCanAddProduct = [];
        foreach ($aRawEstimateData as $aProduct) {
            if (empty($aProduct) || empty($aProduct['estimate_id'])) {
                continue;
            }

            $aOrderProduct = [
                'product_id'       => $aProduct['product_id'] ?? 0,
                'business_id'      => $aProduct['business_id'] ?? 0,
                'require_level'    => (int)$aProduct['require_level'] ?? 0,
                'combo_type'       => $aProduct['combo_type'] ?? 0,
                'carpool_type'     => $aProduct['carpool_type'] ?? 0,
                'route_type'       => $aProduct['route_type'] ?? 0,
                'product_category' => $aProduct['product_category'] ?? 0,
                'level_type'       => $aProduct['level_type'] ?? 0,
                'is_selected'      => false,
                'estimate_id'      => $aProduct['estimate_id'] ?? '',
            ];
            $aCanAddProduct[] = new \Dirpc\SDK\AthenaApiv3\ExpectProductInfo(
                [
                    'n_tuple' => new \Dirpc\SDK\AthenaApiv3\NTuple($aOrderProduct),
                ]
            );
        }

        return $aCanAddProduct;
    }


        /**
     * Function _getUserCalledProductList
     * @param  array $aOrderInfo 订单信息
     * @return array
     */
    private function _getUserCalledProductList($aOrderInfo) {
        if (!$aOrderInfo['is_anycar']) {
            $aCalledProductList[] = [
                'product_id'    => $aOrderInfo['product_id'],
                'require_level' => $aOrderInfo['require_level'],
                'combo_type'    => $aOrderInfo['combo_type'],
                'group_key'     => AnyCarCommonLogic::getGroupKey($aOrderInfo),
                'level_type'    => $aOrderInfo['level_type'],
            ];

            return $aCalledProductList;
        }

        $aExtendFeature = json_decode($aOrderInfo['extend_feature'], true);
        return $aExtendFeature['multi_require_product'] ?? [];
    }

    /**
     * Function _openDriverScheduleInfo athena上线正式数据后下掉此开关
     * 从transaction复制过来的要下掉的话两处都要下掉！！！！
     * @param  array $aOrderInfo 订单信息
     * @return bool|mixed
     */
    private function _openDriverScheduleInfo($aOrderInfo) {
        //迁移自transaction/app/Domain/Model/LineUp/LineUpMatchResult/LineUpMatchResult.php:915
        static $bStatus = null;

        if ($bStatus !== null) {
            return $bStatus;
        }

        $bStatus = Apollo::getInstance()->featureToggle(
            'match_info_show_driver_schedule',
            array(
                'key'   => $aOrderInfo['order_id'],
                'city'  => $aOrderInfo['area'],
                'phone' => $aOrderInfo['passenger_phone'],
            )
        )->allow();

        return $bStatus;
    }

    /**
     * @param array $aOrderInfo 订单信息
     * @return array [是否有福利金， 福利金信息]
     */
    private function _getNotTakeLikeWaitBonus($aOrderInfo) {
        $iOrderId = $aOrderInfo['order_id'];
        $iTurn    = 1;
        $oLikeWaitBonusRepo = new LikeWaiteBounsRepository();
        $aCacheExtraData    = $oLikeWaitBonusRepo->getExtraData($iOrderId, $iTurn);
        $iAmount            = $aCacheExtraData['amount'];
        if (!empty($iAmount)) {
            return [true, $aCacheExtraData];
        }

        return [false, []];
    }

    /**
     * @desc 获取Athena5.0返回信息
     * @param  array $aOrderInfo 订单信息
     * @return array
     */
    private function _getAthenaInfoV5($aOrderInfo) {
        // 用户已选择产品线
        $aMultiOrderProduct = $this->_getUserCalledProductList($aOrderInfo);
        $aCalledProductList = $this->_buildAthenaOrderMatchProductLine($aMultiOrderProduct, 1);

        // 可导流产品线
        $aMultiAddProduct = BothCallAnycarLogic::getInstance()->getShowMultiRequireProduct($aOrderInfo);
        $aMultiAddProduct = $this->_buildAthenaOrderMatchProductLine($aMultiAddProduct, 0);

        //版本
        $sAppVersion = $this->getRequest()->getQuery('app_version', false);
        if (empty($sAppVersion)) {
            $sAppVersion = $this->getRequest()->getQuery('appversion', false);
        }

        $iProductId     = Product::getCommonProductId($aOrderInfo['product_id']);
        $iRequireLevel  = $aOrderInfo['require_level'];
        $aExtendFeature = json_decode($aOrderInfo['extend_feature'], true);
        if (is_array($aExtendFeature) && !empty($aExtendFeature)) {
            $aMultiProducts = $aExtendFeature['multi_require_product'] ?? [];
            if (1 == count($aMultiProducts)) { //单勾品类
                $aProduct      = array_values($aMultiProducts)[0];
                $iProductId    = Product::getCommonProductId($aProduct['product_id']);
                $iRequireLevel = $aProduct['require_level'];
            }
        }

        $aAthenaOrderInfo = [
            'order_id'              => UtilHelper::genHighIntOrderId($aOrderInfo['order_id'], $aOrderInfo['district']),
            'pid'                   => $aOrderInfo['passenger_id'],
            'city_id'               => $aOrderInfo['area'],
            'product_id'            => $iProductId,
            'require_level'         => $iRequireLevel,
            'lang'                  => $this->getRequest()->getQuery('lang',false),
            'app_version'           => $sAppVersion,
            'client_type'           => $this->getRequest()->getQuery('client_type', false),
            'user_type'             => $this->getRequest()->getQuery('user_type', 0),
            'carpool_station_type'  => $this->getRequest()->getQuery('carpool_station_type', 0),
            'multi_require_product' => !empty($aMultiAddProduct) ? json_encode($aMultiAddProduct) : '', // 可出出口车型全集
            'multi_order_product'   => json_encode($aCalledProductList), // 已呼叫车型
            'app_inactive'          => $this->getRequest()->getQuery('app_inactive', false),
            'pay_type'              => $aOrderInfo['pay_type'] ?? 0,
            'access_key_id'         => $this->getRequest()->getQuery('access_key_id', false),
            'extra_info'            => ['is_from_pre_cancel_order' => '1'],
        ];

        $aAthenaResult = (new \BizLib\Client\AthenaApiClient())->getNoReplyPredict($aAthenaOrderInfo);
        if (isset($aAthenaResult['errno']) && 0 == $aAthenaResult['errno']) {
            return $aAthenaResult;
        }

        return [];
    }

    private function _getBookingOrderApolloConfig($sFileName, $sKeyName) {
        $aConfig = ApolloHelper::getConfigContent('booking_order_conf', $sFileName);
        if (isset($aConfig[$sKeyName])) {
            return $aConfig[$sKeyName];
        }

        return [];
    }


    /**
     * @param array $aCancelInfo aCancelInfo
     * @return mixed
     */
    private function _getInterWaitReward($aCancelInfo) {
        if (empty($aCancelInfo['raw_money'])) {
            return $aCancelInfo;
        }

        $iMaxPrice = 0;
        foreach ($aCancelInfo['raw_money'] as $aItem) {
            if ($aItem['price'] / 100 > $iMaxPrice) {
                $iMaxPrice = $aItem['price'] / 100;
            }
        }

        $aConfig   = Language::getDecodedTextFromDcmp('pre_cancel-inter_wait_reward_compatible_msg');
        $iLeftTime = $aCancelInfo['left_time'];
        if ($iLeftTime <= 0) {
            $aCancelInfo['wait_reward']['fee_msg']         = sprintf($aConfig['end_wait']['fee_msg'], $iMaxPrice);
            $aCancelInfo['wait_reward']['fee_explain_msg'] = sprintf($aConfig['end_wait']['fee_explain_msg'], $iMaxPrice);
        } else {
            if (($iLeftTime / 3600) >= 1) {
                $sLeftStr = gmstrftime('%H:%M:%S',$iLeftTime);
            } else {
                $sLeftStr = gmstrftime('%M:%S',$iLeftTime);
            }

            $aCancelInfo['wait_reward']['fee_msg']         = sprintf($aConfig['begin_wait']['fee_msg'], $iMaxPrice);
            $aCancelInfo['wait_reward']['fee_explain_msg'] = sprintf($aConfig['begin_wait']['fee_explain_msg'], $sLeftStr, $iMaxPrice);
        }

        unset($aCancelInfo['raw_money']);
        unset($aCancelInfo['left_time']);
        unset($aCancelInfo['max_time']);
        unset($aCancelInfo['wait_time']);

        return $aCancelInfo;
    }

    /**
     * 判断当前 anycar 订单是否包含某一个业务线
     * @param array $aOrderInfo             订单信息
     * @param int   $iTargetProductCategory product_category
     * @return bool
     */
    private function _bIsIncludeProduct($aOrderInfo, $iTargetProductCategory) {
        $aExtendFeature = json_decode($aOrderInfo['extend_feature'], true);
        $aMultiProduct  = $aExtendFeature['multi_require_product'] ?? [];
        foreach ($aMultiProduct as $sGroupKey => $aProduct) {
            $iProductCategory = $aProduct['product_category'];
            if (empty($iProductCategory)) {
                $aTuple = [
                    'business_id'   => $aProduct['business_id'],
                    'require_level' => $aProduct['require_level'],
                    'combo_type'    => $aProduct['combo_type'],
                ];

                if (!empty($aProduct['level_type'])) {
                    $aTuple['level_type'] = $aProduct['level_type'];
                }

                $oProductCategory = new ProductCategory();
                $iProductCategory = $oProductCategory->getProductCategoryByNTuple($aTuple);
            }

            if ($iProductCategory == $iTargetProductCategory) {
                return true;
            }
        }

        return false;
    }

    /**
     * 等待应答6.0二期放量
     * @param array $aRequest   请求参数
     * @param array $aOrderInfo 订单数据
     * @return bool
     */
    private function _iterationSwitch($aRequest, $aOrderInfo) {
        $iAccessKeyId = $aRequest['access_key_id'];
        // 不支持英文版
        if (Language::ZH_CN != $aRequest['lang']) {
            return false;
        }

        // na端由端上控制是否显示新样式
        if (in_array($iAccessKeyId, [Constants\Common::DIDI_IOS_PASSENGER_APP, Constants\Common::DIDI_ANDROID_PASSENGER_APP, Constants\Common::DIDI_HARMONY_PASSENGER_APP])) {
            $iNewGuide = $aRequest['is_new_guide'];
            return 1 == $iNewGuide;
        }

        $oApollo = new Apollo();
        $oToggle = $oApollo->featureToggle(
            'athena_wait_anycar_rec_ab_config',
            [
                'key'           => $aOrderInfo['passenger_id'],
                'city'          => $aOrderInfo['area'],
                'county'        => $aOrderInfo['county'],
                'pid'           => $aOrderInfo['passenger_id'],
                'phone'         => $aOrderInfo['passenger_phone'],
                'lang'          => $aRequest['lang'],
                'access_key_id' => $aRequest['access_key_id'],
                'version'       => $aRequest['appversion'] ?? $aRequest['app_version'],
            ]
        );
        if ($oToggle->allow()) {
            // pre_cancel = 1 表示走新样式
            $sPreCancel = $oToggle->getParameter('pre_cancel', '');
            return '1' == $sPreCancel;
        } else {
            return false;
        }
    }

    /**
     * 获取运行时数据总线
     * @param array $aReqRawParams        请求的数据
     * @param array $aOrderInfo           订单信息
     * @param int   $iIsWaitAnswerUpgrade 是否走新等应答框架样式
     * @return MatchResultRuntime
     */
    private function _buildMatchResultRuntime($aReqRawParams, $aOrderInfo, $iIsWaitAnswerUpgrade) {
        $oMatchResultRuntime = new MatchResultRuntime($aReqRawParams, $aOrderInfo);
        $oMatchResultRuntime->setRequest($aReqRawParams);
        $oMatchResultRuntime->setAOrderInfo($aOrderInfo);
        $oMatchResultRuntime->iIsWaitAnswerUpgrade = $iIsWaitAnswerUpgrade;
        $oMatchResultRuntime->aQuotation = $this->_getQuotation($aOrderInfo);
        return $oMatchResultRuntime;
    }

    /**
     * 判断是否是预匹配订单
     * @return bool
     */
    private function _isShowPrematchCard() {
        if (!self::_isPreMatchOrder()) {
            return false;
        }

        $aBroadcast = json_decode($this->_sPrematchBroadcast,true);
        $iStatus    = $aBroadcast['status'];
        if (1 != $iStatus && 2 != $iStatus) {
            return false;
        }

        return true;
    }

    /**
     * 判断是否是预匹配订单
     * @return bool
     */
    private function _isPreMatchOrder() {
        return !empty($this->_sPrematchBroadcast);
    }

    /**
     * 判断是否是预约单有车订单
     * @param array $aOrderInfo 订单信息
     * @return bool
     */
    private function _isBookingMockDriverOrder($aOrderInfo) {
        if ($aOrderInfo['type'] != OrderSystem::TYPE_ORDER_BOOKING) {
            return false;
        }
        $aExtendFeature  = json_decode($aOrderInfo['extend_feature'], true);
        NuwaLog::info('angelshianqi_test dos extendFeature:'.json_encode($aExtendFeature));
        // 预约单mock司机接单
        if (!empty($aExtendFeature) && $aExtendFeature['is_booking_virtual_driver'] == 1) {
            NuwaLog::info('angelshianqi_test mock order');
            //您已预约成功需要判断当前是否在倒计时阶段，倒计时阶段还未分配虚拟司机，产品诉求，倒计时阶段复用线上链路
            list($bOk, $iConf) = \Nuwa\ApolloSDK\Apollo::getInstance()
                ->getConfigResult('booking_order_virtual', 'booking_order_virtual_count_down_seconds_config')
                ->getConfig('count_down_seconds');
            if (!$bOk || !isset($iConf)) {
                $iCountDownTime = 15; //兜底15秒
            } else {
                $iCountDownTime = $iConf;
                NuwaLog::info('angelshianqi_test mock order,count_down_time:'.$iCountDownTime);
            }
            $iWaitTime = time() - strtotime($aOrderInfo['_birth_time']);
            if ($iWaitTime < $iCountDownTime) {
                return false;
            }
            return true;
        }
        return false;
    }


    /**
     * @decs 获取预期信息
     * @param array $aAthenaBaseParams 订单信息
     * @param array $aRawEstimateData  预估数据
     * @return array
     */
    private function _getAthenaBubbleExpectInfo($aAthenaBaseParams, $aRawEstimateData) {

        if (empty($aAthenaBaseParams)) {
            return [];
        }

        //参数组装
        $sAppVersion = $this->getRequest()->getQuery('app_version', false);

        if (empty($sAppVersion)) {
            $sAppVersion = $this->getRequest()->getQuery('appversion', false);
        }

        $aAthenaBaseParams['app_version']   = $sAppVersion;
        $aAthenaBaseParams['access_key_id'] = $this->getRequest()->getQuery('access_key_id', false);

        // 用户已选择产品线
        $aMultiOrderProduct = $this->_getUserCalledProductList($aAthenaBaseParams);

        // 用户已选择产品线
        $aAthenaBaseParams['multi_order_product'] = $this->_buildAthenaOrderMatchProductLine($aMultiOrderProduct, 1);

        // 已发单车型
        $aMultiOrderProduct = [];
        if (!empty($aAthenaBaseParams['multi_order_product']) && is_array($aAthenaBaseParams['multi_order_product'])) {
            foreach ($aAthenaBaseParams['multi_order_product'] as $aOrderProduct) {
                $aOrderProduct['require_level'] = (int)$aOrderProduct['require_level'];
                $aOrderProduct['is_selected']   = !empty($aOrderProduct['is_selected']);
                $aMultiOrderProduct[]           = new \Dirpc\SDK\AthenaApiv3\ExpectProductInfo(
                    [
                        'n_tuple' => new \Dirpc\SDK\AthenaApiv3\NTuple($aOrderProduct),
                    ]
                );
            }
        }

        $aExtendFeature = json_decode($aAthenaBaseParams['extend_feature'], true);

        $aCanAddProduct = $this->_buildCanAddProduct($aRawEstimateData);

        $aExpectScene  = [
            'order_match_global_scene', // 已发单车型预期信息
            'product_scene',
        ];
        $aShowTypeList = [
            'ets',
            'answer_rate',
            'etp',
            'queue',
        ];

        $aAthenaOrderInfo = array(
            'order_id'             => UtilHelper::genHighIntOrderId($aAthenaBaseParams['order_id'], $aAthenaBaseParams['district']),
            'pid'                  => $aAthenaBaseParams['passenger_id'],
            'phone'                => $aAthenaBaseParams['passenger_phone'],
            'city_id'              => $aAthenaBaseParams['area'],
            'lang'                 => $this->getRequest()->getQuery('lang', false),
            'client_type'          => $this->getRequest()->getQuery('client_type', false),
            'channel'              => $this->getRequest()->getQuery('channel', false),
            'app_version'          => $sAppVersion,
            'access_key_id'        => $this->getRequest()->getQuery('access_key_id', false),
            'from_lng'             => $aAthenaBaseParams['starting_lng'],
            'from_lat'             => $aAthenaBaseParams['starting_lat'],
            'to_lng'               => $aAthenaBaseParams['dest_lng'],
            'to_lat'               => $aAthenaBaseParams['dest_lat'],
            'from_name'            => $aAthenaBaseParams['starting_name'],
            'to_name'              => $aAthenaBaseParams['dest_name'],
            'is_queue'             => UtilHelper::checkOrderExtraType($aAthenaBaseParams['extra_type'], array('o_lineup')) ? 1 : 0,
            'estimate_trace_id'    => $aExtendFeature['estimate_trace_id'],
            'already_send_product' => $aMultiOrderProduct, // 已发单车型数据
            'expect_scene'         => $aExpectScene,  // 预期场景
            'show_type_list'       => $aShowTypeList, // 预期类型
            'can_add_product'      => $aCanAddProduct, // 可追加车型
        );

        $oAthenaV3Client  = new AthenaApiV3Client();
        $aAthenaResult    = $oAthenaV3Client->getAthenaBubbleExpectInfo($aAthenaOrderInfo);
        if (isset($aAthenaResult['errno']) && 0 == $aAthenaResult['errno']) {
            return $aAthenaResult;
        }

        return [];

    }


    /**
     * @decs 获取报价单
     * @param array $aOrderInfo 订单信息
     * @return array
     */
    private function _getQuotation($aOrderInfo)
    {
        $aMultiProduct = $this->_getUserSendProductList($aOrderInfo);
        if (empty($aMultiProduct)) {
            return [];
        }

        $aEstimateId = [];
        foreach ($aMultiProduct as $aProduct) {
            if (empty($aProduct) || empty($aProduct['estimate_id'])) {
                continue;
            }

            $aEstimateId[] = $aProduct['estimate_id'];

        }

        $aQuotationResult = $this->_getQuotationBatch($aEstimateId);

        return $aQuotationResult;

    }


    /**
     * Function _getUserSendProductList
     * @param  array $aOrderInfo 订单信息
     * @return array
     */
    private function _getUserSendProductList($aOrderInfo) {
        if (!$aOrderInfo['is_anycar']) {
            $aCalledProductList[] = [
                'product_id'    => $aOrderInfo['product_id'],
                'require_level' => $aOrderInfo['require_level'],
                'combo_type'    => $aOrderInfo['combo_type'],
                'group_key'     => AnyCarCommonLogic::getGroupKey($aOrderInfo),
                'level_type'    => $aOrderInfo['level_type'],
                'estimate_id'   => $aOrderInfo['estimate_id'],
            ];

            return $aCalledProductList;
        }

        $aExtendFeature = json_decode($aOrderInfo['extend_feature'], true);
        return $aExtendFeature['multi_require_product'] ?? [];
    }


    /**
     * 初始化预匹配数据
     * @return bool
     */
    private function _getPreMatchOrder() {
        $aOrderInfo     = $this->_aOrderInfo;
        $oDuseApiClient = new DuseApiClient();
        $sTraceId       = \Disf\SPL\Trace::traceId();
        $sSpanId        = \Disf\SPL\Trace::spanId();
        $iFormatOrderId = UtilHelper::genHighIntOrderId($aOrderInfo['order_id'], $aOrderInfo['district']);
        $iCityId        = \BizLib\Utils\MapHelper::getAreaIdByDistrict($aOrderInfo['district']);
        $aResult        = $oDuseApiClient->getOrder(
            'gulfstream',
            $iFormatOrderId,
            $iCityId,
            array('prematch_broadcast'),
            'gs-api',
            $sTraceId,
            $sSpanId,
            $aOrderInfo
        );
        if (0 != $aResult['errno']) {
            return '';
        }

        if (empty($aResult['result']['prematch_broadcast'])) {
            return '';
        }

        return $aResult['result']['prematch_broadcast'];
    }

    /**
     * 请求athena是否增加已呼叫车型预估价格数据开关
     * @param array $aRequest   请求入参
     * @param array $aOrderInfo 订单信息
     * @return bool
     */
    private function _callAthenaAddEstimateFeeFieldSwitch($aRequest, $aOrderInfo) {
        $oApollo = new Apollo();
        $oToggle = $oApollo->featureToggle(
            'gs_pre_cancel_order_call_athena_add_fields',
            [
                'key'           => $aOrderInfo['passenger_id'],
                'city'          => $aOrderInfo['area'],
                'county'        => $aOrderInfo['county'],
                'pid'           => $aOrderInfo['passenger_id'],
                'phone'         => $aOrderInfo['passenger_phone'],
                'lang'          => $aRequest['lang'],
                'access_key_id' => $aRequest['access_key_id'],
                'version'       => $aRequest['appversion'] ?? $aRequest['app_version'],
            ]
        );
        return $oToggle->allow();
    }

    /**
     * 批量获取预估报价单数据
     *
     * @param array $aEstimateIdList 预估eid list
     * @return array
     */
    private function _getQuotationBatch($aEstimateIdList) {
        $aPriceParams = [
            'estimate_id_list' => $aEstimateIdList,
            'fields'           => [
                'dynamic_total_fee', // 券前价
                'estimate_fee',      // 券后价
                'dynamic_times',     // 动调
                'discount_desc',
            ],
        ];
        $oPriceClient = new PriceApiClient(PriceApiClient::MODULE_NAME);
        $aQuotation   = $oPriceClient->getQuotationBatch($aPriceParams);
        if (!isset($aQuotation['errno']) || 0 != $aQuotation['errno'] || empty($aQuotation['data'])) {
            \BizLib\Log::warning(Msg::formatArray(Code::E_COMMON_GET_QUOTATION_FAIL, ['req' => $aPriceParams, 'ret' => $aQuotation]));
            return [];
        }

        return $aQuotation['data'];
    }

    /**
     * @param $sAppVersion
     * @param $iAccessKeyId
     * @return bool
     */
    private function _checklBookingOrderPreCancelVersion($sAppVersion, $iAccessKeyId) {

        if ($this->_getBookingOrderApolloConfig('booking_order_align_version_control','na_version') != []) {
            $sNaVersion = $this->_getBookingOrderApolloConfig('booking_order_align_version_control','na_version');
        } else {
            $sNaVersion = '6.6.20';
        }

        if ($this->_getBookingOrderApolloConfig('booking_order_align_version_control','xcx_version') != []) {
            $sXcxVersion = $this->_getBookingOrderApolloConfig('booking_order_align_version_control','xcx_version');
        } else {
            $sXcxVersion = '6.7.0';
        }

        // NA 要大于等于6.6.20
        if (in_array($iAccessKeyId, [\BizLib\Constants\Common::DIDI_IOS_PASSENGER_APP, \BizLib\Constants\Common::DIDI_ANDROID_PASSENGER_APP])) {
            if (UtilHelper::compareAppVersion($sAppVersion, $sNaVersion) < 0) {
                return false;
            }
        }

        // 小程序版本号大于=6.7.0 才展示
        if (in_array($iAccessKeyId, [\BizLib\Constants\Common::DIDI_WECHAT_MINI_PROGRAM, \BizLib\Constants\Common::DIDI_ALIPAY_MINI_PROGRAM])) {
            if (UtilHelper::compareAppVersion($sAppVersion, $sXcxVersion) < 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * 判断是否是仅小神车订单
     * @param array $aOrderInfo 订单信息
     * @return bool
     */
    private function _isOnlyDidiMiniOrder($aOrderInfo) {
        $aExtendFeature       = json_decode($aOrderInfo['extend_feature'], true) ?? [];
        $aMultiRequireProduct = $aExtendFeature['multi_require_product'] ?? [];
        if (!empty($aMultiRequireProduct) && 1 == count($aMultiRequireProduct) && current($aMultiRequireProduct)['product_id'] == OrderNTuple::PRODUCT_ID_DIDI_MINI) {
            return true;
        }
        return false;
    }

    /**
     * 小巴追加车型弹窗实验
     * @return bool
     */
    private function _isMiniBusAppendAB() {
        $oApolloAB = \Nuwa\ApolloSDK\Apollo::getInstance()->featureToggle('xiaoba_additional_popup', [
            Apollo::APOLLO_INDIVIDUAL_ID => $this->_aOrderInfo['passenger_id'],
            'pid'                        => $this->_aOrderInfo['passenger_id'],
            'city'                       => $this->_aOrderInfo['area'],
            'phone'                      => $this->_aOrderInfo['passenger_phone'],
            'access_key_id'              => $this->getRequest()->getQuery('access_key_id', 0),
            'app_version'                => $this->getRequest()->getQuery('app_version', false),
            'lang'                       => $this->getRequest()->getQuery('lang', false),
        ]);

        if ($oApolloAB->allow() && 'treatment_group' == $oApolloAB->getGroupName()) {
            Log::info('isMiniBusAppendAB ab is true');
            return true;
        }

        Log::info('isMiniBusAppendAB ab is false');
        return false;
    }

    /**
     * @param array $aOrderInfo    订单数据
     * @param int $iAccessKeyId    端来源
     * @param string $sAppVersion  端版本
     * @return bool
     */
    private function _buildOrderMatchGripperOptimize($aOrderInfo, $iAccessKeyId, $sAppVersion) {
        $apolloParams = [
            'key'           => $aOrderInfo['passenger_id'],
            'pid'           => $aOrderInfo['passenger_id'],
            'city'          => $aOrderInfo['area'],
            'access_key_id' => $iAccessKeyId,
            'app_version'   => $sAppVersion,
            'phone'         => $aOrderInfo['passenger_phone'],
            'order_type'    => $aOrderInfo['type'],
        ];
        $aGaiaToggle = Gaia::getInstance()->findGaiaExperiment('athena', 'order_match_gripper_optimize', $apolloParams);
        NuwaLog::info('GET_TOGGLE_GAIA||exp_name=' . $aGaiaToggle->getExperimentName() . '&exp_group=' . $aGaiaToggle->getGroupName() . '||stg_id=' . $aGaiaToggle->getStgId());
        return $aGaiaToggle->getParameter('hit_order_match_gripper_optimize', '0') == '1';
    }

    /**
     * @desc 取消挽留弹窗不出下挂展示品类
     * @param array $aLayout $aLayout
     * @return array
     */
    private function _delLinkInfo($aLayout) {
        if (!is_array($aLayout)) {
            return $aLayout;
        }
        $aNewLayout = [];
        foreach ($aLayout as $key => $aLayoutItem) {
            if (!is_array($aLayoutItem['groups'])) {
                continue;
            }
            $aFixGroups = [];
            foreach ($aLayoutItem['groups'] as $k => $aGroupItem) {
                if (!empty($aGroupItem['link_info'])) {
                    unset($aGroupItem['link_info']);
                }
                $aFixGroups[$k] = $aGroupItem;
            }

            $aLayoutItem['groups'] = $aFixGroups;
            $aNewLayout[$key] = $aLayoutItem;
        }

        $aLayout = $aNewLayout;
        return $aLayout;
    }
}
