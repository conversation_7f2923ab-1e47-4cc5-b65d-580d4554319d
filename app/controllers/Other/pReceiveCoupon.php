<?php
/**
 * Created by PhpStorm.
 * Date: 19/10/14
 * Time: 20:31
 * @category Category
 * @package FileDirFileName
 * <AUTHOR> <<EMAIL>>
 * @link ${link}
 * 用户领券
 */
use BizLib\Utils\Request;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\ErrCode\Code;
use BizLib\Utils\Language;
use BizLib\Exception\InvalidArgumentException;
use BizLib\ExceptionHandler;
use Nuwa\ApolloSDK\Apollo;
use BizLib\Log as NuwaLog;
use BizLib\Libraries\RedisDB;
use BizCommon\Models\Passenger\Passenger;
use PreSale\Logics\carpool\Coupon;

/**
 * Class PGetBannerInfoController
 * @property PGetBannerInfoController $PGetBannerInfoController
 */
class PReceiveCouponController extends \PreSale\Core\Controller
{
    /**
     * @Desc: mixed[] Array structure to count the elements of.
     * @property init $init
     * @return void
     * @Author:<EMAIL>
     */
    public function init() {
        parent::init();
    }

    private $_oCoupon;

    /**
     * @Desc:
     * mixed[] Array structure to count the elements of.
     * @property indexAction $indexAction
     * @throws InvalidArgumentException  If the provided argument is not of type 'array'.
     * @return void
     * @Author:<EMAIL>
     */
    public function indexAction() {
        try {
            $aRet           = UtilsCommon::getErrMsg(GLOBAL_SUCCESS);
            $this->_oCoupon = new Coupon();
            list($aPassengerInfo,$iArea,$sAppversion) = $this->_getInputParams();
            $aConf = $this->_oCoupon->getConfig($iArea, $sAppversion, $aPassengerInfo);
            if (empty($aConf)) {
                throw new InvalidArgumentException(
                    Code::E_COMMON_PARAM_INVALID_VALUE,
                    array(
                        'area' => $iArea,
                        'conf' => $aConf,
                    )
                );
            }

            $iDayDiff       = $this->_oCoupon->getCouponParam($aConf);
            $iCouponBatchId = $aConf['coupon']['coupon_batchid'];
            if ($iDayDiff <= 0||$iCouponBatchId != $this->_oCoupon->iCouponBatchId) {
                throw new InvalidArgumentException(
                    Code::E_COMMON_PARAM_INVALID_VALUE,
                    array(
                        'area'         => $iArea,
                        'day_diff'     => $iDayDiff,
                        'batchid_in'   => $this->_oCoupon->iCouponBatchId,
                        'batchid_conf' => $iCouponBatchId,
                    )
                );
            }

            $iTime = strtotime($aConf['coupon']['open_time']);
            list($iCouponAmount,$iStatus) = $this->_oCoupon->getBindedCoupon($iCouponBatchId,$iTime,$aPassengerInfo);
            if (Coupon::COUPON_STATUS_NOT_BIND != $iStatus) {
                throw new InvalidArgumentException(
                    Code::E_COMMON_PARAM_INVALID_VALUE,
                    array(
                        'area'   => $iArea,
                        'status' => $iStatus,
                        'amount' => $this->_oCoupon->iCouponAmount,
                    )
                );
            }

            $this->_oCoupon->receiveCoupon($aPassengerInfo);
        } catch (\Exception $e) {
            $aErrMsg = NuwaConfig::text('errno', 'pGetBizConfig_error_msg');
            $aRet    = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $aErrMsg, ]);
        }

        $aRet['errno'] = (int)($aRet['errno']);
        $this->sendTextJson($aRet);
    }

    /**
     * @Desc: mixed[] Array structure to count the elements of.
     * @return int
     * @throws InvalidArgumentException <comment for threw> .
     * @property _getInputParams $_getInputParams
     * @Author:<EMAIL>
     */
    private function _getInputParams() {
        $oRequest       = Request::getInstance();
        $sLang          = $oRequest->getStr('lang') ?? Language::getLocalLanguage();
        $sMenuId        = $oRequest->getStr('menu_id');
        $sAppversion    = !empty($oRequest->getStr('app_version')) ? $oRequest->getStr('app_version') : $oRequest->getStr('appversion');
        $iArea          = $oRequest->getInt('city_id');
        $sToken         = $oRequest->getStr('token');
        $iCouponBatchId = $oRequest->getInt('batchid');
        $iCouponAmount  = $oRequest->getInt('coupon_amount');
        $iAcyivityId    = $oRequest->getInt('activityid');

        if (empty($iArea) || !\BizCommon\Utils\Horae::isLowPriceCarpoolEntry($sMenuId)) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                array(
                    'city_id' => $iArea,
                    'menu_id' => $sMenuId,
                )
            );
        }

        if ('zh-CN' != $sLang|| empty($iCouponBatchId) ||empty($sToken)
            ||empty($iAcyivityId) | empty($iCouponAmount)
        ) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                array(
                    'lang'          => $sLang,
                    'batchid'       => $iCouponBatchId,
                    'token'         => $sToken,
                    'activityid'    => $iAcyivityId,
                    'coupon_amount' => $iCouponAmount,
                )
            );
        }

        $aPassengerInfo = Passenger::getInstance()->getPassengerByToken($sToken);
        if (!isset($aPassengerInfo['pid']) || !isset($aPassengerInfo['phone'])) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                array(
                    'lang'          => $sLang,
                    'batchid'       => $iCouponBatchId,
                    'token'         => $sToken,
                    'passengerInfo' => $aPassengerInfo,
                )
            );
        }

        $this->_oCoupon->iCouponAmount  = $iCouponAmount;
        $this->_oCoupon->iAcyivityId    = $iAcyivityId;
        $this->_oCoupon->iCouponBatchId = $iCouponBatchId;
        return [$aPassengerInfo,$iArea,$sAppversion];
    }
}
