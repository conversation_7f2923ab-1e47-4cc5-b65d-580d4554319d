<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @date 2021/2/3
 */

use BizLib\ErrCode\RespCode;
use BizLib\Utils\Common;
//use BizLib\Utils\Common;
use BizLib\Utils\Request;
use BizLib\Config as NuwaConfig;
use BizLib\ErrCode\Code;
use BizLib\ExceptionHandler;
use PreSale\Logics\passenger\PTemperatureCardLogic;

/**
 * Class PGetInterceptCardController
 */
class PGetInterceptCardController extends \PreSale\Core\Controller
{
    /**
     * function indexAction
     * @return  void
     */
    public function indexAction() {
        try {
            $aRet           = Common::getErrMsg(RespCode::P_SUCCESS);
            $aParams        = $this->_getAndCheckParams();
            $oActivityLogic = new PTemperatureCardLogic($aParams);
            $oActivityLogic->checkParams();
            $aRet['data'] = $oActivityLogic->getTemperatureCard();
        } catch (\Exception $e) {
            $aErrMsg  = NuwaConfig::text('errno', 'pEstimatePrice_error_msg');
            $oHandler = ExceptionHandler::getInstance();
            $aRet     = $oHandler->handleException($e, ['err_msg' => $aErrMsg,]);
        }

        $aRet['errno'] = (int)($aRet['errno']);

        $this->sendTextJson($aRet);
    }

    /**
     * @return mixed
     * @throws \BizLib\Exception\InvalidArgumentException Exception
     */
    private function _getAndCheckParams() {
        $oRequest         = Request::getInstance();
        $aParams['token'] = $oRequest->getStr('token');
        $aParams['lng']   = $oRequest->getStr('lng');
        $aParams['lat']   = $oRequest->getStr('lat');
        $aParams['lang']  = $oRequest->getStr('lang');
        $aParams['type']  = $oRequest->getStr('type');    //石家庄开服 体温确认
        if (empty($aParams['token']) || empty($aParams['lang']) || empty($aParams['type'])) {
            throw new BizLib\Exception\InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                array(
                    'token' => $aParams['token'],
                    'lng'   => $aParams['lng'],
                    'lat'   => $aParams['lat'],
                    'lang'  => $aParams['lang'],
                    'type'  => $aParams['type'],
                )
            );
        }

        return $aParams;
    }
}
