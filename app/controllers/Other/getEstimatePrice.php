<?php

use BizLib\Log as NuwaLog;
/*
 * 根据Athena的请求,返回相应账单、支付和运营等相关的预估信息。
 * @date: 17/5/23
 */

use PreSale\Logics\estimatePrice\EstimateLogic;
use PreSale\Logics\estimatePrice\ParamsLogic;
use BizLib\ErrCode;

class getEstimatePriceController extends \PreSale\Core\Controller
{
    public function indexAction() {
        try {
            $oParams = ParamsLogic::getInstance();

            //过滤参数
            $aEstimatePriceParams = $oParams->getParamsFromAthena($this->getRequest()->getPost(null, false, false));

            //获取预估信息
            $aEstimateInfo = EstimateLogic::getInstance($aEstimatePriceParams)->execute();
        } catch (\Exception $e) {
            NuwaLog::warning(ErrCode\Msg::formatArray($e->getCode(), $e->getMessage()));
            $this->sendJson(
                array(
                    'errno'  => $e->getCode(),
                    'errmsg' => ErrCode\Msg::get($e->getCode(), 'internal error'),
                )
            );

            return;
        }

        $this->sendJson(
            array(
                'errno'  => 0,
                'errmsg' => 'ok',
                'data'   => $aEstimateInfo,
            )
        );

        return;
    }
}
