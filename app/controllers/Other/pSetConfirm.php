<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @date 2021-02-03
 */

use BizLib\ErrCode\RespCode;
use BizLib\Utils\Common;
//use BizLib\Utils\Common;
use BizLib\Utils\Request;
use BizLib\Config as NuwaConfig;
use BizLib\ErrCode\Code;
use BizLib\ExceptionHandler;
use PreSale\Logics\passenger\PConfirmTemperature;

/**
 * Class PSetConfirmController
 */
class PSetConfirmController extends \PreSale\Core\Controller
{
    /**
     * function indexAction
     * @return void
     */
    public function indexAction() {
        try {
            $aRet           = Common::getErrMsg(RespCode::P_SUCCESS);
            $aParams        = $this->_getAndCheckParams();
            $oActivityLogic = new PConfirmTemperature($aParams);
            $oActivityLogic->checkParams();
            // $oActivityLogic->SetConfirm();
        } catch (\Exception $e) {
            $aErrMsg  = NuwaConfig::text('errno', 'pEstimatePrice_error_msg');
            $oHandler = ExceptionHandler::getInstance();
            $aRet     = $oHandler->handleException($e, ['err_msg' => $aErrMsg,]);
        }

        $aRet['errno'] = (int)($aRet['errno']);

        $this->sendTextJson($aRet);
    }

    /**
     * @return mixed
     * @throws \BizLib\Exception\InvalidArgumentException Exception
     */
    private function _getAndCheckParams() {
        $oRequest         = Request::getInstance();
        $aParams['token'] = $oRequest->getStr('token');
        $aParams['lang']  = $oRequest->getStr('lang');
        $aParams['estimate_id'] = $oRequest->getStr('estimate_id');
        $aParams['type']        = $oRequest->getStr('type');    //石家庄开服 体温确认
        if (empty($aParams['token']) || empty($aParams['estimate_id'])
            || empty($aParams['lang']) || empty($aParams['type'])
        ) {
            throw new BizLib\Exception\InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                array(
                    'token'       => $aParams['token'],
                    'estimate_id' => $aParams['estimate_id'],
                    'lang'        => $aParams['lang'],
                    'type'        => $aParams['type'],
                )
            );
        }

        return $aParams;
    }
}
