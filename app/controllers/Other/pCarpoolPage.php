<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @date  2022/9/22 4:32 下午
 * @brief 拼成乐首页(渲染头图、畅拼卡、产品介绍等)
 * @wiki http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=934167289
 */

use BizLib\Utils\Request;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\ExceptionHandler;
use PreSale\Logics\carpool\LowPriceCarpoolPage;
use BizLib\Exception\InvalidArgumentException;
use BizLib\ErrCode\Code;

/**
 * Class  PCarpoolPage
 */
class PCarpoolPageController extends \PreSale\Core\Controller
{

    /**
     * @return void
     */
    public function init() {
        parent::init();
    }

    /**
     * @return void
     */
    public function indexAction() {
        try {
            $aRet         = UtilsCommon::getErrMsg(GLOBAL_SUCCESS);
            $aParams      = $this->_getInputParams();
            $oLogic       = new LowPriceCarpoolPage();
            $aRet['data'] = $oLogic->getBannerInfo($aParams);
        } catch (\Exception $e) {
            $aErrMsg = NuwaConfig::text('errno', 'pGetBizConfig_error_msg');
            $aRet    = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $aErrMsg,]);
        }

        $aRet['errno'] = (int)($aRet['errno']);
        $this->sendTextJson($aRet);
    }


    /**
     * @return array
     * @throws InvalidArgumentException InvalidArgumentException
     */
    private function _getInputParams() {
        $oRequest        = Request::getInstance();
        $iArea           = $oRequest->getInt('city_id');
        $sToken          = $oRequest->getStr('token');
        $sLang           = 'zh-CN'; // 只支持中文简体
        $sAppVersion     = $oRequest->getStr('app_version');
        $iAccessKeyId    = $oRequest->getInt('access_key_id');
        $sEstimateId     = $oRequest->getStr('estimate_id');
        $sMenuId         = $oRequest->getStr('menu_id');
        $iInvitationType = $oRequest->getInt('invitation_type');
        $iMatchCode      = $oRequest->getInt('match_code');
        $sChannel        = $oRequest->getStr('channel');
        $fLat            = $oRequest->getFloat('from_lat');
        $fLng            = $oRequest->getFloat('from_lng');
        $iOriginSceneType = $oRequest->getInt('origin_scene_type'); //场景来源，判断是否是金刚位入口
        $sOsVersion       = $oRequest->getStr('osVersion'); // 系统版本
        $sDchn            = $oRequest->getStr('dchn');      // 投放来源

        if ('pincheche' != $sMenuId) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                array(
                    'city_id' => $iArea,
                    'menu_id' => $sMenuId,
                )
            );
        }

        return [
            'area'              => $iArea,
            'token'             => $sToken,
            'lang'              => $sLang,
            'app_version'       => $sAppVersion,
            'access_key_id'     => $iAccessKeyId,
            'estimate_id'       => $sEstimateId,
            'menu_id'           => $sMenuId,
            'invitation_type'   => $iInvitationType,
            'match_code'        => $iMatchCode,
            'channel'           => $sChannel,
            'from_lat'          => $fLat,
            'from_lng'          => $fLng,
            'origin_scene_type' => $iOriginSceneType,
            'os_version'        => $sOsVersion,
            'dchn'              => $sDchn,
        ];
    }
}
