<?php

use BizLib\Utils\Common as UtilsCommon;
use BizLib\ExceptionHandler;
use BizLib\ErrCode\RespCode;
use Disf\SPL\Trace;
use PreSale\Core\Controller;
use PreSale\Logics\taxi\TaxiPriceDetailLogic;

/**
 * Class pGetTaxiPriceDetail  出租车的预估费用详情说明页
 * http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=*********
 */
class PGetTaxiPriceDetailController extends Controller
{

    /**
     * 获取不同场景对应的计价规则详情
     * @return void
     */
    public function indexAction() {
        $oTaxiPriceDetailLogic = new TaxiPriceDetailLogic($_GET);
        $aResponseInfo         = UtilsCommon::pairErrNo(RespCode::P_SUCCESS, $this->aErrnoMsg);
        $aResponseInfo["trace_id"] = Trace::traceId();
        try {
            if ($oTaxiPriceDetailLogic->isNewVersion()) {
                // 2.0优化后版本
                $oTaxiPriceDetailLogic->checkParam();
                $aResult = $oTaxiPriceDetailLogic->getConfig();
                if (!empty($aResult)) {
                    $aResponseInfo['data'] = $aResult;
                }
            } else {
                //老版本等全量后下掉
                // 验证参数
                $oTaxiPriceDetailLogic->verify();
                // 区分场景
                $aResult = $oTaxiPriceDetailLogic->distinguishScene();

                if (empty($aResult)) {
                    $aResult = null;
                }

                $aResponseInfo['data'] = $aResult;
            }
        } catch (\Exception $e) {
            $aContext = [
                'dltag'   => '_com_request_out_failure',
                'err_msg' => $this->aErrnoMsg,
            ];

            //异常处理并得到返回值
            $aResponseInfo = ExceptionHandler::getInstance()->handleException($e, $aContext);
        }

        //$this->sendTextJson($aResponseInfo);
        $this->sendTextJson($aResponseInfo, $oTaxiPriceDetailLogic->getCallback());
    }
}
