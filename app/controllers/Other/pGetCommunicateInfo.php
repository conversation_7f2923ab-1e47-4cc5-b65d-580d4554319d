<?php

use Biz<PERSON>ommon\Constants\OrderNTuple;
use BizCommon\Models\Compensation\CompensationInfo;
use BizCommon\Models\Compensation\DriverArrivedLate;
use BizCommon\Models\Order\LineUpOrderComModel;
use BizCommon\Models\Order\Order;
use BizCommon\Models\Passenger\Passenger;
use BizLib\Client\CombinedTravelClient;
use BizLib\Client\PorscheClient;
use BizLib\Client\PriceApiClient;
use BizLib\Client\UfsClient;
use BizLib\Config;
use BizLib\Config as NuwaConfig;
use BizLib\Constants;
use BizLib\Constants\Common as ConstantsCommon;
use BizLib\Constants\Common as ConstCommon;
use BizLib\Constants\OrderSystem;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\ErrCode\RespCode;
use BizLib\Exception\ExceptionWithResp;
use BizLib\ExceptionHandler;
use BizLib\Log;
use BizLib\Utils\ApolloHelper;
use BizLib\Utils\Common as UtilCommon;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\Horae;
use BizLib\Utils\Language;
use BizLib\Utils\MapHelper;
use BizLib\Utils\NumberHelper;
use BizLib\Utils\Product;
use BizLib\Utils\ProductCategory;
use Disf\SPL\Trace;
use Nuwa\ApolloSDK\Apollo;
use Nuwa\ApolloSDK\Apollo as NuwaApollo;
use PreSale\Infrastructure\Repository\Redis\Common as RedisCommon;
use PreSale\Infrastructure\Repository\Tag\TagServiceRepository;
use PreSale\Infrastructure\Repository\Ufs\UfsRepository;
//use PreSale\Logics\capEstimatePrice\multiRequest\CommonInfo;
use PreSale\Logics\communicateInfo\AthenaExtension;
use PreSale\Logics\communicateInfo\carpoolCommuteCard\CarpoolCommuteCardLogic;
use PreSale\Logics\communicateInfo\CommunicateInfoCache;
use PreSale\Logics\communicateInfo\CommunicateInfoLogic;
use PreSale\Logics\communicateInfo\CommunicateRepo;
use PreSale\Logics\communicateInfo\didiPay\Common as DiDiPayCommon;
use PreSale\Logics\communicateInfo\disPatchOrderLean\DispatchOrderLeanLogic;
use PreSale\Logics\communicateInfo\epidemicPrevention\EpidemicCommunicateInfoLogic;
use PreSale\Logics\communicateInfo\estimateData\Render;
use PreSale\Logics\communicateInfo\memberShip\MemberShip;
use PreSale\Logics\communicateInfo\memberBenefit\MemberBenefitInfoLogic;
use PreSale\Logics\communicateInfo\PublicLog;
use PreSale\Logics\communicateInfo\recommendCombo\RecommendComboLogic;
use PreSale\Logics\communicateInfo\rewardActivity\RewardActivityLogic;
use PreSale\Logics\communicateInfo\taskReminder\TaskReminderLogic;
use PreSale\Logics\communicateInfo\WeightLogic;
use PreSale\Logics\estimatePrice\CompensationLogic;
use PreSale\Logics\estimatePrice\multiResponse\component\promoteSalesTip\Common as TipCommon;
use PreSale\Logics\estimatePrice\multiResponse\component\recommendInfo\NormalRecommendInfo;
use PreSale\Logics\estimatePrice\multiResponse\component\specialPriceTip\Common;
use PreSale\Logics\estimatePrice\multiResponse\external\GuidePopUp;
use PreSale\Logics\estimatePrice\multiResponse\MainDataRepo;
use PreSale\Logics\estimatePrice\multiResponse\MainRender;
use PreSale\Logics\passenger\PActivityCardLogic;
use PreSale\Logics\passenger\TimeDiscountLogic;
use PreSale\Logics\sideEstimate\component\communicate\event\top\PremiumPaidMemberActivityRuleEvent;
use PreSale\Logics\v3Estimate\multiResponse\PostRender\specialPriceTip\Util;
use PreSale\Models\carTicket\BizCarTicket;
use TripcloudCommon\Utils\Product as TripcloudProduct;
use Xiaoju\Apollo\Apollo as ApolloV2;
use Xiaoju\Apollo\DefaultToggle;
use Xiaoju\Apollo\FeatureToggle;
use Xiaoju\Apollo\ToggleResult;

/**
 * 获取冒泡环节沟通组件信息
 * 设计wiki：http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=412527831
 *          http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=666518592
 * Class PGetCommunicateInfoController
 */
class PGetCommunicateInfoController extends \PreSale\Core\Controller
{

    const OPEN_MEMBER          = 'open_member';// 开通会员
    const MEMBER_RENEWAL       = 'member_renewal';// 会员续费
    const MEMBER_BONUS_PACKAGE = 'member_bonus_package';//会员加量包

    const FilterComponent = 'filter';

    private  $_sMemberType;
    private  $_iExpireDay;
    private $_aTypeEventMap;
    private $_aParam;

    private $_aPassengerInfo;
    private $_aQuotationInfo;
    private $_aProductList;
    private $_aProductComboData;
    private $_aPaidMemberData;
    private $_aCompensationWaitData;
    private $_aGetDiscountInfoData;
    private $_aDefaultDiscountInfo;
    private $_aCommunicateInfoText;
    private $_aPreferentialConfigText;
    private $_aPreferentialProducts;
    //城际拼车（远途特快）的沟通位券信息
    private $_aInterCityCarpoolInfo;
    //市内拼车（两口价V3）的沟通位券信息
    private $_aCarpoolActivityInfo;
    private $_aSpaciousCarInfo;
    private $_aMemberBenefitInfo;
    private $_aMemberCommuteUser;
    private $_aTaskReminderInfo;
    private $_aRecommendProduct; // 大额优惠推荐品类
    private $_aLuxuryMemberShip; // 会员身份

    private $_aRecommendComboData;

    private $_bNeedEpidemicPrevent;
    private $_iBusinessPayMaxAmount = 0; //  企业支付的最多金额

    private $_aUnpaidOrderInfo; //未支付订单

    private $_aCombinedTravelRecommendInfo;     // 组合出行推荐位信息
    private $_sCombinedTravelCompareEstimateId; // 组合出行对比EstimateId

    private $_aPriceDiffCompensate; // 价差补偿
    private $_aPriceStandardPrivileges; // 价准保权益
    private $_bSpaciousCarUpgrade; // 车大升级权益
    private $_sSpaciousCarMemberLevel; // 车大升级权益

    private $_bLuxCarNoVipTag;    // 是否是处于豪华车会员人群标签

    private $_aApolloPcId2EventKey;
    private $iCommunicateStyle;
    private $iRuleType;
    private $sDcmpKeyApollo;

    /**
     * k=eid;v=remainTimes
     * level_id
     * percent
     * @var array 会员确定性感知
     */
    private $_aMemberCertainty = [];

    /**
     * @var bool 专车付费会员
     */
    private $_bIsPremiumPaidMember = [];

    /**
     * @var bool 从未开通过专车付费会员的新用户
     */
    private $_bIsPremiumPaidNewMember = [];

    /**
     * @var integer 新会员（未开通）命中膨胀券金额
     */
    private $_iPremiumPaidNewMemberCouponAmout;

    /**
     * @var array 用户标签系统命中情况
     */
    private $_aPassengerTagRet = [];

    /**
     * @var array 排队结果
     */
    private $_aQueueRet = [];

    /**
     * @var array 单单省命中情况
     */
    private $_aDanDanSheng = [];

    /**
     * @var array 配置化沟通组件
     */
    private $_aCustomizedEvent = [];

    /**
     * @var array 拼车配置化沟通组件
     */
    private $_aCarpoolCustomizedEvent = [];

    /**
     * @var WeightLogic
     */
    private $_oWeight;

    /**
     * 预估是否返回置顶样式
     * @var bool
     */
    private $_bStickStyle = false;

    /**
     * @var bool 是否命中沟通组件拆分放量
     */
    private $_bCommunicateSplit = false;

    /**
     * @var array 豪华车可用券信息
     */
    private $_aLuxCoupon = null;

    /**
     * @var int multiple n倍返倍数
     */
    private $_iMultiple = 0;

    /**
     * @var int activityType 活动类型
     */
    private $_iActivityType = 0;

    const POPE_ACTIVITY_N_FOLD = 1;

    const PASSENGER = 2;


    // 推荐决策类型，目前有付费会员、智能套餐
    const RECOMMEND_DECIDE_TYPE_DEFAULT = 0;
    const RECOMMEND_DECIDE_TYPE_MEMBER  = 1; // 付费会员
    const RECOMMEND_DECIDE_TYPE_COMBO   = 2; // 智能套餐

    const RECOMMEND_SHOW_TYPE = 1;
    const SCENE_BARGAIN       = 24;
    const TYPE_ATHENA_TAG     = 1;  //athena返回的推荐
    const TYPE_BARGAIN_TAG    = 2;  //活动返回的tag

    const STATUS_BARGAIN_AGREE = 1;  //可以参与砍价

    const COUPON_TAG_PERORDERSALE        = 'PERORDERSALE'; // pope出的单单省
    const COUPON_TAG_PREMIUM_PAID_MEMBER = 'premium_paid_member'; // 专车付费会员

    const LUX_CAR_NO_VIP_TAG            = '**********';                        // 非豪华车会员人群
    const LUXURY_CAR_COUPONS_CUSTOM_TAG = 'luxury_paid_member'; // 豪华车券标识
    const ONE_DAY = 86400; // 一天的时间戳

    const  REVOLVING_ACCOUNT_DISCOUNT       = 'revolvingAccountDiscount';//内循环账户折扣
    const REVOLVING_ACCOUNT_DISCOUNT_CONFIG = 'revolving_account_discount'; //内循环账户折扣沟通组件文案
    const REVOLVING_ACCOUNT_DISCOUNT_RULE   = 'revolving_account_discount_rule'; //内循环账户折扣沟通组件规则

    const REVOLVING_ACCOUNT_REBATE        = 'revolvingAccountRebate';//内循环账户返利
    const REVOLVING_ACCOUNT_REBATE_CONFIG = 'revolving_account_rebate'; //内循环账户折扣沟通组件文案
    const REVOLVING_ACCOUNT_REBATE_N_FOLD = 'revolving_account_rebate_n_fold'; //内循环账户折扣沟通组件文案
    const REVOLVING_ACCOUNT_REBATE_RULE   = 'revolving_account_rebate_rule';//内循环账户返利沟通组件规则

    /**
     * 获取预估附属信息  ( 沟通组件, 弹窗 等)
     * @return void
     * @throws Exception|ExceptionWithResp 异常
     */
    public function indexAction() {
        try {
            $aResponse = UtilsCommon::pairErrNo(GLOBAL_SUCCESS);
            if (!$this->_checkAndHandlerParams()) {
                $aResponse['data'] = [];
                $this->sendTextJson($aResponse);
                return;
            }

            // 文案配置
            $this->_aCommunicateInfoText = NuwaConfig::text('communicate_component', 'communicate_info');
            if (empty($this->_aCommunicateInfoText)) {
                $aResponse['data'] = [];
                $this->sendTextJson($aResponse);
                return;
            }

            // 构建元数据
            $this->_buildCommunicateMetaInfo();

            // 获取CommonData
            $aResponse['data']['common_data'] = $this->_formatCommonData();

            // 沟通组件信息
            $this->_getCommunicateData($aResponse);

            // 气泡信息 追加套餐信息
            $aBubbleData = $this->_getBubbleData();
            if (!empty($aBubbleData)) {
                $aResponse['data']['bubble_data'] = $aBubbleData;
            }

            // 拦截弹窗
            $aResponse['data']['intercept_data'] = $this->_getInterceptData();

            // 指南弹窗
            $aResponse['data']['guide_popup_data'] = $this->_getGuidePopupData();

            // 个性化开关弹窗
            $aResponse['data']['eject_layer'] = $this->_getEjectLayer();

            $aResponse['trace_id'] = Trace::traceId();
        } catch (\Exception $e) {
            $aResponse = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg]);
        }

        $this->sendTextJson($aResponse);
        fastcgi_finish_request();
        PublicLog::getInstance()->write();
    }

    /**
     * @return array
     */
    private function _getBubbleData() {
        if ($this->_aParam['v3_form']) {
            $aBubbleData = (new Render())->do($this->_aParam, $this->_aQuotationInfo);
        } else {
            $aBubbleData = (new PActivityCardLogic())->getBubbleData($this->_aParam, $this->_aQuotationInfo);
        }

        (new RecommendComboLogic($this->_aParam, $this->_aQuotationInfo))->appendBubbleData($aBubbleData, $this->_getEstimateRecComboData());
        return $aBubbleData;
    }


    /**
     * @decs 获取沟通组件数据
     * @param array $aResponse 响应
     * @return void
     */
    private function _getCommunicateData(array &$aResponse) {
        $aCommunicateInfoText = $this->_aCommunicateInfoText;
        // 获取沟通组件阿波罗优先级配置
        $oApolloResult = $this->_getApolloConfig();
        if (empty($oApolloResult)) {
            return;
        }

        foreach ($this->_getConfigNameList() as $sConfigName) {
            // 处理价格沟通组件配置及优先级
            $aConfig = json_decode($oApolloResult->getParameter($sConfigName, null), true);
            $aConfig = $this->_sortArrayTwo($aConfig, 'digits', 'weight', SORT_DESC, SORT_DESC);
            foreach ($aConfig as $sKey => $aOneConfig) {
                $this->_aTypeEventMap[$aOneConfig['type']] = $aOneConfig['event'];
            }

            // 以下环节用组件化的形式来实现，工厂模式？参考发单预估或者预估response组件化改造?
            list($aPriorityParam, $aProductList) = $this->_buildPriorityParam($aConfig);
            switch ($sConfigName) {
                case 'rule_bottom':
                    $aResponse['data']['bottom_rule_data'] = $this->_formatRuleData($aProductList, $aPriorityParam, $aResponse['data']['common_data'], $aCommunicateInfoText);
                    break;
                default:
                    $aResponse['data']['rule_data'] = $this->_formatRuleData($aProductList, $aPriorityParam, $aResponse['data']['common_data'], $aCommunicateInfoText);
            }
        }
    }

    /**
     * 获取入参
     * @return bool
     * @throws ExceptionWithResp 抛出异常
     */
    private function _checkAndHandlerParams(): bool {
        $aRequestParam['multi_product_category'] = $this->oRequest->getStr('multi_product_category');
        $aRequestParam['lang']          = $this->oRequest->getStr('lang');
        $aRequestParam['token']         = $this->oRequest->getStr('token');
        $aRequestParam['area']          = $this->oRequest->getStr('area');
        $aRequestParam['starting_lat']  = $this->oRequest->getStr('starting_lat');
        $aRequestParam['starting_lng']  = $this->oRequest->getStr('starting_lng');
        $aRequestParam['dest_lat']      = $this->oRequest->getStr('dest_lat');
        $aRequestParam['dest_lng']      = $this->oRequest->getStr('dest_lng');
        $aRequestParam['utc_offset']    = $this->oRequest->getStr('utc_offset');
        $aRequestParam['channel']       = $this->oRequest->getStr('channel');
        $aRequestParam['client_type']   = $this->oRequest->getStr('client_type');
        $aRequestParam['platform_type'] = $this->oRequest->getStr('platform_type');
        $aRequestParam['app_version']   = $this->oRequest->getStr('app_version');
        $aRequestParam['origin_id']     = $this->oRequest->getStr('origin_id');
        $aRequestParam['to_name']       = $this->oRequest->getStr('to_name');
        $aRequestParam['access_key_id'] = $this->oRequest->getStr('access_key_id');
        $aRequestParam['model']         = $this->oRequest->getStr('model'); //手机型号
        $aRequestParam['estimate_trace_id']   = $this->oRequest->getStr('estimate_trace_id');
        $aRequestParam['estimate_style_type'] = $this->oRequest->getInt('estimate_style_type'); // 预估表单页面样式，1:单行，2:多行，0：现状
        $aRequestParam['page_type']           = $this->oRequest->getStr('page_type', '0');
        $aRequestParam['terminal_id']         = $this->oRequest->getStr('terminal_id');
        $aRequestParam['maptype'] = $this->oRequest->getStr('maptype');
        $aRequestParam['menu_id'] = $this->oRequest->getStr('menu_id', 'dache_anycar');
        $aRequestParam['ddfp']    = $this->oRequest->getStr('ddfp');
        $aRequestParam['lat']     = $this->oRequest->getStr('lat');
        $aRequestParam['lng']     = $this->oRequest->getStr('lng');
        $aRequestParam['v3_form'] = $this->oRequest->getStr('v3_form');

        $aResourceData = $this->_getProductList($aRequestParam['multi_product_category']);
        if (empty($aRequestParam['estimate_trace_id']) || empty($aResourceData)) {
            throw new ExceptionWithResp(
                Code::E_COMMON_PARAM_ERROR,
                RespCode::P_PARAMS_ERROR,
                '',
                ['request_data' => $aRequestParam]
            );
        }

        $this->_aProductList = $aResourceData;

        // 预估是否返回置顶沟通组件样式
        $this->_bStickStyle = 1 == $this->oRequest->getInt('is_stick_style');

        // 用户信息
        $aPassengerInfo = Passenger::getInstance()->getPassengerByToken($aRequestParam['token']);
        if (empty($aPassengerInfo)) {
            return false;
        }

        $aRequestParam['uid']   = $aPassengerInfo['uid'];
        $aRequestParam['phone'] = $aPassengerInfo['phone'];
        $aRequestParam['pid']   = $aPassengerInfo['pid'];

        // 城市信息
        $aCityInfo = MapHelper::getAreaInfoByLoc($aRequestParam['starting_lng'], $aRequestParam['starting_lat']);
        $aRequestParam['county_id'] = $aCityInfo['countyid'];

        $this->_aParam = $aRequestParam;

        if ($this->_hkTaxiIntercept()) {
            return false;
        }

        return true;
    }

    /**
     * 获取阿波罗配置
     * @return DefaultToggle|FeatureToggle|ToggleResult
     */
    private function _getApolloConfig() {

        $oApollo       = new NuwaApollo();
        $oApolloResult = $oApollo->featureToggle(
            'bubble_communicate_component',
            [
                'key'           => $this->_aParam['phone'],
                'phone'         => $this->_aParam['phone'],
                'app_version'   => $this->_aParam['app_version'],
                'access_key_id' => $this->_aParam['access_key_id'],
                'lang'          => $this->_aParam['lang'],
                'city'          => $this->_aParam['area'],
                'model'         => $this->_aParam['model'],
                'config'        => 1,
                'page_type'     => $this->_aParam['page_type'],
            ]
        );
        if (!$oApolloResult->allow() || empty($oApolloResult->getParameter('rule', null))) {
            return null;
        }

        return $oApolloResult;
    }

    /**
     * 判断沟通组件是否上下拆分
     * @return bool
     */
    private function _isCommunicateSplit() {
//        if (CommonInfo::PAGE_TYPE_SPECIAL_RATE == $this->_aParam['page_type']) {
//            return false;
//        }

        //v3新表单全量拆分
        if ($this->_aParam['v3_form']) {
            return true;
        }

        return NuwaApollo::getInstance()->featureToggle(
            'gs_est_communicate_split',
            [
                'key'           => $this->_aParam['pid'],
                'phone'         => $this->_aParam['phone'],
                'access_key_id' => $this->_aParam['access_key_id'],
                'lang'          => $this->_aParam['lang'],
                'city'          => $this->_aParam['area'],
                'app_version'   => $this->_aParam['app_version'],
            ]
        )->allow();
    }

    /**
     * 构建元数据信息
     * @return void
     */
    private function _buildCommunicateMetaInfo() {
        // Public日志初始化
        PublicLog::getInstance()->init($this->_aParam);

        // 文案配置
        $this->_aCommunicateInfoText = NuwaConfig::text('communicate_component', 'communicate_info');

        //获取报价单数据
        $this->_aQuotationInfo = $this->_getQuotation();
        // 通过报价单获取返利信息
        list($this->_iActivityType, $this->_iMultiple) = $this->_getRebateInfo();
        //获取智能套餐数据
        $oComboLogic = new RecommendComboLogic($this->_aParam, $this->_aQuotationInfo);
        $this->_aRecommendComboData = $oComboLogic->getRecommendComboData();

        $this->_aProductComboData = $this->_getProductComboData();

        //获取围栏信息
        $oEpidemic = new EpidemicCommunicateInfoLogic($this->_aParam);
        $this->_bNeedEpidemicPrevent = $oEpidemic->needFencePreventCommunicate();

        // 加载优惠配置
        $this->_aPreferentialConfigText = Config::text('config_text', 'preferential_feel_info');

        // 获取远程特快优惠券信息
        $oCommunicateInfoLogic        = new CommunicateInfoLogic($this->_aParam);
        $this->_aInterCityCarpoolInfo = $oCommunicateInfoLogic->getInterCityCarpoolInfo();
        // 获取市内拼车（两口价V3）券活动信息 （2021年123活动）
        $this->_aCarpoolActivityInfo = $oCommunicateInfoLogic->getCarpoolInfo($this->_getQuotation());

        // 车大联盟流量
        $this->_aSpaciousCarInfo = $oCommunicateInfoLogic->getSpaciousCarInfo($this->_getQuotation());

        // 获取橙意保等必补数据
        $this->_aCompensationWaitData = $this->_getCompensationWaitData();

        //获取优惠中心感知数据
        list($this->_aDefaultDiscountInfo, $this->_aGetDiscountInfoData) = $oCommunicateInfoLogic->getDiscountInfo();

        // 构建子权重数组
        $this->_oWeight = WeightLogic::getInstance();
        $this->_oWeight->setQuotation($this->_aQuotationInfo)->buildSubWeight();

        // 会员权益
        MemberBenefitInfoLogic::getInstance()->init($this->_aParam, $this->_aQuotationInfo);
        $this->_aMemberBenefitInfo = MemberBenefitInfoLogic::getInstance()->getMemberBenefitInfo();
        $this->_aMemberCertainty   = MemberBenefitInfoLogic::getInstance()->getMemberCertainty();
        $this->_aMemberCommuteUser = MemberBenefitInfoLogic::getInstance()->getMemberCommuteUser();

        // 会员身份
        MemberShip::getInstance()->init($this->_aParam, $this->_aQuotationInfo);
        $this->_aLuxuryMemberShip = MemberShip::getInstance()->getLuxryMemberInfo();

        // 专车会员权益
        $this->_bIsPremiumPaidMember = MemberBenefitInfoLogic::getInstance()->isPremiumPaidMember();
        $this->_iPremiumPaidNewMemberCouponAmout = $this->_getNewMemberCouponAmount();
        $this->_bIsPremiumPaidNewMember          = $this->_iPremiumPaidNewMemberCouponAmout > 0;

        //优惠任务提示
        $oTaskReminder            = new TaskReminderLogic($this->_aParam, $this->_aQuotationInfo);
        $this->_aTaskReminderInfo = $oTaskReminder->getTaskReminderInfo();

        //未支付订单信息
        $this->_aUnpaidOrderInfo = $this->_getUnPaidOrder();

        // 拼车省钱卡推荐
        CarpoolCommuteCardLogic::getInstance()->loadCommuteCardRelated($this->_aQuotationInfo, $this->_aParam);

        // 请求赔付系统获取赔付能力
        if (NuwaApollo::getInstance()->featureToggle(
            'gs_aplus_sub_title_switch',
            [
                'key'           => $this->_aParam['pid'],
                'phone'         => $this->_aParam['phone'],
                'city'          => $this->_aParam['area'],
                'access_key_id' => $this->_aParam['access_key_id'],
                'app_version'   => $this->_aParam['app_version'],
            ]
        )->allow() || $this->_aParam['v3_form']
        ) {
            CompensationLogic::getInstance()->getMultiCompensationAbilityV2($this->_aParam, $this->_aQuotationInfo);
        }

        //获取athenaExtension信息
        AthenaExtension::getInstance()->fetchAthenaExtensionInfo($this->_aParam);

        // 请求标签系统
        $aTag  = [];
        $aTag += DispatchOrderLeanLogic::getLeanTags($this->_aParam);

        if (count($aTag) > 0) {
            $sTag = implode(',', $aTag);
            $this->_aPassengerTagRet = TagServiceRepository::getPassengerTagResult($this->_aParam['pid'], $sTag, TagServiceRepository::DEFAULT_CALLER);
        }

        $aHitTag = DispatchOrderLeanLogic::getHitTag($this->_aParam, $this->_aPassengerTagRet);
        // 获取排队信息
        if (!empty($aHitTag) || count($this->_aMemberCertainty['benefit']) > 0) {
            $this->_aQueueRet = LineUpOrderComModel::getQueueLen(
                $this->_aParam['area'],
                $this->_aParam['starting_lat'],
                $this->_aParam['starting_lng'],
                [
                    DispatchOrderLeanLogic::QUEUE_TYPE_FLASH,
                    DispatchOrderLeanLogic::QUEUE_TYPE_CARPOOL,
                ]
            );
        }

        // 初始化派单倾斜状态
        DispatchOrderLeanLogic::getInstance()->prepare($this->_aPassengerTagRet, $this->_aQueueRet , $this->_aParam);
        DispatchOrderLeanLogic::getInstance()->getTagResult($aHitTag);
        DispatchOrderLeanLogic::getInstance()->calMaxDeduction($this->_aQuotationInfo);

        // 大额优惠信息-品类推荐信息
        $this->_getPreferentialProducts();

        // 获取单单省命中情况
        $this->_getDanDanShengExist();

        // 组合出行推荐位信息
        $this->_aCombinedTravelRecommendInfo = $this->_getCombinedTravelRecommendInfo();

        // 价差补偿
        $this->_aPriceDiffCompensate = $this->_getPriceDiffCompensate();

        // 超级会员价准保
        $this->_aPriceStandardPrivileges = $this->_getPriceStandardPrivileges();

        // 判断是否是豪华车目标人群
        $this->_bLuxCarNoVipTag = $this->_getTag(self::LUX_CAR_NO_VIP_TAG);

        // 特区配置化沟通组件
        $this->_aCustomizedEvent = $this->_getCustomizeResult();

        // 拼车配制化沟通组件（na跨年感知使用）
        $this->_aCarpoolCustomizedEvent = $this->_getCarpoolCustomizedResult();
    }

    /**
     * 获取组合出行推荐位信息
     * @return array
     */
    private function _getCombinedTravelRecommendInfo() {
        return [];
    }

    /**
     * 价差补偿感知，只适用于快车的实时单
     * @return array
     */
    private function _getPriceDiffCompensate() {
        $aProductList   = $this->_aProductList;
        $aQuotationData = $this->_aQuotationInfo;
        $aPriceDiffInfo = [];

        // 开城校验
        $aToggleParam = [
            'key'           => $this->_aParam['uid'],
            'phone'         => $this->_aParam['phone'],
            'pid'           => $this->_aParam['pid'],
            'access_key_id' => $this->_aParam['access_key_id'],
            'lang'          => $this->_aParam['lang'],
            'city'          => $this->_aParam['area'],
            'app_version'   => $this->_aParam['app_version'],
            'caller'        => 'pre-sale',
        ];

        $oToggle = NuwaApollo::getInstance()->featureToggle('gs_price_diff_compensate_experiment', $aToggleParam);
        if (!$oToggle->allow()) {
            return $aPriceDiffInfo;
        }

        foreach ($aProductList as $sEstimateId => $sOneEstimateData) {
            // 根据阿波罗开城就展示
            $aPriceDiffInfo[$sEstimateId] = false;
            if (ProductCategory::PRODUCT_CATEGORY_FAST == $aQuotationData[$sEstimateId]['product_category']) {
                $aNTuple = json_decode($aQuotationData[$sEstimateId]['n_tuple'], true);
                if (!empty($aNTuple) && OrderSystem::TYPE_ORDER_NOW == $aNTuple['order_type']) {
                    $aPriceDiffInfo[$sEstimateId] = true;
                }
            }
        }

        return $aPriceDiffInfo;
    }

    /**
     * 超级会员- 价准保权益
     * @return array
     */
    private function _getPriceStandardPrivileges() {
        $aProductList   = $this->_aProductList;
        $aQuotationData = $this->_aQuotationInfo;
        $aPriceStandard = [];

        foreach ($aProductList as $sEstimateId => $sOneEstimateData) {
            // 快车  是会员   有权益
            if (ProductCategory::PRODUCT_CATEGORY_FAST == $aQuotationData[$sEstimateId]['product_category']) {
                $aMember = MemberBenefitInfoLogic::getInstance()->getMemberInfoByEid($sEstimateId);
                if (!empty($aMember['privileges']['price_standard']['item'][0])) {
                    $aData = $aMember['privileges']['price_standard']['item'][0];

                    if ('paid_member' == $aData['backend']['privilege_source']) {
                        $aPriceStandard[$sEstimateId] = true;
                    }
                }

                // 车大升级权益
                if (!empty($aMember['privileges']['upgrade_spacious_car'])) {
                    $this->_bSpaciousCarUpgrade     = true;
                    $this->_sSpaciousCarMemberLevel = $aMember['identity_name'] ?: '';
                }
            }
        }

        return $aPriceStandard;
    }

    /**
     * @return array $aQuotationData
     */
    private function _getQuotation() {
        $aEstimateIds = [];
        foreach ($this->_aProductList as $sEstimateId => $aResourceDatum) {
            $aEstimateIds[] = $sEstimateId;
        }

        $aReq = [
            'estimate_id_list' => $aEstimateIds,
            'fields'           => array(
                'deduction_fee',
                'n_tuple',
                'default_pay_type',
                'basic_total_fee',
                'diversion_coupon_amount',
                'diversion_deduction_fee',
                'departure_time',
                'discount_desc',
                'is_mixed_payment',
                'product_category',
                'dynamic_total_fee',
                'estimate_fee',
                'dynamic_times',//动调倍数
                'cap_price',
                'pre_total_fee',
                'currency',
                'combo_type',
                'discount_desc',
                'carpool_seat_num', // 乘客选择的座位数
                'estimate_fee_fail', // 拼车为拼车的价格
                'driver_metre',
                'revolving_account_rebate_amount', //内循环账户返利金额
                'highway_fee', // 高速费
            ),
        ];
        //2.查询报账单
        $aQuotationResult = (new PriceApiClient())->getQuotationBatch($aReq);
        if (empty($aQuotationResult) || 0 != $aQuotationResult['errno'] || empty($aQuotationResult['data'])) {
            return [];
        }

        $aQuotationList = $aQuotationResult['data'];
        foreach ($aQuotationList as $sEstimateId => &$aQuotation) {
            $aNTuple = json_decode($aQuotation['n_tuple'], true);
            if (empty($this->_aParam['order_type'])) {
                $this->_aParam['order_type'] = $aNTuple['order_type'];
            }

            if (empty($this->_aParam['departure_time'])) {
                $this->_aParam['departure_time'] = $aQuotation['departure_time'];
            }

            // 新样式 取可企业支付的最大值，排除混合支付的场景
            if ($this->_aParam['estimate_style_type'] > 0 && Order::BUSINESS_PAY_BY_BUSINESS_BALANCE == $aQuotation['default_pay_type']
                && 0 == $aQuotation['is_mixed_payment'] && version_compare($this->_aParam['app_version'], '6.2.12') < 0
            ) {
                $this->_iBusinessPayMaxAmount = max($this->_iBusinessPayMaxAmount, $aQuotation['dynamic_total_fee']);
            }

            $aQuotation['pay_type']      = $aQuotation['default_pay_type'];
            $aQuotation['business_id']   = $aNTuple['business_id'];
            $aQuotation['product_id']    = $aNTuple['product_id'];
            $aQuotation['combo_type']    = $aNTuple['combo_type'];
            $aQuotation['require_level'] = $aNTuple['require_level'];
            $aQuotation['carpool_type']  = $aNTuple['carpool_type'];
            $aQuotation['is_dual_carpool_price'] = $aNTuple['is_dual_carpool_price'];
            $aQuotation['is_fast_car']           = Constants\Dos::PRODUCT_ID_FAST_CAR == $aNTuple['product_id'];

            if (ProductCategory::PRODUCT_CATEGORY_FAST == $aQuotation['product_category']) {
                // 组合出行比较estimate_id：普通快车
                $this->_sCombinedTravelCompareEstimateId = $sEstimateId;
            }
        }

        return $aQuotationList;
    }

    /**
     * 获取最终优先级权重数据
     * @param array $aConfig        rule配置信息
     * @param array $aPriorityParam 优先级原始数据
     * @param array $aProductList   产品列表
     * @return mixed
     */
    private function _sortPriority(array $aConfig, array $aPriorityParam, array $aProductList) {
        $aCapPriceRuleType = [
            Common::RULE_TYPE_CAP_PRICE,
            Common::RULE_TYPE_SHENGANG_FLAT_RATE,
            Common::RULE_TYPE_FAST_PRICE,
            Common::RULE_TYPE_DIONE_PRICE,
        ];

        $bFixedMeetThreshold = false;
        $oUfs      = new UfsClient();
        $aQueryRet = $oUfs->getFeature(
            ['fixed_price_ord_amt_m'],
            [
                'passenger_id' => $this->_aParam['pid'],
            ],
            'passenger'
        );
        if (!empty($aQueryRet) && isset($aQueryRet) && 0 == $aQueryRet['errno']) {
            if (!empty($aQueryRet['result']) && !empty($aQueryRet['result']['fixed_price_ord_amt_m'])) {
                if ((int)$aQueryRet['result']['fixed_price_ord_amt_m'] > 6) {
                    $bFixedMeetThreshold = true;
                }
            }
        }

        //1、遍历各个品类
        foreach ($aPriorityParam as $sEstimateId => &$aBubbleValue) {
            $aComponentConfigCopy = $aConfig;
            //2、确定一个品类的最高权重的配置项
            //2.1 如果该品类具有价格沟通组件,且价格沟通组件为一口价,且该用户当月的一口价次数>=6
            //获取该用户一口价次数
            if (!empty($aBubbleValue['special_rule']) && !empty($aProductList[$sEstimateId]['special_rule']['rule_type'])) {
                $bOnlyIncludeCapPrice     = true;
                //如果只有一口价
                foreach ($aProductList[$sEstimateId]['special_rule']['rule_type'] as $iRuleType) {

                    //如果存在不是一口价的
                    if (!in_array($iRuleType, $aCapPriceRuleType)) {
                        $bOnlyIncludeCapPrice = false;
                        break;
                    }
                }

                //2.2 特殊价格沟通组件(只有一口价且一口价单量达到阈值)，则优先级置为最低
                if ($bOnlyIncludeCapPrice && $bFixedMeetThreshold) {
                    foreach ($aComponentConfigCopy as $sKey => &$aValue) {
                        if ('special_rule' == $aValue['event']) {
                            $aValue['weight'] = 1;
                            break;
                        }
                    }
                }
            }

            $aComponentConfigCopy = $this->_sortArrayTwo($aComponentConfigCopy, 'digits', 'weight', SORT_DESC, SORT_DESC);
            foreach ($aComponentConfigCopy as $aComponentConfig) {
                //3、如果该品类有此业务
                // 同一品类内, "业务"之间优先级: digits最高的, weight最高的
                if (!empty($aBubbleValue[$aComponentConfig['event']])) {
                    $aBubbleValue['sum_weight']  = $aComponentConfig['weight'] * pow(10, $aComponentConfig['digits']);
                    $aBubbleValue['type']        = $aComponentConfig['type'];
                    $aBubbleValue['style']       = $aComponentConfig['style'];
                    $aBubbleValue['action_type'] = $aComponentConfig['action_type'];

                    //对冒泡业务项的子项权重进行累加
                    if (!empty($aComponentConfig['continue'])) {
                        $aSubValue  = $aComponentConfig['sub'];
                        $aSubWeight = $aBubbleValue['sub_weight'];
                        foreach ($aSubValue as $aOneSubValue) {
                            if (empty($aSubWeight[$aOneSubValue['event']])) {
                                continue;
                            }

                            $aBubbleValue['sum_weight'] += $aSubWeight[$aOneSubValue['event']] * pow(10, $aOneSubValue['digits']);
                        }
                    }

                    break; // 取第一个
                }
            }

            unset($aComponentConfigCopy);
        }

        return $aPriorityParam;
    }

    /**s
     * 初始化原始数据
     * @param string $sMultiProductCategory 多预估品类数据
     * @return array
     */
    private function _getProductList(string $sMultiProductCategory) {
        $aResourceData         = [];
        $aMultiProductCategory = json_decode($sMultiProductCategory, true);
        foreach ($aMultiProductCategory as $aOneMultiProductCategory) {
            if (empty($aOneMultiProductCategory['estimate_id'])) {
                continue;
            }

            $aResourceData[$aOneMultiProductCategory['estimate_id']] = [
                'estimate_id'         => $aOneMultiProductCategory['estimate_id'],
                'business_id'         => $aOneMultiProductCategory['business_id'],
                'require_level'       => $aOneMultiProductCategory['require_level'],
                'payment_id'          => $aOneMultiProductCategory['payment_id'],
                'is_select_rec_combo' => isset($aOneMultiProductCategory['is_select_rec_combo']) ? $aOneMultiProductCategory['is_select_rec_combo'] : 0,

            ];
        }

        return $aResourceData;
    }

    /**
     * 获取车票数据
     * @return array
     */
    private function _getCommonPromoteSalesTipRule() {
        if (empty($this->_aParam['dest_lat']) || empty($this->_aParam['dest_lng']) || empty($this->_aParam['to_name']) || Language::ZH_CN != $this->_aParam['lang']) {
            return [];
        }

        $oBizCarTicket        = new BizCarTicket();
        $aCarTicketTip        = $oBizCarTicket->getPromoteSalesTip($this->_aParam['token'], $this->_aParam['dest_lat'], $this->_aParam['dest_lng']);
        $aBusinessTicketPrice = [
            'multi_scene_text'  => '',//multi_scene_text预留字段，防止后续多个文案冲突
            'single_scene_text' => $aCarTicketTip,
            'link_url'          => $oBizCarTicket->getPromoteSalesUrl() . '&to_location=' . $this->_aParam['dest_lat'] . ',' . $this->_aParam['dest_lng'] . '&to_name=' . $this->_aParam['to_name'],
        ];
        return $aBusinessTicketPrice;
    }


    /**
     * 获取通用场景计价描述
     * @return array
     */
    private function _buildCommonSpecialRule() {
        $iHighWayFee = 0;
        foreach ($this->_aQuotationInfo as $aQuotation) {
            if ($aQuotation['highway_fee']>0){
                $iHighWayFee = $aQuotation['highway_fee'];
                break;
            }
        }

        $aToggleParam = [
            'key'           => $this->_aParam['pid'],
            'phone'         => $this->_aParam['phone'],
            'pid'           => $this->_aParam['pid'],
            'access_key_id' => $this->_aParam['access_key_id'],
            'lang'          => $this->_aParam['lang'],
            'city'          => $this->_aParam['area'],
            'app_version'   => $this->_aParam['app_version'],
        ];

        $oToggle = NuwaApollo::getInstance()->featureToggle('highway_fee_out_grey', $aToggleParam);

        if ($oToggle->allow()) {
            $sMultiSceneDcmpKey  = 'special_price_rule_explain-multi_scene_common_text_v2_highwayfeeout';
            $sSingleSceneDcmpKey = 'special_price_rule_explain-single_scene_common_text_v2';
        } else {
            $sMultiSceneDcmpKey  = 'special_price_rule_explain-multi_scene_common_text_v2';
            $sSingleSceneDcmpKey = 'special_price_rule_explain-single_scene_common_text';
        }

        if (Util::isHighAppVersion($this->_aParam['access_key_id'], $this->_aParam['app_version'])) {
            $sSingleSceneDcmpKey = 'special_price_rule_explain-single_scene_common_text_v2_rich_text';
            if ((ApolloV2::getInstance())->featureToggle('red_packet_change_text_switch', ['city' => $this->_aParam['area']])->allow()) {
                $sSingleSceneDcmpKey = 'special_price_rule_explain-single_scene_common_text_v2_rich_text_full_pay';
            }
        }

        return [
            'multi_scene_text'    => Language::getTextFromDcmp('special_price_rule_explain-multi_scene_common_text'),
            'multi_scene_text_v2' => json_decode(
                Language::getTextFromDcmp(
                    $sMultiSceneDcmpKey,
                    [
                        'highway_fee' => $iHighWayFee,
                    ]
                ),
                true
            ) ?? [],
            'single_scene_text'   => json_decode(
                Language::getTextFromDcmp(
                    $sSingleSceneDcmpKey,
                    [
                        'highway_fee' => $iHighWayFee,
                    ]
                ),
                true
            ) ?? [],
        ];
    }


    /**
     * @return array
     */
    private function _getCompensationWaitCommonData() {
        return [
            'multi_scene_text'  => '',
            'single_scene_text' => ['1' => $this->_aCompensationWaitData['text']],
            'link_url'          => $this->_aCompensationWaitData['url'],
        ];
    }

    /**
     * 获取企业支付提示信息
     * @return array
     */
    private function _getBusinessPayCommonData() {
        $sText = $this->_aCommunicateInfoText['business_pay_rule']['text'];
        $sText = Language::replaceTag($sText, ['amount' => $this->_iBusinessPayMaxAmount]) ?? '';
        return [
            'multi_scene_text'  => '',
            'single_scene_text' => ['1' => $sText],
        ];
    }

    /**
     * 获取派单倾斜
     * @return array
     */
    private function _getDispatchLeanCommonData() {
        $aText = $this->_aCommunicateInfoText['new_dispatch_order_lean'] ?? [];
        $sText = DispatchOrderLeanLogic::getInstance()->getCommonContent($aText);

        return [
            'multi_scene_text'  => '',
            'single_scene_text' => ['1' => $sText],
        ];
    }

    /**
     * 获取价差补偿
     * @return array
     */
    private function _getPriceDiffCommonData() {
        $aPriceDiffCompConf = $this->_aCommunicateInfoText['price_diff_compensate'] ?? [];
        $sLink = '';

        if (in_array($this->_aParam['access_key_id'], [Constants\Common::DIDI_IOS_PASSENGER_APP, Constants\Common::DIDI_ANDROID_PASSENGER_APP])) {
            $sLink = $aPriceDiffCompConf['link_url_app'] ?? '';
        } elseif (in_array($this->_aParam['access_key_id'], [Constants\Common::DIDI_WECHAT_MINI_PROGRAM, Constants\Common::DIDI_ALIPAY_MINI_PROGRAM])) {
            $sLink = $aPriceDiffCompConf['link_url_mini'] ?? '';
        }

        return [
            'multi_scene_text' => $aPriceDiffCompConf['text'] ?? '',
            'link_url'         => $sLink,
            'text_color'       => $aPriceDiffCompConf['text_color'] ?? '',
            'bg_color'         => $aPriceDiffCompConf['bg_color'] ?? '',
        ];
    }

    /**
     * 价准保权益
     * @return array
     */
    private function _getPriceStandardCommonData() {
        $aPriceDiffCompConf = $this->_aCommunicateInfoText['price_standard_privileges'] ?? [];

        return [
            'multi_scene_text' => $aPriceDiffCompConf['text'] ?? '',
            'text_color'       => $aPriceDiffCompConf['text_color'] ?? '',
        ];
    }

    /**
     * 特区配置化沟通组件
     * @return array
     */
    private function _getCustomizedCommonData() {
        return [
            'multi_scene_text' => $this->_aCustomizedEvent['text'] ?? '',
            'text_color'       => $this->_aCustomizedEvent['font_color'] ?? '',
        ];
    }

    /**
     * 拼车配置化沟通组件
     * @return array
     */
    private function _getCarpoolCustomizedCommonData() {
        $aCarpoolCustom = $this->_aCommunicateInfoText['carpool_custom_style'] ?? [];

        return [
            'multi_scene_text'     => $this->_aCarpoolCustomizedEvent['text'] ?? '',
            'icon'                 => $this->_aCarpoolCustomizedEvent['img_url'] ?? '',
            'link_url'             => $this->_aCarpoolCustomizedEvent['link_url'] ?? '',
            'text_color'           => $aCarpoolCustom['text_color'] ?? '',
            'background_gradients' => $aCarpoolCustom['bg_gradients'] ?? '',
            'button'               => $aCarpoolCustom['button'] ?? '',
            'button_style'         => $aCarpoolCustom['button_style'],
        ];
    }

    /**
     * 获取单单省
     * @return array
     */
    private function _getDanDanShengCommonData() {
        $aPerOrderSaleConf = $this->_aCommunicateInfoText['per_order_sale'] ?? [];

        return [
            'multi_scene_text' => $aPerOrderSaleConf['text'] ?? '',
            'button'           => $aPerOrderSaleConf['button'] ?? '',
            'button_style'     => $aPerOrderSaleConf['button_style'],
            'icon'             => $aPerOrderSaleConf['icon'] ?? '',
            'link_url'         => $aPerOrderSaleConf['link_url'] ?? '',
            'bg_color'         => $aPerOrderSaleConf['bg_color'] ?? '',
        ];
    }

    /**
     * 获取疫情绕行信息
     * @return array
     */
    private function _getEpidemicPreventCommonData() {
        $sText = $this->_aCommunicateInfoText['epidemic_prevent_rule']['text'];
        return [
            'multi_scene_text'  => '',
            'single_scene_text' => ['1' => $sText],
        ];
    }


    /**
     *  沟通组件资源位-展示套餐
     * @return array
     */
    private function _getProductComboData() {
        if (empty($this->_aRecommendComboData)) {
            return [];
        }

        $aRecipeResourceData = $this->_aRecommendComboData['component_rec'];

        $aRecipeData = [];
        //沟通组件展示套餐
        if (!empty($aRecipeResourceData)) {
            foreach ($aRecipeResourceData as $aOneRecipeData) {
                $aRecipeData[$aOneRecipeData['estimate_id']] = $aOneRecipeData;
            }

            //对套餐进行优先级排序
            $aRecipeData = $this->_sortArrayOne($aRecipeData, 'priority', SORT_ASC);
            $i           = 1;
            foreach ($aRecipeData as &$aOneRecipeData) {
                $aOneRecipeData['weight'] = $i;
                $i++;
            }
        }

        return $aRecipeData;

    }

    /**
     * 冒泡页套餐搭售
     * @return array
     */
    private function _getEstimateRecComboData() {

        if (empty($this->_aRecommendComboData)) {
            return [];
        }

        $aEstimateSourceData   = $this->_aRecommendComboData['estimate_rec'];
        $aEstimateRecComboData = [];
        if (!empty($aEstimateSourceData)) {
            foreach ($aEstimateSourceData as $aData) {
                if (isset($this->_aProductList[$aData['estimate_id']])) {//二次预估，获取是否勾选了套餐（勾选状态由端带过来）
                    $aData['is_select_rec_combo'] = $this->_aProductList[$aData['estimate_id']]['is_select_rec_combo'];
                }

                $aEstimateRecComboData[$aData['estimate_id']] = $aData;
            }
        }

        return $aEstimateRecComboData;
    }

    /**
     * 获取等久必补的 数据
     * @return array|mixed
     */
    private function _getCompensationWaitData() {
        $aOrderInfo         = array(
            'passenger_id'    => $this->_aParam['pid'],
            'area'            => $this->_aParam['area'],
            'passenger_phone' => $this->_aParam['phone'],
            'type'            => $this->_aParam['order_type'],
            'product_id'      => OrderNTuple::PRODUCT_ID_DEFAULT,
            'business_id'     => OrderNTuple::COMMON_PRODUCT_ID_DEFAULT,
        );
        $aDriverArrivedLate = new DriverArrivedLate($aOrderInfo, Language::getLanguage(), CompensationInfo::FROM_PRE_SALE_GET_COMMUNICATE_INFO);
        $aCompensationInfo  = $aDriverArrivedLate->getCompensationBubble();
        if (empty($aCompensationInfo)) {
            return [];
        }

        return $aCompensationInfo;
    }

    /**
     * 获取最终优先级数据的准备数据,各品类的某项冒泡业务的数据的优先级在这里已算好
     * @param array $aConfig 配置数据
     * @return array [优先级数据,原始品类数据]
     */
    private function _buildPriorityParam(array $aConfig) {
        $aPriorityParam = [];
        $aQuotationData = $this->_aQuotationInfo;
        $aProductList   = $this->_aProductList;

        $aSubWeightData = $this->_oWeight->getSubWeightData();

        $iAccessKeyID = $this->_aParam['access_key_id'];

        //获取原始数据及优先级数据的排序
        $aCategoryConfig = (new TimeDiscountLogic())->getPeriodDiscounCategoryConfig();
        foreach ($aProductList as $sEstimateId => &$sOneEstimateData) {
            $iProductCategory = $aQuotationData[$sEstimateId]['product_category'];

            // 默认是隐私保护 (除香港品类)
            if (Product::COMMON_PRODUCT_ID_HK_TAXI != $sOneEstimateData['business_id']) {
                $aPriorityParam[$sEstimateId]['privacy_protection'] = 1;
            }

            //获取价格沟通组件的数据
            $oRedisdb = \PreSale\Logics\redismove\RedisWrapper::getInstance(P_SPECIAL_PRICE_COMPONENT);
            $sKey     = UtilsCommon::getRedisPrefix(P_SPECIAL_PRICE_COMPONENT) . $sEstimateId;
            $sSpecialPriceData = $oRedisdb->get($sKey);
            if (!empty($sSpecialPriceData)) {
                $sOneEstimateData['special_rule'] = json_decode($sSpecialPriceData, true);
                $aPriorityParam[$sEstimateId]['special_rule'] = 1;
            }

            // qq 小程序只需要价格沟通组件
            if (Constants\Common::DIDI_QQ_MINI_PROGRAM == $iAccessKeyID) {
                continue;
            }

            // 远途拼车品类-远途特快 沟通位
            if (Horae::isInterCityCarpoolNewMode($aQuotationData[$sEstimateId]['combo_type'], $aQuotationData[$sEstimateId]['carpool_type'])) {
                if (!empty($this->_aInterCityCarpoolInfo)) {
                    $aPriorityParam[$sEstimateId]['inter_city_carpool_rule']    = 1;
                    $sOneEstimateData['inter_city_carpool_rule']['rule_type'][] = 1;
                }
            }

            // 市内拼车品类-两口价V3/区域一口价 沟通位
            if (\BizCommon\Utils\Horae::isCarpoolDualPrice($aQuotationData[$sEstimateId])
                || \BizCommon\Utils\Horae::isCarpoolFlatRate($aQuotationData[$sEstimateId])
            ) {
                if (!empty($this->_aCarpoolActivityInfo)) {
                    $aPriorityParam[$sEstimateId]['carpool_rule']    = 1;
                    $sOneEstimateData['carpool_rule']['rule_type'][] = 1;
                }
            }

            // 自定义key支持
            $this->_aApolloPcId2EventKey[$iProductCategory] = $this->isHitApolloCommonEvent($iProductCategory);
            if (!empty($this->_aApolloPcId2EventKey[$iProductCategory])) {
                $aPriorityParam[$sEstimateId][$this->_aApolloPcId2EventKey[$iProductCategory]]    = 1;
                $sOneEstimateData[$this->_aApolloPcId2EventKey[$iProductCategory]]['rule_type'][] = 1;
            }

            // 车大联盟流量需求 开城活动
            if (!empty($this->_aSpaciousCarInfo)
                && $this->_aSpaciousCarInfo['bIsHasSelectFee']
                && !empty($this->_aSpaciousCarInfo['sFastCarEID'])
                && in_array($this->_aParam['access_key_id'], [Constants\Common::DIDI_IOS_PASSENGER_APP, Constants\Common::DIDI_ANDROID_PASSENGER_APP])  // 只有端内处理这个沟通组件
                && $this->_aSpaciousCarInfo['sFastCarEID'] == $sEstimateId // 快车品类展示
            ) {
                $oApolloClient = Apollo::getInstance();
                $aApolloRes    = $oApolloClient->featureToggle(
                    'communicate_spacious_car_info',
                    [
                        'key'           => $this->_aParam['pid'],
                        'pid'           => $this->_aParam['pid'],
                        'phone'         => $this->_aParam['phone'],
                        'access_key_id' => $this->_aParam['access_key_id'],
                        'lang'          => $this->_aParam['lang'],
                        'city'          => $this->_aParam['area'],
                        'app_version'   => $this->_aParam['app_version'],
                    ]
                );
                $bSwitch       = $aApolloRes->allow();
                if ($bSwitch) {
                    $aPriorityParam[$sEstimateId]['spacious_car_rule']    = 1;
                    $sOneEstimateData['spacious_car_rule']['rule_type'][] = 1;
                }
            }

            // 车大权益升级
            if (!empty($this->_aSpaciousCarInfo)
                && $this->_aSpaciousCarInfo['bIsHasSelectFee']
                && $this->_aSpaciousCarInfo['sFastCarEID'] == $sEstimateId // 快车品类展示
                && !empty($this->_aSpaciousCarInfo['sps_spacious_car_member_discount'])
            ) {
                $aApolloRes = Apollo::getInstance()->featureToggle(
                    'gs_common_privilege',
                    [
                        'key'              => $this->_aParam['pid'],
                        'event'            => 'spacious_car_upgrade_event',
                        'pid'              => $this->_aParam['pid'],
                        'phone'            => $this->_aParam['phone'],
                        'access_key_id'    => $this->_aParam['access_key_id'],
                        'lang'             => $this->_aParam['lang'],
                        'city'             => $this->_aParam['area'],
                        'app_version'      => $this->_aParam['app_version'],
                        'product_category' => $aQuotationData[$sEstimateId]['product_category'],
                    ]
                );

                if ($aApolloRes->allow()) {
                    $aPriorityParam[$sEstimateId]['spacious_car_upgrade_event']    = 1;
                    $sOneEstimateData['spacious_car_upgrade_event']['rule_type'][] = 1;
                }
            }

            // NA端默勾
            $aTopInfo = AthenaExtension::getInstance()->getCommunicateInfo($iProductCategory);
            if (!empty($aTopInfo) && self::FilterComponent != $aTopInfo['tip_type']) {
                $aPriorityParam[$sEstimateId]['athena_tip_info_rule']    = 1;
                $sOneEstimateData['athena_tip_info_rule']['rule_type'][] = 1;
            }

            // 专车
            if (Constants\OrderSystem::PRODUCT_ID_DEFAULT == $aQuotationData[$sEstimateId]['product_id']
                && Language::ZH_CN == $this->_aParam['lang']
            ) {
                if ($this->_bIsPremiumPaidMember) { //是专车付费会员
                    // 付费会员  -> 券沟通组件
                    $aCoupon = $this->_getCouponInfo($sEstimateId, self::COUPON_TAG_PREMIUM_PAID_MEMBER);
                    // 判断会员还有多少天过期
                    $this->_iExpireDay = MemberBenefitInfoLogic::getInstance()->getPremiumPaidMemberExpireDay($sEstimateId);
                    if ($this->_iExpireDay < 7) { //专车付费会员  不到七天到期   --->  引导续费
                        $aPriorityParam[$sEstimateId]['premium_paid_activity_rule']    = 1;
                        $sOneEstimateData['premium_paid_activity_rule']['rule_type'][] = 1;
                        $this->_sMemberType = self::MEMBER_RENEWAL;
                    } else {
                        if (!empty($aCoupon)) {
                            $aPriorityParam[$sEstimateId]['premium_paid_coupon_rule']    = 1;
                            $sOneEstimateData['premium_paid_coupon_rule']['rule_type'][] = 1;
                        } else {
                            if (($this->_iExpireDay < 31)
                                || (38 <= $this->_iExpireDay && $this->_iExpireDay < 62)
                                || (69 <= $this->_iExpireDay && $this->_iExpireDay < 94)
                            ) {//没有优惠券 且没有续费过 引导加量包
                                $aPriorityParam[$sEstimateId]['premium_paid_activity_rule']    = 1;
                                $sOneEstimateData['premium_paid_activity_rule']['rule_type'][] = 1;
                                $this->_sMemberType = self::MEMBER_BONUS_PACKAGE;
                            }
                        }
                    }
                } else {
                    // 非付费会员  -> 活动组件 -->引导开通会员
                    $aApolloRes = Apollo::getInstance()->featureToggle(
                        'gs_premium_paid_member',
                        [
                            'key'           => $this->_aParam['pid'],
                            'pid'           => $this->_aParam['pid'],
                            'phone'         => $this->_aParam['phone'],
                            'access_key_id' => $this->_aParam['access_key_id'],
                            'lang'          => $this->_aParam['lang'],
                            'city'          => $this->_aParam['area'],
                            'app_version'   => $this->_aParam['app_version'],
                        ]
                    );

                    $bSwitch = $aApolloRes->allow();
                    if ($bSwitch && PremiumPaidMemberActivityRuleEvent::judgePeopleTagService($this->_aParam['pid'], $aApolloRes)) {
                        $aPriorityParam[$sEstimateId]['premium_paid_activity_rule']    = 1;
                        $sOneEstimateData['premium_paid_activity_rule']['rule_type'][] = 1;
                        $this->_sMemberType = self::OPEN_MEMBER;
                    }
                }
            }

            // 新样式 获取企业支付的提示数据
            if ($this->_aParam['estimate_style_type'] > 0 && $this->_iBusinessPayMaxAmount > 0) {
                $aPriorityParam[$sEstimateId]['business_pay_rule']    = 1;
                $sOneEstimateData['business_pay_rule']['rule_type'][] = 1;
            }

            // 疫情围栏绕行沟通数据
            if (!empty($this->_bNeedEpidemicPrevent)) {
                $aPriorityParam[$sEstimateId]['epidemic_prevent_rule']    = 1;
                $sOneEstimateData['epidemic_prevent_rule']['rule_type'][] = 1;
            }

            // 获取优惠中心感知
            if (!empty($this->_aGetDiscountInfoData) && in_array($iProductCategory, $aCategoryConfig)) {
                if (!empty($this->_aGetDiscountInfoData[$iProductCategory])) {
                    // 分品类展示文案
                    $sOneEstimateData['period_discount_rule']['rule_type'][] = (int)$iProductCategory;
                    $aPriorityParam[$sEstimateId]['period_discount_rule']    = 1;
                } elseif (empty($this->_aDefaultDiscountInfo)) {
                    // 不分品类,
                    $sOneEstimateData['period_discount_rule']['rule_type'][] = 1;
                    $aPriorityParam[$sEstimateId]['period_discount_rule']    = 1;
                }
            }

            // 洋流提醒，仅限微信小程序，优惠金额大于等于2元才提醒
            if (ConstCommon::DIDI_WECHAT_MINI_PROGRAM == $this->_aParam['access_key_id']
                && $aQuotationData[$sEstimateId]['diversion_deduction_fee'] >= 200
            ) {
                $aPriorityParam[$sEstimateId]['diversional_coupon_rule']     = 1;
                $aPriorityParam[$sEstimateId]['sub_weight']['coupon_amount'] = $aSubWeightData[$sEstimateId]['diversion_coupon_amount'];
            }

            //获取滴滴支付的数据
            if (!empty($aQuotationData[$sEstimateId]) && !empty($aQuotationData[$sEstimateId]['discount_desc'])) {
                $aDiscountDesc         = json_decode($aQuotationData[$sEstimateId]['discount_desc'], true) ?? [];
                $bHavingDidiPay        = false;
                $bHavingOtherReduction = false;
                if (!empty($aDiscountDesc)) {
                    foreach ($aDiscountDesc as $index => $oneReduction) {
                        if (!empty($oneReduction['custom_tag']) && 'DIDIPAY' == $oneReduction['custom_tag']) {
                            $bHavingDidiPay = true;
                            continue;
                        }

                        if (empty($oneReduction['custom_tag']) || 'DIDIPAY' != $oneReduction['custom_tag']) {
                            $bHavingOtherReduction = true;
                        }
                    }

                    if ($bHavingDidiPay) {
                        $aPriorityParam[$sEstimateId]['didi_pay_rule'] = 1;
                        //只有滴滴支付券
                        if (!$bHavingOtherReduction) {
                            $sOneEstimateData['didi_pay_rule']['rule_type'][] = DiDiPayCommon::RULE_ONLY_DIDIPAY;
                        }

                        //不仅有滴滴支付券还有其它券
                        if ($bHavingOtherReduction) {
                            $sOneEstimateData['didi_pay_rule']['rule_type'][] = DiDiPayCommon::RULE_INCLUDE_DIDIPAY;
                        }
                    }
                }
            };

            //获取B端车票的数据
            //车票当前只是支持 快车产品线、600车型；
            if (Language::ZH_CN == $this->_aParam['lang']
                && OrderNTuple::COMMON_PRODUCT_ID_FAST_CAR == $sOneEstimateData['business_id']
                && OrderNTuple::DIDI_PUTONG_CAR_LEVEL == $sOneEstimateData['require_level']
                && !Horae::isCarpool($aQuotationData[$sEstimateId]['combo_type'], $aQuotationData[$sEstimateId]['require_level'])
                && in_array($sOneEstimateData['payment_id'], [Order::NOT_BUSINESS_USER, Order::BUSINESS_PAY_BY_PERSON_NEW, -1, Order::BUSINESS_PAY_BY_PERSON_WITH_REFOUND])
            ) {
                $aActivityInfo = (new BizCarTicket())->queryRecActivity(
                    $this->_aParam['token'],
                    $this->_aParam['dest_lat'],
                    $this->_aParam['dest_lng'],
                    $sOneEstimateData['business_id']
                );

                if (!empty($aActivityInfo)) {
                    $sOneEstimateData['promote_sales_rule']['rule_type'][] = TipCommon::RULE_TYPE_CAR_TICKET_FLASH;
                    $aPriorityParam[$sEstimateId]['promote_sales_rule']    = 1;
                }
            }

            // 决策付费会员、智能套餐的优先级
            $iDecideStage = $this->_decideMemberAndComboRecommend((int)$iProductCategory, $aQuotationData[$sEstimateId]['product_id']);
            // 获取智能套餐的优先级的数据
            if (!empty($this->_aProductComboData[$sEstimateId])
                && (empty($this->_aPaidMemberData) || self::RECOMMEND_DECIDE_TYPE_COMBO == $iDecideStage || self::RECOMMEND_DECIDE_TYPE_DEFAULT == $iDecideStage)
            ) {
                $aPriorityParam[$sEstimateId]['recipe_rule'] = 1;
                $aPriorityParam[$sEstimateId]['sub_weight']['recipe_priority'] = $this->_aProductComboData[$sEstimateId]['weight'];
                $sOneEstimateData['recipe_rule'] = $this->_aProductComboData[$sEstimateId];
            };

            // 获取橙意保等必补的数据
            if (OrderNTuple::COMMON_PRODUCT_ID_DEFAULT == $sOneEstimateData['business_id'] && !empty($this->_aCompensationWaitData)) {
                $sOneEstimateData['compensation_driver_arrived_late_rule']['rule_type'][] = 1;
                $aPriorityParam[$sEstimateId]['compensation_driver_arrived_late_rule']    = 1;
            }

            // 拼车省钱卡
            if (!empty(CarpoolCommuteCardLogic::getInstance()->getCardInfoByEid($sEstimateId))) {
                $aPriorityParam[$sEstimateId]['carpool_commute_card'] = 1;
                $aPriorityParam[$sEstimateId]['sub_weight']['commute_card_priority'] = 1; // 子优先级权重,
            }

            // 单单省
            if ($this->_aDanDanSheng[$sEstimateId]) {
                $aPriorityParam[$sEstimateId]['per_order_sale'] = 1;
            }

            //优惠感知，小于八折才命中该业务
            if ($this->_aRecommendProduct[$sEstimateId]) {
                $aPriorityParam[$sEstimateId]['preferential_rule'] = 1;
            }

            // 价差补偿
            if (!empty($this->_aPriceDiffCompensate) && $this->_aPriceDiffCompensate[$sEstimateId]) {
                $aPriorityParam[$sEstimateId]['price_diff_compensate'] = 1;
            }

            // 价准保权益
            if (!empty($this->_aPriceStandardPrivileges) && $this->_aPriceStandardPrivileges[$sEstimateId]) {
                $aPriorityParam[$sEstimateId]['price_standard_privileges'] = 1;
            }

            // 特区配置化沟通组件
            if (!empty($this->_aCustomizedEvent)) {
                $aPriorityParam[$sEstimateId]['customized_event'] = 1;
            }

            // 拼车配置化沟通组件
            if (!empty($this->_aCarpoolCustomizedEvent)) {
                $aPriorityParam[$sEstimateId]['carpool_custom_style'] = 1;
            }

            if (is_array($aQuotationData[$sEstimateId])) {
                // 派单倾斜感知
                if (DispatchOrderLeanLogic::getInstance()->isHit($aQuotationData[$sEstimateId])) {
                    $aPriorityParam[$sEstimateId]['dispatch_order_lean']    = 1;
                    $sOneEstimateData['dispatch_order_lean']['rule_type'][] = DispatchOrderLeanLogic::RULE_TYPE_FLASH;
                }

                // 会员确定性感知
                $iProductCategory = $aQuotationData[$sEstimateId]['product_category'] ?? 0;
                if (MemberBenefitInfoLogic::getInstance()->isHitMemberCertainty($sEstimateId, $iProductCategory ,$this->_aMemberCertainty['benefit'], $this->_aQueueRet)) {
                    $aPriorityParam[$sEstimateId]['member_certainty_rule']    = 1;
                    $sOneEstimateData['member_certainty_rule']['rule_type'][] = MemberBenefitInfoLogic::CERTAINTY_RULE;
                }
            }

            // 会员权益
            if (!empty($this->_aMemberBenefitInfo)) {
                if (!empty($this->_aMemberBenefitInfo[$sEstimateId]) && $this->_aMemberBenefitInfo[$sEstimateId] > 0) {
                    $aPriorityParam[$sEstimateId]['member_benefit_rule']    = 1;
                    $sOneEstimateData['member_benefit_rule']['rule_type'][] = $this->_aMemberBenefitInfo[$sEstimateId]['rule_type'];
                }
            }

            //优惠任务提示
            if (!empty($this->_aTaskReminderInfo) && !empty($this->_aTaskReminderInfo[$sEstimateId])) {
                $aOneTaskReminder = $this->_aTaskReminderInfo[$sEstimateId];
                if (isset($aOneTaskReminder['task_status'])) {
                    $aPriorityParam[$sEstimateId]['task_reminder_rule'] = 1;
                    $aPriorityParam[$sEstimateId]['sub_weight']['product_category_rule'] = $aOneTaskReminder['priority'];
                }
            }

            //是否有909未付订单
            if (!empty($this->_aUnpaidOrderInfo)) {
                $aPriorityParam[$sEstimateId]['unpaid_rule'] = 1;
            }

            // 只展示给非豪华车会员人群
            // 非豪华车付费月卡
            if (Constants\OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR == $aQuotationData[$sEstimateId]['product_id']
                && ($this->_bLuxCarNoVipTag || $this->_aLuxuryMemberShip['is_first_open'])
            ) {
                $aLuxApolloRes = Apollo::getInstance()->featureToggle(
                    'gs_lux_car_paid_card',
                    [
                        'key'           => $this->_aParam['pid'],
                        'pid'           => $this->_aParam['pid'],
                        'phone'         => $this->_aParam['phone'],
                        'access_key_id' => $this->_aParam['access_key_id'],
                        'lang'          => $this->_aParam['lang'],
                        'city'          => $this->_aParam['area'],
                        'app_version'   => $this->_aParam['app_version'],
                    ]
                );

                if ($aLuxApolloRes->allow()) {
                    $aPriorityParam[$sEstimateId]['lux_car_paid_card_rule']    = 1;
                    $sOneEstimateData['lux_car_paid_card_rule']['rule_type'][] = (int)$iProductCategory;
                }
            }

            //绿色环保沟通组件
            if (ProductCategory::PRODUCT_CATEGORY_CARPOOL_STATION == $aQuotationData[$sEstimateId]['product_category']
                || ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTER_NEW == $aQuotationData[$sEstimateId]['product_category']
            ) {
                $aGreenApolloRes = Apollo::getInstance()->featureToggle(
                    'gs_green_env_protection',
                    [
                        'key'           => $this->_aParam['pid'],
                        'pid'           => $this->_aParam['pid'],
                        'phone'         => $this->_aParam['phone'],
                        'access_key_id' => $this->_aParam['access_key_id'],
                        'lang'          => $this->_aParam['lang'],
                        'city'          => $this->_aParam['area'],
                        'app_version'   => $this->_aParam['app_version'],
                    ]
                );
                if ($aGreenApolloRes->allow()) {
                    $aPriorityParam[$sEstimateId]['green_env_protection'] = 1;
                }
            }

            // 通勤权益
            if (!empty($this->_aMemberCommuteUser) && ProductCategory::PRODUCT_CATEGORY_FAST == $aQuotationData[$sEstimateId]['product_category']) {
                $aPriorityParam[$sEstimateId]['commute_user_event_rule']    = 1;
                $sOneEstimateData['commute_user_event_rule']['rule_type'][] = (int)$iProductCategory;
            }

            // 豪华车身份感知
            if (Language::ZH_CN == $this->_aParam['lang'] && !empty($this->_aLuxuryMemberShip) && Constants\OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR == $aQuotationData[$sEstimateId]['product_id'] && $this->_isShowLuxuryPerception($aQuotationData[$sEstimateId])) {
                $aPriorityParam[$sEstimateId]['luxury_car_perception_event']    = 1;
                $sOneEstimateData['luxury_car_perception_event']['rule_type'][] = (int)$iProductCategory;
            }

            // 豪华车续费引导
            if (Language::ZH_CN == $this->_aParam['lang'] && !empty($this->_aLuxuryMemberShip) && Constants\OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR == $aQuotationData[$sEstimateId]['product_id'] && $this->_isShowLuxuryRenewalGuidance()) {
                    $aPriorityParam[$sEstimateId]['luxury_car_renewal_guidance_event']    = 1;
                    $sOneEstimateData['luxury_car_renewal_guidance_event']['rule_type'][] = (int)$iProductCategory;
            }

            //内循环账户折扣
            if (!empty(self::getDiscountAmountByDiscountDesc($aQuotationData[$sEstimateId]))) {
                $aRevolvingAccountDiscountConfig = NuwaConfig::text('side_estimate', self::REVOLVING_ACCOUNT_DISCOUNT_CONFIG);
                $aRevolvingAccountDiscount       = self::getDiscountAmountByDiscountDesc($aQuotationData[$sEstimateId]);
                //1、支持折扣金额>x的时候触发该沟通组件，x可配置
                $iShowAmountLowerLimit = empty($aRevolvingAccountDiscountConfig['showAmountLowerLimit']) ? 0 : $aRevolvingAccountDiscountConfig['showAmountLowerLimit'];
                if (!empty($aRevolvingAccountDiscountConfig) && ($aRevolvingAccountDiscount > $iShowAmountLowerLimit)) {
                    //2、控制每个乘客最多展示3次
                    $sPid        = $this->_aParam['pid'];
                    $sKey        = UtilCommon::getRedisPrefix(P_REVOLVING_ACCOUNT_DISCOUNT_COMMUNICATION) . $sPid;
                    $iLimitTimes = empty($aRevolvingAccountDiscountConfig['limitTimes']) ? 3 : $aRevolvingAccountDiscountConfig['limitTimes'];
                    if (RedisCommon::canIncrease($sKey, $iLimitTimes)) {
                        $aPriorityParam[$sEstimateId][self::REVOLVING_ACCOUNT_DISCOUNT_RULE] = 1;
                    }
                }
            }

            // 内循环账户N倍返返利
            if (self::isNFoldActivity($aQuotationData, $sEstimateId)) {
                $aRevolvingAccountRebateConfig = NuwaConfig::text('side_estimate', self::REVOLVING_ACCOUNT_REBATE_N_FOLD);
                //1、控制每个乘客最多展示3次
                $sPid        = $this->_aParam['pid'];
                $sKey        = UtilCommon::getRedisPrefix(P_REVOLVING_ACCOUNT_REBATE_N_FOLD_COMMUNICATION) . $sPid;
                $iLimitTimes = empty($aRevolvingAccountRebateConfig['limitTimes']) ? 3 : $aRevolvingAccountRebateConfig['limitTimes'];
                if (RedisCommon::canIncrease($sKey, $iLimitTimes)) {
                    $aPriorityParam[$sEstimateId][self::REVOLVING_ACCOUNT_REBATE_N_FOLD] = 1;
                }
            }

            //内循环账户返利
            if (!empty(self::getRebateAmountByQuotaion($aQuotationData[$sEstimateId])) && self::getRebateAmountByQuotaion($aQuotationData[$sEstimateId]) > 0 && self::POPE_ACTIVITY_N_FOLD != $this->_iActivityType) {
                $aRevolvingAccountRebateConfig = NuwaConfig::text('side_estimate', self::REVOLVING_ACCOUNT_REBATE_CONFIG);
                //1、控制每个乘客最多展示3次
                $sPid        = $this->_aParam['pid'];
                $sKey        = UtilCommon::getRedisPrefix(P_REVOLVING_ACCOUNT_REBATE_COMMUNICATION) . $sPid;
                $iLimitTimes = empty($aRevolvingAccountRebateConfig['limitTimes']) ? 3 : $aRevolvingAccountRebateConfig['limitTimes'];
                if (RedisCommon::canIncrease($sKey, $iLimitTimes)) {
                    $aPriorityParam[$sEstimateId][self::REVOLVING_ACCOUNT_REBATE_RULE] = 1;
                }
            }
        }

        $aPriorityParam = $this->_sortPriority($aConfig, $aPriorityParam, $aProductList);
        return [$aPriorityParam, $aProductList];
    }

    /**
     * 通过报价单的discount_desc获取折扣金额
     * @param
     * @return mixed
     */
    private function getDiscountAmountByDiscountDesc($_aOneQuotation) {
        $_sRevolvingAccountDiscountAmount = null;
        $aDiscountList = json_decode($_aOneQuotation['discount_desc'], true);
        $aDiscountList = is_array($aDiscountList) ? $aDiscountList : [];
        foreach ($aDiscountList as $aDiscountItem) {
            if (self::REVOLVING_ACCOUNT_DISCOUNT == $aDiscountItem['type']) {
                $_sRevolvingAccountDiscountAmount = $aDiscountItem['amount'];
            }
        }

        return $_sRevolvingAccountDiscountAmount;
    }
    /**
     * 通过报价单的discount_desc获取返利金额
     * @param
     * @return mixed
     */
    private function getRebateAmountByQuotaion($_aOneQuotation) {
        return $_aOneQuotation['revolving_account_rebate_amount'];
    }

    /**
     * @desc 大额优惠感知实验
     * @return bool
     */
    public function showPreferential() {
        $oApollo        = new NuwaApollo();
        $oFeatureToggle = $oApollo->featureToggle(
            'yugu_bigsaving',
            array(
                Apollo::APOLLO_INDIVIDUAL_ID => $this->_aParam['pid'],
                'key'                        => $this->_aParam['pid'],
                'phone'                      => $this->_aParam['phone'],
                'lang'                       => $this->_aParam['lang'],
                'city'                       => $this->_aParam['area'],
                'open_time'                  => date('H:i'),
                'week'                       => date('w'),
            )
        );
        if ($oFeatureToggle->allow() && 'treatment_group' == $oFeatureToggle->getGroupName()) {
            return true;
        }

        return false;
    }

    /**
     * 决策会员和智能套餐的推荐逻辑 ，目前只放开快车、优享、专车
     * @param int $iProductCategory productCategory
     * @param int $iProductId       productId
     * @return int
     */
    private function _decideMemberAndComboRecommend(int $iProductCategory, $iProductId) {
        $iDecideStageType = self::RECOMMEND_DECIDE_TYPE_DEFAULT;

        $oToggle = (new NuwaApollo())->featureToggle(
            'gs_bubble_communicate_member',
            [
                'key'              => $this->_aParam['phone'],
                'phone'            => $this->_aParam['phone'],
                'pid'              => $this->_aParam['pid'],
                'access_key_id'    => $this->_aParam['access_key_id'],
                'lang'             => $this->_aParam['lang'],
                'city'             => $this->_aParam['area'],
                'product'          => $iProductId,
                'product_category' => $iProductCategory,
            ]
        );
        if (!$oToggle->allow()) {
            return $iDecideStageType;
        }

        $iRatio           = (int)$oToggle->getParameter('ratio', 0);
        $iDecideStageType = rand(1, 100) <= $iRatio ? self::RECOMMEND_DECIDE_TYPE_MEMBER : self::RECOMMEND_DECIDE_TYPE_COMBO;
        return $iDecideStageType;
    }

    /**
     * 拦截过滤掉香港出租车冒泡沟通组件,true表示拦截不继续向下执行
     * @return bool
     */
    private function _hkTaxiIntercept() {
        if (empty($this->_aProductList)) {
            return true;
        }

        $bHitHKTaxi = false;
        foreach ($this->_aProductList as $aOneProduct) {
            if (Product::COMMON_PRODUCT_ID_HK_TAXI == $aOneProduct['business_id']) {
                $bHitHKTaxi = true;
                break;
            }
        }

        /*
        if ($bHitHKTaxi) {
            $oApollo       = new NuwaApollo();
            $oApolloResult = $oApollo->featureToggle(
                'hk_communicate_switch',
                [
                    'phone'         => $this->_aParam['phone'],
                    'app_version'   => $this->_aParam['app_version'],
                    'access_key_id' => $this->_aParam['access_key_id'],
                    'lang'          => $this->_aParam['lang'],
                    'city'          => $this->_aParam['area'],
                ]
            );
            if (!$oApolloResult->allow()) {
                // 拦截
                return true;
            }
        }
        */

        return false;
    }

    /**
     * 渲染CommonData
     * @return array
     */
    private function _formatCommonData() {
        $aCommonData = [];
        //获取价格沟通组件的公共配置
        $aCommonData['special_rule'] = $this->_buildCommonSpecialRule();
        //获取B端车票的公共配置
        $aCommonData['promote_sales_rule'] = $this->_getCommonPromoteSalesTipRule();
        //获取滴滴支付的公共配置
        $aCommonData['didi_pay_rule'] = DiDiPayCommon::getDiDiPayCommonData();

        // 获取橙意保等久必补的公共配置
        if (!empty($this->_aCompensationWaitData)) {
            $aCommonData['compensation_driver_arrived_late_rule'] = $this->_getCompensationWaitCommonData();
        }

        // 新表单 获取企业支付 公共配置
        if ($this->_iBusinessPayMaxAmount > 0) {
            $aCommonData['business_pay_rule'] = $this->_getBusinessPayCommonData();
        }

        // 疫情绕行 公共配置
        if (!empty($this->_bNeedEpidemicPrevent)) {
            $aCommonData['epidemic_prevent_rule'] = $this->_getEpidemicPreventCommonData();
        }

        // 派单倾斜
        if (DispatchOrderLeanLogic::getInstance()->isHitCommonData()) {
            $aCommonData['dispatch_order_lean'] = $this->_getDispatchLeanCommonData();
        }

        //大额优惠感知
        if (!empty($this->_aPreferentialProducts)) {
            $aCommonData['preferential_rule'] = $this->_getPreferentialData();
        }

        // 价差补偿
        if (!empty($this->_aPriceDiffCompensate)) {
            $aCommonData['price_diff_compensate'] = $this->_getPriceDiffCommonData();
        }

        // 价准保
        if (!empty($this->_aPriceStandardPrivileges)) {
            $aCommonData['price_standard_privileges'] = $this->_getPriceStandardCommonData();
        }

        // 特区配置化沟通组件
        if (!empty($this->_aCustomizedEvent)) {
            $aCommonData['customized_event'] = $this->_getCustomizedCommonData();
        }

        // 拼车配置化沟通组件
        if (!empty($this->_aCarpoolCustomizedEvent)) {
            $aCommonData['carpool_custom_style'] = $this->_getCarpoolCustomizedCommonData();
        }

        // 单单省
        if (in_array(true, $this->_aDanDanSheng)) {
            $aCommonData['per_order_sale'] = $this->_getDanDanShengCommonData();
        }

        //未支付订单
        if (!empty($this->_aUnpaidOrderInfo)) {
            $aCommonData['unpaid_rule'] = $this->_getUnpaidData();
        }

        //获取优惠中心感知
        if (!empty($this->_aDefaultDiscountInfo) && !empty($this->_aGetDiscountInfoData)) {
            $aCommonData['period_discount_rule'] = (new CommunicateInfoLogic())->getPeriodDiscountData($this->_aDefaultDiscountInfo);
        }

        //会员权益
        if (!empty($this->_aMemberBenefitInfo)) {
            $aCommonData['member_benefit_rule'] = MemberBenefitInfoLogic::getCommonData();
        }

        // 隐私保护
        $aCommonData['privacy_protection'] = (new CommunicateInfoLogic())->getPrivacyProtectionData();

        // NA默勾
        $aTopInfo = AthenaExtension::getInstance()->getTipInfo();
        if (!empty($aTopInfo)) {
            $aCommonData['athena_tip_info_rule'] = [
                'multi_scene_text'  => '',
                'single_scene_text' => ['1' => sprintf("%s\n%s",$aTopInfo['tip_content'], $aTopInfo['et_content']),],
                'link_url'          => $aTopInfo['switch_link'],
            ];
        }

        // 专车 (勾选多品类时返回活动固定文案)
        if ($this->_bIsPremiumPaidMember) {
            // 付费会员  -> 券沟通组件
            $aConf = $this->_aCommunicateInfoText['premium_paid_coupon_rule'] ?? [];

            $aCommonData['premium_paid_coupon_rule'] = [
                'multi_scene_text'  => $aConf['multi_scene_text'],
                'single_scene_text' => ['1' => $aConf['multi_scene_text']],
            ];
        } else {
            // 非付费会员  -> 活动组件
            $aConf = $this->_aCommunicateInfoText['premium_paid_activity_rule'] ?? [];
            $sText = $aConf['text'];
            if ($this->_bIsPremiumPaidNewMember) {
                $sText = Language::replaceTag(
                    $aConf['new_member_text'],
                    ['amount' => $this->_iPremiumPaidNewMemberCouponAmout]
                );
            }

            $aCommonData['premium_paid_activity_rule'] = [
                'multi_scene_text'  => $sText,
                'single_scene_text' => ['1' => $sText],
                'link_url'          => $aConf['link_url'],
            ];
        }

        //内循环折扣
        $aCommonData['revolving_account_discount_rule'] = $this->_getMaxRevolvingAccountDiscount();
        //内循环返利
        $aCommonData['revolving_account_rebate_rule'] = $this->_getRevolvingAccountRebate();
        // 内循环n倍返
        $aCommonData['revolving_account_rebate_n_fold'] = $this->_getRevolvingAccountRebateNFold();

        return $aCommonData;
    }

    //获取最大的内循环折扣金额
    private function _getMaxRevolvingAccountDiscount() {
        $sMaxRevolvingAccountDiscount = 0;
        foreach ($this->_aQuotationInfo as $val) {
            $sRevolvingAccountDiscount = self::getDiscountAmountByDiscountDesc($val);
            if (!empty($sRevolvingAccountDiscount) && $sRevolvingAccountDiscount > $sMaxRevolvingAccountDiscount) {
                $sMaxRevolvingAccountDiscount = $sRevolvingAccountDiscount;
            }
        }

        $aRevolvingAccountDiscountConfig = NuwaConfig::text('side_estimate', self::REVOLVING_ACCOUNT_DISCOUNT_CONFIG);
        if (0 == $sMaxRevolvingAccountDiscount || empty($aRevolvingAccountDiscountConfig)) {
            return [];
        }

        return [
            'multi_scene_text' => Language::replaceTag($aRevolvingAccountDiscountConfig['text'], ['amount' => $sMaxRevolvingAccountDiscount]),
        ];
    }

    //获取内循环返利
    private  function _getRevolvingAccountRebate() {
        $sRevolvingAccountRebate = 0;
        foreach ($this->_aQuotationInfo as $val) {
            $sRes = self::getRebateAmountByQuotaion($val);
            if (!empty($sRes)) {
                $sRevolvingAccountRebate = $sRes;
            }
        }

        $aRevolvingAccountRebateConfig = NuwaConfig::text('side_estimate', self::REVOLVING_ACCOUNT_REBATE_CONFIG);
        if (0 == $sRevolvingAccountRebate || empty($aRevolvingAccountRebateConfig)) {
            return [];
        }

        return [
            'multi_scene_text' => Language::replaceTag($aRevolvingAccountRebateConfig['text'], ['amount' => $sRevolvingAccountRebate]),
        ];
    }

    /**
     * 用户是否可以看到一口价
     * @param int $ruleType 规则类型
     * @return bool
     */
    private function _isUserCanSeeOnelimitPriceByRuleType($ruleType) {
        $onePriceRuleTypes = [
            Common::RULE_TYPE_FIXED_PRICE_TAXI,
            Common::RULE_TYPE_FAST_PRICE,
            Common::RULE_TYPE_CAP_PRICE,
            Common::RULE_TYPE_INTER_CITY_CARPOOL_NEW_MODE,
        ];
        if (!in_array(
            $ruleType,
            $onePriceRuleTypes
        )
        ) {
            return true;
        }

        $oToggle = (new NuwaApollo())->featureToggle(
            'feature_oe_limit_one_price',
            [
                'key'           => $this->_aParam['phone'],
                'phone'         => $this->_aParam['phone'],
                'pid'           => $this->_aParam['pid'],
                'access_key_id' => $this->_aParam['access_key_id'],
                'lang'          => $this->_aParam['lang'],
                'city'          => $this->_aParam['area'],
            ]
        );
        $day     = (int)$oToggle->getParameter('day', 0);
        $num     = (int)$oToggle->getParameter('num', 0);
        if (!$oToggle->allow()) {
            return true;
        }

        $sFeatureKey = 'fixed_price_sw_cnt';
        $accessMap   = [
            Constants\Common::DIDI_IOS_PASSENGER_APP     => 'na',
            Constants\Common::DIDI_ANDROID_PASSENGER_APP => 'na',
            Constants\Common::DIDI_ALIPAY_MINI_PROGRAM   => 'alipay',
            Constants\Common::DIDI_WECHAT_MINI_PROGRAM   => 'wechat',
        ];
        $aCondition  = ['passenger_id' => $this->_aParam['pid'], 'term_type' => $accessMap[$this->_aParam['access_key_id']]];
        $afeature    = UfsRepository::getUfsFeature([$sFeatureKey], $aCondition, 'passenger.action');
        $asetting    = [3 => 'fixed_price_sw_3d_cnt', 5 => 'fixed_price_sw_5d_cnt', 7 => 'fixed_price_sw_6d_cnt', 15 => 'fixed_price_sw_15d_cnt', 30 => 'fixed_price_sw_30d_cnt'];

        $afeature = json_decode($afeature['fixed_price_sw_cnt'], true);
        if ($afeature[$asetting[$day]] <= $num) {
            return true;
        }

        return false;
    }


    /**
     * 渲染品类沟通数据
     * @param array $aResourceData        数据源
     * @param array $aPriorityParam       优先级数据
     * @param array $aCommonData          CommonData
     * @param mixed $aCommunicateInfoText 沟通文案
     * @return array
     */
    private function _formatRuleData(array $aResourceData, array $aPriorityParam, &$aCommonData, $aCommunicateInfoText) {
        $aRuleData = [];
        //组装各预估的参数
        foreach ($aResourceData as $sEstimateId => $aOneResourceParam) {
            if (empty($aPriorityParam[$sEstimateId]) || empty($aPriorityParam[$sEstimateId]['type'])) {
                continue;
            }

            $aQuotationData = $this->_aQuotationInfo[$sEstimateId];
            $iPcId          = $aQuotationData['product_category'];

            $aRuleData[$sEstimateId] = [
                'estimate_id' => $sEstimateId,
                'type'        => $aPriorityParam[$sEstimateId]['type'],
                'style'       => $aPriorityParam[$sEstimateId]['style'],
                'action_type' => $aPriorityParam[$sEstimateId]['action_type'],
                'weight'      => $aPriorityParam[$sEstimateId]['sum_weight'],
            ];

            // 远途特快券
            if (CommunicateRepo::INTER_CITY_CARPOOL_NEW == $aPriorityParam[$sEstimateId]['type']) {
                $aRuleData[$sEstimateId]['content'] = [
                    'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'rule_type'            => $aOneResourceParam['inter_city_carpool_rule']['rule_type'] ?? [],
                    'button'               => $this->_aInterCityCarpoolInfo['button'],
                    'button_style'         => $this->_aInterCityCarpoolInfo['button_style'],
                    'text_color'           => $this->_aInterCityCarpoolInfo['text_color'],
                    'text'                 => $this->_aInterCityCarpoolInfo['text'],
                    'link_url'             => $this->_aInterCityCarpoolInfo['link_url'],
                    'icon'                 => $this->_aInterCityCarpoolInfo['icon'],
                    'background_gradients' => $this->_aInterCityCarpoolInfo['background_gradients'],
                ];
            }

            // 123拼车优惠券
            if (CommunicateRepo::CARPOOL_COUPON_123 == $aPriorityParam[$sEstimateId]['type']) {
                $aRuleData[$sEstimateId]['content'] = [
                    'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'rule_type'            => $aOneResourceParam['carpool_rule']['rule_type'] ?? [],
                    'button'               => $this->_aCarpoolActivityInfo['button'],
                    'button_style'         => $this->_aCarpoolActivityInfo['button_style'],
                    'text_color'           => $this->_aCarpoolActivityInfo['text_color'],
                    'text'                 => $this->_aCarpoolActivityInfo['text'],
                    'link_url'             => $this->_aCarpoolActivityInfo['link_url'],
                    'icon'                 => $this->_aCarpoolActivityInfo['icon'],
                    'background_gradients' => $this->_aCarpoolActivityInfo['background_gradients'],
                ];
            }

            // 车大联盟流量需求
            if (CommunicateRepo::SPACIOUS_CAR == $aPriorityParam[$sEstimateId]['type']) {
                $aSpaciousCarConf = $aCommunicateInfoText['spacious_car_rule'] ?? [];
                $aRuleData[$sEstimateId]['content'] = [
                    'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'rule_type'            => $aOneResourceParam['spacious_car_rule']['rule_type'] ?? [],
                    'text'                 => Language::replaceTag(
                        $aSpaciousCarConf['text'],
                        ['amount' => $this->_aSpaciousCarInfo['fSelectionFee']]
                    ),
                    'icon'                 => $aSpaciousCarConf['icon'],
                    'text_color'           => $aSpaciousCarConf['text_color'],
                    'background_gradients' => $aSpaciousCarConf['background_gradients'],
                ];
            }

            if ($this->iRuleType[$iPcId] == $aPriorityParam[$sEstimateId]['type']
                && !empty($this->_aApolloPcId2EventKey[$iPcId])
            ) {
                $aApolloParams = [
                    'key'           => $this->_aParam['uid'],
//                    'event'            => $sKey,
                    'pid'           => $this->_aParam['pid'],
                    'phone'         => $this->_aParam['phone'],
                    'lang'          => $this->_aParam['lang'],
                    'city'          => $this->_aParam['area'],
//                    'product_category' => $iProductCategory,
                    'access_key_id' => $this->_aParam['access_key_id'],
                    'app_version'   => $this->_aParam['app_version'],
                ];
                $sEventKey     = $this->_aApolloPcId2EventKey[$iPcId];
                if (!empty($this->sDcmpKeyApollo[$iPcId])) {
                    $aApollo2 = Apollo::getInstance()->featureToggle($this->sDcmpKeyApollo[$iPcId], $aApolloParams);
                    if ($aApollo2->allow()) {
                        $sEventKey = $aApollo2->getParameter('dcmp_key', $this->_aApolloPcId2EventKey[$iPcId]);
                    }
                }

                $aConfig = NuwaConfig::text('side_estimate', $sEventKey);
                switch ($this->iCommunicateStyle[$iPcId]) {
                    case 2:
                        $aRuleData[$sEstimateId]['content'] = [
                            'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                            'rule_type'            => $aOneResourceParam[$this->_aApolloPcId2EventKey[$iPcId]]['rule_type'] ?? [],
                            'text'                 => $aConfig['text'],
                            'icon'                 => $aConfig['bg_img'],
                            'text_color'           => $aConfig['text_color'],
                            'background_gradients' => $aConfig['bg_gradients'],
                        ];
                        break;
                    default:
                        $aRuleData[$sEstimateId]['content'] = [
                            'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                            'rule_type'            => $aOneResourceParam[$this->_aApolloPcId2EventKey[$iPcId]]['rule_type'] ?? [],
                            'text'                 => $aConfig['text'],
                            'icon'                 => $aConfig['bg_img'],
                            'text_color'           => $aConfig['text_color'],
                            'background_gradients' => $aConfig['bg_gradients'],
                            'link_url'             => $aConfig['link_url'],
                            'button'               => $aConfig['button_text'],
                            'button_style'         => $aConfig['button_style'],
                        ];
                }

                $aCommonData[$this->_aApolloPcId2EventKey[$iPcId]] = [
                    'multi_scene_text'  => $aConfig['text'],
                    'single_scene_text' => ['1' => $aConfig['text']],
                    'link_url'          => $aConfig['link_url'],
                ];
            }

            // 车大权益升级
            if (CommunicateRepo::SPACIOUS_CAR_UPGRADE == $aPriorityParam[$sEstimateId]['type']) {
                $aSpaciousCarConf = $aCommunicateInfoText['spacious_car_upgrade_event'] ?? [];
                $aRuleData[$sEstimateId]['content'] = [
                    'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'rule_type'            => $aOneResourceParam['spacious_car_upgrade_event']['rule_type'] ?? [],
                    'text'                 => $aSpaciousCarConf['text'],
                    'icon'                 => $aSpaciousCarConf['icon'],
                    'text_color'           => $aSpaciousCarConf['text_color'],
                    'background_gradients' => $aSpaciousCarConf['background_gradients'],
                ];
            }

            // NA端默勾
            if (CommunicateRepo::ATHENA_TIP_INFO == $aPriorityParam[$sEstimateId]['type']) {
                $aTopInfo  = AthenaExtension::getInstance()->getCommunicateInfo($aQuotationData['product_category']);
                $aDcmpConf = $aCommunicateInfoText['athena_tip_info_rule'] ?? [];
                $aRuleData[$sEstimateId]['content'] = [
                    'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'rule_type'            => $aOneResourceParam['athena_tip_info_rule']['rule_type'] ?? [],
                    'button'               => $aTopInfo['switch_name'],
                    'text'                 => sprintf("%s\n%s",$aTopInfo['tip_content'], $aTopInfo['et_content']),
                    'link_url'             => $aTopInfo['switch_link'],
                    'text_color'           => $aDcmpConf['text_color'],
                    'button_style'         => $aDcmpConf['button_style'],
                    'icon'                 => $aDcmpConf['icon'],
                    'background_gradients' => $aDcmpConf['background_gradients'],
                ];
            }

            // 专车非付费会员 - 活动信息
            if (CommunicateRepo::PREMIUM_PAID_MEMBER_ACTIVITY == $aPriorityParam[$sEstimateId]['type']) {
                $aConf = $aCommunicateInfoText[$this->_sMemberType] ?? [];
                $aRuleData[$sEstimateId]['content'] = [
                    'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'rule_type'            => $aOneResourceParam['premium_paid_activity_rule']['rule_type'] ?? [],
                    'text'                 => $aConf['text'],
                    'icon'                 => $aConf['icon'],
                    'text_color'           => $aConf['text_color'],
                    'background_gradients' => $aConf['background_gradients'],
                    'link_url'             => $aConf['link_url'],
                    'button'               => $aConf['button'],
                    'button_style'         => $aConf['button_style'],
                ];

                if (self::OPEN_MEMBER == $this->_sMemberType && $this->_bIsPremiumPaidNewMember) {
                    $sText = Language::replaceTag(
                        $aConf['new_member_text'],
                        ['amount' => $this->_iPremiumPaidNewMemberCouponAmout]
                    );
                    $aRuleData[$sEstimateId]['content']['text'] = $sText;
                }

                if (self::MEMBER_RENEWAL == $this->_sMemberType) {
                    $aRuleData[$sEstimateId]['content']['text'] = Language::replaceTag(
                        $aConf['text'],
                        ['day' => $this->_iExpireDay + 1]
                    );
                }

                $aRuleData[$sEstimateId]['track'] = [
                    'activity_type' => $this->_sMemberType,
                    'is_new_member' => $this->_bIsPremiumPaidNewMember,
                ];
            }

            // 专车付费会员 - 券展示
            if (CommunicateRepo::PREMIUM_PAID_MEMBER_COUPON == $aPriorityParam[$sEstimateId]['type']) {
                $aCoupon = $this->_getCouponInfo($sEstimateId, self::COUPON_TAG_PREMIUM_PAID_MEMBER);
                if ($aCoupon['extra_info']['is_expand_coupon']) {
                    $aConf = $aCommunicateInfoText['premium_paid_coupon_rule_swell'] ?? [];
                    $aCommonData['premium_paid_coupon_rule'] = [
                        'multi_scene_text'  => $aConf['multi_scene_text'],
                        'single_scene_text' => ['1' => $aConf['multi_scene_text']],
                    ];
                } else {
                    $aConf = $aCommunicateInfoText['premium_paid_coupon_rule'] ?? [];
                }

                $aRuleData[$sEstimateId]['content'] = [
                    'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'rule_type'            => $aOneResourceParam['premium_paid_coupon_rule']['rule_type'] ?? [],
                    'text'                 => Language::replaceTag(
                        $aConf['text'],
                        ['amount' => $aCoupon['amount']]
                    ),
                    'icon'                 => $aConf['icon'],
                    'text_color'           => $aConf['text_color'],
                    'background_gradients' => $aConf['background_gradients'],
                ];

                $aRuleData[$sEstimateId]['track'] = [
                    'strategy' => $aCoupon['extra_info']['is_expand_coupon'] ? 1 : 0,
                ];
            }

            // 单单省
            if (CommunicateRepo::PER_ORDER_SALE == $aPriorityParam[$sEstimateId]['type']) {
                $aPerOrderSaleConf = $aCommunicateInfoText['per_order_sale'] ?? [];
                $aRuleData[$sEstimateId]['content'] = [
                    'event'        => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'button'       => $aPerOrderSaleConf['button'] ?? '',
                    'button_style' => $aPerOrderSaleConf['button_style'],
                    'text'         => $aPerOrderSaleConf['text'] ?? '',
                    'icon'         => $aPerOrderSaleConf['icon'] ?? '',
                    'link_url'     => $aPerOrderSaleConf['link_url'] ?? '',
                    'bg_color'     => $aPerOrderSaleConf['bg_color'] ?? '',
                ];
            }

            // 价差补偿
            if (CommunicateRepo::PRICE_DIFF_COMPENSATE == $aPriorityParam[$sEstimateId]['type']) {
                $aPriceDiffCompConf = $aCommunicateInfoText['price_diff_compensate'] ?? [];
                $aRuleData[$sEstimateId]['content'] = [
                    'event'      => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'text'       => $aPriceDiffCompConf['text'] ?? '',
                    'text_color' => $aPriceDiffCompConf['text_color'] ?? '',
                    'bg_color'   => $aPriceDiffCompConf['bg_color'] ?? '',
                ];
                if (Constants\Common::DIDI_IOS_PASSENGER_APP == $this->_aParam['access_key_id']
                    || Constants\Common::DIDI_ANDROID_PASSENGER_APP == $this->_aParam['access_key_id']
                ) {
                    $aRuleData[$sEstimateId]['content']['link_url'] = $aPriceDiffCompConf['link_url_app'] ?? '';
                } elseif (Constants\Common::DIDI_WECHAT_MINI_PROGRAM == $this->_aParam['access_key_id']
                    || Constants\Common::DIDI_ALIPAY_MINI_PROGRAM == $this->_aParam['access_key_id']
                ) {
                    $aRuleData[$sEstimateId]['content']['link_url'] = $aPriceDiffCompConf['link_url_mini'] ?? '';
                }
            }

            // 超级会员价准权益
            if (CommunicateRepo::PAID_MEMBER_PRICE_STANDARD == $aPriorityParam[$sEstimateId]['type']) {
                $aConf = $aCommunicateInfoText['price_standard_privileges'] ?? [];
                $aRuleData[$sEstimateId]['content'] = [
                    'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'rule_type'            => $aOneResourceParam['price_standard_privileges']['rule_type'] ?? [],
                    'text'                 => $aConf['text'] ?? '',
                    'background_gradients' => $aConf['background_gradients'] ?? [],
                    'text_color'           => $aConf['text_color'] ?? '',
                    'icon'                 => $aConf['icon'] ?? '',
                ];
            }

            if (CommunicateRepo::CARPOOL_COMMUTE_CARD == $aPriorityParam[$sEstimateId]['type']) {
                $aCard = CarpoolCommuteCardLogic::getInstance()->getCardInfoByEid($sEstimateId);
                $aRuleData[$sEstimateId]['content'] = [
                    'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'text'                 => $aCard['text'],
                    'icon'                 => $aCard['icon'],
                    'link_url'             => $aCard['link_url'],
                    'text_color'           => $aCard['text_color'],
                    'background_gradients' => $aCard['background_gradients'],
                    'button'               => $aCard['button'],
                    'button_style'         => $aCard['button_style'],
                ];
            }

            // 隐私保护提醒
            if (CommunicateRepo::PRIVACY_PROTECTION == $aPriorityParam[$sEstimateId]['type']) {
                $privacySettings = Language::getDecodedTextFromDcmp('special_price_rule_explain-privacy_protection');
                $aRuleData[$sEstimateId]['content'] = [
                    'event'      => 'privacy_protection',
                    'rule_type'  => [Common::RULE_TYPE_PRIVACY_PROTECTION],
                    'text'       => $privacySettings['text'],
                    'bg_color'   => $privacySettings['bg_color'],
                    'text_color' => $privacySettings['text_color'],
                    'link_url'   => $privacySettings['link_url'],
                ];
            }

            //疫情绕行
            if (CommunicateRepo::EPIDEMIC_PREVENT_ROUND == $aPriorityParam[$sEstimateId]['type']) {
                $aRuleData[$sEstimateId]['content'] = [
                    'event'     => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'rule_type' => $aOneResourceParam['epidemic_prevent_rule']['rule_type'] ?? [],
                    'text'      => $aCommunicateInfoText['epidemic_prevent_rule']['text'],
                ];
            }

            // 企业支付沟通
            if (CommunicateRepo::BUSINESS_PAY == $aPriorityParam[$sEstimateId]['type']) {
                $aText      = $aCommunicateInfoText['business_pay_rule'];
                $sBGColor   = $this->_bCommunicateSplit ? $aText['split_bg_color'] : $aText['bg_color'];
                $sTextColor = $this->_bCommunicateSplit ? $aText['split_text_color'] : $aText['text_color'];
                $aRuleData[$sEstimateId]['content'] = [
                    'event'      => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'rule_type'  => $aOneResourceParam['business_pay_rule']['rule_type'] ?? [],
                    'bg_color'   => $sBGColor,
                    'text_color' => $sTextColor,
                    'text'       => Language::replaceTag($aText['text'], ['amount' => $this->_iBusinessPayMaxAmount]),
                ];
            }

            //滴滴支付
            if (CommunicateRepo::DIDI_PAY_RULE == $aPriorityParam[$sEstimateId]['type']) {
                if (!empty($aQuotationData) && !empty($aQuotationData['product_category'])) {
                    $aText      = $aCommunicateInfoText['didi_pay_rule'];
                    $sBGColor   = $this->_bCommunicateSplit ? $aText['bg_color'] : '';
                    $sTextColor = $this->_bCommunicateSplit ? $aText['text_color'] : '';

                    $aConf        = MainDataRepo::getBasicConfByProductCategory($aQuotationData[$sEstimateId]['product_category']);
                    $sName        = !empty($aConf) ? $aConf['intro_msg'] : '';
                    $aDidiPayText = DiDiPayCommon::getDiDiPayDocument();
                    $sDidiPayText = Language::replaceTag($aDidiPayText[$aOneResourceParam['didi_pay_rule']['rule_type'][0]], ['name' => $sName]);
                    $aRuleData[$sEstimateId]['content'] = [
                        'event'      => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                        'rule_type'  => $aOneResourceParam['didi_pay_rule']['rule_type'] ?? [],
                        'text'       => $sDidiPayText,
                        'bg_color'   => $sBGColor,
                        'text_color' => $sTextColor,
                    ];
                };
            }

            // 洋流提醒
            if (CommunicateRepo::DIVERSIONAL_COUPON == $aPriorityParam[$sEstimateId]['type']) {
                // 计算在主端的抵扣金额：除券外的其他抵扣金额 + 券的面额
                $fAmount = $aQuotationData['dynamic_total_fee'] - $aQuotationData['estimate_fee'] - $aQuotationData['deduction_fee'] + $aQuotationData['diversion_coupon_amount'] / 100;
                $fAmount = NumberHelper::numberFormatDisplay($fAmount, '', 1);
                $aRuleData[$sEstimateId]['content'] = [
                    'event'      => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'rule_type'  => $aOneResourceParam['diversional_coupon_rule']['rule_type'] ?? [],
                    'bg_color'   => $aCommunicateInfoText['diversional_coupon_rule']['bg_color'],
                    'text_color' => $aCommunicateInfoText['diversional_coupon_rule']['text_color'],
                    'text'       => Language::replaceTag($aCommunicateInfoText['diversional_coupon_rule']['text'], ['amount' => $fAmount]),
                ];
            }

            //价格沟通组件
            if (CommunicateRepo::SPECIAL_RULE == $aPriorityParam[$sEstimateId]['type']) {
                $aText      = $aCommunicateInfoText['special_rule'];
                $sBGColor   = $this->_bCommunicateSplit ? $aText['bg_color'] : '';
                $sTextColor = $this->_bCommunicateSplit ? $aText['text_color'] : '';

                if (!$this::_isUserCanSeeOnelimitPriceByRuleType((int)$aOneResourceParam['special_rule']['rule_type'][0])) {
                    unset($aRuleData[$sEstimateId]);
                    continue;
                }

                $aRuleData[$sEstimateId]['content'] = [
                    'event'           => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'rule_type'       => $aOneResourceParam['special_rule']['rule_type'] ?? [],
                    'bg_color'        => $sBGColor,
                    'text_color'      => $sTextColor,
                    'text'            => $aOneResourceParam['special_rule']['text'],
                    'is_force_notice' => 0,
                ];
            }

            //B端车票
            if (CommunicateRepo::PROMOTE_SALES_RULE == $aPriorityParam[$sEstimateId]['type']) {
                if (!empty($this->_aParam['dest_lat']) && !empty($this->_aParam['dest_lng']) && !empty($this->_aParam['to_name']) && !empty($aCommonData['promote_sales_rule']) && !empty($aCommonData['promote_sales_rule']['link_url'])) {
                    $sLinkUrl = $aCommonData['promote_sales_rule']['link_url'] . '&car_type=' . $aOneResourceParam['business_id'];
                }

                $aRuleData[$sEstimateId]['content'] = [
                    'event'      => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'rule_type'  => $aOneResourceParam['promote_sales_rule']['rule_type'] ?? [],
                    'bg_color'   => '',
                    'text_color' => '',
                    'text'       => $aCommonData['promote_sales_rule']['single_scene_text'][$aOneResourceParam['promote_sales_rule']['rule_type'][0]],
                    'link_param' => ['car_type' => $aOneResourceParam['business_id']],
                    'link_url'   => $sLinkUrl ?? '',
                ];
            }

            //大额优惠感知
            if (CommunicateRepo::PREFERENTIAL_RULE == $aPriorityParam[$sEstimateId]['type']) {
                $aRuleData[$sEstimateId]['content'] = [
                    'event' => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                ];
            }

            //套餐
            if (CommunicateRepo::RECIPE_RULE == $aPriorityParam[$sEstimateId]['type']) {
                $aRuleData[$sEstimateId]['content'] = [
                    'event'      => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'button'     => $aResourceData[$sEstimateId]['recipe_rule']['button'],
                    'bg_color'   => '',
                    'text_color' => '',
                    'text'       => $aResourceData[$sEstimateId]['recipe_rule']['title'],
                    'icon'       => Language::getTextFromDcmp('communicate_component-recipe_rule_icon') ?? '',
                    'link_url'   => $aResourceData[$sEstimateId]['recipe_rule']['url'],
                ];
                $aRuleData[$sEstimateId]['track']   = [
                    'estimate_id' => $sEstimateId,
                    'goods_id'    => $aOneResourceParam['recipe_rule']['goods_id'],
                ];
            }

            //专车等必补
            if (CommunicateRepo::COMPENSATION_DIVER_ARRIVED_LATE_RULE == $aPriorityParam[$sEstimateId]['type']) {
                $aRuleData[$sEstimateId]['content'] = [
                    'event'      => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'rule_type'  => $aOneResourceParam['compensation_driver_arrived_late_rule']['rule_type'] ?? [],
                    'bg_color'   => $this->_aCompensationWaitData['bg_color'],
                    'text_color' => $this->_aCompensationWaitData['text_color'],
                    'text'       => $this->_aCompensationWaitData['text'],
                    'link_url'   => $this->_aCompensationWaitData['url'],
                ];
            }

            //优惠中心感知
            if (CommunicateRepo::PERIOD_DISCOUNT == $aPriorityParam[$sEstimateId]['type']
                && !empty($this->_aGetDiscountInfoData)
                && !empty($aOneResourceParam['period_discount_rule']['rule_type'])
            ) {
                $iRuleType = $aOneResourceParam['period_discount_rule']['rule_type'][0];
                if (empty($this->_aDefaultDiscountInfo)) {
                    // 不存在多品类返回
                    $aRuleData[$sEstimateId]['content'] = [
                        'event'     => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                        'rule_type' => $aOneResourceParam['period_discount_rule']['rule_type'],
                        'text'      => $this->_aGetDiscountInfoData['msg'],
                        'link_url'  => $this->_aGetDiscountInfoData['h5_url'],
                        'icon'      => $this->_aGetDiscountInfoData['icon_url'],
                    ];
                } else {
                    $aRuleData[$sEstimateId]['content'] = [
                        'event'     => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                        'rule_type' => $aOneResourceParam['period_discount_rule']['rule_type'],
                        'text'      => $this->_aGetDiscountInfoData[$iRuleType]['msg'],
                        'link_url'  => $this->_aGetDiscountInfoData[$iRuleType]['h5_url'],
                        'icon'      => $this->_aGetDiscountInfoData[$iRuleType]['icon_url'],
                    ];
                }
            }

            // 派单倾斜(快车)
            if (CommunicateRepo::DISPATCH_ORDER_LEAN == $aPriorityParam[$sEstimateId]['type']) {
                $aDisPatchOrderLeanText = $this->_aCommunicateInfoText['new_dispatch_order_lean'] ?? [];
                $aRuleData[$sEstimateId]['content']  = [
                    'event'     => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'rule_type' => $aOneResourceParam['dispatch_order_lean']['rule_type'] ?? [],
                ];
                $aRuleData[$sEstimateId]['content'] += DispatchOrderLeanLogic::getInstance()->getContent($aDisPatchOrderLeanText);
            }

            // 会员权益
            if (CommunicateRepo::MEMBER_BENEFIT == $aPriorityParam[$sEstimateId]['type']) {
                $aMemberBenefitRule = $this->_aCommunicateInfoText['member_benefit_rule'];
                $aRuleData[$sEstimateId]['content'] = [
                    'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'rule_type'            => $aOneResourceParam['member_benefit_rule']['rule_type'] ?? [],
                    'text'                 => $aMemberBenefitRule['text'][$aOneResourceParam['member_benefit_rule']['rule_type'][0]] ?? '',
                    'background_gradients' => $aMemberBenefitRule['background_gradients'] ?? [],
                    'icon'                 => $aMemberBenefitRule['icon'],
                    'text_color'           => $aMemberBenefitRule['text_color'],
                ];
            }

            // 会员权益确定性感知
            if (CommunicateRepo::MEMBER_CERTAINTY == $aPriorityParam[$sEstimateId]['type']) {
                $aRuleData[$sEstimateId]['content']  = [
                    'event'     => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'rule_type' => $aOneResourceParam['member_certainty_rule']['rule_type'] ?? [],
                ];
                $aRuleData[$sEstimateId]['content'] += MemberBenefitInfoLogic::getInstance()->getMemberCertaintyContent($this->_aMemberCertainty);
            }

            //优惠任务提示
            if (CommunicateRepo::TASK_REMINDER == $aPriorityParam[$sEstimateId]['type']) {
                $aTaskReminderInfo = $this->_aTaskReminderInfo[$sEstimateId];
                $aRuleData[$sEstimateId]['content'] = [
                    'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'text'                 => $aTaskReminderInfo['text'] ?? '',
                    'task_id'              => $aTaskReminderInfo['task_id'],
                    'task_status'          => $aTaskReminderInfo['task_status'],
                    'text_color'           => $this->_aCommunicateInfoText['task_reminder_rule']['text_color'],
                    'icon'                 => $this->_aCommunicateInfoText['task_reminder_rule']['icon'],
                    'background_gradients' => $this->_aCommunicateInfoText['task_reminder_rule']['background_gradients'] ?? [],
                ];

                if (TaskReminderLogic::TASK_NO_GET == $aTaskReminderInfo['task_status']) {
                    $aRuleData[$sEstimateId]['content']['button'] = $this->_aCommunicateInfoText['task_reminder_rule']['button'] ?? '';
                }

                $aRuleData[$sEstimateId]['track'] = [
                    'estimate_id' => $sEstimateId,
                    'activity_id' => $aTaskReminderInfo['task_id'],
                    'user_id'     => $aTaskReminderInfo['user_id'],
                    'task_id'     => $aTaskReminderInfo['exact_task_id'],
                ];
            }

            //909未支付订单提示
            if (CommunicateRepo::UNPAID_RULE == $aPriorityParam[$sEstimateId]['type']) {
                $aRuleData[$sEstimateId]['content'] = [
                    'event' => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'text'  => $this->_aUnpaidOrderInfo['communicate_title'] ?? '',
                ];
            }

            // 豪华车搭售付费月卡
            if (CommunicateRepo::LUX_CAR_PAID_CARD_RULE == $aPriorityParam[$sEstimateId]['type']) {
                // 向dcmp拉取样式配置
                $luxCarConfig = NuwaConfig::text('side_estimate', 'luxury_paid_card');
                if (!empty($luxCarConfig)) {
                    $sText = $luxCarConfig['text'];
                    if ($this->_aLuxuryMemberShip['is_first_open']) {
                        //首次开通
                        $sText = $luxCarConfig['new_user']['text'];
                    }

                    $aRuleData[$sEstimateId]['content'] = [
                        'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                        'rule_type'            => $aOneResourceParam['lux_car_paid_card_rule']['rule_type'] ?? [],
                        'text'                 => $sText ?? '',
                        'text_color'           => $luxCarConfig['text_color'],
                        'link_url'             => $luxCarConfig['link_url'],
                        'background_gradients' => $luxCarConfig['bg_gradients'],
                        'icon'                 => $luxCarConfig['bg_img'],
                        'button'               => $luxCarConfig['button_text'],
                        'button_style'         => [
                            'text_color' => $luxCarConfig['button_font_color'],
                        ],
                    ];
                    $aRuleData[$sEstimateId]['track']   = [
                        'is_first_open' => $this->_aLuxuryMemberShip['is_first_open'],
                    ];
                }
            }

            // 绿色环保沟通组件
            if (CommunicateRepo:: GREEN_ENV_PROTECTION == $aPriorityParam[$sEstimateId]['type']) {
                $aGreenProtConfig = NuwaConfig::text('side_estimate', 'green_environment_protection');
                if (!empty($aGreenProtConfig)) {
                    $aRuleData[$sEstimateId]['content'] = [
                        'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                        'rule_type'            => $aOneResourceParam['green_env_protection']['rule_type'] ?? [],
                        'text'                 => $aGreenProtConfig['text'] ?? '',
                        'text_color'           => $aGreenProtConfig['text_color'],
                        'link_url'             => $aGreenProtConfig['link_url'],
                        'background_gradients' => $aGreenProtConfig['background_gradients'],
                        'icon'                 => $aGreenProtConfig['bg_img'],
                    ];
                }
            }

            // 特区配置化沟通组件
            if (CommunicateRepo:: CUSTOMIZED_EVENT == $aPriorityParam[$sEstimateId]['type']) {
                $aRuleData[$sEstimateId]['content'] = [
                    'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'rule_type'            => $aOneResourceParam['customized_event']['rule_type'] ?? [],
                    'text'                 => $this->_aCustomizedEvent['text'] ?? '',
                    'text_color'           => $this->_aCustomizedEvent['font_color'] ?? '',
                    'background_gradients' => $this->_aCustomizedEvent['bg_gradients'] ?? [],
                    'icon'                 => $this->_aCustomizedEvent['img_url'] ?? '',
                ];
            }

            // 拼车配置化沟通组件
            if (CommunicateRepo:: CARPOOL_CUSTOM_STYLE == $aPriorityParam[$sEstimateId]['type']) {
                $aCarpoolCustom = $aCommunicateInfoText['carpool_custom_style'] ?? [];
                $aRuleData[$sEstimateId]['content'] = [
                    'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                    'rule_type'            => $aOneResourceParam['carpool_custom_style']['rule_type'] ?? [],
                    'text'                 => $this->_aCarpoolCustomizedEvent['text'] ?? '',
                    'icon'                 => $this->_aCarpoolCustomizedEvent['img_url'] ?? '',
                    'link_url'             => $this->_aCarpoolCustomizedEvent['link_url'] ?? '',
                    'text_color'           => $aCarpoolCustom['text_color'] ?? '',
                    'background_gradients' => $aCarpoolCustom['bg_gradients'] ?? [],
                    'button'               => $aCarpoolCustom['button'] ?? '',
                    'button_style'         => $aCarpoolCustom['button_style'] ?? [],
                ];
            }

            // 通勤用户权益
            if (CommunicateRepo::MEMBER_PRIVILEGES_COMMUTE_USER == $aPriorityParam[$sEstimateId]['type']) {
                $commuteUserConfig = NuwaConfig::text('side_estimate', 'commute_user_event_rule');
                if (!empty($commuteUserConfig)) {
                    $scecondLineText = MemberBenefitInfoLogic::getInstance()->getMemberCommuteText($this->_aMemberCommuteUser);
                    $commuteUserText = sprintf("%s\n%s", $commuteUserConfig['text'], $scecondLineText);
                    $aRuleData[$sEstimateId]['content'] = [
                        'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                        'rule_type'            => $aOneResourceParam['commute_user_event_rule']['rule_type'] ?? [],
                        'text'                 => $commuteUserText ?? '',
                        'text_color'           => $commuteUserConfig['text_color'],
                        'background_gradients' => $commuteUserConfig['bg_gradients'],
                        'icon'                 => $commuteUserConfig['bg_img'],
                    ];
                }
            }

            // 豪华车感知
            if (CommunicateRepo::LUXURY_CAR_PERCEPTION_EVENT == $aPriorityParam[$sEstimateId]['type']) {
                $luxuryCarPerceptionConfig = NuwaConfig::text('side_estimate', 'luxury_car_perception_event');
                if (!empty($luxuryCarPerceptionConfig)) {
                    $text = $this->_getLuxuryPerceptionText($luxuryCarPerceptionConfig);
                    $aRuleData[$sEstimateId]['content'] = [
                        'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                        'rule_type'            => $aOneResourceParam['luxury_car_perception_event']['rule_type'] ?? [],
                        'text'                 => $text ?? '',
                        'text_color'           => $luxuryCarPerceptionConfig['text_color'],
                        'background_gradients' => $luxuryCarPerceptionConfig['bg_gradients'],
                        'icon'                 => $luxuryCarPerceptionConfig['bg_img'],
                    ];
                }
            }

            // 豪华车续费引导
            if (CommunicateRepo::LUXURY_CAR_RENEWAL_GUIDANCE == $aPriorityParam[$sEstimateId]['type']) {
                $luxuryCarRenewalGuidanceConfig = NuwaConfig::text('side_estimate', 'luxury_car_renewal_guidance_event');
                if (!empty($luxuryCarRenewalGuidanceConfig)) {
                    $text = $this->_getLuxuryRenewalGuidanceText($luxuryCarRenewalGuidanceConfig);
                    $aRuleData[$sEstimateId]['content'] = [
                        'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                        'rule_type'            => $aOneResourceParam['luxury_car_renewal_guidance']['rule_type'] ?? [],
                        'text'                 => $text ?? '',
                        'text_color'           => $luxuryCarRenewalGuidanceConfig['text_color'],
                        'background_gradients' => $luxuryCarRenewalGuidanceConfig['bg_gradients'],
                        'icon'                 => $luxuryCarRenewalGuidanceConfig['bg_img'],
                        'link_url'             => $luxuryCarRenewalGuidanceConfig['link_url'],
                        'button'               => $luxuryCarRenewalGuidanceConfig['button_text'],
                        'button_style'         => [
                            'text_color' => $luxuryCarRenewalGuidanceConfig['button_font_color'],
                        ],
                    ];
                }
            }

            // 内循环账户折扣
            if (CommunicateRepo:: REVOLVING_ACCOUNT_DISCOUNT_RULE_TYPE == $aPriorityParam[$sEstimateId]['type']) {
                $aRevolvingAccountDiscountConfig = NuwaConfig::text('side_estimate', self::REVOLVING_ACCOUNT_DISCOUNT_CONFIG);
                $sPid        = $this->_aParam['pid'];
                $sKey        = UtilCommon::getRedisPrefix(P_REVOLVING_ACCOUNT_DISCOUNT_COMMUNICATION) . $sPid;
                $iLimitTimes = empty($aRevolvingAccountDiscountConfig['limitTimes']) ? 3 : $aRevolvingAccountDiscountConfig['limitTimes'];
                RedisCommon::increaseLimitTime($sKey, $iLimitTimes);
                if (!empty($aRevolvingAccountDiscountConfig)) {
                    $aRuleData[$sEstimateId]['content'] = [
                        'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                        'rule_type'            => $aOneResourceParam[self::REVOLVING_ACCOUNT_DISCOUNT_RULE]['rule_type'] ?? [],
                        'text'                 => Language::replaceTag(
                            $aRevolvingAccountDiscountConfig['text'],
                            ['amount' => self::getDiscountAmountByDiscountDesc($aQuotationData)]
                        ),
                        'text_color'           => $aRevolvingAccountDiscountConfig['text_color'],
                        'link_url'             => $aRevolvingAccountDiscountConfig['link_url'],
                        'background_gradients' => $aRevolvingAccountDiscountConfig['background_gradients'],
                        'icon'                 => $aRevolvingAccountDiscountConfig['bg_img'],
                    ];
                }
            }

            // 内循环账户返利
            if (CommunicateRepo:: REVOLVING_ACCOUNT_REBATE_RULE_TYPE == $aPriorityParam[$sEstimateId]['type']) {
                $aRevolvingAccountRebateConfig = NuwaConfig::text('side_estimate', self::REVOLVING_ACCOUNT_REBATE_CONFIG);
                $sPid        = $this->_aParam['pid'];
                $sKey        = UtilCommon::getRedisPrefix(P_REVOLVING_ACCOUNT_REBATE_COMMUNICATION) . $sPid;
                $iLimitTimes = empty($aRevolvingAccountDiscountConfig['limitTimes']) ? 3 : $aRevolvingAccountDiscountConfig['limitTimes'];
                RedisCommon::increaseLimitTime($sKey, $iLimitTimes);
                if (!empty($aRevolvingAccountRebateConfig)) {
                    $aRuleData[$sEstimateId]['content'] = [
                        'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                        'rule_type'            => $aOneResourceParam[self::REVOLVING_ACCOUNT_REBATE_RULE]['rule_type'] ?? [],
                        'text'                 => Language::replaceTag(
                            $aRevolvingAccountRebateConfig['text'],
                            ['amount' => self::getRebateAmountByQuotaion($aQuotationData)]
                        ),
                        'text_color'           => $aRevolvingAccountRebateConfig['text_color'],
                        'link_url'             => $aRevolvingAccountRebateConfig['link_url'],
                        'background_gradients' => $aRevolvingAccountRebateConfig['background_gradients'],
                        'icon'                 => $aRevolvingAccountRebateConfig['bg_img'],
                    ];
                }
            }

            // 内循环账户返利
            if (CommunicateRepo:: REVOLVING_ACCOUNT_REBATE_N_FOLD_RULE_TYPE == $aPriorityParam[$sEstimateId]['type']) {
                $aRevolvingAccountRebateConfig = NuwaConfig::text('side_estimate', self::REVOLVING_ACCOUNT_REBATE_N_FOLD);
                $sPid        = $this->_aParam['pid'];
                $sKey        = UtilCommon::getRedisPrefix(P_REVOLVING_ACCOUNT_REBATE_N_FOLD_COMMUNICATION) . $sPid;
                $iLimitTimes = empty($aRevolvingAccountDiscountConfig['limitTimes']) ? 3 : $aRevolvingAccountDiscountConfig['limitTimes'];
                RedisCommon::increaseLimitTime($sKey, $iLimitTimes);
                if (!empty($aRevolvingAccountRebateConfig)) {
                    $aRuleData[$sEstimateId]['content'] = [
                        'event'                => $this->_aTypeEventMap[$aPriorityParam[$sEstimateId]['type']],
                        'rule_type'            => $aOneResourceParam[self::REVOLVING_ACCOUNT_REBATE_RULE]['rule_type'] ?? [],
                        'text'                 => Language::replaceTag(
                            $aRevolvingAccountRebateConfig['text'],
                            [
                                'amount' => self::getRebateAmountByQuotaion($aQuotationData),
                            ]
                        ),
                        'text_color'           => $aRevolvingAccountRebateConfig['text_color'],
                        'link_url'             => $aRevolvingAccountRebateConfig['link_url'],
                        'background_gradients' => $aRevolvingAccountRebateConfig['background_gradients'],
                        'icon'                 => $aRevolvingAccountRebateConfig['bg_img'],
                    ];
                }
            }
        }

        return $aRuleData;
    }


    /**
     * 根据数组中某个字段对数组进行排序
     * @param array  $array        原始数组
     * @param string $typeOne      排位字段
     * @param int    $sortOrderOne 排位顺序
     * @return mixed
     */
    private function _sortArrayOne($array, $typeOne, $sortOrderOne = SORT_DESC) {
        foreach ($array as $sKey => $row) {
            $volumeOne[$sKey] = $row[$typeOne];
        }

        array_multisort($volumeOne, $sortOrderOne, $array);
        return $array;
    }

    /**
     * 根据数组中某2个字段对数组进行排序,先按照type_one,再按照type_two排序
     * @param array  $array        原始数组
     * @param string $typeOne      第一个排位字段
     * @param string $typeTwo      第二个排位字段
     * @param int    $sortOrderOne 第一个字段排位顺序
     * @param int    $sortOrderTwo 第二个字段排位顺序
     * @return mixed
     */
    private function _sortArrayTwo($array, $typeOne, $typeTwo, $sortOrderOne = SORT_DESC, $sortOrderTwo = SORT_DESC) {
        foreach ($array as $sKey => $row) {
            $volumeOne[$sKey] = $row[$typeOne];
            $volumeTwo[$sKey] = $row[$typeTwo];
        }

        array_multisort($volumeOne, $sortOrderOne, $volumeTwo, $sortOrderTwo, $array);
        return $array;
    }

    /**
     * @desc 构造PreferentialData
     * @return array
     */
    private function _getPreferentialData() {
        $aPreferentialProducts = $this->_aPreferentialProducts;
        $aDCMPConfig           = $this->_aPreferentialConfigText;
        $aEstimateList         = array_column($aPreferentialProducts, 'eid');

        if (count($aPreferentialProducts) > 2) {
            $aProductNameList = array_column($aPreferentialProducts, 'product_name');
            $sDiscountFlag    = 'before_sub_title2';
            foreach ($aPreferentialProducts as $aProduct) {
                if ($aProduct['preferential_recommend']) {
                    if ($aProduct['discount'] > 80 && $aProduct['discount'] <= 90) {
                        $sDiscountFlag = 'before_sub_title2_1';
                    } elseif ($aProduct['discount'] > 90) {
                        $sDiscountFlag = 'before_sub_title2_2';
                    }

                    break;
                }
            }

            $sProductStr    = implode('/', $aProductNameList);
            $sSubTitle      = Language::replaceTag($aDCMPConfig[$sDiscountFlag], ['desc' => $sProductStr,]);
            $sAfterSubTitle = Language::replaceTag($aDCMPConfig[$sDiscountFlag], ['desc' => $sProductStr,]);
        } else {
            $aTmpSubTitle1 = [];
            $aTmpSubTitle2 = [];
            foreach ($aPreferentialProducts as $item) {
                $fDiscount1      = round($item['discount'] / 10, 1);
                $fDiscount2      = 100 - $item['discount'];
                $aTmpSubTitle1[] = Language::replaceTag(
                    $aDCMPConfig['after_sub_title1_1'],
                    [
                        'discount1' => $fDiscount1,
                        'discount2' => $fDiscount2,
                        'category'  => $item['product_name'],
                        'text'      => $aDCMPConfig['discount_text'],
                    ]
                );
                $aTmpSubTitle2[] = Language::replaceTag(
                    $aDCMPConfig['before_sub_title1_1'],
                    [
                        'discount1' => $fDiscount1,
                        'discount2' => $fDiscount2,
                        'category'  => $item['product_name'],
                        'text'      => $aDCMPConfig['discount_text'],
                    ]
                );
            }

            $sProductStr1   = implode('，', $aTmpSubTitle1);
            $sProductStr2   = implode('，', $aTmpSubTitle2);
            $sSubTitle      = Language::replaceTag($aDCMPConfig['before_sub_title1'], ['desc' => $sProductStr2,]);
            $sAfterSubTitle = Language::replaceTag($aDCMPConfig['after_sub_title1'], ['desc' => $sProductStr1,]);
        }

        $data = [
            'show_estimate_list' => $aEstimateList,
            'gradient_colors'    => [$aDCMPConfig['start_color'], $aDCMPConfig['mid_color'], $aDCMPConfig['end_color']],
            'anim_icon'          => $aDCMPConfig['anim_icon'] ?? '',
            'anim_end_icon'      => $aDCMPConfig['anim_end_icon'] ?? '',
            'font_color'         => $aDCMPConfig['font_color'] ?? '',
            'before_anim'        => [
                'title'     => $aDCMPConfig['before_title'],
                'sub_title' => $sSubTitle,
                //'discount_color' => $aDCMPConfig['discount_color1'],
            ],
            'after_anim'         => [
                'title'          => '',
                'sub_title'      => $sAfterSubTitle,
                'link_url'       => $aDCMPConfig['after_link_url'] ?? '',
                'discount_color' => $aDCMPConfig['discount_color2'],
            ],
        ];

        return $data;
    }

    /**
     * 未支付订单信息
     * @return array|mixed
     */
    private function _getUnPaidOrder() {
        $aUnpaidInfo   = [];
        $oApollo       = new NuwaApollo();
        $oApolloResult = $oApollo->featureToggle(
            'hxz_unpaid_communicate_switch',
            [
                'key'           => $this->_aParam['phone'],
                'phone'         => $this->_aParam['phone'],
                'app_version'   => $this->_aParam['app_version'],
                'access_key_id' => $this->_aParam['access_key_id'],
                'lang'          => $this->_aParam['lang'],
                'city'          => $this->_aParam['area'],
                'open_time'     => date('H:i'),
                'week'          => date('w'),
            ]
        );

        if (!$oApolloResult->allow()) {
            return $aUnpaidInfo;
        }

        $sConfig = $oApolloResult->getParameter('text_config', null);
        if (empty($sConfig)) {
            return $aUnpaidInfo;
        }

        $aTextConfig = json_decode($sConfig, true);
        if (empty($aTextConfig) || !is_array($aTextConfig)) {
            return $aUnpaidInfo;
        }

        //根据pid获取用户特征
        $sUfsKey     = 'fee.kflower_bad_order_info';
        $oUsfClient  = new UfsClient();
        $aConditions = ['phone' => $this->_aParam['phone']];
        $aUfsRet     = $oUsfClient->getFeature([$sUfsKey], $aConditions, 'passenger');
        if (!isset($aUfsRet['errno']) || 0 != $aUfsRet['errno']) {
            return $aUnpaidInfo;
        }

        //$aUfsData = json_decode($aUfsRet['result'][$sUfsKey], true);
        if (!empty($aUfsRet['result'][$sUfsKey]) && 1 == $aUfsRet['result'][$sUfsKey]) {
            $aUnpaidInfo = $aTextConfig;
        }

        return $aUnpaidInfo;
    }

    /**
     * @desc 未付订单标题描述
     * @return array
     */
    private function _getUnpaidData() {
        return [
            'title'       => $this->_aUnpaidOrderInfo['alert_title'] ?? '',
            'content'     => $this->_aUnpaidOrderInfo['alert_content'] ?? '',
            'button_text' => $this->_aUnpaidOrderInfo['alert_button_text'] ?? '',
        ];
    }

    /**
     * @desc 获取预估返回拦截弹窗
     * @return array
     */
    private function _getInterceptData() {
        $aInterceptData       = [];
        $aInterceptOriginData = CommunicateInfoCache::getInterceptData($this->_aParam['estimate_trace_id']);
        if (!empty($aInterceptOriginData)) {
            $aInterceptData['bg_url']       = $aInterceptOriginData['image'];
            $aInterceptData['title']        = $aInterceptOriginData['title'];
            $aInterceptData['sub_title']    = $aInterceptOriginData['subtitle'];
            $aInterceptData['content']      = $aInterceptOriginData['content'];
            $aInterceptData['content_desc'] = $aInterceptOriginData['description'];
            $aInterceptData['left_button']  = $aInterceptOriginData['confirm_button'];
            $aInterceptData['right_button'] = $aInterceptOriginData['cancel_button'];
            $aInterceptData['extra']        = $aInterceptOriginData['extra'];
        }

        return $aInterceptData;
    }

    /**
     * @desc 获取弹窗
     * @return array
     */
    private function _getGuidePopupData() : array {
        $aResult       = array();
        $aRequestParam = $this->_aParam;

        if (empty($aRequestParam['v3_form'])) {
            // 非V3 的，需要校验是否是小程序的来源（v2的主端无需拼装）
            if (!in_array($aRequestParam['access_key_id'], [ConstantsCommon::DIDI_WECHAT_MINI_PROGRAM, ConstantsCommon::DIDI_ALIPAY_MINI_PROGRAM])) {
                return $aResult;
            }
        }

        try {
            $aBubblePopData = AthenaExtension::getInstance()->getPopUpData(); // athena冒泡弹窗
            if (empty($aBubblePopData)) {
                return [];
            }

            $aGuidePopupData = []; // 最终结果数据
            $aDialogData     = []; // 最终结果数据-对话弹窗信息

            if (1 == $aBubblePopData['text_valid']) { // text_valid如果是1，就用athena文案
                $aDialogData['img_url']      = $aBubblePopData['image'];
                $aDialogData['title']        = $aBubblePopData['title'];
                $aDialogData['content']      = $aBubblePopData['description'];
                $aDialogData['cancel_text']  = $aBubblePopData['cancel_button'];
                $aDialogData['confirm_text'] = $aBubblePopData['confirm_button'];

                $aGuidePopupData['dialog_data'] = $aDialogData;
                //$aGuidePopupData['is_link_product_select'] = 1; // 是否勾选link_product （必传）
                // 点击确定勾选的车型（必传），传父级车型的product_category （快+宽敞好车时传快车）
                $aGuidePopupData['product_category'] = $aBubblePopData['product_category'];
                return $aGuidePopupData;
            }

            switch ($aBubblePopData['pop_type']) {
                case 1: // 弹窗类型 1:开城扶持
                    if (empty($this->_aSpaciousCarInfo['fSelectionFee'])) {
                        return [];
                    }

                    $aTextConf = $this->_aCommunicateInfoText['spacious_car_dialog'];
                    if (empty($aTextConf)) {
                        return [];
                    }

                    $aDialogData['img_url']       = $aTextConf['image'];// 弹窗头图
                    $aDialogData['title']         = Language::replaceTag(
                        $aTextConf['title'],
                        ['amount' => $this->_aSpaciousCarInfo['fSelectionFee']]
                    );
                    $aDialogData['content']       = $aTextConf['content']; // 内容
                    $aDialogData['cancel_text']   = $aTextConf['cancelText'];// 取消文案
                    $aDialogData['confirm_text']  = $aTextConf['confirmText'];// 确定文案
                    $aDialogData['confirm_color'] = $aTextConf['confirm_color']; //确认按钮颜色

                    $aGuidePopupData['dialog_data']      = $aDialogData;
                    $aGuidePopupData['product_category'] = ProductCategory::PRODUCT_CATEGORY_SPACIOUS_CAR;
                    break;
                case GuidePopUp::POP_UP_123_DAY: // 123拼车日
                    $aText      = NuwaConfig::text('config_text', 'guide_pop_up');
                    $aPopText   = $aText[GuidePopUp::POP_UP_123_DAY] ?? [];
                    $aQuotation = self::getQuotationByProductCategory($this->_aQuotationInfo, $aBubblePopData['product_category']);
                    $fAmount    = $aQuotation['estimate_fee'] ?? 0;
                    if (empty($aPopText)) {
                        return [];
                    }

                    $sTitle          = Language::replaceTag($aPopText['title'],['amount' => $fAmount]);
                    $aGuidePopupData = [
                        'product_category' => $aBubblePopData['product_category'],
                        'dialog_data'      => [
                            'img_url'       => $aPopText['bg_url'],
                            'img_ratio'     => 0.5,
                            'title'         => $sTitle,
                            'sub_content'   => [
                                'link' => $aPopText['link_url'],
                                'text' => $aPopText['link_text'],
                            ],
                            'cancel_text'   => $aPopText['left_btn_text'],
                            'confirm_text'  => $aPopText['right_btn_text'],
                            'confirm_color' => $aPopText['confirm_color'],
                        ],
                    ];
                    break;
                default:
                    return [];
            }

            if (!empty($aGuidePopupData)) {
                PublicLog::getInstance()->appendLog(['guide_pop_type' => $aBubblePopData['pop_type'], 'guide_pop_pc' => $aBubblePopData['product_category']]);
            }

            return $aGuidePopupData;
        } catch (Exception $e) {
            Log::warning(
                Msg::formatArray(
                    Code::E_COMMON_REQ_FAIL,
                    ['result' => json_encode($aResult), 'params' => json_encode($aRequestParam)]
                )
            );
            return array();
        }
    }

    /**
     * @desc 获取 沟通组件的配置名称list
     * @return array
     */
    private function _getConfigNameList(): array {
        $aConfigNameList          = array();
        $this->_bCommunicateSplit = $this->_isCommunicateSplit();
        // 组合出行是否推荐
        $bIsCombinedTravelRecommend = CombinedTravelClient::RECOMMEND_TYPE_STRONG === $this->_aCombinedTravelRecommendInfo['recommend_type'];
        if ($this->_bCommunicateSplit) { // 是否分上下沟通组件 true: 是
            $aConfigNameList[] = 'rule_bottom';
            // 优先级：预估置顶样式>组合出行>rule_top
            if ($this->_bStickStyle) {
                $this->_aCombinedTravelRecommendInfo = [];
            } else {
                if (!$bIsCombinedTravelRecommend) {
                    $aConfigNameList[] = 'rule_top';
                }
            }
        } else {
            if (in_array($this->_aParam['access_key_id'], [Constants\Common::DIDI_IOS_ELDERLY, Constants\Common::DIDI_ANDROID_ELDERLY, Constants\Common::DIDI_WE_CHAT_ELDERLY])) {
                $aConfigNameList[] = 'rule_elderly';
            } else {
                if (!$bIsCombinedTravelRecommend) {
                    // 有组合出行强、弱推荐则不出激励组件
                    $aConfigNameList[] = 'rule';
                }
            }
        }

        return $aConfigNameList;
    }


    /**
     * @param array $aQuotations      报价单
     * @param int   $iProductCategory 品类ID
     * @return array
     */
    public static function getQuotationByProductCategory($aQuotations, $iProductCategory) {
        if (!is_array($aQuotations)) {
            return [];
        }

        foreach ($aQuotations as $aQuotation) {
            if ($iProductCategory == $aQuotation['product_category']) {
                return $aQuotation;
            }
        }

        return [];
    }


    /**
     * 获取大额优惠品类
     * @return void
     */
    private function _getPreferentialProducts() {
        $aQuotationData = $this->_aQuotationInfo;
        if (empty($aQuotationData)) {
            // 依赖报价单,报价单没获取到不进行判断
            return;
        }

        $aProductList   = $this->_aProductList;
        $aRecommendInfo = array();

        $aPreferentialProducts = [];
        $iDiscountConf         = $this->_aPreferentialConfigText['discount_val'];

        foreach ($aProductList as $sEstimateId => &$sOneEstimateData) {
            //获取athena缓存
            $sKey           = UtilsCommon::getRedisPrefix(P_ESTIMATE_BUBBLE_ACTIVITY) . $sEstimateId;
            $sRecommendData = \PreSale\Logics\redismove\RedisWrapper::getInstance(P_ESTIMATE_BUBBLE_ACTIVITY)->get($sKey);
            $sRecommendEid  = '';
            if (!empty($sRecommendData)) {
                $aRecommendData = json_decode($sRecommendData, true);
                $aRecommendType = $aRecommendData['recommend_info']['extra_info']['recommend_type_flag'];
                if (!empty($aRecommendType)) {
                    if (NormalRecommendInfo::RECOMMEND_SHOW_TYPE == $aRecommendType) {
                        $sRecommendEid = $sEstimateId; // 推荐
                    }

                    $aRecommendInfo[] = [$aRecommendType => $sEstimateId];
                }
            }

            if (isset($aRecommendInfo[NormalRecommendInfo::CAPACITY_RECOMMEND_TYPE])
                && (empty($aQuotationData[$sEstimateId]['dynamic_total_fee']) || !isset($aQuotationData[$sEstimateId]['estimate_fee']) || empty($this->_aPreferentialConfigText['discount_val']))
            ) {
                // 设置了运力推荐&& (报价单价格为空||折扣没配置)  =>   不展示
                continue;
            }

            $iDiscount   = ceil(($aQuotationData[$sEstimateId]['estimate_fee'] / $aQuotationData[$sEstimateId]['dynamic_total_fee']) * 100);
            $bFromAthena = $sEstimateId == $sRecommendEid; // 是否为Athena推荐
            if ($iDiscount <= (int)$iDiscountConf || $bFromAthena) { //小于等于8折 或Athena推荐
                $iProductCategory = (int)$aQuotationData[$sEstimateId]['product_category'];
                if ($this->showPreferential()) {
                    $sProductName = $this->_aPreferentialConfigText["product_category_{$iProductCategory}"] ?? '';
                    if (!empty($sProductName)) {
                        $this->_aRecommendProduct[$sEstimateId] = true;

                        $aProduct = [
                            'eid'                    => $sEstimateId,
                            'discount'               => $iDiscount,
                            'product_name'           => $sProductName,
                            'preferential_recommend' => $bFromAthena,
                        ];
                        if ($bFromAthena) {
                            array_unshift($aPreferentialProducts, $aProduct);
                        } else {
                            $aPreferentialProducts[] = $aProduct;
                        }
                    }
                }
            }
        }

        $this->_aPreferentialProducts = $aPreferentialProducts;
    }

    /**
     * 获取单单省命中情况
     * @return void
     */
    private function _getDanDanShengExist() {
        $aProductList   = $this->_aProductList;
        $aQuotationData = $this->_aQuotationInfo;

        foreach ($aProductList as $sEstimateId => &$sOneEstimateData) {
            $this->_aDanDanSheng[$sEstimateId] = false;
            $sDiscountDesc = $aQuotationData[$sEstimateId]['discount_desc'] ?? '[]';
            $aDiscountDesc = json_decode($sDiscountDesc, true);
            if (!empty($aDiscountDesc)) {
                foreach ($aDiscountDesc as $aDiscountDescItem) {
                    $iDiscountAmount = $aDiscountDescItem['amount'] ?? 0;
                    $sCustomTag      = $aDiscountDescItem['custom_tag'] ?? '';
                    if ($iDiscountAmount > 0 && self::COUPON_TAG_PERORDERSALE == $sCustomTag) {
                        // 命中单单省
                        $this->_aDanDanSheng[$sEstimateId] = true;
                        break;
                    }
                }
            }
        }
    }

    /**
     * 获取券信息
     * @param string $sEstimateId ...
     * @param string $sTag        ...
     * @return mixed
     */
    private function _getCouponInfo($sEstimateId, $sTag) {
        $aDiscountDesc = $this->_aQuotationInfo[$sEstimateId]['discount_desc'];

        $sDiscountDesc = $aDiscountDesc ?? '[]';
        $aDiscountDesc = json_decode($sDiscountDesc, true);
        if (!empty($aDiscountDesc)) {
            foreach ($aDiscountDesc as $aDiscountDescItem) {
                $iDiscountAmount = $aDiscountDescItem['amount'] ?? 0;
                $sCustomTag      = $aDiscountDescItem['custom_tag'] ?? '';
                if ($iDiscountAmount > 0 && $sTag == $sCustomTag) {
                    return $aDiscountDescItem;
                }
            }
        }

        return [];
    }

    /**
     * 判断该用户是否处于指定人群
     * @return bool
     */
    private function _getTag($sTag) {
        $sPid = $this->_aParam['pid'];
        return TagServiceRepository::isPassengerInTag($sPid, $sTag, TagServiceRepository::DEFAULT_CALLER);
    }

    /**
     * @return int
     */
    private function _getNewMemberCouponAmount() {
        if (!$this->_getTag(PremiumPaidMemberActivityRuleEvent::MEMBER_NEW_TAG)) {
            return 0;
        };
        $aApolloRes = ApolloV2::getInstance()->featureToggle(
            'premier_expand_coupon',
            [
                'key'           => $this->_aParam['pid'],
                'pid'           => $this->_aParam['pid'],
                'phone'         => $this->_aParam['phone'],
                'access_key_id' => $this->_aParam['access_key_id'],
                'lang'          => $this->_aParam['lang'],
                'city'          => $this->_aParam['area'],
                'app_version'   => $this->_aParam['app_version'],
            ]
        );

        if (!$aApolloRes->allow()) {
            return 0;
        }

        return intval(((int)$aApolloRes->getParameter('coupon_amount', '0')) / 100);
    }

    /**
     * 判断该用户是否使用豪华车券
     * @param array $aQuotation 报价单
     * @return bool
     */
    private function _isLuxuryCarCoupns($aQuotation) {
        if (!empty($aQuotation) && !empty($aQuotation['discount_desc'])) {
            $discountDesc = json_decode($aQuotation['discount_desc'], true);

            foreach ($discountDesc as $item) {
                if (self::LUXURY_CAR_COUPONS_CUSTOM_TAG == $item['custom_tag']) {
                    $this->_aLuxCoupon = $item;
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 判断该用户是否展示豪华车感知
     * @param array $aQuotation 报价单
     * @return bool
     */
    private function _isShowLuxuryPerception($aQuotation) {
        $memberShipInfo = $this->_aLuxuryMemberShip;
        if (empty($memberShipInfo) || empty($aQuotation)) {
            Log::warning(sprintf('luxury_isShowLuxuryPerception is nil filter.memberInfo:%s, quotation:%s', json_encode($memberShipInfo), json_encode($aQuotation)));
            return false;
        }

        $curTime = time();
        $endTime = $memberShipInfo['end_time'];
        // 判断身份是否过期
        if ($curTime > $endTime) {
            return false;
        }

        // 非豪华车的券
        if (!$this->_isLuxuryCarCoupns($aQuotation)) {
            return false;
        }

        return true;
    }

    /**
     * 判断该用户是否展示豪华车续费引导
     * @return bool
     */
    private function _isShowLuxuryRenewalGuidance() {
        $memberShipInfo = $this->_aLuxuryMemberShip;
        if (empty($memberShipInfo)) {
            return false;
        }

        $curTime = time();
        $endTime = $memberShipInfo['end_time'];
        // 身份过期 或 距离身份过期时间超过7天
        if (empty($endTime) || $curTime > $endTime || $endTime - $curTime > 7 * self::ONE_DAY) {
            return false;
        }

        // 可续费状态
        if (empty($memberShipInfo['is_renewable']) || !$memberShipInfo['is_renewable']) {
            return false;
        }

        // 3个月sku为空
        if (!isset($memberShipInfo['total_save'])) {
            return false;
        }

        return true;
    }

    /**
     * 获取豪华车身份感知文案
     * @param array $config 配置
     * @return string
     */
    private function _getLuxuryPerceptionText($config) {
        $text = Language::replaceTag(
            $config['text'],
            array(
                'amount' => $this->_aLuxCoupon['amount'],
            )
        );

        return $text;
    }

    /**
     * 获取豪华车续费引导文案
     * @param array $config 配置
     * @return string
     */
    private function _getLuxuryRenewalGuidanceText($config) {
        $curTime   = time();
        $endTime   = $this->_aLuxuryMemberShip['end_time'];
        $textUp    = '';
        $totalSave = $this->_aLuxuryMemberShip['total_save'];
        // 不是最后一天
        if ($endTime - $curTime > self::ONE_DAY) {
            $textUp = Language::replaceTag(
                $config['text'],
                array(
                    'last_day' => ceil(($endTime - $curTime) / self::ONE_DAY),
                )
            );
        } else {
            // 是最后一天
            $textUp = $config['last_day_text'];
        }

        if (empty($totalSave)) {
            $textDown = $config['text_down_no_total_save'];
        } else {
            $textDown = Language::replaceTag(
                $config['text_down'],
                array('sku' => $totalSave / 100,)
            );
        }

        return sprintf("%s\n%s", $textUp, $textDown);
    }

    /**
     * 特区配置化沟通组件（全品类）
     * @return array
     */
    private function _getCustomizeResult() {
        $aRet         = [];
        $aToggleParam = [
            'key'           => $this->_aParam['uid'],
            'phone'         => $this->_aParam['phone'],
            'pid'           => $this->_aParam['pid'],
            'access_key_id' => $this->_aParam['access_key_id'],
            'lang'          => $this->_aParam['lang'],
            'city'          => $this->_aParam['area'],
            'area'          => $this->_aParam['area'],
            'app_version'   => $this->_aParam['app_version'],
        ];

        $oApollo = NuwaApollo::getInstance()->featureToggle('customized_event_switch', $aToggleParam);
        if (!$oApollo->allow() || 'control_group' == $oApollo->getGroupName()) {
            return [];
        }

        $sFirstText         = trim($oApollo->getParameter('first_row_text', ''));
        $sSecondText        = trim($oApollo->getParameter('second_row_text', ''));
        $aRet['text']       = $sFirstText."\n".$sSecondText;
        $aRet['img_url']    = trim($oApollo->getParameter('img_url', ''));
        $aRet['font_color'] = trim($oApollo->getParameter('font_color', ''));
        $aRet['highlight_color'] = trim($oApollo->getParameter('highlight_color', ''));
        // e.g. "#FFFF,#FFFF"
        $sBgGradient = trim($oApollo->getParameter('bg_gradients', ''));
        if (strlen($sBgGradient) > 0) {
            $aRet['bg_gradients'] = explode(',',$sBgGradient);
        }

        if (empty($aRet['text']) || empty($aRet['img_url'])) {
            return [];
        }

        return $aRet;
    }

    /**
     * 拼车配置化沟通组件
     * @return array
     */
    private function _getCarpoolCustomizedResult() {
        $aToggleParam = [
            'key'           => $this->_aParam['uid'],
            'phone'         => $this->_aParam['phone'],
            'pid'           => $this->_aParam['pid'],
            'access_key_id' => $this->_aParam['access_key_id'],
            'lang'          => $this->_aParam['lang'],
            'city'          => $this->_aParam['area'],
            'area'          => $this->_aParam['area'],
            'app_version'   => $this->_aParam['app_version'],
            'is_new_form'   => 0,
        ];

        $oApollo = NuwaApollo::getInstance()->featureToggle('carpool_custom_communication', $aToggleParam);
        if (!$oApollo->allow() || 'control_group' == $oApollo->getGroupName()) {
            return [];
        }

        $sFirstText       = trim($oApollo->getParameter('first_row_text', ''));
        $sSecondText      = trim($oApollo->getParameter('second_row_text', ''));
        $aRet['text']     = $sFirstText."\n".$sSecondText;
        $aRet['img_url']  = trim($oApollo->getParameter('img_url', ''));
        $aRet['link_url'] = trim($oApollo->getParameter('link_url', ''));

        if (empty($aRet['text']) || empty($aRet['img_url']) || empty($aRet['link_url'])) {
            return [];
        }

        return $aRet;
    }

    /**
     * @param int $iProductCategory 品类
     * @return string
     */
    protected function isHitApolloCommonEvent($iProductCategory) {
        $aCommonEvent = ApolloHelper::getConfigContent('common_event', 'common_event');
        if (empty($aCommonEvent['apollo_event']) || !is_array($aCommonEvent['apollo_event'])) {
            return '';
        }

        foreach ($aCommonEvent['apollo_event'] as $sKey) {
            $aApolloParams = [
                'key'              => $this->_aParam['uid'],
                'event'            => $sKey,
                'pid'              => $this->_aParam['pid'],
                'phone'            => $this->_aParam['phone'],
                'lang'             => $this->_aParam['lang'],
                'city'             => $this->_aParam['area'],
                'product_category' => $iProductCategory,
                'access_key_id'    => $this->_aParam['access_key_id'],
                'app_version'      => $this->_aParam['app_version'],
            ];

            $aApolloRes = Apollo::getInstance()->featureToggle('gs_common_apollo_event', $aApolloParams);
            if (!$aApolloRes->allow()) {
                continue;
            }

            $aCommonEventRule = ApolloHelper::getConfigContent('common_event', $sKey);

            if (!empty($aCommonEventRule['ab_test_key_uid'])) {
                $aApolloRes = Apollo::getInstance()->featureToggle($aCommonEventRule['ab_test_key_uid'], $aApolloParams);
                if (!$aApolloRes->allow() || 0 == $aApolloRes->getParameter('hit_communicate', 0)) {
                    continue;
                }
            }

            if (!empty($aCommonEventRule['ab_test_key_pid'])) {
                $aApolloParams['key'] = $this->_aParam['pid'];
                $aApolloRes           = Apollo::getInstance()->featureToggle($aCommonEventRule['ab_test_key_pid'], $aApolloParams);
                if (!$aApolloRes->allow() || 0 == $aApolloRes->getParameter('hit_communicate', 0)) {
                    continue;
                }
            }

            // 人群标签校验   注意: tag_id_list逗号分开(最多五个)
            if (!empty($aCommonEventRule['tag_id_list'])) {
                $bHit = TagServiceRepository::isAnyoneInTag($this->_aParam['pid'], $aCommonEventRule['tag_id_list'], TagServiceRepository::DEFAULT_CALLER);
                if (!$bHit) {
                    continue;
                }
            }

            $this->iCommunicateStyle[$iProductCategory] = $aCommonEventRule['communicate_style'];
            $this->iRuleType[$iProductCategory]         = $aCommonEventRule['type'];
            $this->sDcmpKeyApollo[$iProductCategory]    = $aCommonEventRule['dcmp_key_apollo'];
            return $sKey;
        }

        return '';
    }

    private function _getEjectLayer() {
        $aApolloRes = Apollo::getInstance()->featureToggle(
            'gs_eject_layer_bubble_v2_version',
            [
                'key'           => $this->_aParam['uid'],
                'pid'           => $this->_aParam['pid'],
                'phone'         => $this->_aParam['phone'],
                'lang'          => $this->_aParam['lang'],
                'city'          => $this->_aParam['area'],
                'access_key_id' => $this->_aParam['access_key_id'],
                'app_version'   => $this->_aParam['app_version'],
            ]
        );

        if (!$aApolloRes->allow()) {
            return [];
        }

        $oClient = new PorscheClient();

        $aParams = array(
            'city_id'       => $this->_aParam['area'],
            'access_key_id' => $this->_aParam['access_key_id'],
            'app_version'   => $this->_aParam['app_version'],
            'lang'          => $this->_aParam['lang'],
            'token'         => $this->_aParam['token'],
            'page_select'   => 'wyc_bubble_v2',
        );

        $aRet = $oClient->personalizedSwitchSource($aParams);
        if (empty($aRet) || 0 != $aRet['errno']) {
            return [];
        }

        return $aRet['data'];
    }

    /**
     * 是否是N倍返活动
     * @param array $aQuotationData 报价单
     * @param int $sEstimateId eid
     * @return bool
     */
    private function isNFoldActivity($aQuotationData, $sEstimateId) {
        if (empty(self::getRebateAmountByQuotaion($aQuotationData[$sEstimateId])) || self::getRebateAmountByQuotaion($aQuotationData[$sEstimateId]) <= 0) {
            return false;
        }

        if (self::POPE_ACTIVITY_N_FOLD != $this->_iActivityType) {
            return false;
        }

        if ($this->_iMultiple <= 1) {
            return false;
        }

        return true;
    }

    /**
     * 通过pcid获取报价单
     * @param array $aQuotationData 报价单
     * @param int $iPcId pcid
     * @return array
     */
    private function getQuotationByPcId($aQuotationData, $iPcId) {
        if (empty($aQuotationData) || !is_array($aQuotationData)) {
            return null;
        }

        foreach ($aQuotationData as $aQuotation) {
            if ($aQuotation['product_category'] == $iPcId) {
                return $aQuotation;
            }
        }

        return null;
    }

    private function _getRebateInfo() {
        $activityType    = 0;
        $multiple        = 0;
        $aQuotationData  = $this->_aQuotationInfo;
        $aAPlusQuotation = $this->getQuotationByPcId($aQuotationData, ProductCategory::PRODUCT_CATEGORY_APLUS);
        if (empty($aAPlusQuotation)) {
            return [$activityType, $multiple];
        }

        if (empty($aAPlusQuotation['discount_desc'])) {
            return [$activityType, $multiple];
        }

        $aDiscountList = json_decode($aAPlusQuotation['discount_desc'], true);
        $aDiscountList = is_array($aDiscountList) ? $aDiscountList : [];
        foreach ($aDiscountList as $aDiscountItem) {
            if (self::REVOLVING_ACCOUNT_REBATE == $aDiscountItem['type']) {
                $activityType = $aDiscountItem['extra_info']['activity_type'];
                $multiple     = $aDiscountItem['extra_info']['multiple'];
                return [$activityType, $multiple];
            }
        }

        return [$activityType, $multiple];
    }

    private function _getRevolvingAccountRebateNFold() {
        $sRevolvingAccountRebate = 0;
        foreach ($this->_aQuotationInfo as $val) {
            $sRes = self::getRebateAmountByQuotaion($val);
            if (!empty($sRes)) {
                $sRevolvingAccountRebate = $sRes;
            }
        }

        $aRevolvingAccountRebateConfig = NuwaConfig::text('side_estimate', self::REVOLVING_ACCOUNT_REBATE_N_FOLD);
        if (0 == $sRevolvingAccountRebate || empty($aRevolvingAccountRebateConfig)) {
            return [];
        }

        return [
            'multi_scene_text' => Language::replaceTag($aRevolvingAccountRebateConfig['text'], ['amount' => $sRevolvingAccountRebate]),
        ];
    }
}
