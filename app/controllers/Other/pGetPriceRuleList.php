<?php

use BizLib\Utils\Common as UtilsCommon;
/*
 * 计价规则中间页接口
 * wiki: http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=100904778
 * <AUTHOR>
 * @date 2017/07/02
 */

use BizLib\ExceptionHandler;
use BizLib\ErrCode\RespCode;
use PreSale\Logics\priceRule\GetPriceRuleListReq;
use PreSale\Logics\priceRule\BuildPriceRule;
use BizCommon\Models\Bill\Bill;
use BizLib\Utils\Horae;

/**
 * Class pGetPriceRule.
 *
 * @property CI_Input $input
 */
class pGetPriceRuleListController extends \PreSale\Core\Controller
{
    public function indexAction() {
        //初始化Request对象
        $oRequest = new GetPriceRuleListReq($_GET);
        try {
            //默认返回值
            $aResponseInfo = UtilsCommon::getErrMsg(RespCode::P_SUCCESS, $this->aErrnoMsg);

            //验证参数并返回需要的参数验证过程中产生的中间值
            list($sProductId, $aAreaInfo, $sPhone, $sPid) = $oRequest->verify();

            //拼装调用账单服务的参数
            $aParams = [
                'product_id' => $sProductId,
                'district'   => $aAreaInfo['district'],
                'role'       => 2, //推动在disf bill sdk里加此常量定义
                'lang'       => $oRequest->getLang(),
                'order_info' => [
                    'departure_time' => $oRequest->getDepartureTimeYmdHis(),
                    'trip_country'   => $aAreaInfo['canonical_country_code'] ?? '',
                ],
            ];

            //Abstract district
            if (!empty($aAreaInfo['countyid'])) {
                $aParams['abstract_district'] = $aAreaInfo['district'].','.$aAreaInfo['countyid'];
            }

            //场景参数
            $sSceneType = $oRequest->getSceneType();
            if (is_numeric($sSceneType)) {
                $aParams['combo_type'] = Horae::getComboType($oRequest->getSceneType());
            }

            //调用账单服务
            $oBill          = new Bill();
            $aPriceRuleList = $oBill->getStrategyList($aParams);

            //拼装返回值
            //$aResponseInfo['data'] = $aPriceRuleList;
            $aResponseInfo['data'] = (new BuildPriceRule())->addPriceRule($aPriceRuleList, $sProductId, $aAreaInfo, $sPhone, intval($aAreaInfo['countyid'] ?? 0), $sPid);
        } catch (\Exception $e) {
            //异常处理上下文
            $aContext = [
                'dltag'   => '_com_request_out_failure',
                'err_msg' => $this->aErrnoMsg,
            ];

            //异常处理并得到返回值
            $aResponseInfo = ExceptionHandler::getInstance()->handleException($e, $aContext);
        }

        //输出返回值
        $this->sendTextJson($aResponseInfo, $oRequest->getCallback());
    }
}
