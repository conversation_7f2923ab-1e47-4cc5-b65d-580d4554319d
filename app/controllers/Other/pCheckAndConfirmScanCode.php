<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * <AUTHOR> <<EMAIL>>
 * Date: 2020/3/24
 * Time: 11:14 AM
 */

use BizLib\Exception\ExceptionWithResp;
use BizLib\ErrCode\RespCode;
use BizLib\Error\ExceptionHandler;
use BizLib\Utils\PublicLog;
use PreSale\Logics\order\ScanCodeOrderLogic;
use BizLib\Utils\Common;
use BizLib\Constants\Horae;
use PreSale\Models\carrera\PassengerScanCodeTopic;

/**
 * Class PCheckAndConfirmScanCodeController
 */
class PCheckAndConfirmScanCodeController extends \PreSale\Core\Controller
{

    public $aParams = [];

    /**
     * 初始化
     * @return void
     */
    public function init() {
        parent::init();
    }

    /**
     * action
     * @return void
     */
    public function indexAction() {
        $iRequestStatus = 1;
        try {
            $aResponse = Common::getErrMsg(GLOBAL_SUCCESS);
            $this->_checkParams();
            $oScanCodeOrderLogic = ScanCodeOrderLogic::getInstance();

            $oScanCodeOrderLogic->loadInfos($this->aParams);
            $oScanCodeOrderLogic->checkScanNewOrder();
            $oScanCodeOrderLogic->lockDriver();
            $aResponse['data'] = $oScanCodeOrderLogic->getResponseParam();
            $aKafkaParams      = array_merge($aResponse,$oScanCodeOrderLogic->getInfos());
        } catch (\Exception $e) {
            $iRequestStatus = 0;
            $sMessage       = $e->getMessage();
            $aResponse      = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg,]);
        }

        $aPublicBaseData = [
            'opera_stat_key'    => 'g_p_check_and_confirm_scan_code',
            'request_status'    => $iRequestStatus,
            'response_info'     => json_encode($aResponse),
            'exception_message' => $sMessage ?? '',
        ];
        $aInsuranceLog   = array_merge($aPublicBaseData, ScanCodeOrderLogic::getInstance()->getInfos() ?? []);
        $this->sendJson($aResponse);
        fastcgi_finish_request();
        $this->_writeKafka($aKafkaParams);
        PublicLog::writeLogForOfflineCal('public', $aInsuranceLog);
    }

    /**
     * 参数校验
     * @return void
     * @throws ExceptionWithResp ExceptionWithResp.
     */
    private function _checkParams() {
        $this->aParams['scan_token'] = $this->oRequest->getStr('scan_token');
        $this->aParams['token']      = $this->oRequest->getStr('token');
        $iDriverId = $this->oRequest->getStr('driver_id');
        if (!empty($iDriverId)) {
            $this->aParams['driver_id'] = $iDriverId;
        }

        if ((empty($this->aParams['scan_token']) && empty($iDriverId)) || empty($this->aParams['token'])) {
            throw new ExceptionWithResp(
                RespCode::P_PARAMS_ERROR,
                (string)RespCode::P_PARAMS_ERROR,
                '',
                'errmsg:param error! scan_token:' . $this->aParams['scan_token'] . 'passenger_token:' .$this->aParams['token']
            );
        }

        $this->aParams['lng']     = $this->oRequest->getFloat('lng');
        $this->aParams['lat']     = $this->oRequest->getFloat('lat');
        $this->aParams['city_id'] = $this->oRequest->getInt('city_id');
        if (empty($this->aParams['lng']) || empty($this->aParams['lat']) || empty($this->aParams['city_id'])) {
            throw new ExceptionWithResp(
                RespCode::P_PARAMS_ERROR,
                (string)RespCode::P_PARAMS_ERROR,
                '',
                'errmsg:param error! lng:' .$this->aParams['lng']. 'lat:' . $this->aParams['lat'].'city_id'. $this->aParams['city_id']
            );
        }

        $this->aParams['page_type'] = $this->oRequest->getStr('page_type');
        if (Horae::PAGE_SCAN_CODE != $this->aParams['page_type']) {
            throw new ExceptionWithResp(
                RespCode::P_PARAMS_ERROR,
                (string)RespCode::P_PARAMS_ERROR,
                '',
                'errmsg:param error! page_type:' .  $this->aParams['page_type']
            );
        }
    }

    /**
     * @param array $aInfos kafka参数
     * @return mixed
     */
    private function _writeKafka($aInfos) {
        if (empty($aInfos)) {
            return;
        }

        $oTopic = new PassengerScanCodeTopic();
        $oTopic->sync($aInfos);
    }
}
