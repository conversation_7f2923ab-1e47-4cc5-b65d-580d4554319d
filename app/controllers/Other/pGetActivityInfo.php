<?php
use BizLib\Utils\Common;
use BizLib\Utils\Request;
use BizLib\Config as NuwaConfig;
use BizLib\ErrCode\Code;
use BizLib\ExceptionHandler;
use PreSale\Logics\passenger\PActivityLogic;
use BizLib\Exception\InvalidArgumentException;

class pGetActivityInfoController extends \PreSale\Core\Controller
{
    public function init() {
        parent::init();
    }

    public function indexAction() {
        try {
            $aRet           = Common::getErrMsg(GLOBAL_SUCCESS);
            $aRet['data']   = [];
            $aParams        = $this->_getInputParams();
            $oActivityLogic = new PActivityLogic($aParams);
            $oActivityLogic->checkParams();
            $aRet['data'] = $oActivityLogic->getTravelCard();
        } catch (\Exception $e) {
            $aErrMsg = NuwaConfig::text('errno', 'p_get_activity_info_error_msg');
            $aRet    = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $aErrMsg]);
        }

        $aRet['errno'] = (int)($aRet['errno']);
        $this->sendTextJson($aRet);
        return;
    }

    private function _getInputParams() {
        $oRequest         = Request::getInstance();
        $aParams['token'] = $oRequest->getStr('token');
        $aParams['lng']   = $oRequest->getStr('lng');
        $aParams['lat']   = $oRequest->getStr('lat');
        if (empty($aParams['token']) || !is_numeric($aParams['lng']) || !is_numeric($aParams['lat'])) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                array(
                    'token' => $aParams['token'],
                    'lng'   => $aParams['lng'],
                    'lat'   => $aParams['lat'],
                )
            );
        }

        return $aParams;
    }
}
