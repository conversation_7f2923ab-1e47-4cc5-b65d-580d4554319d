<?php

use PreSale\Core\Controller;
use BizCommon\Models\Passenger\Passenger;
use BizLib\Exception\ExceptionWithResp;
use BizLib\ErrCode\RespCode;
use BizLib\ErrCode\Code;
use BizLib\Exception\InvalidArgumentException;
use BizLib\Client\EstimateDecisionClient;
use BizLib\Utils\MapHelper;
use Xiaoju\Apollo\Apollo as NuwaApollo;
use BizLib\ExceptionHandler;
use BizLib\Utils\Language;
use Dirpc\SDK\PreSale\PageGuideResponse;
use BizLib\Utils\UtilHelper;
use BizLib\Log as NuwaLog;

/**
 * 获取页面导流信息
 */
class PPageGuideController extends Controller
{
    private $_aParams;

    /**
     * 入口
     * @return void
     */
    public function indexAction() {

        try {
            $this->_getAndCheckParams();
            $aGuideResult = EstimateDecisionClient::getInstance()->pageGuide($this->_asmReq());

            $aCurPageConf = $this->loadPageSelfInfo();

            $aResponse = [];
            if (!empty($aCurPageConf['bg_img'])) {
                $aResponse['bg_img'] = $aCurPageConf['bg_img'];
            }

            if (!empty($aCurPageConf['page_title'])) {
                $aResponse['page_title'] = $aCurPageConf['page_title'];
            }

            $aNewPageInfo = $this->loadGuideInfo($aGuideResult);
            if (empty($aNewPageInfo)) {
                $aResponse['no_guide'] = $aCurPageConf['no_guide'];
            } else {
                $aResponse['target'] = $aNewPageInfo;
            }

            $oResponse = new PageGuideResponse(['data' => $aResponse ]);
            $aResponse = $oResponse->jsonSerialize();
        } catch (\Exception $e) {
            $aResponse = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg,]);
        }

        $this->sendTextJson($aResponse);

        return;
    }

    /**
     * 获取并校验参数
     * @throws InvalidArgumentException 异常
     * @throws ExceptionWithResp 异常
     * @return void
    */
    private function _getAndCheckParams() {

        $sMenuID   = $this->oRequest->getStr('menu_id', '');
        $iPageType = $this->oRequest->getInt('page_type', 0);

        $sAppVersion  = $this->oRequest->getStr('app_version', '');
        $iAccessKeyId = $this->oRequest->getInt('access_key_id', 0);

        $fFromLat = $this->oRequest->getFloat('from_lat', '');
        $fFromLng = $this->oRequest->getFloat('from_lng', '');
        $fToLat   = $this->oRequest->getFloat('to_lat', '');
        $fToLng   = $this->oRequest->getFloat('to_lng', '');
        $sMapType = $this->oRequest->getStr('maptype');

        $sToken = $this->oRequest->fetchGetPost('token', '');

        if (empty($sMenuID) && empty($iPageType)) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                ['menu_id' => $sMenuID, 'page_type' => $iPageType]
            );
        }

        if (empty($fFromLat) || empty($fFromLng)  || empty($fToLat)  || empty($fToLng)) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                ['from_lat' => $fFromLat, 'from_lng' => $fFromLng, 'to_lat' => $fToLat, 'to_lng' => $fToLng]
            );
        }

        $aFromArea = MapHelper::getAreaInfoByLoc($fFromLng, $fFromLat);
        $aToArea   = MapHelper::getAreaInfoByLoc($fToLng, $fToLat);
        if (empty($sToken)) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                ['token' => '']
            );
        }

		// uid, pid, phone
        $sPassengerInfo = Passenger::getInstance()->getPassengerByTokenFromPassport($sToken);
        if (empty($sPassengerInfo['pid'])) {
            throw new ExceptionWithResp(
                Code::E_COMMON_TOKEN_INVALID,
                RespCode::P_PARAMS_ERROR
            );
        }

        $this->_aParams = [
            'passenger_info' => [
                'pid'   => $sPassengerInfo['pid'],
                'uid'   => $sPassengerInfo['uid'],
                'phone' => $sPassengerInfo['phone'],
            ],
            'app_version'    => $sAppVersion,
            'access_key_id'  => $iAccessKeyId,
            'menu_id'        => $sMenuID,
            'page_type'      => $iPageType,
            'from_lat'       => $fFromLat,
            'from_lng'       => $fFromLng,
            'to_lat'         => $fToLat,
            'to_lng'         => $fToLng,
            'map_type'       => $sMapType,
            'city'           => $aFromArea['id'],
            'to_city'        => $aToArea['id'],
        ];
    }

    /**
     * loadPageSelfInfo
     * 加载页面自身配置, 与导流到哪里去, 无关
     * @return array
     */
    protected function loadPageSelfInfo() {
        $aNoGuide = Language::getDecodedTextFromDcmp('config_carpool-page_guide_default_msg')['default'];

        $aApolloParam = [
            'city'          => $this->_aParams['city'],
            'pid'           => $this->_aParams['passenger_info']['pid'],
            'app_version'   => $this->_aParams['app_version'],
            'access_key_id' => $this->_aParams['access_key_id'],
            'menu_id'       => $this->_aParams['menu_id'] ?? '',
            'page_type'     => $this->_aParams['page_type'] ?? 0,
        ];
        $oApollo      = (NuwaApollo::getInstance())->featureToggle('carpool_dual_guide_page_conf', $aApolloParam);
        if ($oApollo->allow()) {
            $sNoGuide = $oApollo->getParameter('no_guide', '');
            if (!empty($sNoGuide)) {
                NuwaLog::debug('use no_guide from ab');
                $aNoGuide = json_decode($sNoGuide, true);
            }

            return [
                'bg_img'     => $oApollo->getParameter('bg_img', ''),
                'page_title' => $oApollo->getParameter('page_title', ''),
                'no_guide'   => $aNoGuide,
            ];
        }

        return ['no_guide' => $aNoGuide];
    }

    /**
     * loadGuideInfo
     * 加载目标页的配置
     * @param array $aGuideResult $aGuideResult
     * @return array|void
     */
    protected function loadGuideInfo($aGuideResult) {
        if (Code::E_SUCCESS != ($aGuideResult['errno'] ?? 1) || empty($aGuideResult['data']['to_page'])) {
            NuwaLog::debug('no target page to go');
            return null;
        }

        $aPage = $aGuideResult['data']['to_page'];

        $aApolloParam = [
            'city'          => $this->_aParams['city'],
            'pid'           => $this->_aParams['passenger_info']['pid'],
            'app_version'   => $this->_aParams['app_version'],
            'access_key_id' => $this->_aParams['access_key_id'],
            'menu_id'       => $aPage['menu_id'] ?? '',
            'page_type'     => $aPage['page_type'] ?? 0,
        ];

        $oApollo = (NuwaApollo::getInstance())->featureToggle('carpool_dual_guide_msg_conf', $aApolloParam);
        if (!$oApollo->allow()) {
            NuwaLog::debug('no target page conf');
            return null;
        }

        $sToLink         = $oApollo->getParameter('to_link', '');
        $bNeedToHomepage = !empty($oApollo->getParameter('go_home_page', '0'));
        if (!empty($oApollo->getParameter('need_param', ''))) {
            $param = [
                // 跳转链接, 跳转后自动发起新预估, 端需要经纬度
                'start_lat' => $this->_aParams['from_lat'],
                'start_lng' => $this->_aParams['from_lng'],
                'end_lat'   => $this->_aParams['to_lat'],
                'end_lng'   => $this->_aParams['to_lng'],
            ];

            if ($bNeedToHomepage) {
                // 控制跳转后, 点击 `返回` 按钮(端的后端键), 是否返回app首页
                $param['go_home_page'] = 'true';
            }

            if (!empty($aPage['page_type'])) {
                // 目标页面的pagetype
                $param['page_type'] = $aPage['page_type'];
            }

            if (!empty($oApollo->getParameter('menu_name', ''))) {
                // 目标页面标题
                $param['text'] = $oApollo->getParameter('menu_name', '');
            }

            $sToLink = UtilHelper::httpAppendQuery(
                $sToLink,
                $param
            );
        }

        $aProductTag = json_decode($oApollo->getParameter('product_tag', ''), true);

        return [
            'product_msg'  => $oApollo->getParameter('product_msg', ''),
            'product_desc' => $oApollo->getParameter('product_desc', ''),
            'product_tag'  => $aProductTag,
            'btn_content'  => $oApollo->getParameter('btn_content', ''),
            'menu_name'    => $oApollo->getParameter('menu_name', ''),
            'menu_id'      => $aPage['menu_id'],
            'page_type'    => $aPage['page_type'],
            'to_link'      => $sToLink,
        ];
    }

    /**
     * 组装请求
     * @return array
     */
    private function _asmReq() {
        return [
            'passenger_info' => [
                'pid'   => $this->_aParams['passenger_info']['pid'],
                'uid'   => $this->_aParams['passenger_info']['uid'],
                'phone' => $this->_aParams['passenger_info']['phone'],
            ],
            'client_info'    => [
                'app_version'   => $this->_aParams['app_version'],
                'access_key_id' => $this->_aParams['access_key_id'],
            ],
            'geo_info'       => [
                'from_lat' => $this->_aParams['from_lat'],
                'from_lng' => $this->_aParams['from_lng'],
                'to_lat'   => $this->_aParams['to_lat'],
                'to_lng'   => $this->_aParams['to_lng'],
                'map_type' => $this->_aParams['map_type'],
                'city'     => $this->_aParams['city'],
                'to_city'  => $this->_aParams['to_city'],
            ],
            'page_info'      => [
                'menu_id'   => $this->_aParams['menu_id'],
                'page_type' => $this->_aParams['page_type'],
            ],
        ];
    }
}
