<?php
use Biz<PERSON>ib\ErrCode\Code;
use Biz<PERSON>ib\ErrCode\RespCode;
use BizLib\Exception\ExceptionWithResp;
use BizLib\ExceptionHandler;
use PreSale\Logics\estimatePrice\DynamicPriceDetailLogic;
use BizLib\Utils\Language;
use BizLib\Config as NuwaConfig;

/**
 * @author: zhanghang
 * 获取预估页相关数据
 */
class PGetDynamicPriceDetailV2Controller extends \PreSale\Core\Controller
{

    /**
     * 获取数据
     *
     * @throws ExceptionWithResp ExceptionWithResp.
     * @return void
     */
    public function indexAction() {
        try {
            $aParams           = $this->_getAndCheckParams();
            $aDynamicPriceData = (new DynamicPriceDetailLogic($aParams))->getDynamicPriceDetail();

            $aResponse = [
                'errno'   => 0,
                'err_msg' => '',
                'data'    => $aDynamicPriceData,
            ];
        } catch (\Exception $e) {
            $aErrMsg   = NuwaConfig::text('errno', 'p_get_dynamic_h5_error_msg');
            $aResponse = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $aErrMsg]);
        }

        $this->sendTextJson($aResponse);

        return;
    }

    /**
     * @throws ExceptionWithResp 异常
     * @return array params
     */
    private function _getAndCheckParams() {
        //获取参数
        $estimateIDs = $this->oRequest->getStr('estimate_ids');
        $ISPartcar   = $this->oRequest->getInt('is_partcar');
        $sLanguage   = $this->oRequest->getStr('lang');
        $token       = $this->oRequest->getStr('token');
        $sFlng       = $this->oRequest->getFloat('lng');
        $sFlat       = $this->oRequest->getFloat('lat');

        $estimateIDs = array_filter(json_decode($estimateIDs, true));

        $aParams = [
            'estimate_ids' => $estimateIDs,
            'is_partcar'   => $ISPartcar,
            'lang'         => $sLanguage,
            'token'        => $token,
            'lng'          => $sFlng,
            'lat'          => $sFlat,
        ];

        //检查参数
        if (empty($estimateIDs) || empty($token)) {
            throw new ExceptionWithResp(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                RespCode::P_PARAMS_ERROR,
                '',
                'errmsg:param error! aParam_json:'.json_encode($aParams)
            );
        }

        return $aParams;
    }
}

