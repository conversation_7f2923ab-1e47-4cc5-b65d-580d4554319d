<?php

use BizLib\Utils\Common as UtilsCommon;
use BizLib\ExceptionHandler;
use BizLib\ErrCode\RespCode;
use BizLib\Utils\Language;
use PreSale\Core\Controller;
use PreSale\Logics\priceRule\SpaciousCarPriceRuleLogic;

/**
 * Class pGetSpaciousCarPriceRule  车大获取计价规则
 * http://wiki.intra.xiaojukeji.com/pages/editpage.action?pageId=692559502
 */
class PGetSpaciousCarPriceRuleController extends Controller
{

    /**
     * 根据品类和城市获取计价规则详情
     * @return void
     */
    public function indexAction() {
        $oSpaciousCarPriceRuleLogic = new SpaciousCarPriceRuleLogic($_GET);

        $aResponseInfo = UtilsCommon::pairErrNo(RespCode::P_SUCCESS, $this->aErrnoMsg);

        try {
            //验证参数
            $oSpaciousCarPriceRuleLogic->verify();

            // 页面配置 (DCMP中取 可灵活配置文案)
            $aPageConf = Language::getDecodedTextFromDcmp('config_text-custom_service_spacious_car_alliance');
            if (empty($aPageConf['price_rule_page']) || empty($aPageConf['price_rule_page']['items'])) {
                $this->sendTextJson($aResponseInfo, $oSpaciousCarPriceRuleLogic->getCallback());
                return;
            }

            $aPageConf = $aPageConf['price_rule_page'];

            $aContentList = array();
            foreach ($aPageConf['items'] as $aItem) {
                switch ($aItem['type']) {
                    case 2:
                        // type:2 计费规则
                        $aPriceRule = $oSpaciousCarPriceRuleLogic->getPriceRuleFromBill();
                        if (!empty($aPriceRule)) {
                            $aItem['object'] = $aPriceRule;
                            $aContentList[]  = $aItem;
                        }
                        break;
                    case 3:
                        // type:3 优惠详情
                        $aDiscountDetail = $oSpaciousCarPriceRuleLogic->getDiscountDetailByCity($aItem);
                        if (!empty($aDiscountDetail)) {
                            $aContentList[] = $aDiscountDetail;
                        }
                        break;
                    default:
                        // type:1 普通文案+list展示
                        $aContentList[] = $aItem;
                        break;
                }
            }

            $aPageConf['items'] = $aContentList;

            $aResponseInfo['data'] = $aPageConf;
        } catch (\Exception $e) {
            $aContext = [
                'dltag'   => '_com_request_out_failure',
                'err_msg' => $this->aErrnoMsg,
            ];

            //异常处理并得到返回值
            $aResponseInfo = ExceptionHandler::getInstance()->handleException($e, $aContext);
        }

        $this->sendTextJson($aResponseInfo, $oSpaciousCarPriceRuleLogic->getCallback());
    }
}
