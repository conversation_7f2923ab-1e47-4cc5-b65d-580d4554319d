<?php

use PreSale\Core\Controller;
use BizLib\ExceptionHandler;
use PreSale\Dto\GetIndexInfoRequest;
use PreSale\Application\Service\GetIndexInfoService;
use BizLib\Config as NuwaConfig;

/**
 *
 * Copyraight (c) 2019 xiaojukeji.com, Inc. All Rights Reserved.
 * @author: wangtuan<PERSON><EMAIL>
 * @date: 2019-12-23 14:12
 * @desc: 霸王花，用户进入首页时，获取首页上的顶导、地图车标、情感沟通、运营红包区域banner信息
 * @wiki:
 *
 */

class PGetIndexInfoController extends Controller
{
    /**
     * 构建函数
     * @return void
     */
    public function init() {
        parent::init();
    }

    /**
     * 入口函数
     * @return void
     */
    public function indexAction() {
        try {
            $oRequest = new GetIndexInfoRequest();
            $oRequest->init();

            $oService  = new GetIndexInfoService();
            $oResponse = $oService->build($oRequest);

            $this->echoSuccessResponseWithJson($oResponse);
        } catch (Exception $oException) {
            $aErrMsg = NuwaConfig::text('errno', 'pgetindexinfo_errno_msg');
            $aRet    = ExceptionHandler::getInstance()->handleException($oException, ['err_msg' => $aErrMsg]);
            $this->sendJson($aRet);
        }

        return;
    }
}

