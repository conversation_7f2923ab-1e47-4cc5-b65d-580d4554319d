<?php

use B<PERSON><PERSON>ib\Client\AthenaApiClient;
use B<PERSON><PERSON>ib\Utils\MapHelper;
use <PERSON>izCommon\Models\passenger\Passenger;
/*
 * 请求Athena,返回导流信息。
 * <AUTHOR>
 * @date: 17/5/23
 */

use PreSale\Logics\estimatePrice\ParamsLogic;
use BizLib\Exception\InvalidArgumentException;
use BizLib\ExceptionHandler;
use BizLib\ErrCode;
use BizLib\Utils\Request;

class pQueryAthenaController extends \PreSale\Core\Controller
{
    /** @var $athena AthenaApiClient */
    public $athena;
    /** @var $passenger Passenger */
    public $passenger;
    private $_aIgnoreRet = array('errno' => 0,);

    /**
     * 乘客发单后一段时间没人应答，请求该接口，引导用户发其他订单.
     */
    public function guideInfoAction() {
        try {
            $req = Request::getInstance();
            //参数处理实例
            $oParams = ParamsLogic::getInstance();

            //获取预估参数
            $aEstimatePriceParams = $oParams->getBillParams((array) $req->get());

            if ($aEstimatePriceParams['passenger_info']['pid'] <= 0) {
                throw new InvalidArgumentException(
                    ErrCode\Code::E_COMMON_PARAM_INVALID_VALUE,
                    array('token' => $req->get('token'), )
                );
            }

            $sPdTag   = $req->getStr('pd_tag');
            $sOrderId = $req->getStr('oid');

            $sAthenaParams = $req->get('athena_params');
            $aAthenaParams = json_decode($sAthenaParams, true);
            if (!is_array($aAthenaParams)) {
                $aAthenaParams = array();
            }

            //获取导流需要参数
            $aAthenaRequest = $oParams->getAthenaParams($aAthenaParams, $aEstimatePriceParams);
            if (isset($aAthenaRequest['athena_bubbe_params']) && is_array($aAthenaRequest['athena_bubbe_params'])) {
                $aAthenaRequest['athena_guide_params'] = array_merge(
                    $aAthenaRequest['athena_bubbe_params'],
                    array(
                        'pd_tag'   => $sPdTag,
                        'order_id' => $sOrderId,
                    )
                );
            }

            $this->athena    = new AthenaApiClient();
            $aAthenaResponse = $this->athena->getGuideInfo(
                $aAthenaRequest['price_params'],
                $aAthenaRequest['athena_guide_params']
            );
            if (GLOBAL_SUCCESS != $aAthenaResponse['errno']) {
                throw new InvalidArgumentException(
                    ErrCode\Code::E_ATHENA_REQUEST_FAIL,
                    array('athena_response' => $aAthenaResponse, )
                );
            }
        } catch (\Exception $e) {
            $oHandler        = ExceptionHandler::getInstance();
            $aAthenaResponse = $oHandler->handleException($e, ['err_msg' => $this->aErrnoMsg, ]);
        }

        $this->sendJson($aAthenaResponse, $req->getStr('callback'));
    }

    /*
     * h5导流展示回调
     */
    public function affirmAction() {
        try {
            $req    = Request::getInstance();
            $sToken = $req->getStr('token');
            $this->passenger = new Passenger();
            $aPassengerInfo  = $this->passenger->getPassengerByTokenFromPassport($sToken);
            if ($aPassengerInfo['pid'] <= 0) {
                throw new InvalidArgumentException(
                    ErrCode\Code::E_COMMON_PARAM_INVALID_VALUE,
                    array('token' => $sToken, )
                );
            }

            $iArea     = $req->getInt('area');
            $aAreaInfo = MapHelper::getAreaInfoByAreaId($iArea);

            $sExtraInfo = $req->get('extra_info');
            $aExtraInfo = json_decode($sExtraInfo);
            if (!is_array($aExtraInfo)) {
                $aExtraInfo = array();
            }

            $aAthenaAffirmParams['pid']    = (string) $aPassengerInfo['pid'];
            $aAthenaAffirmParams['phone']  = (string) $aPassengerInfo['phone'];
            $aAthenaAffirmParams['pd_tag'] = $req->getStr('pd_tag');
            $aAthenaAffirmParams['source_business'] = $req->getInt('source_business');
            $aAthenaAffirmParams['guide_business']  = $req->getInt('guide_business');
            $aAthenaAffirmParams['guide_scence']    = $req->getInt('guide_scence');
            $aAthenaAffirmParams['area']            = $req->getInt('area');
            $aAthenaAffirmParams['client_type']     = $req->getStr('client_type');
            $aAthenaAffirmParams['session_id']      = $req->getStr('session_id');
            $aAthenaAffirmParams['extra_info']      = $aExtraInfo;

            $this->athena    = new AthenaApiClient();
            $aAthenaResponse = $this->athena->affirm($aAthenaAffirmParams);
            if (GLOBAL_SUCCESS != $aAthenaResponse['errno']) {
                throw new InvalidArgumentException(
                    ErrCode\Code::E_ATHENA_REQUEST_FAIL,
                    array('athena_response' => $aAthenaResponse, )
                );
            }
        } catch (\Exception $e) {
            $oHandler        = ExceptionHandler::getInstance();
            $aAthenaResponse = $oHandler->handleException($e, $this->_aErrMsg);
        }

        $this->sendJson($aAthenaResponse, $req->getStr('callback'));
    }

    /*
     * 获取乘客导流tag
     */
    public function guideSwitchAction() {
        try {
            $req    = Request::getInstance();
            $sToken = $req->getStr('token');
            $this->passenger = new Passenger();
            $aPassengerInfo  = $this->passenger->getPassengerByTokenFromPassport($sToken);
            if ($aPassengerInfo['pid'] <= 0) {
                throw new InvalidArgumentException(
                    ErrCode\Code::E_COMMON_PARAM_INVALID_VALUE,
                    array('token' => $sToken, )
                );
            }

            $iArea      = $req->getInt('area');
            $aAreaInfo  = MapHelper::getAreaInfoByAreaId($iArea);
            $sExtraInfo = $req->get('extra_info');
            $aExtraInfo = json_decode($sExtraInfo);
            if (!is_array($aExtraInfo)) {
                $aExtraInfo = array();
            }

            $aAthenaParams['pid']          = (string) $aPassengerInfo['pid'];
            $aAthenaParams['phone']        = (string) $aPassengerInfo['phone'];
            $aAthenaParams['area']         = $req->getInt('area');
            $aAthenaParams['map_type']     = $req->getStr('map_type');
            $aAthenaParams['lng']          = $req->getFloat('lng');
            $aAthenaParams['lat']          = $req->getFloat('lat');
            $aAthenaParams['business_ids'] = (array) explode('|', $req->getStr('business_ids'));
            $aAthenaParams['client_type']  = $req->getStr('client_type');
            $aAthenaParams['extra_info']   = $aExtraInfo;

            $this->athena    = new AthenaApiClient();
            $aAthenaResponse = $this->athena->getGuideSwitch($aAthenaParams);
            if (GLOBAL_SUCCESS != $aAthenaResponse['errno']) {
                throw new InvalidArgumentException(
                    ErrCode\Code::E_ATHENA_REQUEST_FAIL,
                    array('athena_response' => $aAthenaResponse, )
                );
            }
        } catch (\Exception $e) {
            $oHandler        = ExceptionHandler::getInstance();
            $aAthenaResponse = $oHandler->handleException($e, $this->_aErrMsg);
        }

        $this->sendJson($aAthenaResponse, $req->getStr('callback'));
    }
}
