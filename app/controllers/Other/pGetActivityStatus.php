<?php

use \PreSale\Core\Controller;
use BizLib\Utils\Common;
use BizLib\Utils\Request;
use BizLib\Config as NuwaConfig;
use BizLib\ErrCode\Code;
use BizLib\ExceptionHandler;
use PreSale\Logics\passenger\PActivityCardLogic;
use BizLib\Exception\InvalidArgumentException;

/**
 * @authors liangdongxu (<EMAIL>)
 * @date    2020-10-26 16:11:06
 * @desc    是否可以砍价
 */
class PGetActivityStatusController extends Controller
{
    /**
     * @desc desc
     * @return void
     * @throws \Exception exception
     */
    public function indexAction() {
        try {
            $aParams        = $this->_getInputParams();
            $aRet           = Common::getErrMsg(GLOBAL_SUCCESS);
            $aRet['data']   = [];
            $oActivityLogic = new PActivityCardLogic($aParams);
            $oActivityLogic->checkParams();
            $aRet['data'] = $oActivityLogic->getActivityStatus();
        } catch (\Exception $e) {
            $aErrMsg = NuwaConfig::text('errno', 'p_get_activity_status_error_msg');
            $aRet    = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $aErrMsg]);
        }

        $aRet['errno'] = (int)($aRet['errno']);
        $this->sendTextJson($aRet);
    }

    /**
     * @desc 参数校验
     * @return array 参数
     * @throws InvalidArgumentException <comment for threw>
     */
    private function _getInputParams() {
        $oRequest    = Request::getInstance();
        $estimateIds = $oRequest->getStr('estimate_ids');
        $aParams['estimate_ids']  = json_decode($estimateIds, true);
        $aParams['app_version']   = $oRequest->getStr('app_version');
        $aParams['token']         = $oRequest->getStr('token');
        $aParams['channel']       = $oRequest->getInt('channel');
        $aParams['access_key_id'] = $oRequest->getInt('access_key_id');
        $aParams['lang']          = $oRequest->getStr('lang');
        $aParams['lng']           = $oRequest->getFloat('lng');
        $aParams['lat']           = $oRequest->getFloat('lat');

        if (empty($aParams['token']) || empty($aParams['estimate_ids']) || !is_numeric($aParams['channel'])
            || !is_numeric($aParams['access_key_id']) || empty($aParams['lang']) || !is_numeric($aParams['lng']) || !is_numeric($aParams['lat'])
        ) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                array(
                    'token'         => $aParams['token'],
                    'estimate_ids'  => $aParams['estimate_ids'],
                    'channel'       => $aParams['channel'],
                    'access_key_id' => $aParams['access_key_id'],
                    'lang'          => $aParams['lang'],
                    'lng'           => $aParams['lng'],
                    'lat'           => $aParams['lat'],
                )
            );
        }

        return $aParams;
    }
}
