<?php
use BizLib\ErrCode\RespCode;
use BizLib\Exception\ExceptionWithResp;
use BizLib\ExceptionHandler;
use \BizCommon\Models\Cache\DynamicPrice as DynamicPriceCache;
use BizLib\Utils\Language;
use BizLib\Libraries\RedisDB;

/**
 * @author: zhanghang
 * 获取预估页相关数据
 */
class PGetDynamicPriceDetailController extends \PreSale\Core\Controller
{

    /**
     * 获取数据
     *
     * @throws ExceptionWithResp ExceptionWithResp.
     * @return void
     */
    public function indexAction() {
        try {
            // 获取参数
            $sEstimateID = $this->oRequest->getStr('estimate_id');
            $sLanguage   = $this->oRequest->getStr('lang');

            // 检查参数
            if (empty($sEstimateID)) {
                throw new ExceptionWithResp(
                    RespCode::P_PARAMS_ERROR,
                    (string) RespCode::P_PARAMS_ERROR
                );
            }

            $sKey   = \BizLib\Utils\Common::getRedisPrefix(P_ESTIMATE_DYNAMIC_PRICE_PAGE_DATA) . $sEstimateID;
            $sCache = \PreSale\Logics\redismove\RedisWrapper::getInstance(P_ESTIMATE_DYNAMIC_PRICE_PAGE_DATA)->get($sKey);
            if ($sCache === null) { // 链接失败
                throw new ExceptionWithResp(
                    RespCode::P_PARAMS_ERROR,
                    (string) RespCode::P_PARAMS_ERROR
                );
            } elseif (false === $sCache) { // 数据为空
                throw new ExceptionWithResp(
                    RespCode::P_DYNAMIC_PRICE_ERROR,
                    (string) RespCode::P_DYNAMIC_PRICE_ERROR
                );
            }

            $aDynamicPriceData = json_decode($sCache, true);
            if (!$aDynamicPriceData) {
                throw new ExceptionWithResp(
                    RespCode::P_PARAMS_ERROR,
                    (string) RespCode::P_PARAMS_ERROR
                );
            }

            $aResponse = [
                'errno' => 0,
                'data'  => $aDynamicPriceData,
            ];
        } catch (\Exception $e) {
            $aResponse = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg,]);
        }

        $this->sendTextJson($aResponse);

        return;
    }
}

