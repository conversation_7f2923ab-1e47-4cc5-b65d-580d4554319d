<?php
use PreSale\Logics\estimatePrice\FeeDetailLogic;
use BizLib\ErrCode\RespCode;
use BizLib\Exception\ExceptionWithResp;
use BizLib\ExceptionHandler;
use PreSale\Logics\order\AnyCarOrderLogic;
use BizCommon\Logics\Order\OrderComLogic;
use BizLib\Constants\OrderSystem;
use BizLib\Utils\Language;

/**
 * @author: zhanghang
 * anycar预估费用头部
 */
class PGetEstimateFeeDetailForAnycarController extends \PreSale\Core\Controller
{

    /**
     * 获取数据
     *
     * @throws ExceptionWithResp ExceptionWithResp.
     * @return void
     */
    public function indexAction() {
        try {
            // 获取参数
            $sEstimateID       = $this->oRequest->getStr('estimate_id');
            $sLanguage         = $this->oRequest->getStr('lang');
            $iSelectCategoryId = $this->oRequest->getInt('category_id', 1);

            // 检查参数
            if (empty($sEstimateID)) {
                throw new ExceptionWithResp(
                    RespCode::P_PARAMS_ERROR,
                    (string) RespCode::P_PARAMS_ERROR
                );
            }

            // 获取分组信息
            $aCategory = $this->_getCategory($iSelectCategoryId);
            if (empty($aCategory)) {
                throw new ExceptionWithResp(
                    RespCode::P_PARAMS_ERROR,
                    (string) RespCode::P_PARAMS_ERROR
                );
            }

            // 获取报价单数据
            $oFeeDetailLogic = FeeDetailLogic::getInstance();
            $aRet            = $oFeeDetailLogic->getFeeDetailData($sEstimateID);
            if (empty($aRet['discount_desc_anycar'])) {
                throw new ExceptionWithResp(
                    RespCode::P_PARAMS_ERROR,
                    (string) RespCode::P_PARAMS_ERROR
                );
            }

            $aDoubleRowConf = json_decode(Language::getTextFromDcmp('config_anycar-double_row_conf', []), true);
            $aMultiInfo     = $aRet['discount_desc_anycar'];
            $sAnyCarType    = AnyCarOrderLogic::getInstance()->getAnyCarType($aMultiInfo);
            $aProductList   = [];
            foreach ($aRet['discount_desc_anycar'] as $aSubProduct) {
                // 出租车没有计价详情页，过滤
                if (AnyCarOrderLogic::PRODUCT_KEY_UNIONE == $aSubProduct['group_key']) {
                    continue;
                }

                $iCategoryId = $aDoubleRowConf[$aSubProduct['group_key']]['category_id'] ?? 1; // 未配置则默认是分组1
                if ($iCategoryId != $iSelectCategoryId) {
                    continue;
                }

                $sDisplayName = OrderComLogic::getProductDisplayNameByCarLevel(
                    $aSubProduct['require_level'],
                    $aSubProduct['combo_type'],
                    OrderSystem::TYPE_ANYCAR_FAST == $sAnyCarType
                );

                if (!empty($aSubProduct['level_type'])) {
                    $aBusinessNameConfig = Language::getDecodedTextFromDcmp('config_anycar-business_name_text');
                    $sDisplayName        = $aBusinessNameConfig[$aSubProduct['group_key']];
                }

                $aProductList[] = [
                    'name'        => $sDisplayName,
                    'group_key'   => $aSubProduct['group_key'],
                    'estimate_id' => $aSubProduct['estimate_id'],
                ];
            }

            $aResponse = [
                'errno' => 0,
                'data'  => [
                    'category_name'      => $aCategory['name'],
                    'category_desc'      => $aCategory['desc'],
                    'category_backgroud' => $aCategory['banner'],
                    'category_color'     => $aCategory['banner_color'],
                    'product_list'       => $aProductList,
                ],
            ];
        } catch (\Exception $e) {
            $aResponse = ExceptionHandler::getInstance()->handleException($e, ['err_msg' => $this->aErrnoMsg,]);
        }

        $this->sendTextJson($aResponse);

        return;
    }

    /**
     * 获取分组信息
     *
     * @param int $iCategoryId 分组id
     * @return array
     */
    private function _getCategory($iCategoryId) {
        $aCategoryList = json_decode(Language::getTextFromDcmp('config_anycar-double_row_category_list', []), true);
        return $aCategoryList[$iCategoryId] ?? [];
    }
}

