<?php

/**
 * 注册异常和错误 Handler
 */

namespace PreSale\Bootstrap;

use Nuwa\Contracts\Bootstrap\BootstrapInterface;
use PreSale\Core\ExceptionHandler;

class HandleException implements BootstrapInterface
{
    /**
     * @param \Nuwa\Core\Application $app
     */
    public function bootstrap($app) {
        error_reporting(E_COMPILE_ERROR | E_ERROR | E_PARSE);

        $aOption = [
            'registerErrorHandle'     => true,
            'registerExceptionHandle' => true,

            //
            //  only use when registerErrorHandle = true
            //
            //  fatalError2Exception turn fatal error to exception(at register shutdown) or will be a fatal
            'errorLevel'              => E_ALL | E_STRICT, //  same to ci
            'fatalError2Exception'    => false,

            //
            //  only use when registerExceptionHandle = true
            //
            //  php7Error2Exception only work for PHP7, turn php7error to exception or will be a fatal
            'php7Error2Exception'     => false,
        ];

        ExceptionHandler::getInstance($aOption)->register();
    }
}
