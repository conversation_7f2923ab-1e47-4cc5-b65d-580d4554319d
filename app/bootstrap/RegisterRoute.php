<?php
/**
 * 注册路由
 */

namespace PreSale\Bootstrap;

use Nuwa\Contracts\Bootstrap\BootstrapInterface;
use Nuwa\Bridge\CI\RouterBridge;

class RegisterRoute implements BootstrapInterface
{
    /**
     * @param \Nuwa\Core\Application $app
     */
    public function bootstrap($app) {
        //
        //  for project from ci
        //
        //  copy config from ci-project/config/routes.php
//        $route = [];
//        $route['gulfstream/api/v1/(.+)'] = "$1";
        $route['gulfstream/passenger/v2/(.+)'] = '$1';
        $route['gulfstream/passenger/v1/(.+)'] = '$1';
        $route['gulfstream/pre-sale/v1/(.+)']  = '$1';
//        $route['gulfstream/driver/v2/(.+)'] = "$1";
//        $route['gulfstream/hermesapi/v1/(.+)'] = "$1";
        $router = $app->getDispatcher()->getRouter();
        $router->addRoute('ci_style_route', new RouterBridge($route));
    }
}
