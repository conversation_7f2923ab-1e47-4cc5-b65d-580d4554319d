<?php
/**
 * 注册ORM组件
 */

namespace PreSale\Bootstrap;

use Nuwa\Contracts\Bootstrap\BootstrapInterface;
use Nuwa\Core\Application;
use Nuwa\Database\Capsule\Capsule;
use Disf\SPL\Scheduler\Svc;
use BizLib\Config;

class RegisterORM implements BootstrapInterface
{
    /**
     * @param \Nuwa\Core\Application $app
     */
    public function bootstrap($app) {
        Application::bind(
            'ORM',
            function () {
                $config = Config::config('database');
                if (empty($config)) {
                    return null;
                }

                $capsule = new Capsule();
                foreach ($config as $key => $value) {
                    Svc::discoverEndpoint(
                        $value['disfServName'],
                        function ($endpoint) use (&$value) {
                            $value['db']['host'] = $endpoint['ip'];
                            $value['db']['port'] = (string)($endpoint['port']);
                        }
                    );
                    $capsule->addConnection($value['db'], $key);
                }

                $capsule->setAsGlobal();
                $capsule->bootEloquent();
                return $capsule;
            }
        );
    }
}
