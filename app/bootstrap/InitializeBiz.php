<?php

/**
 * 业务初始化.
 *
 * 老 CI 项目里 index.php 中初始化代码 迁移到此
 * 初始化 BizLib、BizCommon、BizConfig、服务发现
 */

namespace PreSale\Bootstrap;

use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Log;
use Nuwa\Contracts\Bootstrap\BootstrapInterface;
use Dirpc\SDK\Dcmp;

class InitializeBiz implements BootstrapInterface
{
    /**
     * @param \Nuwa\Core\Application $app
     */
    public function bootstrap($app) {
        $this->adaptCI();

        $status = \Disf\SPL\Introspection\Introspection::setupFromDir(
            PROJ_PATH,
            [],
            [
                'metric' => true,
                'trace'  => ['log_path' => LOG_PATH,],
            ]
        );

        if (true !== $status) {
            Log::fatal(Msg::formatArray(Code::E_COMMON_DISF_NOT_AVAIABLE));
        }

        // dirpc log init
        $logger = \Lego\Log\Logger::getLogger(
            LOG_PATH,
            \Lego\Log\LogLevel::INFO,
            array('warnFile' => true, 'prefix' => '', 'filename' => 'dirpc.log','enableMasking' => true,)
        );
        \Lego\Dirpc::setup($logger, PROJ_PATH, array());
        Dcmp\DcmpClient::Init(PROJ_PATH, 'pre-sale');
        \PlatformHA\Degrade\Degrade::setup(PROJ_PATH);
    }

    /**
     * 适配 CI 代码
     *
     * BizLib、BizCommon、BizConfig 部分代码依赖 CI
     *
     * 可以将这部分代码改造成脱离 CI，此函数的目的是适配那些未被改造的代码
     *
     * load_class
     * config_item
     */
    public function adaptCI() {
        //  BASEPATH
        $system_path = '/home/<USER>/webroot/gulfstream/application/system';
        if (false !== realpath($system_path)) {
            $system_path = realpath($system_path).'/';
        }

        $system_path = rtrim($system_path, '/').'/';
        define('BASEPATH', str_replace('\\', '/', $system_path));

        return;

        //require_once(BIZ_LIB_CIPATH.'Init.php');
        //  override some CI code
        //  log
        require_once(BIZ_LIB_CIPATH.'helpers/log_helper.php');
    }
}
