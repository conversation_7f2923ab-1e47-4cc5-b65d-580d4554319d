<?php
/**
 * 注册日志组件.
 */

namespace PreSale\Bootstrap;

use Nuwa\Contracts\Bootstrap\BootstrapInterface;
use BizLib\Log;
use Nuwa\Log\Exception\InvalidArgumentException;
use Nuwa\Log\Formatter\BaMaiFormatter;
use Nuwa\Log\Masking\Masking;
use Nuwa\Log\Writer\FileWriter;
use Nuwa\Log\Processor\PsrContextProcessor;
use Nuwa\Log\Processor\BacktraceProcessor;

class RegisterLogger implements BootstrapInterface
{
    /**
     * @param \Nuwa\Core\Application $app
     */
    public function bootstrap($app) {
        $config     = $app->getConfig();
        $logOptions = $config['log'];

        try {
            $writers   = [];
            $formatter = new BaMaiFormatter();
            foreach ($logOptions as $logOption) {
                if (!isset($logOption['file'])) {
                    throw new InvalidArgumentException('Log init options need log file.');
                }

                $opt       = [
                    'writeLevels'   => $logOption['writeLevels'],
                    'formatter'     => $formatter,
                    'enableMasking' => $logOption['enableMasking'],
                ];
                $writers[] = new FileWriter($logOption['file'], $opt);
            }

            $processors   = [];
            $processors[] = new PsrContextProcessor();
            $processors[] = new BacktraceProcessor(['BizLib\\Log', 'TripcloudCommon\\ErrCode\\CodeLog', 'BizLib\\Utils\\LogHelper']);

            // 初始化日志脱敏组件
            Masking::SetToggleName('gs_log_desensitization_dynamic_degrade');
            Masking::Init();

            Log::setFormatter($formatter);
            Log::initLogger($writers, $processors, Log::REPLACE_LEVEL_NAME);
        } catch (\Exception $e) {
            trigger_error('Sth error when init nuwa log: '.$e->getMessage(), E_USER_WARNING);
        }
    }
}
