<?php
/**
 * Copyright (c) 2019 xiaojukeji.com, Inc. All Rights Reserved.
 * <AUTHOR> <<EMAIL>>
 * @date   2019/8/6 下午4:35
 * @desc   代叫人信息校验-组装
 */

namespace PreSale\Application\Assembler;

use PreSale\Dto\CheckRiderInfoResponse;

/**
 * Class CheckRiderInfoAssembler
 * @package PreSale\Application\Assembler
 */
class CheckRiderInfoAssembler
{
    /**
     * @param array $ret ret
     * @return CheckRiderInfoResponse
     */
    public static function assembleNewOrderResponse($ret): CheckRiderInfoResponse {
        $response = new CheckRiderInfoResponse();

        $response->errno  = $ret['errno'] ?? GLOBAL_SUCCESS;
        $response->errmsg = $ret['errmsg'] ?? 'SUCCESS';

        unset($ret['errno'], $ret['errmsg']);

        $response->aResponseData = $ret;

        return $response;
    }
}
