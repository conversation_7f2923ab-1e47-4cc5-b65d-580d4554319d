<?php
/**
 * Copyright (c) 2019 xiaojukeji.com, Inc. All Rights Reserved.
 * @Author: xusheng<PERSON>@didiglobal.com
 * @Date  : 2019/4/26 下午4:35
 * @Desc  : 预约能力校验-组装
 */

namespace PreSale\Application\Assembler;

use PreSale\Dto\CheckBookingAbilityResponse;

class CheckBookingAbilityAssembler
{
    public static function assembleNewOrderResponse($ret): CheckBookingAbilityResponse {
        $response = new CheckBookingAbilityResponse();

        $response->errno  = $ret['errno'] ?? GLOBAL_SUCCESS;
        $response->errmsg = $ret['errmsg'] ?? 'SUCCESS';

        unset($ret['errno'], $ret['errmsg']);

        $response->aResponseData = $ret;

        return $response;
    }
}
