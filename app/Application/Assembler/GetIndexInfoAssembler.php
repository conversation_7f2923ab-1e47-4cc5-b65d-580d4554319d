<?php

namespace PreSale\Application\Assembler;

use PreSale\Dto\GetIndexInfoResponse;

/**
 *
 * Copyraight (c) 2019 xiaojukeji.com, Inc. All Rights Reserved.
 * @author: wang<PERSON><PERSON><PERSON><PERSON>@didichuxing.com
 * @date: 2019-12-26 22:57
 * @desc: 组装首页信息接口返回值
 * @wiki:
 *
 */

class GetIndexInfoAssembler
{
    public $aParams = array();

    /**
     * @param array $aRet 返回值
     * @return GetIndexInfoResponse
     */
    public static function assembleIndexInfoResponse($aRet): GetIndexInfoResponse {
        $oResponse = new GetIndexInfoResponse();

        $oResponse->errno  = $aRet['errno'] ?? GLOBAL_SUCCESS;
        $oResponse->errmsg = $aRet['errmsg'] ?? 'SUCCESS';
        $oResponse->aResponseData['data'] = $aRet['data'] ?? $aRet;

        unset($aRet['errno'], $aRet['errmsg'], $aRet['data']);

        return $oResponse;
    }
}

