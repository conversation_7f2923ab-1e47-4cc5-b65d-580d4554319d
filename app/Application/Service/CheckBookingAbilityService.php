<?php
/**
 * Copyright (c) 2019 xiaojukeji.com, Inc. All Rights Reserved.
 * @Author: <EMAIL>
 * @Date  : 2019/4/26 下午3:44
 * @Desc  : 校验预约能力
 */

namespace PreSale\Application\Service;

use PreSale\Application\Assembler\CheckBookingAbilityAssembler;
use PreSale\Domain\Service\BookingAbilityChecker\MainChecker;
use PreSale\Dto\CheckBookingAbilityRequest;
use PreSale\Dto\CheckBookingAbilityResponse;
use PreSale\Domain\Service\Idl\HandleResult;

class CheckBookingAbilityService
{
    /**
     * 执行逻辑
     *
     * @param CheckBookingAbilityRequest $oRequest
     * @return CheckBookingAbilityResponse
     * @throws \PreSale\Exception\BaseException
     */
    public function execute(CheckBookingAbilityRequest $oRequest): CheckBookingAbilityResponse {
        $oChecker      = new MainChecker($oRequest);
        $oHandleResult = $oChecker->execute();

        $outMsg = $this->_getResponse($oRequest, $oHandleResult);
        return CheckBookingAbilityAssembler::assembleNewOrderResponse($outMsg);
    }

    /**
     * 组装返回值
     * @param CheckBookingAbilityRequest $oRequest
     * @param HandleResult                    $oHandleResult
     * @return mixed
     */
    private function _getResponse(CheckBookingAbilityRequest $oRequest, HandleResult $oHandleResult) {
        $aOutErrMsg['errno']      = 0;
        $aOutErrMsg['createTime'] = time();
        $aOutErrMsg['data']       = $oHandleResult->aAppendData;

        return $aOutErrMsg;
    }
}
