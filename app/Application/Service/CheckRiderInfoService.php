<?php
/**
 * Copyright (c) 2019 xiaojukeji.com, Inc. All Rights Reserved.
 * <AUTHOR> <<EMAIL>>
 * @date   2019/4/26 下午3:44
 * @desc   校验预约能力
 */

namespace PreSale\Application\Service;

use PreSale\Application\Assembler\CheckRiderInfoAssembler;
use PreSale\Domain\Service\BookingAbilityChecker\MainChecker;
use PreSale\Dto\CheckRiderInfoRequest;
use PreSale\Dto\CheckRiderInfoResponse;
use PreSale\Domain\Service\Idl\HandleResult;
use BizLib\Client\KronosClient;
use BizLib\Constants\Kronos as ConstKronos;
use BizLib\Config as NuwaConfig;
use BizLib\Client\PassportClient;
use Nuwa\ApolloSDK\Apollo;
use BizLib\Client\FeatureServiceClient;

/**
 * Class CheckRiderInfoService
 * @package PreSale\Application\Service
 */
class CheckRiderInfoService
{
    const MINORS_REQUIRE_NAME = 'yTh5Ky';
    const MINORS_TOKEN        = 'a726262066284e2ebaaab68f97d68acd';
    const MINORS_FEATURE_ID   = '106429';
    /**
     * 执行逻辑
     *
     * @param CheckRiderInfoRequest $oRequest request
     * @return CheckRiderInfoResponse
     */
    public function execute(CheckRiderInfoRequest $oRequest): CheckRiderInfoResponse {
        $aMsgOut = [
            'errno'  => 0,
            'errmsg' => 'success',
            'data'   => [],
        ];
        if ($this->_isUnderAge($oRequest)) {
            $aMessage          = NuwaConfig::text('config_check_rider_info', 'forbidden_not_adult');
            $aMsgOut['errno']  = '10001';
            $aMsgOut['errmsg'] = '未成年人拦截';
            $aMsgOut['data']   = $aMessage;
        }

        return CheckRiderInfoAssembler::assembleNewOrderResponse($aMsgOut);
    }

    /**
     * 验证代叫人是否是未成年人
     * @param CheckRiderInfoRequest $oRequest request
     * @return bool
     */
    private function _isUnderAge($oRequest) {
        $aApolloParam = [
            'key'          => time(),
            'rider_phone'  => $oRequest->sRiderPhone,
            'city'         => $oRequest->iCityId,
            'channel'      => $oRequest->iChannel,
            'client_type'  => $oRequest->iClientType,
            'is_upversion' => version_compare($oRequest->sAppVersion, '5.3.2') >= 0 ? 1 : 0,
        ];
        if (!Apollo::getInstance()->featureToggle('gs_underage_test', $aApolloParam)->allow()) {
            return false;
        }

        $oPassportClient = new PassportClient();
        $aPassportResult = $oPassportClient->getUserInfoByCellRole($oRequest->sRiderPhone, 1);
        if (!isset($aPassportResult['errno'])
            || 0 != $aPassportResult['errno']
            || empty($aPassportResult['result'])
            || empty($aPassportResult['result']['uid'])
        ) {
            return true;
        }

        $iVirsualPid   = \BizLib\Utils\UtilHelper::getPassengerIdByUidNew($aPassportResult['result']['uid']);
        $aParams       = [
            'uid'    => $iVirsualPid,
            'fields' => json_encode(['auth_state']),
        ];
        $oKronosClient = new KronosClient();
        $aKronosRet    = $oKronosClient->getByUid($aParams);
        $aCheckRet     = [];

        if (isset($aKronosRet['errno']) && (0 == $aKronosRet['errno'])) {
            if (ConstKronos::AUTH_STATE_OK == $aKronosRet['data']['auth_state']) {
                return false;
            }
        }

        $oFeatureService = new FeatureServiceClient();
        $aFeatureResult  = $oFeatureService->getFeatureV2(self::MINORS_REQUIRE_NAME, self::MINORS_TOKEN, $iVirsualPid);
        if (0 == $aFeatureResult['errno']
            && isset($aFeatureResult['data'][self::MINORS_FEATURE_ID])
            && date('Y') - $aFeatureResult['data'][self::MINORS_FEATURE_ID] < 16
        ) {
            return true;
        }

        return false;
    }
}
