<?php

namespace PreSale\Application\Service;

//use BizLib\Libraries\RedisDB;
//use BizLib\Log as NuwaLog;
//use BizLib\Utils\Common;
//use BizLib\Utils\MapHelper;
//use BizLib\Utils\PublicLog;
//use Nuwa\ApolloSDK\Apollo;
//use PreSale\Dto\GetIndexInfoRequest;
//use PreSale\Dto\GetIndexInfoResponse;
//use PreSale\Application\Assembler\GetIndexInfoAssembler;
//use PreSale\Domain\Model\Entrance\Entrance;
//use PreSale\Domain\Service\EmotionCommunication\EmotionInfoRender;
//use PreSale\Infrastructure\Repository\Ufs\UfsRepository;
//use BizLib\Client\Cache\StorageClient;
//use BizLib\Client\PopeActionGatewayClient;

/**
 *
 * Copyraight (c) 2019 xiaojukeji.com, Inc. All Rights Reserved.
 * @author: wangtuan<PERSON><EMAIL>
 * @date: 2019-12-27 15:27
 * @desc: 首页信息服务
 * @wiki:
 *
 */

class GetIndexInfoService
{

    /**
     * @param GetIndexInfoRequest $oRequest 请求信息
     * @throws \Exception 异常信息
     * @return GetIndexInfoResponse
     */
//    public function build(GetIndexInfoRequest $oRequest): GetIndexInfoResponse {
//        // 入口model构建
//        $oEntrance = new Entrance($oRequest);
//        $oEntrance->build();
//
//        // 情感沟通
//        $oEmotionRender = new EmotionInfoRender($oEntrance);
//        $aEmotionInfo   = $oEmotionRender->getEmotionInfo();
//        $aIndexInfoResponse = $this->_getResponse($oEntrance, $aEmotionInfo, []);
//        return GetIndexInfoAssembler::assembleIndexInfoResponse($aIndexInfoResponse);
//    }

    // 注释-待删
//    /**
//     * 发放奖励
//     * @param GetIndexInfoRequest $oRequest req
//     * @param int                 $iRewards rewards
//     * @param int                 $aConfig  rewards
//     * @return  mixed
//     */
//    private function _hairReward($oRequest, $iRewards, $aConfig) {
//        $aParam  = $this->_getParams($oRequest, $iRewards, $aConfig);
//        $oClient = new PopeActionGatewayClient();
//        $aRet    = $oClient->syncBind($aParam);
//        if (isset($aRet['errno']) && 0 == $aRet['errno']) {
//            return true;
//        } else {
//            NuwaLog::warning(sprintf('ret:%s|param:%s|', json_encode($aRet), json_encode($aParam)));
//            return false;
//        }
//    }

    // 注释-待删
//    /**
//     * 打印public日志
//     * @param GetIndexInfoRequest $oRequest        req
//     * @param string              $sGroupName      name
//     * @param int                 $iRewards        rewards
//     * @param int                 $iFinishCount    count
//     * @param bool                $bFrequencyLimit limit
//     * @return void
//     */
//    private function _addPublicLog($oRequest, $sGroupName, $iRewards = 0, $iFinishCount = 0, $bFrequencyLimit = false) {
//        $arrStatistic = [
//            'opera_stat_key'  => 'kflower_bubble_guide_info',
//            'time'            => time(),
//            'uid'             => $oRequest->aPassengerInfo['uid'] ?? 0,
//            'finish_count'    => $iFinishCount,
//            'frequency_limit' => (int)$bFrequencyLimit,
//            'group_name'      => $sGroupName,
//            'rewards'         => $iRewards,
//            'city'            => $oRequest->iCityId ?? 0,
//            'phone'           => $oRequest->aPassengerInfo['phone'] ?? 0,
//            'access_key_id'   => $oRequest->iAccessKeyId,
//        ];
//        PublicLog::writeLogForOfflineCal('public', $arrStatistic);
//    }

    // 注释-待删
//    /**
//     * 校验配置是否合法，返回true合法 false不合法
//     * @param array $aConfig config
//     * @return bool
//     */
//    private function _isValidConfig($aConfig) {
//        if (!is_array($aConfig['text']) || count($aConfig['text']) > 2 || empty($aConfig['background']) || !is_array($aConfig['reward_text']) || count($aConfig['reward_text']) > 2) {
//            return false;
//        }
//
//        foreach ($aConfig['text'] as $sText) {
//            if (mb_strlen($sText,'utf8') > 9) {
//                return false;
//            }
//        }
//
//        foreach ($aConfig['reward_text'] as $sText) {
//            if (mb_strlen($sText,'utf8') > 9) {
//                return false;
//            }
//        }
//
//        return true;
//    }

    /**
     *
     * @param Entrance $oEntrance        入口model信息
     * @param array    $aEmotionInfo     情感沟通
     * @param array    $aBubbleGuideInfo 新客冒泡引导
     * @return array
     */
//    private function _getResponse(Entrance $oEntrance, array $aEmotionInfo, array $aBubbleGuideInfo) {
//        $aResponse = [
//            'one_conf_info'            => $oEntrance->aOneConfData,
//            'emotion_communicate_info' => $aEmotionInfo,
//        ];
//
//        if (!empty($aBubbleGuideInfo)) {
//            $aResponse['bubble_guide_info'] = $aBubbleGuideInfo;
//        }
//
//        return $aResponse;
//    }

    // 注释-待删
//    /**
//     * 构造请求参数
//     * @param object $oRequest req
//     * @param int    $iRewards rewards
//     * @param int    $aConfig  rewards
//     * @return array
//     */
//    private function _getParams($oRequest, $iRewards, $aConfig): array {
//        $aAction = array(
//            array(
//                'type'   => 'bonus',
//                'params' => array(
//                    'amount'     => $iRewards,
//                    'bonus_type' => 1,
//                    'channel'    => 'guide_bubble',
//                    'city_id'    => $oRequest->iCityId,
//                ),
//            ),
//            array(
//                'type'   => 'groupmsg',
//                'params' => array(
//                    'app_type'  => 101,
//                    'app_id'    => 299069625327882,
//                    'admin'     => '<EMAIL>',
//                    'title'     => sprintf($aConfig['push']['title'], round($iRewards / 100, 2)),
//                    'active_id' => '210002001',
//                    'content'   => array(
//                        'type'      => $aConfig['push']['type'],
//                        'content'   => $aConfig['push']['content'],
//                        'title'     => sprintf($aConfig['push']['title'], round($iRewards / 100, 2)),
//                        'sub_title' => $aConfig['push']['sub_title'],
//                        'timestamp' => time(),
//                    ),
//                ),
//            ),
//        );
//
//        $aActivityInfo = array(
//            'active_id'         => '210002001',
//            'marketing_purpose' => '引导用户发单',
//        );
//
//        $aParam = array(
//            'channel_id'    => 67,
//            'phone'         => $oRequest->aPassengerInfo['phone'],
//            'uid'           => $oRequest->aPassengerInfo['uid'],
//            'role'          => 1,   //1乘客 2司机
//            'actions'       => json_encode($aAction),
//            'activity_info' => json_encode($aActivityInfo),
//            'uuid'          => $oRequest->aPassengerInfo['uid'] . '_' . $oRequest->iCityId,
//        );
//        ksort($aParam);
//        $parts = [];
//        foreach ($aParam as $k => $v) {
//            if (is_array($v)) {
//                $v = json_encode($v);
//            }
//
//            $parts[] = $k . '=' . $v;
//        }
//
//        $sSign          = strtoupper(md5(implode('&', $parts) . '&key=' . md5($aConfig['secret'])));
//        $aParam['sign'] = $sSign;
//        return $aParam;
//    }
}

