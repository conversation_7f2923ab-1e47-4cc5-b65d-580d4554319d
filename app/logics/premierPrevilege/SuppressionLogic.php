<?php
/**
 * Created by PhpStorm.
 * User: didi
 * Date: 2019/8/15
 * Time: 7:40 PM
 *
 * 当前是专车发单抑制处理逻辑，根据Athena返回的结果判断用户是否进行专车的发单抑制
 * 具体action：
 * 1. 组装抑制的文案以及端所需的气泡展示资源
 * 2. anycar列表中专车对应item置灰
 *
 * 产品wiki:http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=220401428#app-switcher
 * 技术wiki:http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=235814938
 *
 * <AUTHOR> <<EMAIL>>
 * @date 19/8/19
 */

namespace PreSale\Logics\premierPrevilege;

require_once('vendor/autoload.php');

use BizLib\Log as NuwaLog;
use Xiaoju\Apollo\Apollo as ApolloV2;
use Xiaoju\Apollo\Apollo;
use Biz<PERSON>ommon\Constants\OrderNTuple;

/**
 * Class SuppressionLogic
 * @package PreSale\Logics\premierPrevilege
 * desc: 专车冒泡页抑制->导流气泡，非抑制->权益露出
 */
class SuppressionLogic
{
    // apollo 配置namespace以及config文件名
    const NS_PREMIER_EXPECTATIONS_MANAGEMENT       = 'premier_expectations_management';
    const CONF_PREMIER_BUBBLE_SUPPRESSION_RESOURCE = 'premier_bubble_suppression_resource';

    const GUIDE_SCENE_WHEN_SUPPRESSION = 52;
    const GUIDE_SCENE_NOT_SUPPRESSION  = 53;

    const SHOW_TYPE_WHEN_SUPPRESSION = 8;
    const SHOW_TYPE_NOT_SUPPRESSION  = 9;

    /**
     * checkSuppressionToggle 检查apollo开关
     * @param array $aAthenaBubbleParams athena请求参数
     *
     * @return bool
     */
    public static function checkSuppressionToggle(&$aAthenaBubbleParams) {
        $apolloV2 = new ApolloV2();
        if ($apolloV2->featureToggle(
            'premier_bubble_suppression_toggle',
            [
                'key'   => rand(1, 500000),
                'phone' => $aAthenaBubbleParams['phone'],
                'pid'   => $aAthenaBubbleParams['pid'],
                'city'  => $aAthenaBubbleParams['from_area'],
            ]
        )->allow()
        ) {
            return true;
        }

        return false;
    }

    /**
     * handle 专车预估冒泡发单抑制主逻辑
     * @param string $sGuideResult       导流返回结果
     * @param array  $aEstimatePriceResp 预估价格信息
     * @param array  $aAthenaExtra       导流返回extra
     * @return void
     */
    public static function handle(&$sGuideResult, &$aEstimatePriceResp, &$aAthenaExtra) {
        self::handleEstimateData($aEstimatePriceResp,$aAthenaExtra);

        if (empty($sGuideResult)) {
            return;
        }

        $aGuideResult = json_decode($sGuideResult, true);

        if (!isset($aGuideResult['errno'])
            || !isset($aGuideResult['errmsg'])
            || !isset($aGuideResult['data'])
            || !isset($aGuideResult['data']['guide'])
            || 0 == count($aGuideResult['data']['guide'])
        ) {
            NuwaLog::warning('guide_result incomplete||guide_result='.$sGuideResult);
            return;
        }

        if ((int)($aGuideResult['errno'])) {
            NuwaLog::warning(
                sprintf(
                    'guide_result error||errno:%d||errmsg:%s',
                    $aGuideResult['errno'],
                    $aGuideResult['errmsg']
                )
            );
            return;
        }

        $bGuideInfoIsUpdate = false;
        foreach ($aGuideResult['data']['guide'] as &$aGuideItem) {
            $iCurShowType   = 0;
            $iCurGuideScene = 0;

            if (isset($aGuideItem['show_text']) && isset($aGuideItem['show_text']['show_type'])) {
                $iCurShowType = (int)($aGuideItem['show_text']['show_type']);
            }

            if (isset($aGuideItem['extra']) && isset($aGuideItem['extra']['guide_scene'])) {
                $iCurGuideScene = (int)($aGuideItem['extra']['guide_scene']);
            }

            if (self::SHOW_TYPE_NOT_SUPPRESSION == $iCurShowType
                && self::GUIDE_SCENE_NOT_SUPPRESSION == $iCurGuideScene
            ) {
                // 专车区域冒泡抑制场景下，用户未被抑制
                $bGuideInfoIsUpdate = self::handleGuideInfo($aGuideItem);
            }
        }

        unset($aGuideItem);

        if ($bGuideInfoIsUpdate) {
            $sGuideResult = json_encode($aGuideResult);
        }
    }

    /**
     * handleGuideInfo 处理导流数据
     * @param array $aGuideInfo 导流信息
     *
     * @return bool
     */
    public static function handleGuideInfo(&$aGuideInfo) {
        $iMemberID = 0;
        if (isset($aGuideInfo['extra'])) {
            if (isset($aGuideInfo['extra']['member_data'])) {
                $aMemberInfo = json_decode($aGuideInfo['extra']['member_data'], true);
                if (isset($aMemberInfo['errno']) && isset($aMemberInfo['errmsg'])) {
                    if ((int)($aMemberInfo['errno'])) {
                        NuwaLog::warning(
                            sprintf(
                                'get member info error||errno=%d||errmsg=%s',
                                $aMemberInfo['errno'],
                                $aMemberInfo['errmsg']
                            )
                        );
                        return false;
                    }
                }

                $iMemberID = self::_getMemberID($aMemberInfo);
            } else {
                NuwaLog::warning(
                    sprintf(
                        'member data does not exists in athena extra||athena_extra:%s',
                        json_encode($aGuideInfo['extra'])
                    )
                );
                return false;
            }
        }

        $sKey = 'equity_perceptions';
        $aBubbleResourceMap = self::getBubbleResource();
        if (!isset($aBubbleResourceMap[$iMemberID])) {
            NuwaLog::warning(sprintf('member level_id:%d correspond %s config not exists', $iMemberID, $sKey));
            return false;
        }

        $aBubbleResource = $aBubbleResourceMap[$iMemberID];

        $aGuideInfo['show_text']['title']    = $aBubbleResource['title'];
        $aGuideInfo['show_text']['text']     = $aBubbleResource['text'];
        $aGuideInfo['show_text']['show_url'] = $aBubbleResource['member_icon_url'];

        $aGuideInfo['show_text']['extra']['bubble_needle_url'] = $aBubbleResource['bubble_needle_url'];
        $aGuideInfo['show_text']['extra']['color_list']        = json_encode($aBubbleResource['background_color_list']);
        $aGuideInfo['show_text']['extra']['line_color']        = $aBubbleResource['line_color'];
        $aGuideInfo['show_text']['extra']['title_font_color']  = $aBubbleResource['title_font_color'];
        $aGuideInfo['show_text']['extra']['text_font_color']   = $aBubbleResource['text_font_color'];
        $aGuideInfo['show_text']['extra']['close_icon_url']    = $aBubbleResource['close_icon_url'];

        unset($aGuideInfo['extra']['member_data']);
        return true;
    }

    /**
     * handleEstimateData 处理anycar预估数据
     * @param array $aEstimatePriceResp 多预估返回结果
     * @param array $aAthenaExtra       Athena扩展信息
     *
     * @return void
     */
    public static function handleEstimateData(&$aEstimatePriceResp, &$aAthenaExtra) {
        if (!isset($aAthenaExtra['athena_anycar_guide_info'])) {
            return;
        }

        $aAthenaAnycarGuideInfo = json_decode($aAthenaExtra['athena_anycar_guide_info'], true);

        if (!isset($aAthenaAnycarGuideInfo['preference_product_list'])
            || 0 == count($aAthenaAnycarGuideInfo['preference_product_list'])
        ) {
            return;
        }

        $aDisableProduct = array();
        foreach ($aAthenaAnycarGuideInfo['preference_product_list'] as $aData) {
            if (isset($aData['disabled']) && 1 == (int)($aData['disabled'])) {
                $aDisableProduct[(int)($aData['business_id'])] = (int)($aData['require_level']);
            }
        }

        if (empty($aEstimatePriceResp) || 0 == count($aEstimatePriceResp)) {
            NuwaLog::warning('estimateprice_resp is empty');
            return;
        }

        foreach ($aEstimatePriceResp as &$aEstimatePriceItem) {
            if (!isset($aEstimatePriceItem['bill_info']) || !isset($aEstimatePriceItem['athena_info'])) {
                continue;
            }

            $iProductID = isset($aEstimatePriceItem['athena_info']['product_id']) ? (int)($aEstimatePriceItem['athena_info']['product_id']) : 0;
            if (OrderNTuple::COMMON_PRODUCT_ID_ANY_CAR != $iProductID) {
                continue;
            }

            $aBillInfo = json_decode($aEstimatePriceItem['bill_info'], true);
            if (!isset($aBillInfo['multi_info']) || 0 == count($aBillInfo['multi_info'])) {
                continue;
            }

            foreach ($aBillInfo['multi_info'] as &$aInfo) {
                $iBusinessID   = isset($aInfo['business_id']) ? (int)($aInfo['business_id']) : 0;
                $iRequireLevel = isset($aInfo['require_level']) ? (int)($aInfo['require_level']) : 0;
                if ($aDisableProduct[$iBusinessID] == $iRequireLevel) {
                    $aInfo['disabled'] = 1;
                }
            }

            unset($aInfo);

            $aEstimatePriceItem['bill_info'] = json_encode($aBillInfo);
            break;
        }

        unset($aEstimatePriceItem);
    }

    /**
     * getBubbleResource 获取专车冒泡抑制或者权益露出资源
     * @param bool $bSuppression 抑制标示
     *
     * @return array
     */
    public static function getBubbleResource($bSuppression = false) {
        $oEngine          = new Apollo();
        $oConfigNamespace = $oEngine->getConfigsByNamespace(self::NS_PREMIER_EXPECTATIONS_MANAGEMENT);
        $oConfResults     = $oConfigNamespace->getConfig(self::CONF_PREMIER_BUBBLE_SUPPRESSION_RESOURCE);
        if (!$bSuppression) {
            $sConfigKey = 'equity_perceptions';
        } else {
            $sConfigKey = 'bubble_suppression';
        }

        list($bOK, $aBubbleData) = $oConfResults->getConfig($sConfigKey);
        if (!$bOK || empty($aBubbleData) || 0 == count($aBubbleData)) {
            NuwaLog::warning(
                'get '.$sConfigKey.' config from apollo failed||'.json_encode(array('conf' => json_encode($aBubbleData),))
            );
            return array();
        }

        $aBubbleResMap = array();
        foreach ($aBubbleData as $aData) {
            $iMemberID  = $aData['member_id'];
            $sColorList = $aData['background_color_list'];
            $aColorList = explode(',', $sColorList);

            $aBubbleResMap[$iMemberID] = array(
                'title'                 => $aData['title'],
                'text'                  => $aData['text'],
                'bubble_needle_url'     => $aData['bubble_needle_url'],
                'background_color_list' => $aColorList,
                'line_color'            => $aData['line_color'],
                'title_font_color'      => $aData['title_font_color'],
                'text_font_color'       => $aData['text_font_color'],
                'close_icon_url'        => $aData['close_icon_url'],
            );

            if (!$bSuppression) {
                $aBubbleResMap[$iMemberID]['member_icon_url'] = $aData['member_icon_url'];
            } else {
                $aBubbleResMap[$iMemberID]['button_text'] = $aData['button_text'];
            }
        }

        return $aBubbleResMap;
    }

    /**
     * _getMemberID 获取会员level_id
     * @param array $aMemberInfo 会员权益响应体
     *
     * @return bool|int
     */
    private static function _getMemberID(&$aMemberInfo) {
        if (isset($aMemberInfo['data'])) {
            $aMemberInfo = $aMemberInfo['data'];
        }

        return isset($aMemberInfo['level_id']) ? (int)($aMemberInfo['level_id']) : 0;
    }
}

