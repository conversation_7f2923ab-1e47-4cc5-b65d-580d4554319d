<?php

namespace PreSale\Logics\persionalizedSwitch;

use Biz<PERSON>ib\Client\UfsClient;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Log as NuwaLog;

class PersionalizedSwitch
{

    /**
     * @var PersionalizedSwitch
     */
    private static $_oInstance;

    const UfsUserCarRecommend = 'base.user_decision_gulfstream_vehicles_combination'; // 个性化推荐开关

    private $iUserCarRecommendSwitch = 0;


    /**
     * constructor
     */
    private function __construct($uid) {
        $this->init($uid);
    }

    /**
     * @param int $uid $uid
     * @return void
     */
    private function init($uid) {
        $aConditions = ['uid' => $uid];
        $aUfsRet     = (new UfsClient())->getFeature(array(self::UfsUserCarRecommend), $aConditions, 'passenger');
        if (isset($aUfsRet['errno']) && 0 != $aUfsRet['errno']) {
//            NuwaLog::warning(
//                Msg::formatArray(
//                    Code::E_COMMON_GET_UFS_FAIL,
//                    ['featureKey' => self::UfsUserCarRecommend, 'condition' => $aConditions, 'ufsRes' => $aUfsRet]
//                )
//            );
            return;
        }

        $this->iUserCarRecommendSwitch = $aUfsRet['result'][self::UfsUserCarRecommend] ?? 0;
    }

    /**
     * @return PersionalizedSwitch
     */
    public static function getInstance($pid) {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self($pid);
        }

        return self::$_oInstance;
    }

    /**
     * @return int
     */
    public function getIUserCarRecommendSwitch() {
        return $this->iUserCarRecommendSwitch;
    }
}

