<?php
namespace PreSale\Logics\intercityEstimate\multiRequest;

use BizLib\Utils\UtilHelper;
use Dirpc\SDK\PreSale\IntercityEstimatePriceRequest as Request;
use BizCommon\Utils\Horae;
use PreSale\Logics\intercityEstimatePrice\multiRequest\Util;
use TripcloudCommon\Utils\ApolloConf as TripCloudApolloConf;

/**
 * Class OrderInfo
 * @package PreSale\Logics\intercityEstimate\multiRequest
 */
class OrderInfo
{
    /**
     * @var string $sEstimateID
     */
    public $sEstimateID;

    /**
     * @var integer 订单类型
     */
    public $iOrderType = 0;

    /**
     * @var string 顶导字符串ID
     */
    public $sMenuID;

    /**
     * @var integer 二级页面ID
     */
    public $iPageType;

    /**
     * @var integer 用户选择的拼车拼座信息, N元组的一个字段，用于后序计算价各下流识别
     */
    public $iCarpoolSeatNum;

    /**
     * @var integer 品类ID
     */
    public $iProductCategory;

    public $iProductId;

    public $iBusinessId;

    public $iRequireLevel;

    public $iComboType;

    public $iCarpoolType;

    public $iIsSpecialPrice;

    public $iLevelType;

    public $iCarpoolPriceType;

    public $bIsDualCarpoolPrice;

    public $iRouteType;

    /**
     * @var bool 是否被过滤
     */
    public $bRemoveFlag = false;

    /**
     * @var int 支付方式
     */
    public $iPaymentsType = 0;

    /**
     * @var int|string 出发时间
     */
    public $iDepartureTime;

    /**
     * @var int 路线ID
     */
    public $iComboId = 0;

    /**
     * @var int routeGroup
     */
    public $iRouteGroup = 0;

    /**
     * @var string 出发时间片
     */
    public $sDepartureRange = '';


    /**
     * @var array 站点信息
     */
    public $aStationList = [];


    /**
     * @var int 路线ID (账单依赖)
     */
    public $iComboIds = '';


    /**
     * @var array 路线信息
     */
    public $aMatchRoutes = [];

    /**
     * @var int 最早出发时间
     */
    public $iDepartureTimeEarliest = 0;

    /**
     * @var int 预估时间
     */
    public $iBubbleTime = 0;

    /**
     * @var array
     */
    public $oSeatDetailInfo;
    /**
     * @var array
     */
    public $iMaxSeatNum = 0;

    /**
     * @var int 是否计算库存
     */
    public $iIsSkuModel = 0;

    /**
     * OrderInfo constructor.
     * @param Request $oEstimateRequest $oEstimateRequest
     * @return void
     */
    public function __construct(Request $oEstimateRequest) {
        // 构建基本信息
        $this->sMenuID         = $oEstimateRequest->getMenuId();
        $this->iPaymentsType   = $oEstimateRequest->getPaymentsType();
        $this->sDepartureRange = $oEstimateRequest->getDepartureRange();
        $this->iDepartureTime  = time();
    }

    /**
     * @param array      $aBaseOrderInfo orderInfo
     * @param CommonInfo $oCommonInfo    oCommonInfo
     * @return void
     */
    public function fillUpOrderInfo($aBaseOrderInfo, CommonInfo $oCommonInfo) {
        $this->iPageType        = $oCommonInfo->iPageType;
        $this->iProductCategory = $aBaseOrderInfo['product_category'];
        $this->iProductId       = $aBaseOrderInfo['product_id'];
        $this->iBusinessId      = $aBaseOrderInfo['business_id'];
        $this->iRequireLevel    = $aBaseOrderInfo['require_level'];
        $this->iComboType       = $aBaseOrderInfo['combo_type'];
        $this->iCarpoolType     = $aBaseOrderInfo['carpool_type'];
        $this->iIsSpecialPrice  = $aBaseOrderInfo['is_special_price'];
        $this->iLevelType       = $aBaseOrderInfo['level_type'];
        $this->iOrderType       = $aBaseOrderInfo['order_type'];
        $this->iRouteType       = $aBaseOrderInfo['route_type'];

        $this->sEstimateID         = UtilHelper::getEstimateId($this->iProductId, $this->iRequireLevel, $this->iComboType, $this->iProductCategory);
        $this->iCarpoolPriceType   = $aBaseOrderInfo['carpool_price_type'];
        $this->aStationList        = $aBaseOrderInfo['order_info']['station_list'];
        $this->bIsDualCarpoolPrice = $aBaseOrderInfo['is_dual_carpool_price'];
        $this->iCarpoolSeatNum     = $this->_buildCarpoolSeatNum($oCommonInfo);
        $this->bRemoveFlag         = $aBaseOrderInfo['remove_flag'];

        // 城际拼车参数处理
        $this->_buildInterCarpoolProduct($aBaseOrderInfo);
    }


    /**
     * @param array $aBaseOrderInfo aBaseOrderInfo
     * @return void
     */
    private function _buildInterCarpoolProduct($aBaseOrderInfo) {
        $aOrderExtraInfo = $aBaseOrderInfo['order_info']['order_extra_info'] ?? [];
        if (empty($aOrderExtraInfo)) {
            return;
        }

        $aRouteInfo      = $aOrderExtraInfo['route_info'];
        $this->iComboId  = (int)($aRouteInfo['route_id']);
        $this->iComboIds = (string)($aRouteInfo['route_id']);
        $this->iRouteGroup = (int)($aRouteInfo['route_group']);
        $this->iMaxSeatNum = (int)($aRouteInfo['max_seat_num']);
        $this->iIsSkuModel=(int)($aRouteInfo['is_sku_model']);
        if (Horae::isInterCarpoolGoRightNow($aBaseOrderInfo)) {
            $aRouteInfo['time_span'] = [];
            $this->aMatchRoutes[]    = $aRouteInfo;
            return;
        }

        $this->aMatchRoutes[] = $aRouteInfo;
        if (empty($this->sDepartureRange)) { // 端上没有上传，也就是第一次冒泡
            // 首次冒泡，用户没有选择时间片，采用最近的第一个时间片冒泡, 当天23点后可能没有时间片，需要取明天的第一个时间片
            if (isset($aRouteInfo['time_span'][0]['range'][0]['value'])) {
                $sDepartureRange       = $aRouteInfo['time_span'][0]['range'][0]['value'];
                $this->sDepartureRange = $sDepartureRange;
                $aDepartureRange       = json_decode($this->sDepartureRange, true);
                // $this->iDepartureTimeEarliest = $aDepartureRange[0];
            } else {
                $sDepartureRange       = $aRouteInfo['time_span'][1]['range'][0]['value'];
                $aDepartureRange       = json_decode($sDepartureRange, true);
                $this->sDepartureRange = $sDepartureRange;
            }
        } else {
            $aDepartureRange = json_decode($this->sDepartureRange, true);
        }

        $this->iDepartureTime = $aDepartureRange[1] ?? $this->iDepartureTime;
        $this->iBubbleTime    = time();
    }

    /**
     * 将OrderInfo转换成数组格式
     * @return array
     */
    public function toArray() {
        $aObjectVars = get_object_vars($this);
        $aOrderInfo  = array();
        foreach ($aObjectVars as $key => $value) {
            $key   = substr($key, 1);
            $field = $this->_uncamelize($key);
            $aOrderInfo[$field] = $value;
        }

        return $aOrderInfo;
    }

    /**
     * @param string $camelCaps 驼峰字符串
     * @param string $separator 分割符
     * @return string
     */
    private function _uncamelize($camelCaps, $separator = '_') {
        return strtolower(preg_replace('/([a-z])([A-Z])/', '$1' . $separator . '$2', $camelCaps));
    }


    /**
     * 初版定协议，座位数和具体品类关联，这里增加兜底
     * @param CommonInfo $oCommonInfo ...
     * @return int
     */
    private function _buildCarpoolSeatNum(CommonInfo $oCommonInfo) {
        $aMulti = $oCommonInfo->aMultiRequireProducts;

        // 当前品类存在端上传的座位数
        if (isset($aMulti[(int)$this->iProductCategory]['carpool_seat_num']) && (int) $aMulti[(int)$this->iProductCategory]['carpool_seat_num'] > 0) {
            return (int) $aMulti[(int)$this->iProductCategory]['carpool_seat_num'];
        }

        // 其他品类存在端上传的座位数
        foreach ($aMulti as $aItem) {
            if ((int)$aItem['carpool_seat_num'] > 0) {
                return (int)$aItem['carpool_seat_num'];
            }
        }

        // 兜底为座位数1
        return 1;
    }
}
