<?php
namespace PreSale\Logics\intercityEstimatePrice\multiRequest;

use BizCommon\Models\Order\Order;
use BizLib\Client\PrfsClient;
use BizLib\Client\TripcloudPassenger;
use BizLib\Constants\Horae;
use BizLib\Constants\OrderSystem;
use Dirpc\SDK\PreSale\IntercityEstimatePriceRequest as Request;
use BizCommon\Models\Passenger\Passenger;
use BizLib\Utils\MapHelper;
use BizLib\Exception\ExceptionWithResp;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\RespCode;
use PreSale\Logics\intercityEstimate\multiRequest\AreaInfo;
use PreSale\Logics\intercityEstimate\multiRequest\CommonInfo;
use Dirpc\SDK\EstimateDecision\ProductsReq;
use Dirpc\SDK\EstimateDecision\UserInfoV2;
use Dirpc\SDK\EstimateDecision\CommonInfoV2;
use BizLib\Client\EstimateDecisionClient;
use BizLib\Config;
use BizLib\Exception\InvalidArgumentException;
use Nebula\Exception\Route\InterRouteException;
use BizCommon\Utils\Common as BizCommon;
use BizLib\Log as NuwaLog;
use BizLib\ErrCode;
use BizLib\Constants\Common as ConstCommon;
use BizLib\Client\CarpoolClient;
use TripcloudCommon\Utils\ApolloConf as TripCloudApolloConf;
use TripcloudCommon\Utils\Product as TripcloudProduct;
use Xiaoju\Apollo\Apollo;
use Xiaoju\Apollo\Apollo as ApolloV2;
use PreSale\Logics\intercityEstimatePrice\multiResponse\globalData\CarpoolIntercityRule;

/**
 * Class ProductList 构建产品列表入参
 */
class ProductList
{

    /**
     * @var ProductList 单例对象
     */
    protected static $_oInstance = null;

    /**
     * @var Request 请求类
     */
    protected $_oRequest = null;

    /**
     * @var array 基础用户信息
     */
    private $_aBasePassengerInfo = [];

    /**
     * @var array 地理位置信息
     */
    private $_aAreaInfo = [];

    /**
     * @var array 基础品类信息
     */
    private $_aBaseProductList = [];

    /**
     * @var Product[] 构建后的产品线参数
     */
    public $aProductList = [];

    /**
     * @var array 座位信息
     */
    private $_aSeatDetailInfo = [];

    /**
     * @var mixed|Product
     */
    private $_oKeqiSkuProduct;
    /**
     * @var int
     */
    private $isResetSeat = 0;


    /**
     * ProductList constructor.
     * @param Request $oRequest 请求类
     */
    public function __construct(Request $oRequest) {
        $this->_oRequest = $oRequest;
    }


    /**
     * @param Request $oRequest 元请求
     * @return ProductList|null
     */
    public static function getInstance(Request $oRequest) {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self($oRequest);
        }

        return self::$_oInstance;
    }


    /**
     * @return $this
     * @throws ExceptionWithResp passportException
     */
    public function preBuild() {
        $this->_oRequest->setPageType(Horae::PAGE_TYPE_INTER_CITY);
        $this->_oRequest->setMenuId(ConstCommon::MENU_DACHE_ANYCAR);
        $this->_aBasePassengerInfo          = Passenger::getInstance()->getPassengerByTokenFromPassport($this->_oRequest->getToken());
        $this->_aBasePassengerInfo['token'] = $this->_oRequest->getToken();
        $this->_buildOrderType();
        $this->_buildAreaInfo();
        $this->_buildPaymentsType();

        return $this;
    }


    /**
     * @throws ExceptionWithResp AreaInfoException
     * @return void
     */
    private function _buildAreaInfo() {
        $aFromArea = MapHelper::getAreaInfoByLoc($this->_oRequest->getFromLng(), $this->_oRequest->getFromLat());
        if (!empty($aFromArea)) {
            $this->_aAreaInfo['from_area'] = $aFromArea;
        }

        // 起点城市获取失败，默认为0，走降级
        if (empty($this->_aAreaInfo['from_area']['id'])) {
            throw new ExceptionWithResp(
                Code::E_COMMON_HTTP_READ_FAIL,
                RespCode::R_ESTIMATE_DOWNSTREAM_FAIL_DEGRADE,
                '',
                ['from_lng' => $this->_oRequest->getFromLng(), 'from_lat' => $this->_oRequest->getFromLat(), 'ret' => $aFromArea]
            );
        }

        $aToArea = MapHelper::getAreaInfoByLoc($this->_oRequest->getToLng(), $this->_oRequest->getToLat());
        if (!empty($aToArea)) {
            $this->_aAreaInfo['to_area'] = $aToArea;
        }
    }


    /**
     * @return Product[]
     * @throws ExceptionWithResp InterRouteException InvalidArgumentException
     */
    public function buildProductList() {
        $oAreaInfo   = new AreaInfo($this->_oRequest, $this->_aAreaInfo);
        $oCommonInfo = new CommonInfo($this->_oRequest);
        $this->_aBaseProductList = $this->_getDDSProducts($oAreaInfo);

        // 根据products接口返回的多条产品构建基础req数据
        foreach ($this->_aBaseProductList as $iIndex => $aBaseProduct) {
            if ($aBaseProduct['remove_flag']) {
                continue;
            }

            $oProduct = new Product($this->_oRequest);
            $oProduct->buildCommonInfo($oCommonInfo);
            $oProduct->buildOrderInfo($aBaseProduct);
            $oProduct->buildAreaInfo($oAreaInfo);
            $oProduct->buildPassengerInfo($this->_aBasePassengerInfo);
            $this->aProductList[] = $oProduct;
        }

        // 预约更便宜需求特殊过滤逻辑
        // 是否有任意品类命中
        // 首次进入、时间片为空才推荐
        if ('' == $this->_oRequest->getDepartureRange()) {
            $bAnyPreferentialStatus = false;
            $aAliveProductList      = [];
            foreach ($this->aProductList as $iIndex => $aProduct) {
                $aApolloParam = [
                    'key'              => $oProduct->oPassengerInfo->iPid,
                    'phone'            => $oProduct->oPassengerInfo->sPhone,
                    'pid'              => $oProduct->oPassengerInfo->iPid,
                    'uid'              => $oProduct->oPassengerInfo->iUid,
                    'city'             => $oProduct->oAreaInfo->iArea,
                    'combo_id'         => $oProduct->oOrderInfo->iComboId,
                    'route_group'      => $oProduct->oOrderInfo->aMatchRoutes[0]['route_group'],
                    'product_id'       => $oProduct->oOrderInfo->iProductId,
                    'product_category' => $oProduct->oOrderInfo->iProductCategory,
                    'access_key_id'    => $oProduct->oCommonInfo->iAccessKeyID,
                    'app_version'      => $oProduct->oCommonInfo->sAppVersion,
                ];
                list($status, $iTimeInterval) = $this->_PreferentialStatus($aApolloParam);
                if ($status) {
                    $bAnyPreferentialStatus = true;
                    $aProduct            = $this->_rewriteOrderInfoPreferential($aProduct, $iTimeInterval);
                    $aAliveProductList[] = $aProduct;
                    break;
                }
            }

            if ($bAnyPreferentialStatus) {
                $this->aProductList = $aAliveProductList;
            }
        }

        if ($this->_isAllowChildTicket($this->_oRequest, $this->_aBasePassengerInfo)) {
            $this->_getKeqiSkuProduct($this->_oRequest);
            $this->_getCarpoolIntercityRule();
            if (!$this->_checkCarpoolSeatNum()) {
                $this->isResetSeat = 1;
            }
        }
        // 配置list为空，按未开城处理，未开城异常不能放在 _getDDSProducts 方法中抛出
        if (empty($this->aProductList)) {
            // 城际顶导，特殊异常处理
            $this->_dealInterCityException();

            // 通用未开城处理
            Util::handleUnOpenCity();
        }

        return $this->aProductList;
    }

    /**
     * 改写order_type 和时间片 （预约更便宜的mock。。。）
     * @param Product $aProduct      $aProduct
     * @param int     $iTimeInterval $iTimeInterval
     * @return Product $aProduct $aProduct
     */
    private function _rewriteOrderInfoPreferential($aProduct, $iTimeInterval) {
        if (0 == $iTimeInterval) {
            return $aProduct;
        }

        $aDaySpans = $aProduct->oOrderInfo->aMatchRoutes[0]['time_span'];
        foreach ($aDaySpans as $iIndex => $aDaySpan) {
            if (0 == count($aDaySpan['range'])) {
                continue;
            }

            foreach ($aDaySpan['range'] as $iIndex => $aTimeSpan) {
                $aDepartureRange        = json_decode($aTimeSpan['value'], true);
                $iDepartureTimeEarliest = $aDepartureRange[0] ?? 0;
                // 若左时间片 - 当前时间 > 时间间隔，就使用这项改写
                if ($iDepartureTimeEarliest - time() > $iTimeInterval) {
                    $aProduct->oOrderInfo->iOrderType      = 1;
                    $aProduct->oOrderInfo->sDepartureRange = $aTimeSpan['value'];
                    $aProduct->oOrderInfo->iDepartureTime  = $aDepartureRange[1];
                    break 2;
                }
            }
        }

        return $aProduct;
    }
    /**
     * 是否进入预约更便宜实验组
     * @param array $aApolloParam $aApolloParam
     * @return bool
     * @return int
     */
    private function _PreferentialStatus($aApolloParam) {


        $oToggle = Apollo::getInstance()->featureToggle(
            'intercity_carpool_subscribe_preferential_toggle',
            $aApolloParam
        );
        if (!$oToggle->allow()) {
            return [false, 0];
        }

        $oExperiment = Apollo::getInstance()->featureToggle(
            'yuantu_reservation_discount',
            $aApolloParam
        );

        if ($oExperiment->allow() && 'treatment_group' == $oExperiment->getGroupName()) {
            // 获取时间间隔(s)
            $iTimeInterval = $oToggle->getParameter('time_interval', 0) * 60;
            return [true, $iTimeInterval];
        }

        return [false, 0];
    }



    /**
     * @param AreaInfo $oAreaInfo $oAreaInfo
     * @return array
     * @throws ExceptionWithResp ...
     * @throws InvalidArgumentException ...
     */
    private function _getDDSProducts(AreaInfo $oAreaInfo) {
        $oUserInfo = new UserInfoV2();
        $oUserInfo->setPhone($this->_aBasePassengerInfo['phone']);
        $oUserInfo->setPid($this->_aBasePassengerInfo['pid']);
        $oUserInfo->setUid($this->_aBasePassengerInfo['uid']);

        $oCommonInfo = new CommonInfoV2();
        $oCommonInfo->setAppVersion($this->_oRequest->getAppVersion());
        $oCommonInfo->setAccessKeyId($this->_oRequest->getAccessKeyId());
        $oCommonInfo->setLang($this->_oRequest->getLang());
        $oCommonInfo->setChannel($this->_oRequest->getChannel());
        $oCommonInfo->setClientType($this->_oRequest->getClientType());
        $oCommonInfo->setStartLat($this->_oRequest->getFromLat());
        $oCommonInfo->setStartLng($this->_oRequest->getFromLng());
        $oCommonInfo->setDestLat($this->_oRequest->getToLat());
        $oCommonInfo->setDestLng($this->_oRequest->getToLng());
        $oCommonInfo->setStartName($this->_oRequest->getFromName());
        $oCommonInfo->setDestName($this->_oRequest->getToName());
        $oCommonInfo->setStartAddress($this->_oRequest->getFromAddress());
        $oCommonInfo->setDestAddress($this->_oRequest->getToAddress());
        $oCommonInfo->setCity($this->_aAreaInfo['from_area']['id'] ?? 0);
        $oCommonInfo->setToCity($this->_aAreaInfo['to_area']['id'] ?? 0);
        $oCommonInfo->setMenuId($this->_oRequest->getMenuId());
        $oCommonInfo->setOrderType($this->_oRequest->getOrderType());
        $oCommonInfo->setPageType($this->_oRequest->getPageType());
        $oCommonInfo->setDepartureRange($this->_oRequest->getDepartureRange());
        $oCommonInfo->setCounty($oAreaInfo->iFromCounty);
        $oCommonInfo->setToCounty($oAreaInfo->iToCounty);
        $oCommonInfo->setDistrict($oAreaInfo->iDistrict);
        $oCommonInfo->setRouteId($this->_oRequest->getRouteId());

        $oProductsReq = new ProductsReq();
        $oProductsReq->setUserInfo($oUserInfo);
        $oProductsReq->setCommonInfo($oCommonInfo);

        $oClient = new EstimateDecisionClient();
        $aResult = $oClient->getProducts($oProductsReq);
        if (0 != $aResult['errno'] || 200 != $aResult['code']) {
            // 服务异常，走降级
            throw new ExceptionWithResp(
                Code::E_DDS_GET_PRODUCTS_FAIL,
                RespCode::R_ESTIMATE_DOWNSTREAM_FAIL_DEGRADE,
                '',
                ['dds_products_response' => $aResult]
            );
        }

        $aResult = $aResult['result'];
        if (P_ERRNO_STOPOVER_POINTS_CONFLICTS_WITH_SCENES == $aResult['errno']) {
            $sMsg = Config::text('errno', 'stopover_points_unsupport_scene_msg_6_0');
            throw new ExceptionWithResp(
                Code::E_COMMON_AREA_NOT_OPEN_SERVICE,
                RespCode::P_AIRPORT_STATION_NOT_SUPPORT_STOPOVER_POINTS_ERROR,
                $sMsg,
                ['district' => $oCommonInfo->getDistrict()]
            );
        }

        if (GLOBAL_SUCCESS != $aResult['errno']) {
            throw new InvalidArgumentException(Code::E_DDS_GET_PRODUCTS_FAIL, ['ddsResult' => $aResult]);
        }

        $aBaseProductList = [];
        if (GLOBAL_SUCCESS == $aResult['errno'] && !empty($aResult['data']) && !empty($aResult['data']['product_list'])) {
            $aBaseProductList = $aResult['data']['product_list'];
        }

        return $aBaseProductList;
    }

    /**
     * @throws InterRouteException InterRouteException
     * @return void
     */
    private function _dealInterCityException() {
        if (!isset($this->_aBaseProductList[0]['order_info']['order_extra_info'])) {
            return;
        }

        $aOrderExtraInfo = $this->_aBaseProductList[0]['order_info']['order_extra_info'];
        $aErrMsg         = \BizLib\Config::text('errno', 'pEstimatePrice_error_msg');
        if (isset($aOrderExtraInfo['carpool_extra_info']['intercity_errno']) && !empty($aOrderExtraInfo['carpool_extra_info']['intercity_errno'])) {
            $aReturn = \BizLib\Utils\Common::getErrMsg($aOrderExtraInfo['carpool_extra_info']['intercity_errno'], $aErrMsg, PRODUCT_ID_FAST_CAR);
            $oEx     = new InterRouteException($aReturn['errmsg']);// 跨城拼车抛出基于场景异常
            $oEx->setRespCode($aReturn['errno'])->setRespMsg($aReturn['errmsg']);
            throw $oEx;
        }
    }


    /**
     * 判断是否重写orderType
     * @return void
     */
    private function _buildOrderType() {
        if ('' != $this->_oRequest->getDepartureRange()) {
            $aDepartureRange = json_decode($this->_oRequest->getDepartureRange());
            if (2 == count($aDepartureRange)) {
                $iOrderType = -1;
                $iTime      = time();
                if ($iTime < $aDepartureRange[0]) {
                    $iOrderType = OrderSystem::TYPE_ORDER_BOOKING;
                }

                if ($iTime > $aDepartureRange[0] && $iTime < $aDepartureRange[1]) {
                    $iOrderType = OrderSystem::TYPE_ORDER_NOW;
                }

                if (-1 != $iOrderType && $iOrderType != $this->_oRequest->getOrderType()) {
                    NuwaLog::warning('pIntercityEstimateOrderTypeUpdate');
                    $this->_oRequest->setOrderType($iOrderType);
                }
            }
        }
    }


    /**
     * 低版本App默认个人支付
     * @return void
     */
    private function _buildPaymentsType() {
        $aInfo = [
            'common_info' => [
                'access_key_id' => $this->_oRequest->getAccessKeyId(),
                'app_version'   => $this->_oRequest->getAppVersion(),
            ],
        ];
        if (!\PreSale\Logics\intercityEstimatePrice\multiResponse\Util::isSupportBusinessPay($aInfo)) {
            $this->_oRequest->setPaymentsType(Order::BUSINESS_PAY_BY_PERSON_NEW);
        }
    }

    /**
     *
     * @return array
     */
    public function buildCommonInfo() {
        return [
            'is_reset_seat'           => $this->isResetSeat,
        ];
    }

    // 注释-待删
//    /**
//     * 设置用户预估座位数
//     * @param AreaInfo   $oAreaInfo   $oAreaInfo
//     * @param CommonInfo $oCommonInfo $oCommonInfo
//     * @return void
//     */
//    private function _fillUpOrderInfoCarpoolSeatNum($oAreaInfo, $oCommonInfo) {
//        $aSeatNums = [];
//        $aGroupIds = [];
//        // 如果iCarpoolSeatNum=1 可能是上次预估时用户选择的1或者第一次预估默认为1
//        // 这两种情况都不需要再额外请求最大座位数接口，因为默认1人是所有品类都支持的
//        foreach ($this->aProductList as $iIndex => $oProduct) {
//            if ($oProduct->oOrderInfo->iCarpoolSeatNum > 1) {
//                $aSeatNums[] = $oProduct->oOrderInfo->iCarpoolSeatNum;
//                if (!empty($oProduct->oOrderInfo->aMatchRoutes)
//                    && !empty($oProduct->oOrderInfo->aMatchRoutes[0]['route_group'])
//                ) {
//                    $aGroupIds[] = [
//                        'group_id'     => $oProduct->oOrderInfo->aMatchRoutes[0]['route_group'],
//                        'carpool_type' => $oProduct->oOrderInfo->iCarpoolType,
//                    ];
//                }
//            }
//        }
//
//        if (0 == count($aSeatNums)) {
//            return;
//        }
//
//        $oCarPool        = new CarpoolClient();
//        $aResSeatMaxNums = [];
//        foreach ($aGroupIds as $iIndex => $aGroupId) {
//            $aSeatConfig = $oCarPool->pGetSeatNum(
//                $oAreaInfo->iArea,
//                $aGroupId['group_id'],
//                0,
//                $oAreaInfo->iFromCounty,
//                $aGroupId['carpool_type'],
//                $oCommonInfo->iPageType
//            );
//            if (empty($aSeatConfig['result']['passenger_count_option'])) {
//                $aSeatConfig['result']['passenger_count_option'] = [1,2]; // 默认设置为2兜底
//                NuwaLog::warning('get intercity seat num from carpool failed');
//            }
//
//            // 和拼车沟通 不使用max_seat_num字段来判断最大用户可选座位数，而使用passenger_count_option里的最后一项更为准确
//            $aPassengerCountOption = $aSeatConfig['result']['passenger_count_option'];
//            $aResSeatMaxNums[]     = end($aPassengerCountOption);
//        }
//
//        //最大座位数中的最小值
//        $iSeatMinNum = 999;
//        foreach ($aResSeatMaxNums as $iIndex => $iMaxNum) {
//            if ($iSeatMinNum > $iMaxNum) {
//                $iSeatMinNum = $iMaxNum;
//            }
//        }
//
//        // 用户上传的座位数中的最大值
//        $iUserMaxNum = -1;
//        foreach ($aSeatNums as $iIndex => $iSeatNum) {
//            if ($iUserMaxNum < $iSeatNum) {
//                $iUserMaxNum = $iSeatNum;
//            }
//        }
//
//        if ($iUserMaxNum > $iSeatMinNum) {
//            // 如果用户上传的座位数中的最大值比最大座位数中的最小值大,此时设置为1请求下游，否则不变
//            foreach ($this->aProductList as $iIndex => &$oProduct) {
//                $oProduct->oOrderInfo->iCarpoolSeatNum = 1;
//            }
//        }
//
//        return;
//    }

    /**
     * 设置用户预估座位数
     * @param Request $oReq $oReq
     * @return void
     */
    private function _getKeqiSkuProduct($oReq) {
        if (empty($this->aProductList) || !is_array($this->aProductList)) {
            return;
        }

        foreach ($this->aProductList as $product) {
            $aBaseData = [
                'city'             => $product->oAreaInfo->iArea,
                'phone'            => $product->oPassengerInfo->sPhone,
                'product_id'       => $product->oOrderInfo->iProductId,
                'combo_type'       => $product->oOrderInfo->iComboType,
                'carpool_type'     => $product->oOrderInfo->iCarpoolType,
                'access_key_id'    => $product->oCommonInfo->iAccessKeyID,
                'combo_id'         => $product->oOrderInfo->iComboId,
                'route_group'      => $product->oOrderInfo->iRouteGroup,
                'is_sku_model'     => $product->oOrderInfo->iIsSkuModel,
                'product_category' =>$product->oOrderInfo->iProductCategory,
            ];

            if (TripCloudApolloConf::isTCIntercityCarpoolByProductID($product->oOrderInfo->iProductId) && Util::isSkuModelNoSelf($aBaseData)) {
                if (!empty($oReq->getSeatDetailInfo())) {
                    $seatDetailInfo = json_decode($oReq->getSeatDetailInfo(), true);

                    $seatList = [];
                    if (!empty($seatDetailInfo) && is_array($seatDetailInfo)) {
                        foreach ($seatDetailInfo as $seatItem) {
                            $seatList[] = [
                                'type'            => $seatItem['type'],
                                'passenger_count' => $seatItem['passenger_count'],
                                'is_occupy_seat'  => 1,
                            ];
                        }
                    }

                    $product->oOrderInfo->oSeatDetailInfo = $seatList;
                }

                $this->_oKeqiSkuProduct = $product;
                return;
            }
        }
    }

    private function _getCarpoolIntercityRule() {
        if (empty($this->_oKeqiSkuProduct)) {
            return;
        }

        CarpoolIntercityRule::getInstance()->fetchChildTicketRule($this->_oKeqiSkuProduct);
    }

    private function _checkCarpoolSeatNum() {
        if (empty($this->_oKeqiSkuProduct) || empty($this->_oKeqiSkuProduct->oOrderInfo->oSeatDetailInfo)) {
            return true;
        }

        if (empty($this->_oRouteDetailData)) {
            return true;
        }

        $seatMap = [];
        $seatDetailInfo = $this->_oKeqiSkuProduct->oOrderInfo->oSeatDetailInfo;
        $maxSeatNum = $this->_oKeqiSkuProduct->oOrderInfo->iMaxSeatNum;
        if ($maxSeatNum <= 0) {
            return true;
        }

        foreach ($seatDetailInfo as  $seatItem) {
            $seatMap[$seatItem['type']] = $seatItem['passenger_count'];
        }

        if (!isset($seatMap[1]) && !isset($seatMap[2]) && !isset($seatMap[3])) {
            return true;
        }

        if ($seatMap[1] < 0 || $seatMap[2] < 0 || $seatMap[3] < 0) {
            return false;
        }

        if ($seatMap[1] + $seatMap[2] + $seatMap[3] > $maxSeatNum) {
            return false;
        }

        if ($seatMap[3] > $seatMap[1]) {
            return false;
        }

        return true;
    }

    /**
     * 是否有儿童票逻辑
     * @param Request $request $request
     * @param array $passengerInfo $passengerInfo
     * @return bool
     */
    private function _isAllowChildTicket($request, $passengerInfo) {
        $params = [
            'access_key_id' => $request->getAccessKeyId(),
            'app_version' => $request->getAppVersion(),
            'uid'   => $passengerInfo['uid'],
            'pid'   => $passengerInfo['pid'],
            'phone' => $passengerInfo['phone'],
        ];

        $oToggle = Apollo::getInstance()->featureToggle(
            'gs_child_ticket_switch',
            $params
        );
        if ($oToggle->allow()) {
            return true;
        }

        return false;
    }
}
