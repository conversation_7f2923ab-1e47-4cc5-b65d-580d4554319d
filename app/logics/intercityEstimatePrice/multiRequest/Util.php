<?php
namespace PreSale\Logics\intercityEstimatePrice\multiRequest;

use BizLib\Utils\Language;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use Nebula\Exception\Route\InterRouteException;
use <PERSON><PERSON>\Apollo\Apollo as ApolloV2;

/**
 * Class Util
 * @package PreSale\Logics\intercityEstimatePrice\multiResponse
 */
class Util
{
    const INTER_AREA_NOT_OPEN_URL = 'https://v.didi.cn/k5aDxAY';

    /**
     * @throws InterRouteException InterRouteException
     * @return void
     */
    public static function handleUnOpenCity() {
        $oException = new InterRouteException('', Code::E_COMMON_AREA_NOT_OPEN_SERVICE);
        $oException->setRespCode(Code::E_COMMON_AREA_NOT_OPEN_SERVICE);
        throw $oException;
    }

    /**
     * 是否是库存模式(不包含城际自营的判断)
     * @param array   $baseData   $baseData
     * @return bool
     */
    public static function isSkuModelNoSelf($baseData) {
        $oToggle = ApolloV2::getInstance()->featureToggle(
            'intercity_carpool_sku_model_grayscale',
            [
                'timestamp'     => time(),
                'city'          => $baseData['city'],
                'phone'         => $baseData['phone'],
                'product_id'    => $baseData['product_id'],
                'combo_type'    => $baseData['combo_type'],
                'carpool_type'  => $baseData['carpool_type'],
                'access_key_id' => $baseData['access_key_id'],
                'combo_id'      => $baseData['combo_id'],
                'route_group'   => $baseData['route_group'],
                'source'        => 'pre-sale',
            ]
        );

        if ($oToggle->allow()) {
            return true;
        }

        return false;
    }
}
