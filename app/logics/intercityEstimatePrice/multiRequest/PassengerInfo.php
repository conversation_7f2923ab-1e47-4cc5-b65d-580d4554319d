<?php
namespace PreSale\Logics\intercityEstimate\multiRequest;

use BizCommon\Models\Passenger\Passenger;
use Dirpc\SDK\PreSale\IntercityEstimatePriceRequest as Request;

/**
 * Class PassengerInfo
 * @package PreSale\Logics\intercityEstimate\multiRequest
 */
class PassengerInfo
{
    /**
     * @var array 乘客信息
     */
    public $aPassengerInfo = [];

    /**
     * @var string 用户Token
     */
    public $sToken = '';

    /**
     * @var int 用户UID
     */
    public $iUid = 0;

    /**
     * @var int 用户PID
     */
    public $iPid = 0;

    /**
     * @var mixed|string 手机号
     */
    public $sPhone = '';

    /**
     * @var int 用户类型 1-普通用户 2-企业用户
     */
    public $iUserType = 0;

    /**
     * PassengerInfo constructor.
     * @param Request $oEstimateRequest   oEstimateRequest
     * @param array   $aPassportPassenger aPassportPassenger
     */
    public function __construct(Request $oEstimateRequest, array $aPassportPassenger) {
        if (empty($aPassportPassenger)) {
            $aPassportPassenger = Passenger::getInstance()->getPassengerByTokenFromPassport($oEstimateRequest->getToken());
        }

        if (empty($aPassportPassenger)) {
            return;
        }

        $this->aPassengerInfo = $aPassportPassenger;
        $this->iUid           = (int)$aPassportPassenger['uid'];
        $this->iPid           = (int)$aPassportPassenger['pid'];
        $this->sPhone         = $aPassportPassenger['phone'];
        $this->sToken         = $oEstimateRequest->getToken();
        $this->iUserType      = $oEstimateRequest->getUserType();
    }
}
