<?php
namespace PreSale\Logics\intercityEstimatePrice\multiRequest;

use BizLib\Client\PriceApiClient;
use BizCommon\Logics\Order\FieldOrderNTuple;
use BizLib\Exception\ExceptionWithResp;
use BizLib\ErrCode;
use BizCommon\Utils\Horae;
use PreSale\Logics\estimatePrice\EstimateDegradeCode;

/**
 * Class PriceLogic
 * @package PreSale\Logics\intercityEstimatePrice\multiRequest
 */
class PriceLogic
{
    /**
     * 支付类型
     */
//    const PAY_TYPE_BR  = 1;
//    const PAY_TYPE_NEW = 2;

    /**
     * @var Product[] ...$_aProductList
     */
    private $_aProductList = [];


    /**
     * PriceLogic constructor.
     * @param Product ...$aProductList 产品线参数
     */
    public function __construct(Product ...$aProductList) {
        $this->_aProductList = $aProductList;
    }


    /**
     * @return array
     * @throws ExceptionWithResp ...
     */
    public function getMultiResponse() {
        $aPriceParams = $this->_getMultiPriceParams();
        $aPriceRsp    = $this->_getMultiPriceByClient($aPriceParams);
        $aInfo        = $this->_buildAInfos($aPriceParams,$aPriceRsp);
        if (empty($aInfo)) {
            throw new ExceptionWithResp(
                ErrCode\Code::E_PRICE_REQUEST_FAIL,
                EstimateDegradeCode::R_ESTIMATE_DEGRADE_PRICE_EMPTY,
                '',
                ['price_params' => json_encode($aPriceParams)]
            );
        }

        return $aInfo;
    }

    /**
     * @return array
     */
    private function _getMultiPriceParams() {
        $aPriceParams = [];
        foreach ($this->_aProductList as $idx => $oProduct) {
            if ($oProduct->oOrderInfo->bRemoveFlag) {
                continue;
            }

            $aPriceParam    = $this->_assemblePriceParams($oProduct);
            $aPriceParams[] = $aPriceParam;
        }

        return $aPriceParams;
    }


    /**
     * @param array $aPriceParams price请求参数
     * @return array
     */
    private function _getMultiPriceByClient(array $aPriceParams) {
        $aParams    = [
            'estimatePriceReqs' => $aPriceParams,
            'caller'            => 'pIntercityEstimatePrice',
        ];
        $_oClient   = new PriceApiClient(PriceApiClient::MODULE_NAME);
        $aRet       = $_oClient->estimateLite($aParams);
        $aAthenaRet = array();
        if (isset($aRet['errno']) && 0 == $aRet['errno']) {
            return $aRet['data'];
        }

        return $aAthenaRet;
    }


    /**
     * @param array $aPriceReq PriceAPI请求
     * @param array $aPriceRsp PriceAPI返回
     * @return array
     * @throws ExceptionWithResp ...
     */
    private function _buildAInfos(array $aPriceReq, array $aPriceRsp) {
        $aRspData = [];
        foreach ($aPriceRsp as $iIndex => $aPrice) {
            if (empty($aPrice) || !is_array($aPrice) || (empty($aPrice['bill_info']) && empty($aPrice['activity_info']) && empty($aPrice['payment_info']))) {
                continue;
            }

            $aRspData[$iIndex] = $this->_assemblePriceRsp($aPrice);
        }

        return $this->_assembleAInfo($aPriceReq, $aRspData);
    }


    /**
     * @param array $aPriceReq 请求
     * @param array $aPriceRsp 响应
     * @return array
     */
    private function _assembleAInfo(array $aPriceReq, array $aPriceRsp) {
        $aRequestParams = [];
        foreach ($aPriceReq as $iIndex => $aParam) {
            $aParam['order_info']['real_type']   = $aParam['order_info']['order_type'];
            $aParam['common_info']['pixels']     = $this->_aProductList[0]->oCommonInfo->sPixels;
            $aParam['common_info']['agent_type'] = $this->_aProductList[0]->oCommonInfo->sAgentType;
            $aParam['common_info']['page_type']  = $this->_aProductList[0]->oOrderInfo->iPageType;
            if (isset($aParam['extra_info']['order'])) {
                $aOrderExtra = json_decode($aParam['extra_info']['order'], true);
                $aOrderInfo  = (array)$aParam['order_info'];
                if (!empty($aOrderExtra)) {
                    $aParam['order_info'] = $aOrderInfo + $aOrderExtra;
                }

                if (!empty($aOrderExtra['estimate_id'])) {
                    $aRequestParams[$aOrderExtra['estimate_id']] = $aParam;
                }
            }
        }

        $aResponseParams = [];
        foreach ($aPriceRsp as $iIndex => $aRsp) {
            $aBillInfo   = $aRsp['bill_info'];
            $sEstimateId = $aBillInfo['estimate_id'];
            $aReqParam   = $aRequestParams[$sEstimateId];
            if (empty($aReqParam)) {
                continue;
            }

            $aOrderInfo = $aReqParam['order_info'];

            // bill信息中增添路线信息
            $aBillInfo         = $this->_loadRouteInfoNew($aBillInfo, $aOrderInfo);
            $aResponseParam    = [
                'common_info'        => $aReqParam['common_info'],
                'order_info'         => $aOrderInfo,
                'passenger_info'     => json_decode($aReqParam['passenger_info'], true),
                'bill_info'          => $aBillInfo,
                'payments_info'      => $aRsp['payments_info'],
                'activity_info'      => $aRsp['activity_info'],
                'price_extra'        => $aRsp['price_extra'],
                'preference_product' => $aReqParam['extra_info']['preference_product'],
            ];
            $aResponseParams[] = $aResponseParam;
        }

        return $aResponseParams;
    }


    /**
     * @param array $aBillInfo  账单信息
     * @param array $aOrderInfo 订单信息
     * @return mixed
     */
    private function _loadRouteInfoNew($aBillInfo, $aOrderInfo) {
        // 新模式没有时间片组件
        if (Horae::isInterCityCarpool($aOrderInfo) && !Horae::isInterCarpoolGoRightNow($aOrderInfo)) {
            $aBillInfo['match_routes'] = $aOrderInfo['match_routes'][0];
            unset($aBillInfo['route_info']['time_span']);
        }

        return $aBillInfo;
    }


    /**
     * @param array $aPrice PriceItem
     * @return array
     */
    private function _assemblePriceRsp(array $aPrice) {
        $aBillInfo     = $aPrice['bill_info'];
        $aExtraInfo    = $aPrice['extra_info'];
        $aActivityInfo = $aPrice['activity_info'];
        $aUserTypeInfo = $aPrice['payment_info'];
        // activity转换为list，背景是一个产品多价格下需要有多个activity信息。兼容上线过程中不一致
        if (isset($aActivityInfo) && !empty($aActivityInfo) && !is_array($aActivityInfo[0])) {
            $aActivityInfo = [$aActivityInfo];
        }

        $aPriceItem = [
            'payments_info' => $aUserTypeInfo,
            'bill_info'     => $aBillInfo,
            'activity_info' => $aActivityInfo,
            'price_extra'   => $aExtraInfo,
        ];
        return $aPriceItem;
    }


    /**
     * @param  Product $oProduct $oProduct
     * @return array
     */
    private function _assemblePriceParams(Product $oProduct) {
        $aResult = [
            'common_info'         => [
                'business_id'    => $oProduct->oOrderInfo->iBusinessId,
                'access_key_id'  => $oProduct->oCommonInfo->iAccessKeyID,
                'app_version'    => $oProduct->oCommonInfo->sAppVersion,
                'client_type'    => $oProduct->oCommonInfo->iClientType,
                'lang'           => $oProduct->oCommonInfo->sLang,
                'origin_id'      => $oProduct->oCommonInfo->iOriginId,
                'terminal_id'    => $oProduct->oCommonInfo->iTerminalId,
                'imei'           => $oProduct->oCommonInfo->sImei,
                'is_from_b2b'    => $oProduct->oCommonInfo->bIsFromB2b,
                'is_from_webapp' => $oProduct->oCommonInfo->bIsFromWebApp,
                'from'           => $oProduct->oCommonInfo->sFrom,
                'platform_type'  => $oProduct->oCommonInfo->iPlatformType,
            ],
            'order_info'          => [
                'current_lng'       => $oProduct->oAreaInfo->fCurLng,
                'current_lat'       => $oProduct->oAreaInfo->fCurLat,
                'area'              => $oProduct->oAreaInfo->iArea,
                'from_lng'          => $oProduct->oAreaInfo->fFromLng,
                'from_lat'          => $oProduct->oAreaInfo->fFromLat,
                'from_poi_id'       => $oProduct->oAreaInfo->sFromPoiId,
                'from_poi_type'     => $oProduct->oAreaInfo->sFromPoiType,
                'from_address'      => $oProduct->oAreaInfo->sFromAddress,
                'from_name'         => $oProduct->oAreaInfo->sFromName,
                'starting_name'     => $oProduct->oAreaInfo->sStartingName,
                'to_lng'            => $oProduct->oAreaInfo->fToLng,
                'to_lat'            => $oProduct->oAreaInfo->fToLat,
                'to_poi_id'         => $oProduct->oAreaInfo->sToPoiId,
                'to_poi_type'       => $oProduct->oAreaInfo->sToPoiType,
                'to_address'        => $oProduct->oAreaInfo->sToAddress,
                'to_name'           => $oProduct->oAreaInfo->sToName,
                'dest_name'         => $oProduct->oAreaInfo->sDestName,
                'order_type'        => $oProduct->oOrderInfo->iOrderType,
                'channel'           => $oProduct->oCommonInfo->sChannel,
                'combo_type'        => $oProduct->oOrderInfo->iComboType,
                'departure_time'    => $oProduct->oOrderInfo->iDepartureTime,
                'map_type'          => $oProduct->oAreaInfo->sMapType,
                'product_id'        => $oProduct->oOrderInfo->iProductId,
                'require_level'     => $oProduct->oOrderInfo->iRequireLevel,
                'district'          => $oProduct->oAreaInfo->iDistrict,
                'payments_type'     => $oProduct->oOrderInfo->iPaymentsType,
                'combo_id'          => $oProduct->oOrderInfo->iComboId,
                'carpool_seat_num'  => $oProduct->oOrderInfo->iCarpoolSeatNum,

                'abstract_district' => $oProduct->oAreaInfo->sAbstractDistrict,
                'user_type'         => $oProduct->oPassengerInfo->iUserType,
                'remove_flag'       => $oProduct->oOrderInfo->bRemoveFlag,
                'seat_detail_info'  => $oProduct->oOrderInfo->oSeatDetailInfo,
            ],
            'passenger_info'      => json_encode($oProduct->oPassengerInfo->aPassengerInfo, JSON_UNESCAPED_UNICODE),
            // 以下这两个参数是为防止price-api报warning，垃圾参数
            'custom_service_info' => '[]',
            'one_key_activity'    => json_encode(['activity_switch' => false], JSON_UNESCAPED_UNICODE),
        ];

        $aOrderInfo = $oProduct->oOrderInfo->toArray();
        $aNTuple    = FieldOrderNTuple::getOrderNTupleByOrder($aOrderInfo);
        $aOrderInfo['n_tuple'] = $aNTuple;
        $aExtraOrderInfo       = array_diff_key($aOrderInfo, $aResult['order_info']);
        //按需补充order_extra
        $aExtraOrderInfo['access_key_id'] = $oProduct->oCommonInfo->iAccessKeyID;
        $aExtraOrderInfo['to_area']       = $oProduct->oAreaInfo->iToArea;
        $aExtraOrderInfo['county']        = $oProduct->oAreaInfo->iFromCounty;
        $aExtraOrderInfo['to_county']     = $oProduct->oAreaInfo->iToCounty;

        $aResult['extra_info'] = [
            'order'            => json_encode($aExtraOrderInfo, JSON_UNESCAPED_UNICODE),
            'product_category' => $oProduct->oOrderInfo->iProductCategory,
        ];

        return $aResult;
    }
}
