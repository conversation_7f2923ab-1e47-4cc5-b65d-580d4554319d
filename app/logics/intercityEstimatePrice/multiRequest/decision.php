<?php
namespace PreSale\Logics\intercityEstimate\multiRequest;

use PreSale\Logics\intercityEstimatePrice\multiRequest\Util;
use PreSale\Models\strategy\DecisionService;
use Disf\SPL\Trace;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Utils\Language;
use Nebula\Exception\Route\InterRouteException;

/**
 * Class Decision
 */
class Decision
{
    const INTER_AREA_NOT_OPEN_URL = 'https://v.didi.cn/k5aDxAY';

    const DECISION_TYPE_DEFAULT = 1;

    /**
     * @var Decision 单例
     */
    private static $_oInstance = null;


    /**
     * @var array ...
     */
    private $_aInfos = [];


    /**
     * @var array Decision Rsp
     */
    private $_aDecisionRsp = [];


    /**
     * Decision constructor.
     */
    private function __construct() {}


    /**
     * @return Decision
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }


    /**
     * @param array $aInfos ...
     * @return void
     */
    public function initDecision(array $aInfos) {
        $this->_aInfos       = $aInfos;
        $this->_aDecisionRsp = (new DecisionService())->getDecisionResult($this->_assembleDecisionReq());
    }


    /**
     * @return array
     */
    public function getDecisionRsp() {
        return $this->_aDecisionRsp;
    }


    /**
     * @return array
     */
    private function _assembleDecisionReq() {
        $aDecisionReq = array();
        if (empty($this->_aInfos)) {
            return $aDecisionReq;
        }

        $fGetUserInfoAndCommonInfo    = function (array $aInfos) {
            $aInfo          = current($aInfos);
            $aPassengerInfo = $aInfo['passenger_info'];
            $aOrderInfo     = $aInfo['order_info'];
            $aCommonInfo    = $aInfo['common_info'];
            $aUserInfo      = [
                'id'    => (int)$aPassengerInfo['pid'],
                'uid'   => (int)$aPassengerInfo['uid'],
                'pid'   => (int)$aPassengerInfo['pid'],
                'phone' => (string)$aPassengerInfo['phone'],
                'token' => (string)\Nuwa\Core\Dispatcher::getInstance()->getRequest()->fetchGetPost('token', false),
            ];
            $aCommonInfo    = [
                'estimate_trace_id' => Trace::traceId(),
                'lang'              => (string)Language::getLanguage(),
                'start_lat'         => (float)($aOrderInfo['from_lat']),
                'start_lng'         => (float)($aOrderInfo['from_lng']),
                'to_lat'            => (float)($aOrderInfo['to_lat']),
                'to_lng'            => (float)($aOrderInfo['to_lng']),
                'client_type'       => (int)($aCommonInfo['client_type']),
                'city'              => (int)($aOrderInfo['area']),
                'to_city'           => (int)($aOrderInfo['to_area']),
                'county'            => (int)($aOrderInfo['county']),
                'menu_id'           => (string)($aOrderInfo['menu_id']),
                'app_version'       => (string)($aCommonInfo['app_version']),
                'channel'           => (int)($aOrderInfo['channel']),
                'map_type'          => (string)($aOrderInfo['map_type']),
                'order_type'        => (string)($aOrderInfo['order_type']),
                'pixels'            => (string)($aCommonInfo['pixels']),
                'access_key_id'     => (int)($aCommonInfo['access_key_id']),
                'page_type'         => (int)($aCommonInfo['page_type']),
            ];
            return [$aUserInfo, $aCommonInfo];
        };
        list($aUserInfo,$aCommonInfo) = $fGetUserInfoAndCommonInfo($this->_aInfos);

        $aProductInfo = [];
        foreach ($this->_aInfos as $aInfo) {
            $aOrderNTuple = $aInfo['order_info']['n_tuple'];

            $aProductItem = [];
            $aProductItem['product_id']            = (int)$aInfo['order_info']['product_id'];
            $aProductItem['business_id']           = (int)$aInfo['order_info']['business_id'];
            $aProductItem['require_level']         = (int)$aInfo['order_info']['require_level'];
            $aProductItem['combo_type']            = (int)$aInfo['order_info']['combo_type'];
            $aProductItem['carpool_type']          = (int)($aOrderNTuple['carpool_type']) ?? 0;
            $aProductItem['route_type']            = (int)($aOrderNTuple['route_type']) ?? 0;
            $aProductItem['level_type']            = (int)($aOrderNTuple['level_type'] ?? 0);
            $aProductItem['is_dual_carpool_price'] = $aOrderNTuple['is_dual_carpool_price'] ?? false;
            $aProductItem['is_special_price']      = $aOrderNTuple['is_special_price'];
            $aProductItem['estimate_id']           = (string)($aInfo['bill_info']['estimate_id']);
            $aProductItem['pre_total_fee']         = 0;
            $aProductItem['cap_price']        = (float)$aInfo['bill_info']['cap_price'];
            $aProductItem['product_category'] = (int)$aInfo['order_info']['product_category'];
            $aProductItem['estimate_fee']     = (float)($aInfo['activity_info'][0]['estimate_fee']);
            $aProductItem['driver_metre']     = (int)$aInfo['bill_info']['driver_metre'];
            $aProductInfo[] = $aProductItem;
        }

        $aDecisionReq = [
            'user'          => $aUserInfo,
            'common'        => $aCommonInfo,
            'products'      => $aProductInfo,
            'decision_type' => self::DECISION_TYPE_DEFAULT,
        ];
        return $aDecisionReq;
    }



    /**
     * @param array $aInfos ...
     * @return array
     * @throws InterRouteException ...
     */
    public function filterAInfo(array $aInfos) {
        $aRsp = $this->getDecisionRsp();
        if (empty($aRsp) || empty($aRsp['product_list'])) {
            return $aInfos;
        }

        $aInfoRst = [];
        foreach ($aRsp['product_list'] as $aProduct) {
            foreach ($aInfos as $aInfo) {
                if (!$this->_checkSeatNumValid($aInfo)) {
                    continue;
                }

                $iProductCategory = $aInfo['order_info']['product_category'];
                if ($aProduct['product_category'] == $iProductCategory && false == $aProduct['remove_flag']) {
                    $aInfoRst[] = $aInfo;
                }
            }
        }

        if (empty($aInfoRst)) {
            Util::handleUnOpenCity();
        }

        return $aInfoRst;
    }

    /**
     * 检查用户选择的座位数是否超过了限制
     * @param array $aInfo $aInfo
     * @return bool
     */
    private function _checkSeatNumValid($aInfo) : bool {
        $iMaxSeatNum = count($aInfo['bill_info']['carpool_seat_config']);
        $iUserSelect = $aInfo['order_info']['carpool_seat_num'];
        if ($iUserSelect > $iMaxSeatNum) {
            return false;
        }

        return true;
    }
}

























