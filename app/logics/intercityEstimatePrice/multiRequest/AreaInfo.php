<?php
namespace PreSale\Logics\intercityEstimate\multiRequest;

use BizLib\Utils\Address;
use BizLib\Utils\MapHelper;
use Dirpc\SDK\PreSale\IntercityEstimatePriceRequest as Request;

/**
 * 地理位置相关
 * Class AreaInfo
 * @package PreSale\Logics\intercityEstimate\multiRequest
 */
class AreaInfo
{
    //当前经纬度
    public $fCurLat;
    public $fCurLng;
    //起点经纬度
    public $fFromLat;
    public $fFromLng;
    public $sFromPoiId;
    public $sFromPoiType;
    public $sFromAddress;
    public $sFromName;
    public $iFromCounty;
    //终点经纬度
    public $fToLat;
    public $fToLng;
    public $sToPoiId;
    public $sToPoiType;
    public $sToAddress;
    public $sToName;
    public $iToCounty;

    public $sMapType;
    public $iDestPoiCode;
    public $sDestPoiTag;
    public $iArea;
    public $iToArea;
    public $iDistrict;

    public $sStartingName;
    public $sDestName;
    public $sAbstractDistrict;


    /**
     * AreaInfo constructor.
     * @param Request $oEstimateRequest oEstimateRequest
     * @param array   $aAreaInfo        aAreaInfo
     * @throws \Exception bizLib 中 multiAreaInfoByCoord 抛出的异常，奇奇怪怪
     */
    public function __construct(Request $oEstimateRequest, array $aAreaInfo) {
        $this->fCurLat      = $oEstimateRequest->getLat();
        $this->fCurLng      = $oEstimateRequest->getLng();
        $this->fFromLat     = $oEstimateRequest->getFromLat();
        $this->fFromLng     = $oEstimateRequest->getFromLng();
        $this->sFromPoiId   = $oEstimateRequest->getFromPoiId();
        $this->sFromPoiType = $oEstimateRequest->getFromPoiType();
        $this->sFromAddress = $oEstimateRequest->getFromAddress();
        $this->sFromName    = $oEstimateRequest->getFromName();

        $this->fToLat     = $oEstimateRequest->getToLat();
        $this->fToLng     = $oEstimateRequest->getToLng();
        $this->sToPoiId   = $oEstimateRequest->getToPoiId();
        $this->sToPoiType = $oEstimateRequest->getToPoiType();
        $this->sToAddress = $oEstimateRequest->getToAddress();
        $this->sToName    = $oEstimateRequest->getToName();

        if ($this->sFromAddress || $this->sFromName) {
            Address::merge($this->sStartingName,$this->sFromName,$this->sFromAddress);
        }

        if ($this->sToAddress || $this->sToName) {
            Address::merge($this->sDestName,$this->sToName,$this->sToAddress);
        }

        $this->sMapType = $oEstimateRequest->getMaptype();
        $this->_buildAreaInfo($aAreaInfo);
    }

    /**
     * 构建出发和终点信息
     * @param array $aAreaInfo aAreaInfo
     * @throws \Exception Exception
     * @return void
     */
    private function _buildAreaInfo(array $aAreaInfo) {
        if (!empty($aAreaInfo['from_area'])) {
            $aFromArea = $aAreaInfo['from_area'];
        } else {
            $aFromArea = MapHelper::getAreaInfoByLoc($this->fFromLng, $this->fFromLat);
        }

        if (!empty($aFromArea)) {
            $this->iArea       = $aFromArea['id'];
            $this->iDistrict   = $aFromArea['district'];
            $this->iFromCounty = $aFromArea['countyid'];

            $this->sAbstractDistrict = $this->iDistrict.','.$this->iFromCounty;
        }

        if (!empty($aAreaInfo['to_area'])) {
            $aToArea = $aAreaInfo['to_area'];
        } else {
            $aToArea = MapHelper::getAreaInfoByLoc($this->fToLng, $this->fToLat);
        }

        if (!empty($aToArea)) {
            $this->iToArea   = $aToArea['id'];
            $this->iToCounty = $aToArea['countyid'];
        }
    }
}
