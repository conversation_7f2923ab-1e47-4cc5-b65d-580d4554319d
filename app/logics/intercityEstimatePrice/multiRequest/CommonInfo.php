<?php
namespace PreSale\Logics\intercityEstimate\multiRequest;

use Dirpc\SDK\PreSale\IntercityEstimatePriceRequest as Request;
use BizLib\Utils as BizUtils;
use Nuwa\ApolloSDK\Apollo as NuwaApollo;

/**
 * Class CommonInfo
 * @package PreSale\Logics\intercityEstimate\multiRequest
 */
class CommonInfo
{
    //端版本
    public $sAppVersion;

    //端类型client_type
    public $iClientType;

    //端来源
    public $iAccessKeyID;

    //端语言
    public $sLang;

    //渠道号
    public $sChannel;

    //设备分辨率
    public $sPixels;

    //来源id
    public $iOriginId;

    //终端id
    public $iTerminalId;

    //设备号
    public $sImei;

    //b2b来源
    public $bIsFromB2b;

    //webApp来源
    public $bIsFromWebApp;

    //来源
    public $sFrom;

    //暂时还有用到
    public $iPlatformType;

    //prefer路线id
    public $sRouteId;

    //二次预估 上传用户选中产品，及需要分品类配置的
    public $aMultiRequireProducts = [];

    //场景全页面类型
    public $iPageType;

    // 是否为客企扫二维码预估请求
    public $sAgentType;

    /**
     * CommonInfo constructor.
     * @param Request $oEstimateRequest 客户端入参
     */
    public function __construct(Request $oEstimateRequest) {
        $this->sAppVersion   = $oEstimateRequest->getAppVersion();
        $this->sLang         = $oEstimateRequest->getLang();
        $this->sChannel      = $oEstimateRequest->getChannel();
        $this->iClientType   = $oEstimateRequest->getClientType();
        $this->iAccessKeyID  = $oEstimateRequest->getAccessKeyId();
        $this->sPixels       = $oEstimateRequest->getPixels();
        $this->iOriginId     = $oEstimateRequest->getOriginId();
        $this->iTerminalId   = $oEstimateRequest->getTerminalId();
        $this->sImei         = $oEstimateRequest->getImei();
        $this->iPlatformType = $oEstimateRequest->getPlatformType();
        $this->bIsFromB2b    = BizUtils\Common::fromB2B($this->iClientType);
        $this->bIsFromWebApp = BizUtils\Common::fromWebapp($this->iClientType);
        $this->sFrom         = $oEstimateRequest->getFrom();
        $this->iPageType     = $oEstimateRequest->getPageType();
        $this->_buildMultiRequireProducts($oEstimateRequest);
        $this->sAgentType = $oEstimateRequest->getAgentType();
    }


    /**
     * multiRequireProduct 是端用户上传分品类的参数的字段；以json形式上传，反序列化后为一个列表，其中product_category 用于标识品类
     * 此方法用于反序列化multiRequireProduct，并构建map 方便内部参数构建的索引
     * @param Request $oEstimateRequest $oEstimateRequest
     * @return void
     */
    private function _buildMultiRequireProducts(Request $oEstimateRequest) {
        $sMultiStr  = $oEstimateRequest->getMultiRequireProduct();
        $aMultiInfo = json_decode($sMultiStr, true);
        $this->aMultiRequireProducts = [];
        if (!empty($aMultiInfo)) {
            foreach ($aMultiInfo as $aReq) {
                if (!empty($aReq['product_category'])) {
                    $this->aMultiRequireProducts[(int)$aReq['product_category']] = $aReq;
                }
            }
        }
    }
}
