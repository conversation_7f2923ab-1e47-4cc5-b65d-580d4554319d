<?php
namespace PreSale\Logics\intercityEstimatePrice\multiRequest;

use Dirpc\SDK\PreSale\IntercityEstimatePriceRequest as Request;
use BizLib\Constants\Horae;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\intercityEstimate\multiRequest\AreaInfo;
use PreSale\Logics\intercityEstimate\multiRequest\CommonInfo;
use PreSale\Logics\intercityEstimate\multiRequest\OrderInfo;
use PreSale\Logics\intercityEstimate\multiRequest\PassengerInfo;
use PreSale\Logics\scene\custom\CustomLogic;

/**
 * Class Product
 */
class Product
{
    /**
     * @var Request Request
     */
    private $_oRequest;

    /**
     * @var OrderInfo $oOrderInfo
     */
    public $oOrderInfo;

    /**
     * @var CommonInfo $oCommonInfo
     */
    public $oCommonInfo;

    /**
     * @var PassengerInfo $oPassengerInfo
     */
    public $oPassengerInfo;

    /**
     * @var AreaInfo $oAreaInfo
     */
    public $oAreaInfo;

    /**
     * Product constructor.
     * @param Request $oRequest request
     * @return void
     */
    public function __construct(Request $oRequest) {
        $this->_oRequest = $oRequest;
    }

    /**
     * @param array $aBaseProduct dds返回的基础oneconf数据及order_extra数据
     * @return void
     */
    public function buildOrderInfo($aBaseProduct) {
        $oOrderInfo = new OrderInfo($this->_oRequest);
        $oOrderInfo->fillUpOrderInfo($aBaseProduct, $this->oCommonInfo);
        $this->oOrderInfo = $oOrderInfo;
    }


    /**
     * @param array $aBasePassengerInfo 基础用户信息
     * @return void
     */
    public function buildPassengerInfo($aBasePassengerInfo) {
        $oPassenger           = new PassengerInfo($this->_oRequest, $aBasePassengerInfo);
        $this->oPassengerInfo = $oPassenger;
    }

    /**
     * @param CommonInfo $oCommonInfo CommonInfo公用
     * @return void
     */
    public function buildCommonInfo(CommonInfo $oCommonInfo) {
        $this->oCommonInfo = $oCommonInfo;
    }

    /**
     * @param AreaInfo $oAreaInfo AreaInfo公用
     * @return void
     */
    public function buildAreaInfo(AreaInfo $oAreaInfo) {
        $this->oAreaInfo = $oAreaInfo;
    }
}
