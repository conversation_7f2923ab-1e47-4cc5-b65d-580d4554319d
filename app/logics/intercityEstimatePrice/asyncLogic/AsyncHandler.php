<?php
namespace PreSale\Logics\intercityEstimate\asyncLogic;

use BizLib\Utils\Horae;
use BizLib\Utils\Language;
use BizLib\Utils\PublicLog;
use Disf\SPL\Trace;
use PreSale\Logics\carpool\InterCity;
use PreSale\Logics\estimatePrice\bill\CommonBillLogic;
use PreSale\Logics\intercityEstimatePrice\multiRequest\Product;
use Dirpc\SDK\PreSale\IntercityEstimatePriceRequest as Request;
use PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender\IntercityBookingSkuRender;
use PreSale\Models\carrera\PassengerEstimateTopic;
use PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender\BookingCompensateLogic;

/**
 * Class AsyncHandler
 * @package PreSale\Logics\intercityEstimate\asyncLogic
 */
class AsyncHandler
{
    // 客企扫二维码预估标识
    const AGENT_TYPE_KEQI = 'keqi_scan';

    /**
     * @var array
     */
    private $_aInfos;

    /**
     * @var array com_request_out 数据
     */
    private $_aResponseInfo;

    /**
     * @var Request 元请求数据
     */
    private $_oRequest = null;

    /**
     * @var Product[] 产品线数据
     */
    private $_aProductList = [];

    /**
     * PublicLogHandler constructor.
     * @param Request $oRequest        元请求
     * @param array   $aInfos          品类数据
     * @param array   $aResponseInfo   接口响应
     * @param Product ...$aProductList 产品数据
     */
    public function __construct(Request $oRequest, array $aInfos, array $aResponseInfo, Product ...$aProductList) {
        $this->_oRequest      = $oRequest;
        $this->_aInfos        = $aInfos;
        $this->_aResponseInfo = $aResponseInfo;
        $this->_aProductList  = $aProductList;
    }


    /**
     * 写多预估流程的public 日志.
     * @return void
     */
    public function multiWritePublicLog() {
        // 写表单展示日志
        $this->_writeOrderEstimateInfo();
        // 写预估public日志
        $this->_writeOrderEstimatePrice();
    }


    /**
     * 批量写kafka
     * @return void
     */
    public function batchWriteKafka() {
        $aBatchData = [];
        foreach ($this->_aInfos as $aInfo) {
            $aCurEstimateData = [];
            foreach ($this->_aResponseInfo['data']['estimate_data'] as $aData) {
                if ($aData['estimate_id'] == $aInfo['order_info']['estimate_id']) {
                    $aCurEstimateData = $aData;
                }
            }

            if (!empty($aCurEstimateData)) {
                $aData = $this->_assembleKafkaData($aInfo, $aCurEstimateData);
                !empty($aData) && $aBatchData[] = $aData;
            }
        }

        if (!empty($aBatchData)) {
            (new PassengerEstimateTopic())->batchSync($aBatchData);
        }
    }


    /**
     * 此前依据老的aInfo记录日志容易出错，新增一份依据新的aInfo记录的日志
     * 且仅记录预估阶段最终会展示的产品线
     * @return void
     */
    private function _writeOrderEstimateInfo() {
        $aIndexedInfo = [];
        foreach ($this->_aInfos as $aInfo) {
            $iEstimateId = $aInfo['bill_info']['estimate_id'];
            $aIndexedInfo[$iEstimateId] = $aInfo;
        }

        foreach ($this->_aResponseInfo['data']['estimate_data'] as $aResponse) {
            $iEstimateId      = $aResponse['estimate_id'];
            $aInfo            = $aIndexedInfo[$iEstimateId];
            $sCarLevel        = $aInfo['order_info']['require_level'];
            $aBillProductInfo = $aInfo['bill_info']['product_infos'][$sCarLevel];
            $aEstimateStatistic = [
                'opera_stat_key'    => 'g_order_estimate_info',
                'pLang'             => $this->_oRequest->getLang() ?? Language::ZH_CN,
                'estimate_id'       => $iEstimateId,
                'area'              => $aInfo['order_info']['area'],
                'product_id'        => $aInfo['order_info']['product_id'],
                'require_level'     => $sCarLevel,
                'origin_combo_type' => $aInfo['order_info']['combo_type'],
                'combo_type'        => $aBillProductInfo['combo_type'], //因为账单可能修改combo_type，此处特别记录一下请求中的combo_type
                'is_default'        => $aResponse['is_default'],
                'select_type'       => $aResponse['select_type'],
            ];

            if (!empty($aResponse['price_info_desc'][0]['content'])) {
                // price_info_desc 第0项, 如果存在春节服务费,一定在第0项
                $aEstimateStatistic['price_info_desc_0_content'] = $aResponse['price_info_desc'][0]['content'];
            }

            PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);
        }
    }


    /**
     * 品类预估数据
     * @return void
     */
    private function _writeOrderEstimatePrice() {
        foreach ($this->_aInfos as $aInfo) {
            if (empty($aInfo['bill_info']['estimate_id'])) {
                continue;
            }

            $oCurProduct = null;
            $iEstimateID = $aInfo['order_info']['estimate_id'];
            foreach ($this->_aProductList as $oProduct) {
                if ($iEstimateID == $oProduct->oOrderInfo->sEstimateID) {
                    $oCurProduct = $oProduct;
                    break;
                }
            }

            $this->_writePublicLog($aInfo, $oCurProduct, $this->_aResponseInfo);
        }
    }


    /**
     * @param array   $aInfo          $aInfo
     * @param Product $oProduct       $oProduct
     * @param array   $aResponseInfos $aResponseInfos
     * @return void
     */
    private function _writePublicLog(array $aInfo, Product $oProduct, array $aResponseInfos) {
        $sCarLevel         = $aInfo['order_info']['require_level'];
        $aBillInfo         = $aInfo['bill_info'];
        $aEstimateData     = $this->_getEstimateData($aInfo, $aResponseInfos);
        $aBillDisplayLines = CommonBillLogic::formatDisplayLines($aBillInfo['display_lines']);

        $aEstimateStatistic = [
            'opera_stat_key'     => 'g_order_estimate_price',
            'imei'               => $oProduct->oCommonInfo->sImei,
            'appversion'         => $oProduct->oCommonInfo->sAppVersion,
            'client_type'        => $oProduct->oCommonInfo->iClientType,
            'access_key_id'      => $oProduct->oCommonInfo->iAccessKeyID,
            'channel'            => $oProduct->oCommonInfo->sChannel,
            'pLang'              => $oProduct->oCommonInfo->sLang,
            'pid'                => $oProduct->oPassengerInfo->iPid,
            'phone'              => $oProduct->oPassengerInfo->sPhone,
            'biz_user_type'      => $oProduct->oPassengerInfo->iUserType,
            'menu_id'            => $oProduct->oOrderInfo->sMenuID,
            'page_type'          => $oProduct->oOrderInfo->iPageType,
            'product_id'         => $oProduct->oOrderInfo->iProductId,
            'combo_type'         => $oProduct->oOrderInfo->iComboType,
            'require_level'      => $sCarLevel,
            'area'               => $oProduct->oAreaInfo->iArea,
            'to_area'            => $oProduct->oAreaInfo->iToArea,
            'district'           => $oProduct->oAreaInfo->iDistrict,
            'flng'               => $oProduct->oAreaInfo->fFromLng,
            'flat'               => $oProduct->oAreaInfo->fFromLat,
            'tlng'               => $oProduct->oAreaInfo->fToLng,
            'tlat'               => $oProduct->oAreaInfo->fToLat,
            'current_lng'        => $oProduct->oAreaInfo->fCurLng,
            'current_lat'        => $oProduct->oAreaInfo->fCurLat,
            'county'             => $oProduct->oAreaInfo->iFromCounty,
            'to_county'          => $oProduct->oAreaInfo->iToCounty,
            'from_name'          => str_replace(PHP_EOL, '', $oProduct->oAreaInfo->sFromName),
            'to_name'            => str_replace(PHP_EOL, '', $oProduct->oAreaInfo->sToName),
            'carpool_seat_num'   => $oProduct->oOrderInfo->iCarpoolSeatNum,
            'order_type'         => $oProduct->oOrderInfo->iOrderType,
            'time_cost'          => $aBillInfo['driver_minute'],
            'driver_metre'       => $aBillInfo['driver_metre'] ?? 0,
            'total_fee'          => $aBillInfo['total_fee'] ?? 0,
            'pre_total_fee'      => $aBillInfo['pre_total_fee'] ?? 0,
            'dynamic_total_fee'  => $aBillInfo['dynamic_total_fee'] ?? 0,
            'cap_price'          => $aBillInfo['cap_price'] ?? 0,
            'estimate_fee'       => $aInfo['activity_info'][0]['estimate_fee'] ?? 0,
            'is_carpool_open'    => (int) ($aBillInfo['is_carpool_open']),
            'discount_fee'       => $aInfo['activity_info'][0]['discount_fee'],
            'member_level_id'    => $aInfo['passenger_info']['member_profile']['level_id'] ?? 0,
            'order_n_tuple'      => json_encode($aInfo['order_info']['n_tuple'] ?? []),
            'combo_id'           => $oProduct->oOrderInfo->iComboId,
            'is_special_price'   => $oProduct->oOrderInfo->iIsSpecialPrice,
            'carpool_type'       => $oProduct->oOrderInfo->iCarpoolType,
            'product_category'   => $oProduct->oOrderInfo->iProductCategory,
            'is_dynamic'         => $aBillInfo['is_has_dynamic'] ?? 0,
            'dynamic_diff_price' => $aBillInfo['dynamic_diff_price'],
            'discount_info'      => json_encode($aInfo['activity_info'][0]['discount_desc']),
            'select_type'        => $aEstimateData['select_type'],
            'estimate_trace_id'  => Trace::traceId(),
            'estimate_id'        => $aInfo['order_info']['estimate_id'],
            'red_packet'         => (int) $aBillDisplayLines['red_packet']['value'] ?? 0.0,
            'xpsid'              => $this->_oRequest->getXpsid(),
            'xpsid_root'         => $this->_oRequest->getXpsidRoot(),
        ];

        if (\BizLib\Constants\Horae::TYPE_COMBO_CARPOOL_INTER_CITY == $aInfo['order_info']['combo_type']) {
            //跨城
            $aRouteInfo = $aInfo['bill_info']['match_routes'];
            $aEstimateStatistic['combo_id']    = $aRouteInfo['route_id'];
            $aEstimateStatistic['route_group'] = $aRouteInfo['route_group'];
            $aEstimateStatistic['inter_mode']  = $aRouteInfo['inter_mode'];
            $aEstimateStatistic['route_name']  = $aRouteInfo['route_name'];
            if (!empty($this->_oRequest->getDepartureRange())) {
                $sDepartureRange = $this->_oRequest->getDepartureRange();
            } else {
                $sDepartureRange = $aRouteInfo['time_span'][0]['range'][0]['value'];
            }

            $aEstimateStatistic['departure_range'] = $sDepartureRange;

            // 标识城际开放围栏路线
            $aEstimateStatistic['is_open_fence'] = InterCity::getRouteConf($aRouteInfo['route_id'], 'is_open_fence', 0);
        }

        // 远途拼车预约无车赔
        $aEstimateStatistic['intercity_carpool_no_car'] = BookingCompensateLogic::getInstance()->getHitCompensation();

        //命中库存模式
        $aEstimateStatistic['is_sku_model'] = IntercityBookingSkuRender::getInstance()->isHitSkuModel($oProduct->oOrderInfo->iProductCategory);

        //是否是客企扫码上车，是置为1，不是置为0
        $aEstimateStatistic['is_keqi_scan'] = AsyncHandler::AGENT_TYPE_KEQI == $aInfo['common_info']['agent_type'] ? 1 : 0;
        PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);
    }

    /**
     * @param array $aInfo          aInfo
     * @param array $aResponseInfos 预估结果集
     * @return array
     */
    private function _getEstimateData(array $aInfo, array $aResponseInfos) {
        $iEstimateId = $aInfo['order_info']['estimate_id'];
        if (!empty($aResponseInfos['data']['estimate_data'])) {
            foreach ($aResponseInfos['data']['estimate_data'] as $aData) {
                if ($iEstimateId == $aData['estimate_id']) {
                    return $aData;
                }
            }
        }

        return [];
    }


    /**
     * @param array $aInfo            Price返回结果
     * @param array $aCurEstimateData 预估品类数据
     * @return array
     */
    private function _assembleKafkaData(array $aInfo, array $aCurEstimateData) {
        if (!empty($aInfo['passenger_info']['pid']) && isset($aInfo['bill_info'])) {
            if (Horae::isCarpool($aInfo['order_info']['combo_type'], $aInfo['order_info']['require_level']) && empty($aInfo['bill_info']['is_carpool_open'])) {
                return [];
            }

            $aPassengerKafka = [];

            $sCarLevel = $aInfo['order_info']['require_level'];
            $aBillInfo = $aInfo['bill_info'];
            $aPassengerKafka['create_time']     = time();
            $aPassengerKafka['passenger_phone'] = $aInfo['passenger_info']['phone'];
            $aPassengerKafka['passenger_id']    = $aInfo['passenger_info']['pid'];
            $aPassengerKafka['district']        = $aInfo['order_info']['district'];
            $aPassengerKafka['area']            = $aInfo['order_info']['area'];
            $aPassengerKafka['channel']         = $aInfo['order_info']['channel'];
            $aPassengerKafka['starting_lng']    = $aInfo['order_info']['from_lng'];
            $aPassengerKafka['starting_lat']    = $aInfo['order_info']['from_lat'];
            $aPassengerKafka['county']          = (string)($aInfo['order_info']['county']);
            $aPassengerKafka['dest_lng']        = $aInfo['order_info']['to_lng'];
            $aPassengerKafka['dest_lat']        = $aInfo['order_info']['to_lat'];
            $aPassengerKafka['from_poi_id']     = $aInfo['order_info']['from_poi_id'];
            $aPassengerKafka['to_poi_id']       = $aInfo['order_info']['to_poi_id'];
            $aPassengerKafka['product_id']      = $aInfo['order_info']['product_id'];
            $aPassengerKafka['scene_type']      = $aInfo['order_info']['scene_type'];
            $aPassengerKafka['car_type']        = $sCarLevel;
            $aPassengerKafka['multi_require_product'] = '';
            $aPassengerKafka['preference_product']    = '';
            $aPassengerKafka['is_anycar']      = 0;
            $aPassengerKafka['n_tuple']        = json_encode($aInfo['order_info']['n_tuple'], JSON_UNESCAPED_UNICODE);
            $aPassengerKafka['current_lng']    = (float)$this->_oRequest->getLng();
            $aPassengerKafka['current_lat']    = (float)$this->_oRequest->getLat();
            $aPassengerKafka['client_type']    = (int)$this->_oRequest->getClientType();
            $aPassengerKafka['platform_type']  = (int)$this->_oRequest->getPlatformType();
            $aPassengerKafka['starting_name']  = (string)($aInfo['order_info']['starting_name']);
            $aPassengerKafka['dest_name']      = (string)($aInfo['order_info']['dest_name']);
            $aPassengerKafka['departure_time'] = $aInfo['order_info']['departure_time'];
            $aPassengerKafka['is_fast_car']    = (int)($aInfo['order_info']['is_fast_car']);
            $aPassengerKafka['oType']          = $aInfo['order_info']['oType'];
            $aPassengerKafka['app_version']    = $aInfo['common_info']['app_version'];
            $aPassengerKafka['origin_id']      = $aInfo['common_info']['origin_id'];
            $aPassengerKafka['menu_id']        = $aInfo['order_info']['menu_id'];
            $aPassengerKafka['bubble_id']      = $aCurEstimateData['estimate_id'];
            $aPassengerKafka['estimate_id']    = $aCurEstimateData['estimate_id'];
            $aPassengerKafka['combo_type']     = (int)($aCurEstimateData['combo_type']);
            $aPassengerKafka['estimate_distance_metre'] = $aInfo['bill_info']['driver_metre'];
            $aPassengerKafka['estimate_time_minutes']   = $aInfo['bill_info']['driver_minute'];
            $aPassengerKafka['dynamic_total_fee']       = $aBillInfo['dynamic_total_fee'];
            $aPassengerKafka['dynamic_diff_price']      = $aBillInfo['dynamic_diff_price'];
            $aPassengerKafka['estimate_fee']            = $aInfo['activity_info'][0]['estimate_fee'];
            $aPassengerKafka['cap_price']          = $aBillInfo['cap_price'];
            $aPassengerKafka['final_coupon_value'] = $aInfo['activity_info'][0]['final_coupon_value'];
            $aPassengerKafka['select_type']        = $aCurEstimateData['select_type'];
            return $aPassengerKafka;
        }
    }
}
