<?php
namespace PreSale\Logics\intercityEstimatePrice\multiResponse;

use BizLib\Utils\Language;
use Xiaoju\Apollo\Apollo;

/**
 * Class ConfigLoader
 * @package PreSale\Logics\luxEstimatePrice\multiResponse
 */
class ConfigLoader
{

    /**
     * 城际新场景页预估物料配置
     */
    const APOLLO_INTERCITY_NEW_EST_CONFIG = 'intercity_new_est_config';

    /**
     * 城际新场景页预估物料配置路径
     */
    const APOLLO_INTERCITY_COMMON_CONFIG = 'intercity_new_common_config';

    /**
     * 城际新场景页预估物料配置文件
     */
    const APOLLO_INTERCITY_COMMON_FILE = 'intercity_new_common_config';

    /**
     * 城际新场景页预估准时走物料配置
     */
    const APOLLO_INTERCITY_NEW_ZHUNSIZOU_CONFIG = 'intercity_new_zhunshizou_config';

    /**
     * @var ConfigLoader 单例
     */
    private static $_oInstance = null;

    /**
     * @var array 预估品类配置
     */
    private $_aEstimateItemConfig = [];

    /**
     * @var array 公共配置
     */
    private $_aCommonConfig = [];

    /**
     * @var array 预估品类准时走配置
     */
    private $_aEstimateItemZhunShiZouConfig = [];

    /**
     * @var array 城际预估品类特殊模式（库存模式）物料配置
     */
    private $_aIntercityBookingSkuConfig = [];

    /**
     * 构造函数
     * ConfigPreLoader constructor.
     */
    private function __construct(){}

    /**
     * @return ConfigLoader
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }


    /**
     * @return void
     */
    public function init() {
        $this->_initEstimateItemConfig();
        $this->_initCommonConfig();
        $this->_initEstimateItemZhunShiZouConfig();
        $this->_initIntercityBookingSkuConfig();
    }

    /**
     * 城际预估品类特殊模式（库存模式）物料配置
     * @return void
     */
    private function _initIntercityBookingSkuConfig() {
        $this->_aIntercityBookingSkuConfig = Language::getDecodedTextFromDcmp('config_inter_carpool-sku_model_pc');
    }


    /**
     * 城际预估品类物料配置
     * @return void
     */
    private function _initEstimateItemConfig() {
        $oConfig = Apollo::getInstance()->getConfigsByNamespaceAndConditions(self::APOLLO_INTERCITY_NEW_EST_CONFIG, []);
        list($bOk, $aConfig) = $oConfig->getAllConfigData();
        if (!$bOk) {
            return;
        }

        $aEstimateConfig = [];
        foreach ($aConfig as $aConfigItem) {
            $iProductCategory = $aConfigItem['product_category'] ?? 0;
            if (0 == $iProductCategory) {
                continue;
            }

            $aEstimateConfig[$iProductCategory] = $aConfigItem;
        }

        $this->_aEstimateItemConfig = $aEstimateConfig;
    }


    /**
     * 城际场景页公共配置
     * @return void
     */
    private function _initCommonConfig() {
        list($bOK, $aConfig) = Apollo::getInstance()->getConfigResult(self::APOLLO_INTERCITY_COMMON_CONFIG,self::APOLLO_INTERCITY_COMMON_FILE)->getAllConfig();
        if (!$bOK) {
            return;
        }

        $this->_aCommonConfig = $aConfig;
    }

    /**
     * 新场景页预估准时走物料配置
     * @return void
     */
    private function _initEstimateItemZhunShiZouConfig() {
        $oConfig = Apollo::getInstance()->getConfigsByNamespaceAndConditions(
            self::APOLLO_INTERCITY_NEW_ZHUNSIZOU_CONFIG,
            []
        );
        list($bOk, $aConfig) = $oConfig->getAllConfigData();
        if (!$bOk) {
            return;
        }

        $aEstimateConfig = [];
        foreach ($aConfig as $aConfigItem) {
            $iProductCategory = $aConfigItem['product_category'] ?? 0;
            if (0 == $iProductCategory) {
                continue;
            }

            $aEstimateConfig[$iProductCategory] = $aConfigItem;
        }

        $this->_aEstimateItemZhunShiZouConfig = $aEstimateConfig;
    }

    /**
     * @return array
     */
    public function getEstimateItemConfig(): array {
        return $this->_aEstimateItemConfig;
    }

    /**
     * @return array
     */
    public function getCommonConfig(): array {
        return $this->_aCommonConfig;
    }

    /**
     * @return array
     */
    public function getEstimateItemZhunShiZouConfig(): array {
        return $this->_aEstimateItemZhunShiZouConfig;
    }

    /**
     * @return array
     */
    public function getIntercityBookingSkuConfig(): array {
        return $this->_aIntercityBookingSkuConfig;
    }
}
