<?php
namespace PreSale\Logics\intercityEstimatePrice\multiResponse;

use BizCommon\Models\Order\Order;
use BizLib\Utils\UtilHelper;
use BizLib\Constants;

/**
 * Class Util
 * @package PreSale\Logics\intercityEstimatePrice\multiResponse
 */
class Util
{

    /**
     * 端上支付支持企业支付
     * @param array $aInfo ...
     * @return bool
     */
    public static function isSupportBusinessPay(array $aInfo) {
        if (in_array($aInfo['common_info']['access_key_id'],[Constants\Common::DIDI_IOS_PASSENGER_APP, Constants\Common::DIDI_ANDROID_PASSENGER_APP])
            && UtilHelper::compareAppVersion($aInfo['common_info']['app_version'], '6.2.20') >= 0
        ) {
            return true;
        }

        return false;
    }


    /**
     * 判断品类是否选择企业支付
     * @param array $aInfo ...
     * @return bool
     */
    public static function isBusinessPaySelected(array $aInfo) {
        $aPayments = $aInfo['payments_info']['payment'] ?? [];
        if (0 == count($aPayments)) {
            return false;
        }

        $bSelected = false;
        foreach ($aPayments as $aPayment) {
            if (1 == $aPayment['is_selected'] && Order::BUSINESS_PAY_BY_BUSINESS_BALANCE == $aPayment['pay_type']) {
                $bSelected = true;
                break;
            }
        }

        return $bSelected;
    }

    /**
     * 判断是否是客企接入小程序三期功能以上版本
     * @param array $aInfo ...
     * @return bool
     */
    public static function isTripCloudV3(array $aInfo) {
        if ((in_array(
            $aInfo['common_info']['access_key_id'],
            [
                Constants\Common::DIDI_IOS_PASSENGER_APP,
                Constants\Common::DIDI_ANDROID_PASSENGER_APP,
            ]
        ) && version_compare($aInfo['common_info']['app_version'], '6.3.20') >= 0)
            || (in_array(
                $aInfo['common_info']['access_key_id'],
                [
                    Constants\Common::DIDI_WECHAT_MINI_PROGRAM,
                    Constants\Common::DIDI_ALIPAY_MINI_PROGRAM,
                ]
            )
            && version_compare($aInfo['common_info']['app_version'], '6.3.18') >= 0)
        ) {
            return true;
        }

        return false;
    }
}
