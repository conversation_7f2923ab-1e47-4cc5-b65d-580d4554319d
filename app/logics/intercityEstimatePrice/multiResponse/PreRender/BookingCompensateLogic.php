<?php
namespace PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender;

use BizLib\Utils\Language;
use BizLib\Utils\ProductCategory;
use PreSale\Models\rpc\FenceRpc;
use Xiaoju\Apollo\Apollo as ApolloV2;

/**
 * Class BookingCompensateLogic
 * @package PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender
 */
Class BookingCompensateLogic
{
    /**
     * @var BookingCompensateLogic 单例
     */
    private static $_oInstance = null;

    private $_aInfos = [];

    /**
     * @var array 赔付配置
     */
    private $_aCompConfig = [];

    /**
     * @var array 可赔付时间段，格式["10:00-12:00", "22:00-23:00"]
     */
    private $_aCompTimeSpans = [];

    /**
     * @var int 是否命中赔付，监控用
     */
    private $_iHitCompensate = 0;

    /**
     * @return BookingCompensateLogic
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * @param array $aInfos aInfos
     * @return void
     */
    public function init($aInfos) {
        $this->_aInfos = $aInfos;
        $this->_initCompensationConfig();
        $this->_initCompensationTime();
    }

    /**
     * 获取赔付配置
     * @return void
     */
    private function _initCompensationConfig() {
        $aCompConfig        = Language::getDecodedTextFromDcmp('config_inter_carpool-compensation');
        $this->_aCompConfig = $aCompConfig;
    }

    /**
     * @return array
     */
    public function getCompensationConfig() {
        return $this->_aCompConfig;
    }

    /**
     * @return int
     */
    public function getHitCompensation() {
        return $this->_iHitCompensate;
    }

    /**
     * 获取可赔付时间段
     * @return void
     */
    private function _initCompensationTime() {
        $bHitInterCarpool = false;
        foreach ($this->_aInfos as $aInfo) {
            $iProductCategory = $aInfo['order_info']['product_category'] ?? 0;
            if (ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTE == $iProductCategory) {
                // 如果没有远途拼车特价版，就没必要获取赔付时间段
                $bHitInterCarpool = true;
                break;
            }
        }

        if ($bHitInterCarpool) {
            $aOrderInfo     = $aInfo['order_info'];
            $aPassengerInfo = $aInfo['passenger_info'];
            $oToggle        = ApolloV2::getInstance()->featureToggle(
                'no_car_compensation_grayscale',
                [
                    'city'          => $aOrderInfo['area'],
                    'phone'         => $aPassengerInfo['phone'],
                    'access_key_id' => $aOrderInfo['access_key_id'],
                    'combo_id'      => $aOrderInfo['combo_id'],
                    'route_group'   => $aOrderInfo['match_routes'][0]['route_group'],

                ]
            );
            if (!$oToggle->allow()) {
                return;
            }

            $sRouteTimeSlice = $oToggle -> getAllParameters()['route_time_slice'];
            $aRouteTimeSlice = json_decode($sRouteTimeSlice,true);
            if (empty($aRouteTimeSlice)) {
                return;
            }

            // 调围栏服务
            $aGroupId     = [22];
            $aCoordinates = [
                [
                    'lat' => $aOrderInfo['from_lat'],
                    'lng' => $aOrderInfo['from_lng'],
                ],
                [
                    'lat' => $aOrderInfo['to_lat'],
                    'lng' => $aOrderInfo['to_lng'],
                ],
            ];
            $aFenceInfos  = FenceRpc::getInstance()->getMultiInFence($aCoordinates, $aGroupId);
            if (empty($aFenceInfos) || !isset($aFenceInfos[0]) || !isset($aFenceInfos[0][0]) || !isset($aFenceInfos[1]) || !isset($aFenceInfos[1][0])) {
                return;
            }

            $aFromFenceIds = $aFenceInfos[0][0]['fence_ids'];
            $aToFenceIds   = $aFenceInfos[1][0]['fence_ids'];
            $aFromFenceMap = array();
            $aToFenceMap   = array();

            foreach ($aFromFenceIds as $iFromFenceId) {
                $aFromFenceMap[(string)$iFromFenceId] = 1;
            }

            foreach ($aToFenceIds as $iToFenceId) {
                $aToFenceMap[(string)$iToFenceId] = 1;
            }

            // 起终点围栏与apollo配置取交集
            foreach ($aRouteTimeSlice as $aItem) {
                if (!empty($aFromFenceMap[(string)$aItem['from_fence_id']]) && !empty($aToFenceMap[(string)$aItem['to_fence_id']])) {
                    $this->_aCompTimeSpans = $aItem['time_slice'];
                    return;
                }
            }
        }
    }

    /**
     * @return array
     */
    public function getCompTimeSpan() {
        return $this->_aCompTimeSpans;
    }

    /**
     * 判断时间片是否命中赔付
     * @param array $aTimeSpan 时间片如：[1641954953,1641956400]
     * @return bool
     */
    public function isHitCompensation($aTimeSpan) {
        $aCompTimeSpans = $this->_aCompTimeSpans;
        if (empty($aCompTimeSpans) || empty($aTimeSpan)) {
            return false;
        }

        // 完整时间戳 -> 时:分 -> 时间戳
        $sLeftTime  = date('H:i', $aTimeSpan[0]);
        $sRightTime = date('H:i', $aTimeSpan[1]);
        $iLeftTime  = strtotime($sLeftTime);
        $iRightTime = strtotime($sRightTime);
        foreach ($aCompTimeSpans as $sCompTimePairs) {
            $aCompTimePairs = explode('-', $sCompTimePairs);
            $iLeftCompTime  = strtotime($aCompTimePairs[0]);
            $iRightCompTime = strtotime($aCompTimePairs[1]);
            if ($iLeftTime >= $iLeftCompTime && $iRightTime <= $iRightCompTime) {
                // 时间片完全在可赔付时间段内
                $this->_iHitCompensate = 1;

                return true;
            }
        }

        return false;
    }
}
