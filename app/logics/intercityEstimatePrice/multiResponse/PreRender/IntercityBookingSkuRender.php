<?php

namespace PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender;

use BizCommon\Constants\OrderNTuple;
use BizCommon\Utils\Horae as BizHorae;
use BizLib\Utils\ProductCategory;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use Xiaoju\Apollo\Apollo as ApolloV2;

/**
 * 城际预约单库存占座无车赔付
 * Class IntercityBookingSkuRender
 * @package PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender
 */
class IntercityBookingSkuRender
{
    /**
     * @var IntercityBookingSkuRender 单例
     */
    private static $_oInstance = null;

    /**
     * @var array 品类信息
     */
    private $_aInfo = [];

    /**
     * @var boolean 品类中存在城际预约单库存占座无车赔付  默认false
     */
    private $_bExistSkuModel = false;

    /**
     * @var array 时间片库存信息
     */
    private $_aTimeSpan;

    /**
     * @return IntercityBookingSkuRender s
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * @param array $aInfos aInfos
     * @return void
     */
    public function init($aInfos) {
        //城际库存模式 一定是远途拼车特价版（城际拼满走）或 客企
        foreach ($aInfos as $aInfo) {
            $iProductCategory = $aInfo['order_info']['product_category'] ?? 0;
            if ((ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTE == $iProductCategory
                && BizHorae::isInterCityCarpool($aInfo['order_info']))
                || (
                // 客企
                MainDataRepo::isBelongTripCloud($iProductCategory))
            ) {
                $this->_aInfo = $aInfo;
            }
        }

        $this->_initSkuModel();
    }

    /**
     * 初始化【城际预约单库存占座无车赔付】配置，确定是否命中
     * @return void
     */
    private function _initSkuModel() {
        $aInfo = $this->_aInfo;
        //首先命中库存模式的条件是 => （城际拼满走 or 客企） && 灰度阿波罗线路开城
        if (!empty($aInfo)) {
            $oToggle = ApolloV2::getInstance()->featureToggle(
                'intercity_carpool_sku_model_grayscale',
                [
                    'timestamp'     => time(),
                    'city'          => $aInfo['order_info']['area'],
                    'phone'         => $aInfo['passenger_info']['phone'],
                    'product_id'    => $aInfo['order_info']['product_id'],
                    'combo_type'    => $aInfo['order_info']['combo_type'],
                    'carpool_type'  => $aInfo['order_info']['carpool_type'],
                    'access_key_id' => $aInfo['order_info']['access_key_id'],
                    'combo_id'      => $aInfo['order_info']['combo_id'],
                    'route_group'   => $aInfo['order_info']['match_routes'][0]['route_group'],
                    'source'        => 'pre-sale',

                ]
            );
            //后续城际自营新开城库存模式判断统一使用dds返回的is_sku_model
            if (($oToggle->allow() && OrderNTuple::ROUTE_TYPE_POOL_FULL_GO == $aInfo['route_type'])
                || ($aInfo['order_info']['product_category'] == ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTE && $aInfo['order_info']['is_sku_model'] == 1)
            ) {
                $this->_bExistSkuModel = true;
            }
            return;
        }
        $this->_bExistSkuModel = false;
    }


    /**
     * @param array $aTimeSpan 库存时间片信息
     * @return void
     */
    public function setSkuTimeSpan($aTimeSpan) {
        $this->_aTimeSpan = $aTimeSpan;
    }

    /**
     * @return array
     */
    public function getSkuTimeSpan() {
        return $this->_aTimeSpan;
    }

    /**
     * @param int $iProductCategory 品类
     * @return boolean 是否命中
     */
    public function isHitSkuModel($iProductCategory) {
        if ($this->_bExistSkuModel) {
            //_bHitSkuModel这个字段代表多品类中存在库存模式. 库存模式在城际拼满走 + 客企
            if (ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTE != $iProductCategory && !MainDataRepo::isBelongTripCloud($iProductCategory)) {
                return false;
            }
        }

        return $this->_bExistSkuModel;
    }
}
