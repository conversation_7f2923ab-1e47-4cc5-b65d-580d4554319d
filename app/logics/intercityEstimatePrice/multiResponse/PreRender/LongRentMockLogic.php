<?php

namespace PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender;

use BizCommon\Utils\Horae as BizHorae;
use BizLib\Utils\ProductCategory;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use <PERSON><PERSON>\Apollo\Apollo as ApolloV2;

/**
 * 城际包车mock
 * Class LongRentMockLogic
 * @package PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender
 */
class LongRentMockLogic
{
    /**
     * @var LongRentMockLogic 单例
     */
    private static $_oInstance = null;

    /**
     * @var array 品类信息
     */
    private $_aInfo = [];

    /**
     * @var boolean 是否命中包车
     */
    private $_bIntercityLongRent = false;

    /**
     * @var array 物料
     */
    private $_aConfig = [];


    /**
     * @return LongRentMockLogic 实例
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * @param array $aInfos aInfos
     * @return void
     */
    public function init($aInfos) {
        //城际库存模式 一定是远途拼车特价版 或 客企
        foreach ($aInfos as $aInfo) {
            $iProductCategory = $aInfo['order_info']['product_category'] ?? 0;
            if ((ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTE == $iProductCategory)
                || (
                // 客企
                MainDataRepo::isBelongTripCloud($iProductCategory))
            ) {
                $this->_aInfo = $aInfo;
            }
        }

        $this->_initLongRentMock();
    }

    /**
     * 初始化【城际mock包车感知】配置，确定是否命中
     * @return void
     */
    private function _initLongRentMock() {
        $aInfo = $this->_aInfo;
        //命中条件：灰度阿波罗线路开城
        if (!empty($aInfo)) {
            $oToggle = ApolloV2::getInstance()->featureToggle(
                'ab_gray_intercity_estimate_all_car',
                [
                    'timestamp'     => time(),
                    'city'          => $aInfo['order_info']['area'],
                    'phone'         => $aInfo['passenger_info']['phone'],
                    'product_id'    => $aInfo['order_info']['product_id'],
                    'combo_type'    => $aInfo['order_info']['combo_type'],
                    'carpool_type'  => $aInfo['order_info']['carpool_type'],
                    'access_key_id' => $aInfo['order_info']['access_key_id'],
                    'combo_id'      => $aInfo['order_info']['combo_id'],
                    'route_group'   => $aInfo['order_info']['match_routes'][0]['route_group'],
                    'source'        => 'pre-sale',

                ]
            );
            $this->_bIntercityLongRent = $oToggle->allow();
            $this->_aConfig            = $oToggle->getAllParameters();
            return;
        }

        $this->_bIntercityLongRent = false;
    }

    /**
     * @param int $iProductCategory 品类
     * @return boolean 是否命中
     */
    public function isHitLongRentMock($iProductCategory) {
        if ($this->_bIntercityLongRent) {
            if (ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTE != $iProductCategory && !MainDataRepo::isBelongTripCloud($iProductCategory)) {
                return false;
            }
        }

        return $this->_bIntercityLongRent;
    }

    /**
     * @param int $iProductCategory 品类
     * @return array 物料文案
     */
    public function getLongRentMockConfig($iProductCategory) {
        if (!$this->_bIntercityLongRent || (ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTE != $iProductCategory && !MainDataRepo::isBelongTripCloud($iProductCategory))) {
            return null;
        }

        return $this->_aConfig;
    }
}
