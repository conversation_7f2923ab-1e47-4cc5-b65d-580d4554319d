<?php

namespace PreSale\Logics\intercityEstimatePrice\multiResponse\component;

use BizLib\Utils\Language;
use PreSale\Logics\intercityEstimatePrice\multiResponse\ConfigLoader;
use PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender\IntercityBookingSkuRender;
use Xiaoju\Apollo\Apollo;

/**
 * 卡片扩展组件
 * Class CardExpand
 * @package PreSale\Logics\intercityEstimatePrice\multiResponse\component
 */
class CardExpand implements IComponent
{
    // 客企扫二维码预估标识
    const AGENT_TYPE_KEQI = 'keqi_scan';

    /**
     * @var array aInfo
     */
    private $_aInfo = [];

    /**
     * 获取 Apollo 请求参数
     * @var array aInfo
     */
    private $_aApolloParam = [];

    /**
     * ICMatchRoute constructor.
     * @param array $aInfo aInfo
     */
    public function __construct(array $aInfo) {
        $this->_aInfo        = $aInfo;
        $this->_aApolloParam = [
            'key'              => $aInfo['passenger_info']['pid'],
            'phone'            => $aInfo['passenger_info']['phone'],
            'pid'              => $aInfo['passenger_info']['pid'],
            'uid'              => $aInfo['passenger_info']['uid'],
            'city'             => $aInfo['order_info']['area'],
            'combo_id'         => $aInfo['order_info']['combo_id'],
            'route_group'      => $aInfo['order_info']['match_routes'][0]['route_group'],
            'product_id'       => $aInfo['order_info']['product_id'],
            'product_category' => $aInfo['order_info']['product_category'],
            'access_key_id'    => $aInfo['common_info']['access_key_id'],
            'app_version'      => $aInfo['common_info']['app_version'],
        ];
        $this->_sAgentType   = $aInfo['common_info']['agent_type'];
    }

    /**
     * @param array $aResponse aResponse
     * @return array
     */
    public function build(array $aResponse) {
        if (CardExpand::AGENT_TYPE_KEQI == $this->_sAgentType) {
            return $aResponse;
        }

        $iProductCategory = $this->_aInfo['order_info']['product_category'] ?? 0;
        //目前只有库存模式才有这个 扩展组件，如果命中了库存模式，说明是 城际拼满走&&开关开启
        if (IntercityBookingSkuRender::getInstance()->isHitSkuModel($iProductCategory)) {
            list($aTimeSpan, $bIsTop3) = $this->_getExpandTimeSpan($iProductCategory);
            $aSkuConfig  = ConfigLoader::getInstance()->getIntercityBookingSkuConfig()[$iProductCategory];
            $sSignalText = '';
            //卡片是否可用，是否可以通过外漏的卡片直接发单
            $bIsUnavailable = false;
            if (empty($aTimeSpan)) {
                $sSignalText    = $aSkuConfig['signal_text']['no_sku'] ?? '';
                $bIsUnavailable = true;
            }

            //如果$aTimeSpan不为null 说明存在top3时间片或推荐时间片，且 如果不是top3，那就展示推荐文案
            if (!empty($aTimeSpan) && !$bIsTop3) {
                $sSignalText = $aSkuConfig['signal_text']['recommend'] ?? '';
            }

            // 如果只有一个且是当前时间片，则不展示文案
            if (1 == count($aTimeSpan) && $this->_isCurrentTimeSpan($aTimeSpan[0])) {
                $sSignalText = '';
            }

            $aCardExpandData = [
                'bottom_text' => $aSkuConfig['bottom_text'] ?? '',
                'signal_text' => $sSignalText,
                'time_span'   => $aTimeSpan,
            ];

            $aResponse['match_show_sku_data'] = $aCardExpandData;
            $aResponse['is_unavailable']      = $bIsUnavailable;
        }

        return $aResponse;
    }

    /**
     * 获取扩展时间片的详情
     * @param int $iProductCategory $iProductCategory
     * @return array
     */
    private function _getExpandTimeSpan($iProductCategory) {
        $aSkuTimeSpan = IntercityBookingSkuRender::getInstance()->getSkuTimeSpan();
        //这里不用担心$aTimeSpan为空，因为 库存模式的基础是城际特价拼车。如果命中了库存模式，那么一定会有时间片的信息，但是不一定有库存（可能所有时间片都没有库存）
        $aTimeSpan          = [];
        $aRecommendTimeSpan = [];
        $aCurrentTimeItem   = [];
        $bTop3HaveSku       = false;
        $bIsTop3            = true;
        foreach ($aSkuTimeSpan as $iDayIndex => $aDayTimeSpan) {
            //内一天钟没有时间片。直接跳过
            if (empty($aDayTimeSpan['range'])) {
                continue;
            }

            $sTitle = $aDayTimeSpan['title'];
            foreach ($aDayTimeSpan['range'] as $iRangeIndex => $aDayRangeItem) {
                //时间片是否可勾选
                $bIsHaveSku = $aDayRangeItem['available'];
                $aDayRangeItem['title'] = $sTitle;

                //记录当前选择的时间片
                if ($this->_isCurrentTimeSpan($aDayRangeItem)) {
                    $aCurrentTimeItem = $aDayRangeItem;
                }

                //时间片外漏逻辑：
                //当前能看到的最近的3个时间片，只要有一个有库存（可勾选），则该3个时间片都展示，其中没有库存的，置灰不允许勾选。
                //如果最近的3个时间片都没有库存，则展示从第4个开始往后的所有时间片中，有库存的最靠前的两个时间片，同时给推荐标签。以及“最近时间片已售罄”文案。
                if (count($aTimeSpan) < 3) {
                    $bTop3HaveSku = $bTop3HaveSku || $bIsHaveSku;
                    $aTimeSpan[]  = $aDayRangeItem;
                    continue;
                }

                //如果前3个 都没有库存，取后面最近的前两个"有库存"的时间片并给"推荐标签"和"文案"。没有库存的直接跳过。
                if (count($aRecommendTimeSpan) < 2 && $bIsHaveSku) {
                    $aRecommendTimeSpan[] = $aDayRangeItem;
                }

                //循环中干了三件事，找当前选择的时间片。前3个，推荐2个，如果都找到的话，就跳出循环就好
                if (!empty($aCurrentTimeItem) && 3 == count($aTimeSpan) && 2 == count($aRecommendTimeSpan)) {
                    break;
                }
            }
        }

        if (!empty($aTimeSpan) && $bTop3HaveSku) {
            //如果时间片$aTimeSpan不等于空 并且 $bTop3HaveSku = true 说明前三个时间片至少有一个有库存，展示前3个
            $aRet = $aTimeSpan;
        } elseif (!empty($aRecommendTimeSpan)) {
            //如果此时 $aRecommendTimeSpan 不为空 说明 从前三个时间片没有库存，后续时间片，返回有库存的2个
            $bIsTop3 = false;
            $aRet    = $aRecommendTimeSpan;
        } else {
            return [array(),$bIsTop3];
        }

        $aRet = $this->_processTimeSpan($aRet,$bIsTop3,$aCurrentTimeItem,$iProductCategory);
        return [$aRet,$bIsTop3];
    }

    /**
     * 对时间片进行字段处理
     * @param array $aTimeSpan        $aTimeSpan
     * @param bool  $bIsTop3          $bIsTop3
     * @param array $aCurrentTimeItem $aCurrentTimeItem
     * @param int   $iProductCategory $iProductCategory
     * @return array
     */
    private function _processTimeSpan($aTimeSpan, $bIsTop3, $aCurrentTimeItem, $iProductCategory) {
        $aSkuConfig       = ConfigLoader::getInstance()->getIntercityBookingSkuConfig()[$iProductCategory];
        $bHaveCurrentTime = false;
        foreach ($aTimeSpan as &$aDayRangeItem) {
            $aDayRangeItem['label'] = Language::replaceTag(
                $aSkuConfig['label'],
                array(
                    'title' => $aDayRangeItem['title'],
                    'time'  => $aDayRangeItem['left_label'].'-'.$aDayRangeItem['right_label'],
                )
            );

            // 对存在推荐的时间片添加"推荐"icon
            if (!$bIsTop3) {
                $aDayRangeItem['icon'] = $aSkuConfig['icon_url'];
            }

            // 获取添加"预约优惠"icon
            $sTimeSliceIcon = $this->_getPreferentialIcon($aDayRangeItem);
            if (!empty($sTimeSliceIcon)) {
                $aDayRangeItem['icon'] = $sTimeSliceIcon;
            }

            // 如果外漏时间片中存在当前选择的时间片，或者用户没有选择时间片的话，那么外漏时间片不变，或者当前选中的时间片不能勾选（防止首次预估的时候默认时间片）
            if (empty($aCurrentTimeItem) || $aCurrentTimeItem['value'] === $aDayRangeItem['value'] || !$aCurrentTimeItem['available']) {
                $bHaveCurrentTime = true;
            }
        }

        if (!$bHaveCurrentTime) {
            //当前时间片不在外漏的时间片内
            $aCurrentTimeItem['label'] = Language::replaceTag(
                $aSkuConfig['label'],
                array(
                    'title' => $aCurrentTimeItem['title'],
                    'time'  => $aCurrentTimeItem['left_label'].'-'.$aCurrentTimeItem['right_label'],
                )
            );
            $aCurrentSpan[]            = $aCurrentTimeItem;
            return $aCurrentSpan;
        }

        return $aTimeSpan;
    }

    /**
     * 是否是当前时间片
     * @param array $aDayRangeItem $aDayRangeItem
     * @return bool
     */
    private function _isCurrentTimeSpan($aDayRangeItem) {
        $aTimeValue      = json_decode($aDayRangeItem['value'], true);
        $aDepartureRange = json_decode($this->_aInfo['order_info']['departure_range'], true);
        return $aTimeValue === $aDepartureRange;
    }

    /**
     * 获取"预约优惠"icon（满足三个条件：一 开关，二 实验，三 大于时间片间隔）
     * @param array $aDayRangeItem $aDayRangeItem
     * @return string
     */
    private function _getPreferentialIcon($aDayRangeItem) {
        $sTimeSliceIcon = '';
        $oToggle        = Apollo::getInstance()->featureToggle(
            'intercity_carpool_subscribe_preferential_toggle',
            $this->_aApolloParam
        );
        if (!$oToggle->allow()) {
            return $sTimeSliceIcon;
        }

        $oExperiment = Apollo::getInstance()->featureToggle(
            'yuantu_reservation_discount',
            $this->_aApolloParam
        );

        // 满足开关，满足实验后，获取开关的时间间隔，判断时间片是否出icon
        if ($oExperiment->allow() && 'treatment_group' == $oExperiment->getGroupName()) {
            // 获取时间间隔(s)
            $iTimeInterval = $oToggle->getParameter('time_interval', 0) * 60;

            $aDepartureRange        = json_decode($aDayRangeItem['value'], true);
            $iDepartureTimeEarliest = $aDepartureRange[0] ?? 0;
            // 若左时间片 - 当前时间 > 时间间隔，就出icon
            if ($iDepartureTimeEarliest - time() > $iTimeInterval) {
                $sTimeSliceIcon = $oToggle->getParameter('time_slice_icon', '');
            }
        }

        return $sTimeSliceIcon;
    }
}
