<?php
namespace PreSale\Logics\intercityEstimatePrice\multiResponse\component;

use BizLib\Utils\ProductCategory;
use PreSale\Logics\intercityEstimatePrice\multiResponse\ConfigLoader;
use BizLib\Log as NuwaLog;
use BizLib\ErrCode;
use PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender\BookingCompensateLogic;
use BizCommon\Utils\Horae as BizHorae;
use BizCommon\Constants\OrderNTuple;
use BizLib\Constants\Common as Constants;
use PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender\IntercityBookingSkuRender;
use Xiaoju\Apollo\Apollo;
use BizLib\Config as NuwaConfig;
use PreSale\Logics\intercityEstimatePrice\multiResponse\Util;
use PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender\LongRentMockLogic;

/**
 * Class FeatureAttr
 * @package PreSale\Logics\intercityEstimatePrice\multiResponse\component
 */
class FeatureAttr implements IComponent
{

    /**
     * @var array aInfo
     */
    private $_aInfo = [];

    /**
     * ICFeatureAttr constructor.
     * @param array $aInfo aInfo
     */
    public function __construct(array $aInfo) {
        $this->_aInfo = $aInfo;
    }


    /**
     * @param array $aResponse aResponse
     * @return array
     */
    public function build(array $aResponse) {
        $aConfig          = ConfigLoader::getInstance()->getEstimateItemConfig();
        $iProductCategory = $this->_aInfo['order_info']['product_category'] ?? 0;
        if (0 == $iProductCategory) {
            NuwaLog::warning(ErrCode\Msg::formatArray(ErrCode\Code::E_COMMON_CONFIG_NOT_FOUNT,['intercityEstimateItemConfigMissing']));
            return $aResponse;
        }

        if (isset($aConfig[$iProductCategory])) {
            $aResponse['feature_title']     = $aConfig[$iProductCategory]['feature_title'] ?? '';
            $aResponse['feature_sub_title'] = $aConfig[$iProductCategory]['feature_sub_title'] ?? '';
        }

        // 客企接入小程序需求：准时走走另一个配置
        if (OrderNTuple::ROUTE_TYPE_NONE == $this->_aInfo['order_info']['route_type']
            && Util::isTripCloudV3($this->_aInfo)
        ) {
            $aConfigZhunShiZou = ConfigLoader::getInstance()->getEstimateItemZhunShiZouConfig();
            if (isset($aConfigZhunShiZou[$iProductCategory])) {
                $sFeatureTitle    = $aConfigZhunShiZou[$iProductCategory]['feature_title'] ?? '';
                $sFeatureSubTitle = $aConfigZhunShiZou[$iProductCategory]['feature_sub_title'] ?? '';
                if (!empty($sFeatureTitle)) {
                    $aResponse['feature_title'] = $sFeatureTitle;
                }

                if (!empty($sFeatureSubTitle)) {
                    $aResponse['feature_sub_title'] = $sFeatureSubTitle;
                }
            }
        }

        if (ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTE == $iProductCategory) {
            $aResponse = $this->_replaceFeatureTitle($aResponse, $this->_aInfo['order_info']);
            if (BizHorae::isInterCityPoolFullGoBooking($this->_aInfo['order_info'])) {
                // 只有拼满走透出赔付感知
                $bRepSubTitle = $this->_isReplaceFeatureSubTitle($this->_aInfo['order_info']);
                if ($bRepSubTitle) {
                    $aResponse['feature_sub_title'] = BookingCompensateLogic::getInstance()->getCompensationConfig()['feature_sub_title_compensate'] ?? '';
                }
            }
        }

        //如果命中了库存模式，说明是 城际拼满走&&开关开启
        if (IntercityBookingSkuRender::getInstance()->isHitSkuModel($iProductCategory)) {
            $aSkuConfig = ConfigLoader::getInstance()->getIntercityBookingSkuConfig()[$iProductCategory];
            $aResponse['feature_title']     = $aSkuConfig['feature_title'] ?? '';
            $aResponse['feature_sub_title'] = $aSkuConfig['feature_sub_title'] ?? '';
        }

        if (LongRentMockLogic::getInstance()->isHitLongRentMock($iProductCategory)) {
            $featureTitleLongRent = LongRentMockLogic::getInstance()->getLongRentMockConfig($iProductCategory)['feature_title'];
            if (!empty($featureTitleLongRent)) {
                $aResponse['feature_title'] = $featureTitleLongRent;
            }
        }

        return $aResponse;
    }

    /**
     * 预约单（非首个时间片），feature_title换文案
     * @param array $aResponse  aResponse
     * @param array $aOrderInfo aOrderInfo
     * @return mixed
     */
    private function _replaceFeatureTitle($aResponse, $aOrderInfo) {
        $sFirstTimeSpan  = $aOrderInfo['match_routes'][0]['time_span'][0]['range'][0]['value'] ?? '[]';  // eg. "[1641980259,1641981600]"
        $aFirstTimeSpan  = json_decode($sFirstTimeSpan, true);
        $sDepartureRange = $aOrderInfo['departure_range'];                     // eg. "[1641980187,1641981600]"
        $aDepartureRange = json_decode($sDepartureRange, true);
        if (empty($aFirstTimeSpan) || empty($aDepartureRange)) {
            return $aResponse;
        }

        if ($aDepartureRange[1] == $aFirstTimeSpan[1]) {
            return $aResponse;
        }

        foreach ($aOrderInfo['match_routes'][0]['time_span'] as $aDayTimeSpan) {
            foreach ($aDayTimeSpan['range'] as $aDayRangeItem) {
                if ($aDayRangeItem['value'] == $sDepartureRange) {
                    $sTitle = $aDayTimeSpan['title'];
                    $sLabel = str_replace('~', '-', $aDayRangeItem['label']);
                    $aResponse['feature_title'] = $sTitle.$sLabel;
                    return $aResponse;
                }
            }
        }

        return $aResponse;
    }

    /**
     * 出发时间片（非首个时间片）命中赔付，则feature_sub_title换文案
     * @param array $aOrderInfo aOrderInfo
     * @return mixed
     */
    private function _isReplaceFeatureSubTitle($aOrderInfo) {
        $sFirstTimeSpan  = $aOrderInfo['match_routes'][0]['time_span'][0]['range'][0]['value'] ?? '[]';  // eg. "[1641980259,1641981600]"
        $aFirstTimeSpan  = json_decode($sFirstTimeSpan, true);
        $sDepartureRange = $aOrderInfo['departure_range'];              // eg. "[1641980187,1641981600]"
        $aDepartureRange = json_decode($sDepartureRange, true);
        if (empty($aFirstTimeSpan) || empty($aDepartureRange)) {
            return false;
        }

        if ($aDepartureRange[1] == $aFirstTimeSpan[1]) {
            return false;
        }

        return BookingCompensateLogic::getInstance()->isHitCompensation($aDepartureRange);
    }
}
