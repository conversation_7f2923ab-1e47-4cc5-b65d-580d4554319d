<?php
namespace PreSale\Logics\intercityEstimatePrice\multiResponse\component;

use BizLib\Utils\Common as UtilsCommon;
use PreSale\Logics\intercityEstimatePrice\multiResponse\component\IComponent;
use PreSale\Logics\intercityEstimatePrice\multiResponse\globalData\CarpoolIntercityRule;
use Xiaoju\Apollo\Apollo;

class SupportSelectSeat implements IComponent
{
    /**
     * @var array
     */
    private $_aInfo;

    /**
     * UserPayInfo constructor.
     * @param array $aInfo aInfo
     */
    public function __construct(array $aInfo) {
        $this->_aInfo = $aInfo;
    }

    /**
     * @param array $aResponse ...
     * @return array
     */
    public function build(array $aResponse) {
        if (empty($this->_aInfo)) {
            return $aResponse;
        }

        $params = [
            'key'           => $this->_aInfo['passenger_info']['pid'] ?? 0,
            'city'          => $this->_aInfo['order_info']['area'],
            'phone'         => $this->_aInfo['passenger_info']['phone'] ?? '',
            'access_key_id' => $this->_aInfo['common_info']['access_key_id'],
            'app_version'   => $this->_aInfo['common_info']['app_version'],
        ];

        $toggle = Apollo::getInstance()->featureToggle(
            'gs_child_ticket_switch',
            $params
        );
        if (!$toggle->allow()) {
            return $aResponse;
        }

        $keqiProduct = CarpoolIntercityRule::getInstance()->getKeqiProduct();
        if (empty($keqiProduct)) {
            return $aResponse;
        }

        if ($this->_aInfo['order_info']['product_category'] != $keqiProduct->oOrderInfo->iProductCategory) {
            return $aResponse;
        }

        $childTicketRule = CarpoolIntercityRule::getInstance()->getChildTicketRule();
        if (1 != $childTicketRule['is_allow_child_ticket']) {
            return $aResponse;
        }

        $aResponse['support_select_seat'] = 1;

        $cli      = \PreSale\Logics\redismove\RedisWrapper::getInstance(P_SPECIAL_PRICE_COMPONENT);
        $key      = 'p_mamba_seat_selection_info'.'_'.$this->_aInfo['order_info']['estimate_id'];
        $seatInfo = [];

        if (!empty($this->_aInfo['order_info']['match_routes'][0]['max_seat_num'])) {
            $seatInfo['max_passenger_count'] = $this->_aInfo['order_info']['match_routes'][0]['max_seat_num'];
        }

        $typePercent = $childTicketRule['type_percent'];
        if (!empty($typePercent) && is_array($typePercent)) {
            foreach ($typePercent as $item) {
                $seatInfo['discount_info'][(int)$item['type']] = (int)$item['percent'];
            }
        }

        $seatInfo['remain_carry_child_seat'] = -1;
        $seatInfo['child_occupy_seat']       = true;

        $cli->setex($key, 30 * 60, json_encode($seatInfo));

        return $aResponse;
    }
}
