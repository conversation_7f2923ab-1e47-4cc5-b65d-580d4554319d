<?php
namespace PreSale\Logics\intercityEstimatePrice\multiResponse\component;

use PreSale\Logics\intercityEstimatePrice\multiResponse\Util;

/**
 * Class UserPayInfo
 * @package PreSale\Logics\intercityEstimatePrice\multiResponse\component
 */
class UserPayInfo implements IComponent
{

    /**
     * @var array aInfo
     */
    private $_aInfo = [];

    const DEFAULT_PAY_INFO = [
        'payment_id'         => '0',
        'business_const_set' => '0',
    ];

    /**
     * UserPayInfo constructor.
     * @param array $aInfo aInfo
     */
    public function __construct(array $aInfo) {
        $this->_aInfo = $aInfo;
    }

    /**
     * @param array $aResponse ...
     * @return array
     */
    public function build(array $aResponse) {
        if (!Util::isSupportBusinessPay($this->_aInfo)) {
            return $aResponse;
        }

        $aUserPayInfoSrc = $this->_aInfo['payments_info'];
        if (empty($aUserPayInfoSrc)) {
            $aResponse['user_pay_info'] = self::DEFAULT_PAY_INFO;
            return $aResponse;
        }

        $fnGetPaymentInfo = function (int $iDefaultPayType, array $aPayments) {
            foreach ($aPayments as $aPayment) {
                if ($iDefaultPayType == $aPayment['pay_type']) {
                    return $aPayment;
                }
            }

            return [];
        };

        $aDefaultPayment            = $fnGetPaymentInfo((int)$aUserPayInfoSrc['default_pay_type'], $aUserPayInfoSrc['payment'] ?? []);
        $aResponse['user_pay_info'] = [
            'payment_id'         => $aDefaultPayment['pay_type'],
            'business_const_set' => $aDefaultPayment['business_const_set'],
        ];

        return $aResponse;
    }
}
