<?php
namespace PreSale\Logics\intercityEstimatePrice\multiResponse\component;

use BizCommon\Utils\Horae;
use BizLib\Utils\Language;
use PreSale\Logics\intercityEstimatePrice\multiResponse\ConfigLoader;
use BizLib\Utils\NumberHelper;
use PreSale\Logics\intercityEstimatePrice\multiResponse\Util;
use Xiaoju\Apollo\Apollo;

/**
 * Class PriceInfo
 * @package PreSale\Logics\intercityEstimatePrice\multiResponse\component
 */
class PriceInfo implements IComponent
{

    /**
     * @var array aInfo
     */
    private $_aInfo = [];


    /**
     * @var array 优惠项信息
     */
    private $_aDiscountInfo = [];


    /**
     * @var array 展示优惠项
     */
    private $_aDisplayLines = [];


    /**
     * @var array 优惠项皮肤配置
     */
    private $_aSkinConfig = [];
    /**
     * @var array feeDetailInfo
     */
    private $_aFeeDetailInfo;

    /**
     * @var array|mixed
     */
    private $_aTicketConfig;

    /**
     * ICPriceInfo constructor.
     * @param array $aInfo aInfo
     */
    public function __construct(array $aInfo) {
        $this->_aInfo = $aInfo;

        $aCommonConfig      = ConfigLoader::getInstance()->getCommonConfig();
        $this->_aSkinConfig = $aCommonConfig['discount_config'] ?? [];
        $this->_aTicketConfig = $aCommonConfig['ticket_discount_config'] ?? [];

        $aDiscountInfo = $aInfo['activity_info'][0]['discount_desc'] ?? [];
        if (!empty($aDiscountInfo) && is_array($aDiscountInfo) && count($aDiscountInfo) > 0) {
            $this->_aDiscountInfo = array_combine(array_column($aDiscountInfo,'type'), $aDiscountInfo);
        }

        $aDisplayLines = $aInfo['bill_info']['display_lines'];
        if (!empty($aDisplayLines) && is_array($aDisplayLines) && count($aDisplayLines) > 0) {
            $this->_aDisplayLines = array_combine(array_column($aDisplayLines,'name'), $aDisplayLines);
        }
    }

    /**
     * 减项为 券 + 金；加项为 春节服务费；
     * dec_price_info / inc_price_info contains [content,left_icon,font_color,bg_fill_color,border_color]
     * @param array $aResponse aResponse
     * @return array
     */
    public function build(array $aResponse) {
        if (empty($this->_aSkinConfig)) {
            return $aResponse;
        }

        //两口价渲染比快车省**元
        if (Horae::isInterCityDualPrice($this->_aInfo['order_info'])) {
            $aPriceInfo = $this->_getFastCarDiffPrice();
            if (!empty($aPriceInfo)) {
                $aResponse['price_info_1'] = [$aPriceInfo];
            }
        }

        if (empty($aResponse['price_info_1'])) {
            $aDecPrice         = $this->_buildDecPriceInfo();
            $aPreferentialIcon = $this->_getPreferentialIcon();

            // 如果有券的话，就在头部追加"预约优惠"的标识
            if (!empty($aDecPrice) && '' != $aPreferentialIcon['left_icon']) {
                array_unshift($aDecPrice, $aPreferentialIcon);
            }

            $aResponse['price_info_1'] = $aDecPrice;
        }

        $aResponse['price_info_2'] = $this->_buildIncPriceInfo();
        return $aResponse;
    }

    /**
     * 获取比快车省 的文案
     * @return array
     */
    private function _getFastCarDiffPrice() {
        $fPreTotalFee = $this->_aInfo['bill_info']['pre_total_fee'];
        $fSuccess     = $this->_aInfo['activity_info'][0]['estimate_fee'];
        $fFail        = $this->_aInfo['activity_info'][1]['estimate_fee'];

        $fDeductSucc = round($fPreTotalFee - $fSuccess, 2);
        $fDeductFail = round($fPreTotalFee - $fFail, 2);

        $aPriceInfo = [];
        if ($fDeductFail > 0 && $fDeductSucc > 0) {
            $aPriceInfo = [
                'left_icon'     => $this->_aSkinConfig['save_icon'],
                'font_color'    => $this->_aSkinConfig['font_color'],
                'bg_fill_color' => $this->_aSkinConfig['bg_fill_color'],
                'border_color'  => $this->_aSkinConfig['border_color'],
                'content'       => Language::replaceTag(
                    $this->_aSkinConfig['save_content'],
                    ['amount_succ' => NumberHelper::numberFormatDisplay($fDeductSucc),'amount_fail' => NumberHelper::numberFormatDisplay($fDeductFail)]
                ),
            ];
        }

        return $aPriceInfo;
    }


    /**
     * @return array
     */
    private function _buildDecPriceInfo() {
        $aDecPriceInfo = [];
        // 企业支付
        $aBusinessPayInfo = $this->_buildBusinessInfo();
        !empty($aBusinessPayInfo) && $aDecPriceInfo[] = $aBusinessPayInfo;

        // 券
        $aCouponInfo = $this->_buildCouponInfo();
        !empty($aCouponInfo) && $aDecPriceInfo[] = $aCouponInfo;

        // 打车金
        $aRewardInfo = $this->_buildRewardsInfo();
        !empty($aRewardInfo) && $aDecPriceInfo[] = $aRewardInfo;

        // 儿童票打折
        $aChildrenTicketInfo = $this->_buildChildrenTicketInfo();
        !empty($aChildrenTicketInfo) && $aDecPriceInfo[] = $aChildrenTicketInfo;

        return array_slice($aDecPriceInfo,0,2);
    }


    /**
     * @return array
     */
    private function _buildIncPriceInfo() {
        $aIncPriceInfo = [];

        // 处理节假日服务费开关
        $bShowPriceDesc = (new Apollo())->featureToggle(
            'gs_holiday_fee_fee_desc',
            array(
                'key'           => $this->_aInfo['passenger_info']['pid'],
                'phone'         => $this->_aInfo['passenger_info']['phone'],
                'city'          => $this->_aInfo['order_info']['area'],
                'page_type'     => $this->_aInfo['common_info']['page_type'],
                'order_type'    => $this->_aInfo['order_info']['order_type'],
                'access_key_id' => $this->_aInfo['common_info']['access_key_id'],
                'app_version'   => $this->_aInfo['common_info']['app_version'],
            )
        )->allow();
        if (!$bShowPriceDesc) {
            return $aIncPriceInfo;
        }

        $fRedPacket = $this->_aDisplayLines['red_packet']['value'] ?? 0.0;
        if ($fRedPacket > 0) {
            $aIncPriceInfo[] = [
                'content' => Language::replaceTag($this->_aSkinConfig['red_packet'], ['amount' => $fRedPacket]),
            ];
        }

        return $aIncPriceInfo;
    }


    /**
     * 获取打车金优惠
     * @return array
     */
    private function _buildRewardsInfo() {
        $aPriceInfo = [];
        if (isset($this->_aDiscountInfo['reward']) && $this->_aDiscountInfo['reward']['amount'] > 0) {
            $aPriceInfo = [
                'left_icon'     => $this->_aSkinConfig['reward_left_icon'],
                'font_color'    => $this->_aSkinConfig['font_color'],
                'bg_fill_color' => $this->_aSkinConfig['bg_fill_color'],
                'border_color'  => $this->_aSkinConfig['border_color'],
                'content'       => Language::replaceTag($this->_aSkinConfig['content'], ['amount' => NumberHelper::numberFormatDisplay($this->_aDiscountInfo['reward']['amount'])]),
            ];
        }

        return $aPriceInfo;
    }


    /**
     * 获取券优惠
     * @return array
     */
    private function _buildCouponInfo() {
        $aPriceInfo = [];
        if (isset($this->_aDiscountInfo['coupon']) && $this->_aDiscountInfo['coupon']['amount'] > 0) {
            $aPriceInfo = [
                'left_icon'     => $this->_aSkinConfig['coupon_left_icon'],
                'font_color'    => $this->_aSkinConfig['font_color'],
                'bg_fill_color' => $this->_aSkinConfig['bg_fill_color'],
                'border_color'  => $this->_aSkinConfig['border_color'],
                'content'       => Language::replaceTag($this->_aSkinConfig['content'], ['amount' => NumberHelper::numberFormatDisplay($this->_aDiscountInfo['coupon']['amount'])]),
            ];
        }

        return $aPriceInfo;
    }


    /**
     * 获取企业支付信息
     * @return array
     */
    private function _buildBusinessInfo() {
        $aPriceInfo = [];
        if (Util::isSupportBusinessPay($this->_aInfo) && Util::isBusinessPaySelected($this->_aInfo)) {
            $aMixedDeductInfo = $this->_aInfo['activity_info'][0]['mixed_pay_deduct_info'];
            $fEstimateFee     = $this->_aInfo['activity_info'][0]['estimate_fee'];
            $fDeductFee       = $aMixedDeductInfo['deduct_fee'] > 0 ? $aMixedDeductInfo['deduct_fee'] : $fEstimateFee;
            if ($fDeductFee > 0) {
                $aCommonConfig = ConfigLoader::getInstance()->getCommonConfig();
                $aBusinessPay  = $aCommonConfig['business_pay_skin'] ?? [];
                $aPriceInfo    = [
                    'content'         => Language::replaceTag($aBusinessPay['content'], ['amount' => $fDeductFee]),
                    'left_icon'       => $aBusinessPay['left_icon'],
                    'font_color'      => $aBusinessPay['font_color'],
                    'bg_fill_color'   => $aBusinessPay['bg_fill_color'],
                    'border_color'    => $aBusinessPay['border_color'],
                    'font_text_color' => $aBusinessPay['font_text_color'],
                ];
            }
        }

        return $aPriceInfo;
    }

    /**
     * 获取"预约优惠"标签
     * @return array
     */
    private function _getPreferentialIcon() {
        $aPriceInfo = [];

        $oToggle = Apollo::getInstance()->featureToggle(
            'intercity_carpool_subscribe_preferential_toggle',
            [
                'key'              => $this->_aInfo['passenger_info']['pid'],
                'phone'            => $this->_aInfo['passenger_info']['phone'],
                'city'             => $this->_aInfo['order_info']['area'],
                'pid'              => $this->_aInfo['passenger_info']['pid'],
                'uid'              => $this->_aInfo['passenger_info']['uid'],
                'route_group'      => $this->_aInfo['order_info']['match_routes'][0]['route_group'],
                'combo_id'         => $this->_aInfo['order_info']['combo_id'],
                'product_id'       => $this->_aInfo['order_info']['product_id'],
                'product_category' => $this->_aInfo['order_info']['product_category'],
                'access_key_id'    => $this->_aInfo['common_info']['access_key_id'],
                'app_version'      => $this->_aInfo['common_info']['app_version'],
            ]
        );
        if ($oToggle->allow()) {
            $sPreferentialIcon = $oToggle->getParameter('fee_desc_icon', '');

            $aPriceInfo = [
                'left_icon'     => $sPreferentialIcon,
                'font_color'    => '',
                'bg_fill_color' => '',
                'border_color'  => '',
                'content'       => '',
            ];
        }

        return $aPriceInfo;
    }

    private function _buildChildrenTicketInfo() {
        if (empty($this->_aDisplayLines)) {
            return [];
        }
        $aPriceInfo = [];

        $carpoolChildTicketFee = $this->_aDisplayLines['carpool_child_ticket_fee']['value'];
        $carpoolInfantsTicketFee = $this->_aDisplayLines['carpool_infants_ticket_fee']['value'];
        $config = $this->_aTicketConfig;
        if (isset($carpoolChildTicketFee) && isset($carpoolInfantsTicketFee) && $carpoolChildTicketFee >= 0 && $carpoolInfantsTicketFee >= 0) {
            $aPriceInfo    = [
                'content'         => $config['children_infants_ticket'],
                'font_color'      => $config['font_color'],
                'bg_fill_color'   => $config['bg_fill_color'],
                'border_color'    => $config['border_color'],
                'font_text_color' => $config['font_text_color'],
            ];
        } else if (isset($carpoolChildTicketFee) && $carpoolChildTicketFee >= 0) {
            $aPriceInfo    = [
                'content'         => $config['children_ticket'],
                'font_color'      => $config['font_color'],
                'bg_fill_color'   => $config['bg_fill_color'],
                'border_color'    => $config['border_color'],
                'font_text_color' => $config['font_text_color'],
            ];
        } else if (isset($carpoolInfantsTicketFee) && $carpoolInfantsTicketFee >= 0) {
            $aPriceInfo    = [
                'content'         => $config['infants_ticket'],
                'font_color'      => $config['font_color'],
                'bg_fill_color'   => $config['bg_fill_color'],
                'border_color'    => $config['border_color'],
                'font_text_color' => $config['font_text_color'],
            ];
        }

        return $aPriceInfo;
    }
}






