<?php
namespace PreSale\Logics\intercityEstimatePrice\multiResponse\component;

use BizCommon\Utils\Horae;
use BizLib\Utils\Language;
use BizLib\Utils\NumberHelper;
use BizLib\Log as NuwaLog;
use BizLib\ErrCode;
use PreSale\Logics\intercityEstimatePrice\multiResponse\ConfigLoader;
use PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender\IntercityBookingSkuRender;
use PreSale\Logics\intercityEstimatePrice\multiResponse\Util;
use BizCommon\Constants\OrderNTuple;
use BizLib\Constants\Common as Constants;

/**
 * Class Fee
 * @package PreSale\Logics\intercityEstimatePrice\multiResponse\component
 */
class Fee implements IComponent
{
    /**
     * @var array aInfo
     */
    private $_aInfo = [];

    /**
     * @var array 文案配置
     */
    private $_aTextConfig = [];


    /**
     * @var array 企业支付信息
     */
    private $_aBusinessPay = [];


    /**
     * ICFee constructor.
     * @param array $aInfo aInfo
     */
    public function __construct(array $aInfo) {
        $this->_aInfo       = $aInfo;
        $this->_aTextConfig = ConfigLoader::getInstance()->getCommonConfig();
        $this->_initBusinessPay();
    }

    /**
     * @param array $aResponse aResponse
     * @return array
     */
    public function build(array $aResponse) {
        $aResponse['fee_amount'] = $this->_getFeeAmount();
        $aResponse['fee_msg']    = $this->_getFeeMsg();
        return $aResponse;
    }


    /**
     * 获取费用描述
     * @return string
     */
    private function _getFeeMsg() {
        //是否命中两口价展示形态
        if ($this->_isHitShowDualPrice()) {
            $fSuccess = $this->_formatPrice($this->_aInfo['activity_info'][0]['estimate_fee']);
            $fFail    = $this->_formatPrice($this->_aInfo['activity_info'][1]['estimate_fee']);
            $sFormat  = $this->_aTextConfig['fee_msg_dual_price'];
            return Language::replaceTag($sFormat, ['fee_succ' => $fSuccess, 'fee_fail' => $fFail]);
        }

        $sFee    = $this->_formatPrice($this->_aInfo['activity_info'][0]['estimate_fee']) ?? '';
        $sFormat = $this->_aTextConfig['fee_msg'] ?? '';
        $sFeeMsg = Language::replaceTag($sFormat, ['fee' => $sFee]);
        if (!empty($this->_aBusinessPay)) {
            return $this->_aBusinessPay['fee_msg'];
        }

        $iProductCategory = $this->_aInfo['order_info']['product_category'] ?? 0;
        if (Util::isTripCloudV3($this->_aInfo)) {
            $aConfig = ConfigLoader::getInstance()->getEstimateItemConfig();
            if (0 == $iProductCategory) {
                NuwaLog::warning(
                    ErrCode\Msg::formatArray(
                        ErrCode\Code::E_COMMON_CONFIG_NOT_FOUNT,
                        ['intercityEstimateItemConfigMissing']
                    )
                );
                return $sFeeMsg;
            }

            if (isset($aConfig[$iProductCategory]) && !empty($aConfig[$iProductCategory]['fee_msg'])) {
                $sFeeMsg = Language::replaceTag($aConfig[$iProductCategory]['fee_msg'], ['fee' => $sFee]);
            }

            if (OrderNTuple::ROUTE_TYPE_NONE == $this->_aInfo['order_info']['route_type']) {
                $aConfigZhunShiZou = ConfigLoader::getInstance()->getEstimateItemZhunShiZouConfig();
                if (isset($aConfigZhunShiZou[$iProductCategory])
                    && !empty($aConfigZhunShiZou[$iProductCategory]['fee_msg'])
                ) {
                    $sFeeMsg = Language::replaceTag($aConfigZhunShiZou[$iProductCategory]['fee_msg'], ['fee' => $sFee]);
                }
            }
        }

        //如果命中了库存模式，说明是 城际拼满走&&开关开启
        if (IntercityBookingSkuRender::getInstance()->isHitSkuModel($iProductCategory)) {
            $aSkuConfig = ConfigLoader::getInstance()->getIntercityBookingSkuConfig()[$iProductCategory];
            $sFeeMsg    = Language::replaceTag($aSkuConfig['fee_msg'], ['fee' => $sFee]);
        }

        return $sFeeMsg;
    }

    /**
     * @return bool
     */
    private function _isHitShowDualPrice() : bool {
        if (!Horae::isInterCityDualPrice($this->_aInfo['order_info'])) {
            return false;
        }

        if (count($this->_aInfo['activity_info']) < 2) {
            return false;
        }

        $fSuccess = $this->_aInfo['activity_info'][0]['estimate_fee'];
        $fFail    = $this->_aInfo['activity_info'][1]['estimate_fee'];
        return $fSuccess != $fFail;
    }


    /**
     * 获取费用金额
     * @return string
     */
    private function _getFeeAmount() {
        $sFee = $this->_aInfo['activity_info'][0]['estimate_fee'] ?? '';
        if (!empty($this->_aBusinessPay)) {
            $sFee = $this->_aBusinessPay['fee_amount'];
        }

        return $sFee;
    }

    /**
     * 展示一位向上取整
     * @param  float $fFee $fFee
     * @return float
     */
    private function _formatPrice($fFee) : float {
        return round(ceil($fFee * 10) / 10, 1);
    }


    /**
     * 初始化企业支付判断
     * @return void
     */
    private function _initBusinessPay() {
        if (!Util::isSupportBusinessPay($this->_aInfo) || !Util::isBusinessPaySelected($this->_aInfo)) {
            return;
        }

        $fFeeAmount       = 0;
        $aMixedDeductInfo = $this->_aInfo['activity_info'][0]['mixed_pay_deduct_info'] ?? [];
        if (isset($aMixedDeductInfo['deduct_fee']) && $aMixedDeductInfo['deduct_fee'] > 0) {
            $fFeeAmount = $this->_aInfo['activity_info'][0]['estimate_fee'] - $aMixedDeductInfo['deduct_fee'];
            if ($fFeeAmount < 0) {
                $fFeeAmount = 0;
            }
        }

        $iDecimals = $fFeeAmount > 0 ? 1 : 0;
        $sFeeMsg   = Language::replaceTag(
            $this->_aTextConfig['business_pay_skin']['fee_msg'] ?? '',
            [
                'fee' => NumberHelper::numberFormatDisplay($fFeeAmount,'',$iDecimals),
            ]
        );

        $this->_aBusinessPay = ['fee_msg' => $sFeeMsg, 'fee_amount' => $fFeeAmount];
    }
}
