<?php
namespace PreSale\Logics\intercityEstimatePrice\multiResponse\component;

/**
 * Class StaticAttr
 * @package PreSale\Logics\intercityEstimatePrice\multiResponse\component
 */
class StaticAttr implements IComponent
{

    /**
     * @var array aInfo
     */
    private $_aInfo = [];

    /**
     * ICStaticAttr constructor.
     * @param array $aInfo aInfo
     */
    public function __construct(array $aInfo) {
        $this->_aInfo = $aInfo;
    }


    /**
     * @param array $aResponse aResponse
     * @return array
     */
    public function build(array $aResponse) {
        $aResponse['route_type']       = $this->_aInfo['order_info']['route_type'] ?? 0;
        $aResponse['require_level']    = $this->_aInfo['order_info']['require_level'] ?? 0;
        $aResponse['business_id']      = $this->_aInfo['order_info']['business_id'] ?? 0;
        $aResponse['product_id']       = $this->_aInfo['order_info']['product_id'] ?? 0;
        $aResponse['combo_type']       = $this->_aInfo['order_info']['combo_type'] ?? 0;
        $aResponse['product_category'] = $this->_aInfo['order_info']['product_category'] ?? 0;
        $aResponse['estimate_id']      = $this->_aInfo['order_info']['estimate_id'] ?? '';
        $aResponse['combo_id']         = $this->_aInfo['order_info']['combo_id'] ?? 0;
        return $aResponse;
    }
}
