<?php
namespace PreSale\Logics\intercityEstimatePrice\multiResponse\component;

use BizCommon\Constants\InterCarpool;
use BizCommon\Logics\Carpool\RouteLogic;
use BizCommon\Models\Service\custom\Route;
use BizCommon\Utils\Horae as BizHorae;
use Disf\SPL\Trace;
use BizLib\Utils\Language;
use BizLib\Log as NuwaLog;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use \BizCommon\Utils\Time;
use PreSale\Logics\intercityEstimatePrice\multiResponse\ConfigLoader;
use Nuwa\ApolloSDK\Apollo;
use BizLib\Constants\Common as Constants;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo as V2MainDataRepo;
use PreSale\Logics\intercityEstimatePrice\multiResponse\MainRender;
use PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender\BookingCompensateLogic;
use PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender\IntercityBookingSkuRender;
use PreSale\Logics\intercityEstimatePrice\multiResponse\Util;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;

/**
 * Class MatchRoute
 * @package PreSale\Logics\intercityEstimatePrice\multiResponse\component
 */
class MatchRoute implements IComponent
{
    // 客企扫二维码预估标识
    const AGENT_TYPE_KEQI = 'keqi_scan';

    /**
     * @var array aInfo
     */
    private $_aInfo = [];

    /**
     * @var string carLevel
     */
    private $_sCarLevel = '';

    /**
     * @var int productID
     */
    private $_iProductId = 0;

    /**
     * @var int comboType
     */
    private $_iComboType = 0;

    /**
     * @var string agentType
     */
    private $_sAgentType = '';

    /**
     * ICMatchRoute constructor.
     * @param array $aInfo aInfo
     */
    public function __construct(array $aInfo) {
        $this->_aInfo            = $aInfo;
        $this->_sCarLevel        = $aInfo['order_info']['require_level'];
        $this->_iProductId       = $aInfo['order_info']['product_id'];
        $this->_iComboType       = $aInfo['order_info']['combo_type'];
        $this->_sAgentType       = $aInfo['common_info']['agent_type'];
    }

    /**
     * @param array $aResponse aResponse
     * @return array
     */
    public function build(array $aResponse) {
        // 客企扫二维码上车 不下发时间片
        if (empty($this->_aInfo['bill_info']['match_routes'])
            || BizHorae::isInterCarpoolGoRightNow($this->_aInfo['order_info'])
            || MatchRoute::AGENT_TYPE_KEQI == $this->_sAgentType
        ) {
            return $aResponse;
        }

        $aTimePairs = RouteLogic::getTimePairs($this->_aInfo['bill_info']['match_routes']['time_span'], false);
        if (empty($aTimePairs)) {
            return $aResponse;
        }

        $aTimePairsAvailableInfo = [];
        //只有再新版本的城际预估页才会调用GetPrematchRecommendInfo获取库存信息
        if (\BizCommon\Constants\Carpool::INTER_CARPOOL_NEW_MODE == $this->_aInfo['bill_info']['match_routes']['inter_mode']) {
            $aTimePairsAvailableReq = [
                'traceid'        => Trace::traceId(),
                'phone'          => $this->_aInfo['passenger_info']['phone'],
                'pid'            => $this->_aInfo['passenger_info']['pid'],
                'city_id'        => $this->_aInfo['order_info']['area'],
                'cur_lng'        => $this->_aInfo['order_info']['current_lng'],
                'cur_lat'        => $this->_aInfo['order_info']['current_lat'],
                'start_lng'      => $this->_aInfo['order_info']['from_lng'],
                'start_lat'      => $this->_aInfo['order_info']['from_lat'],
                'start_name'     => $this->_aInfo['order_info']['from_name'],
                'dest_lng'       => $this->_aInfo['order_info']['to_lng'],
                'dest_lat'       => $this->_aInfo['order_info']['to_lat'],
                'dest_name'      => $this->_aInfo['order_info']['to_name'],
                'appversion'     => $this->_aInfo['common_info']['app_version'],
                'is_from_webapp' => $this->_aInfo['common_info']['is_from_webapp'],
                'productReq'     => $this->_getProductReq(),
                'extMap'         => [
                    'imei'        => $this->_aInfo['common_info']['imei'],
                    'route_group' => $this->_aInfo['bill_info']['match_routes']['route_group'],
                ],
            ];

            $aTimePairsAvailableInfo = Route::getRouteAvailableInfo($aTimePairsAvailableReq);
            $sKey = implode('', [$this->_iComboType, $this->_sCarLevel, $this->_iProductId]);
            $aTimePairsAvailableInfo = $aTimePairsAvailableInfo[$sKey] ?? [];
        }

        $aResponse['match_routes_data'] = $this->_getMatchRouteData($this->_aInfo,$aTimePairsAvailableInfo);
        return $aResponse;
    }


    /**
     * 构建 getRouteAvailableInfo productReq参数
     * @return array
     */
    private function _getProductReq() {
        $aProductType = $aOrderNTuple = [];
        foreach ($this->_aInfo['order_info']['n_tuple'] as $key => $value) {
            $aOrderNTuple[$key] = (string) $value;
        }

        if (!empty($this->_aInfo['order_info']['estimate_id'])) {
            $aOrderNTuple['estimate_id'] = $this->_aInfo['order_info']['estimate_id'];
        }

        $aCurProductType = [
            'product_id'    => $this->_aInfo['order_info']['product_id'],
            'combo_type'    => $this->_aInfo['order_info']['combo_type'],
            'require_level' => $this->_aInfo['order_info']['require_level'],
            'order_n_tuple' => $aOrderNTuple,
        ];
        $aProductType[]  = $aCurProductType;
        return $aProductType;
    }


    /**
     * @param array $aInfo                   $aInfo
     * @param array $aTimePairsAvailableInfo $aTimePairsAvailableInfo
     * @return array
     */
    private function _getMatchRouteData(array $aInfo, array $aTimePairsAvailableInfo) {
        $aTimeSpan = $this->_generateTimeSpan(
            $aInfo['bill_info']['match_routes'],
            $aTimePairsAvailableInfo
        );
        //构造的时间片，将作为库存模式组件的一个物料，所以这里要确保matchRoute在CardExpand组件构建之前
        IntercityBookingSkuRender::getInstance()->setSkuTimeSpan($aTimeSpan);

        // 准时走逻辑改第一个时间片的left_label和title
        $oApolloToggle  = (new Apollo())->featureToggle(
            'gs_intercity_booking_page_new_broad_msg_test',
            [
                \Xiaoju\Apollo\ApolloConstant::APOLLO_INDIVIDUAL_ID => $this->_aInfo['passenger_info']['pid'],
                'city'                                              => $this->_aInfo['order_info']['area'],
                'phone'                                             => $this->_aInfo['passenger_info']['phone'],
            ]
        );
        $bShouldReplace = BizHorae::isInterCityPoolFullGoBooking($aInfo['order_info']) || ($oApolloToggle->allow() && 'control_group' == $oApolloToggle->getGroupName());
        if ($bShouldReplace && !empty($aTimeSpan[0]['range'][0]['value'])) {
            $sTimes     = $aTimeSpan[0]['range'][0]['value'];
            $aRouteConf = RouteLogic::getRouteGroupConfById($aInfo['bill_info']['match_routes']['route_group']);
            // 判断是否需要第一个时间片的左时间点
            if ($this->_needReplaceFirstSpanText($aRouteConf, $sTimes)) {
                $aConf = Language::getDecodedTextFromDcmp('config_inter_carpool-order_common');
                $aTimeSpan[0]['range'][0]['left_label'] = $aConf['first_time_left_label'] ?? '';
                $aTimeSpan[0]['range'][0]['title']      = ($aConf['first_time_left_label'] ?? '') . ($aConf['mvp_version']['state_suffix'] ?? '');
            }
        }

        // 删Title和subTitle
        $sVersion         = $aInfo['common_info']['app_version'] ?? '';
        $aOrderInfoCustom = array(
            'area'            => $aInfo['order_info']['area'],
            'combo_id'        => $aInfo['order_info']['combo_id'],
            'passenger_phone' => $aInfo['passenger_info']['phone'],
            'route_type'      => $aInfo['order_info']['route_type'],
            'carpool_type'    => $aInfo['order_info']['carpool_type'],
            'combo_type'      => $aInfo['order_info']['combo_type'],
        );
        if (!BizHorae::isInterCityPoolFullGoV2($aOrderInfoCustom, $sVersion)) {
            $aTimeSpan = array_map(
                function ($aDayItem) {
                    $aDayItem['range'] = array_map(
                        function ($aSpanItem) {
                            $aSpanItem['title']     = '';
                            $aSpanItem['sub_title'] = '';
                            // 获取添加"预约优惠"tips
                            $sTimeSliceIcon = $this->_getPreferentialIcon($aSpanItem);
                            if (!empty($sTimeSliceIcon)) {
                                $aSpanItem['tips'] = $sTimeSliceIcon;
                            }

                            return $aSpanItem;
                        },
                        $aDayItem['range']
                    );
                    return $aDayItem;
                },
                $aTimeSpan
            );
        }

        $bFirstSpanFit = false;
        // 第一个时间片添加Tag
        if (!empty($aTimeSpan[0]['range'][0]['value'])) {
            $aTimeSpan[0]['range'][0]['tags'] = Language::getDecodedTextFromDcmp('config_inter_carpool-order_common')['price_tag'] ?? '';
            $sFirstSpan = $aTimeSpan[0]['range'][0]['value'];
            $aFirstSpan = json_decode($sFirstSpan);
            if (2 == count($aFirstSpan)) {
                $iTimePoint = $aFirstSpan[1];
                $iTomorrow  = strtotime(date('Y-m-d', time())) + 86400;
                if ($iTimePoint < $iTomorrow) {
                    $bFirstSpanFit = true;
                }
            }
        }

        // 填充勾选字段 $aTimeSpan
        $sDepartureRange = $aInfo['order_info']['departure_range'];
        $iIsSelect       = 0;
        foreach ($aTimeSpan as $iIndex => &$aItem) {
            foreach ($aItem['range'] as $iIndex1 => &$aTimeItem) {
                if ($aTimeItem['value'] == $sDepartureRange) {
                    $aTimeItem['is_select'] = 1;
                    $iIsSelect = 1;
                    break 2;
                }
            }
        }

        if (0 == $iIsSelect) {
            foreach ($aTimeSpan as $iIndex => &$aItem) {
                foreach ($aItem['range'] as $iIndex1 => &$aTimeItem) {
                    $aTimeItem['is_select'] = 1;
                    break 2;
                }
            }
        }

        $aConfig         = ConfigLoader::getInstance()->getCommonConfig();
        $aMatchRouteData = [
            'time_title'     => $aConfig['time_selector']['time_title'] ?? '',
            'sub_title'      => $aConfig['time_selector']['sub_title'] ?? '',
            'left_text'      => $aConfig['time_selector']['left_text'] ?? '',
            'right_text'     => $aConfig['time_selector']['right_text'] ?? '',
            'time_span'      => $aTimeSpan,
            'first_span_fit' => $bFirstSpanFit,
        ];
        return $aMatchRouteData;
    }


    /**
     * Copy from \PreSale\Logics\estimatePrice\multiResponse\component\route\CarpoolRoute::_generateTimeSpan
     * @param array $aRouteInfo              $aRouteInfo
     * @param array $aTimePairsAvailableInfo $aTimePairsAvailableInfo
     * @return mixed
     */
    private function _generateTimeSpan(array $aRouteInfo, array $aTimePairsAvailableInfo) {
        //dds返回的品类决策时间片
        $aTimeSpans = $aRouteInfo['time_span'];
        $iInterMode = $aRouteInfo['inter_mode'];
        $aInterCarpoolCommonText  = json_decode(Language::getTextFromDcmp('config_inter_carpool-order_common'), true);
        $aInterCarpoolBookingText = json_decode(Language::getTextFromDcmp('config_inter_carpool-booking'), true);
        //getPreMach接口返回的库存信息是否是空的，空的说明没有再trigger的时候下发计算库存或者rpc失败了
        $bEmptyAvailableInfo = empty($aTimePairsAvailableInfo);
        $iSkipTimes          = 0;

        //时间片 分为 今天、明天、后天三个维度，每一天中，有根据间隔分割为多个时间片，双重遍历，针对每一个时间片渲染数据
        foreach ($aTimeSpans as $iDayIndex => &$aDayTimeSpan) {
            //内一天钟没有时间片。直接跳过
            if (empty($aDayTimeSpan['range'])) {
                $iSkipTimes++;
                continue;
            }

            //遍历一天中的所有时间片，针对特价城际拼车新旧版本，渲染具体样式（极速拼车没有时间片。。）
            foreach ($aDayTimeSpan['range'] as $iRangeIndex => &$aDayRangeItem) {
                $sTimeValue     = $aDayRangeItem['value'];
                $aTimeItemPairs = json_decode($sTimeValue, true);

                $aDayRangeItem['base_price_desc'] = '';
                $aDayRangeItem['price_desc']      = '';
                $aDayRangeItem['icon_list']       = array();

                if (\BizCommon\Constants\Carpool::INTER_CARPOOL_NEW_MODE == $iInterMode) {
                    $sTimeTag     = implode('',$aTimeItemPairs);
                    $sTimeTagSeat = $sTimeTag.InterCarpool::SKU_INFO_REMAIN_SEATS_TAG;
                    //如果拿库存失败，或者线路没有开启计算库存，默认所有时间片都是可勾选的 反之。按照库存有无决定
                    $bAvailable = $bEmptyAvailableInfo || (bool)($aTimePairsAvailableInfo[$sTimeTag]);
                    $aDayRangeItem['available']      = $bAvailable;
                    $aDayRangeItem['available_tags'] = !$bAvailable ? $aInterCarpoolBookingText['available_tags'] : '';
                    $aDayRangeItem['order_type']     = \BizLib\Constants\OrderSystem::TYPE_ORDER_BOOKING;

                    //命中库存模式后，时间片需要增加库存详情，文案在配置文件中配置
                    $iProductCategory = $this->_aInfo['order_info']['product_category'] ?? 0;
                    //必须拿到真实的时间片上的库存信息，并切命中库存样式
                    if (!$bEmptyAvailableInfo && IntercityBookingSkuRender::getInstance()->isHitSkuModel($iProductCategory)) {
                        $aSkuConfig = ConfigLoader::getInstance()->getIntercityBookingSkuConfig()[$iProductCategory];
                        if ($aTimePairsAvailableInfo[$sTimeTagSeat] > $aSkuConfig['threshold']) {
                            $aDayRangeItem['sku_desc'] = $aSkuConfig['enough_sku_sku_desc'];
                        } else {
                            $aDayRangeItem['sku_desc'] = $aSkuConfig['less_sku_sku_desc'];
                        }

                        $aDayRangeItem['available'] = true;
                    }

                    if (!(0 == $iDayIndex && 0 == $iRangeIndex)) {
                        // 非首个时间片
                        if (BizHorae::isInterCityPoolFullGoBooking($this->_aInfo['order_info'])) {
                            // 只有拼满走透出赔付感知
                            $bHitCompensation = BookingCompensateLogic::getInstance()->isHitCompensation($aTimeItemPairs);
                            if ($bHitCompensation) {
                                $sIconUrl = BookingCompensateLogic::getInstance()->getCompensationConfig()['icon_url_compensate'] ?? '';
                                $aDayRangeItem['icon_list'][] = array('icon' => $sIconUrl);
                            }
                        }
                    }
                } else {
                    $aDayRangeItem['available']      = true;
                    $aDayRangeItem['available_tags'] = '';
                    $aDayRangeItem['order_type']     = \BizLib\Constants\OrderSystem::TYPE_ORDER_NOW;
                }

                $aTimeLable = explode('~',$aDayRangeItem['label']);
                unset($aDayRangeItem['label']);
                $aDayRangeItem['left_label']  = $aTimeLable[0];
                $aDayRangeItem['right_label'] = $aTimeLable[1];

                $aDayRangeItem['title']     = $aDayRangeItem['left_label'] . $aInterCarpoolCommonText['mvp_version']['state_suffix'];
                $aDayRangeItem['sub_title'] = Language::replaceTag(
                    $aInterCarpoolCommonText['mvp_version']['state_desc'],
                    array(
                        'expect_time'    => Time::sec2HM($aDayRangeItem['fullgo_expect_time']),
                        'departure_time' => $aDayRangeItem['right_label'],
                    )
                );
                // 无效时不高亮时间
                $aTemp = array('{', '}');
                if (!$aDayRangeItem['available']) {
                    $aDayRangeItem['sub_title'] = str_replace($aTemp, '', $aDayRangeItem['sub_title']);
                }
            }
        }

        return $aTimeSpans;
    }



    /**
     * 判断是否需要替换第一个时间片的leftLabel文案
     * @param array  $aRouteConfig 路线配置
     * @param string $sTimes       时间片value[1598250978,1598250978]
     * @return bool
     */
    private function _needReplaceFirstSpanText($aRouteConfig, $sTimes) {
        //有库存模式，不需要替换第一个时间片的左时间片文案
        if (IntercityBookingSkuRender::getInstance()->isHitSkuModel($this->_aInfo['order_info']['product_category'])) {
            return false;
        }

        $aTimes = json_decode($sTimes, true);
        if (count($aTimes) <= 0) {
            return false;
        }

        $sLeftTime = date('H:i:s', $aTimes[0]);

        // 遍历当前路线的时间片的左时间
        if (isset($aRouteConfig['time_span']['range']) && is_array($aRouteConfig['time_span']['range'])) {
            foreach ($aRouteConfig['time_span']['range'] as $aRange) {
                if (!empty($aRange) && $sLeftTime == $aRange['from']) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 获取"预约优惠"文本icon（满足三个条件：一 开关，二 实验，三 大于时间片间隔）
     * @param array $aSpanItem $aSpanItem
     * @return string
     */
    private function _getPreferentialIcon($aSpanItem) {
        $sTimeSliceIcon = '';
        $oToggle        = Apollo::getInstance()->featureToggle(
            'intercity_carpool_subscribe_preferential_toggle',
            $this->_getApolloParam()
        );
        if (!$oToggle->allow()) {
            return $sTimeSliceIcon;
        }

        $oExperiment = Apollo::getInstance()->featureToggle(
            'yuantu_reservation_discount',
            $this->_getApolloParam()
        );
        // 满足开关，满足实验后，获取开关的时间间隔，判断时间片是否出icon
        if ($oExperiment->allow() && 'treatment_group' == $oExperiment->getGroupName()) {
            // 获取时间间隔(s)
            $iTimeInterval = $oToggle->getParameter('time_interval', 0) * 60;

            $aDepartureRange        = json_decode($aSpanItem['value'], true);
            $iDepartureTimeEarliest = $aDepartureRange[0] ?? 0;
            // 若左时间片 - 当前时间 > 时间间隔，就出text
            if ($iDepartureTimeEarliest - time() > $iTimeInterval) {
                $sTimeSliceIcon = $oToggle->getParameter('time_slice_text', '');
            }
        }

        return $sTimeSliceIcon;
    }

    /**
     * 获取Apollo请求参数
     * @return array
     */
    private function _getApolloParam() {
        return [
            'key'              => $this->_aInfo['passenger_info']['pid'],
            'phone'            => $this->_aInfo['passenger_info']['phone'],
            'pid'              => $this->_aInfo['passenger_info']['pid'],
            'uid'              => $this->_aInfo['passenger_info']['uid'],
            'city'             => $this->_aInfo['order_info']['area'],
            'combo_id'         => $this->_aInfo['order_info']['combo_id'],
            'route_group'      => $this->_aInfo['order_info']['match_routes'][0]['route_group'],
            'product_id'       => $this->_aInfo['order_info']['product_id'],
            'product_category' => $this->_aInfo['order_info']['product_category'],
            'access_key_id'    => $this->_aInfo['common_info']['access_key_id'],
            'app_version'      => $this->_aInfo['common_info']['app_version'],
        ];
    }
}
