<?php
namespace PreSale\Logics\intercityEstimatePrice\multiResponse\component;

use BizLib\Config;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\intercityEstimatePrice\multiResponse\globalData\CarpoolIntercityRule;
use PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender\LongRentMockLogic;

/**
 * Class CarpoolSeat
 * @package PreSale\Logics\intercityEstimatePrice\multiResponse\component
 */
class CarpoolSeat implements IComponent
{

    const CARPOOL_WITHOUT_SEAT = 0;

    const OTHER_CARPOOL_TYPE = 1;

    const INTERCITY_CARPOOL_TYPE = 3;

    const INTERCITY_CARPOOL_FOUR_TYPE = 5;

    /**
     * @var array aInfo
     */
    private $_aInfo = [];

    /**
     * @var int|mixed comboType
     */
    private $_iCarpoolComboType = 0;

    /**
     * ICCarpoolSeat constructor.
     * @param array $aInfo aInfo
     */
    public function __construct(array $aInfo) {
        $this->_aInfo = $aInfo;
        $this->_iCarpoolComboType = $aInfo['order_info']['combo_type'] ?? 0;
    }

    /**
     * @param array $aResponse aResponse
     * @return array
     */
    public function build(array $aResponse) {
        $aCarpoolSeatNum          = $this->_getPoolSeatNum($this->_aInfo);
        $iSelectStationCarpoolNum = $this->_aInfo['order_info']['carpool_seat_num'];
        $carpoolType = self::OTHER_CARPOOL_TYPE;
        // 城际拼车
        if (\BizLib\Utils\Horae::isInterCityCarPoolScene($this->_iCarpoolComboType)) {
            $carpoolType = self::INTERCITY_CARPOOL_TYPE;
            $aControl    = [
                'key'   => $this->_aInfo['passenger_info']['pid'] ?? 0,
                'city'  => $this->_aInfo['order_info']['area'],
                'phone' => $this->_aInfo['passenger_info']['phone'] ?? '',
            ];
            // 四座包车文案
            $oApolloFeatureToggle = Apollo::getInstance()->featureToggle('gs_intercity_carpool_seat_msg_toggle', $aControl);
            if ($oApolloFeatureToggle->allow()) {
                $carpoolType = self::INTERCITY_CARPOOL_FOUR_TYPE;
            }
        }

        $aResponse['carpool_seat_module'] = $this->_getCarpoolSeatModule($aCarpoolSeatNum,$iSelectStationCarpoolNum,0,$carpoolType);
        if (1 == $this->_aInfo['biz_common_info']['is_reset_seat']) {
            $aResponse['carpool_seat_module']['select_value'] = 1;
        }

        $childTicketRule = CarpoolIntercityRule::getInstance()->getChildTicketRule();
        if (!empty($childTicketRule) && 1 == $childTicketRule['is_allow_child_ticket']) {
            $aResponse['carpool_seat_module']['select_count_child_ticket'] = $aResponse['carpool_seat_module']['select_value'];
        }

        if (LongRentMockLogic::getInstance()->isHitLongRentMock($this->_aInfo['order_info']['product_category'] ?? 0)) {
            $seatsExceedToastLongRent = LongRentMockLogic::getInstance()->getLongRentMockConfig($this->_aInfo['order_info']['product_category'] ?? 0)['seats_exceed_toast'];
            if (!empty($seatsExceedToastLongRent)) {
                $aResponse['carpool_seat_module']['seats_exceed_toast'] = $seatsExceedToastLongRent;
            }
        }

        return $aResponse;
    }


    /**
     * @param array $aInfo aInfo
     * @return array|mixed
     */
    private function _getPoolSeatNum(array $aInfo) {
        $aCarpoolSeatConfig = $aInfo['bill_info']['carpool_seat_config'];
        if (!empty($aCarpoolSeatConfig)) {
            return $aCarpoolSeatConfig;
        }

        // 下面是兜底逻辑，兼容上线过程中，倒流导致报错
        Config::config('config_carpool', true);
        $aAllPoolSeatNum = Config::config('config_carpool', 'pool_seat_open');
        $aPoolSeats      = $aAllPoolSeatNum[$aInfo['order_info']['district']] ?? $aAllPoolSeatNum['default'];
        return $aPoolSeats;
    }


    /**
     * @param array $aCarpoolSeatNum   $aCarpoolSeatNum
     * @param int   $iSelectCarpoolNum $iSelectCarpoolNum
     * @param int   $iWithSeat         $iWithSeat
     * @param int   $iType             $iType
     * @return array
     */
    private function _getCarpoolSeatModule(array $aCarpoolSeatNum, $iSelectCarpoolNum, $iWithSeat = self::CARPOOL_WITHOUT_SEAT, $iType = self::OTHER_CARPOOL_TYPE) {
        if (empty($aCarpoolSeatNum)) {
            return [];
        }

        $aReplaceVars['cap_discount_fee'] = 0;
        $aReplaceVars['max_num']          = count($aCarpoolSeatNum);
        $aCarpoolSeatModuleConfig         = Config::text('config_carpool', 'carpool_seat_module', $aReplaceVars);
        if (empty($aCarpoolSeatModuleConfig)) {
            return [];
        }

        // 构建seat_config
        $aSeatConfigs        = [];
        $aSeatConfigWithSeat = $aCarpoolSeatModuleConfig[$iWithSeat][$iType];
        $aSeatConfigLocal    = $aSeatConfigWithSeat['seat_config'];
        foreach ($aCarpoolSeatNum as $key => $iSeatNum) {
            // 命中的seat_config
            $aUsedConfig = empty($aSeatConfigLocal[$iSeatNum]) ? $aSeatConfigLocal['default'] : $aSeatConfigLocal[$iSeatNum];
            // seat_config的构建
            $iISJoint           = $aUsedConfig['isjoint'];
            $sLabel2            = (1 == $iISJoint) ? sprintf($aUsedConfig['label_2'], $iSeatNum) : $aUsedConfig['label_2'];
            $aSeatConfig        = [
                'value'   => $iSeatNum,
                'label_2' => $sLabel2, // 比label结尾多一个空格
            ];
            $aSeatConfigs[$key] = $aSeatConfig;
        }

        // 预估默认构建选中座位
        $iSelectValue = (int)$iSelectCarpoolNum;
        if ($iSelectValue > count($aSeatConfigs)) {
            $iSelectValue = count($aSeatConfigs);
        }

        $iSelectIndex          = $iSelectValue - 1;
        $aCarpoolSeatModuleRet = [
            'seat_config'        => $aSeatConfigs,
            'seats_exceed_toast' => $aSeatConfigWithSeat['seats_exceed_toast'] ?? '',
            'select_value'       => $iSelectValue,
            'select_index'       => $iSelectIndex,
            'button_desc'        => $aSeatConfigWithSeat['button_desc'] ?? '',
        ];
        return  $aCarpoolSeatModuleRet;
    }
}
