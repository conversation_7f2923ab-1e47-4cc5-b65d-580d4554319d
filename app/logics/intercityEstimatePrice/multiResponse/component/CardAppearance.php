<?php
namespace PreSale\Logics\intercityEstimatePrice\multiResponse\component;

use BizCommon\Utils\Horae as BizHorae;
use PreSale\Logics\intercityEstimatePrice\multiResponse\ConfigLoader;
use BizLib\Log as NuwaLog;
use BizLib\ErrCode;
use BizCommon\Constants\OrderNTuple;
use BizLib\Constants\Common as Constants;
use BizLib\Utils\ProductCategory;
use PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender\BookingCompensateLogic;
use PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender\IntercityBookingSkuRender;
use PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender\LongRentMockLogic;
use PreSale\Logics\intercityEstimatePrice\multiResponse\Util as Util;

/**
 * Class CardAppearance
 * @package PreSale\Logics\intercityEstimatePrice\multiResponse\component
 */
class CardAppearance implements IComponent
{
    /**
     * @var array aInfo
     */
    private $_aInfo = [];

    /**
     * ICCardAppearance constructor.
     * @param array $aInfo aInfo
     */
    public function __construct(array $aInfo) {
        $this->_aInfo = $aInfo;
    }

    /**
     * @param array $aResponse aResponse
     * @return array
     */
    public function build(array $aResponse) {
        $aConfig          = ConfigLoader::getInstance()->getEstimateItemConfig();
        $iProductCategory = $this->_aInfo['order_info']['product_category'] ?? 0;
        if (0 == $iProductCategory) {
            NuwaLog::warning(ErrCode\Msg::formatArray(ErrCode\Code::E_COMMON_CONFIG_NOT_FOUNT,['intercityEstimateItemConfigMissing']));
            return $aResponse;
        }

        if (isset($aConfig[$iProductCategory])) {
            $aResponse['intro_image']  = $aConfig[$iProductCategory]['intro_image'] ?? '';
            $aResponse['bg_image']     = $aConfig[$iProductCategory]['bg_image'] ?? '';
            $aResponse['border_color'] = $aConfig[$iProductCategory]['border_color'] ?? '';
            $aResponse['corner_image'] = $aConfig[$iProductCategory]['corner_image'] ?? '';
        }

        // 客企接入小程序需求：准时走走另一个配置
        if (OrderNTuple::ROUTE_TYPE_NONE == $this->_aInfo['order_info']['route_type']
            && Util::isTripCloudV3($this->_aInfo)
        ) {
            $aConfigZhunShiZou = ConfigLoader::getInstance()->getEstimateItemZhunShiZouConfig();
            if (isset($aConfigZhunShiZou[$iProductCategory])) {
                $sIntroImage  = $aConfigZhunShiZou[$iProductCategory]['intro_image'] ?? '';
                $sBgImage     = $aConfigZhunShiZou[$iProductCategory]['bg_image'] ?? '';
                $sBorderColor = $aConfigZhunShiZou[$iProductCategory]['border_color'] ?? '';
                $sCornerImage = $aConfigZhunShiZou[$iProductCategory]['corner_image'] ?? '';
                if (!empty($sIntroImage)) {
                    $aResponse['intro_image'] = $sIntroImage;
                }

                if (!empty($sBgImage)) {
                    $aResponse['bg_image'] = $sBgImage;
                }

                if (!empty($sBorderColor)) {
                    $aResponse['border_color'] = $sBorderColor;
                }

                if (!empty($sCornerImage)) {
                    $aResponse['corner_image'] = $sCornerImage;
                }
            }
        }

        // 拼满走命中赔付
        if (ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTE == $iProductCategory
            && BizHorae::isInterCityPoolFullGoBooking($this->_aInfo['order_info'])
        ) {
            $bRepIntroImg = $this->_isReplaceIntroImage($this->_aInfo['order_info']);
            if ($bRepIntroImg) {
                $aResponse['intro_image'] = BookingCompensateLogic::getInstance()->getCompensationConfig()['intro_image_compensate'] ?? '';
            }
        }

        //如果命中了库存模式，说明是 城际拼满走&&开关开启
        if (IntercityBookingSkuRender::getInstance()->isHitSkuModel($iProductCategory)) {
            $aSkuConfig = ConfigLoader::getInstance()->getIntercityBookingSkuConfig()[$iProductCategory];
            $aResponse['intro_image']  = $aSkuConfig['intro_image'] ?? '';
            $aResponse['bg_image']     = $aSkuConfig['bg_image'] ?? '';
            $aResponse['border_color'] = $aSkuConfig['border_color'] ?? '';
            $aResponse['corner_image'] = $aSkuConfig['corner_image'] ?? '';
        }

        if (LongRentMockLogic::getInstance()->isHitLongRentMock($iProductCategory)) {
            $introImageLongRent = LongRentMockLogic::getInstance()->getLongRentMockConfig($iProductCategory)['intro_image'];
            if (!empty($introImageLongRent)) {
                $aResponse['intro_image'] = $introImageLongRent;
            }
        }

        return $aResponse;
    }

    /**
     * 当前出发时间片（非首个时间片）命中赔付，替换intro_image
     * @param array $aOrderInfo aOrderInfo
     * @return mixed
     */
    private function _isReplaceIntroImage($aOrderInfo) {
        $sFirstTimeSpan  = $aOrderInfo['match_routes'][0]['time_span'][0]['range'][0]['value'] ?? '[]';  // eg. "[1641980259,1641981600]"
        $aFirstTimeSpan  = json_decode($sFirstTimeSpan, true);
        $sDepartureRange = $aOrderInfo['departure_range'];              // eg. "[1641980187,1641981600]"
        $aDepartureRange = json_decode($sDepartureRange, true);
        if (empty($aFirstTimeSpan) || empty($aDepartureRange)) {
            return false;
        }

        if ($aDepartureRange[1] == $aFirstTimeSpan[1]) {
            return false;
        }

        return BookingCompensateLogic::getInstance()->isHitCompensation($aDepartureRange);
    }
}
