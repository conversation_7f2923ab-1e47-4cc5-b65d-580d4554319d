<?php
namespace PreSale\Logics\intercityEstimatePrice\multiResponse\component;

/**
 * Class ProductInfo
 * @package PreSale\Logics\intercityEstimatePrice\multiResponse\component
 */
class ProductInfo implements IComponent
{
    /**
     * @var array aInfo
     */
    private $_aInfo = [];

    /**
     * ProductInfo constructor.
     * @param array $aInfo aInfo
     */
    public function __construct(array $aInfo) {
        $this->_aInfo = $aInfo;
    }


    /**
     * @param array $aResponse aResponse
     * @return array
     */
    public function build(array $aResponse) {
        if ($this->isHaveRedPacket()) {
            $aResponse['hit_show_h5_type'] = 1;
        }

        return $aResponse;
    }

    /**
     * @return bool
     */
    public function isHaveRedPacket() {

        $aBillInfo     = $this->_aInfo['bill_info'] ?? [];
        $aDisplayLines = $aBillInfo['display_lines'] ?? [];
        $aDisplayLines = array_combine(array_column($aDisplayLines, 'name'), $aDisplayLines);
        $fRedPacket    = $aDisplayLines['red_packet']['value'] ?? 0.0;

        if ($fRedPacket > 0) {
            return true;
        }

        return false;
    }
}
