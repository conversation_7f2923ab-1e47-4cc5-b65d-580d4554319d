<?php
namespace PreSale\Logics\intercityEstimatePrice\multiResponse\component;

use BizLib\Utils\Language;

/**
 * Class SpecialPriceTip
 * @package PreSale\Logics\intercityEstimatePrice\multiResponse\component
 */
class SpecialPriceTip implements IComponent
{

    const RULE_TYPE_EXTRA_FEE = 5;  // 附加费，目前只有高速费
    const RULE_TYPE_RED_PACKET = 9;  // 节假日服务费
    /**
     * @var array aInfo
     */
    private $_aInfo = [];

    /**
     * @var string carLevel
     */
    private $_sCarLevel = '';

    /**
     * ICSpecialPriceTip constructor.
     * @param array $aInfo aInfo
     */
    public function __construct(array $aInfo) {
        $this->_aInfo     = $aInfo;
        $this->_sCarLevel = $aInfo['order_info']['require_level'];
    }


    /**
     * @param array $aResponse aResponse
     * @return array
     */
    public function build(array $aResponse) {
        $aSpecialPrice = [];
        $aRuleType     = [];
        $iHighwayFee   = $this->_aInfo['bill_info']['highway_fee'] ?? 0;
        if ($iHighwayFee) {
            $aRuleType[] = self::RULE_TYPE_EXTRA_FEE;
        }

        if (0 == count($aRuleType)|| empty($this->_aInfo['passenger_info']['token'])) {
            return $aResponse;
        }

        if ($this->_aInfo['bill_info']['fee_detail_info']['red_packet'] > 0) {
            $aRuleType[] = self::RULE_TYPE_RED_PACKET;
        }

        if (count($aRuleType) > 1) {
            //命中多种特殊计价
            $aResponse['special_price_text']['rule_type'] = $aRuleType;
            $aResponse['special_price_text']['text']      = Language::getTextFromDcmp('special_price_rule_explain-price_text_multiple');
        } else {
            $aSpecialPrice['rule_type'] = $aRuleType;
            switch ($aRuleType[0]) {
                case self::RULE_TYPE_EXTRA_FEE:
                    $aSpecialPrice['text']
                        = Language::getTextFromDcmp('special_price_rule_explain-price_text_highway_fee');
                    break;
                case self::RULE_TYPE_RED_PACKET:
                    $aResponse['special_price_text']['text']
                        = Language::getTextFromDcmp('special_price_rule_explain-text_red_packet');
                    break;
                default:
                    break;
            }

            $aResponse['special_price_text'] = $aSpecialPrice;
        }


        $aResponse['special_price_text'] = $aSpecialPrice;
        return $aResponse;
    }
}
