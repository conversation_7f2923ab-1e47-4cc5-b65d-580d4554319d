<?php
namespace PreSale\Logics\intercityEstimatePrice\multiResponse\globalData;

use BizLib\Client\TicketPriceClient;
use BizLib\Client\TripcloudPassenger;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\estimatePrice\ArrayDiff;
use PreSale\Logics\intercityEstimatePrice\multiRequest\Product;
use BizLib\Log as NuwaLog;

;


/**
 * class CarpoolIntercityRule
 */
class CarpoolIntercityRule
{
    private static $_oInstance;

    const ChildTicketRule = 1;
    const AdditionalChargeRule = 6;
    const CancelChargeRule = 7;
    const AdditionalAndCancelChargeRule = 8;

    const AdditionalChargeRuleStyleType_BottomRule = 0; // 底部沟通组件
    const AdditionalChargeRuleStyleTYpe_PopUp = 1; // 计价弹窗

    const CHILDRULESCENEOLD    = "child_rule_scene_old";

    /**
     * @var Product $_oKeqiProduct 客企品类
     */
    private $_oKeqiProduct = null;

    /**
     * @var array $_oCarpoolIntercityRule 返回值
     */
    private $_oCarpoolIntercityRule;

    /**
     * constructor
     */
    private function __construct() {}

    /**
     * @return CarpoolIntercityRule
     */
    public static function getInstance() {
        if (self::$_oInstance == null) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }


    /**
     * 获取AthenaExtension 数据返回
     * @param Product $keqiProduct $keqiProduct
     * @return void
     */
    public function fetchChildTicketRule($keqiProduct) {
        if (empty($keqiProduct)) {
            return;
        }

        $this->_oKeqiProduct = $keqiProduct;
        $params = [
            'rule_type'   => self::ChildTicketRule,
            'business_id' => $keqiProduct->oOrderInfo->iBusinessId,
            'combo_id'    => $keqiProduct->oOrderInfo->iComboId,
            'scene'       =>self::CHILDRULESCENEOLD,
        ];
        $resp = $this->getPGetCarpoolIntercityRule($params);
        if (empty($resp) || 0 != $resp['errno']) {
            return;
        }

        $this->_oCarpoolIntercityRule = $resp['data'];
    }

    /**
     * 获取客企的附加费和取消费规则文案
     * @param int $iBusinessId ...
     * @param int $iComboId ...
     * @return  void
     */
    public function fetchRule($iBusinessId, $iComboId, $iRuleType) {
        $params = [
            'rule_type'   => $iRuleType,
            'business_id' => $iBusinessId,
            'combo_id'    => $iComboId,
        ];
        $resp = $this->getPGetCarpoolIntercityRule($params);
        if (empty($resp) || 0 != $resp['errno']) {
            return;
        }

        $this->_oCarpoolIntercityRule = $resp['data'];
    }

    /**
     * @return array
     */
    public function getChildTicketRule() {
        if (empty($this->_oCarpoolIntercityRule)) {
            return null;
        }

        return $this->_oCarpoolIntercityRule['child_ticket_rule'];
    }

    /**
     * @param int $iType ...
     * @return mixed|null
     */
    public function getAdditionalChargeRuleByStyleType($iType) {
        if (empty($this->_oCarpoolIntercityRule)) {
            return null;
        }

        if (!is_array($this->_oCarpoolIntercityRule['additional_charge_rule'])
            || empty($this->_oCarpoolIntercityRule['additional_charge_rule'])
        ) {
            return null;
        }

        foreach ($this->_oCarpoolIntercityRule['additional_charge_rule'] as $aRule) {
            if (!empty($aRule) && isset($aRule['style_type']) && $aRule['style_type'] == $iType) {
                return $aRule;
            }
        }

        return null;
    }

    /**
     * @return mixed|null
     */
    public function getCancelChargeRule() {
        if (empty($this->_oCarpoolIntercityRule)) {
            return null;
        }

        return $this->_oCarpoolIntercityRule['estimate_cancel_charge_rule'];
    }

    /**
     * @return mixed|null
     */
    public function getInterCityCarpoolPriceRule() {
        if (empty($this->_oCarpoolIntercityRule)) {
            return null;
        }

        return $this->_oCarpoolIntercityRule['intercity_carpool_price_rule'];
    }

    /**
     * @return Product
     */
    public function getKeqiProduct() {
        return $this->_oKeqiProduct;
    }

    /**
     * @param array $params $params
     * @return array|mixed
     */
    public function getPGetCarpoolIntercityRule(array $params) {
        $sMoveType = "";
        $aParameters = array();
        $oFeatureToggle = Apollo::getInstance()->featureToggle(
            'gs_intercity_move_addition_rule_switch',
            [
                'key'         => $params['business_id'],
                'business_id' => $params['business_id'],
                'combo_id'    => $params['combo_id'],
                'rule_type'   => $params['rule_type'],
            ]
        );
        if ($oFeatureToggle->allow()) {
            $sMoveType = $oFeatureToggle->getParameter("move_type", "");
            $aParameters = $oFeatureToggle->getAllParameters();
        }
        switch ($sMoveType) {
            case "diff":
                $client = new TripcloudPassenger();
                $resp = $client->pGetCarpoolIntercityRule($params);
                $newResp = (new TicketPriceClient())->pGetCarpoolIntercityRule($params);
                $isAllowChildTicket=$resp['data']['child_ticket_rule']['is_allow_child_ticket']??0;
                // 比diff
                if(($params['rule_type']==self::ChildTicketRule&&$isAllowChildTicket==0)) {
                    if($newResp['data']['child_ticket_rule']['is_allow_child_ticket']!=0) {
                        NuwaLog::info(Msg::formatArray(Code::E_COMMON_HTTP_TRIPCLOUD_FEE_DETAIL, ['business_id' => $params['business_id'], 'has intercity_child_rule_diff exit' => 'err']));
                    }
                } else {
                    if ($params['rule_type']==self::ChildTicketRule) {
                        $aParameters['ignore_added_paths']=$aParameters['ignore_added_paths'].",/additional_charge_rule";
                    }
                    $aIgnorePaths = array(
                        'ignore_added_paths'    => explode(',', $aParameters['ignore_added_paths']),
                        'ignore_removed_paths'  => explode(',', $aParameters['ignore_removed_paths']),
                        'ignore_modified_paths' => explode(',', $aParameters['ignore_modified_paths']),
                        'ignore_diff_hash'      => explode(',', $aParameters['ignore_diff_hash']),
                        'simple_hash_enable'    => (bool)($aParameters['simple_hash_enable']),
                    );
                    $aDiff = ArrayDiff::compareDiff($resp['data'], $newResp['data'], $aIgnorePaths);
                    if (!empty($aDiff)) {
                        NuwaLog::info(Msg::formatArray(Code::E_COMMON_HTTP_TRIPCLOUD_FEE_DETAIL, ['business_id' => $params['business_id'], 'has intercity_rule_diff exit' => json_encode($aDiff)]));
                    }
                }
                break;
            case "finish":
                $resp = (new TicketPriceClient())->pGetCarpoolIntercityRule($params);
                break;
            default:
                $client = new TripcloudPassenger();
                $resp = $client->pGetCarpoolIntercityRule($params);
                break;
        }
        return $resp;
    }
}
