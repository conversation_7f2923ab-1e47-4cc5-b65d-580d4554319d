<?php
namespace PreSale\Logics\intercityEstimatePrice\multiResponse;

use BizLib\Constants\Common;
use BizLib\Utils\ProductCategory;
use Dirpc\SDK\PreSale\UserPayInfoItem;
use PreSale\Logics\commonAbility\SpringRedPacketFormatter;
use PreSale\Logics\estimatePrice\multiResponse\MainHelper;
use PreSale\Logics\estimatePrice\multiResponse\Util as EstUtil;
use PreSale\Logics\intercityEstimatePrice\multiRequest\Product;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Constants\Common as Constants;
use Nuwa\ApolloSDK\Apollo;
use Disf\SPL\Trace;
use PreSale\Logics\intercityEstimatePrice\multiResponse\component\IComponent;
use BizLib\Utils\Language;
use BizLib\Config as NuwaConfig;
use PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender\BookingCompensateLogic;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender\IntercityBookingSkuRender;
use PreSale\Logics\intercityEstimatePrice\multiResponse\PreRender\LongRentMockLogic;
use BizLib\Utils\UtilHelper;
use Dirpc\SDK\PreSale\IntercityEstimatePriceRequest as Request;

/**
 * Class MainRender
 * @package PreSale\Logics\intercityEstimatePrice\multiResponse
 */
class MainRender
{
    const INTER_CITY_CONF_BANNER_NS = 'intercity_carpool_estimate_banner_conf';

    /**
     * @var MainRender 单例对象
     */
    protected static $_oInstance;

    /**
     * @var array 品类数据
     */
    protected $_aInfos = [];



    /**
     * @var Product[] 品类信息
     */
    protected $_aProductList = [];

    /**
     * @var array 请求
     */
    protected $_oReq = [];

    /**
     * @var array
     */
    private $_aCommonInfo = [];


    /**
     * MainRender constructor.
     * @param array $aInfos 品类数据
     */

    /**
     * MainRender constructor.
     * @param array   $aInfos          品类数据
     * @param array     $oCommonInfo 通用信息
     * @param Product ...$aProductList 品类信息
     * @param Request $oReq
     */
    private function __construct(array $aInfos, $oCommonInfo, $oReq, Product ...$aProductList) {
        $this->_aInfos       = $aInfos;
        $this->_aCommonInfo  = $oCommonInfo;
        $this->_aProductList = $aProductList;
        $this->_oReq         = $oReq;
    }


    /**
     * @param array   $aInfos          品类数据
     * @param array     $oCommonInfo    通用信息
     * @param Product ...$aProductList 品类信息
     * @param  Request $oReq
     * @return MainRender
     */
    public static function getInstance(array $aInfos, $oCommonInfo, $oReq, Product ...$aProductList) {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self($aInfos, $oCommonInfo, $oReq, ...$aProductList);
        }

        return self::$_oInstance;
    }


    /**
     * @return $this
     */
    public function buildAInfo() {
        return $this;
    }


    /**
     * @return array|bool
     * @throws \Exception ...
     */
    public function multiExecute() {
        $aResponse = UtilsCommon::getErrMsg(GLOBAL_SUCCESS);
        // 数据预渲染
        $this->preRender();
        // 品类数据渲染
        $aResponse['data']['estimate_data'] = $this->buildEstimateData();
        // 全局数据渲染
        $aResponse = $this->buildExternalData($aResponse);

        return $aResponse;
    }


    /**
     * @return void
     */
    public function preRender() {
        // 配置初始化
        ConfigLoader::getInstance()->init();
        // 过滤未开城的拼车
        $this->_filterCarpoolNotOpen();

        BookingCompensateLogic::getInstance()->init($this->_aInfos);
        //初始化城际预约单库存模式相关功能
        IntercityBookingSkuRender::getInstance()->init($this->_aInfos);
        LongRentMockLogic::getInstance()->init($this->_aInfos);
    }


    /**
     * @return array
     */
    public function buildEstimateData() {
        $aEstimateData = [];
        foreach ($this->_aInfos as $aInfo) {
            $aInfo['biz_common_info'] = $this->_aCommonInfo;
            $aResponse   = [];
            $aComponents = $this->_getComponents($aInfo);
            foreach ($aComponents as $oComponent) {
                $aResponse = $oComponent->build($aResponse);
            }

            $aEstimateData[] = $aResponse;
        }
        $aEstimateData = $this->_setDefaultSelection($aEstimateData);
        return $aEstimateData;
    }


    /**
     * 获取实现接口的组件列表
     * @param array $aInfo $aInfo
     * @return IComponent[]
     */
    private function _getComponents(array $aInfo) {
        $aClass        = [
            // [intro_image] [bg_image] [border_color] [corner_image]
            component\CardAppearance::class,

            // [special_price_text]
            component\SpecialPriceTip::class,

            // [fee_msg] [fee_amount]
            component\Fee::class,

            // [route_type] [require_level] [business_id] [product_id] [combo_type] [product_category] [estimate_id] [combo_id]
            component\StaticAttr::class,

            // [feature_title] [feature_sub_title]
            component\FeatureAttr::class,

            // [carpool_seat_module]
            component\CarpoolSeat::class,

            // [match_routes_data]
            component\MatchRoute::class,

            // [price_info_desc]
            component\PriceInfo::class,

            // [user_pay_info]
            component\UserPayInfo::class,

            //[match_show_sku_data]
            component\CardExpand::class,

            //[hit_show_h5_type]
            component\ProductInfo::class,

            // [support_select_seat]
            component\SupportSelectSeat::class,
        ];
        $aObjComponent = [];
        foreach ($aClass as $sClass) {
            $aObjComponent[] = new $sClass($aInfo);
        }

        return $aObjComponent;
    }


    /**
     * @param array $aResponse 接口Response
     * @return array
     * @throws \Exception ...
     */
    public function buildExternalData(array $aResponse) {
        $aResponse['data']['estimate_trace_id'] = Trace::traceId();

        // 跳转链接
        $aConfig = ConfigLoader::getInstance()->getCommonConfig();
        $aResponse['data']['fee_detail_url'] = $aConfig['fee_detail_url'] ?? '';

        // 头图配置
        list($sBackgroundUrl,$sDetailUrl)    = $this->_getHeadPicAndLink();
        $aResponse['data']['background_url'] = $sBackgroundUrl;
        $aResponse['data']['detail_url']     = $sDetailUrl;

        // 获取整合的头图链接
        list($isNewTidy, $sBanner, $sDetail) = $this->_getBannerInfo();
        if ($isNewTidy) {
            $aResponse['data']['background_url'] = $sBanner ?: $aResponse['data']['background_url'];
            $aResponse['data']['detail_url']     = $sDetail ?: $aResponse['data']['detail_url'];
        }

        // 支付方式
        $aResponse['data']['user_pay_info'] = $this->_getUserPayInfo();

        // force_notice_toast
        $aResponse['data']['force_notice_toast'] = $this->_getForceNoticeToast($aResponse['data']['estimate_data']);

        //carpool_seat_module
        $aResponse['data']['carpool_seat_module'] = $this->_getExternalCarpoolSeatModule($aResponse);

        //plugin_page_info
        $aResponse['data']['plugin_page_info'] = $this->_getPluginPageInfo();

        // barrage_text
        if ($this->_oReq->getRouteId() > 0) { // 校园热线
            $aResponse['data']['barrage_text'] = Language::getTextFromDcmp('carpool_intercity-barrage_text');
        }

        return $aResponse;
    }

    /**
     * @param array $aResponse $aResponse
     * @return array|mixed
     */
    private function _getExternalCarpoolSeatModule($aResponse) {
        $aSeatModule = [];
        $iMaxSeat    = -1;
        foreach ($aResponse['data']['estimate_data'] as $aData) {
            $iCurMax = count($aData['carpool_seat_module']['seat_config']);
            if ($iCurMax > $iMaxSeat) {
                $aSeatModule = $aData['carpool_seat_module'];
                $iMaxSeat    = $iCurMax;
            }
        }

        return $aSeatModule;
    }


    /**
     * 过滤拼车未开城对应的aInfo
     * @return void
     */
    private function _filterCarpoolNotOpen() {
        foreach ($this->_aInfos as $iIndex => $aInfo) {
            if (EstUtil::isCarpool($aInfo) && empty($aInfo['bill_info']['is_carpool_open'])) {
                unset($this->_aInfos[$iIndex]);
            }
        }
    }


    /**
     * @param array $aEstimateData 品类数据
     * @return array
     */
    private function _setDefaultSelection(array $aEstimateData) {
        // 暂时按照 ProductCategory降序排列
        if (count($aEstimateData) > 1) {
            array_multisort(array_column($aEstimateData,'product_category'), SORT_DESC, $aEstimateData);
        }

        $bSetDefault = false;

        // 上次选中
        $aMultiRequireProduct = $this->_aProductList[0]->oCommonInfo->aMultiRequireProducts;
        foreach ($aEstimateData as &$aEstimateItem) {
            if (isset($aMultiRequireProduct[$aEstimateItem['product_category']])) {
                if (1 == $aMultiRequireProduct[$aEstimateItem['product_category']]['is_selected']) {
                    $aEstimateItem['select_type'] = 1;
                    $bSetDefault = true;
                    break;
                }
            }
        }

        // 兜底有特快默勾特快
        if (!$bSetDefault) {
            foreach ($aEstimateData as &$aEstimateItem) {
                if (ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTER_NEW == $aEstimateItem['product_category']) {
                    $aEstimateItem['select_type'] = 1;
                    $bSetDefault = true;
                    break;
                }
            }
        }

        // 没有特快默勾特价
        if (!$bSetDefault) {
            foreach ($aEstimateData as &$aEstimateItem) {
                if (ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTE == $aEstimateItem['product_category']) {
                    $aEstimateItem['select_type'] = 1;
                    $bSetDefault = true;
                    break;
                }
            }
        }

        // 没有特价勾客企
        if (!$bSetDefault) {
            foreach ($aEstimateData as &$aEstimateItem) {
                if (MainDataRepo::isBelongTripCloud($aEstimateItem['product_category'])) {
                    $aEstimateItem['select_type'] = 1;
                    break;
                }
            }
        }

        return $aEstimateData;
    }

    // 注释-待删
//    /**
//     * 最大座位数调整
//     * @param array $aEstimateData ...
//     * @return array
//     */
//    private function _updateSeatConfig(array $aEstimateData) {
//        $bSameMaxSeatNum = true;
//        $iMinSeatNum     = 999;
//        $sMinToast       = '';
//        foreach ($aEstimateData as $aEstimateItem) {
//            if (!empty($aEstimateItem['carpool_seat_module'])) {
//                $aSeat    = $aEstimateItem['carpool_seat_module'];
//                $iSeatNum = count($aSeat['seat_config']);
//                if (999 != $iMinSeatNum && $iMinSeatNum != $iSeatNum) {
//                    $bSameMaxSeatNum = false;
//                }
//
//                if ($iSeatNum < $iMinSeatNum) {
//                    $iMinSeatNum = $iSeatNum;
//                    $sMinToast   = $aSeat['seats_exceed_toast'] ?? '';
//                }
//            }
//        }
//
//        // 最大座位数都一样就直接返回
//        if ($bSameMaxSeatNum) {
//            return $aEstimateData;
//        }
//
//        // 修正最大座位数
//        foreach ($aEstimateData as &$aEstimateItem) {
//            if (!empty($aEstimateItem['carpool_seat_module'])) {
//                $aSeatConfig = $aEstimateItem['carpool_seat_module']['seat_config'] ?? [];
//                $iSeatNum    = count($aSeatConfig);
//                if ($iSeatNum > $iMinSeatNum) {
//                    $aEstimateItem['carpool_seat_module']['seat_config']        = array_slice($aSeatConfig,0,$iMinSeatNum);
//                    $aEstimateItem['carpool_seat_module']['seats_exceed_toast'] = $sMinToast;
//                    if ((int)$aEstimateData['carpool_seat_module']['select_index'] >= $iMinSeatNum) {
//                        $aEstimateItem['carpool_seat_module']['select_index'] = 0;
//                        $aEstimateItem['carpool_seat_module']['select_value'] = 1;
//                    }
//                }
//            }
//        }
//
//        return $aEstimateData;
//    }


    /**
     * 获取头图和链接
     * @return array
     */
    private function _getHeadPicAndLink() {
        $aConfig = ConfigLoader::getInstance()->getCommonConfig();
        $aInfo   = $this->_aInfos[0];

        // 兜底配置
        if (in_array($aInfo['common_info']['access_key_id'], [Common::DIDI_WECHAT_MINI_PROGRAM, Common::DIDI_ALIPAY_MINI_PROGRAM])) {
            $sBgUrl = $aConfig['page_head']['webapp_bg_url'] ?? '';
        } else {
            $sBgUrl = $aConfig['page_head']['native_bg_url'] ?? '';
        }

        $sDetailUrl = $aConfig['page_head']['detail_url'] ?? '';

        // 客企接入小程序需求：新城际页面改造头图配置维度，支持父路线维度配置
        // wiki: http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=749817050
        if (Util::isTripCloudV3($aInfo)) {
            // 支持父路线维度配置，由于头图不以品类维度配置，但是同一次预估可能会出多个品类
            // 注意：这里默认用第一个品类所命中的子路线对应的父路线id作为获取头图的参数
            $sRouteGroup  = '';
            $iComboId     = $aInfo['order_info']['combo_id']; // int格式
            $aMatchRoutes = $aInfo['order_info']['match_routes'];
            foreach ($aMatchRoutes as $aMatchRoute) {
                if (empty($aMatchRoute) || empty($aMatchRoute['route_id'])) {
                    continue;
                }

                if ($iComboId == $aMatchRoute['route_id'] && !empty($aMatchRoute['route_group'])) {
                    $sRouteGroup = $aMatchRoute['route_group'];
                    break;
                }
            }

            if (empty($sRouteGroup)) {
                return [$sBgUrl,$sDetailUrl];
            }

            // 获取头图阿波罗配置
            $oApollo       = new Apollo();
            $featureToggle = $oApollo->featureToggle(
                'g_intercity_head_conf',
                [
                    'key'           => $aInfo['passenger_info']['pid'],
                    'pid'           => $aInfo['passenger_info']['pid'],
                    'route_group'   => $sRouteGroup,
                    'app_version'   => $aInfo['common_info']['app_version'],
                    'access_key_id' => $aInfo['common_info']['access_key_id'],
                    'lang'          => $aInfo['common_info']['lang'],
                    'city'          => $aInfo['order_info']['area'],
                    'phone'         => $aInfo['passenger_info']['phone'],

                ]
            );
            $sHeadUrl = $featureToggle->getParameter('head_url', '');
            $sDetaUrl = $featureToggle->getParameter('detail_url', '');
            if (!empty($sHeadUrl)) {
                $sBgUrl = $sHeadUrl;
            }

            if (!empty($sDetaUrl)) {
                $sDetailUrl = $sDetaUrl;
            }
        }

        return [$sBgUrl,$sDetailUrl];
    }


    /**
     * 获取支付方式全集
     * @return array
     * @throws \Exception ...
     */
    private function _getUserPayInfo() {
        if (!Util::isSupportBusinessPay($this->_aInfos[0])) {
            return [];
        }

        $aAllPayments = [];
        $sSelectedPay = '0';
        $aSelectedPay = [];
        foreach ($this->_aInfos as $aInfo) {
            $aPayments = $aInfo['payments_info']['payment'] ?? [];
            foreach ($aPayments as $aPayment) {
                if (empty($aPayment) || empty($aPayment['channel_name'])) {
                    continue;
                }

                if (1 == $aPayment['is_selected']) {
                    $sSelectedPay = $aPayment['pay_type'];
                    if (!in_array($sSelectedPay,$aSelectedPay)) {
                        $aSelectedPay[] = $sSelectedPay;
                    }
                }

                $aPayment['is_selected'] = 0;
                $aAllPayments[$aPayment['pay_type']] = $aPayment;
            }
        }

        if (0 == count($aAllPayments)) {
            $sDefaultPayment = '{"pay_type":0,"business_const_set":0,"mixed_pay_type":0,"channel_name":"个人支付","is_selected":1,"need_separate":0,"disabled":0}';
            $aDefaultPayment = json_decode($sDefaultPayment, true);
            if (Language::isLangEnglish(Language::getCurrentLanguage())) {
                $aDefaultPayment['channel_name'] = 'Personal Pay';
            }

            $aAllPayments[$sSelectedPay] = $aDefaultPayment;
            $aSelectedPay[] = $sSelectedPay;
        }

        // 存在推荐默认选中
        if (1 == count($aSelectedPay)) {
            $aAllPayments[$sSelectedPay]['is_selected'] = 1;
        } else {
            // 用户有选择支付方式
            $aInfo = current($this->_aInfos);
            $sUserSelectedPayType = $aInfo['order_info']['payments_type'];
            if ((int)$sUserSelectedPayType > 0) {
                if (!empty($aAllPayments[$sUserSelectedPayType])) {
                    $aAllPayments[$sUserSelectedPayType]['is_selected'] = 1;
                }
            }
        }

        // 类型转换
        $aUserPayInfoItems = [];
        foreach ($aAllPayments as $aPayment) {
            $oUPI = new UserPayInfoItem();
            $oUPI->setTag($aPayment['pay_type'])->setBusinessConstSet($aPayment['business_const_set'])
                ->setIsSelected($aPayment['is_selected'])->setDisabled($aPayment['disabled'])
                ->setMsg($aPayment['channel_name']);
            $aUserPayInfoItems[$aPayment['pay_type']] = $oUPI->serializeToJsonArray();
        }

        // 排序 && 增加扩展信息
        $aUserPayInfoItems = MainHelper::formatPayInfoComponent($aUserPayInfoItems);
        $aPayText          = NuwaConfig::text('estimate_dache_anycar', 'user_pay_info');
        return [
            'title'        => $aPayText['title'],
            'sub_title'    => $aPayText['sub_title'],
            'payment_list' => $aUserPayInfoItems,
        ];
    }

    /**
     * @param array $aEstimateData 品类数据
     * @return string
     */
    private function _getForceNoticeToast(array $aEstimateData) {

        // 上次选中
        $aMultiRequireProduct = $this->_aProductList[0]->oCommonInfo->aMultiRequireProducts;
        // force_notice_toast 增加字段
        $iUserSeatNum = current($aMultiRequireProduct)['carpool_seat_num'];
        if (!empty($iUserSeatNum)) {
            $iSelectNum = $aEstimateData[0]['carpool_seat_module']['select_value'];
            if ($iSelectNum != $iUserSeatNum) {
                return Language::getTextFromDcmp('carpool_intercity-force_notice_toast_info');
            }
        }

        return '';
    }

    /**
     * 优化城际拼车首页头图&&详情
     * https://cooper.didichuxing.com/knowledge/2199455543792/2199894768901
     * @return array
     */
    private function _getBannerInfo(): array {
        // 由于头图不以品类维度配置，但是同一次预估可能会出多个品类，所以这里默认用第一项来出相关头图信息
        $aInfo = $this->_aInfos[0];

        // 需求wiki: http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=749817050
        if (!Util::isTripCloudV3($aInfo)) {
            return [false, '', ''];
        }

        $iRouteGroup  = 0;
        $aMatchRoutes = $aInfo['order_info']['match_routes'];
        // 因子路线可能存在多个，顾需要找到与父路线匹配的子路线，即combo_ids==route_id，取route_id的route_group值作为子路线
        foreach ($aMatchRoutes as $aMatchRoute) {
            if (empty($aMatchRoute) || empty($aMatchRoute['route_id'])) {
                continue;
            }

            if (!empty($aMatchRoute['route_group']) && $aInfo['order_info']['combo_id'] == $aMatchRoute['route_id']) {
                $iRouteGroup = $aMatchRoute['route_group'];
                break;
            }
        }


        // 判断是否有特定配置头图
        list($ok, $aCityConf) = Apollo::getInstance()->getConfigsByNamespaceAndConditions(self::INTER_CITY_CONF_BANNER_NS, ['group_id' => $iRouteGroup])->getAllConfigData();
        // 若所在城市无配置，走兜底全国配置
        if (!$ok || empty($aCityConf)) {
            list($ok, $aCityConf) = Apollo::getInstance()->getConfigsByNamespaceAndConditions(self::INTER_CITY_CONF_BANNER_NS, ['group_id' => 0])->getAllConfigData();
            if (!$ok || empty($aCityConf)) {
                return [false, '', ''];
            }
        }

        $aConfig = current($aCityConf);
        $sBanner = $aConfig['native_pic'];
        $sDetail = $aConfig['native_click_url'];

        // 小程序下发特定头图与链接
        if (in_array($aInfo['common_info']['access_key_id'],[Constants::DIDI_WECHAT_MINI_PROGRAM, Constants::DIDI_ALIPAY_MINI_PROGRAM])) {
            $sBanner = $aConfig['applets_pic'];
            $sDetail = $aConfig['applets_click_url'];
        }

        return [true, $sBanner, $sDetail];
    }

    /**
     * @return array
     */
    private function _getPluginPageInfo() {
        $aPluginPageInfo = [];

        $aInfo         = $this->_aInfos[0];
        $aBillInfo     = $aInfo['bill_info'] ?? [];
        $aDisplayLines = $aBillInfo['display_lines'] ?? [];
        $aDisplayLines = array_combine(array_column($aDisplayLines, 'name'), $aDisplayLines);
        $fRedPacket    = $aDisplayLines['red_packet']['value'] ?? 0.0;

        $aShowPluginPageInfo = SpringRedPacketFormatter::isRedPacketH5Show($fRedPacket, $aInfo);
        if (!$aShowPluginPageInfo) {
            return $aPluginPageInfo;
        }

        $aRedPacketParams = [
            'red_packet_amount' => $fRedPacket,
            'is_intercept_page' => 1,
            'is_passenger'      => 1,
            'flng'              => $aInfo['order_info']['from_lng'] ?? null,
            'flat'              => $aInfo['order_info']['from_lat'] ?? null,
        ];

        $aPluginPageInfo['type'] = 3;
        $sUrl = Language::getTextFromDcmp('config_text-red_packet_bubble_intercept_page');
        $aPluginPageInfo['show_h5'] = UtilHelper::httpAppendQuery($sUrl, $aRedPacketParams);
        return $aPluginPageInfo;
    }
}


