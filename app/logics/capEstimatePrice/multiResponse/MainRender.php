<?php
//
//namespace PreSale\Logics\capEstimatePrice\multiResponse;
//
//use BizLib\Config as NuwaConfig;
//use BizLib\Utils\Common as UtilsCommon;
//use BizLib\Utils\Language;
//use BizLib\Utils\MapHelper;
//use BizLib\Utils\ProductCategory;
//use Xiaoju\Apollo\Apollo;
//use BizLib\Utils\UtilHelper;
//use Dirpc\SDK\PreSale\CapEstimateData;
//use Disf\SPL\Trace;
//use PreSale\Logics\capEstimatePrice\multiRequest\CommonInfo;
//use PreSale\Logics\capEstimatePrice\multiResponse\component\SortSelection;
//use PreSale\Logics\estimatePrice\multiResponse as dacheAnyCarResponse;
//use PreSale\Logics\estimatePriceV2\response\SceneResponseLogicV2;
//use PreSale\Logics\capEstimatePrice\multiResponse\MainDataRepo;
//
///**
// *
// * Copyraight (c) 2021 xiaojukeji.com, Inc. All Rights Reserved.
// * @author: wangtuan<PERSON><EMAIL>
// * @date: 2021/4/12 11:57 上午
// * @desc: 预估渲染
// * @wiki:
// *
// */
//class MainRender
//{
//    /**
//     * @var
//     */
//    protected static $_oInstance;
//
//    /**
//     * @var
//     */
//    protected $_aInfos;
//
//    /**
//     * @var array 6.0构建的产品列表
//     */
//    protected $_aProductList = [];
//
//    /**
//     * MainRender constructor.
//     * @param array $aInfos 品类信息
//     */
//    private function __construct($aInfos) {
//        MainDataRepo::init($aInfos);
//        $this->_aInfos     = $aInfos;
//        $oneInfo           = current($aInfos);
//        $this->_iFromStyle = MainHelper::getFormStyleByAb(reset($aInfos));
//    }
//
//    /**
//     * @param array $aInfos 品类信息
//     * @return MainRender
//     */
//    public static function getInstance($aInfos) {
//        if (empty(self::$_oInstance)) {
//            self::$_oInstance = new self($aInfos);
//        }
//
//        return self::$_oInstance;
//    }
//
//    /**
//     * @param array $aProductList $aProductList
//     * @return void
//     */
//    public function setProductList($aProductList) {
//        $this->_aProductList = $aProductList;
//    }
//
//    /**
//     * buildAInfo
//     *
//     * @return void
//     */
//    public function buildAInfo() {
//        $aEID2Product = [];
//        foreach ($this->_aProductList as $oProduct) {
//            $aEID2Product[$oProduct->oOrderInfo->sEstimateID] = $oProduct;
//        }
//
//        foreach ($this->_aInfos as &$aInfo) {
//            $sEID = $aInfo['bill_info']['estimate_id'];
//            if (empty($aEID2Product[$sEID])) {
//                continue;
//            }
//
//            $aInfo['product_object'] = $aEID2Product[$sEID];
//        }
//    }
//
//    /**
//     * @return array|bool|mixed
//     */
//    public function multiExecute() {
//        $aMultiResponse = UtilsCommon::pairErrNo(GLOBAL_SUCCESS);
//        //渲染前置数据处理和准备工作
//        $this->preRender();
//        //主渲染逻辑，产生各品类的预估数据
//        $aMultiResponse['data']['estimate_data'] = $this->buildEstimateData();
//        //渲染品类数据之外的全局数据
//        return $this->buildExternalData($aMultiResponse);
//    }
//
//    /**
//     *@return void
//     */
//    public function preRender() {
//        // 批量获取拼车预匹配数据（愿等、ETD、ETS等）
//        $aPinchecheInfos = MainDataRepo::getPinchengleCarpoolInfos();
//        if (!empty($aPinchecheInfos)) {
//            SceneResponseLogicV2::getInstance()->loadInfos($aPinchecheInfos)->loadCarpoolPreMatchInfo();
//        }
//    }
//
//    /**
//     * array $aMultiResponse 批量返回信息
//     * @return array
//     */
//    public function buildEstimateData() {
//        $aComponents = [
//            // [intro_msg] [category_id]
//            component\introMsg\Handler::class,
//            // [fee_msg] [fee_amount]
//            component\basicFeeMsg\Handler::class,
//            // [extra_map] [estimate_id] [user_pay_info]
//            component\productInfo\Handler::class,
//            // [fee_desc_list]
//            component\feeDescList\Handler::class,
//            // [multi_price_list]
//            component\multiPriceList\Handler::class,
//            // [seat_num]
//            component\carpool\Handler::class,
//            // [extra_price_desc]
//            component\priceDescExpand\Handler::class,
//
//        ];
//
//        $aEstimateData = [];
//        foreach ($this->_aInfos as $aInfo) {
//            $aResponse = [];
//            foreach ($aComponents as $handler) {
//                $oComponent = $handler::select($aInfo);
//                if (!empty($oComponent)) {
//                    $aResponse = $oComponent->build($aResponse);
//                }
//            }
//
//            $aEstimateData[] = $aResponse;
//        }
//
//        // [bubble_tip]
//        $aEstimateData = $this->_addCarpoolPageTip($aEstimateData);
//
//        // [select_type]
//        $aEstimateData = SortSelection::setDefaultSelection($this->_aInfos, $aEstimateData);
//
//        // 组合车型策略
//        $aEstimateData = $this->_assembleGroupData($this->_iFromStyle, $aEstimateData);
//
//        // [sort]
//        $aEstimateData = SortSelection::sortForSPPage($this->_iFromStyle, $this->_aInfos, $aEstimateData);
//
//        return $aEstimateData;
//    }
//
//    /**
//     * @param array $aMultiResponse 批量返回数据
//     * @return array
//     */
//    public function buildExternalData(array $aMultiResponse) {
//        // [estimate_trace_id]
//        $aMultiResponse['data']['estimate_trace_id'] = Trace::traceId();
//        // [fee_detail_url]
//        $aMultiResponse['data']['fee_detail_url'] = UtilHelper::getConfigUrl('fee_detail_h5_new');
//        $aMultiResponse['data']['is_support_multi_selection'] = 1;
//        // [category_info_list]
//        $aMultiResponse['data']['category_info_list'] = $this->_getCategoryInfoList();
//        // [seat_prefix_desc]
//        $aMultiResponse['data']['seat_prefix_desc'] = $this->_getSeatPrefixDesc();
//        // [user_pay_info]
//        $aMultiResponse['data']['user_pay_info'] = $this->_getUserPayInfo();
//        // [form_style]
//        $aMultiResponse['data']['form_style'] = $this->_iFromStyle;
//        return $aMultiResponse;
//    }
//    /**
//     * 获取user_pay_info
//     * @return array
//     */
//    private function _getUserPayInfo() {
//        //所有车型的默认选中支付方式
//        $aAllDefaultPayType = [];
//        //所有车型的所有支付方式
//        $aAllPaymentInfo = [];
//        //用户选择的支付方式
//        $iUserSelectPayType = $this->_aInfos[0]['order_info']['payments_type'];
//        foreach ($this->_aInfos as $aInfo) {
//            $aPaymentList    = $aInfo['payments_info']['user_pay_info']['busi_payments'];
//            $iDefaultPayType = MainHelper::getDefaultPayType($aInfo);
//            if (0 != $iDefaultPayType) {
//                $aAllDefaultPayType[$iDefaultPayType] = 1;
//            }
//
//            if (empty($aPaymentList)) {
//                continue;
//            }
//
//            foreach ($aPaymentList as $aPayment) {
//                $iPayType = $aPayment['tag'];
//
//                if (empty($aAllPaymentInfo[$iPayType])) {
//                    $aAllPaymentInfo[$iPayType] = [
//                        'tag'        => $iPayType,
//                        'msg'        => $aPayment['msg'],
//                        'disabled'   => 0,
//                        'isSelected' => 0, //先设置为0，后面根据用户选择，或车型合并后的信息设置
//                    ];
//                }
//            }
//        }
//
//        if (0 != $iUserSelectPayType) {
//            if (isset($aAllPaymentInfo[$iUserSelectPayType])) {
//                $aAllPaymentInfo[$iUserSelectPayType]['isSelected'] = 1;
//            }
//        } elseif (1 == count($aAllDefaultPayType)) {
//            $iDefaultPayType = array_keys($aAllDefaultPayType)[0];
//            if (isset($aAllPaymentInfo[$iDefaultPayType])) {
//                $aAllPaymentInfo[$iDefaultPayType]['isSelected'] = 1;
//            }
//        } else {
//        }
//
//        // 兜底支付方式逻辑
//        if (empty($aAllPaymentInfo)) {
//            $aAllPaymentInfo[2] = [
//                'tag'        => 2,
//                'msg'        => '个人支付',
//                'disabled'   => 0,
//                'isSelected' => 1,
//            ];
//        }
//
//        //排序payment_list
//        $aPaymentList   = array_values($aAllPaymentInfo);
//        $aPaymentConfig = $this->_getPayTypeSortConf();
//        usort(
//            $aPaymentList,
//            function ($aOne, $aTwo) use ($aPaymentConfig) {
//                return $aPaymentConfig[$aOne['pay_type']] - $aPaymentConfig[$aTwo['pay_type']];
//            }
//        );
//
//        //拼装文案
//        $aPayText = NuwaConfig::text('estimate_dache_anycar', 'user_pay_info');
//        return [
//            'title'        => $aPayText['title'],
//            'sub_title'    => $aPayText['sub_title'],
//            'payment_list' => $aPaymentList,
//        ];
//    }
//
//
//
//    /**
//     * 获取categoryInfoList
//     * @return array
//     */
//    private function _getCategoryInfoList() {
//        $aCategoryInfoText = NuwaConfig::text('config_cap_estimate_price', 'category_info');
//        $aCategoryInfoList = [];
//        // 注意：这里顺序决策端上的排序
//        foreach ($aCategoryInfoText['category_info_list'] as $aItem) {
//            $aCategoryInfoList[] = [
//                'category_id'      => (int)$aItem['category_id'],
//                'seat_num'         => (int)$aItem['seat_num'],
//                'seat_desc'        => $aItem['seat_num_desc'],
//                'seat_prefix_desc' => $aItem['seat_prefix_desc'], // 小程序发版后废弃
//            ];
//        }
//
//        // 构建disable_display：端上是否渲染该品类
//        foreach ($aCategoryInfoList as &$aItem) {
//            $aItem['enable_display'] = 1;
//            if ((MainDataRepo::hasCarpool() || MainDataRepo::hasTwoPriceCarpool())) {
//                continue;
//            }
//
//            if (0 != $aItem['seat_num']) {
//                $aItem['enable_display'] = 0;
//            }
//        }
//
//        return $aCategoryInfoList;
//    }
//
//    /**
//     * 获取categoryInfoList
//     * @return array
//     */
//    private function _getSeatPrefixDesc() {
//        $aCategoryInfoText = NuwaConfig::text('config_cap_estimate_price', 'category_info');
//        return $aCategoryInfoText['seat_prefix_desc'];
//    }
//
//    /**
//     * @param int   $formStyle     表单样式
//     * @param array $aEstimateData 品类数据
//     * @return array
//     */
//    private function _assembleGroupData(int $iFormStyle, array $aEstimateData) {
//        // 不拼座车型样式获取
//        // 司乘一口价或两口价拼车 非拼车下聚合 快、出、特惠
//        return $this->_groupCarpoolForCarpoolPage($iFormStyle, $aEstimateData);
//    }
//
//
//    /**
//     * @param int   $formStyle     表单样式标识
//     * @param array $aEstimateData 品类数据
//     * @return array
//     */
//    private function _groupCarpoolForCarpoolPage(int $iFormStyle, array $aEstimateData) {
//        $aNewEstimateData  = [];
//        $aCarpoolGroupData = [];
//        $iCategoryID       = 0;
//        $iTaxiPcID         = 9999;
//        $iSelectType       = $iCarPoolSelectType = $iNoneCarPoolSelectType   = 0;
//        $aTaxiEstimateData = [];
//        $sExtraPriceDesc   = '';
//        foreach ($aEstimateData as $aItem) {
//            if (isset($aItem['product_category']) && in_array(
//                $aItem['product_category'],
//                [
//                    ProductCategory::PRODUCT_CATEGORY_CARPOOL_FLAT_RATE_BY_SEAT,
//                    ProductCategory::PRODUCT_CATEGORY_CARPOOL_STATION,
//                ]
//            )
//            ) {
//                if (1 == $aItem['select_type']) {
//                    $iCarPoolSelectType = 1;
//                }
//
//                $aNewEstimateData[] = $aItem;
//            } else {
//                if (1 == $aItem['select_type']) {
//                    $iNoneCarPoolSelectType = 1;
//                }
//
//                // 分类信息挪到外层
//                $iCategoryID         = $aItem['category_id'] ?? 0;
//                $sExtraPriceDesc     = $aItem['extra_price_desc'];
//                $aCarpoolGroupData[] = $aItem;
//            }
//        }
//
//        // 存在不拼座车型默认勾选 && 没有拼车默认勾选 => 外层默认勾选不拼座
//        if (0 == $iCarPoolSelectType && 1 == $iNoneCarPoolSelectType) {
//            $iSelectType = 1;
//        }
//
//        $aText = NuwaConfig::text('config_cap_estimate_price', 'intro_msg');
//        $aCategoryInfoText = NuwaConfig::text('config_cap_estimate_price', 'category_info');
//        if (1 == $iFormStyle || 3 == $iFormStyle) {
//            // 增加聚合车型配置
//            $oCapEstimateData = new CapEstimateData();
//
//            $sIntroMsg = $aText['product_name_opt']['0_0'] ?? '';
//            $oCapEstimateData->setIntroMsg($sIntroMsg);
//            $oCapEstimateData->setCategoryId($iCategoryID);
//            $oCapEstimateData->setSubProduct($aCarpoolGroupData);
//            $oCapEstimateData->setSelectType($iSelectType);
//            $oCapEstimateData->setExtraPriceDesc($sExtraPriceDesc);
//            $aNewEstimateData[] = $oCapEstimateData->serializeToJsonArray();
//            return $aNewEstimateData;
//        } elseif (2 == $iFormStyle) {
//            $aTaxiEstimateData            = [];
//            $iNoneCarPoolSelectTypeNoTaxi = 0;
//            $aMultiGroupData        = [];
//            $aMultiCarpoolGroupData = [];
//            $aCapEstimateData       = [];
//            $aCapEstimateData['category_id'] = $iCategoryID;
//            $aCapEstimateData['select_type'] = $iSelectType;
//            foreach ($aCarpoolGroupData as $aItem) {
//                if (isset($aItem['product_category']) && in_array(
//                    $aItem['product_category'],
//                    [
//                        ProductCategory::PRODUCT_CATEGORY_UNIONE,
//                        ProductCategory::PRODUCT_CATEGORY_UNIONE_SPECIAL_PRICE,
//                        ProductCategory::PRODUCT_CATEGORY_TAXI_MARKETISATION_PUTONG,
//                    ]
//                ) && ($iTaxiPcID < $aItem['product_category'] || 9999 == $iTaxiPcID)
//                ) {
//                    $iTaxiPcID         = $aItem['product_category'];
//                    $aTaxiEstimateData = $aItem;
//                }
//            }
//
//            foreach ($aCarpoolGroupData as $aItem) {
//                if (isset($aItem['product_category']) && $aItem['product_category'] == $iTaxiPcID) {
//                    continue;
//                }
//
//                $aMultiCarpoolGroupData[] = $aItem;
//                if (1 == $aItem['select_type']) {
//                    $iNoneCarPoolSelectTypeNoTaxi = 1;
//                }
//            }
//
//            $aMultiGroupData[] = [
//                'select_type' => $iNoneCarPoolSelectTypeNoTaxi,
//                'sub_product' => $aMultiCarpoolGroupData,
//                'intro_msg'   => $aText['product_name_opt']['0_0_0'] ?? '',
//                'category_id' => $aCategoryInfoText['category_id_style_2']['0'],
//            ];
//            if (!empty($aTaxiEstimateData)) {
//                $aTaxiEstimateData['category_id'] = $aCategoryInfoText['category_id_style_2'][$aTaxiEstimateData['product_category']];
//                $aMultiGroupData[] = $aTaxiEstimateData;
//            }
//
//            $aCapEstimateData['multi_product'] = $aMultiGroupData;
//            $aNewEstimateData[] = $aCapEstimateData;
//            return $aNewEstimateData;
//        }
//
//    }
//
//
//    /**
//     * @param array $aEstimateData ...
//     * @return array
//     */
//    private function _addCarpoolPageTip(array $aEstimateData) {
//
//        $idxSeatOne      = -1;
//        $sSeatOnePrice   = 0;
//        $sMinNormalPrice = -1;
//        foreach ($aEstimateData as $idx => $aItem) {
//            if (isset($aItem['seat_num']) && 1 == $aItem['seat_num']) {
//                $idxSeatOne    = $idx;
//                $sSeatOnePrice = $aItem['fee_amount'];
//                // 企业支付不显示bubble_tip
//                if (MainHelper::isBusinessPay($this->_aInfos[0])) {
//                    return $aEstimateData;
//                }
//            }
//
//            if (isset($aItem['product_category'])
//                && !in_array(
//                    $aItem['product_category'],
//                    [
//                        ProductCategory::PRODUCT_CATEGORY_CARPOOL_FLAT_RATE_BY_SEAT,
//                        ProductCategory::PRODUCT_CATEGORY_UNIONE,
//                        ProductCategory::PRODUCT_CATEGORY_CARPOOL_STATION,
//                    ]
//                )
//            ) {
//                if (-1 == $sMinNormalPrice || (float)$aItem['fee_amount'] < (float)$sMinNormalPrice) {
//                    $sMinNormalPrice = $aItem['fee_amount'];
//                }
//            }
//        }
//
//        if (-1 != $idxSeatOne && (float)$sMinNormalPrice > (float)$sSeatOnePrice) {
//            $aText      = NuwaConfig::text('config_cap_estimate_price', 'fee_info');
//            $sBubbleTip = $aText['fee_bubble_tip'] ?? '';
//            $aEstimateData[$idxSeatOne]['bubble_tip'] = Language::replaceTag($sBubbleTip,['fee' => $sSeatOnePrice]);
//        }
//
//        return $aEstimateData;
//    }
//
//    /**
//     * 获取支付组件中展示的支付方式信息
//     * @return mixed
//     */
//    private  function _getPayTypeSortConf() {
//        $sLang       = $this->_oBizCommonInfo->oCommonInfo->sLang ?? Language::ZH_CN;
//        $oConfResult = Apollo::getInstance()->getConfigResult(
//            'payment_type_component',
//            'payment_type_list_info' . "_$sLang"
//        );
//        list($bOk, $aPaymentList) = $oConfResult->getConfig('payment_type_list');
//        if (!$bOk) {
//            return false;
//        }
//
//        $aSortConf = [];
//        foreach ($aPaymentList as $aConf) {
//            $aSortConf[$aConf['payment_type']] = (int)$aConf['sort_score'];
//        }
//
//        return $aSortConf;
//    }
//}
