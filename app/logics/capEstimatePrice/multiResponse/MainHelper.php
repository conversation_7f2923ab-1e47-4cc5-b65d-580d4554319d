<?php
//namespace PreSale\Logics\capEstimatePrice\multiResponse;
//
//use BizLib\Constants\OrderSystem;
//use BizLib\Utils\Horae;
//use Dukang\PropertyConst\Order\OrderCountPriceType;
//use PreSale\Logics\capEstimatePrice\multiRequest\CommonInfo;
//use Xiaoju\Apollo\Apollo;
//use BizCommon\Models\Order\Order;
//
///**
// *
// * Copyraight (c) 2021 xiaojukeji.com, Inc. All Rights Reserved.
// * @author: <EMAIL>
// * @date: 2021/4/12 3:18 下午
// * @desc:
// * @wiki:
// *
// */
//
//class MainHelper
//{
//    /**
//     * @param array  $aInfo   信息
//     * @param string $sSagTag 人群标签
//     * @return array
//     */
//    public  static function hitCrowdSupportTest(array $aInfo, string $sSagTag) {
//        if (empty($aInfo)) {
//            return [];
//        }
//
//        $oToggle = Apollo::getInstance()->featureToggle(
//            'ykj_pinzuo_fuchi',
//            [
//                'key'         => $aInfo['passenger_info']['pid'],
//                'phone'       => $aInfo['passenger_info']['phone'],
//                'city'        => $aInfo['order_info']['area'],
//                'app_version' => $aInfo['common_info']['app_version'],
//                'tag'         => $sSagTag,
//            ]
//        );
//        if (!$oToggle->allow() || $oToggle->allow() && 'control_group' == $oToggle->getGroupName()) {
//            return [];
//        } else {
//            $aParams = array();
//            $aParams['is_supp_carpool'] = $oToggle->getParameter('is_supp_carpool', 0);
//            $aParams['noncarpool_pc']   = $oToggle->getParameter('noncarpool_pc', '');
//            return $aParams;
//        }
//    }
//
//    /**
//     * @param array $aInfo aInfo
//     * @return bool
//     */
//    public static function isCarpool(array $aInfo) {
//        if (Horae::isCarpool($aInfo['order_info']['combo_type'], $aInfo['order_info']['require_level'])) {
//            return true;
//        }
//
//        return false;
//    }
//
//    // 注释-待删
////    /**
////     * 通过时间区间，城市放量 返回是否命中
////     * 需求 wiki：http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=723908987
////     * 灰度：http://ab.intra.xiaojukeji.com/launch/1047/normal/130927
////     * @param array $aInfo ...
////     * @return bool
////     */
////    public static function isHitCapFirstOneSeatOpt($aInfo) {
////        if (empty($aInfo)) {
////            return false;
////        }
////
////        $bRet = Apollo::getInstance()->featureToggle(
////            'gs_cap_first_one_seat_opt',
////            [
////                'key'         => $aInfo['passenger_info']['pid'],
////                'phone'       => $aInfo['passenger_info']['phone'],
////                'city'        => $aInfo['order_info']['area'],
////                'app_version' => $aInfo['common_info']['app_version'],
////            ]
////        )->allow();
////        return $bRet;
////    }
//
//    // 注释-待删
////    /**
////     * 通过时间区间，城市放量 该灰度返回参数：expiration_time redis key 过期时间，单位为月
////     * 需求 wiki：http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=723908987
////     * 灰度：http://ab.intra.xiaojukeji.com/launch/1047/normal/130927
////     * @param array $aInfo ...
////     * @return int
////     */
////    public static function getHitCapFirstOneSeatOptConfigMonthCount($aInfo) {
////        if (empty($aInfo)) {
////            return -1;
////        }
////
////        $oApollo = Apollo::getInstance()->featureToggle(
////            'gs_cap_first_one_seat_opt',
////            [
////                'key'         => $aInfo['passenger_info']['pid'],
////                'phone'       => $aInfo['passenger_info']['phone'],
////                'city'        => $aInfo['order_info']['area'],
////                'app_version' => $aInfo['common_info']['app_version'],
////            ]
////        );
////        if (!$oApollo->allow()) {
////            return -1;
////        }
////
////        return $oApollo->getParameter('expiration_time', 3);
////    }
//
//    /**
//     * 通过时间区间，城市放量 返回是否命中出拼成乐导流位
//     * 需求 wiki：http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=789298581
//     * 灰度：http://ab.intra.xiaojukeji.com/launch/1047/normal/146559
//     * @param array $aInfo ...
//     * @return bool
//     */
//    public static function isHitCapPinchengleOpencity($aInfo) {
//        if (empty($aInfo)) {
//            return false;
//        }
//
//        $bRet = Apollo::getInstance()->featureToggle(
//            'gs_cap_pinchengle_opencity',
//            [
//                'key'           => $aInfo['passenger_info']['pid'],
//                'phone'         => $aInfo['passenger_info']['phone'],
//                'city'          => $aInfo['order_info']['area'],
//                'app_version'   => $aInfo['common_info']['app_version'],
//                'access_key_id' => $aInfo['common_info']['access_key_id'],
//            ]
//        )->allow();
//        return $bRet;
//    }
//
//    /**
//     * 判断默认支付方式是否企业支付
//     * @param array $aInfo aInfo
//     * @return bool
//     */
//    public static function isBusinessPay(array $aInfo) {
//        $aPayInfoList = $aInfo['payments_info']['user_pay_info']['busi_payments'] ?: null;
//        if (empty($aPayInfoList)) {
//            return false;
//        }
//
//        foreach ($aPayInfoList as $aPayInfo) {
//            // 获取默认选中的支付方式，如果非企业支付直接返回
//            if (1 == $aPayInfo['isSelected']) {
//                if (Order::BUSINESS_PAY_BY_BUSINESS_BALANCE == $aPayInfo['tag']) {
//                    return true;
//                }
//            }
//        }
//
//        return false;
//    }
//
//    /**
//     * 返回默认支付方式
//     * @param array $aInfo aInfo
//     * @return int
//     */
//    public static function getDefaultPayType(array $aInfo) {
//        $aPayInfoList = $aInfo['payments_info']['user_pay_info']['busi_payments'] ?: null;
//        if (empty($aPayInfoList)) {
//            return 0;
//        }
//
//        foreach ($aPayInfoList as $aPayInfo) {
//            // 获取默认选中的支付方式，如果非企业支付直接返回
//            if (1 == $aPayInfo['isSelected']) {
//                    return $aPayInfo['tag'];
//            }
//        }
//
//        return 0;
//    }
//
//    /**
//     * @param array $aInfo $aInfo
//     * @return int
//     */
//    public static function getFormStyleByAb(array $aInfo) {
//        $oToggle = Apollo::getInstance()->featureToggle(
//            'ykj_pinzuo_v3_1',
//            [
//                'key'         => $aInfo['passenger_info']['uid'],
//                'phone'       => $aInfo['passenger_info']['phone'],
//                'city'        => $aInfo['order_info']['area'],
//                'app_version' => $aInfo['common_info']['app_version'],
//            ]
//        );
//        if (!$oToggle->allow()) {
//            return 1;
//        } else {
//            return $oToggle->getParameter('form_style', 1);
//        }
//    }
//
//    /**
//     * @param integer $iProductId product_id
//     * @param integer $iCountPriceType count_price_type
//     * @return bool
//     */
//    public static function isCapFast($iProductId, $iCountPriceType) {
//        return OrderSystem::PRODUCT_ID_FAST_CAR == $iProductId
//            && OrderCountPriceType::CountPriceTypeCapFast == $iCountPriceType;
//    }
//}
//
