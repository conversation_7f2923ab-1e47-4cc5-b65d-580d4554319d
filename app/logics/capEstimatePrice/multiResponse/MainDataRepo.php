<?php
//namespace PreSale\Logics\capEstimatePrice\multiResponse;
//
//use BizLib\Constants\OrderSystem;
//use BizLib\Utils\CarLevel;
//use BizLib\Utils\Language;
//use BizLib\Utils\Product;
//use BizLib\Utils\ProductCategory;
//
///**
// *
// * Copyraight (c) 2021 xiaojukeji.com, Inc. All Rights Reserved.
// * @author: <EMAIL>
// * @date: 2021/4/13 1:41 下午
// * @desc:
// * @wiki:
// *
// */
//class MainDataRepo
//{
//    const LANGUAGE = 'lang';
//
//    private static $_aInfos;
//
//    /**
//     * @param array $aInfos $aInfos
//     * @return void
//     */
//    public static function init($aInfos) {
//        self::$_aInfos = $aInfos;
//    }
//
//    /**
//     * 获取快车券前价格
//     * @return int
//     */
//    public static function getFastCarFee() {
//        list($aFastCarAInfo, $sCarLevel) = self::getFastCarAInfo();
//        if (empty($aFastCarAInfo)) {
//            return 0;
//        }
//
//        return $aFastCarAInfo['bill_info']['bills'][$sCarLevel]['dynamic_total_fee'];
//    }
//
//
//    /**
//     * 获取快车信息
//     * @return array
//     */
//    public static function getFastCarAInfo(): array {
//        foreach (self::$_aInfos as $aInfo) {
//            $sCarLevel = $aInfo['order_info']['require_level'];
//
//            //优先使用product_category 识别普通快车
//            $iProductCategory = $aInfo['order_info']['product_category'];
//            if (!empty($iProductCategory)) {
//                if (ProductCategory::PRODUCT_CATEGORY_FAST == $iProductCategory) {
//                    return [$aInfo, $sCarLevel];
//                }
//            } else {
//                //若未找到则用三元组匹配
//                if (Product::isFastcar($aInfo['order_info']['product_id'])
//                    && CarLevel::DIDI_PUTONG_CAR_LEVEL == $sCarLevel
//                    && OrderSystem::TYPE_COMBO_DEFAULT == $aInfo['bill_info']['product_infos'][$sCarLevel]['combo_type']
//                ) {
//                    return [$aInfo, $sCarLevel];
//                }
//            }
//        }
//
//        return [];
//    }
//
//
//
//    /**
//     * 获取快车券后价格
//     * @return float
//     */
//    public static function getFastCarAfterDiscountFee(): float {
//        list($aFastCarAInfo,) = self::getFastCarAInfo();
//        if (empty($aFastCarAInfo)) {
//            return 0;
//        }
//
//        return $aFastCarAInfo['activity_info'][0]['estimate_fee'];
//    }
//
//    /**
//     * 是否只有快车
//     * @return bool
//     */
//    public static function isOnlyHasFastCar() {
//        return 1 == count(self::$_aInfos);
//    }
//
//
//    /**
//     * 是否有司乘一口价拼车
//     * @return bool
//     */
//    public static function hasCarpool() {
//        $bFlag = false;
//        foreach (self::$_aInfos as $aInfo) {
//            if (ProductCategory::PRODUCT_CATEGORY_CARPOOL_FLAT_RATE_BY_SEAT == $aInfo['order_info']['product_category']) {
//                $bFlag = true;
//            }
//        }
//
//        return $bFlag;
//    }
//
//    /**
//     * 是否有两口价v3
//     * @return bool
//     */
//    public static function hasTwoPriceCarpool() {
//        $bFlag = false;
//        foreach (self::$_aInfos as $aInfo) {
//            if (ProductCategory::PRODUCT_CATEGORY_CARPOOL_STATION == $aInfo['order_info']['product_category']) {
//                $bFlag = true;
//            }
//        }
//
//        return $bFlag;
//    }
//
//    /**
//     * 是否有A+
//     * @return bool
//     */
//    public static function hasAPlus() {
//        $bFlag = false;
//        foreach (self::$_aInfos as $aInfo) {
//            if (ProductCategory::PRODUCT_CATEGORY_APLUS == $aInfo['order_info']['product_category']) {
//                $bFlag = true;
//            }
//        }
//
//        return $bFlag;
//    }
//
//    /**
//     * 是否有拼成乐
//     * @return bool
//     */
//    public static function hasPinchengleCarpool() {
//        $bFlag = false;
//        foreach (self::$_aInfos as $aInfo) {
//            if (ProductCategory::PRODUCT_CATEGORY_LOW_PRICE_CARPOOL == $aInfo['order_info']['product_category']) {
//                $bFlag = true;
//            }
//        }
//
//        return $bFlag;
//    }
//
//    /**
//     * 返回只有拼成乐infos
//     * @return array
//     */
//    public static function getPinchengleCarpoolInfos() {
//        $aInfosOnlyPincheche = [];
//        foreach (self::$_aInfos as $aInfo) {
//            if (ProductCategory::PRODUCT_CATEGORY_LOW_PRICE_CARPOOL == $aInfo['order_info']['product_category']) {
//                $aInfosOnlyPincheche[] = $aInfo;
//            }
//        }
//
//        return $aInfosOnlyPincheche;
//    }
//}
