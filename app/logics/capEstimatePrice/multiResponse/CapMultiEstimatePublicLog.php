<?php
//
//namespace PreSale\Logics\capEstimatePrice\multiResponse;
//
//use Disf\SPL\Trace;
//use BizLib\Utils\PublicLog;
//use Dirpc\SDK\PreSale\CapMultiEstimatePriceRequest as Request;
//use PreSale\Logics\capEstimatePrice\multiRequest\Product;
//use Dirpc\SDK\PreSale\CapEstimateResponse as Response;
//use Dirpc\SDK\PreSale\CapEstimateData as EstimateData;
//
///**
// * 预估public日志组建
// * derived from app/logics/estimatePrice/multiResponse/MultiEstimatePublicLogV2.php
// */
//class CapMultiEstimatePublicLog
//{
//    /**
//     * @var array
//     */
//    private $_aInfos;
//
//    /**
//     * @var Response
//     */
//    private $_aResponseInfo;
//
//    /**
//     * @var Request
//     */
//    private $_oRequest;
//
//    /**
//     * @var Product[]
//     */
//    private $_aProductList;
//
//    /**
//     * CapMultiEstimatePublicLog constructor.
//     *
//     * @param Request   $oRequest      预估入参
//     * @param array     $aInfos        $aInfos
//     * @param Response  $aResponseInfo $aResponseInfo
//     * @param Product[] $aProductList  $aProductList
//     */
//    public function __construct($oRequest, $aInfos, $aResponseInfo, $aProductList) {
//        $this->_oRequest      = $oRequest;
//        $this->_aInfos        = $aInfos;
//        $this->_aResponseInfo = $aResponseInfo;
//        $this->_aProductList  = $aProductList;
//    }
//
//    /**
//     * 写多预估流程的public 日志.
//     * @return void
//     */
//    public function multiWritePublicLog() {
//        $this->_writeOrderEstimatePrice();
//    }
//
//    /**
//     * 写预估public日志
//     * @return void
//     */
//    private function _writeOrderEstimatePrice() {
//        foreach ($this->_aInfos as $aInfo) {
//            if (empty($aInfo['bill_info']['estimate_id'])) {
//                continue;
//            }
//
//            $oCurProduct = null;
//            $iEstimateID = $aInfo['order_info']['estimate_id'];
//            //  @var Product $oProduct
//            foreach ($this->_aProductList as $oProduct) {
//                if ($iEstimateID == $oProduct->oOrderInfo->sEstimateID) {
//                    $oCurProduct = $oProduct;
//                    break;
//                }
//            }
//
//            $this->_writePublicLog($aInfo, $oCurProduct, $this->_aResponseInfo);
//        }
//    }
//
//    /**
//     * @param array    $aInfo          $aInfo
//     * @param Product  $oProduct       $oProduct
//     * @param Response $aResponseInfos $aResponseInfos
//     * @return void
//     */
//    private function _writePublicLog($aInfo, $oProduct, $aResponseInfos) {
//        $aEstimateData = $this->_getEstimateData($aInfo, $aResponseInfos);
//
//        $sTabList = $this->_oRequest->getTabList();
//        // 是否是默认拼新样式表单识别
//        $bRecCarpool = false;
//        if (!empty($sTabList)) {
//            $aTabList = json_decode($sTabList, true);
//            if (!empty($aTabList)) {
//                foreach ($aTabList as $tab) {
//                    if (!empty($tab) && 'rec_carpool' == $tab['tab_id']) {
//                        $bRecCarpool = true;
//                    }
//                }
//            }
//        }
//
//        $aEstimateStatistic = [
//            // 标记
//            'opera_stat_key'    => 'g_order_cap_multi_estimate_price',
//            'estimate_trace_id' => Trace::traceId(),
//            'estimate_id'       => $aInfo['order_info']['estimate_id'],
//
//            // 端信息
//            'imei'              => $oProduct->oCommonInfo->sImei,
//            'app_version'       => $oProduct->oCommonInfo->sAppVersion,
//            'client_type'       => $oProduct->oCommonInfo->iClientType,
//            'access_key_id'     => $oProduct->oCommonInfo->iAccessKeyID,
//            'channel'           => $oProduct->oCommonInfo->sChannel,
//            'lang'              => $oProduct->oCommonInfo->sLang,
//            'menu_id'           => $oProduct->oOrderInfo->sMenuID,
//            'page_type'         => $oProduct->oOrderInfo->iPageType,
//
//            // 用户信息
//            'pid'               => $oProduct->oPassengerInfo->iPid,
//            // 地理信息
//            'area'              => $oProduct->oAreaInfo->iArea,
//            'to_area'           => $oProduct->oAreaInfo->iToArea,
//            'district'          => $oProduct->oAreaInfo->iDistrict,
//            'flng'              => $oProduct->oAreaInfo->fFromLng,
//            'flat'              => $oProduct->oAreaInfo->fFromLat,
//            'tlng'              => $oProduct->oAreaInfo->fToLng,
//            'tlat'              => $oProduct->oAreaInfo->fToLat,
//            'current_lng'       => $oProduct->oAreaInfo->fCurLng,
//            'current_lat'       => $oProduct->oAreaInfo->fCurLat,
//            'county'            => $oProduct->oAreaInfo->iFromCounty,
//            'to_county'         => $oProduct->oAreaInfo->iToCounty,
//            'from_name'         => str_replace(PHP_EOL, '', $oProduct->oAreaInfo->sFromName),
//            'to_name'           => str_replace(PHP_EOL, '', $oProduct->oAreaInfo->sToName),
//
//            'stopover_points'   => $oProduct->oCommonInfo->sStopoverPoints,
//
//            // 产品信息
//            // product_category 能包含的不再重复
//            // 各品类子场景的描述字段, 有需要的列出
//            'product_category'  => $oProduct->oOrderInfo->iProductCategory,
//            'seat_num'          => $oProduct->oOrderInfo->iCarpoolSeatNum, // 乘客座位数
//            'product_id'        => $oProduct->oOrderInfo->iProductId,
//            'combo_type'        => $oProduct->oOrderInfo->iComboType,
//            'carpool_type'      => $oProduct->oOrderInfo->iCarpoolType,
//            'require_level'     => $oProduct->oOrderInfo->iRequireLevel,
//
//            // 产品 - 显示
//            'select_type'       => $aEstimateData['select_type'] ?? 0,
//
//            'style_type'        => $bRecCarpool ? 1 : 0,
//
//            // 产品 - 账单信息 - 非钱
//            // 涉及到钱的部分 可通过 estimate_id 查询报价单public-log
//            // 账单原生信息如需public记录, 在price-api操作
//        ];
//
//        PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);
//    }
//
//    /**
//     * @param array    $aInfo          aInfo
//     * @param Response $aResponseInfos 预估结果集
//     *
//     * @return EstimateData | array
//     */
//    private function _getEstimateData($aInfo, $aResponseInfos) {
//        $iEstimateId = $aInfo['order_info']['estimate_id'];
//        if (!empty($aResponseInfos['data']['estimate_data'])) {
//            foreach ($aResponseInfos['data']['estimate_data'] as $aData) {
//                if ($iEstimateId == $aData['estimate_id']) {
//                    return $aData;
//                }
//            }
//        }
//
//        return [];
//    }
//}
