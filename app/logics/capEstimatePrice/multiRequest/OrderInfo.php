<?php
//namespace PreSale\Logics\capEstimatePrice\multiRequest;
//
//use BizLib\Utils\Horae;
//use BizLib\Utils\UtilHelper;
//use Dirpc\SDK\PreSale\CapMultiEstimatePriceRequest as Request;
//
///**
// *
// * Copyraight (c) 2021 xiaojukeji.com, Inc. All Rights Reserved.
// * @author: <EMAIL>
// * @date: 2021/4/6 9:49 下午
// * @desc: 订单相关信息
// * @wiki:
// *
// */
//class OrderInfo
//{
//    //拼成乐/城际拼车等 展示在导流位
//    const FORM_SHOW_TYPE_GUIDE = 1;
//
//    /**
//     * @var $sEstimateID string
//     */
//    public $sEstimateID;
//    /**
//     * @var $iOrderType integer 订单类型
//     */
//    public $iOrderType = 0;
//    //顶导字符串ID
//    public $sMenuID;
//    //二级页面ID
//    public $iPageType;
//    // 用户选择的拼车拼座信息, N元组的一个字段，用于后序计算价各下流识别
//    public $iCarpoolSeatNum;
//    // 用户选择的座位灵敏
//    public $iSelectCarpoolSeatNum;
//
//    //品类ID
//    public $iProductCategory;
//    //订单信息
//    public $iProductId;
//    public $iBusinessId;
//    public $iRequireLevel;
//    public $iComboType;
//    public $iCarpoolType;
//    public $iIsSpecialPrice;
//    public $iLevelType;
//    public $iCarpoolPriceType;
//    public $bIsDualCarpoolPrice;
//
//    // 过滤信息
//    public $bRemoveFlag = false;
//
//    // 支付方式
//    public $iPaymentsType = 0;
//    //出发时间
//    public $iDepartureTime;
//
//    //站点信息
//    public $aStationList;
//
//    /**
//     * OrderInfo constructor.
//     * @param Request $oEstimateRequest $oEstimateRequest
//     * @return void
//     */
//    public function __construct(Request $oEstimateRequest) {
//        //构建基本信息
//        $this->sMenuID        = $oEstimateRequest->getMenuId();
//        $this->iPaymentsType  = $oEstimateRequest->getPaymentsType();
//        $this->iPaymentsType  = empty($this->iPaymentsType) ? 0 : $this->iPaymentsType;
//        $this->iDepartureTime = $oEstimateRequest->getDepartureTime();
//        $this->iDepartureTime = empty($this->iDepartureTime) ? time() : $this->iDepartureTime;
//    }
//
//    /**
//     * @param array      $aBaseOrderInfo orderInfo
//     * @param CommonInfo $oCommonInfo    oCommonInfo
//     * @return void
//     */
//    public function fillUpOrderInfo($aBaseOrderInfo, CommonInfo $oCommonInfo) {
//        $this->iPageType        = $oCommonInfo->iPageType;
//        $this->iProductCategory = $aBaseOrderInfo['product_category'];
//        $this->iProductId       = $aBaseOrderInfo['product_id'];
//        $this->iBusinessId      = $aBaseOrderInfo['business_id'];
//        $this->iRequireLevel    = $aBaseOrderInfo['require_level'];
//        $this->iComboType       = $aBaseOrderInfo['combo_type'];
//        $this->iCarpoolType     = $aBaseOrderInfo['carpool_type'];
//        $this->iIsSpecialPrice  = $aBaseOrderInfo['is_special_price'];
//        $this->iLevelType       = $aBaseOrderInfo['level_type'];
//
//        $this->sEstimateID         = UtilHelper::getEstimateId($this->iProductId, $this->iRequireLevel, $this->iComboType, $this->iProductCategory);
//        $this->iCarpoolPriceType   = $aBaseOrderInfo['carpool_price_type'];
//        $this->bIsDualCarpoolPrice = $aBaseOrderInfo['is_dual_carpool_price'];
//        if (Horae::isCarpool($this->iComboType, $this->iRequireLevel)) {
//            if (!empty($aBaseOrderInfo['seat_num'])) {
//                $this->iCarpoolSeatNum = $aBaseOrderInfo['seat_num'];
//            } else {
//                $this->iCarpoolSeatNum = (int)$oCommonInfo->aMultiRequireProducts[(int)$this->iProductCategory]['carpool_seat_num'] ?: 1;
//            }
//        }
//
//        $this->bRemoveFlag  = $aBaseOrderInfo['remove_flag'];
//        $this->aStationList = $aBaseOrderInfo['order_info']['station_list'];
//    }
//
//    /**
//     * 将OrderInfo转换成数组格式
//     * @return array
//     */
//    public function toArray() {
//        $aObjectVars = get_object_vars($this);
//        $aOrderInfo  = array();
//        foreach ($aObjectVars as $key => $value) {
//            $key   = substr($key, 1);
//            $field = $this->_uncamelize($key);
//            $aOrderInfo[$field] = $value;
//        }
//
//        return $aOrderInfo;
//    }
//
//    /**
//     * @param string $camelCaps 驼峰字符串
//     * @param string $separator 分割符
//     * @return string
//     */
//    private function _uncamelize($camelCaps, $separator = '_') {
//        return strtolower(preg_replace('/([a-z])([A-Z])/', '$1' . $separator . '$2', $camelCaps));
//    }
//}
