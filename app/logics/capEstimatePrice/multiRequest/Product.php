<?php
//namespace PreSale\Logics\capEstimatePrice\multiRequest;
//
//use Dirpc\SDK\PreSale\CapMultiEstimatePriceRequest as Request;
//
///**
// *
// * Copyraight (c) 2021 xiaojukeji.com, Inc. All Rights Reserved.
// * @author: wangtuan<PERSON><EMAIL>
// * @date: 2021/4/6 10:49 下午
// * @desc: product
// * @wiki:
// *
// */
//
//class Product
//{
//    private $_oRequest;
//
//    /**
//     * @var OrderInfo OrderInfo
//     */
//    public $oOrderInfo;
//
//    /**
//     * @var CommonInfo CommonInfo
//     */
//    public $oCommonInfo;
//
//    /**
//     * @var PassengerInfo PassengerInfo
//     */
//    public $oPassengerInfo;
//
//    /**
//     * @var AreaInfo AreaInfo
//     */
//    public $oAreaInfo;
//
//    /**
//     * Product constructor.
//     * @param Request $oRequest request
//     * @return void
//     */
//    public function __construct(Request $oRequest) {
//        $this->_oRequest = $oRequest;
//    }
//
//    /**
//     * @param array $aBaseProduct dds返回的基础oneconf数据及order_extra数据
//     * @return void
//     */
//    public function buildOrderInfo($aBaseProduct) {
//        $oOrderInfo = new OrderInfo($this->_oRequest);
//        $oOrderInfo->fillUpOrderInfo($aBaseProduct, $this->oCommonInfo);
//        $this->oOrderInfo = $oOrderInfo;
//    }
//
//
//    /**
//     * @param  array $aBasePassengerInfo 基础用户信息
//     * @return void
//     */
//    public function buildPassengerInfo($aBasePassengerInfo) {
//        $oPassenger = new PassengerInfo($this->_oRequest, $aBasePassengerInfo);
//        $this->oPassengerInfo = $oPassenger;
//    }
//
//    /**
//     * @param CommonInfo $oCommonInfo CommonInfo公用
//     * @return void
//     */
//    public function buildCommonInfo(CommonInfo $oCommonInfo) {
//        $this->oCommonInfo = $oCommonInfo;
//    }
//
//    /**
//     * @param AreaInfo $oAreaInfo AreaInfo公用
//     * @return void
//     */
//    public function buildAreaInfo(AreaInfo $oAreaInfo) {
//        $this->oAreaInfo = $oAreaInfo;
//    }
//}
