<?php
//
//namespace PreSale\Logics\capEstimatePrice\multiRequest;
//
//use Dirpc\SDK\PreSale\CapMultiEstimatePriceRequest as Request;
//use BizLib\Utils\Common;
//
///**
// *
// * Copyraight (c) 2021 xiaojukeji.com, Inc. All Rights Reserved.
// * @author: wangtuan<PERSON><EMAIL>
// * @date: 2021/4/6 10:06 下午
// * @desc: common info
// * @wiki:
// *
// */
//
//class CommonInfo
//{
//    // 区域渗透拼车
//    const PAGE_TYPE_REGION_CARPOOL = 17;
//
//    // 滴滴特价版
//    const PAGE_TYPE_SPECIAL_RATE = 19;
//
//    //端版本
//    public $sAppVersion;
//    //端类型client_type
//    public $iClientType;
//    //端来源
//    public $iAccessKeyID;
//    //端语言
//    public $sLang;
//    //渠道号
//    public $sChannel;
//    //来源id
//    public $iOriginId;
//    //终端id
//    public $iTerminalId;
//    //设备号
//    public $sImei;
//    //b2b来源
//    public $bIsFromB2b;
//    //webApp来源
//    public $bIsFromWebApp;
//    //来源
//    public $sFrom;
//    //暂时还有用到
//    public $iPlatformType;
//    //用户主动勾选的产品
//    public $sPreferenceProduct;
//    // page_type
//    public $iPageType = 0;
//
//    //用户前后切换，二次预估 上传用户选中产品，及需要分品类配置的
//    public $aMultiRequireProducts = [];
//
//    // 途经点信息
//    public $sStopoverPoints = '';
//
//    /**
//     * CommonInfo constructor.
//     * @param Request $oEstimateRequest oRequest
//     */
//    public function __construct(Request $oEstimateRequest) {
//        $this->sAppVersion   = $oEstimateRequest->getAppVersion();
//        $this->sLang         = $oEstimateRequest->getLang();
//        $this->sChannel      = $oEstimateRequest->getChannel();
//        $this->iClientType   = $oEstimateRequest->getClientType();
//        $this->iAccessKeyID  = $oEstimateRequest->getAccessKeyId();
//        $this->iOriginId     = $oEstimateRequest->getOriginId();
//        $this->iTerminalId   = $oEstimateRequest->getTerminalId();
//        $this->iPlatformType = $oEstimateRequest->getPlatformType();
//        $this->bIsFromB2b    = Common::fromB2B($this->iClientType);
//        $this->bIsFromWebApp = Common::fromWebapp($this->iClientType);
//        $this->sFrom         = $oEstimateRequest->getFrom();
//        $this->iPageType     = $oEstimateRequest->getPageType() ? $oEstimateRequest->getPageType() : self::PAGE_TYPE_REGION_CARPOOL;
//        $this->_buildMultiRequireProducts($oEstimateRequest);
//        $this->sStopoverPoints = $oEstimateRequest->getStopoverPoints();
//    }
//
//    /**
//     * multiRequireProduct 是端用户上传分品类的参数的字段；以json形式上传，反序列化后为一个列表，其中product_category 用于标识品类
//     * 此方法用于反序列化multiRequireProduct，并构建map 方便内部参数构建的索引
//     *
//     * 区域渗透一口价场景，拼车可以选择一座、两座，但这两个品类product_category字段是一样的
//     * @param Request $oEstimateRequest $oEstimateRequest
//     * @return void
//     */
//    private function _buildMultiRequireProducts(Request $oEstimateRequest) {
//        $sMultiStr   = $oEstimateRequest->getMultiRequireProduct();
//        $aMultiInfo  = json_decode($sMultiStr, true);
//        $aPreference = [];
//        if (!empty($aMultiInfo)) {
//            foreach ($aMultiInfo as $aReq) {
//                if (!empty($aReq['product_category']) && 1 == (int)$aReq['is_selected']) {
//                    $aPreference[] = [
//                        'product_category' => (int)$aReq['product_category'],
//                        'carpool_seat_num' => (int)$aReq['carpool_seat_num'],
//                    ];
//                }
//
//                $this->aMultiRequireProducts[$aReq['product_category']] = $aReq;
//            }
//        }
//
//        $this->sPreferenceProduct = json_encode($aPreference);
//    }
//}
