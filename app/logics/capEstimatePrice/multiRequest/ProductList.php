<?php
//namespace PreSale\Logics\capEstimatePrice\multiRequest;
//
//use BizCommon\Constants\OrderNTuple;
//use BizCommon\Models\Passenger\Passenger;
//use BizLib\Client\EstimateDecisionClient;
//use BizLib\Config;
//use BizLib\Exception\ExceptionWithResp;
//use BizLib\Utils\MapHelper;
//use BizLib\Utils\ProductCategory;
//use Dirpc\SDK\EstimateDecision\CommonInfoV2;
//use Dirpc\SDK\EstimateDecision\ProductsReq;
//use Dirpc\SDK\EstimateDecision\UserInfoV2;
//use Dirpc\SDK\PreSale\CapMultiEstimatePriceRequest as Request;
//use BizLib\ErrCode\Code;
//use BizLib\ErrCode\RespCode;
//use BizLib\Constants\OrderSystem;
//use PreSale\Logics\capEstimatePrice\multiResponse\MainHelper;
//use Xiaoju\Apollo\Apollo;
//
///**
// *
// * Copyraight (c) 2021 xiaojukeji.com, Inc. All Rights Reserved.
// * @author: <EMAIL>
// * @date: 2021/4/6 9:44 下午
// * @desc: 产品信息
// * @wiki:
// *
// */
//class ProductList
//{
//    public $aProductList = [];
//
//    protected static $_oInstance = null;
//
//    private $_oRequest = null;
//
//    private $_aBaseProductList = [];
//
//    private $_aAreaInfo = [];
//
//    private $_aBasePassengerInfo = [];
//
//    /**
//     * ProductList constructor.
//     * @param Request $oRequest $oRequest
//     * @throws ExceptionWithResp 起点地址获取异常逻辑
//     */
//    public function __construct(Request $oRequest) {
//        $this->_oRequest           = $oRequest;
//        $this->_aBasePassengerInfo = Passenger::getInstance()->getPassengerByTokenFromPassport($oRequest->getToken());
//        $this->_aBasePassengerInfo['token'] = $oRequest->getToken();
//        $this->_buildAreaInfo($oRequest);
//    }
//
//    /**
//     * @param Request $oRequest $oRequest
//     * @return null|ProductList
//     * @throws ExceptionWithResp e
//     */
//    public static function getInstance($oRequest) {
//        if (!self::$_oInstance instanceof self) {
//            self::$_oInstance = new self($oRequest);
//        }
//
//        return self::$_oInstance;
//    }
//
//    /**
//     * @param Request $oRequest 接口入参request
//     * @throws ExceptionWithResp e
//     * @return void
//     */
//    private function _buildAreaInfo(Request $oRequest) {
//        $aFromArea = MapHelper::getAreaInfoByLoc($oRequest->getFromLng(), $oRequest->getFromLat());
//        if (!empty($aFromArea)) {
//            $this->_aAreaInfo['from_area'] = $aFromArea;
//        }
//
//        // 起点城市获取失败，默认为0，走降级
//        if (empty($this->_aAreaInfo['from_area']['id'])) {
//            throw new ExceptionWithResp(
//                Code::E_COMMON_HTTP_READ_FAIL,
//                RespCode::R_ESTIMATE_DOWNSTREAM_FAIL_DEGRADE,
//                '',
//                ['from_lng' => $oRequest->getFromLng(), 'from_lat' => $oRequest->getFromLat(), 'ret' => $aFromArea]
//            );
//        }
//
//        $aToArea = MapHelper::getAreaInfoByLoc($oRequest->getToLng(), $oRequest->getToLat());
//        if (!empty($aToArea)) {
//            $this->_aAreaInfo['to_area'] = $aToArea;
//        }
//    }
//
//
//    /**
//     * @throws ExceptionWithResp exceptionWithResp
//     * @return array
//     */
////    public function buildProductList() {
////        $oAreaInfo   = new AreaInfo($this->_oRequest, $this->_aAreaInfo);
////        $oCommonInfo = new CommonInfo($this->_oRequest);
////        //请求dds/products 接口
////        $this->_getDDSProducts($oCommonInfo, $oAreaInfo);
////        // 请求会员系统不做
////        // 由于会员的数据目前使用到的只有溢价保护，针对动调场景，本次只有拼车和快车，先不加
////        //根据products接口返回的多条产品构建基础req数据
////        foreach ($this->_aBaseProductList as $aBaseProduct) {
////            if ($aBaseProduct['remove_flag']) {
////                continue;
////            }
////
////            // 表示拼车区域一口价没有开，也需要构建到产品列表中，这里是为了后续渲染的需要
////            // 在进行价格地，要把remove_flag为true的remove掉
////            $oProduct = new Product($this->_oRequest);
////            $oProduct->buildCommonInfo($oCommonInfo);
////            $oProduct->buildOrderInfo($aBaseProduct);
////            $oProduct->buildAreaInfo($oAreaInfo);
////            $oProduct->buildPassengerInfo($this->_aBasePassengerInfo);
////            $this->aProductList[] = $oProduct;
////        }
////
////        // 配置list为空，按未开城处理，未开城异常不能放在 _getDDSProducts 方法中抛出
////        if (empty($this->aProductList)) {
////            $sErrMsg = Config::text('errno', 'dache_anycar_no_products_error');
////            throw new ExceptionWithResp(
////                Code::E_COMMON_AREA_NOT_OPEN_SERVICE,
////                RespCode::P_ERRNO_NOT_OPEN_SERVICE,
////                $sErrMsg
////            );
////        }
////
////        return $this->aProductList;
////    }
//
//    /**
//     * @param CommonInfo $oCommonInfo oCommonInfo
//     * @param AreaInfo   $oAreaInfo   oAreaInfo
//     * @throws ExceptionWithResp resp
//     * @return void
//     */
//    private function _getDDSProducts(CommonInfo $oCommonInfo, AreaInfo $oAreaInfo) {
//        $aProductsReq = new ProductsReq();
//        $aUserInfo    = new UserInfoV2();
//        $aUserInfo->setPhone($this->_aBasePassengerInfo['phone']);
//        $aUserInfo->setPid($this->_aBasePassengerInfo['pid']);
//        $aUserInfo->setUid($this->_aBasePassengerInfo['uid']);
//        $aProductsReq->setUserInfo($aUserInfo);
//
//        $aCommonInfoV2 = new CommonInfoV2();
//        $aCommonInfoV2->setAppVersion($this->_oRequest->getAppVersion());
//        $aCommonInfoV2->setAccessKeyId($this->_oRequest->getAccessKeyId());
//        $aCommonInfoV2->setLang($this->_oRequest->getLang());
//        $aCommonInfoV2->setChannel($this->_oRequest->getChannel());
//        $aCommonInfoV2->setClientType($this->_oRequest->getClientType());
//        $aCommonInfoV2->setStartLat($this->_oRequest->getFromLat());
//        $aCommonInfoV2->setStartLng($this->_oRequest->getFromLng());
//        $aCommonInfoV2->setDestLat($this->_oRequest->getToLat());
//        $aCommonInfoV2->setDestLng($this->_oRequest->getToLng());
//        $aCommonInfoV2->setCity($this->_aAreaInfo['from_area']['id'] ?? 0);
//        $aCommonInfoV2->setToCity($this->_aAreaInfo['to_area']['id'] ?? 0);
//        $aCommonInfoV2->setMenuId($this->_oRequest->getMenuId());
//        $aCommonInfoV2->setPageType($oCommonInfo->iPageType);
//        $aCommonInfoV2->setDepartureTime($this->_oRequest->getDepartureTime() ?: time());
//        $aCommonInfoV2->setCounty($oAreaInfo->iFromCounty);
//        $aCommonInfoV2->setToCounty($oAreaInfo->iToCounty);
//        $aCommonInfoV2->setDistrict($oAreaInfo->iDistrict);
//        $aCommonInfoV2->setStopoverPoints($oCommonInfo->sStopoverPoints);
//        $aProductsReq->setCommonInfo($aCommonInfoV2);
//
//        $oClient = new EstimateDecisionClient();
//        $aResult = $oClient->getProducts($aProductsReq);
//
//        if (0 != $aResult['errno'] || 200 != $aResult['code'] || 0 != $aResult['result']['errno']
//            || empty($aResult['result']['data']) || empty($aResult['result']['data']['product_list'])
//        ) {
//            if (P_ERRNO_STOPOVER_POINTS_CONFLICTS_WITH_SCENES == $aResult['result']['errno']) {
//                $sMsg = Config::text('errno', 'stopover_points_unsupport_scene_msg_6_0');
//                throw new ExceptionWithResp(
//                    Code::E_COMMON_AREA_NOT_OPEN_SERVICE,
//                    RespCode::P_AIRPORT_STATION_NOT_SUPPORT_STOPOVER_POINTS_ERROR,
//                    $sMsg,
//                    ['district' => $aCommonInfoV2->getDistrict()]
//                );
//            }
//
//            // 服务异常，走降级
//            throw new ExceptionWithResp(
//                Code::E_DDS_GET_PRODUCTS_FAIL,
//                RespCode::R_ESTIMATE_DOWNSTREAM_FAIL_DEGRADE,
//                '',
//                ['dds_products_response' => $aResult]
//            );
//        }
//
//        $aBaseProductList = $aResult['result']['data']['product_list'];
//
//        // 新增 灰度开关 gs_cap_new_opencity_customization 优先过该灰度，没结果走原有逻辑
//        list($bAllowV2, $aPCList) = $this->getHitCapFirstOneSeatOptConfigMonthCount($aProductsReq);
//        if ($bAllowV2) {
//            $aBaseProductList = $this->_capMultiReduce($aBaseProductList, $aPCList);
//        } else {
//            // 兜底逻辑 上线后观察没问题 下次改动就一起下掉了 不需要迭代
//            // 两口价导流拼成乐复用司乘一口价表单需求：http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=789298581
//            // 需要复用司乘一口价小程序page_type 相关页面，所以原来 PAGE_TYPE_REGION_CARPOOL 还是比较干净的
//            // 但是拼成乐和两口价现在也要开城在这个页面，所以原有的品类过滤需要更细粒度，只保留固定品类，只能逻辑都写死
//            // 改动点：https://cooper.didichuxing.com/docs/document/2199462574521
//            // 品类放量控制
//            $aBaseProductList = $this->_capMultiEstOptTraffic($aBaseProductList,$aProductsReq);
//            // 两口价与司乘一口价互斥逻辑 司乘一口价优先级高
//            if ($this->_existProductCategory(
//                $aBaseProductList,
//                ProductCategory::PRODUCT_CATEGORY_CARPOOL_FLAT_RATE_BY_SEAT
//            )
//            ) {
//                $aBaseProductList = $this->_capMultiCarpoolFlatRateBySeatReduce($aBaseProductList,$aProductsReq);
//            } elseif ($this->_existProductCategory(
//                $aBaseProductList,
//                ProductCategory::PRODUCT_CATEGORY_CARPOOL_STATION
//            )
//            ) {
//                // 两口价and导流拼车乐
//                $aBaseProductList = $this->_capMultiTwoPriceReduce($aBaseProductList,$aProductsReq);
//                if ($this->_existProductCategory(
//                    $aBaseProductList,
//                    ProductCategory::PRODUCT_CATEGORY_LOW_PRICE_CARPOOL
//                ) && !$this->_isHitCapPinchengleOpencity($aBaseProductList,$aProductsReq)
//                ) {
//                    $aBaseProductList = $this->_capMultiPinCheCheReduce($aBaseProductList);
//                }
//            } else {
//                // 删除拼车乐，以防bad case
//                $aBaseProductList = $this->_capMultiPinCheCheReduce($aBaseProductList);
//            }
//        }
//
//        $this->_productFission($aBaseProductList);
//        $this->_removeProduct();
//    }
//
//    /**
//     * 品类裂变逻辑，两口价v3，司乘一口价做一座、两座；拼车乐导流位一座
//     * @param array $aProductList productList
//     * @return void
//     */
//    private function _productFission(array $aProductList) {
//        foreach ($aProductList as $aValue) {
//            $iSeatNum = 1;
//            $aValue   = (array)$aValue;
//            // 一座拼成乐
//            if (OrderNTuple::CARPOOL_TYPE_LOW_PRICE == $aValue['carpool_type']) {
//                $aValue['seat_num']        = $iSeatNum++;
//                $this->_aBaseProductList[] = $aValue;
//            } elseif (in_array(
//                $aValue['carpool_type'],
//                [OrderNTuple::CARPOOL_TYPE_FLAT_RATE, OrderNTuple::CARPOOL_TYPE_STATION]
//            )
//            ) {
//                $aValue['seat_num']        = $iSeatNum++;
//                $this->_aBaseProductList[] = $aValue;
//
//                $aValue['seat_num']        = $iSeatNum++;
//                $this->_aBaseProductList[] = $aValue;
//            } else {
//                $this->_aBaseProductList[] = $aValue;
//            }
//        }
//
//        return;
//    }
//
//    /**
//     * @param array       $aProductList 产品线列表
//     * @param ProductsReq $oReq         ...
//     * @return array
//     */
//    private function _capMultiEstOptTraffic(array $aProductList, ProductsReq $oReq) {
//        foreach ($aProductList as $aProduct) {
//            if (in_array(
//                $aProduct['product_category'],
//                [
//                    ProductCategory::PRODUCT_CATEGORY_CARPOOL_FLAT_RATE_BY_SEAT,
//                    ProductCategory::PRODUCT_CATEGORY_FAST,
//                    ProductCategory::PRODUCT_CATEGORY_FAST_SPECIAL_RATE,
//                    ProductCategory::PRODUCT_CATEGORY_UNIONE,
//                    ProductCategory::PRODUCT_CATEGORY_CARPOOL_STATION,
//                    ProductCategory::PRODUCT_CATEGORY_LOW_PRICE_CARPOOL,
//                ]
//            )
//            ) {
//                $aResProducts[] = $aProduct;
//            }
//        }
//
//            return $aResProducts;
//    }
//
//    /**
//     * 过滤remove_flag为true 的产品
//     * @return void
//     */
//    private function _removeProduct() {
//        foreach ($this->_aBaseProductList as $index => $aProduct) {
//            if ($aProduct['remove_flag']) {
//                unset($this->_aBaseProductList[$index]);
//            }
//        }
//
//        $this->_aBaseProductList = array_values($this->_aBaseProductList);
//    }
//
//    /**
//     * 是否存在某个品类
//     * @param array $aProductList     产品线列表
//     * @param int   $iProductCategory 品类ID
//     * @return bool
//     */
//    private function _existProductCategory(array $aProductList, int $iProductCategory) {
//        foreach ($aProductList as $aProduct) {
//            if ($iProductCategory == $aProduct['product_category']) {
//                return true;
//            }
//        }
//    }
//
//    /**
//     * 保留司乘一口价，整理品类列表
//     * @param array $aProductList 产品线列表
//     * @return array
//     */
//    private function _capMultiCarpoolFlatRateBySeatReduce(array $aProductList) {
//        // 保留:1(快车),61(特惠快车),7(出租车)，69（司乘一口价），删除(3)两口价,(46)拼成乐
//        foreach ($aProductList as $index => $aProduct) {
//            if (in_array(
//                $aProduct['product_category'],
//                [
//                    ProductCategory::PRODUCT_CATEGORY_LOW_PRICE_CARPOOL,
//                    ProductCategory::PRODUCT_CATEGORY_CARPOOL_STATION,
//                ]
//            )
//            ) {
//                unset($aProductList[$index]);
//            }
//        }
//
//        return array_values($aProductList);
//    }
//
//    /**
//     * 拼车两口价v3情况下，整理品类列表
//     * @param array $aProductList 产品线列表
//     * @return array
//     */
//    private function _capMultiTwoPriceReduce(array $aProductList) {
//        // 保留:46(拼成乐), 1(快车),61(特惠快车),7(出租车)，3(两口价)，删除司乘一口价（69）
//        foreach ($aProductList as $index => $aProduct) {
//            if (ProductCategory::PRODUCT_CATEGORY_CARPOOL_FLAT_RATE_BY_SEAT == $aProduct['product_category']) {
//                unset($aProductList[$index]);
//            }
//        }
//
//        return array_values($aProductList);
//    }
//
//    /**
//     * 当没有两口价v3情况下，删除拼成乐，兜底配置不严谨
//     * @param array $aProductList 产品线列表
//     * @return array
//     */
//    private function _capMultiPinCheCheReduce(array $aProductList) {
//        foreach ($aProductList as $index => $aProduct) {
//            if (ProductCategory::PRODUCT_CATEGORY_LOW_PRICE_CARPOOL == $aProduct['product_category']) {
//                unset($aProductList[$index]);
//            }
//        }
//
//        return array_values($aProductList);
//    }
//
//    /**
//     * @param array       $aProductList 产品线列表
//     * @param ProductsReq $oReq         ...
//     * @return bool
//     */
//    private function _isHitCapPinchengleOpencity(array $aProductList, ProductsReq $oReq) {
//        $aParams = [
//            'common_info'    => [
//                'app_version' => $oReq->getCommonInfo()['app_version'],
//            ],
//            'order_info'     => [
//                'area' => $oReq->getCommonInfo()['city'],
//            ],
//            'passenger_info' => [
//                'pid'   => $oReq->getUserInfo()['pid'],
//                'phone' => $oReq->getUserInfo()['phone'],
//            ],
//        ];
//        return MainHelper::isHitCapPinchengleOpencity($aParams);
//    }
//
//    /**
//     * @param ProductsReq $oReq         ...
//     * @return mixed
//     */
//    private function getHitCapFirstOneSeatOptConfigMonthCount(ProductsReq $oReq) {
//        $oApollo = Apollo::getInstance()->featureToggle(
//            'gs_cap_new_opencity_customization',
//            [
//                'key'           => $oReq->getUserInfo()['pid'],
//                'phone'         => $oReq->getUserInfo()['phone'],
//                'city'          => $oReq->getCommonInfo()['city'],
//                'app_version'   => $oReq->getCommonInfo()['app_version'],
//                'access_key_id' => $oReq->getCommonInfo()['access_key_id'],
//                'county_id'     => $oReq->getCommonInfo()['county'],
//
//            ]
//        );
//        if (!$oApollo->allow()) {
//            return [false, null];
//        }
//
//        $icarpoolPC   = $oApollo->getParameter('carpool_pc', 3);
//        $sNoCarpoolPC = $oApollo->getParameter('nocarpool_pc', '[1]');
//        $aNoCarpoolPC = json_decode($sNoCarpoolPC, true);
//        if (empty($aNoCarpoolPC)) {
//            $aNoCarpoolPC = array(1);
//        }
//
//        $aNoCarpoolPC[] = $icarpoolPC;
//        return [true, $aNoCarpoolPC];
//    }
//
//    /**
//     * 通过入参 $aAllowPCList 整理品类列表
//     * @param array $aProductList 产品线列表
//     * @param array $aAllowPCList 保留的品类ID数组
//     * @return array
//     */
//    private function _capMultiReduce(array $aProductList, array $aAllowPCList) {
//        foreach ($aProductList as $index => $aProduct) {
//            if (!in_array($aProduct['product_category'],$aAllowPCList)) {
//                unset($aProductList[$index]);
//            }
//
//            if (ProductCategory::PRODUCT_CATEGORY_LOW_PRICE_CARPOOL == $aProduct['product_category']) {
//                unset($aProductList[$index]);
//            }
//        }
//
//        return array_values($aProductList);
//    }
//}
