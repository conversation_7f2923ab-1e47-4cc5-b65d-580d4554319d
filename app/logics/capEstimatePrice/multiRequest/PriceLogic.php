<?php
//
//namespace PreSale\Logics\capEstimatePrice\multiRequest;
//
//use BizCommon\Logics\Order\FieldOrderNTuple;
//use BizLib\Client\PriceApiClient;
//use BizLib\Exception\ExceptionWithResp;
//use BizCommon\Models\Order\OrderStopoverPoints;
//use BizCommon\Constants\OrderNTuple;
//use BizLib\ErrCode\Code;
//use BizLib\ErrCode\RespCode;
//use BizLib\Utils\Product as UtilProduct;
//use BizLib\Constants;
//
///**
// *
// * Copyraight (c) 2021 xiaojukeji.com, Inc. All Rights Reserved.
// * @author: <EMAIL>
// * @date: 2021/4/8 9:45 下午
// * @desc:
// * @wiki:
// *
// */
//class PriceLogic
//{
//    /**
//     * 支付类型.
//     */
//    const PAY_TYPE_NEW = 2;
//    const PAY_TYPE_BR  = 1;
//
//    public $iIsExtraStop    = 0;
//    public $aStopoverPoints = [];
//    /**
//     * @param array $aProductList aProductList
//     * @return array
//     * @throws ExceptionWithResp resp
//     */
//    public function getMultiResponse(array $aProductList) {
//        //构建price-api需要的参数
//        $aPriceParams = $this->_getMultiPriceParams($aProductList);
//        $aPriceResult = $this->_getMultiPriceByClient($aPriceParams);
//        return $this->_buildResponseAndRequest($aPriceParams, $aPriceResult, $aProductList);
//    }
//
//    /**
//     * @param array $aProductList aProductList
//     * @return array
//     */
//    private function _getMultiPriceParams(array $aProductList) {
//        $aPriceParams = array();
//        foreach ($aProductList as $iIndex => $oProduct) {
//            // 由于品类remove，不应该请求各个下游去计算
//            // 拼车被remove掉了
//            if ($oProduct->oOrderInfo->bRemoveFlag) {
//                continue;
//            }
//
//            $aParams        = $this->_assemblePriceParams($oProduct);
//            $aPriceParams[] = $aParams;
//        }
//
//        return $aPriceParams;
//    }
//
//
//
//    /**
//     * @param  Product $oProduct $oProduct
//     * @return array
//     */
//    private function _assemblePriceParams(Product $oProduct) {
//        $aResult = [
//            'common_info'         => [
//                'business_id'    => $oProduct->oOrderInfo->iBusinessId,
//                'access_key_id'  => $oProduct->oCommonInfo->iAccessKeyID,
//                'app_version'    => $oProduct->oCommonInfo->sAppVersion,
//                'client_type'    => $oProduct->oCommonInfo->iClientType,
//                'lang'           => $oProduct->oCommonInfo->sLang,
//                'origin_id'      => $oProduct->oCommonInfo->iOriginId,
//                'terminal_id'    => $oProduct->oCommonInfo->iTerminalId,
//                'imei'           => $oProduct->oCommonInfo->sImei,
//                'is_from_b2b'    => $oProduct->oCommonInfo->bIsFromB2b,
//                'is_from_webapp' => $oProduct->oCommonInfo->bIsFromWebApp,
//                'from'           => $oProduct->oCommonInfo->sFrom,
//                'platform_type'  => $oProduct->oCommonInfo->iPlatformType,
//                'page_type'      => $oProduct->oCommonInfo->iPageType,
//            ],
//            'order_info'          => [
//                'current_lng'       => $oProduct->oAreaInfo->fCurLng,
//                'current_lat'       => $oProduct->oAreaInfo->fCurLat,
//                'area'              => $oProduct->oAreaInfo->iArea,
//                'from_lng'          => $oProduct->oAreaInfo->fFromLng,
//                'from_lat'          => $oProduct->oAreaInfo->fFromLat,
//                'from_poi_id'       => $oProduct->oAreaInfo->sFromPoiId,
//                'from_poi_type'     => $oProduct->oAreaInfo->sFromPoiType,
//                'from_address'      => $oProduct->oAreaInfo->sFromAddress,
//                'from_name'         => $oProduct->oAreaInfo->sFromName,
//                'starting_name'     => $oProduct->oAreaInfo->sStartingName,
//                'to_lng'            => $oProduct->oAreaInfo->fToLng,
//                'to_lat'            => $oProduct->oAreaInfo->fToLat,
//                'to_poi_id'         => $oProduct->oAreaInfo->sToPoiId,
//                'to_poi_type'       => $oProduct->oAreaInfo->sToPoiType,
//                'to_address'        => $oProduct->oAreaInfo->sToAddress,
//                'to_name'           => $oProduct->oAreaInfo->sToName,
//                'dest_name'         => $oProduct->oAreaInfo->sDestName,
//                'order_type'        => $oProduct->oOrderInfo->iOrderType,
//                'channel'           => $oProduct->oCommonInfo->sChannel,
//                'combo_type'        => $oProduct->oOrderInfo->iComboType,
//                'departure_time'    => $oProduct->oOrderInfo->iDepartureTime,
//                'map_type'          => $oProduct->oAreaInfo->sMapType,
//                'product_id'        => $oProduct->oOrderInfo->iProductId,
//                'require_level'     => $oProduct->oOrderInfo->iRequireLevel,
//                'district'          => $oProduct->oAreaInfo->iDistrict,
//                'payments_type'     => $oProduct->oOrderInfo->iPaymentsType,
//                'carpool_seat_num'  => $oProduct->oOrderInfo->iCarpoolSeatNum,
//                'abstract_district' => $oProduct->oAreaInfo->sAbstractDistrict,
//                'is_fast_car'       => UtilProduct::isFastcar($oProduct->oOrderInfo->iProductId),
//                'user_type'         => $oProduct->oPassengerInfo->iUserType,
//                'remove_flag'       => $oProduct->oOrderInfo->bRemoveFlag,
//            ],
//            'passenger_info'      => json_encode($oProduct->oPassengerInfo->aPassengerInfo, JSON_UNESCAPED_UNICODE),
//            // 以下这两个参数是为防止price-api报warning，垃圾参数
//            'custom_service_info' => '[]',
//            'one_key_activity'    => json_encode(['activity_switch' => false], JSON_UNESCAPED_UNICODE),
//        ];
//
//        $aOrderInfo = $oProduct->oOrderInfo->toArray();
//        $aNTuple    = FieldOrderNTuple::getOrderNTupleByOrder($aOrderInfo);
//        $aOrderInfo['n_tuple'] = $aNTuple;
//        $aExtraOrderInfo       = array_diff_key($aOrderInfo, $aResult['order_info']);
//        //按需补充order_extra
//        $aExtraOrderInfo['access_key_id'] = $oProduct->oCommonInfo->iAccessKeyID;
//        $aExtraOrderInfo['to_area']       = $oProduct->oAreaInfo->iToArea;
//        $aExtraOrderInfo['county']        = $oProduct->oAreaInfo->iFromCounty;
//        $aExtraOrderInfo['to_county']     = $oProduct->oAreaInfo->iToCounty;
//        // 途经点
//        if (!empty($oProduct->oCommonInfo->sStopoverPoints)) {
//            if (0 == $this->iIsExtraStop) {
//                $this->buildStopoverPoints($oProduct->oCommonInfo->sStopoverPoints);
//            }
//
//            $aExtraOrderInfo['stopover_points'] = $this->aStopoverPoints;
//            //获取dos存储格式的途经点信息
//            $aWayPointsInfo = OrderStopoverPoints::getDosFieldsByStopoverPoints($this->aStopoverPoints);
//            if (!empty($aWayPointsInfo)) {
//                $aExtraOrderInfo = array_merge($aExtraOrderInfo, $aWayPointsInfo);
//            }
//        }
//
//        $aResult['extra_info'] = [
//            'order'              => json_encode($aExtraOrderInfo, JSON_UNESCAPED_UNICODE),
//            'preference_product' => $oProduct->oCommonInfo->sPreferenceProduct,
//            'product_category'   => $oProduct->oOrderInfo->iProductCategory,
//        ];
//
//        return $aResult;
//    }
//
//    /**
//     * @param array $aPriceParams $aPriceParams
//     * @return array
//     */
//    private function _getMultiPriceByClient($aPriceParams) {
//        $aMultiParams = [
//            'estimatePriceReqs' => $aPriceParams,
//            'caller'            => PriceApiClient::CLIENT_PASSENGER,
//        ];
//        $_oClient     = new PriceApiClient(PriceApiClient::MODULE_NAME);
//        $aRet         = $_oClient->estimate($aMultiParams);
//        $aAthenaRet   = array();
//        if (isset($aRet['errno']) && 0 == $aRet['errno']) {
//            return $aRet['data'];
//        }
//
//        return $aAthenaRet;
//    }
//
//
//    /**
//     * @param array $aPriceParams aPriceParams
//     * @param array $aPriceResult aPriceResult
//     * @param array $aProductList aProductList
//     * @return array
//     * @throws ExceptionWithResp rep
//     */
//    private function _buildResponseAndRequest($aPriceParams, $aPriceResult, $aProductList) {
//        $aPriceResponse = array();
//        foreach ($aPriceResult as $iIndex => $aPrice) {
//            if (empty($aPrice) || !is_array($aPrice) || (empty($aPrice['bill_info']) && empty($aPrice['activity_info']) && empty($aPrice['payments_info']))) {
//                continue;
//            }
//
//            $aPriceResponse[$iIndex] = $this->_buildPriceResponseV2($aPrice);
//        }
//
//        // price-api请求失败，降级为只能发快车，通过596的形式
//        if (empty($aPriceResponse)) {
//            throw new ExceptionWithResp(
//                Code::E_ATHENA_REQUEST_FAIL,
//                RespCode::R_ESTIMATE_DOWNSTREAM_FAIL_DEGRADE,
//                '',
//                ['price_response' => json_encode($aPriceResponse)]
//            );
//        }
//
//        //formatRequest
//        return $this->getMultiResponseParamsV2($aPriceParams, $aPriceResponse, $aProductList);
//    }
//
//    /**
//     * @param array $aPrice price
//     * @return array
//     */
//    private function _buildPriceResponseV2(array $aPrice) {
//        $bPaymentSeparate = false;
//        $aPayment         = null;
//        $iComboType       = Constants\Horae::TYPE_COMBO_DEFAULT;
//        $aActivityInfo    = json_decode($aPrice['activity_info'], true);
//        $aBillInfo        = json_decode($aPrice['bill_info'], true);
//        $sCarLevel        = array_keys($aBillInfo['bills'])[0];
//        // activity转换为list，背景是一个产品多价格下需要有多个activity信息。兼容上线过程中不一致
//        if (isset($aActivityInfo) && !empty($aActivityInfo) && !is_array($aActivityInfo[0])) {
//            $aActivityInfo = [$aActivityInfo];
//        }
//
//        $aPaymentInfo = json_decode($aPrice['payments_info'], true);
//        $aExtraInfo   = $aPrice['extra_info'];
//
//        if (isset($aBillInfo) && !empty($aBillInfo)) {
//            if (!empty($aBillInfo['bills'][$sCarLevel])) {
//                $aBillInfo['count_price_type'] = $aBillInfo['bills'][$sCarLevel]['count_price_type'];
//            }
//
//            $iComboType = $aBillInfo['product_infos'][$sCarLevel]['combo_type'];
//            //和账单的交互后续用N元组标识产品，所以这里检查是否需要根据N元组将combo_type 做转换
//            $iCheckCombo = $this->_checkOrderNTupleConvert($aBillInfo, $sCarLevel);
//            if ($iCheckCombo) {
//                $iComboType = $iCheckCombo;
//            }
//        }
//
//        //支付方式返回值
//        switch ($iComboType) {
//            case Constants\Horae::TYPE_COMBO_CARPOOL:
//                // no break
//            case Constants\Horae::TYPE_COMBO_CARPOOL_INTER_CITY:
//                // no break
//            case Constants\Horae::TYPE_COMBO_CARPOOL_FLAT_RATE:
//                if (isset($aPaymentInfo) && !empty($aPaymentInfo)) {
//                    switch ($aPaymentInfo['pay_resp_type']) {
//                        case self::PAY_TYPE_NEW:
//                            if (isset($aPaymentInfo['user_pay_info']['carpool'])
//                                && is_array($aPaymentInfo['user_pay_info']['carpool'])
//                            ) {
//                                $aPayment         = $aPaymentInfo['user_pay_info']['carpool'];
//                                $bPaymentSeparate = true;
//                            }
//                            break;
//                        default:
//                            if (isset($aPaymentInfo['user_pay_info'])) {
//                                $aPayment = $aPaymentInfo['user_pay_info'];
//                            }
//                            break;
//                    }
//                }
//                break;
//            default:
//                switch ($aPaymentInfo['pay_resp_type']) {
//                    case self::PAY_TYPE_NEW:
//                        if (isset($aPaymentInfo['user_pay_info']['noncarpool'])
//                            && is_array($aPaymentInfo['user_pay_info']['noncarpool'])
//                        ) {
//                            $aPayment         = $aPaymentInfo['user_pay_info']['noncarpool'];
//                            $bPaymentSeparate = true;
//                        }
//                        break;
//                    case self::PAY_TYPE_BR:
//                        $aPayment = $aPaymentInfo['user_pay_info'];
//                        foreach ($aPayment['busi_payments'] as &$item) {
//                            unset($item['business_config']);
//                        }
//
//                        $bPaymentSeparate = true;
//                        break;
//                    default:
//                        if (isset($aPaymentInfo['user_pay_info'])) {
//                            $aPayment = $aPaymentInfo['user_pay_info'];
//                        }
//                        break;
//                }
//                break;
//        }
//
//        $aUserTypeInfo = array('user_pay_info' => $aPayment, 'payments_separate' => $bPaymentSeparate);
//
//        $aPriceItem['payments_info'] = $aUserTypeInfo;
//
//        $aPriceItem['bill_info']     = $aBillInfo;
//        $aPriceItem['activity_info'] = $aActivityInfo;
//        $aPriceItem['price_extra']   = $aExtraInfo;
//        return $aPriceItem;
//    }
//
//    /**
//     * @param array $aPriceParams aPriceParams
//     * @param array $aPriceResult aPriceResult
//     * @param array $aProductList aProductList
//     * @return array
//     */
//    public function getMultiResponseParamsV2(array $aPriceParams, array $aPriceResult, array $aProductList) {
//        $aRequestParams = [];
//        foreach ($aPriceParams as $iIndex => $aParam) {
//            $aParam['order_info']['real_type'] = $aParam['order_info']['order_type'];
//            if (isset($aParam['extra_info']['order'])) {
//                $aOrderExtra = json_decode($aParam['extra_info']['order'], true);
//                $aOrderInfo  = (array)$aParam['order_info'];
//                if (!empty($aOrderExtra)) {
//                    $aParam['order_info'] = $aOrderInfo + $aOrderExtra;
//                }
//
//                if (!empty($aOrderExtra['estimate_id'])) {
//                    $aRequestParams[$aOrderExtra['estimate_id']] = $aParam;
//                }
//            }
//        }
//
//        $aResponseParams = [];
//        foreach ($aPriceResult as $index => $aItem) {
//            $aBillInfo   = $aItem['bill_info'];
//            $sEstimateId = $aBillInfo['estimate_id'];
//            $aReqParam   = $aRequestParams[$sEstimateId];
//            if (empty($aReqParam)) {
//                continue;
//            }
//
//            $aResponseParam    = [
//                'common_info'        => $aReqParam['common_info'],
//                'order_info'         => $aReqParam['order_info'],
//                'passenger_info'     => json_decode($aReqParam['passenger_info'], true),
//                'bill_info'          => $aItem['bill_info'],
//                'payments_info'      => $aItem['payments_info'],
//                'activity_info'      => $aItem['activity_info'],
//                'price_extra'        => $aItem['price_extra'],
//                'preference_product' => $aReqParam['extra_info']['preference_product'],
//            ];
//            $aResponseParams[] = $aResponseParam;
//        }
//
//        return $aResponseParams;
//    }
//
//    /**
//     * @param array  $aBillInfo bill
//     * @param string $sCarLevel carlevel
//     *
//     * @return int
//     */
//    private function _checkOrderNTupleConvert($aBillInfo, $sCarLevel) {
//        $iComboType = 0;
//        if (!isset($aBillInfo['order_n_tuple_infos'][$sCarLevel])
//            || empty($aBillInfo['order_n_tuple_infos'][$sCarLevel])
//        ) {
//            return $iComboType;
//        }
//
//        $aOrderNTuple = $aBillInfo['order_n_tuple_infos'][$sCarLevel];
//        if (isset($aOrderNTuple['carpool_type']) && !empty($aOrderNTuple['carpool_type'])) {
//            switch ($aOrderNTuple['carpool_type']) {
//                case OrderNTuple::CARPOOL_TYPE_NORMAL:
//                    //no break
//                case OrderNTuple::CARPOOL_TYPE_LOW_PRICE:
//                    //no break
//                case OrderNTuple::CARPOOL_TYPE_STATION:
//                    //no break
//                case OrderNTuple::CARPOOL_TYPE_FLAT_RATE:
//                    $iComboType = Constants\Horae::TYPE_COMBO_CARPOOL;
//                    break;
//                case OrderNTuple::CARPOOL_TYPE_INTERCITY_NEW:
//                    $iComboType = Constants\Horae::TYPE_COMBO_CARPOOL_INTER_CITY;
//                    break;
//                default:
//                    break;
//            }
//        }
//
//        if (isset($aOrderNTuple['is_special_price']) && !empty($aOrderNTuple['is_special_price'])) {
//            if ($aOrderNTuple['is_special_price']) {
//                $iComboType = Constants\Horae::TYPE_COMBO_SPECIAL_RATE;
//            }
//        }
//
//        return $iComboType;
//    }
//
//    /**
//     * 解析途经点
//     *
//     * @param  string $sStopoverPoints sStopoverPoints
//     * @return void
//     */
//    public function buildStopoverPoints($sStopoverPoints) {
//        $this->iIsExtraStop = 0;
//        if (!empty($sStopoverPoints)) {
//            $aPointsInfo = json_decode($sStopoverPoints, true);
//            $aPointsInfo = OrderStopoverPoints::getPointsInfoFromRequest($aPointsInfo);
//            if (empty($aPointsInfo)) {
//                return;
//            }
//
//            $this->iIsExtraStop    = 1;
//            $this->aStopoverPoints = $aPointsInfo;
//        } else {
//            $this->aStopoverPoints = [];
//        }
//    }
//}
