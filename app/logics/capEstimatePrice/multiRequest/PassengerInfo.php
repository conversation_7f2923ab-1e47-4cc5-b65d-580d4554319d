<?php
//namespace PreSale\Logics\capEstimatePrice\multiRequest;
//
//use BizCommon\Models\Passenger\Passenger;
//use Dirpc\SDK\PreSale\CapMultiEstimatePriceRequest as Request;
//
///**
// *
// * Copyraight (c) 2021 xiaojukeji.com, Inc. All Rights Reserved.
// * @author: wangtuan<PERSON><EMAIL>
// * @date: 2021/4/6 9:52 下午
// * @desc: 乘客相关信息
// * @wiki:
// *
// */
//
//class PassengerInfo
//{
//    //乘客信息
//    public $aPassengerInfo;
//    //用户Token
//    public $sToken;
//    //用户UID
//    public $iUid;
//    //用户PID
//    public $iPid;
//    //手机号
//    public $sPhone;
//    //用户类型 1-普通用户 2-企业用户
//    public $iUserType;
//
//    /**
//     * PassengerInfo constructor.
//     * @param Request $oEstimateRequest   oEstimateRequest
//     * @param array   $aPassportPassenger aPassportPassenger
//     */
//    public function __construct(Request $oEstimateRequest, array $aPassportPassenger) {
//        if (empty($aPassportPassenger)) {
//            $aPassportPassenger = Passenger::getInstance()->getPassengerByTokenFromPassport($oEstimateRequest->getToken());
//        }
//
//        if (empty($aPassportPassenger)) {
//            return;
//        }
//
//        $this->aPassengerInfo = $aPassportPassenger;
//        $this->iUid           = (int)$aPassportPassenger['uid'];
//        $this->iPid           = (int)$aPassportPassenger['pid'];
//        $this->sPhone         = $aPassportPassenger['phone'];
//        $this->sToken         = $oEstimateRequest->getToken();
//        $this->iUserType      = $oEstimateRequest->getUserType();
//    }
//}
