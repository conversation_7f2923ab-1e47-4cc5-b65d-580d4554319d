<?php
namespace PreSale\Logics\luxEstimatePrice\multiResponse;

use BizLib\Utils\PublicLog;
use Disf\SPL\Trace;
use PreSale\Logics\luxEstimatePrice\multiRequest\Product;

/**
 * 多预估public日志组建
 * Class MultiEstimatePublicLog
 * @package PreSale\Logics\estimatePrice\multiResponse
 */
class MultiEstimatePublicLogV2
{
    const PRICE_DESC_NO_REWARDS      = 0;
    const PRICE_DESC_ONLY_REWARDS    = 1;
    const PRICE_DESC_INCLUDE_REWARDS = 2;
    /**
     * @var array
     */
    private $_aInfos;

    /**
     * @var array
     */
    private $_aResponseInfo;

    /**
     * @var array
     */
    private $_aProductList;

    /**
     * MultiEstimatePublicLog constructor.
     * @param array $aInfos        $aInfos
     * @param array $aResponseInfo $aResponseInfo
     * @param array $aProductList  $aProductList
     */
    public function __construct($aInfos, $aResponseInfo, $aProductList) {
        $this->_aInfos        = $aInfos;
        $this->_aResponseInfo = $aResponseInfo;
        $this->_aProductList  = $aProductList;
    }


    /**
     * 写多预估流程的public 日志
     * @return void
     */
    public function multiWritePublicLog() {
        //写表单展示日志
        $this->_writeOrderEstimateInfo();
        //写预估public日志
        $this->_writeOrderEstimatePrice();
    }

    /**
     * 此前依据老的aInfo记录日志容易出错，新增一份依据新的aInfo记录的日志
     * 且仅记录预估阶段最终会展示的产品线
     * @return void
     */
    private function _writeOrderEstimateInfo() {

        $aIndexedInfo = [];
        foreach ($this->_aInfos as $aInfo) {
            $iEstimateId = $aInfo['bill_info']['estimate_id'];
            $aIndexedInfo[$iEstimateId] = $aInfo;
        }

        if (!empty($this->_aResponseInfo['data']['estimate_car_level_data'])) {
            foreach ($this->_aResponseInfo['data']['estimate_car_level_data'] as $aResponse) {
                $iEstimateId      = $aResponse['estimate_id'];
                $aInfo            = $aIndexedInfo[$iEstimateId];
                $sCarLevel        = $aInfo['order_info']['require_level'];
                $aBillProductInfo = $aInfo['bill_info']['product_infos'][$sCarLevel];
                $aEstimateStatistic = [
                    'opera_stat_key'     => 'g_lux_order_estimate_info',
                    'estimate_id'        => $iEstimateId,
                    'area'               => $aInfo['order_info']['area'],
                    'product_id'         => $aInfo['order_info']['product_id'],
                    'require_level'      => $sCarLevel,
                    'origin_combo_type'  => $aInfo['order_info']['combo_type'],
                    'combo_type'         => $aBillProductInfo['combo_type'], //因为账单可能修改combo_type，此处特别记录一下请求中的combo_type
                    'driver_id'          => $aResponse['driver_id'],
                ];

                PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);
            }
        }

        if (!empty($this->_aResponseInfo['data']['estimate_driver_level_data'])) {
            foreach ($this->_aResponseInfo['data']['estimate_driver_level_data'] as $aResponse) {
                $iEstimateId      = $aResponse['estimate_id'];
                $aInfo            = $aIndexedInfo[$iEstimateId];
                $sCarLevel        = $aInfo['order_info']['require_level'];
                $aBillProductInfo = $aInfo['bill_info']['product_infos'][$sCarLevel];
                $aEstimateStatistic = [
                    'opera_stat_key'    => 'g_lux_order_estimate_info',
                    'estimate_id'       => $iEstimateId,
                    'area'              => $aInfo['order_info']['area'],
                    'product_id'        => $aInfo['order_info']['product_id'],
                    'require_level'     => $sCarLevel,
                    'origin_combo_type' => $aInfo['order_info']['combo_type'],
                    'combo_type'        => $aBillProductInfo['combo_type'], //因为账单可能修改combo_type，此处特别记录一下请求中的combo_type
                    'driver_id'          => $aResponse['driver_id'],
                ];

                PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);
            }
        }
    }

    /**
     * @return void
     */
    private function _writeOrderEstimatePrice() {
        foreach ($this->_aInfos as $aInfo) {
            if (empty($aInfo['bill_info']['estimate_id'])) {
                continue;
            }

            $oCurProduct = null;
            $iEstimateID = $aInfo['order_info']['estimate_id'];
            //  @var Product $oProduct
            foreach ($this->_aProductList as $oProduct) {
                if ($iEstimateID == $oProduct->oOrderInfo->sEstimateID) {
                     $oCurProduct = $oProduct;
                     break;
                }
            }

            $this->_writePublicLog($aInfo, $oCurProduct);
        }
    }

    /**
     * @param array   $aInfo    $aInfo
     * @param Product $oProduct $oProduct
     * @return void
     */
    private function _writePublicLog(array $aInfo, Product $oProduct) {
        $sCarLevel    = $aInfo['order_info']['require_level'];
        $aBillInfo    = $aInfo['bill_info']['bills'][$sCarLevel];
        $aProductInfo = $aInfo['bill_info']['product_infos'][$sCarLevel];

        $aLogFenceInfo = [];
        if (isset($aInfo['order_info']['fence_info']) && is_array($aInfo['order_info']['fence_info'])) {
            foreach ($aInfo['order_info']['fence_info'] as $aFenceInfo) {
                if (!empty($aFenceInfo)) {
                    $aLogFenceInfo[] = $aFenceInfo;
                }
            }
        }

        $aEstimateStatistic = [
            'opera_stat_key'                       => 'g_lux_order_estimate_price',
            'imei'                                 => $oProduct->oCommonInfo->sImei,
            'appversion'                           => $oProduct->oCommonInfo->sAppVersion,
            'client_type'                          => $oProduct->oCommonInfo->iClientType,
            'access_key_id'                        => $oProduct->oCommonInfo->iAccessKeyID,
            'channel'                              => $oProduct->oCommonInfo->sChannel,
            'pLang'                                => $oProduct->oCommonInfo->sLang,
            'pid'                                  => $oProduct->oPassengerInfo->iPid,
            'phone'                                => $oProduct->oPassengerInfo->sPhone,
            'menu_id'                              => $oProduct->oOrderInfo->sMenuID,
            'page_type'                            => $oProduct->oOrderInfo->iPageType,
            'origin_page_type'                     => $oProduct->oOrderInfo->iOriginPageType,
            'call_car_type'                        => $oProduct->oOrderInfo->iCallCarType,
            'product_id'                           => $oProduct->oOrderInfo->iProductId,
            'combo_type'                           => $oProduct->oOrderInfo->iComboType,
            'bill_combo_type'                      => $aInfo['bill_info']['product_infos'][$sCarLevel]['combo_type'],
            'require_level'                        => $sCarLevel,
            'area'                                 => $oProduct->oAreaInfo->iArea,
            'to_area'                              => $oProduct->oAreaInfo->iToArea,
            'district'                             => $oProduct->oAreaInfo->iDistrict,
            'flng'                                 => $oProduct->oAreaInfo->fFromLng,
            'flat'                                 => $oProduct->oAreaInfo->fFromLat,
            'tlng'                                 => $oProduct->oAreaInfo->fToLng,
            'tlat'                                 => $oProduct->oAreaInfo->fToLat,
            'current_lng'                          => $oProduct->oAreaInfo->fCurLng,
            'current_lat'                          => $oProduct->oAreaInfo->fCurLat,
            'county'                               => $oProduct->oAreaInfo->iFromCounty,
            'to_county'                            => $oProduct->oAreaInfo->iToCounty,
            'from_name'                            => str_replace(PHP_EOL, '', $oProduct->oAreaInfo->sFromName),
            'to_name'                              => str_replace(PHP_EOL, '', $oProduct->oAreaInfo->sToName),
            'order_type'                           => $oProduct->oOrderInfo->iOrderType,
            'designated_driver'                    => $oProduct->oOrderInfo->sDesignatedDriver,
            'time_cost'                            => $aInfo['bill_info']['driver_minute'],
            'driver_second'                        => $aInfo['bill_info']['driver_second'] ?? 0,
            'driver_metre'                         => $aInfo['bill_info']['driver_metre'] ?? 0,
            'total_fee'                            => $aBillInfo['total_fee'] ?? 0,
            'pre_total_fee'                        => $aBillInfo['pre_total_fee'] ?? 0,
            'dynamic_total_fee'                    => $aBillInfo['dynamic_total_fee'] ?? 0,
            'cap_price'                            => $aBillInfo['cap_price'] ?? 0,
            'estimate_fee'                         => $aInfo['activity_info'][0]['estimate_fee'] ?? 0,
            'is_carpool_open'                      => (int)($aInfo['bill_info']['is_carpool_open']),
            'carpool_fail_price'                   => $aBillInfo['carpool_fail_dynamic_total_fee'] ?? 0,
            'discount_fee'                         => $aInfo['activity_info'][0]['discount_fee'],
            'station_id'                           => $aInfo['bill_info']['carpool_station_info']['uid'] ?? 0,
            'is_hit_member_capping'                => (int)($aBillInfo['is_hit_member_capping'] ?? 0),
            'member_dynamic_capping'               => $aBillInfo['member_dynamic_capping'] ?? -1,
            'dynamic_price_without_member_capping' => $aBillInfo['dynamic_price_without_member_capping'] ?? 0,
            'member_level_id'                      => $aInfo['passenger_info']['member_profile']['level_id'] ?? 0,
            'default_pay_type'                     => $this->_getSelectedPayType($aInfo['payments_info']['user_pay_info']['busi_payments'] ?? []),
            'order_n_tuple'                        => json_encode($aInfo['n_tuple']),
            'airport_type'                         => $oProduct->oOrderInfo->iAirportType,
            'product_category'                     => $oProduct->oOrderInfo->iProductCategory,
            'is_dynamic'                           => $aBillInfo['is_has_dynamic'] ?? 0,
            'dynamic_price_id'                     => $aBillInfo['dynamic_info']['dynamic_price_id'],
            'dynamic_diff_price'                   => $aBillInfo['dynamic_diff_price'],
            'dynamic_price'                        => $aBillInfo['dynamic_info']['dynamic_price'],
            'dynamic_kind'                         => $aBillInfo['dynamic_info']['dynamic_kind'],
            'dynamic_times'                        => $aBillInfo['dynamic_times'] ?? 0,
            'coupon'                               => $this->_getCouponInfoStr($aInfo['order_info']['channel'], $aInfo),
            'discount_info'                        => json_encode($aInfo['activity_info'][0]['discount_desc']),
            'user_status'                          => $aInfo['price_extra']['user_status'],
            'estimate_trace_id'                    => Trace::traceId(),
            'estimate_id'                          => $aInfo['order_info']['estimate_id'],
            'red_packet'                           => 0.0,
            'rewards_display_type'                 => 0,
            'show_pickup_service'                  => 0,
            'wait_discount'                        => $aBillInfo['wait_discount'] ?? 0.1,
            'wait_minute'                          => $aBillInfo['dynamic_info']['wait_minute'] ?? 10,
            'queue_data'                           => null,
            'fence_ids'                            => $aLogFenceInfo,
        ];

        PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);
    }

    /**
     * @param array $aPayments aPayments
     * @return int
     */
    private function _getSelectedPayType($aPayments) {
        foreach ($aPayments as $aPayment) {
            if (!empty($aPayment['isSelected'])) {
                return $aPayment['tag'];
            }
        }

        return 0;
    }


    /**
     * @param int   $iChannel channel
     * @param array $aInfo    aInfo
     * @return string
     */
    private function _getCouponInfoStr($iChannel, $aInfo) {
        $aResult     = [];
        $aCouponInfo = $aInfo['activity_info'][0]['coupon_info'];
        if (empty($aCouponInfo['default_coupon'])) {
            $aResult['default_coupon'] = [];
        } else {
            $aResult['default_coupon'] = [
                'channel'       => $iChannel,
                'batchid'       => $aCouponInfo['default_coupon']['batchid'] ?? 0,
                'amount'        => ($aCouponInfo['default_coupon']['amount'] ?? 0) / 100,
                'estimate_show' => $aCouponInfo['default_coupon']['estimate_show'] ?? 0,
            ];
        }

        if (empty($aCouponInfo['activity_coupon'])) {
            $aResult['activity_coupon'] = [];
        } else {
            $aResult['activity_coupon'] = [
                'channel'       => $iChannel,
                'batchid'       => $aCouponInfo['activity_coupon']['batchid'] ?? 0,
                'amount'        => $aCouponInfo['activity_coupon']['money'] ?? 0,
                'estimate_show' => $aCouponInfo['activity_coupon']['estimate_show'] ?? 0,
            ];
        }

        return json_encode($aResult);
    }
}
