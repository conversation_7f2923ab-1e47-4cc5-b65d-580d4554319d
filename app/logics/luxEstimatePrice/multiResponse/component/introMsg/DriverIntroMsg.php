<?php
namespace PreSale\Logics\luxEstimatePrice\multiResponse\component\introMsg;

use PreSale\Logics\luxEstimatePrice\multiResponse\component\CarLevelInfo;

/**
 * Class DriverIntroMsg
 * @package PreSale\Logics\luxEstimatePrice\multiResponse\component\introMsg;
 */
class DriverIntroMsg
{
    protected $_aInfo;

    /**
     * AnyCarIntroMsg constructor.
     * @param array $aInfo 全局品类数据
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
    }

    /**
     * @param array $response 预估结果
     * @return array
     */
    public function build($response) {
        $msg = $this->_buildIntroMsg();
        return Common::format($msg, $response);
    }

    /**
     * @return array
     */
    private function _buildIntroMsg() {
        $ret           = ['intro_msg' => '', 'intro_icon' => '', 'profile_link' => ''];
        $aCarLevelInfo = CarLevelInfo::getInstance()->getCarLevelInfo($this->_aInfo['order_info']['require_level']);
        $aDriverInfo   = $this->_aInfo['order_info']['designated_driver_info'];
        $ret['intro_msg'] = $aDriverInfo['profile'];
        //todo 该字段是否需要使用dds oneconf的gray-icon
        $ret['intro_icon']   = $aDriverInfo['headImg'];
        $ret['profile_link'] = $aDriverInfo['profileLink'];
        $ret['driver_desc']  = $aDriverInfo['driverDesc'];
        $ret['driver_desc_link'] = $aDriverInfo['driverDescLink'];
        return $ret;
    }
}
