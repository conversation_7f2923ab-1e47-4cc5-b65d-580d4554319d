<?php

namespace PreSale\Logics\luxEstimatePrice\multiResponse\component\introMsg;

use PreSale\Logics\luxEstimatePrice\multiResponse\Util;

/**
 * @desc   :
 * <AUTHOR> dingwei <<EMAIL>>
 * @date   : 19/5/16
 */
class Handler
{

    /**
     * @param array $aInfo 全局品类数据
     * @return AnyCarIntroMsg|CarpoolIntroMsg|NormalIntroMsg|NormalIntroMsgV2
     */
    public static function select($aInfo) {
        if (Util::isDesignatedDriver($aInfo)) {
            return new DriverIntroMsg($aInfo);
        }

        return new CarIntroMsg($aInfo);
    }
}
