<?php

namespace PreSale\Logics\luxEstimatePrice\multiResponse\component\introMsg;

use BizLib\Config as NuwaConfig;
use PreSale\Logics\luxEstimatePrice\multiResponse\component\CarLevelInfo;

/**
 * Class CarIntroMsg
 * @package PreSale\Logics\luxEstimatePrice\multiResponse\component\introMsg;
 */
class CarIntroMsg
{
    protected $_aInfo;

    /**
     * @var bool|mixed|null
     */
    private $_aConfig;

    /**
     * AnyCarIntroMsg constructor.
     * @param array $aInfo 全局品类数据
     */
    public function __construct($aInfo) {
        $this->_aInfo   = $aInfo;
        $this->_aConfig = NuwaConfig::text('config_text', 'lux_car_level_config');
    }

    /**
     * @param array $response 预估结果
     * @return array
     */
    public function build($response) {
        $msg = $this->_buildIntroMsg();
        return Common::format($msg, $response);
    }

    /**
     * @return array
     */
    private function _buildIntroMsg() {
        $ret           = ['intro_msg' => '', 'intro_icon' => '', 'profile_link' => ''];
        $aCarLevelInfo = CarLevelInfo::getInstance()->getCarLevelInfo($this->_aInfo['order_info']['require_level']);
        $aCarLevelConf = $this->_aConfig[$this->_aInfo['order_info']['require_level']];
        $ret['intro_msg']    = $aCarLevelConf['level_name'];
        $ret['intro_icon']   = $aCarLevelConf['level_icon'];
        $ret['profile_link'] = $aCarLevelConf['profile_link'];
        $ret['car_feature']  = $aCarLevelConf['car_feature'];
        return $ret;
    }
}
