<?php
namespace PreSale\Logics\luxEstimatePrice\multiResponse\component\introMsg;

use BizLib\Utils\UtilHelper;

/**
 * @desc   :
 * <AUTHOR> dingwei <<EMAIL>>
 * @date   : 19/5/16
 */
class Common
{

    /**
     * @param array $msg       补充信息
     * @param array $aResponse 预估返回值
     * @return mixed
     */
    public static function format($msg, $aResponse) {
        if (isset($msg['intro_msg'])) {
            $aResponse['intro_msg'] = $msg['intro_msg'];
        }

        if (isset($msg['intro_icon'])) {
            $aResponse['intro_icon'] = $msg['intro_icon'];
        }

        if (isset($msg['profile_link'])) {
            $aResponse['profile_link'] = $msg['profile_link'];
        }

        if (isset($msg['car_feature'])) {
            $aResponse['car_feature'] = $msg['car_feature'];
        }

        if (isset($msg['driver_desc'])) {
            $aResponse['driver_desc'] = $msg['driver_desc'];
        }

        if (isset($msg['driver_desc_link'])) {
            $aResponse['driver_desc_link'] = $msg['driver_desc_link'];
        }

        $aResponse['fee_detail_url'] = UtilHelper::getConfigUrl('fee_detail_h5_new');
        return $aResponse;
    }
}
