<?php
/**
 * Created by PhpStorm.
 * <AUTHOR> chen<PERSON><PERSON><PERSON> <<EMAIL>>
 * Date: 2019/6/11
 * Time: 下午2:33
 */

namespace PreSale\Logics\luxEstimatePrice\multiResponse\component\productInfo;

use BizLib\Utils\ProductCategory;

/**
 * Class Common
 * @package PreSale\Logics\estimatePrice\multiResponse\component\productInfo
 */
class Common
{
    /**
     * @param array $aNTuple tuple
     * @return int|string
     */
    public static function getProductCategory($aNTuple) {
        if (!isset($aNTuple) || empty($aNTuple)) {
            $aMap = [];
        } else {
            $aMap = array(
                'business_id'   => (int)($aNTuple['business_id']),
                'require_level' => (int)($aNTuple['require_level']),
                'combo_type'    => (int)($aNTuple['combo_type']),
            );

            if (isset($aNTuple['carpool_type']) && $aNTuple['carpool_type'] > 0) {
                $aMap['carpool_type'] = (int)($aNTuple['carpool_type']);
            }

            if (isset($aNTuple['route_type']) && $aNTuple['route_type'] > 0) {
                $aMap['route_type'] = (int)($aNTuple['route_type']);
            }

            if (isset($aNTuple['is_special_price']) && !empty($aNTuple['is_special_price'])) {
                $aMap['is_special_price'] = $aNTuple['is_special_price'] ? 1 : 0;
            }
        }

        $oProductCategory = new ProductCategory();

        return $oProductCategory->getProductCategory($aMap);
    }

    /**
     * @param array $aInfo ainfo
     * @return string
     */
    public static function getEstimateId($aInfo) {
        $sEstimateId = '';
        if (isset($aInfo['bill_info']['estimate_id'])) {
            $sEstimateId = $aInfo['bill_info']['estimate_id'];
        }

        return $sEstimateId;
    }
}
