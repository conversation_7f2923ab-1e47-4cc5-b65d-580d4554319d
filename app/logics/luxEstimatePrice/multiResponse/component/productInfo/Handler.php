<?php
namespace PreSale\Logics\luxEstimatePrice\multiResponse\component\productInfo;

use PreSale\Logics\luxEstimatePrice\multiResponse\Util;

/**
 * Class Handler
 * @package PreSale\Logics\estimatePrice\multiResponse\component\productInfo
 */
class Handler
{
    /**
     * @param array $aInfo aInfo
     * @return AnycarProductInfo|CarpoolProductInfo|NormalProductInfo|SpecialRateProductInfo|NormalProductInfoV2
     */
    public static function select($aInfo) {
        if (Util::isDesignatedDriver($aInfo)) {
            return new DriverProductInfo($aInfo);
        }

        return new CarProductInfo($aInfo);
    }
}
