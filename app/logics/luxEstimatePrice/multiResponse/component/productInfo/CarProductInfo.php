<?php
/**
 * Created by PhpStorm.
 * <AUTHOR> chen<PERSON><PERSON><PERSON> <<EMAIL>>
 * Date: 2019/6/3
 * Time: 下午5:53
 */

namespace PreSale\Logics\luxEstimatePrice\multiResponse\component\productInfo;

/**
 * Class NormalProductInfo
 * @package PreSale\Logics\estimatePrice\multiResponse\component\productInfo
 */
class CarProductInfo
{
    protected $_aInfo     = [];
    protected $_sCarLevel = '';

    /**
     * NormalProductInfo constructor.
     * @param array $aInfo ainfo
     */
    public function __construct($aInfo) {
        $this->_aInfo     = $aInfo;
        $this->_sCarLevel = $this->_aInfo['order_info']['require_level'];
    }

    /**
     * @param array $aResponse $aResponse
     * @return mixed
     */
    public function build($aResponse) {
        $aResponse['estimate_id']      = Common::getEstimateId($this->_aInfo);
        $aResponse['require_level']    = $this->_aInfo['order_info']['require_level'];
        $aResponse['business_id']      = $this->_aInfo['common_info']['business_id'];
        $aResponse['combo_type']       = $this->_aInfo['order_info']['combo_type'];
        $aResponse['product_category'] = $this->_aInfo['order_info']['product_category'];

        return $aResponse;
    }
}
