<?php
namespace PreSale\Logics\luxEstimatePrice\multiResponse\component\driverInfo;

use PreSale\Logics\luxEstimatePrice\multiResponse\Util;

/**
 * Class Handler
 * @package PreSale\Logics\estimatePrice\multiResponse\component\detailFeeInfo
 */
class Handler
{
    /**
     * @param array $aInfo aInfo
     * @return AnycarDetailFeeInfo|CarpoolDetailFeeInfo|NormalDetailFeeInfo|SpecialRateDetailFeeInfo
     */
    public static function select($aInfo) {
        if (Util::isDesignatedDriver($aInfo)) {
            return new DriverInfo($aInfo);
        }

        return new CarDriverInfo($aInfo);
    }
}
