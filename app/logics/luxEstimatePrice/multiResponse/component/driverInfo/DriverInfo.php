<?php
/**
 * Created by PhpStorm.
 * <AUTHOR> chen<PERSON><PERSON><PERSON> <<EMAIL>>
 * Date: 2019/6/3
 * Time: 下午5:53
 */

namespace PreSale\Logics\luxEstimatePrice\multiResponse\component\driverInfo;

/**
 * Class NormalProductInfo
 * @package PreSale\Logics\estimatePrice\multiResponse\component\productInfo
 */
class DriverInfo
{
    protected $_aInfo = [];

    /**
     * NormalProductInfo constructor.
     * @param array $aInfo ainfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
    }

    /**
     * @param array $aResponse $aResponse
     * @return mixed
     */
    public function build($aResponse) {
        $aResponse['driver_id']      = $this->_aInfo['order_info']['designated_driver_info']['driverId'] ?? 0;
        $aResponse['is_real_driver'] = $this->_aInfo['order_info']['designated_driver_info']['is_real_driver'] ?? 0;
        $aResponse['is_online']      = $this->_aInfo['order_info']['designated_driver_info']['isOnline'] ?? 0;
        $aResponse['is_driver_tab']  = 1;

        return $aResponse;
    }
}
