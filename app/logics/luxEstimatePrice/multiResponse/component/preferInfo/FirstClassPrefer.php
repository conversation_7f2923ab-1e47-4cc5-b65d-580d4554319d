<?php
namespace PreSale\Logics\luxEstimatePrice\multiResponse\component\preferInfo;

use PreSale\Logics\order\AnyCarOrderLogic;
use BizLib\Config as NuwaConfig;
use BizLib\Client\UfsClient;

/**
 * @desc   :豪华车ANYCAR响应构造
 * <AUTHOR> yang<PERSON><PERSON><PERSON> <<EMAIL>>
 * @date   : 20/2/18
 */
class FirstClassPrefer
{

    const PREFER_COUNT_DEFAULT = 5;

    protected $_aInfo;

    /**
     * FirstClassPrefer constructor.
     * @param array $aInfo 全局品类数据
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
    }

    /**
     * @param array $response 预估结果
     * @return array
     */
    public function build($response) {
        if (!\BizLib\Utils\Product::isAnyCar($this->_aInfo['order_info']['product_id'])) {
            return $response;
        }

        $sAnyCarType    = AnyCarOrderLogic::getInstance()->getAnyCarType($this->_aInfo['bill_info']['multi_info']);
        $oApolloV2      = new ApolloV2();
        $oFeatureToggle = $oApolloV2->featureToggle(
            'pre_sale_v2_prefer_open',
            [
                'product_id'  => $this->_aInfo['order_info']['product_id'],
                'anycar_type' => $sAnyCarType,
            ]
        );
        if (!$oFeatureToggle->allow()) {
            return $response;
        }

        $response = $this->_buildPreferData($response);
        return $response;
    }

    /**
     * @param array $response 预估结果
     * @return array
     */
    private function _buildPreferData($response) {
        $response['prefer_data'] = array(
            'prefer_desc'         => NuwaConfig::text('prefer_info', 'prefer_desc'),
            'prefer_is_select'    => 0,
            'prefer_select_count' => 0,
            'prefer_total_count'  => self::PREFER_COUNT_DEFAULT,
            'prefer_display_tags' => array(
                $this->_getDefaultSelectInfo(),
                $this->_getDefaultPreferDisplayNames(),
            ),
        );
        $oUfsClient = new UfsClient();
        $aUfsRet    = $oUfsClient->getFeature(
            ['xxxxxxxxx'],
            ['passenger_id' => $this->_aInfo['xxxxxxxxx']],
            'order.base'
        );
        //从UFS取不到，返回默认信息
        if (!isset($aUfsRet['errno']) || 0 != $aUfsRet['errno']) {
            return $response;
        }

        $aSelectInfo   = $this->_getSelectInfo($aUfsRet['result']['prefer_info']);
        $aDisplayNames = $this->_getPreferDisplayNames($aUfsRet['result']['prefer_info']);

        $response['prefer_data'] = array(
            'prefer_desc'         => $this->_getSelectDesc($this->_isSelect($aUfsRet['result']['prefer_info'])),
            'prefer_is_select'    => $this->_isSelect($aUfsRet['result']['prefer_info']),
            'prefer_select_count' => count($aDisplayNames['display_names']),//偏好里的已选数量
            'prefer_total_count'  => self::PREFER_COUNT_DEFAULT,
            'prefer_display_tags' => array(
                $aSelectInfo,
                $aDisplayNames,
            ),
        );
        return $response;
    }
    /**
     * 获取个人偏好服务信息
     * @param array $sPreferInfo 偏好信息
     * @return array
     */
    private function _getPreferDisplayNames($sPreferInfo) {
        $aDisplayNames = $this->_getDefaultPreferDisplayNames();
        if (!empty($aUfsRet['result'])) {
            $aPreferInfo = json_decode($sPreferInfo,true);
            if (count($aPreferInfo['prefer_info']['displayTags']) > 0) {
                $aDisplayNames['display_names'] = $aPreferInfo['prefer_info']['displayTags'][0]['displayNames'];
            }
        }

        return $aDisplayNames;
    }

    /**
     * 获取选择服务信息
     * @param array $sPreferInfo 偏好信息
     * @return array
     */
    private function _getSelectInfo($sPreferInfo) {
        $sSelectInfo = $this->_getDefaultSelectInfo();
        if (!empty($aUfsRet['result'])) {
            $aPreferInfo = json_decode($sPreferInfo,true);
            if (count($aPreferInfo['prefer_drivers']) > 0) {
                $sSelectInfo['display_names'] = array($aPreferInfo['prefer_drivers'[0]['driver_name']]);
                return $sSelectInfo;
            }

            //如果司务员没选，那看看车型选了没
            if (count($aPreferInfo['prefer_cars']) > 0) {
                $sSelectInfo['display_names'] = array($aPreferInfo['prefer_cars'][0]['display_name']);
                return $sSelectInfo;
            }
        }

        return $sSelectInfo;
    }
    /**
     * 获取选择描述
     * @param int $iIsSelect 已选信息
     * @return array
     */
    private function _getSelectDesc($iIsSelect) {
        if ($iIsSelect) {
            return NuwaConfig::text('prefer_info', 'prefer_desc_select');
        }

        return NuwaConfig::text('prefer_info', 'prefer_desc');
    }

    /**
     * 获取默认选中信息
     * @return array
     */
    private function _getDefaultSelectInfo() {
        $aSelectInfo = array(
            'display_icon'  => NuwaConfig::text('prefer_info', 'display_icon_select'),
            'display_names' => array(
                NuwaConfig::text('prefer_info', 'display_name_select_default'),
            ),
        );
        return $aSelectInfo;
    }

    /**
     * 获取默认偏好设置
     * @return array
     */
    private function _getDefaultPreferDisplayNames() {
        $aPreferDisplayNames = array(
            'display_icon'  => NuwaConfig::text('prefer_info', 'display_icon_prefer'),
            'display_names' => array(
                NuwaConfig::text('prefer_info', 'display_name_prefer_default'),
            ),
        );
        return $aPreferDisplayNames;
    }

    /**
     * 是否已经选择服务
     * @param array $sPreferInfo 偏好信息
     * @return bool
     */
    private function _isSelect($sPreferInfo) {
        if (!empty($aUfsRet['result'])) {
            $aPreferInfo = json_decode($sPreferInfo,true);
            if (count($aPreferInfo['prefer_drivers']) > 0) {
                return 1;
            }

            //如果司务员没选，那看看车型选了没
            if (count($aPreferInfo['prefer_cars']) > 0) {
                return 1;
            }
        }

        return 0;
    }
}
