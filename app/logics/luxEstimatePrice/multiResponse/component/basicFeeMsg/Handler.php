<?php
namespace PreSale\Logics\luxEstimatePrice\multiResponse\component\basicFeeMsg;

use PreSale\Logics\luxEstimatePrice\multiResponse\Util;

/**
 * Class Handler
 * @package PreSale\Logics\estimatePrice\multiResponse\component\basicFeeMsg
 */
class Handler
{
    /**
     * @param array $aInfo aInfo
     * @return AnycarBasicFeeMsg|CarpoolBasicFeeMsg|NormalBasicFeeMsg|SpecialRateBasicFeeMsg|EmergencyFeeMsg
     */
    public static function select($aInfo) {
        if (Util::isDesignatedDriver($aInfo)) {
            return new DriverFeeMsg($aInfo);
        }

        return new CarFeeMsg($aInfo);
    }
}
