<?php
namespace PreSale\Logics\luxEstimatePrice\multiResponse\component\basicFeeMsg;

use BizLib\Constants;

/**
 * Class Common
 * @package PreSale\Logics\estimatePrice\mulitResponse\component\basicFeeMsg
 */
class Common
{

    /**
     * 格式化返回字段
     * @param array  $aFeeMsg   费用信息
     * @param array  $aResponse 返回结果
     * @param array  $aInfo     aInfo
     * @param string $sCarLevel 车型
     * @return array
     */
    public static function format($aFeeMsg, $aResponse, $aInfo, $sCarLevel) {
        $aResponse = self::_formatCommonMsg($aResponse, $aInfo, $sCarLevel);
        if (isset($aFeeMsg['fee_msg'])) {
            $aResponse['fee_msg'] = $aFeeMsg['fee_msg'];
        }

        if (isset($aFeeMsg['data_for_open']) && false !== $aFeeMsg['data_for_open']) {
            $aResponse['data_for_open'] = $aFeeMsg['data_for_open'];
        }

        if (isset($aFeeMsg['estimate_fee'])) {
            $aResponse['estimate_fee'] = $aFeeMsg['estimate_fee'];
        }

        return $aResponse;
    }


    /**
     * 格式化公共返回字段
     * @param array  $aResponse 返回结果
     * @param array  $aInfo     aInfo
     * @param string $sCarLevel 车型
     * @return array
     */
    private static function _formatCommonMsg($aResponse, $aInfo, $sCarLevel) {
        $aResponse['count_price_type'] = $aInfo['bill_info']['bills'][$sCarLevel]['count_price_type'] ?? Constants\Bill::PRICE_TYPE_UNKNOWN;

        return $aResponse;
    }
}
