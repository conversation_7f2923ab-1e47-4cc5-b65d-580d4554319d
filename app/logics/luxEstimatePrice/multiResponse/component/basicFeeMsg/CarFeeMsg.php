<?php
namespace PreSale\Logics\luxEstimatePrice\multiResponse\component\basicFeeMsg;

use Xiao<PERSON>\Apollo\Apollo as ApolloV2;
use BizLib\Utils;
use BizLib\Constants;
use BizLib\Utils\Language;
use Nuwa\ApolloSDK\Apollo as NuwaApollo;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\NumberHelper;
use BizLib\Utils\UtilHelper;
use PreSale\Logics\estimatePrice\multiResponse\Util;
use PreSale\Logics\estimatePrice\ParamsLogic;
use PreSale\Logics\estimatePrice\bill\CommonBillLogic;
use BizCommon\Constants\OrderNTuple;
use TripcloudCommon\Utils\Product as TripcloudProduct;

/**
 * Class CarFeeMsg
 * @package PreSale\Logics\estimatePrice\multiResponse\component\basicFeeMsg
 */
class CarFeeMsg
{
    const REPLACE_BY_TEMPLATE = 1;

    protected $_aInfo;

    //预估相关文案
    protected $_aConfig;

    //预估详情相关文案
    protected $_aEstimateDetail;

    //预估展示文案
    protected $_aEstimateText;

    // 国际化货币符号
    protected $_sCurrencySymbol;

    // 国际化"现金单位"
    protected $_sCurrencyUnit;

    protected $_sCarLevel;

    /**
     * NormalBasicFeeMsg constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo           = $aInfo;
        $this->_aConfig         = NuwaConfig::text('config_text', 'getEstimateFeeInfo');
        $this->_aEstimateDetail = NuwaConfig::text('config_text', 'pGetEstimateFee');
        $this->_aEstimateText   = NuwaConfig::text('config_text', 'mOrderEstimateNew');
        $sCurrency = $this->_aInfo['bill_info']['currency'] ?? '';
        list($sSymbol, $sUnit)  = Utils\Currency::getSymbolUnit($sCurrency, $this->_aInfo['order_info']);
        $this->_sCurrencySymbol = $sSymbol;
        $this->_sCurrencyUnit   = $sUnit;
        $this->_sCarLevel       = $this->_aInfo['order_info']['require_level'];
    }

    /**
     * 构建费用信息
     * @param array $aResponse aResponse
     * @return array
     */
    public function build($aResponse) {
        $aFeeMsg = $this->_getFeeMsg();
        return Common::format($aFeeMsg, $aResponse, $this->_aInfo, $this->_sCarLevel);
    }

    /**
     * 获取费用信息
     * @return array
     */
    protected function _getFeeMsg() {
        $aProductInfo           = $this->_aInfo['bill_info']['product_infos'][$this->_sCarLevel];
        $bIsShenGangFlatRate    = Utils\Horae::isShenGangFlatRate($this->_aInfo['order_info']['combo_type']);
        $bIsFlatRate            = Utils\Horae::isFlatRate($this->_aInfo['order_info']['combo_type']);
        $bIsMultiFactorFlatRate = Utils\Horae::isMultiFactorFlatRate($aProductInfo['combo_type'] ?? 0);
        $sEstimateFee           = $this->_aInfo['activity_info'][0]['estimate_fee'] ?? 0;

        //企业&open_api扩展字段
        $aDataForOpen = $this->_getDataForOpen();

        if (empty($this->_aInfo['bill_info'])) {
            $sFeeMsg = $this->_aConfig['default_estimate_fail'];
        } else {
            $sFeeMsg = $this->_formatFeeMsgWithSymbol('default_estimate_v2', $sEstimateFee);
        }

        return [
            'fee_msg'       => $sFeeMsg,
            'data_for_open' => $aDataForOpen,
            'estimate_fee'  => $sEstimateFee,
        ];
    }

    /**
     * 获取额外字段,除openApi和企业来源无此字段.
     *
     * @return array
     */
    private function _getDataForOpen() {
        if (!$this->_aInfo['common_info']['is_from_openapi'] && !$this->_aInfo['common_info']['is_from_b2b']) {
            return [];
        }

        $aBills     = $this->_aInfo['bill_info']['bills'][$this->_sCarLevel] ?? [];
        $fRedPacket = CommonBillLogic::formatDisplayLines($aBills['display_lines'])['red_packet']['value'] ?? 0.0;

        $aDataForOpen = [
            'highway_fee' => $aBills['highway_fee'] ?? 0,
            'red_packet'  => $fRedPacket > 0 ? $fRedPacket : 0.0,
        ];

        $aEstimateFixedFees = CommonBillLogic::formatEstimateFixedFees($aBills['estimate_fixed_fees']);
        foreach ($aEstimateFixedFees as $aFeeItem) {
            if (isset($aFeeItem['value']) && $aFeeItem['value'] > 0) {
                $aDataForOpen[$aFeeItem['name']] = $aFeeItem['value'];
            }
        }

        $aDataForOpen['member_privileges'] = $this->_getMemberPrivilegesForBusiness($aBills);

        return $aDataForOpen;
    }

    /**
     * 获取会员权益信息，给企业用
     *
     * @param array $aBills 账单信息
     *
     * @return array
     */
    private function _getMemberPrivilegesForBusiness($aBills) {
        $aMemberPrivileges = [];
        $aOrderInfo        = $this->_aInfo['order_info'];
        $aMember           = $this->_aInfo['passenger_info']['member_profile'];

        if ($aMember && $aBills['is_hit_member_capping'] && $aBills['member_dynamic_capping'] >= 0) {
            $fProtectFee = round($aBills['dynamic_price_without_member_capping'] - $aBills['member_dynamic_capping'], 2);
            if ($fProtectFee > 0) {
                $aMemberProtectFee          = [
                    'product_id'     => $aOrderInfo['product_id'],
                    'combo_type'     => $aOrderInfo['combo_type'],
                    'require_level'  => $aOrderInfo['require_level'],
                    'level_id'       => $aMember['level_id'],
                    'level_name'     => $aMember['level_name'],
                    'reason'         => $aMember['privileges']['dpa']['frontend']['reason'] ?? '',
                    'privilege_name' => $aMember['privileges']['dpa']['name'] ?? '',
                    'is_auto'        => $aMember['privileges']['dpa']['is_auto'],
                    'protection_fee' => $fProtectFee,
                ];
                $aMemberPrivileges['dpa'][] = $aMemberProtectFee;
                $aMemberPrivileges['pt_version'] = (int)$aMember['pt_version'];
            }
        }

        return $aMemberPrivileges;
    }

    /**
     * 格式化fee_msg
     * @param string $sKey         sKey
     * @param string $sEstimateFee sEstimateFee
     * @return mixed
     */
    protected function _formatFeeMsgWithSymbol($sKey, $sEstimateFee) {
        $sFeeMsg = Language::replaceTag(
            $this->_aConfig[$sKey],
            [
                'currency_symbol' => $this->_sCurrencySymbol,
                'num'             => NumberHelper::numberFormatDisplay($sEstimateFee),
                'currency_unit'   => $this->_sCurrencyUnit,
            ]
        );

        return $sFeeMsg;
    }
}
