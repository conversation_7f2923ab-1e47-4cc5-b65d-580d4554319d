<?php

namespace PreSale\Logics\luxEstimatePrice\multiResponse\component;

use BizCommon\Models\Mis\CarLevel;

/**
 * Class CarLevelInfo
 * @package PreSale\Logics\luxEstimatePrice\multiResponse\component
 */
class CarLevelInfo
{
    /**
     * @var self
     */
    private static $_oInstance;

    /**
     * @var array
     */
    private $_aCarLevelInfo;

    /**
     * GuideInfo constructor.
     * @return void
     */
    public function loadCarLevelInfo() {
        if (!empty($this->_aCarLevelInfo)) {
            return;
        }

        $aCarLevelInfo = (new CarLevel())->getListWithRecentImg();
        if (!empty($aCarLevelInfo)) {
            $this->_aCarLevelInfo = array_column($aCarLevelInfo, null, 'level_id');
        }

        return;
    }

    /**
     * @param array $sCarLevel sCarLevel
     * @return array
     */
    public function getCarLevelInfo($sCarLevel) {
        if (!isset($this->_aCarLevelInfo[$sCarLevel])) {
            return [];
        }

        return $this->_aCarLevelInfo[$sCarLevel];
    }

    /**
     * @return null|CarLevelInfo
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }
}
