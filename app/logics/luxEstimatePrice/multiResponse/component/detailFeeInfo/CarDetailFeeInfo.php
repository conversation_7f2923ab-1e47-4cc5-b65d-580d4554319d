<?php
namespace PreSale\Logics\luxEstimatePrice\multiResponse\component\detailFeeInfo;

use BizLib\Utils;
use BizLib\Config as NuwaConfig;
use PreSale\Logics\estimatePrice\multiResponse\MainDataRepo;
use PreSale\Models\fee\FeeDetailTemplate;

/**
 * Class NormalDetailFeeInfo
 * @package PreSale\Logics\estimatePrice\multiResponse\component\detailFeeInfo
 */
class CarDetailFeeInfo
{
    protected $_aInfo;

    protected $_aBillInfo;

    protected $_sCarLevel;

    protected $_aPositionInfo;

    //预估展示文案
    protected $_aEstimateText;

    // 国际化货币符号
    protected $_sCurrencySymbol;

    // 国际化"现金单位"
    protected $_sCurrencyUnit;

    /**
     * NormalDetailFeeInfo constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo         = $aInfo;
        $this->_sCarLevel     = $aInfo['order_info']['require_level'];
        $this->_aBillInfo     = $aInfo['bill_info']['bills'][$this->_sCarLevel] ?? [];
        $this->_aEstimateText = NuwaConfig::text('config_text', 'mOrderEstimateNew');
        $sCurrency            = $aInfo['bill_info']['currency'] ?? '';
        list($sSymbol, $sUnit)  = Utils\Currency::getSymbolUnit($sCurrency, $aInfo['order_info']);
        $this->_sCurrencySymbol = $sSymbol;
        $this->_sCurrencyUnit   = $sUnit;
        $this->_aPositionInfo   = [
            'driver_metre'  => $this->_aInfo['bill_info']['driver_metre'] ?? 0,
            'driver_minute' => $this->_aInfo['bill_info']['driver_minute'] ?? 0,
        ];
    }

    /**
     * 构建费用详情页信息
     * @param array $aResponse aResponse
     * @return array
     */
    public function build($aResponse) {
        $aDetailPageInfo = $this->_getDetailPageInfo();
        return Common::format($aDetailPageInfo, $aResponse, $this->_aInfo, $this->_aBillInfo);
    }


    /**
     * 获取费用详情页信息
     * @return array
     */
    private function _getDetailPageInfo() {
        $aDetailPageInfo['currency_symbol'] = $this->_sCurrencySymbol;
        $aDetailPageInfo['currency_unit']   = $this->_sCurrencyUnit;
        $aDetailPageInfo['combo_info']      = Common::getComboInfo((int)($this->_aInfo['order_info']['combo_type']), $this->_aBillInfo, $this->_aInfo) ?: false;

        $aPriceDetail       = $this->_getPriceDetail();
        $aAdditiveAttribute = $this->_getAdditiveAttribute();
        $aDetailPageInfo['fee_detail'] = Common::getBillList($aPriceDetail, $aAdditiveAttribute, $this->_aPositionInfo, $this->_aInfo);

        $aCoupon = $this->_aInfo['activity_info'][0]['estimate_detail'] ?? [];
        if (isset($aCoupon['title'], $aCoupon['value'])) {
            $aDetailPageInfo['fee_detail'][] = [
                'title' => $aCoupon['title'],
                'value' => '-'.$aCoupon['value'],
                'color' => $aCoupon['color'],
            ];
        }

        //预估详情页透传参数 - 计价token
        $priceTokensWithCarLevel        = $this->_aInfo['bill_info']['concrete_product_ids'];
        $aDetailPageInfo['price_token'] = array_shift($priceTokensWithCarLevel);

        //费用明细透传字段
        $aDetailPageInfo['estimated_fee_detail'] = $this->_getEstimateFeeDetail();

        // 预估新标单，是否可以跳转费用详情页
        $aDetailPageInfo['disable_detailpage'] = Common::getDisableDetailPage($this->_aInfo);

        $iComboType = $this->_aInfo['bill_info']['product_infos'][$this->_sCarLevel]['combo_type'] ?? 0;

        //data_for_detail_page sum
        $aDetailPageInfo['sum'] = Common::getSumInfo($this->_aInfo);

        return $aDetailPageInfo;
    }


    /**
     * 预估费用详细
     * @return array
     */
    private function _getPriceDetail() {
        //不拼车预估费用详细
        $aPriceDetail = [
            'start_price'           => $this->_aBillInfo['start_price'] ?? 0,
            'normal_price'          => $this->_aBillInfo['normal_price'] ?? 0,
            'time_price'            => $this->_aBillInfo['time_price'] ?? 0,
            'night_price'           => $this->_aBillInfo['night_price'] ?? 0,
            'empty_price'           => $this->_aBillInfo['empty_price'] ?? 0,
            'low_speed_price'       => $this->_aBillInfo['low_speed_price'] ?? 0,
            'highway_fee'           => $this->_aBillInfo['highway_fee'] ?? 0,
            'designated_driver_fee' => $this->_aBillInfo['designated_driver_fee'] ?? 0, //指定司务员费用
            'limit_fee'             => !empty($this->_aBillInfo['is_limit_fee']) ? $this->_aBillInfo['limit_fee'] : 0,
            'taxes_fee'             => $this->_aBillInfo['taxes_fee'] ?? 0,
            'fixed_preferential'    => $this->_aBillInfo['fixed_preferential'] ?? 0,
            'booking_fee'           => $this->_aBillInfo['booking_fee'] ?? 0,
        ];

        $aCommonPriceDetail = Common::getCommonPriceDetail($this->_aBillInfo);

        return $aPriceDetail + $aCommonPriceDetail;
    }


    /**
     * 获取附加属性
     * @return array
     */
    private function _getAdditiveAttribute() {
        $aAdditiveAttribute       = [
            'package'          => $this->_aBillInfo['package'] ?? 0,
            'package_distance' => $this->_aBillInfo['package_distance'] ?? 0,
            'package_time'     => $this->_aBillInfo['package_time'] ?? 0,
            'distance_result'  => $this->_aBillInfo['distance_result'] ?? [], //分时段计价,里程详情
            'time_result'      => $this->_aBillInfo['time_result'] ?? [], //分时段计价,时间详情
            'low_speed_result' => $this->_aBillInfo['low_speed_result'] ?? [], //分时段计价低速费详情
        ];
        $aCommonAdditiveAttribute = Common::getCommonAdditiveAttribute($this->_aBillInfo);
        return $aAdditiveAttribute + $aCommonAdditiveAttribute;
    }

    /**
     * 获取预估费用详情
     * @return array
     */
    private function _getEstimateFeeDetail() {
        //非拼车
        $aFeeDetailWithCarLevel = $this->_aInfo['bill_info']['fees_detail_infos'];
        $aFeeDetail = array_shift($aFeeDetailWithCarLevel);
        if (empty($aFeeDetail)) {
            return [];
        }

        if (!empty($this->_aInfo['bill_info']['estimate_id'])) {
            $aFeeDetail['estimate_id'] = $this->_aInfo['bill_info']['estimate_id'];
        }

        if (isset($this->_aInfo['price_extra']['is_default'])
            && !empty($this->_aInfo['price_extra']['is_default'])
            && !empty($aFeeDetail)
            && is_array($aFeeDetail)
        ) {
            $aFeeDetail['is_default']        = $this->_aInfo['price_extra']['is_default'];
            $aFeeDetail['user_status']       = $this->_aInfo['price_extra']['user_status'] ?? -1;
            $aFeeDetail['is_default_action'] = $this->_aInfo['price_extra']['is_default_action'] ?? -1;
        }

        $oFeeDetailTemplate = new FeeDetailTemplate();
//        $sShakeFlag         = Common::getShakeFlag($this->_aBillInfo);
        $oFeeDetailTemplate->setTemplate($aFeeDetail);
        $aEstimateFeeDetail = $oFeeDetailTemplate->getFeeDetail(
            $this->_aInfo['activity_info'][0]['estimate_detail']['amount']
        );

        return $aEstimateFeeDetail;
    }



    /**
     * 获取慢必赔信息.
     * @param array $aEnsureInfo aEnsureInfo
     * @return array
     */
    private function _getEnsureInfo(array $aEnsureInfo) {
        $aResult = [];
        if (isset($aEnsureInfo['act_info'])) {
            $aResult = [
                'title' => $aEnsureInfo['act_info']['title'],
                'msg'   => $aEnsureInfo['act_info']['msg'],
                'url'   => $aEnsureInfo['act_info']['url'],
            ];
        }

        return $aResult;
    }
}
