<?php
namespace PreSale\Logics\luxEstimatePrice\multiResponse\component\detailFeeInfo;

use PreSale\Logics\luxEstimatePrice\multiResponse\Util;

/**
 * Class Handler
 * @package PreSale\Logics\estimatePrice\multiResponse\component\detailFeeInfo
 */
class Handler
{
    /**
     * @param array $aInfo aInfo
     * @return AnycarDetailFeeInfo|CarpoolDetailFeeInfo|NormalDetailFeeInfo|SpecialRateDetailFeeInfo
     */
    public static function select($aInfo) {
        if (Util::isDesignatedDriver($aInfo)) {
            return new DriverDetailFeeInfo($aInfo);
        }

        return new CarDetailFeeInfo($aInfo);
    }
}
