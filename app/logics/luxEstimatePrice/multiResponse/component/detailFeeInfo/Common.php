<?php
namespace PreSale\Logics\luxEstimatePrice\multiResponse\component\detailFeeInfo;

use BizLib\Utils;
use BizLib\Constants;
use BizLib\Utils\TimeHelper;
use <PERSON><PERSON>\Apollo\Apollo as ApolloV2;
use BizLib\Utils\NumberHelper;
use BizLib\Utils\Language;
use BizLib\Config as NuwaConfig;
use BizCommon\Models\Mis\CarLevel;
use BizCommon\Models\Order\Order;
use PreSale\Logics\commonAbility\AdjustPriceLogic;
use PreSale\Logics\estimatePrice\bill\CommonBillLogic;
use PreSale\Logics\estimatePrice\multiResponse\MainDataRepo;
use PreSale\Logics\estimatePrice\multiResponse\Util;

/**
 * Class Common
 * @package PreSale\Logics\estimatePrice\multiResponse\component\detailFeeInfo
 */
class Common
{
    const TIME_OF_USE_COLOR = '#999'; // 颜色
    const ENABLE_PRICE_INFO_DESC_VERSION = '5.2.46';
    const DETAIL_PAGE_DEFAULT            = 0; //老逻辑，端上判断
    const DETAIL_PAGE_CLICK     = 1; //可跳转预估详情页
    const DETAIL_PAGE_NOT_CLICK = 2; //不可跳转预估详情页
    const SIXTY    = 60; //时间换算
    const THOUSAND = 1000; //米和千米换算

    /**
     * 格式化返回信息 (json字符串，key顺序和改造前要保持一致，否则报diff。。。)
     * @param array $aDetailPageInfo 费用详情信息
     * @param array $aResponse       返回信息
     * @param array $aInfo           aInfo
     * @param array $aBillInfo       账单信息
     * @return array
     */
    public static function format($aDetailPageInfo, $aResponse, $aInfo, $aBillInfo) {
        $aResponse['data_for_detailpage']['business_id'] = $aInfo['common_info']['business_id'];
        if (isset($aDetailPageInfo['currency_symbol'])) {
            $aResponse['data_for_detailpage']['currency_symbol'] = $aDetailPageInfo['currency_symbol'];
        }

        if (isset($aDetailPageInfo['currency_unit'])) {
            $aResponse['data_for_detailpage']['currency_unit'] = $aDetailPageInfo['currency_unit'];
        }

        $aResponse['data_for_detailpage']['count_price_type'] = $aResponse['count_price_type'];
        $aResponse['data_for_detailpage']['limit_fee_tips']   = self::_getLimitFeeTips($aBillInfo, $aInfo);

        if (isset($aDetailPageInfo['combo_info'])) {
            $aResponse['data_for_detailpage']['combo_info'] = false === $aDetailPageInfo['combo_info'] ? null : $aDetailPageInfo['combo_info'];
        }

        if (isset($aDetailPageInfo['fee_detail'])) {
            $aResponse['data_for_detailpage']['fee_detail'] = $aDetailPageInfo['fee_detail'];
        }

        $aResponse['data_for_detailpage']['departure_time'] = $aInfo['order_info']['departure_time'];
        $aResponse['data_for_detailpage']['flng']           = $aInfo['order_info']['from_lng'];
        $aResponse['data_for_detailpage']['flat']           = $aInfo['order_info']['from_lat'];
        $aResponse['data_for_detailpage']['lang']           = $aInfo['common_info']['lang'];

        if (isset($aDetailPageInfo['price_token'])) {
            $aResponse['data_for_detailpage']['price_token'] = $aDetailPageInfo['price_token'];
        }

        if (isset($aDetailPageInfo['estimated_fee_detail'])) {
            $aResponse['data_for_detailpage']['estimated_fee_detail']   = $aDetailPageInfo['estimated_fee_detail'];
            $aResponse['data_for_detailpage']['use_estimate_detail_v3'] = 1;
        }

        if (isset($aDetailPageInfo['company_carpool_open'])) {
            $aResponse['data_for_detailpage']['company_carpool_open'] = $aDetailPageInfo['company_carpool_open'];
        }

        if (isset($aDetailPageInfo['cap_price_tip'])) {
            $aResponse['data_for_detailpage']['cap_price_tip'] = $aDetailPageInfo['cap_price_tip'];
        }

        if (isset($aDetailPageInfo['ensure_info'])) {
            $aResponse['data_for_detailpage']['ensure_info'] = false === $aDetailPageInfo['ensure_info'] ? null : $aDetailPageInfo['ensure_info'];
        }

        if (isset($aDetailPageInfo['sum'])) {
            $aResponse['data_for_detailpage']['sum'] = $aDetailPageInfo['sum'];
        }

        $aResponse['data_for_detailpage'] = json_encode($aResponse['data_for_detailpage']);
        $aResponse['data_for_detailpage'] = AdjustPriceLogic::getInstance()->addAdjustPriceInfoInFeeDetail($aResponse['data_for_detailpage'], $aInfo);

        if (isset($aDetailPageInfo['disable_detailpage']) && false !== $aDetailPageInfo['disable_detailpage']) {
            $aResponse['disable_detailpage'] = $aDetailPageInfo['disable_detailpage'];
        }

        return $aResponse;
    }

    /**
     * 获取最低消费提示文案.
     * @param array $aBillInfo 账单信息
     * @param array $aInfo     aInfo
     * @return string
     */
    private static function _getLimitFeeTips(array $aBillInfo, array $aInfo) {
        $sLimitFeeTips = '';

        $sCurrency = $aInfo['bill_info']['currency'] ?? '';
        list($sSymbol, $sUnit) = Utils\Currency::getSymbolUnit($sCurrency, $aInfo['order_info']);
        $aEstimateDetail       = NuwaConfig::text('config_text', 'pGetEstimateFee');

        if ($aBillInfo['is_limit_fee'] && $aBillInfo['is_appointment']) {
            $aCarType = CarLevel::getInstance()->getCarNameByLevelId($aInfo['order_info']['require_level']);

            $sLimitFeeTips = Language::replaceTag(
                $aEstimateDetail['limit_fee_tips_v2'],
                [
                    'c_level_name'    => $aCarType['require_level_txt'],
                    'currency_symbol' => $sSymbol,
                    'total_fee'       => NumberHelper::numberFormatDisplay((int)($aBillInfo['total_fee'])),
                    'currency_unit'   => $sUnit,
                ]
            );
        } elseif ($aBillInfo['is_limit_fee']) {
            $sLimitFeeTips = $aEstimateDetail['limit_fee_tips1'];
        }

        return $sLimitFeeTips;
    }


    /**
     * 获取公共price_detail字段
     * @param array $aBillInfo 账单信息
     * @return array
     */
    public static function getCommonPriceDetail($aBillInfo) {
        $fRedPacket = CommonBillLogic::formatDisplayLines($aBillInfo['display_lines'])['red_packet']['value'] ?? 0.0;
        $aCommonEstimatePriceInfo = [
            'total_fee'     => $aBillInfo['cap_price'] ?? 0,
            'red_packet'    => $fRedPacket,
            'br_car_fee'    => $aBillInfo['br_car_fee'] ?? 0,
            'dynamic_price' => $aBillInfo['dynamic_diff_price'] ?? 0,
        ];

        return $aCommonEstimatePriceInfo;
    }


    /**
     * 获取套餐信息.
     * @param int   $iComboType 套餐类型
     * @param array $aBillInfo  账单信息
     * @param array $aInfo      aInfo
     * @return array
     */
    public static function getComboInfo(int $iComboType, array $aBillInfo, array $aInfo) {
        $aComboInfo      = [];
        $aUnit           = Language::getUnit();
        $sLengthUnit     = $aUnit['length'][0];
        $aEstimateDetail = NuwaConfig::text('config_text', 'pGetEstimateFee');

        if (Order::TYPE_COMBO_RENTED == $iComboType) {
            $aComboInfo['scene_type'] = Constants\Horae::HORAE_SCENE_TYPE_RENTED;
            $aComboInfo['combo_tips'] = Language::replaceTag(
                $aEstimateDetail['exceed_rent_combo_tips'],
                [
                    'time'        => round((float)($aBillInfo['package_time']) / self::SIXTY, 1),
                    'distance'    => NumberHelper::getLocalFormatNumber((float)($aBillInfo['package_distance']), $aInfo['order_info']['product_id']),
                    'length_unit' => $sLengthUnit,
                ]
            );
        }

        return $aComboInfo;
    }


    /**
     * 获取公共AdditiveAttribute字段
     * @param array $aBillInfo 账单信息
     * @return array
     */
    public static function getCommonAdditiveAttribute($aBillInfo) {
        $aCommonAdditiveAttribute = [
            'is_hit_dynamic_capping' => $aBillInfo['is_hit_dynamic_capping'] ?? false,
            'dynamic_times'          => $aBillInfo['dynamic_info']['dynamic_times'] ?? 0,
            'if_use_times'           => $aBillInfo['dynamic_info']['if_use_times'] ?? 0,
        ];

        return $aCommonAdditiveAttribute;
    }

    /**
     * 获取费用明细.
     *
     * @param array $aPriceDetail       aPriceDetail
     * @param array $aAdditiveAttribute aAdditiveAttribute
     * @param array $aPositionInfo      aPositionInfo
     * @param array $aInfo              aInfo
     * @return array
     */
    public static function getBillList(array $aPriceDetail, array $aAdditiveAttribute, array $aPositionInfo, $aInfo) {
        $aFeeDetail    = [];
        $aDynamicTitle = NuwaConfig::text('config_text', 'bill_title');
        $aEstimateText = NuwaConfig::text('config_text', 'mOrderEstimateNew');
        $sCurrency     = $aInfo['bill_info']['currency'] ?? '';
        list($sSymbol, $sUnit) = Utils\Currency::getSymbolUnit($sCurrency, $aInfo['order_info']);

        foreach ($aPriceDetail as $sKey => $fAmount) {
            if ('dynamic_price' == $sKey && $fAmount < 0) {
                $fDiscountValue = intval(round(abs($aAdditiveAttribute['dynamic_times']), 2) * 100);
                if (1 != $aAdditiveAttribute['if_use_times']) {
                    $aFeeDetail[] = [
                        'title' => $aDynamicTitle['discount'],
                        'value' => self::getFeeValue($fAmount, $aEstimateText, $sSymbol, $sUnit),
                        'color' => '#ff9a03',
                    ];
                } elseif ($aAdditiveAttribute['is_hit_dynamic_capping']) {
                    $aFeeDetail[] = [
                        'title' => Language::replaceTag(
                            $aDynamicTitle['percent_discount_capping_v2'],
                            [
                                'currency_symbol' => $sSymbol,
                                'discount'        => NumberHelper::getLocalFormatNumber($fDiscountValue, $aInfo['order_info']['product_id']),
                                'amount'          => NumberHelper::numberFormatDisplay(abs($fAmount)),
                                'currency_unit'   => $sUnit,
                            ]
                        ),
                        'value' => self::getFeeValue($fAmount, $aEstimateText, $sSymbol, $sUnit),
                        'color' => '#ff9a03',
                    ];
                } elseif (Utils\Product::getProductConfig('negative_dynamic_times', $aInfo['order_info'])) {
                    $aFeeDetail[] = [
                        'title' => Language::replaceTag(
                            $aDynamicTitle['dynamic_discount'],
                            [
                                'discount' => NumberHelper::getLocalFormatNumber($fDiscountValue, $aInfo['order_info']['product_id']),
                            ]
                        ),
                        'value' => self::getFeeValue($fAmount, $aEstimateText, $sSymbol, $sUnit),
                        'color' => '#ff9a03',
                    ];
                } else {
                    $aFeeDetail[] = [
                        'title' => Language::replaceTag(
                            $aDynamicTitle['percent_discount'],
                            [
                                'discount' => NumberHelper::getLocalFormatNumber($fDiscountValue, $aInfo['order_info']['product_id']),
                            ]
                        ),
                        'value' => self::getFeeValue($fAmount, $aEstimateText, $sSymbol, $sUnit),
                        'color' => '#ff9a03',
                    ];
                }
            }

            if ($fAmount <= 0) {
                continue;
            }

            if ('dynamic_price' == $sKey) {
                if (1 == $aAdditiveAttribute['if_use_times'] && $aAdditiveAttribute['dynamic_times'] > 0
                    && empty($aAdditiveAttribute['is_hit_dynamic_capping'])
                ) {
                    $aFeeDetail[] = [
                        'title' => Language::replaceTag(
                            self::_getTitle($aInfo, $sKey) . $aEstimateText['estimate_multi_title'],
                            [
                                'times' => NumberHelper::getLocalFormatNumber($aAdditiveAttribute['dynamic_times'], $aInfo['order_info']['product_id']),
                            ]
                        ),
                        'value' => self::getFeeValue($fAmount, $aEstimateText, $sSymbol, $sUnit),
                        'color' => '#ff9a03',
                    ];
                } else {
                    $aFeeDetail[] = [
                        'title' => self::_getTitle($aInfo, $sKey, [], $aAdditiveAttribute['is_hit_dynamic_capping']),
                        'value' => self::getFeeValue($fAmount, $aEstimateText, $sSymbol, $sUnit),
                        'color' => '#ff9a03',
                    ];
                }
            } elseif ('start_price' == $sKey && $aAdditiveAttribute['package'] > 0) {
                $aFeeDetail[] = [
                    'title' => Language::replaceTag(
                        self::_getTitle($aInfo, $sKey) . $aEstimateText['estimate_package'],
                        [
                            'time'        => NumberHelper::getLocalFormatNumber(round($aAdditiveAttribute['package_time'] / 60, 1), $aInfo['order_info']['product_id']),
                            'distance'    => NumberHelper::getLocalFormatNumber($aAdditiveAttribute['package_distance'], $aInfo['order_info']['product_id']),
                            'length_unit' => Language::getUnit()['length'][0],
                        ]
                    ),
                    'value' => self::getFeeValue($fAmount, $aEstimateText, $sSymbol, $sUnit),
                    'color' => '#666',
                ];
            } elseif ($aTimeOfUse = self::_getTimeOfUse(array_merge($aPriceDetail, $aAdditiveAttribute), $sKey, $aInfo)) {
                //  分时段计价,添加子项:distance_result
                $aFeeDetail[] = [
                    'title'    => self::_getTitle($aInfo, $sKey, $aPositionInfo, false, $aPriceDetail),
                    'value'    => self::getFeeValue($fAmount, $aEstimateText, $sSymbol, $sUnit),
                    'color'    => '#666',
                    'children' => $aTimeOfUse,
                ];
            } else {
                //巴西预估价不显示里程信息
                if ('normal_price' == $sKey && !Utils\Product::getProductConfig(
                    'estimate_normal_price',
                    $aInfo['order_info']
                )
                ) {
                    continue;
                }

                $aFeeDetail[] = [
                    'title' => self::_getTitle($aInfo, $sKey, $aPositionInfo),
                    'value' => self::getFeeValue($fAmount, $aEstimateText, $sSymbol, $sUnit),
                    'color' => '#666',
                ];
            }
        }

        return $aFeeDetail;
    }


    /**
     * 获取费用项 例如:10.5元.
     * @param float  $fAmount         amount
     * @param array  $aEstimateText   aEstimateText
     * @param string $sCurrencySymbol sCurrencySymbol
     * @param string $sCurrencyUnit   sCurrencyUnit
     * @return mixed
     */
    public static function getFeeValue(float $fAmount, $aEstimateText, $sCurrencySymbol, $sCurrencyUnit) {
        return Language::replaceTag(
            $aEstimateText['fee_info_v2'],
            [
                'currency_symbol' => $sCurrencySymbol,
                'fee'             => NumberHelper::numberFormatDisplay($fAmount),
                'currency_unit'   => $sCurrencyUnit,
            ]
        );
    }



    /**
     * 设置定制化服务费用明细.
     * @param array $aBillInfo aBillInfo
     * @param array $aInfo     aInfo
     * @return array
     */
    public static function getCustomBillList($aBillInfo, $aInfo) {
        $aEstimateText = NuwaConfig::text('config_text', 'mOrderEstimateNew');
        $sCurrency     = $aInfo['bill_info']['currency'] ?? '';
        list($sSymbol, $sUnit) = Utils\Currency::getSymbolUnit($sCurrency, $aInfo['order_info']);

        if (!isset($aBillInfo['customized_service_result']) || !isset($aBillInfo['customized_service_result']['service_fee'])) {
            return [];
        }

        $aCustomInfo = $aBillInfo['customized_service_result'];
        if (empty($aCustomInfo) || !is_array($aCustomInfo) || !isset($aCustomInfo['items'])) {
            return [];
        }

        if (empty($aCustomInfo['items']) || !is_array($aCustomInfo['items']) || !isset($aCustomInfo['items'][0]['id'])) {
            return [];
        }

        $aFeeDetail = [];
        foreach ($aCustomInfo['items'] as $aCustom) {
            if (empty($aCustom['count'])) {
                continue;
            }

            $fAmount = $aCustom['cost'];
            if ($aCustom['count'] > 1) {
                $sTitle = $aCustom['title'].'('.$aCustom['count'].$aCustom['unit'].')';
            } else {
                $sTitle = $aCustom['title'];
            }

            $aFeeDetail[] = [
                'title'  => $sTitle,
                'value'  => self::getFeeValue($fAmount, $aEstimateText, $sSymbol, $sUnit),
                'color'  => '#666',
                'amount' => $fAmount,
            ];
        }

        return $aFeeDetail;
    }



    /**
     * 获取摇一摇标识.
     * @param array $aBillInfo aBillInfo
     * @return string 摇一摇上次预估价格
     */
//    public static function getShakeFlag($aBillInfo) {
//        $sShakeFlag = '';
//        //摇一摇
//        if (isset($aBillInfo['shake_flag'])) {
//            $sShakeFlag = $aBillInfo['shake_flag'];
//        }
//
//        return $sShakeFlag;
//    }


    /**
     * 新版根据此字段判断是是否可跳转费用详情页.
     * @param array $aInfo aInfo
     * @return bool|array
     */
    public static function getDisableDetailPage($aInfo) {
        if (version_compare((string)($aInfo['common_info']['app_version']), self::ENABLE_PRICE_INFO_DESC_VERSION) < 0) {
            return false;
        }

        $iDisableDetailpage = self::DETAIL_PAGE_DEFAULT;

        //出租车需要特殊处理
        if (Utils\Product::isUniTaxi($aInfo['order_info']['product_id'])) {
            if (Util::checkUnioneShowFeeInfo($aInfo)) {
                $aFeeDetailWithCarLevel = $aInfo['bill_info']['fees_detail_infos'];
                $aFeeDetail         = array_shift($aFeeDetailWithCarLevel);
                $iDisableDetailpage = empty($aFeeDetail) ? self::DETAIL_PAGE_NOT_CLICK : self::DETAIL_PAGE_CLICK;
            }
        }

        return $iDisableDetailpage;
    }


    /**
     * 使用新版费用信息开关
     * @param array $aInfo aInfo
     * @return bool
     */
    public static function isNewPriceInfoDescSwitch($aInfo) {

        $aPolloParam = [
            'key'       => $aInfo['passenger_info']['pid'],
            'phone'     => $aInfo['passenger_info']['phone'],
            'city'      => $aInfo['order_info']['area'],
            'car_level' => $aInfo['order_info']['require_level'],
        ];

        $oUpgradeApollo = (new ApolloV2())->featureToggle('gs_upgrade_price_desc', $aPolloParam)->allow();

       // $oH5NotDelayApollo = (new ApolloV2())->featureToggle('gs_h5_not_delay', $aPolloParam)->allow();
        $oH5NotDelayApollo = true;
        if ($oUpgradeApollo && $oH5NotDelayApollo) {
            return true;
        }

        return false;
    }

    /**
     * 获取预估价展示内容.
     * @param array $aInfo aInfo
     * @return array
     */
    public static function getSumInfo($aInfo) {
        $sTitleKey     = 'price_estimate_title';
        $fFeeAmount    = (float)($aInfo['activity_info'][0]['discount_fee']);
        $aEstimateText = NuwaConfig::text('config_text', 'mOrderEstimateNew');

        // 多因素一口价
        $aProductInfo    = $aInfo['bill_info']['product_infos'] ?? [];
        $iComboType      = $aProductInfo[$aInfo['order_info']['require_level']]['combo_type'] ?? 0;
        $iCountPriceType = $aInfo['bill_info']['bills'][$aInfo['order_info']['require_level']]['count_price_type'] ?? 0;
        if (Utils\Horae::isMultiFactorFlatRateV2(['combo_type' => $iComboType, 'count_price_type' => $iCountPriceType])) {
            $sTitleKey = 'price_estimate_title_for_flat_rate';
        }

        return array(
            'title' => $aEstimateText[$sTitleKey],
            'value' => $fFeeAmount,
            'color' => '#ff9a03',
        );
    }

    /**
     * 获取分时计价数据.
     *
     * @param array  $aBillInfo 账单返回预估费用详情
     * @param string $sKey      计价类别：里程费、低速费、时间费
     * @param array  $aInfo     aInfo
     * @return array
     */
    private static function _getTimeOfUse($aBillInfo, $sKey, $aInfo) {
        $aKeyMap = [
            'normal_price'    => 'distance_result',
            'time_price'      => 'time_result',
            'low_speed_price' => 'low_speed_result',
        ];
        if (empty($aBillInfo[$aKeyMap[$sKey]]['intervals'])) {
            return [];
        }

        $aRet          = [];
        $aBill         = $aBillInfo[$aKeyMap[$sKey]];
        $aEstimateText = NuwaConfig::text('config_text', 'mOrderEstimateNew');
        $sCurrency     = $aInfo['bill_info']['currency'] ?? '';
        list($sSymbol, $sUnit) = Utils\Currency::getSymbolUnit($sCurrency, $aInfo['order_info']);
        $sCurrencySymbol       = $sSymbol;
        $sCurrencyUnit         = $sUnit;
        $aUnit       = Language::getUnit();
        $sLengthUnit = $aUnit['length'][0];
        $sMinuteUnit = $aUnit['time'][1];
        switch ($sKey) {
            case 'normal_price':
                $sTemplate = self::_getTimeIntervalChargeDateTemplate($aBill['intervals'], $aEstimateText['bill_date']);
                foreach ($aBill['intervals'] as $aPart) {
                    $sStartDatePart = self::_getTimeIntervalChargeFormatDate(TimeHelper::getLocalTimeByUtcOffset($aPart['begin_time']), $sTemplate);
                    $aRet[]         = [
                        'title' => sprintf(
                            "%s - %s(%s{$sLengthUnit})",
                            $sStartDatePart,
                            date('H:i', strtotime(TimeHelper::getLocalTimeByUtcOffset($aPart['end_time']))),
                            NumberHelper::getLocalFormatNumber(round($aPart['distance'], 2), $aInfo['order_info']['product_id'])
                        ),
                        'value' => sprintf(
                            "{$sCurrencySymbol}%s{$sCurrencyUnit}",
                            NumberHelper::numberFormatDisplay($aPart['fee'])
                        ),
                        'color' => Common::TIME_OF_USE_COLOR,
                    ];
                }

                if ($aBill['normal_fee'] > 0) {
                    $aRet[] = [
                        'title' => sprintf(
                            "{$aEstimateText['bill_normal_time_period']}(%s{$sLengthUnit})",
                            NumberHelper::getLocalFormatNumber(round($aBill['normal_distance'], 2), $aInfo['order_info']['product_id'])
                        ),
                        'value' => sprintf(
                            "{$sCurrencySymbol}%s{$sCurrencyUnit}",
                            NumberHelper::numberFormatDisplay($aBill['normal_fee'])
                        ),
                        'color' => Common::TIME_OF_USE_COLOR,
                    ];
                }
                break;
            case 'time_price':
                $sTemplate = self::_getTimeIntervalChargeDateTemplate($aBill['intervals'], $aEstimateText['bill_date']);
                foreach ($aBill['intervals'] as $aPart) {
                    $sStartDatePart = self::_getTimeIntervalChargeFormatDate(TimeHelper::getLocalTimeByUtcOffset($aPart['begin_time']), $sTemplate);
                    $aRet[]         = [
                        'title' => sprintf(
                            "%s - %s(%d{$sMinuteUnit})",
                            $sStartDatePart,
                            date('H:i', strtotime(TimeHelper::getLocalTimeByUtcOffset($aPart['end_time']))),
                            NumberHelper::getLocalFormatNumber($aPart['time'], $aInfo['order_info']['product_id'])
                        ),
                        'value' => sprintf("{$sCurrencySymbol}%s{$sCurrencyUnit}", NumberHelper::numberFormatDisplay($aPart['fee'])),
                        'color' => Common::TIME_OF_USE_COLOR,
                    ];
                }

                if ($aBill['normal_fee'] > 0) {
                    $aRet[] = [
                        'title' => sprintf(
                            "{$aEstimateText['bill_normal_time_period']}(%d{$sMinuteUnit})",
                            NumberHelper::getLocalFormatNumber($aBill['normal_time'], $aInfo['order_info']['product_id'])
                        ),
                        'value' => sprintf("{$sCurrencySymbol}%s{$sCurrencyUnit}", NumberHelper::numberFormatDisplay($aBill['normal_fee'])),
                        'color' => Common::TIME_OF_USE_COLOR,
                    ];
                }
                break;
            case 'low_speed_price':
                $sTemplate = self::_getTimeIntervalChargeDateTemplate($aBill['intervals'], $aEstimateText['bill_date']);
                foreach ($aBill['intervals'] as $aPart) {
                    $sStartDatePart = self::_getTimeIntervalChargeFormatDate(TimeHelper::getLocalTimeByUtcOffset($aPart['begin_time']), $sTemplate);
                    $aRet[]         = [
                        'title' => sprintf(
                            "%s - %s(%d{$sMinuteUnit})",
                            $sStartDatePart,
                            date('H:i', strtotime(TimeHelper::getLocalTimeByUtcOffset($aPart['end_time']))),
                            NumberHelper::getLocalFormatNumber($aPart['time'], $aInfo['order_info']['product_id'])
                        ),
                        'value' => sprintf("{$sCurrencySymbol}%s{$sCurrencyUnit}", NumberHelper::numberFormatDisplay($aPart['fee'])),
                        'color' => Common::TIME_OF_USE_COLOR,
                    ];
                }

                if ($aBill['normal_fee'] > 0) {
                    $aRet[] = [
                        'title' => sprintf(
                            "{$aEstimateText['bill_normal_time_period']}(%d{$sMinuteUnit})",
                            NumberHelper::getLocalFormatNumber($aBill['normal_time'], $aInfo['order_info']['product_id'])
                        ),
                        'value' => sprintf("{$sCurrencySymbol}%s{$sCurrencyUnit}", NumberHelper::numberFormatDisplay($aBill['normal_fee'])),
                        'color' => Common::TIME_OF_USE_COLOR,
                    ];
                }
                break;
            default:
                break;
        }

        return $aRet;
    }


    /**
     * 获取分时计价模板
     *
     * @param array $aIntervals      账单返回的分时段计价数据
     * @param array $aConfigBillDate 日期输出格式{{month}}月{{day}}日
     *
     * @return string 格式化后的数据 {{month}}月{{day}}日 {{hour}}:{{minute}}
     */
    private static function _getTimeIntervalChargeDateTemplate($aIntervals, $aConfigBillDate) {
        $iMinTime  = min(array_map('strtotime', array_column($aIntervals, 'begin_time')));
        $iMaxTime  = max(array_map('strtotime', array_column($aIntervals, 'end_time')));
        $sTemplate = '{{hour}}:{{minute}}';
        if (date('d', $iMaxTime) != date('d', $iMinTime)) {
            $sTemplate = $aConfigBillDate.' '.$sTemplate;
        }

        return $sTemplate;
    }


    /**
     * 填充分时段计价模板
     *
     * @param string $sDateTime 预估时间
     * @param string $sTemplate 分时计价模板
     *
     * @return string 分时计价格式化后数据
     */
    private static function _getTimeIntervalChargeFormatDate($sDateTime, $sTemplate) {
        $iTimestamp = strtotime($sDateTime);
        $aTransfer  = [
            '{{month}}'  => date('en-US' == Language::getLanguage() ? 'M' : 'n', $iTimestamp),
            '{{day}}'    => date('j', $iTimestamp),
            '{{hour}}'   => date('H', $iTimestamp),
            '{{minute}}' => date('i', $iTimestamp),
        ];

        return strtr($sTemplate, $aTransfer);
    }

    /**
     * 获取预估详情费用项标题.
     * @param array  $aInfo         aInfo
     * @param string $sKey          sKey
     * @param array  $aPositionInfo aPositionInfo
     * @param bool   $bDynamicHit   bDynamicHit
     * @param array  $aPriceDetail  aPriceDetail
     *
     * @return string
     */
    private static function _getTitle($aInfo, string $sKey, array $aPositionInfo = [], bool $bDynamicHit = false, array $aPriceDetail = []) {
        $fDriverMetre  = isset($aPositionInfo['driver_metre']) ? round($aPositionInfo['driver_metre'] / Common::THOUSAND, 2) : 0; //距离公里数
        $iDriverMinute = $aPositionInfo['driver_minute'] ?? 0; //时间分钟
        $aEstimateText = NuwaConfig::text('config_text', 'mOrderEstimateNew');
        $aUnit         = Language::getUnit();
        $sLengthUnit   = $aUnit['length'][0];
        $sMinuteUnit   = $aUnit['time'][1];
        switch ($sKey) {
            case 'start_price':
                $sTitle = $aEstimateText['start_price'];
                break;
            case 'normal_price':
                if (!empty($aPriceDetail['distance_result']['total_distance'])) {
                    $fDriverMetre = round($aPriceDetail['distance_result']['total_distance'], 2);
                }

                $sTitle = Language::replaceTag(
                    $aEstimateText['normal_price'],
                    [
                        'distance'    => NumberHelper::getLocalFormatNumber($fDriverMetre, $aInfo['order_info']['product_id']),
                        'length_unit' => $sLengthUnit,
                    ]
                );
                break;
            case 'time_price':
                if (!empty($aPriceDetail['time_result']['total_time'])) {
                    $iDriverMinute = round($aPriceDetail['time_result']['total_time']);
                }

                $sTitle = Language::replaceTag(
                    $aEstimateText['time_price'],
                    [
                        'timeCost' => NumberHelper::getLocalFormatNumber($iDriverMinute, $aInfo['order_info']['product_id']),
                    ]
                );
                break;
            case 'night_price':
                $sTitle = $aEstimateText['night_price'];
                break;
            case 'red_packet':
                $sTitle = NuwaConfig::text('config_text', 'red_packet_name');
                break;
            case 'empty_price':
                $sTitle = $aEstimateText['empty_price'];
                break;
            case 'highway_fee':
                $sTitle = $aEstimateText['highway_fee'];
                break;
            case 'designated_driver_fee':
                $sTitle = $aEstimateText['designated_driver_fee'];
                break;
            case 'low_speed_price':
                if (!empty($aPriceDetail['low_speed_result']['total_time'])) {
                    $sTitle = sprintf(
                        "{$aEstimateText['low_speed_price']}(%s{$sMinuteUnit})",
                        $aPriceDetail['low_speed_result']['total_time']
                    );
                } else {
                    $sTitle = $aEstimateText['low_speed_price'];
                }
                break;
            case 'limit_fee':
                $sTitle = $aEstimateText['limit_fee'];
                break;
            case 'total_fee':
                // 多因素一口价
                $aProductInfo    = $aInfo['bill_info']['product_infos'] ?? [];
                $iComboType      = $aProductInfo[$aInfo['order_info']['require_level']]['combo_type'] ?? 0;
                $iCountPriceType = $aInfo['bill_info']['bills'][$aInfo['order_info']['require_level']]['count_price_type'] ?? 0;
                $bIsMultiFactorFlatRate = false;
                $bIsSpecialRate         = false;
                if (Utils\Horae::isMultiFactorFlatRateV2(['combo_type' => $iComboType, 'count_price_type' => $iCountPriceType])) {
                    $bIsMultiFactorFlatRate = true;
                }

                if (\BizCommon\Utils\Order::isSpecialRateV2($aInfo['order_info'])) {
                    $bIsSpecialRate = true;
                }

                if ($bIsMultiFactorFlatRate || (Constants\Horae::TYPE_COMBO_FLAT_RATE == $aInfo['order_info']['combo_type']
                    || Constants\Horae::TYPE_COMBO_SHENGANG_FLAT_RATE == $aInfo['order_info']['combo_type'])
                    || $bIsSpecialRate
                ) {
                    $sTitle = $aEstimateText['flat_rate'];
                } else {
                    $iSeatNum = $aInfo['order_info']['carpool_seat_num'];
                    if ($iSeatNum <= 0) {
                        $iSeatNum = 1;
                    }

                    $sTitle = Language::replaceTag(
                        $aEstimateText['total_fee'],
                        [
                            'seat' => NumberHelper::getLocalFormatNumber($iSeatNum, $aInfo['order_info']['product_id']),
                        ]
                    );
                }
                break;
            case 'dynamic_price':
                $sTitle = $bDynamicHit ? $aEstimateText['dynamic_price_hit'] : $aEstimateText['dynamic_price'];
                break;
            case 'br_car_fee':
                $sTitle = $aEstimateText['br_car_fee'];
                break;
            case 'taxes_fee':
                $sTitle = $aEstimateText['taxes_fee'];
                break;
            case 'booking_fee':
                $sTitle = $aEstimateText['booking_fee'];
                break;
            default:
                $sTitle = '';
        }

        return $sTitle;
    }
}
