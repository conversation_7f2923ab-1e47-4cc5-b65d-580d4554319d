<?php
namespace PreSale\Logics\luxEstimatePrice\multiResponse\component\feeDescInfo;

use Biz<PERSON>ib\Constants\OrderSystem;
use BizLib\Utils\Language;
use Xiao<PERSON>\Apollo\Apollo as ApolloV2;
use BizLib\Utils\CarLevel;
use PreSale\Logics\estimatePrice\multiResponse\MainDataRepo;
use PreSale\Logics\estimatePrice\multiResponse\Util;

/**
 * Class Common
 * @package PreSale\Logics\estimatePrice\multiResponse\component\priceDescInfo
 */
class Common
{

    const ENABLE_PRICE_INFO_DESC_VERSION = '5.2.46';

    /**
     * @param array $aResponse      aResponse
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @param array $aInfo          aInfo
     * @param array $aPriceInfoList aPriceInfoList
     * @return mixed
     */
    public static function formatPriceInfoDesc($aResponse, $aPriceDesc, $aPriceDescIcon, $aInfo, $aPriceInfoList = []) {
        //端上 新版本 升级价格描述 price_info_desc 替代 price_desc
        if (version_compare((string)($aInfo['common_info']['app_version']), self::ENABLE_PRICE_INFO_DESC_VERSION) >= 0) {
            $aApolloParam = [
                'key'       => $aInfo['passenger_info']['pid'],
                'phone'     => $aInfo['passenger_info']['phone'],
                'city'      => $aInfo['order_info']['area'],
                'car_level' => $aInfo['order_info']['require_level'],
            ];

            $bUpgrade = (new ApolloV2())->featureToggle('gs_upgrade_price_desc', $aApolloParam)->allow();

            if ($bUpgrade||\BizCommon\Utils\Horae::isLowPriceCarpool($aInfo['order_info'])) {
                $aResponse['price_info_desc'] = self::_formatPriceInfoDesc($aPriceDesc, $aPriceDescIcon, $aPriceInfoList);
            }
        }

        //6.0 固定返回price_info_desc
        if (Util::isDaCheAnyCar($aInfo)) {
            $aResponse['price_info_desc'] = self::_formatPriceInfoDesc($aPriceDesc, $aPriceDescIcon, $aPriceInfoList);
        }

        return $aResponse;
    }

    /**
     * @param array $aResponse    aResponse
     * @param array $aFeeDescInfo aFeeDescInfo
     * @return array
     */
    public static function formatFeeDescInfo($aResponse, $aFeeDescInfo) {
        $aResponse['fee_desc_info'] = $aFeeDescInfo;
        return $aResponse;
    }

    /**
     * 用price_info_desc代替price_desc.
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceIcon     aPriceIcon
     * @param array $aPriceInfoList aPriceInfoList
     * @return array
     */
    private static function _formatPriceInfoDesc($aPriceDesc, $aPriceIcon, $aPriceInfoList) {

        if (empty($aPriceDesc)) {
            return [];
        }

        $aPriceInfoDesc        = [];
        $aPriceInfoDescDefault = Language::getDecodedTextFromDcmp('estimate_price_info_desc-default');
        foreach ($aPriceDesc as $key => $aValue) {
            //如果配置对应的price_info_desc
            if (isset($aPriceInfoList[$key]) && !empty($aPriceInfoList[$key])) {
                $aPriceInfoDesc[$key] = $aPriceInfoList[$key];
            } else {
                $aPriceInfoDesc[$key]            = $aPriceInfoDescDefault;
                $aPriceInfoDesc[$key]['content'] = $aPriceDesc[$key];
                if (!empty($sPriceIcon[$key])) {
                    $aPriceInfoDesc[$key]['left_icon'] = $aPriceIcon[$key];
                }
            }
        }

        return $aPriceInfoDesc;
    }
}
