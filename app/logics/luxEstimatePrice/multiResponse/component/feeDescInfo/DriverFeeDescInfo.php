<?php

namespace PreSale\Logics\luxEstimatePrice\multiResponse\component\feeDescInfo;

/**
 * Class NormalPriceDesc
 * @package PreSale\Logics\estimatePrice\multiResponse\component\priceDescInfo
 */
class DriverFeeDescInfo
{
    /**
     * @var array
     */
    private $_aInfo;

    /**
     * NormalPriceDesc constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
    }

    /**
     * @param array $aResponse aResponse
     * @return mixed
     */
    public function build($aResponse) {
        $aPriceDesc = $aPriceDescIcon = $privilegeDesc = $aPriceInfoList = [];

        $oFormatter = new PriceItemFormatter($this->_aInfo);

        //豪华车司服信息
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getLuxuryDesignedDriverInfo($aPriceDesc, $aPriceDescIcon);

        //动调相关费用和券
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getMemberDynamicProtectInfo($aPriceDesc, $aPriceDescIcon);

        //春节红包
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getRedPacketInfo($aPriceDesc, $aPriceDescIcon);

        //跨城费
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getCrossCityFee($aPriceDesc, $aPriceDescIcon);

        //高速费
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getHighWayFeeDesc($aPriceDesc, $aPriceDescIcon);

        $aFeeDescInfo = [];
        $iDisplayLine = 1;
        for ($idx = 0;$idx < $iDisplayLine;$idx++) {
            if (count($aPriceDesc) == $idx) {
                break;
            }

            $aFeeDescInfo[] = [
                'fee_desc'      => $aPriceDesc[$idx],
                'fee_desc_icon' => $aPriceDescIcon[$idx],
            ];
        }

        $aResponse = Common::formatFeeDescInfo($aResponse, $aFeeDescInfo);

        return $aResponse;
    }
}
