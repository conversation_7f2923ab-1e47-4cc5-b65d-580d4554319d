<?php
namespace PreSale\Logics\luxEstimatePrice\multiResponse\component\feeDescInfo;

use Biz<PERSON>ib\Config as NuwaConfig;
use BizLib\Log as NuwaLog;
use BizLib\Utils\NumberHelper;
use BizLib\Utils\UtilHelper;
use BizLib\Utils\MemberVersion;
use BizLib\Constants;
use BizLib\Utils as BizUtils;
use Xiaoju\Apollo\Apollo as ApolloV2;
use PreSale\Logics\estimatePrice\multiResponse\MainDataRepo;
use PreSale\Logics\estimatePrice\multiResponse\Util;
use PreSale\Logics\estimatePrice\params\SceneParamsLogic;
use PreSale\Models\fee\FeeDetailTemplate;
use PreSale\Logics\estimatePrice\bill\CommonBillLogic;
use BizLib\Utils\Language;
use PreSale\Logics\estimatePrice\multiResponse\Util as PreSaleUtils;
use PreSale\Logics\v3Estimate\multiResponse\Component\basicFeeMsg\Util as FeeMsgUtil;
use Dirpc\SDK\PreSale\NewFormFeeDesc;

/**
 * Class PriceItemFormatter
 * @package PreSale\Logics\estimatePrice\multiResponse\component\priceDescInfo
 */
class PriceItemFormatter
{

    /**
     * @var array
     */
    private $_aInfo;

    /**
     * @var mixed
     */
    private $_aCommonInfo;

    /**
     * @var array|mixed
     */
    private $_aOrderInfo;

    /**
     * @var array
     */
    private $_aBillInfo;

    /**
     * @var array
     */
    private $_aDiscountInfo;

    /**
     * @var array
     */
    private $_aActivityInfo;

    /**
     * @var bool|mixed|null
     */
    private $_aConfig;

    /**
     * @var string
     */
    private $_sCurrencySymbol;

    /**
     * @var string
     */
    private $_sCurrencyUnit;

    /**
     * @var array
     */
    private $_aDisplayLines;

    /**
     * @var mixed
     */
    private $_aPassengerInfo;


    const SHAKE_RESULT_FIRSTESTIMATE = '1';
    const SHAKE_RESULT_SECOND_ESTIMATEBIND_SUCCESS = '2';
    const SHAKE_RESULT_SECOND_ESTIMATEFAIL         = '3';
    const SHAKE_RESULT_ICON      = 'https://dpubstatic.udache.com/static/dpubimg/9804dd19-9f16-43e0-802a-41cedfc3628c.gif';
    const SHAKE_RESULT_OPEN_ICON = 'https://dpubstatic.udache.com/static/dpubimg/e90a8494-1b4e-46e9-a415-5510c754e366.png';
    const LUX_MEMBER_LEVEL_PILOT = 2;
    const LUX_MEMBER_LEVEL_PILOT_BGCOLOR_FROM = '#F4E2BE';
    const LUX_MEMBER_LEVEL_PILOT_BGCOLOR_TO   = '#E3BE7E';
    const LUX_MEMBER_LEVEL_PILOT_FONT_COLOR   = '#333333';
    const LUX_MEMBER_LEVEL_COMMANDER          = 3;
    const LUX_MEMBER_LEVEL_COMMANDER_BGCOLOR_FROM = '#171826';
    const LUX_MEMBER_LEVEL_COMMANDER_BGCOLOR_TO   = '#100D0B';
    const LUX_MEMBER_LEVEL_COMMANDER_FONT_COLOR   = '#FFFFFF';

    /**
     * PriceItemFormatter constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo         = $aInfo;
        $this->_aCommonInfo   = $aInfo['common_info'];
        $this->_aOrderInfo    = $aInfo['order_info'] ?? [];
        $sCarLevel            = $aInfo['order_info']['require_level'];
        $this->_aBillInfo     = $aInfo['bill_info']['bills'][$sCarLevel] ?? [];
        $this->_aActivityInfo = $aInfo['activity_info'][0];
        $this->_aPassengerInfo = $aInfo['passenger_info'];
        $this->_aDiscountInfo  = $aInfo['activity_info'][0]['discount_desc'] ?? [];
        $this->_aDisplayLines  = CommonBillLogic::formatDisplayLines($this->_aBillInfo['display_lines']);

        $this->_aConfig            = NuwaConfig::text('config_text', 'getEstimateFeeInfo');
        $this->_aEstimateText      = NuwaConfig::text('config_text', 'mOrderEstimateNew');
        $this->_aMemberColorConfig = NuwaConfig::text('config_text', 'member_bgpic_font_color_v2');

        $this->_aEstimateTextDcmp = Language::getDecodedTextFromDcmp('config_text-mOrderEstimateNew'); //文案迁到dcmp后可以把上面那行干掉

        $sCurrency = $aInfo['bill_info']['currency'] ?? '';
        list($sSymbol, $sUnit)  = \BizLib\Utils\Currency::getSymbolUnit($sCurrency, $aInfo['order_info']);
        $this->_sCurrencySymbol = $sSymbol;
        $this->_sCurrencyUnit   = $sUnit;
    }


    /**
     * 是否显示折叠优惠项, 优惠项大于1个时展示
     * @return array
     */
    public function getShowFoldDiscountInfo() {
        $aDiscountDesc = [
            'total'                      => 0.0,
            'num'                        => 0,
            'desc'                       => '',
            'desc_icon'                  => '',
            'include_dynamic_price'      => false,
            'include_fixed_preferential' => false,
        ];

        if (empty($this->_aDiscountInfo) || !\BizLib\Utils\Product::displayFoldDiscount($this->_aOrderInfo['product_id'])) {
            return $aDiscountDesc;
        }

        foreach ($this->_aDiscountInfo as $aDiscountItem) {
            if (($fDiscount = abs($aDiscountItem['amount'] ?? 0)) > 0 && 'infofee' != $aDiscountItem['type']) {
                ++$aDiscountDesc['num'];
                $aDiscountDesc['total'] += $fDiscount;
            }
        }

        $fDynamicPrice = $this->_aBillInfo['dynamic_diff_price'] ?? 0.0;
        if ($fDynamicPrice < 0) {
            ++$aDiscountDesc['num'];
            $aDiscountDesc['total'] += abs($fDynamicPrice);
        }

        $fFixedPreferential = $this->_aBillInfo['fixed_preferential'] ?? 0.0;
        if ($fFixedPreferential < 0) {
            ++$aDiscountDesc['num'];
            $aDiscountDesc['total'] += abs($fFixedPreferential);
        }

        if ($aDiscountDesc['num'] > 1) {
            if ($this->_aConfig) {
                $aDiscountDesc['desc'] = Language::replaceTag(
                    $this->_aConfig['discount_title'],
                    array(
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'num'             => NumberHelper::numberFormatDisplay($aDiscountDesc['total']),
                        'currency_unit'   => $this->_sCurrencyUnit,
                    )
                );
            }

            if ($fDynamicPrice < 0) {
                $aDiscountDesc['include_dynamic_price'] = true;
            }

            if ($fFixedPreferential < 0) {
                $aDiscountDesc['include_fixed_preferential'] = true;
            }
        }

        return $aDiscountDesc;
    }

    /**
     * 春节红包
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getRedPacketInfo($aPriceDesc, $aPriceDescIcon) {
        $fRedPacketValue = $this->_aDisplayLines['red_packet']['value'] ?? 0.0;
        if ($fRedPacketValue > 0) {
            $sDesc            = Language::replaceTag(
                NuwaConfig::text('config_text', 'red_packet_estimate_title'),
                array(
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'name'            => NuwaConfig::text('config_text', 'red_packet_name'),
                    'num'             => $fRedPacketValue,
                    'currency_unit'   => $this->_sCurrencyUnit,
                )
            );
            $aPriceDesc[]     = $sDesc;
            $aPriceDescIcon[] = '';
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 跨城费
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getCrossCityFee($aPriceDesc, $aPriceDescIcon) {
        if (isset($this->_aBillInfo['cross_city_fee'])) {
            $iCrossCityFee = (float)($this->_aBillInfo['cross_city_fee']);
            if ($iCrossCityFee > 0) {
                $aPriceDesc[]     = Language::replaceTag(
                    $this->_aConfig['cross_city_fee'],
                    [
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'cross_city_fee'  => NumberHelper::numberFormatDisplay($iCrossCityFee),
                        'currency_unit'   => $this->_sCurrencyUnit,
                    ]
                );
                $aPriceDescIcon[] = '';
            }
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }


    /**
     * 获取接送机单price_desc
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getAirportOrderPriceDesc($aPriceDesc, $aPriceDescIcon) {
        //仅限专车接送机
        if (BizUtils\Product::isDefault($this->_aOrderInfo['product_id'])
            && isset($this->_aOrderInfo['require_level'])
            && in_array($this->_aOrderInfo['combo_type'], [ Constants\Horae::TYPE_COMBO_FROM_AIRPORT, Constants\Horae::TYPE_COMBO_TO_AIRPORT])
        ) {
            // 接送机一口价
            if (isset($this->_aBillInfo['cap_price']) && $this->_aBillInfo['cap_price'] > 0) {
                $aPriceDesc[]     = $this->_aConfig['airport_flat_rate_price_desc'];
                $aPriceDescIcon[] = '';
            }
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }


    /**
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function formatFeeStruct($aPriceDesc, $aPriceDescIcon) {
        $aEstimateFixedFee = CommonBillLogic::formatEstimateFixedFees($this->_aBillInfo['estimate_fixed_fees'] ?? '');
        if (empty($aEstimateFixedFee)) {
            return [$aPriceDesc, $aPriceDescIcon];
        }

        $aTitleConfig = NuwaConfig::text('config_fee_text', 'fee_list');
        foreach ($aEstimateFixedFee as $sKey => $aItem) {
            if (!isset($aItem['value']) || $aItem['value'] <= 0 || empty($sKey)) {
                continue;
            }

            if (empty($aTitleConfig[$sKey]['estimate_title'])) {
                continue;
            }

            $aPriceDesc[]     = Language::replaceTag(
                $aTitleConfig[$sKey]['estimate_title'],
                array(
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'fee'             => NumberHelper::numberFormatDisplay($aItem['value']),
                    'currency_unit'   => $this->_sCurrencyUnit,
                )
            );
            $aPriceDescIcon[] = $aTitleConfig[$sKey]['estiamte_icon'] ?? '';
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 定制化服务
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getCustomizedServicePriceDesc($aPriceDesc, $aPriceDescIcon) {
        //定制化服务
        if (isset($this->_aBillInfo['customized_service_result'])) {
            $sCustomInfo = $this->_getCusItomServiceDesc($this->_aBillInfo['customized_service_result']);
            if (!empty($sCustomInfo)) {
                $aPriceDesc[]     = $sCustomInfo;
                $aPriceDescIcon[] = '';
            }
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @param array $aFoldDiscount  折叠优惠信息
     * @return array
     */
    public function getFixedPreferentialPriceDesc($aPriceDesc, $aPriceDescIcon, $aFoldDiscount) {
        //多因素一口价应该只有普通场景才有，此处不额外做限制（此前只配置了noncarpool）
        $fFixedPref = $this->_aBillInfo['fixed_preferential'] ?? 0;
        if ($fFixedPref < 0 && !$aFoldDiscount['include_fixed_preferential']) {
            //已优惠{4.6}元
            $aPriceDesc[]     = Language::replaceTag(
                $this->_aConfig['fixed_preferential'],
                array(
                    'price'         => abs($fFixedPref),
                    'currency_unit' => $this->_sCurrencyUnit,
                )
            );
            $aPriceDescIcon[] = '';
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 动调相关费用信息
     * @param array $aPriceDesc            aPriceDesc
     * @param array $aPriceDescIcon        aPriceDescIcon
     * @param array $aFoldDiscount         折叠优惠信息
     * @param bool  $bMultiPremiumEstimate 是否专车多预估
     * @return array
     */
    public function getDynamicPriceDesc($aPriceDesc, $aPriceDescIcon, $aFoldDiscount, $bMultiPremiumEstimate = false) {
        //专车,豪华车控制动调展示
        // $bForceDisplay   = (new ApolloV2())->featureToggle(
        // 'gs_red_packet_dynamic_display_toggle',
        // array(
        // 'key'        => $this->_aPassengerInfo['pid'],
        // 'product_id' => $this->_aOrderInfo['product_id'],
        // 'phone'      => $this->_aPassengerInfo['phone'],
        // )
        // )->allow();
        $bForceDisplay   = false;
        $bIsCarpool      = BizUtils\Horae::isCarpool($this->_aOrderInfo['combo_type'], $this->_aOrderInfo['require_level']);
        $fRedPacketValue = $this->_aDisplayLines['red_packet']['value'] ?? 0.0;
        if (!empty($this->_aBillInfo) && !$aFoldDiscount['include_dynamic_price'] && ($fRedPacketValue <= 0 || $bForceDisplay || $bMultiPremiumEstimate)) {
            $sDynamicPriceDesc = $this->getDynamicDesc($bMultiPremiumEstimate);
            if (!empty($sDynamicPriceDesc)) {
                $aPriceDesc[] = $sDynamicPriceDesc;
                //拼车未配置Icon
                if (!$bIsCarpool && $this->_isShowDynamicMemberProtectMsg()) {
                    $aPriceDescIcon[] = $this->_aPassengerInfo['member_profile']['level_icon'];
                } else {
                    $aPriceDescIcon[] = '';
                }
            }
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 跨城拼车保险费
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getInterInsurancePriceDesc($aPriceDesc, $aPriceDescIcon) {
        $iInterInsuranceFee = (float)($this->_aBillInfo['insure_total_fee']);
        if ($iInterInsuranceFee > 0 && $this->_aOrderInfo['is_select_insurance']) {
            $aPriceDesc[]     = Language::replaceTag(
                $this->_aConfig['carpool_inter_insurance_price_desc'],
                array(
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'insurance_fee'   => NumberHelper::numberFormatDisplay($iInterInsuranceFee),
                    'currency_unit'   => $this->_sCurrencyUnit,
                )
            );
            $aPriceDescIcon[] = '';
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 获取券信息
     * @param array  $aPriceDesc     aPriceDesc
     * @param array  $aPriceDescIcon aPriceDescIcon
     * @param array  $aFoldDiscount  折叠优惠信息
     * @param string $sCityCardDesc  市民卡信息
     * @param bool   $bShakeSuccess  摇一摇是否成功
     * @param bool   $bIsSpecialRate 是否特价车
     * @return array
     */
    public function getCouponDesc($aPriceDesc, $aPriceDescIcon, $aFoldDiscount, $sCityCardDesc = '', $bShakeSuccess = false, $bIsSpecialRate = false) {

        $aCouponDetail    = $this->_aActivityInfo['estimate_detail'];
        $aCouponInfo      = $this->_aActivityInfo['coupon_info'];
        $aDiscountInfo    = $this->_aActivityInfo['discount_desc'];
        $iFinalCouponType = $this->_aActivityInfo['final_coupon_type'];

        $sCouponDesc = $sCouponDescIcon = '';
        if (isset($aCouponDetail['title'], $aCouponDetail['value']) && empty($aFoldDiscount['desc']) && empty($sCityCardDesc) && !$bShakeSuccess) {
            //特价出租车
            if ($bIsSpecialRate) {
                if (in_array($this->_aOrderInfo['product_id'],[\BizLib\Constants\OrderSystem::PRODUCT_ID_UNITAXI,\BizLib\Constants\OrderSystem::PRODUCT_ID_BUSINESS_TAXI_CAR])) {
                    foreach ($aDiscountInfo as $aItem) {
                        if (!empty($aItem['type']) && !empty($aItem['amount']) && 'limittime' === $aItem['type'] && $aItem['amount'] > 0) {
                            $iLimitTimeAmount = $aItem['amount'];
                            break;
                        }
                    }

                    $sCouponDesc = Language::replaceTag(
                        $this->_aEstimateTextDcmp['unione_special_rate_coupon_desc'],
                        [
                            'currency_symbol' => $this->_sCurrencySymbol,
                            'coupon_num'      => NumberHelper::numberFormatDisplay($aCouponDetail['amount']),
                            'limit_time_num'  => NumberHelper::numberFormatDisplay($iLimitTimeAmount),
                            'currency_unit'   => $this->_sCurrencyUnit,
                        ]
                    );
                }
            } else {
                //学生券
                if (FeeDetailTemplate::isOnlyStudentCardCoupon($aCouponInfo)) {
                    $sCouponDesc = Language::replaceTag(
                        $this->_aConfig['student_card_title'],
                        [
                            'currency_symbol' => $this->_sCurrencySymbol,
                            'num'             => $aDiscountInfo[0]['amount'],
                            'currency_unit'   => $this->_sCurrencyUnit,
                        ]
                    );
                } elseif (FeeDetailTemplate::isPackageMallCoupon($aCouponInfo, $iFinalCouponType)) {
                    $sCouponDesc = Language::replaceTag(
                        //智慧套餐券
                        $this->_aConfig['package_coupon_title'],
                        [
                            'currency_symbol' => $this->_sCurrencySymbol,
                            'num'             => NumberHelper::numberFormatDisplay($aCouponInfo['default_coupon']['amount'] / 100),
                            'currency_unit'   => $this->_sCurrencyUnit,
                        ]
                    );
                } else {
                    $sCouponDesc = Language::replaceTag(
                        $this->_aConfig['coupon_title_with_brackets'],
                        array(
                            'currency_symbol' => $this->_sCurrencySymbol,
                            'fee'             => NumberHelper::numberFormatDisplay($aCouponDetail['amount']),
                            'currency_unit'   => $this->_sCurrencyUnit,
                            'title'           => $aCouponDetail['title'],
                        )
                    );
                }
            }
        }

        if (!empty($sCouponDesc)) {
            $aPriceDesc[]     = $sCouponDesc;
            $aPriceDescIcon[] = $sCouponDescIcon;
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 获取摇一摇券信息
     * @param array $aFoldDiscount 折叠优惠信息
     * @return array
     */
//    public function getShakeCouponInfo($aFoldDiscount) {
//
//        $sShakeTitle   = '';
//        $sShakeIcon    = '';
//        $sShakeSuccess = false;
//
//        $sShakeFlag = $this->_aInfo['price_extra']['shake_flag'];
//        if (!empty($sShakeFlag)) {
//            $sShakeTitle = $this->getShakeDesc($sShakeFlag, $aFoldDiscount['num'], $aFoldDiscount);
//            if (!empty($sShakeTitle)) {
//                //摇一摇优惠绑定成功
//                $sShakeIcon = self::SHAKE_RESULT_ICON;
//                if (self::SHAKE_RESULT_SECOND_ESTIMATEBIND_SUCCESS == $sShakeFlag) {
//                    $sShakeSuccess = true;
//                    $sShakeIcon    = self::SHAKE_RESULT_OPEN_ICON;
//                }
//            }
//        }
//
//        return [$sShakeTitle, $sShakeIcon, $sShakeSuccess];
//    }

    /**
     * 慢必赔
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getFastEnsurePriceDesc($aPriceDesc, $aPriceDescIcon) {

        $sFastEnsureDesc = $this->_aActivityInfo['fast_ensure_detail']['detail']['title'] ?? '';
        if (!empty($sFastEnsureDesc)) {
            $aPriceDesc[]     = $sFastEnsureDesc;
            $aPriceDescIcon[] = '';
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 豪华车司服信息
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getLuxuryDesignedDriverInfo($aPriceDesc, $aPriceDescIcon) {
        if ((new \Xiaoju\Apollo\Apollo())->featureToggle('gs_luxury_designated_driver_text', $this->_aOrderInfo)->allow()) {
            if (\BizLib\Utils\Product::PRODUCT_ID_FIRST_CLASS_CAR == $this->_aOrderInfo['product_id']
                && ((int)($this->_aOrderInfo['designated_driver'])
                || (int)($this->_aOrderInfo['designated_driver_tag']))
            ) {
                $fDesignatedDriverFee = $this->_aBillInfo['designated_driver_fee'] ?? 0;
                // 1. $fDesignatedDriverFee > 0 含选司务员优惠价 {{$fDesignatedDriverFee}} 元
                // 2. $fDesignatedDriverFee = 0 含选司务员优惠价 {{$fDesignatedDriverFee}} 元 (原价20元)
                $iOriginPrice = 20;

                $sDesignatedDriverPriceDescKey = $fDesignatedDriverFee <= 0 ? 'designated_driver_discount_price_desc' : 'designated_driver_price_desc';
                $aPriceDesc[]     = ' ' . Language::replaceTag(
                    $this->_aConfig[$sDesignatedDriverPriceDescKey],
                    array(
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'discount_fee'    => NumberHelper::numberFormatDisplay($fDesignatedDriverFee),
                        'origin_fee'      => NumberHelper::numberFormatDisplay($iOriginPrice),
                        'currency_unit'   => $this->_sCurrencyUnit,
                    )
                );
                $aPriceDescIcon[] = '';
            }
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 高速费
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getHighWayFeeDesc($aPriceDesc, $aPriceDescIcon) {
        $iHighwayFee = (float)($this->_aBillInfo['highway_fee']);
        if ($iHighwayFee > 0) {
            $aPriceDesc[]     = Language::replaceTag(
                $this->_aConfig['highway_fee'],
                [
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'highway_fee'     => NumberHelper::numberFormatDisplay($iHighwayFee),
                    'currency_unit'   => $this->_sCurrencyUnit,
                ]
            );
            $aPriceDescIcon[] = '';
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 获取打车金intro_msg.
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @param int   $isFolderShow   是否有多项优惠项组合
     * @return array
     */
    public function getRewardsDesc($aPriceDesc, $aPriceDescIcon, $isFolderShow) {

        if (!empty($this->_aDiscountInfo) && !$isFolderShow) {
            foreach ($this->_aDiscountInfo as $aItem) {
                if (!empty($aItem['type']) && !empty($aItem['amount']) && 'reward' === $aItem['type'] && $aItem['amount'] > 0) {
                    $aPriceDesc[]     = Language::replaceTag(
                        $this->_aConfig['rewards_title'],
                        array(
                            'currency_symbol' => $this->_sCurrencySymbol,
                            'num'             => NumberHelper::numberFormatDisplay($aItem['amount']),
                            'currency_unit'   => $this->_sCurrencyUnit,
                        )
                    );
                    $aPriceDescIcon[] = '';
                    break;
                }
            }
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 获取畅行卡
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @param int   $isFolderShow   是否有多项优惠项组合
     * @return array
     */
    public function getBackCardsDesc($aPriceDesc, $aPriceDescIcon, $isFolderShow) {
        if (!empty($this->_aDiscountInfo) && $isFolderShow) {
            foreach ($this->_aDiscountInfo as $aItem) {
                if (!empty($aItem['type']) && !empty($aItem['amount']) && 'backcard' === $aItem['type'] && $aItem['amount'] > 0) {
                    $aPriceDesc[]     = Language::replaceTag(
                        $this->_aConfig['backcards_title'],
                        array(
                            'currency_symbol' => $this->_sCurrencySymbol,
                            'num'             => NumberHelper::numberFormatDisplay($aItem['amount']),
                            'currency_unit'   => $this->_sCurrencyUnit,
                        )
                    );
                    $aPriceDescIcon[] = '';
                    break;
                }
            }
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 获取市民卡intro_msg.
     * @param int $isFolderShow 是否有多项优惠项组合
     * @return array
     */
    public function getCityCardsDesc($isFolderShow) {
        if (empty($this->_aDiscountInfo) || $isFolderShow) {
            return ['', ''];
        }

        foreach ($this->_aDiscountInfo as $aItem) {
            if (!empty($aItem['type']) && !empty($aItem['amount']) && 'citycard' === $aItem['type'] && $aItem['amount'] > 0) {
                $sDesc     = Language::replaceTag(
                    $this->_aConfig['citycards_title'],
                    array(
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'num'             => NumberHelper::numberFormatDisplay($aItem['amount']),
                        'currency_unit'   => $this->_sCurrencyUnit,
                    )
                );
                $sDescIcon = '';

                return [$sDesc, $sDescIcon];
            }
        }

        return ['', ''];
    }

    /**
     * 信息费
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getInfoFeeDesc($aPriceDesc, $aPriceDescIcon) {
        foreach ($this->_aDiscountInfo as $aItem) {
            if (!empty($aItem['type']) && !empty($aItem['amount']) && 'infofee' === $aItem['type'] && $aItem['amount'] > 0) {
                $aPriceDesc[]     = Language::replaceTag(
                    $this->_aConfig['infofee_title'],
                    array(
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'num'             => NumberHelper::numberFormatDisplay($aItem['amount']),
                        'currency_unit'   => $this->_sCurrencyUnit,
                    )
                );
                $aPriceDescIcon[] = '';
                break;
            }
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 获取限时特惠
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @param int   $isFolderShow   是否有多项优惠项组合
     * @return array
     */
    public function getLimitTimeDesc($aPriceDesc, $aPriceDescIcon, $isFolderShow) {

        if (!empty($this->_aDiscountInfo) && !$isFolderShow) {
            foreach ($this->_aDiscountInfo as $aItem) {
                if (!empty($aItem['type']) && !empty($aItem['amount']) && 'limittime' === $aItem['type'] && $aItem['amount'] > 0) {
                    $iLimitTimeAmount = $aItem['amount'];
                }

                if (!empty($aItem['type']) && !empty($aItem['amount']) && 'coupon' === $aItem['type'] && $aItem['amount'] > 0) {
                    $iCouponAmount = $aItem['amount'];
                }
            }

            if ($iLimitTimeAmount > 0 && $iCouponAmount > 0) {
                $aPriceDesc[]     = Language::replaceTag(
                    $this->_aConfig['limittime_title_with_coupon'],
                    array(
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'num'             => NumberHelper::numberFormatDisplay($iCouponAmount + $iLimitTimeAmount),
                        'currency_unit'   => $this->_sCurrencyUnit,
                    )
                );
                $aPriceDescIcon[] = '';
            } elseif ($iLimitTimeAmount > 0) {
                $aPriceDesc[]     = Language::replaceTag(
                    $this->_aConfig['limittime_title'],
                    array(
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'num'             => NumberHelper::numberFormatDisplay($iLimitTimeAmount),
                        'currency_unit'   => $this->_sCurrencyUnit,
                    )
                );
                $aPriceDescIcon[] = '';
            }
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * @Desc:
     * @param mixed[] Array $aPriceDesc     structure to count the elements of.
     * @param mixed[] Array $aPriceDescIcon structure to count the elements of.
     * @return array
     * @property getWaitRewardDesc $getWaitRewardDesc
     * @Author:<EMAIL>
     */
    public function getWaitRewardDesc($aPriceDesc, $aPriceDescIcon) {
        if (!\BizCommon\Utils\Horae::isLowPriceCarpoolNow($this->_aOrderInfo)) {
            return [$aPriceDesc, $aPriceDescIcon];
        }

        $fWaitRewardValue = $this->_aDisplayLines['sps_likewait_reward']['value'] ?? 0.0;
        $fWaitRewardValue = -$fWaitRewardValue;

        $fCouponValue = 0.0;
        if (!empty($this->_aDiscountInfo)) {
            foreach ($this->_aDiscountInfo as $aItem) {
                if (!empty($aItem['type']) && !empty($aItem['amount']) && 'coupon' === $aItem['type'] && $aItem['amount'] > 0) {
                    $fCouponValue = $aItem['amount'];
                }
            }
        }

        $aCouponDescTextTemplate = json_decode(Language::getTextFromDcmp('config_carpool-low_price_carpool_waitreward_show',['-']), true);

        if ($fCouponValue > 0 && $fWaitRewardValue > 0) {
            $sDesc          = Language::replaceTag(
                $aCouponDescTextTemplate['with_coupon'],
                array(
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'num'             => $fWaitRewardValue + $fCouponValue,
                    'currency_unit'   => $this->_sCurrencyUnit,
                )
            );
            $aPriceDesc     = [$sDesc];
            $aPriceDescIcon = [''];
        } elseif ($fWaitRewardValue > 0) {
            $sDesc          = Language::replaceTag(
                $aCouponDescTextTemplate['without_coupon'],
                array(
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'num'             => $fWaitRewardValue,
                    'currency_unit'   => $this->_sCurrencyUnit,
                )
            );
            $aPriceDesc     = [$sDesc];
            $aPriceDescIcon = [''];
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }


    /**
     * 特价车打折信息
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getSpecialRateDiscount($aPriceDesc, $aPriceDescIcon) {
        if (isset($this->_aBillInfo)) {
            $fTotalFee = MainDataRepo::getFastCarTotalFee();
            if ($fTotalFee && !in_array($this->_aOrderInfo['product_id'],[\BizLib\Constants\OrderSystem::PRODUCT_ID_UNITAXI,\BizLib\Constants\OrderSystem::PRODUCT_ID_BUSINESS_TAXI_CAR])) {
                $aPriceDesc[]     = Language::getTextFromDcmp('special_rate-original_price_desc', ['price' => $fTotalFee]);
                $aPriceDescIcon[] = '';
            }
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * @param string $sShakeFlag    sShakeFlag
     * @param int    $iFoldDiscount iFoldDiscountNum
     * @param array  $aFoldDiscount aFoldDiscount
     * @return mixed|string
     */
//    public function getShakeDesc($sShakeFlag, $iFoldDiscount, $aFoldDiscount) {
//        $sRet            = '';
//        $iParamShakeFlag = $this->_aCommonInfo['shake_flag'];
//        if (1 != $iParamShakeFlag) {
//            if (self::SHAKE_RESULT_FIRSTESTIMATE == $sShakeFlag) {
//                if ($iFoldDiscount >= 1) {
//                    $sRet = $this->_aConfig['shake_title_has_coupon'];
//                } else {
//                    $sRet = $this->_aConfig['shake_title_no_coupon'];
//                }
//            }
//        } else {
//            if (self::SHAKE_RESULT_SECOND_ESTIMATEBIND_SUCCESS == $sShakeFlag) {
//                $sRet = $this->_getCouponShakeDesc(!empty($aFoldDiscount['desc']));
//            } elseif (self::SHAKE_RESULT_SECOND_ESTIMATEFAIL == $sShakeFlag) {
//                $sRet = $this->_aConfig['shake_title_fail'];
//            }
//        }
//
//        return $sRet;
//    }

    /**
     * 摇一摇intro_msg.
     * @param bool $isFolderShow isFolderShow
     * @return mixed|string
     */
//    private function _getCouponShakeDesc($isFolderShow) {
//        if (empty($this->_aDiscountInfo) || $isFolderShow) {
//            return '';
//        }
//
//        foreach ($this->_aDiscountInfo as $aItem) {
//            if (!empty($aItem['type']) && !empty($aItem['amount']) && 'shake' === $aItem['type'] && $aItem['amount'] > 0) {
//                $sDesc = Language::replaceTag(
//                    $this->_aConfig['shake_title_OK'],
//                    array(
//                        'currency_symbol' => $this->_sCurrencySymbol,
//                        'num'             => NumberHelper::numberFormatDisplay($aItem['amount']),
//                        'currency_unit'   => $this->_sCurrencyUnit,
//                    )
//                );
//
//                return $sDesc;
//            }
//        }
//
//        return '';
//    }

    /**
     * 获取会员2.0溢价保护文案信息
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getMemberDynamicProtectInfo($aPriceDesc, $aPriceDescIcon) {
        $fMemberProtectFee = $this->_aBillInfo['dynamic_member_reduce'] < 0 ? -$this->_aBillInfo['dynamic_member_reduce'] : $this->_aBillInfo['dynamic_member_reduce'];
        $aCoupon           = $this->_aActivityInfo['estimate_detail'] ?? [];
        $fCouponFee        = 0;
        if (isset($aCoupon['title'], $aCoupon['value'])) {
            $fCouponFee = $aCoupon['amount'];
        }

        //不存在溢价保护并且不存在券
        if ($fMemberProtectFee <= 0 && $fCouponFee <= 0) {
            return [$aPriceDesc, $aPriceDescIcon];
        }

        $sLevelId   = $this->_aPassengerInfo['member_profile']['level_id'];
        $sLevelName = $this->_aPassengerInfo['member_profile']['level_name_new'];
        $sLevelIcon = $this->_aPassengerInfo['member_profile']['level_icon'];

        if (PreSaleUtils::isMiniApp($this->_aCommonInfo['access_key_id'])) {
            $sLevelIcon = '';
        }

        if ($fMemberProtectFee > 0) {
            //存在溢价保护文案逻辑
            $aDynamicPrivileges = $this->_aPassengerInfo['member_profile']['privileges']['dpa'] ?? [];
            if (!isset($aDynamicPrivileges['name'], $aDynamicPrivileges['frontend']['reason'])) {
                return [$aPriceDesc, $aPriceDescIcon];
            }

            $sDpaPrivName = $aDynamicPrivileges['name'];
            if ($fCouponFee > 0) {
                //渲染溢价保护和券文案
                $fMemberProtectCouponFee = $fMemberProtectFee + $fCouponFee;
                //豪华车使用带有会员名称的文案
                $sMemberProtectCouponMsg = $this->_aEstimateText['member_protect_coupon_msg_new_60'];
                $sDesc = Language::replaceTag(
                    $sMemberProtectCouponMsg,
                    [
                        'priv_name'       => $sDpaPrivName,
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'num'             => $fMemberProtectCouponFee,
                        'currency_unit'   => $this->_sCurrencyUnit,
                    ]
                );
            } else {
                $sMemberProtectMsg = $this->_aEstimateText['member_protect_msg_new_60'];
                $sDesc = Language::replaceTag(
                    $sMemberProtectMsg,
                    [
                        'priv_name'       => $sDpaPrivName,
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'amount'          => $fMemberProtectFee,
                        'currency_unit'   => $this->_sCurrencyUnit,
                    ]
                );
            }
        } elseif ($fCouponFee > 0) {
            $sLang = Language::getLanguage();
            $aTemplate = FeeMsgUtil::getCouponDescTemplate($sLang);
            $sContent = $aTemplate['title'].$aTemplate['content'];
            if ('en-US' == $sLang) {
                $sContent = $aTemplate['content'];
                $sLevelIcon = $aTemplate['icon'];
            }
            $sDesc = Language::replaceTag(
                $sContent,
                array(
                    'num' => NumberHelper::numberFormatDisplay($aCoupon['amount']),
                )
            );
        }

        if (empty($sDesc)) {
            return [$aPriceDesc, $aPriceDescIcon];
        }

        $aLevelConfig = $this->_aMemberColorConfig[$sLevelId - 1] ?? [];
        //信宜行用户单独处理
        if (isset($this->_aPassengerInfo['member_profile']['identity_type']) && 2 == $this->_aPassengerInfo['member_profile']['identity_type']) {
            $aLevelConfig = $this->_aMemberColorConfig['99'] ?? [];
        }

        $aPriceDesc[]     = $sDesc;
        $aPriceDescIcon[] = $sLevelIcon;
        return [$aPriceDesc, $aPriceDescIcon];
    }
    /**
     * 豪华车会员溢价保护与券逻辑
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getMemberProtectAndCouponDesc($aPriceDesc, $aPriceDescIcon) {
        //判断是否是豪华车，如果是豪华车则走新逻辑，其他的走老逻辑
        if (isset($this->_aBillInfo['is_has_dynamic']) && !$this->_aBillInfo['is_has_dynamic']) {
            return '';
        }

        if (!$this->_isShowDynamicMemberProtectMsg()) {
            $aPriceDesc[]     = Language::replaceTag($this->_aConfig['dynamic_title_v2'], array('currency_symbol' => $this->_sCurrencySymbol, 'num' => NumberHelper::numberFormatDisplay($this->_aBillInfo['dynamic_diff_price']), 'currency_unit' => $this->_sCurrencyUnit));
            $aPriceDescIcon[] = '';
            return [$aPriceDesc, $aPriceDescIcon];
        }

        //{type=02 原价预估{{currency_symbol}}{{num}}{{currency_unit}}}
        $aPriceDesc[]     = Language::replaceTag(
            $this->_aEstimateText['origin_fee_msg_lux'],
            [
                'currency_symbol' => $this->_sCurrencySymbol,
                'num'             => $this->_aBillInfo['total_fee_without_discount'],
                'currency_unit'   => $this->_sCurrencyUnit,
            ]
        );
        $aPriceDescIcon[] = '';
        //溢价保护文案逻辑
        $aDynamicPrivileges = $this->_aPassengerInfo['member_profile']['privileges']['dpa'] ?? [];
        $sLevelName         = $this->_aPassengerInfo['member_profile']['level_name'];
        if (isset($aDynamicPrivileges['name'], $aDynamicPrivileges['frontend']['reason'])) {
            $sDpaPrivName = $aDynamicPrivileges['name'];
            //豪华车动态加价增加显示动调价格,"已抵*元"
            $fMemberProtectFee = $this->_aBillInfo['dynamic_member_reduce'] < 0 ? -$this->_aBillInfo['dynamic_member_reduce'] : $this->_aBillInfo['dynamic_member_reduce'];
            if ($fMemberProtectFee > 0) {
                //文案为{{level_name}}徽章{{priv_name}}已抵{{currency_symbol}}{{amount}}{{currency_unit}}
                $sMemberProtectDesc = Language::replaceTag(
                    $this->_aEstimateText['member_protect_msg_lux'],
                    [
                        'level_name'      => $sLevelName,
                        'priv_name'       => $sDpaPrivName,
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'amount'          => $fMemberProtectFee,
                        'currency_unit'   => $this->_sCurrencyUnit,
                    ]
                );
            }

            $sMemberProtectDescIcon = $this->_aPassengerInfo['member_profile']['level_icon'];
        }

        //券逻辑
        $aCoupon = $this->_aActivityInfo['estimate_detail'] ?? [];
        if (isset($aCoupon['title'], $aCoupon['value'])) {
            $fCouponFee      = $aCoupon['amount'];
            $sCouponDesc     = Language::replaceTag(
                $this->_aConfig['coupon_title_with_brackets'],
                array(
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'fee'             => NumberHelper::numberFormatDisplay($aCoupon['amount']),
                    'currency_unit'   => $this->_sCurrencyUnit,
                    'title'           => $aCoupon['title'],
                )
            );
            $sCouponDescIcon = '';
        }

        //溢价保护 + 券
        if ((float)($fCouponFee) > 0 && (float)($fMemberProtectFee) > 0) {
            $fMemberProtectCouponFee  = $fMemberProtectFee + $fCouponFee;
            $sMemberProtectCouponDesc = Language::replaceTag(
                //文案为{{level_name}}徽章{{prive_name}}+券共抵{{currency_symbol}}{{{num}}}{{currency_unit}}
                $this->_aEstimateText['member_protect_coupon_msg_lux'],
                [
                    'level_name'      => $sLevelName,
                    'priv_name'       => $sDpaPrivName,
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'num'             => $fMemberProtectCouponFee,
                    'currency_unit'   => $this->_sCurrencyUnit,
                ]
            );
            $sMemberProtectCouponDescIcon = $sMemberProtectDescIcon;
        }

        if (!empty($sMemberProtectCouponDesc)) {
            $aPriceDesc[]     = $sMemberProtectCouponDesc;
            $aPriceDescIcon[] = $sMemberProtectCouponDescIcon;
        } else {
            if (!empty($sMemberProtectDesc)) {
                $aPriceDesc[]['desc'] = $sMemberProtectDesc;
                $aPriceDescIcon[]     = $sMemberProtectDescIcon;
            }

            if (!empty($sCouponDesc)) {
                $aPriceDesc[]     = $sCouponDesc;
                $aPriceDescIcon[] = $sCouponDescIcon;
            }
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }


    /**
     * 获取动调提示文案.
     * @param bool $bMultiPremiumEstimate 是否专车多预估
     * @return mixed|string
     */
    public function getDynamicDesc($bMultiPremiumEstimate = false) {
        //无动调
        if (isset($this->_aBillInfo['is_has_dynamic']) && !$this->_aBillInfo['is_has_dynamic']) {
            return '';
        }

        if ($this->_isShowDynamicMemberProtectMsg() && !$bMultiPremiumEstimate) {
            $aDynamicPrivileges = $this->_aPassengerInfo['member_profile']['privileges']['dpa'] ?? [];
            if (isset($aDynamicPrivileges['name'], $aDynamicPrivileges['frontend']['reason'])) {
                //专车动态加价增加显示动调价格,"已抵*元"
                $amount = round($this->_aBillInfo['dynamic_price_without_member_capping'] - $this->_aBillInfo['member_dynamic_capping'], 2);
                if ($amount > 0 && Constants\OrderSystem::PRODUCT_ID_DEFAULT == $this->_aOrderInfo['product_id']) {
                    $price = Language::replaceTag(
                        $this->_aConfig['member_dynamic_save_amount'],
                        [
                            'amount'        => $amount,
                            'currency_unit' => $this->_sCurrencyUnit,
                        ]
                    );
                }

                return $aDynamicPrivileges['frontend']['reason'].$aDynamicPrivileges['name'].$price;
            }
        }

        $sDynamicDesc = '';
        if (0 == $this->_aBillInfo['dynamic_diff_price']) {
            return $sDynamicDesc;
        }

        if ($this->_aBillInfo['dynamic_diff_price'] < 0) {
            if (BizUtils\Product::getProductConfig('negative_dynamic_times', $this->_aOrderInfo)) {
                return $sDynamicDesc;
            }

            return Language::replaceTag(NuwaConfig::text('config_text', 'estimate_tab_discount_title_v2'), array('currency_symbol' => $this->_sCurrencySymbol, 'num' => NumberHelper::numberFormatDisplay(abs($this->_aBillInfo['dynamic_diff_price'])), 'currency_unit' => $this->_sCurrencyUnit));
        }

        if ($this->_aBillInfo['is_hit_dynamic_capping']
            && Constants\OrderSystem::ORIGIN_ID_DIDI == $this->_aCommonInfo['origin_id']
        ) {
            return Language::replaceTag($this->_aConfig['dynamic_title_v2'], array('currency_symbol' => $this->_sCurrencySymbol, 'num' => NumberHelper::numberFormatDisplay($this->_aBillInfo['dynamic_diff_price']), 'currency_unit' => $this->_sCurrencyUnit));
        } elseif (1 == $this->_aBillInfo['dynamic_info']['if_use_times'] && $this->_aBillInfo['dynamic_info']['dynamic_times'] > 0) {
            // dynamic abtest start
            if (!\BizLib\Utils\Product::isUber($this->_aOrderInfo['product_id'])) {
                $oApolloV2      = new ApolloV2();
                $oFeatureToggle = $oApolloV2->featureToggle(
                    'dp_tixingyemianAB',
                    [
                        'key'   => $this->_aPassengerInfo['pid'],
                        'phone' => $this->_aPassengerInfo['phone'],
                        'city'  => $this->_aOrderInfo['area'],
                    ]
                );
                if ($oFeatureToggle->allow()) {
                    //实验下线需保留,return
                    UtilHelper::writeLogForApollo($oFeatureToggle->getStainLog());
                    if ('treatment_group' == $oFeatureToggle->getGroupName()) {
                        return Language::replaceTag($this->_aConfig['dynamic_multiple_title'], ['num' => NumberHelper::getLocalFormatNumber($this->_aBillInfo['dynamic_info']['dynamic_times'] + 1, $this->_aOrderInfo['product_id']), ]);
                    }
                }
            }

            // dynamic abtest end
            return Language::replaceTag($this->_aConfig['dynamic_multiple_title_v2'], array('num' => NumberHelper::getLocalFormatNumber($this->_aBillInfo['dynamic_info']['dynamic_times'], $this->_aOrderInfo['product_id'])));
        } else {
            return Language::replaceTag($this->_aConfig['dynamic_title_v2'], array('currency_symbol' => $this->_sCurrencySymbol, 'num' => NumberHelper::numberFormatDisplay($this->_aBillInfo['dynamic_diff_price']), 'currency_unit' => $this->_sCurrencyUnit));
        }
    }

    /**
     * 是否显示动调会员保护信息.
     * @return bool
     */
    private function _isShowDynamicMemberProtectMsg() {

        $aMember = $this->_aPassengerInfo['member_profile'] ?? [];

        return $aMember && $this->_aBillInfo['member_dynamic_capping'] >= 0 && $this->_aBillInfo['is_hit_member_capping'] > 0;
    }




    /**
     * 返回 定制化服务 价格信息.
     *
     * @param array $aCustomInfo aCustomInfo
     *
     * @return string
     */
    private function _getCusItomServiceDesc($aCustomInfo) {
        if (empty($aCustomInfo) || !is_array($aCustomInfo) || !isset($aCustomInfo['items'])) {
            return '';
        }

        if (!isset($aCustomInfo['service_fee']) || 0 == $aCustomInfo['service_fee']) {
            return '';
        }

        if (empty($aCustomInfo['items']) || !is_array($aCustomInfo['items']) || !isset($aCustomInfo['items'][0]['id'])) {
            return '';
        }

        //只有一个服务的情况
        if (1 == count($aCustomInfo['items'])) {
            $aCustom = $aCustomInfo['items'][0];

            return Language::replaceTag(
                $this->_aConfig['custom_price_desc_one_v2'],
                array(
                    'title'           => $aCustom['title'],
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'fee'             => $aCustom['cost'],
                    'currency_unit'   => $this->_sCurrencyUnit,
                )
            );
        } elseif (count($aCustomInfo['items']) > 1) { //多个服务的情况
            $fTotalCost = $aCustomInfo['service_fee'];

            return Language::replaceTag(
                $this->_aConfig['custom_price_desc_more_v2'],
                array(
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'fee'             => $fTotalCost,
                    'currency_unit'   => $this->_sCurrencyUnit,
                )
            );
        }
    }


    /**
     * 展示原始非
     * @return mixed
     */
    public function getOriginFeeDesc() {
        // 增加原始费
        $bHitMemberDynamicProtect = !empty($this->_aActivityInfo['without_member_protect_fee']);
        //1. 会员保护加强展示
        $aCoupon       = $this->_aActivityInfo['estimate_detail'] ?? [];
        $bHasCoupon    = isset($aCoupon['title'], $aCoupon['value']);
        $iCouponAmount = 0;
        if ($bHasCoupon && $aCoupon['amount'] > 0) {
            $iCouponAmount = $aCoupon['amount'];
        }

        if ($bHitMemberDynamicProtect) {
            $fOriginFee     = (float)($this->_aActivityInfo['without_member_protect_fee']);
            $sOriginFeeDesc = Language::replaceTag(
                $this->_aEstimateText['origin_fee_msg'],
                [
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'num'             => sprintf('%.1f', (float)($fOriginFee + $iCouponAmount)),
                    'currency_unit'   => $this->_sCurrencyUnit,
                ]
            );
            $aPriceDescIcon = '';
        }

        //2. 非会员有券加强展示
        $aCoupon     = $this->_aActivityInfo['estimate_detail'] ?? [];
        $bHasCoupon  = isset($aCoupon['title'], $aCoupon['value']);
        $iHasDynamic = $this->_aBillInfo['is_has_dynamic'] ?? 0;
        //专车且有券,未命中动调保护
        if (!$bHitMemberDynamicProtect && !$iHasDynamic && $bHasCoupon) {
            $fOriginFee     = (float)($this->_aActivityInfo['estimate_fee'] + $aCoupon['amount']);
            $sOriginFeeDesc = Language::replaceTag(
                $this->_aEstimateText['origin_fee_msg'],
                [
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'num'             => sprintf('%.1f', (float)($fOriginFee)),
                    'currency_unit'   => $this->_sCurrencyUnit,
                ]
            );
        }

        return $sOriginFeeDesc;
    }

    /**
     * 专车新表单高速费
     * @return mixed|string
     */
    public function getPremiumHighWayFeeDesc() {
        $sHighwayFeeDesc = '';
        if (isset($this->_aBillInfo['highway_fee']) && (float)($this->_aBillInfo['highway_fee']) > 0) {
            $sHighwayFeeDesc = Language::replaceTag(
                $this->_aEstimateText['highway_fee_msg'],
                [
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'highway_fee'     => NumberHelper::numberFormatDisplay($this->_aBillInfo['highway_fee']),
                    'currency_unit'   => $this->_sCurrencyUnit,
                ]
            );
        }

        return $sHighwayFeeDesc;
    }

    /**
     * 获取小费相关文案
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getTipsDesc($aPriceDesc, $aPriceDescIcon) {
        if (!empty($this->_aActivityInfo['tip'])) {
            $sTipFeeTag       = version_compare($this->_aCommonInfo['app_version'], '5.2.8') >= 0 ? $this->_aConfig['tip_fee_desc_v2'] : $this->_aConfig['tip_fee_desc'];
            $aPriceDesc[]     = Language::replaceTag(
                $sTipFeeTag,
                array(
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'num'             => NumberHelper::numberFormatDisplay($this->_aActivityInfo['tip']),
                    'currency_unit'   => $this->_sCurrencyUnit,
                )
            );
            $aPriceDescIcon[] = '';
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 专车新表单 会员溢价保护与券逻辑
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @param bool  $bFolderShow    是否有多项优惠项组合
     * @return array
     */
    public function getPremiumMemberProtectAndCouponDesc($aPriceDesc, $aPriceDescIcon, $bFolderShow) {
        // 会员保护
        if ($this->_isShowDynamicMemberProtectMsg()) {
            $aDynamicPrivileges = $this->_aPassengerInfo['member_profile']['privileges']['dpa'] ?? [];
            if (isset($aDynamicPrivileges['name'], $aDynamicPrivileges['frontend']['reason'])) {
                $sDpaPrivName = $aDynamicPrivileges['name'];
                //专车动态加价增加显示动调价格,"已抵*元"
                //$fMemberProtectFee = round($this->_aBillInfo['dynamic_price_without_member_capping'] - $this->_aBillInfo['member_dynamic_capping'], 2);
                //溢价保护专豪统一使用dynamic_member_reduce字段
                $fMemberProtectFee = $this->_aBillInfo['dynamic_member_reduce'] < 0 ? -$this->_aBillInfo['dynamic_member_reduce'] : $this->_aBillInfo['dynamic_member_reduce'];
                if ($fMemberProtectFee > 0) {
                    $sMemberProtectDesc = Language::replaceTag(
                        $this->_aEstimateText['member_protect_msg'],
                        [
                            'priv_name'       => $sDpaPrivName,
                            'currency_symbol' => $this->_sCurrencySymbol,
                            'amount'          => $fMemberProtectFee,
                            'currency_unit'   => $this->_sCurrencyUnit,
                        ]
                    );
                }

                $sMemberProtectDescIcon = $this->_aPassengerInfo['member_profile']['level_icon'];
            }
        }

        //券
        $aCoupon = $this->_aActivityInfo['estimate_detail'] ?? [];
        if (isset($aCoupon['title'], $aCoupon['value']) && !$bFolderShow) {
            $fCouponFee      = $aCoupon['amount'];
            $sCouponDesc     = Language::replaceTag(
                $this->_aConfig['coupon_title_with_brackets'],
                array(
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'fee'             => NumberHelper::numberFormatDisplay($aCoupon['amount']),
                    'currency_unit'   => $this->_sCurrencyUnit,
                    'title'           => $aCoupon['title'],
                )
            );
            $sCouponDescIcon = '';
        }

        //溢价保护 + 券
        if ((float)($fCouponFee) > 0 && (float)($fMemberProtectFee) > 0) {
            $fMemberProtectCouponFee      = $fMemberProtectFee + $fCouponFee;
            $sMemberProtectCouponDesc     = Language::replaceTag(
                $this->_aEstimateText['member_protect_coupon_msg'],
                [
                    'priv_name'       => $sDpaPrivName,
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'num'             => $fMemberProtectCouponFee,
                    'currency_unit'   => $this->_sCurrencyUnit,
                ]
            );
            $sMemberProtectCouponDescIcon = $sMemberProtectDescIcon;
        }

        if (!empty($sMemberProtectCouponDesc)) {
            $aPriceDesc[]     = $sMemberProtectCouponDesc;
            $aPriceDescIcon[] = $sMemberProtectCouponDescIcon;
        } else {
            if (!empty($sMemberProtectDesc)) {
                $aPriceDesc[]     = $sMemberProtectDesc;
                $aPriceDescIcon[] = $sMemberProtectDescIcon;
            }

            if (!empty($sCouponDesc)) {
                $aPriceDesc[]     = $sCouponDesc;
                $aPriceDescIcon[] = $sCouponDescIcon;
            }
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon $aPriceDescIcon
     * @param array $aPriceInfoList $aPriceInfoList
     * @return array
     */
    public function getUnioneEstimatePriceDesc($aPriceDesc, $aPriceDescIcon, $aPriceInfoList) {
        if (Util::checkUnioneShowFeeInfo($this->_aInfo)) {
            $sFeeMsg         = $this->_aConfig['taxi_noncarpool_estimate'];
            $aUnioneDescInfo = Util::getUnioneShowFeeInfo($this->_aInfo,$this->_sCurrencyUnit);
            $aUnioneDescInfo['content'] = $sFeeMsg;
            if (!empty($aUnioneDescInfo)) {
                $aPriceDesc[]     = $aUnioneDescInfo['content'] ?? '';
                $aPriceDescIcon[] = '';
                $aPriceInfoList[] = $aUnioneDescInfo;
            }
        }

        return [$aPriceDesc, $aPriceDescIcon, $aPriceInfoList];
    }

    /**
     * @return mixed
     */
    // 注释-待删
//    public function getUnioneCarpoolPriceInfoDesc() {
//        // 出租车拼车开城，出租车透出价格信息。
//        $aUnionePriceInfoDesc = json_decode(
//            Language::getTextFromDcmp(
//                'estimate_price_info_desc-unione_price',
//                [
//                    'fee'           => NumberHelper::numberFormatDisplay($this->_aActivityInfo['estimate_fee']),
//                    'currency_unit' => $this->_sCurrencyUnit,
//                ]
//            )
//        );
//        return $aUnionePriceInfoDesc;
//    }

    /**
     * 优选出租车开城，透出价格信息。
     * @return bool|mixed|null
     */
    // 注释-待删
//    public function getUnioneYouxuanPriceInfoDesc() {
//        $fPrice = $this->_aActivityInfo['estimate_fee'] ?? $this->_aBillInfo['total_fee'];
//        $aUnioneYouxuanPriceInfoDesc = NuwaConfig::text(
//            'youxuan_taxi',
//            'price_desc_info_new',
//            [
//                'fee'           => NumberHelper::numberFormatDisplay($fPrice),
//                'currency_unit' => $this->_sCurrencyUnit,
//            ]
//        );
//        return $aUnioneYouxuanPriceInfoDesc;
//    }
}
