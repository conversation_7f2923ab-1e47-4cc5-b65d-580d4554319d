<?php
/**
 * <AUTHOR> <<EMAIL>>
 */

namespace PreSale\Logics\luxEstimatePrice\multiResponse;

use <PERSON>izCom<PERSON>\Constants\OrderNTuple;
use BizLib\Constants\OrderSystem;
use Nuwa\ApolloSDK\Apollo;

/**
 * Class Util
 * @package PreSale\Logics\estimatePrice\multiResponse
 */
class Util
{
    const SHOW_TAB_ALL = 0;

    const SHOW_TAB_CAR = 1;
    /**
     * 是否为豪华车包车
     * @param array $aInfo aInfo
     * @return bool
     */
    public static function isLuxRent($aInfo) {
        $iProductId = $aInfo['order_info']['product_id'];
        $sCarLevel  = $aInfo['order_info']['require_level'];
        $iComboType = $aInfo['bill_info']['product_infos'][$sCarLevel]['combo_type'];
        return (OrderNTuple::PRODUCT_ID_FIRST_CLASS_CAR == $iProductId) && (OrderSystem::TYPE_COMBO_RENTED == $iComboType);
    }

    /**
     * @param array $aInfo aInfo
     * @return bool
     */
    public static function isDesignatedDriver($aInfo) {
        return (!empty($aInfo['order_info']['designated_driver']) || !empty($aInfo['order_info']['designated_driver_info']) || !empty($aInfo['order_info']['designated_driver_tag']));
    }

    /**
     * 此方法的背景是6.0英文版暂不支持豪华车偏好设置，如果之后英文版支持偏好设置，可以直接通过Apollo实现而不需要写代码上线
     * @param array $aParams $aParams
     * @return bool
     */
    public static function isSupportPreferInfo($aParams) {
        $oToggle = Apollo::getInstance()->featureToggle(
            'gs_is_support_prefer_info_toggle',
            [
                'key'           => $aParams['pid'],
                'passenger_id'  => $aParams['pid'],
                'city'          => $aParams['area'],
                'phone'         => $aParams['phone'],
                'lang'          => $aParams['lang'],
                'access_key_id' => $aParams['access_key_id'],
                'app_version'   => $aParams['app_version'],
            ]
        );
        return $oToggle->allow();
    }

    /**
     * 获取展示tab （屏蔽选司务员入口）
     * @param array $aParams $aParams
     * @return int
     */
    public static function getShowTab($aParams) {
        if ((new \Nuwa\ApolloSDK\Apollo())->featureToggle(
            'pre_sale_v2_lux_assign_driver_close',
            array(
                'page_type'     => $aParams['page_type'],
                'city'          => $aParams['area'],
                'district'      => $aParams['district'],
                'access_key_id' => $aParams['access_key_id'],
                'app_version'   => $aParams['app_version'],
            )
        )->allow()
        ) {
            return self::SHOW_TAB_CAR;
        }

        return self::SHOW_TAB_ALL;
    }
}
