<?php

namespace PreSale\Logics\luxEstimatePrice\multiResponse;

use BizCommon\Models\Cache\EstimatePrice;
use BizLib\Utils\ApolloHelper;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\Registry;
use Disf\SPL\Trace;
use PreSale\Logics\luxEstimatePrice\multiResponse\component;
use PreSale\Models\rpc\LuxRpc;
use PreSale\Logics\luxEstimatePrice\multiResponse\component\CarLevelInfo;
use PreSale\Logics\estimatePrice\multiResponse\Util as PreSaleUtils;

/**
 * Class MainRender
 * @package PreSale\Logics\estimatePrice\multiResponse
 */
class MainRender
{

    const OLD_LUXURY_PREFER_PAGE = 1;

    const NEW_LUXURY_PREFER_PAGE = 2;

    /**
     * @var
     */
    protected static $_oInstance;

    /**
     * @var
     */
    protected $_aInfos;
    /**
     * @var
     */
    protected $_aPreferInfo;

    protected $_iDefaultSelectTab;


    /**
     * MainRender constructor.
     * @param array $aInfos      品类信息
     * @param array $aPreferInfo 个性化数据
     */
    private function __construct($aInfos, $aPreferInfo) {
        $this->_aInfos            = $aInfos;
        $this->_aPreferInfo       = $aPreferInfo;
        $this->_iDefaultSelectTab = 1;
        if (!empty($aPreferInfo['prefer_request_source']) && $aPreferInfo['prefer_request_source'] == self::OLD_LUXURY_PREFER_PAGE) {
            $this->_aLuxPageConfig    = NuwaConfig::text('config_text', 'lux_multi_estimate_page_conf');
        } elseif (!empty($aPreferInfo['prefer_request_source']) && $aPreferInfo['prefer_request_source'] == self::NEW_LUXURY_PREFER_PAGE) {
            $this->_aLuxPageConfig    = NuwaConfig::text('config_text', 'lux_multi_estimate_page_conf_test');
        }
    }

    /**
     * @param array $aInfos      品类信息
     * @param array $aPreferInfo 个性化数据
     * @return MainRender
     */
    public static function getInstance($aInfos, $aPreferInfo) {
        if (empty(self::$_oInstance)) {
            self::$_oInstance = new self($aInfos, $aPreferInfo);
        }

        return self::$_oInstance;
    }

    /**
     * @return array|bool|mixed
     */
    public function multiExecute() {
        $aMultiResponse = UtilsCommon::getErrMsg(GLOBAL_SUCCESS);
        //渲染前置数据处理和准备工作
        $this->preRender();
        //主渲染逻辑，产生各品类的预估数据
        $aMultiResponse['data'] = $this->buildEstimateData();
        //渲染品类数据之外的全局数据
        $aMultiResponse = $this->buildExternalData($aMultiResponse);

        return $aMultiResponse;
    }

    /**
     * @return void
     */
    public function preRender() {
        //获取车型相关信息
        CarLevelInfo::getInstance()->loadCarLevelInfo();
    }

    /**
     * array $aMultiResponse 批量返回信息
     * @return array
     */
    public function buildEstimateData() {
        $aComponents = [
            component\introMsg\Handler::class,
            component\feeDescInfo\Handler::class,
            component\basicFeeMsg\Handler::class,
            component\productInfo\Handler::class,
            component\driverInfo\Handler::class,
        ];

        $aCarEstimateData    = [];
        $aDriverEstimateData = [];
        $aEstimateData       = [];
        if (is_array($this->_aInfos)) {
            foreach ($this->_aInfos as $aInfo) {
                $aResponse = [];
                foreach ($aComponents as $handler) {
                    $oComponent = $handler::select($aInfo);
                    if (!empty($oComponent)) {
                        $aResponse = $oComponent->build($aResponse);
                    }
                }

                //由于error_status值固定为0，且不依赖于aInfo，直接放在这里赋值
                $aResponse['error_status'] = 0;
                if ($aResponse['is_driver_tab']) {
                    $aDriverEstimateData[] = $aResponse;
                } else {
                    $aCarEstimateData[] = $aResponse;
                }
            }
        }

        $aCarEstimateData    = $this->sortCarItem($aCarEstimateData);
        $aDriverEstimateData = $this->dealDriverItem($aDriverEstimateData);
        $aEstimateData['estimate_car_level_data']    = $this->_setCarDefault($aCarEstimateData);
        $aEstimateData['estimate_driver_level_data'] = $this->_setDriverDefault($aDriverEstimateData);
        if (Util::getShowTab(
            array(
                'page_type'     => $this->_aInfos[0]['common_info']['page_type'],
                'area'          => $this->_aInfos[0]['order_info']['area'],
                'district'      => $this->_aInfos[0]['order_info']['district'],
                'access_key_id' => $this->_aInfos[0]['common_info']['access_key_id'],
                'app_version'   => $this->_aInfos[0]['common_info']['app_version'],
            )
        )
        ) {
            unset($aEstimateData['estimate_driver_level_data']);
        }

        $aEstimateData['default_select_tab'] = 1;

        //设置estimate id缓存
        $this->_setEstimateIdCache($aEstimateData);
        return $aEstimateData;
    }


    /**
     * @param array $aDriverEstimateData aDriverEstimateData
     * @return array
     */
    public function dealDriverItem($aDriverEstimateData) {
        if (empty($aDriverEstimateData)) {
            return $aDriverEstimateData;
        }

        $aAssignDriverSortedList = LuxRpc::getInstance()->getAssignDriverSortedList();
        if (empty($aAssignDriverSortedList)) {
            return $aDriverEstimateData;
        }

        $aRet = [];
        $aDriverEstimateData = array_column($aDriverEstimateData, null, 'driver_id');
        if (!empty($aDriverEstimateData[0])) {
            $aRet[] = $aDriverEstimateData[0];
        }

        foreach ($aAssignDriverSortedList as $aDriver) {
            if (!empty($aDriver['driverId']) && !empty($aDriverEstimateData[$aDriver['driverId']])) {
                $aRet[] = $aDriverEstimateData[$aDriver['driverId']];
            }
        }

        return $aRet;
    }


    /**
     * @param array $aCarEstimateData aCarEstimateData
     * @return array
     */
    public function sortCarItem($aCarEstimateData) {
        if (empty($aCarEstimateData)) {
            return $aCarEstimateData;
        }

        $aCarEstimateData = array_column($aCarEstimateData, null, 'product_category');
        $aSortConfs       = ApolloHelper::getConfigContent('lux_multi_estimate_sort', 'lux_car_sort');
        if (empty($aSortConfs) || empty($aSortConfs['lux_car_sort'])) {
            return $aCarEstimateData;
        }

        $aKeySortConfs = [];
        foreach ($aSortConfs['lux_car_sort'] as $aConf) {
            $aKeySortConfs[$aConf['key']] = $aConf['value'];
        }

        $iArea = $this->_aInfos[0]['order_info']['area'];
        if (isset($aKeySortConfs[$iArea]) && !empty($aKeySortConfs[$iArea])) {
            $aSortConf = $aKeySortConfs[$iArea];
        } elseif (isset($aKeySortConfs['default']) && !empty($aKeySortConfs['default'])) {
            $aSortConf = $aKeySortConfs['default'];
        }

        if (empty($aSortConf)) {
            return $aCarEstimateData;
        }

        $aSortedItems = [];
        foreach ($aSortConf as $k => $v) {
            if (isset($aCarEstimateData[$v]) && !empty($aCarEstimateData[$v])) {
                $aSortedItems[] = $aCarEstimateData[$v];
            }
        }

        return $aSortedItems;
    }

    /**
     * @param array $aData 批量返回信息
     * @return array
     */
    private function _setCarDefault($aData) {
        if (empty($aData)) {
            return $aData;
        }

        if (!empty($this->_aInfos[0]['common_info']['luxury_select_carlevels'])) {
            $sCarLevel = json_decode($this->_aInfos[0]['common_info']['luxury_select_carlevels'], true);
            foreach ($aData as $key => $val) {
                if ($val['require_level'] == $sCarLevel[0]) {
                    $aData[$key]['is_default'] = 1;
                    $this->_iDefaultSelectTab  = 1;
                } else {
                    $aData[$key]['is_default'] = 0;
                }
            }

            return $aData;
        } else {
            foreach ($aData as $key => $val) {
                $aData[$key]['is_default'] = 0;
            }

            $aData[0]['is_default'] = 1;
            return $aData;
        }
    }

    /**
     * @param array $aData 批量返回信息
     * @return array
     */
    private function _setDriverDefault($aData) {
        if (empty($aData)) {
            return $aData;
        }

        if ('' != $this->_aInfos[0]['common_info']['luxury_select_driver']) {
            $sDriverId = $this->_aInfos[0]['common_info']['luxury_select_driver'];
            foreach ($aData as $key => $val) {
                if ($val['driver_id'] == $sDriverId) {
                    $aData[$key]['is_default'] = 1;
                    $this->_iDefaultSelectTab  = 2;
                } else {
                    $aData[$key]['is_default'] = 0;
                }
            }

            return $aData;
        } else {
            foreach ($aData as $key => $val) {
                $aData[$key]['is_default'] = 0;
            }

            $aData[0]['is_default'] = 1;
            return $aData;
        }
    }

    /**
     * @param array $aEstimateData 预估数据
     * 缓存 estimate id
     * @return void
     */
    private function _setEstimateIdCache($aEstimateData) {
        foreach ($this->_aInfos as $aInfo) {
            $iPid = $aInfo['passenger_info']['pid'];
            break;
        }

        foreach ($aEstimateData as $item) {
            if (1 == $item['is_default']) {
                $sEstimateID = $item['estimate_id'];
                break;
            }
        }

        if (empty($iPid) || empty($sEstimateID)) {
            return;
        }

        if (!Registry::has(SET_ESTIMATE_ID_REGISTRY_KEY)) {
            EstimatePrice::getInstance()->setSelectedEstimateId($sEstimateID, $iPid);
            EstimatePrice::getInstance()->setEstimateId($sEstimateID, $iPid);
            Registry::set(SET_ESTIMATE_ID_REGISTRY_KEY, 1);
        }
    }

    /**
     * @param array $aMultiResponse 批量返回数据
     * @return mixed
     */
    public function buildExternalData($aMultiResponse) {
        $aInfo   = current($this->_aInfos);
        $aParams = array_merge($aInfo['order_info'], $aInfo['common_info'], $aInfo['passenger_info']);
        //用户偏好信息，豪华车英文版暂不支持偏好设置
        if (!Util::isSupportPreferInfo($aParams)
            || empty($this->_aPreferInfo)
        ) {
            $aMultiResponse['data']['prefer_info']['prefer_option'] = [];
            $aMultiResponse['data']['prefer_info']['title']         = '';
            $aMultiResponse['data']['prefer_info']['remark']        = '';
        } else {
            $aMultiResponse['data']['prefer_info']['prefer_option'] = $this->_aPreferInfo['prefer_option'];
            $aMultiResponse['data']['prefer_info']['title']         = $this->_aPreferInfo['title'];
            $aMultiResponse['data']['prefer_info']['remark']        = $this->_aPreferInfo['remark'];
            $aMultiResponse['data']['prefer_info']['head']          = $this->_aPreferInfo['head'];
            $aMultiResponse['data']['prefer_info']['head_link']     = $this->_aPreferInfo['head_link'];
            $aMultiResponse['data']['prefer_info']['is_support_title']  = $this->_aPreferInfo['is_support_title'];
            $aMultiResponse['data']['prefer_info']['is_support_remark'] = $this->_aPreferInfo['is_support_remark'];
            $aMultiResponse['data']['prefer_info']['is_im_direct_send'] = $this->_aPreferInfo['is_im_direct_send'];
            $aMultiResponse['data']['prefer_info']['common_expressions'] = $this->_aPreferInfo['common_expressions'];
            $aMultiResponse['data']['prefer_info']['is_remember_history_prefer'] = $this->_aPreferInfo['is_remember_history_prefer'];
            $aMultiResponse['data']['prefer_info']['is_remember_history_prefer_title'] =  $this->_aPreferInfo['is_remember_history_prefer_title'];
        }
        $aPreferInfo = $this->_aPreferInfo;
        //页面信息
        if (!empty($aPreferInfo['prefer_request_source']) && $aPreferInfo['prefer_request_source'] == self::OLD_LUXURY_PREFER_PAGE) {
            $aMultiResponse['data']['start_bg_color'] = $this->_aLuxPageConfig['start_bg_color'];
            $aMultiResponse['data']['end_bg_color']   = $this->_aLuxPageConfig['end_bg_color'];
            $aMultiResponse['data']['title']          = $this->_aLuxPageConfig['title'];
            $aMultiResponse['data']['sub_title']      = $this->_aLuxPageConfig['sub_title'];
            $aMultiResponse['data']['head_img']       = $this->_aLuxPageConfig['head_img'];
        } elseif (!empty($aPreferInfo['prefer_request_source']) && $aPreferInfo['prefer_request_source'] == self::NEW_LUXURY_PREFER_PAGE) {
            $aMultiResponse['data']['head_img']       = $this->_aLuxPageConfig['head_img_v2'];
        }
        $aMultiResponse['data']['theme']          = 2; // 金色主题
        $aMultiResponse['data']['disable']        = 0;
        //公共信息
        $aMultiResponse['data']['estimate_trace_id']  = Trace::traceId();
        $aMultiResponse['data']['default_select_tab'] = $this->_iDefaultSelectTab;
        //展示tab
        $aMultiResponse['data']['show_tab'] = Util::getShowTab($aParams);

        return $aMultiResponse;
    }
}
