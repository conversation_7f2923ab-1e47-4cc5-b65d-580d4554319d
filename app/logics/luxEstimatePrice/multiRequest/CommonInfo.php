<?php
/**
 * Created by PhpStorm.
 * User: didi
 * Date: 2020/2/8
 * Time: 2:32 PM
 * <AUTHOR> <<EMAIL>>
 */

namespace PreSale\Logics\luxEstimatePrice\multiRequest;

use BizLib\Utils as BizUtils;
use Dirpc\SDK\PreSale\LuxMultiEstimatePriceRequest as Request;
use PreSale\Logics\estimatePrice\PopefsLogic;
use BizLib\Log as NuwaLog;

/**
 * Class CommonInfo
 * @package PreSale\Logics\luxEstimatePrice\multiRequest
 */
class CommonInfo
{
    const TAB_TYPE_CAR = 0; //分卡片加载

    const TAB_TYPE_DRIVER = 1;//分卡片加载

    const TAB_TYPE_ALL = 2;//选车型/选司务员 一起预估

    const SOURCE_ID_ANY_LUXURY = 7; // 任意豪华车

    const SOURCE_ID_SIX_SEAT_LUXURY = 8; // 六座豪华车

    //端版本
    /**
     * @var string
     */
    public $sAppVersion;
    //端类型client_type
    /**
     * @var int
     */
    public $iClientType;
    //端来源
    /**
     * @var int
     */
    public $iAccessKeyID;
    //端语言
    /**
     * @var string
     */
    public $sLang;
    //渠道号
    /**
     * @var int
     */
    public $sChannel;
    //反作弊标识
    /**
     * @var string
     */
    public $sA3Token;
    //设备分辨率
    /**
     * @var string
     */
    public $sPixels;
    //来源id
    /**
     * @var int
     */
    public $iOriginId;
    //终端id
    /**
     * @var string
     */
    public $iTerminalId;
    //设备号
    /**
     * @var string
     */
    public $sImei;
    //b2b来源
    /**
     * @var bool
     */
    public $bIsFromB2b;
    //webApp来源
    /**
     * @var bool
     */
    public $bIsFromWebApp;
    //来源
    /**
     * @var string
     */
    public $sFrom;
    //暂时还有用到
    /**
     * @var int
     */
    public $iPlatformType;
    //popeFs Result
    /**
     * @var
     */
    public $aPopefsResult;
    //场景全页面类型
    /**
     * @var int
     */
    public $iPageType;
    //选择车型
    /**
     * @var string
     */
    public $sLuxrySelectCarlevels;
    //选择司机
    /**
     * @var string
     */
    public $sLuxrySelectDriver;
    //展示tab
    /**
     * @var int
     */
    public $iTabType;
    // 资源ID
    /**
     * @var int
     */
    public $iSourceId;


    /**
     * CommonInfo constructor.
     * @param Request $oEstimateRequest 预估请求参数
     */
    public function __construct(Request $oEstimateRequest) {
        $this->sAppVersion   = $oEstimateRequest->getAppVersion();
        $this->sLang         = $oEstimateRequest->getLang();
        $this->sChannel      = $oEstimateRequest->getChannel();
        $this->iClientType   = $oEstimateRequest->getClientType();
        $this->iAccessKeyID  = $oEstimateRequest->getAccessKeyId();
        $this->sA3Token      = $oEstimateRequest->getA3Token();
        $this->sPixels       = $oEstimateRequest->getPixels();
        $this->iOriginId     = $oEstimateRequest->getOriginId();
        $this->iTerminalId   = $oEstimateRequest->getTerminalId();
        $this->sImei         = $oEstimateRequest->getImei();
        $this->iPlatformType = $oEstimateRequest->getPlatformType();
        $this->bIsFromB2b    = BizUtils\Common::fromB2B($this->iClientType);
        $this->bIsFromWebApp = BizUtils\Common::fromWebapp($this->iClientType);
        $this->sFrom         = $oEstimateRequest->getFrom();
        $this->iPageType     = $oEstimateRequest->getPageType();
        $this->sLuxrySelectCarlevels = $oEstimateRequest->getLuxurySelectCarlevels();
        $this->sLuxrySelectDriver    = $oEstimateRequest->getLuxurySelectDriver();
        $this->iTabType = $this->_getTabType($oEstimateRequest);
        $this->iSourceId = $oEstimateRequest->getSourceId();
        // 老偏好设置界面 端没传source-id 后端给兜个底
        if (empty($this->iSourceId)) {
            $bIsSixLux = (new \Nuwa\ApolloSDK\apollo())->featureToggle(
                'six_seat_lux_judge',
                array(
                    'product_category' => $oEstimateRequest->getProductCategory(),
                    'require_level'   => $oEstimateRequest->getRequireLevel(),
                ));
            if ($bIsSixLux->allow()) {
                $this->iSourceId = self::SOURCE_ID_SIX_SEAT_LUXURY;
            } else {
                $this->iSourceId = self::SOURCE_ID_ANY_LUXURY;
            }
        }
    }

    /**
     * 获取预估tab
     * @param Request $oEstimateRequest 预估请求参数
     * @return int
     */
    private function _getTabType($oEstimateRequest) {
        //开关控制
        //6.0之前顶导全部返回
        if ((new \Nuwa\ApolloSDK\Apollo())->featureToggle(
            'lux_multi_all_tab',
            array(
                'app_version' => $oEstimateRequest->getAppVersion(),
                'page_type'   => $oEstimateRequest->getPageType(),
                'business_id' => $oEstimateRequest->getBusinessId(),
            )
        )->allow()
        ) {
            return self::TAB_TYPE_ALL;
        }

        //展示哪个顶导是由tab_type和车型司机一起决定的
        $iTabType = $oEstimateRequest->getTabType();
        $sLuxrySelectCarlevels = $oEstimateRequest->getLuxurySelectCarlevels();
        $sLuxrySelectDriver    = $oEstimateRequest->getLuxurySelectDriver();
        //tab_type优先级最高
        if ($iTabType) {
            return self::TAB_TYPE_DRIVER;
        }

        //判断是否存在默认选中
        if (empty($sLuxrySelectCarlevels) && '' == $sLuxrySelectDriver) {
            return self::TAB_TYPE_CAR;
        }

        //理论上这个分支不会进入, 做下兜底
        if (!empty($sLuxrySelectCarlevels) && '' != $sLuxrySelectDriver) {
            return self::TAB_TYPE_CAR;
        }

        if (!empty($sLuxrySelectCarlevels)) {
            return self::TAB_TYPE_CAR;
        }

        if ('' != $sLuxrySelectDriver) {
            return self::TAB_TYPE_DRIVER;
        }

        return self::TAB_TYPE_CAR;
    }
}
