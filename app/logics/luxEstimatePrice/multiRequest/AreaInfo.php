<?php
/**
 * Created by PhpStorm.
 * User: didi
 * Date: 2020/2/8
 * Time: 2:32 PM
 * <AUTHOR> <<EMAIL>>
 */

namespace PreSale\Logics\luxEstimatePrice\multiRequest;

use BizLib\Utils\Address;
use BizLib\Utils\MapHelper;
use Dirpc\SDK\PreSale\LuxMultiEstimatePriceRequest as Request;

/**
 * Class AreaInfo
 * @package PreSale\Logics\luxEstimatePrice\multiRequest
 */
class AreaInfo
{
    //当前经纬度
    /**
     * @var float
     */
    public $fCurLat;
    /**
     * @var float
     */
    public $fCurLng;
    //起点经纬度
    /**
     * @var float
     */
    public $fFromLat;
    /**
     * @var float
     */
    public $fFromLng;
    /**
     * @var string
     */
    public $sFromPoiId;
    /**
     * @var string
     */
    public $sFromPoiType;
    /**
     * @var string
     */
    public $sFromAddress;
    /**
     * @var string
     */
    public $sFromName;
    /**
     * @var
     */
    public $iFromCounty;
    //终点经纬度
    /**
     * @var float
     */
    public $fToLat;
    /**
     * @var float
     */
    public $fToLng;
    /**
     * @var
     */
    public $iToArea;
    /**
     * @var string
     */
    public $sToPoiId;
    /**
     * @var string
     */
    public $sToPoiType;
    /**
     * @var string
     */
    public $sToAddress;
    /**
     * @var string
     */
    public $sToName;
    /**
     * @var
     */
    public $iToCounty;

    /**
     * @var string
     */
    public $sMapType;
    /**
     * @var int
     */
    public $iDestPoiCode;
    /**
     * @var string
     */
    public $sDestPoiTag;
    /**
     * @var
     */
    public $iArea;
    /**
     * @var
     */
    public $iDistrict;

    /**
     * @var
     */
    public $sStartingName;
    /**
     * @var
     */
    public $sDestName;
    /**
     * @var
     */
    public $sAbstractDistrict;

    /**
     * AreaInfo constructor.
     * @param Request $oEstimateRequest 预估参数
     * @param array   $aAreaInfo        地域信息
     * @throws \Exception Exception
     */
    public function __construct(Request $oEstimateRequest, array $aAreaInfo) {
        $this->fCurLat      = $oEstimateRequest->getLat();
        $this->fCurLng      = $oEstimateRequest->getLng();
        $this->fFromLat     = $oEstimateRequest->getFromLat();
        $this->fFromLng     = $oEstimateRequest->getFromLng();
        $this->sFromPoiId   = $oEstimateRequest->getFromPoiId();
        $this->sFromPoiType = $oEstimateRequest->getFromPoiType();
        $this->sFromAddress = $oEstimateRequest->getFromAddress();
        $this->sFromName    = $oEstimateRequest->getFromName();

        $this->fToLat     = $oEstimateRequest->getToLat();
        $this->fToLng     = $oEstimateRequest->getToLng();
        $this->sToPoiId   = $oEstimateRequest->getToPoiId();
        $this->sToPoiType = $oEstimateRequest->getToPoiType();
        $this->sToAddress = $oEstimateRequest->getToAddress();
        $this->sToName    = $oEstimateRequest->getToName();

        if ($this->sFromAddress || $this->sFromName) {
            Address::merge($this->sStartingName, $this->sFromName, $this->sFromAddress);
        }

        if ($this->sToAddress || $this->sToName) {
            Address::merge($this->sDestName, $this->sToName, $this->sToAddress);
        }

        $this->sMapType     = $oEstimateRequest->getMaptype();
        $this->iDestPoiCode = (int)$oEstimateRequest->getDestPoiCode();
        $this->sDestPoiTag  = $oEstimateRequest->getDestPoiTag();
        $this->buildAreaInfo($aAreaInfo);
    }

    /**
     * @param array $aAreaInfo 地域信息
     * @throws \Exception Exception
     * @return void
     */
    public function buildAreaInfo(array $aAreaInfo) {
        if (!empty($aAreaInfo['from_area'])) {
            $aFromArea = $aAreaInfo['from_area'];
        } else {
            $aFromArea = MapHelper::getAreaInfoByLoc($this->fFromLng, $this->fFromLat);
        }

        if (!empty($aFromArea)) {
            $this->iArea       = $aFromArea['id'];
            $this->iDistrict   = $aFromArea['district'];
            $this->iFromCounty = $aFromArea['countyid'];

            $this->sAbstractDistrict = $this->iDistrict.','.$this->iFromCounty;
        }

        if (!empty($aAreaInfo['to_area'])) {
            $aToArea = $aAreaInfo['to_area'];
        } else {
            $aToArea = MapHelper::getAreaInfoByLoc($this->fToLng, $this->fToLat);
        }

        if (!empty($aToArea)) {
            $this->iToCounty = $aToArea['countyid'];
            $this->iToArea   = $aToArea['id'];
        }

        return;
    }
}
