<?php

namespace PreSale\Logics\LuxEstimatePrice\multiRequest;

use BizCommon\Logics\Luxury\Luxury;
use BizCommon\Models\Passenger\Passenger;
use BizLib\Client\EstimateDecisionClient;
use BizLib\Constants\Horae;
use BizLib\Utils\MapHelper;
use Dirpc\SDK\EstimateDecision\CommonInfoV2;
use Dirpc\SDK\EstimateDecision\ProductsReq;
use Dirpc\SDK\EstimateDecision\UserInfoV2;
use Dirpc\SDK\PreSale\LuxMultiEstimatePriceRequest as Request;
use PreSale\Logics\estimatePriceV2\EstimateDegradeCode;
use BizLib\ErrCode;
use PreSale\Logics\luxEstimatePrice\multiRequest\AthenaReq;
use PreSale\Logics\luxEstimatePrice\multiRequest\AreaInfo;
use PreSale\Logics\luxEstimatePrice\multiRequest\CommonInfo;
use PreSale\Logics\luxEstimatePrice\multiRequest\OrderInfo;
use PreSale\Logics\luxEstimatePrice\multiRequest\PassengerInfo;
use PreSale\Logics\luxEstimatePrice\multiResponse\Util;
use PreSale\Logics\scene\custom\CustomLogic;
use PreSale\Models\rpc\LuxRpc;
use BizLib\Constants\OrderSystem;
use BizLib\Utils\ApolloHelper;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\RespCode;
use BizLib\Config;
use BizLib\Exception\ExceptionWithResp;
use Xiaoju\Apollo\Apollo;
use BizCommon\Models\Rpc\UranusSystem;

/**
 * Created by PhpStorm.
 * User: didi
 * Date: 2020/2/10
 * Time: 9:58 AM
 */
class ProductList
{
    protected static $_oInstance = null;

    public $aProductList = [];

    private $_oRequest;

    private $_aBaseProductList;

    private $_aDDSProductList = [];

    private $_aAreaInfo;

    private $_aBasePassengerInfo;

    private $_iTabType;

    private $_iSourceId;

    /**
     * ProductList constructor.
     * @param Request $oRequest oRequest
     */
    public function __construct(Request $oRequest) {
        $this->_oRequest           = $oRequest;
        $this->_aBasePassengerInfo = Passenger::getInstance()->getPassengerByTokenFromPassport($oRequest->getToken());
        $this->_buildAreaInfo($oRequest);
        $this->_aBaseProductList = [];
    }

    /**
     * _buildAreaInfo
     * @param Request $oRequest oRequest
     * @throws ExceptionWithResp e
     * @return void
     */
    private function _buildAreaInfo(Request $oRequest) {
        $aFromArea = MapHelper::getAreaInfoByLoc($oRequest->getFromLng(), $oRequest->getFromLat());
        if (!empty($aFromArea)) {
            $this->_aAreaInfo['from_area'] = $aFromArea;
        }

        // 起点城市获取失败，默认为0，走降级
        if (empty($this->_aAreaInfo['from_area']['id'])) {
            throw new ExceptionWithResp(
                ErrCode\Code::E_COMMON_HTTP_READ_FAIL,
                // ErrCode\RespCode::R_ESTIMATE_DOWNSTREAM_FAIL_DEGRADE,
                EstimateDegradeCode::R_ESTIMATE_DEGRADE_LOC_FAIL,
                '',
                ['from_lng' => $oRequest->getFromLng(), 'from_lat' => $oRequest->getFromLat(), 'ret' => $aFromArea]
            );
        }

        $aToArea = MapHelper::getAreaInfoByLoc($oRequest->getToLng(), $oRequest->getToLat());
        if (!empty($aToArea)) {
            $this->_aAreaInfo['to_area'] = $aToArea;
        }

        return;
    }

    /**
     * 实例化
     * @param Request $oRequest oRequest
     * @return ProductList
     */
    public static function getInstance($oRequest) {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self($oRequest);
        }

        return self::$_oInstance;
    }

    /**
     * @return array
     */
    public function buildProductList() {
        //请求dds/products 接口
        $oAreaInfo       = new AreaInfo($this->_oRequest, $this->_aAreaInfo);
        $oCommonInfo     = new CommonInfo($this->_oRequest);
        $this->_iTabType = $oCommonInfo->iTabType;
        $this->_iSourceId = $oCommonInfo->iSourceId;
        $this->_getProducts($oAreaInfo);

        //根据products接口返回的多条产品构建基础req数据
        foreach ($this->_aBaseProductList as $aBaseProduct) {
            $oProduct = new Product($this->_oRequest);
            $oProduct->buildOrderInfo($aBaseProduct);
            $oProduct->buildCommonInfo($oCommonInfo);
            $oProduct->buildAreaInfo($oAreaInfo);
            $oProduct->buildPassengerInfo($this->_aBasePassengerInfo);
            $oProduct->buildCustomServiceInfo();
            $this->aProductList[] = $oProduct;
        }

        return $this->aProductList;
    }



    /**
     * 获取司务员列表
     * @param array $oAreaInfo array
     * @return array
     */
    private function _getAssignDriverList($oAreaInfo) {
        //获取司机列表
        $aAssignDriverList = LuxRpc::getInstance()->getAssignDriverList(
            $this->_aBasePassengerInfo['uid'],
            $oAreaInfo->iArea,
            [
                'from_lat'  => $this->_oRequest->getFromLat(),
                'from_lng'  => $this->_oRequest->getFromLng(),
                'from_name' => $this->_oRequest->getFromName(),
                'to_lat'    => $this->_oRequest->getToLat(),
                'to_lng'    => $this->_oRequest->getToLng(),
                'to_name'   => $this->_oRequest->getToName(),
            ],
            $this->_oRequest->getDepartureTime(),
            $this->_oRequest->getOrderType(),
            $this->_oRequest->getLang(),
            (bool)$this->_oRequest->getIsClosePrefer(),
            $this->_oRequest->getAccessKeyId(),
            $this->_oRequest->getAppversion()
        );
        return $aAssignDriverList['driverList'] ?? [];
    }

    /**
     * 获取司务员相关产品列表
     * @param array $oAreaInfo oAreaInfo
     * @return void
     */
    private function _getProducts($oAreaInfo) {
        $this->_getDDSProducts();
        if (empty($this->_aDDSProductList)) {
            return;
        }

        $iShowTab    = Util::getShowTab(
            array(
                'page_type'     => $this->_oRequest->getPageType(),
                'area'          => $oAreaInfo->iArea,
                'district'      => $oAreaInfo->iDistrict,
                'access_key_id' => $this->_oRequest->getAccessKeyId(),
                'app_version'   => $this->_oRequest->getAppversion(),
            )
        );
        $isGetDriver = (Util::SHOW_TAB_ALL == $iShowTab&& CommonInfo::TAB_TYPE_DRIVER == $this->_iTabType);
        $isGetAll    = (Util::SHOW_TAB_ALL == $iShowTab && CommonInfo::TAB_TYPE_ALL == $this->_iTabType);
        //获取渲染的tab
        if ($isGetDriver || $isGetAll) {
            $aAssignDriverList = $this->_getAssignDriverList($oAreaInfo);
            if (empty($aAssignDriverList)) {
                return;
            }

            if (CommonInfo::TAB_TYPE_ALL == $this->_iTabType) {
                //处理推荐司机列表
                $aAssignDriverList = $this->_handleDriverList($aAssignDriverList);
            }

            //给司机添加参数
            $aRequireLevelKeyList = [];
            foreach ($aAssignDriverList as $aAssignDriver) {
                if (empty($aRequireLevelKeyList[$aAssignDriver['requireLevel']])) {
                    $aRequireLevelKeyList[$aAssignDriver['requireLevel']] = [];
                }

                $aRequireLevelKeyList[$aAssignDriver['requireLevel']][] = $aAssignDriver;
            }

            $aDriverProducts = [];
            foreach ($this->_aDDSProductList as $aDDSProduct) {
                $aDDSProduct['designated_driver']      = 0;
                $aDDSProduct['designated_driver_info'] = [];
                if (!empty($aRequireLevelKeyList[(string)$aDDSProduct['require_level']])) {
                    //内循环对$aDDSProduct覆盖问题
                    foreach ($aRequireLevelKeyList[(string)$aDDSProduct['require_level']] as $aItem) {
                        $aDDSProductClone = clone $aDDSProduct;
                        $aDDSProductClone['designated_driver']      = $aItem['driverId'] ?? 0;
                        $aDDSProductClone['designated_driver_info'] = $aItem;
                        $aDriverProducts[] = $aDDSProductClone;
                    }
                }
            }
        }

        if ($isGetAll) {
            $this->_aBaseProductList = array_merge($this->_aDDSProductList, $aDriverProducts);
        } elseif ($isGetDriver) {
            $this->_aBaseProductList = $aDriverProducts;
        } else {
            $this->_aBaseProductList = $this->_aDDSProductList;
        }

        return;
    }

    /**
     * 处理推荐司机长度
     * @param array $aAssignDriverList aAssignDriverList
     * @return array
     */
    private function _handleDriverList($aAssignDriverList) {
        //获取长度配置
        $aLensConfs = ApolloHelper::getConfigContent('lux_multi_estimate_sort','driver_list_len_conf');
        if (empty($aLensConfs) || empty($aLensConfs['lens_conf'])) {
            return $aAssignDriverList;
        }

        $aKeyLensConfs = [];
        foreach ($aLensConfs['lens_conf'] as $aConf) {
            $aKeyLensConfs[$aConf['city']] = (int)$aConf['len'];
        }

        $iArea = $this->_aAreaInfo['from_area']['id'] ?? 0;
        $iLens = 0;
        if (isset($aKeyLensConfs[$iArea]) && !empty($aKeyLensConfs[$iArea])) {
            $iLens = $aKeyLensConfs[$iArea];
        } elseif (isset($aKeyLensConfs['default']) && !empty($aKeyLensConfs['default'])) {
            $iLens = $aKeyLensConfs['default'];
        }

        //去除多余的司务员
        if ($iLens < count($this->_aDDSProductList)) {
            return $aAssignDriverList;
        }

        $iDriverLen = $iLens - count($this->_aDDSProductList);
        //此处认为luxury返回的数组是有序的，直接根据长度截断
        $aAssignDriverList = array_slice($aAssignDriverList, 0, $iDriverLen);
        return $aAssignDriverList;
    }

    /**
     * @return void
     * @throws ExceptionWithResp e
     */
    private function _getDDSProducts() {
        $aProductsReq = new ProductsReq();
        $aUserInfo    = new UserInfoV2();
        $aUserInfo->setPhone($this->_aBasePassengerInfo['phone']);
        $aUserInfo->setPid($this->_aBasePassengerInfo['pid']);
        $aUserInfo->setUid($this->_aBasePassengerInfo['uid']);

        $aCommonInfo = new CommonInfoV2();
        $aCommonInfo->setAppVersion($this->_oRequest->getAppVersion());
        $aCommonInfo->setAccessKeyId($this->_oRequest->getAccessKeyId());
        $aCommonInfo->setLang($this->_oRequest->getLang());
        $aCommonInfo->setChannel($this->_oRequest->getChannel());
        $aCommonInfo->setClientType($this->_oRequest->getClientType());
        $aCommonInfo->setStartLat($this->_oRequest->getFromLat());
        $aCommonInfo->setStartLng($this->_oRequest->getFromLng());
        $aCommonInfo->setDestLat($this->_oRequest->getToLat());
        $aCommonInfo->setDestLng($this->_oRequest->getToLng());
        $aCommonInfo->setCity($this->_aAreaInfo['from_area']['id'] ?? 0);
        $aCommonInfo->setToCity($this->_aAreaInfo['to_area']['id'] ?? 0);
        $aCommonInfo->setMenuId($this->_oRequest->getMenuId());
        $aCommonInfo->setOrderType($this->_oRequest->getOrderType());
        $aCommonInfo->setPageType($this->_oRequest->getPageType());
        $aCommonInfo->setCallcarType($this->_oRequest->getCallCarType());
        $aCommonInfo->setLuxurySelectCarlevels(json_encode([-1]));
        $aCommonInfo->setSourceId($this->_iSourceId);

        $aProductsReq->setUserInfo($aUserInfo);
        $aProductsReq->setCommonInfo($aCommonInfo);

        $oClient = new EstimateDecisionClient();
        $aResult = $oClient->products($aProductsReq);
        if (GLOBAL_SUCCESS == $aResult['errno'] && !empty($aResult['data']) && !empty($aResult['data']['product_list'])) {
            $aResDDSProductList = $aResult['data']['product_list'];
        }

        foreach ($aResDDSProductList as $aOneProduct) {
            if (isset($aOneProduct['remove_flag']) && $aOneProduct['remove_flag']) {
                continue;
            }

            array_push($this->_aDDSProductList, $aOneProduct);
        }

        if (empty($this->_aDDSProductList)) {
            $sErrMsg = Config::text('errno', 'dache_anycar_no_products_error');
            throw new ExceptionWithResp(
                Code::E_COMMON_AREA_NOT_OPEN_SERVICE,
                RespCode::P_ERRNO_NOT_OPEN_SERVICE,
                $sErrMsg
            );
        }
    }
}

/**
 * Class Product
 * @package PreSale\Logics\luxEstimatePrice\multiRequest
 */
class Product
{

    /**
     * @var $oOrderInfo OrderInfo
     */
    public $oOrderInfo;
    /**
     * @var $oCommonInfo CommonInfo
     */
    public $oCommonInfo;
    /**
     * @var $oPassengerInfo PassengerInfo
     */
    public $oPassengerInfo;
    public $aCustomFeature;
    /**
     * @var $oAreaInfo AreaInfo
     */
    public $oAreaInfo;
    private $_oRequest;

    /**
     * 实例化
     * @param Request $oRequest oRequest
     */
    public function __construct(Request $oRequest) {
        $this->_oRequest = $oRequest;
    }

    /**
     * buildOrderInfo
     * @param array $aBaseProduct aBaseProduct
     * @return void
     */
    public function buildOrderInfo($aBaseProduct) {
        $oOrderInfo = new OrderInfo($this->_oRequest);
        $oOrderInfo->fillUpOrderInfo($aBaseProduct);
        $this->oOrderInfo = $oOrderInfo;
        return;
    }

    /**
     * buildPassengerInfo
     * @param array $aBasePassengerInfo aBasePassengerInfo
     * @return void
     */
    public function buildPassengerInfo($aBasePassengerInfo) {
        $oPassenger = new PassengerInfo($this->_oRequest, $aBasePassengerInfo);
        $oPassenger->buildMemberProfile($this->oOrderInfo, $this->oCommonInfo, $this->oAreaInfo);
        $this->oPassengerInfo = $oPassenger;
        return;
    }

    /**
     * buildCommonInfo
     * @param CommonInfo $oCommonInfo oCommonInfo
     * @return void
     */
    public function buildCommonInfo(CommonInfo $oCommonInfo) {
        $this->oCommonInfo = $oCommonInfo;
        return;
    }

    /**
     * buildAreaInfo
     * @param oAreaInfo $oAreaInfo oAreaInfo
     * @return void
     */
    public function buildAreaInfo(AreaInfo $oAreaInfo) {
        $this->oAreaInfo = $oAreaInfo;
        return;
    }

    /**
     * buildAreaInfo
     * @return void
     */
    public function buildCustomServiceInfo() {
        $aCustomFeature = [];
        $iPageType      = $this->_oRequest->getPageType();

        switch ($iPageType) {
            case Horae::PAGE_TYPE_NO_OBSTACLE:
                $aCustomFeature[] = ['id' => CustomLogic::getServiceId(CustomLogic::SUB_MENU_DISABLED_CAR_ID,$this->oPassengerInfo['pid']), 'count' => 1];
                break;
            case Horae::PAGE_TYPE_PET_CAR:
                $aCustomFeature[] = ['id' => CustomLogic::CUSTOM_SERVICE_PET_CAR, 'count' => 1];
                break;
            default:
                break;
        }

        $this->aCustomFeature = $aCustomFeature;
        return;
    }
}
