<?php
/**
 * Created by PhpStorm.
 * User: didi
 * Date: 2020/2/8
 * Time: 5:05 PM
 * <AUTHOR> <<EMAIL>>
 */

namespace PreSale\Logics\luxEstimatePrice\multiRequest;

use BizCommon\Models\Passenger\Passenger;
use BizLib\Client\MemberSystemClient;
use BizLib\Config as NuwaConfig;
use BizLib\Utils;
use BizLib\Utils\Language;
use BizLib\Utils\MemberVersion;
use BizLib\Utils\Registry;
use Dirpc\SDK\PreSale\LuxMultiEstimatePriceRequest as Request;
use Exception;

/**
 * Class PassengerInfo
 * @package PreSale\Logics\luxEstimatePrice\multiRequest
 */
class PassengerInfo
{
    //乘客信息
    /**
     * @var
     */
    public $aPassengerInfo;
    //用户Token
    /**
     * @var string
     */
    public $sToken;
    //用户UID
    /**
     * @var int
     */
    public $iUid;
    //用户PID
    /**
     * @var int
     */
    public $iPid;
    //手机号
    /**
     * @var int
     */
    public $sPhone;
    //是否使用会员权益
    /**
     * @var int
     */
    public $iUseDpaSelected;

    /**
     * PassengerInfo constructor.
     * @param Request $oEstimateRequest   oEstimateRequest
     * @param array   $aPassportPassenger aPassportPassenger
     */
    public function __construct(Request $oEstimateRequest, $aPassportPassenger) {
        if (empty($aPassportPassenger)) {
            $aPassportPassenger = Passenger::getInstance()->getPassengerByTokenFromPassport($oEstimateRequest->getToken());
        }

        if (empty($aPassportPassenger)) {
            return;
        }

        $this->aPassengerInfo  = $aPassportPassenger;
        $this->iUid            = (int)$aPassportPassenger['uid'];
        $this->iPid            = (int)$aPassportPassenger['pid'];
        $this->sPhone          = (int)$aPassportPassenger['phone'];
        $this->sToken          = $oEstimateRequest->getToken();
        $this->iUseDpaSelected = $oEstimateRequest->getUseDpa();
    }


    /**
     * @param OrderInfo  $oOrderInfo  oOrderInfo
     * @param CommonInfo $oCommonInfo oCommonInfo
     * @param AreaInfo   $oAreaInfo   oAreaInfo
     * @throws Exception exception
     * @return void
     */
    public function buildMemberProfile(OrderInfo $oOrderInfo, CommonInfo $oCommonInfo, AreaInfo $oAreaInfo) {
        $this->aPassengerInfo['use_dpa_selected'] = 1;  // 是否使用dpa
        $this->aPassengerInfo['is_user_use_dpa']  = false;      // 是否用户主动选择
        if ('0' == $this->iUseDpaSelected) {
            $this->aPassengerInfo['member_profile'] = [];
            return;
        }

        //获取会员权益信息
        $aMemberProfile = $this->_getMemberInfo($oOrderInfo, $oCommonInfo, $oAreaInfo);

        // 目前预估阶段只用到了会员的dpa权益
        if (empty($aMemberProfile['privileges']['dpa'])) {
            $this->aPassengerInfo['member_profile'] = [];
            return;
        }

        // 企业级临时屏蔽使用有限次溢价保护
        if (Utils\Product::isBusiness($oOrderInfo->iProductId)) {
            if (0 == $aMemberProfile['privileges']['dpa']['is_auto']) {
                unset($aMemberProfile['privileges']['dpa']);
            }
        }

        // 筛选必要字段
        $this->aPassengerInfo['member_profile'] = [
            'level_id'       => $aMemberProfile['level_id'],
            'level_icon'     => $aMemberProfile['level_icon'],
            'level_name'     => $aMemberProfile['level_name'],
            'level_name_new' => $aMemberProfile['level_name'],
            'privileges'     => [
                'dpa' => $aMemberProfile['privileges']['dpa'],
            ],
        ];
        return;
    }

    /**
     * 获取会员信息
     * @param OrderInfo  $oOrderInfo  aOrderInfo
     * @param CommonInfo $oCommonInfo oCommonInfo
     * @param AreaInfo   $oAreaInfo   oAreaInfo
     * @throws Exception exception
     * @return array
     */
    private function _getMemberInfo(OrderInfo $oOrderInfo, CommonInfo $oCommonInfo, AreaInfo $oAreaInfo) {
        //仅支持中文
        if (Language::ZH_CN != $oCommonInfo->sLang) {
            return [];
        }

        $aHitInfo      = [
            'city_id' => $oAreaInfo->iArea,
            'version' => 2, //全部走userPrivOptions接口
        ];
        $sKey          = "memberInfo_{$oOrderInfo->iProductId}_{$this->iPid}_{$oCommonInfo->sLang}";
        $memberProfile = Registry::getInstance()->get($sKey);
        if (null === $memberProfile) {
            $memberProfile = (new MemberSystemClient())->queryInfo(
                $oOrderInfo->iProductId,
                $this->iPid,
                $oCommonInfo->sLang,
                false,
                $aHitInfo
            );
            Registry::getInstance()->set($sKey, $memberProfile);
        }

        //
        //  这段逻辑属于会员兜底逻辑，如果不存在reason字段用于展示，则赋值一个reason用于展示
        //  否则price-api会报错
        if (MemberVersion::isNewMember() && !empty($memberProfile['privileges']) && !empty($memberProfile['privileges']['dpa'])) {
            if (1 == $memberProfile['privileges']['dpa']['is_coupon'] && empty($memberProfile['privileges']['dpa']['frontend']['reason'])) {
                $aEstimateText = NuwaConfig::text('config_text', 'mOrderEstimateNew');
                $memberProfile['privileges']['dpa']['frontend']['reason'] = $aEstimateText['dpa_coupon_used_text'];
            }
        }

        return $memberProfile;
    }
}
