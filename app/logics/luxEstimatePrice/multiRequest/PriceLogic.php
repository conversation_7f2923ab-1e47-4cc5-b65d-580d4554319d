<?php

namespace PreSale\Logics\luxEstimatePrice\multiRequest;

use BizCommon\Logics\Order\FieldOrderNTuple;
use BizLib\Client\AthenaApiClient;
use BizLib\Client\PriceApiClient;
use BizLib\Constants;
use BizLib\Constants\Horae;
use BizLib\ErrCode;
use BizLib\Exception\InvalidArgumentException;
use BizLib\Log as NuwaLog;
use BizLib\Utils\UtilHelper;
use Xiaoju\Apollo\Apollo;

/**
 * Created by PhpStorm.
 * User: didi
 * Date: 2020/2/11
 * Time: 2:27 PM
 */
class PriceLogic
{

    const DEGRADE_OPEN = true; //默认导流不降级
    /**
     * 支付类型.
     */
    const PAY_TYPE_NEW = 2;
    const PAY_TYPE_BR  = 1;

    /**
     * getMultiResponse
     * @param array $aProductList aProductList
     * @return array
     */
    public function getMultiResponse(array $aProductList) {
        $aPriceParams    = $this->_getMultiPriceParams($aProductList);
        $aExtraInfo      = $this->_getExtraInfoParams($aProductList);
        $aPriceResult    = $this->_getMultiPriceByClient($aPriceParams, $aExtraInfo);
        $aResponseInfoV2 = $this->_buildResponseAndRequest($aPriceParams, $aPriceResult, $aProductList);
        return $aResponseInfoV2;
    }

    // 注释-待删
//    /**
//     * @param int $iArea 城市ID
//     * @return bool
//     */
//    private function _isAthenaDegraded($iArea) {
//
//        $bOpen = UtilHelper::getFusingSwitch('passenger_estimate_athena_degrade_switch', 'default', (int)($iArea));
//        if (self::DEGRADE_OPEN === $bOpen) {
//            NuwaLog::warning(
//                ErrCode\Msg::formatArray(
//                    ErrCode\Code::E_COMMON_API_DEGRADE,
//                    array(
//                        'describe' => 'athena is degrade',
//                        'reqArea'  => $iArea,
//                    )
//                )
//            );
//
//            return true;
//        }
//
//        return false;
//    }

    // 注释-待删
//    /**
//     * @param array $aProductList $aProductList
//     * @return array
//     */
//    private function _getAthenaBubbleParams(array $aProductList) {
//        $oProduct = current($aProductList);
//        $aResult  = [
//            'client_type'   => $oProduct->oCommonInfo->iClientType,
//            'app_version'   => $oProduct->oCommonInfo->sAppVersion,
//            'map_type'      => $oProduct->oAreaInfo->sMapType,
//            'lang'          => $oProduct->oCommonInfo->sLang,
//            'order_type'    => $oProduct->oOrderInfo->iOrderType,
//            'phone'         => $oProduct->oPassengerInfo->sPhone,
//            'pid'           => $oProduct->oPassengerInfo->iPid,
//            'area'          => $oProduct->oAreaInfo->iArea,
//            'district'      => $oProduct->oAreaInfo->iDistrict,
//            'from_area'     => $oProduct->oAreaInfo->iArea,
//            'from_name'     => $oProduct->oAreaInfo->sFromName,
//            'from_lng'      => $oProduct->oAreaInfo->fFromLng,
//            'from_lat'      => $oProduct->oAreaInfo->fFromLat,
//            'from_county'   => $oProduct->oAreaInfo->iFromCounty,
//            'to_lng'        => $oProduct->oAreaInfo->fToLng,
//            'to_lat'        => $oProduct->oAreaInfo->fToLat,
//            'to_name'       => $oProduct->oAreaInfo->sToName,
//            'to_county'     => $oProduct->oAreaInfo->iToCounty,
//            'dest_poi_id'   => $oProduct->oAreaInfo->sToPoiId,
//            'current_lng'   => $oProduct->oAreaInfo->fCurLng,
//            'current_lat'   => $oProduct->oAreaInfo->fCurLat,
//            'channel'       => $oProduct->oCommonInfo->sChannel,
//            'token'         => $oProduct->oPassengerInfo->sToken,
//            'menu_id'       => $oProduct->oOrderInfo->sMenuID,
//            'call_car_type' => $oProduct->oOrderInfo->iCallCarType,
//        ];
//
//        $aApiAddProduct = [];
//        //  $oProduct Product
//        foreach ($aProductList as $oProduct) {
//            $aData            = [
//                'product_id'       => $oProduct->oOrderInfo->iProductId,
//                'require_level'    => $oProduct->oOrderInfo->iRequireLevel,
//                'combo_type'       => $oProduct->oOrderInfo->iComboType,
//                'scene_type'       => $oProduct->oOrderInfo->iSceneType,
//                'product_category' => $oProduct->oOrderInfo->iProductCategory,
//                'carpool_type'     => $oProduct->oOrderInfo->iCarpoolType,
//                'airport_type'     => $oProduct->oOrderInfo->iAirportType,
//                'estimate_id'      => $oProduct->oOrderInfo->sEstimateID,
//                'departure_time'   => $oProduct->oOrderInfo->iDepartureTime,
//                'order_type'       => $oProduct->oOrderInfo->iOrderType,
//                'form_show_type'   => 0,
//                'is_add'           => 0,
//                'extra_info'       => [
//                    'order_info' => json_encode(
//                        [
//                            'payments_type' => $oProduct->oOrderInfo->sPaymentsType,
//                        ]
//                    ),
//                ],
//            ];
//            $aApiAddProduct[] = $aData;
//        }
//
//        $aResult['api_add_product'] = $aApiAddProduct;
//        return $aResult;
//    }

    /**
     * @param array $aProductList $aProductList
     * @return array
     */
    private function _getMultiPriceParams(array $aProductList) {
        $aPriceParams = array();
        foreach ($aProductList as $iIndex => $oProduct) {
            $aParams        = $this->getPriceParams($oProduct);
            $aPriceParams[] = $aParams;
        }

        return $aPriceParams;
    }

    /**
     * @param Product $oProduct $oProduct
     * @return array
     */
    public function getPriceParams(Product $oProduct) {
        //注意：新增参数需要更新thift文件，并通知Athena的同学,切记切记。。。
        $aResult = [
            'common_info'         => [
                'business_id'             => $oProduct->oOrderInfo->iBusinessId,
                'access_key_id'           => $oProduct->oCommonInfo->iAccessKeyID,
                'app_version'             => $oProduct->oCommonInfo->sAppVersion,
                'client_type'             => $oProduct->oCommonInfo->iClientType,
                'lang'                    => $oProduct->oCommonInfo->sLang,
                'origin_id'               => $oProduct->oCommonInfo->iOriginId,
                'terminal_id'             => $oProduct->oCommonInfo->iTerminalId,
                'imei'                    => $oProduct->oCommonInfo->sImei,
                'is_from_b2b'             => $oProduct->oCommonInfo->bIsFromB2b,
                'is_from_webapp'          => $oProduct->oCommonInfo->bIsFromWebApp,
                'from'                    => $oProduct->oCommonInfo->sFrom,
                'platform_type'           => $oProduct->oCommonInfo->iPlatformType,
                'luxury_select_carlevels' => $oProduct->oCommonInfo->sLuxrySelectCarlevels,
                'luxury_select_driver'    => $oProduct->oCommonInfo->sLuxrySelectDriver,
                'tab_type'                => $oProduct->oCommonInfo->iTabType,
            ],
            'order_info'          => [
                'current_lng'       => $oProduct->oAreaInfo->fCurLng,
                'current_lat'       => $oProduct->oAreaInfo->fCurLat,
                'area'              => $oProduct->oAreaInfo->iArea,
                'from_lng'          => $oProduct->oAreaInfo->fFromLng,
                'from_lat'          => $oProduct->oAreaInfo->fFromLat,
                'from_poi_id'       => $oProduct->oAreaInfo->sFromPoiId,
                'from_poi_type'     => $oProduct->oAreaInfo->sFromPoiType,
                'from_address'      => $oProduct->oAreaInfo->sFromAddress,
                'from_name'         => $oProduct->oAreaInfo->sFromName,
                'starting_name'     => $oProduct->oAreaInfo->sStartingName,
                'to_lng'            => $oProduct->oAreaInfo->fToLng,
                'to_lat'            => $oProduct->oAreaInfo->fToLat,
                'to_poi_id'         => $oProduct->oAreaInfo->sToPoiId,
                'to_poi_type'       => $oProduct->oAreaInfo->sToPoiType,
                'to_address'        => $oProduct->oAreaInfo->sToAddress,
                'to_name'           => $oProduct->oAreaInfo->sToName,
                'dest_name'         => $oProduct->oAreaInfo->sDestName,
                'order_type'        => $oProduct->oOrderInfo->iOrderType,
                'channel'           => $oProduct->oCommonInfo->sChannel,
                'combo_type'        => $oProduct->oOrderInfo->iComboType,
                'departure_time'    => $oProduct->oOrderInfo->iDepartureTime,
                'call_car_type'     => $oProduct->oOrderInfo->iCallCarType,
                'call_car_phone'    => $oProduct->oOrderInfo->sCallCarPhone,
                'map_type'          => $oProduct->oAreaInfo->sMapType,
                'product_id'        => $oProduct->oOrderInfo->iProductId,
                'require_level'     => $oProduct->oOrderInfo->iRequireLevel,
                'district'          => $oProduct->oAreaInfo->iDistrict,
                'payments_type'     => $oProduct->oOrderInfo->sPaymentsType,
                'airport_id'        => $oProduct->oOrderInfo->iAirportId,
                'abstract_district' => $oProduct->oAreaInfo->sAbstractDistrict,
            ],
            'passenger_info'      => json_encode($oProduct->oPassengerInfo->aPassengerInfo, JSON_UNESCAPED_UNICODE),
            'custom_service_info' => json_encode([], JSON_UNESCAPED_UNICODE),
            'one_key_activity'    => json_encode(['activity_switch' => false], JSON_UNESCAPED_UNICODE),
        ];

        $aOrderInfo = $oProduct->oOrderInfo->toArray();
        $aNTuple    = FieldOrderNTuple::getOrderNTupleByOrder($aOrderInfo);
        $aOrderInfo['n_tuple'] = $aNTuple;
        $aExtraOrderInfo       = array_diff_key($aOrderInfo, $aResult['order_info']);
        $aExtraOrderInfo['county'] = $oProduct->oAreaInfo->iFromCounty;
        $aExtraOrderInfo['to_county'] = $oProduct->oAreaInfo->iToCounty;
        $aExtraInfo            = [
            'order'            => json_encode($aExtraOrderInfo, JSON_UNESCAPED_UNICODE),
            'product_category' => $oProduct->oOrderInfo->iProductCategory,
        ];

        $aResult['extra_info'] = $aExtraInfo;
        return $aResult;
    }

    /**
     * @param array $aProductList $aProductList
     * @return array
     */
    private function _getExtraInfoParams(array $aProductList) {
        $aExtraInfo = [
            'is_new_data_send' => 1, //6.0 全部走字段压缩；因为所有的Athenareq都已经在athena_bubble_req中补齐了
        ];
        return $aExtraInfo;
    }

    // 注释-待删
//    /**
//     * @param array $aPriceParams        price入参
//     * @param array $aAthenaBubbleParams Athena额外需要的参数
//     * @param array $aExtraInfo          extraInfo
//     * @return mixed|string
//     * @throws InvalidArgumentException invalidArgumentException
//     */
//    private function _getAthenaOriginResponse($aPriceParams, $aAthenaBubbleParams, $aExtraInfo) {
//        $oClient         = new AthenaApiClient();
//        $aAthenaResponse = $oClient->getMultiBubbleInfo($aPriceParams, $aAthenaBubbleParams, $aExtraInfo);
//        $aResponse       = json_encode($aAthenaResponse['estimateprice_resp']);
//        $aResponse       = json_decode($aResponse, true);
//
//        if (empty($aResponse)) {
//            throw new InvalidArgumentException(ErrCode\Code::E_PRICE_REQUEST_FAIL, ['params' => $aPriceParams]);
//        }
//
//        return $aResponse;
//    }

    /**
     * @param array $aPriceParams price入参
     * @param array $aPriceResult Athena额外需要的参数
     * @param array $aProductList extraInfo
     * @return mixed|string
     * @throws InvalidArgumentException invalidArgumentException
     */
    private function _buildResponseAndRequest($aPriceParams, $aPriceResult, $aProductList) {
        //formatResponse
        $aPriceResponse = array();
        foreach ($aPriceResult as $iIndex => $aPrice) {
            if (empty($aPrice) || !is_array($aPrice) || (empty($aPrice['bill_info']) && empty($aPrice['activity_info']) && empty($aPrice['payments_info']))) {
                continue;
            }

            //去掉从apollo拉取的Athena需要追加产品
            if (isset($aPrice['extra_info']['is_athena_add_product']) && Constants\Common::ATHENA_ADD_PRODUCT == $aPrice['extra_info']['is_athena_add_product']) {
                continue;
            }

            $aPriceResponse[$iIndex] = $aPrice;
        }

        $aResponseData = array();
        foreach ($aPriceResponse as $iIndex => $aPrice) {
            $aPriceItem = $this->_buildPriceResponseV2($aPrice);
            $aResponseData[$iIndex] = $aPriceItem;
        }

        if (empty($aResponseData)) {
            throw new InvalidArgumentException(
                ErrCode\Code::E_ATHENA_REQUEST_FAIL
            );
        }

        //formatRequest
        $aResponseInfoV2 = $this->getMultiResponseParamsV2($aPriceParams, $aResponseData, $aProductList);
        return $aResponseInfoV2;
    }

    /**
     * @param array $aPrice price
     * @return mixed
     */
    private function _buildPriceResponseV2($aPrice) {
        $bPaymentSeparate = false;
        $aPayment         = null;
        $iComboType       = Horae::TYPE_COMBO_DEFAULT;
        $aActivityInfo    = json_decode($aPrice['activity_info'], true);
        $aBillInfo        = json_decode($aPrice['bill_info'], true);
        $sCarLevel        = array_keys($aBillInfo['bills'])[0];

        // activity转换为list，背景是一个产品多价格下需要有多个activity信息。兼容上线过程中不一致
        if (isset($aActivityInfo) && !empty($aActivityInfo) && !is_array($aActivityInfo[0])) {
            $aActivityInfo = [$aActivityInfo];
        }

        $aPaymentInfo = json_decode($aPrice['payments_info'], true);
        $aExtraInfo   = $aPrice['extra_info'];

        if (isset($aBillInfo) && !empty($aBillInfo)) {
            $iComboType = $aBillInfo['product_infos'][$sCarLevel]['combo_type'];
            //和账单的交互后续用N元组标识产品，所以这里检查是否需要根据N元组将combo_type 做转换
            $iCheckCombo = $this->_checkOrderNTupleConvert($aBillInfo, $sCarLevel);
            if ($iCheckCombo) {
                $iComboType = $iCheckCombo;
            }
        }

        //支付方式返回值
        switch ($iComboType) {
            case Horae::TYPE_COMBO_CARPOOL:
                // no break
            case Horae::TYPE_COMBO_CARPOOL_INTER_CITY:
                // no break
            case Horae::TYPE_COMBO_CARPOOL_FLAT_RATE:
                if (isset($aPaymentInfo) && !empty($aPaymentInfo)) {
                    switch ($aPaymentInfo['pay_resp_type']) {
                        case self::PAY_TYPE_NEW:
                            if (isset($aPaymentInfo['user_pay_info']['carpool']) && is_array($aPaymentInfo['user_pay_info']['carpool'])) {
                                $aPayment         = $aPaymentInfo['user_pay_info']['carpool'];
                                $bPaymentSeparate = true;
                            }
                            break;
                        default:
                            if (isset($aPaymentInfo['user_pay_info'])) {
                                $aPayment = $aPaymentInfo['user_pay_info'];
                            }
                            break;
                    }
                }
                break;
            default:
                switch ($aPaymentInfo['pay_resp_type']) {
                    case self::PAY_TYPE_NEW:
                        if (isset($aPaymentInfo['user_pay_info']['noncarpool']) && is_array($aPaymentInfo['user_pay_info']['noncarpool'])) {
                            $aPayment         = $aPaymentInfo['user_pay_info']['noncarpool'];
                            $bPaymentSeparate = true;
                        }
                        break;
                    case self::PAY_TYPE_BR:
                        $aPayment = $aPaymentInfo['user_pay_info'];
                        foreach ($aPayment['busi_payments'] as &$item) {
                            unset($item['business_config']);
                        }

                        $bPaymentSeparate = true;
                        break;
                    default:
                        if (isset($aPaymentInfo['user_pay_info'])) {
                            $aPayment = $aPaymentInfo['user_pay_info'];
                        }
                        break;
                }
                break;
        }

        $aUserTypeInfo = array('user_pay_info' => $aPayment, 'payments_separate' => $bPaymentSeparate);

        $aPriceItem['payments_info'] = $aUserTypeInfo;
        $aPriceItem['bill_info']     = $aBillInfo;
        $aPriceItem['activity_info'] = $aActivityInfo;
        // 预估Response改造，新增price_extra字段
        $aPriceItem['price_extra'] = $aExtraInfo;
        return $aPriceItem;
    }

    /**
     * @param array  $aBillInfo bill
     * @param string $sCarLevel carlevel
     *
     * @return int
     */
    private function _checkOrderNTupleConvert($aBillInfo, $sCarLevel) {
        $iComboType = 0;
        if (!isset($aBillInfo['order_n_tuple_infos'][$sCarLevel]) || empty($aBillInfo['order_n_tuple_infos'][$sCarLevel])) {
            return $iComboType;
        }

        $aOrderNTuple = $aBillInfo['order_n_tuple_infos'][$sCarLevel];
        if (isset($aOrderNTuple['carpool_type']) && !empty($aOrderNTuple['carpool_type'])) {
            switch ($aOrderNTuple['carpool_type']) {
                case OrderNTuple::CARPOOL_TYPE_NORMAL:
                    //no break
                case OrderNTuple::CARPOOL_TYPE_LOW_PRICE:
                    //no break
                case OrderNTuple::CARPOOL_TYPE_STATION:
                    $iComboType = Horae::TYPE_COMBO_CARPOOL;
                    break;
                default:
                    break;
            }
        }

        if (isset($aOrderNTuple['is_special_price']) && !empty($aOrderNTuple['is_special_price'])) {
            if ($aOrderNTuple['is_special_price']) {
                $iComboType = Horae::TYPE_COMBO_SPECIAL_RATE;
            }
        }

        return $iComboType;
    }

    /**
     * 返回给乘客端相关参数.
     *
     * @param array $aPriceParams aPriceParams
     * @param array $aPriceResult aProcessResult
     * @param array $aProductList aProductList
     * @return array
     */
    public function getMultiResponseParamsV2(array $aPriceParams, array $aPriceResult, array $aProductList) {
        $aRequestParams  = [];
        $aResponseParams = [];
        $oProduct        = reset($aProductList);
        foreach ($aPriceParams as $iIndex => $aParam) {
            $aParam['order_info']['real_type']  = $aParam['order_info']['order_type'];
            $aParam['common_info']['pixels']    = $oProduct->oCommonInfo->sPixels;
            $aParam['common_info']['page_type'] = $oProduct->oOrderInfo->iPageType;
            if (isset($aParam['extra_info']['order'])) {
                $aOrderExtra = json_decode($aParam['extra_info']['order'], true);
                $aOrderInfo  = (array)$aParam['order_info'];
                if (!empty($aOrderExtra)) {
                    $aParam['order_info'] = $aOrderInfo + $aOrderExtra;
                }

                if (!empty($aOrderExtra['estimate_id'])) {
                    $aRequestParams[$aOrderExtra['estimate_id']] = $aParam;
                }
            }
        }

        foreach ($aPriceResult as $index => $aAthenaResult) {
            $aBillInfo   = $aAthenaResult['bill_info'];
            $sEstimateId = $aBillInfo['estimate_id'];
            $aReqParam   = $aRequestParams[$sEstimateId];
            if (empty($aReqParam)) {
                continue;
            }

            $aOrderInfo        = $aReqParam['order_info'];
            $aResponseParam    = [
                'common_info'         => $aReqParam['common_info'],
                'order_info'          => $aOrderInfo,
                'passenger_info'      => json_decode($aReqParam['passenger_info'], true),
                'bill_info'           => $aBillInfo,
                'payments_info'       => $aAthenaResult['payments_info'],
                'activity_info'       => $aAthenaResult['activity_info'],
                'price_extra'         => $aAthenaResult['price_extra'],
                'athena_info'         => $aAthenaResult['athena_info'],
                'athena_extra'        => $aAthenaResult['athena_extra'],
                'one_key_activity'    => [
                    'activity_switch' => $aReqParam['activity_switch'],
                ],
                'custom_service_info' => [], //$aReqParam['custom_service_info'],
                'preference_product'  => $aReqParam['extra_info']['preference_product'],
            ];
            $aResponseParams[] = $aResponseParam;
        }

        return $aResponseParams;
    }

    /**
     * @param array $aPriceParams array
     * @param array $aExtraInfo   array
     * @return array
     */
    private function _getMultiPriceByClient($aPriceParams, $aExtraInfo) {

        $aMultiParams = [
            'estimatePriceReqs' => $aPriceParams,
            'caller'            => PriceApiClient::CLIENT_PASSENGER,
            'biz_data'          => json_encode($aPriceParams),
            'extra_info'        => $aExtraInfo,
        ];
        $_oClient     = new PriceApiClient(PriceApiClient::MODULE_NAME);
        $aRet         = $_oClient->estimate($aMultiParams);
        $aAthenaRet   = array();
        if (isset($aRet['errno']) && 0 == $aRet['errno']) {
            return $aRet['data'];
        }

        return $aAthenaRet;
    }
}
