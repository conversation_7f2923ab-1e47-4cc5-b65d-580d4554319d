<?php

namespace PreSale\Logics\luxEstimatePrice\multiRequest;

use PreSale\Models\rpc\LuxRpc;
use BizLib\Config as NuwaConfig;
use PreSale\Logics\estimatePrice\OptionServiceLogic;

/**
 * Created by PhpStorm.
 * User: didi
 * Date: 2020/2/11
 * Time: 2:27 PM
 */
class UserPreferInfoLogic
{
    /**
     * @param array $aProductList aProductList
     * @return array
     */
    public function getUserPreferResponse($aProductList) {
        if (empty($aProductList)) {
            return [];
        }

        $aReqParams  = array(
            'pid'         => $aProductList[0]->oPassengerInfo->iPid,
            'lang'        => $aProductList[0]->oCommonInfo->sLang,
            'from_kind'   => OptionServiceLogic::FORM_KIND_GET,
            'business_id' => $aProductList[0]->oOrderInfo->iBusinessId,
            'uid'         => $aProductList[0]->oPassengerInfo->iUid,
        );
        $aHundunData = OptionServiceLogic::getOptionService($aReqParams);
        if (OptionServiceLogic::isOptionServiceToHundun($aReqParams)) {
            $aPreferInfo = $aHundunData['prefer_info'] ?? [];
        } else {
            $aPreferInfo = LuxRpc::getInstance()->getUserPreferencesInfo(
                $aProductList[0]->oPassengerInfo->iUid,
                $aProductList[0]->oCommonInfo->sLang
            );
            $aConfig     = NuwaConfig::text('prefer','head');
            if (!empty($aConfig)) {
                $aPreferInfo['head']      = $aConfig['head'];
                $aPreferInfo['head_link'] = $aConfig['link'];
                $aPreferInfo['is_support_title']  = 1;
                $aPreferInfo['is_support_remark'] = 1;
                $aPreferInfo['is_im_direct_send'] = 0;
            }

	        // 为了兼容直接请求merlin返回的prefer_options格式
            if (!empty($aPreferInfo['prefer_options'])) {
                $aPreferInfo['prefer_option'] = $aPreferInfo['prefer_options'];
            }
        }

        return $aPreferInfo;
    }
}
