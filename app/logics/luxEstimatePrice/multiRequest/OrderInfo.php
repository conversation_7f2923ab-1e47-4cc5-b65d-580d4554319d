<?php
/**
 * Created by PhpStorm.
 * User: didi
 * Date: 2020/2/8
 * Time: 2:43 PM
 * <AUTHOR> <<EMAIL>>
 */

namespace PreSale\logics\luxEstimatePrice\multiRequest;

use BizCommon\Models\Dfs\BaseDfsComModel;
use BizLib\Constants\Horae;
use BizLib\Utils\UtilHelper;
use Dirpc\SDK\PreSale\LuxMultiEstimatePriceRequest as Request;
use Disf\SPL\Trace;
use PreSale\Models\order\OrderAirPort;

/**
 * Class OrderInfo
 * @package PreSale\logics\luxEstimatePrice\multiRequest
 */
class OrderInfo
{

    /**
     * @var $sEstimateID string
     */
    public $sEstimateID;
    /**
     * @var $iOrderType integer 订单类型
     */
    public $iOrderType;
    //顶导字符串ID
    /**
     * @var string
     */
    public $sMenuID;
    //二级页面ID
    /**
     * @var int
     */
    public $iPageType;
    //原始二级页面ID，存在跳转时需传
    /**
     * @var int
     */
    public $iOriginPageType;

    //品类ID
    /**
     * @var
     */
    public $iProductCategory;
    //订单信息
    /**
     * @var
     */
    public $iProductId;
    /**
     * @var
     */
    public $iBusinessId;
    /**
     * @var
     */
    public $iRequireLevel;
    /**
     * @var
     */
    public $iComboType;
    /**
     * @var
     */
    public $iAirportType;

    //支付方式选择
    /**
     * @var int
     */
    public $sPaymentsType;
    //出发时间
    /**
     * @var int|string
     */
    public $iDepartureTime;
    //代叫类型
    /**
     * @var int
     */
    public $iCallCarType;
    //代叫手机号
    /**
     * @var string
     */
    public $sCallCarPhone;

    //接送机相关
    //航班出发地三字码,如CTU
    /**
     * @var
     */
    public $sFlightDepCode;
    //航班出发航站楼，如T2
    /**
     * @var
     */
    public $sFlightDepTerminal;
    //航班起飞时间字符串
    /**
     * @var
     */
    public $sTrafficDepTime;
    //航班落地三字码,如CTU
    /**
     * @var
     */
    public $sFlightArrCode;
    //航班落地航站楼，如T2
    /**
     * @var
     */
    public $sFlightArrTerminal;
    //航班到达时间字符串
    /**
     * @var
     */
    public $sTrafficArrTime;
    //航班号，如CA1405
    /**
     * @var
     */
    public $sTrafficNumber;
    //接机时为航班落地航站楼id，送机时为出发航站楼id，如1573
    /**
     * @var
     */
    public $iAirportId;
    //用车偏移时间（接机时，单位：秒）
    /**
     * @var
     */
    public $iShiftTime;

    //指定司机
    /**
     * @var
     */
    public $sDesignatedDriver;
    //指定司机级别
    /**
     * @var
     */
    public $sDesignatedDriverTag;
    //指定司机信息
    /**
     * @var
     */
    public $aDesignatedDriverInfo;


    /**
     * OrderInfo constructor.
     * @param Request $oEstimateRequest 预估请求
     */
    public function __construct(Request $oEstimateRequest) {
        //构建基本信息
        $this->sMenuID         = $oEstimateRequest->getMenuId();
        $this->iPageType       = $oEstimateRequest->getPageType();
        $this->iOriginPageType = $oEstimateRequest->getOriginPageType();
        $this->sPaymentsType   = $oEstimateRequest->getPaymentsType();
        $this->iCallCarType    = $oEstimateRequest->getCallCarType();
        $this->sCallCarPhone   = $oEstimateRequest->getCallCarPhone();
        $this->iDepartureTime  = $oEstimateRequest->getDepartureTime();
        $this->iDepartureTime  = empty($this->iDepartureTime) ? time() : $this->iDepartureTime;
        //接送机相关信息
        $this->_buildAirportInfo($oEstimateRequest);
    }

    /**
     * @param Request $oEstimateRequest 预估参数
     * @return void
     */
    private function _buildAirportInfo(Request $oEstimateRequest) {
        $this->sFlightDepCode     = $oEstimateRequest->getFlightDepCode();
        $this->sFlightDepTerminal = $oEstimateRequest->getFlightDepTerminal();
        $this->sTrafficDepTime    = $oEstimateRequest->getTrafficDepTime();
        $this->sFlightArrCode     = $oEstimateRequest->getFlightArrCode();
        $this->sFlightArrTerminal = $oEstimateRequest->getFlightArrTerminal();
        $this->sTrafficArrTime    = $oEstimateRequest->getTrafficArrTime();
        $this->sTrafficNumber     = $oEstimateRequest->getTrafficNumber();
        $this->iAirportId         = $oEstimateRequest->getAirportId();
        $this->iShiftTime         = $oEstimateRequest->getShiftTime();
        return;
    }

    //指定司机

    /**
     * 填充订单信息
     * @param array $aBaseOrderInfo 订单基础信息
     * @return void
     */
    public function fillUpOrderInfo($aBaseOrderInfo) {
        $this->iProductCategory = $aBaseOrderInfo['product_category'];
        $this->iProductId       = $aBaseOrderInfo['product_id'];
        $this->iBusinessId      = $aBaseOrderInfo['business_id'];
        $this->iRequireLevel    = $aBaseOrderInfo['require_level'];
        $this->iComboType       = $aBaseOrderInfo['combo_type'];
        $this->iOrderType       = $aBaseOrderInfo['order_type'];
        $this->iAirportType     = $aBaseOrderInfo['airport_type'];
        $this->sEstimateID      = UtilHelper::getEstimateId($this->iProductId, $this->iRequireLevel, $this->iComboType);

        // req参数与识别数据合并
        if (0 == $this->iAirportId) {
            $this->iAirportId = $aBaseOrderInfo['order_info']['airport_info']['airport_id'] ?? 0;
        }

        // 发单 combo_type 从报价单取，combo_type 加缓存，兜底报价单获取失败的情况
        $this->_cacheComboType();
        //处理司机相关逻辑
        $this->_designatedDriver($aBaseOrderInfo['designated_driver_info']);
        return;
    }

    // 发单 combo_type 从报价单取，combo_type 加缓存，兜底报价单获取失败的情况
    // combo_type 目前只对接送机场景有影响，所以只兜底 combo_type 为接送机的场景，如有其它场景需要再添加即可
    /**
     * @return void
     */
    private function _cacheComboType() {
        if (in_array($this->iComboType, [Horae::TYPE_COMBO_FROM_AIRPORT, Horae::TYPE_COMBO_TO_AIRPORT])) {
            $sCurrentTraceId = Trace::traceId();
            $aAirportInfo    = ['combo_type' => $this->iComboType];
            $oOrderAirport   = new OrderAirPort();
            $oOrderAirport->setAirPortInfoByTraceId($sCurrentTraceId, $aAirportInfo);
        }

        return;
    }

    /**
     * @param array $aDriverInfo aDriverInfo
     * @return void
     */
    private function _designatedDriver($aDriverInfo) {
        if (empty($aDriverInfo)) {
            return;
        }

        //如果存在对应的司机信息，则赋值。此时driverId可能为空（豪华车）
        $this->aDesignatedDriverInfo = $aDriverInfo;
        $iDesignateDriverId          = $aDriverInfo['driverId'];
        if (empty($iDesignateDriverId)) {
            return;
        }

        //如果是具体的真实司机
        if ($iDesignateDriverId > 0) {
            $this->sDesignatedDriver = (string)$iDesignateDriverId;
            //指定司机时，需获取司机的星级用于计费
            $tags  = BaseDfsComModel::getInstance()->getDFS($iDesignateDriverId, ['lux_driver_specific_feature'])['lux_driver_specific_feature'];
            $aTags = empty($tags) ? [] : (array)(json_decode($tags, true)['specific_grade_feature']);
            foreach ($aTags as $tag) {
                if (-1 != $tag && $tag > -100) {//英语司务员和100以后的值预留给其他的
                    $this->sDesignatedDriverTag = (string)$tag;
                    break;
                }
            }
        }

        //如果不是真实司机
        if ($iDesignateDriverId < 0) {
            $this->sDesignatedDriverTag = (string)$iDesignateDriverId;
        }

        return;
    }

    /**
     * @return array
     */
    public function getOrderNTuple() {
        return [
            'business_id'      => $this->business_id,
            'carpool_type'     => $this->carpool_type,
            'airport_type'     => $this->airport_type,
            'is_special_price' => $this->is_special_price,
        ];
    }

    /**
     * @return array
     */
    public function toArray() {
        $aObjectVars = get_object_vars($this);
        $aOrderInfo  = array();
        foreach ($aObjectVars as $key => $value) {
            $key   = substr($key, 1);
            $field = $this->_uncamelize($key);
            $aOrderInfo[$field] = $value;
        }

        return $aOrderInfo;
    }

    /**
     * @param string $camelCaps camelCaps
     * @param string $separator separator
     * @return string
     */
    private function _uncamelize($camelCaps, $separator = '_') {
        return strtolower(preg_replace('/([a-z])([A-Z])/', '$1' . $separator . '$2', $camelCaps));
    }
}
