<?php
declare(strict_types=1);
namespace PreSale\Logics\estimatePriceV2;

use BizLib\Constants;
use BizLib\Utils\PayType;
use BizLib\Utils\Common as UtilsCommon;
use BizCommon\Models\Pay\Payments;
use BizCommon\Models\Order\Order;
use BizCommon\Models\Tools\BusinessApi;

/**
 * 乘客预估获取支付方式相关逻辑.
 *
 * <AUTHOR>
 * @date(2017-03-13)
 */
class PaymentsLogic
{
    //1. 巴西，2. new_data(新版数据), 3. old_data(兼容旧版数据)
    const PAYMENTS_TYPE_BAXI = 1;
    const PAYMENTS_TYPE_NEW  = 2;
    const PAYMENTS_TYPE_OLD  = 3;
    /**
     * 企业用户支持类型.
     */
    const BUSSINESS_USER_TYPE = 2;

    const CARPOOL_COMBO_TYPE = [
        4, //拼车,站点拼车
        302, //跨城拼车
    ];

    /*
     *@var $_oInstance
     */
    protected static $_oInstance = null;

    /**
     * [$_bIsBuinessUser 是否是企业用户].
     *
     * @var bool
     */
    protected $_bBusinessUser = false;

    /**
     * [$_bIsUseCoupon 是否支持用券].
     *
     * @var bool
     */
    protected $_bUseCoupon = true;

    /**
     * [$_bIsCarpoolUseCoupon 拼车是否支持用券].
     *
     * @var bool
     */
    protected $_bCarpoolUseCoupon = true;

    /**
     * @var array
     */
    protected $_aOrderInfo = array();

    /**
     * [$_fDynamicTotalFee 预估不拼车价].
     *
     * @var float
     */
    protected $_fDynamicTotalFee = 0.0;

    protected $_aBillInfo = [];

    /**
     * [$_aPassengerInfo 乘客信息].
     *
     * @var array
     */
    protected $_aPassengerInfo = array();

    /**
     * [$_aCommonInfo 通用信息参数].
     *
     * @var array
     */
    protected $_aCommonInfo = array();

    /**
     * @var array 选中的支付方式
     */
    protected $_aSelectedPayTypes = array();

    /**
     * PaymentsLogic constructor.
     *
     * @param array $aParams
     */
    private function __construct(array $aParams) {
        $this->_aOrderInfo       = $aParams['order_info'];
        $this->_fDynamicTotalFee = $aParams['bill_info']['noncarpool']['dynamic_total_fee'];
        $this->_aBillInfo        = $aParams['bill_info'];
        $this->_aPassengerInfo   = $aParams['passenger_info'];
        $this->_aCommonInfo      = $aParams['common_info'];
    }

    /**
     * [getInstance 获取单例].
     *
     * @param array $aParams
     *
     * @return PaymentsLogic
     */
    public static function getInstance(array $aParams) {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self($aParams);
        }

        return self::$_oInstance;
    }

    /**
     * [_setBussinessUser 设置是否是企业用户].
     *
     * @param bool $bBusinessUser
     */
    protected function _setBusinessUser(bool $bBusinessUser) {
        $this->_bBusinessUser = $bBusinessUser;
    }

    /**
     * [isBussinessUser 获取是否是企业用户].
     *
     * @return bool
     */
    public function isBusinessUser() {
        return $this->_bBusinessUser;
    }

    /**
     * [_setUseCoupon 设置是否用券].
     *
     * @param bool $bUseCoupon
     */
    protected function _setUseCoupon(bool $bUseCoupon) {
        $this->_bUseCoupon = $bUseCoupon;
    }

    /**
     * [_setCarpoolUseCoupon 设置拼车是否用券].
     *
     * @param bool $bCarpoolUseCoupon
     */
    protected function _setCarpoolUseCoupon(bool $bCarpoolUseCoupon) {
        $this->_bCarpoolUseCoupon = $bCarpoolUseCoupon;
    }

    /**
     * [isUseCoupon 获取企业用户是否用券].
     *
     * @return bool
     */
    public function isUseCoupon() {
        return $this->_bUseCoupon;
    }

    /**
     * [isUseCoupon 获取企业用户是否用券].
     *
     * @return bool
     */
    public function isCarpoolUseCoupon() {
        return $this->_bCarpoolUseCoupon;
    }

    /**
     * 主要事项：
     * 1. 准备获取支付方式相关参数
     * 2. 巴西业务线调用收银台获取支付方式接口,其他业务线调用企业级支付方式接口
     * 3. 默认支付方式处理
     * 4. 支付方式数据返回.
     *
     * [getUserPayInfo 获取乘客支付信息（是否是企业支付）]
     *
     * @param [array] $[name] [是否企业支付相关参数]
     *                        array(
     *                        'user_type','area', 'estimate_fee', 'tip', 'product_id','token', 'callcar_type', ['callcar_phone'],'otype'
     *                        ,'require_level', 'flat', 'flng', 'fromName','tlat','tlng','toName','departure_time','combo_type', 'channel'
     *                        ,'pay_type_switch'
     *
     * )
     *
     * @return array
     */
    public function execute() {
        $aUserPayInfo = [];
        $aEstimate['user_pay_info']     = $aUserPayInfo;
        $aEstimate['payments_separate'] = $aRet['payments_separate'] ?? false;
        return $aEstimate;
    }

    //获取支付方式,
    private function _getPaymentList() {
        $iPassengerId    = $this->_aPassengerInfo['pid'] ?? 0;
        $sPassengerPhone = $this->_aPassengerInfo['phone'] ?? '';
        if (empty($iPassengerId) || empty($sPassengerPhone)) { // 支付方式的获取，必须要登陆
            return array();
        }

        $this->payments = Payments::getInstance();

        $aExtraInfo = [ // 收银需要一些通用参数
            'appversion'  => $this->_aCommonInfo['app_version'],
            'from'        => $this->_aCommonInfo['from'], // 收银、企业要求告知准确来源
            'app_channel' => $this->_aOrderInfo['channel'] ?? 0,
            'open_id'     => $this->_aCommonInfo['openid'] ?? '',
            'device_id'   => !empty($this->_aCommonInfo['suuid']) ? $this->_aCommonInfo['suuid'] : $this->_aCommonInfo['imei'],
            'switch'      => 1,
        ];
        $sLang      = $this->_aCommonInfo['lang'];
        $iTotalFee  = (int)($this->_fDynamicTotalFee * 100);    // 单位：分

        //http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=97832391
        //接口定义的字段在这里明确转化下, 防止后续修改影响下游
        $aOrderInfo = $this->_aOrderInfo;
        $aOrderInfo['passenger_id']    = (string)($iPassengerId);
        $aOrderInfo['passenger_phone'] = (string)($sPassengerPhone);
        $aOrderInfo['driver_id']       = '0';
        $aOrderInfo['driver_phone']    = '0';
        $aOrderInfo['area']            = (int)($aOrderInfo['area']);
        $aOrderInfo['order_id']        = '0';
        $aOrderInfo['channel']         = (int)($aOrderInfo['channel']);
        $aOrderInfo['product_id']      = (int)($aOrderInfo['product_id']);
        $aOrderInfo['starting_lat']    = (string)($aOrderInfo['from_lat']);
        $aOrderInfo['starting_lng']    = (string)($aOrderInfo['from_lng']);
        $aOrderInfo['starting_name']   = (string)($aOrderInfo['starting_name']);
        $aOrderInfo['dest_lat']        = (string)($aOrderInfo['to_lat']);
        $aOrderInfo['dest_lng']        = (string)($aOrderInfo['to_lng']);
        $aOrderInfo['dest_name']       = (string)($aOrderInfo['dest_name']);
        $aOrderInfo['departure_time']  = (int)($aOrderInfo['departure_time']);
        $aOrderInfo['type']            = (int)($aOrderInfo['order_type']);
        $aOrderInfo['order_type']      = (int)($aOrderInfo['order_type']);
        $aOrderInfo['combo_type_list'] = $this->_getListComboType($aOrderInfo);
        $aOrderInfo['origin_id']       = (int)($this->_aCommonInfo['origin_id']);
        $aOrderInfo['user_type']       = (int)($aOrderInfo['user_type']);
        $aOrderInfo['payments_type']   = (int)($aOrderInfo['payments_type']);
        $aOrderInfo['require_level']   = (string)($aOrderInfo['require_level']);
        $aOrderInfo['call_car_type']   = (string)($aOrderInfo['call_car_type']);
        $aOrderInfo['platform_type']   = UtilsCommon::getPlatformType(
            $this->_aCommonInfo['platform_type'],
            $this->_aCommonInfo['client_type'],
            (int)($this->_aCommonInfo['guide_request']),
            $this->_aCommonInfo['data_type']
        );
        $aOrderInfo['product_info']    = $this->_getProductInfo($aOrderInfo['combo_type_list']);
        $aOrderInfo['combo_type']      = (int)($aOrderInfo['combo_type']);
        $aOrderInfo['coupon_type']     = (int)($aOrderInfo['coupon_type']);
        $aOrderInfo['call_car_phone']  = (string)($aOrderInfo['call_car_phone']);

        $aPaymentResult = $this->payments->getPaymentListFromCashier($aOrderInfo, $iTotalFee, $aExtraInfo, $sLang);
        if (!isset($aPaymentResult['results']) || !is_array($aPaymentResult['results'])) {
            return [];
        }

        switch ((int)($aPaymentResult['type'] ?? 0)) {
            //1. 巴西 上线过渡阶段
            case 0: //上线时, 老接口没有type
                // no break
            case self::PAYMENTS_TYPE_BAXI:
                $aPayList = $this->_formatPaymentList($aPaymentResult['results']);

                // 不允许使用券的支付方式
                $aForbidUseCouponPayTypes = PayType::getPayTypeList($this->_aOrderInfo, 'forbid_use_coupon');
                if (empty($this->_aSelectedPayTypes)
                    || (!empty($this->_aSelectedPayTypes) && !empty(array_intersect($this->_aSelectedPayTypes, $aForbidUseCouponPayTypes)))
                    || empty($iTotalFee)
                ) {
                    $this->_setUseCoupon(false);
                    $this->_setCarpoolUseCoupon(false); //巴西暂时没有拼车, 拼车和非拼车保持一致
                }

                $aUserPayInfo = [
                    'busi_payments' => $aPayList,
                    'business_msg'  => '',  // default
                    'is_region'     => 1,   // default
                    // 'default_tag'   => 0,   // default, 该字段废弃；选中方式使用 busi_payments 里的 isSelected 判断
                ];

                if (!empty($aPaymentResult['results']['business_config'])) {
                    $aUserPayInfo['business_config'] = $aPaymentResult['results']['business_config'];
                }
                return ['user_pay_info' => $aUserPayInfo,];
                //2. 新支付结构
            case self::PAYMENTS_TYPE_NEW:
                return [
                    'payments_separate' => true,
                    'user_pay_info'     => $this->_formatPayments($aPaymentResult['results']),
                ];

                //3. 老版本只有企业支付方式
            case self::PAYMENTS_TYPE_OLD:
                return [
                    'user_pay_info' => $this->_formatBusinessResutlt($aPaymentResult['results']),
                ];
            default:
                return [];
        }

        return [];
    }

    private function _formatPayments($aList) {
        if (empty($aList)) {
            return [];
        }

        $aFormatList = [];
        foreach ($aList as $aVal) {
            $sKey = ($bCarpool = in_array($aVal['combo_type'], self::CARPOOL_COMBO_TYPE)) ? 'carpool' : 'noncarpool';

            //暂时只有拼车和非拼车两种模式同时返回
            $aFormatList[$sKey] = [
                'busi_payments' => $this->_formatPaymentList($aVal, $iPaymentOldStyle = 1),
                'is_region'     => $aVal['is_region'] ?? null,
            ];

            if (empty($aFormatList[$sKey]['busi_payments'])) {
                continue;
            }

            //兼容巴西, 巴西乘客可选多种支付方式, 国内只能选一种 *** 国内不支持多种支付方式
            $aAllPayType     = array_column($aFormatList[$sKey]['busi_payments'], 'tag');
            $aFormatPayments = array_combine($aAllPayType, $aFormatList[$sKey]['busi_payments']);
            foreach ($this->_aSelectedPayTypes as $iPayType) {
                if (isset($aFormatPayments[$iPayType]['can_use_coupon']) && empty($aFormatPayments[$iPayType]['can_use_coupon'])) {
                    $bCarpool ? $this->_setCarpoolUseCoupon(false) : $this->_setUseCoupon(false);
                }
            }
        }

        return $aFormatList;
    }

    private function _formatBusinessResutlt($aUserPayInfo) {
        if (empty($aUserPayInfo)) {
            return [];
        }

        $aValidPayType = array(
            Order::BUSINESS_PAY_BY_BUSINESS_BALANCE,
            Order::BUSINESS_PAY_BY_WITHOUT_BALANCE,
            Order::BUSINESS_PAY_BY_PERSON,
            Order::BUSINESS_PAY_BY_PERSON_WITH_REFOUND,
            Order::BUSINESS_PAY_NOT_SUPPORT,
            ORDER_PAY_TYPE_99_AGENT,
        );
        if (!empty($this->_aOrderInfo['payments_type']) && in_array($this->_aOrderInfo['payments_type'], $aValidPayType)) {
            $aPayment = array();
            foreach ($aUserPayInfo['busi_payments'] as $aPayments) {
                $aPayment[] = $aPayments['tag'];
            }

            if (in_array($this->_aOrderInfo['payments_type'], $aPayment)) {
                $aUserPayInfo['default_tag'] = $this->_aOrderInfo['payments_type'];
            }
        }

        if (in_array($aUserPayInfo['default_tag'], array(Order::BUSINESS_PAY_BY_BUSINESS_BALANCE, ORDER_PAY_TYPE_99_AGENT))) {
            $this->_setUseCoupon(false);
            $this->_setCarpoolUseCoupon(false); //巴西暂时没有拼车, 拼车和非拼车保持一致
        }

        return $aUserPayInfo;
    }

    // 注释-待删
//    /**
//     * 校验用户是否满足企业支付条件.
//     *
//     * @return array
//     */
//    private function _getUserPayStatus() {
//        $aUserPayInfo = array();
//        if ($this->_aOrderInfo['product_id']) {
//            $iTimestampMeetingTime = time();
//            $this->businessApi     = BusinessApi::getInstance();
//            $sPhone   = $this->_aPassengerInfo['phone'];
//            $bCallCar = $this->_aOrderInfo['call_car_type'] > 0 && !empty($this->_aOrderInfo['call_car_phone'])
//            && $this->_aOrderInfo['call_car_phone'] != $sPhone ? true : false;   //是否代叫车
//            if (0 != $sPhone && '' != $sPhone) {
//                $aReqParams['passenger_phone']  = $bCallCar ? $this->_aOrderInfo['call_car_phone'] : $sPhone;    //乘车人
//                $aReqParams['call_phone']       = $bCallCar ? $sPhone : '';  //叫车人
//                $aReqParams['otype']            = $this->_aOrderInfo['coupon_type'];
//                $aReqParams['strive_car_level'] = $this->_aOrderInfo['require_level'];
//                $aReqParams['product_id']       = $this->_aOrderInfo['product_id'];
//                $aReqParams['cost']         = $this->_fDynamicTotalFee;
//                $aReqParams['flat']         = $this->_aOrderInfo['from_lat'];
//                $aReqParams['flng']         = $this->_aOrderInfo['from_lng'];
//                $aReqParams['start_name']   = $this->_aOrderInfo['from_name'];
//                $aReqParams['tlng']         = $this->_aOrderInfo['to_lng'];
//                $aReqParams['tlat']         = $this->_aOrderInfo['to_lat'];
//                $aReqParams['end_name']     = $this->_aOrderInfo['to_name'];
//                $aReqParams['area']         = $this->_aOrderInfo['area'];
//                $aReqParams['meeting_time'] = 0 != $this->_aOrderInfo['departure_time'] ? $this->_aOrderInfo['departure_time'] : $iTimestampMeetingTime;
//                $aReqParams['combo_type']   = $this->_aOrderInfo['combo_type'];
//                $aReqParams['channel']      = $this->_aOrderInfo['channel'];
//                $aReqParams['app_version']  = $this->_aCommonInfo['app_version'];
//                $aUserPayInfo = $this->businessApi->getUserPayPermission($aReqParams);
//            }
//        }
//
//        return $aUserPayInfo;
//    }

    /**
     * 格式化支付方式列表, 保持与businessApi返回的结构一致.
     *
     * @param array $aPaymentResult 原支付方式信息
     *
     * @return array
     */
    private function _formatPaymentList(array $aPaymentResult, int $iPaymentOldStyle = 0) {
        $iPayType = $this->_aOrderInfo['payments_type'];
        // 端上传的乘客选中支付类型
        $iDefaultTag = 0;
        if (!empty($aPaymentResult['default_pay_type'])) {  // 收银台返回的默认支付方式
            $iDefaultTag = (int)($aPaymentResult['default_pay_type']);
        }

        if ($iPaymentOldStyle) {
            $this->_aSelectedPayTypes = [];
        }

        $aPayListResult = [];
        if (!empty($aPaymentResult['payments'])) {
            $aSelectTag = array();
            foreach ($aPaymentResult['payments'] as $aItem) {
                $iTag = (int)($aItem['pay_type']);

                // 对返回的字段进行一些转换
                $aItemNew        = [];
                $aItemNew['tag'] = $iTag;
                $aItemNew['msg'] = $aItem['channelName'];
                $aItemNew['icon_url']   = $aItem['icon_url'];
                $aItemNew['channelID']  = (int)($aItem['channelID']);
                $aItemNew['isOffline']  = (int)($aItem['isOffline']);
                $aItemNew['isSigned']   = (int)($aItem['isSigned']);
                $aItemNew['isSelected'] = 0; // 设置支付方式是否选中

                $aItemWarnMsg         = !empty($aItem['warn_msg']) ? json_decode($aItem['warn_msg'], true) : [];
                $aItemExtraInfo       = !empty($aItem['extraInfo']) ? json_decode($aItem['extraInfo'], true) : [];
                $aItemNew['show_msg'] = isset($aItemWarnMsg['show_msg']) ? $aItemWarnMsg['show_msg'] : '';
                $aItemNew['disabled'] = isset($aItemWarnMsg['disabled']) ? (int)($aItemWarnMsg['disabled']) : 0;
                $aItemNew['card']     = isset($aItemExtraInfo['card_suffix']) ? (string)($aItemExtraInfo['card_suffix']) : '';
                $aItemNew['card_org'] = isset($aItemExtraInfo['card_org']) ? (string)($aItemExtraInfo['card_org']) : '';
                $aItemNew['recommendSignPayType'] = (int)($aItem['recommendSignPayType']);
                $aItemNew['can_use_coupon']       = (int)($aItem['can_use_coupon'] ?? 1);

                // 以下参数复用 businessAPI, 保持默认就行
                $aItemNew['text']         = $aItem['text'] ?? '';
                $aItemNew['business_url'] = $aItem['business_url'] ?? '';
                $aItemNew['business_const_set'] = (int)($aItem['business_const_set'] ?? 0);
                $aItemNew['company_pay_msg']    = $aItem['company_pay_msg'] ?? '';
                $aItemNew['disabled_type']      = $aItem['disabled_type'] ?? 0;

                if (isset($aItem['company_carpool_open'])) {
                    $aItemNew['company_carpool_open']   = $aItem['company_carpool_open'];
                    $aItemNew['company_carpool_config'] = $aItem['company_carpool_config'];
                }

                if (isset($aItem['alert_window'])) {
                    $aItemNew['alert_window'] = $aItem['alert_window'];
                }

                $aPayListResult[$iTag] = $aItemNew;

                if ($iPaymentOldStyle) {
                    if ($iTag == $iPayType) {
                        $aSelectTag[] = $iTag;
                    }
                } else {
                    if (($iPayType & $iTag) === $iTag) {
                        $aSelectTag[] = $iTag;
                    }
                }
            }

            // 优先使用端上传的支付方式. 其次使用 收银台 返回的default_pay_type
            if (empty($aSelectTag) && isset($aPayListResult[$iDefaultTag])) {
                $aSelectTag[] = $iDefaultTag;
            }

            $this->_aSelectedPayTypes = $aSelectTag;

            // 设置选中态
            if (!empty($aSelectTag)) {
                foreach ($aSelectTag as $iTag) {
                    $aPayListResult[$iTag]['isSelected'] = 1;
                }
            }
        }

        return array_values($aPayListResult);
    }

    private function _getListComboType($aOrderInfo) {
        $aComboType[] = $aOrderInfo['combo_type'];
        if ($this->_aBillInfo['is_carpool_open']) {
            $aComboType[] = 4;
        }

        return array_unique($aComboType);
    }

    /**
     * 生成产品四元组.
     *
     * @param $aComboList
     *
     * @return array
     */
    private function _getProductInfo($aComboList) {
        return array_map(
            function ($iComboType) {
                return [
                    'product_id'    => $this->_aOrderInfo['product_id'],
                    'combo_type'    => $iComboType,
                    'require_level' => $this->_aOrderInfo['require_level'],
                    'type'          => $this->_aOrderInfo['order_type'],
                ];
            },
            $aComboList
        );
    }
}
