<?php

namespace PreSale\Logics\estimatePriceV2;

use Biz<PERSON>ib\Client\AthenaApiClient;
use Biz<PERSON>ib\Log as NuwaLog;
use BizLib\Utils\UtilHelper;
use BizLib\Exception\BizException;
use BizLib\Exception\ExceptionWithResp;
use PreSale\exception\LogicException;
use BizLib\Exception\InvalidArgumentException;
use BizLib\ErrCode;

/**
 * 获取账单、支付方式、优惠券、活动券相关数据逻辑类.
 *
 * @deprecated 大概率没用了已经
 * @date(2017-05-23)
 */
class EstimateLogic
{
    /** @var $athena AthenaApiClient */
    public $athena;
    /*
     * @var EstimateLogic $_oInstance
     */
    protected static $_oInstance = null;

    /**
     * @var \CI_Controller
     */
    private $_oCI = null;

    /**
     * [$_aParams common、order、passenger、custom_service_info、one_key_activity].
     *
     * @var array
     */
    protected $_aParams = array();

    const PAY_TYPE_NEW = 2;
    const DEGRADE_OPEN = true; //默认倒流不降级

    /**
     * EstimateLogic constructor.
     *
     * @param array $aParams
     */
    private function __construct(array $aParams) {
        $this->_aParams = $aParams;
    }

    /**
     * @param array $aParams
     *
     * @return EstimateLogic
     */
    public static function getInstance(array $aParams) {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self($aParams);
        }

        return self::$_oInstance;
    }

    // 注释-待删
//    /**
//     * 方案wiki:http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=98400320
//     * 1.预估请求有athena_params参数且请求athena-api的apollo开关打开,
//     * 请求athena-api获取账单预估返回值、企业支付方式接口返回值、券系统获取可用券返回值、乘客运营获取活动券返回值等预估信息.
//     *
//     * 2.不满足1条件时执行下面逻辑:
//     *   1)账单系统预估接口交互逻辑,获取预估车费;
//     *   2)企业级支付方式接口交互逻辑,获取支付方式;
//     *   3)券系统获取可用券接口交互逻辑,获取可用券;
//     *   4)乘客运营获取活动券接口交互逻辑,获取活动券;
//     *
//     * 3.将车费数据、支付方式数据、券抵扣数据返回
//     *
//     * @return array
//     */
//    public function getResponseData() {
//        $oParams       = ParamsLogic::getInstance();
//        $aAthenaParams = array();
//        $sAthenaParams = $this->_aParams['common_info']['athena_params'];
//        if (!empty($sAthenaParams)) {
//            $aAthenaParams = json_decode($sAthenaParams, true);
//        }
//
//        try {
//            //乘客端上传调用Athena参数
//            if (count($aAthenaParams) > 0) {
//                //
//                //  一键降级，降级后不访问 Athena.
//                //
//                //  默认走 Athena，除非明确降级 bOpen = true
//                $bFromAthena = true;
//                $iArea       = $this->_aParams[0]['order_info']['area'] ?? 0;
//                $bOpen       = UtilHelper::getFusingSwitch('passenger_estimate_athena_degrade_switch', 'default', (int)($iArea));
//
//                if (self::DEGRADE_OPEN === $bOpen) {
//                    NuwaLog::warning(
//                        ErrCode\Msg::formatArray(
//                            ErrCode\Code::E_COMMON_API_DEGRADE,
//                            array(
//                                'describe'      => 'athena is degrade',
//                                'reqArea'       => $iArea,
//                                'requestParams' => json_encode($this->_aParams),
//                            )
//                        )
//                    );
//
//                    $bFromAthena = false;
//                }
//
//                // 单预估已经没有导流了，且春节期间单预估不会有任何导流
//                // 为了满足春节服务费需求，这里不会再走单预估的athena，若后续单预估仍有导流需求，这里的开关记得需要放开
//                // 春节到现在一直没有走导流，因此判断无导流需求，下掉开关。
//                $oApollo      = new \Xiaoju\Apollo\Apollo();
//                $bWhiteSwitch = $oApollo->featureToggle(
//                    'festival_service_fee_whitelist_switch',
//                    array(
//                        'key'   => rand(1, 500000),
//                        'phone' => $this->_aParams['passenger_info']['phone'],
//                        'pid'   => $this->_aParams['passenger_info']['pid'],
//                        'city'  => $this->_aParams['order_info']['area'],
//                    )
//                )->allow();
//                $bBlackSwitch = $oApollo->featureToggle(
//                    'festival_service_fee_degrade_switch',
//                    array(
//                        'key'   => rand(1, 500000),
//                        'phone' => $this->_aParams['passenger_info']['phone'],
//                        'city'  => $this->_aParams['order_info']['area'],
//                    )
//                )->allow();
//                if ($bWhiteSwitch && !$bBlackSwitch) {
//                    $bFromAthena = false;
//                }
//
//                if ($bFromAthena) {
//                    $aAthenaRequest = $oParams->getAthenaParams($aAthenaParams, $this->_aParams);
//                    $aAthenaResult  = $this->_getAthenaResponse($aAthenaRequest);
//                    $aEstimateInfo  = array(
//                        'bill_info'     => $aAthenaResult['bill_info'],
//                        'payments_info' => $aAthenaResult['payments_info'],
//                        'activity_info' => $aAthenaResult['activity_info'],
//                        'check_sum'     => $aAthenaResult['check_sum'],
//                    );
//                    if (GLOBAL_SUCCESS != $aAthenaResult['errno'] || !$oParams->checkSign($aEstimateInfo)) {
//                        throw new InvalidArgumentException(
//                            ErrCode\Code::E_ATHENA_REQUEST_FAIL,
//                            array('athena_response' => $aAthenaResult,)
//                        );
//                    }
//
//                    $aResponseInfo = $oParams->getResponseParams(
//                        json_decode($aEstimateInfo['bill_info'], true),
//                        json_decode($aEstimateInfo['payments_info'], true),
//                        json_decode($aEstimateInfo['activity_info'], true),
//                        (string)($aAthenaResult['athena_info'])
//                    );
//
//                    return $aResponseInfo;
//                }
//            }
//        } catch (\Exception $e) {
//            NuwaLog::warning(
//                ErrCode\Msg::formatArray(
//                    ErrCode\Code::E_ATHENA_REQUEST_FAIL,
//                    array('message' => $e->getMessage())
//                )
//            );
//        }
//
//        //
//        //  请求Athena失败
//        //  或 一键降级，不调用 Athena
//        //  或 透传数据被修改兜底逻辑.
//        $aEstimateResult = $this->execute();
//        $aResponseInfo   = $oParams->getResponseParams(
//            json_decode($aEstimateResult['bill_info'], true),
//            json_decode($aEstimateResult['payments_info'], true),
//            json_decode($aEstimateResult['activity_info'], true)
//        );
//
//        return $aResponseInfo;
//    }

    /**
     * 获取账单预估返回值、企业支付方式接口返回值、收银台支付方式接口返回值、券系统获取可用券返回值、乘客运营获取活动券返回值等预估信息.
     *
     * 1)账单系统预估接口交互逻辑,获取预估车费;
     * 2)企业级支付方式接口交互逻辑,获取支付方式;
     * 3)运营活动:券系统获取可用券接口交互逻辑,获取可用券;乘客运营获取活动券接口交互逻辑,获取活动券;
     * 4)将车费数据、支付方式数据、券抵扣数据返回
     *
     * @return array
     */
    public function execute() {
        try {
            $oParams = ParamsLogic::getInstance();

            //账单
            $aBillInfo = BillLogic::getInstance($this->_aParams)->execute();

            //支付方式
            $aPaymentsParams = $oParams->getPaymentsParams($aBillInfo);
            $oPayments       = PaymentsLogic::getInstance($aPaymentsParams);
            $aPaymentsInfo   = $oPayments->execute();
            //运营活动
            $aActivityParams = $oParams->getActivityParams($aBillInfo, $oPayments->isUseCoupon(), $oPayments->isCarpoolUseCoupon());
            $aActivityInfo   = ActivityLogic::getInstance($aActivityParams)->execute();

            //返回数据
            $aResponseInfo = $oParams->getEstimatePriceInfo($aBillInfo, $aPaymentsInfo, $aActivityInfo);
        } catch (\Exception $e) {
            if ($e instanceof BizException || $e instanceof ExceptionWithResp) {
                throw $e;
            }

            throw new LogicException($e->getCode(), $e->getMessage());
        }

        return $aResponseInfo;
    }

    // 注释-待删
//    /**
//     * @param $aAthenaRequest
//     *
//     * @return array
//     */
//    private function _getAthenaResponse($aAthenaRequest) {
//        $this->athena  = new AthenaApiClient();
//        $aAthenaResult = $this->athena->getBubbleInfo(
//            $aAthenaRequest['price_params'],
//            $aAthenaRequest['athena_bubbe_params'],
//            $aAthenaRequest['trace_params']
//        );
//
//        return $aAthenaResult;
//    }
}
