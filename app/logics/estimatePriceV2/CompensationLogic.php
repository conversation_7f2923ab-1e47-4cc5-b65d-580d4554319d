<?php

namespace PreSale\Logics\estimatePriceV2;

use Biz<PERSON>om<PERSON>\Constants\OrderNTuple;
use Biz<PERSON>ib\Client\CompensationDirpcClient;
use BizLib\Utils\ProductCategory;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Log;
use BizCommon\Utils\Product;
use Nuwa\ApolloSDK\Apollo;

/**
 * Class CompensationLogic
 * @package PreSale\Logics\estimatePriceV2
 */
class CompensationLogic
{
    private static $_oInstance    = null;
    private $_oCompensationClient = null;
    private $_oProductCategory    = null;
    private $_aMultiCompensationAbilityResult = [];

    /**
     * CompensationLogic constructor.
     */
    private function __construct() {
        $this->_oCompensationClient = new CompensationDirpcClient();
        $this->_oProductCategory    = new ProductCategory();
    }

    /**
     * @return CompensationLogic|null
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * 预估接口获取赔付能力
     * @param array $aInfos $aInfos
     * @return void
     */
    public function getMultiCompensationAbility($aInfos) {
        $aInfo = current($aInfos);
        $aProductCategoryIds = [];
        $bFromMainBubble     = false;
        if (!empty($aInfo['order_info']) && OrderNTuple::COMMON_PRODUCT_ID_ANY_CAR == $aInfo['order_info']['business_id']) {
            if (!empty($aInfo['bill_info'])) {
                $aMultiInfo = $aInfo['bill_info']['multi_info'] ?? [];
            } else {
                $aMultiInfo = [];
            }

            foreach ($aMultiInfo as $aItem) {
                $aNTuple            = [
                    'business_id'   => $aItem['business_id'],
                    'require_level' => $aItem['require_level'],
                    'combo_type'    => $aItem['combo_type'],
                    'level_type'    => $aItem['level_type'],
                ];
                $sProductCategoryId = $this->_oProductCategory->getProductCategoryByNTuple($aNTuple);
                if (ProductCategory::PRODUCT_CATEGORY_DEFAULT == $sProductCategoryId) {
                    // 监控有多少情况会获取不到 product_category_id
                    Log::notice('get product category id by nTuple failed' . json_encode($aNTuple));
                    continue;
                }

                $aProductCategoryIds[] = $sProductCategoryId;
            }
        } else {
            foreach ($aInfos as $aInfoItem) {
                $aNTuple            = [
                    'business_id'           => $aInfoItem['order_info']['business_id'],
                    'require_level'         => $aInfoItem['order_info']['require_level'],
                    'combo_type'            => $aInfoItem['order_info']['combo_type'],
                    'level_type'            => $aInfoItem['order_info']['level_type'],
                    'carpool_type'          => $aInfoItem['order_info']['carpool_type'],
                    'spacious_car_alliance' => $aInfoItem['order_info']['spacious_car_alliance'],
                ];
                $sProductCategoryId = $this->_oProductCategory->getProductCategoryByNTuple($aNTuple);
                if (ProductCategory::PRODUCT_CATEGORY_DEFAULT == $sProductCategoryId) {
                    // 监控有多少情况会获取不到 product_category_id
                    Log::notice('get product category id by nTuple failed' . json_encode($aNTuple));
                    continue;
                }

                $aProductCategoryIds[] = $sProductCategoryId;
            }

            $bFromMainBubble = true;
        }

        $aReqParams = [
            'from'                    => 'pre-sale',
            'compensation_businesses' => implode(',', ['to_airport_insurance', 'no_car_aplus', 'no_answer_compensation']),
            'order_id'                => 0,
            'district'                => $aInfo['order_info']['district'],
            'area'                    => $aInfo['order_info']['area'],
            'passenger_id'            => $aInfo['passenger_info']['pid'],
            'passenger_phone'         => $aInfo['passenger_info']['phone'],
            'call_car_type'           => $aInfo['order_info']['call_car_type'],
            'from_webapp'             => $aInfo['common_info']['is_from_webapp'],
            'access_key_id'           => $aInfo['common_info']['access_key_id'],
            'airport_type'            => $aInfo['order_info']['airport_type'] ?? 0,
            'product_category_ids'    => implode(',', $aProductCategoryIds),
            'extra'                   => json_encode(['from_main_bubble' => $bFromMainBubble], true),
            'app_version'             => $aInfo['common_info']['app_version'],
            'to_area'                 => $aInfo['order_info']['to_area'],
            'from_lat'                => $aInfo['order_info']['from_lat'],
            'from_lng'                => $aInfo['order_info']['from_lng'],
            'to_lat'                  => $aInfo['order_info']['to_lat'],
            'to_lng'                  => $aInfo['order_info']['to_lng'],
        ];

        $oResp = $this->_oCompensationClient->getMultiCompensationAbility($aReqParams);
        $this->_aMultiCompensationAbilityResult = $oResp['data'];
    }

// 注释-待删
//    /**
//     * 沟通组件接口，请求Compensation获取赔付能力
//     * @param array $aParams    入参
//     * @param array $aQuotation 报价单信息
//     * @return void
//     */
//    public function getMultiCompensationAbilityV2(array $aParams, array $aQuotation) {
//        $aGetMultiCompensationAbility = [
//            'actionList' => [
//                'getMultiCompensationAbility' => [
//                    'connectTimeoutMsec' => 30,
//                    'timeoutMsec'        => 150,
//                    'retry'              => 0,
//                ],
//            ],
//        ];
//        $this->_oCompensationClient   = new CompensationDirpcClient(CompensationDirpcClient::MODULE_NAME,$aGetMultiCompensationAbility);
//
//        if (count($aQuotation) > 0) {
//            $aPCId = $aEid = [];
//            foreach ($aQuotation as $sEid => $aQuotationItem) {
//                $aEid[]  = $sEid;
//                $aPCId[] = $aQuotationItem['product_category'];
//            }
//
//            $aReqParams = [
//                'from'                    => 'pre-sale',
//                'order_id'                => 0,
//                'call_car_type'           => 0,
//                'from_webapp'             => 0,
//                'airport_type'            => 0,
//                'compensation_businesses' => 'no_car_aplus',
//                'passenger_id'            => $aParams['pid'],
//                'passenger_phone'         => $aParams['phone'],
//                'access_key_id'           => $aParams['access_key_id'],
//                'app_version'             => $aParams['app_version'],
//                'extra'                   => json_encode(['from_main_bubble' => true], true),
//                'area'                    => $aParams['area'],
//                'client_type'             => $aParams['client_type'],
//                'product_category_ids'    => implode(',', $aPCId),
//                'estimate_ids'            => implode(',',$aEid),
//            ];
//            $oResp      = $this->_oCompensationClient->getMultiCompensationAbility($aReqParams);
//            if (0 == $oResp['errno'] && is_array($oResp['data'])) {
//                $this->_aMultiCompensationAbilityResult = $oResp['data'];
//            }
//        }
//    }


    /**
     * @return array
     */
    public function getMultiCompensationAbilityResult() {
        return $this->_aMultiCompensationAbilityResult;
    }
}
