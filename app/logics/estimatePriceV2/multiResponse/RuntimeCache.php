<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse;

/**
 *
 * Copyraight (c) 2021 xiaojukeji.com, Inc. All Rights Reserved.
 * @author: wang<PERSON><PERSON><PERSON><PERSON>@didichuxing.com
 * @date: 2021/1/4 11:58 上午
 * @desc: 运行时缓存；针对同入参多次调用的函数、同条件多次获取的配置进行缓存，减少文件读取、反序列化，提高性能
 * @wiki:
 *
 */

class RuntimeCache
{
    private static $_aRet;

    /**
     * @param mixed $sKey sKey
     * @return mixed
     */
    public static function get($sKey) {
        return self::$_aRet[$sKey] ?? null;
    }

    /**
     * set方法
     * @param mixed $sKey   sKey
     * @param mixed $mValue mValue
     * @return int
     */
    public static function set($sKey, $mValue) {
        self::$_aRet[$sKey] = $mValue;
        return 0;
    }
}
