<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\linkProduct;

use BizLib\Utils\Language;
use BizLib\Utils\ProductCategory;
use PreSale\Logics\estimatePriceV2\multiResponse\component\customService\MockFastWithSpaciousCarAlliance;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;

/**
 * Class SpaciousCarAlliance
 * @package PreSale\Logics\estimatePriceV2\multiResponse\linkProduct
 */
class SpaciousCarAlliance
{
    private $_iAccessKeyId;
    private $_sAppVersion;
    private $_aInfo;

    private $_iLinkFrom = ProductCategory::PRODUCT_CATEGORY_FAST; // 从快车
    private $_iLinkTo   = ProductCategory::PRODUCT_CATEGORY_SPACIOUS_CAR; //到车大

    /**
     * SpaciousCarAlliance constructor.
     * @param array $aInfo $aInfo
     */
    public function __construct($aInfo) {
        $this->_iAccessKeyId = $aInfo['common_info']['access_key_id'] ?? 0;
        $this->_sAppVersion  = $aInfo['common_info']['app_version'] ?? '';
        $this->_aInfo        = $aInfo ?? [];
    }

    /**
     * @param array $aEstimateData $aEstimateData
     * @return mixed
     */
    public function linkProduct($aEstimateData) {
        $iLinkFromIndex = $iLinkToIndex = -1;
        foreach ($aEstimateData as $iIndex => $aData) {
            if ($this->_iLinkFrom == $aData['product_category']) {
                $iLinkFromIndex = $iIndex;
            }

            if ($this->_iLinkTo == $aData['product_category']) {
                $iLinkToIndex = $iIndex;
            }
        }

        //在正常的表单中 隐藏车大联盟
        if ($iLinkFromIndex >= 0 && $iLinkToIndex >= 0) {
            $aEstimateData[$iLinkToIndex]['hidden'] = 1;
        }

        // 只针对小程序link
        if (!Util::isMiniApp($this->_iAccessKeyId)) {
            return $aEstimateData;
        }

        if (version_compare($this->_sAppVersion, '6.2.21') < 0) {
            return $aEstimateData;
        }

        if ($iLinkFromIndex >= 0 && $iLinkToIndex >= 0) {
            $aLinkConf = Language::getDecodedTextFromDcmp('config_text-link_product_spacious_car_alliance');
            $sAbTitle  = MockFastWithSpaciousCarAlliance::getTitle($this->_aInfo);
            $aLinkInfo = [
                'default_text'     => empty($sAbTitle) ? $aLinkConf['default_text'] : $sAbTitle,
                'select_text'      => empty($sAbTitle) ? $aLinkConf['select_text'] : $sAbTitle,
                'is_multi_select'  => 1,
                'icon_url'         => $aLinkConf['icon_url'],
                'info_url'         => $aLinkConf['info_url'],
                'product_category' => $aEstimateData[$iLinkToIndex]['product_category'],
                'is_select'        => (int)($aEstimateData[$iLinkFromIndex]['select_type'] && $aEstimateData[$iLinkToIndex]['select_type']),
            ];
            $aEstimateData[$iLinkFromIndex]['link_product'] = $aLinkInfo;
        }

        return $aEstimateData;
    }
}
