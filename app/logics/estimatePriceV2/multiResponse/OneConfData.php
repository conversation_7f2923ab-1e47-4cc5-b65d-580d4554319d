<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse;

use BizLib\Utils\Common as UtilsCommon;
use PreSale\Logics\estimatePriceV2\ParamsLogic;

/**
 * Class OneConfData
 * @package PreSale\Logics\estimatePriceV2\multiResponse
 */
class OneConfData
{
    /**
     * @var array|\PreSale\Logics\estimatePriceV2\one_conf
     */
    private $_aOneConfList;
    /**
     * @var object
     */
    private static $_oInstance;

    /**
     * OneConfData constructor.
     */
    public function __construct() {
        $this->_aOneConfList = ParamsLogic::getInstance()->getOneConf() ?? [];
    }

    /**
     * @return object|OneConfData
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * @param array $aInfo aInfo
     * @return array|mixed
     */
    public function getOneConfProductInfo($aInfo) {
        $sCarLevel       = $aInfo['order_info']['require_level'];
        $iBusinessId     = $aInfo['order_info']['business_id'];
        $iComboType      = $aInfo['order_info']['combo_type'];
        $iIsSpecialPrice = $aInfo['order_info']['n_tuple']['is_special_price'];
        $iCarpoolType    = $aInfo['order_info']['n_tuple']['carpool_type'];
        $iLevelType      = $aInfo['order_info']['level_type'] ?? 0;
        $bIsLiteApp      = UtilsCommon::isLiteVersion($aInfo['common_info']['app_version'], $aInfo['common_info']['client_type'], $aInfo['order_info']['channel']);
        $aResult         = [];
        foreach ($this->_aOneConfList as $aConf) {
            if (\BizCommon\Utils\Horae::isNTupleMatch(
                $aConf,
                [
                    'business_id'      => $iBusinessId,
                    'require_level'    => $sCarLevel,
                    'combo_type'       => $iComboType,
                    'is_special_price' => $iIsSpecialPrice,
                    'carpool_type'     => $iCarpoolType,
                    'level_type'       => $iLevelType,
                ]
            )
            ) {
                $aResult = $aConf;
                break;
            }

            if ($aConf['estimate_id'] == $aInfo['bill_info']['estimate_id']
                && ParamsLogic::getInstance()->hitMultiEstimateForPremium()
            ) {
                $aResult = $aConf;
                break;
            }

            if ($aConf['estimate_id'] == $aInfo['bill_info']['estimate_id']
                && \BizCommon\Utils\Horae::isCarpoolFlatRate($aInfo['order_info']['n_tuple'])
            ) {
                $aResult = $aConf;
                break;
            }
        }

        if ($bIsLiteApp && empty($aResult)) {
            foreach ($this->_aOneConfList as $aConf) {
                if ($aConf['business_id'] == $iBusinessId && $aConf['require_level'] == $sCarLevel) {
                    $aResult = $aConf;
                    break;
                }
            }
        }

        return $aResult;
    }
}
