<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse;

use BizCommon\Constants\OrderNTuple;
use BizCommon\Models\Cache\EstimatePrice;
use BizCommon\Models\Order\Order;
use BizCommon\Models\Order\OrderCompanyCarpool;
use BizCommon\Models\Passenger\Passenger;
use BizLib\Config as NuwaConfig;
use BizLib\Constants;
use BizLib\Constants\OrderSystem;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\ErrCode\RespCode;
use BizLib\Exception\ExceptionWithResp;
use BizLib\Libraries\RedisDB;
use BizLib\Log;
use BizLib\Utils\ApolloHelper;
use BizLib\Utils\CarLevel;
use BizLib\Utils\Common;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\Horae;
use BizLib\Utils\Language;
use BizLib\Utils\Product;
use BizLib\Utils\ProductCategory;
use BizLib\Utils\Registry;
use BizLib\Utils\UtilHelper;
use Disf\SPL\Trace;
use Exception;
use Nuwa\ApolloSDK\Apollo;
use Nuwa\ApolloSDK\Apollo as NuwaApollo;
use PreSale\Domain\Service\UIComponent\EstimateSelect;
use PreSale\Logics\athena\DacheAnycarGuide;
use PreSale\Logics\carpool\CarpoolCommuteCard;
use PreSale\Logics\estimatePrice\multiResponse\external\GuidePopUp;
use PreSale\Logics\estimatePriceV2\carpool\DualPrice;
use PreSale\Logics\estimatePriceV2\CompensationLogic;
use PreSale\Logics\estimatePriceV2\DecisionLogic;
use PreSale\Logics\estimatePriceV2\IconConfigLogic;
use PreSale\Logics\estimatePriceV2\KFlowerActivityLogic;
use PreSale\Logics\estimatePriceV2\multiRequest\ProductGroup;
use PreSale\Logics\estimatePriceV2\multiResponse\component;
use PreSale\Logics\estimatePriceV2\multiResponse\component\paymentInfo\NormalPaymentInfoV2;
use PreSale\Logics\estimatePriceV2\multiResponse\linkProduct\SpaciousCarAlliance;
use PreSale\Logics\estimatePriceV2\multiResponse\productAggregation\Handler;
use PreSale\Logics\estimatePriceV2\multiResponse\productAggregation\ShortDistance;
use PreSale\Logics\estimatePriceV2\multiResponse\virtualGroupCar\TaxiPricingSingle;
use PreSale\Logics\estimatePriceV2\PersonalizedCustomServiceLogic;
use PreSale\Logics\estimatePriceV2\response\SceneResponseLogicV2;
use PreSale\Logics\order\AnyCarApolloLogic;
use PreSale\Logics\order\AnyCarOrderLogic;
use PreSale\Logics\taxi\TaxiCarType;
use PreSale\Logics\estimatePriceV2\multiResponse\virtualGroupCar\VirtualGroupCar;
use PreSale\Logics\taxi\TaxiPeakFee;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TaxiPricingBoxLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutBuilder;
use PreSale\Models\carTicket\BizCarTicket;
use Xiaoju\Apollo\Apollo as ApolloV2;
use PreSale\Logics\estimatePriceV2\CarpoolCommuteCardLogic;
use Nebula\Exception\Route\InterRouteException;

/**
 * Class MainRender
 * @package PreSale\Logics\estimatePriceV2\multiResponse
 */
class MainRender
{

    const MULTI_SELECTION_SUPPORT_ON    = 1;
    const MULTI_SELECTION_SUPPORT_OFF   = 0;
    const ESTIMATE_ID_CACHE_EXPIRE_TIME = 6 * 60 * 60;

    /**
     * @var
     */
    protected static $_oInstance;

    /**
     * @var
     */
    protected $_aInfos;

    /**
     * @var
     */
    protected $_aOneConf;

    /**
     * @var
     */
    protected $_aOriginInput;

    /**
     * @var bool 是否是6.0 打车anycar 顶导
     */
    protected $_bIsDacheAnycar;

    /**
     * @var array 6.0构建的产品列表
     */
    protected $_aProductList = [];

    /**
     * @var array 被支付方式过滤的
     */
    protected $_aRemovedByPayment = [];

    /**
     * MainRender constructor.
     * @param array $aInfos       品类信息
     * @param array $aOneConfData oneconf
     * @throws Exception 异常
     */
    private function __construct($aInfos, $aOneConfData) {
        MainDataRepo::init($aInfos);
        $this->_aInfos = $aInfos;
        $this->_setBIsDacheAnycar();
        $this->_aOneConf = $aOneConfData;

    }


    /**
     * @param array $aInfos       品类信息
     * @param array $aOneConfData oneconf
     * @return MainRender
     * @throws Exception 异常
     */
    public static function getInstance($aInfos, $aOneConfData) {
        if (empty(self::$_oInstance)) {
            self::$_oInstance = new self($aInfos, $aOneConfData);
        }

        return self::$_oInstance;
    }

    /**
     * @param array $aProductList $aProductList
     * @return void
     */
    public function setProductList($aProductList) {
        $this->_aProductList = $aProductList;
    }

    /**
     * buildAInfo
     *
     * @return void
     */
    public function buildAInfo() {
        $aEID2Product = [];
        foreach ($this->_aProductList as $oProduct) {
            $aEID2Product[$oProduct->oOrderInfo->sEstimateID] = $oProduct;
        }

        foreach ($this->_aInfos as &$aInfo) {
            $sEID = $aInfo['bill_info']['estimate_id'];
            if (empty($aEID2Product[$sEID])) {
                continue;
            }

            $aInfo['product_object'] = $aEID2Product[$sEID];
        }
    }

    /**
     * @return void
     */
    private function _setBIsDacheAnycar() {
        $aInfo = current($this->_aInfos);
        if (Constants\Common::MENU_DACHE_ANYCAR == $aInfo['order_info']['menu_id']) {
            $this->_bIsDacheAnycar = true;
        } else {
            $this->_bIsDacheAnycar = false;
        }
    }

    /**
     * @return array|bool|mixed
     */
    public function multiExecute() {
        $aMultiResponse = UtilsCommon::getErrMsg(GLOBAL_SUCCESS);
        //渲染前置数据处理和准备工作
        $this->preRender();
        //主渲染逻辑，产生各品类的预估数据
        $aMultiResponse['data']['estimate_data'] = $this->buildEstimateData();
        //渲染品类数据之外的全局数据
        $aMultiResponse = $this->buildExternalData($aMultiResponse);

        $this->_postDeal($aMultiResponse);

        $aMultiResponse = $this->buildVirtualGroupCar($aMultiResponse);

        return $aMultiResponse;
    }

    /**
     *@param array $aMultiResponse $aMultiResponse
     *@return array
     */
    public function buildVirtualGroupCar($aMultiResponse) {
        if ($this->_bIsDacheAnycar) {
            // 构建车型虚拟聚合
            $aMultiResponse = $this->_buildAggregation($aMultiResponse);
        }

        // 构建0.5高峰盒子
        $aMultiResponse = VirtualGroupCar::getInstance()->buildProdcut($aMultiResponse);
        return $aMultiResponse;
    }

    /**
     *@return void
     */
    public function preRender() {
        //过滤未开城的拼车
        $this->_filterCarpoolNotOpen();
        //过滤距离不足的长单拼车
        $this->_filterCarpoolOrder();
        // 过滤支付方式不支持的产品
        $this->_filterByPayment();
        // 若为拼车顶导，校验是否拼车顶到下所有产品均失败
        SceneResponseLogicV2::getInstance()->checkCarpoolEntry();
        // 批量获取拼车预匹配数据（愿等、ETD、ETS等）
        SceneResponseLogicV2::getInstance()->loadInfos($this->_aInfos)->loadCarpoolPreMatchInfo();
        // 获取拼车卡
        CarpoolCommuteCardLogic::getInstance()->loadCommuteCardRelatedByVCard($this->_aInfos);

        // 导流信息, 取非空的第一个 athena_info, 6.0的必为空
        component\GuideInfo::getInstance()->loadInfo($this->_aInfos);

        PersonalizedCustomServiceLogic::getInstance()->formatSsseCustomService($this->_aInfos);

        CompensationLogic::getInstance()->getMultiCompensationAbility($this->_aInfos);

        DacheAnycarGuide::getInstance()->init($this->_aInfos);
        $oIconConfigLogic = IconConfigLogic::getInstance();
        $oIconConfigLogic->loadInfo($this->_aInfos);
        $oIconConfigLogic->loadCarIconConfig();

        TaxiPricingSingle::getInstance()->init(TaxiPricingBoxLayout::buildParams($this->_aInfos[0]));

        foreach ($this->_aInfos as $aInfo) {
            if (Util::isCarpool($aInfo)) {
                $oCompanyCarpool = OrderCompanyCarpool::getInstance();
                $oCompanyCarpool->setParamsWithPaymentTypes($aInfo['payments_info']['user_pay_info']['busi_payments']);
            }
        }
    }


    /**
     * @param array $aMultiResponse 批量返回数据
     * @return mixed
     */
    public function buildExternalData($aMultiResponse) {
        $aMultiResponse['data']['estimate_trace_id'] = Trace::traceId();
        //user_pay_info
        $aMultiResponse = $this->_getUserPayInfo($aMultiResponse);
        //unset [plugin_page_info.dynamic_type] [plugin_page_info.show_red_packet] [plugin_page_info.product_id]
        $aMultiResponse = component\dynamicPrice\Common::filterRedPacketH5($aMultiResponse);
        // 封装Component 真[guide_info] 真[dialog_info]
        //$aMultiResponse = (new EstimateSelect())->buildComponent($this->_aInfos, $aMultiResponse);
        //拼车两口价新形态展示 [link_product] [hidden] [is_default]
        $aMultiResponse['data']['estimate_data'] = DualPrice::getInstance()->linkProduct($aMultiResponse['data']['estimate_data']);
        //车大联盟挂载在快车下展示 [link_product]
        $aMultiResponse['data']['estimate_data'] = (new SpaciousCarAlliance($this->_aInfos[0]))->linkProduct($aMultiResponse['data']['estimate_data']);
        //构建是否支持多选
        $aMultiResponse = $this->_buildMultiSelectionSupport($aMultiResponse);
        //增加品类分类列表category_info_list
        $aMultiResponse = $this->_buildCategoryInfoList($aMultiResponse);
        //增加价格沟通组件多场景和单一场景通用文案[special_rule][promote_sales_rule]
        $aMultiResponse = $this->_buildSpecialRule($aMultiResponse);
        $aMultiResponse = $this->_buildPromoteSalesInfo($aMultiResponse);
        //增加相关H5链接地址
        // 区分新旧
        $aMultiResponse = $this->_buildH5Config($aMultiResponse);
        //增加动调数据 [plugin_page_info]
        $aMultiResponse = (new component\dynamicPrice\DacheAnycarDynamicPrice($this->_aInfos))->build($aMultiResponse);
        //构建用户选中方式与勾选表单不匹配信息[pay_type_disable_msg]
        $aMultiResponse = $this->_buildPayTypeDisableMsg($aMultiResponse);
        //构建品牌信息
        $aMultiResponse = $this->_buildBrandInfo($aMultiResponse);

        return $aMultiResponse;
    }


    /**
     * 处理一些build后的设置缓存等工作
     * @param array $aMultiResponse 批量返回信息
     * @return void
     */
    private function _postDeal($aMultiResponse) {
        //set通勤卡相关缓存
        SceneResponseLogicV2::getInstance()->setCommuteCardCache($this->_aInfos, $aMultiResponse);
    }

    /**
     * array $aMultiResponse 批量返回信息
     * @return array
     */
    public function buildEstimateData() {
        $aComponents = [

            // [intro_msg] [title] [sub_title][sub_title_list] [background_url] [detail_url] [selected_background_url]
            component\introMsg\Handler::class,

            // [driver_metre] [drive_minute] [basic_fee] [count_price_type] [fee_msg] [pre_estimate_fee] [fee_amount]
            // [data_for_extend] [estimate_fee_text] [double_price_available] [fee_desc_bubble] [mixed_fee_msg]
            component\basicFeeMsg\Handler::class,

            // [special_price_text] [special_price_info]
            component\specialPriceTip\Handler::class,

            // [dc_extra_info] [extra_tag] [neworder_notice] [service_protocal] [remind_info] [extend_tag]
            component\extraTag\Handler::class,

            // [combo_id] [price_type] [sky_price] [too_far_info]
            component\priceType\Handler::class,

            // [dynamic_price_info] [plugin_page_info][hit_dynamic_price][hit_show_h5_type]
            component\dynamicPrice\Handler::class,

            // [route_id_list] [route_id] [match_routes_data] [time_span]
            component\route\Handler::class,

            // [estimate_id] [require_level] [business_id] [is_special_price] [scene_type]
            // [combo_type] [product_category] [is_tripcloud] [route_type]
            component\productInfo\Handler::class,

            // [scene_data]
            component\customService\Handler::class,

            // [price_desc] [price_desc_icon] [price_info_desc] [price_desc_tags] [coupon_desc] [discount_desc]
            component\priceDescInfo\Handler::class,

            // [carpool_seat_config] [carpool_seat_module]
            component\carpoolSeat\Handler::class,

            // [user_pay_info] [alert_window] [business_config]
            component\paymentInfo\Handler::class,

            // [promote_sales_text]
            component\promoteSalesTip\Handler::class,

            // [attach_service_icon]
            component\attachService\Handler::class,

            // [station_carpool_booking] [departure_time_text] [departure_time_bubble]
            component\carpoolBooking\Handler::class,

            //[controller_info] [service_dentifier] [category_id] [category_show_msg] [form_show_type] [guide_show_type_tips] [disabled] [disabled_text] [usage_info]
            component\controllerInfo\Handler::class,

            //[prefer_info]
            component\preferInfo\Handler::class,

            //['recommend_tag'] ['selection_tag '] ['short_book_rec']
            component\recommendInfo\Handler::class,

            //['depart_tag']
            component\departTag\Handler::class,

            //['auto_driving_address_info']
            component\autoDrivingAddressInfo\Handler::class,

            //['sub_intro_icon']
            component\subIntroIcon\Handler::class,

            //[recommend_guide]
            component\athenaGuideInfo\Handler::class,
            //[multi_price_desc]
            component\multiPriceDesc\Handler::class,

            // [guide_path]
            component\guidePath\Handler::class,

        ];
        $aEstimateData = [];
        foreach ($this->_aInfos as $aInfo) {
            if (!$this->_checkProductOpne($aInfo)) {
                continue;
            }

            $aResponse = [];
            foreach ($aComponents as $handler) {
                $oComponent = $handler::select($aInfo);
                if (!empty($oComponent)) {
                    $aResponse = $oComponent->build($aResponse);
                }
            }

            //由于error_status值固定为0，且不依赖于aInfo，直接放在这里赋值
            $aResponse['error_status'] = 0;
            $aEstimateData[]           = $aResponse;
        }

        //特价出租车需要交换fee_msg 和 price_desc，类似这些多个component的组合format放在这里
        $oSortSelection = new component\SortSelection($this->_aInfos);

        //设置排序 [sort_index] 6.0需要先设置排序，再做选中；因为选中的兜底逻辑依赖排序
        $aEstimateData = $oSortSelection->sortEstimateData($aEstimateData, $this->_bIsDacheAnycar, $this->_aInfos);

        //设置默认选中
        $aEstimateData = $oSortSelection->setDefaultSelection($aEstimateData, $this->_bIsDacheAnycar);

        //设置estimate id缓存
        $this->_setEstimateIdCache($aEstimateData);
        return $aEstimateData;
    }

    /**
     * @param array $aEstimateData 参数
     * @return mixed
     */
    // 2024.9.10注释-待删
//    private function _checkAnycarSPFlashFilter($aEstimateData) {
//        $aDecisionOrder           = DecisionLogic::getInstance()->getDecisionProductOrder();
//        $bAnycarSpflashRemoveFlag = false;
//        foreach ($aDecisionOrder as $aOneDecision) {
//            if (Product::isAnyCar(Product::getProductIdByBusinessId($aOneDecision['business_id'])) && isset($aOneDecision['anycar_spflash_remove_flag'])) {
//                $bAnycarSpflashRemoveFlag = $aOneDecision['anycar_spflash_remove_flag'];
//            }
//        }
//
//        if (!$bAnycarSpflashRemoveFlag) {
//            return $aEstimateData;
//        }
//
//        foreach ($aEstimateData as &$aOneData) {
//            if (Product::isAnyCar(Product::getProductIdByBusinessId($aOneData['business_id']))) {
//                foreach ($aOneData['preference_product_list'] as $iIndex => $aSubProduct) {
//                    if (OrderNTuple::COMMON_PRODUCT_ID_FAST_CAR == $aSubProduct['business_id']
//                        && OrderNTuple::DIDI_PUTONG_CAR_LEVEL == $aSubProduct['require_level']
//                        && \BizCommon\Utils\Order::isSpecialRateV2($aSubProduct)
//                        && OrderNTuple::LEVEL_TYPE_DEFAULT == $aSubProduct['level_type']
//                    ) {
//                        unset($aOneData['preference_product_list'][$iIndex]);
//                        break;
//                    }
//                }
//
//                $aOneData['preference_product_list'] = array_values($aOneData['preference_product_list']);
//            }
//        }
//
//        return $aEstimateData;
//    }


    /**
     * @Desc:
     * @param array $aEstimateData Array structure to count the elements of.
     * @throws ExceptionWithResp $aEstimateData .
     * @Author:<EMAIL>
     * @return void
     */
    // 2024.9.10注释-待删
//    private function _checkLowPriceCarpool($aEstimateData) {
//        //拼车新产品入口预估数据为空，返回错误
//        if (!empty($aEstimateData)) {
//            return;
//        }
//
//        if (!\BizCommon\Utils\Horae::isLowPriceCarpoolEntry(MainDataRepo::getMenuID())) {
//            return;
//        }
//
//        if (version_compare(MainDataRepo::getAppVersion(), '6.2.16') >= 0) {
//            throw new InterRouteException();
//        }
//
//        $aErrMsg = json_decode(Language::getTextFromDcmp('config_carpool-low_price_carpool_not_open_response',['-']), true);
//        $aReturn = UtilsCommon::pairErrNo(RespCode::P_ESTIMATE_FAIL, $aErrMsg);
//        throw new ExceptionWithResp(
//            Code::E_COMMON_AREA_NOT_OPEN_SERVICE,
//            RespCode::P_ESTIMATE_FAIL,
//            $aReturn['errmsg']
//        );
//
//    }

    /**
     * 校验产品是否开城(拼车、anycar)
     * @param array $aInfo aInfo
     * @return bool
     */
    private function _checkProductOpne($aInfo) {

        if (UtilHelper::isPbdChannel($aInfo['order_info']['channel'])) {
            return true;
        }

        if (Util::isCarpool($aInfo)) {
            return $this->_checkCarpoolOpen($aInfo);
        }

        // 车大费用项 没获取到值不进行渲染
        if (Util::isSpaciousCarAlliance($aInfo)) {
            $iRequireLevel = $aInfo['order_info']['require_level'];
            $fFee          = $aInfo['bill_info']['bills'][$iRequireLevel]['fee_detail_info']['talos_spacious_car_selection_fee'];
            if (!isset($fFee)) {
                return false;
            }
        }

        return true;
    }

    /**
     * @param array $aInfo 品类信息
     * @return bool
     */
    private function _checkCarpoolOpen($aInfo) {
        if (Horae::isCarpool($aInfo['order_info']['combo_type'], $aInfo['order_info']['require_level'])
            && empty($aInfo['bill_info']['is_carpool_open'])
        ) {
            return false;
        }

        return true;
    }

    /**
     * @param array $aEstimateData 预估数据
     * @return mixed
     */
    // 2024.9.10注释-待删
//    private function _estimateDataPostFormat($aEstimateData) {
//        $aIndexedInfo = [];
//        foreach ($this->_aInfos as $aInfo) {
//            $iEstimateId = $aInfo['bill_info']['estimate_id'];
//            $aIndexedInfo[$iEstimateId] = $aInfo;
//        }
//
//        foreach ($aEstimateData as &$aData) {
//            // fee_msg 和 price_desc互换
//            if (!$this->_bIsDacheAnycar && ProductCategory::PRODUCT_CATEGORY_UNIONE_SPECIAL_PRICE == $aData['product_category']) {
//                $aPriceDesc    = explode(',',  $aData['price_desc']);
//                $aPriceDescLen = count($aPriceDesc);
//                if ($aPriceDescLen >= 1) {
//                    $aData['fee_msg'] = $aPriceDesc[$aPriceDescLen - 1];
//                    unset($aPriceDesc[$aPriceDescLen - 1]);
//                    $aData['price_desc'] = implode(',',  $aPriceDesc);
//                    if (isset($aData['price_info_desc'])) {
//                        unset($aData['price_info_desc'][$aPriceDescLen - 1]['content']);
//                    }
//                }
//            }
//        }
//
//        return $aEstimateData;
//    }


    /**
     * @param array $aMultiResponse 批量返回信息
     * @return mixed
     */
    // 2024.9.10注释-待删
//    private function _getCarpoolRequireTraceId($aMultiResponse) {
//        foreach ($this->_aInfos as $aInfo) {
//            if ($aInfo['order_info']['carpool_require_trace_id']) {   //站点拼车切换站点时返回第一次预估时的trace_id
//                $aMultiResponse['data']['carpool_require_trace_id'] = $aInfo['order_info']['carpool_require_trace_id'];
//            }
//        }
//
//        return $aMultiResponse;
//    }


    /**
     * @param array $aMultiResponse 批量返回信息
     * @return mixed
     */
    // 2024.9.10注释-待删
//    private function _getShakeFlag($aMultiResponse) {
//        foreach ($this->_aInfos as $aInfo) {
//            $sShackFlag = $aInfo['price_extra']['shake_flag'];
//            if ('1' == $sShackFlag) {
//                $aMultiResponse['data']['shake_flag'] = $sShackFlag;
//            }
//        }
//
//        return $aMultiResponse;
//    }


    /**
     * @param array $aMultiResponse 批量返回信息
     * @return mixed
     */
    // 2024.9.10注释-待删
//    private function _getBubbleCardSkin($aMultiResponse) {
//        foreach ($this->_aInfos as $aInfo) {
//            $aConf = KFlowerActivityLogic::getInstance()->getConf($aInfo['order_info']['product_id']);
//            if (!empty($aConf['skin'])) {
//                $aMultiResponse['data']['skin_url'] = $aConf['skin'];
//
//                break;
//            }
//        }
//
//        return $aMultiResponse;
//    }


    /**
     * @param array $aMultiResponse 批量返回信息
     * @return mixed
     */
    private function _getUserPayInfo($aMultiResponse) {
        return (new NormalPaymentInfoV2($this->_aInfos))->buildExternalUserPayInfo($aMultiResponse);
    }

    /**
     * @param array $aMultiResponse 批量返回信息
     * @return mixed
     */
    // 2024.9.10注释-待删
//    private function _getSpecialRateAppointmentEnterSwitch($aMultiResponse) {
//        foreach ($this->_aInfos as $aInfo) {
//            $sCarLevel     = $aInfo['order_info']['require_info'];
//            $iComboType    = $aInfo['bill_info']['product_infos'][$sCarLevel]['combo_type'] ?? $aInfo['order_info']['combo_type'];
//            $iSpecialPrice = $aInfo['order_info']['n_tuple']['is_special_price'];
//
//            $aSpecialOrder = ['product_id' => $aInfo['order_info']['product_id'], 'require_level' => $sCarLevel, 'combo_type' => $iComboType];
//            if (\BizCommon\Utils\Order::isSpecialRateV2($aSpecialOrder) || $iSpecialPrice) {
//                $bCapacity = $aInfo['order_info']['recognition_extra_info']['appointment_capacity'];
//                $aMultiResponse['data']['special_rate_appointment_enter_switch'] = !empty($bCapacity) ? $bCapacity : 0;
//                break;
//            }
//        }
//
//        return $aMultiResponse;
//    }


    //通用消息盒子结构
    //原本为平峰期功能做的pushInfo，由于一直没有开量，所以暂时没有迁移这个功能，push_info固定返回false
    /**
     * @return null
     */
    // 2024.9.10注释-待删
//    private function _getPushInfo() {
//        return null;
//    }


    /**
     * 过滤掉anycar顶导下的城际拼车
     *
     * @return void
     */
    // 2024.9.10注释-待删
//    private function _filterAnycarIntercityCarpool() {
//        if ($this->_bIsDacheAnycar) {
//            return;
//        }
//
//        foreach ($this->_aInfos as $iIndex => $aInfo) {
//            if (Horae::isInterCityCarPoolScene($aInfo['order_info']['combo_type']) && Constants\Common::MENU_ANYCAR == $aInfo['order_info']['menu_id']) {
//                unset($this->_aInfos[$iIndex]);
//            }
//        }
//
//        $this->_aInfos = array_values($this->_aInfos);
//    }


    /**
     * 根据起终点距离，过滤距离不足的长单拼车 以及 过滤距离过长的拼车日折扣的拼车单 和 拼成乐单

     * @return void
     */
    private function _filterCarpoolOrder() {
        $oApolloV2 = new ApolloV2();
        foreach ($this->_aInfos as $iIndex => $aInfo) {
            //长单拼车预约，若距离不足，不出长单拼车
            if ($aInfo['order_info']['n_tuple']['carpool_long_order']
                && !$oApolloV2->featureToggle(
                    'gs_long_carpool_limit',
                    array(
                        'phone'        => (string)($aInfo['passenger_info']['phone']),
                        'driver_metre' => $aInfo['bill_info']['driver_metre'],
                    )
                )->allow()
            ) {
                unset($this->_aInfos[$iIndex]);
            }
        }

        // 下面这行代码应该是对_aInfos数组进行重新编号，因为上面可能会过滤掉了数组中的部分元素，数组下表变得不再连续
        $this->_aInfos = array_values($this->_aInfos);
    }

    /**
     * 过滤拼车未开城对应的aInfo
     * @return void
     */
    private function _filterCarpoolNotOpen() {
        if ($this->_bIsDacheAnycar) {
            return;
        }

        foreach ($this->_aInfos as $iIndex => $aInfo) {
            if (Util::isCarpool($aInfo) && empty($aInfo['bill_info']['is_carpool_open'])) {
                unset($this->_aInfos[$iIndex]);
            }
        }
    }

    /**
     * @return void
     */
    private function _filterByPayment() {
        $aInfo = current($this->_aInfos);
        $sUserSelectPaymentType = $aInfo['order_info']['payments_type']; // 乘客自己选择的支付方式

        // 个人支付都支持，不需要判断车型是否支持
        if (empty($sUserSelectPaymentType) || (int)$sUserSelectPaymentType <= 0 || Order::BUSINESS_PAY_BY_PERSON_NEW == (int)$sUserSelectPaymentType) {
            return;
        }

        foreach ($this->_aInfos as $iIndex => $aInfo) {
            if (ProductCategory::PRODUCT_CATEGORY_HK_CAP_TAXI != $aInfo['order_info']['product_category']) {
                // 2022-06-20 仅香港超值的士需要被支付方式过滤
                continue;
            }

            $aPayInfoList = $aInfo['payments_info']['user_pay_info']['busi_payments'] ?: null;
            if (empty($aPayInfoList)) {
                // 收银没有返回,
                // 都不展示
                unset($this->_aInfos[$iIndex]);
            }

            foreach ($aPayInfoList as $aPayInfo) {
                if (1 == $aPayInfo['isSelected']) {
                    // 系统选中的和乘客选中的不同, 那就是不支持
                    if ($sUserSelectPaymentType != $aPayInfo['tag']) {
                        $this->_aRemovedByPayment[] = $aInfo;
                        unset($this->_aInfos[$iIndex]);
                    }

                    break;
                }
            }
        }
    }

    /**
     * 处理anycar未开城品类
     * @return void
     */
    // 2024.9.10注释-待删
//    private function _mergeAnyCarDisable() {
//        if ($this->_bIsDacheAnycar) {
//            return;
//        }
//
//        if (empty($this->_aOneConf)) {
//            return;
//        }
//
//        // get multi require product from one conf
//        $aMultiRequireProduct = [];
//        foreach ($this->_aOneConf as $aOneConf) {
//            if (!empty($aOneConf['multi_require_product'])) {
//                $aMultiRequireProduct = $aOneConf['multi_require_product'];
//            }
//        }
//
//        if (empty($aMultiRequireProduct)) {
//            return;
//        }
//
//        foreach ($this->_aInfos as $iIndex => $aInfo) {
//            if (!empty($aInfo['bill_info']['multi_info'])) {
//                foreach ($aMultiRequireProduct as $item) {
//                    if (true == $item['remove_flag']) {
//                        $bExist = false;
//                        foreach ($aInfo['bill_info']['multi_info'] as $key => $value) {
//                            if ($item['product_id'] == $value['product_id']
//                                && $item['combo_type'] == $value['combo_type']
//                                && $item['require_level'] == $value['require_level']
//                                && true == $item['remove_flag']
//                            ) {
//                                $this->_aInfos[$iIndex]['bill_info']['multi_info'][$key]['disabled'] = 1;
//                                $bExist = true;
//                            }
//                        }
//
//                        if (false == $bExist) {
//                            $item['disabled'] = 1;
//                            $this->_aInfos[$iIndex]['bill_info']['multi_info'][] = $item;
//                        }
//                    }
//                }
//            }
//        }
//
//    }

    /**
     * @param array $aEstimateData 预估数据
     * 缓存 estimate id
     * @return void
     */
    private function _setEstimateIdCache($aEstimateData) {
        foreach ($this->_aInfos as $aInfo) {
            $iPid = $aInfo['passenger_info']['pid'];
            break;
        }

        foreach ($aEstimateData as $item) {
            if (1 == $item['is_default']) {
                $sEstimateID = $item['estimate_id'];
                break;
            }
        }

        foreach ($aEstimateData as $item2) {
            if (OrderSystem::TYPE_COMBO_CARPOOL == $item2['combo_type']
                && Product::COMMON_PRODUCT_ID_FAST_CAR == $item2['business_id']
                && CarLevel::DIDI_PUTONG_CAR_LEVEL == $item2['require_level']
            ) {
                $sCarpoolEstimateID = $item2['estimate_id'];
                break;
            }
        }

        $oApollo = new Apollo();
        if (!$oApollo->featureToggle('redis_remove_2', ['key' => rand(0, 1000000), 'redis_key' => 'P_ESTIMATE_SELECTED_ID'])->allow()) {
            if (!empty($iPid) && !empty($sEstimateID)) {
                if (!Registry::has(SET_ESTIMATE_ID_REGISTRY_KEY)) {
                    EstimatePrice::getInstance()->setSelectedEstimateId($sEstimateID, $iPid);
                    EstimatePrice::getInstance()->setEstimateId($sEstimateID, $iPid);
                    Registry::set(SET_ESTIMATE_ID_REGISTRY_KEY, 1);
                }
            }
        }

        if (!$oApollo->featureToggle('redis_remove_2',['key' => rand(0, 1000000), 'redis_key' => 'P_ESTIMATE_CARPOOL_ID'])->allow()) {
            if (!empty($sCarpoolEstimateID) && !empty($iPid) && !Registry::has(SET_CARPOOL_ESTIMATE_ID_REGISTRY_KEY)) {
                EstimatePrice::getInstance()->setCarpoolEstimateId($sCarpoolEstimateID, $iPid);
                Registry::set(SET_CARPOOL_ESTIMATE_ID_REGISTRY_KEY, 1);
            }
        }

        //缓存多预估中各车型关联拼车的预估id
        $this->_setCarpoolCommendEstimateId();

    }

    /**
     * @desc 缓存多预估中各车型关联拼车的预估id
     * @return void
     */
    private function _setCarpoolCommendEstimateId() {
        $sCarpoolEstimateID = MainDataRepo::getCarpoolDualPriceEstimateID();
        $sFastCarEstimateID = MainDataRepo::getFastCarEstimateID();

        if (!empty($sCarpoolEstimateID) && !empty($sFastCarEstimateID)) {
            $sPrefix = Common::getRedisPrefix(P_ESTIMATE_ID_JOIN_CARPOOL);
            $sKey    = $sPrefix.$sFastCarEstimateID;
            //  redis前缀值: P_ESTIMATE_ID_JOIN_CARPOOL_
            //  存储位置: (无显式声明, 默认)forever
            //  使用位置: 仅一处
            //      1. 等待接驾阶段, 乘客选择快车转拼车, 调用 dExchangeOrder 时读取
            //  快转拼相关wiki: http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=351386068
            $bRet = RedisDB::getInstance()->setex($sKey, self::ESTIMATE_ID_CACHE_EXPIRE_TIME, $sCarpoolEstimateID);
            if (!$bRet) {
                Log::warning(
                    Msg::formatArray(
                        Code::E_COMMON_REDIS_SET_FAIL,
                        array('key' => $sKey, 'value' => $sCarpoolEstimateID)
                    )
                );
            }
        }

    }

    /**
     * @param array $iPid          乘客id
     * @param array $aEstimateData 预估数据
     * @return void
     */
    // 2024.9.10注释-待删
//    private function _setBubbled($iPid, array $aEstimateData) {
//        foreach ($aEstimateData['data']['estimate_data'] as $idx1 => $item1) {
//            if (1 == $item1['is_default']
//                && ((\BizLib\Constants\Horae::HORAE_SCENE_TYPE_DEFAULT == $item1['scene_type'] && CarLevel::DIDI_ANY_CAR_CAR_LEVEL == $item1['require_level'])
//                || CarLevel::DIDI_XIAOBA_CAR_LEVEL == $item1['require_level'])
//            ) {
//                $iProductID = Product::getProductIdByBusinessId($item1['business_id']);
//                Passenger::getInstance()->setBubbleTag($iPid, $iProductID, 1);
//            }
//        }
//    }


    /**
     * @param array $aMultiResponse aMultiResponse
     * @return mixed
     */
    private function _buildCategoryInfoList($aMultiResponse) {
        $aCategoryInfo     = [];
        $aCategoryIDUnique = [];
        $aCategoryConf     = MainDataRepo::getCategoryConfig();
        foreach ($this->_aInfos as $aInfo) {
            $iProductCategory = $aInfo['order_info']['product_category'];
            $aConf            = MainDataRepo::getBasicConfByProductCategory($iProductCategory);
            if (!empty($aConf) && isset($aCategoryConf[$aConf['category_id']])
                && !in_array($aConf['category_id'],$aCategoryIDUnique)
            ) {
                $aCurrentCategory    = $aCategoryConf[$aConf['category_id']];
                $aCategoryInfo[]     = array(
                    'category_id'     => $aCurrentCategory['category_id'],
                    'name'            => $aCurrentCategory['category_name'],
                    'underline_color' => $aCurrentCategory['underline_color'],
                );
                $aCategoryIDUnique[] = $aCurrentCategory['category_id'];
            }
        }

        if (count($aCategoryInfo) > 1) {
            $aCategoryInfo = self::sortArray($aCategoryInfo,'category_id');
        }

        $aMultiResponse['data']['category_info_list'] = $aCategoryInfo;
        return $aMultiResponse;
    }

    /**
     * @param array $aMultiResponse $aMultiResponse
     * @return array
     */
    private function _buildMultiSelectionSupport($aMultiResponse) {
        //默认为1
        $bSupport = self::MULTI_SELECTION_SUPPORT_ON;
        $aInfo    = current($this->_aInfos);
        $oApollo  = new Apollo();
        // 是否支持多选，使用Apollo灰度开关控制，不从配置中拿了，使用灰度开发控制好处是之后如果要升级支持多选，可作为一个放量开关
        if ($oApollo->featureToggle(
            'gs_not_support_multi_selection_switch',
            array(
                'key'           => $aInfo['passenger_info']['pid'],
                'phone'         => $aInfo['passenger_info']['phone'],
                'city'          => $aInfo['order_info']['area'],
                'page_type'     => $aInfo['order_info']['page_type'],
                'order_type'    => $aInfo['order_info']['order_type'],
                'call_car_type' => $aInfo['order_info']['call_car_type'],
                'app_version'   => $aInfo['common_info']['app_version'],
            )
        )->allow()
        ) {
            $bSupport = self::MULTI_SELECTION_SUPPORT_OFF;
            // 不支持多选的情况，需要检查select_type，保证只有一个select_type=1
            $aMultiResponse = $this->_checkResponseSelectType($aMultiResponse);
        }

        $aMultiResponse['data']['is_support_multi_selection'] = $bSupport;
        return $aMultiResponse;
    }

    /**
     * @param array $aMultiResponse $aMultiResponse
     * @return mixed
     */
    private function _checkResponseSelectType($aMultiResponse) {
        $aEstimateData = $aMultiResponse['data']['estimate_data'];
        $bHasSelect    = false;
        foreach ($aEstimateData as $index => $aData) {
            if ($bHasSelect && component\SortSelection::DACHE_ANYCAR_ITEM_SELECTED == $aData['select_type']) {
                $aMultiResponse['data']['estimate_data'][$index]['select_type'] = component\SortSelection::DACHE_ANYCAR_ITEM_NOT_SELECTED;
                continue;
            }

            if (component\SortSelection::DACHE_ANYCAR_ITEM_SELECTED == $aData['select_type']) {
                $bHasSelect = true;
            }
        }

        return $aMultiResponse;
    }


    /**
     * @param array $aMultiResponse aMultiResponse
     * @return mixed
     */
    private function _buildPromoteSalesInfo($aMultiResponse) {
        $aInfo         = current($this->_aInfos);
        $oApollo       = new NuwaApollo();
        $oApolloResult = $oApollo->featureToggle(
            'bubble_communicate_component',
            [
                'key'              => $aInfo['passenger_info']['phone'],
                'phone'            => $aInfo['passenger_info']['phone'],
                'city'             => $aInfo['order_info']['area'],
                'product_id'       => $aInfo['order_info']['product_id'],
                'car_level'        => $aInfo['order_info']['require_level'],
                'lang'             => $aInfo['common_info']['lang'],
                'app_version'      => $aInfo['common_info']['app_version'],
                'access_key_id'    => $aInfo['common_info']['access_key_id'],
                'config'           => 0,
                'combo_type'       => $aInfo['order_info']['combo_type'],
                'menu_id'          => $aInfo['order_info']['menu_id'],
                'product_category' => $aInfo['order_info']['product_category'],
                'page_type'        => $aInfo['order_info']['page_type'],
            ]
        );
        $bAllow        = $oApolloResult->allow();
        if (!$bAllow) {
            $oBizCarTicket = new BizCarTicket();
            $aCarTicketTip = $oBizCarTicket->getPromoteSalesTip($aInfo['passenger_info']['token'],$aInfo['order_info']['to_lat'],$aInfo['order_info']['to_lng']);

            $aSpecialPrice = [
                'multi_scene_text'  => '',//multi_scene_text预留字段，防止后续多个文案冲突
                'single_scene_text' => $aCarTicketTip,
                'link_url'          => $oBizCarTicket->getPromoteSalesUrl(),
            ];

            $aMultiResponse['data']['promote_sales_rule'] = $aSpecialPrice;
        }

        return $aMultiResponse;
    }

    /**
     * @param array $aMultiResponse aMultiResponse
     * @return mixed
     */
    private function _buildSpecialRule($aMultiResponse) {
        $aSpecialPrice = [
            'multi_scene_text'  => Language::getTextFromDcmp('special_price_rule_explain-multi_scene_common_text'),
            'single_scene_text' => Language::getDecodedTextFromDcmp('special_price_rule_explain-single_scene_common_text'),
        ];
        $aMultiResponse['data']['special_rule'] = $aSpecialPrice;
        return $aMultiResponse;
    }


    /**
     * @param  array $aMultiResponse aMultiResponse
     * @return mixed
     */
    private function _buildH5Config($aMultiResponse) {
        $aMultiResponse['data']['fee_detail_url'] = UtilHelper::getConfigUrl('fee_detail_h5_new');
        return $aMultiResponse;
    }


    /**
     * @param array  $aInput 待排数组
     * @param string $sField 排序字段
     * @param string $sType  排序规则
     * @return array
     */
    public static function sortArray(array $aInput, string $sField, $sType = 'SORT_ASC') {
        $aTemp = [];
        foreach ($aInput as $idx => $row) {
            foreach ($row as $key => $value) {
                $aTemp[$key][$idx] = $value;
            }
        }

        array_multisort($aTemp[$sField],constant($sType),$aInput);
        return $aInput;
    }

    /**
     * 构建品牌信息
     * @param array $aMultiResponse 响应
     * @return array
     */
    private function _buildBrandInfo(array $aMultiResponse) {
        $aInfo = current($this->_aInfos);

        //香港出租车特殊逻辑,非场景页,无法复用场景page_type识别逻辑,可用对端协议
        if (357 == $aInfo['order_info']['area'] && \BizLib\Constants\Horae::PAGE_TYPE_YUEGANG != $aInfo['common_info']['page_type']) {
            $aHkBrandInfo = NuwaConfig::text('estimate_brand_info', 'hk_taxi') ?? [];
            if (empty($aHkBrandInfo) && empty($aHkBrandInfo['brand_info'])) {
                return $aMultiResponse;
            }

            $aMultiResponse['data']['brand_info'] = [
                'title'       => $aHkBrandInfo['brand_info']['title'] ?? '',
                'sub_title'   => $aHkBrandInfo['brand_info']['sub_title'] ?? '',
                'font_color'  => $aHkBrandInfo['brand_info']['font_color'] ?? '',
                'bg_img'      => $aHkBrandInfo['brand_info']['bg_img'] ?? '',
                'start_color' => $aHkBrandInfo['brand_info']['start_color'] ?? '',
                'end_color'   => $aHkBrandInfo['brand_info']['end_color'] ?? '',
            ];
            return $aMultiResponse;
        }

        //获取品牌配置
        $aBrandInfo = ApolloHelper::getConfigContent('estimate_brand_info','brand_info_multilingual_'.$aInfo['common_info']['lang']);
        if (empty($aBrandInfo) || empty($aBrandInfo['brand_conf'])) {
            return $aMultiResponse;
        }

        $aBrandConf = [];
        foreach ($aBrandInfo['brand_conf'] as $conf) {
            if ($aInfo['common_info']['page_type'] != $conf['page_type']) {
                continue;
            }

            $aBrandConf['title']       = $conf['title'];
            $aBrandConf['sub_title']   = $conf['sub_title'];
            $aBrandConf['bg_img']      = $conf['bg_img'];
            $aBrandConf['start_color'] = $conf['start_color'];
            $aBrandConf['end_color']   = $conf['end_color'];
            $aBrandConf['font_color']  = $conf['font_color'];
            break;
        }

        if (!empty($aBrandConf)) {
            $aMultiResponse['data']['brand_info'] = $aBrandConf;
        }

        return $aMultiResponse;
    }

    /**
     * 用户切换支付方式与当前勾选表单的支付方式不一致时，提醒用户
     * @param array $aMultiResponse 响应
     * @return array
     */
    private function _buildPayTypeDisableMsg(array $aMultiResponse) {
        $aInfo        = current($this->_aInfos);
        $sPaymentType = $aInfo['order_info']['payments_type'];

        // 个人支付都支持，不需要判断车型是否支持
        if (empty($sPaymentType) || (int)$sPaymentType <= 0 || Order::BUSINESS_PAY_BY_PERSON_NEW == (int)$sPaymentType) {
            return $aMultiResponse;
        }

        // 获取选中列表中不支持的车型信息
        $bMultiCarLevelNotSupport = false;
        $aNotSupportCarLevelMsg   = [];
        foreach ($aMultiResponse['data']['estimate_data'] as $aEstimateData) {
            if (component\SortSelection::DACHE_ANYCAR_ITEM_SELECTED == $aEstimateData['select_type']
                && $sPaymentType != $aEstimateData['user_pay_info']['payment_id']
            ) {
                $aNotSupportCarLevelMsg[] = $aEstimateData['intro_msg'];
                if (count($aNotSupportCarLevelMsg) >= 2) {
                    $bMultiCarLevelNotSupport = true;
                    break;
                }
            }
        }

        if (!empty($this->_aRemovedByPayment)) {
            foreach ($this->_aRemovedByPayment as $aProductInfo) {
                $oHandler = component\introMsg\Handler::select($aProductInfo);
                $aRet     = $oHandler->build([]);
                $aNotSupportCarLevelMsg[] = $aRet['intro_msg'];
            }
        }

        if (empty($aNotSupportCarLevelMsg)) {
            return $aMultiResponse;
        }

        // 获取支付方式文案
        $sPaymentTypeText = ''; // 用户选择的支付方式的名称
        foreach ($aMultiResponse['data']['user_pay_info']['payment_list'] as $aUserPayInfo) {
            if ($sPaymentType == $aUserPayInfo['tag']) {
                $sPaymentTypeText = $aUserPayInfo['msg'];
                break;
            }
        }

        $aText       = NuwaConfig::text(
            'estimate_dache_anycar',
            'pay_type_not_support',
            [
                'car_level_msg' => implode('、', $aNotSupportCarLevelMsg),
                'pay_type_msg'  => $sPaymentTypeText,
            ]
        );
        $sDisableMsg = $aText['enum_car_level_msg'];
        if ($bMultiCarLevelNotSupport) {
            $sDisableMsg = $aText['show_part_car_level_msg'];
        }

        // 所选的支付方式不在支付列表全集中，取默认文案
        if (empty($sPaymentTypeText)) {
            $sDisableMsg = $aText['default_msg'];
        }

        $aMultiResponse['data']['pay_type_disable_msg'] = $sDisableMsg;

        return $aMultiResponse;
    }

    /** 构建虚拟车型
     * @param array $aMultiResponse 返回值
     * @return mixed
     */
    private function _buildAggregation($aMultiResponse) {
        $aSubGroups    = [];
        $aEstimateData = $aMultiResponse['data']['estimate_data'];
        // 下面foreach逻辑的作用是判断所有品类有没有需要聚合的，如果有则最后$aSubGroups数组不为空
        foreach ($aEstimateData as $aData) {
            $iGroupId = ProductGroup::getInstance()->getProductSubGroupID($aData['product_category']);
            if ($iGroupId > 0) {
                $aSubGroups[$iGroupId] = 1;
            }
        }

        //新版特惠小程序全部融合，和主端开城配置无法兼容
        if (MainDataRepo::isSpecialRateNeedAggregation()) {
            $aSubGroups[ShortDistance::SUB_GROUP_ID] = 1;
        }

        if (TaxiPricingSingle::getInstance()->IsPricingBox()) {
            $aSubGroups[LayoutBuilder::SUB_GROUP_ID_TAXI_PRICING] = 1;
        }

        // 下面foreach逻辑的作用是对每一种需要聚合的车型进行聚合
        foreach ($aSubGroups as $iGroupID => $flag) {
            $oHandler = Handler::getAggregationHandler($iGroupID, $this->_aInfos);
            if ($oHandler != null && method_exists($oHandler, 'buildAggregation')) {
                $aEstimateData = $oHandler->buildAggregation($aEstimateData);
            }
        }

        $aMultiResponse['data']['estimate_data'] = $aEstimateData;

        return $aMultiResponse;
    }
}
