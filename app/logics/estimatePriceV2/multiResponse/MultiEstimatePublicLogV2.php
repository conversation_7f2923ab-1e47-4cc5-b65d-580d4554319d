<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse;

use BizCommon\Utils\Horae;
use BizCom<PERSON>\Utils\Order;
use BizLib\Utils\Language;
use BizLib\Utils\PublicLog;
use Dirpc\SDK\PreSale\EstimateData;
use Dirpc\SDK\PreSale\MultiEstimatePriceRequest;
use Disf\SPL\Trace;
use PreSale\Logics\carpool\InterCity;
use PreSale\Logics\estimatePriceV2\bill\CommonBillLogic;
use PreSale\Logics\estimatePriceV2\DecisionLogic;
use PreSale\Logics\estimatePriceV2\multiRequest\Product;
use PreSale\Logics\estimatePriceV2\multiResponse\component\customService\Common;
use PreSale\Logics\estimatePriceV2\PersonalizedCustomServiceLogic;
use PreSale\Logics\scene\custom\CustomLogic;
use BizLib\Utils\ProductCategory;
use PreSale\Logics\taxi\TaxiPeakFee;
use TripcloudCommon\Utils\Product as TripcloudProduct;
use BizLib\Constants\OrderSystem;

/**
 * 多预估public日志组建
 * Class MultiEstimatePublicLog.
 */
class MultiEstimatePublicLogV2
{
    const PRICE_DESC_NO_REWARDS      = 0;
    const PRICE_DESC_ONLY_REWARDS    = 1;
    const PRICE_DESC_INCLUDE_REWARDS = 2;
    /**
     * @var array
     */
    private $_aInfos;

    /**
     * @var array
     */
    private $_aResponseInfo;

    /**
     * @var MultiEstimatePriceRequest
     */
    private $_oRequest;

    /**
     * @var array
     */
    private $_aProductList;
    /**
     * @var array
     */
    private $_aBillsForTcFeeMonitor = [];

    /**
     * MultiEstimatePublicLog constructor.
     *
     * @param MultiEstimatePriceRequest $oRequest      预估入参
     * @param array                     $aInfos        $aInfos
     * @param array                     $aResponseInfo $aResponseInfo
     * @param array                     $aProductList  $aProductList
     */
    public function __construct($oRequest, $aInfos, $aResponseInfo, $aProductList) {
        $this->_oRequest      = $oRequest;
        $this->_aInfos        = $aInfos;
        $this->_aResponseInfo = $aResponseInfo;
        $this->_aProductList  = $aProductList;
    }

    /**
     * 写多预估流程的public 日志.
     */
    public function multiWritePublicLog() {
        //写表单展示日志
        $this->_writeOrderEstimateInfo();
        //写预估public日志
        $this->_writeOrderEstimatePrice();
        // 出租车业务线写public日志
        $this->_writeTaxiEstimatePrice();

    }

    /**
     * 此前依据老的aInfo记录日志容易出错，新增一份依据新的aInfo记录的日志
     * 且仅记录预估阶段最终会展示的产品线
     */
    private function _writeOrderEstimateInfo() {
        $aIndexedInfo = [];
        foreach ($this->_aInfos as $aInfo) {
            $iEstimateId = $aInfo['bill_info']['estimate_id'];
            $aIndexedInfo[$iEstimateId] = $aInfo;
        }

        foreach ($this->_aResponseInfo['data']['estimate_data'] as $aResponse) {
            $iEstimateId      = $aResponse['estimate_id'];
            $aInfo            = $aIndexedInfo[$iEstimateId];
            $sCarLevel        = $aInfo['order_info']['require_level'];
            $aBillProductInfo = $aInfo['bill_info']['product_infos'][$sCarLevel];
            $aEstimateStatistic = [
                'opera_stat_key'    => 'g_order_estimate_info',
                'pLang'             => $this->_oRequest->getLang() ?? Language::ZH_CN,
                'estimate_id'       => $iEstimateId,
                'area'              => $aInfo['order_info']['area'],
                'product_id'        => $aInfo['order_info']['product_id'],
                'require_level'     => $sCarLevel,
                'origin_combo_type' => $aInfo['order_info']['combo_type'],
                'combo_type'        => $aBillProductInfo['combo_type'], //因为账单可能修改combo_type，此处特别记录一下请求中的combo_type
                'is_default'        => $aResponse['is_default'],
                'select_type'       => $aResponse['select_type'],
                'recommend_type'    => $aResponse['recommend_type'],
                'sub_group_id'      => $aResponse['sub_group_id'],
                //优惠信息
                'has_operation'     => is_object($aResponse['operation']) ? (empty((array) $aResponse['operation']) ? 0 : 1) : (empty($aResponse['operation']) ? 0 : 1), //记录是否有回巢券
            ];

            if (!empty($aResponse['price_info_desc'][0]['content'])) {
                // price_info_desc 第0项, 如果存在春节服务费,一定在第0项
                $aEstimateStatistic['price_info_desc_0_content'] = $aResponse['price_info_desc'][0]['content'];
            }

            PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);
        }
    }

    private function _writeOrderEstimatePrice() {
        // 获取三方价格监控需要的费用信息
        $this->_getFeeForTripcloud();

        foreach ($this->_aInfos as $aInfo) {
            if (empty($aInfo['bill_info']['estimate_id'])) {
                continue;
            }

            $oCurProduct = null;
            $iEstimateID = $aInfo['order_info']['estimate_id'];
            //  @var Product $oProduct
            foreach ($this->_aProductList as $oProduct) {
                if ($iEstimateID == $oProduct->oOrderInfo->sEstimateID) {
                    $oCurProduct = $oProduct;
                    break;
                }
            }

            $this->_writePublicLog($aInfo, $oCurProduct, $this->_aResponseInfo);
            $this->_writeLogForRealTimeMonitor($aInfo,$oCurProduct,$this->_aResponseInfo);
        }
    }

    /**
     * 新出租事业线public日志走publicKey:taxi_order_estimate_price
     * 增加fee_msg字段
     * @return void
     */
    private function _writeTaxiEstimatePrice() {

        $aIndexedInfo = [];

        foreach ($this->_aInfos as $aInfo) {
            $iEstimateId = $aInfo['bill_info']['estimate_id'];
            $aIndexedInfo[$iEstimateId] = $aInfo;
        }

        foreach ($this->_aProductList as $oProduct) {
            if (!$this->_isTaxi($oProduct) || !isset($aIndexedInfo[$oProduct->oOrderInfo->sEstimateID])
                || empty($aIndexedInfo[$oProduct->oOrderInfo->sEstimateID])
            ) {
                continue;
            }

            $oCurProduct = $oProduct;
            $aInfo       = $aIndexedInfo[$oProduct->oOrderInfo->sEstimateID];

            $this->_writeTaxiPublicLog($aInfo, $oCurProduct, $this->_aResponseInfo);
        }
    }

    /**
     * @param array   $aInfo          $aInfo
     * @param Product $oProduct       $oProduct
     * @param array   $aResponseInfos $aResponseInfos
     * @return void
     */
    private function _writeTaxiPublicLog(array $aInfo, Product $oProduct, array $aResponseInfos) {

        $sCarLevel     = $aInfo['order_info']['require_level'];
        $aBillInfo     = $aInfo['bill_info']['bills'][$sCarLevel];
        $aEstimateData = $this->_getEstimateData($aInfo, $aResponseInfos);

        $aEstimateStatistic = [
            'opera_stat_key'        => 'taxi_order_estimate_price',
            'access_key_id'         => $oProduct->oCommonInfo->iAccessKeyID,
            'channel'               => $oProduct->oCommonInfo->sChannel,
            'pid'                   => $oProduct->oPassengerInfo->iPid,
            'product_id'            => $oProduct->oOrderInfo->iProductId,
            'combo_type'            => $oProduct->oOrderInfo->iComboType,
            'require_level'         => $sCarLevel,
            'area'                  => $oProduct->oAreaInfo->iArea,
            'to_area'               => $oProduct->oAreaInfo->iToArea,
            'scene_type'            => $oProduct->oOrderInfo->iSceneType,
            'total_fee'             => $aBillInfo['total_fee'] ?? 0,
            'pre_total_fee'         => $aBillInfo['pre_total_fee'] ?? 0,
            'dynamic_total_fee'     => $aBillInfo['dynamic_total_fee'] ?? 0,
            'cap_price'             => $aBillInfo['cap_price'] ?? 0,
            'estimate_fee'          => $aInfo['activity_info'][0]['estimate_fee'] ?? 0,
            'assign_type'           => $aInfo['order_info']['n_tuple']['assign_type'],
            'level_type'            => $aInfo['order_info']['n_tuple']['level_type'],
            'is_special_price'      => $oProduct->oOrderInfo->iIsSpecialPrice ? 'true' : 'false',
            'is_anycar'             => $aInfo['order_info']['n_tuple']['is_anycar'],
            'carpool_type'          => $oProduct->oOrderInfo->iCarpoolType,
            'product_category'      => $oProduct->oOrderInfo->iProductCategory,
            'select_type'           => $aEstimateData['select_type'],
            'recommend_type'        => $aEstimateData['recommend_type'],
            'estimate_trace_id'     => Trace::traceId(),
            'estimate_id'           => $aInfo['order_info']['estimate_id'],
            'fee_msg'               => $aEstimateData['fee_msg'],
            //车大联盟标识
            'spacious_car_alliance' => $oProduct->oOrderInfo->iSpaciousCarAlliance,
        ];

        PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);
    }

        /**
     * @param array   $aInfo          $aInfo
     * @param Product $oProduct       $oProduct
     * @param array   $aResponseInfos $aResponseInfos
     */
    private function _writePublicLog(array $aInfo, Product $oProduct, array $aResponseInfos) {
        $sCarLevel     = $aInfo['order_info']['require_level'];
        $aBillInfo     = $aInfo['bill_info']['bills'][$sCarLevel];
        $aProductInfo  = $aInfo['bill_info']['product_infos'][$sCarLevel];
        $aEstimateData = $this->_getEstimateData($aInfo, $aResponseInfos);
        if (empty($aEstimateData)) {
            return;
        }

        $aBillDisplayLines = CommonBillLogic::formatDisplayLines($aBillInfo['display_lines']);

        $bIsSupportMultiSelection = $aResponseInfos['data']['is_support_multi_selection'];

        $aLogFenceInfo = [];
        if (isset($aInfo['order_info']['fence_info']) && is_array($aInfo['order_info']['fence_info'])) {
            foreach ($aInfo['order_info']['fence_info'] as $aFenceInfo) {
                if (!empty($aFenceInfo)) {
                    $aLogFenceInfo[] = $aFenceInfo;
                }
            }
        }

        $aEstimateStatistic = [
            'opera_stat_key'                       => 'g_order_estimate_price',
            'imei'                                 => $oProduct->oCommonInfo->sImei,
            'appversion'                           => $oProduct->oCommonInfo->sAppVersion,
            'client_type'                          => $oProduct->oCommonInfo->iClientType,
            'access_key_id'                        => $oProduct->oCommonInfo->iAccessKeyID,
            'channel'                              => $oProduct->oCommonInfo->sChannel,
            'pLang'                                => $oProduct->oCommonInfo->sLang,
            'pid'                                  => $oProduct->oPassengerInfo->iPid,
            'phone'                                => $oProduct->oPassengerInfo->sPhone,
            'biz_user_type'                        => $oProduct->oPassengerInfo->iUserType,
            'menu_id'                              => $oProduct->oOrderInfo->sMenuID,
            'page_type'                            => $oProduct->oOrderInfo->iPageType,
            'origin_page_type'                     => $oProduct->oOrderInfo->iOriginPageType,
            'call_car_type'                        => $oProduct->oOrderInfo->iCallCarType,
            'product_id'                           => $oProduct->oOrderInfo->iProductId,
            'combo_type'                           => $oProduct->oOrderInfo->iComboType,
            'bill_combo_type'                      => $aInfo['bill_info']['product_infos'][$sCarLevel]['combo_type'],
            'require_level'                        => $sCarLevel,
            'area'                                 => $oProduct->oAreaInfo->iArea,
            'to_area'                              => $oProduct->oAreaInfo->iToArea,
            'district'                             => $oProduct->oAreaInfo->iDistrict,
            'flng'                                 => $oProduct->oAreaInfo->fFromLng,
            'flat'                                 => $oProduct->oAreaInfo->fFromLat,
            'tlng'                                 => $oProduct->oAreaInfo->fToLng,
            'tlat'                                 => $oProduct->oAreaInfo->fToLat,
            'current_lng'                          => $oProduct->oAreaInfo->fCurLng,
            'current_lat'                          => $oProduct->oAreaInfo->fCurLat,
            'starting_poi_id'                      => $oProduct->oAreaInfo->sFromPoiId,
            'dest_poi_id'                          => $oProduct->oAreaInfo->sToPoiId,
            'choose_f_searchid'                    => $this->_oRequest->getChooseFSearchid(),
            'choose_t_searchid'                    => $this->_oRequest->getChooseTSearchid(),
            'choose_f_srctag'                      => $oProduct->oAreaInfo->sFromPoiType,
            'choose_t_srctag'                      => $oProduct->oAreaInfo->sToPoiType,
            'county'                               => $oProduct->oAreaInfo->iFromCounty,
            'to_county'                            => $oProduct->oAreaInfo->iToCounty,
            'from_name'                            => str_replace(PHP_EOL, '', $oProduct->oAreaInfo->sFromName),
            'to_name'                              => str_replace(PHP_EOL, '', $oProduct->oAreaInfo->sToName),
            'scene_type'                           => $oProduct->oOrderInfo->iSceneType,
            'carpool_seat_num'                     => $oProduct->oOrderInfo->iCarpoolSeatNum,
            'order_type'                           => $oProduct->oOrderInfo->iOrderType,
            'designated_driver'                    => $oProduct->oOrderInfo->sDesignatedDriver,
            'time_cost'                            => $aInfo['bill_info']['driver_minute'],
            'driver_second'                        => $aInfo['bill_info']['driver_second'] ?? 0,
            'driver_metre'                         => $aInfo['bill_info']['driver_metre'] ?? 0,
            'total_fee'                            => $aBillInfo['total_fee'] ?? 0,
            'pre_total_fee'                        => $aBillInfo['pre_total_fee'] ?? 0,
            'dynamic_total_fee'                    => $aBillInfo['dynamic_total_fee'] ?? 0,
            'cap_price'                            => $aBillInfo['cap_price'] ?? 0,
            'estimate_fee'                         => $aInfo['activity_info'][0]['estimate_fee'] ?? 0,
            'exact_estimate_fee'                   => $aInfo['activity_info'][0]['exact_estimate_fee'] ?? 0,
            'is_carpool_open'                      => (int) ($aInfo['bill_info']['is_carpool_open']),
            'carpool_fail_price'                   => $aBillInfo['carpool_fail_dynamic_total_fee'] ?? 0,
            'discount_fee'                         => $aInfo['activity_info'][0]['discount_fee'],
            'carpool_fail_discount_fee'            => $aInfo['activity_info'][1]['discount_fee'],
            'station_id'                           => $aInfo['bill_info']['carpool_station_info']['uid'] ?? 0,
            'is_hit_member_capping'                => (int) ($aBillInfo['is_hit_member_capping'] ?? 0),
            'member_dynamic_capping'               => $aBillInfo['member_dynamic_capping'] ?? -1,
            'dynamic_price_without_member_capping' => $aBillInfo['dynamic_price_without_member_capping'] ?? 0,
            'member_level_id'                      => $aInfo['passenger_info']['member_profile']['level_id'] ?? 0,
            'default_pay_type'                     => $this->_getSelectedPayType($aInfo['payments_info']['user_pay_info']['busi_payments'] ?? []),
            'order_n_tuple'                        => json_encode($aInfo['order_info']['n_tuple'] ?? []),
            'combo_id'                             => $oProduct->oOrderInfo->iComboId,    //combo_id
            'is_special_price'                     => $oProduct->oOrderInfo->iIsSpecialPrice,
            'carpool_type'                         => $oProduct->oOrderInfo->iCarpoolType,
            'airport_type'                         => $oProduct->oOrderInfo->iAirportType,
            'exam_type'                            => $oProduct->oOrderInfo->iExamType,
            'product_category'                     => $oProduct->oOrderInfo->iProductCategory,
            'departure_time'                       => $oProduct->oOrderInfo->iDepartureTime,
            'is_dynamic'                           => $aBillInfo['is_has_dynamic'] ?? 0,
            'dynamic_price_id'                     => $aBillInfo['dynamic_info']['dynamic_price_id'],
            'dynamic_diff_price'                   => $aBillInfo['dynamic_diff_price'],
            'dynamic_price'                        => $aBillInfo['dynamic_info']['dynamic_price'],
            'dynamic_kind'                         => $aBillInfo['dynamic_info']['dynamic_kind'],
            'dynamic_times'                        => $aBillInfo['dynamic_times'] ?? 0,
            'coupon'                               => $this->_getCouponInfoStr($aInfo['order_info']['channel'], $aInfo),
            'discount_info'                        => json_encode($aInfo['activity_info'][0]['discount_desc']),
            'disabled'                             => $aEstimateData['disabled'],
            'select_type'                          => $aEstimateData['select_type'],
            'recommend_type'                       => $aEstimateData['recommend_type'],
            'form_show_type'                       => $aEstimateData['form_show_type'],
            'user_status'                          => $aInfo['price_extra']['user_status'],
            'estimate_trace_id'                    => Trace::traceId(),
            'estimate_id'                          => $aInfo['order_info']['estimate_id'],
            'is_support_multi_selection'           => (int) $bIsSupportMultiSelection,
            'red_packet'                           => (int) $aBillDisplayLines['red_packet']['value'] ?? 0.0,
            'rewards_display_type'                 => $this->_getRewardDisplayType($aInfo['activity_info'][0]['discount_desc'], $aBillInfo),
            'show_pickup_service'                  => $this->_isShowPickUp($aEstimateData),
            'wait_discount'                        => $aBillInfo['wait_discount'] ?? 0.1,
            'wait_minute'                          => $aBillInfo['dynamic_info']['wait_minute'] ?? 10,
            'source_channel'                       => $this->_oRequest->getSourceChannel(),
            'queue_data'                           => $this->_getQueueLen($aInfo['order_info']['queue_data'], $oProduct->oOrderInfo->iProductId, $sCarLevel, $oProduct->oOrderInfo->iComboType),
            'fence_ids'                            => $aLogFenceInfo,
            'count_price_type'                     => $aBillInfo['count_price_type'] ?? 0,
            'stopover_points'                      => json_encode($oProduct->oOrderInfo->aStopoverPoints),
            'discount'                             => $aBillInfo['discount'] ?? 0,
            // 'dds_activity_type'                    => $oProduct->oOrderInfo->aActivityInfo['type'] ?? 0,
            'dds_activity_type'                    => 0,
            // 'dds_activity_key'                     => $oProduct->oOrderInfo->aActivityInfo['activity_key'] ?? '',
            'dds_activity_key'                     => '',
            'activity_discount_fee'                => $aBillDisplayLines['activity_discount_fee']['value'] ?? 0.0,
            'sub_group_id'                         => $aEstimateData['sub_group_id'] ?? null,
            'preferred_route_id'                   => $this->_oRequest->getPreferredRouteId(),
            'xpsid'                                => $this->_oRequest->getXpsid(),
            'xpsid_root'                           => $this->_oRequest->getXpsidRoot(),
            'is_exposure'                          => (int)empty($aEstimateData),
            'price_info_desc'                      => json_encode($aEstimateData['price_info_desc']),
        ];

        $iOptionCnt = isset($aEstimateData['option_data']['service_list']) ? count($aEstimateData['option_data']['service_list']) : 0;
        $iPreferCnt = $aEstimateData['prefer_data']['prefer_select_count'] ?? 0;
        $iPreferCnt = $iPreferCnt - $iOptionCnt;

        $aEstimateStatistic['option_count']        = $iOptionCnt;
        $aEstimateStatistic['prefer_select_count'] = $iPreferCnt;

        if (Order::isSpecialRateV2($aEstimateStatistic)) {
            $sPriceCap = $aBillInfo['extra_info']['price_capability'] ?? '{}';
            $aPriceCap = json_decode($sPriceCap, true);
            $aEstimateStatistic['price_capability'] = $aPriceCap['sp_open'] ?? 0;
            $aAthenaExtra = $aInfo['athena_extra'] ?? [];
            $aEstimateStatistic['athena_capacity'] = (int) ($aAthenaExtra['sp_capability'] ?? 0);
            $aDDSDecision = DecisionLogic::getInstance()->getProductInfoByCategoryId($aInfo['order_info']['product_category']);
            $aEstimateStatistic['dds_capacity'] = (int) (false == $aDDSDecision['remove_flag']);
            $aEstimateStatistic['sp_capacity']  = $aEstimateStatistic['price_capability'] & $aEstimateStatistic['athena_capacity'] & $aEstimateStatistic['dds_capacity'];
        }

        if (Util::isCarpool($aInfo)) {
            if (!empty($aInfo['activity_info'][0]['carpool_scene_price'])) {
                $aEstimateStatistic['carpool_scene_price'] = json_encode($aInfo['activity_info'][0]['carpool_scene_price']);
            }

            //如果命中了区域渗透路线
            if (\BizLib\Constants\Horae::TYPE_COMBO_CARPOOL_INTER_CITY == $aProductInfo['combo_type']) {
                //跨城
                $aRouteInfo = $aInfo['bill_info']['match_routes'];
                $aEstimateStatistic['combo_id']    = $aRouteInfo['route_id'];
                $aEstimateStatistic['route_group'] = $aRouteInfo['route_group'];
                $aEstimateStatistic['inter_mode']  = $aRouteInfo['inter_mode'];
                $aEstimateStatistic['route_name']  = $aRouteInfo['route_name'];
                if (!empty($this->_oRequest->getDepartureRange())) {
                    $sDepartureRange = $this->_oRequest->getDepartureRange();
                } else {
                    $sDepartureRange = $aRouteInfo['time_span'][0]['range'][0]['value'];
                }

                $aEstimateStatistic['departure_range'] = $sDepartureRange;

                // 标识城际开放围栏路线
                $aEstimateStatistic['is_open_fence'] = InterCity::getRouteConf($aRouteInfo['route_id'], 'is_open_fence', 0);
            }
        }

        if (true == TripcloudProduct::isTripcloudByProductID($aInfo['order_info']['product_id'])) {
            $fSpecialRateCapPrice = $this->_aBillsForTcFeeMonitor['special_rate_cap_price'];
            if ($fSpecialRateCapPrice > 0 && $aEstimateStatistic['cap_price'] > 0) {
                $capPriceSub = bcsub($aEstimateStatistic['cap_price'], $fSpecialRateCapPrice, 2);
                $aEstimateStatistic['special_rate_cap_price'] = $fSpecialRateCapPrice;
                $aEstimateStatistic['tc_cap_price_diff']      = $capPriceSub;
            }

            $fFastDynamicTotalFee = $this->_aBillsForTcFeeMonitor['fast_dynamic_total_fee'];
            $fFastEstimateFee     = $this->_aBillsForTcFeeMonitor['fast_estimate_fee'];
            if ($aEstimateStatistic['dynamic_total_fee'] > 0) {
                $aEstimateStatistic['fast_dynamic_total_fee'] = $fFastDynamicTotalFee;
                $aEstimateStatistic['fast_estimate_fee']      = $fFastEstimateFee;
            }
        }

        //构建费项信息
        $aEstimateStatistic['fee_detail_info'] = $this->_buildFeeDetailInfo($aInfo);
        $aEstimateStatistic['custom_service']  = $this->_buildCustomService($aInfo, $oProduct);

        PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);
    }

    /**
     * @param array $aInfo          aInfo
     * @param array $aResponseInfos 预估结果集
     *
     * @return array
     */
    private function _getEstimateData($aInfo, $aResponseInfos) {
        $iEstimateId = $aInfo['order_info']['estimate_id'];
        if (!empty($aResponseInfos['data']['estimate_data'])) {
            foreach ($aResponseInfos['data']['estimate_data'] as $aData) {
                if ($iEstimateId == $aData['estimate_id']) {
                    return $aData;
                }
            }
        }

        return [];
    }

    /**
     * @param array $aPayments aPayments
     *
     * @return int
     */
    private function _getSelectedPayType($aPayments) {
        foreach ($aPayments as $aPayment) {
            if (!empty($aPayment['isSelected'])) {
                return $aPayment['tag'];
            }
        }

        return 0;
    }

    /**
     * @param int   $iChannel channel
     * @param array $aInfo    aInfo
     *
     * @return string
     */
    private function _getCouponInfoStr($iChannel, $aInfo) {
        $aResult     = [];
        $aCouponInfo = $aInfo['activity_info'][0]['coupon_info'];
        if (empty($aCouponInfo['default_coupon'])) {
            $aResult['default_coupon'] = [];
        } else {
            $aResult['default_coupon'] = [
                'channel'       => $iChannel,
                'batchid'       => $aCouponInfo['default_coupon']['batchid'] ?? 0,
                'amount'        => ($aCouponInfo['default_coupon']['amount'] ?? 0) / 100,
                'estimate_show' => $aCouponInfo['default_coupon']['estimate_show'] ?? 0,
            ];
        }

        if (empty($aCouponInfo['activity_coupon'])) {
            $aResult['activity_coupon'] = [];
        } else {
            $aResult['activity_coupon'] = [
                'channel'       => $iChannel,
                'batchid'       => $aCouponInfo['activity_coupon']['batchid'] ?? 0,
                'amount'        => $aCouponInfo['activity_coupon']['money'] ?? 0,
                'estimate_show' => $aCouponInfo['activity_coupon']['estimate_show'] ?? 0,
            ];
        }

        return json_encode($aResult);
    }

    /**
     * 判断reward的展示模式.
     *
     * @param array $aDiscountInfo $aDiscountInfo
     * @param array $aBillInfo     $aBillInfo
     *
     * @return int
     */
    private function _getRewardDisplayType($aDiscountInfo, $aBillInfo) {
        if (empty($aDiscountInfo)) {
            return self::PRICE_DESC_NO_REWARDS;
        }

        $iDiscountNum = 0;
        $bHasRewards  = false;
        foreach ($aDiscountInfo as $aDiscountItem) {
            if (($fDiscount = abs($aDiscountItem['amount'] ?? 0)) > 0 && 'infofee' != $aDiscountItem['type']) {
                ++$iDiscountNum;
            }

            if (isset($aDiscountItem['type'], $aDiscountItem['amount']) && 'reward' === $aDiscountItem['type'] && (int) ($aDiscountItem['amount']) > 0) {
                $bHasRewards = true;
            }
        }

        $fDynamicPrice = $aBillInfo['dynamic_diff_price'] ?? 0.0;
        if ($fDynamicPrice < 0) {
            ++$iDiscountNum;
        }

        $fFixedPreferential = $aBillInfo['fixed_preferential'] ?? 0.0;
        if ($fFixedPreferential < 0) {
            ++$iDiscountNum;
        }

        if ($bHasRewards) {
            if ($iDiscountNum > 1) {
                return self::PRICE_DESC_INCLUDE_REWARDS;
            } else {
                return self::PRICE_DESC_ONLY_REWARDS;
            }
        } else {
            return self::PRICE_DESC_NO_REWARDS;
        }
    }

    /**
     * @param array $aEstimateData $aEstimateData
     *
     * @return int
     */
    private function _isShowPickUp($aEstimateData) {
        if (!empty($aEstimateData['scene_data'])) {
            $aCustomServiceId = array_column($aEstimateData['scene_data'], 'id');
            if (in_array(CustomLogic::CUSTOM_SERVICE_GUIDE_NEW, $aCustomServiceId)) {
                return 1;
            }
        }

        return 0;
    }

    /**
     * @desc: 获取排队队列信息
     *
     * @param array  $aQueueData queue_data
     * @param int    $iProductId product_id
     * @param string $sCarLevel  car_level
     * @param int    $iComboType combo_type
     */
    private function _getQueueLen($aQueueData, $iProductId, $sCarLevel, $iComboType) {
        if (empty($aQueueData)) {
            return null;
        }

        $aOneConf   = [
            'product_id'    => $iProductId,
            'require_level' => $sCarLevel,
            'combo_type'    => $iComboType,
        ];
        $iQueueType = \BizCommon\Models\Order\LineUpOrderComModel::getInstance()->getQueueTypeFromOneConf($aOneConf);
        if (isset($aQueueData[$iQueueType])) {
            return json_encode($aQueueData[$iQueueType]);
        }

        return null;
    }

    /**
     * 获取三方价格监控需要的价格数据
     * @return void
     */
    private function _getFeeForTripcloud() {
        $totalCnt = 2;
        $this->_aBillsForTcFeeMonitor = [];
        foreach ($this->_aInfos as $aInfo) {
            if (ProductCategory::PRODUCT_CATEGORY_FAST == $aInfo['order_info']['product_category']) {
                $sCarLevel           = $aInfo['order_info']['require_level'];
                $aBillInfo           = $aInfo['bill_info']['bills'][$sCarLevel];
                $fastDynamicTotalFee = $aBillInfo['dynamic_total_fee'] ?? 0;
                $fastEstimateFee     = $aInfo['activity_info'][0]['estimate_fee'] ?? 0;
                $this->_aBillsForTcFeeMonitor['fast_dynamic_total_fee'] = $fastDynamicTotalFee;
                $this->_aBillsForTcFeeMonitor['fast_estimate_fee']      = $fastEstimateFee;
                $totalCnt--;
            }

            if (ProductCategory::PRODUCT_CATEGORY_FAST_SPECIAL_RATE == $aInfo['order_info']['product_category']) {
                $sCarLevel           = $aInfo['order_info']['require_level'];
                $aBillInfo           = $aInfo['bill_info']['bills'][$sCarLevel];
                $specialRateCapPrice = $aBillInfo['cap_price'] ?? 0;
                $this->_aBillsForTcFeeMonitor['special_rate_cap_price'] = $specialRateCapPrice;
                $totalCnt--;
            }

            if (0 == $totalCnt) {
                break;
            }
        }

        return;
    }

    /**
     * @param object $oProduct $oProduct
     * @return bool
     */
    private function _isTaxi($oProduct) {
        $iProductID = $oProduct->oOrderInfo->iProductId;
        return in_array($iProductID, [OrderSystem::PRODUCT_ID_UNITAXI, OrderSystem::PRODUCT_ID_BUSINESS_TAXI_CAR]);
    }


    /**
     * 实时监控日志
     * @param array   $aInfo          ...
     * @param Product $oProduct       ...
     * @param array   $aResponseInfos ...
     * @return void
     */
    private function _writeLogForRealtimeMonitor(array $aInfo, Product $oProduct, array $aResponseInfos) {
        $sCarLevel = $aInfo['order_info']['require_level'];
        $aBillInfo = $aInfo['bill_info']['bills'][$sCarLevel];
        // 基础信息
        $aEstimateStatistic = [
            'opera_stat_key'    => 'g_realtime_estimate_price',
            'app_version'       => $oProduct->oCommonInfo->sAppVersion,
            'client_type'       => $oProduct->oCommonInfo->iClientType,
            'access_key_id'     => $oProduct->oCommonInfo->iAccessKeyID,
            'channel'           => $oProduct->oCommonInfo->sChannel,
            'page_type'         => $oProduct->oOrderInfo->iPageType,
            'origin_page_type'  => $oProduct->oOrderInfo->iOriginPageType,
            'call_car_type'     => $oProduct->oOrderInfo->iCallCarType,
            'product_id'        => $oProduct->oOrderInfo->iProductId,
            'combo_type'        => $oProduct->oOrderInfo->iComboType,
            'require_level'     => $aInfo['order_info']['require_level'],
            'area'              => $oProduct->oAreaInfo->iArea,
            'to_area'           => $oProduct->oAreaInfo->iToArea,
            'county'            => $oProduct->oAreaInfo->iFromCounty,
            'to_county'         => $oProduct->oAreaInfo->iToCounty,
            'order_type'        => $oProduct->oOrderInfo->iOrderType,
            'estimate_id'       => $aInfo['order_info']['estimate_id'],
            'estimate_trace_id' => Trace::traceId(),
            'is_special_price'  => $oProduct->oOrderInfo->iIsSpecialPrice,
            'product_category'  => $oProduct->oOrderInfo->iProductCategory,
            'carpool_type'      => $oProduct->oOrderInfo->iCarpoolType,
        ];

        $fnGetDualCarpoolDiscount = function ($aBillInfo, $aActivityInfo) {
            $aDualCarpoolDiscount = [];
            if (2 != count($aActivityInfo)) {
                return $aDualCarpoolDiscount;
            }

            // 拼成实付折扣
            $aDualCarpoolDiscount['dual_success_actual_discount'] = number_format($aActivityInfo[0]['estimate_fee'] / $aBillInfo['pre_total_fee'],2);
            // 拼成应付折扣
            $aDualCarpoolDiscount['dual_success_need_discount'] = number_format($aBillInfo['dynamic_total_fee'] / $aBillInfo['pre_total_fee'],2);
            // 拼不成实付折扣
            $aDualCarpoolDiscount['dual_fail_actual_discount'] = number_format($aActivityInfo[1]['estimate_fee'] / $aBillInfo['pre_total_fee'],2);
            // 拼不成应付折扣
            $aDualCarpoolDiscount['dual_fail_need_discount'] = number_format($aBillInfo['carpool_fail_dynamic_total_fee'] / $aBillInfo['pre_total_fee'],2);
            return $aDualCarpoolDiscount;
        };

        // 两口价折扣
        if (Horae::isCarpoolUnSuccessFlatPrice($aInfo['order_info'])) {
            $aEstimateStatistic += $fnGetDualCarpoolDiscount($aBillInfo, $aInfo['activity_info'] ?? []);
        }

        // 增加品类是否弹窗
        if (!empty($aResponseInfos['data']['guide_info'])) {
            $aEstimateStatistic['guide_pop_up'] = $aResponseInfos['data']['guide_info']['select_product_category'] == $oProduct->oOrderInfo->iProductCategory ? 1 : 0;
        }

        PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);

    }

    /**
     * 构建费项信息
     * @param array $aInfo 品类信息
     * @return string
     */
    private function _buildFeeDetailInfo($aInfo) {
        $aFeeDetailInfo     = [];
        $iRequireLevel      = $aInfo['order_info']['require_level'];
        $aTempFeeDetailInfo = $aInfo['bill_info']['bills'][$iRequireLevel]['fee_detail_info'];
        if (empty($aTempFeeDetailInfo) || !is_array($aTempFeeDetailInfo)) {
            return '[]';
        }

        foreach ($aTempFeeDetailInfo as $sFeeName => $fFeeItem) {
            // 出租车峰期加价服务费
            if (PRODUCT_CATEGORY_UNIONE == $aInfo['order_info']['product_category']) {
                $oTaxiPeakFeeClient = TaxiPeakFee::getPoolInstance($aInfo);
                if ($oTaxiPeakFeeClient->getStatus() && 'taxi_peak_price' == $sFeeName) {
                    $aFeeDetailInfo[] = [
                        'key'    => 'taxi_peak_fee',
                        'amount' => empty($fFeeItem) ? 0 : $fFeeItem * 100, //按分存数据
                    ];
                }

                continue;
            }

            $aFeeDetailInfo[] = [
                'key'    => $sFeeName,
                'amount' => empty($fFeeItem) ? 0 : $fFeeItem * 100, //按分存数据
            ];
        }

        return json_encode($aFeeDetailInfo);
    }

    /**
     * 构建用户选择数据
     * @param array   $aInfo    品类信息
     * @param Product $oProduct 品类信息
     * @return string
     */
    private function _buildCustomService($aInfo, $oProduct) {
        $aCustomService = [];
        $aSceneData     = Common::getServiceInfo(
            $oProduct->oOrderInfo->iProductId,
            $oProduct->oOrderInfo->iRequireLevel,
            $oProduct->oOrderInfo->iComboType,
            true
        );
        if (empty($aSceneData) || !is_array($aSceneData)) {
            return '{}';
        }

        foreach ($aSceneData as $aItem) {
            $aCustomService[$aItem['id']] = [
                'count'       => $aItem['max'], //数量
                'is_selected' => $oProduct->oOrderInfo->aPersonalizedCustomOption[$aItem['id']]['count'] ?: 0,
            ];

            // 出租车峰期加价服务费
            if (OrderSystem::PRODUCT_ID_UNITAXI == $aInfo['order_info']['product_id']) {
                if (PRODUCT_CATEGORY_UNIONE == $aInfo['order_info']['product_category']) {
                    $oTaxiPeakFeeClient = TaxiPeakFee::getPoolInstance($aInfo);
                    if (!$oTaxiPeakFeeClient->getStatus()) {
                        unset($aCustomService[$aItem['id']]);
                    }

                    if ($oTaxiPeakFeeClient->getIsInterActive()) {
                        $aCustomService[$aItem['id']] = $oTaxiPeakFeeClient->buildPublicLogData();
                    } else {
                        unset($aCustomService[$aItem['id']]);
                    }
                } else {
                    unset($aCustomService[$aItem['id']]);
                }
            }
        }

        if (empty($aCustomService)) {
            return '{}';
        }

        return json_encode($aCustomService);
    }
}
