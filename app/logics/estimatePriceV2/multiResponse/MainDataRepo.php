<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse;

use BizCommon\Constants\OrderNTuple;
use BizCommon\Logics\Driver\DriverEmotionalCare;
use BizCommon\Utils\Order;
use BizLib\Constants\Common;
use BizLib\Constants\Horae;
use BizLib\Constants\OrderSystem;
use BizLib\Utils\CarLevel;
use BizLib\Utils\Product;
use BizLib\Utils\ProductCategory;
use BizLib\Utils\Request;
use Exception;
use PreSale\Logics\order\AnyCarOrderLogic;
use PreSale\Logics\estimatePriceV2\multiResponse\component\productInfo\Common as ProductCommon;
use TripcloudCommon\Utils\ApolloConf;
use Xiaoju\Apollo\Apollo;
use BizLib\Utils\Language;
use BizCommon\Utils\Horae as BizHorae;
use PreSale\Logics\estimatePriceV2\DecisionLogic;
use PreSale\Logics\estimatePriceV2\CarpoolCommuteCardLogic;

/**
 * Class MainDataRepo
 * @package PreSale\Logics\estimatePriceV2\multiResponse
 */
class MainDataRepo
{
    const APOLLO_CONF_SCENE_CATEGORY_CONFIG       = 'scene_category_config_v2';
    const APOLLO_CONF_ESTIMATE_FORM_DATA_MATERIEL = 'estimate_formdata_materiel';
    const APOLLO_CONF_ESTIMATE_FORM_DATA_CATEGORY = 'estimate_formdata_category';
    const APOLLO_CONF_PAYMENT_TYPE           = 'payment_type';
    const APOLLO_CONF_PAYMENT_TYPE_COMPONENT = 'payment_type_component';

    const LANGUAGE = 'lang';

    private static $_aInfos;
    private static $_sLang;
    private static $_aCommonInfo;

    /**
     * @param array $aInfos $aInfos
     * @throws Exception e
     * @return void
     */
    public static function init($aInfos) {
        self::$_aInfos = $aInfos;
        self::$_sLang  = Language::getDefaultLanguage();
        $aInfo         = current($aInfos);
        self::$_aCommonInfo = $aInfo['common_info'];
    }


    /**
     * @return int
     */
    public static function getAccessKeyId() {
        if (empty(self::$_aCommonInfo)) {
            return Request::getInstance()->getInt('access_key_id', 0);
        }

        return self::$_aCommonInfo['access_key_id'] ?? 0;
    }

    /**
     * @return string
     */
    public static function getAppLanguage() {
        return self::$_sLang;
    }

    /**
     * @return string
     */
    public static function getMenuID() {
        if (empty(self::$_aInfos)) {
            return Request::getInstance()->getStr('menu_id', '');
        }

        return self::$_aInfos[0]['order_info']['menu_id'] ?? '';
    }

    /**
     * 根据car_level获取对应的拼车aInfo
     * @param string $sCarLevel car_level
     * @return array
     */
    public static function getCarpoolInfoByCarLevel($sCarLevel) {
        foreach (self::$_aInfos as $aInfo) {
            if ($aInfo['order_info']['require_level'] == $sCarLevel && Horae::TYPE_COMBO_CARPOOL == $aInfo['order_info']['combo_type']) {
                return $aInfo;
            }
        }

        return [];
    }

    /**
     * 根据car_level获取对应特价车的aInfo
     * @param string $sCarLevel car_level
     * @return array
     */
    public static function getSpecialRateInfoByCarLevel($sCarLevel) {
        foreach (self::$_aInfos as $aInfo) {
            if ($aInfo['order_info']['require_level'] == $sCarLevel && $aInfo['order_info']['n_tuple']['is_special_price']) {
                return $aInfo;
            }
        }

        return [];
    }

    /**
     * 对应product_id的拼车是否开城
     * @param int $product product_id
     * @return bool
     */
    public static function isCarpoolOpenInProduct($product) {
        foreach (self::$_aInfos as $aInfo) {
            if ($aInfo['order_info']['product_id'] == $product) {
                if ($aInfo['bill_info']['is_carpool_open']) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     *  是否为鸿鹄客企品类
     * @param int $iProductCategory 品类
     * @param int $iProductId       product_id
     * @return bool
     */
    public static function isBelongTripCloud($iProductCategory, $iProductId = 0) {
        if (!empty($iProductCategory)) {
            return ApolloConf::isTCIntercityCarpoolByProductCategory($iProductCategory);
        }

        if (!empty($iProductId)) {
            return ApolloConf::isTCIntercityCarpoolByProductID($iProductId);
        }

        return false;
    }

    /**
     * 获取快车total_fee
     * @return int
     */
    public static function getFastCarTotalFee() {
        foreach (self::$_aInfos as $aInfo) {
            $sCarLevel = $aInfo['order_info']['require_level'];

            //优先使用product_category 识别普通快车
            $iProductCategory = $aInfo['order_info']['product_category'];
            if (!empty($iProductCategory)) {
                if (ProductCategory::PRODUCT_CATEGORY_FAST == $iProductCategory) {
                    return $aInfo['bill_info']['bills'][$sCarLevel]['total_fee'];
                }
            } else {
                //若未找到则用三元组匹配
                $iProductId = $aInfo['order_info']['product_id'];
                $iComboType = $aInfo['bill_info']['product_infos'][$sCarLevel]['combo_type'];
                if (Product::isFastcar($iProductId)
                    && CarLevel::DIDI_PUTONG_CAR_LEVEL == $sCarLevel
                    && OrderSystem::TYPE_COMBO_DEFAULT == $iComboType
                ) {
                    return $aInfo['bill_info']['bills'][$sCarLevel]['total_fee'];
                }
            }
        }

        return 0;
    }

    /**
     * 获取特惠快车预估价
     * @return int
     */
    public static function getFastCarSpecialRateTotalFee() {
        foreach (self::$_aInfos as $aInfo) {
            if (Util::isFastCarSpecialRate($aInfo)) {
                return $aInfo['bill_info']['bills'][$aInfo['order_info']['require_level']]['total_fee'] ?? 0;
            }
        }

        return 0;
    }

    /**
     * 获取特惠快车快车价
     * @return int
     */
    public static function getDiXiaoDiFastCarFee() {
        $fAmount = 0;
        foreach (self::$_aInfos as $aInfo) {
            if (Order::isSpecialRateV2($aInfo['order_info']) && \BizCommon\Utils\Common::isRegionalWeChatMiniProgram($aInfo['common_info']['access_key_id'])) {
                $carLevel = $aInfo['order_info']['require_level'];
                $fAmount  = $aInfo['bill_info']['bills'][$carLevel]['compare_price_info'][$carLevel]['price'] ?? 0;
            }
        }

        return $fAmount;
    }

    /**
     * 获取 特惠快车 福利金
     * @return int
     */
    public static function getFastCarSpecialRateWelfareFee() {
        foreach (self::$_aInfos as $aInfo) {
            if (Util::isFastCarSpecialRate($aInfo)) {
                return $aInfo['bill_info']['bills'][$aInfo['order_info']['require_level']]['extra_info']['welfare'] ?? 0;
            }
        }

        return 0;
    }

// 注释-待删
//    /**
//     * 获取城际拼车的预估ID
//     * @param int $iCarpoolType CarpoolType 3 城际拼车 6 城际拼车-新模式
//     * @return string
//     */
//    public static function getInterCityCarpoolEstimateID($iCarpoolType) {
//        foreach (self::$_aInfos as $aInfo) {
//            $iCurComboType   = $aInfo['order_info']['combo_type'];
//            $iCurCarpoolType = $aInfo['order_info']['carpool_type'];
//            if (Horae::TYPE_COMBO_CARPOOL_INTER_CITY == $iCurComboType && $iCurCarpoolType == $iCarpoolType) {
//                return $aInfo['order_info']['estimate_id'];
//            }
//        }
//
//        return '';
//    }

    /**
     * @Desc: mixed[] Array structure to count the elements of.
     * @return int
     * @property getFastCarEstimateFee $getFastCarEstimateFee
     * @Author:<EMAIL>
     */
    public static function getFastCarEstimateFee() {
        foreach (self::$_aInfos as $aInfo) {
            $iProductId = $aInfo['order_info']['product_id'];
            $sCarLevel  = $aInfo['order_info']['require_level'];
            $iComboType = $aInfo['bill_info']['product_infos'][$sCarLevel]['combo_type'];
            if (Product::isFastcar($iProductId) && CarLevel::DIDI_PUTONG_CAR_LEVEL == $sCarLevel && OrderSystem::TYPE_COMBO_DEFAULT == $iComboType) {
                return $aInfo['activity_info']['0']['estimate_fee'];
            }
        }

        return 0;
    }

    // 注释-待删
//    /**
//     * getInterCityActivityFee
//     * @param int $iCarpoolType CarpoolType
//     * @return int
//     */
//    public static function getInterCityActivityFee($iCarpoolType) {
//        foreach (self::$_aInfos as $aInfo) {
//            // 用Horae的方法对orderinfo判
//            $iCurComboType   = $aInfo['order_info']['combo_type'];
//            $iCurCarpoolType = $aInfo['order_info']['carpool_type'];
//            if (Horae::TYPE_COMBO_CARPOOL_INTER_CITY == $iCurComboType && $iCurCarpoolType == $iCarpoolType) {
//                return $aInfo['activity_info']['0']['estimate_fee'];
//            }
//        }
//
//        return 0;
//    }

// 注释-待删
//    /**
//     * 判断是否有城际
//     * @return bool
//     */
//    public static function hasInterCityCarpool() {
//        foreach (self::$_aInfos as $aInfo) {
//            if (Horae::TYPE_COMBO_CARPOOL_INTER_CITY == $aInfo['order_info']['combo_type']) {
//                return true;
//            }
//        }
//
//        return false;
//    }

// 注释-待删
//    /**
//     * 判断是否有特惠出租车
//     * @return bool
//     */
//    public static function hasSpecialTaxi() {
//        foreach (self::$_aInfos as $aInfo) {
//            if ($aInfo['order_info']['n_tuple']['is_special_price']
//                && in_array($aInfo['order_info']['n_tuple']['product_id'],[Product::PRODUCT_ID_UNIONE_TAXI, Product::PRODUCT_ID_BUSINESS_UNIONE_TAXI])
//            ) {
//                return true;
//            }
//        }
//
//        return false;
//    }

    /**
     * 判断是否有特惠快车
     * @return bool
     */
    public static function hasSpecialRate() {
        foreach (self::$_aInfos as $aInfo) {
            if (Order::isSpecialRateV2($aInfo['order_info'])) {
                return true;
            }
        }

        return false;
    }

    // 注释-待删
//    /**
//     * 判断是否有Uber
//     * @return bool
//     */
//    public static function hasUber() {
//        foreach (self::$_aInfos as $aInfo) {
//            if (Product::isUber($aInfo['order_info']['product_id'])) {
//                return true;
//            }
//        }
//
//        return false;
//    }

    /**
     * get passenger info
     * @return array
     */
    public static function getPassengerInfo() {
        if (!empty(self::$_aInfos[0]['passenger_info'])) {
            return self::$_aInfos[0]['passenger_info'];
        }

        return [];
    }

    /**
     * get carpool n_tuple
     * @param array $aProduct 产品数组
     * @return array
     */
    public static function getCarpoolNTuple($aProduct) {
        //如果是企业级用product_id判断，企业级没有menu_id
        if (Common::MENU_DACHE_ANYCAR == self::$_aInfos[0]['order_info']['menu_id']) {
            foreach (self::$_aInfos as $aInfo) {
                if ($aProduct['product_category'] == $aInfo['order_info']['product_category']) {
                    return $aInfo['order_info']['n_tuple'] ?? [];
                }
            }
        } elseif (isset(self::$_aInfos[0]['order_info']['product_id'])
            && PRODUCT_ID_BUSINESS_FAST_CAR == self::$_aInfos[0]['order_info']['product_id']
        ) {
            foreach (self::$_aInfos as $aInfo) {
                if (CarLevel::DIDI_PUTONG_CAR_LEVEL == $aInfo['order_info']['require_level']
                    && Horae::TYPE_COMBO_CARPOOL == $aInfo['order_info']['combo_type']
                ) {
                    return $aInfo['order_info']['n_tuple'] ?? [];
                }
            }
        } elseif (!isset(self::$_aInfos[0]['order_info']['menu_id'])) {
            return [];
        } elseif (Common::MENU_FLASH == self::$_aInfos[0]['order_info']['menu_id']) {
            foreach (self::$_aInfos as $aInfo) {
                if (CarLevel::DIDI_PUTONG_CAR_LEVEL == $aInfo['order_info']['require_level'] && Horae::TYPE_COMBO_CARPOOL == $aInfo['order_info']['combo_type']) {
                    return $aInfo['order_info']['n_tuple'] ?? [];
                }
            }
        } elseif (Common::MENU_ANYCAR == self::$_aInfos[0]['order_info']['menu_id'] && isset(self::$_aInfos[0]['multi_require_product'])) {
            foreach (self::$_aInfos[0]['multi_require_product'] as $key => $item) {
                if (AnyCarOrderLogic::PRODUCT_KEY_CARPOOL == $key) {
                    return $item;
                }
            }
        }

        return [];
    }

    /**
     * get order info by car level
     * @param string $sCarLevel car level
     * @return array
     */
    public static function getOrderInfoByCarLevel($sCarLevel) {
        if (empty(self::$_aInfos)) {
            return [];
        }

        foreach (self::$_aInfos as $aInfo) {
            if ($aInfo['order_info']['require_level'] == $sCarLevel) {
                return $aInfo['order_info'] ?? [];
            }
        }

        return [];
    }

    // 注释-待删
//    /**
//     * @param int $iProductCategory productCategory
//     * @return array
//     */
//    public static function getBillInfoByProductCategory($iProductCategory) {
//        foreach (self::$_aInfos as $aInfo) {
//            $iProductCategoryItem = ProductCommon::getProductCategory($aInfo['order_info']['n_tuple']);
//            if ($iProductCategoryItem == $iProductCategory) {
//                return $aInfo['bill_info'];
//            }
//        }
//
//        return [];
//    }

    /**
     * @param int $iProductCategory productCategory
     * @return array
     */
    public static function getInfoByProductCategory($iProductCategory) {
        foreach (self::$_aInfos as $aInfo) {
            $iProductCategoryItem = ProductCommon::getProductCategory($aInfo['order_info']['n_tuple']);
            if ($iProductCategoryItem == $iProductCategory) {
                return $aInfo;
            }
        }

        return [];
    }

    /**
     * @desc 获取端上版本
     * @return string
     */
    public static function getAppVersion() {
        if (!empty(self::$_aInfos[0]['common_info']['app_version'])) {
            return self::$_aInfos[0]['common_info']['app_version'];
        }

        return Request::getInstance()->getStr('app_version', '0.0.0');
    }

    /**
     * @Desc 是否是支付宝或微信小程序
     * @return bool
     */
    public static function isApplet() {
        //access_key_id 为9是微信小程序 22是支付宝小程序
        $iAccessKeyId = self::$_aInfos[0]['common_info']['access_key_id'] ?? 0;
        return in_array($iAccessKeyId,[9, 22]);
    }

    /**
     * @Desc 是否是特惠独立小程序
     * @return bool
     */
    public static function isSpecialRate() {
        //access_key_id 为34是特惠独立小程序
        $iAccessKeyId = self::$_aInfos[0]['common_info']['access_key_id'] ?? 0;
        return \BizCommon\Utils\Common::isRegionalWeChatMiniProgram($iAccessKeyId);
    }

    /**
     * @Desc 特惠独立小程序是否需要融合
     * @return bool
     */
    public static function isSpecialRateNeedAggregation() {
        if (self::isSpecialRate() && version_compare(self::getAppVersion(), '6.0.17') >= 0) {
            return true;
        }

        return false;
    }

    /**
     * @param int $iAccessKeyId 是否是native
     * @return bool
     */
    public static function isNativeClient($iAccessKeyId = 0) {
        if (empty($iAccessKeyId)) {
            $iAccessKeyId = self::$_aInfos[0]['common_info']['access_key_id'] ?? 0;
        }

        //access_key_id 为9是微信小程序 22是支付宝小程序
        return in_array($iAccessKeyId,[Common::DIDI_IOS_PASSENGER_APP, Common::DIDI_ANDROID_PASSENGER_APP]);
    }

    /**
     * @Desc 是否是Miniapp
     * @param int $iAccessKeyId $iAccessKeyId
     * @return bool
     */
    public static function isMiniAppClient($iAccessKeyId = 0) {
        if (empty($iAccessKeyId)) {
            $iAccessKeyId = self::$_aInfos[0]['common_info']['access_key_id'] ?? 0;
        }

        //access_key_id 为9是微信小程序 22是支付宝小程序
        return in_array($iAccessKeyId,[Common::DIDI_WECHAT_MINI_PROGRAM, Common::DIDI_ALIPAY_MINI_PROGRAM]);
    }

    /**
     * 判断是否是anycar顶导
     * @return bool
     */
    public static function isAnyCarMenu() {
        $sMenu = self::$_aInfos[0]['order_info']['menu_id'] ?? '';
        if (Common::MENU_ANYCAR == $sMenu) {
            return true;
        }

        return false;
    }

    /**
     * 是否命中拼车多座位预估（一次预估返回1座、2座信息）
     * @param array $aInfo aInfo
     * @return bool
     */
    public static function isCarpoolMultiSeat($aInfo) {
        $oApollo = Apollo::getInstance();
        $aParams = array_merge($aInfo['common_info'], $aInfo['order_info'], $aInfo['order_info']['n_tuple']);
        $aParam  = [
            'app_version'   => $aParams['app_version'],
            'carpool_type'  => $aParams['carpool_type'],
            'access_key_id' => $aParams['access_key_id'],
            'menu_id'       => $aParams['menu_id'],
        ];

        return $oApollo->featureToggle('gs_carpool_multi_seat_open', $aParam)->allow();
    }

    /**
     * 获取乘客端6.0相关配置
     * @param integer $iProductCategory iProductCategory
     * @return array $aAllConfig
     */
    public static function getBasicConfByProductCategory($iProductCategory) {
        $aRet = RuntimeCache::get((int)$iProductCategory);
        if (!empty($aRet)) {
            return $aRet;
        }

        $aAllConfig = self::getConfigsByNSAndCondition(MainDataRepo::APOLLO_CONF_ESTIMATE_FORM_DATA_MATERIEL, ['product_category' => $iProductCategory]);
        RuntimeCache::set($iProductCategory, $aAllConfig);
        return $aAllConfig;
    }

    /**
     * 获取支付方式配置（支持多语言配置的）
     * @param mixed $mPaymentType paymentType
     * @return bool|mixed
     * @throws Exception 异常信息
     */
    public static function getPaymentTypeConfig($mPaymentType) {
        $aRet = RuntimeCache::get($mPaymentType);
        if (!empty($aRet)) {
            return $aRet;
        }

        $aAllConfig = self::getConfigsByNSAndCondition(MainDataRepo::APOLLO_CONF_PAYMENT_TYPE, ['payment_type' => $mPaymentType]);
        RuntimeCache::set($mPaymentType, $aAllConfig);
        return $aAllConfig;
    }

    /**
     * 获取预估分类的配置
     * @return array|bool
     */
    public static function getCategoryConfig() {
        $oConfResult            = Apollo::getInstance()->getConfigsByNamespaceAndConditions(self::APOLLO_CONF_ESTIMATE_FORM_DATA_CATEGORY,[]);
        list($bOk, $aAllConfig) = $oConfResult->getAllConfigData();
        if (!$bOk) {
            return false;
        }

        $aLangConfig = [];
        foreach ($aAllConfig as $sKey => $aConfig) {
            $aKey = explode('_', $sKey);
            if (!is_array($aKey) || 2 != count($aKey)) {
                continue;
            }

            if (empty(self::$_sLang) && Language::ZH_CN == $aKey[1]) {
                $aLangConfig[$aConfig['category_id']] = $aConfig;
                continue;
            }

            if (!empty(self::$_sLang) && self::$_sLang == $aKey[1]) {
                $aLangConfig[$aConfig['category_id']] = $aConfig;
                continue;
            }
        }

        return $aLangConfig;
    }

        /**
     * 获取支付组件中展示的支付方式信息
     * @return mixed
     */
    public static function getPayComponentInfo() {
        $sLang       = self::$_aInfos[0]['common_info']['lang'] ?? Language::ZH_CN;
        $oConfResult = Apollo::getInstance()->getConfigResult(
            self::APOLLO_CONF_PAYMENT_TYPE_COMPONENT,
            'payment_type_list_info' . "_$sLang"
        );
        list($bOk, $aPaymentList) = $oConfResult->getConfig('payment_type_list');
        if (!$bOk) {
            return false;
        }

        array_walk(
            $aPaymentList,
            function (&$aValue, $iIndex) {
                $aValue['sort_score'] = (int)$aValue['sort_score'];
            }
        );
        return $aPaymentList;
    }

    /**
     * 获取Apollo多语言单配置
     * @param string $namespace 命名空间
     * @param array  $condition 条件
     * @return array
     */
    public static function getConfigsByNSAndCondition(string $namespace, array $condition) {
        $oConfResult            = Apollo::getInstance()->getConfigsByNamespaceAndConditions($namespace, $condition);
        list($bOk, $aAllConfig) = $oConfResult->getAllConfigData();
        if (!$bOk) {
            return [];
        }

        $aLangConfig = [];
        foreach ($aAllConfig as $sKey => $aConfig) {
            $aKey = explode('_', $sKey);
            if (!is_array($aKey) || 2 > count($aKey)) {
                continue;
            }

            $aLangConfig[$aKey[1]] = $aConfig;
        }

        return $aLangConfig[Language::getDefaultLanguage()];
    }

    /**
     * getCarpoolDualPriceV2EstimateID
     * 获取预估表单中拼车两口价v2的预估id
     * @return string
     */
    public static function getCarpoolDualPriceEstimateID() {
        foreach (self::$_aInfos as $aInfo) {
            $nTuple = $aInfo['order_info']['n_tuple'] ?? [];
            if (empty($nTuple)) {
                continue;
            }

            $iProductCategory = (new ProductCategory())->getProductCategoryByNTuple($nTuple);
            if (ProductCategory::PRODUCT_CATEGORY_DEFAULT == $iProductCategory) {
                continue;
            }

            if (ProductCategory::PRODUCT_CATEGORY_ANYCAR == $iProductCategory) {
                foreach ($aInfo['multi_require_product'] ?? [] as $aEstimateItem) {
                    if (BizHorae::isCarpoolDualPrice($aEstimateItem)) {
                        return $aEstimateItem['estimate_id'];
                    }
                }
            }

            if (BizHorae::isCarpoolDualPrice($nTuple)) {
                return $aInfo['order_info']['estimate_id'];
            }
        }

        return '';
    }

    /**
     * getFastCarEstimateID
     * 获取预估表单中的快车的预估id
     * @return string
     */
    public static function getFastCarEstimateID() {
        foreach (self::$_aInfos as $aInfo) {
            $nTuple = $aInfo['order_info']['n_tuple'] ?? [];
            if (empty($nTuple)) {
                continue;
            }

            $iProductCategory = (new ProductCategory())->getProductCategoryByNTuple($nTuple);
            if (ProductCategory::PRODUCT_CATEGORY_DEFAULT == $iProductCategory) {
                continue;
            }

            if (ProductCategory::PRODUCT_CATEGORY_ANYCAR == $iProductCategory) {
                foreach ($aInfo['multi_require_product'] ?? [] as $aEstimateItem) {
                    $_iPC = (new ProductCategory())->getProductCategoryByNTuple($aEstimateItem);
                    if (ProductCategory::PRODUCT_CATEGORY_FAST == $_iPC) {
                        return $aEstimateItem['estimate_id'];
                    }
                }
            } elseif (ProductCategory::PRODUCT_CATEGORY_FAST == $iProductCategory) {
                return $aInfo['order_info']['estimate_id'];
            }
        }

        return '';
    }

    /**
     * getPinchecheV2MinusPrice
     * 获取拼成乐v2最低价格
     * @return [boolean, float]
     */
    public static function getPinchecheV2MinusPrice() {
        foreach (self::$_aInfos as $aInfo) {
            $nTuple = $aInfo['order_info']['n_tuple'] ?? [];
            if (empty($nTuple)) {
                continue;
            }

            if (BizHorae::isLowPriceCarpoolV2($nTuple)) {
                $aActivityInfo = $aInfo['activity_info'][0]['carpool_scene_price'] ?? [];
                $ret           = min(array_column($aActivityInfo, 'estimate_fee'));
                return array(true, $ret);
            }
        }

        return array(false, 0);
    }

    // 注释-待删
//    /**
//     * @return bool
//     */
//    public static function existPinchecheV2() {
//        foreach (self::$_aInfos as $aInfo) {
//            $nTuple = $aInfo['order_info']['n_tuple'] ?? [];
//            if (empty($nTuple)) {
//                continue;
//            }
//
//            if (BizHorae::isLowPriceCarpoolV2($nTuple)) {
//                return true;
//            }
//        }
//
//        return false;
//    }

    /**
     * @return bool
     */
    public static function hasCarpoolDualPriceV3() {
        foreach (self::$_aInfos as $aInfo) {
            $nTuple = $aInfo['order_info']['n_tuple'] ?? [];
            if (empty($nTuple)) {
                continue;
            }

            if (BizHorae::isCarpoolUnSuccessFlatPrice($nTuple)) {
                return true;
            }
        }

        return false;
    }

    /**
     * @desc 是否是城际拼车全页面流量
     * @param string $sMenuId   menu_id
     * @param string $sPageType page_type
     * @return bool
     */
    public static function isInterCarpoolPullPage($sMenuId, $sPageType) {
        return \BizLib\Constants\Common::MENU_INTERCITY_CARPOOL == $sMenuId || in_array($sPageType,[Horae::PAGE_TYPE_INTER_CITY, Horae::PAGE_TYPE_INTER_CITY_V2]);
    }

    /**
     * @return bool
     */
    public static function isPinchecheAtGuide() {
        $oDecision      = DecisionLogic::getInstance();
        $aDecisionGuide = $oDecision->getDecisionProductGuideList();
        $iPc            = -1;

        if (!empty($aDecisionGuide)) {
            foreach ($aDecisionGuide as $aOrder) {
                if ($aOrder['is_show']) {
                    $iPc = $aOrder['product_category'];
                    break;
                }
            }
        }

        return ProductCategory::PRODUCT_CATEGORY_LOW_PRICE_CARPOOL == $iPc;
    }

    /**
     * @Desc: 快车预估实付价
     * @return float
     */
    public static function getFastCarEstimateFeeV2() {
        foreach (self::$_aInfos as $aInfo) {
            if (ProductCategory::PRODUCT_CATEGORY_FAST == $aInfo['order_info']['product_category']) {
                return $aInfo['activity_info']['0']['estimate_fee'];
            }
        }

        return 0;
    }

    /**
     * 判定是否两口价V3按照一口价样式显示
     *
     * 当两个价格(拼成, 未拼成) 相等(精度默认1位, 配置)时, 可以展示成一口价
     *
     * @param array $aInfo $aInfo
     * @return boolean
     */
    public static function isCarpoolUnSuccessFlatPriceShowAsCapPrice($aInfo) {
        if (2 != count($aInfo['activity_info'])) {
            return false;
        }

        if (CarpoolCommuteCardLogic::getInstance()->isHaveCardEstimateID($aInfo['bill_info']['estimate_id'])) {
            // 用卡
            return true;
        }

        $fCarpoolFailFee    = $aInfo['activity_info'][1]['estimate_fee'] ?? 0;
        $fCarpoolSuccessFee = $aInfo['activity_info'][0]['estimate_fee'] ?? 0;
        if (empty($fCarpoolFailFee) || empty($fCarpoolSuccessFee)) {
            return false;
        }

        return round($fCarpoolSuccessFee, 1) == round($fCarpoolFailFee, 1);
    }

    /**
     * 查找车大联盟品类项
     * @return array
     */
    public static function getSpaciousCarAlliance() {
        foreach (self::$_aInfos as $aInfo) {
            if (ProductCategory::PRODUCT_CATEGORY_SPACIOUS_CAR == $aInfo['order_info']['product_category']) {
                return $aInfo;
            }
        }

        return [];
    }
}
