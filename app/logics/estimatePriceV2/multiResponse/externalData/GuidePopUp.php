<?php
namespace PreSale\Logics\estimatePrice\multiResponse\external;

use BizLib\Utils\Language;
use BizLib\Utils\ProductCategory;
use Dirpc\SDK\PreSale\GuideInfo;
use BizLib\Constants;
use BizLib\Config as NuwaConfig;

/**
 * Class GuidePopUp
 * @package PreSale\Logics\estimatePrice\multiResponse\external
 */
class GuidePopUp
{
    // 埋点标识
    const DIALOG_ID = '2021_12_3';

    // 普通关闭弹窗
    const DIALOG_CLOSE_TYPE_A = -1;

    // 关闭弹窗，勾选车型(select_product_category)
    const DIALOG_CLOSE_TYPE_B = 0;

    // 123拼车日弹窗
    const POP_UP_123_DAY = 123;

    // 供需失衡
    const SUPPLY_DEMAND_CARPOOL = 124;

    /**
     * 获取弹窗数据
     * @param array $aInfos ...
     * @return array
     */
    public static function get(array $aInfos) {
        // 此弹窗仅support主端
        if (!in_array($aInfos[0]['common_info']['access_key_id'], [Constants\Common::DIDI_IOS_PASSENGER_APP, Constants\Common::DIDI_ANDROID_PASSENGER_APP])) {
            return [];
        }

        if (!is_array($aInfos[0]['athena_anycar_guide'])) {
            return [];
        }

        $iPopType         = 0;
        $iProductCategory = 0;
        foreach ($aInfos[0]['athena_anycar_guide'] as $aGuideRet) {
            if (!empty($aGuideRet->athena_bubble_pop_up) && !empty($aGuideRet->athena_bubble_pop_up->pop_type)) {
                $iPopType         = $aGuideRet->athena_bubble_pop_up->pop_type;
                $iProductCategory = $aGuideRet->athena_bubble_pop_up->product_category;
            }
        }

        if (0 == $iPopType || 0 == $iProductCategory) {
            return [];
        }

        $aText = NuwaConfig::text('config_text', 'guide_pop_up');
        if (empty($aText)) {
            return [];
        }

        $aCurInfo = [];
        foreach ($aInfos as $aInfo) {
            if ($iProductCategory == $aInfo['order_info']['product_category']) {
                $aCurInfo = $aInfo;
            }
        }

        $oPopUp = new GuideInfo();
        switch ($iPopType) {
            case self::POP_UP_123_DAY:
                $aPopText = $aText[$iPopType] ?? [];
                $fAmount  = $aCurInfo['activity_info'][0]['estimate_fee'] ?? 0;
                if (empty($aPopText)) {
                    return [];
                }

                $sTitle = Language::replaceTag($aPopText['title'],['amount' => $fAmount]);
                $oPopUp->setTitle($sTitle)
                    ->setBackgroundUrl($aPopText['bg_url'])
                    ->setLeftButtonText($aPopText['left_btn_text'])
                    ->setRightButtonText($aPopText['right_btn_text'])
                    ->setLinkText($aPopText['link_text'])
                    ->setLinkUrl($aPopText['link_url'])
                    ->setSelectProductCategory($iProductCategory)
                    ->setShowType(1)
                    ->setDialogCloseType(self::DIALOG_CLOSE_TYPE_B)
                    ->setDialogId(self::DIALOG_ID);
                break;
            default:
                break;
        }

        if ('' == $oPopUp->getTitle()) {
            return [];
        }

        return $oPopUp->serializeToJsonArray();
    }
}
