<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\virtualGroupCar;

use BizLib\Utils\ProductCategory;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\estimatePriceV2\multiRequest\OrderInfo;
use PreSale\Logics\estimatePriceV2\multiRequest\ProductGroup;
use PreSale\Logics\estimatePriceV2\multiResponse\component\SortSelection;
use BizLib\Utils\Language;

/**
 * Class VirtualGroupCar
 * @package PreSale\Logics\vitualGroupCar
 */
class VirtualGroupCar
{
    private $_aConfig;
    private static $_oInstance = null;
    private $_aDcmpConfig;
    /**
     * init
     */
    public function __construct() {
        $this->getConfig();
        $this->_aDcmpConfig = Language::getDecodedTextFromDcmp('config_text-virtual_group');
    }

    /**
     * @return VirtualGroupCar
     */
    public static function getInstance() {
        if (null === self::$_oInstance) {
            $kls = __CLASS__;
            self::$_oInstance = new $kls();
        }

        return self::$_oInstance;
    }
    /**
     * @param array $aMultiResponse aMultiResponse
     * @return array
     */
    public function buildProdcut($aMultiResponse) {
        $aEstimateData = $aMultiResponse['data']['estimate_data'];
        $aSubGroups    = [];
        $introMsg      = '';
        $boxLevel      = 0;
        foreach ($aEstimateData as $aData) {
            $fastigiumBoxLevel = ProductGroup::getInstance()->getFastigiumBoxLevel($aData['product_category']);
            if ($fastigiumBoxLevel > 0) {
                $aSubGroups [] = $aData;
                $introMsg      = ProductGroup::getInstance()->getFastigiumBoxText();
                $boxLevel      = $fastigiumBoxLevel;
            }
        }

        if ('' == $introMsg) {
            return $aMultiResponse;
        }

        // 下面foreach逻辑的作用是对每一种需要聚合的车型进行聚合
        $aEstimateData = $this->buildAggregation($aEstimateData, $aSubGroups, $boxLevel);
        $aEstimateData[0]['intro_msg'] = $introMsg;

        $aMultiResponse['data']['estimate_data'] = $aEstimateData;
        return $aMultiResponse;
    }


    /**
     * @param array $aEstimateData $aEstimateData
     * @param array $aSubGroups    $aSubGroups
     * @param int   $boxLevel      $boxLevel
     * @return mixed
     */
    public function buildAggregation($aEstimateData, $aSubGroups, $boxLevel) {
        $aSubProduct = [];
        $maxPrice    = -1;
        $minPrice    = 9999999999;
        foreach ($aSubGroups as $aItem) {
            $atmp = $aItem;
            $atmp['close_checkbox'] = '1';
            $atmp['select_type']    = 0;
            if (ProductCategory::PRODUCT_CATEGORY_PREMIUM_COMFORT == $atmp['product_category'] || ProductCategory::PRODUCT_CATEGORY_APLUS == $atmp['product_category']) {
                $atmp['sub_title_list'] = [];
            }

            if (ProductCategory::PRODUCT_CATEGORY_FAST == $atmp['product_category']) {
                $atmp['link_product'] = [];
            }

            $aSubProduct[] = $atmp;
            $minPrice      = min($atmp['fee_amount'], $minPrice);
            $maxPrice      = max($atmp['fee_amount'], $maxPrice);
        }

        $aThemeData = $this->_buildThemeData();
        $carKinds   = count($aSubProduct);

        $aGroupItem = [
            'group_type'         => 1,
            'form_show_type'     => OrderInfo::FORM_SHOW_TYPE_FIRST,
            'recommend_type'     => SortSelection::DACHE_ANYCAR_ITEM_RECOMMEND,
            'sub_product'        => $aSubProduct,
            'theme_type'         => '3',
            'theme_data'         => $aThemeData,
            'box_id'             => $boxLevel,
            'sub_page_title'     => $this->_aDcmpConfig['sub_page_title'],
            'sub_page_sub_title' => language::replaceTag(
                $this->_aDcmpConfig['sub_page_sub_title'],
                ['nums' => $carKinds]
            ),
            'fee_msg'            => language::replaceTag(
                $this->_aDcmpConfig['fee_msg'],
                ['min' => $minPrice,'max' => $maxPrice]
            ),
        ];
        foreach ($aSubProduct as $product) {
            if (count($product['price_info_desc']) > 0) {
                $aGroupItem['sub_right_title'] = $this->_aDcmpConfig['default_sub_page_title'];
            }
        }

        foreach ($aSubProduct as $product) {
            if (21 == $product['user_pay_info']['payment_id'] || 25 == $product['user_pay_info']['payment_id']) {
                $aGroupItem['sub_right_title'] = $this->_aDcmpConfig['sub_right_title'];
            }
        }

        array_unshift($aEstimateData, $aGroupItem);
        return $aEstimateData;
    }

    /**
     * @return array
     */
    private function _buildThemeData() {
        $themeData = [];
        if (!empty($this->_aConfig)) {
            $themeData = array(
                'title'                 => $this->_aConfig['title'],
                'title_icon'            => $this->_aConfig['title_icon'],
                'right_title'           => $this->_aConfig['sub_title'],
                'right_image'           => $this->_aConfig['right_image'],
                'theme_color'           => $this->_aConfig['theme_color'],
                'selected_bg_gradients' => [
                    $this->_aConfig['selected_bg_gradients']['start_color'],
                    $this->_aConfig['selected_bg_gradients']['end_color'],
                ],
                'right_info_url'        => $this->_aDcmpConfig['right_info_url'],
            );
            $themeData['outer_bg_gradients'] = [
                $this->_aConfig['out_bg_gradients']['end_color'],
                $this->_aConfig['out_bg_gradients']['start_color'],
            ];
        }

        return $themeData;
    }

    /**
     * 初始化配置
     * @return void
     */
    protected function getConfig() {
        list($bThemeOk, $aThemeConfig) = Apollo::getInstance()->getConfigsByNamespace('peak_group_box')->getAllConfigData();
        $this->_aConfig = $aThemeConfig['group_box'];
    }
}
