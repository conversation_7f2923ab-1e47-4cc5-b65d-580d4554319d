<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\virtualGroupCar;

use BizLib\Constants\OrderSystem;
use BizLib\Utils\Language;
use PreSale\Logics\v3Estimate\DecisionV2Service;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TaxiPricingBoxLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutBuilder;

/**
 *  Class TaxiPricingSingle 存放开关,单例的数据
 */
class TaxiPricingSingle
{
    private static $_oInstance = null;

    /**
     * @var null
     */
    public $bIsTaxiBox = false;

    public $aPcIDToSubGroupID = [];

    /**
     * constructor.
     */
    private function __construct(){}

    /**
     * @return TaxiPricingSingle
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * init
     * @param array $aApolloParams
     * @return void
     */
    public function init($aApolloParams) {
        if(TaxiPricingBoxLayout::isPricingBox($aApolloParams)) {
            $this->bIsTaxiBox = true;
            list($this->aPcIDToSubGroupID, ) = DecisionV2Service::getAggConfByGroupId([LayoutBuilder::SUB_GROUP_ID_TAXI_PRICING], $aApolloParams);
        }
    }

    /**
     * @return bool
     */
    public function judgePcIdInBox($iPcId) {
        if (is_array($this->aPcIDToSubGroupID) && $this->aPcIDToSubGroupID[$iPcId] == LayoutBuilder::SUB_GROUP_ID_TAXI_PRICING){
            return true;
        }

        return false;
    }

    /**
     * @return bool
     */
    public function IsPricingBox() {
        return $this->bIsTaxiBox;
    }

    /**
     * @return string
     */
    public function getCarTitle($iPcId) {
        return Language::getDecodedTextFromDcmp('taxi_pricing_box-intro_msg')[$iPcId]['title'];
    }

}