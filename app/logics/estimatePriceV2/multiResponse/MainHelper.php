<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse;

use BizLib\Log;
use Nuwa\ApolloSDK\Apollo;

/**
 * Class MainHelper
 * @package PreSale\Logics\estimatePriceV2\multiResponse
 */
class MainHelper
{
    private static $_bWriteDiffSwitchOpen   = 'undo';  //"undo" 未调用   "open" 打开 "close" 关闭
    private static $_bOpenNewResponseSwitch = 'undo';  //"undo" 未调用   "open" 打开 "close" 关闭
    private static $_bUseNewResponseSwitch  = 'undo';  //"undo" 未调用   "open" 打开 "close" 关闭

    // 注释-待删
//    /**
//     * 查找response里重复的eid 并记录日志
//     * @param array $aResponse aResponse
//     * @param array $aInfos    aInfos
//     * @return void
//     */
//    public static function findDuplicateEid($aResponse, $aInfos) {
//        if (self::_isAbOpen()) {
//            $aEidSet = [];
//            $bHasDup = false;
//            if (!empty($aResponse['data']['estimate_data'])) {
//                foreach ($aResponse['data']['estimate_data'] as $aData) {
//                    if (!empty($aData['estimate_id'])) {
//                        if (isset($aEidSet[$aData['estimate_id']])) {
//                            $bHasDup = true;
//                            break;
//                        } else {
//                            $aEidSet[$aData['estimate_id']] = 1;
//                        }
//                    }
//                }
//
//                if ($bHasDup) {
//                    Log::warning(sprintf('errno:%s|errmsg:%s|aInfos:%s', '600005', 'duplicate eid', json_encode($aInfos)));
//                    Log::warning(sprintf('errno:%s|errmsg:%s|aResponse:%s', '600005', 'duplicate eid', json_encode($aResponse)));
//                }
//            }
//        }
//    }


    /**
     * 开关，用户关闭老流量，使用新流量  老代码（100% --> 0%）
     * @return bool
     */
    public static function useNewResponse($iPhone = 0) {
        return self::_idempotentAb('estimate_use_new_response_switch', self::$_bUseNewResponseSwitch,$iPhone);
    }

    /**
     * 开关，用于上线阶段新代码的放量  新代码（0% -->100%）
     * @return bool
     */
    public static function openNewResponse() {
        // return self::_idempotentAb('estimate_open_new_response_switch', self::$_bOpenNewResponseSwitch);
        return true;
    }

    // 注释-待删
//    /**
//     * response diff
//     * @param array $responseOld responseOld
//     * @param array $responseNew responseNew
//     * @return array
//     */
//    public static function genResponse($responseOld, $responseNew, $aResponseParamsNew) {
//        if (empty($responseOld)) {
//            return $responseNew;
//        }
//
//        //若responseNew为空，则不比diff，防止diff开关打开，引用参数传空会有error
//        if (empty($responseNew)) {
//            return $responseOld;
//        }
//
//        if (self::_isAbOpen()) {
//            $diffResult = self::getResponseDiff($responseOld, $responseNew);
//            if (!empty($diffResult)) {
//                self::_unsetDiffFields($diffResult['diff']);
//                self::_unsetMisMatchedFiled($diffResult['misMatch']['new']);
//                self::_unsetMisMatchedFiled($diffResult['misMatch']['old']);
//                self::_findDataDetailDiff($diffResult['diff']);
//                self::_checkSingleDiff($diffResult, $responseNew, $aResponseParamsNew);
//                if (!empty($diffResult['diff']) || !empty($diffResult['misMatch']['old']) || !empty($diffResult['misMatch']['new'])) {
//                    Log::warning(sprintf('errno:%s|errmsg:%s|param:%s', '600000', 'response has diff', json_encode($diffResult)));
//                }
//            }
//        }
//
//        return $responseOld;
//    }

// 注释-待删
//    /**
//     * 检查单个有estimate_id差别的diff
//     * @param array $aDiff              aDiff
//     * @param array $aResponse          aResponse
//     * @param array $aResponseParamsNew 新的aInfo
//     * @return void
//     */
//    private static function _checkSingleDiff($aDiff, $aResponse, $aResponseParamsNew) {
//        if (!empty($aDiff['diff']['data']['estimate_data'])) {
//            $aDiffData = array_values($aDiff['diff']['data']['estimate_data']);
//            if (1 == count($aDiffData) && !empty($aDiffData[0]['estimate_id'])) {
//                Log::warning(sprintf('errno:%s|errmsg:%s|param:%s', '600006', 'single response diff aResponse', json_encode($aResponse)));
//                Log::warning(sprintf('errno:%s|errmsg:%s|param:%s', '600006', 'single response diff aInfo', json_encode($aResponseParamsNew)));
//            }
//        }
//    }

// 注释-待删
//    /**
//     * @param array $aDiff diff array
//     * @return void
//     */
//    private static function _unsetDiffFields(&$aDiff) {
//        if (!empty($aDiff['data']['estimate_data'])) {
//            $aFilterList = [
//                'operation',
//                'plugin_page_info',
//                'drive_minute',
//                'drive_metre',
//                'product_category',
//            ];
//            foreach ($aDiff['data']['estimate_data'] as &$aData) {
//                foreach ($aFilterList as $sKey) {
//                    if (isset($aData[$sKey])) {
//                        unset($aData[$sKey]);
//                    }
//                }
//            }
//        }
//    }

    // 注释-待删
//    /**
//     * @param array $aDiff diff array
//     * @return void
//     */
//    private static function _unsetMisMatchedFiled(&$aDiff) {
//        if (!empty($aDiff['data']['estimate_data'])) {
//            $aFilterList = ['operation', 'carpool_type'];
//            foreach ($aDiff['data']['estimate_data'] as &$aData) {
//                foreach ($aFilterList as $sKey) {
//                    if (isset($aData[$sKey])) {
//                        unset($aData[$sKey]);
//                    }
//                }
//            }
//        }
//    }

// 注释-待删
//    /**
//     * public log diff
//     * @param array $aOldLog aOldLog
//     * @param array $aNewLog aNewLog
//     * @return void
//     */
//    public static function writePublicLogDiff($aOldLog, $aNewLog) {
//        if (self::_isAbOpen()) {
//            foreach ($aOldLog as $aOld) {
//                $bMatch = false;
//                unset($aOld['time_cost'], $aOld['is_sky_price']);
//                foreach ($aNewLog as $aNew) {
//                    if ($aOld['estimate_id'] == $aNew['estimate_id']) {
//                        unset($aNew['time_cost'], $aNew['is_sky_price']);
//                        $bMatch = true;
//                        $aDiff  = MainHelper::getResponseDiff($aOld, $aNew);
//                        if (!empty($aDiff)) {
//                            Log::warning(sprintf('errno:%s|errmsg:%s|estimate_id:%s|param:%s', '600002', 'public_log_diff', $aOld['estimate_id'], json_encode($aDiff)));
//                        }
//
//                        break;
//                    }
//                }
//
//                if (!$bMatch) {
//                    Log::warning(sprintf('errno:%s|errmsg:%s|param:%s', '600002', 'public log not matched', $aOld['estimate_id']));
//                }
//            }
//        }
//    }

// 注释-待删
//    /**
//     * kafka diff
//     * @param array $aOldLog aOldLog
//     * @param array $aNewLog aNewLog
//     * @return void
//     */
//    public static function writeKafkaDiff($aOldLog, $aNewLog) {
//        if (self::_isAbOpen()) {
//            foreach ($aOldLog as $aOld) {
//                unset($aOld['estimate_fee'], $aOld['n_tuple'], $aOld['create_time'], $aOld['estimate_time_minutes']);
//                $bMatch = false;
//                foreach ($aNewLog as $aNew) {
//                    if ($aOld['estimate_id'] == $aNew['estimate_id']) {
//                        unset($aNew['estimate_fee'], $aNew['n_tuple'], $aNew['create_time'], $aNew['estimate_time_minutes']);
//                        $bMatch = true;
//                        $aDiff  = MainHelper::getResponseDiff($aOld, $aNew);
//                        if (!empty($aDiff)) {
//                            Log::warning(sprintf('errno:%s|errmsg:%s|estimate_id:%s|param:%s', '600003', 'kafka_diff', $aOld['estimate_id'], json_encode($aDiff)));
//                        }
//
//                        break;
//                    }
//                }
//
//                if (!$bMatch) {
//                    Log::warning(sprintf('errno:%s|errmsg:%s|param:%s', '600003', 'kafka not matched', $aOld['estimate_id']));
//                }
//            }
//        }
//    }


// 注释-待删
//    /**
//     * 获取时间片对应运力情况request diff
//     * @param array $aOldParams aOldParams
//     * @param array $aNewParams aNewParams
//     * @return mixed
//     */
//    public static function genRouteAvailableInfoParams($aOldParams, $aNewParams) {
//        if (self::_isAbOpen()) {
//            $aDiff = self::getResponseDiff($aOldParams, $aNewParams);
//            if (!empty($aDiff)) {
//                Log::warning(sprintf('errno:%s|errmsg:%s|param:%s', '600005', 'route_available_info_params_diff', json_encode($aDiff)));
//            }
//        }
//
//        if (!self::useNewResponse()) {
//            return $aOldParams;
//        } else {
//            return $aNewParams;
//        }
//    }

// 注释-待删
//    /**
//     * @param array $aDiff aDiff
//     * @return void
//     */
//    private static function _findDataDetailDiff(&$aDiff) {
//        if (!empty($aDiff['data']['estimate_data'])) {
//            foreach ($aDiff['data']['estimate_data'] as $iIndex => $aData) {
//                if (isset($aData['data_for_detailpage'])) {
//                    $aOldDetail  = json_decode($aData['data_for_detailpage']['old'], true);
//                    $aNewDetail  = json_decode($aData['data_for_detailpage']['new'], true);
//                    $aDetailDiff = self::getResponseDiff($aOldDetail, $aNewDetail);
//                    unset($aDetailDiff['diff']['departure_time']);
//                    unset($aDetailDiff['diff']['fee_detail']);
//                    if (!empty($aDetailDiff['diff']) || !empty($aDetailDiff['misMatch']['old']) || !empty($aDetailDiff['misMatch']['new'])) {
//                        Log::warning(sprintf('errno:%s|errmsg:%s|index:%s|param:%s', '600001', 'data_for_detailpage has diff index', $iIndex, json_encode($aDetailDiff)));
//                    }
//
//                    unset($aDiff['data']['estimate_data'][$iIndex]['data_for_detailpage']);
//                }
//
//                if (empty($aDiff['data']['estimate_data'][$iIndex])) {
//                    unset($aDiff['data']['estimate_data'][$iIndex]);
//                }
//            }
//
//            if (empty($aDiff['data']['estimate_data'])) {
//                unset($aDiff['data']['estimate_data']);
//                if (empty($aDiff['data'])) {
//                    unset($aDiff['data']);
//                }
//            }
//        }
//    }

    /**
     * isAbOpen 会被多个打diff的地方调用，为了保持一直，增加临时变量
     * @return bool
     */
    private static function _isAbOpen() {
        return self::_idempotentAb('estimate_response_write_diff_switch', self::$_bWriteDiffSwitchOpen);
    }

    /**
     * isAbOpen 会被多个打diff的地方调用，为了保持一直，增加临时变量
     * @param string $abName abName
     * @param bool   $bFlag  bFlag
     * @return bool
     */
    private static function _idempotentAb($abName, &$bFlag, $iPhone = 0) {
        if ('open' === $bFlag) {
            return true;
        } elseif ('close' === $bFlag) {
            return false;
        } else {
            $bPluginTestFlag = isset($_SERVER['HTTP_RESPONSE_FORMAT_TEST_FLAG']);
            $iCityId         = $_SERVER['HTTP_CITYID'];
            $sDeviceId       = $_REQUEST['dviceid'];
            $host            = self::getHost();
            $aKeys           = [
                'key'        => mt_rand(1, 1000),
                'host'       => $host,
                'city'       => $iCityId,
                'device_id'  => $sDeviceId,
                'phone'      => $iPhone,
                'controller' => self::getController(),
            ];
            if ($bPluginTestFlag || Apollo::getInstance()->featureToggle($abName, $aKeys)->allow()) {
                $bFlag = 'open';
                return true;
            }

            $bFlag = 'close';
            return false;
        }
    }

    /**
     * @return string
     */
    public static function getController() {
        $request = \Nuwa\Core\Dispatcher::getInstance()->getRequest();
        return $request->getControllerName();
    }


    /**
     * @return string
     */
    public static function getHost() {
        $host = '';
        if (function_exists('gethostname') && gethostname() !== false) {
            $host = gethostname();
        }

        return $host;
    }

    // 注释-待删
//    /**
//     * @param array $responseOld responseOld
//     * @param array $responseNew responseNew
//     * @return array|string
//     */
//    public static function getResponseDiff($responseOld, $responseNew) {
//        if (empty($responseNew)) {
//            return 'new response is empty';
//        }
//
//        $aDiff = $aMisMatch = [];
//        self::arrayRecursiveDiff($responseOld, $responseNew, $aDiff, $aMisMatch['old']);
//        self::arrayRecursiveDiff($responseNew, $responseOld, $aDiff, $aMisMatch['new']);
//        if (!empty($aDiff) || !empty($aMisMatch['old']) || !empty($aMisMatch['new'])) {
//            return [
//                'diff'     => $aDiff,
//                'misMatch' => $aMisMatch,
//            ];
//        }
//
//        return [];
//    }

// 注释-待删
//    /**
//     * @param array $aArray1   aArray1
//     * @param array $aArray2   aArray2
//     * @param array $aDiff     aDiff
//     * @param array $aMisMatch aMisMatch
//     * @return void
//     */
//    public static function arrayRecursiveDiff($aArray1, $aArray2, &$aDiff, &$aMisMatch) {
//        if (is_object($aArray1)) {
//            $aArray1 = (array)$aArray1;
//        }
//
//        if (is_object($aArray2)) {
//            $aArray2 = (array)$aArray2;
//        }
//
//        foreach ($aArray1 as $mKey => $mValue) {
//            if ('show_h5' === $mKey || 'confirm_h5' === $mKey) {
//                continue;
//            }
//
//            if (array_key_exists($mKey, $aArray2)) {
//                if (is_array($mValue)) {
//                    self::arrayRecursiveDiff($mValue, $aArray2[$mKey], $aDiff[$mKey], $aMisMatch[$mKey]);
//                    if (empty($aDiff[$mKey])) {
//                        unset($aDiff[$mKey]);
//                    }
//
//                    if (empty($aMisMatch[$mKey])) {
//                        unset($aMisMatch[$mKey]);
//                    }
//                } else {
//                    if ($mValue != $aArray2[$mKey] && !isset($aDiff[$mKey])) {
//                        $aDiff[$mKey]['old'] = $mValue;
//                        $aDiff[$mKey]['new'] = $aArray2[$mKey];
//                    }
//                }
//            } else {
//                $aMisMatch[$mKey] = $mValue;
//            }
//        }
//    }


    /**
     * 格式化处理支付组件信息：排序、增加扩展信息
     * @param array $aPayListInfo $aPayListInfo
     * @return array
     */
    public static function formatPayInfoComponent(array $aPayListInfo) {
        $aPayComponentInfo = MainDataRepo::getPayComponentInfo();
        $aSortOrder        = [];
        foreach ($aPayComponentInfo as $aPay) {
            $aSortOrder[] = $aPay['sort_score'];
        }

        array_multisort($aSortOrder, SORT_ASC, $aPayComponentInfo);

        $aSortedPayInfos = [];
        foreach ($aPayComponentInfo as $aComponentItem) {
            $aPayInfo = $aPayListInfo[$aComponentItem['payment_type']] ?? [];
            if (!empty($aPayInfo)) {
                $aPayInfo['car_support_desc'] = $aComponentItem['car_support_desc'];
                // 亲友支付方式不可用，组件中对应选项被置灰，展示收银返回的不可用原因
                if (100 == $aPayInfo['tag'] && !empty($aPayInfo['disabled'])) {
                    $aPayInfo['car_support_desc'] = $aPayInfo['show_msg'];
                }

                $aSortedPayInfos[] = $aPayInfo;
            }
        }

        return $aSortedPayInfos;
    }
}


