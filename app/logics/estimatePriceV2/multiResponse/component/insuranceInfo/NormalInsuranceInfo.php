<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\insuranceInfo;

use BizLib\Utils;
use BizLib\Utils\UtilHelper;
use BizLib\Config as NuwaConfig;

/**
 * Class NormalInsuranceInfo
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\insuranceInfo
 */
class NormalInsuranceInfo
{
    protected $_aInfo;

    protected $_sCarLevel;

    /**
     * NormalInsuranceInfo constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo     = $aInfo;
        $this->_sCarLevel = $aInfo['order_info']['require_level'];
    }

    /**
     * 构建保险信息
     * @param array $aResponse aResponse
     * @return mixed
     */
    public function build($aResponse) {
        $aInsuranceInfo = $this->_getInsuranceInfo();
        return $this->_format($aInsuranceInfo, $aResponse);
    }

    /**
     * 获取保险信息
     * @return array
     */
    protected function _getInsuranceInfo() {
        $aInsuranceInfo = [];
        //包车组装信息
        if (Utils\Product::isCharteredCarByProductId($this->_aInfo['order_info']['product_id'])) {
            //保险信息
            $aInsuranceInfo['need_verified']           = 1;
            $aInsuranceInfo['need_accident_insurance'] = 1;
            $iInsuranceSelectMax = NuwaConfig::config('config_insurance', 'insurance_select_max');
            $aInsuranceInfo['insurance_select_max'] = $iInsuranceSelectMax;
            $aInsuranceInfo['insurance_url']        = UtilHelper::getConfigUrl('chartered_inter_insurance_url');
            $aInsuranceInfo['insurance_msg']        = NuwaConfig::text('config_text', 'chartered_inter_insurance_msg');
            $aInsuranceInfo['is_insurance_enable']  = 0;
            $aCharteredInterInsuranceTitle          = NuwaConfig::text('config_text', 'chartered_inter_insurance_title');
            if (empty($this->_aInfo['order_info']['inter_insurance_info'])) {
                $aInsuranceInfo['insurance_title'] = $aCharteredInterInsuranceTitle['no_passenger'];
            } else {
                $aInsuranceInfo['insurance_title'] = sprintf(
                    $aCharteredInterInsuranceTitle['have_passenger'],
                    count($this->_aInfo['order_info']['inter_insurance_info'])
                );
                foreach ($this->_aInfo['order_info']['inter_insurance_info'] as $value) {
                    $bAdult = Utils\Verified::checkAdult($this->_aInfo['passenger_info']['pid'], $value);
                    if ($bAdult) {
                        $aInsuranceInfo['is_insurance_enable'] = 1; //设置用户满足投保条件
                        break;
                    }
                }
            }
        }

        return $aInsuranceInfo;
    }


    /**
     * 格式化
     * @param array $aInsuranceInfo aInsuranceInfo
     * @param array $aResponse      aResponse
     * @return mixed
     */
    protected function _format($aInsuranceInfo, $aResponse) {
        foreach ($aInsuranceInfo as $sKey => $value) {
            $aResponse[$sKey] = $value;
        }

        return $aResponse;
    }
}
