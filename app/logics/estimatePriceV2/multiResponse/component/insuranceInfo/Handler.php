<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\insuranceInfo;

use PreSale\Logics\estimatePriceV2\multiResponse\Util;

/**
 * Class Handler
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\insuranceInfo
 */
class Handler
{
    /**
     * @param array $aInfo aInfo
     * @return CarpoolInsuranceInfo|NormalInsuranceInfo
     */
    public static function select($aInfo) {
        if (Util::isCarpool($aInfo)) {
            return new CarpoolInsuranceInfo($aInfo);
        }

        return new NormalInsuranceInfo($aInfo);
    }
}
