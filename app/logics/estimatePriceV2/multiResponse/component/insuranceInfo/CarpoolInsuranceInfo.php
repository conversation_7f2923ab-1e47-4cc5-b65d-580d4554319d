<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\insuranceInfo;

use BizLib\Utils\Horae;
use BizLib\Utils\CarLevel;
use BizLib\Utils\UtilHelper;
use BizLib\Utils\NumberHelper;
use BizLib\Config as NuwaConfig;
use PreSale\Logics\estimatePriceV2\params\SceneParamsLogic;

/**
 * Class CarpoolInsuranceInfo
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\insuranceInfo
 */
class CarpoolInsuranceInfo
{
    protected $_aInfo;

    private $_bInsuranceRoute = 0; //是否投保路线

    private $_bAdultExist = 0; //是否存在成年人

    /**
     * CarpoolInsuranceInfo constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
        $this->setInsuranceFeature();
    }

    /**
     * @param array $aResponse aResponse
     * @return array
     */
    public function build($aResponse) {
        if (empty($this->_aInfo['bill_info'])) {
            return $aResponse;
        }

        $iCarLevel  = isset($this->_aInfo['order_info']['require_level']) ? $this->_aInfo['order_info']['require_level'] : CarLevel::DIDI_PUTONG_CAR_LEVEL;
        $iComboType = $this->_aInfo['bill_info']['product_infos'][$iCarLevel]['combo_type'];
        if (!Horae::isInterCityCarPoolScene($iComboType)) {
            return $aResponse;
        }

        //组装城际保险
        return $this->_formatInterCityInsuranceResponse($this->_aInfo, $aResponse);
    }

    /**
     * 组装城际保险信息
     * @param array $aInfo     aInfo
     * @param array $aResponse aResponse
     * @return array
     */
    private function _formatInterCityInsuranceResponse($aInfo, $aResponse) {
        $aInterConf = NuwaConfig::text('config_carpool', 'inter_insurance_msg');
        $sInterUrl  = UtilHelper::getConfigUrl('inter_insurance_url');
        //是否支持实名保险 迁移配置化
        if (!SceneParamsLogic::getInstance()->isFromInterCityCarpoolEntry()) { //非跨城拼车入口
            return $aResponse;
        }

        if (empty($aInfo['bill_info']['route_info'])) {
            return $aResponse;
        }

        $sCarLevel = $aInfo['order_info']['require_level'];

        $aResponse['need_verified']           = $aInfo['bill_info']['route_info']['need_verified'] ?? 0;
        $aResponse['need_accident_insurance'] = $this->_bInsuranceRoute;
        $aResponse['is_insurance_enable']     = $this->_bAdultExist;
        if ($this->_bInsuranceRoute) {
            if (!$aInfo['order_info']['is_select_insurance']) {
                if (!empty($aInfo['bill_info']['bills'][$sCarLevel]['insure_unit_price'])) {
                    $aValue['insurance_msg'] = sprintf(
                        $aInterConf[0],
                        NumberHelper::numberFormatDisplay($aInfo['bill_info']['bills'][$sCarLevel]['insure_unit_price'])
                    );
                }
            } else {
                if (!empty($aInfo['bill_info']['bills'][$sCarLevel]['insure_unit_price'])
                    && !empty($aInfo['bill_info']['bills'][$sCarLevel]['insure_total_fee'])
                ) {
                    $aValue['insurance_msg'] = sprintf(
                        $aInterConf[1],
                        NumberHelper::numberFormatDisplay($aInfo['bill_info']['bills'][$sCarLevel]['insure_unit_price']),
                        NumberHelper::numberFormatDisplay($aInfo['bill_info']['bills'][$sCarLevel]['insure_total_fee'])
                    );
                }
            }

            $aResponse['insurance_url'] = $sInterUrl;
        }

        return $aResponse;
    }


    /**
     * 跨城拼车是否保险路线 和 是否允许保险判断
     * @return null
     */
    public function setInsuranceFeature() {
        if (!SceneParamsLogic::getInstance()->isFromInterCityCarpoolEntry()) {
            return false;
        }

        if (empty($this->_aInfo['bill_info']['route_info']['need_accident_insurance'])) {
            return false;
        }

        //设置路线投保特征
        $this->_bInsuranceRoute = 1;

        if (empty($aInfo['order_info']['inter_insurance_info'])) {
            return false;
        }

        $aInsuranceInfo = $aInfo['order_info']['inter_insurance_info'];
        foreach ($aInsuranceInfo as $value) {
            $bAdult = \BizLib\Utils\Verified::checkAdult($aInfo['passenger_info']['pid'], $value);
            if ($bAdult) {
                $this->_bAdultExist = 1; //设置用户满足投保条件
                break;
            }
        }
    }
}
