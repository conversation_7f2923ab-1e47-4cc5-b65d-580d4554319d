<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\component;

use BizLib\Constants\OrderSystem;
use BizLib\Libraries\RedisDB;
use BizLib\Utils\Common;
use BizLib\Utils\Language;
use PreSale\Logics\carpool\CarpoolCommuteCard;
use PreSale\Logics\estimatePriceV2\DecisionLogic;
use BizCommon\Utils\Horae as HoraeUtils;
use PreSale\Logics\estimatePriceV2\response\SceneResponseLogicV2;
use PreSale\Logics\estimatePriceV2\multiResponse\component\LowPriceCarpoolGuide;

/**
 * Class GuideInfo
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component
 */
class GuideInfo
{
    /**
     * @var self
     */
    private static $_oInstance;

    /**
     * @var array
     */
    private $_aInfoList;

    /**
     * @var null|DecisionLogic
     */
    private $_oDecision;

    /**
     * @var array
     */
    private $_aAthenaOriginInfo = array();

    /**
     * @var bool
     */
    private $_bHasAthenaInfo = false;

    /**
     * @var array
     */
    private $_aGuideInfo;

    /**
     * GuideInfo constructor.
     */
    private function __construct() {
        $this->_oDecision = DecisionLogic::getInstance();
    }

    /**
     * @param array $aInfoList aInfo列表
     * @return void
     */
    public function loadInfo($aInfoList) {
        $this->_aInfoList = $aInfoList;
        //build athena 返回的guide_result
        foreach ($aInfoList as $aInfo) {
            if (!empty($aInfo['athena_info'])) {
                $this->_aAthenaOriginInfo = json_decode($aInfo['athena_info'], true);
                break;
            }
        }
    }

    /**
     * @return null|GuideInfo
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * @param array $aResponse aResponse
     * @return mixed
     */
    public function getGuideInfo($aResponse) {
        //dds guide info
        if ($this->_oDecision->getDecisionSortToggleResult()) {
            $aResponse['data']['guide_info'] = $this->_oDecision->getGuideInfo();
        }

        return $aResponse;
    }

    /**
     * @param array $aResponse aResponse
     * @return mixed
     */
    public function getAthenaInfo($aResponse) {
        // data为空直接返回
        if (empty(($aResponse) || empty($aResponse['data']))) {
            return $aResponse;
        }

        $aInfo = current($this->_aInfoList);
        $aResponse['data']['athena_info'] = $aInfo['athena_info'];

        //校验一下Apollo
        if ($this->_checkDegrade()) {
            return $aResponse;
        }

        $aDefaultBizInfo       = array();
        $this->_bHasAthenaInfo = false;
        foreach ($aResponse['data']['estimate_data'] as &$aEstimateItem) {
            if (1 != $aEstimateItem['is_default']) {
                continue;
            }

            $aDefaultBizInfo['business_id']   = $aEstimateItem['business_id'];
            $aDefaultBizInfo['product_id']    = \BizLib\Utils\Product::getProductIdByBusinessId($aEstimateItem['business_id']);
            $aDefaultBizInfo['require_level'] = $aEstimateItem['require_level'];
            $aDefaultBizInfo['combo_type']    = $aEstimateItem['combo_type'];

            //动调气泡
            $aDynamicPriceBubbleInfo = $this->_buildDynamicPriceBubble($aDefaultBizInfo, $aEstimateItem, $this->_aInfoList);
            if (!empty($aDynamicPriceBubbleInfo)) {
                $this->_bHasAthenaInfo            = true;
                $aEstimateItem['athena_info']     = json_encode($aDynamicPriceBubbleInfo);
                $aResponse['data']['athena_info'] = '';
                return $aResponse;
            }

            $aLowPriceAthenaInfo = [];
            if (!empty($aLowPriceAthenaInfo)
                && (empty($this->_aAthenaOriginInfo)
                || 0 != $this->_aAthenaOriginInfo['errno']
                || empty($this->_aAthenaOriginInfo['data']))
            ) {
                $aEstimateItem['athena_info'] = json_encode($aLowPriceAthenaInfo);
                $this->_bHasAthenaInfo        = true;
                return $aResponse;
            }

            // anycar默认选中气泡优先级高
            if (\BizLib\Utils\Product::isAnyCar($aDefaultBizInfo['product_id'])) {
                $aAnycarAthenaInfo = $this->_buildAnycarBubble($aDefaultBizInfo, $this->_aInfoList);
                if (!empty($aAnycarAthenaInfo)) {
                    $aDDSAthenaInfo = $aAnycarAthenaInfo;
                }
            }

            if (empty($aDDSAthenaInfo)) {
                $aDDSAthenaInfo = $this->_oDecision->getDDSAthenaInfo($aDefaultBizInfo);
            }

            if (empty($aDDSAthenaInfo)) {
                $aDDSAthenaInfo = $this->_buildCarpoolCommuteBubble($aDefaultBizInfo, $this->_aInfoList);
            }

            $aDDSSelectedItem = $this->_oDecision->getDecisionSelectedItem();
            $bDDSAthenaFlag   = $aDDSAthenaInfo['bubble_flag'];
            unset($aDDSAthenaInfo['bubble_flag']);
            if (!empty($aDDSAthenaInfo) && 0 == $aDDSAthenaInfo['errno']) {
                $aEstimateItem['athena_info'] = json_encode($aDDSAthenaInfo);
                $this->_bHasAthenaInfo        = true;
                if (!empty($aDDSSelectedItem) && HoraeUtils::isCarpoolFlatRate($aDDSSelectedItem)) {
                    $aResponse['data']['athena_info'] = '';
                    return $aResponse;
                }
            }

            // 如果有原始athena_info，且不是强制使用DDS返回的气泡，则用原始的值。
            if (!$bDDSAthenaFlag
                && !empty($this->_aAthenaOriginInfo)
                && 0 == $this->_aAthenaOriginInfo['errno']
                && !empty($this->_aAthenaOriginInfo['data'])
                && $this->_bDefaultInFromList($aDefaultBizInfo, $this->_aAthenaOriginInfo)
            ) {
                $aEstimateItem['athena_info']     = json_encode($this->_aAthenaOriginInfo, JSON_UNESCAPED_UNICODE);
                $aResponse['data']['athena_info'] = '';
                $this->_bHasAthenaInfo            = true;
                return $aResponse;
            }

            //如果命中拼车通勤卡的气泡，则['data']['athena_info']为空，拼车通勤卡气泡的优先级最低
            if ((new CarpoolCommuteCard())->getCommuteBubbleReturnFlag($aDefaultBizInfo['combo_type'], $aDefaultBizInfo['require_level'], $aDefaultBizInfo['product_id'])) {
                $aResponse['data']['athena_info'] = '';
                return $aResponse;
            }
        }

        //通勤卡送卡和卖卡决策
        SceneResponseLogicV2::getInstance()->buildCommuteCardSendOrSale($this->_aInfoList, $aResponse, $this->_bHasAthenaInfo);
        return $aResponse;

    }

    // 注释-待删
//    /**
//     * @param array $aResponse aResponse
//     * @return mixed
//     */
//    public function getGuideWebToNative($aResponse) {
//        foreach ($this->_aInfoList as $aInfo) {
//           // //webapp倒流到app
//           // $apolloV2 = new \Xiaoju\Apollo\Apollo();
//           // if ($apolloV2->featureToggle(
//               // 'gs_huichao_prime_passage',
//               // array(
//                   // 'key'   => $aInfo['passenger_info']['pid'],
//                   // 'city'  => $aInfo['order_info']['area'],
//                   // 'phone' => $aInfo['passenger_info']['phone'],
//               // )
//           // )->allow()
//           // ) {
//               // if (OrderSystem::PRODUCT_ID_FAST_CAR == $aInfo['order_info']['product_id']
//                   // && $aInfo['common_info']['is_from_webapp']
//               // ) {
//                   // $sLocLang      = Language::getPassengerLanguage($aInfo['passenger_info']['pid'], $aInfo['order_info']['district']);
//                   // $oMemberClient = new \BizLib\Client\MemberSystemClient();
//                   // $aMember       = $oMemberClient->queryInfo(
//                       // $aInfo['order_info']['product_id'],
//                       // $aInfo['passenger_info']['pid'],
//                       // $sLocLang,
//                       // false,
//                       // [
//                           // 'city_id'      => $aInfo['order_info']['area'],
//                           // 'carpool_type' => 0,
//                           // 'call_car'     => 0,
//                           // 'client_type'  => 1,
//                       // ]
//                   // );
//                   // $iLevelID      = $aMember['level_id'];
//                   // $iRemainTimes  = (int)($aMember['privileges']['fast_way']['backend']['remain_times']);
//                   // if (0 != $iRemainTimes) {
//                       // $aResponse['data']['guide_web_to_native'] = $iLevelID;
//                   // }
//               // }
//           // }
//        }
//
//        return $aResponse;
//    }


    /**
     * @return bool
     */
    private function _checkDegrade() {
        // 小于5.2.20,旧的导流交互模式
        $aInfo = current($this->_aInfoList);
        if (empty($aInfo) || empty($aInfo['order_info']) || !empty($aInfo['order_info']['order_type'])) {
            return true;
        }

        if (version_compare($aInfo['common_info']['app_version'], '5.2.22') < 0) {
            return true;
        }

        return false;
    }

    /**
     * 构建拼车通勤卡的气泡
     * @param array $aDefaultBizInfo aDefaultBizInfo
     * @param array $aInfos          aInfos
     * @return array
     */
    private function _buildCarpoolCommuteBubble($aDefaultBizInfo, $aInfos) {
        //组装$aExtInfo
        foreach ($aInfos as $aInfoItem) {
            $iComboType        = $aInfoItem['order_info']['combo_type'];
            $sCarLevel         = $aInfoItem['order_info']['require_level'];
            $iProductId        = $aInfoItem['order_info']['product_id'];
            $iCarpoolComboType = isset($aInfoItem['bill_info']['product_infos'][$sCarLevel]['combo_type']) ? $aInfoItem['bill_info']['product_infos'][$sCarLevel]['combo_type'] : $iComboType;

            if ($aDefaultBizInfo['combo_type'] != $iCarpoolComboType || $aDefaultBizInfo['require_level'] != $sCarLevel
                || $aDefaultBizInfo['product_id'] != $iProductId
            ) {
                continue;
            }

            //如果$bCommuteCardBubbleReturn不为true，则代表不需要展示气泡
            $aRetCheckShowH5OrBubble = (new CarpoolCommuteCard())->checkShowH5OrBubbleV2($aInfoItem);
            if (!$aRetCheckShowH5OrBubble['b_commute_card_bubble_return']) {
                return [];
            }

            $aExtInfo = $this->_formatExtraInfo($aInfoItem);
            break;
        }

        if (empty($aExtInfo)) {
            return [];
        }

        $aGuideInfo[] = $this->_oDecision->buildGuideInfo($aDefaultBizInfo, 'carpool_commute', $aExtInfo);
        if (empty($aGuideInfo)) {
            return [];
        }

        $aAthenaInfo = array(
            'errno'  => 0,
            'errmsg' => 'ok',
            'data'   => array('guide' => $aGuideInfo,),
        );

        return $aAthenaInfo;
    }

    /**
     * 构建anycar推荐气泡
     *
     * @param array $aDefaultBizInfo 选中业务线信息
     * @param array $aInfos          基本信息
     * @return array
     */
    private function _buildAnycarBubble($aDefaultBizInfo, $aInfos) {
        if (!\BizLib\Utils\Product::isAnyCar($aDefaultBizInfo['product_id'])) {
            return [];
        }

        foreach ($aInfos as $aInfo) {
            if ($aDefaultBizInfo['product_id'] == $aInfo['order_info']['product_id']
                && $aDefaultBizInfo['require_level'] == $aInfo['order_info']['require_level']
                && $aDefaultBizInfo['combo_type'] == $aInfo['order_info']['combo_type']
            ) {
                break;
            }
        }

        $aMultiRequireProduct = $aInfo['multi_require_product'] ?? [];
        $sMenuId = $aInfo['order_info']['menu_id'] ?? '';
        if (\BizLib\Constants\Common::MENU_FLASH != $sMenuId && \BizLib\Constants\Common::MENU_ANYCAR != $sMenuId) {
            return [];
        }

        $aCarNameConfig  = json_decode(Language::getTextFromDcmp('config_anycar-anycar_simple_name'), true);
        $aSelectCarNames = $aRecommendCarNames = [];
        foreach ($aMultiRequireProduct as $aProduct) {
            $sGroupKey = implode('_', [$aProduct['product_id'], $aProduct['require_level'], $aProduct['combo_type']]);
            $sCarName  = $aCarNameConfig[$sGroupKey] ?? '';
            if (empty($sCarName)) {
                continue;
            }

            if ($aProduct['is_recommend']) {
                $aRecommendCarNames[] = $sCarName;
            }

            if ($aProduct['is_selected']) {
                $aSelectCarNames[] = $sCarName;
            }
        }

        // 没有推荐，不出提示
        if (empty($aRecommendCarNames)) {
            return [];
        }

        $aDefaultBizInfo['bubble_flag'] = true; // anycar推荐默认选中子车型，强制出
        $aDefaultBizInfo['show_type']   = DecisionLogic::TYPE_ANYCAR_RECOMMEND_BUBBLE;
        if (\BizLib\Constants\Common::MENU_ANYCAR == $sMenuId) {
            $aDefaultBizInfo['bubble_text'] = Language::getTextFromDcmp('config_anycar-recommend_more_car', ['car_names' => implode('、', $aSelectCarNames), 'recommend_car_names' => implode('、', $aRecommendCarNames)]);
        } else {
            $aDefaultBizInfo['bubble_text'] = Language::getTextFromDcmp('config_anycar-recommend_more_car_with_name', ['car_names' => implode('、', $aSelectCarNames), 'recommend_car_names' => implode('、', $aRecommendCarNames)]);
        }

        $aGuideInfo[] = $this->_oDecision->buildGuideInfo($aDefaultBizInfo);

        $aAthenaInfo = array(
            'errno'  => 0,
            'errmsg' => 'ok',
            'data'   => array('guide' => $aGuideInfo,),
        );
        return $aAthenaInfo;
    }

    /**
     * 构建动调提示气泡
     *
     * @param array $aDefaultBizInfo 选中业务线信息
     * @param array $aEstiamteInfo   预估信息
     * @param array $aInfos          基本信息
     * @return array
     */
    private function _buildDynamicPriceBubble($aDefaultBizInfo, $aEstiamteInfo, $aInfos) {
        return [];
    }

    /**
     * @param array $aInfoItem aInfoItem
     * @return mixed
     */
    private function _formatExtraInfo($aInfoItem) {
        $sCarLevel = $aInfoItem['order_info']['require_level'];
        $aExtInfo['commute_card_info'] = $aInfoItem['order_info']['carpool_commute_info']['commute_card_info'];
        $aExtInfo['city_id']           = $aInfoItem['order_info']['area'];
        $aExtInfo['product_id']        = $aInfoItem['order_info']['product_id'];
        $aExtInfo['carpool_bill_info'] = $aInfoItem['bill_info']['bills'][$sCarLevel];
        $aExtInfo['from_name']         = $aInfoItem['order_info']['from_name'];
        $aExtInfo['to_name']           = $aInfoItem['order_info']['to_name'];

        return $aExtInfo;
    }

    /**
     * @param array $aDefaultBizInfo aDefaultBizInfo
     * @param array $aAthenaInfo     aAthenaInfo
     * @return bool
     */
    private function _bDefaultInFromList($aDefaultBizInfo, $aAthenaInfo) {
        if (empty($aAthenaInfo) || empty($aAthenaInfo['data']) || empty($aAthenaInfo['data']['guide']) || empty($aAthenaInfo['data']['guide'][0]) || empty($aAthenaInfo['data']['guide'][0]['from'])) {
            return false;
        }

        $aFromList = $aAthenaInfo['data']['guide'][0]['from'];
        foreach ($aFromList as $aFromItem) {
            if ($aFromItem['source_product'] != $aDefaultBizInfo['business_id']
                || $aFromItem['source_combo_type'] != $aDefaultBizInfo['combo_type']
                || $aFromItem['source_level'] != $aDefaultBizInfo['require_level']
            ) {
                continue;
            }

            return true;
        }

        return false;
    }

    /**
     * 获取推荐位品类信息
     * @return array|mixed
     */
    public function getGuideProductInfo() {
        if (isset($this->_aGuideInfo)) {
            return $this->_aGuideInfo;
        }

        $aDecisionGuide = DecisionLogic::getInstance()->getDecisionProductGuideList();
        if (!empty($aDecisionGuide)) {
            foreach ($aDecisionGuide as $aOrder) {
                foreach ($this->_aInfoList as $aInfo) {
                    if ($aInfo['order_info']['product_category'] == $aOrder['product_category'] && true == $aOrder['is_show']) {
                        $this->_aGuideInfo = $aInfo;
                        return $aInfo;
                    }
                }
            }

            $this->_aGuideInfo = array();
        }

        return $this->_aGuideInfo;
    }
}
