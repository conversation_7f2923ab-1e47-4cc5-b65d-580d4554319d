<?php
/**
 * Created by PhpStorm.
 * <AUTHOR> <<EMAIL>>
 * Date: 2020/4/30
 * Time: 11:09 AM
 */

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\autoDrivingAddressInfo;

use PreSale\Logics\estimatePriceV2\multiResponse\Util;
use BizCommon\Utils\Horae;

/**
 * Class Handler
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\autoDrivingAddressInfo
 */
class Handler
{
    /**
     * @param array $aInfo 参数
     * @return AutoDrivingAddressInfo|NormalDrivingAddressInfo
     */
    public static function select($aInfo) {
        if (Horae::isAutoDriving($aInfo['order_info'])) {
            return new AutoDrivingAddressInfo($aInfo);
        }

        return null;
    }
}
