<?php
/**
 * Created by PhpStorm.
 * <AUTHOR> <<EMAIL>>
 * Date: 2020/4/30
 * Time: 2:34 PM
 */

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\autoDrivingAddressInfo;

use PreSale\Logics\estimatePriceV2\multiRequest\OrderInfo;
use BizCommon\Utils\Horae;
use PreSale\Logics\estimatePriceV2\DecisionLogic;
use BizLib\Log as NuwaLog;

/**
 * Class AutoDrivingAddressInfo
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\autoDrivingAddressInfo
 */
class AutoDrivingAddressInfo
{
    private $_oDecision;
    private $_aInfo;
    /**
     * CarpoolDepartTag constructor.
     * @param array $aInfo ainfo
     */
    public function __construct($aInfo) {
        $this->_aInfo     = $aInfo;
        $this->_oDecision = DecisionLogic::getInstance();
    }

    /**
     * @param array $aResponse response
     * @return mixed
     */
    public function build($aResponse) {
        if (OrderInfo::FORM_SHOW_TYPE_GUIDE !== $this->_aInfo['order_info']['form_show_type']) {
            return $aResponse;
        }

        $aDecisionGuide = $this->_oDecision->getDecisionProductGuideList();
        if (!empty($aDecisionGuide)) {
            foreach ($aDecisionGuide as $aOrder) {
                if (Horae::isAutoDriving($aOrder) && isset($aOrder['extra_info']['redirection_info'])) {
                    $aResponse['auto_driving_address_info'] = json_decode($aOrder['extra_info']['redirection_info'],true);
                    return $aResponse;
                }
            }
        }

        return $aResponse;
    }
}
