<?php
/**
 * Created by PhpStorm.
 * <AUTHOR> <<EMAIL>>
 * Date: 2020/4/30
 * Time: 2:35 PM
 */

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\autoDrivingAddressInfo;

/**
 * Class NormalDrivingAddressInfo
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\autoDrivingAddressInfo
 */
class NormalDrivingAddressInfo
{
    /**
     * CarpoolDepartTag constructor.
     * @param array $aInfo ainfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
    }

    /**
     * @param array $aResponse response
     * @return mixed
     */
    public function build($aResponse) {
        return $aResponse;
    }
}
