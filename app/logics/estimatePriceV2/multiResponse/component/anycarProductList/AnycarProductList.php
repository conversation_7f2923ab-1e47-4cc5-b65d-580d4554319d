<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\anycarProductList;

use B<PERSON>lib\Utils;
use BizLib\Constants;
use BizLib\Utils\Product;
use BizLib\Utils\Language;
use Nuwa\ApolloSDK\Apollo;
use BizLib\Libraries\RedisDB;
use PreSale\Logics\order\AnyCarOrderLogic;
use PreSale\Logics\order\AnyCarApolloLogic;
use PreSale\Logics\estimatePriceV2\multiResponse\component\dynamicPrice\Common;
use PreSale\Logics\estimatePriceV2\response\NoticeDataConfLogic;
use TripcloudCommon\Utils\Product as TripcloudProduct;
use BizCommon\Logics\Anycar\AnyCarCommonLogic;

/**
 * Class AnycarProductList
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\anycarProductList
 */
class AnycarProductList
{
    protected $_aInfo;

    protected $_sCarLevel;

    // 国际化货币符号
    protected $_sCurrencySymbol;

    // 国际化"现金单位"
    protected $_sCurrencyUnit;

    /**
     * AnycarProductList constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo     = $aInfo;
        $this->_sCarLevel = $aInfo['order_info']['require_level'];
        $sCurrency        = $aInfo['bill_info']['currency'] ?? '';
        list($sSymbol, $sUnit)  = Utils\Currency::getSymbolUnit($sCurrency, $aInfo['order_info']);
        $this->_sCurrencySymbol = $sSymbol;
        $this->_sCurrencyUnit   = $sUnit;
    }

    /**
     * 构建anycar偏好列表 & 新手引导描述文案
     * @param array $aResponse 返回结果
     * @return array
     */
    public function build($aResponse) {

        $aResponse['preference_product_list'] = $this->_getPreferenceProductList();
        $aResponse['product_guide_desc']      = $this->_getProductGuideDesc();
        $aResponse['category_list']           = $this->_getCategoryList();
        $aResponse['preference_show_length']  = $this->_getAnycarProductShowLength();
        if (Constants\Common::AGENT_TYPE_BOTH_CALL_ANYCAR === $this->_aInfo['order_info']['agent_type']) {
            $aResponse['include_product'] = AnyCarOrderLogic::getInstance()->getAnyCarIncludeProductList($this->_aInfo['bill_info']);
        }

        $aResponse = NoticeDataConfLogic::getInstance()->updateAnycarProductList($aResponse, $this->_aInfo);

        if (Constants\Common::AGENT_TYPE_BOTH_CALL_ANYCAR != $this->_aInfo['order_info']['agent_type']) {
            $aResponse = $this->_buildTripcloudDiscounts($aResponse);
        }

        return $aResponse;
    }


    /**
     * 多车型偏好 [preference_product_list]
     * @return array
     */
    private function _getPreferenceProductList() {
        $aAnyCarPreferenceList = [];
        $iPid   = (int)($this->_aInfo['passenger_info']['pid'] ?? 0);
        $iArea  = $this->_aInfo['order_info']['area'];
        $sPhone = (string)($this->_aInfo['passenger_info']['phone']);
        //灰度期间为保证public日志无diff，第四个参数传false，此处先不写public日志，在后面单独写
        $bAnyCarPreferenceOpen = AnyCarApolloLogic::getInstance()->getAnyCarPreferenceSwitch(
            $iPid,
            $iArea,
            $sPhone,
            false
        );
        if ($bAnyCarPreferenceOpen) {
            $aOrderInfo = $this->_aInfo['order_info'];

            $aAnyCarPreferenceList = AnyCarOrderLogic::getInstance()->getPreferenceProductList(
                $this->_aInfo['bill_info'],
                $aOrderInfo,
                $this->_sCurrencySymbol,
                $this->_sCurrencyUnit,
                $this->_aInfo
            );
        }

        //为保证和原来的public日志一致，每个车型有且仅有一条日志，感觉只要anycar写一条就可以了，不知道为啥其他车型也要写。。。
        if (!Utils\Registry::has(ANYCAR_PREFERENCE_LIST . $this->_sCarLevel)) {
            // anycar多车型偏好 A/B实验日志
            AnyCarApolloLogic::getInstance()->writeAnyCarPreferABTestLog($bAnyCarPreferenceOpen, $iPid, $iArea, $sPhone);
            Utils\Registry::set(ANYCAR_PREFERENCE_LIST. $this->_sCarLevel, true);
        }

        if (Product::isAnyCar($this->_aInfo['order_info']['product_id'])) {
            $this->_notifyTaxiCashPayClose($aAnyCarPreferenceList);
            return $aAnyCarPreferenceList;
        }

        return [];
    }


    /**
     * 新手引导描述文案，只有anycar在用。(需要包含哪些车型) [product_guide_desc]
     * @return string
     */
    private function _getProductGuideDesc() {
        $sAnyCarProductGuide = AnyCarOrderLogic::getInstance()->getProductGuideDescForDisplay(
            $this->_aInfo['bill_info']
        );

        if (Product::isAnyCar($this->_aInfo['order_info']['product_id'])) {
            return $sAnyCarProductGuide;
        }

        return '';
    }

    /**
     * anycar双排样式增加子产品线分组信息
     * @return array
     */
    private function _getCategoryList() {
        if (Utils\UtilHelper::compareAppVersion($this->_aInfo['common_info']['app_version'], '5.3.14') < 0) {
            return [];
        }

        $iPid    = (int)($this->_aInfo['passenger_info']['pid'] ?? 0);
        $iArea   = $this->_aInfo['order_info']['area'];
        $sPhone  = (string)($this->_aInfo['passenger_info']['phone']);
        $oApollo = new Apollo();
        if (!$oApollo->featureToggle(
            'gs_anycar_double_row_toggle',
            [
                'key'   => $iPid,
                'city'  => $iArea,
                'pid'   => $iPid,
                'phone' => $sPhone,
            ]
        )->allow()
        ) {
            return [];
        }

        $aConfig       = json_decode(Language::getTextFromDcmp('config_anycar-double_row_category_list', []), true);
        $aConfig       = is_array($aConfig) ? $aConfig : [];
        $sEstimateId   = $this->_aInfo['bill_info']['estimate_id'] ?? '';
        $aCategoryList = [];
        foreach ($aConfig as $aGroup) {
            $aCategoryList[] = [
                'id'              => $aGroup['id'],
                'name'            => $aGroup['name'] ?? '',
                'fee_detail_desc' => $aGroup['fee_detail_desc'] ?? '',
                'fee_detail_icon' => $aGroup['fee_detail_icon'] ?? '',
                'fee_detail_url'  => $aGroup['fee_detail_url'] . '?' . http_build_query(
                    [
                        'estimate_id' => $sEstimateId,
                        'category_id' => $aGroup['id'],
                    ]
                ),
            ];
        }

        return $aCategoryList;
    }

    /**
     * 出租车现金付下线，修改hit_dynamic_price
     * @param array $aAnyCarPreferenceList 子产品
     * @return void
     */
    private function _notifyTaxiCashPayClose(&$aAnyCarPreferenceList) {
        foreach ($aAnyCarPreferenceList as &$aItem) {
            $sGroupKey = $aItem['group_key'];
            list($iProductId, $iCarLevel, $iComboType) = explode('_', $sGroupKey);
            if (in_array($iProductId,[Utils\Product::PRODUCT_ID_UNIONE_TAXI, Utils\Product::PRODUCT_ID_BUSINESS_UNIONE_TAXI])) {
                $oToggle = (Apollo::getInstance())->featureToggle(
                    'unione_cashier_pay_offline',
                    [
                        'phone' => $this->_aInfo['passenger_info']['phone'],
                        'city'  => $this->_aInfo['order_info']['area'],
                        'key'   => $this->_aInfo['passenger_info']['phone'],
                    ]
                );
                if ($oToggle->allow()) {
                    $iPassengerId = $this->_aInfo['passenger_info']['pid'];
                    $sKey         = Utils\Common::getRedisPrefix(P_SHOW_TAXI_CASH_PAY_CLOSE).$iPassengerId;
                    $oRedisDB     = RedisDB::getInstance();
                    if (empty($oRedisDB->get($sKey)) && empty($aItem['hit_dynamic_price'])) {
                        $aItem['hit_dynamic_price'] = 1;
                    }
                }
            }
        }
    }

    /**
     * @return float
     */
    private function _getAnycarProductShowLength() {
        return AnyCarApolloLogic::getInstance()->getAnycarProductShowLength(
            $this->_aInfo['order_info']['area'],
            $this->_aInfo['passenger_info']['pid'],
            $this->_aInfo['passenger_info']['phone']
        );
    }

    /**
     * @param array $aResponse 输入的返回结果
     * @return mixed
     */
    private function _buildTripcloudDiscounts($aResponse) {
        $aOrderInfo     = $this->_aInfo['order_info'];
        $iPid           = (int)($this->_aInfo['passenger_info']['pid'] ?? 0);
        $productList    = [];
        $subProductList = [];
        foreach ($aResponse['preference_product_list'] as $value) {
            if (TripcloudProduct::isDiscountsByBusinessID($value['business_id'])) {
                $subProductList[] = $value;
            } else {
                $productList[] = $value;
            }
        }

        if (empty($subProductList)) {
            return $aResponse;
        }

        usort(
            $subProductList,
            function ($a, $b) {
                return $a['price'] - $b['price'];
            }
        );
        $productList[] = AnyCarOrderLogic::getInstance()->getShortDistanceItem(
            $subProductList,
            $this->_sCurrencySymbol,
            $this->_sCurrencyUnit
        );
        $productList   = AnyCarOrderLogic::getInstance()->sortMultiProducts($productList, $iPid, $aOrderInfo['area']);

        $aResponse['preference_product_list'] = $productList;
        return $aResponse;
    }
}
