<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component;

use BizCommon\Constants\OrderNTuple;
use BizCommon\Models\Passenger\Passenger;
use BizCommon\Utils\Common as BizCommon;
use BizCommon\Utils\Order;
use BizLib\Config;
use BizLib\Constants\Common as Constants;
use BizLib\Constants\Horae;
use BizLib\Constants\OrderSystem;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\RespCode;
use BizLib\Exception\ExceptionWithResp;
use BizLib\Log as NuwaLog;
use BizLib\Utils\CarLevel;
use BizLib\Utils\Common;
use BizLib\Utils\Language;
use BizLib\Utils\Product;
use BizLib\Utils\ProductCategory;
use Nebula\Exception\Route\InterRouteException;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\estimatePriceV2\DecisionLogic;
use PreSale\Logics\estimatePriceV2\multiRequest\OrderInfo;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;
use PreSale\Logics\estimatePriceV2\ParamsLogic;

/**
 * Class SortSelection
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component
 */
class SortSelection
{
    const DACHE_ANYCAR_ITEM_NOT_SELECTED  = 0;
    const DACHE_ANYCAR_ITEM_SELECTED      = 1;
    const DACHE_ANYCAR_ITEM_NOT_RECOMMEND = 0;
    const DACHE_ANYCAR_ITEM_RECOMMEND     = 1;

    const RECOMMEND_INDEX_LIMIT = 4;

    const INTER_AREA_NOT_OPEN_URL = 'https://v.didi.cn/k5aDxAY';

    const CARPOOL_ESTIMATE_EXCEPTION_CODE = 535011;

    private $_aOneConf;

    private $_oDecision;

    private $_aInfos;

    private $_aSortIndex = array(
        'anycar'       => 1,
        'xiaoba'       => 2,
        'carpool'      => 3,
        'special_rate' => 4,
        'kuaiche'      => 5,
        'youxiang'     => 6,
    );

    private $_aDefaultSort = array(
        ProductCategory::PRODUCT_CATEGORY_CARPOOL_FLAT_RATE_BY_SEAT,
        ProductCategory::PRODUCT_CATEGORY_FAST,
        ProductCategory::PRODUCT_CATEGORY_FAST_SPECIAL_RATE,
        ProductCategory::PRODUCT_CATEGORY_APLUS,
        ProductCategory::PRODUCT_CATEGORY_UNIONE_SPECIAL_PRICE,
        ProductCategory::PRODUCT_CATEGORY_YOUXUAN_TAXI,
        ProductCategory::PRODUCT_CATEGORY_UNIONE,
        ProductCategory::PRODUCT_CATEGORY_YOUXIANG,
        ProductCategory::PRODUCT_CATEGORY_PREMIUM_COMFORT,
        ProductCategory::PRODUCT_CATEGORY_PREMIUM_BUSINESS,
        ProductCategory::PRODUCT_CATEGORY_LUXURY_ANY,
    );

    /**
     * SortSelection constructor.
     * @param array $aInfos aInfo列表
     */
    public function __construct($aInfos) {
        $this->_aOneConf  = ParamsLogic::getInstance()->getOneConf() ?? [];
        $this->_oDecision = DecisionLogic::getInstance();
        $this->_aInfos    = $aInfos;
    }

    /**
     * @Desc:
     * @param array Array $aInfo structure to count the elements of.
     * @return mixed
     * @Author:<EMAIL>
     */
    private function _fixComboType($aInfo) {
        $sCarLevel  = $aInfo['order_info']['require_level'];
        $iComboType = $aInfo['bill_info']['product_infos'][$sCarLevel]['combo_type'];
        $aOrderInfo = $aInfo['order_info'];
        if (Horae::TYPE_COMBO_CARPOOL != $aOrderInfo['combo_type']) {
            $aOrderInfo['combo_type'] = $iComboType;
        }

        return $aOrderInfo;
    }


    /**
     * @param array $aEstimateData aEstimateData
     * @param bool  $bUseNew       是否是新打车顶导，使用新的选中流程
     * @param array $aInfos        品类数组
     * @return array
     * @throws ExceptionWithResp|InterRouteException 异常
     */
    public function sortEstimateData($aEstimateData, $bUseNew, $aInfos = []) {
        if ($bUseNew) {
            $aSortedEstimateData = $this->sortEstimateDataNew($aEstimateData, $aInfos);
        } else {
            $aSortedEstimateData = $this->sortEstimateDataOld($aEstimateData);
        }

        return $this->_sortHandler($aSortedEstimateData, $aInfos);
    }

    // 注释-待删
//    /**
//     * @desc 排序时是否需要过滤品类,返回true为过滤,否则不过滤
//     * @param array $aSortedDataItem 预估数据子项
//     * @param array $aInfos          aInfos
//     * @return bool
//     */
//    private function _shouldFilterDuringSort($aSortedDataItem, $aInfos) {
//        // 远程特惠只能发30 - 100千米的订单。只能在预估后过滤,因为之前拿不到账单预估距离
//        if (\BizLib\Utils\Horae::isInterCityCarPoolScene($aSortedDataItem['combo_type'])) {
//            $aInfo = current($aInfos);
//
//            // 非打车顶导不过滤
//            if (Constants::MENU_DACHE_ANYCAR != $aInfo['order_info']['menu_id']) {
//                return false;
//            }
//
//            // 非主app不过滤
//            if (!in_array($aInfo['common_info']['access_key_id'], [Constants::DIDI_IOS_PASSENGER_APP, Constants::DIDI_ANDROID_PASSENGER_APP])) {
//                return false;
//            }
//
//            $oConfig            = (new Apollo())->getConfigResult('inter_carpool_city_conf', $aInfo['order_info']['area']);
//            list($bSucc, $aRet) = $oConfig->getAllConfig();
//            if (!$bSucc) {
//                return false;
//            }
//
//            $iDistance           = $aSortedDataItem['drive_metre'] ?? 0; //单位米
//            $iDistanceLowerLimit = $aRet['open_fence_distance_limit']['open_fence_distance_lower_limit'] ?? 0;
//            $iDistanceUpperLimit = $aRet['open_fence_distance_limit']['open_fence_distance_upper_limit'] ?? 1000000;
//            if (empty($iDistance)) {
//                return false;
//            }
//
//            return ($iDistance < $iDistanceLowerLimit) || ($iDistance > $iDistanceUpperLimit);
//        }
//
//        return false;
//    }

    /**
     * @desc 排序钩子,有些过滤逻辑必须在预估信息产生后判定,这里收口
     * @param array $aSortedEstimateData aSortedEstimateData
     * @param array $aInfos              aInfos
     * @return array
     * @throws InterRouteException 异常
     */
    private function _sortHandler($aSortedEstimateData, $aInfos) {
        $this->_interCarpoolHandler($aSortedEstimateData, $aInfos);

        return $aSortedEstimateData;
    }

    /**
     * @desc 城际handler
     * @param string $sSortHandlerData aSortedEstimateData
     * @param array  $aInfos           aInfos
     * @return void
     * @throws InterRouteException     路线异常
     */
    private function _interCarpoolHandler($sSortHandlerData, $aInfos) {
        $aInterCarpoolInfo = $this->_oDecision->getInterCarpoolInfo();
        if (!empty($aInterCarpoolInfo)) {
            $aInfo = $aInterCarpoolInfo;
        } else {
            $aInfo = current($aInfos);
        }

        $sMenuId   = $aInfo['order_info']['menu_id'];
        $sPageType = $aInfo['order_info']['page_type'];

        // 城际拼车顶导或者场景页下预估为空,抛出特定异常
        if (!empty($aInterCarpoolInfo) && MainDataRepo::isInterCarpoolPullPage($sMenuId, $sPageType)) {
            $sJumpUrl = Language::getTextFromDcmp('config_inter_carpool-area_not_open_h5_url');
            $sJumpUrl = empty($sJumpUrl) ? self::INTER_AREA_NOT_OPEN_URL : $sJumpUrl;
            $sErrMsg  = Language::getTextFromDcmp('config_inter_carpool-area_not_open_errmsg');
            $sErrMsg  = empty($sErrMsg) ? \BizLib\ErrCode\Msg::get(\BizLib\ErrCode\Code::E_COMMON_AREA_NOT_OPEN_SERVICE) : $sErrMsg;

            if ((MainDataRepo::isMiniAppClient(MainDataRepo::getAccessKeyId()) && version_compare(MainDataRepo::getAppVersion(), '6.2.17') >= 0)
                || (MainDataRepo::isNativeClient(MainDataRepo::getAccessKeyId()) && version_compare(MainDataRepo::getAppVersion(), '6.2.18') >= 0)
            ) {
                $sH5Url     = Language::getTextFromDcmp('carpool_intercity-area_not_open_guide_url');
                $sAppletUrl = Language::getTextFromDcmp('carpool_intercity-area_not_open_guide_applet_url');
                if (MainDataRepo::isNativeClient(MainDataRepo::getAccessKeyId()) && !empty($sH5Url)) {
                    $sJumpUrl = $sH5Url;
                } elseif (MainDataRepo::isMiniAppClient(MainDataRepo::getAccessKeyId()) && !empty($sAppletUrl)) {
                    $sJumpUrl = $sAppletUrl;
                }

                $sMsg = Language::getTextFromDcmp('carpool_intercity-area_not_open_estimate_msg');
                if (!empty($sMsg)) {
                    $sErrMsg = $sMsg;
                }
            }

            $oException = new InterRouteException('', \BizLib\ErrCode\Code::E_COMMON_AREA_NOT_OPEN_SERVICE);
            $oException->setRespCode(\BizLib\ErrCode\Code::E_COMMON_AREA_NOT_OPEN_SERVICE);
            $oException->setRespMsg($sErrMsg);
            $oException->setRespData(['error_url' => $sJumpUrl]);
            throw $oException;
        }
    }

    /**
     * @param array $aEstimateData aEstimateData
     * @param array $aInfos        品类数组
     * @return array
     * @throws ExceptionWithResp 异常
     */
    public function sortEstimateDataNew($aEstimateData, $aInfos = []) {
        $aSortedEstimateData = array();
        $aDecisionGuide      = $this->_oDecision->getDecisionProductGuideList();
        if (!empty($aDecisionGuide)) {
            foreach ($aDecisionGuide as $aOrder) {
                foreach ($aEstimateData as $aData) {
                    if ($aData['product_category'] == $aOrder['product_category'] && true == $aOrder['is_show']) {
                        if (MainDataRepo::isBelongTripCloud($aOrder['product_category'])) {
                            // 新版本客企不改写为5
                            $aInfo = current($aInfos);
                            if (!(Util::isTripCloudV3NA($aInfo) || Util::isTripCloudV3WebApp($aInfo))) {
                                // 由于端不发版, 暂时将导流位231(济宁客企品类)改为5(城际拼车) 以供跳转
                                $aData['product_category'] = ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTE;
                            }
                        }

                        $aSortedEstimateData[] = $aData;
                        //append一个后就break；保证仅会返回一个
                        break;
                    }
                }
            }
        }

        $aDecisionOrder = $this->_oDecision->getDecisionProductList();
        // 依据DDS结果产品排序, 与推荐
        if (!empty($aDecisionOrder)) {
            foreach ($aDecisionOrder as $aOrder) {
                foreach ($aEstimateData as $aData) {
                    if ($aData['product_category'] == $aOrder['product_category'] && false == $this->_shouldRemove($aOrder)) {
                        $aData['recommend_type'] = $aOrder['is_recommend'] ? self::DACHE_ANYCAR_ITEM_RECOMMEND : self::DACHE_ANYCAR_ITEM_NOT_RECOMMEND;
                        $aSortedEstimateData[]   = $aData;
                    } else {
                        $aInfo        = current($aInfos);
                        $iAccessKeyId = $aInfo['common_info']['access_key_id'];
                        // 非常紧急的PM需求（滴小滴，在 sp_open = 0，即 remove_flag = true 时，下发对应文案）
                        if (BizCommon::isRegionalWeChatMiniProgram($iAccessKeyId)
                            && in_array($aOrder['product_category'], [ProductCategory::PRODUCT_CATEGORY_FAST_SPECIAL_RATE, ProductCategory::PRODUCT_CATEGORY_SPECIAL_RATE])
                            && true == $this->_shouldRemove($aOrder)
                        ) {
                            $sErrMsg = Config::text('errno', 'dache_anycar_no_products_error_for_dixiaodi');
                            throw new ExceptionWithResp(
                                Code::E_COMMON_AREA_NOT_OPEN_SERVICE,
                                RespCode::P_ERRNO_NOT_OPEN_SERVICE,
                                $sErrMsg
                            );
                        }
                    }
                }
            }
        } else {
            foreach ($aEstimateData as $aOneEstimateData) {
                if (OrderInfo::FORM_SHOW_TYPE_ANYCAR == $aOneEstimateData['form_show_type']) {
                    $aFilterEstimateData[] = $aOneEstimateData;
                }
            }

            // 根据 $this->_aDefaultSort 顺序，将 $aSortedEstimateData 排序
            // 这块如果使用 usort 直接排个序逻辑会更加清晰，且时间复杂度也比这低
            $iDataCount = count($aFilterEstimateData);
            $iK         = 0;
            foreach ($this->_aDefaultSort as $iProductCategory) {
                $bFind = false;
                $iJ    = $iK;
                while ($iJ < $iDataCount) {
                    if ($aFilterEstimateData[$iJ]['product_category'] == $iProductCategory) {
                        if ($iK != $iJ) {
                            $aTemp = $aFilterEstimateData[$iJ];
                            $aFilterEstimateData[$iJ] = $aFilterEstimateData[$iK];
                            $aFilterEstimateData[$iK] = $aTemp;
                        }

                        $bFind = true;
                        break;
                    }

                    $iJ ++;
                }

                if ($bFind) {
                    $iK ++;
                }
            }

            $aSortedEstimateData = $aFilterEstimateData;
        }

        return $aSortedEstimateData;
    }

    /**
     * ShouldRemove.
     *
     * @param array $aProduct aProduct
     * @return bool
     */
    private function _shouldRemove($aProduct) {
        $aSpecialProduct = [
            ProductCategory::PRODUCT_CATEGORY_FAST_SPECIAL_RATE,
            ProductCategory::PRODUCT_CATEGORY_DI_XIAO_DI,

        ];
        if (in_array($aProduct['product_category'], $aSpecialProduct)) {
            // 5 排队场景 6 无车场景
            // 见：dds/conf/biz/policy/policy_special_rate_filter.json:79
            return $aProduct['remove_flag'] && !in_array($aProduct['remove_reason'], ['5', '6']);
        }

        if (ProductCategory::PRODUCT_CATEGORY_APLUS == $aProduct['product_category']) {
            return $aProduct['remove_flag'] && DecisionLogic::REMOVE_REASON_HIDE_LEVEL != $aProduct['remove_reason'];
        }

        return $aProduct['remove_flag'];
    }

        /**
     * @param array $aEstimateData aEstimateData
     * @return array|mixed
     */
    public function sortEstimateDataOld($aEstimateData) {

        $aIndexedInfo = [];
        foreach ($this->_aInfos as $aInfo) {
            $iEstimateId = $aInfo['bill_info']['estimate_id'];
            $aIndexedInfo[$iEstimateId] = $aInfo;
        }

        //初始化sort_index
        $aEstimateData = $this->_initSortIndex($aEstimateData,$aIndexedInfo);

        $aSortedEstimateData = $aSortedEstimateId = array();

        $aDecisionOrder = $this->_oDecision->getDecisionProductOrder();
        // 依据DDS结果产品排序
        if ($this->_oDecision->getDecisionSortToggleResult() && !empty($aDecisionOrder)) {
            foreach ($aDecisionOrder as $aOrder) {
                foreach ($aEstimateData as $aData) {
                    $aInfo = $this->_fixComboType($aIndexedInfo[$aData['estimate_id']]);
                    if (\BizCommon\Utils\Horae::isNTupleMatch($aOrder,$aInfo)
                        && false == $this->_shouldRemove($aOrder) && !in_array($aData['estimate_id'], $aSortedEstimateId)
                    ) {
                        $aSortedEstimateData[] = $aData;
                        $aSortedEstimateId[]   = $aData['estimate_id'];
                    }
                }
            }

            // dds 删除的产品不展示
            // 拼接未排产品
            foreach ($aEstimateData as $aData) {
                $bMatch = false;
                foreach ($aSortedEstimateData as $aSortData) {
                    if ($aSortData['estimate_id'] == $aData['estimate_id']) {
                        $bMatch = true;
                        break;
                    }
                }

                $bUsed = false;
                foreach ($aDecisionOrder as $aOrder) {
                    $aInfo = $this->_fixComboType($aIndexedInfo[$aData['estimate_id']]);
                    if (\BizCommon\Utils\Horae::isNTupleMatch($aOrder,$aInfo)) {
                        $bUsed = true;
                        break;
                    }
                }

                if (!$bMatch && !$bUsed) {
                    $aSortedEstimateData[] = $aData;
                }
            }
        } else {
            $aInfo = current($this->_aInfos);
            if (Common::isLiteVersion($aInfo['common_info']['app_version'], $aInfo['common_info']['client_type'], $aInfo['order_info']['channel'])) {
                $aProductSortList = Config::config('config_lite', 'product_sort');
                $iJ = 0;
                foreach ($aProductSortList as $aProduct) {
                    $iI = 0;
                    foreach ($aEstimateData as $aData) {
                        if ($iI >= $iJ && $aProduct['business_id'] == $aData['business_id']
                            && $aProduct['require_level'] == $aData['require_level']
                            && ($aProduct['combo_type'] == $aData['combo_type'] || -1 == $aProduct['combo_type'])
                        ) {
                            $aTmpProduct        = $aEstimateData[$iI];
                            $aEstimateData[$iI] = $aEstimateData[$iJ];
                            $aEstimateData[$iJ] = $aTmpProduct;
                            ++$iJ;
                        }

                        ++$iI;
                    }
                }

                return  $aEstimateData;
            } else {
                //  sort逻辑
                //  1、按sort_index从小到大排序（Anycar，小巴，拼车，特价车，快车，优享）
                //  2、然后拼接无sort_index，按默认的顺序排序（其他）
                foreach ($aEstimateData as $aInfo) {
                    if (!empty($aInfo['sort_index'])) {
                        $aSortedEstimateData[] = $aInfo;
                    }
                }

                usort($aSortedEstimateData, array('PreSale\Logics\estimatePriceV2\multiResponse\component\SortSelection', 'estimateDataSortCmp'));

                foreach ($aEstimateData as $aInfo) {
                    if (empty($aInfo['sort_index'])) {
                        $aSortedEstimateData[] = $aInfo;
                    }
                }
            }
        }

        return $aSortedEstimateData;
    }

    /**
     * @param array $aEstimateData $aEstimateData
     * @param array $aIndexedInfo  $aIndexedInfo
     * @return array
     */
    private function _initSortIndex($aEstimateData, $aIndexedInfo) {

        foreach ($aEstimateData as $iIndex => $aData) {
            $aInfo         = $aIndexedInfo[$aData['estimate_id']];
            $iSortIndex    = null;
            $sCarLevel     = $aInfo['order_info']['require_level'];
            $iComboType    = $aInfo['bill_info']['product_infos'][$sCarLevel]['combo_type'] ?? $aInfo['order_info']['combo_type'];
            $aSpecialOrder = ['product_id' => $aInfo['order_info']['product_id'], 'require_level' => $sCarLevel, 'combo_type' => $iComboType];
            if (Product::isAnyCar($aInfo['order_info']['product_id'])) {
                $iSortIndex = $this->_aSortIndex['anycar'];
            } elseif ($aInfo['bill_info']['is_carpool_open']) {
                if (CarLevel::DIDI_XIAOBA_CAR_LEVEL == $aInfo['order_info']['require_level']) {
                    $iSortIndex = $this->_aSortIndex['xiaoba'];
                } else {
                    $iSortIndex = $this->_aSortIndex['carpool'];
                }
            } elseif (Order::isSpecialRateV2($aSpecialOrder) || $aInfo['order_info']['n_tuple']['is_special_price']) {
                $iSortIndex = $this->_aSortIndex['special_rate'];
            } else {
                if (CarLevel::DIDI_YOUXIANG_CAR_LEVEL == $aInfo['order_info']['require_level']) {
                    $iSortIndex = $this->_aSortIndex['youxiang'];
                } elseif (CarLevel::DIDI_PUTONG_CAR_LEVEL == $aInfo['order_info']['require_level']) {
                    $iSortIndex = $this->_aSortIndex['kuaiche'];
                }
            }

            if (isset($iSortIndex)) {
                $aEstimateData[$iIndex]['sort_index'] = $iSortIndex;
            }
        }

        return $aEstimateData;
    }

    /**
     * @param array $aEstimateData 预估EstimateData
     * @param bool  $bUseNew       是否是新打车顶导
     * @return array
     */
    public function setDefaultSelection($aEstimateData, $bUseNew) {

        if ($bUseNew) {
            $aEstimateData = $this->setDefaultSelectionNew($aEstimateData);
        } else {
            $aEstimateData = $this->setDefaultSelectionOld($aEstimateData);
        }

        return $aEstimateData;
    }

    /**
     * @param array $aEstimateData aEstimateData
     * @return array
     */
    public function setDefaultSelectionNew($aEstimateData) {

        $aDDsProductList = $this->_oDecision->getDecisionProductList();
        $bSetSelectSucc  = false;
        foreach ($aEstimateData as $index => $aData) {
            foreach ($aDDsProductList as $aProduct) {
                if ($aData['product_category'] == $aProduct['product_category']) {
                    if ($aProduct['is_selected']) {
                        $aEstimateData[$index]['select_type'] = self::DACHE_ANYCAR_ITEM_SELECTED;
                        $bSetSelectSucc = true;
                    }
                }
            }
        }

        //若未设置任何选中，兜底选中排序第一个
        if (!$bSetSelectSucc) {
            foreach ($aEstimateData as $index => $aData) {
                $aEstimateData[$index]['select_type'] = self::DACHE_ANYCAR_ITEM_SELECTED;
                break;
            }
        }

        return $aEstimateData;
    }

    /**
     * @param array $aEstimateData aEstimateData
     * @return array
     */
    public function setDefaultSelectionOld($aEstimateData) {

        //获取默认选中的item
        $aDefaultSelection = $this->_getDefaultSelection();

        $aIndexedInfo = [];
        foreach ($this->_aInfos as $aInfo) {
            $iEstimateId = $aInfo['bill_info']['estimate_id'];
            $aIndexedInfo[$iEstimateId] = $aInfo;
        }

        foreach ($aEstimateData as $iIndex => $aData) {
            $aInfo = $aIndexedInfo[$aData['estimate_id']];
            if (empty($aInfo)) {
                continue;
            }

            $bIsDefault = 0;
            if (!empty($aDefaultSelection)) {
                $sCarLevel    = $aInfo['order_info']['require_level'];
                $iProductId   = (int)($aInfo['order_info']['product_id']);
                $iBusinessId  = (int)($aInfo['common_info']['business_id']);
                $iComboType   = $aInfo['bill_info']['product_infos'][$sCarLevel]['combo_type'];
                $iCarpoolType = $aInfo['order_info']['n_tuple']['carpool_type'];
                //carpool 原本的兼容逻辑
                $iCarpoolComboType = $this->_transCarpool($aInfo['bill_info']['product_infos'][$sCarLevel]['combo_type'],$iCarpoolType);
                $bIsSpecialPrice   = $aInfo['order_info']['n_tuple']['is_special_price'];

                if ($sCarLevel == $aDefaultSelection['require_level']
                    && ($iProductId == $aDefaultSelection['product_id'] || $iBusinessId == $aDefaultSelection['business_id'])
                    && ($iComboType == $aDefaultSelection['combo_type'] || $iCarpoolComboType == $aDefaultSelection['combo_type'])
                    && ((empty($aDefaultSelection['is_special_price']) && !$bIsSpecialPrice) || ($bIsSpecialPrice && $aDefaultSelection['is_special_price'])) //校验特价车的标识
                ) {
                    //拼车需要校验is_carpool_open
                    if (\BizLib\Utils\Horae::isCarpool($iCarpoolComboType, $sCarLevel) || \BizCommon\Utils\Horae::isStationCarpool($iCarpoolType)) {
                        if (!empty($aInfo['bill_info']['is_carpool_open'])) {
                            $bIsDefault = 1;
                        }
                    } else {
                        $bIsDefault = 1;
                    }
                }
            }

            $aEstimateData[$iIndex]['is_default'] = $bIsDefault;
        }

        //看起来是pre-sale做的默认选中的兜底逻辑，应该几乎不会走到了，于是照搬到这里
        if ($this->_notSetDefault($aEstimateData)) {
            $aEstimateData = $this->_setIsDefault($aEstimateData, $this->_aOneConf);
        }

        return $aEstimateData;

    }

    // 注释-待删
//    /**
//     * 6.0竖版表单添加推荐字段
//     * @param array $aEstimateData estimate_data
//     * @return mixed
//     */
//    public function setXNewFormRecommend($aEstimateData) {
//
//        $xNewForm = ParamsLogic::getInstance()->getRequest('x_new_form');
//        if ($xNewForm) {
//            //是否设置成功了recommend；若未成功择走兜底逻辑
//            $bSetRecommend    = false;
//            $aProductDecision = $this->_oDecision->getDecisionProductOrder();
//            if (!empty($aProductDecision)) {
//                foreach ($aEstimateData as $iIndex => $aData) {
//                    $aEstimateData[$iIndex]['x_recommend'] = 0;
//                    $aEstimateData[$iIndex]['x_rec_index'] = 0;
//                    foreach ($aProductDecision as $aProduct) {
//                        if (\BizCommon\Utils\Horae::isNTupleMatch($aProduct, $aData)
//                            && false == $aProduct['remove_flag']
//                            && true == $aProduct['x_recommend']
//                        ) {
//                            $bSetRecommend = true;
//                            $aEstimateData[$iIndex]['x_recommend'] = (int)$aProduct['x_recommend'];
//                            $aEstimateData[$iIndex]['x_rec_index'] = (int)$aProduct['x_rec_index'];
//                            break;
//                        }
//                    }
//                }
//            }
//
//            //兜底逻辑（主要面向未登录用户，后续迁移至dds）
//            if (!$bSetRecommend) {
//                $iRecIndex = 1;
//                foreach ($aEstimateData as $iIndex => $aData) {
//                    $aEstimateData[$iIndex]['x_recommend'] = 0;
//                    $aEstimateData[$iIndex]['x_rec_index'] = 0;
//                    //兜底快车/出租车/舒适型
//                    if (in_array(
//                        $aData['require_level'],
//                        [
//                            CarLevel::DIDI_PUTONG_CAR_LEVEL,
//                            CarLevel::DIDI_UNITAXI_PUTONG_CAR_LEVEL,
//                            CarLevel::DIDI_SHUSHI_CAR_LEVEL,
//                        ]
//                    )
//                    ) {
//                        $aEstimateData[$iIndex]['x_recommend'] = 1;
//                        $aEstimateData[$iIndex]['x_rec_index'] = $iRecIndex;
//                        $iRecIndex ++;
//                    }
//                }
//            }
//        }
//
//        return $aEstimateData;
//    }


    /**
     * @return array|mixed|string
     */
    private function _getDefaultSelection() {

        //6.0端上传的默认选中优先级最高，从排序的逻辑里迁移过来的。
        //此前的备注: 6.0机场单端上默认选中，会因为combo_type的变化导致is_default有bug;
        //目前还是有bug的
        $aAppDefaultSelection = ParamsLogic::getInstance()->getDefaultSelection();
        $aInfo = current($this->_aInfos);
        if (!empty($aAppDefaultSelection) && is_array($aAppDefaultSelection) && Common::isLiteVersion($aInfo['common_info']['app_version'], $aInfo['common_info']['client_type'], $aInfo['order_info']['channel'])) {
            return $aAppDefaultSelection;
        }

        //dds
        $aDDsSelection = $this->_oDecision->getDecisionSelectedItem();
        if (!empty($aDDsSelection)) {
            return $aDDsSelection;
        }

        //athena
        $aAthenaSelection = [];
        if (!$this->_isOneConfSetDefault() && !$this->_oDecision->getDecisionAthenaToggleResult()) {
            if (isset($this->_aInfos[0]) && !empty($this->_aInfos[0]['athena_extra'])) {
                $aAthenaExtra = $this->_aInfos[0]['athena_extra'];
                if (!empty($aAthenaExtra['guide_product']) && !empty($aAthenaExtra['guide_level']) && isset($aAthenaExtra['guide_combo_type'])) {
                    $aAthenaSelection = [
                        'business_id'   => $aAthenaExtra['guide_product'],
                        'product_id'    => Product::getProductIdByBusinessId($aAthenaExtra['guide_product']),
                        'require_level' => $aAthenaExtra['guide_level'],
                        'combo_type'    => $aAthenaExtra['guide_combo_type'],
                    ];
                }
            }
        }

        if (!empty($aAthenaSelection)) {
            return $aAthenaSelection;
        }

        //price返回结果
        $aPriceSelection = [];
        foreach ($this->_aInfos as $aInfo) {
            $sCarLeveL = $aInfo['order_info']['require_level'];
            if (1 == (int)($aInfo['price_extra']['is_default'])) {
                $aPriceSelection = [
                    'business_id'      => $aInfo['order_info']['business_id'],
                    'product_id'       => $aInfo['bill_info']['product_infos'][$sCarLeveL]['product_line_id'],
                    'combo_type'       => $aInfo['bill_info']['product_infos'][$sCarLeveL]['combo_type'],
                    'require_level'    => $sCarLeveL,
                    'is_special_price' => $aInfo['order_info']['n_tuple']['is_special_price'],
                ];
                break;
            }
        }

        if (!empty($aPriceSelection)) {
            return $aPriceSelection;
        }

        return [];
    }

    /**
     * @return bool
     */
    private function _isOneConfSetDefault() {
        foreach ($this->_aOneConf as $item) {
            if (isset($item['is_default']) && 1 == $item['is_default']) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param int $iComboType combo_type
     * @return int
     */
    private function _transCarpool($iComboType, $iCarpoolType = 0) {
        if (OrderNTuple::CARPOOL_TYPE_FLAT_RATE == $iCarpoolType) {
            return OrderSystem::TYPE_COMBO_CARPOOL;
        }

        if (Horae::TYPE_COMBO_CARPOOL_FLAT_RATE == $iComboType) {
            return Horae::TYPE_COMBO_CARPOOL;
        }

        return (int)($iComboType);
    }

    /**
     * @param array $aEstimateData aEstimateData
     * @return bool
     */
    private function _notSetDefault($aEstimateData) {
        foreach ($aEstimateData as $idx => $item) {
            if (1 == $item['is_default']) {
                return false;
            }
        }

        return true;
    }

    /**
     *  调用这个函数以前拼车和非拼车必定已经选中了一个,所以这里要处理的只是用端上传的来做下优先级匹配
     * @param array $aEstimateData aEstimateData
     * @param array $aOneConf      aOneConf
     * @return array
     */
    private function _setIsDefault(array $aEstimateData, array $aOneConf) {
        if (0 == count($aEstimateData)) {
            return $aEstimateData;
        }

        if (1 == count($aEstimateData)) {
            $aEstimateData[0]['is_default'] = 1;

            return $aEstimateData;
        }

        foreach ($this->_aInfos as $aInfo) {
            $iPid = $aInfo['passenger_info']['pid'];
            break;
        }

        $confIndex   = array();
        $iBusinessID = -1;
        // 拿到端需要选中的tab
        foreach ($aOneConf as $idx => $item) {
            if ($iBusinessID < 0) {
                $iBusinessID = $item['business_id'];
            }

            if (isset($item['is_default']) && 1 == $item['is_default']) {
                $confIndex = $item;
                break;
            }
        }

        $aPrefer = array();

        $iHitOneConfIdx     = -1;
        $iHitCarpool        = -1;
        $iPreferSelectedIdx = -1;
        $iYXSelectedIdx     = -1;
        $iSum       = 0;
        $iAnyCarIdx = -1;
        foreach ($aEstimateData as $idx1 => $item1) {
            if (1 == $item1['is_default']) {
                ++$iSum;
            }

            if (1 == $item1['is_default'] && Horae::TYPE_COMBO_CARPOOL == \BizLib\Utils\Horae::getComboType($item1['scene_type'])) {
                $iHitCarpool = $idx1;
            }

            if (!isset($aPrefer[$item1['business_id']])) {
                $this->passenger = Passenger::getInstance();
                $iProductID      = Product::getProductIdByBusinessId($item1['business_id']);
                $pre = $this->passenger->getPreference($iPid, $iProductID);
                if (!empty($pre)) {
                    $aPrefer[$item1['business_id']] = $pre;
                } else {
                    $aPrefer[$item1['business_id']] = false;
                }
            }
        }

        if (!empty($aPrefer)) {
            $iMaxTs       = 0;
            $sKey         = '';
            $aLatestValue = array();
            foreach ($aPrefer as $iBusinessId => &$aHistory) {
                if (OrderNTuple::COMMON_PRODUCT_ID_ANY_CAR == (int)($iBusinessId)) {
                    $aHistory['require_level'] = CarLevel::DIDI_ANY_CAR_CAR_LEVEL;
                }

                if ($iMaxTs <= (int)($aHistory['ts'])) {
                    $iMaxTs       = (int)($aHistory['ts']);
                    $sKey         = $iBusinessId;
                    $aLatestValue = $aHistory;
                }
            }

            $aPrefer = array($sKey => $aLatestValue);
        }

        foreach ($aEstimateData as $idx2 => $item2) {
            if (isset($aPrefer[$item2['business_id']])) {
                if ($aPrefer[$item2['business_id']]['require_level'] == $item2['require_level']
                    && $aPrefer[$item2['business_id']]['combo_type'] == \BizLib\Utils\Horae::getComboType($item2['scene_type'])
                ) {
                    $iPreferSelectedIdx = $idx2;
                }
            }

            if (!empty($confIndex) && $item2['business_id'] == $confIndex['business_id']
                && \BizLib\Utils\Horae::getComboType($item2['scene_type']) == $confIndex['combo_type']
                && $item2['require_level'] == $confIndex['require_level']
            ) {
                $iHitOneConfIdx = $idx2;
            }

            // 是否命中优享
            if (Horae::HORAE_SCENE_TYPE_DEFAULT == $item2['scene_type']
                && CarLevel::DIDI_YOUXIANG_CAR_LEVEL == $item2['require_level']
                && 1 == $item2['is_default']
            ) {
                $iYXSelectedIdx = $idx2;
            }

            // 是否返回了anyCar
            if (Horae::HORAE_SCENE_TYPE_DEFAULT == $item2['scene_type']
                && CarLevel::DIDI_ANY_CAR_CAR_LEVEL == $item2['require_level']
            ) {
                $iAnyCarIdx = $idx2;
            }
        }

        // 命中one_conf配置则以one_conf为准
        if ($iHitOneConfIdx > -1) {
            foreach ($aEstimateData as $idx3 => $item3) {
                $aEstimateData[$idx3]['is_default'] = 0;
            }

            $aEstimateData[$iHitOneConfIdx]['is_default'] = 1;

            return $aEstimateData;
        }

        // 如果是有愿拼
        if ($iHitCarpool > -1) {
            foreach ($aEstimateData as $idx5 => $item5) {
                $aEstimateData[$idx5]['is_default'] = 0;
            }

            $aEstimateData[$iHitCarpool]['is_default'] = 1;

            return $aEstimateData;
        }

        // 如果返回了anyCar并且是第一次返回,则选中anyCar,并且记录anyCar冒泡记录
        if ($iAnyCarIdx > -1) {
            $iProductID      = Product::getProductIdByBusinessId($aEstimateData[$iAnyCarIdx]['business_id']);
            $this->passenger = Passenger::getInstance();
            $bHasTag         = $this->passenger->hasBubbleTag($iPid, $iProductID);
            if (!$bHasTag) {
                foreach ($aEstimateData as $idx6 => $item6) {
                    $aEstimateData[$idx6]['is_default'] = 0;
                }

                $aEstimateData[$iAnyCarIdx]['is_default'] = 1;

                return $aEstimateData;
            }
        }

        // 命中发单历史则以发单历史为准
        if ($iPreferSelectedIdx > -1) {
            foreach ($aEstimateData as $idx4 => $item4) {
                $aEstimateData[$idx4]['is_default'] = 0;
            }

            $aEstimateData[$iPreferSelectedIdx]['is_default'] = 1;

            return $aEstimateData;
        }

        // 如果都没命中且选中多个,则把优享的去掉
        if ($iYXSelectedIdx > -1 && $iSum > 1) {
            $aEstimateData[$iYXSelectedIdx]['is_default'] = 0;

            return $aEstimateData;
        }

        if ($iSum > 1) {
            NuwaLog::warning('too many is_default=1');
        }

        return $aEstimateData;
    }

    /**
     * @param array $aInfoA aInfoA
     * @param array $aInfob aInfoB
     * @return int
     */
    public static function estimateDataSortCmp($aInfoA, $aInfob) {
        if ($aInfoA['sort_index'] == $aInfob['sort_index']) {
            return 0;
        } elseif ($aInfoA['sort_index'] < $aInfob['sort_index']) {
            return -1;
        } else {
            return 1;
        }
    }
}
