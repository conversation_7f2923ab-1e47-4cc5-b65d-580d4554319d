<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\estimateDetail;

use BizLib\Utils\Product;

/**
 * Class Handler
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\estimateDetail
 */
class Handler
{

    /**
     * @param array $aInfo aInfo
     * @return null|AnycarEstimateDetail
     */
    public function select($aInfo) {
        if (Product::isAnyCar($aInfo['order_info']['product_id'])) {
            return new AnycarEstimateDetail($aInfo);
        }

        return null;
    }
}
