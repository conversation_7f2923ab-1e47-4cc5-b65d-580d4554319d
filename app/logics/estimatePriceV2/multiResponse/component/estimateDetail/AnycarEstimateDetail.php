<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\estimateDetail;

use BizLib\Constants\OrderSystem;
use BizLib\Utils\Language;
use PreSale\Logics\order\AnyCarOrderLogic;
use BizLib\Config as NuwaConfig;

/**
 * Class AnycarEstimateDetail
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\estimateDetail
 */
class AnycarEstimateDetail
{

    private $_aInfo;

    private $_sCarLevel;

    private $_aEstimateTextDcmp;

    /**
     * AnycarEstimateDetail constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo     = $aInfo;
        $this->_sCarLevel = $aInfo['order_info']['require_level'];
        $this->_aEstimateTextDcmp = Language::getDecodedTextFromDcmp('config_text-mOrderEstimateNew');
    }

    /**
     * @param array $aResponse aResponse
     * @return mixed
     */
    public function build($aResponse) {
        //  乘客端6.0 anycar偏好选择页面 预估费用详情入口文案&icon
        if (\BizLib\Utils\CarLevel::DIDI_ANY_CAR_CAR_LEVEL == $this->_sCarLevel
            && \BizLib\Utils\Common::isLiteVersion($this->_aInfo['common_info']['app_version'], $this->_aInfo['common_info']['client_type'], '')
        ) {
            $sAnyCarType = AnyCarOrderLogic::getInstance()->getAnyCarType($this->_aInfo['bill_info']['multi_info']);
            if (OrderSystem::TYPE_ANYCAR_FAST == $sAnyCarType) {
                $aLiteIconConfig = NuwaConfig::config('config_lite', 'icon_url');
                $aResponse['estimated_detail_entry_text'] = $this->_aEstimateTextDcmp['estimated_detail_entry_text'] ?? '';
                $aResponse['estimated_detail_entry_icon'] = $aLiteIconConfig['estimated_detail_entry_icon'] ?? '';
            }
        }

        //  预估费用详情入口文案&icon end
        return $aResponse;
    }
}
