<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\athenaGuideInfo;

use Biz<PERSON>ommon\Utils\Horae;
use BizLib\Utils\ProductCategory;
use PreSale\Infrastructure\Repository\Redis\AthenaGuideResultRepository;
use PreSale\Logics\athena\DacheAnycarGuide;
use PreSale\Logics\estimatePriceV2\DecisionLogic;
use PreSale\Logics\estimatePriceV2\multiResponse\component\GuideInfo;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;
use Xiaoju\Apollo\Apollo as ApolloV2;

/**
 * 公共方法
 * Class Common
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\athenaGuideInfo
 */
class Common
{
    const STANDARD_RECOMMEND_THEME_TYPE     = 3;
    const STANDARD_NEW_RECOMMEND_THEME_TYPE = 4;
    const MANNED_VEHICLE_RECOMMEND_TYPE     = 2;

    /**
     * 判断是否是载人车实验
     * @param array $aInfo ainfo
     * @return bool
     */
    public static function isMannedVehicleSuggest($aInfo) {
        $iProductCategory = $aInfo['order_info']['product_category'];
        if (ProductCategory::PRODUCT_CATEGORY_CARPOOL_STATION != $aInfo['order_info']['product_category']) {
            return false;
        }

        $aGuideInfo   = DacheAnycarGuide::getInstance()->getGuideInfoByCategory($iProductCategory);
        $aGuideResult = $aGuideInfo['athena_bubble_guide_result'];
        if (empty($aGuideResult)) {
            return false;
        }

        if (\BizCommon\Constants\AthenaGuide::GUIDE_SCENE_CARPOOL == $aGuideResult['guide_scene'] && self::MANNED_VEHICLE_RECOMMEND_TYPE == $aGuideResult['guide_type']) {
                return true;
        }

        return false;
    }

    /**
     * 判断是否是载人车推荐位样式
     * @param array $aInfo ainfo
     * @return bool
     */
    public static function isMannedVehicleSuggestHighLight($aInfo) {
        if (self::isMannedVehicleSuggest($aInfo)) {
            $iProductCategory = $aInfo['order_info']['product_category'];
            $aDecisionInfo    = DecisionLogic::getInstance()->getProductInfoByCategoryId($iProductCategory);
            if (empty($aDecisionInfo)) {
                $aDecisionInfo = DecisionLogic::getInstance()->getProductGuideInfoByCategoryId($iProductCategory);
            }

            if (isset($aDecisionInfo['recommend_info'])) {
                $aRecommendInfo = $aDecisionInfo['recommend_info'];
                if (6 == $aRecommendInfo['show_type']) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 获取载人车推荐的标签
     * @param array $aInfo ainfo
     * @return array
     */
    public static function getMannedVehicleExtraInfo($aInfo) {
        $iProductCategory = $aInfo['order_info']['product_category'];
        $aGuideInfo       = DacheAnycarGuide::getInstance()->getGuideInfoByCategory($iProductCategory);
        $aGuideResult     = $aGuideInfo['athena_bubble_guide_result'];
        return $aGuideResult['extra'];
    }

    /**
     * 是否是载人车降级
     * @param array $aInfo ainfo
     * @return bool
     */
    public static function isMannedVehicleDegrade($aInfo) {
        return self::isMannedVehicleSuggest($aInfo) && self::conflictWithGuide($aInfo);
    }

    /**
     * 是否是有导流位冲突
     * @param array $aInfo ainfo
     * @return bool
     */
    public static function conflictWithGuide($aInfo) {
        $aGuideInfo = GuideInfo::getInstance()->getGuideProductInfo();
        return !empty($aGuideInfo) && $aGuideInfo['order_info']['estimate_id'] != $aInfo['order_info']['estimate_id'];
    }
    /**
     * 载人车推荐是否是最新的强样式
     * @param array $aInfo ainfo
     * @return bool
     */
    public static function isNewHighLightStyle($aInfo) {
        return (version_compare($aInfo['common_info']['app_version'], '6.2.8') >= 0 && 1 == $aInfo['common_info']['access_key_id']) ||
            (version_compare($aInfo['common_info']['app_version'], '6.2.10') >= 0 && 2 == $aInfo['common_info']['access_key_id']);
    }

    /**
     * 是否在载人车实验中
     * @param array $aInfo ainfo
     * @return bool
     */
    // 注释-待删
//    public static function isInMannedVehicleAppollo($aInfo) {
//        $aOrderInfo     = $aInfo['order_info'] ?? [];
//        $aCommonInfo    = $aInfo['common_info'];
//        $aPassengerInfo = $aInfo['passenger_info'];
//        $aApolloParams  = [
//            'key'              => $aPassengerInfo['pid'],
//            'phone'            => $aPassengerInfo['phone'],
//            'access_key_id'    => $aCommonInfo['access_key_id'],
//            'combo_type'       => $aOrderInfo['combo_type'],
//            'level_type'       => $aOrderInfo['level_type'],
//            'time_period'      => Date('H:i'),
//            'date_time_period' => Date('Y-m-d H:i'),
//            'business_id'      => $aCommonInfo['business_id'],
//            'car_level'        => $aOrderInfo['require_level'],
//            'product_category' => $aOrderInfo['product_category'],
//            'city'             => $aOrderInfo['area'],
//        ];
//        $oFeatureToggle = (new ApolloV2())->featureToggle('feature_oe_new_manned_vehicle_suggest', $aApolloParams);
//        return $oFeatureToggle->allow();
//    }
    /**
     * 是否是小手机
     * @param array $aInfo ainfo
     * @return bool
     */
    public static function isSmallPhone($aInfo) {
        $pixels   = $aInfo['common_info']['pixels'];
        $pixels   = explode('*', $pixels);
        $wHRation = $pixels[0] / $pixels[1];
        $hpx      = $pixels[1];
        if ($wHRation >= 0.56 && $hpx <= 1280) {
            return true;
        }

        return false;
    }
}
