<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\athenaGuideInfo;

use PreSale\Logics\athena\DacheAnycarGuide;
use PreSale\Logics\estimatePriceV2\multiResponse\component\productInfo\Common;
use PreSale\Infrastructure\Repository\Redis\AthenaGuideResultRepository;
use Disf\SPL\Trace;

/**
 * NormalGuideInfo
 */
class NormalGuideInfo
{
    protected $_aInfo;
    protected $_oRequest;

    /**
     * __construct
     *
     * @param array $aInfo ainfo
     * @return void
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
    }

    /**
     * recommend_guide
     *
     * @param array $aResponse aResponse
     * @return array
     */
    public function build($aResponse) {
        $iProductCategory = $this->_aInfo['order_info']['product_category'];
        DacheAnycarGuide::getInstance()->mapToResponse($aResponse, $iProductCategory, ['recommend_guide']);
        $this->_cacheAthenaInfo();
        return $aResponse;
    }

    /**
     * 缓存导流信息
     *
     * @return void
     */
    private function _cacheAthenaInfo() {
        $iProductCategory = $this->_aInfo['order_info']['product_category'];
        $aGuideInfo       = DacheAnycarGuide::getInstance()->getGuideInfoByCategory($iProductCategory);
        if (!empty($aGuideInfo['athena_bubble_guide_result'])) {
            $aGuideResult = [
                'guide_type' => $aGuideInfo['athena_bubble_guide_result']['guide_type'],
                'count_down' => $aGuideInfo['athena_bubble_guide_result']['count_down'],
            ];
            $aGuideResult['timestamp']         = time();
            $aGuideResult['estimate_trace_id'] = Trace::traceId();
            $sEstimateId = Common::getEstimateId($this->_aInfo);
            AthenaGuideResultRepository::setAthenaGuideResult($sEstimateId, $aGuideResult);
        }
    }
}
