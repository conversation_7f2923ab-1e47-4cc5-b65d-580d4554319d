<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\athenaGuideInfo;

use B<PERSON><PERSON>om<PERSON>\Utils\Horae;
use BizLib\Utils\Language;
use PreSale\Logics\estimatePriceV2\ParamsLogic;
use PreSale\Logics\estimatePriceV2\multiResponse\component\productInfo\Common;
use BizLib\Config;
use PreSale\Infrastructure\Repository\Redis\AthenaGuideResultRepository;

/**
 * CarpoolGuideInfo
 */
class CarpoolGuideInfo
{
    const GUIDE_TYPE_LOW_PRICE_CARPOOL_UNSUC_FRIENDS_REC = 5;

    protected $_aInfo;
    protected $_oRequest;
    private $_iGuideScene;
    private $_sSourceEid;

    /**
     * contruct
     * @param array $aInfo ainfo
     */
    public function __construct($aInfo) {
        $this->_aInfo    = $aInfo;
        $this->_oRequest = ParamsLogic::getInstance();
    }

    /**
     *
     * @param array $aResponse aResponse
     * @return array
     */
    public function build($aResponse) {
        $this->_iGuideScene = $this->_oRequest->getRequest('guide_scene');
        $this->_sSourceEid  = $this->_oRequest->getRequest('source_estimate_id');
        if (empty($this->_iGuideScene) || empty($this->_sSourceEid)) {
            return $aResponse;
        }

        $aGuideResult = AthenaGuideResultRepository::getAthenaGuideResult($this->_sSourceEid);
        if (empty($aGuideResult)) {
            return $aResponse;
        }

        $this->_build($aResponse, $aGuideResult);
        return $aResponse;
    }

    /**
     * @param array $aResponse    aResponse
     * @param array $aGuideResult aGuideResult
     * @return void
     */
    private function _build(&$aResponse, $aGuideResult) {
        if (Horae::isLowPriceCarpool($this->_aInfo['order_info']['n_tuple'])) {
            if (\BizCommon\Constants\AthenaGuide::GUIDE_SCENE_CARPOOL == $this->_iGuideScene && self::GUIDE_TYPE_LOW_PRICE_CARPOOL_UNSUC_FRIENDS_REC == $aGuideResult['guide_type']) {
                $iDeadline = $aGuideResult['timestamp'] + $aGuideResult['count_down'];
                $iNow      = time();
                $aGuideConfirmMsg = Config::text('config_text', 'guide_confirm_msg_pinchengle');
                if ($iNow < $iDeadline) {
                    $aPop = $aGuideConfirmMsg['guide_confirm_pop'];
                    $aResponse['guide_confirm_pop'] = [
                        'confirm_button'  => $aPop['confirm_button'],
                        'cancel_button'   => $aPop['cancel_button'],
                        'title'           => $aPop['title'],
                        'sub_title'       => $aPop['sub_title'],
                        'count_down_text' => $aPop['count_down_text'],
                        'count_down'      => round($iDeadline - $iNow),
                    ];
                } else {
                    $aResponse['guide_overdue_toast'] = $aGuideConfirmMsg['guide_overdue_toast'];
                }
            }
        }
    }
}
