<?php
/**
 * Created by PhpStorm.
 * Date: 19/8/2
 * Time: 13:24
 * @category Category
 *
 * @package FileDirFileName
 *
 * <AUTHOR> <<EMAIL>>
 * @link ${link}
 *
 * @link ${link}
 */
namespace PreSale\Logics\estimatePriceV2\multiResponse\component;

use BizLib\Utils\Language;
use BizLib\Log as NuwaLog;

/**
 * Class LowPriceCarpoolGuide
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component
 * @property LowPriceCarpoolGuide $LowPriceCarpoolGuide
 */
class LowPriceCarpoolGuide
{
    private static $_aAthenaInfo = [];

    /**
     * @Desc:
     * @param  mixed[] $aAllInfo      Array structure to count the elements of.
     * @param mixed[] $aDecisionResp Array structure to count the elements of.
     * @return array
     * @property set $set
     * @Author:<EMAIL>
     */
    public static function set($aAllInfo, $aDecisionResp) {
        if (empty($aAllInfo)||empty($aDecisionResp)) {
            NuwaLog::notice('low price carpool athena_info set failed'.json_encode($aDecisionResp));
            return [];
        }

        $aSelectProduct = $aDecisionResp['selected_item'] ?? current($aDecisionResp['product_decisions']);
        $fPrice         = 0.0;
        foreach ($aAllInfo as $aInfo) {
            if (\BizCommon\Utils\Horae::isLowPriceCarpool($aInfo['order_info']['n_tuple']) &&'flash' == $aInfo['order_info']['menu_id']) {
               // $sCarLevel    = $aInfo['order_info']['require_level'];
                $fPrice = $aInfo['activity_info']['0']['discount_fee'];// $aInfo['bill_info']['bills'][$sCarLevel]["cap_price"];
                break;
            }
        }

        if ($fPrice <= 0) {
            return [];
        }

        self::$_aAthenaInfo = array(
            'errno'       => 0,
            'errmsg'      => 'ok',
            'data'        => array('guide' => [self::_buildLowPriceCarpoolGuideInfo($aSelectProduct,$fPrice)],),
            'bubble_flag' => true,
        );
        NuwaLog::notice('low price carpool athena_info set sucess');
        return self::$_aAthenaInfo;
    }

    /**
     * @Desc:
     * @param mixed[] $aSelectProduct Array structure to count the elements of.
     * @param mixed[] $fPrice         Array structure to count the elements of.
     * @return array
     * @property _buildLowPriceCarpoolGuideInfo $_buildLowPriceCarpoolGuideInfo
     * @Author:<EMAIL>
     */
    private static function _buildLowPriceCarpoolGuideInfo($aSelectProduct, $fPrice) {
        $aShowText          = json_decode(Language::getTextFromDcmp('config_carpool-low_price_carpool_guide_info',['-']),true);
        $aShowText['title'] = sprintf($aShowText['title'],$fPrice);
        $aFromItem          = [
            'source_product'    => (int)($aSelectProduct['business_id']),
            'source_level'      => (int)($aSelectProduct['require_level']),
            'source_combo_type' => (int)($aSelectProduct['combo_type']),
        ];

        $aFrom = [$aFromItem,];

        $aTo = array(
            'guide_product'    => (int)($aSelectProduct['business_id']),
            'guide_level'      => ($aSelectProduct['require_level']),
            'guide_combo_type' => (int)($aSelectProduct['combo_type']),
            'guide_action'     => 1,
            'tab_switch'       => 1,
            'default_show'     => 0,
        );

        return array(
            'show_text' => $aShowText,
            'from'      => $aFrom,
            'to'        => $aTo,
        );
    }
}
