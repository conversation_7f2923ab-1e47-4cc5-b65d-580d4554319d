<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\customService;

use BizLib\Config as NuwaConfig;
use PreSale\Logics\estimatePriceV2\multiResponse\MainHandler;
use BizLib\Constants;

/**
 * Class NormalCustomService
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\customService
 */
class NormalCustomService
{
    protected $_aInfo = [];
    protected $_sCarLevel;
    protected $_iProductId;
    protected $_bIsDacheAnycar;

    /**
     * NormalCustomService constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo          = $aInfo;
        $this->_sCarLevel      = $this->_aInfo['order_info']['require_level'];
        $this->_iProductId     = $this->_aInfo['order_info']['product_id'];
        $this->_bIsDacheAnycar = Constants\Common::MENU_DACHE_ANYCAR == $this->_aInfo['order_info']['menu_id'] ? true : false;
    }

    /**
     * @param array $aResponse response
     * @return mixed
     */
    public function build($aResponse) {
        $iComboType = $this->_aInfo['order_info']['combo_type'];
        $aResponse['scene_data'] = Common::getServiceInfo($this->_iProductId, $this->_sCarLevel, $iComboType, $this->_bIsDacheAnycar);
        $aConfig = NuwaConfig::text('scene_data','head');
        if (!empty($aConfig)) {
            $aResponse['scene_data_head']      = $aConfig['head'];
            $aResponse['scene_data_head_link'] = $aConfig['link'];
        }

        return $aResponse;
    }
}
