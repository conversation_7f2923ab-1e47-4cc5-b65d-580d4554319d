<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\customService;

use BizLib\Constants\Common as ConstantsCommon;
use PreSale\Logics\taxi\TaxiPeakFee;

/**
 * Class TaxiCustomService
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\customService
 */
class TaxiCustomService
{
    protected $_aInfo = [];
    protected $_sCarLevel;
    protected $_iProductId;
    protected $_iProductCategory;
    protected $_iCityId;
    protected $_bIsDacheAnycar;

    /**
     * NormalCustomService constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo            = $aInfo;
        $this->_sCarLevel        = $this->_aInfo['order_info']['require_level'];
        $this->_iProductId       = $this->_aInfo['order_info']['product_id'];
        $this->_iProductCategory = $this->_aInfo['order_info']['product_category'];
        $this->_iCityId          = (int)$this->_aInfo['order_info']['area'];
        $this->_bIsDacheAnycar   = ConstantsCommon::MENU_DACHE_ANYCAR == $this->_aInfo['order_info']['menu_id'];
    }

    /**
     * @param array $aResponse response
     * @return mixed
     */
    public function build(array $aResponse) {
        //非出租车流量直接返回
        if (PRODUCT_CATEGORY_UNIONE != $this->_iProductCategory) {
            return $aResponse;
        }

        $oTaxiPeakFeeClient = TaxiPeakFee::getPoolInstance($this->_aInfo);

        list($aScene, $iIndex) = $oTaxiPeakFeeClient->getSSSEData();

        //如果是是空说明SSSE没开城
        if (empty($aScene)) {
            return $aResponse;
        }

        //1. 开城状态
        $bStatus = $oTaxiPeakFeeClient->getStatus();
        //2. 有出口状态
        $bIsInterActive = $oTaxiPeakFeeClient->getIsInterActive();

        //以上几种情况渲染有出口文案
        if ($bStatus && $bIsInterActive) {
            if ($oTaxiPeakFeeClient->getIsFirstEstimate()) {
                $iDefaultSelected = 1;
            } else {
                $iDefaultSelected = $oTaxiPeakFeeClient->getIsUserSelect() ? 1 : 0;
            }

            // 没有选的情况下要从sps取价格
            if (1 != $iDefaultSelected) {
                $sPrice = $oTaxiPeakFeeClient->getPeakPriceAmount($this->_getBillPeakFee(), $this->_getBillPeakDiscount());
            } else {
                $sPrice = $oTaxiPeakFeeClient->getPeakPriceAmount();
            }

            $aWayOutConf = $oTaxiPeakFeeClient->getWayOutConf();
            $sDesc       = sprintf(
                $aWayOutConf['content'] ?? '',
                $sPrice
            ) ?? '';

            $urlParam = [
                'city_id'          => $this->_aInfo['order_info']['area'],
                'product_category' => $this->_aInfo['order_info']['product_category'],
                'product_id'       => $this->_aInfo['order_info']['product_id'],
                'estimate_id'      => $this->_aInfo['order_info']['estimate_id'],
            ];
            $sUrl     = sprintf(
                $aWayOutConf['info_url'] ?? '',
                http_build_query($urlParam)
            ) ?? '';

            $aScene[$iIndex]['unit_price']     = $sDesc;
            $aScene[$iIndex]['selected_count'] = $iDefaultSelected;
            $aScene[$iIndex]['title']          = $aWayOutConf['title'] ?? '';
            $aScene[$iIndex]['desc']           = $sDesc;
            $aScene[$iIndex]['icon']           = $aWayOutConf['icon'] ?? '';
            $aScene[$iIndex]['detail']         = $sUrl;  // 出租车峰期加价价格说明h5页面
            $aResponse['scene_data']           = $aScene;
        } else {
            $aResponse['scene_data'] = [];
        }

        //Step4: 渲染结果
        return $aResponse;
    }

    /**
     * 账单返回的峰期费用
     * @return int 峰期加价费用（单位：分）
     */
    private function _getBillPeakFee() {
        $iRequireLevel = $this->_aInfo['order_info']['require_level'];
        $iRes          = $this->_aInfo['bill_info']['bills'][$iRequireLevel]['fee_detail_info']['taxi_peak_price'] ?: 0;
        return bcmul($iRes,100);
    }

    /**
     * 账单返回的峰期费用
     * @return int 峰期加价费用（单位：分）
     */
    private function _getBillPeakDiscount() {
        $iRequireLevel = $this->_aInfo['order_info']['require_level'];
        $iRes          = $this->_aInfo['bill_info']['bills'][$iRequireLevel]['fee_detail_info']['taxi_peak_discount'] ?: 0;
        return bcmul($iRes,100);
    }
}
