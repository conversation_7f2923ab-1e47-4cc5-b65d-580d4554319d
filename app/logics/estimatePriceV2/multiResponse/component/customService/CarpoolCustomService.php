<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\customService;

use BizLib\Constants;

/**
 * Class CarpoolCustomService
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\customService
 */
class CarpoolCustomService
{
    protected $_aInfo = [];
    protected $_sCarLevel;
    protected $_iProductId;
    protected $_bIsDacheAnycar;

    /**
     * CarpoolCustomService constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo          = $aInfo;
        $this->_sCarLevel      = $this->_aInfo['order_info']['require_level'];
        $this->_iProductId     = $this->_aInfo['order_info']['product_id'];
        $this->_bIsDacheAnycar = Constants\Common::MENU_DACHE_ANYCAR == $this->_aInfo['order_info']['menu_id'] ? true : false;
    }

    /**
     * @param array $aResponse response
     * @return mixed
     */
    public function build($aResponse) {
        $iComboType        = $this->_aInfo['order_info']['combo_type'];
        $iCarpoolComboType = isset($aInfo['bill_info']['product_infos'][$this->_sCarLevel]['combo_type']) ? $this->_aInfo['bill_info']['product_infos'][$this->_sCarLevel]['combo_type'] : $iComboType;

        $iCarpoolComboType = !empty($iCarpoolComboType) ? $iCarpoolComboType : Constants\Horae::TYPE_COMBO_CARPOOL;

        $aResponse['scene_data'] = Common::getServiceInfo($this->_iProductId, $this->_sCarLevel, $iCarpoolComboType, $this->_bIsDacheAnycar);
        return $aResponse;
    }
}
