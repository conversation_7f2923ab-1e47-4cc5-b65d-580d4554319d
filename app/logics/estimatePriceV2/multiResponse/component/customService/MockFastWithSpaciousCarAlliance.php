<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\customService;

use BizLib\Libraries\RedisDB;
use BizLib\Utils\Language;
use BizLib\Utils\ProductCategory;
use Dirpc\SDK\PreSale\PreferTag;
use Dirpc\SDK\PreSale\ServicePopup;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\estimatePriceV2\DecisionLogic;
use PreSale\Logics\estimatePriceV2\multiResponse\component\basicFeeMsg\SpaciousCarAllianceFeeMsg;
use PreSale\Logics\estimatePriceV2\multiResponse\component\SortSelection;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use PreSale\Logics\scene\custom\CustomLogic;
use BizLib\Utils\Common;

/**
 * Class MockFastWithSpaciousCarAlliance
 */
class MockFastWithSpaciousCarAlliance
{

    const REDIS_EXPIRE_EID_CACHE = 300; //5min

    protected $_aInfo = [];
    /**
     * MockFastWithSpaciousCarAlliance constructor.
     * @param array $aInfo $aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
    }

    /**
     * @param array $aResponse $aResponse
     * @return mixed
     */
    public function build($aResponse) {
        if (!MainDataRepo::isNativeClient($this->_aInfo['common_info']['access_key_id'])) {
            return $aResponse;
        }

        $aSpaciousCarAllianceInfo = MainDataRepo::getSpaciousCarAlliance();

        $aBillInfo      = $aSpaciousCarAllianceInfo['bill_info']['bills'][$aSpaciousCarAllianceInfo['order_info']['require_level']];
        $aFeeDetailInfo = $aBillInfo['fee_detail_info'];
        list($bIsHasSelectFee, $bIsHasDiscount, $sSelectionFee) = SpaciousCarAllianceFeeMsg::getSpaciousCarInfo($aFeeDetailInfo);

        if (!$bIsHasSelectFee) {
            return $aResponse;
        }

        $bCacheSucc = $this->_setEstimateIDs($this->_aInfo['bill_info']['estimate_id'], $aSpaciousCarAllianceInfo['bill_info']['estimate_id']);
        if (!$bCacheSucc) {
            return $aResponse;
        }

        $aSceneDataConf = Language::getDecodedTextFromDcmp('config_text-custom_service_spacious_car_alliance');
        $sAbTitle       = self::getTitle($this->_aInfo);
        $aSceneData     = [
            'max'            => 1,
            'title'          => !empty($sAbTitle) ? $sAbTitle : $aSceneDataConf['title'],
            'unit'           => $aSceneDataConf['unit'].'            ',
            'price'          => $aSceneDataConf['price'],
            'detail'         => $aSceneDataConf['detail'],
            'id'             => CustomLogic::CUSTOM_SERVICE_MOCK_SPACIOUS_CAR,
            'icon'           => $aSceneDataConf['icon'],
            'selected_count' => (int)$this->_getSpaciousCarSelected(),
            'popup'          => new ServicePopup(),
        ];

        if ($bIsHasDiscount) {
            $aSceneData['unit_price'] = Language::replaceTag($aSceneDataConf['unit_price_first'], ['fee_amount' => $sSelectionFee]);
        } else {
            $aSceneData['unit_price'] = Language::replaceTag($aSceneDataConf['unit_price'], ['fee_amount' => $sSelectionFee]);
        }

        $aResponse['scene_data'][] = $aSceneData;
        $aResponse['prefer_data']['prefer_select_count'] = (int)$this->_getSpaciousCarSelected();
        $aResponse['prefer_data']['prefer_total_count']  = 1;
        $aResponse['prefer_data']['prefer_display_tags'] = [[
            'display_icon'  => $aSceneDataConf['icon'],
            'display_names' => [$aSceneDataConf['prefer_display_tag']],
        ],
        ];
        return $aResponse;
    }

    /**
     * @param string $sFastCarEID  快车EID
     * @param string $sSpaciousEID 车大联盟EID
     * @return mixed
     */
    private function _setEstimateIDs($sFastCarEID, $sSpaciousEID) {

        if (empty($sFastCarEID) || empty($sSpaciousEID)) {
            return false;
        }

        $sRedisKey = Common::getRedisPrefix(P_SPACIOUS_CAR_EID).$sFastCarEID;
        return RedisDB::getInstance()->setex($sRedisKey, self::REDIS_EXPIRE_EID_CACHE, $sSpaciousEID);
    }

    /**
     * @return mixed|string
     */
    public static function getTitle($aInfo) {
        $aParams = [
            'uid'   => $aInfo['passenger_info']['uid'],
            'city'  => $aInfo['order_info']['area'],
            'key'   => $aInfo['passenger_info']['uid'],
            'pid'   => $aInfo['passenger_info']['pid'],
            'phone' => $aInfo['passenger_info']['phone'],
        ];
        $oApollo = Apollo::getInstance()->featureToggle('ab_spacious_car_alliance_info_msg_v3', $aParams);
        if (!$oApollo->allow()) {
            return '';
        }

        switch ($oApollo->getGroupName()) {
            case 'time':
                $iDepartureTime = $aInfo['order_info']['departure_time'];
                $aTimeConfig    = json_decode($oApollo->getParameter('time_condition', ''),true);
                if (empty($aTimeConfig) || !is_array($aTimeConfig)) {
                    return '';
                }

                foreach ($aTimeConfig as $config) {
                    if (self::checkTime($iDepartureTime, $config)) {
                        return $config['content'];
                    }
                }
                break;
            case 'distance':
                $iDriverMeter    = $aInfo['bill_info']['driver_metre']; // 订单距离(米)
                $aDistanceConfig = json_decode($oApollo->getParameter('distance_condition', ''),true);
                if ($iDriverMeter / 1000 >= $aDistanceConfig['gte']) {
                    return $aDistanceConfig['gte_content'];
                } else {
                    return $aDistanceConfig['lt_content'];
                }

            default:
                return $oApollo->getParameter('default', '');
        }

        return '';
    }

    /**
     * 判断出发时间是否在当前时间段
     * @param int   $iDepartureTime 出发时间
     * @param array $config         配置时间段
     * @return bool 布尔
     */
    public static function checkTime($iDepartureTime, $config) :bool {
        if (empty($config['start_time']) || empty($config['end_time']) || empty($iDepartureTime)) {
            return false;
        }

        if (self::getTimestamp($config['start_time']) <= $iDepartureTime
            && $iDepartureTime < self::getTimestamp($config['end_time'])
        ) {
            return true;
        }

        return false;
    }

    /**
     * 获取配置时间的时间戳
     * @param string $sHours 配置时间"24:00"
     * @return int 时间
     */
    public static function getTimestamp(string $sHours) :int {
        return strtotime(date('Y-m-d') .$sHours);
    }

    /**
     * @return bool
     */
    private function _getSpaciousCarSelected() {
        $bSpaciousCarSelect = false;
        $aPreferenceProduct = json_decode($this->_aInfo['preference_product'], true);
        foreach ($aPreferenceProduct as $aProduct) {
            if (ProductCategory::PRODUCT_CATEGORY_SPACIOUS_CAR == $aProduct['product_category']) {
                $bSpaciousCarSelect = true;
                break;
            }
        }

        if (!$bSpaciousCarSelect) {
            $aDdsInfo = DecisionLogic::getInstance()->getProductInfoByCategoryId(ProductCategory::PRODUCT_CATEGORY_SPACIOUS_CAR);
            if (SortSelection::DACHE_ANYCAR_ITEM_SELECTED == $aDdsInfo['is_selected']) {
                $bSpaciousCarSelect = true;
            }
        }

        return $bSpaciousCarSelect;
    }
}
