<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\customService;

use Dirpc\SDK\PreSale\MultiEstimatePriceRequest as MultiV2Request;
use BizLib\Utils\Request;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use PreSale\Logics\estimatePriceV2\ParamsLogic;
use PreSale\Logics\estimatePriceV2\multiRequest\ProductList;
use PreSale\Logics\estimatePriceV2\PersonalizedCustomServiceLogic;
use BizLib\Constants\OrderSystem;

/**
 * Class Common
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\customService
 */
class Common
{
    /**
     * @param int    $iProductId product id
     * @param string $sCarLevel  car level
     * @param int    $iComboType combo type
     * @return array
     */
    public static function getServiceInfo($iProductId, $sCarLevel, $iComboType, $bIsDacheAnycar) {
        $aCustomServiceInfo = PersonalizedCustomServiceLogic::getInstance()->getSceneData();
        $aServiceInfo       = array();

        if (empty($aCustomServiceInfo)) {
            return $aServiceInfo;
        }

        // 屏蔽echo端、优步快车和优步优享
        if (in_array($iProductId, array(OrderSystem::PRODUCT_ID_UBER_FAST_CAR, OrderSystem::PRODUCT_ID_UBER_YOUXIANG_CAR))) {
            return $aServiceInfo;
        }

        $oParam   = ParamsLogic::getInstance();
        $aOneConf = $oParam->getOneConf();
        if ($bIsDacheAnycar) { // 是否是6.0 打车anycar 顶导；6.0 oneconf 走 ProductList 新逻辑
            $oReq        = Request::getInstance();
            $oEstRequest = new MultiV2Request();
            $oEstRequest->mergeFromJsonArray($oReq->get());
            $aOneConf = ProductList::getInstance($oEstRequest)->getBaseProductList();
        }

        foreach ($aOneConf as $iIndex => $aOneConfItem) {
            if ($iProductId == $aOneConfItem['product_id']
                && $sCarLevel == $aOneConfItem['require_level']
                && $iComboType == $aOneConfItem['combo_type']
            ) {
                $aServiceInfo = $aCustomServiceInfo[$iIndex];
            }
        }

        return $aServiceInfo;
    }
}
