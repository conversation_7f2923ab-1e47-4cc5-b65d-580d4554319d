<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\customService;

use BizCommon\Utils\Order;
use BizLib\Utils\Horae;
use BizLib\Utils\Product;
use PreSale\Logics\estimatePriceV2\multiResponse\component\basicFeeMsg\TaxiBasicFeeMsg;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;

/**
 * Class Handler
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\customService
 */
class Handler
{
    /**
     * @param array $aInfo aInfo
     * @return null|CarpoolCustomService|NormalCustomService|SpecialRateCustomService|MockFastWithSpaciousCarAlliance|TaxiCustomService
     */
    public static function select($aInfo) {
        if (Util::isCarpool($aInfo)) {
            return new CarpoolCustomService($aInfo);
        }

        if (Order::isSpecialRateV2($aInfo['order_info'])) {
            return new SpecialRateCustomService($aInfo);
        }

        if (Util::isFastCarWithSpaciousCarAlliance($aInfo)) {
            return new MockFastWithSpaciousCarAlliance($aInfo);
        }

        // 出租车业务线
        if (Product::isUniTaxi($aInfo['order_info']['n_tuple']['product_id'])) {
            return new TaxiCustomService($aInfo);
        }

        return new NormalCustomService($aInfo);
    }
}
