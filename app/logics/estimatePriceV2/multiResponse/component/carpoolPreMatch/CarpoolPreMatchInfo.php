<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\carpoolPreMatch;

use Biz<PERSON>om<PERSON>\Constants\OrderNTuple;
use BizCommon\Logics\Carpool\CarpoolBrand;
use BizCommon\Utils\Horae;
use BizLib\Client\DosClient;
use BizLib\Client\OrderSystemClient;
use BizLib\Constants\OrderSystem;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Utils\Language;
use PreSale\Domain\Model\Order\Order;
use PreSale\Logics\estimatePriceV2\response\SceneResponseLogicV2;
use BizLib\Libraries\RedisDB;
use BizLib\Utils\Common;
use BizLib\Log;
use BizLib\Utils\UtilHelper;
use PreSale\Infrastructure\Repository\Rpc\OrderDosRepository;
use BizCommon\Constants\Invitation;
use Nuwa\ApolloSDK\Apollo;

/**
 * Class CarpoolPreMatchInfo
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\carpoolPreMatch
 */
class CarpoolPreMatchInfo
{
    const ETS_EXPIRE_TIME           = 10 * 60;
    const MATCH_RESULT_EXPIRE_TIME  = 10 * 60;
    private static $_aInvitationMap = [
        Invitation::INVITATION_MATCH_CODE_SUCCESS      => Invitation::INVITATION_MATCH_RESULT_SUCCESS,
        Invitation::INVITATION_MATCH_CODE_OUT_THE_WAY  => Invitation::INVITATION_MATCH_RESULT_NOT_MATCH,
        Invitation::INVITATION_MATCH_CODE_STOP_CARPOOL => Invitation::INVITATION_MATCH_RESULT_STOP_CARPOOL,
    ];

    private $_aInfo;

    /**
     * CarpoolPreMatchInfo constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
    }


    /**
     * 构建拼车预匹配信息
     * @param array $aResponse aResponse
     * @return array
     */
    public function build($aResponse) {
        $aEtdInfo = SceneResponseLogicV2::getInstance()->getCarpoolEtdInfo($this->_aInfo);
        if (isset($aEtdInfo)) {
            $aResponse['carpool_etd'] = $aEtdInfo;
        }

        $this->_buildEts();
        $aResponse = $this->_buildInvitationRes($aResponse);
        $aResponse = $this->_buildBubbleRec($aResponse);
        return $aResponse;
    }

    /**
     * 缓存ets信息
     * @return void
     */
    private function _buildEts() {
        //非实时单不出ETS
        if (OrderSystem::TYPE_ORDER_NOW != $this->_aInfo['order_info']['order_type']) {
            return;
        }

        $aEtsInfo = SceneResponseLogicV2::getInstance()->getCarpoolEts($this->_aInfo);
        if (empty($aEtsInfo) || !isset($aEtsInfo['ets_type']) || empty($aEtsInfo['ets_type']) || !isset($aEtsInfo['time_window'])) {
            return;
        }

        $sEstimateID = $this->_aInfo['bill_info']['estimate_id'];
        $sKey        = Common::getRedisPrefix(P_CARPOOL_ETS_INFO) . $sEstimateID;
        $ret         = RedisDB::getInstance()->setex($sKey, self::ETS_EXPIRE_TIME, json_encode($aEtsInfo));
        if (!$ret) {
            Log::warning(
                \BizLib\ErrCode\Msg::formatArray(
                    \BizLib\ErrCode\Code::E_COMMON_REDIS_SET_FAIL,
                    [
                        'key'   => $sKey,
                        'value' => json_encode($aEtsInfo),
                    ]
                )
            );
        }
    }

    /**
     * 邀约同行信息处理
     * @param array $aResponse response
     * @return array
     */
    private function _buildInvitationRes($aResponse) {
        //过滤订单，拼成乐邀约同行
        if (!in_array($this->_aInfo['order_info']['invitation_type'],[OrderNTuple::INVITATION_TYPE_LOW_CARPOOL_PRICE, OrderNTuple::INVITATION_TYPE_INTER_CITY])
            || empty($this->_aInfo['order_info']['invitation_order'])
        ) {
            return $aResponse;
        }

        //拼成乐默认返回值
        if (OrderNTuple::INVITATION_TYPE_LOW_CARPOOL_PRICE == $this->_aInfo['order_info']['invitation_type']) {
            $aResponse['invitation_match_info'] = ['match_code' => Invitation::INVITATION_MATCH_RESULT_NOT_MATCH];
        }

        //订单号错误
        $aInviteOrderIds = UtilHelper::genLowIntOrderIdV2((int)$this->_aInfo['order_info']['invitation_order']);
        if (empty($aInviteOrderIds['oid']) || empty($aInviteOrderIds['district'])) {
            Log::warning(
                Msg::formatArray(
                    Code::E_COMMON_PARAM_ERROR,
                    [
                        'invitation_type' => $this->_aInfo['order_info']['invitation_type'],
                        'invite_order'    => $this->_aInfo['order_info']['invitation_order'],
                    ]
                )
            );
            return $aResponse;
        }

        //订单信息读取失败
        $aRet = (new OrderSystemClient())->getOrderInfo($aInviteOrderIds['oid'], $aInviteOrderIds['district']);
        if (empty($aRet['result']['order_info']) || 0 != $aRet['result']['errno']) {
            Log::warning(
                Msg::formatArray(
                    Code::E_ORDER_GET_ORDER_INFO_ERROR,
                    [
                        'invitation_type' => $this->_aInfo['order_info']['invitation_type'],
                        'invite_order'    => $this->_aInfo['order_info']['invitation_order'],
                        'ret'             => json_encode($aRet),
                    ]
                )
            );
            return $aResponse;
        }

        $aInviteOrderInfo = $aRet['result']['order_info'];
        $aCacheData       = [
            'order_type'      => $aInviteOrderInfo['type'],
            'invitation_type' => $this->_aInfo['order_info']['invitation_type'],
            'invite_order'    => $this->_aInfo['order_info']['invitation_order'],
        ];
        if (OrderNTuple::INVITATION_TYPE_LOW_CARPOOL_PRICE == $this->_aInfo['order_info']['invitation_type']) {
            $aMatchRes = SceneResponseLogicV2::getInstance()->getInvitationInfo($this->_aInfo);
            //路线类型不匹配
            if ((Horae::isPoolFullGoStyle($aInviteOrderInfo) && !Horae::isPoolFullGoStyle($this->_aInfo['order_info']))
                || (Horae::isPoolFullGoStyle($this->_aInfo['order_info']) && !Horae::isPoolFullGoStyle($aInviteOrderInfo))
            ) {
                $iMatchCode = Invitation::INVITATION_MATCH_RESULT_DIFF_ROUTE_TYPE;
            } else {
                $iMatchCode = self::$_aInvitationMap[$aMatchRes['status_code']] ?? Invitation::INVITATION_MATCH_RESULT_NOT_MATCH;
            }

            $sDepartureRange = json_encode(
                [
                    $aMatchRes['estimate_pick_window']['beg_time'],
                    $aMatchRes['estimate_pick_window']['end_time'],
                ]
            );

            //根据匹配结果获取配置
            $aInvitationMatchInfo = [
                'match_code'        => $iMatchCode,
                'invite_order_type' => $aInviteOrderInfo['type'],
                'departure_range'   => $sDepartureRange,
            ];

            $aConfig = $this->_getMatchConfig($iMatchCode, $aMatchRes['estimate_pick_window']);
            $aInvitationMatchInfo = array_merge($aInvitationMatchInfo, $aConfig);
            //设置缓存
            $aCacheData['match_code']      = $iMatchCode;
            $aCacheData['departure_range'] = $sDepartureRange;

            //todo 已有多少人出发的tip
            $aResponse['invitation_match_info'] = $aInvitationMatchInfo;
        }

        $this->_setInvitationCache($aCacheData);
        return $aResponse;
    }

    /**
     * @param array $aCacheData aCacheData
     * @return mixed
    */
    private function _setInvitationCache($aCacheData) {
        $sKey = Common::getRedisPrefix(P_INVITATION_MATCH_INFO) . $this->_aInfo['bill_info']['estimate_id'];
        $ret  = RedisDB::getInstance()->setex($sKey, self::MATCH_RESULT_EXPIRE_TIME, json_encode($aCacheData));
        if (!$ret) {
            Log::warning(
                \BizLib\ErrCode\Msg::formatArray(
                    \BizLib\ErrCode\Code::E_COMMON_REDIS_SET_FAIL,
                    [
                        'key'   => $sKey,
                        'value' => json_encode($aCacheData),
                    ]
                )
            );
        }
    }

    /**
     * @param int   $iMatchCode      code
     * @param array $aDepartureRange departure_range
     * @return array
     */
    private function _getMatchConfig($iMatchCode, $aDepartureRange) {
        $aParam = $this->_aInfo['order_info'];
        $aParam['passenger_phone'] = $this->_aInfo['passenger_info']['phone'];
        list($ok, $aConfig)        = CarpoolBrand::carpoolBrandApolloConfig('invitation_product_conf', 'estimate_config', $this->_aInfo['common_info']['app_version'], $aParam);
        if (!$ok || empty($aConfig)) {
            Log::warning(
                Msg::formatArray(
                    Code::E_COMMON_APOLLO_CONFIG_FAIL,
                    [
                        'namespace' => 'invitation_product_conf',
                        'file'      => 'estimate_config',
                    ]
                )
            );
            return [];
        }

        foreach ($aConfig['match_result'] as $item) {
            if ($iMatchCode == $item['code']) {
                $aButton = $item['button'];
            }
        }

        if (!empty($aDepartureRange)) {
            $sDepartureTxt = $aConfig['departure_txt'];
            $sLeft         = date('H:i', $aDepartureRange['beg_time']);
            $sRight        = date('H:i', $aDepartureRange['end_time']);
            $sDepartureTxt = Language::replaceTag($sDepartureTxt,['left' => $sLeft, 'right' => $sRight]);
        }

        return [
            'departure_text' => $sDepartureTxt ?? '',
            'button'         => $aButton ?? [],
        ];
    }

    /**
     * 冒泡拼友推荐
     * @param array $aResponse res
     * @return mixed
     */
    private function _buildBubbleRec($aResponse) {
        if (!Horae::isLowPriceCarpool($this->_aInfo['order_info'])) {
            return $aResponse;
        }

        $aInfo = $this->_aInfo;

        $iComboType        = $aInfo['order_info']['combo_type'];
        $sCarLevel         = $aInfo['order_info']['require_level'];
        $iCarpoolComboType = isset($aInfo['bill_info']['product_infos'][$sCarLevel]['combo_type']) ? $aInfo['bill_info']['product_infos'][$sCarLevel]['combo_type'] : $iComboType;
        $iProductId        = \BizLib\Utils\Product::getProductIdByBusinessId($aInfo['order_info']['business_id']);

        $aRecInfo = SceneResponseLogicV2::getInstance()->getBubbleRec($iCarpoolComboType, $sCarLevel, $iProductId);
        if (empty($aRecInfo) || empty($aRecInfo['peer_type']) || empty($aRecInfo['expire_time'])) {
            return $aResponse;
        }

        $aParam = $this->_aInfo['order_info'];
        $aParam['passenger_phone'] = $this->_aInfo['passenger_info']['phone'];
        $aConfig = CarpoolBrand::text('config_carpool', 'bubble_recommend_friends', $this->_aInfo['common_info']['app_version'], $aParam);
        if (empty($aConfig)) {
            Log::warning(
                Msg::formatArray(
                    Code::E_COMMON_TEXT_TO_DCMP_MISS,
                    [
                        'name' => 'config_carpool',
                        'key'  => 'bubble_recommend_friends',
                    ]
                )
            );
            return $aResponse;
        }

        $aRec = $aConfig[$aRecInfo['peer_type']];
        $aResponse['recommend_friends'] = [
            'icon_uri' => $aRec['icon'],
            'tip_text' => $aRec['tip_text'],
            'tip_time' => $aRecInfo['expire_time'],
        ];
        return $aResponse;
    }
}
