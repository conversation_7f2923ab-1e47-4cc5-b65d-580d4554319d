<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\carpoolPreMatch;

use PreSale\Logics\estimatePriceV2\multiResponse\Util;

/**
 * Class Handler
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\carpoolPreMatch
 */
class Handler
{
    /**
     * @param array $aInfo aInfo
     * @return CarpoolPreMatchInfo
     */
    public static function select($aInfo) {
        if (Util::isCarpool($aInfo)) {
            return new CarpoolPreMatchInfo($aInfo);
        }
    }
}
