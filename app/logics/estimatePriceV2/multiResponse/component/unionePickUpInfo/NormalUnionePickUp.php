<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\unionePickUpInfo;

use BizLib\Constants\Horae;
use BizLib\Constants\OrderSystem;
use BizLib\Utils\CarLevel;
use BizLib\Utils\Language;
use BizLib\Utils\Product;
use Disf\SPL\Trace;
use Nuwa\ApolloSDK\Apollo;
use Xiaoju\Apollo\ApolloConstant;

/**
 * Class NormalControllerInfo
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\controllerInfo
 */
class NormalUnionePickUp
{
    protected $_aInfo;

    /**
     * NormalControllerInfo constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
    }

    /**
     * @param array $aResponse aResponse
     * @return mixed
     */
    public function build($aResponse) {

        if (version_compare((string)($this->_aInfo['common_info']['app_version']), '5.2.50') < 0) {
            return $aResponse;
        }

        if (!Apollo::getInstance()->featureToggle(
            'unione_pickup_by_meter_V1',
            [
                'key'   => $this->_aInfo['passenger_info']['pid'],
                'pid'   => $this->_aInfo['passenger_info']['pid'],
                'city'  => $this->_aInfo['order_info']['area'],
                'phone' => $this->_aInfo['passenger_info']['phone'],
            ]
        )->allow()
        ) {
            return $aResponse;
        }

        $iProductId       = $this->_aInfo['order_info']['product_id'];
        $sCarLevel        = $this->_aInfo['order_info']['require_level'];
        $aBillProductInfo = $this->_aInfo['bill_info']['product_infos'][$sCarLevel];
        $iComboType       = $aBillProductInfo['combo_type'] ?? ($this->_aInfo['order_info']['combo_type'] ?? 0);
        $iOrderType       = $this->_aInfo['order_info']['order_type'];
        $iSpecialPrice    = $this->_aInfo['order_info']['n_tuple']['is_special_price'];

        if (Product::PRODUCT_ID_UNIONE_TAXI != $iProductId
            || CarLevel::DIDI_UNITAXI_PUTONG_CAR_LEVEL != $sCarLevel
            || Horae::TYPE_COMBO_DEFAULT != $iComboType
            || $iSpecialPrice
            || OrderSystem::TYPE_ORDER_NOW != $iOrderType
        ) {
            return $aResponse;
        }

        $aApolloUser    = array(ApolloConstant::APOLLO_INDIVIDUAL_ID => Trace::traceId(), 'city' => $this->_aInfo['order_info']['area']);
        $oFeatureToggle = Apollo::getInstance()->featureToggle('taxi_notfree_pick_test', $aApolloUser);
        if (!$oFeatureToggle->allow() || 'treatment_group' == $oFeatureToggle->getGroupName()) {
            $aResponse['unione_pickup_by_meter_info'] = json_decode(Language::getTextFromDcmp('config_passenger-unione_pickup_by_meter_info'),true);
        }

        return $aResponse;
    }
}
