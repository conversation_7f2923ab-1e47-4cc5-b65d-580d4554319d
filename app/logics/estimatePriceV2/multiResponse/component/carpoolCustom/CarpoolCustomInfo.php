<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\carpoolCustom;

use BizCommon\Constants\OrderNTuple;
use BizLib\Client\UfsClient;
use BizLib\Constants\Horae;
use BizLib\Utils\CarLevel;
use BizLib\Utils\Language;
use BizLib\Utils\Registry;
use PreSale\Logics\estimatePriceV2\params\SceneParamsLogic;
use PreSale\Logics\estimatePriceV2\ParamsLogic;

/**
 * Class carpoolCustomInfo
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\carpoolCustom
 */
class CarpoolCustomInfo
{

    private $_aInfo;

    /**
     * carpoolCustomInfo constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
    }

    /**
     * @param array $aResponse response
     * @return mixed
     */
    public function build($aResponse) {

        // 愿走新手
        $aResponse['walk_type']         = $this->_aInfo['order_info']['n_tuple']['walk_type'] ?? 0;
        $aResponse['user_guide_module'] = $this->_buildWalkNewGuideInfo();

        $aResponse['broadcast_type_data'] = $this->_buildCarpoolPackDuseResponse();

        return $aResponse;
    }

    /**
     * @desc: 构建愿走新手引导
     * @return array
     */
    private function _buildWalkNewGuideInfo() {
        if (empty($this->_aInfo['order_info']['n_tuple']['walk_type'])) {
            return [];
        }

        if (Registry::has(WALK_ORDER_COUNT)) {
            $iWalkCnt = Registry::get(WALK_ORDER_COUNT);
        } else {
            // req call cnt
            $iWalkCnt = $this->_getWalkOrderCnt();
        }

        // run policy
        if (0 === $iWalkCnt) {
            return SceneParamsLogic::getInstance()->aWalkNewGuide;
        }

        return [];
    }

    /**
     * @desc: 获取愿走订单单量（预估数据结构太坑了，仅限预估使用）
     * @return int
     */
    private function _getWalkOrderCnt() {
        $oUfsClient    = new UfsClient();
        $iComboType    = $this->_aInfo['order_info']['n_tuple']['combo_type'];
        $iRequireLevel = $this->_aInfo['order_info']['require_level'];

        if (Horae::TYPE_COMBO_CARPOOL == $iComboType && CarLevel::DIDI_XIAOBA_CAR_LEVEL == $iRequireLevel) {
            $aFeatures = ['passenger.carpool_discount_walk.create_count',];
        } elseif (Horae::TYPE_COMBO_CARPOOL == $iComboType && CarLevel::DIDI_PUTONG_CAR_LEVEL == $iRequireLevel && OrderNTuple::ROUTE_TYPE_CARPOOL_FLAT_RATE == $this->_aInfo['order_info']['n_tuple']['route_type']) {
            $aFeatures = ['passenger.carpool_region_walk.create_count',];
        } elseif (Horae::TYPE_COMBO_CARPOOL == $iComboType && CarLevel::DIDI_PUTONG_CAR_LEVEL == $iRequireLevel) {
            $aFeatures = ['passenger.carpool_station_walk.create_count',];
        } elseif (Horae::TYPE_COMBO_CARPOOL_INTER_CITY == $iComboType) {
            $aFeatures = ['passenger.carpool_across_city_walk.create_count',];
        } else {
            return false;
        }

        $aCondition = [
            'passenger_id' => $this->_aInfo['passenger_info']['pid'],
        ];

        $aResult = $oUfsClient->getFeature($aFeatures, $aCondition);
        if (empty($aResult) || 0 != $aResult['errno'] || empty($aResult['result']) || !isset($aResult['result'][$aFeatures[0]])) {
            return false;
        }

        return (int)($aResult['result'][$aFeatures[0]]);
    }

    /**
     * 组装拼车批次分单文案.
     * @return array
     */
    private function _buildCarpoolPackDuseResponse() {
        $aOrderInfo = $this->_aInfo['order_info'];
        $iComboType = $this->_aInfo['bill_info']['product_infos'][$aOrderInfo['require_level']]['combo_type'] ?? $aOrderInfo['combo_type'];
        $aOrderInfo = ParamsLogic::getInstance()->getCacheOriginOrderInfo($aOrderInfo['product_id'], $iComboType, $aOrderInfo['require_level']);
        if (\BizCommon\Utils\Horae::isBatchPackScene($aOrderInfo['start_broadcast_time_type'] ?? 0)) {
            $aBroadcastData = [
                'start_broadcast_time_type' => $aOrderInfo['start_broadcast_time_type'],
                'title'                     => Language::getTextFromDcmp('gs_passenger_broadcast_type_estimate_title'),
                'sub_title'                 => Language::getTextFromDcmp('gs_passenger_broadcast_type_estimate_sub_title', ['time_diff' => $aOrderInfo['broadcast_time_diff']]),
                'start_broadcast_time_list' => [],
            ];
            foreach ($aOrderInfo['broadcast_time_list'] as $k => $unixTime) {
                $sTime = date('H:i', $unixTime);
                $aBroadcastData['start_broadcast_time_list'][] = [
                    'start_broadcast_time' => $unixTime,
                    'item_text'            => $sTime,
                    'select_text'          => Language::getTextFromDcmp('gs_passenger_broadcast_type_estimate_select_text', ['sTime' => $sTime]),
                ];
            }

            return $aBroadcastData;
        }
        return ['start_broadcast_time_type' => 0];
    }
}
