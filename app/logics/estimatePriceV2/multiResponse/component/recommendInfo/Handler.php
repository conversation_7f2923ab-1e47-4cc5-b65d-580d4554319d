<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo;

use PreSale\Logics\estimatePriceV2\multiResponse\Util;

/**
 * Class Handler
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo
 */
class Handler
{
    /**
     * @param array $aInfo aInfo
     * @return NormalRecommendInfo
     */
    public static function select($aInfo) {
        return new NormalRecommendInfo($aInfo);
    }
}
