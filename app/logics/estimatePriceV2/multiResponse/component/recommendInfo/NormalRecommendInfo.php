<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo;

use BizLib\Config as NuwaConfig;
use BizLib\Libraries\RedisDB;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\Language;
use BizLib\Utils\ProductCategory;
use PreSale\Infrastructure\Repository\Ufs\UfsRepository;
use PreSale\Logics\estimatePriceV2\CompensationLogic;
use PreSale\Logics\estimatePriceV2\DecisionLogic;
use PreSale\Logics\estimatePriceV2\multiResponse\component\GuideInfo;
use PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo\strategy\LongDistanceRecommendStrategy;
use PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo\strategy\Common;
use PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo\strategy\MannedVehicleRecommendStrategy;
use PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo\strategy\RecommendStrategyUtil;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;
use Xiaoju\Apollo\Apollo;
use PreSale\Logics\estimatePriceV2\multiResponse\component\athenaGuideInfo\Common as guideCommon;
use PreSale\Logics\estimatePriceV2\multiResponse\component\paymentInfo\Common as payCommon;

/**
 * Class NormalRecommendInfo
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo
 */
class NormalRecommendInfo
{
    const UNDEFINED_SHOW_TYPE          = 0;
    const RECOMMEND_SHOW_TYPE          = 1;
    const SELECTION_SHOW_TYPE          = 2;
    const DIDI_567_SHOW_TYPE           = 3;
    const D1_HIGHLIGHT_SHOW_TYPE       = 4;
    const STANDARD_RECOMMEND_SHOW_TYPE = 6;
    const RECOMMEND_BY_GROUP_SHOW_TYPE = 7; //车型打包推荐

    const PREFERENTIAL_RECOMMEND_TYPE = 1;
    const CAPACITY_RECOMMEND_TYPE     = 2;
    protected $_aInfo;

    /**
     * NormalRecommendInfo constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
    }

    /**
     * @param array $aResponse 预估结果
     * @return array
     */
    public function build($aResponse) {
        $longDistanceRecommendStrategy = new LongDistanceRecommendStrategy($this->_aInfo, []);
        if (0 != Common::getApolloType($this->_aInfo) && Util::isEstimateFormDoubleLineStyle($this->_aInfo)) {
            $aResponse = $longDistanceRecommendStrategy->buildRecommendInfo($aResponse);
            return $aResponse;
        }

        if ($this->_isMannedSuggestRecommend()) {
            $mannedVehicleeRecommendStrategy = new MannedVehicleRecommendStrategy($this->_aInfo, []);
            $aResponse = $mannedVehicleeRecommendStrategy->buildRecommendInfo($aResponse);
            return $aResponse;
        }

        $iProductCategory = $this->_aInfo['order_info']['product_category'];

        if (ProductCategory::PRODUCT_CATEGORY_HK_CAP_TAXI == $iProductCategory) {
            $aResponse['recommend_tag'] = $this->_getHKTaxiRecTag();
        }

        $aDecisionInfo = DecisionLogic::getInstance()->getProductInfoByCategoryId($iProductCategory);
        if (empty($aDecisionInfo)) {
            $aDecisionInfo = DecisionLogic::getInstance()->getProductGuideInfoByCategoryId($iProductCategory);
        }

        if (isset($aDecisionInfo['recommend_info'])) {
            $aRecommendInfo = $aDecisionInfo['recommend_info'];
            switch ($aRecommendInfo['show_type']) {
                case self::UNDEFINED_SHOW_TYPE:
                    //athena默认返回值，无需处理
                    break;
                //车型右边的气泡
                case self::RECOMMEND_SHOW_TYPE:
                    $aResponse['recommend_tag'] = [
                        'content'   => $aRecommendInfo['text'],
                        'left_icon' => $aRecommendInfo['icon'],
                    ];
                    //加缓存价格沟通组件使用
                    $sKey = UtilsCommon::getRedisPrefix(P_ESTIMATE_BUBBLE_ACTIVITY) . $aResponse['estimate_id'];
                    \PreSale\Logics\redismove\RedisWrapper::getInstance(P_ESTIMATE_BUBBLE_ACTIVITY)->setex($sKey,30,json_encode(array_merge($aResponse['recommend_tag'], ['recommend_info' => $aRecommendInfo])));
                    break;
                case self::SELECTION_SHOW_TYPE:
                    $aResponse['selection_tag'] = [
                        'content'   => $aRecommendInfo['text'],
                        'left_icon' => $aRecommendInfo['icon'],
                    ];
                    break;
                case self::DIDI_567_SHOW_TYPE:
                    // if (Util::isEstimateFormSingleLineStyle($this->_aInfo) || Util::isEstimateFormDoubleLineStyle($this->_aInfo)) {
                    if (Util::isEstimateFormDoubleLineStyle($this->_aInfo)) {
                        break;
                    }

                    $aRecommendConfig = $this->_getRecommendConfig($aRecommendInfo['show_type']);
                    if (!empty($aRecommendConfig)) {
                        $aResponse['theme_type']    = $this->_conflictWithGuide() ? 0 : $aRecommendConfig['theme_type'];
                        $aResponse['recommend_tag'] = [
                            'icon' => $this->_getRecommendIcon($aRecommendConfig, $aResponse['theme_type'], $aRecommendInfo),
                        ];
                        //加缓存价格沟通组件使用
                        $sKey = UtilsCommon::getRedisPrefix(P_ESTIMATE_BUBBLE_ACTIVITY) . $aResponse['estimate_id'];
                        \PreSale\Logics\redismove\RedisWrapper::getInstance(P_ESTIMATE_BUBBLE_ACTIVITY)->setex($sKey,30,json_encode(array_merge($aResponse['recommend_tag'], ['recommend_info' => $aRecommendInfo])));
                    }
                    break;
                case self::D1_HIGHLIGHT_SHOW_TYPE:
                    // no break
                case self::RECOMMEND_BY_GROUP_SHOW_TYPE:
                    // no break
                case self::STANDARD_RECOMMEND_SHOW_TYPE:
                    $oRecommendLogic = RecommendStrategyUtil::getInstanceByInfoAndRecommendInfo($this->_aInfo, $aRecommendInfo);
                    $aResponse       = $oRecommendLogic->buildRecommendInfo($aResponse);
                    break;
                default:
                    break;
            }
        }

        //快的新出租独立小程序推荐配置文案
        if ($this->_getRecommendSwitchRes()) {
            $sRecommendConfig = Language::getTextFromDcmp('taxi_special-small_recommend');
            $aResponse['unitaix_recommend_tag'] = json_decode($sRecommendConfig, true);

            //计算是否展示文案下气泡
            $sFeatureKey    = 'taxi_sepcial_price_recommend_count';
            $aCondition     = ['passenger_id' => $this->_aInfo['passenger_info']['pid']];
            $sViewCountInfo = UfsRepository::getUfsFeature([$sFeatureKey], $aCondition, 'passenger');
            $aViewCountInfo = explode('_', $sViewCountInfo['taxi_sepcial_price_recommend_count']);
            if (count($aViewCountInfo) < 2) {
                $iSumCount = 0;
                $sDate     = '';
            } else {
                $iSumCount = $aViewCountInfo[0];
                $sDate     = $aViewCountInfo[1];
            }

            $sCurrentDate = date('Y-m-d');
            if ($iSumCount > $aResponse['unitaix_recommend_tag']['other_info'] || $sDate >= $sCurrentDate) {
                $aResponse['unitaix_recommend_tag']['content']     = '';
                $aResponse['unitaix_recommend_tag']['config_info'] = '';
                $aResponse['unitaix_recommend_tag']['bg_image']    = '';
                $aResponse['unitaix_recommend_tag']['font_color']  = '';
            } else {
                $iSumCount++;
                $sDate     = $sCurrentDate;
                $sSetValue = implode('_', [$iSumCount, $sDate]);
                UfsRepository::setUfsFeature([$sFeatureKey => $sSetValue], $aCondition, 'passenger');
            }
        }

        // 优先级：未应答赔付标签 > athena的推荐标签
        $aNoAnswerTag = $this->getNoAnswerCompensateRecTag();
        if (!empty($aNoAnswerTag)) {
            $aResponse['recommend_tag'] = $aNoAnswerTag;
        }

        //athena的推荐信息覆盖dds返回
        return $aResponse;
    }

    /**
     * 获取推荐配置
     * @param int $iShowType iShowType
     * @return array|mixed
     */
    private function _getRecommendConfig($iShowType) {
        $aRecommendText = NuwaConfig::text('config_text', 'recommend_config');
        if (empty($aRecommendText[$iShowType])) {
            return [];
        }

        $iAccessKeyId     = $this->_aInfo['common_info']['access_key_id'];
        $aRecommendConfig = isset($aRecommendText[$iShowType][$iAccessKeyId]) ? $aRecommendText[$iShowType][$iAccessKeyId] : $aRecommendText[$iShowType]['default'];
        $aNTuple          = $this->_aInfo['order_info']['n_tuple'];
        $sConfigKey       = $this->_aInfo['order_info']['product_category'].'_'.$aNTuple['carpool_price_type'];
        return isset($aRecommendConfig[$sConfigKey]) ? array_merge($aRecommendConfig['default_merge'], $aRecommendConfig[$sConfigKey]) : $aRecommendConfig['default_merge'];
    }

    /**
     * 获取香港一口价推荐标签
     * @return array
     */
    private function _getHKTaxiRecTag() {
        $aParams = array(
            'phone'         => $this->_aInfo['passenger_info']['phone'],
            'app_version'   => $this->_aInfo['common_info']['app_version'],
            'access_key_id' => $this->_aInfo['common_info']['access_key_id'],
        );
        $oApollo = Apollo::getInstance()->featureToggle('hk_cap_taxi_rec_tag', $aParams);
        if (!$oApollo->allow() || 'control_group' == $oApollo->getGroupName()) {
            // 兜底
            $aConfig = Language::getDecodedTextFromDcmp('hk_cap_taxi-recommend_tag_v2');
            if (!empty($aConfig)) {
                return $aConfig;
            } else {
                return [
                    'content'   => '',
                    'left_icon' => '',
                ];
            }
        }

        $sParamName = 'content_' . $this->_aInfo['common_info']['lang'];
        $sContent   = $oApollo->getParameter($sParamName, '');
        $sIcon      = '';

        return [
            'content'   => $sContent,
            'left_icon' => $sIcon,
        ];
    }

    /**
     * 获取推荐icon
     * @param array $aRecommendConfig aRecommendConfig
     * @param int   $iThemeType       iThemeType
     * @param array $aRecommendInfo   aRecommendInfo
     * @return mixed
     */
    private function _getRecommendIcon($aRecommendConfig, $iThemeType, $aRecommendInfo) {
        $sBatchFlag           = isset($aRecommendInfo['extra_info']) && isset($aRecommendInfo['extra_info']['batch_flag']) ? $aRecommendInfo['extra_info']['batch_flag'] : 'default';
        $aRecommendIconConfig = empty($aRecommendConfig['icon'][$iThemeType]) ? $aRecommendConfig['icon']['default'] : $aRecommendConfig['icon'][$iThemeType];
        return empty($aRecommendIconConfig[$sBatchFlag]) ? $aRecommendIconConfig['default'] : $aRecommendIconConfig[$sBatchFlag];
    }

    /**
     * 导流位与新样式冲突，优先展示导流位效果。
     * @return bool
     */
    private function _conflictWithGuide() {
        $aGuideInfo = GuideInfo::getInstance()->getGuideProductInfo();
        return !empty($aGuideInfo) && $aGuideInfo['order_info']['estimate_id'] != $this->_aInfo['order_info']['estimate_id'];
    }

    /**
     * 获取新出租独立小程序推荐信息开关
     * @return array|mixed
     */
    private function _getRecommendSwitchRes() {
        $oApollo        = new Apollo();
        $aPassengerData = array(
            'key'              => $this->_aInfo['passenger_info']['pid'],
            'phone'            => $this->_aInfo['passenger_info']['phone'],
            'city'             => $this->_aInfo['order_info']['area'],
            'page_type'        => $this->_aInfo['order_info']['page_type'],
            'order_type'       => $this->_aInfo['order_info']['order_type'],
            'call_car_type'    => $this->_aInfo['order_info']['call_car_type'],
            'app_version'      => $this->_aInfo['common_info']['app_version'],
            'access_key_id'    => $this->_aInfo['common_info']['access_key_id'],
            'product_category' => $this->_aInfo['order_info']['product_category'],
        );
        return $oApollo->featureToggle('gs_taxi_wx_program_recommend_switch', $aPassengerData)->allow();
    }

    /**
     * 判断是否是载人车推荐
     * @return bool
     */
    private function _isMannedSuggestRecommend() {
        if (Util::isMiniApp($this->_aInfo['common_info']['access_key_id'])) {
            return guideCommon::isMannedVehicleSuggestHighLight($this->_aInfo) && !payCommon::isEnterprisePay($this->_aInfo);
        }

        return Util::isEstimateFormDoubleLineStyle($this->_aInfo) && guideCommon::isMannedVehicleSuggestHighLight($this->_aInfo) && !payCommon::isEnterprisePay($this->_aInfo);
    }

    /**
     * @return array
     */
    private function getNoAnswerCompensateRecTag() {
        if (empty($this->_aInfo)) {
            return [];
        }

        $oProductCategory   = new ProductCategory();
        $aNTuple            = [
            'business_id'           => $this->_aInfo['order_info']['business_id'],
            'require_level'         => $this->_aInfo['order_info']['require_level'],
            'combo_type'            => $this->_aInfo['order_info']['combo_type'],
            'level_type'            => $this->_aInfo['order_info']['level_type'],
            'carpool_type'          => $this->_aInfo['order_info']['carpool_type'],
            'spacious_car_alliance' => $this->_aInfo['order_info']['spacious_car_alliance'],
        ];
        $sProductCategoryId = $oProductCategory->getProductCategoryByNTuple($aNTuple);

        $aParam = [
            'product_category' => $this->_aInfo['order_info']['product_category'],
            'access_key_id'    => $this->_aInfo['common_info']['access_key_id'],
            'app_version'      => $this->_aInfo['common_info']['app_version'],
            'city'             => $this->_aInfo['order_info']['area'],
            'is_new_form'      => 0,
        ];
        $oToggle = Apollo::getInstance()->featureToggle('form_ban_no_answer_tag', $aParam);
        if ($oToggle->allow()) {
            // 屏蔽
            return [];
        }

        $aMultiCompensationAbilityResult = CompensationLogic::getInstance()->getMultiCompensationAbilityResult();
        $aCompensationRes = $aMultiCompensationAbilityResult[$sProductCategoryId];
        if (empty($aCompensationRes)) {
            return [];
        }

        if (!isset($aCompensationRes['no_answer_compensation']['decision'])
            || 0 == $aCompensationRes['no_answer_compensation']['decision']) {
            return [];
        }

       $aIntroTags = NuwaConfig::text('config_passenger', 'intro_tags_no_answer_compensate');
        if (empty($aIntroTags)) {
            return [];
        }

        $aExtra = $aCompensationRes['no_answer_compensation']['extra'] ?? [];
        if (empty($aExtra['biz_name'])) {
            return [];
        }

        $sContent = $aExtra['biz_name'];

        return [
            'content'   => $sContent,
            'left_icon' => $aIntroTags['icon_url'],
        ];
    }
}
