<?php
/**
 * 短时预约推荐.
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2020/10/31
 */
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo;

use PreSale\Logics\estimatePriceV2\ShortBookLogic;
use BizLib\Utils\Language;
use BizLib\Config as NuwaConfig;

/**
 * Class KFlowerRecommendInfo
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo
 */
class KFlowerRecommendInfo
{
    // 注释-待删
//    protected $_aInfo;
//
//    /**
//     * NormalRecommendInfo constructor.
//     * @param array $aInfo aInfo
//     */
//    public function __construct($aInfo) {
//        $this->_aInfo         = $aInfo;
//        $this->_aEstimateText = NuwaConfig::text('config_text', 'mOrderEstimateNew');
//    }
//
//    /**
//     * @param array $aResponse response
//     * @return array|mixed
//     */
//    public function build($aResponse) {
//        $aResponse = $this->_buildShortBookRecInfo($aResponse);
//
//        return $aResponse;
//    }
//
//    /**
//     * @param array $aResponse response
//     * @return array
//     */
//    private function _buildShortBookRecInfo($aResponse) {
//        if (!ShortBookLogic::getInstance()->hasShortBook()) {
//            return $aResponse;
//        }
//
//        $aShortBook = ShortBookLogic::getInstance()->getShortBookInfo();
//        $aNormal    = ShortBookLogic::getInstance()->getNormalInfo();
//
//        $iExpireTime = (int)NuwaConfig::getBizConfig('common', 'valid_interval_short_book') / 60;
//
//        $aShortBookRec = [
//            'title'     => Language::replaceTag(
//                $this->_aEstimateText['short_book_rec_selected_title'],
//                [
//                    'discount' => $aShortBook['short_wait']['Discount'],
//                    'amount'   => $aShortBook['short_wait']['Amount'] / 100,
//                ]
//            ),
//            'sub_title' => Language::replaceTag(
//                $this->_aEstimateText['short_book_rec_selected_sub_title'],
//                ['num' => $iExpireTime,]
//            ),
//            'icon'      => $this->_aEstimateText['short_book_rec_icon'],
//        ];
//
//        $aNormalRec = [
//            'title'     => Language::replaceTag(
//                $this->_aEstimateText['short_book_rec_not_selected_title'],
//                [
//                    'discount' => $aShortBook['short_wait']['Discount'],
//                    'amount'   => $aShortBook['short_wait']['Amount'] / 100,
//                ]
//            ),
//            'sub_title' => Language::replaceTag(
//                $this->_aEstimateText['short_book_rec_not_selected_sub_title'],
//                ['num' => $iExpireTime,]
//            ),
//            'icon'      => $this->_aEstimateText['short_book_rec_icon'],
//        ];
//
//        $aResponse['short_book_rec'] = $aNormalRec;
//
//        $aResponse['element_replace']['normal']['short_book_rec']     = $aNormalRec;
//        $aResponse['element_replace']['short_book']['short_book_rec'] = $aShortBookRec;
//
//        return $aResponse;
//    }
}
