<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo\strategy;

use Xiaoju\Apollo\Apollo;

/**
 * Class GroupRecommendStrategy
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo\strategy
 */
class GroupRecommendStrategy extends RecommendStrategy
{


    /**
     * 构建推荐信息
     * @param array $aResponse aResponse
     * @return array
     */
    public function buildRecommendInfo($aResponse) {

        //聚合场景，theme信息仅构建一次即可
        static $bIsBuild;
        if ($bIsBuild) {
            return $aResponse;
        }

        $bIsBuild = true;

        $aResponse = $this->buildThemeType($aResponse);
        $aResponse = $this->buildThemeData($aResponse);
        return $aResponse;
    }

    /**
     * @param array $aResponse $aResponse
     * @return array
     */
    public function buildThemeType($aResponse) {
        $aResponse['theme_type'] = RecommendStrategy::WITH_TITLE_THEME_TYPE;
        return $aResponse;
    }

    /**
     * @param array $aResponse $aResponse
     * @return array
     */
    public function buildThemeData($aResponse) {

        $aResponse['theme_data'] = [
            'title'              => $this->_aRecommendInfo['text'] ?? $this->_aConfig['title'],
            'title_icon'         => $this->_aConfig['title_icon'],
            'right_image'        => $this->_aConfig['right_image'],
            'texture_image'      => $this->_aConfig['texture_image'],
            'theme_color'        => $this->_aConfig['theme_color'],
            'outer_bg_gradients' => $this->_aConfig['outer_bg_gradients'],
        ];
        return $aResponse;
    }



    /**
     * 初始化配置
     * @return array
     */
    protected function getConfig() {
        list($bOk, $aConfig) = Apollo::getInstance()->getConfigResult('recommend_theme_type_4', 'recommend_group_theme_data')->getAllConfig();
        if (!$bOk || empty($aConfig)) {
            return [];
        }

        return $aConfig;
    }
}
