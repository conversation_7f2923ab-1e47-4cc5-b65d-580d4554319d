<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo\strategy;

use BizLib\Config as NuwaConfig;
use BizLib\Constants\Common as constantsCommon;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use BizLib\Constants;

/**
 * 标准品类推荐位策略
 * Class StandardRecommendStrategy
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo
 */
class LongDistanceRecommendStrategy extends RecommendStrategy
{
    //标准品类推荐主题类型
    const STANDARD_RECOMMEND_THEME_TYPE = 3;

    /**
     * 设置主题类型
     * @param array $aResponse aResponse
     * @return array
     */
    protected function buildThemeType($aResponse) {
        if (Common::APPOLLO_FRIEND_LOW_TYPE == Common::getApolloType($this->_aInfo)) {
            $aResponse['form_show_type'] = 1;
            return $aResponse;
        }

        if ($this->conflictWithGuide()) {
            $this->_iThemeType = self::DEFAULT_THEME_TYPE;
        } else {
            $this->_iThemeType = self::STANDARD_RECOMMEND_THEME_TYPE;
        }

        $aResponse['theme_type'] = $this->_iThemeType;
        return $aResponse;
    }


    /**
     * 设置主题数据
     * @param array $aResponse aResponse
     * @return array
     */
    protected function buildThemeData($aResponse) {
        if (Common::APPOLLO_FRIEND_LOW_TYPE == Common::getApolloType($this->_aInfo)) {
            return  $aResponse;
        }

        $feeInfo = NuwaConfig::text('config_text', 'long_distance_carpool_fee');
        if (!empty($this->_aConfig)) {
            $this->_aThemeData = array(
                'theme_color'           => $this->_aConfig['theme_color'],
                'button_font_color'     => $this->_aConfig['button_font_color'],
                'inner_bg_color'        => $this->_aConfig['inner_bg_color'],
                'selected_bg_gradients' => [
                    $this->_aConfig['selected_bg_gradients']['start_color'],
                    $this->_aConfig['selected_bg_gradients']['end_color'],
                ],
                'outer_bg_gradients'    => [
                    $this->_aConfig['outer_bg_gradients']['start_color'],
                    $this->_aConfig['outer_bg_gradients']['end_color'],
                ],
            );
        }

        $this->_aThemeData['title']      = $feeInfo['friend_title'];
        $this->_aThemeData['title_icon'] = $feeInfo['friend_icon'];

        if (Common::APPOLLO_PEOPEL_CAR_TYPE == Common::getApolloType($this->_aInfo)) {
            $this->_aThemeData['title']      = $feeInfo['maned_vehicle_title'];
            $this->_aThemeData['title_icon'] = $feeInfo['maned_vehicle_icon'];
        }

        if ((version_compare(MainDataRepo::getAppVersion(), '6.2.6') < 0 && constantsCommon::DIDI_IOS_PASSENGER_APP == $this->_aInfo['common_info']['access_key_id'])
            || (version_compare(MainDataRepo::getAppVersion(), '6.2.8') < 0 && constantsCommon::DIDI_ANDROID_PASSENGER_APP == $this->_aInfo['common_info']['access_key_id'])
        ) {
            $aResponse['recommend_tag'] = [
                'content'   => $this->_aThemeData['title'],
                'left_icon' => $this->_aThemeData['title_icon'],
            ];
        }

        $aResponse['theme_data'] = $this->_aThemeData;
        return $aResponse;
    }

    /**
     * 初始化配置
     * @return array
     */
    protected function getConfig() {
        //未读取到个性化配置，读取默认配置
        list($bOk, $aAllThemeConfig) = Apollo::getInstance()->getConfigsByNamespaceAndConditions(
            'standard_recommend_theme_conf',
            ['theme_key' => 'default',]
        )->getAllConfigData();
        if ($bOk && !empty($aAllThemeConfig)) {
            return array_pop($aAllThemeConfig);
        }

        return array();
    }
}
