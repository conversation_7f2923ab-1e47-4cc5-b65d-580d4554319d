<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo\strategy;

use PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo\NormalRecommendInfo;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;

/**
 * 品类推荐策略工具类
 * Class RecommendLogic
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo
 */
class RecommendStrategyUtil
{
    private static $_aCouponInfo = [];

    /**
     * @param array $aInfo          aInfo
     * @param array $aRecommendInfo aRecommendInfo
     * @return RecommendStrategy
     */
    public static function getInstanceByInfoAndRecommendInfo($aInfo, $aRecommendInfo) {
        switch ($aRecommendInfo['show_type']) {
            case NormalRecommendInfo::D1_HIGHLIGHT_SHOW_TYPE:
                if (Util::isEstimateFormDoubleLineStyle($aInfo) || Util::isRecommendGroup($aInfo)) {
                    if ((version_compare($aInfo['common_info']['app_version'], '6.2.8') >= 0 && 1 == $aInfo['common_info']['access_key_id'])||(version_compare($aInfo['common_info']['app_version'], '6.2.10') >= 0 && 2 == $aInfo['common_info']['access_key_id'])) {
                        $oRecommendStrategy = new DoubleD1HighlightRecommendStrategy($aInfo, $aRecommendInfo);
                    }

                    break;
                }

                $oRecommendStrategy = new D1HighlightRecommendStrategy($aInfo, $aRecommendInfo);
                break;
            case NormalRecommendInfo::STANDARD_RECOMMEND_SHOW_TYPE:
                $oRecommendStrategy = new StandardRecommendStrategy($aInfo, $aRecommendInfo);
                break;
            case NormalRecommendInfo::RECOMMEND_BY_GROUP_SHOW_TYPE:
                $oRecommendStrategy = new GroupRecommendStrategy($aInfo, $aRecommendInfo);
                break;
            default:
                break;
        }

        return $oRecommendStrategy;
    }

    /**
     * @param array  $aInfo           aInfo
     * @param string $sCouponDesc     sCouponDesc
     * @param string $sCouponDescIcon sCouponDescIcon
     * @return void
     */
    public static function recordCouponDesc($aInfo, $sCouponDesc, $sCouponDescIcon, $fAmount = 0) {
        self::$_aCouponInfo[$aInfo['order_info']['product_category']] = [$sCouponDesc, $sCouponDescIcon, $fAmount];
    }


    /**
     * 主题样式降级。
     * 降级场景：拼车link品类，特惠盒子内部品类。
     * @param array $aItem dItem
     * @return array
     */
    public static function degradeEstimateData($aItem) {
        return $aItem;
    }
}
