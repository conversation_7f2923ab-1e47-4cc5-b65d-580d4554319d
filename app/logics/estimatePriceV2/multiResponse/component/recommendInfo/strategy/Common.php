<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo\strategy;

use BizLib\Log;
use BizLib\Utils\ProductCategory;
use Nuwa\ApolloSDK\Apollo;

/**
 * 公共方法
 * Class Common
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo
 */
class Common
{
    private $_ainfo = [];
    //拼友强样式
    const APPOLLO_FRIEND_HIGH_TYPE = 1;
    //拼友弱样式
    const APPOLLO_FRIEND_LOW_TYPE = 2;
    //载人车推荐
    const APPOLLO_PEOPEL_CAR_TYPE = 3;

    /**
     * DoubleLinePriceDesc constructor.
     * @param array $aInfo aInfo
     */
    public function __construct(array $aInfo) {
        $this->_ainfo = $aInfo;
    }

    /**
     * 获取apolloType配置
     * @param array $aInfo ainfo
     * @return int
     */
    public static function getApolloType($aInfo) {
        $aOrderInfo = $aInfo['order_info'] ?? [];
        if (ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTE != $aOrderInfo['product_category']) {
            return 0;
        }

        $aCommonInfo      = $aInfo['common_info'];
        $aPassengerInfo   = $aInfo['passenger_info'];
        list($msec, $sec) = explode(' ', microtime());
        $msectime         = (float)sprintf('%.0f', ((float)($msec) + (float)($sec)) * 1000);
        $aApolloParams    = [
            'key'              => $aPassengerInfo['pid'],
            'phone'            => $aPassengerInfo['phone'],
            'access_key_id'    => $aCommonInfo['access_key_id'],
            'time_period'      => Date('H:i'),
            'date_time_period' => Date('Y-m-d H:i'),
            'business_id'      => $aCommonInfo['business_id'],
            'car_level'        => $aOrderInfo['require_level'],
            'product_category' => $aOrderInfo['product_category'],
            'city'             => $aOrderInfo['area'],
            'unix_time'        => $msectime,
        ];
        $apollo           = self::isApolloAllowByRouteIds($aApolloParams,$aOrderInfo['match_routes'],'feature_new_carpool_friend_suggest');
        if ($apollo[0]) {
            if ('high_treatment_group' == $apollo[1]) {
                return  self::APPOLLO_FRIEND_HIGH_TYPE;
            }

            if ('low_treatment_group' == $apollo[1]) {
                return self::APPOLLO_FRIEND_LOW_TYPE;
            }
        }

        $apollo = self::isApolloAllowByRouteIds($aApolloParams,$aOrderInfo['match_routes'],'feature_carpool_manned_vehicle_suggest');
        if ($apollo[0]) {
            if ('treatment_group' == $apollo[1]) {
                return self::APPOLLO_PEOPEL_CAR_TYPE;
            }
        }

        return 0;
    }
    /**
     * 判断是否有一个routeId命中
     * @param array $aApolloParams apollo参数
     * @param array $matchRoutes   route_ids
     * @param array $apolloName    apollo名字
     * @return int
     */
    public static function isApolloAllowByRouteIds($aApolloParams, $matchRoutes, $apolloName) {
        foreach ($matchRoutes as $key => $value) {
            $aApolloParams['route_id'] = $value['route_id'];
            $featureToggle = Apollo::getInstance()->featureToggle(
                $apolloName,
                $aApolloParams
            );
            if ($featureToggle->allow()) {
                return [true, $featureToggle->getGroupName()];
            }
        }

        return [false, ''];
    }
}
