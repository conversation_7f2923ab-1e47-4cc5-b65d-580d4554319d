<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo\strategy;

use BizLib\Libraries\RedisDB;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\Currency;
use BizLib\Utils\Language;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\estimatePriceV2\multiRequest\ProductGroup;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;
use PreSale\Logics\estimatePriceV2\multiResponse\productAggregation\Handler as groupHandler;

/**
 * 标准品类推荐位策略
 * Class StandardRecommendStrategy
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo
 */
class StandardRecommendStrategy extends RecommendStrategy
{
    //标准品类推荐主题类型
    const STANDARD_RECOMMEND_THEME_TYPE = 3;

    private $_defaultGradients = ['#ffffff', '#ffffff'];

    /**
     * @param array $aResponse aResponse
     * @return array
     */
    public function buildPriceDesc($aResponse) {
        if (empty($aResponse['price_info_desc'])
            || !is_array($aResponse['price_info_desc'])
            || self::DEFAULT_THEME_TYPE == $this->_iThemeType
            || ProductGroup::getInstance()->getProductSubGroupID($this->_aInfo['order_info']['product_category']) > 0
        ) {
            return $aResponse;
        }

        foreach ($aResponse['price_info_desc'] as $key => $value) {
            $bNeedBuild = false;
            if (Util::isEstimateFormDoubleLineStyle($this->_aInfo)) {
                if ($this->_aOriginPriceInfoDesc[0] == $value['content'] && $this->_aOriginPriceInfoDesc[1] == $value['left_icon']) {
                    $bNeedBuild       = true;
                    list(, $sUnit)    = Currency::getSymbolUnit($this->_aInfo['bill_info']['currency'] ?? '', $this->_aInfo['order_info']);
                    $sThemeCouponDesc = Language::replaceTag(
                        $this->_aConfig['coupon_desc_conf']['double_line_content'],
                        ['amount' => $this->_aOriginPriceInfoDesc[2], 'currency_unit' => $sUnit]
                    );
                }
            } elseif ($this->_aOriginPriceInfoDesc[0] == $value['content'] && empty($value['left_icon'])) {
                $bNeedBuild       = true;
                $sCouponDesc      = str_replace(['{', '}'], ['', ''], $value['content']);
                $sThemeCouponDesc = Language::replaceTag($this->_aConfig['coupon_desc_conf']['content'], ['content' => $sCouponDesc]);
            }

            if ($bNeedBuild) {
                $sThemeCouponDescIcon       = $this->_aConfig['coupon_desc_conf']['left_icon'];
                $this->_aThemePriceInfoDesc = [$sThemeCouponDesc, $sThemeCouponDescIcon];
                $aResponse['price_info_desc'][$key]['content']       = $sThemeCouponDesc;
                $aResponse['price_info_desc'][$key]['left_icon']     = $sThemeCouponDescIcon;
                $aResponse['price_info_desc'][$key]['bg_gradients']  = array(
                    $this->_aConfig['coupon_desc_conf']['bg_gradients']['start_color'],
                    $this->_aConfig['coupon_desc_conf']['bg_gradients']['end_color'],
                );
                $aResponse['price_info_desc'][$key]['bg_fill_color'] = $this->_aConfig['coupon_desc_conf']['bg_fill_color'];
                $aResponse['price_info_desc'][$key]['font_color']    = $this->_aConfig['coupon_desc_conf']['font_color'];
            }
        }

        return $aResponse;
    }

    /**
     * 主题样式降级。
     * 降级场景：link品类，（盒子内部品类暂时不需要）。
     * @param array $aItem dItem
     * @return array
     */
    public function degradeEstimateData($aItem) {
        if ($aItem['product_category'] != $this->_aInfo['order_info']['product_category']) {
            return $aItem;
        }

        //降级主题数据
        $aItem['theme_type'] = self::DEFAULT_THEME_TYPE;
        unset($aItem['theme_data']);
        $aItem['recommend_tag'] = $this->_aOriginRecommendTag;
        $sKey = UtilsCommon::getRedisPrefix(P_ESTIMATE_BUBBLE_ACTIVITY) . $aItem['estimate_id'];
        \PreSale\Logics\redismove\RedisWrapper::getInstance(P_ESTIMATE_BUBBLE_ACTIVITY)->setex($sKey,30,json_encode(array_merge($this->_aOriginRecommendTag, ['recommend_info' => $this->_aRecommendInfo])));

        //降级price_info_desc
        if (!empty($aItem['price_info_desc']) && is_array($aItem['price_info_desc'])) {
            foreach ($aItem['price_info_desc'] as $key => $value) {
                if (!empty($this->_aThemePriceInfoDesc[0]) && !empty($value['content']) && $value['content'] == $this->_aThemePriceInfoDesc[0]) {
                    $aItem['price_info_desc'][$key]['content']   = $this->_aOriginPriceInfoDesc[0];
                    $aItem['price_info_desc'][$key]['left_icon'] = $this->_aOriginPriceInfoDesc[1];
                    unset($aItem['price_info_desc'][$key]['bg_gradients']);
                    unset($aItem['price_info_desc'][$key]['font_color']);
                    unset($aItem['price_info_desc'][$key]['bg_fill_color']);
                    break;
                }
            }
        }

        return $aItem;
    }

    /**
     * 设置主题类型
     * @param array $aResponse aResponse
     * @return array
     */
    protected function buildThemeType($aResponse) {
        if ($this->conflictWithGuide()) {
            $this->_iThemeType = self::DEFAULT_THEME_TYPE;
        } else {
            $this->_iThemeType = self::STANDARD_RECOMMEND_THEME_TYPE;
        }

        $aResponse['theme_type'] = $this->_iThemeType;
        return $aResponse;
    }

    /**
     * 设置推荐标签
     * @param array $aResponse aResponse
     * @return array
     */
    protected function buildRecommendTag($aResponse) {
        $sKey = UtilsCommon::getRedisPrefix(P_ESTIMATE_BUBBLE_ACTIVITY) . $aResponse['estimate_id'];
        $this->_aThemeRecommendTag  = $this->_getRecommendTag();
        $this->_aOriginRecommendTag = $this->_getDegradeRecommendTag();
        if (self::DEFAULT_THEME_TYPE == $aResponse['theme_type']) {
            //主题降级标签
            //出租车盒子，不需要把内部的标签放到盒子上，所以内外都是降级标签
            $aResponse['recommend_tag'] = $this->_aOriginRecommendTag;
            \PreSale\Logics\redismove\RedisWrapper::getInstance(P_ESTIMATE_BUBBLE_ACTIVITY)->setex($sKey,30,json_encode(array_merge($this->_aOriginRecommendTag, ['recommend_info' => $this->_aRecommendInfo])));
        } elseif (in_array(
            ProductGroup::getInstance()->getProductSubGroupID($this->_aInfo['order_info']['product_category']),
            [groupHandler::SUB_GROUP_ID_SHORT_DISTANCE, groupHandler::SUB_GROUP_ID_MOCK_RECOMMEND]
        )
        ) {
            //特惠盒子 外部正常，内部降级标签
            if (version_compare($this->_aInfo['common_info']['app_version'], '6.2.8') < 0) { //高版本挪到theme_data展示
                $aResponse['recommend_tag'] = $this->_aThemeRecommendTag;
            }

            \PreSale\Logics\redismove\RedisWrapper::getInstance(P_ESTIMATE_BUBBLE_ACTIVITY)->setex($sKey,30,json_encode(array_merge($this->_aOriginRecommendTag, ['recommend_info' => $this->_aRecommendInfo])));
        } else {
            //正常标签
            if (version_compare($this->_aInfo['common_info']['app_version'], '6.2.8') < 0) {
                $aResponse['recommend_tag'] = $this->_aThemeRecommendTag;
                \PreSale\Logics\redismove\RedisWrapper::getInstance(P_ESTIMATE_BUBBLE_ACTIVITY)->setex($sKey,30,json_encode(array_merge($this->_aThemeRecommendTag, ['recommend_info' => $this->_aRecommendInfo])));
            }
        }

        return $aResponse;
    }

    /**
     * 设置主题数据
     * @param array $aResponse aResponse
     * @return array
     */
    protected function buildThemeData($aResponse) {
        if (parent::DEFAULT_THEME_TYPE != $aResponse['theme_type'] && !empty($this->_aConfig)) {
            $this->_aThemeData       = array(
                'theme_color'             => $this->_aConfig['theme_color'],
                'button_font_color'       => $this->_aConfig['button_font_color'],
                'inner_bg_color'          => $this->_aConfig['inner_bg_color'],
                'selected_bg_gradients'   => [
                    $this->_aConfig['selected_bg_gradients']['start_color'],
                    $this->_aConfig['selected_bg_gradients']['end_color'],
                ],
                'outer_bg_gradients'      => [
                    $this->_aConfig['outer_bg_gradients']['start_color'],
                    $this->_aConfig['outer_bg_gradients']['end_color'],
                ],
                'title'                   => $this->_aRecommendInfo['text'],
                'title_icon'              => !empty($this->_aRecommendInfo['icon']) ? $this->_aRecommendInfo['icon'] : '',
                'unselected_bg_gradients' => $this->_defaultGradients,
            );
            $aResponse['theme_data'] = $this->_aThemeData;
        }

        return $aResponse;
    }

    /**
     * 初始化配置
     * @return array
     */
    protected function getConfig() {
        //优先读取品类个性化配置
        list($bOk, $aAllProductConf) = Apollo::getInstance()->getConfigsByNamespaceAndConditions(
            'standard_recommend_product_conf',
            ['product_category' => $this->_aInfo['order_info']['product_category'],]
        )->getAllConfigData();

        if ($bOk && !empty($aAllProductConf)) {
            $aProductConf = array_pop($aAllProductConf);
            list($bOk, $aAllThemeConfig) = Apollo::getInstance()->getConfigsByNamespaceAndConditions(
                'standard_recommend_theme_conf',
                ['theme_key' => $aProductConf['theme_key'],]
            )->getAllConfigData();
            if ($bOk && !empty($aAllThemeConfig)) {
                return array_pop($aAllThemeConfig);
            }
        }

        //未读取到个性化配置，读取默认配置
        list($bOk, $aAllThemeConfig) = Apollo::getInstance()->getConfigsByNamespaceAndConditions(
            'standard_recommend_theme_conf',
            ['theme_key' => 'default',]
        )->getAllConfigData();
        if ($bOk && !empty($aAllThemeConfig)) {
            return array_pop($aAllThemeConfig);
        }

        return array();
    }

    /**
     * 获取正常推荐标签
     * @return array
     */
    private function _getRecommendTag() {
        $aRecommendTag['font_color'] = $this->_aConfig['recommend_font_color'];
        $aRecommendTag['content']    = $this->_aRecommendInfo['text'];
        if (empty($aRecommendTag['content'])) {
            $aRecommendTag['icon'] = $this->_aRecommendInfo['icon'];
        } else {
            $aRecommendTag['left_icon'] = $this->_aRecommendInfo['icon'];
        }

        return $aRecommendTag;
    }

    /**
     * 获取降级标签
     * @return array
     */
    private function _getDegradeRecommendTag() {
        $aRecommendTag['content'] = $this->_aRecommendInfo['extra_info']['degrade_text'];
        if (empty($aRecommendTag['content'])) {
            $aRecommendTag['icon'] = $this->_aRecommendInfo['extra_info']['degrade_icon'];
        } else {
            $aRecommendTag['left_icon'] = $this->_aRecommendInfo['extra_info']['degrade_icon'];
        }

        return $aRecommendTag;
    }
}
