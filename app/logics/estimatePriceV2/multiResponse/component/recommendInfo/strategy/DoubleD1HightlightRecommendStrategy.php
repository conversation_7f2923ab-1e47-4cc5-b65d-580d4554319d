<?php


namespace PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo\strategy;

use BizLib\Libraries\RedisDB;
use BizLib\Utils\Common as UtilsCommon;
use Nuwa\ApolloSDK\Apollo;

/**
 * D1品类认知增强逻辑
 * Class D1HighlightRecommendLogic
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo
 */
class DoubleD1HighlightRecommendStrategy extends RecommendStrategy
{
    // 新样式
    const D1_HIGHLIGHT_THEME_TYPE = 4;
    const FORM_SHOW_TYPE          = 2;

    /**
     * 设置推荐标签
     * @param array $aResponse aResponse
     * @return array
     */
    protected function buildRecommendTag($aResponse) {
        $aResponse['recommend_tag'] = [
            'content'   => $this->_aRecommendInfo['text'],
            'left_icon' => $this->_aRecommendInfo['icon'],
        ];
        $sKey = UtilsCommon::getRedisPrefix(P_ESTIMATE_BUBBLE_ACTIVITY) . $aResponse['estimate_id'];
        \PreSale\Logics\redismove\RedisWrapper::getInstance(P_ESTIMATE_BUBBLE_ACTIVITY)->setex($sKey,30,json_encode(array_merge($aResponse['recommend_tag'], ['recommend_info' => $this->_aRecommendInfo])));
        return $aResponse;
    }

    /**
     * 设置主题类型
     * @param array $aResponse aResponse
     * @return array
     */
    protected function buildThemeType($aResponse) {
        $aResponse['theme_type']     = $this->conflictWithGuide() ? self::DEFAULT_THEME_TYPE : self::D1_HIGHLIGHT_THEME_TYPE;
        $aResponse['form_show_type'] = self::FORM_SHOW_TYPE;
        return $aResponse;
    }

    /**
     * 设置主题数据
     * @param array $aResponse aResponse
     * @return array
     */
    protected function buildThemeData($aResponse) {
        if (parent::DEFAULT_THEME_TYPE == $aResponse['theme_type']) {
            return $aResponse;
        }

        if (!empty($this->_aConfig)) {
            $aResponse['theme_data'] = array(
                'title'                 => $this->_aConfig['title'],
                'title_icon'            => $this->_aConfig['title_icon'],
                'sub_title'             => $this->_aConfig['sub_title'],
                'right_image'           => $this->_aConfig['right_image'],
                'texture_image'         => $this->_aConfig['texture_image'],

                'car_image'             => $this->_aConfig['car_image'],
                'theme_color'           => $this->_aConfig['theme_color'],
                'shadow_color'          => $this->_aConfig['shadow_color'],

                'selected_bg_gradients' => [
                    $this->_aConfig['selected_bg_gradients']['start_color'],
                    $this->_aConfig['selected_bg_gradients']['end_color'],
                ],
            );
            $aResponse['theme_data']['outer_bg_gradients'] = [
                $this->_aConfig['out_bg_gradients']['end_color'],
                $this->_aConfig['out_bg_gradients']['start_color'],
            ];
            if ('' != $this->_aConfig['out_bg_gradients']['mid_color']) {
                $aResponse['theme_data']['outer_bg_gradients'] = [
                    $this->_aConfig['out_bg_gradients']['end_color'],
                    $this->_aConfig['out_bg_gradients']['mid_color'],
                    $this->_aConfig['out_bg_gradients']['start_color'],
                ];
            }
        }

        return $aResponse;
    }

    /**
     * 初始化配置
     * @return array
     */
    protected function getConfig() {
        list($bCityOk, $aCityConfigList) = Apollo::getInstance()->getConfigsByNamespace('d1_bubble_city_config')->getAllConfigData();
        list($bThemeOk, $aThemeConfig)   = Apollo::getInstance()->getConfigsByNamespace('d1_bubble_double_theme_data')->getAllConfigData();
        if (!$bCityOk || !$bThemeOk) {
            return [];
        }

        list($aCityConfig, $aCountryConfig) = $this->_matchCityConfig($aCityConfigList);
        $aConfig = $this->_getThemeData($aCityConfig, $aThemeConfig);
        if (!empty($aConfig)) {
            return $aConfig;
        }

        $aConfig = $this->_getThemeData($aCountryConfig, $aThemeConfig);
        return $aConfig;
    }

    /**
     * 获取城市配置及全国兜底配置
     * @param array $aAllCityConfig 全国城市配置
     * @return array
     */
    private function _matchCityConfig($aAllCityConfig) {
        $aCityConfig    = [];
        $aCountryConfig = [];
        foreach ($aAllCityConfig as $singleConfig) {
            if (isset($singleConfig['city_id']) && $this->_aInfo['order_info']['area'] == $singleConfig['city_id']) {
                $aCityConfig = $singleConfig;
            }

            if (isset($singleConfig['city_id']) && self::COUNTRYWIDE_CITY_ID == $singleConfig['city_id']) {
                $aCountryConfig = $singleConfig;
            }
        }

        return [$aCityConfig, $aCountryConfig];
    }

    /**
     * @param array $aCityConfig  命中城市配置
     * @param array $aThemeConfig 全部主题配置
     * @return array|mixed
     */
    private function _getThemeData($aCityConfig, $aThemeConfig) {
        if (empty($aCityConfig)) {
            return [];
        }

        //优先读取apollo配置
        if (!empty($aCityConfig['ab_test_key'])) {
            $oFeatureToggle = Apollo::getInstance()->featureToggle(
                $aCityConfig['ab_test_key'],
                [
                    'key'           => $this->_aInfo['passenger_info']['pid'],
                    'city'          => $this->_aInfo['order_info']['area'],
                    'phone'         => $this->_aInfo['passenger_info']['phone'],
                    'app_version'   => $this->_aInfo['common_info']['app_version'],
                    'access_key_id' => $this->_aInfo['common_info']['access_key_id'],
                    'time'          => time(),
                ]
            );
            if ($oFeatureToggle->allow()) {
                $sThemeKey        = $oFeatureToggle->getParameter('double_theme_key', '');
                $sBottomLabelList = $oFeatureToggle->getParameter('bottom_label_list', '');
                if (!empty($sThemeKey) && !empty($sBottomLabelList)) {
                    foreach ($aThemeConfig as $singleConfig) {
                        if (!empty($singleConfig['theme_key']) && $singleConfig['theme_key'] == $sThemeKey) {
                            $aConfig = $singleConfig;
                            $aConfig['bottom_label_list'] = explode(',', $sBottomLabelList);
                            return $aConfig;
                        }
                    }
                }
            }
        }

        //读取theme_key对应数据
        if (!empty($aCityConfig['double_theme_key'])) {
            foreach ($aThemeConfig as $singleConfig) {
                if (isset($singleConfig['theme_key']) && $singleConfig['theme_key'] == $aCityConfig['double_theme_key']) {
                    return $singleConfig;
                }
            }
        }

        return [];
    }
}
