<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo\strategy;

use BizLib\Config as NuwaConfig;
use BizLib\Constants\Common as constantsCommon;
use BizLib\Libraries\RedisDB;
use BizLib\Utils\Common as UtilsCommon;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\estimatePriceV2\multiRequest\ProductGroup;
use PreSale\Logics\estimatePriceV2\multiResponse\component\athenaGuideInfo\Common;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use PreSale\Logics\estimatePriceV2\multiResponse\productAggregation\Handler as groupHandler;
use PreSale\Logics\estimatePriceV2\multiResponse\component\athenaGuideInfo\Common as guideCommon;

/**
 * 载人车推荐样式
 * Class MannedVehicleRecommendStrategy
 * @package  PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo\strategy
 */
class MannedVehicleRecommendStrategy extends RecommendStrategy
{
    //标准品类推荐主题类型

    /**
     * 设置主题类型
     * @param array $aResponse aResponse
     * @return array
     */
    protected function buildThemeType($aResponse) {
        if (guideCommon::conflictWithGuide($this->_aInfo)) {
            $this->_iThemeType = self::DEFAULT_THEME_TYPE;
        } elseif (Common::isNewHighLightStyle($this->_aInfo)) {
            $this->_iThemeType           = Common::STANDARD_NEW_RECOMMEND_THEME_TYPE;
            $aResponse['form_show_type'] = 2;
        } else {
            $this->_iThemeType           = Common::STANDARD_RECOMMEND_THEME_TYPE;
            $aResponse['form_show_type'] = 2;
        }

        $aResponse['theme_type'] = $this->_iThemeType;
        return $aResponse;
    }

    /**
     * 设置主题数据
     * @param array $aResponse aResponse
     * @return array
     */
    protected function buildThemeData($aResponse) {
        $textConfig = NuwaConfig::text('config_text', 'manned_vehicle_suggest');
        $aResponse['extra_estimate_data']['left_down_icon_text'] = $textConfig['left_down_icon_text'];
        if (parent::DEFAULT_THEME_TYPE != $aResponse['theme_type'] && !empty($this->_aConfig)) {
            $themeData = array(
                'title'      => $textConfig['before_recommendation_content'],
                'title_icon' => $textConfig['recommendation_icon'],
            );
            $aResponse['extra_estimate_data']['theme_data'] = $themeData;
            $themeData = array(
                'theme_color'           => $this->_aConfig['theme_color'],
                'button_font_color'     => $this->_aConfig['button_font_color'],
                'inner_bg_color'        => $this->_aConfig['inner_bg_color'],
                'selected_bg_gradients' => [
                    $this->_aConfig['selected_bg_gradients']['start_color'],
                    $this->_aConfig['selected_bg_gradients']['end_color'],
                ],
                'outer_bg_gradients'    => [
                    $this->_aConfig['outer_bg_gradients']['start_color'],
                    $this->_aConfig['outer_bg_gradients']['end_color'],
                ],
                'title'                 => $textConfig['after_recommendation_content'],
                'title_icon'            => $textConfig['recommendation_icon'],
            );
            $aResponse['theme_data'] = $themeData;
            if (Common::STANDARD_RECOMMEND_THEME_TYPE == $aResponse['theme_type']) {
                if ((version_compare(MainDataRepo::getAppVersion(), '6.2.6') < 0 && constantsCommon::DIDI_IOS_PASSENGER_APP == $this->_aInfo['common_info']['access_key_id'])
                    || (version_compare(MainDataRepo::getAppVersion(), '6.2.8') < 0 && constantsCommon::DIDI_ANDROID_PASSENGER_APP == $this->_aInfo['common_info']['access_key_id'])
                ) {
                    $aResponse['recommend_tag'] = [
                        'content'   => $themeData['title'],
                        'left_icon' => $themeData['title_icon'],
                    ];
                }
            }
        }

        return $aResponse;
    }

    /**
     * 设置推荐标签
     * @param array $aResponse aResponse
     * @return array
     */
    protected function buildRecommendTag($aResponse) {
        $textConfig = NuwaConfig::text('config_text', 'manned_vehicle_suggest');

        if (self::DEFAULT_THEME_TYPE == $aResponse['theme_type']) {
            //主题降级标签
            //出租车盒子，不需要把内部的标签放到盒子上，所以内外都是降级标签
            $aResponse['recommend_tag'] = [
                'left_icon' => $textConfig['degrade_recommend_tag'],
                'content'   => $textConfig['degrade_recommend_content'],
            ];
        }

        return $aResponse;
    }


    /**
     * 初始化配置
     * @return array
     */
    protected function getConfig() {
        //优先读取品类个性化配置
        list($bOk, $aAllProductConf) = Apollo::getInstance()->getConfigsByNamespaceAndConditions(
            'standard_recommend_product_conf',
            ['product_category' => $this->_aInfo['order_info']['product_category'],]
        )->getAllConfigData();

        if ($bOk && !empty($aAllProductConf)) {
            $aProductConf = array_pop($aAllProductConf);
            list($bOk, $aAllThemeConfig) = Apollo::getInstance()->getConfigsByNamespaceAndConditions(
                'standard_recommend_theme_conf',
                ['theme_key' => $aProductConf['theme_key'],]
            )->getAllConfigData();
            if ($bOk && !empty($aAllThemeConfig)) {
                return array_pop($aAllThemeConfig);
            }
        }

        //未读取到个性化配置，读取默认配置
        list($bOk, $aAllThemeConfig) = Apollo::getInstance()->getConfigsByNamespaceAndConditions(
            'standard_recommend_theme_conf',
            ['theme_key' => 'default',]
        )->getAllConfigData();
        if ($bOk && !empty($aAllThemeConfig)) {
            return array_pop($aAllThemeConfig);
        }

        return array();
    }
}
