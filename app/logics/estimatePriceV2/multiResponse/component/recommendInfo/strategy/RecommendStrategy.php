<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo\strategy;

use PreSale\Logics\estimatePriceV2\multiResponse\component\GuideInfo;

/**
 * 品类推荐营销逻辑
 * Class RecommendLogic
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo
 */
class RecommendStrategy
{
    const COUNTRYWIDE_CITY_ID        = 0;
    const DEFAULT_THEME_TYPE         = 0;
    const WITH_TITLE_THEME_TYPE      = 4; //带title的theme type
    const RECOMMEND_CONFIG_NAMESPACE = 'bubble_theme_config';

    protected $_aInfo;
    protected $_aConfig;
    protected $_aRecommendInfo;

    protected $_iThemeType           = self::DEFAULT_THEME_TYPE;
    protected $_aThemeData           = null;
    protected $_aThemeRecommendTag   = null;
    protected $_aOriginRecommendTag  = null;
    protected $_aThemePriceInfoDesc  = null;
    protected $_aOriginPriceInfoDesc = null;

    /**
     * RecommendLogic constructor.
     * @param array $aInfo          表单数据
     * @param array $aRecommendInfo athena推荐信息
     */
    public function __construct($aInfo, $aRecommendInfo) {
        $this->_aInfo          = $aInfo;
        $this->_aRecommendInfo = $aRecommendInfo;
        $this->_aConfig        = $this->getConfig();
    }

    /**
     * 构建推荐信息
     * @param array $aResponse aResponse
     * @return array
     */
    public function buildRecommendInfo($aResponse) {
        $aResponse = $this->buildThemeType($aResponse);
        $aResponse = $this->buildRecommendTag($aResponse);
        $aResponse = $this->buildThemeData($aResponse);
        $aResponse = $this->buildPriceDesc($aResponse);
        return $aResponse;
    }

    // 注释-待删
//    /**
//     * 构建价格描述信息
//     * @param array $aCouponInfo aCouponInfo
//     * @return void
//     */
//    public function recordCouponDesc($aCouponInfo) {
//        foreach ($aCouponInfo as $iProductCategory => $aCoupon) {
//            if ($this->_aInfo['order_info']['product_category'] == $iProductCategory) {
//                $this->_aOriginPriceInfoDesc = $aCoupon;
//                break;
//            }
//        }
//    }

    /**
     * 设置推荐标签
     * @param array $aResponse aResponse
     * @return array
     */
    protected function buildRecommendTag($aResponse) {
        return $aResponse;
    }

    /**
     * 设置主题类型
     * @param array $aResponse aResponse
     * @return array
     */
    protected function buildThemeType($aResponse) {
        return $aResponse;
    }

    /**
     * 设置主题数据
     * @param array $aResponse aResponse
     * @return array
     */
    protected function buildThemeData($aResponse) {
        return $aResponse;
    }


    /**
     * 设置价格描述信息
     * @param array $aResponse aResponse
     * @return array
     */
    protected function buildPriceDesc($aResponse) {
        return $aResponse;
    }

    /**
     * 主题样式降级。
     * 降级场景：link品类，（盒子内部品类暂时不需要）。
     * @param array $aItem dItem
     * @return array
     */
    public function degradeEstimateData($aItem) {
        return $aItem;
    }

    /**
     * 初始化配置
     * @return array
     */
    protected function getConfig() {
        return array();
    }

    /**
     * 推荐品类是否与导流位冲突
     * @return bool
     */
    protected function conflictWithGuide() {
        $aGuideInfo = GuideInfo::getInstance()->getGuideProductInfo();
        return !empty($aGuideInfo) && $aGuideInfo['order_info']['estimate_id'] != $this->_aInfo['order_info']['estimate_id'];
    }
}
