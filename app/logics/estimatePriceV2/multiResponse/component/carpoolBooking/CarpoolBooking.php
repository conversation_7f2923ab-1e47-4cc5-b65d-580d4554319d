<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\carpoolBooking;

use <PERSON>izCommon\Logics\Carpool\CarpoolBrand;
use <PERSON>izCommon\Utils\Horae;
use BizLib\Log;
use PreSale\Logics\estimatePriceV2\multiResponse\component\carpoolScene\Common;
use Xiaoju\Apollo\Apollo as ApolloV2;
use Xiaoju\Apollo\ApolloConstant;
use BizLib\Constants;
use PreSale\Logics\carpool\Coupon;
use PreSale\Logics\estimatePriceV2\response\CarpoolBookingLogic;
use PreSale\Logics\estimatePriceV2\response\SceneResponseLogicV2;
use PreSale\Logics\order\CustomedOrderLogic;
use BizLib\Utils\Language;
use BizLib\Config as NuwaConfig;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;

/**
 * Class CarpoolBooking
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\carpoolBooking
 */
class CarpoolBooking
{
    protected $_aInfo           = null;
    protected $_aResponse       = null;
    protected $_oApollo         = null;
    protected $_sCarLevel       = '';
    protected $_aLowPriceConfig = [];
    /**
     * CarpoolBooking constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo     = $aInfo;
        $this->_oApollo   = new ApolloV2();
        $this->_sCarLevel = $this->_aInfo['order_info']['require_level'];
        $oCoupon          = new Coupon();
        $this->_aLowPriceConfig = $oCoupon->getConfig($this->_aInfo['order_info']['area'], $this->_aInfo['common_info']['app_version'], $this->_aInfo['passenger_info']);
    }

    /**
     * @param array $aResponse response
     * @return array
     */
    public function build($aResponse) {
        $this->_aResponse = $aResponse;
        $this->_buildDepartureTimeText();
        $this->_buildCarpoolBooking();
        return $this->_aResponse;
    }

    /**
     * @return void
     */
    private function _buildCarpoolBooking() {
        if (\BizCommon\Utils\Horae::isLowPriceCarpool($this->_aInfo['order_info']['n_tuple'])) {
                $aMatchRoutes = $this->_aInfo['bill_info']['match_routes'][0];
                $aTitleText   = json_decode(Language::getTextFromDcmp('config_carpool-low_price_carpool_time_dialog',['-']), true);
                $this->_aResponse['station_carpool_booking'] = [
                    'is_interrupt'             => empty(\Nuwa\Core\Dispatcher::getInstance()->getRequest()->getQuery('departure_range')) ? 1 : 0,
                    'dialog_title'             => $aTitleText['dialog_title'] ?? '',
                    'dialog_sub_title'         => $aTitleText['dialog_sub_title'] ?? '',
                    'dialog_sub_title_booking' => $aTitleText['dialog_sub_title_booking'] ?? '',
                    'title'                    => $aMatchRoutes['time_span'][0]['time'][0]['text'] ?? '',
                    'min_price'                => null,
                    'time_span'                => $aMatchRoutes['time_span'],
                    'first_span_fit'           => false,
                ];
                $this->_buildLowPriceButton();
                $this->_buildLowPriceExtraTag();
                $this->_buildLowPriceCarpoolTimeSpan();
                $this->_buildNewStationBooking();
                return;
        }

        if ($this->_oApollo->featureToggle(
            'carpool_station_booking_open',
            array(
                'key'            => (int)($this->_aInfo['passenger_info']['pid']),
                'district'       => (string)($this->_aInfo['order_info']['district']),
                'city'           => (int)($this->_aInfo['order_info']['area']),
                'phone'          => (string)($this->_aInfo['passenger_info']['phone']),
                'is_from_webapp' => (int)($this->_aInfo['common_info']['is_from_webapp']),
                'product_id'     => (string)($this->_aInfo['order_info']['product_id']),
            )
        )->allow()
        ) {
            $iCarpoolComboType = isset($this->_aInfo['bill_info']['product_infos'][$this->_sCarLevel]['combo_type']) ? $this->_aInfo['bill_info']['product_infos'][$this->_sCarLevel]['combo_type'] : (isset($this->_aInfo['order_info']['combo_type']) ? $this->_aInfo['order_info']['combo_type'] : 0);
            if (version_compare($this->_aInfo['common_info']['app_version'], '5.2.0') >= 0 && Constants\Horae::TYPE_COMBO_CARPOOL == $iCarpoolComboType) {
                $aCarpoolBookingInfo = (new CarpoolBookingLogic())->getBookingInfo($this->_aInfo, $this->_aResponse);
                $this->_aResponse['station_carpool_booking'] = $aCarpoolBookingInfo;
                if (!empty($aCarpoolBookingInfo)) {
                    $this->_aResponse['willing_wait_info'] = null;
                }
            }
        }
    }

    /**
     *@return void
     */
    private function _buildDepartureTimeText() {
        $this->_aResponse = CustomedOrderLogic::getDepartureTimeText($this->_aInfo, $this->_aResponse);
    }

    /**
     * 拼成乐时间片增加运营tag
     * @return void
        ['time_span']  => []*TimeSpan // 切片中每个TimeSpan 对应 每天 的可选时间
        type TimeSpan struct {
	        Title string      `json:"title"` // "今天" | "明天"
	        Time  []Time      `json:"time"`// 切片中每个Time 对应 这一天，每个小时 的可选时间
        }
        type Time struct {
      	    Text       string        `json:"text"` // ["明天"]?%d点%d分 | 当前时间片是当天的第一个，且 左 < 当前时刻 < 右 时为 {{hour}}点{{minute}}分前随时出发 hour, minute为第一个小时组的第一个分钟段
      	    Hour       int           `json:"hour"` // 小时， 数字， 比如10:40， 这里为10
      	    Gap        int64         `json:"gap"` // 时间片长度/2 单位分钟
      	    Desc       string        `json:"desc"` // 前后{{Gap}}分钟出发
      	    MinuteList []MinuteValue `json:"minute_list"` // 切片中每个MinuteValue 对应这个小时 每个分钟段 的可选时间
        }
        type MinuteValue struct {
      	    Minute        int    `json:"minute"` // 小时， 数字， 比如10:40， 这里为40
       	    Value         string `json:"value"` // "[1564544100,1564544400]" 时间片的起终点时间戳，Gap == (右-左)/2
      	    Selected      bool   `json:"default_selected"` // 只有当前时间片是当天的第一个，且 左 < 当前时刻 < 右 时为1， 其他均为0
      	    ValueNow      string `json:"value_now"` //只有当前时间片是当天的第一个，且 左 < 当前时刻 < 右 时为 "[0,1564544400]" 左必为0，左+Gap == 右, 其他为空
      	    Text          string `json:"checkbox_text"` // 当前时间片是当天的第一个，且 左 < 当前时刻 < 右 时为 "随时出发"; ["明天"]?%d点%d分
      	    ButtonText    string `json:"button_text"` // 15:04-15:04出发， 左时间为时间片起点， 右 == 左+Gap*2, 格式为“%h:%m”
      	    ButtonTextNow string `json:"button_text_now"` // 当前时间片是当天的第一个，且 左 < 当前时刻 < 右 时为 "现在-{{Start+Gap}}随时出发"
        }
     */
    private function _buildLowPriceCarpoolTimeSpan() {
        if (empty($this->_aResponse['station_carpool_booking']['time_span']) || version_compare($this->_aInfo['common_info']['app_version'], '5.3.18') < 0) {
            return;
        }

        if (\BizCommon\Utils\Horae::isLowPriceCarpoolV2($this->_aInfo['order_info']['n_tuple'])) {
            // v2 由于时间片组件外显， 使用 {{hour}}点{{minute}}分前随时出发 这种汉字格式的显示宽度不够，故调整格式
            $aTimeSpanTextTemplate = Language::getDecodedTextFromDcmp('config_carpool-time_slice_desc');
            // 处理第一个时间片
            $sFirstSpanRange = $this->_aResponse['station_carpool_booking']['time_span'][0]['time'][0]['minute_list'][0]['value_now'] ?? '';
            if (!empty($sFirstSpanRange)) {
                $this->_aResponse['station_carpool_booking']['time_span'][0]['time'][0]['text'] = Language::replaceTag(
                    $aTimeSpanTextTemplate['pincheche_v2']['predict_departure_text'],
                    [
                        'time_str' => sprintf(
                            '%02d:%02d',
                            $this->_aResponse['station_carpool_booking']['time_span'][0]['time'][0]['hour'],
                            $this->_aResponse['station_carpool_booking']['time_span'][0]['time'][0]['minute_list'][0]['minute']
                        ),
                    ]
                );
                $this->_aResponse['station_carpool_booking']['time_span'][0]['time'][0]['minute_list'][0]['button_text_now'] = Language::replaceTag(
                    $aTimeSpanTextTemplate['pincheche_v2']['departure_text'],
                    [
                        'time_start' => $aTimeSpanTextTemplate['pincheche_v2']['text_now'],
                        'time_end'   => sprintf(
                            '%02d:%02d',
                            $this->_aResponse['station_carpool_booking']['time_span'][0]['time'][0]['hour'],
                            $this->_aResponse['station_carpool_booking']['time_span'][0]['time'][0]['minute_list'][0]['minute']
                        ),
                    ]
                );
            }

            $this->_aResponse['station_carpool_booking']['title'] = $this->_aResponse['station_carpool_booking']['time_span'][0]['time'][0]['text'];

            if (count($this->_aResponse['station_carpool_booking']['time_span']) >= 2) {
                // 第一组非空, 即当天还有可用时间片, 即用户可直接发单
                $args = [
                    ApolloConstant::APOLLO_USER_KEY => $this->_aInfo['passenger_info']['pid'],
                    'city'                          => $this->_aInfo['order_info']['area'],
                    'phone'                         => $this->_aInfo['passenger_info']['phone'],
                ];

                $oApolloExp = (ApolloV2::getInstance())->featureToggle('gs_pincheche_v2_neworder_popup', $args);
                if ($oApolloExp->allow() && 'treatment_group' == $oApolloExp->getGroupName()) {
                    $this->_aResponse['station_carpool_booking']['first_span_fit'] = true;
                }
            }
        }

        //组装时间运营标签
        $aTimeTag = $this->_aLowPriceConfig['time_tag'];
        if (is_null($aTimeTag) || empty($aTimeTag) || !is_array($aTimeTag)) {
            return;
        }

        foreach ($this->_aResponse['station_carpool_booking']['time_span'] as &$item) {
            if (empty($item['time'])) {
                continue;
            }

            foreach ($aTimeTag as $aTag) {
                foreach ($item['time'] as &$time) {
                    $aMinSpan = json_decode($time['minute_list'][0]['value'], true) ?? [];
                    //标签有效期校验，没有配置有效期则为永久生效
                    if (!empty($aTag['available_time'])) {
                        $iLeft  = strtotime($aTag['available_time'][0] . ' 00:00:00');
                        $iRight = strtotime($aTag['available_time'][1] . ' 23:59:59');
                        //不在有效期内
                        if (!($iLeft <= $aMinSpan[1] && $aMinSpan[1] <= $iRight)) {
                            break;
                        }
                    }

                    if ($aTag['hour'][0] <= $time['hour'] && $time['hour'] <= $aTag['hour'][1]) {
                        $time['tag'] = $aTag;
                        unset($time['tag']['hour']);
                        unset($time['tag']['available_time']);
                    }
                }
            }
        }
    }

    /**
     * @return void
     */
    private function _buildNewStationBooking() {
        if (!Horae::isLowPriceCarpoolV2($this->_aInfo['order_info']['n_tuple'])) {
            return;
        }

        if (!Common::isInPingCheCheApollo($this->_aInfo)) {
            return;
        }

        $aTimeSpanTextTemplate = Language::getDecodedTextFromDcmp('config_carpool-time_slice_desc');
        foreach ($this->_aResponse['station_carpool_booking']['time_span'] as &$item) {
            if (empty($item['time'])) {
                continue;
            }

            foreach ($item['time'] as &$time) {
                $time['desc'] = $aTimeSpanTextTemplate['pincheche_v2_new']['desc'];
                foreach ($time['minute_list'] as &$minute) {
                    $value           = json_decode($minute['value']);
                    $startDateHour   = date('H' , $value[0]);
                    $startDateMinitu = date('i' , $value[0]);
                    $endDateHour     = date('H' , $value[1]);
                    $endDateMinitu   = date('i' , $value[1]);
                    $timeStart       = sprintf(
                        '%02d:%02d',
                        $startDateHour,
                        $startDateMinitu
                    );
                    $timeEnd         = sprintf(
                        '%02d:%02d',
                        $endDateHour,
                        $endDateMinitu
                    );
                    $minute['button_text'] = language::replaceTag(
                        $aTimeSpanTextTemplate['pincheche_v2_new']['button_text'],
                        [
                            'time_start' => $timeStart,
                            'time_end'   => $timeEnd,
                        ]
                    );
                }
            }
        }

        $this->_aResponse['station_carpool_booking']['title'] = $aTimeSpanTextTemplate['pincheche_v2_new']['now_text'];
        $sFirstSpanRange = $this->_aResponse['station_carpool_booking']['time_span'][0]['time'][0]['minute_list'][0]['value_now'] ?? '';
        if (!empty($sFirstSpanRange)) {
            $this->_aResponse['station_carpool_booking']['time_span'][0]['time'][0]['minute_list'][0]['checkbox_text']   = $aTimeSpanTextTemplate['pincheche_v2_new']['now_text'];
            $this->_aResponse['station_carpool_booking']['time_span'][0]['time'][0]['minute_list'][0]['button_text_now'] = $aTimeSpanTextTemplate['pincheche_v2_new']['now_text'];
        }

    }

    /**
     * 拼成乐增加价格营销标签
     * @return void
     */
    private function _buildLowPriceExtraTag() {
        $aExtraTextDcmp = json_decode(Language::getTextFromDcmp('config_carpool-low_price_carpool_extra_tag',['-']),true);
        $aExtraText     = $this->_aLowPriceConfig['extra_tag'];
        if (!empty($aExtraText) && isset($aExtraText['type']) && isset($aExtraText['title']) && isset($aExtraText['icon'])) {
            $this->_aResponse['station_carpool_booking']['extra_tag'] = $aExtraText;
        } elseif (!empty($aExtraTextDcmp)) {
            $this->_aResponse['station_carpool_booking']['extra_tag'] = $aExtraTextDcmp;
        }
    }

    /**
     * 拼成乐时间组件按钮文案
     * @return void
     */
    private function _buildLowPriceButton() {
        //青菜拼车
        if (CarpoolBrand::carpoolBrandOpen($this->_aInfo['common_info']['app_version'], $this->_aInfo['order_info'])) {
            $aEstimateText = NuwaConfig::text('config_text', 'mOrderEstimateNew');
            if ((MainDataRepo::isApplet())) {//微信/支付宝 小程序
                $aParam  = [
                    Apollo::APOLLO_INDIVIDUAL_ID => $this->_aInfo['passenger_info']['pid'],
                    'city'                       => $this->_aInfo['order_info']['area'],
                    'phone'                      => $this->_aInfo['passenger_info']['phone'],
                ];
                $oToggle = Apollo::getInstance()->featureToggle('gs_pincheche_webapp_time_slice_button_text', $aParam);
                if ($oToggle->allow()) {//命中ab实验，从ab开关拿文案
                    $sButtonText        = $oToggle->getParameter('button_text', '');
                    $sButtonTextConfirm = $oToggle->getParameter('button_text_confirm', '');
                } else {
                    $sButtonText        = $aEstimateText['carpool_brand_webapp_confirm_button_text'];
                    $sButtonTextConfirm = $aEstimateText['carpool_brand_webapp_confirm_time_button_text'];
                }
            } else {//native或者其他
                $sButtonText        = $aEstimateText['carpool_brand_confirm_button_text'];// 确认呼叫青菜拼车
                $sButtonTextConfirm = $aEstimateText['carpool_brand_confirm_time_button_text'];// 确认
            }
        }

        $this->_aResponse['station_carpool_booking']['button_text']         = $sButtonText ?? '';
        $this->_aResponse['station_carpool_booking']['button_text_confirm'] = $sButtonTextConfirm ?? '';
    }
}
