<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\carpoolBooking;

use PreSale\Logics\estimatePriceV2\multiResponse\Util;

/**
 * Class Handler
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\carpoolBooking
 */
class Handler
{
    /**
     * @param array $aInfo aInfo
     * @return null|CarpoolBooking
     */
    public static function select($aInfo) {
        if (Util::isCarpool($aInfo)) {
            return new CarpoolBooking($aInfo);
        }

        return null;
    }
}
