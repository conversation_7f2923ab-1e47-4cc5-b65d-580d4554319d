<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo;

use BizLib\Config as NuwaConfig;
use BizLib\Constants\OrderSystem;
use BizLib\Utils\Language;
use BizLib\Constants;
use BizLib\Utils\ProductCategory;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\estimatePriceV2\multiResponse\component\athenaGuideInfo\Common as guideCommon;
use PreSale\Logics\estimatePriceV2\multiResponse\component\paymentInfo\Common as payCommon;
use Xiaoju\Apollo\Apollo as ApolloV2;
use BizLib\Utils\CarLevel;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;
use BizLib\Log as NuwaLog;

/**
 * Class Common
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo
 */
class Common
{

    const ENABLE_PRICE_INFO_DESC_VERSION       = '5.2.46';
    const ENABLE_PRICE_INFO_DESC_VERSION_INTER = '6.0.10'; // 城际使用该字段的版本
    private static $_aPriceDescGif  = [];
    private static $_aPriceDescAnim = [];
    private $_ainfo = [];
    const PRICE_TYPE_NEWCPMER_COUPON_RECALL = 3; // 呼返优惠新客券
    const PRICE_TYPE_SINGLE_FOLD            = 4; //单单折
    const PRICE_TYPE_LONG_DISTANCE_CARPOOL  = 5; // 远途特价


    /**
     * DoubleLinePriceDesc constructor.
     * @param array $aInfo aInfo
     */
    public function __construct(array $aInfo) {
        $this->_ainfo = $aInfo;
    }
    /**
     * 获取拼车载人车推荐相关配置
     * @return bool
     */
    public static function getMannedVehicleSuggestInfo() {
        $textConfig = NuwaConfig::text('config_text', 'manned_vehicle_suggest');
        return $textConfig;
    }
    /**
     * 格式化拼车载人车推荐样式
     * @param bool $isBefore isBefore
     * @return array
     */
    public static function formatMannedVehicleCarpoolDescByTime($isBefore) {
        $mannedConfig     = self::getMannedVehicleSuggestInfo();
        $aPriceInfoDesc[] = [
            'content' => $mannedConfig['after_price_content'],
        ];
        if ($isBefore) {
            $aPriceInfoDesc[0]['content'] = $mannedConfig['before_price_content'];
        }

        $config = Common::_getLimitTImeConfig();
        $aPriceInfoDesc[0]['bg_gradients']  = array(
            $config['coupon_desc_conf']['bg_gradients']['start_color'],
            $config['coupon_desc_conf']['bg_gradients']['end_color'],
        );
        $aPriceInfoDesc[0]['bg_fill_color'] = $config['coupon_desc_conf']['bg_fill_color'];
        $aPriceInfoDesc[0]['font_color']    = $config['coupon_desc_conf']['font_color'];
        return $aPriceInfoDesc;
    }

    /**
     * 格式化拼车载人车response
     * @param array $aResponse aResponse
     * @param array $aInfo     aInfo
     * @return array
     */
    public static function formatMannedVehicleExtraResponse($aResponse, $aInfo) {
        if (ProductCategory::PRODUCT_CATEGORY_CARPOOL_STATION == $aInfo['order_info']['product_category'] && guideCommon::isMannedVehicleSuggest($aInfo) && !payCommon::isEnterprisePay($aInfo)) {
            $config = Common::getMannedVehicleSuggestInfo();
            $aResponse['extra_estimate_data']['fee_msg'] = Language::replaceTag(
                $config['degrade_fee_msg'],
                ['amount' => $aInfo['activity_info'][0]['estimate_fee']]
            );
            if (in_array($aInfo['order_info']['access_key_id'], [Constants\Common::DIDI_ALIPAY_MINI_PROGRAM, Constants\Common::DIDI_WECHAT_MINI_PROGRAM])) {
                $aResponse['extra_estimate_data']['fee_msg'] = Language::replaceTag(
                    $config['mini_program_degrade_fee_msg'],
                    ['amount' => $aInfo['activity_info'][0]['estimate_fee']]
                );
            }

            if (guideCommon::isSmallPhone($aInfo)) {
                $aResponse['extra_estimate_data']['fee_msg'] = Language::replaceTag(
                    $config['degrade_small_fee_msg'],
                    ['amount' => $aInfo['activity_info'][0]['estimate_fee']]
                );
                if (in_array($aInfo['order_info']['access_key_id'], [Constants\Common::DIDI_ALIPAY_MINI_PROGRAM, Constants\Common::DIDI_WECHAT_MINI_PROGRAM])) {
                    $aResponse['extra_estimate_data']['fee_msg'] = Language::replaceTag(
                        $config['mini_program_degrade_small_fee_msg'],
                        ['amount' => $aInfo['activity_info'][0]['estimate_fee']]
                    );
                }
            }

            $aResponse['extra_estimate_data']['extra_order_params']  = guideCommon::getMannedVehicleExtraInfo($aInfo);
            $aResponse['extra_estimate_data']['count_down']          = $config['count_down'];
            $aResponse['extra_estimate_data']['left_down_icon_text'] = $config['left_down_icon_text'];
            if (guideCommon::isMannedVehicleSuggestHighLight($aInfo)) {
                if (!guideCommon::isMannedVehicleDegrade($aInfo)) {
                    $aResponse['extra_estimate_data']['price_info_desc'] = Common::formatMannedVehicleCarpoolDescByTime(true);
                    $feeMsg = Language::replaceTag(
                        $config['fee_msg'],
                        ['amount' => $aInfo['activity_info'][0]['estimate_fee']]
                    );
                    $aResponse['extra_estimate_data']['fee_msg'] = $feeMsg;
                }
            }
        }

        return $aResponse;
    }

        /**
     * 获取长途特价实验文案
     * @param string $amount 金额
     * @param string $sUnit  货币单位
     * @return string
     */
    public function getLongDistanceCarpoolContent($amount, $sUnit) {
        $config  = Common::_getLimitTImeConfig();
        $content = $config['coupon_desc_conf']['double_line_content'];
        return Language::replaceTag(
            $content,
            ['amount' => $amount, 'currency_unit' => $sUnit]
        );
    }

    /**
     * 获取长途特价实验图片
     * @return string
     */
    public function getLongDistanceCarpoolIcon() {
        $config = Common::_getLimitTImeConfig();
        return $config['coupon_desc_conf']['left_icon'];
    }


    /**
     * @param array $aResponse      aResponse
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @param array $aInfo          aInfo
     * @return mixed
     */
    public static function formatPriceInfoDesc($aResponse, $aPriceDesc, $aPriceDescIcon, $aInfo, $aPriceInfoList = []) {
        //端上 新版本 升级价格描述 price_info_desc 替代 price_desc
        if (version_compare((string)($aInfo['common_info']['app_version']), self::ENABLE_PRICE_INFO_DESC_VERSION) >= 0) {
            $aApolloParam = [
                'key'                => $aInfo['passenger_info']['pid'],
                'phone'              => $aInfo['passenger_info']['phone'],
                'city'               => $aInfo['order_info']['area'],
                'car_level'          => $aInfo['order_info']['require_level'],
                'carpool_price_type' => $aInfo['order_info']['carpool_price_type'],
                'app_version'        => $aInfo['common_info']['app_version'],
            ];

            $bUpgrade = (new ApolloV2())->featureToggle('gs_upgrade_price_desc', $aApolloParam)->allow();

            if (\BizCommon\Utils\Horae::isLowPriceCarpool($aInfo['order_info'])) {
                $sDcmpKey = 'estimate_price_info_desc-low_price_carpool';
                $aResponse['price_info_desc'] = self::formatPriceInfo($aPriceDesc, $aPriceDescIcon, $aPriceInfoList, $sDcmpKey);
            } elseif (\BizCommon\Utils\Horae::isInterCityCarpool($aInfo['order_info'])
                && version_compare((string)($aInfo['common_info']['app_version']), self::ENABLE_PRICE_INFO_DESC_VERSION_INTER) >= 0
            ) {
                $sDcmpKey = 'estimate_price_info_desc-intercity_carpool';
                $aResponse['price_info_desc'] = self::formatPriceInfo($aPriceDesc, $aPriceDescIcon, $aPriceInfoList, $sDcmpKey);
            } elseif ($bUpgrade) {
                $aResponse['price_info_desc'] = self::formatPriceInfo($aPriceDesc, $aPriceDescIcon, $aPriceInfoList);
            }
        }

        //6.0 固定返回price_info_desc
        if (Util::isDaCheAnyCar($aInfo)) {
            $aPriceInfo = self::formatPriceInfo($aPriceDesc, $aPriceDescIcon, $aPriceInfoList);
            //6.0最多返回两行
            $aResponse['price_info_desc'] = array_slice($aPriceInfo, 0, 2);
        }

        return $aResponse;
    }

    /**
     * 用price_info_desc代替price_desc.
     * @param array  $aPriceDesc     aPriceDesc
     * @param array  $aPriceIcon     aPriceIcon
     * @param array  $aPriceInfoList aPriceInfoList
     * @param string $sDcmpKey       sDcmpKey
     * @param array  $aPriceAmount   aPriceAmount
     * @param array  $aPriceType     aPriceType
     * @return array
     */
    public static function formatPriceInfo($aPriceDesc, $aPriceIcon, $aPriceInfoList, $sDcmpKey = '', $aPriceAmount = [], $aPriceType = [], $aInfo = []) {
        if (empty($aPriceDesc)) {
            return [];
        }

        $aPriceInfoDesc = [];
        $sDcmpKey       = empty($sDcmpKey) ? 'estimate_price_info_desc-default' : $sDcmpKey;
        $aPriceInfoDescDefault = Language::getDecodedTextFromDcmp($sDcmpKey);
        foreach ($aPriceDesc as $key => $aValue) {
            //如果配置对应的price_info_desc
            if (isset($aPriceInfoList[$key]) && !empty($aPriceInfoList[$key])) {
                $aPriceInfoDesc[$key] = $aPriceInfoList[$key];
            } else {
                $aPriceInfoDesc[$key]            = $aPriceInfoDescDefault;
                $aPriceInfoDesc[$key]['content'] = $aPriceDesc[$key];
                $aPriceInfoDesc[$key]['amount']  = 0;
                $aPriceInfoDesc[$key]['type']    = PriceItemFormatter::getDefaultPriceType();

                if (!empty($aPriceIcon[$key])) {
                    $aPriceInfoDesc[$key]['left_icon'] = $aPriceIcon[$key];
                }

                if (!empty(self::$_aPriceDescGif[$key])) {
                    $aPriceInfoDesc[$key]['left_icon'] = self::$_aPriceDescGif[$key];
                }

                if (!empty(self::$_aPriceDescAnim[$key])) {
                    $aPriceInfoDesc[$key]['show_anim'] = self::$_aPriceDescAnim[$key];
                }

                if (isset($aPriceAmount[$key])) {
                    $aPriceInfoDesc[$key]['amount'] = $aPriceAmount[$key];
                }

                if (isset($aPriceType[$key])) {
                    $aPriceInfoDesc[$key]['type'] = $aPriceType[$key];

                    if (self::PRICE_TYPE_LONG_DISTANCE_CARPOOL == $aPriceType[$key] && !empty($aInfo) && Util::isEstimateFormDoubleLineStyle($aInfo)) {
                        $config = Common::_getLimitTImeConfig();
                        $aPriceInfoDesc[$key]['bg_gradients']  = array(
                            $config['coupon_desc_conf']['bg_gradients']['start_color'],
                            $config['coupon_desc_conf']['bg_gradients']['end_color'],
                        );
                        $aPriceInfoDesc[$key]['bg_fill_color'] = $config['coupon_desc_conf']['bg_fill_color'];
                        $aPriceInfoDesc[$key]['font_color']    = $config['coupon_desc_conf']['font_color'];
                    }
                }
            }
        }

        return $aPriceInfoDesc;
    }

    /**
     * 获取限时优惠的配置
     * @return array
     */
    private static function _getLimitTImeConfig() {
        //未读取到个性化配置，读取默认配置
        list($bOk, $aAllThemeConfig) = Apollo::getInstance()->getConfigsByNamespaceAndConditions(
            'standard_recommend_theme_conf',
            ['theme_key' => 'default',]
        )->getAllConfigData();
        if ($bOk && !empty($aAllThemeConfig)) {
            return array_pop($aAllThemeConfig);
        }

        return array();
    }

    /**
     * 格式化价格费用说明信息 v2版本
     * @param array $aPriceDesc     priceDesc
     * @param array $aPriceIcon     priceIcon
     * @param array $aPriceInfoList priceInfoList
     * @param array $aInfo          aInfo
     * @param array $aPriceAmount   aPriceAmount
     * @param array $aPriceType     aPriceType
     * @return array
     */
    public static function formatPriceInfoV2($aPriceDesc, $aPriceIcon, $aPriceInfoList, $aInfo, $aPriceAmount = [], $aPriceType = []) {
        $aPriceInfoDescV2 = self::formatPriceInfo($aPriceDesc, $aPriceIcon,$aPriceInfoList,'', $aPriceAmount, $aPriceType, $aInfo);
        if (empty($aPriceInfoDescV2)) {
            return [];
        }

        // 混合支付只展示一个价格描述项，因为端上只有三行位置，混合支付本身占用了两行
        if (count($aPriceInfoDescV2) >= 2 && Util::isShowMixePaymentMsg($aInfo)) {
            $aPriceInfoDescV2 = [$aPriceInfoDescV2[0]];
        }

        return $aPriceInfoDescV2;
    }

    /**
     * @param array $aDescGif aDescGif
     * @return void
     */
    public static function setPriceDescGif($aDescGif) {
        self::$_aPriceDescGif = $aDescGif;
    }

    /**
     * @param array $aDescAnim aDescAnim
     * @return void
     */
    public static function setPriceDescAnim($aDescAnim) {
        self::$_aPriceDescAnim = $aDescAnim;
    }
}
