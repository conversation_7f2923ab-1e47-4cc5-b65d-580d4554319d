<?php
/**
 * Created by <PERSON><PERSON><PERSON>torm.
 * User: didi
 * Date: 2019/12/17
 * Time: 上午11:35
 * <AUTHOR> <<EMAIL>>
 */

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo;

use BizLib\Utils\Language;
use BizLib\Utils\ApolloHelper;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\NumberHelper;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\estimatePriceV2\KFlowerActivityLogic;
use PreSale\Logics\estimatePriceV2\ShortBookLogic;

/**
 * Class KFlowerPriceDesc
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo
 */
class KFlowerPriceDesc
{
    /**
     * wiki:http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=368046370
     */
    const TYPE_DISCOUNT_TAG_COLOR  = 1; //服务端下发色值，端实现背景
    const TYPE_DISCOUNT_TAG_BG_IMG = 2; //服务端下发背景图
    const TYPE_DISCOUNT_TAG_UNION  = 3; //联合样式，左右复合标签

    /**
     * @var array
     */
    private $_aInfo;

    private $_fDiscountTotal;

    /**
     * CarpoolPriceDesc constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
        $sCurrency    = $this->_aInfo['bill_info']['currency'] ?? '';
        list($sSymbol, $sUnit)  = \BizLib\Utils\Currency::getSymbolUnit($sCurrency, $this->_aInfo['order_info']);
        $this->_sCurrencySymbol = $sSymbol;
        $this->_sCurrencyUnit   = $sUnit;
        $this->_sCarLevel       = $this->_aInfo['order_info']['require_level'];
        $this->_aConfig         = NuwaConfig::text('config_text', 'getEstimateFeeInfo');
        $this->_aEstimateText   = NuwaConfig::text('config_text', 'mOrderEstimateNew');
    }

    /**
     * @param array $aResponse response
     * @return array|mixed
     */
    public function build($aResponse) {
        $this->_getDiscount();
        $aResponse['coupon_desc']     = $this->_formatCouponDesc();
        $aResponse['price_desc_tags'] = $this->_formatPriceDescTag();
        $aResponse['discount_tags']   = $this->_formatDiscountTags($this->_aInfo['activity_info'][0]['discount_desc']);
        $aResponse['discount_desc']   = $this->_formatDiscountDesc();

        $aResponse = $this->_getShortBookInfo($aResponse);

        return $aResponse;
    }

    /**
     * 获取折扣价
     *
     * @return void
     */
    private function _getDiscount() {
        $aDiscountInfo  = $this->_aInfo['activity_info'][0]['discount_desc'] ?? [];
        $fDiscountTotal = 0;
        foreach ($aDiscountInfo as $aDiscountItem) {
            if (($fDiscount = abs($aDiscountItem['amount'] ?? 0)) > 0 && 'infofee' != $aDiscountItem['type']) {
                $fDiscountTotal += $fDiscount;
            }
        }

        $this->_fDiscountTotal = $fDiscountTotal;
    }

    /**
     * @return array
     */
    private function _formatCouponDesc() {
        $fDiscountTotal = $this->_fDiscountTotal;

        $aDiscountDesc = [];
        if ($fDiscountTotal > 0) {
            $aDiscountDesc = [
                'text'         => 0.0,
                'text_color'   => $this->_aConfig['KFlower_coupon_color'],
                'text_bgColor' => $this->_aConfig['KFlower_coupon_bgcolor'],
            ];

            $aDiscountDesc['text'] = Language::replaceTag(
                $this->_aConfig['KFlower_coupon_info'],
                [
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'fee'             => NumberHelper::numberFormatDisplay($fDiscountTotal),
                    'currency_unit'   => $this->_sCurrencyUnit,
                ]
            );
        }

        return $aDiscountDesc;
    }

    /**
     * @return array
     */
    private function _formatPriceDescTag() {
        $aPriceDescTags = [];
        $aDiscountInfo  = $this->_aInfo['activity_info'][0]['discount_desc'] ?? [];
        foreach ($aDiscountInfo as $aItem) {
            $aTags = [
                'text'         => 0.0,
                'text_color'   => $this->_aConfig['KFlower_coupon_tag_color'],
                'text_bgColor' => $this->_aConfig['KFlower_coupon_tag_bgcolor'],
            ];
            if (!empty($aItem['type']) && !empty($aItem['amount']) && 'coupon' === $aItem['type'] && $aItem['amount'] > 0) {
                $aTags['text']    = $this->_aConfig['KFlower_coupon_title'];
                $aPriceDescTags[] = $aTags;
            }

            if (!empty($aItem['type']) && !empty($aItem['amount']) && 'reward' === $aItem['type'] && $aItem['amount'] > 0) {
                $aTags['text']    = $this->_aConfig['KFlower_rewards_title'];
                $aPriceDescTags[] = $aTags;
            }
        }

        return $aPriceDescTags;
    }

    /**
     * @param array $aDiscountInfo $aDiscountInfo
     *
     * @return array
     */
    private function _formatDiscountTags($aDiscountInfo) {
        $aDiscountTags = [];

        $aConfig        = ApolloHelper::getConfigContent('king_flower_config', 'bwh_price_dec_img');
        $aStyleConfig   = $aConfig['discount_tag_style'];
        $aContentConfig = $aConfig['discount_tag_bubble_config'];
        $aCouponConfig  = $aConfig['special_coupon_style'];
        if (empty($aStyleConfig) || empty($aContentConfig) || empty($aDiscountInfo)) {
            return $aDiscountTags;
        }

        $aDiscountInfo = $this->_sortDiscountTags($aDiscountInfo);
        foreach ($aDiscountInfo as $aItem) {
            switch ($aItem['type']) {
                case 'coupon':
                    $aDiscountTag = $aStyleConfig['coupon'];

                    $aDiscountTag['type'] = self::TYPE_DISCOUNT_TAG_COLOR;

                    $bAllow  = false;
                    $oToggle = (new Apollo())->featureToggle(
                        'kflower_call_return_coupon',
                        [
                            'individual_id' => $this->_aInfo['passenger_info']['pid'],
                            'pid'           => $this->_aInfo['passenger_info']['pid'],
                            'phone'         => $this->_aInfo['passenger_info']['phone'],
                            'city'          => $this->_aInfo['order_info']['area'],
                        ]
                    );

                    if ($oToggle->allow() && 'control_group' == $oToggle->getGroupName()) {
                        $bAllow = true;
                    }

                    if (isset($aItem['coupon_type']) && 'call_return' == $aItem['coupon_type']) {
                        if (KFlowerActivityLogic::getInstance()->isLuckyBoy()) {
                            $aDiscountTag['text'] = Language::replaceTag(
                                $aCouponConfig['call_return_lucky']['estimate_text'],
                                [
                                    'currency_symbol' => $this->_sCurrencySymbol,
                                    'fee'             => $aItem['amount'],
                                    'currency_unit'   => $this->_sCurrencyUnit,
                                ]
                            );
                        } elseif ($bAllow) {
                            $aDiscountTag['text'] = Language::replaceTag(
                                $aCouponConfig['call_return']['estimate_text'],
                                [
                                    'currency_symbol' => $this->_sCurrencySymbol,
                                    'fee'             => $aItem['amount'],
                                    'currency_unit'   => $this->_sCurrencyUnit,
                                ]
                            );
                        } else {
                            $aDiscountTag['text'] = Language::replaceTag(
                                $aCouponConfig['call_return_b']['estimate_text'],
                                [
                                    'currency_symbol' => $this->_sCurrencySymbol,
                                    'fee'             => $aItem['amount'],
                                    'currency_unit'   => $this->_sCurrencyUnit,
                                ]
                            );
                        }
                    } else {
                        if (empty($this->_aInfo['passenger_info']['pid']) || \PreSale\Logics\passenger\NewUserLogic::isNewUser($this->_aInfo['passenger_info']['pid'])) {
                            $aDiscountTag['text'] = Language::replaceTag(
                                $aCouponConfig['new_user']['estimate_text'],
                                [
                                    'currency_symbol' => $this->_sCurrencySymbol,
                                    'fee'             => $aItem['amount'],
                                    'currency_unit'   => $this->_sCurrencyUnit,
                                ]
                            );
                        } else {
                            $oToggle = (new Apollo())->featureToggle(
                                'kf_coupon_transform',
                                [
                                    'key'      => $this->_aInfo['passenger_info']['pid'],
                                    'phone'    => $this->_aInfo['passenger_info']['phone'],
                                    'city'     => $this->_aInfo['order_info']['area'],
                                    'batch_id' => $this->_aInfo['activity_info'][0]['coupon_info']['default_coupon']['batchid'] ?? '',
                                ]
                            );

                            $sCouponTextType = $oToggle->getParameter('coupon_type', 'default');

                            if (!empty($aCouponConfig[$sCouponTextType])) {
                                $aDiscountTag['text'] = Language::replaceTag(
                                    $aCouponConfig[$sCouponTextType]['estimate_text'],
                                    [
                                        'currency_symbol' => $this->_sCurrencySymbol,
                                        'fee'             => $aItem['amount'],
                                        'currency_unit'   => $this->_sCurrencyUnit,
                                    ]
                                );
                            } else {
                                $aDiscountTag['text'] = Language::replaceTag(
                                    $aContentConfig['coupon']['text'],
                                    [
                                        'currency_symbol' => $this->_sCurrencySymbol,
                                        'fee'             => $aItem['amount'],
                                        'currency_unit'   => $this->_sCurrencyUnit,
                                    ]
                                );
                            }
                        }
                    }

                    $aDiscountTags[] = $aDiscountTag;
                    break;
                case 'reward':
                    $aDiscountTag = $aStyleConfig['user_reward'];

                    $aDiscountTag['type'] = self::TYPE_DISCOUNT_TAG_COLOR;
                    $aDiscountTag['text'] = Language::replaceTag(
                        $aContentConfig['user_reward']['text'],
                        [
                            'currency_symbol' => $this->_sCurrencySymbol,
                            'fee'             => $aItem['amount'],
                            'currency_unit'   => $this->_sCurrencyUnit,
                        ]
                    );

                    $aDiscountTags[] = $aDiscountTag;
                    break;
                case 'short_wait':
                    $aDiscountTag = $aStyleConfig['sps_short_waiting_discount'];

                    $aDiscountTag['type'] = self::TYPE_DISCOUNT_TAG_COLOR;
                    $aDiscountTag['text'] = Language::replaceTag(
                        $aContentConfig['sps_short_waiting_discount']['text'],
                        [
                            'currency_symbol' => $this->_sCurrencySymbol,
                            'fee'             => $aItem['amount'],
                            'currency_unit'   => $this->_sCurrencyUnit,
                        ]
                    );

                    $aDiscountTags[] = $aDiscountTag;
                    break;
                case 'kcard':
                    $aKCardConfig = $aStyleConfig['saving_card'];
                    $aDiscountTag = [
                        'type'         => self::TYPE_DISCOUNT_TAG_UNION,
                        'border_color' => $aKCardConfig['border_color'],
                        'sub_tags'     => [],
                    ];
                    unset($aKCardConfig['border_color']);

                    $aDiscountTag['sub_tags'][] = $aKCardConfig;

                    $aSubTag         = $aContentConfig['saving_card'];
                    $aSubTag['text'] = Language::replaceTag(
                        $aSubTag['text'],
                        [
                            'currency_symbol' => $this->_sCurrencySymbol,
                            'fee'             => $aItem['amount'],
                            'currency_unit'   => $this->_sCurrencyUnit,
                        ]
                    );

                    $aDiscountTag['sub_tags'][] = $aSubTag;

                    $aDiscountTags[] = $aDiscountTag;
                    break;
                case 'special_line':
                    $aSpecialRouteConfig = $aStyleConfig['surprise_special_fee'];

                    $aDiscountTag = [
                        'type'         => self::TYPE_DISCOUNT_TAG_UNION,
                        'border_color' => $aSpecialRouteConfig['border_color'],
                        'sub_tags'     => [],
                    ];
                    unset($aSpecialRouteConfig['border_color']);

                    $aDiscountTag['sub_tags'][] = $aSpecialRouteConfig;

                    $aSubTag         = $aContentConfig['surprise_special_fee'];
                    $aSubTag['text'] = Language::replaceTag(
                        $aSubTag['text'],
                        [
                            'currency_symbol' => $this->_sCurrencySymbol,
                            'fee'             => $aItem['amount'],
                            'currency_unit'   => $this->_sCurrencyUnit,
                        ]
                    );

                    $aDiscountTag['sub_tags'][] = $aSubTag;

                    $aDiscountTags[] = $aDiscountTag;
                    break;
                default:
                    break;
            }
        }

        return $aDiscountTags;
    }

    /**
     * 优惠标签展示排序
     *
     * @param array $aDiscountInfo $aDiscountInfo
     *
     * @return array
     */
    private function _sortDiscountTags($aDiscountInfo) {
        $aSortPriority = ['special_line', 'coupon', 'reward', 'short_wait', 'kcard',]; //标签展示优先级

        $aSortedDiscountInfo = [];

        foreach ($aSortPriority as $sType) {
            foreach ($aDiscountInfo as $iIndex => $aItem) {
                if (empty($aItem['type']) || empty($aItem['amount'])) {
                    unset($aDiscountInfo[$iIndex]);
                    continue;
                }

                if ($sType == $aItem['type']) {
                    $aSortedDiscountInfo[] = $aItem;
                    unset($aDiscountInfo[$iIndex]);
                    continue;
                }
            }
        }

        return $aSortedDiscountInfo;
    }

    /**
     *
     * @return string
     */
    private function _formatDiscountDesc() {
        $sDiscountDesc = '';
        if ($this->_fDiscountTotal > 0 && isset($this->_aInfo['activity_info'][0]['estimate_fee'])) {
            $fBeforeDiscountFee = $this->_aInfo['activity_info'][0]['estimate_fee'] + $this->_fDiscountTotal;

            $sDiscountDesc = Language::replaceTag(
                $this->_aConfig['KFlower_estimate_discount_desc'],
                [
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'total_fee'       => NumberHelper::numberFormatDisplay($fBeforeDiscountFee),
                    'discount_fee'    => NumberHelper::numberFormatDisplay($this->_fDiscountTotal),
                    'currency_unit'   => $this->_sCurrencyUnit,
                ]
            );
        }

        return $sDiscountDesc;
    }

    /**
     * @param array $aResponse $aResponse
     *
     * @return mixed
     */
    private function _getShortBookInfo($aResponse) {
        if (!ShortBookLogic::getInstance()->hasShortBook()) {
            return $aResponse;
        }

        $aDiscountDesc = ShortBookLogic::getInstance()->getShortBookInfo()['discount_desc'];

        $aResponse['element_replace']['normal']['discount_tags']     = $aResponse['discount_tags'];
        $aResponse['element_replace']['short_book']['discount_tags'] = $this->_formatDiscountTags($aDiscountDesc);

        return $aResponse;
    }
}
