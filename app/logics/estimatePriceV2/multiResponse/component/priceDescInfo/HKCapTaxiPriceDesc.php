<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo;

use PreSale\Logics\estimatePriceV2\multiResponse\Util;
use BizLib\Utils\Language;

/**
 * hk cap taxi
 */
class HKCapTaxiPriceDesc
{
    /**
     * @var array
     */
    protected $_aInfo;

    /**
     * @var array
     */
    protected $_aConfig;

    protected $_aCouponInfo = null;
    protected $_aBonusInfo  = null;

    /**
     * @param array $aInfo $aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo   = $aInfo;
        $this->_aConfig = Language::getDecodedTextFromDcmp('hk_cap_taxi-price_info_desc');
    }

    /**
     * @param array $aResponse $aResponse
     * @return mixed
     */
    public function build($aResponse) {
        $this->fillDesc();
        if (Util::isEstimateFormDoubleLineStyle($this->_aInfo)) {
            $aResponse['price_info_desc'] = $this->doubleLineStyle();
        } else {
            $aResponse['price_info_desc'] = $this->normal();
        }

        return $aResponse;
    }

    /**
     * @return array
     */
    protected function normal() {
        $ret = [];
        if (!empty($this->_aCouponInfo)) {
            array_push($ret, ['content' => Language::replaceTag($this->_aConfig['normal']['coupon_desc'],['num' => $this->_aCouponInfo['amount']]), 'left_icon' => $this->_aConfig['normal']['coupon_icon']]);
        }

        if (!empty($this->_aBonusInfo)) {
            array_push($ret,['content' => Language::replaceTag($this->_aConfig['normal']['bonus_desc'],['num' => $this->_aBonusInfo['amount']]), 'left_icon' => $this->_aConfig['normal']['bonus_icon']]);
        }

        return $ret;
    }

    /**
     * @return array
     */
    protected function doubleLineStyle() {
        $ret = [];
        if (!empty($this->_aCouponInfo)) {
            array_push($ret,['content' => Language::replaceTag($this->_aConfig['double']['coupon_desc'],['num' => $this->_aCouponInfo['amount']]), 'left_icon' => $this->_aConfig['double']['coupon_icon']]);
        }

        if (!empty($this->_aBonusInfo)) {
            array_push($ret,['content' => Language::replaceTag($this->_aConfig['double']['bonus_desc'],['num' => $this->_aBonusInfo['amount']]), 'left_icon' => $this->_aConfig['double']['bonus_icon']]);
        }

        return $ret;
    }

    /**
     * @return void
     */
    protected function fillDesc() {
        $aDescountDesc = $this->_aInfo['activity_info'][0]['discount_desc'] ?? [];
        if (empty($aDescountDesc)) {
            return;
        }

        foreach ($aDescountDesc as $aItem) {
            if (!empty($aItem['type']) && !empty($aItem['amount']) && 'hkbonus' === $aItem['type'] && $aItem['amount'] > 0) {
                $this->_aBonusInfo = [
                    'amount' => $aItem['amount'],
                ];
            }
        }

        $aCouponInfo      = $this->_aInfo['activity_info'][0]['coupon_info'];
        $iFinalCouponType = $this->_aInfo['activity_info'][0]['final_coupon_type'];
        if (1 == $iFinalCouponType && !empty($aCouponInfo['default_coupon'])) {
            $this->_aCouponInfo = [
                'amount' => $aCouponInfo['default_coupon']['amount'] / 100.0,
            ];
        } elseif (2 == $iFinalCouponType && !empty($aCouponInfo['activity_coupon'])) {
            $this->_aCouponInfo = [
                'amount' => $aCouponInfo['activity_coupon']['money'],
            ];
        }

    }
}
