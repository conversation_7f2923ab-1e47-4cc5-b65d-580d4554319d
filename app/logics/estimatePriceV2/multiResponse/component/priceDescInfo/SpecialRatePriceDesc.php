<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo;

use BizLib\Constants;
use PreSale\Logics\commonAbility\SpringRedPacketFormatter;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;

/**
 * 乘客端6.0 特惠快车 price_info_desc 文案处理
 * Class NormalPriceDescV2
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo
 */
class SpecialRatePriceDesc
{

    private $_aInfo;

    private $_oPriceItemFormatter;

    private $_aIncPriceItem = [];

    private $_aDecPriceItem = [];

    private $_aIntroPriceItem = [];

    private $_aExtraPriceItem = [];

    //内循环账户返利
    private $_aRevolvingAccountRebatePriceItem;
    //内循环账户折扣
    private $_aRevolvingAccountDiscountPriceItem;

    private $_aMemberDynamicProtect = [];

    private $_aBillInfo = [];

    /**
     * @var array 自由宝展示内容
     */
    private $_aSpecZiYoubao = [];

    private $_oSpringRedPacketFormatter;

    /**
     * NormalPriceDescV2 constructor.
     * @param array $aInfo $aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
        $this->_oPriceItemFormatter       = new PriceItemFormatter($aInfo);
        $this->_oSpringRedPacketFormatter = new SpringRedPacketFormatter($aInfo);
        $sCarLevel        = $aInfo['order_info']['require_level'];
        $this->_aBillInfo = $aInfo['bill_info']['bills'][$sCarLevel] ?? [];
    }

    /**
     * @param array $aResponse $aResponse
     * @return mixed
     */
    public function build($aResponse) {
        //获取溢价保护
        $this->_setDynamicMemberProtectMsg();
        //优惠券、存量福利金
        $this->_fillDecPriceItem();

        // 加价项
        $this->_fillIncPriceItem();

        // 额外福利
        $this->_fillExtraPriceItem();

        // 内循环账户返利
        $this->_fillRevolvingAccountRebatePriceItem();
        // 内循环账户折扣
        $this->_fillRevolvingAccountDiscountPriceItem();

        //过滤掉为空的
        $this->_filterPriceItem();

        //build price_info_desc
        $aResponse = $this->_format($aResponse);

        return $aResponse;
    }

    /**
     *  返利信息
     *  Item结构为 priceDesc priceIcon amount
     *  @return void
     */
    private function _fillRevolvingAccountRebatePriceItem() {
        //内循环账户返利信息
        $this->_aRevolvingAccountRebatePriceItem[] = $this->_oPriceItemFormatter->getRevolvingAccountRebateLite();
    }
    /**
     *  折扣信息
     *  Item结构为 priceDesc priceIcon amount
     *  @return void
     */
    private function _fillRevolvingAccountDiscountPriceItem() {
        //内循环账户折扣信息
        $this->_aRevolvingAccountDiscountPriceItem[] = $this->_oPriceItemFormatter->getRevolvingAccountDiscountLite();
    }

    /**
     * @desc 加价项
     * @return void
     */
    private function _fillIncPriceItem() {
        //春节红包
        list($sRedPacketDesc, $fRedPacketValue) = $this->_oSpringRedPacketFormatter->getRedPacketInfoLite();
        if (!empty($sRedPacketDesc)) {
            $this->_aIncPriceItem[] = [$sRedPacketDesc,'', $fRedPacketValue];
        }
        list($sCrossCityDesc, $sCrossCityIcon, $fCrossCityValue) = $this->_oPriceItemFormatter->getCrossCityFeeLite();
        if (!empty($sCrossCityDesc)) {
            $this->_aIncPriceItem[] = [$sCrossCityDesc, $sCrossCityIcon, $fCrossCityValue];
        }
    }

    /**
     * 特惠快车优惠券、存量福利金
     *
     * @return void
     */
    private function _fillDecPriceItem() {
        //特价出租车、月卡、学生券、智慧套餐券 [逻辑互斥]
        $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getCouponDescLite([]);

        //打车金
        $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getRewardsDescLite();

        // 单单省钱卡
        $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getEconomicalCardRightDescForV2();

        // 自由宝
        $this->_aSpecZiYoubao = $this-> _oPriceItemFormatter->getSpecZiYoubaoPriceInfoDesc();

        //账单类优惠费用项
        $aDiscounts = $this->_aBillInfo['discounts_bill']['normal']['discounts'] ?? [];
        if (!empty($aDiscounts)) {
            foreach ($aDiscounts as $aDiscount) {
                if (!empty($aDiscount) && isset($aDiscount['fee_name'])
                    && isset($aDiscount['fee_amount']) && $aDiscount['fee_amount'] < 0
                ) {
                    $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getPlutusDiscountDesc($aDiscount);
                }
            }
        }
    }

    /**
     * 顺序：福利金（支付后返回）
     *
     * @return void
     */
    private function _fillExtraPriceItem() {
        $this->_aExtraPriceItem[] = $this->_oPriceItemFormatter->getFastCarSpecialRateWelfareLite();
    }

    /**
     * 顺序：特价车打折信息
     * @return void
     */
    // 注释-待删
//    private function _fillIntroPriceItem() {
//        $this->_aIntroPriceItem[] = $this->_oPriceItemFormatter->getSpecialRateDiscountLite();
//    }

    /**
     *  获取溢价保护文案
     *  @return void
     */
    private function _setDynamicMemberProtectMsg() {
        $aOrderInfo = $this->_oPriceItemFormatter->getOrderInfo();
        if (in_array($aOrderInfo['product_id'], [Constants\OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR, Constants\OrderSystem::PRODUCT_ID_DEFAULT])
        ) {
            $this->_aMemberDynamicProtect = $this->_oPriceItemFormatter->getMemberDynamicProtectInfo();
        }
    }


    /**
     * @param array $aResponse $aResponse
     * @return mixed
     */
    private function _format($aResponse) {
        $aPriceDesc    = $aPriceDescIcon = $aPriceDescGif = $aPriceDescAnim = $aPriceAmount = $aPriceType = [];
        $aPreferential = $this->_oPriceItemFormatter->getPreferentialIcon();
        if ($this->_isMemberDynamicProtect()) {
            if (count($this->_aIncPriceItem) > 0) {
                //加价项一
                $aPriceDesc[]     = $this->_aIncPriceItem[0][0];
                $aPriceDescIcon[] = $this->_aIncPriceItem[0][1];
            }

            if (count($this->_aDecPriceItem) > 0) { // 有其他优惠, 其他优惠合并展示
                //溢价保护 + 其他优惠
                if (!empty($this->_aSpecZiYoubao[2])&&$this->_aSpecZiYoubao[2] > 0) {
                    $this->_specZiYoubao($aPriceDesc, $aPriceDescIcon, $this->_aSpecZiYoubao);
                } else {
                    $fAmount          = $this->_calcDecreaseAmount();
                    $aPriceItem       = $this->_oPriceItemFormatter->getMemberDynamicProtectInfo($fAmount);
                    $aPriceDesc[]     = $aPriceItem[0];
                    $aPriceDescIcon[] = $aPriceItem[1];
                    if (!empty($aPreferential)) {
                        $iIndex = count($aPriceDesc) - 1;
                        $aPriceDescGif[$iIndex]  = $aPreferential['left_gif'];
                        $aPriceDescAnim[$iIndex] = $aPreferential['show_anim'];
                    }
                }
            } else {
                //仅溢价保护
                $aPriceDesc[]     = $this->_aMemberDynamicProtect[0];
                $aPriceDescIcon[] = $this->_aMemberDynamicProtect[1];
            }
        } else {
            if (count($this->_aIncPriceItem) < 2) {
                if (1 == count($this->_aIncPriceItem)) { // 有加价项
                    //加价项一
                    $aPriceDesc[]     = $this->_aIncPriceItem[0][0];
                    $aPriceDescIcon[] = $this->_aIncPriceItem[0][1];
                }

                if (!empty($this->_aSpecZiYoubao[2]) && $this->_aSpecZiYoubao[2] > 0) {
                    $this->_specZiYoubao($aPriceDesc, $aPriceDescIcon, $this->_aSpecZiYoubao);
                } elseif (1 == count($this->_aDecPriceItem)) { // 只有一个优惠时, 不论加价项有一个还是没有, 都可展示
                    $aPriceDesc[]     = $this->_aDecPriceItem[0][0];
                    $aPriceDescIcon[] = $this->_aDecPriceItem[0][1];
                    $iDecIndex        = count($aPriceDesc) - 1;
                    $aPriceAmount[$iDecIndex] = $this->_aDecPriceItem[0][2] ?? 0;
                    $aPriceType[$iDecIndex]   = $this->_aDecPriceItem[0][3] ?? $this->_oPriceItemFormatter->getDefaultPriceType();
                } elseif (count($this->_aDecPriceItem) > 1) { // 多个优惠时
                    $fAmount          = $this->_calcDecreaseAmount();
                    $aPriceDesc[]     = $this->_oPriceItemFormatter->getDecreaseAmountDesc($fAmount);
                    $aPriceDescIcon[] = '';
                    $iDecIndex        = count($aPriceDesc) - 1;
                    $aPriceAmount[$iDecIndex] = $fAmount;
                    $aPriceType[$iDecIndex]   = $this->_oPriceItemFormatter->getSumDecreasePriceType();
                }

                if (count($this->_aDecPriceItem) >= 1 && !empty($aPreferential)) {
                    $iIndex = count($aPriceDesc) - 1;
                    $aPriceDescGif[$iIndex]  = $aPreferential['left_gif'];
                    $aPriceDescAnim[$iIndex] = $aPreferential['show_anim'];
                }
            } else { // 加价项两个或更多, 不显示优惠, 显示前面两个加价项
                //加价项一
                $aPriceDesc[]     = $this->_aIncPriceItem[0][0];
                $aPriceDescIcon[] = $this->_aIncPriceItem[0][1];
                //加价项二
                $aPriceDesc[]     = $this->_aIncPriceItem[1][0];
                $aPriceDescIcon[] = $this->_aIncPriceItem[1][1];
            }
        }

        //加价项 + 减价项 < 2 & 说明性存在
        if (count($aPriceDesc) < 2 && count($aPriceDescIcon) < 2 && count($this->_aIntroPriceItem) > 0) {
            $aPriceDesc[]     = $this->_aIntroPriceItem[0][0];
            $aPriceDescIcon[] = $this->_aIntroPriceItem[0][1];
        }

        //加价项 + 减价项 < 2 & 额外福利
        if (count($aPriceDesc) < 2 && count($aPriceDescIcon) < 2 && count($this->_aExtraPriceItem) > 0) {
            $aPriceDesc[]     = $this->_aExtraPriceItem[0][0];
            $aPriceDescIcon[] = $this->_aExtraPriceItem[0][1];
        }

        //目前（2022.10）内循环账户返利的优先级是最低的，故放到最后处理
        if (count($aPriceDesc) < 2 && !empty($this->_aRevolvingAccountRebatePriceItem)&& !empty($this->_aRevolvingAccountRebatePriceItem[0])) {
            $aPriceDesc[] = $this->_aRevolvingAccountRebatePriceItem[0][0];
            $iDecIndex = count($aPriceDesc) - 1;
            $aPriceAmount[$iDecIndex] = $this->_aRevolvingAccountRebatePriceItem[0][2];
            $aPriceType[$iDecIndex]   = $this->_aRevolvingAccountRebatePriceItem[0][3];
        }
        if (count($aPriceDesc) < 2 && !empty($this->_aRevolvingAccountDiscountPriceItem)&& !empty($this->_aRevolvingAccountDiscountPriceItem[0])) {
            $aPriceDesc[] = $this->_aRevolvingAccountDiscountPriceItem[0][0];
            $iDecIndex = count($aPriceDesc) - 1;
            $aPriceAmount[$iDecIndex] = $this->_aRevolvingAccountDiscountPriceItem[0][2];
            $aPriceType[$iDecIndex]   = $this->_aRevolvingAccountDiscountPriceItem[0][3];
        }

        Common::setPriceDescGif($aPriceDescGif);
        Common::setPriceDescAnim($aPriceDescAnim);
        $aResponse['price_info_desc'] = Common::formatPriceInfoV2($aPriceDesc,$aPriceDescIcon,[], $this->_aInfo, $aPriceAmount, $aPriceType);

        return $aResponse;
    }


    /**
     *  @return void
     */
    private function _filterPriceItem() {
        foreach ($this->_aDecPriceItem as $idx => $aPriceItem) {
            if (!self::_checkValid($aPriceItem)) {
                unset($this->_aDecPriceItem[$idx]);
            }
        }

        if (count($this->_aDecPriceItem) > 0) {
            $this->_aDecPriceItem = array_values($this->_aDecPriceItem);
        }

        foreach ($this->_aIncPriceItem as $idx => $aPriceItem) {
            if (!self::_checkValid($aPriceItem)) {
                unset($this->_aIncPriceItem[$idx]);
            }
        }

        if (count($this->_aIncPriceItem) > 0) {
            $this->_aIncPriceItem = array_values($this->_aIncPriceItem);
        }

        foreach ($this->_aIntroPriceItem as $idx => $aPriceItem) {
            if (!self::_checkValid($aPriceItem)) {
                unset($this->_aIntroPriceItem[$idx]);
            }
        }

        if (count($this->_aIntroPriceItem) > 0) {
            $this->_aIntroPriceItem = array_values($this->_aIntroPriceItem);
        }

        foreach ($this->_aExtraPriceItem as $idx => $aPriceItem) {
            if (!self::_checkValid($aPriceItem)) {
                unset($this->_aExtraPriceItem[$idx]);
            }
        }

        if (count($this->_aExtraPriceItem) > 0) {
            $this->_aExtraPriceItem = array_values($this->_aExtraPriceItem);
        }
    }


    /**
     * 计算减价 总抵扣金额
     * @return int|string
     */
    private function _calcDecreaseAmount() {
        $fAmount = 0;
        foreach ($this->_aDecPriceItem as $item) {
            if (is_numeric($item[2]) && $item[2] > 0) {
                $fAmount += $item[2];
            }
        }

        return $fAmount;
    }


    /**
     * 检查PriceItem的格式
     * @param array $aPriceItem $aPriceItem
     * @return bool
     */
    private static function _checkValid($aPriceItem) {
        if (2 == count($aPriceItem) && !empty($aPriceItem[0])) {
            return true;
        } elseif (3 == count($aPriceItem) && !empty($aPriceItem[0]) && $aPriceItem[2] > 0) {
            return true;
        } elseif (4 == count($aPriceItem) && !empty($aPriceItem[0])) {
            return true;
        } else {
            return false;
        }
    }


    /**
     * 是否存在溢价保护
     * @return bool
     */
    private function _isMemberDynamicProtect() {
        if (!empty($this->_aMemberDynamicProtect[0]) && $this->_aMemberDynamicProtect[2] > 0) {
            return true;
        }

        return false;
    }

    /**
     * @param array $aPriceDesc        $aPriceDesc,
     * @param array $aPriceDescIcon    $aPriceDescIcon,
     * @param array $aSpecZiYoubaoDesc $aSpecZiYoubaoDesc
     * @return void
     */
    private function _specZiYoubao(&$aPriceDesc, &$aPriceDescIcon, $aSpecZiYoubaoDesc) {

        // 低版本
        // 由于主端上实现问题(会对图标进行缩放), 低版本只能使用两段内容, 拼出 自由宝 的展示
        // 小程序没有问题
        if (MainDataRepo::isNativeClient() && version_compare(MainDataRepo::getAppVersion(), '6.3.2') < 0) {
            $aPriceDescIcon[] = ''; // 占位
            $aPriceDesc[]     = $aSpecZiYoubaoDesc[4] ?? '';

            $aPriceDescIcon[] = ''; // 占位
        } else {
            $aPriceDescIcon[] = $aSpecZiYoubaoDesc[1] ?? '';
        }

        $aPriceDesc[] = $aSpecZiYoubaoDesc[0] ?? '';
    }
}
