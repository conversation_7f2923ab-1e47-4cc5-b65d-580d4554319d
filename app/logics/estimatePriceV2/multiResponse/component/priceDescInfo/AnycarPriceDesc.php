<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo;

use BizLib\Constants\OrderSystem;
use PreSale\Logics\order\AnyCarOrderLogic;
use BizLib\Utils\MemberVersion;

/**
 * Class AnycarPriceDesc
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo
 */
class AnycarPriceDesc
{

    /**
     * @var array
     */
    private $_aInfo;

    /**
     * AnycarPriceDesc constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
    }

    /**
     * @param array $aResponse response
     * @return mixed
     */
    public function build($aResponse) {

        $aPriceDesc = $aPriceDescIcon = [];
        $aMultiInfo = $this->_aInfo['bill_info']['multi_info'];
        $sCarlevel  = $this->_aInfo['order_info']['require_level'];
        $aBillInfo  = $this->_aInfo['bill_info']['bills'][$sCarlevel];

        $sCurrency = $aInfo['bill_info']['currency'] ?? '';
        list($sSymbol, $sUnit) = \BizLib\Utils\Currency::getSymbolUnit($sCurrency, $aInfo['order_info']);
        $privilegeDesc         = [];

        // anycar
        if (!empty($aMultiInfo)) {
            // 豪华车展示优惠券
            $sAnyCarType = AnyCarOrderLogic::getInstance()->getAnyCarType($aMultiInfo);
            if (OrderSystem::TYPE_ANYCAR_FIRST_CLASS == $sAnyCarType) {
                // 豪华车需要展示动调
                $sAnyCarDynamicInfo = $aBillInfo ?? [];
                $oFormatter         = new PriceItemFormatter($this->_aInfo);
                $sAnyCarDynamicDesc = $oFormatter->getDynamicDesc($sAnyCarDynamicInfo);
                if (!empty($sAnyCarDynamicDesc)) {
                    $aPriceDesc[] = $sAnyCarDynamicDesc;
                }

                list($sAnyCarCouponDesc, $privilegeDesc, $privilegeDescV2) = AnyCarOrderLogic::getInstance()->getCouponProtectFeeForDisplay($this->_aInfo['bill_info'], $sSymbol, $sUnit, $this->_aInfo['passenger_info']);
                if (!empty($sAnyCarCouponDesc)) {
                    $aPriceDesc[] = $sAnyCarCouponDesc;
                }
            } else {
                // anycar快车的价格区间，由于太长，没法放在fee_msg，只能放在price_desc.
                $sAnyCarPriceRangeDesc = AnyCarOrderLogic::getInstance()->getPriceRangeForDisplay($this->_aInfo['bill_info'], $sSymbol, $sUnit);
                if (!empty($sAnyCarPriceRangeDesc)) {
                    $aPriceDesc[] = $sAnyCarPriceRangeDesc;
                }
            }
        }

        $aResponse['price_desc']      = implode(',', $aPriceDesc);
        $aResponse['price_desc_icon'] = '';
        if (MemberVersion::isNewMember()) {
            if (empty($aResponse['extra_tag'])) {
                $aResponse['extra_tag'] = [$privilegeDescV2];
            } else {
                $aResponse['extra_tag'][] = $privilegeDescV2;
            }
        } else {
            $aResponse['privilege_desc'] = $privilegeDesc;
        }

        //build price_info_desc
        $aResponse = Common::formatPriceInfoDesc($aResponse, $aPriceDesc, $aPriceDescIcon, $this->_aInfo);

        return $aResponse;
    }
}
