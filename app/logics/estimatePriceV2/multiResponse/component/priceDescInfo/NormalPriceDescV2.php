<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo;

use BizLib\Config as NuwaConfig;
use BizLib\Constants;
use BizLib\Constants\OrderSystem;
use BizLib\Utils\Nuwa;
use Nuwa\ApolloSDK\Apollo as NuwaApollo;
use PreSale\Logics\commonAbility\SpringRedPacketFormatter;
use PreSale\Logics\estimatePriceV2\DecisionLogic;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;
use BizLib\Log as NuwaLog;

/**
 * 乘客端6.0 price_info_desc 文案处理
 * Class NormalPriceDescV2
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo
 */
class NormalPriceDescV2
{

    private $_aInfo;

    private $_oPriceItemFormatter;

    private $_aIncPriceItem = [];

    private $_aDecPriceItem = [];

    private $_aIntroPriceItem = [];
    //内循环账户折扣
    private $_aRevolvingAccountDiscountPriceItem=[];
    //内循环账户返利
    private $_aRevolvingAccountRebatePriceItem=[];

    private $_aMemberDynamicProtect = [];

    private $_bIsSpecialRate;

    private $_aBillInfo = [];

    private $_oSpringRedPacketFormatter;

    /**
     * NormalPriceDescV2 constructor.
     * @param array $aInfo $aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
        $this->_oPriceItemFormatter       = new PriceItemFormatter($aInfo);
        $this->_oSpringRedPacketFormatter = new SpringRedPacketFormatter($aInfo);
        $this->_bIsSpecialRate            = Util::isSpecialRate($aInfo);
        $sCarLevel        = $aInfo['order_info']['require_level'];
        $this->_aBillInfo = $aInfo['bill_info']['bills'][$sCarLevel] ?? [];
    }


    /**
     * @param array $aResponse $aResponse
     * @return mixed
     */
    public function build($aResponse) {
        //获取溢价保护
        $this->_setDynamicMemberProtectMsg();

        if (!$this->_isSpecialPriceFastCar()) {
            $this->_fillIncPriceItem();
            $this->_fillDecPriceItem();
            //内循环账户返利信息
            $this->_fillRevolvingAccountRebatePriceItem();
            //内循环账户折扣信息
            $this->_fillRevolvingAccountDiscountPriceItem();
        }

        $this->_fillIntroPriceItem();

        //过滤掉为空的
        $this->_filterPriceItem();

        //build price_info_desc
        $aResponse = $this->_format($aResponse);

        return $aResponse;
    }

    /**
     *  内循环账户折扣信息
     *  Item结构为 priceDesc priceIcon amount
     *  @return void
     */
    private function _fillRevolvingAccountDiscountPriceItem() {
        //内循环账户折扣信息
        $this->_aRevolvingAccountDiscountPriceItem[] = $this->_oPriceItemFormatter->getRevolvingAccountDiscountLite();
    }
    /**
     *  内循环账户返利信息
     *  Item结构为 priceDesc priceIcon amount
     *  @return void
     */
    private function _fillRevolvingAccountRebatePriceItem() {
        //内循环账户返利信息
        $this->_aRevolvingAccountRebatePriceItem[] = $this->_oPriceItemFormatter->getRevolvingAccountRebateLite();
    }

    /**
     *  顺序：多因素一口价、动调相关费用、特价出租车、月卡、学生券、智慧套餐券、打车金、畅行卡、限时特惠 特惠出租车愿减、
     *       市民卡、优惠共抵、单单省钱卡
     *  Item结构为 priceDesc priceIcon amount
     *  @return void
     */
    private function _fillDecPriceItem() {
        //多因素一口价
        $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getFixedPreferentialPriceDescLite([]);

        //快车价格打专车权益
        $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getPricePrivilegeDescLite();

        //非溢价保护，展示动调
        if (!$this->_isMemberDynamicProtect()) {
            //动调减项
            $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getDynamicPriceDescLite('dec',[],false);
        }

        //特价出租车、月卡、学生券、智慧套餐券 [逻辑互斥]
        $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getCouponDescLite([],'',false,$this->_bIsSpecialRate,[]);

        //打车金
        $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getRewardsDescLite();

        //香港打车金 DiDi Dollar
        $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getHKBonusDescLite();

        // 费用详情中 减价项展示
        $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getFeeDetailDec();

        //畅行卡
        $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getBackCardsDescLite();

        //限时特惠，特惠出租车、司机愿减
        $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getLimitTimeDescLite();

        //市民卡
        $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getCityCardsDescLite();

        //账单优惠
        $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getTalosDiscountFeeDescLite();

        // 单单折扣卡
        $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getEconomicalCardRightDescForV2();

        //账单类优惠费用项
        $aDiscounts = $this->_aBillInfo['discounts_bill']['normal']['discounts'] ?? [];
        if (!empty($aDiscounts)) {
            foreach ($aDiscounts as $aDiscount) {
                if (!empty($aDiscount) && isset($aDiscount['fee_name'])
                    && isset($aDiscount['fee_amount']) && $aDiscount['fee_amount'] < 0
                ) {
                    $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getPlutusDiscountDesc($aDiscount);
                }
            }
        }
        // 网开台第三方优惠
        $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getTripCloudDesc();
    }

    /**
     *  加项不需要展示总价格
     *  顺序：动调、春节红包、豪华车司服信息、定制化服务相关费、跨城费、estimate_fixed_fees、信息费、小费
     *  Item 结构为 priceDesc priceIcon
     *  @return void
     */
    private function _fillIncPriceItem() {

        //非溢价保护，展示动调
        if (!$this->_isMemberDynamicProtect()) {
            //动调增项
            $this->_aIncPriceItem[] = $this->_oPriceItemFormatter->getDynamicPriceDescLite('inc',[],false);
        }

        //春节红包
        list($sRedPacketDesc, $fRedPacketValue) = $this->_oSpringRedPacketFormatter->getRedPacketInfoLite();
        if (!empty($sRedPacketDesc)) {
            $this->_aIncPriceItem[] = [$sRedPacketDesc,'', $fRedPacketValue];
        }

        //豪华车司务员
        $this->_aIncPriceItem[] = $this->_oPriceItemFormatter->getLuxuryDesignedDriverInfoLite();

        //定制化服务相关费用
        $this->_aIncPriceItem[] = $this->_oPriceItemFormatter->getCustomizedServicePriceDescLite();

        //跨城费用
        $this->_aIncPriceItem[] = $this->_oPriceItemFormatter->getCrossCityFeeLite();

        //estimate_fixed_fees
        $this->_aIncPriceItem[] = $this->_oPriceItemFormatter->formatFeeStructLite();

        //信息费
        $this->_aIncPriceItem[] = $this->_oPriceItemFormatter->getInfoFeeDescLite();

        //小费
        $this->_aIncPriceItem[] = $this->_oPriceItemFormatter->getTipsDescLite();
    }


    /**
     * 顺序：特价车打折信息
     * @return void
     */
    private function _fillIntroPriceItem() {
        //判断支付方式,如果为线上支付,需要添加香港线上支付手续费策略透出
        $this->_aIntroPriceItem[] = $this->_oPriceItemFormatter->getHongKongTaxiCommissionSix();
    }

    /**
     *  获取溢价保护文案
     *  @return void
     */
    private function _setDynamicMemberProtectMsg() {
        $aOrderInfo = $this->_oPriceItemFormatter->getOrderInfo();
        if (in_array($aOrderInfo['product_id'], [Constants\OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR, Constants\OrderSystem::PRODUCT_ID_DEFAULT])
        ) {
            $this->_aMemberDynamicProtect = $this->_oPriceItemFormatter->getMemberDynamicProtectInfo();
        }
    }


    /**
     * @param array $aResponse $aResponse
     * @return mixed
     */
    private function _format($aResponse) {
        $aPriceDesc       = $aPriceDescIcon = $aPriceDescGif = $aPriceDescAnim = $aPriceAmount = $aPriceType = [];
        $aPreferential    = $this->_oPriceItemFormatter->getPreferentialIcon();
        $aActivityInfo    = $this->_aInfo['activity_info'][0];
        $aCouponInfo      = $aActivityInfo['coupon_info'];
        $sCouponCustomTag = $aCouponInfo['activity_coupon']['custom_tag'] ?? '';
        $bHKBonusFlag     = false;
        if ($this->_isMemberDynamicProtect()) {
            if (count($this->_aIncPriceItem) > 0) {
                //加价项一
                $aPriceDesc[]     = $this->_aIncPriceItem[0][0];
                $aPriceDescIcon[] = $this->_aIncPriceItem[0][1];
            }

            $aOrderInfo = $this->_oPriceItemFormatter->getOrderInfo();
            if (count($this->_aDecPriceItem) > 0) {
                //溢价保护 + 其他优惠
                $fAmount      = $this->_calcDecreaseAmount();
                $aPriceItem   = $this->_oPriceItemFormatter->getMemberDynamicProtectInfo($fAmount);
                $aPriceDesc[] = $aPriceItem[0];

                if ($this->_oPriceItemFormatter->isPaidMemberDpa()) {
                    $aPriceDescIcon[] = $this->_oPriceItemFormatter->getMemberDynamicProtectIcon();
                } else {
                    $aPriceDescIcon[] = $aPriceItem[1];
                }

                if (!empty($aPreferential)) {
                    $iIndex = count($aPriceDesc) - 1;
                    $aPriceDescGif[$iIndex]  = $aPreferential['left_gif'];
                    $aPriceDescAnim[$iIndex] = $aPreferential['show_anim'];
                }
            } else {
                //仅溢价保护
                $aPriceDesc[] = $this->_aMemberDynamicProtect[0];
                if ($this->_oPriceItemFormatter->isPaidMemberDpa()) {
                    $aPriceDescIcon[] = $this->_oPriceItemFormatter->getMemberDynamicProtectIcon();
                } else {
                    $aPriceDescIcon[] = $this->_aMemberDynamicProtect[1];
                }
            }
        } else {
            if (count($this->_aIncPriceItem) < 2) {
                if (1 == count($this->_aIncPriceItem)) {
                    //加价项一
                    $aPriceDesc[]     = $this->_aIncPriceItem[0][0];
                    $aPriceDescIcon[] = $this->_aIncPriceItem[0][1];
                }

                if (1 == count($this->_aDecPriceItem)) {
                    $aPriceDesc[]     = $this->_aDecPriceItem[0][0];
                    $aPriceDescIcon[] = $this->_aDecPriceItem[0][1];
                    $iDecIndex        = count($aPriceDesc) - 1;
                    $aPriceAmount[$iDecIndex] = $this->_aDecPriceItem[0][2] ?? 0;
                    $aPriceType[$iDecIndex]   = $this->_aDecPriceItem[0][3] ?? $this->_oPriceItemFormatter->getDefaultPriceType();
                } elseif (count($this->_aDecPriceItem) > 1) {
                    foreach ($this->_aDecPriceItem as $aPriceItem) {
                        if (PriceItemFormatter::PRICE_TYPE_DIDI_DOLLAR == $aPriceItem[3]) {
                            $bHKBonusFlag = true;
                        }
                    }

                    $fAmount = $this->_calcDecreaseAmount();
                    if (!$bHKBonusFlag) {
                        if (PriceItemFormatter::COUPON_TAG_PERORDERSALE == $sCouponCustomTag) {
                            // 多个减价项&单单省 --> 展示为 单单省·惠icon-x元
                            $aPriceDesc[]     = $this->_oPriceItemFormatter->getPriceDesc($fAmount);
                            $aPriceDescIcon[] = $this->_oPriceItemFormatter->getDanDanShengCombineIcon();
                        } else {
                            $aPriceDesc[]     = $this->_oPriceItemFormatter->getDecreaseAmountDesc($fAmount);
                            $aPriceDescIcon[] = '';
                        }

                        $iDecIndex = count($aPriceDesc) - 1;
                        $aPriceAmount[$iDecIndex] = $fAmount;
                        $aPriceType[$iDecIndex]   = $this->_oPriceItemFormatter->getSumDecreasePriceType();
                    } else {
                        // 香港端券和打车金都有时，不合并，分两行展示
                        foreach ($this->_aDecPriceItem as $aPriceItem) {
                            $aPriceDesc[]     = $aPriceItem[0];
                            $aPriceDescIcon[] = $aPriceItem[1];
                            $iDecIndex        = count($aPriceDesc) - 1;
                            $aPriceAmount[$iDecIndex] = $aPriceItem[2] ?? 0;
                            $aPriceType[$iDecIndex]   = $aPriceItem[3] ?? $this->_oPriceItemFormatter->getDefaultPriceType();
                        }
                    }
                }

                if (count($this->_aDecPriceItem) >= 1 && !empty($aPreferential)) {
                    $iIndex = count($aPriceDesc) - 1;
                    $aPriceDescGif[$iIndex]  = $aPreferential['left_gif'];
                    $aPriceDescAnim[$iIndex] = $aPreferential['show_anim'];
                }
            } else {
                //加价项一
                $aPriceDesc[]     = $this->_aIncPriceItem[0][0];
                $aPriceDescIcon[] = $this->_aIncPriceItem[0][1];

                //加价项二
                $aPriceDesc[]     = $this->_aIncPriceItem[1][0];
                $aPriceDescIcon[] = $this->_aIncPriceItem[1][1];
            }
        }

        //加价项 + 减价项 < 2 & 说明性存在
        if (count($aPriceDesc) < 2 && count($aPriceDescIcon) < 2 && count($this->_aIntroPriceItem) > 0) {
            $aPriceDesc[]     = $this->_aIntroPriceItem[0][0];
            $aPriceDescIcon[] = $this->_aIntroPriceItem[0][1];
        }
        // //内循环账户返利
        if (count($aPriceDesc) < 2 && !empty($this->_aRevolvingAccountRebatePriceItem)&& !empty($this->_aRevolvingAccountRebatePriceItem[0])) {
            $aPriceDesc[] = $this->_aRevolvingAccountRebatePriceItem[0][0];
            $iDecIndex = count($aPriceDesc) - 1;
            $aPriceAmount[$iDecIndex] = $this->_aRevolvingAccountRebatePriceItem[0][2];
            $aPriceType[$iDecIndex]   = $this->_aRevolvingAccountRebatePriceItem[0][3];
        }
        //内循环账户折扣
        if (count($aPriceDesc) < 2 && !empty($this->_aRevolvingAccountDiscountPriceItem)&& !empty($this->_aRevolvingAccountDiscountPriceItem[0])) {
            $aPriceDesc[] = $this->_aRevolvingAccountDiscountPriceItem[0][0];
            $iDecIndex = count($aPriceDesc) - 1;
            $aPriceAmount[$iDecIndex] = $this->_aRevolvingAccountDiscountPriceItem[0][2];
            $aPriceType[$iDecIndex]   = $this->_aRevolvingAccountDiscountPriceItem[0][3];
        }
        Common::setPriceDescGif($aPriceDescGif);
        Common::setPriceDescAnim($aPriceDescAnim);
        $aResponse['price_info_desc'] = Common::formatPriceInfoV2($aPriceDesc,$aPriceDescIcon,[], $this->_aInfo, $aPriceAmount, $aPriceType);

        return $aResponse;
    }


    /**
     *  @return void
     */
    private function _filterPriceItem() {
        foreach ($this->_aDecPriceItem as $idx => $aPriceItem) {
            if (!self::_checkValid($aPriceItem)) {
                unset($this->_aDecPriceItem[$idx]);
            }
        }

        if (count($this->_aDecPriceItem) > 0) {
            $this->_aDecPriceItem = array_values($this->_aDecPriceItem);
        }

        foreach ($this->_aIncPriceItem as $idx => $aPriceItem) {
            if (!self::_checkValid($aPriceItem)) {
                unset($this->_aIncPriceItem[$idx]);
            }
        }

        if (count($this->_aIncPriceItem) > 0) {
            $this->_aIncPriceItem = array_values($this->_aIncPriceItem);
        }

        foreach ($this->_aIntroPriceItem as $idx => $aPriceItem) {
            if (!self::_checkValid($aPriceItem)) {
                unset($this->_aIntroPriceItem[$idx]);
            }
        }

        if (count($this->_aIntroPriceItem) > 0) {
            $this->_aIntroPriceItem = array_values($this->_aIntroPriceItem);
        }
    }


    /**
     * 计算减价 总抵扣金额
     * @return int|string
     */
    private function _calcDecreaseAmount() {
        $fAmount = 0;
        foreach ($this->_aDecPriceItem as $item) {
            if (is_numeric($item[2]) && $item[2] > 0) {
                $fAmount += $item[2];
            }
        }

        return $fAmount;
    }


    /**
     * 检查PriceItem的格式
     * @param array $aPriceItem $aPriceItem
     * @return bool
     */
    private static function _checkValid($aPriceItem) {
        if (2 == count($aPriceItem) && !empty($aPriceItem[0])) {
            return true;
        } elseif (3 == count($aPriceItem) && !empty($aPriceItem[0]) && $aPriceItem[2] > 0) {
            return true;
        } elseif (4 == count($aPriceItem) && !empty($aPriceItem[0])) {
            return true;
        } else {
            return false;
        }
    }


    /**
     * 是否存在溢价保护
     * @return bool
     */
    private function _isMemberDynamicProtect() {
        if (!empty($this->_aMemberDynamicProtect[0]) && $this->_aMemberDynamicProtect[2] > 0) {
            return true;
        }

        return false;
    }

    /**
     * 特惠快车判断
     * @return bool
     */
    private function _isSpecialPriceFastCar() {
        $aOrderInfo = $this->_oPriceItemFormatter->getOrderInfo();
        return $this->_bIsSpecialRate && Constants\OrderSystem::PRODUCT_ID_FAST_CAR == $aOrderInfo['product_id'];
    }
}
