<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo;

use BizLib\Config as NuwaConfig;
use BizLib\Constants\OrderSystem;
use BizLib\Utils\Currency;
use BizLib\Utils\Language;
use BizLib\Utils\NumberHelper;

/**
 *
 * Copyraight (c) 2021 xiaojukeji.com, Inc. All Rights Reserved.
 * @author: <EMAIL>
 * @date: 2021/1/1 3:59 下午
 * @desc: 单行价格描述（设计考虑可读性、扩展性、规范）
 * @wiki:
 *
 */

class SingleLinePriceDesc
{

    // 加价费用渲染方式映射
    private static $_aIncPriceRenderMap = [
        ['icon_type' => 'spring_red_packet', 'action' => 'getRedPacketInfoLite'],
        ['icon_type' => 'fuzzy_fee', 'action' => 'getLuxuryDesignedDriverInfoLite'],
        ['icon_type' => 'fuzzy_fee', 'action' => 'getCustomizedServicePriceDescLite'],
        ['icon_type' => 'fuzzy_fee', 'action' => 'getCrossCityFeeLite'],
        ['icon_type' => 'fuzzy_fee', 'action' => 'formatFeeStructLite'],
        ['icon_type' => 'fuzzy_fee', 'action' => 'getInfoFeeDescLite'],
        ['icon_type' => 'fuzzy_fee', 'action' => 'getTipsDescLite'],
    ];

    private $_sCurrencyUnit;

    private $_aInfo;
    private $_aText;

    // 正向费用项信息
    private $_aIncPriceItem = [];
    // 负向费用项信息
    private $_aDescPriceItem = [];

    /**
     * @var PriceItemFormatter
     */
    private $_oPriceItemFormatter;
    /**
     * @var array
     */
    private $_aMemberDynamicProtect = [];

    private $_aBillInfo = [];

    /**
     * SingleLinePriceDesc constructor.
     * @param array $aInfo aInfo
     */
    public function __construct(array $aInfo) {
        $this->_aInfo     = $aInfo;
        $sCarLevel        = $aInfo['order_info']['require_level'];
        $this->_aBillInfo = $aInfo['bill_info']['bills'][$sCarLevel] ?? [];
        $this->_oPriceItemFormatter = new PriceItemFormatter($aInfo);
        $this->_aText          = NuwaConfig::text('estimate_new_form', 'estimate_form_info');
        list($sSymbol, $sUnit) = Currency::getSymbolUnit($aInfo['bill_info']['currency'] ?? '', $aInfo['order_info']);
        $this->_sCurrencyUnit  = $sUnit;
    }

    /**
     * 构建价格描述信息
     * @param array $aResponse aResponse
     * @return array
     */
    public function build(array $aResponse) {
        //获取溢价保护
        $this->_getDynamicMemberProtectMsg();

        $this->_fillIncrPriceItem();
        $this->_fillDecPriceItem();

        $aResponse = $this->_renderResponse($aResponse);
        return $aResponse;
    }

    /**
     * 填充加价费用，只关注动调，春节服务费，其他统一模糊化；
     * 有多项费用时，统一模糊化；
     * @return void
     */
    private function _fillIncrPriceItem() {
        //非溢价保护，展示动调
        if (!$this->_isMemberDynamicProtect()) {
            //动调加价
            $aData = $this->_oPriceItemFormatter->getIncDynamicDesc();
            if (!empty($aData[0])) {
                $this->_aIncPriceItem[] = [
                    'icon_type' => 'dynamic_price',
                    'data'      => $aData,
                ];
            }
        }

        foreach (self::$_aIncPriceRenderMap as $aMap) {
            $sAction = $aMap['action'];
            if (method_exists($this->_oPriceItemFormatter, $sAction)) {
                $aData = $this->_oPriceItemFormatter->$sAction();
                if (!empty($aData[0]) && $aData[2] > 0) {
                    $this->_aIncPriceItem[] = [
                        'icon_type' => $aMap['icon_type'],
                        'data'      => $aData,
                    ];
                }
            }
        }
    }

    /**
     * 填充减价费用，只关注会员权益、其他统一模糊化为券；
     * 有多个减价费用，统一模糊化；
     * @return void
     */
    private function _fillDecPriceItem() {
        $sCarLevel        = $this->_aInfo['order_info']['require_level'];
        $fDynamicTotalFee = $this->_aInfo['bill_info']['bills'][$sCarLevel]['dynamic_total_fee'];
        $iEstimateFee     = $this->_aInfo['activity_info'][0]['estimate_fee'];
        $fDiscountFee     = NumberHelper::numberFormatDisplay($fDynamicTotalFee - (float)$iEstimateFee, '', 1);

        $iActivityDiscount = $this->_aBillInfo['discounts_bill']['normal']['discounts_total'] ?? 0;
        if ($iActivityDiscount < 0) {
            $fDiscountFee += abs($iActivityDiscount);
        }

        if ($fDiscountFee > 0) {
            $sPriceDesc = Language::replaceTag(
                $this->_aText['common_dec_price_desc'],
                ['fee_amount' => $fDiscountFee, 'currency_unit' => $this->_sCurrencyUnit]
            );
            // 有优惠的场景，类似展示为"惠 -x元"
            $this->_aDescPriceItem[] = [$sPriceDesc, $this->_aText['common_dec_price_icon'], $fDiscountFee];
        }
    }

    /**
     * 渲染最终的展示项，原则：
     *  1.最终只能有一个费用项，优先级 加价 > 减价
     *  2.区分会员保护权益，有会员保护权益，优先展示减价费用
     * 3.加价项分优先级展示，不做聚合；减价项根据费用项个数，只有一个时，单独展示，多个聚合展示
     * @param array $aResponse aResponse
     * @return array
     */
    private function _renderResponse(array $aResponse) {
        $aPriceDesc = $aPriceDescIcon = [];
        if ($this->_isMemberDynamicProtect()) {
            // 优先展示减价，没有减价则展示加价
            if (count($this->_aDescPriceItem) > 0) {
                // 有溢价保护和其他信息，模糊展示
                $aPriceDesc[]     = Language::replaceTag(
                    $this->_aText['common_dec_price_desc'],
                    ['fee_amount' => $this->_aDescPriceItem[0][2] + $this->_aMemberDynamicProtect[2], 'currency_unit' => $this->_sCurrencyUnit]
                );
                $aPriceDescIcon[] = $this->_aText['common_dec_price_icon'];
            } else {
                // 只展示溢价保护
                $aPriceDesc[]     = Language::replaceTag($this->_aText['common_dec_price_desc'], ['fee_amount' => $this->_aMemberDynamicProtect[2], 'currency_unit' => $this->_sCurrencyUnit]);
                $aPriceDescIcon[] = $this->_aText['member_dec_price_icon'];
            }
        } else {
            // 有加价费用
            if (count($this->_aIncPriceItem) > 0) {
                $aIncPriceItem = current($this->_aIncPriceItem);
                // 动调区分有x倍和x元的情况
                if ('dynamic_price' == $aIncPriceItem['icon_type']) {
                    if (1 == $aIncPriceItem['data']['extra']['use_times']) {
                        // 使用倍数表示 +x倍
                        $aPriceDesc[] = Language::replaceTag($this->_aText['dynamic_times_price_desc'], ['fee_amount' => $aIncPriceItem['data']['extra']['amount']]);
                    } else {
                        // 使用金额表示 +x元
                        $aPriceDesc[] = Language::replaceTag($this->_aText['dynamic_amount_price_desc'], ['fee_amount' => $aIncPriceItem['data']['extra']['amount'], 'currency_unit' => $this->_sCurrencyUnit]);
                    }
                } else {
                    $aPriceDesc[] = Language::replaceTag(
                        $this->_aText['common_inc_price_desc'],
                        ['fee_amount' => $this->_aIncPriceItem[0]['data'][2], 'currency_unit' => $this->_sCurrencyUnit]
                    );
                }

                $aPriceDescIcon[] = $this->_aText[$aIncPriceItem['icon_type']];
            } elseif (count($this->_aDescPriceItem) > 0) {
                $aPriceDesc[]     = $this->_aDescPriceItem[0][0];
                $aPriceDescIcon[] = $this->_aDescPriceItem[0][1];
            }
        }

        $aResponse['price_info_desc'] = Common::formatPriceInfoV2($aPriceDesc,$aPriceDescIcon,[], $this->_aInfo);
        return $aResponse;
    }


    /**
     *  获取溢价保护文案
     *  @return void
     */
    private function _getDynamicMemberProtectMsg() {
        $aOrderInfo = $this->_oPriceItemFormatter->getOrderInfo();
        if (in_array($aOrderInfo['product_id'], [OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR, OrderSystem::PRODUCT_ID_DEFAULT])) {
            $this->_aMemberDynamicProtect = $this->_oPriceItemFormatter->getMemberDynamicProtectInfo();
        }
    }

    /**
     * 是否存在溢价保护
     * @return bool
     */
    private function _isMemberDynamicProtect() {
        if (!empty($this->_aMemberDynamicProtect[0]) && $this->_aMemberDynamicProtect[2] > 0) {
            return true;
        }

        return false;
    }
}
