<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo;

use BizCommon\Constants\OrderNTuple;
use BizCommon\Logics\Order\PricePrivilegeLogic;
use BizLib\Config;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\NumberHelper;
use BizLib\Utils\MemberVersion;
use BizLib\Utils\ProductCategory;
use BizLib\Constants;
use BizLib\Constants\OrderSystem;
use BizLib\Utils as BizUtils;
use BizLib\Utils\PublicLog;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Infrastructure\Util\AsyncMode\AsyncDecorator;
use PreSale\Logics\estimatePriceV2\DecisionLogic;
use PreSale\Logics\estimatePriceV2\multiResponse\component\GuideInfo;
use PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo\strategy\RecommendStrategyUtil;
use PreSale\Logics\taxi\TaxiPeakFee;
use PreSale\Logics\v3Estimate\FeeDescInstance;
use Xiaoju\Apollo\Apollo as ApolloV2;
use PreSale\Logics\estimatePriceV2\multiResponse\component\basicFeeMsg\EmergencyFeeMsg;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;
use PreSale\Models\fee\FeeDetailTemplate;
use PreSale\Logics\estimatePriceV2\bill\CommonBillLogic;
use BizLib\Utils\Language;
use BizCommon\Models\Order;
use PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo\strategy\Common as strategyCommon;

/**
 * Class PriceItemFormatter
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo
 */
class PriceItemFormatter
{
    use AsyncDecorator;
    /**
     * @var array
     */
    private $_aInfo;

    /**
     * @var mixed
     */
    private $_aCommonInfo;

    /**
     * @var array|mixed
     */
    private $_aOrderInfo;

    /**
     * @var array
     */
    private $_aBillInfo;

    /**
     * @var array
     */
    private $_aPaymentsInfo;

    /**
     * @var array
     */
    private $_aDiscountInfo;
    private $_aDiscountInfoMap;

    private $_aCouponInfo;

    /**
     * @var array
     */
    private $_aActivityInfo;

    /**
     * @var bool|mixed|null
     */
    private $_aConfig;

    /**
     * @var string
     */
    private $_sCurrencySymbol;

    /**
     * @var string
     */
    private $_sCurrencyUnit;

    /**
     * @var array
     */
    private $_aDisplayLines;

    /**
     * @var mixed
     */
    private $_aPassengerInfo;

    /**
     * @var mixed
     */
    private $_common;

    const SHAKE_RESULT_FIRSTESTIMATE = '1';
    const SHAKE_RESULT_SECOND_ESTIMATEBIND_SUCCESS = '2';
    const SHAKE_RESULT_SECOND_ESTIMATEFAIL         = '3';
    const SHAKE_RESULT_ICON      = 'https://dpubstatic.udache.com/static/dpubimg/9804dd19-9f16-43e0-802a-41cedfc3628c.gif';
    const SHAKE_RESULT_OPEN_ICON = 'https://dpubstatic.udache.com/static/dpubimg/e90a8494-1b4e-46e9-a415-5510c754e366.png';
    const LUX_MEMBER_LEVEL_PILOT = 2;
    const LUX_MEMBER_LEVEL_PILOT_BGCOLOR_FROM = '#F4E2BE';
    const LUX_MEMBER_LEVEL_PILOT_BGCOLOR_TO   = '#E3BE7E';
    const LUX_MEMBER_LEVEL_PILOT_FONT_COLOR   = '#333333';
    const LUX_MEMBER_LEVEL_COMMANDER          = 3;
    const LUX_MEMBER_LEVEL_COMMANDER_BGCOLOR_FROM = '#171826';
    const LUX_MEMBER_LEVEL_COMMANDER_BGCOLOR_TO   = '#100D0B';
    const LUX_MEMBER_LEVEL_COMMANDER_FONT_COLOR   = '#FFFFFF';

    const FEE_AIRPORT_VIP   = 'sps_airport_vip_fee';      //机场贵宾休息费用名 (已下线)
    const FEE_CIP           = 'sps_cip_fee';              //cip费用名 (已下线)
    const FEE_STATION_GUIDE = 'sps_pick_up_guide_fee';    //接送机引导费用名
    const FEE_CHILD_SEAT    = 'sps_child_seat_fee';       //儿童座椅服务 (已下线)
    const FEE_BARRIER_FREE_CAR = 'sps_barrier_free_car_fee'; //无障碍车服务费
    const FEE_PET_SEAT         = 'sps_pet_fee';       //宠物专车服务
    const FEE_PET_PLATFORM_FEE = 'sps_pet_platform_fee';  //宠物专车-专项保险和运营费
    const FEE_FIVE_STAR_FEE    = 'sps_five_star_service_fee';  //专车-五星司机服务费

    const COUPON_TAG_NEW_USER            = 'NEW_USER'; // 新人券标识
    const COUPON_TAG_NEW_PLATFORM        = 'MPT_NEW_PLATFORM'; // 智能定价决策出来的新客 平台新
    const COUPON_TAG_NEW_CATEGORY        = 'MPT_NEW_CATEGORY'; // 智能定价决策出来的新客 品类新
    const COUPON_TAG_POPE_CALLRETURN     = 'POPE_CALLRETURN';
    const COUPON_TAG_PERORDERSALE        = 'PERORDERSALE'; // pope出的单单省
    const COUPON_TAG_PREMIUM_PAID_MEMBER = 'premium_paid_member'; // tag:专车付费会员
    const COUPON_TAG_NEWLOSS      = 'newloss';
    const COUPON_TAG_NEWLOSS_TASK = 'newloss_develop_task';
    const CHAO_ZHI_WEEKEND        = 'CHAO_ZHI_WEEKEND'; // 超值标签

    // 优惠类型
    const DISCOUNT_TYPE_CITY_CARD = 'citycard'; // 市民卡
    const DISCOUNT_TYPE_ECONOMICAL_CARD_RIGHT = 'economical_card_right'; // 单单省钱卡

    const FEE_DISPLAY_TYPE_NORMAL  = 1; // 已省
    const FEE_DISPLAY_TYPE_SPECIAL = 2; // 特殊活动感知
    const FEE_DISPLAY_TYPE_JINGXI  = 3; // 惊喜优惠

    const PRICE_TYPE_DEFAULT         = 0; // 其他
    const PRICE_TYPE_COUPON          = 1; // 券优惠
    const PRICE_TYPE_DISCOUNT_COMMON = 2; // 非券优惠
    const PRICE_TYPE_NEWCPMER_COUPON_RECALL = 3; // 呼返优惠新客券
    const PRICE_TYPE_SINGLE_FOLD            = 4; //单单折
    const PRICE_TYPE_LONG_DISTANCE_CARPOOL  = 5; // 远途特价
    const PRICE_TYPE_DIDI_DOLLAR            = 6; // 香港打车金 DiDi Dollar
    const PRICE_TYPE_REVOLVING_ACCOUNT_DISCOUNT = 9; // 内循环账户折扣
    const PRICE_TYPE_REVOLVING_ACCOUNT_REBATE   = 10; // 内循环账户返利

    const PRICE_TYPE_TAXI_PEAK_FEE    = 7;  // 出租车峰期加价
    const PRICE_TYPE_TAXI_HOLIDAY_FEE = 8;  // 出租车节假日附加费
    const PRICE_TYPE_TAXI_CROSS_CITY_FEE = 11; // 出租车跨城费

    const PRICE_TYPE_COUPON_PAYED_RETURN = 3; // 支返券


    /**
     * PriceItemFormatter constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo         = $aInfo;
        $this->_aCommonInfo   = $aInfo['common_info'];
        $this->_aOrderInfo    = $aInfo['order_info'] ?? [];
        $sCarLevel            = $aInfo['order_info']['require_level'];
        $this->_aBillInfo     = $aInfo['bill_info']['bills'][$sCarLevel] ?? [];
        $this->_aPaymentsInfo = $aInfo['payments_info'] ?? [];
        $this->_aActivityInfo = $aInfo['activity_info'][0];
        $this->_aPassengerInfo = $aInfo['passenger_info'];
        $this->_aDiscountInfo  = $aInfo['activity_info'][0]['discount_desc'] ?? [];
        $this->_aCouponInfo    = $aInfo['activity_info'][0]['coupon_info'] ?? [];
        $this->_aDisplayLines  = CommonBillLogic::formatDisplayLines($this->_aBillInfo['display_lines']);

        $this->_aConfig       = NuwaConfig::text('config_text', 'getEstimateFeeInfo');
        $this->_aEstimateText = NuwaConfig::text('config_text', 'mOrderEstimateNew');

        $this->_aEstimateTextDcmp = Language::getDecodedTextFromDcmp('config_text-mOrderEstimateNew'); //文案迁到dcmp后可以把上面那行干掉

        $sCurrency = $aInfo['bill_info']['currency'] ?? '';
        list($sSymbol, $sUnit)  = \BizLib\Utils\Currency::getSymbolUnit($sCurrency, $aInfo['order_info']);
        $this->_sCurrencySymbol = $sSymbol;
        $this->_sCurrencyUnit   = $sUnit;
        $this->_common          = new Common($aInfo);
        $this->_initDiscountInfoMap();
    }

    /**
     * @return void
     */
    private function _initDiscountInfoMap() {
        $aDiscountInfo = $this->_aInfo['activity_info'][0]['discount_desc'] ?? [];
        foreach ($aDiscountInfo as $aItem) {
            $this->_aDiscountInfoMap[$aItem['type']] = $aItem;
        }
    }

    /**
     * 返回货币符号和单位
     * @return array
     */
    public function getSymbolUnit() {
        return [$this->_sCurrencySymbol, $this->_sCurrencyUnit];
    }

    /**
     * 是否显示折叠优惠项, 优惠项大于1个时展示
     * @param array $aDescConfig DescConfig
     * @return array
     */
    public function getShowFoldDiscountInfo($aDescConfig = []) {
        $aDiscountDesc = [
            'total'                      => 0.0,
            'num'                        => 0,
            'desc'                       => '',
            'desc_icon'                  => '',
            'include_dynamic_price'      => false,
            'include_fixed_preferential' => false,
        ];

        if (empty($this->_aDiscountInfo) || !\BizLib\Utils\Product::displayFoldDiscount($this->_aOrderInfo['product_id'])) {
            return $aDiscountDesc;
        }

        foreach ($this->_aDiscountInfo as $aDiscountItem) {
            if (($fDiscount = abs($aDiscountItem['amount'] ?? 0)) > 0 && 'infofee' != $aDiscountItem['type']) {
                ++$aDiscountDesc['num'];
                $aDiscountDesc['total'] += $fDiscount;
            }
        }

        $fDynamicPrice = $this->_aBillInfo['dynamic_diff_price'] ?? 0.0;
        if ($fDynamicPrice < 0) {
            ++$aDiscountDesc['num'];
            $aDiscountDesc['total'] += abs($fDynamicPrice);
        }

        $fFixedPreferential = $this->_aBillInfo['fixed_preferential'] ?? 0.0;
        if ($fFixedPreferential < 0) {
            ++$aDiscountDesc['num'];
            $aDiscountDesc['total'] += abs($fFixedPreferential);
        }

        $iActivityDiscount = $this->_aBillInfo['discounts_bill']['normal']['discounts_total'] ?? 0;
        if ($iActivityDiscount < 0) {
            $iNum = count($this->_aBillInfo['discounts_bill']['normal']['discounts']);
            $aDiscountDesc['num']    = $aDiscountDesc['num'] + $iNum;
            $aDiscountDesc['total'] += abs($iActivityDiscount);
        }

        if ($aDiscountDesc['num'] > 1) {
            if ($this->_aConfig) {
                $sDiscountTitle        = empty($aDescConfig) ? $this->_aConfig['discount_title'] : $aDescConfig['discount_title'];
                $aDiscountDesc['desc'] = Language::replaceTag(
                    $sDiscountTitle,
                    array(
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'num'             => NumberHelper::numberFormatDisplay($aDiscountDesc['total']),
                        'currency_unit'   => $this->_sCurrencyUnit,
                    )
                );
            }

            if ($fDynamicPrice < 0) {
                $aDiscountDesc['include_dynamic_price'] = true;
            }

            if ($fFixedPreferential < 0) {
                $aDiscountDesc['include_fixed_preferential'] = true;
            }
        }

        return $aDiscountDesc;
    }


    /**
     * 春节红包
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getRedPacketInfo($aPriceDesc, $aPriceDescIcon) {
        list($sPriceDesc,$sPriceDescIcon, $fRedPacket) = $this->getRedPacketInfoLite();
        if (!empty($sPriceDesc)) {
            $aPriceDesc[]     = $sPriceDesc;
            $aPriceDescIcon[] = $sPriceDescIcon;
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 春节红包
     * @return array
     */
    public function getRedPacketInfoLite() {
        $sPriceDesc      = $sPriceDescIcon = '';
        $fRedPacketValue = $this->_aDisplayLines['red_packet']['value'] ?? 0.0;
        if ($fRedPacketValue > 0) {
            $sDesc          = Language::replaceTag(
                NuwaConfig::text('config_text', 'red_packet_estimate_title'),
                array(
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'name'            => NuwaConfig::text('config_text', 'red_packet_name'),
                    'num'             => $fRedPacketValue,
                    'currency_unit'   => $this->_sCurrencyUnit,
                )
            );
            $sPriceDesc     = $sDesc;
            $sPriceDescIcon = '';
        }

        return [$sPriceDesc,$sPriceDescIcon, $fRedPacketValue];
    }

    /**
     * 优惠次数
     * @param array $aPriceDesc     $aPriceDesc
     * @param array $aPriceDescIcon $aPriceDescIcon
     * @return array
     */
    public function getEmergencyFreqSurplus($aPriceDesc, $aPriceDescIcon) {
        if (isset($this->_aInfo['order_info']['activity_info']['emergency_freq_surplus'])
            && $this->_aInfo['order_info']['activity_info']['emergency_freq_surplus'] >= 0
        ) {
            $aPriceDesc[] = Language::replaceTag(
                $this->_aConfig['emergency_freq_surplus'],
                [
                    'num' => $this->_aInfo['order_info']['activity_info']['emergency_freq_surplus'],
                ]
            );
        }

        $aPriceDescIcon[] = '';
        return [$aPriceDesc, $aPriceDescIcon];
    }


    /**
     * 优惠多少元
     * @param array $aPriceDesc     $aPriceDesc
     * @param array $aPriceDescIcon $aPriceDescIcon
     * @return array
     */
    public function getEmergencyDiscount($aPriceDesc, $aPriceDescIcon) {
        list($fRetFree, $fRetDiscount, $fFeeAmount) = (new EmergencyFeeMsg($this->_aInfo))->getFeeDiscount();
        if ($fRetDiscount > 0) {
            $aPriceDesc[] = Language::replaceTag(
                $this->_aConfig['emergency_discount_msg'],
                ['num' => $fRetDiscount,]
            );
        }

        $aPriceDescIcon[] = '';

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 跨城费
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getCrossCityFee($aPriceDesc, $aPriceDescIcon) {
        list($sPriceDesc, $sPriceDescIcon, $fCost) = $this->getCrossCityFeeLite();
        if (!empty($sPriceDesc)) {
            $aPriceDesc[]     = $sPriceDesc;
            $aPriceDescIcon[] = $sPriceDescIcon;
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 跨城费
     * @return array
     */
    public function getCrossCityFeeLite() {
        $sPriceDesc = $sPriceDescIcon = '';
        //拼车/特价车 都没有配置跨城费
        if (Util::isCarpool($this->_aInfo)) {
            return [$sPriceDesc, $sPriceDescIcon, 0];
        }

        if (isset($this->_aBillInfo['cross_city_fee'])) {
            $iCrossCityFee = (float)($this->_aBillInfo['cross_city_fee']);
            if ($iCrossCityFee > 0) {
                $sPriceDesc     = Language::replaceTag(
                    $this->_aConfig['cross_city_fee'],
                    [
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'cross_city_fee'  => NumberHelper::numberFormatDisplay($iCrossCityFee),
                        'currency_unit'   => $this->_sCurrencyUnit,
                    ]
                );
                $sPriceDescIcon = '';
            }
        }

        //如果是豪华车包车，则增加固定文案，提示跨城费
        if (Util::isLuxRent($this->_aInfo)) {
            $oApolloV2      = new ApolloV2();
            $oFeatureToggle = $oApolloV2->featureToggle(
                'gs_luxrent_cross_city_fee_text',
                [
                    'key'   => $this->_aPassengerInfo['pid'],
                    'phone' => $this->_aPassengerInfo['phone'],
                ]
            );
            if ($oFeatureToggle->allow()) {
                //文案"若终点跨城，将产生额外跨城费"
                $sPriceDesc     = $this->_aEstimateText['cross_city_fee_lux_rent_text'];
                $sPriceDescIcon = '';
            }
        }

        return [$sPriceDesc, $sPriceDescIcon, $iCrossCityFee ?? 0];
    }

    /**
     * 获取接送机单price_desc
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getAirportOrderPriceDesc($aPriceDesc, $aPriceDescIcon) {
        list($sPriceDesc,$sPriceDescIcon) = $this->getAirportOrderPriceDescLite();
        if (!empty($sPriceDesc)) {
            $aPriceDesc[]     = $sPriceDesc;
            $aPriceDescIcon[] = $sPriceDescIcon;
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * @return array
     */
    public function getAirportOrderPriceDescLite() {
        $sPriceDesc = $sPriceDescIcon = '';
        //仅限专车接送机
        if (BizUtils\Product::isDefault($this->_aOrderInfo['product_id'])
            && isset($this->_aOrderInfo['require_level'])
            && in_array($this->_aOrderInfo['combo_type'], [ Constants\Horae::TYPE_COMBO_FROM_AIRPORT, Constants\Horae::TYPE_COMBO_TO_AIRPORT])
        ) {
            // 接送机一口价
            if (isset($this->_aBillInfo['cap_price']) && $this->_aBillInfo['cap_price'] > 0) {
                $sPriceDesc     = $this->_aConfig['airport_flat_rate_price_desc'];
                $sPriceDescIcon = '';
            }
        }

        return [$sPriceDesc, $sPriceDescIcon];
    }


    /**
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function formatFeeStruct($aPriceDesc, $aPriceDescIcon) {
        list($sPriceDesc,$sPriceDescIcon, $fCost) = $this->formatFeeStructLite();
        if (!empty($sPriceDesc)) {
            $aPriceDesc[]     = $sPriceDesc;
            $aPriceDescIcon[] = $sPriceDescIcon;
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * @return array
     */
    public function formatFeeStructLite() {
        $sPriceDesc        = $sPriceDescIcon = '';
        $aEstimateFixedFee = CommonBillLogic::formatEstimateFixedFees($this->_aBillInfo['estimate_fixed_fees'] ?? '');
        if (empty($aEstimateFixedFee)) {
            return [$sPriceDesc, $sPriceDescIcon, 0];
        }

        $fFee         = 0;
        $aTitleConfig = NuwaConfig::text('config_fee_text', 'fee_list');
        foreach ($aEstimateFixedFee as $sKey => $aItem) {
            if (!isset($aItem['value']) || $aItem['value'] <= 0 || empty($sKey)) {
                continue;
            }

            if (empty($aTitleConfig[$sKey]['estimate_title'])) {
                continue;
            }

            // 这个值会被覆盖？原有逻辑并没有break退出循环
            $fFee           = $aItem['value'];
            $sPriceDesc     = Language::replaceTag(
                $aTitleConfig[$sKey]['estimate_title'],
                array(
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'fee'             => NumberHelper::numberFormatDisplay($aItem['value']),
                    'currency_unit'   => $this->_sCurrencyUnit,
                )
            );
            $sPriceDescIcon = $aTitleConfig[$sKey]['estiamte_icon'] ?? '';
        }

        return [$sPriceDesc, $sPriceDescIcon, $fFee];
    }

    /**
     * 定制化服务
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getCustomizedServicePriceDesc($aPriceDesc, $aPriceDescIcon) {
        list($sPriceDesc,$sPriceDescIcon, $fCost) = $this->getCustomizedServicePriceDescLite();
        if (!empty($sPriceDesc)) {
            $aPriceDesc[]     = $sPriceDesc;
            $aPriceDescIcon[] = $sPriceDescIcon;
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * @return array
     */
    public function getCustomizedServicePriceDescLite() {
        $sCustomInfo = $sPriceDescIcon = '';
        // 定制化服务:宝贝专车和残障车未迁移ssse前使用 customized_service_result 结果，迁移后使用 personalized_customized_result
        if (isset($this->_aBillInfo['personalized_customized_result']) || isset($this->_aBillInfo['display_lines'])) {
            list($sCustomInfo, $fCost) = $this->_getCustomServiceDescNew($this->_aBillInfo['personalized_customized_result'], $this->_aBillInfo['display_lines'], $this->_aBillInfo['fee_detail_info']);
        } elseif (isset($this->_aBillInfo['customized_service_result'])) {
            list($sCustomInfo, $fCost) = $this->_getCusItomServiceDesc($this->_aBillInfo['customized_service_result']);
        }

        return [$sCustomInfo, $sPriceDescIcon, $fCost ?? 0];
    }

    /**
     * @param array $aPersonalizedCustomized $aPersonalizedCustomized
     * @param array $aDisplayLines           $aDisplayLines
     * @param array $aFeeDetailInfo          $aFeeDetailInfo
     * @return mixed|string
     */
    private function _getCustomServiceDescNew($aPersonalizedCustomized, $aDisplayLines, $aFeeDetailInfo) {
        $fCostSum = 0.0;
        if (!empty($aPersonalizedCustomized)) {
            if (isset($aPersonalizedCustomized['sps_pick_up_guide_fee'])) {
                $fCostSum += $aPersonalizedCustomized['sps_pick_up_guide_fee']['cost'];
            }

            if (isset($aPersonalizedCustomized['sps_cip_fee'])) {
                $fCostSum += $aPersonalizedCustomized['sps_cip_fee']['cost'];
            }
        }

        $fPetFee     = 0.0;
        $iCount      = 0;
        $sKeyOnlyOne = '';
        if (!empty($aDisplayLines)) {
            $aSpsKeys = [
                self::FEE_AIRPORT_VIP,
                self::FEE_CIP,
                self::FEE_CHILD_SEAT,
                self::FEE_BARRIER_FREE_CAR,
                self::FEE_FIVE_STAR_FEE,
            ];

            if (!isset($aFeeDetailInfo['sps_pick_up_guide_free'])) {
                $aSpsKeys[] = self::FEE_STATION_GUIDE;
            }

            foreach ($aDisplayLines as $aDisplayLine) {
                if (self::FEE_PET_SEAT == $aDisplayLine['name'] || self::FEE_PET_PLATFORM_FEE == $aDisplayLine['name']) {
                    $fCostSum += $aDisplayLine['value'];
                    $fPetFee  += $aDisplayLine['value'];
                } elseif (in_array($aDisplayLine['name'], $aSpsKeys)) {
                    $fCostSum   += $aDisplayLine['value'];
                    $sKeyOnlyOne = $aDisplayLine['name'];
                    $iCount++;
                }
            }
        }

        if ($fCostSum > 0) {
            if ($fCostSum == $fPetFee) {
                // 只有宠物专车费用,pr文案不能是"含已选服务费xx元", 需要独立展示
                return [
                    Language::replaceTag(
                        $this->_aConfig['pet_car_price_desc'],
                        ['cost' => $fCostSum,]
                    ), $fCostSum,
                ];
            }

            $aConfig = NuwaConfig::text('scene_data', 'custom_service');
            if (1 == $iCount && !empty($aConfig[$sKeyOnlyOne])) {
                // 只存在一个时展示
                return [
                    Language::replaceTag(
                        $aConfig[$sKeyOnlyOne],
                        ['cost' => $fCostSum,]
                    ), $fCostSum,
                ];
            }

            return [
                Language::replaceTag(
                    $this->_aConfig['custom_price_desc'],
                    ['cost' => $fCostSum,]
                ), $fCostSum,
            ];
        }

        return ['', 0];
    }

    /**
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @param array $aFoldDiscount  折叠优惠信息
     * @return array
     */
    public function getFixedPreferentialPriceDesc($aPriceDesc, $aPriceDescIcon, $aFoldDiscount) {
        list($sPriceDesc, $sPriceDescIcon, $fAmount) = $this->getFixedPreferentialPriceDescLite($aFoldDiscount);
        if (!empty($sPriceDesc)) {
            $aPriceDesc[]     = $sPriceDesc;
            $aPriceDescIcon[] = $sPriceDescIcon;
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }


    /**
     * @param array $aFoldDiscount 折叠优惠信息
     * @return array
     */
    public function getFixedPreferentialPriceDescLite($aFoldDiscount) {
        $sPriceDesc = $sPriceDescIcon = '';
        //多因素一口价应该只有普通场景才有，此处不额外做限制（此前只配置了noncarpool）
        $fFixedPref = $this->_aBillInfo['fixed_preferential'] ?? 0;
        $fAmount    = 0;
        if ($fFixedPref < 0 && !$aFoldDiscount['include_fixed_preferential']) {
            //已优惠{4.6}元
            $fAmount        = abs($fFixedPref);
            $sPriceDesc     = Language::replaceTag(
                $this->_aConfig['fixed_preferential'],
                array(
                    'price'         => $fAmount,
                    'currency_unit' => $this->_sCurrencyUnit,
                )
            );
            $sPriceDescIcon = '';
        }

        return [$sPriceDesc,$sPriceDescIcon,$fAmount];
    }

    /**
     * 动调相关费用信息
     * @param array $aPriceDesc            aPriceDesc
     * @param array $aPriceDescIcon        aPriceDescIcon
     * @param array $aFoldDiscount         折叠优惠信息
     * @param bool  $bMultiPremiumEstimate 是否专车多预估
     * @return array
     */
    public function getDynamicPriceDesc($aPriceDesc, $aPriceDescIcon, $aFoldDiscount, $bMultiPremiumEstimate = false) {
        list($sPriceDesc,$sPriceDescIcon) = $this->getDynamicPriceDescLite('both',$aFoldDiscount,$bMultiPremiumEstimate);
        if (!empty($sPriceDesc)) {
            $aPriceDesc[]     = $sPriceDesc;
            $aPriceDescIcon[] = $sPriceDescIcon;
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }


    /**
     * 获取动调 price_desc
     * @param string $sDynamicPriceDescType $sDynamicPriceDescType
     * @param array  $aFoldDiscount         $aFoldDiscount
     * @param bool   $bMultiPremiumEstimate $bMultiPremiumEstimate
     * @return array
     */
    public function getDynamicPriceDescLite($sDynamicPriceDescType, $aFoldDiscount, $bMultiPremiumEstimate = false) {
        $fAmount         = 0;
        $sPriceDesc      = $sPriceDescIcon = '';
        $bForceDisplay   = false;
        $bIsCarpool      = BizUtils\Horae::isCarpool($this->_aOrderInfo['combo_type'], $this->_aOrderInfo['require_level']);
        $fRedPacketValue = $this->_aDisplayLines['red_packet']['value'] ?? 0.0;
        if (!empty($this->_aBillInfo) && !$aFoldDiscount['include_dynamic_price'] && ($fRedPacketValue <= 0 || $bForceDisplay || $bMultiPremiumEstimate)) {
            if ('both' == $sDynamicPriceDescType) {
                $sPriceDesc = $this->getDynamicDesc($bMultiPremiumEstimate);
            } elseif ('inc' == $sDynamicPriceDescType) {
                list($sPriceDesc, $aTmp) = $this->getIncDynamicDesc();
            } elseif ('dec' == $sDynamicPriceDescType) {
                list($sPriceDesc,$fAmount) = $this->getDecDynamicDesc($bMultiPremiumEstimate);
                $sPriceDesc = '-1' == $sPriceDesc ? '' : $sPriceDesc;
            }

            if (!empty($sPriceDesc)) {
                //拼车未配置Icon
                if (!$bIsCarpool && $this->isShowDynamicMemberProtectMsg()) {
                    $sPriceDescIcon = $this->_aPassengerInfo['member_profile']['level_icon'];
                } elseif (util::isDaCheAnyCar($this->_aInfo)) {
                    $sPriceDescIcon = $this->_aEstimateText['dynamic_icon'];
                }
            }
        }

        if ('dec' == $sDynamicPriceDescType) {
            return [$sPriceDesc, $sPriceDescIcon, $fAmount];
        } else {
            return [$sPriceDesc, $sPriceDescIcon];
        }
    }

    /**
     * 跨城拼车保险费
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getInterInsurancePriceDesc($aPriceDesc, $aPriceDescIcon) {
        list($sPriceDesc,$sPriceDescIcon) = $this->getInterInsurancePriceDescLite();
        if (!empty($sPriceDesc)) {
            $aPriceDesc[]     = $sPriceDesc;
            $aPriceDescIcon[] = $sPriceDescIcon;
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * @return array
     */
    public function getInterInsurancePriceDescLite() {
        $sPriceDesc         = $sPriceDescIcon = '';
        $iInterInsuranceFee = (float)($this->_aBillInfo['insure_total_fee']);
        if ($iInterInsuranceFee > 0 && $this->_aOrderInfo['is_select_insurance']) {
            $sPriceDesc     = Language::replaceTag(
                $this->_aConfig['carpool_inter_insurance_price_desc'],
                array(
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'insurance_fee'   => NumberHelper::numberFormatDisplay($iInterInsuranceFee),
                    'currency_unit'   => $this->_sCurrencyUnit,
                )
            );
            $sPriceDescIcon = '';
        }

        return [$sPriceDesc, $sPriceDescIcon];
    }

    /**
     * 获取券信息
     * @param array  $aPriceDesc      aPriceDesc
     * @param array  $aPriceDescIcon  aPriceDescIcon
     * @param array  $aFoldDiscount   折叠优惠信息
     * @param string $sCityCardDesc   市民卡信息
     * @param bool   $bShakeSuccess   摇一摇是否成功
     * @param bool   $bIsSpecialRate  是否特价车
     * @param array  $aDiscountConfig DescConfig
     * @return array
     */
    public function getCouponDesc($aPriceDesc, $aPriceDescIcon, $aFoldDiscount, $sCityCardDesc = '', $bShakeSuccess = false, $bIsSpecialRate = false, $aDiscountConfig = []) {
        list($sPriceDesc, $sPriceDescIcon, $fAmount) = $this->getCouponDescLite($aFoldDiscount,$sCityCardDesc,$bShakeSuccess,$bIsSpecialRate,$aDiscountConfig);
        if (!empty($sPriceDesc)) {
            $aPriceDesc[]     = $sPriceDesc;
            $aPriceDescIcon[] = $sPriceDescIcon;
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 获取券信息
     * @param array  $aFoldDiscount   $aFoldDiscount
     * @param string $sCityCardDesc   $sCityCardDesc
     * @param bool   $bShakeSuccess   $bShakeSuccess
     * @param bool   $bIsSpecialRate  $bIsSpecialRate
     * @param array  $aDiscountConfig $aDiscountConfig
     * @return array
     */
    public function getCouponDescLite($aFoldDiscount, $sCityCardDesc = '', $bShakeSuccess = false, $bIsSpecialRate = false, $aDiscountConfig = []) {
        $aConfig          = empty($aDiscountConfig) ? $this->_aConfig : $aDiscountConfig;
        $aCouponDetail    = $this->_aActivityInfo['estimate_detail'];
        $aCouponInfo      = $this->_aActivityInfo['coupon_info'];
        $aDiscountInfo    = $this->_aActivityInfo['discount_desc'];
        $iFinalCouponType = $this->_aActivityInfo['final_coupon_type'];

        $fAmount     = 0;
        $sCouponDesc = $sCouponDescIcon = '';
        $iPriceType  = self::PRICE_TYPE_DEFAULT;
        if (isset($aCouponDetail['title'], $aCouponDetail['value']) && empty($aFoldDiscount['desc']) && empty($sCityCardDesc) && !$bShakeSuccess) {
            if ($bIsSpecialRate && !Util::isFastCarSpecialRate($this->_aInfo)) {
                //特价出租车
                list($fAmount, $sCouponDesc) = $this->_handlerSpecialTaxi($aDiscountInfo, $aCouponDetail['amount']);
                return [$sCouponDesc, $sCouponDescIcon, $fAmount, $iPriceType];
            }

            if (FeeDetailTemplate::isMonthlyCardCoupon($aCouponInfo)) {
                //打车月卡
                $fAmount     = $aDiscountInfo[0]['amount'];
                $sCouponDesc = Language::replaceTag(
                    $this->_aConfig['monthly_card_title'],
                    [
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'num'             => $fAmount,
                        'currency_unit'   => $this->_sCurrencyUnit,
                    ]
                );
                $iPriceType  = self::PRICE_TYPE_DISCOUNT_COMMON;
            } elseif (FeeDetailTemplate::isPaidMemberCoupon($aCouponInfo)) {
                // 超级会员
                $fAmount         = $aDiscountInfo[0]['amount'];
                $sCouponDesc     = Language::replaceTag(
                    $this->_aConfig['super_member_title'],
                    [
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'num'             => $fAmount,
                        'currency_unit'   => $this->_sCurrencyUnit,
                    ]
                );
                $sCouponDescIcon = $this->_aConfig['super_member_icon'];
                $iPriceType      = self::PRICE_TYPE_DISCOUNT_COMMON;
            } elseif (FeeDetailTemplate::isOnlyStudentCardCoupon($aCouponInfo)) {
                //学生券
                $fAmount     = $aDiscountInfo[0]['amount'];
                $sCouponDesc = Language::replaceTag(
                    $aConfig['student_card_title'],
                    [
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'num'             => $fAmount,
                        'currency_unit'   => $this->_sCurrencyUnit,
                    ]
                );
                $iPriceType  = self::PRICE_TYPE_DISCOUNT_COMMON;
            } else {
                $fAmount          = NumberHelper::numberFormatDisplay($aCouponDetail['amount']);
                $iPriceType       = self::PRICE_TYPE_COUPON;
                $sCouponCustomTag = $aCouponInfo['activity_coupon']['custom_tag'] ?? ($aCouponInfo['default_coupon']['custom_tag'] ?? '');
                $iProductCategory = $this->_aOrderInfo['product_category'] ?? -1;
                $iMaxAmount       = (int)$aConfig['aplus_max_amount'];

                $sCouponDesc = Language::replaceTag(
                    $aConfig['coupon_title_with_brackets'],
                    array(
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'fee'             => $fAmount,
                        'currency_unit'   => $this->_sCurrencyUnit,
                        'title'           => $aCouponDetail['title'],
                    )
                );

                // 小程序新人券信息
                if (MainDataRepo::isApplet() && self::COUPON_TAG_NEW_USER == $sCouponCustomTag) {
                    $sCouponDesc = Language::replaceTag(
                        $aConfig['new_user_coupon_title'],
                        array(
                            'currency_symbol' => $this->_sCurrencySymbol,
                            'fee'             => $fAmount,
                            'currency_unit'   => $this->_sCurrencyUnit,
                            'title'           => $aCouponDetail['title'],
                        )
                    );
                }

                // 特快新客特殊处理
                if (ProductCategory::PRODUCT_CATEGORY_APLUS == $iProductCategory
                    && self::COUPON_TAG_NEW_CATEGORY == $sCouponCustomTag
                    && $aCouponDetail['amount'] < $iMaxAmount
                ) {
                    $sCouponDesc = $aConfig['aplus_new_category'];
                }

                //记录当前优惠描述信息
                RecommendStrategyUtil::recordCouponDesc($this->_aInfo, $sCouponDesc, $sCouponDescIcon);

                //6.0.6以上为呼返券添加icon
                if (version_compare(MainDataRepo::getAppVersion(), '6.0.6') >= 0 && Util::isDaCheAnyCar($this->_aInfo)) {
                    if (self::COUPON_TAG_POPE_CALLRETURN == $sCouponCustomTag) {
                        $sCouponDescIcon = $this->_aConfig['pope_callreturn_icon_gif'];
                        //区域渗透灯塔营销
                        $aApolloParams  = [
                            'key'           => $this->_aPassengerInfo['pid'],
                            'phone'         => $this->_aPassengerInfo['phone'],
                            'city'          => $this->_aOrderInfo['area'],
                            'business_id'   => $this->_aOrderInfo['business_id'],
                            'car_level'     => $this->_aOrderInfo['require_level'],
                            'combo_type'    => $this->_aOrderInfo['combo_type'],
                            'level_type'    => $this->_aOrderInfo['level_type'],
                            'access_key_id' => $this->_aCommonInfo['access_key_id'],
                            'county'        => $this->_aOrderInfo['county'],
                        ];
                        $oFeatureToggle = (new ApolloV2())->featureToggle('gs_coupon_desc_icon_switch', $aApolloParams);
                        if ($oFeatureToggle->allow()) {
                            $sCouponDescIcon = $oFeatureToggle->getParameter('pic_url', '');
                            if (!empty($sCouponDescIcon)) {
                                $this->async()->writeSpecialPublicLog(self::PRICE_TYPE_COUPON);
                            }
                        }

                        $iPriceType = self::PRICE_TYPE_COUPON;
                    }

                    // 根据tag配置三排文案
                    if (!empty($sCouponCustomTag)) {
                        $aCouponTagConfig = NuwaConfig::text('estimate_new_form', 'three_line_coupon_template');
                        if (!empty($aCouponTagConfig) && !empty($aCouponTagConfig[$sCouponCustomTag])) {
                            if (isset($aCouponTagConfig[$sCouponCustomTag]['icon'])) {
                                $sCouponDescIcon = $aCouponTagConfig[$sCouponCustomTag]['icon'];
                            }

                            if (isset($aCouponTagConfig[$sCouponCustomTag]['desc'])) {
                                $sCouponDesc = Language::replaceTag(
                                    $aCouponTagConfig[$sCouponCustomTag]['desc'],
                                    array(
                                        'currency_symbol' => $this->_sCurrencySymbol,
                                        'amount'          => $fAmount,
                                        'currency_unit'   => $this->_sCurrencyUnit,
                                    )
                                );
                            }
                        }
                    }

                    //特惠周活动标签优先级高
                    $aCouponInfo = $this->_aInfo['activity_info'][0]['coupon_info'];
                    $iBatchId    = $aCouponInfo['activity_coupon']['batchid'] ?? 0;
                    if (empty($iBatchId) && !empty($aCouponInfo['default_coupon']['batchid'])) {
                        $iBatchId = $aCouponInfo['default_coupon']['batchid'];
                    }

                    $sWeek            = date('w') == 0 ? '7' : date('w');
                    list($msec, $sec) = explode(' ', microtime());
                    $msectime         = (float)sprintf('%.0f', ((float)($msec) + (float)($sec)) * 1000);
                    $aApolloParams    = [
                        'key'              => $this->_aPassengerInfo['pid'],
                        'phone'            => $this->_aPassengerInfo['phone'],
                        'city'             => $this->_aOrderInfo['area'],
                        'access_key_id'    => $this->_aCommonInfo['access_key_id'],
                        'county'           => $this->_aOrderInfo['county'],
                        'batch_ids'        => $iBatchId,
                        'time_period'      => Date('H:i'),
                        'date_time_period' => Date('Y-m-d H:i'),
                        'day_of_week'      => $sWeek,
                        'business_id'      => $this->_aOrderInfo['business_id'],
                        'car_level'        => $this->_aOrderInfo['require_level'],
                        'product_category' => $this->_aOrderInfo['product_category'],
                        'unix_time'        => $msectime,
                    ];
                    $oFeatureToggle   = (new ApolloV2())->featureToggle('gs_coupon_week_icon_switch', $aApolloParams);
                    if (\BizCommon\Utils\Order::isSpecialRateV2($this->_aOrderInfo) && $oFeatureToggle->allow()) {
                        $sCouponDescIcon = $oFeatureToggle->getParameter('pic_url', '');
                        if (!empty($sCouponDescIcon)) {
                            $this->async()->writeSpecialPublicLog(self::PRICE_TYPE_COUPON_PAYED_RETURN);
                        }
                    }

                    $oFeatureToggle = (new ApolloV2())->featureToggle('feature_oe_newcomer_specials', $aApolloParams);
                    // 安卓需要6.2.6版本以上才显示新客特惠
                    if ($oFeatureToggle -> allow() && ((Constants\Common::DIDI_ANDROID_PASSENGER_APP == $this->_aCommonInfo['access_key_id'] && version_compare(MainDataRepo::getAppVersion(), '6.2.6') >= 0)|| Constants\Common::DIDI_IOS_PASSENGER_APP == $this->_aCommonInfo['access_key_id'])) {
                        $iPriceType = self::PRICE_TYPE_NEWCPMER_COUPON_RECALL;
                    }

                    $oFeatureToggle = (new ApolloV2())->featureToggle('feature_oe_coupon_only_label', $aApolloParams);
                    if ($oFeatureToggle -> allow()) {
                        if (!((Constants\Common::DIDI_ANDROID_PASSENGER_APP == $this->_aCommonInfo['access_key_id'] || Constants\Common::DIDI_IOS_PASSENGER_APP == $this->_aCommonInfo['access_key_id']) && version_compare(MainDataRepo::getAppVersion(), '6.1.18') < 0)) {
                            $iPriceType  = self::PRICE_TYPE_SINGLE_FOLD;
                            $sCouponDesc = Language::replaceTag(
                                $this->_aConfig['common_dec_price_desc'],
                                [
                                    'fee_amount'    => $fAmount,
                                    'currency_unit' => $this->_sCurrencyUnit,
                                ]
                            );
                            if ($this->_isReccomandStyle()) {
                                $sCouponDescIcon = $this->_aConfig['top_pope_single_fold_icon_gif'];
                            } else {
                                $sCouponDescIcon = $this->_aConfig['pope_single_fold_icon_gif'];
                            }
                        }
                    }

                    if ($this->_isLongDistanceApollo($this->_aInfo)) {
                        $iPriceType = self::PRICE_TYPE_LONG_DISTANCE_CARPOOL;
                    }
                }

                // 会员券抵扣信息
                if ('' != $sCouponCustomTag && false !== strpos($sCouponCustomTag, 'member_v3')) {
                    $aMemberIcon = Language::getDecodedTextFromDcmp('estimate_form_v3-fee_desc_member_coupon');
                    $icon        = $aMemberIcon[$sCouponCustomTag] ?? '';
                    if ('' != $icon) {
                        $sCouponDescIcon = $icon;
                        $sCouponDesc     = Language::replaceTag(
                            $aConfig['member_coupon'],
                            [
                                'num'           => $fAmount,
                                'currency_unit' => $this->_sCurrencyUnit,
                            ]
                        );
                    }
                }

                // 新召券
                if (self::COUPON_TAG_NEWLOSS == $sCouponCustomTag || self::COUPON_TAG_NEWLOSS_TASK == $sCouponCustomTag) {
                    $sCouponDescIcon = $aConfig['newloss_coupon_icon'] ?? '';
                    $sCouponDesc     = Language::replaceTag(
                        $aConfig['newloss_coupon'],
                        array('num' => $fAmount,)
                    );
                }
            }
        }

        return [$sCouponDesc, $sCouponDescIcon, $fAmount, $iPriceType];
    }


    /**
     * @param array $aDiscountInfo 折扣信息
     * @param float $amount        金额
     * @return array
     */
    private function _handlerSpecialTaxi($aDiscountInfo, $amount) {
        $fAmount     = 0;
        $sCouponDesc = '';
        if (in_array($this->_aOrderInfo['product_id'], [OrderSystem::PRODUCT_ID_UNITAXI, OrderSystem::PRODUCT_ID_BUSINESS_TAXI_CAR])) {
            foreach ($aDiscountInfo as $aItem) {
                if (!empty($aItem['type']) && !empty($aItem['amount']) && 'limittime' === $aItem['type'] && $aItem['amount'] > 0) {
                    $iLimitTimeAmount = $aItem['amount'];
                    break;
                }
            }

            $fAmount     = NumberHelper::numberFormatDisplay($amount);
            $sCouponDesc = Language::replaceTag(
                $this->_aEstimateTextDcmp['unione_special_rate_coupon_desc'],
                [
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'coupon_num'      => $fAmount,
                    'limit_time_num'  => NumberHelper::numberFormatDisplay($iLimitTimeAmount),
                    'currency_unit'   => $this->_sCurrencyUnit,
                ]
            );
        }

        return array($fAmount, $sCouponDesc);
    }

    /**
     * 是否是远途特价实验
     * @param array $aInfo apollo参数
     * @return bool
     */
    private function _isLongDistanceApollo($aInfo) {
        $aOrderInfo = $aInfo['order_info'] ?? [];
        if (BizUtils\ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTE != $aOrderInfo['product_category']) {
            return false;
        }

        if ((strategyCommon::APPOLLO_FRIEND_HIGH_TYPE == strategyCommon::getApolloType($aInfo)) || (strategyCommon::APPOLLO_PEOPEL_CAR_TYPE == strategyCommon::getApolloType($aInfo))) {
            return true;
        }

        return false;
    }

    /**
     * 特惠快车活动标签记录到public日志
     * @param int $iType 标签类型
     * @return void
     */
    public function writeSpecialPublicLog($iType) {
        $aEstimateStatistic = [
            'opera_stat_key'   => 'g_special_rate_bubble_tag',
            'app_version'      => $this->_aInfo['common_info']['app_version'] ?? '',
            'access_key_id'    => $this->_aInfo['common_info']['access_key_id'] ?? '',
            'pid'              => $this->_aInfo['passenger_info']['pid'] ?? '',
            'phone'            => $this->_aInfo['passenger_info']['phone'] ?? '',
            'estimate_id'      => $this->_aInfo['order_info']['estimate_id'] ?? '',
            'city_id'          => $this->_aInfo['order_info']['area'],
            'product_category' => PRODUCT_CATEGORY_SPECIAL_RATE,
            'combo_type'       => Constants\Horae::TYPE_COMBO_SPECIAL_RATE,
            'bubble_tag_type'  => $iType,
        ];
        PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);
    }

    /**
     * 获取出租车峰期加价灵活补
     * @return array
     */
    public function getUnionePeakDiscount() {
        if (ProductCategory::PRODUCT_CATEGORY_UNIONE != $this->_aInfo['order_info']['product_category']) {
            return [];
        }

        $sPriceDescIcon = '';
        $iRequireLevel  = $this->_aInfo['order_info']['require_level'];
        $sDiscount      = $this->_aInfo['bill_info']['bills'][$iRequireLevel]['fee_detail_info']['taxi_peak_discount'] ?? 0;

        $oTaxiPeakFeeClient = TaxiPeakFee::getPoolInstance([$this->_aInfo['order_info']['product_category'], $this->_aOrderInfo['area']]);
        if (!$oTaxiPeakFeeClient->getStatus()) {
            return [];
        }

        $sDiscount = $oTaxiPeakFeeClient->getStringPrice(abs((int)($sDiscount * 100)));
        $sDiscount = NumberHelper::numberFormatDisplay((float)$sDiscount, Language::getLanguage(), 2);
        if (empty($sDiscount)) {
            return [];
        }

        $sDiscountDesc = Language::replaceTag(
            $this->_aEstimateTextDcmp['special_price_taxi_dis_desc'],
            array(
                'currency_symbol' => $this->_sCurrencySymbol,
                'num'             => $sDiscount,
                'currency_unit'   => $this->_sCurrencyUnit,
            )
        );
        $iPriceType    = self::getDefaultPriceType();

        return [$sDiscountDesc, $sPriceDescIcon, $sDiscount, $iPriceType];

    }


    /**
     * 获取出租车业务车型的抵扣券信息（在端上冒泡页车型右边的预估价下的券抵扣）
     * <AUTHOR> <<EMAIL>>
     * @param bool $bIsSpecialRate $bIsSpecialRate
     * @return array
     */
    public function getUnioneCouponDescLite($bIsSpecialRate = false) {
        $aConfig       = $this->_aConfig;
        $aCouponDetail = $this->_aActivityInfo['estimate_detail'];
        $aCouponInfo   = $this->_aActivityInfo['coupon_info'];
        $aDiscountInfo = $this->_aActivityInfo['discount_desc'];

        $fAmount          = $iLimitTimeAmount = $iCouponAmount = 0;
        $sCouponDesc      = $sCouponDescIcon = '';
        $sCouponCustomTag = $aCouponInfo['activity_coupon']['custom_tag'] ?? ($aCouponInfo['default_coupon']['custom_tag'] ?? '');
        if (isset($aCouponDetail['title'], $aCouponDetail['value'])) {
            // 超值出租车
            if ($bIsSpecialRate) {
                if (in_array($this->_aOrderInfo['product_id'],[OrderSystem::PRODUCT_ID_UNITAXI, OrderSystem::PRODUCT_ID_BUSINESS_TAXI_CAR])) {
                    foreach ($aDiscountInfo as $aItem) {
                        if (!empty($aItem['type']) && !empty($aItem['amount']) && 'limittime' === $aItem['type'] && $aItem['amount'] > 0) {
                            $iLimitTimeAmount = $aItem['amount'];
                            break;
                        }

                        if (!empty($aItem['type']) && !empty($aItem['amount']) && 'coupon' === $aItem['type'] && $aItem['amount'] > 0) {
                            $iCouponAmount = $aItem['amount'];
                            break;
                        }
                    }

                    $fAmount = NumberHelper::numberFormatDisplay($aCouponDetail['amount']);
                    if ($iCouponAmount > 0 && self::CHAO_ZHI_WEEKEND == $sCouponCustomTag) {
                        list($sCouponDesc, $sCouponDescIcon) = $this->getTaxiActivityCouponTag($sCouponCustomTag, $fAmount);
                    } else {
                        $sCouponDesc = Language::replaceTag(
                            $this->_aEstimateTextDcmp['unione_special_rate_coupon_desc'],
                            [
                                'currency_symbol' => $this->_sCurrencySymbol,
                                'coupon_num'      => $fAmount,
                                'limit_time_num'  => NumberHelper::numberFormatDisplay($iLimitTimeAmount),
                                'currency_unit'   => $this->_sCurrencyUnit,
                            ]
                        );
                    }
                }
            } else {
                $fAmount     = NumberHelper::numberFormatDisplay($aCouponDetail['amount']);
                $sCouponDesc = Language::replaceTag(
                    $aConfig['coupon_title_with_brackets'],
                    array(
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'fee'             => $fAmount,
                        'currency_unit'   => $this->_sCurrencyUnit,
                        'title'           => '券已抵',  // 临时改券文案，一亿补贴后移除恢复原状
                    )
                );

                $iFinalCouponType = $this->_aActivityInfo['final_coupon_type'];
                $aDefaultCoupon   = $aCouponInfo['default_coupon'];
                $aActivityCoupon  = $aCouponInfo['activity_coupon'];
                $sCouponSuffix    = '';   // 券文案后缀 （满X元可用）
                if (1 == $iFinalCouponType
                    && !is_null($aDefaultCoupon)
                    && !is_null($aDefaultCoupon['showrule'])
                ) { // coupon的券
                    foreach ($aDefaultCoupon['showrule'] as $aRule) {
                        if (9 == $aRule['ruleid']) {
                            $sCouponSuffix = $aRule['show_rule_values'];
                        }
                    }
                } elseif (2 == $iFinalCouponType
                    && !is_null($aActivityCoupon)
                    && !is_null($aActivityCoupon['show_rule'])
                ) {  // pope的活动券
                    foreach ($aActivityCoupon['show_rule'] as $aRule) {
                        if (9 == $aRule['ruleid']) {
                            $sCouponSuffix = $aRule['show_rule_values'];
                        }
                    }
                }

                // 将文案后缀添加到文案后面
                if (strlen($sCouponSuffix) > 0) {
                    $sCouponDesc = sprintf('%s(%s)', $sCouponDesc, $sCouponSuffix);
                }

                if (self::COUPON_TAG_NEWLOSS == $sCouponCustomTag || self::COUPON_TAG_NEWLOSS_TASK == $sCouponCustomTag) {
                    $sCouponDescIcon = $this->_aConfig['newloss_coupon_icon'] ?? '';
                    $sCouponDesc     = Language::replaceTag(
                        $aConfig['newloss_coupon'],
                        array('num' => $fAmount,)
                    );
                }
            }
        }

        // 端上6.0.6版本且是打车顶导入口进来的流量，才有呼返券前的icon
        if (version_compare(MainDataRepo::getAppVersion(), '6.0.6') >= 0 && Util::isDaCheAnyCar($this->_aInfo)) {
            if (self::COUPON_TAG_POPE_CALLRETURN == $sCouponCustomTag) {
                $sCouponDescIcon = $this->_aConfig['unione_pope_callreturn_icon_png'];
            } elseif (self::COUPON_TAG_NEW_PLATFORM == $sCouponCustomTag) {
                // 新客优惠
                $sCouponDescIcon = $this->_aConfig['mpt_platform_icon'];
            } elseif (self::COUPON_TAG_NEW_CATEGORY == $sCouponCustomTag) {
                // 首单优惠
                $sCouponDescIcon = $this->_aConfig['mpt_category_icon'];
            }

            // 根据tag配置三排文案
            if (!empty($sCouponCustomTag)) {
                $aCouponTagConfig = NuwaConfig::text('estimate_new_form', 'double_line_coupon_template');
                if (!empty($aCouponTagConfig) && !empty($aCouponTagConfig[$sCouponCustomTag])) {
                    if (isset($aCouponTagConfig[$sCouponCustomTag]['icon'])) {
                        $sCouponDescIcon = $aCouponTagConfig[$sCouponCustomTag]['icon'];
                    }

                    if (isset($aCouponTagConfig[$sCouponCustomTag]['desc'])) {
                        $sCouponDesc = Language::replaceTag(
                            $aCouponTagConfig[$sCouponCustomTag]['desc'],
                            array(
                                'currency_symbol' => $this->_sCurrencySymbol,
                                'amount'          => $fAmount,
                                'currency_unit'   => $this->_sCurrencyUnit,
                            )
                        );
                    }
                }
            }
        }

        return [$sCouponDesc, $sCouponDescIcon, $fAmount, $sCouponCustomTag];
    }

    /**
     * 超值出租车优惠感知文案
     * @param float $sAmount 费用
     * @return string 优惠感知文案
     */
    public function calSpecialTaxiDiscountPerception($sAmount) {
        if ($sAmount <= 0) {
            return '';
        }

        return Language::replaceTag(
            $this->_aEstimateTextDcmp['special_price_taxi_dis_desc'],
            ['num' => $sAmount,]
        );
    }

    /**
     * 计算超值出租车优惠感知价格
     * @return string 优惠感知文案
     */
    public function calSpecialTaxiDiscountAmount() {
        $fAmount = 0.0;

        if (!empty($this->_aBillInfo['pre_total_fee']) && !empty($this->_aBillInfo['cap_price'])) {
            $fAmount = NumberHelper::numberFormatDisplay(
                (float)$this->_aBillInfo['pre_total_fee'] - (float)$this->_aBillInfo['cap_price'],
                '',
                2
            );
        }

        return (string)$fAmount;
    }

    /**
     * 获取出租车标签券
     * @param string $sCouponCustomTag 标签
     * @param string $fAmount          金额
     * @return array 标签文案
     */
    public function getTaxiActivityCouponTag($sCouponCustomTag, $fAmount) {
        $sCouponDesc      = $sCouponDescIcon = '';
        $aCouponTagConfig = NuwaConfig::text('estimate_new_form', 'three_line_coupon_template');
        if (!empty($aCouponTagConfig) && !empty($aCouponTagConfig[$sCouponCustomTag])) {
            if (isset($aCouponTagConfig[$sCouponCustomTag]['icon'])) {
                $sCouponDescIcon = $aCouponTagConfig[$sCouponCustomTag]['icon'];
            }

            if (isset($aCouponTagConfig[$sCouponCustomTag]['desc'])) {
                $sCouponDesc = Language::replaceTag(
                    $aCouponTagConfig[$sCouponCustomTag]['desc'],
                    array(
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'amount'          => $fAmount,
                        'currency_unit'   => $this->_sCurrencyUnit,
                    )
                );
            }
        }

        return [$sCouponDesc, $sCouponDescIcon];
    }

    /**
     * 慢必赔
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getFastEnsurePriceDesc($aPriceDesc, $aPriceDescIcon) {

        $sFastEnsureDesc = $this->_aActivityInfo['fast_ensure_detail']['detail']['title'] ?? '';
        if (!empty($sFastEnsureDesc)) {
            $aPriceDesc[]     = $sFastEnsureDesc;
            $aPriceDescIcon[] = '';
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 豪华车司务员
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getLuxuryDesignedDriverInfo($aPriceDesc, $aPriceDescIcon) {
        list($sPriceDesc,$sPriceDescIcon, $fValue) = $this->getLuxuryDesignedDriverInfoLite();
        if (!empty($sPriceDesc)) {
            $aPriceDesc[]     = $sPriceDesc;
            $aPriceDescIcon[] = $sPriceDescIcon;
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * @return array
     */
    public function getLuxuryDesignedDriverInfoLite() {
        $sPriceDesc = $sPriceDescIcon = '';
        if ((new \Xiaoju\Apollo\Apollo())->featureToggle('gs_luxury_designated_driver_text', $this->_aOrderInfo)->allow()) {
            if (\BizLib\Utils\Product::PRODUCT_ID_FIRST_CLASS_CAR == $this->_aOrderInfo['product_id']
                && ((int)($this->_aOrderInfo['designated_driver'])
                || (int)($this->_aOrderInfo['designated_driver_tag']))
            ) {
                $fDesignatedDriverFee = $this->_aBillInfo['designated_driver_fee'] ?? 0;
                // 1. $fDesignatedDriverFee > 0 含选司务员优惠价 {{$fDesignatedDriverFee}} 元
                // 2. $fDesignatedDriverFee = 0 含选司务员优惠价 {{$fDesignatedDriverFee}} 元 (原价20元)
                $iOriginPrice = 20;

                $sDesignatedDriverPriceDescKey = $fDesignatedDriverFee <= 0 ? 'designated_driver_discount_price_desc' : 'designated_driver_price_desc';
                $sPriceDesc = ' ' . Language::replaceTag(
                    $this->_aConfig[$sDesignatedDriverPriceDescKey],
                    array(
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'discount_fee'    => NumberHelper::numberFormatDisplay($fDesignatedDriverFee),
                        'origin_fee'      => NumberHelper::numberFormatDisplay($iOriginPrice),
                        'currency_unit'   => $this->_sCurrencyUnit,
                    )
                );
            }
        }

        return [$sPriceDesc, $sPriceDescIcon, abs($fDesignatedDriverFee ?? 0)];
    }

    /**
     * 高速费
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getHighWayFeeDesc($aPriceDesc, $aPriceDescIcon) {
        $iHighwayFee = (float)($this->_aBillInfo['highway_fee']);
        if ($iHighwayFee > 0) {
            $aPriceDesc[]     = Language::replaceTag(
                $this->_aConfig['highway_fee'],
                [
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'highway_fee'     => NumberHelper::numberFormatDisplay($iHighwayFee),
                    'currency_unit'   => $this->_sCurrencyUnit,
                ]
            );
            $aPriceDescIcon[] = '';
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 获取打车金intro_msg.
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @param int   $isFolderShow   是否有多项优惠项组合
     * @param array $aDescConfig    DescConfig
     * @return array
     */
    public function getRewardsDesc($aPriceDesc, $aPriceDescIcon, $isFolderShow, $aDescConfig = []) {
        if (!$isFolderShow) {
            list($sPriceDesc, $sPriceDescIcon, $fAmount) = $this->getRewardsDescLite($aDescConfig);
            if (!empty($sPriceDesc)) {
                $aPriceDesc[]     = $sPriceDesc;
                $aPriceDescIcon[] = $sPriceDescIcon;
            }
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 获取打车金 price_desc
     * @param array $aDescConfig $aDescConfig
     * @return array
     */
    public function getRewardsDescLite($aDescConfig = []) {
        $sPriceDesc = $sPriceDescIcon = '';
        $fAmount    = 0;
        $aConfig    = empty($aDescConfig) ? $this->_aConfig : $aDescConfig;
        $iPriceType = self::PRICE_TYPE_DISCOUNT_COMMON;

        if (!empty($this->_aDiscountInfo)) {
            foreach ($this->_aDiscountInfo as $aItem) {
                if (!empty($aItem['type']) && !empty($aItem['amount']) && 'reward' === $aItem['type'] && $aItem['amount'] > 0) {
                    $fAmount        = NumberHelper::numberFormatDisplay($aItem['amount']);
                    $sPriceDesc     = Language::replaceTag(
                        $aConfig['rewards_title'],
                        array(
                            'currency_symbol' => $this->_sCurrencySymbol,
                            'num'             => $fAmount,
                            'currency_unit'   => $this->_sCurrencyUnit,
                        )
                    );
                    $sPriceDescIcon = '';
                    break;
                }
            }
        }

        return [$sPriceDesc, $sPriceDescIcon, $fAmount, $iPriceType];
    }

    /**
     * 获取香港打车金DiDi Dollar price_desc
     * @param array $aDescConfig $aDescConfig
     * @return array
     */
    public function getHKBonusDescLite($aDescConfig = []) {
        $sPriceDesc = $sPriceDescIcon = '';
        $fAmount    = 0;
        $aConfig    = empty($aDescConfig) ? $this->_aConfig : $aDescConfig;
        $iPriceType = self::PRICE_TYPE_DIDI_DOLLAR;

        if (!empty($this->_aDiscountInfo)) {
            foreach ($this->_aDiscountInfo as $aItem) {
                if (!empty($aItem['type']) && !empty($aItem['amount']) && 'hkbonus' === $aItem['type'] && $aItem['amount'] > 0) {
                    $fAmount        = NumberHelper::numberFormatDisplay($aItem['amount']);
                    $sPriceDesc     = Language::replaceTag(
                        $aConfig['hkbonus_title'],
                        array(
                            'currency_unit'   => $this->_sCurrencyUnit,
                            'num'             => $fAmount,
                            'currency_symbol' => $this->_sCurrencySymbol,
                        )
                    );
                    $sPriceDescIcon = '';
                    break;
                }
            }
        }

        return [$sPriceDesc, $sPriceDescIcon, $fAmount, $iPriceType];
    }

    /**
     * 获取内循环账户折扣 price_desc
     * @param array $aDescConfig $aDescConfig
     * @return array
     */
    public function getRevolvingAccountDiscountLite() {
        $sPriceDesc = $sPriceDescIcon = '';
        $fAmount    = 0;
        $aConfig    = Language::getDecodedTextFromDcmp('config_text-revolving_account_discount');
        $iPriceType = self::PRICE_TYPE_REVOLVING_ACCOUNT_DISCOUNT;

        if (!empty($this->_aDiscountInfo)) {
            foreach ($this->_aDiscountInfo as $aItem) {
                if (!empty($aItem['type']) && !empty($aItem['amount']) && 'revolvingAccountDiscount' === $aItem['type'] && $aItem['amount'] > 0) {
                    $fAmount        = NumberHelper::numberFormatDisplay($aItem['amount'],'');
                    $sPriceDesc     = Language::replaceTag(
                        $aConfig['revolving_account_discount_title'],
                        array(
                            'currency_unit'   => $this->_sCurrencyUnit,
                            'num'             => $fAmount,
                            'currency_symbol' => $this->_sCurrencySymbol,
                        )
                    );
                    $sPriceDescIcon = $aConfig['icon'];
                    break;
                }
            }
        }

        if (!empty($sPriceDesc)) {
            return [$sPriceDesc, $sPriceDescIcon, $fAmount, $iPriceType];
        } else {
            return [];
        }
    }

    /**
     * 获取内循环账户返利 price_desc
     * @param array $aDescConfig $aDescConfig
     * @return array
     */
    public function getRevolvingAccountRebateLite() {
        $sPriceDesc = $sPriceDescIcon = '';
        $fAmount    = 0;
        $iPriceType = self::PRICE_TYPE_REVOLVING_ACCOUNT_REBATE;

        if (!empty($this->_aDiscountInfo)) {
            foreach ($this->_aDiscountInfo as $aItem) {
                if (!empty($aItem['type']) && !empty($aItem['amount']) && 'revolvingAccountRebate' === $aItem['type'] && $aItem['amount'] > 0) {
                    $activityType = $aItem['extra_info']['activity_type'];
                    $multiple = NumberHelper::numberFormatDisplay($aItem['extra_info']['multiple'], '', 1);
                    $fAmount        = NumberHelper::numberFormatDisplay($aItem['amount']);

                    if ($activityType == FeeDescInstance::POPE_ACTIVITY_N_FOLD) {
                        if ($multiple <= 1) {
                            return [];
                        }

                        $aConfig    = Language::getDecodedTextFromDcmp('config_text-revolving_account_rebate_n_fold');
                        $sPriceDesc     = Language::replaceTag(
                            $aConfig['revolving_account_rebate_title'],
                            array(
                                'currency_unit'   => $this->_sCurrencyUnit,
                                'multiple'        => \PreSale\Logics\v3Estimate\multiResponse\Component\basicFeeMsg\Util::formatMoneyDisplay($multiple),
                                'num'             => $fAmount,
                                'currency_symbol' => $this->_sCurrencySymbol,
                            )
                        );
                        $sPriceDescIcon = $aConfig['icon'];
                    } else {
                        $aConfig    = Language::getDecodedTextFromDcmp('config_text-revolving_account_rebate');
                        $sPriceDesc     = Language::replaceTag(
                            $aConfig['revolving_account_rebate_title'],
                            array(
                                'currency_unit'   => $this->_sCurrencyUnit,
                                'num'             => $fAmount,
                                'currency_symbol' => $this->_sCurrencySymbol,
                            )
                        );
                        $sPriceDescIcon = $aConfig['icon'];
                    }

                    break;
                }
            }
        }

        if (!empty($sPriceDesc)) {
            return [$sPriceDesc, $sPriceDescIcon, $fAmount, $iPriceType];
        } else {
            return [];
        }
    }

    /**
     * 获取fee_detail的减价项展示
     * @return array
     */
    public function getFeeDetailDec() {

        $sPriceDesc = $sPriceDescIcon = '';
        $fAmount    = 0;
        $aConfig    = NuwaConfig::text('config_text', 'fee_detail_template');
        $feeDescList = [];

        if (!empty($aConfig) && !empty($this->_aBillInfo['fee_detail_info'])) {
            foreach ($this->_aBillInfo['fee_detail_info'] as $sKey => $fVAmount) {
                if (empty($aConfig[$sKey])) {
                    continue;
                }

                if ($fVAmount >= 0) {
                    continue;
                }

                $sAmount = NumberHelper::numberFormatDisplay(-$fVAmount);
                $feeDescList[] = [
                    'amount' => -$fVAmount,
                    'price_desc' => Language::replaceTag(
                        $aConfig[$sKey]['price_info_desc'],
                        array(
                            'currency_unit'   => $this->_sCurrencyUnit,
                            'num'             => $sAmount,
                            'currency_symbol' => $this->_sCurrencySymbol,
                        )
                    ),
                    'price_icon' => $aConfig[$sKey]['price_info_icon'],
                ];
            }
        }

        if (count($feeDescList) == 1) {
            $fAmount = NumberHelper::numberFormatDisplay($feeDescList[0]['amount']);
            $sPriceDesc = $feeDescList[0]['price_desc'];
            $sPriceDescIcon = $feeDescList[0]['price_icon'];
        } else if (count($feeDescList) > 1) {
            $totalFee = 0;
            foreach ($feeDescList as $key => $item) {
                $totalFee += $item['amount'];
            }

            $fAmount = NumberHelper::numberFormatDisplay($totalFee);
            $sPriceDesc = Language::replaceTag(
                $aConfig['fee_detail_info_discount_merge']['price_info_desc'],
                array(
                    'currency_unit'   => $this->_sCurrencyUnit,
                    'num'             => $fAmount,
                    'currency_symbol' => $this->_sCurrencySymbol,
                )
            );
            $sPriceDescIcon = $aConfig['fee_detail_info_discount_merge']['price_info_icon'];
        }

        return [$sPriceDesc, $sPriceDescIcon, $fAmount, self::PRICE_TYPE_DEFAULT];
    }


    /**
     * 获取打车自由宝 price_desc
     *
     * @return array
     */
    public function getSpecZiYoubaoPriceInfoDesc() {
        $sPriceDesc = $sPriceDescIcon = '';
        $fAmount    = 0;
        $aConfig    = Language::getDecodedTextFromDcmp('estimate_new_form-spec_ziyoubao_price_desc');
        $iPriceType = self::PRICE_TYPE_DISCOUNT_COMMON;

        if (!empty($this->_aDiscountInfo)) {
            foreach ($this->_aDiscountInfo as $aItem) {
                if (!empty($aItem['type']) && !empty($aItem['deduction_amount']) && 'spec_ziyoubao' === $aItem['type'] && $aItem['deduction_amount'] > 0) {
                    $fAmount        = NumberHelper::numberFormatDisplay($aItem['deduction_amount']);
                    $sPriceDesc     = Language::replaceTag(
                        $aConfig['price_desc']['template'],
                        [
                            'mile'          => NumberHelper::numberFormatDisplay($aItem['deduction_mile'] / 1000.0, '', $aConfig['price_desc']['mile_pre'] ?? 0), // 精度
                            'mile_unit'     => '',
                            'num'           => NumberHelper::numberFormatDisplay($aItem['deduction_amount'] / 100.0, '' ,$aConfig['price_desc']['num_pre'] ?? 0),
                            'currency_unit' => $this->_sCurrencyUnit,
                        ]
                    );
                    $sPriceDescIcon = $aConfig['icon'];
                    break;
                }
            }
        }

        return [$sPriceDesc, $sPriceDescIcon, $fAmount, $iPriceType, $aConfig['name']];
    }

    /**
     * 获取畅行卡
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @param int   $isFolderShow   是否有多项优惠项组合
     * @param array $aDescConfig    DescConfig
     * @return array
     */
    public function getBackCardsDesc($aPriceDesc, $aPriceDescIcon, $isFolderShow, $aDescConfig = []) {
        if ($isFolderShow) {
            list($sPriceDesc, $sPriceDescIcon, $fAmount) = $this->getBackCardsDescLite($aDescConfig);
            if (!empty($sPriceDesc)) {
                $aPriceDesc[]     = $sPriceDesc;
                $aPriceDescIcon[] = $sPriceDescIcon;
            }
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 6.0获取畅行卡
     * @param array $aDescConfig $aDescConfig
     * @return array
     */
    public function getBackCardsDescLite($aDescConfig = []) {
        $iPriceType = self::PRICE_TYPE_DISCOUNT_COMMON;
        $sPriceDesc = $sPriceDescIcon = '';
        $fAmount    = 0;
        $aConfig    = empty($aDescConfig) ? $this->_aConfig : $aDescConfig;

        if (!empty($this->_aDiscountInfo)) {
            foreach ($this->_aDiscountInfo as $aItem) {
                if (!empty($aItem['type']) && !empty($aItem['amount']) && 'backcard' === $aItem['type'] && $aItem['amount'] > 0) {
                    $fAmount        = NumberHelper::numberFormatDisplay($aItem['amount']);
                    $sPriceDesc     = Language::replaceTag(
                        $aConfig['backcards_title'],
                        array(
                            'currency_symbol' => $this->_sCurrencySymbol,
                            'num'             => $fAmount,
                            'currency_unit'   => $this->_sCurrencyUnit,
                        )
                    );
                    $sPriceDescIcon = '';
                    break;
                }
            }
        }

        return [$sPriceDesc, $sPriceDescIcon, $fAmount, $iPriceType];
    }


    /**
     * 获取市民卡intro_msg.
     * @param int $isFolderShow 是否有多项优惠项组合
     * @return array
     */
    public function getCityCardsDesc($isFolderShow) {
        $sDesc = $sDescIcon = '';
        if (!$isFolderShow) {
            list($sDesc, $sDescIcon, $fAmount) = $this->getCityCardsDescLite();
        }

        return [$sDesc, $sDescIcon];
    }

    /**
     * 6.0获取市民卡
     * @return array
     */
    public function getCityCardsDescLite() {
        $fAmount    = 0;
        $sPriceDesc = $sPriceDescIcon = '';
        $iPriceType = self::PRICE_TYPE_DISCOUNT_COMMON;
        foreach ($this->_aDiscountInfo as $aItem) {
            if (!empty($aItem['type']) && !empty($aItem['amount']) && 'citycard' === $aItem['type'] && $aItem['amount'] > 0) {
                $fAmount    = NumberHelper::numberFormatDisplay($aItem['amount']);
                $sPriceDesc = Language::replaceTag(
                    $this->_aConfig['citycards_title'],
                    array(
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'num'             => $fAmount,
                        'currency_unit'   => $this->_sCurrencyUnit,
                    )
                );
                break;
            }
        }

        return [$sPriceDesc, $sPriceDescIcon , $fAmount, $iPriceType];
    }

        /**
     * 6.0获取市民卡
     * @return array
     */
    public function getTalosDiscountFeeDescLite() {
        $fAmount        = 0;
        $sPriceDesc     = $sPriceDescIcon = '';
        $aFeeDetailInfo = $this->_aBillInfo['fee_detail_info'];
        if (empty($aFeeDetailInfo)) {
            return ['', '', 0, self::PRICE_TYPE_DISCOUNT_COMMON];
        }

        $fAmount = $fAmount + abs($aFeeDetailInfo['talos_supply_demand_discount_repeat_fee']);
        $fAmount = $fAmount + abs($aFeeDetailInfo['talos_supply_demand_discount_dicount_fee']);
        if ($fAmount > 0) {
            $sPriceDesc = Language::replaceTag(
                $this->_aConfig['talos_supply_demand_discount_repeat_fee'],
                array(
                    'num' => NumberHelper::numberFormatDisplay($fAmount, '', 2),
                )
            );
        }

        return [$sPriceDesc, $sPriceDescIcon , $fAmount, self::PRICE_TYPE_DISCOUNT_COMMON];
    }


    /**
     * 获取单单省多优惠icon
     * @return string
     */
    public function getDanDanShengCombineIcon() {
        return $this->_aConfig['pope_dandansheng_combine_icon'];
    }

    /**
     * 6.0获取单单省钱卡优惠
     * @return array
     */
    public function getEconomicalCardRightDescForV2() {
        $iPriceType = self::PRICE_TYPE_DISCOUNT_COMMON;
        list($sPriceDesc, $fAmount) = $this->_getPriceDescInfoElem(self::DISCOUNT_TYPE_ECONOMICAL_CARD_RIGHT, $this->_aConfig['economical_card']);
        return [$sPriceDesc, '', $fAmount, $iPriceType];
    }

    /**
     * 5.0获取单单省钱卡优惠
     * @param array   $aPriceDesc     priceDesc
     * @param array   $aPriceDescIcon priceDescIcon
     * @param boolean $bFolderShow    bFolderShow
     * @return array
     */
    public function getEconomicalCardRightDesc($aPriceDesc, $aPriceDescIcon, $bFolderShow) {
        if ($bFolderShow) {
            return [$aPriceDesc, $aPriceDescIcon];
        }

        list($sPriceDesc, $fAmount) = $this->_getPriceDescInfoElem(self::DISCOUNT_TYPE_ECONOMICAL_CARD_RIGHT, $this->_aConfig['economical_card']);
        if (!empty($sPriceDesc)) {
            $aPriceDesc[]     = $sPriceDesc;
            $aPriceDescIcon[] = '';
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 获取网开台第三方优惠
     * @return array
     */
    public function getTripCloudDesc() {
        $sPriceDesc = $sPriceDescIcon = '';
        $fAmount    = $this->_aBillInfo['trip_cloud_discount_fee'];
        if (empty($fAmount)) {
            return ['', '', 0, self::PRICE_TYPE_DISCOUNT_COMMON];
        }

        if ($fAmount < 0) {
            $sPriceDesc = Language::replaceTag(
                $this->_aEstimateText['trip_cloud_discount'],
                array(
                    'num' => NumberHelper::numberFormatDisplay(abs($fAmount), '', 2),
                )
            );
        }

        return [$sPriceDesc, $sPriceDescIcon , $fAmount, self::PRICE_TYPE_DISCOUNT_COMMON];

    }

    /**
     * 获取价格描述元信息，抽出 getXXDesc的共同逻辑，减少代码冗余
     * @param string $sType    优惠类型
     * @param string $sTextKey 文案信息
     * @return array
     */
    private function _getPriceDescInfoElem($sType, $sTextKey) {
        $aItem = $this->_aDiscountInfoMap[$sType] ?? [];
        if (!empty($aItem['amount']) && $aItem['amount'] > 0) {
            $fAmount    = NumberHelper::numberFormatDisplay($aItem['amount']);
            $sPriceDesc = Language::replaceTag(
                $sTextKey,
                array(
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'num'             => $fAmount,
                    'currency_unit'   => $this->_sCurrencyUnit,
                )
            );
            return [$sPriceDesc, $fAmount];
        }

        return ['', 0];
    }

    /**
     * 信息费
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getInfoFeeDesc($aPriceDesc, $aPriceDescIcon) {
        list($sPriceDesc, $sPriceDescIcon, $fCost) = $this->getInfoFeeDescLite();
        if (!empty($sPriceDesc)) {
            $aPriceDesc[]     = $sPriceDesc;
            $aPriceDescIcon[] = $sPriceDescIcon;
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 信息费处理
     * @return array
     */
    public function getInfoFeeDescLite() {
        $sPriceDesc = $sPriceDescIcon = '';
        $fFee       = 0;
        foreach ($this->_aDiscountInfo as $aItem) {
            if (!empty($aItem['type']) && !empty($aItem['amount']) && 'infofee' === $aItem['type'] && $aItem['amount'] > 0) {
                $sPriceDesc = Language::replaceTag(
                    $this->_aConfig['infofee_title'],
                    array(
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'num'             => NumberHelper::numberFormatDisplay($aItem['amount']),
                        'currency_unit'   => $this->_sCurrencyUnit,
                    )
                );
                $fFee       = $aItem['amount'];
                break;
            }
        }

        return [$sPriceDesc, $sPriceDescIcon, $fFee];
    }

    /**
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @param int   $isFolderShow   isFolderShow
     * @param array $aDescConfig    aDescConfig
     * @return array
     */
    public function getActivityDiscountDesc($aPriceDesc, $aPriceDescIcon, $isFolderShow, $aDescConfig = []) {
        list($sPriceDesc, $sPriceDescIcon) = $this->getActivityDiscountDescLite($isFolderShow,$aDescConfig);
        if (!empty($sPriceDesc)) {
            $aPriceDesc[]     = $sPriceDesc;
            $aPriceDescIcon[] = $sPriceDescIcon;
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * @param int   $isFolderShow $isFolderShow
     * @param array $aDescConfig  $aDescConfig
     * @return array
     */
    public function getActivityDiscountDescLite($isFolderShow, $aDescConfig = []) {
        $sPriceDesc        = $sPriceDescIcon = '';
        $fAmount           = 0;
        $aConfig           = empty($aDescConfig) ? $this->_aConfig : $aDescConfig;
        $aActivityDiscount = $this->_aBillInfo['discounts_bill']['normal'] ?? [];
        $aDiscountsDetail  = $aActivityDiscount['discounts'][0] ?? [];

        if (!$isFolderShow && !empty($aActivityDiscount) && isset($aActivityDiscount['discounts_total']) && $aActivityDiscount['discounts_total'] < 0 && !empty($aDiscountsDetail)) {
            $sFeeName = $aDiscountsDetail['fee_name'];

            if (!empty($aDiscountsDetail['fee_display'])) {
                $sFeeDisplay = $aDiscountsDetail['fee_display'];
                $aFeeDisplay = json_decode($sFeeDisplay, true);
            }

            $sPriceDesc     = Language::replaceTag(
                $aConfig[$sFeeName] ?? $aConfig['activity_discount_title'],
                array(
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'num'             => NumberHelper::numberFormatDisplay(abs($aActivityDiscount['discounts_total'])),
                    'currency_unit'   => $this->_sCurrencyUnit,
                    'display_tag'     => $aFeeDisplay['display_tag'] ?? '',
                )
            );
            $sPriceDescIcon = '';
            $fAmount        = NumberHelper::numberFormatDisplay(abs($aActivityDiscount['discounts_total']));
        }

        return [$sPriceDesc, $sPriceDescIcon, $fAmount];
    }

    /**
     * 获取账单优惠描述文案
     * @param array $aDiscount $aDiscount
     * @return array
     */
    public function getPlutusDiscountDesc($aDiscount) {
        $iPriceType = self::PRICE_TYPE_DISCOUNT_COMMON;
        $sPriceDesc = $sPriceDescIcon = '';
        $fAmount    = -$aDiscount['fee_amount'];
        $aKeyMap    = NuwaConfig::text('config_text', 'map_key_to_msg');
        $sConfigKey = 'plutus_discount_default_msg';
        if (!empty($aKeyMap[$aDiscount['fee_name']])) {
            $sConfigKey = $aKeyMap[$aDiscount['fee_name']];
        }

        $sPriceDesc = Language::replaceTag($this->_aConfig[$sConfigKey],['fee_amount' => $fAmount]);

        return [$sPriceDesc,$sPriceDescIcon,$fAmount,$iPriceType];
    }

    /**
     * 账单类优惠费用项
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @param int   $isFolderShow   是否有多项优惠项组合
     * @return array
     */
    public function getRandomPlutusDiscountDesc($aPriceDesc, $aPriceDescIcon, $isFolderShow) {
        if (!$isFolderShow) {
            $aActivityDiscount = $this->_aBillInfo['discounts_bill']['normal'] ?? [];
            $aDiscounts        = $aActivityDiscount['discounts'] ?? [];
            if (!empty($aActivityDiscount) && isset($aActivityDiscount['discounts_total']) && $aActivityDiscount['discounts_total'] < 0 && !empty($aDiscounts)) {
                foreach ($aDiscounts as $aDiscount) {
                    if (!empty($aDiscount) && 'talos_random_discount_fee' == $aDiscount['fee_name']) {
                        $aPriceDesc[]     = Language::replaceTag(
                            $this->_aConfig['random_plutus_discount_fee_msg'],
                            ['fee_amount' => -$aDiscount['fee_amount']]
                        );
                        $aPriceDescIcon[] = '';
                    } elseif (!empty($aDiscount) && 'talos_buy_discount_fee' == $aDiscount['fee_name']) {
                        $aPriceDesc[]     = Language::replaceTag(
                            $this->_aConfig['talos_buy_discount_fee_msg'],
                            ['fee_amount' => -$aDiscount['fee_amount']]
                        );
                        $aPriceDescIcon[] = '';
                    }
                }
            }
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 获取限时特惠
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @param int   $isFolderShow   是否有多项优惠项组合
     * @param array $aDescConfig    $aDescConfig
     * @return array
     */
    public function getLimitTimeDesc($aPriceDesc, $aPriceDescIcon, $isFolderShow, $aDescConfig = []) {
        if (!$isFolderShow) {
            list($sPriceDesc, $sPriceDescIcon) = $this->getLimitTimeDescLite($aDescConfig);
            if (!empty($sPriceDesc)) {
                $aPriceDesc[]     = $sPriceDesc;
                $aPriceDescIcon[] = $sPriceDescIcon;
            }
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 6.0获取限时特惠
     * @param array $aDescConfig $aDescConfig
     * @return array
     */
    public function getLimitTimeDescLite($aDescConfig = []) {
        $sPriceDesc = $sPriceDescIcon = '';
        $fAmount    = 0;
        $aConfig    = empty($aDescConfig) ? $this->_aConfig : $aDescConfig;
        if (!empty($this->_aDiscountInfo)) {
            $iLimitTimeAmount = $iCouponAmount = 0;
            foreach ($this->_aDiscountInfo as $aItem) {
                if (!empty($aItem['type']) && !empty($aItem['amount']) && 'limittime' === $aItem['type'] && $aItem['amount'] > 0) {
                    $iLimitTimeAmount = $aItem['amount'];
                }

                if (!empty($aItem['type']) && !empty($aItem['amount']) && 'coupon' === $aItem['type'] && $aItem['amount'] > 0) {
                    $iCouponAmount = $aItem['amount'];
                }
            }

            if ($iLimitTimeAmount > 0 && $iCouponAmount > 0) {
                $fAmount        = NumberHelper::numberFormatDisplay($iCouponAmount + $iLimitTimeAmount);
                $sPriceDesc     = Language::replaceTag(
                    $aConfig['limittime_title_with_coupon'],
                    array(
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'num'             => $fAmount,
                        'currency_unit'   => $this->_sCurrencyUnit,
                    )
                );
                $sPriceDescIcon = '';
            } elseif ($iLimitTimeAmount > 0) {
                $fAmount        = NumberHelper::numberFormatDisplay($iLimitTimeAmount);
                $sPriceDesc     = Language::replaceTag(
                    $aConfig['limittime_title'],
                    array(
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'num'             => $fAmount,
                        'currency_unit'   => $this->_sCurrencyUnit,
                    )
                );
                $sPriceDescIcon = '';
            }
        }

        return [$sPriceDesc, $sPriceDescIcon, $fAmount];
    }

    /**
     * @Desc:
     * @param mixed[] Array $aPriceDesc     structure to count the elements of.
     * @param mixed[] Array $aPriceDescIcon structure to count the elements of.
     * @return array
     * @property getWaitRewardDesc $getWaitRewardDesc
     * @Author:<EMAIL>
     */
    public function getWaitRewardDesc($aPriceDesc, $aPriceDescIcon) {
        list($sPriceDesc, $sPriceDescIcon) = $this->getWaitRewardDescLite();
        if (!empty($sPriceDesc)) {
            $aPriceDesc[]     = $sPriceDesc;
            $aPriceDescIcon[] = $sPriceDescIcon;
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * @return array
     */
    public function getWaitRewardDescLite() {
        $sPriceDesc = $sPriceDescIcon = '';
        $fAmount    = 0;
        if (!\BizCommon\Utils\Horae::isLowPriceCarpoolNow($this->_aOrderInfo)) {
            return [$sPriceDesc, $sPriceDescIcon];
        }

        $fWaitRewardValue = $this->_aDisplayLines['sps_likewait_reward']['value'] ?? 0.0;
        $fWaitRewardValue = -$fWaitRewardValue;

        $fCouponValue = 0.0;
        if (!empty($this->_aDiscountInfo)) {
            foreach ($this->_aDiscountInfo as $aItem) {
                if (!empty($aItem['type']) && !empty($aItem['amount']) && 'coupon' === $aItem['type'] && $aItem['amount'] > 0) {
                    $fCouponValue = $aItem['amount'];
                }
            }
        }

        $aCouponDescTextTemplate = json_decode(Language::getTextFromDcmp('config_carpool-low_price_carpool_waitreward_show',['-']), true);

        if ($fCouponValue > 0 && $fWaitRewardValue > 0) {
            $sPriceDesc     = Language::replaceTag(
                $aCouponDescTextTemplate['with_coupon'],
                array(
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'num'             => $fWaitRewardValue + $fCouponValue,
                    'currency_unit'   => $this->_sCurrencyUnit,
                )
            );
            $sPriceDescIcon = '';
            $fAmount        = $fWaitRewardValue + $fCouponValue;
        } elseif ($fWaitRewardValue > 0) {
            $sPriceDesc     = Language::replaceTag(
                $aCouponDescTextTemplate['without_coupon'],
                array(
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'num'             => $fWaitRewardValue,
                    'currency_unit'   => $this->_sCurrencyUnit,
                )
            );
            $sPriceDescIcon = '';
            $fAmount        = $fWaitRewardValue;
        }

        return [$sPriceDesc, $sPriceDescIcon, $fAmount];
    }

    /**
     * 特价车打折信息
     * @param array   $aPriceDesc          aPriceDesc
     * @param array   $aPriceDescIcon      aPriceDescIcon
     * @param boolean $bFastCarSpecialRate bFastCarSpecialRate
     * @return array
     */
    public function getSpecialRateDiscount(array $aPriceDesc, array $aPriceDescIcon, $bFastCarSpecialRate = false) {
        // 特惠快车有其他优惠信息，不展示划线价格
        if (count($aPriceDesc) > 0 && $bFastCarSpecialRate) {
            return [$aPriceDesc, $aPriceDescIcon];
        }

        list($sPriceDesc, $sPriceDescIcon) = $this->getSpecialRateDiscountLite();
        if (!empty($sPriceDesc)) {
            $aPriceDesc[]     = $sPriceDesc;
            $aPriceDescIcon[] = $sPriceDescIcon;
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 香港出租车线上支付手续费预估价展示策略
     * @param array $aPriceDesc     $aPriceDesc
     * @param array $aPriceDescIcon $aPriceDescIcon
     * @return array
     */
    public function getHongKongTaxiCommission(array $aPriceDesc, array $aPriceDescIcon) {
        //判断支付方式,如果为线下支付,则不需要添加香港线上支付手续费策略透出
        if (empty($this->_aPaymentsInfo) || empty($this->_aPaymentsInfo['user_pay_info']) || empty($this->_aPaymentsInfo['user_pay_info']['busi_payments'])) {
            return [$aPriceDesc, $aPriceDescIcon];
        }

        foreach ($this->_aPaymentsInfo['user_pay_info']['busi_payments'] as $iIndex => $aPayInfo) {
            if (!empty($aPayInfo['isSelected']) && 1 == $aPayInfo['isSelected']) {
                $iSelectedTag = $aPayInfo['tag'];
                break;
            }
        }

        if (in_array($iSelectedTag, [Order\Order::ORDER_PAY_TYPE_CASH])) {
            return [$aPriceDesc, $aPriceDescIcon];
        }

        //如果为线上方式,判断是否有券等信息
        //1、如果没有则只显示:需要收取手续费
        //2、如果有券等信息,则后面追加:含线上支付手续费
        $aHkPriceDesc = NuwaConfig::text('hk_taxi', 'price_desc');

        if (!empty($aHkPriceDesc)) {
            if (empty($aPriceDesc)) {
                $sPriceDesc = $aHkPriceDesc['desc_price'];
            } else {
                $sPriceDesc = $aHkPriceDesc['desc_price_suffix'];
            }

            if (!empty($sPriceDesc)) {
                $aPriceDesc[] = $sPriceDesc;
            }
        }

        //如果为线上支付,但是
        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 6.0预估线上支付手续费提示
     * @return array
     */
    public function getHongKongTaxiCommissionSix() {
        $sPriceDesc = '';
        $sPriceIcon = '';

        if (\BizLib\Constants\OrderSystem::PRODUCT_ID_HK_TAXI_CAR != $this->_aOrderInfo['product_id']) {
            return [$sPriceDesc, $sPriceIcon];
        }

        //判断支付方式,如果为线下支付,则不需要添加香港线上支付手续费策略透出
        if (empty($this->_aPaymentsInfo) || empty($this->_aPaymentsInfo['user_pay_info']) || empty($this->_aPaymentsInfo['user_pay_info']['busi_payments'])) {
            return [$sPriceDesc, $sPriceIcon];
        }

        foreach ($this->_aPaymentsInfo['user_pay_info']['busi_payments'] as $iIndex => $aPayInfo) {
            if (!empty($aPayInfo['isSelected']) && 1 == $aPayInfo['isSelected']) {
                $iSelectedTag = $aPayInfo['tag'];
                break;
            }
        }

        if (in_array($iSelectedTag, [Order\Order::ORDER_PAY_TYPE_CASH])) {
            return [$sPriceDesc, $sPriceIcon];
        }

        $aHkPriceDesc = NuwaConfig::text('hk_taxi', 'price_desc');
        if (!empty($aHkPriceDesc) && !empty($aHkPriceDesc['desc_price'])) {
            $sPriceDesc = $aHkPriceDesc['desc_price'];
        }

        return [$sPriceDesc, $sPriceIcon];
    }

    /**
     * 6.0特价车打折信息
     * @return array
     */
    public function getSpecialRateDiscountLite() {
        $fAmount    = 0;
        $sPriceDesc = $sPriceDescIcon = '';
        return [$sPriceDesc, $sPriceDescIcon];
    }

    /**
     * GetFastCarSpecialRateWelfare.
     *
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getFastCarSpecialRateWelfare($aPriceDesc, $aPriceDescIcon) {
        list($sPriceDesc, $sPriceDescIcon) = $this->getFastCarSpecialRateWelfareLite();
        if (!empty($sPriceDesc)) {
            $aPriceDesc[]     = $sPriceDesc;
            $aPriceDescIcon[] = $sPriceDescIcon;
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * GetFastCarSpecialRateWelfareLite.
     *
     * @return array
     */
    public function getFastCarSpecialRateWelfareLite() {
        $sPriceDesc = $sPriceDescIcon = '';
        if (isset($this->_aBillInfo)) {
            $fAmount = MainDataRepo::getFastCarSpecialRateWelfareFee();
            if ($fAmount) {
                $sPriceDesc     = Language::getTextFromDcmp('special_rate-welfare_desc', ['price' => $fAmount]);
                $sPriceDescIcon = '';
            }
        }

        return [$sPriceDesc, $sPriceDescIcon];
    }

    /**
     * 获取会员2.0溢价保护文案信息
     * @param integer $fOtherReduceAmount $fOtherReduceAmount
     * @return array
     */
    public function getMemberDynamicProtectInfo($fOtherReduceAmount = 0) {
        $fMemberProtectFee  = $this->_aBillInfo['dynamic_member_reduce'] < 0 ? -$this->_aBillInfo['dynamic_member_reduce'] : $this->_aBillInfo['dynamic_member_reduce'];
        $aDynamicPrivileges = $this->_aPassengerInfo['member_profile']['privileges']['dpa'] ?? [];
        $fAmount            = 0;
        $sPriceDesc         = $sPriceDescIcon = '';
        //存在溢价保护文案逻辑 & 动调费用
        if ($fMemberProtectFee > 0 && isset($aDynamicPrivileges['name'], $aDynamicPrivileges['frontend']['reason'])) {
            if (0 == $fOtherReduceAmount) {
                $fAmount    = $fMemberProtectFee;
                $sPriceDesc = Language::replaceTag(
                    $this->_aEstimateText['member_protect_msg_new_60_main'],
                    [
                        'priv_name'       => $aDynamicPrivileges['name'],
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'amount'          => $fAmount,
                        'currency_unit'   => $this->_sCurrencyUnit,
                    ]
                );
            } else {
                $fAmount    = $fMemberProtectFee + $fOtherReduceAmount;
                $sPriceDesc = Language::replaceTag(
                    $this->_aEstimateText['member_protect_coupon_msg_new_60_main'],
                    [
                        'priv_name'       => $aDynamicPrivileges['name'],
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'amount'          => $fAmount,
                        'currency_unit'   => $this->_sCurrencyUnit,
                    ]
                );
            }

            if ($this->isShowDynamicMemberProtectMsg() && !empty($this->_aPassengerInfo['member_profile']['level_icon'])) {
                $sPriceDescIcon = $this->_aPassengerInfo['member_profile']['level_icon'];
            }

            if (Util::isMiniApp($this->_aCommonInfo['access_key_id'])) {
                $sPriceDescIcon = '';
            }
        }

        return [$sPriceDesc, $sPriceDescIcon, $fAmount];
    }

    /**
     * 是否为专车付费会员 且免溢价权益
     * @return bool
     */
    public function isPaidMemberDpa() {
        $aDynamicPrivileges = $this->_aPassengerInfo['member_profile']['privileges']['dpa'] ?? [];
        if ('premium_paid_member' == $aDynamicPrivileges['backend']['privilege_source']) {
            return true;
        }

        return false;
    }

    /**
     * 专车付费会员Icon
     * @return bool
     */
    public function getMemberDynamicProtectIcon() {
        return $this->_aEstimateText['dynamic_icon_v2'];
    }

    /**
     * 豪华车会员溢价保护与券逻辑
     * @param array  $aPriceDesc     aPriceDesc
     * @param array  $aPriceDescIcon aPriceDescIcon
     * @param array  $aFoldDiscount  折叠优惠信息
     * @param string $sCityCardDesc  城市卡描述
     * @param bool   $bShakeSuccess  摇一摇标识
     * @param bool   $bIsSpecialRate 特价
     * @return array
     */
    public function getMemberProtectAndCouponDesc($aPriceDesc, $aPriceDescIcon, $aFoldDiscount, $sCityCardDesc = '', $bShakeSuccess = false, $bIsSpecialRate = false) {
        //专豪统一走一段逻辑渲染会员文案
        if ($this->isShowDynamicMemberProtectMsg() && in_array($this->_aOrderInfo['product_id'], [Constants\OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR, Constants\OrderSystem::PRODUCT_ID_DEFAULT])) {
            //{type=02 原价预估{{currency_symbol}}{{num}}{{currency_unit}}}
            $aPriceDesc[]     = Language::replaceTag(
                $this->_aEstimateText['origin_fee_msg_lux'],
                [
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'num'             => $this->_aBillInfo['total_fee_without_discount'],
                    'currency_unit'   => $this->_sCurrencyUnit,
                ]
            );
            $aPriceDescIcon[] = '';
            //如果触发新会员，则不渲染相关数据，数据由extra_tag字段返回
            if (MemberVersion::isNewMember()) {
                return [$aPriceDesc, $aPriceDescIcon, []];
            }

            //溢价保护文案逻辑
            $aDynamicPrivileges = $this->_aPassengerInfo['member_profile']['privileges']['dpa'] ?? [];
            $sLevelName         = $this->_aPassengerInfo['member_profile']['level_name'];
            if (isset($aDynamicPrivileges['name'], $aDynamicPrivileges['frontend']['reason'])) {
                $sDpaPrivName = $aDynamicPrivileges['name'];
                //豪华车动态加价增加显示动调价格,"已抵*元"
                $fMemberProtectFee = $this->_aBillInfo['dynamic_member_reduce'] < 0 ? -$this->_aBillInfo['dynamic_member_reduce'] : $this->_aBillInfo['dynamic_member_reduce'];
                if ($fMemberProtectFee > 0) {
                    //文案为{{level_name}}徽章{{priv_name}}已抵{{currency_symbol}}{{amount}}{{currency_unit}}
                    $sMemberProtectDesc = Language::replaceTag(
                        $this->_aEstimateText['member_protect_msg_lux'],
                        [
                            'level_name'      => $sLevelName,
                            'priv_name'       => $sDpaPrivName,
                            'currency_symbol' => $this->_sCurrencySymbol,
                            'amount'          => $fMemberProtectFee,
                            'currency_unit'   => $this->_sCurrencyUnit,
                        ]
                    );
                }

                $sMemberProtectDescIcon = $this->_aPassengerInfo['member_profile']['level_icon'];
            }

            //券逻辑
            $aCoupon = $this->_aActivityInfo['estimate_detail'] ?? [];
            if (isset($aCoupon['title'], $aCoupon['value'])) {
                $fCouponFee      = $aCoupon['amount'];
                $sCouponDesc     = Language::replaceTag(
                    $this->_aConfig['coupon_title_with_brackets'],
                    array(
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'fee'             => NumberHelper::numberFormatDisplay($aCoupon['amount']),
                        'currency_unit'   => $this->_sCurrencyUnit,
                        'title'           => $aCoupon['title'],
                    )
                );
                $sCouponDescIcon = '';
            }

            //溢价保护 + 券
            if ((float)($fCouponFee) > 0 && (float)($fMemberProtectFee) > 0) {
                $fMemberProtectCouponFee  = $fMemberProtectFee + $fCouponFee;
                $sMemberProtectCouponDesc = Language::replaceTag(
                    //文案为{{level_name}}徽章{{prive_name}}+券共抵{{currency_symbol}}{{{num}}}{{currency_unit}}
                    $this->_aEstimateText['member_protect_coupon_msg_lux'],
                    [
                        'level_name'      => $sLevelName,
                        'priv_name'       => $sDpaPrivName,
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'num'             => $fMemberProtectCouponFee,
                        'currency_unit'   => $this->_sCurrencyUnit,
                    ]
                );
                $sMemberProtectCouponDescIcon = $sMemberProtectDescIcon;
            }

            $privilegeDesc = [];
            if (!empty($sMemberProtectCouponDesc)) {
                $privilegeDesc['desc'] = $sMemberProtectCouponDesc;
                $privilegeDesc['icon'] = $sMemberProtectCouponDescIcon;
            } else {
                if (!empty($sMemberProtectDesc)) {
                    $privilegeDesc['desc'] = $sMemberProtectDesc;
                    $privilegeDesc['icon'] = $sMemberProtectDescIcon;
                }

                if (!empty($sCouponDesc)) {
                    $privilegeDesc['desc'] = $sCouponDesc;
                    $privilegeDesc['icon'] = $sCouponDescIcon;
                }
            }

            if (!empty($privilegeDesc['desc'])) {
                if (self::LUX_MEMBER_LEVEL_PILOT == $this->_aPassengerInfo['member_profile']['level_id']) {
                    $privilegeDesc['from_color'] = self::LUX_MEMBER_LEVEL_PILOT_BGCOLOR_FROM;
                    $privilegeDesc['to_color']   = self::LUX_MEMBER_LEVEL_PILOT_BGCOLOR_TO;
                    $privilegeDesc['font_color'] = self::LUX_MEMBER_LEVEL_PILOT_FONT_COLOR;
                } else {
                    $privilegeDesc['from_color'] = self::LUX_MEMBER_LEVEL_COMMANDER_BGCOLOR_FROM;
                    $privilegeDesc['to_color']   = self::LUX_MEMBER_LEVEL_COMMANDER_BGCOLOR_TO;
                    $privilegeDesc['font_color'] = self::LUX_MEMBER_LEVEL_COMMANDER_FONT_COLOR;
                }
            }

            return [$aPriceDesc, $aPriceDescIcon, $privilegeDesc];
        } else {
            //如果不是豪华车则走老逻辑
            list($aPriceDesc, $aPriceDescIcon) = $this->getDynamicPriceDesc($aPriceDesc, $aPriceDescIcon, $aFoldDiscount);
            list($aPriceDesc, $aPriceDescIcon) = $this->getCouponDesc($aPriceDesc, $aPriceDescIcon, $aFoldDiscount, $sCityCardDesc, $bShakeSuccess, $bIsSpecialRate);
            return [$aPriceDesc, $aPriceDescIcon, []];
        }

    }

    /**
     * 获取动调提示文案.
     * @param bool $bMultiPremiumEstimate 是否专车多预估
     * @return mixed|string
     */
    public function getDynamicDesc($bMultiPremiumEstimate = false) {
        list($sPriceDesc,$fAmount) = $this->getDecDynamicDesc($bMultiPremiumEstimate);
        if ('-1' === $sPriceDesc) {
            list($sPriceDesc, $aTmp) = $this->getIncDynamicDesc();
        }

        return $sPriceDesc;
    }

    /**
     * 动调减项文案
     * @param bool $bMultiPremiumEstimate $bMultiPremiumEstimate
     * @return mixed|string
     */
    public function getDecDynamicDesc($bMultiPremiumEstimate) {
        $fAmount = 0;

        //无动调
        if (isset($this->_aBillInfo['is_has_dynamic']) && !$this->_aBillInfo['is_has_dynamic']) {
            return ['',$fAmount];
        }

        if ($this->isShowDynamicMemberProtectMsg() && !$bMultiPremiumEstimate) {
            $aDynamicPrivileges = $this->_aPassengerInfo['member_profile']['privileges']['dpa'] ?? [];
            if (isset($aDynamicPrivileges['name'], $aDynamicPrivileges['frontend']['reason'])) {
                //专车动态加价增加显示动调价格,"已抵*元"
                $amount = round($this->_aBillInfo['dynamic_price_without_member_capping'] - $this->_aBillInfo['member_dynamic_capping'], 2);
                if ($amount > 0 && Constants\OrderSystem::PRODUCT_ID_DEFAULT == $this->_aOrderInfo['product_id']) {
                    $fAmount = $amount;
                    $price   = Language::replaceTag(
                        $this->_aConfig['member_dynamic_save_amount'],
                        [
                            'amount'        => $amount,
                            'currency_unit' => $this->_sCurrencyUnit,
                        ]
                    );
                }

                return [$aDynamicPrivileges['frontend']['reason'].$aDynamicPrivileges['name'].$price, $fAmount];
            }
        }

        $sDynamicDesc = '';
        if (0 == $this->_aBillInfo['dynamic_diff_price']) {
            return [$sDynamicDesc,$fAmount];
        }

        if ($this->_aBillInfo['dynamic_diff_price'] < 0) {
            if (BizUtils\Product::getProductConfig('negative_dynamic_times', $this->_aOrderInfo)) {
                return [$sDynamicDesc,$fAmount];
            }

            $fAmount = NumberHelper::numberFormatDisplay(abs($this->_aBillInfo['dynamic_diff_price']));
            $aParams = array(
                'currency_symbol' => $this->_sCurrencySymbol,
                'num'             => $fAmount,
                'currency_unit'   => $this->_sCurrencyUnit,
            );
            return [Language::replaceTag(NuwaConfig::text('config_text', 'estimate_tab_discount_title_v2'),$aParams),$fAmount];
        }

        return ['-1',$fAmount];
    }

    /**
     * 动调加项文案
     * @return mixed
     */
    public function getIncDynamicDesc() {
        if (0 == $this->_aBillInfo['dynamic_diff_price']) {
            return ['', 'extra' => ''];
        }

        if ($this->_aBillInfo['is_hit_dynamic_capping']
            && Constants\OrderSystem::ORIGIN_ID_DIDI == $this->_aCommonInfo['origin_id']
        ) {
            $sDesc = Language::replaceTag($this->_aConfig['dynamic_title_v2'], array('currency_symbol' => $this->_sCurrencySymbol, 'num' => NumberHelper::numberFormatDisplay($this->_aBillInfo['dynamic_diff_price']), 'currency_unit' => $this->_sCurrencyUnit));
            return [$sDesc, 'extra' => ['use_times' => 0, 'amount' => NumberHelper::numberFormatDisplay($this->_aBillInfo['dynamic_diff_price'])]];
        } elseif (1 == $this->_aBillInfo['dynamic_info']['if_use_times'] && $this->_aBillInfo['dynamic_info']['dynamic_times'] > 0) {
            // dynamic abtest start
            if (!\BizLib\Utils\Product::isUber($this->_aOrderInfo['product_id'])) {
                $oApolloV2      = new ApolloV2();
                $oFeatureToggle = $oApolloV2->featureToggle(
                    'dp_tixingyemianAB',
                    [
                        'key'   => $this->_aPassengerInfo['pid'],
                        'phone' => $this->_aPassengerInfo['phone'],
                        'city'  => $this->_aOrderInfo['area'],
                    ]
                );
                if ($oFeatureToggle->allow()) {
                    //实验下线需保留,return
                    if ('treatment_group' == $oFeatureToggle->getGroupName()) {
                        $sDesc = Language::replaceTag($this->_aConfig['dynamic_multiple_title'], ['num' => NumberHelper::getLocalFormatNumber($this->_aBillInfo['dynamic_info']['dynamic_times'] + 1, $this->_aOrderInfo['product_id']), ]);
                        return [$sDesc, 'extra' => ['use_times' => 1, 'amount' => $this->_aBillInfo['dynamic_info']['dynamic_times']]];
                    }
                }
            }

            // dynamic abtest end
            $sDesc = Language::replaceTag($this->_aConfig['dynamic_multiple_title_v2'], array('num' => NumberHelper::getLocalFormatNumber($this->_aBillInfo['dynamic_info']['dynamic_times'], $this->_aOrderInfo['product_id'])));
            return [$sDesc, 'extra' => ['use_times' => 1, 'amount' => $this->_aBillInfo['dynamic_info']['dynamic_times']]];
        } else {
            $sDesc = Language::replaceTag($this->_aConfig['dynamic_title_v2'], array('currency_symbol' => $this->_sCurrencySymbol, 'num' => NumberHelper::numberFormatDisplay($this->_aBillInfo['dynamic_diff_price']), 'currency_unit' => $this->_sCurrencyUnit));
            return [$sDesc, 'extra' => ['use_times' => 0, 'amount' => NumberHelper::numberFormatDisplay($this->_aBillInfo['dynamic_diff_price'])]];
        }
    }

    /**
     * 是否显示动调会员保护信息.
     * @return bool
     */
    public function isShowDynamicMemberProtectMsg() {

        $aMember = $this->_aPassengerInfo['member_profile'] ?? [];

        return $aMember && $this->_aBillInfo['member_dynamic_capping'] >= 0 && $this->_aBillInfo['is_hit_member_capping'] > 0;
    }

    /**
     * 返回 定制化服务 价格信息.
     *
     * @param array $aCustomInfo aCustomInfo
     *
     * @return array
     */
    private function _getCusItomServiceDesc($aCustomInfo) {
        if (empty($aCustomInfo) || !is_array($aCustomInfo) || !isset($aCustomInfo['items'])) {
            return ['', 0];
        }

        if (!isset($aCustomInfo['service_fee']) || 0 == $aCustomInfo['service_fee']) {
            return ['', 0];
        }

        if (empty($aCustomInfo['items']) || !is_array($aCustomInfo['items']) || !isset($aCustomInfo['items'][0]['id'])) {
            return ['', 0];
        }

        //只有一个服务的情况
        if (1 == count($aCustomInfo['items'])) {
            $aCustom = $aCustomInfo['items'][0];

            return [
                Language::replaceTag(
                    $this->_aConfig['custom_price_desc_one_v2'],
                    array(
                        'title'           => $aCustom['title'],
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'fee'             => $aCustom['cost'],
                        'currency_unit'   => $this->_sCurrencyUnit,
                    )
                ), $aCustom['cost'],
            ];
        } elseif (count($aCustomInfo['items']) > 1) { //多个服务的情况
            $fTotalCost = $aCustomInfo['service_fee'];

            return [
                Language::replaceTag(
                    $this->_aConfig['custom_price_desc_more_v2'],
                    array(
                        'currency_symbol' => $this->_sCurrencySymbol,
                        'fee'             => $fTotalCost,
                        'currency_unit'   => $this->_sCurrencyUnit,
                    )
                ), $fTotalCost,
            ];
        }

        return ['', 0];
    }

    /**
     * 展示原始非
     * @return mixed
     */
    public function getOriginFeeDesc() {
        // 增加原始费
        $bHitMemberDynamicProtect = !empty($this->_aActivityInfo['without_member_protect_fee']);
        //1. 会员保护加强展示
        $aCoupon       = $this->_aActivityInfo['estimate_detail'] ?? [];
        $bHasCoupon    = isset($aCoupon['title'], $aCoupon['value']);
        $iCouponAmount = 0;
        if ($bHasCoupon && $aCoupon['amount'] > 0) {
            $iCouponAmount = $aCoupon['amount'];
        }

        if ($bHitMemberDynamicProtect) {
            $fOriginFee     = (float)($this->_aActivityInfo['without_member_protect_fee']);
            $sOriginFeeDesc = Language::replaceTag(
                $this->_aEstimateText['origin_fee_msg'],
                [
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'num'             => sprintf('%.1f', (float)($fOriginFee + $iCouponAmount)),
                    'currency_unit'   => $this->_sCurrencyUnit,
                ]
            );
            $aPriceDescIcon = '';
        }

        //2. 非会员有券加强展示
        $aCoupon     = $this->_aActivityInfo['estimate_detail'] ?? [];
        $bHasCoupon  = isset($aCoupon['title'], $aCoupon['value']);
        $iHasDynamic = $this->_aBillInfo['is_has_dynamic'] ?? 0;
        //专车且有券,未命中动调保护
        if (!$bHitMemberDynamicProtect && !$iHasDynamic && $bHasCoupon) {
            $fOriginFee     = (float)($this->_aActivityInfo['estimate_fee'] + $aCoupon['amount']);
            $sOriginFeeDesc = Language::replaceTag(
                $this->_aEstimateText['origin_fee_msg'],
                [
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'num'             => sprintf('%.1f', (float)($fOriginFee)),
                    'currency_unit'   => $this->_sCurrencyUnit,
                ]
            );
        }

        return $sOriginFeeDesc;
    }

    /**
     * 专车新表单高速费
     * @return mixed|string
     */
    public function getPremiumHighWayFeeDesc() {
        $sHighwayFeeDesc = '';
        if (isset($this->_aBillInfo['highway_fee']) && (float)($this->_aBillInfo['highway_fee']) > 0) {
            $sHighwayFeeDesc = Language::replaceTag(
                $this->_aEstimateText['highway_fee_msg'],
                [
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'highway_fee'     => NumberHelper::numberFormatDisplay($this->_aBillInfo['highway_fee']),
                    'currency_unit'   => $this->_sCurrencyUnit,
                ]
            );
        }

        return $sHighwayFeeDesc;
    }

    /**
     * 获取小费相关文案
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @return array
     */
    public function getTipsDesc($aPriceDesc, $aPriceDescIcon) {
        list($sPriceDesc, $sPriceDescIcon, $fFee) = $this->getTipsDescLite();
        if (!empty($sPriceDesc)) {
            $aPriceDesc[]     = $sPriceDesc;
            $aPriceDescIcon[] = $sPriceDescIcon;
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * 获取小费相关文案
     * @return array
     */
    public function getTipsDescLite() {
        $sPriceDesc = $sPriceDescIcon = '';
        $fFee       = 0;
        if (!empty($this->_aActivityInfo['tip'])) {
            $sTipFeeTag = version_compare($this->_aCommonInfo['app_version'], '5.2.8') >= 0 ? $this->_aConfig['tip_fee_desc_v2'] : $this->_aConfig['tip_fee_desc'];
            $sPriceDesc = Language::replaceTag(
                $sTipFeeTag,
                array(
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'num'             => NumberHelper::numberFormatDisplay($this->_aActivityInfo['tip']),
                    'currency_unit'   => $this->_sCurrencyUnit,
                )
            );
            $fFee       = $this->_aActivityInfo['tip'];
        }

        return [$sPriceDesc, $sPriceDescIcon, $fFee];
    }

    /**
     * 专车新表单 会员溢价保护与券逻辑
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon aPriceDescIcon
     * @param bool  $bFolderShow    是否有多项优惠项组合
     * @return array
     */
    public function getPremiumMemberProtectAndCouponDesc($aPriceDesc, $aPriceDescIcon, $bFolderShow) {
        //如果触发了新会员, 则不渲染相关数据，数据由extra_tag字段返回
        if (MemberVersion::isNewMember()) {
            return [$aPriceDesc, $aPriceDescIcon];
        }

        // 会员保护
        if ($this->isShowDynamicMemberProtectMsg()) {
            $aDynamicPrivileges = $this->_aPassengerInfo['member_profile']['privileges']['dpa'] ?? [];
            if (isset($aDynamicPrivileges['name'], $aDynamicPrivileges['frontend']['reason'])) {
                $sDpaPrivName = $aDynamicPrivileges['name'];
                //专车动态加价增加显示动调价格,"已抵*元"
                //$fMemberProtectFee = round($this->_aBillInfo['dynamic_price_without_member_capping'] - $this->_aBillInfo['member_dynamic_capping'], 2);
                //溢价保护专豪统一使用dynamic_member_reduce字段
                $fMemberProtectFee = $this->_aBillInfo['dynamic_member_reduce'] < 0 ? -$this->_aBillInfo['dynamic_member_reduce'] : $this->_aBillInfo['dynamic_member_reduce'];
                if ($fMemberProtectFee > 0) {
                    $sMemberProtectDesc = Language::replaceTag(
                        $this->_aEstimateText['member_protect_msg'],
                        [
                            'priv_name'       => $sDpaPrivName,
                            'currency_symbol' => $this->_sCurrencySymbol,
                            'amount'          => $fMemberProtectFee,
                            'currency_unit'   => $this->_sCurrencyUnit,
                        ]
                    );
                }

                $sMemberProtectDescIcon = $this->_aPassengerInfo['member_profile']['level_icon'];
            }
        }

        //券
        $aCoupon = $this->_aActivityInfo['estimate_detail'] ?? [];
        if (isset($aCoupon['title'], $aCoupon['value']) && !$bFolderShow) {
            $fCouponFee      = $aCoupon['amount'];
            $sCouponDesc     = Language::replaceTag(
                $this->_aConfig['coupon_title_with_brackets'],
                array(
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'fee'             => NumberHelper::numberFormatDisplay($aCoupon['amount']),
                    'currency_unit'   => $this->_sCurrencyUnit,
                    'title'           => $aCoupon['title'],
                )
            );
            $sCouponDescIcon = '';
        }

        //溢价保护 + 券
        if ((float)($fCouponFee) > 0 && (float)($fMemberProtectFee) > 0) {
            $fMemberProtectCouponFee      = $fMemberProtectFee + $fCouponFee;
            $sMemberProtectCouponDesc     = Language::replaceTag(
                $this->_aEstimateText['member_protect_coupon_msg'],
                [
                    'priv_name'       => $sDpaPrivName,
                    'currency_symbol' => $this->_sCurrencySymbol,
                    'num'             => $fMemberProtectCouponFee,
                    'currency_unit'   => $this->_sCurrencyUnit,
                ]
            );
            $sMemberProtectCouponDescIcon = $sMemberProtectDescIcon;
        }

        if (!empty($sMemberProtectCouponDesc)) {
            $aPriceDesc[]     = $sMemberProtectCouponDesc;
            $aPriceDescIcon[] = $sMemberProtectCouponDescIcon;
        } else {
            if (!empty($sMemberProtectDesc)) {
                $aPriceDesc[]     = $sMemberProtectDesc;
                $aPriceDescIcon[] = $sMemberProtectDescIcon;
            }

            if (!empty($sCouponDesc)) {
                $aPriceDesc[]     = $sCouponDesc;
                $aPriceDescIcon[] = $sCouponDescIcon;
            }
        }

        return [$aPriceDesc, $aPriceDescIcon];
    }

    /**
     * @param array $aPriceDesc     aPriceDesc
     * @param array $aPriceDescIcon $aPriceDescIcon
     * @param array $aPriceInfoList $aPriceInfoList
     * @return array
     */
    public function getUnioneEstimatePriceDesc($aPriceDesc, $aPriceDescIcon, $aPriceInfoList) {
        if (Util::checkUnioneShowFeeInfo($this->_aInfo) || Util::checkUnioneShowEstimateFeeAB($this->_aInfo)) {
            $sFeeMsg         = $this->_aConfig['taxi_noncarpool_estimate'];
            $aUnioneDescInfo = Util::getUnioneShowFeeInfo($this->_aInfo,$this->_sCurrencyUnit);
            $aUnioneDescInfo['content'] = $sFeeMsg;
            if (!empty($aUnioneDescInfo)) {
                $aPriceDesc[]     = $aUnioneDescInfo['content'] ?? '';
                $aPriceDescIcon[] = '';
                $aPriceInfoList[] = $aUnioneDescInfo;
            }
        }

        return [$aPriceDesc, $aPriceDescIcon, $aPriceInfoList];
    }

    /**
     * @return mixed
     */
    public function getUnioneCarpoolPriceInfoDesc() {
        // 出租车拼车开城，出租车透出价格信息。
        $aUnionePriceInfoDesc = json_decode(
            Language::getTextFromDcmp(
                'estimate_price_info_desc-unione_price',
                [
                    'fee'           => NumberHelper::numberFormatDisplay($this->_aActivityInfo['estimate_fee']),
                    'currency_unit' => $this->_sCurrencyUnit,
                ]
            )
        );
        return $aUnionePriceInfoDesc;
    }

    /**
     * 优选出租车开城，透出价格信息。
     * @return bool|mixed|null
     */
    public function getUnioneYouxuanPriceInfoDesc() {
        $fPrice = $this->_aActivityInfo['estimate_fee'] ?? $this->_aBillInfo['total_fee'];
        $aUnioneYouxuanPriceInfoDesc = NuwaConfig::text(
            'youxuan_taxi',
            'price_desc_info_new',
            [
                'fee'           => NumberHelper::numberFormatDisplay($fPrice),
                'currency_unit' => $this->_sCurrencyUnit,
            ]
        );
        return $aUnioneYouxuanPriceInfoDesc;
    }

    /**
     * 获取orderInfo
     * @return array|mixed
     */
    public function getOrderInfo() {
        return $this->_aOrderInfo;
    }

    /**
     * 获取文案
     * @return bool|mixed|null
     */
    public function getConfig() {
        return $this->_aConfig;
    }

    /**
     * 获取总抵扣优惠
     * @param float $fAmount $fAmount
     * @return mixed
     */
    public function getDecreaseAmountDesc($fAmount) {
        return Language::replaceTag(
            $this->_aConfig['discount_title'],
            [
                'currency_symbol' => $this->_sCurrencySymbol,
                'num'             => $fAmount,
                'currency_unit'   => $this->_sCurrencyUnit,
            ]
        );
    }

    /**
     * 获取减券的文案
     * @param string $iAmount 价格
     * @return string
     */
    public function getPriceDesc($iAmount) {
        return Language::replaceTag(
            $this->_aConfig['common_dec_price_desc'],
            [
                'fee_amount'    => $iAmount,
                'currency_unit' => $this->_sCurrencyUnit,
            ]
        );
    }

    /**
     * 获取多个价格时的price_Type
     * @return int
     */
    public static function getSumDecreasePriceType() {
        return self::PRICE_TYPE_DISCOUNT_COMMON;
    }

    /**
     * 获取默认的价格类型
     * @return int
     */
    public static function getDefaultPriceType() {
        return self::PRICE_TYPE_DEFAULT;
    }

    /**
     * 获取出租车峰期加价
     * @return int
     */
    public static function getTaxiPeakFeePriceType() {
        return self::PRICE_TYPE_TAXI_PEAK_FEE;
    }

    /**
     * 获取出租车节日附加费
     * @return int
     */
    public static function getTaxiHolidayFeePriceType() {
        return self::PRICE_TYPE_TAXI_HOLIDAY_FEE;
    }

    /**
     * 获取出租车跨城费price_Type
     * @return int
     */
    public static function getTaxiCrossCityFeePriceType() {
        return self::PRICE_TYPE_TAXI_CROSS_CITY_FEE;
    }

    /**
     * 春节红包
     * @return bool
     */
    public function haveRedPacket() {
        $fRedPacketValue = $this->_aDisplayLines['red_packet']['value'] ?? 0.0;
        if ($fRedPacketValue > 0) {
            return true;
        }

        return false;
    }

    /**
     * @desc 大额优惠小金币图标
     * @return array
     */
    public function getPreferentialIcon() {
        $sCarLevel = $this->_aInfo['order_info']['require_level'];
        $aPreferentialConfigText = Config::text('config_text', 'preferential_feel_info');
        $fDynamicTotalFee        = $this->_aInfo['bill_info']['bills'][$sCarLevel]['dynamic_total_fee'];
        if (!empty($fDynamicTotalFee) && isset($this->_aActivityInfo['estimate_fee']) && !empty($aPreferentialConfigText['discount_val'])) {
            $fEstimateFee  = $this->_aActivityInfo['estimate_fee'];
            $iDiscountConf = $aPreferentialConfigText['discount_val'];
            $iDiscount     = ceil(($fEstimateFee / $fDynamicTotalFee) * 100);
            if ($iDiscount <= (int)$iDiscountConf || DecisionLogic::getInstance()->getPreferentialRecommendFlag($this->_aInfo['order_info']['product_category'])) { //小于等于8折
                if ($this->hitPreferentialApollo()) {
                    $sPriceDescIcon = $aPreferentialConfigText['gold_icon_url'] ?? '';
                    return ['left_gif' => $sPriceDescIcon, 'show_anim' => 1];
                }
            }
        }

        return [];
    }

    /**
     * @desc 命中大额优惠apollo
     * @return bool
     */
    public function hitPreferentialApollo() {
        $oApollo       = new Apollo();
        $oApolloResult = $oApollo->featureToggle(
            'preferential_feel_switch',
            [
                'key'              => $this->_aPassengerInfo['phone'],
                'phone'            => $this->_aPassengerInfo['phone'],
                'app_version'      => $this->_aCommonInfo['app_version'],
                'access_key_id'    => $this->_aCommonInfo['access_key_id'],
                'lang'             => $this->_aCommonInfo['lang'],
                'city'             => $this->_aOrderInfo['area'],
                'open_time'        => date('H:i'),
                'product_category' => $this->_aOrderInfo['product_category'],
                'week'             => date('w'),
            ]
        );

        if ($oApolloResult->allow() && $this->showPreferential() && DecisionLogic::getInstance()->getCapacityBalanceFlag()) {
            return true;
        }

        return false;
    }

    /**
     * @desc 大额优惠感知实验
     * @return bool
     */
    public function showPreferential() {
        $oApollo        = new Apollo();
        $oFeatureToggle = $oApollo->featureToggle(
            'youhuiganzhi_experience',
            array(
                Apollo::APOLLO_INDIVIDUAL_ID => $this->_aPassengerInfo['pid'],
                'key'                        => $this->_aPassengerInfo['pid'],
                'phone'                      => $this->_aPassengerInfo['phone'],
                'lang'                       => $this->_aCommonInfo['lang'],
                'city'                       => $this->_aOrderInfo['area'],
                'open_time'                  => date('H:i'),
                'week'                       => date('w'),
            )
        );
        if ($oFeatureToggle->allow() && 'control_group' == $oFeatureToggle->getGroupName()) {
            return true;
        }

        return false;
    }

    /**
     * @desc 特惠下车福利金.
     * @return array
     */
    public function getPopeSpecialRateWelfare() {
        if (isset($this->_aBillInfo)) {
            $fAmount = MainDataRepo::getFastCarSpecialRateWelfareFee();
            if ($fAmount) {
                $aDcmpConf      = NuwaConfig::text('estimate_new_form', 'estimate_form_info');
                $sRewardDesc    = $aDcmpConf['reward_desc'] ?? '';
                $sPriceDesc     = Language::replaceTag($sRewardDesc, ['reward_amount' => $fAmount]);
                $sPriceDescIcon = $aDcmpConf['reward_icon'] ?? '';
                return [$sPriceDesc, $sPriceDescIcon, $fAmount];
            }
        }

        return [];
    }

    /**
     * @desc 快车打专车权益
     * @return array
     */
    public function getPricePrivilegeDescLite() {
        $sPriceDesc = $sPriceDescIcon = '';
        if (OrderNTuple::PRICE_PRIVILEGE_TYPE_FAST_UP_DEFAULT != $this->_aBillInfo['price_privilege_type']) {
            return [$sPriceDesc, $sPriceDescIcon];
        }

        // 是否展示快车打专车权益
        $aApolloParams = [
            'key'           => $this->_aInfo['passenger_info']['pid'],
            'passenger_id'  => $this->_aInfo['passenger_info']['pid'],
            'city'          => $this->_aInfo['order_info']['area'],
            'access_key_id' => $this->_aInfo['common_info']['access_key_id'],
            'lang'          => $this->_aInfo['common_info']['lang'],
            'require_level' => $this->_aInfo['order_info']['require_level'],
            'product_id'    => $this->_aInfo['order_info']['product_id'],
            'type'          => $this->_aInfo['order_info']['order_type'],
            'from'          => PricePrivilegeLogic::FROM_PRE_PRICE_INFO_DESC,
        ];

        if (Apollo::getInstance()->featureToggle(PricePrivilegeLogic::APOLLO_SHOW_SWITCH, $aApolloParams)->allow()) {
            $sPriceDesc = $this->_aEstimateTextDcmp['price_privilege_desc'];
        }

        return [$sPriceDesc, $sPriceDescIcon];
    }

    /**
     * 是否是置顶样式
     * @return bool
     */
    private function _isReccomandStyle() {
        if (!Util::isDaCheAnyCar($this->_aInfo)) {
            return false;
        }

        if ($this->conflictWithGuide()) {
            return false;
        }

        $iProductCategory = $this->_aInfo['order_info']['product_category'];
        $aDecisionInfo    = DecisionLogic::getInstance()->getProductInfoByCategoryId($iProductCategory);
        if (empty($aDecisionInfo)) {
            $aDecisionInfo = DecisionLogic::getInstance()->getProductGuideInfoByCategoryId($iProductCategory);
        }

        if (isset($aDecisionInfo['recommend_info'])) {
            $aRecommendInfo = $aDecisionInfo['recommend_info'];
        }

        if (6 == $aRecommendInfo['show_type']) {
            return true;
        }

        return false;
    }

    /**
     * 是否是置顶样式冲突
     * @return bool
     */
    protected function conflictWithGuide() {
        $aGuideInfo = GuideInfo::getInstance()->getGuideProductInfo();
        return !empty($aGuideInfo) && $aGuideInfo['order_info']['estimate_id'] != $this->_aInfo['order_info']['estimate_id'];
    }
}
