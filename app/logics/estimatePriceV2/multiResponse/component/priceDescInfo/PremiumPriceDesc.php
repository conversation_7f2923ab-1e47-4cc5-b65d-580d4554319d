<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo;

/**
 * Class PremiumPriceDesc
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo
 * @deprecated 无用
 */
class PremiumPriceDesc
{
    /**
     * @var array
     */
    private $_aInfo;

    /**
     * PremiumPriceDesc constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
    }

    /**
     * @param array $aResponse aResponse
     * @return mixed
     */
    public function build($aResponse) {

        $aPriceDesc = $aPriceDescIcon = [];

        $oFormatter = new PriceItemFormatter($this->_aInfo);
        //费用折叠信息（优惠项大于一项时展示）
        $aFoldDiscount = $oFormatter->getShowFoldDiscountInfo();
        $bFolderShow   = !empty($aFoldDiscount['desc']);

        // 接送机展示
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getAirportOrderPriceDesc($aPriceDesc, $aPriceDescIcon);

        //estimate_fixed_fees
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->formatFeeStruct($aPriceDesc, $aPriceDescIcon);

        //原始费
        $sOriginFeeDesc = $oFormatter->getOriginFeeDesc();
        //高速费
        $sHighwayFeeDesc = $oFormatter->getPremiumHighWayFeeDesc();

        if (!empty($sOriginFeeDesc)) {
            $aPriceDesc[]     = $sOriginFeeDesc;
            $aPriceDescIcon[] = '';
        }

        if (!empty($sHighwayFeeDesc)) {  //这个逻辑有点诡异，不需要校验$aPriceDesc当前是否包涵原始费用吗？
            if (1 == count($aPriceDesc)) {
                unset($aPriceDesc);
                unset($aPriceDescIcon);
                $sHighwayFeeDesc = $sOriginFeeDesc.' '.$sHighwayFeeDesc;
            }

            $aPriceDesc[]     = $sHighwayFeeDesc;
            $aPriceDescIcon[] = '';
        }

        //跨城费
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getCrossCityFee($aPriceDesc, $aPriceDescIcon);

        //市民卡
        list($sCityCardDesc, $sCityCardDescIcon) = $oFormatter->getCityCardsDesc($bFolderShow);
        if (!empty($sCityCardDesc)) {
            $aPriceDesc[]     = $sCityCardDesc;
            $aPriceDescIcon[] = $sCityCardDescIcon;
        }

        // 专车新表单春节服务费
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getRedPacketInfo($aPriceDesc, $aPriceDescIcon);

        // 会员溢价保护与券 合并展示逻辑
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getPremiumMemberProtectAndCouponDesc($aPriceDesc, $aPriceDescIcon, $bFolderShow);

        // 小费
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getTipsDesc($aPriceDesc, $aPriceDescIcon);

        // 豪华车司服
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getLuxuryDesignedDriverInfo($aPriceDesc, $aPriceDescIcon);

        // 打车金
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getRewardsDesc($aPriceDesc, $aPriceDescIcon, $bFolderShow);

        //账单类优惠费用项
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getRandomPlutusDiscountDesc($aPriceDesc, $aPriceDescIcon, $bFolderShow);

        //优惠项折叠信息
        if (!empty($aFoldDiscount['desc'])) {
            $aPriceDesc[]     = $aFoldDiscount['desc'];
            $aPriceDescIcon[] = $aFoldDiscount['icon'];
        }

        $aResponse['price_desc']      = implode(',', $aPriceDesc);
        $aResponse['price_desc_icon'] = implode(',', $aPriceDescIcon);
        //build price_info_desc
        $aResponse = Common::formatPriceInfoDesc($aResponse, $aPriceDesc, $aPriceDescIcon, $this->_aInfo);

        return $aResponse;
    }
}
