<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo;

use BizCom<PERSON>\Constants\OrderNTuple;
use BizCommon\Utils\Horae;
use BizLib\Utils\ProductCategory;
use BizLib\Utils\PublicLog;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Domain\Model\UIComponent\Feature\EstimateFeature;
use PreSale\Infrastructure\Util\AsyncMode\AsyncDecorator;
use PreSale\Logics\commonAbility\SpringRedPacketFormatter;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use PreSale\Logics\order\CustomedOrderLogic;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\NumberHelper;
use BizLib\Utils\Currency;
use BizLib\Constants;
use BizLib\Utils\Language;
use Xiao<PERSON>\Apollo\Apollo as ApolloV2;
use PreSale\Logics\estimatePriceV2\CarpoolCommuteCardLogic;

/**
 * Class CarpoolPriceDesc
 *
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo
 */
class CarpoolPriceDesc
{

    use AsyncDecorator;

    /**
     * @var array
     */
    private $_aInfo;

    /**
     * @var PriceItemFormatter
     */
    private $_oFormatter;

    private $_oSpringRedPacketFormatter;

    const ENABLE_PRICE_DIFF_INTER_VERSION = '6.0.10'; // 城际是否增加与快车预估价比较的版本

    const PINCHECHE_V2_SCENE_KEY_1 = 'success_1_2'; //V2表单临时用来识别的，不是长期方案
    const PINCHECHE_V2_SCENE_KEY_2 = 'success_2_1';

    /**
     * CarpoolPriceDesc constructor.
     *
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo      = $aInfo;
        $this->_oFormatter = new PriceItemFormatter($this->_aInfo);
        $this->_oSpringRedPacketFormatter = new SpringRedPacketFormatter($aInfo);
    }

    /**
     * @param  array $aResponse response
     * @return array|mixed
     */
    public function build($aResponse) {
        list($sSymbol, $sCurrencyUnit) = $this->_oFormatter->getSymbolUnit();

        $aPriceDesc = $aPriceDescIcon = [];

        // 春节服务费  优先级最高
        $sRedPacketIcon = '';
        list($sRedPacketDesc, $fRedPacketAmount) = $this->_oSpringRedPacketFormatter->getRedPacketInfoLite();

        // 折扣较多时，优惠前展示icon
        $aPreferential = $this->_oFormatter->getPreferentialIcon();

        //获取总优惠信息
        $aCarpoolDescConfig = $this->_getCarpoolPriceMsg();

        $aFoldDiscount = $this->_oFormatter->getShowFoldDiscountInfo($aCarpoolDescConfig);
        $bFolderShow   = !empty($aFoldDiscount['desc']);
        $sFoldDesc     = $aFoldDiscount['desc'] ?? '';
        $sFoldDescIcon = $aFoldDiscount['desc_icon'] ?? '';

        $aPriceDescGif = $aPriceDescAnim = [];

        list($sCityCardDesc, $sCityCardDescIcon) = $this->_oFormatter->getCityCardsDesc($bFolderShow);

        //estimate_fixed_fees
        list($aPriceDesc, $aPriceDescIcon) = $this->_oFormatter->formatFeeStruct($aPriceDesc, $aPriceDescIcon);

        //动调相关费用
        list($aPriceDesc, $aPriceDescIcon) = $this->_oFormatter->getDynamicPriceDesc($aPriceDesc, $aPriceDescIcon, $aFoldDiscount);

        //拼车跨城保险
        list($aPriceDesc, $aPriceDescIcon) = $this->_oFormatter->getInterInsurancePriceDesc($aPriceDesc, $aPriceDescIcon);

        //券(在 没有 摇一摇/市民卡的时候生效)
        list($aPriceDesc, $aPriceDescIcon) = $this->_oFormatter->getCouponDesc($aPriceDesc, $aPriceDescIcon, $aFoldDiscount, $sCityCardDesc, false, false, $aCarpoolDescConfig);
        if (!empty($aPreferential) && !empty($aPriceDesc)) {
            $iIndex = count($aPriceDesc) - 1;
            $aPriceDescGif[$iIndex]  = $aPreferential['left_gif'];
            $aPriceDescAnim[$iIndex] = $aPreferential['show_anim'];
        }

        //等待激励
        list($aPriceDesc, $aPriceDescIcon) = $this->_oFormatter->getWaitRewardDesc($aPriceDesc, $aPriceDescIcon);

        //信息费
        list($aPriceDesc, $aPriceDescIcon) = $this->_oFormatter->getInfoFeeDesc($aPriceDesc, $aPriceDescIcon);

        //打车金
        list($aPriceDesc, $aPriceDescIcon) = $this->_oFormatter->getRewardsDesc($aPriceDesc, $aPriceDescIcon, $bFolderShow, $aCarpoolDescConfig);

        //畅行卡
        list($aPriceDesc, $aPriceDescIcon) = $this->_oFormatter->getBackCardsDesc($aPriceDesc, $aPriceDescIcon, $bFolderShow, $aCarpoolDescConfig);

        //限时特惠
        list($aPriceDesc, $aPriceDescIcon) = $this->_oFormatter->getLimitTimeDesc($aPriceDesc, $aPriceDescIcon, $bFolderShow, $aCarpoolDescConfig);

        //分层定价优惠
        list($aPriceDesc, $aPriceDescIcon) = $this->_oFormatter->getActivityDiscountDesc($aPriceDesc, $aPriceDescIcon, $bFolderShow, $aCarpoolDescConfig);

        //市民卡
        if (!empty($sCityCardDesc)) {
            $aPriceDesc[]     = $sCityCardDesc;
            $aPriceDescIcon[] = $sCityCardDescIcon;
        }

        //foldDesc
        if (!empty($sFoldDesc)) {
            $aPriceDesc[]     = $sFoldDesc;
            $aPriceDescIcon[] = $sFoldDescIcon;
        }

        //有春节红包,优先级最高
        if ($fRedPacketAmount > 0) {
            $aPriceDesc     = [$sRedPacketDesc];
            $aPriceDescIcon = [$sRedPacketIcon];
        }

        $aResponse['price_desc']      = implode(',', $aPriceDesc);
        $aResponse['price_desc_icon'] = implode(',', $aPriceDescIcon);
        //处理拼车/城际拼车的特殊逻辑
        $aResponse = CustomedOrderLogic::getPriceDescInfo($this->_aInfo, $aResponse);

        //build price_info_desc
        Common::setPriceDescGif($aPriceDescGif);
        Common::setPriceDescAnim($aPriceDescAnim);
        $aResponse = Common::formatPriceInfoDesc($aResponse, $aPriceDesc, $aPriceDescIcon, $this->_aInfo);

        // 有春节红包,优先级最高
        if ($fRedPacketAmount > 0) {
            return $aResponse;
        }

        $aCarpoolDescConfig = NuwaConfig::text('config_carpool', 'dache_carpool_estimate_fee');
        $sCarLevel          = $this->_aInfo['order_info']['require_level'];
        $aBillInfo          = $this->_aInfo['bill_info']['bills'][$sCarLevel] ?? [];
        $fTotalFeeWithoutDiscount = $aBillInfo['total_fee_without_discount'] ?? 0.0;
        $fEstimateFee   = $this->_aInfo['activity_info'][0]['estimate_fee'];
        $fDiffPrice     = 0.0;
        $sFormat        = $aCarpoolDescConfig['diff_price_title'];
        $sPriceDescIcon = '';

        $aOrderInfoNTuple = $this->_aInfo['order_info']['n_tuple'];
        if (Horae::isCarpoolUnsuccRealTimePrice($aOrderInfoNTuple)) {
            $fDiffPrice = $fTotalFeeWithoutDiscount - $fEstimateFee;
        } elseif (Horae::isInterCityCarpool($aOrderInfoNTuple) && version_compare((string)($this->_aInfo['common_info']['app_version']), self::ENABLE_PRICE_DIFF_INTER_VERSION) >= 0) {
            $aConf      = \BizCommon\Logics\Carpool\RouteLogic::getRouteInfoByComboId($this->_aInfo['order_info']['area'], $this->_aInfo['order_info']['combo_id']);
            $fBaseFee   = $aConf['fast_car_price'] ?? 0;
            $fDiffPrice = $fBaseFee - $fEstimateFee;
            if ($fDiffPrice > 0) {
                $aPriceDesc = [
                    Language::replaceTag(
                        $sFormat,
                        [
                            'currency_symbol' => $sSymbol,
                            'fee'             => NumberHelper::numberFormatDisplay($fDiffPrice, '', 1),
                            'currency_unit'   => $sCurrencyUnit,
                        ]
                    ),
                ];
            }
        } elseif (Horae::isCarpoolFlatRate($aOrderInfoNTuple)) {
            // 区域渗透(司乘一口价)
            $fDiffPrice = $this->_getCarpoolPriceDesc(
                MainDataRepo::getFastCarEstimateFeeV2(), // 快车券后价格
                $sSymbol,
                $sCurrencyUnit,
                $aPriceDesc
            );
        }

        if (Util::isDaCheAnyCar($this->_aInfo)) {
            $oGrayToggle = (ApolloV2::getInstance())->featureToggle(
                'fastcar_carpool_price_diff_icon_gray',
                [
                    Apollo::APOLLO_INDIVIDUAL_ID => $this->_aInfo['passenger_info']['phone'],
                    'city'                       => $this->_aInfo['order_info']['area'], // 城市id
                    'phone'                      => $this->_aInfo['passenger_info']['phone'], // 人群
                    'product_category'           => $this->_aInfo['order_info']['product_category'] ?? 0, // 品类
                    'price_gap'                  => $fDiffPrice, // 快拼价差
                ]
            );
            if ($oGrayToggle->allow()) {
                // 只有拼成乐在用, 且价差需要大于10
                $sPriceDescIcon = $oGrayToggle->getParameter('img_url', '');
            }
        }

        if (round($fDiffPrice, 1) > 0.0) {
            $this->async()->writePublicLog($this->_aInfo['order_info']['product_category'], $fDiffPrice);
            $aPriceDescIcon = [$sPriceDescIcon];
        }

        if (round($fDiffPrice, 1) > 0.0 && Util::isEstimateFormDoubleLineStyle($this->_aInfo)) {
            $aDcmpConf   = NuwaConfig::text('estimate_new_form', 'estimate_form_info');
            $sCommonDesc = $aDcmpConf['common_dec_price_desc'] ?? '';
            $sDesc       = Language::replaceTag( // 修复lint
                $sCommonDesc,
                [
                    'fee_amount'    => NumberHelper::numberFormatDisplay($fDiffPrice, '', 1),
                    'currency_unit' => $sCurrencyUnit,
                ]
            );
            $aPriceDesc  = [$sDesc];

            $aPriceDescIcon = [$aDcmpConf['common_dec_price_icon'] ?? ''];
            if (Horae::isLowPriceCarpool($this->_aInfo['order_info']['n_tuple'])) {
                $aPriceDescIcon = [$aDcmpConf['coupon_dec_price_icon'] ?? ''];
            }
        }

        if (Util::isDaCheAnyCar($this->_aInfo) && \BizCommon\Utils\Horae::isCarpoolUnSuccessFlatPrice($this->_aInfo['order_info']['n_tuple']) && MainDataRepo::isCarpoolUnSuccessFlatPriceShowAsCapPrice($this->_aInfo)) {
            list($sSource, $aPayStatus) = CarpoolCommuteCardLogic::getInstance()->getVCardInfoByEstimateID($this->_aInfo['bill_info']['estimate_id']);
            if (!empty($sSource)) {
                // 有卡
                if ('send' == $sSource) {
                    // 首次赠卡
                    $bFeatureStatus = CarpoolCommuteCardLogic::getInstance()->getSendCardFeatureByEstimateID($this->_aInfo['bill_info']['estimate_id']);
                    if (2 == $bFeatureStatus) {
                        // 流失用户
                        $sFormatConfig = Language::getDecodedTextFromDcmp('estimate_dache_anycar-carpool_price_info')['commute_card_send_loss'];
                    } else {
                        $sFormatConfig = Language::getDecodedTextFromDcmp('estimate_dache_anycar-carpool_price_info')['commute_card_send_first'];
                    }

                    array_splice($aPriceDescIcon, 0, 0,  $sFormatConfig['icon']);
                    array_splice($aPriceDesc, 0, 0, $sFormatConfig['desc']);
                } elseif ('usable_give' == $sSource) {
                    $sFormatConfig = Language::getDecodedTextFromDcmp('estimate_dache_anycar-carpool_price_info')['commute_card_usable_give'];
                    array_splice($aPriceDescIcon, 0, 0,  $sFormatConfig['icon']);
                    array_splice($aPriceDesc, 0, 0, $sFormatConfig['desc']);
                } elseif (0 == $aPayStatus) {
                    $sFormatConfig = Language::getDecodedTextFromDcmp('estimate_dache_anycar-carpool_price_info')['commute_card_send'];
                    array_splice($aPriceDescIcon, 0, 0, $sFormatConfig['icon']);
                    array_splice($aPriceDesc, 0, 0, $sFormatConfig['desc']);
                } else {
                    $sFormatConfig = Language::getDecodedTextFromDcmp('estimate_dache_anycar-carpool_price_info')['commute_card'];
                    array_splice($aPriceDescIcon, 0, 0, $sFormatConfig['icon']);
                    array_splice($aPriceDesc, 0, 0, '');
                }
            }
        }

        if (Constants\Common::DIDI_WECHAT_MINI_PROGRAM == $this->_aInfo['common_info']['access_key_id'] || Constants\Common::DIDI_ALIPAY_MINI_PROGRAM == $this->_aInfo['common_info']['access_key_id']) {
            $aResponse = Common::formatMannedVehicleExtraResponse($aResponse, $this->_aInfo);
        }

        return Common::formatPriceInfoDesc($aResponse, $aPriceDesc, $aPriceDescIcon, $this->_aInfo);
    }

    /**
     * @return array
     */
    private function _getCarpoolPriceMsg() {
        //获取总优惠信息
        $aCarpoolDescConfig = NuwaConfig::text('config_carpool', 'estimate_fee_info');
        if (Util::isDaCheAnyCar($this->_aInfo)) {
            $aCarpoolDescConfig = NuwaConfig::text('config_carpool', 'dache_carpool_estimate_fee');
        } elseif (OrderNTuple::CARPOOL_PRICE_UN_SUCC_REAL_TIME_PRICING == $this->_aInfo['order_info']['carpool_price_type'] && !MainDataRepo::isApplet()) {
            $aCarpoolDescConfig = NuwaConfig::text('config_carpool', 'dual_carpool_estimate_fee');
        }

        return $aCarpoolDescConfig;
    }

    /**
     * 拼车冒泡导流位快拼价差public日志
     *
     * @param int   $iProductCategory 品类id
     * @param float $fDiffPrice       快拼价差
     * @return void
     */
    public function writePublicLog($iProductCategory, $fDiffPrice) {
        $aEstimateStatistic = [
            'opera_stat_key'   => 'g_carpool_guide_diff_price',
            'app_version'      => $this->_aInfo['common_info']['app_version'] ?? '',
            'access_key_id'    => $this->_aInfo['common_info']['access_key_id'] ?? '',
            'pid'              => $this->_aInfo['passenger_info']['pid'] ?? '',
            'phone'            => $this->_aInfo['passenger_info']['phone'] ?? '',
            'estimate_id'      => $this->_aInfo['order_info']['estimate_id'] ?? '',
            'city_id'          => $this->_aInfo['order_info']['area'],
            'product_category' => $iProductCategory,
            'diff_price'       => $fDiffPrice,
        ];
        PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);
    }




    /**
     * @Desc 比快车省 文案生成
     * @param float  $fFlashFee     快车价格(1.快车综合预估价2.快车券后价格)
     * @param string $sSymbol       符号
     * @param string $sCurrencyUnit 单位
     * @param array  $aPriceDesc    价格描述
     * @return float
     */
    private function _getCarpoolPriceDesc($fFlashFee, $sSymbol, $sCurrencyUnit, &$aPriceDesc) {
        $fEstimateFee = $this->_aInfo['activity_info'][0]['estimate_fee'] ?? 0.0;
        $fDiffPrice   = ($fFlashFee ?? 0.0) - $fEstimateFee;
        if ($fDiffPrice > 0.0) {
            // 生成"比快车省"价格描述
            $aCarpoolDescConfig = Language::getDecodedTextFromDcmp('config_carpool-estimate_fee_discount_msg');
            $sFormat            = $aCarpoolDescConfig['cheaper_than_flash'] ?? '';
            $aPriceDesc         = [
                Language::replaceTag(
                    $sFormat,
                    [
                        'currency_symbol' => $sSymbol,
                        'price'           => NumberHelper::numberFormatDisplay($fDiffPrice, '', 2),
                        'currency_unit'   => $sCurrencyUnit,
                    ]
                ),
            ];
            return $fDiffPrice;
        }

        return 0;
    }
}
