<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo;

/**
 * Class UberPriceDesc
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo
 */
class UberPriceDesc
{
    /**
     * @var array
     */
    private $_aInfo;

    /**
     * UberPriceDesc constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
    }

    /**
     * @param array $aResponse response
     * @return mixed
     */
    public function build($aResponse) {

        $aPriceDesc = $aPriceDescIcon = [];

        $oFormatter = new PriceItemFormatter($this->_aInfo);

        //获取总优惠信息
        $aFoldDiscount = $oFormatter->getShowFoldDiscountInfo();
        $bFolderShow   = !empty($aFoldDiscount['desc']);

        list($sCityCardDesc, $sCityCardDescIcon) = $oFormatter->getCityCardsDesc($bFolderShow);
        //春节红包
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getRedPacketInfo($aPriceDesc, $aPriceDescIcon);

        //券(在 没有 摇一摇/市民卡的时候生效)
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getCouponDesc($aPriceDesc, $aPriceDescIcon, $aFoldDiscount, $sCityCardDesc, false);

        $aResponse['price_desc']      = implode(',', $aPriceDesc);
        $aResponse['price_desc_icon'] = implode(',', $aPriceDescIcon);
        //build price_info_desc
        $aResponse = Common::formatPriceInfoDesc($aResponse, $aPriceDesc, $aPriceDescIcon, $this->_aInfo);

        return $aResponse;
    }
}
