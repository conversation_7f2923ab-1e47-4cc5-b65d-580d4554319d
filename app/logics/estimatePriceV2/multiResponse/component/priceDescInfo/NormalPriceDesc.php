<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo;

use BizLib\Utils\Horae;
use BizLib\Utils\CarLevel;
use BizLib\Config as NuwaConfig;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;
use Nuwa\ApolloSDK\Apollo as NuwaApollo;
use BizLib\Log as NuwaLog;

/**
 * Class NormalPriceDesc
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo
 * @deprecated 无用
 */
class NormalPriceDesc
{
    /**
     * @var array
     */
    private $_aInfo;

    /**
     * @var bool
     */
    private $_bIsSpecialRate;

    /**
     * @var bool
     */
    private $_bIsFastCarSpecialRate;

    /**
     * NormalPriceDesc constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;

        $this->_bIsSpecialRate        = Util::isSpecialRate($aInfo);
        $this->_bIsFastCarSpecialRate = Util::isFastCarSpecialRate($aInfo);
    }

    /**
     * @param array $aResponse aResponse
     * @return mixed
     */
    public function build($aResponse) {
        $aPriceDesc = $aPriceDescIcon = $privilegeDesc = $aPriceInfoList = [];

        $oFormatter = new PriceItemFormatter($this->_aInfo);

        //获取总优惠信息
        $aFoldDiscount = $oFormatter->getShowFoldDiscountInfo();
        $bFolderShow   = !empty($aFoldDiscount['desc']);
        $sFoldDesc     = $aFoldDiscount['desc'] ?? '';
        $sFoldDescIcon = $aFoldDiscount['desc_icon'] ?? '';

        list($sCityCardDesc, $sCityCardDescIcon) = $oFormatter->getCityCardsDesc($bFolderShow);
        //出租车价格信息放在第一位
        list($aPriceDesc, $aPriceDescIcon, $aPriceInfoList) = $oFormatter->getUnioneEstimatePriceDesc($aPriceDesc, $aPriceDescIcon, $aPriceInfoList);

        //春节红包
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getRedPacketInfo($aPriceDesc, $aPriceDescIcon);

        //跨城费
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getCrossCityFee($aPriceDesc, $aPriceDescIcon);

        //接送机一口价
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getAirportOrderPriceDesc($aPriceDesc, $aPriceDescIcon);

        //estimate_fixed_fees
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->formatFeeStruct($aPriceDesc, $aPriceDescIcon);

        //定制化服务相关费用
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getCustomizedServicePriceDesc($aPriceDesc, $aPriceDescIcon);

        //多因素一口价相关费用
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getFixedPreferentialPriceDesc($aPriceDesc, $aPriceDescIcon, $aFoldDiscount);

        //动调相关费用和券
        list($aPriceDesc, $aPriceDescIcon, $privilegeDesc) = $oFormatter->getMemberProtectAndCouponDesc($aPriceDesc, $aPriceDescIcon, $aFoldDiscount, $sCityCardDesc, false, $this->_bIsSpecialRate);

        //慢必赔
        if (!$this->_bIsSpecialRate) {
            list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getFastEnsurePriceDesc($aPriceDesc, $aPriceDescIcon);
        }

        //豪华车司服信息
        if (!$this->_bIsSpecialRate) {
            list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getLuxuryDesignedDriverInfo($aPriceDesc, $aPriceDescIcon);
        }

        //高速费
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getHighWayFeeDesc($aPriceDesc, $aPriceDescIcon);

        //信息费
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getInfoFeeDesc($aPriceDesc, $aPriceDescIcon);

        //打车金
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getRewardsDesc($aPriceDesc, $aPriceDescIcon, $bFolderShow);

        // 省钱卡
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getEconomicalCardRightDesc($aPriceDesc, $aPriceDescIcon, $bFolderShow);

        //畅行卡
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getBackCardsDesc($aPriceDesc, $aPriceDescIcon, $bFolderShow);

        //限时特惠，特惠出租车 司机愿减
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getLimitTimeDesc($aPriceDesc, $aPriceDescIcon, $bFolderShow);

        //账单类优惠费用项
        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getRandomPlutusDiscountDesc($aPriceDesc, $aPriceDescIcon, $bFolderShow);

        //市民卡
        if (!empty($sCityCardDesc)) {
            $aPriceDesc[]     = $sCityCardDesc;
            $aPriceDescIcon[] = $sCityCardDescIcon;
        }

        //foldDesc
        if (!empty($sFoldDesc)) {
            $aPriceDesc[]     = $sFoldDesc;
            $aPriceDescIcon[] = $sFoldDescIcon;
        }

        // 快车特惠快车福利金
        if ($this->_bIsFastCarSpecialRate) {
            list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getFastCarSpecialRateWelfare($aPriceDesc, $aPriceDescIcon);
        }

        //特价车打折信息
        if ($this->_bIsSpecialRate) {
            list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getSpecialRateDiscount($aPriceDesc, $aPriceDescIcon, $this->_bIsFastCarSpecialRate);
        }

        $oApollo = new NuwaApollo();
        if ($oApollo->featureToggle(
            'hk_taxi_commission',
            [
                'key'           => $this->_aInfo['passenger_info']['phone'],
                'phone'         => $this->_aInfo['passenger_info']['phone'],
                'city'          => $this->_aInfo['order_info']['area'],
                'product_id'    => $this->_aInfo['order_info']['product_id'],
                'car_level'     => $this->_aInfo['order_info']['require_level'],
                'lang'          => $this->_aInfo['common_info']['lang'],
                'app_version'   => $this->_aInfo['common_info']['app_version'],
                'access_key_id' => $this->_aInfo['common_info']['access_key_id'],
                'combo_type'    => $this->_aInfo['order_info']['combo_type'],
            ]
        )->allow()
        ) {
            //香港出租车线上支付手续费
            list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getHongKongTaxiCommission($aPriceDesc, $aPriceDescIcon);
        }

        $aResponse['price_desc']      = implode(',', $aPriceDesc);
        $aResponse['price_desc_icon'] = implode(',', $aPriceDescIcon);
        $aResponse['privilege_desc']  = $privilegeDesc;

        $aResponse = Common::formatPriceInfoDesc($aResponse, $aPriceDesc, $aPriceDescIcon, $this->_aInfo, $aPriceInfoList);

        return $aResponse;
    }
}
