<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo;

use BizLib\Utils\Product;
use BizLib\Utils\UtilHelper;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;
use PreSale\Logics\estimatePriceV2\ParamsLogic;
use BizCommon\Utils\Horae;
use BizLib\Utils\ProductCategory;

/**
 * 费用信息相关字段
 * price_desc price_desc_icon price_info_desc
 * Class Handler
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo
 */
class Handler
{
    /**
     * @param array $aInfo aInfo
     * @return CarpoolLowPrice|CarpoolPriceDesc|DiXiaoDiPriceDesc|DoubleLinePriceDesc|HKCapTaxiPriceDesc|NormalPriceDescV2|SpecialRatePriceDesc|TaxiPriceDesc
     */
    public static function select($aInfo) {
        // 车大联盟暂时不用优惠信息展示
        if (Util::isSpaciousCarAlliance($aInfo)) {
            return null;
        }

        // 香港一口价出租
        if (ProductCategory::PRODUCT_CATEGORY_HK_CAP_TAXI == $aInfo['order_info']['product_category']) {
            return new HKCapTaxiPriceDesc($aInfo);
        }

        // 对出租车业务下的各个品类进行独立处理
        if (Util::isUnione($aInfo)) {
            return new TaxiPriceDesc($aInfo);
        }

        if ((Util::isEstimateFormDoubleLineStyle($aInfo) || Util::isRecommendGroup($aInfo)) && !Horae::isLowPriceCarpool($aInfo['order_info']['n_tuple'])) {
            return new DoubleLinePriceDesc($aInfo);
        }

        // 单独处理拼成乐
        if (Horae::isLowPriceCarpool($aInfo['order_info']['n_tuple'])) {
            return new CarpoolLowPrice($aInfo);
        }

        if (Util::isCarpool($aInfo)) {
            return new CarpoolPriceDesc($aInfo);
        } elseif (\BizCommon\Utils\Common::isRegionalWeChatMiniProgram($aInfo['common_info']['access_key_id'])) {
            return new DiXiaoDiPriceDesc($aInfo);
        } elseif (Util::isFastCarSpecialRate($aInfo)) {
            return new SpecialRatePriceDesc($aInfo);
        }

        return new NormalPriceDescV2($aInfo);
    }
}
