<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo;

use BizCommon\Utils\Horae;
use BizLib\Config as NuwaConfig;
use BizLib\Constants;
use BizLib\Constants\OrderSystem;
use BizLib\Utils\Currency;
use BizLib\Utils\Language;
use BizLib\Utils\NumberHelper;
use BizLib\Utils\ProductCategory;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\commonAbility\SpringRedPacketFormatter;
use PreSale\Logics\estimatePriceV2\DecisionLogic;
use PreSale\Logics\estimatePriceV2\multiResponse\component\athenaGuideInfo\Common as guideCommon;
use PreSale\Logics\estimatePriceV2\multiResponse\component\paymentInfo\Common as payCommon;

use PreSale\Logics\estimatePriceV2\multiResponse\component\GuideInfo;
use PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo\strategy\RecommendStrategyUtil;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;
use PreSale\Logics\estimatePriceV2\CarpoolCommuteCardLogic;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use Xiaoju\Apollo\Apollo as ApolloV2;

/**
 *
 * Copyraight (c) 2021 xiaojukeji.com, Inc. All Rights Reserved.
 * @author: <EMAIL>
 * @date  : 2021/1/1 3:59 下午
 * @desc  : 双行价格描述（设计考虑可读性、扩展性、规范）
 * @wiki  : http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=503975067
 *
 */
class DoubleLinePriceDesc
{
    // 加价费用渲染方式映射
    private static $_aIncPriceRenderMap = [
        ['icon_type' => 'fuzzy_fee', 'action' => 'getLuxuryDesignedDriverInfoLite'],
        ['icon_type' => 'fuzzy_fee', 'action' => 'getCustomizedServicePriceDescLite'],
        ['icon_type' => 'fuzzy_fee', 'action' => 'getCrossCityFeeLite'],
        ['icon_type' => 'fuzzy_fee', 'action' => 'formatFeeStructLite'],
        ['icon_type' => 'fuzzy_fee', 'action' => 'getInfoFeeDescLite'],
        ['icon_type' => 'fuzzy_fee', 'action' => 'getTipsDescLite'],
    ];

    // 减价费用渲染方式映射
    // 伪工厂, 要求实现的方法满足一定的函数签名
    // 要求和限制
    // 1. 函数必须返回一个array;
    // 2. array长度至少为3, 且第三位(索引对应为2), 必须是一个可以和 0 比较的数据
    // 3. 第四位(如果有), 必须是 PriceItemFormatter 中 PRICE_TYPE_ 中的一个
    // 4. 对图标, 文案有特殊逻辑的, 通过 icon_type 来区分
    //
    // 5. 只有第三位大于0, 返回的数据才会被暂存用于后面的渲染
    // 6. 如果一个优惠想占用多个优惠展示, 不支持
    private static $_aDecPriceRenderMap = [
        ['icon_type' => 'price_privilege', 'action' => 'getPricePrivilegeDescLite'],
        ['icon_type' => 'coupon_dec_price_icon', 'action' => 'getCouponDescLite'],
        ['icon_type' => 'reward_icon', 'action' => 'getRewardsDescLite'],
        ['icon_type' => 'common_dec_price_icon', 'action' => 'getFixedPreferentialPriceDescLite'],
        ['icon_type' => 'common_dec_price_icon', 'action' => 'getBackCardsDescLite'],
        ['icon_type' => 'common_dec_price_icon', 'action' => 'getLimitTimeDescLite'],
        ['icon_type' => 'common_dec_price_icon', 'action' => 'getCityCardsDescLite'],
        ['icon_type' => 'common_dec_price_icon', 'action' => 'getEconomicalCardRightDescForV2'],
        ['icon_type' => 'spec_ziyoubao', 'action' => 'getSpecZiYoubaoPriceInfoDesc'],
        ['icon_type' => 'hkbonus_icon', 'action' => 'getHKBonusDescLite'],
        ['icon_type' => 'common_dec_price_icon', 'action' => 'getFeeDetailDec'],
        ['icon_type' => 'common_dec_price_icon', 'action' => 'getTalosDiscountFeeDescLite'],
    ];

    private $_sCurrencyUnit;

    private $_aInfo;
    private $_aText;

    //正向费用项信息
    private $_aIncPriceItem = [];
    //负向费用项信息
    private $_aDecPriceItem = [];
    //额外福利
    private $_aExtraPriceItem = [];

    private $_aBusinessPaymentItem = [];

    //内循环账户返利
    private $_aRevolvingAccountRebatePriceItem;
    //内循环账户折扣
    private $_aRevolvingAccountDiscountPriceItem;

    /**
     * @var PriceItemFormatter
     */
    private $_oPriceItemFormatter;

    /**
     * @var SpringRedPacketFormatter
     */
    private $_oRedPacketFormatter;

    /**
     * @var array
     */
    private $_aMemberDynamicProtect = [];

    /**
     * @var array
     */
    private $_aActivityInfo;

    private $_aBillInfo = [];

    private $_bIsSpecialRate;
    private $_common = [];
    /**
     * DoubleLinePriceDesc constructor.
     * @param array $aInfo aInfo
     */
    public function __construct(array $aInfo) {
        $this->_aInfo         = $aInfo;
        $sCarLevel            = $aInfo['order_info']['require_level'];
        $this->_aBillInfo     = $aInfo['bill_info']['bills'][$sCarLevel] ?? [];
        $this->_aActivityInfo = $aInfo['activity_info'][0];
        $this->_oPriceItemFormatter = new PriceItemFormatter($aInfo);
        $this->_oRedPacketFormatter = new SpringRedPacketFormatter($aInfo);
        $this->_aText          = NuwaConfig::text('estimate_new_form', 'estimate_form_info');
        list($sSymbol, $sUnit) = Currency::getSymbolUnit($aInfo['bill_info']['currency'] ?? '', $aInfo['order_info']);
        $this->_sCurrencyUnit  = $sUnit;
        $this->_bIsSpecialRate = Util::isSpecialRate($aInfo);
        $this->_common         = new Common($aInfo);
    }

    /**
     * 构建价格描述信息
     * @param array $aResponse aResponse
     * @return array
     */
    public function build(array $aResponse) {
        //减价项
        $this->_fillDecPriceItem();
        //获取溢价保护
        $this->_getDynamicMemberProtectMsg();
        //加价项
        $this->_fillIncrPriceItem();
        //额外福利(下车返福利金)
        $this->_fillExtraPriceItem();

        //企业付信息
        $this->_buildBusinessPaymentItem();
        //内循环账户返利信息
        $this->_fillRevolvingAccountRebatePriceItem();
        //内循环账户折扣信息
        $this->_fillRevolvingAccountDiscountPriceItem();

        $aResponse = $this->_renderResponse($aResponse);
        $aResponse = Common::formatMannedVehicleExtraResponse($aResponse, $this->_aInfo);
        return $aResponse;
    }

    /**
     *  返利信息
     *  Item结构为 priceDesc priceIcon amount
     *  @return void
     */
    private function _fillRevolvingAccountRebatePriceItem() {
        //内循环账户返利信息
        $this->_aRevolvingAccountRebatePriceItem[] = $this->_oPriceItemFormatter->getRevolvingAccountRebateLite();
    }

    /**
     *  折扣信息
     *  Item结构为 priceDesc priceIcon amount
     *  @return void
     */
    private function _fillRevolvingAccountDiscountPriceItem() {
        //内循环账户返利信息
        $this->_aRevolvingAccountDiscountPriceItem[] = $this->_oPriceItemFormatter->getRevolvingAccountDiscountLite();
    }

    /**
     * 处理企业支付，eg：企-5元 在企业支付及混合支付的场景下做解释说明
     * @return void
     */
    private function _buildBusinessPaymentItem() {
        if (version_compare($this->_aInfo['common_info']['app_version'], '6.2.12') < 0) {
            return;
        }

        if (!Util::isBusinessPay($this->_aInfo)) {
            return;
        }

        // 判断拆费的场景
        $aMixedDeductInfo = $this->_aInfo['activity_info'][0]['mixed_pay_deduct_info'];
        $fEstimateFee     = $this->_aInfo['activity_info'][0]['estimate_fee'];
        $fDeductFee       = $aMixedDeductInfo['deduct_fee'] > 0 ? $aMixedDeductInfo['deduct_fee'] : $fEstimateFee;
        if ($fDeductFee <= 0) {
            return;
        }

        $this->_aBusinessPaymentItem = [
            'content' => Language::replaceTag($this->_aText['common_dec_price_desc'], ['fee_amount' => $fDeductFee, 'currency_unit' => $this->_sCurrencyUnit]),
            'icon'    => $this->_aText['business_pay_icon'],
        ];
    }

    /**
     * 填充加价费用，只关注动调，春节服务费，其他统一模糊化；
     * 有多项费用时，统一模糊化；
     * @return void
     */
    private function _fillIncrPriceItem() {
        //非溢价保护，展示动调
        if (!$this->_isMemberDynamicProtect()) {
            //动调加价
            $aData = $this->_oPriceItemFormatter->getIncDynamicDesc();
            if (!empty($aData[0])) {
                $this->_aIncPriceItem[] = [
                    'icon_type' => 'dynamic_price',
                    'data'      => $aData,
                ];
            }
        }

        //获取各加价费用项
        foreach (self::$_aIncPriceRenderMap as $aMap) {
            $sAction = $aMap['action'];
            if (method_exists($this->_oPriceItemFormatter, $sAction)) {
                $aData = $this->_oPriceItemFormatter->$sAction();
                if (!empty($aData[0]) && $aData[2] > 0) {
                    $this->_aIncPriceItem[] = [
                        'icon_type' => $aMap['icon_type'],
                        'data'      => $aData,
                    ];
                }
            }
        }

        list($sPriceDesc, $fRedPacket) = $this->_oRedPacketFormatter->getRedPacketInfoLite();
        if ($fRedPacket > 0) {
            $this->_aIncPriceItem[] = [
                'icon_type' => 'spring_red_packet',
                'data'      => [$sPriceDesc,'', $fRedPacket],
            ];
        }

    }

    /**
     * 填充减价费用
     * @return void
     */
    private function _fillDecPriceItem() {

        //获取各减价费用项
        foreach (self::$_aDecPriceRenderMap as $aMap) {
            $sAction   = $aMap['action'];
            $iIconType = $aMap['icon_type'];
            if (method_exists($this->_oPriceItemFormatter, $sAction)) {
                if ('getCouponDescLite' == $sAction) {
                    $aData = $this->_oPriceItemFormatter->$sAction([], '', false, $this->_bIsSpecialRate, []);
                } elseif ('getFixedPreferentialPriceDescLite' == $sAction) {
                    $aData = $this->_oPriceItemFormatter->$sAction([]);
                } elseif ('getHKBonusDescLite' == $sAction) {
                    $aData = $this->_oPriceItemFormatter->$sAction();
                } else {
                    $aData = $this->_oPriceItemFormatter->$sAction();
                }

                if (!empty($aData[0]) && (($aData[2] > 0) || ('getPricePrivilegeDescLite' == $sAction))) {
                    $this->_aDecPriceItem[] = [
                        'icon_type' => $iIconType,
                        'data'      => $aData,
                    ];
                }
            }
        }

        //账单类优惠费用项
        $aDiscounts = $this->_aBillInfo['discounts_bill']['normal']['discounts'] ?? [];
        if (!empty($aDiscounts)) {
            foreach ($aDiscounts as $aDiscount) {
                if (!empty($aDiscount) && isset($aDiscount['fee_name'])
                    && isset($aDiscount['fee_amount']) && $aDiscount['fee_amount'] < 0
                ) {
                    $aData = $this->_oPriceItemFormatter->getPlutusDiscountDesc($aDiscount);
                    if (!empty($aData[0]) && $aData[2] > 0) {
                        $this->_aDecPriceItem[] = [
                            'icon_type' => 'common_dec_price_icon',
                            'data'      => $aData,
                        ];
                    }
                }
            }
        }
    }

    /**
     * 渲染最终的展示项，原则：
     *  1.最终只能有一个费用项，优先级 加价 > 减价
     *  2.区分会员保护权益，有会员保护权益，优先展示减价费用
     *  3.加价项分优先级展示，不做聚合；减价项根据费用项个数，只有一个时，单独展示，多个聚合展示
     * @param array $aResponse aResponse
     * @return array
     */
    private function _renderResponse(array $aResponse) {
        $aPriceDesc   = $aPriceDescIcon = $aPriceAmount = $aPriceType = [];
        $aCouponInfo  = $this->_aActivityInfo['coupon_info'];
        $bHKBonusFlag = false;
        if ($this->_isMemberDynamicProtect()) { //溢价保护
            $aPriceDesc[]     = Language::replaceTag($this->_aText['common_dec_price_desc'], ['fee_amount' => $this->_aMemberDynamicProtect[2], 'currency_unit' => $this->_sCurrencyUnit]);
            $aPriceDescIcon[] = $this->_aMemberDynamicProtect[1];
            if (count($this->_aIncPriceItem) > 0) { //加价项
                $aIncPriceItem    = current($this->_aIncPriceItem);
                $aPriceDesc[]     = Language::replaceTag(
                    $this->_aText['common_inc_price_desc'],
                    ['fee_amount' => $aIncPriceItem['data'][2], 'currency_unit' => $this->_sCurrencyUnit]
                );
                $aPriceDescIcon[] = $this->_aText[$aIncPriceItem['icon_type']];
            } elseif (count($this->_aExtraPriceItem) > 0) { //pope福利金
                $aExtPriceItem    = current($this->_aExtraPriceItem);
                $aPriceDesc[]     = Language::replaceTag($this->_aText['reward_desc'], ['reward_amount' => $aExtPriceItem[2]]);
                $aPriceDescIcon[] = $aExtPriceItem[1];
            }
        } else {
            // 有加价费用
            $iIncItemTotal = count($this->_aIncPriceItem);
            $iDecItemTotal = count($this->_aDecPriceItem);
            if ($iIncItemTotal > 0) {
                foreach ($this->_aIncPriceItem as $k => $incItem) {
                    //最多展示2项
                    if (count($aPriceDesc) >= 2) {
                        break;
                    }

                    if ('spring_red_packet' == $incItem['icon_type']) {
                        $sDesc = SpringRedPacketFormatter::getRedPacketString($incItem['data'][2],'',$this->_sCurrencyUnit);
                        if (!empty($sDesc)) {
                            $aPriceDesc[]     = $sDesc;
                            $aPriceDescIcon[] = '';  // 春节服务费不加icon(文案需要全部透出去)
                        }
                    } elseif ('dynamic_price' == $incItem['icon_type']) {
                        if (1 == $incItem['data']['extra']['use_times']) {
                            // 使用倍数表示 +x倍
                            $aPriceDesc[] = Language::replaceTag($this->_aText['dynamic_times_price_desc'], ['fee_amount' => $incItem['data']['extra']['amount']]);
                        } else {
                            // 使用金额表示 +x元
                            $aPriceDesc[] = Language::replaceTag($this->_aText['dynamic_amount_price_desc'], ['fee_amount' => $incItem['data']['extra']['amount'], 'currency_unit' => $this->_sCurrencyUnit]);
                        }

                        $aPriceDescIcon[] = $this->_aText[$incItem['icon_type']];
                    } else {
                        $aPriceDesc[]     = Language::replaceTag(
                            $this->_aText['common_inc_price_desc'],
                            ['fee_amount' => $incItem['data'][2], 'currency_unit' => $this->_sCurrencyUnit]
                        );
                        $aPriceDescIcon[] = $this->_aText[$incItem['icon_type']];
                    }
                }
            }

            if (count($aPriceDesc) < 2 && !empty($this->_aBusinessPaymentItem)) {
                $aPriceDesc[]     = $this->_aBusinessPaymentItem['content'];
                $aPriceDescIcon[] = $this->_aBusinessPaymentItem['icon'];
            }

            // 减价费用 多个减价项
            if ($iDecItemTotal >= 2 && count($aPriceDesc) < 2) {
                foreach ($this->_aDecPriceItem as $aPriceItem) {
                    if (PriceItemFormatter::PRICE_TYPE_DIDI_DOLLAR == $aPriceItem['data'][3]) {
                        $bHKBonusFlag = true;
                    }
                }

                $iAmount      = $this->_calcDecreaseAmount();
                $fEstimateFee = $this->_aInfo['activity_info'][0]['estimate_fee'] ?? 0.0;
                $fDecPrice    = MainDataRepo::getFastCarEstimateFeeV2() - $fEstimateFee;
                if (Horae::isCarpoolFlatRate($this->_aInfo['order_info']) && $fDecPrice > 0) {
                    $aPriceDesc = $this->_getAPriceDesc($fDecPrice, $aPriceDesc);
                } elseif (!$bHKBonusFlag) {
                    $aPriceDesc[]     = $this->_getPriceDesc($iAmount);
                    $sCouponCustomTag = $aCouponInfo['activity_coupon']['custom_tag'] ?? '';

                    // 以下就算是优惠共抵也会覆盖icon  (需要和pm确认是否需要覆盖)
                    if (PriceItemFormatter::COUPON_TAG_NEW_CATEGORY == $sCouponCustomTag) {
                        // 多个减价项&&含新客标识 --> Icon全为新客
                        $aPriceDescIcon[] = $this->_aText['mpt_category_icon'];
                    } elseif (PriceItemFormatter::COUPON_TAG_NEW_PLATFORM == $sCouponCustomTag) {
                        $aPriceDescIcon[] = $this->_aText['mpt_platform_icon'];
                    } elseif (PriceItemFormatter::COUPON_TAG_PERORDERSALE == $sCouponCustomTag) {
                        // 多个减价项&单单省 --> 安卓：【惠】icon  IOS/小程序：【单单省·惠】icon
                        if (in_array($this->_aInfo['common_info']['access_key_id'], [Constants\Common::DIDI_IOS_PASSENGER_APP, Constants\Common::DIDI_WECHAT_MINI_PROGRAM, Constants\Common::DIDI_ALIPAY_MINI_PROGRAM])) {
                            $aPriceDescIcon[] = $this->_aText['pope_dandansheng_combine_icon_ios_mini'];
                        } elseif (Constants\Common::DIDI_ANDROID_PASSENGER_APP == $this->_aInfo['common_info']['access_key_id']) {
                            $aPriceDescIcon[] = $this->_aText['pope_dandansheng_combine_icon'];
                        }
                    } else {
                        $aPriceDescIcon[] = $this->_getDefaultPriceDescIcon();
                    }

                    $iDecIndex = count($aPriceDesc) - 1;
                    $aPriceAmount[$iDecIndex] = $iAmount;
                    $aPriceType[$iDecIndex]   = $this->_getSumDecreasePriceType();
                } else {
                    // 香港端券和打车金都有时，不合并，共同展示
                    foreach ($this->_aDecPriceItem as $aPriceItem) {
                        $sPriceDesc       = $this->_getPriceDesc($aPriceItem['data'][2]);
                        $aPriceDesc[]     = $sPriceDesc;
                        $aPriceDescIcon[] = $this->_getPriceDescIconByTypeAndContent($aPriceItem['icon_type'], $sPriceDesc, $aPriceItem['data'][2]);
                        $iDecIndex        = count($aPriceDesc) - 1;
                        $aPriceAmount[$iDecIndex] = $aPriceItem['data'][2];
                        $aPriceType[$iDecIndex]   = $aPriceItem['data'][3] ?? $this->_oPriceItemFormatter->getDefaultPriceType();
                    }
                }
            } elseif (1 == $iDecItemTotal && count($aPriceDesc) < 2) {
                $aDecPriceItem = current($this->_aDecPriceItem);
                $iDecIndex     = count($aPriceDesc);

                if (0 == count($aPriceDesc)) { //无加价项
                    $fAmount = $aDecPriceItem['data']['extra']['amount'];

                    $fEstimateFee = $this->_aInfo['activity_info'][0]['estimate_fee'] ?? 0.0;
                    $fDecPrice    = MainDataRepo::getFastCarEstimateFeeV2() - $fEstimateFee;
                    if (Horae::isCarpoolFlatRate($this->_aInfo['order_info']) && $fDecPrice > 0) {
                        // 区域渗透(司乘一口价)
                        $aPriceDesc = $this->_getAPriceDesc($fDecPrice, $aPriceDesc);
                        $aPriceAmount[$iDecIndex] = $fDecPrice;
                        $aPriceType[$iDecIndex]   = $this->_getSumDecreasePriceType();
                    } elseif ('dynamic_price' == $aDecPriceItem['icon_type']) {
                        if (1 == $aDecPriceItem['data']['extra']['use_times']) {
                            // 使用倍数表示 -x倍
                            $aPriceDesc[] = Language::replaceTag($this->_aText['dynamic_times_dec_price_desc'], ['fee_amount' => $fAmount]);
                        } else {
                            // 使用金额表示 -x元
                            $aPriceDesc[] = Language::replaceTag($this->_aText['dynamic_amount_dec_price_desc'], ['fee_amount' => $fAmount, 'currency_unit' => $this->_sCurrencyUnit]);
                        }

                        $aPriceDescIcon[] = $this->_aText[$aDecPriceItem['icon_type']];

                        $aPriceAmount[$iDecIndex] = $fAmount;
                        $aPriceType[$iDecIndex]   = $this->_getSumDecreasePriceType();
                    } elseif ('spec_ziyoubao' == $aDecPriceItem['icon_type']) {
                        $this->_specZiYoubao($aPriceDesc, $aPriceDescIcon, $aDecPriceItem['data']);
                    } else {
                        $iAmount          = $aDecPriceItem['data'][2];
                        $sCouponCustomTag = $aCouponInfo['activity_coupon']['custom_tag'] ?? ($aCouponInfo['default_coupon']['custom_tag'] ?? '');

                        // 1. 配置价格描述
                        if (!empty($sCouponCustomTag)) {
                            list($sPriceDescByTag, $sPriceDescIcon) = $this->_getCouponDescByTag($sCouponCustomTag, $iAmount);
                        }

                        if (empty($sPriceDescByTag)) {
                            $iMaxAmount = (int)$this->_aText['aplus_max_amount'];
                            if ('price_privilege' == $aDecPriceItem['icon_type']) {
                                $sPriceDesc = $aDecPriceItem['data'][0];
                            } elseif ($this->_isAPlusNewCategory() && $iAmount < $iMaxAmount) {
                                $sPriceDesc = $this->_aText['aplus_new_category'];
                            } else {
                                $sPriceDesc = $this->_getPriceDesc($aDecPriceItem['data'][2]);
                            }
                        } else {
                            $sPriceDesc = $sPriceDescByTag;
                        }

                        $aPriceDesc[] = $sPriceDesc;

                        // 2. 配置icon
                        if (!empty($sPriceDescByTag)) {
                            // icon需要根据CustomTag配置 (有需求配置空)
                            $aPriceDescIcon[] = $sPriceDescIcon ?: '';
                        } else {
                            $aPriceDescIcon[] = empty($sPriceDescIcon) ? $this->_getPriceDescIconByTypeAndContent($aDecPriceItem['icon_type'], $sPriceDesc, $aDecPriceItem['data'][2]) : $sPriceDescIcon;
                        }

                        $aPriceAmount[$iDecIndex] = $this->_aDecPriceItem[0]['data'][2] ?? 0;
                        $aPriceType[$iDecIndex]   = $this->_aDecPriceItem[0]['data'][3] ?? $this->_oPriceItemFormatter->getDefaultPriceType();
                    }
                } else {
                    // 加价项已经占据了一个位置时
                    // 前面的条件可保证, 此时铁定只有1个减价项
                    $fEstimateFee = $this->_aInfo['activity_info'][0]['estimate_fee'] ?? 0.0;
                    $fDecPrice    = MainDataRepo::getFastCarEstimateFeeV2() - $fEstimateFee;
                    if (Horae::isCarpoolFlatRate($this->_aInfo['order_info']) && $fDecPrice > 0) {
                        // 区域渗透(司乘一口价)
                        $aPriceDesc = $this->_getAPriceDesc($fDecPrice, $aPriceDesc);
                        $aPriceAmount[$iDecIndex] = $fDecPrice;
                        $aPriceType[$iDecIndex]   = $this->_getSumDecreasePriceType();
                    } elseif ('spec_ziyoubao' == $aDecPriceItem['icon_type']) {
                        $this->_specZiYoubao($aPriceDesc, $aPriceDescIcon, $aDecPriceItem['data']);
                    } else {
                        $iMaxAmount       = (int)$this->_aText['aplus_max_amount'];
                        $iAmount          = $aDecPriceItem['data'][2];
                        $sCouponCustomTag = $aCouponInfo['activity_coupon']['custom_tag'] ?? ($aCouponInfo['default_coupon']['custom_tag'] ?? '');

                        if ('price_privilege' == $aDecPriceItem['icon_type']) {
                            $sPriceDesc = $aDecPriceItem['data'][0];
                        } elseif ($this->_isAPlusNewCategory() && $iAmount < $iMaxAmount) {
                            $sPriceDesc = $this->_aText['aplus_new_category'];
                        } elseif (PriceItemFormatter::COUPON_TAG_NEWLOSS == $sCouponCustomTag || PriceItemFormatter::COUPON_TAG_NEWLOSS_TASK == $sCouponCustomTag) {
                            $sPriceDesc = Language::replaceTag(
                                $this->_aText['newloss_coupon'],
                                array(
                                    'num' => $aDecPriceItem['data'][2],
                                )
                            );
                        } else {
                            $sPriceDesc = $this->_getPriceDesc($aDecPriceItem['data'][2]);
                        }

                        $aPriceDesc[] = $sPriceDesc;
                        if (PriceItemFormatter::COUPON_TAG_NEW_CATEGORY == $sCouponCustomTag) {
                            // 含新客标识 ->Icon全为新客
                            $aPriceDescIcon[] = $this->_aText['mpt_category_icon'];
                        } elseif (PriceItemFormatter::COUPON_TAG_NEW_PLATFORM == $sCouponCustomTag) {
                            $aPriceDescIcon[] = $this->_aText['mpt_platform_icon'];
                        } elseif (PriceItemFormatter::COUPON_TAG_PERORDERSALE == $sCouponCustomTag) {
                            // 单单省icon
                            if (in_array($this->_aInfo['common_info']['access_key_id'], [Constants\Common::DIDI_IOS_PASSENGER_APP, Constants\Common::DIDI_WECHAT_MINI_PROGRAM, Constants\Common::DIDI_ALIPAY_MINI_PROGRAM])) {
                                $aPriceDescIcon[] = $this->_aText['pope_dandansheng_icon_ios_mini'];
                            } elseif (Constants\Common::DIDI_ANDROID_PASSENGER_APP == $this->_aInfo['common_info']['access_key_id']) {
                                $aPriceDescIcon[] = $this->_aText['pope_dandansheng_icon'];
                            }
                        } elseif (PriceItemFormatter::COUPON_TAG_NEWLOSS == $sCouponCustomTag || PriceItemFormatter::COUPON_TAG_NEWLOSS_TASK == $sCouponCustomTag) {
                            $aPriceDescIcon[] = '';
                        } else {
                            $aPriceDescIcon[] = $this->_getPriceDescIconByTypeAndContent($aDecPriceItem['icon_type'], $sPriceDesc, $aDecPriceItem['data'][2]);
                        }

                        $aPriceAmount[$iDecIndex] = $aDecPriceItem['data'][2];
                        $aPriceType[$iDecIndex]   = $aDecPriceItem['data'][3] ?? $this->_oPriceItemFormatter->getDefaultPriceType();
                    }
                }
            }

            //其他优惠
            if (count($aPriceDesc) < 2 && count($this->_aExtraPriceItem) > 0) { //pope福利金
                $aExtPriceItem    = current($this->_aExtraPriceItem);
                $aPriceDesc[]     = Language::replaceTag($this->_aText['reward_desc'], ['reward_amount' => $aExtPriceItem[2]]);
                $aPriceDescIcon[] = $aExtPriceItem[1];
            }
        }

        list($bHave, $aCarpoolCard) = $this->tryGetCarpoolCardDesc();
        if ($bHave) {
            array_splice($aPriceDescIcon, 0, 0, $aCarpoolCard['icon']); // 占位
            array_splice($aPriceDesc, 0, 0, $aCarpoolCard['desc'] ?? '');
        }

        //内循环账户返利
        if (count($aPriceDesc) < 2 && !empty($this->_aRevolvingAccountRebatePriceItem)&& !empty($this->_aRevolvingAccountRebatePriceItem[0])) {
            $aPriceDesc[] = $this->_aRevolvingAccountRebatePriceItem[0][0];
            $iDecIndex    = count($aPriceDesc) - 1;
            $aPriceAmount[$iDecIndex] = $this->_aRevolvingAccountRebatePriceItem[0][2];
            $aPriceType[$iDecIndex]   = $this->_aRevolvingAccountRebatePriceItem[0][3];
        }

        //内循环账户返利
        if (count($aPriceDesc) < 2 && !empty($this->_aRevolvingAccountDiscountPriceItem) && !empty($this->_aRevolvingAccountDiscountPriceItem[0])) {
            $aPriceDesc[] = $this->_aRevolvingAccountDiscountPriceItem[0][0];
            $iDecIndex    = count($aPriceDesc) - 1;
            $aPriceAmount[$iDecIndex] = $this->_aRevolvingAccountDiscountPriceItem[0][2];
            $aPriceType[$iDecIndex]   = $this->_aRevolvingAccountDiscountPriceItem[0][3];
        }

        $aResponse['price_info_desc'] = Common::formatPriceInfoV2($aPriceDesc, $aPriceDescIcon, [], $this->_aInfo, $aPriceAmount, $aPriceType);
        return $aResponse;
    }

    /**
     *  priceType
     * @return string
     */
    private function _getSumDecreasePriceType() {
        $type = $this->_oPriceItemFormatter->getSumDecreasePriceType();
        if ($this->_isLongDistanceCarpool()) {
            $type = $this -> _oPriceItemFormatter::PRICE_TYPE_LONG_DISTANCE_CARPOOL;
        }

        return $type;
    }

    /**
     *  获取溢价保护文案
     * @return void
     */
    private function _getDynamicMemberProtectMsg() {
        $aOrderInfo = $this->_oPriceItemFormatter->getOrderInfo();
        if (in_array($aOrderInfo['product_id'], [OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR, OrderSystem::PRODUCT_ID_DEFAULT])) {
            $iAmount = $this->_calcDecreaseAmount();
            $this->_aMemberDynamicProtect = $this->_oPriceItemFormatter->getMemberDynamicProtectInfo($iAmount);
            if ($iAmount > 0 && $this->_aMemberDynamicProtect[2] > 0) { //有优惠项
                $this->_aMemberDynamicProtect[1] = $this->_aText['common_dec_price_icon'];
            } else {
                $this->_aMemberDynamicProtect[1] = $this->_aText['member_dec_price_icon'];
            }

            if ($this->_oPriceItemFormatter->isPaidMemberDpa()) {
                $this->_aMemberDynamicProtect[1] = $this->_aText['paid_member_dec_price_icon'];
            }
        }
    }

    /**
     * 是否存在溢价保护
     * @return bool
     */
    private function _isMemberDynamicProtect() {
        if (!empty($this->_aMemberDynamicProtect[0]) && $this->_aMemberDynamicProtect[2] > 0) {
            return true;
        }

        return false;
    }

    /**
     * 福利金（支付后返回）
     * @return void
     */
    private function _fillExtraPriceItem() {
        $aSpecialRateWelfare = $this->_oPriceItemFormatter->getPopeSpecialRateWelfare();
        if (!empty($aSpecialRateWelfare)) {
            $this->_aExtraPriceItem[] = $aSpecialRateWelfare;
        }
    }

    /**
     * 计算减价 总抵扣金额
     * @return int|string
     */
    private function _calcDecreaseAmount() {
        $fAmount = 0;
        foreach ($this->_aDecPriceItem as $item) {
            if (is_numeric($item['data'][2]) && $item['data'][2] > 0) {
                $fAmount += $item['data'][2];
            }
        }

        return $fAmount;
    }
    /**
     * 判断是否是新客券
     * @return bool
     */
    private function _isNewcomerPope() {
        foreach ($this->_aDecPriceItem as $item) {
            if (count($item['data']) > 3 && PriceItemFormatter::PRICE_TYPE_NEWCPMER_COUPON_RECALL == $item['data'][3]) {
                return true;
            }
        }

        return false;
    }

    /**
     * 判断是否是单单折
     * @return bool
     */
    private function _isSingleFoldPope() {
        foreach ($this->_aDecPriceItem as $item) {
            if (count($item['data']) > 3 && PriceItemFormatter::PRICE_TYPE_SINGLE_FOLD == $item['data'][3]) {
                return true;
            }
        }

        return false;
    }

    /**
     * 判断是否是实验的长途特价
     * @return bool
     */
    private function _isLongDistanceCarpool() {
        foreach ($this->_aDecPriceItem as $item) {
            if (count($item['data']) > 3 && PriceItemFormatter::PRICE_TYPE_LONG_DISTANCE_CARPOOL == $item['data'][3]) {
                return true;
            }
        }

        return false;
    }

    /**
     * 判断是否是滴滴特快新客优惠
     * @return bool
     */
    private function _isAPlusNewCategory() {
        $iProductCategory = $this->_aInfo['order_info']['product_category'] ?? -1;
        $aCouponInfo      = $this->_aActivityInfo['coupon_info'];
        $sCouponCustomTag = $aCouponInfo['activity_coupon']['custom_tag'] ?? '';

        if (ProductCategory::PRODUCT_CATEGORY_APLUS == $iProductCategory && PriceItemFormatter::COUPON_TAG_NEW_CATEGORY == $sCouponCustomTag) {
            return true;
        }

        return false;
    }

    /**
     * 获取减券的文案
     * @param string $iAmount 价格
     * @return string
     */
    private function _getPriceDesc($iAmount) {
        $sPriceDesc = Language::replaceTag(
            $this->_aText['common_dec_price_desc'],
            ['fee_amount' => $iAmount, 'currency_unit' => $this->_sCurrencyUnit]
        );
        if ($this->_isLongDistanceCarpool()) {
            $sPriceDesc = $this->_common->getLongDistanceCarpoolContent($iAmount, $this->_sCurrencyUnit);
        }

        return $sPriceDesc;
    }

    /**
     * 获取减券的文案
     * @param string $type    类型
     * @param string $content 文案
     * @param string $amount  金额
     * @return string
     */
    private function _getPriceDescIconByTypeAndContent($type, $content, $amount) {
        if ('price_privilege' == $type) {
            return '';
        } elseif ($this->_isNewcomerPope()) {
            return $this->_aText['pope_newcomer_specials_icon_gif'];
        } elseif ($this->_isSingleFoldPope()) {
            return  $this->_getSingleFoldIcon();
        } elseif ($this -> _isLongDistanceCarpool()) {
            return $this->_common->getLongDistanceCarpoolIcon();
        } else {
            if ('coupon_dec_price_icon' == $type) {
                RecommendStrategyUtil::recordCouponDesc($this->_aInfo, $content, $this->_aText[$type], $amount);
            }

            return  $this->_aText[$type];
        }
    }
    /**
     *  获取减券的图片
     * @return string
     */
    private  function _getDefaultPriceDescIcon() {
        if ($this->_isNewcomerPope()) {
            return $this->_aText['pope_newcomer_specials_icon_gif'];
        } elseif ($this->_isSingleFoldPope()) {
            return $this->_getSingleFoldIcon();
        } elseif ($this->_isLongDistanceCarpool()) {
            return $this->_common->getLongDistanceCarpoolIcon();
        } else {
            return  $this->_aText['common_dec_price_icon'];
        }
    }


    /**
     * 是否是置顶样式
     * @return bool
     */
    private function _isReccomandStyle() {
        if (!Util::isDaCheAnyCar($this->_aInfo)) {
            return false;
        }

        if ($this->conflictWithGuide()) {
            return false;
        }

        $iProductCategory = $this->_aInfo['order_info']['product_category'];
        $aDecisionInfo    = DecisionLogic::getInstance()->getProductInfoByCategoryId($iProductCategory);
        if (empty($aDecisionInfo)) {
            $aDecisionInfo = DecisionLogic::getInstance()->getProductGuideInfoByCategoryId($iProductCategory);
        }

        if (isset($aDecisionInfo['recommend_info'])) {
            $aRecommendInfo = $aDecisionInfo['recommend_info'];
        }

        if (6 == $aRecommendInfo['show_type']) {
            return true;
        }

        return false;
    }

    /**
     * 是否是置顶样式
     * @return string
     */
    private function _getSingleFoldIcon() {
        if ($this->_isReccomandStyle()) {
            return $this->_aText['top_pope_single_fold_icon_gif'];
        } else {
            return $this->_aText['pope_single_fold_icon_gif'];
        }
    }

    /**
     * 是否是置顶样式冲突
     * @return bool
     */
    protected function conflictWithGuide() {
        $aGuideInfo = GuideInfo::getInstance()->getGuideProductInfo();
        return !empty($aGuideInfo) && $aGuideInfo['order_info']['estimate_id'] != $this->_aInfo['order_info']['estimate_id'];
    }

    /**
     * 加载拼车静态配置
     * @return array
     */
    protected function tryGetCarpoolCardDesc() {
        if (!Util::isDaCheAnyCar($this->_aInfo) || !\BizCommon\Utils\Horae::isCarpoolUnSuccessFlatPrice($this->_aInfo['order_info']['n_tuple'])) {
            return [false, []];
        }

        list($sSource, $aPayStatus) = CarpoolCommuteCardLogic::getInstance()->getVCardInfoByEstimateID($this->_aInfo['bill_info']['estimate_id']);
        if (empty($sSource)) {
            // 不用卡
            return [false, []];
        }

        // 有卡
        if ('send' == $sSource) {
            // 首次赠卡
            $bFeatureStatus = CarpoolCommuteCardLogic::getInstance()->getSendCardFeatureByEstimateID($this->_aInfo['bill_info']['estimate_id']);
            if (2 == $bFeatureStatus) {
                // 流失用户
                $sFormatConfig = Language::getDecodedTextFromDcmp('estimate_dache_anycar-carpool_price_info')['commute_card_send_loss'] ?? [];
            } else {
                $sFormatConfig = Language::getDecodedTextFromDcmp('estimate_dache_anycar-carpool_price_info')['commute_card_send_first'] ?? [];
            }

            return [true, $sFormatConfig];
        } elseif ('usable_give' == $sSource) {
            $sFormatConfig = Language::getDecodedTextFromDcmp('estimate_dache_anycar-carpool_price_info')['commute_card_usable_give'] ?? [];
            return [true, $sFormatConfig];
        } elseif (0 == $aPayStatus) {
            $sFormatConfig = Language::getDecodedTextFromDcmp('estimate_dache_anycar-carpool_price_info')['commute_card_send'] ?? [];
            return [true, $sFormatConfig];
        } else {
            // 获取省钱卡文案,icon配置
            $sFormatConfig = Language::getDecodedTextFromDcmp('estimate_dache_anycar-carpool_price_info')['commute_card'] ?? [];
            return [true, $sFormatConfig];
        }
    }

    /**
     * @param array $aPriceDesc        $aPriceDesc,
     * @param array $aPriceDescIcon    $aPriceDescIcon,
     * @param array $aSpecZiYoubaoDesc $aSpecZiYoubaoDesc
     * @return void
     */
    private function _specZiYoubao(&$aPriceDesc, &$aPriceDescIcon, $aSpecZiYoubaoDesc) {
        // 低版本
        // 由于主端上实现问题(会对图标进行缩放), 低版本只能使用两段内容, 拼出 自由宝 的展示
        // 小程序没有问题
        if (MainDataRepo::isNativeClient() && version_compare(MainDataRepo::getAppVersion(), '6.3.2') < 0) {
            $aPriceDescIcon[] = ''; // 占位
            $aPriceDesc[]     = $aSpecZiYoubaoDesc[4] ?? '';

            $aPriceDescIcon[] = ''; // 占位
        } else {
            $aPriceDescIcon[] = $aSpecZiYoubaoDesc[1] ?? '';
        }

        $aPriceDesc[] = $aSpecZiYoubaoDesc[0] ?? '';
    }

    /**
     * @param float $fAmount    省XX  金额
     * @param array $aPriceDesc 价格描述
     * @return array
     */
    private function _getAPriceDesc($fAmount, $aPriceDesc) {
        $aCarpoolDescConfig = Language::getDecodedTextFromDcmp('config_carpool-estimate_fee_discount_msg');
        $sFormat            = $aCarpoolDescConfig['cheaper_than_flash'] ?? '';
        $aPriceDesc[]       = Language::replaceTag(
            $sFormat,
            [
                'currency_symbol' => '',
                'price'           => NumberHelper::numberFormatDisplay($fAmount, '', 2),
                'currency_unit'   => $this->_sCurrencyUnit,
            ]
        );
        return $aPriceDesc;
    }

    /**
     * @param string $sCouponCustomTag 券tag
     * @param int    $iAmount          券金额
     * @return array
     */
    private function _getCouponDescByTag($sCouponCustomTag, $iAmount) {
        $sPriceDesc     = '';
        $sPriceDescIcon = '';

        $aCouponTagConfig = NuwaConfig::text('estimate_new_form', 'double_line_coupon_template');
        if (!empty($aCouponTagConfig) && !empty($aCouponTagConfig[$sCouponCustomTag])) {
            if (empty($aCouponTagConfig[$sCouponCustomTag]['apollo'])) {
                // 正常逻辑
                $sPriceDesc     = $aCouponTagConfig[$sCouponCustomTag]['desc'];
                $sPriceDescIcon = $aCouponTagConfig[$sCouponCustomTag]['icon'];
            } else {
                // 根据阿波罗选择文案
                $aPassengerInfo = $this->_aInfo['passenger_info'];

                $aApolloParams  = [
                    'key'           => $aPassengerInfo['pid'],
                    'phone'         => $aPassengerInfo['phone'],
                    'city'          => $this->_aInfo['order_info']['area'],
                    'business_id'   => $this->_aInfo['order_info']['business_id'],
                    'car_level'     => $this->_aInfo['order_info']['require_level'],
                    'combo_type'    => $this->_aInfo['order_info']['combo_type'],
                    'level_type'    => $this->_aInfo['order_info']['level_type'],
                    'access_key_id' => $this->_aInfo['common_info']['access_key_id'],
                    'county'        => $this->_aInfo['order_info']['county'],
                ];
                $oFeatureToggle = (new ApolloV2())->featureToggle($aCouponTagConfig[$sCouponCustomTag]['apollo']['key'], $aApolloParams);

                if ($oFeatureToggle->allow()) {
                    $sPriceDesc     = $aCouponTagConfig[$sCouponCustomTag]['apollo']['true']['desc'];
                    $sPriceDescIcon = $aCouponTagConfig[$sCouponCustomTag]['apollo']['true']['icon'];
                } else {
                    $sPriceDesc     = $aCouponTagConfig[$sCouponCustomTag]['apollo']['false']['desc'];
                    $sPriceDescIcon = $aCouponTagConfig[$sCouponCustomTag]['apollo']['false']['icon'];
                }
            }
        }

        if (!empty($sPriceDesc)) {
            $sPriceDesc = Language::replaceTag(
                $sPriceDesc,
                ['amount' => $iAmount, 'currency_unit' => $this->_sCurrencyUnit]
            );
        }

        return [$sPriceDesc, $sPriceDescIcon];
    }
}
