<?php

/**
 * 处理出租车业务下的品类的 price_info_desc字段的component类，该字段是一个数组类型
 * <AUTHOR> <yangshu<PERSON><PERSON>@didiglobal.com>
 */

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo;

use BizLib\Utils\Language;
use BizLib\Utils\NumberHelper;
use BizLib\Utils\ProductCategory;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;
use PreSale\Logics\estimatePriceV2\multiResponse\virtualGroupCar\TaxiPricingSingle;
use PreSale\Logics\taxi\TaxiPeakFee;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TaxiPricingBoxLayout;

/**
 * Class TaxiPriceDesc
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo
 * <AUTHOR> <<EMAIL>>
 */
class TaxiPriceDesc
{
    private $_aInfo;
    private $_bIsSpecialRate;
    private $_oPriceItemFormatter;
    private $_aIncPriceItem = [];  // 加价项目: 信息服务费
    private $_aDecPriceItem = [];  // 减价项目：司机愿减、券优惠
    private $_bBonusFLag    = false;

    const HOLIDAY_SURCHARGE_FEE_TEXT = 'taxi_holiday_surcharge-way_out_conf'; // 节假日附加费标签文案

    /**
     * UnionePriceDesc constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo          = $aInfo;
        $this->_bIsSpecialRate = Util::isSpecialRate($aInfo);

        $this->_oPriceItemFormatter = new PriceItemFormatter($aInfo);
    }

    /**
     * @param array $aResponse aResponse
     * @return array
     */
    public function build($aResponse) {
        $this->_fillIncPriceItem(); // 费用加项
        $this->_fillDecPriceItem();  // 费用减项
        $this->_filterPriceItem();
        // 一口价超值出租车品类不需在预估价下方展示”打表计价“文本，否则它会被拼接"打表计价"前缀，跟”一口价“的概念冲突
        $bFlag = ProductCategory::PRODUCT_CATEGORY_UNIONE_SPECIAL_PRICE == $aResponse['product_category'];

        // 中文版本才走下面的逻辑
        if (Language::ZH_CN == $this->_aInfo['common_info']['lang']
            && !$bFlag && !Util::isTaxiMarketisation($this->_aInfo)
            && !TaxiPricingSingle::getInstance()->judgePcIdInBox($aResponse['product_category'])
        ) {
            if ('打表计价' != $aResponse['fee_msg'] && count($this->_aDecPriceItem) > 0) { // 预估价且有券的时候
                if (!Util::isEstimateFormDoubleLineStyle($this->_aInfo)) {
                    $this->_addMsgToPriceDesc();
                }
            } elseif ('打表计价' != $aResponse['fee_msg']
                && 0 == count($this->_aDecPriceItem)
                && 0 == count($this->_aIncPriceItem)
            ) { //
                // 预估价且无券时
                if (!Util::isEstimateFormDoubleLineStyle($this->_aInfo)) {
                    $this->_aDecPriceItem[0] = array('打表计价', '',);
                }
            } else {
            }
        }

        $aResponse = $this->_format($aResponse);

        return $aResponse;
    }

    /**
     * 加价项费用顺序：信息服务费 > 节假日服务费
     * 旧表单中只能展示两个标签
     * @return void
     */
    private function _fillIncPriceItem() {
        // 节假日服务费
        $this->_aIncPriceItem[] = $this->_getIncHolidayFee();
        // 峰期加价
        $this->_aIncPriceItem[] = $this->_getIncPeakFee();
        // 跨城费
        $this->_aIncPriceItem[] = $this->_getIncTaxiCrossCityFee();
        // 出租车的商业化信息费没有用到，防止未知事故，不删除此代码，作为一个case来处理
        $this->_aIncPriceItem[] = $this->_oPriceItemFormatter->getInfoFeeDescLite();
    }

    /**
     * 减价项费用：司机愿减、限时特惠、优惠券
     * @return void
     */
    private function _fillDecPriceItem() {
        // 限时特惠，超值出租车、司机愿减
        $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getLimitTimeDescLite();
        // 优惠券（coupon、pope）
        $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getUnioneCouponDescLite($this->_bIsSpecialRate);
        // 打车福利金
        $aBonus = $this->_oPriceItemFormatter->getRewardsDescLite();
        if (!empty($aBonus[2])) {
            $this->_aDecPriceItem[] = $aBonus;
            $this->_bBonusFLag      = true;
        }

        // 出租车峰期灵活补
        $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getUnionePeakDiscount();
    }

    /**
     * 格式化输出 price_info_desc字段
     * @param array $aResponse $aResponse
     * @return array
     */
    private function _format($aResponse) {
        $aPriceDesc       = $aPriceDescIcon = $aPriceType = [];
        $sAmountDec       = '0';
        $sCouponCustomTag = '';
        $iIncCountDesc    = count($this->_aIncPriceItem);
        $iCountDesc       = count($this->_aDecPriceItem);

        // 目前旧表单只展示两个标签，基于加费项大于减费项的原则，将加费项进行挨个渲染
        // 如果费项大于两个，后续的减费项会依旧返回数据，但实际上展示的时候没渲染
        if (!empty($this->_aIncPriceItem)) {
            foreach ($this->_aIncPriceItem as $v) {
                $aPriceDesc[]     = $v[0];
                $aPriceDescIcon[] = $v[1];
                $aPriceType[]     = $v[3];
            }
        }

        if (1 == count($this->_aDecPriceItem)) {  // 只有一个减费项
            $aPriceDesc[]     = $this->_aDecPriceItem[0][0];
            $aPriceDescIcon[] = $this->_aDecPriceItem[0][1];
            $sAmountDec       = $this->_aDecPriceItem[0][2];
            $sCouponCustomTag = $this->_aDecPriceItem[0][3];
            $aPriceType[]     = $this->_oPriceItemFormatter::getDefaultPriceType();
        } elseif (count($this->_aDecPriceItem) > 1) {  // 有多个减费项时，要计算减项的总金额
            $sAmountDec       = $this->_calcDecreaseAmount();
            $aPriceDesc[]     = $this->_oPriceItemFormatter->getDecreaseAmountDesc($sAmountDec);
            $aPriceDescIcon[] = '';
            $aPriceType[]     = $this->_oPriceItemFormatter::getDefaultPriceType();
            foreach ($this->_aDecPriceItem as $item) {
                if (in_array(PriceItemFormatter::CHAO_ZHI_WEEKEND, $item)) {
                    $sCouponCustomTag = PriceItemFormatter::CHAO_ZHI_WEEKEND;
                }
            }
        } else {
        }

        // 超值出租车
        if ($this->_bIsSpecialRate) {
            // 优惠感知需求，优惠透出文案为： 已优惠n元， 且 n = 超值出租车品类折扣差额 + 券金额
            // http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=*********
            $sSpecialDis = $this->_oPriceItemFormatter->calSpecialTaxiDiscountAmount();
            // 有标签券
            if (!empty($sCouponCustomTag) && !$this->_bBonusFLag) {
                $sAmountDec  = NumberHelper::numberFormatDisplay((float)$sAmountDec + (float)$sSpecialDis, '', 2);
                $sCouponDesc = $this->_oPriceItemFormatter->getTaxiActivityCouponTag($sCouponCustomTag, $sAmountDec);
                // 有标签券 && 无加费项
                if ('' != $sCouponDesc[0] && 0 == $iIncCountDesc) {
                    unset($aPriceDesc);
                    $aPriceDesc[] = $sCouponDesc[0];
                }
            } else {
                // 无标签券
                $aSpecialDesc = [];
                // 因之前的逻辑太乱，故此处对超值出租车品类重新渲染
                unset($aPriceDesc);
                // 先把加费项整理出来
                foreach ($this->_aIncPriceItem as $v) {
                    array_push($aSpecialDesc, $v[0]);
                }

                // 判断是否有定价折扣，有定价折扣的，展示"已优惠x元"
                if ('0.00' != $sSpecialDis) {
                    $sAmountDec     = NumberHelper::numberFormatDisplay((float)$sAmountDec + (float)$sSpecialDis, '', 2);
                    $sSpecialDisPer = $this->_oPriceItemFormatter->calSpecialTaxiDiscountPerception($sAmountDec);
                    if ('' != $sSpecialDisPer) {
                        array_push($aSpecialDesc, $sSpecialDisPer);
                    }
                } else {
                    // 只有一个减费项
                    if (1 == count($this->_aDecPriceItem)) {
                        array_push($aSpecialDesc, $this->_aDecPriceItem[0][0]);
                    }

                    // 多个减费项，展示"优惠共抵扣x元"
                    if (count($this->_aDecPriceItem) > 1 && $sSpecialDis > 0) {
                        array_push($aSpecialDesc, $this->_oPriceItemFormatter->getDecreaseAmountDesc($sAmountDec));
                    }
                }

                // 重新赋值
                $aPriceDesc = $aSpecialDesc;
            }
        }

        // 产品要求，费项为稳妥起见，只展示两个
        $aPriceDesc = array_splice($aPriceDesc, 0, 2);
        $aResponse['price_info_desc'] = Common::formatPriceInfoV2($aPriceDesc, $aPriceDescIcon, [], $this->_aInfo, [], $aPriceType);

        return $aResponse;
    }

    /**
     * 检查PriceItem的格式
     * @param array $aPriceItem $aPriceItem
     * @return bool
     */
    private static function _checkValid($aPriceItem) {
        if (2 == count($aPriceItem) && !empty($aPriceItem[0])) {
            return true;
        } elseif (3 == count($aPriceItem) && !empty($aPriceItem[0]) && $aPriceItem[2] > 0) {
            return true;
        } elseif (4 == count($aPriceItem) && $aPriceItem[2] > 0) {
            return true;
        } else {
        }

        return false;
    }

    /**
     * 过滤空的price item项
     *  @return void
     */
    private function _filterPriceItem() {
        foreach ($this->_aDecPriceItem as $idx => $aPriceItem) {
            if (!self::_checkValid($aPriceItem)) {
                unset($this->_aDecPriceItem[$idx]);
            }
        }

        if (count($this->_aDecPriceItem) > 0) {
            $this->_aDecPriceItem = array_values($this->_aDecPriceItem);
        }

        foreach ($this->_aIncPriceItem as $idx => $aPriceItem) {
            if (!self::_checkValid($aPriceItem)) {
                unset($this->_aIncPriceItem[$idx]);
            }
        }

        if (count($this->_aIncPriceItem) > 0) {
            $this->_aIncPriceItem = array_values($this->_aIncPriceItem);
        }
    }

    /**
     * 计算减价 总抵扣金额
     * @return int
     */
    private function _calcDecreaseAmount() {
        $fAmount = 0;
        foreach ($this->_aDecPriceItem as $item) {
            if (is_numeric($item[2]) && $item[2] > 0) {
                $fAmount += $item[2];
            }
        }

        return $fAmount;
    }

    /**
     * price_info_desc字段的内部content字段前添加"打表计价"文案，只作用到减价项
     * @return void
     */
    private function _addMsgToPriceDesc() {
        foreach ($this->_aDecPriceItem as &$item) {
            $item[0] = '打表计价，' . $item[0];
        }
    }

    /**
     * 获取峰期加价信息费
     * @return array 信息费标签内容
     */
    private function _getIncPeakFee() {
        $oTaxiPeakFeeClient = TaxiPeakFee::getPoolInstance($this->_aInfo);
        //1. 开城状态
        $bStatus = $oTaxiPeakFeeClient->getStatus();
        //2. 是否有出口
        $bIsInterActive = $oTaxiPeakFeeClient->getIsInterActive();
        // 只有在开城 且 无出口的情况下会渲染无出口样式
        if ($bStatus && !$bIsInterActive) {
            $aWayOutConf = $oTaxiPeakFeeClient->getWayOutConf();
            $sPeakPrice  = $this->_getBillExpense('taxi_peak_price');
            if (empty($sPeakPrice)) {
                return [];
            }

            return [
                sprintf($aWayOutConf['content'] ?? '', $sPeakPrice),
                $aWayOutConf['icon'] ?? '',
                NumberHelper::numberFormatDisplay($sPeakPrice),
                PriceItemFormatter::getTaxiPeakFeePriceType(),
            ];
        }

        return [];
    }

    /**
     * 获取节假日附加费
     * @return array 附加费标签内容
     */
    private function _getIncHolidayFee() {
        $iBillFee = $this->_getBillExpense('taxi_holiday_price');
        if ($iBillFee <= 0) {
            return [];
        }

        $aLabel = Language::getDecodedTextFromDcmp(self::HOLIDAY_SURCHARGE_FEE_TEXT);
        return [
            sprintf($aLabel['content'], NumberHelper::numberFormatDisplay($iBillFee)),
            $aLabel['icon'] ?? '',
            $iBillFee,
            PriceItemFormatter::getTaxiHolidayFeePriceType(),
        ];
    }

    /**
     * 账单返回的费用
     * @param string $key 账单所返回费用的key值
     * @return string 加价费用（单位：分）
     */
    private function _getBillExpense($key) {
        $iRequireLevel = $this->_aInfo['order_info']['require_level'];
        return $this->_aInfo['bill_info']['bills'][$iRequireLevel]['fee_detail_info'][$key] ?: 0;
    }

    /**
     * 获取跨城费
     * @return array 附加费标签内容
     */
    private function _getIncTaxiCrossCityFee() {
        $iCrossCityFee = $this->_getBillExpense('cross_city_fee');
        if ($iCrossCityFee <= 0) {
            return [];
        }

        $aConfig = Language::getDecodedTextFromDcmp('config_text-taxi_crosscity_fee');
        return [
            Language::replaceTag(
                $aConfig['content'],
                [
                    'content' => NumberHelper::numberFormatDisplay($iCrossCityFee),
                ]
            ),
            $aConfig['icon'] ?? '',
            $iCrossCityFee,
            PriceItemFormatter::getTaxiCrossCityFeePriceType(),
        ];
    }
}
