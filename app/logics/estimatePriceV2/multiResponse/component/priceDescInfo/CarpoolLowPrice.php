<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo;

use <PERSON>izCommon\Utils\Horae;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\Language;
use BizLib\Utils\NumberHelper;
use Dirpc\SDK\PreSale\NewFormFeeDesc;
use Nuwa\ApolloSDK\Apollo;
use Nuwa\ApolloSDK\Apollo as NuwaApollo;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;
use Xiaoju\Apollo\Apollo as ApolloV2;

/**
 * class CarpoolLowPrice
 */
class CarpoolLowPrice
{
    /**
     * @var array
     */
    private $_aInfo;

    /**
     * @var PriceItemFormatter
     */
    private $_oFormatter;

    const SCENE_KEY_1 = 'success_1_2'; //V2表单临时用来识别的，不是长期方案
    const SCENE_KEY_2 = 'success_2_1';

    /**
     * CarpoolPriceDesc constructor.
     *
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo      = $aInfo;
        $this->_oFormatter = new PriceItemFormatter($this->_aInfo);
    }

    /**
     * @param array $aResponse $aResponse
     * @return mixed
     */
    public function build($aResponse) {
        $aOrderInfoNTuple = $this->_aInfo['order_info']['n_tuple'];
        if (Horae::isLowPriceCarpoolV2($aOrderInfoNTuple)) {
            $aPriceDesc = $this->_getV2PriceDesc();
        } else {
            $aPriceDesc = $this->_getV1PriceDesc();
        }

        if (!empty($aPriceDesc)) {
            $aResponse['price_info_desc'][] = $aPriceDesc;
        }

        $aLowCarbon = $this->getLowCarbonHourFeeDesc();
        if (!empty($aLowCarbon)) {
            if (empty($aResponse['price_info_desc'])) {
                $aResponse['price_info_desc'] = array();
            }

            array_unshift($aResponse['price_info_desc'], $aLowCarbon);
        }

        return $aResponse;
    }


    /**
     * @return array
     */
    private function _getV1PriceDesc() {
        $aPriceDesc = [];

        list($sSymbol, $sCurrencyUnit) = $this->_oFormatter->getSymbolUnit();
        // 拼成乐V1
        $sCarLevel = $this->_aInfo['order_info']['require_level'];
        $aBillInfo = $this->_aInfo['bill_info']['bills'][$sCarLevel] ?? [];

        $fBaseFee     = $aBillInfo['dynamic_total_fee'] ?? 0.0;
        $fEstimateFee = $this->_aInfo['activity_info'][0]['estimate_fee'] ?? 0.0;
        $fDiffPrice   = $fBaseFee - $fEstimateFee;
        if (round($fDiffPrice, 1) > 0.0) {
            $aPriceDesc = $this->_generatePriceDesc($sSymbol, $sCurrencyUnit, $fDiffPrice);
        }

        return $aPriceDesc;
    }

    /**
     * @return array
     */
    private function _getV2PriceDesc() {
        $aPriceDesc = [];

        list($sSymbol, $sCurrencyUnit) = $this->_oFormatter->getSymbolUnit();

        //实付优惠
        $fChannelDiscount = $this->_pinchecheV2GetPrice();
        //sps优惠
        $fSpsDiscount = $this->_getSPSDiscountInfo();
        $fDiffPrice   = $fChannelDiscount + $fSpsDiscount;
        if (round($fDiffPrice, 1) > 0.0) {
            $aPriceDesc = $this->_generatePriceDesc($sSymbol, $sCurrencyUnit, $fDiffPrice);
        }

        return $aPriceDesc;
    }

    /**
     * _pinchecheV2GetPrice
     * @return float
     */
    private function _pinchecheV2GetPrice() {
        $sCarLevel = $this->_aInfo['order_info']['require_level'];
        $aBillInfo = $this->_aInfo['bill_info']['bills'][$sCarLevel] ?? [];

        $iMaxPoolNum      = -1;
        $fDynamicTotalFee = 0.0;
        $fEstimateFee     = 0.0;

        // 找到最大拼到人数, 及对应的券前价格,
        // 也就是找到拼满 对应的人数和价格
        $aCarpoolScenePrice = $this->_aInfo['activity_info'][0]['carpool_scene_price'] ?? [];
        foreach ($aCarpoolScenePrice as $aScenePrice) {
            $aOption = $aScenePrice['option'];
            if (($aOption['pool_num'] ?? -1) > $iMaxPoolNum) {
                $iMaxPoolNum      = $aOption['pool_num'];
                $fDynamicTotalFee = $aScenePrice['dynamic_total_fee'] ?? 0.0;
                $fEstimateFee     = $aScenePrice['estimate_fee'];
            }
        }

        if (empty($fDynamicTotalFee) || empty($fEstimateFee)) {
            return 0.0;
        }

        return $fDynamicTotalFee - $fEstimateFee;
    }


    /**
     * _pinchecheV2GetCoupon
     * @return array
     */
    // 注释-待删
//    private function _pinchecheV2GetCoupon() {
//        $sCarLevel = $this->_aInfo['order_info']['require_level'];
//        $aBillInfo = $this->_aInfo['bill_info']['bills'][$sCarLevel] ?? [];
//
//        $iMaxPoolNum      = -1;
//        $fCouponAmount    = 0.0;
//        $fNotCouponAmount = 0.0;
//
//        // 找到最大拼到人数
//        foreach ($aBillInfo['carpool_scene_price'] ?? [] as $aScenePrice) {
//            $aOption = $aScenePrice['option'];
//            if (($aOption['pool_num'] ?? -1) > $iMaxPoolNum) {
//                $iMaxPoolNum = $aOption['pool_num'];
//            }
//        }
//
//        // 找出拼到最大人数时的券折扣和非券折扣
//        $aCarpoolScenePrice = $this->_aInfo['activity_info'][0]['carpool_scene_price'] ?? [];
//        foreach ($aCarpoolScenePrice as $aScenePrice) {
//            if ($aScenePrice['option']['pool_num'] == $iMaxPoolNum) {
//                if (!empty($aScenePrice['discount_item'])) {
//                    foreach ($aScenePrice['discount_item'] as $aItem) {
//                        if ('coupon' == $aItem['type']) {
//                            $fCouponAmount = $aItem['amount'];
//                        } else {
//                            $fNotCouponAmount += $aItem['amount'];
//                        }
//                    }
//                }
//            }
//        }
//
//        return [$fCouponAmount, $fNotCouponAmount];
//    }

    /**
     * 获取SPS定价优惠
     * @return float
     */
    private function _getSPSDiscountInfo() {
        $aBillDetail = current($this->_aInfo['bill_info']['bills']);
        if (empty($aBillDetail['carpool_price_result_list'])) {
            return 0;
        }

        $aBill = $aBillDetail['carpool_price_result_list'][self::SCENE_KEY_1] ?? $aBillDetail['carpool_price_result_list'][self::SCENE_KEY_2];

        if (empty($aBill)) {
            return 0;
        }

        $aCarpoolDescConfig = Language::getDecodedTextFromDcmp('config_carpool-estimate_fee_discount_msg');

        $fSPSAmount = 0;
        foreach ($aBill['fee_detail_info'] as $sFeeName => $fAmount) {
            if (!empty($aCarpoolDescConfig[$sFeeName])) {
                $fSPSAmount = abs($fAmount);
            }
        }

        return $fSPSAmount;
    }


    /**
     * @param string $sSymbol       $sSymbol
     * @param string $sCurrencyUnit $sCurrencyUnit
     * @param float  $fDiffPrice    diff
     * @return array
     */
    // 注释-待删
//    private function _generateTotalDiscountDesc($sSymbol, $sCurrencyUnit, $fDiffPrice) {
//        $aCarpoolDescConfig = Language::getDecodedTextFromDcmp('config_carpool-estimate_fee_discount_msg');
//        $sFormat            = $aCarpoolDescConfig['total_discount'] ?? '';
//        return [
//            Language::replaceTag(
//                $sFormat,
//                [
//                    'currency_symbol' => $sSymbol,
//                    'price'           => NumberHelper::numberFormatDisplay($fDiffPrice, '', 1),
//                    'currency_unit'   => $sCurrencyUnit,
//                ]
//            ),
//        ];
//    }

    /**
     * @param string $sSymbol       $sSymbol
     * @param string $sCurrencyUnit $sCurrencyUnit
     * @param float  $fDiffPrice    diff
     * @return array
     */
    private function _generatePriceDesc($sSymbol, $sCurrencyUnit, $fDiffPrice) {
        if (Util::isEstimateFormDoubleLineStyle($this->_aInfo)) {
            $aConfig    = NuwaConfig::text('estimate_new_form', 'estimate_form_info');
            $aPriceDesc = [
                'content' => Language::replaceTag(
                    $aConfig['common_dec_price_desc'],
                    [
                        'currency_symbol' => $sSymbol,
                        'fee_amount' => NumberHelper::numberFormatDisplay($fDiffPrice, '', 1),
                        'currency_unit' => $sCurrencyUnit,
                    ]
                ),
                'left_icon' => $aConfig['common_dec_price_icon'],
            ];
        } else {
            $aCarpoolDescConfig = Language::getDecodedTextFromDcmp('config_carpool-estimate_fee_discount_msg');

            $sFormat    = $aCarpoolDescConfig['normal_discount'] ?? '';
            $aPriceDesc = [
                'content' => Language::replaceTag(
                    $sFormat,
                    [
                        'currency_symbol' => $sSymbol,
                        'price' => NumberHelper::numberFormatDisplay($fDiffPrice, '', 1),
                        'currency_unit' => $sCurrencyUnit,
                    ]
                )
            ];
        }

        return $aPriceDesc;
    }

    /**
     * 低碳一小时感知ab实验
     * @return bool
     */
    private function _hitLowCarbonHourShow() {
        $oApollo       = new NuwaApollo();
        $aParams       =  [
            Apollo::APOLLO_INDIVIDUAL_ID => $this->_aInfo['passenger_info']['pid'],
            'city'                       => $this->_aInfo['order_info']['area'],
            'phone'                      => $this->_aInfo['passenger_info']['phone'],
        ];
        $featureToggle = $oApollo->featureToggle('low_carbon_hour_bubble_activity_show_ab', $aParams);
        if (!$featureToggle->allow()) {
            return $this->__hitLowCarbonHourShowToggle();
        }

        if ('treatment_group' != $featureToggle->getGroupName()) {
            return false;
        }

        return true;
    }

    /**
     * 低碳一小时感知灰度实验
     * @return bool
     */
    private function __hitLowCarbonHourShowToggle() {
        $oApollo       = new NuwaApollo();
        $aParams       =  [
            'city'                       => $this->_aInfo['order_info']['area'],
            'phone'                      => $this->_aInfo['passenger_info']['phone'],
        ];

        $featureToggle = $oApollo->featureToggle('low_carbon_hour_bubble_activity_show_toggle', $aParams);

        return $featureToggle->allow();
    }

    /**
     * 低碳一小时标签
     * @return array
     */
    private function getLowCarbonHourFeeDesc() {
        // 1. 是否命中活动策略
        $aApolloParams = [
            'pid'           => $this->_aInfo['passenger_info']['pid'],
            'phone'         => $this->_aInfo['passenger_info']['phone'],
            'city'          => $this->_aInfo['order_info']['area'],
            'access_key_id' => $this->_aInfo['common_info']['access_key_id'],
            'app_version'   => $this->_aInfo['common_info']['app_version'],
            'lang'          => $this->_aInfo['common_info']['lang'],
        ];

        $oFeatureToggle = ApolloV2::getInstance()->featureToggle('low_carbon_hour_fee_desc_toggle', $aApolloParams);
        if (!$oFeatureToggle->allow()) {
            return null;
        }

        if (empty($oFeatureToggle->getParameter('icon', ''))) {
            return null;
        }

        // 2.命中策略后，是否做感知曝光: 先过ab实验，如果没有进入实验，走灰度控制
        if (!$this->_hitLowCarbonHourShow()) {
            return null;
        }

        // 3. 返回物料
        return [
            'left_icon' => $oFeatureToggle->getParameter('icon', ''), // todo icon or left_icon?
        ];
    }
}
