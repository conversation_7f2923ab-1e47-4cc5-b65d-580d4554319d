<?php
/**
 * Created by PhpStorm.
 * User: didi
 * Date: 2019/12/17
 * Time: 上午11:35
 * <AUTHOR> <<EMAIL>>
 */

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo;

use BizLib\Config;
use BizLib\Constants;
use BizLib\Utils\Language;
use BizLib\Utils\NumberHelper;
use Dirpc\SDK\Veyron\email;
use PreSale\Logics\commonAbility\SpringRedPacketFormatter;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;

/**
 * Class PriceDesc
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo
 */
class DiXiaoDiPriceDesc
{
    private $_aInfo;

    private $_oPriceItemFormatter;

    private $_oSpringRedPacketFormatter;

    private $_aDecPriceItem = [];


    private $_aBillInfo = [];

    /**
     * @var array 自由宝展示内容
     */
    private $_aSpecZiYoubao = [];

    /**
     * NormalPriceDescV2 constructor.
     * @param array $aInfo $aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
        $this->_oPriceItemFormatter       = new PriceItemFormatter($aInfo);
        $this->_oSpringRedPacketFormatter = new SpringRedPacketFormatter($aInfo);
        $sCarLevel        = $aInfo['order_info']['require_level'];
        $this->_aBillInfo = $aInfo['bill_info']['bills'][$sCarLevel] ?? [];
    }

    /**
     * @param array $aResponse $aResponse
     * @return mixed
     */
    public function build($aResponse) {
        $this->_fillDecPriceItem();

        //build price_info_desc
        $aResponse = $this->_format($aResponse);

        return $aResponse;
    }

    /**
     * 6.0特价车打折信息
     * @return string
     */
    public function getDiXiaoDiDiscountLite() {
        $sPriceDesc = '';
        if (isset($this->_aBillInfo)) {
            $carLevel = $this->_aInfo['order_info']['require_level'];
            $fAmount  = $this->_aInfo['bill_info']['bills'][$carLevel]['compare_price_info'][$carLevel]['price'] ?? 0;
            $fAmount4SpecialRate = MainDataRepo::getFastCarSpecialRateTotalFee();
            // 当 特惠快车原价 与 快车原价 一致时，不展示该文案
            if ($fAmount && $fAmount > $fAmount4SpecialRate) {
                $aConfigText = Config::text('config_passenger', 'dixiaodi');
                $sPriceDesc  = Language::replaceTag(
                    $aConfigText['underline_price_desc'],
                    [
                        'price' => NumberHelper::numberFormatDisplay($fAmount),
                    ]
                );
            }
        }

        return $sPriceDesc;
    }

    /**
     * @param array $aResponse $aResponse
     * @return mixed
     */
    private function _format($aResponse) {
        $aPriceDesc  = [];
        $sCouponDesc = '';

        $fAmount = $this->_calcDecreaseAmount();
        if (0 < count($this->_aDecPriceItem) && $fAmount > 0) {
            $aConfigText = Config::text('config_passenger', 'dixiaodi');
            $sCouponDesc = Language::replaceTag(
                $aConfigText['estimate_coupon_info'],
                ['fee' => $fAmount,]
            );
        }

        //春节红包
        list($sRedPacketDesc, $fRedPacketValue) = $this->_oSpringRedPacketFormatter->getRedPacketInfoLite();
        if (!empty($sRedPacketDesc)) {
            $aPriceDesc[] = ['content' => $sRedPacketDesc, 'left_icon' => '4'];
        }

        if (!empty($this->_aSpecZiYoubao[2]) && $this->_aSpecZiYoubao[2] > 0) {
            $aPriceDesc[] = [ 'content' => $this->_aSpecZiYoubao[0]];
        }

        if (!empty($sCouponDesc) && empty($sRedPacketDesc)) {
            $aPriceDesc[] = ['content' => $sCouponDesc, 'left_icon' => '1'];
        }

        $sFastCarPrice = $this->getDiXiaoDiDiscountLite();
        if (!empty($sFastCarPrice)) {
            $aPriceDesc[] = ['content' => $sFastCarPrice, 'left_icon' => '2'];
        }

        // 福利金（支付后返回）
        $aReward = $this->_oPriceItemFormatter->getFastCarSpecialRateWelfareLite();
        if (!empty($aReward) && !empty($aReward[0])  && empty($sRedPacketDesc)) {
            $aPriceDesc[] = ['content' => $aReward[0], 'left_icon' => '3'];
        }

        $aResponse['price_info_desc'] = $aPriceDesc;

        return $aResponse;
    }

    /**
     * 计算减价 总抵扣金额
     * @return int|string
     */
    private function _calcDecreaseAmount() {
        $fAmount = 0;
        if (empty($this->_aDecPriceItem)) {
            return $fAmount;
        }

        foreach ($this->_aDecPriceItem as $item) {
            if (is_numeric($item[2]) && $item[2] > 0) {
                $fAmount += $item[2];
            }
        }

        return $fAmount;
    }


    /**
     *  顺序：多因素一口价、动调相关费用、特价出租车、月卡、学生券、智慧套餐券、打车金、畅行卡、限时特惠 特惠出租车愿减、
     *       市民卡、优惠共抵、摇一摇
     *  Item结构为 priceDesc priceIcon amount
     * @return void
     */
    private function _fillDecPriceItem() {
        //券信息
        $aCoupon = $this->_getCouponDescLite();
        if (!empty($aCoupon[2])) {
            $this->_aDecPriceItem[] = $this->_getCouponDescLite();
        }

        //打车金
        $aReward = $this->_oPriceItemFormatter->getRewardsDescLite();
        if (!empty($aReward[2])) {
            $this->_aDecPriceItem[] = $aReward;
        }

        // 自由宝
        $this->_aSpecZiYoubao = $this->_oPriceItemFormatter->getSpecZiYoubaoPriceInfoDesc();

        //账单类优惠费用项
        $aDiscounts = $this->_aBillInfo['discounts_bill']['normal']['discounts'] ?? [];
        if (!empty($aDiscounts)) {
            foreach ($aDiscounts as $aDiscount) {
                if (!empty($aDiscount) && isset($aDiscount['fee_name'])
                    && isset($aDiscount['fee_amount']) && $aDiscount['fee_amount'] < 0
                ) {
                    $this->_aDecPriceItem[] = $this->_oPriceItemFormatter->getPlutusDiscountDesc($aDiscount);
                }
            }
        }
    }

    /**
     * @return array
     */
    private function _getCouponDescLite() {
        $aActivityInfo = $this->_aInfo['activity_info'][0];
        $aConfigText   = Config::text('config_passenger', 'dixiaodi');
        $aCouponDetail = $aActivityInfo['estimate_detail'];

        $sCouponDesc = '';
        $fAmount     = 0;
        if (isset($aCouponDetail['amount'], $aConfigText['estimate_coupon_info'])) {
            $fAmount     = NumberHelper::numberFormatDisplay($aCouponDetail['amount']);
            $sCouponDesc = Language::replaceTag(
                $aConfigText['estimate_coupon_info'],
                ['fee' => $fAmount,]
            );
        }

        return [$sCouponDesc, '', $fAmount];
    }
}
