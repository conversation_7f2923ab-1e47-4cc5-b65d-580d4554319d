<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo;

/**
 * 应急出行 EmergencyPriceDesc
 * Class EmergencyPriceDesc
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo
 */
class EmergencyPriceDesc
{
    /**
     * @var array
     */
    private $_aInfo;

    /**
     * EmergencyPriceDesc constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
    }

    /**
     * 构建返回值
     * @param array $aResponse $aResponse
     * @return mixed
     */
    public function build($aResponse) {
        //版本低于5.4.6 无需展示这个；
        if (\Bizlib\Utils\UtilHelper::compareAppVersion($this->_aInfo['common_info']['app_version'], '5.4.6') < 0) {
            return $aResponse;
        }

        //识别失败的情况下也无需展示
        if (empty($this->_aInfo['order_info']['n_tuple']['emergency_service_type'])) {
            return $aResponse;
        }

        $aPriceDesc = $aPriceDescIcon = $privilegeDesc = $aPriceInfoList = [];

        $oFormatter = new PriceItemFormatter($this->_aInfo);

        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getEmergencyDiscount($aPriceDesc,$aPriceDescIcon);

        list($aPriceDesc, $aPriceDescIcon) = $oFormatter->getEmergencyFreqSurplus($aPriceDesc,$aPriceDescIcon);

        $aResponse['price_desc']      = implode(' ', $aPriceDesc);
        $aResponse['price_desc_icon'] = implode(' ', $aPriceDescIcon);
        $aResponse = Common::formatPriceInfoDesc($aResponse, $aPriceDesc, $aPriceDescIcon, $this->_aInfo);
        return $aResponse;
    }
}
