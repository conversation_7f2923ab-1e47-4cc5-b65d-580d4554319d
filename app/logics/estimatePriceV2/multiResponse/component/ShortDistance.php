<?php
/**
 * Created by PhpStorm.
 * Date: 2020/6/28
 * Time: 20:32
 * <AUTHOR> <<EMAIL>>
 */

namespace PreSale\Logics\estimatePriceV2\multiResponse\component;

use PreSale\Logics\estimatePriceV2\DecisionLogic;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use BizLib\Utils\ProductCategory;
use TripcloudCommon\Utils\ApolloConf;
use TripcloudCommon\Constants\ApolloConfConstant;
use PreSale\Logics\estimatePriceV2\multiRequest\OrderInfo;

/**
 * Class ShortDistance
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component
 */
class ShortDistance
{
    protected $_aInfos = [];
    protected $_aShortDistanceNTuple = [];
    protected $_subProductList       = [];
    protected $_selectedFeeAmount    = [];
    protected $_shortDistanceItem    = [];
    protected $_productCategory      = ProductCategory::PRODUCT_CATEGORY_SHORT_DISTANCE;

    /**
     * ShortDistance constructor.
     * @param array $aInfos 真实车型信息
     */
    public function __construct($aInfos) {
        $this->_aInfos = $aInfos;
        $area          = $this->_aInfos[0]['order_info']['area'];
        $allConf       = ApolloConf::get(
            ApolloConfConstant::FILENAME_TRIPCLOUD_SHOW_AS_SHORT_DISTANCE,
            null,
            ApolloConf::NAMESPACE_DEFAULT,
            false
        );
        if (empty($allConf) || !isset($allConf[$area]) || empty($allConf[$area])) {
            $shortDistanceNTuple = [];
        } else {
            $shortDistanceNTuple = $allConf[$area];
        }

        $this->_aShortDistanceNTuple = $shortDistanceNTuple;
    }


    // 注释-待删
//    /**
//     * @param array $aEstimateData 预估数据
//     * @return array
//     */
//    public function buildShortDistance($aEstimateData) {
//        // 如果没有配置短途特惠，立即返回
//        if (empty($this->_aShortDistanceNTuple)) {
//            return $aEstimateData;
//        }
//
//        $response = [];
//
//        $shortDistanceIndex = -1;
//        // 遍历短途特惠车型，并设置短途特惠相对位置
//        foreach ($aEstimateData as $estimateDatum) {
//            $isTripcloud = $estimateDatum['is_tripcloud'];
//            // 设置短途特惠的相对位置，在所有的三方车型之前出现
//            if ($shortDistanceIndex < 0 && 1 == $isTripcloud) {
//                $response[]         = [];
//                $shortDistanceIndex = count($response) - 1;
//            }
//
//            $businessId   = $estimateDatum['business_id'];
//            $requireLevel = $estimateDatum['require_level'];
//            $comboType    = $estimateDatum['combo_type'];
//            $nTupleStr    = implode('_', [$businessId, $requireLevel, $comboType]);
//            if (in_array($nTupleStr, $this->_aShortDistanceNTuple)) {
//                $this->_subProductList[] = $estimateDatum;
//            } else {
//                $response[] = $estimateDatum;
//            }
//        }
//
//        // 如果没有检索到短途特惠，立即返回
//        if (empty($this->_subProductList)) {
//            return $aEstimateData;
//        }
//
//        // subProductList 按照价格排序
//        usort(
//            $this->_subProductList,
//            function ($a, $b) {
//                return $a['fee_amount'] - $b['fee_amount'];
//            }
//        );
//
//        $this->buildShortDistanceItem();
//        $response[$shortDistanceIndex] = $this->_shortDistanceItem;
//
//        return $response;
//    }

    // 注释-待删
//    /**
//     * 构建短途特惠item
//     * @return void
//     */
//    protected function buildShortDistanceItem() {
//        $this->setBaseField();
//        $this->setIntroMsg();
//        $this->setSelectAndRecommendField(); // 先设置选中，才能计算费用项
//        $this->setSubProductList();
//    }

// 注释-待删
//    /**
//     * 构建基础字段
//     * @return void
//     */
//    protected function setBaseField() {
//        $this->_shortDistanceItem['category_id'] = $this->_subProductList[0]['category_id'];
//    }

    /**
     * 构建intro_msg和sub_title_list
     * @return void
     */
    protected function setIntroMsg() {
        $aConfig = MainDataRepo::getBasicConfByProductCategory($this->_productCategory);
        $this->_shortDistanceItem['intro_msg'] = !empty($aConfig) ? $aConfig['intro_msg'] : '';
        $aSubTitleList = [];
        if (!empty($aConfig) && !empty($aConfig['sub_title_text'])) {
            $aSubTitle = $aConfig['sub_title_text'];
            if (is_array($aSubTitle) && isset($aSubTitle[0]['item_config'])) {
                $aSubTitleList = array_column($aSubTitle,'item_config');
            }
        }

        $this->_shortDistanceItem['sub_title_list'] = $aSubTitleList;
    }

    // 注释-待删
//    /**
//     * 构建推荐和选中字段
//     * @return void
//     */
//    protected function setSelectAndRecommendField() {
//        // 设置导流字段
//        $this->_shortDistanceItem['recommend_type'] = 0;
//        $this->_shortDistanceItem['select_type']    = 0;
//        foreach ($this->_subProductList as &$productItem) {
//            // 设置commend_type
//            if ((1 == $productItem['recommend_type']) && (0 == $this->_shortDistanceItem['recommend_type'])) {
//                $this->_shortDistanceItem['recommend_type'] = 1;
//            }
//
//            // 设置select_type
//            if (1 == $productItem['select_type']) {
//                $this->_selectedFeeAmount[] = $productItem['fee_amount'];
//                if (0 == $this->_shortDistanceItem['select_type']) {
//                    $this->_shortDistanceItem['select_type'] = 1;
//                }
//            }
//
//            // 车型右边的推荐语，内页没有，只保留外页
//            if (isset($productItem['recommend_tag']) && !empty($productItem['recommend_tag'])) {
//                if (!isset($this->_shortDistanceItem['recommend_tag']) || empty($this->_shortDistanceItem['recommend_tag'])) {
//                    $this->_shortDistanceItem['recommend_tag'] = $productItem['recommend_tag'];
//                }
//
//                unset($productItem['recommend_tag']);
//            }
//
//            // 勾选按钮上的气泡tag，内页没有，只保留外页
//            if (isset($productItem['selection_tag']) && !empty($productItem['selection_tag'])) {
//                if (!isset($this->_shortDistanceItem['selection_tag']) || empty($this->_shortDistanceItem['selection_tag'])) {
//                    $this->_shortDistanceItem['selection_tag'] = $productItem['selection_tag'];
//                }
//
//                unset($productItem['selection_tag']);
//            }
//
//            //推荐置顶标识
//            if (!empty($productItem['product_category'])) {
//                $aDdsProduct = DecisionLogic::getInstance()->getProductInfoByCategoryId($productItem['product_category']);
//                if (isset($aDdsProduct['first_show']) && $aDdsProduct['first_show']) {
//                    $this->_shortDistanceItem['form_show_type'] = OrderInfo::FORM_SHOW_TYPE_FIRST;
//                }
//            }
//        }
//    }

// 注释-待删
//    /**
//     * 构建sub_product
//     * @return void
//     */
//    protected function setSubProductList() {
//        $this->_shortDistanceItem['sub_product'] = $this->_subProductList;
//    }
}
