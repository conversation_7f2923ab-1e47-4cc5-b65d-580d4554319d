<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\extraDescInfo;

use PreSale\Models\fee\FeeDetailTemplate;

/**
 * Class NormalExtraDesc
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\extraDescInfo
 */
class NormalExtraDesc
{

    /**
     * @var array
     */
    protected $_aInfo;

    /**
     * @var array
     */
    protected $_aActivityInfo;

    /**
     * NormalExtraDesc constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo         = $aInfo;
        $this->_aActivityInfo = $aInfo['activity_info'][0];
    }

    /**
     * @param array $aResponse aResponse
     * @return mixed
     */
    public function build($aResponse) {
        $sExtraDesc = $this->_aActivityInfo['estimate_detail']['extra_desc'] ?? '';
        //如果是学生券，不在这个字段透出，放在券文案透出
        if (FeeDetailTemplate::isOnlyStudentCardCoupon($this->_aActivityInfo['coupon_info'])) {
            $sExtraDesc = '';
        }

        $aResponse['extra_desc'] = $sExtraDesc;
        return $aResponse;
    }
}
