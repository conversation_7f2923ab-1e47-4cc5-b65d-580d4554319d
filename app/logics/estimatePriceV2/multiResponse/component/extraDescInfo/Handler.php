<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\extraDescInfo;

use BizCommon\Utils\Order;
use BizLib\Utils\Horae;
use BizLib\Utils\Product;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;

/**
 * extra_desc
 * Class Handler
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\extraDescInfo
 */
class Handler
{

    /**
     * @param array $aInfo aInfo
     * @return null|NormalExtraDesc
     */
    public static function select($aInfo) {
        if (Product::isAnyCar($aInfo['order_info']['product_id'])) {
            return null;
        }

        if (Util::isCarpool($aInfo)) {
            return null;
        }

        if (Order::isSpecialRateV2($aInfo['order_info']) || $aInfo['order_info']['n_tuple']['is_special_price']) {
            return null;
        }

        // if (Util::isKFlower($aInfo)) {
        //     return null;
        // }
        return new NormalExtraDesc($aInfo);
    }
}
