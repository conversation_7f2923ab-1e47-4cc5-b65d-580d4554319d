<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\promoteSalesTip;

use BizCommon\Constants\OrderNTuple;
use BizLib\Libraries\RedisDB;
use BizLib\Utils\Common as UtilsCommon;
use Nuwa\ApolloSDK\Apollo as NuwaApollo;
use PreSale\Models\carTicket\BizCarTicket;

/**
 * Class NormalIconInfo
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\recommendCar
 */
class NormalPromoteSales
{
    protected $_aInfo;

    /**
     * NormalIconInfo constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
    }

    /**
     * @param array $aResponse response
     * @return mixed
     */
    public function build($aResponse) {
        $this->_buildCarTicket($aResponse);
        return $aResponse;
    }


    /**
     * 构造车票的营销文案
     * @param array $aResponse $aResponse
     * @return void
     */
    private function _buildCarTicket(&$aResponse) {

        $oApollo       = new NuwaApollo();
        $oApolloResult = $oApollo->featureToggle(
            'bubble_communicate_component',
            [
                'key'              => $this->_aInfo['passenger_info']['phone'],
                'phone'            => $this->_aInfo['passenger_info']['phone'],
                'city'             => $this->_aInfo['order_info']['area'],
                'product_id'       => $this->_aInfo['order_info']['product_id'],
                'car_level'        => $this->_aInfo['order_info']['require_level'],
                'lang'             => $this->_aInfo['common_info']['lang'],
                'app_version'      => $this->_aInfo['common_info']['app_version'],
                'access_key_id'    => $this->_aInfo['common_info']['access_key_id'],
                'config'           => 0,
                'combo_type'       => $this->_aInfo['order_info']['combo_type'],
                'menu_id'          => $this->_aInfo['order_info']['menu_id'],
                'product_category' => $this->_aInfo['order_info']['product_category'],
                'page_type'        => $this->_aInfo['order_info']['page_type'],
            ]
        );
        $bAllow        = $oApolloResult->allow();
        if (!$bAllow) {
            //车票当前只是支持 快车产品线、600车型；
            if (OrderNTuple::COMMON_PRODUCT_ID_FAST_CAR == $this->_aInfo['order_info']['business_id']
                && OrderNTuple::DIDI_PUTONG_CAR_LEVEL == $this->_aInfo['order_info']['require_level']
                && in_array($this->_aInfo['order_info']['payments_type'],[\BizCommon\Models\Order\Order::NOT_BUSINESS_USER, -1, \BizCommon\Models\Order\Order::BUSINESS_PAY_BY_PERSON_WITH_REFOUND])
            ) {
                $aActivityInfo = (new BizCarTicket())->queryRecActivity(
                    $this->_aInfo['passenger_info']['token'],
                    $this->_aInfo['order_info']['to_lat'],
                    $this->_aInfo['order_info']['to_lng'],
                    $this->_aInfo['order_info']['business_id']
                );

                if (empty($aActivityInfo)) {
                    return;
                }

                $aResponse['promote_sales_text']['rule_type'][] = Common::RULE_TYPE_CAR_TICKET_FLASH;
            }
        }
    }
}
