<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\promoteSalesTip;

use PreSale\Logics\estimatePriceV2\multiResponse\Util;

/**
 * Class Handler
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\promoteSales
 */
class Handler
{
    /**
     * @param array $aInfo aInfo
     * @return NormalPromoteSales
     */
    public static function select($aInfo) {
        return new NormalPromoteSales($aInfo);
    }
}
