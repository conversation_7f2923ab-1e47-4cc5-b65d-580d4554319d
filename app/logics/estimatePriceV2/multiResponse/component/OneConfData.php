<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component;

use BizLib\Utils\Common as UtilsCommon;
use PreSale\Logics\estimatePriceV2\DecisionLogic;
use PreSale\Logics\estimatePriceV2\ParamsLogic;

/**
 * Class OneConfData
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component
 */
class OneConfData
{

    private $_aOneConf;
    private $_aInfos;

    /**
     * OneConfData constructor.
     * @param array $aInfos aInfos
     */
    public function __construct($aInfos) {
        $this->_aOneConf  = ParamsLogic::getInstance()->getOneConf() ?? [];
        $this->_oDecision = DecisionLogic::getInstance();
        $this->_aInfos    = $aInfos;
    }

     // 注释-待删
//    /**
//     * @param array $aEstimateData aEstimateData
//     * @return array
//     */
//    public function setDeletionInfoByOneConf($aEstimateData) {
//        $aEstimateIconData = array();
//        $aInfo      = current($this->_aInfos);
//        $bIsLiteApp = UtilsCommon::isLiteVersion($aInfo['common_info']['app_version'], $aInfo['common_info']['client_type'], $aInfo['order_info']['channel']);
//        foreach ($aEstimateData as $aData) {
//            $bIsFind = false;
//            foreach ($this->_aOneConf as $oneConf) {
//                if ($aData['business_id'] == $oneConf['business_id']
//                    && $aData['require_level'] == $oneConf['require_level']
//                    && $aData['combo_type'] == $oneConf['combo_type']
//                    && $aData['is_special_price'] == $oneConf['is_special_price']
//                ) {
//                    $bIsFind = true;
//                    $aData   = $this->_setEstimateData($aData, $oneConf, $bIsLiteApp);
//                    break;
//                }
//
//                if (isset($oneConf['estimate_id'])
//                    && $aData['estimate_id'] == $oneConf['estimate_id']
//                    && ParamsLogic::getInstance()->hitMultiEstimateForPremium()
//                ) {
//                    $bIsFind = true;
//                    $aData   = $this->_setEstimateData($aData, $oneConf, $bIsLiteApp);
//                    break;
//                }
//            }
//
//            if ($bIsLiteApp && false == $bIsFind) {
//                foreach ($this->_aOneConf as $oneConf) {
//                    if ($aData['business_id'] == $oneConf['business_id']
//                        && $aData['require_level'] == $oneConf['require_level']
//                    ) {
//                        $bIsFind = true;
//                        $aData   = $this->_setEstimateData($aData, $oneConf, $bIsLiteApp);
//                        break;
//                    }
//                }
//            }
//
//            $aEstimateIconData[] = $aData;
//        }
//
//        return $aEstimateIconData;
//    }

// 注释-待删
//    /**
//     * @param array $aEstimateData aEstimateData
//     * @param array $oneConf       oneConf
//     * @param bool  $bIsLiteApp    bIsLiteApp
//     * @return mixed
//     */
//    private function _setEstimateData($aEstimateData, $oneConf, $bIsLiteApp = false) {
//        $aData = $aEstimateData;
//        if (empty($aData['gray_icon']) && isset($oneConf['gray_icon'])) {
//            $aData['gray_icon'] = $oneConf['gray_icon'];
//        }
//
//        if (empty($aData['light_icon'])) {
//            // 兜底逻辑,专车gray_icon在顶导中都没配。
//            if (isset($oneConf['light_icon']) && !empty($oneConf['light_icon'])) {
//                $aData['light_icon'] = $oneConf['light_icon'];
//            } elseif (!empty($oneConf['gray_icon']) && ParamsLogic::getInstance()->hitMultiEstimateForPremium()) {
//                $aData['light_icon'] = $oneConf['gray_icon'];
//            }
//        }
//
//        if (empty($aData['map_icon']) && isset($oneConf['map_icon'])) {
//            $aData['map_icon'] = $oneConf['map_icon'];
//        }
//
//        if (empty($aData['intro_msg'])) {
//            if (!empty($aData['title']) && ParamsLogic::getInstance()->hitMultiEstimateForPremium()) {
//                $aData['intro_msg'] = $aData['title'];
//            } elseif (!empty($oneConf['name'])) {
//                $aData['intro_msg'] = $oneConf['name'];
//            }
//        }
//
//        if (empty($aData['sub_title']) && !empty($oneConf['sub_title']) && ParamsLogic::getInstance()->hitMultiEstimateForPremium()) {
//            $aData['sub_title'] = $oneConf['sub_title'];
//        }
//
//        if ($bIsLiteApp) {
//            if (isset($oneConf['controller_info'])) {
//                $aControllerInfo          = $this->_checkControllerInfo($aData, $oneConf['controller_info']);
//                $aData['controller_info'] = $aControllerInfo;
//            }
//
//            if (isset($oneConf['service_dentifier'])) {
//                $aData['service_dentifier'] = $oneConf['service_dentifier'];
//            }
//
//            if (isset($oneConf['category_id'])) {
//                $aData['category_id'] = $oneConf['category_id'];
//            }
//
//            if (isset($oneConf['category_show_msg'])) {
//                $aData['category_show_msg'] = $oneConf['category_show_msg'];
//            }
//        }
//
//        return $aData;
//    }

// 注释-待删
//    /**
//     * @param array $aEstimateData   aEstimateData
//     * @param array $aControllerInfo aControllerInfo
//     * @return array
//     */
//    private function _checkControllerInfo($aEstimateData, $aControllerInfo) {
//        if (in_array('user_pay_info', $aControllerInfo)) {
//            if (!isset($aEstimateData['user_pay_info'])
//                || empty($aEstimateData['user_pay_info'])
//            ) {
//                $aControllerInfo = array_merge(array_diff($aControllerInfo, array('user_pay_info')));
//            }
//        }
//
//        if (in_array('carpool_seat_config', $aControllerInfo)) {
//            if (!isset($aEstimateData['carpool_seat_config'])
//                || empty($aEstimateData['carpool_seat_config'])
//            ) {
//                $aControllerInfo = array_merge(array_diff($aControllerInfo, array('carpool_seat_config')));
//            }
//        }
//
//        return $aControllerInfo;
//    }
}
