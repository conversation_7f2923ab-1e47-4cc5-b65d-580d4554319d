<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\specialPriceTip;

use BizCommon\Utils\Order;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Horae;
use BizLib\Utils\Product;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;

/**
 * Class Handler
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\specialPriceTip
 */
class Handler
{
    /**
     * @param array $aInfo aInfo
     * @return CarpoolSpecialPriceTrip|HKTaxiSpecialPriceTip|NormalSpecialPriceTip|SpecialRateSpecialPriceTrip
     */
    public static function select($aInfo) {
        if (Util::isCarpool($aInfo)) {
            return New CarpoolSpecialPriceTrip($aInfo);
        }

        if (Order::isSpecialRateV2($aInfo['order_info'])) {
            return New SpecialRateSpecialPriceTrip($aInfo);
        }

        if (Product::COMMON_PRODUCT_ID_HK_TAXI == $aInfo['order_info']['business_id']) {
            return new HKTaxiSpecialPriceTip($aInfo);
        }

        return new NormalSpecialPriceTip($aInfo);
    }
}
