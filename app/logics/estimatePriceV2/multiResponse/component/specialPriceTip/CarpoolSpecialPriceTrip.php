<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\specialPriceTip;

use BizLib\Libraries\RedisDB;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\Language;
use BizLib\Utils\ProductCategory;
use Disf\SPL\Trace;
use BizLib\Client\Cache\StorageClient;
use PreSale\Logics\estimatePriceV2\bill\CommonBillLogic;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;
use BizLib\Log as NuwaLog;
use Nuwa\ApolloSDK\Apollo as NuwaApollo;
use BizLib\Utils\Horae;

//use PreSale\Logics\passenger\SpecialFeeLogic;

/**
 * Class CarpoolSpecialPriceTrip
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\specialPriceTip
 */
class CarpoolSpecialPriceTrip
{
    private $_aInfo;

    private $_sCarLevel;

    private $_oFusionClient;

    // const SPECIAL_PRICE_COMPONENT_EXPIRE_TIME = 3000;
    const SPECIAL_PRICE_COMPONENT_EXPIRE_TIME = 60;

    /**
     * NormalSpecialPrice constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo         = $aInfo;
        $this->_sCarLevel     = $aInfo['order_info']['require_level'];
        $this->_oFusionClient = StorageClient::getInstance(StorageClient::STORE);
    }

    /**
     * 特殊价格沟通组件数据获取
     * @param array $aResponse aResponse
     * @return array
     */
    public function build($aResponse) {
        if (!Common::isHitSpecialRuleData($this->_aInfo)) {
            return $aResponse;
        }

        $aRuleType = [];
        $aBills    = $this->_aInfo['bill_info']['bills'][$this->_sCarLevel];

        $aRuleType = Common::getCommonRuleType($aBills, $aRuleType);

        // 远途特快一口价
        if (Horae::isInterCityCarpoolNewMode($this->_aInfo['order_info']['combo_type'], $this->_aInfo['order_info']['carpool_type'])) {
            $aRuleType[] = Common::RULE_TYPE_INTER_CITY_CARPOOL_NEW_MODE;
        }

        /*
         * 因为目前冒泡页的沟通位展示的优先级为：
         * 企业付沟通 > 滴滴支付沟通 > 价格沟通 > 车大联盟（只有快车） > 拼车活动 > 大额优惠 > 乘客任务 > 权益沟通 > 智能套餐 > 超级会员 > 其他 （兜底是隐私保护）
         * 原有痛点：在拼车活动期间，由于价格沟通优先级大于拼车活动，在单勾拼车品类时，拼车活动被价格沟通覆盖。。活动沟通位展示不出来
         * 流程逻辑：价格沟通位的数据，是在预估渲染的时候，写入到redis中的，在调用沟通组件接口pGetCommunicateInfo中，会去获取redis中的数据,以便于后续确保命中沟通位
         * 解决办法：在预估的渲染时，入redis的代码位置加上阿波罗灰度开关配置，如果需要展示拼车活动（只限市内拼车）的沟通位，那就将开关打开，这样就不向redis中写入数据
         */
        $bSwitch = NuwaApollo::getInstance()->featureToggle(
            'communicate_carpool_grayscale',
            [
                'key'           => $this->_aInfo['passenger_info']['pid'],
                'phone'         => $this->_aInfo['passenger_info']['phone'],
                'access_key_id' => $this->_aInfo['common_info']['access_key_id'],
                'lang'          => $this->_aInfo['common_info']['lang'],
                'city'          => $this->_aInfo['order_info']['area'],
                'app_version'   => $this->_aInfo['common_info']['app_version'],
            ]
        )->allow();
        if (!$bSwitch) {
            // 市内拼车两口价
            if (\BizCommon\Utils\Horae::isCarpoolUnSuccessFlatPrice($this->_aInfo['order_info'])) {
                if (MainDataRepo::isCarpoolUnSuccessFlatPriceShowAsCapPrice($this->_aInfo)) {
                    $aRuleType[] = Common::RULE_TYPE_CARPOOL_DUAL_PRICE_MERGE;
                } else {
                    $aRuleType[] = Common::RULE_TYPE_CARPOOL_DUAL_PRICE;
                }
            }
        }

        //未命中特殊计价
        if (empty($aRuleType)) {
            return $aResponse;
        }

        if (1 == count($aRuleType)) {
            $aResponse['special_price_text'] = ['rule_type' => $aRuleType,];

            switch ($aRuleType[0]) {
                case Common::RULE_TYPE_CROSSCITY:
                    $aResponse['special_price_text']['text']
                        = Language::getTextFromDcmp(
                            'special_price_rule_explain-price_text_crosscity',
                            ['fee' => $this->_aInfo['bill_info']['bills'][$this->_sCarLevel]['cross_city_fee']]
                        );
                    break;
                case Common::RULE_TYPE_EXTRA_FEE:
                    $aResponse['special_price_text']['text']
                        = Language::getTextFromDcmp('special_price_rule_explain-price_text_highway_fee');
                    break;
                case Common::RULE_TYPE_RED_PACKET:
                    $aResponse['special_price_text']['text']
                        = Language::getTextFromDcmp('special_price_rule_explain-text_red_packet');
                    break;
                case Common::RULE_TYPE_INTER_CITY_CARPOOL_NEW_MODE:
                    $aResponse['special_price_text']['text']
                        = Language::getTextFromDcmp('special_price_rule_explain-intercity_carpool_new_mode_cap_price');
                    break;
                case Common::RULE_TYPE_CARPOOL_DUAL_PRICE:
                    $aResponse['special_price_text']['text']
                        = Language::getTextFromDcmp('special_price_rule_explain-carpool_dual_price');
                    break;
                case Common::RULE_TYPE_CARPOOL_DUAL_PRICE_MERGE:
                    $aResponse['special_price_text']['text']
                        = Language::getTextFromDcmp('special_price_rule_explain-carpool_dual_price_merge');
                    break;
                default:
                    break;
            }
        } else { //命中多种特殊计价
            $aResponse['special_price_text'] = [
                'text'      => Language::getTextFromDcmp('special_price_rule_explain-price_text_multiple'),
                'rule_type' => $aRuleType,
            ];
        }

        if (Util::isDaCheAnyCar($this->_aInfo) && Common::isForceNotice($this->_aInfo['passenger_info'])) {
            $aResponse = Common::buildDaCheForceNotice($aResponse,null);
        }

        if (!empty($aResponse['special_price_text'])) {
            $oApollo       = new NuwaApollo();
            $oApolloResult = $oApollo->featureToggle(
                'bubble_communicate_component',
                [
                    'key'              => $this->_aInfo['passenger_info']['phone'],
                    'phone'            => $this->_aInfo['passenger_info']['phone'],
                    'city'             => $this->_aInfo['order_info']['area'],
                    'product_id'       => $this->_aInfo['order_info']['product_id'],
                    'car_level'        => $this->_aInfo['order_info']['require_level'],
                    'lang'             => $this->_aInfo['common_info']['lang'],
                    'app_version'      => $this->_aInfo['common_info']['app_version'],
                    'access_key_id'    => $this->_aInfo['common_info']['access_key_id'],
                    'config'           => 0,
                    'combo_type'       => $this->_aInfo['order_info']['combo_type'],
                    'menu_id'          => $this->_aInfo['order_info']['menu_id'],
                    'product_category' => $this->_aInfo['order_info']['product_category'],
                    'page_type'        => $this->_aInfo['order_info']['page_type'],
                ]
            );
            $bAllow        = $oApolloResult->allow();
            if ($bAllow) {
                $sEstimateId = $this->_aInfo['order_info']['estimate_id'];
                $oRedisdb    = \PreSale\Logics\redismove\RedisWrapper::getInstance(P_SPECIAL_PRICE_COMPONENT);
                $sKey        = UtilsCommon::getRedisPrefix(P_SPECIAL_PRICE_COMPONENT).$sEstimateId;
                $oRedisdb->setex($sKey, self::SPECIAL_PRICE_COMPONENT_EXPIRE_TIME, json_encode($aResponse['special_price_text']));
                unset($aResponse['special_price_text']);
            }
        }

        return $aResponse;
    }

    // 注释-待删
//    /**
//     * 附加费(高速费)强弹判断
//     * @param array $aRuleType 特殊计价类型
//     * @return bool
//     */
//    private function _isHitForceNotice($aRuleType) {
//        $iPid = $this->_aInfo['passenger_info']['pid'];
//
//        $sTraceId = Trace::traceId();
//        if (in_array(Common::RULE_TYPE_EXTRA_FEE, $aRuleType)) {
//            $sPrefix = Common::PREFIX_TYPE_EXTRA;
//            $sKey    = $sPrefix . $iPid;
//            $sRes    = $this->_oFusionClient->smembers($sKey);
//            if (!empty($sRes) && in_array($sTraceId, $sRes)) {
//                return true;
//            }
//
//            if (count($sRes) >= 3) {
//                return false;
//            } else {
//                $bRet = $this->_oFusionClient->sadd($sKey, $sTraceId);
//                if (!$bRet) {
//                    NuwaLog::warning(
//                        sprintf(
//                            'set fusion fail key:%s|pid:%s',
//                            $sKey,
//                            (string)($iPid)
//                        )
//                    );
//                }
//
//                return true;
//            }
//        }
//
//        return false;
//    }
}
