<?php
/**
 * Created by PhpStorm.
 * User: didi
 * Date: 2019/12/18
 * Time: 上午11:23
 * <AUTHOR> <<EMAIL>>
 */

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\specialPriceTip;

use BizLib\Utils\Language;
use BizLib\Utils\PublicLog;

/**
 * Class KFlowerSpecialPriceTip
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\specialPriceTip
 * @deprecated 无用
 */
class KFlowerSpecialPriceTip
{
    // 注释-待删
//    private $_aInfo;
//
//    private $_sCarLevel;
//
//    /**
//     * NormalSpecialPrice constructor.
//     * @param array $aInfo aInfo
//     */
//    public function __construct($aInfo) {
//        $this->_aInfo     = $aInfo;
//        $this->_sCarLevel = $aInfo['order_info']['require_level'];
//    }
//
//    /**
//     * 特殊价格沟通组件数据获取
//     * @param array $aResponse aResponse
//     * @return array
//     */
//    public function build($aResponse) {
//        $aRuleType = [];
//
//        // 高速费
//        $iHighwayFee = $this->_aInfo['bill_info']['bills'][$this->_sCarLevel]['extra']['highway_fee_value'] ?? 0;
//        if ($iHighwayFee) {
//            $aRuleType[] = Common::RULE_TYPE_EXTRA_FEE;
//        }
//
//        //未命中特殊计价
//        if (empty($aRuleType)) {
//            return $aResponse;
//        }
//
//        if (in_array(Common::RULE_TYPE_EXTRA_FEE, $aRuleType)) {
//            $aResponse['special_price_text']         = ['rule_type' => $aRuleType,];
//            $aResponse['special_price_text']['text']
//                = Language::getTextFromDcmp('special_price_rule_explain-price_text_highway_fee');
//            $this->_addPublicLog();
//        }
//
//        return $aResponse;
//    }
//
//    /**
//     * public日志，记录预估命中特殊计价的预估信息
//     * @return void
//     */
//    private function _addPublicLog() {
//        $sEstimateId    = $this->_aInfo['bill_info']['estimate_id'];
//        $aPassengerInfo = $this->_aInfo['passenger_info'];
//        $aOrderInfo     = $this->_aInfo['order_info'];
//        $aCommonInfo    = $this->_aInfo['common_info'];
//        $aLogData       = [
//            'opera_stat_key' => 'kflower_estimate_price_hit_special_price',
//            'area'           => $aOrderInfo['area'],
//            'business_id'    => $aOrderInfo['business_id'],
//            'product_id'     => $aOrderInfo['product_id'],
//            'require_level'  => $aOrderInfo['require_level'],
//            'access_key_id'  => $aCommonInfo['access_key_id'],
//            'phone'          => $aPassengerInfo['phone'],
//            'uid'            => $aPassengerInfo['uid'],
//            'estimate_id'    => $sEstimateId,
//            'highway_fee'    => $this->_aInfo['bill_info']['bills'][$this->_sCarLevel]['extra']['highway_fee_value'] ?? 0,
//        ];
//        PublicLog::writeLogForOfflineCal('public', $aLogData);
//    }
}
