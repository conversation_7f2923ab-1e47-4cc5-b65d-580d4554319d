<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\specialPriceTip;

use BizLib\Libraries\RedisDB;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\Language;
use Disf\SPL\Trace;
use BizLib\Client\Cache\StorageClient;
use PreSale\Logics\passenger\SpecialFeeLogic;
use Nuwa\ApolloSDK\Apollo as NuwaApollo;
use BizLib\Log as NuwaLog;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;

/**
 * Class AnyCarSpecialPriceTrip
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\specialPriceTip
 * @deprecated 无用
 */
class AnyCarSpecialPriceTrip
{
    // 注释-待删
//    private $_aInfo;
//
//    private $_sCarLevel;
//
//    private $_oFusionClient;
//
//    // const SPECIAL_PRICE_COMPONENT_EXPIRE_TIME = 3000;
//    const SPECIAL_PRICE_COMPONENT_EXPIRE_TIME = 60;
//
//    /**
//     * NormalSpecialPrice constructor.
//     * @param array $aInfo aInfo
//     */
//    public function __construct($aInfo) {
//        $this->_aInfo         = $aInfo;
//        $this->_sCarLevel     = $aInfo['order_info']['require_level'];
//        $this->_oFusionClient = StorageClient::getInstance(StorageClient::STORE);
//    }
//
//    /**
//     * 特殊价格沟通组件数据获取
//     * @param array $aResponse aResponse
//     * @return array
//     */
//    public function build($aResponse) {
//        if (!Common::isHitSpecialRuleData($this->_aInfo)) {
//            return $aResponse;
//        }
//
//        $aRuleType      = [];
//        $oApollo        = new NuwaApollo();
//        $oFeatureToggle = $oApollo->featureToggle(
//            'toll_node_route_explain',
//            [
//                'key'   => $this->_aInfo['passenger_info']['pid'],
//                'phone' => $this->_aInfo['passenger_info']['phone'],
//                'city'  => $this->_aInfo['order_info']['area'],
//            ]
//        );
//        $bHitTollNode   = $oFeatureToggle->allow();
//        $bHitHighWayTollNode = false; //是否命中有高速费的疫情路线沟通
//
//        //高速费
//        foreach ($this->_aInfo['bill_info']['multi_info'] as $aProductInfo) {
//            if (isset($aProductInfo['extra']['highway_fee_value']) && $aProductInfo['extra']['highway_fee_value'] > 0) {
//                $aRuleType[] = Common::RULE_TYPE_EXTRA_FEE;
//                if ($bHitTollNode) {
//                    $aRuleType[]         = Common::RULE_TYPE_TOLL_NODE_WITH_EXTRA;
//                    $bHitHighWayTollNode = true;
//                }
//
//                break;
//            }
//        }
//
//        //春节红包
//        foreach ($this->_aInfo['bill_info']['multi_info'] as $aProductInfo) {
//            if (isset($aProductInfo['red_packet']) && $aProductInfo['red_packet'] > 0) {
//                $aRuleType[] = Common::RULE_TYPE_RED_PACKET;
//                break;
//            }
//        }
//
//        //疫情路线沟通
//        foreach ($this->_aInfo['bill_info']['multi_info'] as $aProductInfo) {
//            if (isset($aProductInfo['extra']['have_toll_station']) && $aProductInfo['extra']['have_toll_station']
//                && !in_array(Common::RULE_TYPE_EXTRA_FEE, $aRuleType) && $bHitTollNode
//            ) {
//                $aRuleType[] = Common::RULE_TYPE_TOLL_NODE;
//                break;
//            }
//        }
//
//        //未命中特殊计价
//        if (empty($aRuleType)) {
//            return $aResponse;
//        }
//
//        if (1 == count($aRuleType)
//            || (2 == count($aRuleType) && $bHitHighWayTollNode)
//        ) {
//            $aResponse['special_price_text'] = ['rule_type' => $aRuleType,];
//
//            switch ($aRuleType[0]) {
//                case Common::RULE_TYPE_EXTRA_FEE:
//                    $aResponse['special_price_text']['text']
//                        = Language::getTextFromDcmp('special_price_rule_explain-price_text_highway_fee');
//                    break;
//                case Common::RULE_TYPE_TOLL_NODE_WITH_EXTRA:
//                    $aResponse['special_price_text']['text']
//                        = Language::getTextFromDcmp('special_price_rule_explain-price_text_highway_fee');
//                    break;
//                case Common::RULE_TYPE_RED_PACKET:
//                    $aResponse['special_price_text']['text']
//                        = Language::getTextFromDcmp('special_price_rule_explain-text_red_packet');
//                    break;
//                case Common::RULE_TYPE_TOLL_NODE:
//                    $aResponse['special_price_text']['text']
//                        = Language::getTextFromDcmp('special_price_rule_explain-text_toll_node');
//                    break;
//                default:
//                    break;
//            }
//        } else { //命中多种特殊计价
//            $aResponse['special_price_text'] = [
//                'text'      => Language::getTextFromDcmp('special_price_rule_explain-price_text_multiple'),
//                'rule_type' => $aRuleType,
//            ];
//        }
//
//        //是否命中附加费强弹
//        if ($this->_isHitForceNotice($aRuleType)) {
//            $sEstamateId = $this->_aInfo['order_info']['estimate_id'] ?? '';
//            $sLang       = $this->_aInfo['common_info']['lang'] ?? '';
//            $sAppversion = $this->_aInfo['common_info']['app_version'] ?? '';
//            $aParams     = [
//                'estimate_id' => $sEstamateId,
//                'lang'        => $sLang,
//                'rule_type'   => $aRuleType,
//                'app_version' => $sAppversion,
//                'phone'       => $this->_aInfo['passenger_info']['phone'] ?? '',
//            ];
//
//            if (!empty($aParams['estimate_id']) && !empty($aParams['lang'])) {
//                $oSpecialFeeLogic = new SpecialFeeLogic($aParams);
//                $aRet = $oSpecialFeeLogic->getSpecialRuleText();
//                if (!empty($aRet) && !Util::isDaCheAnyCar($this->_aInfo)) {
//                    $aResponse['special_price_info'] = $aRet;
//                }
//
//                if (Util::isDaCheAnyCar($this->_aInfo)) {
//                    $aResponse = Common::buildDaCheForceNotice($aResponse,$aRet);
//                }
//            }
//        }
//
//        if (!empty($aResponse['special_price_text'])) {
//            $oApollo       = new NuwaApollo();
//            $oApolloResult = $oApollo->featureToggle(
//                'bubble_communicate_component',
//                [
//                    'key'              => $this->_aInfo['passenger_info']['pid'],
//                    'phone'            => $this->_aInfo['passenger_info']['phone'],
//                    'city'             => $this->_aInfo['order_info']['area'],
//                    'product_id'       => $this->_aInfo['order_info']['product_id'],
//                    'car_level'        => $this->_aInfo['order_info']['require_level'],
//                    'lang'             => $this->_aInfo['common_info']['lang'],
//                    'app_version'      => $this->_aInfo['common_info']['app_version'],
//                    'access_key_id'    => $this->_aInfo['common_info']['access_key_id'],
//                    'config'           => 0,
//                    'combo_type'       => $this->_aInfo['order_info']['combo_type'],
//                    'menu_id'          => $this->_aInfo['order_info']['menu_id'],
//                    'product_category' => $this->_aInfo['order_info']['product_category'],
//                    'page_type'        => $this->_aInfo['order_info']['page_type'],
//                ]
//            );
//            $bAllow        = $oApolloResult->allow();
//            if ($bAllow) {
//                $sEstimateId = $this->_aInfo['order_info']['estimate_id'];
//                $oRedisdb    = \PreSale\Logics\redismove\RedisWrapper::getInstance(P_SPECIAL_PRICE_COMPONENT);
//                $sKey        = UtilsCommon::getRedisPrefix(P_SPECIAL_PRICE_COMPONENT).$sEstimateId;
//                $oRedisdb->setex($sKey, self::SPECIAL_PRICE_COMPONENT_EXPIRE_TIME, json_encode($aResponse['special_price_text']));
//                unset($aResponse['special_price_text']);
//            }
//        }
//
//        return $aResponse;
//    }
//
//    /**
//     * 附加费(高速费)强弹判断
//     * @param array $aRuleType 特殊计价类型
//     * @return bool
//     */
//    private function _isHitForceNotice($aRuleType) {
//        $iPid = $this->_aInfo['passenger_info']['pid'];
//
//        $sTraceId = Trace::traceId();
//        if (in_array(Common::RULE_TYPE_EXTRA_FEE, $aRuleType)) {
//            $sPrefix = Common::PREFIX_TYPE_EXTRA;
//            $sKey    = $sPrefix . $iPid;
//            $sRes    = $this->_oFusionClient->smembers($sKey);
//            if (!empty($sRes) && in_array($sTraceId, $sRes)) {
//                return true;
//            }
//
//            if (count($sRes) >= 3) {
//                return false;
//            } else {
//                $bRet = $this->_oFusionClient->sadd($sKey, $sTraceId);
//                if (!$bRet) {
//                    NuwaLog::warning(
//                        sprintf(
//                            'set fusion fail key:%s|pid:%s',
//                            $sKey,
//                            (string)($iPid)
//                        )
//                    );
//                }
//
//                return true;
//            }
//        }
//
//        return false;
//    }
}
