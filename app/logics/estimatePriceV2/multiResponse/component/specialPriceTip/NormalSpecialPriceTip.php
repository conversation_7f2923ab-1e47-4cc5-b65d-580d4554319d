<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\specialPriceTip;

use BizLib\Constants;
use BizLib\Constants\OrderSystem;
use BizLib\Utils\CarLevel;
use BizLib\Utils\ProductCategory;
use Disf\SPL\Trace;
use BizLib\Utils\Language;
use BizLib\Log as NuwaLog;
use BizCommon\Constants\PrefixSuffix;
use BizLib\Client\Cache\StorageClient;
use Nuwa\ApolloSDK\Apollo as NuwaApollo;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;
use PreSale\Logics\passenger\SpecialFeeLogic;
use PreSale\Logics\passenger\SpecialFeeCommonLogic;
use PreSale\Logics\estimatePriceV2\bill\CommonBillLogic;
use PreSale\Logics\taxi\TaxiCarType;
use BizLib\Libraries\RedisDB;
use BizLib\Utils\Common as UtilsCommon;
use BizCommon\Utils\Horae as BizCommonHorae;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TaxiPricingBoxLayout;
use Xiaoju\Apollo\Apollo;
use BizLib\Utils\Horae;
use BizLib\Config as NuwaConfig;

/**
 * Class NormalSpecialPrice
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\specialPriceTip
 */
class NormalSpecialPriceTip
{
    private $_aInfo;

    private $_sCarLevel;

    private $_oFusionClient;

    private $_oApollo;

    const ESTIMATE_ID_FENCE_ID_CACHE_TIME = 60 * 60;

    // const SPECIAL_PRICE_COMPONENT_EXPIRE_TIME = 3000;
    const SPECIAL_PRICE_COMPONENT_EXPIRE_TIME = 60;

    /**
     * NormalSpecialPrice constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo         = $aInfo;
        $this->_sCarLevel     = $aInfo['order_info']['require_level'];
        $this->_oFusionClient = StorageClient::getInstance(StorageClient::STORE);
        $this->_oApollo       = new NuwaApollo();
    }

    /**
     * 特殊价格沟通组件数据获取
     * @param array $aResponse aResponse
     * @return array
     */
    public function build($aResponse) {
        if (!Common::isHitSpecialRuleData($this->_aInfo)) {
            return $aResponse;
        }

        $aRuleType = [];

        $oFeatureToggle      = $this->_oApollo->featureToggle(
            'toll_node_route_explain',
            [
                'key'   => $this->_aInfo['passenger_info']['pid'],
                'phone' => $this->_aInfo['passenger_info']['phone'],
                'city'  => $this->_aInfo['order_info']['area'],
            ]
        );
        $bHitTollNode        = $oFeatureToggle->allow();
        $bHitHighWayTollNode = false; //是否命中有高速费的疫情路线沟通
        $aBillExtraInfo      = $this->_aInfo['bill_info']['bills'][$this->_sCarLevel];
        $sFenceId            = (string)$aBillExtraInfo['fence_id'];
        if (!empty($aBillExtraInfo['sps_start_fence_id'])) {
            $sFenceId = implode(',', [$aBillExtraInfo['sps_start_fence_id'], $aBillExtraInfo['sps_end_fence_id']]);
        }

        $bHitFenceExplain = $this->_oApollo->featureToggle(
            'gs_special_fence_explain_switch',
            [
                'key'              => $this->_aInfo['passenger_info']['pid'],
                'phone'            => $this->_aInfo['passenger_info']['phone'],
                'city'             => $this->_aInfo['order_info']['area'],
                'car_level'        => $this->_aInfo['order_info']['require_level'],
                'product_id'       => $this->_aInfo['order_info']['product_id'],
                'product_category' => $this->_aInfo['order_info']['product_category'],
                'fence_id'         => $sFenceId,
            ]
        )->allow();

        //特殊区域计价
        if (!empty($sFenceId) && $bHitFenceExplain) {
            $aRuleType[] = Common::RULE_TYPE_FENCE;
        }

        //专车多因素一口价
        $aProductInfo    = $this->_aInfo['bill_info']['product_infos'][$this->_sCarLevel];
        $iCountPriceType = $aBillExtraInfo['count_price_type'] ?? 0;
        $iComboType      = $aProductInfo['combo_type'] ?? 0;
        $bIsMultiFactorFlatRate = Horae::isMultiFactorFlatRateV2(['combo_type' => $iComboType, 'count_price_type' => $iCountPriceType]);
        if ((OrderSystem::PRODUCT_ID_DEFAULT == $this->_aInfo['order_info']['product_id']
            || OrderSystem::PRODUCT_ID_BUSINESS == $this->_aInfo['order_info']['product_id'])
            && $aBillExtraInfo['cap_price'] > 0
            && $bIsMultiFactorFlatRate
        ) {
            $aRuleType[] = Common::RULE_TYPE_CAP_PRICE;
        }

        //快车多因素一口价
        if ((OrderSystem::PRODUCT_ID_FAST_CAR == $this->_aInfo['order_info']['product_id']
            || OrderSystem::PRODUCT_ID_BUSINESS_FAST_CAR == $this->_aInfo['order_info']['product_id'])
            && $bIsMultiFactorFlatRate
        ) {
            $aRuleType[] = Common::RULE_TYPE_FAST_PRICE;
        }

        // 快车一口价
//        if (Util::isCapFast($this->_aInfo['order_info']['product_id'] ?? 0, $aBillExtraInfo['count_price_type'] ?? 0)) {
//            $aRuleType[] = Common::RULE_TYPE_CAP_FAST;
//        }

        // D1一口价
        if (isset($this->_aInfo['order_info']['n_tuple']) && BizCommonHorae::isDiOne($this->_aInfo['order_info']['n_tuple'])) {
            $iCountPriceType = $aBillExtraInfo['count_price_type'] ?? 0;
            if (Horae::isDioneCountPriceType(['count_price_type' => $iCountPriceType])) {
                $aRuleType[] = Common::RULE_TYPE_DIONE_PRICE;
            }
        }

        // 出租车
        if (isset($this->_aInfo['order_info']['n_tuple'])) {
            // 节假日附加费（品类展示依附于出租车sps）
            if (TaxiCarType::isTaxiHolidayFeeScene($this->_aInfo)) {
                $aRuleType[] = Common::RULE_TYPE_TAXI_HOLIDAY_SURCHARGE;
            }

            // 普通出租车高峰期加价 7
            if (TaxiCarType::isTaxiPeakFeeScene($this->_aInfo)) {
                $aRuleType[] = Common::RULE_TYPE_TAXI_PEAK_FEE;
            }

            // 一口价超值出租车 39
            if (TaxiCarType::isTaxiFixedPrice($this->_aInfo['order_info']['n_tuple'])) {
                $aRuleType[] = Common::RULE_TYPE_FIXED_PRICE_TAXI;
            }

            // 普通出租车一车两价 188
            if (TaxiCarType::isTaxiPutongMarketingPrice($this->_aInfo['order_info']['n_tuple'])) {
                $aRuleType[] = Common::RULE_TYPE_MARKETING_PRICE_PUTONG_TAXI;
            }

            // 优选出租车一车两价 189
            if (TaxiCarType::isTaxiYouxuanMarketingPrice($this->_aInfo['order_info']['n_tuple'])) {
                $aRuleType[] = Common::RULE_TYPE_MARKETING_PRICE_YOUXUAN_TAXI;
            }
        }

        //跨城费
        if ($aBillExtraInfo['cross_city_fee'] > 0) {
            $aRuleType[] = Common::RULE_TYPE_CROSSCITY;
        }

        //预约计价
        $bStartPriceAllow = (new NuwaApollo())->featureToggle(
            'booking_communicate_component',
            [
                'key'              => $this->_aInfo['passenger_info']['phone'],
                'phone'            => $this->_aInfo['passenger_info']['phone'],
                'city'             => $this->_aInfo['order_info']['area'],
                'product_id'       => $this->_aInfo['order_info']['product_id'],
                'car_level'        => $this->_aInfo['order_info']['require_level'],
                'lang'             => $this->_aInfo['common_info']['lang'],
                'app_version'      => $this->_aInfo['common_info']['app_version'],
                'access_key_id'    => $this->_aInfo['common_info']['access_key_id'],
                'combo_type'       => $this->_aInfo['order_info']['combo_type'],
                'menu_id'          => $this->_aInfo['order_info']['menu_id'],
                'product_category' => $this->_aInfo['order_info']['product_category'],
                'page_type'        => $this->_aInfo['order_info']['page_type'],
            ]
        )->allow();
        if (Constants\OrderSystem::TYPE_ORDER_BOOKING == $this->_aInfo['order_info']['order_type']) {
            if (($bStartPriceAllow && $aBillExtraInfo['start_price'] > 0)
                || (!$bStartPriceAllow && $aBillExtraInfo['limit_fee'] > 0)
            ) {
                $aRuleType[] = Common::RULE_TYPE_BOOKING;
            }
        }

        // 粤港车（区域一口价）
        $bIsShenGangFlatRate = Horae::isShenGangFlatRate(
            $aProductInfo['combo_type'] ?? 0
        );
        if ($bIsShenGangFlatRate) {
            $aRuleType[] = Common::RULE_TYPE_SHENGANG_FLAT_RATE;
        }

        // 高速费
        $iHighwayFee = $aBillExtraInfo['extra']['highway_fee_value'] ?? 0;
        if ($iHighwayFee) {
            $aRuleType[] = Common::RULE_TYPE_EXTRA_FEE;
            if ($bHitTollNode) {
                $aRuleType[]         = Common::RULE_TYPE_TOLL_NODE_WITH_EXTRA;
                $bHitHighWayTollNode = true;
            }
        }

        // 疫情路线沟通
        $bHaveTollStation = $aBillExtraInfo['extra']['have_toll_station'] ?? false;
        if ($bHaveTollStation && !in_array(Common::RULE_TYPE_EXTRA_FEE, $aRuleType) && $bHitTollNode) {
            $aRuleType[] = Common::RULE_TYPE_TOLL_NODE;
        }

        //优选出租车配置 还差开关配置
        if (CarLevel::DIDI_UNITAXI_YOUXUAN_CAR_LEVEL == $this->_aInfo['order_info']['require_level']) {
            $oFeatureToggle = $this->_oApollo->featureToggle(
                'youxuan_special_rule_whitelist',
                [
                    'key'           => $this->_aInfo['passenger_info']['pid'],
                    'phone'         => $this->_aInfo['passenger_info']['phone'],
                    'city'          => $this->_aInfo['order_info']['area'],
                    'access_key_id' => $this->_aInfo['common_info']['access_key_id'],
                    'app_version'   => $this->_aInfo['common_info']['app_version'],
                    'menu_id'       => $this->_aInfo['order_info']['menu_id'],
                ]
            );
            if ($oFeatureToggle->allow()) {
                $aRuleType[] = Common::RULE_TYPE_SPECIAL_YOUXUAN_TAXI;
            }
        }

        $aRuleType = Common::getCommonRuleType($aBillExtraInfo, $aRuleType);

        // 专车接机特殊价格沟通
        $sEstimateId = '';
        if (in_array(
            $this->_aInfo['order_info']['product_id'],
            [
                Constants\OrderSystem::PRODUCT_ID_DEFAULT,
                Constants\OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR,
            ]
        )
            && Constants\Horae::TYPE_COMBO_FROM_AIRPORT == $aProductInfo['combo_type']
        ) {
            if (!empty($this->_aInfo['bill_info']['estimate_id'])) {
                $sEstimateId = $this->_aInfo['bill_info']['estimate_id'];
                $oRedisdb    = RedisDB::getInstance();
                $sKey        = UtilsCommon::getRedisPrefix(P_ESTIMATE_ID_FENCE_ID_LINK_PREFIX).$sEstimateId;
                $oRedisdb->setex($sKey, self::ESTIMATE_ID_FENCE_ID_CACHE_TIME, json_encode($this->_aInfo['order_info']['fence_info']['airport']['fence_ids']));
            }

            $aRuleType[] = Common::RULE_TYPE_AIRPORT_FEE;
        }

        //未命中特殊计价
        if (empty($aRuleType)) {
            return $aResponse;
        }

        $bHitHighWayOut = $this->_oApollo->featureToggle(
            'highway_fee_out_grey',
            [
                'key'           => $this->_aInfo['passenger_info']['pid'],
                'phone'         => $this->_aInfo['passenger_info']['phone'],
                'city'          => $this->_aInfo['order_info']['area'],
                'access_key_id' => $this->_aInfo['common_info']['access_key_id'],
                'app_version'   => $this->_aInfo['common_info']['app_version'],
                'lang'          => $this->_aInfo['common_info']['lang'],
            ]
        )->allow();

        $aResponse['special_price_text'] = ['rule_type' => $aRuleType,];
        if (1 == count($aRuleType)
            || (2 == count($aRuleType) && $bHitHighWayTollNode)
        ) {
            switch ($aRuleType[0]) {
                case Common::RULE_TYPE_CAP_PRICE:
                    $aResponse['special_price_text']['text']
                        = Language::getTextFromDcmp('special_price_rule_explain-price_text_cap_price');
                    break;
                case Common::RULE_TYPE_FAST_PRICE:
                    $aResponse['special_price_text']['text']
                        = Language::getTextFromDcmp('special_price_rule_explain-price_text_fast_price');
                    break;
                case Common::RULE_TYPE_FENCE:
                    $aExplainInfo = SpecialFeeCommonLogic::getFenceExplainPriceText(
                        $aBillExtraInfo,
                        $this->_aInfo['common_info']['lang'],
                        SpecialFeeCommonLogic::SCENE_TYPE_COMMUNICATE
                    );
                    if (!empty($aExplainInfo)) {
                        $aResponse['special_price_text']['text'] = $aExplainInfo['title'];
                    }
                    break;
                case Common::RULE_TYPE_CROSSCITY:
                    $aResponse['special_price_text']['text']
                        = Language::getTextFromDcmp(
                            'special_price_rule_explain-price_text_crosscity',
                            ['fee' => $aBillExtraInfo['cross_city_fee']]
                        );
                    break;
                case Common::RULE_TYPE_BOOKING:
                    if ($bStartPriceAllow) {
                        $aResponse['special_price_text']['text'] = Language::getTextFromDcmp(
                            'special_price_rule_explain-price_text_booking_start_price',
                            ['fee' => $aBillExtraInfo['start_price']]
                        );
                    } else {
                        $aResponse['special_price_text']['text'] = Language::getTextFromDcmp(
                            'special_price_rule_explain-price_text_booking',
                            ['fee' => $aBillExtraInfo['limit_fee']]
                        );
                    }
                    break;
                case Common::RULE_TYPE_TOLL_NODE_WITH_EXTRA:
                    // no break
                case Common::RULE_TYPE_EXTRA_FEE:
                    if ($bHitHighWayOut) {
                        $aResponse['special_price_text']['text']
                            = Language::getTextFromDcmp(
                                'special_price_rule_explain-price_text_highway_fee_out',
                                ['fee' => $iHighwayFee,]
                            );
                    } else {
                        $aResponse['special_price_text']['text']
                            = Language::getTextFromDcmp('special_price_rule_explain-price_text_highway_fee');
                    }
                    break;
                case Common::RULE_TYPE_SHENGANG_FLAT_RATE:
                    $aResponse['special_price_text']['text']
                        = Language::getTextFromDcmp('special_price_rule_explain-price_text_shengang_flat_rate');
                    break;
                case Common::RULE_TYPE_SPECIAL_YOUXUAN_TAXI:
                    //这个地方因为有普通和价格透传两种，所以公用一个文案
                    $aResponse['special_price_text']['text']
                        = Language::getTextFromDcmp('youxuan_taxi-product_extra_desc');
                    break;
                case Common::RULE_TYPE_RED_PACKET:
                    $aResponse['special_price_text']['text']
                        = Language::getTextFromDcmp('special_price_rule_explain-text_red_packet');
                    break;
                case Common::RULE_TYPE_AIRPORT_FEE:
                    $aResponse['special_price_text']['text'] = SpecialFeeCommonLogic::getAirportFenceExplainPriceText($sEstimateId, $this->_aInfo['common_info']['lang'], 'special_price_rule_explain-text_zhuanceh_airport_pick_up_no_name', $this->_aInfo['order_info']['area']) ?? Language::getTextFromDcmp('special_price_rule_explain-text_zhuanceh_airport_pick_up');
                    break;
                case Common::RULE_TYPE_TOLL_NODE:
                    $aResponse['special_price_text']['text']
                        = Language::getTextFromDcmp('special_price_rule_explain-text_toll_node');
                    break;
                case Common::RULE_TYPE_DIONE_PRICE:
                    $aResponse['special_price_text']['text'] = Language::getTextFromDcmp('special_price_rule_explain-dione_price');
                    break;
                case Common::RULE_TYPE_FIXED_PRICE_TAXI:
                    $aResponse['special_price_text']['text'] = NuwaConfig::text('special_price_rule_explain', 'taxi_fixed_price');
                    break;
                case Common::RULE_TYPE_MARKETING_PRICE_YOUXUAN_TAXI:
                    $sProductName = TaxiCarType::getTaxiMarketingPriceProductName($this->_aInfo['order_info']['area'],ProductCategory::PRODUCT_CATEGORY_TAXI_MARKETISATION_YOUXUAN);
                    if (TaxiPricingBoxLayout::isPricingBox(TaxiPricingBoxLayout::buildParams($this->_aInfo))) {
                        $sProductName = Language::getDecodedTextFromDcmp('taxi_pricing_box-intro_msg')[ProductCategory::PRODUCT_CATEGORY_TAXI_MARKETISATION_YOUXUAN]['communicate_title'];
                    }

                    $aResponse['special_price_text']['text'] = Language::replaceTag(
                        NuwaConfig::text('special_price_rule_explain', 'youxuan_taxi_marketing_price'),
                        ['product_name' => $sProductName ?: '']
                    );
                    break;
                case Common::RULE_TYPE_MARKETING_PRICE_PUTONG_TAXI:
                    $sProductName = TaxiCarType::getTaxiMarketingPriceProductName($this->_aInfo['order_info']['area'],ProductCategory::PRODUCT_CATEGORY_TAXI_MARKETISATION_PUTONG);
                    if (TaxiPricingBoxLayout::isPricingBox(TaxiPricingBoxLayout::buildParams($this->_aInfo))) {
                        $sProductName = Language::getDecodedTextFromDcmp('taxi_pricing_box-intro_msg')[ProductCategory::PRODUCT_CATEGORY_TAXI_MARKETISATION_PUTONG]['communicate_title'];
                    }

                    $aResponse['special_price_text']['text'] = Language::replaceTag(
                        NuwaConfig::text('special_price_rule_explain', 'putong_taxi_marketing_price'),
                        ['product_name' => $sProductName ?: '']
                    );
                    break;
                case Common::RULE_TYPE_TAXI_PEAK_FEE:
                    $aResponse['special_price_text']['text']
                        = Language::getTextFromDcmp('taxi_peak_fee-online_payment_title');
                    break;
//                case Common::RULE_TYPE_CAP_FAST:
//                    $aResponse['special_price_text']['text']
//                        = Language::getTextFromDcmp('special_price_rule_explain-cap_fast');
//                    break;
                default:
                    $aTextConf = Language::getDecodedTextFromDcmp('special_price_rule_explain-rule_config');
                    if (!empty($aTextConf) && !empty($aTextConf[$aRuleType[0]])) {
                        $aResponse['special_price_text']['text'] = $aTextConf[$aRuleType[0]];
                    }
                    break;
            }
        } else {
            //多因素一口价若包含高速费，表单优先展示高速费
            if ($bIsMultiFactorFlatRate && in_array(Common::RULE_TYPE_EXTRA_FEE, $aRuleType)) {
                if ($bHitHighWayOut) {
                    $aResponse['special_price_text']['text']
                        = Language::getTextFromDcmp(
                            'special_price_rule_explain-price_text_highway_fee_out',
                            ['fee' => $iHighwayFee,]
                        );
                } else {
                    $aResponse['special_price_text']['text']
                        = Language::getTextFromDcmp('special_price_rule_explain-price_text_highway_fee');
                }
            } else {
                //命中多种特殊计价
                $aResponse['special_price_text']['text']
                    = Language::getTextFromDcmp('special_price_rule_explain-price_text_multiple');
            }
        }

        //是否命中特殊区域计价or附加费强弹
        if ($this->_isHitForceNotice($aRuleType) && Common::isForceNotice($this->_aInfo['passenger_info'])) {
            $sEstamateId = $this->_aInfo['order_info']['estimate_id'] ?? '';
            $sLang       = $this->_aInfo['common_info']['lang'] ?? '';
            $sAppversion = $this->_aInfo['common_info']['app_version'] ?? '';
            $aParams     = [
                'estimate_id' => $sEstamateId,
                'lang'        => $sLang,
                'rule_type'   => $aRuleType,
                'app_version' => $sAppversion,
                'phone'       => $this->_aInfo['passenger_info']['phone'] ?? '',
            ];
            if (!empty($aParams['estimate_id']) && !empty($aParams['lang'])) {
                if (Util::isDaCheAnyCar($this->_aInfo)) {
                    // $aResponse['special_price_text']['is_force_notice'] = empty($aResponse['special_price_text']) ? 0 : 1;
                } else {
                    $oSpecialFeeLogic = new SpecialFeeLogic($aParams);
                    $aRet = $oSpecialFeeLogic->getSpecialRuleText();
                    if (!empty($aRet)) {
                        $aResponse['special_price_info'] = $aRet;
                    }
                }
            }
        }

        if (!empty($aResponse['special_price_text'])) {
            $oApollo       = new NuwaApollo();
            $oApolloResult = $oApollo->featureToggle(
                'bubble_communicate_component',
                [
                    'key'              => $this->_aInfo['passenger_info']['phone'],
                    'phone'            => $this->_aInfo['passenger_info']['phone'],
                    'city'             => $this->_aInfo['order_info']['area'],
                    'product_id'       => $this->_aInfo['order_info']['product_id'],
                    'car_level'        => $this->_aInfo['order_info']['require_level'],
                    'lang'             => $this->_aInfo['common_info']['lang'],
                    'app_version'      => $this->_aInfo['common_info']['app_version'],
                    'access_key_id'    => $this->_aInfo['common_info']['access_key_id'],
                    'config'           => 0,
                    'combo_type'       => $this->_aInfo['order_info']['combo_type'],
                    'menu_id'          => $this->_aInfo['order_info']['menu_id'],
                    'product_category' => $this->_aInfo['order_info']['product_category'],
                    'page_type'        => $this->_aInfo['order_info']['page_type'],
                ]
            );
            $bAllow        = $oApolloResult->allow();
            if ($bAllow) {
                $sEstimateId = $this->_aInfo['order_info']['estimate_id'];
                $oRedisdb    = \PreSale\Logics\redismove\RedisWrapper::getInstance(P_SPECIAL_PRICE_COMPONENT);
                $sKey        = UtilsCommon::getRedisPrefix(P_SPECIAL_PRICE_COMPONENT).$sEstimateId;
                $oRedisdb->setex($sKey, self::SPECIAL_PRICE_COMPONENT_EXPIRE_TIME, json_encode($aResponse['special_price_text']));
                unset($aResponse['special_price_text']);
            }
        }

        return $aResponse;
    }

    /**
     * 特殊区域计价and附加费(高速费)强弹判断
     * @param array $aRuleType 特殊计价类型
     * @return bool
     */
    private function _isHitForceNotice($aRuleType) {
        $oApollo     = new Apollo();
        $aKeyConfigs = [];
        $oToggle     = $oApollo->featureToggle(
            'gs_fee_force_notice_switch',
            [
                'key'         => $this->_aInfo['passenger_info']['pid'],
                'city'        => $this->_aInfo['order_info']['area'],
                'phone'       => $this->_aInfo['passenger_info']['phone'],
                'app_version' => $this->_aInfo['common_info']['app_version'],
                'client_type' => $this->_aInfo['common_info']['client_type'],
            ]
        );
        if ($oToggle->allow()) {
            $aKeyConfigs[Common::RULE_TYPE_CAP_PRICE]  = $oToggle->getParameter(Common::RULE_TYPE_CAP_PRICE, '');
            $aKeyConfigs[Common::RULE_TYPE_FAST_PRICE] = $oToggle->getParameter(Common::RULE_TYPE_FAST_PRICE, '');
        }

        $iPid     = $this->_aInfo['passenger_info']['pid'];
        $sTraceId = Trace::traceId();
        if (in_array(Common::RULE_TYPE_FENCE, $aRuleType)) {
            $iFenceId = $this->_aInfo['bill_info']['bills'][$this->_sCarLevel]['fence_id'];
            $sPrefix  = PrefixSuffix::S_PASSENGER_FENCE_FORCE_HIT;
            $sKey     = $sPrefix . $iPid;
            $aResult  = $this->_oFusionClient->hgetall($sKey);
            if (isset($aResult[(string)($iFenceId)])) {
                // 保证同一次预估返回的二级顶导预估数据中，符合强弹条件的tab都有强弹数据，端上会保证同一次预估只强弹一次
                if ($sTraceId == $aResult[(string)($iFenceId)]) {
                    return true;
                }
            } else {
                $bRet = $this->_oFusionClient->hset($sKey, (string)($iFenceId), $sTraceId);
                if (!$bRet) {
                    NuwaLog::warning(
                        sprintf(
                            'set fusion fail key:%s|pid:%s|fenceid:%s',
                            $sKey,
                            (string)($iPid),
                            (string)($iFenceId)
                        )
                    );
                }

                return true;
            }
        }

        if (in_array(Common::RULE_TYPE_EXTRA_FEE, $aRuleType)) {
            $sPrefix = Common::PREFIX_TYPE_EXTRA;
            $sKey    = $sPrefix . $iPid;
            $sRes    = $this->_oFusionClient->smembers($sKey);
            if (!empty($sRes) && in_array($sTraceId, $sRes)) {
                return true;
            }

            if (count($sRes) >= 3) {
                return false;
            } else {
                $bRet = $this->_oFusionClient->sadd($sKey, $sTraceId);
                if (!$bRet) {
                    NuwaLog::warning(
                        sprintf(
                            'set fusion fail key:%s|pid:%s',
                            $sKey,
                            (string)($iPid)
                        )
                    );
                }

                return true;
            }
        }

        if (in_array(Common::RULE_TYPE_CAP_PRICE, $aRuleType)) {
            if (!empty($aKeyConfigs[Common::RULE_TYPE_CAP_PRICE])) {
                $sPrefix = $aKeyConfigs[Common::RULE_TYPE_CAP_PRICE];
            } else {
                $sPrefix = Common::PREFIX_TYPE_CAP_PRICE;
            }

            $sKey = $sPrefix . $iPid;
            $sRes = $this->_oFusionClient->get($sKey);
            if (empty($sRes)) {
                $bRet = $this->_oFusionClient->set($sKey, $sTraceId);
                if (!$bRet) {
                    NuwaLog::warning(
                        sprintf(
                            'set fusion fail key:%s|pid:%s',
                            $sKey,
                            (string)($iPid)
                        )
                    );
                }

                return true;
            }

            if ($sRes == $sTraceId) { //同一次预估强弹一次(端限制只弹一次)
                return true;
            }
        }

        if (in_array(Common::RULE_TYPE_FAST_PRICE, $aRuleType)) {
            if (!empty($aKeyConfigs[Common::RULE_TYPE_FAST_PRICE])) {
                $sPrefix = $aKeyConfigs[Common::RULE_TYPE_FAST_PRICE];
            } else {
                $sPrefix = Common::PREFIX_TYPE_FAST_PRICE;
            }

            $sKey = $sPrefix . $iPid;
            $sRes = $this->_oFusionClient->get($sKey);
            if (empty($sRes)) {
                $bRet = $this->_oFusionClient->set($sKey, $sTraceId);
                if (!$bRet) {
                    NuwaLog::warning(
                        sprintf(
                            'set fusion fail key:%s|pid:%s',
                            $sKey,
                            (string)($iPid)
                        )
                    );
                }

                return true;
            }

            if ($sRes == $sTraceId) { //同一次预估强弹一次(端限制只弹一次)
                return true;
            }
        }

        if (in_array(Common::RULE_TYPE_AIRPORT_FEE, $aRuleType)) {
            $aFenceIds = $this->_aInfo['order_info']['fence_info']['airport']['fence_ids'];
            $sPrefix   = Common::PREFIX_TYPE_ZHUANCHE_AIRPORT_PICK_UP;
            $sKey      = $sPrefix . $iPid;
            $bIsHitForceNotice = false; // 设此变量是为了将此次命中的围栏id均标记为已强弹过的状态
            $aResult           = $this->_oFusionClient->hgetall($sKey);
            foreach ($aFenceIds as $key => $iFenceId) {
                if (isset($aResult[(string)($iFenceId)])) {
                    // 保证同一次预估返回的二级顶导预估数据中，符合强弹条件的tab都有强弹数据，端上会保证同一次预估只强弹一次
                    if ($sTraceId == $aResult[(string)($iFenceId)]) {
                        $bIsHitForceNotice = true;
                    }
                } else {
                    $bRet = $this->_oFusionClient->hset($sKey, (string)($iFenceId), $sTraceId);
                    if (!$bRet) {
                        NuwaLog::warning(
                            sprintf(
                                'set fusion fail key:%s|pid:%s|fenceid:%s',
                                $sKey,
                                (string)($iPid),
                                (string)($iFenceId)
                            )
                        );
                    }

                    $bIsHitForceNotice = true;
                }
            }

            if ($bIsHitForceNotice) {
                return $bIsHitForceNotice;
            }
        }

        return false;
    }
}
