<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\specialPriceTip;

use BizLib\Constants;
use BizLib\Libraries\RedisDB;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\Language;
use Illuminate\Support\Facades\Lang;
use Nuwa\ApolloSDK\Apollo as NuwaApollo;
use PreSale\Logics\estimatePriceV2\bill\CommonBillLogic;
use Xiaoju\Apollo\Apollo as ApolloV2;
use Xiaoju\Apollo\ApolloConstant;
use BizLib\Utils\ProductCategory;

/**
 * Class Common
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\specialPriceTip
 */
class Common
{
    //特殊订单计价类型,价格沟通组件使用
    const ENABLE_SPECIAL_PRICE_EXPLAIN_VERSION = '5.2.58';
    const RULE_TYPE_CAP_FAST                   = 31; // 快车一口价
    const RULE_TYPE_TAXI_HOLIDAY_SURCHARGE     = 26; // 出租车节假日附加费
    const RULE_TYPE_ENERGY_CONSUME_FEE         = 25; // 综合能耗费
    const RULE_TYPE_HK_PRODUCT_TUNNEL_FEE      = 24; // 香港隧道费
    const RULE_TYPE_CARPOOL_DUAL_PRICE_MERGE   = 23; // 两口价合并成一口价
    const RULE_TYPE_TAXI_PEAK_FEE      = 22; // 普通出租车高峰期加价
    const RULE_TYPE_CARPOOL_DUAL_PRICE = 21;//两口价
    const RULE_TYPE_PRIVACY_PROTECTION = 20; // 隐私保护
    const RULE_TYPE_HK_CAP_TAXI        = 30; // 香港一口价出租
    const RULE_TYPE_MARKETING_PRICE_PUTONG_TAXI  = 19; // 普通出租车一车两价
    const RULE_TYPE_MARKETING_PRICE_YOUXUAN_TAXI = 18; // 优选出租车一车两价
    const RULE_TYPE_INTER_CITY_CARPOOL_NEW_MODE  = 17; // 远途特快一口价
    const RULE_TYPE_FIXED_PRICE_TAXI     = 16; // 一口价出租车
    const RULE_TYPE_DIONE_PRICE          = 15; // D1一口价
    const RULE_TYPE_FAST_PRICE           = 13; // 快车一口价
    const RULE_TYPE_TOLL_NODE_WITH_EXTRA = 12; // 疫情期间有高速费的路线沟通
    const RULE_TYPE_TOLL_NODE            = 11; // 疫情期间无高速费的路线沟通
    const RULE_TYPE_AIRPORT_FEE          = 10; // 接送机场景独立计价
    const RULE_TYPE_RED_PACKET           = 9;  // 春节服务费
    const RULE_TYPE_SPECIAL_YOUXUAN_TAXI = 8;  // 优选出租车
    const RULE_TYPE_SHENGANG_FLAT_RATE   = 6;  // 粤港车（区域一口价）
    const RULE_TYPE_EXTRA_FEE            = 5;  // 附加费，目前只有高速费
    const RULE_TYPE_CAP_PRICE            = 4;  // 专车一口价
    const RULE_TYPE_CROSSCITY            = 3;  // 跨城费
    const RULE_TYPE_BOOKING = 2;  // 预约单计价
    const RULE_TYPE_FENCE   = 1;  // 特殊区域计价

    const PREFIX_TYPE_ZHUANCHE_AIRPORT_PICK_UP = 'S_PASSENGER_ZHUANCHE_AIRPORT_PICK_UP_';
    const PREFIX_TYPE_EXTRA = 'S_PASSENGER_EXTRA_HIT_';                     // 附加费强弹标识前缀
    // 注意：以下key由阿波罗控制!!! gs_fee_force_notice_switch
    const PREFIX_TYPE_CAP_PRICE          = 'S_PASSENGER_CAP_PRICE_HIT_';    // 多因素一口价强弹前缀
    const PREFIX_TYPE_FAST_PRICE         = 'S_PASSENGER_FAST_PRICE_HIT_';   // 快车多因素一口价强弹前缀
    const PREFIX_TYPE_APLUS_PRICE        = 'S_PASSENGER_APLUS_PRICE_HIT_';  // A+一口价强弹前缀
    const PREFIX_TYPE_SPECIAL_RATE_PRICE = 'S_PASSENGER_SPECIAL_RATE_HIT_'; // 特惠快车一口价强弹枪前缀

    /**
     * 控制订单价格沟通组件放量
     * @param array $aInfo 预估结果
     * @return bool
     */
    public static function isHitSpecialRuleData($aInfo) {
        $iProductId      = $aInfo['order_info']['product_id'];
        $iClientType     = $aInfo['common_info']['client_type'];
        $sAppVersion     = (string)($aInfo['common_info']['app_version']);
        $aProductIdList  = [
            Constants\OrderSystem::PRODUCT_ID_ANY_CAR,           // anycar
            Constants\OrderSystem::PRODUCT_ID_FAST_CAR,          // 快车
            Constants\OrderSystem::PRODUCT_ID_DEFAULT,           // 专车
            Constants\OrderSystem::PRODUCT_ID_BUSINESS,          // 企业专车
            Constants\OrderSystem::PRODUCT_ID_BUSINESS_FAST_CAR, // 企业快车
            Constants\OrderSystem::PRODUCT_ID_UNITAXI,           // 出租车的优选需要用到价格透传组件
            Constants\OrderSystem::PRODUCT_ID_BUSINESS_TAXI_CAR, // 企业出租车的优选需要用到价格透传组件
            Constants\OrderSystem::PRODUCT_ID_DIONE,             // D1
            Constants\OrderSystem::PRODUCT_ID_SPECIAL_RATE,      //特惠快车
            Constants\OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR,
        ];
        $aClientTypeList = [
            Constants\Common::CLIENT_TYPE_ANDROID,
            Constants\Common::CLIENT_TYPE_IOS,
        ];

        if (in_array($iProductId, $aProductIdList)) {
            if (in_array($iClientType, $aClientTypeList)) {
                if (version_compare($sAppVersion, self::ENABLE_SPECIAL_PRICE_EXPLAIN_VERSION) >= 0
                    && Language::isLangChinese($aInfo['common_info']['lang'])
                ) {
                    return true;
                }

                if (Language::isLangEnglish($aInfo['common_info']['lang'])) {
                    $oApollo        = new NuwaApollo();
                    $oFeatureToggle = $oApollo->featureToggle(
                        'english_6_0_special_rule',
                        [
                            'key'         => $aInfo['passenger_info']['pid'],
                            'phone'       => $aInfo['passenger_info']['phone'],
                            'city'        => $aInfo['order_info']['area'],
                            'product_id'  => $aInfo['order_info']['product_id'],
                            'app_version' => (string)($aInfo['common_info']['app_version']),
                        ]
                    );
                    if ($oFeatureToggle->allow()) {
                        return true;
                    }
                }
            }

            if (Constants\Common::CLIENT_TYPE_WEBAPP == $iClientType) {
                return true;
            }
        }

        return false;
    }

    /**
     * 6.0强弹字段
     * @param array $aResponse aResponse
     * @param array $aRet      aRet
     * @return mixed
     */
    public static function buildDaCheForceNotice($aResponse, $aRet) {
        if (isset($aResponse['special_price_text'])) {
            $aResponse['special_price_text']['is_force_notice'] = empty($aRet) ? 0 : 1;
        }

        return $aResponse;
    }

    /**
     * 是否强弹AB实验
     * @param array $aPassengerInfo 乘客信息
     * @return bool
     */
    public static function isForceNotice($aPassengerInfo) :bool {
        $featureToggle = (new ApolloV2())->featureToggle(
            'ab_force_notice',
            [
                ApolloConstant::APOLLO_INDIVIDUAL_ID => $aPassengerInfo['pid'],
                'phone'                              => $aPassengerInfo['phone'],
            ]
        );

        if ($featureToggle->allow()) {
            if ('treatment_group' == $featureToggle->getGroupName()) {
                return false;
            }
        }

        return true;
    }

    /**
     * @param array $aInfo     aInfo
     * @param array $aRuleType rule_type
     * @return string
     */
    public static function getSpecialPriceText($aInfo, $aRuleType) {
        if (1 == count($aRuleType)) {
            switch ($aRuleType[0]) {
                case Common::RULE_TYPE_HK_PRODUCT_TUNNEL_FEE:
                    $sText = Language::getTextFromDcmp('special_price_rule_explain-price_text_hk_tunnel_fee');
                    break;
                case Common::RULE_TYPE_HK_CAP_TAXI:
                    $sText = Language::getTextFromDcmp('special_price_rule_explain-hk_cap_taxi_without_tunnel_fee');
                    break;
                default:
                    break;
            }
        }

        return $sText;
    }

    /**
     * @param array $aInfo         aInfo
     * @param array $aSpecialPrice aSpecialRule
     * @return void
     */
    public static function tryStoreSpecialPriceText($aInfo, $aSpecialPrice) {
        $oApollo       = new \Xiaoju\Apollo\Apollo();
        $oApolloResult = $oApollo->featureToggle(
            'bubble_communicate_component',
            [
                'key'              => $aInfo['passenger_info']['phone'],
                'phone'            => $aInfo['passenger_info']['phone'],
                'city'             => $aInfo['order_info']['area'],
                'product_id'       => $aInfo['order_info']['product_id'],
                'car_level'        => $aInfo['order_info']['require_level'],
                'lang'             => $aInfo['common_info']['lang'],
                'app_version'      => $aInfo['common_info']['app_version'],
                'access_key_id'    => $aInfo['common_info']['access_key_id'],
                'config'           => 0,
                'combo_type'       => $aInfo['order_info']['combo_type'],
                'menu_id'          => $aInfo['order_info']['menu_id'],
                'product_category' => $aInfo['order_info']['product_category'],
                'page_type'        => $aInfo['order_info']['page_type'],
            ]
        );
        $bAllow        = $oApolloResult->allow();
        if ($bAllow) {
            $sEstimateId = $aInfo['order_info']['estimate_id'];
            $oRedisdb    = \PreSale\Logics\redismove\RedisWrapper::getInstance(P_SPECIAL_PRICE_COMPONENT);
            $sKey        = UtilsCommon::getRedisPrefix(P_SPECIAL_PRICE_COMPONENT).$sEstimateId;
            // $oRedisdb->setex($sKey, 50 * 60, json_encode($aSpecialPrice));
            $oRedisdb->setex($sKey, 60, json_encode($aSpecialPrice));
        }

    }

    /**
     * 香港隧道费
     * @param array $aInfo aInfo
     * @return bool
     */
    public static function hitHKTunnelFee($aInfo) {
        $sCarLevel  = $aInfo['order_info']['require_level'];
        $aBills     = $aInfo['bill_info']['bills'][$sCarLevel];
        $fTunnelFee = $aBills['extra_info']['ToRb_tunnel_fee'] ?? 0.0;
        return $fTunnelFee > 0;
    }


    /**
     * 处理公共能力
     * @param array $aBill     账单信息
     * @param array $aRuleType 源数据
     * @return array
     */
    public static function getCommonRuleType($aBill, $aRuleType) {

        //春节服务费
        $fRedPacket = CommonBillLogic::formatDisplayLines($aBill['display_lines'])['red_packet']['value'] ?? 0.0;
        if ($fRedPacket > 0) {
            $aRuleType[] = Common::RULE_TYPE_RED_PACKET;
        }

        // 综合能耗费
        if ($aBill['fee_detail_info']['energy_consume_fee'] > 0) {
            $aRuleType[] = Common::RULE_TYPE_ENERGY_CONSUME_FEE;
        }

        return $aRuleType;
    }
    /**
     * 香港一口价出租
     * @param array $aInfo aInfo
     * @return bool
     */
    public static function hitHKCapTaxi($aInfo) {
        return ProductCategory::PRODUCT_CATEGORY_HK_CAP_TAXI == $aInfo['order_info']['product_category'];
    }
}
