<?php


namespace PreSale\Logics\estimatePriceV2\multiResponse\component\specialPriceTip;

use PreSale\Logics\redismove;
use BizLib\Utils\Common as UtilCommon;

/**
 * Class HKTaxiSpecialPriceTip
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\specialPriceTip
 */
class HKTaxiSpecialPriceTip
{
    const HK_CAP_TAXI_PRICE_FORCE_COMMUNICATION_TIMES = 3;

    private $_aInfo;

    /**
     * HKTaxiSpecialPriceTip constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
    }

    /**
     * @param array $aResponse response
     * @return array
     */
    public function build($aResponse) {
        $aRuleTypeMap = [
            Common::RULE_TYPE_HK_PRODUCT_TUNNEL_FEE => function () {
                return Common::hitHKTunnelFee($this->_aInfo);
            },
            Common::RULE_TYPE_HK_CAP_TAXI        => function () {
                return Common::hitHKCapTaxi($this->_aInfo);
            },
        ];

        $aRuleType = [];

        foreach ($aRuleTypeMap as $iRuleType => $cChecker) {
            if ($cChecker()) {
                $aRuleType[] = $iRuleType;
            }
        }

        if (empty($aRuleType)) {
            return $aResponse;
        }

        $sText = Common::getSpecialPriceText($this->_aInfo, $aRuleType);

        $aSpecialPrice = [
            'rule_type'       => $aRuleType,
            'text'            => $sText,
            'is_force_notice' => 0,
        ];
        Common::tryStoreSpecialPriceText($this->_aInfo, $aSpecialPrice);
        return $aResponse;
    }

    // 注释-待删
//    /**
//     * @param array $aRuleType aRuleType
//     * @return int
//     */
//    protected function needForceNotice($aRuleType) {
//        if (!in_array(Common::RULE_TYPE_HK_CAP_TAXI, $aRuleType)) {
//            return 0;
//        }
//
//        $oRedisDB = redismove\RedisWrapper::getInstance(P_HK_CAP_TAXI_FORCE_COMMUNICATION);
//        $iPid     = $this->_aInfo['passenger_info']['pid'];
//        $sKey     = UtilCommon::getRedisPrefix(P_CARPOOL_GUIDE_COMMUNICATION) . $iPid;
//
//        $iRet = $oRedisDB->get($sKey);
//        if ($iRet > self::HK_CAP_TAXI_PRICE_FORCE_COMMUNICATION_TIMES) {
//            return 0;
//        }
//
//        $iRet = $oRedisDB->incr($sKey);
//        if ($iRet > self::HK_CAP_TAXI_PRICE_FORCE_COMMUNICATION_TIMES) {
//            return 0;
//        }
//
//        return 1;
//    }
}
