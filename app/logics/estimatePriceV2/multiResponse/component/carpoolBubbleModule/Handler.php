<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\carpoolBubbleModule;

use PreSale\Logics\estimatePriceV2\multiResponse\Util;

/**
 * Handler
 * @deprecated 这个功能不要了, 见 http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=594988725
 */
class Handler
{
    /**
     * @param array $aInfo aInfo
     * @return null|CarpoolBubbleModule
     */
    public static function select($aInfo) {
        if (Util::isCarpool($aInfo)) {
            return new CarpoolBubbleModule($aInfo);
        }

        return null;
    }
}
