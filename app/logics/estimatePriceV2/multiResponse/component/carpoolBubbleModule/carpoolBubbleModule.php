<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\carpoolBubbleModule;

use BizCom<PERSON>\Utils\Horae;
use BizLib\Constants\OrderSystem;
use BizLib\Utils\Language;
use BizLib\Utils\UtilHelper;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\estimatePriceV2\ParamsLogic;
use PreSale\Logics\estimatePriceV2\response\SceneResponseLogicV2;

/**
 * 拼车气泡
 * @deprecated 这个功能不要了, 见 http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=594988725
 */
class CarpoolBubbleModule
{
    protected $_aInfo     = null;
    protected $_aResponse = null;
    protected $_oApollo   = null;

    const NS_CARPOOL_BUBBLE = 'carpool_bubble_module'; //预估气泡

    /**
     *
     * @param array $aInfo info
     * @return void
     */
    public function __construct($aInfo) {
        $this->_aInfo   = $aInfo;
        $this->_oApollo = Apollo::getInstance();
    }

    /**
     * carpool_bubble_module
     *
     * @param array $aResponse response
     * @return array
     */
    public function build($aResponse) {
        $aCarpoolBubble = $this->_buildCarpoolBubble();
        if (!empty($aCarpoolBubble)) {
            $aResponse['carpool_bubble_module'] = $aCarpoolBubble;
        }

        return $aResponse;
    }

    /**
     * @return array
     */
    private function _buildCarpoolBubble() {
        //ETS气泡
        $aEtsBubble = $this->_buildEtsBubble();
        return $aEtsBubble;
    }

    /**
     * ETS气泡
     *
     * @return array
     */
    private function _buildEtsBubble() {
        if (!Horae::isLowPriceCarpool($this->_aInfo['order_info'])) {
            return [];
        }

        //非实时单不出ETS
        if (OrderSystem::TYPE_ORDER_NOW != $this->_aInfo['order_info']['order_type']) {
            return [];
        }

        //从6.0导流过来的冒泡不出ETS气泡
        $iGuideScene = ParamsLogic::getInstance()->getRequest('guide_scene');
        $sSourceEid  = ParamsLogic::getInstance()->getRequest('source_estimate_id');
        if (\BizCommon\Constants\AthenaGuide::GUIDE_SCENE_CARPOOL == $iGuideScene && !empty($sSourceEid)) {
            return [];
        }

        $aEtsInfo = SceneResponseLogicV2::getInstance()->getCarpoolEts($this->_aInfo);
        if (empty($aEtsInfo) || empty($aEtsInfo['time_window']['end_time'])) {
            return [];
        }

        $oToggle          = $this->_oApollo->getConfigResult(self::NS_CARPOOL_BUBBLE, 'ets_bubble');
        list($ok, $aConf) = $oToggle->getAllConfig();
        if (!$ok || empty($aConf)) {
            return [];
        }

        //AB实验
        $aToggleParam = [
            Apollo::APOLLO_INDIVIDUAL_ID => $this->_aInfo['passenger_info']['pid'],
            'pid'                        => $this->_aInfo['passenger_info']['pid'],
            'phone'                      => $this->_aInfo['passenger_info']['phone'],
            'city'                       => $this->_aInfo['order_info']['area'],
        ];

        $oABToggle = $this->_oApollo->featureToggle('gs_ets_bubble_toggle', $aToggleParam);
        if ($oABToggle->allow()) {
            $aConf['threshold']          = $oABToggle->getParameter('threshold', 0);
            $aConf['dynamic_time_title'] = $oABToggle->getParameter('dynamic_time_title', '');
            $aConf['limit_time_title']   = $oABToggle->getParameter('limit_time_title', '');
            $aConf['limit'] = $oABToggle->getParameter('limit', 0);
        }

        if ($aEtsInfo['time_window']['end_time'] < $aConf['threshold']) {
            $sTitle  = $aConf['dynamic_time_title'];
            $iMinute = floor($aEtsInfo['time_window']['end_time'] / 60);
        } else {
            $sTitle  = $aConf['limit_time_title'];
            $iMinute = floor($aConf['limit'] / 60);
        }

        if (empty($sTitle)) {
            return [];
        }

        $aRet = [
            'carpool_bubble_title' => Language::replaceTag($sTitle, ['minute' => $iMinute]),
            'icon'                 => $aConf['icon'],
            'style'                => $aConf['style'],
            'show_time'            => $aConf['show_time'],
        ];

        return $aRet;
    }
}
