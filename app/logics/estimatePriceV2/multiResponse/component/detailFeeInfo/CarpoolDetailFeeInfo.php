<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\detailFeeInfo;

use BizLib\Utils;
use BizLib\Config as NuwaConfig;
use PreSale\Models\fee\FeeDetailTemplate;
use BizCommon\Models\Order\OrderCompanyCarpool;

/**
 * Class CarpoolDetailFeeInfo
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\detailFeeInfo
 */
class CarpoolDetailFeeInfo
{
    protected $_aInfo;

    protected $_aBillInfo;

    protected $_sCarLevel;

    protected $_aPositionInfo;

    //预估展示文案
    protected $_aEstimateText;

    // 国际化货币符号
    protected $_sCurrencySymbol;

    // 国际化"现金单位"
    protected $_sCurrencyUnit;


    // 国际化"时间单位"
    protected $_sMinuteUnit;

    /**
     * CarpoolDetailFeeInfo constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo         = $aInfo;
        $this->_sCarLevel     = $aInfo['order_info']['require_level'];
        $this->_aBillInfo     = $aInfo['bill_info']['bills'][$this->_sCarLevel] ?? [];
        $this->_aEstimateText = NuwaConfig::text('config_text', 'mOrderEstimateNew');
        $sCurrency            = $aInfo['bill_info']['currency'] ?? '';
        list($sSymbol, $sUnit) = Utils\Currency::getSymbolUnit($sCurrency, $aInfo['order_info']);
        $this->_sCurrencyUnit  = $sUnit;
        $this->_aPositionInfo  = [
            'driver_metre'  => $this->_aInfo['bill_info']['driver_metre'] ?? 0,
            'driver_minute' => $this->_aInfo['bill_info']['driver_minute'] ?? 0,
        ];
    }

    /**
     * 构建费用详情
     * @param array $aResponse aResponse
     * @return array
     */
    public function build($aResponse) {
        $aDetailPageInfo = $this->_getDetailPageInfo();
        return Common::format($aDetailPageInfo, $aResponse, $this->_aInfo, $this->_aBillInfo);
    }

    /**
     * 获取费用详情页信息
     * @return mixed
     */
    private function _getDetailPageInfo() {
        $aPriceDetail       = Common::getCommonPriceDetail($this->_aBillInfo);
        $aAdditiveAttribute = Common::getCommonAdditiveAttribute($this->_aBillInfo);
        $aDetailPageInfo['fee_detail'] = Common::getBillList(
            $aPriceDetail,
            $aAdditiveAttribute,
            $this->_aPositionInfo,
            $this->_aInfo
        );

        $aCarpoolCoupon = $this->_aInfo['activity_info'][0]['estimate_detail'] ?? [];

        if (isset($aCarpoolCoupon['title'], $aCarpoolCoupon['value'])) {
            $aDetailPageInfo['fee_detail'][] = [
                'title' => $aCarpoolCoupon['title'],
                'value' => '-'.$aCarpoolCoupon['value'],
                'color' => $aCarpoolCoupon['color'],
            ];
        }

        //预估详情页透传参数 - 计价token
        $priceTokensWithCarLevel = $this->_aInfo['bill_info']['concrete_product_ids'];
        $priceTokens = array_shift($priceTokensWithCarLevel);
        if (isset($priceTokens)) {
            $aDetailPageInfo['price_token'] = $priceTokens;
        }

        //费用明细透传字段
        $aDetailPageInfo['estimated_fee_detail'] = $this->_getEstimateFeeDetail();

        // 预估新标单，是否可以跳转费用详情页
        $aDetailPageInfo['disable_detailpage'] = Common::DETAIL_PAGE_CLICK;

        if ((OrderCompanyCarpool::getInstance())->isCompanyCarpoolOpen()) {
            $aDetailPageInfo['company_carpool_open'] = true;
        }

        //data_for_detail_page sum
        $aDetailPageInfo['sum'] = Common::getSumInfo($this->_aInfo);

        return $aDetailPageInfo;
    }


    /**
     * 费用明细透传字段
     * @return array
     */
    private function _getEstimateFeeDetail() {
        //拼车
        $aFeeDetailWithCarLevel = $this->_aInfo['bill_info']['fees_detail_infos'];
        $aFeeDetail = array_shift($aFeeDetailWithCarLevel);
        if (empty($aFeeDetail)) {
            return null;
        }

        if (!empty($this->_aInfo['bill_info']['estimate_id'])) {
            $aFeeDetail['estimate_id'] = $this->_aInfo['bill_info']['estimate_id'];
        }

        if (isset($this->_aInfo['price_extra']['is_default'])
            && !empty($this->_aInfo['price_extra']['is_default'])
            && !empty($aFeeDetail)
            && is_array($aFeeDetail)
        ) {
            $aFeeDetail['is_default']        = $this->_aInfo['price_extra']['is_default'];
            $aFeeDetail['user_status']       = $this->_aInfo['price_extra']['user_status'] ?? -1;
            $aFeeDetail['is_default_action'] = $this->_aInfo['price_extra']['is_default_action'] ?? -1;
        }

        $oFeeDetailTemplate = new FeeDetailTemplate();
//        $sShakeFlag         = Common::getShakeFlag($this->_aBillInfo);
        $oFeeDetailTemplate->setTemplate($aFeeDetail);
        $aParams            = [
            'discount'      => $this->_aInfo['activity_info'][0]['estimate_detail']['amount'],
            'currency_unit' => $this->_sCurrencyUnit,
            'activity_info' => $this->_aInfo['activity_info'],
            'bills'         => $this->_aBillInfo,
            'discount_list' => $this->_aInfo['activity_info'][0]['discount_desc'] ?? [],
//            'shake_flag'    => $sShakeFlag,
        ];
        $aEstimateFeeDetail = $oFeeDetailTemplate->getFeeDetail($aParams);

        return $aEstimateFeeDetail;
    }
}
