<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\detailFeeInfo;

use BizCommon\Logics\Anycar\AnyCarCommonLogic;
use BizLib\Utils;
use BizLib\Constants;
use BizLib\Utils\Language;
use BizLib\Utils\NumberHelper;
use BizLib\Utils\MemberVersion;
use BizLib\Config as NuwaConfig;
use BizCommon\Logics\Order\OrderComLogic;
use PreSale\Logics\commonAbility\AdjustPriceLogic;
use PreSale\Logics\order\AnyCarOrderLogic;
use PreSale\Models\order\OrderCarpoolDualPrice;

/**
 * Class AnycarDetailFeeInfo
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\detailFeeInfo
 */
class AnycarDetailFeeInfo
{
    protected $_aInfo;

    protected $_aBillInfo;

    protected $_sCarLevel;

    protected $_aPositionInfo;

    //预估详情相关文案
    protected $_aEstimateDetail;

    //预估展示文案
    protected $_aEstimateText;

    // 国际化货币符号
    protected $_sCurrencySymbol;

    // 国际化"现金单位"
    protected $_sCurrencyUnit;

    // 国际化"长度单位"
    protected $_sLengthUnit;

    // 国际化"时间单位"
    protected $_sMinuteUnit;

    private $_oCarpoolDualPrice = null;

    private $_isDualPrice = false;

    /**
     * AnycarDetailFeeInfo constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo         = $aInfo;
        $this->_sCarLevel     = $aInfo['order_info']['require_level'];
        $this->_aBillInfo     = $aInfo['bill_info']['bills'][$this->_sCarLevel] ?? [];
        $this->_aEstimateText = NuwaConfig::text('config_text', 'mOrderEstimateNew');
        $sCurrency            = $aInfo['bill_info']['currency'] ?? '';
        list($sSymbol, $sUnit)  = Utils\Currency::getSymbolUnit($sCurrency, $aInfo['order_info']);
        $this->_sCurrencySymbol = $sSymbol;
        $this->_sCurrencyUnit   = $sUnit;
    }

    /**
     * 构建费用详情
     * @param array $aResponse aResponse
     * @return mixed
     */
    public function build($aResponse) {
        $aAnyCarText   = NuwaConfig::text('config_text', 'anycar');
        $aEstimateText = NuwaConfig::text('config_text', 'mOrderEstimateNew');

        $aMultiInfo        = AnyCarOrderLogic::getInstance()->getMultiBillInfo($this->_aInfo['bill_info']);
        $aSelectedProducts = AnyCarOrderLogic::getInstance()->getSelectedEnabledProducts($aMultiInfo);
        $sAnyCarType       = AnyCarOrderLogic::getInstance()->getAnyCarType($aMultiInfo);

        $aFeeItems = [];
        if (!empty($aSelectedProducts)) {
            $aFeeDetailList    = [];
            $aDisplayNameGroup = [];
            // 优享快车，区域一口价和多因素一口价
            $aFastCarFlatRate = [
                AnyCarOrderLogic::PRODUCT_KEY_KUAICHE_FLAT_RATE,
                AnyCarOrderLogic::PRODUCT_KEY_KUAICHE_FACTOR_FLAT_RATE,
                AnyCarOrderLogic::PRODUCT_KEY_YOUXIANG_FACTOR_FLAT_RATE,
                AnyCarOrderLogic::PRODUCT_KEY_APLUS_FLAT_RATE,
                AnyCarOrderLogic::PRODUCT_KEY_APLUS_FLAT_RATE_FOR_BUSINESS,
            ];
            foreach ($aSelectedProducts as $aItem) {
                $fValue = $aItem['discount_fee'] ?? $aItem['pre_total_fee'];

                $sGroupKey = AnyCarCommonLogic::getGroupKey($aItem);
                switch ($sGroupKey) {
                    case AnyCarOrderLogic::PRODUCT_KEY_UNIONE:
                        //no break
                    case AnyCarOrderLogic::PRODUCT_KEY_BUSINESS_UNIONE:
                        // unione: 打表计价
                        $sValue = $aAnyCarText['fee_info_unione'];
                        break;
                    case AnyCarOrderLogic::PRODUCT_KEY_CARPOOL:
                        //no break
                    case AnyCarOrderLogic::PRODUCT_KEY_BUSINESS_CARPOOL:
                        // 一口价
                        $iSeatNum = !empty($this->_aInfo['order_info']['carpool_seat_num']) ? $this->_aInfo['order_info']['carpool_seat_num'] : 1;
                        $sValue   = Language::replaceTag(
                            $aAnyCarText['carpool_estimate_price'],
                            [
                                'seat_num'        => $iSeatNum,
                                'currency_symbol' => $this->_sCurrencySymbol,
                                'num'             => NumberHelper::numberFormatDisplay($fValue),
                                'currency_unit'   => $this->_sCurrencyUnit,
                            ]
                        );
                        $this->_oCarpoolDualPrice = new OrderCarpoolDualPrice();
                        if ($this->_oCarpoolDualPrice->isCarpoolDualPrice($aItem)) {
                            $this->_isDualPrice = true;
                            list($sPriceDesc, $sCouponAmountDesc) = $this->_oCarpoolDualPrice->getFeeDescforAnycarDetailPageOld($aItem, $this->_sCurrencyUnit);
                            $sValue = $sPriceDesc;
                        }
                        break;
                    default:
                        $sValue = Common::getFeeValue(
                            $fValue,
                            $this->_aEstimateText,
                            $this->_sCurrencySymbol,
                            $this->_sCurrencyUnit
                        );
                        break;
                }

                //优享，快车一口价
                if (in_array($sGroupKey, $aFastCarFlatRate) || Utils\Horae::isFlatRateByCountPriceType($aItem)) {
                    $sValue = Language::replaceTag(
                        $aAnyCarText['anycar_estimate_flat_rate_price'],
                        array(
                            'currency_symbol' => $this->_sCurrencySymbol,
                            'num'             => $fValue,
                            'currency_unit'   => $this->_sCurrencyUnit,
                        )
                    );
                }

                // anycar豪华车
                if (Constants\OrderSystem::TYPE_ANYCAR_FIRST_CLASS == $sAnyCarType) {
                    list($sDisplayName) = OrderComLogic::getLuxuryDisplayNameByCarLevel($aItem['require_level']);
                    $sValue = Language::replaceTag(
                        $aAnyCarText['luxury_fee_info_v2'],
                        [
                            'currency_symbol' => $this->_sCurrencySymbol,
                            'fee'             => NumberHelper::numberFormatDisplay($fValue),
                            'currency_unit'   => $this->_sCurrencyUnit,
                        ]
                    );
                } else {
                    $sDisplayName = OrderComLogic::getProductDisplayNameByCarLevel(
                        $aItem['require_level'],
                        $aItem['combo_type']
                    );
                    if (!empty($aItem['level_type'])) {
                        $aBusinessNameConfig = Language::getDecodedTextFromDcmp('config_anycar-business_name_text');
                        $sDisplayName        = $aBusinessNameConfig[$sGroupKey];
                    }
                }

                $aDisplayNameGroup[] = $sDisplayName;

                $aSubItems = [];

                // 春节服务费
                if (isset($aItem['red_packet']) && $aItem['red_packet'] > 0) {
                    $aSubItems[] = [
                        'name'  => '',
                        'value' => Language::replaceTag(
                            $aAnyCarText['anycar_spring_festival_red_packet_desc_for_detail'],
                            [
                                'red_packet' => $aItem['red_packet'],
                            ]
                        ),
                    ];
                }

                // 动调加价
                $fDynamicTimes = isset($aItem['dynamic_times']) ? (float)($aItem['dynamic_times']) : 0;
                $bIfUseTimes   = $aItem['if_use_times'] ?? false;
                if ($bIfUseTimes && !empty($fDynamicTimes)) {
                    $aSubItems[] = [
                        'name'  => '',
                        'value' => Language::replaceTag($aAnyCarText['dynamic_multiple_msg'], ['num' => $fDynamicTimes]),
                    ];
                }

                // 优惠项统计
                $aDiscountDesc = [
                    'total' => 0.0,
                    'num'   => 0,
                ];

                // 计算总优惠价格
                if (AnyCarOrderLogic::PRODUCT_KEY_CARPOOL == $sGroupKey && $this->_isDualPrice) {
                    if (!empty($sCouponAmountDesc)) {
                        $aSubItems[] = [
                            'name'  => '',
                            'value' => $sCouponAmountDesc,
                        ];
                    }
                } elseif (!empty($aItem['discount_desc'])) {
                    foreach ($aItem['discount_desc'] as $aDiscountItem) {
                        if (($fDiscount = abs($aDiscountItem['amount'] ?? 0)) > 0
                            && 'infofee' != $aDiscountItem['type']
                        ) {
                            ++$aDiscountDesc['num'];
                            $aDiscountDesc['total'] += $fDiscount;
                        }

                        if (!empty($aDiscountItem['type'])
                            && !empty($aDiscountItem['amount'])
                            && 'citycard' === $aDiscountItem['type']
                            && $aDiscountItem['amount'] > 0
                        ) {
                            $aSubItems[] = [
                                'name'  => '',
                                'value' => Language::replaceTag(
                                    $aAnyCarText['citycard_msg'],
                                    [
                                        'currency_symbol' => $this->_sCurrencySymbol,
                                        'num'             => $aDiscountItem['amount'],
                                        'currency_unit'   => $this->_sCurrencyUnit,
                                    ]
                                ),
                            ];
                        }

                        if (!empty($aDiscountItem['type'])
                            && !empty($aDiscountItem['amount'])
                            && 'backcard' === $aDiscountItem['type']
                            && $aDiscountItem['amount'] > 0
                        ) {
                            $aSubItems[] = [
                                'name'  => '',
                                'value' => Language::replaceTag(
                                    $aAnyCarText['backcard_msg'],
                                    [
                                        'currency_symbol' => $this->_sCurrencySymbol,
                                        'num'             => $aDiscountItem['amount'],
                                        'currency_unit'   => $this->_sCurrencyUnit,
                                    ]
                                ),
                            ];
                        }

                        if (!empty($aDiscountItem['type'])
                            && !empty($aDiscountItem['amount'])
                            && 'coupon' === $aDiscountItem['type']
                            && $aDiscountItem['amount'] > 0
                        ) {
                            //是否触发了溢价保护, 保护+劵
                            if (-$aItem['dynamic_member_reduce'] > 0 && Constants\OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR == $aItem['product_id']) {
                                $sMemberProtectCoupText = $aEstimateText['member_protect_coupon_msg_lux'];
                                $sLevelName = $aItem['level_name'];
                                if (MemberVersion::isNewMember()) {
                                    $sMemberProtectCoupText = $aEstimateText['member_protect_coupon_msg_lux_new'];
                                    $sLevelName = $aItem['privileges']['dpa']['frontend']['reason'];
                                }

                                $aSubItems[] = [
                                    'name'  => '',
                                    'value' => Language::replaceTag(
                                        $sMemberProtectCoupText,
                                        [
                                            'level_name'      => $sLevelName,
                                            'priv_name'       => $aItem['privileges']['dpa']['name'],
                                            'currency_symbol' => $this->_sCurrencySymbol,
                                            'num'             => $aDiscountItem['amount'] - $aItem['dynamic_member_reduce'],
                                            'currency_unit'   => $this->_sCurrencyUnit,
                                        ]
                                    ),
                                ];
                            } else {
                                $aSubItems[] = [
                                    'name'  => '',
                                    'value' => Language::replaceTag(
                                        $aAnyCarText['coupon_msg'],
                                        [
                                            'currency_symbol' => $this->_sCurrencySymbol,
                                            'num'             => $aDiscountItem['amount'],
                                            'currency_unit'   => $this->_sCurrencyUnit,
                                        ]
                                    ),
                                ];
                            }
                        } else {
                            //是否触发了溢价保护
                            if (-$aItem['dynamic_member_reduce'] > 0 && Constants\OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR == $aItem['product_id']) {
                                $sMemberProtectText = $aEstimateText['member_protect_msg_lux'];
                                $sLevelName         = $aItem['level_name'];
                                if (MemberVersion::isNewMember()) {
                                    $sMemberProtectText = $aEstimateText['member_protect_msg_lux_new'];
                                    $sLevelName         = $aItem['privileges']['dpa']['frontend']['reason'];
                                }

                                $aSubItems[] = [
                                    'name'  => '',
                                    'value' => Language::replaceTag(
                                        $sMemberProtectText,
                                        [
                                            'level_name'      => $sLevelName,
                                            'priv_name'       => $aItem['privileges']['dpa']['name'],
                                            'currency_symbol' => $this->_sCurrencySymbol,
                                            'amount'          => -$aItem['dynamic_member_reduce'],
                                            'currency_unit'   => $this->_sCurrencyUnit,
                                        ]
                                    ),
                                ];
                            }
                        }

                        if (!empty($aDiscountItem['type'])
                            && !empty($aDiscountItem['amount'])
                            && 'reward' === $aDiscountItem['type']
                            && $aDiscountItem['amount'] > 0
                        ) {
                            $aSubItems[] = [
                                'name'  => '',
                                'value' => Language::replaceTag(
                                    $aAnyCarText['reward_msg'],
                                    [
                                        'currency_symbol' => $this->_sCurrencySymbol,
                                        'num'             => $aDiscountItem['amount'],
                                        'currency_unit'   => $this->_sCurrencyUnit,
                                    ]
                                ),
                            ];
                        }

                        if (!empty($aDiscountItem['type'])
                            && !empty($aDiscountItem['amount'])
                            && 'infofee' === $aDiscountItem['type']
                            && $aDiscountItem['amount'] > 0
                        ) {
                            $aSubItems[] = [
                                'name'  => '',
                                'value' => Language::replaceTag(
                                    $aAnyCarText['infofee_msg'],
                                    [
                                        'num' => $aDiscountItem['amount'],
                                    ]
                                ),
                                'hint'  => [
                                    'link' => $aAnyCarText['infofee_msg_url'],
                                ],
                            ];
                        }
                    }
                } else {
                    //是否触发了溢价保护
                    if (-$aItem['dynamic_member_reduce'] > 0 && Constants\OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR == $aItem['product_id']) {
                        $sMemberProtectText = $aEstimateText['member_protect_msg_lux'];
                        $sLevelName         = $aItem['level_name'];
                        if (MemberVersion::isNewMember()) {
                            $sMemberProtectText = $aEstimateText['member_protect_msg_lux_new'];
                            $sLevelName         = $aItem['privileges']['dpa']['frontend']['reason'];
                        }

                        $aSubItems[] = [
                            'name'  => '',
                            'value' => Language::replaceTag(
                                $sMemberProtectText,
                                [
                                    'level_name'      => $sLevelName,
                                    'priv_name'       => $aItem['privileges']['dpa']['name'],
                                    'currency_symbol' => $this->_sCurrencySymbol,
                                    'amount'          => -$aItem['dynamic_member_reduce'],
                                    'currency_unit'   => $this->_sCurrencyUnit,
                                ]
                            ),
                        ];
                    }

                    // 优惠券信息
                    $fCouponAmount = isset($aItem['coupon_amount']) ? (float)($aItem['coupon_amount']) : 0;
                    if (!empty($fCouponAmount)) {
                        $aSubItems[] = [
                            'name'  => '',
                            'value' => Language::replaceTag(
                                $aAnyCarText['coupon_msg'],
                                [
                                    'currency_symbol' => $this->_sCurrencySymbol,
                                    'num'             => $fCouponAmount,
                                    'currency_unit'   => $this->_sCurrencyUnit,
                                ]
                            ),
                        ];
                    }
                }

                $aFeeDetailList[] = [
                    'name'      => $sDisplayName,
                    'value'     => $sValue,
                    'sub_items' => $aSubItems,
                ];
            }

            foreach ($aFeeDetailList as $iIndex => $aDetail) {
                $aItem = [];
                if (0 === $iIndex) {
                    $aItem['sub_title'] = $aAnyCarText['fee_detail_sub_title'];
                }

                $aItem['need_pay_detail'][] = $aDetail;
                $aFeeItems[] = $aItem;
            }

            $sProductIntroKey = 'anycar_product_desc_v2';
            if (Constants\OrderSystem::TYPE_ANYCAR_FIRST_CLASS == $sAnyCarType) {
                $sProductIntroKey = 'luxury_anycar_product_desc';
            }

            // 可呼叫快车、优享、出租车
            $sAnyCarProductDesc = Language::replaceTag(
                $aAnyCarText[$sProductIntroKey],
                [
                    'display_name' => implode('、', $aDisplayNameGroup),
                ]
            );
        } else {
            $sAnyCarProductDesc = $aAnyCarText['anycar_product_desc_disabled'];
            $aFeeItems[]        = [
                'sub_title'       => $aAnyCarText['fee_detail_sub_title'],
                'need_pay_detail' => [
                    [
                        'intros' => [
                            [
                                'value' => $aAnyCarText['fee_detail_disabled'],
                            ],
                        ],
                    ],
                ],
            ];
        }

        $aEstimatedFeeDetail = [
            'page_title'        => $aAnyCarText['fee_detail_title'],
            'title'             => '',
            'price_rule_hidden' => 1,   // 是否隐藏 "计价规则详情"
            'product_intro'     => $sAnyCarProductDesc,
            'items'             => $aFeeItems,
        ];

        $aResponse['data_for_detailpage'] = [
            'use_estimate_detail_v3' => 1,
            'estimated_fee_detail'   => $aEstimatedFeeDetail,
        ];
        if (version_compare((string)($this->_aInfo['common_info']['app_version']), Common::ENABLE_PRICE_INFO_DESC_VERSION) >= 0) {
            $aResponse['disable_detailpage'] = Common::DETAIL_PAGE_DEFAULT;
            if (Common::isNewPriceInfoDescSwitch($this->_aInfo)) {
                $aResponse['disable_detailpage'] = Common::DETAIL_PAGE_CLICK;
            }
        }

        $aResponse['data_for_detailpage'] = json_encode($aResponse['data_for_detailpage']);
        $aResponse['data_for_detailpage'] = AdjustPriceLogic::getInstance()->addAdjustPriceInfoInFeeDetail($aResponse['data_for_detailpage'], $this->_aInfo);

        return $aResponse;
    }
}
