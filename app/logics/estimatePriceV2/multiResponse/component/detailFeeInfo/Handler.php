<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\detailFeeInfo;

use BizCommon\Utils\Order;
use BizLib\Utils\Horae;
use BizLib\Utils\Product;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;

/**
 * Class Handler
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\detailFeeInfo
 */
class Handler
{
    /**
     * @param array $aInfo aInfo
     * @return AnycarDetailFeeInfo|CarpoolDetailFeeInfo|NormalDetailFeeInfo|SpecialRateDetailFeeInfo
     */
    public static function select($aInfo) {
        if (Util::isCarpool($aInfo)) {
            return new CarpoolDetailFeeInfo($aInfo);
        }

        if (Order::isSpecialRateV2($aInfo['order_info'])) {
            return new SpecialRateDetailFeeInfo($aInfo);
        }

        if (Product::isAnyCar($aInfo['order_info']['product_id'])) {
            return new AnycarDetailFeeInfo($aInfo);
        }

        return new NormalDetailFeeInfo($aInfo);
    }
}
