<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\multiPriceDesc;

use PreSale\Logics\estimatePriceV2\multiResponse\Util;

/**
 * Class Handler
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\multiPriceDesc
 */
class Handler
{
    /**
     * @param array $aInfo aInfo
     * @return CarpoolMultiPriceDesc
     */
    public static function select($aInfo) {
        if (Util::isCarpool($aInfo)) {
            return new CarpoolMultiPriceDesc($aInfo);
        }
    }
}
