<?php


namespace PreSale\Logics\estimatePriceV2\multiResponse\component\multiPriceDesc;

use PreSale\Logics\estimatePriceV2\carpool\DualPrice;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;

/**
 * Class CarpoolMultiPriceDesc
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\multiPriceDesc
 */
class CarpoolMultiPriceDesc
{
    protected $_aInfo;

    /**
     * CarpoolMultiPriceDesc constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
    }

    /**
     * build multi price desc
     * @param array $aResponse aResponse
     * @return mixed
     */
    public function build($aResponse) {
        //如果不是两口价，则直接返回
        if (!Util::isCarpoolDualPrice($this->_aInfo)) {
            return $aResponse;
        }

        //如果没有两个价格，则直接返回
        if (2 != count($this->_aInfo['activity_info'])) {
            return $aResponse;
        }

        // 当按照一口价样式显示时, 不再渲染multi_price_desc字段
        if (MainDataRepo::isCarpoolUnSuccessFlatPriceShowAsCapPrice($this->_aInfo)) {
            return $aResponse;
        }

        $aMultiPriceDesc = DualPrice::getInstance()->getMultiPriceCarpoolDesc($this->_aInfo);
        if (!empty($aMultiPriceDesc)) {
            $aResponse['multi_price_desc'] = $aMultiPriceDesc;
            if (MainDataRepo::isApplet()) {
                $aResponse['style_type'] = 1;//item 列表展示
            }
        }

        return $aResponse;
    }
}
