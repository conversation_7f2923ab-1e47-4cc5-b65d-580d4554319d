<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\controllerInfo;

use BizCommon\Constants\OrderNTuple;
use BizCommon\Utils\Order;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\CarLevel;
use BizLib\Utils\Common as UtilCommon;
use BizLib\Utils\Horae;
use BizLib\Utils\Language;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\estimatePriceV2\DecisionLogic;
use PreSale\Logics\estimatePriceV2\multiRequest\Product;
use PreSale\Logics\estimatePriceV2\multiResponse\OneConfData;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;
use BizCommon\Utils\Horae as BizCommonHorae;

/**
 * Class NormalControllerInfo
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\controllerInfo
 */
class NormalControllerInfo
{
    protected $_aInfo;

    /**
     * @var Product
     */
    protected $_oProduct;

    /**
     * NormalControllerInfo constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
        if (!empty($aInfo['product_object']) && $aInfo['product_object'] instanceof Product) {
            $this->_oProduct = $aInfo['product_object'];
        }
    }

    /**
     * @param array $aResponse response
     * @return mixed
     */
    public function build($aResponse) {
        $aOneConfResult = OneConfData::getInstance()->getOneConfProductInfo($this->_aInfo);
        $bIsLiteApp     = UtilCommon::isLiteVersion($this->_aInfo['common_info']['app_version'], $this->_aInfo['common_info']['client_type'], $this->_aInfo['order_info']['channel']);

        if ($bIsLiteApp) {
            if (isset($aOneConfResult['controller_info'])) {
                $aControllerInfo = $this->_checkControllerInfo($aResponse, $aOneConfResult['controller_info']);
                $aResponse['controller_info'] = $aControllerInfo;
            }

            if (isset($aOneConfResult['service_dentifier'])) {
                $aResponse['service_dentifier'] = $aOneConfResult['service_dentifier'];
            }

            if (isset($aOneConfResult['category_id'])) {
                $aResponse['category_id'] = $aOneConfResult['category_id'];
            }

            if (isset($aOneConfResult['category_show_msg'])) {
                $aResponse['category_show_msg'] = $aOneConfResult['category_show_msg'];
            }
        }

        // disabled 字段
        $aResponse = array_merge($aResponse, $this->_getDisabledAndUsageInfoFields());

        return $aResponse;
    }

    /**
     * @param array $aResponse       aResponse
     * @param array $aControllerInfo aControllerInfo
     * @return array
     */
    private function _checkControllerInfo($aResponse, $aControllerInfo) {
        if (in_array('user_pay_info', $aControllerInfo)) {
            if (!isset($aResponse['user_pay_info'])
                || empty($aResponse['user_pay_info'])
            ) {
                $aControllerInfo = array_merge(array_diff($aControllerInfo, array('user_pay_info')));
            }
        }

        if (in_array('carpool_seat_config', $aControllerInfo)) {
            if (!isset($aResponse['carpool_seat_config'])
                || empty($aResponse['carpool_seat_config'])
            ) {
                $aControllerInfo = array_merge(array_diff($aControllerInfo, array('carpool_seat_config')));
            }
        }

        return $aControllerInfo;
    }

    /**
     * _getDisabledAndUsageInfoFields
     *
     * @return array
     */
    private function _getDisabledAndUsageInfoFields() {
        $aResponse = ['disabled' => 0];
        if (BizCommonHorae::isAPlus($this->_aInfo['order_info']['n_tuple'])) {
            return $this->_doAplus($aResponse);
        } elseif (Util::isFastCarSpecialRate($this->_aInfo)) {
            return $this->_doFastCarSpecialRate($aResponse);
        }

        return $aResponse;
    }

    /**
     * DoAplus.
     *
     * @param array $aResponse aResponse
     * @return mixed
     */
    private function _doAplus($aResponse) {
        $oApollo = new Apollo();
        $oToggle = $oApollo->featureToggle(
            'aplus_frequency_limit',
            [
                'key'         => $this->_aInfo['passenger_info']['pid'],
                'city'        => $this->_aInfo['order_info']['area'],
                'phone'       => $this->_aInfo['passenger_info']['phone'],
                'app_version' => $this->_aInfo['common_info']['app_version'],
                'client_type' => $this->_aInfo['common_info']['client_type'],
            ]
        );
        if (false == $oToggle->allow()) {
            return $aResponse;
        }

        $aAnyCarText  = NuwaConfig::text('config_text', 'anycar');
        $iCurUseTimes = $aInfo['feature_data']['data']['featureMap']['ufs.passenger.statistics.aplus.finish_order_cnt']['val'] ?? 0;
        $iLimitTimes  = $oToggle->getParameter('times_limit', '0');
        $iLimitTimes  = max($iLimitTimes, 0);
        $iCurUseTimes = max($iCurUseTimes, 0);
        $aUsageInfo   = [];
        if ($iLimitTimes > $iCurUseTimes) {
            // 还剩一次 or 剩余多次
            $aUsageInfo['left_text']  = Language::replaceTag(
                $aAnyCarText['aplus_frequency_limit_left_text'] ?? '',
                [
                    'cur_times'   => $iLimitTimes - $iCurUseTimes,
                    'limit_times' => $iLimitTimes,
                ]
            );
            $aUsageInfo['right_text'] = $aAnyCarText['aplus_frequency_limit_right_text'] ?? '';
        }

        if ($iLimitTimes - 1 == $iCurUseTimes) {
            // 还剩一次 追加弹窗 强感知
            $aUsageInfo['alert'] = $aAnyCarText['aplus_frequency_limit_alert_text'] ?? '';
        }

        if ($iLimitTimes <= $iCurUseTimes) {
            $aResponse['disabled']      = 1;
            $aResponse['disabled_text'] = $aAnyCarText['aplus_frequency_limit_disabled_text'] ?? '';
        }

        if (!empty($aUsageInfo)) {
            $aResponse['usage_info'] = $aUsageInfo;
        }

        return $aResponse;
    }

    /**
     * DoFastCarSpecialRate.
     *
     * @param array $aResponse aResponse
     * @return mixed
     */
    private function _doFastCarSpecialRate($aResponse) {
        $aText         = NuwaConfig::text('config_text', 'special_rate');
        $aDecisionInfo = DecisionLogic::getInstance()->getDecisionProductOrder();

        foreach ($aDecisionInfo as $aProduct) {
            if ((OrderNTuple::PRODUCT_ID_FAST_CAR == $aProduct['product_id'] && CarLevel::DIDI_PUTONG_CAR_LEVEL == $aProduct['require_level'] && \BizLib\Constants\Horae::TYPE_COMBO_SPECIAL_RATE == $aProduct['combo_type']) || (Order::isIndependentSpecialRate($aProduct['product_id'], $aProduct['require_level']))) {
                if (true == $aProduct['remove_flag'] && ('5' == $aProduct['remove_reason'] || '6' == $aProduct['remove_reason'])) {
                    $aResponse['disabled']      = 1;
                    $aResponse['disabled_text'] = $aText['special_rate_disabled_text'][$aProduct['remove_reason']] ?? '';
                }
            }
        }

        return $aResponse;
    }
}
