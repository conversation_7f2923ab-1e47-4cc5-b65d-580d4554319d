<?php

namespace PreSale\Logics\estimatePriceV2\multiResponse\component\controllerInfo;

use PreSale\Logics\estimatePriceV2\multiResponse\component\introMsg\NormalIntroMsgV2;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;

/**
 * Class Handler
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\iconInfo
 */
class Handler
{

    /**
     * @param array $aInfo aInfo
     * @return NormalControllerInfo|NormalControllerInfoV2
     */
    public static function select($aInfo) {
        return new NormalControllerInfoV2($aInfo);
    }
}
