<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\component\controllerInfo;

use BizLib\Utils\Language;
use BizLib\Utils\ProductCategory;
use BizLib\Config as NuwaConfig;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\estimatePriceV2\DecisionLogic;
use PreSale\Logics\estimatePriceV2\multiRequest\OrderInfo;
use PreSale\Logics\estimatePriceV2\multiRequest\Product;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use BizLib\Constants;
use Xiaoju\Apollo\Apollo as ApolloV2;

/**
 * Class NormalControllerInfoV2
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component\controllerInfo
 */
class NormalControllerInfoV2
{
    protected $_aInfo;
    /**
     * @var Product
     */
    protected $_oProduct;

    /**
     * NormalControllerInfo constructor.
     * @param array $aInfo aInfo
     */
    public function __construct($aInfo) {
        $this->_aInfo = $aInfo;
        if (!empty($aInfo['product_object']) && $aInfo['product_object'] instanceof Product) {
            $this->_oProduct = $aInfo['product_object'];
        }
    }

    /**
     * @param array $aResponse response
     * @return mixed
     */
    public function build($aResponse) {
        $iProductCategory = $this->_aInfo['order_info']['product_category'];

        $aResponse['form_show_type'] = $this->_aInfo['order_info']['form_show_type'];
        $bFirstShow = $this->_firstShow($this->_aInfo['order_info']);
        if ($bFirstShow) {
            $aResponse['form_show_type'] = OrderInfo::FORM_SHOW_TYPE_FIRST;
        }

        if (OrderInfo::FORM_SHOW_TYPE_GUIDE == $aResponse['form_show_type']) {
            $aResponse['category_id'] = -1;
            $aText = Language::getDecodedTextFromDcmp('config_text-guide_show_type_text', $this->_aInfo['common_info']['lang']);
            if (ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTE == $iProductCategory) {
                $aResponse['guide_show_type_tips'] = $aText['intercity_tips'];
                $sButtonText = $this->_getIntercityExp();
                if (!empty($sButtonText)) {
                    $aResponse['guide_show_type_tips'] = $sButtonText;
                }
            } elseif (ProductCategory::PRODUCT_CATEGORY_LOW_PRICE_CARPOOL == $iProductCategory) {
                $aResponse['guide_show_type_tips'] = $aText['pincheche_tips'];
            } elseif (ProductCategory::PRODUCT_CATEGORY_AUTO_DRIVING == $iProductCategory) {
                $aResponse['guide_show_type_tips'] = $aText['autodriving_tips'];
            } elseif (MainDataRepo::isBelongTripCloud($iProductCategory)) {
                $aResponse['guide_show_type_tips'] = $aText['intercity_tips'];
            }
        }

        //category_id 兜底
        $aOneConfResult = MainDataRepo::getBasicConfByProductCategory($iProductCategory);
        if (!isset($aResponse['category_id']) && isset($aOneConfResult['category_id'])) {
            $aResponse['category_id'] = $aOneConfResult['category_id'];
        }

        // disabled & usage_info 字段
        $aResponse = array_merge($aResponse, $this->_getDisabledAndUsageInfoFields());

        return $aResponse;
    }

    /**
     * _getDisabledAndUsageInfoFields
     *
     * @return array
     */
    private function _getDisabledAndUsageInfoFields() {
        $aResponse = ['disabled' => 0,];
        $iPC       = $this->_oProduct->oOrderInfo->iProductCategory;
        $aNTuples  = [
            'product_id'    => $this->_oProduct->oOrderInfo->iProductId,
            'require_level' => $this->_oProduct->oOrderInfo->iRequireLevel,
            'combo_type'    => $this->_oProduct->oOrderInfo->iComboType,
            'level_type'    => $this->_oProduct->oOrderInfo->iLevelType,
        ];

        if (\BizCommon\Utils\Horae::isAplus($aNTuples)) {
            // A+ 滴滴特快 disabled
            return $this->_doAplus($aResponse);
        }

        if (in_array(
            $iPC,
            [
                ProductCategory::PRODUCT_CATEGORY_DI_XIAO_DI,
                ProductCategory::PRODUCT_CATEGORY_FAST_SPECIAL_RATE,
            ]
        )
        ) {
            // 特惠快车
            return $this->_doFastCarSpecialRate($aResponse);
        }

        // 默认
        return $aResponse;
    }

    /**
     * DoFastCarSpecialRate.
     *
     * @param array $aResponse aResponse
     * @return mixed
     */
    private function _doFastCarSpecialRate($aResponse) {
        // 特惠快车
        $aText         = NuwaConfig::text('config_text', 'special_rate');
        $aDecisionInfo = DecisionLogic::getInstance()->getDecisionProductList();
        foreach ($aDecisionInfo as $aProduct) {
            if ($this->_oProduct->oOrderInfo->iProductCategory == $aProduct['product_category']) {
                if (true == $aProduct['remove_flag'] && ('5' == $aProduct['remove_reason'] || '6' == $aProduct['remove_reason'])) {
                    $aResponse['disabled']      = 1;
                    $aResponse['disabled_text'] = $aText['special_rate_disabled_text'][$aProduct['remove_reason']] ?? '';
                }
            }
        }

        return $aResponse;
    }

    /**
     * DoAplus.
     *
     * @param array $aResponse aResponse
     * @return mixed
     */
    private function _doAplus($aResponse) {
        $oApollo = new Apollo();
        $oToggle = $oApollo->featureToggle(
            'gs_aplus_gray_hide_level',
            [
                'key'           => $this->_aInfo['passenger_info']['pid'],
                'city'          => $this->_aInfo['order_info']['area'],
                'phone'         => $this->_aInfo['passenger_info']['phone'],
                'app_version'   => $this->_aInfo['common_info']['app_version'],
                'access_key_id' => $this->_aInfo['common_info']['access_key_id'],
            ]
        );
        if ($oToggle->allow()) {
            $aDecisionInfo = DecisionLogic::getInstance()->getDecisionProductByCategoryId($this->_oProduct->oOrderInfo->iProductCategory);
            if ($aDecisionInfo['remove_flag'] && DecisionLogic::REMOVE_REASON_HIDE_LEVEL == $aDecisionInfo['remove_reason']) {
                $aText = NuwaConfig::text('config_text', 'aplus');
                $aResponse['disabled']      = 1;
                $aResponse['disabled_text'] = $aText['aplus_disabled_text'][$aDecisionInfo['remove_reason']] ?? '';
                return $aResponse;
            }
        }

        return $aResponse;
    }

    /**
     * @desc 判断是否推荐位
     * @param array $aInfo 订单信息
     * @return bool
     */
    private function _firstShow($aInfo) {
        if (!isset($aInfo['product_category'])) {
            return false;
        }

        $aDdsProduct = DecisionLogic::getInstance()->getProductInfoByCategoryId($aInfo['product_category']);
        if (isset($aDdsProduct['first_show']) && $aDdsProduct['first_show']) {
            return true;
        }

        return false;
    }

    /**
     * @param array $aResponse aResponse
     * @return array
     */
    // 注释-待删
//    private function _doAplusFrequencyLimit(array $aResponse) {
//
//       // if (false == $this->_oProduct->oUsageInfo->doHaveInfo()) {
//           // return $aResponse;
//       // }
//        // // $aUsageInfo
//       // // "usage_info": { // 使用信息，展示在车型下方
//       // //                   "alert": "滴滴特快今日使用次数已用完", // 弹窗
//       // //                   "left_text": "今日：还剩{2}次，可用3次", // 左侧文案
//       // //                   "right_text": "建议仅在急需用车时使用", // 右侧
//       // //               },
//       // $aAnyCarText  = NuwaConfig::text('config_text', 'anycar');
//       // $iCurUseTimes = $this->_oProduct->oUsageInfo->getCurTimes();
//       // $iLimitTimes  = $this->_oProduct->oUsageInfo->getLimitTimes();
//       // $iLimitTimes  = max($iLimitTimes, 0);
//       // $iCurUseTimes = max($iCurUseTimes, 0);
//       // $aUsageInfo   = [];
//       // if ($iLimitTimes > $iCurUseTimes) {
//           // // 还剩一次 or 剩余多次
//           // $aUsageInfo['left_text']  = Language::replaceTag(
//               // $aAnyCarText['aplus_frequency_limit_left_text'] ?? '',
//               // [
//                   // 'cur_times'   => $iLimitTimes - $iCurUseTimes,
//                   // 'limit_times' => $iLimitTimes,
//               // ]
//           // );
//           // $aUsageInfo['right_text'] = $aAnyCarText['aplus_frequency_limit_right_text'] ?? '';
//       // }
//        // if ($iLimitTimes - 1 == $iCurUseTimes) {
//           // // 还剩一次 追加弹窗 强感知
//           // $aUsageInfo['alert'] = $aAnyCarText['aplus_frequency_limit_alert_text'] ?? '';
//       // }
//        // if ($iLimitTimes <= $iCurUseTimes) {
//           // $aResponse['disabled']      = 1;
//           // $aResponse['disabled_text'] = $aAnyCarText['aplus_frequency_limit_disabled_text'] ?? '';
//       // }
//        // if (!empty($aUsageInfo)) {
//           // $aResponse['usage_info'] = $aUsageInfo;
//       // }
//        return $aResponse;
//    }

    /**
     * 远途拼车导流位实验
     * @return string
     */
    private function _getIntercityExp() {
        $sButtonText  = '';
        $aApolloParam = [
            \Xiaoju\Apollo\ApolloConstant::APOLLO_INDIVIDUAL_ID => $this->_aInfo['passenger_info']['uid'],
            'phone'         => $this->_aInfo['passenger_info']['phone'],
            'city'          => $this->_aInfo['order_info']['area'],
            'pid'           => $this->_aInfo['passenger_info']['pid'],
            'uid'           => $this->_aInfo['passenger_info']['uid'],
            'route_group'   => $this->_aInfo['order_info']['match_routes'][0]['route_group'],
            'combo_id'      => $this->_aInfo['order_info']['combo_id'],
            'access_key_id' => $this->_aInfo['common_info']['access_key_id'],
            'app_version'   => $this->_aInfo['common_info']['app_version'],
        ];
        // 实验1
        $oApollo = (ApolloV2::getInstance())->featureToggle('bubble_ganzhi_jining', $aApolloParam);
        if ($oApollo->allow()) {
            $sButtonText = $oApollo->getParameter('button_text', '');
        }

        // 实验2
        $oApolloMini = (ApolloV2::getInstance())->featureToggle('bubble_ganzhi0524_0530', $aApolloParam);
        if ($oApolloMini->allow()) {
            $sButtonText = $oApolloMini->getParameter('button_text', '');
        }

        return $sButtonText;
    }
}
