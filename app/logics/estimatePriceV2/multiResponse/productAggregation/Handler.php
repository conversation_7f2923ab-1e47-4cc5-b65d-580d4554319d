<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\productAggregation;

use PreSale\Logics\estimatePriceV2\multiRequest\ProductGroup;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutBuilder;

/**
 * Class Handler
 * @package PreSale\Logics\estimatePriceV2\multiResponse\productAggregation
 */
class Handler
{
    const SUB_GROUP_ID_SHORT_DISTANCE = 1;  // 短途特惠
    const SUB_GROUP_ID_UNITAXI        = 2;   // 出租车
    const SUB_GROUP_ID_RECOMMEND      = 3;  //打包置顶推荐
    const SUB_GROUP_ID_MOCK_RECOMMEND = 100; //借用sub_group_id

    /**
     * @param int   $iSubGroupID sub group id
     * @param array $aExtraInfo  扩展信息
     * @return ShortDistance|UnioneAggregation|RecommendGroup|MockRecommendGroup|TaxiPricingBoxAggregation|null
     */
    public static function getAggregationHandler($iSubGroupID, $aExtraInfo = []) {

        if (LayoutBuilder::SUB_GROUP_ID_TAXI_PRICING == $iSubGroupID) {
            return new TaxiPricingBoxAggregation($aExtraInfo);
        }

        $aGroupConf = ProductGroup::getInstance()->getSubGroupConf($iSubGroupID);
        if (empty($aGroupConf)) {
            return null;
        }

        $oGroupConf = new GroupConf($aGroupConf);
        switch ($iSubGroupID) {
            case self::SUB_GROUP_ID_SHORT_DISTANCE:
                return new ShortDistance($iSubGroupID, $oGroupConf, $aExtraInfo);
            case self::SUB_GROUP_ID_UNITAXI:
                return new UnioneAggregation($iSubGroupID, $aGroupConf);
            case self::SUB_GROUP_ID_RECOMMEND:
                return new RecommendGroup($aGroupConf);
            case self::SUB_GROUP_ID_MOCK_RECOMMEND:
                return new MockRecommendGroup($aGroupConf);
            default:
                return null;
        }
    }
}
