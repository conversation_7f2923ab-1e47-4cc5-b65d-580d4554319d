<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\productAggregation;

use BizLib\Config as NuwaConfig;
use BizLib\Utils\ProductCategory;
use Dirpc\SDK\PreSale\EstimateData;
use PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo\PriceItemFormatter;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use BizLib\Utils\Language;
use PreSale\Logics\v3Estimate\DecisionV2Service;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TaxiPricingBoxLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutBuilder;

/**
 * 出租车计价盒子
 * Class TaxiPricingBoxAggregation
 * @package PreSale\Logics\estimatePriceV2\multiResponse\productAggregation
 */

class TaxiPricingBoxAggregation
{
    private $_aSubProducts;
    private $_aOuterProduct;

    private $_PricingBoxProducts;

    private $_aConfig;

    // 出租车各品类的排序权重，值越小排序越优先
    private $_aUniTaxiProductPriority = array(
        188                                      => 1,
        ProductCategory::PRODUCT_CATEGORY_UNIONE => 2,
    );

    /**
     *  constructor.
     * @param array $aInfos  $aInfos
     */
    public function __construct( $aInfos) {
        $this->_aSubProducts  = array();
        $this->_aOuterProduct = json_decode(json_encode(new EstimateData()), true);
        $this->_aConfig       = NuwaConfig::text('config_text', 'getEstimateFeeInfo');

        list($newPcIDToSubGroupID, $aSubGroupIDToProducts) =
            DecisionV2Service::getAggConfByGroupId([LayoutBuilder::SUB_GROUP_ID_TAXI_PRICING], TaxiPricingBoxLayout::buildParams($aInfos[0]));
        $this->_PricingBoxProducts = $aSubGroupIDToProducts[LayoutBuilder::SUB_GROUP_ID_TAXI_PRICING];
    }

    /**
     * 聚合
     * @param array $aEstimateDataList estimateDataList
     * @return array
     */
    public function buildAggregation($aEstimateDataList) {
        // 只有6.0.4版本及以后才有聚合虚拟车型的概念
        if (version_compare(MainDataRepo::getAppVersion(), '6.0.4') < 0) {
            return $aEstimateDataList;
        }

        $iFastCarPos = 0;  // 快车品类的位置
        $fFeeAmount  = 0;  // hack 价格
        foreach ($aEstimateDataList as $index => $aProductData) {
            // 记录快车品类在数组中的位置，出租车品类要插入在快车品类后面
            if (ProductCategory::PRODUCT_CATEGORY_FAST == $aProductData['product_category']) {
                $iFastCarPos = $index;
            }
            if (in_array($aProductData['product_category'], $this->_PricingBoxProducts)
                && $aProductData['fee_amount'] > 0
            ) {
                $fFeeAmount = $aProductData['fee_amount'];
            }
        }

        $bHaveCar = false;
        foreach ($aEstimateDataList as $index => $aProductData) {

            if (in_array($aProductData['product_category'], $this->_PricingBoxProducts)) {
                $bHaveCar = true;
                $aProductData['priority'] = $this->_aUniTaxiProductPriority[$aProductData['product_category']] ?? 0;
                $aProductData['fee_amount'] = $fFeeAmount;
                array_push($this->_aSubProducts, $aProductData);
                unset($aEstimateDataList[$index]);
            }
        }
        if (!$bHaveCar) {
            return $aEstimateDataList;
        }


        // 对子品类进行排序：优选出租车 > 出租车 > 超值出租车> 出租车拼车
        uasort($this->_aSubProducts, array($this, '_compareProductCategories'));

        // 构建出租车业务外层虚拟车型数据
        $this->_buildOuterProduct();

        // 将聚合的出租车虚拟车型插在快车品类后面
        array_splice($aEstimateDataList, $iFastCarPos + 1, 0, array($this->_aOuterProduct));

        return $aEstimateDataList;
    }

    /**
     * 按品类的priority字段比较大小，作为uasort排序函数的自定义比较大小函数参数
     * @param array $aProductA A车型
     * @param array $aProductB B车型
     * @return int
     * <AUTHOR> <<EMAIL>>
     */
    private function _compareProductCategories($aProductA, $aProductB) {
        return ($aProductA['priority'] < $aProductB['priority']) ? -1 : 1;
    }

    /**
     * 按加费的priority字段比较大小，作为uasort排序函数的自定义比较大小函数参数
     * @param array $aPriceItemA A费项
     * @param array $aPriceItemB B费项
     * @return int
     */
    private function _comparePriceType($aPriceItemA, $aPriceItemB) {
        return ($aPriceItemA['priority'] < $aPriceItemB['priority']) ? -1 : 1;
    }

    /**
     * 构建外层虚拟品类数据
     * @return void
     */
    private function _buildOuterProduct() {
        $aDcmpConfig    = Language::getDecodedTextFromDcmp('estimate_new_form-taxi_pricing_layout');

        $this->_aOuterProduct['category_id']        = 1;   // 1 经济； 2 舒适； 3 豪华
        $this->_aOuterProduct['fee_msg']            = $aDcmpConfig['fee_msg'] ?: '';
        $this->_aOuterProduct['sub_product']        = $this->_aSubProducts;
        $this->_aOuterProduct['intro_msg']          = $aDcmpConfig['car_title'];
        $this->_aOuterProduct['sub_group_id']       = LayoutBuilder::SUB_GROUP_ID_SHORT_DISTANCE;
        $this->_aOuterProduct['sub_page_title']     = $aDcmpConfig['popup_title'];
        $this->_aOuterProduct['sub_page_sub_title'] = $aDcmpConfig['popup_sub_title'];

        $this->_setOuterProductRecommendType();
        $this->_setOuterProductPriceInfoDesc();
    }

    /**
     * 设置外层品类的recommend_type字段,
     * 只要子品类中有一个品类的recommend_type=1，则外层的recommend_type=1
     * @return void
     */
    private function _setOuterProductRecommendType() {
        $iRecommendType = 0;
        $iSelectType = 0;

        $iSelectTypeNum = 0; // select_type=1 计数器
        $sSelectionTag  = null;

        foreach ($this->_aSubProducts as $aProduct) {
            if (1 == $aProduct['recommend_type']) {
                $iRecommendType = 1;
            }

            if (1 == $aProduct['select_type']) {
                $iSelectType = 1;
                $iSelectTypeNum += 1;
                $sSelectionTag   = $aProduct['selection_tag'];
            }
        }

        $this->_aOuterProduct['recommend_type'] = $iRecommendType;
        $this->_aOuterProduct['select_type'] = $iSelectType;


        //  * 当sub_product子品类数组中只有一个品类被勾选（select_type=1）时，
        //     * 将该品类的selection_tag参数值挪到外层虚拟品类
        if (1 == $iSelectTypeNum) {
            $this->_aOuterProduct['selection_tag'] = $sSelectionTag;
        }

    }

    /**
     * 构建外层品类的price_info_desc字段，
     * @return void
     */
    private function _setOuterProductPriceInfoDesc() {
        // 减价项优先级更高
        $aDecrease = $this->_buildDecreasePrice();
        if (!empty($aDecrease)) {
            $this->_aOuterProduct['price_info_desc'] = $aDecrease;
            return;
        }

        // 在看加价项
        $aIncrease = $this->_buildIncreasePrice();
        if (!empty($aIncrease)) {
            $this->_aOuterProduct['price_info_desc'] = $aIncrease;
        }
    }

    /**
     * 构建外层品类的price_info_desc减价项部分，
     * @return array|\string[][] []
     */
    private function _buildIncreasePrice() {
        $sIconUrl      = '';
        $aIncreaseItem = [];
        foreach ($this->_aSubProducts as $aProduct) {
            if (empty($aProduct['price_info_desc'])) {
                continue;
            }

            if (!empty($aProduct['price_info_desc'][0]) && !empty($aProduct['price_info_desc'][0]['type'])
                && in_array(
                    $aProduct['price_info_desc'][0]['type'],
                    [
                        PriceItemFormatter::PRICE_TYPE_TAXI_PEAK_FEE,
                        PriceItemFormatter::PRICE_TYPE_TAXI_HOLIDAY_FEE,
                        PriceItemFormatter::PRICE_TYPE_TAXI_CROSS_CITY_FEE,
                    ]
                )
            ) {
                $aIncreaseItem[] = $aProduct['price_info_desc'][0];
            }
        }

        uasort($aIncreaseItem, array($this, '_comparePriceType'));

        if (!empty($aIncreaseItem)) {
            return [
                [
                    'content'   => $this->_aConfig['unione_aggregation_increase'].$aIncreaseItem[0]['content'],
                    'left_icon' => $sIconUrl,
                ],
            ];
        }

        return [];

    }

    /**
     * 构建外层品类的price_info_desc减价项部分，
     * @return []
     */
    private function _buildDecreasePrice() {
        $fMaxCouponValue = 0; // 记录子品类中最大的优惠券金额
        $sIconUrl        = '';

        foreach ($this->_aSubProducts as $aProduct) { // for循环中获取所有品类中最大值的券信息
            if (empty($aProduct['price_info_desc'])) {
                continue;
            }

            foreach ($aProduct['price_info_desc'] as $aPriceItem) {
                if (PriceItemFormatter::getDefaultPriceType() == $aPriceItem['type']) {
                    $sContent = $aPriceItem['content'];
                    preg_match_all('!\d+\.*\d*!', $sContent, $match);
                    $fCouponValue = $match[0][0];
                    if ($aPriceItem['left_icon']) {
                        $sIconUrl = $aPriceItem['left_icon'];
                    }

                    if ($fMaxCouponValue < $fCouponValue) {
                        $fMaxCouponValue = $fCouponValue;
                    }
                }
            }
        }

        if ($fMaxCouponValue > 0) {  // 大于0，说明有券
            $sCouponDesc = Language::replaceTag(
                $this->_aConfig['unione_aggregation_discount'],
                array('amount' => $fMaxCouponValue)
            );

            return [
                [
                    'content'   => $sCouponDesc,
                    'left_icon' => $sIconUrl,
                ],
            ];
        }

        return [];

    }
}
