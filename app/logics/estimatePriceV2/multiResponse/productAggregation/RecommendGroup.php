<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\productAggregation;

use PreSale\Logics\estimatePriceV2\multiRequest\OrderInfo;
use PreSale\Logics\estimatePriceV2\multiResponse\component\SortSelection;

/**
 * Class RecommendGroup
 * @package PreSale\Logics\estimatePriceV2\multiResponse\productAggregation
 */
class RecommendGroup
{

    private $_aGroupConf;

    /**
     * RecommendGroup constructor.
     * @param array $aGroupConf $aGroupConf
     */
    public function __construct($aGroupConf) {
        $this->_aGroupConf = $aGroupConf;
    }

    /**
     * @param array $aEstimateData $aEstimateData
     * @return mixed
     */
    public function buildAggregation($aEstimateData) {
        if (empty($this->_aGroupConf['product_list']) || count($this->_aGroupConf['product_list']) <= 1) {
            return $aEstimateData;
        }

        $aSubProduct = [];
        foreach ($aEstimateData as $iIndex => $aItem) {
            if (in_array($aItem['product_category'], $this->_aGroupConf['product_list'])) {
                $aSubProduct[] = $aItem;
                unset($aEstimateData[$iIndex]);
            }
        }

        $aGroupItem = $this->_buildGroupItem($aSubProduct);

        array_unshift($aEstimateData, $aGroupItem);
        return $aEstimateData;
    }

    /**
     * @param array $aSubProduct $aSubProduct
     * @return array
     */
    private function _buildGroupItem($aSubProduct) {

        $aSubProductSequence = $this->_aGroupConf['product_sequence'];
        if (!empty($aSubProductSequence)) {
            usort(
                $aSubProduct,
                function ($aFirst, $aSecond) use ($aSubProductSequence) {
                    return (int)$aSubProductSequence[$aFirst['product_category']] - (int)$aSubProductSequence[$aSecond['product_category']];
                }
            );
        }

        $sThemeType = array_filter(array_column($aSubProduct, 'theme_type'))[0];
        $aThemeData = array_filter(array_column($aSubProduct, 'theme_data'))[0];

        $bHasSelection = false;
        foreach ($aSubProduct as $aProduct) {
            if (SortSelection::DACHE_ANYCAR_ITEM_SELECTED == $aProduct['select_type']) {
                $bHasSelection = true;
                break;
            }
        }

        return [
            'sub_group_id'   => $this->_aGroupConf['sub_group_id'],
            'form_show_type' => OrderInfo::FORM_SHOW_TYPE_FIRST,
            'recommend_type' => SortSelection::DACHE_ANYCAR_ITEM_RECOMMEND,
            'select_type'    => $bHasSelection ? SortSelection::DACHE_ANYCAR_ITEM_SELECTED : SortSelection::DACHE_ANYCAR_ITEM_NOT_SELECTED,
            'sub_product'    => $aSubProduct,
            'theme_type'     => $sThemeType,
            'theme_data'     => $aThemeData,
        ];
    }
}
