<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\productAggregation;

use BizLib\Config as NuwaConfig;
use BizLib\Utils\ProductCategory;
use Dirpc\SDK\PreSale\EstimateData;
use Dirpc\SDK\Veyron\info;
use PreSale\Logics\estimatePriceV2\multiResponse\component\priceDescInfo\PriceItemFormatter;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use BizLib\Utils\Language;

/**
 * 需求文档: http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=376475185
 * 乘客端6.0需要对出租车业务的多个子车型（出租车、优选出租车、超值出租车、出租车拼车）进行接入，
 * 让客户能够在预估冒泡中选择出租车的子车型出行，本类对多个出租车子车型进行聚合构建外层的虚拟车型
 * Class UnioneAggregation
 * @package PreSale\Logics\estimatePriceV2\multiResponse\productAggregation
 */

class UnioneAggregation
{
    private $_iSubGroupID;
    private $_aGroupConf;
    private $_aSubProducts;
    private $_aOuterProduct;

    private $_aConfig;

    // 出租车各品类的排序权重，值越小排序越优先
    private $_aUniTaxiProductPriority = array(
        ProductCategory::PRODUCT_CATEGORY_YOUXUAN_TAXI         => 1,
        ProductCategory::PRODUCT_CATEGORY_UNIONE               => 2,
        ProductCategory::PRODUCT_CATEGORY_UNIONE_SPECIAL_PRICE => 3,
        ProductCategory::PRODUCT_CATEGORY_UNIONE_CARPOOL       => 4,
    );
    // 出租车各品类加价项的排序权重，值越小排序越优先
    private $_aUniTaxiPricePriority = array(
        PriceItemFormatter::PRICE_TYPE_TAXI_HOLIDAY_FEE    => 1,
        PriceItemFormatter::PRICE_TYPE_TAXI_PEAK_FEE       => 2,
        PriceItemFormatter::PRICE_TYPE_TAXI_CROSS_CITY_FEE => 3,
    );
    /**
     * UnioneAggregation constructor.
     * @param int   $iSubGroupID sub group id
     * @param array $aGroupConf  group conf
     */
    public function __construct($iSubGroupID, $aGroupConf) {
        $this->_iSubGroupID   = $iSubGroupID;
        $this->_aGroupConf    = $aGroupConf;
        $this->_aSubProducts  = array();
        $this->_aOuterProduct = json_decode(json_encode(new EstimateData()), true);
        $this->_aConfig       = NuwaConfig::text('config_text', 'getEstimateFeeInfo');
    }

    /**
     * @param array $aEstimateDataList estimate data list
     * @return int
     */
    private function _getUniTaxiProductNum($aEstimateDataList) {
        $iUniTaxiProductNum = 0; // 出租车业务车型计数器
        foreach ($aEstimateDataList as $index => $aProductData) {
            if (in_array($aProductData['product_category'], $this->_aGroupConf['product_list'])) {
                $iUniTaxiProductNum += 1;
            }
        }

        return $iUniTaxiProductNum;
    }

    /**
     * 对普通出租车、超值出租车、优选出租车、出租车拼车等品类进行聚合
     * <AUTHOR> <<EMAIL>>
     * @param array $aEstimateDataList estimateDataList
     * @return array
     */
    public function buildAggregation($aEstimateDataList) {
        // 只有6.0.4版本及以后才有聚合虚拟车型的概念
        if (version_compare(MainDataRepo::getAppVersion(), '6.0.4') < 0) {
            return $aEstimateDataList;
        }

        // 当只有一个出租车车型时，直接返回，不用做虚拟聚合车型
        $iUniTaxiProductNum = $this->_getUniTaxiProductNum($aEstimateDataList);
        if ($iUniTaxiProductNum <= 1) {
            return $aEstimateDataList;
        }

        $iFastCarPos = 0;  // 快车品类的位置
        foreach ($aEstimateDataList as $index => $aProductData) {
            // 记录快车品类在数组中的位置，出租车品类要插入在快车品类后面
            if (ProductCategory::PRODUCT_CATEGORY_FAST == $aProductData['product_category']) {
                $iFastCarPos = $index;
            }

            if (in_array($aProductData['product_category'], $this->_aGroupConf['product_list'])) {
                $aProductData['priority'] = $this->_aUniTaxiProductPriority[$aProductData['product_category']] ?? 0;
                array_push($this->_aSubProducts, $aProductData);
                unset($aEstimateDataList[$index]);
            }
        }

        // 对子品类进行排序：优选出租车 > 出租车 > 超值出租车> 出租车拼车
        uasort($this->_aSubProducts, array($this, '_compareProductCategories'));

        // 构建出租车业务外层虚拟车型数据
        $this->_buildOuterProduct();

        // 将聚合的出租车虚拟车型插在快车品类后面
        array_splice($aEstimateDataList, $iFastCarPos + 1, 0, array($this->_aOuterProduct));

        return $aEstimateDataList;
    }

    /**
     * 按品类的priority字段比较大小，作为uasort排序函数的自定义比较大小函数参数
     * @param array $aProductA A车型
     * @param array $aProductB B车型
     * @return int
     * <AUTHOR> <<EMAIL>>
     */
    private function _compareProductCategories($aProductA, $aProductB) {
        return ($aProductA['priority'] < $aProductB['priority']) ? -1 : 1;
    }

    /**
     * 按加费的priority字段比较大小，作为uasort排序函数的自定义比较大小函数参数
     * @param array $aPriceItemA A费项
     * @param array $aPriceItemB B费项
     * @return int
     */
    private function _comparePriceType($aPriceItemA, $aPriceItemB) {
        return ($aPriceItemA['priority'] < $aPriceItemB['priority']) ? -1 : 1;
    }

    /**
     * 构建外层虚拟品类数据
     * <AUTHOR> <<EMAIL>>
     * @return void
     */
    private function _buildOuterProduct() {
        $this->_aOuterProduct['category_id']        = 1;   // 1 经济； 2 舒适； 3 豪华
        $this->_aOuterProduct['fee_msg']            = '打表计价';
        $this->_aOuterProduct['sub_product']        = $this->_aSubProducts;
        $this->_aOuterProduct['intro_msg']          = $this->_aGroupConf['intro_msg'];
        $this->_aOuterProduct['sub_group_id']       = $this->_aGroupConf['sub_group_id'];
        $this->_aOuterProduct['sub_page_title']     = $this->_aGroupConf['sub_page_title'];
        $this->_aOuterProduct['sub_page_sub_title'] = $this->_aGroupConf['sub_page_sub_title'];

        $this->_setOuterProductRecommendType();
        $this->_setOuterProductSelectType();
        $this->_setOuterProductSelectionTag();
        $this->_setOuterProductPriceInfoDesc();
    }

    /**
     * 当sub_product子品类数组中只有一个品类被勾选（select_type=1）时，
     * 将该品类的selection_tag参数值挪到外层虚拟品类
     * <AUTHOR> <<EMAIL>>
     * @return void
     */
    private function _setOuterProductSelectionTag() {
        $iSelectTypeNum = 0; // select_type=1 计数器
        $sSelectionTag  = null;
        foreach ($this->_aSubProducts as $aProduct) {
            if (1 == $aProduct['select_type']) {
                $iSelectTypeNum += 1;
                $sSelectionTag   = $aProduct['selection_tag'];
            }
        }

        if (1 == $iSelectTypeNum) {
            $this->_aOuterProduct['selection_tag'] = $sSelectionTag;
        }
    }

    /**
     * 设置外层品类的recommend_type字段,
     * 只要子品类中有一个品类的recommend_type=1，则外层的recommend_type=1
     * <AUTHOR> <<EMAIL>>
     * @return void
     */
    private function _setOuterProductRecommendType() {
        $iRecommendType = 0;
        foreach ($this->_aSubProducts as $aProduct) {
            if (1 == $aProduct['recommend_type']) {
                $iRecommendType = 1;
            }
        }

        $this->_aOuterProduct['recommend_type'] = $iRecommendType;
    }

    /**
     * 设置外层品类的select_type字段,
     * 只要子品类中有一个品类的select_type=1，则外层的select_type=1
     * <AUTHOR> <<EMAIL>>
     * @return void
     */
    private function _setOuterProductSelectType() {
        $iSelectType = 0;
        foreach ($this->_aSubProducts as $aProduct) {
            if (1 == $aProduct['select_type']) {
                $iSelectType = 1;
            }
        }

        $this->_aOuterProduct['select_type'] = $iSelectType;
    }

    /**
     * 构建外层品类的price_info_desc字段，
     * 需求文档：http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=430135379
     * <AUTHOR> <<EMAIL>>
     * @return void
     */
    private function _setOuterProductPriceInfoDesc() {
        // 减价项优先级更高
        $aDecrease = $this->_buildDecreasePrice();
        if (!empty($aDecrease)) {
            $this->_aOuterProduct['price_info_desc'] = $aDecrease;
            return;
        }

        // 在看加价项
        $aIncrease = $this->_buildIncreasePrice();
        if (!empty($aIncrease)) {
            $this->_aOuterProduct['price_info_desc'] = $aIncrease;
        }
    }

    /**
     * 构建外层品类的price_info_desc减价项部分，
     * @return []
     */
    private function _buildIncreasePrice() {
        $sIconUrl      = '';
        $aIncreaseItem = [];
        foreach ($this->_aSubProducts as $aProduct) {
            if (empty($aProduct['price_info_desc'])) {
                continue;
            }

            if (!empty($aProduct['price_info_desc'][0]) && !empty($aProduct['price_info_desc'][0]['type'])
                && in_array(
                    $aProduct['price_info_desc'][0]['type'],
                    [
                        PriceItemFormatter::PRICE_TYPE_TAXI_PEAK_FEE,
                        PriceItemFormatter::PRICE_TYPE_TAXI_HOLIDAY_FEE,
                        PriceItemFormatter::PRICE_TYPE_TAXI_CROSS_CITY_FEE,
                    ]
                )
            ) {
                $aIncreaseItem[] = $aProduct['price_info_desc'][0];
            }
        }

        uasort($aIncreaseItem, array($this, '_comparePriceType'));

        if (!empty($aIncreaseItem)) {
            return [
                [
                    'content'   => $this->_aConfig['unione_aggregation_increase'].$aIncreaseItem[0]['content'],
                    'left_icon' => $sIconUrl,
                ],
            ];
        }

        return [];

    }

    /**
     * 构建外层品类的price_info_desc减价项部分，
     * @return []
     */
    private function _buildDecreasePrice() {
        $fMaxCouponValue = 0; // 记录子品类中最大的优惠券金额
        $sIconUrl        = '';

        foreach ($this->_aSubProducts as $aProduct) { // for循环中获取所有品类中最大值的券信息
            if (empty($aProduct['price_info_desc'])) {
                continue;
            }

            foreach ($aProduct['price_info_desc'] as $aPriceItem) {
                if (PriceItemFormatter::getDefaultPriceType() == $aPriceItem['type']) {
                    $sContent = $aPriceItem['content'];
                    preg_match_all('!\d+\.*\d*!', $sContent, $match);
                    $fCouponValue = $match[0][0];
                    if ($aPriceItem['left_icon']) {
                        $sIconUrl = $aPriceItem['left_icon'];
                    }

                    if ($fMaxCouponValue < $fCouponValue) {
                        $fMaxCouponValue = $fCouponValue;
                    }
                }
            }
        }

        if ($fMaxCouponValue > 0) {  // 大于0，说明有券
            $sCouponDesc = Language::replaceTag(
                $this->_aConfig['unione_aggregation_discount'],
                array('amount' => $fMaxCouponValue)
            );

            return [
                [
                    'content'   => $sCouponDesc,
                    'left_icon' => $sIconUrl,
                ],
            ];
        }

        return [];

    }
}
