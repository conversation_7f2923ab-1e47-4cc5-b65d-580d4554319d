<?php
/**
 * Created by PhpStorm.
 * Date: 2020/6/28
 * Time: 20:32
 * <AUTHOR> <<EMAIL>>
 */

namespace PreSale\Logics\estimatePriceV2\multiResponse\productAggregation;

use BizCommon\Logics\RegionalGrowth\PassengerLogic as RegionalGrowthPassengerLogic;
use BizCommon\Utils\Order;
use BizLib\Exception\ExceptionWithResp;
use BizLib\Utils\ApolloHelper;
use BizLib\Utils\Language;
use PreSale\Logics\estimatePriceV2\DecisionLogic;
use PreSale\Logics\commonAbility\SpringRedPacketFormatter;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use BizLib\Utils\ProductCategory;
use PreSale\Logics\estimatePriceV2\multiRequest\OrderInfo;
use Xiaoju\Apollo\Apollo as ApolloV2;
use BizLib\ErrCode;
use BizLib\Utils\PublicLog;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutBuilder;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\ShortDistanceCarAllianceLayout;

/**
 * Class ShortDistance
 * @package PreSale\Logics\estimatePriceV2\multiResponse\component
 */
class ShortDistance
{
    protected $_aShortDistanceProduct     = [];
    protected $_subProductList            = []; //特惠品类聚合
    protected $_subProductListCapPrice    = []; //特惠一口价品类
    protected $_subProductListNormal      = []; //特惠普通品类
    protected $_subProductListSpecialRate = []; //滴滴特惠快车品类
    protected $_selectedFeeAmount         = [];
    protected $_shortDistanceItem         = [];
    private $_iSubGroupId;
    private $_oGroupConf;
    private $_aInfos = [];
    private $_aDiscountPriceProduct       = [];        // 含优惠品类
    private $_aCapPriceProduct            = [];        // 全部一口价品类
    private $_subProductContainsSR        = false;
    private $_bIsSpecialRateWxMiniProgram = false;
    private $_aMaterial = []; //特惠快车素材等
    private $_aConfig; //文案

    const AB_TC_NAMESPACE = 'tripcloud_config';
    const AB_TC_FILE      = 'tc_odds';
    const SUB_GROUP_ID    = 1;
    const MATERIAL_ID_SELF_OPERATED = 7;

    /**
     * ShortDistance constructor.
     * @param int       $iSubGroupID group id
     * @param GroupConf $aGroupConf  group conf
     * @param array     $aInfos      各种数据
     */
    public function __construct($iSubGroupID, $aGroupConf, $aInfos = []) {
        $this->_iSubGroupId           = $iSubGroupID;
        $this->_oGroupConf            = $aGroupConf;
        $this->_aShortDistanceProduct = $aGroupConf->aProductList; //$shortDistanceNTuple;
        $this->_bIsSpecialRateWxMiniProgram = MainDataRepo::isSpecialRate();
        $this->_aInfos  = $aInfos;
        $this->_aConfig = Language::getDecodedTextFromDcmp('estimate_new_form-special_alliance');
    }


    /**
     * @param array $aEstimateData 预估数据
     * @return array
     */
    public function buildAggregation($aEstimateData) {
        if (version_compare(MainDataRepo::getAppVersion(), '6.0.2') < 0) {
            return $aEstimateData;
        }

        // 特惠小程序6.0.17及以上，才支持聚合及三方，可通过6.0配置此处保留
        if (version_compare(MainDataRepo::getAppVersion(), '6.0.17') < 0 && $this->_bIsSpecialRateWxMiniProgram) {
            return $aEstimateData;
        }

        // 如果没有配置短途特惠，立即返回
        if (empty($this->_aShortDistanceProduct)) {
            return $aEstimateData;
        }

        $response = [];
        //设置素材
        $this->_setMaterial();
        // 遍历短途特惠车型，并设置短途特惠相对位置
        $shortDistanceIndex = -1;
        //快车原始价格
        $fastCarOriFee = $this->_getFastCarOrinFee();
        $this->_getSpecialProduct();
        foreach ($aEstimateData as $estimateDatum) {
            $isTripcloud      = $estimateDatum['is_tripcloud'];
            $iProductCategory = $estimateDatum['product_category'];

            // "滴滴特惠快车"没有聚合的时候，出现在所有三方运力之前
            // "滴滴特惠快车"聚合的时候，出现在三方运力之前或者聚合结构里面第一个非三方运力的位置
            if ($shortDistanceIndex < 0
                && (1 == $isTripcloud || in_array($iProductCategory, $this->_aShortDistanceProduct))
            ) {
                $response[]         = [];
                $shortDistanceIndex = count($response) - 1;
            }

            //特惠小程序下，都融合进特惠联盟
            if ($this->_bIsSpecialRateWxMiniProgram || in_array($iProductCategory, $this->_aShortDistanceProduct)
            ) {
                $estimateDatum['sub_group_id'] = $this->_iSubGroupId;

                // 判断是否包含有优惠
                if (in_array($iProductCategory, $this->_aDiscountPriceProduct)) {
                    $estimateDatum['agg_discounts_desc'] = $this->_oGroupConf->sDiscountsDesc;
                } else {
                    $estimateDatum['agg_discounts_desc'] = '';
                }

                if ($this->_isSpecialRate($iProductCategory)) {
                    $this->_subProductContainsSR = true;
                }

                $this->_setHighLightType($estimateDatum);
                $this->_setCategoryLogo($estimateDatum);
                $this->_setSubProductList($iProductCategory, $estimateDatum);
            } else {
                $response[] = $estimateDatum;
            }
        }

        //检测特惠小程序，是否存在特惠快车，若无特惠快车则抛异常
        $this->checkWxMiniProgramIncludeSpecialRate();

        $this->_subProductList = $this->_getSortedSubProductList();
        //没有检索到特惠，删除之前的占位符，返回
        if (empty($this->_subProductList)) {
            if (isset($response[$shortDistanceIndex])) {
                unset($response[$shortDistanceIndex]);
            }

            return $response;
        }

        $this->_preRender();
        $this->buildShortDistanceItem();
        $this->setSubProductList();

        $response[$shortDistanceIndex] = $this->_shortDistanceItem;

        return $response;
    }

    /**
     * 构建短途特惠item
     * @return void
     */
    protected function buildShortDistanceItem() {
        $this->setBaseField();
        $this->setIntroMsg();
        $this->setSubPageInfo();
        $this->setSelectAndRecommendField(); // 先设置选中，才能计算费用项
        $this->setUserPayInfo();
        $this->setShowSubPage();
        $this->setPriceInfoDesc();
    }

    /**
     * 构建基础字段
     * @return void
     */
    protected function setBaseField() {
        $this->_shortDistanceItem['category_id']  = $this->_oGroupConf->iCategoryId;
        $this->_shortDistanceItem['sub_group_id'] = $this->_iSubGroupId;
        // 特惠小程序，设置参考价字段
        if ($this->_bIsSpecialRateWxMiniProgram) {
            $this->_shortDistanceItem['reference_fee_amount'] = MainDataRepo::getDiXiaoDiFastCarFee();
        }
    }

    /**
     * 构建intro_msg和sub_title_list
     * @return void
     */
    protected function setIntroMsg() {
        $this->_shortDistanceItem['intro_msg'] = $this->_oGroupConf->sIntroMsg;
        $aSubTitleList = [];
        if (!empty($this->_oGroupConf->aSubTitleList)) {
            if (is_array($this->_oGroupConf->aSubTitleList) && isset($this->_oGroupConf->aSubTitleList[0]['item_config'])) {
                $aSubTitleList = array_column($this->_oGroupConf->aSubTitleList, 'item_config');
            }
        }

        $this->_shortDistanceItem['sub_title_list'] = $aSubTitleList;

        //特惠小程序，需设置icon_url
        if ($this->_bIsSpecialRateWxMiniProgram) {
            foreach ($this->_subProductList as &$productItem) {
                if (!empty($productItem['sub_title_list'][0])) {
                    $productItem['sub_title_list'][0]['icon_url'] = $this->_aMaterial[$productItem['product_category']]['logo'] ?? '';
                } else {
                    $productItem['sub_title_list'][] = ['icon_url' => $this->_aMaterial[$productItem['product_category']]['logo'] ?? ''];
                }

                //特惠小程序，将特惠快车划线价放置外层
                if (Order::isSpecialRateV2($productItem)) {
                    $this->_shortDistanceItem['price_info_desc'] = $productItem['price_info_desc'];
                }
            }
        }
    }

    /**
     * 构建PriceInfoDesc
     * @return void
     */
    protected function setPriceInfoDesc() {

        $bHaveRedPacket = false;
        foreach ($this->_aInfos as $aInfo) {
            $iProductCategory = $aInfo['order_info']['product_category'];
            if (!in_array($iProductCategory, $this->_aShortDistanceProduct)) {
                continue;
            }

            $_oFormatter = new SpringRedPacketFormatter($aInfo);
            list(, $fRedPacketValue) = $_oFormatter->getRedPacketInfoLite();
            if ($fRedPacketValue > 0) {
                $bHaveRedPacket = true;
                break;
            }
        }

        if ($bHaveRedPacket) {
            // 透出春节服务费
            $this->_shortDistanceItem['price_info_desc'][] = [
                'content'   => $this->_aConfig['red_packet'],
                'left_icon' => '',
            ];
        }

        // 构建盒子价格描述
        $fDescFee    = $this->_setOuterProductPriceInfoDesc();
        if ($fDescFee == 0) {
            $this->_shortDistanceItem['price_info_desc'][] = [];
            return;
        }
        $sCouponDesc = Language::replaceTag(
            $this->_aConfig['trip_cloud_desc'],
            array('num' => $fDescFee)
        );

        $this->_shortDistanceItem['price_info_desc'][] = [
            'content'       => $sCouponDesc,
            'left_icon'     => '',
            'show_anim'     => 0,
            'icon'          => '',
            'bg_gradients'  => [],
            'bg_fill_color' => '',
            'font_color'    => '',
            'type'          => '',
            'amount'        => $fDescFee,
        ];
    }

    /**
     * 构建外层品类的price_info_desc减价项部分，
     * @return []
     */
    private function _buildDecreasePrice() {
        $fMaxCouponValue = 0; // 记录子品类中最大的优惠金额

        foreach ($this->_subProductList as $aProduct) { // for循环中获取所有品类中最大值的优惠
            if (empty($aProduct['price_info_desc'])) {
                continue;
            }

            foreach ($aProduct['price_info_desc'] as $aPriceItem) {
                $sContent = $aPriceItem['content'];
                preg_match_all('!\d+\.*\d*!', $sContent, $match);
                $fCouponValue = (float)$match[0][0];

                if ($fMaxCouponValue < $fCouponValue) {
                    $fMaxCouponValue = $fCouponValue;
                }
            }
        }

        return $fMaxCouponValue;
    }

    /**
     * 构建优惠费用字段
     * @return float
     */
    protected function _setOuterProductPriceInfoDesc() {
        // 减价项优先级更高
        $aDecrease = $this->_buildDecreasePrice();
        if (!empty($aDecrease)) {
            return $aDecrease;
        }

        return 0;
    }

    /**
     * 构建推荐和选中字段、特惠小程序特征字段
     * @return void
     */
    protected function setSelectAndRecommendField() {
        // 设置导流字段
        $this->_shortDistanceItem['recommend_type'] = 0;
        $this->_shortDistanceItem['select_type']    = 0;

        //特惠小程序，设置相应字段，修改气泡文案字段
        if ($this->_bIsSpecialRateWxMiniProgram) {
            //特惠小程序勾选气泡
            $this->_shortDistanceItem['selection_tag'] = [
                'content'   => Language::getTextFromDcmp('special_rate-selection_tag'),
                'left_icon' => 1,
            ];
        }

        foreach ($this->_subProductList as &$productItem) {
            // 设置commend_type
            if ((1 == $productItem['recommend_type']) && (0 == $this->_shortDistanceItem['recommend_type'])) {
                $this->_shortDistanceItem['recommend_type'] = 1;
            }

            // 设置select_type
            if (1 == $productItem['select_type']) {
                $this->_selectedFeeAmount[] = $productItem['fee_amount'];
                if (0 == $this->_shortDistanceItem['select_type']) {
                    $this->_shortDistanceItem['select_type'] = 1;
                }
            }

            // 车型右边的推荐语，内页没有，只保留外页
            if (isset($productItem['recommend_tag']) && !empty($productItem['recommend_tag'])) {
                if (!isset($this->_shortDistanceItem['recommend_tag']) || empty($this->_shortDistanceItem['recommend_tag'])) {
                    $this->_shortDistanceItem['recommend_tag'] = $productItem['recommend_tag'];
                }

                unset($productItem['recommend_tag']);
            }

            // 勾选按钮上的气泡tag，内页没有，只保留外页
            if (isset($productItem['selection_tag']) && !empty($productItem['selection_tag'])) {
                if (!isset($this->_shortDistanceItem['selection_tag']) || empty($this->_shortDistanceItem['selection_tag'])) {
                    $this->_shortDistanceItem['selection_tag'] = $productItem['selection_tag'];
                }

                unset($productItem['selection_tag']);
            }

            //推荐置顶标识
            if (!empty($productItem['product_category'])) {
                $aDdsProduct = DecisionLogic::getInstance()->getProductInfoByCategoryId($productItem['product_category']);
                if (isset($aDdsProduct['first_show']) && $aDdsProduct['first_show']) {
                    $this->_shortDistanceItem['form_show_type'] = OrderInfo::FORM_SHOW_TYPE_FIRST;
                }
            }

            //设置样式标识
            if (!empty($productItem['theme_type']) && empty($this->_shortDistanceItem['theme_type'])) {
                $this->_shortDistanceItem['theme_type'] = $productItem['theme_type'];
                unset($productItem['theme_type']);
            }

            //设置样式标识
            if (!empty($productItem['theme_data']) && empty($this->_shortDistanceItem['theme_data'])) {
                $this->_shortDistanceItem['theme_data'] = $productItem['theme_data'];
                unset($productItem['theme_data']);
            }

            //特惠小程序，设置相应字段
            if ($this->_bIsSpecialRateWxMiniProgram && Order::isSpecialRateV2($productItem)) {
                //强制勾选特惠快车，其他走上一单，都应由dds控制，临时方案
                $iForceSelect = ApolloHelper::getConfigContent(RegionalGrowthPassengerLogic::REGIONAL_GROWTH_CONFIG_NAMESPACE, RegionalGrowthPassengerLogic::REGIONAL_GROWTH_CONFIG_COMMON_CONFIG)['force_select'] ?? 0;
                if (1 == $iForceSelect) {
                    $productItem['select_type'] = 1;
                }
            }
        }
    }

    /**
     * 构建sub_product
     * @return void
     */
    protected function setSubProductList() {
        $this->_shortDistanceItem['sub_product'] = $this->_subProductList;
    }


    /**
     * @return void
     */
    protected function setSubPageInfo() {
        $this->_shortDistanceItem['sub_page_title']     = $this->_oGroupConf->sSubPageTitle;
        $this->_shortDistanceItem['sub_page_sub_title'] = $this->_oGroupConf->sSubPageSubTitle;
    }

    /**
     * @return void
     */
    protected function setUserPayInfo() {
        $userPayInfo = [];
        foreach ($this->_subProductList as $product) {
            if (isset($product['user_pay_info']) && !empty($product['user_pay_info'])) {
                $userPayInfo = $product['user_pay_info'];
                break;
            }
        }

        $this->_shortDistanceItem['user_pay_info'] = $userPayInfo;
    }

    /**
     * 获取快车原始价格
     * @return int|mixed
     */
    private function _getFastCarOrinFee() {

        foreach ($this->_aInfos as $aInfo) {
            $iProductCategory = $aInfo['order_info']['product_category'];
            if (!empty($iProductCategory) && ProductCategory::PRODUCT_CATEGORY_FAST === (int)($iProductCategory)) {
                $aBills    = $aInfo['bill_info']['bills'] ?? [];
                $sCarLevel = $aInfo['order_info']['require_level'] ?? 0;
                if (empty($aBills) || empty($sCarLevel)) {
                    return 0;
                }

                return $aBills[$sCarLevel]['dynamic_total_fee'] ?? 0;
            }
        }

        return 0;
    }

    // 注释-待删
//    /**
//     * 价格门槛判断是否展示tc车型
//     * @param int $fastCarFee       快车券前价格
//     * @param int $shortDistanceFee 特惠快车价格
//     * @return bool
//     */
//    private function _isShowTCByPrice($fastCarFee, $shortDistanceFee) {
//        if ($fastCarFee <= 0) {
//            return true;
//        }
//
//        $oConfig            = ApolloV2::getInstance()->getConfigResult(self::AB_TC_NAMESPACE, self::AB_TC_FILE);
//        list($bOk, $aValue) = $oConfig->getAllConfig();
//        if (!$bOk || empty($aValue['display_discount'])) {
//            return true;
//        }
//
//        return $shortDistanceFee <= $fastCarFee * $aValue['display_discount'];
//    }

    /**
     * 按照价格排序
     * @param array $subProductList 排序品类
     * @return void
     */
    private function _sortSubProductList(&$subProductList) {
        usort(
            $subProductList,
            function ($a, $b) {
                return ($a['fee_amount'] - $b['fee_amount']) > 0 ? 1 : -1;
            }
        );
    }

    /**
     * 获取有优惠价格的特惠品类
     * @return void
     */
    private function _getSpecialProduct() {
        foreach ($this->_aInfos as $aInfo) {
            $iProductCategory = $aInfo['order_info']['product_category'];
            if (!in_array($iProductCategory, $this->_aShortDistanceProduct)) {
                continue;
            }

            $aBills      = $aInfo['bill_info']['bills'] ?? [];
            $sCarLevel   = $aInfo['order_info']['require_level'] ?? 0;
            $totalFee    = $aBills[$sCarLevel]['dynamic_total_fee'] ?? 0;
            $estimateFee = $aInfo['activity_info'][0]['exact_estimate_fee'] ?? 0;
            $capPrice    = $aBills[$sCarLevel]['cap_price'] ?? 0;
            if ($totalFee > 0 && $estimateFee > 0 && bccomp($totalFee, $estimateFee, 2) > 0) {
                $this->_aDiscountPriceProduct[] = $iProductCategory;
            }

            if ($capPrice > 0) {
                $this->_aCapPriceProduct[] = $iProductCategory;
            }
        }
    }

    /**
     * 设置是否可以直接弹出聚合的内页
     * @return void
     */
    protected function setShowSubPage() {
        if ($this->_subProductContainsSR) {
            $this->_shortDistanceItem['show_sub_page_directly'] = true;
        }
    }

    /**
     * 检查特惠小程序返回是否包含特惠快车
     * @return void
     * @throws ExceptionWithResp 无特惠快车抛异常
     */
    protected function checkWxMiniProgramIncludeSpecialRate() {
        //特惠小程序必须出特惠快车，无特惠快车则抛异常
        if (false == $this->_subProductContainsSR && $this->_bIsSpecialRateWxMiniProgram) {
            throw new ExceptionWithResp(
                ErrCode\Code::E_COMMON_NETWORK_EXCEPTION,
                (string)ErrCode\RespCode::P_PARAMS_ERROR,
                '',
                'errmsg: empty special rate data'
            );
        }
    }

    /**
     * 设置subProductList
     * @param int   $iProductCategory 品类ID
     * @param array $estimateDatum    预估单个数据
     * @return void
     */
    private function _setSubProductList($iProductCategory, $estimateDatum) {
        switch (true) {
            case $this->_isSpecialRate($iProductCategory): // first：特惠快车
                $this->_subProductListSpecialRate[] = $estimateDatum;
                break;
            case in_array($iProductCategory, $this->_aCapPriceProduct): // second：一口价品类
                $this->_subProductListCapPrice[] = $estimateDatum;
                break;
            default:                                                   // default：普通品类
                $this->_subProductListNormal[] = $estimateDatum;
        }
    }
    /**
     * 设置高亮类型
     * @param array $estimateDatum $iProductCategory
     * @return void
     */
    private function _setHighLightType(&$estimateDatum) {
        $estimateDatum['highlight_type'] = 0;

        if ($this->_isSpecialRate($estimateDatum['product_category'])) {
            $estimateDatum['highlight_type'] = 1;
        }
    }
    /**
     * 设置品牌logo
     * @param array $estimateDatum $iProductCategory
     * @return void
     */
    private function _setCategoryLogo(&$estimateDatum) {
        $iProductCategory            = $estimateDatum['product_category'] ?? 0;
        $estimateDatum['brand_logo'] = $this->_aMaterial[$iProductCategory]['logo'] ?? '';
    }

    /**
     * 获取排序后的subProductList
     * @return array
     */
    private function _getSortedSubProductList() {
        if (empty($this->_subProductListSpecialRate) && empty($this->_subProductListCapPrice)
            && empty($this->_subProductListNormal)
        ) {
            return [];
        }

        //分组内小于2个元素没必要排序，节约CPU
        if (count($this->_subProductListSpecialRate) >= 2) {
            $this->_sortSubProductList($this->_subProductListSpecialRate);
        }

        if (count($this->_subProductListCapPrice) >= 2) {
            $this->_sortSubProductList($this->_subProductListCapPrice);
        }

        if (count($this->_subProductListNormal) >= 2) {
            $this->_sortSubProductList($this->_subProductListNormal);
        }

        return array_merge(
            $this->_subProductListSpecialRate,
            $this->_subProductListCapPrice,
            $this->_subProductListNormal
        );
    }

    /**
     * 判断是否存在自营三方品类
     * @param array $aPcIds $aPcIds
     * @return bool
     */
    private function _bHaveSelfOperated($aPcIds) {
        foreach ($aPcIds as $iPcId) {
            if (ShortDistanceCarAllianceLayout::bHaveSelfOperated($iPcId)) {
                return true;
            }
        }

        return false;
    }

    /**
     * http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=491111532
     * 预渲染盒子的文案
     * @return void
     */
    private function _preRender() {
        $aCurrentInfo = current($this->_aInfos);
        $iMaterialId  = -1;

        if ($this->_oGroupConf->bIsTestCity) {
            $iMaterialId = $this->_oGroupConf->iMaterialId;
        } else {
            $sApollo   = 'discounts_aggregation_estimate_material_conf';
            $aCond     = [
                'key'           => $aCurrentInfo['passenger_info']['pid'] ?? time(),
                'phone'         => $aCurrentInfo['passenger_info']['phone'] ?? '',
                'city'          => $aCurrentInfo['order_info']['area'] ?? '',
                'access_key_id' => $aCurrentInfo['common_info']['access_key_id'],
                'lang'          => $aCurrentInfo['common_info']['lang'],
                'contain61'     => $this->_subProductContainsSR ? 1 : -1,
                'containsr'     => $this->_subProductContainsSR ? 1 : -1,
            ];
            $oApolloTP = ApolloV2::getInstance()->featureToggle($sApollo, $aCond);
            if ($oApolloTP->allow()) {
                $iMaterialId = $oApolloTP->getParameter('material_id', -1);
            }
        }

        $aMaterialConfig = [];
        if ($iMaterialId > 0) {
            $namespace   = 'aggregation_estimate_material';
            $aCond       = [
                'sub_group_id' => $this->_iSubGroupId,
                'material_id'  => $iMaterialId,
            ];
            $bHaveSelfOperated = $this->_bHaveSelfOperated($this->_aShortDistanceProduct);
            if ($bHaveSelfOperated) {
                $aCond['material_id'] = self::MATERIAL_ID_SELF_OPERATED;
            }

            $oConfResult = ApolloV2::getInstance()->getConfigsByNamespaceAndConditions($namespace, $aCond);
            list($bOk, $aMaterialConfig) = $oConfResult->getAllConfigData();
            if (!$bOk || empty($aMaterialConfig)) {
                return;
            }
        }

        if (empty($aMaterialConfig)) {
            return;
        }

        $aConfig = current(array_values($aMaterialConfig));
        $this->_oGroupConf->sIntroMsg        = $aConfig['intro_msg'] ?? $this->_oGroupConf->sIntroMsg;
        $this->_oGroupConf->aSubTitleList    = $aConfig['sub_title_list'] ?? [];
        $this->_oGroupConf->sSubPageTitle    = $aConfig['sub_page_title'] ?? '';
        $this->_oGroupConf->sSubPageSubTitle = $aConfig['sub_page_sub_title'] ?? '';
    }

    /**
     * 设置特惠品类相关素材
     * @return void
     */
    private function _setMaterial() {
        $aConfRet = ApolloHelper::getConfigContent('regional_growth_config', 'product_config');
        foreach ($aConfRet['data'] as $aConfig) {
            $this->_aMaterial[$aConfig['product_category']] = [
                'biz_name' => $aConfig['product_name'],
                'logo'     => $aConfig['logo'],
            ];
        }
    }

    /**
     * 判断是否是特惠快车，用于切换到独立业务线
     * @param int $iProductCategory 品类id
     * @return bool
     */
    private function _isSpecialRate($iProductCategory) {
        $aSpecialRateProduct = [
            ProductCategory::PRODUCT_CATEGORY_FAST_SPECIAL_RATE,
            ProductCategory::PRODUCT_CATEGORY_SPECIAL_RATE,
        ];

        return in_array($iProductCategory, $aSpecialRateProduct);
    }
}

