<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\productAggregation;

/**
 * Class GroupConf
 * @package PreSale\Logics\estimatePriceV2\multiResponse\productAggregation
 */
class GroupConf
{
    public $iSubGroupId;
    public $sIntroMsg;
    public $sSubPageTitle;
    public $sSubPageSubTitle;
    public $sDiscountsDesc;
    public $aProductList;
    public $iCategoryId;
    public $aSubTitleList;
    public $bIsTestCity;
    public $iMaterialId;

    /**
     * GroupConf constructor.
     * @param array $aGroupConf group conf
     */
    public function __construct($aGroupConf) {
        $this->iSubGroupId      = $aGroupConf['sub_group_id'] ?? 0;
        $this->sIntroMsg        = $aGroupConf['intro_msg'] ?? '';
        $this->sSubPageTitle    = $aGroupConf['sub_page_title'] ?? '';
        $this->sSubPageSubTitle = $aGroupConf['sub_page_sub_title'] ?? '';
        $this->sDiscountsDesc   = $aGroupConf['discounts_desc'] ?? '';
        $this->aProductList     = $aGroupConf['product_list'] ?? [];
        $this->iCategoryId      = $aGroupConf['category_id'] ?? '';
        $this->aSubTitleList    = $aGroupConf['sub_title_list'] ?? [];
        $this->bIsTestCity      = $aGroupConf['is_test_city'] ?? false;
        $this->iMaterialId      = $aGroupConf['material_id'] ?? -1;
    }
}
