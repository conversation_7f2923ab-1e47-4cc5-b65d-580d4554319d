<?php
namespace PreSale\Logics\estimatePriceV2\multiResponse\productAggregation;

use PreSale\Logics\estimatePriceV2\DecisionLogic;
use PreSale\Logics\estimatePriceV2\multiRequest\OrderInfo;
use PreSale\Logics\estimatePriceV2\multiResponse\component\SortSelection;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use \BizLib\Constants\Common;

/**
 * Class RecommendGroup
 * @package PreSale\Logics\estimatePriceV2\multiResponse\productAggregation
 */
class MockRecommendGroup
{

    private $_aGroupConf;

    /**
     * RecommendGroup constructor.
     * @param array $aGroupConf $aGroupConf
     */
    public function __construct($aGroupConf) {
        $this->_aGroupConf = $aGroupConf;
    }

    /**
     * @param array $aEstimateData $aEstimateData
     * @return mixed
     */
    public function buildAggregation($aEstimateData) {
        if (empty($this->_aGroupConf['product_list']) || count($this->_aGroupConf['product_list']) <= 1) {
            return $aEstimateData;
        }

        $aSubProduct = [];
        foreach ($aEstimateData as $iIndex => $aItem) {
            if (in_array($aItem['product_category'], $this->_aGroupConf['product_list'])) {
                $aSubProduct[] = $aItem;
                unset($aEstimateData[$iIndex]);
            }
        }

        $aGroupItem = $this->_buildGroupItem($aSubProduct);

        array_unshift($aEstimateData, $aGroupItem);
        return $aEstimateData;
    }


    /**
     * @param array $aSubProduct $aSubProduct
     * @return array
     */
    private function _buildGroupItem($aSubProduct) {
        usort(
            $aSubProduct,
            function ($aFirst, $aSecond) {
                return (int)$aFirst['fee_amount'] - (int)$aSecond['fee_amount'];
            }
        );

        $aSelectionTag = null; //如果默认为[] idl会gen出一个空对象，导致客户端展示异常
        foreach ($aSubProduct as &$aProduct) {
            if (!empty($aProduct['selection_tag']) && empty($aSelectionTag)) {
                $aSelectionTag = $aProduct['selection_tag'];
            }

            unset($aProduct['selection_tag']);
        }

        $aRecommendTag = null;
        foreach ($aSubProduct as &$aProduct) {
            if (!empty($aProduct['recommend_tag']) && empty($aRecommendTag)) {
                $aRecommendTag = $aProduct['recommend_tag'];
            }

            unset($aProduct['recommend_tag']);
        }

        $sThemeType = array_filter(array_column($aSubProduct, 'theme_type'))[0];
        $aThemeData = array_filter(array_column($aSubProduct, 'theme_data'))[0];
        $bHasSelect = empty(
            array_filter(
                $aSubProduct,
                function ($aProduct) {
                    return SortSelection::DACHE_ANYCAR_ITEM_SELECTED == $aProduct['select_type'];
                }
            )
        ) ? 0 : 1;

        return [
            'sub_group_id'       => $this->_buildSubGroupId(),
            'intro_msg'          => $this->_aGroupConf['intro_msg'],
            'sub_title_list'     => $this->_buildSubTitleList($aSubProduct),
            'form_show_type'     => OrderInfo::FORM_SHOW_TYPE_FIRST,
            'recommend_type'     => SortSelection::DACHE_ANYCAR_ITEM_RECOMMEND,
            'sub_product'        => $aSubProduct,
            'selection_tag'      => $aSelectionTag,
            'recommend_tag'      => $aRecommendTag,
            'select_type'        => $bHasSelect,
            'theme_type'         => $sThemeType,
            'theme_data'         => $aThemeData,
            'sub_page_title'     => $this->_aGroupConf['sub_page_title'],
            'sub_page_sub_title' => $this->_aGroupConf['sub_page_sub_title'],
        ];
    }

    /**
     * @param array $aSubProducts 子车型列表
     * @return array|bool|mixed|null
     */
    private function _buildSubTitleList($aSubProducts) {
        $aSubTitleList = [];
        $aPcIdList     = array_column($aSubProducts, 'product_category');
        foreach ($aPcIdList as $iPcId) {
            $aDdsProduct = DecisionLogic::getInstance()->getDecisionProductByCategoryId($iPcId);
            $sAnswerRate = $aDdsProduct['recommend_info']['extra_info']['answer_rate'] ?? '';
            if (!empty($sAnswerRate)) {
                $sContent  = \BizLib\Config::text('config_text', 'answer_rate_sub_title_content', ['rate' => $sAnswerRate]);
                $aSubTitle = \BizLib\Config::text('config_text', 'answer_rate_sub_title_config');
                $aSubTitle['content'] = $sContent;
                $aSubTitleList[]      = $aSubTitle;
                break;
            }
        }

        $aItemConfig = array_column($this->_aGroupConf['sub_title_list'], 'item_config');
        foreach ($aItemConfig as $aConf) {
            array_push($aSubTitleList, $aConf);
        }

        return $aSubTitleList;
    }

    /**
     * @return int
     */
    private function _buildSubGroupId() {
        $iAccessKeyId = MainDataRepo::getAccessKeyId();
        if (Common::DIDI_IOS_PASSENGER_APP == $iAccessKeyId) {
            return Handler::SUB_GROUP_ID_UNITAXI;
        } else {
            return Handler::SUB_GROUP_ID_SHORT_DISTANCE;
        }
    }
}
