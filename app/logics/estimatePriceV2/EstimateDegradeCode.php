<?php
namespace PreSale\Logics\estimatePriceV2;

/**
 * Class EstimateDegradeCode
 * @package PreSale\Logics\estimatePriceV2
 */
class EstimateDegradeCode
{
    /**
     * @desc 访问local server失败
     */
    const R_ESTIMATE_DEGRADE_LOC_FAIL = 596001;

    /**
     * @desc 访问dds products失败
     */
    const R_ESTIMATE_DEGRADE_PRODUCTS_FAIL = 596002;

    /**
     * @desc 访问athena失败
     */
    const R_ESTIMATE_DEGRADE_ATHENA_FAIL = 596003;

    /**
     * @desc 所有品类价格数据为空
     */
    const R_ESTIMATE_DEGRADE_PRICE_EMPTY = 596004;

    /**
     * @desc 滴滴自有品类价格为空
     */
    const R_ESTIMATE_DEGRADE_DIDI_EMPTY = 596005;

    /**
     * function isDegradeCode
     * @param  int $iCode 错误码
     * @return bool
     */
    public static function isDegradeCode($iCode) {
        return in_array(
            $iCode,
            [
                self::R_ESTIMATE_DEGRADE_LOC_FAIL,
                self::R_ESTIMATE_DEGRADE_PRODUCTS_FAIL,
                self::R_ESTIMATE_DEGRADE_ATHENA_FAIL,
                self::R_ESTIMATE_DEGRADE_PRICE_EMPTY,
                self::R_ESTIMATE_DEGRADE_DIDI_EMPTY,
            ]
        );
    }
}
