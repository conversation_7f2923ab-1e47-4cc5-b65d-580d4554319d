<?php
namespace PreSale\Logics\estimatePriceV2;

use BizCom<PERSON>\Logics\Order\OrderComLogic;
use BizCommon\Models\Passenger\Passenger;
use BizLib\Client\MemberSystemClient;
use BizLib\Client\PriceApiClient;
use BizLib\Config as NuwaConfig;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\RespCode;
use BizLib\Exception\ExceptionWithResp;
use BizLib\ErrCode\Msg;
use BizLib\Constants;
use BizLib\Utils\ProductCategory;
use BizLib\Log;
use BizCommon\Models\Order\LineUpOrderComModel;
use BizLib\Utils\Language;
use BizLib\Utils\NumberHelper;
use BizLib\Utils\Product;
use Disf\SPL\Trace;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\estimatePriceV2\multiResponse\MainDataRepo;
use PreSale\Logics\estimatePriceV2\multiResponse\Util;
use BizLib\Utils;

/**
 * 动调拦截详情
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2021/1/15
 */
class DynamicPriceDetailLogic
{
    private $_aParams        = [];
    private $_aPassengerInfo = [];
    private $_aMemberInfo    = [];
    private $_aQueueLen      = [];
    private $_aDynamicPriceConfig = [];

    const ERRNO_SUCCESS            = 0;
    const DYNAMIC_TIPS_TYPE_AMOUNT = 0; //金额
    const DYNAMIC_TIPS_TYPE_TIMES  = 1; //倍数
    const DYNAMIC_TIPS_TYPE_HIT_CAPPING = 2; //封顶

    const IS_SINGLE  = 1; //单动调
    const NOT_SINGLE = 2; //多动调

    const NOT_QUEUE = 0;  //非排队场景
    const IS_QUEUE  = 1;  //排队场景

    const NOT_PARTCAR = 1; //全部车型动调
    const IS_PARTCAR  = 2; //部分车型动调

    const CATEGORY_ECONOMIC    = 1;   //经济型
    const CATEGORY_COMFORTABLE = 2;   //舒适性
    const CATEGORY_LUXURY      = 3;   //豪华型

    const MEMBER_NOT_XYX = 1; //非信易行会员
    const MEMBER_IS_XYX  = 2;  //信易行会员

    const NOT_VIP_SHOW_CARD = 0;  //不展示溢价保护
    const IS_VIP_SHOW_CARD  = 1;   //展示溢价保护

    const DPA_NOT_SELECTED = 0;  //默认不选中
    const DPA_IS_SELECTED  = 1;   //默认选中

    /**
     * Dynamic constructor.
     * @param array $aParams 入参
     */
    public function __construct($aParams) {
        $aPassengerInfo        = self::_getPassengerDetail($aParams['token']);
        $this->_aParams        = $aParams;
        $this->_aPassengerInfo = $aPassengerInfo;

    }

    // 注释-待删
//    /**
//     * @desc 动调详情
//     *
//     * @return array
//     * @throws ExceptionWithResp ExceptionWithResp.
//     */
//    public function getDynamicPriceDetail() {
//        $aProductList = [];
//        $aOrderInfos  = [];
//        //请求报价单数据
//        $multiQuotation = self::_getMultiQuotation($this->_aParams['estimate_ids']);
//        if (empty($multiQuotation)) {
//            return [];
//        }
//
//        foreach ($multiQuotation as $sEstimateId => $aQuotation) {
//            $quotationTuple = json_decode($aQuotation['n_tuple'], true);
//            $iCityId        = $aQuotation['area'];
//            //只有A+请求排队系统
//            $oToggle = Apollo::getInstance()->featureToggle('dynamic_price_queue_len', ['product_category' => $aQuotation['product_category']]);
//            if ($oToggle->allow()) {
//                $aOrderInfos[] = [
//                    'product_id'    => $quotationTuple['product_id'],
//                    'require_level' => $quotationTuple['require_level'],
//                    'combo_type'    => $quotationTuple['combo_type'],
//                ];
//            }
//
//            //专车和豪车请求会员系统溢价保护逻辑
//            if ((Product::PRODUCT_ID_DEFAULT == $quotationTuple['product_id'] || Product::PRODUCT_ID_FIRST_CLASS_CAR == $quotationTuple['product_id'])
//                && $aQuotation['is_hit_member_capping']
//                && $aQuotation['member_dynamic_capping'] >= 0
//            ) {
//                $aProductList[] = [
//                    'business_id'   => $quotationTuple['business_id'],
//                    'product_id'    => $quotationTuple['product_id'],
//                    'combo_type'    => $quotationTuple['combo_type'],
//                    'carpool_type'  => $quotationTuple['carpool_type'],
//                    'require_level' => $quotationTuple['require_level'],
//                ];
//            }
//        }
//
//        $this->_aDynamicPriceConfig = NuwaConfig::text('config_text', 'dynamic_price_detail', [], $this->_aParams['lang']);
//
//        //排队系统
//        if (!empty($aOrderInfos)) {
//            $this->_aQueueLen = self::_getQueueLen($iCityId, $aOrderInfos);
//        }
//
//        //会员系统
//        if (!empty($aProductList)) {
//            $this->_aMemberInfo = self::_getMemberInfo($this->_aPassengerInfo['uid'], $this->_aParams['lang'], $aProductList, $iCityId);
//        }
//
//        $response = self::_getDynamicResponse($multiQuotation, $iCityId);
//
//        if (empty($response)) {
//            throw new ExceptionWithResp(
//                Code::E_COMMON_HTTP_READ_FAIL,
//                RespCode::R_COMMON_SERVER_ERROR,
//                '',
//                ['params' => json_encode($this->_aParams)]
//            );
//        }
//
//        return $response;
//    }

    // 注释-待删
//    /**
//     * 构建动调返回信息
//     * @param array $multiQuotation 报价单数据
//     * @param int   $iCityId        $iCityId
//     * @return array 批量报价单数据
//     */
//    private function _getDynamicResponse($multiQuotation, $iCityId) {
//        $dynamicList = [];
//        foreach ($multiQuotation as $sEstimateId => $aQuotation) {
//            $quotationTuple = json_decode($aQuotation['n_tuple'], true);
//            $bIsCarpool     = Util::isCarpool(['order_info' => $quotationTuple]);
//            $dynamicInfo    = $this->_getDynamicInfo($aQuotation, $quotationTuple, $bIsCarpool);
//            if (!empty($dynamicInfo)) {
//                $dynamicInfo['estimate_id'] = $sEstimateId;
//                $dynamicList[] = $dynamicInfo;
//            }
//        }
//
//        if (empty($dynamicList)) {
//            return [];
//        }
//
//        $pageTitle = $contentTitle = $contentProductName = $acceptButtonTag = '';
//        if (self::IS_PARTCAR == $this->_aParams['is_partcar']) {
//            $bottomContent      = $this->_aDynamicPriceConfig['bottom_content'];
//            $isPartcar          = self::IS_PARTCAR;
//            $contentProductName = $this->_aDynamicPriceConfig['is_partcar_name'];
//        } else {
//            $bottomContent      = '';
//            $isPartcar          = self::NOT_PARTCAR;
//            $contentProductName = $this->_aDynamicPriceConfig['not_partcar_name'];
//        }
//
//        //单动调数据
//        if (1 == count($dynamicList)) {
//            $isSingle     = self::IS_SINGLE;
//            $pageTitle    = Language::replaceTag(
//                $this->_aDynamicPriceConfig['page_title'],
//                [
//                    'product_name' => $dynamicList[0]['product_name'],
//                ]
//            );
//            $contentTitle = Language::replaceTag(
//                $this->_aDynamicPriceConfig['content_title_1'],
//                [
//                    'product_name' => $dynamicList[0]['product_name'],
//                ]
//            );
//
//            $oToggle = Apollo::getInstance()->featureToggle('dynamic_price_queue_len', ['product_category' => $dynamicList[0]['product_category']]);
//            if ($oToggle->allow()) {
//                if (!empty($this->_aQueueLen) && $this->_aQueueLen['queue_len'] > 0) {
//                    $acceptButtonTag = $this->_aDynamicPriceConfig['button_tag_queue'];
//                } else {
//                    $acceptButtonTag = $this->_aDynamicPriceConfig['button_tag_no_queue'];
//                }
//            } else {
//                if (Product::PRODUCT_ID_DEFAULT == $dynamicList[0]['product_id']
//                    || Product::PRODUCT_ID_FIRST_CLASS_CAR == $dynamicList[0]['product_id']
//                    || ProductCategory::PRODUCT_CATEGORY_YOUXIANG == $dynamicList[0]['product_category']
//                ) {
//                    $acceptButtonTag = $this->_aDynamicPriceConfig['button_tag_other'];
//                }
//            }
//
//            //激励文案城市纬度做实验，默认展示文案，实验城市分组展示
//            $oFeatureToggle = Apollo::getInstance()->featureToggle(
//                'dynamic_price_button_inspire',
//                [
//                    'key'   => $this->_aPassengerInfo['uid'],
//                    'phone' => $this->_aPassengerInfo['phone'],
//                    'city'  => $iCityId,
//                ]
//            );
//            if ($oFeatureToggle->allow()) {
//                    $acceptButtonTag = '';
//            }
//        } else {
//            $isSingle     = self::NOT_SINGLE;
//            $contentTitle = Language::replaceTag(
//                $this->_aDynamicPriceConfig['content_title_2'],
//                ['content_product_name' => $contentProductName,]
//            );
//        }
//
//        //动调过期时间
//        $isFastCar  = Product::isFastcar($quotationTuple['product_id']);
//        $iCacheMin  = self::_getAddFeeCacheMinutes($isFastCar);
//        $sDeadLine  = date('H:i:s', strtotime("+ {$iCacheMin} minutes"));
//        $expireText = Language::replaceTag($this->_aDynamicPriceConfig['expire_text'], ['deadline' => $sDeadLine]);
//
//        //快车排队
//        if (!empty($this->_aQueueLen) && $this->_aQueueLen['queue_len'] > 0) {
//            $iIsQueue = self::IS_QUEUE;
//        } else {
//            $iIsQueue = self::NOT_QUEUE;
//        }
//
//        $aResponse = [
//            'trace_id'              => Trace::traceId(),
//            'is_single'             => $isSingle,
//            'is_partcar'            => $isPartcar,
//            'is_queue'              => $iIsQueue,
//            'page_title'            => $pageTitle,
//            'desc_image'            => $this->_aDynamicPriceConfig['desc_image'],
//            'content_title'         => $contentTitle,
//            'sub_title'             => $this->_aDynamicPriceConfig['sub_title'],
//            'sub_title_icon'        => $this->_aDynamicPriceConfig['sub_title_icon'],
//            'expire_text'           => $expireText,
//            'accept_button_text'    => $this->_aDynamicPriceConfig['accept_button_text'],
//            'deny_button_text'      => $this->_aDynamicPriceConfig['deny_button_text'],
//            'bottom_content'        => $bottomContent,
//            'accept_button_tag'     => $acceptButtonTag,
//            'dynamic_price_explain' => [
//                'title'        => $this->_aDynamicPriceConfig['dynamic_price_explain']['title'],
//                'content'      => $this->_aDynamicPriceConfig['dynamic_price_explain']['content'],
//                'button'       => $this->_aDynamicPriceConfig['dynamic_price_explain']['button'],
//                'explain_icon' => $this->_aDynamicPriceConfig['dynamic_price_explain']['explain_icon'],
//            ],
//            'list'                  => self::_sortDynamicList($dynamicList),
//        ];
//
//        return $aResponse;
//    }

    // 注释-待删
//    /**
//     * 动调列表排序（产品规定排序规则不会改变，所以在代码中写死排序顺序了，顺序：1：经济型>2：舒适性>3：豪华型（滴滴特快>优享>其他；专车>六座>其他；豪车））
//     * @param array $dynamicList 动调列表
//     * @return array 动调列表
//     */
//    private static function _sortDynamicList($dynamicList) {
//        $response = [];
//        if (count($dynamicList) > 1) {
//            //前两位有特定品类，别的品类从2开始填充。
//            $num = 2;
//            foreach ($dynamicList as $dynamic) {
//                switch ($dynamic['category_id']) {
//                    case self::CATEGORY_ECONOMIC:
//                        if (ProductCategory::PRODUCT_CATEGORY_APLUS == $dynamic['product_category']) {
//                            $response[self::CATEGORY_ECONOMIC][0] = $dynamic;
//                        } elseif (ProductCategory::PRODUCT_CATEGORY_YOUXIANG == $dynamic['product_category']) {
//                            $response[self::CATEGORY_ECONOMIC][1] = $dynamic;
//                        } else {
//                            $response[self::CATEGORY_ECONOMIC][$num] = $dynamic;
//                        }
//                        break;
//                    case self::CATEGORY_COMFORTABLE:
//                        if (ProductCategory::PRODUCT_CATEGORY_PREMIUM_COMFORT == $dynamic['product_category']) {
//                            $response[self::CATEGORY_COMFORTABLE][0] = $dynamic;
//                        } elseif (ProductCategory::PRODUCT_CATEGORY_PREMIUM_BUSINESS == $dynamic['product_category']) {
//                            $response[self::CATEGORY_COMFORTABLE][1] = $dynamic;
//                        } else {
//                            $response[self::CATEGORY_COMFORTABLE][$num] = $dynamic;
//                        }
//                        break;
//                    case self::CATEGORY_LUXURY:
//                        if (ProductCategory::PRODUCT_CATEGORY_LUXURY_ANY == $dynamic['product_category']) {
//                            $response[self::CATEGORY_LUXURY][0] = $dynamic;
//                        } else {
//                            $response[self::CATEGORY_LUXURY][$num] = $dynamic;
//                        }
//                        break;
//                    default:
//                        $response[$dynamic['category_id']][$num] = $dynamic;
//                        break;
//                }
//
//                $num++;
//            }
//        } else {
//            $response[] = $dynamicList;
//        }
//
//        ksort($response);
//        $response = array_values($response);
//        foreach ($response as $key => &$value) {
//            ksort($value);
//            $value = array_values($value);
//        }
//
//        return $response;
//    }

    // 注释-待删
//    /**
//     * 单条动调数据
//     * @param array $aQuotation     报价单数据
//     * @param array $quotationTuple n元组
//     * @param bool  $iCarpool       是否拼车
//     * @return array 批量报价单数据
//     */
//    private function _getDynamicInfo($aQuotation, $quotationTuple, $iCarpool) {
//
//        if (0 == $aQuotation['dynamic_diff_price']) {
//            //仅专车
//            if (Product::PRODUCT_ID_DEFAULT != (int)$quotationTuple['product_id']) {
//                return [];
//            }
//
//            //不存在动调
//            if ($aQuotation['dynamic_price_without_member_capping'] <= 0) {
//                return [];
//            }
//
//            //如果没有触发会员溢价保护
//            if (!$aQuotation['is_hit_member_capping']) {
//                return [];
//            }
//        }
//
//        //获取基本信息
//        $iComboType   = $iCarpool ? Constants\Horae::TYPE_COMBO_CARPOOL : $quotationTuple['combo_type'];
//        $sProductName = OrderComLogic::getProductDisplayNameByCarLevel($quotationTuple['require_level'], $iComboType);
//        //获取乘客端6.0相关配置
//        $iCategoryId     = self::CATEGORY_ECONOMIC;
//        $aMainDataConfig = MainDataRepo::getBasicConfByProductCategory($aQuotation['product_category']);
//        if (!empty($aMainDataConfig)) {
//            $sProductName = $aMainDataConfig['intro_msg'];
//            $iCategoryId  = $aMainDataConfig['category_id'];
//        }
//
//        list($sCurrencySymbol, $sCurrencyUnit) = Utils\Currency::getSymbolUnit(
//            $aQuotation['currency'],
//            [
//                'product_id' => $quotationTuple['product_id'],
//                'district'   => $aQuotation['district'],
//            ]
//        );
//
//        //获取动调系数、动调类型、系数单位
//        $aUnit = Language::getUnit();
//        if (isset($aQuotation['is_hit_dynamic_capping']) && !empty($aQuotation['is_hit_dynamic_capping'])) {
//            $iTipsType = self::DYNAMIC_TIPS_TYPE_HIT_CAPPING;
//            $fValue    = $aQuotation['dynamic_diff_price'];
//            $sUnit     = $aUnit['currency'];
//        } elseif (isset($aQuotation['if_use_time'])
//            && 1 == $aQuotation['if_use_time']
//        ) {
//            $iTipsType = self::DYNAMIC_TIPS_TYPE_TIMES;
//            $fValue    = $aQuotation['dynamic_times'];
//            $sUnit     = $this->_aDynamicPriceConfig['dynamic_times_unit'];
//        } else {
//            $iTipsType = self::DYNAMIC_TIPS_TYPE_AMOUNT;
//            $fValue    = $aQuotation['dynamic_diff_price'];
//            $sUnit     = $sCurrencyUnit;
//        }
//
//        $aPlusFeeNum  = NumberHelper::numberFormatDisplay($fValue);
//        $aEstimateFee = NumberHelper::numberFormatDisplay($aQuotation['estimate_fee'], '', 2);
//        $aPlusFeeText = Language::replaceTag(
//            $this->_aDynamicPriceConfig['plus_fee_text'],
//            [
//                'currency_symbol' => $sCurrencySymbol,
//                'fee'             => $aPlusFeeNum,
//                'currency_unit'   => $sUnit,
//            ]
//        );
//        $totalFeeText = Language::replaceTag(
//            $this->_aDynamicPriceConfig['total_fee_text'],
//            [
//                'currency_symbol' => $sCurrencySymbol,
//                'fee'             => $aEstimateFee,
//                'currency_unit'   => $aUnit['currency'],
//            ]
//        );
//
//        //会员溢价保护
//        $mDynamicReduce = $this->_getMemberDynamicReduce($aQuotation, $quotationTuple['product_id'], $sCurrencySymbol, $aUnit);
//
//        $dynamicInfo = [
//            'product_id'           => $quotationTuple['product_id'],
//            'combo_type'           => $quotationTuple['combo_type'],
//            'require_level'        => $quotationTuple['require_level'],
//            'product_name'         => $sProductName,
//            'product_category'     => $aQuotation['product_category'],
//            'plutus_fee_type'      => $iTipsType,
//            'plutus_fee_type_name' => $this->_aDynamicPriceConfig['plutus_fee_type'][$iTipsType] ?? '',
//            'plus_fee_num'         => $aPlusFeeNum,
//            'plus_fee_text'        => $aPlusFeeText,
//            'total_fee_text'       => $totalFeeText,
//            'payments_type'        => $quotationTuple['pay_type'],
//            'total_fee'            => $aEstimateFee,
//            'category_id'          => $iCategoryId,
//        ];
//        $dynamicInfo = array_merge($dynamicInfo, $mDynamicReduce);
//
//        return $dynamicInfo;
//
//    }

    // 注释-待删
//    /**
//     * 会员溢价保护
//     * @param array  $aQuotation      报价单
//     * @param int    $iProductId      产品id
//     * @param string $sCurrencySymbol 货币符号
//     * @param array  $aUnit           单位
//     * @return array 批量报价单数据
//     */
//    private function _getMemberDynamicReduce($aQuotation, $iProductId, $sCurrencySymbol, $aUnit) {
//        //专车和豪车溢价保护逻辑
//        if ($this->_aMemberInfo) {
//            $aMember = $this->_aMemberInfo[$iProductId] ?? [];
//        } else {
//            $aMember = [];
//        }
//
//        $iVipLevel           = $aMember['level_id'] ?? 0;
//        $sVipName            = $aMember['level_name'] ?? '';
//        $sVipIcon            = $aMember['level_icon'] ?? '';
//        $sPrivilegesName     = $this->_aDynamicPriceConfig['privileges_name'];
//        $dynamicMemberReduce = 0;
//
//        if ((Utils\Product::PRODUCT_ID_DEFAULT == $iProductId || Utils\Product::PRODUCT_ID_FIRST_CLASS_CAR == $iProductId)
//            && $aQuotation['is_hit_member_capping']
//            && $aQuotation['member_dynamic_capping'] >= 0
//        ) {
//            $dynamicMemberReduce    = $aQuotation['dynamic_price_without_member_capping'] - $aQuotation['member_dynamic_capping'];
//            $dynamicMemberReduce    = NumberHelper::numberFormatDisplay($dynamicMemberReduce, '', 2);
//            $iVipShowCard           = self::IS_VIP_SHOW_CARD;
//            $iDpaSelected           = self::DPA_IS_SELECTED;
//            $aMemberBackgroundColor = NuwaConfig::text('config_text', 'dynamic_member_background');
//            $sVipProtectFeeText     = Language::replaceTag(
//                $this->_aDynamicPriceConfig['vip_project_fee_text'],
//                [
//                    'currency_symbol' => $sCurrencySymbol,
//                    'fee'             => $dynamicMemberReduce,
//                    'currency_unit'   => $aUnit['currency'],
//                ]
//            );
//
//            if (Product::PRODUCT_ID_FIRST_CLASS_CAR == $iProductId) {
//                $sVipProtectText = Language::replaceTag(
//                    $this->_aDynamicPriceConfig['vip_protect_dynamic_msg_lux'],
//                    [
//                        'level_name'      => $sVipName,
//                        'priv_name'       => $sPrivilegesName,
//                        'currency_symbol' => $sCurrencySymbol,
//                        'amount'          => $dynamicMemberReduce,
//                        'currency_unit'   => $aUnit['currency'],
//                    ]
//                );
//            } else {
//                $sVipProtectText = Language::replaceTag(
//                    $this->_aDynamicPriceConfig['vip_project_text'],
//                    [
//                        'level_name'      => $sVipName,
//                        'privileges_name' => $sPrivilegesName,
//                        'currency_symbol' => $sCurrencySymbol,
//                        'fee'             => $dynamicMemberReduce,
//                        'currency_unit'   => $aUnit['currency'],
//                    ]
//                );
//            }
//
//            //更新信宜行用户标识
//            if (self::MEMBER_IS_XYX == $aMember['identity_type']) {
//                $iIsXyx = 1;
//                $sVipBackgroundColor = $aMemberBackgroundColor['xyx_background_color'];
//                $sVipFontColor       = $aMemberBackgroundColor['xyx_font_color'];
//                $radioCheckImg       = $aMemberBackgroundColor['xyx_radio_check_img']['radio_check_img'];
//                $radioUnCheckImg     = $aMemberBackgroundColor['xyx_radio_check_img']['radio_uncheck_img'];
//            } else {
//                $iIsXyx = 0;
//                $sVipBackgroundColor = $aMemberBackgroundColor['vip_background_color'][$iVipLevel] ?? '';
//                $sVipFontColor       = $aMemberBackgroundColor['vip_font_color'][$iVipLevel] ?? $aMemberBackgroundColor['vip_font_color']['default'];
//                $radioCheckImg       = $aMemberBackgroundColor['radio_check_img'][$iVipLevel]['radio_check_img'] ?? $aMemberBackgroundColor['radio_check_img']['default']['radio_check_img'];
//                $radioUnCheckImg     = $aMemberBackgroundColor['radio_check_img'][$iVipLevel]['radio_uncheck_img'] ?? $aMemberBackgroundColor['radio_check_img']['default']['radio_uncheck_img'];
//            }
//
//            $sVipRemainTimes = $aMember['dpa']['remain_times'] ?? 0;
//            if ($sVipRemainTimes > 0) {
//                $sVipRemainText = Language::replaceTag(
//                    $this->_aDynamicPriceConfig['vip_remain_times_text'],
//                    ['remain_times' => $sVipRemainTimes,]
//                );
//            }
//        } else {
//            $sVipRemainTimes = $iIsXyx          = $iVipShowCard  = $iDpaSelected = 0;
//            $sVipProtectText = $sVipProtectFeeText = $sVipRemainText = $sVipBackgroundColor = $sVipFontColor = '';
//            $radioCheckImg   = $radioUnCheckImg = '';
//        }
//
//        $aOrignTotalFee = NumberHelper::numberFormatDisplay($aQuotation['estimate_fee'] + $dynamicMemberReduce, '', 2);
//
//        $orignTotalFeeText = Language::replaceTag(
//            $this->_aDynamicPriceConfig['orign_total_fee_text'],
//            [
//                'currency_symbol' => $sCurrencySymbol,
//                'fee'             => $aOrignTotalFee,
//                'currency_unit'   => $aUnit['currency'],
//            ]
//        );
//
//        $list = [
//            'is_xyx'               => $iIsXyx,
//            'vip_name'             => $sVipName,
//            'vip_protect_text'     => $sVipProtectText,
//            'vip_protect_fee_text' => $sVipProtectFeeText,
//            'vip_protect_fee'      => $dynamicMemberReduce,
//            'vip_priv_name'        => $sPrivilegesName,
//            'vip_remain_times'     => $sVipRemainTimes,
//            'vip_remain_text'      => $sVipRemainText,
//            'vip_icon'             => $sVipIcon,
//            'vip_show_card'        => $iVipShowCard,
//            'vip_background_color' => $sVipBackgroundColor,
//            'vip_font_color'       => $sVipFontColor,
//            'radio_check_img'      => $radioCheckImg,
//            'radio_uncheck_img'    => $radioUnCheckImg,
//            'dpa_select'           => $iDpaSelected,
//            'orign_total_fee'      => $aOrignTotalFee,
//            'orign_total_fee_text' => $orignTotalFeeText,
//        ];
//
//        return $list;
//    }

    // 注释-待删
//    /**
//     * 批量获取报价单信息
//     * @param array $aMultiEstimateIds 预估id数组
//     * @return array 批量报价单数据
//     */
//    private static function _getMultiQuotation($aMultiEstimateIds) {
//        $aReq = [
//            'estimate_id_list' => $aMultiEstimateIds,
//            'fields'           => [
//                'dynamic_price_without_member_capping',
//                'member_dynamic_capping',
//                'dynamic_diff_price',
//                'estimate_fee',
//                'product_category',
//                'currency',
//                'district',
//                'is_hit_dynamic_capping',
//                'is_hit_member_capping',
//                'if_use_time',
//                'dynamic_times',
//                'area',
//                'n_tuple',
//            ],
//        ];
//
//        $aQuotationResult = (new PriceApiClient())->getQuotationBatch($aReq);
//        if (!isset($aQuotationResult['errno']) || self::ERRNO_SUCCESS != $aQuotationResult['errno'] || empty($aQuotationResult['data'])) {
//            Log::warning(Msg::formatArray(Code::E_COMMON_REQ_FAIL, ['result' => json_encode($aQuotationResult), 'estimate_id_list' => $aMultiEstimateIds]));
//            return [];
//        }
//
//        return $aQuotationResult['data'];
//    }

    /**
     * 获取乘客信息
     * @param string $sToken $sToken
     * @return array
     */
    private static function _getPassengerDetail($sToken) {
        $aPassengerInfo = Passenger::getInstance()->getPassengerByTokenFromPassport($sToken);
        if (!isset($aPassengerInfo['uid']) || !isset($aPassengerInfo['phone'])) {
            Log::warning(Msg::formatArray(Code::E_COMMON_HTTP_PASSPORT_PASSENGER_ERROR, ['result' => json_encode($aPassengerInfo)]));
            return [];
        }

        return $aPassengerInfo;
    }

    // 注释-待删
//    /**
//     * 会员系统
//     * @param int    $uid         用户id
//     * @param string $sLang       语言
//     * @param array  $productList $productList
//     * @param int    $cityID      $cityID
//     * @return array
//     */
//    private static function _getMemberInfo($uid, $sLang, $productList, $cityID) {
//        $aHitInfo         = array(
//            'city_id' => $cityID,
//            'version' => 2,
//        );
//        $oMemberClient    = new MemberSystemClient();
//        $aMultiMemberInfo = $oMemberClient->multiProductsQueryInfoV2($uid, $sLang, $productList, $aHitInfo);
//        $aMemberRes       = [];
//        if (empty($aMultiMemberInfo)) {
//            Log::warning(Msg::formatArray(Code::E_PASSENGER_GET_MEMBER_PROFILE_ERROR, ['result' => json_encode($aMultiMemberInfo)]));
//            return $aMemberRes;
//        }
//
//        if (!empty($aMultiMemberInfo['product_list'])) {
//            foreach ($aMultiMemberInfo['product_list'] as $aProductInfo) {
//                if (!empty($aProductInfo['product_info'])) {
//                    //获取product_id
//                    $iProductId  = Product::getProductIdByBusinessId($aProductInfo['product_info']['business_id']);
//                    $dpa         = $aProductInfo['privileges']['dpa'] ?? [];
//                    $aMemberInfo = [
//                        'level_id'      => $aProductInfo['level_id'],
//                        'pt_version'    => $aProductInfo['pt_version'],
//                        'level_name'    => $aProductInfo['level_name_new'],
//                        'level_icon'    => $aProductInfo['level_icon'],
//                        'identity_type' => $aProductInfo['identity_type'],
//                        'dpa'           => [
//                            'is_auto'      => $dpa['item'][0]['is_auto'] ?? 0,
//                            'priv_name'    => $dpa['item'][0]['priv_name'] ?? '',
//                            'remain_times' => $dpa['item'][0]['backend']['remain_times'] ?? 0,
//                        ],
//                    ];
//                    $aMemberRes[$iProductId] = $aMemberInfo;
//                }
//            }
//        }
//
//        return $aMemberRes;
//    }

    // 注释-待删
//    /**
//     * 排队系统
//     * @param int   $iCityId     城市id
//     * @param array $aOrderInfos $aOrderInfos
//     * @return array
//     */
//    private function _getQueueLen($iCityId, $aOrderInfos) {
//        $result    = [];
//        $aQueueLen = LineUpOrderComModel::getInstance()->getQueueLenByOrderInfos($iCityId, $this->_aParams['lat'], $this->_aParams['lng'], $aOrderInfos);
//        if (!empty($aQueueLen)) {
//            $result = array_shift($aQueueLen);
//        }
//
//        return $result;
//    }

    // 注释-待删
//    /**
//     * 获取缓存时间
//     * @param bool $isFastCar 是否是快车
//     * @return int
//     */
//    private function _getAddFeeCacheMinutes($isFastCar) {
//        if ($isFastCar) {
//            $iCacheTime = $this->_aDynamicPriceConfig['estimate_price_fast_car_time'];
//        } else {
//            $iCacheTime = $this->_aDynamicPriceConfig['estimate_price_default_time'];
//        }
//
//        return (int)($iCacheTime / 60);
//    }
}
