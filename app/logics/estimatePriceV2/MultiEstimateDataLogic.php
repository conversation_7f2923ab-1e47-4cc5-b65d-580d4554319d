<?php

namespace PreSale\logics\estimatePriceV2;

use BizLib\Client\TCPassengerClient;
use BizLib\Client\TCPassengerClientV2;
use BizLib\Exception\BizException;
use BizLib\Log as NuwaLog;
use Dirpc\SDK\PreSale\B2bMultiEstimateDataRequest;
use BizLib\Exception\InvalidArgumentException;
use BizLib\Client\MemberSystemClient;
use BizLib\Client\BillClient;
use BizLib\ErrCode\Msg;
use BizCommon\Models\Passenger\Passenger;
use BizLib\ErrCode\Code;
use BizLib\Log;
use BizLib\ErrCode;
use BizLib\Exception\ExceptionWithResp;
use BizLib\Utils\Product;
use BizLib\Client\PriceApiClient;
use Dirpc\SDK\PreSale\B2bMultiEstimateDataResponse as Response;
use TripcloudCommon\Utils\Product as TripcloudProduct;

/**
 * Class MultiEstimateDataLogic
 */
class MultiEstimateDataLogic
{
    protected static $_oInstance = null;
    private $_oEstimateRequest;
    private $_aPassengerInfo;
    private $_aQuotationBatch;

    // B2bMultiEstimateDataRequest type 字段枚举值
    const B2B_ESTIMATE_DYNAMIC_TYPE          = 1; // 动调
    const B2B_ESTIMATE_SPECIAL_FEE_TYPE      = 2; // 特殊费用
    const B2B_ESTIMATE_FEE_DETAIL_TYPE       = 3; // 费用详情
    const B2B_ESTIMATE_FEE_BOOKING_RULE_TYPE = 4; // 预约计价规则:起步价/基础费
    // const B2B_ESTIMATE_ALL   = 4; // 全部
    const NAME_PRODUCT_CATEGORY = 'product_category';
    const NAME_AREA           = 'area';
    const NAME_PRICE_TOKEN    = 'price_token';
    const NAME_N_TUPLE        = 'n_tuple';
    const NAME_DEPARTURE_TIME = 'departure_time';
    const NAME_DISTRICT       = 'district';
    const NAME_ABSTRACT_DISTRICT = 'abstract_district';
    const NAME_FROM_LNG          = 'from_lng';
    const NAME_FROM_LAT          = 'from_lat';

    const ROLE_PASSENGER = 2;

    const ERRNO_SUCCESS = 0;



    /**
     * 实例化.
     *
     * @return MultiEstimateDataLogic
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * function checkParams
     * @param B2bMultiEstimateDataRequest $oEstimateRequest 企业6.0入参
     * @return void
     */
    public function setEstimateRequest($oEstimateRequest) {
        $this->_oEstimateRequest = $oEstimateRequest;
    }

    /**
     * function checkParams
     * @param B2bMultiEstimateDataRequest $oRequest 企业6.0入参
     * @return void
     * @throws InvalidArgumentException 参数校验失败异常
     */
    public static function checkParams($oRequest) {
        if (empty($oRequest->getEstimateIdList())) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                ['estimate_id_list' => $oRequest->getEstimateIdList()]
            );
        }

        if (empty($oRequest->getType())) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                ['type' => $oRequest->getType()]
            );
        }

    }

    /**
     * function getB2bEstimateDataType 获取需要执行的逻辑对应的类型数组
     * @param B2bMultiEstimateDataRequest $oRequest 企业6.0入参
     * @return array
     */
    public static function getB2bEstimateDataType($oRequest) {
        $aTypes = [];
        switch ($oRequest->getType()) {
            case MultiEstimateDataLogic::B2B_ESTIMATE_DYNAMIC_TYPE:
                $aTypes[] = MultiEstimateDataLogic::B2B_ESTIMATE_DYNAMIC_TYPE;
                break;
            case MultiEstimateDataLogic::B2B_ESTIMATE_SPECIAL_FEE_TYPE:
                $aTypes[] = MultiEstimateDataLogic::B2B_ESTIMATE_SPECIAL_FEE_TYPE;
                break;
            case MultiEstimateDataLogic::B2B_ESTIMATE_FEE_DETAIL_TYPE:
                $aTypes[] = MultiEstimateDataLogic::B2B_ESTIMATE_FEE_DETAIL_TYPE;
                break;
            case MultiEstimateDataLogic::B2B_ESTIMATE_FEE_BOOKING_RULE_TYPE:
                $aTypes[] = MultiEstimateDataLogic::B2B_ESTIMATE_FEE_BOOKING_RULE_TYPE;
                break;
            default:
        }

        return $aTypes;
    }
    /**
     * 通过预估id列表获取动调相关数据
     * @param array    $aEstimatePrices $aEstimatePrices
     * @param Response $oResponse       $oResponse
     * @param array    $aEstimateList   预估id列表（array）
     * @return Response $oResponse $oResponse
     */
    public function buildDynamicDetails($aEstimatePrices, $oResponse, $aEstimateList) {

        // 获取乘客信息
        $this->_buildPassengerDetail($this->_oEstimateRequest->getToken());
        if (empty($this->_aQuotationBatch)) {
            $this->_buildQuotationBatch($aEstimateList);
        }

        $aProductList = [];
        foreach ($oResponse->getData()->getItem() as $oItem) {
            if (empty($oItem)) {
                break;
            }

            if (empty($oItem->getEstimateId())) {
                continue;
            }

            $sEid = $oItem->getEstimateId();
            if (empty($aEstimatePrices[$sEid])) {
                continue;
            }

            $oB2bDetailDataItem = ($oResponse->getData()->getItem())[$sEid];
            $iDynamicPriceWithoutMemberCapping = $aEstimatePrices[$sEid]['dynamic_price_without_member_capping'] ?? 0.0;
            $aB2bDynamicInfo = [
                'member_dynamic_capping'               => $aEstimatePrices[$sEid]['member_dynamic_capping'] ?? -1,
                'dynamic_diff_price'                   => $aEstimatePrices[$sEid]['dynamic_diff_price'] ?? 0.0,
                'dynamic_price_without_member_capping' => $iDynamicPriceWithoutMemberCapping,
                'is_hit_member_capping'                => $aEstimatePrices[$sEid]['is_hit_member_capping'] ?? false,
                'is_hit_dynamic_capping'               => $aEstimatePrices[$sEid]['is_hit_dynamic_capping'] ?? false,
                'dynamic_times'                        => $aEstimatePrices[$sEid]['dynamic_times'] ?? 0.0,
                'dynamic_total_fee'                    => $aEstimatePrices[$sEid]['dynamic_total_fee'] ?? 0.0,
            ];
            if (!empty($aEstimatePrices[$sEid]['dynamic_info'])) {
                $aB2bDynamicInfo['if_use_times'] = $aEstimatePrices[$sEid]['dynamic_info']['if_use_times'];
            }

            $oB2bDetailDataItem->setDynamicInfo((object)$aB2bDynamicInfo);
            if (empty($this->_aQuotationBatch) || (empty($this->_aQuotationBatch[$sEid]))
                || empty($this->_aQuotationBatch[$sEid]['n_tuple'])
            ) {
                continue;
            }

            $aQuotationTuple = json_decode($this->_aQuotationBatch[$sEid]['n_tuple'], true);
            if ((Product::PRODUCT_ID_BUSINESS == $aQuotationTuple['product_id']
                || Product::PRODUCT_ID_ENTERPRISE_LUXURY == $aQuotationTuple['product_id'])
                && $aB2bDynamicInfo['is_hit_member_capping'] && $aB2bDynamicInfo['member_dynamic_capping'] >= 0
            ) {
                    $aProductList[] = [
                        'estimate_id'   => $sEid,
                        'business_id'   => $aQuotationTuple['business_id'],
                        'product_id'    => $aQuotationTuple['product_id'],
                        'combo_type'    => $aQuotationTuple['combo_type'],
                        'carpool_type'  => $aQuotationTuple['carpool_type'],
                        'require_level' => $aQuotationTuple['require_level'],
                    ];
            }
        }

        // 请求会员系统
        if (!empty($aProductList) && !empty($this->_aPassengerInfo)) {
            $aMemberInfo = self::_getMemberInfo(
                $this->_aPassengerInfo['uid'],
                $this->_oEstimateRequest->getLang(),
                $aProductList,
                $this->_aQuotationBatch[$sEid]['area']
            );
        }

        if (!empty($aMemberInfo)) {
            foreach ($aMemberInfo as $sBusnessCarLevelId => $aInfo) {
                foreach ($aProductList as $aProduct) {
                    if ($sBusnessCarLevelId == $aProduct['business_id'].$aProduct['require_level']) {
                        $oB2bDetailDataItem = ($oResponse->getData()->getItem())[$aProduct['estimate_id']];
                        $oB2bDetailDataItem->setMemberInfo((object)$aInfo);
                        continue;
                    }
                }
            }
        }

        return $oResponse;
    }

    /**
     * 通过预估id列表获取特殊费用
     * @param array    $aEstimatePrices $aEstimatePrices
     * @param Response $oResponse       $oResponse
     * @return Response $oResponse $oResponse
     */
    public function buildSpecialFees($aEstimatePrices, $oResponse) {
        foreach ($oResponse->getData()->getItem() as $oItem) {
            if (empty($oItem)) {
                break;
            }

            if (empty($oItem->getEstimateId())) {
                continue;
            }

            $sEid = $oItem->getEstimateId();
            if (empty($aEstimatePrices[$sEid])) {
                continue;
            }

            $aB2bSpecialFee = [
                'limit_fee'        => $aEstimatePrices[$sEid]['limit_fee'],
                'count_price_type' => $aEstimatePrices[$sEid]['count_price_type'],
            ];
            if (!empty($aEstimatePrices[$sEid]['extra'])) {
                $aB2bSpecialFee['highway_fee'] = $aEstimatePrices[$sEid]['extra']['highway_fee_value'];
            }

            $afeeDetailInfo = $aEstimatePrices[$sEid]['fee_detail_info'];
            if (!empty($afeeDetailInfo)) {
                $aB2bSpecialFee['red_packet_fee']        = $afeeDetailInfo['red_packet'];
                $aB2bSpecialFee['cross_city_fee']        = $afeeDetailInfo['cross_city_fee'];
                $aB2bSpecialFee['designated_driver_fee'] = $afeeDetailInfo['designated_driver_fee'];
                $aB2bSpecialFee['start_price']           = $afeeDetailInfo['start_price'];
                $aB2bSpecialFee['energy_consume_fee']    = $afeeDetailInfo['energy_consume_fee'];
                $aB2bSpecialFee['empty_distance']        = $aEstimatePrices[$sEid]['empty_distance'];
                $aB2bSpecialFee['normal_distance']       = $aEstimatePrices[$sEid]['total_distance'];
                $aB2bSpecialFee['normal_time']           = $aEstimatePrices[$sEid]['total_time'];
                $aB2bSpecialFee['total_fee']             = $aEstimatePrices[$sEid]['total_fee'];
                $aB2bSpecialFee['limit_lowest_fee']      = $aEstimatePrices[$sEid]['limit_fee'];
                $aB2bSpecialFee['start_distance']        = $aEstimatePrices[$sEid]['start_distance'];
                $aB2bSpecialFee['start_time']            = $aEstimatePrices[$sEid]['start_time'];
                $aB2bSpecialFee['fee_detail_info']       = json_encode($afeeDetailInfo, true);
            }

            $oB2bDetailDataItem = ($oResponse->getData()->getItem())[$sEid];
            $oB2bDetailDataItem->setSpecialFee((object)$aB2bSpecialFee);
        }

        return $oResponse;
    }
    /**
     * 通过预估id列表拼接请求price-api-批量获取报价单的参数
     * @param array $aEstimateIds 预估ID列表 类型：list<string>
     * @return array $aReq 请求报价单的参数
     */
    private function _getQuotationBatchParam($aEstimateIds) {

        $aReq = [
            'estimate_id_list' => $aEstimateIds,
            'fields'           => array(
                self::NAME_AREA,
                self::NAME_N_TUPLE,
                self::NAME_PRICE_TOKEN,
                self::NAME_DEPARTURE_TIME,
                self::NAME_DISTRICT,
                self::NAME_ABSTRACT_DISTRICT,
                self::NAME_FROM_LNG,
                self::NAME_FROM_LAT,
            ),
        ];
        return $aReq;
    }

    /**
     * @param array $aEstimateIds $aEstimateIds
     * @return void
     */
    private function _buildQuotationBatch($aEstimateIds) {
        // 查询报价单
        $aParams  = $this->_getQuotationBatchParam($aEstimateIds);
        $_oClient = new PriceApiClient(PriceApiClient::MODULE_NAME);
        $aRet     = $_oClient->getQuotationBatch($aParams);
        if (isset($aRet['errno']) && 0 == $aRet['errno']) {
            $this->_aQuotationBatch = $aRet['data']; // data 内部结构为: {"预估id":{...}}
            return;
        }

        $this->_aQuotationBatch = [];
    }
    /**
     * 通过预估id列表获取账单费用详情转成json字符串
     * @param array  $aEstimateIds 预估ID列表
     * @param object $oResponse    $oResponse
     * @return object
     * @throws InvalidArgumentException InvalidArgumentException
     */
    public function buildPlutusFeeDetails($aEstimateIds, $oResponse) {
        if (empty($this->_aQuotationBatch)) {
            $this->_buildQuotationBatch($aEstimateIds);
        }

        $oBillClient = new BillClient();
        $oTcClient   = new TCPassengerClient();
        foreach ($aEstimateIds as $sEid) {
            $aNTuple = json_decode($this->_aQuotationBatch[$sEid]['n_tuple'], true);
            if (TripcloudProduct::isTripcloudByBusinessID((int)$aNTuple['business_id'])) {
                $feeDetailInfo = $this->getTripcloudFeeDetailInfos($sEid, $oTcClient);
            } else {
                $feeDetailInfo = $this->getPlutusFeeDetailInfos($sEid, $oBillClient);
            }

            $oB2bDetailDataItem = ($oResponse->getData()->getItem())[$sEid];
            $oB2bDetailDataItem->setFeeDetail(json_encode($feeDetailInfo['data'], true));
            if (!empty($this->_aQuotationBatch[$sEid])) {
                $oB2bDetailDataItem->setPriceToken($this->_aQuotationBatch[$sEid]['price_token']);
            }
        }

        return $oResponse;
    }

    /**
     * 获取账单费用详情
     * @param string $sEstimateId eid
     * @param object $oBillClient $oBillClient
     * @return mixed
     * @throws InvalidArgumentException InvalidArgumentException
     */
    public function getPlutusFeeDetailInfos($sEstimateId, $oBillClient) {
        $aReq    = ['estimate_id' => $sEstimateId,];
        $feeDetailInfo = $oBillClient->getFeeDetailInfos($aReq);
        if (!isset($feeDetailInfo['errno'])
            || self::ERRNO_SUCCESS != $feeDetailInfo['errno']
            || empty($feeDetailInfo['data'])
        ) {
            NuwaLog::warning(
                Msg::formatArray(
                    Code::E_COMMON_HTTP_PLUTUS_FEE_DETAIL,
                    [
                        'estimate_id' => $sEstimateId,
                        'result'      => json_encode($feeDetailInfo),
                    ]
                )
            );
            throw new InvalidArgumentException(
                ErrCode\Code::E_COMMON_HTTP_PLUTUS_FEE_DETAIL,
                array('params' => $sEstimateId)
            );
        }

        return $feeDetailInfo;
    }

    /**
     * 获取三方账单费用详情
     * @param string $sEstimateId eid
     * @param object $oTcClient $oTcClient
     * @return array|mixed
     * @throws InvalidArgumentException InvalidArgumentException
     */
    public function getTripcloudFeeDetailInfos($sEstimateId, $oTcClient) {
        $aReq          = ['estimate_id' => $sEstimateId,];
        $feeDetailInfo = $oTcClient->pGetEstimateFeeDetail($aReq);
        if (!isset($feeDetailInfo['errno'] ) || self::ERRNO_SUCCESS != $feeDetailInfo['errno'] || empty($feeDetailInfo['data'])) {
            NuwaLog::warning(Msg::formatArray(Code::E_COMMON_HTTP_TRIPCLOUD_FEE_DETAIL, ['estimate_id' => $sEstimateId, 'result' => json_encode($feeDetailInfo)]));

            throw new InvalidArgumentException(
                ErrCode\Code::E_COMMON_HTTP_PLUTUS_FEE_DETAIL,
                array('params' => $sEstimateId)
            );
        }

        return $feeDetailInfo;
    }

    /**
     * 请求账单获取计价
     * @param string $sStrategyToken    $sStrategyToken
     * @param string $sDistrict         $sDistrict
     * @param string $sAbstractDistrict $sAbstractDistrict
     * @param string $sLang             $sLang
     * @param array  $aOrderInfo        $aOrderInfo
     * @return array
     */
    public function fetchPriceData($sStrategyToken, $sDistrict, $sAbstractDistrict, $sLang, $aOrderInfo) {
        $aDriverParam = new \stdClass();
        $aOrderExtra  = new \stdClass();
        $aBillResult  = (new BillClient())->getStrategyByTokenOrProduct(self::ROLE_PASSENGER, $sLang, $sDistrict, $sAbstractDistrict, 0, $sStrategyToken,$aDriverParam,$aOrderInfo, $aOrderExtra);
        if (isset($aBillResult['errno']) && self::ERRNO_SUCCESS == $aBillResult['errno'] && !empty($aBillResult['result']['data']['multi_response'][0])) {
            return $aBillResult['result']['data']['multi_response'][0];
        } else {
            Log::warning(Msg::formatArray(Code::E_COMMON_REQ_FAIL, ['result' => json_encode($aBillResult)]));
            return [];
        }
    }

    /**
     * 通过预估id列表获取预约单计价规则
     * @param Response $oResponse     $oResponse
     * @param array    $aEstimateList 预估id列表（array）
     * @return Response $oResponse $oResponse
     * @deprecated 无用
     */
    public function buildBookingRules($oResponse, $aEstimateList) {

        if (empty($this->_aQuotationBatch)) {
            $this->_buildQuotationBatch($aEstimateList);
        }

        foreach ($oResponse->getData()->getItem() as $oItem) {
            if (empty($oItem) || empty($oItem->getEstimateId())) {
                break;
            }

            $sEid = $oItem->getEstimateId();
            $sAbstractDistrict = $this->_aQuotationBatch[$sEid][self::NAME_ABSTRACT_DISTRICT] ?? '';
            $aOrderInfo        = [
                'departure_time' => date('Y-m-d H:i:s',$this->_aQuotationBatch[$sEid][self::NAME_DEPARTURE_TIME]),
                'starting_lng'   => (string)($this->_aQuotationBatch[$sEid][self::NAME_FROM_LNG]),
                'starting_lat'   => (string)($this->_aQuotationBatch[$sEid][self::NAME_FROM_LAT]),
            ];
            $sPriceToken       = $this->_aQuotationBatch[$sEid][self::NAME_PRICE_TOKEN];
            $sDistrict         = $this->_aQuotationBatch[$sEid][self::NAME_DISTRICT];
            $sLang = $this->_oEstimateRequest->getLang();

            $aPriceData = self::fetchPriceData(
                $sPriceToken,
                $sDistrict,
                $sAbstractDistrict,
                $sLang,
                $aOrderInfo
            );
            if (empty($aPriceData)) {
                break;
            }

            $aStrategyRule = [];
            foreach ($aPriceData['strategies'] as $data) {
                foreach ($data as $key => $val) {
                    foreach ($val['strategy'] as $item) {
                        foreach ($item as $k => $v) {
                            if ('items_title_book' == $k) {
                                $aStrategyRule[$key] = $v['items'];
                            }
                        }
                    }
                }
            }

            if (empty($aStrategyRule)) {
                break;
            }

            if (!empty($aPriceData['curr_day_type'])) {
                $aStrategyRule['type'] = $aPriceData['curr_day_type'];
            }

            $sStrategyRule = json_encode($aStrategyRule);
            if ($sStrategyRule) {
                $oB2bDetailDataItem = ($oResponse->getData()->getItem())[$sEid];
                $oB2bDetailDataItem->setBookingRules($sStrategyRule);
            }
        }

        return $oResponse;
    }

    /**
     * 通过预估id列表获取账单预估价格
     * @param array $aEstimateIds 预估ID列表
     * @return array $aEstimatePrices
     * @throws InvalidArgumentException InvalidArgumentException
     */
    public function getEstimatePrices($aEstimateIds) {
        if (empty($this->_aQuotationBatch)) {
            $this->_buildQuotationBatch($aEstimateIds);
        }

        $aTcEstimateIds = [];
        $aPlutusEstimateIds = [];
        foreach ($aEstimateIds as $sEid) {
            if (empty($sEid)) {
                continue;
            }

            $aNTuple = json_decode($this->_aQuotationBatch[$sEid]['n_tuple'], true);
            if (TripcloudProduct::isTripcloudByBusinessID((int)$aNTuple['business_id'])) {
                $aTcEstimateIds[] = $sEid;
            } else {
                $aPlutusEstimateIds[] = $sEid;
            }
        }

        $aEstimatePrices = [];
        if (!empty($aTcEstimateIds)) {
            $aTcReq = [
                'estimate_ids' => $aTcEstimateIds
            ];

            $oTcClient = new TCPassengerClientV2();
            $aTCEstimateRet = $oTcClient->getEstimateBillByEstimateIds($aTcReq);
            if (!isset($aTCEstimateRet['errno'])
                || self::ERRNO_SUCCESS != $aTCEstimateRet['errno']
                || empty($aTCEstimateRet['data'])
                || !is_array($aTCEstimateRet['data'])
            ) {
                // 增加一个错误码 todo sxm
                NuwaLog::warning(
                    Msg::formatArray(
                        Code::E_COMMON_HTTP_PLUTUS_FEE_DETAIL,
                        [
                            'estimate_ids' => json_encode($aTcEstimateIds),
                            'result'       => json_encode($aTCEstimateRet),
                        ]
                    )
                );
            } else {
                $aEstimatePrices = array_merge($aEstimatePrices, $aTCEstimateRet['data']);
            }
        }

        if (!empty($aPlutusEstimateIds)) {
            $oBillClient     = new BillClient();
            $estimatePrice   = $oBillClient->getEstimatePriceByEstimateIds($aPlutusEstimateIds);
            if (!isset($estimatePrice['errno'])
                || self::ERRNO_SUCCESS != $estimatePrice['errno']
                || empty($estimatePrice['data'])
                || !is_array($estimatePrice['data'])
            ) {
                // 增加一个错误码 todo sxm
                NuwaLog::warning(
                    Msg::formatArray(
                        Code::E_COMMON_HTTP_PLUTUS_FEE_DETAIL,
                        [
                            'estimate_ids' => json_encode($aPlutusEstimateIds),
                            'result'       => json_encode($estimatePrice),
                        ]
                    )
                );
            } else {
                $aEstimatePrices = array_merge($aEstimatePrices, $estimatePrice['data']);
            }
        }

        if (empty($aEstimatePrices)) {
            throw new InvalidArgumentException(
                ErrCode\Code::E_PRICE_FEE_DETAIL_FAIL,
                array('params' => $aEstimateIds)
            );
        }

        return $aEstimatePrices;
    }

    /**
     * 获取乘客信息
     * @param string $sToken $sToken
     * @return void
     */
    private function _buildPassengerDetail($sToken) {
        $aPassengerInfo = Passenger::getInstance()->getPassengerByTokenFromPassport($sToken);
        if (!isset($aPassengerInfo['uid']) || !isset($aPassengerInfo['phone'])) {
            NuwaLog::warning(
                Msg::formatArray(
                    Code::E_COMMON_HTTP_PASSPORT_PASSENGER_ERROR,
                    ['result' => json_encode($aPassengerInfo)]
                )
            );
            return;
        }

        $this->_aPassengerInfo = $aPassengerInfo;
    }

    /**
     * 会员系统
     * @param int    $uid         用户id
     * @param string $sLang       语言
     * @param array  $productList $productList
     * @param int    $cityID      $cityID
     * @return array
     */
    private static function _getMemberInfo($uid, $sLang, $productList, $cityID) {
        $aHitInfo         = array(
            'city_id' => $cityID,
            'version' => 2,
        );
        $oMemberClient    = new MemberSystemClient();
        $aMultiMemberInfo = $oMemberClient->multiProductsQueryInfoV2($uid, $sLang, $productList, $aHitInfo);
        $aMemberRes       = [];
        if (empty($aMultiMemberInfo)) {
            NuwaLog::warning(
                Msg::formatArray(
                    Code::E_PASSENGER_GET_MEMBER_PROFILE_ERROR,
                    ['result' => json_encode($aMultiMemberInfo)]
                )
            );
            return $aMemberRes;
        }

        if (!empty($aMultiMemberInfo['product_list'])) {
            foreach ($aMultiMemberInfo['product_list'] as $aProductInfo) {
                if (!empty($aProductInfo['product_info'])) {
                    $dpa         = $aProductInfo['privileges']['dpa'] ?? [];
                    $aMemberInfo = [
                        'level_id'   => $aProductInfo['level_id'],
                        'level_name' => $aProductInfo['level_name_new'],
                        'is_auto'    => $dpa['item'][0]['is_auto'] ?? 0,
                    ];
                    $aMemberRes[
                        $aProductInfo['product_info']['business_id'].
                        $aProductInfo['product_info']['require_level']] = $aMemberInfo;
                }
            }
        }

        return $aMemberRes;
    }
}
