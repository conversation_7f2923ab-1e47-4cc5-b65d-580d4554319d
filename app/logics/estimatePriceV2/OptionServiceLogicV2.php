<?php

namespace PreSale\Logics\estimatePriceV2;

use BizCommon\Logics\Activity\ActivityConfigManager;
use BizLib\Client\HundunClient;
use BizLib\Log;
use BizLib\Utils\Common as UtilsCommon;
use Nuwa\ApolloSDK\Apollo;

/**
 * Class OptionServiceLogicV2
 * @package PreSale\Logics\estimatePriceV2
 */
class OptionServiceLogicV2
{
    protected static $_oInstance = null;

    /**
     * @return null|OptionServiceLogicV2
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * 获取偏好
     * @param $aParams $aParams $aParams
     * @return array
     */
    public static function getService($aParams) {
        $aReturn        = UtilsCommon::getErrMsg(GLOBAL_SUCCESS);
        $oApollo        = new Apollo();
        $oFeatureToggle = $oApollo->featureToggle(
            'pre_sale_option_open_control_v2',
            [
                'key'           => $aParams['pid'],
                'city'          => $aParams['area'],
                'phone'         => $aParams['phone'],
                'lang'          => $aParams['lang'],
                'product_id'    => $aParams['product_id'],
                'app_version'   => $aParams['app_version'],
                'access_key_id' => $aParams['access_key_id'],
            ]
        );

        if (!$oFeatureToggle->allow()) {
            return $aReturn;
        }

        $aReturn['data'] = self::getOptionService($aParams);
        self::_setActivityData($aReturn, $aParams);
        return $aReturn;
    }

    /**
     * 获取hundun中的偏好服务
     * @param array $aParams params
     * @return array
     */
    public static function getOptionService($aParams) {
        $aRet = (new HundunClient())->getTailorServiceV2($aParams);
        if (0 != $aRet['errno'] || !isset($aRet['errno'])) {
            Log::warning('get hundun service error|' . 'errno:' . $aRet['errno'] . '|errmsg：' . $aRet['errmsg']);
        }

        return $aRet['data'];
    }

    /**
     * todo 设置彩单车活动数据（彩单车新偏好设置页配置）
     * 覆盖 title、sub_title、head、head_img、sub_title_link 字段
     * @param array $aReturn aReturn
     * @param array $aParams aParams
     * @return void
     */
    private static function _setActivityData(&$aReturn, $aParams) {
        $sPassengerId  = $aParams['pid'];
        $iCityId       = $aParams['area'];
        $iProductId    = $aParams['product_id'];
        $iRequireLevel = $aParams['require_level'];
        if (empty($sPassengerId) || empty($iCityId)|| empty($iProductId)|| empty($iRequireLevel)) {
            return;
        }

        $aHitActivityData = ActivityConfigManager::getUserHitActivityData($sPassengerId, $iCityId, $iProductId , $iRequireLevel);
        if (is_array($aHitActivityData) && !empty($aHitActivityData['activity_id'])) {
            $aActivityConfig = ActivityConfigManager::getActivityConfigById($aHitActivityData['activity_id'], $aParams['lang']);
            if (empty($aActivityConfig) || empty($aActivityConfig['option_service_new_page'])) {
                return;
            }

            $aConfig = $aActivityConfig['option_service_new_page'];
            if (!empty($aConfig['head'])) {
                $aReturn['data']['head'] = $aConfig['head'];
            }

            if (!empty($aConfig['title'])) {
                $aReturn['data']['title'] = $aConfig['title'];
            }

            if (!empty($aConfig['sub_title'])) {
                $aReturn['data']['sub_title'] = $aConfig['sub_title'];
            }

            if (!empty($aConfig['head_img'])) {
                $aReturn['data']['head_img'] = $aConfig['head_img'];
            }

            if (!empty($aConfig['sub_title_link'])) {
                $aReturn['data']['sub_title_link'] = $aConfig['sub_title_link'];
            }
        }
    }
}
