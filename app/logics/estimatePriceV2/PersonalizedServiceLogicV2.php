<?php

namespace PreSale\Logics\estimatePriceV2;

use Biz<PERSON>ommon\Constants\OrderNTuple;
use BizLib\Client\MemberSystemClient;
use BizLib\Client\PrfsClient;
use BizLib\Client\SsseClient;
use BizLib\Config as NuwaConfig;
use BizLib\Constants\Common;
use BizLib\Constants\Common as ConstantsCommon;
use BizLib\Constants\Horae;
use BizLib\Utils\Product as ProductUtil;
use BizLib\Log;
use BizLib\Utils\Horae as HoraeUtils;
use BizLib\Utils\Language;
use BizLib\Utils\ProductCategory;
use BizLib\Utils\Registry;
use BizLib\Utils\UtilHelper;
use Dirpc\SDK\Hundun\SceneCommReq;
use Dirpc\SDK\Hundun\SceneReq;
use Dirpc\SDK\PreSale\LuxMultiEstimatePriceRequest as LuxRequest;
use Dirpc\SDK\PreSale\MultiEstimatePriceRequest as Request;
use Dirpc\SDK\Ssse;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\estimatePrice\OptionServiceLogic;
use PreSale\Logics\estimatePriceV2\multiRequest\AreaInfo;
use PreSale\Logics\estimatePriceV2\multiRequest\CommonInfo;
use PreSale\Logics\estimatePriceV2\multiRequest\Product;
use PreSale\Logics\scene\custom\CustomLogic;
use PreSale\Logics\taxi\TaxiPeakFee;
use PreSale\Models\fence\FenceInfo;
use BizLib\Utils\Product as UtilsProduct;
use PreSale\Models\rpc\HundunRpc;

/**
 * Class PersonalizedServiceLogicV2
 * @package PreSale\Logics\estimatePriceV2
 */
class PersonalizedServiceLogicV2
{

    private static $_oInstance   = null;
    private $_aSsseCustomService = []; // 从 ssse 中请求到的个性化服务
    private $_aSceneData         = [];

    private $_oSsseClient          = null;
    private $_aBaseProductList     = [];
    private $_aChooseCustomService = []; // 用来承载端上上传的用户选择的个性化服务

    private $_aMemberInfo = [];

    const ORIGIN_PRICE_TEXT = '%s%s';

    // 豪华车偏好设置昵称文案key
    const LUXURY_CAR_APPELLATION_TEXT_KEY = 'luxury_car_appellation_text';

    // 豪华车定制迭代页面更多服务文案前缀
    const LUXURY_CAR_PREFER_TEXT_PREFIX = 'prefer_info';

    // 豪华车偏好设置更多服务默认文案key
    const LUXURY_CAR_EXTRA_SERVICE_TEXT_KEY = 'luxury_car_extra_service_text';

    // 豪华车偏好设置服务反馈相关文案key
    const LUXURY_CAR_SERVICE_FEEDBACK_TEXT_KEY = "luxury_car_service_feedback_text";

    // 举牌接机服务
    const PICK_UP_SERVICE_NAME = 'airport_pick_up';

    // hundun callee
    const HUNDUN_CALLEE = 'hundun';

    //hundun 降级开关
    const APOLLO_TOGGLE_HUNDUN_CONTROLLER = 'hundun_access_controller';

    /**
     * PersonalizedServiceLogicV2 constructor.
     */
    private function __construct() {
        $this->_oSsseClient = new SsseClient();
    }

    /**
     * @return PersonalizedServiceLogicV2
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * 由airport_type代替combo_type
     * @param int $iAirportType $iAirportType
     * @return int
     */
    public static function getComboType($iAirportType) {
        switch ($iAirportType) {
            case OrderNTuple::AIRPORT_TYPE_FROM:
                return Horae::TYPE_COMBO_FROM_AIRPORT;
            case OrderNTuple::AIRPORT_TYPE_TO:
                return Horae::TYPE_COMBO_TO_AIRPORT;
            default:
                break;
        }

        return Horae::TYPE_COMBO_DEFAULT;
    }

    /**
     * PGetTailorService 二级偏好页
     * @param LuxRequest $oEstimateRequest $oEstimateRequest
     * @param array      $aPassengerInfo   $aPassengerInfo
     * @param array      $aAreaInfo        $aAreaInfo
     * @return void
     */
    public function loadCustomServiceSecondaryPage($oEstimateRequest, $aPassengerInfo, $aAreaInfo) {
        $aCommonRequestParam = self::buildSsseCommonReq($oEstimateRequest, $aPassengerInfo, $aAreaInfo);
        // 构建品类列表
        $aBaseProductList[] = [
            'business_id'   => $oEstimateRequest->getBusinessId(),
            'combo_type'    => self::getComboType($oEstimateRequest->getAirportType()),
            'require_level' => $oEstimateRequest->getRequireLevel(),
        ];

         $this->_getServiceFromSsse(
             $aBaseProductList,
             $aCommonRequestParam,
             $aAreaInfo['id'],
             $aPassengerInfo['phone'],
            $oEstimateRequest->getMenuId()
         );
    }

    /**
     * PGetTailorService 二级偏好页
     * @param LuxRequest $oEstimateRequest $oEstimateRequest
     * @param array      $aPassengerInfo   $aPassengerInfo
     * @param array      $aAreaInfo        $aAreaInfo
     * @return void
     */
    public function loadCustomServiceformHundun($oEstimateRequest, $aPassengerInfo, $aAreaInfo) {
        // hundun 降级开关
        $oApollo       = new Apollo();
        $bIsOpenHundun = $oApollo->featureToggle(
            self::APOLLO_TOGGLE_HUNDUN_CONTROLLER,
            [
                'city'   => $aAreaInfo['id'],
                'phone'  => $aPassengerInfo['phone'],
                'caller' => 'pre_sale',
            ]
        )->allow();

        if (Common::MENU_DACHE_ANYCAR != $oEstimateRequest->getMenuId() || !$bIsOpenHundun) {
            //不满足条件，返回
            return;
        }

        $sDepartureTime = empty($oEstimateRequest->getDepartureTime()) ? time() : $oEstimateRequest->getDepartureTime();

        //Hundun公用参数
        $hundunCommonReq = new SceneCommReq();
        $hundunCommonReq->setCaller(MODULE_NAME);
        $hundunCommonReq->setAppVersion($oEstimateRequest->getAppVersion());
        $hundunCommonReq->setAccessKeyId($oEstimateRequest->getAccessKeyId());
        $hundunCommonReq->setLang($oEstimateRequest->getLang());
        $hundunCommonReq->setChannel($oEstimateRequest->getChannel());
        $hundunCommonReq->setPid($aPassengerInfo['pid']);
        $hundunCommonReq->setArea($aAreaInfo['id']);
        $hundunCommonReq->setPhone($aPassengerInfo['phone']);
        $hundunCommonReq->setCallee(self::HUNDUN_CALLEE);
        $hundunCommonReq->setDistrict($aAreaInfo['district']);
        $hundunCommonReq->setCountyId($aAreaInfo['countyid']);

        //Hundun节点参数
        $aReqs           = [];
        $aParam['pid']   = (int)$aPassengerInfo['pid'];
        $aParam['phone'] = (string)$aPassengerInfo['phone'];
        $aParam['access_key_id']  = $oEstimateRequest->getAccessKeyId();
        $aParam['app_version']    = $oEstimateRequest->getAppVersion();
        $aParam['lang']           = $oEstimateRequest->getLang();
        $aParam['area']           = $aAreaInfo['id'];
        $aParam['flat']           = $oEstimateRequest->getFromLat();
        $aParam['flng']           = $oEstimateRequest->getFromLng();
        $aParam['tlat']           = $oEstimateRequest->getToLat();
        $aParam['tlng']           = $oEstimateRequest->getToLng();
        $aParam['type']           = (int)$oEstimateRequest->getOrderType();
        $aParam['page_type']      = $oEstimateRequest->getPageType();
        $aParam['call_car_type']  = $oEstimateRequest->getCallCarType();
        $aParam['departure_time'] = (int)$sDepartureTime;
        $aParam['menu_id']        = $oEstimateRequest->getMenuId();
        $aParam['sub_menu_id']    = '';
        $aParam['traffic_number'] = $oEstimateRequest->getTrafficNumber();
        $aParam['traffic_dep_time']    = $oEstimateRequest->getTrafficDepTime();
        $aParam['airport_id']          = $oEstimateRequest->getAirportId();
        $aParam['flight_dep_code']     = $oEstimateRequest->getFlightDepCode();
        $aParam['flight_dep_terminal'] = $oEstimateRequest->getFlightDepTerminal();
        $aParam['extra_info']          = ['gender' => 0]; //专豪偏好页不用感知女乘客呼叫女司机
        if (empty($oEstimateRequest->getAirportType()) && empty($oEstimateRequest->getAirportId())) {
            //判断机场状态
            // set AirportType to req
            self::setAirportStationDataFromPrfs($aParam,$oEstimateRequest);
        }

        //品类数据依赖AirportType的计算
        $aParam['business_id']      = (int)$oEstimateRequest->getBusinessId();
        $aParam['product_category'] = (int)$oEstimateRequest->getProductCategory();
        $aParam['combo_type']       = self::getComboType($oEstimateRequest->getAirportType());
        $aParam['require_level']    = (string)$oEstimateRequest->getRequireLevel();
        $aParam['carpool_type']     = 0;
        $aParam['product_id']       = (int)productUtil::getProductIdByBusinessId($oEstimateRequest->getBusinessId());
        $aReqs[] = $aParam;

        //组装Hundun请求
        $hundunReq = new SceneReq();
        $hundunReq->setCommonReq($hundunCommonReq);
        $hundunReq->setNodeReq(json_encode($aReqs));

        //获取hundun响应
        $aResponse = HundunRpc::getInstance()->getServiceList($hundunReq);
        if (empty($aResponse)) {
            return;
        }

        $this->_aSsseCustomService = $aResponse[$oEstimateRequest->getProductCategory()]['service_data'];
    }

    /**
    * 构建请求ssse需要的公共参数
    * @param LuxRequest $oEstimateRequest $oEstimateRequest
    * @param array      $aPassengerInfo   $aPassengerInfo
    * @param array      $aAreaInfo        $aAreaInfo
    * @return mixed
    */
    public static function buildSsseCommonReq(&$oEstimateRequest, $aPassengerInfo, $aAreaInfo) {
        $aParam['pid']            = (int)$aPassengerInfo['pid'];
        $aParam['phone']          = $aPassengerInfo['phone'];
        $aParam['type']           = $oEstimateRequest->getOrderType();
        $aParam['area']           = $aAreaInfo['id'] ?? 0;
        $aParam['flat']           = (double)$oEstimateRequest->getFromLat();
        $aParam['flng']           = (double)$oEstimateRequest->getFromLng();
        $aParam['tlat']           = (double)$oEstimateRequest->getToLat();
        $aParam['tlng']           = (double)$oEstimateRequest->getToLng();
        $aParam['call_car_type']  = $oEstimateRequest->getCallCarType();
        $aParam['departure_time'] = empty($oEstimateRequest->getDepartureTime()) ? time() : $oEstimateRequest->getDepartureTime();
        $aParam['menu_id']        = $oEstimateRequest->getMenuId();
        $aParam['sub_menu_id']    = '';
        $aParam['app_version']    = $oEstimateRequest->getAppVersion();
        $aParam['lang']           = $oEstimateRequest->getLang();
        $aParam['traffic_number'] = $oEstimateRequest->getTrafficNumber();
        $aParam['airport_id']     = $oEstimateRequest->getAirportId(); //不是从dds取的数据，只能依赖端上
        $aParam['flight_dep_code']     = $oEstimateRequest->getFlightDepCode();
        $aParam['flight_dep_terminal'] = $oEstimateRequest->getFlightDepTerminal();
        $aParam['traffic_dep_time']    = $oEstimateRequest->getTrafficDepTime();
        $aParam['page_type']           = $oEstimateRequest->getPageType();
        $aParam['access_key_id']       = $oEstimateRequest->getAccessKeyId();
        if (empty($oEstimateRequest->getAirportType()) && empty($oEstimateRequest->getAirportId())) {
            // set AirportType to req
            self::setAirportStationDataFromPrfs($aParam,$oEstimateRequest);
        }

        return $aParam;
    }

    /**
     * 获取airport_id，并判断是否是接机单
     * 获取railway_id，并判断是否是接站单
     * @param array      $aParam           $aParam
     * @param LuxRequest $oEstimateRequest $oEstimateRequest
     * @return bool $bAirportPickUp
     */
    public static function setAirportStationDataFromPrfs(&$aParam, $oEstimateRequest) {
        $iProductId  = UtilsProduct::getProductIdByBusinessId($oEstimateRequest->getBusinessId());
        $oPrfsClient = new PrfsClient();
        $aRet        = $oPrfsClient->atMultiFence(
            $aParam['flat'],
            $aParam['flng'],
            $aParam['tlat'],
            $aParam['tlng'],
            $iProductId,
            1
        );
        if (!isset($aRet) && 0 != $aRet['errno']) {
            Log::warning('errno:' . $aRet['errno'] . '| errmsg:' . $aRet['errmsg']);
            return false;
        }

        if (!isset($aRet['data'])) {
            return false;
        }

        $aStartingFenceInfo = $aRet['data']['starting_info']['fence_info'] ?? [];
        $aDestFenceInfo     = $aRet['data']['dest_info']['fence_info'] ?? [];

        // 先判断机场
        if (!empty($aStartingFenceInfo)) {
            foreach ($aStartingFenceInfo as $aFenceInfo) {
                if (UtilHelper::checkOrderTag($aFenceInfo['tag'], ['tag_airport'])) {
                    $oEstimateRequest->setAirportType(OrderNTuple::AIRPORT_TYPE_FROM);
                    $aParam['airport_type'] = OrderNTuple::AIRPORT_TYPE_FROM;
                }

                $aSceneInfoList = $aFenceInfo['scene_info'];
                if (empty($aSceneInfoList)) {
                    continue;
                }
                foreach ($aSceneInfoList as $aSceneInfo) {
                    if (!isset($aSceneInfo['airport_id']) || 0 == (int)$aSceneInfo['airport_id']) {
                        continue;
                    }

                    $aParam['airport_id'] = (int)$aSceneInfo['airport_id'];
                    return true;
                }
            }
        }

        if (!empty($aDestFenceInfo)) {
            foreach ($aDestFenceInfo as $aFenceInfo) {
                if (UtilHelper::checkOrderTag($aFenceInfo['tag'], ['tag_airport'])) {
                    $oEstimateRequest->setAirportType(OrderNTuple::AIRPORT_TYPE_TO);
                    $aParam['airport_type'] = OrderNTuple::AIRPORT_TYPE_TO;
                }

                $aSceneInfoList = $aFenceInfo['scene_info'];
                if (empty($aSceneInfoList)) {
                    continue;
                }
                foreach ($aSceneInfoList as $aSceneInfo) {
                    if (!isset($aSceneInfo['airport_id']) || 0 == (int)$aSceneInfo['airport_id']) {
                        continue;
                    }

                    $aParam['airport_id'] = (int)$aSceneInfo['airport_id'];
                    return true;
                }
            }
        }

        // 后判断车站
        if (!empty($aDestFenceInfo)) {
            foreach ($aDestFenceInfo as $aFenceInfo) {
                if (UtilHelper::checkOrderTag($aFenceInfo['tag'], ['tag_railway'])) {
                    $oEstimateRequest->setRailwayType(OrderNTuple::RAILWAY_TYPE_TO);
                    $aParam['railway_type'] = OrderNTuple::RAILWAY_TYPE_TO;
                }

                $aSceneInfoList = $aFenceInfo['scene_info'];
                if (empty($aSceneInfoList)) {
                    continue;
                }
                foreach ($aSceneInfoList as $aSceneInfo) {
                    if (!isset($aSceneInfo['railway_id']) || 0 == (int)$aSceneInfo['railway_id']) {
                        continue;
                    }

                    $aParam['railway_id'] = (int)$aSceneInfo['railway_id'];
                    return true;
                }
            }
        }

        if (!empty($aStartingFenceInfo)) {
            foreach ($aStartingFenceInfo as $aFenceInfo) {
                if (UtilHelper::checkOrderTag($aFenceInfo['tag'], ['tag_railway'])) {
                    $oEstimateRequest->setRailwayId(OrderNTuple::RAILWAY_TYPE_FROM);
                    $aParam['railway_type'] = OrderNTuple::RAILWAY_TYPE_FROM;
                }

                $aSceneInfoList = $aFenceInfo['scene_info'];
                if (empty($aSceneInfoList)) {
                    continue;
                }
                foreach ($aSceneInfoList as $aSceneInfo) {
                    if (!isset($aSceneInfo['railway_id']) || 0 == (int)$aSceneInfo['railway_id']) {
                        continue;
                    }

                    $aParam['railway_id'] = (int)$aSceneInfo['railway_id'];
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 构建请求sps需要的公共参数
     * @param LuxRequest $oEstimateRequest $oEstimateRequest
     * @param array      $aPassengerInfo   $aPassengerInfo
     * @param array      $aAreaInfo        $aAreaInfo
     * @param int        $iProductCategory $iProductCategory
     * @return mixed
     */
    public static function getSpsCommonParams($oEstimateRequest, $aPassengerInfo, $aAreaInfo, $iProductCategory) {
        $aParam['passenger_id'] = $aPassengerInfo['pid'];
        $aParam['city']         = $aAreaInfo['id'];
        $aParam['product_id']   = UtilsProduct::getProductIdByBusinessId($oEstimateRequest->getBusinessId());
        $aParam['car_level']    = $oEstimateRequest->getRequireLevel();
        $aParam['combo_type']   = self::getComboType($oEstimateRequest->getAirportType());
        $aParam['product_category'] = $iProductCategory;
        $aParam['departure_time']   = $oEstimateRequest->getDepartureTime();
	    // 预约单切换为实时单时，会传departure_time，实时单且没有传时，修正该字段
        if (empty($aParam['departure_time']) && empty($oEstimateRequest->getOrderType())) {
            $aParam['departure_time'] = time();
        }

        return $aParam;
    }

    /**
     * @param int    $iBusinessId $iBusinessId
     * @param int    $iPid        乘客id
     * @param string $sLang       语言
     * @param array  $aAreaInfo   $aAreaInfo
     * @return array|mixed
     */
    public static function getMemberInfoRPC($iBusinessId, $iPid, $sLang, $aAreaInfo) {
        // 获取会员权益数据
        return (new MemberSystemClient())->queryInfo(
            UtilsProduct::getProductIdByBusinessId($iBusinessId),
            $iPid,
            $sLang,
            false,
            [
                'city_id' => $aAreaInfo['id'] ?? 0,
                'version' => 2,
            ]
        );
    }

    /**
     * @param array  $aBaseProductList    原始oneconf
     * @param array  $aCommonRequestParam 获取个性化服务公共参数
     * @param int    $iFromArea           $iFromArea
     * @param string $sPhone              $sPhone
     * @param string $sMenuId             $sMenuId
     * @return void
     */
    private function _getServiceFromSsse($aBaseProductList, $aCommonRequestParam, $iFromArea, $sPhone, $sMenuId = '') {
        // ssse 降级开关
        $oApollo     = new Apollo();
        $bIsOpenSsse = $oApollo->featureToggle(
            'gs_ssse_flow_degrade_switch',
            [
                'city'  => $iFromArea,
                'phone' => $sPhone,
            ]
        )->allow();

        if (Common::MENU_DACHE_ANYCAR == $sMenuId && $bIsOpenSsse) {
            $aReqs       = [];
            $oSsseParams = new Ssse\GetMultiServiceReq();
            foreach ($aBaseProductList as $aOneConfItem) {
                $aParam['business_id']   = (int)($aOneConfItem['business_id']);
                $aParam['combo_type']    = (int)($aOneConfItem['combo_type']);
                $aParam['require_level'] = (string)($aOneConfItem['require_level']);
                $aParam += $aCommonRequestParam;

                $oReq    = new Ssse\GetServiceReq($aParam);
                $aReqs[] = $oReq;
            }

            $oSsseParams->setCaller(MODULE_NAME);
            $oSsseParams->setReqs($aReqs);

            $aRet = $this->_oSsseClient->getService($oSsseParams);
            if (0 != $aRet['errno'] || !isset($aRet['errno'])) {
                return;
            } else {
                $this->_aSsseCustomService = $aRet['data'];
            }
        }
    }

    /**
     * 构建新偏好设置页增值服务
     * @param array      $aCustomFeatures  $aCustomFeatures
     * @param LuxRequest $oEstimateRequest $oEstimateRequest
     * @param array      $aPassengerInfo   $aPassengerInfo
     * @param array      $aAreaInfo        $aAreaInfo
     * @return array
     * @throws \Exception e
     */
    public function buildNewCustomService($aCustomFeatures, $oEstimateRequest, $aPassengerInfo, $aAreaInfo, $bIsLuxury) {
        if (empty($this->_aSsseCustomService) || empty($this->_aSsseCustomService[0])) {
            return [];
        }

        $aFormatService = $this->_formatNewServiceData($this->_aSsseCustomService[0], $aCustomFeatures);

        // 获取会员权益数据
        $this->_aMemberInfo = PersonalizedServiceLogicV2::getMemberInfoRPC(
            $oEstimateRequest->getBusinessId(),
            $aPassengerInfo['pid'],
            $oEstimateRequest->getLang(),
            $aAreaInfo
        );

        // 通过sps获取个性化服务价格 （老链路）
        $aSpsAddServiceReqs = [];
        $aSpsAddServiceReqs = $this->buildSpsAddServiceReqs(
            $aFormatService,
            self::getSpsCommonParams($oEstimateRequest, $aPassengerInfo, $aAreaInfo, $oEstimateRequest->getProductCategory()),
            $this->_aMemberInfo,
            $aSpsAddServiceReqs
        );

        //价格相关渲染逻辑
        SpsAddServicePriceLogic::getInstance()->initAddServiceFeeInfo($aSpsAddServiceReqs);
        SpsAddServicePriceLogic::getInstance()->addNewCustomServicePrice(
            $aFormatService,
            $aPassengerInfo['pid'],
            $oEstimateRequest,
            $this->_aMemberInfo,
            false,
            $aAreaInfo
        );

        return self::_buildRespServiceData($oEstimateRequest, $aFormatService, $bIsLuxury);
    }

    /**
     * 构建新偏好设置页增值服务
     * @param array      $aCustomFeatures  $aCustomFeatures
     * @param LuxRequest $oEstimateRequest $oEstimateRequest
     * @param array      $aPassengerInfo   $aPassengerInfo
     * @param array      $aAreaInfo        $aAreaInfo
     * @param bool       $bIsLuxury        $bIsLuxury
     * @return array
     * @throws \Exception e
     */
    public function buildNewCustomServiceWithHundun($aCustomFeatures, $oEstimateRequest, $aPassengerInfo, $aAreaInfo, $bIsLuxury) {
        if (empty($this->_aSsseCustomService)) {
            return [];
        }

        //是否命中个性化服务新链路
        $bIsHitPriceNew = $this->_isNewSpsPrice($oEstimateRequest, $aPassengerInfo, $aAreaInfo);

        //个性化服务响应格式化
        $aFormatService = $this->_formatHundunData($oEstimateRequest,$this->_aSsseCustomService, $bIsHitPriceNew, $aCustomFeatures);

        // 获取会员权益数据
        $this->_aMemberInfo = PersonalizedServiceLogicV2::getMemberInfoRPC(
            $oEstimateRequest->getBusinessId(),
            $aPassengerInfo['pid'],
            $oEstimateRequest->getLang(),
            $aAreaInfo
        );

        // 通过sps获取个性化服务价格 （老链路）
        $aSpsAddServiceReqs = [];
        $aSpsAddServiceReqs = $this->buildSpsAddServiceReqs(
            $aFormatService,
            self::getSpsCommonParams($oEstimateRequest, $aPassengerInfo, $aAreaInfo, $oEstimateRequest->getProductCategory()),
            $this->_aMemberInfo,
            $aSpsAddServiceReqs
        );

        //价格相关渲染逻辑
        SpsAddServicePriceLogic::getInstance()->initAddServiceFeeInfo($aSpsAddServiceReqs);
        SpsAddServicePriceLogic::getInstance()->addNewCustomServicePrice(
            $aFormatService,
            $aPassengerInfo['pid'],
            $oEstimateRequest,
            $this->_aMemberInfo,
            $bIsHitPriceNew,
            $aAreaInfo
        );

        return self::_buildRespServiceData($oEstimateRequest, $aFormatService, $bIsLuxury);
    }

    /**
     * @param LuxRequest $oEstimateRequest $oEstimateRequest
     * @param array      $aFormatService   $aFormatService
     * @return array
     */
    private function _buildRespServiceData($oEstimateRequest, $aFormatService, $bIsLuxury) {
        $aRet = [];
        foreach ($aFormatService as $aService) {
            if ($aService['is_multi_select']) {
                $aRet['upgrade_info'][] = $aService;
            } else {
                if ($oEstimateRequest->getIsMultiSelect()) {
                    $aService['status'] = 0; //多勾品类服务不可选
                }

                $aRet['warm_info'][] = $aService;
            }
        }

        $aConfig = NuwaConfig::text('scene_data', 'service_head');
        if (!empty($aConfig) && !empty($aRet['upgrade_info'])) {
            $aRet['upgrade_head'] = $aConfig['upgrade_head'];
            if ($bIsLuxury) {
                $aRet['upgrade_head'] = $aConfig['upgrade_head_luxury'];
            }
        }

        if (!empty($aConfig) && !empty($aRet['warm_info'])) {
            $aRet['warm_head'] = $aConfig['warm_head'];
            if (empty($aRet['warm_info'][0]['status'])) {
                $aRet['warm_desc'] = $aConfig['warm_desc'][$oEstimateRequest->getProductCategory()] ?? '';
            }
        }

        return $aRet;
    }

    /**
     * @param LuxRequest $oEstimateRequest $oEstimateRequest
     * @param array      $aServiceInfo     $aServiceInfo
     * @param bool       $bIsHitPriceNew   $bIsHitPriceNew
     * @param array      $aChooseList      $aChooseList
     * @return array
     */
    private function _formatHundunData($oEstimateRequest, $aServiceInfo, $bIsHitPriceNew, $aChooseList = []) {
        $aFormatService = [];

        if (!empty($aChooseList)) {
            $aChooseList = array_combine(array_column($aChooseList, 'id'), $aChooseList);
        }

        foreach ($aServiceInfo as $item) {
            if (!$this->_checkService($item)) {
                continue;
            }

            if (CustomLogic::CUSTOM_SERVICE_GUIDE_NEW == $item['service_id'] && ProductUtil::COMMON_PRODUCT_ID_FIRST_CLASS_CAR == $oEstimateRequest->getBusinessId()) {
                //目前只有豪华车的机场助理引导，开量后会删除这个逻辑，所以不收在方法内
                //命中价格新链路
                if ($bIsHitPriceNew && empty($item['fee_info'])) {
                    //无价格数据 不正常 过滤
                    continue;
                }

                if (!$bIsHitPriceNew && horae::HORAE_SCENE_TYPE_AIRPORT_PICK_UP == $oEstimateRequest->getPageType()) {
                    //未命中，接机独立页不展示
                    continue;
                }
            }

            $iSelectedCount = 0; // 已选择的数量，默认为0
            if (!empty($aChooseList) && key_exists($item['service_id'], $aChooseList)) {
                $count          = (int)($aChooseList[$item['service_id']]['count']);
                $iSelectedCount = $count;
            }

            $aFormatService[] = [
                'id'               => $item['service_id'],
                'max'              => $item['max'],
                'title'            => $item['title'],
                'unit'             => $item['unit'],
                'selected_count'   => $iSelectedCount,
                'detail'           => $item['detail'],
                'icon'             => $item['icon'],
                'is_multi_select'  => $item['is_multi_select'],
                'desc'             => $item['tips'],
                'status'           => $item['status'],
                'disable_icon'     => $item['disable_icon'],
                'service_fee_info' => $item['fee_info'], //新链路，价格数据由hundun产出
            ];
        }

        return $aFormatService;
    }


    /**
     * @param array $aServiceInfo $aServiceInfo
     * @param array $aChooseList  $aChooseList
     * @return array
     */
    private function _formatNewServiceData($aServiceInfo, $aChooseList = []) {
        $aFormatService = [];

        if (!empty($aChooseList)) {
            $aChooseList = array_combine(array_column($aChooseList, 'id'), $aChooseList);
        }

        foreach ($aServiceInfo as $item) {
            if (!$this->_checkService($item)) {
                continue;
            }

            $iSelectedCount = 0; // 已选择的数量，默认为0
            if (!empty($aChooseList) && key_exists($item['service_id'], $aChooseList)) {
                $count          = (int)($aChooseList[$item['service_id']]['count']);
                $iSelectedCount = $count;
            }

            $aFormatService[] = [
                'id'              => $item['service_id'],
                'max'             => $item['max'],
                'title'           => $item['title'],
                'unit'            => $item['unit'],
                'selected_count'  => $iSelectedCount,
                'detail'          => $item['detail'],
                'icon'            => $item['icon'],
                'is_multi_select' => $item['is_multi_select'],
                'desc'            => $item['tips'],
                'status'          => $item['status'],
                'disable_icon'    => $item['disable_icon'],
            ];
        }

        return $aFormatService;
    }


    /**
     * @param array $aService $aService
     * @return bool
     */
    private function _checkService($aService) {
        if (empty($aService['service_id']) || empty($aService['max']) || empty($aService['title'])) {
            return false;
        }

        // 专车豪华车service_id准入
        $oApollo = new Apollo();
        $aApolloParam['service_id'] = $aService['service_id'];
        $bIsServiceIdAllow          = $oApollo->featureToggle('first_class_additional_service_controller',$aApolloParam)->allow();
        if (!$bIsServiceIdAllow) {
            //个性化服务不应该展示
            return false;
        }

        return true;
    }

    /**
     * 是否命中个性化服务价格新链路 （目前只有豪华车-机场助理）
     * @param LuxRequest $oEstimateRequest $oEstimateRequest
     * @param array      $aPassengerInfo   $aPassengerInfo
     * @param array      $aAreaInfo        $aAreaInfo
     * @return bool
     */
    private function _isNewSpsPrice($oEstimateRequest, $aPassengerInfo, $aAreaInfo) {
        $oApollo     = new Apollo();
        $bIsOpenSsse = $oApollo->featureToggle(
            'gs_ssse_service_new_price',
            [
                'caller'           => 'pre-sale',
                'key'              => $aPassengerInfo['pid'],
                'phone'            => $aPassengerInfo['phone'],
                'app_version'      => $oEstimateRequest->getAppVersion(),
                'access_key_id'    => $oEstimateRequest->getAccessKeyId(),
                'business_id'      => $oEstimateRequest->getBusinessId(),
                'product_category' => $oEstimateRequest->getProductCategory(),
                'page_type'        => $oEstimateRequest->getPageType(),
                'city'             => $aAreaInfo['id'],
            ]
        )->allow();

        return $bIsOpenSsse;
    }

    /**
     * @param array $aFormatService      ssse服务列表
     * @param array $aCommonRequestParam 公共参数
     * @param array $aMemberProfile      会员信息
     * @param array $aSpsAddServiceReqs  reqs
     * @return array
     */
    public function buildSpsAddServiceReqs($aFormatService, $aCommonRequestParam, $aMemberProfile, $aSpsAddServiceReqs) {
        $aSpsAddServiceReq = [];
        if (!empty($aFormatService) && is_array($aFormatService)) {
            if (!empty($aMemberProfile) && !empty($aMemberProfile['privileges'])) {
                foreach ($aMemberProfile['privileges'] as $sKey => $aV) {
                    if ('airport_guide' == $sKey) {
                        $aSpsAddServiceReq['member_privilege']['airport_guide_right'] = 1;
                    }

                    if (self::PICK_UP_SERVICE_NAME == $sKey) {
                        $aSpsAddServiceReq['member_privilege'][self::PICK_UP_SERVICE_NAME] = 1;
                    }
                }
            }

            foreach ($aFormatService as $aService) {
                $aSpsAddServiceReq['service_list'][] = [
                    'service_id' => $aService['id'],
                    'count'      => $aService['selected_count'] ?: 1,
                ];
            }

            $aSpsAddServiceReq   += $aCommonRequestParam;
            $aSpsAddServiceReqs[] = $aSpsAddServiceReq;
        }

        return $aSpsAddServiceReqs;
    }

    /**
     * @param array $aRenderInfo 偏好数据
     * @return array 拼装昵称相关文案的偏好数据
     */
    public function setNickNameText($aRenderInfo): array {
        if (empty($aRenderInfo['data']['prefer_info'])) {
            return $aRenderInfo;
        }
        $sTitle = $aRenderInfo['data']['prefer_info']['title'];
        // 判断当前时间是早上、下午还是晚上，根据时间拼凑文案返回给端上
        $iCurrentHour = date('H');
        $aLuxuryAppellationText = NuwaConfig::text(self::LUXURY_CAR_PREFER_TEXT_PREFIX, self::LUXURY_CAR_APPELLATION_TEXT_KEY);
        if ($iCurrentHour >= 0 && $iCurrentHour < 12) {
            // 早上
            $sGreetText = $aLuxuryAppellationText['greet_text']['morning_greet'];
        } elseif ($iCurrentHour >= 12 && $iCurrentHour < 18) {
            // 下午
            $sGreetText = $aLuxuryAppellationText['greet_text']['afternoon_greet'];
        } else {
            // 晚上
            $sGreetText = $aLuxuryAppellationText['greet_text']['night_greet'];
        }
        $aRenderInfo['data']['prefer_info']['greet'] = $sGreetText;
        if (!empty($sTitle)) {
            $aRenderInfo['data']['prefer_info']['title'] = $sTitle;
            $aRenderInfo['data']['prefer_info']['appellation_ways'] = $aLuxuryAppellationText['appellation_ways_text']['appellation_ways_have_nickname'];
        } else {
            $aRenderInfo['data']['prefer_info']['appellation_ways'] = $aLuxuryAppellationText['appellation_ways_text']['appellation_ways_have_no_nickname'];
        }
        return $aRenderInfo;
    }

    /**
     * @param array $aRenderInfo 偏好数据
     * @return array 拼装常用语默认文案后的偏好数据
     */
    public function setMoreServiceText($aRenderInfo): array {
        if (empty($aRenderInfo['data']['prefer_info'])) {
            return $aRenderInfo;
        }
        $aLuxuryMoreServiceDefaultText = NuwaConfig::text(self::LUXURY_CAR_PREFER_TEXT_PREFIX,
            self::LUXURY_CAR_EXTRA_SERVICE_TEXT_KEY);
        $aRenderInfo['data']['extra_service']['title'] = $aLuxuryMoreServiceDefaultText['title'];
        $aRenderInfo['data']['extra_service']['default_text'] = $aLuxuryMoreServiceDefaultText['default_text'];
        return $aRenderInfo;
    }

    /**
     * @param array $aRenderInfo 偏好数据
     * @return array 拼装服务反馈文案后的偏好数据
     */
    public function setServiceFeedbackText($aRenderInfo): array {
        if (empty($aRenderInfo['data'])) {
            return $aRenderInfo;
        }
        $aLuxuryServiceFeedbackText = NuwaConfig::text(self::LUXURY_CAR_PREFER_TEXT_PREFIX,
            self::LUXURY_CAR_SERVICE_FEEDBACK_TEXT_KEY);
        $aRenderInfo['data']['tip'] = $aLuxuryServiceFeedbackText['tip'];
        $aRenderInfo['data']['tip_link'] = $aLuxuryServiceFeedbackText['tip_link'];
        return $aRenderInfo;
    }

    /**
     * 从hundun获取豪华车更多服务中常用语的数据
     * @param array  $aRenderInfo 偏好数据
     * @param array  $iPid        pid
     * @param string $sLang       lang
     * @param int    $iAreaId     areaId
     * @return array 拼装常用语后的偏好数据
     */
    public function setCommonExpressions($aRenderInfo, $iPid, $sLang, $iAreaId): array {
        if (empty($aRenderInfo['data'])) {
            return $aRenderInfo;
        }
        $aReqParams = array(
            'pid'  => $iPid,
            'lang' => $sLang,
            'area' => $iAreaId,
        );
        $aCommonExpressionsData = OptionServiceLogic::getCommonExpressions($aReqParams);
        $aRenderInfo['data']['extra_service']['common_expressions'] = $aCommonExpressionsData['common_expressions'];
        return $aRenderInfo;
    }
}
