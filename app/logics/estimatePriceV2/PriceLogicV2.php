<?php

namespace PreSale\Logics\estimatePriceV2;

use Biz<PERSON>om<PERSON>\Utils\Order;
use BizLib\Client\AthenaApiClient;
use BizLib\Config as NuwaConfig;
use BizLib\Log as NuwaLog;
use BizLib\Utils\UtilHelper;
use BizLib\Exception\BizException;
use BizLib\Exception\ExceptionWithResp;
use BizLib\Utils as BizUtils;
use BizLib\Constants;
use PreSale\exception\LogicException;
use BizLib\Exception\InvalidArgumentException;
use BizLib\ErrCode;
use BizLib\Client\PriceApiClient;
use PreSale\Logics\estimatePriceV2\params\SceneParamsLogic;
use PreSale\Logics\estimatePriceV2\response\SceneResponseLogic;
use BizCommon\Constants\OrderNTuple;
use PreSale\Logics\estimatePriceV2\response\SceneResponseLogicV2;
use PreSale\Logics\premierPrevilege\SuppressionLogic;
use Nuwa\ApolloSDK\Apollo;

/**
 * 获取账单、支付方式、优惠券、活动券相关数据逻辑类.
 *
 * @date(2017-05-23)
 */
class PriceLogicV2
{
    /** @var $athena AthenaApiClient */
    public $athena;
    public $oParamsLogic;
    /** @var $aIsMultiRoute */
    public $aIsMultiRoute = false;
    /**
     * @var null
     */
    protected static $_oInstance = null;

    /**
     * @var null
     */
    private $_oCI = null;

    /**
     * [$_aParams common、order、passenger、custom_service_info、one_key_activity].
     *
     * @var array
     */
    protected $_aParams = array();

    /**
     * @var array
     */
    private $_aErrMsg;
    /*
     * @var boolean
     */
    private $_bAthenaIsFailed = false;

    /**
     * @var array
     */
    private $_aAthenaOriginRes = [];

    private $_sGuideResult = '';

    private $_aAthenaExtra = [];

    private $_aAthenaRequest = [];
    /**
     * 支付类型.
     */
    const PAY_TYPE_NEW      = 2;
    const PAY_TYPE_BR       = 1;
    const DEGRADE_OPEN      = true; //默认倒流不降级
    const SHAKE_APP_VERAION = '5.2.32';

    /**
     * PriceLogicV2 constructor.
     *
     * @param array $aParams aParams
     */
    private function __construct(array $aParams) {
        $this->_aParams     = $aParams;
        $this->_aErrMsg     = NuwaConfig::text('errno', 'pEstimatePrice_error_msg');
        $this->oParamsLogic = ParamsLogic::getInstance();
    }

    /**
     * @param array $aParams aParams
     *
     * @return null|PriceLogicV2
     */
    public static function getInstance(array $aParams) {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self($aParams);
        }

        return self::$_oInstance;
    }

    /**
     * 方案wiki:http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=98400320
     * 1.预估请求有athena_params参数且请求athena-api的apollo开关打开,
     * 请求athena-api获取账单预估返回值、企业支付方式接口返回值、券系统获取可用券返回值、乘客运营获取活动券返回值等预估信息.
     *
     * 2.不满足1条件时执行下面逻辑:
     *   1)账单系统预估接口交互逻辑,获取预估车费;
     *   2)企业级支付方式接口交互逻辑,获取支付方式;
     *   3)券系统获取可用券接口交互逻辑,获取可用券;
     *   4)乘客运营获取活动券接口交互逻辑,获取活动券;
     *
     * 3.将车费数据、支付方式数据、券抵扣数据返回
     *
     * @return array
     * @throws InvalidArgumentException InvalidArgumentException.
     */
    public function getMultiResponse() {
        $aAthenaParams = array();
        $sAthenaParams = $this->_aParams[0]['common_info']['athena_params'];
        if (!empty($sAthenaParams)) {
            $aAthenaParams = json_decode($sAthenaParams, true);
            if (empty($aAthenaParams) || !is_array($aAthenaParams)) {
                throw new InvalidArgumentException(
                    ErrCode\Code::E_COMMON_PARAM_JSON_DECODE_FAIL
                );
            }
        }

        $bFromAthena = false;
        $iArea       = $this->_aParams[0]['order_info']['area'] ?? 0;
        //乘客端上传调用Athena参数
        if ($this->_checkAthenaRequest($aAthenaParams) && !$this->_isAthenaDegraded((int)$iArea)) {
            $bFromAthena = true;
        }

        if ($bFromAthena) {
            $this->_aAthenaRequest = $this->oParamsLogic->getMultiAthenaParams($aAthenaParams, $this->_aParams);
            list($aAthenaOriginRes, $sGuideResult, $aAthenaExtra) = $this->_getAthenaOriginResponse($this->_aAthenaRequest);
            $aResponseInfoV2 = $this->_buildResponseAndRequestV2($this->_aAthenaRequest, $aAthenaOriginRes, $sGuideResult, $aAthenaExtra);
            return $aResponseInfoV2;
        } else {
            // athena降级走price-api
            $this->_aAthenaRequest = $this->oParamsLogic->getMultiAthenaParams($aAthenaParams, $this->_aParams);
            list($aAthenaOriginRes, $sGuideResult, $aAthenaExtra) = $this->_getAthenaOriginResponse($this->_aAthenaRequest, 'http');
            $aResponseInfoV2 = $this->_buildResponseAndRequestV2($this->_aAthenaRequest, $aAthenaOriginRes, $sGuideResult, $aAthenaExtra);
        }

        return $aResponseInfoV2;
    }

    /**
     * @param array $aAthenaParams athena参数
     * @return bool
     */
    private function _checkAthenaRequest($aAthenaParams) {
        //乘客端上传调用Athena参数，请求Athena
        if (count($aAthenaParams) > 0) {
            return true;
        }

        $aParam = $this->_aParams[0];
        //开放平台渠道配置phone pid会导致开放平台全量，需慎重
        $aApolloParam = [
            'key'        => $aParam['order_info']['departure_time'],
            'district'   => $aParam['order_info']['district'],
            'city'       => $aParam['order_info']['area'],
            'channel'    => $aParam['order_info']['channel'],
            'phone'      => $aParam['passenger_info']['phone'],
            'pid'        => $aParam['passenger_info']['pid'],
            'type'       => $aParam['order_info']['order_type'],
            'product_id' => $aParam['order_info']['product_id'],
        ];

        $oApollo        = new Apollo();
        $ofeatureToggle = $oApollo->featureToggle('depend_athena_channel_switch', $aApolloParam);
        if ($ofeatureToggle->allow()) {
            return true;
        }

        return false;
    }

    /**
     * @param  int $iArea 城市ID
     * @return bool
     */
    private function _isAthenaDegraded($iArea) {

        $bOpen = UtilHelper::getFusingSwitch('passenger_estimate_athena_degrade_switch', 'default', (int)($iArea));
        if (self::DEGRADE_OPEN === $bOpen) {
            NuwaLog::warning(
                ErrCode\Msg::formatArray(
                    ErrCode\Code::E_COMMON_API_DEGRADE,
                    array(
                        'describe'      => 'athena is degrade',
                        'reqArea'       => $iArea,
                        'requestParams' => json_encode($this->_aParams),
                    )
                )
            );

            return true;
        }

        return false;
    }

    // 注释-待删
//    /**
//     * @return array
//     */
//    public function formatResponseInfoForLog() {
//        try {
//            $aResponseInfo = $this->_buildResponseAndRequest($this->_aAthenaRequest, $this->_aAthenaOriginRes, $this->_sGuideResult, $this->_aAthenaExtra);
//        } catch (\Exception $e) {
//            $aResponseInfo = [];
//        }
//
//        return $aResponseInfo;
//    }

    /**
     * 获取账单预估返回值、企业支付方式接口返回值、收银台支付方式接口返回值、券系统获取可用券返回值、乘客运营获取活动券返回值等预估信息.
     *
     * 1)账单系统预估接口交互逻辑,获取预估车费;
     * 2)企业级支付方式接口交互逻辑,获取支付方式;
     * 3)运营活动:券系统获取可用券接口交互逻辑,获取可用券;乘客运营获取活动券接口交互逻辑,获取活动券;
     * 4)将车费数据、支付方式数据、券抵扣数据返回
     *
     * @return array
     * @throws \Exception Exception.
     * @throws LogicException LogicException.
     */
    public function execute() {
        try {
            $oParams = ParamsLogic::getInstance();

            //账单
            $aBillInfo = BillLogic::getInstance($this->_aParams)->execute();

            //支付方式
            $aPaymentsParams = $oParams->getPaymentsParams($aBillInfo);
            $oPayments       = PaymentsLogic::getInstance($aPaymentsParams);
            $aPaymentsInfo   = $oPayments->execute();
            //运营活动
            $aActivityParams = $oParams->getActivityParams($aBillInfo, $oPayments->isUseCoupon(), $oPayments->isCarpoolUseCoupon());
            $aActivityInfo   = ActivityLogic::getInstance($aActivityParams)->execute();

            //返回数据
            $aResponseInfo = $oParams->getEstimatePriceInfo($aBillInfo, $aPaymentsInfo, $aActivityInfo);
        } catch (\Exception $e) {
            if ($e instanceof BizException || $e instanceof ExceptionWithResp) {
                throw $e;
            }

            throw new LogicException($e->getCode(), $e->getMessage());
        }

        return $aResponseInfo;
    }

    /**
     * @param array  $aAthenaRequest aAthenaRequest
     * @param string $sType          stype
     * @return array
     * @throws InvalidArgumentException InvalidArgumentException.
     */
    private function _getAthenaOriginResponse(&$aAthenaRequest, $sType = 'athena') {
        $this->athena = new AthenaApiClient();
        $sGuideResult = '';

        if ('athena' == $sType) {
            $aAthenaResponse = $this->athena->getMultiBubbleInfo($aAthenaRequest['price_params'], $aAthenaRequest['bubble_params'], $aAthenaRequest['extra_info']);
            $aResponse       = json_encode($aAthenaResponse['estimateprice_resp']);
            $aResponse       = json_decode($aResponse, true);
        } else {
            $aResponse = $this->_getMultiPriceByClient($aAthenaRequest);
        }

        // 从athena获取导流数据
        if (isset($aAthenaResponse['guide_result']) && is_string($aAthenaResponse['guide_result'])) {
            $sGuideResult = $aAthenaResponse['guide_result'];
        }

        $aAthenaExtra = !empty($aAthenaResponse['extra_info']) ? $aAthenaResponse['extra_info'] : array();

        if (empty($aResponse)) {
            throw new InvalidArgumentException(
                ErrCode\Code::E_PRICE_REQUEST_FAIL,
                array('params' => $aAthenaRequest)
            );
        }

        // 专车冒泡抑制处理逻辑
        if (SuppressionLogic::checkSuppressionToggle($aAthenaRequest['bubble_params'])) {
            SuppressionLogic::handle($sGuideResult, $aResponse, $aAthenaExtra);
        }

        // 数据压缩后处理请求参数
        if ('1' == $aAthenaRequest['extra_info']['is_new_data_send']) {
            foreach ($aAthenaRequest['price_params'] as $iIndex => $aPriceReq) {
                if (isset($aPriceReq['extra_info']['is_athena_add_product']) && '1' == $aPriceReq['extra_info']['is_athena_add_product']) {
                    unset($aAthenaRequest['price_params'][$iIndex]);
                }
            }
        }

        if (isset($aAthenaResponse) && !empty($aAthenaResponse['dds_response'])) {
            $aDecisionResponse = json_decode($aAthenaResponse['dds_response'], true);
            if (!empty($aDecisionResponse) && 0 == $aDecisionResponse['errno']) {
                DecisionLogic::getInstance()->setAPreDecisionResult($aDecisionResponse['data']);
            }
        }

        if (isset($aAthenaResponse) && !empty($aAthenaResponse['guide_dds_response'])) {
            $aDecisionResponse = json_decode($aAthenaResponse['guide_dds_response'], true);
            if (!empty($aDecisionResponse) && 0 == $aDecisionResponse['errno']) {
                DecisionLogic::getInstance()->setAthenaDecisionResult($aDecisionResponse['data']);
            }
        }

        $this->_aAthenaOriginRes = $aResponse;
        $this->_sGuideResult     = $sGuideResult;
        $this->_aAthenaExtra     = $aAthenaExtra;
        return [$aResponse, $sGuideResult, $aAthenaExtra];
    }

    // 注释-待删
//    /**
//     * @param array  $aAthenaRequest   array
//     * @param array  $aAthenaOriginRes array
//     * @param string $sGuideResult     string
//     * @param array  $aAthenaExtra     array
//     * @return array
//     * @throws InvalidArgumentException InvalidArgumentException.
//     */
//    private function _buildResponseAndRequest($aAthenaRequest, $aAthenaOriginRes, $sGuideResult, $aAthenaExtra) {
//        //formatResponse
//        $aPriceResponse = array();
//        foreach ($aAthenaOriginRes as $iIndex => $aPrice) {
//            if (empty($aPrice) || !is_array($aPrice) || (empty($aPrice['bill_info']) && empty($aPrice['activity_info']) && empty($aPrice['payments_info']))) {
//                continue;
//            }
//
//            //去掉从apollo拉取的Athena需要追加产品
//            if (isset($aPrice['extra_info']['is_athena_add_product']) && Constants\Common::ATHENA_ADD_PRODUCT == $aPrice['extra_info']['is_athena_add_product']) {
//                continue;
//            }
//
//            $aBillInfo = json_decode($aPrice['bill_info'], true);
//            if (empty($aBillInfo) || !is_array($aBillInfo) || empty($aBillInfo['bills']) || !is_array($aBillInfo['bills'])) {
//                continue;
//            }
//
//            $sCarLevel = array_keys($aBillInfo['bills'])[0];
//            $aPriceResponse[$sCarLevel][] = $aPrice;
//        }
//
//        if ($this->aIsMultiRoute) {
//            //多路线预估
//            $aResponse = $this->_formatMultiRouteResponse($aPriceResponse, $sGuideResult, $aAthenaExtra);
//        } else {
//            $aResponse = $this->_formatMultiResponse($aPriceResponse, $sGuideResult, $aAthenaExtra);
//        }
//
//        if (empty($aResponse)) {
//            throw new InvalidArgumentException(
//                ErrCode\Code::E_ATHENA_REQUEST_FAIL
//            );
//        }
//
//        //formatRequest
//        $aResponseInfo = $this->oParamsLogic->getMultiResponseParams($aAthenaRequest, $aResponse, $this->_aParams);
//        return $aResponseInfo;
//    }

    /**
     * @param array  $aAthenaRequest   array
     * @param array  $aAthenaOriginRes array
     * @param string $sGuideResult     string
     * @param array  $aAthenaExtra     array
     * @return array
     * @throws InvalidArgumentException InvalidArgumentException.
     */
    private function _buildResponseAndRequestV2($aAthenaRequest, $aAthenaOriginRes, $sGuideResult, $aAthenaExtra) {
        //formatResponse
        $aPriceResponse = array();

        // 不再使用car_level作为key
        foreach ($aAthenaOriginRes as $iIndex => $aPrice) {
            if (empty($aPrice) || !is_array($aPrice) || (empty($aPrice['bill_info']) && empty($aPrice['activity_info']) && empty($aPrice['payments_info']))) {
                continue;
            }

            //去掉从apollo拉取的Athena需要追加产品
            if (isset($aPrice['extra_info']['is_athena_add_product']) && Constants\Common::ATHENA_ADD_PRODUCT == $aPrice['extra_info']['is_athena_add_product']) {
                continue;
            }

            $aPriceResponse[$iIndex] = $aPrice;
        }

        $aResponseData = array();
        foreach ($aPriceResponse as $iIndex => $aPrice) {
            $aPriceItem = $this->_buildPriceResponseV2($aPrice);
            $aPriceItem['athena_info']  = $sGuideResult;
            $aPriceItem['athena_extra'] = $aAthenaExtra;
            $aResponseData[$iIndex]     = $aPriceItem;
        }

        if (empty($aResponseData)) {
            throw new InvalidArgumentException(
                ErrCode\Code::E_ATHENA_REQUEST_FAIL
            );
        }

        //formatRequest
        $aResponseInfoV2 = $this->oParamsLogic->getMultiResponseParamsV2($aAthenaRequest, $aResponseData, $this->_aParams);
        return $aResponseInfoV2;
    }


    /**
     * @param array $aPriceParams array
     *
     * @return array
     */
    private function _getMultiPriceByClient($aPriceParams) {
        //请求AthenaIsFailed失败，再请求price-api，重试设置为0
        $aPriceSdKConfig = [];
        if ($this->_bAthenaIsFailed) {
            $aPriceSdKConfig = ['retry' => 0,];
        }

        $_oClient   = new PriceApiClient(PriceApiClient::MODULE_NAME, $aPriceSdKConfig);
        $aParams    = $this->_getMultiPriceParams($aPriceParams);
        $aRet       = $_oClient->estimate($aParams);
        $aAthenaRet = array();
        if (isset($aRet['errno']) && 0 == $aRet['errno']) {
            return $aRet['data'];
        }

        return $aAthenaRet;
    }

    // 注释-待删
//    /**
//     * @param array  $aPriceInfo   array
//     *
//     * @param string $sGuideResult string
//     * @param array  $aAthenaExtra array
//     * @return array
//     */
//    private function _formatMultiRouteResponse($aPriceInfo, $sGuideResult, $aAthenaExtra) {
//        $aRet = array();
//        foreach ($aPriceInfo as $sKey => $aPrice) {
//            foreach ($aPrice as $aPriceItem) {
//                $aPriceItem = $this->_buildPriceResponse($sKey, [$aPriceItem]);
//                $aPriceItem['athena_info']  = $sGuideResult;
//                $aPriceItem['athena_extra'] = $aAthenaExtra;
//                $aRet[$sKey][] = $aPriceItem;
//            }
//        }
//
//        return $aRet;
//    }

   // 注释-待删
//    /**
//     * @param array  $aPriceInfo   array
//     *
//     * @param string $sGuideResult string
//     * @param array  $aAthenaExtra array
//     * @return array
//     */
//    private function _formatMultiResponse($aPriceInfo, $sGuideResult, $aAthenaExtra) {
//        $aRet = array();
//        foreach ($aPriceInfo as $sKey => $aPrice) {
//            $aPriceItem = $this->_buildPriceResponse($sKey, $aPrice);
//            $aPriceItem['athena_info']  = $sGuideResult;
//            $aPriceItem['athena_extra'] = $aAthenaExtra;
//            $aRet[$sKey][] = $aPriceItem;
//        }
//
//        return $aRet;
//    }

   // 注释-待删
//    /**
//     * 将账单、支付、运营活动参数拼装为改造之前的数据格式.
//     *
//     * @param string $sCarLevel  carlevel
//     * @param array  $aPriceInfo priceinfo
//     *
//     * @return mixed
//     */
//    private function _buildPriceResponse($sCarLevel, $aPriceInfo) {
//        $aKeys = array(
//            'bills',
//            'schema_args',
//            'concrete_product_ids',
//            'product_infos',
//            'region_info',
//            'fees_detail_infos',
//        );
//        //预估时若命中跨城拼车，账单返回的里程和行驶时间0，会影响普通快车的气泡展示，特殊处理
//        $aInterCityNoNeedKeys = array(
//            'driver_minute',
//            'driver_metre',
//        );
//        $bShakeFlag           = false;
//        $bPaymentSeparate     = false;
//        $aPayment = null;
//        $aBill    = $aActivity            = array();
//        //shake flag
//        if (version_compare($this->_aParams[0]['common_info']['app_version'], self::SHAKE_APP_VERAION) >= 0) {
//            $bShakeFlag = true;
//        }
//
//        foreach ($aPriceInfo as $iIndex => $aPrice) {
//            $iComboType    = Constants\Horae::TYPE_COMBO_DEFAULT;
//            $aBillInfo     = json_decode($aPrice['bill_info'], true);
//            $aActivityInfo = json_decode($aPrice['activity_info'], true);
//            // activity转换为list，背景是一个产品多价格下需要有多个activity信息。兼容上线过程中不一致
//            if (isset($aActivityInfo) && !empty($aActivityInfo) && !is_array($aActivityInfo[0])) {
//                $aActivityInfo = [$aActivityInfo];
//            }
//
//            $aPaymentInfo = json_decode($aPrice['payments_info'], true);
//            $aExtraInfo   = $aPrice['extra_info'];
//            if (isset($aBillInfo) && !empty($aBillInfo)) {
//                $iComboType = $aBillInfo['product_infos'][$sCarLevel]['combo_type'];
//                //和账单的交互后续用N元组标识产品，所以这里检查是否需要根据N元组将combo_type 做转换
//                $iCheckCombo = $this->_checkOrderNTupleConvert($aBillInfo, $sCarLevel);
//                if ($iCheckCombo) {
//                    $iComboType = $iCheckCombo;
//                }
//
//                //拼车顶导
//                if (SceneParamsLogic::getInstance()->isFromCarpoolEntry()) {
//                    SceneResponseLogic::getInstance()->loadCarpoolEntryInfo($sCarLevel, $aBillInfo, $aExtraInfo);
//                }
//
//                // 将账单的返回值在car_level里面增加一层carpool、noncarpool、anycar
//                foreach ($aKeys as $sItem) {
//                    // anycar
//                    if (BizUtils\CarLevel::DIDI_ANY_CAR_CAR_LEVEL == $sCarLevel) {
//                        foreach ($aBillInfo as $sBillField => $aBillDetail) {
//                            if (!in_array($sBillField, $aKeys)) {
//                                $aBill[$sBillField] = $aBillDetail;
//                            }
//                        }
//
//                        $aBill[$sItem][$sCarLevel]['anycar'] = $aBillInfo[$sItem][$sCarLevel];
//                        if (!empty($aBillInfo['estimate_id'])
//                            && !empty($aBill[$sItem][$sCarLevel]['anycar'])
//                            && is_array($aBill[$sItem][$sCarLevel]['anycar'])
//                        ) {
//                            $aBill[$sItem][$sCarLevel]['anycar']['estimate_id'] = $aBillInfo['estimate_id'];
//                        }
//
//                        if (isset($aExtraInfo['is_default'])
//                            && !empty($aBill[$sItem][$sCarLevel]['anycar'])
//                            && is_array($aBill[$sItem][$sCarLevel]['anycar'])
//                        ) {
//                            $aBill[$sItem][$sCarLevel]['anycar']['is_default']        = $aExtraInfo['is_default'];
//                            $aBill[$sItem][$sCarLevel]['anycar']['user_status']       = $aExtraInfo['user_status'] ?? -1;
//                            $aBill[$sItem][$sCarLevel]['anycar']['is_default_action'] = $aExtraInfo['is_default_action'] ?? -1;
//                        }
//
//                        if (!empty($aBill[$sItem][$sCarLevel]['anycar'])
//                            && is_array($aBill[$sItem][$sCarLevel]['anycar'])
//                            && $bShakeFlag
//                        ) {
//                            if (isset($aExtraInfo['shake_flag'])) {
//                                $aBill[$sItem][$sCarLevel]['anycar']['shake_flag'] = $aExtraInfo['shake_flag'];
//                            }
//
//                            if (isset($aExtraInfo['pre_estimate_fee'])) {
//                                $aBill[$sItem][$sCarLevel]['anycar']['pre_estimate_fee'] = $aExtraInfo['pre_estimate_fee'];
//                            }
//                        }
//
//                        continue;
//                    }
//
//                    $aSpecialOrder = ['product_id' => $aBillInfo['product_infos'][$sCarLevel]['product_line_id'] ?? 0, 'require_level' => $sCarLevel, 'combo_type' => $iComboType];
//                    if (Order::isSpecialRateV2($aSpecialOrder)) {
//                        $iComboType = Constants\Horae::TYPE_COMBO_SPECIAL_RATE;
//                    }
//
//                    switch ($iComboType) {
//                        case Constants\Horae::TYPE_COMBO_CARPOOL:
//                            //no break
//                        case Constants\Horae::TYPE_COMBO_CARPOOL_INTER_CITY:
//                            //no break
//                        case Constants\Horae::TYPE_COMBO_CARPOOL_FLAT_RATE:
//                            foreach ($aBillInfo as $sBillField => $aBillDetail) {
//                                //在跨城拼车情景下，不覆盖$aInterCityNoNeedKeys中数据
//                                if (!in_array($sBillField, $aKeys)) {
//                                    if (!in_array($sBillField, $aInterCityNoNeedKeys) || !isset($aBill[$sBillField])) {
//                                        $aBill[$sBillField] = $aBillDetail;
//                                    }
//                                }
//                            }
//
//                            $aBill[$sItem][$sCarLevel]['carpool'] = $aBillInfo[$sItem][$sCarLevel];
//                            if (!empty($aBillInfo['estimate_id'])
//                                && !empty($aBill[$sItem][$sCarLevel]['carpool'])
//                                && is_array($aBill[$sItem][$sCarLevel]['carpool'])
//                            ) {
//                                $aBill[$sItem][$sCarLevel]['carpool']['estimate_id'] = $aBillInfo['estimate_id'];
//                            }
//
//                            if (isset($aExtraInfo['is_default']) && !empty($aExtraInfo['is_default'])
//                                && !empty($aBill[$sItem][$sCarLevel]['carpool'])
//                                && is_array($aBill[$sItem][$sCarLevel]['carpool'])
//                            ) {
//                                $aBill[$sItem][$sCarLevel]['carpool']['is_default']        = $aExtraInfo['is_default'];
//                                $aBill[$sItem][$sCarLevel]['carpool']['user_status']       = $aExtraInfo['user_status'] ?? -1;
//                                $aBill[$sItem][$sCarLevel]['carpool']['is_default_action'] = $aExtraInfo['is_default_action'] ?? -1;
//                            }
//
//                            if (!empty($aBill[$sItem][$sCarLevel]['carpool'])
//                                && is_array($aBill[$sItem][$sCarLevel]['carpool'])
//                                && $bShakeFlag
//                            ) {
//                                if (isset($aExtraInfo['shake_flag'])) {
//                                    $aBill[$sItem][$sCarLevel]['carpool']['shake_flag'] = $aExtraInfo['shake_flag'];
//                                }
//
//                                if (isset($aExtraInfo['pre_estimate_fee'])) {
//                                    $aBill[$sItem][$sCarLevel]['carpool']['pre_estimate_fee'] = $aExtraInfo['pre_estimate_fee'];
//                                }
//                            }
//                            break;
//                        case Constants\Horae::TYPE_COMBO_SPECIAL_RATE:
//                            foreach ($aBillInfo as $sBillField => $aBillDetail) {
//                                if (!in_array($sBillField, $aKeys)) {
//                                    $aBill[$sBillField] = $aBillDetail;
//                                }
//                            }
//
//                            $aBill[$sItem][$sCarLevel]['special_rate'] = $aBillInfo[$sItem][$sCarLevel];
//                            if (!empty($aBillInfo['estimate_id'])
//                                && !empty($aBill[$sItem][$sCarLevel]['special_rate'])
//                                && is_array($aBill[$sItem][$sCarLevel]['special_rate'])
//                            ) {
//                                $aBill[$sItem][$sCarLevel]['special_rate']['estimate_id'] = $aBillInfo['estimate_id'];
//                            }
//
//                            if (isset($aExtraInfo['is_default']) && !empty($aExtraInfo['is_default'])
//                                && !empty($aBill[$sItem][$sCarLevel]['special_rate'])
//                                && is_array($aBill[$sItem][$sCarLevel]['special_rate'])
//                            ) {
//                                $aBill[$sItem][$sCarLevel]['special_rate']['is_default']        = $aExtraInfo['is_default'];
//                                $aBill[$sItem][$sCarLevel]['special_rate']['user_status']       = $aExtraInfo['user_status'] ?? -1;
//                                $aBill[$sItem][$sCarLevel]['special_rate']['is_default_action'] = $aExtraInfo['is_default_action'] ?? -1;
//                            }
//
//                            if (!empty($aBill[$sItem][$sCarLevel]['special_rate'])
//                                && is_array($aBill[$sItem][$sCarLevel]['special_rate'])
//                                && $bShakeFlag
//                            ) {
//                                if (isset($aExtraInfo['shake_flag'])) {
//                                    $aBill[$sItem][$sCarLevel]['special_rate']['shake_flag'] = $aExtraInfo['shake_flag'];
//                                }
//
//                                if (isset($aExtraInfo['pre_estimate_fee'])) {
//                                    $aBill[$sItem][$sCarLevel]['special_rate']['pre_estimate_fee'] = $aExtraInfo['pre_estimate_fee'];
//                                }
//                            }
//                            break;
//                        default:
//                            foreach ($aBillInfo as $sBillField => $aBillDetail) {
//                                if (!in_array($sBillField, $aKeys)) {
//                                    $aBill[$sBillField] = $aBillDetail;
//                                }
//                            }
//
//                            $aBill[$sItem][$sCarLevel]['noncarpool'] = $aBillInfo[$sItem][$sCarLevel];
//                            if (!empty($aBillInfo['estimate_id'])
//                                && !empty($aBill[$sItem][$sCarLevel]['noncarpool'])
//                                && is_array($aBill[$sItem][$sCarLevel]['noncarpool'])
//                            ) {
//                                $aBill[$sItem][$sCarLevel]['noncarpool']['estimate_id'] = $aBillInfo['estimate_id'];
//                            }
//
//                            if (isset($aExtraInfo['is_default']) && !empty($aExtraInfo['is_default'])
//                                && !empty($aBill[$sItem][$sCarLevel]['noncarpool'])
//                                && is_array($aBill[$sItem][$sCarLevel]['noncarpool'])
//                            ) {
//                                $aBill[$sItem][$sCarLevel]['noncarpool']['is_default']        = $aExtraInfo['is_default'];
//                                $aBill[$sItem][$sCarLevel]['noncarpool']['user_status']       = $aExtraInfo['user_status'] ?? -1;
//                                $aBill[$sItem][$sCarLevel]['noncarpool']['is_default_action'] = $aExtraInfo['is_default_action'] ?? -1;
//                            }
//
//                            if (!empty($aBill[$sItem][$sCarLevel]['noncarpool'])
//                                && is_array($aBill[$sItem][$sCarLevel]['noncarpool'])
//                                && $bShakeFlag
//                            ) {
//                                if (isset($aExtraInfo['shake_flag'])) {
//                                    $aBill[$sItem][$sCarLevel]['noncarpool']['shake_flag'] = $aExtraInfo['shake_flag'];
//                                }
//
//                                if (isset($aExtraInfo['pre_estimate_fee'])) {
//                                    $aBill[$sItem][$sCarLevel]['noncarpool']['pre_estimate_fee'] = $aExtraInfo['pre_estimate_fee'];
//                                }
//                            }
//                            break;
//                    }
//                }
//            }
//
//            $aSpecialOrder = ['product_id' => $aBillInfo['product_infos'][$sCarLevel]['product_line_id'] ?? 0, 'require_level' => $sCarLevel, 'combo_type' => $iComboType];
//            if (Order::isSpecialRateV2($aSpecialOrder)) {
//                $iComboType = Constants\Horae::TYPE_COMBO_SPECIAL_RATE;
//            }
//
//            // 拼装运营活动和支付方式返回值
//            switch ($iComboType) {
//                case Constants\Horae::TYPE_COMBO_CARPOOL:
//                    //no break
//                case Constants\Horae::TYPE_COMBO_CARPOOL_INTER_CITY:
//                    //no break
//                case Constants\Horae::TYPE_COMBO_CARPOOL_FLAT_RATE:
//                    if (isset($aActivityInfo) && isset($aActivityInfo[0]) && !empty($aActivityInfo[0])) {
//                        $aActivity['carpool']      = $aActivityInfo;
//                        $aActivity['discount_fee'] = $aActivityInfo[0]['estimate_fee'];
//                        $aActivity['carpool_estimate_detail'] = array(
//                            'detail'        => $aActivityInfo[0]['estimate_detail'],
//                            'discount_desc' => $aActivityInfo[0]['discount_desc'] ?? [],
//                        );
//                        $aActivity['coupon_info']['carpool']  = $aActivityInfo[0]['coupon_info'] ?? [];
//                    }
//
//                    if (isset($aPaymentInfo) && !empty($aPaymentInfo)) {
//                        switch ($aPaymentInfo['pay_resp_type']) {
//                            case self::PAY_TYPE_NEW:
//                                if (isset($aPaymentInfo['user_pay_info']['carpool']) && is_array($aPaymentInfo['user_pay_info']['carpool'])) {
//                                    $aPayment['carpool'] = $aPaymentInfo['user_pay_info']['carpool'];
//                                    $bPaymentSeparate    = true;
//                                }
//                                break;
//                            default:
//                                if (isset($aPaymentInfo['user_pay_info'])) {
//                                    $aPayment = $aPaymentInfo['user_pay_info'];
//                                }
//                                break;
//                        }
//                    }
//                    break;
//                case Constants\Horae::TYPE_COMBO_SPECIAL_RATE:
//                    $aActivity['special_rate_estimate_fee']    = $aActivityInfo[0]['estimate_fee'];
//                    $aActivity['special_rate_estimate_detail'] = array(
//                        'detail'        => $aActivityInfo[0]['estimate_detail'],
//                        'discount_desc' => $aActivityInfo[0]['discount_desc'] ?? [],
//                    );
//                    $aActivity['coupon_info']['special_rate']  = $aActivityInfo[0]['coupon_info'] ?? [];
//                    switch ($aPaymentInfo['pay_resp_type']) {
//                        case self::PAY_TYPE_NEW:
//                            if (isset($aPaymentInfo['user_pay_info']['noncarpool']) && is_array($aPaymentInfo['user_pay_info']['noncarpool'])) {
//                                $aPayment['special_rate'] = $aPaymentInfo['user_pay_info']['noncarpool'];
//                                $bPaymentSeparate         = true;
//                            }
//                            break;
//                        case self::PAY_TYPE_BR:
//                            $aPayment['special_rate'] = $aPaymentInfo['user_pay_info'];
//                            foreach ($aPayment['special_rate']['busi_payments'] as &$item) {
//                                unset($item['business_config']);
//                            }
//
//                            $bPaymentSeparate = true;
//                            break;
//                        default:
//                            if (isset($aPaymentInfo['user_pay_info'])) {
//                                $aPayment = $aPaymentInfo['user_pay_info'];
//                            }
//                            break;
//                    }
//                    break;
//
//                default:
//                    // what a bad for-loop is...
//                    // have to re-write the special date in case it's set to empty
//                    $aSpecialRateFee    = $aActivity['special_rate_estimate_fee'] ?? 0;
//                    $aSpecialRateDetail = $aActivity['special_rate_estimate_detail'] ?? [];
//                    $aSpecialCouponInfo = $aActivity['coupon_info']['special_rate'] ?? [];
//
//                    $aActivity = $aActivityInfo[0];
//                    if (isset($aActivityInfo) && isset($aActivityInfo[0]) && !empty($aActivityInfo[0])) {
//                        $aActivity['estimate_fee'] = $aActivityInfo[0]['estimate_fee'];
//                        $aActivity['noncarpool_estimate_detail'] = array(
//                            'detail'        => $aActivityInfo[0]['estimate_detail'],
//                            'discount_desc' => $aActivityInfo[0]['discount_desc'] ?? [],
//                        );
//                        $aActivity['coupon_info']['noncarpool']  = $aActivityInfo[0]['coupon_info'] ?? [];
//                    }
//
//                    $aActivity['special_rate_estimate_fee']    = $aSpecialRateFee;
//                    $aActivity['special_rate_estimate_detail'] = $aSpecialRateDetail;
//                    $aActivity['coupon_info']['special_rate']  = $aSpecialCouponInfo;
//                    if (isset($aPaymentInfo) && !empty($aPaymentInfo)) {
//                        switch ($aPaymentInfo['pay_resp_type']) {
//                            case self::PAY_TYPE_NEW:
//                                if (isset($aPaymentInfo['user_pay_info']['noncarpool']) && is_array($aPaymentInfo['user_pay_info']['noncarpool'])) {
//                                    $aPayment['noncarpool'] = $aPaymentInfo['user_pay_info']['noncarpool'];
//                                    $bPaymentSeparate       = true;
//                                }
//                                break;
//                            case self::PAY_TYPE_BR:
//                                $aPayment['noncarpool'] = $aPaymentInfo['user_pay_info'];
//                                foreach ($aPayment['noncarpool']['busi_payments'] as &$item) {
//                                    unset($item['business_config']);
//                                }
//
//                                $bPaymentSeparate = true;
//                                break;
//                            default:
//                                if (isset($aPaymentInfo['user_pay_info'])) {
//                                    $aPayment = $aPaymentInfo['user_pay_info'];
//                                }
//                                break;
//                        }
//                    }
//                    break;
//            }
//        }
//
//        $aUserTypeInfo = array('user_pay_info' => $aPayment, 'payments_separate' => $bPaymentSeparate);
//
//        $aPriceItem['payments_info'] = json_encode($aUserTypeInfo, JSON_UNESCAPED_UNICODE);
//        $aPriceItem['bill_info']     = json_encode($aBill, JSON_UNESCAPED_UNICODE);
//        $aPriceItem['activity_info'] = json_encode($aActivity, JSON_UNESCAPED_UNICODE);
//
//        return $aPriceItem;
//    }

    /**
     * @param array $aPrice price
     * @return mixed
     */
    private function _buildPriceResponseV2($aPrice) {
//        $bShakeFlag       = false;
        $bPaymentSeparate = false;
        $aPayment         = null;
//        //shake flag
//        if (version_compare($this->_aParams[0]['common_info']['app_version'], self::SHAKE_APP_VERAION) >= 0) {
//            $bShakeFlag = true;
//        }

        $iComboType    = Constants\Horae::TYPE_COMBO_DEFAULT;
        $aActivityInfo = json_decode($aPrice['activity_info'], true);
        $aBillInfo     = json_decode($aPrice['bill_info'], true);
        $sCarLevel     = array_keys($aBillInfo['bills'])[0];

        // activity转换为list，背景是一个产品多价格下需要有多个activity信息。兼容上线过程中不一致
        if (isset($aActivityInfo) && !empty($aActivityInfo) && !is_array($aActivityInfo[0])) {
            $aActivityInfo = [$aActivityInfo];
        }

        $aPaymentInfo = json_decode($aPrice['payments_info'], true);
        $aExtraInfo   = $aPrice['extra_info'];

        if (isset($aBillInfo) && !empty($aBillInfo)) {
            $iComboType = $aBillInfo['product_infos'][$sCarLevel]['combo_type'];
            //和账单的交互后续用N元组标识产品，所以这里检查是否需要根据N元组将combo_type 做转换
            $iCheckCombo = $this->_checkOrderNTupleConvert($aBillInfo, $sCarLevel);
            if ($iCheckCombo) {
                $iComboType = $iCheckCombo;
            }

            //拼车顶导
            //TODO 这块逻辑应该可以放到后面MultiExecute里面去
            if (SceneParamsLogic::getInstance()->isFromCarpoolEntry()) {
                SceneResponseLogicV2::getInstance()->loadCarpoolEntryInfo($sCarLevel, $aBillInfo, $aExtraInfo);
            }
        }

        // 不再把extra_info里面的数据移动到bill_info里面
        // shake_flag字段在$abShakeFlag为false时需要unset掉，保持和老代码一样的逻辑
//        if (isset($aExtraInfo) && !empty($aExtraInfo)) {
//            if (!$bShakeFlag && isset($aExtraInfo['shake_flag'])) {
//                unset($aExtraInfo['shake_flag']);
//            }
//        }

        //支付方式返回值
        switch ($iComboType) {
            case Constants\Horae::TYPE_COMBO_CARPOOL:
                // no break
            case Constants\Horae::TYPE_COMBO_CARPOOL_INTER_CITY:
                // no break
            case Constants\Horae::TYPE_COMBO_CARPOOL_FLAT_RATE:
                if (isset($aPaymentInfo) && !empty($aPaymentInfo)) {
                    switch ($aPaymentInfo['pay_resp_type']) {
                        case self::PAY_TYPE_NEW:
                            if (isset($aPaymentInfo['user_pay_info']['carpool']) && is_array($aPaymentInfo['user_pay_info']['carpool'])) {
                                $aPayment         = $aPaymentInfo['user_pay_info']['carpool'];
                                $bPaymentSeparate = true;
                            }
                            break;
                        default:
                            if (isset($aPaymentInfo['user_pay_info'])) {
                                $aPayment = $aPaymentInfo['user_pay_info'];
                            }
                            break;
                    }
                }
                break;
            default:
                switch ($aPaymentInfo['pay_resp_type']) {
                    case self::PAY_TYPE_NEW:
                        if (isset($aPaymentInfo['user_pay_info']['noncarpool']) && is_array($aPaymentInfo['user_pay_info']['noncarpool'])) {
                            $aPayment         = $aPaymentInfo['user_pay_info']['noncarpool'];
                            $bPaymentSeparate = true;
                        }
                        break;
                    case self::PAY_TYPE_BR:
                        $aPayment = $aPaymentInfo['user_pay_info'];
                        foreach ($aPayment['busi_payments'] as &$item) {
                            unset($item['business_config']);
                        }

                        $bPaymentSeparate = true;
                        break;
                    default:
                        if (isset($aPaymentInfo['user_pay_info'])) {
                            $aPayment = $aPaymentInfo['user_pay_info'];
                        }
                        break;
                }
                break;
        }

        $aUserTypeInfo = array('user_pay_info' => $aPayment, 'payments_separate' => $bPaymentSeparate);

        $aPriceItem['payments_info'] = $aUserTypeInfo;
        $aPriceItem['bill_info']     = $aBillInfo;
        $aPriceItem['activity_info'] = $aActivityInfo;
        // 预估Response改造，新增price_extra字段
        $aPriceItem['price_extra'] = $aExtraInfo;
        return $aPriceItem;
    }

    /**
     * @param array $aPriceParams params
     *
     * @return array
     */
    private function _getMultiPriceParams($aPriceParams) {
        $aReqParams = [];
        foreach ($aPriceParams['price_params'] as $aParam) {
            $aReqParams[] = new \Dirpc\SDK\PriceApi\EstimatePriceReq($aParam);
        }

        $aReq = [
            'estimatePriceReqs' => $aReqParams,
            'caller'            => PriceApiClient::CLIENT_PASSENGER,
            'biz_data'          => json_encode($aReqParams),
            'extra_info'        => $aPriceParams['extra_info'],
        ];

        return $aReq;
    }

    /**
     * @param array  $aBillInfo bill
     * @param string $sCarLevel carlevel
     *
     * @return int
     */
    private function _checkOrderNTupleConvert($aBillInfo, $sCarLevel) {
        $iComboType = 0;
        if (!isset($aBillInfo['order_n_tuple_infos'][$sCarLevel]) || empty($aBillInfo['order_n_tuple_infos'][$sCarLevel])) {
            return $iComboType;
        }

        $aOrderNTuple = $aBillInfo['order_n_tuple_infos'][$sCarLevel];
        if (isset($aOrderNTuple['carpool_type']) && !empty($aOrderNTuple['carpool_type'])) {
            switch ($aOrderNTuple['carpool_type']) {
                case OrderNTuple::CARPOOL_TYPE_NORMAL:
                    //no break
                case OrderNTuple::CARPOOL_TYPE_LOW_PRICE:
                    //no break
                case OrderNTuple::CARPOOL_TYPE_STATION:
                    $iComboType = Constants\Horae::TYPE_COMBO_CARPOOL;
                    break;
                default:
                    break;
            }
        }

        if (isset($aOrderNTuple['is_special_price']) && !empty($aOrderNTuple['is_special_price'])) {
            if ($aOrderNTuple['is_special_price']) {
                $iComboType = Constants\Horae::TYPE_COMBO_SPECIAL_RATE;
            }
        }

        return $iComboType;
    }


    /**
     * 直接请求Price-api获取数据
     * @return array
     * @throws InvalidArgumentException InvalidArgumentException
     */
    public function getMultiResponseUseHTTP() {
        $aAthenaParams = array();
        $sAthenaParams = $this->_aParams[0]['common_info']['athena_params'];
        if (!empty($sAthenaParams)) {
            $aAthenaParams = json_decode($sAthenaParams, true);
            if (empty($aAthenaParams) || !is_array($aAthenaParams)) {
                throw new InvalidArgumentException(
                    ErrCode\Code::E_COMMON_PARAM_JSON_DECODE_FAIL
                );
            }
        }

        $this->_aAthenaRequest = $this->oParamsLogic->getMultiAthenaParams($aAthenaParams, $this->_aParams);
        list($aAthenaOriginRes, $sGuideResult, $aAthenaExtra) = $this->_getAthenaOriginResponse($this->_aAthenaRequest, 'http');
        $aResponseInfoV2 = $this->_buildResponseAndRequestV2($this->_aAthenaRequest, $aAthenaOriginRes, $sGuideResult, $aAthenaExtra);

        return $aResponseInfoV2;
    }
}
