<?php
/**
 * Created by PhpStorm.
 * 预估时营销气泡：商城套餐券
 * <AUTHOR> <<EMAIL>>
 * Date: 2019/9/20
 * Time: 11:34 AM
 */

namespace PreSale\Logics\estimatePriceV2;

use BizLib\Utils as BizUtils;
use BizLib\Constants\Horae;
use BizLib\Client\QuartzClient;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Log as NuwaLog;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\Language;
use PreSale\Infrastructure\Repository\Redis\UIComponent;
use PreSale\Domain\Service\UIComponent\EstimateSelect;
use Nuwa\ApolloSDK\Apollo as NuwaApollo;
use BizLib\Constants;

/**
 * Class MarkingBubbleLogic
 * @package PreSale\Logics\estimatePriceV2
 */
class MarkingBubbleLogic
{
    const TYPE_PACKAGE_COUPON_BUBBLE = 1008;

    protected static $_oInstance = null;
    private $_oQuartzClient      = null;
    private $_aRequestParams     = [];
    private $_aOrderInfo         = [];
    private $_aPackageCouponRes  = [];
    private $_aPackageCouponBubbleInfo = [];
    private $_aPackageCouponText       = [];

    /**
     * MarkingBubbleLogic constructor.
     */
    public function __construct() {
        $this->_oQuartzClient      = new QuartzClient();
        $this->_aPackageCouponText = NuwaConfig::text('config_text', 'package_coupon_bubble');
    }

    /**
     * @return null|MarkingBubbleLogic
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }


    /**
     * @param array $aParams 参数
     * @return mixed
     */
    public function setParams(array $aParams) {
        if (empty($this->_aRequestParams) && !empty($aParams)) {
            $this->_aRequestParams = $aParams;
        }

        return;
    }

    /**
     * @return bool
     */
    private function _checkParams() {
        //未登录用户，不获取套餐券
        if (empty($this->_aRequestParams['passenger_info']['pid'])) {
            return false;
        }

        if (empty($this->_aRequestParams['order_info']) || empty($this->_aRequestParams['pre_estimate_id'])) {
            return false;
        }

        if (!(Language::ZH_CN == Language::getLanguage() || (Language::EN_US == Language::getLanguage() && in_array($this->_aRequestParams['common_info']['access_key_id'], [Constants\Common::DIDI_WECHAT_MINI_PROGRAM, Constants\Common::DIDI_ALIPAY_MINI_PROGRAM])))) {
            return false;
        }

        if ($this->checkCarpoolDayHitDialog()) {
            return false;
        }

        $this->_aOrderInfo = $this->_aRequestParams['order_info'];
        $this->_aOrderInfo['business_id'] = $this->_aRequestParams['common_info']['business_id'];
        $aApollo       = new NuwaApollo();
        $featureToggle = $aApollo->featureToggle(
            'gs_support_marking_bubble',
            [
                'pid'         => $this->_aRequestParams['passenger_info']['pid'],
                'phone'       => $this->_aRequestParams['passenger_info']['phone'],
                'product_id'  => $this->_aOrderInfo['product_id'],
                'business_id' => $this->_aOrderInfo['business_id'],
                'combo_type'  => $this->_aOrderInfo['combo_type'],
                'car_level'   => $this->_aOrderInfo['require_level'],
            ]
        );

        if ($featureToggle->allow()) {
            return true;
        }

        return false;
    }

    /**
     * @return bool
     */
    public function checkCarpoolDayHitDialog() {
        $oComponentCache = new UIComponent();
        $aActivityType   = $oComponentCache->getActivityCache($this->_aRequestParams['estimate_trace_id']);

        if (!empty($aActivityType[EstimateSelect::CARPOOL_DAY_DIALOG_ACTIVITY])) {
            return true;
        }

        return false;
    }


    /**
     * @return array
     */
    public function getMarkingBubbleInfo() {
        if (!$this->_checkParams()) {
            return [];
        }

        $aReq = [
            'estimate_id' => $this->_aRequestParams['pre_estimate_id'],
        ];

        $aResult = $this->_oQuartzClient->estimateRec($aReq);
        if (0 != $aResult['errno']) {
            NuwaLog::warning(Msg::formatArray(Code::E_COMMON_REQ_FAIL, ['req' => json_encode($aReq), 'result' => json_encode($aResult)]));
            return [];
        }

        if (empty($aResult['data'])) {
            return [];
        }

        $this->_aPackageCouponRes = $aResult['data'];
        $this->_buildPackageCouponBubbleInfo();
        return $this->_aPackageCouponBubbleInfo;
    }


    /**
     * @return mixed
     */
    private function _buildPackageCouponBubbleInfo() {
        if (empty($this->_aPackageCouponRes['title']) || empty($this->_aPackageCouponRes['url'])) {
            return;
        }

        $aShowText = [
            'show_type'            => self::TYPE_PACKAGE_COUPON_BUBBLE,
            'show_url'             => $this->_aPackageCouponText['show_url'] ?? '',
            'title'                => $this->_aPackageCouponRes['title'],//文案
            'text '                => $this->_aPackageCouponText['text'] ?? '',
            'cancel_button_title'  => $this->_aPackageCouponText['cancel_button_title'] ?? '',
            'confirm_button_title' => $this->_aPackageCouponRes['button'] ?? $this->_aPackageCouponText['confirm_button_title'],
            'confirm_button_url'   => $this->_aPackageCouponRes['url'],//购买链接
            'action'               => $this->_aPackageCouponText['action'] ?? null,
            'count_down'           => $this->_aPackageCouponText['count_down'] ?? '',
            'valid_time'           => $this->_aPackageCouponText['valid_time'] ?? '',
            'extra'                => $this->_aPackageCouponText['extra'] ?? [],
        ];

        $aFromItem = [
            'source_product'    => (int)($this->_aOrderInfo['business_id']),
            'source_level'      => (int)($this->_aOrderInfo['require_level']),
            'source_combo_type' => (int)($this->_aOrderInfo['combo_type']),
        ];

        $aFrom = [$aFromItem,];

        $aTo = array(
            'guide_product'    => (int)($this->_aOrderInfo['business_id']),
            'guide_level'      => (int)($this->_aOrderInfo['require_level']),
            'guide_combo_type' => (int)($this->_aOrderInfo['combo_type']),
            'guide_action'     => 0,
            'tab_switch'       => 0,
            'default_show'     => 0,
        );

        $aGuideInfo = array(
            'show_text' => $aShowText,
            'from'      => $aFrom,
            'to'        => $aTo,
        );

        $aFormatGuideInfo = [
            'guide' => [$aGuideInfo,],
        ];

        $this->_aPackageCouponBubbleInfo = array(
            'errno'  => 0,
            'errmsg' => 'ok',
            'data'   => $aFormatGuideInfo,
        );

        return;
    }
}
