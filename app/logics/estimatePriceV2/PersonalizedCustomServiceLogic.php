<?php

namespace PreSale\Logics\estimatePriceV2;

use Biz<PERSON>ommon\Constants\OrderNTuple;
use BizLib\Client\SsseClient;
use BizLib\Config as NuwaConfig;
use BizLib\Constants\Common;
use BizLib\Constants\Common as ConstantsCommon;
use BizLib\Constants\Horae;
use BizLib\Utils\Product as ProductConst;
use BizLib\Log;
use BizLib\Utils\Horae as HoraeUtils;
use BizLib\Utils\ProductCategory;
use BizLib\Utils\Registry;
use Dirpc\SDK\PreSale\LuxMultiEstimatePriceRequest as LuxRequest;
use Dirpc\SDK\PreSale\MultiEstimatePriceRequest as Request;
use Dirpc\SDK\Ssse;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\estimatePriceV2\multiRequest\AreaInfo;
use PreSale\Logics\estimatePriceV2\multiRequest\CommonInfo;
use PreSale\Logics\estimatePriceV2\multiRequest\Product;
use PreSale\Logics\scene\custom\CustomLogic;
use PreSale\Logics\taxi\TaxiPeakFee;
use PreSale\Models\fence\FenceInfo;
use BizLib\Utils\Product as UtilsProduct;

/**
 * Class PersonalizedCustomServiceLogic
 * @package PreSale\Logics\estimatePriceV2
 */
class PersonalizedCustomServiceLogic
{

    private static $_oInstance   = null;
    private $_aSsseCustomService = []; // 从 ssse 中请求到的个性化服务
    private $_aSceneData         = [];

    private $_oSsseClient          = null;
    private $_aBaseProductList     = [];
    private $_aChooseCustomService = []; // 用来承载端上上传的用户选择的个性化服务

    private $_aMemberInfo = [];

    const ORIGIN_PRICE_TEXT = '%s%s';

    /**
     * PersonalizedCustomServiceLogic constructor.
     */
    private function __construct() {
        $this->_oSsseClient = new SsseClient();
    }

    /**
     * @return PersonalizedCustomServiceLogic|null
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * @param array      $aBaseProductList 品类列表
     * @param CommonInfo $oCommonInfo      CommonInfo对象
     * @param array      $aPassengerInfo   $aPassengerInfo
     * @param AreaInfo   $oAreaInfo        $oAreaInfo
     * @return void
     */
    public function buildChooseCustomServiceV2($aBaseProductList, CommonInfo $oCommonInfo, $aPassengerInfo, $oAreaInfo) {
        $this->_aBaseProductList = $aBaseProductList;
        $aCommonCustomService    = [];
        $iPageType = $oCommonInfo->iPageType;
        switch ($iPageType) {
            case Horae::PAGE_TYPE_NO_OBSTACLE:
                $aCommonCustomService[] = ['id' => CustomLogic::CUSTOM_SERVICE_DISABLED, 'count' => 1];
                break;
            case Horae::PAGE_TYPE_PET_CAR:
                $aApolloParams = [
                    'key'           => $aPassengerInfo['pid'],
                    'uid'           => $aPassengerInfo['uid'],
                    'pid'           => $aPassengerInfo['pid'],
                    'phone'         => $aPassengerInfo['phone'],
                    'app_version'   => $oCommonInfo->sAppVersion,
                    'city'          => $oAreaInfo->iArea,
                    'access_key_id' => $oCommonInfo->iAccessKeyID,
                    'caller'        => 'pre-sale',
                ];
                $bAllow        = (new Apollo())->featureToggle(
                    'gs_five_star_premier_toggle',
                    $aApolloParams
                )->allow();
                if ($bAllow) {
                    $aCommonCustomService[] = ['id' => CustomLogic::CUSTOM_SERVICE_FIVE_STAR, 'count' => 1];
                } else {
                    $aCommonCustomService[] = ['id' => CustomLogic::CUSTOM_SERVICE_PET_CAR, 'count' => 1];
                }
                break;
            case 37:
                $aCommonCustomService[] = ['id' => CustomLogic::CUSTOM_SERVICE_FIVE_STAR, 'count' => 1];
                break;
            default:
                break;
        }

        if (!empty($aCommonCustomService)) {
            foreach ($aCommonCustomService as &$aItem) {
                $aItem['skip_check'] = 1;
            }
        }

        $aMultiInfo         = $oCommonInfo->aMultiRequireProducts;
        $aProductCategories = NuwaConfig::getBizConfig('config_product_category_map', 'product_category');
        foreach ($aBaseProductList as $iIndex => $aProduct) {
            $aOptionCusSvc = $aCommonCustomService;
            if (!empty($aMultiInfo)) {
                foreach ($aMultiInfo as $aReq) {
                    if (!empty($aReq['product_category'])) {
                        $aMap = $aProductCategories[(int)($aReq['product_category'])];
                        if ($aMap['business_id'] == $aProduct['business_id']
                            && $aMap['require_level'] == $aProduct['require_level']
                            && $aMap['is_special_price'] == $aProduct['is_special_price']
                            && $aMap['level_type'] == $aProduct['level_type']
                        ) {
                            // 个性化参数
                            $sCustomFeatures = is_string($aReq['custom_feature']) ? $aReq['custom_feature'] : null;
                            $aCustomFeatures = json_decode($sCustomFeatures, true);
                            if (!empty($aCustomFeatures) && is_array($aCustomFeatures)) {
                                $aOptionCusSvc = array_merge($aOptionCusSvc, $aCustomFeatures);
                            }

                            break;
                        }
                    }
                }
            }

            $this->_aChooseCustomService[] = $aOptionCusSvc;
        }
    }

    /**
     * PGetTailorService 二级偏好页
     * @param LuxRequest $oEstimateRequest $oEstimateRequest
     * @param array      $aPassengerInfo   $aPassengerInfo
     * @param array      $aAreaInfo        $aAreaInfo
     * @return void
     */
    public function loadCustomServiceSecondaryPage($oEstimateRequest, $aPassengerInfo, $aAreaInfo) {
        $aApolloParams = [
            'key'           => $aPassengerInfo['pid'],
            'city'          => $aAreaInfo['id'] ?? 0,
            'phone'         => $aPassengerInfo['phone'],
            'page_type'     => $oEstimateRequest->getPageType(),
            'app_version'   => $oEstimateRequest->getAppVersion(),
            'menu_id'       => $oEstimateRequest->getMenuId(),
            'access_key_id' => $oEstimateRequest->getAccessKeyId(),
        ];
        $oToggle       = (new Apollo())->featureToggle('gs_secondary_page_support_custom_service_switch', $aApolloParams);
        if (!$oToggle->allow()) {
            return;
        }

        $aCommonRequestParam = PersonalizedServiceLogicV2::buildSsseCommonReq($oEstimateRequest, $aPassengerInfo, $aAreaInfo);
        // 构建品类列表
        $aBaseProductList[] = [
            'business_id'   => $oEstimateRequest->getBusinessId(),
            'combo_type'    => PersonalizedServiceLogicV2::getComboType($oEstimateRequest->getAirportType()),
            'require_level' => $oEstimateRequest->getRequireLevel(),
        ];
        $this->_getService(
            $aBaseProductList,
            $aCommonRequestParam,
            $aAreaInfo['id'],
            $aPassengerInfo['phone'],
            $oEstimateRequest->getMenuId()
        );

        // 偏好设置页旧入口需屏蔽增值服务
        $aServiceIds = json_decode($oToggle->getParameter('filter_service_ids', ''), true);
        if (!empty($aServiceIds) && !empty($this->_aSsseCustomService[0])) {
            foreach ($this->_aSsseCustomService[0] as $iIndex => $aItem) {
                if (in_array($aItem['service_id'], $aServiceIds)) {
                    unset($this->_aSsseCustomService[0][$iIndex]);
                }
            }
        }
    }

    /**
     * 构建请求sps需要的公共参数
     * @param LuxRequest $oEstimateRequest $oEstimateRequest
     * @return int
     */
    private function _getProductCategory($oEstimateRequest) {
        if (!$oEstimateRequest->getProductCategory()) {
            $aNTuple          = [
                'business_id'   => $oEstimateRequest->getBusinessId(),
                'require_level' => $oEstimateRequest->getRequireLevel(),
                'combo_type'    => PersonalizedServiceLogicV2::getComboType($oEstimateRequest->getAirportType()),
            ];
            $iProductCategory = (new ProductCategory())->getProductCategoryByNTuple($aNTuple);
            if (in_array($iProductCategory, [ProductCategory::PRODUCT_CATEGORY_PREMIUM_COMFORT_PICKUP, ProductCategory::PRODUCT_CATEGORY_PREMIUM_COMFORT_DROPOFF])) {
                return ProductCategory::PRODUCT_CATEGORY_PREMIUM_COMFORT;
            } elseif (in_array($iProductCategory, [ProductCategory::PRODUCT_CATEGORY_PREMIUM_BUSINESS_PICKUP, ProductCategory::PRODUCT_CATEGORY_PREMIUM_BUSINESS_DROPOFF])) {
                return ProductCategory::PRODUCT_CATEGORY_PREMIUM_BUSINESS;
            }
        }

        return $oEstimateRequest->getProductCategory();
    }

    /**
     * 构建二级偏好设置页透出的个性化增值服务
     * @param array      $aCustomFeatures  $aCustomFeatures
     * @param LuxRequest $oEstimateRequest $oEstimateRequest
     * @param array      $aPassengerInfo   $aPassengerInfo
     * @param array      $aAreaInfo        $aAreaInfo
     * @return array
     * @throws \Exception e
     */
    public function buildSsseCustomService($aCustomFeatures, $oEstimateRequest, $aPassengerInfo, $aAreaInfo) {
        if (empty($this->_aSsseCustomService)) {
            return [];
        }

        if ($aCustomFeatures == null) {
            $aCustomFeatures = [];
        }

        //是否命中个性化服务新链路
        $bIsHitNew = $this->_isNewSpsPrice($oEstimateRequest,$aPassengerInfo,$aAreaInfo);

        $aCommonParam   = [
            'business_id' => $oEstimateRequest->getBusinessId(),
            'page_type'   => $oEstimateRequest->getPageType(),
            'menu_id'     => $oEstimateRequest->getMenuId(),
        ];

        $aFormatService = $this->_formatServiceData($aCommonParam, $this->_aSsseCustomService[0], $bIsHitNew, $aCustomFeatures);

	    // 获取会员权益数据
        $this->_aMemberInfo = PersonalizedServiceLogicV2::getMemberInfoRPC(
            $oEstimateRequest->getBusinessId(),
            $aPassengerInfo['pid'],
            $oEstimateRequest->getLang(),
            $aAreaInfo
        );

        // 构建请求sps的数据
        $aSpsAddServiceReqs = [];
        // todo android端入参没有product_category，先使用配置转换
        $iProductCategory   = $this->_getProductCategory($oEstimateRequest);
        $aSpsAddServiceReqs = $this->buildSpsAddServiceReqs( // v1 tailor service
            $aFormatService,
            PersonalizedServiceLogicV2::getSpsCommonParams($oEstimateRequest, $aPassengerInfo, $aAreaInfo, $iProductCategory),
            $this->_aMemberInfo,
            $aSpsAddServiceReqs
        );
	    // sps的返回值
        SpsAddServicePriceLogic::getInstance()->initAddServiceFeeInfo($aSpsAddServiceReqs);
        SpsAddServicePriceLogic::getInstance()->addSsseCustomServicePrice($aFormatService, $iProductCategory);
        $aConfig = NuwaConfig::text('scene_data','head');
        if (!empty($aFormatService) && !empty($aConfig)) {
             return [
                 'custom_info'      => $aFormatService,
                 'custom_head'      => $aConfig['head'],
                 'custom_head_link' => $aConfig['link'],
             ];
        }

        return [];
    }


    /**
     * 是否命中个性化服务价格新链路 （目前只有豪华车-机场助理）
     * @param LuxRequest $oEstimateRequest $oEstimateRequest
     * @param array      $aPassengerInfo   $aPassengerInfo
     * @param array      $aAreaInfo        $aAreaInfo
     * @return bool
     */
    private function _isNewSpsPrice($oEstimateRequest, $aPassengerInfo, $aAreaInfo) {
        $oApollo     = new Apollo();
        $bIsOpenSsse = $oApollo->featureToggle(
            'gs_ssse_service_new_price',
            [
                'caller'           => 'pre-sale',
                'key'              => $aPassengerInfo['pid'],
                'phone'            => $aPassengerInfo['phone'],
                'app_version'      => $oEstimateRequest->getAppVersion(),
                'access_key_id'    => $oEstimateRequest->getAccessKeyId(),
                'business_id'      => $oEstimateRequest->getBusinessId(),
                'product_category' => $oEstimateRequest->getProductCategory(),
                'page_type'        => $oEstimateRequest->getPageType(),
                'city'             => $aAreaInfo['id'],
            ]
        )->allow();

        return $bIsOpenSsse;
    }

    /**
     * 主预估流程
     * @param array    $aBaseProductList $aBaseProductList
     * @param array    $aPassengerInfo   $aPassengerInfo
     * @param AreaInfo $oAreaInfo        $oAreaInfo
     * @param Request  $oEstimateRequest $oEstimateRequest
     * @return void
     */
    public function loadCustomServiceDacheAnycar($aBaseProductList, $aPassengerInfo, AreaInfo $oAreaInfo, Request $oEstimateRequest) {
        $aBaseProduct = [];
        foreach ($aBaseProductList as $aProduct) {
            if (isset($aProduct['order_info']['airport_info'])) {
                $aBaseProduct = $aProduct;
                break;
            }
        }

        if (empty($aBaseProduct)) {
            $aBaseProduct = current($aBaseProductList);
        }

        // 6.0场景入口是否支持个性化服务露出
        $oApollo = new Apollo();
        if (!$oApollo->featureToggle(
            'gs_support_custom_service_switch',
            array(
                'key'         => $aPassengerInfo['pid'],
                'city'        => $oAreaInfo->iArea,
                'phone'       => $aPassengerInfo['phone'],
                'page_type'   => $oEstimateRequest->getPageType(),
                'app_version' => $oEstimateRequest->getAppVersion(),
                'menu_id'     => $oEstimateRequest->getMenuId(),
            )
        )->allow()
        ) {
            return;
        }

        $aCommonRequestParam = $this->_getCommonRequestParamsDacheAnycar($aBaseProduct, $aPassengerInfo, $oAreaInfo, $oEstimateRequest);
        $this->_getService(
            $aBaseProductList,
            $aCommonRequestParam,
            $oAreaInfo->iArea,
            $aPassengerInfo['phone'],
            $oEstimateRequest->getMenuId()
        );

        foreach ($this->_aSsseCustomService as $iIndex => $aItem) {
            $iServiceId = $aItem[0]['service_id'] ?: 0;

            //针对出租车峰期加价需要小程序旧表单兼容增值服务，但是其他品类不兼容。所以把其他品类的增值服务去掉
            if (ConstantsCommon::DIDI_WECHAT_MINI_PROGRAM == $aCommonRequestParam['access_key_id']) {
                if (CustomLogic::CUSTOM_SERVICE_TAXI_PEAK != $iServiceId) {
                    $this->_aSsseCustomService[$iIndex] = [];
                }
            }
        }
    }

    /**
     * @param array    $aBaseProduct     $aBaseProduct
     * @param array    $aPassengerInfo   $aPassengerInfo
     * @param AreaInfo $oAreaInfo        $oAreaInfo
     * @param Request  $oEstimateRequest $oEstimateRequest
     * @return mixed
     */
    private function _getCommonRequestParamsDacheAnycar($aBaseProduct, $aPassengerInfo, AreaInfo $oAreaInfo, Request $oEstimateRequest) {
        $aParam['pid']            = (int)$aPassengerInfo['pid'];
        $aParam['phone']          = $aPassengerInfo['phone'];
        $aParam['type']           = (int)$aBaseProduct['order_type'];
        $aParam['area']           = $oAreaInfo->iArea;
        $aParam['flat']           = (double)$oAreaInfo->fFromLat;
        $aParam['flng']           = (double)$oAreaInfo->fFromLng;
        $aParam['tlat']           = (double)$oAreaInfo->fToLat;
        $aParam['tlng']           = (double)$oAreaInfo->fToLng;
        $aParam['call_car_type']  = $oEstimateRequest->getCallCarType();
        $aParam['departure_time'] = empty($oEstimateRequest->getDepartureTime()) ? time() : $oEstimateRequest->getDepartureTime();
        $aParam['menu_id']        = $oEstimateRequest->getMenuId();
        $aParam['sub_menu_id']    = '';
        $aParam['app_version']    = $oEstimateRequest->getAppVersion();
        $aParam['lang']           = $oEstimateRequest->getLang();
        $aParam['traffic_number'] = $oEstimateRequest->getTrafficNumber();
        $aParam['airport_id']     = $oEstimateRequest->getAirportId();
        $aParam['flight_dep_code']     = $oEstimateRequest->getFlightDepCode();
        $aParam['flight_dep_terminal'] = $oEstimateRequest->getFlightDepTerminal();
        $aParam['traffic_dep_time']    = $oEstimateRequest->getTrafficDepTime();
        $aParam['page_type']           = $oEstimateRequest->getPageType();
        $aParam['access_key_id']       = $oEstimateRequest->getAccessKeyId();

        //如果端上上传airport_id为空，使用围栏获取到的airport_id
        if (empty($aParam['airport_id']) && isset($aBaseProduct['order_info']['airport_info']['airport_id'])) {
            $aParam['airport_id'] = (int)$aBaseProduct['order_info']['airport_info']['airport_id'];
        }

        return $aParam;
    }

    /**
     * @param int     $iIndex   $iIndex
     * @param Product $oProduct $oProduct
     * @return void
     */
    public function buildAndCheckCustomServiceOptionsDacheAnycar($iIndex, Product &$oProduct) {
        $aOptionCusSvc = $this->_aChooseCustomService[$iIndex];
        $aPersonalized = new \ArrayObject();
        if (!empty($aOptionCusSvc)) {
            foreach ($aOptionCusSvc as $index => $item) {
                // 做用户选择个性化服务校验参数
                if (!empty($item['skip_check'])) {
                    $aPersonalized[$item['id']] = array('count' => $item['count']);
                } else {
                    if (!empty($this->_aSsseCustomService[$iIndex])) {
                        foreach ($this->_aSsseCustomService[$iIndex] as $aBaseCusSvc) {
                            if ($aBaseCusSvc['service_id'] == $item['id']) {
                                $aPersonalized[$item['id']] = array('count' => $item['count']);
                            }
                        }
                    }
                }
            }
        }

        $oProduct->oOrderInfo->aPersonalizedCustomOption = $aPersonalized;
        // $aProduct['custom_service_info']：个性化服务请求账单老参数，个性化服务已全部切到 sps 不需要再传内容，但这个字段可能承载其他价格项，所以这里对此字段不做改动
        if (empty($oProduct->aCustomFeature)) {
            $oProduct->aCustomFeature = [];
        }
    }

    /**
     * @param array  $aBaseProductList    原始oneconf
     * @param array  $aCommonRequestParam 获取个性化服务公共参数
     * @param int    $iFromArea           $iFromArea
     * @param string $sPhone              $sPhone
     * @param string $sMenuId             $sMenuId
     * @param int    $iDegradeType        $iDegradeType
     * @return void
     */
    private function _getService($aBaseProductList, $aCommonRequestParam, $iFromArea, $sPhone, $sMenuId = '', $iDegradeType = 0) {
        // ssse 降级开关
        $oApollo     = new Apollo();
        $bIsOpenSsse = $oApollo->featureToggle(
            'gs_ssse_flow_degrade_switch',
            [
                'city'  => $iFromArea,
                'phone' => $sPhone,
            ]
        )->allow();
        if ((1 == $iDegradeType || Common::MENU_DACHE_ANYCAR == $sMenuId)
            && $bIsOpenSsse
        ) {
            $oReqParams = $this->_getSsseReqParams($aBaseProductList, $aCommonRequestParam);
            $aRet       = $this->_oSsseClient->getService($oReqParams);
            if (0 != $aRet['errno'] || !isset($aRet['errno'])) {
                return;
            } else {
                $this->_aSsseCustomService = $aRet['data'];
            }
        }
    }

    /**
     * @param array $aBaseProductList    $aBaseProductList
     * @param array $aCommonRequestParam $aCommonRequestParam
     * @return Ssse\GetMultiServiceReq
     */
    private function _getSsseReqParams($aBaseProductList, $aCommonRequestParam) {
        $aReqs       = [];
        $oSsseParams = new Ssse\GetMultiServiceReq();
        foreach ($aBaseProductList as $aOneConfItem) {
            $aParam['business_id']   = (int)($aOneConfItem['business_id']);
            $aParam['combo_type']    = (int)($aOneConfItem['combo_type']);
            $aParam['require_level'] = (string)($aOneConfItem['require_level']);
            $aParam += $aCommonRequestParam;

            $oReq    = new Ssse\GetServiceReq($aParam);
            $aReqs[] = $oReq;
        }

        $oSsseParams->setCaller(MODULE_NAME);
        $oSsseParams->setReqs($aReqs);
        return $oSsseParams;
    }

    /**
     * 构建请求sps需要的公共参数
     * @param array $aParamItem $aParamItem
     * @param array $aProduct   $aProduct
     * @return mixed
     */
    private function _getSpsCommonParams($aParamItem, $aProduct) {
        $aParam['passenger_id'] = $aParamItem['passenger_info']['pid'];
        $aParam['city']         = $aParamItem['order_info']['area'];
        $aParam['product_id']   = $aProduct['product_id'];
        $aParam['car_level']    = $aProduct['require_level'];
        $aParam['combo_type']   = $aProduct['combo_type'];
        $aParam['product_category'] = $aProduct['product_category'];
        $aParam['departure_time']   = $aParamItem['order_info']['departure_time'];
        $aParam['app_version'] = $aParamItem['common_info']['app_version'];
        $aParam['access_key_id'] = $aParamItem['common_info']['access_key_id'];
        // 预约单切换为实时单时，会传departure_time，实时单且没有传时，修正该字段
        if (empty($aParam['departure_time']) && empty($aParamItem['order_info']['order_type'])) {
            $aParam['departure_time'] = time();
        }

        return $aParam;
    }

    /**
     * 个性化服务 -> 端上展示需求
     * @param array $aInfos $aInfos
     * @return void
     * @throws \Exception e
     */
    public function formatSsseCustomService($aInfos) {
        if (empty($this->_aBaseProductList)) {
            return;
        }

        //是否命中个性化服务新链路
        $oApollo   = new Apollo();
        $bIsHitNew = $oApollo->featureToggle(
            'gs_ssse_service_new_price',
            [
                'caller'        => 'pre-sale',
                'key'           => $aInfos[0]['passenger_info']['pid'],
                'phone'         => $aInfos[0]['passenger_info']['phone'],
                'app_version'   => $aInfos[0]['common_info']['app_version'],
                'access_key_id' => $aInfos[0]['common_info']['access_key_id'],
                'city'          => $aInfos[0]['order_info']['area'],
            ]
        )->allow();

        $aSpsAddServiceReqs = [];

        $aServiceList = [];
        foreach ($this->_aBaseProductList as $iIndex => $aProduct) {
            $aOptionCusSvc = [];
            foreach ($aInfos as $item) {
                $nTuple = $item['order_info']['n_tuple'];
                if ($nTuple['product_id'] == $aProduct['product_id']
                    && $nTuple['require_level'] == $aProduct['require_level']
                ) {
                    $aOptionCusSvc = $item['custom_service_info'];
                }
            }

            $aParamItem   = current($aInfos);
            $aCommonParam = [
                'business_id' => $aParamItem['order_info']['n_tuple']['product_id'],
                'page_type'   => $aParamItem['common_info']['page_type'],
                'menu_id'     => $aParamItem['order_info']['menu_id'],
            ];

            // 增值服务
            $aFormatService = $this->_formatServiceData(
                $aCommonParam,
                $this->_aSsseCustomService[$iIndex],
                $bIsHitNew,
                $aOptionCusSvc
            );

            // 会员信息
            $sKey           = "memberInfo_{$aProduct['product_id']}_{$aParamItem['passenger_info']['pid']}_{$aParamItem['common_info']['lang']}";
            $aMemberProfile = Registry::getInstance()->get($sKey);
            $aSpsAddServiceReqs = $this->buildSpsAddServiceReqs( // 车型下方展示
                $aFormatService,
                $this->_getSpsCommonParams($aParamItem, $aProduct),
                $aMemberProfile,
                $aSpsAddServiceReqs,
                $aParamItem
            );
            $aServiceList[]     = $aFormatService;
        }

        SpsAddServicePriceLogic::getInstance()->initAddServiceFeeInfo($aSpsAddServiceReqs);
        foreach ($this->_aBaseProductList as $iIndex => $aProduct) {
            if (empty($aServiceList[$iIndex]) || !is_array($aServiceList[$iIndex])) {
                continue;
            }

            SpsAddServicePriceLogic::getInstance()->addSsseCustomServicePrice($aServiceList[$iIndex], $aProduct['product_category']);
        }

        $this->_aSceneData = $aServiceList;
    }

    /**
     * 提供接口获取构造后的 scene_data
     * @return array
     */
    public function getSceneData() {
        return $this->_aSceneData;
    }

    /**
     * @param array $aCommonParam $aCommonParam
     * @param array $aServiceInfo $aServiceInfo
     * @param array $aChooseList  $aChooseList
     * @return array
     */
    private function _formatServiceData($aCommonParam, $aServiceInfo, $bIsHitNew, $aChooseList = []) {
        $aFormatService = [];
        $aChooseList    = array_combine(array_column($aChooseList, 'id'), $aChooseList);
        if (empty($aServiceInfo)) {
            return [];
        }

        $oToggle           = (new Apollo())->featureToggle(
            'gs_secondary_page_support_custom_service_switch',
            ['form' => '6.0',]
        );
        $aFilterServiceIds = [];
        if ($oToggle->allow()) {
            // 偏好设置页旧入口需屏蔽增值服务
            $aFilterServiceIds = json_decode($oToggle->getParameter('filter_service_ids', ''), true);
        }

        foreach ($aServiceInfo as $item) {
            if ($bIsHitNew) {
                //命中个性化服务新链路,会收费
                if (CustomLogic::CUSTOM_SERVICE_GUIDE_NEW == $item['service_id'] && ProductConst::COMMON_PRODUCT_ID_FIRST_CLASS_CAR == $aCommonParam['business_id']) {
                    //豪华车 机场助理收费后在老版本不再展示
                    continue;
                }
            }

            $aCustomServiceItem = [];
            if (!$this->_checkService($item)) {
                continue;
            }

            // hack 由于ssse不识别新旧表单, 需要手动屏蔽  (待旧表单下线 此处就无调用)
            if (!empty($aFilterServiceIds) && in_array($item['service_id'], $aFilterServiceIds)) {
                continue;
            }

            $count          = 1; // 展示的数量，未选择情况下默认展示为1
            $iSelectedCount = 0; // 已选择的数量，默认为0
            if (key_exists($item['service_id'], $aChooseList)) {
                $count          = (int)($aChooseList[$item['service_id']]['count']);
                $iSelectedCount = $count;
            }

            $aCustomServiceItem['id']           = $item['service_id'];
            $aCustomServiceItem['service_id']   = $item['service_id'];
            $aCustomServiceItem['product_id']   = $item['product_id'] ?? \BizLib\Utils\Product::PRODUCT_ID_DEFAULT;
            $aCustomServiceItem['mutex_ids']    = $item['mutex_ids'] ?? [];
            $aCustomServiceItem['max']          = $item['max'];
            $aCustomServiceItem['title']        = $item['title'];
            $aCustomServiceItem['desc']         = $item['desc'] ?? '';
            $aCustomServiceItem['price']        = (int)($item['price']) > 0 ? ((string)($item['price'] * $count) . $item['currency']) : '';
            $aCustomServiceItem['origin_price'] = ''; //sprintf(self::ORIGIN_PRICE_TEXT, $item['origin_price'] * $count, $item['currency']);
            $aCustomServiceItem['unit']         = $item['unit'];
            $aCustomServiceItem['num']          = $count . $item['unit'];
            $aCustomServiceItem['selected_count'] = $iSelectedCount;
            $aCustomServiceItem['detail']         = $item['detail'] ?? '';
            $aCustomServiceItem['status']         = $item['status'];
            $aCustomServiceItem['tips']           = $item['tips'] ?? '';
            $aCustomServiceItem['icon']           = $item['icon'] ?? '';
            $aCustomServiceItem['light_icon']     = $item['light_icon'] ?? '';
            $aCustomServiceItem['num_selector_title']    = $item['num_selector_title'] ?? '';
            $aCustomServiceItem['num_selector_subtitle'] = $item['num_selector_subtitle'] ?? '';
            $aCustomServiceItem['popup'] = $item['popup'] ?? new \ArrayObject();
            // 接机员上线比cip早一些，端上没有拼接机员的unit值；cip端上是有拼 unit 值的，所以不需要走下面逻辑
            if (CustomLogic::CUSTOM_SERVICE_GUIDE_NEW == $item['service_id'] && !HoraeUtils::isAirportPageType($aCommonParam['page_type'])) {
                $aCustomServiceItem['price']        = ((string)($item['price'] * $count) . $item['currency']) . '/' . $item['unit'];
                $aCustomServiceItem['origin_price'] = ''; //sprintf(self::ORIGIN_PRICE_TEXT, $item['origin_price'] * $count, $item['currency']) . '/' . $item['unit'];
            }

            // 不管上面怎么变，6.0统一走新逻辑；6.0 返回给端上的文案中的数字需要加"{}"，增加新字段 origin_price
            // origin_price去掉原价（划线价）
            if (Common::MENU_DACHE_ANYCAR == $aCommonParam['menu_id']) {
                $aCustomServiceItem['desc']         = $item['desc'] ? $this->_getDesc($item['desc']) : '';
                $aCustomServiceItem['price']        = $item['price'] > 0 ? sprintf('{%s}%s', $item['price'] * $count, $item['currency']) : '{0}'.$item['currency'];
                $aCustomServiceItem['unit_price']   = $item['price'] > 0 ? sprintf('{%s}%s', $item['price'], $item['currency']) : '{0}'.$item['currency']; // 单价
                $aCustomServiceItem['origin_price'] = ''; //sprintf('{%s}%s', $item['origin_price'] * $count, $item['currency']);
                $aCustomServiceItem['origin_unit_price'] = sprintf('{%s}%s', $item['origin_price'], $item['currency']);
            }

            $aFormatService[] = $aCustomServiceItem;
        }

        if (empty($aFormatService)) {
            return $aFormatService;
        }

        $aServiceMap = array_combine(array_column($aFormatService, 'id'), $aFormatService);
        //补全互斥服务id
        foreach ($aServiceMap as $index => $item) {
            foreach ($item['mutex_ids'] as $id) {
                if (!isset($aServiceMap[$id])) {
                    continue;
                }

                if (!in_array($item['id'], $aServiceMap[$id]['mutex_ids'])) {
                    $aServiceMap[$id]['mutex_ids'][] = $item['id'];
                }
            }
        }

        return array_values($aServiceMap);
    }

    /**
     * @param string $sDesc $sDesc
     * @return string
     */
    private function _getDesc($sDesc) {
        $len        = mb_strlen($sDesc, 'UTF-8');
        $bHasNumber = false;
        $aDesc      = [];
        for ($iIde = 0; $iIde < $len; $iIde++) {
            $aDesc[] = mb_substr($sDesc, $iIde, 1, 'UTF-8');
        }

        foreach ($aDesc as $iIndex => &$sItem) {
            if (is_numeric($sItem) || '.' == $sItem) {
                if (!$bHasNumber) {
                    $sItem      = '{'.$sItem;
                    $bHasNumber = true;
                }
            } else {
                if ($bHasNumber) {
                    $sItem      = '}'.$sItem;
                    $bHasNumber = false;
                }
            }

            if (count($aDesc) - 1 == $iIndex && $bHasNumber) {
                $sItem = $sItem.'}';
            }
        }

        return implode('', $aDesc);
    }

    /**
     * @param array $aService $aService
     * @return bool
     */
    private function _checkService($aService) {
        if (empty($aService['service_id']) || empty($aService['max']) || empty($aService['title'])) {
            return false;
        }

        return true;
    }

    /**
     * 检查是否能mock选择出租车峰期加价
     * @param Product $oProduct 品类信息
     * @return bool
     */
    private function _checkNeedMock($oProduct) {
        $oTaxiPeakFeeClient = TaxiPeakFee::getPoolInstance($oProduct);
        // V2对sps的初始化在render中 依赖较广很难进行迁移 因此直接mock勾选
        if ($oTaxiPeakFeeClient->diffSwitch()) {
            return true;
        }
        return $oTaxiPeakFeeClient->getStatus() && $oTaxiPeakFeeClient->getIsInterActive();
    }

    /**
     * 该逻辑只有出租车会进入（product_category==7）
     * 方法背景：有出口的情况下，首次预估的默勾需要帮用户选择加价标识
     * @param int     $iIndex   索引
     * @param Product $oProduct 品类信息
     * @return void
     */
    public function buildTaxiPeakFeeCustom(int $iIndex, Product $oProduct) {
        $oTaxiPeakFeeClient = TaxiPeakFee::getPoolInstance($oProduct);
        if (!$this->_checkNeedMock($oProduct)) {
            return;
        }

        //针对主小旧表单的特殊入参
        $isMiniUserSelected = ConstantsCommon::DIDI_WECHAT_MINI_PROGRAM == $oProduct->oCommonInfo->iAccessKeyID
            && $oTaxiPeakFeeClient->getIsUserSelect();

        if ($oTaxiPeakFeeClient->getIsFirstEstimate() || $isMiniUserSelected) {
            $this->_aChooseCustomService[$iIndex] = [
                [
                    'id'    => CustomLogic::CUSTOM_SERVICE_TAXI_PEAK,
                    'count' => 1,
                ],
            ];
        }
    }

    /**
     * 请求账单之后构造 custom_service_info，该字段承载用户选择的个性化服务
     * @param array $aParam 请求账单request
     * @return array|mixed
     * @deprecated 无用
     */
    public function getCustomServiceInfo($aParam) {
        $aExtraInfoOrder = json_decode($aParam['extra_info']['order'], true);
        $aCustomService  = json_decode($aParam['custom_service_info'], true);
        if (empty($aCustomService)) {
            $aCustomService = [];
        }

        if (!empty($aExtraInfoOrder)) {
            $aPersonalizedCutomizedOption = $aExtraInfoOrder['personalized_custom_option'];
            $aCustomServiceSsse           = [];
            if (!empty($aPersonalizedCutomizedOption)) {
                foreach ($aPersonalizedCutomizedOption as $key => $item) {
                    $aCustomServiceSsse[] = ['id' => (int)($key), 'count' => $item['count']];
                }
            }

            $aCustomService = array_merge($aCustomService, $aCustomServiceSsse);
        }

        $aServiceInfos = [];
        if (!empty($aCustomService)) {
            foreach ($aCustomService as $index => $item) {
                if (!empty($item['id']) && !empty($item['count'])) {
                    $aServiceInfos[$item['id']] = $item;
                }
            }
        }

        return array_values($aServiceInfos);
    }

    /**
     * @param array   $aFormatService      ssse服务列表
     * @param array   $aCommonRequestParam 公共参数
     * @param array   $aMemberProfile      会员信息
     * @param array   $aSpsAddServiceReqs  reqs
     * @param Product $oProduct            品类信息
     * @return array
     */
    public function buildSpsAddServiceReqs($aFormatService, $aCommonRequestParam, $aMemberProfile, $aSpsAddServiceReqs, $oProduct = null) {
        $aSpsAddServiceReq = [];
        if (!empty($aFormatService) && is_array($aFormatService)) {
            if (!empty($aMemberProfile) && !empty($aMemberProfile['privileges'])) {
                foreach ($aMemberProfile['privileges'] as $sKey => $aV) {
                    if ('airport_guide' == $sKey) {
                        $aSpsAddServiceReq['member_privilege'] = ['airport_guide_right' => 1];
                        break;
                    }
                }
            }

            foreach ($aFormatService as $aService) {
                // 新版本出租车价格依然从SPS取
                if (107 == $aService['id'] && !TaxiPeakFee::getPoolInstance($oProduct)->diffSwitch()) {
                    // 出租车价格从出租车sps获取的
                    continue;
                }

                $aSpsAddServiceReq['service_list'][] = [
                    'service_id' => $aService['id'],
                    'count'      => $aService['selected_count'] ?: 1,
                ];
            }

            $aSpsAddServiceReq   += $aCommonRequestParam;
            $aSpsAddServiceReqs[] = $aSpsAddServiceReq;
        }

        return $aSpsAddServiceReqs;
    }
}
