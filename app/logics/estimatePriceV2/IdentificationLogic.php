<?php

/***************************************************************************
 * 从需求识别服务dds,获取预估产品数据
 *
 * Copyright (c) 2018 xiaojukeji.com, Inc. All Rights Reserved
 * <AUTHOR>
 * @version 2018-07-16
 *
 **************************************************************************/

namespace PreSale\Logics\estimatePriceV2;

//use BizLib\Utils\Language;
use BizLib\Utils\Partner;
use PreSale\Models\strategy\DecisionService;

//use BizLib\Utils\Locsvr;
//use BizLib\Utils\Channel;
//use BizLib\Utils\Common;
//use BizLib\Utils\Product;
//use Nuwa\ApolloSDK\Apollo;
//use BizCommon\Utils\Product as BizCommonProduct;
//use PreSale\Domain\Model\OneConf\OneConf;

class IdentificationLogic
{
    /** @var $decisionService DecisionService */
    public $decisionService;
    /**
     * @var null
     */
    protected static $_oInstance = null;

    /**
     * @var null
     */
    private $_oCI = null;

    /**
     * @var null
     */
    private $_oDecision = null;

    /**
     * @var array
     */
    private $_aIdentificationReq = array();

    /**
     * @var array
     */
    private $_aIdentificationResp = array();

    /**
     * @var array
     */
    private $_aInfo = array();

    /**
     * @var array
     */
    private $_aConfig = array();

    /**
     * @var bool
     */
    private $_aToggle = null;

    /**
     * @var array
     */
    private $_aPassengerInfo = array();

    /**
     * IdentificationLogic constructor.
     */
    public function __construct() {
        $this->decisionService = new DecisionService();
        $this->_oDecision      = $this->decisionService;
        $this->_aToggle        = false;
    }

    /**
     * @return null|IdentificationLogic
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * 设置参数.
     *
     * @param array $aParams
     *
     * @return bool
     */
    // 2024.9.11注释-待删
//    public function setIdentificationParams(array $aParams, array $aPassengerInfo) {
//        if (empty($aParams)) {
//            return false;
//        }
//
//        $this->_aInfo = $aParams;
//        if (empty($aPassengerInfo)) {
//            return false;
//        }
//
//        $this->_aInfo['phone'] = $aPassengerInfo['phone'];
//
//        $this->_aPassengerInfo = $aPassengerInfo;
//
//        return true;
//    }

      // 2024.9.11注释-待删
//    public function setIdentificationApolloSwitch() {
//        // 切流量开关
//        if ($this->hitIdentificationService()) {
//            return true;
//        }
//
//        return false;
//    }


    /**
     * 格式化dds请求参数.
     *
     * @param $aParams
     *
     * @return array
     */
    public function formatRequest($aParams, $aPassengerInfo) {
        $aIdentificationReq = array();
        if (empty($aParams)) {
            return $aIdentificationReq;
        }

        $sAppVersion = isset($aParams['app_version']) ? $aParams['app_version'] : $aParams['appversion'];

        $aIdentificationReq['lng']          = (double)($aParams['from_lng']);
        $aIdentificationReq['lat']          = (double)($aParams['from_lat']);
        $aIdentificationReq['to_lng']       = (double)($aParams['to_lng']);
        $aIdentificationReq['to_lat']       = (double)($aParams['to_lat']);
        $aIdentificationReq['location_lng'] = (double)($aParams['lng']);
        $aIdentificationReq['location_lat'] = (double)($aParams['lat']);
        $aIdentificationReq['uid']          = $aPassengerInfo['uid'] ?? 0;
        $aIdentificationReq['phone']        = $aPassengerInfo['phone'] ?? 0;
        $aIdentificationReq['map_type']     = (string)($aParams['map_type']);
        $aIdentificationReq['origin_id']    = (int)($aParams['origin_id']);
        $aIdentificationReq['app_version']  = (string)$sAppVersion;
        $aIdentificationReq['lang']         = (string)($aParams['lang']);
        $aIdentificationReq['data_type']    = (string)($aParams['datatype']);
        $aIdentificationReq['menu_id']      = (string)($aParams['menu_id']);
        $aIdentificationReq['order_type']   = (string)($aParams['order_type']);
        $aOrderTypeSpecial = json_decode((string)($aParams['order_type_special']), true);
        $aIdentificationReq['order_type_special'] = $aOrderTypeSpecial;
        $aIdentificationReq['one_conf_origin']    = (string)($aParams['one_conf']);
        $aIdentificationReq['pixels']        = (string)($aParams['pixels']);
        $aIdentificationReq['channel']       = (int) $aParams['channel'];
        $aIdentificationReq['access_key_id'] = (int) $aParams['access_key_id'];
        $aIdentificationReq['callcar_type']  = (int) $aParams['call_car_type'];
        $aIdentificationReq['client_type']   = (int) $aParams['client_type'];
        $aIdentificationReq['partner_id']    = Partner::getPartnerIdByChannel((int) $aParams['channel']);
        $aIdentificationReq['page_type']     = (int) $aParams['page_type'];
        $aIdentificationReq['from']          = (string)$aParams['from'];
        $aIdentificationReq['city_id']       = (int)$aParams['city_id'];
        $aDefaultSelection = json_decode((string)($aParams['default_selection']), true);
        $aIdentificationReq['default_selection'] = $aDefaultSelection;
        if (ParamsLogic::getInstance()->hitMultiEstimateForPremium()) {
            $aIdentificationReq['default_selection'] = ParamsLogic::getInstance()->getDefaultSelection();
        }

        $aIdentificationReq['stopover_points'] = json_encode($aParams['stopover_points']);
        $aIdentificationReq['x_new_form']      = (int)$aParams['x_new_form'];
        $aIdentificationReq['x_activity_id']   = (int)$aParams['x_activity_id'];

        return $aIdentificationReq;
    }

    /**
     * @return bool
     */
    // 2024.9.10注释-待删
//    public function hitIdentificationService() {
//        if (!$this->_isInland() && Product::COMMON_PRODUCT_ID_UNITAXI_CAR != $this->_aInfo['business_id']) {
//            return false;
//        }
//
//        if (empty($this->_aInfo)) {
//            return false;
//        }
//
//        //未登录用户也调用dds/oneconf
////        if (empty($this->_aPassengerInfo)) {
////            return false;
////        }
//        $apolloV2 = new Apollo();
//        if ($apolloV2->featureToggle(
//            'gs_demand_Identification_switch',
//            [
//                'key'          => $this->_aPassengerInfo['phone'] ?? 0,
//                'city'         => (string)($this->_aInfo['city_id']),
//                'phone'        => (string)($this->_aPassengerInfo['phone'] ?? 0),
//                'origin_id'    => (string)($this->_aInfo['origin_id']),
//                'app_version'  => (string)($this->_aInfo['app_version']),
//                'menu_id'      => (string)($this->_aInfo['menu_id']),
//                'client_type'  => (string)($this->_aInfo['client_type']),
//                'channel'      => (string)($this->_aInfo['channel']),
//                'agent_type'   => (string)($this->_aInfo['agent_type'] ?? ''),
//                'from'         => (string)($this->_aInfo['from'] ?? ''),
//                'dds_customer' => (string)($this->_aInfo['dds_customer'] ?? ''),
//                'degrade_type' => (string)($this->_aInfo['degrade_type'] ?? ''),
//            ]
//        )->allow()
//        ) {
//            return true;
//        }
//
//        return false;
//    }

    /**
     * @return bool
     */
    // 2024.9.11注释-待删
//    public function hitMiniAppIdentificationService() {
//        if (empty($this->_aInfo)) {
//            return false;
//        }
//
//        //if (Common::isLiteVersion($this->_aInfo['app_version'], $this->_aInfo['client_type'], $this->_aInfo['channel'])) {
//        //    return true;
//        //}
//        return false;
//    }

    /**
     * 执行识别。
     *
     * @return array
     */
    public function getOneConfList() {
        if (!empty($this->_aIdentificationResp) && is_array($this->_aIdentificationResp)) {
            return $this->_aIdentificationResp;
        }

        // 保证开关一致性
        $this->_aToggle = true;
        // dds服务
        $this->_aIdentificationReq  = $this->formatRequest($this->_aInfo, $this->_aPassengerInfo);
        $this->_aIdentificationResp = $this->_oDecision->getIdentificationResult($this->_aIdentificationReq);

        return $this->_aIdentificationResp;
    }

    /**
     * @return array
     * @throws \Exception 异常
     */
    // 2024.9.11注释-待删
//    public function getOneConfFromApollo() {
//        $newAInfo            = $this->_aInfo;
//        $newAInfo['channel'] = $this->_aPassengerInfo['channel'];
//        $oOneConf            = new OneConf($newAInfo);
//        return $oOneConf->getEstimateOneConf();
//    }

    /**
     * 获取需求识别开关结果。
     *
     * @return bool
     */
    // 2024.9.11注释-待删
//    public function getIdentificationToggleResult() {
//        return $this->_aToggle;
//    }

    /**
     * @return bool
     */
    // 2024.9.11注释-待删
//    private function _isInland() {
//        return Locsvr::isInland($this->_aInfo['order_info']['area']) && in_array(Language::getLanguage(), array(Language::ZH_CN));
//    }
}
