<?php
/**
 * 冒泡阶段新手教育.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */

namespace PreSale\Logics\estimatePriceV2;

use BizCommon\Logics\Carpool\CarpoolBrand;
use BizCommon\Utils\Order;
use BizLib\Config as NuwaConfig;
use BizCommon\Utils\Horae;
use BizLib\Constants\OrderSystem;
use PreSale\Models\order\OrderCarpoolDualPrice;

class FreshEducationLogic
{
    private $_aProductInfo = [];
    /**
     * @var \BizCommon\Models\Cache\EstimatePrice
     */
    private $_oEstimatePriceCache = null;

    public function __construct($aProductInfo, $aConfirmParams) {
        $this->_aProductInfo        = $aProductInfo;
        $this->_aConfirmParams      = $aConfirmParams;
        $this->_oEstimatePriceCache = \BizCommon\Models\Cache\EstimatePrice::getInstance();
    }

    // 注释-待删
//    public function getFreshEducationConfig() {
//        if (in_array($this->_aProductInfo['product_id'],[OrderSystem::PRODUCT_ID_UNITAXI, OrderSystem::PRODUCT_ID_BUSINESS_TAXI_CAR])
//            && Horae::bStationCarpool($this->_aProductInfo)
//        ) {
//            return NuwaConfig::text('config_carpool', 'fresh_guide_page_unione_carpool');
//        } elseif (Horae::bStationCarpool($this->_aProductInfo)) {
////            $sAppversion = $this->_aConfirmParams['common_info']['app_version'];
////            $aOrderInfo = $this->_aConfirmParams['order_info'];
//            //如果是两口价新用户，返回两口价的新手教育
//            if (Horae::isCarpoolDualPrice($this->_aProductInfo)) {
//                //获取两口价减券后的价格
//                $aCacheInfo = $this->_oEstimatePriceCache->getConfirmCache($this->_aConfirmParams['pre_estimate_id']);
//                $aFeeRet    = (new OrderCarpoolDualPrice())->getEstimateDualPriceWithCoupons($aCacheInfo);
//                if (empty($aFeeRet)) {
//                    return [];
//                }
//
//                $aFillData = [
//                    'carpool_succ_fee' => $aFeeRet[0],
//                    'carpool_fail_fee' => $aFeeRet[1],
//                ];
//
////                $sText = CarpoolBrand::text('config_carpool', 'carpool_dual_price_fresh_guide_page', $sAppversion, $aOrderInfo, $aFillData);
//                $sText = \BizLib\Utils\Language::getTextFromDcmp('carpool_dual_price_fresh_guide_page', $aFillData);
//                return json_decode($sText, true);
//            }
//
////            $sText = CarpoolBrand::text('config_carpool', 'carpool_fresh_guide_page', $sAppversion, $aOrderInfo);
//            $sText = \BizLib\Utils\Language::getTextFromDcmp('carpool_fresh_guide_page');
//            return json_decode($sText, true);
//        } elseif (Order::isSpecialRateV2($this->_aProductInfo)) {
//            return NuwaConfig::text('config_special', 'fresh_guide_page');
//        } elseif (\BizLib\Utils\CarLevel::DIDI_YOUXIANG_CAR_LEVEL == $this->_aProductInfo['require_level']
//            && \BizLib\Constants\Horae::TYPE_COMBO_DEFAULT == $this->_aProductInfo['combo_type']
//            && \BizLib\Constants\OrderSystem::PRODUCT_ID_FAST_CAR == $this->_aProductInfo['product_id']
//        ) {
//            $aFreshPage     = NuwaConfig::text('config_select', 'fresh_guide_page');
//            $iCityId        = $this->_aConfirmParams['order_info']['area'];
//            $iBusinessId    = \BizLib\Utils\Product::getCommonProductId($this->_aConfirmParams['order_info']['product_id']);
//            $iRequiredLevel = $this->_aConfirmParams['order_info']['require_level'];
//            $iComboType     = $this->_aConfirmParams['order_info']['combo_type'];
//            $aTitanConfig   = \BizLib\Utils\BizConfigLoader::getNewSelectPopWin($iCityId, $iBusinessId, $iRequiredLevel, $iComboType);
//            if (!empty($aTitanConfig)) {
//                $aFreshPage['title']        = $aTitanConfig['title'] ?? $aFreshPage['title'];
//                $aFreshPage['icon']         = $aTitanConfig['icon'] ?? $aFreshPage['icon'];
//                $aFreshPage['main_content'] = $aTitanConfig['main_content'] ?? $aFreshPage['main_content'];
//                $aFreshPage['button_text']  = $aTitanConfig['button_text'] ?? $aFreshPage['button_text'];
//            }
//
//            return $aFreshPage;
//        }
//
//        return [];
//    }
}
