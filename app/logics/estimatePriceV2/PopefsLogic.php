<?php
/***************************************************************************
 * 获取popefs特征
 *
 * Copyright (c) 2018 xiaojukeji.com, Inc. All Rights Reserved
 * <AUTHOR>
 * @version 2018-11-27
 *
 **************************************************************************/

namespace PreSale\Logics\estimatePriceV2;

use PreSale\Models\rpc\PopefsRpc;

class PopefsLogic
{
    /** @var $popefsRpc PopefsRpc */
    public $popefsRpc;
    /**
     * @var null
     */
    protected static $_oInstance = null;

    /**
     * @var OrderSystemClient|null
     */
    private $_oClient = null;

    /**
     * @var array
     */
    private $_aInfo = array();

    /**
     * @var array
     */
    private $_aPassengerInfo = array();

    /**
     * PopefsLogic constructor.
     */
    public function __construct() {
        $this->popefsRpc   = new PopefsRpc();
        $this->_oPopefsRpc = $this->popefsRpc;
        $this->_aToggle    = false;
    }

    /**
     * @return null|PopefsLogic
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * 设置参数.
     *
     * @param array $aParams
     *
     * @return bool
     */
    public function setPopefsParams(array $aParams, array $aPassengerInfo, $sRouteId = 0, $aServiceTag = []) {
        if (empty($aParams)) {
            return false;
        }

        $this->_aInfo = $aParams;
        $this->_aInfo['commute_route_id'] = $sRouteId;
        $this->_aInfo['service_tag']      = $aServiceTag;
        if (empty($aPassengerInfo)) {
            return false;
        }

        $this->_aPassengerInfo = $aPassengerInfo;

        return true;
    }

    public function formatRequest($aParams, $aPassengerInfo) {
        $aPopefsReq = array();
        if (empty($aParams)) {
            return $aPopefsReq;
        }

        $aPopefsReq['lng']         = (double)($aParams['from_lng']);
        $aPopefsReq['lat']         = (double)($aParams['from_lat']);
        $aPopefsReq['pid']         = $aPassengerInfo['pid'];
        $aPopefsReq['route_id']    = $aParams['commute_route_id'];
        $aPopefsReq['service_tag'] = $aParams['service_tag'];

        return $aPopefsReq;
    }

    /**
     * 获取popefs特征
     *
     * @return array
     */
    public function mGetFeatures() {
        $aMFeaturesResult = array();
        $aMFeaturesReq    = $this->formatRequest($this->_aInfo, $this->_aPassengerInfo);
        $aMFeaturesResult = $this->_oPopefsRpc->getPopefsMFeatures($aMFeaturesReq);

        return $aMFeaturesResult;
    }
}
