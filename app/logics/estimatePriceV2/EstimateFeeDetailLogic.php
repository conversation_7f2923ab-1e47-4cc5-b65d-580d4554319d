<?php
/**
 * <AUTHOR> <<EMAIL>>
 * Date: 2020/05/15
 * DESC: 预估费用详情页
 */
namespace PreSale\Logics\estimatePriceV2;

use BizLib\Config as NuwaConfig;
use BizLib\Utils\CarLevel;
use BizLib\Utils\ProductCategory;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\estimatePriceV2\multiResponse\component\productInfo\Common;

/**
 * @desc 预估账单详情页逻辑处理
 * Class EstimateFeeDetailLogic
 * @package PreSale\Logics\estimatePriceV2
 */
class EstimateFeeDetailLogic
{
    const SERVICE_ITEM_NS = 'fee_detail_service_item_conf';
    private $_aFeeDetailData;
    private $_aNTuple;
    private $_iCity;
    private $_iPid;

    /**
     * EstimateFeeDetailLogic constructor.
     * @param array $aFeeDetailData aFeeDetailData
     */
    public function __construct($aFeeDetailData) {
        $this->_aFeeDetailData = $aFeeDetailData;
        $this->_aNTuple        = $aFeeDetailData['n_tuple'] ?? [];
        $this->_iCity          = (int)$aFeeDetailData['area'] ?? 0;
    }
    /**
     * @desc 组装过滤参数
     * @return array
     */
    private function _getParams() {
        $aParams = [
            Apollo::APOLLO_INDIVIDUAL_ID => $this->_iCity,
            'city'                       => $this->_iCity,
        ];
        return array_merge($this->_aNTuple,$aParams);
    }

    /**
     * @param int $iPid $iPid
     * @return void
     */
    public function setPid($iPid) {
        $this->_iPid = $iPid;
    }

    // 注释-待删
//    /**
//     * @desc 获取服务项
//     * @return array
//     */
//    public function getServiceItem() {
//        $aServiceItems = [];
//
//        // 单一城市有多种起步价等计价规则（如济南）时，从 dcmp 获取文案信息下发
//        $aMultiRulePrice = NuwaConfig::text('config_text', 'multi_rule_price') ?? [];
//        if (CarLevel::DIDI_UNITAXI_PUTONG_CAR_LEVEL == $this->_aFeeDetailData['url_params']['car_level']
//            && in_array($this->_iCity, array_keys($aMultiRulePrice))
//        ) {
//            $aServiceItems = [
//                [
//                    'icon'       => $aMultiRulePrice[$this->_iCity]['icon'],
//                    'link_title' => $aMultiRulePrice[$this->_iCity]['link_title'],
//                    'link_url'   => $aMultiRulePrice[$this->_iCity]['link_url'],
//                    'title'      => $aMultiRulePrice[$this->_iCity]['title'],
//                    'content'    => $aMultiRulePrice[$this->_iCity]['content'],
//                ],
//            ];
//
//            return $aServiceItems;
//        }
//
//        //1.prepare param.
//        $iProductCategory = Common::getProductCategory($this->_aNTuple);
//        $aParams          = $this->_getParams();
//
//        //2.get apollo conf.
//        list($bOk, $aConfig) = Apollo::getInstance()->getConfigsByNamespaceAndConditions(
//            self::SERVICE_ITEM_NS,
//            ['product_category' => $iProductCategory]
//        )->getAllConfigData();
//        $aServiceConfigs     = $bOk && is_array($aConfig) ? array_values($aConfig) : [];
//        if (empty($aServiceConfigs)) {
//            return [];
//        }
//
//        $aServiceConf = current($aServiceConfigs);
//        $aServiceConf = $aServiceConf['content'] ?? [];
//        foreach ($aServiceConf as $aServiceItem) {
//            //3.filter condition
//            $aCondition = $aServiceItem['condition'] ?? [];
//            if (!empty($aCondition['apollo_key'])) {
//                $sApolloKey = $aCondition['apollo_key'];
//                if (!Apollo::getInstance()->featureToggle($sApolloKey, $aParams)->allow()) {
//                    continue;
//                }
//            }
//
//            //4.get service item
//            $aItem         = $aServiceItem['service_item'] ?? [];
//            $aServiceItems = array_merge($aServiceItems, $aItem);
//        }
//
//        // 拼车准时保ab实验 实验组跳转新的准时保详情链接
//        if (ProductCategory::PRODUCT_CATEGORY_CARPOOL_STATION == $iProductCategory) {
//            $oToggle = Apollo::getInstance()->featureToggle(
//                'gs_carpool_etd_text_exp',
//                [
//                    Apollo::APOLLO_INDIVIDUAL_ID => $this->_iPid,
//                    'pid'                        => $this->_iPid,
//                    'city'                       => $this->_iCity,
//                ]
//            );
//
//            if ($oToggle->allow() && 'treatment_group' == $oToggle->getGroupName()) {
//                if (!empty($aServiceItems)) {
//                    $aServiceItems[0]['link_url'] = $oToggle->getAllParameters()['url'];
//                }
//            }
//        }
//
//        //5.return
//        return $aServiceItems;
//    }
}
