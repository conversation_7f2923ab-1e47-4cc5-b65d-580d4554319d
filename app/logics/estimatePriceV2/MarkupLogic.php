<?php

namespace PreSale\Logics\estimatePriceV2;

use B<PERSON><PERSON>ib\Log as NuwaLog;
use Biz<PERSON>ib\Utils\Language;
use BizLib\Utils\NumberHelper;
use BizLib\Utils\UtilHelper;
use Disf\SPL\Trace;
use B<PERSON><PERSON><PERSON><PERSON>\Logics\Order\FieldOrderNTuple;
use BizCommon\Logics\Order\OrderComLogic;
use BizLib\Config;
use BizLib\Utils\Product;
use BizLib\ErrCode;
use Xiaoju\Diconf;
use BizLib\Config as NuwaConfig;

/**
 * 加价相关.
 *
 * <AUTHOR>
 * @date(2018-08-20)
 */
class MarkupLogic
{
    const FEE_ICON_NONE = 0;
    const FEE_ICON_UP   = 1;
    const FEE_ICON_DOWN = 2;

    const UBER_ADD_FEE_REASON_IMAGE     = '//pt-starimg.didistatic.com/static/starimg/img/15254144286947VAjBkJaijoVmSzDACY.png';
    const YOUXIANG_ADD_FEE_REASON_IMAGE = '//pt-starimg.didistatic.com/static/starimg/img/15253171971808JkQFmb0IAHYgNOig4M.png';
    const DEFAULT_ADD_FEE_REASON_IMAGE  = '//pt-starimg.didistatic.com/static/starimg/img/15253172000512zLQAufcfZ968xbjZY1.png';

    private static $_ci = null;

    private static function _loadCi() {
        if (empty(self::$_ci)) {
        }

        return self::$_ci;
    }

    //获取加价原因和加价图片
    public static function getAddFeeImageAndReason($aOrderInfo) {
        $aRet = [
            'reason' => null,
            'image'  => '',
        ];

        $aRet = self::_getSpecialSceneConfig();
        if (empty($aRet['image'])) {
            if (Product::isUber($aOrderInfo['product_id'])) {
                $aRet['image'] = self::UBER_ADD_FEE_REASON_IMAGE;
            } elseif (900 == $aOrderInfo['require_level']) {
                $aRet['image'] = self::YOUXIANG_ADD_FEE_REASON_IMAGE;
            } else {
                $aRet['image'] = self::DEFAULT_ADD_FEE_REASON_IMAGE;
            }
        }

        return $aRet;
    }

    private static function _getSpecialSceneConfig() {
        $aRet = [];

        $oParams            = ParamsLogic::getInstance();
        $iSpecialSceneParam = $oParams->getSpecialSceneParam();

        if (!empty($iSpecialSceneParam)) {
            // 获取 Diconf 实例
            $oDiconf = Diconf\Diconf::getInstance(Diconf\APOLLO_MODE, Diconf\JSON_FILE_TYPE);

            // 根据 type 获取对应 value
            list($bOk, $aValue) = $oDiconf->getConfig('special_scenario_res', 'special_scenario_res', 'data');
            // 判断是否成功
            if ($bOk && (!empty($aValue))) {
                $aConfig = $aValue[$iSpecialSceneParam][0];
                $aRet    = [
                    'reason' => $aConfig['confirm']['title'] ?? '',
                    'image'  => $aConfig['confirm']['image'] ?? '',
                ];
            } else {
                NuwaLog::warning(
                    ErrCode\Msg::formatArray(
                        ErrCode\Code::E_PUSH_SDL_GET_EMPTY,
                        ['msg' => 'get config special_scene failed', 'result' => $aValue, 'module' => MODULE_NAME, ]
                    )
                );
            }
        }

        return $aRet;
    }

    // 注释-待删
//    /**
//     * 获取默认加价确认页信息.
//     *
//     * param array   $aOrderInfo
//     * param array   $aFee  //账单返回的加价费用项 "parent":"","name":"peak_fee","from":0,"to":0,"range_unit":"","quantity":0,"quantity_unit":"%","value":26} 包含 extra_info {"supply_num":0,"demand_num":0,"dynamic_price_id":"dpkey_fdd4edd4b4b1ed217b1cb254ed5fffa5_3_4_0_15602313724_838334222336_20180817141803"}"
//     * param array   $aH5ConfirmConfig
//     * param float   $fTotalFee
//     * param array   $aCommInfo
//     * param array   $aEnv currency_unit,currency_symbol
//     * return array
//     **/
//    public static function getDefaultConfirmInfo($aOrderInfo, $aFee, $aH5ConfirmConfig, $fTotalFee, $aCommInfo, $aEnv) {
//        if (empty($aFee['value']) || $aFee['value'] <= 0 || empty($aH5ConfirmConfig)) {
//            return [];
//        }
//
//        $aFeeExtraInfo = [];
//        $sFeeExtraKey  = '';
//        if (isset($aFee['name']) && 'peak_fee' == $aFee['name']) {
//            $sFeeExtraKey = 'peak_info';
//        }
//
//        if (!empty($sFeeExtraKey) && !empty($aFee['extra_info'][$sFeeExtraKey])) {
//            $aJson = json_decode($aFee['extra_info'][$sFeeExtraKey], true);
//            if (!empty($aJson['demand_num'])) {
//                $aFeeExtraInfo = $aJson;
//            }
//        }
//
//        $iProductId = (int)($aOrderInfo['product_id']);
//        $iCarLevel  = $aOrderInfo['require_level'];
//        $iComboType = $aOrderInfo['combo_type'];
//
//        $sProductName = OrderComLogic::getProductDisplayNameByCarLevel($iCarLevel, $iComboType);
//
//        $aReasonInfo  = self::getAddFeeImageAndReason($aOrderInfo);
//        $sFirstReason = $aReasonInfo['reason'] ?: $aH5ConfirmConfig['content_title_1'];
//        $sReason      = $aH5ConfirmConfig['content_title_2'];
//
//        $iTipsType = self::FEE_ICON_UP;
//
//        $aParam = [
//            'is_anycar'          => 0,
//            'trace_id'           => Trace::traceId(),
//            'page_title'         => Language::replaceTag(
//                $aH5ConfirmConfig['page_title'],
//                ['name' => $sProductName,]
//            ),
//            'desc_image'         => $aReasonInfo['image'],
//            'content_title_1'    => $sFirstReason,
//            'content_title_2'    => Language::replaceTag(
//                $sReason,
//                [
//                    'queue_length' => $aFeeExtraInfo['demand_num'] ?? 0,
//                    'car_num'      => $aFeeExtraInfo['supply_num'] ?? 0,
//                ]
//            ),
//            'is_uber'            => Product::isUber($iProductId) ? 1 : 0,
//            'expire_text'        => Language::replaceTag(
//                $aH5ConfirmConfig['expire_text'],
//                [
//                    'cache_minutes' => self::getAddFeeCacheMinutes($aOrderInfo),
//                ]
//            ),
//            'accept_button_text' => $aH5ConfirmConfig['accept_button_text'],
//            'deny_button_text'   => $aH5ConfirmConfig['deny_button_text'],
//        ];
//
//        $aList[]         = [
//            'product_name'       => $sProductName,
//            'type'               => $iTipsType, //加价类型
//            'type_name'          => '', //加价倍数,加价封顶等
//            'vip_level'          => 0,
//            'vip_name'           => '',
//            'vip_total_fee_text' => '',
//            'vip_project_text'   => '',
//            'plus_fee_num'       => NumberHelper::numberFormatDisplay($aFee['value']),
//            'plus_fee_unit'      => $aEnv['currency_unit'],
//            'plus_fee_sybmol'    => $aEnv['currency_symbol'],
//            'plus_fee_operator'  => '+',
//            'total_fee_text'     => Language::replaceTag(
//                $aH5ConfirmConfig['total_fee_text'],
//                [
//                    'currency_symbol' => $aEnv['currency_symbol'],
//                    'fee'             => NumberHelper::numberFormatDisplay($fTotalFee),
//                    'currency_unit'   => $aEnv['currency_unit'],
//                ]
//            ),
//            'statistic_data'     => self::addFeeStatistic($aOrderInfo, $aFeeExtraInfo),
//        ];
//        $aParam['list']  = $aList;
//        $aReturn['type'] = $iTipsType;
//        $aConfirmParams  = [
//            'data' => json_encode($aParam),
//        ];
//        $aReturn['confirm_h5'] = self::getH5StaticUrl('fee_confirm_url', $iProductId, $aConfirmParams);
//        if ($aCommInfo['is_from_webapp']) {
//            $aReturn['webapp_component_confirm_h5'] = self::getH5StaticUrl(
//                'webapp_component_fee_confirm_url',
//                $iProductId,
//                $aConfirmParams
//            );
//        }
//
//        return $aReturn;
//    }

    // 注释-待删
//    public static function getH5StaticUrl(string $sKey, int $iProductId, array $aParams) {
//        if (!is_array($aParams)) {
//            return '';
//        }
//
//        $sUrl = UtilHelper::getConfigUrl($sKey, $iProductId);
//        $sUrl = UtilHelper::httpAppendQuery($sUrl, $aParams);
//
//        return $sUrl;
//    }

    public function getAddFeeCacheMinutes($aOrderInfo) {
        $aPassengerConfig = Config::config('config_passenger', 'config_passenger');
        if ($aOrderInfo['is_fast_car']) {
            $iCacheTime = $aPassengerConfig['estimate_price_fast_car_time'];
        } else {
            $iCacheTime = $aPassengerConfig['estimate_price_default_time'];
        }

        return (int)($iCacheTime / 60);
    }

    //加价统计数据 H5埋点
    public static function addFeeStatistic($aOrderInfo, $aFeeExtraInfo = []) {
        $aData      = FieldOrderNTuple::getOrderNTupleByOrder($aOrderInfo);
        $aNeedFiled = [
            'product_id'    => 0,
            'business_id'   => 0,
            'require_level' => 0,
            'combo_type'    => '',
        ];
        $aData      = array_intersect_key($aData, $aNeedFiled);
        $aData['dynamic_price_id'] = $aFeeExtraInfo['dynamic_price_id'] ?? '';

        return $aData;
    }

    /**
     * 获取指定产品线指定的动调确认页静态配置.
     *
     * @param int $iProductId 产品线id
     *
     * @return string h5的url
     */
    public static function getConfirmConf($iProductId = 0) {
        $aConf = NuwaConfig::text('config_fee_text', 'confirm_content');
        if (isset($aConf[$iProductId]) && !empty($aConf[$iProductId])) {
            return $aConf[$iProductId];
        }

        return $aConf['0'];
    }
}
