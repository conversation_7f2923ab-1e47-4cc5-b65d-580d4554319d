<?php

namespace PreSale\Logics\estimatePriceV2;

use BizCom<PERSON>\Logics\Anycar\AnyCarCommonLogic;
use BizLib\Client\BillClient;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\ErrCode\RespCode;
use BizLib\Exception\ExceptionWithResp;
use BizLib\Libraries\RedisDB;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Language;
use BizLib\Utils\Product;
use BizLib\Exception\InvalidArgumentException;
use BizLib\ErrCode;
use BizLib\Client\PriceApiClient;
use BizCommon\Logics\Order\OrderComLogic;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\ProductCategory;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\estimatePriceV2\multiResponse\MainHelper;
use TripcloudCommon\Utils\Product as TripcloudProduct;
use BizLib\Client\TCPassengerClient;
use Biz<PERSON>om<PERSON>\Utils\Horae as BizHorae;

/**
 * Created by PhpStorm.
 * User: Luda
 * Date: 2018/12/11
 * Time: 下午3:10
 * <AUTHOR> <<EMAIL>>
 */

class FeeDetailLogic
{
    /**
     * @var ParamsLogic
     */
    protected static $_oInstance = null;

    public $redisdb;

    const NAME_DISCOUNT_DESC      = 'discount_desc';
    const NAME_FEE_DESC           =	'fee_desc';
    const NAME_TOTAL_FEE          = 'total_fee';
    const NAME_ESTIMATE_FEE       = 'estimate_fee';
    const NAME_DISCOUNT_DESC_FAIL = 'discount_desc_fail';
    const NAME_MULTI_INFO         = 'multi_info';
    const NAME_CARPOOL_SEAT_NUM   = 'carpool_seat_num';
    const NAME_PRODUCT_CATEGORY   = 'product_category';
    const NAME_AREA           = 'area';
    const NAME_DEPARTURE_TIME = 'departure_time';
    const NAME_FROM_LAT       = 'from_lat';
    const NAME_FROM_LNG       = 'from_lng';
    const NAME_PRICE_TOKEN    = 'price_token';
    const NAME_COMBO_TYPE     = 'combo_type';
    const NAME_ESTIMATE_TIME  = 'estimate_time';
    const REDIS_PREFIX_FEE_DETAIL = 'P_ESTIMATE_FEE_DESC_';
    const REDIS_PREFIX_SHORT_BOOK = 'P_SHORT_WAIT_SUMMARY_';
    const NAME_N_TUPLE            = 'n_tuple';
    const NAME_COMPARE_FEE        = 'compare_price';
    const COUNT_PRICE_TYPE        = 'count_price_type';
    const IS_MIXED_PAYMENT        = 'is_mixed_payment';
    const MIXED_PAYMENT_INFO      = 'mixed_payment_info';
    const DEFAULT_PAY_TYPE        = 'default_pay_type';
    CONST CARPOOL_SCENE_BILL      = 'carpool_scene_bill';
    CONST PRICE_PRIVILEGE_TYPE    = 'price_privilege_type';
    const NAME_CURRENCY           = 'currency';

    const ERRNO_SUCCESS = 0;
    const CONTRACT_TYPE_CONTRACT_USER = 1;

    /**
     * FeeDetailLogic constructor.
     */
    private function __construct() {
        $this->_aErrMsg = NuwaConfig::text('errno', 'pEstimatePrice_error_msg');
    }

    /**
     * 实例化.
     *
     * @return FeeDetailLogic
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    // 注释-待删
//    /**
//     * @desc  提供给企业级网开台飞费用明细
//     * @param array $aParams $aParams
//     * @return array 费用详情
//     */
//    public function getFeeDetailInfos($aParams) {
//        $aPriceParams = [
//            'estimate_id' => $aParams['estimate_id'],
//            'fields'      => [
//                'n_tuple',
//                'estimate_fee',
//                'fee_desc',
//                'area',
//                'product_category',
//            ],
//        ];
//
//        $aQuotation = (new PriceApiClient())->getQuotation($aPriceParams);
//        if (!isset($aQuotation['errno']) || Code::E_SUCCESS != $aQuotation['errno'] || empty($aQuotation['data'])) {
//            NuwaLog::warning(Msg::formatArray(Code::E_COMMON_GET_QUOTATION_FAIL, ['req' => json_encode($aPriceParams), 'resp' => json_encode($aQuotation)]));
//            return [];
//        }
//
//        $aFeeDesc = $this->_getFeeDetailPlutusTripcloud($aQuotation['data'], $aParams['estimate_id'], $aParams['lang']);
//
//        return $aFeeDesc;
//    }


    // 注释-待删
//    /**
//     * @desc  获取费用详情
//     * @param array  $aQuotation  报价单数据
//     * @param string $sEstimateID 预估id：
//     * @param string $lang        语言
//     * @return array 费用详情
//     */
//    private function _getFeeDetailPlutusTripcloud($aQuotation, $sEstimateID, $lang) {
//        //先用报价单数据兜底网开台数据，当网开台接口稳定后下掉兜底
//        $aOrderNTuple     = json_decode($aQuotation['n_tuple'], true);
//        $quotationFeeDesc = json_decode(json_decode($aQuotation['fee_desc']), true) ?? [];
//
//        //Limo端使用报价单的FEE_DESC字段获取预估费用明细信息
//        if (isset($aOrderNTuple['contract_type']) && self::CONTRACT_TYPE_CONTRACT_USER == $aOrderNTuple['contract_type']) {
//            return $quotationFeeDesc;
//        }
//
//        if (TripcloudProduct::isTripcloudByBusinessID((int)$aOrderNTuple['business_id'])) {
//            $aFeeDesc = $this->_getTripcloudFeeDetailInfos($sEstimateID, (int)$aQuotation['area']);
//        } else {
//            $aFeeDesc = $this->_getPlutusFeeDetailInfos($sEstimateID);
//        }
//
//        if (empty($aFeeDesc)) {
//            $aFeeDesc = $this->_getFeeDetailDefault($aQuotation, $aOrderNTuple, $lang);
//        }
//
//        return $aFeeDesc;
//    }


    // 注释-待删
//    /**
//     * @param string $sEstimateID 预估id
//     * @return array
//     */
//    private function _getFeeDetailParams($sEstimateID) {
//        $aReq = array(
//            'estimate_id' => $sEstimateID,
//            'fields'      => array(
//                self::NAME_DISCOUNT_DESC,
//                self::NAME_FEE_DESC,
//                self::NAME_TOTAL_FEE,
//                self::NAME_ESTIMATE_FEE,
//                self::NAME_DISCOUNT_DESC_FAIL,
//                self::NAME_CARPOOL_SEAT_NUM,
//                self::NAME_MULTI_INFO,
//                self::NAME_AREA,
//                self::NAME_COMBO_TYPE,
//                self::NAME_ESTIMATE_TIME,
//                self::NAME_N_TUPLE,
//                self::NAME_DEPARTURE_TIME,
//                self::NAME_FROM_LAT,
//                self::NAME_FROM_LNG,
//                self::NAME_PRICE_TOKEN,
//                self::NAME_COMPARE_FEE,
//                self::COUNT_PRICE_TYPE,
//                self::IS_MIXED_PAYMENT,
//                self::MIXED_PAYMENT_INFO,
//                self::DEFAULT_PAY_TYPE,
//                self::NAME_PRODUCT_CATEGORY,
//                self::CARPOOL_SCENE_BILL,
//                self::PRICE_PRIVILEGE_TYPE,
//                self::NAME_CURRENCY,
//            ),
//        );
//
//        return $aReq;
//    }

    // 注释-待删
//    /**
//     * @desc  获取账单费用详情
//     * @param string $estimateId 预估id
//     * @return array 费用详情
//     */
//    private function _getPlutusFeeDetailInfos($estimateId) {
//        $aReq = ['estimate_id' => $estimateId,];
//
//        $feeDetailInfo = (new BillClient())->getFeeDetailInfos($aReq);
//        if (!isset($feeDetailInfo['errno']) || self::ERRNO_SUCCESS != $feeDetailInfo['errno'] || empty($feeDetailInfo['data'])) {
//            NuwaLog::warning(Msg::formatArray(Code::E_COMMON_HTTP_PLUTUS_FEE_DETAIL, ['estimate_id' => $estimateId, 'result' => json_encode($feeDetailInfo)]));
//            return [];
//        }
//
//        return $feeDetailInfo['data'];
//    }

    // 注释-待删
//    /**
//     * @desc  获取网开台费用详情
//     * @param string $estimateId 预估id
//     * @param int    $city       城市
//     * @return array 费用详情
//     */
//    private function _getTripcloudFeeDetailInfos($estimateId, $city) {
//        $aReq          = ['estimate_id' => $estimateId,];
//        $feeDetailInfo = (new TCPassengerClient())->pGetEstimateFeeDetail($aReq);
//        if (!isset($feeDetailInfo['errno']) || self::ERRNO_SUCCESS != $feeDetailInfo['errno'] || empty($feeDetailInfo['data'])) {
//            NuwaLog::warning(Msg::formatArray(Code::E_COMMON_HTTP_TRIPCLOUD_FEE_DETAIL, ['estimate_id' => $estimateId, 'result' => json_encode($feeDetailInfo)]));
//            return [];
//        }
//
//        return (array)$feeDetailInfo['data'];
//    }

    // 注释-待删
//    /**
//     * 费用详情比diff
//     * @param array $quotationFeeDesc 报价单费用
//     * @param array $plutusFeeDesc    费用接口
//     * @return array 活动详情
//     */
//    private function _getFeeDetailDiff($quotationFeeDesc, $plutusFeeDesc) {
//        unset($quotationFeeDesc['extra_info']);
//        unset($plutusFeeDesc['extra_info']);
//        $aDiff = MainHelper::getResponseDiff($quotationFeeDesc, $plutusFeeDesc);
//        if (!empty($aDiff)) {
//            NuwaLog::warning(Msg::formatArray(Code::E_COMMON_HTTP_PLUTUS_FEE_DETAIL_DIFF, ['result' => json_encode($aDiff)]));
//            return $aDiff;
//        }
//
//        return [];
//    }

    // 注释-待删
//    /**
//     * 费用详情兜底
//     * @param array  $aRet         报价单
//     * @param array  $aOrderNTuple $aOrderNTuple
//     * @param string $lang         语言
//     * @return array 费用详情兜底
//     */
//    private function _getFeeDetailDefault($aRet, $aOrderNTuple, $lang = 'zh-CN') {
//        $aFeeDesc   = NuwaConfig::text('config_fee_text', 'fee_detail_default', ['estimate_fee' => $aRet['estimate_fee']], $lang);
//        $labelTitle = '';
//        if (ProductCategory::PRODUCT_CATEGORY_FAST == $aRet['product_category']) {
//            $labelTitle = $aFeeDesc['items']['label_title_express'];
//        }
//
//        if (\BizCommon\Utils\Horae::isCarpoolUnsuccRealTimePrice($aOrderNTuple)) {
//            $labelTitle = $aFeeDesc['items']['label_title_carpool'];
//        }
//
//        //费用明细兜底
//        $items = [
//            'bill_data'       => [],
//            'label_title'     => $labelTitle,
//            'need_pay'        => [
//                'need_pay_total_fee' => $aFeeDesc['items']['need_pay_total_fee'],
//                'total_fee'          => $aFeeDesc['items']['total_fee'],
//            ],
//            'need_pay_detail' => [],
//            'real_pay'        => [
//                'bill_total_fee' => $aRet['estimate_fee'],
//                'intros'         => [
//                    'font_color' => $aFeeDesc['items']['font_color'],
//                    'name'       => '',
//                    'value'      => $aFeeDesc['items']['intros_value'],
//                ],
//                'total_fee'      => $aFeeDesc['items']['real_pay_total_fee'],
//            ],
//        ];
//
//        $aFeeDesc = [
//            'estimate_or_cashier' => 1,
//            'extra_info'          => [],
//            'fee_problem'         => [
//                'button' => [
//                    'link' => $aFeeDesc['fee_problem']['button_link'],
//                    'name' => $aFeeDesc['fee_problem']['button_name'],
//                    'type' => 1,
//                ],
//                'intros' => [
//                    [
//                        'name'  => '',
//                        'value' => $aFeeDesc['fee_problem']['intros_value'],
//                    ],
//                ],
//                'name'   => $aFeeDesc['fee_problem']['name'],
//            ],
//            'items'               => [$items],
//            'order_type'          => $aFeeDesc['order_type'],
//            'page_title'          => '',
//            'title'               => '',
//        ];
//
//        return $aFeeDesc;
//    }

    // 注释-待删
//    /**
//     * @param array $aQuotation 报价单数据
//     * @param array $aFeeDesc   费用明细
//     * @return array
//     */
//    private function _formatFeeDetailResponse($aQuotation, $aFeeDesc) {
//        $aOrderNTuple        = json_decode($aQuotation[self::NAME_N_TUPLE], true);
//        $aFormatDiscountList = array();
//        // 两口价
//        $aFormatDiscountListFail = array();
//        // 正常优惠列表
//        $aDiscountList = json_decode($aQuotation[self::NAME_DISCOUNT_DESC], true);
//        $aDiscountList = is_array($aDiscountList) ? $aDiscountList : [];
//        // 一口失败价优惠列表
//        $aDiscountListFail = json_decode($aQuotation[self::NAME_DISCOUNT_DESC_FAIL], true);
//        $aDiscountListFail = is_array($aDiscountListFail) ? $aDiscountListFail : [];
//        // anycar multi_info字段
//        $aMultiInfo = json_decode($aQuotation[self::NAME_MULTI_INFO], true);
//
//        $aFormatDiscountList     = $this->_generateDiscountDescArray($aDiscountList);
//        $aFormatDiscountListFail = $this->_generateDiscountDescArray($aDiscountListFail);
//
//        $aDiscountDesc = [
//            $aFormatDiscountList,
//            $aFormatDiscountListFail,
//        ];
//
//        $aFormatDiscountListAnycar = $this->_formatMultiBillInfo($aMultiInfo);
//        $iCarpoolSeatNum           = json_decode($aQuotation[self::NAME_CARPOOL_SEAT_NUM], true);
//
//        if ($this->_hitMultiFailFee($aOrderNTuple)) {
//            // 只有拼成乐走到这里
//            // map[fee_type]"discount_desc"
//            $aFormatDiscountListFail = [];
//            foreach ($aDiscountListFail as $iFeeType => $sDiscount) {
//                $aTempDiscount = json_decode($sDiscount, true);
//                if (!is_array($aTempDiscount)) {
//                    continue;
//                }
//
//                $aFormatDiscountListFail[$iFeeType] = $this->_generateDiscountDescArray($aTempDiscount);
//            }
//
//            $iDefaultFeeType = 0;
//            $aDiscountDesc   = $aFormatDiscountListFail;
//            $aDiscountDesc[$iDefaultFeeType] = $aFormatDiscountList;
//        }
//
//        $urlParams = array(
//            'business_id'    => $aOrderNTuple['business_id'],
//            'combo_type'     => $aOrderNTuple['combo_type'],
//            'car_level'      => $aOrderNTuple['require_level'],
//            'departure_time' => $aQuotation[self::NAME_DEPARTURE_TIME],
//            'flat'           => $aQuotation[self::NAME_FROM_LAT],
//            'flng'           => $aQuotation[self::NAME_FROM_LNG],
//            'price_token'    => $aQuotation[self::NAME_PRICE_TOKEN],
//        );
//        // 头部banner位
//        $aFeeDesc['head_banner'] = [];
//
//        $aScenePrice = $aQuotation[self::CARPOOL_SCENE_BILL]['scene_price'];
//        if (BizHorae::isLowPriceCarpoolV2($aOrderNTuple) && !empty($aScenePrice) && is_array($aScenePrice)) {
//            foreach ($aFeeDesc['items'] as $iIndex => $item) {
//                $aExpectOption = $item['option'] ?? ['pool_num' => -1, 'seat_num' => -1];
//
//                foreach ($aScenePrice as $aScenePriceElement) {
//                    if ($aScenePriceElement['option']['pool_num'] == $aExpectOption['pool_num']
//                        && $aScenePriceElement['option']['seat_num'] == $aExpectOption['seat_num']
//                        && $aScenePriceElement['option']['with_commute_card'] == $aExpectOption['with_commute_card']
//                    ) {
//                            $aFeeDesc['items'][$iIndex]['_carpool_scene_discount_item']    = $this->_generateDiscountDescArray($aScenePriceElement['discount_item'] ?? []);
//                            $aFeeDesc['items'][$iIndex]['_carpool_scene_discount_item_v2'] = $this->_generateDiscountDescArray($aScenePriceElement['discount_item_v2'] ?? []);
//                    }
//                }
//            }
//        }
//
//        $aResponse = array(
//            // 普通的产品 这两个字段不为空
//            'discount_desc'        => $aDiscountDesc,
//            'fee_desc'             => $aFeeDesc,
//            // anycar 这两个字段不为空
//            'discount_desc_anycar' => $aFormatDiscountListAnycar,
//            'carpool_seat_num'     => $iCarpoolSeatNum,
//            'url_params'           => $urlParams,
//            'combo_type'           => $aQuotation[self::NAME_COMBO_TYPE] ?? 0,
//            'estimate_time'        => $aQuotation[self::NAME_ESTIMATE_TIME] ?? 0,
//            'n_tuple'              => $aOrderNTuple,
//            'compare_price'        => $aQuotation[self::NAME_COMPARE_FEE] ?? 0,
//            'count_price_type'     => $aQuotation[self::COUNT_PRICE_TYPE] ?? 0,
//            'area'                 => (int)($aQuotation[self::NAME_AREA]) ?? 0,
//
//            'default_pay_type'     => $aQuotation[self::DEFAULT_PAY_TYPE] ?? 0,
//            'is_mixed_payment'     => $aQuotation[self::IS_MIXED_PAYMENT] ?? 0,
//            'mixed_payment_info'   => $aQuotation[self::MIXED_PAYMENT_INFO] ?? '',
//            'price_privilege_type' => $aQuotation[self::PRICE_PRIVILEGE_TYPE] ?? 0,
//            'currency'             => $aQuotation[self::NAME_CURRENCY] ?? 'CNY',
//        );
//        return $aResponse;
//    }

    // 注释-待删
//    /**
//     * _generateDiscountDescArray
//     * @param array $aRawDiscountArray ??
//     * @return array
//     */
//    private function _generateDiscountDescArray($aRawDiscountArray) {
//        $aFormatDiscountList = [];
//        foreach ($aRawDiscountArray as $aDiscountItem) {
//            if (0 != abs($aDiscountItem['amount'] ?? 0)) {
//                $aFormatDiscountList[$aDiscountItem['type']] = array(
//                    'amount'           => $aDiscountItem['amount'],
//                    'coupon_type'      => $aDiscountItem['coupon_type'] ?? '',
//                    'custom_tag'       => $aDiscountItem['custom_tag'] ?? '',
//                    'coupon_id'        => $aDiscountItem['coupon_id'] ?? '',
//                    'coupon_discount'  => $aDiscountItem['coupon_discount'] ?? '',
//                    'deduction_amount' => $aDiscountItem['deduction_amount'], // 分级别的金额
//                    'deduction_mile'   => $aDiscountItem['deduction_mile'], // 米级别的里程
//                );
//            }
//        }
//
//        return $aFormatDiscountList;
//    }

// 注释-待删
//    /**
//     * @param array $aOrderNTuple n_tuple
//     * @return bool
//     */
//    private function _hitMultiFailFee($aOrderNTuple) {
//        $oFeatureToggle = (new Apollo())->featureToggle(
//            'multi_fail_fee',
//            [
//                'key'           => $this->aParams['estimate_id'] ?? 0,
//                'product_id'    => $aOrderNTuple['product_id'],
//                'combo_type'    => $aOrderNTuple['combo_type'],
//                'require_level' => $aOrderNTuple['require_level'],
//                'carpool_type'  => $aOrderNTuple['carpool_type'],
//            ]
//        );
//
//        return $oFeatureToggle->allow();
//    }

    // 注释-待删
//    /**
//     * @desc 获取带产品线key的列表.
//     *
//     * @param array $aMultiInfo $aMultiInfo
//     *
//     * @return array
//     */
//    private function _formatMultiBillInfo($aMultiInfo) {
//        $aMultiInfo        = is_array($aMultiInfo) ? $aMultiInfo : [];
//        $aMultiInfoWithKey = [];
//        foreach ($aMultiInfo as $aItem) {
//            $sGroupKey          = AnyCarCommonLogic::getGroupKey($aItem);
//            $aItem['group_key'] = $sGroupKey;
//
//            // $aItem里没有business_id
//            if (!isset($aItem['business_id'])) {
//                $iProductId           = (int)($aItem['product_id']);
//                $aItem['business_id'] = Product::getCommonProductId($iProductId);
//            }
//
//            $sBusinessName          = OrderComLogic::getProductDisplayNameByCarLevel($aItem['require_level'], $aItem['combo_type']);
//            $aItem['business_name'] = $sBusinessName;
//
//            if (!empty($aItem['level_type'])) {
//                $aBusinessNameConfig    = Language::getDecodedTextFromDcmp('config_anycar-business_name_text');
//                $aItem['business_name'] = $aBusinessNameConfig[$sGroupKey];
//            }
//
//            $fPriceValue    = $aItem['discount_fee'] ?? 0;
//            $aItem['price'] = $fPriceValue;
//
//            $aMultiInfoWithKey[$sGroupKey] = $aItem;
//        }
//
//        return $aMultiInfoWithKey;
//    }
}
