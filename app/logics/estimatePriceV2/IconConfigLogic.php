<?php
/**
 * Created by PhpStorm.
 * <AUTHOR> guozhiying <<EMAIL>>
 * Date: 2020/11/9
 * Time: 7:22 PM
 */

namespace PreSale\Logics\estimatePriceV2;

use <PERSON><PERSON>\Apollo\Apollo as ApolloV2;
use BizLib\Utils\Language;
use BizLib\Log as NuwaLog;

/**
 * Class IconConfigLogic
 * @package PreSale\Logics\estimatePriceV2
 */
class IconConfigLogic
{

    private static $_oInstance = null;

    private $_aInfo          = [];
    private $_aCommonInfo    = [];
    private $_aPassengerInfo = [];
    private $_aOrderInfo     = [];

    private $_aCarIconConf = [];

    const APOLLO_NS_LOCAL_ICON_CONF    = 'form_activity_caricon_config';
    const APOLLO_NS_GLOBAL_ICON_CONF   = 'global_car_icon_conf';
    const APOLLO_FILE_GLOBAL_ICON_CONF = 'global_car_icon_config';
    /**
     * @return null|IconConfigLogic
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }


    /**
     * @param array $aParams 参数
     * @return mixed
     */
    public function loadInfo(array $aParams) {
        if (empty($aParams) || empty(current($aParams))) {
            return;
        }

        $this->_aInfo          = current($aParams);
        $this->_aCommonInfo    = $this->_aInfo['common_info'];
        $this->_aPassengerInfo = $this->_aInfo['passenger_info'];
        $this->_aOrderInfo     = $this->_aInfo['order_info'];
        return;
    }


    /**
     * @return array
     */
    public function getCarIconConfig() {
        return $this->_aCarIconConf;
    }


    /**
     * @return mixed
     */
    public function loadCarIconConfig() {
        if (!empty($this->_aOrderInfo['area'])) {
            $oLocalConfig = ApolloV2::getInstance()->getConfigsByNamespaceAndConditions(
                self::APOLLO_NS_LOCAL_ICON_CONF,
                array('city_id' => $this->_aOrderInfo['area'])
            );
            list($bOk, $aLocalConfigs) = $oLocalConfig->getAllConfigData();

            if ($bOk && !empty($aLocalConfigs)) {
                $aLocalConfig = current(array_values($aLocalConfigs));
                $this->_parsedCarIconConfig($aLocalConfig);
            }
        }

        if (empty($this->_aCarIconConf)) {
            $oGlobalConfig = ApolloV2::getInstance()->getConfigResult(
                self::APOLLO_NS_GLOBAL_ICON_CONF,
                self::APOLLO_FILE_GLOBAL_ICON_CONF
            );

            list($bOk, $aGlobalConfig) = $oGlobalConfig->getAllConfig();

            if ($bOk && !empty($aGlobalConfig)) {
                $this->_parsedCarIconConfig($aGlobalConfig);
            }
        }
    }


    /**
     * @param array $aAllConfig 参数
     * @return mixed
     */
    private function _parsedCarIconConfig($aAllConfig) {
        foreach ($aAllConfig['activity_conf'] as $aConf) {
            //强行check的，必须配置，且需要符合
            if (empty($aConf['activity_rules']) || empty($aConf['car_icon_list'])) {
                continue;
            }

            if ($this->_checkActivityRules($aConf['activity_rules'])) {
                if (!empty($aConf['car_icon_list'])) {
                    foreach ($aConf['car_icon_list'] as $oneConf) {
                        $this->_aCarIconConf[$oneConf['pc_id']]['gray_icon']  = $oneConf['gray_icon'];
                        $this->_aCarIconConf[$oneConf['pc_id']]['light_icon'] = $oneConf['light_icon'];
                    }

                    break;
                }
            }
        }

        return;
    }


    /**
     * @param array $aActivityRules 参数
     * @return bool
     */
    private function _checkActivityRules($aActivityRules) {
        if (empty($aActivityRules['valid']) || empty($aActivityRules['open_time'])) {
            return false;
        }

        $iStartTime = strtotime($aActivityRules['open_time'][0]);
        $iEndTime   = strtotime($aActivityRules['open_time'][1]);
        if (empty($iStartTime) || empty($iEndTime) || time() < $iStartTime || time() > $iEndTime) {
            return false;
        }

        if (!empty($aActivityRules['lang']) && $this->_aCommonInfo['lang'] != $aActivityRules['lang']) {
            return false;
        }

        if (!empty($aActivityRules['source_control'])) {
            $bHitSource = false;
            foreach ($aActivityRules['source_control'] as $oneSource) {
                if ($oneSource['access_key_id'] == $this->_aCommonInfo['access_key_id']
                    && version_compare($this->_aCommonInfo['app_version'], $oneSource['app_version']) >= 0
                ) {
                    $bHitSource = true;
                    break;
                }
            }

            if (!$bHitSource) {
                return false;
            }
        }

        if (!empty($aActivityRules['launch_apollo']) && !ApolloV2::getInstance()->featureToggle(
            $aActivityRules['launch_apollo'],
            array(
                'key'     => $this->_aPassengerInfo['phone'],
                'phone'   => $this->_aPassengerInfo['phone'],
                'pid'     => $this->_aPassengerInfo['pid'],
                'uid'     => $this->_aPassengerInfo['uid'],
                'channel' => $this->_aOrderInfo['channel'],
            )
        )->allow()
        ) {
            return false;
        }

        return true;
    }
}
