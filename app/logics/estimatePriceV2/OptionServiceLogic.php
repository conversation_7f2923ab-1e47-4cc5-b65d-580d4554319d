<?php
/**
 * Created by Php<PERSON>torm.
 * <AUTHOR> kaweh <<EMAIL>>
 * Date: 2020/4/21
 * Time: 上午11:49
 */

namespace PreSale\Logics\estimatePriceV2;

use BizCommon\Infrastructure\Repository\UfsRepository;
use BizCommon\Logics\Activity\ActivityConfigManager;
use BizCommon\Models\Rpc\ImMessageRpc;
use BizLib\Client\DosClient;
use BizLib\Client\UfsClient;
use BizLib\Config as NuwaConfig;
use BizLib\Libraries\RedisDB;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\Language;
use BizLib\Utils\UtilHelper;
use Nuwa\ApolloSDK\Apollo;

/**
 * Class OptionServiceLogic
 * @package PreSale\Logics\estimatePriceV2
 */
class OptionServiceLogic
{
    const HK_TAXI_OPTION_SERVICE_TIME = 600;

    const IM_HIGHLIGHT_TEMPLATE = "<H>%s</H>\n"; // IM 消息中字符串格式高亮文本

    const APOLLO_FROM_PRE_SALE = 'pre-sale';

    protected static $_oInstance = null;

    // apollo 配置的静态缓存
    private static $_apolloOptionServiceConfig;
    private static $_apolloCitySupportServiceConfig;

    /**
     * constructor.
     */
    private function __construct() {
    }

    /**
     * @return null|OptionServiceLogic
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * @param array $aInfo info
     * @return array
     * @throws \Exception e
     */
    public static function getService($aInfo) {
        if (version_compare($aInfo['common_info']['app_version'], '6.0.0') < 0) {
            return [];
        }

        $iProductId     = $aInfo['order_info']['product_id'];
        $iPid           = $aInfo['passenger_info']['pid'];
        $oApollo        = new Apollo();
        $oFeatureToggle = $oApollo->featureToggle(
            'pre_sale_option_open_control',
            [
                'key'              => $iPid,
                'product_id'       => $iProductId,
                'city'             => $aInfo['order_info']['area'],
                'phone'            => $aInfo['passenger_info']['phone'],
                'page_type'        => $aInfo['common_info']['page_type'],
                'product_category' => $aInfo['order_info']['product_category'],
            ]
        );
        if (!$oFeatureToggle->allow()) {
            return [];
        }

        $aServiceConfig = self::_getServiceConfig($aInfo['order_info']);
        if (empty($aServiceConfig)) {
            return [];
        }

        $sLang = Language::getCurrentLanguage();
        $aServiceConfig['title']            = $aServiceConfig['title'][$sLang];
        $aServiceConfig['set_option_title'] = $aServiceConfig['set_option_title'][$sLang];
        if (Language::EN_US == $sLang) {
            $aServiceConfig['im_contact'] = $aServiceConfig['im_contact_en'];
        }

        foreach ($aServiceConfig['service_list'] as $key => $aItem) {
            $aServiceConfig['service_list'][$key]['title'] = $aItem['title'][$sLang];
            $aServiceConfig['service_list'][$key]['desc']  = $aItem['desc'][$sLang];
        }

        // 读取用户历史选择
        $aInfo['order_info']['passenger_id'] = $iPid;
        // {"version":"v2","title":"称呼","remark":"备注","options"：[{"option_id":102,"count":1 } ]}
        $aHisChoseOption = \BizCommon\Logics\Order\OptionServiceLogic::getHisOptionService($aInfo['order_info']);
        if (empty($aHisChoseOption) || 'v2' != $aHisChoseOption['version'] || empty($aHisChoseOption['options'])) {
            return $aServiceConfig;
        }

        foreach ($aHisChoseOption['options'] as $aItem) {
            foreach ($aServiceConfig['service_list'] as $key => $aService) {
                if ($aItem['option_id'] == $aService['option_id']) {
                    $aServiceConfig['service_list'][$key]['selected_count'] = $aItem['count'];
                }
            }
        }

        return $aServiceConfig;
    }

    /**
     * @param array $aOrderInfo $aOrderInfo
     * @return array
     */
    private static function _getServiceConfig($aOrderInfo) {
        if (empty($aOrderInfo)) {
            return [];
        }

        $aServiceConfig = self::_getAllOptionService($aOrderInfo);
        if (empty($aServiceConfig)) {
            return [];
        }

        $aCitySupportService = self::_getCitySupportService($aOrderInfo);
        if (!empty($aCitySupportService) && !empty($aCitySupportService['option_service'])) {
            $aNewServiceList = [];
            foreach ($aServiceConfig['service_list'] as $key => $aService) {
                if (in_array($aService['option_id'], $aCitySupportService['option_service'])) {
                    $aNewServiceList[] = $aService;
                }
            }

            $aServiceConfig['service_list'] = $aNewServiceList;
        }

        return $aServiceConfig;
    }

    /**
     * @param array $aOrderInfo orderInfo
     * @return array|mixed
     */
    private static function _getAllOptionService($aOrderInfo) {
        $key        = $aOrderInfo['product_id'];
        $aCondition = ['product_id' => $key];

        if (isset(self::$_apolloOptionServiceConfig[$key])) {
            return self::$_apolloOptionServiceConfig[$key];
        }

        list($bOk, $aConfig) = Apollo::getInstance()->getConfigsByNamespaceAndConditions(
            'option_service_config',
            $aCondition
        )->getAllConfigData();
        if (!$bOk || empty($aConfig) || empty(array_values($aConfig)[0])) {
            $result = [];
        } else {
            $result = array_values($aConfig)[0];
        }

        self::$_apolloOptionServiceConfig[$key] = $result;
        return $result;
    }

    /**
     * @param array $aOrderInfo $aOrderInfo
     * @return array
     */
    private static function _getCitySupportService($aOrderInfo) {
        $key        = $aOrderInfo['area'];
        $aCondition = ['city' => $key ];

        if (isset(self::$_apolloCitySupportServiceConfig[$key])) {
            return self::$_apolloCitySupportServiceConfig[$key];
        }

        list($bOk, $aConfig) = Apollo::getInstance()->getConfigsByNamespaceAndConditions(
            'city_support_service_config',
            $aCondition
        )->getAllConfigData();
        if (!$bOk || empty($aConfig) || empty(array_values($aConfig)[0])) {
            $result = [];
        } else {
            $result = array_values($aConfig)[0];
        }

        self::$_apolloCitySupportServiceConfig[$key] = $result;
        return array_values($aConfig)[0];
    }

    /**
     * @param array $aParams params
     * @return array
     * @throws \Exception e
     */
    public static function getBaseConfig(array $aParams) {
        $aServiceConfig = self::_getServiceConfig($aParams);
        if (empty($aServiceConfig)) {
            return [];
        }

        $sLang = Language::getCurrentLanguage();
        $aServiceConfig['title']          = $aServiceConfig['title'][$sLang];
        $aServiceConfig['head']           = $aServiceConfig['head'][$sLang];
        $aServiceConfig['head_title']     = $aServiceConfig['head_title'][$sLang];
        $aServiceConfig['head_sub_title'] = $aServiceConfig['head_sub_title'][$sLang];

        foreach ($aServiceConfig['service_list'] as $key => $aItem) {
            $aServiceConfig['service_list'][$key]['title']     = $aItem['title'][$sLang];
            $aServiceConfig['service_list'][$key]['desc']      = $aItem['desc'][$sLang];
            $aServiceConfig['service_list'][$key]['toast']     = $aItem['toast'][$sLang];
            $aServiceConfig['service_list'][$key]['broadcast'] = $aItem['broadcast'][$sLang];
        }

        return $aServiceConfig;
    }

    // 注释-待删
//    /**
//     * @param array $aParams params
//     * @return void
//     */
//    public static function submitServiceV2(array $aParams) {
//        if (empty($aParams['options'])) {
//            return;
//        }
//
//        // save to ufs
//        $aUploadOptions      = json_decode($aParams['options'], true);
//        $aOptions['version'] = 'v2';
//        $aOptions['title']   = $aUploadOptions['title'];
//        $aOptions['remark']  = $aUploadOptions['remark'];
//        if (!empty($aUploadOptions['prefer_option'])) {
//            foreach ($aUploadOptions['prefer_option'] as $aItem) {
//                $aOptions['options'][] = [
//                    'option_id' => $aItem['id'],
//                    'count'     => (int)$aItem['is_selected'],
//                ];
//            }
//        }
//
//        if (empty($aParams['oid'])) {
//            \BizCommon\Logics\Order\OptionServiceLogic::setHisService(
//                $aOptions,
//                $aParams['passenger_info']['pid'],
//                $aParams['require_level']
//            );
//            return;
//        }
//
//        // send im msg
//        $aDecodeOid = UtilHelper::decodeId($aParams['oid']);
//        $oDosClient = new DosClient();
//        $aOrderInfo = $oDosClient->getOrderInfo($aDecodeOid['oid'], $aDecodeOid['district']);
//        $aOrderInfo = $aOrderInfo['result']['order_info'];
//
//        // 校验本次选择是否有修改
//        // 历史不透出单程有效偏好
//        $aServiceConfig = \BizCommon\Logics\Order\OptionServiceLogic::getAllOptionService($aOrderInfo);
//        $bFixed         = true;
//        $aHisOptions    = \BizCommon\Logics\Order\OptionServiceLogic::getHisOptionService($aOrderInfo);
//        if (!empty($aHisOptions['options'])) {
//            $aHisOptionIds    = [];
//            $aUploadOptionIds = [];
//
//            foreach ($aHisOptions['options'] as $aHisItem) {
//                foreach ($aServiceConfig['service_list'] as $aServiceItem) {
//                    if ($aHisItem['count'] > 0 && ($aHisItem['option_id'] == $aServiceItem['option_id']) && !$aServiceItem['once']) {
//                        $aHisOptionIds[] = $aHisItem['option_id'];
//                    }
//                }
//            }
//
//            foreach ($aOptions['options'] as $aItem) {
//                if ($aItem['count'] > 0) {
//                    $aUploadOptionIds[] = $aItem['option_id'];
//                }
//            }
//
//            if (!array_diff($aHisOptionIds, $aUploadOptionIds) && !array_diff($aUploadOptionIds, $aHisOptionIds)) {
//                $bFixed = false;
//            }
//        }
//
//        \BizCommon\Logics\Order\OptionServiceLogic::setHisService(
//            $aOptions,
//            $aOrderInfo['passenger_id'],
//            $aOrderInfo['require_level']
//        );
//
//        // 写入订单偏好, 因为是用户手动提交，所以把 source 改成 1
//        $aOptionsForOrder           = $aOptions;
//        $aOptionsForOrder['source'] = \BizCommon\Logics\Order\OptionServiceLogic::ORDER_PREFER_SETTING_FROM_USER_UPLOAD;
//        \BizCommon\Logics\Order\OptionServiceLogic::setServiceForOrder(
//            $aOptionsForOrder,
//            $aOrderInfo
//        );
//
//        // 如果没有修改，那就返回，不发送 IM 消息
//        if (!$bFixed) {
//            return;
//        }
//
//        // send im 发生在用户下单后，还有一次机会修改的情况下
//        $sDmcKey     = 'option_service_send_im_to_passenger_with_fix_v2';
//        $sLang       = Language::ZH_CN;
//        $sContent    = '';
//        $sTTSContent = '';
//        foreach ($aOptions['options'] as $aHisItem) {
//            foreach ($aServiceConfig['service_list'] as $aServiceItem) {
//                if ($aHisItem['count'] > 0 && ($aHisItem['option_id'] == $aServiceItem['option_id'])) {
//                    $sTag         = $aServiceItem['title'][$sLang];
//                    $sContent    .= "\n - $sTag";
//                    $sTTSContent .= " $sTag";
//                }
//            }
//        }
//
//        // 电话联系偏好下线
////        $bIsCloseApollo = (new \Nuwa\ApolloSDK\Apollo())->featureToggle(
////            'gs_prefer_setting_phone_connect_switch',
////            [
////                'key'        => $aParams['passenger_info']['pid'],
////                'product_id' => $aParams['product_id'],
////                'pid'        => $aParams['passenger_info']['pid'],
////                'from'       => self::APOLLO_FROM_PRE_SALE,
////
////            ]
////        )->allow();
//        $bIsCloseApollo = true;
//
//        // 这里的 \n 写的有点乱，不过在目前的这个写法下（pre-sale 里面重复代码太多)，不好安排抽象了，等待和豪车共建的时候抽离
//        if (!$bIsCloseApollo) {
//            if (!empty($sContent)) {
//                // 如果没有包含 “电话联系” 那么就加入消息联系进入
//                if (!\BizCommon\Logics\Order\OptionServiceLogic::hasService($aOrderInfo, \BizCommon\Logics\Order\OptionServiceLogic::SERVICE_PHONE_CONTACT, $aOptions)) {
//                    $sContent    = "\n - " . $aServiceConfig['im_contact'] . $sContent;
//                    $sTTSContent = ' ' . $aServiceConfig['im_contact'] . $sTTSContent;
//                }
//            } else {
//                // 为空那么直接变成下 "消息联系"
//                $sContent    = "\n - " . $aServiceConfig['im_contact'];
//                $sTTSContent = ' ' . $aServiceConfig['im_contact'];
//            }
//        }
//
//        $aArguments = [
//            'option_service' => $sContent . "\n",
//            'oid'            => $aParams['oid'],
//        ];
//        $oIm        = new ImMessageRpc();
//        // 发送给乘客
//        $oIm->sendImMessageByDMC($aOrderInfo, [], 2, $sDmcKey, 3, $aArguments);
//
//        // 发给司机的内容加上高亮
//        $sContent = sprintf(self::IM_HIGHLIGHT_TEMPLATE,$sContent);
//        $aArguments['option_service'] = $sContent;
//        // 因为 tts 语音默认播报会使用 option_service ，但是现在在添加了高亮和换行的情况下，就要过滤掉特殊字符 \H \h  -   三个标记字符, 所以重新配置了一个 "option_service_tts" 用于播报
//        $aArguments['option_service_tts'] = $sTTSContent;
//        // 发给司机
//        $sDmcKey = 'option_service_send_im_to_driver_with_fix_v2';
//        if (\BizCommon\Logics\Order\OptionServiceLogic::hasService($aOrderInfo, \BizCommon\Logics\Order\OptionServiceLogic::SERVICE_SIMPLIFY_WORDS, $aOptions)) {
//            $sDmcKey = 'option_service_send_im_to_driver_with_fix_with_simplify_words_v2';
//        }
//
//        $oIm->sendImMessageByDMC($aOrderInfo, [], 1, $sDmcKey, 3, $aArguments);
//    }

    // 注释-待删
//    /**
//     * 提交香港可选服务
//     * @param array $aParams params
//     * @return void
//     */
//    public static function submitHkService(array $aParams) {
//        if (empty($aParams['options'])) {
//            return;
//        }
//
//        $aUploadOptions = json_decode($aParams['options'], true);
//
//        $oRedisdb = RedisDB::getInstance();
//        //红包存redis
//        $sTipKey = UtilsCommon::getRedisPrefix(P_HK_TAXI_TIP_INFO).$aParams['passenger_info']['pid'].'_'.$aParams['product_id'];
//        $sTipRes = '';
//        if (!empty($aUploadOptions['dispatch_fee'])) {
//            $sTipRes = json_encode($aUploadOptions['dispatch_fee']);
//        }
//
//        $oRedisdb->setex($sTipKey, self::HK_TAXI_OPTION_SERVICE_TIME, $sTipRes);
//
//        $sCommentRes = '';
//        if (!empty($aUploadOptions['comment_option'])) {
//            $sCommentRes = json_encode($aUploadOptions['comment_option']);
//        }
//
//        //捎话存redis
//        $sCommentKey = UtilsCommon::getRedisPrefix(P_HK_TAXI_COMMENT_INFO).$aParams['passenger_info']['pid'].'_'.$aParams['product_id'];
//        $oRedisdb->setex($sCommentKey, self::HK_TAXI_OPTION_SERVICE_TIME, $sCommentRes);
//    }

    // 注释-待删
//    /**
//     * @param array $aParams params
//     * @return array
//     * @throws \Exception e
//     */
//    public static function getServiceV2(array $aParams) {
//        $aReturn = UtilsCommon::getErrMsg(GLOBAL_SUCCESS);
//        if (version_compare($aParams['app_version'], '6.0.0') < 0) {
//            return $aReturn;
//        }
//
//        $aOrderInfo = [];
//        if (!empty($aParams['oid'])) {
//            $aDecodeOid = UtilHelper::decodeId($aParams['oid']);
//            $oDosClient = new DosClient();
//            $aOrderInfo = $oDosClient->getOrderInfo($aDecodeOid['oid'], $aDecodeOid['district']);
//            $aOrderInfo = $aOrderInfo['result']['order_info'];
//
//            $aParams['product_id']    = $aOrderInfo['product_id'];
//            $aParams['require_level'] = $aOrderInfo['require_level'];
//        }
//
//        $oApollo        = new Apollo();
//        $oFeatureToggle = $oApollo->featureToggle(
//            'pre_sale_option_open_control',
//            [
//                'key'        => $aParams['passenger_info']['pid'],
//                'product_id' => $aParams['product_id'],
//                'city'       => $aParams['area'],
//                'phone'      => $aParams['passenger_info']['phone'],
//            ]
//        );
//
//        $aBaseConfig = self::getBaseConfig($aParams);
//        if (empty($aBaseConfig)) {
//            return $aReturn;
//        }
//
//        $aReturn['data'] = [
//            'title'          => $aBaseConfig['head_title'],
//            'sub_title'      => $aBaseConfig['head_sub_title'],
//            'head_img'       => $aBaseConfig['head_img'],
//            'start_bg_color' => $aBaseConfig['start_bg_color'],
//            'end_bg_color'   => $aBaseConfig['end_bg_color'],
//            'theme'          => 1,
//            'disable'        => 0,
//            'show_tab'       => 3,
//        ];
//        $aPreferInfo     = [
//            'head'              => $aBaseConfig['head'],
//            'head_link'         => $aBaseConfig['head_link'],
//            'is_support_title'  => $aBaseConfig['is_support_title'],
//            'is_support_remark' => $aBaseConfig['is_support_remark'],
//            'is_im_direct_send' => $aBaseConfig['is_im_direct_send'],
//            'title'             => '',
//            'remark'            => '',
//            'prefer_option'     => [],
//        ];
//
//        if (!empty($aBaseConfig['service_list'])) {
//            foreach ($aBaseConfig['service_list'] as $aItem) {
//                $aPreferInfo['prefer_option'][] = [
//                    'id'         => $aItem['option_id'],
//                    'text'       => $aItem['title'],
//                    'is_select'  => $aItem['is_select'],
//                    'light_icon' => $aItem['light_icon'],
//                    'gray_icon'  => $aItem['gray_icon'],
//                    'toast'      => $aItem['toast'],
//                    'once'       => $aItem['once'],
//                ];
//            }
//        }
//
//        $aReturn['data']['prefer_info'] = $aPreferInfo;
//
//        // TODO 提前
//        // 上面不开城的 在这里赋值, 应该提前的 不然 prefer_info 就白赋了
//        if (!$oFeatureToggle->allow()) {
//            $aReturn['data']['prefer_info'] = null;
//            return $aReturn;
//        }
//
//        //设置活动文案
//        //覆盖 title 和 sub_title，head_img ，添加 sub_title_link 字段
//        self::_setActivityData($aReturn, $aParams);
//
//        // 如果历史有选择，则展示历史
//        // 历史不展示一次性的
//        $aInfo['passenger_id']  = $aParams['passenger_info']['pid'];
//        $aInfo['require_level'] = $aParams['require_level'];
//        $aHisOption = \BizCommon\Logics\Order\OptionServiceLogic::getHisOptionService($aInfo);
//        if (!empty($aHisOption) && 'v2' == $aHisOption['version']) {
//            // 目前没什么用，我看一直为空
//            $aPreferInfo['title']  = $aHisOption['title'];
//            $aPreferInfo['remark'] = $aHisOption['remark'];
//
//            if (!empty($aPreferInfo['prefer_option']) && !empty($aHisOption['options'])) {
//                foreach ($aPreferInfo['prefer_option'] as $key => $aItem) {
//                    $aPreferInfo['prefer_option'][$key]['is_select'] = false;
//                    foreach ($aHisOption['options'] as $aHisItem) {
//                        if ($aItem['id'] == $aHisItem['option_id'] && $aHisItem['count'] > 0) {
//                            // 代表用户选择了这个选项
//                            $aPreferInfo['prefer_option'][$key]['is_select'] = true;
//                            break;
//                        }
//                    }
//                }
//            }
//
//            $aReturn['data']['prefer_info'] = $aPreferInfo;
//        }
//
//        // 还没有调用 pNewOrder 那就返回
//        if (empty($aParams['oid'])) {
//            return $aReturn;
//        }
//
//        $aHisOption = \BizCommon\Logics\Order\OptionServiceLogic::getServiceFromOrder($aOrderInfo);
//
//        // 已经设置过
//        // bIsSet 代表用户是否设置过自己的历史
//        $bIsSet = false;
//        if (!empty($aHisOption) && 'v2' == $aHisOption['version']) {
//             // 这段逻辑是为了 如果 订单的 source 等于 1, 代表是用户自己设定的， 那么就不能修改, ，因为产品需要，临时先关闭
//            // if (!empty($aHisOption['source']) && $aHisOption['source'] === \BizCommon\Logics\Order\OptionServiceLogic::ORDER_PREFER_SETTING_FROM_USER_UPLOAD) {
//            //     $bIsSet = true;
//            //     $aReturn['data']['disable'] = 1;
//            // }
//        } else {
//            $aHisOption = \BizCommon\Logics\Order\OptionServiceLogic::getHisOptionService($aOrderInfo);
//        }
//
//        // 上车前才可修改
//        if (\BizLib\Constants\OrderSystem::ST_ARRIVED < $aOrderInfo['order_status']) {
//            $aReturn['data']['disable'] = 1;
//        }
//
//        // {"version":"v2","title":"称呼","remark":"备注","options"：[{"option_id":102,"count":1 } ]}
//        if (!is_array($aHisOption) || 'v2' != $aHisOption['version']) {
//            return $aReturn;
//        }
//
//        $aPreferInfo['title']  = $aHisOption['title'];
//        $aPreferInfo['remark'] = $aHisOption['remark'];
//
//        // TODO 应该先读订单，再读历史 现在是拿的订单的做一次逻辑
//        if (!empty($aPreferInfo['prefer_option']) && !empty($aHisOption['options'])) {
//            foreach ($aPreferInfo['prefer_option'] as $key => $aItem) {
//                $aPreferInfo['prefer_option'][$key]['is_select'] = false;
//                foreach ($aHisOption['options'] as $aHisItem) {
//                    if ($aItem['id'] == $aHisItem['option_id'] && $aHisItem['count'] > 0) {
//                        // 如果用户已经设置过历史的
//                        if ($bIsSet) {
//                            $aPreferInfo['prefer_option'][$key]['is_select'] = true;
//                        } else {
//                            // 如果没有设置过，那么不是一次性的才选择，是一次性的就不选择了
//                            if (!$aItem['once']) {
//                                $aPreferInfo['prefer_option'][$key]['is_select'] = true;
//                            }
//                        }
//
//                        break;
//                    }
//                }
//            }
//        }
//
//        $aReturn['data']['prefer_info'] = $aPreferInfo;
//
//        return $aReturn;
//    }

    // 注释-待删
//    /**
//     * 获取香港的士的可选服务项
//     * @param array $aParams 必要参数
//     * @return array|bool
//     */
//    public static function getHkService(array $aParams) {
//        $aReturn = UtilsCommon::getErrMsg(GLOBAL_SUCCESS);
//        //获取hk_taxi可选服务的配置
//        $aPreferInfo = NuwaConfig::text('option_service', 'hk_taxi') ?? [];
//
//        if (empty($aPreferInfo)) {
//            return $aReturn;
//        }
//
//        $aDispatchFee = [];
//        $aComment     = [];
//
//        $oRedisdb = RedisDB::getInstance();
//        //红包
//        if (!empty($aPreferInfo['dispatch_fee'])) {
//            //从redis获取红包的信息
//            $sTipKey          = UtilsCommon::getRedisPrefix(P_HK_TAXI_TIP_INFO).$aParams['passenger_info']['pid'].'_'.$aParams['product_id'];
//            $sTipRes          = $oRedisdb->get($sTipKey);
//            $aDispatchHistory = [];
//
//            if (!empty($sTipRes)) {
//                $aTipRes = json_decode($sTipRes, true) ?? [];
//                if (!empty($aTipRes['option_id'])) {
//                    $aDispatchHistory[] = $aTipRes['option_id'];
//                }
//            }
//
//            $aDispatchFee['head']       = $aPreferInfo['dispatch_fee']['head'] ?? '';
//            $aDispatchFee['sub_head']   = $aPreferInfo['dispatch_fee']['sub_head'] ?? '';
//            $aDispatchFee['tip_option'] = [];
//            if (!empty($aPreferInfo['dispatch_fee']['tip_option'])) {
//                foreach ($aPreferInfo['dispatch_fee']['tip_option'] as $aOneTipOption) {
//                    $aDispatchFee['tip_option'][] = [
//                        'option_id' => $aOneTipOption['option_id'] ?? '',
//                        'currency'  => $aOneTipOption['currency'] ?? 0,
//                        'text'      => $aOneTipOption['text'] ?? '',
//                        'is_select' => in_array($aOneTipOption['option_id'], $aDispatchHistory) ? true : false,
//                        'icon'      => $aOneTipOption['icon'] ?? '',
//                    ];
//                }
//            }
//        }
//
//        //捎话
//        if (!empty($aPreferInfo['comment'])) {
//            //从redis获取捎话的信息
//            $sCommentKey      = UtilsCommon::getRedisPrefix(P_HK_TAXI_COMMENT_INFO).$aParams['passenger_info']['pid'].'_'.$aParams['product_id'];
//            $sCommentRes      = $oRedisdb->get($sCommentKey);
//            $aDispatchHistory = [];
//
//            if (!empty($sCommentRes)) {
//                $aCommentRes = json_decode($sCommentRes, true) ?? [];
//                foreach ($aCommentRes as $aOneComment) {
//                    if (!empty($aOneComment['option_id'])) {
//                        $aDispatchHistory[] = $aOneComment['option_id'];
//                    }
//                }
//            }
//
//            $aComment['head']           = $aPreferInfo['comment']['head'] ?? '';
//            $aComment['sub_head']       = $aPreferInfo['comment']['sub_head'] ?? '';
//            $aComment['comment_option'] = [];
//            if (!empty($aPreferInfo['comment']['comment_option'])) {
//                foreach ($aPreferInfo['comment']['comment_option'] as $aOneCommentOption) {
//                    $aComment['comment_option'][] = [
//                        'option_id' => $aOneCommentOption['option_id'] ?? '',
//                        'text'      => $aOneCommentOption['text'] ?? '',
//                        'is_select' => in_array($aOneCommentOption['option_id'], $aDispatchHistory) ? true : false,
//                    ];
//                }
//            }
//        }
//
//        $aReturn['data']['dispatch_fee']   = $aDispatchFee ?? [];
//        $aReturn['data']['comment']        = $aComment ?? [];
//        $aReturn['data']['title']          = $aPreferInfo['title'] ?? '';
//        $aReturn['data']['sub_title']      = $aPreferInfo['sub_title'] ?? '';
//        $aReturn['data']['head_img']       = $aPreferInfo['head_img'] ?? '';
//        $aReturn['data']['start_bg_color'] = $aPreferInfo['start_bg_color'] ?? '';
//        $aReturn['data']['end_bg_color']   = $aPreferInfo['end_bg_color'] ?? '';
//        $aReturn['data']['default_select_tab'] = $aPreferInfo['default_select_tab'] ?? '';
//        $aReturn['data']['theme']          = $aPreferInfo['theme'] ?? '';
//        $aReturn['data']['disable']        = $aPreferInfo['disable'] ?? '';
//        $aReturn['data']['show_tab']       = $aPreferInfo['show_tab'] ?? '';
//        $aReturn['data']['sub_title_link'] = $aPreferInfo['sub_title_link'] ?? '';
//
//        return $aReturn;
//    }

   // 注释-待删
//    /**
//     * 设置活动数据
//     * 覆盖 title 和 sub_title，head_img ,start_bg_color,end_bg_color，添加 sub_title_link 字段
//     * @param array $aReturn aReturn
//     * @param array $aParams aParams
//     * @return bool
//     */
//    private static function _setActivityData(&$aReturn, $aParams) {
//        $sPassengerId  = $aParams['passenger_info']['pid'];
//        $iCityId       = $aParams['area'];
//        $iProductId    = $aParams['product_id'];
//        $iRequireLevel = $aParams['require_level'];
//        if (empty($sPassengerId) || empty($iCityId)|| empty($iProductId)|| empty($iRequireLevel)) {
//            return false;
//        }
//
//        $aHitActivityData = ActivityConfigManager::getUserHitActivityData($sPassengerId, $iCityId, $iProductId , $iRequireLevel);
//        if (is_array($aHitActivityData) && !empty($aHitActivityData['activity_id'])) {
//            $aActivityConfig = ActivityConfigManager::getActivityConfigById($aHitActivityData['activity_id'], $aParams['lang']);
//            if (empty($aActivityConfig) || empty($aActivityConfig['option_service_page'])) {
//                return false;
//            }
//
//            $aConfig = $aActivityConfig['option_service_page'];
//            if (!empty($aConfig['head_sub_title'])) {
//                $aReturn['data']['sub_title'] = $aConfig['head_sub_title'];
//            }
//
//            if (!empty($aConfig['head_img'])) {
//                $aReturn['data']['head_img'] = $aConfig['head_img'];
//            }
//
//            if (!empty($aConfig['sub_title_link'])) {
//                $aReturn['data']['sub_title_link'] = $aConfig['sub_title_link'];
//            }
//
//            if (!empty($aConfig['start_bg_color'])) {
//                $aReturn['data']['start_bg_color'] = $aConfig['start_bg_color'];
//            }
//
//            if (!empty($aConfig['end_bg_color'])) {
//                $aReturn['data']['end_bg_color'] = $aConfig['end_bg_color'];
//            }
//        }
//
//        return true;
//    }
}


