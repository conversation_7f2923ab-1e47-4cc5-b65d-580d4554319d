<?php
declare(strict_types=1);
namespace PreSale\Logics\estimatePriceV2;

use BizLib\Config as NuwaConfig;
use BizLib\Constants;
use BizLib\Utils;
use BizCommon\Models\Activity\ActivityMis;

/*
 * 预估相关运营活动类
 * <AUTHOR>
 * @date(2017-03-13)
 */

class ActivityLogic
{
    /**
     * @var \CI_Controller
     */
    protected $_oCI = null;

    /*
     * @var ActivityLogic $_oInstance
     */
    protected static $_oInstance = null;

    /**
     * [$_aParams 所有参数].
     *
     * @var array
     */
    protected $_aParams = array();

    /**
     * [$_aOrderInfo 订单参数信息].
     *
     * @var array
     */
    protected $_aOrderInfo = array();

    /**
     * [$_aPassengerInfo 乘客参数信息].
     *
     * @var array
     */
    protected $_aPassengerInfo = array();

    /**
     * [$_aBillInfo 账单参数信息].
     *
     * @var array
     */
    protected $_aBillInfo = array();

    /**
     * [$_aCommonInfo 公共参数信息].
     *
     * @var array
     */
    protected $_aCommonInfo = array();

    const FROM_WLWEBAPP = 'wlwebapp';   //微信

    /**
     * ActivityLogic constructor.
     *
     * @param array $aParams
     */
    private function __construct(array $aParams) {
        $this->_aParams        = $aParams;
        $this->_aOrderInfo     = $aParams['order_info'];
        $this->_aBillInfo      = $aParams['bill_info'];
        $this->_aPassengerInfo = $aParams['passenger_info'];
        $this->_aCommonInfo    = $aParams['common_info'];
    }

    /**
     * @param array $aParams
     *
     * @return ActivityLogic
     */
    public static function getInstance(array $aParams) {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self($aParams);
        }

        return self::$_oInstance;
    }

    /**
     * 运营活动入口
     * 1.检查是否可用券
     * 2.准备获取优惠券、活动券相关参数
     * 3.请求券系统,获取优惠券
     * 4.请求乘客运营系统,获取运营活动券
     * 5.选择优惠券和活动券中抵扣更多的券作为本次预估展示的券
     * 6.快车险相关逻辑处理
     * 特殊说明:券相关预估展示数据、快车险相关预估展示数据都在这里处理.
     *
     * @return array
     */
    public function execute() {
        $aExtInfo = array(
            'time_cost'        => $this->_aBillInfo['driver_minute'],
            'bubble_id'        => $this->_aBillInfo['estimate_id'],
            'user_type'        => $this->_aCommonInfo['data_type'],
            'is_smart_request' => 0,
            'dynamic_price_id' => $this->_aBillInfo['noncarpool']['dynamic_price_id'],
            'is_guide_request' => $this->_aCommonInfo['guide_request'],
        );
        $this->_aParams['ext_info'] = $aExtInfo;
        //调用券获取券列表
        $this->_aParams['order_info']['is_from_webapp'] = $this->_aCommonInfo['is_from_webapp'];
        $this->_aParams['order_info']['is_from_b2b']    = $this->_aCommonInfo['is_from_b2b'];

        $oCoupon            = CouponLogic::getInstance($this->_aParams);
        $aNormalCoupon      = $aMisData      = $aCarPoolCoupon      = $aWithoutMemberProtectCoupon      = array();
        $aDiversionalCoupon = array();
        //如果可以用券再去请求券系统、乘客运营系统
        $bCarpoolUseCoupon    = $oCoupon->bUseCoupon($this->_aParams['one_key_activity']['activity_switch'], $this->_aParams['carpool_use_coupon'], $this->_aPassengerInfo['phone']);
        $bNoncarpoolUseCoupon = $oCoupon->bUseCoupon($this->_aParams['one_key_activity']['activity_switch'], $this->_aParams['noncarpool_use_coupon'], $this->_aPassengerInfo['phone']);
        if ($bNoncarpoolUseCoupon || $bCarpoolUseCoupon) {
            $aCouponExtInfo = $oCoupon->getCouponExtInfo();
            $aExtInfo      += isset($aCouponExtInfo['ext_info']) ? $aCouponExtInfo['ext_info'] : array();
            $aNormalCoupon  = isset($aCouponExtInfo['noncarpool_coupon_list']) ? $aCouponExtInfo['noncarpool_coupon_list'] : array();
            $aCarPoolCoupon = isset($aCouponExtInfo['carpool_coupon_list']) ? $aCouponExtInfo['carpool_coupon_list'] : array();
            $aWithoutMemberProtectCoupon = $aCouponExtInfo['without_member_protect_coupon_list'] ?? [];
            //导流券
            $aDiversionalCoupon['noncarpool_diversional_coupon_list'] = $aCouponExtInfo['noncarpool_diversional_coupon_list'] ?? array();
            //调用mis运营平台
            //$aMisData = $this->_getMisActivity($aExtInfo, $this->_aBillInfo['is_carpool_open'] && $bCarpoolUseCoupon);
            //$aMisData = $aMisData ? $aMisData : array();
        }

        $aRet = $oCoupon->getCouponFeeInfo($aMisData, $aNormalCoupon, $aCarPoolCoupon, $aWithoutMemberProtectCoupon);
        if ($aDiversionalCoupon && $aRet) {
            $aRet['diversional_info'] = $this->_getDiversionalInfo($aRet, $aDiversionalCoupon);
        }

        //快车险
        $aRet['fast_ensure_detail'] = $bNoncarpoolUseCoupon ? $this->_getFastEncureMsg($aMisData) : [];

        return $aRet;
    }

    /**
     * [getDiversionalInfo //获取导流信息].
     *
     * @param array $aExtInfo
     *
     * @return array
     */
    private function _getDiversionalInfo($aCouponInfo, $aDiversionalCoupon) {
        $aRet = array();
        if (self::FROM_WLWEBAPP != $this->_aCommonInfo['from'] || Utils\CarLevel::DIDI_PUTONG_CAR_LEVEL != $this->_aOrderInfo['require_level']) {
            return $aRet;
        }

        $iNoncarpoolCounponAmount = $aCouponInfo['noncarpool_estimate_detail']['detail']['amount'] ?? 0;
        $fPreferentialMoney       = bcdiv((string) $aDiversionalCoupon['noncarpool_diversional_coupon_list'][0]['coupon_amount'], '100', 2);
        if (isset($aDiversionalCoupon['noncarpool_diversional_coupon_list'][0]['coupon_amount'])
            && $iNoncarpoolCounponAmount < $fPreferentialMoney
        ) {
            //$aRet['noncarpool']['title'] = '使用滴滴APP下单可享' . $fPreferentialMoney . '元优惠';
            $aText = NuwaConfig::text('config_activity', 'activity_webapp_to_native');
            $aRet['noncarpool']['title'] = sprintf($aText['show_webapp'], $fPreferentialMoney);
        }

        return $aRet;
    }

    /**
     * [getMisActivity //预估请求运营活动返回命中信息].
     *
     * @param array $aExtInfo
     * @param array $bWithCarpool 是否带有拼车数据
     *
     * @return array
     */
//    private function _getMisActivity(array $aExtInfo, bool $bWithCarpool) {
//        $this->activityMis = ActivityMis::getInstance();
//        $aPayFee           = array(
//            'fast_car' => $this->_aBillInfo['noncarpool']['basic_total_fee'],
//            'car_pool' => $this->_aBillInfo['carpool']['basic_total_fee'],
//        );
//        if (!empty($this->_aBillInfo['noncarpool']['without_member_protect_basic_fee'])) {
//            $aPayFee['without_member_protect'] = $this->_aBillInfo['noncarpool']['without_member_protect_basic_fee'];
//        }
//
//        $this->_aOrderInfo['channel'] = $this->_aPassengerInfo['channel'];
//        $aMisData = $this->activityMis->getMultiEstimateInfo(
//            $this->_aOrderInfo,
//            $aPayFee,
//            $this->_aCommonInfo['app_version'], // 不使用该参数，故写死在这
//            '',
//            $this->_aPassengerInfo,
//            $bWithCarpool,
//            $this->_aBillInfo['noncarpool']['dynamic_times'],
//            $aExtInfo
//        );
//
//        //activityMis 切Pope
//        $aPopePassengerInfo = $this->_aPassengerInfo;
//        if (!empty($aPopePassengerInfo)) {
//            $aPopePassengerInfo['origin_id'] = (int)($this->_aCommonInfo['origin_id']);
//        }
//
//        $aPopeData = $this->activityMis->getMultiPopeInfo(
//            $this->_aOrderInfo,
//            $aPayFee,
//            $this->_aCommonInfo['app_version'], // 不使用该参数，故写死在这
//            '',
//            $aPopePassengerInfo,
//            $bWithCarpool,
//            $this->_aBillInfo['noncarpool']['dynamic_times'],
//            $aExtInfo
//        );
//        $aMisData  = $this->activityMis->mergePopeIntoMis($aMisData, $aPopeData);
//
//        return $aMisData;
//    }

    /**
     * 获取快车险配置.
     *
     * @param $aMisData
     *
     * @return array
     */
    private function _getFastEncureMsg($aMisData) {
        $aReturn           = array();
        $this->activityMis = ActivityMis::getInstance();
        if ($this->activityMis->isOpen($this->_aParams['order_info'], ActivityMis::FAST_ENCURE_FLAG)) {
            $aHitInfo = $this->activityMis->getEstimateMsgByActivityFlag(ActivityMis::FAST_ENCURE_FLAG, $aMisData, $this->_aOrderInfo);
            if (!empty($aMisData) && $aHitInfo && !empty($aHitInfo['not_carpool'])
                && in_array($this->_aOrderInfo['product_id'], array(Constants\OrderSystem::PRODUCT_ID_FAST_CAR, Constants\OrderSystem::PRODUCT_ID_UBER_FAST_CAR))
            ) {
                //预估车费
                $aReturn['detail']   = array('priceTagId' => '50', 'title' => $aHitInfo['not_carpool']['config']['estimate_msg']);
                $aReturn['act_info'] = array(
                    'title' => $aHitInfo['not_carpool']['config']['estimate_h5_title'],
                    'msg'   => $aHitInfo['not_carpool']['config']['estimate_h5_msg'],
                    'color' => $aHitInfo['not_carpool']['config']['estimate_color'],
                    'url'   => $aHitInfo['not_carpool']['config']['fastensure_url'],
                );
            }
        }

        return $aReturn;
    }
}
