<?php

/***************************************************************************
 * 从需求决策服务dds,获取决策数据
 *
 * Copyright (c) 2018 xiaojukeji.com, Inc. All Rights Reserved
 * <AUTHOR>
 * @version 2018-05-31
 *
 **************************************************************************/

namespace PreSale\Logics\estimatePriceV2;

use BizLib\Config as NuwaConfig;
use BizLib\Utils\Language;
use BizLib\Utils\Horae;
use BizLib\Utils\Product;
use Disf\SPL\Trace;
use PreSale\Logics\estimatePriceV2\multiRequest\ProductGroup;
use PreSale\Logics\estimatePriceV2\multiResponse\component\recommendInfo\NormalRecommendInfo;
use PreSale\Logics\estimatePriceV2\multiResponse\MainHelper;
use PreSale\Models\strategy\DecisionService;
use BizLib\Utils\Locsvr;
use PreSale\Logics\carpool\CarpoolCommuteCard;
use PreSale\Logics\estimatePriceV2\multiResponse\component\LowPriceCarpoolGuide;
use PreSale\Logics\estimatePriceV2\params\SceneParamsLogic;
use BizLib\Utils\ProductCategory;
use Dirpc\SDK\EstimateDecision;
use BizLib\Constants;
use Xiaoju\Apollo\Apollo as ApolloV2;

class DecisionLogic
{
    /** @var $decisionService DecisionService */
    public $decisionService;
    const GUIDE_TYPE_EXHIBITION            = 1; // 露出式导流
    const DDS_GETRECORD_FIELD_ESTIMATE_IDS = 'estimate_ids'; //dds getrecod 字段名

    const TYPE_CARPOOL_COMMUTE_BUBBLE = 1005; //跟客户端约定的拼车通勤卡气泡的id
    const TYPE_DDS = 1004; //默认dds来源的气泡ID
    const TYPE_LOW_PRICE_CARPOOL       = 1007;//拼车车导流气泡
    const TYPE_ANYCAR_RECOMMEND_BUBBLE = 1006; //anycar默认选中推荐气泡
    const TYPE_DYNAMIC_PRICE_BUBBLE    = 1004; //动调解释说明气泡

    const DECISION_TYPE_DEFAULT         = 1;
    const DECISION_TYPE_SECOND_DECISION = 4; //第二次decision，无需执行默认选中逻辑

    const REMOVE_REASON_HIDE_LEVEL = 7; //dds满足隐藏等级条件时置灰, 条件可见dds:policy_aplus_filter.json
    /**
     * @var null
     */
    protected static $_oInstance = null;

    /**
     * @var null
     */
    private $_oCI = null;

    /**
     * @var null
     */
    private $_oDecision = null;

    /**
     * @var array
     */
    private $_aDecisionReq = array();

    /**
     * @var array
     */
    private $_aDecisionResp = array();

    /**
     * @var array
     */
    private $_aInfo = array();

    /**
     * @var array
     */
    private $_aConfig = array();

    /**
     * @var array
     */
    private $_aNewUserBubbleText = array();
    private $_aUserBubbleText    = array();

    /**
     * @var bool
     */
    private $_aToggle = null;

    /**
     * @var array
     */
    private $_aAthenaDecisionResult = array();

    /**
     * @var array athena请求dds获取默认选中结果时的返回
     */
    private $_aPreDecisionResult = array();

    private $_oProductCategory = null;

    private $_aAnycarSelection = null;

    private $_aProductList = null;

    private $_aProductGuideList = null;

    private $_aRecommendFlagInfo = null;

    private $_aInterCarpoolInfo = array();

    /**
     * @param array $aPreDecisionResult
     */
    public function setAPreDecisionResult(array $aPreDecisionResult) {
        $this->_aPreDecisionResult = $aPreDecisionResult;
    }

    /**
     * DecisionLogic constructor.
     */
    public function __construct() {
        $this->_aConfig = NuwaConfig::text('config_text', 'decision_require_level');
        $aBubbleText    = NuwaConfig::text('config_text', 'estimate_bubble_text');
        //new_user_carpool_long_order_booking
        $aBubbleText['new_user']['carpool_long_order_booking'] = Language::getTextFromDcmp('carpool_long_order_booking_bubble_text');
        $this->_aUserBubbleText    = $aBubbleText;
        $this->_aNewUserBubbleText = $aBubbleText['new_user'];
        $this->decisionService     = new DecisionService();
        $this->_oDecision          = $this->decisionService;
        $this->_aToggle            = false;
        $this->_oProductCategory   = new ProductCategory();
    }

    /**
     * @return null|DecisionLogic
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * 设置参数.
     *
     * @param array $aParams
     *
     * @return bool
     */
    public function setDecisionParams(array $aParams) {
        if (empty($aParams)) {
            return false;
        }

        $this->_aInfo = $aParams;

        return true;
    }

    /**
     * @param array $aResp
     *
     * @return bool
     */
    public function setDecisionResp(array $aResp) {
        if (empty($aResp)) {
            return false;
        }

        $this->_aDecisionResp = $aResp;

        return true;
    }

    /**
     * @return array
     */
    public function getDecisionParams() {
        return $this->_aDecisionReq;
    }


    /**
     * @param array $aAthenaDecisionResult
     */
    public function setAthenaDecisionResult(array $aAthenaDecisionResult) {
        $this->_aAthenaDecisionResult = $aAthenaDecisionResult;
    }

    /**
     * 格式化dds请求参数.
     *
     * @param $aParams
     *
     * @return array
     */
    public function formatRequest($aParams) {
        $aDecisionReq = array();
        if (empty($aParams)) {
            return $aDecisionReq;
        }

        $aUserInfo    = array();
        $aCommonInfo  = array();
        $aProductInfo = array();
        $sAthenaInfo  = '';
        $sAthenaExtra = array();
        $aScene       = array();
        $sPopeFsData  = '';
        foreach ($aParams as $sCarLevel => $aBizDetail) {
            if (empty($aUserInfo) && !empty($aBizDetail['passenger_info'])) {
                $aUserInfo['id']    = (int)($aBizDetail['passenger_info']['pid']);
                $aUserInfo['uid']   = (int)($aBizDetail['passenger_info']['uid']);
                $aUserInfo['pid']   = (int)($aBizDetail['passenger_info']['pid']);
                $aUserInfo['phone'] = (string)($aBizDetail['passenger_info']['phone']);
                $aUserInfo['call_car_phone'] = (string)($aBizDetail['order_info']['call_car_phone']);
                $aUserInfo['token']          = (string)(\Nuwa\Core\Dispatcher::getInstance()->getRequest()->fetchGetPost('token', false)); //平滑移动需要token，看他们后续能不能拆一个内部接口给我们，然后可以下掉这个字段
            }

            if (empty($aBizDetail['order_info']) || !is_array($aBizDetail['order_info'])) {
                continue;
            }

            if (empty($sAthenaInfo) && !empty($aBizDetail['athena_info'])) {
                $sAthenaInfo = json_encode($aBizDetail['athena_info']);
            }

            if (empty($sAthenaExtra) && !empty($aBizDetail['athena_extra'])) {
                $sAthenaExtra = $aBizDetail['athena_extra'];
            }

            if (empty($aCommonInfo)) {
                $aCommonInfo['start_lat']    = (float)($aBizDetail['order_info']['from_lat']);
                $aCommonInfo['start_lng']    = (float)($aBizDetail['order_info']['from_lng']);
                $aCommonInfo['to_lat']       = (float)($aBizDetail['order_info']['to_lat']);
                $aCommonInfo['to_lng']       = (float)($aBizDetail['order_info']['to_lng']);
                $aCommonInfo['client_type']  = (int)($aBizDetail['common_info']['client_type']);
                $aCommonInfo['city']         = (int)($aBizDetail['order_info']['area']);
                $aCommonInfo['county']       = (int)($aBizDetail['order_info']['county']);
                $aCommonInfo['menu_id']      = (string)($aBizDetail['order_info']['menu_id']);
                $aCommonInfo['sub_menu_id']  = (string)($aBizDetail['order_info']['sub_menu_id']);
                $aCommonInfo['app_version']  = (string)($aBizDetail['common_info']['app_version']);
                $aCommonInfo['channel']      = (int)($aBizDetail['order_info']['channel']);
                $aCommonInfo['degrade_type'] = (int)($aBizDetail['order_info']['degrade_type']);
                $aCommonInfo['estimate_trace_id'] = Trace::traceId();
                $aCommonInfo['map_type']          = (string)($aBizDetail['order_info']['map_type']);
                $aCommonInfo['order_type']        = (string)($aBizDetail['order_info']['order_type']);
                $aCommonInfo['pixels']            = (string)($aBizDetail['common_info']['pixels']);
                $aCommonInfo['access_key_id']     = (int)($aBizDetail['common_info']['access_key_id']);
                $aCommonInfo['page_type']         = (int)($aBizDetail['common_info']['page_type']);
                $aCommonInfo['lang']        = (string)Language::getLanguage();
                $aCommonInfo['service_tag'] = json_encode(SceneParamsLogic::getInstance()->getServiceTag($aBizDetail['passenger_info'], $aBizDetail['order_info']));
            }

            $aBillList    = $aBizDetail['bill_info']['bills'][$sCarLevel];
            $aBillProduct = $aBizDetail['bill_info']['product_infos'][$sCarLevel];
            //对于车型为600的,会存在carpool、noncarpool、special_rate三种,这里好坑。
            $iBusinessId  = (int)($aBizDetail['common_info']['business_id']);
            $aOrderNTuple = $aBizDetail['order_info']['order_n_tuple'] ?? [];
            $aAthenaExtra = $sAthenaExtra;
            foreach ($aBillList as $sSceneTag => $aSceneBill) {
                if (empty($aSceneBill)) {
                    continue;
                }

                $aProductItem = array();
                $aProductItem['product_id']            = (int)($aBillProduct[$sSceneTag]['product_line_id']);
                $aProductItem['business_id']           = $iBusinessId;
                $aProductItem['require_level']         = (int)($aBillProduct[$sSceneTag]['car_level']);
                $aProductItem['combo_type']            = (int)($aBillProduct[$sSceneTag]['combo_type']);
                $aProductItem['is_special_price']      = $aOrderNTuple[$sSceneTag]['is_special_price'];
                $aProductItem['carpool_type']          = (int)($aOrderNTuple[$sSceneTag]['carpool_type']) ?? 0;
                $aProductItem['route_type']            = (int)($aOrderNTuple[$sSceneTag]['route_type']) ?? 0;
                $aProductItem['is_dual_carpool_price'] = $aOrderNTuple[$sSceneTag]['is_dual_carpool_price'] ?? false;
                $aProductItem['estimate_id']           = (string)($aSceneBill['estimate_id']);
                $aProductItem['pre_total_fee']         = 0;
                $aProductItem['carpool_long_order']    = (int)($aOrderNTuple[$sSceneTag]['carpool_long_order']);
                $aProductItem['carpool_price_type']    = (int)($aOrderNTuple[$sSceneTag]['carpool_price_type'] ?? 0);
                $aProductItem['product_category']      = (int)($this->_oProductCategory->getProductCategoryByNTuple($aOrderNTuple[$sSceneTag]));

                if ('noncarpool' == $sSceneTag) {
                    $aProductItem['estimate_fee'] = (float)($aBizDetail['activity_info']['estimate_fee']);
                } elseif ('carpool' == $sSceneTag) {
                    $aProductItem['estimate_fee'] = (float)($aBizDetail['activity_info']['discount_fee']);
                } elseif ('special_rate' == $sSceneTag) {
                    $aProductItem['estimate_fee'] = (float)($aBizDetail['activity_info']['special_rate_estimate_fee']);
                } else {
                    $aProductItem['estimate_fee'] = 0;
                }

                $aProductItem['driver_metre'] = (int)$aBizDetail['bill_info']['driver_metre'];

                $aDiscountInfo = new \Dirpc\SDK\EstimateDecision\DiscountInfo();
                $aDynamicInfo  = new \Dirpc\SDK\EstimateDecision\DynamicInfo();
                $aProductItem['discount']          = $aDiscountInfo;
                $aProductItem['dynamic']           = $aDynamicInfo;
                $aProductItem['athena_capability'] = ['sp_capability' => (int)($aAthenaExtra['sp_capability']) ?? 0];
                $aBillExtraInfo = json_decode($aSceneBill['extra_info']['price_capability'] ?? '{}', true);
                $aProductItem['price_capability'] = [
                    'sp_open' => (int)($aBillExtraInfo['sp_open']) ?? 0,
                    'sp_rate' => (int)($aBillExtraInfo['sp_rate']) ?? 0,
                ];

                $aProductItem['taxi_sp_discount_fee_info'] = [
                    'sp_open'              => (int)($aSceneBill['taxi_sp_discount_fee_info']['sp_open']) ?? 0,
                    'taxi_sp_discount_fee' => (int)($aSceneBill['taxi_sp_discount_fee']) ?? 0,
                ];
                $aProductItem['long_rent_type']            = (int)($aOrderNTuple['long_rent_type'] ?? 0);
                $aProductItem['is_default'] = $this->_filterIsDefault($aProductItem);
                $aProductInfo[] = $aProductItem;

                if (empty($aScene) && !empty($aBizDetail['custom_service_info'][$sSceneTag])) {
                    $aScene['custom_service'] = json_decode($aBizDetail['custom_service_info'][$sSceneTag], true);
                }
            }

            if (empty($sPopeFsData) && !empty($aBizDetail['feature_data'])) {
                $sPopeFsData = json_encode($aBizDetail['feature_data']);
            }
        }

        $aDecisionReq['user']          = $aUserInfo;
        $aDecisionReq['common']        = $aCommonInfo;
        $aDecisionReq['products']      = $aProductInfo;
        $aDecisionReq['scene']         = $aScene;
        $aDecisionReq['athena_extra']  = $sAthenaExtra;
        $aDecisionReq['athena_info']   = $sAthenaInfo;
        $aDecisionReq['feature_data']  = $sPopeFsData;
        $aDecisionReq['decision_type'] = $this->_getDecisionType();

        return $aDecisionReq;
    }

    /**
     * 格式化dds 请求入参
     * @param array $aInfos aInfo列表
     * @return array
     */
    public function formatDecisionRequest($aInfos) {
        $aDecisionReq = array();
        if (empty($aInfos)) {
            return $aDecisionReq;
        }

        $aInfo          = current($aInfos);
        $aPassengerInfo = $aInfo['passenger_info'];
        $aOrderInfo     = $aInfo['order_info'];
        $aUserInfo      = [
            'id'             => (int)$aPassengerInfo['pid'],
            'uid'            => (int)$aPassengerInfo['uid'],
            'pid'            => (int)$aPassengerInfo['pid'],
            'phone'          => (string)$aPassengerInfo['phone'],
            'call_car_phone' => (string)$aOrderInfo['call_car_phone'],
            'token'          => (string)\Nuwa\Core\Dispatcher::getInstance()->getRequest()->fetchGetPost('token', false), //平滑移动需要token，看他们后续能不能拆一个内部接口给我们，然后可以下掉这个字段
        ];

        $aCommonInfo = [
            'start_lat'         => (float)($aOrderInfo['from_lat']),
            'start_lng'         => (float)($aOrderInfo['from_lng']),
            'to_lat'            => (float)($aOrderInfo['to_lat']),
            'to_lng'            => (float)($aOrderInfo['to_lng']),
            'client_type'       => (int)($aInfo['common_info']['client_type']),
            'city'              => (int)($aOrderInfo['area']),
            'to_city'            => (int)($aOrderInfo['to_area']),
            'county'            => (int)($aOrderInfo['county']),
            'menu_id'           => (string)($aOrderInfo['menu_id']),
            'sub_menu_id'       => (string)($aOrderInfo['sub_menu_id']),
            'app_version'       => (string)($aInfo['common_info']['app_version']),
            'channel'           => (int)($aOrderInfo['channel']),
            'degrade_type'      => (int)($aOrderInfo['degrade_type']),
            'estimate_trace_id' => Trace::traceId(),
            'map_type'          => (string)($aOrderInfo['map_type']),
            'order_type'        => (string)($aOrderInfo['order_type']),
            'pixels'            => (string)($aInfo['common_info']['pixels']),
            'access_key_id'     => (int)($aInfo['common_info']['access_key_id']),
            'page_type'         => (int)($aInfo['common_info']['page_type']),
            'lang'              => (string)Language::getLanguage(),
            'service_tag'       => json_encode(SceneParamsLogic::getInstance()->getServiceTag($aPassengerInfo, $aOrderInfo)),
            'x_new_form'        => (int)ParamsLogic::getInstance()->getRequest('x_new_form'),
        ];

        $aAthenaExtra = $aInfo['athena_extra'];
        $sAthenaInfo  = '';
        if (!empty($aInfo['athena_info'])) {
            $sAthenaInfo = json_encode($aInfo['athena_info']);
        }

        $sPopeFsData = '';
        if (!empty($aInfo['feature_data'])) {
            $sPopeFsData = json_encode($aInfo['feature_data']);
        }

        $aScene = array();
        if (isset($aInfo['custom_service_info'])) {
            $aScene['custom_service'] = $aInfo['custom_service_info'];
        }

        $aProductInfo = [];
        foreach ($aInfos as $aInfo) {
            $sCarLevel    = $aInfo['order_info']['require_level'];
            $aBillInfo    = $aInfo['bill_info']['bills'][$sCarLevel];
            $aBillProduct = $aInfo['bill_info']['product_infos'][$sCarLevel];
            $aOrderNTuple = $aInfo['order_info']['n_tuple'];
            $iBusinessId  = (int)($aInfo['common_info']['business_id']);

            $aProductItem = array();
            $aProductItem['product_id']            = (int)($aBillProduct['product_line_id']);
            $aProductItem['business_id']           = $iBusinessId;
            $aProductItem['require_level']         = (int)($aBillProduct['car_level']);
            $aProductItem['combo_type']            = (int)($aBillProduct['combo_type']);
            $aProductItem['is_special_price']      = $aOrderNTuple['is_special_price'];
            $aProductItem['carpool_type']          = (int)($aOrderNTuple['carpool_type']) ?? 0;
            $aProductItem['route_type']            = (int)($aOrderNTuple['route_type']) ?? 0;
            $aProductItem['is_dual_carpool_price'] = $aOrderNTuple['is_dual_carpool_price'] ?? false;
            $aProductItem['estimate_id']           = (string)($aInfo['bill_info']['estimate_id']);
            $aProductItem['pre_total_fee']         = (float)$aInfo['bill_info']['bills'][$sCarLevel]['pre_total_fee'];
            $aProductItem['cap_price']          = (float)$aInfo['bill_info']['bills'][$sCarLevel]['cap_price']; //拼车新产品需要
            $aProductItem['carpool_long_order'] = (int)($aOrderNTuple['carpool_long_order']);
            $aProductItem['carpool_price_type'] = (int)($aOrderNTuple['carpool_price_type'] ?? 0);
            $aProductItem['level_type']         = (int)($aOrderNTuple['level_type'] ?? 0);
            if (!empty($aInfo['order_info']['product_category'])) {
                $aProductItem['product_category'] = (int)$aInfo['order_info']['product_category'];
            } else {
                $aProductItem['product_category'] = (int)($this->_oProductCategory->getProductCategoryByNTuple($aOrderNTuple));
            }

            if (Product::isAnyCar($aInfo['order_info']['product_id'])) {
                $aProductItem['estimate_fee'] = 0; //与此前逻辑保持一致
            } else {
                $aProductItem['estimate_fee'] = (float)($aInfo['activity_info'][0]['estimate_fee']);
            }

            $aProductItem['driver_metre'] = (int)$aInfo['bill_info']['driver_metre'];

            $aDiscountInfo = new \Dirpc\SDK\EstimateDecision\DiscountInfo();
            $aDynamicInfo  = new \Dirpc\SDK\EstimateDecision\DynamicInfo();
            $aProductItem['discount'] = $aDiscountInfo;
            $aProductItem['dynamic']  = $aDynamicInfo;
            $aProductItem['athena_capability']['sp_capability'] = (int)($aAthenaExtra['sp_capability']) ?? 0;
            $aProductItem['athena_capability']['unione_youxuan_capability'] = 1;
            $aProductItem['athena_capability']['aplus_flash_capability']    = (int)($aAthenaExtra['aplus_flash_capability']) ?? 0;
            $aProductItem['athena_capability']['sp_flash_disabled_type']    = (int)($aAthenaExtra['sp_flash_disabled_type']) ?? 0;

            $aBillExtraInfo = json_decode($aBillInfo['extra_info']['price_capability'] ?? '{}', true);
            $aProductItem['price_capability'] = [
                'sp_open' => (int)($aBillExtraInfo['sp_open']) ?? 0,
                'sp_rate' => (int)($aBillExtraInfo['sp_rate']) ?? 0,
            ];

            $aProductItem['taxi_sp_discount_fee_info'] = [
                'sp_open'              => (int)($aBillInfo['taxi_sp_discount_fee_info']['sp_open']) ?? 0,
                'taxi_sp_discount_fee' => (int)($aBillInfo['taxi_sp_discount_fee']) ?? 0,
            ];
            $aMultiRequireProduct = [];
            if (!empty($aInfo['multi_require_product'])) {
                foreach ($aInfo['multi_require_product'] as $aProduct) {
                    $aMultiRequireProduct[] = new EstimateDecision\ProductCell($aProduct);
                }
            }

            $aProductItem['multi_require_product'] = array_values($aMultiRequireProduct);
            $aProductItem['form_show_type']        = $aInfo['order_info']['form_show_type'];
            $aProductItem['long_rent_type']        = (int)($aOrderNTuple['long_rent_type'] ?? 0);
            $aProductItem['is_default']            = $this->_filterIsDefault($aProductItem);
            $aProductItem['total_fee_without_discount'] = $aBillInfo['total_fee_without_discount'] ?? 0.0;
            $aProductInfo[] = $aProductItem;
        }

        $aDecisionReq['user']          = $aUserInfo;
        $aDecisionReq['common']        = $aCommonInfo;
        $aDecisionReq['products']      = $aProductInfo;
        $aDecisionReq['scene']         = $aScene;
        $aDecisionReq['athena_extra']  = $aAthenaExtra;
        $aDecisionReq['athena_info']   = $sAthenaInfo;
        $aDecisionReq['feature_data']  = $sPopeFsData;
        $aDecisionReq['decision_type'] = $this->_getDecisionType();

        $aPreferenceProduct = [];
        foreach ($aInfos as $aInfo) {
            if (!empty($aInfo['preference_product'])) {
                $aDecodeInfo = json_decode($aInfo['preference_product'], true);
                foreach ($aDecodeInfo as $aProduct) {
                    $aPreferenceProduct[] = new EstimateDecision\ProductCell($aProduct);
                }

                break;
            }
        }

        $aDecisionReq['preference_product'] = $aPreferenceProduct;

        return $aDecisionReq;
    }



    /**
     * 获取dds执行decision的类型
     * @return int
     */
    private function _getDecisionType() {
        if (!empty($this->_aPreDecisionResult) || !empty($this->_aAthenaDecisionResult)) {
            return self::DECISION_TYPE_SECOND_DECISION;
        }

        return self::DECISION_TYPE_DEFAULT;
    }


    /**
     * @param $iProductId
     * @param $iComboType
     * @param $iRequireLevel
     *
     * @return int
     */
    private function _filterIsDefault($aProductItem) {
        $iIsDefault   = 0;
        $aOneConfList = ParamsLogic::getInstance()->getOneConf();
        if (ParamsLogic::getInstance()->hitMultiEstimateForPremium()) {
            foreach ($aOneConfList as $aOneConfItem) {
                if ($aOneConfItem['estimate_id'] != $aProductItem['estimate_id']) {
                    continue;
                }

                $iIsDefault = (int)($aOneConfItem['is_default']);
            }
        } else {
            foreach ($aOneConfList as $aOneConfItem) {
                if (\BizLib\Constants\Horae::TYPE_COMBO_CARPOOL_FLAT_RATE == $aProductItem['combo_type']) {
                    $aProductItem['combo_type'] = \BizLib\Constants\Horae::TYPE_COMBO_CARPOOL;
                }

                // 新包车特殊逻辑：修改oneConf中的combo_type为10009，即为账单侧的包车combo_type。不然会因为oneConf和productItem数据不一致 导致端上选中不生效。
                if (isset($aProductItem['long_rent_type']) && Constants\OrderSystem::LONG_RENT_TYPE_NEW == $aProductItem['long_rent_type']) {
                    $aOneConfItem['combo_type'] = 10009;
                }

                if (!\BizCommon\Utils\Horae::isNTupleMatch($aOneConfItem, $aProductItem)) {
                    continue;
                }

                $iIsDefault = (int)($aOneConfItem['is_default']);
            }
        }

        return $iIsDefault;
    }



    /**
     * dds降级开关.
     *
     * @return bool
     */
    public function hitDecisionDegrade() {
        if (empty($this->_aInfo) || empty(current($this->_aInfo))) {
            return false;
        }
        return false;
    }

    /**
     * @return bool
     */
    public function hitDecisionService() {
        if (empty($this->_aInfo) || empty(current($this->_aInfo))) {
            return false;
        }

        //接送机全屏需要使用dds中decision的数据
        if ($this->hitAirportPageType() || $this->hitNewLongRentPageType()) {
            return true;
        }

        $aBizInfo = current($this->_aInfo);

        $apolloV2 = new \Xiaoju\Apollo\Apollo();
        if ($apolloV2->featureToggle(
            'gs_demand_decision_switch',
            [
                'key'         => rand(1, 500000),
                'city'        => (string)($aBizInfo['order_info']['area']),
                'phone'       => (string)($aBizInfo['passenger_info']['phone']),
                'product_id'  => (string)($aBizInfo['order_info']['product_id']),
                'app_version' => (string)($aBizInfo['common_info']['app_version']),
            ]
        )->allow()
        ) {
            return true;
        }

        return false;
    }

    /**
     * 妇女节车标需从dds获取开关.
     *
     * @return bool
     */
    public function hitWomenDayDecision() {
        if (empty($this->_aInfo) || empty(current($this->_aInfo))) {
            return false;
        }

        return false;
    }

    /**
     * 是否命中接送机全屏
     * @return bool
     */
    public function hitAirportPageType() {

        if (empty($this->_aInfo) || empty(current($this->_aInfo))) {
            return false;
        }

        $aBizInfo = current($this->_aInfo);
        return Horae::isAirportPageType($aBizInfo['common_info']['page_type']);
    }

    /**
     * 是否命中新包车页面
     * @return bool
     */
    public function hitNewLongRentPageType() {
        if (empty($this->_aInfo) || empty(current($this->_aInfo))) {
            return false;
        }

        $aBizInfo = current($this->_aInfo);
        return Horae::isNewLongRentPageType($aBizInfo['common_info']['page_type']);
    }


    /**
     * @return bool
     */
    public function hitDecisionSortSwitch() {
        if (empty($this->_aInfo) || empty(current($this->_aInfo))) {
            return false;
        }

        $aBizInfo = current($this->_aInfo);

        $apolloV2 = new \Xiaoju\Apollo\Apollo();
        if ($apolloV2->featureToggle(
            'gs_demand_decision_sort_switch',
            [
                'key'         => rand(1, 500000),
                'city'        => (string)($aBizInfo['order_info']['area']),
                'phone'       => (string)($aBizInfo['passenger_info']['phone']),
                'product_id'  => (string)($aBizInfo['order_info']['product_id']),
                'app_version' => (string)($aBizInfo['common_info']['app_version']),
                'client_type' => (string)($aBizInfo['common_info']['client_type']),
                'channel'     => (string)($aBizInfo['order_info']['channel']),
            ]
        )->allow()
        ) {
            return true;
        }

        return false;
    }

    /**
     * 执行决策。
     *
     * @return array
     */
    public function executeDecision() {
        $bIsFestivalIcon = $this->hitWomenDayDecision();
        // 切流量开关
        if ((!$this->hitDecisionService() || !$this->_isInland()) && !ParamsLogic::getInstance()->hitMultiEstimateForPremium() && (!$bIsFestivalIcon || !$this->_isInlandEnglish()) && !$this->hitAirportPageType()) {
            $this->_aDecisionResp = array();

            return $this->_aDecisionResp;
        }

        if (!empty($this->_aDecisionResp) && is_array($this->_aDecisionResp)) {
            return $this->_aDecisionResp;
        }

        // 降级开关,如果打开,则降级dds服务。
        if ($this->hitDecisionDegrade()) {
            $this->_aDecisionResp = $this->loadDefaultResult();

            return $this->_aDecisionResp;
        }

        // 保证开关一致性
        $this->_aToggle = true;
        // dds服务
        //获取dds 参数，response改造切量期间 因为aInfo变更了，需要format两次，大家也记得改两次呀
        if (MainHelper::useNewResponse()) {
            $this->_aDecisionReq = $this->formatDecisionRequest($this->_aInfo);
        } else {
            $this->_aDecisionReq = $this->formatRequest($this->_aInfo);
        }

        $this->_aDecisionResp = $this->_oDecision->getDecisionResult($this->_aDecisionReq);

        //athena 会请求一次dds，返回guide_dds_response 及 dds_response 若这两个字段有值则使用当前结果，若没有则使用第二次decision的兜底结果
        $aPreSelection    = [];
        $aPreProductsList = [];
        $aAnycarDecision  = [];
        if (!empty($this->_aAthenaDecisionResult)) {
            $aPreSelection    = $this->_aAthenaDecisionResult['selected_item'] ?? [];
            $aPreProductsList = $this->_aAthenaDecisionResult['product_decisions'] ?? $this->_aAthenaDecisionResult['product_decisions'];
            $aAnycarDecision  = $this->_aAthenaDecisionResult['anycar_decision'] ?? $this->_aAthenaDecisionResult['anycar_decision'];
            $aNewProductList  = $this->_aAthenaDecisionResult['product_list'] ?? $this->_aAthenaDecisionResult['product_list'];
        } elseif (!empty($this->_aPreDecisionResult)) {
            $aPreSelection    = $this->_aPreDecisionResult['selected_item'] ?? [];
            $aPreProductsList = $this->_aPreDecisionResult['product_decisions'] ?? [];
            $aAnycarDecision  = $this->_aPreDecisionResult['anycar_decision'] ?? $this->_aPreDecisionResult['anycar_decision'];
            $aNewProductList  = $this->_aPreDecisionResult['product_list'] ?? $this->_aPreDecisionResult['product_list'];
        }

        if (!empty($aPreSelection)) {
            if (\BizCommon\Constants\OrderNTuple::CARPOOL_TYPE_FLAT_RATE == $aPreSelection['carpool_type']) {
                $aPreSelection['product_category'] = \Bizlib\Utils\ProductCategory::PRODUCT_CATEGORY_CARPOOL_FLAT_RATE_BY_SEAT;
                 $aPreSelection['combo_type']      = \BizLib\Constants\OrderSystem::TYPE_COMBO_CARPOOL;
            }

            $this->_aDecisionResp['selected_item'] = $aPreSelection;
        }

        if (!empty($aAnycarDecision)) {
            foreach ($aAnycarDecision as &$aProductInfo) {
                if (\BizCommon\Constants\OrderNTuple::CARPOOL_TYPE_FLAT_RATE == $aProductInfo['carpool_type']) {
                    //修复区域一口价bug，长期方案账单解决，已讨论方案
                    $aProductInfo['product_category'] = \Bizlib\Utils\ProductCategory::PRODUCT_CATEGORY_CARPOOL_FLAT_RATE_BY_SEAT;
                    $aProductInfo['combo_type']       = \BizLib\Constants\OrderSystem::TYPE_COMBO_CARPOOL;
                }
            }

            $this->_aDecisionResp['anycar_decision'] = $aAnycarDecision;
        }

        if (!empty($aNewProductList)) {
            $this->_aDecisionResp['product_list'] = $aNewProductList;
        }

        LowPriceCarpoolGuide::set($this->_aInfo,$this->_aDecisionResp);

        if (!empty($aPreProductsList)) {
            //1,根据predecision的排序排列decision返回的product_decisions
            //2,同时merge x_recommend/x_rec_index 结果
            $iI     = 0;
            $iCount = count($this->_aDecisionResp['product_decisions']);
            foreach ($aPreProductsList as $aData) {
                $iJ = $iI;
                while ($iJ < $iCount) {
                    $aProduct = $this->_aDecisionResp['product_decisions'][$iJ];
                    if (\BizCommon\Utils\Horae::isNTupleMatch($aProduct,$aData)) {
                        $aTmpProduct = $aProduct;
                        $aTmpProduct['x_recommend']   = $aData['x_recommend'];
                        $aTmpProduct['x_rec_index']   = $aData['x_rec_index'];
                        $aTmpProduct['remove_flag']   = $aData['remove_flag'];
                        $aTmpProduct['remove_reason'] = $aData['remove_reason'];
                        $this->_aDecisionResp['product_decisions'][$iJ] = $this->_aDecisionResp['product_decisions'][$iI];
                        $this->_aDecisionResp['product_decisions'][$iI] = $aTmpProduct;
                        $iI++;
                        break;
                    }

                    $iJ++;
                }
            }
        }

        // 若是春节且大陆英文版，则只保留gray_icon和light_icon
        if ($bIsFestivalIcon && $this->_isInlandEnglish()) {
            $aFilterDecisionResp  = $this->_aDecisionResp['product_decisions'];
            $aSetIndex            = ['product_id', 'business_id', 'require_level', 'combo_type', 'carpool_type', 'route_type', 'light_icon', 'gray_icon'];
            $aNewProductDecisions = array();
            foreach ($aFilterDecisionResp as $filterDecisionItem) {
                $aNewDecisionItem = array();
                foreach ($aSetIndex as $sIndexItem) {
                    $aNewDecisionItem[$sIndexItem] = $filterDecisionItem[$sIndexItem];
                }

                $aNewProductDecisions[] = $aNewDecisionItem;
            }

            unset($this->_aDecisionResp['selected_item']);
            $this->_aDecisionResp['product_decisions'] = $aNewProductDecisions;
        }

        return $this->_aDecisionResp;
    }


    /**
     * 6.0 dds执行决策
     * @return array|EstimateDecision\DecisionResponse
     */
    public function executeDecisionV2() {
        //athena 会请求一次dds，返回dds_response; 若请求成功直接使用Athena的结果；若失败则兜底请求一次
        if (!empty($this->_aPreDecisionResult) && !empty($this->_aPreDecisionResult['product_list'])) {
            $this->_aDecisionResp = $this->_aPreDecisionResult;
        } else {
            //获取dds 参数
            $this->_aDecisionReq = $this->formatDecisionRequest($this->_aInfo);

            $this->_aDecisionResp = $this->_oDecision->getDecisionResult($this->_aDecisionReq);
        }

        return $this->_aDecisionResp;
    }

    // 注释-待删
//    /**
//     * 通过dds返回修正 sub_group_id
//     * @return void
//     */
//    public function modifySubGroupId() {
//        $aApolloParams = [
//            'key'   => $this->_aInfo[0]['passenger_info']['pid'],
//            'city'  => $this->_aInfo[0]['order_info']['area'],
//            'phone' => $this->_aInfo[0]['passenger_info']['phone'],
//        ];
//        $oToggle       = ApolloV2::getInstance()->featureToggle('gs_new_aggregation_switch', $aApolloParams);
//
//        //0流量表示走pre-sale，则不进行下面的modify
//        if (!$oToggle->allow()) {
//            return;
//        }
//
//        $aDecisionProductList = $this->getDecisionProductList();
//        if (empty($aDecisionProductList)) {
//            return;
//        }
//
//        foreach ($aDecisionProductList as $aDecisionProduct) {
//            $iSubGroupID      = $aDecisionProduct['sub_group_id'] ?? -1;
//            $iProductCategory = $aDecisionProduct['product_category'];
//            ProductGroup::getInstance() -> modProductSubGroupID($iProductCategory,$iSubGroupID);
//            ProductGroup::getInstance() -> modSubGroupConf($iProductCategory,$iSubGroupID);
//        }
//    }

    /**
     * 构建聚合数据
     * @return void
     */
    public function buildGroupConf() {
        $bNewNewAggregationDegrade = $this->_checkNewAggregationDegrade();
        if ($bNewNewAggregationDegrade) {
            return;
        }

        $aDecisionProductList = $this->getDecisionProductList();
        $iArea          = $this->_aInfo[0]['order_info']['area'];
        $aPassengerInfo = $this->_aInfo[0]['passenger_info'];
        ProductGroup::getInstance()->buildProductGroupInfoV2($aDecisionProductList,$iArea,$this->_aInfo[0]['common_info'],$aPassengerInfo);
    }

    /**
     * 检测是否降级
     * @return bool
     */
    private function _checkNewAggregationDegrade() {
        $aApolloParams = [
            'key'   => $this->_aInfo[0]['passenger_info']['pid'],
            'city'  => $this->_aInfo[0]['order_info']['area'],
            'phone' => $this->_aInfo[0]['passenger_info']['phone'],
        ];
        $oToggle       = ApolloV2::getInstance()->featureToggle('gs_new_aggregation_switch', $aApolloParams);
        return  !$oToggle->allow();
    }

    // 注释-待删
//    /**
//     * 直接用format 之后的参数请求dds.
//     *
//     * @param $aDecisionReq
//     *
//     * @return array
//     */
//    public function getDecisionResultDirect($aDecisionReq) {
//        $this->_aDecisionResp = $this->_oDecision->getDecisionResult($aDecisionReq);
//
//        return $this->_aDecisionResp;
//    }

    /**
     * 获取DDS排序开关结果。
     *
     * @return bool
     */
    public function getDecisionSortToggleResult() {
        return $this->_aToggle && $this->hitDecisionSortSwitch();
    }

    /**
     * 获取DDS athena选中开关结果。
     *
     * @return bool
     */
    public function getDecisionAthenaToggleResult() {
        return $this->_aToggle;
    }

    /**
     * 获取全部决策排序。
     *
     * @return array|mixed
     */
    public function getDecisionProductOrder() {
        if (empty($this->_aDecisionResp) || empty($this->_aDecisionResp['product_decisions'])) {
            return array();
        }

        return $this->_aDecisionResp['product_decisions'];
    }

    /**
     * 获取6.0 的导流信息
     * @return array
     */
    public function getDecisionProductGuideList() {
        if (empty($this->_aDecisionResp) || empty($this->_aDecisionResp['product_guide_list'])) {
            return array();
        }

        return $this->_aDecisionResp['product_guide_list'];
    }


    /**
     * 获取6.0 的排序列表
     * @return array
     */
    public function getDecisionProductList() {
        if (empty($this->_aDecisionResp) || empty($this->_aDecisionResp['product_list'])) {
            return array();
        }

        return $this->_aDecisionResp['product_list'];
    }

    /**
     * @param int $iProductCategory 品类ID
     * @return array
     */
    public function getDecisionProductByCategoryId($iProductCategory) {
        $aDecisionProductList = $this->getDecisionProductList();
        foreach ($aDecisionProductList as $aDecisionProduct) {
            if ($iProductCategory == $aDecisionProduct['product_category']) {
                return $aDecisionProduct;
            }
        }

        return array();
    }

    /**
     * @param int $iProductCategory 品类ID
     * @return array|mixed
     */
    public function getProductInfoByCategoryId($iProductCategory) {

        if (isset($this->_aProductList) && is_array($this->_aProductList)) {
            return $this->_aProductList[$iProductCategory] ?? array();
        } else {
            $this->_aProductList = [];
            if (!empty($this->_aDecisionResp['product_list'])) {
                foreach ($this->_aDecisionResp['product_list'] as $aProductInfo) {
                    $this->_aProductList [$aProductInfo['product_category']] = $aProductInfo;
                }
            }

            return $this->_aProductList[$iProductCategory] ?? array();
        }
    }

    /**
     * @param int $iProductCategory 品类ID
     * @return array|mixed
     */
    public function getProductGuideInfoByCategoryId($iProductCategory) {

        if (isset($this->_aProductGuideList) && is_array($this->_aProductGuideList)) {
            return $this->_aProductGuideList[$iProductCategory] ?? array();
        } else {
            $this->_aProductGuideList = [];
            if (!empty($this->_aDecisionResp['product_guide_list'])) {
                foreach ($this->_aDecisionResp['product_guide_list'] as $aProductGuideInfo) {
                    $this->_aProductGuideList [$aProductGuideInfo['product_category']] = $aProductGuideInfo;
                }
            }

            return $this->_aProductGuideList[$iProductCategory] ?? array();
        }
    }

    // 注释-待删
//    /**
//     * @return array
//     */
//    public function getAnycarSelection() {
//
//        if (isset($this->_aAnycarSelection) && is_array($this->_aAnycarSelection)) {
//            return $this->_aAnycarSelection;
//        } else {
//            $this->_aAnycarSelection = [];
//            if (!empty($this->_aDecisionResp['anycar_decision'])) {
//                foreach ($this->_aDecisionResp['anycar_decision'] as $aProductInfo) {
//                    if (1 == $aProductInfo['is_selected'] && -1 != $aProductInfo['product_category']) {
//                        $this->_aAnycarSelection [$aProductInfo['product_category']] = $aProductInfo;
//                    }
//                }
//            }
//
//            return $this->_aAnycarSelection;
//        }
//    }

    /**
     * 获取anycar_decision
     * @return array
     */
    public function getAnycarDecision() {
        $aAnycarDecision = [];
        if (!empty($this->_aDecisionResp['anycar_decision'])) {
            foreach ($this->_aDecisionResp['anycar_decision'] as $aProductInfo) {
                $aAnycarDecision[$aProductInfo['product_category']] = $aProductInfo;
            }
        }

        return $aAnycarDecision;
    }

    /**
     * 根据产品信息,获取决策结果。
     *
     * @param $iProductId
     * @param $sRequireLevel
     * @param $iComboType
     *
     * @return array|mixed
     */
    public function getDecisionProductInfo($iProductId, $sRequireLevel, $iComboType, $bIsSpecialPrice = false, $iLevelType = 0) {
        if (empty($sRequireLevel)) {
            return array();
        }

        if (empty($this->_aDecisionResp) || empty($this->_aDecisionResp['product_decisions'])) {
            return array();
        }

        foreach ($this->_aDecisionResp['product_decisions'] as $aDecisionItem) {
            $aDecisionItem['is_special_price'] = (isset($aDecisionItem['is_special_price']) && true == $aDecisionItem['is_special_price'] ) ? true : false;
            if ($aDecisionItem['product_id'] != $iProductId
                || $aDecisionItem['require_level'] != $sRequireLevel
                || $aDecisionItem['combo_type'] != $iComboType
                || $aDecisionItem['is_special_price'] != $bIsSpecialPrice
                || $aDecisionItem['level_type'] != $iLevelType
            ) {
                continue;
            }

            return $aDecisionItem;
        }

        return array();
    }

    /**
     * 获取默认选中项.
     *
     * @return array|mixed
     */
    public function getDecisionSelectedItem() {
        if (empty($this->_aDecisionResp) || empty($this->_aDecisionResp['selected_item'])) {
            return array();
        }

        return $this->_aDecisionResp['selected_item'];
    }



    // 注释-待删
//    /**
//     * 获取默认选中项.
//     *
//     * @return array|mixed
//     */
//    public function getDecisionBubbleText() {
//        $aRet = '';
//        if (empty($this->_aDecisionResp)
//            || empty($this->_aDecisionResp['selected_item'])
//            || empty($this->_aDecisionResp['product_decisions'])
//        ) {
//            return $aRet;
//        }
//
//        $aSelectItem = $this->_aDecisionResp['selected_item'];
//        foreach ($this->_aDecisionResp['product_decisions'] as $aDecisionItem) {
//            if ($aDecisionItem['product_id'] == $aSelectItem['product_id']
//                && $aDecisionItem['require_level'] == $aSelectItem['require_level']
//                && $aDecisionItem['combo_type'] == $aSelectItem['combo_type']
//                && isset($aDecisionItem['bubble_text'])
//                && !empty($aDecisionItem['bubble_text'])
//            ) {
//                return $aDecisionItem['bubble_text'];
//            }
//        }
//
//        return $aRet;
//    }

    /**
     * 获取guide info.
     *
     * @return array|mixed
     */
    public function getGuideInfo() {
        if (empty($this->_aDecisionResp)
            || empty($this->_aDecisionResp['guide_info'])
            || empty($this->_aDecisionResp['guide_info']['type'])
        ) {
            return array();
        }

        $aRet = array();
        $type = $this->_aDecisionResp['guide_info']['type'];
        switch ($type) {
            case self::GUIDE_TYPE_EXHIBITION: // 露出式导流
                $aRet[] = array(
                    'type'      => self::GUIDE_TYPE_EXHIBITION,
                    'show_list' => $this->_aDecisionResp['guide_info']['to'],
                );
                break;
        }

        return $aRet;
    }

    /**
     * load本地配置文件,并格式化.
     *
     * @return array
     */
    public function loadDefaultResult() {
        if (empty($this->_aConfig)) {
            return array();
        }

        $aGuideInfo  = array();
        $aSelected   = $this->getDefaultSelectedItem();
        $aProductRes = array();

        foreach ($this->_aDecisionReq['products'] as $aReq) {
            $aProductRes[] = $this->getDefaultProductInfo($aReq);
        }

        $aDecisionResult = array(
            'guide_info'        => $aGuideInfo,
            'selected_item'     => $aSelected,
            'product_decisions' => $aProductRes,
        );

        return $aDecisionResult;
    }

    /**
     * 获取降级配置,默认选中态
     *
     * @return array|mixed
     */
    public function getDefaultSelectedItem() {
        if (empty($this->_aConfig)) {
            return array();
        }

        foreach ($this->_aConfig as $aConfig) {
            if (1 == $aConfig['is_default']) {
                return $aConfig;
            }
        }

        return array();
    }

    /**
     * 获取降级配置,产品配置.
     *
     * @param $aReq
     *
     * @return array|mixed
     */
    public function getDefaultProductInfo($aReq) {
        if (empty($this->_aConfig)) {
            return array();
        }

        foreach ($this->_aConfig as $aDefaultCfg) {
            if ($aDefaultCfg['business_id'] == $aReq['business_id']
                && $aDefaultCfg['require_level'] == $aReq['require_level']
                && $aDefaultCfg['combo_type'] == $aReq['combo_type']
            ) {
                return $aDefaultCfg;
            }
        }

        return array();
    }

    // 注释-待删
//    /**
//     * @return bool
//     */
//    public function hitBubbleTextSwitch() {
//        if (empty($this->_aInfo) || empty(current($this->_aInfo))) {
//            return false;
//        }
//
//        $aBizInfo = current($this->_aInfo);
//        $apolloV2 = new \Xiaoju\Apollo\Apollo();
//        if ($apolloV2->featureToggle(
//            'gs_dds_bubble_text_switch',
//            [
//                'key'         => rand(1, 500000),
//                'city'        => (string)($aBizInfo['order_info']['area']),
//                'pid'         => (string)($aBizInfo['passenger_info']['pid']),
//                'phone'       => (string)($aBizInfo['passenger_info']['phone']),
//                'product_id'  => (string)($aBizInfo['order_info']['product_id']),
//                'app_version' => (string)($aBizInfo['common_info']['app_version']),
//            ]
//        )->allow()
//        ) {
//            return true;
//        }
//
//        return false;
//    }

    /**
     * 根据dds返回气泡文案,构造athena_info.
     *
     * @return array
     */
    public function getDDSAthenaInfo($aDefaultInfo = null) {
        return [];
    }

    /**
     * 构建拼车通勤卡的导流信息.
     *
     * @param $aExtInfo
     *
     * @return array
     */
    private function _buildCarpoolCommuteShowText($aExtInfo) {
        //获取通勤卡配置，如果这块要迁到dds，可以删除这几行。
        $aCarpoolCommuteCardConfigInfo = NuwaConfig::config('config_carpool', 'carpool_commute_card');

        //获取通勤卡的跳转h5
        $oCarpoolCommuteCard = new CarpoolCommuteCard();
        $sConfirmButtonUrl   = $oCarpoolCommuteCard->getCommuteCardUrl($aExtInfo['commute_card_info'], $aExtInfo['carpool_bill_info'], $aExtInfo['product_id'], $aExtInfo['city_id'], $aExtInfo['from_name'], $aExtInfo['to_name']);

        $aShowText = array(
            'show_type'            => self::TYPE_CARPOOL_COMMUTE_BUBBLE,
            'show_url'             => $aCarpoolCommuteCardConfigInfo['bubble_config']['show_url'],
            'title'                => sprintf($aCarpoolCommuteCardConfigInfo['bubble_config']['title'], $aExtInfo['commute_card_info']['sale_price'], ltrim($aExtInfo['commute_card_info']['discount'], '0.')),
            'confirm_button_title' => $aCarpoolCommuteCardConfigInfo['bubble_config']['confirm_button_title'],
            'confirm_button_url'   => $sConfirmButtonUrl,
        );

        return $aShowText;
    }

    /**
     * 根据气泡文案构造导流结构.
     *
     * @param $aDecision
     *
     * @return array
     */
    public function buildGuideInfo($aDecision, $sFrom = '', $aExtInfo = []) {
        $aShowText = array(
            'show_type' => $aDecision['show_type'] ?? self::TYPE_DDS,
            'show_url'  => '',
            'title'     => $aDecision['bubble_text'],
        );

        if (\BizLib\Constants\OrderSystem::PRODUCT_ID_FAST_CAR == $aDecision['product_id']
            && \BizLib\Utils\CarLevel::DIDI_XIAOBA_CAR_LEVEL == $aDecision['require_level']
            && \BizLib\Constants\OrderSystem::TYPE_COMBO_CARPOOL == $aDecision['combo_type']
            && \BizLib\Constants\OrderSystem::CARPOOL_TYPE_STATION == $aDecision['carpool_type']
            && true == $aDecision['bubble_flag']
        ) {
            $aShowText['show_url'] = Language::getTextFromDcmp('carpool_long_order_bubble_url');
        }

        if ('carpool_commute' == $sFrom) {
            $aShowText = $this->_buildCarpoolCommuteShowText($aExtInfo);
        }

        $aFromItem = [
            'source_product'    => (int)($aDecision['business_id']),
            'source_level'      => (int)($aDecision['require_level']),
            'source_combo_type' => (int)($aDecision['combo_type']),
        ];

        $aFrom = [$aFromItem,];

        $aTo = array(
            'guide_product'    => (int)($aDecision['business_id']),
            'guide_level'      => (int)($aDecision['require_level']),
            'guide_combo_type' => (int)($aDecision['combo_type']),
            'guide_action'     => 1,
            'tab_switch'       => 1,
            'default_show'     => 0,
        );

        return array(
            'show_text' => $aShowText,
            'from'      => $aFrom,
            'to'        => $aTo,
        );
    }

    /**
     * 根据trace_id获取表单数据.
     *
     * @return array
     */
    public function getRecordByTraceId($sTraceID) {
        $aGetRecordResp = array();
        if (empty($sTraceID)) {
            return $aGetRecordResp;
        }

        // dds服务
        $aRecordGetReq  = array(
            'trace_id' => $sTraceID,
            'fields'   => array(self::DDS_GETRECORD_FIELD_ESTIMATE_IDS),
        );
        $aGetRecordResp = $this->_oDecision->getRecordResult($aRecordGetReq);

        return $aGetRecordResp;
    }

    /**
     * 获取athena推荐类型，品类信息
     * @return array
     */
    public function getRecommendFlagInfo() {
        if (isset($this->_aRecommendFlagInfo)) {
            return $this->_aRecommendFlagInfo;
        }

        if (!empty($this->_aDecisionResp['product_list'])) {
            foreach ($this->_aDecisionResp['product_list'] as $aProductInfo) {
                if (!empty($aProductInfo['recommend_info']) && !empty($aProductInfo['recommend_info']['extra_info'])
                    && isset($aProductInfo['recommend_info']['extra_info']['recommend_type_flag'])
                ) {
                    $this->_aRecommendFlagInfo = [
                        'recommend_flag' => $aProductInfo['recommend_info']['extra_info']['recommend_type_flag'],
                    ];
                    if (NormalRecommendInfo::PREFERENTIAL_RECOMMEND_TYPE == $aProductInfo['recommend_info']['extra_info']['recommend_type_flag']) {
                        $this->_aRecommendFlagInfo['preferential_product_category'] = $aProductInfo['product_category'];
                    }

                    return $this->_aRecommendFlagInfo;
                }
            }
        }

        $this->_aRecommendFlagInfo = [];
        return $this->_aRecommendFlagInfo;
    }


    /**
     * 获取athena品类运力平衡标识
     * @return bool|null
     */
    public function getCapacityBalanceFlag() {
        $aRecommendInfo = $this->getRecommendFlagInfo();
        return !isset($aRecommendInfo['recommend_flag']) || NormalRecommendInfo::CAPACITY_RECOMMEND_TYPE != $aRecommendInfo['recommend_flag'];
    }

    /**
     * 获取品类是否命中athena优惠推荐
     * @param int $iProductCategory iProductCategory
     * @return bool
     */
    public function getPreferentialRecommendFlag($iProductCategory) {
        $aRecommendInfo = $this->getRecommendFlagInfo();
        return isset($aRecommendInfo['preferential_product_category']) && $iProductCategory == $aRecommendInfo['preferential_product_category'];
    }

    /**
     * @return bool
     */
    private function _isInland() {
        return Locsvr::isInland($this->_aInfo['order_info']['area']) && in_array(Language::getLanguage(), array(\BizLib\Utils\Language::ZH_CN));
    }

    /**
     * @return bool
     */
    private function _isInlandEnglish() {
        return Locsvr::isInland($this->_aInfo['order_info']['area']) && in_array(Language::getLanguage(), array(\BizLib\Utils\Language::EN_US));
    }

    /**
     * @return array
     */
    public function filerProducts() {
        $aDecisionRemove = [];
        foreach ($this->_aDecisionResp['product_list'] as $iIndex => $aProduct) {
            if ($aProduct['remove_flag']) {
                $aDecisionRemove[] = $aProduct['product_category'];
            }
        }

        if (!empty($aDecisionRemove)) {
            foreach ($this->_aInfo as $iIndex => $aInfo) {
                $iPcID = $aInfo['order_info']['product_category'];
                if (in_array($iPcID, $aDecisionRemove)) {
                    if (ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTE == $iPcID) {
                        $this->_aInterCarpoolInfo = $aInfo;
                    }

                    unset($this->_aInfo[$iIndex]);
                }
            }
        }

        return $this->_aInfo;
    }

    /**
     * @return array
     */
    public function getInterCarpoolInfo() {
        return $this->_aInterCarpoolInfo;
    }
}
