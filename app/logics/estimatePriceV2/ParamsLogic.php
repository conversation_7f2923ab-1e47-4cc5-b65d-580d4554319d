<?php

/**
 * 根据乘客输入出发地、目的地进行车费预估的参数处理逻辑类文件
 * 处理请求账单系统预估接口、企业支付方式接口、券系统可用券、乘客运营活动券接口需要的通用参数
 * 例如:
 * business_id、client_type等公共参数;
 * pid、phone等乘客相关参数;
 * from_lng、from_lat、require_level等订单相关参数
 * district、countyid等区域相关参数.
 *
 * 是否开通拼车,只有请求账单系统预估接口使用,相关处理逻辑在预估账单交互逻辑类文件
 * 是否可用优惠券,在请求券系统获取可用券、乘客运营系统获取活动券使用,相关处理逻辑在券系统交互、乘客运营交互逻辑类文件
 *
 * @author: <EMAIL>
 * @date: 17/3/14
 */

namespace PreSale\Logics\estimatePriceV2;

use BizCommon\Logics\Anycar\AnyCarCommonLogic;
use BizCommon\Logics\Scene\RouteComboTypeLogic;
use BizCommon\Models\Order\OrderStopoverPoints;
use BizCommon\Utils\Horae;
use BizCommon\Constants\Commute;
use BizLib\Client\HoraeClient;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\RespCode;
use BizLib\Log as NuwaLog;
use BizLib\Log;
use BizLib\Utils\Address;
use BizLib\Utils\CarLevel;
use BizLib\Utils\Common as UtilsCommon;
use BizLib\Utils\Coordtrans;
use BizLib\Utils\MemberVersion;
use BizLib\Utils\Language;
use BizLib\Utils\LogHelper;
use BizLib\Utils\MapHelper;
use BizLib\Utils\UtilHelper;
use Disf\SPL\Trace;
use PreSale\Infrastructure\Util\AirGap;
use PreSale\Logics\anycar\ExitEstimateLogic;
use PreSale\Logics\estimatePriceV2\multiResponse\MainHelper;
use PreSale\Logics\scene\SceneListLogic;
use PreSale\Models\order\OrderAirPort;
use BizCommon\Constants\OrderNTuple;
use BizCommon\Logics\Order\FieldOrderNTuple;
use BizLib\Utils;
use BizLib\Constants;
use BizLib\Constants\OrderSystem;
use BizLib\Constants\Horae as HoraeConstants;
use Xiaoju\Apollo\Apollo as ApolloV2;
use BizLib\Exception\ExceptionWithResp;
use BizLib\Exception\InvalidArgumentException;
use BizLib\ErrCode;
use BizLib\Utils as BizUtils;
use BizLib\Client\MemberSystemClient;
use BizLib\Utils\Horae as HoraeUtils;
use BizLib\Utils\Partner;
use BizCommon\Models\Dfs\BaseDfsComModel;
use PreSale\Logics\order\AnyCarOrderLogic;
use PreSale\Logics\estimatePriceV2\params\SceneParamsLogic;
use PreSale\Logics\estimatePriceV2\params\RecognitionLogic;
use Nebula\Exception\BaseException as SceneException;
use Nebula\Exception\Route\InterRouteException;
use BizCommon\Logics\Scene\SceneChooseLogic;
use Xiaoju\Diconf;
use BizLib\Client\RouteBrokerServiceClient;
use BizLib\Config;
use BizCommon\Models\Passenger\Passenger;
use BizCommon\Models\Passenger\PassengerPFS;
use BizCommon\Models\Order\OrderStation;
use BizCommon\Models\Order\Order;
use BizCommon\Models\Activity\OneKeyCall;
use BizCommon\Models\Activity\Coupon;
use BizLib\Utils\Registry;
use BizLib\Config as NuwaConfig;
use BizLib\Utils\Common;
use TripcloudCommon\Utils\Product as TripcloudProduct;
use BizLib\Utils\ProductCategory;
use PreSale\Logics\carpool\CarpoolCommuteCard;
use BizCommon\Models\Cache\EstimatePrice;
use BizCommon\Models\Order\LineUpOrderComModel;
use BizLib\Client\PriceApiClient;
use Nuwa\ApolloSDK\Apollo;
use BizCommon\Logics\CommuteCard\CommuteCardOpenLogic;
use PreSale\Exception\LuxuryNeedFilterException;
use BizCommon\Utils\Product;
use BizCommon\Utils\Common as BizCommon;
use PreSale\Models\fence\FenceInfo;
use BizLib\Utils\Product as UtilProduct;

/**
 * Class ParamsLogic.
 */
class ParamsLogic
{
    /** @var $orderAirPort OrderAirPort */
    public $orderAirPort;
    /**
     * @var ParamsLogic
     */
    protected static $_oInstance = null;

    /**
     * @var \CI_Controller
     */
    private $_oCI = null;

    /**
     * @var array
     */
    private $_aErrMsg;

    /**
     * @var bool
     */
    private $_bB2BChannel;

    /**
     * @var null|DecisionLogic
     */
    private $_oDecision = null;

    /**
     * @var
     */
    private $_aOneConfList = [];

    /**
     * @var
     */
    private $_aRequest;

    /**
     * @var array
     */
    private $_aCommonInfo        = []; //公共参数
    private $_aPassengerInfo     = []; //乘客相关信息
    private $_aOrderInfo         = []; //订单相关信息
    private $_aAreaInfo          = []; //区域相关信息
    private $_aToAreaInfo        = []; //目的地区域相关新
    private $_aOneKeyActivity    = []; //一键叫活动相关信息
    private $_aCustomServiceInfo = []; //定制化服务参数
    private $_fenseInfo          = []; // 命中围栏信息，火车站埋点需求增加
    private $_aDefaultSelection  = [];
    private $_aOrderInfoBeforePrice = []; // 缓存原始OrderInfo信息，passenger请求Price后，返回值组装采用的是$sCarLevel做Key，导致订单信息丢失
    private $_aCommuteRouteCardInfo = []; //用户所选路线的拼车通勤卡信息
    private $_aPopefsResult         = []; //pope的特征
    private $_aLineupLen            = null; //排队队列信息


    //记录转换后的开始坐标
    private $_aStartCoord = [];
    //记录转换后的目的地坐标
    private $_aDestCoord = [];

    //拼车特有的路线信息
    private $_aCarpoolRouteInfo = [];

    //城际拼车的路线信息
    private $_aInterRouteInfo = [];
    //端上入参（如城际departure_range）有些需要在识别后才能处理，先发到这个结构
    private $_aExtraInfo = [];

    //特价车特有的路线信息
    private $_aFlatRateRouteInfo = [];
    private $_oPassengerPFS;
    private $_oPassenger;

    //特殊场景参数枚举值
    private $_iSpecialSceneParam = 0;

    /**
     * @var null|IdentificationLogic
     */
    private $_oIdentification = null;

    /**
     * @var RecognitionLogic
     */
    private $_oRecognition = null;
    /**
     * @var PopefsLogic
     */
    private $_oPopefs = null;

    /**
     * @var bool
     */
    private $_bHitMultiEstimateForPremium = false;

    /**
     * 导流系统过滤不加入签名参数列表
     * array(
     *      'common_info' => 1, //过滤外层数组
     *      'common_info' => array(
     *                             'common_info' => array(
     *                                                    'platform_type'=>1, //过滤内层参数
     *                                                   )
     *                            )
     *      ).
     *
     * @var array
     */
    private $_aFilterSignParams = [
        'common_info' => [
            'platform_type'      => 1,
            'from'               => 1,
            'open_id'            => 1,
            'suuid'              => 1,
            'reestimate_ability' => 1,
            'terminal_id'        => 1,
            'feature_enable'     => 1,
            'dialog_id'          => 1,
            'pre_trace_id'       => 1,
            'access_key_id'      => 1,
            'taxi_car_type_list' => 1,
        ],
    ];

    /**
     @var $aIsMultiRoute
     */
    public $aIsMultiRoute = false;

    const COORDINATE_WGS   = 'wgs';
    const COORDINATE_BAIDU = 'baidu';
    const GUIDE_SIGN       = 'guide_result';
    const PREMIUM_CALL_TAG = 'call_premium';

    const APPID          = 100001;
    const CARDBIZID      = 100001;
    const NONCARPOOLUSER = 0;
    const CARPOOLUSER    = 1;

    const PIXTYPE_LOW  = 1;
    const PIXTYPE_MID  = 2;
    const PIXTYPE_HIGH = 3;

    const MONTH_CARD_PAGE = 2; //通勤卡月卡

    const CUSTOM_PICKUP_GUIDE = 2;

    const NAME_N_TUPLE    = 'n_tuple';
    const NAME_MULTI_INFO = 'multi_info';
    /**
     * ParamsLogic constructor.
     */
    private function __construct() {
        //多语言，将错误信息统一集中在config/text/{language}/errno.php中。language与语言有关
        $this->_aErrMsg   = Config::text('errno', 'pEstimatePrice_error_msg');
        $this->_oDecision = DecisionLogic::getInstance();
        //$this->_oCI->load->model('passenger/PassengerPFS');
        $this->_oPassengerPFS = PassengerPFS::getInstance();
        //$this->_oCI->load->model('passenger/Passenger');
        $this->_oPassenger      = Passenger::getInstance();
        $this->_oIdentification = IdentificationLogic::getInstance();
        $this->_oPopefs         = PopefsLogic::getInstance();
    }

    /**
     * 实例化.
     *
     * @return ParamsLogic
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * @return array
     */
    public function getActivityInfo() {
        if ($this->_oRecognition == null) {
            return [];
        }

        return $this->_oRecognition->getActivityInfo();
    }

    /**
     * 设置客户端上传的参数
     * @param array $aParams aParams
     * @return void
     */
    public function setRequest($aParams) {
        $this->_aRequest = $aParams;
    }

    /**
     * 获取客户端上传参数
     * @param string $key key
     * @return bool
     */
    public function getRequest($key) {
        if (!empty($this->_aRequest) && is_array($this->_aRequest)) {
            return $this->_aRequest[$key] ?? false;
        }

        return false;
    }

    /*
     * 获取屏幕属性，用作快车兜底逻辑使用
     */
    public function getPixelType($sPixels) {
        $iPixret = self::PIXTYPE_MID;
        if (0 == strlen(trim($sPixels))) {
            return $iPixret;
        }

        $aPixParam = explode('*', $sPixels);
        if (2 != count($aPixParam)) {
            return $iPixret;
        }

        if ($aPixParam[0] <= 500) {
            $iPixret = self::PIXTYPE_LOW;
        } elseif ($aPixParam[0] > 500 && $aPixParam[0] <= 1000) {
            $iPixret = self::PIXTYPE_MID;
        } else {
            $iPixret = self::PIXTYPE_HIGH;
        }

        return $iPixret;
    }

     // 注释-待删
//    /**
//     * ios 端发版后one_conf没下发拼车参数,在其进行one_conf参数兜底.
//     *
//     * @param $aOneConf
//     * @param $aParams
//     */
//    private function _updateOneConf(&$aOneConf, $aParams) {
//        // one_conf为空,不兜底
//        if (empty($aOneConf) || 0 == count($aOneConf)) {
//            return;
//        }
//
//        // 如果非5.1.6版本号,或者非ios端,不兜底
//        if ('5.1.16' != (string)($aParams['app_version']            ) || Constants\Common::CLIENT_TYPE_IOS != $aParams['client_type']
//        ) {
//            return;
//        }
//
//        // 多产品线不兜底
//        $aNum = 0;
//        foreach ($aOneConf as $aItem) {
//            if (UtilProduct::COMMON_PRODUCT_ID_FAST_CAR == $aItem['business_id']) {
//                ++$aNum;
//
//                // 如果拼车item已经存在、跨城拼车或区域一口价不兜底
//                if (HoraeConstants::TYPE_COMBO_CARPOOL == $aItem['combo_type']
//                    || HoraeConstants::TYPE_COMBO_CARPOOL_INTER_CITY == $aItem['combo_type']
//                    || HoraeConstants::TYPE_COMBO_FLAT_RATE == $aItem['combo_type']
//                    || \BizCommon\Utils\Order::isSpecialRateV2($aItem)
//                ) {
//                    return;
//                }
//
//                continue;
//            }
//
//            // item中有一个不是快车,不兜底
//            return;
//        }
//
//        // 如果item中是3项不兜底
//        if ($aNum != count($aOneConf) || 3 == $aNum) {
//            return;
//        }
//
//        $bAddItem = false;
//        foreach ($aOneConf as $aItem) {
//            // item中有普通快车,才兜底
//            if (HoraeConstants::TYPE_COMBO_DEFAULT == $aItem['combo_type'] && CarLevel::DIDI_PUTONG_CAR_LEVEL == $aItem['require_level']) {
//                $bAddItem = true;
//                break;
//            }
//        }
//
//        if (empty($bAddItem)) {
//            return;
//        }
//
//        $aOneConfItem['product_id']    = UtilProduct::PRODUCT_ID_FAST_CAR;
//        $aOneConfItem['business_id']   = UtilProduct::COMMON_PRODUCT_ID_FAST_CAR;
//        $aOneConfItem['require_level'] = CarLevel::DIDI_PUTONG_CAR_LEVEL;
//        $aOneConfItem['combo_type']    = HoraeConstants::TYPE_COMBO_CARPOOL;
//        $aOneConfItem['is_default']    = 0;
//
//        // 仅在快车线下添加图标逻辑
//        if (UtilProduct::COMMON_PRODUCT_ID_FAST_CAR == $aParams['business_id']) {
//            $aOneConfItem['strong_display'] = 0;
//
//            $aIconConfig = Config::text('config_text', 'fastcar_icon');
//            $iPixType    = $this->getPixelType($aParams['pixels']);
//            if (self::PIXTYPE_LOW == $iPixType) {
//                $aOneConfItem['gray_icon']  = $aIconConfig['carpool_gray_low'];
//                $aOneConfItem['light_icon'] = $aIconConfig['carpool_light_low'];
//            } elseif (self::PIXTYPE_MID == $iPixType) {
//                $aOneConfItem['gray_icon']  = $aIconConfig['carpool_gray_mid'];
//                $aOneConfItem['light_icon'] = $aIconConfig['carpool_light_mid'];
//            } else {
//                $aOneConfItem['gray_icon']  = $aIconConfig['carpool_gray_high'];
//                $aOneConfItem['light_icon'] = $aIconConfig['carpool_light_high'];
//            }
//        }
//
//        $aOneConf[] = $aOneConfItem;
//
//        return;
//    }

    /**
     * @return string
     */
    public function getDefaultSelection() {
        return $this->_aDefaultSelection;
    }

    /**
     * @param $aParams
     *
     * @return bool
     */
    public function hitMultiEstimateForPremium() {
        return $this->_bHitMultiEstimateForPremium;
    }

    /**
     * 为anycar每一个子项补全product_category，这个功能应收敛到one_conf
     * 另：手动为拼车补上carpool_type=2
     * @param array $aMultiRequireProduct $aMultiRequireProduct
     * @return array
     */
    // 2024.9.11注释-待删
//    private function _formatMultiRequireProduct($aMultiRequireProduct) {
//
//        if (!empty($aMultiRequireProduct) && is_array($aMultiRequireProduct)) {
//            $oPC = new ProductCategory();
//            foreach ($aMultiRequireProduct as $key => &$aProduct) {
//                if (empty($aProduct['product_category'])) {
//                    if (true == $aProduct['remove_flag']) {
//                        unset($aMultiRequireProduct[$key]);
//                    }
//
//                    //此处mock拼车的carpool_type 为2
//                    if (Utils\Horae::isCarpool($aProduct['combo_type'], $aProduct['require_level']) && empty($aProduct['carpool_type'])) {
//                        $aProduct['carpool_type'] = 2;
//                    }
//
//                    $aProduct['product_category'] = $oPC->getProductCategoryByNTuple($aProduct);
//                }
//            }
//
//            $aMultiRequireProduct = array_values($aMultiRequireProduct);
//        }
//
//        return $aMultiRequireProduct;
//    }

    /**
     * 获取请求Price-api之前的订单信息.
     *
     * @param $iProductID
     * @param $iComboType
     * @param $iRequireLevel
     *
     * @return array
     */
    public function getCacheOriginOrderInfo($iProductID, $iComboType, $iRequireLevel, $aExtra = []) {
        $aProduct = [
            'product_id'    => $iProductID,
            'combo_type'    => $iComboType,
            'require_level' => $iRequireLevel,
            'level_type'    => $aExtra['level_type'] ?? 0,
        ];
        $sCacheKey = AnyCarCommonLogic::getGroupKey($aProduct);
        return $this->_aOrderInfoBeforePrice[$sCacheKey] ?? [];
    }

      // 2024.9.11注释-待删
//    public function getQuationInfo($sEstimateID) {
//        $aParams = array(
//            'estimate_id' => $sEstimateID,
//            'fields'      => array(
//                self::NAME_N_TUPLE,
//                self::NAME_MULTI_INFO,
//            ),
//        );
//
//        $_oClient = new PriceApiClient(PriceApiClient::MODULE_NAME, []);
//        $aRet     = $_oClient->getQuotation($aParams);
//        if (isset($aRet['errno']) && 0 == $aRet['errno']) {
//            return $aRet['data'];
//        }
//
//        return [];
//    }

    /**
     * 获取请求"企业支付方式"接口相关参数.
     *
     * @param array $aBillInfo
     *
     * @return array
     */
    public function getPaymentsParams(array $aBillInfo) {
        $aNonCarpoolBills = $aBillInfo['bills'][$this->_aOrderInfo['require_level']]['noncarpool'] ?? [];
        $aPaymentsParams  = [
            'bill_info'      => [
                'is_carpool_open' => $aBillInfo['is_carpool_open'] ?? 0,
                'noncarpool'      => [
                    'dynamic_total_fee' => $aNonCarpoolBills['dynamic_total_fee'] ?? 0,
                ],
            ],
            'order_info'     => $this->_aOrderInfo,
            'passenger_info' => $this->_aPassengerInfo,
            'common_info'    => $this->_aCommonInfo,
        ];

        return $aPaymentsParams;
    }

    /**
     * 获取请求券和乘客运营接口相关参数.
     *
     * @param array $aBillInfo
     * @param bool  $bNoncarpoolUseCoupon
     * @param bool  $bCarpoolUseCoupon
     *
     * @return array
     */
    public function getActivityParams(array $aBillInfo, bool $bNoncarpoolUseCoupon, bool $bCarpoolUseCoupon) {
        $aCarpoolBills    = $aBillInfo['bills'][$this->_aOrderInfo['require_level']]['carpool'];
        $aNonCarpoolBills = $aBillInfo['bills'][$this->_aOrderInfo['require_level']]['noncarpool'];
        $this->_aOrderInfo['start_dest_distance'] = round($aBillInfo['driver_metre'] / 1000, 2);

        $fWithoutMemberProtectTotalFee = 0;
        if ($aNonCarpoolBills['is_hit_member_capping']) {
            $fWithoutMemberProtectTotalFee = round(
                $aNonCarpoolBills['dynamic_total_fee'] + $aNonCarpoolBills['dynamic_price_without_member_capping'] - $aNonCarpoolBills['member_dynamic_capping'],
                2
            );
            $fWithoutMemberProtectBasicFee = round(
                $aNonCarpoolBills['basic_total_fee'] + $aNonCarpoolBills['dynamic_price_without_member_capping'] - $aNonCarpoolBills['member_dynamic_capping'],
                2
            );
        }

        $fCarpoolSuccFee = $aBillInfo['biz_price_field']['carpool_success_fee'] ?? $aCarpoolBills['carpool_success_fee'];
        $aActivityParams = [
            'bill_info'             => [
                'driver_minute'   => $aBillInfo['driver_minute'],
                'estimate_id'     => $aBillInfo['estimate_id'],
                'is_carpool_open' => $aBillInfo['is_carpool_open'],
                'noncarpool'      => [
                    'dynamic_total_fee'                => $aNonCarpoolBills['dynamic_total_fee'],
                    'basic_total_fee'                  => $aNonCarpoolBills['basic_total_fee'],
                    'dynamic_price_id'                 => $aNonCarpoolBills['dynamic_info']['dynamic_price_id'] ?? 0,
                    'dynamic_times'                    => (float)(
                        $aNonCarpoolBills['dynamic_info']['dynamic_times']
                    ) + 1,
                    'without_member_protect_basic_fee' => $fWithoutMemberProtectBasicFee,
                    'without_member_protect_total_fee' => $fWithoutMemberProtectTotalFee,
                ],
                'carpool'         => [
                    'dynamic_total_fee' => $fCarpoolSuccFee ?? ($aCarpoolBills['dynamic_total_fee'] ?? 0),
                    // 若命中跨城拼车两口价，无动调，取同一值
                    'basic_total_fee'   => $fCarpoolSuccFee ?? ($aCarpoolBills['basic_total_fee'] ?? 0),
                ],
                'currency'        => $aBillInfo['currency'],
            ],
            'common_info'           => $this->_aCommonInfo,
            'order_info'            => $this->_aOrderInfo,
            'passenger_info'        => $this->_aPassengerInfo,
            'one_key_activity'      => [
                'activity_switch' => $this->_aOneKeyActivity['activity_switch'],
            ],
            'noncarpool_use_coupon' => $bNoncarpoolUseCoupon,
            'carpool_use_coupon'    => $bCarpoolUseCoupon,
        ];

        return $aActivityParams;
    }

    // 注释-待删
//    /**
//     * 返回给乘客端相关参数.
//     *
//     * @param array $aBillInfo
//     * @param array $aPaymentsInfo
//     * @param array $aActivityInfo
//     *
//     * @return array
//     */
//    public function getResponseParams(array $aBillInfo, array $aPaymentsInfo, array $aActivityInfo, $sAthenaInfo = '') {
//        $iRequireLevel = $this->_aOrderInfo['require_level'];
//        //若账单降级为实时订单，把combo_type改写为实时
//        if ((HoraeConstants::TYPE_COMBO_FLAT_RATE == $this->_aOrderInfo['combo_type']
//            || \BizCommon\Utils\Order::isSpecialRateV2($this->_aOrderInfo))
//            && isset($aBillInfo['product_infos'][$iRequireLevel]['noncarpool']['combo_type']) && $aBillInfo['product_infos'][$iRequireLevel]['noncarpool']['combo_type'] != $this->_aOrderInfo['combo_type']
//        ) {
//            $this->_aOrderInfo['combo_type'] = $aBillInfo['product_infos'][$iRequireLevel]['noncarpool']['combo_type'];
//        }
//
//        $aResponseParams = [
//            'common_info'      => $this->_aCommonInfo,
//            'order_info'       => $this->_aOrderInfo,
//            'passenger_info'   => $this->_aPassengerInfo,
//            'bill_info'        => $aBillInfo,
//            'payments_info'    => $aPaymentsInfo,
//            'activity_info'    => $aActivityInfo,
//            'athena_info'      => $sAthenaInfo,
//            'one_key_activity' => [
//                'activity_switch' => $this->_aOneKeyActivity['activity_switch'],
//            ],
//        ];
//
//        return $aResponseParams;
//    }

    /**
     * 返回给乘客端相关参数.
     *
     * @param array $aBillInfo
     * @param array $aPaymentsInfo
     * @param array $aActivityInfo
     *
     * @return array
     */
    public function getMultiResponseParams(array $aAthenaRequests, array $aAthenaResults, array $aEstimateParams) {
        $aResponseParams = [];
        //将athena的快车、拼车和优享,组织为按照车型为key进行组织,覆盖掉拼车的数据
        $aRequestParams = [];
        $aOrderType     = [];
        $aHoldType      = [];
        $aSeatNum       = [];
        //这个地方快车和拼车的orderInfo相互覆盖，不改以车型为key的组织方式，始终是得不到根本性的解决...
        $aOrderNTuple       = [];
        $aCustomServiceInfo = [];
        foreach ($aAthenaRequests['price_params'] as $iIndex => $aParam) {
            $sCarLevel          = $aParam['order_info']['require_level'];
            $aOrderType         = $this->_setRealOrderType($aOrderType, $aParam);
            $aHoldType          = $this->_setHoldType($aHoldType, $aParam);
            $aSeatNum           = $this->_setSeatNum($aSeatNum, $aParam);
            $aOrderNTuple       = $this->_setOrderNTuple($aOrderNTuple, $aParam);
            $aCustomServiceInfo = $this->_setCustomServiceInfo($aCustomServiceInfo, $aParam);
            $aEstimateParam     = reset($aEstimateParams);
            if (isset($aRequestParams[$sCarLevel]) && !empty($aRequestParams[$sCarLevel])) {
                //拼车数据中的订单类型不能被覆盖
                $aRequestParams[$sCarLevel]['order_info']['real_type'] = $aOrderType[$sCarLevel];
                //拼车数据中的通勤特征不能被覆盖
                $aRequestParams[$sCarLevel]['order_info']['carpool_commute_info'] = $this->_getCommuteInfo($aParam);
                $aRequestParams[$sCarLevel]['order_info']['order_n_tuple']        = $aOrderNTuple[$sCarLevel];
                $aRequestParams[$sCarLevel]['order_info']['hold_type']            = $aHoldType[$sCarLevel];
                $aRequestParams[$sCarLevel]['order_info']['carpool_seat_num']     = $aSeatNum[$sCarLevel];
                //个性化服务 同一车型可能不同，不能覆盖
                $aRequestParams[$sCarLevel]['custom_service_info'] = $aCustomServiceInfo[$sCarLevel];
                continue;
            }

            $aRequestParams[$sCarLevel] = $aParam;
            $aRequestParams[$sCarLevel]['order_info']['order_n_tuple']        = $aOrderNTuple[$sCarLevel];
            $aRequestParams[$sCarLevel]['order_info']['carpool_commute_info'] = $this->_getCommuteInfo($aParam);
            //$aRequestParams[$sCarLevel]['common_info']['shake_flag']          = $aEstimateParam['common_info']['shake_flag'];
            $aRequestParams[$sCarLevel]['common_info']['pixels']    = $aEstimateParam['common_info']['pixels'];
            $aRequestParams[$sCarLevel]['common_info']['page_type'] = $aEstimateParam['common_info']['page_type'];
            $aRequestParams[$sCarLevel]['custom_service_info']      = $aCustomServiceInfo[$sCarLevel];
        }

        foreach ($aAthenaResults as $sCarLevel => $aCarLevelAthenaResult) {
            $aAthenaResultsCount = 0;
            foreach ($aCarLevelAthenaResult as $aAthenaResult) {
                ++$aAthenaResultsCount;
                if (!$this->aIsMultiRoute && $aAthenaResultsCount > 1) {
                    //非多路线预估每个sCarLevel里面只有1组数据
                    break;
                }

                $aOrderInfo  = $aRequestParams[$sCarLevel]['order_info'];
                $sOrderExtra = $aRequestParams[$sCarLevel]['extra_info']['order'];
                if (isset($sOrderExtra) && !empty($sOrderExtra)) {
                    $aOrderExtra = json_decode($sOrderExtra, true);
                    $aOrderInfo  = $aOrderInfo + $aOrderExtra;
                }

                // 场景识别命中的围栏
                $aEstimateParam           = reset($aEstimateParams);
                $aOrderInfo['fence_info'] = $aEstimateParam['order_info']['fence_info'];

                $iRequireLevel = $aOrderInfo['require_level'];
                $sAppVersion   = $aRequestParams[$sCarLevel]['common_info']['app_version'];
                $aBillInfo     = json_decode($aAthenaResult['bill_info'], true);

                if ((HoraeConstants::TYPE_COMBO_FLAT_RATE == $aOrderInfo['combo_type']
                    || \BizCommon\Utils\Order::isSpecialRateV2($aOrderInfo))
                    && isset($aBillInfo['product_infos'][$iRequireLevel]['noncarpool']['combo_type'])
                    && $aBillInfo['product_infos'][$iRequireLevel]['noncarpool']['combo_type'] != $aOrderInfo['combo_type']
                ) {
                    $aOrderInfo['combo_type'] = $aBillInfo['product_infos'][$iRequireLevel]['noncarpool']['combo_type'];
                }

                //bill 信息中增添路线信息
                $aBillInfo = $this->_loadRouteInfo($aBillInfo, $iRequireLevel, $sAppVersion);

                $aNewMultiRequireProduct = [];
                if (!empty($aBillInfo['multi_info'])) { // anycar
                    $aBillInfo['multi_info'] = AnyCarOrderLogic::getInstance()->fixSourceMultiInfo(
                        $aBillInfo['multi_info'],
                        $aOrderInfo
                    );
                    $aNewMultiRequireProduct = AnyCarOrderLogic::getInstance()->getMultiBillInfo($aBillInfo);
                }

                $aOneKeyActivity = [];
                if (!empty($aRequestParams[$sCarLevel]['one_key_activity'])) {
                    $aOneKeyActivity = json_decode($aRequestParams[$sCarLevel]['one_key_activity'], true);
                }

                $aResponseParam = [
                    'common_info'           => $aRequestParams[$sCarLevel]['common_info'],
                    'order_info'            => $aOrderInfo,
                    'passenger_info'        => json_decode($aRequestParams[$sCarLevel]['passenger_info'], true),
                    'bill_info'             => $aBillInfo,
                    'payments_info'         => json_decode($aAthenaResult['payments_info'], true),
                    'activity_info'         => json_decode($aAthenaResult['activity_info'], true),
                    'athena_info'           => $aAthenaResult['athena_info'],
                    'athena_extra'          => $aAthenaResult['athena_extra'],
                    'one_key_activity'      => $aOneKeyActivity,
                    'multi_require_product' => $aNewMultiRequireProduct,
                    'custom_service_info'   => $aRequestParams[$sCarLevel]['custom_service_info'],
                    'feature_data'          => $aEstimateParam['feature_data'],
                    'queue_data'            => $this->_aLineupLen,
                ];
                if ($this->aIsMultiRoute) {
                    $aResponseParams[] = $aResponseParam;
                } else {
                    //保留非多路线原有的数组逻辑
                    $aResponseParams[$sCarLevel] = $aResponseParam;
                }
            }
        }

        return $aResponseParams;
    }

    /**
     * 返回给乘客端相关参数.
     *
     * @param array $aAthenaRequests aAthenaRequests
     * @param array $aAthenaResults  aAthenaResults
     * @param array $aEstimateParams aEstimateParams
     *
     * @return array
     */
    public function getMultiResponseParamsV2(array $aAthenaRequests, array $aAthenaResults, array $aEstimateParams) {
        $aRequestParams  = [];
        $aResponseParams = [];

        foreach ($aAthenaRequests['price_params'] as $iIndex => $aParam) {
            $aEstimateParam = reset($aEstimateParams);
            // 补全 custom_service_info 字段，此处将逻辑收敛，之后使用 option_custom_service 承载用户选择的个性化服务项
            $aParam['custom_service_info']     = PersonalizedCustomServiceLogic::getInstance()->getCustomServiceInfo($aParam);
            $aParam['order_info']['real_type'] = $aParam['order_info']['order_type'];
            $aParam['order_info']['carpool_commute_info'] = $this->_getCommuteInfo($aParam);
//            $aParam['common_info']['shake_flag']          = $aEstimateParam['common_info']['shake_flag'];
            $aParam['common_info']['pixels']    = $aEstimateParam['common_info']['pixels'];
            $aParam['common_info']['page_type'] = $aEstimateParam['common_info']['page_type'];
            if (isset($aParam['extra_info']['order'])) {
                $aOrderExtra = json_decode($aParam['extra_info']['order'], true);
                $aOrderInfo  = $aParam['order_info'];
                if (!empty($aOrderExtra)) {
                    $aParam['order_info'] = $aOrderInfo + $aOrderExtra;
                }

                if (!empty($aOrderExtra['estimate_id'])) {
                    $aRequestParams[$aOrderExtra['estimate_id']] = $aParam;
                }
            }
        }

        foreach ($aAthenaResults as $index => $aAthenaResult) {
            $aBillInfo   = $aAthenaResult['bill_info'];
            $sEstimateId = $aBillInfo['estimate_id'];
            $aReqParam   = $aRequestParams[$sEstimateId];
            if (empty($aReqParam)) {
                continue;
            }

            $aOrderInfo = $aReqParam['order_info'];
            if (isset($aOrderInfo['n_tuple']['airport_type']) && empty($aOrderInfo['airport_type'])) {
                $aOrderInfo['airport_type'] = $aOrderInfo['n_tuple']['airport_type'];
            }

            // 场景识别命中的围栏
            $aEstimateParam           = reset($aEstimateParams);
            $aOrderInfo['fence_info'] = $aEstimateParam['order_info']['fence_info'];

            $iRequireLevel = $aOrderInfo['require_level'];
            $sAppVersion   = $aReqParam['common_info']['app_version'];

            if ((HoraeConstants::TYPE_COMBO_FLAT_RATE == $aOrderInfo['combo_type']
                || \BizCommon\Utils\Order::isSpecialRateV2($aOrderInfo))
                && isset($aBillInfo['product_infos'][$iRequireLevel]['combo_type'])
                && $aBillInfo['product_infos'][$iRequireLevel]['combo_type'] != $aOrderInfo['combo_type']
            ) {
                $aOrderInfo['combo_type'] = $aBillInfo['product_infos'][$iRequireLevel]['combo_type'];
            }

            //bill 信息中增添路线信息
            $aBillInfo = $this->_loadRouteInfoNew($aBillInfo, $iRequireLevel, $sAppVersion);

            $aNewMultiRequireProduct = [];
            if (!empty($aBillInfo['multi_info'])) { // anycar
                $aBillInfo['multi_info'] = AnyCarOrderLogic::getInstance()->fixSourceMultiInfo(
                    $aBillInfo['multi_info'],
                    $aOrderInfo
                );
                $aNewMultiRequireProduct = AnyCarOrderLogic::getInstance()->getMultiBillInfo($aBillInfo);
            }

            $aResponseParam    = [
                'common_info'           => $aReqParam['common_info'],
                'order_info'            => $aOrderInfo,
                'passenger_info'        => json_decode($aReqParam['passenger_info'], true),
                'bill_info'             => $aBillInfo,
                'payments_info'         => $aAthenaResult['payments_info'],
                'activity_info'         => $aAthenaResult['activity_info'],
                'price_extra'           => $aAthenaResult['price_extra'],
                'athena_info'           => $aAthenaResult['athena_info'],
                'athena_extra'          => $aAthenaResult['athena_extra'],
                'one_key_activity'      => [
                    'activity_switch' => $aReqParam['activity_switch'],
                ],
                'multi_require_product' => $aNewMultiRequireProduct,
                'custom_service_info'   => $aReqParam['custom_service_info'], // 用户预估上传选择的个性化服务字段
                'feature_data'          => $aEstimateParam['feature_data'],
                'queue_data'            => $this->_aLineupLen,
                'preference_product'    => $aReqParam['extra_info']['preference_product'],
            ];
            $aResponseParams[] = $aResponseParam;
        }

        return $aResponseParams;
    }

    /**
     * 获取请求Athena系统相关参数.
     *
     * @param array $aAthenaParams
     * @param array $aEstimateParams
     *
     * @return array
     */
    public function getAthenaParams(array $aAthenaParams, array $aEstimateParams) {
        //注意：新增参数需要更新thift文件，并通知Athena的同学,切记切记。。。
        $aResult = [];
        $aResult['price_params'] = [
            'common_info'         => [
                'taxi_car_type_list' => $aEstimateParams['common_info']['taxi_car_type_list'],
                'transparent_data'   => $aEstimateParams['common_info']['transparent_data'],
                'business_id'        => $aEstimateParams['common_info']['business_id'],
                'origin_id'          => $aEstimateParams['common_info']['origin_id'],
                'terminal_id'        => $aEstimateParams['common_info']['terminal_id'],
                'app_version'        => $aEstimateParams['common_info']['app_version'],
                'client_type'        => $aEstimateParams['common_info']['client_type'],
                'imei'               => '',
                'lang'               => $aEstimateParams['common_info']['lang'],
                'data_type'          => $aEstimateParams['common_info']['data_type'],
                'is_from_b2b'        => $aEstimateParams['common_info']['is_from_b2b'],
                'is_from_webapp'     => $aEstimateParams['common_info']['is_from_webapp'],
                'guide_request'      => $aEstimateParams['common_info']['guide_request'],
                'from'               => $aEstimateParams['common_info']['from'],
                'platform_type'      => $aEstimateParams['common_info']['platform_type'],
                'reestimate_ability' => $aEstimateParams['common_info']['reestimate_ability'],
                'feature_enable'     => $aEstimateParams['common_info']['feature_enable'],
                'route_id'           => $aEstimateParams['common_info']['route_id'],
                'dialog_id'          => $aEstimateParams['common_info']['dialog_id'],
                'pre_trace_id'       => $aEstimateParams['common_info']['pre_trace_id'],
                'access_key_id'      => $aEstimateParams['common_info']['access_key_id'],
            ],
            'order_info'          => [
                'current_lng'              => $aEstimateParams['order_info']['current_lng'],
                'current_lat'              => $aEstimateParams['order_info']['current_lat'],
                'area'                     => $aEstimateParams['order_info']['area'],
                'order_type'               => $aEstimateParams['order_info']['order_type'],
                'channel'                  => $aEstimateParams['order_info']['channel'],
                'from_lng'                 => $aEstimateParams['order_info']['from_lng'],
                'from_lat'                 => $aEstimateParams['order_info']['from_lat'],
                'from_poi_id'              => $aEstimateParams['order_info']['from_poi_id'],
                'from_poi_type'            => $aEstimateParams['order_info']['from_poi_type'],
                'from_address'             => $aEstimateParams['order_info']['from_address'],
                'from_name'                => $aEstimateParams['order_info']['from_name'],
                'to_lng'                   => $aEstimateParams['order_info']['to_lng'],
                'to_lat'                   => $aEstimateParams['order_info']['to_lat'],
                'to_poi_id'                => $aEstimateParams['order_info']['to_poi_id'],
                'to_poi_type'              => $aEstimateParams['order_info']['to_poi_type'],
                'to_address'               => $aEstimateParams['order_info']['to_address'],
                'to_name'                  => $aEstimateParams['order_info']['to_name'],
                'scene_type'               => $aEstimateParams['order_info']['scene_type'],
                'combo_type'               => $aEstimateParams['order_info']['combo_type'],
                'combo_id'                 => $aEstimateParams['order_info']['combo_id'],
                'departure_time'           => $aEstimateParams['order_info']['departure_time'],
                'call_car_type'            => $aEstimateParams['order_info']['call_car_type'],
                'call_car_phone'           => $aEstimateParams['order_info']['call_car_phone'],
                'willing_wait'             => $aEstimateParams['order_info']['willing_wait'],
                'map_type'                 => $aEstimateParams['order_info']['map_type'],
                'activity_id'              => $aEstimateParams['order_info']['activity_id'],
                'user_type'                => $aEstimateParams['order_info']['user_type'],
                'payments_type'            => $aEstimateParams['order_info']['payments_type'],
                'guide_type'               => $aEstimateParams['order_info']['guide_type'],
                'guide_state'              => $aEstimateParams['order_info']['guide_state'],
                'product_id'               => $aEstimateParams['order_info']['product_id'],
                'require_level'            => $aEstimateParams['order_info']['require_level'],
                'is_fast_car'              => $aEstimateParams['order_info']['is_fast_car'],
                'carpool_seat_num'         => $aEstimateParams['order_info']['carpool_seat_num'],
                'carpool_station_type'     => $aEstimateParams['order_info']['carpool_station_type'],
                'carpool_require_trace_id' => $aEstimateParams['order_info']['carpool_require_trace_id'],
                'current_station_id'       => $aEstimateParams['order_info']['current_station_id'],
                'starting_name'            => $aEstimateParams['order_info']['starting_name'],
                'dest_name'                => $aEstimateParams['order_info']['dest_name'],
                'district'                 => $aEstimateParams['order_info']['district'],
                'abstract_district'        => $aEstimateParams['order_info']['abstract_district'],
            ],
            'passenger_info'      => json_encode($aEstimateParams['passenger_info'], JSON_UNESCAPED_UNICODE),
            'custom_service_info' => json_encode($aEstimateParams['custom_service_info'], JSON_UNESCAPED_UNICODE),
            'one_key_activity'    => json_encode($aEstimateParams['one_key_activity'], JSON_UNESCAPED_UNICODE),
        ];
        $aOrderInfo = $aEstimateParams['order_info'];
        //n元组新加字段需要转化，合并以原始传参为准
        $newField   = \BizCommon\Logics\Order\FieldConverter::getNDimensionalNewField($aOrderInfo);
        $aOrderInfo = array_merge($newField, $aOrderInfo);
        // 填充n_tuple
        $aNTuple = FieldOrderNTuple::getOrderNTupleByOrder($aOrderInfo);
        $aEstimateParams['order_info']['n_tuple'] = isset($aEstimateParams['order_info']['n_tuple']) ? array_merge(
            $aNTuple,
            $aEstimateParams['order_info']['n_tuple']
        ) : $aNTuple;
        if (!$aEstimateParams['order_info']['n_tuple']['is_dual_carpool_price']) {
            $aEstimateParams['order_info']['n_tuple']['is_dual_carpool_price'] = false;
        }

        //N元组内type 按预估order_type 修正
        $aEstimateParams['order_info']['n_tuple']['type'] = $aEstimateParams['order_info']['order_type'];

        $aEstimateParams['order_info']['airport_id'] = (int)($aEstimateParams['order_info']['airport_id']);
        //临时放在这里, 新版本会放到extra_info下的common_info
        $aEstimateParams['order_info']['designated_driver']     = $aEstimateParams['common_info']['designated_driver'];
        $aEstimateParams['order_info']['designated_driver_tag'] = $aEstimateParams['common_info']['designated_driver_tag'];
        // 国家码从这里传递，personalized_customized_option 也从这里传递
        $aExtraInfo = array_diff_key($aEstimateParams['order_info'], $aResult['price_params']['order_info']);
        $aExtraInfo['access_key_id'] = $aEstimateParams['common_info']['access_key_id'];
        // 需求识别 carpool 判断前置，将结果写入 extra_info，price中可以直接escape掉select判断
        if (isset($aEstimateParams['dds_recognition']) && $aEstimateParams['dds_recognition']) {
            $aExtraInfo['recognition_result'] = true; // map[string]string
            $aExtraInfo['station_list']       = $aEstimateParams['station_list'];
            $aExtraInfo['commute_card_info']  = $aEstimateParams['order_info']['commute_card'] ?? [];
        }

        //修复上面取dds_recognition的bug. 为保证服务稳定，加个开关
        $apolloV2 = new ApolloV2();
        if ($apolloV2->featureToggle(
            'gs_passenger_dds_recognition_fix',
            [
                'key'        => rand(1, 500000),
                'phone'      => $aEstimateParams['passenger_info']['phone'],
                'area'       => $aEstimateParams['order_info']['area'],
                'product_id' => $aEstimateParams['order_info']['product_id'],
            ]
        )->allow() || ($aEstimateParams['order_info']['n_tuple']['carpool_long_order']) || Horae::isLowPriceCarpool($aEstimateParams['order_info']['n_tuple'])
        ) {
            if (isset($aEstimateParams['order_info']['dds_recognition']) && $aEstimateParams['order_info']['dds_recognition']) {
                $aExtraInfo['recognition_result'] = true; // map[string]string
                $aExtraInfo['station_list']       = $aEstimateParams['order_info']['station_list'];
            }
        }

        //获取dos存储格式的途经点信息
        $aWayPointsInfo = OrderStopoverPoints::getDosFieldsByStopoverPoints($aEstimateParams['order_info']['stopover_points']);
        if (!empty($aWayPointsInfo)) {
            $aExtraInfo = array_merge($aExtraInfo, $aWayPointsInfo);
        }

        $aResult['price_params']['extra_info']['order'] = json_encode($aExtraInfo, JSON_UNESCAPED_UNICODE);
        $aResult['price_params']['extra_info']['multi_require_product'] = !empty($aEstimateParams['multi_require_product']) ? json_encode($aEstimateParams['multi_require_product'], JSON_UNESCAPED_UNICODE) : '';
        $aResult['price_params']['extra_info']['preference_product']    = $aEstimateParams['common_info']['preference_product'];
        $aResult['price_params']['extra_info']['special_scene_param']   = $this->_iSpecialSceneParam;
        $aResult['price_params']['extra_info']['is_athena_add_product'] = $aEstimateParams['is_athena_add_product'];
        $aResult['price_params']['extra_info']['is_special_price']      = $aEstimateParams['order_info']['is_special_price'];
        $aResult['price_params']['extra_info']['product_category']      = $this->_getProductCategory($aEstimateParams['order_info']);
        $aResult['price_params']['extra_info']['x_new_form']            = $aEstimateParams['common_info']['x_new_form'];
        $aResult['price_params']['extra_info']['preferred_route_id']    = $aEstimateParams['common_info']['preferred_route_id'];
        $aResult['price_params']['check_sum'] = $this->createSign($aResult['price_params']);
        $aResult['athena_bubbe_params']       = [
            'client_type'        => $aEstimateParams['common_info']['client_type'],
            'app_version'        => $aEstimateParams['common_info']['app_version'],
            'map_type'           => $aEstimateParams['order_info']['map_type'],
            'lang'               => $aEstimateParams['common_info']['lang'],
            'network_type'       => (string)($aAthenaParams['network_type']),
            'carpool_seat_num'   => $aEstimateParams['order_info']['carpool_seat_num'],
            'session_id'         => (string)($aAthenaParams['session_id']),
            'cur_business_id'    => $aEstimateParams['common_info']['cur_business_id'],
            'require_level'      => $aEstimateParams['order_info']['require_level'],
            'order_type'         => $aEstimateParams['order_info']['order_type'],
            'business_ids'       => (array) explode('|', $aAthenaParams['business_ids']),
            'phone'              => (string)($aEstimateParams['passenger_info']['phone']),
            'pid'                => (int)($aEstimateParams['passenger_info']['pid']),
            'require_levels'     => (array) explode('|', $aAthenaParams['require_levels']),
            'load_require_level' => (int)($aAthenaParams['load_require_level']),
            'from_area'          => $aEstimateParams['order_info']['area'],
            'from_lng'           => $aEstimateParams['order_info']['from_lng'],
            'from_lat'           => $aEstimateParams['order_info']['from_lat'],
            'to_lng'             => $aEstimateParams['order_info']['to_lng'],
            'to_lat'             => $aEstimateParams['order_info']['to_lat'],
            'channel'            => $aEstimateParams['order_info']['channel'],
            'extra_info'         => [
                'athena_extra'       => json_encode($aAthenaParams['extra_info'], JSON_UNESCAPED_UNICODE),
                'mobile_type'        => $aEstimateParams['common_info']['mobile_type'],
                'model'              => $aEstimateParams['common_info']['model'],
                'one_conf'           => $this->_convertOneConf(),
                'preference_product' => $aEstimateParams['common_info']['preference_product'],
            ],
            'token'              => $aEstimateParams['common_info']['token'],
            'access_key_id'      => (int)$aEstimateParams['common_info']['access_key_id'],
        ];

        //针对ios header中没有lang参数;获取url中的lang添加到header中
        $sHeaderLang = Language::getHeaderLanguage();
        if (empty($sHeaderLang) || !in_array($sHeaderLang, Language::getSupportLanguages())) {
            Language::setHeaderLanguage($aEstimateParams['common_info']['lang']);
        }

        $aResult['trace_params'] = [
            'trace_id'     => Trace::traceId(),
            'span_id'      => Trace::spanId(),
            'caller'       => MODULE_NAME,
            'src_method'   => $_SERVER['REQUEST_URI'],
            'hint_code'    => LogHelper::getHintCode(),
            'hint_content' => Trace::hintContent(),
        ];
        //android和ios参数中session_id和sessionId导致的不兼容兜底
        if (isset($aAthenaParams['sessionId']) && !empty($aAthenaParams['sessionId'])) {
            $aResult['athena_bubbe_params']['session_id'] = (string)($aAthenaParams['sessionId']);
        }

        return $aResult;
    }

    private function _getAthenaBubbleProductData(array $aEstimateParams) {
        $aData = array(
            'product_id'    => $aEstimateParams['order_info']['product_id'],
            'require_level' => $aEstimateParams['order_info']['require_level'],
            'combo_type'    => $aEstimateParams['order_info']['combo_type'],
            'is_add'        => $aEstimateParams['is_athena_add_product'],
            'extra_info'    => array(
                'custom_service_info' => json_encode($aEstimateParams['custom_service_info'], JSON_UNESCAPED_UNICODE),
                'order_info'          => json_encode($aEstimateParams['order_info'], JSON_UNESCAPED_UNICODE),
                'is_special_price'    => $aEstimateParams['order_info']['is_special_price'],
                'product_category'    => $this->_getProductCategory($aEstimateParams['order_info']),
                'carpool_type'        => $aEstimateParams['order_info']['carpool_type'],
            ),
        );

        return $aData;
    }

    /**
     * 获取请求Athena系统相关参数.
     *
     * @param array $aAthenaParams
     * @param array $aEstimateParams
     *
     * @return array
     */
    public function getMultiAthenaParams(array $aAthenaParams, array $aEstimateParams) {
        $aResult            = array();
        $aApiAddProductData = array();
        $aAthenaBubbleParams = [];
        //根据oneConf的配置组织批量athena参数
        foreach ($aEstimateParams as $iIndex => $aParams) {
            $aAthenaParamsItem   = $aAthenaParams;
            $aEstimateParamsItem = $aParams;
            $aAthenaRequest      = $this->getAthenaParams($aAthenaParamsItem, $aEstimateParamsItem);
            array_push($aResult, $aAthenaRequest['price_params']);
            array_push($aApiAddProductData, $this->_getAthenaBubbleProductData($aEstimateParamsItem));
            if ((empty($aAthenaBubbleParams) || !is_array($aAthenaBubbleParams)) && !empty($aAthenaRequest['athena_bubbe_params'])) {
                $aAthenaBubbleParams = $aAthenaRequest['athena_bubbe_params'];
            }
        }

        //决定是否用数据压缩方式，Apollo控制放量，全量后可以把apollo 以及is_new_data_send字段干掉。但是要和Athena的人（国军，孔力）同步 @zhixiang
        $sIsNewDataSend = '';
        $apolloV2       = new ApolloV2();
        if ($apolloV2->featureToggle(
            'gs_passenger_new_send_data',
            [
                'key'   => rand(1, 500000),
                'phone' => $aAthenaBubbleParams['phone'],
                'pid'   => $aAthenaBubbleParams['pid'],
                'city'  => $aAthenaBubbleParams['from_area'],
            ]
        )->allow()
        ) {
            $sIsNewDataSend = '1';
        }

        //摇一摇参数
//        $sShakeFlag   = $this->_aCommonInfo['shake_flag'];
//        $sPreTraceId  = $this->_aCommonInfo['pre_trace_id'];
        $aMultiParams = array(
            'price_params'  => $aResult,
            'bubble_params' => $aAthenaBubbleParams,
            'extra_info'    => array(
                'is_new_data_send' => $sIsNewDataSend,
//                'shake_flag'       => $sShakeFlag,
            ),
        );
        //pre_estimate_id_list
//        if (1 == $sShakeFlag) {
//            $aPreEstimateIdList = $this->_oDecision->getRecordByTraceId($sPreTraceId);
//            if (!empty($aPreEstimateIdList) && isset($aPreEstimateIdList['estimate_ids'])) {
//                $aMultiParams['extra_info']['pre_estimate_id_list'] = $aPreEstimateIdList['estimate_ids'];
//            }
//        }

        $aMultiParams['bubble_params']['api_add_product'] = $aApiAddProductData;

        $aMultiParams['extra_info']['feature_data'] = empty($this->_aPopefsResult) ? '{}' : json_encode($this->_aPopefsResult);

        $aMultiParams['extra_info']['business_option'] = $aEstimateParams[0]['common_info']['business_option'] ?? '';//透传到price-api

        return $aMultiParams;
    }

    /**
     * 拼装返回给Athena的数据。
     *
     * @param array $aParams
     * @param array $aBillInfo
     * @param array $aPaymentsInfo
     * @param array $aActivityInfo
     *
     * @return array
     */
    public function getEstimatePriceInfo(array $aBillInfo, array $aPaymentsInfo, array $aActivityInfo) {
        $aResponseInfo = [
            'bill_info'     => json_encode($aBillInfo, JSON_UNESCAPED_UNICODE),
            'payments_info' => json_encode($aPaymentsInfo, JSON_UNESCAPED_UNICODE),
            'activity_info' => json_encode($aActivityInfo, JSON_UNESCAPED_UNICODE),
        ];

        //client_type为导流请求时,check_sum返回guide_result
        if (Constants\Common::CLIENT_TYPE_GUIDE == $this->_aCommonInfo['client_type']) {
            $aResponseInfo['check_sum'] = self::GUIDE_SIGN;
        } else {
            $aResponseInfo['check_sum'] = $this->createSign($aResponseInfo);
        }

        $aResponseInfo['athena_info'] = $this->_getAthenaInfo($aBillInfo, $aPaymentsInfo, $aActivityInfo);

        return $aResponseInfo;
    }

    /**
     * 获取Athena所需要的额外数据.
     *
     * @param array $aBillInfo
     * @param array $aPaymentsInfo
     * @param array $aActivityInfo
     */
    private function _getAthenaInfo(array $aBillInfo, array $aPaymentsInfo, array $aActivityInfo) {
        $sCarLevel          = $this->_aOrderInfo['require_level'];
        $aNoCarPoolBillInfo = $aBillInfo['bills'][$sCarLevel]['noncarpool'];
        $aNoCarPoolFee      = [
            'estimate_fee'  => $aActivityInfo['estimate_fee'] ?? 0,                                 //预估费
            'coupon_amount' => $aActivityInfo['noncarpool_estimate_detail']['detail']['amount'] ?? 0,         //优惠券信息
            'dynamic_info'  => $aNoCarPoolBillInfo['dynamic_info'], //非拼车动调信息,包括动调比例、预测拼车率
            'discount_info' => $this->_formatForString($aNoCarPoolBillInfo['discount_info']),       //动折信息
        ];

        //返回结构初始化
        $aAthenaInfo = [
            'total_fee'            => [                                                                   //总预估费用
                'carpool'   => [],
                                                                                                          //拼车预估费用和券信息
                'nocarpool' => $aNoCarPoolFee,
                                                                                                          //非拼车预估费用和券信息
            ],
            'payment_type'         => $this->_getDefaultPayType($aPaymentsInfo),
            'carpool_station_info' => ['uid' => -1, 'name' => ''],                            //站点信息
            'carpool_decision'     => 0,                                                           //是否拼车
            'bubble_id'            => $aBillInfo['estimate_id'] ?? '',                             //预估id
            'dynamic_diff_price'   => $aNoCarPoolBillInfo['dynamic_diff_price'] ?? 0,              //动调费用
        ];

        $aCarPoolBillInfo = $aBillInfo['bills'][$sCarLevel]['carpool'];
        if (isset($aCarPoolBillInfo) && !empty($aCarPoolBillInfo) && $aBillInfo['is_carpool_open']) {
            $aCarPoolFee = [
                'estimate_fee'  => $aActivityInfo['discount_fee'] ?? 0,
                'coupon_amount' => $aActivityInfo['carpool_estimate_detail']['detail']['amount'] ?? 0,
                'dynamic_info'  => $aCarPoolBillInfo['dynamic_info'],
                'discount_info' => $this->_formatForString($aCarPoolBillInfo['discount_info']),
            ];

            $aAthenaInfo['total_fee']['carpool'] = $aCarPoolFee;
            $uid  = $aBillInfo['carpool_station_info']['uid'];
            $name = $aBillInfo['carpool_station_info']['name'];
            if (isset($uid) && isset($name)) {
                $aAthenaInfo['carpool_station_info'] = [
                    'uid'  => $uid,
                    'name' => $name,
                ];
            }
            $bCarPoolDecision = Passenger::getInstance()->getCarPoolDecision($this->_aPassengerInfo['pid']);
            $aAthenaInfo['dynamic_diff_price'] = $aCarPoolBillInfo['dynamic_diff_price'] ?? 0;
            if ($aBillInfo['is_carpool_open'] && $bCarPoolDecision) {
                $aAthenaInfo['carpool_decision'] = 1;
            }
        }

        return $aAthenaInfo;
    }

    /**
     * 一维数组的value格式化为string类型。
     *
     * @param $aDiscountInfo
     *
     * @return mixed
     */
    private function _formatForString($aDiscountInfo) {
        if (empty($aDiscountInfo) || !is_array($aDiscountInfo)) {
            return $aDiscountInfo;
        }

        foreach ($aDiscountInfo as $key => &$value) {
            $value = (string)($value);
        }

        return $aDiscountInfo;
    }

    // 注释-待删
//    /**
//     * 豪华车指定司务员或类型.
//     */
//    private function _getDesignatedDriver($designatedDriver) {
//        $aResult = ['designated_driver' => '', 'designated_driver_tag' => ''];
//        if (empty($designatedDriver)) {
//            return $aResult;
//        }
//
//        if ($designatedDriver > 0) {
//            $aResult['designated_driver'] = "$designatedDriver";
//            //指定司机时，需获取司机的星级用于计费
//            $tags  = BaseDfsComModel::getInstance()->getDFS($designatedDriver, ['lux_driver_specific_feature'])['lux_driver_specific_feature'];
//            $aTags = empty($tags) ? [] : (array) (json_decode($tags, true)['specific_grade_feature']);
//            foreach ($aTags as $tag) {
//                if (-1 != $tag && $tag > -100) {//英语司务员和100以后的值预留给其他的
//                    $aResult['designated_driver_tag'] = "$tag";
//                    break;
//                }
//            }
//        }
//
//        if ($designatedDriver < 0) {
//            $aResult['designated_driver_tag'] = "$designatedDriver";
//        }
//
//        return $aResult;
//    }


    /**
     * 参数检查.
     */
    // 2024.9.10注释-待删
//    private function _checkParams() {
//        //坐标检查
//        $this->_checkCoordinate();
//
//        //区域信息检查
//        //$this->_checkCurrArea();
//        //开城检查
//        $this->_checkOpenService();
//
//        //小费金额检查
//        $this->_checkTipInfo();
//
//        // 特殊场景、功能检查
//        //一键叫活动参数检查
//        $this->_checkActivity();
//
//        //路线检查，区域一口价、跨城拼车
//        $this->_checkRoute();
//        //机场单机场参数检查
//        $this->_checkFence();
//
//        // airport and railway station service card
//        $this->_checkAirportStationControl();
//        $this->_cacheAirportStationControl();
//    }

    // 注释-待删
//    /**
//     * @param $iComboType
//     * 获取有特殊计价场景的线路
//     *
//     * @return array|mixed
//     */
//    private function _checkRouteByComboType($iComboType) {
//        $objHoraeSdk = new \BizLib\Client\HoraeClient();
//        //优化，防止批量预估的时候，重复去horae取路径信息
//        $sKey           = "getMatchRouteIds_{$this->_aCommonInfo['business_id']}_{$iComboType}_{$this->_aOrderInfo['from_lat']}_{$this->_aOrderInfo['from_lng']}_{$this->_aOrderInfo['to_lat']}_{$this->_aOrderInfo['to_lng']}";
//        $aCheckRouteRet = Registry::getInstance()->get($sKey);
//        if (empty($aCheckRouteRet)) {
//            $aCheckRouteRet = $objHoraeSdk->getMatchRouteIds(
//                $this->_aCommonInfo['business_id'],
//                \BizLib\Utils\Horae::getSceneType($iComboType),
//                $this->_aOrderInfo['from_lat'],
//                $this->_aOrderInfo['from_lng'],
//                $this->_aOrderInfo['to_lat'],
//                $this->_aOrderInfo['to_lng'],
//                time(),
//                $this->_aPassengerInfo['phone']
//            );
//            Registry::getInstance()->set($sKey, $aCheckRouteRet);
//        }
//
//        return $aCheckRouteRet;
//    }

    // 注释-待删
//    /*
//     * 当没有命中围栏的情况下，判断是否需要阻塞发单.
//     *
//     * <AUTHOR> <<EMAIL>>
//     *
//     * @return bool
//     */
//    private function _isBlockForRouteComboType($iRouteComboType) {
//        if (HoraeConstants::TYPE_COMBO_CARPOOL == $iRouteComboType) {
//            return false;
//        }
//
//        if (\BizLib\Utils\Horae::isFlatRate($iRouteComboType)) {
//            // 当场景是专车 & 快车 的区域一口价（combo_type 303）时，不阻塞发单。
//            return false;
//        }
//
//        $aSpecialOrder = array_merge($this->_aOrderInfo, ['combo_type' => $iRouteComboType]);
//        if (\BizCommon\Utils\Order::isSpecialRateV2($aSpecialOrder)) {
//            // 当场景是 快车 的线路一口价（combo_type 314）时，不阻塞发单。
//            return false;
//        }
//
//        // 如果入参是302的时候，会阻塞发单
//        return true;
//    }

    /**
     * 需求 http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=147352213
     * 特价车城市、时间、距离检查.
     * 是否命中了时间及距离不显示特价车.
     *
     * @return true : 命中， false：没命中
     */
    // 2024.9.10注释-待删
//    private function _checkSpecialRate() {
//        $bRet = false;
//
//        if (empty($this->_aOrderInfo['departure_time']) || empty($this->_aOrderInfo['area'])) {
//            return $bRet;
//        }
//
//       // $apolloV2      = new \Xiaoju\Apollo\Apollo();
//       // $bApolloSwitch = $apolloV2->featureToggle(
//           // 'gs_special_rate_call_map_sdk',
//           // [
//               // 'phone' => $this->_aPassengerInfo['phone'],
//               // 'key'   => rand(1, 500000),
//           // ]
//       // )->allow();
//        $bApolloSwitch = false;
//
//        if (!\BizCommon\Utils\Order::isSpecialRateV2($this->_aOrderInfo) || !$bApolloSwitch) {
//            return $bRet;
//        }
//
//       // $bFileConfigApolloSwitch = $apolloV2->featureToggle(
//           // 'gs_special_rate_call_map_sdk_config',
//           // [
//               // 'city'  => $this->_aOrderInfo['area'],
//               // 'phone' => $this->_aPassengerInfo['phone'],
//               // 'key'   => $this->_aPassengerInfo['phone'],
//           // ]
//       // )->allow();
//        $iCityId = $this->_aOrderInfo['area'];
//
//        $bFileConfigApolloSwitch = true;
//        if ($bFileConfigApolloSwitch) {
//            $bRet = $this->_hasSpecialRateFromFile($iCityId);
//        } else {
//            $bRet = $this->_hasSpecialRateFromTitan($iCityId);
//        }
//
//        return $bRet;
//    }

    // 注释-待删
//    /**
//     * 从文件读取分城等配置检查特价车入口，因titan配置涉及22城市，先采用biz-config作新titan过渡方案.
//     *
//     * @param $iCityId
//     *
//     * @return bool
//     */
//    private function _hasSpecialRateFromFile($iCityId) {
//        // Get city config from file
//        $aSpecialRateCityEntry = Config::text('config_passenger', 'special_rate_city_entry');
//        if (empty($aSpecialRateCityEntry[$iCityId])) {
//            return false;
//        }
//
//        $aCityConfig = $aSpecialRateCityEntry[$iCityId];
//
//        $iDistance      = $aCityConfig['distance'] ?? 0;
//        $iDepartureHour = date('H', $this->_aOrderInfo['departure_time']);
//        $iDistance      = $this->_checkSpecialRateTime($iDepartureHour, $aCityConfig['conf_info'], $iDistance);
//        if ($iDistance <= 0) {
//            return false;
//        }
//
//        // get  Route-Broke PointToPointBaseRoute
//        $iTmpDistance = $this->_getPointToPointBaseRoute();
//        $iDistance    = $iDistance * 1000;
//        if ($iTmpDistance > 0 && $iDistance >= $iTmpDistance) {
//            return true;
//        }
//
//        return false;
//    }

    // 注释-待删
//    /**
//     * 从titan读取分城等配置检查特价车入口.
//     *
//     * @param $iCityId
//     *
//     * @return bool
//     */
//    // 2024.9.10注释-待删
////    private function _hasSpecialRateFromTitan($iCityId) {
////        $bRet = false;
////        // Get titan config
////        $aCityConfig = Utils\BizConfigLoader::getSpecialRateCityConfig($iCityId);
////        // 没有配置的城市默认有特价车入口, 不命中
////        if (empty($aCityConfig['conf_info'])) {
////            return $bRet;
////        }
////
////        // 需要取时分转换为分钟
////        $iDepartureTime = date('H:i', $this->_aOrderInfo['departure_time']);
////
////        // 有效的距离区间
////        $aEffectDistInterval = [];
////        foreach ($aCityConfig['conf_info'] as $aConfInfo) {
////            if (isset($aConfInfo['start_time'])
////                && isset($aConfInfo['end_time'])
////                && isset($aConfInfo['distance'])
////                && $iDepartureTime >= $aConfInfo['start_time']
////                && $iDepartureTime <= $aConfInfo['end_time']
////            ) {
////                $aEffectDistInterval = (array) $aConfInfo['distance'];
////                break;
////            }
////        }
////
////        // 当前时间点不在限制时间区间内，需展示特价车入口
////        if (empty($aEffectDistInterval)) {
////            return $bRet;
////        }
////
////        // get  Route-Broke PointToPointBaseRoute
////        $iRealDistance = $this->_getPointToPointBaseRoute();
////
////        $bRet = true;
////        foreach ($aEffectDistInterval as $aDistInterval) {
////            if (!isset($aDistInterval['left_dist']) || !isset($aDistInterval['right_dist'])) {
////                continue;
////            }
////
////            $aDistInterval['left_dist']  *= 1000;
////            $aDistInterval['right_dist'] *= 1000;
////
////            // 满足某个里程区间，不命中不显示特价车入口
////            if ($iRealDistance >= $aDistInterval['left_dist'] && $iRealDistance <= $aDistInterval['right_dist']) {
////                $bRet = false;
////                break;
////            }
////        }
////
////        return $bRet;
////    }
//
//    private function _getPointToPointBaseRoute() {
//        $aRequest = [
//            'src_point' => [
//                'latitude'  => $this->_aOrderInfo['from_lat'],
//                'longitude' => $this->_aOrderInfo['from_lng'],
//            ],
//            'dst_point' => [
//                'latitude'  => $this->_aOrderInfo['to_lat'],
//                'longitude' => $this->_aOrderInfo['to_lng'],
//            ],
//        ];
//        //地图要求的请求来源常量
//        $aRequest['caller_id'] = 'gulfstream_260_dds';
//        //如果不需要计算预估时间，请务必加上这一句，以提高响应速度
//        $aRequest['no_need_eta'] = true;
//        //0 时间最短，1 距离最短
//        $aRequest['strategy'] = 1;
//        //请求超时时间
//        $aRequest['req_timeout'] = 50000;
//
//        //国家码
//        $aRequest['trace'] = [
//            'regionId' => $this->_aOrderInfo['trip_country'],
//        ];
//        //城市码
//        $aRequest['city_id'] = $this->_aOrderInfo['area'];
//
//        $oRouteBrokerClient = new RouteBrokerServiceClient();
//        $aResult            = $oRouteBrokerClient->pointToPointBaseRoute($aRequest);
//
//        if (!isset($aResult['errno']) || 0 != $aResult['errno']) {
//            NuwaLog::warning(
//                ErrCode\Msg::formatArray(
//                    ErrCode\Code::E_PASSENGER_P2P_BASE_ROUTE_ERROR,
//                    ['result' => json_encode($aResult)]
//                )
//            );
//
//            return 0;
//        }
//
//        return $aResult['dist'];
//    }

   // 注释-待删
//    private function _checkSpecialRateTime($iDepartureHour, $aConfInfos, $iDistance) {
//        $iRet = 0;
//        if (empty($aConfInfos)) {
//            return $iRet;
//        }
//
//        foreach ($aConfInfos as $aConfInfo) {
//            if (isset($aConfInfo['start_time'])
//                && isset($aConfInfo['end_time'])
//                && $iDepartureHour >= (int) $aConfInfo['start_time']
//                && $iDepartureHour <= (int) $aConfInfo['end_time']
//            ) {
//                if (isset($aConfInfo['distance'])) {
//                    return (int) $aConfInfo['distance'];
//                } else {
//                    return $iDistance;
//                }
//            }
//        }
//
//        return $iRet;
//    }

    /**
     * 路线检查.
     */
    // 2024.9.10注释-待删
//    private function _checkRoute() {
//        // 获取有能力请求路线的场景
//        $oRouteComboTypeLogic = new RouteComboTypeLogic();
//        $aCondition           = [
//            'passenger_phone' => $this->_aPassengerInfo['phone'],
//            'pid'             => $this->_aPassengerInfo['pid'],
//            'client_type'     => $this->_aCommonInfo['client_type'],
//            'app_version'     => $this->_aCommonInfo['app_version'],
//            'is_from_webapp'  => $this->_aCommonInfo['is_from_webapp'],
//            'product_id'      => $this->_aOrderInfo['product_id'],
//            'district'        => $this->_aOrderInfo['district'],
//        ];
//        $iRouteComboType      = $oRouteComboTypeLogic->getRouteComboType(
//            'flat_rate_for_estimate_price',
//            $this->_aOrderInfo,
//            $aCondition
//        );
//
//        $aSpecialOrder = array_merge($this->_aOrderInfo, ['combo_type' => $iRouteComboType]);
//        if (\BizCommon\Utils\Order::isSpecialRateV2($aSpecialOrder)
//            && false == $this->_aCommonInfo['is_from_webapp']
//            && version_compare($this->_aCommonInfo['app_version'], '5.2.2') < 0
//        ) {
//            return;
//        }
//
//        // 如果 $iRouteComboType == false
//        // 区域渗透、城际拼车，在DDS流量开发的情况下，$iRouteComboType == false，流程继续
//        if ($iRouteComboType) {
//            $aCheckRouteRet            = $this->_checkRouteByComboType($iRouteComboType);
//            $bIsBlockForRouteComboType = $this->_isBlockForRouteComboType($iRouteComboType);
//            if (isset($aCheckRouteRet['errno']) && GLOBAL_SUCCESS == $aCheckRouteRet['errno']) {
//                //父路线ID
//                if (!empty($aCheckRouteRet['match_routes'])) {
//                    // combo_type=4的场景 match_route的结构同price-api IDL定义的不一致,此处暂时不往orderInfo结构中写入
//                    if (HoraeConstants::TYPE_COMBO_CARPOOL != $iRouteComboType) {
//                        $this->_aOrderInfo['match_routes'] = $aCheckRouteRet['match_routes'];
//                    }
//
//                    $aSpecialOrder = ['product_id' => $this->_aOrderInfo['product_id'], 'require_level' => $this->_aOrderInfo['require_level'], 'combo_type' => $iRouteComboType];
//                    if (HoraeConstants::TYPE_COMBO_CARPOOL == $iRouteComboType) {
//                        $this->_aCarpoolRouteInfo[$this->_aOrderInfo['require_level']] = $aCheckRouteRet['match_routes'];
//                    } elseif (\BizCommon\Utils\Order::isSpecialRateV2($aSpecialOrder)) {
//                        $this->_aFlatRateRouteInfo = $aCheckRouteRet['match_routes'];
//                    }
//                }
//
//                // 城际拼车combo_id, departure_range, departure_time, bubble_time
//                if (HoraeConstants::TYPE_COMBO_CARPOOL_INTER_CITY == $iRouteComboType) {
//                    $aRouteInfo = $aCheckRouteRet['match_routes'][0] ?? [];
//                    if (!isset($this->_aOrderInfo['departure_range']) || empty($this->_aOrderInfo['departure_range'])) {
//                        if (!empty($this->_aExtraInfo['departure_range'])) {
//                            //三叉戟场景departure_range在这个阶段才能确定
//                            $this->_aOrderInfo['departure_range'] = $this->_aExtraInfo['departure_range'];
//                        } else {
//                            //首次冒泡，用户没有选择时间片，采用最近的第一个时间片冒泡, 当天23点后可能没有时间片，需要取明天的第一个时间片
//                            $this->_aOrderInfo['departure_range'] = $aRouteInfo['time_span'][0]['range'][0]['value'] ?? ($aRouteInfo['time_span'][1]['range'][0]['value'] ?? '[]');
//                        }
//
//                        $aDepartureRange = json_decode($this->_aOrderInfo['departure_range'], true);
//                        $this->_aOrderInfo['departure_time'] = $aDepartureRange[1] ?? $this->_aOrderInfo['departure_time'];
//                    }
//
//                    $this->_aOrderInfo['bubble_time'] = time();
//                    $this->_aOrderInfo['combo_id']    = (int)($aRouteInfo['route_id']);
//                }
//
//                //计价使用参数
//                $sComboIds = $aCheckRouteRet['combo_ids'] ?? '';
//                if ($sComboIds) {
//                    $this->_aOrderInfo['combo_ids'] = $sComboIds;
//                }
//
//                if (false == $bIsBlockForRouteComboType && $sComboIds) {
//                    // 不阻塞的场景下，客户端没有传递combo_type值，所以要自行设置
//                    $this->_aOrderInfo['combo_type'] = $iRouteComboType;
//                }
//            } else {
//                //路线检查失败，预估失败
//                if ($bIsBlockForRouteComboType) {
//                    if (!isset($aCheckRouteRet['errno'])) {
//                        $aReturn = UtilsCommon::getErrMsg(P_ERRNO_PARAMS_ERROR, $this->_aErrMsg, $this->_aOrderInfo['product_id']);
//                    } else {
//                        $aReturn = UtilsCommon::getErrMsg($aCheckRouteRet['errno'], $this->_aErrMsg, $this->_aOrderInfo['product_id']);
//                    }
//
//                    if (HoraeUtils::isInterCityCarPoolScene($iRouteComboType)) {
//                        // 跨城拼车抛出基于场景异常
//                        $oEx = new InterRouteException($aReturn['errmsg']);
//                        $oEx->setRespCode($aReturn['errno'])
//                            ->setRespMsg($aReturn['errmsg'])
//                            ->setRespData(['miss_route' => $aCheckRouteRet['miss_route']]);
//                        throw $oEx;
//                    } else {
//                        throw new ExceptionWithResp(
//                            $aReturn['errno'],
//                            $aReturn['errno'],
//                            $aReturn['errmsg'],
//                            [],
//                            ['miss_route' => $aCheckRouteRet['miss_route']]
//                        );
//                    }
//                }
//            }
//        }
//    }

    /**
     * 开城检查.
     */
    // 2024.9.10注释-待删
//    private function _checkOpenService() {
//        if ($this->_bB2BChannel) {
//            return true;
//        }
//
//        //6.0 不做开城校验
//        //if (Common::isLiteVersion($this->_aCommonInfo['app_version'], $this->_aCommonInfo['client_type'], $this->_aOrderInfo['channel'])) {
//        //    return true;
//        //}
//        $cityListIns = new Utils\CityList();
//        if (UtilProduct::isFastcar($this->_aOrderInfo['product_id'])) {
//            if (!$cityListIns->isOpenCity(Constants\OrderSystem::PRODUCT_ID_FAST_CAR, $this->_aAreaInfo['id'])) {
//                $aReturn = UtilsCommon::getErrMsg(ErrCode\RespCode::P_ERRNO_NOT_OPEN_SERVICE, $this->_aErrMsg, $this->_aOrderInfo['product_id']);
//                throw new ExceptionWithResp(
//                    ErrCode\Code::E_COMMON_AREA_NOT_OPEN_SERVICE,
//                    $aReturn['errno'],
//                    $aReturn['errmsg'],
//                    ['district' => $this->_aOrderInfo['district']]
//                );
//            }
//        }
//
//        if (UtilProduct::isSpecial($this->_aOrderInfo['product_id'])) {
//            if (!$cityListIns->isOpenCity(Constants\OrderSystem::PRODUCT_ID_DEFAULT, $this->_aAreaInfo['id'])) {
//                $aReturn = UtilsCommon::getErrMsg(ErrCode\RespCode::P_ERRNO_NOT_OPEN_SERVICE, $this->_aErrMsg, $this->_aOrderInfo['product_id']);
//                throw new ExceptionWithResp(
//                    ErrCode\Code::E_COMMON_AREA_NOT_OPEN_SERVICE,
//                    $aReturn['errno'],
//                    $aReturn['errmsg'],
//                    ['district' => $this->_aOrderInfo['district']]
//                );
//            }
//        }
//
//        if (UtilProduct::isFirstClass($this->_aOrderInfo['product_id'])) {
//            $aReturn = UtilsCommon::getErrMsg(ErrCode\RespCode::P_ERRNO_NOT_OPEN_SERVICE, $this->_aErrMsg, $this->_aOrderInfo['product_id']);
//            if (!$cityListIns->isOpenCity(Constants\OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR, $this->_aAreaInfo['id'])) {
//                throw new ExceptionWithResp(
//                    ErrCode\Code::E_COMMON_AREA_NOT_OPEN_SERVICE,
//                    $aReturn['errno'],
//                    $aReturn['errmsg'],
//                    ['district' => $this->_aOrderInfo['district']]
//                );
//            }
//
//            //豪华车+判断是否是阿波罗配的映射起点终点城市。如果不是则拦截
//            if ($this->_aAreaInfo['id'] !== $this->_aToAreaInfo['id']) {
//                if (!$cityListIns->isDestinationCityOpen($this->_aAreaInfo['id'], $this->_aToAreaInfo['id'])) {
//                    NuwaLog::warning(
//                        sprintf(
//                            'lux product destination city can not reach |'.'errno:%s|errmsg:%s|from_city:%s|to_city:%s',
//                            $aReturn['errno'],
//                            $aReturn['errmsg'],
//                            $this->_aAreaInfo['id'],
//                            $this->_aToAreaInfo['id']
//                        )
//                    );
//                    $aReturn['errmsg'] = '终点城市还未开通跨城车服务，我们会努力拓展，敬请期待';
//                    if (Language::ZH_CN != Language::getCurrentLanguage()) {
//                        $aReturn['errmsg'] = 'No inter-city cars available for this city. Coming soon';
//                    }
//
//                    if ((Constants\Common::MENU_FIRST_CLASS == $this->_aOrderInfo['menu_id'])) {
//                        throw new ExceptionWithResp(
//                            ErrCode\Code::E_COMMON_AREA_NOT_OPEN_SERVICE,
//                            $aReturn['errno'],
//                            $aReturn['errmsg'],
//                            ['district' => $this->_aOrderInfo['district']]
//                        );
//                    } elseif ((Constants\Common::MENU_PREMIUM == $this->_aOrderInfo['menu_id'])) {
//                        throw new LuxuryNeedFilterException();
//                    }
//                }
//            }
//        }
//
//        return true;
//    }

    /**
     * 一键叫活动.
     */
    // 2024.9.10注释-待删
//    private function _checkActivity() {
//        //批量优化，当批量调用时，先去判断是否已经取了一键叫活动的信息。
//        $sKey           = "oneKeyActivity_{$this->_aOrderInfo['activity_id']}_{$this->_aOrderInfo['area']}_{$this->_aOrderInfo['combo_type']}";
//        $oneKeyActivity = Registry::getInstance()->get($sKey);
//        if (empty($oneKeyActivity)) {
//            $this->_aOneKeyActivity = OneKeyCall::getInstance()->getOneKeyActivityOrderData(
//                $this->_aOrderInfo['activity_id'],
//                $this->_aOrderInfo['area'],
//                $this->_aOrderInfo['product_id'],
//                $this->_aOrderInfo['require_level']
//            );
//            Registry::getInstance()->set($sKey, $this->_aOneKeyActivity);
//        } else {
//            $this->_aOneKeyActivity = $oneKeyActivity;
//        }
//
//        if ($this->_aOneKeyActivity['activity_switch']) {
//            $this->_aOrderInfo['combo_type'] = $this->_aOneKeyActivity['combo_type'];
//            $this->_aOrderInfo['n_tuple']['x_activity_type'] = 1;
//            $this->_aOrderInfo['combo_id'] = $this->_aOneKeyActivity['combo_id'];
//        }
//    }

    /**
     * 场景检查：接送机、接送站、酒店接送（未来商圈、景区等）.
     *
     * @throws \Exception 异常
     * @return void
     */
    // 2024.9.10注释-待删
//    private function _checkFence() {
//        $aFenceInfo = $this->_getSceneFenceInfo();
//
//        if (Partner::is12306Partner($this->_aOrderInfo['partner_id'])) {
//            if (!empty($this->_aOrderInfo['railway_type'])) {
//                $aFenceInfo['order_n_tuple']['railway_type'] = $this->_aOrderInfo['railway_type'];
//            } else {
//                $this->_aOrderInfo['railway_type'] = $aFenceInfo['order_n_tuple']['railway_type'];
//            }
//        }
//
//        $this->_aOrderInfo['coupon_type'] = Coupon::getInstance()->getCouponType($aFenceInfo['order_n_tuple']);
//        $this->_aOrderInfo['oType']       = $aFenceInfo['oType'];
//        // 场景信息，
//        $this->_aOrderInfo['n_tuple']    = array_merge($this->_aOrderInfo['n_tuple'] ?? [], $aFenceInfo['order_n_tuple']);
//        $this->_aOrderInfo['fence_info'] = $aFenceInfo['fence'] ?? [];
//        if (!empty($aFenceInfo['starting_info']['composite_tag'])) {
//            $this->_aOrderInfo['starting_tag'] = $aFenceInfo['starting_info']['composite_tag'];
//        }
//
//        if (!empty($aFenceInfo['dest_info']['composite_tag'])) {
//            $this->_aOrderInfo['dest_tag'] = $aFenceInfo['dest_info']['composite_tag'];
//        }
//
//        //接机员收费开关，免费时不传id给账单
//        $oApollo = new Apollo();
//        foreach ($this->_aCustomServiceInfo as $index => $custom) {
//            if (self::CUSTOM_PICKUP_GUIDE == $custom['id']) {
//                if (!$oApollo->featureToggle(
//                    'gs_airport_guide_fee',
//                    array(
//                        'key'        => $this->_aPassengerInfo['pid'],
//                        'pid'        => $this->_aPassengerInfo['pid'],
//                        'city'       => $this->_aOrderInfo['area'],
//                        'product_id' => $this->_aOrderInfo['product_id'],
//                        'usage'      => 'toggle',
//                    )
//                )->allow()
//                ) {
//                    unset($this->_aCustomServiceInfo[$index]);
//                }
//
//                break;
//            }
//        }
//
//        $aSceneTypeComboType = $this->_processAirportSceneTypeAndComboType(
//            $this->_aOrderInfo['product_id'],
//            $this->_aOrderInfo['scene_type'],
//            $this->_aOrderInfo['combo_type'],
//            $aFenceInfo
//        );
//        $this->_aOrderInfo['combo_type'] = $aSceneTypeComboType['combo_type'] ?? HoraeConstants::TYPE_COMBO_DEFAULT;
//        $this->_aOrderInfo['scene_type'] = $aSceneTypeComboType['scene_type'] ?? HoraeConstants::HORAE_SCENE_TYPE_DEFAULT;
//    }

    /**
     * 检查途经点的场景围栏
     * @param array $aStopOverPoints aStopOverPoints
     * @param int   $iBusinessId     $iBusinessId
     * @return bool
     */
    // 2024.9.10注释-待删
//    private function _checkSceneFenceForStopOverPoints($aStopOverPoints, $iBusinessId) {
//        $aCoordinates = [];
//        if (!empty($aStopOverPoints) && is_array($aStopOverPoints)) {
//            foreach ($aStopOverPoints as $aStop) {
//                $aCoordinates[] = $aStop['lat'].'_'.$aStop['lng'];
//            }
//        }
//
//        $aParams = [];
//        if (!empty($aCoordinates)) {
//            $aParams = [
//                'from'            => Constants\ModuleName::PRE_SALE,
//                'coordinate_info' => implode(',' , $aCoordinates),
//                'scene_groups'    => implode(',', [HoraeConstants::HORAE_SCENE_GROUP_AIRPORT, HoraeConstants::HORAE_SCENE_GROUP_RAILWAY]),
//                'product_id'      => UtilProduct::getProductIdByBusinessId($iBusinessId),
//            ];
//        }
//
//        //默认未命中场站
//        $bCheckStatus = true;
//        if (!empty($aParams)) {
//            $aRet = (new HoraeClient())->atScenes($aParams);
//            if (0 == $aRet['errno'] && !empty($aRet['data'])) {
//                foreach ($aRet['data'] as $aFenceInfo) {
//                    //若命中机场或者火车站，则表示命中总场站
//                    if (!empty($aFenceInfo['fence_info'][HoraeConstants::HORAE_SCENE_GROUP_AIRPORT])
//                        || !empty($aFenceInfo['fence_info'][HoraeConstants::HORAE_SCENE_GROUP_RAILWAY])
//                    ) {
//                        $bCheckStatus = false;
//                        break;
//                    }
//                }
//            }
//        }
//
//        return $bCheckStatus;
//    }

    /**
     * 获取场景围栏信息
     *
     * @return array|mixed
     * @throws \Exception  异常
     */
    // 2024.9.10注释-待删
//    private function _getSceneFenceInfo() {
//        $aOrderInfo     = [
//            'starting_name' => $this->_aOrderInfo['starting_name'],
//            'dest_name'     => $this->_aOrderInfo['dest_name'],
//            'from_lat'      => $this->_aOrderInfo['from_lat'],
//            'from_lng'      => $this->_aOrderInfo['from_lng'],
//            'to_lat'        => $this->_aOrderInfo['to_lat'],
//            'to_lng'        => $this->_aOrderInfo['to_lng'],
//            'product_id'    => $this->_aOrderInfo['product_id'],
//        ];
//        $aPassengerInfo = [
//            'pid'   => $this->_aPassengerInfo['pid'],
//            'phone' => $this->_aPassengerInfo['phone'],
//        ];
//        return FenceInfo::getInstance()->getMultiFenceInfo($aOrderInfo, $aPassengerInfo);
//    }

    /**
     * 处理机场单scene_type和combo_type值
     * TODO：乘客端6.0逻辑已经迁移到dds，未处理快车默认&会议用车，未设置combo_type缓存
     * 快车默认：新版本不存在问题；会议用车：有需求再处理；新版从dds/products接口获取结果后设置combo_type缓存做兜底（报价单也有）
     *
     * @param int   $iProductId product_id
     * @param int   $iSceneType scene_type
     * @param int   $iComboType combo_type
     * @param array $aFenceInfo 围栏识别信息
     * @return array
     */
      // 2024.9.10注释-待删
//    private function _processAirportSceneTypeAndComboType(
//        $iProductId,
//        $iSceneType,
//        $iComboType,
//        $aFenceInfo
//    ) {
//        $oApollo = new Apollo();
//        $aRet    = [
//            'combo_type' => $iComboType,
//            'scene_type' => $iSceneType,
//        ];
//        // 快车接送机,走普通计价
//        if (OrderSystem::PRODUCT_ID_FAST_CAR == $iProductId) {
//            if (HoraeConstants::HORAE_SCENE_TYPE_AIRPORT_PICK_UP == $iSceneType
//                || HoraeConstants::HORAE_SCENE_TYPE_AIRPORT_DROP_OFF == $iSceneType
//            ) {
//                $aRet['combo_type'] = HoraeConstants::TYPE_COMBO_DEFAULT;
//            }
//
//            return $aRet;
//        }
//
//        // 只处理专车、企业专车、豪华车接送机单
//        if (OrderSystem::PRODUCT_ID_DEFAULT != $iProductId
//            && OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR != $iProductId
//            && OrderSystem::PRODUCT_ID_BUSINESS != $iProductId
//            && OrderSystem::PRODUCT_ID_BUSINESS_FIRST_CLASS_CAR != $iProductId
//        ) {
//            return $aRet;
//        }
//
//        //会议用车接送机不用combo_type计价，跳过逻辑
//        if (Utils\Commercial::isMeetingCar($this->_aOrderInfo['commercial_type'])) {
//            return $aRet;
//        }
//
//        $iTempComboType = HoraeConstants::TYPE_COMBO_DEFAULT;
//        $iTempSceneType = HoraeConstants::HORAE_SCENE_TYPE_DEFAULT;
//        // 如果没有scene_type和combo_type参数值,使用围栏检查
//        if (HoraeConstants::HORAE_SCENE_TYPE_DEFAULT == $iSceneType
//            && HoraeConstants::TYPE_COMBO_DEFAULT == $iComboType
//        ) {
//            // 接机
//            if (!empty($aFenceInfo['starting_info']['composite_tag'])) {
//                if (UtilHelper::checkOrderTag($aFenceInfo['starting_info']['composite_tag'], ['tag_airport'])) {
//                    $iTempComboType = HoraeConstants::TYPE_COMBO_FROM_AIRPORT;
//                    $iTempSceneType = HoraeConstants::HORAE_SCENE_TYPE_AIRPORT_PICK_UP;
//                }
//            }
//
//            // 送机
//            if (!empty($aFenceInfo['dest_info']['composite_tag'])) {
//                if (UtilHelper::checkOrderTag($aFenceInfo['dest_info']['composite_tag'], ['tag_airport'])) {
//                    $iTempComboType = HoraeConstants::TYPE_COMBO_TO_AIRPORT;
//                    $iTempSceneType = HoraeConstants::HORAE_SCENE_TYPE_AIRPORT_DROP_OFF;
//                }
//            }
//
//            // 同时命中接机和送机，那么 combo_type 的值为接机（灰度开关全量后可下掉）
//            $aToggleInfo = [
//                'key'        => $this->_aPassengerInfo['pid'],
//                'phone'      => $this->_aPassengerInfo['phone'],
//                'city'       => $this->_aOrderInfo['area'],
//                'product_id' => $iProductId,
//                'airport_id' => $aFenceInfo['airport_id'] ?? 0,
//            ];
//            if ($oApollo->featureToggle('gs_airport_combo_type_strategy_adjustment_toggle', $aToggleInfo)->allow()) {
//                if (!empty($aFenceInfo['starting_info']['composite_tag']) && !empty($aFenceInfo['dest_info']['composite_tag'])) {
//                    if (UtilHelper::checkOrderTag($aFenceInfo['starting_info']['composite_tag'], ['tag_airport']) && UtilHelper::checkOrderTag($aFenceInfo['dest_info']['composite_tag'], ['tag_airport'])) {
//                        $iTempComboType = HoraeConstants::TYPE_COMBO_FROM_AIRPORT;
//                    }
//                }
//            }
//
//            if (!empty($iTempComboType)) {
//                $aRet['combo_type'] = $iTempComboType;
//                $aRet['scene_type'] = $iTempSceneType;
//                // 非anycar场景更新缓存
//                if (Constants\OrderSystem::PRODUCT_ID_ANY_CAR != $this->_aOrderInfo['product_id']) {
//                    $this->_setOneConfComboType($this->_aOrderInfo, $iTempComboType);
//                    //如果命中机场围廊,并且是专车订单,设置订单airport缓存
//                    $sCurrentTraceId = Trace::traceId();
//                    $aAirportInfo    = ['combo_type' => $iTempComboType];
//                    $oOrderAirport   = new OrderAirPort();
//                    $oOrderAirport->setAirPortInfoByTraceId($sCurrentTraceId, $aAirportInfo);
//                }
//            }
//        }
//
//        // 接送机计价分离apollo
//        $aControl = [
//            'key'        => rand(1, 500000),
//            'phone'      => $this->_aPassengerInfo['phone'],
//            'city'       => $this->_aOrderInfo['area'],
//            'product_id' => $iProductId,
//            'airport_id' => $aFenceInfo['airport_id'] ?? 0,
//        ];
//        // 未开启接送机计价分离时处理
//        if (!$oApollo->featureToggle('gs_airportOrder_strategies_toggle', $aControl)->allow()) {
//            if ((HoraeConstants::HORAE_SCENE_TYPE_DEFAULT != $iSceneType || HoraeConstants::TYPE_COMBO_DEFAULT != $iTempComboType)
//                && (HoraeConstants::TYPE_COMBO_FROM_AIRPORT == $aRet['combo_type'] || HoraeConstants::TYPE_COMBO_TO_AIRPORT == $aRet['combo_type'])
//            ) {
//                $aRet['combo_type'] = HoraeConstants::TYPE_COMBO_DEFAULT;
//                $aRet['scene_type'] = $iSceneType;
//                // 非anycar订单处理oneconf、更新日志
//                if (OrderSystem::PRODUCT_ID_ANY_CAR != $this->_aOrderInfo['product_id']) {
//                    $this->_setOneConfComboType($this->_aOrderInfo, HoraeConstants::TYPE_COMBO_DEFAULT);
//                    NuwaLog::updateLogHeadData(['airportOrder_strategies' => 'estimate']);
//                }
//            }
//        }
//
//        return $aRet;
//    }

    /**
     * 校验拼车是否开通买卡路线
     *
     * @param $iPassengerId
     */
    // 2024.9.10
//    private function _rebuildRouteCardInfo() {
//        if (empty($this->_aOrderInfo['commute_route_id'])) {
//            return;
//        }
//
//        $oPassengerUFS = new \BizCommon\Models\Passenger\PassengerUFS();
//        $aFeatureRet   = $oPassengerUFS->getUfsFeature(['commute_route_info.route_info'], ['route_id' => $this->_aOrderInfo['commute_route_id']], 'route');
//        $this->_aCommuteRouteCardInfo = json_decode($aFeatureRet['commute_route_info.route_info'], true) ?? [];
//    }

    // 注释-待删
//    //因为场景在围栏判断是接送机情况下，会更新combo_type的值，导致最后根据
//    //oneconf更新icon等数据时，三元组无法匹配的问题，所以在更改预估combo_type时同时更改oneconf
//    private function _setOneConfComboType($aOrderInfo, $iNewComboType) {
//        if (!is_array($this->_aOneConfList)) {
//            return;
//        }
//
//        foreach ($this->_aOneConfList as &$oneConf) {
//            if ($oneConf['business_id'] == $aOrderInfo['business_id']
//                && $oneConf['require_level'] == $aOrderInfo['require_level']
//                && $oneConf['combo_type'] == $aOrderInfo['combo_type']
//            ) {
//                $oneConf['combo_type'] = $iNewComboType;
//            }
//        }
//    }

    /**
     * @desc 判断是否给用户免费送通勤卡
     * @param $aCommuteFeatures
     * @return bool
     */
    // 2024.9.10注释-待删
//    private function _checkCommuteCardSend($aCommuteFeatures, $aCommuteResultH5) {
//        if (empty($aCommuteFeatures)) {
//            return false;
//        }
//
//        //版本控制
//        if (!(version_compare($this->_aCommonInfo['app_version'], '5.2.56') >= 0)) {
//            return false;
//        }
//
//        //ab-test
//        $aApolloV2 = new Apollo();
//        $toggle    = $aApolloV2->featureToggle(
//            'commute_card_present_card_ab',
//            array(
//                'key'   => $this->_aPassengerInfo['pid'],
//                'city'  => $this->_aOrderInfo['area'],
//                'pid'   => $this->_aPassengerInfo['pid'],
//                'phone' => $this->_aPassengerInfo['phone'],
//            )
//        );
//
//        if (!$toggle->allow() || 'treatment_group' != $toggle->getGroupName()) {
//            return false;
//        }
//
//        if (!CarpoolCommuteCard::checkCarpoolUserType($aCommuteResultH5['carpool_user'], Commute::EXPRESS_COMMUTE_USER_INDEX)) {
//            return false;
//        }
//
//        $aCommuteFeature    = $aCommuteFeatures['data']['featureMap'];
//        $aCommuteCardConfig = NuwaConfig::text('config_text', 'carpool_commute_send_card');
//
//        if (time() - $aCommuteFeature['ufs.passenger.commute_card.last_buy_time']['val'] <= $aCommuteCardConfig['last_buy_card_interval']
//            || $aCommuteFeature['ufs.passenger.commute_card.present_times']['val'] >= $aCommuteCardConfig['maximum_send_times']
//            || $aCommuteFeature['ufs.passenger.commute_card.continuous_buy_times']['val'] >= $aCommuteCardConfig['continuous_send_times']
//        ) {
//            return false;
//        }
//
//        return true;
//    }
    /**
     * @desc 判断是否给用户展示通勤H5（控制展示频次）3天一次，若redis未过期，不展示
     *
     * @param $aCommuteCardInfo
     *
     * @return bool
     */
    // 2024.9.10注释-待删
//    private function _checkCommuteCardShow($aCommuteCardInfo, $aCommuteResultH5) {
//        if (empty($aCommuteCardInfo['route_id'])) {
//            return false;
//        }
//
//        if (!CarpoolCommuteCard::checkCarpoolUserType(
//            $aCommuteResultH5['carpool_user'],
//            Commute::CARPOOL_COMMUTE_USER_INDEX
//        )
//            && !CarpoolCommuteCard::checkCarpoolUserType(
//                $aCommuteResultH5['carpool_user'],
//                Commute::NON_CARPOOL_COMMUTE_USER_INDEX
//            )
//            && 0 != $aCommuteResultH5['carpool_user']
//        ) {
//            return false;
//        }
//
//        $iDecision = EstimatePrice::getInstance()->getCommuteCardCache($this->_aPassengerInfo['pid'], $aCommuteCardInfo['route_id']);
//        if ($iDecision) {
//            return false;
//        }
//
//        $aCommuteCardConfig = NuwaConfig::config('config_carpool', 'carpool_commute_H5');
//
//        return (new CarpoolCommuteCard())->checkRejectRecord($this->_aPassengerInfo['pid'], $aCommuteCardInfo['route_id'], $aCommuteCardConfig['rejection_record_num'], $aCommuteCardConfig['rejection_H5_time'], P_CARPOOL_COMMUTE_H5_REJECT_RECORD);
//    }

    //临时方案,快车接送机需求
    // 2024.9.10注释-待删
//    private function _getSceneType($iSceneType) {
//        if (\BizLib\Constants\OrderSystem::PRODUCT_ID_FAST_CAR == $this->_aOrderInfo['product_id']) {
//            if (HoraeConstants::HORAE_SCENE_TYPE_AIRPORT_PICK_UP == $iSceneType
//                || HoraeConstants::HORAE_SCENE_TYPE_AIRPORT_DROP_OFF == $iSceneType
//            ) {
//                return HoraeConstants::HORAE_SCENE_TYPE_DEFAULT;
//            }
//        }
//
//        return $iSceneType;
//    }

    //临时方案,只允许的专车才能转接送机comobo_type
    // 2024.9.10注释-待删
//    private function _getComboType($iSceneType) {
//        $iTempComboType = \BizLib\Utils\Horae::getComboType($iSceneType);
//        if (\BizLib\Constants\OrderSystem::PRODUCT_ID_DEFAULT == $this->_aOrderInfo['product_id']
//            || \BizLib\Constants\OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR == $this->_aOrderInfo['product_id']
//            || \BizLib\Constants\OrderSystem::PRODUCT_ID_BUSINESS == $this->_aOrderInfo['product_id']
//            || \BizLib\Constants\OrderSystem::PRODUCT_ID_BUSINESS_FIRST_CLASS_CAR == $this->_aOrderInfo['product_id']
//        ) {
//            return $iTempComboType;
//        }
//
//        if (HoraeConstants::HORAE_SCENE_TYPE_AIRPORT_DROP_OFF == $iSceneType
//            || HoraeConstants::HORAE_SCENE_TYPE_AIRPORT_PICK_UP == $iSceneType
//        ) {
//            $iTempComboType = HoraeConstants::TYPE_COMBO_DEFAULT;
//        }
//
//        return $iTempComboType;
//    }

    /**
     * 对比接收的参数中的签名和实际签名是否一致.
     *
     * @param $aParams
     *
     * @return bool
     */
    public function checkSign(array $aParams) {
        if (empty($aParams['check_sum']) || !is_array($aParams)) {
            return false;
        }

        $sOriginSign = $aParams['check_sum'];
        $sRealSign   = $this->createSign($aParams);

        return $sOriginSign == $sRealSign;
    }

    /**
     * 创建签名,先对数组进行升序,json_encode之后直接md5.
     *
     * @param $aParams
     *
     * @return string
     */
    public function createSign(array $aParams) {
        if (empty($aParams) || !is_array($aParams)) {
            return '';
        }

        //过滤不验证参数
        foreach ($this->_aFilterSignParams as $sKey => $iValue) {
            if (!is_array($this->_aFilterSignParams[$sKey]) && array_key_exists($sKey, $aParams)) {
                unset($aParams[$sKey]);
                continue;
            }

            if (is_array($this->_aFilterSignParams[$sKey])) {
                foreach ($this->_aFilterSignParams[$sKey] as $sSubKey => $iSubValue) {
                    if (is_array($aParams[$sKey]) && array_key_exists($sSubKey, $aParams[$sKey])) {
                        unset($aParams[$sKey][$sSubKey]);
                    }
                }
            }
        }

        unset($aParams['extra_info']);
        unset($aParams['check_sum']);
        foreach ($aParams as $key => &$value) {
            if (is_array($value)) {
                $value = json_encode($value, JSON_UNESCAPED_UNICODE);
            }
        }

        ksort($aParams);

        return md5(json_encode($aParams, JSON_UNESCAPED_UNICODE));
    }


    /**
     * 获取端上传的one_conf配置.
     *
     * @return one_conf obj
     */
    public function getOneConf() {
        return $this->_aOneConfList;
    }

    public function setOneConf($aOneConfList) {
        $this->_aOneConfList = $aOneConfList;
    }

    public function getSpecialSceneParam() {
        return $this->_iSpecialSceneParam;
    }

    /**
     * 修改端上传的one_conf配置.
     */
    // 2024.9.10注释-待删
//    public function updateOneConf($iBusinessId, $iComboType, $iRequireLevel) {
//        $aOneConf = $this->_aOneConfList;
//        foreach ($aOneConf as $idx => $item) {
//            if ($iBusinessId == $item['business_id']
//                && $iComboType == $item['combo_type']
//                && $iRequireLevel == $item['require_level']
//            ) {
//                $this->_aOneConfList[$idx]['is_default'] = 1;
//            } else {
//                $this->_aOneConfList[$idx]['is_default'] = 0;
//            }
//        }
//    }

    /*
     * @param $aPaymentsInfo
     * @return array
     */
    private function _getDefaultPayType($aPaymentsInfo) {
        $aPayment            = [];
        $aPayment['carpool'] = 0;
        $aPayment['nocarpool'] = 0;
        if (empty($aPaymentsInfo)) {
            return $aPayment;
        }

        // 老支付方式
        if (isset($aPaymentsInfo['user_pay_info']['default_tag']) && !empty($aPaymentsInfo['user_pay_info']['default_tag'])) {
            $aPayment['nocarpool'] = (int)($aPaymentsInfo['user_pay_info']['default_tag']);

            return $aPayment;
        }

        //新支付方式
        if (isset($aPaymentsInfo['payments_separate']) && !empty($aPaymentsInfo['payments_separate'])) {
            if (isset($aPaymentsInfo['user_pay_info']['carpool']['busi_payments']) && is_array(
                $aPaymentsInfo['user_pay_info']['carpool']['busi_payments']
            )
            ) {
                $aPayment['carpool'] = (int)(
                    $this->_getSelectedPayType($aPaymentsInfo['user_pay_info']['carpool']['busi_payments'])
                );
            }

            if (isset($aPaymentsInfo['user_pay_info']['noncarpool']['busi_payments']) && is_array(
                $aPaymentsInfo['user_pay_info']['noncarpool']['busi_payments']
            )
            ) {
                $aPayment['nocarpool'] = (int)(
                    $this->_getSelectedPayType($aPaymentsInfo['user_pay_info']['noncarpool']['busi_payments'])
                );
            }

            return $aPayment;
        }

        //巴西
        if (isset($aPaymentsInfo['user_pay_info']['busi_payments']) && !empty($aPaymentsInfo['user_pay_info']['busi_payments'])) {
            $aPayment['nocarpool'] = (int)(
                $this->_getSelectedPayType($aPaymentsInfo['user_pay_info']['busi_payments'])
            );

            return $aPayment;
        }

        return $aPayment;
    }

    /**
     * @param $aOrderType
     * @param $aParam
     *
     * @return mixed
     */
    private function _setRealOrderType($aOrderType, $aParam) {
        $sCarLevel = $aParam['order_info']['require_level'];
        if (HoraeConstants::TYPE_COMBO_CARPOOL == $aParam['order_info']['combo_type']) {
            $aOrderType[$sCarLevel]['carpool'] = $aParam['order_info']['order_type'];
        } else {
            $aOrderType[$sCarLevel]['noncarpool'] = $aParam['order_info']['order_type'];
        }

        return $aOrderType;
    }

    /**
     * @desc: hold_type
     * @param $aHoldType
     * @param $aParam
     * @return mixed
     */
    private function _setHoldType($aHoldType, $aParam) {
        $sCarLevel  = $aParam['order_info']['require_level'];
        $aExtraInfo = json_decode($aParam['extra_info']['order'], true);
        if (!isset($aExtraInfo['hold_type'])) {
            return $aHoldType;
        }

        $aHoldType[$sCarLevel] = $aExtraInfo['hold_type'];
        return $aHoldType;
    }

    /**
     * @desc:  seat_num
     * @param  array $aSeatNum 座位数
     * @param  array $aParam   参数
     * @return array    座位数信息
     */
    private function _setSeatNum($aSeatNum, $aParam) {
        $sCarLevel = $aParam['order_info']['require_level'];

        $aSeatNum[$sCarLevel] = $aParam['order_info']['carpool_seat_num'];
        return $aSeatNum;
    }

    private function _setOrderNTuple($aOrderNtuple, $aParam) {
        if (HoraeConstants::TYPE_COMBO_CARPOOL == $aParam['order_info']['combo_type']) {
            $sScenTag = 'carpool';
        } elseif (\BizCommon\Utils\Order::isSpecialRateV2($aParam['order_info']) || $aParam['extra_info']['is_special_price']) {
            $sScenTag = 'special_rate';
        } else {
            $sScenTag = 'noncarpool';
        }

        $sCarLevel  = $aParam['order_info']['require_level'];
        $aExtraInfo = json_decode($aParam['extra_info']['order'], true);
        $aNTuple    = $aExtraInfo['n_tuple'] ?? [];
        $aOrderNtuple[$sCarLevel][$sScenTag] = $aNTuple;

        return $aOrderNtuple;
    }

    private function _setCustomServiceInfo($aCustomServiceInfo, $aParam) {
        if (HoraeConstants::TYPE_COMBO_CARPOOL == $aParam['order_info']['combo_type']) {
            $sScenTag = 'carpool';
        } elseif (\BizCommon\Utils\Order::isSpecialRateV2($aParam['order_info'])) {
            $sScenTag = 'special_rate';
        } else {
            $sScenTag = 'noncarpool';
        }

        $sCarLevel       = $aParam['order_info']['require_level'];
        $aExtraInfoOrder = json_decode($aParam['extra_info']['order'], true);
        $aPersonalizedCutomizedOption = $aExtraInfoOrder['personalized_custom_option'];
        $aCustomServiceSsse           = [];
        if (!empty($aPersonalizedCutomizedOption)) {
            foreach ($aPersonalizedCutomizedOption as $key => $item) {
                $aCustomServiceSsse[] = ['id' => (int)($key), 'count' => $item['count']];
            }
        }

        $aCustomService = array_merge(json_decode($aParam['custom_service_info'], true), $aCustomServiceSsse);
        $aCustomServiceInfo[$sCarLevel][$sScenTag] = json_encode($aCustomService);

        return $aCustomServiceInfo;
    }

    private function _getCommuteInfo($aParam) {
        if (HoraeConstants::TYPE_COMBO_CARPOOL != $aParam['order_info']['combo_type']) {
            return [];
        }

        if (empty($aParam['extra_info']['order'])) {
            return [];
        }

        $aExtraInfo = json_decode($aParam['extra_info']['order'], true);
        if (empty($aExtraInfo['commute_card_info'])) {
            return [];
        }

        $aCarpoolCommuteInfo = [
            'commute_card_info'           => $aExtraInfo['commute_card_info'],
            'is_show_commute_card_h5'     => $aExtraInfo['is_show_commute_card_h5'],
            'is_show_commute_card_bubble' => $aExtraInfo['is_show_commute_card_bubble'],
            'is_send_commute_card'        => $aExtraInfo['is_send_commute_card'],
        ];

        return $aCarpoolCommuteInfo;
    }

    /**
     * @param $aPayments
     *
     * @return int
     */
    private function _getSelectedPayType($aPayments) {
        foreach ($aPayments as $aPayment) {
            if (!empty($aPayment['isSelected'])) {
                return $aPayment['tag'];
            }
        }

        return 0;
    }

    /**
     * 获取会员信息.
     *
     * @param $aOrderInfo
     * @param $aPassengerInfo
     * @param $aCommonInfo
     *
     * @return array|mixed
     */
    // 2024.9.10注释-待删
//    private function _getMemberInfo($aOrderInfo, $aPassengerInfo, $aCommonInfo) {
//        //获取会员信息
//        //豪华车溢价开关, 包括豪华车和专车menu中的豪华车
//        if (Constants\Common::MENU_FIRST_CLASS == $aOrderInfo['menu_id'] || \BizLib\Constants\OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR == $aOrderInfo['product_id']) {
//            if (!(ApolloV2::getInstance()->featureToggle(
//                'lux_dynamic_member_protect_fee_toggle',
//                [
//                    'key'         => $aPassengerInfo['phone'],
//                    'phone'       => $aPassengerInfo['phone'],
//                    'area'        => $aOrderInfo['area'],
//                    'app_version' => $aCommonInfo['app_version'],
//                ]
//            )->allow())
//            ) {
//                return [];
//            }
//        }
//
//        //批量的接口优化。
//        if (UtilProduct::isAnyCar($aOrderInfo['product_id'])) {
//            //排除豪华车anycar
//            if (Constants\Common::MENU_FIRST_CLASS == $aOrderInfo['menu_id']) {
//                //如果是豪华车anycar则更新product_id为豪华车id
//                $aOrderInfo['product_id'] = \BizLib\Constants\OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR;
//            } else {
//                return [];
//            }
//        }
//
//        if (\BizLib\Utils\Language::ZH_CN != $aCommonInfo['lang']) {
//            return [];
//        }
//
//        $aHitInfo = [
//            'city_id' => $aOrderInfo['area'],
//        ];
//
//       // $apolloV2 = new \Xiaoju\Apollo\Apollo();
//       // if ($apolloV2->featureToggle(
//           // 'gs_passenger_member_coupon_privileges',
//           // [
//               // 'key'   => $aPassengerInfo['phone'],
//               // 'phone' => $aPassengerInfo['phone'],
//               // 'area'  => $aOrderInfo['area'],
//           // ]
//       // )->allow()
//       // ) {
//           // $aHitInfo['version'] = 2;
//       // }
//        $aHitInfo['version'] = 2;
//        $sKey          = "memberInfo_{$aOrderInfo['product_id']}_{$aPassengerInfo['pid']}_{$aCommonInfo['lang']}";
//        $memberProfile = Registry::getInstance()->get($sKey);
//        if (null === $memberProfile) {
//            $memberProfile = (new MemberSystemClient())->queryInfo(
//                $aOrderInfo['product_id'],
//                $aPassengerInfo['pid'],
//                $aCommonInfo['lang'],
//                false,
//                $aHitInfo
//            );
//            Registry::getInstance()->set($sKey, $memberProfile);
//        }
//
//        //如果存在溢价券逻辑的兜底逻辑，溢价权益中不存在reason则赋值
//        if (MemberVersion::isNewMember() && !empty($memberProfile['privileges']) && !empty($memberProfile['privileges']['dpa'])) {
//            if (1 == $memberProfile['privileges']['dpa']['is_coupon'] && empty($memberProfile['privileges']['dpa']['frontend']['reason'])) {
//                $aEstimateText = NuwaConfig::text('config_text', 'mOrderEstimateNew');
//                $memberProfile['privileges']['dpa']['frontend']['reason'] = $aEstimateText['dpa_coupon_used_text'];
//            }
//        }
//
//        return $memberProfile;
//    }

    private function _convertOneConf() {
        $aOneConf = $this->_aOneConfList;
        if (!is_array($aOneConf)) {
            return '';
        }

        foreach ($aOneConf as &$item) {
            $item['is_default'] = (int) $item['is_default'];
        }

        return json_encode($aOneConf);
    }

    /**
     * @param $aPassengerInfo
     * @param $aOrderInfo
     *
     * @return int|string
     */
    // 2024.9.10注释-待删
//    public function getDefaultCarLevel($aPassengerInfo, $aOrderInfo) {
//
//       // $apolloV2 = new \Xiaoju\Apollo\Apollo();
//       // if ($apolloV2->featureToggle(
//           // 'gs_premium_default_carlevel_toggle',
//           // [
//               // 'key'   => rand(1, 500000),
//               // 'city'  => (int)($aOrderInfo['area']),
//               // 'phone' => $aPassengerInfo['phone'],
//           // ]
//       // )->allow()
//       // ) {
//           // return \BizLib\Utils\CarLevel::DIDI_SHUSHI_CAR_LEVEL;
//       // }
//        $iPid       = (int)($aPassengerInfo['pid']);
//        $iCityId    = (int)($aOrderInfo['area']);
//        $iProductId = (int)($aOrderInfo['product_id']);
//        $aPassengerFeature = $this->_oPassengerPFS->getPFS(
//            $iPid,
//            [Constants\Pfs::SCENE_STATS => [self::PREMIUM_CALL_TAG]]
//        );
//        $aMigrationConf    = $this->getMigrationConfig($iCityId);
//        if (empty($aPassengerFeature)
//            || empty($aPassengerFeature[Constants\Pfs::SCENE_STATS])
//            || empty($aPassengerFeature[Constants\Pfs::SCENE_STATS][self::PREMIUM_CALL_TAG])
//        ) {
//            return $this->getMisCarLevel($aMigrationConf);
//        }
//
//        return $this->getHistoryCarLevel($iPid, $iProductId, $aMigrationConf);
//    }

    /**
     * @param $iPid
     * @param $iProductId
     * @param $aMigrationCfg
     *
     * @return string
     */
    // 2024.9.10注释-待删
//    public function getHistoryCarLevel($iPid, $iProductId, $aMigrationCfg) {
//        $sDefaultCarLevel = $this->getMisCarLevel($aMigrationCfg);
//        if (empty($iPid) || empty($iProductId) || empty($aMigrationCfg) || empty($aMigrationCfg['support'])) {
//            return $sDefaultCarLevel;
//        }
//
//        $aHistory = Passenger::getInstance()->getPreference($iPid, $iProductId);
//        if (empty($aHistory) || !is_array($aHistory)) {
//            return $sDefaultCarLevel;
//        }
//
//        if (!empty($aHistory['require_level']) && in_array($aHistory['require_level'], $aMigrationCfg['support'])) {
//            $sDefaultCarLevel = (string)($aHistory['require_level']);
//        }
//
//        return $sDefaultCarLevel;
//    }

    /**
     * @param $iCityId
     *
     * @return array
     */
    // 2024.9.10注释-待删
//    public function getMigrationConfig($iCityId) {
//        if (empty($iCityId)) {
//            return [];
//        }
//
//        //
//        //  // --@var Diconf\Diconf
//        $oDiconf = Diconf\Diconf::getInstance(Diconf\APOLLO_MODE, Diconf\JSON_FILE_TYPE);
//        // 根据 $namespace, $file, $key 获取对应 $value
//        list($bOk, $aMigrationCfg) = $oDiconf->getConfig(
//            'api_passenger_config',
//            'estimate_premier_navigation_config',
//            'data'
//        );
//        // 判断是否成功
//        if (!$bOk || empty($aMigrationCfg)) {
//            NuwaLog::warning(
//                ErrCode\Msg::formatArray(
//                    ErrCode\Code::E_COMMON_CONFIG_NOT_FOUNT,
//                    ['result' => $aMigrationCfg, 'module' => MODULE_NAME, ]
//                )
//            );
//
//            return [];
//        }
//
//        if (empty($aMigrationCfg[(string)($iCityId)])) {
//            return [];
//        }
//
//        return $aMigrationCfg[(string)($iCityId)];
//    }

    /**
     * @param $aMigrationCfg
     *
     * @return string
     */
    // 2024.9.10注释-待删
//    public function getMisCarLevel($aMigrationCfg) {
//        $sDefaultCarLevel = CarLevel::DIDI_SHUSHI_CAR_LEVEL;
//        if (empty($aMigrationCfg) || empty($aMigrationCfg['default'])) {
//            return $sDefaultCarLevel;
//        }
//
//        $sDefaultCarLevel = (string)($aMigrationCfg['default']);
//
//        return $sDefaultCarLevel;
//    }

      // 2024.9.10注释-待删
//    private function _checkTipInfo() {
//        if ($this->_aOrderInfo['tip'] < 0 || $this->_aOrderInfo['tip'] > 200) {
//            throw new InvalidArgumentException(
//                ErrCode\Code::E_COMMON_PARAM_INVALID_VALUE,
//                ['tip' => $this->_aOrderInfo['tip']]
//            );
//        }
//    }


      // 2024.9.10注释-待删
//    private function _getExtensionsParams($aParams, $aOrderInfo) {
//        if (!isset($aParams['extensions'])) {
//            return;
//        }
//
//        $aExtensions = json_decode($aParams['extensions'], true);
//        if (empty($aExtensions) || !is_array($aExtensions)) {
//            return;
//        }
//
//        // product_category 需要完全匹配，除开business_id, require_level, combo_type基础三元组为必须项外，其他key不能传入无意义的默认值
//        $aProductCategories = \BizLib\Config::config('config_product_category_map', 'product_category');
//        $aNTuple            = array(
//            'business_id'   => (int)($aOrderInfo['business_id']),
//            'require_level' => (int)($aOrderInfo['require_level']),
//            'combo_type'    => (int)($aOrderInfo['combo_type']),
//        );
//        if (isset($aOrderInfo['carpool_type']) && $aOrderInfo['carpool_type'] > 0) {
//            $aNTuple['carpool_type'] = (int)($aOrderInfo['carpool_type']);
//        }
//
//        if (isset($aOrderInfo['route_type']) && $aOrderInfo['route_type'] > 0) {
//            $aNTuple['route_type'] = (int)($aOrderInfo['route_type']);
//        }
//
//        foreach ($aExtensions as $item) {
//            if (key_exists('product_category', $item)) {
//                $aMap = $aProductCategories[(int)($item['product_category'])];
//                if ($aMap == $aNTuple) {
//                    if (isset($item['carpool_seat_num'])) {
//                        $this->_aOrderInfo['carpool_seat_num'] = $item['carpool_seat_num'];
//                    }
//
//                    if (isset($item['like_wait'])) {
//                        $this->_aOrderInfo['hold_type'] = $item['like_wait'];
//                    }
//
//                    if (isset($item['hold_time'])) {
//                        $this->_aOrderInfo['hold_time'] = $item['hold_time'];
//                    }
//
//                    if (isset($item['hold_unlimited'])) {
//                        $this->_aOrderInfo['hold_time'] = 60 * 60; // 无限愿等，约定1h
//                    }
//
//                    break;
//                }
//            }
//        }
//    }

    /**
     * _getProductCategory
     * @param array $aNTuple xx
     * @return int|string
     */
    private function _getProductCategory($aNTuple) {
        if (!isset($aNTuple) || empty($aNTuple)) {
            $aMap = [];
        } else {
            $aMap = array(
                'business_id'   => (int)($aNTuple['business_id']),
                'require_level' => (int)($aNTuple['require_level']),
                'combo_type'    => (int)($aNTuple['combo_type']),
            );

            if (isset($aNTuple['carpool_type']) && $aNTuple['carpool_type'] > 0) {
                $aMap['carpool_type'] = (int)($aNTuple['carpool_type']);
            }

            if (isset($aNTuple['route_type']) && $aNTuple['route_type'] > 0) {
                $aMap['route_type'] = (int)($aNTuple['route_type']);
            }

            if (isset($aNTuple['is_special_price']) && !empty($aNTuple['is_special_price'])) {
                $aMap['is_special_price'] = $aNTuple['is_special_price'] ? 1 : 0;
            }

            if (isset($aNTuple['level_type']) && !empty($aNTuple['level_type'])) {
                $aMap['level_type'] = (int)$aNTuple['level_type'];
            }
        }

        $oProductCategory = new ProductCategory();

        return $oProductCategory->getProductCategory($aMap);
    }

    /**
     * 路线处理.暂存路线信息.
     */
    // 2024.9.10注释-待删
//    private function _storeRouteInfo() {
//        if (Horae::isLowPriceCarpool($this->_aOrderInfo)) {
//            $this->_aCarpoolRouteInfo[$this->_aOrderInfo['require_level']] = $this->_aOrderInfo['match_routes'];//老写成这样，改不动
//            // 缓存路线数据
//            unset($this->_aOrderInfo['is_carpool_booking']);
//        } elseif ((isset($this->_aOrderInfo['is_carpool_booking']) && $this->_aOrderInfo['is_carpool_booking'])||$this->_aOrderInfo['carpool_long_order']) {
//            $this->_aCarpoolRouteInfo[$this->_aOrderInfo['require_level']] = $this->_aOrderInfo['match_routes'];//老写成这样，改不动
//            // 拼车预约特殊处理，缓存路线数据
//            unset($this->_aOrderInfo['match_routes']);
//            unset($this->_aOrderInfo['is_carpool_booking']);
//        }
//
//        // 城际拼车，保存路线数据
//        if (HoraeUtils::isInterCityCarPoolScene($this->_aOrderInfo['combo_type'])) {
//            $this->_aInterRouteInfo = $this->_aOrderInfo['match_routes'][0];
//        }
//    }

    /**
     * 路线处理.获取路线信息.
     */
    private function _loadRouteInfo($aBillInfo, $iRequireLevel, $sAppVersion) {
        if (!empty($aBillInfo['product_infos'][$iRequireLevel]['carpool']['combo_type'])
            && HoraeConstants::TYPE_COMBO_CARPOOL_INTER_CITY == $aBillInfo['product_infos'][$iRequireLevel]['carpool']['combo_type']
            && !empty($this->_aInterRouteInfo)
        ) {
            if (version_compare($sAppVersion, '5.2.46') >= 0) {
                $aBillInfo['match_routes'] = $this->_aInterRouteInfo;
            } else {
                $aBillInfo['match_routes'] = $aBillInfo['route_info'];
            }

            unset($aBillInfo['route_info']['time_span']);
        }

        if (!empty($aBillInfo['product_infos'][$iRequireLevel]['carpool']['combo_type'])
            && HoraeConstants::TYPE_COMBO_CARPOOL == $aBillInfo['product_infos'][$iRequireLevel]['carpool']['combo_type']
            && !empty($this->_aCarpoolRouteInfo)
        ) {
            $aBillInfo['match_routes'] = $this->_aCarpoolRouteInfo[$iRequireLevel];
        }

        $aBillProductInfo = $aBillInfo['product_infos'][$iRequireLevel];
        $aSpecialOrder    = ['product_id' => $aBillProductInfo['noncarpool']['product_line_id'] ?? 0, 'require_level' => $iRequireLevel ?? 0, 'combo_type' => $aBillProductInfo['special_rate']['combo_type'] ?? 0];
        if (\BizCommon\Utils\Order::isSpecialRateV2($aSpecialOrder)) {
            $aBillInfo['match_routes'] = $this->_aFlatRateRouteInfo;
        }

        //anycar下增加区域渗透路线匹配
        if (CarLevel::DIDI_ANY_CAR_CAR_LEVEL == $iRequireLevel && !empty($aBillInfo['multi_info'])) {
            foreach ($aBillInfo['multi_info'] as $aProductInfo) {
                $iCarpoolType = $aProductInfo['carpool_type'] ?? 0;
                if (\BizCommon\Constants\OrderNTuple::CARPOOL_TYPE_FLAT_RATE == $iCarpoolType) {
                    $aRouteInfo = $this->_oRecognition->getFlatRateRouteInfo();
                    $aBillInfo['match_routes'] = $aRouteInfo;
                    continue;
                }
            }
        }

        return $aBillInfo;
    }

    /**
     * 路线处理.获取路线信息.
     * @param array  $aBillInfo     aBillInfo
     * @param int    $iRequireLevel iRequireLevel
     * @param string $sAppVersion   sAppVersion
     * @return mixed
     */
    private function _loadRouteInfoNew($aBillInfo, $iRequireLevel, $sAppVersion) {
        if (!empty($aBillInfo['product_infos'][$iRequireLevel]['combo_type'])
            && HoraeConstants::TYPE_COMBO_CARPOOL_INTER_CITY == $aBillInfo['product_infos'][$iRequireLevel]['combo_type']
            && !empty($this->_aInterRouteInfo)
        ) {
            if (version_compare($sAppVersion, '5.2.46') >= 0) {
                $aBillInfo['match_routes'] = $this->_aInterRouteInfo;
            } else {
                $aBillInfo['match_routes'] = $aBillInfo['route_info'];
            }

            unset($aBillInfo['route_info']['time_span']);
        }

        if (!empty($aBillInfo['product_infos'][$iRequireLevel]['combo_type'])
            && HoraeConstants::TYPE_COMBO_CARPOOL == $aBillInfo['product_infos'][$iRequireLevel]['combo_type']
            && !empty($this->_aCarpoolRouteInfo)
        ) {
            $aBillInfo['match_routes'] = $this->_aCarpoolRouteInfo[$iRequireLevel];
        }

        $aSpecialOrder = array_merge($this->_aOrderInfo, ['combo_type' => $aBillInfo['product_infos'][$iRequireLevel]['combo_type'] ?? 0]);
        if (\BizCommon\Utils\Order::isSpecialRateV2($aSpecialOrder)) {
            $aBillInfo['match_routes'] = $this->_aFlatRateRouteInfo;
        }

        //anycar下增加区域渗透路线匹配
        if (CarLevel::DIDI_ANY_CAR_CAR_LEVEL == $iRequireLevel && !empty($aBillInfo['multi_info'])) {
            foreach ($aBillInfo['multi_info'] as $aProductInfo) {
                $iCarpoolType = $aProductInfo['carpool_type'] ?? 0;
                if (\BizCommon\Constants\OrderNTuple::CARPOOL_TYPE_FLAT_RATE == $iCarpoolType) {
                    $aRouteInfo = $this->_oRecognition->getFlatRateRouteInfo();
                    $aBillInfo['match_routes'] = $aRouteInfo;
                    continue;
                }
            }
        }

        return $aBillInfo;
    }

    /**
     * 城际特殊路线处理.
     * @param array $aParams Params
     */
    // 2024.9.10注释-待删
//    private function _processInterCityLongRoute($aParams) {
//        //设置场景参数 -- 三叉戟下，几条路线的特殊处理，fix 了一下城际其中的起终点名称
//        SceneParamsLogic::getInstance()->setSceneParams($this->_aOrderInfo);
//        // 跨城拼车三叉戟 命中长单路线
//        if (SceneParamsLogic::getInstance()->checkLongRouteType($this->_aOrderInfo)) {
//            $this->_aOrderInfo['intercity_from_district'] = SceneParamsLogic::getInstance()->getFromName();
//            $this->_aOrderInfo['intercity_to_district']   = SceneParamsLogic::getInstance()->getToName();
//        }
//
//        //城际座位数
//        if (\BizLib\Utils\Horae::isInterCityCarPoolScene($this->_aOrderInfo['combo_type'])
//            && !empty($aParams['intercity_carpool_seat_num'])
//        ) {
//            $this->_aOrderInfo['carpool_seat_num'] = (int)$aParams['intercity_carpool_seat_num'];
//        }
//    }

    /**
     * Function _isEnAppletsAnyCar 判断是否为英文小程序anycar（5.0anycar）
     * @param array $aParams 参数
     * @return bool
     */
    // 2024.9.10注释-待删
//    private function _isEnAppletsAnyCar($aParams) {
//        if ((isset($aParams['lang'])  && !empty($aParams['lang']) && 'en-US' == $aParams['lang'])
//            && (isset($aParams['menu_id']) && !empty($aParams['menu_id']) && 'nav_anycar' == $aParams['menu_id'])
//            && (isset($aParams['access_key_id']) && !empty($aParams['access_key_id'])
//            && (\BizLib\Constants\Common::DIDI_WECHAT_MINI_PROGRAM == $aParams['access_key_id']
//            || \BizLib\Constants\Common::DIDI_ALIPAY_MINI_PROGRAM == $aParams['access_key_id']))
//        ) {
//            return true;
//        }
//
//        return false;
//    }

    /**
     * Function _enAppletsAnyCarSubItemFilter 英文小程序接入anycar子车型过滤
     * @param  array $aOneConfs 端上传的oneConf
     * @return array
     */
    // 2024.9.10注释-待删
//    private function _enAppletsAnyCarSubItemFilter($aOneConfs) {
//        if (isset($aOneConfs) && !empty($aOneConfs) && is_array($aOneConfs)) {
//            foreach ($aOneConfs as &$aOneConfItem) {
//                if (!isset($aOneConfItem['product_id']) || empty($aOneConfItem['product_id'])
//                    || OrderSystem::PRODUCT_ID_ANY_CAR != $aOneConfItem['product_id']
//                    || !isset($aOneConfItem['multi_require_product']) || empty($aOneConfItem['multi_require_product'])
//                    || !is_array($aOneConfItem['multi_require_product'])
//                ) {
//                    continue;
//                }
//
//                $aMultiRequireProducts = [];
//                foreach ($aOneConfItem['multi_require_product'] as $aSingleItem) {
//                    $iComboType = $aSingleItem['combo_type'] ?? 0;
//                    $iProductId = UtilProduct::getProductIdByBusinessId($aSingleItem['business_id'] ?? 0);
//                    // 过滤拼车和第三方
//                    if (TripcloudProduct::isTripcloudByProductID($iProductId)
//                        || HoraeConstants::TYPE_COMBO_CARPOOL == $iComboType // 普通拼车
//                        || HoraeConstants::TYPE_COMBO_CARPOOL_INTER_CITY == $iComboType
//                    ) {
//                        continue;
//                    }
//
//                    $aMultiRequireProducts[] = $aSingleItem;
//                }
//
//                $aOneConfItem['multi_require_product'] = $aMultiRequireProducts;
//            }
//        }
//
//        return $aOneConfs;
//    }
}
