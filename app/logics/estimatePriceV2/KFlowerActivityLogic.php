<?php
/**
 * 霸王花预估活动运营.
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2020/3/30
 */

namespace PreSale\Logics\estimatePriceV2;

use BizCommon\Utils\Product;
use PreSale\Logics\passenger\NewUserLogic;
use Nuwa\ApolloSDK\Apollo;
use BizLib\Libraries\RedisDB;
use BizLib\Utils\Common;
use BizLib\Client\PopeEngineClient;
use BizLib\Constants\Common as CommonConstant;
use BizLib\Client\PopeRosenBridgeClient;
use BizLib\Log;
use BizLib\ErrCode;
use BizCommon\Utils\PerceiveAuto;

/**
 * Class KFlowerActivityLogic
 * @package PreSale\Logics\estimatePriceV2
 */
class KFlowerActivityLogic
{
    const TYPE_OPERATION_ACTIVITY_LOWEST_PRICE = 1; //全网最低价
    const TYPE_OPERATION_ACTIVITY_NEW_USER     = 2; //新客专享价
    const TYPE_OPERATION_ACTIVITY_SHOCKING     = 3; //惊爆一口价
    const TYPE_OPERATION_ACTIVITY_LUCKY        = 5; //惊喜锦鲤

    const ACTIVITY_ID_SHOCKING = 560870; //惊爆一口价账单返回类型

    private static $_oInstance = null;

    private $_aConf = [];

    private $_aCashBack = [];

    private $_aPsgTeamRushIndex = [];

    private $_bLuckyBoy = false;

    /**
     *
     * @return null|KFlowerActivityLogic
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * @param int $iProductID product_id
     *
     * @return array|mixed
     */
    public function getConf($iProductID) {
        return $this->_aConf[$iProductID] ?? [];
    }

    /**
     * @return array|mixed
     */
    public function getCashBack() {
        return $this->_aCashBack;
    }

    /**
     * @return array|mixed
     */
    public function getPsgTeamRushIndex() {
        return $this->_aPsgTeamRushIndex;
    }

    /**
     *
     * @return bool
     */
    public function isLuckyBoy() {
        return $this->_bLuckyBoy;
    }

    /**
     * loadConf
     *
     * @param array $aInfos aInfos
     *
     * @return void
     */
    public function loadInfo($aInfos) {
        foreach ($aInfos as $aInfo) {
            $this->_loadConf($aInfo);
        }
    }

    /**
     * loadConf
     *
     * @param array $aInfo aInfo
     *
     * @return void
     */
    private function _loadConf($aInfo) {
        //目前只有霸王花
        if (!Product::isKFlowerByProductID($aInfo['order_info']['product_id'])) {
            return;
        }

        //感知自动化切流
        $iOperationActivityType = $this->_getOperationType($aInfo);
        $aConfig = PerceiveAuto::getConfigByPosition(PerceiveAuto::ESTIMATE_FORM_CONFIG, $aInfo['passenger_info']['phone'] ?? '', $aInfo['order_info']['area'] ?? '', ['operation_type' => $iOperationActivityType]);

        //获取不到感知自动化，走原有逻辑 TODO 后续这块完全切流后，可删除
        if (empty($aConfig)) {
            //优先使用apollo配置
            $aConfig = $this->_getApolloOperationConfig($aInfo);

            //apollo无配置，走正常业务逻辑判断
            if (empty($aConfig)) {
                $aConfig = $this->_getBizOperationConfig($aInfo);
            }
        }

        if (!empty($aConfig)) {
            // $this->_setCache($aConfig, $aInfo);
            $this->_aConf[$aInfo['order_info']['product_id']] = $this->_format($aConfig);
        }

        //返现活动
        $this->_aCashBack = $this->_getCashBackInfo($aInfo);

        $oToggle = \Nuwa\ApolloSDK\Apollo::getInstance()->featureToggle(
            'kf_psg_team_rush_switch',
            [
                'phone' => $aInfo['passenger_info']['phone'],
                'city'  => $aInfo['order_info']['area'],
                'key'   => time(),
            ]
        );
        if ($oToggle->allow()) {
            $this->_aPsgTeamRushIndex = $this->_getPsgTeamRushIndex($aInfo);
        }
    }

    /**
     * 读取apollo的运营配置
     *
     * @param array $aInfo aInfo
     *
     * @return array
     */
    private function _getApolloOperationConfig($aInfo) {
        $aCondition = array(
            'city_id'     => $aInfo['order_info']['area'],
            'business_id' => $aInfo['order_info']['business_id'],
        );

        $aConfig = Apollo::getInstance()->getConfigsByNamespaceAndConditions(
            'kflower_estimate_marketing',
            $aCondition
        );

        list($ok, $aRet) = $aConfig->getAllConfigData();
        $aRet            = array_values($aRet)[0] ?? [];

        //检查配置是否生效
        $aActivityConfig = [];
        if (empty($aRet['open'])) {
            return $aActivityConfig;
        }

        //城市配置
        if (!empty($aRet['conf_list'])) {
            $aActivityConfig = $aRet['conf_list'];
        }

        //区县自定义配置优先级更高
        if (!empty($aRet['county_customize'])) {
            foreach ($aRet['county_customize'] as $aCountyConfig) {
                if (in_array($aInfo['order_info']['county'], $aCountyConfig['county_list'])) {
                    $aActivityConfig = $aCountyConfig['conf_list'];

                    break;
                }
            }
        }

        return $aActivityConfig;
    }

    /**
     * 根据业务判断运营类型
     *
     * @param array $aInfo aInfo
     *
     * @return array
     */
    private function _getBizOperationConfig($aInfo) {
        $iOperationActivityType = $this->_getOperationType($aInfo);

        $aCondition = array('operation_type' => $iOperationActivityType,);

        $aConfig = Apollo::getInstance()->getConfigsByNamespaceAndConditions(
            'kflower_estimate_component_control',
            $aCondition
        );

        list($ok, $aRet) = $aConfig->getAllConfigData();
        $aRet            = array_values($aRet)[0] ?? [];

        $aRet['conf_list']['operation_type'] = $iOperationActivityType;

        return $aRet['conf_list'];
    }

    /**
     * 计算type
     *
     * @param array $aInfo aInfo
     *
     * @return int
     */
    private function _getOperationType($aInfo) {
        $sCarLevel   = $aInfo['order_info']['require_level'];
        $iActivityId = $aInfo['bill_info']['bills'][$sCarLevel]['extra_info']['privilege_activity_id'] ?? 0;
        if (self::ACTIVITY_ID_SHOCKING == $iActivityId) {
            return self::TYPE_OPERATION_ACTIVITY_SHOCKING;
        }

        //惊喜锦鲤
        if (!empty($aInfo['activity_info'][0]['coupon_info']['kf_activity_coupon']['call_return_memo'])) {
            $aCallReturn = explode('|', $aInfo['activity_info'][0]['coupon_info']['kf_activity_coupon']['call_return_memo']);
            if (!empty($aCallReturn[3])) {
                if (ShortBookLogic::getInstance()->hasShortBook()) {
                    Log::warning(
                        ErrCode\Msg::formatArray(
                            ErrCode\Code::E_PASSENGER_LUCKY_BOY_ERROR,
                            array(
                                'call_retrun_memo' => $aInfo['activity_info'][0]['coupon_info']['kf_activity_coupon']['call_return_memo'],
                            )
                        )
                    );
                } else {
                    $this->_bLuckyBoy = true;

                    return self::TYPE_OPERATION_ACTIVITY_LUCKY;
                }
            }
        }

        if (empty($aInfo['passenger_info']['pid']) || NewUserLogic::isNewUser($aInfo['passenger_info']['pid'])) {
            return self::TYPE_OPERATION_ACTIVITY_NEW_USER;
        }

        return self::TYPE_OPERATION_ACTIVITY_LOWEST_PRICE;
    }

    /**
     * format
     *
     * @param array $aConfig $aConfig
     *
     * @return array
     */
    private function _format($aConfig) {
        $aConf = [
            'operation_type' => $aConfig['operation_type'],
            'skin'           => $aConfig['skin'] ?? '',
            'price_text'     => $aConfig['price_text'] ?? '',
            'estimate_text'  => (empty($aConfig['estimate_text']) || mb_strlen($aConfig['estimate_text'], 'utf8') > 12) ? '单单有优惠，打车更便宜' : $aConfig['estimate_text'],
        ];

        //计算价格展示效果
        $iPrice = 0;
        if (!empty($aConfig['price'])) {
            foreach ($aConfig['price'] as $item) {
                $iPrice += $item;
            }
        }

        $aConf['price'] = $iPrice;

        //计算按钮展示效果
        $iButton = 0;
        if (!empty($aConfig['button'])) {
            foreach ($aConfig['button'] as $item) {
                $iButton += $item;
            }
        }

        $aConf['button'] = $iButton;

        //计算优惠总价格标签展示效果
        $iDiscountLabel = 0;
        if (!empty($aConfig['discount_label'])) {
            foreach ($aConfig['discount_label'] as $item) {
                $iDiscountLabel += $item;
            }
        }

        $aConf['discount_label'] = $iDiscountLabel;

        return $aConf;
    }

    /**
     * @param array $aInfo Info
     *
     * @return array
     */
    private function _getCashBackInfo($aInfo) {
        if (empty($aInfo['passenger_info']['uid'])) {
            return [];
        }

        $aPopeParams = [
            'product_id'     => $aInfo['order_info']['business_id'],
            'event_info'     => [
                'city_id' => $aInfo['order_info']['area'],
                'role'    => CommonConstant::BIZ_TYPE_PASSENGER,
            ],
            'extra_info'     => [
                'scene'       => 'bubble',
                'estimate_id' => $aInfo['bill_info']['estimate_id'],
                'app_version' => $aInfo['common_info']['app_version'],
            ],
            'passenger_info' => [
                'user_id'      => $aInfo['passenger_info']['uid'],
                'passenger_id' => $aInfo['passenger_info']['pid'],
                'telphone'     => $aInfo['passenger_info']['phone'],
                'cur_lng'      => $aInfo['order_info']['current_lng'],
                'cur_lat'      => $aInfo['order_info']['current_lat'],
            ],
            'order_info'     => [
                'estimate_fee' => $aInfo['activity_info'][0]['estimate_fee'] ?? 0,
                'car_level'    => $aInfo['order_info']['require_level'],
            ],
        ];

        $aRet = (new PopeEngineClient())->popeEngineStandardApi($aPopeParams, PopeEngineClient::APP_ID_CASH_BACK);
        if (0 !== $aRet['errno'] || 0 !== $aRet['result']['errno']) {
            return [];
        }

        return $aRet['result']['data'];
    }

    /**获取乘客战队打车活动主页
     * @param array $aInfo Info
     *
     * @return array
     */
    private function _getPsgTeamRushIndex($aInfo) {
        if (empty($aInfo['passenger_info']['uid'])) {
            return [];
        }

        $aPopeParams = [
            'user_id'    => $aInfo['passenger_info']['uid'],
            'user_phone' => $aInfo['passenger_info']['phone'] ?? '',
            'lat'        => $aInfo['order_info']['current_lat'] ?? 0.0,
            'lng'        => $aInfo['order_info']['current_lng'] ?? 0.0,
            'scene_type' => 'psg_team_rush',
        ];

        $aRet = (new PopeRosenBridgeClient())->getPsgTeamRushIndex($aPopeParams);
        if (!isset($aRet['errno']) || 0 != $aRet['errno']) {
            Log::warning(
                ErrCode\Msg::formatArray(
                    ErrCode\Code::E_PASSENGER_POPE_ESTIMATE_REQ,
                    array(
                        'params' => json_encode($aPopeParams),
                        'return' => json_encode($aRet),
                    )
                )
            );
            return [];
        }

        return $aRet['data'];
    }
}
