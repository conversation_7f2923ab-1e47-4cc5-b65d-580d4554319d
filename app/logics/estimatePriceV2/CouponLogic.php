<?php

namespace PreSale\Logics\estimatePriceV2;

use Biz<PERSON>ib\Config as NuwaConfig;
use BizLib\Utils\Language;
use BizLib\Utils\NumberHelper;
use BizLib\Utils\UtilHelper;
use BizLib\Constants;
use BizCommon\Models\Activity\Coupon;
use BizCommon\Models\Order\Order;
use BizCommon\Models\Passenger\Passenger;
use BizCommon\Models\Activity\ActivityMis;

/**
 * 预估券相关.
 *
 * <AUTHOR>
 * @date(2017-03-13)
 */
class CouponLogic
{
    /**
     * @var \CI_Controller
     */
    private $_oCI = null;

    /**
     * @var CouponLogic
     */
    protected static $_oInstance = null;

    /**
     * @var array
     */
    private $_aConfig        = array();
    private $_aCommonInfo    = array();
    private $_aBillInfo      = array();
    private $_aOrderInfo     = array();
    private $_aPassengerInfo = array();
    private $_aParams        = array();
    private $_sCurrencyUnit;
    private $_bCarpoolCanUseCoupon    = true;
    private $_bNonCarpoolCanUseCoupon = true;

    const PASSENGER_OPERATION_COUPON_LENGTH = 5; //乘客运营需要的券列表长度
    const OT_DEFAULT_COMBO_RENTED_COUPON    = 37; //包车券
    const FENCE_SERVICE_SUCCESS_ERRNO       = 10000;
    const OT_COMBO_SHENGANG_GOUPON          = 308; // 深港

    /**
     * CouponLogic constructor.
     *
     * @param $aParams
     */
    private function __construct($aParams) {
        $this->_aCommonInfo    = $aParams['common_info'];
        $this->_aBillInfo      = $aParams['bill_info'];
        $this->_aPassengerInfo = $aParams['passenger_info'];
        $this->_aOrderInfo     = $aParams['order_info'];

        $sCurrency = isset($this->_aBillInfo['currency']) ? $this->_aBillInfo['currency'] : '';
        list($sSymbol, $sUnit)  = \BizLib\Utils\Currency::getSymbolUnit($sCurrency, $this->_aOrderInfo);
        $this->_sCurrencySymbol = $sSymbol;
        $this->_sCurrencyUnit   = $sUnit;

        $this->_aConfig  = NuwaConfig::text('config_text', 'mOrderEstimateNew');
        $this->_aMessage = NuwaConfig::text('message', 'message');
        $this->_aParams  = $aParams;
    }

    /**
     * [getInstance 获取单例].
     *
     * @param array $aParams
     *
     * @return CouponLogic
     */
    public static function getInstance(array $aParams) {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self($aParams);
        }

        return self::$_oInstance;
    }

    /**
     * getCouponFeeInfo.
     *
     * @param array $aMisData                    预估请求运营活动返回命中信息
     * @param array $aNormalCoupon
     * @param array $aCarPoolCoupon
     * @param array $aWithoutMemberProtectCoupon 没有动调会员保护券列表
     *
     * @return array
     */
    public function getCouponFeeInfo(array $aMisData, array $aNormalCoupon, array $aCarPoolCoupon, array $aWithoutMemberProtectCoupon) {
        //用户已经登录
        if ($this->_aPassengerInfo['pid'] > 0) {
            return $this->_getLoginUserFeeInfo($aMisData, $aNormalCoupon, $aCarPoolCoupon, $aWithoutMemberProtectCoupon);
        }

        return $this->_getNoLoginUserFeeInfo($aMisData);
    }

    /**
     * getCouponExtInfo 获取券extInfo.
     *
     * @return array
     */
    public function getCouponExtInfo() {
        $aCouponInfo = array();
        if ($this->_aPassengerInfo['pid'] > 0) {  //用户已经登录
            $aCouponInfo = $this->_getCouponInfoForLoginUser($this->_aOrderInfo['channel'], $this->_aParams['ext_info']);

            return $aCouponInfo;
        }

        return $aCouponInfo;
    }

    /**
     * [_bUseCoupon 是否可以用券].
     *
     * @param bool   $bActivityType          [是否活动类型]
     * @param bool   $bBusinessUserUseCoupon [企业用户是否可以用券]
     * @param string $sPassengerPhone        [乘客手机号，判断是否是南航]
     *
     * @return bool [是否用券]
     */
    public function bUseCoupon($bActivityType, bool $bBusinessUserUseCoupon, $sPassengerPhone) {
        //判断南航接送机
        $this->passenger = Passenger::getInstance();
        if ($this->passenger->bNanHangPassenger($sPassengerPhone)
            && Order::PRODUCT_ID_DEFAULT === $this->_aOrderInfo['product_id']
        ) {
            return false;
        }

        //企业不用券
        if ($this->_aCommonInfo['is_from_b2b']) {
            return false;
        }

        if (UtilHelper::isOpenApiChannel($this->_aOrderInfo['channel'])) {
            return false;
        }

        //一键叫、企业、导流都不用券
        if ($bActivityType || !$bBusinessUserUseCoupon) {
            return false;
        }

        return true;
    }

    /**
     * [getCouponInfoForLoginUser 已经登录用户拉券].
     *
     * @param int   $iChannel [乘客channel]
     * @param array $aExtInfo [description]
     *
     * @return array [description]
     */
    protected function _getCouponInfoForLoginUser(int $iChannel, array $aExtInfo) {
        $this->coupon = Coupon::getInstance();
        $this->coupon->setAppChannel($iChannel);
        $iOrderSectionType = $this->_aOrderInfo['coupon_type'];
        $aLocation         = array(
            'sta' => array(
                'lat' => (float)($this->_aOrderInfo['from_lat']),
                'lng' => (float)($this->_aOrderInfo['from_lng']),
            ),
            'des' => array(
                'lat' => (float)($this->_aOrderInfo['to_lat']),
                'lng' => (float)($this->_aOrderInfo['to_lng']),
            ),
        );
        $bIsFastCar        = $this->_aOrderInfo['is_fast_car'];
        if (Constants\OrderSystem::PRODUCT_ID_DEFAULT == $this->_aOrderInfo['product_id']
            && Constants\OrderSystem::TYPE_COMBO_RENTED == $this->_aOrderInfo['combo_type']
        ) {
            $iOrderSectionType = self::OT_DEFAULT_COMBO_RENTED_COUPON; //包车券 = 37
        }

        if (Constants\Horae::TYPE_COMBO_SHENGANG_FLAT_RATE == $this->_aOrderInfo['combo_type']) {
            $iOrderSectionType = self::OT_COMBO_SHENGANG_GOUPON;
        }

        $aPayFee = array(
            'fast_car' => $this->_aBillInfo['noncarpool']['basic_total_fee'],
            'car_pool' => $this->_aBillInfo['carpool']['basic_total_fee'],
        );

        $bHitMemberCapping = !empty($this->_aBillInfo['noncarpool']['without_member_protect_basic_fee']);
        if ($bHitMemberCapping) {
            $aPayFee['without_member_protect'] = $this->_aBillInfo['noncarpool']['without_member_protect_basic_fee'];
        }

        //预先处理好
        $this->_bNonCarpoolCanUseCoupon = $this->bUseCoupon($this->_aParams['one_key_activity']['activity_switch'], $this->_aParams['noncarpool_use_coupon'], $this->_aPassengerInfo['phone']);
        $this->coupon->setNonCarpoolUseCoupon($this->_bNonCarpoolCanUseCoupon);

        $this->_bCarpoolCanUseCoupon = $this->_aBillInfo['is_carpool_open'] && $this->bUseCoupon($this->_aParams['one_key_activity']['activity_switch'], $this->_aParams['carpool_use_coupon'], $this->_aPassengerInfo['phone']);
        $this->coupon->setCarpoolUseCoupon($this->_bCarpoolCanUseCoupon);

        $aCouponData   = $this->coupon->getAvailableForEstimate(
            $this->_aOrderInfo,
            $this->_aPassengerInfo,
            $aPayFee,
            $aLocation,
            $this->_bCarpoolCanUseCoupon,
            $bIsFastCar,
            $iOrderSectionType,
            $this->_aCommonInfo,
            'pre-sale',
        );
        $aNormalCoupon = $aCarPoolCoupon = array();
        if (isset($aCouponData['errno']) && GLOBAL_SUCCESS == $aCouponData['errno']) {
            $aNormalCoupon  = !empty($aCouponData['not_carPool']['info']) ? $aCouponData['not_carPool']['info'] : array();
            $aCarPoolCoupon = !empty($aCouponData['carPool']['info']) ? $aCouponData['carPool']['info'] : array();
            //判断是否有导流券
            $aNormalDiversionalCoupon = $aCouponData['not_carPool']['diversional_coupon_list'] ?? array();
            if ($bHitMemberCapping) {
                $aWithoutMemberProtectCoupon = !empty($aCouponData['without_member_protect']['info']) ? $aCouponData['without_member_protect']['info'] : [];
            }
        }

        $aExtInfo['noncarpool_coupon_list'] = array_slice($aNormalCoupon, 0, self::PASSENGER_OPERATION_COUPON_LENGTH);
        $aExtInfo['carpool_coupon_list']    = array_slice($aCarPoolCoupon, 0, self::PASSENGER_OPERATION_COUPON_LENGTH);
        if ($bHitMemberCapping) {
            $aExtInfo['without_member_protect_coupon_list'] = array_slice($aWithoutMemberProtectCoupon, 0, self::PASSENGER_OPERATION_COUPON_LENGTH);
        }

        $aExtInfo['coupon_request_data'] = $this->coupon->buildAvailableCouponRequest(
            $this->_aOrderInfo,
            $this->_aPassengerInfo,
            $aPayFee,
            $aLocation,
            $this->_bCarpoolCanUseCoupon,
            $bIsFastCar,
            $iOrderSectionType,
            $this->_aCommonInfo
        );

        $aReturn = [
            'noncarpool_coupon_list'             => $aNormalCoupon,
            'carpool_coupon_list'                => $aCarPoolCoupon,
            'noncarpool_diversional_coupon_list' => $aNormalDiversionalCoupon,
            'ext_info'                           => $aExtInfo,
        ];
        if ($bHitMemberCapping) {
            $aReturn['without_member_protect_coupon_list'] = $aWithoutMemberProtectCoupon;
        }

        return $aReturn;
    }

    /**
     * 根据券的值,计算各种费用.
     *
     * @param array $aMisData
     * @param array $aOrderInfo
     * @param array $aCouponInfo
     *
     * @return array
     */
    private function _calCouponInfo(array $aMisData, array $aOrderInfo, array $aCouponInfo) {
        $aRetCouponInfo = array();
        $aBatchInfo     = $this->_getMiusInfo($aMisData, $aOrderInfo);
        foreach ($aCouponInfo as $sMode => $aNormalCoupon) {
            //默认选券的信息
            $aDefaultCoupon = isset($aNormalCoupon[0]['amount']) && sprintf('%.1f', $aNormalCoupon[0]['amount'] / 100) > 0 ? $aNormalCoupon[0] : array();
            //呼叫返券信息
            $aBatchConfig = $aBatchInfo[$sMode]['config'] ?? array();

            //禁止用券, 运营活动拼车全依赖于非拼车, 此处过滤拼车可用券非拼车不可用券
            if ((!$this->_bNonCarpoolCanUseCoupon && in_array($sMode, ['not_carpool', 'without_member_protect']))
                || (!$this->_bCarpoolCanUseCoupon && 'carpool' == $sMode)
            ) {
                $aDefaultCoupon = $aBatchConfig = [];
            }

            //券抵扣前费用
            if ('without_member_protect' == $sMode) {
                if (empty($this->_aBillInfo['noncarpool']['without_member_protect_total_fee'])) {
                    continue; //没有命中会员封顶,不需要计算
                }

                $fEstimateFee = $this->_aBillInfo['noncarpool']['without_member_protect_total_fee'];
            } else {
                //这个新预估重构之后就有,  carpool, not_carpool这bill种不对应 ....
                $fEstimateFee = isset($this->_aBillInfo[$sMode]) ? $this->_aBillInfo['carpool']['dynamic_total_fee'] : $this->_aBillInfo['noncarpool']['dynamic_total_fee'];
            }

            $aEstimate = array();

            //乘客运营和券系统同时返回券的情况
            if (!empty($aDefaultCoupon) && !empty($aBatchConfig)) {
                //
                //  选取优惠券逻辑：
                //  1. 绝对值差额最小（预估价10元，1张8元券，1张15元券，用8元券）
                //  2. 券面额最小（预估价10元，1张8元券，1张12元券，用8元券）
                //  3. 过期时间近（预估价10元，1张8元券，明天过期，1张8元券，后天过期，用明天过期的8元券）
                //  4. 以上都满足，使用券系统返回的券.
                $fCouponPrice        = (float)(sprintf('%.1f', $aDefaultCoupon['amount'] / 100));
                $iCouponDiffPriceAbs = $aDefaultCoupon['diff_amount'];  //券系统返回的优惠金额跟预估价的差值的绝对值
                $iMisDiffAbs         = round(abs($fEstimateFee - $aBatchConfig['money']) * 100);   //乘客运营返回的优惠金额跟预估价的差值的绝对值
                $bUseCoupon          = false;
                $bUseMis = false;
                if ($iCouponDiffPriceAbs < $iMisDiffAbs) {
                    $bUseCoupon = true;
                } elseif ($iCouponDiffPriceAbs > $iMisDiffAbs) {
                    $bUseMis = true;
                } else {
                    if ($fCouponPrice < $aBatchConfig['money']) {
                        $bUseCoupon = true;
                    } elseif ($fCouponPrice > $aBatchConfig['money']) {
                        $bUseMis = true;
                    } else {
                        $iCouponExpireTime = isset($aDefaultCoupon['expire_time']) ? strtotime($aDefaultCoupon['expire_time']) : 0;
                        if ($iCouponExpireTime <= $aBatchConfig['expire_time']) {
                            $bUseCoupon = true;
                        } else {
                            $bUseMis = true;
                        }
                    }
                }

                if ($bUseCoupon) {
                    $sCouponTitle        = $this->_aConfig['coupon_title'];
                    $aEstimate['detail'] = array(
                        'title'  => $sCouponTitle,
                        'value'  => $this->_genFeeInfo($fCouponPrice),
                        'color'  => '#ff9a03',
                        'amount' => $fCouponPrice,
                    );
                    $aDefaultCoupon['estimate_show'] = 1;
                    $fEstimateFee -= $fCouponPrice;
                    $fEstimateFee  = $fEstimateFee > 0 ? $fEstimateFee : 0;
                } elseif ($bUseMis) {
                    $sCouponTitle        = $aBatchConfig['coupon_msg'];
                    $aEstimate['detail'] = array(
                        'title'  => $sCouponTitle,
                        'value'  => $this->_genFeeInfo($aBatchConfig['money']),
                        'color'  => '#ff9a03',
                        'amount' => $aBatchConfig['money'],
                    );
                    $aBatchConfig['estimate_show'] = 1;
                    $fEstimateFee = $fEstimateFee - $aBatchConfig['money'] ?: 0;
                    $fEstimateFee = $fEstimateFee > 0 ? $fEstimateFee : 0;
                } else {
                    continue;
                }

                $aRetCouponInfo[$sMode] = array(
                    'estimate_fee'    => (float)(sprintf('%.1f', $fEstimateFee)),
                    'estimate_detail' => $aEstimate,
                    'default_coupon'  => $aDefaultCoupon,
                    'activity_coupon' => $aBatchConfig,
                );
                continue;
            }

            if (!empty($aDefaultCoupon)) {
                $sCouponPrice = (float)(sprintf('%.1f', $aDefaultCoupon['amount'] / 100));
                //30立减标志(前面图标的展示)
                if (isset($aBatchConfig['money']) && $aBatchConfig['money'] > $sCouponPrice) {
                    $sCouponTitle        = $aBatchConfig['coupon_msg'];
                    $aEstimate['detail'] = array(
                        'title'  => $sCouponTitle,
                        'value'  => $this->_genFeeInfo($aBatchConfig['money']),
                        'color'  => '#ff9a03',
                        'amount' => $aBatchConfig['money'],
                    );
                    $aBatchConfig['estimate_show'] = 1;
                    $fEstimateFee = $fEstimateFee - $aBatchConfig['money'] ?: 0;
                    $fEstimateFee = $fEstimateFee > 0 ? $fEstimateFee : 0;
                } else {
                    $sCouponTitle        = $this->_aConfig['coupon_title'];
                    $aEstimate['detail'] = array(
                        'title'  => $sCouponTitle,
                        'value'  => $this->_genFeeInfo($sCouponPrice),
                        'color'  => '#ff9a03',
                        'amount' => $sCouponPrice,
                    );
                    $aDefaultCoupon['estimate_show'] = 1;
                    $fEstimateFee -= $sCouponPrice;
                    $fEstimateFee  = $fEstimateFee > 0 ? $fEstimateFee : 0;
                }
            } elseif (!empty($aBatchConfig)) {
                $sCouponTitle        = $aBatchConfig['coupon_msg'];
                $aEstimate['detail'] = array(
                    'title'  => $sCouponTitle,
                    'value'  => $this->_genFeeInfo($aBatchConfig['money']),
                    'color'  => '#ff9a03',
                    'amount' => $aBatchConfig['money'],
                );
                $aBatchConfig['estimate_show'] = 1;
                $fEstimateFee = $fEstimateFee - $aBatchConfig['money'] ?: 0;
                $fEstimateFee = $fEstimateFee > 0 ? $fEstimateFee : 0;
            }

            $aRetCouponInfo[$sMode] = array(
                'estimate_fee'    => (float)(sprintf('%.1f', $fEstimateFee)),
                'estimate_detail' => $aEstimate,
                'default_coupon'  => $aDefaultCoupon,
                'activity_coupon' => $aBatchConfig,
            );
        }

        return $aRetCouponInfo;
    }

    /**
     * [_getLoginUserFeeInfo 登录用户获取拼车、非拼车券信息].
     *
     * @param array $aMisData
     * @param array $aNormalCoupon
     * @param array $aCarPoolCoupon
     * @param array $aWithoutMemberProtectCoupon 没有动调会员保护券列表
     *
     * @return array
     */
    private function _getLoginUserFeeInfo(array $aMisData, array $aNormalCoupon, array $aCarPoolCoupon, array $aWithoutMemberProtectCoupon) {
        $aReturn        = array();
        $aCouponInfo    = array(
            'not_carpool'            => $aNormalCoupon,
            'carpool'                => $aCarPoolCoupon,
            'without_member_protect' => $aWithoutMemberProtectCoupon,
        );
        $aRetCouponInfo = $this->_calCouponInfo($aMisData, $this->_aOrderInfo, $aCouponInfo);
        if (!empty($aRetCouponInfo)) {
            $aReturn['noncarpool_estimate_detail'] = $aRetCouponInfo['not_carpool']['estimate_detail'];
            $aReturn['estimate_fee']            = $aRetCouponInfo['not_carpool']['estimate_fee'];
            $aReturn['carpool_estimate_detail'] = $aRetCouponInfo['carpool']['estimate_detail'];
            $aReturn['discount_fee']            = $aRetCouponInfo['carpool']['estimate_fee'];
            if (isset($aRetCouponInfo['without_member_protect'])) {
                $aReturn['without_member_protect_fee']    = $aRetCouponInfo['without_member_protect']['estimate_fee'] ?? 0;
                $aReturn['without_member_protect_detail'] = $aRetCouponInfo['without_member_protect']['estimate_detail'] ?? [];
            }

            $aReturn['coupon_info'] = [
                'carpool'    => [
                    'default_coupon'  => $aRetCouponInfo['carpool']['default_coupon'] ?? [],
                    'activity_coupon' => $aRetCouponInfo['carpool']['activity_coupon'] ?? [],
                ],
                'noncarpool' => [
                    'default_coupon'  => $aRetCouponInfo['not_carpool']['default_coupon'] ?? [],
                    'activity_coupon' => $aRetCouponInfo['not_carpool']['activity_coupon'] ?? [],
                ],
            ];
        }

        return $aReturn;
    }

    /**
     * [_getNoLoginUserFeeInfo 未登录用户获取券信息].
     *
     * @param array $aMisData
     *
     * @return array
     */
    private function _getNoLoginUserFeeInfo($aMisData) {
        $aNoCarpoolEstimate = $aCarpoolEstimate = array();
        $fEstimateFee       = $this->_aBillInfo['noncarpool']['dynamic_total_fee'];
        $fDiscountFee       = $this->_aBillInfo['carpool']['dynamic_total_fee'];
        if ($aMisData) {
            $aBatchInfo = $this->_getMiusInfo($aMisData, $this->_aOrderInfo);
            //拼车状况
            if (isset($aBatchInfo['carpool']['config'])) {
                $aCarPoolConfig = $aBatchInfo['carpool']['config'];
                $sCouponTitle   = $aCarPoolConfig['coupon_msg'];
                $aCarpoolEstimate['detail'] = array(
                    'title'  => $sCouponTitle,
                    'value'  => $this->_genFeeInfo($aCarPoolConfig['money']),
                    'color'  => '#ff9a03',
                    'amount' => $aCarPoolConfig['money'],
                );
                $fDiscountFee = $this->_aBillInfo['carpool']['dynamic_total_fee'] - $aCarPoolConfig['money'];
                $fDiscountFee = $fDiscountFee > 0 ? $fDiscountFee : 0;
            }

            //非拼车状况
            if (isset($aBatchInfo['not_carpool']['config'])) {
                $aNoCarPoolConfig = $aBatchInfo['not_carpool']['config'];
                $sCouponTitle     = $aNoCarPoolConfig['coupon_msg'];
                $aNoCarpoolEstimate['detail'] = array(
                    'title'  => $sCouponTitle,
                    'value'  => $this->_genFeeInfo($aNoCarPoolConfig['money']),
                    'color'  => '#ff9a03',
                    'amount' => $aNoCarPoolConfig['money'],
                );
                $fEstimateFee = $this->_aBillInfo['noncarpool']['dynamic_total_fee'] - $aNoCarPoolConfig['money'];
                $fEstimateFee = $fEstimateFee > 0 ? $fEstimateFee : 0;
            }

            return array(
                'noncarpool_estimate_detail' => $aNoCarpoolEstimate,
                'carpool_estimate_detail'    => $aCarpoolEstimate,
                'estimate_fee'               => $fEstimateFee,
                'discount_fee'               => $fDiscountFee,
            );
        }

        $sTitleNoLogin = $this->_aMessage['estimate_no_login'];

        return array(
            'noncarpool_estimate_detail' => array(
                'detail' => array(
                    'title'  => $sTitleNoLogin,
                    'value'  => '',
                    'color'  => '#ff9a03',
                    'amount' => 0,
                ),
            ),
            'carpool_estimate_detail'    => array(
                'detail' => array(
                    'title'  => $sTitleNoLogin,
                    'value'  => '',
                    'color'  => '#ff9a03',
                    'amount' => 0,
                ),
            ),
            'estimate_fee'               => $fEstimateFee,
            'discount_fee'               => $fDiscountFee,
        );
    }

    /**
     * @param $aMisData
     * @param $aOrderInfo
     *
     * @return array
     *
     * $fYuan = 14.5;
     * $aTestData = array(
     * 'estimate_coupon_msg'=> "活动抵扣{$fYuan}元",
     * 'coupon_msg' => '',
     * 'batchid' => 999,
     * 'activityid' => 888,
     * 'max_amount' => $fYuan,
     * 'tag' => '',
     * 'money' => $fYuan,
     * );
     * return $aTestData;
     */
    private function _getMiusInfo($aMisData, $aOrderInfo) {
        $aReturn = array();
        if (empty($aMisData)) {
            return array();
        }

        $this->activityMis = ActivityMis::getInstance();
        $aHitInfo          = $this->activityMis->getEstimateMsgByActivityFlag(ActivityMis::CALL_COUPON_FLAG, $aMisData, $aOrderInfo);
        if (empty($aHitInfo)) {
            return array();
        }

        if (!empty($aHitInfo['carpool']['config'])) {
            $aReturn['carpool']['config'] = $aHitInfo['carpool']['config'];
        }

        if (!empty($aHitInfo['not_carpool']['config'])) {
            $aReturn['not_carpool']['config'] = $aHitInfo['not_carpool']['config'];
        }

        if (!empty($aHitInfo['without_member_protect']['config'])) {
            $aReturn['without_member_protect']['config'] = $aHitInfo['without_member_protect']['config'];
        }

        return $aReturn;
    }

    /**
     * [_genFeeInfo 翻译语言].
     *
     * @param [type] $fee
     *
     * @return string
     */
    private function _genFeeInfo($fee) {
        return Language::replaceTag(
            $this->_aConfig['fee_info_v2'],
            array(
                'currency_symbol' => $this->_sCurrencySymbol,
                'fee'             => NumberHelper::numberFormatDisplay($fee),
                'currency_unit'   => $this->_sCurrencyUnit,
            )
        );
    }
}
