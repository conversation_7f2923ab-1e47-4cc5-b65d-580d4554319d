<?php

namespace PreSale\Logics\estimatePriceV2;

use Biz<PERSON>ib\Client\SpsClient;
use BizLib\Config as NuwaConfig;
use BizLib\Log;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Product;
use BizLib\Utils\Language;
use BizLib\Utils\UtilHelper;
use Dirpc\SDK\PreSale\LuxMultiEstimatePriceRequest as LuxRequest;
use PreSale\Logics\scene\custom\CustomLogic;
use PreSale\Logics\v3Estimate\AdditionalServiceLogic;
use Xiaoju\Apollo\Apollo;
use Xiaoju\Apollo\ApolloConstant;

/**
 * 增值服务价格获取
 * Class SpsAddServicePriceLogic
 */
class SpsAddServicePriceLogic
{
    private static $_oInstance = null;

    private $_oSpsClient;
    private $_aConfig;

    /**
     * @var array map[pcId]{map[102]服务价格}
     */
    private $_aData = []; //sps的结果

    const LUXURY_MEMBER_DISCOUNT = 'sps_privilege_level_discount'; //专豪会员等级权益优惠
    const LUXURY_REDUCE_DISCOUNT = 'sps_privilege_reduce_discount'; // 溢价服务费权益减免优惠

    /**
     * SpsAddServicePriceLogic constructor.
     */
    private function __construct() {
        $this->_oSpsClient = new SpsClient();
        $this->_aConfig    = NuwaConfig::text('additional_service', 'content');
    }

    /**
     * @return SpsAddServicePriceLogic
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * 请求sps获取服务价格
     * @param array $aSpsAddServiceReqs 请求
     * @return void
     */
    public function initAddServiceFeeInfo($aSpsAddServiceReqs) {
        $aResult = [];

        if (empty($aSpsAddServiceReqs)) {
            return;
        }

        try {
            $aRes = $this->_oSpsClient->getCustomedServiceFeeInfo(['req_list' => $aSpsAddServiceReqs]);
            if (0 != $aRes['errno'] || empty($aRes['data']['fee_info']) || !is_array($aRes['data']['fee_info'])) {
                return;
            }

            foreach ($aRes['data']['fee_info'] as $aServiceFeeInfo) {
                if (empty($aServiceFeeInfo['fee_list']) || !is_array($aServiceFeeInfo['fee_list'])) {
                    continue;
                }

                foreach ($aServiceFeeInfo['fee_list'] as $aFeeInfo) {
                    $aResult[$aServiceFeeInfo['product_category']][$aFeeInfo['service_id']] = $aFeeInfo;
                }
            }
        } catch (\Exception $e) {
            Log::warning('getCustomedServiceFeeInfo error|' . $e->getMessage());
        }

        $this->_aData = $aResult;
    }

    /**
     * 构建新的价格
     * @param  array      $aServiceList     $aServiceList
     * @param  int        $sPid             pid
     * @param  LuxRequest $oEstimateRequest $oEstimateRequest
     * @param  array      $aMemberInfo      会员信息
     * @param  bool       $bIsHitPriceNew   $bIsHitPriceNew
     * @param  array      $aAreaInfo        $aAreaInfo
     * @return void
     * @throws \Exception e
     * sps价格 http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=280300447
     */
    public function addNewCustomServicePrice(&$aServiceList, $sPid, $oEstimateRequest, $aMemberInfo, $bIsHitPriceNew, $aAreaInfo) {
        if (empty($this->_aConfig)) {
            return;
        }

        if (Product::COMMON_PRODUCT_ID_FIRST_CLASS_CAR == $oEstimateRequest->getBusinessId()) {
            //豪华车逻辑
            $this->addLuxuryCustomServicePrice($aServiceList, $sPid, $oEstimateRequest, $aMemberInfo, $bIsHitPriceNew, $aAreaInfo);
            return;
        }

        //专车逻辑 （先复制下一样的 之后再改）
        $this->addPremiumCustomServicePrice($aServiceList, $sPid, $oEstimateRequest, $aMemberInfo);
        return;
    }

    /**
     * 豪华车个性化服务渲染逻辑
     * @param  array      $aServiceList     $aServiceList
     * @param  int        $sPid             $sPid
     * @param  LuxRequest $oEstimateRequest $oEstimateRequest
     * @param  array      $aMemberInfo      $aMemberProfile
     * @param  bool       $bIsHitPriceNew   $bIsHitPriceNew
     * @param  array      $aAreaInfo        $aAreaInfo
     * @return void
     * @throws \Exception e
     * sps价格 http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=280300447
     */
    public function addLuxuryCustomServicePrice(&$aServiceList, $sPid, $oEstimateRequest, $aMemberInfo, $bIsHitPriceNew, $aAreaInfo) {
        // 豪华车偏好的逻辑以后写在这里
        //通用逻辑-后续做
        //先专车豪华车解藕
        $iProductCategory = $oEstimateRequest->getProductCategory();
        $sLang            = $oEstimateRequest->getLang();
        $aUnit            = Language::getUnit($sLang);
        $bIsNewDesc       = $this->_isNewVersionDesc($oEstimateRequest, $sPid);
        AdditionalServiceLogic::getInstance()->initCustomFeatureAndPreferData($sPid);
        foreach ($aServiceList as &$aService) {
            if ($bIsHitPriceNew && CustomLogic::CUSTOM_SERVICE_GUIDE_NEW == $aService['id']) {
                //机场助理引导 且 走新链路
                $this->_getNewPriceLogic($aService,$aMemberInfo,$oEstimateRequest,$aAreaInfo);
                continue;
            }

            //只有专车的逻辑
            if ($bIsNewDesc) {
                if (!empty($aService['desc'])) {
                    $aService['service_desc'] = $aService['desc'];
                    unset($aService['desc']);
                } else {
                    $aService['service_desc'] = $this->_aConfig[$aService['id']]['service_desc'] ?? '';
                }
            }

            //获取sps的费用
            $aFeeInfo = $this->_aData[$iProductCategory][$aService['id']];
            if (empty($aFeeInfo) || !isset($aFeeInfo['discount_price'])) {
                continue;
            }

            //只有携宠出行 才会命中的逻辑
            if (!empty($aFeeInfo['multi_prices']) && $this->_isMultiPriceService($oEstimateRequest, $sPid, $aService['id'])) {
                $aServicePrices       = $this->_buildMultiPrices($oEstimateRequest, $aFeeInfo['multi_prices'], $aService, $sLang, $aUnit);
                $aService['services'] = $aServicePrices['multi_prices'] ?? null;
                continue;
            }

            $aService['price']      = $aFeeInfo['discount_price'];
            $aService['price_msg']  = sprintf('{%s}%s', $aFeeInfo['discount_price'], $aUnit['currency']);
            $aService['price_desc'] = Language::replaceTag(
                $this->_aConfig['price_desc'],
                array(
                    'discount_price' => $aFeeInfo['discount_price'],
                    'currency'       => $aUnit['currency'],
                    'unit'           => $aService['unit'],
                )
            );

            // 服务为 机场助理引导（102），儿童贴心服务（111 才会执行后续逻辑）
            $oApollo = Apollo::getInstance();
            $oToggle = $oApollo->featureToggle(
                'gs_custom_service_price_desc_switch',
                [
                    'key'        => time(),
                    'service_id' => $aService['id'],
                ]
            );

            // 如果当前服务为儿童暖心服务，且价格为0，不显示0元/次
            if ($oToggle->allow() && 0 == $aFeeInfo['fee_price'] && 0 == $aFeeInfo['discount_price']) {
                // 费用为0时，机场助理和亲子服务不显示 0元/次 文案
                $aService['price']        = '';
                $aService['unit']         = '';
                $aService['unit_price']   = '';
                $aService['origin_price'] = '';
                $aService['price_desc']   = '';
                $aService['price_msg']    = '';
                continue;
            }

            //专车偏好页的开关-主要做版本控制
            $iProductID    = \BizLib\Utils\Product::getProductIdByBusinessId($oEstimateRequest->getBusinessId());
            $oToggleResult = $oApollo->featureToggle(
                'special_car_new_setting_page',
                [
                    'key'           => time(),
                    'app_version'   => $oEstimateRequest->getAppVersion(),
                    'access_key_id' => $oEstimateRequest->getAccessKeyId(),
                    'product_id'    => $iProductID,
                    'pid'           => $sPid,
                ]
            );

            $aFeeDetailConfig = $this->_aConfig[$aService['id']]['fee_detail_template'] ?? [];

            if ($oToggleResult->allow()) {
                $sDcmpName = $oToggleResult->getParameter('dcmp_name', '');
                if ('' != $sDcmpName) {
                    $aFeeDetailConfig = $this->_aConfig[$aService['id']][$sDcmpName] ?? [];
                }
            }

            // 下面计算折扣并展示
            if (empty($aFeeInfo['discount_info']) || !is_array($aFeeInfo['discount_info']) || empty($aFeeDetailConfig)) {
                continue;
            }

            //机场助理 置灰的话，不调整desc (豪华车之前没有收费，所以这里的逻辑是专车的机场助理逻辑)
            if (CustomLogic::CUSTOM_SERVICE_GUIDE_NEW == $aService['id'] && 1 != $aService['status']) {
                continue;
            }

            // todo 多个负的费用项文案,不考虑
            if (1 == sizeof($aFeeInfo['discount_info'])) {
                $aDiscount = $aFeeInfo['discount_info'][0];
                if (!empty($aFeeDetailConfig[$aDiscount['fee_name']])) {
                    $aService['desc'] = Language::replaceTag(
                        $aFeeDetailConfig[$aDiscount['fee_name']]['price_info_desc'],
                        [
                            'fee_price' => $aFeeInfo['fee_price'],
                            'amount'    => $aDiscount['fee_price'],
                            'currency'  => $aUnit['currency'],
                            'unit'      => $aService['unit'],
                        ]
                    );
                    $aService['tag']  = $aFeeDetailConfig[$aDiscount['fee_name']]['price_info_tag'] ?? '';
                }
            }
        }
    }

    /**
     * 新的取价格的链路
     * @param array  $aService     $aService
     * @param array  $aMemberInfo  $aMemberProfile
     * @param LuxRequest $oEstimateRequest $oEstimateRequest
     * @param array      $aAreaInfo        $aAreaInfo
     * @return void
     */
    private function _getNewPriceLogic(&$aService, $aMemberInfo, $oEstimateRequest, $aAreaInfo) {
        if (empty($aService['service_fee_info'])) {
            // 新链路没价格数据 应该已经过滤了
            return;
        }
        if (0 == $aService['service_fee_info']['fee_price']) {
            // 原价为0时，机场助理和不显示价格信息
            $aService['price']        = '';
            $aService['unit']         = '';
            $aService['unit_price']   = '';
            $aService['origin_price'] = '';
            $aService['price_desc']   = '';
            $aService['price_msg']    = '';
            return;
        }

        $aCustomNewConfig = NuwaConfig::text('estimate_form_v3', 'fee_desc_custom_discount'); //新链路文案模版
        if (empty($aCustomNewConfig)) {
            return;
        }

        //价格与费用描述
        $aService['price']      = $aService['service_fee_info']['fee_price'];
        $aService['unit']       = '';
        $aService['price_desc'] = Language::replaceTag(
            $aCustomNewConfig['102_tailor_service']['price_desc'],
            array(
                'currency' => $aService['service_fee_info']['fee_price'],
            )
        );

        //机场助理 置灰的话，不展示折扣信息
        if (CustomLogic::CUSTOM_SERVICE_GUIDE_NEW == $aService['id'] && 1 != $aService['status']) {
            //置灰后，端上识别的是disable_icon
            $aService['disable_icon'] = $aService['icon'];
            unset($aService['icon']);
            return;
        }

        $params = [
            'pid'           => $aMemberInfo['pid'],
            'city'          => $aAreaInfo['id'],
            'access_key_id' => $oEstimateRequest->getAccessKeyId(),
            'app_version'   => $oEstimateRequest->getAppVersion(),
        ];
        //机场助理默认勾选
        if (AdditionalServiceLogic::getInstance()->isNeedDefaultCheckGuideNewCustomService($params)) {
            if ($this->_defaultCheckGuideNew($oEstimateRequest, $params, $aService)) {
                $aService['selected_count']    = 1;
            }
        }
        //处理价格抵扣
        if (0 != $aService['service_fee_info']['discount_fee']) {
            $sLuxuryMemberIcon = $aCustomNewConfig['premium_member']['luxury'];
            $aTemp = $aCustomNewConfig['102_tailor_service']['reduce_desc']; //用{惠-xx元}样式兜底
            if (self::LUXURY_MEMBER_DISCOUNT == $aService['service_fee_info']['discount_name']) {
                //是专豪等级会员带来的权益，有独特展示
                if (2 != $oEstimateRequest->getAccessKeyId() || UtilHelper::compareAppVersion($oEstimateRequest->getAppVersion(), '6.9.18') > -1) {
                    //安卓端在 6.9.18版本以下 豪华车偏好页 展示tag有问题，所以不赋值 （a小于b版本为-1）
                    $iLuxuryMemberLevel = $aMemberInfo['luxury_level_id']; //专豪会员等级
                    if ($iLuxuryMemberLevel > 0) {
                        //有专豪等级
                        $aService['tag'] = $sLuxuryMemberIcon[$iLuxuryMemberLevel];
                        $aTemp           = $aCustomNewConfig['102_tailor_service']['member_desc'];
                    }
                }
            }

            //券后价
            $aService['price'] = $aService['service_fee_info']['discount_price'];

            //减价项描述
            $aService['desc'] = Language::replaceTag(
                $aTemp,
                array(
                    'currency' => $aService['service_fee_info']['discount_fee'],
                )
            );
            return;
        }

        return;
    }

    /**
     * 专车个性化服务渲染逻辑
     * @param array      $aServiceList     $aServiceList
     * @param int        $sPid             pid
     * @param LuxRequest $oEstimateRequest $oEstimateRequest
     * @param array      $aMemberInfo      会员信息
     * @return void
     * @throws \Exception e
     * sps价格 http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=280300447
     */
    public function addPremiumCustomServicePrice(&$aServiceList, $sPid, $oEstimateRequest, $aMemberInfo) {
        if (empty($this->_aConfig)) {
            return;
        }

        $iProductCategory = $oEstimateRequest->getProductCategory();
        $sLang            = $oEstimateRequest->getLang();
        $aUnit            = Language::getUnit($sLang);
        $bIsNewDesc       = $this->_isNewVersionDesc($oEstimateRequest, $sPid);
        foreach ($aServiceList as &$aService) {
            //只有专车的逻辑
            if ($bIsNewDesc) {
                if (!empty($aService['desc'])) {
                    $aService['service_desc'] = $aService['desc'];
                    unset($aService['desc']);
                } else {
                    $aService['service_desc'] = $this->_aConfig[$aService['id']]['service_desc'] ?? '';
                }
            }

            //获取sps的费用
            $aFeeInfo = $this->_aData[$iProductCategory][$aService['id']];
            if (empty($aFeeInfo) || !isset($aFeeInfo['discount_price'])) {
                continue;
            }

            //只有携宠出行 才会命中的逻辑
            if (!empty($aFeeInfo['multi_prices']) && $this->_isMultiPriceService($oEstimateRequest, $sPid, $aService['id'])) {
                $aServicePrices = $this->_buildMultiPrices(
                    $oEstimateRequest, $aFeeInfo['multi_prices'],
                    $aService, $sLang, $aUnit
                );
                $aService['services'] = $aServicePrices['multi_prices'] ?? null;
                continue;
            }

            $aService['price']      = $aFeeInfo['discount_price'];
            $aService['price_msg']  = sprintf('{%s}%s', $aFeeInfo['discount_price'], $aUnit['currency']);
            $aService['price_desc'] = Language::replaceTag(
                $this->_aConfig['price_desc'],
                array(
                    'discount_price' => $aFeeInfo['discount_price'],
                    'currency'       => $aUnit['currency'],
                    'unit'           => $aService['unit'],
                )
            );

            // 服务为 机场助理引导（102），儿童贴心服务（111 才会执行后续逻辑）
            $oApollo = Apollo::getInstance();
            $oToggle = $oApollo->featureToggle(
                'gs_custom_service_price_desc_switch',
                [
                    'key'        => time(),
                    'service_id' => $aService['id'],
                ]
            );

            // 如果当前服务为儿童暖心服务，且价格为0，不显示0元/次
            if ($oToggle->allow() && 0 == $aFeeInfo['fee_price'] && 0 == $aFeeInfo['discount_price']) {
                // 费用为0时，机场助理和亲子服务不显示 0元/次 文案
                $aService['price']        = '';
                $aService['unit']         = '';
                $aService['unit_price']   = '';
                $aService['origin_price'] = '';
                $aService['price_desc']   = '';
                $aService['price_msg']    = '';
                continue;
            }


            //专车偏好页的开关-主要做版本控制
            $iProductID   = \BizLib\Utils\Product::getProductIdByBusinessId($oEstimateRequest->getBusinessId());
            $oToggleResult = $oApollo->featureToggle(
                'special_car_new_setting_page',
                [
                    'key'           => time(),
                    'app_version'   => $oEstimateRequest->getAppVersion(),
                    'access_key_id' => $oEstimateRequest->getAccessKeyId(),
                    'product_id'    => $iProductID,
                    'pid'           => $sPid,
                ]
            );

            $aFeeDetailConfig = $this->_aConfig[$aService['id']]['fee_detail_template'] ?? [];

            if ($oToggleResult->allow()) {
                $sDcmpName = $oToggleResult->getParameter('dcmp_name', '');
                if ('' != $sDcmpName){
                    $aFeeDetailConfig = $this->_aConfig[$aService['id']][$sDcmpName] ?? [];
                }
            }

            // 下面计算折扣并展示
            if (empty($aFeeInfo['discount_info']) || !is_array($aFeeInfo['discount_info']) || empty($aFeeDetailConfig)) {
                continue;
            }

            //机场助理 置灰的话，不调整desc (豪华车之前没有收费，所以这里的逻辑是专车的机场助理逻辑)
            if (CustomLogic::CUSTOM_SERVICE_GUIDE_NEW == $aService['id'] && $aService['status'] != 1) {
                continue;
            }

            // todo 多个负的费用项文案,不考虑
            if (1 == sizeof($aFeeInfo['discount_info'])) {
                $aDiscount = $aFeeInfo['discount_info'][0];
                if (!empty($aFeeDetailConfig[$aDiscount['fee_name']])) {
                    $aService['desc'] = Language::replaceTag(
                        $aFeeDetailConfig[$aDiscount['fee_name']]['price_info_desc'],
                        [
                            'fee_price' => $aFeeInfo['fee_price'],
                            'amount'    => $aDiscount['fee_price'],
                            'currency'  => $aUnit['currency'],
                            'unit'      => $aService['unit'],
                        ]
                    );
                    $aService['tag']  = $aFeeDetailConfig[$aDiscount['fee_name']]['price_info_tag'] ?? '';
                }
            }

            //不同的个性化服务
        }

    }

    /**
     * 多价格定制服务
     * @param LuxRequest $oEstimateRequest $oEstimateRequest
     * @param array      $aSpsPrices       $aSpsPrices
     * @param array      $aService         $aService
     * @param string     $sLang            $sLang
     * @param array      $aUnit            $aUnit
     * @return array
     */
    private function _buildMultiPrices(LuxRequest $oEstimateRequest, $aSpsPrices, $aService, $sLang, $aUnit): array {
        $oConfRes     = Apollo::getInstance()->getConfigResult('custom_service_new', 'service_id_2_service_data');
        $aTemplateRes = $oConfRes->getConfig("service_prices_template_{$aService['id']}_$sLang");
        if ($aTemplateRes[0] && !empty($aTemplateRes[1])) {
            $aTemplate = $aTemplateRes[1];
        } else {
            return [];
        }

        // 用户已勾选的服务
        $sCustomFeatures = $oEstimateRequest->getCustomFeature();
        $aCustomFeatures = !empty($sCustomFeatures) ? json_decode($sCustomFeatures, true) : [];
        $aSelectedMap    = array_column($aCustomFeatures, null, 'service_type');

        // sps返回的价格
        $aPriceMap = array_column($aSpsPrices, null, 'service_type');

        // dcmp配置的文案
        $aFeeDescConf = $this->_aConfig[$aService['id']]['fee_detail_template_new'] ?? [];

        $aValidPrices = [];
        foreach ($aTemplate['multi_prices'] as &$aPriceTemp) {
            if (empty($aPriceMap[$aPriceTemp['service_type']])) {
                continue;
            }

            // 设置价格文案
            $aSpsPrice                = $aPriceMap[$aPriceTemp['service_type']];
            $aPriceTemp['price']      = $aSpsPrice['discount_price'];
            $aPriceTemp['price_msg']  = Language::replaceTag(
                $aPriceTemp['price_msg'], ['price' => $aSpsPrice['discount_price']]
            );
            $aPriceTemp['price_desc'] = Language::replaceTag(
                $aPriceTemp['price_desc'], ['price' => $aSpsPrice['discount_price']]
            );

            // 设置选中状态
            if (!empty($aSelectedMap) && key_exists($aPriceTemp['service_type'], $aSelectedMap)) {
                $aPriceTemp['selected_count'] = (int)($aSelectedMap[$aPriceTemp['service_type']]['count']);
            }

            // 设置折扣信息
            if (!empty($aSpsPrice['discount_info'])) {
                $sDiscountName = '';
                $fDiscount = 0;
                foreach ($aSpsPrice['discount_info'] as $aDiscountInfo) {
                    $fDiscount += $aDiscountInfo['fee_price'];
                    $sDiscountName = $aDiscountInfo['fee_name'];
                }

                if (sizeof($aSpsPrice['discount_info']) > 1) {
                    $aPriceTemp['tag'] = $this->_aConfig['discount_info_tag'] ?? '';
                } else {
                    $aPriceTemp['tag'] = $aFeeDescConf[$sDiscountName]['price_info_tag'] ?? '';
                }

                $aPriceTemp['desc'] = Language::replaceTag(
                    $aFeeDescConf[$sDiscountName]['price_info_desc'],
                    [
                        'amount'   => sprintf('%.1f', $fDiscount),
                        'currency' => $aUnit['currency'],
                    ]
                );
            }

            $aValidPrices[] = $aPriceTemp;
        }

        $aTemplate['multi_prices'] = $aValidPrices;
        return $aTemplate;
    }

    /**
     * 新版的desc字段仅描述折扣信息
     * @param LuxRequest $oEstimateRequest $oEstimateRequest
     * @param int        $sPid             pid
     * @return bool
     */
    private function _isNewVersionDesc($oEstimateRequest, $sPid) {
        return Apollo::getInstance()->featureToggle(
            'personalized_page_desc_new_version_toggle',
            [
                'key'              => $sPid,
                'app_version'      => $oEstimateRequest->getAppVersion(),
                'access_key_id'    => $oEstimateRequest->getAccessKeyId(),
                'business_id'      => $oEstimateRequest->getBusinessId(),
                'product_category' => $oEstimateRequest->getProductCategory(),
                'page_type'        => $oEstimateRequest->getPageType(),
            ]
        )->allow();
    }

    /**
     * 新版的desc字段仅描述折扣信息
     * @param LuxRequest $oEstimateRequest $oEstimateRequest
     * @param int        $sPid             pid
     * @param int        $iServiceID       serviceID
     * @return bool
     */
    private function _isMultiPriceService($oEstimateRequest, $sPid, $iServiceID) {
        return Apollo::getInstance()->featureToggle(
            'full_page_service_multi_price_toggle',
            [
                'key'              => $sPid,
                'pid'              => $sPid,
                'app_version'      => $oEstimateRequest->getAppVersion(),
                'access_key_id'    => $oEstimateRequest->getAccessKeyId(),
                'product_category' => $oEstimateRequest->getProductCategory(),
                'service_id'       => $iServiceID,
            ]
        )->allow();
    }

    /**
     * 构建个性化增值服务价格相关信息
     * @param array $aServiceList     $aServiceList
     * @param int   $iProductCategory $iProductCategory
     * @return void
     * @throws \Exception e
     */
    public function addSsseCustomServicePrice(&$aServiceList, $iProductCategory) {
        if (empty($this->_aConfig)) {
            return;
        }

        $sDefaultLanguage = Language::getDefaultLanguage();
        $aUnit            = Language::getUnit($sDefaultLanguage);

        foreach ($aServiceList as &$aService) {
            $aFeeInfo = $this->_aData[$iProductCategory][$aService['id']];
            if (empty($aFeeInfo) || !isset($aFeeInfo['discount_price'])) {
                continue;
            }

            $oApollo = Apollo::getInstance();
            $oToggle = $oApollo->featureToggle(
                'gs_custom_service_mod_price_desc_switch',
                [
                    'key'        => time(),
                    'service_id' => $aService['service_id'],
                ]
            );
            if ($oToggle->allow() && 0 == $aFeeInfo['fee_price'] && 0 == $aFeeInfo['discount_price']) {
                // 费用为0时，机场助理和亲子服务不显示 0元/次 文案
                $aService['price']        = '';
                $aService['unit']         = '';
                $aService['unit_price']   = '';
                $aService['origin_price'] = '';
                continue;
            }

            $aService['price']      = sprintf('{%s}%s', $aFeeInfo['discount_price'] * ($aService['selected_count'] ?: 1), $aUnit['currency']);
            $aService['unit_price'] = sprintf('{%s}%s', $aFeeInfo['discount_price'], $aUnit['currency']);
            $aService['desc']       = $aService['desc'] ? $this->_getDesc($aService['desc']) : '';

            // 下面计算折扣并展示
            $aContentConfig = $this->_aConfig[$aService['id']];
            if (empty($aFeeInfo['discount_info']) || !is_array($aFeeInfo['discount_info']) || empty($aContentConfig)) {
                continue;
            }

            if (sizeof($aFeeInfo['discount_info']) > 1) {
                $iSum = 0;
                foreach ($aFeeInfo['discount_info'] as $aDiscount) {
                    $iSum += $aDiscount['fee_price'];
                }

                $aService['unit'] = Language::replaceTag(
                    $aContentConfig['price_desc']['price_desc_common'],
                    [
                        'amount'   => min($iSum, $aFeeInfo['discount_price']),
                        'currency' => $aUnit['currency'],
                    ]
                );
            } else {
                $sContent  = '';
                $aDiscount = $aFeeInfo['discount_info'][0];

                switch ($aDiscount['fee_name']) {
                    case 'sps_pick_up_guide_free':
                        $sContent = $aContentConfig['price_desc']['sps_pick_up_guide_free'];
                        break;
                    case 'sps_pick_up_guide_discount':
                        $sContent = $aContentConfig['price_desc']['sps_pick_up_guide_discount'];
                        break;
                    default:
                }

                $iAmount      = $aDiscount['fee_price']; // 抵扣金额
                $iOriginPrice = $aFeeInfo['fee_price']; // 服务原价

                // 已抵扣xx元
                $aService['unit'] = Language::replaceTag(
                    $sContent,
                    [
                        'fee_price' => $iOriginPrice,
                        'amount'    => min($iAmount, $aFeeInfo['fee_price']),
                        'currency'  => $aUnit['currency'],
                    ]
                );
            }
        }
    }

    /**
     * @param string $sDesc $sDesc
     * @return string
     */
    private function _getDesc($sDesc) {
        $len        = mb_strlen($sDesc, 'UTF-8');
        $bHasNumber = false;
        $aDesc      = [];
        for ($iIde = 0; $iIde < $len; $iIde++) {
            $aDesc[] = mb_substr($sDesc, $iIde, 1, 'UTF-8');
        }

        foreach ($aDesc as $iIndex => &$sItem) {
            if (is_numeric($sItem) || '.' == $sItem) {
                if (!$bHasNumber) {
                    $sItem      = '{'.$sItem;
                    $bHasNumber = true;
                }
            } else {
                if ($bHasNumber) {
                    $sItem      = '}'.$sItem;
                    $bHasNumber = false;
                }
            }

            if (count($aDesc) - 1 == $iIndex && $bHasNumber) {
                $sItem = $sItem.'}';
            }
        }

        return implode('', $aDesc);
    }

    /**
     * 请求sps获取服务价格
     * @return array
     */
    public function getSpsServiceData(){
        return $this->_aData;
    }

    /**
     * 判断是否需要进行默认勾选
     * @param  LuxRequest $oEstimateRequest $oEstimateRequest
     * @param  array      $params           $params
     * @param  array      $aService         $aService
     * @return bool
     */
    private function _defaultCheckGuideNew($oEstimateRequest, $params,$aService): bool {
        if ($aService['service_fee_info']['discount_price'] != 0) {
            return false;
        }
        // 用户首次进入偏好页则进行默认勾选
        $sLuxrySelectCarlevels = $oEstimateRequest->getLuxurySelectCarlevels();
        if (empty($sLuxrySelectCarlevels) || $sLuxrySelectCarlevels == "[]") {
            return true;
        }
        // 用户主动取消过勾选 则不进行系统默认勾选，否则进行默认勾选
        if (AdditionalServiceLogic::getInstance()->isSaveToUfsCustomFeatureVersion($params)) {
            if (AdditionalServiceLogic::getInstance()->isUserActiveCancel(CustomLogic::CUSTOM_SERVICE_GUIDE_NEW)) {
                return false;
            } else  {
                return true;
            }
        }
        return false;
    }
}
