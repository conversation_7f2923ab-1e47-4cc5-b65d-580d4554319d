<?php
/**
 * 短时预约.
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2020/10/31
 */

namespace PreSale\Logics\estimatePriceV2;

use BizCommon\Utils\Product;

/**
 * Class ShortBookLogic
 * @package PreSale\Logics\estimatePriceV2
 */
class ShortBookLogic
{
    private static $_oInstance = null;

    private $_bHasShortBook = false;

    private $_aShortBookInfo = [];
    private $_aNormalInfo    = [];

    /**
     *
     * @return null|ShortBookLogic
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * @param array $aInfos $aInfos
     *
     * @return  void
     */
    public function loadInfo($aInfos) {
        foreach ($aInfos as $aInfo) {
            if (Product::isKFlowerByProductID($aInfo['order_info']['product_id'])) {
                $this->_buildShortBookInfo($aInfo);
                break;
            }
        }
    }

    /**
     *
     * @return bool
     */
    public function hasShortBook() {
        return $this->_bHasShortBook;
    }

    /**
     *
     * @return array
     */
    public function getShortBookInfo() {
        return $this->_aShortBookInfo;
    }

    /**
     *
     * @return array
     */
    public function getNormalInfo() {
        return $this->_aNormalInfo;
    }

    /**
     * @param array $aInfo $aInfo
     *
     * @return void
     */
    private function _buildShortBookInfo($aInfo) {
        if (empty($aInfo['activity_info'][0]['short_book_summary'])) {
            $this->_bHasShortBook = false;
            return;
        }

        $aSummary = json_decode($aInfo['activity_info'][0]['short_book_summary'], true);

        $aShortBookInfo = [
            'estimate_fee'    => $aSummary['short']['estimate_fee'],
            'estimate_detail' => $aSummary['short']['estimate_detail'],
            'discount_desc'   => $aSummary['short']['discount_desc'],
            'short_wait'      => $aSummary['short']['short_wait'],
        ];

        $aNormalInfo = [
            'estimate_fee'    => $aSummary['not_short']['estimate_fee'],
            'estimate_detail' => $aSummary['not_short']['estimate_detail'],
            'discount_desc'   => $aSummary['not_short']['discount_desc'],
            'short_wait'      => $aSummary['not_short']['short_wait'],
        ];

        $this->_aShortBookInfo = $aShortBookInfo;
        $this->_aNormalInfo    = $aNormalInfo;
        $this->_bHasShortBook  = true;
    }
}
