<?php
namespace PreSale\Logics\estimatePriceV2;

use B<PERSON><PERSON>ib\Client\PandoraClient;
use BizCom<PERSON>\Utils\Horae as BizHorae;
use BizLib\Client\VcardClient;
use BizLib\Utils\NumberHelper;
use BizLib\Log as NuwaLog;
use BizLib\ErrCode\Msg as ErrMsg;
use BizLib\ErrCode\Code as ErrCode;
use BizLib\Utils\Product;

use BizLib\Utils\ProductCategory;
use Xiaoju\Apollo\Apollo;
use Xiaoju\Apollo\ApolloConstant;

/**
 * CarpoolCommuteCardLogic
 * 处理拼车省钱卡
 */
class CarpoolCommuteCardLogic
{
    protected static $oInstance = null;

    /**
     * @var PandoraClient
     * @deprecated 不在走潘多拉链路
     */
    protected $oClient;

    protected $aCardRelate = [];

    protected $oVCardClient;

    protected $aCardData = [];

    /**
     * 构造
     * @return CarpoolCommuteCardLogic
     */
    private function __construct() {
        $this->oClient      = new PandoraClient();
        $this->oVCardClient = new VcardClient();
    }

    /**
     * @return CarpoolCommuteCardLogic
     */
    public static function getInstance() {
        if (empty(self::$oInstance) || !self::$oInstance instanceof self) {
            self::$oInstance = new self();
        }

        return self::$oInstance;
    }

    // 注释-待删
//    /**
//     * loadCommuteCardRelated
//     * 加载相关信息并暂存
//     * @param array   $aInfos  $aInfos
//     * @param boolean $bAnycar 是否是anycar
//     * @return void
//     * @deprecated 无用
//     */
//    public function loadCommuteCardRelated($aInfos, $bAnycar) {
//        if (!$this->_hit($aInfos, $bAnycar)) {
//            return;
//        }
//
//        $aRequest = [
//            'passenger_id'  => 0,
//            'app_version'   => '',
//            'access_key_id' => '',
//            'menu_id'       => '',
//            'page_type'     => '',
//            'scene_price'   => [],
//        ];
//        foreach ($aInfos as $aInfo) {
//            if (empty($aInfo)) {
//                continue;
//            }
//
//            // 重复赋值, 不关键
//            $aRequest['passenger_id']  = $aInfo['passenger_info']['pid'];
//            $aRequest['app_version']   = $aInfo['common_info']['app_version'];
//            $aRequest['access_key_id'] = $aInfo['common_info']['access_key_id'];
//            $aRequest['menu_id']       = $aInfo['order_info']['menu_id'];
//            $aRequest['page_type']     = $aInfo['common_info']['page_type'];
//            $sEid    = $aInfo['bill_info']['estimate_id'];
//            $aNTuple = $aInfo['order_info']['n_tuple'];
//
//            // 2021-7-30 目前看, 只有拼车两口价有
//            if (BizHorae::isCarpoolUnSuccessFlatPrice($aNTuple) && 2 == count($aInfo['activity_info'])) {
//                if (empty($aRequest['scene_price'][$sEid])) {
//                    $aRequest['scene_price'][$sEid] = [];
//                }
//
//                // array_map ??
//                $aRequest['scene_price'][$sEid][] = [
//                    'estimate_id'        => $sEid,
//                    'seat_num'           => $aInfo['order_info']['carpool_seat_num'],
//                    'pool_num'           => 0,
//                    'is_carpool_success' => true,
//                    'carpool_price_type' => $aNTuple['carpool_price_type'],
//                    'estimate_price'     => $this->floatToHundred($aInfo['activity_info'][0]['estimate_fee']),// 分
//
//                ];
//
//                $aRequest['scene_price'][$sEid][] = [
//                    'estimate_id'        => $sEid,
//                    'seat_num'           => $aInfo['order_info']['carpool_seat_num'],
//                    'pool_num'           => 0,
//                    'is_carpool_success' => false,
//                    'carpool_price_type' => $aNTuple['carpool_price_type'],
//                    'estimate_price'     => $this->floatToHundred($aInfo['activity_info'][1]['estimate_fee']),// 分
//
//                ];
//            } elseif (Product::isAnyCar($aInfo['order_info']['product_id']) && $bAnycar) {
//                $aBillInfo = $aInfo['bill_info']['multi_info'];
//                foreach ($aBillInfo as $aBillItem) {
//                    if (BizHorae::isCarpoolUnSuccessFlatPrice($aBillItem)) {
//                        $sEid = $aBillItem['estimate_id'];
//                        if (empty($aRequest['scene_price'][$sEid])) {
//                            $aRequest['scene_price'][$sEid] = [];
//                        }
//
//                        // array_map
//                        array_push($aRequest['scene_price'][$sEid], $this->_generateAnycarCarpoolInfo($aInfo, true, $aBillItem));
//                        array_push($aRequest['scene_price'][$sEid], $this->_generateAnycarCarpoolInfo($aInfo, false, $aBillItem));
//                    }
//                }
//            }
//        }
//
//        // empty(['a' => []]) == false
//        if (empty($aRequest['scene_price'])) {
//            NuwaLog::info('没有拼车裂变场景, 不需要调用Pandora');
//            return;
//        }
//
//        $aResp = $this->oClient->GetUserEffectiveCard($aRequest);
//        if (empty($aResp) || $aResp['errno'] ?? -1 != ErrCode::E_SUCCESS) {
//            NuwaLog::warning(
//                ErrMsg::formatArray(
//                    $aResp['errno'] ?? -1,
//                    [
//                        'errno' => $aResp['errno'] ?? -1,
//                        'msg'   => $aResp['errmsg'] ?? '',
//                    ]
//                )
//            );
//            return;
//        }
//
//        $this->aCardRelate = $aResp['data'];
//
//    }

    /**
     * 元转分
     * @param float $fEstimatePrice $fEstimatePrice
     * @return string
     */
    protected function floatToHundred($fEstimatePrice) {
        return NumberHelper::numberFormatDisplay($fEstimatePrice, '', 2) * 100;
    }

    // 注释-待删
//    /**
//     * 根据预估id获取对应的用卡信息
//     * @param string $sEid 预估id
//     * @return array
//     */
//    public function getExtInfoByEstimateID($sEid) {
//        return $this->aCardRelate[$sEid] ?? [];
//    }

// 注释-待删
//    /**
//     * @param array $aInfo              info
//     * @param bool  $bIsCarpoolSuccess  is_carpool_success
//     * @param array $aMultiBillInfoItem multi_bill_Info
//     * @return array
//     */
//    private function _generateAnycarCarpoolInfo($aInfo, $bIsCarpoolSuccess, $aMultiBillInfoItem) {
//        if ($bIsCarpoolSuccess) {
//            $fEstimatePrice = $this->floatToHundred($aMultiBillInfoItem['discount_fee']);
//        } else {
//            $fEstimatePrice = $this->floatToHundred($aMultiBillInfoItem['carpool_fail_discount_fee']);
//        }
//
//        return [
//            'estimate_id'        => $aInfo['bill_info']['estimate_id'],
//            'seat_num'           => $aInfo['order_info']['carpool_seat_num'],
//            'pool_num'           => 0,
//            'is_carpool_success' => $bIsCarpoolSuccess,
//            'carpool_price_type' => $aMultiBillInfoItem['carpool_price_type'],
//            'estimate_price'     => $fEstimatePrice,// 分
//        ];
//
//    }

    // 注释-待删
//    /**
//     * 功能开关
//     * @param array   $aInfos  $aInfos
//     * @param boolean $bAnycar 是否是anycar
//     * @return boolean
//     */
//    private function _hit($aInfos, $bAnycar) {
//        $aInfo = null;
//        foreach ($aInfos as $aX) {
//            if (!empty($aX)) {
//                $aInfo = $aX;
//                break;
//            }
//        }
//
//        if (empty($aInfo)) {
//            return false;
//        }
//
//        $aParam        = [
//            ApolloConstant::APOLLO_INDIVIDUAL_ID => $aInfo['passenger_info']['pid'] ?? '0',
//            'city'                               => $aInfo['order_info']['area'],
//            'pid'                                => $aInfo['passenger_info']['pid'],
//            'app_version'                        => $aInfo['common_info']['app_version'],
//            'access_key_id'                      => $aInfo['common_info']['access_key_id'],
//            'menu_id'                            => $aInfo['order_info']['menu_id'],
//            'page_type'                          => $aInfo['common_info']['page_type'],
//            'is_anycar_est'                      => ($bAnycar ? '1' : '0'),
//        ];
//        $oApolloToggle = (Apollo::getInstance())->featureToggle('gs_carpool_commute_card_open', $aParam);
//        return $oApolloToggle->allow();
//    }

    /**
     * loadCommuteCardRelatedByVCard
     * 加载相关信息并暂存
     * @param array $aInfos $aInfos
     * @return void
     */
    public function loadCommuteCardRelatedByVCard($aInfos) {
        $aRequest    = [];
        $sEstimateId = '';
        foreach ($aInfos as $aInfo) {
            if (empty($aInfo)) {
                continue;
            }

            // 2021-7-30 目前看, 只有拼车两口价有
            $aNTuple = $aInfo['order_info']['n_tuple'];
            if (BizHorae::isCarpoolUnSuccessFlatPrice($aNTuple) && 2 == count($aInfo['activity_info'])) {
                $sEstimateId = $aInfo['bill_info']['estimate_id'];
                $aRequest    = ['estimate_id' => $sEstimateId];
                break;
            }
        }

        if (empty($aRequest)) {
            NuwaLog::debug('没有拼车裂变场景, 不需要调用Pandora');
            return;
        }

        $aResp = $this->oVCardClient->getMatchInfo($aRequest);
        if (empty($aResp) || ErrCode::E_SUCCESS != $aResp['errno']) {
            NuwaLog::warning(
                ErrMsg::formatArray(
                    $aResp['errno'] ?? -1,
                    [
                        'errno' => $aResp['errno'] ?? -1,
                        'msg'   => $aResp['errmsg'] ?? '',
                    ]
                )
            );
        }

        if (empty($sEstimateId)) {
            $this->aCardData = [];
        } else {
            $this->aCardData[$sEstimateId] = $aResp['data'] ?: [];
        }
    }
    /**
     * 根据预估id获取对应的用卡信息
     * @param string $sEstimateId 预估id
     * @return bool
     */
    public function isHaveCardEstimateID($sEstimateId) {
        if (empty($this->aCardData[$sEstimateId])) {
            return false;
        }

        if (!empty($this->aCardData[$sEstimateId]['send_card'])
            ||!empty($this->aCardData[$sEstimateId]['usable_card'])
        ) {
            return true;
        }

        return false;
    }

    /**
     * 获取省钱卡
     * @param string $sEstimateId 预估id
     * @return array [卡来源, 卡支付状态]
     */
    public function getVCardInfoByEstimateID($sEstimateId) {

        if (!empty($this->aCardData[$sEstimateId]['send_card'])) {
            return ['send', 0];
        }

        if (!empty($this->aCardData[$sEstimateId]['usable_card'])) {
            // 买赠卡
            if (3 == $this->aCardData[$sEstimateId]['usable_card']['card_type']) {
                return ['usable_give',$this->aCardData[$sEstimateId]['usable_card']['pay_status']];
            }

            return ['usable',$this->aCardData[$sEstimateId]['usable_card']['pay_status']];
        }

        return [];
    }
    /**
     * 获取省钱卡赠卡的用户特征
     * @param string $sEstimateId 预估id
     * @return bool $bStatus 0 兜底渲染 1新用户 2流失用户
     */
    public function getSendCardFeatureByEstimateID($sEstimateId) {

        if (empty($this->aCardData[$sEstimateId]['send_card']) || empty($this->aCardData[$sEstimateId]['send_card']['user_feature'])) {
            return 0;
        }

        $aUserFeature = $this->aCardData[$sEstimateId]['send_card']['user_feature'];

        if ($aUserFeature['is_dual_price_loss']) {
            return 2;
        } elseif ($aUserFeature['is_dual_price_new']) {
            return 1;
        }

        return 0;
    }
}
