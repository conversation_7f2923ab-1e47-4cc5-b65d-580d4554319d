<?php

/**
 * kf调价公示
 * wiki: http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=443121341
 * <AUTHOR> <<EMAIL>>
 * @date 2020/10/14
 */

namespace PreSale\Logics\priceAdjustment;

use BizLib\Client\BillClient;
use BizLib\Utils\MapHelper;
use BizLib\Utils\Product;
use BizLib\Exception\ExceptionWithResp;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\RespCode;
use Nuwa\ApolloSDK\Apollo;

/**
 * Class PriceAdjustmentLogic
 * @package PreSale\Logics\priceAdjustment
 */
class PriceAdjustmentLogic
{
    const KFLOWER_PRICE_RULE_NS     = 'kf_price_rule_show';
    const KFLOWER_PRICE_RULE_CONFIG = 'kf_price_rule_show_passenger';
    /**
     * 计价token.
     */
    private $_sPriceToken;

    /**
     * 用户token.
     */
    private $_sToken;

    /**
     * 语言标识.
     */
    private $_sLang;

    /**
     * jsonp callback.
     */
    private $_sCallback;

    /**
     * 城市ID
     */
    private $_sCityId;

    /**
     * 二级区县ID
     */
    private $_sCountyId;

    /**
     * 出发地国际码
     */
    private $_sTripCountry;

    /**
     * 出发点经度.
     */
    private $_fLng;

    /**
     * 出发点纬度.
     */
    private $_fLat;

    /**
     * 公共产品线id
     */
    private $_iBusinessId;

    /**
     * @access_key_id
     */
    private $_iAccessKeyId;

    private $_iRequireLevel;

    private $_iComboType;

    private $_iRuleShowType;

    private $_iBeginTime;

    const PRICE_RULE_DETAIL_TYPE = 0;//计价规则简单展示
    const PRICE_RULE_SIMPLE_TYPE = 1;//计价规则详细展示

    /**
     * PriceAdjustmentLogic constructor.
     *
     * @param array $aRequest request
     */
    public function __construct($aRequest) {
        $this->_sPriceToken   = $aRequest['price_token'];
        $this->_sToken        = $aRequest['token'];
        $this->_sLang         = $aRequest['lang'];
        $this->_sCallback     = htmlspecialchars($aRequest['callback']);
        $aAreaInfo            = MapHelper::getAreaInfoByLoc($aRequest['flng'], $aRequest['flat']);
        $this->_sTripCountry  = $aAreaInfo['canonical_country_code'];
        $this->_fLng          = $aRequest['flng'];
        $this->_fLat          = $aRequest['flat'];
        $this->_sCityId       = $aRequest['city_id'] ?? '0';
        $this->_sCountyId     = $aRequest['county_id'] ?? '0';
        $this->_iBusinessId   = $aRequest['business_id'] ?? 0;
        $this->_iAccessKeyId  = $aRequest['access_key_id'] ?? 0;
        $this->_iRequireLevel = $aRequest['car_level'] ?? 0;
        $this->_iComboType    = $aRequest['combo_type'] ?? 0;
        $this->_iRuleShowType = (int)($aRequest['rule_show_type']) ?? self::PRICE_RULE_DETAIL_TYPE;
        $this->_iBeginTime    = (int)$aRequest['begin_time'];
    }

    /**
     * @return array
     * @throws ExceptionWithResp excepton
     */
    public function verify() {
        //token此处是可选参数，如果token不为空，验证token是否合法
        if (!empty($this->_sToken)) {
            $aPassenger = (\BizCommon\Models\Passenger\Passenger::getInstance())->getPassengerByToken($this->_sToken);
            $sPid       = $aPassenger['pid'] ?? 0;
            $sPhone     = $aPassenger['phone'] ?? 0;

            if (empty($sPid)) {
                throw new ExceptionWithResp(
                    Code::E_COMMON_TOKEN_INVALID,
                    RespCode::P_PARAMS_ERROR,
                    '',
                    [
                        'exceptionMsg' => [
                            'msg'      => 'pid is empty (token is invalid)',
                            'moreInfo' => [
                                'args' => (array) $this,
                                'pid'  => $sPid,
                            ],
                        ],
                    ]
                );
            }
        }

        //获取product_id
        $iProductId = Product::getProductIdByBusinessId($this->_iBusinessId);

        if (empty($iProductId) || empty($this->_iRequireLevel)) {
            throw new ExceptionWithResp(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                RespCode::P_PARAMS_ERROR,
                '',
                [
                    'exceptionMsg' => [
                        'msg'      => 'product params is empty',
                        'moreInfo' => [
                            'args'          => (array) $this,
                            'product_id'    => $iProductId,
                            'require_level' => $this->_iRequireLevel,
                        ],
                    ],
                ]
            );
        }

        if (empty($this->_iRuleShowType)) {
            $this->_iRuleShowType = self::PRICE_RULE_DETAIL_TYPE;
        }

        if (!in_array($this->_iRuleShowType, [self::PRICE_RULE_DETAIL_TYPE, self::PRICE_RULE_SIMPLE_TYPE])) {
            throw new ExceptionWithResp(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                RespCode::P_PARAMS_ERROR,
                '',
                [
                    'exceptionMsg' => [
                        'msg'      => 'rule_show_type error',
                        'moreInfo' => [
                            'args' => (array)$this,
                        ],
                    ],
                ]
            );
        }

        //如果经纬度不为空，根据地经纬度获取区域信息，否走用传入的城市ID和区县ID，再否则抛异常
        if (!empty($this->_fLng) && !empty($this->_fLat)) {
            $aAreaInfo = MapHelper::getAreaInfoByLoc($this->_fLng, $this->_fLat);
            $sDistrict = $aAreaInfo['district'];
            if (empty($sDistrict)) {
                throw new ExceptionWithResp(
                    Code::E_COMMON_PARAM_INVALID_VALUE,
                    RespCode::P_PARAMS_ERROR,
                    '',
                    [
                        'exceptionMsg' => [
                            'msg'      => 'district id is empty',
                            'moreInfo' => [
                                'args' => (array)$this,
                            ],
                        ],
                    ]
                );
            }
        } elseif (!empty($this->_sCityId)) {
            $aAreaInfo = MapHelper::getAreaInfoByAreaId($this->_sCityId);
            $aAreaInfo['countyid'] = $this->_sCountyId;
        } else {
            $aAreaInfo = array(
                'id'       => -1,
                'countyid' => -1,
                'district' => -1,
            );
        }

        //校验生效时间戳
        if (empty($this->_iBeginTime)) {
            throw new ExceptionWithResp(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                RespCode::P_PARAMS_ERROR,
                '',
                [
                    'exceptionMsg' => [
                        'msg'      => 'begin_time error',
                        'moreInfo' => [
                            'args'       => (array) $this,
                            'begin_time' => $this->_iBeginTime,
                        ],
                    ],
                ]
            );
        }

        return array($iProductId, $aAreaInfo);
    }

    /**获取调价公示
     * @param  int   $iProductId 产品id
     * @param  array $aAreaInfo  位置信息
     * @return array
     * @throws \Exception exception
     */
    public function getPriceAdjustment($iProductId, $aAreaInfo) {
        $aOrderInfo      = $this->_buildOrderInfo();
        $aPassengerParam = $this->_buildPassengerParam($iProductId, $aAreaInfo);

        //调用账单服务
        $oBill      = new BillClient();
        $aParams    = [
            'price_token'     => '',
            'lang'            => $this->_sLang,
            'order_info'      => $aOrderInfo,
            'district'        => $aAreaInfo['district'],
            'countyid'        => (isset($aAreaInfo['countyid']) && !empty($aAreaInfo['countyid'])) ? (string)($aAreaInfo['district'].','.$aAreaInfo['countyid']) : '',
            'passenger_param' => $aPassengerParam,
            'role'            => 2,
            'rule_show_type'  => $this->_iRuleShowType,
        ];
        $aPriceRule = $oBill->getPriceStrategy($aParams);
        if (0 !== $aPriceRule['errno']) {
            throw new \Exception('Call ' . __METHOD__ . '() failed.', Code::E_COMMON_HTTP_READ_FAIL);
        }

        if (empty($aPriceRule['result']['data'])) {
            return [];
        }

        $configResult    = Apollo::getInstance()->getConfigResult(
            self::KFLOWER_PRICE_RULE_NS,
            self::KFLOWER_PRICE_RULE_CONFIG
        );
        list($ok, $aRet) = $configResult->getAllConfig();
        if (!$ok || empty($aRet)) {
            return [];
        }

        $aPriceRule['result']['data']['public_title']      = $aRet['public_title'];
        $aPriceRule['result']['data']['public_content']    = $aRet['public_content'];
        $aPriceRule['result']['data']['public_content'][0] = sprintf($aRet['public_content'][0], date('Y', $this->_iBeginTime), date('n', $this->_iBeginTime), date('j', $this->_iBeginTime));

        return $aPriceRule['result']['data'];
    }

    /**
     * @return string
     */
    public function getCallback() {
        return $this->_sCallback;
    }

    /**
     * @return array
     */
    private function _buildOrderInfo() {
        $sStartingLng = !empty($this->_fLng) ? (string)($this->_fLng) : '0';
        $sStartingLat = !empty($this->_fLat) ? (string)($this->_fLat) : '0';
        $aOrderInfo   = [
            'departure_time' => date('Y-m-d H:i:s', $this->_iBeginTime),
            'trip_country'   => $this->_sTripCountry,
            'starting_lng'   => $sStartingLng,
            'starting_lat'   => $sStartingLat,
        ];

        return $aOrderInfo;
    }

    /**
     * @param int   $iProductId 产品id
     * @param array $aAreaInfo  位置信息
     * @return array
     */
    private function _buildPassengerParam($iProductId, $aAreaInfo) {
        $aSearchParam    = [
            'combo_type'        => (string)($this->_iComboType),
            'car_level'         => (string)($this->_iRequireLevel),
            'district'          => $aAreaInfo['district'],
            'abstract_district' => (isset($aAreaInfo['countyid']) && !empty($aAreaInfo['countyid'])) ? (string)($aAreaInfo['district'].','.$aAreaInfo['countyid']) : ''
        ];
        $aPassengerParam = [
            'product_id'   => $iProductId,
            'search_param' => $aSearchParam,
        ];

        return $aPassengerParam;
    }
}
