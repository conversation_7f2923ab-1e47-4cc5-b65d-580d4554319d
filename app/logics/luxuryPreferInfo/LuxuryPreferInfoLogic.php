<?php

namespace PreSale\Logics\luxuryPreferInfo;

use PreSale\Logics\estimatePrice\OptionServiceLogic;
use Nuwa\ApolloSDK\Apollo;

/**
 * Created by PhpStorm.
 * User: didi
 * Date: 2023/5/31
 * Time: 18:30 PM
 */
class LuxuryPreferInfoLogic
{
    /**
     * @param array $aProductList aProductList
     * @return array
     */
    public function getLuxuryPreferResponse($aProductList) {
        if (empty($aProductList)) {
            return [];
        }

        $aReqParams  = array(
            'pid'           => $aProductList[0]->oPassengerInfo->iPid,
            'lang'          => $aProductList[0]->oCommonInfo->sLang,
            'from_kind'     => OptionServiceLogic::FORM_KIND_GET,
            'business_id'   => $aProductList[0]->oOrderInfo->iBusinessId,
            'uid'           => $aProductList[0]->oPassengerInfo->iUid,
            'access_key_id' => $aProductList[0]->oCommonInfo->iAccessKeyID,
            'app_version'   => $aProductList[0]->oCommonInfo->sAppVersion,
        );
        $aHundunData = OptionServiceLogic::getOptionService($aReqParams);
        // prefer_info做过滤，配一个apollo来过滤
        $aFilteredPreferInfo = self::_filterOptionService($aHundunData['prefer_info'], $aReqParams);
        $aHundunData['prefer_info'] = $aFilteredPreferInfo;
        return $aHundunData['prefer_info'] ?? [];
    }

    /**
     * @param array $aPreferInfo $aPreferInfo
     * @param array $aReqParams  $aReqParams
     * @return array
     */
    private function _filterOptionService($aPreferInfo, $aReqParams) {
        if (empty($aPreferInfo)) {
            return [];
        }
        $aPreferOptions = array();
        // prefer_info遍历做过滤，用apollo来过滤
        foreach ($aPreferInfo['prefer_option'] as $item) {
            // apollo过滤，通过的是被过滤的，不添加。使用「通过开关的流量」作为过滤流量的原因是：维度较多，单独配置
            $oApollo = new Apollo();
            $oToggle = $oApollo->featureToggle(
                'gs_prefer_option_filter_toggle',
                [
                    'key'           => $aReqParams['pid'],
                    'pid'           => $aReqParams['pid'],
                    'uid'           => $aReqParams['uid'],
                    'lang'          => $aReqParams['lang'],
                    'access_key_id' => $aReqParams['access_key_id'],
                    'app_version'   => $aReqParams['app_version'],
                    'option_id'     => $item['id'],
                ]
            );
            if ($oToggle->allow()) {
                continue;
            }
            $aPreferOptions[] = $item;
        }
        $aPreferInfo['prefer_option'] = $aPreferOptions;

        return $aPreferInfo ?? [];
    }
}
