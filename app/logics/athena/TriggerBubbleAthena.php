<?php

namespace PreSale\Logics\athena;

use BizLib\Client\ClientTimeOutConfig;
use Dirpc\SDK\AthenaApiv3\AthenaBubbleNewReq;
use Dirpc\SDK\AthenaApiv3\AthenaEstimateProductInfo;
use Dirpc\SDK\AthenaApiv3\Client;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;
use PreSale\Logics\v3Estimate\BizProduct;
use BizLib\Log;
use Disf\SPL\Trace as SPLTrace;

class TriggerBubbleAthena
{
    use ClientTimeOutConfig;

    const MODULE_NAME = Client::MODULE_NAME;
    private $_oDirpcClient;
    private static $_oInstance = null;

    /**
     * @var BizCommonInfo
     */
    private $_oBizCommonInfo;

    /**
     * @var BizProduct[]
     */
    private $_aBizProductMap;

    /**
     * TriggerBubbleAthena constructor.
     * @param string $sServiceName name
     * @param array $aConfig config
     */
    public function __construct($sServiceName = self::MODULE_NAME, $aConfig = []) {
        $aSdkConfig = array();
        $aSdkConfig = array_merge($aSdkConfig, $aConfig);
        $this->_oDirpcClient = new Client($sServiceName, $aSdkConfig);
    }

    public static function getInstance() {
        if (self::$_oInstance == null) {
            self::$_oInstance = new self();
        }
        return self::$_oInstance;
    }

    /**
     * 调用 athena_api TriggerBubbleAthena
     * @param BizCommonInfo $oBizCommonInfo $oBizCommonInfo
     * @param BizProduct[] $aBizProductList $aBizProductList
     * @return void
     */
    public function triggerBubbleAthena(BizCommonInfo $oBizCommonInfo, $aBizProductList) {
        $aAthenaParams = [
            //commonInfo
            'client_type'   => $oBizCommonInfo->oCommonInfo->iClientType,
            'app_version'   => $oBizCommonInfo->oCommonInfo->sAppVersion,
            'lang'          => $oBizCommonInfo->oCommonInfo->sLang,
            'channel'       => $oBizCommonInfo->oCommonInfo->sChannel,
            'page_type'     => $oBizCommonInfo->oCommonInfo->iPageType,
            'menu_id'       => $oBizCommonInfo->oCommonInfo->sMenuId,
            'access_key_id' => $oBizCommonInfo->oCommonInfo->iAccessKeyID,
            'order_type'    => $oBizCommonInfo->oCommonInfo->iOrderType,
            'tab_id'        => $oBizCommonInfo->oCommonInfo->sTabId,
            'link_source'   => 'v3',

            //location info
            //'map_type'            => $oBizCommonInfo->oAreaInfo->sMapType,
            // map_type 处理和原 mq->athena_click_stream -> athena_api:TriggerBubbleAthena 链路对齐，暂时传空值
            'map_type'      => '',
            'from_area'     => $oBizCommonInfo->oAreaInfo->iArea,
            'from_lat'      => $oBizCommonInfo->oAreaInfo->fFromLat,
            'from_lng'      => $oBizCommonInfo->oAreaInfo->fFromLng,
            'from_name'     => $oBizCommonInfo->oAreaInfo->sFromName,
            'to_lat'        => $oBizCommonInfo->oAreaInfo->fToLat,
            'to_lng'        => $oBizCommonInfo->oAreaInfo->fToLng,
            'to_name'       => $oBizCommonInfo->oAreaInfo->sToName,
            'current_lng'   => $oBizCommonInfo->oAreaInfo->fCurLng,
            'current_lat'   => $oBizCommonInfo->oAreaInfo->fCurLat,

            //passenger info
            'phone'         => $oBizCommonInfo->oPassengerInfo->sPhone,
            'pid'           => $oBizCommonInfo->oPassengerInfo->iPid,
            'user_type'     => $oBizCommonInfo->oPassengerInfo->iUserType,
        ];
        $extraInfo = [
            'athena_rpc_trigger_switch' => $this->getAthenaTriggerSwitch($oBizCommonInfo),
        ];
        $aAthenaParams['extra_info'] = $extraInfo;

        $aApiAddProducts = [];
        foreach ($aBizProductList as $oBizProduct) {
            $aApiAddProducts[] = new AthenaEstimateProductInfo (
                [
                    'product_id'            => $oBizProduct->oProduct->oOrderInfo->iProductId,
                    'require_level'         => (string)$oBizProduct->oProduct->oOrderInfo->iRequireLevel,
                    'combo_type'            => $oBizProduct->oProduct->oOrderInfo->iComboType,
                    'carpool_type'          => $oBizProduct->oProduct->oOrderInfo->iCarpoolType,
                    'is_special_price'      => (bool)$oBizProduct->oProduct->oOrderInfo->iIsSpecialPrice,
                    'product_category'      => $oBizProduct->oProduct->oOrderInfo->iProductCategory,
                    'estimate_id'           => $oBizProduct->oProduct->oOrderInfo->sEstimateID,
                    'level_type'            => $oBizProduct->oProduct->oOrderInfo->iLevelType,
                    'is_dual_carpool_price' => $oBizProduct->oProduct->oOrderInfo->bIsDualCarpoolPrice,
                    'carpool_price_type'    => $oBizProduct->oProduct->oOrderInfo->iCarpoolPriceType,
                    'departure_time'        => $oBizProduct->oProduct->oOrderInfo->iDepartureTime,
                    // business_id 处理和原 mq->athena_click_stream -> athena_api:TriggerBubbleAthena 链路对齐，暂时传空值
                    //'business_id' => $oBizProduct->oProduct->oOrderInfo->iBusinessId,
                ]
            );
            if ($oBizProduct->oProduct->aMemberInfo['level_id'] ?? 0) {
                $aAthenaParams['member_level'] = $oBizProduct->oProduct->aMemberInfo['level_id'];
            }
        }
        $aAthenaParams['api_add_product'] = $aApiAddProducts;
        $req = new AthenaBubbleNewReq($aAthenaParams);
        $oAthenaApi = $this->_oDirpcClient;
        $oTrace = $this->_getTraceV3();
        $oAthenaApi->TriggerBubbleAthena($req, $oTrace);
        return;
    }

    /**
     * traceV3
     *
     * @return \Dirpc\SDK\AthenaApiv3\Trace
     */
    private function _getTraceV3() {
        return new \Dirpc\SDK\AthenaApiv3\Trace(
            [
                'trace_id'     => SPLTrace::traceId(),
                'span_id'      => SPLTrace::generateSpanId(),
                'caller'       => MODULE_NAME,
                'src_method'   => strtok($_SERVER['REQUEST_URI'], '?'),
                'hint_code'    => '' === SPLTrace::hintCode() ? null : SPLTrace::hintCode(),
                'hint_content' => SPLTrace::hintContent(),
            ]
        );
    }

    /**
     * 获取切量开关
     * @param BizCommonInfo $oBizCommonInfo $oBizCommonInfo
     * @return int
     */
    public function getAthenaTriggerSwitch(BizCommonInfo $oBizCommonInfo): int {
        static $iTriggerSwitch = null;
        if ($iTriggerSwitch === null) {
            $oApollo = new \Xiaoju\Apollo\Apollo();
            $triggerSwitch = $oApollo->featureToggle(
                'pre_sale_call_athena_trigger_switch',
                $oBizCommonInfo->getApolloParams(null)
            );
            $iTriggerSwitch = $triggerSwitch->allow() ? 1 : 0;
        }
        return $iTriggerSwitch;
    }

}
