<?php
namespace PreSale\Logics\v3Estimate;

use BizCommon\Logics\Order\FieldOrderNTuple;
use BizCommon\Models\Order\OrderStopoverPoints;
use BizLib\Client\PriceApiClient;
use BizLib\Config as NuwaConfig;
use BizLib\Constants\Common;
use BizLib\Constants\Horae;
use BizLib\ErrCode\Code;
use BizLib\Exception\ExceptionWithResp;
use BizLib\Utils\ProductCategory;
use Dirpc\SDK\Mamba\OrderType;
use Dirpc\SDK\PriceApi\EstimateNewFormData;
use Dirpc\SDK\Spruce\baseStatusData;
use Dukang\PropertyConst\Order\OrderEstimatePcId;
use Dukang\PropertyConst\Product\ProductProductId;
use PreSale\Logics\commonAbility\FenceLogic;
use PreSale\Logics\estimatePriceV2\EstimateDegradeCode;
use PreSale\Logics\scene\custom\CustomLogic;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;
use PreSale\Logics\v3Estimate\multiRequest\Product;
use TripcloudCommon\Utils\Product as TripcloudProduct;
use BizLib\Config;

/**
 * Created by PhpStorm.
 * User: didi
 * Date: 2020/2/11
 * Time: 2:27 PM
 */
class PriceLogic
{
    const DEGRADE_OPEN = true; //默认导流不降级
    /**
     * 支付类型.
     */
    const PAY_TYPE_NEW      = 2;
    const PAY_TYPE_BR       = 1;
    const IS_INTERCITY_SURPRISE_ALONE = '1';

//    const SHAKE_APP_VERAION = '5.2.32';

    private $_aAthenaExtra; //Athena Response.extra_info
    private $_aAthenaAnycarGuide; //Athena Response.dache_anycar_guide_result

//    // 6.0的小程序、主端，当请求品类中同时有网开台、和开放平台订单
//    private $_bPlutusDegradeAbility = false;

    /**
     * @var BizProduct[]
     */
    private $_aBizProductMap;

    /**
     * @var BizCommonInfo
     */
    private $_oBizCommonInfo;

    //需要被merge的导流EID MAP
    /**
     * @var map[guide_mock_eid]=origin_eid
     */
    private $_aGuideEstimateID;

    /**
     * @var bool
     */
    private $_bIsCheatingTraffic;
    private $_oBusInfo;


    /**
     * PriceLogic constructor.
     * @param BizCommonInfo $oBizCommonInfo  $oBizCommonInfo
     * @param BizProduct[]  $aBizProductList $aBizProductList
     */
    public function __construct(BizCommonInfo $oBizCommonInfo, $aBizProductList, $oBusInfo, $bIsCheatingTraffic = false) {
        $this->_oBizCommonInfo     = $oBizCommonInfo;
        $this->_aBizProductMap     = $aBizProductList;
        $this->_bIsCheatingTraffic = $bIsCheatingTraffic;
        $this->_oBusInfo           = $oBusInfo;
    }

    /**
     * @return array
     * @throws ExceptionWithResp 自营价格不存在异常
     */
    public function getBizProductsWithPrice() {
        //获取价格数据
        $aPriceInfoList = $this->getMultiResponse($this->_oBizCommonInfo, $this->_aBizProductMap);
        if (empty($aPriceInfoList)) {
            $sErrMsg = Config::text('errno', 'dache_anycar_no_products_error');
            throw new ExceptionWithResp(
                Code::E_ATHENA_REQUEST_FAIL,
                EstimateDegradeCode::R_ESTIMATE_DEGRADE_PRICE_EMPTY,
                $sErrMsg
            );
        }

        //构建BizProduct的 map，用product_category索引
        $aBizProductsMap = [];
        $iTripCloudCount = 0;
        foreach ($aPriceInfoList as $oPriceInfo) {
            if (empty($oPriceInfo) || $oPriceInfo->getProductCategory() < 0) {
                continue;
            }

            $oBizProduct = $this->_aBizProductMap[$oPriceInfo->getProductCategory()];
            $oProduct    = $oBizProduct->oProduct;
            if ($this->_isNotSelfSupportProduct($oProduct)) {
                ++$iTripCloudCount;
            }

            if (null != $oBizProduct) {
                $oBizProduct->setPriceInfo($oPriceInfo);
                $this->setIntercitySurpriseAloneSign($oPriceInfo, $oProduct);
                $aBizProductsMap[$oPriceInfo->getProductCategory()] = $oBizProduct;
            }
        }

        $bHasValidPrice = ($iTripCloudCount < count($aPriceInfoList)) ?? false;
        // 若没有任何一个自营的有效价格，则需要抛异常
        if (!$bHasValidPrice && !$this->_notVerifySelfPrice()) {
            $sErrMsg = Config::text('errno', 'dache_anycar_no_products_error');
            throw new ExceptionWithResp(
                Code::E_ATHENA_REQUEST_FAIL,
                EstimateDegradeCode::R_ESTIMATE_DEGRADE_DIDI_EMPTY,
                $sErrMsg
            );
        }

        return $aBizProductsMap;
    }
    /**
     * @param array $aProductList 构建的product列表
     * @param BizCommonInfo $oBizCommonInfo
     * @return array
     * @throws ExceptionWithResp 获取价格失败异常
     */

    /**
     * @param BizCommonInfo $oBizCommonInfo $oBizCommonInfo
     * @param BizProduct[]  $aBizProducts   $aBizProducts
     * @return EstimateNewFormData[]
     */
    public function getMultiResponse(BizCommonInfo $oBizCommonInfo, $aBizProducts) {
        $aPriceParams = $this->_getMultiPriceParams($oBizCommonInfo, $aBizProducts);
        $aExtraInfo   = $this->_getExtraInfoParams();
        $aPriceResult = $this->_getMultiPriceByClient($aPriceParams, $aExtraInfo);
        return $aPriceResult;
    }

    /**
     * @param BizCommonInfo $oBizCommonInfo $oBizComonInfo
     * @param BizProduct[]  $aBizProducts   $aBizProducts
     * @return array
     */
    private function _getMultiPriceParams(BizCommonInfo $oBizCommonInfo, $aBizProducts) {
        $aPriceParams = array();
        foreach ($aBizProducts as $oBizProduct) {
            $oProduct = $oBizProduct->oProduct;
            $aParams        = $this->getPriceParams($oBizCommonInfo, $oProduct);
            $aPriceParams[] = $aParams;

            //todo tobe removed append 导流品类
            if ($oProduct->oOrderInfo->bNeedGuideInfo) {
                $aPriceParams[] = $this->_mockGuideRequest($aParams);
            }
        }

        return $aPriceParams;
    }

    /**
     * @param BizCommonInfo $oBizCommonInfo 通用入参
     * @param Product       $oProduct       产品信息
     * @return array
     */
    public function getPriceParams(BizCommonInfo $oBizCommonInfo, Product $oProduct) {
        // 顺风车只有预约单模式
        $orderType = $oProduct->oOrderInfo->iOrderType;
        if (in_array($oProduct->oOrderInfo->iProductCategory, [OrderEstimatePcId::EstimatePcIdCarpoolSFCar, OrderEstimatePcId::EstimatePcIdCarpoolCrossSFCar])) {
            $orderType = OrderType::BookingOrder;
        }

        //注意：新增参数需要更新thift文件，并通知Athena的同学,切记切记。。。
        $aResult = [
            'common_info'         => [
                'business_id'         => $oProduct->oOrderInfo->iBusinessId,
                'access_key_id'       => $oBizCommonInfo->oCommonInfo->iAccessKeyID,
                'app_version'         => $oBizCommonInfo->oCommonInfo->sAppVersion,
                'client_type'         => $oBizCommonInfo->oCommonInfo->iClientType,
                'lang'                => $oBizCommonInfo->oCommonInfo->sLang,
                'origin_id'           => $oBizCommonInfo->oCommonInfo->iOriginId,
                'terminal_id'         => $oBizCommonInfo->oCommonInfo->iTerminalId,
                'imei'                => $oBizCommonInfo->oCommonInfo->sImei,
                'is_from_b2b'         => $oBizCommonInfo->oCommonInfo->bIsFromB2b,
                'is_from_webapp'      => $oBizCommonInfo->oCommonInfo->bIsFromWebApp,
                'from'                => $oBizCommonInfo->oCommonInfo->sFrom,
                'pre_trace_id'        => $oBizCommonInfo->oCommonInfo->sPreTraceId,
                'platform_type'       => $oBizCommonInfo->oCommonInfo->iPlatformType,
                'estimate_style_type' => $oBizCommonInfo->oCommonInfo->iEstimateStyleType,
                'route_id'            => (string)$oBizCommonInfo->oCommonInfo->sRouteId ?: null,
            ],
            'order_info'          => [
                'current_lng'       => $oBizCommonInfo->oAreaInfo->fCurLng,
                'current_lat'       => $oBizCommonInfo->oAreaInfo->fCurLat,
                'area'              => $oBizCommonInfo->oAreaInfo->iArea,
                'from_lng'          => $oBizCommonInfo->oAreaInfo->fFromLng,
                'from_lat'          => $oBizCommonInfo->oAreaInfo->fFromLat,
                'from_poi_id'       => $oBizCommonInfo->oAreaInfo->sFromPoiId,
                'from_poi_type'     => $oBizCommonInfo->oAreaInfo->sFromPoiType,
                'from_address'      => $oBizCommonInfo->oAreaInfo->sFromAddress,
                'from_name'         => $oBizCommonInfo->oAreaInfo->sFromName,
                'starting_name'     => $oBizCommonInfo->oAreaInfo->sStartingName,
                'choose_f_searchid' => $oBizCommonInfo->oAreaInfo->sChooseFSearchid,
                'to_lng'            => $oBizCommonInfo->oAreaInfo->fToLng,
                'to_lat'            => $oBizCommonInfo->oAreaInfo->fToLat,
                'to_poi_id'         => $oBizCommonInfo->oAreaInfo->sToPoiId,
                'to_poi_type'       => $oBizCommonInfo->oAreaInfo->sToPoiType,
                'to_address'        => $oBizCommonInfo->oAreaInfo->sToAddress,
                'to_name'           => $oBizCommonInfo->oAreaInfo->sToName,
                'dest_name'         => $oBizCommonInfo->oAreaInfo->sDestName,
                'choose_t_searchid' => $oBizCommonInfo->oAreaInfo->sChooseTSearchid,
                'order_type'        => $orderType,
                'channel'           => $oBizCommonInfo->oCommonInfo->sChannel,
                'combo_type'        => $oProduct->oOrderInfo->iComboType,
                'departure_time'    => $oProduct->oOrderInfo->iDepartureTime,
                'call_car_type'     => $oProduct->oOrderInfo->iCallCarType,
                'call_car_phone'    => $oProduct->oOrderInfo->sCallCarPhone,
                'map_type'          => $oBizCommonInfo->oAreaInfo->sMapType,
                'product_id'        => $oProduct->oOrderInfo->iProductId,
                'require_level'     => $oProduct->oOrderInfo->iRequireLevel,
                'district'          => $oBizCommonInfo->oAreaInfo->iDistrict,
                'payments_type'     => $oProduct->oOrderInfo->sPaymentsType,
                'combo_id'          => $oProduct->oOrderInfo->iComboId,
                'carpool_seat_num'  => $oProduct->oOrderInfo->iCarpoolSeatNum,
                'scene_type'        => $oProduct->oOrderInfo->iSceneType,
                'airport_id'        => $oProduct->oOrderInfo->iAirportId,
                'abstract_district' => $oBizCommonInfo->oAreaInfo->sAbstractDistrict,
                'is_fast_car'       => \BizLib\Utils\Product::isFastcar($oProduct->oOrderInfo->iProductId),
                'activity_id'       => $oProduct->oOrderInfo->iActivityId,
                'user_type'         => $oBizCommonInfo->oPassengerInfo->iUserType,
            ],
            'passenger_info'      => json_encode($oProduct->getPassengerInfoWithMember($oBizCommonInfo), JSON_UNESCAPED_UNICODE),
            'custom_service_info' => json_encode($oProduct->aCustomFeature, JSON_UNESCAPED_UNICODE),
            'one_key_activity'    => json_encode(['activity_switch' => false], JSON_UNESCAPED_UNICODE),
        ];
        //改写大巴起终点信息
        if ($oProduct->oOrderInfo->iProductId == $this->_oBusInfo['product_id']) {
            $aResult['order_info']['from_lat']       = $this->_oBusInfo['on_station']['xy']['lat'];
            $aResult['order_info']['from_lng']       = $this->_oBusInfo['on_station']['xy']['lng'];
            $aResult['order_info']['to_lat']         = $this->_oBusInfo['off_station']['xy']['lat'];
            $aResult['order_info']['to_lng']         = $this->_oBusInfo['off_station']['xy']['lng'];
            $aResult['order_info']['departure_time'] = $this->_oBusInfo['departure_time'];
            $aResult['order_info']['shift_id']       = $this->_oBusInfo['shift_id'];
            $aResult['order_info']['from_poi_id']    = '';
            $aResult['order_info']['from_poi_type']  = '';
            $aResult['order_info']['from_address']   = '';
            $aResult['order_info']['starting_name']  = '';
            $aResult['order_info']['to_poi_id']      = '';
            $aResult['order_info']['to_poi_type']    = '';
            $aResult['order_info']['to_address']     = '';
            $aResult['order_info']['dest_name']      = '';
        }
        $aOrderInfo = $oProduct->oOrderInfo->toArray();
        $aNTuple    = FieldOrderNTuple::getOrderNTupleByOrder($aOrderInfo);
        $aOrderInfo['n_tuple'] = $aNTuple;
        $aExtraOrderInfo       = array_diff_key($aOrderInfo, $aResult['order_info']);

        //按需补充order_extra
        $aExtraOrderInfo['access_key_id'] = $oBizCommonInfo->oCommonInfo->iAccessKeyID;
        $aExtraOrderInfo['to_area']       = $oBizCommonInfo->oAreaInfo->iToArea;
        $aExtraOrderInfo['county']        = $oBizCommonInfo->oAreaInfo->iFromCounty;
        $aExtraOrderInfo['to_county']     = $oBizCommonInfo->oAreaInfo->iToCounty;
        if (!empty($oBizCommonInfo->getCommonInfo()->aMultiRequireProducts)) {
            foreach ($oBizCommonInfo->getCommonInfo()->aMultiRequireProducts as $aProduct) {
                if ($aProduct['product_category'] == $oProduct->oOrderInfo->iProductCategory && !empty($aProduct['voy_route_id'])) {
                    $aExtraOrderInfo['voy_route_id'] = $aProduct['voy_route_id'];
                }
            }
        }

        // 宠物快车调用price-api时，需要设置宠物数据：小型犬+带箱子
        if ($oProduct->oOrderInfo->iProductCategory == OrderEstimatePcId::EstimatePcIdPetFastCar) {
            $aExtraOrderInfo['pet_info'] = [
                'pet_service_tag' => 1001,  //1000-不带箱子 1001-带普通宠物箱子
                'weight_category' => 1,     //小型犬
            ];
        }

        //获取dos存储格式的途经点信息
        $aWayPointsInfo = OrderStopoverPoints::getDosFieldsByStopoverPoints($oProduct->oOrderInfo->aStopoverPoints);
        if (!empty($aWayPointsInfo)) {
            $aExtraOrderInfo = array_merge($aExtraOrderInfo, $aWayPointsInfo);
        }

        $aExtraInfo = [
            'order'                 => json_encode($aExtraOrderInfo, JSON_UNESCAPED_UNICODE),
            'preference_product'    => $oBizCommonInfo->oCommonInfo->sPreferenceProduct,
            'product_category'      => $oProduct->oOrderInfo->iProductCategory,
            'special_scene_param'   => $oProduct->oOrderInfo->iSpecialSceneParam,
            'preferred_route_id'    => $oBizCommonInfo->oCommonInfo->sRouteId,
            'route_preference_type' => $oBizCommonInfo->oCommonInfo->iRoutePreferenceType,
        ];
        if (OrderEstimatePcId::EstimatePcIdBargain == $oProduct->oOrderInfo->iProductCategory) {
            $aExtraInfo['start_fence_id'] = json_encode($oProduct->aExtraInfo['start_fence_id']);
            $aExtraInfo['stop_fence_id']  = json_encode($oProduct->aExtraInfo['stop_fence_id']);
        }

        if ($oProduct->oOrderInfo->iProductId == $this->_oBusInfo['product_id']) {
            $aExtraInfo['from_station_id'] = $this->_oBusInfo['on_station']['station_id'];
            $aExtraInfo['dest_station_id'] = $this->_oBusInfo['off_station']['station_id'];
            $aExtraInfo['shift_id']        = $this->_oBusInfo['shift_id'];
            $aExtraInfo['departure_time']  = $this->_oBusInfo['departure_time'];
        }

        if (OrderEstimatePcId::EstimatePcIdTaxiCarpool == $oProduct->oOrderInfo->iProductCategory
            && !empty(AdditionalServiceLogic::getInstance()->getPcId2CustomFeature($oProduct->oOrderInfo->iProductCategory))
        ) {
            foreach (AdditionalServiceLogic::getInstance()->getPcId2CustomFeature($oProduct->oOrderInfo->iProductCategory) as $oFeature) {
                if (CustomLogic::CUSTOM_SERVICE_LANKEBAO_PICKUP == $oFeature->service_id) {
                    $aExtraInfo['is_lankebao'] = '1';
                }
            }
        }


        $aResult['extra_info'] = $aExtraInfo;
        return $aResult;
    }

    /**
     * @return array
     */
    private function _getExtraInfoParams() {
        $aExtraInfo = [
            'is_new_data_send'     => 0, //6.0不走字段压缩
            //'shake_flag'           => '0',
            'pre_estimate_id_list' => '',
            'feature_data'         => '{}',
        ];
        return $aExtraInfo;
    }



    /**
     * @param array $aPriceParams $aPriceParams
     * @param array $aExtraInfo   $aExtraInfo
     * @return array
     */
    private function _getMultiPriceByClient($aPriceParams, $aExtraInfo) {
        $aHeader = [];
        if ($this->_bIsCheatingTraffic) {
            $aHeader['is_cheating_traffic'] = '1';
        }

        $aMultiParams = [
            'estimatePriceReqs' => $aPriceParams,
            'caller'            => 'pMultiEstimatePriceV3', //FIXME: 没有ctx, 先写死吧. 等效 getControllerName
            'biz_data'          => '',
            'extra_info'        => $aExtraInfo,
        ];
        $_oClient     = new PriceApiClient(PriceApiClient::MODULE_NAME);
        $aRet         = $_oClient->estimateV3($aMultiParams, $aHeader);

        if (isset($aRet['errno']) && 0 == $aRet['errno']) {
            $aData = $aRet['data'];
            if (!empty($this->_aGuideEstimateID)) {
                $aData = $this->_mergeGuideRequest($aData);
            }

            return $aData;
        } else {
            return [];
        }
    }

    /**
     * @param array $aParams price单品类入参
     * @return array
     */
    private function _mockGuideRequest($aParams) {
        $aParams['common_info']['client_type']   = Common::CLIENT_TYPE_GUIDE;
        $aParams['common_info']['guide_request'] = true;
        $aOrderExtra = json_decode($aParams['extra_info']['order'], true);
        if (empty($aOrderExtra['personalized_custom_option'])) {
            $aOrderExtra['personalized_custom_option'] = new \ArrayObject();
        }

        if (empty($aOrderExtra['custom_feature'])) {
            $aOrderExtra['custom_feature'] = new \ArrayObject();
        }

        $sOriginEID = $aOrderExtra['estimate_id'];
        $sGuideEID  = strtoupper($aOrderExtra['estimate_id']);
        $aOrderExtra['estimate_id']          = $sGuideEID;
        $aParams['extra_info']['order']      = json_encode($aOrderExtra, JSON_UNESCAPED_UNICODE);
        $this->_aGuideEstimateID[$sGuideEID] = $sOriginEID;
        return $aParams;
    }

    /**
     * @param EstimateNewFormData[] $aPriceResp eid => priceInfo
     * @return array
     */
    private function _mergeGuideRequest($aPriceResp) {
        $aMergedData = [];
        foreach ($aPriceResp as $sEID => $oPriceInfo) {
            //若当前EID是导流EID，则将当前的priceInfo 塞到原品类的 guide_price_info字段
            $sOriginEID = $this->_aGuideEstimateID[$sEID];
            if (!empty($sOriginEID)) {
                $oOriginPriceInfo = $aPriceResp[$sOriginEID];
                if (!empty($oOriginPriceInfo)) {
                    $oOriginPriceInfo['guide_price_info'] = $oPriceInfo;
                }

                continue;
            }

            $aMergedData[$sEID] = $oPriceInfo;
        }

        return $aMergedData;
    }

    /**
     * 判断是否是三方或货运或火车票
     * @param Product $oProduct ProductID
     * @return bool
     */
    private function _isNotSelfSupportProduct($oProduct): bool {
        $iProductID = $oProduct->oOrderInfo->iProductId;
        $iProductCategory = $oProduct->oOrderInfo->iProductCategory;
        if ($oProduct->isTripcloud()
            || ProductProductId::ProductIdFreight == $iProductID
            || ProductProductId::ProductIdTrainTicket == $iProductID
            || OrderEstimatePcId::EstimatePcIdPetFastCar == $iProductCategory
            || OrderEstimatePcId::EstimatePcIdEngageCar == $iProductCategory
            || OrderEstimatePcId::EstimatePcIdDaiJia == $iProductCategory
        ) {
            return true;
        }

        return false;
    }

    /**
     * 判断是否不需要验证自营车型无价格。
     * 此场景 case1: 无障碍金刚位，曹操无障碍车
     */
    private function _notVerifySelfPrice() {
       return $this->_oBizCommonInfo->getCommonInfo()->iPageType == Horae::PAGE_TYPE_NO_OBSTACLE;
    }

    /**
     * @param EstimateNewFormData $oPriceInfo PriceInfo
     * @param Product             $oProduct   Product
     * @return void
     */
    public function setIntercitySurpriseAloneSign(EstimateNewFormData $oPriceInfo, Product $oProduct) {
        if ('1' == $oPriceInfo->getBillInfo()['history_extra_map']['is_intercity_surprise_alone']) {
            $oProduct->oOrderInfo->iIsIntercitySurpriseAlone = '1';
        }
    }
}
