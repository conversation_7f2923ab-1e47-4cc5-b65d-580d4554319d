<?php

namespace PreSale\Logics\v3Estimate;

use Biz<PERSON>om<PERSON>\Constants\OrderNTuple;
use BizLib\Client\UfsClient;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Log;
use BizLib\PhystrixClient\AthenaApiPhystrixClient;
use BizLib\Utils\ProductCategory;
use Dirpc\SDK\AthenaApiv3\ApiAddProduct;
use Dirpc\SDK\AthenaApiv3\ApiGuideInfo;
use Dirpc\SDK\AthenaApiv3\AthenaNewFormBubbleReq;
use Dirpc\SDK\AthenaApiv3\BenefitsType;
use Dirpc\SDK\AthenaApiv3\CustomFeature;
use Dirpc\SDK\AthenaApiv3\PlatformCompensateData;
use Dirpc\SDK\AthenaApiv3\PreferenceProductItem;
use Dirpc\SDK\AthenaApiv3\TabItem;
use Dirpc\SDK\PriceApi\EstimateNewFormCouponInfo;
use Dirpc\SDK\PriceApi\EstimateNewFormData;
use Dirpc\SDK\PriceApi\EstimateNewFormExtend;
use Dukang\PropertyConst\Order\OrderEstimatePcId;
use PreSale\Infrastructure\Repository\Redis\GuideDiscountForm;
use PreSale\Infrastructure\Util\AsyncMode\AsyncDecorator;
use PreSale\Logics\newFormTab\Tab;
use PreSale\Logics\taxi\TaxiPeakFee;
use PreSale\Logics\v3Estimate\FormAB\SurpriseDiscountABParam;
use PreSale\Logics\v3Estimate\multiRequest\AreaInfo;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;
use PreSale\Logics\v3Estimate\multiRequest\CommonInfo;
use PreSale\Logics\v3Estimate\multiRequest\HildaPrivilegeInfo;
use PreSale\Logics\v3Estimate\multiRequest\MemberInfo;
use PreSale\Logics\v3Estimate\multiRequest\OrderInfo;
use PreSale\Logics\v3Estimate\multiRequest\PassengerInfo;
use PreSale\Logics\v3Estimate\multiResponse\CategoryConfig\CategoryConfig;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\BargainRangeLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\SurpriseDiscountBoxLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutAbstract;
use Xiaoju\Apollo\Apollo;
use Xiaoju\Apollo\Apollo as ApolloV2;
use Xiaoju\Apollo\ApolloConstant;
use Dukang\PropertyConst\Product\ProductPageType;

/**
 * class AthenaRecommend
 */
class AthenaRecommend
{
    use AsyncDecorator;

    const MANNED_VEHICLE_GUIDE_TYPE  = 2;
    const MANNED_VEHICLE_GUIDE_SCENE = 5;

    private static $_oInstance;

    private static $_bInit;

    const MODE_ONLY_PRODUCTS_FILTER = 1; //仅供需准入数据
    const MODE_ALL_FUNCTIONS        = 2; //获取所有能力

    const GUIDE_TYPE_NORMAL = 1;
    const GUIDE_TYPE_GUIDE  = 2; //拼成乐/城际拼车/无人车 等

    const GUIDE_DISCOUNT_FORM_CHENGYIDAN = 1; // 橙意单折扣(分单给降价司机)

    const SINGLE_TYPE         = 0; // 0:单车型
    const SHORT_DISTANCE_TYPE = 2; // 2:聚合车型(三方,出租车,  远必省盒子)  盒子尽量用这个不要新增
    const PEAK_TYPE           = 3; // 3:高峰期盒子 todo delete
    const TP_TYPE  = 4; // 4:TP盒子  todo delete
    const FLOW_BOX = 5;  //流量盒子

    // 所在位置（2: 推荐区、1:置顶-导流 0: 非推荐区 4:底部导流位 5:置顶）
    const REC_POS_TOP_AREA       = 1;
    const REC_POS_RECOMMEND_AREA = 2;
    const REC_POS_OTHER_AREA     = 0;
    const REC_POS_BOTTOM_AREA    = 4;
    const REC_POS_TOP_AREA_NEW   = 5;

    const LUX_PREMIUM_PEOPLE     = 1; // 专豪核心人群
    const LUX_PREMIUM_PEOPLE_TAG = 'luxr_premium_protect'; //  专豪核心人群
    const FANKUAI_TAG            = 'fankuai';  //  独乘泛快人群
    const CARPOOL_PEOPLE_TAG     = 'carpool_people';  //  拼车人群
    const IS_SUPPORT = '1'; //司乘议价推荐或者多勾支持字段

    /**
     * @var AreaInfo
     */
    private $_oAreaInfo;

    /**
     * @var CommonInfo
     */
    private $_oCommonInfo;

    /**
     * @var PassengerInfo
     */
    private $_oPassengerInfo;

    /**
     * @var BizProduct[]
     */
    private $_aBizProductList;

    private $_aAthenaResp = [];

    public $iFilterStyle = 0;

    private $_aPcIDToSelectType;

    private $_aMannedCarpoolInfo;

    private $_aRecResultMap = [];

    /**
     * @var string[] 导流位PcIdList
     */
    private $_aGuidePCIds = [];

    /**
     * @var array 命中的司乘议价场景
     */
    private $_aBargainSense = [];

    /**
     * 自身: {<product_category>: <ext_info> }
     * ext_info: map<string, string>
     * @var array
     */
    private $_aSingleProductExtraInfo = [];  //品类->extra_info

    private $_aProductIdPosition;  //品类->rec_pos
    private $_aPossibleCategoryMap;  //（新配置）品类->分栏映射
    private $_aCategoryIdPcIdMap;  //（兜底）品类->分栏映射
    private $_aPcIDToSubGroupIDMap;  //（配置）品类->盒子映射
    private $_aSubGroupIdToCategoryMap;  //（配置）盒子->分栏映射
    private $_aCategoryIdPosition;  //(athena)品类->分栏id
    private $_aSubCategoryIdPosition; //(athena)盒子->分栏id

    private $_aProductFoldType;   //品类折叠状态
    private $_aSubGroupFoldType; // 盒子折叠状态

    private $_aSubGroupInfoMap;

    private $_aTopSinglePcIdList           = []; // 置顶单车型IdList
    private $_aTopShortDistanceSubGroupIds = []; // 置顶聚合车型IdList

    private $_aRecPcToSubGroup = []; // 品类->盒子

    private $_aProductEtdMap = [];

    /**
     * 过滤器相关信息
     */
    private $_aFilterInfo;

    private $_aProductTopScore = [];

    /**
     * @var map[SubGroupId][品类category1,品类category2]
     */
    private $_aTPSubGroupId2PcIdList;


    /**
     * @var int
     */
    private $_iShowCarNum = 0;
    /**
     * @var int
     */
    private $_iHitLuxPremiumProtect = 0;
    /**
     * @var array 人群标签
     */
    private $_aCrowdLabelMap = [];

    /**
     * @var array athena返回的estimate_strip_info
     */
    private $_aEstimateStripInfo = [];

    /**
     * @var int
     */
    private $_iETSInfo = 0;

    /**
     * @var BizCommonInfo
     */
    private $_oBizCommonInfo;


    /**
     * @return void
     */
    private function __construct() {
    }


    /**
     * @return AthenaRecommend
     */
    public static function getInstance() {
        if (self::$_oInstance == null) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * AthenaRecommend init.
     * @param BizCommonInfo $oBizCommonInfo oBizCommonInfo
     * @param BizProduct[] $aBizProductMap aBizProductMap
     * @return void
     */
    public function init(BizCommonInfo $oBizCommonInfo, $aBizProductMap) {
        if (!self::$_bInit) {
            $this->_oBizCommonInfo  = $oBizCommonInfo;
            $this->_oAreaInfo       = $oBizCommonInfo->oAreaInfo;
            $this->_oCommonInfo     = $oBizCommonInfo->oCommonInfo;
            $this->_oPassengerInfo  = $oBizCommonInfo->oPassengerInfo;
            $this->_aBizProductList = $aBizProductMap;
            //提前加载三方聚合表单侧边栏
            if (Tab::CLASSIFY_TAB == $this->_oCommonInfo->sTabId) {
                $oInstance = CategoryConfig::getInstance($oBizCommonInfo, $aBizProductMap);
                $this->_aPossibleCategoryMap     = $oInstance->getPossibleCategoryMap();
                $this->_aCategoryIdPcIdMap       = $oInstance->getCategoryIdPcIdMap();
                $this->_aSubGroupIdToCategoryMap = $oInstance->getCategoryIdTypeMap();
                $this->_aPcIDToSubGroupIDMap     = DecisionV2Service::getInstance()->getNewPcIDToSubGroupID();
            }

            //调用athena获取数据
            $this->_fetchAthenaRecommendInfo();
            //解析返回，并设置到私有属性中，为对应的get方法提供数据
            if (!empty($this->_aAthenaResp)) {
                // 设置置顶推荐数据
                $this->_setTopRecommendData();

                // 单车型推荐结果(单车型位置)
                $this->_setRecommendAreaProducts();

                // 设置默认勾选的车型
                $this->_setPcIDToSelectType($oBizCommonInfo->oCommonInfo->bIsMultiSelectionSupport);

                // 聚合车型推荐信息
                $this->_setSubGroupInfoMap();

                // 设置ETP信息
                $this->_setTPFormInfo();

                // 设置过滤器信息
                $this->_setBubbleFilterInfo();
                $this->async()->setChengYiDanCache();

                $this->_setRecResultMap();

                // 获取athena品类和盒子的映射关系
                $this->_setRecPcToSubGroup();

                $this->_setData();

                // 设置是否是专豪核心人群
                $this->_setHitLuxPremiumProtect();

                // 设置人群标签
                $this->_setCrowdLabelMap();

                // 设置7.0推荐tab数据
                $this->_setClassifyRecForm();

                // 设置品类etd数据
                $this->_setProductEtdMap();
            }
        }

        self::$_bInit = true;
    }

    /**
     * @param BizProduct[] $aBizProductMap $aBizProductMap
     * @return mixed
     */
    public function filterBizProducts($aBizProductMap) {
        //过滤Athena未准入的
        $aDisableInfoList = $this->_aAthenaResp['disable_product_category'];
        if (!empty($aDisableInfoList) && is_array($aDisableInfoList)) {
            $aDisablePcIdList = [];
            foreach ($aDisableInfoList as $aDisableInfo) {
                if ($aDisableInfo['product_item']['product_category'] > 0) {
                    $aDisablePcIdList[] = $aDisableInfo['product_item']['product_category'];
                }
            }

            foreach ($aBizProductMap as $iPcID => $oBizProduct) {
                if (in_array($iPcID, $aDisablePcIdList)) {
                    unset($aBizProductMap[$iPcID]);
                }
            }
        }

        //过滤导流品类，Athena未设置需要导流的
        foreach ($aBizProductMap as $iPcID => $oBizProduct) {
            if ($oBizProduct->isGuideProduct() && !in_array($oBizProduct->getProductCategory(), $this->_aGuidePCIds)) {
                unset($aBizProductMap[$iPcID]);
            }
        }

        return $aBizProductMap;
    }

    /**
     * @param int $iProductCategory $iProductCategory
     * @return array|mixed
     */
    public function getGuideProductExtraInfoByProductCategory($iProductCategory) {
        return $this->_aSingleProductExtraInfo[$iProductCategory] ?? [];
    }

    /**
     * @return array
     */
    public function getGuideProductExtraInfo() {
        return $this->_aSingleProductExtraInfo;
    }


    /**
     * 勾选业务线
     * @param bool $bIsMultiSelectionSupport $bIsMultiSelectionSupport
     * @return void
     */
    private function _setPcIDToSelectType($bIsMultiSelectionSupport) {
        $this->_aPcIDToSelectType = [];

        if (!$bIsMultiSelectionSupport) {
            foreach ($this->_aAthenaResp['checked_product_category'] as $acheckedProduct) {
                $this->_aPcIDToSelectType[$acheckedProduct['product_category']] = LayoutAbstract::CHECK;
                return;
            }
        }

        if (!empty($this->_aAthenaResp['checked_product_category'])) {
            foreach ($this->_aAthenaResp['checked_product_category'] as $acheckedProduct) {
                $this->_aPcIDToSelectType[$acheckedProduct['product_category']] = LayoutAbstract::CHECK;
            }
        }
    }



    /**
     *
     * @return void
     */
    private function _setProductEtdMap() {
        if (empty($this->_aAthenaResp) || empty($this->_aAthenaResp['rec_result'])) {
            return;
        }

        $aProductToEtdMap = [];

        foreach ($this->_aAthenaResp['rec_result'] as $oItem) {
            if (!empty($oItem['estimated_time_info']) && !empty($oItem['estimated_time_info']['etd'])) {
                $aProductToEtdMap[$oItem['product_category']] = $oItem['estimated_time_info']['etd'];
            }
        }

        $this->_aProductEtdMap = $aProductToEtdMap;

    }

    /**
     * @return mixed
     */
    public function getProductEtdMap() {
        return $this->_aProductEtdMap;
    }


    /**
     * @return mixed
     */
    public function getPcIDToSelectType() {
        return $this->_aPcIDToSelectType;
    }

    /**
     * @return mixed
     */
    public function getFilterInfo() {
        return $this->_aFilterInfo;
    }

    /**
     * 获取置顶样式
     * @return array
     */
    public function getTopRecommendStyle() {
        return $this->_aAthenaResp['top_rec'];
    }

    /**
     * 获取品类推荐分栏
     * @return array
     */
    public function getRecommendCategory() {
        return $this->_aCategoryIdPosition;
    }

    /**
     * 获取盒子推荐分栏
     * @return array
     */
    public function getSubCategoryIdPosition() {
        return $this->_aSubCategoryIdPosition;
    }

    /**
     * 获取ExtraInfo
     * @return array
     */
    public function getExtraInfo() {
        return $this->_aAthenaResp['extra_info'];
    }

    /**
     * @return void
     */
    private function _setTopRecommendData() {

        // top_rec置顶推荐结果
        // top_rec_list:具体推荐内容
        if (!empty($this->_aAthenaResp['top_rec']['top_rec_list'])) {
            foreach ($this->_aAthenaResp['top_rec']['top_rec_list'] as $item) {
                if (self::SHORT_DISTANCE_TYPE == $item['type']) {
                    $this->_aTopShortDistanceSubGroupIds[] = $item['category'];
                }

                if (self::SINGLE_TYPE == $item['type']) {
                    $this->_aTopSinglePcIdList[] = $item['category'];
                }
            }
        }
    }

    /**
     * 聚合车型ids  (目前聚合车型没有置顶)
     * @return mixed
     */
    public function getTopShortDistanceSubGroupIds() {
        return $this->_aTopShortDistanceSubGroupIds;
    }

    /**
     * @return mixed
     */
    public function getTopSinglePcIds() {
        return $this->_aTopSinglePcIdList;
    }

    /**
     * @return mixed
     */
    public function getProductIdPosition() {
        return $this->_aProductIdPosition;
    }

    /**
     * @return void
     */
    private function _setRecommendAreaProducts() {
        $this->_aProductIdPosition = [];
        // rec_result(List) 单车型推荐结果
        if (!isset($this->_aAthenaResp['rec_result']) || !is_array($this->_aAthenaResp['rec_result'])) {
            return;
        }

        foreach ($this->_aAthenaResp['rec_result'] as $aResult) {
            $this->_aProductIdPosition[$aResult['product_category']]  = $aResult['rec_pos'];
            $this->_aCategoryIdPosition[$aResult['product_category']] = $aResult['category_id'];
            $this->_aProductFoldType[$aResult['product_category']]    = $aResult['fold_type'];

            if ((self::REC_POS_TOP_AREA == $aResult['rec_pos'] || self::REC_POS_BOTTOM_AREA == $aResult['rec_pos']) && 0 != $aResult['product_category']) {
                // 设置导流位车型
                $this->_aGuidePCIds[] = $aResult['product_category'];
            }

            if (!empty($aResult['extra_info'])) {
                $this->_aSingleProductExtraInfo[$aResult['product_category']] = $aResult['extra_info'];
            }

            if ($aResult['top_rec_flag']) {
                // 排序置顶权重
                $this->_aProductTopScore[$aResult['product_category']] = $aResult['top_rec_score'];
            }
        }
    }

    /**
     * @return string[]
     */
    public function getGuidePCIds() {
        return $this->_aGuidePCIds;
    }

    /**
     * @return void
     */
    private function _setSubGroupInfoMap() {
        $this->_aSubGroupInfoMap = [];
        // 聚合车型推荐信息
        if (!empty($this->_aAthenaResp['sub_group_rec_list'])) {
            foreach ($this->_aAthenaResp['sub_group_rec_list'] as $aSubGroup) {
                $this->_aSubGroupInfoMap[$aSubGroup['sub_group_id']]       = $aSubGroup;
                $this->_aSubGroupFoldType[$aSubGroup['sub_group_id']]      = $aSubGroup['fold_type'];
                $this->_aSubCategoryIdPosition[$aSubGroup['sub_group_id']] = $aSubGroup['category_id'];
            }
        }
    }

    /**
     * 设置Athena过滤器信息
     * @return void
     */
    private function _setBubbleFilterInfo() {
        $aFilterInfo = $this->_aAthenaResp['filter_info'];
        if (empty($aFilterInfo)
            || !is_array($aFilterInfo['filter_list'])
            || sizeof($aFilterInfo['filter_list']) < 1
        ) {
            // 原始数据校验
            return;
        }

        $this->_aFilterInfo = $aFilterInfo;
    }


    /**
     * 设置TP表单信息
     * @return void
     */
    private function _setTPFormInfo() {
        $aTPGroupInfoList = $this->_aAthenaResp['tp_form_list'];
        if (!is_array($aTPGroupInfoList)
            || sizeof($aTPGroupInfoList) < 1
        ) {
            // 原始数据校验
            return;
        }

        $aTpDataCache = [];
        foreach ($aTPGroupInfoList as $aTPGroupInfo) {
            if (empty($aTPGroupInfo)
                || !is_array($aTPGroupInfo['tp_form_item'])
                || sizeof($aTPGroupInfo['tp_form_item']) < 1
            ) {
                continue;
            }

            foreach ($aTPGroupInfo['tp_form_item'] as $aCommonItem) {
                $iPcId = $aCommonItem['property_id'];

                if (empty($this->_aBizProductList[$iPcId]) || empty($this->_aBizProductList[$iPcId]->getOrderInfo())) {
                    continue;
                }

                $aTpDataCache[$aTPGroupInfo['sub_group_id']][$this->_aBizProductList[$iPcId]->getEstimateID()] = [
                    'product_category' => $iPcId,
                    'etp'              => $aCommonItem['etp'],
                    'text'             => $aCommonItem['text'],
                    'icon'             => $aCommonItem['icon'],
                    'departure_time'   => $this->_aBizProductList[$iPcId]->getOrderInfo()->iDepartureTime,
                    'tp_label_text'    => $aCommonItem['tp_label_text'],
                    'group_info'       => $aTPGroupInfo['group_info'],
                    'ets'              => $aCommonItem['ets'],
                    'ets_text'         => $aCommonItem['ets_text'],
                ];
            }
        }

        // 存储信息,发单使用
        $aFeature    = ['estimate.tp_info' => json_encode($aTpDataCache)];
        $aConditions = ['uid' => $this->_oPassengerInfo->getPassengerInfo()['uid']];

        $aRes = (new UfsClient())->setFeature($aFeature, $aConditions, 'passenger');

        if (UfsClient::SUCCESS_CODE !== $aRes['errno']) {
            Log::warning(
                Msg::formatArray(
                    Code::E_COMMON_SET_UFS_FAIL,
                    ['featureValues' => $aFeature, 'condition' => $aConditions, 'setUfsRes' => $aRes]
                )
            );
        }
    }

    /**
     * @return mixed
     */
    public function getSubGroupInfoMap() {
        return $this->_aSubGroupInfoMap;
    }

    /**
     * @return void
     */
    private function _setRecResultMap() {
        if (!isset($this->_aAthenaResp['rec_result']) || !is_array($this->_aAthenaResp['rec_result'])) {
            return;
        }

        foreach ($this->_aAthenaResp['rec_result'] as $aResult) {
            if (empty($aResult['bubble_info'])) {
                continue;
            }

            $this->_aRecResultMap[$aResult['product_category']] = $aResult;
        }
    }

    /**
     * @return void
     */
    private function _setRecPcToSubGroup() {
        if (!isset($this->_aAthenaResp['rec_result']) || !is_array($this->_aAthenaResp['rec_result'])) {
            return;
        }

        foreach ($this->_aAthenaResp['rec_result'] as $aResult) {
            if (empty($aResult['sub_group_id'])) {
                continue;
            }

            $this->_aRecPcToSubGroup[$aResult['product_category']] = $aResult['sub_group_id'];
        }
    }

    /**
     * @param int $iProductCategory product_category
     * @return array|mixed
     */
    public function getRecResultMap($iProductCategory) {
        return $this->_aRecResultMap[$iProductCategory] ?? [];
    }


    /**
     * 接口文档地址: http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=743927604
     * @return void
     */
    private function _fetchAthenaRecommendInfo() {
        $aAthenaParams = [
            //commonInfo
            'client_type'         => $this->_oCommonInfo->iClientType,
            'app_version'         => $this->_oCommonInfo->sAppVersion,
            'lang'                => $this->_oCommonInfo->sLang,
            'channel'             => $this->_oCommonInfo->sChannel,
            'page_type'           => $this->_oCommonInfo->iPageType,
            'menu_id'             => $this->_oCommonInfo->sMenuId,
            'screen_pixels'       => $this->_oCommonInfo->sPixels,
            'screen_scale'        => $this->_oCommonInfo->fScreenScale,
            'form_height'         => $this->_oCommonInfo->iFormHeight,
            'access_key_id'       => $this->_oCommonInfo->iAccessKeyID,
            'order_type'          => $this->_oCommonInfo->iOrderType,
            'call_car_type'       => $this->_oCommonInfo->iCallCarType,
            'estimate_style_type' => $this->_oCommonInfo->iEstimateStyleType,
            'tab_id'              => $this->_oCommonInfo->sTabId,

            //location info
            'map_type'            => $this->_oAreaInfo->sMapType,
            'from_area'           => $this->_oAreaInfo->iArea,
            'from_lat'            => $this->_oAreaInfo->fFromLat,
            'from_lng'            => $this->_oAreaInfo->fFromLng,
            'from_name'           => $this->_oAreaInfo->sFromName,
            'from_county'         => $this->_oAreaInfo->iFromCounty,
            'to_area'             => $this->_oAreaInfo->iToArea,
            'to_lat'              => $this->_oAreaInfo->fToLat,
            'to_lng'              => $this->_oAreaInfo->fToLng,
            'to_name'             => $this->_oAreaInfo->sToName,
            'to_county'           => $this->_oAreaInfo->iToCounty,
            'current_lng'         => $this->_oAreaInfo->fCurLng,
            'current_lat'         => $this->_oAreaInfo->fCurLat,
            'district'            => $this->_oAreaInfo->iDistrict,
            'dest_poi_id'         => $this->_oAreaInfo->sToPoiId,
            'from_poi_code'       => $this->_oAreaInfo->sFromPoiCode,
            'dest_poi_code'       => $this->_oAreaInfo->sDestPoiCode,

            //passenger info
            'phone'               => $this->_oPassengerInfo->sPhone,
            'pid'                 => $this->_oPassengerInfo->iPid,
            'user_type'           => $this->_oPassengerInfo->iUserType,
            'font_scale_type'     => $this->_oCommonInfo->iFontScaleType,
            'adapter_scale'       => $this->_oCommonInfo->fAdapterScale,
        ];

        $aApiAddProducts           = [];
        $aBargainRecommendStatus   = 0;
        $aBargainPriceStrategyType = '';
        foreach ($this->_aBizProductList as $oBizProduct) {
            $oOrderInfo = $oBizProduct->oProduct->oOrderInfo;

            $aProduct = [
                'product_id'       => $oOrderInfo->iProductId,
                'business_id'      => $oOrderInfo->iBusinessId,
                'require_level'    => $oOrderInfo->iRequireLevel,
                'combo_type'       => $oOrderInfo->iComboType,
                'carpool_type'     => $oOrderInfo->iCarpoolType,
                'level_type'       => $oOrderInfo->iLevelType,
                'is_special_price' => $oOrderInfo->iIsSpecialPrice,
                'product_category' => $oOrderInfo->iProductCategory,
                'estimate_id'      => $oOrderInfo->sEstimateID,
                'departure_time'   => $oOrderInfo->iDepartureTime,
                'airport_type'     => $oOrderInfo->iAirportType,
                'sub_group_id'     => DecisionV2Service::getInstance()->getSubGroupIDByPcID($oOrderInfo->iProductCategory),
                'extra_info'       => $this->_buildProductExtraInfo($oBizProduct),
                'route_id'         => $oOrderInfo->iComboId,
                'disabled'         => $oBizProduct->oProduct->getDisabled(),
                'is_trip_cloud'    => $oBizProduct->oProduct->isTripcloud(),
            ];
            if (Tab::CLASSIFY_TAB == $this->_oCommonInfo->sTabId) {
                $aProduct['possible_category_ids'] = $this->_getPossibleCategoryIds($oOrderInfo);
                $aProduct['default_category_id']   = $this->_getDefaultCategoryId($oOrderInfo);
            }

            // priceInfo 是个map[string]string
            $aPriceInfo = $this->_initPriceInfoByBillAndDiscount($oBizProduct);
            $aPriceInfo['carpool_station_info']    = !empty($oOrderInfo->aStationList) ? json_encode($oOrderInfo->aStationList) : '';
            $aPriceInfo['carpool_dual_price_info'] = $this->_buildCarpoolDualPriceInfo($oBizProduct->oPriceInfo, $oBizProduct->getCarpoolFailPriceInfo());

            $oGuidePriceInfo = $oBizProduct->getPriceInfo()['guide_price_info'] ?? [];//$oBizProduct->getGuideRequestPriceInfo();
            if (!empty($oGuidePriceInfo)) {
                $aMockPriceInfo = $this->_initPriceInfoByBillAndDiscount($oBizProduct, $oGuidePriceInfo);
            }

            $aApiGuideInfo = [
                'guide_type'            => $this->_getGuideType($oOrderInfo->iFormShowType),
                'price_info'            => $aPriceInfo,
                'mock_price_info'       => $aMockPriceInfo ?? [],
                'is_dual_carpool_price' => $oOrderInfo->bIsDualCarpoolPrice,
                'carpool_price_type'    => $oOrderInfo->iCarpoolPriceType,
            ];

            //给Athena传下挂信息
            $aSSSEData = AdditionalServiceLogic::getInstance()->getPcId2CustomFeature($oOrderInfo->iProductCategory);
            if (!empty($aSSSEData)) {
                //s3e开城才会有下挂
                foreach ($aSSSEData as $iKey => $aItem) {
                    $oAddedService = new CustomFeature();
                    //取下挂id
                    $oAddedService->id    = $aItem['service_id'];
                    $oAddedService->count = 1;
                    $aProduct['value_added_service'][] = $oAddedService;
                }
            }

            $aProduct['api_guide_info'] = new ApiGuideInfo($aApiGuideInfo);
            $aApiAddProducts[]          = new ApiAddProduct($aProduct);
            if (!empty($oBizProduct->getBargainRecommendInfo())) {
                $aBargainRecommendStatus = $oBizProduct->getBargainRecommendInfo()['recommend_status'];
            }

            if (!empty($oBizProduct->getBargainCarInfo()['price_strategy_type'])) {
                $aBargainPriceStrategyType = $oBizProduct->getBargainCarInfo()['price_strategy_type'];
            }
        }

        //用户选择的过滤器  和  筛选器样式
        $aAthenaParams['preference_filter_id'] = $this->_oCommonInfo->sPreferenceFilterId;
        $this->iFilterStyle            = $this->_getFilterStrategy();
        $aAthenaParams['filter_style'] = $this->iFilterStyle;

        //过滤器样式 0:无过滤器 1:多个过滤器 2:2个过滤器
        $aTabList = [];
        foreach ($this->_oCommonInfo->aTabList as $aTab) {
            $aTabList[] = new TabItem($aTab);
        }

        $aAthenaParams['tab_list']        = $aTabList;
        $aAthenaParams['api_add_product'] = $aApiAddProducts;
        $aAthenaParams['passenger_preference_list'] = $this->_getUserSelectProducts();
        $aExtendInfo = [];
        if (!empty(MemberInfo::$iMemberLevel)) {
            $aExtendInfo['member_level_id'] = MemberInfo::$iMemberLevel;
        }

        //自选车推荐状态：1：表示高于特快基础价1.3倍,0:表示低于特快基础价1.3倍
        $aExtendInfo['bargain_recommend_status'] = $aBargainRecommendStatus;
        // expert_strategy 人工策略 model_strategy算法策略
        $aExtendInfo['price_strategy_type'] = (string)$aBargainPriceStrategyType;
        // athena 要用scene type区分 是否是再来一单的首次预估
        $aExtendInfo['from_type'] = $this->_oCommonInfo->iFromType;
        if (!empty($aExtendInfo)) {
            $aAthenaParams['extra_info'] = $aExtendInfo;
        }

        $expectScene       = [];
        if (Tab::isHitClassifyTabByTabId($this->_oCommonInfo->sTabId)) {
            $expectScene[] = 'classify_form_global_scene';
        }

        $aAthenaParams['expect_scene'] = $expectScene;
        // 赔付规则命中结果
        $aAthenaParams['platform_compensate_info'] = $this->_getPlatFormCompensationInfo();

        // 是否有惊喜特价
        $oHildaParam      = HildaPrivilegeInfo::getInstance();
        $sSurpriseBatchId = $oHildaParam->getSurprisePrivilege();
        if (!empty($sSurpriseBatchId)) {
            $aAthenaParams['effective_benefits'][] = BenefitsType::SURPRISE_CARD;
        }

        $aResp = (new AthenaApiPhystrixClient())->getAthenaBubbleRecommend(
            [
                'athena_bubble_req' => new AthenaNewFormBubbleReq($aAthenaParams),
                'mode'              => self::MODE_ALL_FUNCTIONS,
            ]
        );

        if (0 == $aResp['err_no']) {
            $this->_aAthenaResp = $aResp;
        }
    }

    /**
     * 返回用户二次预估选择的品类
     * @return array
     */
    private function _getUserSelectProducts() {
        $aProducts = [];
        if (!empty($this->_oCommonInfo->aMultiRequireProducts)) {
            foreach ($this->_oCommonInfo->aMultiRequireProducts as $aProduct) {
                if ($aProduct['is_selected']) {
                    $aProducts[] = new PreferenceProductItem(['product_category' => $aProduct['product_category']]);
                }
            }
        }

        return $aProducts;
    }

    /**
     * 过滤器样式  0:无筛选器  1:推荐筛选器   2:普通筛选器
     * @return int
     */
    private function _getFilterStrategy() {
        if ('zh-CN' != $this->_oCommonInfo->sLang) {
            return 0;
        }

        if (OrderNTuple::TYPE_ORDER_BOOKING == $this->_oCommonInfo->iOrderType) {
            return 0;
        }

        if (Tab::isHitClassifyTabByTabId($this->_oCommonInfo->sTabId)) {
            // 三方表单
            return 2;
        }

        $params = [
            ApolloConstant::APOLLO_INDIVIDUAL_ID => $this->_oPassengerInfo->iUid,
            'city'                               => $this->_oAreaInfo->iArea,
            'access_key_id'                      => $this->_oCommonInfo->iAccessKeyID,
            'phone'                              => $this->_oPassengerInfo->sPhone,
            'tab_id'                             => 'normal',
            'app_version'                        => $this->_oCommonInfo->sAppVersion,
            'pid'                                => $this->_oPassengerInfo->iPid,
            'uid'                                => $this->_oPassengerInfo->iUid,
            'feature'                            => 'filter',
        ];

        $aApolloToggle = Apollo::getInstance()->featureToggle('gs_filter_ets_feature_delete', $params);
        if ($aApolloToggle->allow()) {
            return 2;
        }

        return 0;
    }


    /**
     * @param int $iProductCategory $iProductCategory
     * @return int|mixed
     */
    public function getProductTopScore($iProductCategory) {
        return $this->_aProductTopScore[$iProductCategory] ?? 0;
    }

    /**
     * @return array
     */
    public function getAProductTopScore() {
        return $this->_aProductTopScore;
    }


    /**
     * @param BizProduct $oBizProduct $oBizProduct
     * @param EstimateNewFormData $oPriceInfo $oPriceInfo
     * @return array
     */
    private function _initPriceInfoByBillAndDiscount($oBizProduct, $oPriceInfo = null) {
        if (empty($oPriceInfo)) {
            $oPriceInfo = $oBizProduct->getPriceInfo();
        }

        $oBillInfo     = $oPriceInfo->getBillInfo();
        $oDiscountInfo = $oPriceInfo->getDiscountSet();
        $iCouponAmount = 0;
        if (!empty($oDiscountInfo->getCoupon())) {
            $iCouponAmount = (int)$oDiscountInfo->getCoupon()->getAmount();
        }

        $aTotalFee = [
            'estimate_fee'          => $oPriceInfo->getEstimateFee(),
            'personal_estimate_fee' => $oPriceInfo->getPersonalEstimateFee(),
            'coupon_amount'         => 0 == $iCouponAmount ? 0 : round($iCouponAmount / 100, 2),
            'dynamic_info'          => $oBillInfo->getHistoryExtraMap()['dynamic_info'],
            'discount_info'         => $oBillInfo->getHistoryExtraMap()['discount_info'],
        ];

        if (BargainRangeLayout::PRODUCT_CATEGORY_BARGAIN_RANGE == $oPriceInfo->getProductCategory()) {
            $aTotalFee['left_range_price']  = $oBizProduct->getBargainRangeRecommendPriceLower($this->_oBizCommonInfo);
            $aTotalFee['right_range_price'] = $oBizProduct->getBargainRangeRecommendPriceUpper($this->_oBizCommonInfo);
        }

        $aFeeDetailInfo = $oBillInfo->getFeeDetailInfo();
        $aPriceInfo     = [
            'estimate_id'                     => $oBillInfo->getEstimateId(),
            'total_fee'                       => json_encode($aTotalFee),
            'dynamic_diff_price'              => $oBillInfo->getDynamicDiffPrice(),
            'cap_price'                       => (string)$oBillInfo->getCapPrice(),
            'time_cost'                       => (string)$oBillInfo->getDriverMinute(),
            'driver_metre'                    => (string)$oBillInfo->getDriverMetre(),
            'count_price_type'                => (string)$oBillInfo->getCountPriceType(),
            'pre_total_fee'                   => (string)$oBillInfo->getPreTotalFee(),
            'fee_detail_info'                 => empty($aFeeDetailInfo) ? '' : json_encode($aFeeDetailInfo),
            'coupon_info'                     => $this->_buildCouponInfo($oDiscountInfo->getCoupon()),
            'payment_type'                    => (string)$oPriceInfo->getPaymentInfo()->getDefaultPayType(),
            'price_privilege_type'            => (string)$oBillInfo->getHistoryExtraMap()['price_privilege_type'],
            'sp_open'                         => (string)$oBillInfo->getHistoryExtraMap()['sp_open'],
            'price_hide_level'                => (string)$oBillInfo->getHistoryExtraMap()['price_hide_level'],
            'aplus_pay_return'                => empty($oDiscountInfo->getRevolvingAccountRebateInfo()) ? 0 : $oDiscountInfo->getRevolvingAccountRebateInfo()->getAmount(),
            'crazy_aplus_pay_return_type'     => empty($oDiscountInfo->getRevolvingAccountRebateInfo()) ? 0 : $oDiscountInfo->getRevolvingAccountRebateInfo()->getActivityType(),
            'crazy_aplus_pay_return_multiple' => empty($oDiscountInfo->getRevolvingAccountRebateInfo()) ? 0 : $oDiscountInfo->getRevolvingAccountRebateInfo()->getMultiple(),
            'revolving_account_balance'       => empty($oDiscountInfo->getRevolvingAccountDiscountInfo()) ? 0 : $oDiscountInfo->getRevolvingAccountDiscountInfo()->getAmount(),
            'dape_force_sp_open'              => (string)$oBillInfo->getHistoryExtraMap()['dape_force_sp_open'],
            // 惊喜权益卡
            'dape_surprise_deal_privilege_discount' => (string)$oBillInfo->getHistoryExtraMap()['dape_surprise_deal_privilege_discount'],
        ];

        if (ProductPageType::PageTypePageTypeXinZhu == $this->_oBizCommonInfo->getCommonInfo()->iPageType) {
            $oDiscountSet = $oBizProduct->getPriceInfo()->getDiscountSet();
            if (!empty($oDiscountSet)) {
                $oCashback = $oDiscountSet->getCashbackXinZhuInfo();
                if (!empty($oCashback)) {
                    $aPriceInfo['xin_zhu_cashback'] = $oCashback->getCashbackAmount();
                }
            }
        }

        return $aPriceInfo;
    }

    /**
     * @param EstimateNewFormCouponInfo $oCouponInfo $oCouponInfo
     * @return string
     */
    private function _buildCouponInfo($oCouponInfo) {
        $aCouponInfo = [];
        if (!empty($oCouponInfo) && !empty($oCouponInfo->getCouponSource())) {
            if ('pope' == $oCouponInfo->getCouponSource()) {
                $sKey = 'activity_coupon';
            } else {
                $sKey = 'default_coupon';
            }

            $aCouponInfo[$sKey] = [
                'batch_id'      => $oCouponInfo->getBatchId(),
                'batch_type'    => $oCouponInfo->getCouponType(),
                'discount'      => (int)$oCouponInfo->getDiscount(),
                'coupon_amount' => (int)$oCouponInfo->getAmount(),
                'coupon_type'   => $oCouponInfo->getCouponType(),
                'expire_time'   => $oCouponInfo->getExpireTime(),
                'custom_tag'    => $oCouponInfo->getCustomTag(),
            ];
        }

        return empty($aCouponInfo) ? '' : json_encode($aCouponInfo);
    }

    /**
     * @param EstimateNewFormData $oPriceInfo $oPriceInfo
     * @param EstimateNewFormExtend $oCarpoolFailPrice $oCarpoolFailPrice
     * @return string
     */
    private function _buildCarpoolDualPriceInfo($oPriceInfo, $oCarpoolFailPrice) {
        $aCarpoolDualPriceInfo = [];
        if (!empty($oPriceInfo) && !empty($oCarpoolFailPrice)) {
            $aCarpoolDualPriceInfo = [
                'success' => [
                    'estimate_fee'  => (float)$oPriceInfo->getEstimateFee(),
                    'coupon_amount' => (int)$oPriceInfo->getDiscountSet()['coupon']['amount'] ?? 0,
                ],
                'failed'  => [
                    'estimate_fee'  => (float)$oCarpoolFailPrice->getEstimateFee(),
                    'coupon_amount' => (int)$oCarpoolFailPrice->getDiscountSet()['coupon']['amount'] ?? 0,
                ],
            ];
        }

        return empty($aCarpoolDualPriceInfo) ? '' : json_encode($aCarpoolDualPriceInfo);
    }

    /**
     * @param int $iFormShowType $iFormShowType
     * @return int
     */
    private function _getGuideType($iFormShowType) {
        switch ($iFormShowType) {
            case OrderInfo::FORM_SHOW_TYPE_GUIDE:
                return self::GUIDE_TYPE_GUIDE;

            case OrderInfo::FORM_SHOW_TYPE_ANYCAR:
                // no break
            default:
                return self::GUIDE_TYPE_NORMAL;
        }
    }

    /**
     * @return void
     */
    public function setChengYiDanCache() {
        if (!isset($this->_aAthenaResp['rec_result']) || !is_array($this->_aAthenaResp['rec_result'])) {
            return;
        }

        foreach ($this->_aAthenaResp['rec_result'] as $aResult) {
            if (self::GUIDE_DISCOUNT_FORM_CHENGYIDAN == $aResult['discount_form']) {
                $oBizProduct = $this->_aBizProductList[$aResult['product_category']];
                if (!empty($oBizProduct) && !empty($oBizProduct->getEstimateID())) {
                    GuideDiscountForm::setGuideDiscountFormCache($oBizProduct->getEstimateID(), self::GUIDE_DISCOUNT_FORM_CHENGYIDAN);
                }
            }
        }
    }

    /**
     * 是否是载人车
     * @param int $iProductCategory product_category
     * @return bool
     */
    public function isMannedVehicle($iProductCategory) {
        if (ProductCategory::PRODUCT_CATEGORY_CARPOOL_STATION != $iProductCategory) {
            return false;
        }

        if (empty($this->_aRecResultMap[$iProductCategory]['bubble_info'])) {
            return false;
        }

        // 载人车标识
        if (self::MANNED_VEHICLE_GUIDE_SCENE == $this->_aRecResultMap[$iProductCategory]['bubble_info']['guide_scene']
            && self::MANNED_VEHICLE_GUIDE_TYPE == $this->_aRecResultMap[$iProductCategory]['bubble_info']['guide_type']
        ) {
            return true;
        }

        return false;
    }

    /**
     * 构建Athena入参中的品类extra_info字段
     * @param BizProduct $oBizProduct 品类数据
     * @return array
     */
    private function _buildProductExtraInfo($oBizProduct) {
        $aRes = [];
        //出租车峰期加价标识
        if (TaxiPeakFee::getPoolInstance([$oBizProduct->getProductCategory(), $this->_oAreaInfo->iArea])->getStatus()) {
            $aRes['is_peak_period'] = 1;
        }

        if (!empty($oBizProduct->oProduct->aExtraInfo['start_fence_id'])) {
            $aRes['start_fence_id'] = json_encode($oBizProduct->oProduct->aExtraInfo['start_fence_id']);
        }

        if (!empty($oBizProduct->oProduct->aExtraInfo['stop_fence_id'])) {
            $aRes['stop_fence_id'] = json_encode($oBizProduct->oProduct->aExtraInfo['stop_fence_id']);
        }

        return $aRes;
    }

    /**
     * @return void
     */
    private function _setData() {
        // 设置首屏车型展示数量
        if (!empty($this->_aAthenaResp['extra_info']) && !empty($this->_aAthenaResp['extra_info']['show_car_num'])) {
            $this->_iShowCarNum = $this->_aAthenaResp['extra_info']['show_car_num'] ?? 5;
        }

        // 司乘议价信息
        if (!empty($this->_aAthenaResp['extra_info']) && !empty($this->_aAthenaResp['extra_info']['bargain_config_id'])) {
            $this->buildBargainInfo();
        }
    }


    /**
     * @return int
     */
    public function getShowCarNum() {
        return $this->_iShowCarNum;
    }

    /**
     * @return void
     */
    private function _setHitLuxPremiumProtect() {
        if (!empty($this->_aAthenaResp['extra_info']) && !empty($this->_aAthenaResp['extra_info']['hit_luxr_premium_protect'])) {
            $this->_iHitLuxPremiumProtect = $this->_aAthenaResp['extra_info']['hit_luxr_premium_protect'];
        }
    }

    /**
     * @return void
     */
    private function _setCrowdLabelMap() {
        if (!empty($this->_aAthenaResp['extra_info'])) {
            if (!empty($this->_aAthenaResp['extra_info']['hit_luxr_premium_protect_new'])) {
                $this->_aCrowdLabelMap[self::LUX_PREMIUM_PEOPLE_TAG] = $this->_aAthenaResp['extra_info']['hit_luxr_premium_protect_new'];
            }

            if (!empty($this->_aAthenaResp['extra_info']['hit_exclusive_protect'])) {
                $this->_aCrowdLabelMap[self::FANKUAI_TAG] = $this->_aAthenaResp['extra_info']['hit_exclusive_protect'];
            }

            if (!empty($this->_aAthenaResp['extra_info']['hit_carpool_protect'])) {
                $this->_aCrowdLabelMap[self::CARPOOL_PEOPLE_TAG] = $this->_aAthenaResp['extra_info']['hit_carpool_protect'];
            }
        }
    }

    private function _setClassifyRecForm() {
        AthenaRecommendRecForm::getInstance()->init($this->_aAthenaResp['rec_tab_info'] ?? []);
    }

    /**
     * @return array
     */
    public function getEstimateStripInfo() {
        return $this->_aAthenaResp['estimate_strip_info'] ?? [];
    }

    /**
     * @return int
     */
    public function getHitLuxPremiumProtect() {
        return $this->_iHitLuxPremiumProtect;
    }

    /**
     * @return array
     */
    public function getCrowdLabelMap() {
        return $this->_aCrowdLabelMap;
    }

    /**
     * @return array
     */
    public function getExpectInfo() {
        return $this->_aAthenaResp['expect_info'] ?? [];
    }


    /**
     * @return array
     */
    public function getSupplyInfo() {
        return $this->_aAthenaResp['supply_info'] ?? [];
    }


    /**
     * @return bool 司乘议价是否推荐态
     */
    public function bargainIsRecommendStyle() {
        $_aTopRecommendStyle     = $this->getTopRecommendStyle();
        $_sBargainSceneRecommend = $_aTopRecommendStyle['extra_info']['bargain_scene_recommend'] ?? '';
        return self::IS_SUPPORT == $_sBargainSceneRecommend;
    }

    /**
     * @return bool 司乘议价是否多勾
     */
    public function bargainIsMultiSelect() {
        $aResult = ApolloV2::getInstance()->featureToggle(
            'bargain_recommend_v2_switch',
            array(
                'phone'         => $this->_oPassengerInfo->sPhone,
                'app_version'   => $this->_oCommonInfo->sAppVersion,
                'access_key_id' => $this->_oCommonInfo->iAccessKeyID,
                'city'          => $this->_oAreaInfo->iArea,
                'pid'           => $this->_oPassengerInfo->iPid,
            )
        );
        if ($aResult->allow()) {
            return true;
        }

        return false;
    }

    /**
     * @return array|mixed athena排序策略
     */
    public function getProductSortInfo() {
        return $this->_aAthenaResp['product_sort_info'] ?? [];
    }

    /**
     * @return array 获取athena品类Id到组信息映射
     */
    public function getRecPcToSubGroup() {
        return $this->_aRecPcToSubGroup ?? [];
    }

    /**
     * @return array 获取athena返回的品类折叠状态
     */
    public function getProductFoldTypeMap() {
        return $this->_aProductFoldType ?? [];
    }

    /**
     * @return array 获取athena返回的盒子折叠状态
     */
    public function getSubGroupFoldTypeMap() {
        return $this->_aSubGroupFoldType ?? [];
    }

    /**
     * @return PlatformCompensateData
     */
    private function _getPlatFormCompensationInfo() {
        return new PlatformCompensateData(
            [
                'compensate_status' => CompensationLogic::getInstance()->GetCompensationStatus(),
            ]
        );

    }

    /**
     * @param OrderInfo  品类信息
     * @return array 品类的possible_category_ids
     */
    private function _getPossibleCategoryIds(OrderInfo $oOrderInfo) {
        //优先传可以移动分栏
        if (!empty($this->_aPossibleCategoryMap[$oOrderInfo->iProductCategory])) {
            return $this->_aPossibleCategoryMap[$oOrderInfo->iProductCategory];
        }

        //兜底分栏
        if (!empty($this->_aCategoryIdPcIdMap[$oOrderInfo->iProductCategory])) {
            return [(string)$this->_aCategoryIdPcIdMap[$oOrderInfo->iProductCategory]];
        }

        return [];
    }

    /**
     * @return array
     */
    public function getBargainInfo() {
        return $this->_aBargainSense;
    }

    /**
     * @return void
     */
    protected function buildBargainInfo() {
        if (empty($this->_aBizProductList[OrderEstimatePcId::EstimatePcIdBargain])) {
            return;
        }

        $sConfigId = $this->_aAthenaResp['extra_info']['bargain_config_id'];
        if (empty($sConfigId)) {
            return;
        }

        $oConfResult = Apollo::getInstance()->getConfigResult('bargain-scenes-config', $sConfigId);
        $aList       = $oConfResult->getConfig('scenes_sense')[1]['sense_lists'];
        if (empty($aList) || !is_array($aList) || count($aList) <= 0) {
            return;
        }

        $fBargainEstimateFee = $this->_aBizProductList[OrderEstimatePcId::EstimatePcIdBargain]->getEstimateFee();
        $iSceneType          = 2;
        if (!empty($this->_aBizProductList[OrderEstimatePcId::EstimatePcIdSpecialRate])) {
            if ($fBargainEstimateFee < $this->_aBizProductList[OrderEstimatePcId::EstimatePcIdSpecialRate]->getEstimateFee()) {
                $iSceneType = 1; // 低价
            }
        }

//        if (!empty($this->_aBizProductList[OrderEstimatePcId::EstimatePcIdFastCar]) &&
//            !empty($this->_aBizProductList[OrderEstimatePcId::EstimatePcIdSpecialRate])) {
//            if ($fBargainEstimateFee >= $this->_aBizProductList[OrderEstimatePcId::EstimatePcIdSpecialRate]->getEstimateFee() &&
//                $fBargainEstimateFee < $this->_aBizProductList[OrderEstimatePcId::EstimatePcIdFastCar]->getEstimateFee()) {
//                $iSceneType = 2; // 默认
//            }
//        }
        if (!empty($this->_aBizProductList[OrderEstimatePcId::EstimatePcIdAplus])) {
            if ($fBargainEstimateFee >= $this->_aBizProductList[OrderEstimatePcId::EstimatePcIdAplus]->getEstimateFee()) {
                $iSceneType = 3; // 高价
            }
        }

//        if ($iSceneType <= 0) {
//            return;
//        }
        foreach ($oConfResult->getConfig('scenes_sense')[1]['sense_lists'] as $aSense) {
            if ($aSense['sense_type'] == $iSceneType) {
                $this->_aBargainSense = $aSense;
                $this->_aBargainSense['config_id'] = $sConfigId;
                break;
            }
        }
    }

    /**
     * @param OrderInfo  品类信息
     * @return int       默认分栏id
     */
    private function _getDefaultCategoryId(OrderInfo $oOrderInfo) {
        $iPCid = $oOrderInfo->iProductCategory;

        // 优先盒子分栏
        if (!empty($this->_aPcIDToSubGroupIDMap[$iPCid]) && !empty($this->_aSubGroupIdToCategoryMap[$this->_aPcIDToSubGroupIDMap[$iPCid]])) {
            return $this->_aSubGroupIdToCategoryMap[$this->_aPcIDToSubGroupIDMap[$iPCid]];
        }

        // 兜底分栏
        return $this->_aCategoryIdPcIdMap[$iPCid];
    }
}
