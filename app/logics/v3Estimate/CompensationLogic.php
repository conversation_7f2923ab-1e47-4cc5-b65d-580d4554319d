<?php

namespace PreSale\Logics\v3Estimate;

use BizLib\Client\CompensationDirpcClient;
use Dirpc\SDK\Compensation\TriggerTackReq;
use Disf\SPL\Trace;
use PreSale\Infrastructure\Repository\Tag\TagServiceRepository;
use PreSale\Logics\communicateInfo\disPatchOrderLean\DispatchOrderLeanLogic;
use PreSale\Logics\newFormTab\Tab;
use PreSale\Logics\v3Estimate\multiRequest\AreaInfo;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;
use PreSale\Logics\v3Estimate\multiRequest\CommonInfo;
use PreSale\Logics\v3Estimate\multiRequest\PassengerInfo;
use PreSale\Logics\v3Estimate\multiResponse\PriceAxleABParam;
use Xiaoju\Apollo\Apollo;
use PreSale\Models\businessScene\BusinessScene;

/**
 * class CompensationLogic
 * @package PreSale\Logics\v3Estimate
 */
class CompensationLogic
{
    const CompensationBusiness = 'normal_no_answer_compensation';

    const DEFAULT_NORMAL_NO_ANSWER_EXPERIMENT = 'normal_no_car_compensation_exp';

    private static $_oInstance;

    private static $_bInit;

    /**
     * @var AreaInfo
     */
    private $_oAreaInfo;

    /**
     * @var CommonInfo
     */
    private $_oCommonInfo;

    /**
     * @var PassengerInfo
     */
    private $_oPassengerInfo;

    /**
     * @var BizProduct[]
     */
    private $_aBizProductList;

    private $_aCompensation = [];

    /**
     * @return void
     */
    private function __construct() {}

    /**
     * @return CompensationLogic
     */
    public static function getInstance() {
        if (self::$_oInstance == null) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * @param BizCommonInfo $oBizCommonInfo
     * @param BizProduct[]  $aBizProductMap
     * @return void
     */
    public function init($oBizCommonInfo, $aBizProductMap) {
        if (!self::$_bInit) {
            $this->_oAreaInfo       = $oBizCommonInfo->oAreaInfo;
            $this->_oCommonInfo     = $oBizCommonInfo->oCommonInfo;
            $this->_oPassengerInfo  = $oBizCommonInfo->oPassengerInfo;
            $this->_aBizProductList = $aBizProductMap;

            // 大字版屏蔽无车赔
            if ($oBizCommonInfo->oCommonInfo->iFontScaleType != 0) {
                return;
            }

            
            // 返现表单
            $params = [
                "tab_id"        => $this->_oCommonInfo->sTabId,
                "page_type"     => $this->_oCommonInfo->iPageType,
                "lang"          => $this->_oCommonInfo->sLang,
                "access_key_id" => $this->_oCommonInfo->iAccessKeyID,
                "app_version"   => $this->_oCommonInfo->sAppVersion,
            ];
            if (BusinessScene::isXinZhu($params)) {
                return;
            }

            $oToggle = \Nuwa\ApolloSDK\Apollo::getInstance()->featureToggle(
                'gs_normal_no_answer_change_swicth',
                [
                    'key'           => $oBizCommonInfo->getPassengerID(),
                    'city'          => $oBizCommonInfo->getFromCity(),
                    'pid'           => $oBizCommonInfo->getPassengerID(),
                ]
            );
            if ($oToggle->allow()) {
                $this->_fetchCompensationTiggerTack();
            }
        }
        self::$_bInit = true;
    }

    /**
     * @return void
     */
    private function _fetchCompensationTiggerTack() {
        $aEstimateProducts = array();
        foreach ($this->_aBizProductList as $oBizProduct) {
            $oBillInfo  = $oBizProduct->oPriceInfo->getBillInfo();
            $oOrderInfo = $oBizProduct->getOrderInfo();

            $aEstimateProduct = [
                'product_category'  => $oBizProduct->getProductCategory(),
                'dynamic_total_fee' => $oBillInfo->getDynamicTotalFee(),
                'estimate_fee'      => $oBizProduct->getPriceInfo()->getEstimateFee(),
                'product_id'        => $oOrderInfo->iProductId,
                'require_level'     => $oOrderInfo->iRequireLevel,
                'combo_type'        => $oOrderInfo->iComboType,
                'carpool_type'      => $oOrderInfo->iCarpoolType,
                'level_type'        => $oOrderInfo->iLevelType,
                'is_special_price'  => $oOrderInfo->iIsSpecialPrice,
                'estimate_id'       => $oBillInfo->getEstimateId(),
            ];

            $aEstimateProducts[] = $aEstimateProduct;
        }

        $aCompensationParams = [
            // biz info
            'compensation_businesses' => [self::CompensationBusiness],
            'estimate_products'       => $aEstimateProducts,
            'bubble_trace_id'         => Trace::traceId(),

            // common info
            'lang'                    => $this->_oCommonInfo->sLang,
            'call_car_type'           => $this->_oCommonInfo->iCallCarType,
            'app_version'             => $this->_oCommonInfo->sAppVersion,
            'access_key_id'           => $this->_oCommonInfo->iAccessKeyID,
            'client_type'             => $this->_oCommonInfo->iClientType,

            // loccation info
            'area'                    => $this->_oAreaInfo->iArea,
            'to_area'                 => $this->_oAreaInfo->iToArea,
            'from_lat'                => (string)$this->_oAreaInfo->fFromLat,
            'from_lng'                => (string)$this->_oAreaInfo->fFromLng,
            'to_lat'                  => (string)$this->_oAreaInfo->fToLat,
            'to_lng'                  => (string)$this->_oAreaInfo->fToLng,
            'from_name'               => $this->_oAreaInfo->sFromName,
            'to_name'                 => $this->_oAreaInfo->sToName,

            //user info
            'passenger_id'            => (string)$this->_oPassengerInfo->iPid,
            'phone'                   => $this->_oPassengerInfo->sPhone,

            //order info
            'order_type'              => $this->_oCommonInfo->iOrderType,
        ];

        $aResp = (new CompensationDirpcClient())->triggerTack($aCompensationParams);

        if (0 == $aResp['err_no']) {
            $this->_aCompensation = $aResp['data'];
        }

        // 命中无车赔配置则写功能点监控日志
        if (CompensationLogic::getInstance()->GetCompensationStatus()) {
            FeatureRecord::getInstance()->RecordFeature(FeatureRecord::FEATURE_NORMAL_COMPENSATION_HIT);
        }
    }

    /**
     * 赔付规则命中状态 0:未命中 1:命中
     * @return int
     */
    public function GetCompensationStatus(): int {
        if (empty($this->_aCompensation)
            || empty($this->_aCompensation['compensation_info'])
            || empty($this->_aCompensation['compensation_info']['normal_no_answer_compensation'])
        ) {
            return 0;
        }

        return 1;
    }
}
