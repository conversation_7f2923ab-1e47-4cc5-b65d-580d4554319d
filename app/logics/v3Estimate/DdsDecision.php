<?php

namespace PreSale\Logics\v3Estimate;

use B<PERSON><PERSON>ib\Client\EstimateDecisionClient;
use Biz<PERSON>ib\Utils\ApolloHelper;
use BizLib\Utils\Language;
use BizLib\Utils\ProductCategory;
use Dirpc\SDK\EstimateDecision\ProductCell;
use Disf\SPL\Trace;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;
use Dirpc\SDK\EstimateDecision\DiscountInfo;
use Dirpc\SDK\EstimateDecision\DynamicInfo;
use PreSale\Logics\v3Estimate\multiRequest\OrderInfo;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutBuilder;
use Dukang\PropertyConst\Order\OrderEstimatePcId;

/**
 * class DdsDecision
 * @package PreSale\Logics\v3Estimate
 */
class DdsDecision
{
    private static $_oInstance;

    private static $_bInit;

    const DECISION_TYPE_DEFAULT = 1;

    /**
     * @var BizCommonInfo
     */
    private $_oBizCommonInfo;

    private $_aProductsDecisionResp;

    //private $_aGuideProductResp;

    /**
     * @var int[] 单车型排序列表
     */
    private $_aSingleProductsSort;

    /**
     * @var array map[pcID] int
     */
    private $_aPcIDToSelectType;

    /**
     * @var BizProduct[]
     */
    private $_aBizProductList;

    /**
     * @var int[]
     */
    private $_aRemovedProducts;

    /**
     * 单例, 屏蔽构造函数
     * @return void
     */
    private function __construct() {}
    /**
     * @return DdsDecision
     */
    public static function getInstance() {
        if (self::$_oInstance == null) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * @param BizCommonInfo $oBizCommonInfo  $oBizCommonInfo
     * @param BizProduct[]  $aBizProductList $aBizProductList
     * @return void
     */
    public function init(BizCommonInfo $oBizCommonInfo, $aBizProductList) {
        if (!self::$_bInit) {
            $this->_oBizCommonInfo  = $oBizCommonInfo;
            $this->_aBizProductList = $aBizProductList;
            $this->_getDecisionInfo();
        }

        self::$_bInit = true;
    }

    /**
     * @param BizProduct[] $aBizProductsMap $aBizProductsMap
     * @return mixed
     */
    public function filterProducts($aBizProductsMap) {
        if (!empty($this->_aRemovedProducts)) {
            foreach ($aBizProductsMap as $iPcID => $oBizProduct) {
                if (in_array($iPcID, $this->_aRemovedProducts)) {
                    unset($aBizProductsMap[$iPcID]);
                }
            }
        }

        $this->_dealMutex($aBizProductsMap);

        return $aBizProductsMap;
    }

    /**
     * @param BizProduct[] $aBizProductsMap $aBizProductsMap
     * @return void
     */
    private function _dealMutex(&$aBizProductsMap) {
        $aMutexMap = [
            ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTE    => ProductCategory::PRODUCT_CATEGORY_LOW_PRICE_CARPOOL,
            ProductCategory::PRODUCT_CATEGORY_JININGKEQI      => ProductCategory::PRODUCT_CATEGORY_LOW_PRICE_CARPOOL,
            OrderEstimatePcId::EstimatePcIdFastTaxi           => OrderEstimatePcId::EstimatePcIdTaxiMarketisationPutong,
            ProductCategory::PRODUCT_CATEGORY_HANDPICKED_FAST => ProductCategory::PRODUCT_CATEGORY_SPACIOUS_CAR,
        ];

        foreach ($aMutexMap as $iWin => $iLose) {
            if (!empty($aBizProductsMap[$iWin])) {
                unset($aBizProductsMap[$iLose]);
            }
        }
    }


    /**
     * @return array
     */
    public function getSelectType() {
        return $this->_aPcIDToSelectType;
    }

    /**
     * 有序的关联数组
     * @return array map[pcId]bool
     */
    public function getSortedProductsMap() :array {
        if (!is_array($this->_aSingleProductsSort)) {
            return [];
        }

        $aProductsMap = [];
        foreach ($this->_aSingleProductsSort as $iPcId) {
            $aProductsMap[$iPcId] = true;
        }

        return $aProductsMap;
    }

    /**
     * 有序的关联数组
     * @param array $aBizProductMap
     * @return array map[pcId]bool
     */
    public function getSortedProductsMapV2($aBizProductMap) {
        $aProductsMap = [];

        if (empty($this->_aSingleProductsSort) || !is_array($this->_aSingleProductsSort)) {
            // 兜底车型列表排序
            $aSceneConf = ApolloHelper::getConfigContent('car_sort', 'anycar_sort_v3');
            if (!empty($aSceneConf['products_sort']) && is_array($aSceneConf['products_sort'])) {
                $aSort = array_column($aSceneConf['products_sort'], 'product_category');
                foreach ($aSort as $iPcId) {
                    if (!empty($aBizProductMap)) {
                        $aProductsMap[$iPcId] = true;
                    }
                }

                return $aProductsMap;
            }
        }

        foreach ($this->_aSingleProductsSort as $iPcId) {
            $aProductsMap[$iPcId] = true;
        }

        return $aProductsMap;
    }

    /**
     * 获取品类权重
     * @return array map[pcId]bool
     */
    public function getProductWeight() :array {
        if (!is_array($this->_aSingleProductsSort)) {
            return [];
        }

        $aIdList         = array_reverse($this->_aSingleProductsSort);
        $aProductsWeight = [];
        foreach ($aIdList as $i => $iPcId) {
            $aProductsWeight[$iPcId] = $i;
        }

        return $aProductsWeight;
    }

    /**
     * @return void
     */
    private function _getDecisionInfo() {
        $aDDSReq = $this->_formatDecisionRequest();
        $aResp   = (new EstimateDecisionClient())->decision($aDDSReq);

        if (0 == $aResp['errno'] && !empty($aResp['data']['product_list'])) {
            $this->_aProductsDecisionResp = $aResp['data']['product_list'];
            //格式化数据
            $this->_formatResp();
        }

    }

    /**
     * @return void
     */
    private function _formatResp() {
        //设置单品类信息
        foreach ($this->_aProductsDecisionResp as $aProduct) {
            if (!empty($aProduct['is_selected'])) {
                $this->_aPcIDToSelectType[$aProduct['product_category']] = 1;
            }

            if ($aProduct['remove_flag']) {
                $this->_aRemovedProducts[] = $aProduct['product_category'];
            }
        }

        $this->_aSingleProductsSort = array_column($this->_aProductsDecisionResp, 'product_category');
    }


    /**
     * 格式化dds 请求入参
     * @return array
     */
    private function _formatDecisionRequest() {
        $oPassengerInfo = $this->_oBizCommonInfo->oPassengerInfo;
        $oAreaInfo      = $this->_oBizCommonInfo->oAreaInfo;
        $oCommonInfo    = $this->_oBizCommonInfo->oCommonInfo;
        $aUserInfo      = [
            'id'             => (int)$oPassengerInfo->iPid,
            'uid'            => (int)$oPassengerInfo->iUid,
            'pid'            => (int)$oPassengerInfo->iPid,
            'phone'          => (string)$oPassengerInfo->sPhone,
            'call_car_phone' => (string)$oPassengerInfo->sCallCarPhone,
        ];

        $aCommonInfo  = [
            'start_lat'         => (float)$oAreaInfo->fFromLat,
            'start_lng'         => (float)$oAreaInfo->fFromLng,
            'to_lat'            => (float)$oAreaInfo->fToLat,
            'to_lng'            => (float)$oAreaInfo->fToLng,
            'client_type'       => (int)$oCommonInfo->iClientType,
            'city'              => (int)($oAreaInfo->iArea),
            'to_city'           => (int)($oAreaInfo->iToArea),
            'county'            => (int)($oAreaInfo->iFromCounty),
            'menu_id'           => (string)($oCommonInfo->sMenuId),
            'app_version'       => (string)($oCommonInfo->sAppVersion),
            'channel'           => (int)($oCommonInfo->sChannel),
            'estimate_trace_id' => Trace::traceId(),
            'map_type'          => (string)($oAreaInfo->sMapType),
            'order_type'        => (string)($oCommonInfo->iOrderType),
            'access_key_id'     => (int)($oCommonInfo->iAccessKeyID),
            'page_type'         => (int)($oCommonInfo->iPageType),
            'lang'              => (string)Language::getLanguage(),
            'tab_id'            => (string)$oCommonInfo->sTabId,
        ];
        $aProductInfo = [];
        foreach ($this->_aBizProductList as $oBizProduct) {
//            //过滤在导流位的产品, 导流产品无需DDS决策，即不使用 guide_product_list这一项返回决定导流位
//            if (OrderInfo::FORM_SHOW_TYPE_GUIDE == $oBizProduct->oProduct->oOrderInfo->iFormShowType) {
//                continue;
//            }
            $oOrderInfo = $oBizProduct->oProduct->oOrderInfo;
            $oBillInfo  = $oBizProduct->getBillInfo();

            $aProductItem = array();
            //product info
            $aProductItem['product_id']            = $oOrderInfo->iProductId;
            $aProductItem['business_id']           = $oOrderInfo->iBusinessId;
            $aProductItem['require_level']         = $oOrderInfo->iRequireLevel;
            $aProductItem['combo_type']            = $oOrderInfo->iComboType;
            $aProductItem['is_special_price']      = $oOrderInfo->iIsSpecialPrice;
            $aProductItem['carpool_type']          = $oOrderInfo->iCarpoolType;
            $aProductItem['route_type']            = $oOrderInfo->iRouteType;
            $aProductItem['is_dual_carpool_price'] = $oOrderInfo->bIsDualCarpoolPrice;
            $aProductItem['estimate_id']           = $oOrderInfo->sEstimateID;
            $aProductItem['carpool_price_type']    = (int)($oOrderInfo->iCarpoolPriceType);
            $aProductItem['level_type']            = (int)($oOrderInfo->iLevelType);
            $aProductItem['product_category']      = $oOrderInfo->iProductCategory;

            //price-info
            $aProductItem['cap_price']     = (float)$oBillInfo->getCapPrice(); //拼车新产品需要
            $aProductItem['estimate_fee']  = (float)$oBizProduct->getPriceInfo()->getEstimateFee();
            $aProductItem['pre_total_fee'] = (float)$oBizProduct->getPriceInfo()->getBillInfo()->getPreTotalFee();
            $aProductItem['driver_metre']  = (int)$oBillInfo->getDriverMetre();
            $aProductItem['discount']      = new DiscountInfo();
            $aProductItem['dynamic']       = new DynamicInfo();

            //mock athena info -- 写死为1 后面会根据Athena的准入结果做过滤逻辑
            $aProductItem['athena_capability']['sp_capability'] = 1;
            $aProductItem['athena_capability']['unione_youxuan_capability'] = 1;
            $aProductItem['athena_capability']['aplus_flash_capability']    = 1;
            $aProductItem['athena_capability']['sp_flash_disabled_type']    = 0;

            $aProductItem['price_capability']          = [
                'sp_open' => $oBillInfo->getHistoryExtraMap()['sp_open'] ?? 0,
                'sp_rate' => 1, //写死即可
            ];
            $aProductItem['taxi_sp_discount_fee_info'] = [
                'sp_open'              => $oBillInfo->getTaxiSpDiscountFee() > 0 ? 1 : 0,
                'taxi_sp_discount_fee' => $oBillInfo->getTaxiSpDiscountFee(),
            ];

            $aMultiRequireProduct = [];
            $aPreferenceProduct   = [];
            foreach ($this->_oBizCommonInfo->oCommonInfo->aMultiRequireProducts as $aProduct) {
                $aMultiRequireProduct[] = new ProductCell($aProduct);
            }

            $aProductItem['multi_require_product'] = array_values($aMultiRequireProduct);
            //$aProductItem['form_show_type']        = $aInfo['order_info']['form_show_type'];
            //$aProductItem['long_rent_type']        = (int)($aOrderNTuple['long_rent_type'] ?? 0);
            //$aProductItem['is_default']            = $this->_filterIsDefault($aProductItem);
            //$aProductItem['total_fee_without_discount'] = $aBillInfo['total_fee_without_discount'] ?? 0.0;
            $aProductInfo[] = $aProductItem;
        }

        $aDecisionReq['user']          = $aUserInfo;
        $aDecisionReq['common']        = $aCommonInfo;
        $aDecisionReq['products']      = $aProductInfo;
        $aDecisionReq['decision_type'] = self::DECISION_TYPE_DEFAULT;
        foreach ($this->_oBizCommonInfo->oCommonInfo->aMultiRequireProducts as $aProduct) {
            if ($aProduct['is_selected']) {
                $aPreferenceProduct[] = new ProductCell($aProduct);
            }
        }

        $aDecisionReq['preference_product'] = $aPreferenceProduct;

        return $aDecisionReq;
    }
}
