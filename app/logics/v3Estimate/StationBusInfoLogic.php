<?php
namespace PreSale\Logics\v3Estimate;

use BizCom<PERSON>\Logics\Order\FieldOrderNTuple;
use BizCommon\Models\Order\OrderStopoverPoints;
use BizLib\Client\CarPoolOpenApiClient;
use BizLib\Constants\Common;
use BizLib\ErrCode\Code;
use BizLib\Exception\ExceptionWithResp;
use Dirpc\SDK\PriceApi\EstimateNewFormData;
use Dirpc\SDK\CarpoolOpenApi\GeoPoint;
use PreSale\Logics\carpool\InterCity;
use PreSale\Logics\estimatePriceV2\EstimateDegradeCode;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;
use PreSale\Logics\v3Estimate\multiRequest\Product;
use TripcloudCommon\Utils\Product as TripcloudProduct;
use BizLib\Config;
use BizCommon\Constants\OrderNTuple;

/**
 * Created by PhpStorm.
 * User: didi
 * Date: 2023/11/14
 * Time: 7:57 PM
 */
class StationBusInfoLogic
{
    private $_oBizCommonInfo;
    private $_aBizProductMap;

    private $_bFlag = false; // 是否有站点巴士品类

    /**
     * @param BizCommonInfo $oBizCommonInfo
     * @param $aBizProductMap
     */
    public function __construct(BizCommonInfo $oBizCommonInfo,$aBizProductMap) {
        $this->_oBizCommonInfo = $oBizCommonInfo;
        $this->_aBizProductMap = $aBizProductMap;
    }

    /**
     * @param
     * @return array
     */
    public function getBusInfo() {

        $aBusPcList = [];
        if (count($this->_aBizProductMap) < 1) {
            $this->_bFlag = true;
            return null;
        }

        foreach ($this->_aBizProductMap as $aProduct) {
            if ($aProduct->oProduct != null && $aProduct->oProduct->oOrderInfo != null) {
                $aOrderInfo = $aProduct->oProduct->oOrderInfo;
                if (OrderNTuple::CARPOOL_TYPE_INTERCITY_STATION == $aOrderInfo->iCarpoolType) {
                    $aBusPcList[] = (string)$aOrderInfo->iProductId;
                }
            }
        }

        if (count($aBusPcList) < 1) {
            $this->_bFlag = true;
            return null;
        }

        $aFrom = new GeoPoint([
            'lng' => $this->_oBizCommonInfo->getAreaInfo()->fFromLng,
            'lat' => $this->_oBizCommonInfo->getAreaInfo()->fFromLat,
        ]);
        $aTo = new GeoPoint([
            'lng' => $this->_oBizCommonInfo->getAreaInfo()->fToLng,
            'lat' => $this->_oBizCommonInfo->getAreaInfo()->fToLat,
        ]);

        $aMultiParams = [
            'passenger_id'    => (string)$this->_oBizCommonInfo->getPassengerID(),
            'city_id'         => $this->_oBizCommonInfo->getAreaInfo()->iArea,
            'product_id_list' => $aBusPcList,
            'from_xy'         => $aFrom,
            'to_xy'           => $aTo,
        ];

        $_oClient = new CarPoolOpenApiClient(CarPoolOpenApiClient::MODULE_NAME);
        $aRet = $_oClient->busBubbleRecommendShift($aMultiParams);

        if (isset($aRet['errno']) && $aRet['errno'] == 0 ) {
            $aData = $aRet['data'];
            if (!isset($aData)
                || !isset($aData['recommend'])
                || $aData['recommend'] <= 0
                || !isset($aData['recommend_info_list'])
                || count($aData['recommend_info_list']) < 1) {
                return null;
            }

            foreach ($aData['recommend_info_list'] as $aRecommend) {
                if (!isset($aRecommend)
                    || !isset($aRecommend['on_station'])
                    || !isset($aRecommend['off_station'])
                    || !isset($aRecommend['shift_id'])
                    || !isset($aRecommend['route_group'])
                    || !isset($aRecommend['product_id'])
                    || !isset($aRecommend['departure_time'])) {
                    continue;
                }

                return $aRecommend;
            }
        }

        return null;
    }


    /**
     * @param  BizProduct[] $aBizProductMap
     * @param  array        $oBusInfo
     * @return array
     */
    public function rewrite($aBizProductMap, $oBusInfo) {
        if ($this->_bFlag) {
            // 站点巴士无品类 true: 没有站点巴士
            return $aBizProductMap;
        }

        if (!isset($oBusInfo)) {
            foreach ($aBizProductMap as $aProduct) {
                $oOrder = $aProduct->oProduct->oOrderInfo;
                if (OrderNTuple::CARPOOL_TYPE_INTERCITY_STATION == $oOrder->iCarpoolType) {
                    unset($aBizProductMap[$oOrder->iProductCategory]);
                }
            }
            return $aBizProductMap;
        }
        foreach ($aBizProductMap as $aProduct) {
            $oOrder = $aProduct->oProduct->oOrderInfo;
            if ($oOrder->iCarpoolType == OrderNTuple::CARPOOL_TYPE_INTERCITY_STATION) {
                if ($oOrder->iProductId == $oBusInfo['product_id']) {
                    $oOrder->iOrderType     = 1;
                    $oOrder->iComboId       = $oBusInfo['route_group'];
                    $oOrder->iDepartureTime = $oBusInfo['departure_time'];
                    $aExtraInfo = $oBusInfo['extra_info'];

                    // 大巴信息存储
                    $oOrder->aBusShiftInfo = [
                        'shift_id'               => $oBusInfo['shift_id'],
                        'is_best_shift'          => (int)$aExtraInfo['optimal_shift'] ?? 0,
                        'optimal_shift_dist'     => (int)$aExtraInfo['optimal_shift_dist'] ?? 0,
                        'remain_carry_child_num' => $aExtraInfo['remain_carry_child_num'] ?? '0',
                        'max_inventory'          => (int)$aExtraInfo['remain_seats'] ?? 5,
                        'exp_group'              => (int)$aExtraInfo['exp_group'] ?? 0,
                    ];
                    // 多点到门班次不满足新版本就过滤掉
                    if (1 == (int)$aExtraInfo['route_scene_type']
                        && !InterCity::isPointToDoorGuideNewAppVersion($this->_oBizCommonInfo->oCommonInfo->iAccessKeyID, $this->_oBizCommonInfo->oCommonInfo->sAppVersion, $this->_oBizCommonInfo->getPassengerPhone(), $this->_oBizCommonInfo->getPassengerID(), $this->_oBizCommonInfo->getAreaInfo()->iArea)
                    ) {
                        unset($aBizProductMap[$oOrder->iProductCategory]);
                    } else {
                        $aProduct->oProduct->rewriteRequest($oBusInfo);
                    }
                } else {
                    unset($aBizProductMap[$oOrder->iProductCategory]);
                }
            }
        }
        return $aBizProductMap;
    }
}
