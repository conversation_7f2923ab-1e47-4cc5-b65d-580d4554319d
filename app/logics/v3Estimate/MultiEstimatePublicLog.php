<?php

namespace PreSale\Logics\v3Estimate;

use BizCommon\Constants\OrderNTuple;
use Biz<PERSON>om<PERSON>\Utils\Horae;
use BizCommon\Utils\Order;
use BizLib\Constants\OrderSystem;
use BizLib\Utils\Language;
use BizLib\Utils\ProductCategory;
use BizLib\Utils\PublicLog;
use Dirpc\SDK\PreSale\MultiEstimatePriceRequest;
use Dirpc\SDK\PreSale\NewFormEstimateData;
use Dirpc\SDK\PriceApi\EstimateNewFormCouponInfo;
use Dirpc\SDK\PriceApi\EstimateNewFormDiscountSet;
use Disf\SPL\Trace;
use PreSale\Logics\carpool\InterCity;
use PreSale\Logics\estimatePrice\multiRequest\Product;
use PreSale\Logics\newFormTab\Tab;
use PreSale\Logics\scene\custom\CustomLogic;
use PreSale\Logics\taxi\TaxiPeakFee;
use PreSale\Logics\v3Estimate\FormAB\NewStyleFormABParam;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutBuilder;
use PreSale\Logics\v3Estimate\multiResponse\layout\PriceSortExp;
use PreSale\Logics\v3Estimate\multiResponse\PreRender\CarpoolPreMatchLogic;

/**
 * 多预估public日志组建
 * Class MultiEstimatePublicLog.
 */
class MultiEstimatePublicLog
{
    const PRICE_DESC_NO_REWARDS      = 0;
    const PRICE_DESC_ONLY_REWARDS    = 1;
    const PRICE_DESC_INCLUDE_REWARDS = 2;

    const EXPRESS_LINE_LEVEL_TYPE = 116;

    /**
     * @var MultiEstimatePriceRequest
     */
    private $_oRequest;

    private $_oBizCommonInfo;

    private $_aBizProductMap;

    private $_aEstimateDataMap;

    /**
     * @var array
     */
    private $_aBillsForTcFeeMonitor = [];

    /**
     * @var array
     */
    private $_aLayout;
    /**
     * @var array
     */
    private $_aRecLayout;

    private $_aFilterResult;

    /**
     * MultiEstimatePublicLog constructor.
     *
     * @param MultiEstimatePriceRequest $oRequest         预估入参
     * @param BizCommonInfo             $oBizCommonInfo   $oBizCommonInfo
     * @param BizProduct[]              $aBizProductMap   $aBizProductMap
     * @param NewFormEstimateData[]     $aEstimateDataMap $aEstimateDataMap
     * @param array                     $aLayout          布局信息
     * @param array                     $aRecLayout       推荐框布局信息
     * @param array                     $aFilterResult    筛选器信息
     */
    public function __construct($oRequest, $oBizCommonInfo, $aBizProductMap, $aEstimateDataMap, $aLayout, $aRecLayout, $aFilterResult) {
        $this->_oRequest         = $oRequest;
        $this->_oBizCommonInfo   = $oBizCommonInfo;
        $this->_aBizProductMap   = $aBizProductMap;
        $this->_aEstimateDataMap = $aEstimateDataMap;
        $this->_aLayout          = $aLayout;
        $this->_aRecLayout       = $aRecLayout;
        $this->_aFilterResult    = $aFilterResult;
    }

    /**
     * 写多预估流程的public 日志.
     * @return void
     */
    public function multiWritePublicLog() {
        // 记录预估阶段最终会展示的产品线 (分品类记录)
        $this->_writeOrderEstimateInfo();
        // 写预估public日志  (分品类记录)
        $this->_writeOrderEstimatePrice();

        // 写预估样式信息, 品类外信息
        $this->_writeEstimateExtraInfo();
        //写表单盒子。推荐数据
        $this->_writeLayoutInfo();
        // 出租车业务线写public日志
        //$this->_writeTaxiEstimatePrice();
    }


    /**
     * 记录layout的数据
     * 且仅记录预估阶段最终会展示的产品线
     * @return void
     */
    private function _writeLayoutInfo() {
        if (Tab::isHitClassifyTabByTabId($this->_oBizCommonInfo->oCommonInfo->sTabId)) {
            $oClassifyFS    = new ClassifyFirstScreen($this->_oRequest, $this->_mergeLayout($this->_aRecLayout, $this->_aLayout), $this->_oBizCommonInfo, $this->_aBizProductMap);
            $this->_aLayout = $oClassifyFS->getFirstScreenProducts();
        }

        $classifyFoldLogic = ClassifyFoldLogic::getInstance();
        $iCurSort          = 1;
        foreach ($this->_aLayout as $aLayout) {
            foreach ($aLayout['groups'] as $aGroup) {
                $this->_writeLayoutGroupInfo($aLayout,$aGroup,$iCurSort);

                $aGroupIDList = $this->_getChildGroupIDs($aGroup['products'],$aLayout['category_id']??0);
                if (!empty($aGroupIDList)) {
                    foreach ($aGroupIDList as $sGroupId) {
                        $aGroupLayout = $classifyFoldLogic->getAFinalInBoxLayoutByGroupId($sGroupId);
                        if (!empty($aGroupLayout)) {
                            $this->_writeLayoutGroupInfo($aGroupLayout,$aGroupLayout['groups'][0],$iCurSort,1);
                        }
                    }
                }

                $iCurSort++;
            }
        }
    }

    /**
     * @param $aLayout
     * @param $aRecLayout
     * @return array
     */
    private function _mergeLayout($aRecLayout, $aLayout) {
        if (empty($aRecLayout)) {
            return $aLayout;
        }
        return array_merge($aRecLayout, $aLayout);
    }

        /**
     * @return int
     */
    private function _getVXForm() {
        if (Tab::isHitClassifyTabByTabId($this->_oBizCommonInfo->oCommonInfo->sTabId)) {
            return 3;
        }

//        $oExp = ExperimentLogic::getInstance()->getExpResult(
//            ExperimentLogic::EXP_NAME_SINK_FORM,
//            [
//                'uid'           => $this->_oBizCommonInfo->getPassengerUID(),
//                'pid'           => $this->_oBizCommonInfo->getPassengerID(),
//                'phone'         => $this->_oBizCommonInfo->getPassengerPhone(),
//                'city'          => $this->_oBizCommonInfo->getFromCity(),
//                'key'           => $this->_oBizCommonInfo->getPassengerID(),
//                'access_key_id' => $this->_oBizCommonInfo->getAccessKeyID(),
//                'app_version'   => $this->_oBizCommonInfo->getAppVersion(),
//            ]
//        );
//        if (!empty($oExp) && $oExp->allow()) {
//            return 2;
//        }

        return 0;
    }

    /**
     * @param array $aPcIDList $aPcIDList
     * @return string
     */
    private function _genProductsEID($aPcIDList) {
        $aEIDList = [];
        foreach ($aPcIDList as $iPcID) {
            if (empty($this->_aBizProductMap[$iPcID])) {
                continue;
            }

            $aEIDList[] = $this->_aBizProductMap[$iPcID]->getEstimateID();
        }

        return implode(',', $aEIDList);
    }

    /**
     * @param array $aPcIDList $aPcIDList
     * @return int
     */
    private function _getSingleProductCategory($aPcIDList) {
        if (empty($aPcIDList) || count($aPcIDList) > 1) {
            return 0;
        }

        foreach ($aPcIDList as $iPcID) {
            return $iPcID;
        }

        return 0;
    }


    /**
     * 此前依据老的aInfo记录日志容易出错，新增一份依据新的aInfo记录的日志
     * 且仅记录预估阶段最终会展示的产品线
     * @return void
     */
    private function _writeOrderEstimateInfo() {

        $oAreaInfo = $this->_oBizCommonInfo->oAreaInfo;
        //$oProduct =
        foreach ($this->_aEstimateDataMap as $oEstimateData) {
            $oBizProduct        = $this->_aBizProductMap[$oEstimateData->getProductCategory()];
            $oProduct           = $oBizProduct->oProduct;
            $aEstimateStatistic = [
                'opera_stat_key' => 'g_order_estimate_info',
                'pLang'          => $this->_oRequest->getLang() ?? Language::ZH_CN,
                'estimate_id'    => $oEstimateData->getProductCategory(),
                'area'           => $oAreaInfo->iArea,
                'product_id'     => $oProduct->oOrderInfo->iProductId,
                'require_level'  => $oProduct->oOrderInfo->iRequireLevel,
                'select_type'    => $oEstimateData->getIsSelected(),
                'sub_group_id'   => DecisionV2Service::getInstance()->getSubGroupIDByPcID($oBizProduct->getProductCategory()),
            ];

            PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);
        }
    }

    /**
     * @return void
     */
    private function _writeOrderEstimatePrice() {
        // 获取三方价格监控需要的费用信息
        $this->_getFeeForTripcloud();
        foreach ($this->_aBizProductMap as $oBizProduct) {
            if (empty($oBizProduct->getEstimateID())) {
                continue;
            }

            $oCurEstimateData = null;
            foreach ($this->_aEstimateDataMap as $oEstimateData) {
                if ($oBizProduct->getEstimateID() == $oEstimateData->getEstimateId()) {
                    $oCurEstimateData = $oEstimateData;
                    break;
                }
            }

            $this->_writePublicLog($this->_oBizCommonInfo, $oBizProduct, $oCurEstimateData);
            $this->_writeLogForRealTimeMonitor($oBizProduct);
        }
    }

    /**
     * 和预估品类data平级的数据存在这里
     * @return void
     */
    private function _writeEstimateExtraInfo() {
        $aEstimateStatistic = [
            'opera_stat_key' => 'g_estimate_overall_price',
            'filter_style'   => AthenaRecommend::getInstance()->iFilterStyle,
            'area'           => $this->_oBizCommonInfo->oAreaInfo->iArea,
            'access_key_id'  => $this->_oBizCommonInfo->oCommonInfo->iAccessKeyID,
            'app_version'    => $this->_oBizCommonInfo->oCommonInfo->sAppVersion,
            'page_type'      => $this->_oBizCommonInfo->oCommonInfo->iPageType,
            'trace_id'       => Trace::traceId(),
        ];

        if ($this->_aFilterResult['filter_recommend']) {
            $aEstimateStatistic['filter_list'] = json_encode($this->_aFilterResult['filter_recommend']['filter_list']);
        } elseif ($this->_aFilterResult['filter_normal']) {
            $aEstimateStatistic['filter_list'] = json_encode($this->_aFilterResult['filter_normal']['filter_list']);
        }

        if (!empty(OperationLogic::getInstance($this->_oBizCommonInfo->getApolloParams(null))->getOperationListByKey(OperationLogic::RIDE_PREFERENCE))) {
            $aEstimateStatistic['ride_preference'] = 1;
        }
        if (!empty(AthenaRecommend::getInstance()->getBargainInfo()['sense_type'])) {
            $aEstimateStatistic["bargain_sense_type"] = AthenaRecommend::getInstance()->getBargainInfo()['sense_type'];
            $aEstimateStatistic["bargain_config_id"] = AthenaRecommend::getInstance()->getBargainInfo()['config_id'];
        }

        // 建设功能点监控
        $aFeatureList = FeatureRecord::getInstance()->GetFeatureList();
        foreach ($aFeatureList as $sFeatureName => $iFlag) {
            $aEstimateStatistic[$sFeatureName] = $iFlag;
        }

        PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);
    }

    /**
     * @param array   $aInfo          $aInfo
     * @param Product $oProduct       $oProduct
     * @param array   $aResponseInfos $aResponseInfos
     * @return void
     */
    private function _writeTaxiPublicLog(array $aInfo, Product $oProduct, array $aResponseInfos) {

        $sCarLevel     = $aInfo['order_info']['require_level'];
        $aBillInfo     = $aInfo['bill_info']['bills'][$sCarLevel];
        $aEstimateData = $this->_getEstimateData($aInfo, $aResponseInfos);

        $aEstimateStatistic = [
            'opera_stat_key'        => 'taxi_order_estimate_price',
            'access_key_id'         => $oProduct->oCommonInfo->iAccessKeyID,
            'channel'               => $oProduct->oCommonInfo->sChannel,
            'pid'                   => $oProduct->oPassengerInfo->iPid,
            'product_id'            => $oProduct->oOrderInfo->iProductId,
            'combo_type'            => $oProduct->oOrderInfo->iComboType,
            'require_level'         => $sCarLevel,
            'area'                  => $oProduct->oAreaInfo->iArea,
            'to_area'               => $oProduct->oAreaInfo->iToArea,
            'scene_type'            => $oProduct->oOrderInfo->iSceneType,
            'total_fee'             => $aBillInfo['total_fee'] ?? 0,
            'pre_total_fee'         => $aBillInfo['pre_total_fee'] ?? 0,
            'dynamic_total_fee'     => $aBillInfo['dynamic_total_fee'] ?? 0,
            'cap_price'             => $aBillInfo['cap_price'] ?? 0,
            'estimate_fee'          => $aInfo['activity_info'][0]['estimate_fee'] ?? 0,
            'assign_type'           => $aInfo['order_info']['n_tuple']['assign_type'],
            'level_type'            => $aInfo['order_info']['n_tuple']['level_type'],
            'is_special_price'      => $oProduct->oOrderInfo->iIsSpecialPrice ? 'true' : 'false',
            'is_anycar'             => $aInfo['order_info']['n_tuple']['is_anycar'],
            'carpool_type'          => $oProduct->oOrderInfo->iCarpoolType,
            'product_category'      => $oProduct->oOrderInfo->iProductCategory,
            'select_type'           => $aEstimateData['select_type'],
            'recommend_type'        => $aEstimateData['recommend_type'],
            'estimate_trace_id'     => Trace::traceId(),
            'estimate_id'           => $aInfo['order_info']['estimate_id'],
            'fee_msg'               => $aEstimateData['fee_msg'],
            //车大联盟标识
            'spacious_car_alliance' => $oProduct->oOrderInfo->iSpaciousCarAlliance,
        ];

        PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);
    }


    /**
     * @param BizCommonInfo       $oBizCommonInfo $oBizCommonInfo
     * @param BizProduct          $oBizProduct    $oBizProduct
     * @param NewFormEstimateData $oEstimateData  $oEstimateData
     * @return void
     */
    private function _writePublicLog($oBizCommonInfo, $oBizProduct, $oEstimateData) {
        $oAreaInfo      = $oBizCommonInfo->oAreaInfo;
        $oCommonInfo    = $oBizCommonInfo->oCommonInfo;
        $oPassengerInfo = $oBizCommonInfo->oPassengerInfo;
        $oProduct       = $oBizProduct->oProduct;
        $oBillInfo      = $oBizProduct->getBillInfo();
        $oCarpoolFailExtendInfo = $oBizProduct->getCarpoolFailPriceInfo();

        $aEstimateStatistic = [
            'opera_stat_key'                       => 'g_order_estimate_price',
            'is_v3_form'                           => 1,
            'imei'                                 => $oCommonInfo->sImei,
            'appversion'                           => $oCommonInfo->sAppVersion,
            'client_type'                          => $oCommonInfo->iClientType,
            'access_key_id'                        => $oCommonInfo->iAccessKeyID,
            'channel'                              => $oCommonInfo->sChannel,
            'pLang'                                => $oCommonInfo->sLang,
            'pid'                                  => $oPassengerInfo->iPid,
            'phone'                                => $oPassengerInfo->sPhone,
            'biz_user_type'                        => $oPassengerInfo->iUserType,
            'menu_id'                              => $oProduct->oOrderInfo->sMenuID,
            'page_type'                            => $oProduct->oOrderInfo->iPageType,
            'origin_page_type'                     => $oProduct->oOrderInfo->iOriginPageType,
            'call_car_type'                        => $oProduct->oOrderInfo->iCallCarType,
            'product_id'                           => $oProduct->oOrderInfo->iProductId,
            'combo_type'                           => $oProduct->oOrderInfo->iComboType,
            'require_level'                        => $oProduct->oOrderInfo->iRequireLevel,
            'area'                                 => $oAreaInfo->iArea,
            'to_area'                              => $oAreaInfo->iToArea,
            'district'                             => $oAreaInfo->iDistrict,
            'flng'                                 => $oAreaInfo->fFromLng,
            'flat'                                 => $oAreaInfo->fFromLat,
            'tlng'                                 => $oAreaInfo->fToLng,
            'tlat'                                 => $oAreaInfo->fToLat,
            'current_lng'                          => $oAreaInfo->fCurLng,
            'current_lat'                          => $oAreaInfo->fCurLat,
            'starting_poi_id'                      => $oAreaInfo->sFromPoiId,
            'dest_poi_id'                          => $oAreaInfo->sToPoiId,
            'choose_f_searchid'                    => $this->_oRequest->getChooseFSearchid(),
            'choose_t_searchid'                    => $this->_oRequest->getChooseTSearchid(),
            'choose_f_srctag'                      => $oAreaInfo->sFromPoiType,
            'choose_t_srctag'                      => $oAreaInfo->sToPoiType,
            'county'                               => $oAreaInfo->iFromCounty,
            'to_county'                            => $oAreaInfo->iToCounty,
            'from_name'                            => addcslashes(str_replace(PHP_EOL, '', $oAreaInfo->sFromName),"|"),
            'to_name'                              => addcslashes(str_replace(PHP_EOL, '', $oAreaInfo->sToName),"|"),
            'scene_type'                           => $oProduct->oOrderInfo->iSceneType,
            'carpool_seat_num'                     => $oProduct->oOrderInfo->iCarpoolSeatNum,
            'order_type'                           => $oProduct->oOrderInfo->iOrderType,
            'designated_driver'                    => $oProduct->oOrderInfo->sDesignatedDriver,
            'time_cost'                            => $oBillInfo->getDriverMinute(),
            'driver_metre'                         => $oBillInfo->getDriverMetre(),
            'total_fee'                            => $oBillInfo->getTotalFee(),
            'pre_total_fee'                        => $oBillInfo->getPreTotalFee(),
            'dynamic_total_fee'                    => $oBillInfo->getDynamicTotalFee(),
            'cap_price'                            => $oBillInfo->getCapPrice() ?? 0, //$aBillInfo['cap_price'] ?? 0,
            'estimate_fee'                         => $oBizProduct->oPriceInfo->getEstimateFee(),
            'carpool_fail_price'                   => $oCarpoolFailExtendInfo ? $oCarpoolFailExtendInfo->getBillInfo()['dynamic_total_fee'] : 0,
            'discount_fee'                         => $oBizProduct->oPriceInfo->getEstimateFee(),
            'carpool_fail_discount_fee'            => $oCarpoolFailExtendInfo ? $oCarpoolFailExtendInfo->getEstimateFee() : 0,
            'is_hit_member_capping'                => $oBillInfo->getIsHitDynamicCapping(),
            'dynamic_price_without_member_capping' => $oBillInfo->getDynamicPriceWithoutMemberCapping() ?? 0,
            'member_level_id'                      => $oProduct->aMemberProfile['member_profile']['level_id'] ?? 0,
            'default_pay_type'                     => $oBizProduct->getPaymentInfo()->getDefaultPayType(),
            'order_n_tuple'                        => json_encode($oProduct->oOrderInfo->toArray() ?? []),
            'combo_id'                             => $oProduct->oOrderInfo->iComboId,    //combo_id
            'is_special_price'                     => $oProduct->oOrderInfo->iIsSpecialPrice,
            'carpool_type'                         => $oProduct->oOrderInfo->iCarpoolType,
            'level_type'                           => $oProduct->oOrderInfo->iLevelType,
            'airport_type'                         => $oProduct->oOrderInfo->iAirportType,
            'railway_type'                         => $oProduct->oOrderInfo->iRailwayType,
            'exam_type'                            => $oProduct->oOrderInfo->iExamType,
            'product_category'                     => $oProduct->oOrderInfo->iProductCategory,
            'departure_time'                       => $oProduct->oOrderInfo->iDepartureTime,
            'is_dynamic'                           => $oBillInfo->getDynamicTimes() > 0, //$aBillInfo['is_has_dynamic'] ?? 0,
            'dynamic_price_id'                     => $oBillInfo->getDynamicInfo()['dynamic_price_id'], //$aBillInfo['dynamic_info']['dynamic_price_id'],
            'dynamic_diff_price'                   => $oBillInfo->getDynamicDiffPrice(),
            'dynamic_price'                        => $oBillInfo->getDynamicInfo()['dynamic_price'], //$aBillInfo['dynamic_info']['dynamic_price'],
            'dynamic_kind'                         => $oBillInfo->getDynamicInfo()['dynamic_kind'], //$aBillInfo['dynamic_info']['dynamic_kind'],
            'dynamic_times'                        => $oBillInfo->getDynamicTimes() ?? 0, //$aBillInfo['dynamic_times'] ?? 0,
            'dynamic_icon_type'                    => empty($oEstimateData->getFeeMsgPrefixIcon())  ? 0:1,
            'coupon'                               => $this->_getCouponInfoStr($oCommonInfo->sChannel, $oBizProduct->oPriceInfo->getDiscountSet()['coupon']),
            'discount_info'                        => $this->_getDiscountInfo($oBizProduct->oPriceInfo->getDiscountSet()),
            'disabled'                             => 0,
            'select_type'                          => $oEstimateData->getIsSelected(),
//            'user_status'                          => $aInfo['price_extra']['user_status'],
            'estimate_trace_id'                    => Trace::traceId(),
            'estimate_id'                          => $oBizProduct->getEstimateID(),
            'is_support_multi_selection'           => (int) $oCommonInfo->bIsMultiSelectionSupport,
            'rewards_display_type'                 => $this->_getRewardDisplayType($oBizProduct->getPriceInfo()->getDiscountSet()),
            'show_pickup_service'                  => $this->_isShowPickUp($oBizProduct),
            'source_channel'                       => $this->_oRequest->getSourceChannel(),
            'count_price_type'                     => $oBillInfo->getCountPriceType(),
            'stopover_points'                      => json_encode($oProduct->oOrderInfo->aStopoverPoints),
            'dds_activity_type'                    => 0,
            'dds_activity_key'                     => '',
            'activity_discount_fee'                => $aBillDisplayLines['activity_discount_fee']['value'] ?? 0.0,
            'sub_group_id'                         => DecisionV2Service::getInstance()->getSubGroupIDByPcID($oBizProduct->getProductCategory()),
            'preferred_route_id'                   => $this->_oRequest->getPreferredRouteId(),
            'custom_service'                       => json_encode($oBizProduct->getOrderInfo()->aPersonalizedCustomOption),
            'xpsid'                                => $this->_oRequest->getXpsid(),
            'xpsid_root'                           => $this->_oRequest->getXpsidRoot(),
            'price_info_desc'                      => json_encode($oEstimateData->getFeeDescList()),
            'tab_id'                               => $this->_oRequest->getTabId(),
            'category_info'                        => json_encode(CategoryInfoLogic::getInstance($this->_oBizCommonInfo, $this->_aBizProductMap)->getCategoryInfo()),
            'from_type'                            => $oCommonInfo->iFromType,
            'guide_trace_id'                       => $oCommonInfo->sGuideTraceId,
            'etp'                                  => CarpoolPreMatchLogic::getInstance()->getMiniBusInfo($oBizProduct)['etp_info']['etp_time_duration'],
            'long_trip_id'                         => CarpoolPreMatchLogic::getInstance()->getMiniBusInfo($oBizProduct)['extMap']['long_trip_id'],
            'candidate_ets'                        => CarpoolPreMatchLogic::getInstance()->getMiniBusInfo($oBizProduct)['extMap']['wait_answer_time'],
            'form_style_exp'                       => NewStyleFormABParam::getInstance($this->_oBizCommonInfo->getApolloParams(null))->getHitNewStyleForm(),
            'rec_form'                             => intval(AthenaRecommendRecForm::getInstance()->isRecForm($this->_oBizCommonInfo)),
            'tab_product_count'                    => sizeof($this->_aBizProductMap),
        ];

//        $iOptionCnt = isset($aEstimateData['option_data']['service_list']) ? count($aEstimateData['option_data']['service_list']) : 0;
//        $iPreferCnt = $aEstimateData['prefer_data']['prefer_select_count'] ?? 0;
//        $iPreferCnt = $iPreferCnt - $iOptionCnt;
//        $aEstimateStatistic['option_count']        = $iOptionCnt;
//        $aEstimateStatistic['prefer_select_count'] = $iPreferCnt;
        if (Order::isSpecialRateV2($aEstimateStatistic)) {
            $aEstimateStatistic['athena_capacity'] = 1;
            $aEstimateStatistic['sp_open']         = $oBillInfo->getHistoryExtraMap()['sp_open'] ?? '0';
            $aEstimateStatistic['dds_capacity']    = 1;
            $aEstimateStatistic['sp_capacity']     = 1;
        }

        if ($oProduct->oOrderInfo->iCarpoolType) {
            //如果命中了区域渗透路线
            if (\BizLib\Constants\Horae::TYPE_COMBO_CARPOOL_INTER_CITY == $oProduct->oOrderInfo->iComboType) {
                //跨城
                $aRouteInfo = $oProduct->oOrderInfo->aMatchRoutes; //$aInfo['bill_info']['match_routes'];
                $aEstimateStatistic['combo_id']    = $aRouteInfo['route_id'];
                $aEstimateStatistic['route_group'] = $aRouteInfo['route_group'];
                $aEstimateStatistic['inter_mode']  = $aRouteInfo['inter_mode'];
                $aEstimateStatistic['route_name']  = $aRouteInfo['route_name'];
                if (!empty($this->_oRequest->getDepartureRange())) {
                    $sDepartureRange = $this->_oRequest->getDepartureRange();
                } else {
                    $sDepartureRange = $aRouteInfo['time_span'][0]['range'][0]['value'];
                }

                $aEstimateStatistic['departure_range'] = $sDepartureRange;

                // 标识城际开放围栏路线
                $aEstimateStatistic['is_open_fence'] = InterCity::getRouteConf($aRouteInfo['route_id'], 'is_open_fence', 0);
            }

            // 如果是大巴
            if (OrderNTuple::CARPOOL_TYPE_INTERCITY_STATION == $oProduct->oOrderInfo->iCarpoolType) {
                $aEstimateStatistic['is_best_shift'] = $oProduct->oOrderInfo->aBusShiftInfo['is_best_shift'] ?? 0;  // 查看最优班次曝光
            }
            // 如果是快线
            if (self::EXPRESS_LINE_LEVEL_TYPE == $oProduct->oOrderInfo->iLevelType) {
                if (!empty(CarpoolPreMatchLogic::getInstance()->getMiniBusInfo($oBizProduct)['shift_info'])) {
                    $aEstimateStatistic['shift_id'] = CarpoolPreMatchLogic::getInstance()->getMiniBusInfo($oBizProduct)['shift_info']['shift_id'];
                    $aEstimateStatistic['combo_id'] = CarpoolPreMatchLogic::getInstance()->getMiniBusInfo($oBizProduct)['shift_info']['route_id'] ?? 0;
                    if (!empty(CarpoolPreMatchLogic::getInstance()->getMiniBusInfo($oBizProduct)['shift_info']['start_station'])) {
                        $aEstimateStatistic['start_station_id'] = CarpoolPreMatchLogic::getInstance()->getMiniBusInfo($oBizProduct)['shift_info']['start_station']['station_id'] ?? 0;
                    }
                    if (!empty(CarpoolPreMatchLogic::getInstance()->getMiniBusInfo($oBizProduct)['shift_info']['end_station'])) {
                        $aEstimateStatistic['end_station_id'] = CarpoolPreMatchLogic::getInstance()->getMiniBusInfo($oBizProduct)['shift_info']['end_station']['station_id'] ?? 0;
                    }
                }
            }
        }

        if ($oProduct->isTripcloud()) {
            $fSpecialRateCapPrice = $this->_aBillsForTcFeeMonitor['special_rate_cap_price'];
            if ($fSpecialRateCapPrice > 0 && $aEstimateStatistic['cap_price'] > 0) {
                $capPriceSub = bcsub($aEstimateStatistic['cap_price'], $fSpecialRateCapPrice, 2);
                $aEstimateStatistic['special_rate_cap_price'] = $fSpecialRateCapPrice;
                $aEstimateStatistic['tc_cap_price_diff']      = $capPriceSub;
            }

            $fFastDynamicTotalFee = $this->_aBillsForTcFeeMonitor['fast_dynamic_total_fee'];
            $fFastEstimateFee     = $this->_aBillsForTcFeeMonitor['fast_estimate_fee'];
            if ($aEstimateStatistic['dynamic_total_fee'] > 0) {
                $aEstimateStatistic['fast_dynamic_total_fee'] = $fFastDynamicTotalFee;
                $aEstimateStatistic['fast_estimate_fee']      = $fFastEstimateFee;
            }
        }

        $aHistoryExtraMap = $oBillInfo->getHistoryExtraMap();
        if (!empty($aHistoryExtraMap) && !empty($aHistoryExtraMap['voy_route_id'])) {
            $aEstimateStatistic['voy_route_id']      = $aHistoryExtraMap['voy_route_id'];
        }

        if (ProductCategory::PRODUCT_CATEGORY_BARGAIN_CAR == $oProduct->oOrderInfo->iProductCategory) {
            $aEstimateStatistic['bargain_recommend_price'] = json_encode(
                array(round(
                    $oBizProduct->getRecommendPrice() > 0 ? $oBizProduct->getRecommendPrice() : $oBillInfo->getDynamicTotalFee(),
                    1
                ))
            );
            $aEstimateStatistic['bargain_ceil']            = $oBizProduct->getRecommendLimit()['ceil'];
            $aEstimateStatistic['bargain_floor1']          = $oBizProduct->getRecommendLimit()['floor1'];
            $aEstimateStatistic['bargain_floor2']          = $oBizProduct->getRecommendLimit()['floor2'];
            $aTopRecommendStyle = AthenaRecommend::getInstance()->getTopRecommendStyle();
            $aEstimateStatistic['recommend_open_status']   = 1;
            $aEstimateStatistic['recommend_status']        = $aTopRecommendStyle['extra_info']['bargain_scene_recommend'] ?? 0;
            $aEstimateStatistic['bargain_is_multi_select'] = AthenaRecommend::getInstance()->bargainIsMultiSelect() ? 1 : 0;
        }
        //惊喜独享标识
        if($oProduct->oOrderInfo->judgeIntercitySurpriseAlone()) {
            $aEstimateStatistic['is_intercity_surprise_alone']= "1";
        }
        //构建费项信息
        $aEstimateStatistic['fee_detail_info'] = $this->_buildFeeDetailInfo($oBizCommonInfo, $oBizProduct);
        $aEstimateStatistic['custom_service']  = $this->_buildCustomService($oBizCommonInfo, $oBizProduct);

        PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);
    }

    /**
     * @param array $aInfo          aInfo
     * @param array $aResponseInfos 预估结果集
     *
     * @return array
     */
    private function _getEstimateData($aInfo, $aResponseInfos) {
        $iEstimateId = $aInfo['order_info']['estimate_id'];
        if (!empty($aResponseInfos['data']['estimate_data'])) {
            foreach ($aResponseInfos['data']['estimate_data'] as $aData) {
                if ($iEstimateId == $aData['estimate_id']) {
                    return $aData;
                }
            }
        }

        return [];
    }

    /**
     * @param array $aPayments aPayments
     *
     * @return int
     */
    private function _getSelectedPayType($aPayments) {
        foreach ($aPayments as $aPayment) {
            if (!empty($aPayment['isSelected'])) {
                return $aPayment['tag'];
            }
        }

        return 0;
    }

    /**
     * @param string                    $iChannel    channel
     * @param EstimateNewFormCouponInfo $oCouponInfo 券信息
     *
     * @return string
     */
    private function _getCouponInfoStr($iChannel, $oCouponInfo) {
        $aResult = [
            'default_coupon'  => [],
            'activity_coupon' => [],
        ];
        if (!empty($oCouponInfo)) {
            $sKey           = 'coupon' == $oCouponInfo->getCouponSource() ? 'default_coupon' : 'activity_coupon';
            $aResult[$sKey] = [
                'channel' => $iChannel,
                'batchid' => $oCouponInfo->getBatchId(),
                'amount'  => $oCouponInfo->getAmount(),
            ];
        }

        return json_encode($aResult);
    }

    /**
     * @param EstimateNewFormDiscountSet $aDiscountSet $aDiscountSet
     * @return string
     */
    private function _getDiscountInfo($aDiscountSet) {
        $aDiscountInfo = [];
        foreach ($aDiscountSet as $sType => $oDiscount) {
            $aItem           = $oDiscount;
            $aItem['type']   = $sType;
            $aDiscountInfo[] = $aItem;
        }

        return json_encode($aDiscountInfo);
    }

    /**
     * 判断reward的展示模式.
     * @param EstimateNewFormDiscountSet $oDiscountSet $oDiscountSet
     * @return int
     */
    private function _getRewardDisplayType($oDiscountSet) {
        if (empty($oDiscountSet)) {
            return self::PRICE_DESC_NO_REWARDS;
        }

        $iDiscountNum = 0;
        $bHasRewards  = false;
        foreach ($oDiscountSet as $sKey => $aValue) {
            if ($aValue['amount'] > 0) {
                $iDiscountNum ++;
            }

            if ('bonus' == $sKey) {
                $bHasRewards = true;
            }
        }

        if ($bHasRewards) {
            if ($iDiscountNum > 1) {
                return self::PRICE_DESC_INCLUDE_REWARDS;
            } else {
                return self::PRICE_DESC_ONLY_REWARDS;
            }
        } else {
            return self::PRICE_DESC_NO_REWARDS;
        }
    }

    /**
     * @param BizProduct $oBizProduct $oBizProduct
     * @return int
     */
    private function _isShowPickUp($oBizProduct) {

        $iPcId  = $oBizProduct->getProductCategory();
        $aScene = AdditionalServiceLogic::getInstance()->getSceneDataByPcId($iPcId);
        foreach ($aScene as $aItem) {
            if (CustomLogic::CUSTOM_SERVICE_GUIDE_NEW == $aItem['service_id']) {
                return 1;
            }
        }

        return 0;
    }

    /**
     * @desc: 获取排队队列信息
     *
     * @param array  $aQueueData queue_data
     * @param int    $iProductId product_id
     * @param string $sCarLevel  car_level
     * @param int    $iComboType combo_type
     * @return string|null
     */
    private function _getQueueLen($aQueueData, $iProductId, $sCarLevel, $iComboType) {
        if (empty($aQueueData)) {
            return null;
        }

        $aOneConf   = [
            'product_id'    => $iProductId,
            'require_level' => $sCarLevel,
            'combo_type'    => $iComboType,
        ];
        $iQueueType = \BizCommon\Models\Order\LineUpOrderComModel::getInstance()->getQueueTypeFromOneConf($aOneConf);
        if (isset($aQueueData[$iQueueType])) {
            return json_encode($aQueueData[$iQueueType]);
        }

        return null;
    }

    /**
     * 获取三方价格监控需要的价格数据
     * @return void
     */
    private function _getFeeForTripcloud() {
        $totalCnt = 2;
        $this->_aBillsForTcFeeMonitor = [];
        foreach ($this->_aBizProductMap as $oBizProduct) {
            if (ProductCategory::PRODUCT_CATEGORY_FAST == $oBizProduct->getProductCategory()) {
                $oBillInfo = $oBizProduct->getBillInfo();
                $this->_aBillsForTcFeeMonitor['fast_dynamic_total_fee'] = $oBillInfo->getDynamicTotalFee();
                $this->_aBillsForTcFeeMonitor['fast_estimate_fee']      = $oBizProduct->oPriceInfo->getEstimateFee();
                $totalCnt--;
            }

            if (ProductCategory::PRODUCT_CATEGORY_FAST_SPECIAL_RATE == $oBizProduct->getProductCategory()) {
                $oBillInfo = $oBizProduct->getBillInfo();
                $this->_aBillsForTcFeeMonitor['special_rate_cap_price'] = $oBillInfo->getCapPrice();
                $totalCnt--;
            }

            if (0 == $totalCnt) {
                break;
            }
        }
    }

    /**
     * @param object $oProduct $oProduct
     * @return bool
     */
    private function _isTaxi($oProduct) {
        $iProductID = $oProduct->oOrderInfo->iProductId;
        return in_array($iProductID, [OrderSystem::PRODUCT_ID_UNITAXI, OrderSystem::PRODUCT_ID_BUSINESS_TAXI_CAR]);
    }


    /**
     * 实时监控日志
     * @param BizProduct $oBizProduct ...
     * @return void
     */
    private function _writeLogForRealtimeMonitor($oBizProduct) {

        $oAreaInfo   = $this->_oBizCommonInfo->oAreaInfo;
        $oCommonInfo = $this->_oBizCommonInfo->oCommonInfo;
        $oProduct    = $oBizProduct->oProduct;

        // 基础信息
        $aEstimateStatistic = [
            'opera_stat_key'    => 'g_realtime_estimate_price',
            'app_version'       => $oCommonInfo->sAppVersion,
            'client_type'       => $oCommonInfo->iClientType,
            'access_key_id'     => $oCommonInfo->iAccessKeyID,
            'channel'           => $oCommonInfo->sChannel,
            'page_type'         => $oProduct->oOrderInfo->iPageType,
            'origin_page_type'  => $oProduct->oOrderInfo->iOriginPageType,
            'call_car_type'     => $oProduct->oOrderInfo->iCallCarType,
            'product_id'        => $oProduct->oOrderInfo->iProductId,
            'combo_type'        => $oProduct->oOrderInfo->iComboType,
            'require_level'     => $oProduct->oOrderInfo->iRequireLevel,
            'area'              => $oAreaInfo->iArea,
            'to_area'           => $oAreaInfo->iToArea,
            'county'            => $oAreaInfo->iFromCounty,
            'to_county'         => $oAreaInfo->iToCounty,
            'order_type'        => $oProduct->oOrderInfo->iOrderType,
            'estimate_id'       => $oBizProduct->getEstimateID(),
            'estimate_trace_id' => Trace::traceId(),
            'is_special_price'  => $oProduct->oOrderInfo->iIsSpecialPrice,
            'product_category'  => $oProduct->oOrderInfo->iProductCategory,
            'carpool_type'      => $oProduct->oOrderInfo->iCarpoolType,
        ];

        //
        //  // --@param EstimateNewFormData $oOriginPrice
        //  // --@param EstimateNewFormExtend $oCarpoolFailPrice
        //  // --@return array
        $fnGetDualCarpoolDiscount = function ($oOriginPrice, $oCarpoolFailPrice) {
            $aDualCarpoolDiscount = [];
            if (empty($oCarpoolFailPrice)) {
                return $aDualCarpoolDiscount;
            }

            // 拼成实付折扣
            $aDualCarpoolDiscount['dual_success_actual_discount'] = number_format($oOriginPrice->getEstimateFee() / $oOriginPrice->getBillInfo()['pre_total_fee'],2);
            // 拼成应付折扣
            $aDualCarpoolDiscount['dual_success_need_discount'] = number_format($oOriginPrice->getBillInfo()['dynamic_total_fee'] / $oOriginPrice->getBillInfo()['pre_total_fee'],2);
            // 拼不成实付折扣
            $aDualCarpoolDiscount['dual_fail_actual_discount'] = number_format($oCarpoolFailPrice->getEstimateFee() / $oCarpoolFailPrice->getBillInfo()['pre_total_fee'],2);
            // 拼不成应付折扣
            $aDualCarpoolDiscount['dual_fail_need_discount'] = number_format($oCarpoolFailPrice->getBillInfo()['dynamic_total_fee'] / $oCarpoolFailPrice->getBillInfo()['pre_total_fee'],2);
            return $aDualCarpoolDiscount;
        };

        $aOrderInfo = [
            'carpool_type'          => $oProduct->oOrderInfo->iCarpoolType,
            'is_dual_carpool_price' => $oProduct->oOrderInfo->bIsDualCarpoolPrice,
            'carpool_price_type'    => $oProduct->oOrderInfo->iCarpoolPriceType,
        ];
        // 两口价折扣
        if (Horae::isCarpoolUnSuccessFlatPrice($aOrderInfo)) {
            $aEstimateStatistic += $fnGetDualCarpoolDiscount($oBizProduct->oPriceInfo, $oBizProduct->getCarpoolFailPriceInfo());
        }

        PublicLog::writeLogForOfflineCal('public', $aEstimateStatistic);

    }

    /**
     * 构建费项信息
     * @param BizCommonInfo $oBizCommonInfo 公用信息
     * @param BizProduct    $oBizProduct    品类信息
     * @return string
     */
    private function _buildFeeDetailInfo($oBizCommonInfo, $oBizProduct) {
        $aFeeDetailInfo     = [];
        $aTempFeeDetailInfo = $oBizProduct->getBillInfo()->getFeeDetailInfo();
        if (empty($aTempFeeDetailInfo) || !is_array($aTempFeeDetailInfo)) {
            return '[]';
        }

        // 出租车峰期加价服务费
        foreach ($aTempFeeDetailInfo as $sFeeName => $fFeeItem) {
            // 出租车峰期加价服务费
            if (OrderSystem::PRODUCT_ID_UNITAXI == $oBizProduct->getProductID()) {
                $oTaxiPeakFeeClient = TaxiPeakFee::getPoolInstance(
                    [
                        (int)$oBizProduct->getProductCategory(),
                        (int)$oBizCommonInfo->getFromCity(),
                    ]
                );
                if ($oTaxiPeakFeeClient->getStatus() && 'taxi_peak_price' == $sFeeName) {
                    $aFeeDetailInfo[] = [
                        'key'    => 'taxi_peak_fee',
                        'amount' => empty($fFeeItem) ? 0 : $fFeeItem * 100, //按分存数据,
                    ];
                    continue;
                }
            }

            $aFeeDetailInfo[] = [
                'key'    => $sFeeName,
                'amount' => empty($fFeeItem) ? 0 : $fFeeItem * 100, //按分存数据
            ];
        }

        return json_encode($aFeeDetailInfo);
    }


    /**
     * 构建用户选择数据
     * @param BizCommonInfo $oBizCommonInfo 公共信息
     * @param BizProduct    $oBizProduct    品类信息
     * @return string
     */
    private function _buildCustomService($oBizCommonInfo, $oBizProduct) {
        $aCustomService = [];
        $aSceneData     = AdditionalServiceLogic::getInstance()->getSceneDataByPcId($oBizProduct->getProductCategory());
        if (empty($aSceneData) || !is_array($aSceneData)) {
            return '{}';
        }

        foreach ($aSceneData as $aItem) {
            $aCustomService[$aItem['id']] = [
                'count'       => $aItem['max'], //数量
                'is_selected' => $oBizProduct->getOrderInfo()->aPersonalizedCustomOption[$aItem['id']]['count'] ?: 0,
            ];

            // 出租车峰期加价服务费
            if (CustomLogic::CUSTOM_SERVICE_TAXI_PEAK == $aItem['id']) {
                if (OrderSystem::PRODUCT_ID_UNITAXI == $oBizProduct->getProductID()) {
                    $oTaxiPeakFeeClient = TaxiPeakFee::getPoolInstance(
                        [
                            $oBizProduct->getProductCategory(),
                            $oBizCommonInfo->getAreaInfo()->iArea,
                        ]
                    );
                    if (!$oTaxiPeakFeeClient->getStatus()) {
                        unset($aCustomService[$aItem['id']]);
                    }

                    if ($oTaxiPeakFeeClient->getIsInterActive()) {
                        $aCustomService[$aItem['id']] = $oTaxiPeakFeeClient->buildPublicLogData();
                    } else {
                        unset($aCustomService[$aItem['id']]);
                    }
                } else {
                    unset($aCustomService[$aItem['id']]);
                }
            }
        }

        if (empty($aCustomService)) {
            return '{}';
        }

        return json_encode($aCustomService);
    }

    /**
     * @param array $aPcIDList $aPcIDList
     * @return array
     */
    private function _getChildGroupIDs($aPcIDList,$categoryId) {
        $classifyFoldLogic = ClassifyFoldLogic::getInstance();
        $finalInBox        = $classifyFoldLogic->getAFinalInBoxSubGroupIdMap();
        if (empty($finalInBox)) {
            return [];
        }

        $aGroupIDList = [];

        foreach ($aPcIDList as $sGroupId) {
            if ($classifyFoldLogic->getIsFinalInBoxByGroupId($sGroupId,$categoryId)) {
                $aGroupIDList[] = $sGroupId;
            }
        }

        return  $aGroupIDList;
    }


    /**
     * 记录layout内部group日志
     * @param $aLayout $aLayout
     * @param $aGroup  $aGroup
     * @param $iCurSort $iCurSort
     * @param $iLevel  $iLevel
     * @return void
     */
    private function _writeLayoutGroupInfo($aLayout, $aGroup, $iCurSort, $iLevel = 0) {
        $iLinkProduct = $aGroup['link_info']['link_product'] ?? 0;

        $aLog = [
            'opera_stat_key'          => 'g_order_estimate_data_layout',
            'city'                    => $this->_oBizCommonInfo->getFromCity(),
            'access_key_id'           => $this->_oBizCommonInfo->getAccessKeyID(),
            'is_vx_form'              => $this->_getVXForm(),
            'is_first_screen'         => $iCurSort <= AthenaRecommend::getInstance()->getShowCarNum() ? 1 : 0,
            'sort'                    => $iCurSort,
            'form_item_type'          => $aGroup['type'],
            'products'                => $this->_genProductsEID($aGroup['products']),
            'form_show_type'          => $aLayout['form_show_type'],
            'box_id'                  => $aGroup['box_id'] ?? 0,
            'sub_group_id'            => $this->getSubGroupIdByGroupId($aGroup['group_id']),
            'link_product'            => $iLinkProduct,
            'link_product_eid'        => empty($iLinkProduct) ? '' : $this->_aBizProductMap[$iLinkProduct]->getEstimateID(),
            'box_price_info'          => $aGroup['fee_msg'] ?? '',
            'group_id'                => $aGroup['group_id'] ?? '',
            'action_type'             => $aGroup['action_type'] ?? 0,
            'style_type'              => $aGroup['style_type'] ?? 0,
            'dialog_id'               => AthenaRecommend::getInstance()->getTopRecommendStyle()['extra_info']['dialog_id'] ?? 0,
            'theme_style'             => AthenaRecommend::getInstance()->getTopRecommendStyle()['style'] ?? 0,
            'category_id'             => $aLayout['category_id'] ?? 0,
            'single_product_category' => $this->_getSingleProductCategory($aGroup['products']),  // 非盒子形态的product_category
            'pixels'                  => $this->_oRequest->getPixels(),
            'screen_scale'            => $this->_oRequest->getScreenScale(),
            'fold_type'               => $aLayout['fold_type'] ?? 0,
            'child_group_ids'         => implode(',', $this->_getChildGroupIDs($aGroup['products'],$aLayout['category_id']??0)),
            'box_level'               => $iLevel,
            'pc_ids'                  => implode(',', array_map(function($element) {
                return '_' . $element . '_';
            }, $aGroup['products'])),
        ];

        // 三方聚合表单的首屏曝光由api计算
        if (Tab::isHitClassifyTabByTabId($this->_oBizCommonInfo->oCommonInfo->sTabId)) {
            $aLog['is_first_screen'] = $aGroup['is_first_screen'] ?? 0;
        }

        PublicLog::writeLogForOfflineCal('public', $aLog);
    }

    /**
     * @param string $sGroupId $sGroupId
     * @return int
     */
    private function getSubGroupIdByGroupId($sGroupId) {
        $parts = explode('_', $sGroupId);
        if (count($parts) < 2) {
            return LayoutBuilder::SUB_GROUP_ID_SINGLE_CAR;
        }

        if ($parts[0] == AthenaRecommend::SHORT_DISTANCE_TYPE ){
            return (int)$parts[1];
        }

        return LayoutBuilder::SUB_GROUP_ID_SINGLE_CAR;
    }
}
