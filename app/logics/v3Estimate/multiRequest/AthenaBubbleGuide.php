<?php

namespace PreSale\Logics\v3Estimate\multiRequest;

use BizLib\PhystrixClient\AthenaApiPhystrixClient;
use Dirpc\SDK\AthenaApiv3\AthenaEstimateProductInfo;

/**
 * class AthenaBubbleGuide
 */
class AthenaBubbleGuide
{
    private $_oBizCommonInfo;

    /**
     * @param BizCommonInfo $oBizCommonInfo $oBizCommonInfo
     */
    public function __construct(BizCommonInfo $oBizCommonInfo) {
        $this->_oBizCommonInfo = $oBizCommonInfo;
    }

    /**
     * @param Product[] $aProducts DDS 返回的产品列表
     * @return void
     */
    public function setAthenaGuideFlag($aProducts) {
        $aGuideProducts = $this->_fetchAthenaResp($aProducts);
        if (!empty($aGuideProducts)) {
            foreach ($aProducts as $oProduct) {
                if (in_array($oProduct->oOrderInfo->iProductCategory, $aGuideProducts)) {
                    $oProduct->oOrderInfo->bNeedGuideInfo = true;
                }
            }
        }
    }

    /**
     * @param Product[] $aProducts DDS 返回的产品列表
     * @return array
     */
    private function _fetchAthenaResp($aProducts) {
        $oClientInfo = $this->_oBizCommonInfo->getClientInfo();
        $oAreaInfo   = $this->_oBizCommonInfo->getAreaInfo();

        $aAthenaReq = [
            'client_type'   => $oClientInfo->iClientType,
            'app_version'   => $oClientInfo->sAppVersion,
            'lang'          => $oClientInfo->sLang,
            'order_type'    => $oClientInfo->iOrderType,
            'channel'       => $oClientInfo->sChannel,
            'menu_id'       => $oClientInfo->sMenuId,
            'access_key_id' => $oClientInfo->iAccessKeyID,

            'from_area'     => $oAreaInfo->iArea,
            'from_lng'      => $oAreaInfo->fFromLng,
            'from_lat'      => $oAreaInfo->fFromLat,
            'to_lng'        => $oAreaInfo->fToLng,
            'to_lat'        => $oAreaInfo->fToLat,
            'map_type'      => $oAreaInfo->sMapType,

            'phone'         => $this->_oBizCommonInfo->getPassengerPhone(),
            'pid'           => $this->_oBizCommonInfo->getPassengerID(),
        ];

        $aApiAddProducts = [];
        foreach ($aProducts as $oProduct) {
            $oOrderInfo        = $oProduct->oOrderInfo;
            $aAddProduct       = [
                'estimate_id'        => $oOrderInfo->sEstimateID,
                'product_category'   => $oOrderInfo->iProductCategory,
                'product_id'         => $oOrderInfo->iProductId,
                'require_level'      => $oOrderInfo->iRequireLevel,
                'carpool_type'       => $oOrderInfo->iCarpoolType,
                'is_special_price'   => $oOrderInfo->iIsSpecialPrice,
                'level_type'         => $oOrderInfo->iLevelType,
                'is_dual_price'      => $oOrderInfo->bIsDualCarpoolPrice,
                'carpool_price_type' => $oOrderInfo->iCarpoolPriceType,
            ];
            $aApiAddProducts[] = new AthenaEstimateProductInfo($aAddProduct);
        }

        $aAthenaReq['api_add_product'] = $aApiAddProducts;

        $oAthenaClient = new AthenaApiPhystrixClient();
        $aResp         = $oAthenaClient->getBubbleGuideScene($aAthenaReq);
        if (0 == $aResp['errno'] && !empty($aResp['guide_line'])) {
            return $aResp['guide_line'];
        }

        return [];
    }
}
