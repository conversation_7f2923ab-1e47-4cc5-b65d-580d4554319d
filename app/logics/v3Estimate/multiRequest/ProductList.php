<?php
namespace PreSale\Logics\v3Estimate\multiRequest;

use BizLib\Client\EstimateDecisionClient;
use BizLib\Client\SpsClient;
use BizLib\Constants\Horae;
use BizLib\Constants\Common;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\RespCode;
use BizLib\Exception\ExceptionWithResp;
use BizLib\Log;
use BizLib\Log as NuwaLog;
use BizLib\Utils\ProductCategory;
use Dirpc\SDK\EstimateDecision\CommonInfoV2;
use Dirpc\SDK\EstimateDecision\ProductsReq;
use Dirpc\SDK\EstimateDecision\UserInfoV2;
use Dirpc\SDK\PreSale\MultiEstimatePriceRequest as Request;
use BizLib\Exception\InvalidArgumentException;
use BizLib\Config;
use Disf\SPL\Trace;
use TripcloudCommon\Utils\Product as TripcloudProduct;
use Xengine\condition\Condition;
use Xengine\condition\Option as XOption;
use Xiaoju\Apollo\Apollo;
use PreSale\Logics\v3Estimate\AdditionalServiceLogic;
use PreSale\Logics\v3Estimate\BizProduct;
use BizLib\ErrCode;
use PreSale\Logics\estimatePrice\EstimateDegradeCode;
use PreSale\Logics\newFormTab\Tab;
use BizCommon\Constants\OrderNTuple;
use BizLib\Client\DuseCarpoolApiClient as CarpoolApiClient;
use \Dirpc\SDK\CarpoolApi\InterBubbleRequest;
use Xiaoju\Apollo\ApolloConstant;

/**
 * Created by PhpStorm.
 * User: didi
 * Date: 2020/2/10
 * Time: 9:58 AM
 */
class ProductList
{

    const HK_MAINLAND_CAR_PAGE = 9; //粤港车pagetype

    public $aProductList = [];

    protected static $_oInstance = null;

    private $_oRequest;

    /**
     * @var BizCommonInfo
     */
    private $_oBizCommonInfo;

    private $_aBasePassengerInfo;

    private $_oMultiMemberInfo;

    /**
     * ProductList constructor.
     * @param Request       $oRequest       $oRequest
     * @param BizCommonInfo $oBizCommonInfo $oBizCommonInfo
     */
    public function __construct(Request $oRequest, BizCommonInfo $oBizCommonInfo) {
        $this->_oRequest       = $oRequest;
        $this->_oBizCommonInfo = $oBizCommonInfo;
        if (!is_null($oBizCommonInfo)) {
            $this->_aBasePassengerInfo = $oBizCommonInfo->oPassengerInfo->getPassengerInfo();
        }

        //判断是否是代叫请求
        if ($oRequest->getCallCarType()) {
            if ('' == $oRequest->getCallCarPhone()) {
                $oRequest->setCallCarPhone($oBizCommonInfo->oPassengerInfo->sCallCarPhone);
            }
        }
    }

    /**
     * 构建基础品类
     * @param BizCommonInfo $oBizCommonInfo 通用参数
     * @return BizProduct[] 品类map[PcId]BizProduct
     * @throws ExceptionWithResp|InvalidArgumentException 开城|参数错误
     */
    public function buildProductMap(BizCommonInfo $oBizCommonInfo) {

        // 1. 调用dds服务，获取当前城市已开服的基础品类
        $aDdsProductList = $this->_getDDSProducts();
        // 1.1 过滤remove_flag==true
        $aBaseProductList = $this->_formatProducts($aDdsProductList);
        $this->judgeEmpty($aBaseProductList);

        // 2. 请求增值服务
        $oCustomServiceLogic = AdditionalServiceLogic::getInstance();
        $oCustomServiceLogic->init($this->_oRequest, $this->_oBizCommonInfo, $aBaseProductList);

        // 2.2 过滤ssse不支持的品类
        $aBaseProductList = $oCustomServiceLogic->filterProducts($this->_oBizCommonInfo, $aBaseProductList);
        $this->judgeEmpty($aBaseProductList);

        // 3.批量请求会员信息
        $this->_oMultiMemberInfo = new MemberInfo(
            $this->_oRequest,
            $this->_aBasePassengerInfo,
            $aBaseProductList,
            $this->_oBizCommonInfo->oCommonInfo->sLang,
            $this->_oBizCommonInfo->oAreaInfo->iArea,
            $this->_oBizCommonInfo->oAreaInfo->iToArea
        );
        $this->_oMultiMemberInfo->buildMemberInfo();

        // 4. 构建品类信息list
        $aProductList = $this->buildProductList($aBaseProductList);

        // 5.根据AthenaGuide接口，设置是否需要H5导流的标识，日常不涉及H5导流需求的可忽略
        (new AthenaBubbleGuide($oBizCommonInfo))->setAthenaGuideFlag($aProductList);

        // 6.调用duse，获取惊喜独享标识
        $this->_setIntercitySurpriseAloneSign($aProductList);

        // 7. 转化为map格式
        $aBizProductMap = [];
        foreach ($aProductList as $oProduct) {
            $oBizProduct      = new BizProduct($oProduct);
            $iProductCategory = $oProduct->oOrderInfo->iProductCategory;
            if (!empty($iProductCategory)) {
                $aBizProductMap[$iProductCategory] = $oBizProduct;
            }
        }

        return $aBizProductMap;
    }

    /**
     * @param Product[] $aProductList $aProductList
     * @return void
     * @throws ExceptionWithResp e
     * @throws InvalidArgumentException e
     */
    private function _setIntercitySurpriseAloneSign($aProductList) {
        $iComboId = 0;
        foreach ($aProductList as $oProduct) {
            //自营拼满走才会命中
            if ($oProduct->oOrderInfo->iProductCategory == ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTE&&$oProduct->oOrderInfo->iRouteType==OrderNTuple::ROUTE_TYPE_POOL_FULL_GO) {
                $iComboId = $oProduct->oOrderInfo->iComboId;
            }
        }
        //非城际自营过滤
        if ($iComboId == 0) {
            return;
        }
        //城际自营命中惊喜独享对应的端、小程序版本
        $oToggle =\Nuwa\ApolloSDK\Apollo::getInstance()->featureToggle('gs_intercity_surprise_alone_switch', $this->_oBizCommonInfo->getApolloParams(null));
        if (!$oToggle->allow()) {
            return ;
        }
        //命中惊喜独享配置
        $aEngineReq = [
            'city'        => $this->_oBizCommonInfo->getFromCity(),
            'route_group' => $iComboId,
        ];
        $oOption = new XOption();
        $oRes      = Condition::Check('intercity_carpool_surprise_alone_conf', $aEngineReq, $oOption);
        if (!$oRes->isAllow()) {
            return;
        }
        //走ab实验
        $oApollo        = new \Nuwa\ApolloSDK\Apollo();
        $oFeatureToggle = $oApollo->featureToggle(
            'intercity_uni_express',
            array(
                ApolloConstant::APOLLO_INDIVIDUAL_ID => $this->_aBasePassengerInfo['pid'],
                'key'                                => $this->_aBasePassengerInfo['pid'],
                'pid'                                => $this->_aBasePassengerInfo['pid'],
                'phone'                              => $this->_aBasePassengerInfo['phone'],
                'city'                               => $this->_oBizCommonInfo->getFromCity(),
                'combo_id'                           => $iComboId,
            )
        );
        if ($oFeatureToggle->allow() && '1'== $oFeatureToggle->getParameter('hit_show_surprise_alone','')) {
            foreach ($aProductList as $oProduct) {
                if ($oProduct->oOrderInfo->iProductCategory == ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTE) {
                    $oProduct->oOrderInfo->iIsIntercitySurpriseAlone='1';
                }
            }
        }
    }

    /**
     * 配置list为空，抛出未开城异常
     * @param array $aProductList 品类列表
     * @throws ExceptionWithResp 未开城异常
     * @return void
     */
    public function judgeEmpty($aProductList) {
        $iCodeNo = Code::E_COMMON_AREA_NOT_OPEN_SERVICE;
        if (self::HK_MAINLAND_CAR_PAGE == $this->_oBizCommonInfo->oCommonInfo->iPageType) {
            $apolloResult = Apollo::getInstance()->featureToggle("yuegangche_errmsg", $this->_oBizCommonInfo->getApolloParams($this->_oBizCommonInfo->getPassengerID()));
            if ($apolloResult->allow()) {
                $iCodeNo = Code::E_PASSENGER_HK_MAINLAND_CAR_EMPTY;
            }
        }

        if (empty($aProductList)) {
            $sErrMsg = Config::text('errno', 'dache_anycar_no_products_error');
            throw new ExceptionWithResp(
                $iCodeNo,
                RespCode::P_ERRNO_NOT_OPEN_SERVICE,
                $sErrMsg
            );
        }
    }

    /**
     * 补充产品列表  (会员信息,可选服务)
     * @param array $aBaseProductList 基础品类
     * @return array
     */
    public function buildProductList($aBaseProductList) {
        $oCommonInfo = $this->_oBizCommonInfo->oCommonInfo;
        $oAreaInfo   = $this->_oBizCommonInfo->oAreaInfo;

        // 请求sps获取增值服务信息
        AdditionalServiceLogic::getInstance()->addSpsServiceFeeInfo($aBaseProductList,$this->_oBizCommonInfo);

        $oHildaPrivilegeInfo = HildaPrivilegeInfo::getInstance();
        $oHildaPrivilegeInfo->init($this->_oBizCommonInfo);
        $aProducts = [];
        foreach ($aBaseProductList as $aProduct) {
            $aProducts[] = $aProduct['product_category'];
        }
        $oHildaPrivilegeInfo->getHildaPrivilege($aProducts);
        //根据products接口返回的多条产品构建基础req数据
        foreach ($aBaseProductList as $aBaseProduct) {
            $oProduct = new Product($this->_oRequest);
            // 构建会员信息
            $oProduct->buildMemberInfo($this->_oMultiMemberInfo, $oCommonInfo, $aBaseProduct);
            // 构建权益信息
            $oProduct->buildPrivilegeInfo($oHildaPrivilegeInfo, $aBaseProduct);
            // 构建订单信息
            $oProduct->buildOrderInfo($aBaseProduct,$this->_aBasePassengerInfo, $oAreaInfo, $oCommonInfo);
            // 修正出发时间 (其实应该放上面)
            $oProduct->buildOrderTimeInfo($this->_oBizCommonInfo, $aBaseProduct);
            // 构建增值服务
            AdditionalServiceLogic::getInstance()->buildPriceParamsByCustomService($oProduct, $this->_oBizCommonInfo);
            // 构建可选服务
            $oProduct->buildOptionServiceInfo();
            //构建额外信息
            $oProduct->buildExtra();
            $this->aProductList[] = $oProduct;
        }
        return $this->aProductList;
    }

    /**
     * @return array
     * @throws ExceptionWithResp e
     * @throws InvalidArgumentException e
     */
    private function _getDDSProducts() {
        $oCommonInfo = $this->_oBizCommonInfo->oCommonInfo;
        $oAreaInfo   = $this->_oBizCommonInfo->oAreaInfo;

        $aProductsReq = new ProductsReq();
        $aUserInfo    = new UserInfoV2();
        $aUserInfo->setPhone($this->_aBasePassengerInfo['phone']);
        $aUserInfo->setPid($this->_aBasePassengerInfo['pid']);
        $aUserInfo->setUid($this->_aBasePassengerInfo['uid']);

        $aCommonInfo = new CommonInfoV2();
        $aCommonInfo->setAppVersion($oCommonInfo->sAppVersion);
        $aCommonInfo->setAccessKeyId($oCommonInfo->iAccessKeyID);
        $aCommonInfo->setLang($oCommonInfo->sLang);
        $aCommonInfo->setChannel($oCommonInfo->sChannel);
        $aCommonInfo->setClientType($oCommonInfo->iClientType);
        $aCommonInfo->setStartLat($oAreaInfo->fFromLat);
        $aCommonInfo->setStartLng($oAreaInfo->fFromLng);
        $aCommonInfo->setDestLat($oAreaInfo->fToLat);
        $aCommonInfo->setDestLng($oAreaInfo->fToLng);
        $aCommonInfo->setCurrentLat($oAreaInfo->fCurLat);
        $aCommonInfo->setCurrentLng($oAreaInfo->fCurLng);
        $aCommonInfo->setStartName($oAreaInfo->sFromName);
        $aCommonInfo->setDestName($oAreaInfo->sToName);
        $aCommonInfo->setStartAddress($oAreaInfo->sFromAddress);
        $aCommonInfo->setDestAddress($oAreaInfo->sToAddress);
        $aCommonInfo->setCity($oAreaInfo->iArea ?? 0);
        $aCommonInfo->setToCity($oAreaInfo->iToArea ?? 0);
        $aCommonInfo->setMenuId($oCommonInfo->sMenuId);
        $aCommonInfo->setOrderType($oCommonInfo->iOrderType);
        $aCommonInfo->setPageType($oCommonInfo->iPageType);
        $aCommonInfo->setCallcarType($oCommonInfo->iCallCarType);
        $aCommonInfo->setLuxurySelectCarlevels($oCommonInfo->sLuxurySelectCarlevels);
        $aCommonInfo->setDepartureTime($oCommonInfo->iDepartureTime ?: time());
        $aCommonInfo->setDepartureRange($oCommonInfo->sDepartureRange);
        $aCommonInfo->setCounty($oAreaInfo->iFromCounty);
        $aCommonInfo->setToCounty($oAreaInfo->iToCounty);
        $aCommonInfo->setDistrict($oAreaInfo->iDistrict);
        $aCommonInfo->setAirportType($oCommonInfo->iAirportType);
        $aCommonInfo->setStopoverPoints(json_encode($oAreaInfo->aStopoverPoints));
        $aCommonInfo->setStartPoiId($oAreaInfo->sFromPoiId);
        $aCommonInfo->setDestPoiId($oAreaInfo->sToPoiId);
        $aCommonInfo->setCarpoolSeatNum($this->_oBizCommonInfo->oCommonInfo->iCarpoolSeatNum);
        $aCommonInfo->setSelectedCarlevel($oCommonInfo->sSelectedCarLevel);
        $aCommonInfo->setSixSeatSelectedCarlevel($oCommonInfo->sSixSeatSelectedCarLevel);
        $aCommonInfo->setTabId($oCommonInfo->sTabId);
        $aCommonInfo->setChooseFSearchid($oAreaInfo->sChooseFSearchid);
        $aCommonInfo->setChooseTSearchid($oAreaInfo->sChooseTSearchid);
        $aProductsReq->setUserInfo($aUserInfo);
        $aProductsReq->setCommonInfo($aCommonInfo);

        $aBaseProductList = [];
        $aResult          = (new EstimateDecisionClient())->getProducts($aProductsReq);
        if (0 != $aResult['errno'] || 200 != $aResult['code']) {
            // 服务异常，走降级
            throw new ExceptionWithResp(
                ErrCode\Code::E_DDS_GET_PRODUCTS_FAIL,
                // ErrCode\RespCode::R_ESTIMATE_DOWNSTREAM_FAIL_DEGRADE,
                EstimateDegradeCode::R_ESTIMATE_DEGRADE_PRODUCTS_FAIL,
                '',
                ['dds_products_response' => $aResult]
            );
        }

        $aResult = $aResult['result'];

        if (P_ERRNO_STOPOVER_POINTS_CONFLICTS_WITH_SCENES == $aResult['errno']) {
            $sMsg = Config::text('errno', 'stopover_points_unsupport_scene_msg_6_0');
            throw new ExceptionWithResp(
                ErrCode\Code::E_COMMON_AREA_NOT_OPEN_SERVICE,
                ErrCode\RespCode::P_AIRPORT_STATION_NOT_SUPPORT_STOPOVER_POINTS_ERROR,
                $sMsg,
                ['district' => $aCommonInfo->getDistrict()]
            );
        }

        if (P_ERRNO_DDS_INTERCEPT_WITH_MSG == $aResult['errno']) {
            throw new ExceptionWithResp(
                ErrCode\Code::E_COMMON_AREA_NOT_OPEN_SERVICE,
                ErrCode\RespCode::P_SERVICE_NOT_OPEN, // 复用这个错误码
                $aResult['errmsg']
            );
        }

        if (GLOBAL_SUCCESS != $aResult['errno']) {
            throw new InvalidArgumentException(Code::E_DDS_GET_PRODUCTS_FAIL, ['ddsResult' => $aResult]);
        }

        if (GLOBAL_SUCCESS == $aResult['errno'] && !empty($aResult['data']) && !empty($aResult['data']['product_list'])) {
            $aBaseProductList = $aResult['data']['product_list'];
        }

        // 城际场景页6.0预估只返回一个远途特价 放量期间存在
        if (Horae::PAGE_TYPE_INTER_CITY == $this->_oRequest->getPageType()) {
            $fnFilterIntercity = function ($aProductList) {
                if (empty($aProductList)) {
                    return $aProductList;
                }

                $aRsp = [];
                foreach ($aProductList as $aProduct) {
                    if (ProductCategory::PRODUCT_CATEGORY_CARPOOL_INTE == $aProduct['product_category']) {
                        $aRsp[] = $aProduct;
                    }
                }

                return $aRsp;
            };
            $aBaseProductList  = $fnFilterIntercity($aBaseProductList);
        }

        if (empty($aBaseProductList)) {
            $aBaseProductList = [];
        }

        $isFromNA = (Common::DIDI_IOS_PASSENGER_APP == $this->_oBizCommonInfo->getAccessKeyID()) || (Common::DIDI_ANDROID_PASSENGER_APP == $this->_oBizCommonInfo->getAccessKeyID());
        $isFromWechatMiniProgram = Common::DIDI_WECHAT_MINI_PROGRAM == $this->_oBizCommonInfo->getAccessKeyID();
        $appVersion = $this->_oBizCommonInfo->getAppVersion();
        if (($isFromNA && version_compare($appVersion, '6.7.4', '<')) || ($isFromWechatMiniProgram && version_compare($appVersion, '6.7.20', '<'))) {
            // 三方聚合表单不支持小巴展示从这里去除掉, 旧版本
            if (Tab::isHitClassifyTabByTabId($oCommonInfo->sTabId)) {
                $fnFilterMiniBus  = function ($aProductList) {
                    if (empty($aProductList)) {
                        return $aProductList;
                    }

                    $aRsp = [];
                    foreach ($aProductList as $aProduct) {
                        if (OrderNTuple::CARPOOL_TYPE_MINI_BUS != $aProduct['carpool_type']) {
                            $aRsp[] = $aProduct;
                        }
                    }

                    return $aRsp;
                };
                $aBaseProductList = $fnFilterMiniBus($aBaseProductList);
            }
        }

        return $aBaseProductList;
    }

    /**
     * @param array $aDdsProductList 基础品类
     * @return array
     */
    private function _formatProducts($aDdsProductList) {
        $aBaseProductList = [];
        foreach ($aDdsProductList as $aBaseProduct) {
            if ($aBaseProduct['remove_flag']) {
                continue;
            }

            //提前处理三方sdk 保证判断三方逻辑收敛到一处
            $iProductID = $aBaseProduct['product_id'];
            // 如果是香港三方
            if (\BizCommon\Utils\Horae::isHongKongThird($iProductID)) {
                $aBaseProduct['is_hk_tripcloud']  = 1;
            }

            $aBaseProductList[] = $aBaseProduct;
        }

        return $aBaseProductList;
    }

}
