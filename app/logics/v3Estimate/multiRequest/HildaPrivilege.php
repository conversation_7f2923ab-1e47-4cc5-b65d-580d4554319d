<?php

namespace PreSale\Logics\v3Estimate\multiRequest;

use BizLib\Client\HildaClient;
use BizLib\Log;
use Dukang\PropertyConst\Order\OrderEstimatePcId;
use Nuwa\ApolloSDK\Apollo;

use function PHPSTORM_META\type;

/**
 * class HildaPrivilege
 */
class HildaPrivilegeInfo
{
    private $oPrivilegeInfo;
    /**
     * @var BizCommonInfo
     */
    private $oBizCommonInfo;

    private static $_oInstance;
    private static $_bInit;
    public function __construct() {
    }

    /**
     * @return HildaPrivilegeInfo
     */
    public static function getInstance() {
        if (self::$_oInstance == null) {
            self::$_oInstance = new self();
        }
        
        return self::$_oInstance;
    }

    /**
     * 初始化
     * @param BizCommonInfo $oBizCommonInfo 业务信息
     */
    public function init(BizCommonInfo $oBizCommonInfo) {
        if (!self::$_bInit) {
            $this->oBizCommonInfo = $oBizCommonInfo;
        }

        self::$_bInit = true;
    }

    /**
     * 从hilda获取权益卡
     * @param array $aProductList 品类列表        
     */
    public function getHildaPrivilege($aProductList) {
        // 请求hilda灰度
        $aParams = [
            'key'    => $this->oBizCommonInfo->oPassengerInfo->iPid,
            'pid'    => $this->oBizCommonInfo->oPassengerInfo->iPid,
            'phone'  => $this->oBizCommonInfo->oPassengerInfo->sPhone,
            'city'   => $this->oBizCommonInfo->oAreaInfo->iArea,
            'county' => $this->oBizCommonInfo->oAreaInfo->iFromCounty,
        ];
        $oToggle = Apollo::getInstance()->featureToggle('privilege_hilda_upstream', $aParams);
        if (!$oToggle->allow()) {
            return;
        }
        $iNow = time();
        $aStartPoi = [
            'lng'         => $this->oBizCommonInfo->oAreaInfo->fFromLng,
            'lat'         => $this->oBizCommonInfo->oAreaInfo->fFromLat,
            'poi_id'      => $this->oBizCommonInfo->oAreaInfo->sFromPoiId,
            'displayname' => $this->oBizCommonInfo->oAreaInfo->sFromName,
            'city_id'     => $this->oBizCommonInfo->oAreaInfo->iArea,
        ];
        $aDestPoi = [
            'lng'         => $this->oBizCommonInfo->oAreaInfo->fToLng,
            'lat'         => $this->oBizCommonInfo->oAreaInfo->fToLat,
            'poi_id'      => $this->oBizCommonInfo->oAreaInfo->sToPoiId,
            'displayname' => $this->oBizCommonInfo->oAreaInfo->sToName,
            'city_id'     => $this->oBizCommonInfo->oAreaInfo->iToArea,
        ];
        $aParams = [
            'estimate_time'    => $iNow,
            'departure_time'   => $this->oBizCommonInfo->oCommonInfo->iDepartureTime,
            'airport_type'     => $this->oBizCommonInfo->oCommonInfo->iAirportType,
            'railway_type'     => '',
            'start_poi'        => $aStartPoi,
            'end_poi'          => $aDestPoi,
            'caller'           => 'pre-sale',
            'uid'              => $this->oBizCommonInfo->oPassengerInfo->iUid,
            'pid'              => $this->oBizCommonInfo->oPassengerInfo->iPid,
            'product_category' => $aProductList,
        ];
        $oResult = (new HildaClient())->privilegeQueryV2($aParams);
        if (is_array($oResult)) {
            $aResult = $oResult;
        } else {
            $aResult = $oResult->getArrayCopy();
        }

        if (isset($aResult['errno']) && $aResult['errno'] != 0) {
            Log::warning('HildaPrivilege getHildaPrivilege error', [
                'errno' => $aResult['errno'],
                'errmsg' => $aResult['errmsg'],
            ]);
            $this->oPrivilegeInfo = [];
            return;
        }

        if (is_array($aResult['data'])) {
            $this->oPrivilegeInfo = $aResult['data'];
        } else {
            $this->oPrivilegeInfo = [];
        }
    }

    /**
     * 获取惊喜特价权益卡
     * @return string 惊喜权益卡批次id
     */
    public function getSurprisePrivilege() {
        $sKey = strval(OrderEstimatePcId::EstimatePcIdEstimatePcIdTimeLimitSpecialRate);
        if (empty($this->oPrivilegeInfo)) {
            return '';
        }

        if (empty($this->oPrivilegeInfo[$sKey])) {
            return '';
        }

        $aSpecialRatePrivilege = $this->oPrivilegeInfo[$sKey];
        if (empty($aSpecialRatePrivilege['surprise_special_card'])) {
            return '';
        }

        $aSurpriseSpecialCard = $aSpecialRatePrivilege['surprise_special_card'];
        if (false == $aSurpriseSpecialCard['enable']) {
            return '';
        }

        return strval($aSurpriseSpecialCard['batch_id']);
    }
}