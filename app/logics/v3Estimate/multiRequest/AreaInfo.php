<?php
namespace PreSale\Logics\v3Estimate\multiRequest;

use BizCommon\Models\Order\OrderStopoverPoints;
use BizLib\ErrCode\Code;
use BizLib\Utils\Address;
use BizLib\Utils\MapHelper;
use Dirpc\SDK\PreSale\MultiEstimatePriceRequest as Request;
use BizLib\Exception\InvalidArgumentException;
use BizLib\Exception\ExceptionWithResp;
use PreSale\Logics\estimatePrice\EstimateDegradeCode;

/**
 * 地理位置相关
 * Class AreaInfo
 * @package PreSale\Logics\estimatePrice\multiRequest
 */
class AreaInfo
{
    //当前经纬度
    public $fCurLat;
    public $fCurLng;
    //起点经纬度
    public $fFromLat;
    public $fFromLng;
    public $sFromPoiId;
    public $sFromPoiType;
    public $sFromAddress;
    public $sFromName;
    public $iFromCounty;
    //终点经纬度
    public $fToLat;
    public $fToLng;
    public $sToPoiId;
    public $sToPoiType;
    public $sToAddress;
    public $sToName;
    public $iToCounty;

    public $sMapType;
    public $sFromPoiCode;
    public $sDestPoiCode;
    public $sDestPoiTag;
    public $iArea;
    public $iFromCityDesc;
    public $iToArea;
    public $iToCityDesc;
    public $iDistrict;

    public $sStartingName;
    public $sDestName;
    public $sStartCountyName;
    public $sDestCountyName;
    public $sAbstractDistrict;

    public $sCanonicalCountryCode;

    public $aStopoverPoints;

    //用户选择起终点点请求ID
    public $sChooseFSearchid;
    public $sChooseTSearchid;

    /**
     * AreaInfo constructor.
     * @param Request $oEstimateRequest oEstimateRequest
     * @throws \Exception Exception
     */
    public function __construct(Request $oEstimateRequest) {
        $this->fCurLat      = $oEstimateRequest->getLat();
        $this->fCurLng      = $oEstimateRequest->getLng();
        $this->fFromLat     = $oEstimateRequest->getFromLat();
        $this->fFromLng     = $oEstimateRequest->getFromLng();
        $this->sFromPoiId   = $oEstimateRequest->getFromPoiId();
        $this->sFromPoiType = $oEstimateRequest->getFromPoiType();
        $this->sFromAddress = $oEstimateRequest->getFromAddress();
        $this->sFromName    = $oEstimateRequest->getFromName();

        $this->fToLat     = $oEstimateRequest->getToLat();
        $this->fToLng     = $oEstimateRequest->getToLng();
        $this->sToPoiId   = $oEstimateRequest->getToPoiId();
        $this->sToPoiType = $oEstimateRequest->getToPoiType();
        $this->sToAddress = $oEstimateRequest->getToAddress();
        $this->sToName    = $oEstimateRequest->getToName();

        $this->sChooseFSearchid = $oEstimateRequest->getChooseFSearchid();
        $this->sChooseTSearchid = $oEstimateRequest->getChooseTSearchid();

        if ($this->sFromAddress || $this->sFromName) {
            Address::merge($this->sStartingName,$this->sFromName,$this->sFromAddress);
        }

        if ($this->sToAddress || $this->sToName) {
            Address::merge($this->sDestName,$this->sToName,$this->sToAddress);
        }

        $this->sMapType     = $oEstimateRequest->getMaptype();
        $this->sFromPoiCode = $oEstimateRequest->getFromPoiCode();
        $this->sDestPoiCode = $oEstimateRequest->getDestPoiCode();
        $this->sDestPoiTag  = $oEstimateRequest->getDestPoiTag();
        $this->_buildAreaInfo();
        $this->_buildStopoverPoints($oEstimateRequest);
    }

    /**
     * 构建出发和终点信息
     * @throws \Exception Exception
     * @return void
     */
    private function _buildAreaInfo() {

        $aFromArea = MapHelper::getAreaInfoByLoc($this->fFromLng, $this->fFromLat);
        if (!empty($aFromArea)) {
            $this->iArea       = $aFromArea['id'];
            $this->iDistrict   = $aFromArea['district'];
            $this->iFromCounty = $aFromArea['countyid'];
            $this->sCanonicalCountryCode = $aFromArea['canonical_country_code'];
            $this->sAbstractDistrict     = $this->iDistrict.','.$this->iFromCounty;
            $this->iFromCityDesc         = $aFromArea['name'];
            $this->sStartCountyName      = $aFromArea['county_name'] ?? '';
        }

        // 起点城市获取失败，默认为0，走降级
        if (empty($this->iArea)) {
            throw new ExceptionWithResp(
                Code::E_COMMON_HTTP_READ_FAIL,
                EstimateDegradeCode::R_ESTIMATE_DEGRADE_LOC_FAIL,
                '',
                ['from_lng' => $this->fFromLng, 'from_lat' => $this->fFromLat, 'ret' => json_encode($aFromArea)]
            );
        }

        $aToArea = MapHelper::getAreaInfoByLoc($this->fToLng, $this->fToLat);
        if (!empty($aToArea)) {
            $this->iToArea         = $aToArea['id'];
            $this->iToCounty       = $aToArea['countyid'];
            $this->iToCityDesc     = $aToArea['name'];
            $this->sDestCountyName = $aToArea['county_name'] ?? '';
        }
    }

    /**
     * 解析途经点
     *
     * @param  Request $oEstimateRequest $oEstimateRequest
     * @throws InvalidArgumentException InvalidArgumentException
     * @return void
     */
    private function _buildStopoverPoints(Request $oEstimateRequest) {
        $this->aStopoverPoints = [];
        $sStopOverPointsStr    = $oEstimateRequest->getStopoverPoints();
        if (empty($sStopOverPointsStr) || '[]' == $sStopOverPointsStr) {
            return;
        }

        $aPointsInfo = json_decode($oEstimateRequest->getStopoverPoints(), true);
        $aPointsInfo = OrderStopoverPoints::getPointsInfoFromRequest($aPointsInfo);
        if (empty($aPointsInfo)) {
            throw new InvalidArgumentException(
                Code::E_COMMON_PARAM_INVALID_VALUE,
                [
                    'from_poi_id'     => $oEstimateRequest->getFromPoiId(),
                    'to_poi_id'       => $oEstimateRequest->getToPoiId(),
                    'stopover_points' => $oEstimateRequest->getStopoverPoints(),
                ]
            );
        }

        $this->aStopoverPoints = $aPointsInfo;
    }
}
