<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @brief  批量请求会员
 * @date 2020-09-15
 * //由于时间紧张，目前采用的方案为请求完后直接将结果塞到全局存储中，构建产品时取出的方案，后续优化一版，结果直接内部传递即可
 */
namespace PreSale\Logics\v3Estimate\multiRequest;

use BizLib\Utils\Product;
use BizLib\Utils\Registry;
use Dirpc\SDK\PreSale\MultiEstimatePriceRequest as Request;
use Nuwa\ApolloSDK\Apollo;
use BizLib\Client\MemberSystemClient;
use BizLib\Utils\Language;
use BizLib\Utils\MemberVersion;

/**
 * Class MemberInfo
 * @package PreSale\Logics\estimatePrice\multiRequest
 */
class MemberInfo
{
    //dds获取的产品列表
    private $_aBaseProductList;
    //请求信息
    private $_oRequest;
    //乘客信息
    private $_aPassengerInfo;
    //area info
    private $_iCityId;
    //area info
    private $_iToCityId;
    //乘客Uid
    private $_iUid;
    //乘客Pid
    private $_iPid;
    //乘客phone
    private $_sPhone;
    //语言信息
    private $_sLang;
    //待查询产品N元组
    private $_aProductList = [];
    //批量会员信息
    private $_aMultiMemberInfo = [];
    //单产品线会员信息
    public $aMemberInfo;
    // 会员等级
    public static $iMemberLevel;

    /**
     * MemberInfo constructor.
     *
     * @param Request $oRequest         请求信息
     * @param array   $aPassengerInfo   乘客信息
     * @param array   $aBaseProductList 产品列表
     * @param string  $sLang            语言信息
     * @param int     $iCityId          城市信息
     * @param int     $iToCityId        终点城市信息
     */
    public function __construct(Request $oRequest, $aPassengerInfo, $aBaseProductList, $sLang, $iCityId, $iToCityId) {
        $this->_oRequest         = $oRequest;
        $this->_aPassengerInfo   = $aPassengerInfo;
        $this->_aBaseProductList = $aBaseProductList ?? [];
        $this->_iCityId          = $iCityId;
        $this->_sLang            = $sLang;
        $this->_iToCityId        = $iToCityId;
    }

    /**
     * Function _buildPassengerInfo 构建乘客信息
     *
     * @return bool
     */
    private function _buildPassengerInfo() {
        if (empty($this->_aPassengerInfo)) {
            return false;
        }

        $this->_iUid   = (int)$this->_aPassengerInfo['uid'];
        $this->_iPid   = (int)$this->_aPassengerInfo['pid'];
        $this->_sPhone = $this->_aPassengerInfo['phone'];

        return true;
    }

    /**
     * Function _buildProductList 构建待请求产品列表
     *
     * @return void
     */
    private function _buildProductList() {
        $aProductIdList = [];
        foreach ($this->_aBaseProductList as $iIndex => $aBaseProduct) {
            if ($aBaseProduct['remove_flag']) {
                continue;
            }

            if ($aBaseProduct['is_tripcloud']) {
                continue;
            }

            //每个产品线只请求一次
            $sKey = (string)$aBaseProduct['product_id'];
            if (array_key_exists($sKey, $aProductIdList)) {
                continue;
            } else {
                $aProductIdList[$sKey] = 1;
            }

            $aSingleProduct = array(
                'business_id'   => $aBaseProduct['business_id'],
                'combo_type'    => $aBaseProduct['combo_type'],
                'carpool_type'  => $aBaseProduct['carpool_type'],
                'require_level' => $aBaseProduct['require_level'],
            );

            //此处是否需要再次过滤相关产品或者改写相关信息（待定）
            //{"business_id":260,"combo_type":4,"carpool_type":0,"require_level":100}
            $this->_aProductList[] = $aSingleProduct;
        }
    }

    /**
     * Function buildMemberInfo 批量请求会员并返回结果
     *
     * @return void
     */
    public function buildMemberInfo() {
        // 产品列表为空直接返回
        if (empty($this->_aBaseProductList)) {
            return;
        }

        //获取不到乘客信息则直接返回
        if (!$this->_buildPassengerInfo()) {
            return;
        }

        //仅支持中文
        if (Language::ZH_CN != $this->_sLang) {
            return;
        }

        $aHitInfo = array(
            'city_id'        => $this->_iCityId,
            'version'        => 2,
            'order_property' => json_encode(
                [
                    'start_poi'      => [
                        'lat'     => (string)$this->_oRequest->getFromLat(),
                        'lng'     => (string)$this->_oRequest->getFromLng(),
                        'city_id' => (int)$this->_iCityId,
                    ],
                    'end_poi'        => [
                        'lat'     => (string)$this->_oRequest->getToLat(),
                        'lng'     => (string)$this->_oRequest->getToLng(),
                        'city_id' => (int)$this->_iToCityId,
                    ],
                    'departure_time' => $this->_handlerDepartureTime($this->_oRequest->getDepartureTime()),
                ]
            ),
        );

        $this->_buildProductList();

        //检查待请求产品列表是否为空
        if (empty($this->_aProductList)) {
            return;
        }

        $oMemberClient    = new MemberSystemClient();
        $aMultiMemberInfo = $oMemberClient->multiProductsQueryInfoV2(
            $this->_iUid,
            $this->_sLang,
            $this->_aProductList,
            $aHitInfo
        );

        if (!empty($aMultiMemberInfo['product_list'])) {
            foreach ($aMultiMemberInfo['product_list'] as $aProductInfo) {
                $aSingleMemberInfo = $oMemberClient->queryInfoAdapter(
                    $aProductInfo,
                    $aHitInfo
                );
                $iBusinessId       = $aProductInfo['product_info']['business_id'];
                $this->_aMultiMemberInfo[$iBusinessId] = $aSingleMemberInfo;
                if (!isset($iMemberLevel) && !empty($aSingleMemberInfo['level_id'])) {
                    self::$iMemberLevel = $aSingleMemberInfo['level_id'];
                }
            }
        }
    }

    /**
     * @param int        $iProductCategory $iProductCategory
     * @param int        $iBusinessID      $iBusinessID
     * @param CommonInfo $oCommonInfo      $oCommonInfo
     * @return array
     */
    public function getProductMemberProfile($iProductCategory, $iBusinessID, CommonInfo $oCommonInfo) {
        // 没传use_dpa默认使用
        if (isset($oCommonInfo->aMultiRequireProducts[$iProductCategory]['use_dpa'])) {
            $iUseDpa       = (int)$oCommonInfo->aMultiRequireProducts[$iProductCategory]['use_dpa'];
            $bIsUserUseDpa = true;
        } else {
            $iUseDpa       = 1;
            $bIsUserUseDpa = false;
        }

        $aMemberInfo['use_dpa_selected'] = $iUseDpa > 0 ? 1 : 0;  // 是否使用dpa
        $aMemberInfo['is_user_use_dpa']  = $bIsUserUseDpa;        // 是否用户主动选择
        if (!$iUseDpa) {
            $aMemberInfo['member_profile'] = [];
            return $aMemberInfo;
        }

        $aMemberProfile = $this->_aMultiMemberInfo[$iBusinessID];

        // 筛选必要字段
        $aMemberInfo['member_profile'] = [
            'level_id'       => $aMemberProfile['level_id'],
            'level_icon'     => $aMemberProfile['level_icon'],
            'level_name'     => $aMemberProfile['level_name'],
            'level_name_new' => $aMemberProfile['level_name_new'],
            'privileges'     => [
                'dpa'      => $aMemberProfile['privileges']['dpa'],
                'fast_way' => $aMemberProfile['privileges']['fast_way'],
            ],
        ];

        return $aMemberInfo;
    }

    /**
     * @param int $iBusinessID $iBusinessID
     * @return array
     */
    public function buildMemberPrivilegeList($iBusinessID) {

        $aMemberProfile = $this->_aMultiMemberInfo[$iBusinessID];

        // 用户权益列表
        $aMemberInfo = [];

        // 优化 apollo 自动配置哪些传下去
        if (!empty($aMemberProfile['privileges']['airport_guide'])) {
            $aMemberInfo['member_privilege_list']['airport_guide_right'] = 1;
        }

        if (!empty($aMemberProfile['privileges']['airport_pick_up'])) {
            $aMemberInfo['member_privilege_list']['airport_pick_up'] = 1;
        }

        if (!empty($aMemberProfile['privileges']['upgrade_spacious_car'])) {
            $aMemberInfo['member_privilege_list']['upgrade_spacious_car'] = 1;
        }

        return $aMemberInfo;
    }

    /**
     * @param int $iBusinessID $iBusinessID
     * @return mixed
     */
    public function getMemberInfoByBusinessID($iBusinessID) {
        return $this->_aMultiMemberInfo[$iBusinessID];
    }

    /**
     * 处理出发时间
     * @param string $departureTime 出发时间
     * @return int
     */
    private function _handlerDepartureTime($departureTime) {
        $res = explode('.', $departureTime);
        return (int)($res[0]);
    }
}
