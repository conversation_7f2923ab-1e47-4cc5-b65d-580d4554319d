<?php
namespace PreSale\Logics\v3Estimate;

use BizLib\Client\SsseClient;
use BizLib\Config;
use BizLib\Config as NuwaConfig;
use BizLib\Constants\Common;
use BizLib\Constants\OrderSystem;
use BizLib\Client\UfsClient;
use BizLib\Utils\ProductCategory;
use Dirpc\SDK\Hundun\SceneCommReq;
use Dirpc\SDK\Hundun\SceneReq;
use Dirpc\SDK\PreSale\AdditionalService;
use Dirpc\SDK\PreSale\AdditionalServiceData;
use Dirpc\SDK\PreSale\MultiEstimatePriceRequest as Request;
use Nuwa\ApolloSDK\Apollo;
use Dirpc\SDK\Ssse;
use PreSale\Logics\estimatePriceV2\SpsAddServicePriceLogic;
use PreSale\Logics\taxi\TaxiPeakFee;
use PreSale\Logics\v3Estimate\multiRequest\AreaInfo;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;
use PreSale\Logics\v3Estimate\multiRequest\CommonInfo;
use PreSale\Logics\v3Estimate\multiRequest\Product;
use PreSale\Logics\scene\custom\CustomLogic;
use BizLib\Constants\Horae;
use Dukang\PropertyConst\Product\ProductPageType;
use PreSale\Logics\taxi\TaxiLanKeBao;
use PreSale\Logics\taxi\TaxiPricingByOnline;
use PreSale\Logics\v3Estimate\multiResponse\Component\preferInfo\UniTaxiFeaturePrefer;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TaxiPricingBoxLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutBuilder;
use PreSale\Models\rpc\HundunRpc;
use BizLib\Utils\UtilHelper;

/**
 * Class AdditionalServiceLogic 增值服务处理逻辑
 */
class AdditionalServiceLogic
{

    private static $_oInstance = null;
    private $_oSsseClient;

    const APOLLO_CONF_SERVICE_LIST = 'payment_type_component';
    const APOLLO_CONF_SERVICE_FILE = 'additional_service';
    const APOLLO_TOGGLE_HUNDUN_CONTROLLER = 'hundun_access_controller';
    const HUNDUN_CALLEE = 'hundun';

    private $_aProductMap = []; // 定制化服务支持的品类

    /**
     * 存储ssse响应 单品类支持的服务信息
     */
    private $_aPcId2CustomFeatureFromSsse = []; // service(map {"pcid":[{服务1},{服务2}]})

    /**
     * 存储ssse响应 单品类支持的服务的信息
     */
    private $_aPcId2ServiceId2ServiceInfo = []; // service(map {"pcid":["serviceId1":{服务1},"serviceId2":{服务2}]})


    /**
     * 存储ssse响应 预估级别支持的附加需求
     */
    private $_aPcId2AddServiceIdListFromSsse = [];
    private $_aAddServiceIdListFromSsse      = []; // ssse返回的支持的附加需求全集 array
    private $_aAvailableServiceMap           = []; //s3e支持的增值服务

    private $_aSceneData = [];

    public  $aChooseCustomFeatureService = []; // 用户选择的品类下的服务  [{"pcid":[{id:1,count:1}]}]
    private $_aChooseAdditionalService   = []; // 用户选择的预估级别的附加需求
    public  $aSystemCustomFeatureService = []; // 系统默认勾选的品类下的服务  [{"pcid":[{id:1,count:1}]}]
    public  $aUfsPreferOption = []; // 为减少ufs的调用次数，此处缓存ufs的结果
    public  $aUfsCustomFeature = []; // 为减少ufs的调用次数，此处缓存ufs的结果


    private $_aNeedFilterServiceIdConfig = []; // 需要过滤品类的id配置
    private $_aNeedFilterServiceId       = []; //  需要过滤品类的真实id

    public static $bHadFilterProduct = false;
    private $_bServiceAvailable      = false; //s3e服务是否可用

    // PcIdToSubGroupId
    private static $aPcIdToSubGroupId = [];

    /**
     * AdditionalServiceLogic constructor.
     */
    private function __construct() {
        $this->_oSsseClient = new SsseClient();
    }

    /**
     * @return AdditionalServiceLogic|null
     */
    public static function getInstance() {
        if (!self::$_oInstance instanceof self) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }

    /**
     * 初始化数据
     * @param Request       $oRequest         req
     * @param BizCommonInfo $oBizCommonInfo   公共数据
     * @param array         $aBaseProductList 基础品类数据
     * @return void
     */
    public function init(Request $oRequest, BizCommonInfo $oBizCommonInfo, $aBaseProductList) {
        $aPassengerInfo = $oBizCommonInfo->oPassengerInfo->getPassengerInfo();
        $oAreaInfo      = $oBizCommonInfo->oAreaInfo;
        // 6.0场景入口是否支持个性化服务露出
        if (!(new Apollo())->featureToggle(
            'gs_support_custom_service_switch',
            array(
                'key'         => $aPassengerInfo['pid'],
                'city'        => $oAreaInfo->iArea,
                'phone'       => $aPassengerInfo['phone'],
                'page_type'   => $oRequest->getPageType(),
                'app_version' => $oRequest->getAppVersion(),
                'menu_id'     => $oRequest->getMenuId(),
                'lang'        => $oBizCommonInfo->getAppLanguage(),
            )
        )->allow()
        ) {
            return;
        }

        // init 配置
         $this->_initConfig();

        // 获取盒子信息，用于峰期加价与揽客宝下挂是否出现的判断
        list(self::$aPcIdToSubGroupId, ) = DecisionV2Service::getAggConfByGroupId(
            [
                LayoutBuilder::SUB_GROUP_ID_TAXI_PRICING,
                LayoutBuilder::SUB_GROUP_ID_UNITAXI,
            ],
            [
                'order_type'    => OrderSystem::TYPE_ORDER_NOW,
                'pid'           => $oBizCommonInfo->oPassengerInfo->iPid,
                'phone'         => $oBizCommonInfo->oPassengerInfo->sPhone,
                'county_id'     => $oBizCommonInfo->oAreaInfo->iFromCounty,
                'city'          => $oBizCommonInfo->oAreaInfo->iArea,
                'lang'          => $oBizCommonInfo->getAppLanguage(),
                'access_key_id' => $oBizCommonInfo->getAccessKeyID(),
                'app_version'   => $oBizCommonInfo->getAppVersion(),
            ]
        );

        // 收集用户勾选的 预估级别 增值服务
        $this->buildUserChooseAdditionalService($oRequest);

        // 收集用户勾选的 品类下 增值服务
        $this->buildUserChoosePcId2AdditionalService($oBizCommonInfo->oCommonInfo);

        // 从ssse获取支持的品类
        $this->handlerSsseData($aBaseProductList, $oBizCommonInfo, $oRequest);

        // 从ufs获取获取prefer_info和custom_feature信息
        $this->initCustomFeatureAndPreferData($oBizCommonInfo->getPassengerID());
    }

    /**
     * 根据用户勾选的附加需求(全品类增值服务)
     * 过滤不支持的品类 1.用户勾选&&品类不支持该服务
     * @param BizCommonInfo $oBizCommonInfo     公共数据
     * @param array         $aOriginProductList 原始品类list
     * @return array
     */
    public function filterProducts(BizCommonInfo $oBizCommonInfo, $aOriginProductList) {

        if (empty($this->_aNeedFilterServiceId) || !$this->_shouldFilterProduct($oBizCommonInfo)) {
            // 用户没选择附加需求, 不需要过滤
            return $aOriginProductList;
        }

        $aProductList = [];
        foreach ($aOriginProductList as $aBaseProduct) {
            // 获取某个品类 支持的附加需求id列表
            $aSupportIdList = $this->_aPcId2AddServiceIdListFromSsse[$aBaseProduct['product_category']];
            if ($this->isIncludeService($aSupportIdList)) {
                // 过滤
                self::$bHadFilterProduct = true;
                continue;
            }

            $aProductList[] = $aBaseProduct;
        }

        return $aProductList;
    }

    /**
     * @param BizCommonInfo $oBizCommonInfo 公共数据
     * @return bool
     */
    private function _shouldFilterProduct(BizCommonInfo $oBizCommonInfo): bool {
        return Apollo::getInstance()->featureToggle(
            'pre_sale_filter_product_by_page_type_toggle',
            [
                'key'           => $oBizCommonInfo->oCommonInfo->iPageType,
                'app_version'   => $oBizCommonInfo->oCommonInfo->sAppVersion,
                'access_key_id' => $oBizCommonInfo->oCommonInfo->iAccessKeyID,
                'caller'        => 'pre-sale',
                'page_type'     => $oBizCommonInfo->oCommonInfo->iPageType,
            ]
        )->allow();
    }

    /**
     * 返回该品类是否需要被过滤
     * @param array $aServiceList 该品类支持的服务list
     * @return bool true:需要过滤
     */
    public function isIncludeService($aServiceList) {
        if (empty($aServiceList)) {
            return true;
        }

        foreach ($this->_aNeedFilterServiceId as $iServiceId) {
            if (!$this->_aNeedFilterServiceIdConfig[$iServiceId]) {
                // 该服务不需要过滤品类
                continue;
            }

            // 需要过滤 && 服务不在品类支持的服务中  -> 过滤品类
            if (!in_array($iServiceId, $aServiceList)) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param CommonInfo $oCommonInfo CommonInfo对象
     * @return void
     */
    public function buildUserChoosePcId2AdditionalService(CommonInfo $oCommonInfo) {
        $aCommonCustomService = [];

        $aMultiInfo = $oCommonInfo->aMultiRequireProducts;
        if (empty($aMultiInfo)) {
            return;
        }

        foreach ($aMultiInfo as $iPcId => $aData) {
            if (empty($iPcId) || empty($aData['custom_feature']) || !is_string($aData['custom_feature'])) {
                continue;
            }

            // 校验 ProductCategory 有效
            $aMap = $this->_aProductMap[(int)($aData['product_category'])];
            if (empty($aMap)) {
                continue;
            }

            // [{"id":102,"count":1}]
            $aCustomFeatures = json_decode($aData['custom_feature'] , true);
            if (empty($aCustomFeatures) || !is_array($aCustomFeatures)) {
                continue;
            }

            $aOptionCusSvc = $aCommonCustomService;

            foreach ($aCustomFeatures as $aItem) {
                if (empty($aItem['id']) || empty($aItem['count']) || $aItem['count'] < 0) {
                    continue;
                }

                if ($this->_aNeedFilterServiceIdConfig[$aItem['id']]) {
                    $this->_aNeedFilterServiceId[] = $aItem['id'];
                }

                $aOptionSvc = [
                    'id'    => $aItem['id'],
                    'count' => $aItem['count'],
                ];
                if (!empty($aItem['service_type'])) {
                    $aOptionSvc['service_type'] = $aItem['service_type'];
                }

                $aOptionCusSvc[] = $aOptionSvc;
            }

            $this->aChooseCustomFeatureService[$iPcId] = $aOptionCusSvc;
        }
    }


    /**
     * 请求ssse, 通过category获取各个品类的增值服务
     * @param array         $aBaseProductList 基础品类
     * @param BizCommonInfo $oBizCommonInfo   公共数据
     * @param Request       $oEstimateRequest req
     * @return void
     */
    public function handlerSsseData(array $aBaseProductList, BizCommonInfo $oBizCommonInfo, Request $oEstimateRequest) {
        $aPassengerInfo = $oBizCommonInfo->oPassengerInfo->getPassengerInfo();
        // 未登陆情况不走hundun
        if (empty($aPassengerInfo['pid']) && empty($aPassengerInfo['uid'])) {
            return;
        }

        // hundun 降级开关
        $oApollo       = new Apollo();
        $bIsOpenHundun = $oApollo->featureToggle(
            self::APOLLO_TOGGLE_HUNDUN_CONTROLLER,
            [
                'city'   => $oBizCommonInfo->oAreaInfo->iArea,
                'phone'  => $aPassengerInfo['phone'],
                'caller' => 'pre_sale',
            ]
        )->allow();

        if (!$bIsOpenHundun) {
            return;
        }

        //切量
        $this->_handlerHundunData($aBaseProductList,$oBizCommonInfo,$aPassengerInfo, $oEstimateRequest);
        return;
    }

    /**
     * @param array         $aBaseProductList 原始oneconf
     * @param BizCommonInfo $oBizCommonInfo   基础配置
     * @param array         $aPassengerInfo   user
     * @param Request       $oEstimateRequest req
     * @return array
     */
    private function _getService($aBaseProductList, BizCommonInfo $oBizCommonInfo, $aPassengerInfo, $oEstimateRequest) {
        if (Common::MENU_DACHE_ANYCAR != $oEstimateRequest->getMenuId()) {
            return [];
        }

        $oAreaInfo = $oBizCommonInfo->oAreaInfo;
        // 个性化服务公共参数
        $aBaseProduct = [];
        foreach ($aBaseProductList as $aProduct) {
            if (isset($aProduct['order_info']['airport_info'])) {
                $aBaseProduct = $aProduct;
                break;
            }
        }

        if (empty($aBaseProduct)) {
            $aBaseProduct = current($aBaseProductList);
        }

        $aCommonRequestParam = $this->_getCommonRequestParams($aBaseProduct, $aPassengerInfo, $oAreaInfo, $oEstimateRequest);

        $aReqs       = [];
        $oSsseParams = new Ssse\GetMultiServiceReq();
        foreach ($aBaseProductList as $aOneConfItem) {
            $aParam['business_id']      = (int)($aOneConfItem['business_id']);
            $aParam['combo_type']       = (int)($aOneConfItem['combo_type']);
            $aParam['require_level']    = (string)($aOneConfItem['require_level']);
            $aParam['product_category'] = (string)($aOneConfItem['product_category']);
            $aParam += $aCommonRequestParam;

            $oReq = new Ssse\GetServiceReq($aParam);
            $oReq->setExtraInfo(['gender' => $oBizCommonInfo->oPassengerInfo->iUserGender]); //性别
            $aReqs[] = $oReq;
        }

        $oSsseParams->setCaller(MODULE_NAME);
        $oSsseParams->setReqs($aReqs);

        $aRet = $this->_oSsseClient->getService($oSsseParams);
        if (0 != $aRet['errno'] || !isset($aRet['errno'])) {
            return [];
        } else {
            return $aRet['data'];
        }
    }


    /**
     * @param array         $aBaseProductList 原始oneconf
     * @param BizCommonInfo $oBizCommonInfo   基础配置
     * @param array         $aPassengerInfo   user
     * @param Request       $oEstimateRequest req
     * @return void
     */
    private function _handlerHundunData($aBaseProductList, BizCommonInfo $oBizCommonInfo, $aPassengerInfo, $oEstimateRequest) {
        //常规参数，减少计算逻辑
        $iDepartureTime = empty($oEstimateRequest->getDepartureTime()) ? time() : $oEstimateRequest->getDepartureTime();

        // 基准品类，机场信息与订单类型以基准品类为准
        $aBaseProduct = [];
        foreach ($aBaseProductList as $aProduct) {
            if (isset($aProduct['order_info']['airport_info'])) {
                $aBaseProduct = $aProduct;
                break;
            }
        }

        if (empty($aBaseProduct)) {
            $aBaseProduct = current($aBaseProductList);
        }

        //Hundun公用参数
        $hundunCommonReq = new SceneCommReq();
        $hundunCommonReq->setCaller(MODULE_NAME);
        $hundunCommonReq->setAppVersion($oEstimateRequest->getAppVersion());
        $hundunCommonReq->setAccessKeyId($oEstimateRequest->getAccessKeyId());
        $hundunCommonReq->setLang($oEstimateRequest->getLang());
        $hundunCommonReq->setChannel($oEstimateRequest->getChannel());
        $hundunCommonReq->setPid($oBizCommonInfo->oPassengerInfo->iPid);
        $hundunCommonReq->setArea($oBizCommonInfo->oAreaInfo->iArea);
        $hundunCommonReq->setPhone($oBizCommonInfo->oPassengerInfo->sPhone);
        $hundunCommonReq->setCallee(self::HUNDUN_CALLEE);
        $hundunCommonReq->setDistrict($oBizCommonInfo->oAreaInfo->iDistrict);
        $hundunCommonReq->setCountyId($oBizCommonInfo->oAreaInfo->iToCounty);

        //Hundun节点参数
        $aReqs = [];
        foreach ($aBaseProductList as $aOneConfItem) {
            $aParam['product_category'] = (int)($aOneConfItem['product_category']);
            $aParam['product_id']       = (int)($aOneConfItem['product_id']);
            $aParam['business_id']      = (int)($aOneConfItem['business_id']);
            $aParam['combo_type']       = (int)($aOneConfItem['combo_type']);
            $aParam['carpool_type']     = (int)($aOneConfItem['carpool_type']);
            $aParam['require_level']    = (string)($aOneConfItem['require_level']);
            $aParam['pid']            = (int)$aPassengerInfo['pid'];
            $aParam['phone']          = (string)$aPassengerInfo['phone'];
            $aParam['access_key_id']  = $oEstimateRequest->getAccessKeyId();
            $aParam['app_version']    = $oEstimateRequest->getAppVersion();
            $aParam['lang']           = $oEstimateRequest->getLang();
            $aParam['area']           = $oBizCommonInfo->oAreaInfo->iArea;
            $aParam['flat']           = $oBizCommonInfo->oAreaInfo->fFromLat;
            $aParam['flng']           = $oBizCommonInfo->oAreaInfo->fFromLng;
            $aParam['tlat']           = $oBizCommonInfo->oAreaInfo->fToLat;
            $aParam['tlng']           = $oBizCommonInfo->oAreaInfo->fToLng;
            $aParam['type']           = (int)$aBaseProduct['order_type'];
            $aParam['page_type']      = $oEstimateRequest->getPageType();
            $aParam['call_car_type']  = $oEstimateRequest->getCallCarType();
            $aParam['departure_time'] = (int)$iDepartureTime;
            $aParam['menu_id']        = $oEstimateRequest->getMenuId();
            $aParam['sub_menu_id']    = '';
            $aParam['traffic_number'] = $oEstimateRequest->getTrafficNumber();
            $aParam['traffic_dep_time']    = $oEstimateRequest->getTrafficDepTime();
            $aParam['airport_id']          = $oEstimateRequest->getAirportId();
            $aParam['flight_dep_code']     = $oEstimateRequest->getFlightDepCode();
            $aParam['flight_dep_terminal'] = $oEstimateRequest->getFlightDepTerminal();
            $aParam['extra_info']          = ['gender' => $oBizCommonInfo->oPassengerInfo->iUserGender];
            //如果端上上传airport_id为空，使用围栏获取到的airport_id
            if (empty($aParam['airport_id']) && isset($aBaseProduct['order_info']['airport_info']['airport_id'])) {
                $aParam['airport_id'] = (int)$aBaseProduct['order_info']['airport_info']['airport_id'];
            }

            $aReqs[] = $aParam;
        }

        //组装Hundun请求
        $hundunReq = new SceneReq();
        $hundunReq->setCommonReq($hundunCommonReq);
        $hundunReq->setNodeReq(json_encode($aReqs));

        //获取hundun响应
        $aResponse = HundunRpc::getInstance()->getServiceList($hundunReq);
        if (empty($aResponse)) {
            return;
        }

        $this->_bServiceAvailable = true;
        $aSupportServiceIdList    = [];

        foreach ($aResponse as $iPcId => $aSceneDatalist) {
            if (!empty($aSceneDatalist) && is_array($aSceneDatalist)) {
                $aServiceList = $aSceneDatalist['service_data'];
                foreach ($aServiceList as $aService) {
                    //接机员引导不可用情况下，不计算服务费
                    $iServiceId = $aService['service_id'];
                    if (CustomLogic::CUSTOM_SERVICE_UN_USABLE == $aService['status']
                        && (CustomLogic::CUSTOM_SERVICE_GUIDE_NEW == $iServiceId
                        || CustomLogic::CUSTOM_SERVICE_DRIVER_PICKUP == $iServiceId)
                    ) {
                        continue;
                    }

                    $this->_aAvailableServiceMap[] = $iServiceId;
                    if (in_array($iServiceId, array_keys($this->_aNeedFilterServiceIdConfig))) {
                        $aSupportServiceIdList[] = $iServiceId;
                        // [预估级别]附加需求idList
                        $this->_aPcId2AddServiceIdListFromSsse[$iPcId][] = $iServiceId;
                    }

                    // [单品类]支持的服务list
                    $this->_aPcId2CustomFeatureFromSsse[$iPcId][] = $aService;
                    $this->_aPcId2ServiceId2ServiceInfo[$iPcId][$iServiceId] = $aService;
                }
            }
        }

        //  [预估级别]支持的服务idList全集
        $this->_aAddServiceIdListFromSsse = array_unique($aSupportServiceIdList);
        return;
    }

    /**
     * @param array    $aBaseProduct     $aBaseProduct
     * @param array    $aPassengerInfo   $aPassengerInfo
     * @param AreaInfo $oAreaInfo        $oAreaInfo
     * @param Request  $oEstimateRequest $oEstimateRequest
     * @return mixed
     */
    private function _getCommonRequestParams($aBaseProduct, $aPassengerInfo, AreaInfo $oAreaInfo, Request $oEstimateRequest) {
        $aParam['pid']            = (int)$aPassengerInfo['pid'];
        $aParam['phone']          = $aPassengerInfo['phone'];
        $aParam['type']           = (int)$aBaseProduct['order_type'];
        $aParam['area']           = $oAreaInfo->iArea;
        $aParam['flat']           = $oAreaInfo->fFromLat;
        $aParam['flng']           = $oAreaInfo->fFromLng;
        $aParam['tlat']           = $oAreaInfo->fToLat;
        $aParam['tlng']           = $oAreaInfo->fToLng;
        $aParam['call_car_type']  = $oEstimateRequest->getCallCarType();
        $aParam['departure_time'] = empty($oEstimateRequest->getDepartureTime()) ? time() : $oEstimateRequest->getDepartureTime();
        $aParam['menu_id']        = $oEstimateRequest->getMenuId();
        $aParam['access_key_id']  = $oEstimateRequest->getAccessKeyId();
        $aParam['sub_menu_id']    = '';
        $aParam['app_version']    = $oEstimateRequest->getAppVersion();
        $aParam['lang']           = $oEstimateRequest->getLang();
        $aParam['traffic_number'] = $oEstimateRequest->getTrafficNumber();
        $aParam['airport_id']     = $oEstimateRequest->getAirportId();
        $aParam['flight_dep_code']     = $oEstimateRequest->getFlightDepCode();
        $aParam['flight_dep_terminal'] = $oEstimateRequest->getFlightDepTerminal();
        $aParam['traffic_dep_time']    = $oEstimateRequest->getTrafficDepTime();
        $aParam['page_type']           = $oEstimateRequest->getPageType();

        //如果端上上传airport_id为空，使用围栏获取到的airport_id
        if (empty($aParam['airport_id']) && isset($aBaseProduct['order_info']['airport_info']['airport_id'])) {
            $aParam['airport_id'] = (int)$aBaseProduct['order_info']['airport_info']['airport_id'];
        }

        return $aParam;
    }

    /**
     * 构建该品类已选服务 作为price-api参数
     * @param Product       $oProduct       oProduct
     * @param BizCommonInfo $oBizCommonInfo 公共数据
     * @return void
     */
    public function buildPriceParamsByCustomService(Product $oProduct, $oBizCommonInfo) {
        $oCommonInfo    = $oBizCommonInfo->oCommonInfo;
        $oPassengerInfo = $oBizCommonInfo->getPassengerInfo();
        $aMap           = $this->_aProductMap[(int)($oProduct->oOrderInfo->iProductCategory)];

        if (empty($aMap)) {
            return;
        }

        // 存储参数 ArrayObject为了是空不被序列化为'{}'
        $aPersonalized  = new \ArrayObject();
        $aCustomFeature = new \ArrayObject();

        //  需要包含 1. 附加需求(不需要校验,不支持的品类已经被过滤)
        $aAdditionalService = $this->_aChooseAdditionalService;
        foreach ($aAdditionalService as $aService) {
            if (CustomLogic::CUSTOM_SERVICE_LANKEBAO_PICKUP == $aService['id']) {
                //二次预估 揽客宝入参不给price-api带
                continue;
            }

            $aPersonalized[$aService['id']] = array('count' => $aService['count']);
            if (!empty($aService['service_type'])) {
                $aPersonalized[$aService['id']]['service_type'] = $aService['service_type'];
            }
        }

        //  需要包含 2, 单品类需求(需要校验)
        $iPcId = $oProduct->oOrderInfo->iProductCategory;
        foreach (($this->aChooseCustomFeatureService[$iPcId] ?? []) as $aItem) {
            if ($this->checkServiceAvailable($iPcId, $aItem['id'])) {
                if (CustomLogic::CUSTOM_SERVICE_LANKEBAO_PICKUP == $aItem['id']) {
                    //揽客宝不带
                    continue;
                }

                $aPersonalized[$aItem['id']] = array('count' => $aItem['count']);
                if (!empty($aItem['service_type'])) {
                    $aPersonalized[$aItem['id']]['service_type'] = $aItem['service_type'];
                }
            }
        }

        //  需要包含 3. page_type转化出来的需求(不需要校验)
        $iPageType = $oCommonInfo->iPageType;
        switch ($iPageType) {
            case Horae::PAGE_TYPE_NO_OBSTACLE:
                $aPersonalized[CustomLogic::CUSTOM_SERVICE_DISABLED] = array('count' => 1);
                break;
            case Horae::PAGE_TYPE_PET_CAR:
                $aApolloParams = [
                    'key'           => $oPassengerInfo->iPid,
                    'uid'           => $oPassengerInfo->iUid,
                    'pid'           => $oPassengerInfo->iPid,
                    'phone'         => $oPassengerInfo->sPhone,
                    'app_version'   => $oCommonInfo->sAppVersion,
                    'city'          => $oBizCommonInfo->oAreaInfo->iArea,
                    'access_key_id' => $oCommonInfo->iAccessKeyID,
                    'caller'        => 'pre-sale',
                ];
                $bAllow        = (new Apollo())->featureToggle(
                    'gs_five_star_premier_toggle',
                    $aApolloParams
                )->allow();
                if ($bAllow) {
                    $aPersonalized[CustomLogic::CUSTOM_SERVICE_FIVE_STAR] = array('count' => 1);
                } elseif (empty($aPersonalized[CustomLogic::CUSTOM_SERVICE_PET_CAR])) { // 优先取$aAdditionalService
                    $aPersonalized[CustomLogic::CUSTOM_SERVICE_PET_CAR] = array('count' => 1);
                }
                break;
            case 37:
                $aPersonalized[CustomLogic::CUSTOM_SERVICE_FIVE_STAR] = array('count' => 1);
                break;
            case 45: //只有揽客宝独立页才带
                $aPersonalized[CustomLogic::CUSTOM_SERVICE_LANKEBAO_PICKUP] = array('count' => 1);
                break;
            default:
                break;
        }

        //  需要包含 4. 特殊场景单独决策
        // 出租车峰期加价场景字段透传
        if ($this->_bIsNeedBuildTaxiPeakFee($oProduct, $oBizCommonInfo)) {
            $aPersonalized[CustomLogic::CUSTOM_SERVICE_TAXI_PEAK] = array('count' => 1);
        }

        // 夜间偏好-女司机优先场景
        if ($this->_bIsNeedNightPreference($iPcId, $oBizCommonInfo)) {
            $aPersonalized[CustomLogic::CUSTOM_SERVICE_FEMALE_DRIVER_FIRST] = array('count' => 1);
        }

        //机场助理默认勾选
        if ($this->isNeedDefaultCheckGuideNewCustomService($oBizCommonInfo->getApolloParams($oBizCommonInfo->getPassengerID())) && $this->_defaultCheckGuideNew($oBizCommonInfo, $iPcId)) {
            $aPersonalized[CustomLogic::CUSTOM_SERVICE_GUIDE_NEW] = array('count' => 1);
            //记录系统默认勾选的服务
            $aOptionSvc = [
                'id'    => CustomLogic::CUSTOM_SERVICE_GUIDE_NEW,
                'count' => 1,
            ];
            $aOptionCusSvc[] = $aOptionSvc;
            $this->aSystemCustomFeatureService[$iPcId] = $aOptionCusSvc;
        }
        // 判断用户是否选择个性化服务的逻辑是一致的，因此减少计算，从$aPersonalized反解
        $aServiceInfoList = $this->_aPcId2ServiceId2ServiceInfo[$iPcId];
        if (!empty($aServiceInfoList)) {
            foreach ($aServiceInfoList as $aItem) {
                $iServiceId = $aItem['service_id'];
                if (!empty($aPersonalized[$iServiceId]) && !empty($aItem['fee_info'])) {
                    //用户勾选了个性化服务，且从hundun中有拿到价格信息
                    $aServiceInfo          = $aItem['fee_info'];
                    $aServiceInfo['count'] = 1;
                    $aCustomFeature[$iServiceId] = $aServiceInfo;
                }
            }
        }

        // price-api 参数
        $oProduct->oOrderInfo->aPersonalizedCustomOption = $aPersonalized; //个性化服务旧链路，plutus说下不掉，只能保持，也不做修改
        $oProduct->oOrderInfo->aCustomFeature            = $aCustomFeature; //个性化服务新链路，新链路为 sps出价格给hundun，hundun拿到价格透传给price-api，再入账
        // $aProduct['custom_service_info']：个性化服务请求账单老参数，个性化服务已全部切到 sps 不需要再传内容，但这个字段可能承载其他价格项，所以这里对此字段不做改动
        if (empty($oProduct->aCustomFeature)) {
            $oProduct->aCustomFeature = [];
        }
    }


    /**
     * @param  int $iProductCategory 品类pcid
     * @return array
     */
    public function getPcId2CustomFeature($iProductCategory) {
        return $this->_aPcId2CustomFeatureFromSsse[$iProductCategory];
    }

    /**
     * @param array $aService $aService
     * @return bool
     */
    private function _checkService($aService) {
        if (empty($aService['service_id']) || empty($aService['max']) || empty($aService['title'])) {
            return false;
        }

        return true;
    }

    /**
     * @param int $iServiceId service_id
     * @return bool
     */
    public function isHitService($iServiceId) {
        //判断是否命中增值服务
        return in_array($iServiceId,$this->_aAvailableServiceMap);
    }

    /**
     * @return bool
     */
    public function isS3eAvailable() {
        //判断s3e服务是否可用（有无超时）
        return $this->_bServiceAvailable;
    }

    /**
     * 构造夜间偏好女司机优先场景price-api入参
     * @param int           $iPcId          品类id
     * @param BizCommonInfo $oBizCommonInfo 公共数据
     * @return bool
     */
    private function _bIsNeedNightPreference($iPcId, $oBizCommonInfo) {
        if ($this->checkServiceAvailable($iPcId, CustomLogic::CUSTOM_SERVICE_FEMALE_DRIVER_FIRST)) {
            if (1 == $oBizCommonInfo->oCommonInfo->iIsFemaleDriverFirst) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param array $aServiceInfo $aServiceInfo
     * @return array
     */
    private function _formatServiceDataV3($aServiceInfo) {
        $aFormatService = [];
        if (empty($aServiceInfo)) {
            return [];
        }

        foreach ($aServiceInfo as $item) {
            $aCustomServiceItem = [];
            if (!$this->_checkService($item)) {
                continue;
            }

            $aCustomServiceItem['id']         = $item['service_id'];
            $aCustomServiceItem['service_id'] = $item['service_id'];
            $aCustomServiceItem['mutex_ids']  = $item['mutex_ids'] ?? [];
            $aCustomServiceItem['max']        = $item['max'];
            $aCustomServiceItem['title']      = $item['title'];
            $aCustomServiceItem['icon']       = $item['icon'] ?? '';
            $aCustomServiceItem['light_icon'] = $item['light_icon'] ?? '';
            $aCustomServiceItem['service_fee_info'] = $item['fee_info'] ?? ''; //个性化服务新链路保存的价格信息
            $aFormatService[] = $aCustomServiceItem;
        }

        if (empty($aFormatService)) {
            return $aFormatService;
        }

        // {"service_id" => service}
        $aServiceMap = array_combine(array_column($aFormatService, 'id'), $aFormatService);
        //补全互斥服务id
        foreach ($aServiceMap as $index => $item) {
            foreach ($item['mutex_ids'] as $id) {
                if (!isset($aServiceMap[$id])) {
                    continue;
                }

                if (!in_array($item['id'], $aServiceMap[$id]['mutex_ids'])) {
                    $aServiceMap[$id]['mutex_ids'][] = $item['id'];
                }
            }
        }

        return $aServiceMap;
    }

    /**
     * 格式化品类附属的CustomFeature信息
     * @return void
     */
    public function formatSsseCustomServiceV3() {
        if (empty($this->_aPcId2CustomFeatureFromSsse) || !is_array($this->_aPcId2CustomFeatureFromSsse)) {
            return;
        }

        foreach ($this->_aPcId2CustomFeatureFromSsse as $iPcId => $aItem) {
            $this->_aSceneData[$iPcId] = $this->_formatServiceDataV3($aItem);
        }
    }

    /**
     * 校验 需要上游返回的所有可用个性化服务中有这个
     * @param int $iPcId      category
     * @param int $iServiceId 服务id
     * @return bool
     */
    public function checkServiceAvailable($iPcId, $iServiceId) {
        $aServiceList = $this->_aPcId2CustomFeatureFromSsse[$iPcId] ?? [];

        foreach ($aServiceList as $aService) {
            if ($iServiceId == $aService->service_id) {
                //todo hundun全量后下线这个
                return true;
            }

            if ($iServiceId == $aService['service_id']) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取某个品类 支持的附加需求id列表
     * @param int $iPcId category
     * @return array
     */
    public function getSupportAddServiceIdListByPcId($iPcId) {
        return $this->_aPcId2AddServiceIdListFromSsse[$iPcId] ?? [];
    }

    /**
     * 提供接口获取某个品类 构造后的 scene_data
     * @param int $iPcId category
     * @return array
     */
    public function getSceneDataByPcId($iPcId) {
        return $this->_aSceneData[$iPcId] ?? [];
    }

    /**
     * 渲染端上的附加需求列表
     * @param BizCommonInfo $oBizCommonInfo 公共
     * @return AdditionalServiceData|null
     */
    public function buildAdditionalService(BizCommonInfo $oBizCommonInfo) {
        $oAreaInfo = $oBizCommonInfo->getAreaInfo();
        if (empty($oAreaInfo->iArea)) {
            return null;
        }

        if ('zh-CN' != $oBizCommonInfo->getAppLanguage()) {
            return null;
        }

        $aTextCfg = NuwaConfig::text('estimate_form_v3', 'additional_service');
        if (empty($aTextCfg)) {
            return null;
        }

        $aChooseAdditionalServiceIdList = []; // 用户上传的附加需求id List
        if (sizeof($this->_aChooseAdditionalService) > 0) {
            foreach ($this->_aChooseAdditionalService as $aService) {
                $aChooseAdditionalServiceIdList[] = $aService['id'];
            }
        }

        $aServiceList = [];
        foreach ($this->_aAddServiceIdListFromSsse as $iServiceId) {
            $aService = new AdditionalService();
            $aService->setId($iServiceId);
            $aService->setText($aTextCfg['service_config'][$iServiceId]['text']);
            $aService->setIcon($aTextCfg['service_config'][$iServiceId]['icon']);
            $aService->setDetail($aTextCfg['service_config'][$iServiceId]['detail']);

            if (!empty($aChooseAdditionalServiceIdList) && in_array($iServiceId, $aChooseAdditionalServiceIdList)) {
                // 是否勾选 0未勾选 1:已勾选
                $aService->setSelect(1);
            } else {
                $aService->setSelect(0);
            }

            $aServiceList[] = $aService;
        }

        if (empty($aServiceList)) {
            return null;
        }

        $oData = new AdditionalServiceData();
        $oData->setServiceList($aServiceList);
        $oData->setPageTitle($aTextCfg['page_title']);
        $oData->setTitle($aTextCfg['title']);
        $oData->setTipsUrl($aTextCfg['tips_url']);
        $oData->setSubTitleList($aTextCfg['sub_title_list']);

        $oApolloResult = \Xiaoju\Apollo\Apollo::getInstance()->featureToggle(
            'gs_additional_service_toast',
            [
                'key'           => $oBizCommonInfo->oPassengerInfo->iPid,
                'phone'         => $oBizCommonInfo->oPassengerInfo->sPhone,
                'app_version'   => $oBizCommonInfo->getAppVersion(),
                'access_key_id' => $oBizCommonInfo->getAccessKeyID(),
                'lang'          => $oBizCommonInfo->getAppLanguage(),
                'city'          => $oAreaInfo->iArea,
            ]
        );
        if ($oApolloResult->allow() && !empty($oApolloResult->getParameter('guide_text', null))) {
            $oData->setGuideText($oApolloResult->getParameter('guide_text', ''));
            $oData->setGuideTimes($oApolloResult->getParameter('guide_times', 0));
            $oData->setVersion($oApolloResult->getParameter('version', 0));
        }

        return $oData;
    }

    /**
     * 获取附加需求配置  (哪些服务id属于 附加需求)
     * @return array
     */
    private function _initConfig() {

        $this->_aProductMap = Config::getBizConfig('config_product_category_map', 'product_category');

        // 获取配置 - 哪些服务是预估级别的附加需求
        $oConfResult = \Xiaoju\Apollo\Apollo::getInstance()->getConfigResult(
            self::APOLLO_CONF_SERVICE_LIST,
            self::APOLLO_CONF_SERVICE_FILE
        );

        // [{id:106,is_filter:true}]   is_filter=true需要过滤
        list($bOk, $aRes) = $oConfResult->getConfig('service_list');
        if (!$bOk) {
            return [];
        }

        $aServiceList = [];
        foreach ($aRes as $aConfig) {
            $aServiceList[$aConfig['id']] = $aConfig['is_filter'];
        }

        $this->_aNeedFilterServiceIdConfig = $aServiceList;
    }

    /**
     * 构造出租车峰期加价场景price-api入参
     * 背景：出租车峰期加价
     * @param Product       $oProduct       基础品类
     * @param BizCommonInfo $oBizCommonInfo 公共数据
     * @return bool
     */
    private function _bIsNeedBuildTaxiPeakFee($oProduct, $oBizCommonInfo) {
        if (OrderSystem::PRODUCT_ID_UNITAXI != $oProduct->oOrderInfo->iProductId) {
            return false;
        }

        $oTaxiPeakFeeClient = TaxiPeakFee::getPoolInstance([$oProduct, $oBizCommonInfo]);

        // 如果未命中出租车峰期加价逻辑则直接返回
        if (!$oTaxiPeakFeeClient->getStatus()) {
            return false;
        }

        if ($oTaxiPeakFeeClient->getIsBookingOrder() && $oTaxiPeakFeeClient->getIsInterActive()) {
            // 预约单逻辑强制无出口
            // 若当前配置为有出口，则走模拟乘客勾选
            return true;
        } else {
            // 实时单逻辑

            // 如果首次预估，且有出口，则模拟乘客勾选，构造默认勾选峰期加价出口
            if ($oTaxiPeakFeeClient->getIsFirstEstimate()
                && $oTaxiPeakFeeClient->getIsInterActive()
                && $oTaxiPeakFeeClient->getIsUserSelect()
            ) {
                // 如果有揽客宝下挂，不模拟乘客勾选
                if ($this->hasLankeBaoPrefer($oProduct, $oBizCommonInfo)) {
                    return false;
                }

                return true;
            }

            // 若二次预估，有出口，且乘客仍在勾
            if (!$oTaxiPeakFeeClient->getIsFirstEstimate()
                && ($oTaxiPeakFeeClient->getIsInterActive()
                && $oTaxiPeakFeeClient->getIsUserSelect())
            ) {
                return true;
            }
        }

        //如果是新链路，且信息费无出口，说明需要默认入账
        $aParam = [
            'pid'          => $oBizCommonInfo->getPassengerID(),
            'phone'        => $oBizCommonInfo->getPassengerPhone(),
            'city'         => $oBizCommonInfo->getFromCity(),
            'combo_type'   => $oProduct->oOrderInfo->iComboType,
            'carpool_type' => $oProduct->oOrderInfo->iCarpoolType,
            'county_id'    => $oBizCommonInfo->getAreaInfo()->iFromCounty,
        ];
        if (!$oTaxiPeakFeeClient->getIsInterActive() && TaxiPeakFee::checkSwitchIntel($aParam)) {
            return true;
        }

        return false;
    }

    /**
     * @param Request $oRequest
     * @return void
     */
    protected function buildUserChooseAdditionalService($oRequest) {
        $this->_aChooseAdditionalService = json_decode($oRequest->getAdditionalService(), true) ?: [];
        foreach ($this->_aChooseAdditionalService as $aService) {
            if (!$this->_aNeedFilterServiceIdConfig[$aService['id']]) {
                // 该服务不需要过滤品类
                continue;
            }

            $this->_aNeedFilterServiceId[] = $aService['id'];
        }
    }

    /**
     * @param array $aBaseProductList
     * @param BizCommonInfo $oBizCommonInfo 公共数据
     * @return void
     */
    public function addSpsServiceFeeInfo($aBaseProductList, $oBizCommonInfo) {
        $oCommonInfo    = $oBizCommonInfo->oCommonInfo;
        $oPassengerInfo = $oBizCommonInfo->getPassengerInfo();
        $oAreaInfo      = $oBizCommonInfo->oAreaInfo;

        $aSpsAddServiceReqs = [];
        foreach ($aBaseProductList as $aProduct) {
            $aCommonRequestParam = $this->getSpsCommonParams($aProduct,$oPassengerInfo->aPassengerInfo,$oAreaInfo,$oCommonInfo);
            $aSpsAddServiceReqs  = $this->buildSpsAddServiceReqs($aProduct,$aCommonRequestParam,$aSpsAddServiceReqs);
        }

        SpsAddServicePriceLogic::getInstance()->initAddServiceFeeInfo($aSpsAddServiceReqs);
    }

    /**
     * 构建请求sps需要的公共参数
     * @param array $aProduct               aProduct
     * @param array $aPassengerInfo         aPassengerInfo
     * @param AreaInfo $oAreaInfo           oAreaInfo
     * @param CommonInfo $oCommonInfo       oCommonInfo
     * @return array
     */
    public static function getSpsCommonParams($aProduct, $aPassengerInfo, $oAreaInfo, $oCommonInfo) {
        $aParam['passenger_id'] = $aPassengerInfo['pid'];
        $aParam['city']         = $oAreaInfo->iArea;
        $aParam['county_id']    = $oAreaInfo->iFromCounty;
        $aParam['product_id']   = $aProduct['product_id'];
        $aParam['car_level']    = $aProduct['require_level'];
        $aParam['combo_type']   = $aProduct['combo_type'];
        $aParam['product_category'] = $aProduct['product_category'];
        $aParam['departure_time']   = $oCommonInfo->iDepartureTime;
        $aParam['access_key_id']    = $oCommonInfo->iAccessKeyID;
        $aParam['app_version']      = $oCommonInfo->sAppVersion;
        $aParam['extra_info'] = self::buildExtraInfo($oCommonInfo);

        return $aParam;
    }


    /**
     * @desc 构建请求extraInfo
     * @param CommonInfo $oCommonInfo oCommonInfo
     * @return array
     */
    public static function buildExtraInfo($oCommonInfo) {
        $aExtraInfo = [];
        if (ProductPageType::PageTypePageTypeLanKeBao == $oCommonInfo->iPageType) {
            $aExtraInfo['offline_lanke'] = '1';
        }

        return $aExtraInfo;
    }

    /**
     * 根据ssse返回补全入参
     * @param array $aProduct            aProduct
     * @param array $aCommonRequestParam 公共参数
     * @param array $aSpsAddServiceReqs  reqs
     * @return array
     */
    public function buildSpsAddServiceReqs($aProduct, $aCommonRequestParam, $aSpsAddServiceReqs) {
        $aSpsAddServiceReq   = [];
        $aPcId2CustomFeature = $this->_aPcId2CustomFeatureFromSsse;
        $iProCategory        = $aProduct['product_category'];
        if (!empty($aPcId2CustomFeature) && !empty($aPcId2CustomFeature[$iProCategory])) {
            foreach ($aPcId2CustomFeature[$iProCategory] as $aService) {
                $aSpsAddServiceReq['service_list'][] = [
                    'service_id' => $aService['service_id'],
                    'count'      => $aService['selected_count'] ?: 1,
                ];
            }

            $aSpsAddServiceReq   += $aCommonRequestParam;
            $aSpsAddServiceReqs[] = $aSpsAddServiceReq;
        }

        return $aSpsAddServiceReqs;
    }

    /**
     * 是否有揽客宝下挂
     * @param Product       $aProduct       基础品类
     * @param BizCommonInfo $oBizCommonInfo 公共数据
     * @return bool
     */
    public function hasLankeBaoPrefer($aProduct, $oBizCommonInfo) {
        if (ProductPageType::PageTypePageTypeLanKeBao == $oBizCommonInfo->oCommonInfo->iPageType) {
            return true;
        }

        if (!$this->isLankebaoPickUpShow($aProduct, $oBizCommonInfo)) {
            return false;
        }

        $this->formatSsseCustomServiceV3();
        $iPcId = $aProduct->oOrderInfo->iProductCategory;
        if (empty($this->_aSceneData[$iPcId])
            || empty($this->_aSceneData[$iPcId][TaxiLanKeBao::LANKEBAO_SERVICE_ID])
        ) {
            return false;
        }

        $iSubGroupId = self::$aPcIdToSubGroupId[$iPcId];
        if (LayoutBuilder::SUB_GROUP_ID_TAXI_PRICING == $iSubGroupId
            || LayoutBuilder::SUB_GROUP_ID_UNITAXI == $iSubGroupId
        ) {
            return false;
        }

        return true;
    }

    /**
     * 是否展示揽客宝下挂
     * @param Product       $aProduct       基础品类
     * @param BizCommonInfo $oBizCommonInfo 公共数据
     * @return bool
     */
    public function isLankeBaoPickUpShow($aProduct, $oBizCommonInfo) {
        $oApollo = Apollo::getInstance();
        $aParam  = $oBizCommonInfo->getApolloParams($oBizCommonInfo->getPassengerID());
        $aParam['product_category'] = $aProduct->oOrderInfo->iProductCategory;
        $bIsAllow = $oApollo->featureToggle(
            'lankebao_version_controller',
            $aParam
        )->allow();
        if (!$bIsAllow) {
            return false;
        }

        $bIsAllow = $oApollo->featureToggle(
            'lankebao_pick_up_show',
            $aParam
        )->allow();
        if (!$bIsAllow) {
            return false;
        }

        return true;
    }

    /**
     * 默认选择机场助理服务的灰度开关判断
     * @param array $aApolloParams []
     * @return bool
     */
    public function isNeedDefaultCheckGuideNewCustomService($aApolloParams): bool {
        return Apollo::getInstance()->featureToggle(
            'gs_cs_guide_new_default_check_switch',$aApolloParams
        )->allow();
    }
    /**
     * 判断是否是ufs中保存了用户勾选增值服务状态的版本
     * @param array $aApolloParams []
     * @return bool
     */
    public function isSaveToUfsCustomFeatureVersion($aApolloParams): bool {
        // NA 要大于等于7.0.10
        if (in_array($aApolloParams['access_key_id'], [Common::DIDI_IOS_PASSENGER_APP, Common::DIDI_ANDROID_PASSENGER_APP])) {
            if (UtilHelper::compareAppVersion($aApolloParams['app_version'], "7.0.10") < 0) {
                return false;
            } else {
                return true;
            }
        }
        // 小程序版本号大于等于6.10.50
        if (in_array($aApolloParams['access_key_id'], [Common::DIDI_WECHAT_MINI_PROGRAM, Common::DIDI_ALIPAY_MINI_PROGRAM])) {
            if (UtilHelper::compareAppVersion($aApolloParams['app_version'], "6.10.50") < 0) {
                return false;
            } else {
                return true;
            }
        }
        return false;
    }
    /**
     * 默认勾选机场助理的判断逻辑
     * @param BizCommonInfo $oBizCommonInfo oBizCommonInfo
     * @param int           $iPcId          iPcId
     * @return bool
     */
    private function _defaultCheckGuideNew(BizCommonInfo $oBizCommonInfo, $iPcId): bool {
        //判断机场费用是否为0
        $aServiceInfoList = $this->_aPcId2ServiceId2ServiceInfo[$iPcId];
        if (empty($aServiceInfoList)) {
            return false;
        }
        $bZeroPrice = false;
        foreach ($aServiceInfoList as $aItem) {
            $iServiceId = $aItem['service_id'];
            if ($iServiceId == CustomLogic::CUSTOM_SERVICE_GUIDE_NEW) {
                if (!empty($aItem['fee_info']) && $aItem['fee_info']['discount_price'] == 0) {
                    $bZeroPrice = true;
                }
                break;
            }
        }
        if (!$bZeroPrice) {
            return false;
        }

        //根据版本走不通的判断逻辑
        if ($this->isSaveToUfsCustomFeatureVersion($oBizCommonInfo->getApolloParams($oBizCommonInfo->getPassengerID()))) {
            return $this->_defaultCheckGuideNewByUfs($oBizCommonInfo);
        } else {
            return $this->_defaultCheckGuideNewOld($oBizCommonInfo);
        }
    }
    /**
     * ufs未保存custom_feature的版本，默认勾选机场助理的判断逻辑
     * @param BizCommonInfo $oBizCommonInfo oBizCommonInfo
     * @return bool
     */
    private function _defaultCheckGuideNewOld(BizCommonInfo $oBizCommonInfo): bool {
        //条件：未选择豪华车的
        $bNotSelected = true;
        if (!empty($oBizCommonInfo->getCommonInfo()->aMultiRequireProducts)) {
            foreach ($oBizCommonInfo->getCommonInfo()->aMultiRequireProducts as $aProduct) {
                if (($aProduct['product_category'] == ProductCategory::PRODUCT_CATEGORY_LUXURY_ANY || $aProduct['product_category'] == ProductCategory::PRODUCT_CATEGORY_LUXURY_ANY_SIX_SEATS) && (isset($aProduct['is_selected']) && $aProduct['is_selected'] == 1)) {
                    $bNotSelected = false;
                    break;
                }
            }
        }
        if ($oBizCommonInfo->isFirstEstimate() || $bNotSelected) {
            return true;
        }
        return false;
    }
    /**
     * ufs保存custom_feature的版本，默认勾选机场助理的判断逻辑
     * @param BizCommonInfo $oBizCommonInfo oBizCommonInfo
     * @return bool
     */
    private function _defaultCheckGuideNewByUfs(BizCommonInfo $oBizCommonInfo): bool {
        //首次进入直接默认勾选
        if ($oBizCommonInfo->isFirstEstimate()) {
            return true;
        }
        //ufs中记录用户主动取消勾选过则返回false，否则返回true
        if ($this->isUserActiveCancel(CustomLogic::CUSTOM_SERVICE_GUIDE_NEW)) {
            return false;
        } else {
            return true;
        }
    }
    /**
     * 用户是否主动取消过增值服务
     * @param int $iCustomServiceId 增值服务id
     * @return bool
     * */
    public function isUserActiveCancel($iCustomServiceId): bool {
        $aUserCustomFeatureData = $this->aUfsCustomFeature;
        if (empty($aUserCustomFeatureData)) {
            return false;
        }
        foreach ($aUserCustomFeatureData as $aCustomFeature) {
            //"custom_feature": [{"id":102,"count":1}]，id为增值服务id，count：0-取消勾选，1-勾选
            if (isset($aCustomFeature['id']) && $iCustomServiceId == $aCustomFeature['id']) {
                if (isset($aCustomFeature['count']) && $aCustomFeature['count'] == 0) {
                    return true;
                } else {
                    return false;
                }
            }
        }
        return false;
    }
    /**
     * 获取UFS中用户选择的增值服务和prefer数据
     * @param int $iPid 乘客id
     * @return array
     * */
    public function initCustomFeatureAndPreferData($iPid) {
        $oUfsClient = new UfsClient();
        $aUfsRet    = $oUfsClient->getFeature(
            //为减少ufs调用次数取多个key
            ['tailor.custom_feature','luxury.perfer_info'],
            ['passenger_id' => $iPid,],
            'passenger'
        );
        //从UFS取不到，返回默认信息
        if (!isset($aUfsRet['errno']) || 0 != $aUfsRet['errno']) {
            return [];
        }
        $aCustomFeature = $aUfsRet['result']['tailor.custom_feature'];
        $this->aUfsPreferOption = json_decode($aUfsRet['result']['luxury.perfer_info'], true);
        $this->aUfsCustomFeature = json_decode($aCustomFeature, true);
    }
}
