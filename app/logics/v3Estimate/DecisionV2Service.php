<?php

namespace PreSale\Logics\v3Estimate;

use <PERSON>iz<PERSON><PERSON>\Log as NuwaLog;
use BizL<PERSON>\Utils\ProductCategory;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\v3Estimate\FormAB\SurpriseDiscountABParam;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TaxiPricingBoxLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutBuilder;
use PreSale\Models\featurePlugin\Common as FeatureCommon;

/**
 * class DecisionV2Service
 * @package PreSale\Logics\v3Estimate
 */
class DecisionV2Service
{
    const MANUFACTURER_PCID_KEY = 'manufacturer_in';
    private static $_oInstance;

    private static $_bInit;

    const DECISION_TYPE_DEFAULT = 1;

    const CAR_MANUFACTURER_IN_BOX = 'manufacturer_in';

    /**
     * @var BizCommonInfo
     */
    private $_oBizCommonInfo;

    /**
     * @var array 根据PCID 查询从属的sub_group_id
     */
    private $_newPcIDToSubGroupID = [];


    /**
     * @var array  map[sub_group_id] product_category[]
     */
    private $_newSubGroupIDToProducts = [];

    /**
     * @var BizProduct[]
     */
    private $_aBizProductList;

    /**
     * @var BizProduct[]
     */
    private static $_aBizProducts;

    /**
     * @return void
     */
    private function __construct() {}

    /**
     * @return DecisionV2Service
     */
    public static function getInstance() {
        if (self::$_oInstance == null) {
            self::$_oInstance = new self();
        }

        return self::$_oInstance;
    }


    /**
     * @param array $aFinalGroupList 组信息
     * @param array $aParams         参数
     * @return array
     */
    protected static function _filterGroupList($aFinalGroupList, $aParams) {
        if (!empty($aFinalGroupList[LayoutBuilder::SUB_GROUP_ID_TAXI_PRICING])) {
            if (!TaxiPricingBoxLayout::isPricingBox($aParams)) {
                unset($aFinalGroupList[LayoutBuilder::SUB_GROUP_ID_TAXI_PRICING]);
            }
        }

        return $aFinalGroupList;
    }


    /**
     * @param array  $aFinalGroupList 组信息
     * @param array  $aProductList 品类list
     * @return array
     */
    protected static function _fillingProductListByLevelType($aFinalGroupList, $aProductList) {
        if (empty($aProductList)) {
            return $aFinalGroupList;
        }

        foreach ($aFinalGroupList as $aConf) {
            //若盒子和level_type绑定，则遍历product_map,往product_list append
            if (empty($aConf['level_type'])) {
                continue;
            }

            foreach ($aProductList as $aProduct) {
                if ($aConf['level_type'] == $aProduct['level_type']) {
                    $aConf['product_list'][] = $aProduct['product_category'];
                }
            }
        }

        return $aFinalGroupList;
    }

    /**
     * @param array $aFinalGroupList 盒子配置
     * @param array $aParams         实验参数
     * @return mixed
     */
    protected static function _filterProductList($aFinalGroupList, $aParams) {
        if (empty($aFinalGroupList) || empty($aFinalGroupList[LayoutBuilder::SUB_GROUP_ID_UNITAXI])) {
            return $aFinalGroupList;
        }

        $aUniTaxiConf    = &$aFinalGroupList[LayoutBuilder::SUB_GROUP_ID_UNITAXI]; // 使用引用
        $aNewProductList = [];

        foreach ($aUniTaxiConf['product_list'] as $sPcID) {
            if (self::_allowProductIntoBox($aParams, (int)$sPcID, LayoutBuilder::SUB_GROUP_ID_UNITAXI)) {
                $aNewProductList[] = $sPcID;
            }
        }

        $aUniTaxiConf['product_list'] = $aNewProductList;

        return $aFinalGroupList;
    }

    /**
     * @param BizCommonInfo $oBizCommonInfo  $oBizCommonInfo
     * @param BizProduct[]  $aBizProductList $aBizProductList
     * @return void
     */
    public function init(BizCommonInfo $oBizCommonInfo, $aBizProductList) {
        if (!self::$_bInit) {
            $this->_oBizCommonInfo  = $oBizCommonInfo;
            $this->_aBizProductList = $aBizProductList;
            self::$_aBizProducts    = $aBizProductList;
            // 处理聚合逻辑
            $aParams = [
                'key'           => $this->_oBizCommonInfo->getPassengerInfo()->iUid,
                'pid'           => $this->_oBizCommonInfo->getPassengerInfo()->iPid,
                'phone'         => $this->_oBizCommonInfo->getPassengerInfo()->sPhone,
                'app_version'   => $this->_oBizCommonInfo->getAppVersion(),
                'access_key_id' => $this->_oBizCommonInfo->getAccessKeyID(),
                'lang'          => $this->_oBizCommonInfo->getAppLanguage(),
                'city'          => $this->_oBizCommonInfo->getAreaInfo()->iArea,
                'county_id'     => $this->_oBizCommonInfo->getAreaInfo()->iFromCounty,
                'order_type'    => $this->_oBizCommonInfo->oCommonInfo->iOrderType,
                'tab_id'        => $this->_oBizCommonInfo->oCommonInfo->sTabId,
                'page_type'     => $this->_oBizCommonInfo->oCommonInfo->iPageType,
            ];

            $aGroupList = [
                LayoutBuilder::SUB_GROUP_ID_SHORT_DISTANCE,
                LayoutBuilder::SUB_GROUP_ID_UNITAXI,
                LayoutBuilder::SUB_GROUP_ID_TAXI_PRICING,
                LayoutBuilder::SUB_GROUP_ID_FAR_MUST_CHEAPER,
                LayoutBuilder::SUB_GROUP_ID_CHAO_ZHI_DA,
                LayoutBuilder::SUB_GROUP_ID_HK_THIRD_BUSINESS_NORMAL,
                LayoutBuilder::SUB_GROUP_ID_HK_THIRD_BUSINESS_COMFORT,
            ];
            if ($this->isHitXDiscountBox($oBizCommonInfo->oCommonInfo->sTabId, $aParams)) {
                $aGroupList[] = LayoutBuilder::SUB_GROUP_ID_X_DISCOUNT;
            }

            $oSurpriseDiscountBoxAB = SurpriseDiscountABParam::getInstance($oBizCommonInfo);
            if ($oSurpriseDiscountBoxAB->isHitSurpriseDiscountBox()) {
                $aGroupList[] = LayoutBuilder::SUB_GROUP_ID_SURPRISE_DISCOUNT;
            }

            //统一入参格式
            $aProductList = [];
            foreach ($aBizProductList as $oBizProduct) {
                $aProductList[] = $oBizProduct->oProduct->oOrderInfo->toArray();
            }

            list($this->_newPcIDToSubGroupID, $this->_newSubGroupIDToProducts) = self::getAggConfByGroupId(
                $aGroupList,
                $aParams,
                $aProductList
            );
        }

        // 获取排序逻辑
//        $aSceneConf = ApolloHelper::getConfigContent("car_sort", "anycar_sort_v3");
//        if (!empty($aSceneConf['products_sort']) && is_array($aSceneConf['products_sort'])){
//            $this->_aSingleProductsSort = array_column($aSceneConf['products_sort'], 'product_category');
//        }
        self::$_bInit = true;
    }

    /**
     * @param string $sTabId      tab_id
     * @param array $aApolloParam param
     * @return bool
     */
    public function isHitXDiscountBox($sTabId, $aApolloParam) {
        if (FeaturePlugin::getInstance()->checkFeatureBanStatus(FeatureCommon::FEATURE_X_DISCOUNT_BOX)) {
            // X折特价车盒子只在三方聚合表单
            return false;
        }

        return true;
    }

    /**
     * @param int $iProductCategory $iProductCategory
     * @return int|mixed
     */
    public function getSubGroupIDByPcID($iProductCategory) {
        return $this->_newPcIDToSubGroupID[$iProductCategory] ?? 0;
    }

    /**
     * @param int $iGroupID $iGroupID
     * @return array|mixed
     */
    public function getProductsListByGroupID($iGroupID) {
        return $this->_newSubGroupIDToProducts[$iGroupID] ?? [];
    }

    public function unsetSubGroupIDByPcID($iPcId, $iGroupID) {
        if (empty($this->_newSubGroupIDToProducts) || !is_array($this->_newSubGroupIDToProducts)) {
            return;
        }

        if (in_array($iPcId, $this->_newSubGroupIDToProducts[$iGroupID])) {//特定品类出盒子
            $this->_newSubGroupIDToProducts[$iGroupID] = array_diff($this->_newSubGroupIDToProducts[$iGroupID],[$iPcId]);
            $this->_newPcIDToSubGroupID[$iPcId]        = 0;
        }
    }

    /**
     * @param array $aGroupList   id list
     * @param array $aParams      阿波罗参数
     * @param array $aProductList 品类list 对于（远必省、超值达）这类品类与level_type 强绑定的盒子，该字段为必传参数
     * @return array
     */
    public static function getAggConfByGroupId($aGroupList, $aParams, $aProductList = []) {
        // 读三方盒子和出租车盒子
        $aPcIDToSubGroupID     = [];
        $aSubGroupIDToProducts = [];

        $aFinalGroupList = [];
        foreach ($aGroupList as $iGroupID) {
            // 揽客宝线下报单页(page_type=45)，不渲染出租车相关的盒子
            if (isset($aParams['page_type']) && 45 == $aParams['page_type']) {
                if (LayoutBuilder::SUB_GROUP_ID_TAXI_PRICING == $iGroupID
                || LayoutBuilder::SUB_GROUP_ID_UNITAXI == $iGroupID) {
                    continue;
                }
            }

            $oConfig = Apollo::getInstance()->getConfigsByNamespaceAndConditions('car_aggregation', ['sub_group_id' => $iGroupID]);
            list($bOK, $aAllConfig) = $oConfig->getAllConfigData();
            if (!$bOK) {
                continue;
            }

            foreach ($aAllConfig as $aValue) {
                if (!empty($aValue['city_toggle'])
                    && !(new Apollo())->featureToggle($aValue['city_toggle'], $aParams)->allow()
                ) {
                    continue;
                }

                $aFinalGroupList[$aValue['sub_group_id']] = $aValue;

                break;
            }
        }

        //填充绑定level_type的盒子
        $aFinalGroupList = self::_fillingProductListByLevelType($aFinalGroupList, $aProductList);

        // 车企出盒子（商家扶持）
        $aFinalGroupList = self::_outsideCarManufacturer($aFinalGroupList, $aParams);

        // 盒子内品类灰度控制
        $aFinalGroupList = self::_filterProductList($aFinalGroupList, $aParams);

        // pk 优先级
        $aFinalGroupList = self::_filterGroupList($aFinalGroupList, $aParams);

        foreach ($aFinalGroupList as $iGroupID => $aConf) {
            $aProductListConfig = $aConf['product_list'];

            $tConfig = [];
            foreach ($aProductListConfig as $iPCIds) {
                $tConfig[] = $iPCIds;
                $aPcIDToSubGroupID[$iPCIds] = $iGroupID;
            }

            $aSubGroupIDToProducts[$iGroupID] = $tConfig;
        }

        return [$aPcIDToSubGroupID, $aSubGroupIDToProducts];
    }

    /**
     * 过滤品类
     * @return void
     */
    public function filterAggregationPcID() {
        if (empty($this->_newSubGroupIDToProducts) || !is_array($this->_newSubGroupIDToProducts)) {
            return;
        }

        $aSubGroupInfoMap = AthenaRecommend::getInstance()->getSubGroupInfoMap();
        $aGroupList       = [
            LayoutBuilder::SUB_GROUP_ID_SHORT_DISTANCE,
            LayoutBuilder::SUB_GROUP_ID_DISCOUNT_ALLIANCE,
        ];

        foreach ($aGroupList as $aGroupId) {
            $splitCategory = $aSubGroupInfoMap[$aGroupId]['split_category'];
            if (empty($splitCategory)) {
                continue;
            }

            foreach ($splitCategory as $iPcId) {
                if (empty($this->_newSubGroupIDToProducts) || !is_array($this->_newSubGroupIDToProducts)) {
                    break;
                }

                if (in_array($iPcId, $this->_newSubGroupIDToProducts[$aGroupId])) {//特定品类出盒子
                    $this->_newSubGroupIDToProducts[$aGroupId] = array_diff($this->_newSubGroupIDToProducts[$aGroupId],[$iPcId]);
                    $this->_newPcIDToSubGroupID[$iPcId]        = 0;
                }
            }

            if (is_array($this->_newSubGroupIDToProducts[$aGroupId]) && 1 == sizeof($this->_newSubGroupIDToProducts[$aGroupId])) {
                $this->_newSubGroupIDToProducts[$aGroupId] = [];
                $this->_newPcIDToSubGroupID[$this->_newSubGroupIDToProducts[$aGroupId][0]] = 0;
            }
        }
    }

    /**
     * 根据Athena返回的数据对盒子进行移动
     * @return void
     */
    public function moveSubGroupByAthena() {
        $oAthenaInfo      = AthenaRecommend::getInstance();
        $oRecPcToSubGroup = $oAthenaInfo->getRecPcToSubGroup();
        $oNeedMovePcIDs   = array_keys($oRecPcToSubGroup);

        foreach ($oNeedMovePcIDs as $iPcId) {
            $newSubGroupId = $oRecPcToSubGroup[$iPcId];
            $oldSubGroupId = $this->_newPcIDToSubGroupID[$iPcId];
            if (!empty($oldSubGroupId) && $oldSubGroupId != $newSubGroupId && LayoutBuilder::SUB_GROUP_ID_SINGLE_CAR != $oldSubGroupId) {
                // 根据athena结果，把旧盒子的该品类剔除
                $this->_newSubGroupIDToProducts[$oldSubGroupId] = array_diff(
                    $this->_newSubGroupIDToProducts[$oldSubGroupId],
                    [$iPcId]
                );
            }

            if (empty($newSubGroupId) || $oldSubGroupId == $newSubGroupId) {
                continue;
            }

             // 加入到新盒子
            if (!isset($this->_newSubGroupIDToProducts[$newSubGroupId])) {
                $this->_newSubGroupIDToProducts[$newSubGroupId] = [];
            }

            $this->_newSubGroupIDToProducts[$newSubGroupId][] = $iPcId;
            $this->_newPcIDToSubGroupID[$iPcId] = $newSubGroupId;
        }
    }

    /**
     * 车企出盒子
     *
     * @param array $aFinalGroupList 组信息
     * @param array $aApolloParam    阿波罗参数
     * @return array 过滤后的组信息
     */
    private static function _outsideCarManufacturer($aFinalGroupList, $aApolloParam) {
        $aApolloParam['key'] = $aApolloParam['pid'];
        // 灰度控制 城市-扶持品类 关系
        $oToggle = Apollo::getInstance()->featureToggle('estimate_support_car_manufacturer', $aApolloParam);
        if (!$oToggle->allow()) {
            return $aFinalGroupList;
        }

        $sSupportedPcIDs = $oToggle->getParameter('supported_pcid', '');
        $aSupportedPcID  = [];
        if (strlen($sSupportedPcIDs) > 0) {
            $aSupportedPcID = explode(',', $sSupportedPcIDs);
        }

        $sUnsupportedPcIDs = $oToggle->getParameter('unsupported_pcid', '');
        $aUnsupportedPcID  = [];
        if (strlen($sUnsupportedPcIDs) > 0) {
            $aUnsupportedPcID = explode(',', $sUnsupportedPcIDs);
        }

        $sExpName = $oToggle->getParameter('exp_name', '');
        $oExp     = Apollo::getInstance()->featureToggle($sExpName, $aApolloParam);
        if (!$oExp->allow()) {
            return $aFinalGroupList;
        }

        // 是否有下一层实验
        $sNextExpName = $oExp->getParameter('next_exp_name', '');
        if ('' != $sNextExpName) {
            $oNextExp = Apollo::getInstance()->featureToggle($sNextExpName, $aApolloParam);
            if (!$oNextExp->allow() || 1 != $oNextExp->getParameter('is_support_outside', 0)) {
                return $aFinalGroupList;
            }
        } else {
            if (1 != $oExp->getParameter('is_support_outside', 0)) {
                return $aFinalGroupList;
            }
        }

        // 扶持车型出盒子，作为独立品类
        foreach ($aSupportedPcID as $sSupportedPcID) {
            if (in_array($sSupportedPcID, $aFinalGroupList[LayoutBuilder::SUB_GROUP_ID_SHORT_DISTANCE]['product_list'] ?? [])) {
                $aFinalGroupList[LayoutBuilder::SUB_GROUP_ID_SHORT_DISTANCE]['product_list'] = array_diff(
                    $aFinalGroupList[LayoutBuilder::SUB_GROUP_ID_SHORT_DISTANCE]['product_list'],
                    [$sSupportedPcID]
                );
            }
        }

        // 非扶持车型进盒子
        foreach ($aUnsupportedPcID as $sUnsupportedPcID) {
            if (!in_array($sUnsupportedPcID, $aFinalGroupList[LayoutBuilder::SUB_GROUP_ID_SHORT_DISTANCE]['product_list'] ?? [])) {
                $aFinalGroupList[LayoutBuilder::SUB_GROUP_ID_SHORT_DISTANCE]['product_list'][] = $sUnsupportedPcID;
            }
        }

        return $aFinalGroupList;
    }
    /**
     * 获取PCID从属的sub_group_id
     *
     * @return array
     */
    public function getNewPcIDToSubGroupID() {
        return $this->_newPcIDToSubGroupID ?? [];
    }

    /**
     * 品类是否命中进入盒子的灰度开关
     * @param array $aParams  参数
     * @param int   $iPcID    品类id
     * @param int   $iGroupID 盒子id
     * @return bool
     */
    private static function _allowProductIntoBox($aParams, $iPcID, $iGroupID) {
        $aParams['pc_id']    = $iPcID;
        $aParams['group_id'] = $iGroupID;
        $oToggle             = Apollo::getInstance()->featureToggle('gs_car_aggregation_product_filter', $aParams);
        return $oToggle->allow();
    }
}
