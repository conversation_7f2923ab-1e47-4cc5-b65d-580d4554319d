<?php

namespace PreSale\Logics\v3Estimate\FormAB;

use BizLib\Constants\Common;
use BizLib\ErrCode\Code;
use Nuwa\ApolloSDK\Apollo;
use PreSale\exception\RuntimeException;
use <PERSON><PERSON>\Apollo\ApolloConstant;

class SurpriseDiscountABParam {

    const VERSION_CONTROL_MAP = array(
        Common::DIDI_IOS_PASSENGER_APP => "7.1.4",
        Common::DIDI_ANDROID_PASSENGER_APP=> "7.1.4",
        Common::DIDI_WECHAT_MINI_PROGRAM => "7.0.30",
        Common::DIDI_ALIPAY_MINI_PROGRAM => "7.0.30",
    );

    // 单例实例
    private static $_oInstance;


    // 是否命中实验
    private $bIsHit = false;

    public static function getInstance($oBizCommonInfo) {
        if (empty(self::$_oInstance)) {
            self::$_oInstance = new self($oBizCommonInfo);
        }
        return self::$_oInstance;
    }

    public function __construct($oBizCommonInfo) {
        if (!empty($oBizCommonInfo)) {
            $aApolloParams = [
                ApolloConstant::APOLLO_INDIVIDUAL_ID => $oBizCommonInfo->getPassengerID(),
                'key' => $oBizCommonInfo->getPassengerID(),
                'pid' => $oBizCommonInfo->getPassengerID(),
                'phone' => $oBizCommonInfo->getPassengerPhone(),
                'city' => $oBizCommonInfo->getFromCity(),
                'county' => $oBizCommonInfo->getAreaInfo()->iFromCounty,
                'app_version' => $oBizCommonInfo->getAppVersion(),
                'access_key_id' => $oBizCommonInfo->getAccessKeyID(),
                'page_type' => $oBizCommonInfo->getClientInfo()->iPageType,
                'tab_id' => $oBizCommonInfo->oCommonInfo->sTabId,
            ];

            $toggleResult = Apollo::getInstance()->featureToggle('surprise_discount_box_toggle', $aApolloParams);
            $sControlVersion = self::VERSION_CONTROL_MAP[$oBizCommonInfo->getAccessKeyID()];
            $bHighVersion = isset($sControlVersion) && version_compare($oBizCommonInfo->getAppVersion(), $sControlVersion, ">=");
            $this->bIsHit = $bHighVersion && $toggleResult->allow();
        }
    }

    public function isHitSurpriseDiscountBox() {
        return $this->bIsHit;
    }
}