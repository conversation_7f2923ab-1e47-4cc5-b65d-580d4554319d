<?php
namespace PreSale\Logics\v3Estimate\multiResponse\Component\productInfo;

use BizLib\Utils\Horae;
use BizLib\Utils\ProductCategory;
use Dukang\PropertyConst\Order\OrderEstimatePcId;
use PreSale\Logics\newFormTab\Tab;
use PreSale\Logics\v3Estimate\AthenaRecommend;
use PreSale\Logics\v3Estimate\BizProduct;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;
use PreSale\Logics\v3Estimate\multiResponse\PreRender\DefaultAuthLogic;
use TripcloudCommon\Utils\Product as TripcloudProduct;
use PreSale\Logics\v3Estimate\multiResponse\PreRender\CarpoolPreMatchLogic;
use BizCommon\Constants\OrderNTuple;
use \Dirpc\SDK\PreSale\NewFormExtraMap;

use PreSale\Logics\v3Estimate\multiResponse\Component\DataInfo;
use PreSale\Logics\v3Estimate\multiResponse\Component\Render;
use BizLib\Utils\Language;
use PreSale\Logics\scene\custom\CustomLogic;
use PreSale\Logics\taxi\TaxiLanKeBao;
use PreSale\Logics\v3Estimate\DecisionV2Service;
use PreSale\Logics\v3Estimate\multiResponse\Component\preferInfo\UniTaxiFeaturePrefer;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutBuilder;
use Xiaoju\Apollo\Apollo;

/**
 * Class NormalProductInfo
 */
class NormalProductInfoV2 implements Render
{
    use DataInfo;

    const TRIPCLOUD_NEW_FORM = 'tripcloud_form_style_controller';

    /**
     * @var \PreSale\Logics\v3Estimate\BizProduct
     */
    private $_oBizProductInfo = null;

    /**
     * @var \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo
     */
    private $_oBizCommonInfo = null;

    /**
     * @param BizProduct    $oProduct    oProduct
     * @param BizCommonInfo $oCommonInfo oCommonInfo
     */
    public function __construct($oProduct, $oCommonInfo) {
        $this->_oBizCommonInfo  = $oCommonInfo;
        $this->_oBizProductInfo = $oProduct;
    }

    /**
     * @return \PreSale\Logics\v3Estimate\BizProduct
     */
    final public function getBizProduct() {
        return $this->_oBizProductInfo;
    }

    /**
     * @return \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo
     */
    final public function getBizCommonInfo() {
        return $this->_oBizCommonInfo;
    }

    /**
     * @param \Dirpc\SDK\PreSale\NewFormEstimateData $oEstimateData oEstimateData
     * @return void
     */
    public function build($oEstimateData) {
        $oEstimateData->setEstimateID($this->estimateID());
        $oEstimateData->setProductCategory($this->productCategory());
        $bIsTripCloud = $this->_oBizProductInfo->oProduct->isTripcloud();
        $bIsFastcar   = $this->_isNormalFastCar();
        $bIsTaxi = $this->_oBizProductInfo->oProduct->oOrderInfo->iProductId == OrderNTuple::PRODUCT_ID_UNITAXI;

        // 产品诉求 希望只有三方的时候不进行动调拦截
        if ($this->isHitDynamicPrice() && !$bIsTripCloud && !$bIsFastcar && !$bIsTaxi) {
            $oEstimateData->setHitDynamicPrice(1);
        }

        if ($this->isHaveRedPacket()) {
            $oEstimateData->setHitShowH5Type(1);
        }

        $oEstimateData->setIsTripcloud($bIsTripCloud);
        $oEstimateData->setExtraMap($this->extraMap());
    }

    /**
     * @return string
     */
    public function estimateID() {
        return $this->getEstimateID();
    }
    /**
     * @return int
     */
    public function productCategory() {
        return $this->getProductCategory();
    }
    /**
     * @return bool
     */
    public function isHitDynamicPrice() {
        return $this->getBillInfo()->getDynamicDiffPrice() > 0;
    }

    /**
     * @return bool
     */
    public function isHaveRedPacket() {
        // app/logics/v3Estimate/multiResponse/Component/dynamicPrice/NormalDynamicPrice.php 58
        $aDisplayLine = $this->getBillInfo()->getDisplayLines();

        foreach ($aDisplayLine as $oItem) {
            if ('red_packet' == $oItem->getName() && $oItem->getValue() > 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * 发单参数(品类级别)
     * @return NewFormExtraMap
     */
    public function extraMap() {
        $aExtra = [
            'product_id'       => $this->getOrderInfo()->iProductId,
            'business_id'      => $this->getOrderInfo()->iBusinessId,
            'combo_type'       => $this->__getComboType(),
            'require_level'    => $this->getOrderInfo()->iRequireLevel,

            'level_type'       => $this->getOrderInfo()->iLevelType,

            'combo_id'         => $this->getOrderInfo()->iComboId,
            'route_type'       => $this->getOrderInfo()->iRouteType,

            'is_special_price' => $this->getOrderInfo()->iIsSpecialPrice,

            'count_price_type' => $this->getBillInfo()->getCountPriceType(),
        ];
        //司乘议价&单勾导流场景下，将导流来源标识给到发单
        if (ProductCategory::PRODUCT_CATEGORY_BARGAIN_CAR == $this->getOrderInfo()->iProductCategory && !AthenaRecommend::getInstance()->bargainIsMultiSelect()) {
            $aExtra['bargain_from_type'] = 1;
        }

        //深港车口岸控制
        if (\BizLib\Constants\Horae::TYPE_COMBO_SHENGANG_FLAT_RATE == $this->getOrderInfo()->iComboType) {
            $iCityId     = $this->getAreaInfo()->iArea;
            $iToCityId   = $this->getAreaInfo()->iToArea;
            $sKey        = $iCityId . '_' . $iToCityId;
            $aConf = Language::getDecodedTextFromDcmp('estimate_form_v3-yuegang_port_type',$this->getBizCommonInfo()->getAppLanguage());
            if (!empty($aConf)) {
                if (!empty($aConf[$sKey])) {
                    $aExtra['port_type'] = $aConf[$sKey];
                } elseif (!empty($aConf['default'])) {
                    $aExtra['port_type'] = $aConf['default'];
                }
            }
        }

        // 检查该品类是否可以默认授权
        if (DefaultAuthLogic::getInstance()->needDefaultAuth($this->_oBizProductInfo->oProduct, $this->getOrderInfo()->iBusinessId, $this->getOrderInfo()->iProductCategory)) {
            $aExtra['is_default_auth'] = 1; // 为1代表需要去授权
        }
        // 待授权的协议
        $aExtra['need_auth_list'] = DefaultAuthLogic::getInstance()->needAuthList();

        // 三方聚合表单is_default_auth = 0
        if (Tab::isHitClassifyTabByTabId($this->_oBizCommonInfo->oCommonInfo->sTabId)) {
            $aApolloParams = $this->_oBizCommonInfo->getApolloParams(null);
            $aApolloToggle      = Apollo::getInstance()->featureToggle(self::TRIPCLOUD_NEW_FORM, $aApolloParams);
            //apollo控制放量
            if (!$aApolloToggle->allow()) {
                $aExtra['is_default_auth'] = 0;
            }
        }

        if (OrderNTuple::CARPOOL_TYPE_MINI_BUS == $this->getOrderInfo()->iCarpoolType) {
            $aExtra['etp'] = CarpoolPreMatchLogic::getInstance()->getMiniBusInfo($this->_oBizProductInfo)['etp_info']['etp_time_duration'];
        }

        // 揽客宝与一车双价同时出现时，勾选品类等于默认勾选首选司机
        $sExtraCustomFeat = $this->__buildExtraCustomFeature($this->getOrderInfo()->iProductCategory);
        $aExtra['extra_custom_feature'] = $sExtraCustomFeat;

        //城际自营惊喜独享，增加入参
        if ($this->getOrderInfo()->judgeIntercitySurpriseAlone()) {
            $aExtra['departure_range'] = $this->getOrderInfo()->sDepartureRange;
            $aExtra['carpool_seat_num'] = 1;
            $aExtra['is_intercity_surprise_alone'] = "1";
        }
        return new NewFormExtraMap($aExtra);
    }

    /**
     * @return int
     */
    private function __getComboType() {
        //$aBillProduct = $this->_aInfo['bill_info']['product_infos'][$this->_sCarLevel];
        //if (\BizLib\Constants\Horae::TYPE_COMBO_MULTI_FACTOR_FLAT_RATE == $aBillProduct['combo_type']) {
          //  return $aBillProduct['combo_type'];
        //} else {
            return $this->getOrderInfo()->iComboType;
        //}
    }

    /**
     * 勾选出租车盒子任一品类或全部品类，默认勾选首选司机，跟随品类勾选写入揽客宝特征
     * @param int $iPcId 品类id
     * @return string extra_custom_feature字段
     */
    private function __buildExtraCustomFeature($iPcId) {
        $bIsS3eOpen = TaxiLanKeBao::getInstance()->getSSSEData($iPcId);
        if (!$bIsS3eOpen) {
            return '';
        }

        $aPcIdConfList = DecisionV2Service::getInstance()->getProductsListByGroupID(
            LayoutBuilder::SUB_GROUP_ID_TAXI_PRICING,
        );

        $aPcIdConfList = array_merge($aPcIdConfList,
            DecisionV2Service::getInstance()->getProductsListByGroupID(
                LayoutBuilder::SUB_GROUP_ID_UNITAXI,
            )
        );

        if (in_array($iPcId, $aPcIdConfList)) {
            $sCustomFeature = json_encode(
                [
                    'id'    => CustomLogic::CUSTOM_SERVICE_LANKEBAO_PICKUP,
                    'count' => 1,
                ]
            );
            return $sCustomFeature;
        }

        return '';
    }

    /**
     * @return bool
     */
    private function _isNormalFastCar() {
        $productCategory = $this->_oBizProductInfo->getProductCategory();
        return in_array($productCategory, [
            OrderEstimatePcId::EstimatePcIdFastCar,
            OrderEstimatePcId::EstimatePcIdFastSpecialRate,
            OrderEstimatePcId::EstimatePcIdSpecialRate,
            OrderEstimatePcId::EstimatePcIdAplus,
            OrderEstimatePcId::EstimatePcIdSpaciousCar,
            OrderEstimatePcId::EstimatePcIdEstimatePcIdTimeLimitSpecialRate,
        ]);
    }
}
