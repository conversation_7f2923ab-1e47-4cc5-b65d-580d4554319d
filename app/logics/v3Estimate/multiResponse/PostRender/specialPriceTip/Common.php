<?php

namespace PreSale\Logics\v3Estimate\multiResponse\PostRender\specialPriceTip;

use BizCommon\Utils\Horae;
use BizCommon\Utils\Order;
use BizLib\Utils\ProductCategory;
use Dukang\PropertyConst\Order\OrderEstimatePcId;
use PreSale\Logics\taxi\TaxiPeakFee;
use PreSale\Logics\passenger\SpecialFeeCommonLogic;
use PreSale\Logics\v3Estimate\AthenaRecommend;
use PreSale\Logics\v3Estimate\DecisionV2Service;
use PreSale\Logics\v3Estimate\FeaturePlugin;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutBuilder;
use PreSale\Models\featurePlugin\Common as FeatureCommon;
use Xiaoju\Apollo\Apollo;
use BizLib\Constants;
use BizLib\Constants\OrderSystem;
use BizLib\Utils\CarLevel;

use PreSale\Logics\taxi\TaxiCarType;

/**
 * Common
 */
class Common
{
    //特殊订单计价类型,价格沟通组件使用
    // 2021-12-24: 应该是 rule_type关联 hit 和 render两个方法; 不过我懒
    const ENABLE_SPECIAL_PRICE_EXPLAIN_VERSION = '5.2.58';

    
    const RULE_TYPE_CROSSCITY_NEGOTIATION = 37;     // 协商跨城费
    const RULE_TYPE_HK_PRODUCT_SPECIAL_RULE = 38;
    const RULE_TYPE_HK_PRODUCT_NO_TUNNEL_FEE  = 36; // 已弃用 香港品类未命中隧道费
    const RULE_TYPE_HK_PRODUCT_ADDITIONAL_FEE = 35; // 已弃用 香港的士(product_id=12、7501、7502...)附加费
    const RULE_TYPE_HK_PRODUCT_TUNNEL_FEE  = 24; // 已弃用 香港品类命中隧道费
    const RULE_TYPE_HK_THIRTY_TAXI         = 34; // 已弃用 香港三方出租车
    const RULE_TYPE_HK_CAP_TAXI            = 30; // 已弃用 香港一口价出租
    const RULE_TYPE_TAXI_CARPOOL      = 33; // 出租车拼车
    const RULE_TYPE_FAST_TAXI         = 32; // 补天出租车在线计价
    const RULE_TYPE_CAP_FAST          = 31; // 快车一口价
    const RULE_TYPE_TP_TAXI_PRICE     = 28; // delete TP出租车
    const RULE_TYPE_TP_FAST_CAR_PRICE = 27; // delete TP快车
    const RULE_TYPE_TAXI_HOLIDAY_SURCHARGE   = 26; // 出租车节假日附加费
    const RULE_TYPE_ENERGY_CONSUME_FEE       = 25; // 综合能耗费
    const RULE_TYPE_CARPOOL_DUAL_PRICE_MERGE = 23; // 两口价合并一口价
    const RULE_TYPE_TAXI_PEAK_FEE            = 22; // 普通出租车高峰期加价
    const RULE_TYPE_CARPOOL_DUAL_PRICE       = 21; // 两口价
    const RULE_TYPE_PRIVACY_PROTECTION       = 20; // 隐私保护
    const RULE_TYPE_MARKETING_PRICE_PUTONG_TAXI  = 19; // 普通出租车一车两价
    const RULE_TYPE_MARKETING_PRICE_YOUXUAN_TAXI = 18; // 优选出租车一车两价
    const RULE_TYPE_INTER_CITY_CARPOOL_NEW_MODE  = 17; // 远途特快一口价
    const RULE_TYPE_FIXED_PRICE_TAXI     = 16; // 一口价出租车
    const RULE_TYPE_DIONE_PRICE          = 15; // D1一口价
    const RULE_TYPE_FAST_PRICE           = 13; // 快车一口价
    const RULE_TYPE_TOLL_NODE_WITH_EXTRA = 12; // 疫情期间有高速费的路线沟通
    const RULE_TYPE_TOLL_NODE            = 11; // 疫情期间无高速费的路线沟通
    const RULE_TYPE_AIRPORT_FEE          = 10; // 接送机场景独立计价
    const RULE_TYPE_RED_PACKET           = 9;  // 春节服务费
    const RULE_TYPE_SPECIAL_YOUXUAN_TAXI = 8;  // 优选出租车
    const RULE_TYPE_SHENGANG_FLAT_RATE   = 6;  // 粤港车（区域一口价）
    const RULE_TYPE_EXTRA_FEE            = 5;  // 附加费，目前只有高速费
    const RULE_TYPE_CAP_PRICE            = 4;  // 专车一口价
    const RULE_TYPE_CROSSCITY            = 3;  // 跨城费
    const RULE_TYPE_BOOKING = 2;  // 预约单计价
    const RULE_TYPE_FENCE   = 1;  // 特殊区域计价

    // 特殊费项说明tip DCMP KEY
    const DCMP_KEY_HK_PRODUCT_SPECIAL_RULE = 'harbour-tip_special_rule'; // DCMP_KEY_HK_PRODUCT_SPECIAL_RULE
    const DCMP_KEY_HK_PRODUCT_SPECIAL_RULE_RICH = 'harbour-tip_special_rule_rich'; // DCMP_KEY_HK_PRODUCT_SPECIAL_RULE_RICH

    const PREFIX_TYPE_ZHUANCHE_AIRPORT_PICK_UP = 'S_PASSENGER_ZHUANCHE_AIRPORT_PICK_UP_';
    const PREFIX_TYPE_EXTRA = 'S_PASSENGER_EXTRA_HIT_';                     // 附加费强弹标识前缀
    // 注意：以下key由阿波罗控制!!! gs_fee_force_notice_switch
    const PREFIX_TYPE_CAP_PRICE          = 'S_PASSENGER_CAP_PRICE_HIT_';    // 多因素一口价强弹前缀
    const PREFIX_TYPE_FAST_PRICE         = 'S_PASSENGER_FAST_PRICE_HIT_';   // 快车多因素一口价强弹前缀
    const PREFIX_TYPE_APLUS_PRICE        = 'S_PASSENGER_APLUS_PRICE_HIT_';  // A+一口价强弹前缀
    const PREFIX_TYPE_SPECIAL_RATE_PRICE = 'S_PASSENGER_SPECIAL_RATE_HIT_'; // 特惠快车一口价强弹枪前缀

    const SUPPORT_PRODUCTID   = [
        Constants\OrderSystem::PRODUCT_ID_ANY_CAR,           // anycar
        Constants\OrderSystem::PRODUCT_ID_FAST_CAR,          // 快车
        Constants\OrderSystem::PRODUCT_ID_DEFAULT,           // 专车
        Constants\OrderSystem::PRODUCT_ID_BUSINESS,          // 企业专车
        Constants\OrderSystem::PRODUCT_ID_BUSINESS_FAST_CAR, // 企业快车
        Constants\OrderSystem::PRODUCT_ID_UNITAXI,           // 出租车的优选需要用到价格透传组件
        Constants\OrderSystem::PRODUCT_ID_BUSINESS_TAXI_CAR, // 企业出租车的优选需要用到价格透传组件
        Constants\OrderSystem::PRODUCT_ID_DIONE,             // D1
        Constants\OrderSystem::PRODUCT_ID_SPECIAL_RATE,      //特惠快车
        Constants\OrderSystem::PRODUCT_ID_HK_TAXI_CAR,       // 香港的士
        Constants\OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR,
        Constants\OrderSystem::PRODUCT_ID_DIDI_MINI,         // 小神车
        Constants\OrderSystem::PRODUCT_ID_BARGAIN_CAR,       // 惠选车
        Constants\OrderSystem::PRODUCT_ID_SPECIAL_PREMIUM,   // 轻享专车
    ];
    const SUPPORT_CLIENT_TYPE = [
        Constants\Common::CLIENT_TYPE_ANDROID,
        Constants\Common::CLIENT_TYPE_IOS,
        Constants\Common::CLIENT_TYPE_WEBAPP,
    ];
    const NOT_HIT_FIXED_PRICE_TAXI_CITY_HZ = 5;  // 杭州不展示底部价格沟通



    /**
     * 6.0强弹字段
     * @param array $aResponse aResponse
     * @param array $aRet      aRet
     * @return mixed
     */
    public static function buildDaCheForceNotice($aResponse, $aRet) {
        if (isset($aResponse['special_price_text'])) {
            $aResponse['special_price_text']['is_force_notice'] = empty($aRet) ? 0 : 1;
        }

        return $aResponse;
    }

    /**
     * RULE_TYPE_RED_PACKET
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitRedPacket($oBizProduct, $oBizCommonInfo) {
        $fX = $oBizProduct->tryGetRedPacketFee();
        return !empty($fX);
    }

    /**
     * RULE_TYPE_EXTRA_FEE
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitHighwayFee($oBizProduct, $oBizCommonInfo) {
        $fHighFee = $oBizProduct->getBillInfo()->getHighwayFee();
        if (!empty($fHighFee) && is_numeric($fHighFee)) {
            return $fHighFee > 0;
        }

        return false;
    }
    /**
     * RULE_TYPE_INTER_CITY_CARPOOL_NEW_MODE
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitIntercityCarpoolNewmode($oBizProduct, $oBizCommonInfo) {
        return \BizLib\Utils\Horae::isInterCityCarpoolNewMode(
            $oBizProduct->getOrderInfo()->iComboType,
            $oBizProduct->getOrderInfo()->iCarpoolType
        );
    }

    /**
     * RULE_TYPE_CARPOOL_DUAL_PRICE
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitCarpoolDualPrice($oBizProduct, $oBizCommonInfo) {

        $bIgnoreCarpool = Apollo::getInstance()->featureToggle(
            'communicate_carpool_grayscale',
            [
                'key'           => $oBizCommonInfo->getPassengerID(),
                'phone'         => $oBizCommonInfo->getPassengerPhone(),
                'access_key_id' => $oBizCommonInfo->getAccessKeyID(),
                'lang'          => $oBizCommonInfo->getAppLanguage(),
                'city'          => $oBizCommonInfo->getFromCity(),
                'app_version'   => $oBizCommonInfo->getAppVersion(),
            ]
        )->allow();

        if ($bIgnoreCarpool) {
            return false;
        }

        return $oBizProduct->isCarpoolDualPriceV3() && !$oBizProduct->isHitCarpoolDualPriceMerge();
    }


    /**
     * RULE_TYPE_CARPOOL_DUAL_PRICE_MERGE
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function isCarpoolUnSuccessFlatPriceShowAsCapPrice($oBizProduct, $oBizCommonInfo) {
        return $oBizProduct->isHitCarpoolDualPriceMerge();
    }



    /**
     * RULE_TYPE_TOLL_NODE_WITH_EXTRA
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitTollnodeWithExtra($oBizProduct, $oBizCommonInfo) {
        $oToggle = Apollo::getInstance()->featureToggle(
            'toll_node_route_explain',
            [
                'key'   => $oBizCommonInfo->getPassengerID(),
                'phone' => $oBizCommonInfo->getPassengerPhone(),
                'city'  => $oBizCommonInfo->getFromCity(),
            ]
        );
        return self::hitHighwayFee($oBizProduct, $oBizCommonInfo) && $oToggle->allow();
    }

    /**
     * RULE_TYPE_TOLL_NODE
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitTollnode($oBizProduct, $oBizCommonInfo) {
        $oToggle          = Apollo::getInstance()->featureToggle(
            'toll_node_route_explain',
            [
                'key'   => $oBizCommonInfo->getPassengerID(),
                'phone' => $oBizCommonInfo->getPassengerPhone(),
                'city'  => $oBizCommonInfo->getFromCity(),
            ]
        );
        $aBillExtraInfo   = $oBizProduct->getBillInfo()->getHistoryExtraMap();
        $bHaveTollStation = $aBillExtraInfo['have_toll_station'] ?? false;

        return $bHaveTollStation && !self::hitHighwayFee($oBizProduct, $oBizCommonInfo) && $oToggle->allow();
    }

    /**
     * RULE_TYPE_CAP_PRICE
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitCapPrice($oBizProduct, $oBizCommonInfo) {
        $fCapPrice  = $oBizProduct->getBillInfo()->getCapPrice();
        $iProductID = $oBizProduct->getProductID();

        return in_array($iProductID, [OrderSystem::PRODUCT_ID_DEFAULT, OrderSystem::PRODUCT_ID_BUSINESS])
            && $fCapPrice > 0
            && \BizLib\Utils\Horae::isMultiFactorFlatRateV2(['combo_type' => $oBizProduct->getOrderInfo()->iComboType, 'count_price_type' => $oBizProduct->getBillInfo()->getCountPriceType()]);
    }

    /**
     * 综合能耗费
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitEnergyFee($oBizProduct, $oBizCommonInfo) {
        if (!empty($oBizProduct->getBillInfo()) && $oBizProduct->getBillInfo()->getFeeDetailInfo()['energy_consume_fee'] > 0) {
            return true;
        }

        return false;
    }

    /**
     * RULE_TYPE_FAST_PRICE
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitFastPrice($oBizProduct, $oBizCommonInfo) {
        $iProductID = $oBizProduct->getProductID();

        return in_array($iProductID, [OrderSystem::PRODUCT_ID_FAST_CAR, OrderSystem::PRODUCT_ID_BUSINESS_FAST_CAR])
            && \BizLib\Utils\Horae::isMultiFactorFlatRateV2(['combo_type' => $oBizProduct->getOrderInfo()->iComboType, 'count_price_type' => $oBizProduct->getBillInfo()->getCountPriceType()]);
    }

    /**
     * RULE_TYPE_DIONE_PRICE
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitDiOnePrice($oBizProduct, $oBizCommonInfo) {
        $aParam = [
            'product_id'    => $oBizProduct->getProductID(),
            'require_level' => $oBizProduct->getOrderInfo()->iRequireLevel,
            'combo_type'    => $oBizProduct->getOrderInfo()->iComboType,
        ];

        return \BizCommon\Utils\Horae::isDiOne($aParam) && \BizLib\Utils\Horae::isDioneCountPriceType(['count_price_type' => $oBizProduct->getBillInfo()->getCountPriceType()]);
    }

    /**
     * RULE_TYPE_FIXED_PRICE_TAXI
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitFixedPriceTaxi($oBizProduct, $oBizCommonInfo) {
        if ($oBizCommonInfo->getAreaInfo()->iArea == Common::NOT_HIT_FIXED_PRICE_TAXI_CITY_HZ) {
            return false;
        }
        $oOrderInfo = $oBizProduct->getOrderInfo();

        $aParam = $oBizProduct->getOrderNTupleArrayForProductCategoryMap();

        $aParam = array_merge(
            $aParam,
            [
                'product_id'       => $oBizProduct->getProductID(),
                'level_type'       => $oOrderInfo->iLevelType,
                'is_special_price' => (bool)$oOrderInfo->iIsSpecialPrice,
            ]
        );

        return TaxiCarType::isTaxiFixedPrice($aParam);
    }
    /**
     * RULE_TYPE_MARKETING_PRICE_PUTONG_TAXI
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitMarketingPricePutongTaxi($oBizProduct, $oBizCommonInfo) {
        $oOrderInfo = $oBizProduct->getOrderInfo();

        $aParam = $oBizProduct->getOrderNTupleArrayForProductCategoryMap();

        $aParam = array_merge(
            $aParam,
            [
                'product_id'       => $oBizProduct->getProductID(),
                'level_type'       => $oOrderInfo->iLevelType,
                'require_level'    => $oOrderInfo->iRequireLevel,
                'is_special_price' => (bool)$oOrderInfo->iIsSpecialPrice,
            ]
        );

        return TaxiCarType::isTaxiPutongMarketingPrice($aParam);
    }

    /**
     * RULE_TYPE_FAST_TAXI
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitFastTaxiPriceRule($oBizProduct) {
        return OrderEstimatePcId::EstimatePcIdFastTaxi == $oBizProduct->getProductCategory() &&
            LayoutBuilder::SUB_GROUP_ID_TAXI_PRICING == DecisionV2Service::getInstance()->getSubGroupIDByPcID(OrderEstimatePcId::EstimatePcIdFastTaxi);
    }

    /**
     * RULE_TYPE_MARKETING_PRICE_YOUXUAN_TAXI
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitMarketingPriceYouxuanTaxi($oBizProduct, $oBizCommonInfo) {
        $oOrderInfo = $oBizProduct->getOrderInfo();

        $aParam = $oBizProduct->getOrderNTupleArrayForProductCategoryMap();

        $aParam = array_merge(
            $aParam,
            [
                'product_id'       => $oBizProduct->getProductID(),
                'level_type'       => $oOrderInfo->iLevelType,
                'require_level'    => $oOrderInfo->iRequireLevel,
                'is_special_price' => (bool)$oOrderInfo->iIsSpecialPrice,
            ]
        );

        return TaxiCarType::isTaxiYouxuanMarketingPrice($aParam);
    }

    /**
     * RULE_TYPE_CROSSCITY
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitCrosscity($oBizProduct, $oBizCommonInfo) {
        $fCrossCity = $oBizProduct->getBillInfo()->getCrossCityFee();

        return $fCrossCity > 0;
    }

    /**
     * RULE_TYPE_BOOKING
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitBooking($oBizProduct, $oBizCommonInfo) {
        $bStartPriceAllow = (Apollo::getInstance())->featureToggle(
            'booking_communicate_component',
            [
                'key'              => $oBizCommonInfo->getPassengerPhone(),
                'phone'            => $oBizCommonInfo->getPassengerPhone(),
                'city'             => $oBizCommonInfo->getFromCity(),
                'product_id'       => $oBizProduct->getProductID(),
                'car_level'        => $oBizProduct->getOrderInfo()->iRequireLevel,
                'lang'             => $oBizCommonInfo->getAppLanguage(),
                'app_version'      => $oBizCommonInfo->getAppVersion(),
                'access_key_id'    => $oBizCommonInfo->getAccessKeyID(),
                'combo_type'       => $oBizProduct->getOrderInfo()->iComboType,
                'menu_id'          => $oBizCommonInfo->getClientInfo()->sMenuId,
                'product_category' => $oBizProduct->getProductCategory(),
                'page_type'        => $oBizCommonInfo->getClientInfo()->iPageType,
            ]
        )->allow();

        $aBillExtraInfo = $oBizProduct->getBillInfo()->getHistoryExtraMap();
        $fStartPrice    = $aBillExtraInfo['start_price'] ?? 0;
        $fLimitFee      = $aBillExtraInfo['limit_fee'] ?? 0;

        if (Constants\OrderSystem::TYPE_ORDER_BOOKING == $oBizProduct->getOrderInfo()->iOrderType) {
            if (($bStartPriceAllow && $fStartPrice > 0)
                || (!$bStartPriceAllow && $fLimitFee > 0)
            ) {
                return true;
            }
        }

        return false;
    }

    /**
     * RULE_TYPE_SHENGANG_FLAT_RATE
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitShengangFlatRate($oBizProduct, $oBizCommonInfo) {
        return \BizLib\Utils\Horae::isShenGangFlatRate(
            $oBizProduct->getOrderInfo()->iComboType
        );
    }

    /**
     * RULE_TYPE_SPECIAL_YOUXUAN_TAXI
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitSpecialYouxuanTaxi($oBizProduct, $oBizCommonInfo) {
        if (CarLevel::DIDI_UNITAXI_YOUXUAN_CAR_LEVEL != $oBizProduct->getOrderInfo()->iRequireLevel) {
            return false;
        }

        $oFeatureToggle = Apollo::getInstance()->featureToggle(
            'youxuan_special_rule_whitelist',
            [
                'key'           => $oBizCommonInfo->getPassengerID(),
                'phone'         => $oBizCommonInfo->getPassengerPhone(),
                'city'          => $oBizCommonInfo->getFromCity(),
                'access_key_id' => $oBizCommonInfo->getAccessKeyID(),
                'app_version'   => $oBizCommonInfo->getAppVersion(),
                'menu_id'       => $oBizCommonInfo->getClientInfo()->sMenuId,
            ]
        );
        return $oFeatureToggle->allow();
    }

    /**
     * RULE_TYPE_AIRPORT_FEE
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitAirportFee($oBizProduct, $oBizCommonInfo) {
        return   in_array($oBizProduct->getProductID(), [Constants\OrderSystem::PRODUCT_ID_DEFAULT, Constants\OrderSystem::PRODUCT_ID_FIRST_CLASS_CAR])
            && Constants\Horae::TYPE_COMBO_FROM_AIRPORT == $oBizProduct->getOrderInfo()->iComboType;
    }

    /**
     * RULE_TYPE_FENCE
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitFence($oBizProduct, $oBizCommonInfo) {
        $aBillExtraInfo = $oBizProduct->getBillInfo()->getHistoryExtraMap();
        $sFenceID       = (string)$aBillExtraInfo['fence_id'] ?? '';
        if (!empty($aBillExtraInfo['sps_start_fence_id'])) {
            $sFenceID = implode(',', [$aBillExtraInfo['sps_start_fence_id'], $aBillExtraInfo['sps_end_fence_id']]);
        }

        $oToggle = (Apollo::getInstance())->featureToggle(
            'gs_special_fence_explain_switch',
            [
                'key'              => $oBizCommonInfo->getPassengerID(),
                'phone'            => $oBizCommonInfo->getPassengerPhone(),
                'city'             => $oBizCommonInfo->getFromCity(),
                'fence_id'         => $sFenceID,
                'car_level'        => $oBizProduct->getOrderInfo()->iRequireLevel,
                'product_category' => $oBizProduct->getProductCategory(),
                'product_id'       => $oBizProduct->getProductID(),
            ]
        );
        return !empty($sFenceID) && $oToggle->allow();

    }

    /**
     * RULE_TYPE_TAXI_PEAK_FEE 普通出租车 && 高峰期加价场景
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitTaxiPeakFeeScene($oBizProduct, $oBizCommonInfo) {
        $oOrderInfo         = $oBizProduct->getBizProduct();
        $oProduct           = $oOrderInfo->oProduct;
        $oTaxiPeakFeeClient = TaxiPeakFee::getPoolInstance([$oProduct, $oBizCommonInfo]);
        return $oTaxiPeakFeeClient->getIsTaxiPeakFee();
    }

    /**
     * RULE_TYPE_TAXI_HOLIDAY_SURCHARGE 出租车节假日附加费
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitTaxiHolidayFeeScene($oBizProduct, $oBizCommonInfo) {
        $aBillFeeDetail = $oBizProduct->getBillInfo()->getFeeDetailInfo();
        $iHolidayFee    = $aBillFeeDetail['taxi_holiday_price'] ?? 0;
        return $iHolidayFee > 0;
    }

    /**
     * RULE_TYPE_HK_PRODUCT_SPECIAL_RULE
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitHkProductSpecialRule($oBizProduct, $oBizCommonInfo) {
        return Horae::isHongKongProduct($oBizProduct->getProductID());
    }

    /**
     * RULE_TYPE_CAP_FAST
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitCapFast($oBizProduct, $oBizCommonInfo) {
        //甄选快车不展示价格说明文案
        if (ProductCategory::PRODUCT_CATEGORY_HANDPICKED_FAST == $oBizProduct->getProductCategory()) {
            return false;
        }
        return \PreSale\Logics\v3Estimate\multiResponse\Util::isCapFast(
            $oBizProduct->getProductID(),
            $oBizProduct->getBillInfo()->getCountPriceType()
        );
    }

    /**
     * RULE_TYPE_TAXI_CARPOOL
     *
     * @param \PreSale\Logics\v3Estimate\BizProduct                 $oBizProduct    全局品类数据
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oCommonInfo
     * @return bool
     */
    public static function hitTaxiCarpool($oBizProduct, $oBizCommonInfo) {
        return ProductCategory::PRODUCT_CATEGORY_TAXI_CARPOOL == $oBizProduct->getProductCategory();
    }

    public static function unsetSpecialRule($aRuleType) {
        // 远途特快一口价
        if (FeaturePlugin::getInstance()->checkFeatureBanStatus(FeatureCommon::FEATURE_INTER_CITY_CARPOOL_CAP_PRICE)) {
            unset($aRuleType[Common::RULE_TYPE_INTER_CITY_CARPOOL_NEW_MODE]);
        }

        // 两口价
        if (FeaturePlugin::getInstance()->checkFeatureBanStatus(FeatureCommon::FEATURE_CARPOOL_DUAL_PRICE_RULE)) {
            unset($aRuleType[Common::RULE_TYPE_CARPOOL_DUAL_PRICE]);
        }

        // 专车一口价
        if (FeaturePlugin::getInstance()->checkFeatureBanStatus(FeatureCommon::FEATURE_PREMIUM_CAP_PRICE_RULE)) {
            unset($aRuleType[Common::RULE_TYPE_CAP_PRICE]);
        }

        // 快车一口价
        if (FeaturePlugin::getInstance()->checkFeatureBanStatus(FeatureCommon::FEATURE_FAST_CAR_CAP_PRICE_RULE)) {
            unset($aRuleType[Common::RULE_TYPE_FAST_PRICE]);
        }

        // 出租车一口价
        if (FeaturePlugin::getInstance()->checkFeatureBanStatus(FeatureCommon::FEATURE_TAXI_CAP_PRICE_RULE)) {
            unset($aRuleType[Common::RULE_TYPE_FIXED_PRICE_TAXI]);
        }

        return $aRuleType;
    }
}
