<?php

namespace PreSale\Logics\v3Estimate\multiResponse\layout;

use BizLib\Log as NuwaLog;
use BizLib\Utils\ProductCategory;
use Dukang\PropertyConst\Order\OrderEstimatePcId;
use PreSale\Logics\newFormTab\Tab;
use Dukang\PropertyConst\Order\OrderTypes;
use PreSale\Logics\v3Estimate\AthenaRecommend;
use PreSale\Logics\v3Estimate\AthenaRecommendRecForm;
use PreSale\Logics\v3Estimate\BizProduct;
use BizCommon\Constants\OrderNTuple;
use Dukang\PropertyConst\Product\ProductPageType;
use PreSale\Logics\v3Estimate\CategoryConfig;
use PreSale\Logics\v3Estimate\CategoryInfoLogic;
use PreSale\Logics\v3Estimate\ClassifyFoldLogic;
use PreSale\Logics\v3Estimate\DdsDecision;
use PreSale\Logics\v3Estimate\FormAB\SurpriseDiscountABParam;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;
use PreSale\Logics\v3Estimate\multiResponse\Component\Selection;
use PreSale\Logics\v3Estimate\multiResponse\layout\builder\ClassifyLayoutBuilder;
use PreSale\Logics\v3Estimate\multiResponse\layout\builder\NormalLayoutBuilder;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\BargainLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\BargainRangeLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\DiscountAllianceLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\FarMustCheaperBoxLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\ChaoZhiDaBoxLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\FlowFoldBoxLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\GoodsTransportLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\GuideCarLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\HongKongThirdBoxLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\NormalBargainLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\ShortDistanceCarAllianceLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\SingleCarLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\SpaciousCarAllianceLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\SurpriseDiscountBoxLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TaxiPricingBoxLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\UniTaxiBoxLayout;
use PreSale\Logics\v3Estimate\DecisionV2Service;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\MiniBusLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\XDiscountBoxLayout;
use TripcloudCommon\Utils\Product as TripcloudProduct;

abstract class LayoutBuilder
{

    /**
     * 聚合车型 sub_group_id
     * 1: 短途特惠, 第三方
     * 5：出租车计价盒子
     * 6: 远必省盒子
     * 7: X折特价车盒子
     * 8: 优惠联盟盒子 https://cooper.didichuxing.com/knowledge/2199455543792/2200197137724
     * 10: 超值达盒子
     * 21: 香港三方盒子-经济型
     * 22: 香港三方盒子-舒适型
     */
    const SUB_GROUP_ID_SINGLE_CAR     = 0;
    const SUB_GROUP_ID_SHORT_DISTANCE = 1;
    const SUB_GROUP_ID_UNITAXI        = 2; // 出租车盒子
    const SUB_GROUP_ID_TP           = 3; // 没用:tp表单：泛快
    const SUB_GROUP_ID_TAXI_TP      = 4; // 没用:tp表单：出租车
    const SUB_GROUP_ID_TAXI_PRICING = 5;
    const SUB_GROUP_ID_FAR_MUST_CHEAPER  = 6;
    const SUB_GROUP_ID_X_DISCOUNT        = 7;
    const SUB_GROUP_ID_DISCOUNT_ALLIANCE = 8;
    const SUB_GROUP_ID_CHAO_ZHI_DA       = 10;
    const SUB_GROUP_ID_SURPRISE_DISCOUNT = 11; //惊喜特价盒子
    const SUB_GROUP_ID_HK_THIRD_BUSINESS_NORMAL = 21; // 香港三方盒子-经济型
    const SUB_GROUP_ID_HK_THIRD_BUSINESS_COMFORT = 22; // 香港三方盒子-舒适型

    /**
     * @var array 车型排序列表 (有序map[pcId]status)
     */
    protected $_aSortedPcIdMap;

    /**
     * 置顶信息
     * @var $_aTopRecommendStyle array 置顶推荐样式
     * @var $_aTopBoxSinglePcIds array map[box_id]box_info
     */
    protected $_aTopRecommendStyle;
    protected $_aTopSinglePcIds = [];
    protected $_aTopShortDistanceSubGroupIds = [];

    /**
     * @var string[] 导流位idList
     */
    protected $_aGuidePCIds;

    /**
     * @var string[] 导流位包框idList
     */
    protected $_aGuideRecommendList;

    /**
     * @var array map[pcID] int 品类对应选中态
     */
    protected $_aPcIDToSelectType;
    /**
     * @var mixed map[product_category]res_pos 品类对应位置
     */
    protected $_aProductIdPosition;

    /**
     * @var BizProduct[] array map[product_category] BizProduct
     */
    protected $_aBizProductMap;

    /**
     * @var BizCommonInfo
     */
    protected $_oBizCommonInfo;

    /**
     * @var mixed map[sub_group_id] SubGroupInfo
     */
    protected $_aSubGroupInfoMap;

    private static $_aDefaultRecommendPCs = [
        ProductCategory::PRODUCT_CATEGORY_FAST,
        ProductCategory::PRODUCT_CATEGORY_UNIONE,
        ProductCategory::PRODUCT_CATEGORY_CARPOOL_STATION,
        ProductCategory::PRODUCT_CATEGORY_APLUS,
        ProductCategory::PRODUCT_CATEGORY_SPECIAL_RATE,
        ProductCategory::PRODUCT_CATEGORY_FAST_SPECIAL_RATE,
    ];

    /**
     * @return mixed
     */
    abstract public function buildLayout();

    /**
     * @param array         $aBizProductMap $aBizProductMap
     * @param BizCommonInfo $oBizCommonInfo $oBizCommonInfo
     * @param  array        $oBusInfo       $oBusInfo
     * @return NormalLayoutBuilder | ClassifyLayoutBuilder
     */
    public static function select($aBizProductMap, $oBizCommonInfo, $oBusInfo) {
        if (Tab::isHitClassifyTabByTabId($oBizCommonInfo->oCommonInfo->sTabId)) {
            return new ClassifyLayoutBuilder($aBizProductMap, $oBizCommonInfo, $oBusInfo);
        }

        return new NormalLayoutBuilder($aBizProductMap, $oBizCommonInfo, $oBusInfo);
    }

    /**
     * @param array         $aBizProductMap $aBizProductMap
     * @param BizCommonInfo $oBizCommonInfo $oBizCommonInfo
     * @param  array        $oBusInfo       $oBusInfo
     * @return void
     */
    public function __construct(array $aBizProductMap, $oBizCommonInfo, $oBusInfo) {
        $this->_init($aBizProductMap, $oBizCommonInfo, $oBusInfo);
        $this->_dealDefault();
    }

    /**
     * @param array         $aBizProductMap $aBizProductMap
     * @param BizCommonInfo $oBizCommonInfo $oBizCommonInfo
     * @return void
     */
    private function _init(array $aBizProductMap, $oBizCommonInfo, $oBusInfo) {
        $aEnabledPcIds         = array_keys($aBizProductMap);
        $this->_aBizProductMap = $this->_fillStationInfo($aBizProductMap, $oBusInfo);
        $this->_oBizCommonInfo = $oBizCommonInfo;

        $this->_aSortedPcIdMap = DdsDecision::getInstance()->getSortedProductsMapV2($aBizProductMap);

        $oAthena = AthenaRecommend::getInstance();

        $this->_aTopRecommendStyle = $oAthena->getTopRecommendStyle();
        $this->_aTopSinglePcIds    = array_intersect($oAthena->getTopSinglePcIds(), $aEnabledPcIds);
        $this->_aTopShortDistanceSubGroupIds = $oAthena->getTopShortDistanceSubGroupIds();

        $this->_aGuidePCIds         = array_intersect($oAthena->getGuidePCIds(), $aEnabledPcIds);
        $this->_aGuideRecommendList = $this->_getGuideRecommend();

        $this->_aProductIdPosition = $oAthena->getProductIdPosition();
        $this->_aPcIDToSelectType  = $oAthena->getPcIDToSelectType();

        $this->_aSubGroupInfoMap = $oAthena->getSubGroupInfoMap();

        SingleData::getInstance()->init($oBizCommonInfo);
    }


    /**
     * ProductMap里填充站点巴士的站点信息，拼接跳转链接用
     * @param $aBizProductMap
     * @param $oBusInfo
     * @return mixed
     */
    private function _fillStationInfo($aBizProductMap, $oBusInfo) {
        foreach ($aBizProductMap as $aProduct) {
            $oOrder = $aProduct->oProduct->oOrderInfo;
            if ($oOrder->iProductId == $oBusInfo['product_id']) {
                $oOrder->aStationList['on_station']  = $oBusInfo['on_station'];
                $oOrder->aStationList['off_station'] = $oBusInfo['off_station'];
            }
        }

        return $aBizProductMap;
    }

    /**
     * 处理默认值，兜底athena的逻辑
     * @return void
     */
    private function _dealDefault() {
        //推荐区兜底
        if (empty($this->_aProductIdPosition)) {
            foreach ($this->_aSortedPcIdMap as $iPcId => $bStatus) {
                if (in_array($iPcId, self::$_aDefaultRecommendPCs)) {
                    $this->_aProductIdPosition[$iPcId] = LayoutAbstract::RECOMMEND_AREA;
                    continue;
                }

                $this->_aProductIdPosition[$iPcId] = LayoutAbstract::OTHER_AREA;
            }
        }

        //勾选兜底
        if (empty(AthenaRecommend::getInstance()->getPcIDToSelectType())) {
            $this->_aPcIDToSelectType = Selection::$aDefaultSelect;
        }
    }

    /**
     *双导流位是否有包框
     * @return mixed
     *
     */
    private function _getGuideRecommend() {
        if (empty($this->_aTopRecommendStyle) || sizeof($this->_aGuidePCIds) < 2) {
            return null;
        }

        $aList = [];
        foreach ($this->_aGuidePCIds as $iPcId) {
            foreach ($this->_aTopRecommendStyle['top_rec_list'] as $aItem) {
                if ($aItem['category'] == $iPcId) {
                    $aList[] = $iPcId;
                }
            }
        }

        return $aList;
    }

    /**
     * 过滤多种品类
     * @param array $aNeedFilterPCIds 需要去除的PCIds
     * @return void
     */
    protected function _addFilterPCIds($aNeedFilterPCIds) {
        foreach ($aNeedFilterPCIds as $iPCId) {
            unset($this->_aSortedPcIdMap[$iPCId]);
        }
    }

    /**
     * 获取远必省盒子
     * @return mixed
     */
    protected function _getFarMustCheaperBoxBuilder() {
        $aPcIdConfList = DecisionV2Service::getInstance()->getProductsListByGroupID(self::SUB_GROUP_ID_FAR_MUST_CHEAPER);
        if (empty($aPcIdConfList)) {
            return null;
        }

        $aPcIdList = [];
        foreach ($aPcIdConfList as $iPCId) {
            $aProduct = $this->_aBizProductMap[$iPCId];
            if (empty($aProduct)) {
                continue;
            }

            $aPcIdList[] = $iPCId;
        }

        if (empty($aPcIdList)) {
            return null;
        }

        $oGuideLayoutBuilder = new FarMustCheaperBoxLayout($aPcIdList, $this->_oBizCommonInfo, $this->_aBizProductMap);

        $this->_addFilterPCIds($aPcIdList);
        return $oGuideLayoutBuilder;
    }

    /**
     * 获取超值达盒子
     * @return mixed
     */
    protected function _getChaoZhiDaBoxBuilder() {
        $aPcIdConfList = DecisionV2Service::getInstance()->getProductsListByGroupID(self::SUB_GROUP_ID_CHAO_ZHI_DA);
        if (empty($aPcIdConfList)) {
            return null;
        }

        $aPcIdList = [];
        foreach ($aPcIdConfList as $iPCId) {
            $aProduct = $this->_aBizProductMap[$iPCId];
            if (empty($aProduct)) {
                continue;
            }

            $aPcIdList[] = $iPCId;
        }

        if (empty($aPcIdList)) {
            return null;
        }

        $oGuideLayoutBuilder = new ChaoZhiDaBoxLayout($aPcIdList, $this->_oBizCommonInfo, $this->_aBizProductMap);

        $this->_addFilterPCIds($aPcIdList);
        return $oGuideLayoutBuilder;
    }

    /**
     * 获取惊喜盒子
     * @return mixed
     */
    protected function _getSurpriseDiscountBoxBuilder() {
        // 如果获取不到这个盒子的配置，大概率是因为没有开城。 所以不渲染成盒子
        // 三方的过滤 在beforeRender已经过滤掉了
        $aPcIdConfList = DecisionV2Service::getInstance()->getProductsListByGroupID(self::SUB_GROUP_ID_SURPRISE_DISCOUNT);
        if (empty($aPcIdConfList)) {
            return null;
        }
        $aPcIdList = [];
        foreach ($aPcIdConfList as $iPCId) {
            $aProduct = $this->_aBizProductMap[$iPCId];
            if (empty($aProduct)) {
                continue;
            }
            $aPcIdList[] = $iPCId;
        }
        // 这种是 盒子开城了，但是自营惊喜品类 没有透出，所以盒子也不能透出
        // 自营惊喜 没有透出的话 三方也要被干掉， 在beforeRender 已经过滤掉了
        if (empty($aPcIdList)) {
            return null;
        }

        // 这种是盒子开城了，并且有包括 自营惊喜品类，可以正常渲染为盒子
        $oGuideLayoutBuilder = new SurpriseDiscountBoxLayout($aPcIdList, $this->_oBizCommonInfo, $this->_aBizProductMap);
        $this->_addFilterPCIds($aPcIdList);
        return $oGuideLayoutBuilder;
    }

    /**
     * 获取X折特价车盒子
     * @return mixed
     */
    protected function _getXDiscountBoxBuilder() {
        $aPcIdConfList = DecisionV2Service::getInstance()->getProductsListByGroupID(self::SUB_GROUP_ID_X_DISCOUNT);
        if (empty($aPcIdConfList)) {
            return null;
        }

        $aPcIdList = [];
        foreach ($aPcIdConfList as $iPCId) {
            if (empty($this->_aBizProductMap[$iPCId]) || empty($this->_aSortedPcIdMap[$iPCId])) {
                continue;
            }

            $aPcIdList[] = $iPCId;
        }

        if (empty($aPcIdList)) {
            return null;
        }

        $oGuideLayoutBuilder = new XDiscountBoxLayout($aPcIdList, $this->_oBizCommonInfo, $this->_aBizProductMap);

        $this->_addFilterPCIds($aPcIdList);
        return $oGuideLayoutBuilder;
    }

    /**
     * 通过subGroupId获取品类信息
     *
     * @param int $iSubGroupId 盒子id
     * @return array
     */
    protected function _getSubGroupProductInfos($iSubGroupId) {
        $aPcIdConfList = DecisionV2Service::getInstance()->getProductsListByGroupID($iSubGroupId);
        if (empty($aPcIdConfList)) {
            return [];
        }

        $aProductList = [];
        foreach ($aPcIdConfList as $iPCId) {
            $aProduct = $this->_aBizProductMap[$iPCId];
            if (empty($aProduct)) {
                continue;
            }

            $aProductList[] = $iPCId;
        }

        return $aProductList;
    }

    /**
     * 获取三方优惠联盟车盒子
     *
     * @return mixed
     */
    protected function _getDiscountAllianceBuilder() {
        $aProductList = $this->_getSubGroupProductInfos(self::SUB_GROUP_ID_DISCOUNT_ALLIANCE);
        if (empty($aProductList)) {
            return null;
        }

        $iRecPos = $this->_aSubGroupInfoMap[self::SUB_GROUP_ID_DISCOUNT_ALLIANCE]['rec_pos'];
        $oGuideLayoutBuilder = new DiscountAllianceLayout(
            $aProductList,
            $this->_aPcIDToSelectType,
            $this->_aBizProductMap,
            $this->_oBizCommonInfo,
            $iRecPos
        );
        $this->_addFilterPCIds($aProductList);
        return $oGuideLayoutBuilder;
    }

    /**
     * 获取<更多>分栏渲染容器
     *
     * @return null
     */
    protected function _getMoreCategoryLayoutBuilder() {
        $aProductList = [];
        $this->_aGuidePCIds = array_values(
            array_filter(
                $this->_aGuidePCIds,
                function ($iPCId) use (&$aProductList) {
                    $aMoreCategoryPcIds = [
                        OrderEstimatePcId::EstimatePcIdPetFastCar,
                        OrderEstimatePcId::EstimatePcIdEngageCar,
                        OrderEstimatePcId::EstimatePcIdCaoCaoAccessibility,
                    ];
                    $bizProduct         = $this->_aBizProductMap[$iPCId];
                    if (!empty($bizProduct) && in_array($bizProduct->getProductCategory(), $aMoreCategoryPcIds)) {
                        $aProductList[] = $iPCId;
                        return false;
                    }

                    return true;
                }
            )
        );

        if (!empty($this->_aGuideRecommendList)) {
            $productListSet = array_flip($aProductList);
            $this->_aGuideRecommendList = array_filter(
                $this->_aGuideRecommendList,
                function ($iPCId) use ($productListSet) {
                    return !isset($productListSet[$iPCId]);
                }
            );
        }

        if (empty($aProductList)) {
            return [];
        }
        $this->_addFilterPCIds($aProductList);

        $aBuilders = [];
        foreach ($aProductList as $iPcid) {
            $aBuilders[] = new GuideCarLayout([$iPcid], $this->_oBizCommonInfo, $this->_aBizProductMap,$this->_aTopRecommendStyle);
        }
        return $aBuilders;
    }

    /**
     * 获取小件急送渲染容器
     *
     * @return array|null
     */
    protected function _getGoodsTransportLayoutBuilder() {
        $aProductList = [];
        // 过滤小件急送导流品类
        $this->_aGuidePCIds = array_values(
            array_filter(
                $this->_aGuidePCIds,
                function ($iPCId) use (&$aProductList) {
                    $bizProduct = $this->_aBizProductMap[$iPCId];
                    if (!empty($bizProduct) && PRODUCT_ID_TRANSPORT == $bizProduct->getProductID()) {
                        $aProductList[] = $iPCId;
                        return false;
                    }

                    return true;
                }
            )
        );

        if (empty($aProductList)) {
            return [];
        }

        if (!empty($this->_aGuideRecommendList)) {
            $productListSet = array_flip($aProductList);
            $this->_aGuideRecommendList = array_filter(
                $this->_aGuideRecommendList,
                function ($iPCId) use ($productListSet) {
                    return !isset($productListSet[$iPCId]);
                }
            );
        }

        $oGuideLayoutBuilder = [];
        foreach ($aProductList as $iPcId) {
            $oGuideLayoutBuilder[] = new GoodsTransportLayout(
                $iPcId,
                $this->_aBizProductMap,
                $this->_aProductIdPosition,
                $this->_oBizCommonInfo
            );
        }

        $this->_addFilterPCIds($aProductList);
        return $oGuideLayoutBuilder;
    }

    /**
     * @desc 所有的GuideLayout在此处构建
     * @return array
     */
    protected function _getGuideLayoutBuilders() {
        if (empty($this->_aGuidePCIds)) {
            return [];
        }

        $aGuideLayoutLists = [];
        $aGuidePCIds       = $this->_aGuidePCIds;
        $aFilterPCIds      = [];

        // 多导流位包框，先构建
        if (is_array($this->_aGuideRecommendList) && sizeof($this->_aGuideRecommendList) > 1) {
            $aGuideLayoutLists[] = new GuideCarLayout(
                $this->_aGuideRecommendList,
                $this->_oBizCommonInfo,
                $this->_aBizProductMap,
                $this->_aTopRecommendStyle
            );
            $aGuidePCIds         = array_diff($aGuidePCIds, $this->_aGuideRecommendList);
            $aFilterPCIds        = array_merge($aFilterPCIds,$this->_aGuideRecommendList);
        }

        // 构建剩下的单导流品类
        foreach ($aGuidePCIds as $guidePCId) {
            $layout = $this->_createGuideLayout($guidePCId);
            if ($layout) {
                $aGuideLayoutLists[] = $layout;
                $aFilterPCIds[]      = $guidePCId;
            }
        }

        $this->_addFilterPCIds($aFilterPCIds);
        return $aGuideLayoutLists;

    }

    /**
     * @param  int $guidePCId $guidePCId
     * @return BargainLayout|BargainRangeLayout|GoodsTransportLayout|GuideCarLayout|null
     */
    private function _createGuideLayout($guidePCId) {
        switch ($guidePCId) {
            case OrderEstimatePcId::EstimatePcIdGoodsTransport:
            case OrderEstimatePcId::EstimatePcIdTwoWheelerFreight:
                return new GoodsTransportLayout(
                    $guidePCId,
                    $this->_aBizProductMap,
                    $this->_aProductIdPosition,
                    $this->_oBizCommonInfo
                );

            case OrderEstimatePcId::EstimatePcIdBargain:
                return new BargainLayout(
                    $guidePCId,
                    $this->_oBizCommonInfo,
                    $this->_aBizProductMap
                );

            case OrderEstimatePcId::EstimatePcIdHuiXuanCar:
                if (!\PreSale\Logics\v3Estimate\multiResponse\Util::bBargainRangeCheckable($this->_oBizCommonInfo->oCommonInfo)) {
                    return new BargainRangeLayout(
                        $guidePCId,
                        $this->_oBizCommonInfo,
                        $this->_aBizProductMap,
                        $this->_aProductIdPosition
                    );
                }
                break;

            default:
                return new GuideCarLayout(
                    [$guidePCId],
                    $this->_oBizCommonInfo,
                    $this->_aBizProductMap,
                    $this->_aTopRecommendStyle
                );
        }

        return null;
    }

    /**
     * @return mixed
     */
    protected function _getGuideLayoutBuilder() {
        if (empty($this->_aGuidePCIds)) {
            return null;
        }

        //双导流位有包框时走新的layout
        if (!empty($this->_aGuideRecommendList) && sizeof($this->_aGuidePCIds) > 1) {
            return null;
        }

        // 推荐表单,多导流位场景下，导流位需要拆分出来
        if (sizeof($this->_aGuidePCIds) >= 2 && AthenaRecommendRecForm::getInstance()->isRecForm($this->_oBizCommonInfo)) {
            return null;
        }

        // 导流样式中如果Athena返回了司乘议价，则优先渲染司乘议价
        if (ProductCategory::PRODUCT_CATEGORY_BARGAIN_CAR == $this->_aGuidePCIds[0]) {
            $oGuideLayoutBuilder = new BargainLayout($this->_aGuidePCIds[0], $this->_oBizCommonInfo, $this->_aBizProductMap);
        } elseif (BargainRangeLayout::PRODUCT_CATEGORY_BARGAIN_RANGE == $this->_aGuidePCIds[0] && !\PreSale\Logics\v3Estimate\multiResponse\Util::bBargainRangeCheckable($this->_oBizCommonInfo->oCommonInfo)) { //自选车价格range
            $oGuideLayoutBuilder = new BargainRangeLayout($this->_aGuidePCIds[0], $this->_oBizCommonInfo, $this->_aBizProductMap, $this->_aProductIdPosition);
        } else {
            $oGuideLayoutBuilder = new GuideCarLayout($this->_aGuidePCIds, $this->_oBizCommonInfo, $this->_aBizProductMap,$this->_aTopRecommendStyle);
        }

        $this->_addFilterPCIds($this->_aGuidePCIds);
        return $oGuideLayoutBuilder;
    }


    /**
     *多导流位包框layout
     * @return mixed
     *
     */
    protected function _getGuideWrapRoundBuilder() {
        if (empty($this->_aGuidePCIds) || sizeof($this->_aGuidePCIds) < 2 || empty($this->_aGuideRecommendList)) {
            return null;
        }

        $oLayout = new GuideCarLayout($this->_aGuideRecommendList, $this->_oBizCommonInfo, $this->_aBizProductMap,$this->_aTopRecommendStyle);

        $this->_addFilterPCIds($this->_aGuideRecommendList);
        return $oLayout;
    }

    /**
     *多导流位非包框layout
     * @return mixed
     *
     */
    protected function _getGuideNoWrapRoundBuilder() {
        if (empty($this->_aGuidePCIds) || sizeof($this->_aGuidePCIds) < 2 || empty($this->_aGuideRecommendList)) {
            return null;
        }

        $aNoWrapList = array_values(array_diff($this->_aGuidePCIds,$this->_aGuideRecommendList));
        if (empty($aNoWrapList)) {
            return  null;
        }

        // 推荐表单,多导流位场景下，导流位需要拆分出来
        if (sizeof($aNoWrapList) >= 2 && AthenaRecommendRecForm::getInstance()->isRecForm($this->_oBizCommonInfo)) {
            return null;
        }

        $oLayout = new GuideCarLayout($aNoWrapList, $this->_oBizCommonInfo, $this->_aBizProductMap,$this->_aTopRecommendStyle);

        $this->_addFilterPCIds($aNoWrapList);
        return $oLayout;
    }



    /**
     * @return mixed
     */
    protected function _getNormalBargainLayoutBuilder() {

        $aProduct = $this->_aBizProductMap[ProductCategory::PRODUCT_CATEGORY_BARGAIN_CAR];
        if (empty($aProduct)
            || (is_array($this->_aGuidePCIds) && in_array(ProductCategory::PRODUCT_CATEGORY_BARGAIN_CAR, $this->_aGuidePCIds))
        ) {
            // 自选车为空不渲染      自选车在导流位不渲染
            return null;
        }

        $oLayoutBuilder = new NormalBargainLayout(ProductCategory::PRODUCT_CATEGORY_BARGAIN_CAR, $this->_oBizCommonInfo, $this->_aBizProductMap);

        $this->_addFilterPCIds([ProductCategory::PRODUCT_CATEGORY_BARGAIN_CAR]);
        return $oLayoutBuilder;
    }

    /**
     * @return mixed
     */
    protected function _getBargainRangeLayoutBuilder() {

        $aProduct = $this->_aBizProductMap[BargainRangeLayout::PRODUCT_CATEGORY_BARGAIN_RANGE];
        if (empty($aProduct)
        || is_array($this->_aGuidePCIds)
            && in_array(BargainRangeLayout::PRODUCT_CATEGORY_BARGAIN_RANGE, $this->_aGuidePCIds) ) {
            // 惠选车为空不渲染,惠选车在导流位不渲染
            return null;
        }

        if (\PreSale\Logics\v3Estimate\multiResponse\Util::bBargainRangeCheckable($this->_oBizCommonInfo->oCommonInfo)) {
            // 惠选车支持多勾，不渲染单勾样式
            return null;
        }

        $oLayoutBuilder = new BargainRangeLayout(BargainRangeLayout::PRODUCT_CATEGORY_BARGAIN_RANGE, $this->_oBizCommonInfo, $this->_aBizProductMap, $this->_aProductIdPosition);

        $this->_addFilterPCIds([BargainRangeLayout::PRODUCT_CATEGORY_BARGAIN_RANGE]);
        return $oLayoutBuilder;
    }

    protected function _getUniTaxiBoxBuilder() {
        $aPcIdConfList = DecisionV2Service::getInstance()->getProductsListByGroupID(self::SUB_GROUP_ID_UNITAXI);
        if (empty($aPcIdConfList)) {
            return null;
        }

        $aPcIdList = [];
        foreach ($aPcIdConfList as $iPCId) {
            $aProduct = $this->_aBizProductMap[$iPCId];
            if (!empty($aProduct)) {
                $aPcIdList[] = $iPCId;
            }
        }

        if (count($aPcIdList) == 0) { // 一个品类也出盒子
            return null;
        }

        $oGuideLayoutBuilder = new UniTaxiBoxLayout($aPcIdList, $this->_oBizCommonInfo, $this->_aBizProductMap, $this->_aTopShortDistanceSubGroupIds);

        $this->_addFilterPCIds($aPcIdList);
        return $oGuideLayoutBuilder;
    }

    /**
     * 获取出租车计价盒子
     * @return mixed
     */
    protected function _getTaxiPricingBoxBuilder() {

        if (OrderTypes::TypeBooking == $this->_oBizCommonInfo->oCommonInfo->iOrderType) {
            return null;
        }

        $aPcIdConfList = DecisionV2Service::getInstance()->getProductsListByGroupID(self::SUB_GROUP_ID_TAXI_PRICING);
        if (empty($aPcIdConfList)) {
            return null;
        }

        $aPcIdList = [];
        foreach ($aPcIdConfList as $iPCId) {
            $aProduct = $this->_aBizProductMap[$iPCId];
            if (!empty($aProduct)) {
                $aPcIdList[] = $iPCId;
            }
        }

        if (empty($aPcIdList)) {
            return null;
        }

        $oGuideLayoutBuilder = new TaxiPricingBoxLayout($aPcIdList, $this->_oBizCommonInfo, $this->_aBizProductMap,$this->_aTopShortDistanceSubGroupIds,$this->_aTopRecommendStyle);

        $this->_addFilterPCIds($aPcIdList);
        return $oGuideLayoutBuilder;
    }

    /**
     * 获取香港三方盒子
     * @return mixed
     */
    protected function _getHongKongThirdBoxBuilders() {
        $aBuilders = [];

        $aHKThirdBoxSubgroupIds = [
            self::SUB_GROUP_ID_HK_THIRD_BUSINESS_NORMAL,
            self::SUB_GROUP_ID_HK_THIRD_BUSINESS_COMFORT,
        ];
        foreach ($aHKThirdBoxSubgroupIds as $iSubGroupID) {
            // 获取盒子中的品类，groupID->pcID
            $aPcIds = DecisionV2Service::getInstance()->getProductsListByGroupID($iSubGroupID);
            if (empty($aPcIds)) {
                continue;
            }

            $aPcIdList = [];
            foreach ($aPcIds as $iPcId) {
                $aProduct = $this->_aBizProductMap[$iPcId];
                if (!empty($aProduct)) {
                    $aPcIdList[] = $iPcId;
                }
            }

            if (empty($aPcIdList)) {
                continue;
            }

            // 盒子聚合的品类个数决定最终是否出盒子
            $oToggle = \Xiaoju\Apollo\Apollo::getInstance()->featureToggle(
                'hk_third_box_toggle',
                [
                    'pid'           => $this->_oBizCommonInfo->getPassengerID(),
                    'fn'            => 'estimate',
                    'app_version'   => $this->_oBizCommonInfo->getAppVersion(),
                    'access_key_id' => $this->_oBizCommonInfo->getAccessKeyID(),
                    'lang'          => $this->_oBizCommonInfo->getAppLanguage(),
                    'order_type'    => $this->_oBizCommonInfo->oCommonInfo->iOrderType,
                    'sub_group_id'  => $iSubGroupID,
                    'product_count' => sizeof($aPcIdList),
                ]
            );
            if (!$oToggle->allow()) {
                continue;
            }

            $aBuilders[] = new HongKongThirdBoxLayout($aPcIdList, $iSubGroupID, $this->_oBizCommonInfo, $this->_aBizProductMap);
            $this->_addFilterPCIds($aPcIdList);
        }

        return $aBuilders;
    }

    /**
     * @return mixed
     */
    protected function _getShortDistanceCarAllianceLayoutBuilder() {
        $aPcIdList = [];
        foreach ($this->_aBizProductMap as $iPcId => $aValue) {
            if (self::SUB_GROUP_ID_SHORT_DISTANCE == DecisionV2Service::getInstance()->getSubGroupIDByPcID($iPcId)) {
                $aPcIdList[] = $iPcId;
            }
        }

        if (empty($aPcIdList)) {
            return null;
        }

        $iRecPos = $this->_aSubGroupInfoMap[self::SUB_GROUP_ID_SHORT_DISTANCE]['rec_pos'];
        $oLayout = new ShortDistanceCarAllianceLayout($aPcIdList, $this->_aPcIDToSelectType, $this->_aBizProductMap, $iRecPos, $this->_oBizCommonInfo);

        $this->_addFilterPCIds($aPcIdList);
        return $oLayout;
    }

    /**
     *
     * @return mixed
     *
     */
    protected function _getSpaciousCarLayoutBuilder() {
        if (!$this->_aSortedPcIdMap[ProductCategory::PRODUCT_CATEGORY_FAST] // 不存在快车
            || (!$this->_aSortedPcIdMap[ProductCategory::PRODUCT_CATEGORY_SPACIOUS_CAR] && !$this->_aSortedPcIdMap[ProductCategory::PRODUCT_CATEGORY_HANDPICKED_FAST])// 不存在车大甄选
        ) {
            return null;
        }

        if (empty($this->_aBizProductMap[ProductCategory::PRODUCT_CATEGORY_SPACIOUS_CAR]) && empty($this->_aBizProductMap[ProductCategory::PRODUCT_CATEGORY_HANDPICKED_FAST])) {
            return null;
        }

        $oSpaciousCarLayoutBuilder = new SpaciousCarAllianceLayout($this->_oBizCommonInfo,$this->_aBizProductMap, $this->_aPcIDToSelectType, $this->_aProductIdPosition, $this->_aTopSinglePcIds);
        $this->_addFilterPCIds([ProductCategory::PRODUCT_CATEGORY_FAST, ProductCategory::PRODUCT_CATEGORY_SPACIOUS_CAR, ProductCategory::PRODUCT_CATEGORY_HANDPICKED_FAST]);
        return $oSpaciousCarLayoutBuilder;
    }

    /**
     *
     * @return mixed
     *
     */
    protected function _getMiniBusLayoutBuilder() {
        $aPcIdList = [];
        foreach ($this->_aBizProductMap as $iPcId => $aValue) {
            if (OrderNTuple::CARPOOL_TYPE_MINI_BUS == $aValue->oProduct->oOrderInfo->iCarpoolType) {
                $aPcIdList[] = $iPcId;
            }
        }

        if (empty($aPcIdList)) {
            return null;
        }

        $oLayout = new MiniBusLayout($aPcIdList[0], $this->_aBizProductMap, $this->_aProductIdPosition, $this->_aPcIDToSelectType, $this->_oBizCommonInfo);
        $this->_addFilterPCIds($aPcIdList);
        return $oLayout;
    }

    /**
     * 获取流量盒子
     * @return mixed
     */
    protected function _getFlowFoldBoxBuilder($CategoryId, $aPcIdList, $aSubGroupIdList = []) {
        if (count($aPcIdList) + count($aSubGroupIdList) <= 1) {
            return null;
        }

        $flowFoldBoxLayout = new FlowFoldBoxLayout($this->_oBizCommonInfo, $this->_aBizProductMap,$this->_aProductIdPosition,$aPcIdList,$aSubGroupIdList,$CategoryId);

        $this->_addFilterPCIds($aPcIdList);
        return $flowFoldBoxLayout;
    }

    /**
     * @decs  处理品类需要被收进流量盒子
     * @param bool $bisRec $bisRec
     * @return array
     */
    protected function _getFlowFoldBoxBuilders($bisRec = false) {

        $aFlowBoxLayoutList = [];

        $classifyFoldLogic          = ClassifyFoldLogic::getInstance();
        $flowBoxCategoryIdToPcIDMap = $classifyFoldLogic->getFlowBoxCategoryIdToPcIDMap();
        $flowBoxCategoryIdToSubGroupIdMap = $classifyFoldLogic->getAFlowBoxCategoryIdToSubGroupIdMap();

        if (empty($flowBoxCategoryIdToPcIDMap)) {
            return $aFlowBoxLayoutList;
        }

        foreach ($flowBoxCategoryIdToPcIDMap as $categoryId => $pcIdList) {
            if (($categoryId == CategoryInfoLogic::REC_FORM_CATEGORY) ^ $bisRec) {
                continue;
            }

            $subGroupIds = $this->_getFinalSubGroupIdList($flowBoxCategoryIdToSubGroupIdMap[$categoryId] ?? []);
            $resPcIdList = $this->_getFinalPcIdList($pcIdList);

            $flowFoldBoxLayoutBuilder = $this->_getFlowFoldBoxBuilder($categoryId, $resPcIdList, $subGroupIds);
            if (!empty($flowFoldBoxLayoutBuilder) && !empty($subGroupIds)) {
                // 记录后续被收进流量盒子的group id,最后删除
                $classifyFoldLogic->setAFinalInBoxSubGroupIdMap($subGroupIds,$categoryId);
            }

            $aFlowBoxLayoutList[] = $flowFoldBoxLayoutBuilder;
        }

        return $aFlowBoxLayoutList;
    }

    /**
     * @param  array $subGroupIds $subGroupIds
     * @return array
     */
    private function _getFinalSubGroupIdList($subGroupIds) {
        if (empty($subGroupIds)) {
            return [];
        }

        return array_filter(
            $subGroupIds,
            function ($subGroupId) {
                $aPcIdConfList = DecisionV2Service::getInstance()->getProductsListByGroupID($subGroupId);
                if (empty($aPcIdConfList)) {
                    return false;
                }

            // 过滤有效的产品ID
                $resPcIdList = array_filter(
                    $aPcIdConfList,
                    function ($iPcId) {
                        return array_key_exists($iPcId, $this->_aBizProductMap);
                    }
                );

                return !empty($resPcIdList); // 只有当有效产品ID不为空时才返回true
            }
        );
    }

    /**
     * @param array $pcIdList $pcIdList
     * @return array
     */
    private function _getFinalPcIdList($pcIdList) {
        return array_filter(
            $pcIdList,
            function ($iPcId) {
                return array_key_exists($iPcId, $this->_aBizProductMap) &&
                array_key_exists($iPcId, $this->_aSortedPcIdMap);
            }
        );
    }

    /**
     * @return array
     */
    protected function _getFinalSingleCarBuilders() {
        $singleLayoutBuilders = [];
        if (empty($this->_aSortedPcIdMap)) {
            return $singleLayoutBuilders;
        }

        foreach ($this->_aSortedPcIdMap as $iPCId => $aStatus) {
            if (!array_key_exists($iPCId, $this->_aBizProductMap)) {
                continue;
            }

            $oSingleLayoutBuilder = new SingleCarLayout(
                $iPCId,
                $this->_aBizProductMap,
                $this->_aProductIdPosition,
                $this->_aPcIDToSelectType,
                $this->_oBizCommonInfo
            );
            $singleLayoutBuilders[] = $oSingleLayoutBuilder;
        }

        return $singleLayoutBuilders;
    }
}
