<?php

namespace PreSale\Logics\v3Estimate\multiResponse\layout\instance;

use BizCommon\Logics\PassengerPriceExceptionCheck\PriceExceptionChecker as Price;
use BizLib\Constants\Common;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Language;
use http\Exception\InvalidArgumentException;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\newFormTab\Tab;
use PreSale\Logics\v3Estimate\AthenaRecommend;
use PreSale\Logics\v3Estimate\AthenaRecommendRecForm;
use PreSale\Logics\v3Estimate\BizProduct;
use PreSale\Logics\v3Estimate\CategoryInfoLogic;
use PreSale\Logics\v3Estimate\FeeDescInstance;
use PreSale\Logics\v3Estimate\FormAB\SurpriseDiscountABParam;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;
use PreSale\Logics\v3Estimate\multiResponse\Component\basicFeeMsg\Util;
use PreSale\Logics\v3Estimate\multiResponse\Component\Selection;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutBuilder;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutAbstract;
use TripcloudCommon\Utils\Product as TripcloudProduct;
use Xiaoju\Apollo\ApolloConstant;

/**
 * Class 惊喜特价盒子
 * wiki: https://cooper.didichuxing.com/knowledge/2199626789935/2204653407141/
 */
class SurpriseDiscountBoxLayout extends LayoutAbstract
{
    // 自营惊喜特价 pcid
    const BASE_PC_ID = 191;
    // 惊喜特价的levelType=8
    const BASE_LEVEL_TYPE = 8;

    const VERSION_CONTROL_MAP = array(
        Common::DIDI_IOS_PASSENGER_APP => "7.1.4",
        Common::DIDI_ANDROID_PASSENGER_APP=> "7.1.4",
        Common::DIDI_WECHAT_MINI_PROGRAM => "7.0.30",
        Common::DIDI_ALIPAY_MINI_PROGRAM => "7.0.30",
    );

    private static $_oInstance;

    /**
     * @var array pcId列表
     */
    private $_aPcIds;

    /**
     * @var BizCommonInfo
     */
    private $_oBizCommonInfo;

    private $_aDcmpConfig;

    /**
     * @var BizProduct[] array map[product_category] BizProduct
     */
    private $_aBizProductMap;

    /**
     * @param array $aPcIdList $aPcIdList
     * @param BizCommonInfo $oBizCommonInfo $oBizCommonInfo
     * @param array $_aBizProductMap $aBizProductMap
     */
    public function __construct($aPcIdList, $oBizCommonInfo, $_aBizProductMap) {
        $this->_oBizCommonInfo = $oBizCommonInfo;
        $this->_aBizProductMap = $_aBizProductMap;
        $this->_aPcIds         = $aPcIdList;
        $this->_aDcmpConfig    = Language::getDecodedTextFromDcmp('estimate_new_form-surprise_discount_box_layout');
    }

    /**
     * @return array
     */
    public function buildGroups() {
        $aGroups = [];
        $aSingleGroup = [
            'products' => $this->_aPcIds,
            'type'     => self::FAR_MUST_CHEAPER_BOX_GROUP_TYPE,
            'group_id' => parent::buildGroupId(AthenaRecommend::SHORT_DISTANCE_TYPE, LayoutBuilder::SUB_GROUP_ID_SURPRISE_DISCOUNT),
        ];

        $aSingleGroup = $this->_buildGroupInfo($aSingleGroup);
        $aGroups [] = $aSingleGroup;
        return $aGroups;
    }

    /**
     * @param array $aGroup $aGroup
     * @return mixed
     */
    private function _buildGroupInfo($aGroup) {
        if (Tab::isHitClassifyTabByTabId($this->_oBizCommonInfo->oCommonInfo->sTabId)) {
            $aGroup['car_icon'] = $this->_aDcmpConfig['car_icon_for_classify'] ?: '';
        } else {
            $aGroup['car_icon'] = $this->_aDcmpConfig['car_icon'] ?: '';
        }

        $aGroup['car_title'] = $this->_aDcmpConfig['car_title'] ?: '';

        $aSelectionMap = Selection::getInstance()->aSelectionMap;
        $bHasSelected = !empty($aSelectionMap[$this::BASE_PC_ID]);
        $checkPCID = $this::BASE_PC_ID;

        $aGroup['box_desc'] = $this->_aDcmpConfig['box_desc'];
        $aGroup['popup_title'] = $this->_aDcmpConfig['popup_title'];
        $aGroup['popup_sub_title'] = $this->_aDcmpConfig['popup_sub_title'];
        $aGroup['is_selected'] = $bHasSelected ? Selection::TYPE_SELECTED : 0;

        $iFeeType = Util::FEE_TYPE_CAPPRICE;
        $fEstimateFee = $this->_aBizProductMap[$checkPCID]->getEstimateFee();
        $fFinalFee = Util::priceFormat($this->_oBizCommonInfo->getApolloParams(null), $fEstimateFee, $iFeeType);

        $aGroup['fee_amount'] = $fFinalFee;
        Price::checkSingle("pMultiEstimatePriceV3", "SurpriseDiscountBoxLayout_buildGroupInfo", "fee_amount", $fFinalFee, true);

        $aGroup['fee_msg'] = Language::replaceTag(
            $this->_aDcmpConfig['fee_msg'],
            [
                'fee_amount' => Util::formatMoneyDisplay($aGroup['fee_amount'], $this->_oBizCommonInfo->getAppLanguage()),
            ]
        );

        $aGroup['fee_msg_template'] = $this->_formatFeeMsgTemplate($this->_oBizCommonInfo,$this->_aDcmpConfig);

        $aGroup['fee_detail_url'] = Language::replaceTag($this->_aDcmpConfig['fee_detail_url'], ['eid' => $this->_aBizProductMap[$checkPCID]->getEstimateID()]);

        $feeDescList = FeeDescInstance::initFeeDescListByPCIDList([$checkPCID]);
        if (!empty($feeDescList)) {
            $aGroup['fee_desc_list'] = $feeDescList;
        }
        $aGroup['is_sub_nodisplay_etp'] = 1;
        $aGroup['popup_style_type'] = 1; //1弹层内 没有子车型暴漏

        return $aGroup;
    }

    /**
     * @return int
     */
    public function buildFormShowType() {
        //TODO 这里空指针，看下配置发布之后的情况
        $_aSubGroupInfoMap = AthenaRecommend::getInstance()->getSubGroupInfoMap();
        $iRecPos = isset($_aSubGroupInfoMap[LayoutBuilder::SUB_GROUP_ID_SURPRISE_DISCOUNT]) ? $_aSubGroupInfoMap[LayoutBuilder::SUB_GROUP_ID_SURPRISE_DISCOUNT]['rec_pos'] : null;
        return parent::athenaRecommendAreaToFormShowType($iRecPos);
    }

    /**
     * @return array
     */
    public function buildTheme() {
        return null;
    }

    /**
     * @inheritDoc
     */
    public function buildCategoryId() {
        return CategoryInfoLogic::getInstance($this->_oBizCommonInfo, $this->_aBizProductMap)->getCategoryIdByType(LayoutBuilder::SUB_GROUP_ID_SURPRISE_DISCOUNT);
    }

    /**
     * @return int
     */
    public function buildSubCategoryId() {
        return AthenaRecommendRecForm::getInstance()->getRecSubTypeForSubGroupId(LayoutBuilder::SUB_GROUP_ID_SURPRISE_DISCOUNT);
    }

    /**
     * @param BizCommonInfo $oBizCommonInfo $oBizCommonInfo
     * @param array         $aBizProductMap $aBizProductMap
     * @return array
     */
    public static function filterAfterPrice(BizCommonInfo $oBizCommonInfo, $aBizProductMap) {
        $oSurpriseBoxABParam = SurpriseDiscountABParam::getInstance($oBizCommonInfo);

        // 待过滤pcId
        $aFilterPcIdList = [];
        $oBaseProduct = $aBizProductMap[self::BASE_PC_ID];
        $bIsHit = $oSurpriseBoxABParam->isHitSurpriseDiscountBox();
        if (!$bIsHit || $oBaseProduct == null) {
            // 如果低版本 或者没开城，需要吧所有三方惊喜特价干掉
            foreach ($aBizProductMap as $iPcID => $oBizProduct) {
                // 惊喜三方
                if (self::BASE_LEVEL_TYPE == $oBizProduct->getLevelType() && TripcloudProduct::isTripcloudByProductID($oBizProduct->getProductId())) {
                    $aFilterPcIdList[] = $iPcID;
                }
            }
            $iAfterSize = count($aFilterPcIdList);
            if ($iAfterSize > 0) {
                NuwaLog::info(sprintf('惊喜盒子未开城，过滤全部三方惊喜特价|$bHasBaseProduct:%s|$aFilterPcIdList:%s', isset($oBaseProduct), json_encode($aFilterPcIdList)));
            }
        } else {
            // 已开城 并且base品类 惊喜特价存在，需要吧价格不一致的干掉
            foreach ($aBizProductMap as $iPcID => $oBizProduct) {
                // 惊喜三方
                if (self::BASE_LEVEL_TYPE == $oBizProduct->getLevelType() && TripcloudProduct::isTripcloudByProductID($oBizProduct->getProductId())) {
                    // 价格不相等
                    if ($oBizProduct->getEstimateFee() != $oBaseProduct->getEstimateFee()) {
                        $aFilterPcIdList[] = $iPcID;
                    }
                }
            }
            $iAfterSize = count($aFilterPcIdList);
            if ($iAfterSize > 0) {
                NuwaLog::info(sprintf('过滤价格不一致的惊喜三方品类|EstimateFee:%s|aFilterPcIdList:%s', $oBaseProduct->getEstimateFee(), json_encode($aFilterPcIdList)));
            }
        }

        if (count($aFilterPcIdList) > 0) {
            foreach ($aFilterPcIdList as $iPcID) {
                unset($aBizProductMap[$iPcID]);
            }
        }

        return $aBizProductMap;
    }

}