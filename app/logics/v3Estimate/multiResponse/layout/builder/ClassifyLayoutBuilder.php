<?php

namespace PreSale\Logics\v3Estimate\multiResponse\layout\builder;

use PreSale\Logics\v3Estimate\CategoryInfoLogic;
use PreSale\Logics\v3Estimate\ClassifyFoldLogic;
use PreSale\Logics\v3Estimate\DecisionV2Service;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\GuideCarLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\SingleCarLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutAbstract;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutBuilder;
use PreSale\Logics\v3Estimate\multiResponse\layout\sort_strategy\ChooseSortStrategy;
use PreSale\Logics\v3Estimate\multiResponse\layout\sort_strategy\StrategyClassifySort;
use PreSale\Logics\v3Estimate\multiResponse\layout\sort_strategy\StrategyLargeFontSort;
use PreSale\Logics\v3Estimate\multiResponse\layout\sort_strategy\StrategyPriceSort;
use PreSale\Logics\v3Estimate\multiResponse\layout\ThemeData;

class ClassifyLayoutBuilder extends LayoutBuilder
{
    /**
     * @param array         $aBizProductMap $aBizProductMap
     * @param BizCommonInfo $oBizCommonInfo $oBizCommonInfo
     * @param  array         $oBusInfo       $oBusInfo
     * @return void
     */
    public function __construct(array $aBizProductMap, $oBizCommonInfo, $oBusInfo) {
        parent::__construct($aBizProductMap, $oBizCommonInfo, $oBusInfo);
    }

    /**
     * @return array
     */
    public function buildLayout() {
        ThemeData::getInstance()->Init($this->_oBizCommonInfo);
        $oLayoutBuilders = $this->_getAllSortedLayoutBuilders();

        // 排序策略
        $aAbData = ChooseSortStrategy::buildAbData($this->_oBizCommonInfo);

        // 大字版适配
        if (0 != $this->_oBizCommonInfo->oCommonInfo->iFontScaleType) {
            return (new StrategyLargeFontSort($this->_aBizProductMap, $this->_oBizCommonInfo))->buildLayout($oLayoutBuilders, $aAbData);
        }

        //不下发分框,价格排序
        if (!CategoryInfoLogic::getInstance($this->_oBizCommonInfo, $this->_aBizProductMap)->isDistributeCategoryInfo()) {
            return (new StrategyPriceSort($this->_aBizProductMap, $this->_oBizCommonInfo))->buildLayout($oLayoutBuilders, $aAbData);
        }

        return (new StrategyClassifySort($this->_aBizProductMap, $this->_oBizCommonInfo,$aAbData))->buildLayout($oLayoutBuilders, $aAbData);
    }


    /**
     * @return LayoutAbstract[]
     */
    private function _getAllSortedLayoutBuilders() {
        $aLayoutList = [
            $this->_getNormalBargainLayoutBuilder(),
            $this->_getBargainRangeLayoutBuilder(),
            $this->_getSpaciousCarLayoutBuilder(),
            $this->_getMiniBusLayoutBuilder(),

            $this->_getFarMustCheaperBoxBuilder(),
            $this->_getChaoZhiDaBoxBuilder(),
            $this->_getSurpriseDiscountBoxBuilder(),
            $this->_getTaxiPricingBoxBuilder(),
            $this->_getUniTaxiBoxBuilder(),

            $this->_getXDiscountBoxBuilder(),
            $this->_getShortDistanceCarAllianceLayoutBuilder(),
            $this->_getDiscountAllianceBuilder(),
        ];

        // 导流位builders
        $guideLayoutBuilders = $this->_getGuideLayoutBuilders();

        // 香港三方盒子
        $aHKThirdBoxBuilders = $this->_getHongKongThirdBoxBuilders();

        // 流量盒子builders
        $flowFoldBoxBuilders = $this->_getFlowFoldBoxBuilders();

        $aLayoutList         = array_merge(
            $guideLayoutBuilders,
            $aLayoutList,
            $aHKThirdBoxBuilders,
            $flowFoldBoxBuilders,
            $this->_getFinalSingleCarBuilders()
        );

        return array_filter($aLayoutList);
    }
}
