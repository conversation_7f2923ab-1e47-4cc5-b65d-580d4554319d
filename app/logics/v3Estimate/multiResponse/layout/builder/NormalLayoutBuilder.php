<?php

namespace PreSale\Logics\v3Estimate\multiResponse\layout\builder;

use PreSale\Logics\newFormTab\Tab;
use PreSale\Logics\v3Estimate\FeatureRecord;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutBuilder;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\BargainRangeLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\DoubleGuideCarLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\SingleCarLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TaxiTimePriceLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\TimePriceLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutAbstract;
use PreSale\Logics\v3Estimate\multiResponse\layout\sort_strategy\ChooseSortStrategy;
use PreSale\Logics\v3Estimate\multiResponse\layout\sort_strategy\StrategyClassifySort;
use PreSale\Logics\v3Estimate\multiResponse\layout\sort_strategy\StrategyDefaultSort;
use PreSale\Logics\v3Estimate\multiResponse\layout\sort_strategy\StrategyPriceSort;
use PreSale\Logics\v3Estimate\multiResponse\layout\ThemeData;
use PreSale\Logics\v3Estimate\multiResponse\Util;

/**
 * Class NormalLayoutBuilder
 */
class NormalLayoutBuilder extends LayoutBuilder
{
    /**
     * @param array         $aBizProductMap $aBizProductMap
     * @param BizCommonInfo $oBizCommonInfo $oBizCommonInfo
     * @param  array        $oBusInfo       $oBusInfo
     * @return void
     */
    public function __construct(array $aBizProductMap, $oBizCommonInfo, $oBusInfo) {
        parent::__construct($aBizProductMap, $oBizCommonInfo, $oBusInfo);
    }

    /**
     * @return array
     */
    public function buildLayout() {
        ThemeData::getInstance()->Init($this->_oBizCommonInfo);
        $oLayoutBuilders = $this->_getAllSortedLayoutBuilders();

        // 排序策略
        $aAbData   = ChooseSortStrategy::buildAbData($this->_oBizCommonInfo);
        $oStrategy = null;
        if ($aAbData->IsHitPriceSortExp()) {
            $oStrategy = new StrategyPriceSort($this->_aBizProductMap, $this->_oBizCommonInfo);
            FeatureRecord::getInstance()->RecordFeature(FeatureRecord::FEATURE_PRICE_SORT_STRATEGY);
        } else {
            $oStrategy = new StrategyDefaultSort($this->_aBizProductMap, $this->_oBizCommonInfo);
            FeatureRecord::getInstance()->RecordFeature(FeatureRecord::FEATURE_DEFAULT_SORT_STRATEGY);
        }

        return $oStrategy->buildLayout($oLayoutBuilders, $aAbData);
    }


    /**
     * @return LayoutAbstract[]
     */
    private function _getAllSortedLayoutBuilders() {
        $aLayoutList = [
            $this->_getNormalBargainLayoutBuilder(),
            $this->_getFarMustCheaperBoxBuilder(),
            $this->_getChaoZhiDaBoxBuilder(),
            $this->_getSurpriseDiscountBoxBuilder(),
            $this->_getTaxiPricingBoxBuilder(),
            $this->_getUniTaxiBoxBuilder(),
            $this->_getXDiscountBoxBuilder(),
            $this->_getShortDistanceCarAllianceLayoutBuilder(),
            $this->_getSpaciousCarLayoutBuilder(),
            $this->_getMiniBusLayoutBuilder(),
            $this->_getDiscountAllianceBuilder(),
        ];

        // 导流位builders
        $guideLayoutBuilders = $this->_getGuideLayoutBuilders();

        // 香港三方盒子
        $aHKThirdBoxBuilders = $this->_getHongKongThirdBoxBuilders();

        $aLayoutList = array_merge($aLayoutList, $aHKThirdBoxBuilders, $guideLayoutBuilders);

        $oLayoutBuilders = [];
        foreach ($aLayoutList as $aLayoutBuilder) {
            if (!empty($aLayoutBuilder)) {
                $oLayoutBuilders[] = $aLayoutBuilder;
            }
        }

        foreach ($this->_aSortedPcIdMap as $iPCId => $aStatus) {
            if (!array_key_exists($iPCId, $this->_aBizProductMap)) {
                continue;
            }

            if (BargainRangeLayout::PRODUCT_CATEGORY_BARGAIN_RANGE == $iPCId && !Util::bBargainRangeCheckable($this->_oBizCommonInfo->oCommonInfo)) {
                $oSingleLayoutBuilder = new BargainRangeLayout($iPCId, $this->_oBizCommonInfo, $this->_aBizProductMap, $this->_aProductIdPosition);
            } else {
                $oSingleLayoutBuilder = new SingleCarLayout($iPCId, $this->_aBizProductMap, $this->_aProductIdPosition, $this->_aPcIDToSelectType, $this->_oBizCommonInfo);
            }

            $oLayoutBuilders[] = $oSingleLayoutBuilder;
        }

        return $oLayoutBuilders;
    }
}
