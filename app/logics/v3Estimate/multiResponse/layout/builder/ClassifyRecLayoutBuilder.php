<?php

namespace PreSale\Logics\v3Estimate\multiResponse\layout\builder;

use PreSale\Logics\v3Estimate\AthenaRecommend;
use PreSale\Logics\v3Estimate\AthenaRecommendRecForm;
use PreSale\Logics\v3Estimate\CategoryInfoLogic;
use PreSale\Logics\v3Estimate\ClassifyFoldLogic;
use PreSale\Logics\v3Estimate\DecisionV2Service;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\GuideCarLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\instance\SingleCarLayout;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutAbstract;
use PreSale\Logics\v3Estimate\multiResponse\layout\LayoutBuilder;
use PreSale\Logics\v3Estimate\multiResponse\layout\sort_strategy\ABParam;
use PreSale\Logics\v3Estimate\multiResponse\layout\sort_strategy\ChooseSortStrategy;
use PreSale\Logics\v3Estimate\multiResponse\layout\sort_strategy\StrategyClassifyRecSort;

class ClassifyRecLayoutBuilder extends LayoutBuilder
{

    /**
     * @param array         $aBizProductMap $aBizProductMap
     * @param BizCommonInfo $oBizCommonInfo $oBizCommonInfo
     * @param  array         $oBusInfo       $oBusInfo
     * @return void
     */
    public function __construct(array $aBizProductMap, $oBizCommonInfo, $oBusInfo) {
        $recPCIds          = AthenaRecommendRecForm::getInstance()->getRecPCIds();
        $recSubGroupIds    = AthenaRecommendRecForm::getInstance()->getRecSubGroupIds();
        $bizProductMapKeys = array_keys($aBizProductMap);
        foreach ($recSubGroupIds as $subGroupId) {
            $aPcIdConfList = DecisionV2Service::getInstance()->getProductsListByGroupID($subGroupId);

            if (!empty($aPcIdConfList)) {
                $recPCIds = array_merge($recPCIds, array_intersect($aPcIdConfList, $bizProductMapKeys));
            }
        }

        $result = array_filter(
            $aBizProductMap,
            function ($key) use ($recPCIds) {
                return in_array($key, $recPCIds);
            },
            ARRAY_FILTER_USE_KEY
        );

        parent::__construct($result, $oBizCommonInfo, $oBusInfo);
    }

    /**
     * @inheritDoc
     */
    public function buildLayout() {
        if (!AthenaRecommendRecForm::getInstance()->isRecForm($this->_oBizCommonInfo)) {
            return [];
        }

        $oLayoutBuilders = $this->_getAllSortedLayoutBuilders();

        // 排序策略
        $oAthenaSortParam = AthenaRecommendRecForm::getInstance()->getProductSortInfo();
        $oStrategyParam   = new ABParam();
        if (!empty($oAthenaSortParam)) {
            $oStrategyParam->setBHitPriceSortExp($oAthenaSortParam['sort_type'] ?? 0);
            $oStrategyParam->setIRankPrice($oAthenaSortParam['rank_sort'] ?? 0);
            $oStrategyParam->setBDefaultSelectUp($oAthenaSortParam['default_select_up'] ?? 0);
        }

        return (new StrategyClassifyRecSort($this->_aBizProductMap, $this->_oBizCommonInfo,$oStrategyParam))->buildLayout($oLayoutBuilders, $oStrategyParam);
    }

    /**
     * 7.0流量盒子只存在远比省/一车双价/流量盒子
     * @return LayoutAbstract[]
     */
    private function _getAllSortedLayoutBuilders() {
        // 特殊车型/盒子builders
        $aLayoutList = [
            $this->_getNormalBargainLayoutBuilder(),
            $this->_getBargainRangeLayoutBuilder(),
            $this->_getSpaciousCarLayoutBuilder(),
            $this->_getMiniBusLayoutBuilder(),

            $this->_getFarMustCheaperBoxBuilder(),
            $this->_getChaoZhiDaBoxBuilder(),
            $this->_getSurpriseDiscountBoxBuilder(),
            $this->_getTaxiPricingBoxBuilder(),
            $this->_getUniTaxiBoxBuilder(),

            $this->_getXDiscountBoxBuilder(),
            $this->_getShortDistanceCarAllianceLayoutBuilder(),
            $this->_getDiscountAllianceBuilder(),
        ];

        // 香港三方盒子
        $aHKThirdBoxBuilders = $this->_getHongKongThirdBoxBuilders();

        // 导流位builders
        $guideLayoutBuilders = $this->_getGuideLayoutBuilders();
        // 流量盒子builders
        $flowFoldBoxBuilders = $this->_getFlowFoldBoxBuilders(true);

        $aLayoutList = array_merge(
            $guideLayoutBuilders,
            $aLayoutList,
            $aHKThirdBoxBuilders,
            $flowFoldBoxBuilders,
            $this->_getFinalSingleCarBuilders()
        );

        return array_filter($aLayoutList);
    }
}
