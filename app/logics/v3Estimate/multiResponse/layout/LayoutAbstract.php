<?php

namespace PreSale\Logics\v3Estimate\multiResponse\layout;

use BizLib\Config as NuwaConfig;
use BizLib\Utils\Language;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\newFormTab\Tab;
use PreSale\Logics\v3Estimate\AthenaRecommend;
use PreSale\Logics\v3Estimate\AthenaRecommendRecForm;
use PreSale\Logics\v3Estimate\BizProduct;
use PreSale\Logics\v3Estimate\ExperimentLogic;
use PreSale\Logics\v3Estimate\FormAB\NewStyleFormABParam;
use PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo;

/**
 * Layout
 */
abstract class LayoutAbstract
{
    const SINGLE_GROUP_TYPE         = 1;  // 普通单车型
    const LINK_GROUP_TYPE       = 1;  // 下挂展示：车大、甄选快车
    const SHORT_DISTANCE_GROUP_TYPE = 2;  // 三方盒子 / 三方主端二期特惠联盟
    const TAXI_GROUP_TYPE           = 3;  // 出租车盒子
//    const PEAK_GROUP_TYPE           = 4;  // 高峰盒子
//    const TP_GROUP_TYPE             = 5;  // TP盒子
    const TAXI_PRICING_BOX_GROUP_TYPE     = 6;  // 出租车计价盒子
    const FAR_MUST_CHEAPER_BOX_GROUP_TYPE = 7;  // 远必省盒子 / X折特价车
    const MINIBUS_BOX_TYPE    = 8;  // 可置灰单盒子
    const GUIDE_GROUP_TYPE    = 99; // 导流
    const GUIDE_GROUP_TYPE_2  = 100; // 带主题的导流
    const BARGAIN_GROUP_TYPE  = 101; // 司乘议价：砍价+呼叫按钮
    const SIMPLE_CARPOOL_TYPE = 102; // 简化表单：拼车卡片
    const SIMPLE_COMMON_TYPE  = 103; // 简化表单：非拼车卡片

    const TOP_AREA       = 1; // 置顶区域 from_show_type
    const RECOMMEND_AREA = 2; // 推荐区域
    const OTHER_AREA     = 3; // 更多车型区域

    const CHECK   = 1;    //勾选
    const UNCHECK = 0;  //不勾选

    const RANK_PRICE_DEFAULT = 0;
    const RANK_PRICE_MAX     = 1;
    const RANK_PRICE_MIN     = 2;

    const PERSONAL_RECOMMEND_CAR_SELECT = 'base.user_decision_gulfstream_vehicles_combination';
    public static $aKeyList = [self::PERSONAL_RECOMMEND_CAR_SELECT];
    protected $bRecLayOut = false;


    //layout节点action_type枚举
    const LAYOUT_GROUPS_ACTION_TYPE_NONE      = 0; // 默认
    const LAYOUT_GROUPS_ACTION_TYPE_JUMP_TYPE = 1; // 跳tab
    const LAYOUT_GROUPS_ACTION_TYPE_ORDER     = 2; // 发单
    const LAYOUT_GROUPS_ACTION_TYPE_REFRESH   = 3; // 刷新预估


    const CAR_ICON_TYPE_NORMAL = 0;     // 默认
    const CAR_ICON_TYPE_LAYOUT_NEW = 2; // 盒子新样式

    /**
     * @return mixed
     */
    abstract public function buildGroups();

    /**
     * @return mixed
     */
    abstract public function buildFormShowType();

    /**
     * @return mixed
     */
    abstract public function buildTheme();

    /**
     * @return int
     */
    abstract public function buildCategoryId();

    abstract public function buildSubCategoryId();

    /**
     * @param $iRecPos $iRecPos 推荐区位置
     * @return int
     */
    public static function athenaRecommendAreaToFormShowType($iRecPos) {

        if (AthenaRecommend::REC_POS_RECOMMEND_AREA == $iRecPos) {
            return self::RECOMMEND_AREA;
        }

        if (AthenaRecommend::REC_POS_TOP_AREA == $iRecPos
            || AthenaRecommend::REC_POS_TOP_AREA_NEW == $iRecPos
        ) {
            return self::TOP_AREA;
        }

        return self::OTHER_AREA;
    }

    /**
     * @param BizProduct $oProduct product
     * @return mixed
     */
    public static function getRedPacketFee($oProduct) {
        $aDisplayLine = $oProduct->getBillInfo()->getDisplayLines();

        foreach ($aDisplayLine as $oItem) {
            if ('red_packet' == $oItem->getName() && $oItem->getValue() > 0) {
                return $oItem->getValue();
            }
        }

        return null;
    }

    /**
     * @param string $sType 类型 0:单车型  2:聚合车型(三方,出租车)  3:高峰期盒子
     * @param string $sKey  type为单车型：product_category 聚合车型：subgroup_id 盒子：box_id
     * @return string
     */
    public static function buildGroupId($sType, $sKey) {
        return sprintf('%d_%s', $sType, $sKey);
    }

    /**
     * 构建权重
     * @param BizCommonInfo $oBizCommonInfo $oBizCommonInfo 各种参数信息
     * @return int
     */
    public static function buildBubbleRank($oBizCommonInfo) {

        $oApolloAB = Apollo::getInstance()->featureToggle(
            'xcx_bubble_recommendation_2',
            [
                'key'           => $oBizCommonInfo->getPassengerUID(),
                'phone'         => $oBizCommonInfo->getPassengerPhone(),
                'city'          => $oBizCommonInfo->getFromCity(),
                'pid'           => $oBizCommonInfo->getPassengerID(),
                'access_key_id' => $oBizCommonInfo->getAccessKeyID(),
                'app_version'   => $oBizCommonInfo->getAppVersion(),
                'lang'          => $oBizCommonInfo->getAppLanguage(),
            ]
        );
        if ($oApolloAB->allow() && 0 == $oApolloAB->getAllParameters()['selected_up']) {
            return false;
        }

        return true;
    }

    /**
     * @param $oBizCommonInfo
     * @return bool
     */
    protected function IsBoxSubTitleShowPrice($oBizCommonInfo) {
        return Apollo::getInstance()->featureToggle(
            'box_sub_title_show_price',
            [
                'key'           => $oBizCommonInfo->getPassengerUID(),
                'phone'         => $oBizCommonInfo->getPassengerPhone(),
                'city'          => $oBizCommonInfo->getFromCity(),
                'pid'           => $oBizCommonInfo->getPassengerID(),
                'access_key_id' => $oBizCommonInfo->getAccessKeyID(),
                'app_version'   => $oBizCommonInfo->getAppVersion(),
                'lang'          => $oBizCommonInfo->getAppLanguage(),
            ]
        )->allow();
    }

    /**
     * @params array $aAthenaTagContent
     * @param BizCommonInfo $oBizCommonInfo $oBizCommonInfo
     * @return array
     */
    protected function buildGroupRecData($aAthenaTagContent,$oBizCommonInfo) {
        if (!$this->bRecLayOut) {
            return null;
        }
        if (empty($aAthenaTagContent)) {
            return null;
        }
        $aRecData = [];
        $aDcmpText = NuwaConfig::text('estimate_form_v3', 'rec_form_tag');
        if (!empty($aAthenaTagContent['sub_tag_content']) && mb_strlen($aAthenaTagContent['sub_tag_content'], 'UTF-8') <= 6) {
            $aRecData['rec_tag'] = $this->_buildRecTag($aDcmpText['sub_tag'], $aAthenaTagContent['sub_tag_content']);
        }

        if (!NewStyleFormABParam::getInstance($oBizCommonInfo->getApolloParams(null)) ->isHideRecRightTag()) {
            $aRecData['rec_right_tag'] = $this->_buildRecTag($aDcmpText['right_tag'], $aAthenaTagContent['right_tag_content']);
        }
        $aRecData['rec_bg_color'] = $aDcmpText['rec_bg_color'];
        return $aRecData;
    }

    /**
     * @params array $aSubTag
     * @params string $content
     * @return array
     */
    private function _buildRecTag($aSubTag, $content) {
        if (empty($content)){
            return null;
        }
        return [
            'content'      => $content,
            'border_color' => $aSubTag['border_color'],
            'bg_gradients' => $aSubTag['bg_gradients'],
            'font_color'   => $aSubTag['font_color'],
            'icon_url'     => $aSubTag['icon_url'],
            'highlight_color' => $aSubTag['highlight_color'],
        ];
    }

    public function setRecLayout($bIsRecLayout) {
        $this->bRecLayOut = $bIsRecLayout;
    }

    /**
     * @param BizCommonInfo $oBizCommonInfo $oBizCommonInfo
     * @param array $aConfig  $aConfig
     * @return string
     */
    protected function _formatFeeMsgTemplate($oBizCommonInfo, $aConfig)
    {
        if (!NewStyleFormABParam::getInstance($oBizCommonInfo->getApolloParams(null))->getHitNewStyleForm()) {
            return '';
        }

       return  $aConfig['fee_msg_template'] ?? '预估{￥}%s';
    }

    /**
     * @param int  $iPageType $iPageType
     * @param string $sTabId $sTabId
     * @param array $aPcIDList $aPcIDList
     * @return int
     */
    protected function _buildCarIconType($iPageType, $sTabId, $aPcIDList) {
        // 7.0 车型icon规范
        // http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=1244134390
        // 条件：7.0表单且多车型
        // 非7.0
        if (empty($sTabId) || !Tab::isHitClassifyTabByTabId($sTabId)) {
            return self::CAR_ICON_TYPE_NORMAL;
        }

        // 非主表单
        if (0 != $iPageType) {
            return self::CAR_ICON_TYPE_NORMAL;
        }

        return self::CAR_ICON_TYPE_LAYOUT_NEW;
    }
}

