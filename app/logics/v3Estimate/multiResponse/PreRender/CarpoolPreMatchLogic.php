<?php

namespace PreSale\Logics\v3Estimate\multiResponse\PreRender;

use BizLib\Client\UfsClient;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Log as NuwaLog;
use Disf\SPL\Trace;

use BizCommon\Models\Service\custom\LikeWait;

use PreSale\Domain\Model\UIComponent\Feature\EstimateFeature;
use PreSale\Models\order\OrderEstimateNew;

/**
 * Class CarpoolPreMatchLogic
 */
class CarpoolPreMatchLogic
{

    /**
     * @var CarpoolPreMatchLogic
     */
    private static $_oInstance = null;

    private $_aCarpoolEts = []; // ETS，预计拼成时间

    private $_aMiniBusInfo = []; //小巴信息
    /**
     * @return CarpoolPreMatchLogic
     */
    public static function getInstance() {
        if (null === self::$_oInstance) {
            $kls = __CLASS__;
            self::$_oInstance = new $kls();
        }

        return self::$_oInstance;
    }

    /**
     * CarpoolPreMatchLogic constructor.
     */
    private function __construct() {
    }

    /**
     * @param \PreSale\Logics\v3Estimate\BizProduct[]               $aBizProductMap $aBizProductMap
     * @param \PreSale\Logics\v3Estimate\multiRequest\BizCommonInfo $oBizCommonInfo oBizCommonInfo
     * @return void
     */
    public function loadCarpoolPreMatchInfoV3($aBizProductMap, $oBizCommonInfo) {

        // 原始筛选条件: app/logics/estimatePriceV2/response/SceneResponseLogicV2.php
        // is_carpool_open 赋值在price-api, 只要combo_type==4, 在调用账单后都会有值
        $aBizProductMap = array_values(
            array_filter(
                $aBizProductMap,
                function ($oBizProduct) {
                    $iComboType = $oBizProduct->getOrderInfo()->iComboType;
                    return \BizLib\Constants\OrderSystem::TYPE_COMBO_CARPOOL == $iComboType;
                }
            )
        );

        if (empty($aBizProductMap)) {
            return;
        }

        $aProductList = array_map(
            function ($oBizProduct) {
                return [
                    'product_id'    => $oBizProduct->getProductID(),
                    'combo_type'    => $oBizProduct->getOrderInfo()->iComboType,
                    'require_level' => $oBizProduct->getOrderInfo()->iRequireLevel,
                    'order_n_tuple' => $this->__asmNTuple($oBizProduct),
                ];
            },
            $aBizProductMap
        );

        $aPreMatchReq = [
            'phone'          => $oBizCommonInfo->getPassengerPhone(),
            'pid'            => (string)($oBizCommonInfo->getPassengerID()),
            'city_id'        => $oBizCommonInfo->getFromCity(),
            'cur_lng'        => $oBizCommonInfo->getAreaInfo()->fCurLng,
            'cur_lat'        => $oBizCommonInfo->getAreaInfo()->fCurLat,
            'start_lng'      => $oBizCommonInfo->getAreaInfo()->fFromLng,
            'start_lat'      => $oBizCommonInfo->getAreaInfo()->fFromLat,
            'start_name'     => $oBizCommonInfo->getAreaInfo()->sFromName,
            'dest_lng'       => $oBizCommonInfo->getAreaInfo()->fToLng,
            'dest_lat'       => $oBizCommonInfo->getAreaInfo()->fToLat,
            'dest_name'      => $oBizCommonInfo->getAreaInfo()->sToName,
            'traceid'        => Trace::traceId(),
            'appversion'     => $oBizCommonInfo->getAppVersion(),
            'is_from_webapp' => $oBizCommonInfo->getClientInfo()->bIsFromWebApp,
            'productReq'     => $aProductList,
            'extMap'         => [
                'imei' => $oBizCommonInfo->getClientInfo()->sImei,
            ],
        ];

        //这里返回的并不只是愿等的数据，拼车预匹配的数据都通过这个接口返回，后续把这个方法重新封装到预匹配的类里面 @chenfeiyu
        $aPreMatchResp = LikeWait::getInstance()->getMatchHoldInfoAndRecommendStation($aPreMatchReq);

        if (empty($aPreMatchResp)) {
            return;
        }

        foreach ($aBizProductMap as $oBizProduct) {
            $key = implode('', [$oBizProduct->getOrderInfo()->iComboType, $oBizProduct->getOrderInfo()->iCarpoolType, $oBizProduct->getProductID()]);
            if (isset($aPreMatchResp['holdResults'][$key])) {
                // 写UFS
                EstimateFeature::getInstance()->buildLikeWait(
                    $oBizProduct->getProductID(),
                    $oBizProduct->getOrderInfo()->iRequireLevel,
                    $oBizProduct->getOrderInfo()->iCarpoolType,
                    $aPreMatchResp['holdResults'][$key],
                    $oBizCommonInfo->getPassengerID()
                );
            }

            // 写redis缓存
            if (!empty($aPreMatchResp['extResults'][$key])) {
                $this->_cacheExtInfo($oBizProduct->getEstimateID(), $aPreMatchResp['extResults'][$key]);
            }
        }

        if (isset($aPreMatchResp['carpoolEts']) && !empty($aPreMatchResp['carpoolEts'])) {
            $this->_aCarpoolEts = $aPreMatchResp['carpoolEts'];
        }

        if (isset($aPreMatchResp['miniBusRes']) && !empty($aPreMatchResp['miniBusRes'])) {
            $this->_aMiniBusInfo = $aPreMatchResp['miniBusRes'];
            foreach ($aBizProductMap as $oBizProduct) {
                $miniBusInfo = $this->getMiniBusInfo($oBizProduct);
                if (empty($miniBusInfo)) {
                    continue;
                }

                $sToken = $miniBusInfo['mapinfo_cache_token'];
                if (!empty($miniBusInfo['mapinfo_start_cache_token']) && !empty($miniBusInfo['mapinfo_dest_cache_token'])) {
                    $sToken = $miniBusInfo['mapinfo_start_cache_token'] . '-' .  $miniBusInfo['mapinfo_dest_cache_token'];
                }

                if (!empty($sToken)) {
                    $this->_cacheMiniBusMapTokenInfo($oBizProduct->getEstimateID(),$sToken);
                    $this->_cacheMiniBusInfo(
                        $oBizProduct->getEstimateID(),
                        [
                            'mapinfo_cache_token' => $sToken,
                            'etp'                 => $miniBusInfo['etp_info']['etp_time_duration'],
                        ]
                    );
                }
                // 存储小巴预匹配etp、extMap信息
                $aMiniBusInfo = [
                    'etp'    => $miniBusInfo['etp_info']['etp_time_duration'],
                ];
                if (!empty($miniBusInfo['extMap'])) {
                    $aMiniBusInfo['extMap'] = $miniBusInfo['extMap'];
                }
                $this->_setUfsMiniBusInfo($oBizProduct->getEstimateID(), $aMiniBusInfo);
            }
        }

        return;
    }

    /**
     * @param \PreSale\Logics\v3Estimate\BizProduct $oBizProduct $oBizProduct
     * @return array
     */
    private function __asmNTuple($oBizProduct) {
        return array_merge(
            // 前面的为必须字段
            [
                'product_id'    => $oBizProduct->getProductID(),
                'require_level' => $oBizProduct->getOrderInfo()->iRequireLevel,
                'combo_type'    => $oBizProduct->getOrderInfo()->iComboType,
            ],
            array_filter(
                // 需要保留key
                [
                    'carpool_price_type'    => $oBizProduct->getOrderInfo()->iCarpoolPriceType,
                    'carpool_type'          => $oBizProduct->getOrderInfo()->iCarpoolType,
                    'route_type'            => $oBizProduct->getOrderInfo()->iRouteType,
                    'order_type'            => $oBizProduct->getOrderInfo()->iOrderType,
                    'menu_id'               => $oBizProduct->getOrderInfo()->sMenuID,
                    // 'invitation_type'       => $oBizProduct->getOrderInfo()->in,
                    'is_dual_carpool_price' => $oBizProduct->getOrderInfo()->bIsDualCarpoolPrice,
                    'departure_range'       => $oBizProduct->getOrderInfo()->sDepartureRange,
                    'level_type'            => $oBizProduct->getOrderInfo()->iLevelType,
                ],
                function ($v) {
                    // 过滤空值
                    return !empty($v);
                }
            )
        );
    }

    /**
     * @param \PreSale\Logics\v3Estimate\BizProduct $oBizProduct $oBizProduct
     * @return array
     */
    public function getCarpoolEts($oBizProduct) {
        if (empty($this->_aCarpoolEts)) {
            return [];
        }

        $iComboType   = $oBizProduct->getOrderInfo()->iComboType;
        $sCarLevel    = $oBizProduct->getOrderInfo()->iRequireLevel;
        $iProductId   = $oBizProduct->getProductID();
        $iCarpoolType = $oBizProduct->getOrderInfo()->iCarpoolType;

        foreach ($this->_aCarpoolEts as $ets) {
            if ($ets['product_info']['combo_type'] == $iComboType
                && $ets['product_info']['require_level'] == $sCarLevel
                && $ets['product_info']['product_id'] == $iProductId
                && $ets['product_info']['order_n_tuple']['carpool_type'] == $iCarpoolType
            ) {
                return $ets;
            }
        }

        return [];
    }

    /**
     * @param \PreSale\Logics\v3Estimate\BizProduct $oBizProduct $oBizProduct
     * @param array $aProductInfo $aProductInfo
     * @return bool
     */
    public function isHit($oBizProduct, $aProductInfo) {
        $iComboType   = $oBizProduct->getOrderInfo()->iComboType;
        $sCarLevel    = $oBizProduct->getOrderInfo()->iRequireLevel;
        $iProductId   = $oBizProduct->getProductID();
        $iCarpoolType = $oBizProduct->getOrderInfo()->iCarpoolType;
        $iLevelType   = $oBizProduct->getOrderInfo()->iLevelType;
        if ($aProductInfo['combo_type'] == $iComboType
            && $aProductInfo['require_level'] == $sCarLevel
            && $aProductInfo['product_id'] == $iProductId
            && $aProductInfo['order_n_tuple']['carpool_type'] == $iCarpoolType
            && $aProductInfo['order_n_tuple']['level_type'] == $iLevelType
        ) {
            return true;
        }

        return false;
    }

    /**
     * @param \PreSale\Logics\v3Estimate\BizProduct $oBizProduct $oBizProduct
     * @return array
     */
    public function getMiniBusInfo($oBizProduct) {
        if (empty($this->_aMiniBusInfo)) {
            return [];
        }

        foreach ($this->_aMiniBusInfo as $item) {
            if ($this->isHit($oBizProduct, $item['product_info'])) {
                return $item;
            }
        }

        return [];
    }

    /**
     * @param string $sEstimateId sEstimateId
     * @param array  $aExtInfo    ExtInfo
     * @return void
     */
    private function _cacheExtInfo($sEstimateId, $aExtInfo) {
        $oOrderEstimateNew = new OrderEstimateNew();
        //缓存extInfo
        $oOrderEstimateNew->cacheExtResult($sEstimateId, $aExtInfo);
    }

    /**
     * @param string $sEstimateId      sEstimateId
     * @param string $aMiniBusMapToken aMiniBusMapToken
     * @return void
     */
    private function _cacheMiniBusMapTokenInfo($sEstimateId, $aMiniBusMapToken) {
        $oOrderEstimateNew = new OrderEstimateNew();
        //缓存extInfo
        $oOrderEstimateNew->cacheMiniBusTokenResult($sEstimateId, $aMiniBusMapToken);
    }

     /**
     * @param string $sEstimateId  sEstimateId
     * @param array  $aMiniBusInfo aMiniBusInfo
     * @return void
     */
    private function _cacheMiniBusInfo($sEstimateId, $aMiniBusInfo) {
        $oOrderEstimateNew = new OrderEstimateNew();
        //缓存小巴信息： etp、 地图token
        $oOrderEstimateNew->cacheMiniBusInfoResult($sEstimateId, $aMiniBusInfo);
    }

    /**
     * @param string $sEstimateId  sEstimateId
     * @param array  $aMiniBusInfo aMiniBusInfo
     * @return void
     */
    private function _setUfsMiniBusInfo($sEstimateId, $aMiniBusInfo) {
        $sMinibusInfo = json_encode($aMiniBusInfo, true);
        $aFeature    = ['carpool.minibus_prematch_info' => $sMinibusInfo];
        $aConditions = ['estimate_id' => $sEstimateId];
        $aRes        = (new UfsClient())->setFeature($aFeature, $aConditions, 'passenger');
        if (UfsClient::SUCCESS_CODE !== $aRes['errno']) {
            NuwaLog::warning(
                Msg::formatArray(
                    Code::E_COMMON_SET_UFS_FAIL,
                    ['featureValues' => $aFeature, 'condition' => $aConditions, 'setUfsRes' => $aRes]
                )
            );
        }
    }
}
