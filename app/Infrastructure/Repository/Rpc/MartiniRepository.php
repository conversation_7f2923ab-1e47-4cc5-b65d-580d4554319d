<?php

namespace PreSale\Infrastructure\Repository\Rpc;

use BizLib\Client\MartiniProxyClient;
use BizLib\Log as NuwaLog;

/**
 * Class MartiniRepository
 */
class MartiniRepository
{

    private $_oMartiniClient;

    /**
     * @return void
     */
    public function __construct() {
        $this->_oMartiniClient = new MartiniProxyClient();
    }

    /** 查询快车的排队信息，目前只返回是否排队和排队位次
     * @param  array $aOrderInfo 订单信息
     * @return array
     */
    public function getFastCarQueueInfo($aOrderInfo) {

        $aRet = $this->_oMartiniClient->GetQueueLenForDP($aOrderInfo['area'], $aOrderInfo['starting_lat'], $aOrderInfo['starting_lng'], [MartiniProxyClient::QUEUE_TYPE_FASTCAR], 'transaction');

        if (0 != $aRet['errno']) {
            NuwaLog::warning(sprintf('查询快车排队信息失败,城市Id=%,%', $aOrderInfo['area'], $aRet['errmsg']));
            return [];
        }

        if (0 == count($aRet) || empty($aRet['data']) || empty($aRet['data'][1])) {
            return [];
        }

        return [
            'isInLine' => $aRet['data'][1]['open_flag'],
            'rank'     => $aRet['data'][1]['queue_len'],
        ];
    }

    /**
     * 是否快车排队
     * @param array $aOrderInfo orderInfo
     * @return bool
     */
    public function checkFastCarIsLineUp($aOrderInfo) {
        $oMartiniRepository = new MartiniRepository();
        $aLineInfo          = $oMartiniRepository->getFastCarQueueInfo($aOrderInfo);
        return 1 == $aLineInfo['isInLine'];
    }
}
