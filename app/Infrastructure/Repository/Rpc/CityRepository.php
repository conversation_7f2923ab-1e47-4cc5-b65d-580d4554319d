<?php

/**
 * @brief
 *
 * <AUTHOR>
 * @date 2018-08-28
 */

namespace PreSale\Infrastructure\Repository\Rpc;

use BizLib\Utils\MapHelper;

class CityRepository extends BaseContainer
{
    public function __construct($cityInfo) {
        parent::__construct($cityInfo);
    }

    public static function buildByLoc($lat, $lng) {
        $cityInfo = MapHelper::getAreaInfoByLoc($lng, $lat);

        if (empty($cityInfo)) {
            return false;
        } else {
            return new self($cityInfo);
        }
    }
}
