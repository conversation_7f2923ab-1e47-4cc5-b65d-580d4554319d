<?php

/**
 * @brief
 *
 * <AUTHOR>
 * @date 2018-09-25
 */

namespace PreSale\Infrastructure\Repository\Rpc;

use BizLib\Client\DosClient;
use BizLib\Log as NuwaLog;
use BizCommon\Logics\Order\LogicOrder;
use BizLib\Client\OrderSystemClient;
use PreSale\Infrastructure\Repository\Translator\OrderTranslator;

class OrderDosRepository
{
    public static function getOrder($iOrderId, $sDistrict, $aOrderFields = array(), $iFromDb = 0, $iConsistency = 0) {
        $orderInfo = LogicOrder::getOrderInfo($iOrderId, $sDistrict, $aOrderFields, $iFromDb, $iConsistency);

        if (GLOBAL_SUCCESS == $orderInfo['errno']) {
            return OrderTranslator::translateToOrder($orderInfo['order_info']);
        } else {
            return false;
        }
    }

    public static function getHistoryOrderListByDriverId($sDriverId, $iStartTime, $iEndTime, $aWhereEqual, $iOffset = 0, $iPageSize = 10) {
        if ($iPageSize <= 0 || $iOffset < 0) {
            return [];
        }

        if (empty($iEndTime)) {
            $iEndTime = time() + 86400 * 30; // 往后推30天
        }

        if (empty($iStartTime)) {
            $iStartTime = $iEndTime - 31104000; // 最近12个月的订单
        }

        if ($iStartTime > $iEndTime) {
            NuwaLog::warning(
                'params error|errno:' . D_ERRNO_PARAMS_ERROR . '|errmsg:D_ERRNO_PARAMS_ERROR' .
                '|src/rpc/OrderSyste,/getDriverHistoryOrder' . '|starttime:' . $iStartTime . ',endtime:' . $iEndTime
            );

            return [];
        }

        $aWhereBetween   = array(
            'departure_time' => array(
                date('Y-m-d H:i:s', $iStartTime),
                date('Y-m-d H:i:s', $iEndTime),
            ),
        );
        $aLimit          = array(
            'offset' => $iOffset,
            'size'   => $iPageSize,
        );
        $oOrderSystemRpc = new OrderSystemClient();
        $aFields         = $aExtraCondition = $aSort = [];
        $aReturn         = $oOrderSystemRpc->getDriverHistoryOrderInfoV1($sDriverId, $aFields, $aWhereBetween, $aWhereEqual, $aExtraCondition, $aSort, $aLimit);
        $aOrderList      = [];
        if (isset($aReturn['errno']) && 0 == $aReturn['errno'] && isset($aReturn['result'])) {
            $aResult = $aReturn['result'];
            if (isset($aResult['errno']) && 0 == $aResult['errno'] && isset($aResult['data'])) {
                $aOrderList = $aResult['data'];
            }
        }

        if ($aOrderList) {
            $aStatus = $aType = $aVolume = [];
            foreach ($aOrderList as $k => $value) {
                $aVolume[$k] = $value['departure_time'];
                $aStatus[$k] = $value['order_status'];
                $aType[$k]   = $value['type'];
            }

            array_multisort($aStatus, SORT_DESC, $aType, SORT_ASC, $aVolume, SORT_DESC, $aOrderList);
            $aOrderList = array(array_shift($aOrderList));
        }

        return $aOrderList;
    }

    /**
     * @param string $sDriverId     司机ID
     * @param array  $aFields       字段
     * @param array  $aWhereBetween 限制条件
     * @param array  $aWhereEqual   条件
     * @param int    $iOffset       起始offset
     * @param int    $iPageSize     录数
     * @return array|mixed
     */
    public static function getOrderListByDriverId($sDriverId, $aFields, $aWhereBetween, $aWhereEqual, $iOffset = 0, $iPageSize = 10) {
        if ($iPageSize <= 0 || $iOffset < 0) {
            return [];
        }

        $aLimit = array(
            'offset' => $iOffset,
            'size'   => $iPageSize,
        );

        $oOrderSystemRpc = new OrderSystemClient();
        $aSort           = $aExtraCondition = [];
        $aReturn         = $oOrderSystemRpc->getDriverHistoryOrderInfoV1($sDriverId, $aFields, $aWhereBetween, $aWhereEqual, $aExtraCondition, $aSort, $aLimit);

        $aOrderList = [];
        if (isset($aReturn['errno']) && 0 == $aReturn['errno'] && isset($aReturn['result'])) {
            $aResult = $aReturn['result'];
            if (isset($aResult['errno']) && 0 == $aResult['errno'] && isset($aResult['data'])) {
                $aOrderList = $aResult['data'];
            }
        }

        return $aOrderList;
    }


    /**
     * 更新订单表中passenger_id
     *  @param array $aOrderInfo     订单详情
     *  @param array $aPassengerInfo 更新乘客信息
     * @return array|mixed
     */
    public function updateOrderPassengerID($aOrderInfo, $aPassengerInfo) {
        if (empty($aOrderInfo['order_id']) || empty($aOrderInfo['district'])  ||empty($aOrderInfo['passenger_id'])) {
            return false;
        }

        if (empty($aPassengerInfo['passenger_id']) || empty($aPassengerInfo['passenger_phone'])) {
            return false;
        }

        $aWhereConditions     = [
            'order_id'     => $aOrderInfo['order_id'],
            'passenger_id' => $aOrderInfo['passenger_id'],
        ];
        $aUpdatePassengerInfo = [
            'passenger_id'    => $aPassengerInfo['passenger_id'],
            'passenger_phone' => $aPassengerInfo['passenger_phone'],
        ];

        $oDosClient = new DosClient();
        $aReturn    = $oDosClient->updatePassengerInfo($aOrderInfo['order_id'], $aOrderInfo['district'], $aUpdatePassengerInfo, $aWhereConditions);
        if (isset($aReturn['errno']) && 0 == $aReturn['errno'] && isset($aReturn['result'])) {
            $aResult = $aReturn['result'];
            if (isset($aResult['errno']) && 0 == $aResult['errno']) {
                return true;
            }
        }

        return false;
    }
}

