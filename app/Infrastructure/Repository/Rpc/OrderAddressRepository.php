<?php

/**
 * @brief
 *
 * <AUTHOR>
 * @date 2018-09-12
 */

namespace PreSale\Infrastructure\Repository\Rpc;

use BizLib\Utils\Address;
use BizLib\Utils\Language;
use BizLib\Constants\Common;
use BizLib\Client\MapPointSysClient;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Log as NuwaLog;

class OrderAddressRepository
{
    const API_ADDR_REWRITE_TIMEOUT = 50;   //地址重写api超时时间，线上建议50ms，单位：ms

    /**
     * @param $addrStart
     * @param $nameStart
     * @param $iArea
     * @param $fStartingLng
     * @param $fStartingLat
     * @param $aFakeOrderInfo
     * @param $is_begin
     * @param $start_poiid
     *
     * @return array|mixed
     */
    public function rewriteAddress($addrStart, $nameStart, $iArea, $fStartingLng, $fStartingLat, $aFakeOrderInfo, $is_begin, $start_poiid) {
        $aRet = Address::rewrite($addrStart, $nameStart, $iArea, $fStartingLng, $fStartingLat, $aFakeOrderInfo, $is_begin, $start_poiid, self::API_ADDR_REWRITE_TIMEOUT);

        return $aRet;
    }

    /**
     * @param $sCanonicalCountryCode
     * @param $iLocalDistrict
     * @param $aPoiId
     * @param $sPhone
     *
     * @return array
     */
    public function getPoiInfo($sCanonicalCountryCode, $iLocalDistrict, $aPoiId, $sPhone) {
        $oMapDIClient = new \BizLib\Client\MapDIClient();
        $oParams      = new \BizLib\Client\GetPoiInfoServiceParams();
        $oParams->setCanonicalCountryCode($sCanonicalCountryCode);
        if (!empty($sPhone)) {
            $oParams->setPhone($sPhone);
        }

        //端上语言和本地语言不一样，需要调用DI翻译
        $aTranslateResult = $oMapDIClient->getPoiInfoService(Language::getLocalLanguage($iLocalDistrict), $aPoiId, 'api', $oParams);

        return $aTranslateResult;
    }

    /**
     * 根据经纬度反解位置信息
     *
     * @param $aParams
     * @return array
     */
    public function reverseGeo($aParams = array()) {
        $aRequestParams = [
            'business_id'   => $aParams['business_id'],
            'app_version'   => $aParams['app_version'],
            'platform_type' => $aParams['platform_type'],
            'lat'           => $aParams['lat'],
            'lng'           => $aParams['lng'],
            'role'          => Common::BIZ_TYPE_PASSENGER,
            'lang'          => Language::getLanguage(),
        ];
        if (isset($aParams['iB2b'])) {
            $aRequestParams['iB2b'] = $aParams['iB2b'];
        }

        $aRet = (new MapPointSysClient())->reversegeo($aRequestParams);
        if (Code::E_SUCCESS != $aRet['errno']
            || Code::E_SUCCESS != $aRet['result']['errno']
            || empty($aRet['result']['rgeo_result'])
        ) {
            NuwaLog::warning(
                Msg::formatArray(
                    Code::E_COMMON_HTTP_GEO_REQUEST_ERROR,
                    [
                        'params' => $aRequestParams,
                        'ret'    => $aRet,
                    ]
                )
            );
            return [];
        }

        return $aRet['result']['rgeo_result'][0]['base_info'];
    }
}
