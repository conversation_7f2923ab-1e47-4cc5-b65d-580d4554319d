<?php
namespace PreSale\Infrastructure\Repository\Rpc;

use BizLib\Client\PaidMemberClient;
use BizLib\ErrCode\Msg;
use BizLib\ErrCode\Code;
use BizLib\Log as NuwaLog;

/**
 *
 * Copyraight (c) 2020 xiaojukeji.com, Inc. All Rights Reserved.
 * @author: wangtuan<PERSON><EMAIL>
 * @date: 2020/10/22 9:18 下午
 * @desc: 付费会员仓储
 * @wiki:
 *
 */
class PaidMemberRepository
{

    /**
     * 冒泡页面会员套餐获取
     * @param array $aParams params
     * @return array|mixed []
     */
    public static function recCalling(array $aParams = []) {
        if (empty($aParams)) {
            return [];
        }

        $aPaidMemberParams = [
            'uid' => $aParams['uid'],
        ];
        $aRet = (new PaidMemberClient())->recCalling($aPaidMemberParams);
        if (0 != $aRet['errno'] || !isset($aRet['data'])) {
            NuwaLog::warning(Msg::formatArray(Code::E_COMMON_HTTP_READ_FAIL, ['req_params' => $aPaidMemberParams, 'ret' => $aRet]));
            return [];
        }

        return true === $aRet['data']['display'] ? $aRet['data'] : [];
    }
}
