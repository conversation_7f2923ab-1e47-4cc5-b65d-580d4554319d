<?php
/**
 * Created by PhpStorm.
 * <AUTHOR> <<EMAIL>>
 * Date: 2021/6/1
 * Time: 4:59 下午
 */

namespace PreSale\Infrastructure\Repository\Rpc;

use BizLib\Client\ChunXiaoTradecenterClient;
use BizLib\Log as NuwaLog;

/**
 * Class CashRepository
 * @package PreSale\Infrastructure\Repository\Rpc
 */
class CashRepository
{
    const ORDER_TYPE_CHUXING = 1;
    /**
     * @param array $aOrderInfo     更新前订单信息
     * @param array $aPassengerInfo 需更新乘客信息
     * @return false
     */
    public static function bindOrderReceipt($aOrderInfo, $aPassengerInfo) {
        if (empty($aOrderInfo['order_id'])
            || empty($aOrderInfo['passenger_id'])
            || empty($aPassengerInfo['passenger_id'])
            || empty($aOrderInfo['product_id'])
        ) {
            return false;
        }

        $aReq = [
            'order_type'           => self::ORDER_TYPE_CHUXING,
            'order_id'             => $aOrderInfo['order_id'],
            'product_id'           => $aOrderInfo['product_id'],
            'virtual_passenger_id' => $aOrderInfo['passenger_id'],
            'passenger_id'         => $aPassengerInfo['passenger_id'],
            'passenger_phone'      => $aPassengerInfo['passenger_phone'],
        ];
        $oChunXiaoTradecenterRpc = new ChunXiaoTradecenterClient();
        $aReturn = $oChunXiaoTradecenterRpc->bindOrderReceipt($aReq);
        if (isset($aReturn['errno']) && 0 == $aReturn['errno']) {
            return true;
        }

        return false;

    }
}
