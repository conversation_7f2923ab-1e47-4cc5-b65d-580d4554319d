<?php

namespace PreSale\Infrastructure\Repository;

use PreSale\Domain\Model\Passenger\PassengerDbRepository;
use PreSale\Infrastructure\Repository\Translator\DbPassengerTranslator;

class PassengerRepository{
    public static function getDbPassenger($iPid) {
        $aPassenger = PassengerDbRepository::getByPid($iPid);

        if (empty($aPassenger)) {
            return false;
        }

        return DbPassengerTranslator::translateToDbPassenger($aPassenger);
    }
}
