<?php

/**
 * @brief
 *
 * <AUTHOR>
 * @date 2018-09-25
 */

namespace PreSale\Infrastructure\Repository\Translator;

use PreSale\Domain\Model\Order;

class OrderTranslator
{
    public static function translateToOrder($aOrderInfo) {
        if (empty($aOrderInfo) || !is_array($aOrderInfo)) {
            return false;
        }

        $oOrder = new Order\Order($aOrderInfo);
        foreach ($aOrderInfo as $key => $value) {
            $oOrder->$key = $value;
        }

        return $oOrder;
    }
}
