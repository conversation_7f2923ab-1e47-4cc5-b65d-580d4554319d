<?php

namespace PreSale\Infrastructure\Repository\Translator;

use PreSale\Domain\Model\Passenger\DbPassenger;

/**
 * 乘客信息转换类 .
 *
 * @brief
 *
 * <AUTHOR>
 * @date 2023-09-15
 */

class DbPassengerTranslator
{
    /**
     * @param array $aDbPassengerInfo 乘客信息
     * @return DbPassenger
     */
    public static function translateToDbPassenger($aDbPassengerInfo) {
        $oDbPassenger = new DbPassenger();

        if (is_array($aDbPassengerInfo)) {
            foreach ($aDbPassengerInfo as $key => $value) {
                $oDbPassenger->$key = $value;
            }
        }

        return $oDbPassenger;
    }
}
