<?php
/**
 *
 * Copyright (c) 2018 xiaojukeji.com, Inc. All Rights Reserved.
 * @Author: wang<PERSON><PERSON><PERSON><PERSON>@didichuxing.com
 * @Date: 2018/12/29 下午4:15
 * @Desc: Ufs仓储
 * @wiki:
 *
 */

namespace PreSale\Infrastructure\Repository\Ufs;

use BizLib\Client\UfsClient;
use BizLib\Constants\Ufs;
use BizLib\ErrCode\Msg;
use BizLib\ErrCode\Code;
use BizLib\Log as NuwaLog;

class UfsRepository
{
    public static function setUfsFeature($aFeatureValues, $aConditions, $sDomain = Ufs::CALLCAR_CONTACT_RIDER_FIRST_DOMIN) {
        $aRes = (new UfsClient())->setFeature($aFeatureValues, $aConditions, $sDomain);
        if (UfsClient::SUCCESS_CODE !== $aRes['errno']) {
            /*
            NuwaLog::warning(
                Msg::formatArray(
                    Code::E_COMMON_SET_UFS_FAIL,
                    ['featureValues' => $aFeatureValues, 'condition' => $aConditions, 'setUfsRes' => $aRes]
                )
            );
            */
            return [];
        }

        return $aRes;
    }

    /**
     * @param array  $aFeatureValues FeatureValues
     * @param array  $aConditions    Conditions
     * @param string $sDomain        domain
     * @return array
     */
    public static function setMultiUfsFeature($aFeatureValues, $aConditions, $sDomain = Ufs::CALLCAR_CONTACT_RIDER_FIRST_DOMIN) {
        $aRes = (new UfsClient())->setMultiFeature($aFeatureValues, $aConditions, $sDomain);
        if (UfsClient::SUCCESS_CODE !== $aRes['errno']) {
            NuwaLog::warning(
                Msg::formatArray(
                    Code::E_COMMON_SET_UFS_FAIL,
                    ['featureValues' => $aFeatureValues, 'condition' => $aConditions, 'setUfsRes' => $aRes]
                )
            );
            return [];
        }

        return $aRes;
    }

    /**
     * @param array $aFeaturesKeyMapGroup aFeatureKeyMapGroup
     * @return array
     */
    public static function getUfsFeatureMulti(array $aFeaturesKeyMapGroup) {
        $aResp = (new UfsClient())->getFeatureMultiV2($aFeaturesKeyMapGroup);
        if (UfsClient::SUCCESS_CODE !== $aResp['errno']) {
            NuwaLog::warning(
                Msg::formatArray(
                    Code::E_COMMON_GET_UFS_FAIL,
                    ['feature_key_map_group' => $aFeaturesKeyMapGroup]
                )
            );
            return [];
        }

        $aRet = [];
        foreach ($aResp['result'] as $sDomain => $aFeatureKeyDataMap) {
            foreach ($aFeatureKeyDataMap as $sFeatureKey => $aFeatureData) {
                $aRet[$sFeatureKey] = $aFeatureData;
            }
        }

        return $aRet;
    }

    /**
     * @param array   $aFeature
     * @param array   $aCondition
     * @param $sDomain
     * @return array
     * @codeCoverageIgnore get方法，考虑到将来使用时而准备，暂时不使用，为避免影响代码覆盖率，先注释下，2019.1.3
     */
    public static function getUfsFeature(array $aFeature, array $aCondition, $sDomain = Ufs::CALLCAR_CONTACT_RIDER_FIRST_DOMIN) {
        $aGetFeatureRes = [];
        $aRes           = (new UfsClient())->getFeature($aFeature, $aCondition, $sDomain);
        if (UfsClient::SUCCESS_CODE != $aRes['errno']) {
            NuwaLog::warning(
                Msg::formatArray(
                    Code::E_COMMON_GET_UFS_FAIL,
                    ['aFeature' => $aFeature, 'aCondition' => $aCondition, 'domain' => $sDomain, 'aGetUfsRes' => $aRes]
                )
            );
            return $aGetFeatureRes;
        }

        foreach ($aFeature as $sFeatureValue) {
            if (false !== $aRes['result'][$sFeatureValue]) {
                $aGetFeatureRes[$sFeatureValue] = $aRes['result'][$sFeatureValue];
            }
        }

        return $aGetFeatureRes;
    }

    public static function mGetUfsRaw($aFeatures) {
        $aMGetUfsRes = [];
        $aRes = (new UfsClient())->mget($aFeatures);
        if (UfsClient::SUCCESS_CODE != $aRes['errno']) {
            NuwaLog::warning(
                Msg::formatArray(
                    Code::E_COMMON_GET_UFS_FAIL,
                    ['aFeatures' => $aFeatures, 'aMGetUfsRes' => $aRes]
                )
            );
            return $aMGetUfsRes;
        }

        foreach ($aFeatures as $sFeature) {
            if (false !== $aRes['result'][$sFeature]) {
                $aMGetUfsRes[$sFeature] = $aRes['result'][$sFeature];
            }
        }

        return $aMGetUfsRes;
    }

    public static function genUfsFeatureKey(string $sDomain, string $sKey, array $aConditions) {
        $sPrefix = empty($sDomain) ? '' : ($sDomain . '.');
        $sParamsJsonStyle = empty($aConditions) ? '{}' : json_encode(array_map('strval', $aConditions));
        return sprintf('%s%s@%s', $sPrefix, $sKey, $sParamsJsonStyle);
    }
}
