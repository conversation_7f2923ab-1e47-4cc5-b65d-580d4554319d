<?php
/**
 * 套餐售卖cache
 * <AUTHOR> <<EMAIL>>
 * @Date: 2021-06-24 17:55
 */

namespace PreSale\Infrastructure\Repository\Redis;

use BizLib\Libraries\RedisDB;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Common;

/**
 * Class RecComboCache
 * @package PreSale\Infrastructure\Repository\Redis
 */
class RecComboCache
{

    const REC_COMBO_CACHE_EXPIRE = 600;

    /**
     * cache rec combo info
     * @param String $sEstimateId sEstimateId
     * @param array  $aCombo      aCombo
     * @return bool
     */
    public static function setRecComboInfo($sEstimateId, $aCombo) {
        if (empty($sEstimateId) || empty($aCombo)) {
            return false;
        }

        $oRedisDb = \PreSale\Logics\redismove\RedisWrapper::getInstance(P_REC_COMBO_CACHE_PREFIX);
        $sKey     = Common::getRedisPrefix(P_REC_COMBO_CACHE_PREFIX) . $sEstimateId;

        $ret = $oRedisDb->setex($sKey, self::REC_COMBO_CACHE_EXPIRE, json_encode($aCombo));
        if (!$ret) {
            NuwaLog::warning(sprintf('cache rec combo fail, key:%s', $sKey));
        }

        return true;
    }


    /**
     * @param String String $sEstimateId sEstimateId
     * @return array
     */
    public static function getRecComboCache($sEstimateId) {
        $sKey  = Common::getRedisPrefix(P_REC_COMBO_CACHE_PREFIX) . $sEstimateId;
        $sData = \PreSale\Logics\redismove\RedisWrapper::getInstance(P_REC_COMBO_CACHE_PREFIX)->get($sKey);
        return json_decode($sData, true) ?? [];
    }
}
