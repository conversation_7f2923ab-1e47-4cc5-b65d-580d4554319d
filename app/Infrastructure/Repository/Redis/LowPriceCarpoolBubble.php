<?php
/**
 * Created by PhpStorm.
 * Date: 19/9/5
 * Time: 12:20
 * @category Category
 * @package FileDirFileName
 * <AUTHOR> <<EMAIL>>
 * @link ${link}
 */
namespace PreSale\Infrastructure\Repository\Redis;

use BizLib\Libraries\RedisDB;
use BizLib\Utils\Common;
use BizLib\Log as NuwaLog;

/**
 * Class LowPriceCarpoolBubble
 * @package PreSale\Infrastructure\Repository\Redis
 * @property LowPriceCarpoolBubble $LowPriceCarpoolBubble
 */
class LowPriceCarpoolBubble
{

    /**
     * @Desc:
     * @param mixed[] $iPassengerId int structure to count the elements of.
     * @return string
     * @property _getKey $_getKey
     * @Author:<EMAIL>
     */
    /*
    private static function _getKey($iPassengerId) {
        $prefix = Common::getRedisPrefix(P_LOW_PRICE_CARPOOL_SHOW_BUBBLE);
        return $prefix.$iPassengerId;
    }
    */

    /**
     * @Desc:
     * @param mixed[] $iPassengerId structure to count the elements of.
     * @return mixed[] int|null structure to count the elements of.
     * @property getShowCount $getShowCount
     * @Author:<EMAIL>
     */
    /*
    public static function getShowCount($iPassengerId) {
        $redisdb = RedisDB::getInstance();
        $sKey    = self::_getKey($iPassengerId);
        $re      = $redisdb->get($sKey);

        return false === $re ? 0 : $re;
    }
    */

    /**
     * @Desc:
     * @param mixed[] $iPassengerId structure to count the elements of.
     * @return void
     * @property increaseShowCount $increaseShowCount
     * @Author:<EMAIL>
     */
    /*
    public static function increaseShowCount($iPassengerId) {
        $redisdb = RedisDB::getInstance();
        $sKey    = self::_getKey($iPassengerId);
        $re      = $redisdb->incr($sKey);
        if (!$re) {
            NuwaLog::warning(sprintf('errmsg:cache low price carpool bubble count set fail strKey:%s', $sKey));
        }
    }
    */
}
