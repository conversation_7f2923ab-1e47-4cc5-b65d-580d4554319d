<?php

namespace PreSale\Infrastructure\Repository\Redis;

use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Log;
use BizLib\Utils\Common;
use PreSale\Logics\redismove\RedisWrapper;

/**
 * redis实体 .
 *
 * @brief
 *
 * <AUTHOR>
 * @date 2023-09-15
 */

class PrepayInterceptResultRepository
{

    // 缓存时间3小时
    const TTL = 3 * 60 * 60;

    /**
     * 以10分钟
     * @param string $sOrderId 低位订单号
     * @param array  $aData    拦截结果对象缓存
     * @return void
     */
    public static function setPrepayInterceptResult($sOrderId, $aData) {
        $sKey = Common::getRedisPrefix(O_MULTI_ADD_PRODUCT_INTERCEPT_RESULT) . $sOrderId;
        $ret  = RedisWrapper::getInstance(O_MULTI_ADD_PRODUCT_INTERCEPT_RESULT)->setex($sKey,self::TTL, json_encode($aData));
        if (!$ret) {
            Log::warning(Msg::formatArray(Code::E_COMMON_REDIS_SET_FAIL),['key' => $sKey, 'data' => $aData]);
        }
    }

    /**
     *
     * @param string $sOrderId 加密订单id
     * @return array|mixed
     */
    public static function getPrepayInterceptResult($sOrderId) {
        if (empty($sOrderId)) {
            return [];
        }
        $sKey = Common::getRedisPrefix(O_MULTI_ADD_PRODUCT_INTERCEPT_RESULT) . $sOrderId;
        $sData = RedisWrapper::getInstance(O_MULTI_ADD_PRODUCT_INTERCEPT_RESULT)->get($sKey);
        if (empty($sData)) {
            return [];
        }
        return json_decode($sData, true) ?? [];
    }
}
