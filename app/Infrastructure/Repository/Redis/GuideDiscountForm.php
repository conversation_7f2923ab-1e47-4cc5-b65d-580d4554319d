<?php
/**
 * @desc 导流折扣类型缓存
 * <AUTHOR> <owen<PERSON>u<PERSON><EMAIL>>
 * @date 2020-07-07
 */

namespace PreSale\Infrastructure\Repository\Redis;

use BizLib\Libraries\RedisDB;
use BizLib\Utils\Common;
use BizLib\Log as NuwaLog;

/**
 * Class GuideDiscountForm.
 */
class GuideDiscountForm
{

    const CACHE_TIME = 600; // 缓存时间

    /**
     * @desc setGuideDiscountFormCache 设置导流折扣类型到缓存中
     * @param string $sEstimateId        sEstimateId
     * @param int    $iGuideDiscountForm iGuideDiscountForm
     * @return void
     */
    public static function setGuideDiscountFormCache($sEstimateId, $iGuideDiscountForm) {
        $oRedisDB = RedisDB::getInstance();
        $sKey     = self::_getKey($sEstimateId);
        $bOk      = $oRedisDB->setex($sKey, self::CACHE_TIME, $iGuideDiscountForm);
        if (!$bOk) {
            NuwaLog::warning(sprintf('errmsg:cache guide discount form set fail strKey:%s', $sKey));
        }
    }

    /**
     * @desc _getKey
     * @param string $sEstimateId sEstimateId
     * @return string
、   */
    private static function _getKey($sEstimateId) {
        $sPrefix = Common::getRedisPrefix(P_GUIDE_DISCOUNT_FORM);

        return $sPrefix . $sEstimateId;
    }
}
