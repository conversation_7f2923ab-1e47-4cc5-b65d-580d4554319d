<?php
/**
 * Created by PhpStorm.
 * Author:xia<PERSON><PERSON>@didichuxing.com
 * Date: 19/4/23
 * Time: 21:53.
 */

namespace PreSale\Infrastructure\Repository\Redis;

use BizLib\Libraries\RedisDB;
use BizLib\Utils\Common;
use BizLib\Log as NuwaLog;

class CarpoolLongOrderBubble
{
    /*
    private static function _getKey($sBubbleId) {
        $prefix = Common::getRedisPrefix(P_CARPOOL_LONG_ORDER_SHOW_BUBBLE);

        return $prefix.$sBubbleId;
    }

    public static function getShowCount($sBubbleId) {
        $redisdb = RedisDB::getInstance();
        $sKey    = self::_getKey($sBubbleId);
        $re      = $redisdb->get($sKey);

        return false === $re ? 0 : $re;
    }

    public static function setShowCount($sBubbleId) {
        $redisdb = RedisDB::getInstance();
        $sKey    = self::_getKey($sBubbleId);
        $re      = $redisdb->setex($sKey, 1800, 1);
        if (!$re) {
            NuwaLog::warning(sprintf('errmsg:cache carpool_long_order bubble count set fail strKey:%s', $sKey));
        }
    }
    */

    /**
     * @param string $sTraceId    traceid
     * @param array  $aTimeRanges 时间范围
     * @return void
     */
    /*
    public static function storeTimeRanges($sTraceId, $aTimeRanges) {
        if (empty($sTraceId) ||empty($aTimeRanges)) {
            NuwaLog::warning(sprintf('errmsg:cache carpool_long_order time ranges fail traceid:%s  value:%s', $sTraceId,json_encode($aTimeRanges)));
            return;
        }

        $redisdb = RedisDB::getInstance();
        $sKey    = Common::getRedisPrefix(P_CARPOOL_TIME_RANGE).$sTraceId;
        $re      = $redisdb->setex($sKey, 1800, json_encode($aTimeRanges));
        if (!$re) {
            NuwaLog::warning(sprintf('errmsg:cache carpool_long_ordertime ranges fail :%s', $re));
        }
    }
    */
}
