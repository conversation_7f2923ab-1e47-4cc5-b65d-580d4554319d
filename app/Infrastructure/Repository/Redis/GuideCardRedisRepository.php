<?php

namespace PreSale\Infrastructure\Repository\Redis;

use BizLib\Client\Cache\StorageClient;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Utils\Common;
use PreSale\Logics\redismove\RedisWrapper;
use PreSale\Models\guideCardView\guideCard\BaseGuideCard;
use PreSale\Models\guideCardView\guideCard\GuideAnycarCard;
use BizLib\Log as NuwaLog;

/**
 * 取消挽留出口卡片缓存
 */
class GuideCardRedisRepository
{
    const TTL = 1200;

    /**
     * @param int           $iPid       pid
     * @param string        $sCardID    cardId
     * @param BaseGuideCard $oGuideCard guideCard
     * @return bool
     */
    public static function save($iPid, $sCardID, $oGuideCard) {
        if (empty($iPid) || empty($sCardID) || empty($oGuideCard)) {
            return false;
        }

        $sPrefix  = Common::getRedisPrefix(P_PRECANCEL_APPEND_CARPOOL_CARD);
        $key      = $sPrefix.$iPid.$sCardID;
        $value    = serialize($oGuideCard);
        $oRedisDB = StorageClient::getInstance(0, RedisWrapper::DISF);
        $result   = $oRedisDB->setex($key, self::TTL, $value);
        if (empty($result)) {
            NuwaLog::warning(Msg::formatArray(Code::E_COMMON_REDIS_GET_FAIL, ['key' => $key,]));
            return false;
        }

        return true;
    }

    /**
     * @param int    $iPid    pid
     * @param string $sCardID cardId
     * @return BaseGuideCard
     */
    public static function get($iPid, $sCardID) {
        if (empty($iPid) || empty($sCardID)) {
            return null;
        }

        $oRedisDB = StorageClient::getInstance(0, RedisWrapper::DISF);
        $sPrefix  = Common::getRedisPrefix(P_PRECANCEL_APPEND_CARPOOL_CARD);
        $key      = $sPrefix.$iPid.$sCardID;
        $result   = $oRedisDB->get($key);
        if (empty($result)) {
            return null;
        }

        $oGuideCard = unserialize($result);
        return $oGuideCard ?: null;
    }
}
