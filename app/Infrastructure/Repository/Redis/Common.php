<?php

namespace PreSale\Infrastructure\Repository\Redis;

use BizLib\Libraries\RedisDB;

class Common
{
    /**
     * 频次控制
     * @param string $sKey redisKey
     * @param int $iExpireTime 失效时间
     * @param int $iLimitTimes 频次上限
     * @return bool
     */
    public static function controlFrequency($sKey, $iExpireTime, $iLimitTimes)
    {
        $oRedisDB = RedisDB::getInstance();
        $iTimes = $oRedisDB->get($sKey);
        if (!empty($iTimes)) {
            if ($iTimes < $iLimitTimes) {
                $iTimes = $iTimes + 1;
                $iExpireTime = $oRedisDB->ttl($sKey);
                $oRedisDB->setex($sKey, $iExpireTime, $iTimes);
                return true;
            } else {
                return false;
            }
        } else {
            $oRedisDB->setex($sKey, $iExpireTime, 1);
            return true;
        }
    }

    /**
     * 判断是否可增长
     * @param string $sKey redisKey
     * @param int $iLimitTimes 频次上限
     * @return bool
     */
    public static function canIncrease($sKey, $iLimitTimes)
    {
        $oRedisDB = RedisDB::getInstance();
        $iTimes = $oRedisDB->get($sKey);
        if (!empty($iTimes)) {
            if ($iTimes < $iLimitTimes) {
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    /**
     * 设置频次+1
     * @param string $sKey redisKey
     * @param int $iExpireTime 失效时间
     * @return bool
     */
    public static function increaseLimitTime($sKey, $iExpireTime)
    {
        $oRedisDB = RedisDB::getInstance();
        $iTimes = $oRedisDB->get($sKey);
        if (!empty($iTimes)) {
            $iTimes = $iTimes + 1;
            $iExpireTime = $oRedisDB->ttl($sKey);
            $oRedisDB->setex($sKey, $iExpireTime, $iTimes);
        } else {
            $oRedisDB->setex($sKey, $iExpireTime, 1);
        }
    }

}