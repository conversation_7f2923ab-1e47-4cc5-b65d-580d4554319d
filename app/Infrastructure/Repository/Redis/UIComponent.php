<?php
/**
 * redis repo
 * <AUTHOR> <<EMAIL>>
 * @Date: 2019-07-08 10:55
 */

namespace PreSale\Infrastructure\Repository\Redis;

use BizLib\Libraries\RedisDB;
use BizLib\Log as NuwaLog;
use BizLib\Utils\Common;

/**
 * Class UIComponent
 * @package PreSale\Infrastructure\Repository\Redis
 */
class UIComponent
{

    const COMPONENT_CACHE_EXPIRE = 600;

    /**
     * cache component info
     * @param string $sKey       key
     * @param array  $aComponent component
     * @return bool
     */
    public function storeComponent($sKey, $aComponent) {
        if (empty($sKey) || empty($aComponent)) {
            return false;
        }

        $oRedisDb = RedisDB::getInstance();
        $sKey     = Common::getRedisPrefix(P_COMPONENT_CACHE_PREFIX) . $sKey;
        $re       = $oRedisDb->setex($sKey, self::COMPONENT_CACHE_EXPIRE, json_encode($aComponent));
        if (!$re) {
            NuwaLog::warning(sprintf('cache ui component fail, key:%s', $sKey));
        }

        return true;
    }

    /**
     * @param int   $iTraceId      estimate_id
     * @param array $aActivityType activityType
     * @return bool
     */
    public function cacheActivity($iTraceId, $aActivityType) {
        if (empty($iTraceId) || empty($aActivityType)) {
            return false;
        }

        $oRedisDb = \PreSale\Logics\redismove\RedisWrapper::getInstance(P_ACTIVITY_TYPE_PREFIX);
        $sKey     = Common::getRedisPrefix(P_ACTIVITY_TYPE_PREFIX) . $iTraceId;
        $re       = $oRedisDb->hMSet($sKey, $aActivityType);
        if (!$re) {
            NuwaLog::warning(sprintf('cache ui component fail, key:%s', $sKey));
            return false;
        }

        $oRedisDb->expire($sKey, self::COMPONENT_CACHE_EXPIRE);

        return true;
    }

    /**
     * @param int $iTraceId trace_id
     * @return array|bool
     */
    public function getActivityCache($iTraceId) {
        if (empty($iTraceId)) {
            return [];
        }

        $oRedisDb = \PreSale\Logics\redismove\RedisWrapper::getInstance(P_ACTIVITY_TYPE_PREFIX);
        $sKey     = Common::getRedisPrefix(P_ACTIVITY_TYPE_PREFIX) . $iTraceId;
        $re       = $oRedisDb->hgetall($sKey);
        if (!$re) {
            return [];
        }

        return $re;
    }
}
