<?php

namespace PreSale\Infrastructure\Repository\Redis;

use BizCommon\Logics\Anycar\AnyCarCommonLogic;
use BizLib\Log;
use TripcloudCommon\Utils\Product;
use Xiaoju\Apollo\Apollo;

/**
 * PriceRepository
 */
class PriceRepository
{
    // 三方车型未授权
    const TRIP_CLOUD_NEED_AUTH = '1';

    /**
     * @param array $aAnycarEstimate      追加车型预估信息
     * @param array $aOrderInfo           订单信息
     * @param array $aRequest             请求
     * @param array $aMultiRequireProduct 车型列表
     * @param bool  $bIterationV3         是否为追加车型anycarEstimateV3接口
     * @return array
     */
    public static function getAnycarWithEstimateInfo($aAnycarEstimate, $aOrderInfo, $aRequest, $aMultiRequireProduct, $bIterationV3 = false) {
        $aProductList = self::_getEstimateProductList($aAnycarEstimate, $bIterationV3);
        if (empty($aAnycarEstimate) || empty($aProductList)) {
            if (is_array($aMultiRequireProduct)) {
                foreach ($aMultiRequireProduct as &$aProductItem) {
                    $aProductItem['estimate_fee']  = -1;
                    $aProductItem['coupon_amount'] = 0;
                    $aProductItem['dynamic_times'] = 0;
                }
            }

            return $aMultiRequireProduct;
        }

        $aEstimateFeeInfo = [];
        foreach ($aProductList as $key => $aProductItem) {
            $sGroupKey    = AnyCarCommonLogic::getGroupKey($aProductItem);
            $iEstimateFee = $aProductItem['dynamic_total_fee'];
            $iExactFee    = $aProductItem['price'];
            $iCounpon     = $iEstimateFee - $iExactFee;
            if ($iCounpon < 0) {
                $iCounpon = 0;
            }

            // 增加三方车型是否授权字段，下发给athena，athena通过该字段来筛选未授权的三方车型(低价盒子、取消挽留)
            $sIsDefaultAuth = '0';
            if (array_key_exists('is_default_auth', $aProductItem)
                && self::TRIP_CLOUD_NEED_AUTH == $aProductItem['is_default_auth']
            ) {
                $sIsDefaultAuth = '1';
            }

            $aExtraMap = [];
            if (!empty($aProductItem['extra_map'])) {
                $aExtraMap = is_array($aProductItem['extra_map']) ? $aProductItem['extra_map'] : json_decode($aProductItem['extra_map'],true);
            }
            $aEstimateFeeInfo[$sGroupKey] = [
                'estimate_fee'             => $iExactFee,
                'coupon_amount'            => $iCounpon,
                'dynamic_times'            => $aProductItem['dynamic_times'],

                'anycar_estimate_id'       => $aAnycarEstimate['estimate_id'],
                'anycar_estimate_trace_id' => $aAnycarEstimate['estimate_trace_id'],
                'sub_estimate_id'          => $aProductItem['estimate_id'],
                'business_name'            => $aProductItem['business_name'],
                'product_category'         => $aProductItem['product_category'],
                'is_default_auth'          => $sIsDefaultAuth,
            ];
            if (!empty($aExtraMap)) {
                $aEstimateFeeInfo[$sGroupKey]['sub_group_id'] = $aExtraMap['sub_group_id'];
            }
        }

        $aFinalProductList = [];
        $oApollo           = new Apollo();
        if (!is_array($aMultiRequireProduct)) {
            return $aFinalProductList;
        }

        foreach ($aMultiRequireProduct as $aProductItem) {
            $sGroupKey = AnyCarCommonLogic::getGroupKey($aProductItem);
            $aEstimate = $aEstimateFeeInfo[$sGroupKey] ?? [];

            // 如果没有获取到预估价格，剔除掉
            if (empty($aEstimate) && $oApollo->featureToggle(
                'athena_order_match_delete_product_switch',
                [
                    'key'           => $aOrderInfo['passenger_id'] ?? 0,
                    'city'          => $aOrderInfo['area'],
                    'business_id'   => $aProductItem['business_id'],
                    'require_level' => $aProductItem['require_level'],
                    'combo_type'    => $aProductItem['combo_type'],
                ]
            )
            ) {
                continue;
            }

            $aItem = array_merge($aProductItem, $aEstimate);
            $aItem['estimate_fee']  = $aEstimate['estimate_fee'] ?? -1;
            $aItem['coupon_amount'] = $aEstimate['coupon_amount'] ?? 0;
            $aItem['dynamic_times'] = $aEstimate['dynamic_times'] ?? 0;
            $aItem['extra']         = [
                'anycar_estimate_id'       => $aEstimate['anycar_estimate_id'],
                'anycar_estimate_trace_id' => $aEstimate['anycar_estimate_trace_id'],
                'sub_estimate_id'          => $aEstimate['sub_estimate_id'],
                'business_name'            => $aEstimate['business_name'],
                'coupon_amount'            => (string)($aEstimate['coupon_amount']),
            ];
            if (Product::isTripcloudByBusinessID($aProductItem['business_id'])) {
                $aItem['extra']['is_third_party'] = '1';
            } else {
                $aItem['extra']['is_third_party'] = '0';
            }

            $aFinalProductList[] = $aItem;
        }

        return $aFinalProductList;
    }

    /**
     * @param array $aAnycarEstimate 追加车型预估数据
     * @param bool  $bIterationV3    是否为追加车型anycarEstimateV3接口
     * @return array
     */
    private static function _getEstimateProductList($aAnycarEstimate, $bIterationV3) {
        if (!$bIterationV3) {
            return $aAnycarEstimate;
        } else {
            // 从v3的结构中取预估数据
            $aEstimateData = $aAnycarEstimate;
            if (!is_array($aEstimateData)) {
                return $aEstimateData;
            }

            foreach ($aEstimateData as $iProductCategory => &$aEstimateItem) {
                // scene_info中的数据移动到外层
                $aEstimateItem = array_merge($aEstimateItem, $aEstimateItem['scene_info']);
                if (array_key_exists('p_new_order_params', $aEstimateItem)) {
                    $aEstimateItem = array_merge($aEstimateItem, $aEstimateItem['p_new_order_params']);
                }

                // 处理price和business_name
                $aEstimateItem['price']         = (float)$aEstimateItem['fee_amount'];
                $aEstimateItem['business_name'] = $aEstimateItem['car_title'];
                // 处理 seat_nums
                if (!empty($aEstimateItem['carpool_seat_list'])) {
                    $aEstimateItem['seat_nums']['text']        = $aEstimateItem['carpool_seat_list']['label'];
                    $aEstimateItem['seat_nums']['num']         = $aEstimateItem['carpool_seat_list']['value'];
                    $aEstimateItem['seat_nums']['is_selected'] = $aEstimateItem['carpool_seat_list']['selected'];
                }
            }

            return $aEstimateData;
        }
    }
}
