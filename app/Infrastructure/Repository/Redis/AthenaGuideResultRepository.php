<?php
namespace PreSale\Infrastructure\Repository\Redis;

use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Libraries\RedisDB;
use BizLib\Utils\Common;
use BizLib\Log;

/**
 * AthenaGuideResultRepository
 */
class AthenaGuideResultRepository
{
    const GUIDE_RESULT_CACHE_EXPIRE_BUFFER = 1 * 60;

    /**
     *
     * @param string $sEstimateId sEstimateId
     * @param array  $aData       aData
     * @return void
     */
    public static function setAthenaGuideResult($sEstimateId, $aData) {
        $iCountDown = $aData['count_down'] ?? 0;
        $sKey       = Common::getRedisPrefix(O_DACHE_ANYCAR_GUIDE_INFO) . $sEstimateId;
        $ret        = \PreSale\Logics\redismove\RedisWrapper::getInstance(O_DACHE_ANYCAR_GUIDE_INFO)->setex($sKey,self::GUIDE_RESULT_CACHE_EXPIRE_BUFFER + $iCountDown,json_encode($aData));
        if (!$ret) {
            Log::warning(Msg::formatArray(Code::E_COMMON_REDIS_SET_FAIL),['key' => $sKey, 'data' => $aData]);
        }
    }

    /**
     *
     * @param string $sEstimateId sEstimateId
     * @return array
     */
    public static function getAthenaGuideResult($sEstimateId) {
        $sKey  = Common::getRedisPrefix(O_DACHE_ANYCAR_GUIDE_INFO) . $sEstimateId;
        $sData = \PreSale\Logics\redismove\RedisWrapper::getInstance(O_DACHE_ANYCAR_GUIDE_INFO)->get($sKey);
        return json_decode($sData, true) ?? [];
    }
}
