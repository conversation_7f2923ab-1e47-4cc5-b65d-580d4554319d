<?php

namespace PreSale\Infrastructure\Ofs;

use BizLib\Client\OfsClient;

/**
 * 订单特征
 */
class OrderOfsRepository
{
    /**
     * @param string $sOrderId          order_id
     * @param array  $aSceneFeatureMaps 要设置的特征数据
     * @return bool
     */
    public function setOrderFeature($sOrderId, $aSceneFeatureMaps) {
        $oOfs    = new OfsClient();
        $aOfsRes = $oOfs->setOrderFeature($sOrderId, $aSceneFeatureMaps);
        if (0 != $aOfsRes['errno']) {
            return false;
        }

        return true;
    }

    /**
     * @param string $sOrderId          order_id
     * @param array  $aSceneFeatureKeys 要获取的订单特征key
     * @return array
     */
    public function getOrderFeature($sOrderId, $aSceneFeatureKeys) {
        $oOfs    = new OfsClient();
        $aResult = $oOfs->getOrderFeature($sOrderId, $aSceneFeatureKeys);

        return $aResult;
    }
}
