<?php
namespace PreSale\Infrastructure\Repository\Tag;

use Biz<PERSON>ib\Client\DiTagServiceClient;
use Biz<PERSON>ib\Client\TagServiceClient;
use Xiaoju\Apollo\Apollo;

/**
 * Class TagServiceRepository
 * @package PreSale\Infrastructure\Repository\Tag
 */
class TagServiceRepository
{

    const DEFAULT_CALLER = 'pre-sale';

    /**
     * @param string $sPid    乘客PID
     * @param string $sTag    implode(",",[1,2,3])
     * @param string $sCaller Caller
     * @return array
     */
    public static function getPassengerTagResult(string $sPid, string $sTag, string $sCaller) {
        $aTagsRet = self::getClient($sPid)->getUserTagResult($sPid, $sTag, $sCaller);
        $aRet     = [];
        if (TagServiceClient::SUCCESS_CODE == $aTagsRet['errno']) {
            $aRawRet = $aTagsRet['value'] ?? [];
            foreach ($aRawRet as $sTag => $iStatus) {
                if (1 == $iStatus) {
                    $aRet[] = $sTag;
                }
            }
        }

        return $aRet;
    }

    /**
     * @param string $sPid    乘客PID
     * @param string $sTag    单标签
     * @param string $sCaller Caller
     * @return bool
     */
    public static function isPassengerInTag(string $sPid, string $sTag, string $sCaller) {
        $aTagsRet = self::getClient($sPid)->getUserTagResult($sPid, $sTag, $sCaller);
        if (TagServiceClient::SUCCESS_CODE == $aTagsRet['errno']) {
            $aRawRet = $aTagsRet['value'] ?? [];
            foreach ($aRawRet as $sTagIndex => $iStatus) {
                if (1 == $iStatus && $sTagIndex == $sTag) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * @param string $sPid    乘客PID
     * @param string $sTag    多标签
     * @param string $sCaller Caller
     * @return bool
     */
    public static function isPassengerInTags(string $sPid, string $sTag, string $sCaller) {
        $aTagsRet = self::getClient($sPid)->getUserTagResult($sPid, $sTag, $sCaller);
        $aTags = explode(',' , $sTag);
        if (empty($aTags)) {
            return false;
        }
        if (TagServiceClient::SUCCESS_CODE == $aTagsRet['errno']) {
            $aRawRet = $aTagsRet['value'] ?? [];
            foreach ($aRawRet as $sTagIndex => $iStatus) {
                if (1 == $iStatus && in_array($sTagIndex, $aTags)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * @param string $sPid    乘客PID
     * @param string $sTag    单标签
     * @param string $sCaller Caller
     * @return bool
     */
    public static function isAnyoneInTag(string $sPid, string $sTag, string $sCaller) {
        $aTagsRet = self::getClient($sPid)->getUserTagResult($sPid, $sTag, $sCaller);
        if (TagServiceClient::SUCCESS_CODE == $aTagsRet['errno']) {
            $aRawRet = $aTagsRet['value'] ?? [];
            foreach ($aRawRet as $sTagIndex => $iStatus) {
                if (1 == $iStatus) {
                    return true;
                }
            }
        }

        return false;
    }


    public static function getClient(string $sPid) {
        return new DiTagServiceClient();
    }
}
