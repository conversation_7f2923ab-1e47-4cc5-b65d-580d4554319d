<?php

namespace PreSale\Infrastructure\Repository\OneConf;

use BizLib\Log as NuwaLog;
use <PERSON><PERSON>\Apollo\Apollo as ApolloV2;
use Nuwa\ApolloSDK\Apollo;
use PreSale\Logics\openCity\OpenCityLogic;

/**
 *
 * Copyraight (c) 2019 xiaojukeji.com, Inc. All Rights Reserved.
 * @author: wang<PERSON><PERSON><PERSON><PERSON>@didichuxing.com
 * @date: 2019-12-27 16:57
 * @desc: gulfstream/oneConf仓储
 * @wiki:
 *
 */
class OneConfRepository
{
    const OPEN_CITY_NS            = 'kflower_open_city_conf';
    const OPEN_CITY_WHITE_LIST_NS = 'kflower_open_city_white_list_conf';
    const OPEN_CITY_WHITE_LIST_KEY = 'open_city_white_list';

    // 开城状态枚举值
    const OPEN_CITY_STATUS_NORMAL  = 1; // 开城
    const OPEN_CITY_STATUS_WARM_UP = 2; // 预热
    const OPEN_CITY_STATUS_CLOSE   = 3; // 下线

    private $_aParams;

    public $aOneConfData = [];

    /**
     * OneConfRepository constructor.
     * @param array $aParams 请求参数
     */
    public function __construct(array $aParams) {
        $this->_aParams = $aParams;
    }

    /**
     * 根据locsvr城市 id 获取开城的配置信息
     * 因为之前countryid的逻辑在上层，现在下沉，所以增加countryId参数
     * @param int $iCityId 城市id
     * @return array
     */
    public function getOpenCityDataByCityId($iCityId, $iCountyId) {
        $aCondition = array(
            'city_id'     => $iCityId,
            'phone'       => $this->_aParams['phone'],
            'county_id'   => $iCountyId,
            'app_version' => $this->_aParams['app_version'],
            'channel'     => $this->_aParams['channel'],
        );
        return (new OpenCityLogic())->getOpenCityConfigFromApollo($aCondition);
    }

    /**
     * 因为之前countryid的逻辑在上层，现在下沉，所以增加countryId参数
     * @param int $iCity       城市id
     * @param int $iBusinessId 业务线id
     * @return array
     */
    public function getOneConfByCityAndBusiness($iCity, $iBusinessId, $iCountyId) {
        $aApolloCondition = array(
            'city_id'     => $iCity,
            'business_id' => $iBusinessId,
            'phone'       => $this->_aParams['phone'],
            'county_id'   => $iCountyId,
            'app_version' => $this->_aParams['app_version'],
            'channel'     => $this->_aParams['channel'],
        );
        $aConfigData      = (new OpenCityLogic())->getOpenCityConfigFromApollo($aApolloCondition);

        return array_values($aConfigData)[0];
    }
}

