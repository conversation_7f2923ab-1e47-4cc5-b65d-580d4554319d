<?php


namespace PreSale\Infrastructure\Util;

use Nuwa\ApolloSDK\Apollo;

/**
 * Class AirGap
 * @package PreSale\Infrastructure\Util
 */
class AirGap
{
    /**
     * @param string $bizKey biz key.
     * @return bool
     */
    public static function hit($bizKey) {
        $iNowTime     = time();
        $aApolloParam = [
            'key' => $iNowTime,
            'biz_key' => $bizKey,
        ];
        $oApollo      = new Apollo();
        //909 不走
        $ofeatureToggle = $oApollo->featureToggle('drop_off_dsig_service', $aApolloParam);

        return $ofeatureToggle->allow();
    }
}
