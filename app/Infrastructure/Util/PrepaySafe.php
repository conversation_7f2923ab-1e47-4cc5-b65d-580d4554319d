<?php
/**
 * Created by PhpStorm.
 * <AUTHOR> <<EMAIL>>
 * Date: 2021/2/18
 * Time: 4:11 下午
 */

namespace PreSale\Infrastructure\Util;

use BizLib\Client\NewtonHackClient;
use BizLib\ErrCode\Code;
use BizLib\ErrCode\Msg;
use BizLib\Log;
use BizLib\Utils\UtilHelper;
use Nuwa\ApolloSDK\Apollo;
use BizLib\Utils\Request;
use BizLib\PhystrixClient\NewtonPhystrixCommonClient;

/**
 * Class PrepaySafe
 * @package PreSale\Infrastructure\Util
 */
class PrepaySafe
{
    /***
     * @desc 注：在v2和v3接口场景，这个是新的正常码，不用拦截
     */
    const RISK_SATE_INTERCEPT_CODE = 100001;

    /**
     * @desc 新的拦截码用100002
     */
    const RISK_SATE_INTERCEPT_NEW_CODE = 100002;

    /**
     * @desc 新的拦截码用100003
     */
    const RISK_SATE_POISONING_CODE = 100003;

    /**
     * @var bool
     */
    public static $bIsCheatingTraffic = false;

    public static $aRiskStationPrepaySafeUri = array(
        '/gulfstream/passenger/v2/core/pmultiestimateprice',
        '/gulfstream/pre-sale/v1/core/pmultiestimateprice',
        '/gulfstream/passenger/v2/core/pestimateprice',
        '/gulfstream/pre-sale/v1/core/pestimateprice',
        '/gulfstream/api/v1/passenger/pGetEstimatePriceCoupon',
    );

    /**
     * 接入新风控的uri
     * @var string[]
     */
    public static $aNewPrepaySafeUri = array('/gulfstream/pre-sale/v1/core/pmultiestimatepricev3',);

    /**
     * @return bool|mixed
     */
    public static function hit() {
        $iNowTime       = time();
        $oRequest       = Request::getInstance();
        $aBody          = $oRequest->get();
        $iCity          = $aBody['city_id'];
        $iChannel       = $aBody['channel'];
        $aApolloParam   = [
            'key'     => $iNowTime,
            'city'    => $iCity,
            'channel' => $iChannel,
        ];
        $oApollo        = new Apollo();
        $ofeatureToggle = $oApollo->featureToggle('prepay_safe_service_check_toggle', $aApolloParam);
        return $ofeatureToggle->allow();
    }

    /**
     * @return bool
     */
    public static function check(): bool {
        $sUri  = strtolower(parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH));
        $bOpen = UtilHelper::getFusingSwitch('prepay_safe_degrade_switch');
        if (UtilHelper::FUSING_DEGRADE_OPEN === $bOpen) {
            Log::warning(
                Msg::formatArray(
                    Code::E_COMMON_API_DEGRADE,
                    array('describe' => 'repay_safe_degrade_switch is degrade in pre-sale')
                )
            );
            return true;
        }

        $oRequest = Request::getInstance();
        $aHeaders = $oRequest->getHeaders();
        $aGetParam  = $oRequest->get();
        $aPostParam = $oRequest->post(null,  false, false);
        $aGetParam  = is_array($aGetParam) ? $aGetParam : array();
        $aPostParam = is_array($aPostParam) ? $aPostParam : array();
        $aBody      = array_merge($aGetParam, $aPostParam);

        $iChannel = $aBody['channel'];
        $iCity    = $aBody['city_id'];
        // 应安全部需求，来自开放平台的预估请求无需访问
        if (UtilHelper::isOpenApiChannel($iChannel)
            || UtilHelper::isPbdChannel($iChannel)
        ) {
            return true;
        }

        $oApollo       = new Apollo();
        $oApolloResult = $oApollo->featureToggle(
            'prepay_safe_service_check_toggle',
            [
                'key'     => time(),
                'city'    => $iCity,
                'channel' => $iChannel,
            ]
        );
        if (!$oApolloResult->allow()) {
            return true;
        }

        $aNewRiskStationPrepaySafeUri = [];
        $sUrls = $oApolloResult->getParameter('urls', '');
        if (!empty($sUrls)) {
            $aUrls = json_decode($sUrls, true);
            if (is_array($aUrls)) {
                $aNewRiskStationPrepaySafeUri = $aUrls;
            }
        }

        $bIntercept = false;
        $sRouterCode = "";
        if (in_array($sUri, self::$aRiskStationPrepaySafeUri)) {
            $aReqData  = array(
                'header' => $aHeaders,
                'params' => $aBody,
            );
            $aRetCheck = (new NewtonHackClient())->riskStationPrepaySafe($aReqData);

            // 命中拦截
            if (isset($aRetCheck['code']) && self::RISK_SATE_INTERCEPT_CODE == $aRetCheck['code']) {
                $bIntercept = true;
                Log::warning(
                    Msg::formatArray(
                        Code::E_COMMON_PARAM_ERROR,
                        array(
                            'err_msg'       => 'prepay safe check fail',
                            'from'          => (string)($aBody['from']),
                            'access_key_id' => (int)($aBody['access_key_id']),
                            'checkerrno'    => $aRetCheck['code'],
                        )
                    )
                );
            }
            // v2接口
        } elseif (in_array($sUri, $aNewRiskStationPrepaySafeUri)) {
            $aData     = [
                'header' => $aHeaders,
                'params' => $aBody,
            ];
            $aReqData  = ['data' => json_encode($aData)];
            $aRetCheck = (new NewtonPhystrixCommonClient())->riskServiceHackNewPrice($aReqData);

            // 命中拦截
            if (isset($aRetCheck['code']) && self::RISK_SATE_INTERCEPT_NEW_CODE == $aRetCheck['code']) {
                $bIntercept = true;
                Log::warning(
                    Msg::formatArray(
                        Code::E_COMMON_PARAM_ERROR,
                        array(
                            'err_msg'       => 'prepay safe check fail',
                            'from'          => (string)($aBody['from']),
                            'access_key_id' => (int)($aBody['access_key_id']),
                            'checkerrno'    => $aRetCheck['code'],
                        )
                    )
                );
            } elseif (isset($aRetCheck['code']) && self::RISK_SATE_POISONING_CODE == $aRetCheck['code']) {
                self::$bIsCheatingTraffic = true;
            }

            $sRouterCode = self::_getRouterHeadCode($aRetCheck);
            // v3接口
        } elseif (in_array($sUri, self::$aNewPrepaySafeUri)) {

            $aData     = [
                'header' => self::buildNewPrepaySafeHeader($aHeaders),
                'params' => self::buildNewPrepaySafeReq($aBody),
            ];

            $aReqData  = ['data' => json_encode($aData)];
            $aRetCheck = (new NewtonPhystrixCommonClient())->riskServiceHackWycNewPrice($aReqData);

            // 命中拦截
            if (isset($aRetCheck['code']) && self::RISK_SATE_INTERCEPT_NEW_CODE == $aRetCheck['code']) {
                $bIntercept = true;
                Log::warning(
                    Msg::formatArray(
                        Code::E_COMMON_PARAM_ERROR,
                        array(
                            'err_msg'       => 'prepay safe check fail',
                            'from'          => (string)($aBody['from']),
                            'access_key_id' => (int)($aBody['access_key_id']),
                            'checkerrno'    => $aRetCheck['code'],
                        )
                    )
                );
            } elseif (isset($aRetCheck['code']) && self::RISK_SATE_POISONING_CODE == $aRetCheck['code']) {
                self::$bIsCheatingTraffic = true;
            }

            $sRouterCode = self::_getRouterHeadCode($aRetCheck);
        }

        $sHeader = sprintf('SECDD-NEWTON-ABNORMAL: %s', $sRouterCode);
        header($sHeader);

        if ($bIntercept) {
            header('HTTP/1.1 520 Internal Server Error', true, 520);
            exit;
        }

        return true;
    }

    /**
     * @param array $aHeader header
     * @return mixed
     */
    public static function buildNewPrepaySafeHeader($aHeader) {
        $aReq = [
            'wsgdid'          => $aHeader['wsgdid'] ?? '',
            'wsgsig'          => $aHeader['wsgsig'] ?? '',
            'user_agent'      => $aHeader['User-Agent'] ?? '',
            'referer'         => $aHeader['Referer'] ?? '',
            'didi_header_rid' => $aHeader['Didi-Header-Rid'] ?? '',
            'x_real_ip'       => $aHeader['X-Real-Ip'] ?? '',
        ];

        $oReq = new \Dirpc\SDK\NewtonCommon\NewRiskServiceHackNewPriceHeader($aReq);
        return json_decode(json_encode($oReq), true);
    }

    /**
     * @param array $aParam param
     * @return array
     */
    public static function buildNewPrepaySafeReq($aParam) {
        $aReq = [
            'access_key_id' => $aParam['access_key_id'] ?? 0,
            'to_poi_type'   => $aParam['to_poi_type'] ?? '',
            'app_version'   => $aParam['app_version'] ?? '',
            'from_lng'      => $aParam['from_lng'] ?? 0,
            'channel'       => $aParam['channel'] ?? 0,
            'origin_id'     => $aParam['origin_id'] ?? 0,
            'client_type'   => $aParam['client_type'] ?? 0,
            'from_name'     => $aParam['from_name'] ?? '',
            'a3_token'      => $aParam['a3_token'] ?? '',
            'user_type'     => $aParam['user_type'] ?? 0,
            'platform_type' => $aParam['platform_type'] ?? 0,
            'wsgenv'        => $aParam['wsgenv'] ?? '',
            'activity_id'   => $aParam['activity_id'] ?? 0,
            'to_lng'        => $aParam['to_lng'] ?? 0,
            'api'           => parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH),
            'from_poi_type' => $aParam['from_poi_type'] ?? '',
            'from_address'  => $aParam['from_address'] ?? '',
            'terminal_id'   => $aParam['terminal_id'] ?? '',
            'lat'           => $aParam['lat'] ?? 0,
            'from_poi_id'   => $aParam['from_poi_id'] ?? '',
            'menu_id'       => $aParam['menu_id'] ?? '',
            'from_lat'      => $aParam['from_lat'] ?? 0,
            'to_lat'        => $aParam['to_lat'] ?? 0,
            'lng'           => $aParam['lng'] ?? 0,
            'suuid'         => $aParam['suuid'] ?? '',
            'to_name'       => $aParam['to_name'] ?? '',
            'openid'        => $aParam['open_id'] ?? '',
            'to_address'    => $aParam['to_address'] ?? '',
            'token'         => $aParam['token'] ?? '',
            'pixels'        => $aParam['pixels'] ?? '',
            'wsgsig'        => $aParam['wsgsig'] ?? '',
            'to_poi_id'     => $aParam['to_poi_id'] ?? '',
            'imei'          => $aParam['imei'] ?? '',
        ];

        $oReq = new \Dirpc\SDK\NewtonCommon\NewRiskServiceHackNewPriceReq($aReq);
        return json_decode(json_encode($oReq), true);
    }

    /**
     * 处理投毒：
     * (目前只针对v2和v3接口）
     * 如果newton返回空，表示调用问题，返回"-1"
     * 如果newton返回不为空：
     *     header中有SECDD-NEWTON-ABNORMAL字段，返回该字段的值
     *     header中没有SECDD-NEWTON-ABNORMAL字段，或者为空：
     *          code为100001：返回"0"
     *          code为非100001（目前为100002或者100003），返回"1"
     */
    /**
     * @param array|mixed $aRetCheck
     * @return mixed|string
     */
    public static function _getRouterHeadCode($aRetCheck) {
        if (empty($aRetCheck)) {
            return "-1";
        }

        $sHeaderCode = (isset($aRetCheck->data['secdd_newton_abnormal']) && !empty($aRetCheck['secdd_newton_abnormal']))
            ? $aRetCheck['secdd_newton_abnormal']:'';

        if (!empty($sHeaderCode)) {
            return $sHeaderCode;
        } else {
            $iRespCode = $aRetCheck['code'];
            if (self::RISK_SATE_INTERCEPT_CODE == $iRespCode) {
                return "0";
            } else {
                return "1";
            }
        }
    }

}
