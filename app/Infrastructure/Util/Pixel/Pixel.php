<?php

namespace PreSale\Infrastructure\Util\Pixel;

/**
 *
 * Copyraight (c) 2020 xiaojukeji.com, Inc. All Rights Reserved.
 * @author: wang<PERSON><PERSON><PERSON><PERSON>@didichuxing.com
 * @date: 2020-01-04 16:26
 * @desc: 屏蔽像素值
 * @wiki:
 *
 */

class Pixel
{
    const PIXEL_TYPE_LOW  = 1; // 1x图标
    const PIXEL_TYPE_MID  = 2; // 2x图标
    const PIXEL_TYPE_HIGH = 3; // 3x图标

    /**
     * 获取屏幕像素对应的pixel type
     * @param string $sPixels 像素值 w*h
     * @return int
     */
    public static function getPixelType($sPixels = '') {
        $iPixelType = self::PIXEL_TYPE_MID;
        if (0 == strlen(trim($sPixels))) {
            return $iPixelType;
        }

        $aPixelType = explode('*', $sPixels);
        if (2 != count($aPixelType)) {
            return $iPixelType;
        }

        if ($aPixelType[0] <= 500) {
            $iPixelType = self::PIXEL_TYPE_LOW;
        } elseif ($aPixelType[0] > 500 && $aPixelType[0] <= 1000) {
            $iPixelType = self::PIXEL_TYPE_MID;
        } else {
            $iPixelType = self::PIXEL_TYPE_HIGH;
        }

        return $iPixelType;
    }
}
