<?php

namespace PreSale\Infrastructure\Util\Gaia;
/**
 * gaia实验参数格式化
 */
class GaiaParams
{

    /**
     * format 参数
     * @param array $aParams apollo实验参数
     * @return array
     */
    public static function getGaiaParams(array $aParams) {
        if (empty($aParams['city']) || empty($aParams['access_key_id']) || empty($aParams['app_version'])) {
            return [$aParams, false];
        }
        if (empty($aParams['uid']) && empty($aParams['pid'])) {
            return [$aParams, false];
        }

        if (isset($aParams['uid']) && isset($aParams['pid'])) {
            unset($aParams['uid']);
        }
        return [$aParams, true];
    }
}
