<?php
namespace PreSale\Infrastructure\Util;

use Disf\SPL\Metric;

/**
 * Trait RenderElapsedTimeLogTrait
 */
trait RenderElapsedTimeLogTrait
{
    protected $iStart;
    protected $iDuration;

    /**
     * @return void
     */
    public function renderStart() {
        $this->iStart = microtime(true) * 1000;
    }

    /**
     * @return void
     */
    public function renderStop() {
        $iNow            = microtime(true) * 1000;
        $this->iDuration = (int)($iNow - $this->iStart);
    }

    /**
     * @param string $sCaller       $sCaller
     * @param string $sCallee       $sCallee
     * @param int    $iProductCount $iProductCount
     * @return void
     */
    public function logRenderTimeMetric($sCaller, $sCallee, $iProductCount) {
        $tags = ['product_count' => $iProductCount,];
        Metric::sendRpcMetric($sCaller, $sCallee, $this->iDuration, 1, $tags, 'rpc_render_time');
    }
}
