<?php

/**
 * @brief
 *
 * <AUTHOR>
 * @date 2018-09-25
 */

namespace PreSale\Infrastructure\Util\AsyncMode;

class AsyncManager
{
    protected $_obj = '';

    public function __construct($obj) {
        $this->_obj = $obj;
    }

    public function __call($funcName, $arguments) {
        AsyncContainer::$ASYNC_LIST[] = function () use ($funcName, $arguments) {
            call_user_func_array([$this->_obj, $funcName], $arguments);
        };
    }
}
