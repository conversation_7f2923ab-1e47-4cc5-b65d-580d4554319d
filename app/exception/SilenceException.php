<?php
/**
 * 静默发单的返回
 * User: yuh<PERSON><PERSON>
 * Date: 2018/11/16
 * Time: 上午11:26
 */

namespace PreSale\Exception;

use BizLib\ErrCode\RespCode;

class SilenceException extends BaseException
{

    const DEFAULT_ORDER_ID = 'TkRJNU5EazJOekk1TmpBPQ==';

    public function __construct() {
        parent::__construct(null ,0, [], null);
        $this->mData = ['errno' => RespCode::R_SUCCESS, 'oid' => self::DEFAULT_ORDER_ID];
    }
}
