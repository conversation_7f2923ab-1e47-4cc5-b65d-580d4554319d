<?php
/**
 * Created by PhpStorm.
 * User: yuh<PERSON><PERSON>
 * Date: 2018/9/17
 * Time: 下午4:05.
 */

namespace PreSale\Exception;

use Throwable;

class BaseException extends \Exception
{
    public $mData;

    /** @var mixed 由于有些错误码是硬编码、下游透传的，类型有的为 int 有的为 string ，所以这里单独提供属性访问 */
    public $mErrno;

    public function __construct(string $sMsg = null, $mErrno = null, $mData = null, Throwable $oPrevious = null) {
        parent::__construct($sMsg, $mErrno, $oPrevious);
        $this->mData  = $mData ?? [];
        $this->mErrno = $mErrno;
    }
}
