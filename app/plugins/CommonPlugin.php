<?php

namespace PreSale\Plugins;

use BizLib\Utils\NuwaMetricHelper;
use Nuwa\Core\Application;
use Nuwa\Cors\CORS;
use Nuwa\Contracts\Plugin\PluginInterface;
use Nuwa\Core\Request\Request;
use Nuwa\Core\Response\Response;
use BizLib\Utils\LogHelper;
use Nuwa\Core\PluginEvents;

class CommonPlugin implements PluginInterface
{
    public static function getSubscribedEvents() {
        return [
            PluginEvents::ROUTER_SHUTDOWN => [
                'doLogReqIn',
                'doCORS',
            ],
            PluginEvents::PRE_DISPATCH    => ['doMetric',],
        ];
    }

    /**
     * @param Request  $request
     * @param Response $response
     */
    public function doLogReqIn($request, $response) {
        LogHelper::logRequestIn();
    }

    /**
     * @param Request  $request
     * @param Response $response
     */
    public function doCORS($request, $response) {
        $config = Application::app()->getConfig();
        CORS::handle($config['cors'], $request->getRequestUri());
    }

    /**
     * metric 上报
     * @param Request  $request
     * @param Response $response
     */
    public function doMetric($request, $response) {
        $track  = [
            'url' => $request->getRequestUri(),
            'access_key_id' => $_GET['access_key_id'] ?? ($_POST['access_key_id'] ?? 0)
        ];
        $metric = new NuwaMetricHelper();
        $metric->preController($track);
    }
}
