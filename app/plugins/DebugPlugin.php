<?php

namespace PreSale\Plugins;

use Nuwa\Contracts\Plugin\PluginInterface;
use Nuwa\Core\PluginSubject;
use Nuwa\Core\PluginEvents;

/**
 * Debug Plugin
 *
 * 显示 Route 结果等
 */
class DebugPlugin implements PluginInterface
{
    public static function getSubscribedEvents() {
        return [
            PluginEvents::ROUTER_STARTUP  => 'debugRouterUp',
            PluginEvents::ROUTER_SHUTDOWN => 'debugRouterDown',
        ];
    }

    public function debugRouterUp($request, $response) {
        $aLog = [
            '@'                          => __FUNCTION__,
            '$_SERVER["PATH_INFO"]'      => $_SERVER['PATH_INFO'],
            '$_SERVER["REQUEST_URI"]'    => $_SERVER['REQUEST_URI'],
            '$_SERVER["ORIG_PATH_INFO"]' => $_SERVER['ORIG_PATH_INFO'],
            'Request()->getBaseUri'      => \Yaf_Dispatcher::getInstance()->getRequest()->getBaseUri(),
            'Request()->getRequestUri()' => \Yaf_Dispatcher::getInstance()->getRequest()->getRequestUri(),
            'Request'                    => \Yaf_Dispatcher::getInstance()->getRequest(),
            'Router'                     => \Yaf_Dispatcher::getInstance()->getRouter(),
            'Config'                     => \Yaf_Application::app()->getConfig(),
            'Plugin'                     => PluginSubject::getInstance()->getAll(),
        ];
        \BizLib\Log::notice($aLog);
    }

    public function debugRouterDown($request, $response) {
        $aLog = [
            '@'          => __FUNCTION__,
            'Request'    => \Yaf_Dispatcher::getInstance()->getRequest(),
            'Module'     => \Yaf_Dispatcher::getInstance()->getRequest()->getModuleName(),
            'Controller' => \Yaf_Dispatcher::getInstance()->getRequest()->getControllerName(),
            'Action'     => \Yaf_Dispatcher::getInstance()->getRequest()->getActionName(),
            'Router'     => \Yaf_Dispatcher::getInstance()->getRouter(),
            'Router Hit' => \Yaf_Dispatcher::getInstance()->getRouter()->getCurrentRoute(),
        ];
        \BizLib\Log::notice($aLog);
    }
}
